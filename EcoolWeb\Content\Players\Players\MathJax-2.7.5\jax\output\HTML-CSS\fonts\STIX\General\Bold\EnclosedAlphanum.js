/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/EnclosedAlphanum.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{9312:[690,19,695,0,695],9313:[690,19,695,0,695],9314:[690,19,695,0,695],9315:[690,19,695,0,695],9316:[690,19,695,0,695],9317:[690,19,695,0,695],9318:[690,19,695,0,695],9319:[690,19,695,0,695],9320:[690,19,695,0,695],9398:[690,19,695,0,695],9399:[690,19,695,0,695],9400:[690,19,695,0,695],9401:[690,19,695,0,695],9402:[690,19,695,0,695],9403:[690,19,695,0,695],9404:[690,19,695,0,695],9405:[690,19,695,0,695],9406:[690,19,695,0,695],9407:[690,19,695,0,695],9408:[690,19,695,0,695],9409:[690,19,695,0,695],9410:[690,19,695,0,695],9411:[690,19,695,0,695],9412:[690,19,695,0,695],9413:[690,19,695,0,695],9414:[690,19,695,0,695],9415:[690,19,695,0,695],9416:[690,19,695,0,695],9417:[690,19,695,0,695],9418:[690,19,695,0,695],9419:[690,19,695,0,695],9420:[690,19,695,0,695],9421:[690,19,695,0,695],9422:[690,19,695,0,695],9423:[690,19,695,0,695],9424:[690,19,695,0,695],9425:[690,19,695,0,695],9426:[690,19,695,0,695],9427:[690,19,695,0,695],9428:[690,19,695,0,695],9429:[690,19,695,0,695],9430:[690,19,695,0,695],9431:[690,19,695,0,695],9432:[690,19,695,0,695],9433:[690,19,695,0,695],9434:[690,19,695,0,695],9435:[690,19,695,0,695],9436:[690,19,695,0,695],9437:[690,19,695,0,695],9438:[690,19,695,0,695],9439:[690,19,695,0,695],9440:[690,19,695,0,695],9441:[690,19,695,0,695],9442:[690,19,695,0,695],9443:[690,19,695,0,695],9444:[690,19,695,0,695],9445:[690,19,695,0,695],9446:[690,19,695,0,695],9447:[690,19,695,0,695],9448:[690,19,695,0,695],9449:[690,19,695,0,695],9450:[690,19,695,0,695]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/EnclosedAlphanum.js");
