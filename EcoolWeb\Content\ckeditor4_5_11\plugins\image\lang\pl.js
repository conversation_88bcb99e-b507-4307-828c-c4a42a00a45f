﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'pl', {
	alt: 'Tekst zastępczy',
	border: 'Obramowanie',
	btnUpload: 'Wyślij',
	button2Img: '<PERSON><PERSON> chcesz przekonwertować zaznaczony przycisk graficzny do zwykłego obrazka?',
	hSpace: 'Odstęp poziomy',
	img2Button: '<PERSON><PERSON> chcesz przekonwertować zaznaczony obrazek do przycisku graficznego?',
	infoTab: 'Informacje o obrazku',
	linkTab: 'Hiperłącze',
	lockRatio: 'Zablokuj proporcje',
	menu: 'Właściwości obrazka',
	resetSize: 'Przywr<PERSON>ć rozmiar',
	title: '<PERSON><PERSON><PERSON><PERSON><PERSON>ści obrazka',
	titleButton: 'Właściwości przycisku graficznego',
	upload: 'Wyślij',
	urlMissing: '<PERSON>da<PERSON> adres URL obrazka.',
	vSpace: 'Odstęp pionowy',
	validateBorder: 'Wartość obramowania musi być liczbą całkowitą.',
	validateHSpace: 'Wartość odstępu poziomego musi być liczbą całkowitą.',
	validateVSpace: 'Wartość odstępu pionowego musi być liczbą całkowitą.'
} );
