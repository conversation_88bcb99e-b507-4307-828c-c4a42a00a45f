/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Main,{768:[699,-505,0,-394,-205],769:[699,-505,0,-297,-107],770:[694,-531,0,-388,-113],771:[668,-565,0,-417,-84],772:[590,-544,0,-431,-70],774:[694,-515,0,-408,-93],775:[669,-549,0,-310,-191],776:[669,-554,0,-405,-96],778:[715,-542,0,-353,-148],779:[701,-510,0,-378,-80],780:[644,-513,0,-386,-115],824:[716,215,0,-639,-140]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Regular/CombDiacritMarks.js");
