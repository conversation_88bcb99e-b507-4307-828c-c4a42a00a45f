﻿<script src=@Url.Content("~/Scripts/jquery-3.6.4.js")></script>
    @*<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.4.2/jquery.js"></script>*@
    <script src="~/Content/WavRecorder/js/swfobject.js"></script>
    <script src="~/Content/WavRecorder/js/recorder.js"></script>
    <script src="~/Content/WavRecorder/basic/basic.js"></script>
    <link href="~/Content/WavRecorder/basic/basic.css" rel="stylesheet" />
    <style>
        /* Styles for recorder buttons */
        .recorder button, .recorder .upload, .level {
            border: 1px solid #686868;
            height: 30px;
            background-color: white;
            display: inline-block;
            vertical-align: bottom;
            margin: 2px;
            box-sizing: border-box;
            border-radius: 4px;
        }

        /* Styles for level indicator - required! */
        .level {
            width: 30px;
            height: 30px;
            position: relative;
        }

        .progress {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: #b10000;
        }

        .upload {
            padding-top: 2px;
        }
    </style>



    <div class="container">
        <section class="recorder-container">
            <!-- Recorder control buttons -->
            <div class="recorder">
                <button class="start-recording" onclick="FWRecorder.record('audio', 'audio.wav');">
                    <img src="~/Content/WavRecorder/images/record.png" alt="Record" />
                </button>
                <div class="level">
                    <div class="progress"></div>
                </div>
                <button class="stop-recording" onclick="FWRecorder.stopRecording('audio');">
                    <img src="~/Content/WavRecorder/images/stop.png" alt="Stop Recording" />
                </button>
                <button class="start-playing" onclick="FWRecorder.playBack('audio');" title="Play">
                    <img src="~/Content/WavRecorder/images/play.png" alt="Play" />
                </button>
                <div class="upload" style="display: inline-block">
                    <div id="flashcontent">
                        <p>您的瀏覽器必須啟用javascript並安裝Adobe Flash播放器。</p>
                    </div>
                </div>
            </div>
            <!-- Hidden form for easy specifying the upload request parameters -->
            @using (Html.BeginForm("UploadAction", "Audio", FormMethod.Post, new { id = "uploadForm", name = "uploadForm", enctype = "multipart/form-data" }))
            {
                <input name="authenticity_token" value="xxxxx" type="hidden">
                <input name="upload_file[parent_id]" value="1" type="hidden">
                <input name="format" value="json" type="hidden">
            }
        </section>
    </div>
