﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01SysSetWearIndexViewModel
    {
        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///防身有配載時給於的點數
        /// </summary>
        [DisplayName("配載獲得的點數")]
        [Required]
        public short ALARM_CASH { get; set; }

        /// <summary>
        ///通知單訊息預帶內容
        /// </summary>
        [DisplayName("通知單的預設文字")]
        [Required]
        public string ALARM_NOTICE { get; set; }

        /// <summary>
        ///開學日，只有總召要填
        /// </summary>
        [DisplayName("開學日")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? FIRST_DAY { get; set; }
    }
}