﻿@model RollCallCashViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("RollCallCash", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Keyword)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">

                <div class="row">

                    <div class="col-md-6 col-sm-6 dl-horizontal-EZ">
                        <samp class="dt">
                            @Html.LabelFor(m => m.Main.ROLL_CALL_NAME)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.Main.ROLL_CALL_NAME)
                        </samp>
                    </div>
                    <div class="col-md-6 col-sm-6  dl-horizontal-EZ ">
                        <samp class="dt">
                            @Html.LabelFor(m => m.Main.CASH)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.Main.CASH)
                        </samp>
                    </div>
                </div>

                <hr class="hr-line-dashed" />
                @if (Model.Main.STATUS != (byte)AWAT12.StatusVal.己給點)
                {
                    <div class="form-horizontal">
                        <div style="height:15px"></div>
                        <div class="row">
                            <div class="col-md-offset-1 col-md-11">
                                <div class="input-group">
                                    @Html.Editor("AllCash", new { htmlAttributes = new { @class = "form-control input-sm", @placeholder = "批次給分的貼心工具 請輸入 0 ~ 50 的數字" } })
                                    <span class="input-group-btn">
                                        <button class="btn btn-default btn-sm" type="button" onclick="onAllCashJs($('#AllCash').val())">全部自動帶入這個點數</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <ul>
                    <li class="list-group-item clearfix">
                        <div class="row">
                            <div class="col-xs-2">
                                班級
                            </div>
                            <div class="col-xs-2">
                                座號
                            </div>
                            <div class="col-xs-4">
                                姓名
                            </div>
                            <div class="col-xs-4">
                                點數
                            </div>
                            <div class="col-xs-4">
                                日期
                            </div>
                        </div>
                    </li>

                    @if (Model.Details?.Count() > 0)
                    {
                        foreach (var item in Model.Details)
                        {
                            item.STATUS = (byte)Model.Main.STATUS;
                            @Html.Partial("_RollCallCashDetail", item)
                        }
                    }
                </ul>
            </div>
        </div>
    </div>

    <div class="text-center">
        <hr />
        <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
            回上一頁
        </button>

        @if (Model.Main.STATUS != (byte)AWAT12.StatusVal.己給點)
        {
            <button type="button" class="btn btn-default" onclick="onSave()">
                <span class="fa fa-check-circle" aria-hidden="true"></span>確定給點
            </button>
        }
    </div>
}

@section Scripts {

    <script language="JavaScript">

        var targetFormID = '#form1';

        function onAllCashJs(Val) {
            $(".Input_Cash").prop("value",Val);
        }

        function onSave(StatusVal)
        {
            var OK = confirm("您確定給點?確定後就無法修改")

            if (OK==true)
            {
                 $(targetFormID).attr("action", "@Url.Action("RollCallCashSave", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
            }
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("RollCallIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}