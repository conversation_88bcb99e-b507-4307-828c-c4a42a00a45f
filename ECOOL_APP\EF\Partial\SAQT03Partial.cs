﻿
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public partial class SAQT03
    {
        public class Q_INPUT_TYPEVal
        {

            static public string text = "text";

            static public string textarea = "textarea";
            static public string UploadWorld = "UploadWorld";
            static public string UploadPdf= "UploadPdf";
            static public string UploadImage = "UploadImage";
            static public string date = "date";

            static public string number = "number";

            static public string email = "email";

            static public string url = "url";

            static public string file = "file";

            static public string radio = "radio";

            static public string checkbox = "checkbox";

            static public string select = "select";

            static public string StudentMenu = "StudentMenu";

            static public string GetDesc(string Val)
            {
                if (string.IsNullOrWhiteSpace(Val)) return string.Empty;

                if (Val == text)
                {
                    return "簡答";
                }
                else if (Val == UploadWorld) {
                    return "請上傳一個Word檔";
                }
                else if (Val == UploadPdf)
                {
                    return "請上傳一個Pdf檔";
                }
                else if (Val == UploadImage)
                {
                    return "上傳圖片";
                }
                else if (Val == textarea)
                {
                    return "詳答";
                }
                else if (Val == date)
                {
                    return "日期";
                }
                else if (Val == date)
                {
                    return "日期";
                }
                else if (Val == number)
                {
                    return "數字";
                }
                else if (Val == email)
                {
                    return "E-Mail";
                }
                else if (Val == url)
                {
                    return "網址";
                }
                else if (Val == radio)
                {
                    return "單選";
                }
                else if (Val == checkbox)
                {
                    return "複選題";
                }
                else if (Val == select)
                {
                    return "下拉式選單";
                }
                else if (Val == StudentMenu)
                {
                    return "學生選單";
                }
                else if (Val == file)
                {
                    return "上檔文件";
                }
                else
                {
                    return string.Empty;
                }

            }

            static private List<string> SetInput()
            {
                List<string> TempList = new List<string>();
                TempList.Add(text);
                TempList.Add(textarea);
                TempList.Add(radio);
                TempList.Add(checkbox);
                TempList.Add(select);
                TempList.Add(date);
                TempList.Add(number);
                TempList.Add(email);
                TempList.Add(url);
                TempList.Add(file);
                TempList.Add(StudentMenu);
                return TempList;
            }

            static private List<string> SetVoteInput()
            {
                List<string> TempList = new List<string>();
                TempList.Add(text);
                TempList.Add(radio);
                TempList.Add(checkbox);
                TempList.Add(UploadWorld);
                TempList.Add(UploadImage);
                TempList.Add(UploadPdf);
                return TempList;
            }

            public static List<SelectListItem> SelectItem(string SelectedVal = null)
            {
                List<SelectListItem> ThisSelectItem = new List<SelectListItem>();
                ThisSelectItem.Add(new SelectListItem() { Text = "請選擇..", Value = string.Empty, Selected = string.IsNullOrWhiteSpace(SelectedVal) });
                foreach (var item in SetInput())
                {
                    ThisSelectItem.Add(new SelectListItem() { Text = GetDesc(item), Value = item, Selected = SelectedVal == item });
                }

                return ThisSelectItem;
            }

            public static List<SelectListItem> VoteSelectItem(string SelectedVal = null)
            {
                List<SelectListItem> ThisSelectItem = new List<SelectListItem>();
                ThisSelectItem.Add(new SelectListItem() { Text = "請選擇..", Value = string.Empty, Selected = string.IsNullOrWhiteSpace(SelectedVal) });
                foreach (var item in SetVoteInput())
                {
                    ThisSelectItem.Add(new SelectListItem() { Text = GetDesc(item), Value = item, Selected = SelectedVal == item });
                }

                return ThisSelectItem;
            }


            static public bool isInputHtml(string type)
            {
                if (string.IsNullOrWhiteSpace(type)) return false;

                var TempList = SetInput().Where(a => a != textarea && a != file).ToList();

                if (TempList.Contains(type))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }

        }

        public static MvcHtmlString CreSelectHtml(List<SAQT03> info_D, string Ans, byte? Must, int Index, IDictionary<string, object> htmlAttributes)
        {
            var ReturnHtml = new StringBuilder();

            var selectList = new TagBuilder("select");
            selectList.MergeAttribute("name", string.Format("Topic[{0}].ANSWER", Index));
            selectList.MergeAttribute("id", string.Format("Topic{0}_ANSWER", Index));
            selectList.MergeAttribute("data-val", "true");
            selectList.MergeAttributes<string, object>(htmlAttributes);

            var Temp = info_D.FirstOrDefault();

            if (!string.IsNullOrWhiteSpace(Temp.Q_CLASS))
            {
                selectList.MergeAttribute("class", Temp.Q_CLASS);
            }
            else
            {
                selectList.MergeAttribute("class", "form-control input-sm");
            }


            if (Convert.ToBoolean(Must))
            {
                selectList.MergeAttributes<string, object>(Validate.required(Temp.Q_REQUIRED_MSG));
            }

            var option = new TagBuilder("option");
            option.MergeAttribute("value", "");
            option.InnerHtml = "請選擇...";

            if (string.IsNullOrWhiteSpace(Ans))
            {
                option.MergeAttribute("selected", "selected");
            }
            selectList.InnerHtml += option.ToString();

            foreach (var info in info_D)
            {
                var infoOption = new TagBuilder("option");
                infoOption.MergeAttribute("value", info.Q_VAL);

                if (!string.IsNullOrWhiteSpace(Ans))
                {
                    if (info.Q_VAL == Ans)
                    {
                        infoOption.MergeAttribute("selected", "selected");
                    }
                }

                infoOption.InnerHtml = info.Q_VAL;
                selectList.InnerHtml += infoOption.ToString();
            }

            ReturnHtml.Append(selectList.ToString(TagRenderMode.Normal));



            return MvcHtmlString.Create(ReturnHtml.ToString());
        }

        public static MvcHtmlString CreInputHtml(SAQT03 info_D, string Ans, byte? Must, int Index, IDictionary<string, object> htmlAttributes)
        {
            string ReturnHtml = string.Empty;

            if (info_D.Q_INPUT_TYPE == Q_INPUT_TYPEVal.textarea)
            {
                ReturnHtml = CreTextarea(info_D, Ans, Must, Index, htmlAttributes);
            }
            else if (info_D.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.file|| info_D.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld || info_D.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage || info_D.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadPdf)
            {
                if (info_D.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld || info_D.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage || info_D.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadPdf)
                {
                    info_D.Q_INPUT_TYPE = SAQT03.Q_INPUT_TYPEVal.file;
                }

                ReturnHtml = CreFile(info_D, Ans, Must, Index, htmlAttributes);
            }
            else
            {
                ReturnHtml = CreInput(info_D, Ans, Must, Index, htmlAttributes);
            }


            return MvcHtmlString.Create(ReturnHtml.ToString()); ;
        }

        internal static string CreFile(SAQT03 info, string Ans, byte? Must, int Index, IDictionary<string, object> htmlAttributes)
        {
            var sb = new StringBuilder();

            var builder = new TagBuilder("input");
            builder.MergeAttributes<string, object>(htmlAttributes);
            builder.MergeAttribute("name", string.Format("Topic[{0}].ANSWER", Index));
            builder.MergeAttribute("id", string.Format("Topic{0}_ANSWER", Index));
            builder.MergeAttribute("type", info.Q_INPUT_TYPE);
          
            if (!string.IsNullOrWhiteSpace(info.Q_CLASS))
            {
                builder.MergeAttribute("class", info.Q_CLASS);
            }
            else
            {
                builder.MergeAttribute("class", "form-control input-sm");
            }

            if (!string.IsNullOrWhiteSpace(info.Q_PLACEHOLDER))
            {
                builder.MergeAttribute("placeholder", info.Q_PLACEHOLDER);
            }

            if (!string.IsNullOrWhiteSpace(info.Q_ACCEPT))
            {
                builder.MergeAttribute("accept", info.Q_ACCEPT);
                builder.MergeAttribute("data-val-accept", info.Q_FORMAT_MSG);
                builder.MergeAttribute("data-val-accept-exts", info.Q_ACCEPT);
            }
            if (Convert.ToBoolean(Must))
            {
                builder.MergeAttributes<string, object>(Validate.required(info.Q_REQUIRED_MSG));
            }

            sb.Append(builder.ToString(TagRenderMode.Normal));

            return sb.ToString();
        }

        internal static string CreTextarea(SAQT03 info, string Ans, byte? Must, int Index, IDictionary<string, object> htmlAttributes)
        {
            var sb = new StringBuilder();

            var builder = new TagBuilder("textarea");
            builder.MergeAttributes<string, object>(htmlAttributes);
            builder.MergeAttribute("name", string.Format("Topic[{0}].ANSWER", Index));
            builder.MergeAttribute("id", string.Format("Topic{0}_ANSWER", Index));
            builder.MergeAttribute("cols", "30");
            builder.MergeAttribute("rows", "15");
            builder.MergeAttribute("data-val", "true");

            if (!string.IsNullOrWhiteSpace(info.Q_CLASS))
            {
                builder.MergeAttribute("class", info.Q_CLASS);
            }
            if (!string.IsNullOrWhiteSpace(info.Q_PLACEHOLDER))
            {
                builder.MergeAttribute("placeholder", info.Q_PLACEHOLDER);
            }

            if (Convert.ToBoolean(Must))
            {
                builder.MergeAttributes<string, object>(Validate.required(info.Q_REQUIRED_MSG));
            }

            builder.InnerHtml = Ans;
            sb.Append(builder.ToString(TagRenderMode.Normal));

            return sb.ToString();
        }

        internal static string CreInput(SAQT03 info, string Ans, byte? Must, int Index, IDictionary<string, object> htmlAttributes)
        {
            var sb = new StringBuilder();

            var builder = new TagBuilder("input");
            builder.MergeAttributes<string, object>(htmlAttributes);
            builder.MergeAttribute("type", info.Q_INPUT_TYPE);
            builder.MergeAttribute("data-val", "true");

            if (!string.IsNullOrWhiteSpace(info.Q_CLASS))
            {
                builder.MergeAttribute("class", info.Q_CLASS);
            }


            if (!string.IsNullOrWhiteSpace(info.Q_PLACEHOLDER))
            {
                builder.MergeAttribute("placeholder", info.Q_PLACEHOLDER);
            }

            if (info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.radio ||
                info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.checkbox)
            {
                builder.MergeAttribute("value", info.Q_VAL);

                if (!string.IsNullOrWhiteSpace(Ans))
                {

                    string ThisAns = "," + Ans + ",";
                    string ThisQ_VAL = "," + info.Q_VAL + ",";

                    if (ThisAns.IndexOf(ThisQ_VAL) >= 0)
                    {
                        builder.MergeAttribute("checked", "checked");
                    }
                }
                else
                {
                    if (info.Q_VAL == info.DEFAULT_VAL)
                    {
                        builder.MergeAttribute("checked", "checked");
                    }
                }

                builder.MergeAttribute("id", string.Format("Topic{0}_ArrANSWER_{1}", Index, info.Q_VAL));
                builder.MergeAttribute("name", string.Format("Topic[{0}].ArrANSWER", Index));
            }
            else
            {
                if (string.IsNullOrWhiteSpace(info.Q_CLASS))
                {
                    builder.MergeAttribute("class", "form-control input-sm");
                }


                if (!string.IsNullOrWhiteSpace(Ans))
                {
                    builder.MergeAttribute("value", Ans);
                }
                else
                {
                    builder.MergeAttribute("value", "");
                }

                builder.MergeAttribute("id", string.Format("Topic{0}_ANSWER", Index));
                builder.MergeAttribute("name", string.Format("Topic[{0}].ANSWER", Index));
            }



            if (Convert.ToBoolean(Must))
            {
                builder.MergeAttributes<string, object>(Validate.required(info.Q_REQUIRED_MSG));
            }

            builder.MergeAttributes<string, object>(Validate.Validation(info));




            sb.Append(builder.ToString(TagRenderMode.Normal));

            var returnSb = new StringBuilder();

            if (info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.radio)
            {
                var label = new TagBuilder("label");
                label.MergeAttribute("class", "radio");
                label.InnerHtml = sb.Replace("</input>", "").ToString() + " " + info.Q_VAL;
                returnSb.Append(label.ToString(TagRenderMode.Normal));
            }
            else if (info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.checkbox)
            {
                var label = new TagBuilder("label");
                label.MergeAttribute("class", "checkbox");
                label.InnerHtml = sb.Replace("</input>", "").ToString() + " " + info.Q_VAL;
                returnSb.Append(label.ToString(TagRenderMode.Normal));
            }
            else
            {
                returnSb.Append(sb);
            }

            return returnSb.ToString();
        }

        public class Validate
        {
            static private string DefaultRequired = "*此欄位必輸";


            static public string requiredToStr(string validationMessage = null)
            {
                if (string.IsNullOrWhiteSpace(validationMessage))
                {
                    validationMessage = DefaultRequired;
                }

                return validationMessage;
            }

            static public Dictionary<string, object> required(string validationMessage = null)
            {
                Dictionary<string, object> Attributes = new Dictionary<string, object>();

                if (string.IsNullOrWhiteSpace(validationMessage))
                {
                    validationMessage = DefaultRequired;
                }
                Attributes.Add("data-val-required", validationMessage);
                Attributes.Add("required", "required");

                return Attributes;
            }

            static public Dictionary<string, object> Validation(SAQT03 info)
            {

                Dictionary<string, object> Attributes = new Dictionary<string, object>();

                if (info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.date)
                {
                    if (!string.IsNullOrWhiteSpace(info.Q_FORMAT_MSG))
                    {
                        Attributes.Add("data-val-date", info.Q_FORMAT_MSG);
                    }
                    else
                    {
                        Attributes.Add("data-val-date", "*必須為日期");
                    }
                }
                else if (info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.number)
                {
                    if (!string.IsNullOrWhiteSpace(info.Q_FORMAT_MSG))
                    {
                        Attributes.Add("data-val-number", info.Q_FORMAT_MSG);
                    }
                    else
                    {
                        Attributes.Add("data-val-number", "*必須為有效數字");
                    }

                }
                else if (info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.email)
                {
                    if (!string.IsNullOrWhiteSpace(info.Q_FORMAT_MSG))
                    {
                        Attributes.Add("data-val-email", info.Q_FORMAT_MSG);
                    }
                    else
                    {
                        Attributes.Add("data-val-email", "*必須為Email");
                    }
                }
                else if (info.Q_INPUT_TYPE == Q_INPUT_TYPEVal.url)
                {
                    if (!string.IsNullOrWhiteSpace(info.Q_FORMAT_MSG))
                    {
                        Attributes.Add("data-val-url", info.Q_FORMAT_MSG);
                    }
                    else
                    {
                        Attributes.Add("data-val-url", "*必須為有效網址");
                    }
                }


                if (!string.IsNullOrWhiteSpace(info.Q_REGEX))
                {
                    Attributes.Add("data-val-regex-pattern", info.Q_REGEX);
                    Attributes.Add("data-val-regex", info.Q_REGEX_MSG);
                }

                if (info.Q_LENGTH_MIN != null && info.Q_LENGTH_MAX != null)
                {
                    Attributes.Add("data-val-range-min", info.Q_LENGTH_MIN);
                    Attributes.Add("data-val-range-max", info.Q_LENGTH_MAX);
                    Attributes.Add("data-val-range", string.Format(@"須介於{0}到{1}", info.Q_LENGTH_MIN, info.Q_LENGTH_MAX));
                }

                if (info.Q_LENGTH_MIN != null && info.Q_LENGTH_MAX == null)
                {
                    Attributes.Add("data-val-range-min", info.Q_LENGTH_MIN);
                    Attributes.Add("data-val-range", string.Format(@"須大於{0}", info.Q_LENGTH_MIN));
                }

                if (info.Q_LENGTH_MIN == null && info.Q_LENGTH_MAX != null)
                {
                    Attributes.Add("data-val-range-max", info.Q_LENGTH_MIN);
                    Attributes.Add("data-val-range", string.Format(@"須小於{0}", info.Q_LENGTH_MAX));
                }

                return Attributes;
            }



        }




    }
}