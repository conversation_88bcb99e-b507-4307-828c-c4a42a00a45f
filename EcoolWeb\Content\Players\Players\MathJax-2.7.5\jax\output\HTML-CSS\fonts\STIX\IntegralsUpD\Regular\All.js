/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpD/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXIntegralsUpD,{32:[0,0,250,0,0],160:[0,0,250,0,0],8748:[2000,269,787,58,832],8749:[2000,269,1107,58,1152],8751:[2000,269,849,39,849],8752:[2000,269,1161,36,1172],8753:[2000,269,608,47,736],8754:[2000,269,616,56,746],8755:[2000,269,605,56,785],10763:[2000,269,914,58,856],10764:[2000,269,1397,58,1442],10765:[2000,269,609,35,647],10766:[1999,270,609,35,647],10767:[1999,270,658,25,734],10768:[2000,269,629,56,635],10769:[2000,269,608,47,736],10770:[2000,269,568,58,597],10771:[2000,269,530,58,599],10772:[2000,269,695,58,776],10773:[2000,269,615,56,684],10774:[2000,269,653,56,682],10775:[2000,269,945,24,1039],10776:[2000,269,597,62,608],10777:[2000,269,735,65,801],10778:[2000,269,735,65,801],10779:[2157,269,701,0,741],10780:[2000,426,467,58,799]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsUpD/Regular/All.js");
