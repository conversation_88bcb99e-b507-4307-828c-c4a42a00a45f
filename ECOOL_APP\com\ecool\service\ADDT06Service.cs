﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace com.ecool.service
{
    public class ADDT06Service
    {
        /// <summary>
        /// 閱讀認證待審核項目畫面
        /// </summary>
        /// <param name="sidx">排序欄位名稱</param>
        /// <param name="sord">排序方式</param>
        /// <param name="page">目前頁數</param>
        /// <param name="pageSize">一頁幾筆</param>
        /// <returns></returns>
        public DataTable USP_ADDT06VIEWMODEL_QUERY(string sidx, string sord, int page, int pageSize, string SCHOOL_NO, string USER_NO)
        {
            DataTable dt = new DataTable();
            StringBuilder sb = new StringBuilder();
            try
            {
                int startIndex = ((page - 1) * pageSize) + 1;
                int endIndex = page * pageSize;
                sb.Append(" WITH PAGED_ADDT AS ");
                sb.Append(" ( ");
                sb.Append("     SELECT SYEAR,CASE SEMESTER WHEN 1 THEN'上' WHEN 2 THEN'下' END AS CSEMESTER, CLASS_NO,SEAT_NO,SNAME,BOOK_NAME,CONVERT(varchar(12), CRE_DATE, 111 ) as CRE_DATE  ");
                sb.Append("     ,CASE APPLY_STATUS  WHEN '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify + "' THEN '批閱中'  ");
                sb.Append("                         WHEN '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave + "' THEN '暫存草稿'  ");
                sb.Append("                         WHEN '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify_X + "' THEN '轉批閱'  ");
                sb.Append("                         WHEN '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass + "'  THEN '通過'   ");
                sb.Append("                         WHEN '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_oPass_X + "' THEN '通過'   ");
                sb.Append("                         WHEN '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back + "' THEN '退回'");
                sb.Append("                         WHEN '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL + "' THEN '作廢' END AS APPLY_STATUS ");
                sb.Append("      , APPLY_NO, ");
                sb.Append("            ROW_NUMBER() OVER (ORDER BY " + sidx + @" " + sord + @") AS ADDT_NO  ");
                sb.Append("     FROM ADDT06 A (NOLOCK) ");
                sb.Append("     WHERE SCHOOL_NO='" + SCHOOL_NO + "' AND USER_NO='" + USER_NO + "' and SNAME is not null ");
                sb.Append(" ) ");
                sb.Append(" SELECT   SYEAR,CSEMESTER,CLASS_NO,SEAT_NO,SNAME,BOOK_NAME,CRE_DATE,APPLY_NO,APPLY_STATUS ,ADDT_NO  ");
                sb.Append(" FROM PAGED_ADDT ");
                sb.Append(" WHERE ADDT_NO BETWEEN " + startIndex + @" AND " + endIndex + @";");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return dt;
        }
    }
}