﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'lv', {
	border: 'Rāmja izmērs',
	caption: 'Leģenda',
	cell: {
		menu: 'Šūna',
		insertBefore: 'Pievienot šūnu pirms',
		insertAfter: '<PERSON>vienot šūnu pēc',
		deleteCell: 'Dzēst rūtiņas',
		merge: 'Apvienot rūtiņas',
		mergeRight: 'Apvieno pa labi',
		mergeDown: 'Apvienot uz leju',
		splitHorizontal: 'Sadalīt šūnu horizontāli',
		splitVertical: 'Sadalīt šūnu vertikāli',
		title: '<PERSON><PERSON><PERSON> uzstādījumi',
		cellType: '<PERSON>ū<PERSON> tips',
		rowSpan: 'Apvienotas rindas',
		colSpan: 'Apvienotas kolonas',
		wordWrap: 'Vārdu pārnese',
		hAlign: 'Horizontālais novietojums',
		vAlign: 'Vertikālais novietojums',
		alignBaseline: 'Pamatrinda',
		bgColor: 'Fona krāsa',
		borderColor: 'Rāmja krāsa',
		data: 'Dati',
		header: 'Virsraksts',
		yes: 'Jā',
		no: 'Nē',
		invalidWidth: 'Šūnas platumam jābūt skaitlim',
		invalidHeight: 'Šūnas augstumam jābūt skaitlim',
		invalidRowSpan: 'Apvienojamo rindu skaitam jābūt veselam skaitlim',
		invalidColSpan: 'Apvienojamo kolonu skaitam jābūt veselam skaitlim',
		chooseColor: 'Izvēlēties'
	},
	cellPad: 'Rūtiņu nobīde',
	cellSpace: 'Rūtiņu atstatums',
	column: {
		menu: 'Kolonna',
		insertBefore: 'Ievietot kolonu pirms',
		insertAfter: 'Ievieto kolonu pēc',
		deleteColumn: 'Dzēst kolonnas'
	},
	columns: 'Kolonnas',
	deleteTable: 'Dzēst tabulu',
	headers: 'Virsraksti',
	headersBoth: 'Abi',
	headersColumn: 'Pirmā kolona',
	headersNone: 'Nekas',
	headersRow: 'Pirmā rinda',
	invalidBorder: 'Rāmju izmēram jābūt skaitlim',
	invalidCellPadding: 'Šūnu atkāpēm jābūt pozitīvam skaitlim',
	invalidCellSpacing: 'Šūnu atstarpēm jābūt pozitīvam skaitlim',
	invalidCols: 'Kolonu skaitam jābūt lielākam par 0',
	invalidHeight: 'Tabulas augstumam jābūt skaitlim',
	invalidRows: 'Rindu skaitam jābūt lielākam par 0',
	invalidWidth: 'Tabulas platumam jābūt skaitlim',
	menu: 'Tabulas īpašības',
	row: {
		menu: 'Rinda',
		insertBefore: 'Ievietot rindu pirms',
		insertAfter: 'Ievietot rindu pēc',
		deleteRow: 'Dzēst rindas'
	},
	rows: 'Rindas',
	summary: 'Anotācija',
	title: 'Tabulas īpašības',
	toolbar: 'Tabula',
	widthPc: 'procentuāli',
	widthPx: 'pikseļos',
	widthUnit: 'platuma mērvienība'
} );
