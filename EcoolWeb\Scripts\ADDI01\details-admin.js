// Modern jQuery code for ADDI01 Details Admin page
$(document).ready(function() {
    // 文字計數功能
    const textCounter = {
        init: function() {
            this.updateCount();
        },

        updateCount: function() {
            const text = $("#ARTICLE").text();
            const textWithoutWhitespace = text.replace(/[\n\s]/g, "");
            const length = textWithoutWhitespace.length;
            $("#ShowFontLen").text(length);
        }
    };

    // 國語日報投稿模態框
    const mdnKidsModal = {
        init: function() {
            window.onMdnKidsModal = this.showModal.bind(this);
        },

        showModal: function() {
            $("#notice").removeAttr("hidden");
            
            $("#notice").dialog({
                width: 500,
                buttons: {
                    "不投稿": function() {
                        $(this).dialog("close");
                    },
                    "投稿國語日報": function() {
                        window.open('https://submission.mdnkids.com/', '投稿國語日報');
                    }
                }
            });
        }
    };

    // 複製功能
    const copyHandler = {
        init: function() {
            window.OnCopy = this.handleCopy.bind(this);
        },

        handleCopy: function() {
            const clipboard = new Clipboard("#id_copy");
            
            clipboard.on("success", (element) => {
                console.info("複製成功，複製內容：" + element.text);
                $('#success').show();
                $('#id_text').hide();
                
                setTimeout(() => {
                    this.hideModal();
                }, 2000);
            });
            
            clipboard.on("error", (element) => {
                console.error("複製失敗：", element);
            });
        },

        hideModal: function() {
            if (typeof HidemySmallModal === 'function') {
                HidemySmallModal();
            } else {
                $('#myShareUrlModal').modal('hide');
            }
        }
    };

    // 分享網址功能
    const shareUrlHandler = {
        init: function() {
            window.onWinOpenShareUrlLink = this.showShareModal.bind(this);
        },

        showShareModal: function(url) {
            $(".modal-content").attr("style", "width:500px");
            $('#id_text').html(url);
            $('#copyStr').html(url);
            $('#success').hide();
            $('#id_text').show();

            if (!$('#myShareUrlModal').is(':visible')) {
                $('#myShareUrlModal').modal('show');
            }
        }
    };

    // 提醒模態框功能
    const reminderModal = {
        init: function() {
            window.RemindShow = this.showReminder.bind(this);
        },

        showReminder: function() {
            const reminderLength = $("#remind-content").text().length;
            
            if (reminderLength > 0) {
                $('#remind-modal').modal('show');
            }
        }
    };

    // 圖片畫廊功能
    const imageGallery = {
        init: function() {
            this.initColorbox();
        },

        initColorbox: function() {
            $("#DivImg img").colorbox({ 
                opacity: 0.82, 
                maxHeight: '95%', 
                maxWidth: '95%' 
            });
        }
    };

    // 字體大小調整功能
    const fontSizeController = {
        init: function() {
            this.initJFontSize();
        },

        initJFontSize: function() {
            $('.p-context').jfontsize({
                btnMinusMaxHits: 4, // 最多可被縮小的次數
                btnPlusMaxHits: 6,  // 最多可被放大的次數
                sizeChange: 2       // 每次縮放字體變化像素
            });
        }
    };

    // 頁面加載處理
    const pageLoader = {
        init: function() {
            this.handlePageLoad();
        },

        handlePageLoad: function() {
            // 檢查狀態消息
            const statusMessage = window.ADDI01_STATUS_MESSAGE || "";
            if (statusMessage && statusMessage.length > 0) {
                // 如果需要顯示提醒，可以在這裡處理
                // reminderModal.showReminder();
            }

            // 初始化文字計數
            textCounter.updateCount();
        }
    };

    // 初始化所有功能
    textCounter.init();
    mdnKidsModal.init();
    copyHandler.init();
    shareUrlHandler.init();
    reminderModal.init();
    imageGallery.init();
    fontSizeController.init();
    pageLoader.init();

    // 頁面加載完成後的額外初始化
    $(window).on('load', function() {
        pageLoader.handlePageLoad();
    });
});
