﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Web.Mvc;
using System.Web.Mvc.Html;
using System.Web.Routing;

namespace EcoolWeb.Util
{
    public static class HtnlHelperService
    {
        public static bool HasValidationMessageFor<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression)
        {
            var value = htmlHelper.ValidationMessageFor(expression).ToString();

            return value.Contains("field-validation-error");
        }

        public static bool HasValidationMessage(this HtmlHelper htmlHelper, string modelName)
        {
            var value = htmlHelper.ValidationMessage(modelName).ToString();

            return value.Contains("field-validation-error");
        }

        public static List<SelectListItem> YNSelectItem(string SelectedVal = null)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            SelectItem.Add(new SelectListItem() { Text = "是", Value = "Y", Selected = SelectedVal == "Y" });
            SelectItem.Add(new SelectListItem() { Text = "否", Value = "N", Selected = SelectedVal == "N" });

            return SelectItem;
        }

        public static List<SelectListItem> YNSelectItem(bool SelectedVal = false)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            SelectItem.Add(new SelectListItem() { Text = "是", Value = true.ToString(), Selected = SelectedVal.ToString() == true.ToString() });
            SelectItem.Add(new SelectListItem() { Text = "否", Value = false.ToString(), Selected = SelectedVal.ToString() == false.ToString() });

            return SelectItem;
        }

        public static List<SelectListItem> Y_SelectItem(string SelectedVal = null)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            SelectItem.Add(new SelectListItem() { Text = "是", Value = "Y", Selected = SelectedVal == "Y" });

            return SelectItem;
        }

        public static List<SelectListItem> OXSelectItem(string SelectedVal = null)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            SelectItem.Add(new SelectListItem() { Text = "O", Value = "O", Selected = SelectedVal == "O" });
            SelectItem.Add(new SelectListItem() { Text = "X", Value = "X", Selected = SelectedVal == "X" });

            return SelectItem;
        }

        public static List<SelectListItem> MCQSelectItem(string SelectedVal = null)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            SelectItem.Add(new SelectListItem() { Text = "1", Value = "1", Selected = SelectedVal == "1" });
            SelectItem.Add(new SelectListItem() { Text = "2", Value = "2", Selected = SelectedVal == "2" });
            SelectItem.Add(new SelectListItem() { Text = "3", Value = "3", Selected = SelectedVal == "3" });
            SelectItem.Add(new SelectListItem() { Text = "4", Value = "4", Selected = SelectedVal == "4" });

            return SelectItem;
        }

        public static bool IsPostBack()
        {
            bool isPost = string.Compare(HttpContext.Current.Request.HttpMethod, "POST",
               StringComparison.CurrentCultureIgnoreCase) == 0;
            if (HttpContext.Current.Request.UrlReferrer == null) return false;

            bool isSameUrl = string.Compare(HttpContext.Current.Request.Url.AbsolutePath,
               HttpContext.Current.Request.UrlReferrer.AbsolutePath,
               StringComparison.CurrentCultureIgnoreCase) == 0;

            return isPost && isSameUrl;
        }

        /// <summary>
        /// 排序 Link
        /// </summary>
        /// <typeparam name="TModel"></typeparam>
        /// <typeparam name="TValue"></typeparam>
        /// <param name="html"></param>
        /// <param name="expression"></param>
        /// <param name="sortColumn">目前排序欄位</param>
        /// <param name="sortType">目前排序方式</param>
        /// <param name="IsSort">是否能排序</param>
        /// <param name="htmlAttributes">htmlAttributes</param>
        /// <returns></returns>
        public static MvcHtmlString DisplaySortColumnFor<TModel, TValue>(this HtmlHelper<TModel> html, Expression<Func<TModel, TValue>> expression, string sortColumn = "", PageGlobal.SortType? sortType = null, bool IsSort = false, object htmlAttributes = null)
        {
            sortColumn = sortColumn ?? "";
            sortType = sortType ?? PageGlobal.SortType.ASC;

            var metadata = ModelMetadata.FromLambdaExpression(expression, html.ViewData);

            string displayName = metadata.DisplayName;
            string columnName = metadata.PropertyName;
            if (IsSort)
            {
                var aBuilder = new TagBuilder("a");
                aBuilder.MergeAttribute("role", "button");
                aBuilder.MergeAttribute("id", $"button_{columnName}");
                aBuilder.MergeAttribute("onclick", $"doSort('{columnName}')");
                aBuilder.MergeAttribute("style", "cursor: pointer;");
                var attributes = HtmlHelper.AnonymousObjectToHtmlAttributes(htmlAttributes);
                if (attributes.ContainsKey("class") == false)
                {
                    //aBuilder.MergeAttribute("class", "btn btn-link");
                }

                aBuilder.MergeAttributes<string, object>(new RouteValueDictionary(htmlAttributes));

                if (columnName.Equals(sortColumn, StringComparison.OrdinalIgnoreCase))
                {
                    aBuilder.AddCssClass("active");

                    var iBuilder = new TagBuilder("i");

                    if (sortType == PageGlobal.SortType.ASC)
                    {
                        iBuilder.AddCssClass("fa fa-sort-amount-asc");
                        // displayName += "<i class='fa fa-sort-amount-asc' aria-hidden='true'></i>";
                    }
                    else
                    {
                        iBuilder.AddCssClass("fa fa-sort-amount-desc");
                        //   displayName += "<i class='fa fa-sort-amount-desc' aria-hidden='true'></i>";
                    }
                    aBuilder.InnerHtml = displayName + iBuilder;
                    //aBuilder.SetInnerText(displayName + iBuilder);
                }
                else
                {
                    var iBuilder = new TagBuilder("i");
                    iBuilder.AddCssClass("fa fa-sort");
                    aBuilder.InnerHtml = displayName + iBuilder;
                }

                return MvcHtmlString.Create(aBuilder.ToString());
            }
            else
            {
                return MvcHtmlString.Create(displayName);
            }
        }
    }
}