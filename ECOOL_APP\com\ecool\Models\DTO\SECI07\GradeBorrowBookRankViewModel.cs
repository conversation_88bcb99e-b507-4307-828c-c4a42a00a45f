﻿using DotNet.Highcharts;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class GradeBorrowBookRankViewModel
    {
        public Highcharts GradeColumnChart;
        public string Where_SCHOOLNO { get; set; }

        [Display(Name = "學年度")]
        public string Where_SYEAR { get; set; }

        [Display(Name = "月份")]
        public string Where_MONTH { get; set; }

        public List<GradeBorrowBookRank> GradeRankList { get; set; }
    }

    public class GradeBorrowBookRank
    {
        [Display(Name = "年級")]
        public string Grade { get; set; }

        [Display(Name = "借書量(本)")]
        public int BORROW_BOOK_COUNT { get; set; }

        [Display(Name = "平均借書量(本)")]
        public double BORROW_BOOK_AVG { get; set; }

        [Display(Name = "排名")]
        public int RANK { get; set; }
    }
}