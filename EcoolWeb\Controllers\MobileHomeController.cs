﻿using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using ECOOL_APP.com.ecool.service;
using Dapper;
using com.ecool.service;
using log4net;
using EcoolWeb.Util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class MobileHomeController : Controller
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "MobileHome";

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        /// <summary>
        /// APP 首頁 (訪客) 會判斷角色轉向個自首頁
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();

            if (user != null)
            {
                if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
                {
                    return RedirectToAction("StudentIndex", Bre_NO);
                }
                else if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Parents)
                {
                    if (user.Chance_BIRTHDAY && user.USER_TYPE == UserType.Student)
                    {
                        return RedirectToAction("Birthday", "Home");
                    }
                    else if (user.NotePassGrade > 0 && user.USER_TYPE == UserType.Student)
                    {
                      return RedirectToAction("PassNote", "Home");
                    }
                    else if (user.NoteReadLevel > 0 && user.USER_TYPE == UserType.Student)
                    {
                        return RedirectToAction("ReadLevelNote", "Home");
                      
                    }
                    else
                    {
                        return RedirectToAction("StudentIndex", Bre_NO);
                    }
                }
            }
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db);
            GetBET02List(SchoolNo);

            ViewBag.ADDV01List = db.ADDV01.Where(a => a.SCHOOL_NO == SchoolNo).OrderByDescending(a => a.WRITING_QTY).Take(5).ToList();

            var ans = from a9 in db.ADDT09
                      join h1 in db.HRMT01
                      on new { a9.SCHOOL_NO, a9.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                      where a9.SCHOOL_NO == SchoolNo && a9.BOOK_QTY > 0 && (!UserStaus.NGUserStausList.Contains(h1.USER_STATUS))
                      select new HRMT01QTY { CLASS_NO = h1.CLASS_NO, NAME = h1.SNAME, USER_NO = h1.USER_NO, QTY = a9.BOOK_QTY, USER_STATUS = h1.USER_STATUS };

            ViewBag.ADDT09List = ans.OrderByDescending(a => a.QTY).Take(5).ToList();

            var RankCash = from w1 in db.AWAT01
                           join h1 in db.HRMT01
                           on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                           into h1Table
                           from hh1 in h1Table.DefaultIfEmpty()
                           where w1.SCHOOL_NO == SchoolNo && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
                           select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };

            ViewBag.AWAT01List = RankCash.OrderByDescending(a => a.CASH_ALL).Take(5).ToList();

            return View();
        }

        public string GetDATA_TYPE_NAME(string wDATA_ANGLE_TYPE, string wCLASS_NO)
        {
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                return "(" + wCLASS_NO + "班)";
            }
            else if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData)
            {
                return "(本校)";
            }
            else
            {
                return "";
            }
        }

        /// <summary>
        /// 學生首頁
        /// </summary>
        /// <returns></returns>
        public ActionResult StudentIndex()
        {
            string SchoolNo = "";
            ViewBag.BRE_NO = Bre_NO;
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
          
           
                bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
            HRMT05_CHECK hRMT05_ = new HRMT05_CHECK();

           
          
            UserProfile user = UserProfileHelper.Get();
         
            if (user==null||(AppMode && user == null)) {
                logger.Info("StudentIndex AppMode 竟然是空得");
                hRMT05_ = db.HRMT05_CHECK.Where(x => x.UUID == UUID).FirstOrDefault();
                if (!string.IsNullOrEmpty(UUID )) { logger.Info("StudentIndex UUID" + UUID); }
                else {

                    logger.Info("StudentIndex UUID 竟然是空得" );

               
                }
           
                if (hRMT05_ != null && user==null) { 
                HRMT01 FindUser = db.HRMT01.Where(x => x.SCHOOL_NO == hRMT05_.SCHOOL_NO && x.USER_NO == hRMT05_.USER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                    SchoolNo = user.SCHOOL_NO;
                    if (FindUser != null) {
                UserProfileHelper.Set(LoginUser);
                    }
                }
            }

            if (user != null && string.IsNullOrWhiteSpace(UUID))
            {

                SchoolNo = user.SCHOOL_NO;
                logger.Info("studentHOME:" + user.USER_NO);
                logger.Info("studentHOME:" + user.SCHOOL_NO);
            }
            else if (!string.IsNullOrWhiteSpace(UUID)  && user != null)
            {
                hRMT05_ = db.HRMT05_CHECK.Where(x => x.UUID == UUID).FirstOrDefault();
                HRMT01 FindUser = new HRMT01();
                if (hRMT05_ != null)
                {

                 FindUser = db.HRMT01.Where(x => x.SCHOOL_NO == hRMT05_.SCHOOL_NO && x.USER_NO == hRMT05_.USER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                }

                else {
                    if (user.SCHOOL_NO != null && user.USER_NO != null) {
                        FindUser = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                    }
                  
                }

                if (FindUser != null)
                {
                    UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                    UserProfileHelper.Set(LoginUser);
                    user.USER_NO = FindUser.USER_NO;
                    user.SCHOOL_NO = FindUser.SCHOOL_NO;
                    user.CLASS_NO = FindUser.CLASS_NO;
                    UserProfileHelper.Set(LoginUser);
                }
                logger.Info("studentHOME UUID:" + user.USER_NO);
                logger.Info("studentHOME UUID:" + user.SCHOOL_NO);
          
                SchoolNo = hRMT05_.SCHOOL_NO;

            }
            else
            {
                logger.Info("挖哩user為空");

                SchoolNo = UserProfileHelper.GetSchoolNo();
            }
            if (user != null)
            {
                SchoolNo = user.SCHOOL_NO;
                LogHelper.AddLogToDB(user.SCHOOL_NO, user.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "StudentIndex" + user.SCHOOL_NO + " " + user.USER_NO, "LoginSuccess");
            }
                if (user == null )
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            GetBET02List(user.SCHOOL_NO);
            int LoginCount = 0;
          
            ECOOL_APP.com.ecool.Models.DTO.SECI01IndexViewModel Data = new ECOOL_APP.com.ecool.Models.DTO.SECI01IndexViewModel();
            Data.wSCHOOL_NO = user.SCHOOL_NO;
            Data.wUSER_NO = user.USER_NO;
            Data.wREF_BRE_NO = Bre_NO;
            Data.DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            string sSQL = @" select case when Sum(w.AMT) IS NULL   then 0 else  Sum(w.AMT) end from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=@SCHOOL_NO and w.USER_NO=@USER_NO";
            int? Deposit = 0;
            int? SUMCASHAVAILABLECash = 0;
            var DepositQuryable = db.Database.Connection.Query<int>(sSQL, new { SCHOOL_NO = Data.wSCHOOL_NO, USER_NO = Data.wUSER_NO });
            if (DepositQuryable != null)
            {
                Deposit = DepositQuryable.FirstOrDefault();
            }
            string sSQL1 = @"select 
                                 SUMCASH_AVAILABLE=w1.CASH_AVAILABLE+isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0)　 FROM AWAT01 w1 (nolock)　where  w1.SCHOOL_NO=@SCHOOL_NO and w1.USER_NO=@USER_NO";
            var SUMCASHAVAILABLE = db.Database.Connection.Query<int>(sSQL1, new { SCHOOL_NO = Data.wSCHOOL_NO, USER_NO = Data.wUSER_NO });
            if (SUMCASHAVAILABLE != null)
            {
                SUMCASHAVAILABLECash = SUMCASHAVAILABLE.FirstOrDefault();
            }
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db);
            HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.USER_NO == Data.wUSER_NO).FirstOrDefault();

            if (thisUser == null)
            {
                return RedirectToAction("NotFindError", "Error", new { error = "無此帳號資料，請確認(SCHOOL_NO:" + Data.wSCHOOL_NO + "、USER_NO:" + Data.wUSER_NO + ")" });
            }

            if (Data.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                Data.DATA_TYPE_NAME = GetDATA_TYPE_NAME(Data.DATA_ANGLE_TYPE, user.TEACH_CLASS_NO);
            }
            else
            {
                Data.DATA_TYPE_NAME = GetDATA_TYPE_NAME(Data.DATA_ANGLE_TYPE, user.CLASS_NO);
            }

            //Data.wPRINT = wPRINT;
            Data.NAME = thisUser.SNAME;

            Data.wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == thisUser.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

            string StudentNO = user.USER_NO;
            if (thisUser.USER_TYPE == UserType.Parents) StudentNO = StudentNO.Substring(1, StudentNO.Length - 1);
            HRMT01 Student = db.HRMT01.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.USER_NO == StudentNO).FirstOrDefault();
            //個人酷幣點數
            if (thisUser.USER_TYPE == UserType.Student || thisUser.USER_TYPE == UserType.Parents)
            {
                //學生
                AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == StudentNO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_DEPOSIT = (Deposit != null) ? Deposit : 0;
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                    Data.SUMCASH_AVAILABLE= (SUMCASHAVAILABLECash!=null)?SUMCASHAVAILABLECash : 0;
                }
                else
                {
                    Data.CASH_DEPOSIT = 0;
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                    Data.SUMCASH_AVAILABLE = 0;
                }

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                List<DB2_L_WORK2> BookWorks = db.DB2_L_WORK2.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.NO_READ == Student.IDNO && a.SEYEAR == SYear.ToString() && a.SESEM == Semesters.ToString() && a.RET_YYMM != "      ").ToList();
                if (BookWorks != null)
                {
                    Data.BOOKS = BookWorks.Sum(a => a.QTY);
                    string YYYYMM = DateTime.Today.ToString("yyyyMM");
                    DB2_L_WORK2 mm = BookWorks.Where(a => a.RET_YYMM == YYYYMM).FirstOrDefault();
                    if (mm != null)
                        Data.BOOKS_MONTH = mm.QTY;
                    else
                        Data.BOOKS_MONTH = 0;
                }
                //DB2_SCH_STATIC SCH_STATIC = db.DB2_SCH_STATIC.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.USER_NO == StudentNO && a.SYEAR == SYear && a.SEMESTER == Semesters).FirstOrDefault();
                //if (SCH_STATIC != null)
                //{
                //    Data.BOOKS = SCH_STATIC.BOOKS;
                //}
                // CustomerName == null ? n.CustomerName == n.CustomerName : n.CustomerName == CustomerName.ToString())

                //閱讀等級
                ADDT09 a9 = db.ADDT09.Where(uu => uu.SCHOOL_NO == Data.wSCHOOL_NO && uu.USER_NO == StudentNO).FirstOrDefault();
                if (a9 != null)
                {
                    Data.ReadLEVEL = Convert.ToInt16(a9.LEVEL_ID);
                }
                else
                {
                    Data.ReadLEVEL = 0;
                }

                //閱讀認證.等級
                Data.PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == StudentNO && p.SCHOOL_NO == Data.wSCHOOL_NO && p.PASS_YN == "Y").Count();

                //閱讀等級圖示
                Data.ReadImgURL = UserProfileHelper.GetSECImgReadLEVEL((Byte)Data.ReadLEVEL);

                //「認證」等級圖示
                Data.PassportImgURL = UserProfileHelper.GetSECImgPassportLEVEL(UserProfile.GetOenUseADDT04toShort(StudentNO, Data.wSCHOOL_NO, ref db));

                //我可兌換獎品 TOP 3 筆
                Data.AWAT02List = db.AWAT02.Where(a => (a.SCHOOL_NO == thisUser.SCHOOL_NO || a.SCHOOL_NO == "ALL") && a.AWARD_STATUS != "N" && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today && a.EDATETIME >= DateTime.Today && a.COST_CASH <= Data.CASH_AVAILABLE
                                 && (a.READ_LEVEL == null || a.READ_LEVEL <= Data.ReadLEVEL)
                                 && (a.PASSPORT_LEVEL == null || a.PASSPORT_LEVEL <= Data.PassportLEVEL)
                                 )
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);

                //QAT02Qty
                ViewBag.QAT02Qty = db.QAT02.Where(q2 => q2.A_USER_NO == Data.wUSER_NO && q2.A_STATUS != "2").Count();
            }
            else
            {
                //老師
                AWAT08 tCASH = db.AWAT08.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == thisUser.USER_NO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_DEPOSIT = (Deposit != null) ? Deposit : 0;
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                    Data.SUMCASH_AVAILABLE = (SUMCASHAVAILABLECash != null) ? SUMCASHAVAILABLECash : 0;
                }
                else
                {
                    Data.CASH_DEPOSIT = 0;
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                    Data.SUMCASH_AVAILABLE = 0;
                }

                //我可兌換獎品 TOP 3 筆
                Data.AWAT09List = db.AWAT09.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today.Date && a.EDATETIME >= DateTime.Today.Date && a.COST_CASH <= Data.CASH_AVAILABLE)
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Teacher);

                //QAT02Qty
                ViewBag.QAT02Qty = 0;
            }

            //角色娃娃
            Data.PlayerUrl = UserProfile.GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);

            if (Data.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                SetStatisticalData(Data.wSCHOOL_NO, StudentNO, user.TEACH_CLASS_NO, Data.DATA_ANGLE_TYPE, ref Data);
            }
            else
            {
                SetStatisticalData(Data.wSCHOOL_NO, StudentNO, user.CLASS_NO, Data.DATA_ANGLE_TYPE, ref Data);
            }
           
            LoginCount = ADDT03Service.UPLoginCount(user.USER_NO, user.SCHOOL_NO);
            return View(Data);
        }

        /// <summary>
        /// APP 首頁 (訪客) 會判斷角色轉向個自首頁
        /// </summary>
        /// <returns></returns>
        public ActionResult COOCIndex()
        {
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();

            if (user != null)
            {
                if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
                {
                    return RedirectToAction("COOCStudentIndex", Bre_NO);
                }
                else if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Parents)
                {
                    if (user.Chance_BIRTHDAY && user.USER_TYPE == UserType.Student)
                    {
                        return RedirectToAction("Birthday", "Home");
                    }
                    else
                    {
                        return RedirectToAction("COOCStudentIndex", Bre_NO);
                    }
                }
            }
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db);
            GetBET02List(SchoolNo);

            ViewBag.ADDV01List = db.ADDV01.Where(a => a.SCHOOL_NO == SchoolNo).OrderByDescending(a => a.WRITING_QTY).Take(5).ToList();

            var ans = from a9 in db.ADDT09
                      join h1 in db.HRMT01
                      on new { a9.SCHOOL_NO, a9.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                      where a9.SCHOOL_NO == SchoolNo && a9.BOOK_QTY > 0 && (!UserStaus.NGUserStausList.Contains(h1.USER_STATUS))
                      select new HRMT01QTY { CLASS_NO = h1.CLASS_NO, NAME = h1.SNAME, USER_NO = h1.USER_NO, QTY = a9.BOOK_QTY, USER_STATUS = h1.USER_STATUS };

            ViewBag.ADDT09List = ans.OrderByDescending(a => a.QTY).Take(5).ToList();

            var RankCash = from w1 in db.AWAT01
                           join h1 in db.HRMT01
                           on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                           into h1Table
                           from hh1 in h1Table.DefaultIfEmpty()
                           where w1.SCHOOL_NO == SchoolNo && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
                           select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };

            ViewBag.AWAT01List = RankCash.OrderByDescending(a => a.CASH_ALL).Take(5).ToList();

            return View();
        }

        /// <summary>
        /// 學生首頁
        /// </summary>
        /// <returns></returns>
        public ActionResult COOCStudentIndex()
        {
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            ViewBag.BRE_NO = Bre_NO;
            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            GetBET02List(user.SCHOOL_NO);

            ECOOL_APP.com.ecool.Models.DTO.SECI01IndexViewModel Data = new ECOOL_APP.com.ecool.Models.DTO.SECI01IndexViewModel();
            Data.wSCHOOL_NO = user.SCHOOL_NO;
            Data.wUSER_NO = user.USER_NO;
            Data.wREF_BRE_NO = Bre_NO;
            Data.DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            string sSQL = @" select case when Sum(w.AMT) IS NULL   then 0 else  Sum(w.AMT) end from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=@SCHOOL_NO and w.USER_NO=@USER_NO";
            int? Deposit = 0;

            var DepositQuryable = db.Database.Connection.Query<int>(sSQL, new { SCHOOL_NO = Data.wSCHOOL_NO, USER_NO = Data.wUSER_NO });
            if (DepositQuryable != null)
            {
                Deposit = DepositQuryable.FirstOrDefault();
            }
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db);
            HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.USER_NO == Data.wUSER_NO).FirstOrDefault();

            if (thisUser == null)
            {
                return RedirectToAction("NotFindError", "Error", new { error = "無此帳號資料，請確認(SCHOOL_NO:" + Data.wSCHOOL_NO + "、USER_NO:" + Data.wUSER_NO + ")" });
            }

            if (Data.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                Data.DATA_TYPE_NAME = GetDATA_TYPE_NAME(Data.DATA_ANGLE_TYPE, user.TEACH_CLASS_NO);
            }
            else
            {
                Data.DATA_TYPE_NAME = GetDATA_TYPE_NAME(Data.DATA_ANGLE_TYPE, user.CLASS_NO);
            }

            //Data.wPRINT = wPRINT;
            Data.NAME = thisUser.SNAME;

            Data.wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == thisUser.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

            string StudentNO = user.USER_NO;
            if (thisUser.USER_TYPE == UserType.Parents) StudentNO = StudentNO.Substring(1, StudentNO.Length - 1);
            HRMT01 Student = db.HRMT01.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.USER_NO == StudentNO).FirstOrDefault();
            //個人酷幣點數
            if (thisUser.USER_TYPE == UserType.Student || thisUser.USER_TYPE == UserType.Parents)
            {
                //學生
                AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == StudentNO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_DEPOSIT = (Deposit != null) ? Deposit : 0;
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                }
                else
                {
                    Data.CASH_DEPOSIT = 0;
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                }

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                List<DB2_L_WORK2> BookWorks = db.DB2_L_WORK2.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.NO_READ == Student.IDNO && a.SEYEAR == SYear.ToString() && a.SESEM == Semesters.ToString() && a.RET_YYMM != "      ").ToList();
                if (BookWorks != null)
                {
                    Data.BOOKS = BookWorks.Sum(a => a.QTY);
                    string YYYYMM = DateTime.Today.ToString("yyyyMM");
                    DB2_L_WORK2 mm = BookWorks.Where(a => a.RET_YYMM == YYYYMM).FirstOrDefault();
                    if (mm != null)
                        Data.BOOKS_MONTH = mm.QTY;
                    else
                        Data.BOOKS_MONTH = 0;
                }
                //DB2_SCH_STATIC SCH_STATIC = db.DB2_SCH_STATIC.Where(a => a.SCHOOL_NO == Data.wSCHOOL_NO && a.USER_NO == StudentNO && a.SYEAR == SYear && a.SEMESTER == Semesters).FirstOrDefault();
                //if (SCH_STATIC != null)
                //{
                //    Data.BOOKS = SCH_STATIC.BOOKS;
                //}
                // CustomerName == null ? n.CustomerName == n.CustomerName : n.CustomerName == CustomerName.ToString())

                //閱讀等級
                ADDT09 a9 = db.ADDT09.Where(uu => uu.SCHOOL_NO == Data.wSCHOOL_NO && uu.USER_NO == StudentNO).FirstOrDefault();
                if (a9 != null)
                {
                    Data.ReadLEVEL = Convert.ToInt16(a9.LEVEL_ID);
                }
                else
                {
                    Data.ReadLEVEL = 0;
                }

                //閱讀認證.等級
                Data.PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == StudentNO && p.SCHOOL_NO == Data.wSCHOOL_NO && p.PASS_YN == "Y").Count();

                //閱讀等級圖示
                Data.ReadImgURL = UserProfileHelper.GetSECImgReadLEVEL((Byte)Data.ReadLEVEL);

                //「認證」等級圖示
                Data.PassportImgURL = UserProfileHelper.GetSECImgPassportLEVEL(UserProfile.GetOenUseADDT04toShort(StudentNO, Data.wSCHOOL_NO, ref db));

                //我可兌換獎品 TOP 3 筆
                Data.AWAT02List = db.AWAT02.Where(a => (a.SCHOOL_NO == thisUser.SCHOOL_NO || a.SCHOOL_NO == "ALL") && a.AWARD_STATUS != "N" && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today && a.EDATETIME >= DateTime.Today && a.COST_CASH <= Data.CASH_AVAILABLE
                                 && (a.READ_LEVEL == null || a.READ_LEVEL <= Data.ReadLEVEL)
                                 && (a.PASSPORT_LEVEL == null || a.PASSPORT_LEVEL <= Data.PassportLEVEL)
                                 )
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);

                //QAT02Qty
                ViewBag.QAT02Qty = db.QAT02.Where(q2 => q2.A_USER_NO == Data.wUSER_NO && q2.A_STATUS != "2").Count();
            }
            else
            {
                //老師
                AWAT08 tCASH = db.AWAT08.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == thisUser.USER_NO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_DEPOSIT = (Deposit != null) ? Deposit : 0;
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                }
                else
                {
                    Data.CASH_DEPOSIT = 0;
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                }

                //我可兌換獎品 TOP 3 筆
                Data.AWAT09List = db.AWAT09.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today.Date && a.EDATETIME >= DateTime.Today.Date && a.COST_CASH <= Data.CASH_AVAILABLE)
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Teacher);

                //QAT02Qty
                ViewBag.QAT02Qty = 0;
            }

            //角色娃娃
            Data.PlayerUrl = UserProfile.GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);

            if (Data.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                SetStatisticalData(Data.wSCHOOL_NO, StudentNO, user.TEACH_CLASS_NO, Data.DATA_ANGLE_TYPE, ref Data);
            }
            else
            {
                SetStatisticalData(Data.wSCHOOL_NO, StudentNO, user.CLASS_NO, Data.DATA_ANGLE_TYPE, ref Data);
            }

            return View(Data);
        }

        /// <summary>
        /// 以各角度看 線上投稿/閱讀認證 資料
        /// </summary>
        /// <param name="wSCHOOL_NO"></param>
        /// <param name="wUSER_NO"></param>
        /// <param name="wCLASS_NO"></param>
        /// <param name="Data"></param>
        /// <param name="wDATA_ANGLE_TYPE"></param>
        public void SetStatisticalData(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, ref SECI01IndexViewModel Data)
        {
            //線上投稿資料
            var MyADDT01List = from a01 in db.ADDT01
                               join h01 in db.HRMT01 on new { a01.USER_NO, a01.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a01.SCHOOL_NO == wSCHOOL_NO && (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable
                               select a01;

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                MyADDT01List = MyADDT01List.Where(a => a.USER_NO == wUSER_NO);

                Data.arrWRITING_NO = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Select(a => a.WRITING_NO).ToList();
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                MyADDT01List = MyADDT01List.Where(a => a.CLASS_NO == wCLASS_NO);
            }

            Data.WritingCount = MyADDT01List.Count();
            Data.WritingShareCount = MyADDT01List.Where(a => a.SHARE_YN == "Y").Count();
            Data.ADDT01List = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Take(5).ToList();

            //閱讀認證
            var MyADDT06List = from a06 in db.ADDT06
                               join h01 in db.HRMT01
                                   on new { a06.SCHOOL_NO, a06.USER_NO }
                                   equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a06.SCHOOL_NO == wSCHOOL_NO && (!UserStaus.NGKeyinUserStausList.Contains(h01.USER_STATUS))
                                && a06.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL
                               select a06;

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                MyADDT06List = MyADDT06List.Where(a => a.USER_NO == wUSER_NO);

                Data.arrAPPLY_NO = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Select(a => a.APPLY_NO).ToList();
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                MyADDT06List = MyADDT06List.Where(a => a.CLASS_NO == wCLASS_NO);
            }

            Data.BookCount = MyADDT06List.Count();
            Data.BookShareCount = MyADDT06List.Where(a => a.SHARE_YN == "y").Count();
            Data.ADDT06List = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Take(5).ToList();

            //建議與鼓勵 top 5
            Data.ADDT02List = (from a in db.ADDT02
                               join b in MyADDT01List on new { a.SCHOOL_NO, a.WRITING_NO } equals new { b.SCHOOL_NO, b.WRITING_NO }
                               where a.COMMENT_STATUS != 9
                               select a).OrderByDescending(a => a.CRE_DATE).Take(5).ToList();

            // 校內外表現
            var SchoolDataList = (from a in db.ADDT14
                                  join h01 in db.HRMT01 on new { a.USER_NO, a.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                                  from h01 in h01_join.DefaultIfEmpty()
                                  where a.SCHOOL_NO == wSCHOOL_NO && a.APPLY_STATUS != "9" && (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                  select new SECI01SchoolDataViewModel
                                  {
                                      SYS_TABLE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN,
                                      USER_NO = a.USER_NO,
                                      CLASS_NO = a.CLASS_NO,
                                      NO = a.IAWARD_ID,
                                      CRE_DATE = a.CREATEDATE,
                                      CONTENT_TXT = a.IAWARD_ITEM,
                                      CASH = a.CASH
                                  })
                              .Union(from a in db.ADDT15
                                     join h01 in db.HRMT01 on new { a.USER_NO, a.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                                     from h01 in h01_join.DefaultIfEmpty()
                                     where a.SCHOOL_NO == wSCHOOL_NO && a.APPLY_STATUS != "9" && (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                     select new SECI01SchoolDataViewModel
                                     {
                                         SYS_TABLE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC,
                                         USER_NO = a.USER_NO,
                                         CLASS_NO = a.CLASS_NO,
                                         NO = a.OAWARD_ID,
                                         CRE_DATE = a.CREATEDATE,
                                         CONTENT_TXT = a.OAWARD_ITEM,
                                         CASH = a.CASH
                                     }
                                 );

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                SchoolDataList = SchoolDataList.Where(a => a.USER_NO == wUSER_NO);
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                SchoolDataList = SchoolDataList.Where(a => a.CLASS_NO == wCLASS_NO);
            }

            Data.SchoolInCount = SchoolDataList.Where(A => A.SYS_TABLE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN).Count();
            Data.SchoolObcCount = SchoolDataList.Where(A => A.SYS_TABLE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC).Count();

            // 校內外表現top 5
            Data.SchoolDataList = SchoolDataList.OrderByDescending(a => a.CRE_DATE).Take(5).ToList();

            // 最後投稿日 備註

            //取出比較的天數
            string LASH_DAY_VAL = BDMT02Service.GetRefFirstVal("SECI01", "LAST_DAY_MEMO", "DAY", wSCHOOL_NO, ref db);

            if (string.IsNullOrWhiteSpace(LASH_DAY_VAL))
            {
                LASH_DAY_VAL = "7";
            }

            //比較的日期
            var CompareDdays = DateTime.Now.AddDays(-1 * Convert.ToDouble(LASH_DAY_VAL));

            //最後一筆文章的日期
            var LastDdays = (
                         from c in Data.ADDT01List
                         select c.CRE_DATE
                        ).Union(
                         from e in Data.ADDT06List
                         select e.CRE_DATE
                    ).Max();

            var MemoList = BDMT02Service.GetRefDataList("SECI01", "LAST_DAY_MEMO", null, wSCHOOL_NO, ref db);

            string MoreDataString = "非常棒!!";
            string LessDataString = "很久沒寫文章了喔!!，再努力點喔!!";
            string NotDataString = "無任何文章，再努力再努力點喔!!";

            if (MemoList.Count > 0)
            {
                //大於等於
                MoreDataString = MemoList.Where(a => a.DATA_TYPE == "1").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? MoreDataString;

                //小於
                LessDataString = MemoList.Where(a => a.DATA_TYPE == "2").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? LessDataString;

                //完全沒資料
                NotDataString = MemoList.Where(a => a.DATA_TYPE == "3").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? NotDataString;
            }

            //沒文章
            if (LastDdays == null)
            {
                Data.Last_Day_MEMO = Data.NAME + " " + NotDataString;
            }
            else if (LastDdays < CompareDdays) //最後一筆文章的日期 小於 比較的日期
            {
                Data.Last_Day_MEMO = Data.NAME + " 最後投稿日(投稿, 閱讀): " + Convert.ToDateTime(LastDdays).ToString("yyyy/MM/dd") + @"，" + LessDataString;
            }
            else
            {
                Data.Last_Day_MEMO = Data.NAME + " 最後投稿日(投稿, 閱讀): " + Convert.ToDateTime(LastDdays).ToString("yyyy/MM/dd") + @"，" + MoreDataString;
            }
        }

        /// <summary>
        /// 老師首頁
        /// </summary>
        /// <returns></returns>
        public ActionResult TeacherIndex()
        {
            return View();
        }

        protected void GetBET02List(string SchoolNo)
        {
            ViewBag.BET02List = db.BET02.Include("BET02_LANG").
                Where(a => a.S_DATE <= DateTime.Today && a.E_DATE > DateTime.Today && a.ISPUBLISH == "Y" &&
                        (a.CLASS_TYPE == "1" || (a.SCHOOL_NO == SchoolNo && a.CLASS_TYPE == "2"))).
                            OrderByDescending(a => a.TOP_YN).
                                ThenByDescending(a => a.S_DATE).ThenByDescending(a => a.BULLET_ID).Take(5).ToList();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}