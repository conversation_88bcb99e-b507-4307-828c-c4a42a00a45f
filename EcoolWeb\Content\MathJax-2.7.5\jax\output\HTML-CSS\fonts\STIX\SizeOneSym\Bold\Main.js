/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeOneSym/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXSizeOneSym-bold"]={directory:"SizeOneSym/Bold",family:"STIXSizeOneSym",weight:"bold",Ranges:[[8512,8512,"All"]],32:[0,0,250,0,0],40:[1104,126,468,158,439],41:[1104,126,468,29,310],47:[1104,126,579,14,564],91:[1104,126,408,186,407],92:[1104,126,579,14,564],93:[1104,126,408,1,222],123:[1104,126,595,115,503],125:[1104,126,595,92,480],160:[0,0,250,0,0],8719:[1500,-49,1355,35,1321],8720:[1500,-49,1355,34,1320],8721:[1500,-49,1292,60,1215],8730:[1588,241,1061,109,1119],8968:[1104,126,476,186,470],8969:[1104,126,476,6,292],8970:[1104,126,476,184,470],8971:[1104,126,476,6,290],10216:[1104,126,579,99,481],10217:[1104,126,579,98,480]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeOneSym-bold");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeOneSym/Bold/Main.js");
