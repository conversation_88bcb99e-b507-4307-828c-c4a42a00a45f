/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Symbols/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Symbols={directory:"Symbols/Regular",family:"AsanaMathJax_Symbols",testString:"\u2300\u2304\u2305\u2306\u2310\u2319\u231C\u231D\u231E\u231F\u2320\u2321\u2329\u232A\u233D",32:[0,0,249,0,0],8960:[591,13,733,65,668],8964:[361,-18,688,65,623],8965:[515,-18,688,65,623],8966:[682,-18,688,65,623],8976:[360,-88,672,65,608],8985:[360,-88,672,65,608],8988:[713,-450,391,93,355],8989:[713,-450,391,36,298],8990:[91,172,391,93,355],8991:[91,172,391,36,298],8992:[1412,0,1371,537,1262],8993:[1412,0,1371,-36,689],9001:[733,192,381,53,309],9002:[733,192,381,53,309],9021:[660,119,669,18,652],9115:[885,0,442,53,412],9116:[1122,0,442,53,194],9117:[886,0,654,53,412],9118:[885,0,442,32,391],9119:[1122,0,442,249,391],9120:[886,0,442,32,391],9121:[888,0,408,86,374],9122:[1132,0,408,86,206],9123:[888,0,408,86,374],9124:[888,0,408,35,323],9125:[1132,0,408,203,323],9126:[888,0,408,35,323],9127:[721,0,627,246,578],9128:[757,0,627,51,382],9129:[715,7,627,246,578],9130:[688,0,627,246,382],9131:[721,0,627,51,382],9132:[757,0,627,246,578],9133:[722,0,627,51,382],9134:[1125,0,1371,537,689],9135:[300,-241,637,0,638],9138:[981,480,1701,132,1536],9139:[886,443,1701,124,1576],9143:[1388,0,987,63,738],10176:[521,-21,564,66,514],10177:[559,18,666,44,623],10178:[621,79,748,65,684],10179:[533,-8,668,55,615],10180:[533,-8,668,55,615],10181:[718,192,381,42,340],10182:[718,192,381,42,340],10183:[563,22,687,65,623],10184:[714,169,987,68,920],10185:[714,169,987,68,920],10186:[570,29,317,65,253],10190:[540,1,668,64,605],10191:[541,0,668,64,605],10192:[630,89,761,47,715],10193:[563,22,687,65,623],10194:[498,-44,665,70,596],10195:[476,-66,535,65,473],10196:[476,-66,535,65,473],10197:[515,-23,858,58,802],10198:[515,-23,858,58,802],10199:[515,-23,1009,76,934],10200:[671,129,748,65,684],10201:[671,129,748,65,684],10202:[541,0,1189,70,1120],10203:[541,0,1189,70,1120],10204:[446,-94,1016,65,952],10205:[579,40,969,85,885],10206:[579,40,969,85,885],10207:[671,129,748,65,684],10208:[630,89,626,47,580],10209:[578,37,558,44,515],10210:[578,37,640,53,588],10211:[578,37,640,53,588],10212:[541,0,782,59,724],10213:[541,0,782,58,724],10214:[726,184,484,79,440],10215:[726,184,484,45,406],10218:[713,172,581,67,515],10219:[713,172,581,67,515],10220:[709,191,384,87,298],10221:[709,191,384,87,298],10624:[713,172,620,71,550],10625:[521,-20,620,58,563],10626:[760,0,495,72,424],10627:[726,188,554,53,502],10628:[726,188,554,53,502],10629:[726,215,362,36,325],10630:[726,215,394,36,325],10631:[750,250,420,99,341],10632:[750,250,420,80,322],10633:[668,111,407,40,338],10634:[668,111,407,70,368],10635:[726,300,332,79,288],10636:[726,300,332,79,288],10637:[726,184,352,79,308],10638:[726,184,352,45,274],10639:[726,184,352,79,308],10640:[726,184,352,45,274],10641:[713,172,381,53,329],10642:[713,172,381,53,329],10643:[693,159,671,54,618],10644:[693,159,671,54,618],10645:[635,200,919,87,835],10646:[635,200,919,87,835],10649:[716,5,249,67,183],10650:[609,66,269,66,204],10651:[501,-40,544,65,480],10652:[541,0,668,64,605],10653:[541,0,668,64,605],10654:[474,-68,535,65,471],10655:[322,-73,535,65,471],10656:[410,81,544,69,476],10657:[405,2,621,65,557],10658:[559,18,535,65,471],10659:[559,18,535,65,471],10660:[615,72,535,65,471],10661:[615,72,535,65,471],10662:[380,-162,722,65,658],10663:[379,-161,722,65,658],10664:[589,41,544,65,480],10665:[589,41,544,65,480],10666:[589,41,544,65,480],10667:[589,41,544,65,480],10668:[479,-63,759,65,695],10669:[479,-63,759,65,695],10670:[479,-63,759,65,695],10671:[479,-63,759,65,695],10672:[578,26,733,65,668],10673:[714,13,733,65,668],10674:[852,13,733,65,668],10675:[871,13,733,65,668],10676:[871,13,733,65,668],10677:[587,46,761,18,744],10678:[587,46,668,18,652],10679:[587,46,668,18,652],10680:[587,46,668,18,652],10681:[587,46,668,18,652],10682:[587,46,668,18,652],10683:[587,46,668,18,652],10684:[587,46,668,18,652],10685:[858,96,643,18,624],10686:[587,46,668,18,652],10687:[587,46,668,18,652],10688:[587,46,668,18,652],10689:[587,46,668,18,652],10690:[587,46,875,18,858],10691:[587,46,942,18,925],10692:[541,0,668,64,605],10693:[541,0,668,64,605],10694:[541,0,668,64,605],10695:[541,0,668,64,605],10696:[541,0,668,64,605],10697:[645,147,911,64,848],10698:[633,92,660,65,596],10699:[463,181,660,65,596],10700:[544,0,660,65,596],10701:[544,3,671,15,650],10702:[670,117,833,65,769],10703:[514,-25,953,65,889],10704:[514,-25,953,65,889],10705:[515,-23,758,65,694],10706:[515,-23,758,65,694],10707:[515,-23,758,65,694],10708:[515,-23,758,65,694],10709:[518,-26,758,65,694],10710:[584,46,620,64,556],10711:[584,46,620,64,556],10712:[567,26,269,66,204],10713:[568,25,269,66,204],10714:[568,25,438,66,373],10715:[568,25,438,66,373],10716:[463,-65,897,55,835],10717:[570,29,897,55,843],10718:[615,100,897,55,843],10719:[446,-94,1363,65,1299],10720:[541,0,668,64,605],10721:[592,39,844,65,780],10722:[469,-73,822,62,760],10723:[539,-7,673,51,623],10724:[618,75,673,51,623],10725:[635,65,669,65,605],10726:[541,0,761,65,697],10727:[542,-10,605,51,555],10728:[543,2,660,65,596],10729:[543,2,660,65,596],10730:[739,195,761,47,715],10732:[587,281,668,18,652],10733:[587,281,668,18,652],10734:[725,183,668,64,605],10735:[725,183,668,64,605],10736:[875,240,761,47,715],10737:[814,301,761,47,715],10738:[802,290,669,18,652],10739:[802,290,669,18,652],10740:[518,-23,1206,85,1142],10742:[801,171,581,87,495],10743:[714,169,463,59,405],10746:[512,-7,605,51,555],10747:[512,-7,605,51,555],10748:[750,203,533,65,469],10749:[750,203,533,64,469],10750:[560,0,678,60,619],10751:[367,-197,678,60,619]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Symbols"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Symbols/Regular/Main.js"]);
