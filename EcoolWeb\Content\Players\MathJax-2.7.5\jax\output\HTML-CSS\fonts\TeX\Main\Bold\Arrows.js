/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/Arrows.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"],{8592:[518,17,1150,64,1084],8593:[694,193,575,14,561],8594:[518,17,1150,65,1085],8595:[694,194,575,14,561],8596:[518,17,1150,64,1085],8597:[767,267,575,14,561],8598:[724,194,1150,64,1084],8599:[724,193,1150,64,1085],8600:[694,224,1150,65,1085],8601:[694,224,1150,64,1085],8614:[518,17,1150,65,1085],8617:[518,17,1282,64,1218],8618:[518,17,1282,65,1217],8636:[518,-220,1150,64,1084],8637:[281,17,1150,64,1084],8640:[518,-220,1150,65,1085],8641:[281,17,1150,64,1085],8652:[718,17,1150,64,1085],8656:[547,46,1150,64,1085],8657:[694,193,703,30,672],8658:[547,46,1150,64,1084],8659:[694,194,703,30,672],8660:[547,46,1150,47,1102],8661:[767,267,703,30,672]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/Arrows.js");
