/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpSm/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXIntegralsUpSm,{32:[0,0,250,0,0],160:[0,0,250,0,0],8748:[690,189,587,52,605],8749:[690,189,817,52,835],8751:[690,189,682,52,642],8752:[690,189,909,52,869],8753:[690,189,480,52,447],8754:[690,189,480,52,448],8755:[690,189,480,52,470],10763:[694,190,556,41,515],10764:[694,190,1044,68,1081],10765:[694,190,420,68,391],10766:[694,190,420,68,391],10767:[694,190,520,39,482],10768:[694,190,324,41,380],10769:[694,190,480,52,447],10770:[694,190,450,68,410],10771:[690,189,450,68,412],10772:[690,189,550,68,512],10773:[690,189,450,50,410],10774:[694,191,450,50,410],10775:[694,190,611,12,585],10776:[694,190,450,48,412],10777:[694,190,450,59,403],10778:[694,190,450,59,403],10779:[784,189,379,68,416],10780:[690,283,357,52,400]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsUpSm/Regular/All.js");
