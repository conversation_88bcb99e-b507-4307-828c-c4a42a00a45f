// Modern jQuery code for ZZZI26 ManyStudentIndex page
$(document).ready(function() {
    // 表單處理模組
    const formHandler = {
        init: function() {
            window.Add = this.submitForm.bind(this);
            this.bindEvents();
            this.setupValidation();
        },

        bindEvents: function() {
            // 綁定表單提交事件
            $('form[name="form1"]').on('submit', this.handleFormSubmit.bind(this));
            
            // 綁定下拉選單變更事件
            $('#Search_CLASS_NO').on('change', this.handleClassChange.bind(this));
            $('#Search_NumType').on('change', this.handleNumTypeChange.bind(this));
        },

        submitForm: function() {
            if (this.validateForm()) {
                const form = document.form1;
                form.action = window.ZZZI26_URLS.editAction;
                form.submit();
            }
        },

        handleFormSubmit: function(e) {
            if (!this.validateForm()) {
                e.preventDefault();
                return false;
            }
        },

        validateForm: function() {
            let isValid = true;
            let errorMessages = [];

            // 驗證班級選擇
            const classValue = $('#Search_CLASS_NO').val();
            if (!classValue || classValue === '') {
                isValid = false;
                errorMessages.push('請選擇班級');
                this.showFieldError('#Search_CLASS_NO', '請選擇班級');
            } else {
                this.hideFieldError('#Search_CLASS_NO');
            }

            // 驗證人數類型
            const numTypeValue = $('#Search_NumType').val();
            if (!numTypeValue || numTypeValue === '') {
                isValid = false;
                errorMessages.push('請選擇人數類型');
                this.showFieldError('#Search_NumType', '請選擇人數類型');
            } else {
                this.hideFieldError('#Search_NumType');
            }

            if (!isValid) {
                this.showValidationSummary(errorMessages);
            } else {
                this.hideValidationSummary();
            }

            return isValid;
        },

        showFieldError: function(fieldSelector, message) {
            const $field = $(fieldSelector);
            const $parent = $field.closest('.form-group');
            
            // 移除現有錯誤
            $parent.find('.field-validation-error').remove();
            $field.removeClass('input-validation-error');
            
            // 添加新錯誤
            $field.addClass('input-validation-error');
            const $errorSpan = $('<span class="field-validation-error text-danger">' + message + '</span>');
            $field.closest('.col-md-9').append($errorSpan);
        },

        hideFieldError: function(fieldSelector) {
            const $field = $(fieldSelector);
            const $parent = $field.closest('.form-group');
            
            $field.removeClass('input-validation-error');
            $parent.find('.field-validation-error').remove();
        },

        showValidationSummary: function(messages) {
            let $summary = $('.validation-summary-errors');
            
            if ($summary.length === 0) {
                $summary = $('<div class="validation-summary-errors text-danger"><ul></ul></div>');
                $('.form-horizontal').prepend($summary);
            }
            
            const $list = $summary.find('ul');
            $list.empty();
            
            messages.forEach(function(message) {
                $list.append('<li>' + message + '</li>');
            });
            
            $summary.show();
        },

        hideValidationSummary: function() {
            $('.validation-summary-errors').hide();
        },

        handleClassChange: function(e) {
            const selectedClass = $(e.target).val();
            console.log('選擇的班級:', selectedClass);
            
            // 可以在這裡添加班級變更後的邏輯
            this.hideFieldError('#Search_CLASS_NO');
        },

        handleNumTypeChange: function(e) {
            const selectedNumType = $(e.target).val();
            console.log('選擇的人數類型:', selectedNumType);
            
            // 可以在這裡添加人數類型變更後的邏輯
            this.hideFieldError('#Search_NumType');
        },

        setupValidation: function() {
            // 設置即時驗證
            $('#Search_CLASS_NO, #Search_NumType').on('blur', this.validateField.bind(this));
        },

        validateField: function(e) {
            const $field = $(e.target);
            const fieldId = $field.attr('id');
            
            if (!$field.val()) {
                let message = '';
                switch(fieldId) {
                    case 'Search_CLASS_NO':
                        message = '請選擇班級';
                        break;
                    case 'Search_NumType':
                        message = '請選擇人數類型';
                        break;
                }
                this.showFieldError('#' + fieldId, message);
            } else {
                this.hideFieldError('#' + fieldId);
            }
        }
    };

    // 用戶體驗增強模組
    const uxEnhancer = {
        init: function() {
            this.enhanceDropdowns();
            this.addLoadingStates();
            this.addTooltips();
        },

        enhanceDropdowns: function() {
            // 為下拉選單添加更好的樣式和行為
            $('.form-control').on('focus', function() {
                $(this).closest('.form-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.form-group').removeClass('focused');
            });
        },

        addLoadingStates: function() {
            // 為提交按鈕添加載入狀態
            $('.btn').on('click', function() {
                const $btn = $(this);
                const originalText = $btn.text();
                
                $btn.prop('disabled', true)
                    .html('<i class="fa fa-spinner fa-spin"></i> 處理中...');
                
                // 如果表單驗證失敗，恢復按鈕狀態
                setTimeout(function() {
                    if ($('.validation-summary-errors:visible').length > 0) {
                        $btn.prop('disabled', false).text(originalText);
                    }
                }, 100);
            });
        },

        addTooltips: function() {
            // 為表單元素添加提示
            $('#Search_CLASS_NO').attr('title', '請選擇要代申請的班級');
            $('#Search_NumType').attr('title', '請選擇學生人數類型');
        }
    };

    // 無障礙功能模組
    const accessibilityEnhancer = {
        init: function() {
            this.addAriaLabels();
            this.addKeyboardNavigation();
        },

        addAriaLabels: function() {
            // 添加ARIA標籤
            $('#Search_CLASS_NO').attr('aria-label', '選擇班級');
            $('#Search_NumType').attr('aria-label', '選擇人數類型');
            $('.btn').attr('aria-label', '提交表單進入下一步');
        },

        addKeyboardNavigation: function() {
            // 添加鍵盤導航支援
            $(document).on('keydown', function(e) {
                if (e.which === 13 && e.target.tagName === 'SELECT') {
                    // Enter鍵在下拉選單上時，移動到下一個元素
                    const $current = $(e.target);
                    const $next = $current.closest('.form-group').next().find('select, button');
                    if ($next.length > 0) {
                        $next.focus();
                    }
                }
            });
        }
    };

    // 數據處理模組
    const dataHandler = {
        init: function() {
            this.loadInitialData();
        },

        loadInitialData: function() {
            // 檢查是否有預設值需要處理
            const classValue = $('#Search_CLASS_NO').val();
            const numTypeValue = $('#Search_NumType').val();
            
            if (classValue) {
                console.log('預設班級:', classValue);
            }
            
            if (numTypeValue) {
                console.log('預設人數類型:', numTypeValue);
            }
        },

        getFormData: function() {
            return {
                classNo: $('#Search_CLASS_NO').val(),
                numType: $('#Search_NumType').val(),
                modeVal: $('input[name="Search.ModeVal"]').val(),
                schoolNo: $('input[name="Search.SCHOOL_NO"]').val()
            };
        }
    };

    // 錯誤處理模組
    const errorHandler = {
        init: function() {
            this.setupErrorHandling();
        },

        setupErrorHandling: function() {
            $(window).on('error', function(e) {
                console.error('頁面錯誤:', e);
            });
        }
    };

    // 初始化所有模組
    formHandler.init();
    uxEnhancer.init();
    accessibilityEnhancer.init();
    dataHandler.init();
    errorHandler.init();

    // 設置全局 URL 配置（需要在 CSHTML 中定義）
    window.ZZZI26_URLS = window.ZZZI26_URLS || {};
});
