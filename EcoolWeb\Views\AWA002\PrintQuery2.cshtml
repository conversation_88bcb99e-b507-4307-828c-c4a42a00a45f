﻿@model EcoolWeb.ViewModels.AWA002Query2ViewModel
@{
    if (Model.WhereIsPassbook)
    {
        ViewBag.Title = "酷幣給點紀錄-我的數位存摺 PRINT";
    }
    else
    {
        ViewBag.Title = "酷幣給點紀錄-酷幣紀錄明細一覽表 PRINT";
    }

    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
@if (Model.WhereIsPassbook)
{
    <div class="Div-EZ-Right cScreen">
        <button id="ButtonExcel" class="btn btn-sm btn-sys">匯出excel</button>
    </div>
}



<div class="print table-92Per" style="margin: 0px auto; " id="tbData">
                    <table class="table-ecool table-ecool-pink-SEC" border="1">
                        <thead>
                            <tr></tr>
                            <tr class="text-center">
                                <th>
                                    日期
                                </th>
                                @if (Model.WhereIsPassbook)
                                {
                                    <th>
                                        支出
                                    </th>
                                    <th>
                                        存入
                                    </th>

                                    <th>
                                        給扣點人員
                                    </th>
                                    <th>
                                        目前點數
                                    </th>
                                }
                                else
                                {
                                    <th>
                                        學號
                                    </th>
                                    <th>
                                        班級
                                    </th>

                                    <th>
                                        座號
                                    </th>
                                    <th>
                                        姓名
                                    </th>
                                    <th>
                                        點數
                                    </th>
                                    <th>
                                        異動說明
                                    </th>
                                    <th>
                                        給點人員
                                    </th>
                                }
                              
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.VAWAT01_LOG)
                            {
                                <tr align="center">
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.LOG_TIME, "ShortDateTime")
                                    </td>
                                   @if (Model.WhereIsPassbook)
                                   {
                                    <td align="center">
                                        @if (item.CASH_IN < 0)
                                        {
                                            @(item.CASH_IN * -1);
                                        }
                                    </td>
                                    <td align="center">
                                        @if (item.CASH_IN >= 0)
                                        {
                                            @Html.DisplayFor(modelItem => item.CASH_IN)
                                        }
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.LOG_PERSON_NAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.AWAT01_CASH_AVAILABLE)
                                    </td>
                                   }
                                   else
                                   {
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.USER_NO)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CASH_IN)
                                    </td>
                                    <td style="text-align: left;white-space:normal">
                                        @Html.DisplayFor(modelItem => item.LOG_DESC)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.LOG_PERSON_NAME)
                                    </td>
                                   }

                                    
                                </tr>
                                }
                        </tbody>
                    </table>
   
</div>

<script type="text/javascript">
    window.onload =function()
    {
        window.print()
    }

    $(function () {
        $('#ButtonExcel').click(function () {
            var blob = new Blob([document.getElementById('tbData').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });
            var strFile = "Report.xls";
            saveAs(blob, strFile);
            return false;
        });
    });
</script>

