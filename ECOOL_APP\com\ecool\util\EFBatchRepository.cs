﻿using ECOOL_APP.EF;
using EntityFramework.Utilities;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.util
{
    public class EFBatchRepository : IDisposable
    {
        protected ECOOL_DEVEntities db;

        /// <summary>
        /// 會初始化 DbContext
        /// </summary>
        public EFBatchRepository()
        {
            db = new ECOOL_DEVEntities();
        }

        /// <summary>
        /// 使用原本的 DbContext
        /// </summary>
        /// <param name="database"></param>
        public EFBatchRepository(ECOOL_DEVEntities database)
        {
            this.db = database;
        }

        /// <summary>
        /// Insert 並儲存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        public void InsertAll<T>(IEnumerable<T> list) where T : class
        {
            try
            {

                EFBatchOperation.For(db, db.Set<T>()).InsertAll(list);
            }
            catch (Exception e) {

                string str = "";

               str = e.InnerException.StackTrace;
            }
        }

        /// <summary>
        /// Update 並儲存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="columnsToUpdateFunc"></param>
        public void UpdateAll<T>(IEnumerable<T> list, Expression<Func<T, object>> columnsToUpdateFunc) where T : class
        {
            EFBatchOperation.For(db, db.Set<T>()).UpdateAll(list, x => x.ColumnsToUpdate(columnsToUpdateFunc));
        }

        #region IDisposable 實作
        private bool disposed = false;
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        protected virtual void Dispose(bool disposing)
        {
            // Check to see if Dispose has already been called.
            if (!this.disposed)
            {
                if (disposing)
                {
                    db.Dispose();
                    db = null;
                }
                disposed = true;
            }
        }
        ~EFBatchRepository()
        {
            Dispose(false);
        }
        #endregion
    }
}
