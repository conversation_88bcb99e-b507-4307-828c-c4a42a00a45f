﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace ECOOL_APP
{
    public class FileHelper
    {
        public string GetMapPathImg(string SCHOOL_NO, string ControllersFile)
        {
            return string.Format(@"{0}{1}\{2}\", HttpContext.Current.Server.MapPath(ConfigHelpercs.GetappSettings("UploadImageRoot")), ControllersFile, SCHOOL_NO);
        }

        public string GetVirtualPathImg(string SCHOOL_NO, string ControllersFile)
        {
            return string.Format(@"{0}{1}\{2}\", ConfigHelpercs.GetappSettings("UploadImageRoot"), ControllersFile, SCHOOL_NO);
        }

        /// <summary>
        /// 取至檔案路徑
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="ControllersFile"></param>
        /// <param name="KeyNo"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysPathImg(string SCHOOL_NO, string ControllersFile, string KeyNo, string FileName)
        {
            if (string.IsNullOrWhiteSpace(FileName)) return string.Empty;

            string TempPath = $@"{GetVirtualPathImg(SCHOOL_NO, ControllersFile)}\{KeyNo}\{FileName}";

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return UrlCustomHelper.Url_Content(TempPath);
            }

            return string.Empty;
        }

        /// <summary>
        /// 取至功能目錄路徑
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="ControllersFile"></param>
        /// <param name="KeyNo"></param>
        /// <returns></returns>
        public string GetDirectorySysControllersFile(string SCHOOL_NO, string ControllersFile)
        {
            string TempPath = $@"{GetVirtualPathImg(SCHOOL_NO, ControllersFile)}";

            return TempPath;
        }

        public static int GetUploadCount(List<HttpPostedFileBase> files)
        {
            int UploadCount = 0;

            if (files != null)
            {
                foreach (var item in files)
                {
                    if ((item?.ContentLength ?? 0) > 0)
                    {
                        UploadCount++;
                    }
                }
            }

            return UploadCount;
        }

        public static string Cvt(long? FileSz) // 單位轉換
        {
            double Sz = FileSz ?? 0;

            string ret = null;
            if (Sz > 1099511627776)
            {
                ret = (Sz / 1099511627776).ToString("0.00 TB");
            }
            else if (Sz > 1073741824)
            {
                ret = (Sz / 1073741824).ToString("0.00 GB");
            }
            else if (Sz > 1048576)
            {
                ret = (Sz / 1048576).ToString("0.00 MB");
            }
            else
            {
                ret = (Sz / 1024).ToString("0.00 KB");
            }
            return ret;
        }

        public static long ToMb(long? FileSz)
        {
            if (FileSz == null) return 0;

            return (long)FileSz / (1024 * 1024);
        }

        public static string UpLoadFile(string SavePath, HttpPostedFileBase files, ref string Message)
        {
            string fileName = string.Empty;

            try
            {
                if (files != null)
                {
                    if (Directory.Exists(SavePath) == false)
                    {
                        Directory.CreateDirectory(SavePath);
                    }

                    if (files != null && files.ContentLength > 0)
                    {
                        fileName = Path.GetFileName(files.FileName);
                        string UpLoadFile = SavePath + "\\" + fileName;

                        files.SaveAs(UpLoadFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }

            return fileName;
        }

        /// <summary>
        /// 上傳
        /// </summary>
        /// <param name="file">檔案</param>
        /// <param name="UploadSysPath">上傳路徑</param>
        /// <param name="ErrorMsg">回傳錯誤訊息</param>
        /// <param name="ImgSize">是否調整圖片大小</param>
        /// <param name="KeyFile">是否有來源key的目錄</param>
        /// <param name="NewfileName">產生新檔名的名稱</param>
        /// <returns></returns>
        public static bool DoLoadFile(HttpPostedFileBase file, string UploadSysPath, ref string ErrorMsg
            , bool ImgSize = false
            , string KeyFile = null
            , string NewfileName = null)
        {
            if (file == null || file.ContentLength == 0) return false;

            try
            {
                string fileName = string.Empty;

                if (string.IsNullOrWhiteSpace(NewfileName))
                {
                    fileName = Path.GetFileName(file.FileName);
                }
                else
                {
                    fileName = NewfileName;
                }

                string tempPath = UploadSysPath;

                if (!string.IsNullOrWhiteSpace(KeyFile))
                {
                    tempPath = tempPath + $@"\{KeyFile}\";
                }

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(KeyFile))
                    {
                        //刪原檔
                        string[] tempFile = Directory.GetFiles(tempPath);

                        if (tempFile.Length >= 0)
                        {
                            foreach (var item in tempFile)
                            {
                                System.IO.File.Delete(item);
                            }
                        }
                    }
                }

                if (ImgSize)
                {
                    //c.縮圖
                    System.Drawing.Image image = System.Drawing.Image.FromStream(file.InputStream);
                    double FixWidth = 1100;
                    double FixHeight = 800;
                    double rate = 1;
                    if (image.Width > FixWidth || image.Height > FixHeight)
                    {
                        if (image.Width > FixWidth) rate = FixWidth / image.Width;
                        if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                        int w = Convert.ToInt32(image.Width * rate);
                        int h = Convert.ToInt32(image.Height * rate);
                        Bitmap imageOutput = new Bitmap(image, w, h);
                        imageOutput.Save(Path.Combine(tempPath, fileName), image.RawFormat);
                        imageOutput.Dispose();
                    }
                    else
                    {
                        //直接儲存
                        file.SaveAs(Path.Combine(tempPath, fileName));
                    }
                    image.Dispose();
                }
                else
                {
                    //直接儲存
                    file.SaveAs(Path.Combine(tempPath, fileName));
                }
            }
            catch (Exception ex)
            {
                ErrorMsg += file.FileName + "上傳失敗" + ex + "r\n";
                return false;
            }

            return true;
        }
    }
}