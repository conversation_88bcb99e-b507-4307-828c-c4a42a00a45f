/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/Latin1Supplement.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{160:[0,0,250,0,0],161:[474,205,389,59,321],162:[560,143,500,77,472],163:[670,8,500,10,517],164:[534,10,500,-22,522],165:[653,0,500,28,605],166:[666,18,275,105,171],167:[666,162,500,53,461],168:[606,-508,333,107,405],169:[666,18,760,41,719],170:[676,-406,276,42,352],171:[403,-37,500,53,445],172:[386,-108,675,86,590],173:[255,-192,333,49,282],174:[666,18,760,41,719],175:[583,-532,333,99,411],176:[676,-390,400,101,387],177:[568,0,675,86,590],178:[676,-271,300,33,324],179:[676,-268,300,43,339],180:[664,-494,333,180,403],181:[428,209,500,-30,497],182:[653,123,559,60,621],183:[310,-199,250,70,181],184:[0,217,333,-30,182],185:[676,-271,300,43,284],186:[676,-406,310,67,362],187:[403,-37,500,55,447],188:[676,10,750,33,736],189:[676,10,750,34,749],190:[676,10,750,23,736],191:[473,205,500,28,367],192:[914,0,611,-51,564],193:[914,0,611,-51,564],194:[911,0,611,-51,564],195:[874,0,611,-51,572],196:[856,0,611,-51,564],197:[957,0,611,-51,564],198:[653,0,889,-27,911],199:[666,217,667,66,689],200:[914,0,611,-1,634],201:[914,0,611,-1,634],202:[911,0,611,-1,634],203:[856,0,611,-1,634],204:[914,0,333,-8,398],205:[914,0,333,-8,414],206:[911,0,333,-8,450],207:[856,0,333,-8,457],208:[653,0,722,-8,700],209:[874,15,667,-20,727],210:[914,18,722,60,699],211:[914,18,722,60,699],212:[911,18,722,60,699],213:[874,18,722,60,699],214:[856,18,722,60,699],215:[497,-8,675,93,582],216:[722,105,722,60,699],217:[914,18,722,102,765],218:[914,18,722,102,765],219:[911,18,722,102,765],220:[856,18,722,102,765],221:[914,0,556,78,633],222:[653,0,611,0,569],223:[679,207,500,-168,493],224:[664,11,501,17,476],225:[664,11,501,17,476],226:[661,11,501,17,497],227:[624,11,501,17,521],228:[606,11,501,17,503],229:[709,11,501,17,476],230:[441,11,667,23,640],231:[441,217,444,26,425],232:[664,11,444,31,414],233:[664,11,444,31,431],234:[661,11,444,31,466],235:[606,11,444,31,475],236:[664,11,278,47,302],237:[664,11,278,47,318],238:[661,11,278,47,351],239:[606,11,278,47,361],240:[683,11,500,27,482],241:[624,9,500,14,488],242:[664,11,500,27,468],243:[664,11,500,27,468],244:[661,11,500,27,468],245:[624,11,500,27,494],246:[606,11,500,27,474],247:[517,11,675,86,590],248:[554,135,500,28,469],249:[664,11,500,42,475],250:[664,11,500,42,475],251:[661,11,500,42,475],252:[606,11,500,42,475],253:[664,206,444,-24,426],254:[683,205,500,-75,469],255:[606,206,444,-24,442]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/Latin1Supplement.js");
