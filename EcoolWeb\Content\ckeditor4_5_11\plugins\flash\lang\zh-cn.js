﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'zh-cn', {
	access: '允许脚本访问',
	accessAlways: '总是',
	accessNever: '从不',
	accessSameDomain: '同域',
	alignAbsBottom: '绝对底部',
	alignAbsMiddle: '绝对居中',
	alignBaseline: '基线',
	alignTextTop: '文本上方',
	bgcolor: '背景颜色',
	chkFull: '启用全屏',
	chkLoop: '循环',
	chkMenu: '启用 Flash 菜单',
	chkPlay: '自动播放',
	flashvars: 'Flash 变量',
	hSpace: '水平间距',
	properties: 'Flash 属性',
	propertiesTab: '属性',
	quality: '质量',
	qualityAutoHigh: '高(自动)',
	qualityAutoLow: '低(自动)',
	qualityBest: '最好',
	qualityHigh: '高',
	qualityLow: '低',
	qualityMedium: '中(自动)',
	scale: '缩放',
	scaleAll: '全部显示',
	scaleFit: '严格匹配',
	scaleNoBorder: '无边框',
	title: '标题',
	vSpace: '垂直间距',
	validateHSpace: '水平间距必须为数字格式',
	validateSrc: '请输入源文件地址',
	validateVSpace: '垂直间距必须为数字格式',
	windowMode: '窗体模式',
	windowModeOpaque: '不透明',
	windowModeTransparent: '透明',
	windowModeWindow: '窗体'
} );
