﻿using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using com.ecool.service;
using ECOOL_APP.com.ecool.service;
using MvcPaging;
using Dapper;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using EcoolWeb.Util;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using System.Net;
using Aspose.Words.Loading;
using Aspose.Words;
using System.Threading.Tasks;
using System.Threading;
using PuppeteerSharp;
using iTextSharp.text;
using SelectPdf;
using Spire.Pdf;
using System.Threading;
using System.IO;
using ECOOL_APP.com.ecool.Models;
using ECOOL_APP.com.ecool.LogicCenter;
using static ECOOL_APP.EF.HRMT01;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace EcoolWeb.Controllers
{
    public class ZZZI091Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string Title = "學習成果匯出";
        private static string Bre_NO = "ZZZI091";
        // GET: ZZZI09
        //[CheckPermission]
        public ActionResult Index()
        {
            ViewBag.Panel_Title = Title;
            this.Shared();
            var IDNOStr = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
            ViewBag.SchoolItems =db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO)
                     .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                     .Distinct()
                     .Select(s => new SelectListItem()
                     {
                         Text = s.SHORT_NAME,
                         Value = s.SCHOOL_NO
                     }).ToList(); 
            if (!string.IsNullOrWhiteSpace(IDNOStr))
            {
               var schoolDrop = db.HRMT01.Where(h => h.IDNO == IDNOStr)
                      .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                      .Distinct()
                      .Select(s => new SelectListItem()
                      {
                          Text = s.SHORT_NAME,
                          Value = s.SCHOOL_NO
                      }).ToList();
                ViewBag.SchoolItems = schoolDrop;
            }
        
       
            ViewBag.SCHOOL_NO = user.SCHOOL_NO;
            return View();
        }

      
       // GET: ZZZI09
       //[CheckPermission]
        public ActionResult Index2()
        {
            ViewBag.Panel_Title = Title;
            this.Shared();
            var IDNOStr = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
            ViewBag.SchoolItems = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO)
                     .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                     .Distinct()
                     .Select(s => new SelectListItem()
                     {
                         Text = s.SHORT_NAME,
                         Value = s.SCHOOL_NO
                     }).ToList();
            if (!string.IsNullOrWhiteSpace(IDNOStr))
            {
                var schoolDrop = db.HRMT01.Where(h => h.IDNO == IDNOStr)
                       .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                       .Distinct()
                       .Select(s => new SelectListItem()
                       {
                           Text = s.SHORT_NAME,
                           Value = s.SCHOOL_NO
                       }).ToList();
                ViewBag.SchoolItems = schoolDrop;
            }


            ViewBag.SCHOOL_NO = user.SCHOOL_NO;
            return View();
        }
        public ActionResult ExportResultView1(ZZZI09ExportResultViewViewModel model)
        {
            this.Shared();

            string Message = string.Empty;
            // string classNO = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.USER_NO == model.User_No && x.USER_STATUS == UserStaus.Enabled).Select(x => x.CLASS_NO).FirstOrDefault();

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = Message });
            }

            if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = "您無權查看此學生資料，必需是「管理者」或「班導」或「本人」才有權查看" });
            }

            if (string.IsNullOrEmpty(model.User_No) == false)
            {
                HRMT01 st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.USER_NO == model.User_No).FirstOrDefault();
                if (st != null)
                {
                    string SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == st.SCHOOL_NO).Select(x=>x.SCHOOL_NAME).FirstOrDefault();

                    // ViewBag.Panel_Title = st.CLASS_NO + "班-" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";
                    ViewBag.Panel_Title = SCHOOLNAME+st.NAME + "的國小回憶";

                     ViewBag.Title = ViewBag.Panel_Title;
                }
                string classNO = st.CLASS_NO;
                model.Class_NO = classNO;
            }

            return View(model);
        }
        [HttpPost]
        public ActionResult _GetSCHOOL_NODDLHtml(string tagId, string tagName, string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE)
        {
            user = EcoolWeb.Models.UserProfileHelper.Get();

            this.Shared();
            IQueryable<SelectListItem> SCHOOLNONoItems = null;
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var H01 = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO &&a.USER_STATUS==UserStaus.Enabled).FirstOrDefault();

                var UserNoListData = HRMT01.GetUserNoListData(wSCHOOL_NO, wCLASS_NO, ref db);
                var wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == H01.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

                ViewBag.slectIDNO = H01.IDNO;


              //  UserNoListData = db.HRMT01.Join(db.BDMT01).Where(a => a.IDNO == H01.IDNO && a.USER_TYPE == UserType.Student && (a.USER_STATUS == UserStaus.Enabled || a.USER_STATUS == UserStaus.Invalid))
              //.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
              //.Select(x => new SelectListItem { Text = x., Value = x.SCHOOL_NO });
                var _Join = db.HRMT01.Join(db.BDMT01, o => o.SCHOOL_NO, p => p.SCHOOL_NO, (o, c) => new { c.SHORT_NAME, c.SCHOOL_NO,o.IDNO,o.USER_STATUS,o.USER_TYPE}).Where(a => a.IDNO == H01.IDNO && a.USER_TYPE == UserType.Student && (a.USER_STATUS == UserStaus.Enabled || a.USER_STATUS == UserStaus.Invalid)).Distinct();
                UserNoListData = _Join.Distinct().Select(x => new SelectListItem { Text = x.SHORT_NAME, Value = x.SCHOOL_NO+"_"+x.IDNO }).Distinct();
                string _html = string.Empty;
                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                    //UserNoListData = UserNoListData.Where(a => a.Value == user.SCHOOL_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, H01.SCHOOL_NO + "_" + H01.IDNO, false, null);
                }
                else
                {
                    //if (user.USER_TYPE == "S")
                    //{
                    //    UserNoListData = UserNoListData.Where(a => a.Value == user.SCHOOL_NO);
                    //}
                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, H01.SCHOOL_NO+"_"+H01.IDNO, false, null);
                }

                return Content(_html);
            }
        }

        public ActionResult _SelectDiv(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, string ReadYN, ZZZI09ExportResultViewViewModel model)
        {
            ViewBag.Panel_Title = "選擇匯出功能";
          
            user = EcoolWeb.Models.UserProfileHelper.Get();
            var IDNOStr = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
            ViewBag.IDNO = IDNOStr;


           var schoolDrop = db.HRMT01.Where(h => h.IDNO == IDNOStr &&(h.USER_STATUS==UserStaus.Invalid|| h.USER_STATUS == UserStaus.Enabled))
                      .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                      .Distinct()
                      .Select(s => new SelectListItem()
                      {
                          Text = s.SHORT_NAME,
                          Value = s.SCHOOL_NO
                      }).ToList();
            if (!string.IsNullOrEmpty(model.School_No))
            {

                wSCHOOL_NO = model.School_No;
                wUSER_NO = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.USER_NO).FirstOrDefault();
                wCLASS_NO = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.CLASS_NO).FirstOrDefault();
            }
            else {
                wUSER_NO = user.USER_NO;
                wCLASS_NO = user.CLASS_NO;
            }
            ViewBag.SCHOOL_NO = wSCHOOL_NO;
            ViewBag.USER_NO = wUSER_NO;
            ViewBag.CLASS_NO = wCLASS_NO;
            ViewBag.DATA_ANGLE_TYPE = wDATA_ANGLE_TYPE;
          
           

            ViewBag.SchoolItems = schoolDrop;

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            var ClassItems = HRMT01.GetClassListData(wSCHOOL_NO, ref db).AsQueryable();

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData || wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                ClassItems = ClassItems.Where(a => a.Value == wCLASS_NO).AsQueryable();
            }
            if (user.USER_TYPE == "S")
            {
                if (!string.IsNullOrEmpty(model.School_No))
                {
                    string classNOstr = "";
                    classNOstr = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.CLASS_NO).FirstOrDefault();
                    ClassItems = ClassItems.Where(a => a.Value == classNOstr).AsQueryable();
                }
                else {
                    ClassItems = ClassItems.Where(a => a.Value == user.CLASS_NO).AsQueryable();
                }
                   
            }
            string TeachCLASS_NO = UserProfile.GetTeachCLASS_NO(user, ref db);
            if (user.USER_TYPE == "T" && TeachCLASS_NO != null) {

                ClassItems = ClassItems.Where(a => a.Value == TeachCLASS_NO).AsQueryable();
            }

            ViewBag.ClassItems = ClassItems;

            var USER_NOItems = HRMT01.GetUserNoListData(wSCHOOL_NO, "", ref db);

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                USER_NOItems = USER_NOItems.Where(a => a.Value == wUSER_NO).AsQueryable();
            }
            if (user.USER_TYPE == "S")
            {
                if (!string.IsNullOrEmpty(model.School_No))
                {
                    string classNOstr = "";
                    string USERNOstr = "";
                    byte? SYEARStr;
                    classNOstr = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.CLASS_NO).FirstOrDefault();
                    USERNOstr= db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.USER_NO).FirstOrDefault();
                    SYEARStr= db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.SYEAR).FirstOrDefault();
                    USER_NOItems = HRMT01.GetUserNoListDataForSchool(wSCHOOL_NO, classNOstr, USERNOstr, SYEARStr,ref db).AsQueryable();
                }
                else {

                    USER_NOItems = HRMT01.GetUserNoListData(wSCHOOL_NO, user.CLASS_NO, user.USER_NO, ref db).AsQueryable();

                }
                   
            }

            ViewBag.USER_NOItems = USER_NOItems;

            bool wIsQhisSchool = false;

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                var H01 = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO).FirstOrDefault();

                if (H01 != null)
                {
                    wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == H01.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

                    ViewBag.IDNO = H01.IDNO;

                    var SCHOOL_ITME = (from a in db.BDMT01
                                       join b in db.HRMT01 on new { a.SCHOOL_NO } equals new { b.SCHOOL_NO }
                                       where b.IDNO == H01.IDNO && b.USER_STATUS == UserStaus.Invalid
                                       select a).Select(x => new SelectListItem { Text = x.SHORT_NAME, Value = x.SCHOOL_NO }).Distinct().OrderBy(o => o.Value);

                    ViewBag.SCHOOL_NOItems = SCHOOL_ITME.ToList();
                }
            }

            ViewBag.wIsQhisSchool = wIsQhisSchool;

            return PartialView();
        }
        public ActionResult _SelectDiv1(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, string ReadYN, ZZZI09ExportResultViewViewModel model)
        {
            ViewBag.Panel_Title = "選擇匯出功能";

            user = EcoolWeb.Models.UserProfileHelper.Get();
            var IDNOStr = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
            ViewBag.IDNO = IDNOStr;
            var schoolDrop = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO)
                       .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                       .Distinct()
                       .Select(s => new SelectListItem()
                       {
                           Text = s.SHORT_NAME,
                           Value = s.SCHOOL_NO
                       }).ToList();
            if (!string.IsNullOrEmpty(model.School_No))
            {

                wSCHOOL_NO = model.School_No;
                wUSER_NO = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.USER_NO).FirstOrDefault();
                wCLASS_NO = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.CLASS_NO).FirstOrDefault();
            }
            else
            {
                wUSER_NO = user.USER_NO;
                wCLASS_NO = user.CLASS_NO;
            }
            ViewBag.SCHOOL_NO = wSCHOOL_NO;
            ViewBag.USER_NO = wUSER_NO;
            if (user.CLASS_NO == null && user.USER_TYPE=="T") {
                HRMT01 hrmt01temp = new HRMT01();
                hrmt01temp = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                if (hrmt01temp.CLASS_NO != null) {
                    wCLASS_NO = hrmt01temp.CLASS_NO;

                    wDATA_ANGLE_TYPE = UserProfileHelper.AngleVal.ClassData;
                }

            }
            ViewBag.CLASS_NO = wCLASS_NO;
            ViewBag.DATA_ANGLE_TYPE = wDATA_ANGLE_TYPE;



            ViewBag.SchoolItems = schoolDrop;

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }
            List<SelectListItem> ClassItemsGetitem = new List<SelectListItem>();
            var ClassItems = HRMT01.GetClassListData(wSCHOOL_NO, ref db).AsQueryable();
         
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData || wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                ClassItems = ClassItems.Where(a => a.Value == wCLASS_NO).AsQueryable();
            }
            if (user.USER_TYPE == "S")
            {
                if (!string.IsNullOrEmpty(model.School_No))
                {
                    string classNOstr = "";
                    classNOstr = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.CLASS_NO).FirstOrDefault();
                    ClassItems = ClassItems.Where(a => a.Value == classNOstr).AsQueryable();
                }
                else
                {
                    ClassItems = ClassItems.Where(a => a.Value == user.CLASS_NO).AsQueryable();
                }

            }
            ClassItemsGetitem.Add(new SelectListItem() { Text = "請選擇..", Value = "" });
            ClassItemsGetitem.AddRange(ClassItems.ToList());
            ViewBag.ClassItems = ClassItemsGetitem;
            Dictionary<string, List<string>> USerMOListStr = new Dictionary<string, List<string>>();
            var USER_NOItems = HRMT01.GetUserNoListData(wSCHOOL_NO, "", ref db);
            USerMOListStr = HRMT01.UserNoListDatestring(wSCHOOL_NO, "", ref db);
            ViewBag.UserNOstr = USerMOListStr;
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                USER_NOItems = USER_NOItems.Where(a => a.Value == wUSER_NO).AsQueryable();
            }
            if (user.USER_TYPE == "S")
            {
                if (!string.IsNullOrEmpty(model.School_No))
                {
                    string classNOstr = "";
                    string USERNOstr = "";
                    byte? SYEARStr;
                    classNOstr = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.CLASS_NO).FirstOrDefault();
                    USERNOstr = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.USER_NO).FirstOrDefault();
                    SYEARStr = db.HRMT01.Where(h => h.IDNO == IDNOStr && h.SCHOOL_NO == model.School_No).Select(x => x.SYEAR).FirstOrDefault();
                    USER_NOItems = HRMT01.GetUserNoListDataForSchool(wSCHOOL_NO, classNOstr, USERNOstr, SYEARStr, ref db).AsQueryable();
                }
                else
                {

                    USER_NOItems = HRMT01.GetUserNoListData(wSCHOOL_NO, user.CLASS_NO, user.USER_NO, ref db).AsQueryable();

                }

            }

            ViewBag.USER_NOItems = USER_NOItems;

            bool wIsQhisSchool = false;

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                var H01 = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO).FirstOrDefault();

                if (H01 != null)
                {
                    wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == H01.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

                    ViewBag.IDNO = H01.IDNO;

                    var SCHOOL_ITME = (from a in db.BDMT01
                                       join b in db.HRMT01 on new { a.SCHOOL_NO } equals new { b.SCHOOL_NO }
                                       where b.IDNO == H01.IDNO && b.USER_STATUS == UserStaus.Invalid
                                       select a).Select(x => new SelectListItem { Text = x.SHORT_NAME, Value = x.SCHOOL_NO }).Distinct().OrderBy(o => o.Value);

                    ViewBag.SCHOOL_NOItems = SCHOOL_ITME.ToList();
                }
            }

            ViewBag.wIsQhisSchool = wIsQhisSchool;

            return PartialView();
        }
        /// <summary>
        /// 【線上投稿成果匯出】
        /// </summary>
        /// <param name="School_No"></param>
        /// <param name="ShowOriginal"></param>
        /// <returns></returns>
        public ActionResult Details3(string School_No, string User_No, string S_DATE, string E_DATE, bool? ShowOriginal, string IDNO, string LogimUser)
        {
            UserProfile user = new UserProfile();
            if (LogimUser != "Y")
            {
                user = UserProfileHelper.Get();
            }
            ViewBag.IDNO = IDNO;
            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            string[] ArrUSER_NO;

            if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
            {
                ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
            }
            else
            {
                ArrUSER_NO = new[] { User_No };
                if (LogimUser != "Y")
                {
                    string IDNOSTR = "";
                    IDNOSTR = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
                    if (IDNO == IDNOSTR)
                    {

                        if (UserProfileHelper.CheckSeeStudentIDNOData(user, School_No, User_No, IDNO, ref db) == false)
                        {
                            return RedirectToAction("NotSeeDataError", "Error");
                        }

                    }
                    else
                    {
                        if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                        {
                            return RedirectToAction("NotSeeDataError", "Error");
                        }

                    }
                }
            }

            IQueryable<ADDT01> Temp;

            Temp = db.ADDT01.Include("ADDT02").Where(a => a.SCHOOL_NO == School_No && a.WRITING_STATUS != 9 && a.WRITING_STATUS != 8);

            if (string.IsNullOrWhiteSpace(User_No) == false)
            {
                Temp = Temp.Where(a => a.USER_NO == User_No);
            }
            else
            {
                User_No = ArrUSER_NO.FirstOrDefault();
            }

            if (ArrUSER_NO.Length > 0)
            {
                Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(S_DATE) == false)
            {
                DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
            }

            if (string.IsNullOrWhiteSpace(E_DATE) == false)
            {
                DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
            }

            List<ADDT01> ltaDDT01 = Temp.ToList();
            if (ltaDDT01 == null)
            {
                return HttpNotFound();
            }

            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageDictionaryUrl(ltaDDT01);

            //取出學校名稱
            ViewBag.SchoolName = db.BDMT01.Where(p => p.SCHOOL_NO == School_No).FirstOrDefault().SHORT_NAME;

            //取出學生名稱 及 學習期間

            //取出學生名稱 及 學習期間
            ViewBag.UserName = db.HRMT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Select(p => p.NAME).FirstOrDefault();

            if (ltaDDT01.Count() > 0)
            {
                //學習期間
                ViewBag.SYear = ltaDDT01.Min(a => a.SYEAR).Value.ToString() + "學年度 ~ " + ltaDDT01.Max(a => a.SYEAR).Value.ToString() + "學年度";
            }

            //取出酷幣點數
            AWAT01 UserCash = db.AWAT01.Where(a => a.SCHOOL_NO == School_No && a.USER_NO == User_No).FirstOrDefault();
            if (UserCash != null)
            {
                ViewBag.CoolCash = UserCash.CASH_AVAILABLE.Value;
            }
            //線上投稿總篇數
            ViewBag.SumAcount = db.ADDT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Count();

            //閱讀認證等級
            var TempLEVEL = (from a09 in db.ADDT09
                             join a08 in db.ADDT08
                                   on new { a09.SCHOOL_NO, LEVEL_ID = (byte)a09.LEVEL_ID }
                               equals new { a08.SCHOOL_NO, LEVEL_ID = a08.LEVEL_ID }
                             where a09.USER_NO == User_No && a09.SCHOOL_NO == School_No
                             select new ADDT0809
                             {
                                 LEVEL_DESC = a08.LEVEL_DESC
                             }).FirstOrDefault();

            if (TempLEVEL != null)
            {
                ViewBag.PassPort = TempLEVEL.LEVEL_DESC;
            }
            else
            {
                ViewBag.PassPort = "";
            }

            //預設顯示老師批閱的文章
            bool ChangeARTICLE_VERIFY = true;
            if (ShowOriginal.HasValue) if (ShowOriginal.Value) ChangeARTICLE_VERIFY = false;
            if (ChangeARTICLE_VERIFY)
            {
                foreach (var aDDT01 in ltaDDT01)
                {
                    if (string.IsNullOrWhiteSpace(aDDT01.ARTICLE_VERIFY) == false)
                    {
                        if (aDDT01.ARTICLE_VERIFY != null) aDDT01.ARTICLE_VERIFY = aDDT01.ARTICLE_VERIFY.Replace("\r\n", "");
                        //db.Entry(aDDT01).State = EntityState.Detached; //會造成ADDT02不見
                        aDDT01.ARTICLE = aDDT01.ARTICLE_VERIFY;
                    }
                }
            }

            return PartialView(ltaDDT01);
        }
        public List<Image_File_Multiple> GetImageDictionaryUrl(List<ADDT01> liaDDT01)
        {
            List<Image_File_Multiple> dicImage = new List<Image_File_Multiple>();

            foreach (var item in liaDDT01)
            {
                dicImage.Add(new Image_File_Multiple { APPLY_NO = item.WRITING_NO, ImageUrl = GetImageUrl(item) });
            }
            return dicImage;
        }
        public List<string> GetImageUrl(ADDT01 aDDT01)
        {
            return GetImagePath(aDDT01);
        }
        private string GetImageUrl(ADDT06 aDDT06)
        {
            if (string.IsNullOrEmpty(aDDT06.IMG_FILE) == false)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";

                var sysPath = System.Web.HttpContext.Current.Request.MapPath(UploadImageRoot);

                string imgPath = string.Format(@"{0}ADDT06IMG\{1}\", sysPath, aDDT06.SCHOOL_NO.ToString());

                if (System.IO.File.Exists(Path.Combine(imgPath, aDDT06.IMG_FILE)))
                {
                    string imgUrl = string.Format(@"{0}ADDT06IMG\{1}\{2}", UploadImageRoot, aDDT06.SCHOOL_NO, aDDT06.IMG_FILE);

                    var strUrl = string.Empty;

                    try
                    {
                        strUrl = Url.Content(imgUrl);
                    }
                    catch (Exception)
                    {
                        strUrl = imgUrl;
                    }

                    return strUrl;
                }
            }
            return string.Empty;
            //return string.Format(Url.Content("~/Content/SchoolIMG/{0}/ADDT06.jpg"), aDDT06.SCHOOL_NO);
        }
        public List<string> GetImagePath(ADDT01 aDDT01, bool Player = true)
        {
            List<string> ImagePath = new List<string>();

            if (string.IsNullOrEmpty(aDDT01.IMG_FILE) == false)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];

                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";

                string imgPath = string.Format(@"{0}ADDI01IMG\{1}\", System.Web.HttpContext.Current.Server.MapPath(UploadImageRoot), aDDT01.SCHOOL_NO.ToString());

                List<string> ArrImg = aDDT01.IMG_FILE.Split('|').ToList();

                foreach (var Img in ArrImg)
                {
                    string url = Img;
                    string error;
                    if (LogicCenter.YoutubeUrlConvert(ref url, out error))
                    {
                        ImagePath.Add(url);
                        continue;
                    }

                    if (System.IO.File.Exists(Path.Combine(imgPath, Img)))
                    {
                        string imgUrl = ECOOL_APP.UrlCustomHelper.Url_Content(string.Format(@"{0}ADDI01IMG\{1}\{2}", UploadImageRoot, aDDT01.SCHOOL_NO, Img));
                        ImagePath.Add(imgUrl);
                    }
                }

                if (ImagePath.Count > 0)
                {
                    return ImagePath;
                }
            }
            if (aDDT01.PLAYER_NO != null && Player == true)
            {




                string ImageUrl = @"~/Content/Players/";

                ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(ImageUrl + db.AWAT06.Find(aDDT01.PLAYER_NO).IMG_FILE));
                return ImagePath;


                //ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(string.Format("~/Content/SchoolIMG/{0}/ADDI01.jpg", aDDT01.SCHOOL_NO)));

            }

            if (aDDT01.PLAYER_NO == null && Player)
            {
                //沒有上傳圖片則改放角色娃娃
                AWAT07 WriterPlayer =
                    db.AWAT07.Where(a => a.SCHOOL_NO == aDDT01.SCHOOL_NO && a.USER_NO == aDDT01.USER_NO && a.DEFAULT_YN == true)
                    .FirstOrDefault();

                if (WriterPlayer != null)
                {
                    string ImageUrl = @"~/Content/Players/";

                    ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(ImageUrl + db.AWAT06.Find(WriterPlayer.PLAYER_NO).IMG_FILE));
                    return ImagePath;
                }

                ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(string.Format("~/Content/SchoolIMG/{0}/ADDI01.jpg", aDDT01.SCHOOL_NO)));
            }
            return ImagePath;
        }

        public ActionResult _SEI05Partial1(SECI05IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            SECI05Service Service = new SECI05Service();

            //  ViewBag.Panel_Title = "學生閱讀狀況";
            //  this.Shared(ViewBag.Panel_Title);

            if (model.LogimUser != "Y")
            {
                user = UserProfileHelper.Get();
            }
            List<string> MyPanyStudent = new List<string>();

            //if (user == null)
            //{
            //    return RedirectToAction("SessionTimeOutError", "Error");
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            //{
            //    model.WhereSCHOOL_NO = user.SCHOOL_NO;
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO ?? user.TEACH_CLASS_NO;
            //}

            //if (user.USER_TYPE == UserType.Student)
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO;
            //    model.WhereUSER_NO = user.USER_NO;
            //}
            //else if (user.USER_TYPE == UserType.Parents)
            //{
            //    var Hr06 = HRMT06.GetMyPanyStudent(user, db);

            //    if (Hr06 != null)
            //    {
            //        if (Hr06.Count() > 0)
            //        {
            //            MyPanyStudent = Hr06.Select(a => a.STUDENT_USER_NO).ToList();
            //        }
            //    }

            //    if (MyPanyStudent.Count >= 1 && string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            //    {
            //        model.WhereUSER_NO = MyPanyStudent.FirstOrDefault();
            //    }
            //}

            //List<SelectListItem> SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.WhereSCHOOL_NO, null, user, true, null, null, false);

            //if (!HRMT24_ENUM.CheckQQutSchool(user))
            //{
            //    SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == model.WhereSCHOOL_NO).ToList();
            //}
            //ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            //var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            //if (!HRMT24_ENUM.CheckQAdmin(user))
            //{
            //    if (user.TEACH_CLASS_NO == null)
            //    {
            //        if (HRMT24_ENUM.IsOtherTeacher(user) == false)
            //        {
            //            ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //        }
            //    }
            //    else
            //    {
            //        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //    }
            //}
            //ViewBag.ClassItems = ClassItems;

            //List<SelectListItem> USER_NOItems = null;

            //if (user.USER_TYPE == UserType.Parents)
            //{
            //    USER_NOItems = HRMT01.GetUserNoListDataForP(model.WhereUSER_NO, user, db).ToListNoLock();
            //}
            //else
            //{
            //    USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db, true);
            //}

            //ViewBag.USER_NOItems = USER_NOItems;

            if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
           
                model = Service.GetBorrowData(model, user, ref db);
              
                if (model.MyBorrow != null)
                {
                    model.BorrowColumnChart = GetBorrowColumnChart(model, 750, 1000);
                    model.GradeQtyCharts = GetGradeQtyCharts(model.MyBorrow, 750, 1000);
                }

                List<SelectListItem> GradeSeyearItem = new List<SelectListItem>();

                int InSchoolYear = Convert.ToInt32(model.WhereUSER_NO.Substring(0, 3));

                GradeSeyearItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSEYEAR) });

                foreach (var Item in Enum.GetValues(typeof(GradeVal)))
                {
                    GradeSeyearItem.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = (InSchoolYear + ((byte)Item - 1)).ToString(), Selected = (InSchoolYear + ((byte)Item - 1)).ToString() == model.WhereSEYEAR });
                }

                ViewBag.GradeSeyearItem = GradeSeyearItem;
            }

            return View(model);
        }
        public async Task<ActionResult> Index3(ZZZI09ExportResultViewViewModel model)
        {
           // await SomeJobBy3rdPtyLibrary();
            this.Shared();
            EcoolWeb.Service.HTMLtoPDF htmlService = new Service.HTMLtoPDF();
            string str =
         $"http://127.0.0.1/" + $@"Ecool/ZZZI091/ExportResultView3?School_No=" + model.School_No + "&USER_NO=" + model.User_No + "&S_DATE=&E_DATE=&IDNO=&COVERJPG=coverA.jpg&UploadCoverFileName=&redirect=s&ReadYN=false&&LogimUser=Y";
          
            string googledriverPth = System.Web.HttpContext.Current.Server.MapPath(@"~\Content\.local-chromium\Win64-706915\chrome-win\chrome.exe");
            string filestr =
          System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\");
            string filestr1 =
           System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\" + model.User_No+ ".html");
            string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\" + model.User_No + ".pdf");
            if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\")))
            {
                if (System.IO.File.Exists(filestrPDF))
                {


                    System.IO.File.Delete(filestrPDF);
                }

            }

            // await htmlService.AppetizerAsync(str, filestrPDF);

            var firstUrl = $"https://github.com/explore";
            var secondUrl = $"http://127.0.0.1/Ecool//ZZZI091/ExportResultView3?School_No=111111&USER_NO=107038";
            byte[] mergePage = null;
            await new BrowserFetcher().DownloadAsync(BrowserFetcher.DefaultRevision);
            //var browser1 = await Puppeteer.ConnectAsync(new ConnectOptions
            //{
            //    BrowserURL = "http://127.0.0.1:2122"
            //});
            var pdfUrl = new[] { firstUrl, secondUrl };
            //var pdfUrl = new[] {str };

            using (var browser = await Puppeteer.LaunchAsync(new LaunchOptions()
            {
                Headless = true, //偵測時可設定false觀察網頁顯示結果(註：非Headless時不能匯出PDF)
               // ExecutablePath = @"C:\Program Files\Google\Chrome\Application\chrome.exe"
            }))
            {
                //using (var stream = System.IO.File.Create(@"D:\test\test.pdf"))
                //using (var doc = new iTextSharp.text.Document(PageSize.A4))
                //using (var pdfWriter = iTextSharp.text.pdf.PdfWriter.GetInstance(doc, stream))
                using (var page = await browser.NewPageAsync())

                {

                    //doc.Open();
                    //   await page.GoToAsync(secondUrl, new NavigationOptions
                    //{
                    //    //此方法可以偵測JS連線都完成了
                    //    WaitUntil = new WaitUntilNavigation[2]
                    //});
                
                        await page.GoToAsync("http://127.0.0.1/Ecool//ZZZI091/ExportResultView3?School_No=111111&USER_NO=107038");
                        Thread.Sleep(7000);
                        await page.ScreenshotAsync($"D:\\test\\Snapshot.png");
             

                    //但用上述waitUntil方式我的目標網站渲染方式不同，所以最後還是加上了等待。

                    //Thread.Sleep(8000);
                    //    mergePage = await page.ScreenshotDataAsync(new ScreenshotOptions
                    //    {
                    //        OmitBackground = true,      //是否顯示背景圖式
                    //        FullPage = true,        //是否整頁
                    //        Type = ScreenshotType.Jpeg,     //可以設定檔案格式
                    //        Quality = 100           //可以設定照片品質
                    //    });
                        //檔案都取回後再透過itextSharp進行處理
                        //var image = iTextSharp.text.Image.GetInstance(mergePage);
                        //這邊可以調整圖片大小
                        //image.ScalePercent(60);
                    ////    doc.Add(image);
                
                    ////doc.Close();
                    ////doc.Dispose();
                }
            }
            return Content("Done");
        }

        #region 借閱類別統計表 長條圖

        /// <summary>
        ///  借閱類別統計表
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetBorrowColumnChart(SECI05IndexViewModel model, int HightNum, int WidthNum)
        {
            var returnPoint = new List<Point>();

            foreach (var item in model.BorrowTypeQty)
            {
                returnPoint.Add(new Point
                {
                    Y = item.QTY
                    ,
                    Name = item.TYPE_NAME
                });
            }

            Data data = new Data(returnPoint.ToArray());
            if (HightNum > 0)
            {
                Highcharts TempPreColumnChart = new Highcharts("TempBorrowColumnChart")
        .InitChart(new Chart
        {
            DefaultSeriesType = ChartTypes.Column,

            Height = HightNum,
            Width = WidthNum
        })
        .SetTitle(new Title { Text = "借閱類別統計表" })
        .SetXAxis(new XAxis
        {
            Type = AxisTypes.Category
                     ,
            Labels = new XAxisLabels
            {
                Rotation = -45,
                Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
            }
        })
        .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借閱數量" }, AllowDecimals = false })
        .SetSeries(new[]
                {
                        new Series {
                            Data = data,
                            Name = "借閱數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                })
        .SetPlotOptions(new PlotOptions
        {
            Column = new PlotOptionsColumn
            {
                DataLabels = new PlotOptionsColumnDataLabels
                {
                    Enabled = true,
                    Rotation = 0,
                    Color = System.Drawing.Color.Black,
                    Format = "{point.y:.1f}",
                    Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                }
            }
        })
        .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
        .SetLegend(new Legend { Enabled = false });
                chartsHelper.SetCopyright(TempPreColumnChart);

                return TempPreColumnChart;
            }
            else
            {
                Highcharts TempPreColumnChart = new Highcharts("TempBorrowColumnChart")
               .InitChart(new Chart
               {
                   DefaultSeriesType = ChartTypes.Column,
               })
               .SetTitle(new Title { Text = "借閱類別統計表" })
               .SetXAxis(new XAxis
               {
                   Type = AxisTypes.Category
                            ,
                   Labels = new XAxisLabels
                   {
                       Rotation = -45,
                       Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
                   }
               })
               .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借閱數量" }, AllowDecimals = false })
               .SetSeries(new[]
                       {
                        new Series {
                            Data = data,
                            Name = "借閱數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                       })
               .SetPlotOptions(new PlotOptions
               {
                   Column = new PlotOptionsColumn
                   {
                       DataLabels = new PlotOptionsColumnDataLabels
                       {
                           Enabled = true,
                           Rotation = 0,
                           Color = System.Drawing.Color.Black,
                           Format = "{point.y:.1f}",
                           Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                       }
                   }
               })
               .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
               .SetLegend(new Legend { Enabled = false });
                chartsHelper.SetCopyright(TempPreColumnChart);

                return TempPreColumnChart;
            }
        }

        #endregion 借閱類別統計表 長條圖

        /// <summary>
        /// 產生 個人借閱數量趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetGradeQtyCharts(SECI05MyBorrowViewModel Data, int HightNum, int WidthNum)
        {
            if (Data == null)
            {
                return null;
            }

            List<int> GradeQtys = new List<int>();
            GradeQtys.Add(Data.QTY_GRADE_1);
            GradeQtys.Add(Data.QTY_GRADE_2);
            GradeQtys.Add(Data.QTY_GRADE_3);
            GradeQtys.Add(Data.QTY_GRADE_4);
            GradeQtys.Add(Data.QTY_GRADE_5);
            GradeQtys.Add(Data.QTY_GRADE_6);

            List<string> Grades = new List<string>();
            foreach (var Item in Enum.GetValues(typeof(GradeVal)))
            {
                Grades.Add(ParserGrade((byte)Item));
            }
            if (HightNum > 0)
            {
                Highcharts GradeQtyChart = new Highcharts("GradeQtyChart");

                GradeQtyChart
                .InitChart(new DotNet.Highcharts.Options.Chart
                {
                    DefaultSeriesType = ChartTypes.Line,
                    Height = HightNum,
                    Width = WidthNum
                })
                .SetTitle(new Title { Text = "個人借閱數量趨示圖", X = 0 })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = Grades.ToArray() })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "本" }, Min = 0 })
                .SetPlotOptions(new PlotOptions
                {
                    Line = new PlotOptionsLine
                    {
                        DataLabels = new PlotOptionsLineDataLabels
                        {
                            Enabled = true
                        },
                        EnableMouseTracking = false
                    }
                })
                .SetTooltip(new Tooltip { ValueSuffix = "本" })
                .SetSeries(new Series[]
                            {
                                 new Series
                                 {
                                     Name ="各年級借閱數量",
                                   Data = new DotNet.Highcharts.Helpers.Data( GradeQtys.Cast<object>().ToArray())
                                 }
                            }
                );

                chartsHelper.SetCopyright(GradeQtyChart);

                return GradeQtyChart;
            }
            else
            {
                Highcharts GradeQtyChart = new Highcharts("GradeQtyChart");

                GradeQtyChart
                .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
                .SetTitle(new Title { Text = "個人借閱數量趨示圖", X = 0 })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = Grades.ToArray() })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "本" }, Min = 0 })
                .SetPlotOptions(new PlotOptions
                {
                    Line = new PlotOptionsLine
                    {
                        DataLabels = new PlotOptionsLineDataLabels
                        {
                            Enabled = true
                        },
                        EnableMouseTracking = false
                    }
                })
                .SetTooltip(new Tooltip { ValueSuffix = "本" })
                .SetSeries(new Series[]
                            {
                                 new Series
                                 {
                                     Name ="各年級借閱數量",
                                   Data = new DotNet.Highcharts.Helpers.Data( GradeQtys.Cast<object>().ToArray())
                                 }
                            }
                );

                chartsHelper.SetCopyright(GradeQtyChart);

                return GradeQtyChart;
            }
        }
        private Task SomeJobBy3rdPtyLibrary()
        {
            return Task.Factory.StartNew(() =>
            {
                Thread.Sleep(3000);
            });
        }
        public ActionResult useBatGenertPdftemp(ZZZI09ExportResultViewViewModel model)
        {
            HRMT01 hrmt = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.CLASS_NO== model.Class_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            if (hrmt != null)
            {

                model.Class_NO = hrmt.CLASS_NO;
         
            string SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == hrmt.SCHOOL_NO).Select(x => x.SCHOOL_NAME).FirstOrDefault();

            // ViewBag.Panel_Title = st.CLASS_NO + "班-" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";
            //ViewBag.Panel_Title = SCHOOLNAME + st.NAME + "的國小回憶";
           List<HRMT01> hRMT01s= db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.CLASS_NO == model.Class_NO && x.USER_STATUS == UserStaus.Enabled).ToList();
            foreach (var item in hRMT01s) { 
            string FileName = SCHOOLNAME + item.NAME + "的國小回憶";
            string FILENAMESTR = "";
            FILENAMESTR = model.Class_NO + "temp";

            // string str =
            //UrlCustomHelper.GetOwnWebUri() + $@"/ZZZI091/ExportResultView3?School_No=" + model.School_No + "&USER_NO=" + model.User_No + "&S_DATE=&E_DATE=&IDNO=&COVERJPG=coverA.jpg&UploadCoverFileName=&redirect=s&ReadYN=false&&LogimUser=Y";
            string str =
            UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI091/ExportResultView3?School_No=" + model.School_No + "&USER_NO=" + item.USER_NO + "&S_DATE=&E_DATE=&IDNO=&COVERJPG=" + model.COVERJPG + "&UploadCoverFileName=" + model.UploadCoverFileName + "&redirect=" + model.redirect + "&ReadYN=" + model.ReadYN + "&&LogimUser=Y";
            string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + FILENAMESTR + @"\" + FileName + ".pdf");
            if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\")))
            {
                if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\")))
                {
                    if (System.IO.File.Exists(filestrPDF))
                    {


                        System.IO.File.Delete(filestrPDF);
                    }

                }
                else
                {
                    Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + FILENAMESTR + @"\"));

                }
            }
            else
            {
                Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\"));
                Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + FILENAMESTR + @"\"));

            }
            string FilnAME = "my_script" + hrmt.SCHOOL_NO + item.USER_NO + "FILENAMESTR.bat";
            //string FilnAME = "my_script.bat";
            string batFileName = @"D:\" + FilnAME; // 设置要创建的批处理文件的文件名
            string batScript = $@"
                                   @echo off
                                     chcp 65001 > nul
                                   setlocal enabledelayedexpansion

                                   rem 设置变量
                                   set ""url={str}""
                                   set ""output_file={filestrPDF}""
                                   
                                   rem 启动 Chrome 以打开网址并保存为 PDF
                                   ""C:\Program Files\Google\Chrome\Application\chrome.exe"" --headless --disable-gpu --print-to-pdf=""!output_file!"" --virtual-time-budget=180000  -- ""!url!""
                                  timeout /t 120
                                   endlocal
                                         ";




            // 将批处理脚本写入文件
            System.IO.File.WriteAllText(batFileName, batScript);

            //// 调用批处理文件
            System.Diagnostics.Process.Start(batFileName);
             Thread.Sleep(10000);
                    //// 创建一个 Process 对象来执行批处理文件
                    //Process process = new Process();
                    //process.StartInfo.FileName = batFileName;
                    //process.StartInfo.UseShellExecute = false;
                    //process.StartInfo.RedirectStandardOutput = true;

                    //// 启动批处理文件
                    //process.Start();

                    // 等待批处理文件执行完毕
                    //process.WaitForExit();
                    // 删除生成的批处理文件
                    //System.IO.File.Delete(batFileName);
                }
            }
            return new JsonResult()
            {

                Data = new
                {

                    resault = true
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };



        }


        public ActionResult useBatGenertPdf(ZZZI09ExportResultViewViewModel model) {
            HRMT01 hrmt= db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.USER_NO == model.User_No && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            if (hrmt != null) {

                model.Class_NO = hrmt.CLASS_NO;
            }
            string SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == hrmt.SCHOOL_NO).Select(x => x.SCHOOL_NAME).FirstOrDefault();
            int CLASSCOUNTSYU = 0;
            CLASSCOUNTSYU = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.CLASS_NO == model.Class_NO && x.USER_STATUS == UserStaus.Enabled).Count();

            // ViewBag.Panel_Title = st.CLASS_NO + "班-" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";
            //ViewBag.Panel_Title = SCHOOLNAME + st.NAME + "的國小回憶";

            string FileName = SCHOOLNAME + hrmt.NAME + "的國小回憶";
            // string str =
            //UrlCustomHelper.GetOwnWebUri() + $@"/ZZZI091/ExportResultView3?School_No=" + model.School_No + "&USER_NO=" + model.User_No + "&S_DATE=&E_DATE=&IDNO=&COVERJPG=coverA.jpg&UploadCoverFileName=&redirect=s&ReadYN=false&&LogimUser=Y";
            //string str =
            //UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI091/ExportResultView3?School_No=" + model.School_No + "&USER_NO=" + model.User_No + "&S_DATE=&E_DATE=&IDNO=&COVERJPG="+ model.COVERJPG + "&UploadCoverFileName=" + model.UploadCoverFileName + "&redirect=" + model.redirect + "&ReadYN="+model.ReadYN+"&&LogimUser=Y";
            string strtest = "https://ecc.tp.edu.tw:443/";
            string str =
          strtest+$@"/EcoolWeb/ZZZI091/ExportResultView3?School_No=" + model.School_No + "&USER_NO=" + model.User_No + "&S_DATE=&E_DATE=&IDNO=&COVERJPG="+ model.COVERJPG + "&UploadCoverFileName=" + model.UploadCoverFileName + "&redirect=" + model.redirect + "&ReadYN="+model.ReadYN+"&&LogimUser=Y";
            string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + model.Class_NO + @"\" + FileName + ".pdf");
            if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\")))
            {
                if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\")))
                {
                    if (System.IO.File.Exists(filestrPDF))
                    {


                        System.IO.File.Delete(filestrPDF);
                    }

                }
                else
                {
                    Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + model.Class_NO + @"\"));

                }
            }
            else {
                Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" ));
                Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + model.Class_NO + @"\"));

            }
              string FilnAME = "my_script" + hrmt.SCHOOL_NO+ hrmt.USER_NO + ".bat";
            //string FilnAME = "my_script.bat";
            DateTime now = DateTime.Now;

            // Use the ticks (a unique number representing the current date and time) as the seed for the random number generator
            int seed = now.Millisecond; // Using Millisecond for better uniqueness, but you can use other components too

            // Initialize the random number generator with the seed
            Random rand = new Random(seed);

            // Generate a random string containing numbers and English letters
            // Generate an 8-character random string
            user = UserProfileHelper.Get();
            string randomString = user.USER_NO;
            string dictioneryPath = "";
            dictioneryPath = @"D:\BATTemp\" + user.USER_NO + @"\";
           string batFileName = @"D:\BATTemp\" + user.USER_NO + @"\"+ FilnAME; // 设置要创建的批处理文件的文件名
            if (!Directory.Exists(dictioneryPath))
            {
                Directory.CreateDirectory(dictioneryPath);
              
            }

            string batScript = $@"
                                   @echo off
                                     chcp 65001 > nul
                                   setlocal enabledelayedexpansion

                                   rem 设置变量
                                   set ""url={str}""
                                   set ""output_file={filestrPDF}""
                                   
                                   rem 启动 Chrome 以打开网址并保存为 PDF
                                   ""C:\Program Files\Google\Chrome\Application\chrome.exe"" --headless --disable-gpu --print-to-pdf=""!output_file!"" --virtual-time-budget=180000 -- ""!url!""
                                  timeout /t 120
                                   endlocal
                                         ";




            // 将批处理脚本写入文件
            System.IO.File.WriteAllText(batFileName, batScript);


            // Thread.Sleep(30000);
            //// 创建一个 Process 对象来执行批处理文件
            //Process process = new Process();
            //process.StartInfo.FileName = batFileName;
            //process.StartInfo.UseShellExecute = false;
            //process.StartInfo.RedirectStandardOutput = true;

            //// 启动批处理文件
            //process.Start();

            // 等待批处理文件执行完毕
            //process.WaitForExit();
            // 删除生成的批处理文件
            //System.IO.File.Delete(batFileName);
            return new JsonResult()
            {

                Data = new
                {
                    dcount = CLASSCOUNTSYU,
                    resault = true
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };



        }

        public async Task<bool> logFileSize(ZZZI09ExportResultViewViewModel model) {
            bool Returnstatus = false;
            List<HRMT01> HRMT01Temps = new List<HRMT01>();
           Dictionary<string, string> FiLName = new Dictionary<string, string>();
           
            string SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == model.School_No).Select(x => x.SCHOOL_NAME).FirstOrDefault();

            var NameUSERList = db.HRMT01.Where(x => x.CLASS_NO == model.Class_NO && x.SCHOOL_NO == model.School_No && x.USER_STATUS == UserStaus.Enabled)
                .Select(x => new
                {

                    filname = SCHOOLNAME + x.NAME + "的國小回憶.pdf",
                    x.USER_NO
                }).ToList();
            user = UserProfileHelper.Get();
            string dictioneryPath = "";
           // dictioneryPath = @"D:\BATTemp\" + user.USER_NO + @"\";
            int ISExcuteBat = 0;
            dictioneryPath = System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + model.Class_NO + @"\");
            if (Directory.Exists(dictioneryPath))
            {
                // Get all files in the directory and its subdirectories
                string[] files = Directory.GetFiles(dictioneryPath, "*.*", SearchOption.AllDirectories);
   
                // Iterate over each file and display its size
                foreach (string file in files)
                {
                    FileInfo fileInfo = new FileInfo(file);
                    if (fileInfo.Length <= 2048000) {
                        ISExcuteBat = 1;
                        string USER_NOstr = "";
                     USER_NOstr = NameUSERList.Where(x => x.filname == fileInfo.Name).Select(x=>x.USER_NO).FirstOrDefault();
                        string strtest = "https://ecc.tp.edu.tw:443/";
                        string str =
                      strtest + $@"/EcoolWeb/ZZZI091/ExportResultView3?School_No=" + model.School_No + "&USER_NO=" + USER_NOstr + "&S_DATE=&E_DATE=&IDNO=&COVERJPG=" + model.COVERJPG + "&UploadCoverFileName=" + model.UploadCoverFileName + "&redirect=";

                        string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + model.Class_NO + @"\" + fileInfo.Name);
                        if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\")))
                        {
                            if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\")))
                            {
                                if (System.IO.File.Exists(filestrPDF))
                                {


                                    System.IO.File.Delete(filestrPDF);
                                }

                            }
                            else
                            {
                                Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + model.Class_NO + @"\"));

                            }
                        }
                        else
                        {
                            Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\"));
                            Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath(@"~\Content" + @"\" + model.School_No + @"\" + model.Class_NO + @"\"));

                        }


                        string batScript = $@"
                                   @echo off
                                     chcp 65001 > nul
                                   setlocal enabledelayedexpansion

                                   rem 设置变量
                                   set ""url={str}""
                                   set ""output_file={filestrPDF}""
                                   
                                   rem 启动 Chrome 以打开网址并保存为 PDF
                                   ""C:\Program Files\Google\Chrome\Application\chrome.exe"" --headless --disable-gpu --print-to-pdf=""!output_file!"" --virtual-time-budget=180000  -- ""!url!""
                                  timeout /t 120
                                   endlocal";
                        string FilnAME = "my_script" + model.School_No + model.User_No + ".bat";
                        //string FilnAME = "my_script.bat";
                        DateTime now = DateTime.Now;

                        // Use the ticks (a unique number representing the current date and time) as the seed for the random number generator
                        int seed = now.Millisecond; // Using Millisecond for better uniqueness, but you can use other components too

                        // Initialize the random number generator with the seed
                        Random rand = new Random(seed);

                        // Generate a random string containing numbers and English letters
                        // Generate an 8-character random string
            
                  
                
                   
                        string batFileName = @"D:\BATTemp\" + user.USER_NO + @"\" + FilnAME; // 设置要创建的批处理文件的文件名

                        // 将批处理脚本写入文件
                        System.IO.File.WriteAllText(batFileName, batScript);
                    }
               
                    //Console.WriteLine($"File: {fileInfo.FullName}");
                    //Console.WriteLine($"Size: {fileInfo.Length} bytes");
                    //Console.WriteLine();
                }
            }
            Returnstatus = true;
            return Returnstatus;
        }
        public void ExcuBat() {
            user = UserProfileHelper.Get();
            string dictioneryPath = "";
            dictioneryPath = @"D:\BATTemp\" + user.USER_NO + @"\";
            string batScript1 = $@"@echo off
:: Scan the D: drive for .bat files and execute them in order
echo Scanning D: drive for batch files...

for /r {dictioneryPath} %%f in (*.bat) do (
   echo Sleeping for 1 minute...



    echo Found %%f
    echo Executing %%f...
    call ""%%f""
    echo Finished executing %%f
    echo Sleeping for 1 minute...
  
    
    echo Deleting %%f...
    del ""%%f""
    echo Deleted %%f
   echo Sleeping for 1 minute...
   

    echo.
)

echo A1 ";

            string batFileName1 = @"D:\"+ user.USER_NO+"A.bat";
            // 将批处理脚本写入文件
            System.IO.File.WriteAllText(batFileName1, batScript1);

            //// 调用批处理文件
            System.Diagnostics.Process.Start(batFileName1);

            //return new JsonResult()
            //{

            //    Data = new
            //    {
                 
            //        resault = true
            //    },
            //    JsonRequestBehavior = JsonRequestBehavior.AllowGet
            //};


        }
        static string GenerateRandomString(Random rand, int length)
        {
            const string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"; // Define the characters to be used
            char[] buffer = new char[length];
            for (int i = 0; i < length; i++)
            {
                buffer[i] = chars[rand.Next(chars.Length)]; // Select a random character from the defined set
            }
            return new string(buffer);
        }
        public ActionResult DeleteZip(string SCHOOL_NO, string USER_NO)

        {

            HRMT01 hrmt = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.USER_NO == USER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();

            string Class_NO = "";
            if (hrmt != null)
            {

                Class_NO = hrmt.CLASS_NO;
            }
            List<HRMT01> hrmttemp = new List<HRMT01>();
            hrmttemp = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.CLASS_NO == Class_NO && x.USER_STATUS == UserStaus.Enabled).ToList();
            string realdicnewpath = "";

            string strPATH = "";
            string dicnewZippath = "";
            string realdicnewZippath = "";
            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            string RealUploadImageRoot = Request.MapPath(UploadImageRoot);
            realdicnewpath = RealUploadImageRoot + $@"{SCHOOL_NO}\{Class_NO}";
            realdicnewZippath = RealUploadImageRoot;
            // string RealUploadImageRoot = Request.MapPath(UploadImageRoot);
            string startPath = realdicnewpath;
            dicnewZippath = UploadImageRoot + $@"{SCHOOL_NO}\{Class_NO}";
            strPATH = Guid.NewGuid().ToString("N") + ".zip";
            string strTMPFile = UploadImageRoot + @"\" + strPATH;
            string zipPath = realdicnewZippath + @"\" + SCHOOL_NO+ @"\"+ Class_NO;

            foreach (string d in Directory.GetFileSystemEntries(zipPath))
            {
                if (System.IO.File.Exists(d))
                {
                    FileInfo fi = new FileInfo(d);
                    if (fi.Attributes.ToString().IndexOf("ReadOnly") != -1)
                        fi.Attributes = FileAttributes.Normal;
                    System.IO.File.Delete(d);//直接删除其中的文件   
                }
                
                 
            }
           
            Directory.Delete(zipPath);//删除已空文件夹   
            return RedirectToAction("Index2");
        }
        //壓縮資料夾並跳出給使用者下載
        public async Task<ActionResult> ExportPdfZip(string SCHOOL_NO,string USER_NO) {
            bool returnResual = false;
            ZZZI09ExportResultViewViewModel model = new ZZZI09ExportResultViewViewModel();
            HRMT01 hrmt = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.USER_NO == USER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            model.Class_NO = hrmt.CLASS_NO;
            model.School_No = hrmt.SCHOOL_NO;
            returnResual =await logFileSize(model);
            if (returnResual)
            {



                string Class_NO = "";
                if (hrmt != null)
                {

                    Class_NO = hrmt.CLASS_NO;
                }
                List<HRMT01> hrmttemp = new List<HRMT01>();
                hrmttemp = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.CLASS_NO == Class_NO && x.USER_STATUS == UserStaus.Enabled).ToList();
                string realdicnewpath = "";

                string strPATH = "";
                string dicnewZippath = "";
                string realdicnewZippath = "";
                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);
                realdicnewpath = RealUploadImageRoot + $@"{SCHOOL_NO}\{Class_NO}";
                realdicnewZippath = RealUploadImageRoot;
                // string RealUploadImageRoot = Request.MapPath(UploadImageRoot);
                string startPath = realdicnewpath;
                dicnewZippath = UploadImageRoot + $@"{SCHOOL_NO}\{Class_NO}";
                strPATH = Guid.NewGuid().ToString("N") + ".zip";
                string strTMPFile = UploadImageRoot + @"\" + strPATH;
                string zipPath = realdicnewZippath + @"\" + strPATH;
                foreach (var item in hrmttemp)
                {
                    string FilnAME = "my_script" + item.SCHOOL_NO + item.USER_NO + ".bat";
                    //string FilnAME = "my_script.bat";
                    string batFileName = @"D:\" + FilnAME; // 设置要创建的批处理文件的文件名

                    System.IO.File.Delete(batFileName);

                }

                //if (Directory.Exists(zipPath))
                //{
                //    System.IO.File.Delete(zipPath);
                //}
                System.IO.Compression.ZipFile.CreateFromDirectory(startPath, zipPath);
                var file2 = new System.IO.FileStream(
          System.Web.HttpContext.Current.Server.MapPath(strTMPFile),
           System.IO.FileMode.Open,
           System.IO.FileAccess.Read,
           System.IO.FileShare.Read,
           4096,
           System.IO.FileOptions.DeleteOnClose);
                return File(file2, "application/zip", Class_NO + ".zip");
            }
            else {
                // 返回相应的结果或处理错误
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest, "Failed to log file size");

            }
        }
        public ActionResult ConvertoPDF(ZZZI09ExportResultViewViewModel model) {


            string url = "http://127.0.0.1/Ecool//ZZZI091/ExportResultView3?School_No=111111&USER_NO=107038";
            string pdf_page_size = "A4";
            SelectPdf.PdfPageSize pageSize = (SelectPdf.PdfPageSize)Enum.Parse(typeof(SelectPdf.PdfPageSize),
                pdf_page_size, true);
            string pdf_orientation = "Portrait";    // or     Landscape
            SelectPdf.PdfPageOrientation pdfOrientation =
               (SelectPdf.PdfPageOrientation)Enum.Parse(typeof(SelectPdf.PdfPageOrientation),
               pdf_orientation, true);

            int webPageWidth = 1024;
            int webPageHeight = 0;

            // instantiate a html to pdf converter object
            HtmlToPdf converter = new HtmlToPdf();

            // set converter options
            converter.Options.PdfPageSize = pageSize;
            converter.Options.PdfPageOrientation = pdfOrientation;
            converter.Options.WebPageWidth = webPageWidth;
            converter.Options.WebPageHeight = webPageHeight;

            // create a new pdf document converting an url
            SelectPdf.PdfDocument doc = new SelectPdf.PdfDocument();
            Thread thread = new Thread(() =>
            {
               doc = converter.ConvertUrl(url);
            });
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
            thread.Join();
            string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\20230916.pdf");
            // save pdf document
            doc.Save(filestrPDF);

            // close pdf document
            doc.Close();
            System.Diagnostics.Process.Start(filestrPDF);
            return Content("Done");
        }

        public ActionResult GenerSpirPDF(ZZZI09ExportResultViewViewModel model)
        {

            //string url = "http://127.0.0.1/Ecool//ZZZI091/ExportResultView3?School_No=111111&USER_NO=108";
            Spire.Pdf.PdfDocument doc = new Spire.Pdf.PdfDocument();

            String url = "http://www.hinet.net";
            Thread thread = new Thread(() =>
            {
                doc.LoadFromHTML(url, false, true, true);
            });
           
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
            thread.Join();
            string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\20230916.pdf");
            //Save pdf file.
            doc.SaveToFile(filestrPDF);
            doc.Close();
            //Launching the Pdf file.
            
            // save pdf document
          
            System.Diagnostics.Process.Start(filestrPDF);
            return Content("Done");
        }
        public ActionResult DownlodHTM(ZZZI09ExportResultViewViewModel model) {
            this.Shared();
            EcoolWeb.Service.HTMLtoPDF htmlService = new Service.HTMLtoPDF();
            string str=
            UrlCustomHelper.GetOwnWebUri() + $@"/ZZZI091/ExportResultView3?School_No="+model.School_No+"&USER_NO="+model.User_No+ "&S_DATE=&E_DATE=&IDNO=&COVERJPG=coverA.jpg&UploadCoverFileName=&redirect=s&ReadYN=false&&LogimUser=Y";
           
            string filestr =
          System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\");
           // string filestr1 =
           //System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\" + file);
            string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\" + model.User_No + ".pdf");
            if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + model.School_No + @"\" + model.Class_NO + @"\")))
            {
                if (System.IO.File.Exists(filestrPDF))
                {


                    System.IO.File.Delete(filestrPDF);
                }

            }

            htmlService.AppetizerAsync(str, filestrPDF).Wait();

            //UrlToFile(str, model.User_No + ".html", model.User_No + ".pdf", model.School_No,model.Class_NO);
            return new JsonResult()
            {

                Data = new
                {

                    resault = true
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };

        }
      
      public ActionResult ExportResultView3(ZZZI09ExportResultViewViewModel model)
        {
            

            string Message = string.Empty;
            ViewBag.LogimUser = "Y";
            if (ViewBag.LogimUser != "Y") {
                this.Shared();

            }
            // string classNO = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.USER_NO == model.User_No && x.USER_STATUS == UserStaus.Enabled).Select(x => x.CLASS_NO).FirstOrDefault();

            //if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            //{
            //    return RedirectToAction("NotSeeDataError", "Error", new { error = Message });
            //}

            //if (UserProfileHelper.CheckSeeStudentIDNOData(user, model.School_No, model.User_No, model.IDNO, ref db) == false)
            //{
            //    return RedirectToAction("NotSeeDataError", "Error", new { error = "您無權查看此學生資料，必需是「管理者」或「班導」或「本人」才有權查看" });
            //}
            //if(string.IsNullOrEmpty(model.SName) == false)
            if (string.IsNullOrEmpty(model.User_No) == false)
            {
                HRMT01 st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.USER_NO == model.User_No).FirstOrDefault();
                if (model.Where_SCHOOLNO_FORADMIN != null && model.School_No != model.Where_SCHOOLNO_FORADMIN && string.IsNullOrEmpty(model.IDNO) == true)
                {
                    HRMT01 st1 = db.HRMT01.Where(a => a.SCHOOL_NO == model.Where_SCHOOLNO_FORADMIN && a.USER_NO == model.User_No).FirstOrDefault();
                    st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == st1.IDNO).FirstOrDefault();
                }
                if (model.Where_SCHOOLNO_FORADMIN != null && model.School_No != model.Where_SCHOOLNO_FORADMIN && string.IsNullOrEmpty(model.IDNO) == false)
                {
                    HRMT01 st1 = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO).FirstOrDefault();
                    model.User_No = st1.USER_NO;
                    model.Class_NO = st1.CLASS_NO;

                    st = st1;
                }
                if (st != null)
                {
                    model.SName = st.SNAME;
                    model.IDNO = st.IDNO;

                    string SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == st.SCHOOL_NO).Select(x => x.SCHOOL_NAME).FirstOrDefault();
                    //ViewBag.Panel_Title = st.CLASS_NO + "班-" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";
                    ViewBag.Panel_Title = SCHOOLNAME + st.NAME + "的國小回憶";
                    ViewBag.Title = ViewBag.Panel_Title;
                }
                string classNO = st.CLASS_NO;
                model.Class_NO = classNO;
            }
            ZZZI34Service zZZI34 = new ZZZI34Service();
            if (model.COVERJPG == "coverF.jpg" && model.redirect != "s")
            {
                string FileName = "";
                FileName = zZZI34.UpLoadFilesetImg(model.School_No, model.User_No, model.UploadCoverFile, ref Message);
                string imgpath = zZZI34.GetDirectorySysArtGalleryimgPath(model.School_No, model.User_No, FileName);
                model.COVERJPGFileName = imgpath;
            }
            if (model.COVERJPG == "coverF.jpg" && model.redirect == "s")
            {
                string FileName = "";
                FileName = model.UploadCoverFileName;
                string imgpath = zZZI34.GetDirectorySysArtGalleryimgPath(model.School_No, model.User_No, FileName);
                model.COVERJPGFileName = imgpath;
            }
            else
            {
                string imgpath = zZZI34.GetSetArtimgpGalleryPath(model.School_No, model.User_No, model.COVERJPG); ;
                model.COVERJPGFileName = imgpath;
            }

            return View(model);
        }
        public ActionResult ExportResultView(ZZZI09ExportResultViewViewModel model)
        {
            this.Shared();
            
            string Message = string.Empty;
         
            // string classNO = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.USER_NO == model.User_No && x.USER_STATUS == UserStaus.Enabled).Select(x => x.CLASS_NO).FirstOrDefault();

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = Message });
            }

            if (UserProfileHelper.CheckSeeStudentIDNOData(user, model.School_No, model.User_No,model.IDNO, ref db) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = "您無權查看此學生資料，必需是「管理者」或「班導」或「本人」才有權查看" });
            }
            //if(string.IsNullOrEmpty(model.SName) == false)
            if (string.IsNullOrEmpty(model.User_No) == false)
            {
               

                HRMT01 st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.USER_NO == model.User_No ).FirstOrDefault();

                Regex rx = new Regex(@"_");

                if (model.School_No!=null&& rx.Matches(model.School_No).Count > 0)
                {
                    string[] strSchool = new string[2];
                    strSchool = model.School_No.Split('_');
                    model.School_No = strSchool[0];
                    model.IDNO = strSchool[1];
                   st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO).FirstOrDefault();

                    model.User_No = st.USER_NO; 

                }

                if (model.Where_SCHOOLNO_FORADMIN != null && model.School_No != model.Where_SCHOOLNO_FORADMIN && string.IsNullOrEmpty(model.IDNO) == true) {
                    HRMT01 st1 = db.HRMT01.Where(a => a.SCHOOL_NO ==model.Where_SCHOOLNO_FORADMIN && a.USER_NO == model.User_No).FirstOrDefault();
                    if ((model.School_No == null|| model.School_No == "undefined") && model.Where_SCHOOLNO_FORADMIN != null ) {
                        st = db.HRMT01.Where(a => a.SCHOOL_NO == model.Where_SCHOOLNO_FORADMIN && a.IDNO == st1.IDNO).FirstOrDefault();

                    }

                    if (model.School_No != null&&model.School_No != "undefined") {
                        st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == st1.IDNO).FirstOrDefault();

                    }
                }
                if (model.Where_SCHOOLNO_FORADMIN != null && model.School_No != model.Where_SCHOOLNO_FORADMIN && string.IsNullOrEmpty(model.IDNO) == false) {
                    HRMT01 st1 = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO).FirstOrDefault();
                    model.User_No = st1.USER_NO;
                    model.Class_NO = st1.CLASS_NO;
                  
                    st = st1;
                }
                if (st != null)
                {
                    model.SName = st.SNAME;
                    model.IDNO = st.IDNO;
                    string SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == st.SCHOOL_NO).Select(x => x.SCHOOL_NAME).FirstOrDefault();
                    //ViewBag.Panel_Title = st.CLASS_NO + "班 -" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";

                    ViewBag.Panel_Title = SCHOOLNAME+st.NAME + "的國小回憶";
                    ViewBag.Title = ViewBag.Panel_Title;
                }
                string classNO = st.CLASS_NO;
                model.Class_NO = classNO;
            }
            ZZZI34Service zZZI34 = new ZZZI34Service();
            if (model.COVERJPG == "coverF.jpg" && model.redirect != "s")
            {
                string FileName = "";
                FileName = zZZI34.UpLoadFilesetImg(model.School_No, model.User_No, model.UploadCoverFile, ref Message);
                string imgpath = zZZI34.GetDirectorySysArtGalleryimgPath(model.School_No, model.User_No, FileName);
                model.COVERJPGFileName = imgpath;
            }
            if (model.COVERJPG == "coverF.jpg" && model.redirect == "s")
            {
                string FileName = "";
                FileName = model.UploadCoverFileName;
                string imgpath = zZZI34.GetDirectorySysArtGalleryimgPath(model.School_No, model.User_No, FileName);
                model.COVERJPGFileName = imgpath;
            }
            else
            {
                string imgpath = zZZI34.GetSetArtimgpGalleryPath(model.School_No, model.User_No, model.COVERJPG); ;
                model.COVERJPGFileName = imgpath;
            }

            return View(model);
        }
        public ActionResult ExportResultView2(ZZZI09ExportResultViewViewModel model)
        {
            this.Shared();

            string Message = string.Empty;
            model.School_No = "403605";
            model.User_No= "104001";
            model.COVERJPG = "coverF.jpg";
            model.redirect = "s";
            // string classNO = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.USER_NO == model.User_No && x.USER_STATUS == UserStaus.Enabled).Select(x => x.CLASS_NO).FirstOrDefault();

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = Message });
            }

            if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = "您無權查看此學生資料，必需是「管理者」或「班導」或「本人」才有權查看" });
            }

            if (string.IsNullOrEmpty(model.User_No) == false)
            {
                HRMT01 st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.USER_NO == model.User_No).FirstOrDefault();
                if (st != null)
                {
                    model.SName = st.SNAME;
                    string SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == st.SCHOOL_NO).Select(x => x.SCHOOL_NAME).FirstOrDefault();
                    //ViewBag.Panel_Title = st.CLASS_NO + "班-" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";
                    ViewBag.Panel_Title = SCHOOLNAME+st.NAME + "的國小回憶";
                    ViewBag.Title = ViewBag.Panel_Title;
                }
                string classNO = st.CLASS_NO;
                model.Class_NO = classNO;
            }
            ZZZI34Service zZZI34 = new ZZZI34Service();
            if (model.COVERJPG == "coverF.jpg" && model.redirect != "s")
            {
                string FileName = "";
                FileName = zZZI34.UpLoadFilesetImg(model.School_No, model.User_No, model.UploadCoverFile, ref Message);
                string imgpath = zZZI34.GetDirectorySysArtGalleryimgPath(model.School_No, model.User_No, FileName);
                model.COVERJPGFileName = imgpath;
            }
            if (model.COVERJPG == "coverF.jpg" && model.redirect == "s")
            {
                string FileName = "";
                FileName = model.UploadCoverFileName;
                string imgpath = zZZI34.GetDirectorySysArtGalleryimgPath(model.School_No, model.User_No, FileName);
                model.COVERJPGFileName = imgpath;
            }
            else
            {
                string imgpath = zZZI34.GetSetArtimgpGalleryPath(model.School_No, model.User_No, model.COVERJPG); ;
                model.COVERJPGFileName = imgpath;
            }

            return View(model);
        }
        public ActionResult ShowSportsView(string WhereSCHOOL_NO, string WhereUSER_NO,string LogimUser)
        {
        
            string Message = string.Empty;
            if (LogimUser != "Y") {

                this.Shared();
                if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }
            }
            SECI06Service sECI06Service = new SECI06Service();

            SECI06IndexViewModel model = new SECI06IndexViewModel();
            model.WhereSCHOOL_NO = WhereSCHOOL_NO;
            model.WhereUSER_NO = WhereUSER_NO;
            HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == WhereSCHOOL_NO && a.USER_NO == WhereUSER_NO).FirstOrDefault();
            HRMT01 thisTeacher = db.HRMT01.Where(a => a.SCHOOL_NO == WhereSCHOOL_NO && a.CLASS_NO == thisUser.CLASS_NO && a.USER_TYPE == "T").FirstOrDefault();
            SECI01Service Service = new SECI01Service();
            model = sECI06Service.GetHealthData(model, user, ref db);
            int SyearsNow = 0;
            SyearsNow = (int)thisUser.SYEAR + (int)thisUser.GRADE - 1;
            string sSQL1 = @"SELECT [SEYEAR],[SESEM] ,[CLASSID],[CLASSNAME] ,[TEAID] ,[SCHOOL_NO] FROM [DB2_BASCLS] where SEYEAR=@SEYEAR and SCHOOL_NO=@SCHOOL_NO and CLASSID =@CLASSID";
            var QTemp1 = db.Database.Connection.Query<DB2_BASCLS>(sSQL1
                  , new
                  {
                      SEYEAR = SyearsNow,
                      SCHOOL_NO = WhereSCHOOL_NO,
                      CLASSID = thisUser.CLASS_NO
                  });
            if (QTemp1 != null)
            {

                int TeaID = 0;
                TeaID = QTemp1.Select(x => x.TEAID).FirstOrDefault();
                string sSQL2 = @" SELECT [TEANAME] FROM DB2_TEABAS where TEAID=@TEAID and SCHOOL_NO=@SCHOOL_NO ";
                var QTemp2 = db.Database.Connection.Query<string>(sSQL2
                 , new
                 {
                     TEAID = TeaID,
                     SCHOOL_NO = WhereSCHOOL_NO

                 });
                model.TeacherSTR = QTemp2.FirstOrDefault();
            }
            if (!string.IsNullOrWhiteSpace(thisUser.PHOTO))
            {
                model.PlayerUrl = Service.GetDirectorySysMyPhotoPath(thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.PHOTO);
            }
            else
            {
                model.PlayerUrl = GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);
            }
          
            model.SYEARSTR = (thisUser.SYEAR).ToString() + "~" + (thisUser.SYEAR - (thisUser.GRADE - 1)).ToString() + "/06";
            SECI03Controller sECI03Controller = new SECI03Controller();
            model.TALLchart = sECI03Controller.GetTALLchart(model.Hrmt08List, 600, 3000, "TALLchart");
            model.WEIGHTchart = sECI03Controller.GetWEIGHTchart(model.Hrmt08List, 600, 3000, "WEIGHTchart");

            return PartialView(model);
        }
        /// <summary>
        /// 角色娃娃路徑
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static string GetPlayerUrl(ref ECOOL_DEVEntities db, string SCHOOL_NO, string USER_NO, string SEX, string USER_TYPE)
        {
            if (USER_TYPE == UserType.Parents)
            {
                return "~/Content/img/web-parent.png";
            }
            //角色娃娃
            List<AWAT07> MyPlayerList =
                db.AWAT07.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).ToList();
            if (MyPlayerList.Any())
            {
                AWAT07 MyPlayer = MyPlayerList.Where(a => a.DEFAULT_YN == true).FirstOrDefault();
                if (MyPlayer == null) MyPlayer = MyPlayerList.First();

                string ImageUrl = @"~/Content/Players/";
                return ImageUrl + db.AWAT06.Find(MyPlayer.PLAYER_NO).IMG_FILE;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(SEX))
                {
                    HRMT01 tHRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

                    if (tHRMT01 != null)
                    {
                        SEX = tHRMT01.SEX;
                    }
                }

                if (SEX == "1") //男預設值
                {
                    return "~/Content/img/web-student-allpage-33plus.png";
                }
                else            //女預設值
                {
                    return "~/Content/img/web-student_allpage-17.png";
                }
            }
        }
        public ActionResult ShowADDT14View(ZZZI09ShowADDT14ViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowADDT14ViewViewModel();

            string Message = string.Empty;
            if (model.LogimUser != "Y")
            {
                if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
                {
                    TempData["StatusMessage"] = Message;
                    return PartialView();
                }
            }
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var Temp =
                        from a14 in db.ADDT14
                        join h01 in db.HRMT01 on new { a14.USER_NO, a14.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                        from h01 in h01_join.DefaultIfEmpty()
                        where a14.SCHOOL_NO == model.School_No && a14.APPLY_STATUS != "9" &&a14.IAWARD_KIND!= "小獎勵" && a14.IAWARD_KIND!= "小獎勵(學校加點，不受點數控管)" && a14.IAWARD_KIND != "小獎勵(班級加點，受點數控管)"
                        //&& h01.USER_STATUS != UserStaus.Disable && h01.USER_STATUS != UserStaus.Invalid
                        orderby a14.CREATEDATE descending
                        select a14;

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };
                    if (model.LogimUser != "Y")
                    {
                        string IDNOSTR = "";
                        IDNOSTR = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
                        if (model.IDNO == IDNOSTR)
                        {
                            if (UserProfileHelper.CheckSeeStudentIDNOData(user, model.School_No, model.User_No, model.IDNO, ref db) == false)
                            {
                                TempData["StatusMessage"] = "校內表現 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                                return PartialView(model);
                            }

                        }
                        else
                        {
                            if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
                            {
                                TempData["StatusMessage"] = "校內表現 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                                return PartialView(model);
                            }

                        }
                    }
                       
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE <= dateFromE_DATE);
                }

                model.ADDT14List = (Temp.ToList() ?? new List<ADDT14>());

                //組圖檔系統目錄路徑
                ViewBag.ImageUrl = new FileHelper().GetDirectorySysControllersFile(model.School_No, EcoolWeb.Controllers.ADDI06Controller.ImgPath);

                return PartialView(model);
            }
        }

        public ActionResult ShowADDT15View(ZZZI09ShowADDT15ViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowADDT15ViewViewModel();

            string Message = string.Empty;
            string IDNOSTR = "";
            if (model.LogimUser != "Y") { 
     
            IDNOSTR = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
            if (model.IDNO == IDNOSTR)
            {

                if (UserProfileHelper.CheckSeeStudentIDNOData(user, model.School_No, model.User_No, model.IDNO, ref db) == false)
                {
                    return RedirectToAction("NotSeeDataError", "Error");
                }

            }
            else {

                if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
                {
                    TempData["StatusMessage"] = Message;
                    return PartialView();
                }
            }

            }
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var Temp =
                     from a15 in db.ADDT15
                     join h01 in db.HRMT01 on new { a15.USER_NO, a15.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                     from h01 in h01_join.DefaultIfEmpty()
                     where a15.SCHOOL_NO == model.School_No && a15.APPLY_STATUS != "9"
                     //&& h01.USER_STATUS != UserStaus.Disable && h01.USER_STATUS != UserStaus.Invalid
                     orderby a15.CREATEDATE descending
                     select a15;

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };
                    if (model.LogimUser != "Y")
                    {
                        if (model.IDNO == IDNOSTR)
                        {

                            if (UserProfileHelper.CheckSeeStudentIDNOData(user, model.School_No, model.User_No, model.IDNO, ref db) == false)
                            {
                                return RedirectToAction("NotSeeDataError", "Error");
                            }

                        }
                        else
                        {

                            if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
                            {
                                TempData["StatusMessage"] = "校外榮譽 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                                return PartialView(model);
                            }
                        }
                    }
                   
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE <= dateFromE_DATE);
                }

                model.ADDT15List = (Temp.OrderByDescending(x => x.CREATEDATE).ToList() ?? new List<ADDT15>());

                //組圖檔系統目錄路徑
                ViewBag.ImageUrl = new FileHelper().GetDirectorySysControllersFile(model.School_No, EcoolWeb.Controllers.ADDI07Controller.ImgPath);

                return PartialView(model);
            }
        }

        public ActionResult ShowAWAT01_LOGView(ZZZI09ShowAWAT01_LOGViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowAWAT01_LOGViewViewModel();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var Temp = (from aw01_log in db.AWAT01_LOG
                            join h01 in db.HRMT01 on new { aw01_log.SCHOOL_NO, aw01_log.USER_NO } equals new { h01.SCHOOL_NO, h01.USER_NO }
                            join P_h01 in db.HRMT01 on aw01_log.LOG_PERSON equals P_h01.USER_KEY into temp
                            from ds in temp.DefaultIfEmpty()
                            join P_REF in db.AWAT01_X_REF on new { aw01_log.SOURCE_TYPE } equals new { P_REF.SOURCE_TYPE } into temp_ref
                            from ds_ref in temp_ref.DefaultIfEmpty()
                            where aw01_log.SCHOOL_NO == model.School_No
                            select new uAWAT01_Detail_LOG
                            {
                                CLASS_NO = h01.CLASS_NO,
                                SEAT_NO = h01.SEAT_NO,
                                SCHOOL_NO = aw01_log.SCHOOL_NO,
                                USER_NO = aw01_log.USER_NO,
                                USER_TYPE = h01.USER_TYPE,
                                SOURCE_TYPE = aw01_log.SOURCE_TYPE,
                                SOURCE_NO = aw01_log.SOURCE_NO,
                                CASH_IN = aw01_log.CASH_IN,
                                LOG_TIME = aw01_log.LOG_TIME,
                                LOG_DESC = aw01_log.LOG_DESC,
                                CHART_DESC = ds_ref.CHART_DESC,
                                LOG_PERSON = aw01_log.LOG_PERSON,
                                LOG_PERSON_NAME = ds.NAME ?? aw01_log.LOG_PERSON,
                                USERNAME = h01.NAME,
                                SNAME = h01.SNAME,
                                SYEAR = aw01_log.LOG_TIME.Year - 1911,
                                SEMESTER = (byte)((aw01_log.LOG_TIME.Month >= 2 && aw01_log.LOG_TIME.Month < 8) ? 2 : 1),
                                AWAT01_CASH_AVAILABLE = aw01_log.AWAT01_CASH_AVAILABLE,
                            });

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };

                    if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
                    {
                        TempData["StatusMessage"] = "數位存摺 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                        return PartialView(model);
                    }
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Temp = Temp.Where(a => a.LOG_TIME >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Temp = Temp.Where(a => a.LOG_TIME <= dateFromE_DATE);
                }

                model.AWAT01_LOG_List = (Temp.ToList() ?? new List<uAWAT01_Detail_LOG>());

                return PartialView(model);
            }
        }

        /// <summary>
        ///  借閱類別統計表
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetBorrowColumnChart(SECI05IndexViewModel model)
        {
            var returnPoint = new List<Point>();

            foreach (var item in model.BorrowTypeQty)
            {
                returnPoint.Add(new Point
                {
                    Y = item.QTY
                    ,
                    Name = item.TYPE_NAME
                });
            }

            Data data = new Data(returnPoint.ToArray());

            Highcharts TempPreColumnChart = new Highcharts("TempBorrowColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
           })
           .SetTitle(new Title { Text = "借閱類別統計表" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category
                        ,
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借閱數量" }, AllowDecimals = false })
           .SetSeries(new[]
                   {
                        new Series {
                            Data = data,
                            Name = "借閱數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
           .SetLegend(new Legend { Enabled = false });

            chartsHelper.SetCopyright(TempPreColumnChart);

            return TempPreColumnChart;
        }

        public ActionResult _SEI05Partial(SECI05IndexViewModel model)
        {
            string Bre_NO = "ZZZI09";
            SECI05Service Service = new SECI05Service();
            ViewBag.BRE_NO = Bre_NO;
            //  ViewBag.Panel_Title = "學生閱讀狀況";
            //  this.Shared(ViewBag.Panel_Title);

            user = UserProfileHelper.Get();
            List<string> MyPanyStudent = new List<string>();

            //if (user == null)
            //{
            //    return RedirectToAction("SessionTimeOutError", "Error");
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            //{
            //    model.WhereSCHOOL_NO = user.SCHOOL_NO;
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO ?? user.TEACH_CLASS_NO;
            //}

            //if (user.USER_TYPE == UserType.Student)
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO;
            //    model.WhereUSER_NO = user.USER_NO;
            //}
            //else if (user.USER_TYPE == UserType.Parents)
            //{
            //    var Hr06 = HRMT06.GetMyPanyStudent(user, db);

            //    if (Hr06 != null)
            //    {
            //        if (Hr06.Count() > 0)
            //        {
            //            MyPanyStudent = Hr06.Select(a => a.STUDENT_USER_NO).ToList();
            //        }
            //    }

            //    if (MyPanyStudent.Count >= 1 && string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            //    {
            //        model.WhereUSER_NO = MyPanyStudent.FirstOrDefault();
            //    }
            //}

            //List<SelectListItem> SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.WhereSCHOOL_NO, null, user, true, null, null, false);

            //if (!HRMT24_ENUM.CheckQQutSchool(user))
            //{
            //    SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == model.WhereSCHOOL_NO).ToList();
            //}
            //ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            //var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            //if (!HRMT24_ENUM.CheckQAdmin(user))
            //{
            //    if (user.TEACH_CLASS_NO == null)
            //    {
            //        if (HRMT24_ENUM.IsOtherTeacher(user) == false)
            //        {
            //            ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //        }
            //    }
            //    else
            //    {
            //        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //    }
            //}
            //ViewBag.ClassItems = ClassItems;

            //List<SelectListItem> USER_NOItems = null;

            //if (user.USER_TYPE == UserType.Parents)
            //{
            //    USER_NOItems = HRMT01.GetUserNoListDataForP(model.WhereUSER_NO, user, db).ToListNoLock();
            //}
            //else
            //{
            //    USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db, true);
            //}

            //ViewBag.USER_NOItems = USER_NOItems;

            if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                model = Service.GetBorrowData(model, user, ref db);

                if (model.MyBorrow != null)
                {
                    model.BorrowColumnChart = GetBorrowColumnChart(model);
                    model.GradeQtyCharts = GetGradeQtyCharts(model.MyBorrow);
                }

                List<SelectListItem> GradeSeyearItem = new List<SelectListItem>();

                int InSchoolYear = Convert.ToInt32(model.WhereUSER_NO.Substring(0, 3));

                GradeSeyearItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSEYEAR) });

                foreach (var Item in Enum.GetValues(typeof(HRMT01.GradeVal)))
                {
                    GradeSeyearItem.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = (InSchoolYear + ((byte)Item - 1)).ToString(), Selected = (InSchoolYear + ((byte)Item - 1)).ToString() == model.WhereSEYEAR });
                }

                ViewBag.GradeSeyearItem = GradeSeyearItem;
            }

            return View(model);
        }

        /// <summary>
        /// 取得年級名稱
        /// </summary>
        /// <param name="grade"></param>
        /// <returns></returns>
        static public string ParserGrade(byte? grade)
        {
            if (grade.HasValue == false) return string.Empty;

            switch (grade.Value)
            {
                case (byte)HRMT01.GradeVal.In1Grade:
                    return "一年級";

                case (byte)HRMT01.GradeVal.In2Grade:
                    return "二年級";

                case (byte)HRMT01.GradeVal.In3Grade:
                    return "三年級";

                case (byte)HRMT01.GradeVal.In4Grade:
                    return "四年級";

                case (byte)HRMT01.GradeVal.In5Grade:
                    return "五年級";

                case (byte)HRMT01.GradeVal.In6Grade:
                    return "六年級";
            }

            return grade.Value.ToString();
        }

        /// <summary>
        /// 產生 個人借閱數量趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetGradeQtyCharts(SECI05MyBorrowViewModel Data)
        {
            if (Data == null)
            {
                return null;
            }

            List<int> GradeQtys = new List<int>();
            GradeQtys.Add(Data.QTY_GRADE_1);
            GradeQtys.Add(Data.QTY_GRADE_2);
            GradeQtys.Add(Data.QTY_GRADE_3);
            GradeQtys.Add(Data.QTY_GRADE_4);
            GradeQtys.Add(Data.QTY_GRADE_5);
            GradeQtys.Add(Data.QTY_GRADE_6);

            List<string> Grades = new List<string>();
            foreach (var Item in Enum.GetValues(typeof(HRMT01.GradeVal)))
            {
                Grades.Add(ParserGrade((byte)Item));
            }

            Highcharts GradeQtyChart = new Highcharts("GradeQtyChart");

            GradeQtyChart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = "個人借閱數量趨示圖", X = 0 })
            .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = Grades.ToArray() })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "本" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "本" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 {
                                     Name ="各年級借閱數量",
                                   Data = new DotNet.Highcharts.Helpers.Data( GradeQtys.Cast<object>().ToArray())
                                 }
                        }
            );

            chartsHelper.SetCopyright(GradeQtyChart);

            return GradeQtyChart;
        }

        public ActionResult ShowCashPreView(ZZZI09ShowCashPreViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowCashPreViewViewModel();

            string Message = string.Empty;
            string IDNOSTR = "";
            if (model.LogimUser != "Y") { 
            IDNOSTR = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
            if (model.IDNO == IDNOSTR)
            {

                if (UserProfileHelper.CheckSeeStudentIDNOData(user, model.School_No, model.User_No, model.IDNO, ref db) == false)
                {
                    return RedirectToAction("NotSeeDataError", "Error");
                }

            }
            else {
                if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
                {
                    TempData["StatusMessage"] = Message;
                    return PartialView();
                }

            }
            }

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var TempHRMT01 = db.HRMT01.Where(B => B.SCHOOL_NO == model.School_No
                //&& (!UserStaus.NGUserStausList.Contains(B.USER_STATUS))
                );

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };
                
                    if (model.IDNO == IDNOSTR && model.LogimUser != "Y")
                    {

                        if (UserProfileHelper.CheckSeeStudentIDNOData(user, model.School_No, model.User_No, model.IDNO, ref db) == false)
                        {
                            return RedirectToAction("NotSeeDataError", "Error");
                        }

                    }
                    else {
                        if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false && model.LogimUser != "Y")
                        {
                            TempData["StatusMessage"] = "點數統計 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                            return PartialView(model);
                        }
                    }
                    
                }

                if (ArrUSER_NO.Length > 0)
                {
                    TempHRMT01 = TempHRMT01.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                var Log = from A in db.AWAT01_LOG
                          join B in TempHRMT01 on new { A.SCHOOL_NO, A.USER_NO } equals new { B.SCHOOL_NO, B.USER_NO }
                          select A;

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Log = Log.Where(a => a.LOG_TIME >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Log = Log.Where(a => a.LOG_TIME <= dateFromE_DATE);
                }

                //各類別 酷幣點數，及比例
                model.PerData = (from A in Log
                                 join C in db.AWAT01_X_REF on new { A.SOURCE_TYPE } equals new { C.SOURCE_TYPE } into REF
                                 from X in REF.DefaultIfEmpty()
                                 group A by new
                                 {
                                     LOG_DESC = X.CHART_DESC != null ? X.CHART_DESC : A.LOG_DESC,
                                 } into g
                                 select new SECSharedCashPreViewModel
                                 {
                                     LOG_DESC = g.Key.LOG_DESC,
                                     SUM_ADD_CASH_ALL = g.Sum(a => a.ADD_CASH_ALL),
                                 }).Where(t => t.SUM_ADD_CASH_ALL > 0).OrderByDescending(t => t.SUM_ADD_CASH_ALL).ToList();

                model.CashPreColumnChart = new SECI02Controller().GetCashPreColumnChart(model.PerData);

                return PartialView(model);
            }
        }

        public ActionResult ShowHrmt08View(SECI03IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new SECI03IndexViewModel();

            string Message = string.Empty;
            if (model.LogimUser != "Y")
            {
                if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
                {
                    TempData["StatusMessage"] = Message;
                    return PartialView();
                }
            }
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (string.IsNullOrWhiteSpace(model.whereIDNO))
                {
                    model.whereIDNO = db.HRMT01.Where(a => a.SCHOOL_NO == model.whereSCHOOL_NO && a.USER_NO == model.whereUSER_NO).Select(a => a.IDNO).FirstOrDefault();
                }

                IEnumerable<SECI03Hrmt08ListViewModel> QTemp = new SECI03Service().GetMyHrmt08Data(model.whereIDNO, model.whereSCHOOL_NO, ref db);
                IEnumerable<SECI03Hrmt09ListViewModel> Hrmt09 = new SECI03Service().GetMyHrmt09Data(model.whereIDNO, ref db);

                model.Hrmt08List = QTemp.ToPagedList(0, int.MaxValue);
                model.Hrmt09List = Hrmt09.Where(x => x.IDNO == model.whereIDNO).ToPagedList(0, int.MaxValue);
                SECI03Controller sECI03Controller = new SECI03Controller();

                model.TALLchart = sECI03Controller.GetTALLchart(model.Hrmt08List, 750, 1000, "TALLchart1");
                model.WEIGHTchart = sECI03Controller.GetWEIGHTchart(model.Hrmt08List, 750, 1000, "WEIGHTchart1");
                model.printTALLchart = sECI03Controller.GetTALLchart(model.Hrmt08List, 600, 1200, "PrintTALLchart");
                model.printWEIGHTchart = sECI03Controller.GetWEIGHTchart(model.Hrmt08List, 600, 1200, "PrintWEIGHTchart");
                model.RIGHT_VISIONColumnChart = sECI03Controller.GetRIGHT_VISION_ColumnChart(model.whereIDNO);
                model.LEFT_VISIONColumnChart = sECI03Controller.GetLEFT_VISION_ColumnChart(model.whereIDNO);
                model.FitnesschartV = sECI03Controller.GetFitnesschartV(model, 750, 1000, "FitnesschartV");
                model.printFitnesschartV = sECI03Controller.GetFitnesschartV(model, 600, 1200, "printFitnesschartV");
                model.FitnesschartSL = sECI03Controller.GetFitnesschartSL(model, 750, 1000, "Fitnesschart");
                model.printFitnesschartSL = sECI03Controller.GetFitnesschartSL(model, 600, 1200, "printFitnesschartSL");
                model.FitnesschartSU = sECI03Controller.GetFitnesschartSU(model, 750, 1000, "FitnesschartSU");
                model.printFitnesschartSU = sECI03Controller.GetFitnesschartSU(model, 600, 1200, "printFitnesschartSU");
                model.FitnesschartC = sECI03Controller.GetFitnesschartC(model, 750, 1000, "FitnesschartC");
                model.prinFitnesschartC = sECI03Controller.GetFitnesschartC(model, 600, 1200, "prinFitnesschartC");
                return PartialView(model);
            }
        }
        /// <summary>
        /// 【閱讀認證成果匯出】
        /// </summary>
        /// <param name="School_No"></param>
        /// <param name="ShowOriginal"></param>
        /// <returns></returns>
        public ActionResult ADDTALLListDetails3(string School_No, string User_No, string S_DATE, string E_DATE, bool? ShowOriginal, string IDNO, string LogimUser)
        {
            user = UserProfileHelper.Get();
            ViewBag.IDNO = IDNO;
            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            List<ADDT06> liaDDT06 = new List<ADDT06>();

            if (db.ADDT06 == null)
            {
                return HttpNotFound();
            }
            else if (School_No != string.Empty && (User_No != string.Empty || IDNO != string.Empty))
            {
                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { User_No };
                    if (LogimUser != "Y")
                    {
                        string IDNOSTR = "";
                        IDNOSTR = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
                        if (IDNO == IDNOSTR)
                        {

                            if (UserProfileHelper.CheckSeeStudentIDNOData(user, School_No, User_No, IDNO, ref db) == false)
                            {
                                return RedirectToAction("NotSeeDataError", "Error");
                            }

                        }
                        else
                        {
                            if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                            {
                                return RedirectToAction("NotSeeDataError", "Error");
                            }

                        }
                    }
                }

                var Temp = db.ADDT06.Where(a => a.SCHOOL_NO == School_No && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back);

                if (string.IsNullOrWhiteSpace(User_No) == false)
                {
                    Temp = Temp.Where(a => a.USER_NO == User_No);
                }
                else
                {
                    User_No = ArrUSER_NO.FirstOrDefault();
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                    Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                    Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
                }

                liaDDT06 = Temp.ToList();

                //if (liaDDT06.Count() == 0)
                //{
                //    return View(liaDDT06);
                //}
                //組圖檔路徑
                ViewBag.ImageUrl = GetImageDictionaryNOSmallUrl(liaDDT06);
                ViewBag.ImageUrl1 = GetImageDictionaryNOSmallUrl(liaDDT06);
                //取出學校名稱
                ViewBag.SchoolName = db.BDMT01.Where(p => p.SCHOOL_NO == School_No).FirstOrDefault().SHORT_NAME;

                //取出學生名稱 及 學習期間
                ViewBag.UserName = db.HRMT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Select(p => p.NAME).FirstOrDefault();

                if (liaDDT06.Count() > 0)
                {
                    //學習期間
                    ViewBag.SYear = liaDDT06.Min(a => a.SYEAR).Value.ToString() + "學年度 ~ " + liaDDT06.Max(a => a.SYEAR).Value.ToString() + "學年度";
                }

                //取出酷幣點數
                AWAT01 UserCash = db.AWAT01.Where(u => u.SCHOOL_NO == School_No && u.USER_NO == User_No).FirstOrDefault();
                if (UserCash != null)
                {
                    ViewBag.CoolCash = UserCash.CASH_ALL.Value;
                }

                //線上投稿總篇數
                ViewBag.SumAcount = db.ADDT01.Where(a => a.SCHOOL_NO == School_No && a.USER_NO == User_No).Count();

                var T0809 = (from a09 in db.ADDT09
                             join a08 in db.ADDT08
                                   on new { a09.SCHOOL_NO, LEVEL_ID = (byte)a09.LEVEL_ID }
                               equals new { a08.SCHOOL_NO, LEVEL_ID = a08.LEVEL_ID }
                             where a09.USER_NO == User_No && a09.SCHOOL_NO == School_No
                             select new ADDT0809
                             {
                                 LEVEL_DESC = a08.LEVEL_DESC
                             }).FirstOrDefault();

                //閱讀認證等級
                ViewBag.PassPort = T0809 != null ? T0809.LEVEL_DESC : "";
            }

            #region "閱讀護照等級撈出"

            //取出該校所有的小朋友閱讀護照通過認證暫存表
            List<uADDT04> litmpADDI04 = new List<uADDT04>();
            //待回傳閱讀護照通過認證
            List<uADDT04Q02> liADDT04Q02 = new List<uADDT04Q02>();

            var temp = (from a04 in db.ADDT04
                        join h01 in db.HRMT01 on new { a04.SCHOOL_NO, a04.USER_NO } equals new { h01.SCHOOL_NO, h01.USER_NO }
                        where a04.USER_NO == User_No
                        && a04.SCHOOL_NO == School_No
                        orderby
                          a04.SCHOOL_NO,
                          h01.CLASS_NO
                        select new uADDT04
                        {
                            CLASS_NO = h01.CLASS_NO,
                            NAME = h01.NAME,
                            SONAME = (h01.SEAT_NO + h01.NAME),
                            GRADE = a04.GRADE,
                            PASS_DATE = a04.PASS_DATE,
                            USER_NO = h01.USER_NO
                        });

            litmpADDI04 = temp.ToList();

            int iStuSeat = -1;    //資料位置
            string Name = string.Empty;
            for (int i = 0; i < litmpADDI04.Count(); i++)
            {
                if (Name != litmpADDI04.ToList()[i].NAME)
                {
                    //取得該學生該年度的學習護照閱讀總筆數
                    int iADDT04Count = litmpADDI04.Where(p => p.USER_NO == litmpADDI04.ToList()[i].USER_NO).Count();

                    liADDT04Q02.Add(new uADDT04Q02
                    {
                        CLASS_NO = litmpADDI04.ToList()[i].CLASS_NO,
                        SONAME = litmpADDI04.ToList()[i].SONAME,
                        UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : "",
                        UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : "",
                        UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : "",
                        UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : "",
                        UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : "",
                        UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : "",
                        UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        FinishStatus = (iADDT04Count == 6) ? "Y" : ""
                    });
                    iStuSeat++;
                    Name = litmpADDI04.ToList()[i].NAME;
                }
                else
                {
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO06;

                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06;
                }
            }
            ViewBag.ListA04Q2 = liADDT04Q02;

            #endregion "閱讀護照等級撈出"

            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            return PartialView(liaDDT06);
        }
        public List<Image_File> GetImageDictionaryNOSmallUrl(List<ADDT06> liaDDT06)
        {
            List<Image_File> dicImage = new List<Image_File>();

            foreach (var item in liaDDT06)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                var sysPath = System.Web.HttpContext.Current.Request.MapPath(UploadImageRoot);
                string smallimageurl = "";

                string imgPath = string.Format(@"{0}ADDT06IMG\{1}\", sysPath, item.SCHOOL_NO.ToString());
                smallimageurl = GetImageUrl(item);
                if (item.IMG_FILE != null)
                {
                    if (!System.IO.File.Exists(Path.Combine(imgPath, item.IMG_FILE)))
                    {
                        smallimageurl = GetImageUrl(item);
                    }
                }

                dicImage.Add(new Image_File { APPLY_NO = item.APPLY_NO, ImageUrl = smallimageurl });
            }
            return dicImage;
        }

        public static void UrlToFile(String url, String file, String pdffile, string SCHOOL_NO,string CLASS_NO)
        {
            WebClient webclient = new WebClient();
            string filestr=
            System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + SCHOOL_NO + @"\" + CLASS_NO + @"\");
            string filestr1 =
           System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + SCHOOL_NO + @"\" + CLASS_NO + @"\" + file);
            string filestrPDF = System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + SCHOOL_NO + @"\" + CLASS_NO + @"\" + pdffile);
            if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(@"~\Content\" + @"\" + SCHOOL_NO + @"\" + CLASS_NO + @"\")))
            {
                if (System.IO.File.Exists(filestr1)) {


                    System.IO.File.Delete(filestr1);
                }

            }
            else
            {
                System.IO.Directory.CreateDirectory(filestr);
                
            }
            webclient.DownloadFile(url, filestr1);
            HtmlLoadOptions htmloptions = new HtmlLoadOptions();
            Aspose.Words.Document doc = new Aspose.Words.Document(filestr1, htmloptions);
            doc.Save(filestrPDF);
        }
        public ActionResult ShowADDT22View(ZZZI09ShowADDT22ViewViewModel model)
        {
            this.Shared();

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (model == null) model = new ZZZI09ShowADDT22ViewViewModel();

                string sSQL = @"select a.ART_GALLERY_NO ,a.ART_SUBJECT,a.WORK_TYPE,b.PHOTO_NO,b.PHOTO_FILE,b.PHOTO_SUBJECT,b.PHOTO_CLASS_NO
									 ,b.PHOTO_DESC,a.CRE_DATE,b.PHOTO_SCHOOL_NO,b.PHOTO_USER_NO
									 from  ADDT21 a (nolock)
									 join ADDT22 b (nolock) on a.ART_GALLERY_NO=b.ART_GALLERY_NO
                                     join HRMT01 c (nolock) on b.PHOTO_SCHOOL_NO=c.SCHOOL_NO and b.PHOTO_USER_NO=c.USER_NO
                                     where 1=1 and a.DEL_DATE is null ";

                if (!string.IsNullOrWhiteSpace(model.IDNO))
                {
                    sSQL = sSQL + @" and c.IDNO=@IDNO ";
                }
                else
                {
                    sSQL = sSQL + @" and b.PHOTO_SCHOOL_NO = @PHOTO_SCHOOL_NO
                                 and b.PHOTO_USER_NO = @PHOTO_USER_NO  ";
                }

                if (model.S_DATE != null)
                {
                    sSQL = sSQL + @" and CONVERT(nvarchar(10),a.CRE_DATE,111)>=@S_DATE ";
                }

                if (model.E_DATE != null)
                {
                    sSQL = sSQL + @" and CONVERT(nvarchar(10),a.CRE_DATE,111)<=@E_DATE ";
                }

                sSQL = sSQL + " order by a.CRE_DATE,b.CRE_DATE";
                model.ADDT22List = db.Database.Connection.Query<ZZZI09ShowADDT22PHOTOViewViewModel>(sSQL
               , new
               {
                   PHOTO_SCHOOL_NO = model.School_No,
                   PHOTO_USER_NO = model.User_No,
                   IDNO = model.IDNO,
                   S_DATE = model.S_DATE,
                   E_DATE = model.E_DATE,
               }).ToList();

                return PartialView(model);
            }
        }

        public ActionResult ShowADDT25_TREFView(ZZZI09ShowADDT25_TREFViewViewModel model)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (model == null) model = new ZZZI09ShowADDT25_TREFViewViewModel();

                var temp = from a in db.ADDT25_TREF
                           join b in db.HRMT01 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                           select new { Task = a, Hr = b };

                if (!string.IsNullOrWhiteSpace(model.IDNO))
                {
                    temp = temp.Where(a => a.Hr.IDNO == model.IDNO);
                }
                else
                {
                    temp = temp.Where(a => a.Hr.SCHOOL_NO == model.School_No && a.Hr.USER_NO == model.User_No);
                }

                if (model.S_DATE != null)
                {
                    var s_datetime = Convert.ToDateTime(model.S_DATE);
                    temp = temp.Where(a => a.Task.TASK_DATE >= s_datetime);
                }

                if (model.E_DATE != null)
                {
                    var e_datetime = Convert.ToDateTime(model.E_DATE);
                    temp = temp.Where(a => a.Task.TASK_DATE <= e_datetime);
                }

                model.Datalist = temp.Select(a => new ZZZI09ShowADDT25_TREFListViewModel()
                {
                    SCHOOL_NO = a.Task.SCHOOL_NO,
                    USER_NO = a.Task.USER_NO,
                    SOU_SCHOOL_NO = a.Task.SOU_SCHOOL_NO,
                    SOU_ITEM_NO = a.Task.SOU_ITEM_NO,
                    TASK_DATE = a.Task.TASK_DATE,
                    TASK_DESC = a.Task.TASK_DESC,
                    ANSWERS = a.Task.ANSWERS,
                    UPLOAD_FILES = a.Task.UPLOAD_FILES,
                }).ToList();

                model.Datalist = model.Datalist.Select(
                    a =>
                    {
                        a.FilePaths = ADDI11Service.GetFilePath(a.SCHOOL_NO, a.USER_NO, a.SOU_SCHOOL_NO, a.SOU_ITEM_NO, 2);
                        a.FileName = (a.UPLOAD_FILES ?? "").Split('|');
                        return a;
                    }
                    ).ToList();

                return PartialView(model);
            }
        }

        public ActionResult ShowCERT05View(ZZZI09ShowCERT05ViewModel model)
        {
            this.Shared();

            string Message = string.Empty;
            if (model.LogimUser != "Y") { 
            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }
            }
            CER002Service cER002Service = new CER002Service();
            model.ListData = cER002Service.GetMyAccreditationListData(model.School_No, model.User_No, ref db).ToList();

            var cERT03G = db.CERT03_G.Where(x => x.SCHOOL_NO == model.School_No).ToListNoLock();

            if (cERT03G.Count > 0)
            {
                model.ListData.Select(a =>
                {
                    a.GRADE_SEMESTERs = cERT03G.Where(x => x.ACCREDITATION_ID == a.ACCREDITATION_ID && x.ITEM_NO == a.ITEM_NO)
                    .Select(x => $"{HRMT01.ParserGrade((byte)x.GRADE)}{HRMT01.ParserSemester((byte)x.SEMESTER)}").ToList();
                    return a;
                }).ToList();
            }

            return PartialView(model);
        }
        [HttpPost]
        public ActionResult _GetUSER_NODDLHtmlForALLOlDE(string tagId, string tagName, string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wIDNO, string wDATA_ANGLE_TYPE)
        {
            user = EcoolWeb.Models.UserProfileHelper.Get();

            this.Shared();

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var UserNoListData = HRMT01.GetUserNoListData(wSCHOOL_NO, wCLASS_NO, ref db);

                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData && user.USER_TYPE == "S")
                {
                    UserNoListData = HRMT01.GetUserNoListOneData(wSCHOOL_NO, wCLASS_NO, wIDNO, ref db);
                }
                else if (user.USER_TYPE != "S" && string.IsNullOrEmpty(wIDNO) == false) 
               {

                    UserNoListData = HRMT01.GetUserNoListOneData(wSCHOOL_NO, wCLASS_NO, wIDNO, ref db);

                }
                string _html = string.Empty;
                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, wUSER_NO, false, null);
                }
                else if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData && user.USER_TYPE == "S")
                {

                    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, wUSER_NO, false, null);
                }
                else
                {
                    if (user.USER_TYPE == "S")
                    {
                        UserNoListData = UserNoListData.Where(a => a.Value == user.USER_NO).AsQueryable();
                    }
                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, "", true, null);
                }

                return Content(_html);
            }
        }
        [HttpPost]
        public ActionResult _GetUSER_NODDLHtmlForALL(string tagId, string tagName, string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wIDNO,string wDATA_ANGLE_TYPE)
        {
            user = EcoolWeb.Models.UserProfileHelper.Get();

            this.Shared();

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var UserNoListData = HRMT01.GetUserNoListData(wSCHOOL_NO, wCLASS_NO, ref db);

                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData&& user.USER_TYPE=="S")
                {
                    UserNoListData = HRMT01.GetUserNoListOneData(wSCHOOL_NO, wCLASS_NO, wIDNO,ref db);
                }
                string _html = string.Empty;
                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control",onchange = "ChangeSCHOOL_NOUseReplaceWith()" }, wUSER_NO, false, null);
                }
                else if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData && user.USER_TYPE == "S") {

                    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control", onchange = "ChangeSCHOOL_NOUseReplaceWith()" }, wUSER_NO, false, null);
                }
                else
                {
                    if (user.USER_TYPE == "S")
                    {
                        UserNoListData = UserNoListData.Where(a => a.Value == user.USER_NO).AsQueryable();
                    }
                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control", onchange = "ChangeSCHOOL_NOUseReplaceWith()" }, "", true, null);
                }

                return Content(_html);
            }
        }
        [HttpPost]
        public ActionResult _GetUSER_NODDLHtmlForALLstr(string tagId, string tagName, string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wIDNO, string wDATA_ANGLE_TYPE)
        {
            user = EcoolWeb.Models.UserProfileHelper.Get();

            this.Shared();

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var UserNoListData = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == wCLASS_NO && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))).Select(x => x.USER_NO).ToList();

                //if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData && user.USER_TYPE == "S")
                //{
                //    UserNoListData = HRMT01.GetUserNoListOneData(wSCHOOL_NO, wCLASS_NO, wIDNO, ref db);
                //}
                //string _html = string.Empty;
                //if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                //{
                //    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                //    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, wUSER_NO, false, null);
                //}
                //else if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData && user.USER_TYPE == "S")
                //{

                //    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                //    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, wUSER_NO, false, null);
                //}
                //else
                //{
                //    if (user.USER_TYPE == "S")
                //    {
                //        UserNoListData = UserNoListData.Where(a => a.Value == user.USER_NO).AsQueryable();
                //    }
                //    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, "", true, null);
                //}
                Dictionary<string, List<string>> temp = new Dictionary<string, List<string>>();
                temp = HRMT01.UserNoListDatestring(wSCHOOL_NO, wCLASS_NO, ref db);
                ViewBag.UserNOstrSIng = temp[wCLASS_NO];
                return Json(UserNoListData);
            } }
        [HttpPost]
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE)
        {
            user = EcoolWeb.Models.UserProfileHelper.Get();

            this.Shared();

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var UserNoListData = HRMT01.GetUserNoListData(wSCHOOL_NO, wCLASS_NO, ref db);
            
           

                string _html = string.Empty;
                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownOnchangeList(tagId, tagName, UserNoListData, new { @class = "form-control",@onchange= "ChangeSCHOOL_NOUseReplaceWith()" }, wUSER_NO, false, null);
                }
                else
                {
                    if (user.USER_TYPE == "S")
                    {
                        UserNoListData = UserNoListData.Where(a => a.Value == user.USER_NO).AsQueryable();
                    }
                    _html = Util.DropDownListHelper.GetDropdownOnchangeList(tagId, tagName, UserNoListData, new { @class = "form-control", @onchange = "ChangeSCHOOL_NOUseReplaceWith()" }, "", true, null);
                }

                return Content(_html);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Shared

        private void Shared()
        {
            ViewBag.Panel_Title = Title;
            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                }
            }
        }

        #endregion Shared
    }
}