/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Variants/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Variants-bold-italic"]={directory:"Variants/BoldItalic",family:"STIXMathJax_Variants",weight:"bold",style:"italic",testString:"\u00A0\uE247\uE248\uE249\uE24A\uE24B\uE24C\uE24D\uE24E\uE24F\uE250\uE251\uE252\uE253\uE254",32:[0,0,250,0,0],160:[0,0,250,0,0],57927:[711,47,871,38,834],57928:[703,10,755,33,740],57929:[704,12,667,36,669],57930:[696,0,802,30,808],57931:[704,8,609,41,626],57932:[696,0,645,34,738],57933:[704,144,615,43,615],57934:[696,24,849,22,858],57935:[696,0,621,36,623],57936:[695,116,645,36,811],57937:[703,14,856,38,820],57938:[704,8,726,38,688],57939:[705,45,1186,38,1146],57940:[835,39,997,36,1098],57941:[707,10,772,43,782],57942:[696,0,645,36,731],57943:[704,145,778,43,737],57944:[697,13,869,36,831],57945:[705,7,667,36,699],57946:[783,0,547,33,747],57947:[700,14,787,33,936],57948:[711,31,652,36,706],57949:[711,34,956,36,1010],57950:[710,14,720,36,781],57951:[711,144,720,36,773],57952:[702,98,778,36,744],57956:[473,10,600,47,554],57960:[473,0,600,95,450],57964:[473,0,600,54,531],57968:[463,217,600,31,547],57972:[450,217,600,30,564],57976:[450,218,600,25,561],57980:[670,10,600,55,545],57984:[450,217,600,24,582],57988:[670,10,600,41,560],57992:[463,217,600,49,539]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Variants-bold-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Variants/BoldItalic/Main.js"]);
