/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Normal/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Normal-italic"]={directory:"Normal/Italic",family:"STIXMathJax_Normal",style:"italic",testString:"\u00A0\u210E\uD835\uDC34\uD835\uDC35\uD835\uDC36\uD835\uDC37\uD835\uDC38\uD835\uDC39\uD835\uDC3A\uD835\uDC3B\uD835\uDC3C\uD835\uDC3D\uD835\uDC3E\uD835\uDC3F\uD835\uDC40",32:[0,0,250,0,0],160:[0,0,250,0,0],8462:[668,11,513,45,483],119860:[667,0,717,35,685],119861:[653,0,696,38,686],119862:[659,12,671,50,711],119863:[653,0,790,38,765],119864:[653,0,714,38,734],119865:[653,0,618,38,723],119866:[668,12,734,50,734],119867:[653,0,873,38,923],119868:[653,0,480,38,530],119869:[653,12,540,60,620],119870:[653,0,762,38,802],119871:[653,0,708,38,668],119872:[653,0,1005,38,1055],119873:[653,0,851,38,901],119874:[669,11,732,50,712],119875:[653,0,594,38,704],119876:[667,152,781,50,731],119877:[653,0,740,38,725],119878:[668,10,650,50,680],119879:[653,0,550,25,670],119880:[653,13,705,65,775],119881:[653,16,575,60,760],119882:[653,16,916,60,1101],119883:[653,0,790,25,810],119884:[653,0,535,35,695],119885:[653,0,772,60,802],119886:[441,10,502,40,472],119887:[668,11,470,45,450],119888:[441,11,415,40,400],119889:[668,12,532,40,527],119890:[441,11,445,40,410],119891:[668,187,555,40,615],119892:[441,187,492,20,492],119894:[616,11,311,50,257],119895:[616,187,389,-16,372],119896:[668,11,542,45,527],119897:[668,10,318,45,278],119898:[441,8,710,30,680],119899:[441,8,497,30,467],119900:[441,11,458,40,438],119901:[441,183,489,-30,474],119902:[441,183,458,40,463],119903:[441,0,408,30,393],119904:[441,11,440,50,390],119905:[567,9,313,40,283],119906:[441,9,474,30,444],119907:[458,9,506,72,479],119908:[460,9,775,72,748],119909:[441,9,550,30,510],119910:[440,183,496,30,496],119911:[450,14,499,42,467],120484:[441,11,278,47,235],120485:[441,207,278,-124,246],120546:[667,0,717,35,685],120547:[653,0,696,38,686],120548:[653,0,616,38,721],120549:[667,0,596,30,556],120550:[653,0,714,38,734],120551:[653,0,772,60,802],120552:[653,0,873,38,923],120553:[669,11,737,50,712],120554:[653,0,480,38,530],120555:[653,0,762,38,802],120556:[667,0,718,35,686],120557:[653,0,1005,38,1055],120558:[653,0,851,38,901],120559:[653,0,706,52,741],120560:[669,11,732,50,712],120561:[653,0,873,38,923],120562:[653,0,594,38,704],120563:[669,11,737,50,712],120564:[653,0,735,58,760],120565:[653,0,550,25,670],120566:[668,0,613,28,743],120567:[653,0,772,25,747],120568:[653,0,790,25,810],120569:[667,0,670,28,743],120570:[666,0,800,32,777],120571:[653,15,627,42,600],120572:[441,10,524,40,529],120573:[668,183,493,25,518],120574:[441,187,428,35,458],120575:[668,11,463,40,451],120576:[441,11,484,25,444],120577:[668,183,435,40,480],120578:[441,183,460,30,455],120579:[668,11,484,40,474],120580:[441,11,267,50,227],120581:[441,0,534,50,549],120582:[668,16,541,50,511],120583:[428,183,579,30,549],120584:[446,9,452,50,462],120585:[668,183,433,25,443],120586:[441,11,458,40,438],120587:[428,13,558,35,568],120588:[441,183,502,30,472],120589:[490,183,439,35,464],120590:[428,11,537,40,547],120591:[428,5,442,30,472],120592:[439,11,460,30,445],120593:[441,183,666,50,631],120594:[441,202,595,30,645],120595:[441,183,661,30,711],120596:[441,11,681,20,661],120597:[668,11,471,40,471],120598:[441,11,430,40,430],120599:[678,10,554,20,507],120600:[441,13,561,12,587],120601:[668,183,645,40,620],120602:[441,187,509,40,489],120603:[428,11,856,30,866]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Normal-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/Italic/Main.js"]);
