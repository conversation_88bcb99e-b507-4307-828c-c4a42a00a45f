﻿@model ECOOL_APP.EF.AWAT02
@{
    ViewBag.Title = "獎品兌換-確認兌換獎品內容";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = ViewBag.User;

    string aImgUrl = ViewBag.ImgUrl + Model.SCHOOL_NO + @"/"+ Model.IMG_FILE.ToString();
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<form action="#" name="contentForm" id="contentForm" method="post">
    @Html.Hidden("hidAWARD_NO",Model.AWARD_NO)
    <img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Awat02">
        <div class="form-horizontal">
            <div class="col-md-12">
                <div class="p-context">
                    <img src='@aImgUrl' style="float:right;margin:10px;max-width:200px;width:auto" href="@aImgUrl" class="img-responsive" />
                    <div>
                        <samp class="dt">
                            學生姓名
                        </samp>
                        <samp class="dd">
                            @user.NAME
                        </samp>
                    </div>
                    <div>
                        <samp class="dt">
                            獎品名稱
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(model => model.AWARD_NAME)
                        </samp>
                    </div>
                    <div>
                        <samp class="dt">
                            兌換點數
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(model => model.COST_CASH)
                        </samp>
                    </div>
                    <div>
                        <samp class="dt">
                            兌換後剩餘點數
                        </samp>
                        <samp class="dd">
                            @(user.CASH - Convert.ToInt32(Model.COST_CASH))
                        </samp>
                    </div>
                    @if (Model.AWARD_TYPE == "P" || Model.AWARD_TYPE == "C")
                    {
                        <div>
                            <samp class="dt">
                                已募資點數
                            </samp>
                            <samp class="dd">
                              @(Model.COST_CASH * Model.QTY_TRANS)
                            </samp>
                        </div>
                    }
                    else
                    {
                        <div>
                            <samp class="dt">
                                剩餘數量
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(model => model.QTY_STORAGE)
                            </samp>
                        </div>
                    }
             
                    <div>
                        <samp class="dt">
                            閱讀認證
                        </samp>
                        <samp class="dd">
                            @if (Model.READ_LEVEL != null)
                            {
                              
                                <samp>需滿</samp>                                
                                <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgReadUrl(Model.READ_LEVEL))" style="max-height:30px;margin-right:1px">
                                <samp>級</samp>
                            }
                            else
                            {
                                 <samp>無限制</samp>
                            }
                        </samp>
                    </div>
                    <div>
                        <samp class="dt">
                            閱讀護照
                        </samp>
                        <samp class="dd">
                            @if (Model.PASSPORT_LEVEL != null)
                            {
                                <samp>需滿</samp>
                                <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgPassportUrl(Model.PASSPORT_LEVEL))" style="max-height:30px;margin-right:1px">
                                <samp>級</samp>
                            }
                            else
                            {
                                <samp>無限制</samp>
                            }
                        </samp>
                    </div>
                    @if (Model.SHOW_DESCRIPTION_YN == "Y")
                    {
                        <div>
                            <samp class="dt">
                                備註說明
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(model => model.DESCRIPTION)
                            </samp>
                        </div>
                    }
                </div>
            </div>
            <div class="row Div-btn-center">
                <div class="form-group">

                    @if (string.IsNullOrWhiteSpace(ViewBag.NGError))
                    {
                        <div class="col-md-offset-3 col-md-3">
                            <button role="button" type="button" id="Create"  value="Create" class="btn btn-default" onclick="doExchange()">
                                確定送出
                            </button>
                        </div>

                        <div class="col-md-offset-1 col-md-3">
                            <a href='@Url.Action("AwatQ02", "Awat")' role="button" class="btn btn-default">
                                放棄
                            </a>
                        </div>
                    }
                    else
                    {
                        <a href='@Url.Action("AwatQ02", "Awat")' role="button" class="btn btn-default">
                            返回
                        </a>
                    }


                </div>
            </div>
        </div>
    </div>
</form>
<!--資料顯示區END -->
<script type="text/javascript">

    $(document).ready(function () {
        $(".img-responsive").colorbox({ opacity: 0.82 });
    });

    function doExchange() {
        document.getElementById('Create').style.visibility = 'hidden';
        document.contentForm.action = "AwatExchange02Start";
        document.contentForm.submit();
    }

</script>