﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Util
{
    /// <summary>
    /// 資料轉換
    /// </summary>
    public class DataConvertHelper
    {

        /// <summary>
        /// Y 、N 轉 true 、false
        /// </summary>
        /// <param name="Value">Y 、N</param>
        /// <returns>true 、false</returns>
        public static bool YnToBool(string Value)
        {
  

            if (string.IsNullOrWhiteSpace(Value))
            {
                return false;
            }
            else
            {
                if (Value.ToUpper() == "Y") return true;
                else return false;
            }
        }

        /// <summary>
        /// true 、false 轉 Y 、N 
        /// </summary>
        /// <param name="Value"></param>
        /// <returns></returns>
        public static string BoolToYn(bool? boolVal)
        {
            if (boolVal==null)
            {
                return "N";
            }
            else
            {
                if ((bool)boolVal) return "Y";
                else return "N";
            }
        }

        /// <summary>
        /// true 、false 轉 Y 、N 
        /// </summary>
        /// <param name="Value"></param>
        /// <returns></returns>
        public static string BoolToYn(string boolVal)
        {
            if (string.IsNullOrWhiteSpace(boolVal))
            {
                return "N";
            }
            else
            {
                if (boolVal.ToLower()== "true") return "Y";
                else return "N";
            }
        }

    }
}