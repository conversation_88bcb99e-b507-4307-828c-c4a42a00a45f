﻿@model RollCallBarcodeCashDetailViewModel
@using (Html.BeginCollectionItem("Details"))
{
    var Index = Html.GetIndex("Details");
<div class="form-group row" id="Tr@(Index)">
    <div class="col-md-1" style="text-align:center;">
        @if (Model.ROLL_CALL_ID == null)
        {
            <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>}
        </div>
    @{
        string str = "calate('Details_" + Index + "__CASH','Details_" + Index + "__NUM','Sum" + Index + "')";
        //string str1 = "calate('Details_" + Index + "__NUM')";
    }
    <div class="col-md-3" style="text-align:center;">
        @if (Model.ROLL_CALL_ID != null)
        {

            @Html.EditorFor(m => m.CASH, new { htmlAttributes = new { @class = "form-control input-md", @onchange = str, disabled = "disabled" } })
        }
        else
        {
            @Html.EditorFor(m => m.CASH, new { htmlAttributes = new { @class = "form-control input-md", @onchange = str } })

        }
    </div>
    <div class="col-md-3" style="text-align:center;">
        @if (Model.ROLL_CALL_ID != null)
        {
            @Html.EditorFor(m => m.NUM, new { htmlAttributes = new { @class = "form-control input-md", @onchange = str, disabled = "disabled" } })

        }
        else
        {
            @Html.EditorFor(m => m.NUM, new { htmlAttributes = new { @class = "form-control input-md", @onchange = str } })
        }

    </div>
    @{ var itemi = 0;


        itemi = Model.CASH * Model.NUM;
    }
    <input id="Sum@(Index)" name="SumCash" value="@itemi" hidden/>
</div>
    }
