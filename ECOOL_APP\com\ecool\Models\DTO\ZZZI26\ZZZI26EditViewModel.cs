﻿using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI26EditViewModel
    {
        public List<ZZZI26DetailsViewModel> Details_List { get; set; }
        public ZZZI26SearchViewModel Search { get; set; }

        /// <summary>
        /// 增加明細筆數
        /// </summary>
        public int? ADDNUM;
    }


    public class ZZZI26DetailsViewModel
    {

        [DisplayName("刪除")]
        public bool Del { get; set; }

        ///Summary
        ///APPLY_NO
        ///Summary
        [DisplayName("APPLY_NO")]
        public int? APPLY_NO { get; set; }



        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("帳號")]
        [Required]
        public string USER_NO { get; set; }

        ///Summary
        ///CLASS_NO
        ///Summary
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        ///Summary
        ///SYEAR
        ///Summary
        [DisplayName("學年度")]
        public byte? SYEAR { get; set; }

        ///Summary
        ///SEMESTER
        ///Summary
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        ///Summary
        ///SEAT_NO
        ///Summary
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        ///Summary
        ///NAME
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        [DisplayName("姓名")]
        public string SNAME { get; set; }

        ///Summary
        ///PASSPORT_YN
        ///Summary
        [DisplayName("是否為閱讀護照")]
        public string PASSPORT_YN { get; set; }

        ///Summary
        ///BOOK_ID
        ///Summary
        [DisplayName("BOOK_ID")]
        public string BOOK_ID { get; set; }

        ///Summary
        ///BOOK_NAME
        ///Summary
        [DisplayName("書名")]
        [Required]
        public string BOOK_NAME { get; set; }

        ///Summary
        ///REVIEW
        ///Summary
        [DisplayName("REVIEW")]
        public string REVIEW { get; set; }

        ///Summary
        ///REVIEW_VERIFY
        ///Summary
        [DisplayName("REVIEW_VERIFY")]
        public string REVIEW_VERIFY { get; set; }

        ///Summary
        ///VERIFY_COMMENT
        ///Summary
        [DisplayName("VERIFY_COMMENT")]
        public string VERIFY_COMMENT { get; set; }

        ///Summary
        ///IMG_FILE
        ///Summary
        [DisplayName("IMG_FILE")]
        public string IMG_FILE { get; set; }

        ///Summary
        ///VOICE_FILE
        ///Summary
        [DisplayName("VOICE_FILE")]
        public string VOICE_FILE { get; set; }

        ///Summary
        ///APPLY_TYPE
        ///Summary
        [DisplayName("APPLY_TYPE")]
        public byte? APPLY_TYPE { get; set; }

        ///Summary
        ///APPLY_STATUS
        ///Summary
        [DisplayName("APPLY_STATUS")]
        public string APPLY_STATUS { get; set; }

        ///Summary
        ///VERIFIER
        ///Summary
        [DisplayName("VERIFIER")]
        public string VERIFIER { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
        [DisplayName("CRE_DATE")]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [DisplayName("CHG_DATE")]
        public DateTime? CHG_DATE { get; set; }

        ///Summary
        ///VERIFIED_DATE
        ///Summary
        [DisplayName("VERIFIED_DATE")]
        public DateTime? VERIFIED_DATE { get; set; }

        ///Summary
        ///推薦
        ///Summary
        [DisplayName("推薦")]
        public string SHARE_YN { get; set; }

        ///Summary
        ///MEMO
        ///Summary
        [DisplayName("備註")]
        public string MEMO { get; set; }

        public string OK_YN { get; set; }

        public HttpPostedFileBase files;

    }

}
