﻿@model ECOOL_APP.com.ecool.Models.DTO.AWAT14.AWAT14RankLogViewModel

    @{
        ViewBag.Title = ViewBag.Panel_Title;
        string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
        bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

        if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
        {
            Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        }
        string HidStyle = "";
    }
    @*<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>*@
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.SyntaxName)
    @if (AppMode == false)
    {
        @Html.Partial("_Title_Secondary")
    }
    @Html.Partial("_Notice")

    @**說明視窗*@
    <style>


        .table-ecool .tr-mrt-bw td:first-child {
            background-color: #f0f6ff;
            box-shadow: 0 0 3px #9cb5df;
        }

            .table-ecool .tr-mrt-bw td:first-child::before {
                font-size: 1em;
                font-weight: normal;
            }

        .table-ecool .tr-mrt-bw td:first-child {
            font-weight: 600;
        }

            .table-ecool .tr-mrt-bw td:first-child::before {
                content: "";
                display: inline-block;
                min-width: 2.3em;
                min-height: 1em;
                margin-right: .5em;
                vertical-align: text-bottom;
                background-repeat: no-repeat;
                background-position: left 20% center;
                background-size: auto 1em;
                border-radius: .5rem;
            }

        .table-ecool .tr-mrt-bj td:first-child {
            background-color: #fffaf0;
            box-shadow: 0 0 3px #dfc29c;
        }

            .table-ecool .tr-mrt-bj td:first-child::before {
                font-size: 1em;
                font-weight: normal;
            }

        .table-ecool .tr-mrt-bj td:first-child {
            font-weight: 600;
        }

            .table-ecool .tr-mrt-bj td:first-child::before {
                content: "";
                display: inline-block;
                min-width: 2.3em;
                min-height: 1em;
                margin-right: .5em;
                vertical-align: text-bottom;
                background-repeat: no-repeat;
                background-position: left 20% center;
                background-size: auto 1em;
                border-radius: .5rem;
            }

        .table-ecool .tr-mrt-bx td:first-child {
            background-color: #fff0f0;
            box-shadow: 0 0 3px #df9c9c;
        }

            .table-ecool .tr-mrt-bx td:first-child::before {
                font-size: 1em;
                font-weight: normal;
            }

        .table-ecool .tr-mrt-bx td:first-child {
            font-weight: 600;
        }

            .table-ecool .tr-mrt-bx td:first-child::before {
                content: "";
                display: inline-block;
                min-width: 2.3em;
                min-height: 1em;
                margin-right: .5em;
                vertical-align: text-bottom;
                background-repeat: no-repeat;
                background-position: left 20% center;
                background-size: auto 1em;
                border-radius: .5rem;
            }
    </style>

    <div id='container' style="@HidStyle">
        @{
            Html.RenderAction("_PageMenu", new { NowAction = "ADDTCountLog" });
        }
    </div>
    @using (Html.BeginForm("ADDTCountLog", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
    {
        @Html.AntiForgeryToken()

        <div id="PageContent">
            @Html.Action("_PageADDTCountLog", (string)ViewBag.BRE_NO)
        </div>
    }

    @section scripts{
        <script>
        var targetFromID = "#form1";
        $(document).ready(function () {

            $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
        });
        function searchFriendRunLog(userno,schoolno,classno) {

            $("#@(Html.IdFor(m=>m.WhereSCHOOL_NO))").val(schoolno);

            $(targetFromID).submit();
        }
        function funAjax() {


        $.ajax({
            type: "GET",
                url: '@(Url.Action("_PageRunCountLog", (string)ViewBag.BRE_NO))',
            data: {

                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),

                WhereSCHOOL_NO:$('#WhereSCHOOL_NO').val(),
            },
                async: false,
                cache: false,
                dataType: 'html',
            success: function (data) {

                $("#form1").html('');

                $("#form1").html(data);
                }
            });
    }
        function doSort(SortCol) {
            var sort = "";
            sort = SortCol;
            OrderByName = $('#OrdercColumn').val();
            SyntaxName = $('#SyntaxName').val();
            $('#OrdercColumn').val(SortCol);

            if (OrderByName == SortCol) {

                if (SyntaxName == "Desc") {

                    $('#OrdercColumn').val(sort);
                    $('#SyntaxName').val("ASC");
                }
                else {

                    $('#OrdercColumn').val(sort);
                    $('#SyntaxName').val("Desc");
                }
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
            funAjax()
        }
        </script>
    }