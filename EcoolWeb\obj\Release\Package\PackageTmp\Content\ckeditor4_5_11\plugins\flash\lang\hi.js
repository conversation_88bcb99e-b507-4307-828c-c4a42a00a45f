﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'hi', {
	access: 'Script Access', // MISSING
	accessAlways: 'Always', // MISSING
	accessNever: 'Never', // MISSING
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Abs नीचे',
	alignAbsMiddle: 'Abs ऊपर',
	alignBaseline: 'मूल रेखा',
	alignTextTop: 'टेक्स्ट ऊपर',
	bgcolor: 'बैक्ग्राउन्ड रंग',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'लूप',
	chkMenu: 'फ़्लैश मॅन्यू का प्रयोग करें',
	chkPlay: 'ऑटो प्ले',
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'हॉरिज़ॉन्टल स्पेस',
	properties: 'फ़्लैश प्रॉपर्टीज़',
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'स्केल',
	scaleAll: 'सभी दिखायें',
	scaleFit: 'बिल्कुल फ़िट',
	scaleNoBorder: 'कोई बॉर्डर नहीं',
	title: 'फ़्लैश प्रॉपर्टीज़',
	vSpace: 'वर्टिकल स्पेस',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'लिंक URL टाइप करें',
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
