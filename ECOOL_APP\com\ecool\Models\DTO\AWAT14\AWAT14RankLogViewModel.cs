﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO.AWAT14
{
    public class AWAT14RankLogViewModel
    {/// <summary>
     /// 判斷從UserController Index 連結進來的
     /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        public string WhereUSER_NO { get; set; }

        public string WhereCLASS_NO { get; set; }
        public string WhereRANKNO { get; set; }
        public List<AWAT14RankCount> RankCount { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }
        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }
        public IPagedList<AWAT14RankLogDataViewModel> ListData;
        public List<AWAT14RankLogDataViewModel> ColumnListData;
        public AWAT14RankLogViewModel()
        {
            PageSize = 7;
        }

    }
}
