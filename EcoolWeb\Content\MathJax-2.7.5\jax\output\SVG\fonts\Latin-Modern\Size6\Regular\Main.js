/*
 *  /MathJax/jax/output/SVG/fonts/Latin-Modern/Size6/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.LatinModernMathJax_Size6={directory:"Size6/Regular",family:"LatinModernMathJax_Size6",id:"LATINMODERNSIZE6",32:[0,0,332,0,0,""],40:[1446,946,736,226,682,"682 -928c0 -10 -8 -18 -18 -18c-4 0 -8 2 -11 4c-222 167 -427 613 -427 1006v372c0 393 205 839 427 1006c3 2 7 4 11 4c10 0 18 -8 18 -18c0 -6 -3 -11 -7 -14c-213 -160 -361 -603 -361 -978v-372c0 -375 148 -818 361 -978c4 -3 7 -8 7 -14"],41:[1446,946,736,54,510,"510 64c0 -393 -205 -839 -427 -1006c-3 -2 -7 -4 -11 -4c-10 0 -18 8 -18 18c0 6 3 11 7 14c213 160 361 603 361 978v372c0 375 -148 818 -361 978c-4 3 -7 8 -7 14c0 10 8 18 18 18c4 0 8 -2 11 -4c222 -167 427 -613 427 -1006v-372"],47:[2777,2277,1997,56,1941,"1941 2741c0 -4 -1 -9 -2 -12l-1813 -4982c-5 -14 -18 -24 -34 -24c-20 0 -36 16 -36 36c0 4 1 9 2 12l1813 4982c5 14 18 24 34 24c20 0 36 -16 36 -36"],91:[1450,950,528,233,509,"509 -921c0 -16 -13 -29 -29 -29h-247v2400h247c16 0 29 -13 29 -29s-13 -29 -29 -29h-189v-2284h189c16 0 29 -13 29 -29"],92:[2777,2277,1997,56,1941,"1941 -2241c0 -20 -16 -36 -36 -36c-16 0 -29 10 -34 24l-1813 4982c-1 3 -2 8 -2 12c0 20 16 36 36 36c16 0 29 -10 34 -24l1813 -4982c1 -3 2 -8 2 -12"],93:[1450,950,528,19,295,"295 1450v-2400h-247c-16 0 -29 13 -29 29s13 29 29 29h189v2284h-189c-16 0 -29 13 -29 29s13 29 29 29h247"],123:[1450,950,750,102,648,"648 -928c0 -12 -10 -22 -22 -22c-2 0 -4 0 -6 1c-155 48 -292 176 -292 299v600c0 110 -87 241 -210 279c-9 3 -16 11 -16 21s7 18 16 21c123 38 210 169 210 279v600c0 123 137 251 292 299c2 1 4 1 6 1c12 0 22 -10 22 -22c0 -10 -7 -18 -16 -21 c-122 -38 -210 -159 -210 -257v-600c0 -120 -107 -240 -237 -300c130 -60 237 -180 237 -300v-600c0 -98 88 -219 210 -257c9 -3 16 -11 16 -21"],124:[1752,1252,278,103,175,"175 -1216c0 -20 -16 -36 -36 -36s-36 16 -36 36v2932c0 20 16 36 36 36s36 -16 36 -36v-2932"],125:[1450,950,750,102,648,"648 250c0 -10 -7 -18 -16 -21c-123 -38 -210 -169 -210 -279v-600c0 -123 -137 -251 -292 -299c-2 -1 -4 -1 -6 -1c-12 0 -22 10 -22 22c0 10 7 18 16 21c122 38 210 159 210 257v600c0 120 107 240 237 300c-130 60 -237 180 -237 300v600c0 98 -88 219 -210 257 c-9 3 -16 11 -16 21c0 12 10 22 22 22c2 0 4 0 6 -1c155 -48 292 -176 292 -299v-600c0 -110 87 -241 210 -279c9 -3 16 -11 16 -21"],160:[0,0,332,0,0,""],770:[748,-570,1581,0,1581,"1581 598l-5 -28l-786 114l-785 -114l-5 28l790 150"],771:[769,-532,1599,0,1599,"1599 754c0 -4 -2 -8 -4 -11c-82 -75 -194 -135 -387 -160c-32 -5 -63 -6 -95 -6c-107 0 -214 20 -320 42c-101 20 -203 41 -305 41c-30 0 -59 -2 -88 -6c-182 -24 -299 -48 -376 -119c-2 -2 -6 -3 -9 -3c-9 0 -15 6 -15 14c0 4 2 8 5 11c81 75 193 135 386 160 c32 5 64 6 95 6c107 0 214 -20 320 -42c101 -20 203 -41 305 -41c30 0 59 2 89 6c181 24 298 48 375 119c2 2 6 4 10 4c8 0 14 -7 14 -15"],774:[743,-574,1604,0,1604,"1604 733c-54 -159 -486 -159 -802 -159s-749 0 -802 159l28 10c34 -105 473 -105 774 -105c300 0 739 0 774 105"],780:[742,-564,1581,0,1581,"1581 714l-791 -150l-790 150l5 28l785 -114l786 114"],785:[760,-591,1604,0,1604,"1604 601l-28 -10c-35 105 -474 105 -774 105c-301 0 -740 0 -774 -105l-28 10c53 159 486 159 802 159s748 0 802 -159"],812:[-96,275,1581,0,1581,"1581 -125l-791 -150l-790 150l5 29l785 -114l786 114"],813:[-108,287,1581,0,1581,"1581 -258l-5 -29l-786 114l-785 -114l-5 29l790 150"],814:[-96,265,1604,0,1604,"1604 -105c-54 -160 -486 -160 -802 -160s-749 0 -802 160l28 9c34 -105 473 -105 774 -105c300 0 739 0 774 105"],815:[-118,287,1604,0,1604,"1604 -277l-28 -10c-35 105 -474 105 -774 105c-301 0 -740 0 -774 -105l-28 10c53 159 486 159 802 159s748 0 802 -159"],816:[-118,355,1599,0,1599,"1599 -132c0 -5 -2 -9 -4 -11c-82 -75 -194 -135 -387 -161c-32 -4 -63 -6 -95 -6c-107 0 -214 21 -320 42c-101 20 -203 41 -305 41c-30 0 -59 -2 -88 -6c-182 -24 -299 -47 -376 -118c-2 -3 -6 -4 -9 -4c-9 0 -15 6 -15 15c0 4 2 8 5 10c81 75 193 135 386 161 c32 4 64 6 95 6c107 0 214 -21 320 -42c101 -20 203 -41 305 -41c30 0 59 2 89 6c181 24 298 47 375 118c2 3 6 4 10 4c8 0 14 -6 14 -14"],8214:[1752,1252,410,56,354,"128 -1216c0 -20 -16 -36 -36 -36s-36 16 -36 36v2932c0 20 16 36 36 36s36 -16 36 -36v-2932zM354 -1216c0 -20 -16 -36 -36 -36s-36 16 -36 36v2932c0 20 16 36 36 36s36 -16 36 -36v-2932"],8260:[2777,2277,1997,56,1941,"1941 2741c0 -4 -1 -9 -2 -12l-1813 -4982c-5 -14 -18 -24 -34 -24c-20 0 -36 16 -36 36c0 4 1 9 2 12l1813 4982c5 14 18 24 34 24c20 0 36 -16 36 -36"],8425:[764,-513,2610,0,2610,"2610 764v-222c0 -16 -13 -29 -29 -29s-29 13 -29 29v164h-2494v-164c0 -16 -13 -29 -29 -29s-29 13 -29 29v222h2610"],8739:[1752,1252,278,103,175,"175 -1216c0 -20 -16 -36 -36 -36s-36 16 -36 36v2932c0 20 16 36 36 36s36 -16 36 -36v-2932"],8741:[1752,1252,410,56,354,"128 -1216c0 -20 -16 -36 -36 -36s-36 16 -36 36v2932c0 20 16 36 36 36s36 -16 36 -36v-2932zM354 -1216c0 -20 -16 -36 -36 -36s-36 16 -36 36v2932c0 20 16 36 36 36s36 -16 36 -36v-2932"],8968:[1450,950,583,210,555,"555 1421c0 -16 -13 -29 -29 -29h-258v-2313c0 -16 -13 -29 -29 -29s-29 13 -29 29v2371h316c16 0 29 -13 29 -29"],8969:[1450,950,583,28,373,"373 -921c0 -16 -13 -29 -29 -29s-29 13 -29 29v2313h-258c-16 0 -29 13 -29 29s13 29 29 29h316v-2371"],8970:[1450,950,583,210,555,"555 -921c0 -16 -13 -29 -29 -29h-316v2371c0 16 13 29 29 29s29 -13 29 -29v-2313h258c16 0 29 -13 29 -29"],8971:[1450,950,583,28,373,"373 -950h-316c-16 0 -29 13 -29 29s13 29 29 29h258v2313c0 16 13 29 29 29s29 -13 29 -29v-2371"],9001:[1450,950,750,173,697,"697 -921c0 -16 -13 -29 -29 -29c-12 0 -23 8 -27 18l-466 1171c-1 4 -2 7 -2 11s1 7 2 11l466 1171c4 10 15 18 27 18c16 0 29 -13 29 -29c0 -4 -1 -7 -2 -11l-462 -1160l462 -1160c1 -4 2 -7 2 -11"],9002:[1450,950,750,53,577,"577 250c0 -4 -1 -7 -2 -11l-466 -1171c-4 -10 -15 -18 -27 -18c-16 0 -29 13 -29 29c0 4 1 7 2 11l462 1160l-462 1160c-1 4 -2 7 -2 11c0 16 13 29 29 29c12 0 23 -8 27 -18l466 -1171c1 -4 2 -7 2 -11"],9140:[764,-513,2610,0,2610,"2610 764v-222c0 -16 -13 -29 -29 -29s-29 13 -29 29v164h-2494v-164c0 -16 -13 -29 -29 -29s-29 13 -29 29v222h2610"],9141:[-83,334,2610,0,2610,"2610 -334h-2610v222c0 16 13 29 29 29s29 -13 29 -29v-164h2494v164c0 16 13 29 29 29s29 -13 29 -29v-222"],9180:[787,-505,3524,0,3524,"3524 527c0 -12 -10 -22 -22 -22c-6 0 -12 2 -16 6c-117 118 -715 188 -1330 188h-788c-615 0 -1213 -70 -1330 -188c-4 -4 -10 -6 -16 -6c-12 0 -22 10 -22 22c0 6 2 12 6 16c121 121 729 244 1362 244h788c633 0 1241 -123 1362 -244c4 -4 6 -10 6 -16"],9181:[-75,357,3524,0,3524,"3524 -97c0 -6 -2 -12 -6 -16c-121 -121 -729 -244 -1362 -244h-788c-633 0 -1241 123 -1362 244c-4 4 -6 10 -6 16c0 12 10 22 22 22c6 0 12 -2 16 -6c117 -118 715 -188 1330 -188h788c615 0 1213 70 1330 188c4 4 10 6 16 6c12 0 22 -10 22 -22"],9182:[845,-498,3502,0,3502,"3502 509c0 -7 -5 -11 -11 -11c-5 0 -9 3 -10 7c-32 85 -237 122 -446 122h-828c-229 0 -411 83 -456 167c-45 -84 -227 -167 -456 -167h-828c-209 0 -414 -37 -446 -122c-1 -4 -5 -7 -10 -7c-6 0 -11 4 -11 11c0 1 0 2 1 3c34 94 236 203 466 203h828 c239 0 445 33 445 119c0 6 5 11 11 11s11 -5 11 -11c0 -86 206 -119 445 -119h828c230 0 432 -109 466 -203c1 -1 1 -2 1 -3"],9183:[-67,414,3502,0,3502,"3502 -78c0 -2 0 -3 -1 -4c-34 -94 -236 -203 -466 -203h-828c-239 0 -445 -33 -445 -118c0 -7 -5 -11 -11 -11s-11 4 -11 11c0 85 -206 118 -445 118h-828c-230 0 -432 109 -466 203c-1 1 -1 2 -1 4c0 6 5 11 11 11c5 0 9 -4 10 -8c32 -85 237 -122 446 -122h828 c229 0 411 -83 456 -167c45 84 227 167 456 167h828c209 0 414 37 446 122c1 4 5 8 10 8c6 0 11 -5 11 -11"],9184:[869,-606,3574,0,3574,"3574 606h-94l-169 169h-3048l-169 -169h-94l263 263h3048"],9185:[-176,439,3574,0,3574,"3574 -176l-263 -263h-3048l-263 263h94l169 -169h3048l169 169h94"],10214:[1450,950,838,282,816,"816 -921c0 -16 -13 -29 -29 -29h-505v2400h505c16 0 29 -13 29 -29s-13 -29 -29 -29h-194v-2284h194c16 0 29 -13 29 -29zM535 -892v2284h-195v-2284h195"],10215:[1450,950,838,22,556,"556 -950h-505c-16 0 -29 13 -29 29s13 29 29 29h194v2284h-194c-16 0 -29 13 -29 29s13 29 29 29h505v-2400zM498 -892v2284h-195v-2284h195"],10216:[1450,950,750,173,697,"697 -921c0 -16 -13 -29 -29 -29c-12 0 -23 8 -27 18l-466 1171c-1 4 -2 7 -2 11s1 7 2 11l466 1171c4 10 15 18 27 18c16 0 29 -13 29 -29c0 -4 -1 -7 -2 -11l-462 -1160l462 -1160c1 -4 2 -7 2 -11"],10217:[1450,950,750,53,577,"577 250c0 -4 -1 -7 -2 -11l-466 -1171c-4 -10 -15 -18 -27 -18c-16 0 -29 13 -29 29c0 4 1 7 2 11l462 1160l-462 1160c-1 4 -2 7 -2 11c0 16 13 29 29 29c12 0 23 -8 27 -18l466 -1171c1 -4 2 -7 2 -11"],10218:[1450,950,1124,173,1071,"697 -921c0 -16 -13 -29 -29 -29c-12 0 -23 8 -27 18l-466 1171c-1 4 -2 7 -2 11s1 7 2 11l466 1171c4 10 15 18 27 18c16 0 29 -13 29 -29c0 -4 -1 -7 -2 -11l-462 -1160l462 -1160c1 -4 2 -7 2 -11zM1071 -921c0 -16 -13 -29 -29 -29c-12 0 -22 8 -27 18l-466 1171 c-1 4 -2 7 -2 11s1 7 2 11l466 1171c5 10 15 18 27 18c16 0 29 -13 29 -29c0 -4 0 -7 -2 -11l-461 -1160l461 -1160c2 -4 2 -7 2 -11"],10219:[1450,950,1124,53,951,"951 250c0 -4 0 -7 -2 -11l-466 -1171c-4 -10 -14 -18 -27 -18c-16 0 -29 13 -29 29c0 4 1 7 2 11l462 1160l-462 1160c-1 4 -2 7 -2 11c0 16 13 29 29 29c13 0 23 -8 27 -18l466 -1171c2 -4 2 -7 2 -11zM577 250c0 -4 -1 -7 -2 -11l-466 -1171c-4 -10 -15 -18 -27 -18 c-16 0 -29 13 -29 29c0 4 1 7 2 11l462 1160l-462 1160c-1 4 -2 7 -2 11c0 16 13 29 29 29c12 0 23 -8 27 -18l466 -1171c1 -4 2 -7 2 -11"],10222:[1472,972,541,235,485,"485 -950c0 -12 -10 -22 -22 -22c-6 0 -12 2 -16 6c-104 105 -212 486 -212 859v714c0 373 108 754 212 859c4 4 10 6 16 6c12 0 22 -10 22 -22c0 -6 -2 -12 -6 -16c-100 -99 -156 -472 -156 -827v-714c0 -355 56 -728 156 -827c4 -4 6 -10 6 -16"],10223:[1472,972,541,56,306,"306 -107c0 -373 -108 -754 -212 -859c-4 -4 -10 -6 -16 -6c-12 0 -22 10 -22 22c0 6 2 12 6 16c100 99 156 472 156 827v714c0 355 -56 728 -156 827c-4 4 -6 10 -6 16c0 12 10 22 22 22c6 0 12 -2 16 -6c104 -105 212 -486 212 -859v-714"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size6/Regular/Main.js");
