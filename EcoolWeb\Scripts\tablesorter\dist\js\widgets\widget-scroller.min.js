(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: scroller - updated 2018-05-07 (v2.30.4) */
!function(z,x){"use strict";var B=z.tablesorter,R=B.css;z.extend(B.css,{scrollerWrap:"tablesorter-scroller",scrollerHeader:"tablesorter-scroller-header",scrollerTable:"tablesorter-scroller-table",scrollerFooter:"tablesorter-scroller-footer",scrollerFixed:"tablesorter-scroller-fixed",scrollerFixedPanel:"tablesorter-scroller-fixed-panel",scrollerHasFix:"tablesorter-scroller-has-fixed-columns",scrollerHideColumn:"tablesorter-scroller-hidden-column",scrollerHideElement:"tablesorter-scroller-hidden",scrollerSpacerRow:"tablesorter-scroller-spacer",scrollerBarSpacer:"tablesorter-scroller-bar-spacer",scrollerAddedHeight:"tablesorter-scroller-added-height",scrollerHack:"tablesorter-scroller-scrollbar-hack",scrollerRtl:"ts-scroller-rtl"}),B.addWidget({id:"scroller",priority:60,options:{scroller_height:300,scroller_jumpToHeader:!0,scroller_upAfterSort:!0,scroller_fixedColumns:0,scroller_rowHighlight:"hover",scroller_addFixedOverlay:!1,scroller_barWidth:null},format:function(e,r,l){r.isScrolling?B.scroller.resize(r,l):B.scroller.setup(r,l)},remove:function(e,r,l){B.scroller.remove(r,l)}}),B.window_resize=function(){B.timer_resize&&clearTimeout(B.timer_resize),B.timer_resize=setTimeout(function(){z(x).trigger("resizeEnd")},250)},z(function(){var e="<style>."+R.scrollerWrap+" { position: relative; overflow: hidden; }."+R.scrollerWrap+" * { box-sizing: border-box; }."+R.scrollerHeader+", ."+R.scrollerFooter+" { position: relative; overflow: hidden; }."+R.scrollerHeader+" table."+R.table+" { margin-bottom: 0; }."+R.scrollerTable+" { position: relative; overflow: auto; }."+R.scrollerTable+" table."+R.table+" { border-top: 0; margin-top: 0; margin-bottom: 0; overflow: hidden; max-width: initial; }."+R.scrollerTable+" tfoot, ."+R.scrollerHideElement+", ."+R.scrollerHideColumn+" { display: none; }."+R.scrollerFixed+", ."+R.scrollerFixed+" ."+R.scrollerFixedPanel+" { pointer-events: none; }."+R.scrollerFixed+" > div { pointer-events: all; }."+R.scrollerWrap+" ."+R.scrollerFixed+" { position: absolute; top: 0; z-index: 1; left: 0 } ."+R.scrollerWrap+" ."+R.scrollerFixed+"."+R.scrollerRtl+" { left: auto; right: 0 } ."+R.scrollerWrap+"."+R.scrollerHasFix+" > ."+R.scrollerTable+" { overflow: auto; }."+R.scrollerFixed+" ."+R.scrollerFooter+" { position: absolute; bottom: 0; }."+R.scrollerFixed+" ."+R.scrollerTable+" { position: relative; left: 0; overflow: auto; -ms-overflow-style: none; }."+R.scrollerFixed+" ."+R.scrollerTable+"::-webkit-scrollbar { display: none; }."+R.scrollerWrap+" ."+R.scrollerFixedPanel+" { position: absolute; top: 0; bottom: 0; z-index: 2; left: 0; right: 0; } </style>";z("head").append(e)}),B.scroller={isFirefox:-1<navigator.userAgent.toLowerCase().indexOf("firefox"),isOldIE:document.all&&!x.atob,isIE:document.all&&!x.atob||0<navigator.appVersion.indexOf("Trident/"),isSafari:-1<navigator.userAgent.toLowerCase().indexOf("safari")&&-1===navigator.userAgent.toLowerCase().indexOf("chrome"),hasScrollBar:function(e,r){return r?e.get(0).scrollWidth>e.width():e.get(0).scrollHeight>e.height()},setWidth:function(e,r){e.css({width:r,"min-width":r,"max-width":r})},getBarWidth:function(){var e=z("<div>").css({position:"absolute",top:"-9999px",left:0,width:"100px",height:"100px",overflow:"scroll",visibility:"hidden"}).appendTo("body"),r=e[0],l=r.offsetWidth-r.clientWidth;return e.remove(),l},setup:function(o,s){var r,l,e,t,i,d,c,a,n=z(x),h=B.scroller,f=o.namespace+"tsscroller",p=z(),b=o.namespace.slice(1)+"tsscroller",u=o.$table;o.widthFixed=!0,s.scroller_calcWidths=[],s.scroller_saved=[0,0],s.scroller_isBusy=!0,(s.scroller_scrollTimer=null)!==s.scroller_barWidth?s.scroller_barSetWidth=s.scroller_barWidth:(a=h.getBarWidth(),s.scroller_barSetWidth=null!==a?a:15),c=u.children("caption"),l=z('<table class="'+u.attr("class")+'" cellpadding=0 cellspacing=0>'+(c.length?c[0].outerHTML:"")+u.children("thead")[0].outerHTML+"</table>"),s.scroller_$header=l.addClass(o.namespace.slice(1)+"_extra_table"),(e=u.children("tfoot")).length&&(p=z('<table class="'+u.attr("class")+'" cellpadding=0 cellspacing=0 style="margin-top:0"></table>').addClass(o.namespace.slice(1)+"_extra_table").append(e.clone(!0)).wrap('<div class="'+R.scrollerFooter+'"/>')),s.scroller_$footer=p,u.wrap('<div id="'+b+'" class="'+R.scrollerWrap+'" />').before(l).find("."+R.filterRow).addClass(R.filterRowHide),s.scroller_$container=u.parent(),p.length&&u.after(p.parent()),t=l.wrap('<div class="'+R.scrollerHeader+'" />').find("."+R.header),u.wrap('<div class="'+R.scrollerTable+(0<s.scroller_height?'" style="max-height:'+s.scroller_height+'px;">':'">')),i=u.parent(),B.bindEvents(o.table,t),u.hasClass("hasFilters")&&B.filter.bindSearch(u,l.find("."+R.filter)),u.children("thead, caption").addClass(R.scrollerHideElement),r=i.parent().height(),i.off("scroll"+f).on("scroll"+f,function(){if(clearTimeout(s.scroller_scrollTimer),s.scroller_scrollTimer=setTimeout(function(){s.scroller_saved[0]=i.scrollLeft(),s.scroller_saved[1]=i.scrollTop()},300),s.scroller_jumpToHeader){var e=n.scrollTop()-l.offset().top;0!==z(this).scrollTop()&&e<r&&0<e&&n.scrollTop(l.offset().top)}l.parent().add(p.parent()).scrollLeft(z(this).scrollLeft())}),d=((c=B.hasWidget(o.table,"filter")?"filterEnd filterInit":"tablesorter-initialized updateComplete")+" sortEnd pagerComplete columnUpdate ").split(" ").join(f+" "),u.off(f).on("sortStart"+f,function(){clearTimeout(s.scroller_scrollTimer),s.scroller_isBusy=!0}).on("sortEnd filterEnd".split(" ").join(f+" "),function(e){"sortEnd"===e.type&&s.scroller_upAfterSort?i.scrollLeft(s.scroller_saved[0]).animate({scrollTop:0},"fast",function(){s.scroller_isBusy=!1}):s.scroller_fixedColumns&&setTimeout(function(){i.scrollTop(s.scroller_saved[1]).scrollLeft(s.scroller_saved[0]),h.updateFixed(o,s)},0)}).on("setFixedColumnSize"+f,function(e,r){var l=s.scroller_$container;void 0===r||isNaN(r)||(s.scroller_fixedColumns=parseInt(r,10)),h.removeFixed(o,s),0<(r=s.scroller_fixedColumns)&&r<o.columns-1?h.updateFixed(o,s):l.hasClass(R.scrollerHasFix)&&(l.removeClass(R.scrollerHasFix),h.resize(o,s))}).on(d,function(e){B.hasWidget("pager")&&"updateComplete"===e.type||(0<s.scroller_fixedColumns&&h.updateFixed(o,s),h.resize(o,s))}),n.off("resize resizeEnd ".split(" ").join(f+" ")).on("resize"+f,B.window_resize).on("resizeEnd"+f,function(){n.off("resize"+f,B.window_resize),h.resize(o,s),n.on("resize"+f,B.window_resize),i.trigger("scroll"+f)}),o.isScrolling=!0,h.updateFixed(o,s),o.table.hasInitialized&&o.isScrolling&&setTimeout(function(){B.scroller.resize(o,s)},50)},resize:function(e,r){if(!r.scroller_isBusy){var l,o,s,t,i,d=B.scroller,c=r.scroller_$container,a=e.$table,n=a.parent(),h=r.scroller_$header,f=r.scroller_$footer,p=z(x),b=[p.scrollLeft(),p.scrollTop()],u=e.namespace.slice(1)+"tsscroller",m=z("div."+R.scrollerWrap+'[id!="'+u+'"]').addClass(R.scrollerHideElement),g="padding:0;margin:0;border:0;height:0;max-height:0;min-height:0;",v='<tr class="'+R.scrollerSpacerRow+" "+e.selectorRemove.slice(1)+'" style="'+g+'">';for(r.scroller_calcWidths=[],d.removeFixed(e,r),c.find("."+R.scrollerSpacerRow).remove(),c.find("."+B.css.colgroup).remove(),a.find("."+R.scrollerHideElement).removeClass(R.scrollerHideElement),o=parseInt(a.css("border-left-width"),10),t=e.$headerIndexed,l=0;l<e.columns;l++)v+='<td data-column="'+l+'" style="'+g+"width:"+(s="border-box"===(i=t[l]).css("box-sizing")?i.outerWidth():"collapse"===i.css("border-collapse")?i.length&&x.getComputedStyle?parseFloat(x.getComputedStyle(i[0],null).width):i.outerWidth()-parseFloat(i.css("padding-left"))-parseFloat(i.css("padding-right"))-(parseFloat(i.css("border-width"))||0):i.width())+"px;min-width:"+s+"px;max-width:"+s+'px"></td>',r.scroller_calcWidths[l]=s;v+="</tr>",e.$tbodies.eq(0).append(v),h.children("thead").append(v),f.children("tfoot").append(v),B.fixColumnWidth(e.table),v=e.$table.children("colgroup")[0].outerHTML,h.append(v),f.append(v),g=n.parent().innerWidth()-(d.hasScrollBar(n)?r.scroller_barSetWidth:0),n.width(g),g=(d.hasScrollBar(n)?r.scroller_barSetWidth:0)+o,s=n.innerWidth()-g,h.parent().add(f.parent()).width(s),n.width(s+g),a.children("thead, caption").addClass(R.scrollerHideElement),d.updateFixed(e,r),m.removeClass(R.scrollerHideElement),n.scrollTop(r.scroller_saved[1]),r.scroller_$container.find("."+R.scrollerFixed).find("."+R.scrollerTable).scrollTop(r.scroller_saved[1]),p.scrollLeft(b[0]),p.scrollTop(b[1]),setTimeout(function(){e.$table.triggerHandler("resizableUpdate"),e.$table.triggerHandler("scrollerComplete")},100)}},setupFixed:function(e,r){var l,o,s,t,i,d,c,a=e.$table,n=r.scroller_$container,h=r.scroller_fixedColumns;for((d=n.addClass(R.scrollerHasFix).clone().addClass(R.scrollerFixed).removeClass(R.scrollerWrap).attr("id","")).find("caption").html("&nbsp;"),r.scroller_addFixedOverlay&&d.append('<div class="'+R.scrollerFixedPanel+'">'),(c=d.find("."+R.scrollerTable)).children("table").addClass(e.namespace.slice(1)+"_extra_table").attr("id","").children("thead, tfoot").remove(),r.scroller_$fixedColumns=d,a.hasClass(R.scrollerRtl)&&d.addClass(R.scrollerRtl),t=(s=d.find("tr")).length,l=0;l<t;l++)s.eq(l).children(":gt("+(h-1)+")").remove();if(d.addClass(R.scrollerHideElement).prependTo(n),e.$table.hasClass("hasFilters"))for(s=d.find("."+R.filter).not("."+R.filterDisabled).prop("disabled",!1),B.filter.bindSearch(a,d.find("."+R.filter)),t=(s=n.children("."+R.scrollerHeader).find("."+R.filter)).length,l=0;l<t;l++)s.eq(l).hasClass(R.filterDisabled||"disabled")||s.eq(l).prop("disabled",l<h);for(e.$table.add("."+R.scrollerFooter+" table").children("thead").children("tr."+R.headerRow).children().attr("tabindex",-1),t=(s=r.scroller_$header.add(d.find("."+R.scrollerTable+" table")).children("thead").children("tr."+R.headerRow)).length,l=0;l<t;l++)for(i=s.eq(l).children(),o=0;o<i.length;o++)i.eq(o).attr("tabindex",o<h?-1:0);B.bindEvents(e.table,d.find("."+R.header)),B.scroller.bindFixedColumnEvents(e,r),(B.scroller.isFirefox||B.scroller.isOldIE)&&c.wrap('<div class="'+R.scrollerHack+'" style="overflow:hidden;">')},throttle:function(o,s,t){var i,d;return s=s||50,function(){var e=t||this,r=+new Date,l=arguments;i&&r<i+s?(clearTimeout(d),d=setTimeout(function(){i=r,o.apply(e,l)},s)):(i=r,o.apply(e,l))}},bindFixedColumnEvents:function(l,o){var e=B.scroller,r=l.namespace+"tsscrollerFixed",s="scroll"+r,t=o.scroller_$fixedColumns.find("."+R.scrollerTable),i=!0,d=!0;l.$table.parent().off(s).on(s,e.throttle(function(){if(!o.scroller_isBusy&&i){d=!1;var e=z(this);t[0].scrollTop=o.scroller_saved[1]=e.scrollTop(),o.scroller_saved[0]=e.scrollLeft(),setTimeout(function(){d=!0},20)}})),t.off(s).on(s,e.throttle(function(){!o.scroller_isBusy&&d&&(i=!1,l.$table.parent()[0].scrollTop=o.scroller_saved[1]=z(this).scrollTop(),setTimeout(function(){i=!0},20))})).scroll(),""!==o.scroller_rowHighlight&&(s="mouseover mouseleave ".split(" ").join(r+" "),l.$table.off(s,"tbody > tr").on(s,"tbody > tr",function(e){var r=l.$table.children("tbody").children("tr").index(this);t.children("table").children("tbody").children("tr").eq(r).add(this).toggleClass(o.scroller_rowHighlight,"mouseover"===e.type)}),t.find("table").off(s,"tbody > tr").on(s,"tbody > tr",function(e){var r=t.children("table").children("tbody").children("tr").index(this);l.$table.children("tbody").children("tr").eq(r).add(this).toggleClass(o.scroller_rowHighlight,"mouseover"===e.type)}))},adjustWidth:function(e,r,l,o,s){var t=r.scroller_$container;t.children("."+R.scrollerTable).css(s?"right":"left",l),t.children("."+R.scrollerHeader+", ."+R.scrollerFooter).css(s?"right":"left",l+(s&&B.scroller.isSafari?o:0))},updateFixed:function(e,r){var l,o,s=r.scroller_$container,t=r.scroller_$header,i=r.scroller_$footer,d=e.$table,c=d.parent(),a=r.scroller_barSetWidth,n=d.hasClass(R.scrollerRtl);if(0===r.scroller_fixedColumns)return r.scroller_isBusy=!1,B.scroller.removeFixed(e,r),l=s.width(),c.width(l),o=B.scroller.hasScrollBar(c)?a:0,void t.parent().add(i.parent()).width(l-o);if(e.isScrolling){r.scroller_isBusy=!0,s.find("."+R.scrollerFixed).length||B.scroller.setupFixed(e,r);var h,f,p,b,u,m,g,v=r.scroller_$container.children("."+R.scrollerTable).children("table").children("tbody"),x=r.scroller_$header.children("thead").children("."+R.headerRow),_=r.scroller_$fixedColumns.addClass(R.scrollerHideElement),w=_.find("."+R.scrollerTable).children("table"),C=w.children("tbody"),F=B.scroller,H=r.scroller_fixedColumns,T=function(e,r,l){return parseInt(e.css(r)||"",10)||l||0},y=d.find("tbody td"),$=T(y,"border-right-width",1),W=T(y,"border-spacing",0),S=T(d,"padding-left")+T(d,"padding-right")+2*T(d,"border-left-width",1)+T(d,"border-right-width",1)-$+W/2,E=r.scroller_calcWidths;for(B.scroller.removeFixed(e,r,!1),h=0;h<H;h++)S+=E[h]+W;for(S+=2*$,F.setWidth(_.add(_.children()),S),F.setWidth(_.children().children("table"),S),f=0;f<e.$tbodies.length;f++)if((b=v.eq(f)).length){for(g=(x=b.children()).length,(m=B.processTbody(w,C.eq(f),!0)).empty(),p=0;p<g;p++)(u=z(x[p].outerHTML)).children("td, th").slice(H).remove(),m.append(u);B.processTbody(w,m,!1)}for(o=B.scroller.hasScrollBar(c)?a:0,(F.isFirefox||F.isOldIE)&&w.css("width",S).parent().css("width",S+o),h=0;h<H;h++)l=":nth-child("+(h+1)+")",s.children("div").children("table").find("th"+l+", td"+l+", col"+l).addClass(R.scrollerHideColumn);_.removeClass(R.scrollerHideElement).find("colgroup").each(function(){z(this).find("col:gt("+(H-1)+")").addClass(R.scrollerHideElement)}),S-=$,l=c.parent().innerWidth()-S,c.width(l),s.children("."+R.scrollerTable).css(n?"right":"left",S),s.children("."+R.scrollerHeader+", ."+R.scrollerFooter).css(n?"right":"left",S+(n&&B.scroller.isSafari?o:0)),t.parent().add(i.parent()).width(l-o),o=(l=B.scroller.hasScrollBar(c,!0))?a:0,!_.find("."+R.scrollerBarSpacer).length&&l?(y=z('<div class="'+R.scrollerBarSpacer+'">').css("height",o+"px"),_.find("."+R.scrollerTable).append(y)):l||_.find("."+R.scrollerBarSpacer).remove(),B.scroller.updateRowHeight(e,r),_.height(s.height()),_.removeClass(R.scrollerHideElement),_.find("caption").height(r.scroller_$header.find("caption").height()),c.scroll(),setTimeout(function(){r.scroller_isBusy=!1},0)}},fixHeight:function(e,r){var l,o,s,t,i,d=R.scrollerAddedHeight,c=e.length;for(l=0;l<c;l++)t=e.eq(l),i=r.eq(l),o=t.height(),(s=i.height())<o?i.addClass(d).height(o):o<s&&t.addClass(d).height(s)},updateRowHeight:function(e,r){var l,o,s=r.scroller_$fixedColumns;r.scroller_$container.find("."+R.scrollerAddedHeight).removeClass(R.scrollerAddedHeight).height(""),l=r.scroller_$header.children("thead").children("tr"),o=s.children("."+R.scrollerHeader).children("table").children("thead").children("tr"),B.scroller.fixHeight(l,o),l=r.scroller_$footer.children("tfoot").children("tr"),o=s.children("."+R.scrollerFooter).children("table").children("tfoot").children("tr"),B.scroller.fixHeight(l,o),(B.scroller.isFirefox||B.scroller.isOldIE)&&(s=s.find("."+R.scrollerHack)),l=e.$table.children("tbody").children("tr"),o=s.children("."+R.scrollerTable).children("table").children("tbody").children("tr"),B.scroller.fixHeight(l,o)},removeFixed:function(e,r,l){var o=e.$table,s=r.scroller_$container,t=o.hasClass(R.scrollerRtl);(l||void 0===l)&&s.find("."+R.scrollerFixed).remove(),s.find("."+R.scrollerHideColumn).removeClass(R.scrollerHideColumn),s.children(":not(."+R.scrollerFixed+")").css(t?"right":"left",0)},remove:function(e,r){var l=r.scroller_$container,o=e.namespace+"tsscroller";e.$table.off(o),z(x).off(o),l&&(e.$table.insertBefore(l).find("thead").removeClass(R.scrollerHideElement).children("tr."+R.headerRow).children().attr("tabindex",0).end().find("."+R.filterRow).removeClass(R.scrollerHideElement+" "+R.filterRowHide),e.$table.find("."+R.filter).not("."+R.filterDisabled).prop("disabled",!1),l.remove(),e.isScrolling=!1)}}}(jQuery,window);return jQuery;}));
