﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI10IndexListViewModel
    {
        /// <summary>
        ///投票ID
        /// </summary>
        [DisplayName("投票ID")]
        public string QUESTIONNAIRE_ID { get; set; }
        public int QUESTIONNAIRE_ANSWER_Count { get; set; }
        /// <summary>
        ///投票標題
        /// </summary>
        [DisplayName("投票標題")]
        public string QUESTIONNAIRE_NAME { get; set; }

        /// <summary>
        ///投票敘述
        /// </summary>
        [DisplayName("投票敘述")]
        public string QUESTIONNAIRE_DESC { get; set; }

        /// <summary>
        ///投票開始日
        /// </summary>
        [DisplayName("投票開始日")]
       [ DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? QUESTIONNAIRE_SDATE { get; set; }

        /// <summary>
        ///投票結束日
        /// </summary>
        [DisplayName("投票結束日")]
       [ DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? QUESTIONNAIRE_EDATE { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public byte? STATUS { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日
        /// </summary>
        [DisplayName("修改日")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("建立人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///承辦人員全稱
        /// </summary>
        [DisplayName("承辦人員")]
        public string CRE_PERSON_NAME { get; set; }

        /// <summary>
        ///承辦人員簡稱
        /// </summary>
        [DisplayName("承辦人員")]
        public string CRE_PERSON_SNAME { get; set; }

        /// <summary>
        ///建立日
        /// </summary>
        [DisplayName("建立日")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///來源key=>學校代碼
        /// </summary>
        [DisplayName("來源key=>學校代碼")]
        public string SOU_KEY { get; set; }

        /// <summary>
        ///給予點數
        /// </summary>
        [DisplayName("酷幣點數")]
        public short CASH { get; set; }

        public int  ANSWERCount { get; set; }
        /// <summary>
        ///否開放看結果
        /// </summary>
        [DisplayName("否開放看結果")]
        public bool? RESULT { get; set; }
        public string RESULT_PERSON { get; set; }
        /// <summary>
        ///是否記名
        /// </summary>
        [DisplayName("是否記名")]
        public bool? REGISTERED_BALLOT { get; set; }

        [DisplayName("投票人數")]
        public int VOTE_COUNT { get; set; }

        public bool IsVote { get; set; }
        [DisplayName("此題可回答次數")]
        public int VoteUSERCount { get; set; }
        [DisplayName("指定對象")]
        public string ANSWER_PERSON_YN { get; set; }

        /// <summary>
        ///你是否是指定對象的帳號，大於0 是
        /// </summary>
        public int ANSWER_PERSON_COUNT { get; set; }
    }
}