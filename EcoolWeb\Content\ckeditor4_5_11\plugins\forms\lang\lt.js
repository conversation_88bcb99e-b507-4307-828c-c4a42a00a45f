﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'lt', {
	button: {
		title: 'My<PERSON><PERSON><PERSON> savy<PERSON>',
		text: '<PERSON><PERSON><PERSON> (Reikšmė)',
		type: 'Tipas',
		typeBtn: 'Mygtuka<PERSON>',
		typeSbm: '<PERSON>ųsti',
		typeRst: 'Išvalyti'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Žymimo<PERSON> langelio savyb<PERSON>',
		radioTitle: '<PERSON>ym<PERSON><PERSON><PERSON> akutė<PERSON> savybė<PERSON>',
		value: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
		selected: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Formos savybės',
		menu: 'Formos savybės',
		action: 'Veiksmas',
		method: 'Metodas',
		encoding: 'Kodavimas'
	},
	hidden: {
		title: 'Nerodomo lauko sa<PERSON>',
		name: '<PERSON><PERSON><PERSON>',
		value: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
	},
	select: {
		title: 'Atrank<PERSON> lauk<PERSON>',
		selectInfo: 'Informacija',
		opAvail: '<PERSON><PERSON><PERSON> parinkty<PERSON>',
		value: 'Reikšm<PERSON>',
		size: 'Dydis',
		lines: 'eilučių',
		chkMulti: 'Leisti daugeriopą atranką',
		required: 'Required', // MISSING
		opText: 'Tekstas',
		opValue: 'Reikšmė',
		btnAdd: 'Įtraukti',
		btnModify: 'Modifikuoti',
		btnUp: 'Aukštyn',
		btnDown: 'Žemyn',
		btnSetValue: 'Laikyti pažymėta reikšme',
		btnDelete: 'Trinti'
	},
	textarea: {
		title: 'Teksto srities savybės',
		cols: 'Ilgis',
		rows: 'Plotis'
	},
	textfield: {
		title: 'Teksto lauko savybės',
		name: 'Vardas',
		value: 'Reikšmė',
		charWidth: 'Ilgis simboliais',
		maxChars: 'Maksimalus simbolių skaičius',
		required: 'Required', // MISSING
		type: 'Tipas',
		typeText: 'Tekstas',
		typePass: 'Slaptažodis',
		typeEmail: 'El. paštas',
		typeSearch: 'Paieška',
		typeTel: 'Telefono numeris',
		typeUrl: 'Nuoroda'
	}
} );
