﻿@using ECOOL_APP.com.ecool.Models.entity;
@using System.Collections;
@model  IEnumerable<uADDT04Q02>
@{
    int iRowCount = 0,iHandleStatus=0;
    ViewBag.Title = "小小讀書人閱讀護照-獎狀處理清單";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<html>
<body>
    @using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
    {
        <table width="100%" border="0" cellpadding="0" cellspacing="00" bordercolorlight="#4E4636" bordercolordark="white" summary="*">
            <tr>
                <td align="center" style="white-space: nowrap;">
                    <span>【@Html.ActionLink("一年級", "Query3", "ADDI03", new { GRADE = 1 }, new { @style = "color:blue;" })】</span>
                    <span>【@Html.ActionLink("二年級", "Query3", "ADDI03", new { GRADE = 2 }, new { @style = "color:blue;" })】</span>
                    <span>【@Html.ActionLink("三年級", "Query3", "ADDI03", new { GRADE = 3 }, new { @style = "color:blue;" })】</span>
                    <span>【@Html.ActionLink("四年級", "Query3", "ADDI03", new { GRADE = 4 }, new { @style = "color:blue;" })】</span>
                    <span>【@Html.ActionLink("五年級", "Query3", "ADDI03", new { GRADE = 5 }, new { @style = "color:blue;" })】</span>
                    <span>【@Html.ActionLink("六年級", "Query3", "ADDI03", new { GRADE = 6 }, new { @style = "color:blue;" })】</span>
                    <span>【@Html.ActionLink("回閱讀護照", "rpp", "rpp", null, new { @style = "color:blue;" })】</span>
                </td>
            </tr>
            <tr>
                <td width="100%" align="center">
                    <span style="font-size:16pt">
                        護照閱讀書單
                    </span>
                </td>
            </tr>
            <tr>
                <td>
                    <table width="100px">
                        <tr>
                            <td>年級:</td>
                            <td>@Html.DropDownList("ddlGrade", (IEnumerable<SelectListItem>)ViewBag.GradeItem)</td>
                        </tr>
                    </table>

                </td>
            </tr>
        </table>
    <img src="~/Content/img/web-Bar-16.png">
        <table width="100%" border="1" cellpadding="0" cellspacing="00" bordercolorlight="#4E4636" bordercolordark="white" summary="*">
            <tr>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>班級</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>姓名</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>一年級認證</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>二年級認證</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>三年級認證</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>四年級認證</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>五年級認證</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>六年級認證</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>全部認證通過</font></td>
                <td width="10%" align="center" bgcolor="#D49976" style="white-space: nowrap;"><font size=-1>備註</font></td>
            </tr>
            @foreach (var item in Model)
        {
        iRowCount++;
                <tr>
                    <td align="center" style="white-space: nowrap;">@item.CLASS_NO</td>
                    <td align="center" style="white-space: nowrap;">@item.SONAME</td>
                    <td align="center" style="white-space: nowrap;"> @item.UserStatus_NO01</td>
                    <td align="center" style="white-space: nowrap;"> @item.UserStatus_NO02</td>
                    <td align="center" style="white-space: nowrap;"> @item.UserStatus_NO03</td>
                    <td align="center" style="white-space: nowrap;"> @item.UserStatus_NO04</td>
                    <td align="center" style="white-space: nowrap;"> @item.UserStatus_NO05</td>
                    <td align="center" style="white-space: nowrap;"> @item.UserStatus_NO06</td>
                    <td align="center" style="white-space: nowrap;">@item.FinishStatus</td>
                    <td align="center" style="white-space: nowrap;">
                        @if (item.FinishStatus == "Y" && item.CASH_YN!="Y")
                        {
                                    iHandleStatus++;
                                    string ZZZI06 = "ZZZI06_" +iRowCount;
                                    string hidSCHOOL_NO = "hidSCHOOL_NO_" +iRowCount;
                                    string hidUSER_NO = "hidUSER_NO_" +iRowCount;
                                    string hidGRADE = "hidGRADE_" +iRowCount;
                            <input id=@ZZZI06 name=@ZZZI06 type='checkbox' />
                            <input id=@hidSCHOOL_NO name=@hidSCHOOL_NO type='hidden' value=@item.SCHOOL_NO />
                            <input id=@hidUSER_NO name=@hidUSER_NO type='hidden' value=@item.USER_NO />
                            <input id=@hidGRADE name=@hidGRADE type='hidden' value=@item.GRADE />
                        }
                    </td>
                </tr>
        }
            @if (iHandleStatus >0)
                    {
                <tr>
                    <td align="center" style="white-space: nowrap;" rowspan="10">

                        <button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick();">@Resources.Resource.Send</button>
                        <input type="hidden" id="hidRowsCount" name="hidRowsCount" value=@iRowCount>

                    </td>
                </tr>
            }
        </table>
    }
</body>
</html>
@section Scripts
{
    <script type="text/javascript">
        $(function () {
            $("#ddlGrade").change(function () { ddlGrade_onchange(); });
        });
        function ddlGrade_onchange() {
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "Query";
            document.contentForm.submit();
        }

        function btnSend_onclick() {
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "../ZZZI06/ADDT04_EDIT";
            document.contentForm.submit();
        }
    </script>
}
@*<link href="~/Content/css/ui.jqgrid.css" rel="stylesheet" type="text/css" />
    @using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
    {
        <table id="list" class="scroll" cellpadding="0" cellspacing="0"></table>
        <div id="pager" class="scroll" style="text-align: center;"></div>
        <input type="hidden" id="hidRowsCount" name="hidRowsCount">
        <div><button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick();">@Resources.Resource.BookSuccess</button></div>
    }
    @section Scripts
        {
        <script src="~/Scripts/jquery-migrate-1.2.1.min.js" type="text/javascript"></script>
        <script src="~/Scripts/js/i18n/grid.locale-zh-tw.js" type="text/javascript"></script>
        <script src="~/Scripts/jquery.jqGrid.js" type="text/javascript"></script>
        <script type="text/javascript">

            $(function () {
                $("#list").jqGrid({
                    url: '../ZZZI06/GetZZZI06Data/',
                    datatype: 'json',
                    mtype: 'GET',
                    height: 'auto',
                    colNames: ['處理 ' + ' <input type=checkbox id="CheckAll">', '學年', '學期', '班級', '座號', '姓名', '認證等級', '升級日期'],//
                    colModel: [
                    //{ name: 'ZZZI06', index: 'ZZZI06', height: 60, width: 40, align: 'center', cellattr: bgcolorattr ,formatter: "checkbox", formatoptions: {disabled : true},},
                    //{ name: 'ZZZI06', index: 'ZZZI06', width: 50, resizable: true, editable: true, align: 'center', edittype: 'checkbox', formatter: "checkbox",
                    //formatoptions: { disabled: false }, editrules: { required: false }, editoptions: { size: 39, value: "True:False" }
                    //},
                    { name: 'ADDT04_NO', index: 'ADDT04_NO', height: 60, width: 60, align: 'center', formatter: CustomChk },
                    { name: 'SYEAR', index: 'SYEAR', height: 60, width: 40, align: 'center' },
                    { name: 'SEMESTER', index: 'SEMESTER', height: 60, width: 40, align: 'center' },
                    { name: 'CLASS_NO', index: 'CLASS_NO', height: 60, width: 40, align: 'center' },
                    { name: 'SEAT_NO', index: 'SEAT_NO', height: 60, width: 40, align: 'center' },
                    { name: 'SNAME', index: 'SNAME', height: 60, width: 40, align: 'center' },
                    { name: 'LEVEL_DESC', index: 'LEVEL_DESC', height: 60, width: 200, align: 'center' },
                    { name: 'PASS_DATE', index: 'PASS_DATE', height: 60, width: 100, align: 'left' }],
                    pager: jQuery('#pager'),
                    rowNum: 10,    // 由Server取回10筆
                    rowList: [5, 10, 20, 50],   // 每頁顯示筆數
                    sortname: 'CLASS_NO',
                    sortorder: "asc",
                    viewrecords: true,
                    imgpath: '/scripts/themes/coffee/images'
                    //caption: '閱讀認證待審查項目'
                });

            });




            function bgcolorattr(rowId, cellValue, rawobject, cm, rdata) {
                if (rowId % 2 == 1) {
                    $("tr.jqgrow:odd").css("background", "#EBEBC8");
                }
                else {
                    $("tr.jqgrow:even").css("background", "#FFFFEC");
                }
            }

            function btnSend_onclick()
            {
                //$("#list").find('input[type=checkbox]').each(function () {
                //    var colid = $(this).parents('tr:last').attr('id');
                //    if ($(this).is(':checked')) {
                //        alert(colid);
                //            $("#list").jqGrid('setSelection', colid);
                //            $(this).prop('checked', true);
                //        }

                //});
                document.contentForm.enctype = "multipart/form-data";
                document.contentForm.action = "../ZZZI06/QUERY";
                document.contentForm.submit();
            }

            function CustomChk(cellvalue, options, rowObject) {
                $("#hidRowsCount").val(options.rowId);
                return "<input id='ZZZI06_" + options.rowId + "' name='ZZZI06_" + options.rowId + "' type='checkbox' />" +
                       "<input id='hidSCHOOL_NO_" + options.rowId + "' name='hidSCHOOL_NO_" + options.rowId + "' type='hidden' value=" + rowObject[8] + " />" +
                       "<input id='hidUSER_NO_" + options.rowId + "' name='hidUSER_NO_" + options.rowId + "' type='hidden' value=" + rowObject[9] + " />" +
                       "<input id='hidGRADE_"+options.rowId+"' name='hidGRADE_"+options.rowId+"' type='hidden' value=" + rowObject[10] + " />"

            }

            function checkBox(obj) {
                $('.check').prop('checked', obj.checked);
            }
            $(document).ready(function () {
                $('input[id="CheckAll"]').click(function () {
                    var isChecked = $(this).prop('checked'); //先抓取CheckAll checked屬性的值
                    var tbl = $("#list");
                    tbl.each(function () {
                        $(this).find('tr').each(function () {
                            $(this).find('td').each(function () {
                                //CheckAll的屬性值是啥，其他checkbox的屬性值跟著CheckAll跑
                                $(this).find('input[type="checkbox"]').attr('checked', isChecked);
                            })
                        })
                    })
                })
            })
            ////chebkbox全選
            //$(document).ready(function () {
            //    $('input[id="CheckAll"]').click(function () {
            //        var isChecked = $(this).attr('checked'); //先抓取CheckAll checked屬性的值
            //        var tbl = $("#list");
            //        //var tbl = $('table[id ="table1"]');
            //        tbl.each(function () {
            //            $(this).find('tr').each(function () {
            //                $(this).find('td').each(function () {
            //                    //CheckAll的屬性值是啥，其他checkbox的屬性值跟著CheckAll跑
            //                    $(this).find('input[type="checkbox"]').attr('checked', isChecked);
            //                })
            //            })
            //        })
            //    })
            //})
        </script>
    }*@
