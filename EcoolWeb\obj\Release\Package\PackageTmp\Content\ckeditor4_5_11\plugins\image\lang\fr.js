﻿/*
Copyright (c) 2003-2016, CKSource <PERSON> <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'fr', {
	alt: 'Texte alternatif',
	border: 'Bordure',
	btnUpload: 'Envoyer sur le serveur',
	button2Img: 'Voulez-vous transformer le bouton avec image sélectionné en simple image ?',
	hSpace: 'Espacement horizontal',
	img2Button: 'Voulez-vous transformer l\'image sélectionnée en bouton avec image ?',
	infoTab: 'Informations sur l\'image',
	linkTab: 'Lien',
	lockRatio: 'Conserver les proportions',
	menu: 'Propriétés de l\'image',
	resetSize: 'Réinitialiser la taille',
	title: 'Propriétés de l\'image',
	titleButton: 'Propriétés du bouton avec image',
	upload: 'Téléverser',
	urlMissing: 'L\'URL source de l\'image est manquante.',
	vSpace: 'Espacement vertical',
	validateBorder: 'La bordure doit être un nombre entier.',
	validateHSpace: 'L\'espacement horizontal doit être un nombre entier.',
	validateVSpace: 'L\'espacement vertical doit être un nombre entier.'
} );
