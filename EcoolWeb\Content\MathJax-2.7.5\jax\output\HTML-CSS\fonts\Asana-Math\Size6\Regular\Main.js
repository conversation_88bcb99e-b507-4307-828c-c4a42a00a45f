/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Size6/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Size6={directory:"Size6/Regular",family:"AsanaMathJax_Size6",testString:"\uE000\uE001\uE002\uE003\uE004\uE005\uE006\uE007\uE008\uE009\uE00A\uE00B\uE00C\uE00D\uE00E",32:[0,0,249,0,0],124:[1960,1217,308,85,223],57344:[1428,887,272,86,187],57345:[587,-542,510,0,511],57346:[-130,175,510,0,511],57347:[-130,283,510,0,511],57348:[695,-542,510,0,511],57349:[1428,887,553,86,468],57350:[384,-100,375,48,314],57351:[700,-100,375,59,170],57352:[523,-100,375,59,319],57353:[384,-100,375,48,314],57354:[384,-100,375,62,328],57355:[700,-100,373,206,317],57356:[523,-100,373,57,317],57357:[384,-100,375,62,328],57358:[673,-636,484,0,485],57359:[832,-544,887,0,888],57360:[827,-712,687,0,688],57361:[833,-545,887,0,888],57362:[-200,237,484,0,485],57363:[486,-55,948,65,949],57364:[300,-241,834,0,770],57365:[428,172,524,233,292],57366:[300,-241,834,65,835],57367:[486,-55,948,0,884],57368:[486,-55,243,0,208],57369:[486,-55,243,36,244],57370:[494,-241,375,0,311],57371:[494,-241,375,65,376],57372:[537,-5,948,65,949],57373:[406,-134,638,0,639],57374:[406,-134,834,0,770],57375:[428,172,578,153,425],57376:[406,-134,834,65,835],57377:[537,-5,948,0,884],57378:[494,0,915,679,988],57379:[-340,628,887,0,888],57380:[-513,628,687,0,688],57381:[-345,633,887,0,888],57382:[877,-545,773,51,773],57383:[877,-741,688,0,688],57384:[877,-545,770,0,720],57385:[-545,877,773,51,773],57386:[-741,877,687,0,687],57387:[-545,877,770,0,720],57388:[1072,-741,758,0,758],57389:[-741,1072,758,0,758],57390:[486,-55,271,0,207],57391:[486,-55,271,65,272]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Size6"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size6/Regular/Main.js"]);
