﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'zh', {
	euro: '歐元符號',
	lsquo: '左單引號',
	rsquo: '右單引號',
	ldquo: '左雙引號',
	rdquo: '右雙引號',
	ndash: '短破折號',
	mdash: '長破折號',
	iexcl: '倒置的驚嘆號',
	cent: '美分符號',
	pound: '英鎊符號',
	curren: '貨幣符號',
	yen: '日圓符號',
	brvbar: '破折號',
	sect: '章節符號',
	uml: '分音符號',
	copy: '版權符號',
	ordf: '雌性符號',
	laquo: '左雙角括號',
	not: 'Not 符號',
	reg: '註冊商標符號',
	macr: '長音符號',
	deg: '度數符號',
	sup2: '上標字 2',
	sup3: '上標字 3',
	acute: '尖音符號',
	micro: '微',
	para: '段落符號',
	middot: '中間點',
	cedil: '字母 C 下面的尾型符號 ',
	sup1: '上標',
	ordm: '雄性符號',
	raquo: '右雙角括號',
	frac14: '四分之一符號',
	frac12: '二分之一符號',
	frac34: '四分之三符號',
	iquest: '倒置的問號',
	Agrave: '拉丁大寫字母 A 帶抑音符號',
	Aacute: '拉丁大寫字母 A 帶尖音符號',
	Acirc: '拉丁大寫字母 A 帶揚抑符',
	Atilde: '拉丁大寫字母 A 帶波浪號',
	Auml: '拉丁大寫字母 A 帶分音符號',
	Aring: '拉丁大寫字母 A 帶上圓圈',
	AElig: '拉丁大寫字母 Æ',
	Ccedil: '拉丁大寫字母 C 帶下尾符號',
	Egrave: '拉丁大寫字母 E 帶抑音符號',
	Eacute: '拉丁大寫字母 E 帶尖音符號',
	Ecirc: '拉丁大寫字母 E 帶揚抑符',
	Euml: '拉丁大寫字母 E 帶分音符號',
	Igrave: '拉丁大寫字母 I 帶抑音符號',
	Iacute: '拉丁大寫字母 I 帶尖音符號',
	Icirc: '拉丁大寫字母 I 帶揚抑符',
	Iuml: '拉丁大寫字母 I 帶分音符號',
	ETH: '拉丁大寫字母 Eth',
	Ntilde: '拉丁大寫字母 N 帶波浪號',
	Ograve: '拉丁大寫字母 O 帶抑音符號',
	Oacute: '拉丁大寫字母 O 帶尖音符號',
	Ocirc: '拉丁大寫字母 O 帶揚抑符',
	Otilde: '拉丁大寫字母 O 帶波浪號',
	Ouml: '拉丁大寫字母 O 帶分音符號',
	times: '乘號',
	Oslash: '拉丁大寫字母 O 帶粗線符號',
	Ugrave: '拉丁大寫字母 U 帶抑音符號',
	Uacute: '拉丁大寫字母 U 帶尖音符號',
	Ucirc: '拉丁大寫字母 U 帶揚抑符',
	Uuml: '拉丁大寫字母 U 帶分音符號',
	Yacute: '拉丁大寫字母 Y 帶尖音符號',
	THORN: '拉丁大寫字母 Thorn',
	szlig: '拉丁小寫字母 s',
	agrave: '拉丁小寫字母 a 帶抑音符號',
	aacute: '拉丁小寫字母 a 帶尖音符號',
	acirc: '拉丁小寫字母 a 帶揚抑符',
	atilde: '拉丁小寫字母 a 帶波浪號',
	auml: '拉丁小寫字母 a 帶分音符號',
	aring: '拉丁小寫字母 a 帶上圓圈',
	aelig: '拉丁小寫字母 æ',
	ccedil: '拉丁小寫字母 c 帶下尾符號',
	egrave: '拉丁小寫字母 e 帶抑音符號',
	eacute: '拉丁小寫字母 e 帶尖音符號',
	ecirc: '拉丁小寫字母 e 帶揚抑符',
	euml: '拉丁小寫字母 e 帶分音符號',
	igrave: '拉丁小寫字母 i 帶抑音符號',
	iacute: '拉丁小寫字母 i 帶尖音符號',
	icirc: '拉丁小寫字母 i 帶揚抑符',
	iuml: '拉丁小寫字母 i 帶分音符號',
	eth: '拉丁小寫字母 eth',
	ntilde: '拉丁小寫字母 n 帶波浪號',
	ograve: '拉丁小寫字母 o 帶抑音符號',
	oacute: '拉丁小寫字母 o 帶尖音符號',
	ocirc: '拉丁小寫字母 o 帶揚抑符',
	otilde: '拉丁小寫字母 o 帶波浪號',
	ouml: '拉丁小寫字母 o 帶分音符號',
	divide: '除號',
	oslash: '拉丁小寫字母 o 帶粗線符號',
	ugrave: '拉丁小寫字母 u 帶抑音符號',
	uacute: '拉丁小寫字母 u 帶尖音符號',
	ucirc: '拉丁小寫字母 u 帶揚抑符',
	uuml: '拉丁小寫字母 u 帶分音符號',
	yacute: '拉丁小寫字母 y 帶尖音符號',
	thorn: '拉丁小寫字母 thorn',
	yuml: '拉丁小寫字母 y 帶分音符號',
	OElig: '拉丁大寫字母 OE',
	oelig: '拉丁小寫字母 oe',
	'372': '拉丁大寫字母 W 帶揚抑符',
	'374': '拉丁大寫字母 Y 帶揚抑符',
	'373': '拉丁小寫字母 w 帶揚抑符',
	'375': '拉丁小寫字母 y 帶揚抑符',
	sbquo: '低 9 單引號',
	'8219': '高 9 反轉單引號',
	bdquo: '低 9 雙引號',
	hellip: '水平刪節號',
	trade: '商標符號',
	'9658': '黑色向右指箭號',
	bull: '項目符號',
	rarr: '向右箭號',
	rArr: '向右雙箭號',
	hArr: '左右雙箭號',
	diams: '黑鑽套裝',
	asymp: '約等於'
} );
