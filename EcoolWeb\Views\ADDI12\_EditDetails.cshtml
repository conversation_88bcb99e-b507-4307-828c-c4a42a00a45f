﻿@*
    // 暫無使用 => 改為用可排序的DataTables套件
*@

@model ADDI12EditPeopleViewModel

@using (Html.BeginCollectionItem("Details"))
{
    var Index = Html.GetIndex("Details");

    <div class="tr" id="Tr@(Index)">
        <div class="td" style="text-align:center">
            <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
            @Html.HiddenFor(m => m.STAGE_PERSON_ID)
            @Html.HiddenFor(m => m.STAGE_PERSON_ITEM)
            @Html.HiddenFor(m => m.STAGE_ID)
            @Html.HiddenFor(m => m.SCHOOL_NO)
            @Html.HiddenFor(m => m.SHORT_NAME)
            @Html.HiddenFor(m => m.USER_NO)
            @Html.HiddenFor(m => m.NAME)
            @Html.HiddenFor(m => m.GRADE)
            @Html.HiddenFor(m => m.CLASS_NO)
            @Html.HiddenFor(m => m.SEAT_NO)

        </div>
        <div class="td" style="text-align:center">
             @Model.SHORT_NAME
        </div>
        <div class="td" style="text-align:center">
            @Model.NAME
        </div>
        <div class="td" style="text-align:center">
            @Model.USER_NO
        </div>
        <div class="td" style="text-align:center">
            @Model.CLASS_NO
        </div>
        <div class="td" style="text-align:center">
            @Model.SEAT_NO
        </div>
    </div>
}


