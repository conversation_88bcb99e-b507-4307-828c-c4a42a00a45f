﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.util;
using Dapper;
using MvcPaging;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using System.Data;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using EcoolWeb.Util;
using EcoolWeb.CustomAttribute;
using EntityFramework.Extensions;
using NPOI.SS.UserModel;
using ECOOL_APP.com.ecool.service;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class SECI03Controller : Controller
    {
        protected string _ErrorRowCellExcel = string.Empty;

        /// <summary>
        /// 是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckErr = false;

        /// <summary>
        /// 格式是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckDataTypeErr = false;

        /// <summary>
        /// 必輸未輸 true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckMustErr = false;

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "SECI03";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities Db = new ECOOL_DEVEntities();
        // GET: SECI03

        private SECI03Service Service = new SECI03Service();

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg;

        /// <summary>
        /// 必輸欄位陣列
        /// </summary>
        private string[] MustArray;

        /// <summary>
        /// Excel標題
        /// </summary>
        private string[] TitleArray;

        private ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();

        [CheckPermission] //檢查權限
        public ActionResult Index(SECI03IndexViewModel model)
        {
            if (model == null) model = new SECI03IndexViewModel();

            string TitleStr = model.GetShowTypeName(model.whereShowType) + "一覽表";

            this.Shared(TitleStr);

            if (user != null)
            {
                if (user.USER_TYPE == UserType.Student)
                {
                    string IDNO = Db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Select(a => a.IDNO).FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(IDNO))
                    {
                        return RedirectToAction("Details", new { whereIDNO = IDNO });
                    }
                }
            }

            GetIndexData(model);

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(model.whereSCHOOL_NO, model.whereGrade, model.whereCLASS_NO, ref Db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            ViewBag.SEMESTERItems = HRMT01.GetSEMESTERItems(model.whereSEMESTER.ToString());
            ViewBag.SYEARItems = GetSYearsItems(model.whereSYEAR.ToString());

            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult PrintExcel(SECI03IndexViewModel model)
        {
            if (model == null) model = new SECI03IndexViewModel();

            string TitleStr = "學生健康資訊";

            this.Shared(TitleStr);

            model.PageSize = int.MaxValue;

            GetIndexData(model);

            DataTable DataTableExcel = new DataTable();
            string TempleteFileFullName = string.Empty;
            if (model.whereShowType == SECI03IndexViewModel.ShowTypeVal.Fitness)
            {
                DataTableExcel = model.Hrmt09List.ToList().AsDataTable();
                TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/HRMT09_ExportExcel.xlsx");
            }
            else
            {
                DataTableExcel = model.Hrmt08List.ToList().AsDataTable();
                TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/HRMT08_ExportExcel.xlsx");
            }

            NPOIHelper npoi = new NPOIHelper();

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, TitleStr, false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\" + TitleStr + "_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "" + TitleStr + ".xlsx");//輸出檔案給Client端
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Details(SECI03IndexViewModel model)
        {
            if (model.ShowOnMobile == "True")
            {
                ViewBag.isshowonMobil = "true";
            }
            UserProfile user = UserProfileHelper.Get();
            if (user != null && user.SCHOOL_NO != null)
            {
            }
            List<string> Hrm01SCHOOLList = new List<string>();
            if (user != null && user.SCHOOL_NO != "111111")
            {
                if (!string.IsNullOrWhiteSpace(model.whereSCHOOL_NO))
                {
                    model.myHRMT01 = Db.HRMT01.Where(a => a.IDNO == model.whereIDNO && a.SCHOOL_NO != "111111" && a.SCHOOL_NO == model.whereSCHOOL_NO).FirstOrDefault();
                }
                else {

                    model.myHRMT01 = Db.HRMT01.Where(a => a.IDNO == model.whereIDNO && a.SCHOOL_NO != "111111" && a.SCHOOL_NO==user.SCHOOL_NO).FirstOrDefault();
                }
              
            }
            else
            {
                model.myHRMT01 = Db.HRMT01.Where(a => a.IDNO == model.whereIDNO && a.SCHOOL_NO == "111111").FirstOrDefault();
            }

            // Hrm01SCHOOLList = Db.HRMT01.Where(a => a.IDNO == model.whereIDNO).Select(x => x.SCHOOL_NO).ToList();
            string TitleStr = model.myHRMT01.NAME + "-" + model.GetShowTypeName(model.whereShowType) + "明細";

            this.Shared(TitleStr);

            if (model.whereShowType != SECI03IndexViewModel.ShowTypeVal.Fitness)
            {
                IEnumerable<SECI03Hrmt08ListViewModel> QTemp = Service.GetMyHrmt08Data(model.whereIDNO, model.myHRMT01.SCHOOL_NO, ref Db);

                model.Hrmt08List = QTemp.ToPagedList(0, int.MaxValue);

                var NowHrmt08 = QTemp.OrderByDescending(a => a.SYEAR).ThenByDescending(a => a.SEMESTER).FirstOrDefault();
                if (NowHrmt08 != null)
                {
                    if (model.myHRMT01.SEX == "1")
                    {
                        model.TallMemo = string.Format("{0}目前的身高是{1}公分，當屆男生平均身高是{2}公分，歷屆男生平均身高是{3}公分。"
                                               , NowHrmt08.NAME, NowHrmt08.TALL.Value.ToString("#.#"), NowHrmt08.AVG_TALL_M.Value.ToString("#.#"), NowHrmt08.SUM_AVG_TALL_M.Value.ToString("#.#"));

                        model.WeightMemo = string.Format("{0}目前的體重是{1}公斤，當屆男生平均體重是{2}公斤，歷屆男生平均體重是{3}公斤。"
                                            , NowHrmt08.NAME, NowHrmt08.WEIGHT.Value.ToString("#.#"), NowHrmt08.AVG_WEIGHT_M.Value.ToString("#.#"), NowHrmt08.SUM_AVG_WEIGHT_M.Value.ToString("#.#"));
                    }
                    else
                    {
                        model.TallMemo = string.Format("{0}目前的身高是{1}公分，當屆女生平均身高是{2}公分，歷屆女生平均身高是{3}公分。"
                                             , NowHrmt08.NAME, NowHrmt08.TALL.Value.ToString("#.#"), NowHrmt08.AVG_TALL_W.Value.ToString("#.#"), NowHrmt08.SUM_AVG_TALL_W.Value.ToString("#.#"));

                        model.WeightMemo = string.Format("{0}目前的體重是{1}公斤，當屆女生平均體重是{2}公斤，歷屆女生平均體重是{3}公斤。"
                                         , NowHrmt08.NAME, NowHrmt08.WEIGHT.Value.ToString("#.#"), NowHrmt08.AVG_WEIGHT_W.Value.ToString("#.#"), NowHrmt08.SUM_AVG_WEIGHT_W.Value.ToString("#.#"));
                    }

                    model.VisionMemo = string.Format("{0}目前的視力右眼裸視{1}，左眼裸視{2}。", NowHrmt08.NAME, NowHrmt08.VISION_RIGHT, NowHrmt08.VISION_LEFT);
                }

                model.TALLchart = GetTALLchart(model.Hrmt08List, 600, 600, "TALLchart");
                model.WEIGHTchart = GetWEIGHTchart(model.Hrmt08List, 600, 600, "WEIGHTchart");

                if (model.whereShowType == SECI03IndexViewModel.ShowTypeVal.VISION)
                {
                    model.RIGHT_VISIONColumnChart = GetRIGHT_VISION_ColumnChart(model.whereIDNO);
                    model.LEFT_VISIONColumnChart = GetLEFT_VISION_ColumnChart(model.whereIDNO);
                }
            }
            else
            {
                IEnumerable<SECI03Hrmt09ListViewModel> QTemp = Service.GetMyHrmt09Data(model.whereIDNO, ref Db);
                if (ViewBag.isshowonMobil == "true")
                {
                    model.Hrmt09List = QTemp.Where(x => x.IDNO == model.whereIDNO && x.SCHOOL_NO == model.myHRMT01.SCHOOL_NO).ToPagedList(0, int.MaxValue);
                    model.FitnesschartV = GetFitnesschartV(model, 600, 600, "FitnesschartV");
                    model.FitnesschartSL = GetFitnesschartSL(model, 600, 600, "Fitnesschart");
                    model.FitnesschartSU = GetFitnesschartSU(model, 600, 600, "FitnesschartSU");
                    model.FitnesschartC = GetFitnesschartC(model, 600, 600, "FitnesschartC");
                }
                else
                {
                    model.Hrmt09List = QTemp.Where(x => x.IDNO == model.whereIDNO && x.SCHOOL_NO == model.myHRMT01.SCHOOL_NO).ToPagedList(0, int.MaxValue);
                    model.FitnesschartV = GetFitnesschartV(model, 600, 600, "FitnesschartV");
                    model.FitnesschartSL = GetFitnesschartSL(model, 600, 600, "Fitnesschart");
                    model.FitnesschartSU = GetFitnesschartSU(model, 600, 600, "FitnesschartSU");
                    model.FitnesschartC = GetFitnesschartC(model, 600, 600, "FitnesschartC");
                }
            }

            return View(model);
        }

        public ActionResult BMICal(SECI03BMICalViewModel model)
        {
            this.Shared("計算BMI");
            if (model == null) model = new SECI03BMICalViewModel();
            return View(model);
        }

        [HttpPost]
        public ActionResult BMICal2(SECI03BMICalViewModel model)
        {
            string BMI = "請填入正確數值";

            if (model.Height.HasValue && model.Weight.HasValue)
            {
                float h2 = (float)(model.Height.Value / 100);
                double fBMI = model.Weight.Value / Math.Pow(h2, 2);

                //體重過輕
                if (fBMI < 18.5)
                {
                    //(身高/100) X (身高/100) X 標準bmi最小值 = 標準體重

                    var AddWEIGHT = (((double)model.Height.Value / 100) * ((double)model.Height.Value / 100) * (double)18.5) - (double)model.Weight.Value;

                    BMI = $"你的BMI是{fBMI.ToString("###.0")}，你再增胖{AddWEIGHT.ToString("0.00")}公斤，體重就正常了喔！";
                }
                else if (fBMI > 24) //過重
                {
                    var AddWEIGHT = (((double)model.Height.Value / 100) * ((double)model.Height.Value / 100) * (double)24) - (double)model.Weight.Value;
                    BMI = $"你的BMI:{fBMI.ToString("###.0")};你再瘦{ Math.Abs(AddWEIGHT).ToString("0.00")}公斤，BMI就「會變成24那就會」是標準的喔,加油";
                }
                else
                {
                    BMI = $"你的BMI:{fBMI.ToString("###.0")};你的BMI是標準的喔,恭喜";
                }
            }

            var data = "{ \"BMI\" : \"" + BMI + "\" }";
            return Json(data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 身高
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetTALLchart(IPagedList<SECI03Hrmt08ListViewModel> Hrmt08List, int HightNum, int WidthNum, string HigechartName)
        {
            Highcharts chart = new Highcharts(HigechartName);

            if (Hrmt08List.Count() > 0)
            {
                object[] ArrTALL = new object[] { };
                object[] ArrAVG_TALL_M = new object[] { };
                object[] ArrAVG_TALL_W = new object[] { };
                object[] ArrSUM_AVG_TALL_M = new object[] { };
                object[] ArrSUM_AVG_TALL_W = new object[] { };
                string[] ArrGRADE = new string[] { };

                ArrTALL = (from a in Hrmt08List
                           select a.TALL).Cast<object>().ToArray();

                ArrAVG_TALL_M = (from a in Hrmt08List
                                 select a.AVG_TALL_M).Cast<object>().ToArray();

                ArrAVG_TALL_W = (from a in Hrmt08List
                                 select a.AVG_TALL_W).Cast<object>().ToArray();

                ArrSUM_AVG_TALL_M = (from a in Hrmt08List
                                     select a.SUM_AVG_TALL_M).Cast<object>().ToArray();

                ArrSUM_AVG_TALL_W = (from a in Hrmt08List
                                     select a.SUM_AVG_TALL_W).Cast<object>().ToArray();

                ArrGRADE = (from a in Hrmt08List
                            select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();
                if (HightNum > 0)
                {
                    chart
                    .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line, Height = HightNum, Width = WidthNum })
                    .SetTitle(new Title { Text = "身高趨勢圖" })
                    .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = ArrGRADE })
                    .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "身高(cm)" }, TickInterval = 1 })
                    .SetPlotOptions(new PlotOptions
                    {
                        //Line = new PlotOptionsLine
                        //{
                        //    DataLabels = new PlotOptionsLineDataLabels
                        //    {
                        //        Enabled = true
                        //    },
                        //    //EnableMouseTracking = false (是否移過去顯示)
                        //}
                    })
                    .SetTooltip(new Tooltip { ValueSuffix = "cm", Crosshairs = new Crosshairs(true), Shared = true })
                    .SetSeries(new Series[]
                                {
                                 new Series
                                 {
                                    Name =  "身高",
                                    Color = ECOOL_APP.HtmlHelper.HexColor("#434348"),
                                    Data = new DotNet.Highcharts.Helpers.Data(ArrTALL)
                                 },
                                 new Series
                                 { Name = "當屆平均(男)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_M)
                                 },
                                 new Series
                                 { Name = "當屆平均(女)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_W)
                                 },
                                 new Series
                                 {
                                   Name = "歷屆平均(男)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#E4D354") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_TALL_M)
                                 },
                                 new Series
                                 {
                                   Name = "歷屆平均(女)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_TALL_W)
                                 }
                                }
                    );

                    chartsHelper.SetCopyright(chart);
                }
                else
                {
                    chart
               .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
               .SetTitle(new Title { Text = "身高趨勢圖" })
               .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = ArrGRADE })
               .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "身高(cm)" }, TickInterval = 1 })
               .SetPlotOptions(new PlotOptions
               {
                   //Line = new PlotOptionsLine
                   //{
                   //    DataLabels = new PlotOptionsLineDataLabels
                   //    {
                   //        Enabled = true
                   //    },
                   //    //EnableMouseTracking = false (是否移過去顯示)
                   //}
               })
               .SetTooltip(new Tooltip { ValueSuffix = "cm", Crosshairs = new Crosshairs(true), Shared = true })
               .SetSeries(new Series[]
                           {
                                 new Series
                                 {
                                    Name =  "身高",
                                    Color = ECOOL_APP.HtmlHelper.HexColor("#434348"),
                                    Data = new DotNet.Highcharts.Helpers.Data(ArrTALL)
                                 },
                                 new Series
                                 { Name = "當屆平均(男)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_M)
                                 },
                                 new Series
                                 { Name = "當屆平均(女)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_W)
                                 },
                                 new Series
                                 {
                                   Name = "歷屆平均(男)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#E4D354") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_TALL_M)
                                 },
                                 new Series
                                 {
                                   Name = "歷屆平均(女)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_TALL_W)
                                 }
                           }
               );

                    chartsHelper.SetCopyright(chart);
                }
            }

            return chart;
        }

        /// <summary>
        /// 體重
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetWEIGHTchart(IPagedList<SECI03Hrmt08ListViewModel> Hrmt08List, int HightNum, int WidthNum, string HighchartsName)
        {
            try
            {
                Highcharts chart = new Highcharts(HighchartsName);

                if (Hrmt08List.Count() > 0)
                {
                    object[] ArrWEIGHT = new object[] { };
                    object[] ArrAVG_WEIGHT_M = new object[] { };
                    object[] ArrAVG_WEIGHT_W = new object[] { };
                    object[] ArrSUM_AVG_WEIGHT_M = new object[] { };
                    object[] ArrSUM_AVG_WEIGHT_W = new object[] { };
                    string[] ArrGRADE = new string[] { };

                    ArrWEIGHT = (from a in Hrmt08List
                                 select a.WEIGHT).Cast<object>().ToArray();

                    ArrAVG_WEIGHT_M = (from a in Hrmt08List
                                       select a.AVG_WEIGHT_M).Cast<object>().ToArray();

                    ArrAVG_WEIGHT_W = (from a in Hrmt08List
                                       select a.AVG_WEIGHT_W).Cast<object>().ToArray();

                    ArrSUM_AVG_WEIGHT_M = (from a in Hrmt08List
                                           select a.SUM_AVG_WEIGHT_M).Cast<object>().ToArray();

                    ArrSUM_AVG_WEIGHT_W = (from a in Hrmt08List
                                           select a.SUM_AVG_WEIGHT_W).Cast<object>().ToArray();

                    ArrGRADE = (from a in Hrmt08List
                                select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();
                    if (HightNum > 0)
                    {
                        chart
                        .InitChart(new DotNet.Highcharts.Options.Chart
                        {
                            DefaultSeriesType = ChartTypes.Line
                        ,
                            Height = HightNum,
                            Width = WidthNum
                        })
                        .SetTitle(new Title { Text = "體重趨勢圖" })
                        .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = ArrGRADE })
                        .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "體重(kg)" }, TickInterval = 1 })
                        .SetPlotOptions(new PlotOptions
                        {
                            //Line = new PlotOptionsLine
                            //{
                            //    DataLabels = new PlotOptionsLineDataLabels
                            //    {
                            //        Enabled = true
                            //    },
                            //    //EnableMouseTracking = false (是否移過去顯示)
                            //},
                        })
                        .SetTooltip(new Tooltip { ValueSuffix = "kg", Crosshairs = new Crosshairs(true), Shared = true })
                        .SetSeries(new Series[]
                                    {
                                 new Series
                                 { Name =  "體重",
                                    Color = ECOOL_APP.HtmlHelper.HexColor("#434348"),
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT)
                                 },
                                 new Series
                                 { Name = "全校平均(男)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_M)
                                 },
                                 new Series
                                 { Name = "全校平均(女)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_W)
                                 },
                                 new Series
                                 { Name = "歷屆平均(男)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#E4D354") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_WEIGHT_M)
                                 },
                                 new Series
                                 { Name = "歷屆平均(女)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_WEIGHT_W)
                                 }
                                    }
                        );
                        chartsHelper.SetCopyright(chart);
                    }
                    else
                    {
                        chart
                      .InitChart(new DotNet.Highcharts.Options.Chart
                      {
                          DefaultSeriesType = ChartTypes.Line
                      })
                      .SetTitle(new Title { Text = "體重趨勢圖" })
                      .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = ArrGRADE })
                      .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "體重(kg)" }, TickInterval = 1 })
                      .SetPlotOptions(new PlotOptions
                      {
                          //Line = new PlotOptionsLine
                          //{
                          //    DataLabels = new PlotOptionsLineDataLabels
                          //    {
                          //        Enabled = true
                          //    },
                          //    //EnableMouseTracking = false (是否移過去顯示)
                          //},
                      })
                      .SetTooltip(new Tooltip { ValueSuffix = "kg", Crosshairs = new Crosshairs(true), Shared = true })
                      .SetSeries(new Series[]
                                  {
                                 new Series
                                 { Name =  "體重",
                                    Color = ECOOL_APP.HtmlHelper.HexColor("#434348"),
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT)
                                 },
                                 new Series
                                 { Name = "全校平均(男)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_M)
                                 },
                                 new Series
                                 { Name = "全校平均(女)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_W)
                                 },
                                 new Series
                                 { Name = "歷屆平均(男)",
                                     Color = ECOOL_APP.HtmlHelper.HexColor("#E4D354") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_WEIGHT_M)
                                 },
                                 new Series
                                 { Name = "歷屆平均(女)",
                                   Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA") ,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSUM_AVG_WEIGHT_W)
                                 }
                                  }
                      );
                        chartsHelper.SetCopyright(chart);
                    }
                }

                return chart;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 視力 (RIGHT)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetRIGHT_VISION_ColumnChart(string IDNO)
        {
            string sSQL = @" SELECT m.SCHOOL_NO,m.SYEAR,m.SEMESTER,m.GRADE,m.GRADE_SEMESTER,m.Dscer,isnull(b.People,0) People
						    FROM (
							        SELECT a.SCHOOL_NO,a.SYEAR,a.SEMESTER,a.GRADE,GRADE_SEMESTER = a.GRADE + CASE WHEN a.SEMESTER = 1 Then '上' Else '下' END,d.Dscer
							        from HRMT08 a (nolock)
                                    cross join (select '0.8以下' Dscer
												union
												select '0.9~1.1' Dscer
												union
												select '1.2以上' Dscer) as d
							         where 1=1
							         and a.IDNO=@IDNO
							        GROUP BY a.SCHOOL_NO,a.SYEAR,a.SEMESTER,a.GRADE,d.Dscer
						        ) AS m
						    left outer join (
                                        Select s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER,
                                        case when CAST(s.VISION_RIGHT as float) <= 0.8 Then '0.8以下'
                                        when  CAST(s.VISION_RIGHT as float) >= 0.9 and CAST(s.VISION_RIGHT as float) <= 1.1 Then '0.9~1.1'
                                        when  CAST(s.VISION_RIGHT as float) >= 1.2 Then '1.2以上' End as Dscer
                                        , count(*) as People
                                        from HRMT08 s (NOLOCK)
                                        where  1=1 and isNumeric(s.VISION_RIGHT) =1 and isNumeric(s.VISION_LEFT) =1
                                        and isnull(s.VISION_RIGHT, '') <> '' and isnull(s.VISION_LEFT, '') <> ''
                                        group by s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
                                        , case when  CAST(s.VISION_RIGHT as float) <= 0.8 Then '0.8以下'
                                        when  CAST(s.VISION_RIGHT as float) >= 0.9 and CAST(s.VISION_RIGHT as float) <= 1.1 Then '0.9~1.1'
                                        when  CAST(s.VISION_RIGHT as float) >= 1.2 Then '1.2以上'
                                        End
						) as b on m.SCHOOL_NO = b.SCHOOL_NO and m.GRADE = b.GRADE and m.SYEAR = b.SYEAR and m.SEMESTER = b.SEMESTER  and m.Dscer = b.Dscer
						  where 1=1
						 ORDER BY m.SYEAR,m.SEMESTER,m.GRADE";

            var QTemp = Db.Database.Connection.Query<SECI03VisionDataViewModel>(sSQL
                , new
                {
                    IDNO = IDNO
                }).ToList();

            string[] ArrGRADE = new string[] { };
            ArrGRADE = (from a in QTemp
                        select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

            // var ArrGrade = Enum.GetValues(typeof(HRMT01.GradeVal)).Cast<byte>().Select(x => StringHelper.ConvertNumberToChineseNumber(x)).ToArray();

            var Arr_8 = QTemp.Where(a => a.Dscer == "0.8以下").Select(x => new Point { Y = x.People, Name = x.Dscer }).Cast<object>().ToArray();
            var Arr_9_11 = QTemp.Where(a => a.Dscer == "0.9~1.1").Select(x => new Point { Y = x.People, Name = x.Dscer }).Cast<object>().ToArray();
            var Arr_12 = QTemp.Where(a => a.Dscer == "1.2以上").Select(x => new Point { Y = x.People, Name = x.Dscer }).Cast<object>().ToArray();

            Highcharts TempPreColumnChart = new Highcharts("RIGHT_VISION_ColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
           })
            .SetTitle(new Title { Text = "視力分布狀況(右眼)" })
           .SetXAxis(new XAxis
           {
               Title = new XAxisTitle { Text = "年級" },
               Categories = ArrGRADE,
               Labels = new XAxisLabels
               {
                   Style = "fontSize: '11px'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "人數" }, Min = 0 })
           .SetSeries(new[]
                   {
                        new Series {
                            Name="0.8以下",
                            Data = new DotNet.Highcharts.Helpers.Data(Arr_8)
                        },
                         new Series {
                            Name="0.9~1.1",
                            Data = new DotNet.Highcharts.Helpers.Data(Arr_9_11)
                         }
                         ,
                         new Series {
                            Name="1.2以上",
                           Data = new DotNet.Highcharts.Helpers.Data(Arr_12)
                         }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 人</b>" });

            chartsHelper.SetCopyright(TempPreColumnChart);

            return TempPreColumnChart;
        }

        /// <summary>
        /// 視力 (LEFT)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetLEFT_VISION_ColumnChart(string IDNO)
        {
            string sSQL = @" SELECT m.SCHOOL_NO,m.SYEAR,m.SEMESTER,m.GRADE,m.GRADE_SEMESTER,m.Dscer,isnull(b.People,0) People
						    FROM (
							        SELECT a.SCHOOL_NO,a.SYEAR,a.SEMESTER,a.GRADE,GRADE_SEMESTER = a.GRADE + CASE WHEN a.SEMESTER = 1 Then '上' Else '下' END,d.Dscer
							        from HRMT08 a (nolock)
                                    cross join (select '0.8以下' Dscer
												union
												select '0.9~1.1' Dscer
												union
												select '1.2以上' Dscer) as d
							         where 1=1
							         and a.IDNO=@IDNO
							        GROUP BY a.SCHOOL_NO,a.SYEAR,a.SEMESTER,a.GRADE,d.Dscer
						        ) AS m
						    left outer join (
                                        Select s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER,
                                        case when CAST(s.VISION_LEFT as float) <= 0.8 Then '0.8以下'
                                        when  CAST(s.VISION_LEFT as float) >= 0.9 and CAST(s.VISION_LEFT as float) <= 1.1 Then '0.9~1.1'
                                        when  CAST(s.VISION_LEFT as float) >= 1.2 Then '1.2以上' End as Dscer
                                        , count(*) as People
                                        from HRMT08 s (NOLOCK)
                                        where 1=1 and isNumeric(s.VISION_RIGHT) =1 and isNumeric(s.VISION_LEFT) =1
                                        and isnull(s.VISION_RIGHT, '') <> ''and isnull(s.VISION_LEFT, '') <> ''
                                        group by s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
                                        , case when  CAST(s.VISION_LEFT as float) <= 0.8 Then '0.8以下'
                                        when  CAST(s.VISION_LEFT as float) >= 0.9 and CAST(s.VISION_LEFT as float) <= 1.1 Then '0.9~1.1'
                                        when  CAST(s.VISION_LEFT as float) >= 1.2 Then '1.2以上'
                                        End
						) as b on m.SCHOOL_NO = b.SCHOOL_NO and m.GRADE = b.GRADE and m.SYEAR = b.SYEAR and m.SEMESTER = b.SEMESTER  and m.Dscer = b.Dscer
						  where 1=1
						 ORDER BY m.SYEAR,m.SEMESTER,m.GRADE";

            var QTemp = Db.Database.Connection.Query<SECI03VisionDataViewModel>(sSQL
                , new
                {
                    IDNO = IDNO
                }).ToList();

            string[] ArrGRADE = new string[] { };
            ArrGRADE = (from a in QTemp
                        select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

            var Arr_8 = QTemp.Where(a => a.Dscer == "0.8以下").Select(x => new Point { Y = x.People, Name = x.Dscer }).Cast<object>().ToArray();
            var Arr_9_11 = QTemp.Where(a => a.Dscer == "0.9~1.1").Select(x => new Point { Y = x.People, Name = x.Dscer }).Cast<object>().ToArray();
            var Arr_12 = QTemp.Where(a => a.Dscer == "1.2以上").Select(x => new Point { Y = x.People, Name = x.Dscer }).Cast<object>().ToArray();

            Highcharts TempPreColumnChart = new Highcharts("LEFT_VISION_ColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
           })
            .SetTitle(new Title { Text = "視力分布狀況(左眼)" })
           .SetXAxis(new XAxis
           {
               Title = new XAxisTitle { Text = "年級" },
               Categories = ArrGRADE,
               Labels = new XAxisLabels
               {
                   Style = "fontSize: '11px'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "人數" }, Min = 0 })
           .SetSeries(new[]
                   {
                        new Series {
                            Name="0.8以下",
                            Data = new DotNet.Highcharts.Helpers.Data(Arr_8)
                        },
                         new Series {
                            Name="0.9~1.1",
                            Data = new DotNet.Highcharts.Helpers.Data(Arr_9_11)
                         }
                         ,
                         new Series {
                            Name="1.2以上",
                           Data = new DotNet.Highcharts.Helpers.Data(Arr_12)
                         }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 人</b>" });

            chartsHelper.SetCopyright(TempPreColumnChart);

            return TempPreColumnChart;
        }

        /// <summary>
        /// 坐姿體前彎
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetFitnesschartV(SECI03IndexViewModel model, int HightNum, int WidthNum, string FitnesschartName)
        {
            Highcharts chart = new Highcharts(FitnesschartName);

            if (model.Hrmt09List.Count() > 0)
            {
                object[] ArrV_SET_REACH_TEST = new object[] { };

                string[] ArrTDATE = new string[] { };

                ArrV_SET_REACH_TEST = (from a in model.Hrmt09List
                                       select a.V_SET_REACH_TEST).Cast<object>().ToArray();

                ArrTDATE = (from a in model.Hrmt09List
                            select a.SYEAR.ToString()).ToArray();
                if (HightNum > 0)
                {
                    chart
                    .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line, Height = HightNum, Width = WidthNum })
                    .SetTitle(new Title { Text = "體適能趨勢圖-坐姿體前彎" })
                    .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                    .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "次數" } })
                    .SetPlotOptions(new PlotOptions
                    {
                        Line = new PlotOptionsLine
                        {
                            DataLabels = new PlotOptionsLineDataLabels
                            {
                                Enabled = true
                            },
                            //EnableMouseTracking = false (是否移過去顯示)
                        }
                    })
                    .SetSeries(new Series[]
                                {
                                 new Series
                                 { Name =  "坐姿體前彎",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrV_SET_REACH_TEST)
                                 }
                                }
                    );

                    chartsHelper.SetCopyright(chart);
                }
                else
                {
                    chart
                    .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
                    .SetTitle(new Title { Text = "體適能趨勢圖-坐姿體前彎" })
                    .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                    .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "次數" } })
                    .SetPlotOptions(new PlotOptions
                    {
                        Line = new PlotOptionsLine
                        {
                            DataLabels = new PlotOptionsLineDataLabels
                            {
                                Enabled = true
                            },
                            //EnableMouseTracking = false (是否移過去顯示)
                        }
                    })
                    .SetSeries(new Series[]
                                {
                                 new Series
                                 { Name =  "坐姿體前彎",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrV_SET_REACH_TEST)
                                 }
                                }
                    );

                    chartsHelper.SetCopyright(chart);
                }
            }

            return chart;
        }

        /// <summary>
        /// 立定跳遠
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetFitnesschartSL(SECI03IndexViewModel model, int HightNum, int WidthNum, string FitnesschartName)
        {
            Highcharts chart = new Highcharts(FitnesschartName);

            if (model.Hrmt09List.Count() > 0)
            {
                object[] ArrS_L_JUMP_TEST = new object[] { };

                string[] ArrTDATE = new string[] { };

                ArrS_L_JUMP_TEST = (from a in model.Hrmt09List
                                    select a.S_L_JUMP_TEST).Cast<object>().ToArray();

                ArrTDATE = (from a in model.Hrmt09List
                            select a.SYEAR.ToString()).ToArray();
                if (HightNum > 0)
                {
                    chart
                    .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line, Height = HightNum, Width = WidthNum })
                    .SetTitle(new Title { Text = "體適能趨勢圖-立定跳遠" })
                    .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                    .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "公分" } })
                    .SetPlotOptions(new PlotOptions
                    {
                        Line = new PlotOptionsLine
                        {
                            DataLabels = new PlotOptionsLineDataLabels
                            {
                                Enabled = true
                            },
                            EnableMouseTracking = false
                        }
                    })
                    .SetSeries(new Series[]
                                {
                                 new Series
                                 { Name = "立定跳遠",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrS_L_JUMP_TEST)
                                 }
                                }
                    );

                    chartsHelper.SetCopyright(chart);
                }
                else
                {
                    chart
                 .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
                 .SetTitle(new Title { Text = "體適能趨勢圖-立定跳遠" })
                 .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                 .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "公分" } })
                 .SetPlotOptions(new PlotOptions
                 {
                     Line = new PlotOptionsLine
                     {
                         DataLabels = new PlotOptionsLineDataLabels
                         {
                             Enabled = true
                         },
                         EnableMouseTracking = false
                     }
                 })
                 .SetSeries(new Series[]
                             {
                                 new Series
                                 { Name = "立定跳遠",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrS_L_JUMP_TEST)
                                 }
                             }
                 );

                    chartsHelper.SetCopyright(chart);
                }
            }

            return chart;
        }

        /// <summary>
        /// 仰臥起坐
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetFitnesschartSU(SECI03IndexViewModel model, int HightNum, int WidthNum, string FitnesschartName)
        {
            Highcharts chart = new Highcharts(FitnesschartName);

            if (model.Hrmt09List.Count() > 0)
            {
                object[] ArrSIT_UPS_TEST = new object[] { };
                string[] ArrTDATE = new string[] { };

                ArrSIT_UPS_TEST = (from a in model.Hrmt09List
                                   select a.SIT_UPS_TEST).Cast<object>().ToArray();

                ArrTDATE = (from a in model.Hrmt09List
                            select a.SYEAR.ToString()).ToArray();
                if (HightNum > 0)
                {
                    chart
                    .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line, Height = HightNum, Width = WidthNum })
                    .SetTitle(new Title { Text = "體適能趨勢圖-仰臥起坐" })
                    .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                    .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "次數" } })
                    .SetPlotOptions(new PlotOptions
                    {
                        Line = new PlotOptionsLine
                        {
                            DataLabels = new PlotOptionsLineDataLabels
                            {
                                Enabled = true
                            },
                            //EnableMouseTracking = false (是否移過去顯示)
                        }
                    })
                    .SetSeries(new Series[]
                                {
                                 new Series
                                 { Name = "仰臥起坐",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSIT_UPS_TEST)
                                 }
                                }
                    );

                    chartsHelper.SetCopyright(chart);
                }
                else
                {
                    chart
                   .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
                   .SetTitle(new Title { Text = "體適能趨勢圖-仰臥起坐" })
                   .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                   .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "次數" } })
                   .SetPlotOptions(new PlotOptions
                   {
                       Line = new PlotOptionsLine
                       {
                           DataLabels = new PlotOptionsLineDataLabels
                           {
                               Enabled = true
                           },
                           //EnableMouseTracking = false (是否移過去顯示)
                       }
                   })
                   .SetSeries(new Series[]
                               {
                                 new Series
                                 { Name = "仰臥起坐",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSIT_UPS_TEST)
                                 }
                               }
                   );

                    chartsHelper.SetCopyright(chart);
                }
            }

            return chart;
        }

        /// <summary>
        /// 800公尺跑走
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Highcharts GetFitnesschartC(SECI03IndexViewModel model, int HightNum, int WidthNum, string FitnesschartName)
        {
            Highcharts chart = new Highcharts(FitnesschartName);

            if (model.Hrmt09List.Count() > 0)
            {
                object[] ArrC_P_F_TEST = new object[] { };
                string[] ArrTDATE = new string[] { };

                ArrC_P_F_TEST = (from a in model.Hrmt09List
                                 select a.C_P_F_TEST).Cast<object>().ToArray();

                ArrTDATE = (from a in model.Hrmt09List
                            select a.SYEAR.ToString()).ToArray();
                if (HightNum > 0)
                {
                    chart
                    .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line, Height = HightNum, Width = WidthNum })
                    .SetTitle(new Title { Text = "體適能趨勢圖-800公尺跑走" })
                    .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                    .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "分鐘" } })
                    .SetPlotOptions(new PlotOptions
                    {
                        Line = new PlotOptionsLine
                        {
                            DataLabels = new PlotOptionsLineDataLabels
                            {
                                Enabled = true
                            },
                            //EnableMouseTracking = false (是否移過去顯示)
                        }
                    })
                    .SetSeries(new Series[]
                                {
                                 new Series
                                 { Name = "800公尺跑走",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrC_P_F_TEST)
                                 }
                                }
                    );

                    chartsHelper.SetCopyright(chart);
                }
                else
                {
                    chart
                  .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
                  .SetTitle(new Title { Text = "體適能趨勢圖-800公尺跑走" })
                  .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "測驗日期" }, Categories = ArrTDATE })
                  .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "分鐘" } })
                  .SetPlotOptions(new PlotOptions
                  {
                      Line = new PlotOptionsLine
                      {
                          DataLabels = new PlotOptionsLineDataLabels
                          {
                              Enabled = true
                          },
                          //EnableMouseTracking = false (是否移過去顯示)
                      }
                  })
                  .SetSeries(new Series[]
                              {
                                 new Series
                                 { Name = "800公尺跑走",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrC_P_F_TEST)
                                 }
                              }
                  );

                    chartsHelper.SetCopyright(chart);
                }
            }

            return chart;
        }

        //標準標題
        public ActionResult DownloadExcel(string FilePath)
        {
            //讀成串流
            Stream iStream = new FileStream(FilePath, FileMode.Open, FileAccess.Read, FileShare.Read);

            //回傳出檔案
            return File(iStream, "application/vnd.ms-excel", System.IO.Path.GetFileName(FilePath));
        }

        //必輸欄位
        [CheckPermission(CheckACTION_ID = "ImportExcel")] //檢查權限
        public ActionResult ImportExcel(SECI03ImportExcelViewModel model)
        {
            if (model == null) model = new Models.SECI03ImportExcelViewModel();
            ImportViewShare(model);

            return View(model);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "ImportExcel")] //檢查權限
        public ActionResult ImportExcel(SECI03ImportExcelViewModel model, HttpPostedFileBase files)
        {
            ImportViewShare(model);

            ViewBag.BRE_NO = Bre_NO;
            //ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, string.Empty) + " - 請匯入Excel";

            if (files == null || files.FileName == string.Empty) ModelState.AddModelError("files", "請上傳檔案");
            else if (files != null)
            {
                Regex regexCode = new Regex(@".*\.(xls|xlsx)");

                string fileNameExtension = System.IO.Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳Excel格式為xls、xlsx");
                }
            }

            if (ModelState.IsValid == false)
            {
                ErrorMsg = "錯誤\r\n";
                return View();
            }
            else
            {
                var OK = this.ExcelData(model, files);
                if (OK)
                {
                    return RedirectToAction("Index", "SECI03");
                }
                else
                {
                    return View();
                }
            }
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        protected List<SelectListItem> GetSYearsItems(string defaultSelectValue)
        {
            List<SelectListItem> SYEARItems = new List<SelectListItem>();
            Byte MinYear = this.EntitiesDb.HRMT01.Where(a => a.USER_TYPE == UserType.Student && a.USER_STATUS == UserStaus.Enabled).Min(a => a.SYEAR).Value;
            Byte MaxYear = this.EntitiesDb.HRMT01.Where(a => a.USER_TYPE == UserType.Student && a.USER_STATUS == UserStaus.Enabled).Max(a => a.SYEAR).Value;
            for (Byte i = MinYear; i <= MaxYear; i++)
            {
                SYEARItems.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString(), Selected = i.ToString() == defaultSelectValue });
            }

            return SYEARItems;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Db.Dispose();
            }
            base.Dispose(disposing);
        }

        private void GetIndexData(SECI03IndexViewModel model)
        {
            if (string.IsNullOrWhiteSpace(model.whereSCHOOL_NO))
            {
                model.whereSCHOOL_NO = DefaultSCHOOL_NO;
            }

            if (model.whereSYEAR == null || model.whereSEMESTER == null)
            {
                //預設值
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                if (model.whereSEMESTER == null)
                {
                    model.whereSEMESTER = Convert.ToByte(Semesters);
                }

                if (model.whereSYEAR == null)
                {
                    model.whereSYEAR = Convert.ToByte(SYear);
                }
            }

            if (model.whereShowType != SECI03IndexViewModel.ShowTypeVal.Fitness)
            {
                var Temp = from a in Db.HRMT08
                           join h in Db.HRMT01 on new { a.IDNO } equals new { h.IDNO }
                           join s in Db.BDMT01 on new { h.SCHOOL_NO } equals new { s.SCHOOL_NO }
                           where (!UserStaus.NGUserStausList.Contains(h.USER_STATUS)) && h.USER_TYPE == "S"
                           && a.SCHOOL_NO == model.whereSCHOOL_NO
                           && a.SYEAR == model.whereSYEAR
                           && a.SEMESTER == model.whereSEMESTER
                           select new
                           {
                               HRMT08 = a,
                               HRMT01 = h,
                               BDMT01 = s
                           };

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    Temp = Temp.Where(a => a.HRMT01.USER_NO.Contains(model.whereKeyword.Trim()) || a.HRMT01.NAME.Contains(model.whereKeyword.Trim()));
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    Temp = Temp.Where(a => a.HRMT01.CLASS_NO == model.whereCLASS_NO.Trim());

                    model.PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    Temp = Temp.Where(a => a.HRMT01.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }

                if (user != null)
                {
                    if (user.USER_TYPE == UserType.Student)
                    {
                        Temp = Temp.Where(a => a.HRMT01.USER_NO == user.USER_NO);
                    }
                    else if (user.USER_TYPE == UserType.Parents)
                    {
                        var whereUserNoValueArr = HRMT06.GetArrMyPanyStudent(user, Db);
                        if (whereUserNoValueArr != null)
                        {
                            Temp = Temp.Where(a => whereUserNoValueArr.Contains(a.HRMT01.USER_NO));
                        }
                        else
                        {
                            Temp = Temp.Where(a => a.HRMT01.USER_NO == null);
                        }
                    }
                    else
                    {
                        var PermissionBtn = PermissionService.GetActionPermissionForBreNO("SECI03", user.SCHOOL_NO, user.USER_NO);
                        //判斷是否有[匯入Excel]權限
                        ViewBag.VisibleImportExcel = (PermissionBtn.Where(a => a.ActionName == "ImportExcel").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

                        if (user.TEACH_CLASS_NO != null)
                        {
                            var MyClass = Db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.CLASS_NO == user.TEACH_CLASS_NO).Select(a => a.USER_NO).ToList();

                            if (MyClass.Count() > 0)
                            {
                                Temp = Temp.Where(a => MyClass.Contains(a.HRMT01.USER_NO));
                            }
                            else
                            {
                                Temp = Temp.Where(a => a.HRMT01.USER_NO == null);
                            }
                        }
                        else
                        {
                            if (HRMT24_ENUM.CheckQAdmin(user) == false && ViewBag.VisibleImportExcel == "N")
                            {
                                Temp = Temp.Where(a => a.HRMT01.USER_NO == null);
                            }
                        }
                    }
                }

                Temp = from x in Temp
                       orderby x.HRMT08.SYEAR, x.HRMT08.SEMESTER, x.HRMT01.CLASS_NO, x.HRMT01.SEAT_NO
                       select x;

                var QTemp = Temp.Select(x => new SECI03Hrmt08ListViewModel
                {
                    IDNO = x.HRMT01.IDNO,
                    SCHOOL_NO = x.HRMT01.SCHOOL_NO,
                    SCHOOL_NAME = x.BDMT01.SHORT_NAME,
                    SYEAR = x.HRMT08.SYEAR,
                    SEMESTER = x.HRMT08.SEMESTER,
                    CLASS_NO = x.HRMT08.CLASS_NO,
                    SEAT_NO = x.HRMT08.SEAT_NO,
                    USER_NO = x.HRMT01.USER_NO,
                    NAME = x.HRMT01.SNAME,
                    SNAME = x.HRMT01.SNAME,
                    SEX = x.HRMT01.SEX,
                    TALL = x.HRMT08.TALL,
                    WEIGHT = x.HRMT08.WEIGHT,
                    POSTURE_MEMO = x.HRMT08.POSTURE_MEMO,
                    S_WEIGHT = x.HRMT08.S_WEIGHT,
                    O_WEIGHT = x.HRMT08.O_WEIGHT,
                    BMI = x.HRMT08.BMI,
                    S_BMI = x.HRMT08.S_BMI,
                    VISION_RIGHT = x.HRMT08.VISION_RIGHT,
                    VISION_LEFT = x.HRMT08.VISION_LEFT,
                    G_VISION_RIGHT = x.HRMT08.G_VISION_RIGHT,
                    G_VISION_LEFT = x.HRMT08.G_VISION_LEFT
                });

                model.Hrmt08List = QTemp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
            }
            else
            {
                var Temp = from a in Db.HRMT09
                           join h in Db.HRMT01 on new { IDNO = a.ID_NO } equals new { h.IDNO }
                           join s in Db.BDMT01 on new { h.SCHOOL_NO } equals new { s.SCHOOL_NO }
                           where a.SCHOOL_NO == model.whereSCHOOL_NO && h.USER_TYPE == "S"
                           && a.SYEAR == model.whereSYEAR
                           && (!UserStaus.NGUserStausList.Contains(h.USER_STATUS))
                           select new
                           {
                               HRMT09 = a,
                               HRMT01 = h,
                               BDMT01 = s
                           };

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    Temp = Temp.Where(a => a.HRMT01.USER_NO.Contains(model.whereKeyword.Trim()) || a.HRMT01.NAME.Contains(model.whereKeyword.Trim()));
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    Temp = Temp.Where(a => a.HRMT01.CLASS_NO == model.whereCLASS_NO.Trim());
                    model.PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    Temp = Temp.Where(a => a.HRMT01.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }

                if (user != null)
                {
                    if (user.USER_TYPE == UserType.Student)
                    {
                        Temp = Temp.Where(a => a.HRMT01.USER_NO == user.USER_NO);
                    }

                    if (user.USER_TYPE == UserType.Parents)
                    {
                        var whereUserNoValueArr = HRMT06.GetArrMyPanyStudent(user, Db);
                        Temp = Temp.Where(a => whereUserNoValueArr.Contains(a.HRMT01.USER_NO));
                    }
                    else
                    {
                        var PermissionBtn = PermissionService.GetActionPermissionForBreNO("SECI03", user.SCHOOL_NO, user.USER_NO);
                        //判斷是否有[匯入Excel]權限
                        ViewBag.VisibleImportExcel = (PermissionBtn.Where(a => a.ActionName == "ImportExcel").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

                        if (user.TEACH_CLASS_NO != null)
                        {
                            var MyClass = Db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.CLASS_NO == user.TEACH_CLASS_NO).Select(a => a.USER_NO).ToList();

                            if (MyClass.Count() > 0)
                            {
                                Temp = Temp.Where(a => MyClass.Contains(a.HRMT01.USER_NO));
                            }
                            else
                            {
                                Temp = Temp.Where(a => a.HRMT01.USER_NO == null);
                            }
                        }
                        else
                        {
                            if (HRMT24_ENUM.CheckQAdmin(user) == false && ViewBag.VisibleImportExcel == "N")
                            {
                                Temp = Temp.Where(a => a.HRMT01.USER_NO == null);
                            }
                        }
                    }
                }

                Temp = from x in Temp
                       orderby x.HRMT09.SYEAR, x.HRMT09.SEMESTER, x.HRMT01.CLASS_NO, x.HRMT01.SEAT_NO
                       select x;

                var QTemp = Temp.Select(x => new SECI03Hrmt09ListViewModel
                {
                    IDNO = x.HRMT01.IDNO,
                    SCHOOL_NO = x.HRMT01.SCHOOL_NO,
                    SCHOOL_NAME = x.BDMT01.SHORT_NAME,
                    SYEAR = x.HRMT09.SYEAR,
                    SEMESTER = x.HRMT09.SEMESTER,
                    TDATE = x.HRMT09.TDATE,
                    NAME = x.HRMT01.SNAME,
                    V_SET_REACH_TEST = x.HRMT09.V_SET_REACH_TEST,
                    S_L_JUMP_TEST = x.HRMT09.S_L_JUMP_TEST,
                    SIT_UPS_TEST = x.HRMT09.SIT_UPS_TEST,
                    C_P_F_TEST = x.HRMT09.C_P_F_TEST,
                });

                model.Hrmt09List = QTemp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
            }
        }

        #region Shared

        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        private void ImportViewShare(Models.SECI03ImportExcelViewModel model)
        {
            ViewBag.Panel_Title = "匯入健康資料";

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = user.SCHOOL_NO;

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.SYEAR)) model.SYEAR = SYear.ToString();
            if (string.IsNullOrWhiteSpace(model.SEMESTER)) model.SEMESTER = Semesters.ToString();

            ViewBag.SEMESTERItems = HRMT01.GetSEMESTERItems(model.SEMESTER.ToString());
            ViewBag.SYEARItems = GetSYearsItems(model.SYEAR.ToString());

            var logList = EntitiesDb.HRMT0809_LOG.Where(a => a.SCHOOL_NO == SchoolNO).OrderByDescending(a => a.EVENT_TIME); ;
            model.LogList = logList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 50);

            //model.SYEAR = SYear.ToString();
        }

        #region Excel 匯入處理

        /// <summary>
        /// Excel 匯入處理
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        private bool ExcelData(SECI03ImportExcelViewModel model, HttpPostedFileBase files)
        {
            bool ReturnBool = true;

            byte SYear = Convert.ToByte(model.SYEAR);
            byte Semester = Convert.ToByte(model.SEMESTER);

            HRMT0809_LOG aLog = EntitiesDb.HRMT0809_LOG.Create();
            aLog.EVENT_TIME = DateTime.Now;
            aLog.SYEAR = SYear;
            aLog.SEMESTER = Semester;
            aLog.IMPORT_TYPE = model.ImportType;

            UserProfile user = UserProfileHelper.Get();
            aLog.SCHOOL_NO = user.SCHOOL_NO;
            aLog.USER_NO = user.USER_NO;

            //建立上傳路徑
            string ExclePath = @"\SECI03Excel\" + aLog.SCHOOL_NO + @"\" + aLog.EVENT_TIME.ToString("yyyyMMdd_HH_mm_ss") + @"\";
            var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath;
            System.IO.Directory.CreateDirectory(tempPath);
            aLog.FILE_PATH = tempPath + System.IO.Path.GetFileName(files.FileName);
            files.SaveAs(aLog.FILE_PATH);

            NPOIHelper npoi = new NPOIHelper(); //NEW NPOIHelper
            npoi.AllowFirstSheet = true;

            npoi.onDataTypeConflict += new DataRowCellHandler(this.NPOI_DataTypeConflict); //宣告使用 輸入內容的型態 是否設定 及 Excel存儲格式一樣 ex. 日期 輸入[aaa] ，Excel存儲格式設日期，或欄位第一行有定義 System.DateTime
            npoi.onRowCheckValue += new DataRowCellHandler(this.NPOI_RowCheckValue); //宣告使用 NPOI_RowCheckValue (自訂檢查Value)

            TitleArray = new string[] { "USER_NO" }; //標題欄位
            MustArray = new string[] { "USER_NO" }; // Excel 必輸欄位

            string _Error;

            int c = 0;
            int r = 1;
            if (model.ImportType == "HRMT09")
            {
                c = 15;
                r = 16;
            }

            try
            {
                DataTable dt = npoi.Excel2Table(Server.MapPath(@"~\Content\ExcelSample\" + model.ImportType + "_MAP.xls"), files.InputStream, "Sheet1", c, r);

                if (dt == null)
                {
                    _Error = "上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                ///EXCEL內容資料型態數誤
                if (_CheckDataTypeErr || _CheckMustErr)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> " + _ErrorRowCellExcel;
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                if (model.ImportType == "HRMT08")
                    innerImport08(dt, aLog.SCHOOL_NO, SYear, Semester);
                else if (model.ImportType == "HRMT09")
                    innerImport09(dt, aLog.SCHOOL_NO, SYear, Semester);

                aLog.RowsCount = dt.Rows.Count;
                EntitiesDb.HRMT0809_LOG.Add(aLog);
                EntitiesDb.SaveChanges();
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException ex)
            {
                // Retrieve the error messages as a list of strings.
                var errorMessages = ex.EntityValidationErrors
                        .SelectMany(x => x.ValidationErrors)
                        .Select(x => x.ErrorMessage);

                // Join the list to a single string.
                var fullErrorMessage = string.Join("; ", errorMessages);

                // Combine the original exception message with the new one.
                var exceptionMessage = string.Concat(ex.Message, " The validation errors are: ", fullErrorMessage);
                _Error = "上傳錯誤，錯誤原因如下:<br><br>" + exceptionMessage;
                TempData["StatusMessage"] = _Error;
                ReturnBool = false;
                return ReturnBool;
            }
            catch (Exception ex)
            {
                _Error = "上傳錯誤，錯誤原因如下:<br><br>" + ex.Message;
                TempData["StatusMessage"] = _Error;
                ReturnBool = false;
                return ReturnBool;
            }

            return ReturnBool;
        }

        private void innerImport08(DataTable dt, string SchoolNO, byte SYear, byte Semester)
        {
            foreach (DataRow row in dt.Rows)
            {
                HRMT08 h8 = new HRMT08();
                foreach (DataColumn column in dt.Columns)
                {
                    System.Reflection.PropertyInfo pInfo = h8.GetType().GetProperty(column.ColumnName);
                    if (pInfo == null) continue;
                    pInfo.SetValue(h8, row[column.ColumnName] == DBNull.Value ? null : row[column.ColumnName], null);
                }

                h8.SYEAR = SYear;
                h8.SEMESTER = Semester;
                h8.SCHOOL_NO = SchoolNO;

                switch (h8.GRADE)
                {
                    case "一":
                        h8.GRADE = "1"; break;
                    case "二":
                        h8.GRADE = "2"; break;
                    case "三":
                        h8.GRADE = "3"; break;
                    case "四":
                        h8.GRADE = "4"; break;
                    case "五":
                        h8.GRADE = "5"; break;
                    case "六":
                        h8.GRADE = "6"; break;
                }

                if (string.IsNullOrWhiteSpace(h8.IDNO)) break;
                if (string.IsNullOrWhiteSpace(h8.USER_NO)) break;
                if (h8.IDNO.Contains("承辦")) break;

                EntitiesDb.HRMT08.Add(h8);
            }

            using (var dbContextTransaction = EntitiesDb.Database.BeginTransaction())
            {
                try
                {
                    EntitiesDb.HRMT08.Where(a => a.SCHOOL_NO == SchoolNO && a.SYEAR == SYear && a.SEMESTER == Semester).Delete();
                    EntitiesDb.SaveChanges();
                    dbContextTransaction.Commit();
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        private void innerImport09(DataTable dt, string SchoolNO, byte SYear, byte Semester)
        {
            byte theTYEAR = 0;
            byte TMONTH1 = 0;
            byte TMONTH2 = 0;
            DateTime now = DateTime.Now;
            UserProfile user = UserProfileHelper.Get();

            List<HRMT09> h9List = new List<HRMT09>();
            foreach (DataRow row in dt.Rows)
            {
                HRMT09 h9 = new HRMT09();
                foreach (DataColumn column in dt.Columns)
                {
                    System.Reflection.PropertyInfo pInfo = h9.GetType().GetProperty(column.ColumnName);
                    if (pInfo == null) continue;

                    byte g;
                    Decimal d;

                    if (column.ColumnName == "GRADE")
                    {
                        if (row[column.ColumnName] == DBNull.Value) row[column.ColumnName] = string.Empty;
                        if (row[column.ColumnName] == null) row[column.ColumnName] = string.Empty;
                        Byte.TryParse(row[column.ColumnName].ToString(), out g);
                        h9.GRADE = g;
                    }
                    else if (column.ColumnName == "V_SET_REACH_TEST" || column.ColumnName == "S_L_JUMP_TEST" || column.ColumnName == "SIT_UPS_TEST" || column.ColumnName == "C_P_F_TEST")
                    {
                        if (row[column.ColumnName] == DBNull.Value) row[column.ColumnName] = string.Empty;
                        if (row[column.ColumnName] == null) row[column.ColumnName] = string.Empty;
                        Decimal.TryParse(row[column.ColumnName].ToString(), out d);
                        pInfo.SetValue(h9, d, null);
                    }
                    else if (column.ColumnName == "TDATE")
                    {
                        pInfo.SetValue(h9, row[column.ColumnName] == DBNull.Value ? null : row[column.ColumnName], null);
                        if (string.IsNullOrWhiteSpace(h9.TDATE) == false)
                        {
                            byte TYEAR = 0, TMONTH = 0;
                            if (byte.TryParse(h9.TDATE.Substring(0, 3), out TYEAR)) h9.TYEAR = TYEAR;
                            if (byte.TryParse(h9.TDATE.Substring(3, 2), out TMONTH)) h9.TMONTH = TMONTH;
                            if (theTYEAR == 0 && TYEAR != 0) theTYEAR = TYEAR;
                            if (TMONTH1 == 0)
                                if (TMONTH != 0) TMONTH1 = TMONTH;
                                else if (TMONTH2 == 0)
                                    if (TMONTH != 0 && TMONTH != TMONTH1) TMONTH2 = TMONTH;
                        }
                    }
                    else
                    {
                        pInfo.SetValue(h9, row[column.ColumnName] == DBNull.Value ? null : row[column.ColumnName], null);
                    }
                }

                h9.SYEAR = SYear;
                h9.SEMESTER = Semester;
                h9.SCHOOL_NO = SchoolNO;
                h9.CRE_DATE = now;
                h9.CHG_PERSON = user.USER_NO;

                if (string.IsNullOrWhiteSpace(h9.ID_NO)) break;

                h9List.Add(h9);
            }

            using (var dbContextTransaction = EntitiesDb.Database.BeginTransaction())
            {
                try
                {
                    EntitiesDb.HRMT09.Where(a => a.SCHOOL_NO == SchoolNO && a.SYEAR == SYear && a.SEMESTER == Semester).Delete();
                    EntitiesDb.HRMT09.Where(a => a.SCHOOL_NO == SchoolNO && a.TYEAR == theTYEAR && a.TMONTH == TMONTH1).Delete();
                    EntitiesDb.HRMT09.Where(a => a.SCHOOL_NO == SchoolNO && a.TYEAR == theTYEAR && a.TMONTH == TMONTH2).Delete();

                    EntitiesDb.HRMT09.AddRange(h9List);

                    EntitiesDb.SaveChanges();
                    dbContextTransaction.Commit();
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        #endregion Excel 匯入處理

        #region 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_DataTypeConflict(object sender, DataRowCellFilledArgs e)
        {
            _CheckErr = true;
            _CheckDataTypeErr = true;
            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
        }

        #endregion 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        #region 作用:NPOI EXCEL 自訂檢查，顯示的錯誤訊息內容

        /// <summary>
        /// 自訂檢查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void NPOI_RowCheckValue(object sender, DataRowCellFilledArgs e)
        {
            if (MustArray.Any(s => e.ColName.Equals(s)) == true)
            {
                try
                {
                    if (e.CellToString == "" || e.CellToString == null || Convert.IsDBNull(e.CellToString))
                    {
                        _CheckErr = true;
                        _CheckMustErr = true;
                        _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        #endregion 作用:NPOI EXCEL 自訂檢查，顯示的錯誤訊息內容
    }
}