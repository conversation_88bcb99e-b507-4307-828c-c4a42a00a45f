/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Bold/PrivateUse.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXNonUnicode-bold"],{57344:[610,25,1184,808,912],57345:[704,-75,1198,808,1224],57358:[819,339,750,80,670],57359:[742,235,750,80,670],57360:[742,235,750,80,670],57361:[819,339,750,80,670],57379:[742,235,750,68,683],57381:[852,345,750,67,683],57384:[672,166,1000,38,961],57385:[672,166,1000,38,961],57399:[672,166,750,67,682],57421:[553,47,750,68,683],57424:[672,166,750,87,663],57425:[672,166,750,87,663],57426:[574,69,750,68,683],57435:[574,-16,750,68,683],57436:[553,31,750,68,683],57438:[762,-565,0,95,425],57441:[-137,437,0,0,330],57442:[-137,552,0,0,330],57444:[837,-565,333,-16,349],57445:[-137,409,0,-16,349],57446:[801,-565,0,91,430],57447:[-137,409,0,-16,349],57453:[835,113,750,82,668],57454:[835,113,750,82,668],57455:[835,113,750,82,668],57456:[835,113,750,82,668],57470:[738,230,750,80,670],57471:[742,234,750,80,670],57472:[819,337,750,80,670],57473:[820,342,750,91,681],57474:[742,235,750,80,670],57475:[742,234,750,80,670],57476:[738,230,750,80,670],57477:[742,234,750,80,670],57520:[752,-531,0,100,417],57521:[-50,271,0,100,417],57522:[-50,271,0,99,416],57523:[691,203,556,14,487],57524:[555,-209,282,42,239],57525:[555,-209,282,43,240],57526:[478,-56,0,15,142],57560:[688,13,400,57,343],57561:[663,0,314,54,260],57562:[663,0,425,54,371],57565:[930,0,553,76,483],57566:[926,0,549,67,482],57567:[765,0,773,67,706],57568:[920,0,552,42,510],57569:[765,0,378,55,323],57570:[754,0,481,63,435],57611:[297,-209,315,0,315],57614:[405,-101,714,211,503],57615:[399,-107,315,0,315],57680:[175,302,735,-40,756],57681:[175,302,735,-21,775],57682:[477,0,735,-40,756],57683:[477,0,735,-21,775],57955:[422,10,523,26,496],57959:[425,0,523,111,420],57963:[421,0,523,53,470],57967:[424,198,523,31,478],57971:[420,198,523,42,496],57975:[421,198,523,49,474],57979:[614,8,523,21,502],57983:[421,198,523,8,507],57987:[606,12,523,31,493],57991:[421,202,523,25,499],57997:[734,-484,0,92,498],57999:[175,0,325,-1,326],58000:[175,0,633,-1,634],58109:[775,235,722,9,689],58111:[775,235,667,16,619],58113:[775,207,620,16,593],58115:[775,207,722,33,673],58117:[775,235,667,16,641],58119:[775,235,667,28,634],58121:[775,235,778,21,759],58123:[775,207,778,35,743],58125:[775,235,389,-36,436],58127:[775,235,778,30,769],58129:[775,207,707,9,674],58131:[775,235,944,14,921],58133:[775,235,722,16,701],58135:[775,207,647,40,607],58137:[775,235,778,35,743],58139:[775,207,778,21,759],58141:[775,235,611,16,600],58143:[775,207,671,28,641],58145:[775,235,667,31,636],58147:[775,207,723,14,700],58149:[775,207,836,18,818],58151:[775,235,722,16,699],58153:[775,207,804,11,793],58155:[775,207,768,28,740],58213:[775,235,669,32,665],58217:[775,235,667,-13,670],58221:[793,235,757,-49,758],58225:[775,235,734,27,710],58236:[775,235,667,16,641],58295:[681,11,525,40,482],58296:[681,0,525,90,450],58297:[681,0,525,52,470],58298:[681,11,525,43,479],58299:[682,0,525,29,493],58300:[670,11,525,52,470],58301:[681,11,525,43,479],58302:[686,11,525,43,479],58303:[681,11,525,43,479],58304:[681,11,525,43,479],58307:[747,243,750,68,683],58308:[747,243,750,68,683]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/NonUnicode/Bold/PrivateUse.js");
