/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Script/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Script-italic"]={directory:"Script/Italic",family:"STIXMathJax_Script",style:"italic",testString:"\u00A0\u210A\u210B\u2110\u2112\u211B\u212C\u212F\u2130\u2131\u2133\u2134\uD835\uDC9C\uD835\uDC9E\uD835\uDC9F",32:[0,0,250,0,0],160:[0,0,250,0,0],8458:[441,219,738,30,678],8459:[687,15,997,53,991],8464:[675,15,897,26,888],8466:[687,15,946,33,931],8475:[687,15,944,34,876],8492:[687,15,950,34,902],8495:[441,11,627,30,554],8496:[687,15,750,100,734],8497:[680,0,919,43,907],8499:[674,15,1072,38,1056],8500:[441,11,697,30,680],119964:[674,15,855,31,846],119966:[687,15,797,37,781],119967:[687,15,885,36,818],119970:[687,15,773,83,740],119973:[674,177,802,9,792],119974:[687,15,1009,40,1004],119977:[687,15,970,38,956],119978:[680,15,692,82,663],119979:[687,15,910,38,886],119980:[680,38,692,82,663],119982:[680,15,743,67,701],119983:[687,15,912,43,907],119984:[687,15,842,36,805],119985:[687,15,932,35,922],119986:[687,15,1078,35,1070],119987:[687,15,891,36,873],119988:[687,226,926,91,916],119989:[687,15,932,59,912],119990:[441,11,819,30,758],119991:[687,12,580,47,559],119992:[441,11,662,30,589],119993:[687,11,845,30,827],119995:[687,209,685,27,673],119997:[687,11,753,38,690],119998:[653,11,496,83,484],119999:[653,219,730,9,718],120000:[687,11,726,40,666],120001:[687,11,579,48,571],120002:[441,11,1038,49,978],120003:[441,11,761,49,701],120005:[441,209,773,23,694],120006:[441,209,780,30,743],120007:[444,0,580,48,572],120008:[531,11,515,62,412],120009:[658,11,551,30,532],120010:[424,11,753,30,693],120011:[441,11,618,30,582],120012:[441,11,888,30,852],120013:[441,11,752,65,675],120014:[424,219,658,30,617],120015:[478,11,691,52,617]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Script-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Italic/Main.js"]);
