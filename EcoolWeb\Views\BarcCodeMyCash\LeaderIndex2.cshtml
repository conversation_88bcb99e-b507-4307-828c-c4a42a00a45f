﻿@model BarcCodeMyCashIndexViewModel
@{


    ECOOL_APP.UserProfile user = new ECOOL_APP.UserProfile();

    Layout = "~/Views/Shared/_LayoutSleepATM.cshtml";
    List<string> RankStr = new List<string>();
    RankStr.Add(" number-first");
    RankStr.Add(" number-second");
    RankStr.Add(" number-third");

}
@*<link href="~/Content/css/bootstrap.css" rel="stylesheet">
    <link href="~/Content/styles/leaderboard.css" rel="stylesheet" />*@
<body class="leaderboard-page-bg-cold"  onclick="openImg()">

    <h1 class="text-hide">閱讀和運動排行榜</h1>

    <div class="container-fluid leaderboard-page-layout">

     
        <div class="row">


            @Html.Action("_BookQtyRank", (string)ViewBag.BRE_NO)




            @Html.Action("_RunRank", (string)ViewBag.BRE_NO)

        </div>


    </div>

</body>
<script>
    var box = document.querySelector('.leaderboard-page-layout');
    box.addEventListener('mousemove', touch, false);
    function openImg() {
  
        window.location.href =     "@Url.Action(ViewBag.FROMACTION, "BarcCodeMyCash")?WhereSchoolNo=" + "@Model.WhereSchoolNo" + "&FROMACTION=" + "@ViewBag.FROMACTION" + "&TimeoutSeconds=" + "@ViewBag.TimeoutSeconds";


    }
    function touch() {

           window.location.href =     "@Url.Action(ViewBag.FROMACTION, "BarcCodeMyCash")?WhereSchoolNo=" + "@Model.WhereSchoolNo" + "&FROMACTION=" + "@ViewBag.FROMACTION" + "&TimeoutSeconds=" + "@ViewBag.TimeoutSeconds";

    }
</script>