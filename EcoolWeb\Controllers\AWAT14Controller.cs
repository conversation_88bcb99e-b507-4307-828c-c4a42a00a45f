﻿using EcoolWeb.Models;
using ECOOL_APP.com.ecool.Models.entity;
using com.ecool.service;
using System.Collections;
using System.Data.Entity.Validation;
using System.Data.Entity;
using EcoolWeb.CustomAttribute;
using ECOOL_APP.com.ecool.service;
using Dapper;
using System.Data;
using System.Data.SqlClient;
using com.ecool.sqlConnection;
using System.Text;
using System.Web.Mvc;
using ECOOL_APP.EF;
using ECOOL_APP;
using ECOOL_APP.com.ecool.ResultService;
using EcoolWeb.ViewModels;
using System.Linq;
using ECOOL_APP.com.ecool.Models;
using System.Data.Entity.SqlServer;
using MvcPaging;
using ECOOL_APP.com.ecool.Models.DTO.AWAT14;
using System.Collections.Generic;
using System;
using ECOOL_APP.com.ecool.util;
using System.IO;
using NPOI.SS.UserModel;

namespace EcoolWeb.Controllers
{
    public class AWAT14Controller : Controller
    {
        AWAT14Service aWAT14Service = new AWAT14Service();
        private UserProfile user = UserProfileHelper.Get();
        

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        /// </summary>
        private static string Bre_NO = "AWAT14";

        private string Bre_Name = "酷幣點數升級名單";
        public ActionResult Index()
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            AWAT14SysSetIntervalViewModel model = new AWAT14SysSetIntervalViewModel();
            UserProfile user = UserProfileHelper.Get();
            model.SCHOOL_NO = user.SCHOOL_NO;
            BDMT01 bDMT01 = new BDMT01();
            bDMT01 = aWAT14Service.GetSchooLSingel(model);
            model.AWAT14_CASH = (int)bDMT01.AWAT14_CASH;
            model.SCHOOL_NO = user.SCHOOL_NO;
            return View(model);
        }
        public ActionResult SysSetinterval() {
            AWAT14SysSetIntervalViewModel model = new AWAT14SysSetIntervalViewModel();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            UserProfile user = UserProfileHelper.Get();
            BDMT01 bDMT01 = new BDMT01();
            bDMT01 = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();

            model.SCHOOL_NO = bDMT01.SCHOOL_NO;
            model.AWAT14_CASH = (int)bDMT01.AWAT14_CASH;
            return View(model);

        }

        [HttpPost]
        public ActionResult Modify(AWA15ModifyQueryViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            
                foreach (var item in model.Chk.Where(a => a.CheckBoxNo == true).ToList())
                {
                    var AWAT15 = db.AWAT15.Where(p => p.AWAT15NO == item.AWAT15.AWAT15NO).FirstOrDefault();
                    if (AWAT15 != null)
                    {
                        AWAT15.IsReMark = (byte)1;
                        AWAT15.GOT_DATE = DateTime.Now.Date;
                        db.Entry(AWAT15).State = System.Data.Entity.EntityState.Modified;
                        db.SaveChanges();
                    }
                }
          
            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);

            TempData["StatusMessage"] = "異動完成";

            return RedirectToAction("ModifyQuery", "AWAT14", new { model.RoleName });
        }
        public ActionResult PrintSetView(AWAT15QueryViewModel model)
        {
            return View(model);
        }
        public ActionResult _TrPrintSetView(AWAT15QueryViewModel Item)
        {
            return View(Item);
        }
        public ActionResult PrintModify(AWAT15QueryViewModel model)
        {
            return View(model);
        }
        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
                ViewBag.Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
                ViewBag.Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }
        public ActionResult _PageRunDetailLog(string WhereSCHOOL_NO, string WhereRANKNO, string OrdercColumn, string SyntaxName) {
            AWAT14Service aWAT14Service = new AWAT14Service();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            UserProfile user = UserProfileHelper.Get();
            AWAT14RankViewModel aWAT14RankViewModel = new AWAT14RankViewModel();
            if (!string.IsNullOrEmpty(WhereSCHOOL_NO))
            {
                aWAT14RankViewModel.WhereSCHOOL_NO = WhereSCHOOL_NO;
            }
            if (!string.IsNullOrEmpty(WhereRANKNO))
            {
                aWAT14RankViewModel.WhereRankNO = WhereRANKNO;
            }
            if (!string.IsNullOrEmpty(OrdercColumn))
            {

                aWAT14RankViewModel.OrdercColumn = OrdercColumn;
                aWAT14RankViewModel.SyntaxName = SyntaxName;
            }
           AWAT14RankCount aWAT14RankCounts = new AWAT14RankCount();
            aWAT14RankCounts = aWAT14Service.AWAT14RankCountDetail(aWAT14RankViewModel, ref db);
            //model.RankCount = aWAT14RankCounts;
            aWAT14RankCounts.WhereRANKNO = WhereRANKNO;
            return PartialView(aWAT14RankCounts);
        }
        public ActionResult ExportExcel(AWAT15QueryViewModel model)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            UserProfile user = UserProfileHelper.Get();
            model.PageSize = int.MaxValue;
            model.Page = 1;
            string USER_Tyep = "";
            DataTable DataTableExcel = null;
            List<VAWAT15> aWAT15s = new List<VAWAT15>();
            List<int> TRANS_NOlist = new List<int>();
            aWAT15s = model.Chk.Where(x=>x.CheckBoxNo ==true).Select(x=>x.AWAT15).ToList();
            TRANS_NOlist = aWAT15s.Select(x => x.AWAT15NO).ToList();
            aWAT15s = aWAT15s.Where(x => TRANS_NOlist.Contains(x.AWAT15NO)).ToList();
            //狀況
            if (string.IsNullOrWhiteSpace(model.whereSTATUS) == false)
            {


                byte TRANS_STATUS = Convert.ToByte(model.whereSTATUS);
                //byte TRANS_STATUS = 0;
                //if (model.whereSTATUS == "取消")
                //{
                //    TRANS_STATUS = (byte)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_Del;
                //}
                //else if (model.whereSTATUS == "已升級未領")
                //{
                //    TRANS_STATUS = (byte)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_UN;

                //}
                //else
                //{
                //    TRANS_STATUS = (byte)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_Receive;
                //}
                aWAT15s = aWAT15s.Where(a => a.IsReMark == TRANS_STATUS).ToList();
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                var arrUSER_NO = model.whereUserNo.Split(',');
                aWAT15s = aWAT15s.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null).ToList();
            }
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                int AWARD_NO;

                if (Int32.TryParse(model.whereKeyword, out AWARD_NO))
                {
                    aWAT15s = aWAT15s.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                               || a.NAME.Contains(model.whereKeyword.Trim())
                                           
                                               || a.AWAT15NO == AWARD_NO
                                               ).ToList();
                }
                else
                {
                    aWAT15s = aWAT15s.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                                    || a.NAME.Contains(model.whereKeyword.Trim())
                                                    
                                               ).ToList();
                }
            }
            if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
                {
                    aWAT15s = aWAT15s.Where(a => a.NAME.Contains(model.whereSNAME)).ToList();
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    aWAT15s = aWAT15s.Where(a => a.CLASS_NO == model.whereCLASS_NO).ToList();
                }

                if (string.IsNullOrWhiteSpace(model.whereAWAT15NO) == false)
                {
                    int an = 0;
                    int.TryParse(model.whereAWAT15NO, out an);
                    aWAT15s = aWAT15s.Where(a => a.AWAT15NO == an).ToList();
                }
                //  -- 排序開始 --
                Func<VAWAT15, object> sortExpression;
                switch (model.OrderColumn)
                {


                    case "SYEAR":
                        sortExpression = (q => q.SYEAR);
                        break;

                    case "CLASS_NO":
                        sortExpression = (q => q.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        sortExpression = (q => q.SEAT_NO);
                        break;

                    case "CASH_Rank":
                        sortExpression = (q => q.CASH_Rank);
                        break;



                    case "CreatDate":
                        sortExpression = (q => q.CreatDate);
                        break;

                    default:
                        sortExpression = (q => q.AWAT15NO);
                        break;
                }
                if (model.SortBy == "ASC")
                {
                    aWAT15s = aWAT15s.OrderBy(sortExpression).ToList();
                }
                else
                {
                    aWAT15s = aWAT15s.OrderByDescending(sortExpression).ToList();
                }
                var DataList = model.Chk?.Where(a => a.CheckBoxNo == true).Select(a => a.AWAT15).ToList();
            string SHORT_NAME = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.SHORT_NAME).FirstOrDefault();

            var VAWAT15LIsy = (from x in aWAT15s
                               select new VAWAT15()
                               {

                                   CASH_Rank = x.CASH_Rank,
                                   SYEAR = x.SYEAR,
                                   SEMESTER = x.SEMESTER,
                                   CLASS_NO = x.CLASS_NO,
                                   SEAT_NO = x.SEAT_NO,
                                   SNAME = x.SNAME,
                                   IsReMarkDesc = (x.IsReMark.HasValue ? HRMT01.ParseAWAT15(0) : HRMT01.ParseAWAT15((int)x.IsReMark)),
                                   IsReMark = (int)x.IsReMark,
                                   CreatDate = x.CreatDate,
                                   NAME = x.NAME,
                                   SHORT_NAME= SHORT_NAME

                               }
                     ).ToList();
                DataTableExcel = VAWAT15LIsy?.AsDataTable();
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/AWAT14RANKExportExcel.xlsx");
            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);
            if (DataTableExcel != null)
            {


                npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "升級名單", false, 2);
            }
            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }
            strTMPFile = strTMPFile + @"\酷幣點數升級名單_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "獎品兌換清單.xlsx");//輸出檔案給Client端
        }
        public ActionResult ModifyQuery(AWA15ModifyQueryViewModel model)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            ModelState.Clear();
            if (model == null) model = new AWA15ModifyQueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);
            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);
            #region action method stuAction
            
              //  IQueryable<AWAT15> AWAT15List = db.AWAT15;

            IQueryable<VAWAT15> AWAT15List = (
                  from aTemp in db.AWAT15
                  join awat01 in db.AWAT01 on new { aTemp.SCHOOL_NO ,aTemp.USER_NO} equals  new { awat01.SCHOOL_NO , awat01.USER_NO}
                  join aLog in
                     (from log in db.AWAT01_LOG
                      where 1 == 1
                      group log by new { log.SCHOOL_NO, log.USER_NO } into logGroup
                      select new
                      {
                          logGroup.Key.SCHOOL_NO,
                          logGroup.Key.USER_NO,
                          CASH_ALL = logGroup.Sum(x => x.ADD_CASH_ALL),
                          CASH_AVAILABLE = logGroup.Sum(x => x.ADD_CASH_AVAILABLE),
                          CASH_WORKHARD = logGroup.Sum(x => x.ADD_CASH_WORKHARD)
                      })
                 on new { aTemp.SCHOOL_NO, aTemp.USER_NO } equals new { aLog.SCHOOL_NO, aLog.USER_NO }
                  select new VAWAT15
                  {

                      SCHOOL_NO = aTemp.SCHOOL_NO,
                      USER_NO = aTemp.USER_NO,
                      CASH_Rank = aTemp.CASH_Rank,
                      CreatDate = DateTime.Now,
                      IsReMark=aTemp.IsReMark,
                      GOT_DATE= aTemp.GOT_DATE,
                      CANCEL_DATE=aTemp.CANCEL_DATE,
                      AWAT15NO= aTemp.AWAT15NO,
                      SYEAR=aTemp.SYEAR,
                      SEMESTER=aTemp.SEMESTER,
                      CLASS_NO=aTemp.CLASS_NO,
                      SEAT_NO=aTemp.SEAT_NO,
                      NAME=aTemp.NAME,
                      SNAME=aTemp.SNAME,
                      MEMO=aTemp.MEMO,
                      AWAT14_CASH=aTemp.AWAT14_CASH,
                      CASH_ALL=(int) awat01.CASH_ALL,
                      SUMCASH_AVAILABLE= ((aLog.CASH_AVAILABLE < 0) ? (awat01.CASH_AVAILABLE ?? 0) : (aLog.CASH_AVAILABLE ?? 0)) +
                                    (db.AWAT10.Where(w => w.STATUS == "1 "&& w.SCHOOL_NO == aTemp.SCHOOL_NO && w.USER_NO == aTemp.USER_NO)
                                               .Sum(w => (int?)w.AMT) ?? 0),
                      // Other properties might be assigned here if available in the VAWAT15 class
                  }
                         )
                         
                         
                         .AsQueryable();
            var mainQuery = from temp in AWAT15List
                            select new VAWAT15
                            {
                                SCHOOL_NO = temp.SCHOOL_NO,
                                USER_NO = temp.USER_NO,
                                CASH_Rank = temp.CASH_Rank,
                                CreatDate = 
                                db.AWAT14_LOG.Where(x => x.SCHOOL_NO == temp.SCHOOL_NO && x.USER_NO == temp.USER_NO && x.CASH_ALL == ((int)(temp.SUMCASH_AVAILABLE > temp.CASH_ALL ? temp.SUMCASH_AVAILABLE : temp.CASH_ALL))).OrderBy(x => x.CHG_DATE).Select(x => x.CHG_DATE).FirstOrDefault()??
                                db.AWAT15.Where(x => x.SCHOOL_NO == temp.SCHOOL_NO && x.USER_NO == temp.USER_NO &&x.CASH_Rank == temp.CASH_Rank).Select(x => x.CreatDate).FirstOrDefault()

                                ,
                                IsReMark = temp.IsReMark,
                                GOT_DATE = temp.GOT_DATE,
                                CANCEL_DATE = temp.CANCEL_DATE,
                                AWAT15NO = temp.AWAT15NO,
                                SYEAR = temp.SYEAR,
                                SEMESTER = temp.SEMESTER,
                                CLASS_NO = temp.CLASS_NO,
                                SEAT_NO = temp.SEAT_NO,
                                NAME = temp.NAME,
                                SNAME = temp.SNAME,
                                MEMO = temp.MEMO,
                                AWAT14_CASH = temp.AWAT14_CASH,
                                CASH_ALL =(int)(temp.SUMCASH_AVAILABLE> temp.CASH_ALL ? temp.SUMCASH_AVAILABLE: temp.CASH_ALL),

                                 SUMCASH_AVAILABLE = temp.SUMCASH_AVAILABLE,


                            };
         //List <VAWAT15 > AWAT15Listtemp   = mainQuery.ToList();
            AWAT15List = mainQuery.AsQueryable();
            List<string> USERNOLIS = new List<string>();
            USERNOLIS = db.HRMT01.Where(y => y.USER_STATUS == 1 && y.USER_TYPE == "S").Select(x => x.USER_NO).ToList();
                //人員
                if (SchoolNO != "ALL")
                {
                    AWAT15List = AWAT15List.Where(a => a.SCHOOL_NO == SchoolNO);
                }
                if (string.IsNullOrWhiteSpace(model.whereSTATUS))
                {

                int toTranStatus = 0;
                toTranStatus= (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_UN;
                model.whereSTATUS = toTranStatus.ToString();
                }
                if (string.IsNullOrWhiteSpace(model.whereAWAT15NO) == false)
                {
                    int AWARD_NO;
                    if (Int32.TryParse(model.whereAWAT15NO, out AWARD_NO))
                    {
                        AWAT15List = AWAT15List.Where(a => a.AWAT15NO == AWARD_NO);
                    }
                    else
                    {
                        model.whereAWAT15NO = model.whereAWAT15NO + "-此升級ID無效";
                    }
                }

                if (string.IsNullOrWhiteSpace(model.whereSTATUS) == false)
                {
             
                int TRANS_STATUS = 0;

                TRANS_STATUS = Int32.Parse(model.whereSTATUS);
                    //if (model.whereSTATUS=="取消") {
                    //    TRANS_STATUS = (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_Del;
                    //}
                    //else if (model.whereSTATUS == "已升級未領") {
                    //    TRANS_STATUS = (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_UN;

                //}
                //else {
                //    TRANS_STATUS = (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_Receive;
                //}

                AWAT15List = AWAT15List.Where(a => a.IsReMark == TRANS_STATUS && a.CASH_Rank!=0 );
                }
                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    AWAT15List = AWAT15List.Where(a =>
                        a.USER_NO.Contains(model.whereKeyword.Trim())
                        || a.NAME.Contains(model.whereKeyword.Trim())
                   
                        );
                 
                }
            if (string.IsNullOrWhiteSpace(model.WhereRankNO) == false) {


                int RANK_NO = Int32.Parse(model.WhereRankNO) ;
                AWAT15List = AWAT15List.Where(a => a.CASH_Rank == RANK_NO);
            }
                if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
                {
                    AWAT15List = AWAT15List.Where(a => a.NAME.Contains(model.whereSNAME.Trim()));
                }
                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    AWAT15List = AWAT15List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                }
                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    AWAT15List = AWAT15List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }
                AWAT15List = AWAT15List.Where(x => x.CASH_Rank > 0 && USERNOLIS.Contains(x.USER_NO));

       
            //  -- 排序開始 --
            Func<VAWAT15, object> sortExpression;
                switch (model.OrderColumn)
                {
                  

                    case "SYEAR":
                        sortExpression = (q => q.SYEAR);
                        break;

                    case "CLASS_NO":
                        sortExpression = (q => q.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        sortExpression = (q => q.SEAT_NO);
                        break;

                    case "CASH_Rank":
                        sortExpression = (q => q.CASH_Rank);
                        break;

                   

                    case "CreatDate":
                        sortExpression = (q => q.CreatDate);
                        break;
                case "CASH_ALL":
                    sortExpression = (q => q.CASH_ALL);
                    break;
                    default:
                        sortExpression = (q => q.CASH_ALL);
                        break;
                }
                if (model.SortBy == "ASC")
                {
                AWAT15List = AWAT15List.OrderBy(sortExpression).AsQueryable();
              
                }
                else
                {
                AWAT15List = AWAT15List.OrderByDescending(sortExpression).AsQueryable();
                }
            
                model.AWAT15List = AWAT15List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 300);

      


                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

                ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
         
            #endregion action method stuAction
            return View("../AWAT14/ModifyQuery", model);
        }
        public ActionResult _ModifyView(AWAT15PrintViewModel Item)
        {
            return PartialView(Item);
        }
        public ActionResult _PageADDTCountLog(AWAT14RankLogViewModel model)
        {
            this.Shared(Bre_Name + "-點數級距紀錄");
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            AWAT14RankLogViewModel aDDI11RunMapViewModel = new AWAT14RankLogViewModel();
            if (string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                aDDI11RunMapViewModel.WhereSCHOOL_NO = user.SCHOOL_NO;
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }
            if (!string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                aDDI11RunMapViewModel.WhereSCHOOL_NO = model.WhereSCHOOL_NO;

            }
            if (!string.IsNullOrEmpty(model.OrdercColumn))
            {

                aDDI11RunMapViewModel.OrdercColumn = model.OrdercColumn;
                aDDI11RunMapViewModel.SyntaxName = model.SyntaxName;
            }
            AWAT14RankViewModel aWAT14RankViewModel = new AWAT14RankViewModel();
           List<AWAT14RankCount> aWAT14RankCounts = new List<AWAT14RankCount>();
            aWAT14RankCounts = aWAT14Service.AWAT14RankCount(aDDI11RunMapViewModel, ref db);
            model.RankCount = aWAT14RankCounts;

            return PartialView(model);
        }
        public ActionResult ADDTCountLog(AWAT14RankLogViewModel model)
        {
            this.Shared(Bre_Name + "-各級數統計");

            return View(model);
        }
        /// <summary>
        /// Menu 清單
        /// </summary>
        /// <param name="NowAction"></param>
        /// <returns></returns>
        public ActionResult _PageMenu(string NowAction)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            if (user != null)
            {
                var hrmt01 = db.HRMT01.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                user.CLASS_NO = hrmt01.CLASS_NO == null ? "" : hrmt01.CLASS_NO;
            }
            //ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        /// <summary>
        /// 閱讀認證排行榜
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult ADDTList(AWAT14ListViewModel model)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }
            string UseYN = "N";
            AWAT14Service aWAT14Service = new AWAT14Service();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (model == null) model = new AWAT14ListViewModel();
            try
            {

                if (model.doClear)
                {
                    model.ClearWhere();
                }

                int PageSize = 20;
                int Count = 0;

                string sqlwherekeyword = "";

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {

                    sqlwherekeyword = sqlwherekeyword+" and (USER_NO in (''" + model.whereKeyword.Trim() + "'') or  (USERNAME like ''%" + model.whereKeyword.Trim() + "%''))";
                  
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    sqlwherekeyword = sqlwherekeyword+" and CLASS_NO=''" + model.whereCLASS_NO.Trim() + "''";
                      
                    PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                   

                    sqlwherekeyword = sqlwherekeyword+"  and substring(CLASS_NO,1,1)=''" + model.whereGrade.Trim() + "''";
                }

                if (model.WhereUP_DATE_START != null)
                {
                
                    sqlwherekeyword = sqlwherekeyword + "  and  DP_DATE>=''" + model.WhereUP_DATE_START + "''";
                }
                if (model.WhereRankNO != null)
                {
                    int rank = 0;
                    rank = int.Parse(model.WhereRankNO);
                    if (rank > 11)
                    {


                    }
                   
                    sqlwherekeyword = sqlwherekeyword + "  and  CASH_Rank=''" + rank + "''";
                }
                if (model.WhereUP_DATE_END != null)
                {
                    sqlwherekeyword = sqlwherekeyword + "  and  DP_DATE<=''" + model.WhereUP_DATE_END + "''";
                 
                }
                string OrderByStr = " ";
                switch (model.OrdercColumn)
                {
                    case "CLASS_NO":
                        OrderByStr = OrderByStr + " CLASS_NO ";
                        
                        break;

                    case "SEAT_NO":
                     
                        OrderByStr = OrderByStr + " SEAT_NO ";
                        break;

                    case "SNAME":
                        OrderByStr = OrderByStr + " SNAME ";
                      
                        break;

                    case "CASH_ALL":
                        OrderByStr = OrderByStr + " CASH_ALL ";
               
                        break;

                    case "LEVEL_DESC":
                        OrderByStr = OrderByStr + " LEVEL_DESC ";
                       
                        break;

                    case "DP_DATE":
                        OrderByStr = OrderByStr + " DP_DATE ";

                   
                        break;



                    default:

                        OrderByStr = OrderByStr + " CASH_Rank ";
                      
                        break;
                }
                OrderByStr = OrderByStr + "desc";
                // IQueryable<AWAT1409> AWAT1409List = null;
                // IQueryable<ADDV14> ADDV14List= db.ADDV14.Where(x => x.SCHOOL_NO == SchoolNO).AsQueryable();
                //List<ADDV14>  ADDV14ListQuery = aWAT14Service.GetALLCash(SchoolNO, ref Count, sqlwherekeyword, OrderByStr, model.Page > 0 ? model.Page - 1 : 1, PageSize);
                //IQueryable<ADDV14> ADDV14List = ADDV14ListQuery.AsQueryable();
                IQueryable<AWAT1409> AWAT1409List = (from x in db.AWAT14
                                                     join y in db.HRMT01

                                 on new { x.SCHOOL_NO, x.USER_NO }
                                 equals new { y.SCHOOL_NO, y.USER_NO }
                                                     join xy in db.AWAT01 on new { y.SCHOOL_NO, y.USER_NO }

                                                     equals new { xy.SCHOOL_NO, xy.USER_NO }

                                                     join aLog in
                     (from log in db.AWAT01_LOG
                      where 1 == 1
                      group log by new { log.SCHOOL_NO, log.USER_NO } into logGroup
                      select new
                      {
                          logGroup.Key.SCHOOL_NO,
                          logGroup.Key.USER_NO,
                          CASH_ALL = logGroup.Sum(x => x.ADD_CASH_ALL),
                          CASH_AVAILABLE = logGroup.Sum(x => x.ADD_CASH_AVAILABLE),
                          CASH_WORKHARD = logGroup.Sum(x => x.ADD_CASH_WORKHARD)
                      }) on new { x.SCHOOL_NO, x.USER_NO } equals new { aLog.SCHOOL_NO, aLog.USER_NO }
                                                     where x.SCHOOL_NO == SchoolNO && y.USER_STATUS == UserStaus.Enabled && y.USER_TYPE == UserType.Student && x.CASH_Rank!=0
                                                     select new AWAT1409
                                                     {

                                                         SYEAR = SqlFunctions.StringConvert((double)y.SYEAR),
                                                         CLASS_NO = y.CLASS_NO,
                                                         SEAT_NO = y.SEAT_NO,
                                                         SNAME = y.SNAME,
                                                         USERNAME = y.NAME,

                                                         SCHOOL_NO = y.SCHOOL_NO,
                                                         USER_NO = y.USER_NO,

                                                         CASH_ALL = (int)xy.CASH_ALL,
                                                         SUMCASH_AVAILABLE = ((aLog.CASH_AVAILABLE < 0) ? (xy.CASH_AVAILABLE ?? 0) : (aLog.CASH_AVAILABLE ?? 0)) +
                                    (db.AWAT10.Where(w => w.STATUS == "1 " && w.SCHOOL_NO == SchoolNO && w.USER_NO == y.USER_NO)
                                               .Sum(w => (int?)w.AMT) ?? 0),
                                                         LEVEL_DESC = "第" + x.CASH_Rank + "級",
                                                         DP_DATE = (db.AWAT14_LOG.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == y.USER_NO && x.CASH_ALL == xy.CASH_ALL).OrderBy(x => x.CHG_DATE).Select(x => x.CHG_DATE).FirstOrDefault())
                                                         ,


                                                         CASH_Rank = (int)x.CASH_Rank

                                                     }
                                                    );

                var mainQuery = from temp in AWAT1409List
                                select new AWAT1409
                                {
                                    SYEAR = temp.SYEAR,
                                    CLASS_NO = temp.CLASS_NO,
                                    SEAT_NO = temp.SEAT_NO,
                                    SNAME = temp.SNAME,
                                    USERNAME = temp.USERNAME,
                                    SCHOOL_NO = temp.SCHOOL_NO,
                                    USER_NO = temp.USER_NO,
                                    CASH_ALL = (int)(temp.SUMCASH_AVAILABLE > temp.CASH_ALL ? temp.SUMCASH_AVAILABLE : temp.CASH_ALL),

                                    SUMCASH_AVAILABLE = temp.SUMCASH_AVAILABLE,

                                    LEVEL_DESC = temp.LEVEL_DESC,

                                    DP_DATE = temp.DP_DATE,
                                    CASH_Rank = temp.CASH_Rank,


                                };
                AWAT1409List = mainQuery.AsQueryable();
                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    AWAT1409List = AWAT1409List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()));
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    AWAT1409List = AWAT1409List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                    PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    AWAT1409List = AWAT1409List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }

                if (model.WhereUP_DATE_START != null)
                {
                    AWAT1409List = AWAT1409List.Where(a => a.DP_DATE >= model.WhereUP_DATE_START);
                }
                if (model.WhereRankNO != null)
                {
                    int rank = 0;
                    rank = int.Parse(model.WhereRankNO);
                    if (rank > 11)
                    {


                    }
                    AWAT1409List = AWAT1409List.Where(a => a.CASH_Rank == rank);
                }
                if (model.WhereUP_DATE_END != null)
                {
                    AWAT1409List = AWAT1409List.Where(a => a.DP_DATE <= model.WhereUP_DATE_END);
                }
                switch (model.OrdercColumn)
                {
                    case "CLASS_NO":
                        AWAT1409List = AWAT1409List.OrderByDescending(a => a.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        AWAT1409List = AWAT1409List.OrderByDescending(a => a.SEAT_NO);
                        break;

                    case "SNAME":
                        AWAT1409List = AWAT1409List.OrderByDescending(a => a.SNAME);
                        break;

                    case "CASH_ALL":
                        AWAT1409List = AWAT1409List.OrderByDescending(a => a.CASH_ALL);
                        break;

                    case "LEVEL_DESC":
                        AWAT1409List = AWAT1409List.OrderByDescending(a => a.CASH_Rank);
                        break;

                    case "DP_DATE":
                        AWAT1409List = AWAT1409List.OrderByDescending(a => a.DP_DATE);
                        break;



                    default:
                        AWAT1409List = AWAT1409List.OrderByDescending(a => a.CASH_Rank).OrderByDescending(a => a.CASH_ALL);
                        break;
                }
                model.AWAT14List = AWAT1409List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);


              //  model.TotalItemCount = Count;
               // model.AWAT14List = ADDV14ListQuery.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
       
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            //判斷是否有權限
            if (UseYN == "N")
            {
                ViewBag.VisableInsert = false;
            }
            else
            {
                ViewBag.VisableInsert = true;
            }
            if (model.isCarousel)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }
            return View();

        }
            public ActionResult SysSetintervalSave(AWAT14SysSetIntervalViewModel model)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            UserProfile user = UserProfileHelper.Get();
            if (model == null)
            {
                return RedirectToAction("SysSetinterval");
            }
            IResult result = aWAT14Service.SaveSysSetAWAT14Data(model, user,ref  db);

            IResult resulta = aWAT14Service.InsertAWAT15List(model, user, ref db);
            BDMT01 bDMT01 = new BDMT01();
            bDMT01 = (BDMT01)result.ModelItem;
            model.AWAT14_CASH = model.AWAT14_CASH;
            model.SCHOOL_NO = user.SCHOOL_NO;
            return RedirectToAction("SysSetinterval", "AWAT14");
        }

        public ActionResult SysSetintervalAllSave() {


            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<BDMT01> BDMT01List = new List<BDMT01>();
            BDMT01List = db.BDMT01.ToList();
            AWAT14SysSetIntervalViewModel aWAT14Sys = new AWAT14SysSetIntervalViewModel();
            

            foreach (var item in BDMT01List) {
                aWAT14Sys.SCHOOL_NO = item.SCHOOL_NO;



            }
            return View();
        }
        [HttpPost]
        public ActionResult CancelTrans(FormCollection vawa04)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (vawa04["hidSelectTRANS_NO"] != null)
            {
                //查詢交易紀錄
                int TranLogNO = Convert.ToInt32(vawa04["hidSelectTRANS_NO"]);

                //更新交易紀錄-->取消 (9)
                AWAT15 TranLog = db.AWAT15.Where(p => p.AWAT15NO == TranLogNO).FirstOrDefault();

                if (TranLog.IsReMark != 9)
                {
                    TranLog.IsReMark = (byte)9;
                    TranLog.CANCEL_DATE = DateTime.Now.Date;
                    db.Entry(TranLog).State = System.Data.Entity.EntityState.Modified;


                    ////調整商品庫存量
                    //AWAT02 BackAward = db.AWAT02.Where(a => a.AWARD_NO == TranLog.AWARD_NO).FirstOrDefault();

                    //AWAT02_HIS HIS = db.AWAT02_HIS.Create();
                    //HIS.AWARD_NO = BackAward.AWARD_NO;
                    //HIS.SCHOOL_NO = BackAward.SCHOOL_NO;
                    //HIS.AWARD_TYPE = BackAward.AWARD_TYPE;
                    //HIS.AWARD_NAME = BackAward.AWARD_NAME;
                    //HIS.COST_CASH = BackAward.COST_CASH;
                    //HIS.QTY_STORAGE = BackAward.QTY_STORAGE;
                    //HIS.SDATETIME = BackAward.SDATETIME;
                    //HIS.EDATETIME = BackAward.EDATETIME;
                    //HIS.DESCRIPTION = BackAward.DESCRIPTION;
                    //HIS.IMG_FILE = BackAward.IMG_FILE;
                    //HIS.IMG2_FILE = BackAward.IMG2_FILE;
                    //HIS.AWARD_STATUS = BackAward.AWARD_STATUS;
                    //HIS.HOT_YN = BackAward.HOT_YN;
                    //HIS.BUY_PERSON_YN = BackAward.BUY_PERSON_YN;
                    //HIS.CHG_DATE = DateTime.Now;
                    //HIS.READ_LEVEL = BackAward.READ_LEVEL;
                    //HIS.PASSPORT_LEVEL = BackAward.PASSPORT_LEVEL;
                    //HIS.QTY_LIMIT = BackAward.QTY_LIMIT;
                    //HIS.SHOW_DESCRIPTION_YN = BackAward.SHOW_DESCRIPTION_YN;
                    //HIS.FULLSCREEN_YN = BackAward.FULLSCREEN_YN;
                    //db.AWAT02_HIS.Add(HIS);
                    //    if (BackAward.AWARD_TYPE != "B")
                    //    {
                    //        BackAward.QTY_STORAGE = (BackAward.QTY_STORAGE.Value) + 1;// +TranLog.TRANS_QTY.Value;
                    //        BackAward.QTY_TRANS = BackAward.QTY_TRANS.Value - 1;// TranLog.TRANS_QTY.Value;
                    //    }
                    //    else
                    //    {
                    //        int? COST_CASH = 0;
                    //        // COST_CASH = Int32.Parse(vawa04["COST_CASH"]);
                    //        if (BackAward.AWARD_TYPE == "B" && TranLog.TRANS_CASH < BackAward.BID_BUY_PRICE)
                    //        {
                    //            BackAward.QTY_STORAGE = (BackAward.QTY_STORAGE.Value) + 1;// +TranLog.TRANS_QTY.Value;
                    //            BackAward.QTY_TRANS = BackAward.QTY_TRANS.Value - 1;// TranLog.TRANS_QTY.Value;
                    //        }


                    //    string BATCH_ID = PushService.CreBATCH_ID();
                    //    string BODY_TXT = string.Empty;

                    //    //還原學生酷幣點數
                    //    if (BackAward.AWARD_TYPE == "C")
                    //    {
                    //        BODY_TXT = "被取消兌換募資公益，品名:" + HIS.AWARD_NAME + "加回原酷幣點數" + (TranLog.TRANS_CASH.Value).ToString() + "數";

                    //        CashHelper.AddCash(user, TranLog.TRANS_CASH.Value, TranLog.SCHOOL_NO, TranLog.USER_NO, "AWAT02", TranLog.TRANS_NO.ToString(), "取消兌換募資公益", false, ref db, "", "", ref valuesList);
                    //    }
                    //    else
                    //    {
                    //        BODY_TXT = "取消獎品兌換，品名:" + HIS.AWARD_NAME + "加回原酷幣點數" + (TranLog.TRANS_CASH.Value).ToString() + "數";

                    //        CashHelper.AddCash(user, TranLog.TRANS_CASH.Value, TranLog.SCHOOL_NO, TranLog.USER_NO, "AWAT02", TranLog.TRANS_NO.ToString(), "取消獎品兌換", false, ref db, "", "", ref valuesList);
                    //    }

                    //    //PushService.InsertPushDataParents(BATCH_ID, TranLog.SCHOOL_NO, TranLog.USER_NO, "", BODY_TXT, "", "AWA004", "CancelTrans", TranLogNO.ToString(), "", false, ref db);
                    //    PushService.InsertPushDataMe(BATCH_ID, TranLog.SCHOOL_NO, TranLog.USER_NO, "", BODY_TXT, "", "AWA004", "CancelTrans", TranLogNO.ToString(), "", false, ref db);

                    //    db.SaveChanges();
                    //    TempData["StatusMessage"] = $"完成取消 {TranLog.NAME} 獎品[{BackAward.AWARD_NAME}]的兌換";

                    //    //Push
                    //    PushHelper.ToPushServer(BATCH_ID);
                    //}
                    db.SaveChanges();
                    TempData["StatusMessage"] = $"完成取消 {TranLog.NAME} 頒獎";
                    UserProfile.RefreshCashInfo(user, ref db);
                    UserProfileHelper.Set(user);
                }
                else
                {
                    TempData["StatusMessage"] = "重覆取消[" + TranLog.NAME + "]頒獎";
                }
            }

            if (vawa04["ActionFrom"] != null) // 來源為Query Action傳來?
            {
                return RedirectToAction(vawa04["ActionFrom"].ToString(), "AWAT14", new { RoleName = vawa04["RoleName"], whereUserNo = vawa04["whereUserNo"] });
            }

            return RedirectToAction("ModifyQuery", "AWAT14", new { RoleName = vawa04["RoleName"], whereSTATUS = vawa04["whereSTATUS"] });
        }
    }
}