﻿@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string ErrMsg;
    short ThisMonthCash;
    string LimitShow = string.Empty;
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    int CashLimit = 0;
    CashLimit = ECOOL_APP.UserProfile.
         GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
    if (CashLimit != short.MaxValue)
    {
        LimitShow = "您本月有" + CashLimit + "點酷幣可以發給特殊表現的孩子，你已經發放了" + ThisMonthCash.ToString() + "點。";
    }
    else {
        bool? CHECK_CASH_LIMIT = null;
        BDMT01 dMT01 = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
        CHECK_CASH_LIMIT = dMT01.CHECK_CASH_LIMIT;
        if (CHECK_CASH_LIMIT!=null && CHECK_CASH_LIMIT==true ) {

            LimitShow = "您本月有" + CashLimit + "點酷幣可以發給特殊表現的孩子，你已經發放了" + ThisMonthCash.ToString() + "點。";
        }

    }

}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="form-group ">
    <label class="text-danger col-md-12">
        @LimitShow
    </label>
</div>

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            @if (ViewBag.ISTASKLIST != "True")
            {
                <div class="form-group text-center">
                    @{
                        var BtnItem = TempData["BtnItem"] as List<Tuple<string, string>>;
                        @*foreach (var Btn in BtnItem)
                            {
                                <div class="col-xs-6">
                                    @Html.ActionLink(Btn.Item2, "Index", new { SYS_TABLE_TYPE = Btn.Item1 }, new { @class = "btn-block btn btn-default", @role = "button" })
                                </div>

                            }*@

                        <div class="tab-content">
                            <ul class="nav nav-pills">
                                <li @((bool)ViewBag.IsAdmin ? "class=active" : "")><a data-toggle="pill" href="#admindiv" style=""><b>行政專區</b></a></li>
                                <li @((bool)!ViewBag.IsAdmin ? "class=active" : "")><a data-toggle="pill" href="#teacherdiv" style=""><b>級(科)任專區</b></a></li>
                            </ul>
                            <br />
                            <div id="admindiv" class="tab-pane fade @((bool)ViewBag.IsAdmin ? "in active" : "")">
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-default" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "ADDT15" })" role="button">校外<br class="hidden-md hidden-sm hidden-lg" />榮譽</a>
                                    @*@Html.ActionLink("校外榮礜", "Index", new { SYS_TABLE_TYPE = "ADDT15" }, new { @class = "btn-block btn btn-default", @role = "button" })*@
                                </div>
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-default" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "" })" role="button">校內<br class="hidden-md hidden-sm hidden-lg" />表現</a>
                                    
                                </div>

                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-warning" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "ADDT20"})" role="button">特殊加<br class="hidden-md hidden-sm hidden-lg" />扣點</a>
                                    @*@Html.ActionLink("特殊加扣點", "Index", new { SYS_TABLE_TYPE = "ADDT20" }, new { @class = "btn-block btn btn-warning", @role = "button" })*@
                                </div>
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-default" href="@Url.Action("ListView", "ADDI09")" role="button">查詢歷史<br class="hidden-md hidden-sm hidden-lg" />記綠</a>
                                    @*@Html.ActionLink("查詢歷史記綠", "ListView", null, new { @class = "btn-block btn btn-default", @role = "button" })*@
                                </div>

                                @if ((bool)ViewBag.IsAdmin && user.USER_NO == "0000")
                                {
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-default" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "AWAT08_LOG" })" role="button">老師加<br class="hidden-md hidden-sm hidden-lg" />扣點</a>
                                    @*@Html.ActionLink("老師加扣點", "Index", new { SYS_TABLE_TYPE = "AWAT08_LOG" }, new { @class = "btn-block btn btn-default", @role = "button" })*@
                                </div>
                                <div class="col-xs-6">


                                    <a class="btn-block btn btn-default" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "AWAT01_LOG_LUCKY" })" role="button">加好運<br class="hidden-md hidden-sm hidden-lg" />集點</a>
                                    @*@Html.ActionLink("加好運集點", "Index", new { SYS_TABLE_TYPE = "AWAT01_LOG_LUCKY" }, new { @class = "btn-block btn btn-default", @role = "button" })*@
                                </div>

                                }

                                @if (ViewBag.SuperGivePoint == "Y" && ViewBag.ISTASKLIST != "True")
                                {
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-default" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "_CLASS_BATCH" })" role="button">無敵加<br class="hidden-md hidden-sm hidden-lg" />點</a>
                                    @*@Html.ActionLink("無敵加點", "Index", new { SYS_TABLE_TYPE = "_CLASS_BATCH" }, new { @class = "btn-block btn btn-default", @role = "button" })*@
                                </div>
                                }
                            </div>

                            <div id="teacherdiv" class="tab-pane fade @((bool)!ViewBag.IsAdmin ? "in active" : "")">

                                <div class="col-xs-6">
                                    <a class="btn-block btn btn-default" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "ADDT14" , ADDT14_STYLE = "LEADER"})" role="button">班級<br class="hidden-md hidden-sm hidden-lg" />幹部</a>
                                    @*@Html.ActionLink("班級幹部", "Index", new { SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "LEADER" }, new { @class = "btn-block btn btn-default", @role = "button" })*@
                                    <a class="btn-block btn btn-info" href="@Url.Action("EditQuickBulk", "ADDI09", new { SYS_TABLE_TYPE = "QUICKBULK" , Individual_Give = true,DeleteTemp = true})" role="button">快速<br class="hidden-md hidden-sm hidden-lg" />加點</a>
                                    @*@Html.ActionLink("快速加點", "EditQuickBulk", new { SYS_TABLE_TYPE = "QUICKBULK", Individual_Give = true, DeleteTemp = true }, new { @class = "btn-block btn btn-info", @role = "button" })*@
                                </div>
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-info" href="@Url.Action("QuerySelectView1", "ADDI09", new { SYS_TABLE_TYPE = "ADDT14" , ADDT14_STYLE = "LEADER"})" role="button" style="margin-bottom:12px;margin-right: 12px;">班級幹<br class="hidden-md hidden-sm hidden-lg" />部(多筆)</a>
                                    @*@Html.ActionLink("班級幹部(多筆)", "QuerySelectView1", new { SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "LEADER" }, new { @class = "btn-block btn btn-info", @role = "button", @style = "margin-bottom:12px;margin-right: 12px;" })*@

                                    <a class="btn-block btn btn-default" href="@Url.Action("ListView", "ADDI09", new { SYS_TABLE_TYPE = "ADDT14" , ADDT14_STYLE = "LEADER"})" role="button" >查詢歷史<br class="hidden-md hidden-sm hidden-lg" />記綠</a>

                                    @*@Html.ActionLink("查詢歷史記綠", "ListView", null, new { @class = "btn-block btn btn-default", @role = "button" })*@
                                </div>
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-warning" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "ADDT14" , ADDT14_STYLE = "HELPER"})" role="button">班級幫手<br class="hidden-md hidden-sm hidden-lg" />和榮譽</a>
                                    @*@Html.ActionLink("班級幫手和榮譽", "Index", new { SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "HELPER" }, new { @class = "btn-block btn btn-warning", @role = "button" })*@

                                </div>
                                <div class="col-xs-6">

                                    <a class="btn-block btn btn-warning" href="@Url.Action("Index", "ADDI09", new { SYS_TABLE_TYPE = "ADDT20_CLASS"})" role="button">特殊班級<br class="hidden-md hidden-sm hidden-lg" />加扣點</a>
                                    @*@Html.ActionLink("班級特殊加扣點", "Index", new { SYS_TABLE_TYPE = "ADDT20_CLASS" }, new { @class = "btn-block btn btn-warning", @role = "button" })*@
                                </div>
                                

                            </div>
                        </div>
                    }
                </div>
            }
            else
            { <div class="form-group text-center">
                  <div class="col-xs-6">
                      <a class="btn-block btn btn-info" href="@Url.Action("EditQuickBulk", "ADDI09", new { SYS_TABLE_TYPE = "QUICKBULK", Individual_Give = true, DeleteTemp = true})" role="button">快速<br class="hidden-md hidden-sm hidden-lg" />加點</a>
                      @*@Html.ActionLink("快速加點", "EditQuickBulk", new { SYS_TABLE_TYPE = "QUICKBULK", Individual_Give = true, DeleteTemp = true }, new { @class = "btn-block btn btn-info", @role = "button" })*@
                  </div>
                </div>
            }
            @*<div class="form-group ">
                    <label class="text-warning col-md-12">
                        1.「特殊」加扣點紀錄不顯示於校內表現、校外榮礜
                    </label>
                    <label class="text-warning col-md-12">
                        2.各校是否啟用班級功能，請洽各校管理者
                    </label>
                    <label class="text-warning col-md-12">
                        3.班級酷幣會有每個月加點的上限，請洽各校管理者
                    </label>
                </div>*@

            <div class="form-group ">
                <label>
                    說明:
                </label>
                <label >
                    1.<font style="color:#f0ad4e">橘色按鈕</font>(特殊加扣點、班級特殊加扣點、班級幫手和榮譽)是被控管的點數，每個月發出去的點數有被限制。<a  href="https://youtu.be/CoNHkV9Z-Pw">若有疑問請看影片說明。</a>
                </label>
                <label>
                    2.<font style="color:#428bca">淡藍色按鈕</font>(快速加點，班級幹部-多筆)，APP上也可以使用，但是這是給導師使用的功能。
                </label>
                <label >
                    3.只要是「特殊」開頭的加點按鈕，加扣點紀錄不顯示於校內表現、校外榮譽裡，也不會在e本書裡面，只會在給點紀錄或數位存摺等功能看到。
                </label>
                <label >
                    4.行政專區的無敵加點，預設只給管理者，有需要開放給部分行政老師，請各校管理員自行斟酌開放。(從帳號權限維護開放)
                </label>
            </div>

            @*<div class="form-group">
                    <label class="text-danger">
                        給點上限說明：
                    </label>
                    <label class="text-danger">
                        1.點數限制只計算批次加扣點裡面的「班級特殊加扣點」、「班級小幫手」，以及即時加點的「特殊加點--固定」、「特殊加點--隨機」
                    </label>
                    <label class="text-danger">
                        2.不包括線上投稿、閱讀認證、藝廊、校內外表現等獲得的點數。
                    </label>
                </div>*@
        </div>
    </div>
}