﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'he', {
	find: 'חיפוש',
	findOptions: 'א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> חיפוש',
	findWhat: 'חיפוש מחרוזת:',
	matchCase: 'הבחנה בין אותיות רשיות לקטנות (Case)',
	matchCyclic: 'התאמה מחזורית',
	matchWord: 'התאמה למילה המלאה',
	notFoundMsg: 'הטקסט המבוקש לא נמצא.',
	replace: 'החלפה',
	replaceAll: 'החל<PERSON>ה בכל העמוד',
	replaceSuccessMsg: '%1 טקסטים הוחלפו.',
	replaceWith: 'החלפה במחרוזת:',
	title: 'חי<PERSON><PERSON><PERSON> והחלפה'
} );
