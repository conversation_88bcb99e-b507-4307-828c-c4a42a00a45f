﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI28Controller : Controller
    {

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI28";
        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;
        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;


        private ECOOL_DEVEntities Db = new ECOOL_DEVEntities();
        // GET: ZZZI28
        public ActionResult Index(ZZZI28QueryViewModel Data)
        {
            this.Shared("己綁定清單");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (Data == null) Data = new ZZZI28QueryViewModel();

            if (Data.SCHOOL_NO == null) Data.SCHOOL_NO = user.SCHOOL_NO;
            if (Data.PARENTS_USER_NO == null) Data.PARENTS_USER_NO = user.USER_NO;

            var temp=  from a in Db.HRMT06
                         join b in Db.HRMT01
                         on new { SCHOOL_NO = a.SCHOOL_NO, USER_NO = a.STUDENT_USER_NO } equals new { SCHOOL_NO = b.SCHOOL_NO, USER_NO = b.USER_NO }
                         where a.SCHOOL_NO==Data.SCHOOL_NO && a.PARENTS_USER_NO==Data.PARENTS_USER_NO
                        select b;

            Data.HRMT01List = temp.ToList();


            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }

            var UserNoListData = HRMT01.GetUserNoListNoData(SCHOOL_NO, CLASS_NO, ref Db);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, USER_NO, true, null);

            return Content(_html);
        }

        [HttpGet]
        [CheckPermission] //檢查權限
        public ActionResult Edit(string STUDENT_USER_NO,string REF_BRE_NO)
        {
            this.Shared("新增綁定-步驟1-請選擇您家寶貝");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            ZZZI28AddViewModel Data = new ZZZI28AddViewModel();
            Data.SCHOOL_NO = user.SCHOOL_NO;
            Data.PARENTS_USER_NO = user.USER_NO;
            Data.REF_BRE_NO = REF_BRE_NO;

            if (string.IsNullOrWhiteSpace(STUDENT_USER_NO) == false)
            {
                Data.STUDENT_USER_NO = STUDENT_USER_NO;

                var MyBady = Db.HRMT01.Where(a => a.SCHOOL_NO == Data.SCHOOL_NO && a.USER_NO == Data.STUDENT_USER_NO).FirstOrDefault();

                if (MyBady != null)
                {
                    Data.CLASS_NO = MyBady.CLASS_NO;
                }
            }


            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO,null, Data.CLASS_NO, ref Db);


            ViewBag.USER_NOItems = HRMT01.GetUserNoListNoData(DefaultSCHOOL_NO, Data.CLASS_NO, Data.STUDENT_USER_NO, ref Db);


            return View(Data);
        }


        [HttpPost]
        [CheckPermission] //檢查權限
        public ActionResult Edit(ZZZI28AddViewModel Data)
        {
            this.Shared("新增綁定-步驟1-請選擇您家寶貝");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            Data.NUM_TYPE = 1;
            Data.SCHOOL_NO = user.SCHOOL_NO;
            Data.PARENTS_USER_NO = user.USER_NO;
            Data.CRE_PERSON = user.USER_KEY;

            string Err = string.Empty;
            var OK = this.CheckData(Data, out Err);

            if (OK)
            {
                return View("Edit_last", Data);
            }

            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, null, Data.CLASS_NO, ref Db);
            ViewBag.USER_NOItems = HRMT01.GetUserNoListNoData(DefaultSCHOOL_NO, Data.CLASS_NO, Data.STUDENT_USER_NO, ref Db);

            TempData["StatusMessage"] = Err;
            return View(Data);
        }


        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult Edit_last(string STUDENT_USER_NO, string REF_BRE_NO)
        {
            this.Shared("新增綁定-請輸入您家寶貝身分證、學號和生日");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            ZZZI28AddViewModel Data = new ZZZI28AddViewModel();
            Data.SCHOOL_NO = user.SCHOOL_NO;
            Data.PARENTS_USER_NO = user.USER_NO;
            Data.REF_BRE_NO = REF_BRE_NO;

            Data.NUM_TYPE = 2;
            Data.CRE_PERSON = user.USER_KEY;
            Data.STUDENT_USER_NO = STUDENT_USER_NO;

            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult Edit_last(ZZZI28AddViewModel Data)
        {
            this.Shared("新增綁定");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (Data.NUM_TYPE==0)
            {
                Data.NUM_TYPE = 2;
            }
           
           



            Data.SCHOOL_NO = user.SCHOOL_NO;
            Data.PARENTS_USER_NO = user.USER_NO;
            Data.CRE_PERSON = user.USER_KEY;
          

            string Err = string.Empty;

            var Check = this.CheckData(Data, out Err);
            if (Check)
            {
                if (Data.NUM_TYPE==2)
                {
                    ModelState.Clear();
                    Data.NUM_TYPE = 3;
                    return View(Data);
                }

                var OK = this.Save(Data, out Err);

                if (OK)
                {
                    TempData["StatusMessage"] = "綁定成功";

                    if (string.IsNullOrWhiteSpace(Data.REF_BRE_NO)==false)
                    {
                        return RedirectToAction("Index");
                    }

                    string ActionName = string.Empty, ControllerName = string.Empty;
                    HomeController.NextAction(user, out ActionName, out ControllerName);

              

                    if (ActionName == "MODIFY" && ControllerName == "ZZT08")
                    {
                        return RedirectToAction(ActionName, ControllerName);
                    }

                    return RedirectToAction(ActionName, ControllerName, new { school = user.SCHOOL_NO });
                }
            }

            TempData["StatusMessage"] = Err;
            return View(Data);
        }





        [HttpGet]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult DEL(ZZZI28AddViewModel Data)
        {
            this.Shared("刪除");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }


            if (Data==null)
            {
                return RedirectToAction("NotParameterError", "Error");
            }

            if (Data.STUDENT_USER_NO==null)
            {
                return RedirectToAction("NotParameterError", "Error");
            }

            if (Data.SCHOOL_NO == null) Data.SCHOOL_NO = user.SCHOOL_NO;
            if (Data.PARENTS_USER_NO == null) Data.PARENTS_USER_NO = user.USER_NO;

            HRMT06 T06 = Db.HRMT06.Where(a => a.SCHOOL_NO == Data.SCHOOL_NO && a.PARENTS_USER_NO == Data.PARENTS_USER_NO && a.STUDENT_USER_NO == Data.STUDENT_USER_NO).FirstOrDefault();

            if (T06==null)
            {
                TempData["StatusMessage"] = "此筆資料已不存在";
                return RedirectToAction("Index");
            }

            Db.HRMT06.Remove(T06);

            try
            {
                Db.SaveChanges();
                TempData["StatusMessage"] = "刪除成功";
            }
            catch (Exception ex)
            {
                TempData["StatusMessage"] = "刪除失敗"+ex.Message;
                return RedirectToAction("Index");
            }



            return RedirectToAction("Index");
        }




        private bool CheckData(ZZZI28AddViewModel Data, out string ErrorMsg)
        {
            ErrorMsg = string.Empty;


            if (Data.NUM_TYPE==1)
            {

                if (string.IsNullOrWhiteSpace(Data.STUDENT_USER_NO))
                {
                    ErrorMsg = "請選擇學生姓名";
                    return false;
                }


                HRMT01 StH01 = Db.HRMT01.Where(a => a.SCHOOL_NO == Data.SCHOOL_NO && a.USER_NO == Data.STUDENT_USER_NO).FirstOrDefault();

                if (StH01 == null)
                {
                    ErrorMsg = "查無此學生姓名";
                    return false;
                }

              HRMT06 T06=Db.HRMT06.Where(a=>a.SCHOOL_NO==Data.SCHOOL_NO && a.PARENTS_USER_NO==Data.PARENTS_USER_NO && a.STUDENT_USER_NO== Data.STUDENT_USER_NO).FirstOrDefault();

                if (T06!= null)
                {
                    ErrorMsg = "此學生已綁定";
                    return false;
                }
            }

            if (Data.NUM_TYPE >= 2)
            {


                if (string.IsNullOrWhiteSpace(Data.IDNO))
                {
                    ErrorMsg = "請輸入學生身份證";
                    return false;
                }

           

                if (string.IsNullOrWhiteSpace(Data.STUDENT_USER_NO2))
                {
                    ErrorMsg = "請輸入學生學號";
                    return false;
                }

                HRMT01 StH01 = null;

                if (string.IsNullOrWhiteSpace(Data.STUDENT_USER_NO))
                {
                    Data.STUDENT_USER_NO = Data.STUDENT_USER_NO2;
                }
               
                StH01 = Db.HRMT01.Where(a => a.SCHOOL_NO == Data.SCHOOL_NO && a.USER_NO == Data.STUDENT_USER_NO).FirstOrDefault();

                if (StH01 == null)
                {
                    ErrorMsg = "查無此學生學號";
                    return false;
                }


                HRMT06 T06 = Db.HRMT06.Where(a => a.SCHOOL_NO == Data.SCHOOL_NO && a.PARENTS_USER_NO == Data.PARENTS_USER_NO && a.STUDENT_USER_NO == Data.STUDENT_USER_NO).FirstOrDefault();

                if (T06 != null)
                {
                    ErrorMsg = "此學生已綁定";
                    return false;
                }


          

                if (StH01.IDNO.ToUpper() != Data.IDNO.ToUpper())
                {
                    ErrorMsg = "學生身份證輸入錯誤";
                    return false;
                }



                if (StH01.USER_NO != Data.STUDENT_USER_NO2)
                {
                    ErrorMsg = "學生學號輸入錯誤";
                    return false;
                }

                if (Data.NUM_TYPE >= 3)
                {
                    //if (Data.BIRTHDAY == null || Data.BIRTHDAY == DateTime.MinValue)
                    //{
                    //    ErrorMsg = "請輸入學生生日";
                    //    return false;
                    //}

                    //if (DateTime.Compare((DateTime)StH01.BIRTHDAY, Data.BIRTHDAY) != 0)
                    //{
                    //    ErrorMsg = "學生生日輸入錯誤";
                    //    return false;
                    //}


                    ZZT08 T08 = Db.ZZT08.Where(a => a.SCHOOL_NO == StH01.SCHOOL_NO && a.USER_NO == StH01.USER_NO && a.PASSWORD == Data.STUDENT_PWD).FirstOrDefault();

                    if (T08 == null)
                    {
                        ErrorMsg = "學生登入密碼輸入錯誤，需注意大小寫";
                        return false;
                    }

                    if (Data.STUDENT_PWD != T08.PASSWORD)
                    {
                        ErrorMsg = "學生登入密碼輸入錯誤，需注意大小寫";
                        return false;
                    }
                }

            }


            return true;
        }


     


        private bool Save(ZZZI28AddViewModel Data, out string ErrorMsg)
        {
            ErrorMsg = string.Empty;

            HRMT06 T06 = new HRMT06();
            T06.SCHOOL_NO = Data.SCHOOL_NO;
            T06.PARENTS_USER_NO = Data.PARENTS_USER_NO;
            T06.STUDENT_USER_NO = Data.STUDENT_USER_NO;
            T06.CRE_DATE = DateTime.Now;
            T06.CRE_PERSON = Data.CRE_PERSON;

            Db.HRMT06.Add(T06);

            try
            {
                Db.SaveChanges();
            }
            catch (Exception EX)
            {
                ErrorMsg = "系統發生錯誤;原因:"+EX.Message;
                return false;
            }

            return true;
        }


        #region  Shared
        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }
        #endregion


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Db.Dispose();
            }
            base.Dispose(disposing);
        }

    }
}