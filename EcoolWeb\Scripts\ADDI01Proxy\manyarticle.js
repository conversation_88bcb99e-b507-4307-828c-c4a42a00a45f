// Modern jQuery code for ADDI01Proxy ManyArticle page
$(document).ready(function() {
    // 表單處理模組
    const formHandler = {
        init: function() {
            window.Add = this.submitForm.bind(this);
            this.bindEvents();
            this.setupKeyboardHandlers();
        },

        bindEvents: function() {
            // 綁定表單提交事件
            $('form[name="form1"]').on('submit', this.handleFormSubmit.bind(this));
        },

        setupKeyboardHandlers: function() {
            // Enter鍵提交表單
            $("form input").on('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.submitForm();
                    return false;
                }
                return true;
            });
        },

        submitForm: function() {
            if (this.validateForm()) {
                const form = document.form1;
                form.action = window.ADDI01PROXY_URLS.editAction;
                form.submit();
            }
        },

        handleFormSubmit: function(e) {
            if (!this.validateForm()) {
                e.preventDefault();
                return false;
            }
        },

        validateForm: function() {
            let msg = '';
            let isValid = true;

            // 驗證學生選擇
            const userNo = $('#Search_USER_NO').val();
            if (!userNo || userNo === '' || userNo === ' ') {
                const userLabel = $("label[for='Search_USER_NO']").text();
                msg += '請選擇「' + userLabel + '」\n';
                this.showFieldError('#Search_USER_NO', '請選擇' + userLabel);
                isValid = false;
            } else {
                this.hideFieldError('#Search_USER_NO');
            }

            // 驗證文章數量
            const numArticle = $('#Search_NumArticle').val();
            const numArticleLabel = $("label[for='Search_NumArticle']").text();
            
            if (!numArticle || numArticle === '') {
                msg += '請輸入「' + numArticleLabel + '」\n';
                this.showFieldError('#Search_NumArticle', '請輸入' + numArticleLabel);
                isValid = false;
            } else if (isNaN(numArticle)) {
                msg += '「' + numArticleLabel + '」請輸入數字\n';
                this.showFieldError('#Search_NumArticle', '請輸入數字');
                isValid = false;
            } else if (parseInt(numArticle) <= 0) {
                msg += '「' + numArticleLabel + '」必須大於0\n';
                this.showFieldError('#Search_NumArticle', '必須大於0');
                isValid = false;
            } else {
                this.hideFieldError('#Search_NumArticle');
            }

            if (!isValid) {
                alert(msg);
            }

            return isValid;
        },

        showFieldError: function(fieldSelector, message) {
            const $field = $(fieldSelector);
            const $parent = $field.closest('.form-group');
            
            // 移除現有錯誤
            $parent.find('.field-validation-error').remove();
            $field.removeClass('input-validation-error');
            
            // 添加新錯誤
            $field.addClass('input-validation-error');
            const $errorSpan = $('<span class="field-validation-error text-danger">' + message + '</span>');
            $field.closest('.col-md-9').append($errorSpan);
        },

        hideFieldError: function(fieldSelector) {
            const $field = $(fieldSelector);
            const $parent = $field.closest('.form-group');
            
            $field.removeClass('input-validation-error');
            $parent.find('.field-validation-error').remove();
        }
    };

    // 下拉選單處理模組
    const dropdownHandler = {
        init: function() {
            window.ChangeUSER_NOUseReplaceWith = this.updateUserDropdown.bind(this);
            window.SetUSER_NODDLEmpty = this.setUserDropdownEmpty.bind(this);
            this.bindEvents();
            this.initializeDropdowns();
        },

        bindEvents: function() {
            // 班級變更事件已在HTML中綁定，這裡添加額外的處理
            $('#Search_CLASS_NO').on('change', () => {
                this.updateUserDropdown();
                this.hideFieldError('#Search_CLASS_NO');
            });

            // 學生選擇變更事件
            $(document).on('change', '#Search_USER_NO', () => {
                this.hideFieldError('#Search_USER_NO');
            });

            // 文章數量輸入事件
            $('#Search_NumArticle').on('input blur', this.validateNumArticle.bind(this));
        },

        initializeDropdowns: function() {
            // 頁面載入時初始化下拉選單
            $(window).on('load', () => {
                this.updateUserDropdown();
            });
        },

        updateUserDropdown: function() {
            const selectedClassNo = $.trim($('#Search_CLASS_NO option:selected').val());
            
            if (selectedClassNo.length === 0) {
                this.setUserDropdownEmpty();
            } else {
                this.loadUserOptions(selectedClassNo);
            }
        },

        setUserDropdownEmpty: function() {
            const classLabel = $("label[for='Search_CLASS_NO']").text();
            $('#Search_USER_NO').empty();
            $('#Search_USER_NO').append(
                $('<option></option>')
                    .val(' ')
                    .text('請選擇' + classLabel + '...')
                    .prop('selected', true)
            );
        },

        loadUserOptions: function(classNo) {
            $.ajax({
                url: window.ADDI01PROXY_URLS.getUserDropdownHtml,
                data: {
                    tagId: 'Search_USER_NO',
                    tagName: 'Search.USER_NO',
                    CLASS_NO: classNo
                },
                type: 'post',
                cache: false,
                async: false,
                dataType: 'html',
                success: (data) => {
                    if (data.length > 0) {
                        $('#Search_USER_NO').replaceWith(data);
                    }
                },
                error: (xhr, status, error) => {
                    console.error('載入學生選項失敗:', error);
                    this.setUserDropdownEmpty();
                }
            });
        },

        validateNumArticle: function(e) {
            const $field = $(e.target);
            const value = $field.val();
            
            if (value === '') {
                this.hideFieldError('#Search_NumArticle');
                return;
            }
            
            if (isNaN(value)) {
                this.showFieldError('#Search_NumArticle', '請輸入數字');
            } else if (parseInt(value) <= 0) {
                this.showFieldError('#Search_NumArticle', '必須大於0');
            } else {
                this.hideFieldError('#Search_NumArticle');
            }
        },

        showFieldError: function(fieldSelector, message) {
            formHandler.showFieldError(fieldSelector, message);
        },

        hideFieldError: function(fieldSelector) {
            formHandler.hideFieldError(fieldSelector);
        }
    };

    // AJAX內容更新模組
    const contentUpdater = {
        init: function() {
            window.funAjax = this.updatePageContent.bind(this);
        },

        updatePageContent: function() {
            const data = {
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
                Page: $('#Page').val(),
                whereKeyword: $('#whereKeyword').val(),
                whereAPPLY_STATUS: $('#whereAPPLY_STATUS').val()
            };

            $.ajax({
                url: window.ADDI01PROXY_URLS.pageContentAction,
                data: data,
                method: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: (data) => {
                    $('#PageContent').html(data);
                },
                error: (xhr, status, error) => {
                    console.error('更新頁面內容失敗:', error);
                }
            });
        }
    };

    // 用戶體驗增強模組
    const uxEnhancer = {
        init: function() {
            this.enhanceFormElements();
            this.addLoadingStates();
            this.addTooltips();
        },

        enhanceFormElements: function() {
            // 為表單元素添加焦點效果
            $('.form-control').on('focus', function() {
                $(this).closest('.form-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.form-group').removeClass('focused');
            });
        },

        addLoadingStates: function() {
            // 為提交按鈕添加載入狀態
            $('.btn').on('click', function() {
                const $btn = $(this);
                const originalText = $btn.text();
                
                $btn.prop('disabled', true)
                    .html('<i class="fa fa-spinner fa-spin"></i> 處理中...');
                
                // 如果表單驗證失敗，恢復按鈕狀態
                setTimeout(function() {
                    if ($('.field-validation-error:visible').length > 0) {
                        $btn.prop('disabled', false).text(originalText);
                    }
                }, 100);
            });
        },

        addTooltips: function() {
            // 為表單元素添加提示
            $('#Search_CLASS_NO').attr('title', '請選擇班級');
            $('#Search_USER_NO').attr('title', '請選擇學生');
            $('#Search_NumArticle').attr('title', '請輸入要建立的文章數量');
        }
    };

    // 錯誤處理模組
    const errorHandler = {
        init: function() {
            this.setupErrorHandling();
        },

        setupErrorHandling: function() {
            $(window).on('error', function(e) {
                console.error('頁面錯誤:', e);
            });

            // AJAX錯誤處理
            $(document).ajaxError(function(event, xhr, settings, error) {
                console.error('AJAX錯誤:', {
                    url: settings.url,
                    error: error,
                    status: xhr.status
                });
            });
        }
    };

    // 初始化所有模組
    formHandler.init();
    dropdownHandler.init();
    contentUpdater.init();
    uxEnhancer.init();
    errorHandler.init();

    // 設置全局 URL 配置（需要在 CSHTML 中定義）
    window.ADDI01PROXY_URLS = window.ADDI01PROXY_URLS || {};
});
