﻿
@model ADDI11EditViewModel
@using EcoolWeb.Util;
@using ECOOL_APP.com.ecool.service

@{
    /**/
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    string UUIDstr = "N";
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {

        UUIDstr = "Y";

    }
    string ISTASKLISTSTR = "";
    ViewBag.Title = ViewBag.Panel_Title;
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    //Double Max_LAP_M = Math.Floor(Convert.ToDouble((2000.0 / Model.ONE_LAP_M) * 10)) / 10;
}


<style type="text/css">
    .css-table {
        display: table;
        border-collapse: collapse;
        width: 100%
    }

        .css-table .thead {
            display: table-header-group;
        }

        .css-table .tbody {
            display: table-row-group;
        }

        .css-table .tr {
            display: table-row;
            padding-bottom: 2px
        }

        .css-table .th, .css-table .td {
            display: table-cell;
            padding-left: 3px;
        }

        .css-table .th, .css-table .td_title {
            display: table-cell;
            padding-left: 3px;
            border: 1px solid #cccccc;
            color: black;
            background-color: chartreuse;
            text-align: center;
            line-height: 28px;
        }

    .input-group-btn {
        position: relative;
    }
</style>


@Html.Partial("_Notice")
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@if ("@UUIDstr" == "Y")
{
    ISTASKLISTSTR = "True";
}
@{ Html.RenderAction("_RunMenu", new { NowAction = "ADDRunIndex" }); }


<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<img src="~/Content/img/web-bar-Run.png" class="img-responsive" />
<br />

<div style="width:100%;">

    說明：<br />
    1.	本功能是利用數位學生證感應加「跑步圈數」<br />
    2.	如果是每跑一圈感應一次，請選每圈；如果是每兩圈感應，請選「每兩圈」。或其他應用<br />
    3.	輸入錯誤時，請到圈數紀錄裡編輯(編輯圈數)，編輯權限預設為導師和管理者，權限可以請管理者調整。<br />
    4.	感應必須間隔一秒以上，必注意看成功訊息<br />
</div>
<br /><br />

<div class="Div-EZ-ADDI09" style="background-color:#ffee92">
    <div class="form-horizontal" style="height:200px;">
        <label class="control-label">運動撲滿-感應跑步圈數</label>
        <br /><br />
        <div class="form-group text-center">
            <div class="col-md-6" style="margin-top:10px">
                @Html.ActionLink("每跑一圈紀錄", "LogRunIndexDetail", new { LAP = 1, ISTASKLIST = ISTASKLISTSTR }, new { @class = "btn-block btn btn-default", @role = "button" })
            </div>
            <div class="col-md-6" style="margin-top:10px">
                @Html.ActionLink("每跑2圈紀錄", "LogRunIndexDetail", new { LAP = 2, ISTASKLIST = ISTASKLISTSTR }, new { @class = "btn-block btn btn-default", @role = "button" })
            </div>
            <div class="col-md-6" style="margin-top:10px">
                @Html.ActionLink("每跑3圈紀錄", "LogRunIndexDetail", new { LAP = 3, ISTASKLIST = ISTASKLISTSTR }, new { @class = "btn-block btn btn-default", @role = "button" })
            </div>
            <div class="col-md-6" style="margin-top:10px">
                @Html.ActionLink("每跑4圈紀錄", "LogRunIndexDetail", new { LAP = 4, ISTASKLIST = ISTASKLISTSTR }, new { @class = "btn-block btn btn-default", @role = "button" })
            </div>

        </div>
        <div class="form-group">
            <label class="text-danger">
            </label>
        </div>
    </div>
</div>

