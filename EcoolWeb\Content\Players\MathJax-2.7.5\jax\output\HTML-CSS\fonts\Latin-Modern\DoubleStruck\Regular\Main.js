/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/DoubleStruck/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_DoubleStruck={directory:"DoubleStruck/Regular",family:"LatinModernMathJax_DoubleStruck",testString:"\u00A0\u2102\u210D\u2115\u2119\u211A\u211D\u2124\u213C\u213D\u213E\u213F\u2140\u2145\u2146",32:[0,0,332,0,0],160:[0,0,332,0,0],8450:[705,22,667,55,611],8461:[683,0,722,83,639],8469:[683,0,722,83,639],8473:[683,0,639,83,583],8474:[705,194,667,55,611],8477:[683,0,639,83,583],8484:[683,0,667,55,611],8508:[431,0,517,27,488],8509:[431,216,472,27,444],8510:[683,0,611,83,583],8511:[683,0,667,83,583],8512:[683,0,667,55,611],8517:[683,0,694,47,680],8518:[694,22,500,26,547],8519:[453,22,472,26,460],8520:[691,0,279,19,331],8521:[691,216,389,-69,441],120120:[683,0,611,27,583],120121:[683,0,639,83,583],120123:[683,0,694,83,639],120124:[683,0,611,83,583],120125:[683,0,611,83,583],120126:[705,22,667,55,611],120128:[683,0,334,83,251],120129:[683,22,639,55,555],120130:[683,0,639,83,583],120131:[683,0,611,83,583],120132:[683,0,722,83,639],120134:[705,22,667,55,611],120138:[705,22,611,55,555],120139:[683,0,611,27,583],120140:[683,22,722,83,639],120141:[683,0,611,27,583],120142:[683,0,833,27,805],120143:[683,0,667,55,611],120144:[683,0,611,27,583],120146:[453,22,500,27,444],120147:[694,22,628,55,599],120148:[453,22,472,27,444],120149:[694,22,500,27,444],120150:[453,22,472,27,444],120151:[716,0,389,55,388],120152:[453,216,500,27,444],120153:[694,0,572,55,516],120154:[691,0,279,33,245],120155:[691,216,389,0,355],120156:[694,0,544,55,516],120157:[694,0,279,55,223],120158:[453,0,722,55,667],120159:[453,0,572,55,516],120160:[453,22,472,27,444],120161:[453,194,628,55,599],120162:[453,194,500,27,444],120163:[453,0,544,55,516],120164:[453,22,389,27,360],120165:[694,22,417,55,388],120166:[431,22,528,55,472],120167:[431,0,472,27,443],120168:[431,0,667,27,639],120169:[431,0,472,27,444],120170:[431,216,472,27,443],120171:[431,0,472,27,444],120792:[666,22,556,55,499],120793:[644,0,556,55,499],120794:[666,0,556,55,499],120795:[666,22,556,55,499],120796:[644,0,556,55,499],120797:[644,22,556,55,499],120798:[666,22,556,55,499],120799:[644,0,556,55,499],120800:[666,22,556,55,499],120801:[666,22,556,55,499]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_DoubleStruck"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/DoubleStruck/Regular/Main.js"]);
