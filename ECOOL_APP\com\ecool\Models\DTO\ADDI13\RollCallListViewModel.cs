﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class RollCallListViewModel
    {
        /// <summary>
        ///點名 ID
        /// </summary>
        [DisplayName("點名 ID")]
        public string ROLL_CALL_ID { get; set; }

        /// <summary>
        ///活動名稱
        /// </summary>
        [DisplayName("活動名稱")]
        public string ROLL_CALL_NAME { get; set; }

        /// <summary>
        ///活動開始日
        /// </summary>
        [DisplayName("活動開始日")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm}", ApplyFormatInEditMode = true)]
        public DateTime? ROLL_CALL_DATES { get; set; }

        /// <summary>
        ///活動結束日
        /// </summary>
        [DisplayName("活動結束日")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm}", ApplyFormatInEditMode = true)]
        public DateTime? ROLL_CALL_DATEE { get; set; }

        [DisplayName("承辦人")]
        public string CRE_PERSON_NAME { get; set; }

        [DisplayName("報到人數")]
        public int ROLL_CALL_COUNT { get; set; }

        [DisplayName("點數")]
        public short CASH { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///1.點名前 ,2 點名後,3.己給點
        /// </summary>
        public byte? STATUS { get; set; }
    }
}