/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"]={directory:"General/BoldItalic",family:"STIXGeneral",weight:"bold",style:"italic",Ranges:[[0,127,"BasicLatin"],[160,255,"Latin1Supplement"],[256,383,"LatinExtendedA"],[384,591,"LatinExtendedB"],[592,687,"IPAExtensions"],[688,767,"SpacingModLetters"],[880,1023,"GreekAndCoptic"],[1024,1279,"Cyrillic"],[7680,7935,"LatinExtendedAdditional"],[8192,8303,"GeneralPunctuation"],[8352,8399,"CurrencySymbols"],[8400,8447,"CombDiactForSymbols"],[8448,8527,"LetterlikeSymbols"],[8704,8959,"MathOperators"],[9216,9279,"ControlPictures"],[9312,9471,"EnclosedAlphanum"],[9472,9599,"BoxDrawing"],[64256,64335,"AlphaPresentForms"],[119912,119963,"MathBoldItalic"],[120016,120067,"MathBoldScript"],[120380,120431,"MathSSItalicBold"],[120604,120661,"GreekBoldItalic"],[120720,120777,"GreekSSBoldItalic"]]};MathJax.OutputJax["HTML-CSS"].initFont("STIXGeneral-bold-italic");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/Main.js");
