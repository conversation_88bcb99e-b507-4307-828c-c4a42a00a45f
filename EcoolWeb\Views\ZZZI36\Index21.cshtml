﻿@{
    ViewBag.Title = "老師內碼匯入";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<br />
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("ImportTeacherCard", "ZZZI36", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.ValidationSummary()
    @Html.AntiForgeryToken()

    <button type="button" class="btn btn-lg btn-default" data-toggle="modal" data-target="#excelModal">
        匯入Excel
    </button> <br />

    @Html.Partial("_ExcelImportModal2")
}