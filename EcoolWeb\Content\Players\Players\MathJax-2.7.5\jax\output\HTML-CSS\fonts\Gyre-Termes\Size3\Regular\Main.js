/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Size3={directory:"Size3/Regular",family:"GyreTermesMathJax_Size3",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[949,449,491,131,404],41:[949,449,491,87,360],47:[1149,649,798,80,718],91:[961,461,434,131,347],92:[1149,649,798,80,718],93:[961,461,434,87,303],123:[951,451,459,87,372],124:[941,441,211,80,131],125:[951,451,459,87,372],160:[0,0,250,0,0],770:[698,-536,870,0,870],771:[688,-532,863,0,863],774:[694,-541,885,0,885],780:[694,-532,870,0,870],785:[707,-554,885,0,885],812:[-70,232,870,0,870],813:[-80,242,870,0,870],814:[-70,223,885,0,885],815:[-88,241,885,0,885],816:[-88,244,863,0,863],8214:[941,441,362,80,282],8260:[1149,649,798,80,718],8425:[736,-548,1500,0,1500],8730:[1192,666,634,120,660],8739:[941,441,211,80,131],8741:[941,441,362,80,282],8968:[961,441,434,131,347],8969:[961,441,434,87,303],8970:[941,461,434,131,347],8971:[941,461,434,87,303],9001:[1153,653,439,87,352],9002:[1153,653,439,87,352],9140:[736,-548,1500,0,1500],9141:[-98,286,1500,0,1500],9180:[736,-541,2014,0,2014],9181:[-91,286,2014,0,2014],9182:[771,-532,2019,0,2019],9183:[-82,320,2019,0,2019],9184:[738,-545,2072,0,2072],9185:[-95,288,2072,0,2072],10214:[961,461,442,131,355],10215:[961,461,442,87,311],10216:[1153,653,439,87,352],10217:[1153,653,439,87,352],10218:[1153,653,665,87,578],10219:[1153,653,665,87,578],10222:[948,448,363,131,276],10223:[948,448,363,87,232]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Size3"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size3/Regular/Main.js"]);
