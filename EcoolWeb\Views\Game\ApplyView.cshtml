﻿@model GameLeveViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();
}
<link href="~/Content/css/childrens-month.min.css" rel="stylesheet" />
@using (Html.BeginForm("ApplyView", "Game", FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>報名(查詢點數)</h1>
                </div>
                <div class="panel-body">
                    <div >
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                            @Html.EditorFor(model => model.GameUserID, new { htmlAttributes = new { @class = "form-control", @placeholder = "Username", @type = "number", @min = "0", @max = "9999999999", @onKeyPress = "call(event,this);" } })
                        </div>
                        @Html.ValidationMessageFor(model => model.GameUserID, "", new { @class = "text-danger" })
                    </div>
                    <br />
                    <div class="text-center">
                        <h4>


                            1.未報到的「學生」請感應數位學生證報到。<br />
                            2.使用手機產生 QR Code的「訪客」，請利用本次QR Code感應。<br />
                            3.使用臨時卡「訪客」請至「測試及開卡」機器「開卡」<br />
                        </h4>
                        <br />
                        <div>
                            <img src="~/Content/img/Asset1.png" style="height:100px;padding-right:10px" />
                            <img src="~/Content/img/Asset2.png" style="height:100px;" />
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin: 0px auto;text-align:center">
                <img id="ViewImg" src="@Model.Details.LEVEL_IMG_PATH" class="img-responsive" style="margin: 0 auto;" />
            </div>
        </div>
    </div>

    <div id="DivAddButton">
        <i id="title" class="fa fa-arrow-left fa-3x"></i>
        <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
    </div>

}

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

        $(document).ready(function () {

               $(document).on('keypress', function (e) {
                if (e.which == 13) {

                    var GameUserID = $("#@Html.IdFor(m=>m.GameUserID)");

                    if ($('#loading').is(":hidden")) {

                        if (GameUserID.is(":focus")==false) {
                            GameUserID.focus();
                        }
                    }
                    else {
                        return false;
                    }

                }
            });

            $("#@Html.IdFor(m=>m.GameUserID)").focus();

            Wa_SetImgAutoSize()

        });

        function Wa_SetImgAutoSize() {
            var img = $('#ViewImg'); //獲取圖片

            var windowHeight = $(window).height();
            var panelHeight = $("#panel").height();

            var MaxHeight = (windowHeight - panelHeight - 30); //設置圖片高度界限

            if (MaxHeight > 200) {
                var HeightWidth = img.offsetHeight / img.offsetWidth; //設置高寬比
                var WidthHeight = img.offsetWidth / img.offsetHeight; //設置寬高比

                img.height = MaxHeight;
                img.width = MaxHeight * WidthHeight;

                (img).css({
                    "width": img.width, "height": img.height
                });
            }
        }

        function OnBack() {
             $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")
             $(targetFormID).submit();
         }

        function OnKeyinUse() {
                   $(targetFormID).attr("action", "@Url.Action("ApplySave", "Game")")
                   $(targetFormID).submit();
        }

        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                event.preventDefault();

                var GameUserID = $('#@Html.IdFor(m=>m.GameUserID)');
                GameUserID.prop('readonly', true);

                var OK = false;

                if (GameUserID.val().length == 10) {
                    OK = true;
                }
                else if (GameUserID.val().length >= 20)
                {
                    var ThisVal = GameUserID.val().substring(0, 10);
                    GameUserID.val(ThisVal)
                    OK = true;
                }
                else {
                    GameUserID.val('')
                }

                if (OK) {
                    setTimeout(function () {
                        OnKeyinUse();
                    });
                }
                else {
                    GameUserID.prop('readonly', false);
                }
            }
        }
    </script>
}