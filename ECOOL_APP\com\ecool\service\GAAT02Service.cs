﻿using Dapper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Utilities;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.SqlServer;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class GAAT02Service
    {
        /// <summary>
        /// 不同模式轉換成 每個人未配載原因清單
        /// </summary>
        /// <param name="model"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public GAAI01WearIndexViewModel ConvertToWearUserMemo(GAAI01WearIndexViewModel model, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            model.Users = (from x in db.HRMT01
                           where x.SCHOOL_NO == SCHOOL_NO && x.CLASS_NO == model.WhereCLASS_NO && x.USER_STATUS != UserStaus.Invalid && x.USER_TYPE == UserType.Student
                           select new GAAI01WearUserMemoViewModel()
                           {
                               USER_NO = x.USER_NO,
                               NAME = x.NAME,
                               SNAME = x.SNAME,
                               SEAT_NO = x.SEAT_NO,
                           }).ToListNoLock();

            if (model.Users?.Count > 0)
            {
                if (model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.未配戴點選登記)
                {
                    model.Users.Select(a =>
                    {
                        a.IS_WEAR = model.USER_NOs != null ? model.USER_NOs.Where(x => x.Equals(a.USER_NO)).Any() ? false : true : true;
                        return a;
                    }).ToList();
                }
                else if (model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.有配戴點選登記)
                {
                    model.Users.Select(a =>
                    {
                        a.IS_WEAR = model.USER_NOs != null ? model.USER_NOs.Where(x => x.Equals(a.USER_NO)).Any() ? true : false : false;
                        return a;
                    }).ToList();
                }
                else if (model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.有配戴感應登記)
                {
                    model.Users.Select(a =>
                    {
                        a.IS_WEAR = model.TagWearDetails != null ? model.TagWearDetails.Where(x => x.USER_NO == a.USER_NO).Any() ? true : false : false;
                        return a;
                    }).ToList();
                }
                else if (model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.全部都配戴登記)
                {
                    model.Users.Select(a =>
                    {
                        a.IS_WEAR = true;
                        return a;
                    }).ToList();
                }

                model.Users.Select(a =>
                {
                    a.UN_WEAR_TYPE = a.UN_WEAR_TYPE == null && a.IS_WEAR == false ? GAAT02_U.UnWearType.忘記帶 : (GAAT02_U.UnWearType?)null;
                    return a;
                }).ToList();
            }

            return model;
        }

        /// <summary>
        /// 學生是否配載防身警報器 資料處理
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public IResult SaveUnWearMemoData(GAAI01WearIndexViewModel model, UserProfile user, ref ECOOL_DEVEntities db, ref List<Tuple<string, string, int>> valuesList)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    try
                    {
                        StringHelper stringHelper = new StringHelper();

                        //取出學年度及學期
                        int SYear;
                        int Semesters;
                        SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                        //主檔
                        GAAT02_C SaveUp = null;
                        SaveUp = db.GAAT02_C.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.ALARM_ID == model.WhereALARM_ID && a.CLASS_NO == model.WhereCLASS_NO).FirstOrDefault();

                        if (SaveUp == null)
                        {
                            SaveUp = new GAAT02_C();
                            SaveUp.ALARM_ID = model.WhereALARM_ID;
                            SaveUp.SCHOOL_NO = user.SCHOOL_NO;
                            SaveUp.CLASS_NO = model.WhereCLASS_NO;
                            SaveUp.GRADE = byte.Parse(stringHelper.StrLeft(model.WhereCLASS_NO, 1));
                            SaveUp.SYEAR = (byte)SYear;
                            SaveUp.STUDENT_NUMBER = model.Users.Count();
                            SaveUp.WEAR_NUMBER = model.Users.Where(x => x.IS_WEAR == true).Count();
                            SaveUp.WEAR_RATE = Convert.ToDecimal((double)SaveUp.WEAR_NUMBER / (double)SaveUp.STUDENT_NUMBER);
                            SaveUp.TEACHER_NAME = HRMT01.GetTeacherName(SaveUp.SCHOOL_NO, SaveUp.CLASS_NO, ref db);
                            SaveUp.CHG_PERSON = user?.USER_KEY;
                            SaveUp.CHG_DATE = DateTime.Now;
                            db.Entry(SaveUp).State = System.Data.Entity.EntityState.Added;
                            db.GAAT02_C.Add(SaveUp);
                        }
                        else
                        {
                            SaveUp.STUDENT_NUMBER = model.Users.Count();
                            SaveUp.WEAR_NUMBER = model.Users.Where(x => x.IS_WEAR == true).Count();
                            SaveUp.WEAR_RATE = Convert.ToDecimal((double)SaveUp.WEAR_NUMBER / (double)SaveUp.STUDENT_NUMBER);
                            SaveUp.TEACHER_NAME = HRMT01.GetTeacherName(SaveUp.SCHOOL_NO, SaveUp.CLASS_NO, ref db);
                            SaveUp.CHG_PERSON = user?.USER_KEY;
                            SaveUp.CHG_DATE = DateTime.Now;
                            db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                        }

                        GAAT03Service gAAT03Service = new GAAT03Service();
                        var gAAT03 = gAAT03Service.GetGAAT03(SaveUp.SCHOOL_NO, ref db);

                        var USER_NOs = model.Users.Select(a => a.USER_NO).ToList();

                        var oGAAT02_Us = db.GAAT02_U.Where(x => x.ALARM_ID == SaveUp.ALARM_ID && x.SCHOOL_NO == SaveUp.SCHOOL_NO && USER_NOs.Contains(x.USER_NO)).ToList();

                        List<GAAT02_U> CreGAAT02Us = new List<GAAT02_U>();
                        List<GAAT02_U> UpdateGAAT02Us = new List<GAAT02_U>();

                        foreach (var item in model.Users)
                        {
                            var thisGAAT02_U = oGAAT02_Us.Where(x => x.ALARM_ID == SaveUp.ALARM_ID && x.SCHOOL_NO == SaveUp.SCHOOL_NO
                            && x.CLASS_NO == SaveUp.CLASS_NO && x.USER_NO == item.USER_NO).FirstOrDefault();

                            if (thisGAAT02_U == null)
                            {
                                thisGAAT02_U = new GAAT02_U();

                                thisGAAT02_U.ALARM_ID = SaveUp.ALARM_ID;
                                thisGAAT02_U.SCHOOL_NO = SaveUp.SCHOOL_NO;
                                thisGAAT02_U.CLASS_NO = SaveUp.CLASS_NO;
                                thisGAAT02_U.USER_NO = item.USER_NO;
                                thisGAAT02_U.NAME = item.NAME;
                                thisGAAT02_U.SNAME = item.SNAME;
                                thisGAAT02_U.SEAT_NO = item.SEAT_NO;
                                thisGAAT02_U.IS_WEAR = item.IS_WEAR;
                                thisGAAT02_U.UN_WEAR_TYPE = (byte?)item.UN_WEAR_TYPE ?? null;

                                if (thisGAAT02_U.IS_WEAR == true)
                                {
                                    thisGAAT02_U.CASH = gAAT03?.ALARM_CASH ?? 0;
                                }
                                else
                                {
                                    thisGAAT02_U.CASH = 0;
                                }

                                CreGAAT02Us.Add(thisGAAT02_U);
                                db.GAAT02_U.Add(thisGAAT02_U);
                                //這次有配載 ,需加點
                                if (thisGAAT02_U.CASH > 0)
                                {
                                    ECOOL_APP.CashHelper.AddCash(user, (int)thisGAAT02_U.CASH, thisGAAT02_U.SCHOOL_NO, thisGAAT02_U.USER_NO, "GAAT02_U", thisGAAT02_U.ALARM_ID + Guid.NewGuid().ToString("N")
                                        , $"{model.ALARM_DATE}，抽查防身警報器，有配載警報器，獲得 {thisGAAT02_U.CASH} 點", true, ref db,"","",ref valuesList);
                                }
                            }
                            else
                            {
                                if ((byte?)item.UN_WEAR_TYPE != thisGAAT02_U.UN_WEAR_TYPE)
                                {
                                    //同日期 - 這次未配載 , 上次有配載，需扣點
                                    if (item.IS_WEAR == false && thisGAAT02_U.IS_WEAR == true && thisGAAT02_U.CASH > 0)
                                    {
                                        var O_CASH = (int)(thisGAAT02_U.CASH * -1);

                                        ECOOL_APP.CashHelper.AddCash(user, O_CASH, thisGAAT02_U.SCHOOL_NO, thisGAAT02_U.USER_NO, "GAAT02_U", thisGAAT02_U.ALARM_ID + Guid.NewGuid().ToString("N")
                                            , $"取消-原「{model.ALARM_DATE}，抽查防身警報器，有配載警報器 ，獲得的點數」", true, ref db,"", "",ref valuesList);
                                    }

                                    thisGAAT02_U.IS_WEAR = item.IS_WEAR;
                                    thisGAAT02_U.UN_WEAR_TYPE = (byte?)item.UN_WEAR_TYPE ?? null;

                                    if (thisGAAT02_U.IS_WEAR == true)
                                    {
                                        thisGAAT02_U.CASH = gAAT03?.ALARM_CASH ?? 0;
                                    }
                                    else
                                    {
                                        thisGAAT02_U.CASH = 0;
                                    }
                                    UpdateGAAT02Us.Add(thisGAAT02_U);
                                    db.Entry(thisGAAT02_U).State = System.Data.Entity.EntityState.Modified;
                                    //這次有配載 ,需加點
                                    if (thisGAAT02_U.CASH > 0)
                                    {
                                        ECOOL_APP.CashHelper.AddCash(user, (int)thisGAAT02_U.CASH, thisGAAT02_U.SCHOOL_NO, thisGAAT02_U.USER_NO, "GAAT02_U", thisGAAT02_U.ALARM_ID + Guid.NewGuid().ToString("N")
                                            , $"{model.ALARM_DATE}，抽查防身警報器，有配載警報器，獲得 {thisGAAT02_U.CASH} 點", true, ref db,"","",ref valuesList);
                                    }
                                }
                            }
                        }

                        //if (CreGAAT02Us?.Count() > 0)
                        //{
                        //    EFBatchOperation.For(db, db.GAAT02_U).InsertAll(CreGAAT02Us);
                        //}

                        //if (UpdateGAAT02Us?.Count() > 0)
                        //{
                        //    EFBatchOperation.For(db, db.GAAT02_U).UpdateAll(UpdateGAAT02Us, x => x.ColumnsToUpdate(u => u.IS_WEAR, u => u.UN_WEAR_TYPE, u => u.CASH));
                        //}

                        try
                        {
                            db.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            result.Exception = ex;
                            result.Message += ex.Message;
                            return result;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        /// <summary>
        /// 判斷這個班級這個區間是否填報過
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="ALARM_ID"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public bool IsWearData(string SCHOOL_NO, string CLASS_NO, string ALARM_ID, ref ECOOL_DEVEntities db)
        {
            return db.GAAT02_C.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.CLASS_NO == CLASS_NO && x.ALARM_ID == ALARM_ID).NoLock(x => x.Any());
        }

        public List<GAAI01ShowClassIsWearListViewModel> GetShowClassIsWearList(string SCHOOL_NO, string ALARM_ID, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"select b.ALARM_ID, c.SCHOOL_NO,c.CLASS_NO,case when isnull(d.ALARM_ID,'')<>'' Then 1 else 0 end as IsWear
                            from GAAT01 b (nolock)
                            join GAAT01_C c (nolock) on b.SYEAR=c.SYEAR and b.SEMESTER=c.SEMESTER and c.SCHOOL_NO=@SCHOOL_NO
                            left join GAAT02_C d (nolock)  on  d.ALARM_ID =b.ALARM_ID and c.SCHOOL_NO=d.SCHOOL_NO and c.CLASS_NO=d.CLASS_NO
                            where  b.ALARM_ID=@ALARM_ID
                            group by  b.ALARM_ID, c.SCHOOL_NO,c.CLASS_NO,case when isnull(d.ALARM_ID,'')<>'' Then 1 else 0 end
                            order by b.ALARM_ID, c.SCHOOL_NO,c.CLASS_NO";
            return db.Database.Connection.Query<GAAI01ShowClassIsWearListViewModel>(sSQL
             , new
             {
                 ALARM_ID = ALARM_ID,
                 SCHOOL_NO = SCHOOL_NO,
             }).ToList();
        }

        /// <summary>
        /// 取得這個學期此班的配戴資料統計
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01SearchClassListViewModel> GetClassList(byte SYEAR, byte SEMESTER, string SCHOOL_NO, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            var Temp = (from a in db.GAAT01
                        join b in db.GAAT02_C on a.ALARM_ID equals b.ALARM_ID
                        where a.SYEAR == SYEAR && a.SEMESTER == SEMESTER && b.SCHOOL_NO == SCHOOL_NO
                        select new GAAI01SearchClassListViewModel()
                        {
                            ALARM_ID = a.ALARM_ID,
                            CYCLE = a.CYCLE,
                            ALARM_DATES = a.ALARM_DATES,
                            ALARM_DATEE = a.ALARM_DATEE,
                            CLASS_NO = b.CLASS_NO,
                            STUDENT_NUMBER = b.STUDENT_NUMBER,
                            WEAR_NUMBER = b.WEAR_NUMBER,
                            UN_WEAR_NUMBER = (b.STUDENT_NUMBER - b.WEAR_NUMBER),
                            WEAR_RATE = b.WEAR_RATE
                        });

            if (!string.IsNullOrWhiteSpace(CLASS_NO))
            {
                Temp = Temp.Where(a => a.CLASS_NO == CLASS_NO);
            }

            return Temp.OrderBy(X => X.ALARM_ID).ToListNoLock();
        }

        /// <summary>
        /// 取得 人員配戴明細
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="IS_WEAR"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01SearchWearDetailListViewModel> GetWearDetailList(byte SYEAR, byte SEMESTER, string SCHOOL_NO, string CLASS_NO, bool? IS_WEAR, ref ECOOL_DEVEntities db)
        {
            var Temp = (from a in db.GAAT01
                        join b in db.GAAT02_C on a.ALARM_ID equals b.ALARM_ID
                        join c in db.GAAT02_U on new { b.ALARM_ID, b.SCHOOL_NO, b.CLASS_NO } equals new { c.ALARM_ID, c.SCHOOL_NO, c.CLASS_NO }
                        where a.SYEAR == SYEAR && a.SEMESTER == SEMESTER && b.SCHOOL_NO == SCHOOL_NO
                        select new GAAI01SearchWearDetailListViewModel()
                        {
                            ALARM_ID = a.ALARM_ID,
                            CYCLE = a.CYCLE,
                            ALARM_DATES = a.ALARM_DATES,
                            ALARM_DATEE = a.ALARM_DATEE,
                            SCHOOL_NO = b.SCHOOL_NO,
                            CLASS_NO = b.CLASS_NO,
                            USER_NO = c.USER_NO,
                            NAME = c.NAME,
                            SNAME = c.SNAME,
                            SEAT_NO = c.SEAT_NO,
                            IS_WEAR = c.IS_WEAR,
                            UN_WEAR_TYPE = c.UN_WEAR_TYPE,
                        });

            if (!string.IsNullOrWhiteSpace(CLASS_NO))
            {
                Temp = Temp.Where(b => b.CLASS_NO == CLASS_NO);
            }

            if (IS_WEAR != null)
            {
                Temp = Temp.Where(c => c.IS_WEAR == IS_WEAR);
            }

            Temp = Temp.OrderBy(a => a.ALARM_ID).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);

            return Temp.ToListNoLock();
        }

        /// <summary>
        /// 全校填報的班級數量資料
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01SearchNotReportedViewModel> GetClassReportedData(byte SYEAR, byte SEMESTER, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"select A.ALARM_ID,A.CYCLE,A.ALARM_DATES,A.ALARM_DATEE,S.CLASS_COUNT
            ,(SELECT COUNT(*) FROM GAAT02_C C(nolock) WHERE C.ALARM_ID = A.ALARM_ID AND C.SCHOOL_NO = S.SCHOOL_NO) AS ReportedClassCount
            from GAAT01 A (nolock)
            join GAAT01_S S (nolock) on A.SYEAR = S.SYEAR AND A.SEMESTER = S.SEMESTER
            WHERE S.SCHOOL_NO = @SCHOOL_NO and A.SYEAR=@SYEAR and A.SEMESTER =@SEMESTER
            ORDER BY A.ALARM_ID ";
            var temp = db.Database.Connection.Query<GAAI01SearchNotReportedViewModel>(sSQL
           , new
           {
               SYEAR = SYEAR,
               SEMESTER = SEMESTER,
               SCHOOL_NO = SCHOOL_NO,
           }).ToList();

            if (temp != null)
            {
                temp = temp.Select(a =>
                {
                    a.NotReportedClassCount = a.CLASS_COUNT - a.ReportedClassCount;
                    a.ReportedClassRate = a.ReportedClassCount > 0 ? (decimal)a.ReportedClassCount / a.CLASS_COUNT : 0;
                    return a;
                }).ToList();
            }

            return temp;
        }

        /// <summary>
        /// 全校配戴率
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01SearchSchoolRateViewModel> GetSchoolRateData(byte SYEAR, byte SEMESTER, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"select A.ALARM_ID,A.CYCLE,A.ALARM_DATES,A.ALARM_DATEE,S.STUDENT_NUMBER
           ,Isnull((SELECT sum(WEAR_NUMBER) FROM GAAT02_C C  (nolock) WHERE C.ALARM_ID=A.ALARM_ID AND C.SCHOOL_NO=S.SCHOOL_NO),0) AS WEAR_NUMBER
           ,Isnull((SELECT sum(STUDENT_NUMBER-WEAR_NUMBER) FROM GAAT02_C C  (nolock) WHERE C.ALARM_ID=A.ALARM_ID AND C.SCHOOL_NO=S.SCHOOL_NO),0) AS UN_WEAR_NUMBER
            from GAAT01 A (nolock)
            join GAAT01_S S (nolock) on A.SYEAR = S.SYEAR AND A.SEMESTER = S.SEMESTER
            WHERE S.SCHOOL_NO = @SCHOOL_NO and A.SYEAR=@SYEAR and A.SEMESTER =@SEMESTER
            ORDER BY A.ALARM_ID ";

            var temp = db.Database.Connection.Query<GAAI01SearchSchoolRateViewModel>(sSQL
           , new
           {
               SYEAR = SYEAR,
               SEMESTER = SEMESTER,
               SCHOOL_NO = SCHOOL_NO,
           }).ToList();

            if (temp != null)
            {
                temp = temp.Select(a =>
                {
                    a.NotReported_NUMBER = a.STUDENT_NUMBER - a.WEAR_NUMBER - a.UN_WEAR_NUMBER;
                    a.WEAR_RATE = a.WEAR_NUMBER > 0 ? (decimal)a.WEAR_NUMBER / a.STUDENT_NUMBER : 0;
                    return a;
                }).ToList();
            }

            return temp;
        }

        /// <summary>
        /// 管理者統計報表 - 各未配載原因統計人數
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01SearchUnWearMemoCountViewModel> GetUnWearMemoCount(byte SYEAR, byte SEMESTER, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"select A.ALARM_ID,A.CYCLE,A.ALARM_DATES,A.ALARM_DATEE,S.STUDENT_NUMBER,U.UN_WEAR_TYPE,COUNT(U.USER_NO) WEAR_TYPE_STUDENT_NUMBER
            from GAAT01 A (nolock)
            join GAAT01_S S (nolock) on A.SYEAR = S.SYEAR AND A.SEMESTER =S.SEMESTER
            LEFT OUTER JOIN GAAT02_U U (nolock) on S.SCHOOL_NO=U.SCHOOL_NO AND A.ALARM_ID = U.ALARM_ID
            WHERE S.SCHOOL_NO = @SCHOOL_NO and A.SYEAR=@SYEAR and A.SEMESTER =@SEMESTER
            GROUP BY A.ALARM_ID,A.CYCLE,A.ALARM_DATES,A.ALARM_DATEE,S.STUDENT_NUMBER,U.UN_WEAR_TYPE
            ORDER BY A.ALARM_ID";

            var temp = db.Database.Connection.Query<GAAI01SearchUnWearMemoCountViewModel>(sSQL
           , new
           {
               SYEAR = SYEAR,
               SEMESTER = SEMESTER,
               SCHOOL_NO = SCHOOL_NO,
           }).ToList();

            return temp;
        }

        /// <summary>
        ///  督學月統計表 年級  - 取出這個月最好的配載率反堆配載人數
        /// </summary>
        /// <param name="YYMM"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01MonthMaxGradeWearrRateViewModel> GetMonthMaxGradeWearrRate(string YYMM, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            var temp = this.GetMonthMaxClassWearrRate(YYMM, SCHOOL_NO, ref db);

            if (temp != null)
            {
                var Gtemp = (from p in temp
                             group p by new { p.GRADE } into g
                             select new GAAI01MonthMaxGradeWearrRateViewModel
                             {
                                 GRADE = g.Key.GRADE,
                                 STUDENT_NUMBER = g.Sum(p => p.STUDENT_NUMBER),
                                 WEAR_RATE = g.Average(p => p.WEAR_RATE),
                                 WEAR_NUMBER = g.Sum(p => p.WEAR_NUMBER),
                             }).ToList();

                Gtemp = Gtemp.Select(a =>
                {
                    a.UN_WEAR_MEMO = temp.Where(x => x.GRADE == a.GRADE).Where(x => !string.IsNullOrWhiteSpace(x.UN_WEAR_MEMO)).Any() ?
                    String.Join(",", temp.Where(x => x.GRADE == a.GRADE).Where(x => !string.IsNullOrWhiteSpace(x.UN_WEAR_MEMO)).Select(x => x.UN_WEAR_MEMO).Distinct().ToList()) : "";
                    return a;
                }).ToList();

                return Gtemp;
            }

            return null;
        }
        public List<GAAI01MonthMaxGradeWearrRateViewModel> GetWeekMaxGradeWearrRate(string Weekno, string SCHOOL_NO,string Year, ref ECOOL_DEVEntities db)
        {
            var temp = this.GetWeekMaxClassWearrRate(Weekno, SCHOOL_NO, Year,ref db);

            if (temp != null)
            {
                var Gtemp = (from p in temp
                            
                             group p by new { p.GRADE } into g
                             select new GAAI01MonthMaxGradeWearrRateViewModel
                             {
                                 GRADE = g.Key.GRADE,
                                 STUDENT_NUMBER = g.Sum(p =>p.STUDENT_NUMBER),
                                 WEAR_RATE = g.Max(p => p.WEAR_RATE),
                                 WEAR_NUMBER = g.Sum(p => p.WEAR_NUMBER),
                             }).ToList();

                Gtemp = Gtemp.Select(a =>
                {
                    a.UN_WEAR_MEMO = temp.Where(x => x.GRADE == a.GRADE).Where(x => !string.IsNullOrWhiteSpace(x.UN_WEAR_MEMO)).Any() ?
                    String.Join(" ", temp.Where(x => x.GRADE == a.GRADE).Where(x => !string.IsNullOrWhiteSpace(x.UN_WEAR_MEMO)).Select(x => x.UN_WEAR_MEMO).Distinct().ToList()) : "";
                    return a;
                }).ToList();

                return Gtemp;
            }

            return null;
        }
        public List<GAAI01MonthMaxClassWearrRateViewModel> GetWeekMaxClassWearrRate(string Weekno, string SCHOOL_NO, string Year,ref ECOOL_DEVEntities db)
        {
            var whereSql = "";
            var ssQLs = "";
            DateTime? SDATE = null;
            DateTime? EDATE1 = null;
            if (!string.IsNullOrEmpty(Weekno) && Weekno.Length > 0)
            {
               
                //string MM = YYMM.Substring(4);
                //int YY = Int32.Parse(YYMM.Substring(0, 4));
                
                WeekSet weekSet = new WeekSet();
                weekSet = db.WeekSet.Where(x => x.weekNO == Weekno && x.Year ==Year).FirstOrDefault();
                SDATE = weekSet.weekStartDate;
                EDATE1 = ((DateTime)weekSet.weekEtartDate).AddHours(23).AddMinutes(59).AddSeconds(59);
               
               
                //else if (SDATE != null)
                //{
                //    string SDateSTR = "";
                //    SDateSTR = YY.ToString() + (((DateTime)SDATE).Month).ToString().PadLeft(2, '0'); ;
                //    whereSql = " where CONVERT(varchar(6),b.ALARM_DATES,112)= '" + SDateSTR + "' and CONVERT(varchar(6),b.ALARM_DATEE,112)= @YYMM";
                //}
            
                    whereSql = " where ALARM_DATEE between @SDATE and  @EDATE1 or ALARM_DATES between @SDATE and  @EDATE1";
              
            }
            string sSQL = $@"
                       select c.SCHOOL_NO,c.GRADE,c.CLASS_NO,c.STUDENT_NUMBER
                            ,isnull(max(d.WEAR_RATE),0) WEAR_RATE,isnull(max(d.WEAR_NUMBER),0) WEAR_NUMBER
                            from GAAT01 b (nolock)
                            join GAAT01_C c (nolock) on b.SYEAR=c.SYEAR and b.SEMESTER=c.SEMESTER and c.SCHOOL_NO=@SCHOOL_NO
                            left join GAAT02_C d (nolock)  on  d.ALARM_ID =b.ALARM_ID and c.SCHOOL_NO=d.SCHOOL_NO and c.CLASS_NO=d.CLASS_NO
                            {whereSql}
                            group by  c.SCHOOL_NO,c.GRADE,c.CLASS_NO,c.STUDENT_NUMBER ";

            var temp = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(sSQL
            , new
            {
                SDATE = SDATE,
                EDATE1= EDATE1,
                SCHOOL_NO = SCHOOL_NO,
            }).ToList();

            if (temp != null)
            {
                sSQL = $@" select b.ALARM_ID,b.ALARM_DATES,b.ALARM_DATEE,d.SCHOOL_NO,d.CLASS_NO,d.WEAR_RATE
                from GAAT01 b(nolock)
                join GAAT02_C d(nolock)  on d.ALARM_ID = b.ALARM_ID and d.SCHOOL_NO =@SCHOOL_NO
                {whereSql}
                  ";
                var Dtemp = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(sSQL
                 , new
                 {
                     SDATE = SDATE,
                     EDATE1 = EDATE1,
                     SCHOOL_NO = SCHOOL_NO,
                 }).ToList();

                sSQL = $@" select d.ALARM_ID, d.SCHOOL_NO,d.CLASS_NO,u.UN_WEAR_TYPE
                            from GAAT01 b (nolock)
                            join GAAT02_C d (nolock) on d.ALARM_ID = b.ALARM_ID and d.SCHOOL_NO = @SCHOOL_NO
                            join GAAT02_U u(nolock) on d.ALARM_ID = u.ALARM_ID and d.SCHOOL_NO = u.SCHOOL_NO and d.CLASS_NO = u.CLASS_NO
                          {whereSql}
                            and u.IS_WEAR = 0
                            group by d.ALARM_ID, d.SCHOOL_NO,d.CLASS_NO,u.UN_WEAR_TYPE";
                var Utemp = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(sSQL
                , new
                {
                    SDATE = SDATE,
                    EDATE1 = EDATE1,
                    SCHOOL_NO = SCHOOL_NO,
                }).ToList();

                if (Dtemp != null)
                {
                    temp = temp.Select(a =>
                    {
                        var ThisD = Dtemp.Where(x => x.CLASS_NO == a.CLASS_NO && x.WEAR_RATE == a.WEAR_RATE).FirstOrDefault();

                        if (ThisD != null)
                        {
                            a.ALARM_ID = ThisD.ALARM_ID;
                            a.ALARM_DATES = ThisD.ALARM_DATES;
                            a.ALARM_DATEE = ThisD.ALARM_DATEE;
                            a.UN_WEAR_MEMO = Utemp.Where(x => x.CLASS_NO == a.CLASS_NO && x.ALARM_ID == ThisD.ALARM_ID).Where(x => x.UN_WEAR_TYPE != 0).Any() ?
                                             String.Join(", ", Utemp.Where(x => x.CLASS_NO == a.CLASS_NO && x.ALARM_ID == ThisD.ALARM_ID).Where(x => x.UN_WEAR_TYPE != 0)
                                             .Select(x => GAAT02_U.GetUnWearTypeDesc(x.UN_WEAR_TYPE)).ToList()) : "";
                        }
                        return a;
                    }).ToList();
                }
            }

            return temp;
        }
        /// <summary>
        /// 督學月統計表 班級  - 取出這個月最好的配載率
        /// </summary>
        /// <param name="YYMM"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01MonthMaxClassWearrRateViewModel> GetMonthMaxClassWearrRate(string YYMM, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            var whereSql = "";
            var ssQLs = "";
            if (!string.IsNullOrEmpty(YYMM) && YYMM.Length > 0)
            {
                string MM = YYMM.Substring(4);
                int YY = Int32.Parse(YYMM.Substring(0, 4));
                DateTime? SDATE = null;
                DateTime? EDATE1 = null;
                ssQLs = $@"SELECT * FROM GAAT01 where CONVERT(varchar(6),ALARM_DATEE,112)=@YYMM order by CYCLE";

                var temp1 = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(ssQLs
                , new
                {
                    YYMM = YYMM,
                }).ToList();
                SDATE = temp1.Select(x => x.ALARM_DATES).FirstOrDefault();
                EDATE1 = temp1.Select(x => x.ALARM_DATES).LastOrDefault();
                if (MM == "12")
                {
                    string SDateSTR = YY.ToString() + "-01-01 00:00:00";
                    string EDateSTR = YY.ToString() + "-01-03 23:59:59";
                    whereSql = " where CONVERT(varchar(6),b.ALARM_DATES,112)= @YYMM and  b.ALARM_DATEE BETWEEN '" + SDateSTR + "' and '" + EDateSTR + "'";
                }
                if (MM == "08")
                {
                    ssQLs = $@"SELECT * FROM GAAT01 where CONVERT(varchar(6),ALARM_DATES,112)=@YYMM order by CYCLE";

                    temp1 = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(ssQLs
                    , new
                    {
                        YYMM = YYMM,
                    }).ToList();
                    SDATE = temp1.Select(x => x.ALARM_DATEE).FirstOrDefault();
                    EDATE1 = temp1.Select(x => x.ALARM_DATEE).LastOrDefault();
                    string SDateSTR = "";
                    SDateSTR = YY.ToString() + (((DateTime)SDATE).Month).ToString().PadLeft(2, '0');
                    whereSql = " where CONVERT(varchar(6),b.ALARM_DATEE,112)= '" + SDateSTR + "' and CONVERT(varchar(6),b.ALARM_DATES,112)= @YYMM";
                }
                //else if (SDATE != null)
                //{
                //    string SDateSTR = "";
                //    SDateSTR = YY.ToString() + (((DateTime)SDATE).Month).ToString().PadLeft(2, '0'); ;
                //    whereSql = " where CONVERT(varchar(6),b.ALARM_DATES,112)= '" + SDateSTR + "' and CONVERT(varchar(6),b.ALARM_DATEE,112)= @YYMM";
                //}
                else
                {
                    whereSql = " where CONVERT(varchar(6),b.ALARM_DATES,112)= @YYMM and CONVERT(varchar(6),b.ALARM_DATEE,112)= @YYMM";
                }
            }
            string sSQL = $@"
                       select c.SCHOOL_NO,c.GRADE,c.CLASS_NO,c.STUDENT_NUMBER
                            ,isnull(max(d.WEAR_RATE),0) WEAR_RATE,isnull(max(d.WEAR_NUMBER),0) WEAR_NUMBER
                            from GAAT01 b (nolock)
                            join GAAT01_C c (nolock) on b.SYEAR=c.SYEAR and b.SEMESTER=c.SEMESTER and c.SCHOOL_NO=@SCHOOL_NO
                            left join GAAT02_C d (nolock)  on  d.ALARM_ID =b.ALARM_ID and c.SCHOOL_NO=d.SCHOOL_NO and c.CLASS_NO=d.CLASS_NO
                            {whereSql}
                            group by  c.SCHOOL_NO,c.GRADE,c.CLASS_NO,c.STUDENT_NUMBER ";

            var temp = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(sSQL
            , new
            {
                YYMM = YYMM,
                SCHOOL_NO = SCHOOL_NO,
            }).ToList();

            if (temp != null)
            {
                sSQL = $@" select b.ALARM_ID,b.ALARM_DATES,b.ALARM_DATEE,d.SCHOOL_NO,d.CLASS_NO,d.WEAR_RATE
                from GAAT01 b(nolock)
                join GAAT02_C d(nolock)  on d.ALARM_ID = b.ALARM_ID and d.SCHOOL_NO =@SCHOOL_NO
                {whereSql}
                  ";
                var Dtemp = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(sSQL
                 , new
                 {
                     YYMM = YYMM,
                     SCHOOL_NO = SCHOOL_NO,
                 }).ToList();

                sSQL = $@" select d.ALARM_ID, d.SCHOOL_NO,d.CLASS_NO,u.UN_WEAR_TYPE
                            from GAAT01 b (nolock)
                            join GAAT02_C d (nolock) on d.ALARM_ID = b.ALARM_ID and d.SCHOOL_NO = @SCHOOL_NO
                            join GAAT02_U u(nolock) on d.ALARM_ID = u.ALARM_ID and d.SCHOOL_NO = u.SCHOOL_NO and d.CLASS_NO = u.CLASS_NO
                          {whereSql}
                            and u.IS_WEAR = 0
                            group by d.ALARM_ID, d.SCHOOL_NO,d.CLASS_NO,u.UN_WEAR_TYPE";
                var Utemp = db.Database.Connection.Query<GAAI01MonthMaxClassWearrRateViewModel>(sSQL
                , new
                {
                    YYMM = YYMM,
                    SCHOOL_NO = SCHOOL_NO,
                }).ToList();

                if (Dtemp != null)
                {
                    temp = temp.Select(a =>
                    {
                        var ThisD = Dtemp.Where(x => x.CLASS_NO == a.CLASS_NO && x.WEAR_RATE == a.WEAR_RATE).FirstOrDefault();

                        if (ThisD != null)
                        {
                            a.ALARM_ID = ThisD.ALARM_ID;
                            a.ALARM_DATES = ThisD.ALARM_DATES;
                            a.ALARM_DATEE = ThisD.ALARM_DATEE;
                            a.UN_WEAR_MEMO = Utemp.Where(x => x.CLASS_NO == a.CLASS_NO && x.ALARM_ID == ThisD.ALARM_ID).Any() ?
                                             String.Join(", ", Utemp.Where(x => x.CLASS_NO == a.CLASS_NO && x.ALARM_ID == ThisD.ALARM_ID)
                                             .Select(x => GAAT02_U.GetUnWearTypeDesc(x.UN_WEAR_TYPE)).ToList()) : "";
                        }
                        return a;
                    }).ToList();
                }
            }

            return temp;
        }

        /// <summary>
        /// 班級統計表
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<GAAI01AVGClassWearrRateViewModel> GetAvgClassWearrRate(byte SYEAR, byte SEMESTER, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"Select c.SCHOOL_NO,c.GRADE,c.CLASS_NO,c.STUDENT_NUMBER
                            ,Sum(isnull(d.WEAR_RATE,0))/max(b.CYCLE) as WEAR_RATE,max(b.CYCLE) CYCLE
                            ,Sum(Case When isnull(d.ALARM_ID,'')<>'' Then 1 else 0 end ) as OK_CYCLE
                            from GAAT01 b (nolock)
                            join GAAT01_C c (nolock) on b.SYEAR=c.SYEAR and b.SEMESTER=c.SEMESTER and c.SCHOOL_NO=@SCHOOL_NO
                            left join GAAT02_C d (nolock)  on  d.ALARM_ID =b.ALARM_ID and c.SCHOOL_NO=d.SCHOOL_NO and c.CLASS_NO=d.CLASS_NO
                            where b.SYEAR=@SYEAR and b.SEMESTER=@SEMESTER
                            and b.ALARM_DATES <getdate()
                            group by  c.SCHOOL_NO,c.GRADE,c.CLASS_NO,c.STUDENT_NUMBER
                            order by c.SCHOOL_NO,c.GRADE,c.CLASS_NO,c.STUDENT_NUMBER";

            var temp = db.Database.Connection.Query<GAAI01AVGClassWearrRateViewModel>(sSQL
           , new
           {
               SYEAR = SYEAR,
               SEMESTER = SEMESTER,
               SCHOOL_NO = SCHOOL_NO,
           }).ToList();

            return temp;
        }

        public GAAI01SpotCheckPeopleViewModel SaveSpotCheckData(string SCHOOL_NO
            , string CARD_NO
            , bool SetIs_WEAR
            , short SetCASH
            , GAAT02_U.UnWearType SetUN_WEAR_TYPE
            , UserProfile user
            , ref ECOOL_DEVEntities db
            , ref string Message, ref List<Tuple<string, string, int>> valuesList)
               
        {
            var model = (from b in db.HRMT01
                         join c in db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                         where (
                                        (b.CARD_NO == CARD_NO)
                                     || (b.SCHOOL_NO + b.CLASS_NO + b.SEAT_NO == SCHOOL_NO + CARD_NO)
                                     || (b.SCHOOL_NO + b.USER_NO == SCHOOL_NO + CARD_NO)
                                )
                         && b.USER_TYPE == UserType.Student
                         && (!UserStaus.NGUserStausList.Contains(b.USER_STATUS))
                         select new GAAI01SpotCheckPeopleViewModel()
                         {
                             SCHOOL_NO = b.SCHOOL_NO,
                             SHORT_NAME = c.SHORT_NAME,
                             USER_NO = b.USER_NO,
                             NAME = b.NAME,
                             GRADE = b.GRADE,
                             CLASS_NO = b.CLASS_NO,
                             SEAT_NO = b.SEAT_NO,
                             CARD_NO = b.CARD_NO,
                             IS_WEAR = SetIs_WEAR,
                             UN_WEAR_TYPE = (SetIs_WEAR == false ? (byte)SetUN_WEAR_TYPE : (byte?)null),
                             CASH = SetIs_WEAR == true ? SetCASH : (short?)null,
                         }).NoLock(x => x.FirstOrDefault());

            if (model != null)
            {
                if (!db.GAAT04.Where(x => x.SCHOOL_NO == model.SCHOOL_NO
               && x.USER_NO == model.USER_NO
               && SqlFunctions.DateDiff("second", x.ALARM_DATE, DateTime.Now) < 300).NoLock(x => x.Any()))
                {
                    int SYear;
                    int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                    GAAT04 Cre = new GAAT04();

                    Cre.SPOT_CHECK_ID = Guid.NewGuid().ToString("N");
                    Cre.ALARM_DATE = DateTime.Now;
                    Cre.SCHOOL_NO = model.SCHOOL_NO;
                    Cre.CLASS_NO = model.CLASS_NO;
                    Cre.GRADE = model.GRADE;
                    Cre.SYEAR = (byte)SYear;
                    Cre.SEMESTER = (byte)Semesters;
                    Cre.USER_NO = model.USER_NO;
                    Cre.NAME = model.NAME;
                    Cre.SEAT_NO = model.SEAT_NO;
                    Cre.IS_WEAR = model.IS_WEAR;
                    Cre.UN_WEAR_TYPE = model.UN_WEAR_TYPE;
                    Cre.CASH = model.CASH ?? 0;

                    db.GAAT04.Add(Cre);

                    if (Cre.CASH > 0)
                    {
                        ECOOL_APP.CashHelper.AddCash(user, (short)Cre.CASH, Cre.SCHOOL_NO, Cre.USER_NO, "GAAT04", Cre.SPOT_CHECK_ID, $"學校隨機抽查防身警報器有配載，獲得 {Cre.CASH} 點", true, ref db,"", "",ref valuesList);
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "Error:" + ex.Message;
                        return null;
                    }

                    return model;
                }
                else
                {
                    Message = "Error:此數位學生證-請勿重複刷卡";
                }
            }
            else
            {
                Message = "Error:此數位學生證對應不到學生資料";
            }

            return null;
        }

        public GAAI0SpotCheckSearchViewModel GetSpotCheckSearch(GAAI0SpotCheckSearchViewModel model, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(GAAI0SpotCheckSearchListViewModel.ALARM_DATE);
                model.SortType = PageGlobal.SortType.DESC;
            }

            var temp = (from a in db.GAAT04
                        where a.SCHOOL_NO == model.WhereSCHOOL_NO
                        select new GAAI0SpotCheckSearchListViewModel()
                        {
                            SPOT_CHECK_ID = a.SPOT_CHECK_ID,
                            ALARM_DATE = a.ALARM_DATE,
                            SCHOOL_NO = a.SCHOOL_NO,
                            CLASS_NO = a.CLASS_NO,
                            GRADE = a.GRADE,
                            SYEAR = a.SYEAR,
                            SEMESTER = a.SEMESTER,
                            USER_NO = a.USER_NO,
                            NAME = a.NAME,
                            SEAT_NO = a.SEAT_NO,
                            IS_WEAR = a.IS_WEAR,
                            UN_WEAR_TYPE = a.UN_WEAR_TYPE,
                            CASH = (short)a.CASH,
                        });

            if (model.WhereGRADE != null)
            {
                temp = temp.Where(a => a.GRADE == model.WhereGRADE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                temp = temp.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereUser))
            {
                temp = temp.Where(a => a.NAME.Contains(model.WhereUser.Trim()) || a.USER_NO.Contains(model.WhereUser.Trim()));
            }

            if (model.WhereALARM_DATEs != null)
            {
                temp = temp.Where(a => DbFunctions.TruncateTime(a.ALARM_DATE) >= model.WhereALARM_DATEs);
            }

            if (model.WhereALARM_DATEe != null)
            {
                temp = temp.Where(a => DbFunctions.TruncateTime(a.ALARM_DATE) <= model.WhereALARM_DATEe);
            }

            temp = temp.OrderBy(model.OrderByColumnName, model.SortType);

            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.ListData.PageNumber;

            return model;
        }

        public GAAI01TagWearDetailsViewModel GetHrmt01forCard(string SCHOOL_NO, string CLASS_NO, string CARD_NO, ref ECOOL_DEVEntities db, ref string Message)
        {
            var model = (from b in db.HRMT01
                         join c in db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                         where (
                                        (b.CARD_NO == CARD_NO)
                                     || (b.SCHOOL_NO + b.CLASS_NO + b.SEAT_NO == SCHOOL_NO + CARD_NO)
                                     || (b.SCHOOL_NO + b.USER_NO == SCHOOL_NO + CARD_NO)
                                )
                         && b.USER_TYPE == UserType.Student
                         && (!UserStaus.NGUserStausList.Contains(b.USER_STATUS))
                         select new GAAI01TagWearDetailsViewModel()
                         {
                             SCHOOL_NO = b.SCHOOL_NO,
                             SHORT_NAME = c.SHORT_NAME,
                             USER_NO = b.USER_NO,
                             NAME = b.NAME,
                             GRADE = b.GRADE,
                             CLASS_NO = b.CLASS_NO,
                             SEAT_NO = b.SEAT_NO,
                             CARD_NO = b.CARD_NO,
                             IS_WEAR = true,
                         }).NoLock(x => x.FirstOrDefault());

            if (model != null)
            {
                if (model.CLASS_NO != CLASS_NO)
                {
                    Message = $"Error:此數位學生證不是{CLASS_NO}班的學生";
                    return null;
                }

                return model;
            }
            else
            {
                Message = "Error:此數位學生證對應不到學生資料";
            }

            return null;
        }

        public GAAI0CareListIndexViewModel GetCareListData(GAAI0CareListIndexViewModel model, ref ECOOL_DEVEntities db)
        {
            GAAT01Service gAAT01Service = new GAAT01Service();

            var gAAT01s = gAAT01Service.GetGAAT01s((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, ref db, DateTime.Now);

            var ClassList = this.GetClassList((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, model.WhereCLASS_NO, ref db);

            var WearDetailList = this.GetWearDetailList((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, model.WhereCLASS_NO, false, ref db);

            if (gAAT01s?.Count > 0 && WearDetailList?.Count() > 0)
            {
                List<GAAI01CareFoDateClassViewModel> DateClasss = new List<GAAI01CareFoDateClassViewModel>();

                foreach (var item in gAAT01s)
                {
                    GAAI01CareFoDateClassViewModel DC = new GAAI01CareFoDateClassViewModel();

                    DC.ALARM_ID = item.ALARM_ID;
                    DC.SCHOOL_NO = model.WhereSCHOOL_NO;
                    DC.CLASS_NO = model.WhereCLASS_NO;
                    DC.CYCLE = item.CYCLE;
                    DC.ALARM_DATES = item.ALARM_DATES;
                    DC.ALARM_DATEE = item.ALARM_DATEE;

                    var thisWearDetails = WearDetailList.Where(a => a.ALARM_ID == item.ALARM_ID).Select(a => $"{a.CLASS_NO} {a.NAME} {a.SEAT_NO}").ToList();

                    if (thisWearDetails?.Count > 0)
                    {
                        DC.UN_WEAR_USERs = String.Join("、", thisWearDetails.ToArray());
                    }

                    DC.UN_WEAR_MEMOs = string.Empty;

                    foreach (var ItemUnWearType in Enum.GetValues(typeof(GAAT02_U.UnWearType)))
                    {
                        var ItemUnWearTypeCount = WearDetailList.Where(a => a.ALARM_ID == item.ALARM_ID & a.UN_WEAR_TYPE == (byte)ItemUnWearType).Count();

                        if (string.IsNullOrWhiteSpace(DC.UN_WEAR_MEMOs))
                        {
                            DC.UN_WEAR_MEMOs = $"{ItemUnWearType.ToString()}:{ItemUnWearTypeCount}人";
                        }
                        else
                        {
                            DC.UN_WEAR_MEMOs = DC.UN_WEAR_MEMOs + $"、{ItemUnWearType.ToString()}:{ItemUnWearTypeCount}人";
                        }
                    }

                    DateClasss.Add(DC);
                }

                model.CareFoDateClass = DateClasss;

                model.CareFoUser =
                  (from a in WearDetailList
                   group a by new
                   {
                       a.SCHOOL_NO,
                       a.CLASS_NO,
                       a.USER_NO,
                       a.NAME,
                       a.SNAME,
                       a.SEAT_NO
                   } into gcs
                   select new GAAI01CareFoUserViewModel()
                   {
                       SCHOOL_NO = gcs.Key.SCHOOL_NO,
                       CLASS_NO = gcs.Key.CLASS_NO,
                       USER_NO = gcs.Key.USER_NO,
                       NAME = gcs.Key.NAME,
                       SNAME = gcs.Key.SNAME,
                       SEAT_NO = gcs.Key.SEAT_NO,
                       UN_WEAR_COUNT = gcs.Count(),
                   }).OrderBy(x => x.CLASS_NO).ThenBy(x => x.SEAT_NO).ToList();
            }

            return model;
        }
    }
}