/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Size4/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Size4={directory:"Size4/Regular",family:"NeoEulerMathJax_Size4",id:"NEOEULERSIZE4",32:[0,0,333,0,0,""],40:[2799,199,790,236,768,"236 1300c0 553 105 1106 481 1499h51l-13 -16c-344 -402 -422 -950 -422 -1483s78 -1081 422 -1483l13 -16h-51c-376 393 -481 946 -481 1499"],41:[2799,199,790,22,554,"554 1300c0 -553 -105 -1106 -481 -1499h-51l13 16c344 402 422 950 422 1483s-78 1081 -422 1483l-13 16h51c376 -393 481 -946 481 -1499"],47:[2799,200,1277,50,1228,"1168 2797l12 2l43 -5l5 -11l-1120 -2983l-55 8l-3 14"],91:[2874,125,583,275,571,"275 -125v2999h296v-62h-234v-2875h234v-62h-296"],92:[2799,200,1277,50,1228,"110 2797l-12 2l-43 -5l-5 -11l1120 -2983l55 8l3 14"],93:[2874,125,583,11,307,"245 -63v2875h-234v62h296v-2999h-296v62h234"],123:[2799,200,806,144,661,"361 164v689c0 92 0 117 -13 166c-43 162 -156 232 -196 257c-7 5 -8 6 -8 24c0 17 1 18 13 26c48 30 99 69 144 142c60 99 60 180 60 256v711c0 74 43 248 240 353c19 11 21 11 37 11c22 0 23 -1 23 -23c0 -16 0 -19 -6 -22c-65 -38 -142 -91 -189 -212 c-21 -56 -21 -97 -21 -182v-616c0 -74 0 -81 -2 -101c-14 -141 -104 -266 -242 -344c93 -49 195 -144 232 -284c12 -47 12 -75 12 -157v-616c0 -66 0 -80 2 -96c12 -133 92 -234 203 -298c10 -6 11 -7 11 -25c0 -22 -1 -23 -23 -23c-15 0 -17 0 -30 8 c-210 110 -247 289 -247 356"],124:[3098,208,213,86,126,"126 -200l-40 -8v3298l40 8v-3298"],125:[2799,200,806,144,661,"361 239v693c0 71 39 254 243 368c-167 88 -243 254 -243 367v690c0 66 0 80 -2 96c-13 139 -97 237 -207 300c-8 4 -8 8 -8 23c0 22 1 23 24 23c14 0 16 0 30 -8c220 -116 247 -303 247 -356v-768c0 -9 0 -83 48 -178c49 -98 132 -149 160 -166c7 -5 8 -6 8 -24 c0 -16 0 -18 -8 -23c-50 -31 -96 -65 -143 -136c-65 -100 -65 -191 -65 -265v-711c0 -45 -20 -215 -211 -336c-45 -28 -52 -28 -66 -28c-23 0 -24 1 -24 23c0 18 1 19 13 26c141 81 180 192 193 241c11 45 11 59 11 149"],160:[0,0,333,0,0,""],8214:[3098,208,403,86,316,"316 -200l-40 -8v3298l40 8v-3298zM126 -200l-40 -8v3298l40 8v-3298"],8260:[2799,200,1277,50,1228,"1168 2797l12 2l43 -5l5 -11l-1120 -2983l-55 8l-3 14"],8725:[2799,200,1277,50,1228,"1168 2797l12 2l43 -5l5 -11l-1120 -2983l-55 8l-3 14"],8730:[3002,1,1000,111,1023,"249 1499l216 -1255h1l518 2758l39 -8l-562 -2995h-37l-231 1343l-68 -132c-1 1 -14 12 -14 16c0 1 1 3 6 13"],8739:[3098,208,213,86,126,"126 -200l-40 -8v3298l40 8v-3298"],8741:[2498,208,403,86,316,"316 -200l-40 -8v2698l40 8v-2698zM126 -200l-40 -8v2698l40 8v-2698"],8968:[2799,200,638,275,627,"275 -200v2999h352v-62h-290v-2937h-62"],8969:[2799,200,638,11,363,"301 -200v2937h-290v62h352v-2999h-62"],8970:[2799,200,638,275,627,"275 -200v2999h62v-2937h290v-62h-352"],8971:[2799,200,638,11,363,"301 -138v2937h62v-2999h-352v62h290"],9001:[2730,228,803,137,694,"202 1251l492 -1459l-59 -20l-498 1479l498 1479l59 -20"],9002:[2730,228,859,109,666,"168 2730l498 -1479l-498 -1479l-59 20l492 1459l-492 1459"],9180:[814,-293,3111,56,3055,"66 293c-10 0 -10 7 -10 22c0 17 1 18 9 27c104 99 241 194 393 265c342 160 724 207 1098 207c163 0 327 -8 488 -32c298 -45 592 -133 838 -307c110 -78 170 -140 171 -141c2 -2 2 -4 2 -19s0 -22 -10 -22c-4 0 -6 2 -9 4c-108 89 -226 165 -353 223 c-328 148 -702 197 -1128 197c-430 0 -788 -52 -1094 -183c-139 -59 -267 -139 -383 -235c-7 -6 -9 -6 -12 -6"],9181:[264,257,3111,56,3055,"1555 -257c-163 0 -327 8 -488 32c-298 45 -592 133 -838 307c-110 78 -170 140 -171 141c-2 2 -2 4 -2 19c0 14 0 22 10 22c3 0 7 -3 9 -4c108 -89 226 -165 353 -223c328 -148 702 -197 1128 -197c430 0 788 52 1094 183c139 59 267 139 383 235c3 2 8 6 12 6 c10 0 10 -8 10 -22c0 -17 -1 -18 -9 -27c-104 -99 -241 -194 -393 -265c-342 -160 -724 -207 -1098 -207"],9182:[962,-445,3111,56,3055,"420 745h689c92 0 117 0 166 13c162 43 232 156 257 196c5 7 6 8 24 8c17 0 18 -1 26 -13c30 -48 69 -99 142 -144c99 -60 180 -60 256 -60h711c74 0 248 -43 353 -240c11 -19 11 -21 11 -37c0 -22 -1 -23 -23 -23c-16 0 -19 0 -22 6c-38 65 -91 142 -212 189 c-56 21 -97 21 -182 21h-616c-74 0 -81 0 -101 2c-141 14 -266 104 -344 242c-49 -93 -144 -195 -284 -232c-47 -12 -75 -12 -157 -12h-616c-66 0 -80 0 -96 -2c-133 -12 -234 -92 -298 -203c-6 -10 -7 -11 -25 -11c-22 0 -23 1 -23 23c0 15 0 17 8 30 c110 210 289 247 356 247"],9183:[110,407,3111,56,3055,"495 -107h693c71 0 254 -39 368 -243c88 167 254 243 367 243h690c66 0 80 0 96 2c139 13 237 97 300 207c4 8 8 8 23 8c22 0 23 -1 23 -24c0 -14 0 -16 -8 -30c-116 -220 -303 -247 -356 -247h-768c-9 0 -83 0 -178 -48c-98 -49 -149 -132 -166 -160c-5 -7 -6 -8 -24 -8 c-16 0 -18 0 -23 8c-31 50 -65 96 -136 143c-100 65 -191 65 -265 65h-711c-45 0 -215 20 -336 211c-28 45 -28 52 -28 66c0 23 1 24 23 24c18 0 19 -1 26 -13c81 -141 192 -180 241 -193c45 -11 59 -11 149 -11"],10216:[2134,232,757,123,648,"181 951l467 -1163l-50 -20l-475 1183l475 1183l50 -20"],10217:[2134,232,818,100,625,"150 2134l475 -1183l-475 -1183l-50 20l467 1163l-467 1163"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size4/Regular/Main.js");
