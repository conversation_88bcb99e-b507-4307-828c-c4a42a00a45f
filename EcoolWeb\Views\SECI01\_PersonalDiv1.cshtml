﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI01IndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

    int len = 15;

    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        len = 10;
    }

    bool IsAllList = (this.Request.CurrentExecutionFilePath.Contains("ADDTALLListDetails"));
    string UserDomain = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Host + ":" + HttpContext.Current.Request.Url.Port;
}
@if (Model.WritingCount > 0) { 
<div id="PersonalDivContent">


    <div class="row">
        <div class="col-4">
            <table class="table table-record">
                <tr>
                    <th colspan="2">線上投稿 @Model.DATA_TYPE_NAME</th>
                </tr>
                <tr>
                    <td>投稿數(含草稿)</td>
                    <td>推薦數</td>
                </tr>
                <tr>
                    <td>@Model.WritingCount</td>
                    <td>@Model.WritingShareCount</td>
                </tr>
            </table>
        </div>
        <div class="col-4">
            <table class="table table-record">
                <tr>
                    <th colspan="2">閱讀認證 @Model.DATA_TYPE_NAME</th>
                </tr>
                <tr>
                    <td>閱讀數</td>
                    <td>推薦數</td>
                </tr>
                <tr>
                    <td>@Model.BookCount</td>
                    <td>@Model.BookShareCount</td>
                </tr>
            </table>
        </div>
        <div class="col-4">
            <table class="table table-record">
                <tr>
                    <th colspan="2">榮譽表現 @Model.DATA_TYPE_NAME</th>
                </tr>
                <tr>
                    <td>校內筆數</td>
                    <td>校外筆數</td>
                </tr>
                <tr>
                    <td>>@Model.SchoolInCount</td>
                    <td>@Model.SchoolObcCount</td>
                </tr>
            </table>
        </div>
    </div>


    @if (Model.DATA_ANGLE_TYPE == EcoolWeb.Models.UserProfileHelper.AngleVal.OneData)
    {

        int Num = 0;

        @*以下是閱讀等級徽章*@

        <h2 class="heading-h2">閱讀認證榮譽</h2>
        <div class="d-flex justify-content-between pt-3 readingAward  @(AppMode==true? "text-center" :"")">
            @if (Model.ReadImgURL != null)
            {

                foreach (var ReadImg in Model.ReadImgURL as Dictionary<byte, string>)
                {
                    Num = Num + 1;
                    <img alt="@ReadImg.Key" src="@<EMAIL>(ReadImg.Value)" class="imgEz_Badge" />

                    if (Num == 5)
                    {
                        <span class="w-100"></span>
                    }

                }
            }
        </div>
        <br />
        <div class="mb-3"></div>

        @*以下是認證等級徽章*@
        <h2 class="heading-h2">閱讀護照榮譽</h2>
        <div class="d-flex justify-content-between pt-3 readingPassportAward @(AppMode==true? "text-center" :"")">
            @if (Model.ReadImgURL != null)
            {
                foreach (var PassportImg in Model.PassportImgURL as Dictionary<byte, string>)
                {
                    <img alt="@PassportImg.Key" src="@<EMAIL>(PassportImg.Value)" class="imgEz_BadgeBook" />
                }
            }
        </div>
    }

    @if (Model.wREF_BRE_NO == EcoolWeb.Controllers.SECI01Controller.REF_BRE_NO_VAL.C_SECI01
            || Model.wREF_BRE_NO == EcoolWeb.Controllers.SECI01Controller.REF_BRE_NO_VAL.C_MobileHome
            )
    {
        <div class="mb-3"></div>
        <div class="row">
            <div class="col-sm-6">
                <table class="table-ecool table-hover table-ecool-pink-SEC">
                    <thead>
                        <tr class="text-center">
                            <th colspan="3">
                                線上投稿 @Model.DATA_TYPE_NAME
                            </th>
                        </tr>
                        <tr class="text-center">
                            <th>
                                日期
                            </th>
                            <th>
                                標題
                            </th>
                            <th>
                                狀態
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.ADDT01List.Count > 0)
                        {
                            foreach (var aADDT01 in Model.ADDT01List)
                            {
                                <tr onclick="onBtnLinkADDT01('@aADDT01.WRITING_NO','@aADDT01.USER_NO','@Model.DATA_ANGLE_TYPE')" style="cursor:pointer">
                                    <td title="線上投稿.日期" class="text-center">@Convert.ToDateTime(aADDT01.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                    <td title="線上投稿.標題">
                                        @StringHelper.LeftStringR(aADDT01.SUBJECT, len)

                                        @if (aADDT01.SHARE_YN == "Y")
                                        {
                                            <img src="@<EMAIL>("~/Content/img/icons-like-05.png")" />
                                        }

                                        @if (aADDT01.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Verified && aADDT01.CASH.HasValue)
                                        {
                                            <span class="text-danger">+@aADDT01.CASH</span>
                                        }
                                    </td>
                                    <td title="線上投稿.狀態" class="text-center">@ADDStatus.GetADDT01StatusString((byte)aADDT01.WRITING_STATUS)</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr class="text-center">
                                <td colspan="3">無線上投稿資料</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr class="text-right">
                            <td colspan="3">
                                <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkADDT01('','@Model.wUSER_NO','@Model.DATA_ANGLE_TYPE')">more</button>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class="col-sm-6">

                <table class="table-ecool table-hover table-ecool-pink-SEC">
                    <thead>
                        <tr class="text-center">
                            <th colspan="3">
                                建議與鼓勵 @Model.DATA_TYPE_NAME
                            </th>
                        </tr>
                        <tr class="text-center">
                            <th>
                                日期
                            </th>
                            <th>
                                文章標題
                            </th>
                            <th>
                                留言
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.ADDT02List.Count > 0)
                        {
                            foreach (var aADDT02 in Model.ADDT02List)
                            {
                                <tr onclick="onBtnLinkADDT01('@aADDT02.WRITING_NO','@aADDT02.USER_NO','@Model.DATA_ANGLE_TYPE')" style="cursor:pointer">
                                    <td title="建議與鼓勵.日期" class="text-center">@Convert.ToDateTime(aADDT02.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                    <td title="建議與鼓勵.文章標題">@StringHelper.LeftStringR(aADDT02.ADDT01.SUBJECT, len)</td>
                                    <td title="建議與鼓勵.留言">@StringHelper.LeftStringR(aADDT02.COMMENT, len)</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr class="text-center">
                                <td colspan="3">無建議與鼓勵資料</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr class="text-right">
                            <td colspan="3">
                                <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkADDT01('','@Model.wUSER_NO','@Model.DATA_ANGLE_TYPE')">more</button>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <table class="table-ecool  table-hover table-ecool-yellow-SEC">
                    <thead>
                        <tr class="text-center">
                            <th colspan="3">
                                閱讀認證 @Model.DATA_TYPE_NAME
                            </th>
                        </tr>
                        <tr class="text-center">
                            <th>
                                日期
                            </th>
                            <th>
                                書名
                            </th>
                            <th>
                                狀態
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.ADDT06List.Count > 0)
                        {
                            foreach (var aADDT06 in Model.ADDT06List)
                            {
                                <tr onclick="onBtnLinkADDT06('@aADDT06.APPLY_NO','@Model.DATA_ANGLE_TYPE')" style="cursor:pointer">
                                    <td title="閱讀認證.日期" class="text-center">@Convert.ToDateTime(aADDT06.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                    <td title="閱讀認證.書名">
                                        @aADDT06.BOOK_NAME

                                        @if (aADDT06.SHARE_YN == "Y")
                                        {
                                            <img src="@<EMAIL>("~/Content/img/icons-like-05.png")" />
                                        }
                                    </td>
                                    <td title="閱讀認證.狀態">@ADDStatus.GetADDT06StatusString(aADDT06.APPLY_STATUS)</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr class="text-center">

                                <td colspan="3">無閱讀認證資料</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr class="text-right">
                            <td colspan="3">
                                <button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkADDT06('', '@Model.DATA_ANGLE_TYPE')">more</button>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="col-sm-6">
                <table class="table-ecool table-hover table-ecool-Tangerine-SEC">
                    <thead>
                        <tr class="text-center">
                            <th colspan="3">
                                校內表現/校外榮譽 @Model.DATA_TYPE_NAME
                            </th>
                        </tr>
                        <tr class="text-center">
                            <th>
                                日期
                            </th>
                            <th>
                                內容
                            </th>
                            <th>
                                獲得點數
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.SchoolDataList.Count > 0)
                        {
                            foreach (var item in Model.SchoolDataList)
                            {
                                <tr onclick="onBtnLinkSchoolData('@item.NO','@item.USER_NO','@item.SYS_TABLE')" >
                                    <td title="校內表現/校外榮譽.日期" class="text-center">@Convert.ToDateTime(item.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                    <td title="表現/榮譽內容">@StringHelper.LeftStringR(item.CONTENT_TXT, len)</td>
                                    <td title="獲得點數" class="text-center">@item.CASH</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr class="text-center">
                                <td colspan="3">無校內表現/校外榮譽資料</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr class="text-center">

                            <td colspan="2" align="right">
                                <button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkSchoolData('','@Model.wUSER_NO','@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN')">校內more</button>
                            </td>
                            <td>
                                <button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkSchoolData('','@Model.wUSER_NO','@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC')">校外more</button>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <br />
        <div class="col-xs-12">
            <table class="table-ecool table-hover table-ecool-Tangerine2-SEC">
                <thead>
                    <tr class="text-center">
                        <th colspan="6">
                            可兌換獎品
                        </th>
                    </tr>
                    <tr class="text-center">
                        <th>
                            圖示
                        </th>
                        <th>
                            品名
                        </th>
                        <th>
                            兌換點數
                        </th>
                        <th class="hidden-xs">
                            剩餘數量
                        </th>
                        <th class="hidden-xs">
                            兌換期限
                        </th>
                        <th>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.AWAT02List != null || Model.AWAT09List != null)
                    {
                        int AwatCount = 0;

                        if (Model.AWAT02List != null)
                        {
                            AwatCount = Model.AWAT02List.Count;
                        }
                        else
                        {
                            AwatCount = Model.AWAT09List.Count;
                        }

                        if (AwatCount > 0)
                        {
                            if (Model.AWAT02List != null)
                            {
                                foreach (var aAWAT02 in Model.AWAT02List)
                                {
                                    <tr>
                                        <td title="可兌換獎品.圖示" align="center" valign="middle">
                                            <img src='@<EMAIL>(ViewBag.SysAwardPath+ aAWAT02.SCHOOL_NO + @"/"+aAWAT02.IMG_FILE)' class="img-responsive img-exchange-with" alt="" />
                                        </td>
                                        <td title="可兌換獎品.品名">@StringHelper.LeftStringR(aAWAT02.AWARD_NAME, 15)</td>
                                        <td title="可兌換獎品.兌換點數" class="text-center">@aAWAT02.COST_CASH</td>
                                        <td title="可兌換獎品.剩餘數量" class="text-center hidden-xs">@aAWAT02.QTY_STORAGE</td>

                                        <td title="可兌換獎品.兌換期限" class="hidden-xs text-center">@(aAWAT02.EDATETIME.HasValue ? aAWAT02.EDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA")</td>

                                        <td class="text-center">
                                            @{
                                                string NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange(aAWAT02);

                                                if (string.IsNullOrWhiteSpace(NG) == false)
                                                {
                                                    <button role="button" class="btn-default btn btn-xs disabled" disabled>
                                                        無法兌換 -
                                                        @Html.Raw(HttpUtility.HtmlDecode(NG))
                                                    </button>

                                                }
                                                else
                                                {
                                                    <button type="button" class="btn-default btn btn-xs" onclick="funGetExchange('@aAWAT02.AWARD_NO')">
                                                        我要兌換
                                                    </button>
                                                }

                                            }
                                        </td>
                                    </tr>
                                }
                            }

                            if (Model.AWAT09List != null)
                            {
                                foreach (var aAWAT09 in Model.AWAT09List)
                                {
                                    <tr>
                                        <td title="可兌換獎品.圖示" align="center" valign="middle">
                                            <img src='@Url.Content(ViewBag.SysAwardPath +aAWAT09.IMG_FILE)' class="img-responsive img-exchange-with" alt="" />
                                        </td>
                                        <td title="可兌換獎品.品名">@StringHelper.LeftStringR(aAWAT09.AWARD_NAME, 15)</td>
                                        <td title="可兌換獎品.兌換點數" class="text-center">@aAWAT09.COST_CASH</td>
                                        <td title="可兌換獎品.剩餘數量" class="text-center hidden-xs">@aAWAT09.QTY_STORAGE</td>
                                        <td title="可兌換獎品.兌換期限" class="text-center hidden-xs">@(aAWAT09.EDATETIME.HasValue ? aAWAT09.EDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA")</td>
                                        <td class="text-center">
                                            @{
                                                string NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange(aAWAT09);

                                                if (string.IsNullOrWhiteSpace(NG) == false)
                                                {
                                                    <button role="button" class="btn-default btn btn-xs disabled" disabled>
                                                        無法兌換 -
                                                        @Html.Raw(HttpUtility.HtmlDecode(NG))
                                                    </button>

                                                }
                                                else
                                                {
                                                    <button type="button" class="btn-default btn btn-xs" onclick="funGetExchange('@aAWAT09.AWARD_NO')">
                                                        我要兌換
                                                    </button>
                                                }

                                            }
                                        </td>
                                    </tr>
                                }
                            }
                        }
                        else
                        {
                            <tr class="text-center">
                                <td colspan="6">無可兌換獎品</td>
                            </tr>
                        }
                    }
                </tbody>
                <tfoot>
                    <tr class="text-right">
                        <td colspan="6">
                            <button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkAWAT02()">more</button>
                        </td>
                    </tr>
                </tfoot>
            </table>
            @if (Model.AWAT02List != null)
            {
                using (Html.BeginForm("AwatExchange02", "AWAI01", FormMethod.Post, new { id = "form1", name = "form1", target = "_blank" }))
                {
                    @Html.AntiForgeryToken()
                    @Html.Hidden("Search.WhereAWARD_NO")
                    @Html.Hidden("Search.WhereSouTable", AWAI01SearchViewModel.SouTableVal.Student)
                    @Html.Hidden("Search.SouController", "SECI01")
                    @Html.Hidden("Search.SouAction", "Index")
                    @Html.Hidden("Search.BackAction", "AwatQ02")
                    @Html.Hidden("Search.BackController", "AWAI01")
                }
                <script type="text/javascript">
                    var targetFormID = '#form1';
                        function funGetExchange(AWARD_NO) {
                        $('#Search_WhereAWARD_NO').val(AWARD_NO);
                        $(targetFormID).attr("action", "@Url.Action("AwatExchange02", "AWAI01")")
                        $(targetFormID).submit();
                    }
                </script>
            }

            @if (Model.AWAT09List != null)
            {
                using (Html.BeginForm("AwatExchange02", "AWAI01", FormMethod.Post, new { id = "form1", name = "form1", target = "_blank" }))
                {
                    @Html.AntiForgeryToken()
                    @Html.Hidden("Search.WhereAWARD_NO")
                    @Html.Hidden("Search.WhereSouTable", AWAI01SearchViewModel.SouTableVal.Teacher)
                    @Html.Hidden("Search.SouController", "SECI01")
                    @Html.Hidden("Search.SouAction", "Index")
                    @Html.Hidden("Search.BackAction", "Awat2Q02")
                    @Html.Hidden("Search.BackController", "AWAI03")
                }
                <script type="text/javascript">
                    var targetFormID = '#form1';
                        function funGetExchange(AWARD_NO) {
                        $('#Search_WhereAWARD_NO').val(AWARD_NO);
                        $(targetFormID).attr("action", "@Url.Action("AwatExchange02", "AWAI01")")
                        $(targetFormID).submit();
                    }
                </script>
            }
        </div>
        if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin)
        {
            @Html.Action("_TaskList", "Home")
        }

    }
</div>}

<script language="javascript" type="text/javascript">

        function printScreenH() {
            window.open('@Url.Action("Index", "SECI01")' + '?PRINT=Y', '_blank');
        }

        function funBookW() {

            if ($('#W1').length > 0) {
                $('#W1').click();
            }
            else {
                alert('無任何資料')
            }
        }

        function funBookA() {
            $('#A1').click();
        }

        //換獎品mory
        function onBtnLinkAWAT02() {
            window.open('@Url.Action("AwatQ02", "Awat")', '_blank');
        }

        //線上投稿
        function onBtnLinkADDT01(NoVal, USER_NO, DATA_TYPE) {

            if (NoVal != '') {
                window.open('@Url.Action("Details", "ADDI01")' + '?WRITING_NO=' + NoVal + '&whereUserNo=' + USER_NO + '', '_blank');
            }
            else {

                if (DATA_TYPE == '@EcoolWeb.Models.UserProfileHelper.AngleVal.OneData') {
                    window.open('@Url.Action("Index", "ADDI01")' + '?whereUserNo=' + USER_NO + '', '_blank');
                }
                else {
                    window.open('@Url.Action("Index", "ADDI01")', '_blank');
                }
            }
        }

        //閱讀認證
        function onBtnLinkADDT06(NoVal, DATA_TYPE) {
            if (NoVal != '') {
                window.open('@Url.Action("ADDTALLListDetails", "ADDT")' + '?APPLY_NO=' + NoVal + '', '_blank');
            }
            else {
                if (DATA_TYPE == '@EcoolWeb.Models.UserProfileHelper.AngleVal.OneData') {
                    window.open('@Url.Action("QUERY", "ADDI02")', '_blank');
                }
                else {
                    window.open('@Url.Action("ADDTALLList", "ADDT")', '_blank');
                }
            }
        }

        function onBtnLinkSchoolData(NoVal, USER_NO, DATA_TYPE) {
            if (DATA_TYPE == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN') {
                window.open('@Url.Action("QUERY", "ADDI06")', '_blank');
            }
            else {
                window.open('@Url.Action("QUERY", "ADDI07")', '_blank');
            }
        }
</script>