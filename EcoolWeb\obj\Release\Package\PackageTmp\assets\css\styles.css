#activity_stage_c1 {
  width:100%;
  height:202px;
}

@media (min-width: 576px) {
  #activity_stage_c1 {
    width:100%;
    height:304px;
  }
}

@media (min-width: 768px) {
  #activity_stage_c1 {
    width:100%;
    height:405px;
  }
}

@media (min-width: 992px) {
  #activity_stage_c1 {
    width:100%;
    height:540px;
  }
}

@media (min-width: 1200px) {
  #activity_stage_c1 {
    width:100%;
    height:641px;
  }
}

#stage_frame_like-1 {
  /*background-image:url(../../assets/img/activity_stage_like-1.png);*/
  /*background-size:contain;*/
  /*background-position:center;*/
  /*background-repeat:no-repeat;*/
  margin-top:-1px;
  width:50%;
  height:50%;
  animation-name:bounceIn;
  animation-duration:3s;
  -webkit-animation-iteration-count:infinite;
}

#stage_frame_like-2 {
  background-image:url(../../assets/img/activity_stage_like-2.png);
  background-size:contain;
  background-position:center;
  background-repeat:no-repeat;
  margin-top:-1px;
  width:50%;
  height:50%;
  animation-name:bounceIn;
  animation-duration:3s;
  /*animation-direction:alternate;*/
  /*animation-timing-function:ease-out;*/
  -webkit-animation-iteration-count:infinite;
}

#stage_frame_like-3 {
  background-image:url(../../assets/img/activity_stage_like-3.png);
  background-size:contain;
  background-position:center;
  background-repeat:no-repeat;
  margin-top:-1px;
  width:50%;
  height:50%;
  animation-name:bounceIn;
  animation-duration:3s;
  /*animation-direction:alternate;*/
  /*animation-timing-function:ease-out;*/
  -webkit-animation-iteration-count:infinite;
}

#stage_frame_like-4 {
  background-image:url(../../assets/img/activity_stage_like-4.png);
  background-size:contain;
  background-position:center;
  background-repeat:no-repeat;
  margin-top:-1px;
  width:50%;
  height:50%;
  animation-name:bounceIn;
  animation-duration:3s;
  /*animation-direction:alternate;*/
  /*animation-timing-function:ease-out;*/
  -webkit-animation-iteration-count:infinite;
}

#stage_frame_like-5 {
  background-image:url(../../assets/img/activity_stage_like-5.png);
  background-size:contain;
  background-position:center;
  background-repeat:no-repeat;
  margin-top:-1px;
  width:50%;
  height:50%;
  animation-name:bounceIn;
  animation-duration:3s;
  /*animation-direction:alternate;*/
  /*animation-timing-function:ease-out;*/
  -webkit-animation-iteration-count:infinite;
}

#stage_frame_top {
  position:fixed;
  z-index:5;
  background-image:url(../../assets/img/stage_frame-1.png);
  background-size:contain;
  /*background-position:center;*/
  background-repeat:repeat-x;
  width:58%;
  height:10%;
}

.stage_firework_b1 {
  /*background-image:url(../../assets/img/web_activity_stage_firework.gif);*/
  background-position:center;
  background-repeat:no-repeat;
  background-size:cover;
}




#stage_artist_frame {
  background-image:url(../../assets/img/activity_stage_photoframe.png);
  position:relative;
  z-index:2;
  background-position:bottom;
  background-size:contain;
  background-repeat:no-repeat;
  width:100%;
  height:100%;
  /*margin-top:9vh;*/
  /*margin-left:1vw;*/
}

#stage_artist_photo {
  position:absolute;
  z-index:1;
  background-position:bottom;
  background-size:cover;
  background-repeat:no-repeat;
  width:130px;
  height:110px;
  margin-top:-7px;
  margin-left:-1.4%;
}

@media (min-width: 576px) {
  #stage_artist_photo {
    width:197px;
    height:168px;
    margin-top:-10px;
    margin-left:-1.4%;
  }
}

@media (min-width: 768px) {
  #stage_artist_photo {
    width:268px;
    height:224px;
    margin-top:-15px;
    margin-left:-1.4%;
  }
}

@media (min-width: 992px) {
  #stage_artist_photo {
    width:358px;
    height:300px;
    margin-top:-20px;
    margin-left:-1.4%;
  }
}

@media (min-width: 1200px) {
  #stage_artist_photo {
    width:431px;
    height:361px;
    margin-top:-25px;
    margin-left:-1.4%;
  }
}

#stage_frame_root {
  width:70%;
  height:70%;
  position:relative;
  z-index:3;
  margin-top:20%;
}

@media (min-width: 576px) {
  #stage_frame_root {
    width:71%;
    height:71%;
    z-index:3;
    margin-top:20%;
  }
}

@media (min-width: 768px) {
  #stage_frame_root {
    width:72%;
    height:72%;
    z-index:3;
    margin-top:19%;
  }
}

@media (min-width: 992px) {
  #stage_frame_root {
    width:72%;
    height:72%;
    z-index:3;
    margin-top:20%;
  }
}

@media (min-width: 1200px) {
  #stage_frame_root {
    width:73%;
    height:73%;
    z-index:3;
    margin-top:19%;
  }
}

#stage_frame_logo {
  position:fixed;
  z-index:10;
  background-image:url(../../assets/img/stage_logo-1.png);
  background-size:contain;
  background-position:center;
  background-repeat:no-repeat;
  margin-top:-3px;
  width:150px;
  height:70px;
  animation-name:bounce;
  animation-duration:4s;
  animation-timing-function:ease-out;
  -webkit-animation-iteration-count:infinite;
}

@media (min-width: 576px) {
  #stage_frame_logo {
    width:190px;
    height:110px;
    margin-top:-11px;
  }
}

@media (min-width: 768px) {
  #stage_frame_logo {
    width:250px;
    height:149px;
    margin-top:-16px;
  }
}

@media (min-width: 992px) {
  #stage_frame_logo {
    width:330px;
    height:190px;
    margin-top:-19px;
  }
}

@media (min-width: 1200px) {
  #stage_frame_logo {
    width:360px;
    height:250px;
    margin-top:-30px;
  }
}


.Ezcontainer {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

@media (min-width: 576px) {
    .Ezcontainer {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .Ezcontainer {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .Ezcontainer {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .Ezcontainer {
        max-width: 1140px;
    }
}

.Addlike {
    width: 30%;
    height: 30%;
    background-position: top;
    background-size: contain;
    background-repeat: no-repeat;
    margin-top: 1px;
}

.likeNum {
    width: 30%;
    height: 30%;
    background-position: top;
    background-size: contain;
    background-repeat: no-repeat;
}
