﻿@model ECOOL_APP.EF.ADDT06
@using ECOOL_APP.com.ecool.service
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}<!-- clipboard.js v1.7.1 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.7.1/clipboard.min.js"></script>
<style type="text/css">

    #DivNextButton {
        position: fixed; /*固定在網頁上不隨卷軸移動，若要隨卷軸移動用absolute*/
        top: 50%; /*設置垂直位置*/
        left: 10px; /*設置水平位置，依所放的內容多寡需要自行手動調整*/
        padding: 10px 20px;
        z-index: 99999;
        border-radius: 10px; /*圓角*/
        -moz-border-radius: 10px;
        -webkit-border-radius: 10px;
    }

    #DivPrevButton {
        position: fixed; /*固定在網頁上不隨卷軸移動，若要隨卷軸移動用absolute*/
        top: 50%; /*設置垂直位置*/
        right: 10px; /*設置水平位置，依所放的內容多寡需要自行手動調整*/
        padding: 10px 20px;
        z-index: 99999;
        border-radius: 10px; /*圓角*/
        -moz-border-radius: 10px;
        -webkit-border-radius: 10px;
    }

    .videoWrapper {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 */
        padding-top: 25px;
        height: 0;
        max-height: 600px;
    }

        .videoWrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

    .css-table {
        display: table;
        border-collapse: collapse;
        width: 100%
    }

        .css-table .thead {
            display: table-header-group;
        }

        .css-table .tbody {
            display: table-row-group;
        }

        .css-table .tr {
            display: table-row;
            padding-bottom: 2px
        }

        .css-table .th, .css-table .td {
            display: table-cell;
            padding-left: 3px;
            vertical-align: middle;
            text-align: center;
        }

    .modal {
        position: fixed;
        top: auto;
        right: 0;
        left: 0;
        z-index: 1040;
        display: none;
        overflow: auto;
        overflow-y: scroll;
    }
</style>
<div style="height:25px"></div>
<div id="ShowDiv">

    @if (Model == null)
    {
        <div class="text-center">
            <h1>無任何資料</h1>
        </div>
    }
    else
    {
        @Html.Partial("_Title_Secondary")
        Layout = null;
        <img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
        <div class="Div-EZ-reader">
            <div class="Details">
                <div class="row">
                    <div class="col-md-5 col-sm-5 dl-horizontal-EZ">
                        <samp class="dt">
                            申請日
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime")
                        </samp>
                    </div>
                    <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                        <samp class="dt">
                            班級
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(model => model.CLASS_NO)
                        </samp>
                    </div>
                    <div class="col-md-4 col-sm-4 dl-horizontal-EZ">
                        <samp class="dt">
                            姓名
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(model => model.SNAME)
                        </samp>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 col-sm-12  dl-horizontal-EZ">
                        <samp class="dt">
                            書名
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(model => model.BOOK_NAME)
                        </samp>
                    </div>
                </div>
                <div style="height:20px"></div>
                <div class="row">
                    <div class="col-md-12 col-sm-12  dl-horizontal-EZ">
                        <samp class="dt">
                            心得
                        </samp>
                        <div class="p-context">
                            @if (string.IsNullOrWhiteSpace(ViewBag.ImageUrl) == false)
                            {
                                <img id="imgArticle1" class="img-responsive " src='@ViewBag.ImageUrl' href="@ViewBag.ImageUrl"      onclick=""        style ="float:right;margin:10px;max-height:250px;width:auto" />
                            }

                            @if (ViewBag.ShowOriginalArticle == "V")
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(Model.REVIEW_VERIFY.Replace("\r\n", "<br/>")))
                            }
                            else
                            {
                                if (Model.REVIEW != null)
                                {
                                    @Html.Raw(HttpUtility.HtmlDecode(Model.REVIEW.Replace("\r\n", "<br/>")))
                                }

                            }
                        </div>
                    </div>
                </div>
                <div class="row Div-btn-center">

                    @if (ViewBag.ShowOriginalArticle == "O")
                    {
                        @Html.ActionLink("批閱後文章", "PushListDetail", new { APPLY_NO = Model.APPLY_NO, ShowOriginal = false }, new { @role = "button", @class = "btn btn-default" })
                    }
                    else if (ViewBag.ShowOriginalArticle == "V")
                    {
                        <a href="@Url.Action("PushListDetail", "ADDT",new { APPLY_NO = Model.APPLY_NO,ShowOriginal = true})" role="button" class="btn btn-default">
                            學生原稿
                        </a>
                    }
                    <a href='@Url.Action("ADDTALLList", "ADDT", new { ListType=Request.Params["ListType"]})' role="button" class="btn btn-default">
                        返回
                    </a>
                </div>
            </div>

        </div>

        <script language="javascript">
            $(document).ready(function () {
                $("#imgArticle1").colorbox({ opacity: 0.82 });
            });
            function imgA()
            {
                $("#imgArticle1").colorbox({ opacity: 0.82 });
            }
        </script>

    }



</div>
