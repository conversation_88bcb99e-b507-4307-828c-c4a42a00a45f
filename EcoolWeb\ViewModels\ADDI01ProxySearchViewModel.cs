﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class ADDI01ProxySearchViewModel
    {
        /// <summary>
        /// 模式
        /// </summary>
        [DisplayName("模式")]
        [Required]
        public byte ModeVal { get; set; }

        /// <summary>
        /// 學校
        /// </summary>
        [DisplayName("學校")]
        [Required]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        /// 登入帳號
        /// </summary>
        [DisplayName("姓名/座號")]
        public string USER_NO { get; set; }


        /// <summary>
        /// 人數
        /// </summary>
        [DisplayName("筆數")]
        public string NumType { get; set; }

        /// <summary>
        /// 文章數
        /// </summary>
        [DisplayName("文章數")]
        public Int16 NumArticle { get; set; }

    }
}