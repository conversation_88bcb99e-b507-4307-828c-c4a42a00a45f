.challenge-loading,.challenge-panel-bg{flex-direction:column;font-weight:600;text-align:center}.challenge-loading{position:fixed;top:0;right:0;bottom:0;left:0;z-index:3;display:none;justify-content:center;align-items:center;background-color:#b5e4ff;font-size:3rem;color:#0b4ca7}.challenge-loading img{display:inline-block;max-width:100%;min-width:35%}.challenge-btn-back{position:fixed;right:0!important;z-index:3!important;bottom:2rem;font-size:2rem;padding:.5rem 1rem}.challenge-panel-bg,.challenge-ribbon{position:fixed;top:0;left:0;right:0;bottom:0;z-index:3}.challenge-btn-back span{display:inline-block;vertical-align:middle}.challenge-btn-back .btn-primary{display:none}.challenge-btn-back:hover .btn-primary{display:inline-block}.challenge-panel-bg{display:none;justify-content:center;align-items:center;background-color:rgba(0,0,0,.1);font-size:3rem}.challenge-ribbon{background-image:url(../img/happy.svg);pointer-events:none;animation-name:zoomIn;animation-delay:.1s;animation-iteration-count:1;animation-duration:.7s}.challenge-info{background-color:#fff;display:flex;flex-direction:column;justify-content:center;align-items:center;width:100%;max-width:60%;min-height:35%;z-index:4;text-align:center;font-size:5vw;animation-name:zoomIn;animation-delay:.5s;animation-duration:.3s;border-radius:4vw;box-shadow:0 0 1vw rgba(0,0,0,.7)}.challenge-info-success{color:#b72700;border:2vw solid #fff8e0}.challenge-info-failed{background-color:#a91f1f;color:#fff;border:2vw solid #ff4848}.challenge-img-sensor{height:6rem;height:10vh;width:auto}.challenge-font-size{font-size:1.75rem;font-size:1.75vw}