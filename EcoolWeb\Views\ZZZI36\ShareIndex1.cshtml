﻿
@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<img src="~/Content/img/web-bar3-revise-104.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ZZZI26">
    <div class="form-horizontal">
        <label class="control-label">@ViewBag.Panel_Title</label>
        <br /><br />
        <div class="form-group text-center">
            @{

                <div class="col-md-4">
                    @Html.ActionLink("匯入老師資料", "Index31", new { ModeValString = "匯入老師" }, new { @class = "btn btn-default  btn-block", @role = "button", target = "_blank" })
                </div>
                <div class="col-md-4">
                    @Html.ActionLink("匯入班級資料", "Index41", new { ModeValString = "匯入班級" }, new { @class = "btn btn-default  btn-block", @role = "button", target = "_blank" })
                </div>
                <div class="col-md-4">
                    @Html.ActionLink("匯入學生資料", "Index51", new { ModeValString = "匯入學生" }, new { @class = "btn btn-default  btn-block", @role = "button", target = "_blank" })
                </div>
            }
        </div>
        <div>
            說明：預計111年11月開始實施<br />
            1.	這是給「外縣市學校」管理平台帳號的程式。<br />
            2.	每學年開始，8到9月將下載<a href="@Url.Content("~/Content/ExcelSample/sample.xlsx")" target="_blank" class="btn-table-link">
                【Sample】
            </a>檔填寫全校師生資料，並將資料交由<font style="color:red">西湖國小匯入。</font><br />
            3.	學期中，各校若有轉出入學生，可針對實際轉出入自行匯入異動人員(只需匯入轉出入名單)，匯入後可到帳號管理→學生資料維護查詢是否成功。<br />
            4.	要給西湖國小的<a href="@Url.Content("~/Content/ExcelSample/sample.xlsx")" target="_blank" class="btn-table-link">
                【Sample】
            </a>檔，請請注意 <font style="color:red">欄位格式(座號和生日)</font><br />
            (1)101班，年級是1，班級是01<br />
            (2)座號不能寫1，要寫01，排序才不會亂掉<br />
            (3)生日的格式要選日期非前面有*那個，例如1998年4月29日
        </div>

    </div>
</div>