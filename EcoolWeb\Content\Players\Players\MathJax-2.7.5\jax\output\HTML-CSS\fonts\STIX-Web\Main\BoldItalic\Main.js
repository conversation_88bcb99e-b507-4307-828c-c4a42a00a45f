/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Main/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Main-bold-italic"]={directory:"Main/BoldItalic",family:"STIXMathJax_Main",weight:"bold",style:"italic",testString:"\u00A0\u00A3\u00A5\u00A7\u00A8\u00AF\u00B0\u00B4\u00B5\u00B7\u00F0\u0127\u0131\u0237\u02C6",32:[0,0,250,0,0],33:[684,13,389,67,370],34:[685,-398,555,136,536],35:[700,0,500,-32,532],36:[733,100,500,-20,497],37:[706,29,757,80,707],38:[682,19,849,76,771],39:[685,-398,278,128,268],40:[685,179,333,28,344],41:[685,179,333,-44,271],42:[685,-252,500,101,492],43:[506,0,570,33,537],44:[134,182,250,-60,144],45:[282,-166,333,2,271],46:[135,13,250,-9,139],47:[685,18,278,-64,342],48:[683,14,500,17,477],49:[683,0,500,5,419],50:[683,0,500,-27,446],51:[683,13,500,-14,450],52:[683,0,500,-15,503],53:[669,13,500,-11,486],54:[679,15,500,23,509],55:[669,0,500,52,525],56:[683,13,500,3,476],57:[683,10,500,-12,475],58:[459,13,333,23,264],59:[459,183,333,-25,264],60:[518,12,570,31,539],61:[399,-107,570,33,537],62:[518,12,570,31,539],63:[684,13,500,79,470],64:[685,18,939,118,825],65:[683,0,667,-68,593],66:[669,0,667,-25,624],67:[685,18,667,32,677],68:[669,0,722,-46,685],69:[669,0,667,-27,653],70:[669,0,667,-13,660],71:[685,18,722,21,705],72:[669,0,778,-24,799],73:[669,0,389,-32,406],74:[669,99,500,-46,524],75:[669,0,667,-21,702],76:[669,0,611,-22,590],77:[669,12,889,-29,917],78:[669,15,722,-27,748],79:[685,18,722,27,691],80:[669,0,611,-28,613],81:[685,208,722,27,691],82:[669,0,667,-28,623],83:[685,18,556,2,526],84:[669,0,611,49,650],85:[669,18,722,67,744],86:[669,18,667,66,715],87:[669,18,889,64,940],88:[669,0,667,-24,694],89:[669,0,611,71,659],90:[669,0,611,-12,589],91:[674,159,333,-37,362],92:[685,18,278,-1,279],93:[674,157,333,-56,343],94:[669,-304,570,67,503],95:[-75,125,500,0,500],96:[697,-516,333,85,297],97:[462,14,500,-21,456],98:[699,13,500,-14,444],99:[462,13,444,-5,392],100:[699,13,500,-21,517],101:[462,13,444,5,398],102:[698,205,333,-169,446],103:[462,203,500,-52,477],104:[699,9,556,-13,498],105:[684,9,278,2,262],106:[685,207,278,-189,279],107:[699,8,500,-23,483],108:[699,9,278,2,290],109:[462,9,778,-14,723],110:[462,9,556,-6,494],111:[462,13,500,-3,441],112:[462,205,500,-120,446],113:[462,205,500,1,471],114:[462,0,389,-21,389],115:[462,13,389,-19,333],116:[594,9,278,-11,281],117:[462,9,556,15,493],118:[462,13,444,15,401],119:[462,13,667,15,614],120:[462,13,500,-45,469],121:[462,205,444,-94,392],122:[449,78,389,-43,368],123:[686,187,348,4,436],124:[685,18,220,66,154],125:[686,187,348,-129,303],126:[331,-175,570,54,516],160:[0,0,250,0,0],163:[683,12,500,-32,510],165:[669,0,500,33,628],167:[685,143,500,36,459],168:[655,-525,333,55,397],175:[623,-553,333,51,393],176:[688,-402,400,83,369],180:[697,-516,333,139,379],181:[449,207,576,-60,516],183:[405,-257,250,51,199],240:[699,13,500,-3,454],295:[699,9,556,-13,498],305:[462,9,278,2,238],567:[462,207,278,-189,239],710:[690,-516,333,40,367],711:[690,-516,333,79,411],728:[678,-516,333,71,387],729:[655,-525,333,163,293],730:[754,-541,333,127,340],732:[655,-536,333,48,407],913:[683,0,667,-68,593],914:[669,0,667,-25,624],915:[669,0,585,-13,670],916:[683,0,667,-65,549],917:[669,0,667,-27,653],918:[669,0,611,-12,589],919:[669,0,778,-24,799],920:[685,18,718,27,691],921:[669,0,389,-32,406],922:[669,0,667,-21,702],923:[683,0,655,-68,581],924:[669,12,889,-29,917],925:[669,15,722,-27,748],926:[669,0,746,25,740],927:[685,18,722,27,691],928:[669,0,778,-24,799],929:[669,0,611,-28,613],931:[669,0,633,-11,619],932:[669,0,611,49,650],933:[685,0,611,21,697],934:[669,0,771,26,763],935:[669,0,667,-24,694],936:[685,0,661,17,780],937:[685,0,808,25,774],945:[462,13,576,-3,574],946:[698,205,500,-79,480],947:[462,204,438,3,461],948:[698,13,496,-3,456],949:[462,13,454,-5,408],950:[698,205,415,-5,473],951:[462,205,488,-7,474],952:[698,13,501,-3,488],953:[462,9,278,2,238],954:[462,12,500,-23,504],955:[698,18,484,-34,459],956:[449,205,523,-82,483],957:[462,13,469,-23,441],958:[698,205,415,-5,426],959:[462,13,500,-3,441],960:[449,15,558,-6,570],961:[462,205,495,-81,447],962:[462,205,415,-5,447],963:[449,13,499,-3,536],964:[449,9,415,4,455],965:[462,13,536,-7,477],966:[462,205,678,-3,619],967:[462,205,404,-136,515],968:[462,205,652,-5,715],969:[462,13,735,-3,676],976:[696,12,500,42,479],977:[698,13,582,8,589],978:[685,0,611,21,696],981:[699,205,678,-3,619],982:[449,13,828,-2,844],984:[685,200,722,27,691],985:[462,205,500,-3,441],986:[685,205,669,32,665],987:[492,205,475,-5,509],988:[669,0,667,-13,670],989:[450,190,525,32,507],990:[793,18,757,-7,758],991:[698,0,485,16,466],992:[685,205,734,27,710],993:[639,205,530,47,467],1008:[462,15,569,-50,592],1009:[462,206,517,-12,458],1012:[685,18,722,27,691],1013:[462,13,466,-3,429],1014:[460,15,486,-5,427],8211:[269,-178,500,-40,477],8212:[269,-178,1000,-40,977],8216:[685,-369,333,128,332],8217:[685,-369,333,98,302],8220:[685,-369,500,53,513],8221:[685,-369,500,53,513],8224:[685,145,500,91,494],8225:[685,139,500,10,493],8254:[838,-766,500,0,500],8260:[688,12,183,-168,345],8467:[699,14,500,43,632],8706:[686,10,559,44,559],9416:[690,19,695,0,695]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Main-bold-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/BoldItalic/Main.js"]);
