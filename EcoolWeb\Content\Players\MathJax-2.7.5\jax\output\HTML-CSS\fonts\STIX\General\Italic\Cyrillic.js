/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/Cyrillic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{1025:[856,0,611,1,631],1026:[653,208,723,70,663],1027:[914,0,569,-36,603],1028:[666,18,657,67,680],1029:[667,18,500,7,498],1030:[653,0,333,-7,382],1031:[856,0,333,-31,433],1032:[653,18,444,-34,463],1033:[653,16,961,-35,901],1034:[653,0,966,-28,906],1035:[653,0,786,70,701],1036:[914,0,621,-28,657],1038:[887,14,656,110,716],1039:[653,179,722,-25,747],1040:[668,0,611,-49,566],1041:[653,0,590,-28,603],1042:[653,0,597,-23,571],1043:[653,0,569,-36,603],1044:[653,179,655,-103,696],1045:[653,0,611,1,631],1046:[661,0,956,-55,972],1047:[668,16,564,9,548],1048:[653,0,708,-25,749],1049:[887,0,708,-25,749],1050:[661,0,621,-28,657],1051:[653,16,699,-35,740],1052:[653,0,814,-33,855],1053:[653,0,708,-26,749],1054:[667,18,712,60,699],1055:[653,0,704,-29,745],1056:[653,0,568,-24,578],1057:[666,18,667,67,690],1058:[653,0,556,70,644],1059:[653,14,656,110,716],1060:[653,0,772,73,758],1061:[653,0,575,-67,617],1062:[653,179,706,-25,747],1063:[653,0,622,54,663],1064:[653,0,936,-14,977],1065:[653,179,936,-14,977],1066:[653,0,695,63,652],1067:[653,0,852,-28,893],1068:[653,0,597,-28,537],1069:[666,18,658,15,636],1070:[666,18,877,-32,850],1071:[653,0,635,-49,676],1072:[441,11,514,23,482],1073:[683,11,498,36,535],1074:[441,11,442,31,423],1075:[441,11,390,1,384],1076:[683,11,489,30,470],1077:[441,11,440,34,422],1078:[441,11,799,0,791],1079:[441,11,376,-18,357],1080:[441,11,527,29,495],1081:[667,11,527,29,495],1082:[441,11,491,18,485],1083:[441,12,474,-44,442],1084:[432,12,633,-45,601],1085:[441,9,504,20,472],1086:[441,11,489,29,470],1087:[441,9,511,19,479],1088:[441,205,483,-77,464],1089:[441,11,441,27,422],1090:[441,9,741,17,709],1091:[441,206,421,-61,389],1092:[683,205,702,29,677],1093:[441,11,444,-35,439],1094:[441,182,527,29,495],1095:[441,9,482,42,450],1096:[441,11,785,31,753],1097:[441,182,785,31,753],1098:[441,11,567,12,528],1099:[441,11,689,50,657],1100:[441,11,471,50,433],1101:[441,11,408,7,391],1102:[441,11,674,21,655],1103:[432,9,481,-25,449],1105:[606,11,440,34,475],1106:[683,208,479,20,448],1107:[664,11,390,1,455],1108:[441,11,428,26,441],1109:[442,13,389,-9,341],1110:[654,11,278,43,258],1111:[606,11,278,43,357],1112:[652,207,278,-172,231],1113:[441,12,679,-44,631],1114:[441,11,697,21,649],1115:[683,9,511,20,479],1116:[664,11,491,18,485],1118:[667,206,421,-61,417],1119:[441,182,527,29,495],1122:[653,0,681,19,621],1123:[683,11,542,13,504],1130:[653,0,953,-55,893],1131:[432,11,741,0,686],1138:[667,18,712,60,699],1139:[441,11,489,29,470],1140:[662,18,646,76,742],1141:[441,18,464,34,528],1168:[783,0,524,-30,622],1169:[507,11,337,42,404]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/Cyrillic.js");
