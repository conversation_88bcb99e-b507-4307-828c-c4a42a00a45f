﻿
@model GameBuskerEditDetailsViewModel

@using (Html.BeginCollectionItem("Details"))
{
    var Index = Html.GetIndex("Details");

    <div class="tr" id="Tr@(Index)">
        <div class="td" style="text-align:center">
            <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
            @Html.HiddenFor(m => m.BUSKER_ID)
            @Html.HiddenFor(m => m.BUSKER_ITEM)
            @Html.HiddenFor(m => m.TEMP_USER_ID)
            @Html.HiddenFor(m => m.SHORT_NAME)
            @Html.HiddenFor(m => m.NAME)
            @Html.HiddenFor(m => m.GAME_USER_TYPE)
            @Html.HiddenFor(m => m.USER_NO)
            @Html.HiddenFor(m => m.CLASS_NO)
            @Html.HiddenFor(m => m.SEAT_NO)
            @Html.HiddenFor(m => m.PHONE)
            @Html.HiddenFor(m => m.GAME_USER_ID)
        </div>
        <div class="td" style="text-align:center">
            @if (Model.GAME_USER_TYPE == UserType.Student)
            {
                @Model.SHORT_NAME
            }
            else
            {
                <text>卡片</text>
            }
        </div>
        <div class="td" style="text-align:center">
            @Model.NAME
        </div>
        <div class="td" style="text-align:center">

            @if (string.IsNullOrWhiteSpace(Model.GAME_USER_TYPE_DESC))
            {
                @UserType.GetDesc(Model.GAME_USER_TYPE)
            }
            else
            {
                @Model.GAME_USER_TYPE_DESC
            }
        </div>
        <div class="td" style="text-align:center">
            @Model.USER_NO
        </div>
        <div class="td" style="text-align:center">
            @Model.CLASS_NO
        </div>
        <div class="td" style="text-align:center">
            @Model.SEAT_NO
        </div>
        <div class="td" style="text-align:center">
            @Model.PHONE
        </div>
        <div class="td" style="text-align:center">
            @Model.GAME_USER_ID
        </div>
    </div>
}

