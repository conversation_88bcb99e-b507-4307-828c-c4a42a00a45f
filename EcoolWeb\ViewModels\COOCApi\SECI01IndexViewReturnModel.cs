﻿using System;
using System.Web;

namespace EcoolWeb.ViewModels.COOCApi
{
    public class SECI01IndexViewReturnModel 
    {
        public int ErrorCode { get; set; }

        public string ErrorMessage { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string NAME { get; set; }

        /// <summary>
        /// 現有酷幣點數
        /// </summary>
        public int? CASH_AVAILABLE { get; set; }

        /// <summary>
        /// 現有定存點數
        /// </summary>
        public System.Nullable<int> CASH_DEPOSIT { get; set; }

        /// <summary>
        /// 借閱本數
        /// </summary>
        public int? BOOKS { get; set; }

        /// <summary>
        /// 借閱本數 by 月
        /// </summary>
        public int? BOOKS_MONTH { get; set; }

        /// <summary>
        /// 累計酷幣點數
        /// </summary>
        public int? CASH_ALL { get; set; }

        /// <summary>
        /// 本月給予酷幣點數 (老師用)
        /// </summary>
        public int Month_Given_Cash { get; set; }

        /// <summary>
        /// 本月給予特殊點數 (老師用)
        /// </summary>
        public String Special__Cash_Limit { get; set; }

        /// <summary>
        /// 角色娃娃 圖 路徑
        /// </summary>
        public string PlayerUrl { get; set; }

        /// <summary>
        /// 線上投稿.投稿數
        /// </summary>
        public Int32 WritingCount { get; set; }

        /// <summary>
        /// 線上投稿.推薦數
        /// </summary>
        public Int32 WritingShareCount { get; set; }

        /// <summary>
        /// 閱讀認證.投稿數
        /// </summary>
        public Int32 BookCount { get; set; }

        /// <summary>
        /// 閱讀認證.推薦數
        /// </summary>
        public Int32 BookShareCount { get; set; }

        /// <summary>
        /// 閱讀等級.等級 FOR ADDT09
        /// </summary>
        public Int32 ReadLEVEL { get; set; }

        /// <summary>
        /// 閱讀認證.等級 FOR ADDT04
        /// </summary>
        public Int32 PassportLEVEL { get; set; }

        /// <summary>
        /// 校內筆數
        /// </summary>
        public Int32 SchoolInCount { get; set; }

        /// <summary>
        /// 校外筆數
        /// </summary>
        public Int32 SchoolObcCount { get; set; }

    }
}
