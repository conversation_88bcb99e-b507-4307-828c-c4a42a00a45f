﻿@{
    ViewBag.Title = "老師兌換獎品-新增獎品";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm("Awat2Insert02", "Awat2", FormMethod.Post, new { id = "contentForm", name = "contentForm", enctype = "multipart/form-data" }))
{
    <img src="~/Content/img/web-bar2-revise-23.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-Awat02" id="showView">
    @Html.Partial("../Awat2/_Awat202Form")
    
        <div class="row Div-btn-center">
            <div class="form-group">
                <div class="col-md-offset-3 col-md-3">
                    <input type="submit" value="新增獎品" class="btn btn-default" onclick="doInsert();" />
                </div>
                <div class="col-md-offset-1 col-md-3">
                    <input type="button" value="返回" class="btn btn-default" onclick="goBack();" />
                </div>
            </div>
        </div>
    </div>

}

@section Scripts
{
    <script type="text/javascript">

	
	function DateDiff(sType, beginDate, endDate) {

            var dtBegin = new_Date(beginDate);
            var dtEnd = new_Date(endDate);
            switch (sType) {
                case "s": return parseInt((dtEnd - dtBegin) / 1000); //秒
                case "n": return parseInt((dtEnd - dtBegin) / 60000); //分
                case "h": return parseInt((dtEnd - dtBegin) / 3600000); //時
                case "d": return parseInt((dtEnd - dtBegin) / 86400000);  //日
                case "w": return parseInt((dtEnd - dtBegin) / (86400000 * 7)); //週
                case "m": return (dtEnd.getMonth() + 1) + ((dtEnd.getFullYear() - dtBegin.getFullYear()) * 12) - (dtBegin.getMonth() + 1); //月
                case "y": return dtEnd.getFullYear() - dtBegin.getFullYear(); //年
            }
        }
	
	    function new_Date(sValue) {
            var TempValue

            TempValue = sValue

            if (typeof (TempValue) == 'undefined') {
                TempValue = new Date()
            }
            else {
                TempValue = new Date(TempValue)
            }
            //alert(TempValue);
            return TempValue

        }

        function ValidDate() {
            var DateNow = new Date();


            var ParamSDATETIME = $('#ParamSDATETIME').val()
            var ParamEDATETIME = $('#ParamEDATETIME').val()


            if (DateDiff("d", ParamSDATETIME, DateNow)>0)
            {
                alert('開始兌換需大於系統日');
                $('#ParamSDATETIME').val('');
            }
            
            if (DateDiff("d", ParamEDATETIME, DateNow) > 0) {
                alert('兌換期限需大於系統日');
                $('#ParamEDATETIME').val('');
            }

          
            if (DateDiff("d", ParamEDATETIME, ParamSDATETIME) > 0) {
                alert('開始兌換不能大於兌換期限');
                $('#ParamSDATETIME').val('');
                $('#ParamEDATETIME').val('');
            }

          
        }

        function goBack() {
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "Awat2Q02";
            document.contentForm.submit();
        }
        function doInsert() {
            try {
                if ($("#ParamAWARD_NO").val() != "" && $("#ParamAWARD_NO").val() != "0") {
                    alert('新增程序不正確');
                    return;
                }
                if ($("#file").val() != "") {
                    if (false == FileUpload_click()) {
                        return;
                    }
                }
            }
            catch (err) {
            }

            //document.contentForm.enctype = "multipart/form-data";
            //document.contentForm.action = "AwatInsert02";
            //document.contentForm.submit();
        }
        $(function () {
            var TData = [];
            for (var hi = 0; hi <= 23; hi++) {
                if (hi == 0)
                    TData.push("<option value='" + hi + "' selected>" + hi + "</option>");
                else
                    TData.push("<option value='" + hi + "' >" + hi + "</option>");
            }
            $('#selSH').append(TData);
            $('#selEH').append(TData);
            TData = [];
            for (var mi = 0; mi <= 59; mi++) {
                TData.push("<option value='" + mi + "' >" + (mi.toString().length > 1 ? mi : "0"+mi) + "</option>");
            }
            $('#selSM').append(TData);
            $('#selEM').append(TData);

            $("#ParamSDATETIME").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,

            });

            $("#ParamSDATETIME").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,

            });

            $("#ParamEDATETIME").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true
            });

            var Today = new Date();
            $("#ParamSDATETIME").datepicker("setDate", Today);
            Today.setMonth(Today.getMonth() + 12);
            $("#ParamEDATETIME").datepicker('setDate', Today);

        });
    </script>
}