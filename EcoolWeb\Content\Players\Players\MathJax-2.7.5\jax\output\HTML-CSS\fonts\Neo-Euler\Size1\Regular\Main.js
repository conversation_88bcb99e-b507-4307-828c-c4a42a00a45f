/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Size1/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Size1={directory:"Size1/Regular",family:"NeoEulerMathJax_Size1",testString:"\u00A0\u2016\u2044\u21D0\u21D2\u21D4\u220F\u2210\u2211\u2215\u221A\u2223\u2225\u2227\u2228",32:[0,0,333,0,0],40:[999,199,456,153,426],41:[999,199,457,30,304],47:[999,200,577,55,524],91:[1074,125,416,202,394],92:[999,200,577,55,524],93:[1074,125,416,22,214],123:[999,200,583,110,474],124:[1297,208,213,86,126],125:[999,200,583,110,474],160:[0,0,333,0,0],8214:[1297,208,403,86,316],8260:[999,200,577,55,524],8656:[598,98,1700,55,1622],8658:[598,98,1700,55,1622],8660:[598,98,1700,33,1665],8719:[1200,200,1411,56,1354],8720:[1200,200,1411,56,1354],8721:[1200,200,1444,56,1361],8725:[999,200,577,55,524],8730:[1207,2,1000,110,1027],8739:[1297,208,213,86,126],8741:[738,167,392,86,306],8743:[924,74,1124,56,1068],8744:[874,124,1124,56,1068],8745:[959,6,833,56,776],8746:[924,41,833,56,776],8747:[2022,200,555,41,517],8748:[2022,200,773,41,847],8749:[2022,200,1103,41,1177],8750:[2022,200,555,17,556],8846:[924,41,833,56,776],8896:[924,74,1124,56,1068],8897:[874,124,1124,56,1068],8898:[959,6,833,56,776],8899:[924,41,833,56,776],8968:[999,200,472,202,449],8969:[999,200,472,22,269],8970:[999,200,472,202,449],8971:[999,200,472,22,269],9001:[939,237,501,95,392],9002:[939,237,568,79,375],9180:[786,-525,1311,56,1255],9181:[31,230,1311,56,1255],9182:[878,-514,1311,56,1255],9183:[40,324,1311,56,1255],10216:[737,237,388,107,330],10217:[737,237,388,57,280],10764:[2022,200,1433,41,1507]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Size1"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size1/Regular/Main.js"]);
