/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Normal/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Normal={directory:"Normal/Regular",family:"AsanaMathJax_Normal",testString:"\u210E\uD835\uDC00\uD835\uDC01\uD835\uDC02\uD835\uDC03\uD835\uDC04\uD835\uDC05\uD835\uDC06\uD835\uDC07\uD835\uDC08\uD835\uDC09\uD835\uDC0A\uD835\uDC0B\uD835\uDC0C\uD835\uDC0D",32:[0,0,249,0,0],8462:[733,9,499,10,471],119808:[686,3,777,24,757],119809:[681,3,666,39,611],119810:[695,17,721,44,695],119811:[681,3,832,35,786],119812:[682,4,610,39,577],119813:[682,3,555,28,539],119814:[695,17,832,47,776],119815:[681,3,832,36,796],119816:[681,3,388,39,350],119817:[681,213,388,-11,350],119818:[681,3,777,39,763],119819:[681,4,610,39,577],119820:[681,10,1000,32,968],119821:[681,16,832,35,798],119822:[695,17,832,47,787],119823:[681,3,610,39,594],119824:[695,184,832,47,787],119825:[681,3,721,39,708],119826:[695,17,610,57,559],119827:[681,3,666,17,650],119828:[681,17,777,26,760],119829:[681,3,777,20,763],119830:[686,3,1000,17,988],119831:[695,3,666,17,650],119832:[695,3,666,15,660],119833:[681,3,666,24,627],119834:[471,17,499,40,478],119835:[720,17,610,10,556],119836:[471,17,443,37,414],119837:[720,17,610,42,577],119838:[471,17,499,42,461],119839:[720,3,388,34,381],119840:[471,266,555,26,535],119841:[720,3,610,24,587],119842:[706,3,332,34,298],119843:[706,266,332,3,241],119844:[720,3,610,21,597],119845:[720,3,332,24,296],119846:[471,3,888,24,864],119847:[471,3,610,24,587],119848:[471,17,555,40,517],119849:[471,258,610,29,567],119850:[471,258,610,52,589],119851:[471,3,388,30,389],119852:[471,17,443,39,405],119853:[632,17,332,22,324],119854:[471,17,610,25,583],119855:[459,3,555,11,545],119856:[471,3,832,13,820],119857:[471,3,499,20,483],119858:[459,266,555,10,546],119859:[459,3,499,16,464],119860:[705,3,751,28,724],119861:[692,6,610,26,559],119862:[706,18,687,45,651],119863:[692,3,777,28,741],119864:[692,3,610,30,570],119865:[692,3,555,0,548],119866:[706,18,721,50,694],119867:[692,3,777,-3,800],119868:[692,3,332,7,354],119869:[692,206,332,-35,358],119870:[692,3,666,13,683],119871:[692,3,555,16,523],119872:[698,18,953,-19,950],119873:[692,11,777,2,802],119874:[706,18,777,53,748],119875:[692,3,610,9,594],119876:[706,201,777,53,748],119877:[691,3,666,9,639],119878:[706,18,555,42,506],119879:[692,3,610,53,635],119880:[692,19,777,88,798],119881:[692,8,721,75,754],119882:[700,8,943,71,980],119883:[692,3,721,20,734],119884:[705,3,666,52,675],119885:[692,3,666,20,637],119886:[482,11,443,4,406],119887:[733,11,462,37,433],119888:[482,11,406,25,389],119889:[733,11,499,17,483],119890:[483,11,388,15,374],119891:[733,276,550,-25,550],119892:[482,276,499,-37,498],119894:[712,9,277,34,264],119895:[712,276,277,-70,265],119896:[733,9,468,14,455],119897:[733,9,277,36,251],119898:[482,9,777,24,740],119899:[482,9,555,24,514],119900:[482,11,443,17,411],119901:[482,276,499,-7,465],119902:[482,276,462,24,432],119903:[482,9,388,26,384],119904:[482,11,388,9,345],119905:[646,9,332,41,310],119906:[482,11,555,32,512],119907:[482,11,499,21,477],119908:[482,11,721,21,699],119909:[482,11,499,9,484],119910:[482,276,499,-8,490],119911:[482,11,443,-1,416],119912:[683,3,721,-35,685],119913:[682,3,666,8,629],119914:[695,17,684,69,695],119915:[682,3,777,0,747],119916:[681,3,620,11,606],119917:[681,3,555,-6,593],119918:[695,17,777,72,750],119919:[681,3,777,-12,826],119920:[681,3,388,-1,412],119921:[681,207,388,-29,417],119922:[681,3,721,-10,746],119923:[681,3,610,26,578],119924:[681,17,943,-23,985],119925:[681,3,777,-2,829],119926:[695,17,832,76,794],119927:[681,3,711,11,673],119928:[695,222,832,76,794],119929:[681,3,721,4,697],119930:[695,17,555,50,517],119931:[681,3,610,56,674],119932:[681,17,777,83,825],119933:[681,3,666,67,745],119934:[689,3,1000,67,1073],119935:[681,3,721,-9,772],119936:[695,3,610,54,675],119937:[681,3,666,1,676],119938:[470,17,555,44,519],119939:[726,17,536,44,494],119940:[469,17,443,32,436],119941:[726,17,555,38,550],119942:[469,17,443,28,418],119943:[726,271,449,-25,554],119944:[469,271,499,-50,529],119945:[726,17,555,22,522],119946:[695,17,332,26,312],119947:[695,271,332,-64,323],119948:[726,17,555,34,528],119949:[726,17,332,64,318],119950:[469,17,832,19,803],119951:[469,17,555,17,521],119952:[469,17,555,48,502],119953:[469,271,555,-21,516],119954:[469,271,536,32,513],119955:[469,17,388,20,411],119956:[469,17,443,25,406],119957:[636,17,388,42,409],119958:[469,17,555,22,521],119959:[469,17,555,19,513],119960:[469,17,832,27,802],119961:[469,17,499,-8,500],119962:[469,271,555,13,541],119963:[469,17,499,31,470],120484:[482,9,277,34,241],120485:[482,276,277,-70,228],120488:[686,3,777,24,757],120489:[681,3,666,39,611],120490:[681,3,555,28,533],120491:[693,0,686,31,662],120492:[681,4,610,39,577],120493:[681,3,666,24,627],120494:[681,3,832,36,796],120495:[695,17,832,47,787],120496:[681,3,388,39,350],120497:[681,3,777,39,763],120498:[693,3,777,29,757],120499:[681,9,1000,32,968],120500:[681,16,832,35,798],120501:[689,3,684,34,646],120502:[695,17,832,47,787],120503:[689,3,817,36,782],120504:[681,3,610,39,594],120505:[695,17,832,47,787],120506:[689,3,659,25,614],120507:[681,3,666,17,644],120508:[705,0,698,6,702],120509:[689,3,957,46,913],120510:[695,0,666,17,650],120511:[695,5,935,6,928],120512:[700,3,810,34,773],120513:[693,0,686,25,656],120514:[478,21,663,68,628],120515:[705,282,582,57,527],120516:[480,291,632,-19,556],120517:[707,22,556,75,519],120518:[479,21,463,67,459],120519:[704,193,558,51,533],120520:[480,282,560,-16,492],120521:[701,21,645,87,587],120522:[481,17,272,64,275],120523:[481,17,539,3,534],120524:[698,6,587,54,616],120525:[492,302,610,69,583],120526:[480,16,561,2,513],120527:[704,193,524,59,529],120528:[471,17,555,40,517],120529:[476,16,633,14,628],120530:[476,281,566,38,502],120531:[477,193,515,30,502],120532:[492,25,570,60,554],120533:[480,17,518,37,485],120534:[480,18,576,5,514],120535:[478,277,836,31,753],120536:[480,183,583,1,551],120537:[688,279,762,-3,708],120538:[480,14,817,61,755],120539:[740,17,537,52,482],120540:[470,17,528,38,477],120541:[700,18,590,32,576],120542:[439,24,666,60,712],120543:[688,279,742,47,685],120544:[476,266,562,95,535],120545:[566,14,817,68,762],120546:[705,3,721,-19,677],120547:[692,4,610,26,559],120548:[692,3,555,-39,597],120549:[697,4,688,-33,602],120550:[690,0,610,30,570],120551:[692,3,666,20,637],120552:[689,3,777,-3,800],120553:[706,18,777,53,748],120554:[689,3,332,7,345],120555:[692,3,666,13,683],120556:[697,4,753,-41,670],120557:[697,18,963,-19,940],120558:[692,11,777,2,804],120559:[689,4,692,6,673],120560:[706,18,777,53,748],120561:[689,4,812,-33,845],120562:[692,3,610,9,594],120563:[706,18,777,53,748],120564:[689,4,684,-16,645],120565:[692,3,610,53,635],120566:[691,4,733,41,778],120567:[689,4,745,21,732],120568:[692,3,721,20,734],120569:[689,4,870,79,906],120570:[704,6,824,-11,790],120571:[697,4,688,87,717],120572:[473,16,594,23,558],120573:[680,283,512,-8,476],120574:[473,273,581,18,547],120575:[701,16,497,28,455],120576:[473,15,493,54,446],120577:[712,149,504,49,473],120578:[473,275,531,38,497],120579:[702,16,613,68,576],120580:[473,16,310,62,275],120581:[473,16,571,38,542],120582:[701,16,618,25,570],120583:[473,286,610,45,565],120584:[473,7,497,2,474],120585:[701,148,547,42,501],120586:[482,11,443,17,411],120587:[467,15,653,46,613],120588:[473,284,566,34,525],120589:[463,155,501,44,458],120590:[474,15,552,28,524],120591:[463,14,519,48,476],120592:[471,12,547,39,509],120593:[485,277,681,28,643],120594:[479,193,608,-1,601],120595:[682,281,695,39,653],120596:[463,12,715,24,673],120597:[754,4,563,53,538],120598:[481,11,465,13,457],120599:[702,15,620,41,572],120600:[439,2,666,24,746],120601:[705,289,665,26,622],120602:[474,260,478,55,493],120603:[528,12,715,24,674],120604:[686,3,758,-37,703],120605:[681,3,666,12,627],120606:[681,3,555,-32,595],120607:[693,0,686,-30,604],120608:[681,0,610,12,622],120609:[681,3,666,-36,683],120610:[681,3,832,-24,856],120611:[695,17,832,38,795],120612:[681,3,388,-21,411],120613:[681,3,777,-21,798],120614:[693,3,777,-32,703],120615:[681,9,1000,-28,1029],120616:[681,15,832,-24,859],120617:[689,3,684,-2,660],120618:[695,17,832,38,795],120619:[689,3,817,-25,843],120620:[681,3,610,-21,631],120621:[695,17,832,38,795],120622:[689,3,659,-36,656],120623:[681,3,666,61,704],120624:[705,0,698,41,742],120625:[689,3,957,44,921],120626:[695,3,666,-44,708],120627:[695,5,935,62,989],120628:[700,3,810,-11,779],120629:[693,0,686,83,717],120630:[478,21,663,46,635],120631:[705,282,582,-21,581],120632:[480,291,632,32,614],120633:[707,22,556,45,505],120634:[479,21,463,47,436],120635:[704,193,558,32,535],120636:[480,279,560,31,518],120637:[701,21,645,70,605],120638:[481,17,272,30,249],120639:[481,17,539,29,546],120640:[698,7,587,2,565],120641:[492,302,610,24,581],120642:[480,16,561,29,542],120643:[705,193,524,37,550],120644:[471,17,555,32,523],120645:[476,16,633,26,624],120646:[476,279,566,-17,536],120647:[477,193,515,49,484],120648:[492,25,570,46,600],120649:[480,17,518,46,529],120650:[480,18,576,38,529],120651:[478,277,836,50,791],120652:[480,183,583,-47,600],120653:[688,279,762,33,735],120654:[480,14,817,47,767],120655:[750,13,493,26,466],120656:[470,17,528,28,508],120657:[700,20,590,10,566],120658:[439,24,666,-7,787],120659:[688,279,742,48,695],120660:[476,266,566,81,565],120661:[566,14,817,45,768],120782:[689,18,499,33,468],120783:[699,3,499,35,455],120784:[689,3,499,25,472],120785:[689,18,499,22,458],120786:[702,3,499,12,473],120787:[685,18,499,42,472],120788:[689,18,499,37,469],120789:[685,3,499,46,493],120790:[689,18,499,34,467],120791:[689,18,499,31,463]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Normal"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/Regular/Main.js"]);
