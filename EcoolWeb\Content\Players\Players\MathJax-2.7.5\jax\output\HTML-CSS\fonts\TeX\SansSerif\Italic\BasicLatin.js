/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Italic/BasicLatin.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_SansSerif-italic"],{32:[0,0,250,0,0],33:[694,0,319,110,355],34:[694,-471,500,133,472],35:[694,194,833,87,851],36:[750,56,500,56,565],37:[750,56,833,165,815],38:[716,22,758,71,747],39:[694,-471,278,190,335],40:[750,250,389,104,491],41:[750,250,389,2,390],42:[750,-306,500,156,568],43:[583,83,778,108,775],44:[98,125,278,63,209],45:[259,-186,333,51,332],46:[98,0,278,90,209],47:[750,250,500,6,600],48:[678,22,500,88,549],49:[678,0,500,88,451],50:[678,0,500,50,551],51:[678,22,500,56,544],52:[656,0,500,62,521],53:[656,22,500,50,555],54:[678,22,500,94,548],55:[656,11,500,143,596],56:[678,22,500,77,554],57:[677,22,500,77,545],58:[444,0,278,90,282],59:[444,125,278,63,282],61:[370,-130,778,88,796],63:[704,0,472,173,536],64:[705,10,667,120,707],65:[694,0,667,28,638],66:[694,0,667,90,696],67:[705,10,639,124,719],68:[694,0,722,88,747],69:[691,0,597,86,688],70:[691,0,569,86,673],71:[705,11,667,125,730],72:[694,0,708,86,768],73:[694,0,278,87,338],74:[694,22,472,46,535],75:[694,0,694,88,785],76:[694,0,542,87,516],77:[694,0,875,92,929],78:[694,0,708,88,766],79:[716,22,736,118,763],80:[694,0,639,88,690],81:[716,125,736,118,763],82:[694,0,646,88,698],83:[716,22,556,54,609],84:[688,0,681,165,790],85:[694,22,688,131,747],86:[694,0,667,161,799],87:[694,0,944,161,1076],88:[694,0,667,14,758],89:[694,0,667,151,810],90:[694,0,611,55,702],91:[750,250,289,41,425],93:[750,250,289,-31,353],94:[694,-527,500,190,533],95:[-38,114,500,50,565],97:[461,10,481,61,473],98:[694,11,517,75,539],99:[460,11,444,75,499],100:[694,10,517,73,588],101:[460,11,444,71,472],102:[705,0,306,94,494],103:[455,206,500,12,568],104:[694,0,517,73,513],105:[680,0,239,74,315],106:[680,204,267,-96,336],107:[694,0,489,76,543],108:[694,0,239,74,311],109:[455,0,794,73,790],110:[454,0,517,73,513],111:[461,11,500,69,523],112:[455,194,517,34,538],113:[455,194,517,72,538],114:[455,0,342,74,424],115:[461,11,383,35,436],116:[571,11,361,97,410],117:[444,10,517,90,537],118:[444,0,461,108,540],119:[444,0,683,108,762],120:[444,0,461,1,537],121:[444,205,461,1,540],122:[444,0,435,28,494],126:[327,-193,500,199,560]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Italic/BasicLatin.js");
