﻿

@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();


}

<div class="form-group">
    <a role="button" href='@Url.Action("EGameIndex","BarcCodeMyCash")' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="EGameIndex" ? "active":"")">
        市集說明
    </a>
    <a role="button" href='@Url.Action("BDMT05Index","BarcCodeMyCash")' class="btn btn-sm btn-sys @(ViewBag.NowAction=="BDMT05Index" ? "active":"")">
        關主設定關卡
    </a>
    <a role="button" href='@Url.Action("Game","ADDI09",new { IsFix = "True" })' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Game" ? "active":"")">
        闖關畫面
    </a>
    <a role="button" href='@Url.Action("EGameTicket0","BarcCodeMyCash")' class="btn btn-sm btn-sys @(ViewBag.NowAction=="EGameTicket0" ? "active":"")">
        來賓使用二維條碼支付
    </a>
</div>