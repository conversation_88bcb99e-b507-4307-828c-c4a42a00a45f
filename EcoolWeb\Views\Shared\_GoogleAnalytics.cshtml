﻿@{
    ECOOL_APP.UserProfile User = EcoolWeb.Models.UserProfileHelper.Get();

    if (!string.IsNullOrWhiteSpace(SharedGlobal.GoogleAnalyticsKey))
    {
        if (User != null)
        {
            <script  nonce="ecool">
          (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
          (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
          m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
          })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

            ga('create', '@SharedGlobal.GoogleAnalyticsKey', 'auto');
          ga('send', 'pageview');

        ga('set', 'userId', '@User.USER_NO'); // 使用已登入的 user_id 設定 User-ID。
            </script>
            <script async src="https://www.googletagmanager.com/gtag/js?id=G-QSMPE3E3L5"></script>
            <script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-QSMPE3E3L5');
  gtag('config', 'TAG_ID', {

'user_id': '@User.USER_NO'

});
            </script>

        }
        else
        {
            <script  nonce="ecool"> 
          (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
          (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
          m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
          })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

            ga('create', '@SharedGlobal.GoogleAnalyticsKey', 'auto');
            ga('send', 'pageview');

            </script>

            <script async src="https://www.googletagmanager.com/gtag/js?id=G-QSMPE3E3L5"></script>
            <script>
  window.dataLayer = window.dataLayer || [];
                function gtag() { dataLayer.push(arguments); }

  gtag('js', new Date());

  gtag('config', 'G-QSMPE3E3L5');
   </script>
        }
    }

}