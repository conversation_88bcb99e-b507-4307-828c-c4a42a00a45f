﻿@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
</head>
<body>
    <div>
        @using (Html.BeginForm("GenderQRCode", "Home", FormMethod.Post, new { name = "loginform", defaultbutton = "Button1", defaultfocus = "TextBox1", id = "form1" }))
        {
            <table>
                @{
                    var SchoolList = EcoolWeb.Models.UserProfileHelper.GetSchoolList().Select(x => new SelectListItem { Text = x.Value.SHORT_NAME, Value = x.Value.SCHOOL_NO });
                }
                <tr>
                    <td>
                        <div class="form-inline">
                            <div class="form-group">
                                <label class="lobinLabel">學 校 </label>
                                @Html.DropDownList("SchoolNo", SchoolList.OrderBy(a => a.Text), "選擇學校...", new { @class = "input-sm LoginBox" })
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="form-inline">
                            <div class="form-group">
                                <label class="lobinLabel">帳 號 </label>
                                @Html.TextBox("UserNo", "", new { @class = "input-xs LoginBox" })
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>

                    <td>
                        <div class="form-inline">
                            <div class="form-group">
                                <label class="lobinLabel">密 碼 </label>
                                @Html.Password("sPwd", "", new { @class = "input-xs LoginBox" })
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>

                    <td style="white-space:nowrap" align="right">
                        <input type="button" class="btn btn-default btn-sm " value="登入" onclick='GenderQRCodePost()' />
                    </td>
                </tr>
            </table>
        }
    </div>
</body>
</html>
<script>
    var targetFormID = '#form1';
    //$(document).ready(function () { GenderQRCodePost(); })
    function GenderQRCodePost() {
        // $("#SchoolNo").val("111111");
        // $("#UserNo").val("107038");
        // $("#sPwd").val("0000");
        $(targetFormID).submit();
    }
</script>