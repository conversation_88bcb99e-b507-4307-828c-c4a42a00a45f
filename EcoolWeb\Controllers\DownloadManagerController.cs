﻿using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.service;
using MvcPaging;
using Dapper;


namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class DownloadManagerController : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private static string Bre_NO = "DownloadManager";
        private static string DirectoryPath = "DLM";
        private ECOOL_APP.UserProfile user;

        public ActionResult Index(DownloadManagerIndexViewModel model)
        {
            if (model == null) model = new DownloadManagerIndexViewModel();
            ViewBag.Panel_Title = "教學文件管理";
            model.BackAction = "Index";
            model.BackController = Bre_NO;

            var listData= db.DLMT01.Where(a => a.DL_SUBJECT !=null &&　a.FileType=="F");
            if (string.IsNullOrWhiteSpace( model.whereSearch)==false)
            {
                listData = listData.Where(a => a.DL_SUBJECT.Contains(model.whereSearch));
            }
            if (string.IsNullOrWhiteSpace(model.ShowDisable) )
            {
                listData = listData.Where(a => a.STATUS != 9);
            }

            model.ListData = listData.OrderBy(a => a.DL_GROUP).ThenByDescending(a => a.CHG_DATE).ToList();
            //model.ListData = db.FAQT01.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize); 
            //db.FAQT01

            return View(model);
        }
        public ActionResult VedioADD(DownloadManagerIndexViewModel model)
        {
            if (model == null) model = new DownloadManagerIndexViewModel();
            ViewBag.Panel_Title = "教學影片管理";
            model.BackAction = "Index";
            model.BackController = Bre_NO;

            var listData = db.DLMT01.Where(a => a.DL_SUBJECT != null && a.FileType == "V");
            if (string.IsNullOrWhiteSpace(model.whereSearch) == false)
            {
                listData = listData.Where(a => a.DL_SUBJECT.Contains(model.whereSearch));
            }
            if (string.IsNullOrWhiteSpace(model.ShowDisable))
            {
                listData = listData.Where(a => a.STATUS != 9);
            }

            model.ListData = listData.OrderBy(a => a.DL_GROUP).ThenByDescending(a => a.CHG_DATE).ToList();
            //model.ListData = db.FAQT01.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize); 
            //db.FAQT01

            return View(model);
        }
        [AllowAnonymous]
        public ActionResult Index2(DownloadManagerIndex2ViewModel model)
        {
            if (model == null) model = new DownloadManagerIndex2ViewModel();
            ViewBag.Panel_Title = "教學文件/影片管理";
            model.BackAction = "Index";
            model.BackController = Bre_NO;
            List<DLMT01> dLMT01s = new List<DLMT01>();
            var items = db.DLMT01.Where(a => a.DL_SUBJECT != null);
            if (string.IsNullOrWhiteSpace(model.whereSearch) == false)
            {
                items = items.Where(a => a.DL_SUBJECT.Contains(model.whereSearch) && a.FileType== model.FileType);
                List<DLMT01> Item2 = db.DLMT01.Where(a => a.DL_SUBJECT != null && a.FileType != model.FileType).ToList();
                dLMT01s.AddRange(Item2);
            }
            if (string.IsNullOrWhiteSpace(model.ShowDisable))
            {
                items = items.Where(a => a.STATUS != 9);
            }
       


              var ListData = items.OrderBy(a => a.DL_GROUP).ThenByDescending(a => a.CHG_DATE);
            dLMT01s.AddRange(items.ToList());
            model.GroupDataItem = new SortedList<string, SortedList<string, List<DLMT01>>>();
            model.GroupData = new SortedList<string, List<DLMT01>>();
            string[] GroupDataTypeItem = { "F", "V" };
            foreach (var info in GroupDataTypeItem) {
                SortedList<string, List<DLMT01>> GroupDataItem=     new SortedList<string, List<DLMT01>>();
                foreach (var it in dLMT01s.Where(x=>x.FileType== info))
                {

                    string gKey = it.DL_GROUP;
                    List<DLMT01> gList;
                    if (model.GroupData.ContainsKey(gKey) == false)
                    {
                        model.GroupData.Add(gKey, new List<DLMT01>());
                        GroupDataItem.Add(gKey, new List<DLMT01>());
                    }
                    gList = model.GroupData[gKey];
                    gList.Add(it);
                    GroupDataItem[gKey] = gList;
                    // gList.Add(it,);

                }
                model.GroupDataItem.Add(info, GroupDataItem);
                }
            if (model.FileType == "F") {
                model.ACT = "F";
            }
            else {
                model.ACT = "VF";

            }
            //foreach(var it in items)
            //{
            //    string gKey = it.DL_GROUP;
            //    List<DLMT01> gList;
            //    if (model.GroupData.ContainsKey(gKey)==false)
            //    {
            //        model.GroupData.Add(gKey, new List<DLMT01>());
            //    }
            //    gList = model.GroupData[gKey];
            //    gList.Add(it);
            //}

            return View(model);
        }

        [AllowAnonymous]
        public ActionResult DownLoad(string DL_ID, string FILE_NAME)
        {

            string TargetPath = System.Web.HttpContext.Current.Server.MapPath(SharedGlobal.DownloadFilePath);// + @"\" + entity.DL_ID + @"\" + DirectoryPath + @"\" + NEWS_ID;
            TargetPath = TargetPath + @"\" + DirectoryPath + @"\" + DL_ID;
            string DownLoadFile = TargetPath + "\\" + FILE_NAME;

            if (System.IO.File.Exists(DownLoadFile))
            {
                return File(System.IO.File.ReadAllBytes(DownLoadFile), "application/unknown", Server.HtmlEncode(FILE_NAME));
            }
            else
            {

                return RedirectToAction(ErrorHelper.ErrorVal.NotFileError, "Error");
            }
        }

        public ActionResult VedioADDEdit(string DL_ID) {

            ViewBag.Panel_Title = "教學影片管理";
            ModelState.Clear();

            DLMT01 entity = null;

            if (string.IsNullOrWhiteSpace(DL_ID) == false)
            {
                entity = db.DLMT01.Where(a => a.DL_ID == DL_ID).FirstOrDefault();
            }
            if (entity == null)
            {
                entity = new DLMT01();
               
                entity.DL_ID = Guid.NewGuid().ToString("N");
                entity.YOUTUBE_URL = "";
                entity.Orderby =0;
                entity.FileType = "V";
                entity.CHG_DATE = DateTime.Now;
                entity.STATUS = 0;
            }

            return View(entity);

        }
        public ActionResult Edit(string DL_ID)
        {
            ViewBag.Panel_Title = "教學文件管理";
            ModelState.Clear();

            DLMT01 entity = null;

            if (string.IsNullOrWhiteSpace(DL_ID) == false)
            {
                entity = db.DLMT01.Where(a => a.DL_ID == DL_ID).FirstOrDefault();
            }
            if (entity == null)
            {
                entity = new DLMT01();
                entity.FileType = "F";
                entity.DL_ID = Guid.NewGuid().ToString("N");
                entity.CHG_DATE = DateTime.Now;
                entity.STATUS = 0;
            }

            return View(entity);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditSave(DLMT01 entity, List<HttpPostedFileBase> files)
        {
            ViewBag.Panel_Title = "教學文件管理";
            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
                TempData["StatusMessageCenter"] = Message;
                return View("Edit");
            }

            DLMT01 oldEntity = db.DLMT01.Find(entity.DL_ID);
            if (oldEntity != null)
            {
                oldEntity.DL_SUBJECT = entity.DL_SUBJECT;
                oldEntity.DL_GROUP = entity.DL_GROUP;
                oldEntity.DL_MEMO = entity.DL_MEMO;
                oldEntity.YOUTUBE_URL = entity.YOUTUBE_URL;
                oldEntity.FileType = entity.FileType;
                oldEntity.Orderby = entity.Orderby;
                oldEntity.CHG_DATE = DateTime.Now;
            }
            else
            {
                db.DLMT01.Add(entity);
            }

            if (files != null)
            {
                if (files.Count > 0)
                {

                    string TargetPath = System.Web.HttpContext.Current.Server.MapPath(SharedGlobal.DownloadFilePath);// + @"\" + entity.DL_ID + @"\" + DirectoryPath + @"\" + NEWS_ID;
                    TargetPath = TargetPath + @"\" + DirectoryPath + @"\" + entity.DL_ID;
                    if (System.IO.Directory.Exists(TargetPath) == false)
                    {
                        System.IO.Directory.CreateDirectory(TargetPath);
                    }

                    foreach (var file in files)
                    {
                        if (file != null && file.ContentLength > 0)
                        {
                            string fileName = System.IO.Path.GetFileName(file.FileName);
                            string UpLoadFile = TargetPath + "\\" + fileName;

                            if (System.IO.File.Exists(UpLoadFile))
                            {
                                System.IO.File.Delete(UpLoadFile);
                            }
                            file.SaveAs(UpLoadFile);

                            if (oldEntity != null) oldEntity.FILENAME = fileName;
                            if (entity != null) entity.FILENAME = fileName;
                        }
                    }
                }
            }

            db.SaveChanges();
            TempData["StatusMessageCenter"] = "儲存完成";
            if (entity.FileType == "F")
            {

                return RedirectToAction("Index");

            }
            else {

                return RedirectToAction("VedioADD");

            }

        }

        public ActionResult Delete(string DL_ID)
        {
            string Message = string.Empty;

            DLMT01 entity = null;

            if (string.IsNullOrWhiteSpace(DL_ID) == false)
            {
                entity = db.DLMT01.Where(a => a.DL_ID == DL_ID).FirstOrDefault();
            }
            if (entity == null)
            {
                TempData["StatusMessageCenter"] = "找不到資料";
                return RedirectToAction("Index");
            }

            entity.STATUS = 9;
            db.SaveChanges();

            TempData["StatusMessageCenter"] = "刪除完成";
            return RedirectToAction("Index");
        }

    }
}