﻿using ECOOL_APP;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using com.ecool.service;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using ECOOL_APP.com.ecool.Models.DTO;
using log4net;
using Dapper;
using ECOOL_APP.com.ecool.service;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// /參數設定檔
    /// </summary>
    [SessionExpire]
    public class ZZZI27Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI27";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private ZZZI27Service Service = new ZZZI27Service();

        [CheckPermission] //檢查權限
        public ActionResult Index()
        {
            this.Shared("選擇功能");
            ZZZI27IndexViewModel model = new ZZZI27IndexViewModel();
            if (model.Search == null) model.Search = new ZZZI27SearchViewModel();
            if (string.IsNullOrWhiteSpace(model.Search.wSCHOOL_NO))
            {
                if (user.ROLE_LEVEL == 0)
                {
                    model.Search.wSCHOOL_NO = SharedGlobal.ALL;
                }
                else
                {
                    model.Search.wSCHOOL_NO = DefaultSCHOOL_NO;
                }
            }
            var Data = db.BDMT02.Where(A => (A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO != "Home" && A.DATA_CODE != "TeacherIndex") 
           ||
           (A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == "Home" && A.DATA_CODE == "TeacherIndex" && A.SCHOOL_NO == model.Search.wSCHOOL_NO)
            );

            if (user.ROLE_LEVEL != 0)
            {
                Data = Data.Where(a => a.ALL_SCHOOL_SET_YN == SharedGlobal.Y);
            }

            model.Data_List = Data.ToList();

            return View(model);
        }
        [CheckPermission] //檢查權限
        public ActionResult LeaderIndex()
        {
            this.Shared("批次加扣點—班級幹部多筆");
            ZZZI27IndexViewModel model = new ZZZI27IndexViewModel();
            if (model.Search == null) model.Search = new ZZZI27SearchViewModel();
            if (string.IsNullOrWhiteSpace(model.Search.wSCHOOL_NO))
            {
                if (user.ROLE_LEVEL == 0)
                {
                    model.Search.wSCHOOL_NO = SharedGlobal.ALL;
                }
                else
                {
                    model.Search.wSCHOOL_NO = DefaultSCHOOL_NO;
                }
            }
            var Data = db.BDMT02.Where(A => (A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO != "Home" && A.DATA_CODE != "TeacherIndex") || A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == "Home" && A.DATA_CODE == "TeacherIndex" && A.SCHOOL_NO == model.Search.wSCHOOL_NO);
           Data = Data.Where(Btn => Btn.BRE_NO + Btn.DATA_CODE == "ADDI14SYS_TABLE_Person_TYPE" || Btn.BRE_NO + Btn.DATA_CODE == "ADDI14SYS_TABLE_TYPE");
            if (user.ROLE_LEVEL != 0)
            {
                Data = Data.Where(a => a.ALL_SCHOOL_SET_YN == SharedGlobal.Y);
            }
            model.Data_List = Data.ToList();
            return View(model);
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Edit(ZZZI27EditViewModel model)
        {
            this.Shared("詳細內容");
            if (model == null) model = new ZZZI27EditViewModel();
            if (model.Search == null) model.Search = new ZZZI27SearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.wSCHOOL_NO))
            {
                if (user.ROLE_LEVEL == 0)
                {
                    model.Search.wSCHOOL_NO = SharedGlobal.ALL;
                }
                else
                {
                    model.Search.wSCHOOL_NO = DefaultSCHOOL_NO;
                }
            }
           // ViewBag.BarcCodeMyCashItem = BDMT02Service.GetRefSelectListItem("BarcCodeMyCash", "BarcCodeMyCash_Mode", "ALL", "ALL", "", false, null, false, null, ref db);
            this.EditShared(model);

            model = Service.GetEditData(model, ref db);
            if (model.T_BDMT02.DATA_CODE == "BarcCodeMyCash_Mode")
            {
                string str = "";
                var REF = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.DATA_TYPE == model.T_BDMT02.DATA_TYPE && A.SCHOOL_NO == model.Search.wSCHOOL_NO && A.IS_SELECT == 1).ToList();
                if (REF.Count() != 0)
                {
                    str = REF.FirstOrDefault().CONTENT_TXT;

                }
                ViewBag.BarcCodeMyCashItem = BDMT02Service.GetRefSelectListItem("BarcCodeMyCash", "BarcCodeMyCash_Mode", "ALL", model.Search.wSCHOOL_NO, str, false, null, false, null, ref db);

            }
            if (model.IsLoadingDefault)
            {
                TempData["StatusMessage"] = "已載入預設值";
            }

            return View(model);
        }

        public void EditShared(ZZZI27EditViewModel model)
        {
            model.T_BDMT02 = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE).FirstOrDefault();
            string str = "系統預設值";
            if (model.Search.wBRE_NO == "Home" && model.Search.wDATA_CODE == "TeacherIndex")
            {
                model.T_BDMT02 = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).FirstOrDefault();
            }
            if (model.T_BDMT02 != null && model.T_BDMT02.DATA_CODE == "TeacherIndex" &&  user.ROLE_LEVEL == 0) {

                str = "所有學校";
            }
            var SchoolNoSelectItem = new List<SelectListItem>();

            if (user.ROLE_LEVEL != 0)
            {
                SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.Search.wSCHOOL_NO, null, user, false, null, null, false);
                SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == user.SCHOOL_NO).ToList();
            }

            if (user.ROLE_LEVEL == 0)
            {
                SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.Search.wSCHOOL_NO, null, user, true, str, "ALL", true);

                if (model.T_BDMT02.ALL_SCHOOL_SET_YN == SharedGlobal.N)
                {
                    SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == SharedGlobal.ALL).ToList();
                }
            }

            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;
        }

        public ActionResult EditSave(ZZZI27EditViewModel model)
        {
            this.Shared("詳細內容");
            if (model == null) model = new ZZZI27EditViewModel();
            if (model.Search == null) model.Search = new ZZZI27SearchViewModel();
            if (string.IsNullOrWhiteSpace(model.Search.wSCHOOL_NO))
            {
                if (user.ROLE_LEVEL == 0)
                {
                    model.Search.wSCHOOL_NO = SharedGlobal.ALL;
                }
                else
                {
                    model.Search.wSCHOOL_NO = DefaultSCHOOL_NO;
                }
            }
            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = Service.SaveEditData(model, user, ref db, ref Message);

                if (OK)
                {
                    TempData["StatusMessage"] = "儲存完成";
                    ModelState.Clear();
                    model.IsLoadingDefault = false;
                    var Qmodel = Service.GetEditData(model, ref db);
                    if (model.T_BDMT02.DATA_CODE == "BarcCodeMyCash_Mode")
                    {
                        string str = "";
                        var REF = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.DATA_TYPE == model.T_BDMT02.DATA_TYPE && A.SCHOOL_NO == model.Search.wSCHOOL_NO && A.IS_SELECT == 1).ToList();
                        if (REF.Count() != 0)
                        {
                            str = REF.FirstOrDefault().CONTENT_TXT;

                        }
                        ViewBag.BarcCodeMyCashItem = BDMT02Service.GetRefSelectListItem("BarcCodeMyCash", "BarcCodeMyCash_Mode", "ALL", "ALL", str, false, null, false, null, ref db);

                    }

                    this.EditShared(Qmodel);

                    return View("Edit", Qmodel);
                }
            }

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            if (model.T_BDMT02.DATA_CODE == "BarcCodeMyCash_Mode")
            {
                string str = "";
                var REF = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.DATA_TYPE == model.T_BDMT02.DATA_TYPE && A.SCHOOL_NO == model.Search.wSCHOOL_NO && A.IS_SELECT==1).ToList();
                if (REF.Count() != 0) {
                    str = REF.FirstOrDefault().CONTENT_TXT;

                }
                ViewBag.BarcCodeMyCashItem = BDMT02Service.GetRefSelectListItem("BarcCodeMyCash", "BarcCodeMyCash_Mode", "ALL", "ALL", str, false, null, false, null, ref db);
               
            }


            this.EditShared(model);

            TempData["StatusMessage"] = Message;
            ModelState.Clear();
            model.IsLoadingDefault = false;
            return View("Edit", model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _REF(ZZZI27DetailsViewModel Item)
        {
            return PartialView(Item);
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _REF1(ZZZI27DetailsViewModel Item)
        {
            return PartialView(Item);
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _ATMREF(List<ZZZI27DetailsViewModel> Item)
        {
          
            ViewBag.DelayTime = Item.FirstOrDefault().DelayTime;
            ZZZI27DetailsViewModel Item2 = new ZZZI27DetailsViewModel();
            Item2.DelayTime = Item.FirstOrDefault().DelayTime;
            //Item.Where(x=>x.IS_SELECT==1).
            //退回原因選單
            ViewBag.BarcCodeMyCashItem = BDMT02Service.GetRefSelectListItem("BarcCodeMyCash", "BarcCodeMyCash_Mode", "ALL", Item.FirstOrDefault().SCHOOL_NO, "", true, null, true, null, ref db);
            return PartialView(Item);
        }

        public ActionResult EditDel(ZZZI27EditViewModel model)
        {
            this.Shared("詳細內容");
            if (model == null) model = new ZZZI27EditViewModel();
            if (model.Search == null) model.Search = new ZZZI27SearchViewModel();

            string Message = string.Empty;

            bool OK = Service.SaveEditDelData(model, user, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "整筆刪除完成";
                var Qmodel = Service.GetEditData(model, ref db);

                this.EditShared(Qmodel);
                return View("Edit", Qmodel);
            }

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            this.EditShared(model);
            TempData["StatusMessage"] = Message;

            return View("Edit", model);
        }

        #region Shared

        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}