﻿$(document).ready(function () {
    const totalGraphModule = {

        init: function () {
            this.setGlobalFunction();
            this.bindEvents();

        },
        bindEvents: function () {
            $('a[onclick*="OnGo"]').on('click', this.handleNavigation.bind(this));
            $('form[name="form1"]').on('submit', this.handelFormSubmit.bind(this));
        },
        handelFormSubmit: function () {
            try {
                return false;
            }
            catch (error) { 

                event.preventDefault();
                AddI05Common.showMessage('送出時發生錯誤，請稍後再試', 'error');
                return false;
            }

        },
        setGlobalFunction: function () {
            window.onGo = this.onGo.bind(this);
        },
        handleNavigation: function () {
            event.preventDefault();
            try {
                const $button = $(event.target);
                const onclick = $button.attr['onclick'];
                if (onclick) {
                    const match = onclick.match(/onGo\('([^']*)'\)/);
                    if (match) {
                        const action = match[1];
                        this.onGo(action);
                    }
                }

            } catch (error) {
                console.error('導航處理時發生錯誤:', error);
                ADDI05Common.showMessage('導航時發生錯誤，請稍後再試', 'error');

            }
        },
        onGo: function (actionVal) {
            return AddI05Common.onGo(actionVal);
            
        },
        fn_save: function () {
            $(".sticky-col").remove();
            $(".sticky-intersect").remove();

            var blob = new Blob([document.getElementById('TableData').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });
            var strFile = "Report.xls";

            saveAs(blob, strFile);
            funAjax();
            return false;
        }
    }
    totalGraphModule.init();
    window.addEventListener('error', function (e) {
        console.error('頁面錯誤', e);

    })
})