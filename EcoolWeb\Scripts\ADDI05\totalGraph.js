﻿// JavaScript for ADDI05 TotalGraph page - 統計圖表
$(document).ready(function () {
    const totalGraphModule = {
        init: function () {
            this.setupGlobalFunctions();
            this.bindEvents();
            this.initializeColorbox();
            this.initializeSearch();
        },

        bindEvents: function () {
            // 綁定導航按鈕事件
            $('a[onclick*="onGo"]').on('click', this.handleNavigation.bind(this));

            // 綁定表單提交事件
            $('form[name="form1"]').on('submit', this.handleFormSubmit.bind(this));

            // 綁定搜尋按鈕事件
            $('input[onclick*="onSearch"]').on('click', this.handleSearch.bind(this));

            // 綁定匯出按鈕事件
            $('button[onclick*="fn_save"]').on('click', this.handleExport.bind(this));
        },

        setupGlobalFunctions: function () {
            // 設置全局函數以保持向後兼容
            window.onGo = this.onGo.bind(this);
            window.onSearch = this.onSearch.bind(this);
            window.fn_save = this.fn_save.bind(this);
        },

        onGo: function (actionVal) {
            // 使用共用的onGo函數
            return ADDI05Common.onGo(actionVal);
        },

        onSearch: function() {
            try {
                const className = $("#CLASS_NO1").val().trim();

                if (className) {
                    // 隱藏所有行
                    $(".CLASS_NO").hide();
                    // 顯示符合條件的行
                    $(".CLASS_NO." + className).show();

                    console.log('TotalGraph 搜尋班級:', className);
                } else {
                    // 顯示所有行
                    $(".CLASS_NO").show();
                    console.log('TotalGraph 顯示所有班級');
                }

                return true;
            } catch (error) {
                console.error('TotalGraph 搜尋時發生錯誤:', error);
                ADDI05Common.showMessage('搜尋時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        fn_save: function () {
            try {
                // 移除 sticky 相關元素
                $(".sticky-col").remove();
                $(".sticky-intersect").remove();

                const tableData = document.getElementById('TableData');
                if (!tableData) {
                    ADDI05Common.showMessage('找不到表格資料', 'error');
                    return false;
                }

                // 創建 Excel 檔案
                const blob = new Blob([tableData.innerHTML], {
                    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                });

                const fileName = "TotalGraph_Report_" + new Date().toISOString().slice(0, 10) + ".xls";

                if (typeof saveAs === 'function') {
                    saveAs(blob, fileName);
                    console.log('TotalGraph Excel 檔案已儲存:', fileName);
                    ADDI05Common.showMessage('Excel 檔案匯出成功', 'success');
                } else {
                    console.error('saveAs 函數不可用');
                    ADDI05Common.showMessage('匯出功能不可用，請檢查相關檔案是否載入', 'error');
                    return false;
                }

                // 如果有 funAjax 函數則調用
                if (typeof funAjax === 'function') {
                    funAjax();
                }

                return true;
            } catch (error) {
                console.error('TotalGraph 匯出 Excel 時發生錯誤:', error);
                ADDI05Common.showMessage('匯出 Excel 時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        initializeColorbox: function() {
            try {
                // 初始化 colorbox
                if (typeof $.colorbox === 'function') {
                    $(".progress").colorbox({
                        opacity: 0.82,
                        width: "70%",
                        innerHeight: "500px",
                        scrolling: true
                    });
                    console.log('TotalGraph Colorbox 已初始化');
                } else {
                    console.warn('Colorbox 插件未載入');
                }
            } catch (error) {
                console.error('TotalGraph 初始化 Colorbox 時發生錯誤:', error);
            }
        },

        initializeSearch: function() {
            try {
                // 為搜尋輸入框添加 Enter 鍵支援
                $("#CLASS_NO1").on('keypress', function(e) {
                    if (e.which === 13) { // Enter 鍵
                        e.preventDefault();
                        this.onSearch();
                    }
                }.bind(this));

                console.log('TotalGraph 搜尋功能已初始化');
            } catch (error) {
                console.error('TotalGraph 初始化搜尋功能時發生錯誤:', error);
            }
        },

        // 事件處理器
        handleNavigation: function (event) {
            event.preventDefault();
            try {
                const $button = $(event.target);
                const onclick = $button.attr('onclick');

                if (onclick) {
                    const match = onclick.match(/onGo\('([^']*)'\)/);
                    if (match) {
                        const action = match[1];
                        this.onGo(action);
                    }
                }
            } catch (error) {
                console.error('TotalGraph 導航處理時發生錯誤:', error);
                ADDI05Common.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },

        handleFormSubmit: function (event) {
            try {
                console.log('TotalGraph 表單提交');
                return true;
            } catch (error) {
                console.error('TotalGraph 表單提交時發生錯誤:', error);
                event.preventDefault();
                ADDI05Common.showMessage('表單提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        handleSearch: function(event) {
            event.preventDefault();
            this.onSearch();
        },

        handleExport: function(event) {
            event.preventDefault();
            this.fn_save();
        }
    };

    // 初始化模組
    totalGraphModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('TotalGraph 頁面錯誤:', e);
    });
});