﻿@{
    ViewBag.Title = "重設密碼";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@using (Html.BeginForm("DOModifyPassword", "ZZT08", FormMethod.Post, new { id = "ZZT08" }))
{

    @Html.Hidden("sControllerName", (string)ViewBag.sControllerName)
    @Html.Hidden("sActionName", (string)ViewBag.sActionName)

}

@section css{
    <style>
        body{
            background-image:url('@Url.Content("~/Content/img/web-01.png")');
            background-repeat:repeat;
        }
    </style>
}
<table class="w-100">
    <tr><td align="center" valign="middle"><img src="@Url.Content("~/Content/img/icon-check.png")" width="70" /></td></tr>
    <tr>

        <td align="center" valign="middle">

            <h4>@ViewBag.Msg</h4>
            <input type="button" id="btnColorboxClose" value="關閉" class="btn-primary btn btn-sm" onclick="ColorboxClose();" />
        </td>
    </tr>
</table>
@section scripts{
    <script type="text/javascript">
    var targetFormID = "";
    function ColorboxClose() {
        targetFormID = "#ZZT08";
        $(targetFormID).attr('action', '@Url.Action("DOModify")');
                $(targetFormID).submit();
    }
    </script>
}