﻿@model EcoolWeb.ViewModels.AWAT15PrintViewModel

    @using (Html.BeginCollectionItem("Chk"))
    {
        @Html.CheckBoxFor(m => m.CheckBoxNo, new { @class = "css-checkbox" })
        if (Model.AWAT15 != null)
        {
            @Html.HiddenFor(m => m.AWAT15.AWAT15NO)
            @Html.HiddenFor(m => m.AWAT15.USER_NO)
            @Html.HiddenFor(m => m.AWAT15.SCHOOL_NO)
           
            @Html.HiddenFor(m => m.AWAT15.CLASS_NO)
            @Html.HiddenFor(m => m.AWAT15.SEAT_NO)
            @Html.HiddenFor(m => m.AWAT15.NAME)
            @Html.HiddenFor(m => m.AWAT15.SNAME)
            @Html.HiddenFor(m => m.AWAT15.CreatDate)
            @Html.HiddenFor(m => m.AWAT15.CASH_Rank)
            @Html.HiddenFor(m => m.AWAT15.IsReMark)
          
          
        }

      
    }