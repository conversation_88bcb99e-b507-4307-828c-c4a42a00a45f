/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Shapes/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Shapes={directory:"Shapes/Regular",family:"AsanaMathJax_Shapes",testString:"\u2422\u2423\u25B7\u25BA\u25BB\u25C1\u25C4\u25C5\u25CB\u25CE\u25CF\u25E6\u25E7\u25E8\u25EB",32:[0,0,249,0,0],9250:[726,28,552,-27,508],9251:[262,0,726,35,691],9655:[578,1,667,45,622],9658:[515,-26,838,65,774],9659:[515,-26,838,65,774],9665:[578,1,667,45,622],9668:[515,-26,838,65,774],9669:[515,-26,838,65,774],9675:[705,164,906,18,889],9678:[705,164,906,18,889],9679:[705,164,906,18,889],9702:[466,-75,522,65,458],9703:[560,0,688,65,623],9704:[560,0,688,65,623],9707:[560,0,688,65,623],9723:[480,0,598,64,534],9724:[480,0,598,64,534],9733:[778,98,1013,46,968],9734:[778,98,1013,46,968],9828:[642,21,570,23,547],9829:[591,7,636,44,593],9830:[642,101,559,44,516],9831:[605,21,607,23,585],9833:[701,19,319,19,301],9834:[701,19,525,19,507],9856:[669,23,982,145,837],9857:[669,23,982,145,837],9858:[669,23,982,145,837],9859:[669,23,982,145,837],9860:[669,23,982,145,837],9861:[669,23,982,145,837],11008:[583,139,854,65,785],11009:[583,139,854,65,785],11010:[583,139,854,65,785],11011:[583,139,854,65,785],11012:[554,12,1128,64,1064],11013:[554,12,1013,64,950],11014:[713,172,678,56,622],11015:[713,172,678,56,622],11016:[583,139,852,65,785],11017:[583,139,852,65,785],11018:[583,139,852,65,785],11019:[583,139,852,65,785],11020:[554,12,1128,64,1064],11021:[751,209,694,63,629],11022:[425,-48,968,65,904],11023:[425,-48,968,65,904],11024:[425,-48,968,65,904],11025:[425,-48,968,65,904],11034:[674,6,800,60,740],11035:[703,0,843,70,773],11036:[703,0,843,70,773],11056:[504,-33,1089,27,1063],11057:[845,305,1013,65,949],11058:[524,-17,1013,65,949],11059:[486,-55,1513,38,1476],11060:[486,-55,1013,65,949],11061:[486,-55,1013,65,949],11062:[486,-55,1013,65,949],11063:[486,-55,1150,27,1124],11064:[486,-55,1211,63,1147],11065:[489,-58,1150,28,1123],11066:[486,-55,1150,86,1066],11067:[486,-55,1150,28,1122],11068:[486,-55,1150,28,1123],11069:[486,-55,1150,28,1123],11070:[486,-55,1013,65,949],11071:[484,-53,961,-3,902],11072:[613,-41,1013,65,949],11073:[486,-55,1013,65,949],11074:[564,22,1013,65,949],11075:[535,-7,1013,65,960],11076:[535,-10,1013,65,957],11077:[647,107,1013,64,950],11078:[647,107,1013,64,950],11079:[486,-55,1013,65,949],11080:[486,136,1013,65,949],11081:[486,-55,1013,65,949],11082:[486,136,1013,65,949],11083:[486,-55,1013,65,949],11084:[486,-55,1013,65,949],11088:[577,37,708,32,678],11089:[458,2,554,35,519],11090:[458,2,554,35,519]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Shapes"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Shapes/Regular/Main.js"]);
