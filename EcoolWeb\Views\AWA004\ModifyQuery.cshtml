﻿@model EcoolWeb.ViewModels.AWA004_006_ModifyQueryViewModel

@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    int i = 0;
    string iUser_no = (user != null) ? user.USER_NO : "";

    string role = Model.RoleName;
    string student = "Student";
    string teacher = "Teacher";

    bool BoolbtnTRANS_NO =
        role == student ?
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("AWA004", "ModifyQuery", null) :
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("AWA006", "ModifyQuery", null);

    bool BoolbtnModifyQuery =
        role == teacher ?
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("ModifyQuery", "ModifyQuery", null) :
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("ModifyQuery", "ModifyQuery", null);
}

@helper  buttonFun(string SchoolNO)
{

    string STATUS_Receive = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive.ToString()) ? "active" : "";
    string STATUS_UN = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString()) ? "active" : "";
    string STATUS_Del = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del.ToString()) ? "active" : "";

    <div class="row">
        <div class="col-md-12 col-xs-12 text-right">
            <samp>兌換狀態：</samp>
            <button class="btn btn-xs btn-pink  @STATUS_UN" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN');doSearch('whereUserNo', '');">未領取</button>
            <button class="btn btn-xs btn-pink  @STATUS_Receive" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive');doSearch('whereUserNo', '');">已頒發</button>
            <button class="btn btn-xs btn-pink  @STATUS_Del" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del');doSearch('whereUserNo', '');">已取消</button>
        </div>
    </div>

    string AWARD_SCHOOL_NO_EMPTY = (string.IsNullOrWhiteSpace(Model.whereAWARD_SCHOOL_NO)) ? "active" : "";
    string AWARD_SCHOOL_NO_ALL = (Model.whereAWARD_SCHOOL_NO == "ALL") ? "active" : "";
    string AWARD_SCHOOL_NO_Me = (Model.whereAWARD_SCHOOL_NO == SchoolNO && string.IsNullOrWhiteSpace(Model.whereAWARD_SCHOOL_NO) == false) ? "active" : "";

    string AWARD_TYPE_All = (string.IsNullOrWhiteSpace(Model.whereAWARD_TYPE)) ? "active" : "";
    string AWARD_TYPE_A = (Model.whereAWARD_TYPE == "A") ? "active" : "";
    string AWARD_TYPE_T = (Model.whereAWARD_TYPE == "T") ? "active" : "";
    string AWARD_TYPE_S = (Model.whereAWARD_TYPE == "S") ? "active" : "";
    string AWARD_TYPE_PC = (Model.whereAWARD_TYPE == "P,C") ? "active" : "";

    <br />
    <div class="row">
        <div class="col-md-6 col-xs-12 text-left">
            <samp>獎品類別：</samp>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_EMPTY" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', '');">全部</button>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_ALL" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', 'ALL');">總召學校獎品</button>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_Me" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', '@SchoolNO');">本校獎品</button>
        </div>
        <div class="col-md-6 col-xs-12 text-right">
            <samp>獎品狀態：</samp>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_All" type="button" onclick="doSearch('whereAWARD_TYPE', '')">全部</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_A" type="button" onclick="doSearch('whereAWARD_TYPE', 'A')">活動</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_T" type="button" onclick="doSearch('whereAWARD_TYPE', 'T')">票券</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_S" type="button" onclick="doSearch('whereAWARD_TYPE', 'S')">實體</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_PC" type="button" onclick="doSearch('whereAWARD_TYPE', 'P,C')">募資</button>
        </div>
    </div>
}

@section css{
    <link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
    <style>
        .orderColumn {
            color: #6badcf
        }

        .mybox {
            height: 2rem;
            width: auto;
        }

        .imgCol {
            display: none;
        }
    </style>
}

@section scripts{
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>

    <script type="text/javascript">
        var targetFormID = '#AWA004';

        $(document).ready(function () {
            $(".mybox").colorbox({ opacity: 0.82 });
        });

        function btnTRANS_NO_onclick(TRANS_NO) {
            var yes = confirm('你確認要取消領取嗎?');
            if (yes) {
            $("#hidSelectTRANS_NO").val(TRANS_NO);
            document.AWA004.enctype = "multipart/form-data";
            document.AWA004.action = "CancelTrans";
                document.AWA004.submit();
            }
        }
        function btnSend_onclick() {
            document.AWA004.enctype = "multipart/form-data";
            document.AWA004.action = "Modify";
            document.AWA004.submit();
        }
        function PrintSetView() {
            if ($(".css-checkbox:checked").length == 0) {
                alert('請勾選要通知的人員')
                return false;
            }
            else {

                document.AWA004.enctype = "multipart/form-data";
                document.AWA004.action = "PrintSetView";
                document.AWA004.submit();
            }
        }
        function exportExcel() {
           if ($(".css-checkbox:checked").length == 0) {
                alert('請勾選要通知的人員')
                return false;
            }
            else {

                document.AWA004.enctype = "multipart/form-data";
                document.AWA004.action = "ExportExcel";
                document.AWA004.submit();
            }
        }
        //↓出自：http://stackoverflow.com/questions/1219860/javascript-jquery-html-encoding
        function htmlEncode(value) {
            return $('<div />').text(value).html();
        }

        $("#chkALL").click(function () {

            if ($("#chkALL").prop("checked")) {
                $("input:checkbox").each(function () {
                    if ($(this).attr("id") != 'cbPicture') {

                        $(this).prop("checked", true);
                    }

                });
            }
            else {
                $("input:checkbox").each(function () {
                    if ($(this).attr("id") != 'cbPicture') {
                        $(this).prop("checked", false);
                    }
                });
            }
        });

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function todoClear() {
            ////重設

            $(targetFormID).find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });
            @if(role == teacher) {
                <text>
                    $("#RoleName").val("Teacher")
                </text>
            }

            $(targetFormID).submit();
        }
    </script>

}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("ModifyQuery", "AWA004", FormMethod.Post, new { name = "AWA004", id = "AWA004" }))
{
    @Html.HiddenFor(m => m.OrderColumn)
    @Html.HiddenFor(m => m.SortBy)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.whereAWARD_SCHOOL_NO)
    @Html.HiddenFor(m => m.whereAWARD_TYPE)
    @Html.HiddenFor(m => m.whereSNAME)
    @Html.HiddenFor(m => m.whereAWARD_NAME)
    @Html.HiddenFor(m => m.whereSTATUS)
    @Html.Hidden("hHtml")
    @Html.Hidden("hidSelectTRANS_NO")
    @Html.HiddenFor(m => m.RoleName)
    <br />
    <br />
    <div class="form-inline">
        <div class="form-inline" role="form">

            @if (Request["Awat"] == "Awat_Key")
            {
                <div class="form-group">
                    <label class="control-label">獎品ID:@Model.whereAWARD_NO</label>
                    @Html.HiddenFor(m => m.whereAWARD_NO)
                </div>
                <br />
            }

            @if (ViewBag.Show)
            {
                <div class="form-inline">
                    <div class="form-group">
                        <label class="control-label">學校</label>
                    </div>
                    <div class="form-group">
                        @Html.DropDownList("whereSchoolNo", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
                    </div>
                </div>
                <br />
            }

            <div class="form-group">
                @if (role == student)
                {
                    <label class="control-label">學號/姓名/品名</label>
                }
                else if (role == teacher)
                {
                    <label class="control-label">姓名/品名</label>
                }
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>

            @if (role == student)
            {
                <div class="form-group">
                    <label class="control-label">年級</label>
                </div>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                </div>
                <div class="form-group">
                    <label class="control-label">班級</label>
                </div>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                </div>
            }

            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
    </div>
    <br />
    @buttonFun(SchoolNO)
    <img src="~/Content/img/web-bar2-revise-17.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="row">
        <div class="col-lg-12 text-right">
            <input id="cbPicture" type="checkbox" onclick="$('.imgCol').toggle()" /> <samp><span for="cbPicture">顯示縮圖</span></samp>
        </div>
    </div>

    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-AWA004" id="OutList">
                <thead>
                    <tr>
                        @if(true)// (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString())
                        {
                            <th>
                                全選 <input type="checkbox" id="chkALL" name="chkALL" />
                            </th>
                        }
                        @if (ViewBag.Show)
                        {
                            <th>
                                <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SHORT_NAME';document.forms[0].submit()">
                                    學校
                                </a>
                            </th>
                        }
                        else
                        {
                            <th>
                                <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SYEAR';document.forms[0].submit()">
                                    學年
                                </a>
                            </th>
                            <th>
                                學期
                            </th>
                        }
                        <th>
                            <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='CLASS_NO';document.forms[0].submit()">
                                班級
                            </a>
                        </th>
                        <th>
                            <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SEAT_NO';document.forms[0].submit()">
                                座號
                            </a>
                        </th>
                        <th>
                            姓名
                        </th>
                        <th>
                            品名
                        </th>
                        <th>
                            <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='COST_CASH';document.forms[0].submit()">
                                酷幣數
                            </a>
                        </th>
                        <th>
                            狀態
                        </th>
                        <th>
                            <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='TRANS_DATE';document.forms[0].submit()">
                                兌獎日期
                            </a>
                        </th>
                        @if (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive.ToString())
                        {
                            <th>
                                <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='GOT_DATE';document.forms[0].submit()">
                                    領獎日期
                                </a>
                            </th>
                        }
                        <th class="imgCol">
                            縮圖
                        </th>
                        <th>
                            備註
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (role == student)
                    {
                        foreach (var item in Model.VAWA004List)
                        {
                            string imgSrc = ViewBag.ImgUrl + user.SCHOOL_NO + @"/" + item.IMG_FILE;
                            @Html.HiddenFor(modelItem => item.CLASS_NO)
                    <tr>
                        @if (true)//(Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString())
                        {
                            EcoolWeb.ViewModels.AWA004PrintViewModel Chk_Item = new EcoolWeb.ViewModels.AWA004PrintViewModel();
                            Chk_Item.V004 = item;
                            if (Model.Chk != null)
                            {
                                Chk_Item.CheckBoxNo = Model.Chk.Where(a => a.V004.TRANS_NO == item.TRANS_NO).Select(a => a.CheckBoxNo).FirstOrDefault();
                            }
                            <td>
                                @Html.Partial("_ModifyView", Chk_Item)
                            </td>
                        }
                        
                            @if (ViewBag.Show)
                            {
                                <td style="white-space:nowrap;font-size:13px">
                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                </td>
                            }
                            else
                            {
                                <td style="text-align: center;font-size:13px">
                                    @Html.DisplayFor(modelItem => item.SYEAR)
                                </td>
                                <td style="text-align: center;font-size:13px">
                                    @Html.DisplayFor(modelItem => item.SEMESTER)
                                </td>
                            }
                            <td style="text-align: center;font-size:13px;text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td style="text-align: center;font-size:13px">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            @if (item.SNAME.Trim() == "")
                            {
                                <td style="text-align: center;font-size:12px">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                            }
                            else
                            {
                                <td style="text-align: center;font-size:13px;white-space:nowrap;cursor:pointer;" onclick="doSearch('whereSNAME','@item.NAME');">
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>
                            }
                            <td style="font-size:13px;text-align: left;white-space:normal;cursor:pointer;" onclick="doSearch('whereAWARD_NAME','@item.AWARD_NAME');">
                                @Html.DisplayFor(modelItem => item.AWARD_NAME)
                            </td>
                            <td style="font-size:13px">
                                @Html.DisplayFor(modelItem => item.COST_CASH)
                            </td>
                            <td style="font-size:13px">
                                @Html.DisplayFor(modelItem => item.CTRANS_STATUS)
                            </td>
                            <td style="font-size:13px">
                              @(((DateTime)item.TRANS_DATE).ToString("yyyy/MM/dd HH:mm"))
                            </td>
                            @if (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive.ToString())
                            {
                                <td style="font-size:13px">
                                    @Html.DisplayFor(modelItem => item.GOT_DATE, "ShortDateTime")
                                </td>
                            }
                            <td class="imgCol">
                                <div>
                                    <img src="@imgSrc" href="@imgSrc" class="imgSmall mybox" />
                                </div>
                            </td>
                            <td style="font-size:13px">
                                @Html.DisplayFor(modelItem => item.MEMO)
                                @if ((item.CTRANS_STATUS == "已訂未領" || item.CTRANS_STATUS == "已領取") && BoolbtnModifyQuery)
                                {
                                    <input type="button" value="取消領取" onclick="btnTRANS_NO_onclick('@item.TRANS_NO');" class="btn btn-xs btn-Basic">
                                }
                            </td>
                        </tr>
                            i++;
                        }
                    }
                    else if (role == teacher)
                    {
                        foreach (var item in Model.VAWA005List)
                        {
                            string imgSrc = ViewBag.ImgUrl + @"/" + item.IMG_FILE;

                    <tr>
                        @if (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString())
                        {
                            EcoolWeb.ViewModels.AWA004PrintViewModel Chk_Item = new EcoolWeb.ViewModels.AWA004PrintViewModel();
                            Chk_Item.V005 = item;
                            if (Model.Chk != null)
                            {
                                Chk_Item.CheckBoxNo = Model.Chk.Where(a => a.V005.TRANS_NO == item.TRANS_NO).Select(a => a.CheckBoxNo).FirstOrDefault();
                            }
                            <td>
                                @Html.Partial("_ModifyView", Chk_Item)
                            </td>
                        }
                        @if (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive.ToString() || Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del.ToString())
                        {
                            <td>  </td>

                        }
                        @if (ViewBag.Show)
                        {
                            <td style="white-space:nowrap;font-size:13px">
                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                            </td>
                        }
                        else
                        {
                            <td style="text-align: center;font-size:13px">
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td style="text-align: center;font-size:13px">
                                @Html.DisplayFor(modelItem => item.SEMESTER)
                            </td>
                        }
                        <td style="text-align: center;font-size:13px;text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                        </td>
                        <td style="text-align: center;font-size:13px">
                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                        </td>
                        @if (item.SNAME.Trim() == "")
                        {
                            <td style="text-align: center;font-size:12px">
                                @Html.DisplayFor(modelItem => item.SNAME)
                            </td>
                        }
                        else
                        {
                            <td style="text-align: center;font-size:13px;white-space:nowrap;cursor:pointer;" onclick="doSearch('whereSNAME','@item.NAME');">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                        }
                        <td style="font-size:13px;text-align: left;white-space:normal;cursor:pointer;" onclick="doSearch('whereAWARD_NAME','@item.AWARD_NAME');">
                            @Html.DisplayFor(modelItem => item.AWARD_NAME)
                        </td>
                        <td style="font-size:13px">
                            @Html.DisplayFor(modelItem => item.COST_CASH)
                        </td>
                        <td style="font-size:13px">
                            @Html.DisplayFor(modelItem => item.CTRANS_STATUS)
                        </td>
                        <td style="font-size:13px">
                            @Html.DisplayFor(modelItem => item.TRANS_DATE, "ShortDateTime")
                        </td>
                        @if (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive.ToString())
                        {
                            <td style="font-size:13px">
                                @Html.DisplayFor(modelItem => item.GOT_DATE, "ShortDateTime")
                            </td>
                        }
                        <td class="imgCol">
                            <div>
                                <img src="@imgSrc" href="@imgSrc" class="imgSmall mybox" />
                            </div>
                        </td>
                        <td style="font-size:13px">
                            @Html.DisplayFor(modelItem => item.MEMO)
                            @if ((item.CTRANS_STATUS == "已訂未領" || item.CTRANS_STATUS == "已領取") && BoolbtnModifyQuery)
                            {
                                <br />
                                <input type="button" id='[@i].btnTRANS_NO' name='[@i].btnTRANS_NO' value="取消領取" onclick="btnTRANS_NO_onclick('@item.TRANS_NO');" class="btn-primary btn btn-sm">
                                <input type="hidden" id='[@i].TRANS_NO' name='[@i].TRANS_NO' value=@item.TRANS_NO />
                            }
                        </td>
                    </tr>
                            i++;
                        }
                    }
                </tbody>
            </table>
            @if (role == student)
            {
                if (Model.VAWA004List.Count() == 0)
                {
                    <div style="margin-top:15px;margin-bottom:5px">
                        <div class="text-danger">查無任何資料需要處理</div>
                    </div>
                }
            }
            else if (role == teacher)
            {
                if (Model.VAWA005List.Count() == 0)
                {
                    <div style="margin-top:15px;margin-bottom:5px">
                        <div class="text-danger">查無任何資料需要處理</div>
                    </div>
                }
            }
        </div>
    </div>
    <div>
        @if (role == student)
        {
            @Html.Pager(Model.VAWA004List.PageSize, Model.VAWA004List.PageNumber, Model.VAWA004List.TotalItemCount).Options(o => o
            .DisplayTemplate("BootstrapPagination")
            .MaxNrOfPages(5)
            .SetPreviousPageText("上頁")
            .SetNextPageText("下頁"))
        }
        else if (role == teacher)
        {
            @Html.Pager(Model.VAWA005List.PageSize, Model.VAWA005List.PageNumber, Model.VAWA005List.TotalItemCount).Options(o => o
            .DisplayTemplate("BootstrapPagination")
            .MaxNrOfPages(5)
            .SetPreviousPageText("上頁")
            .SetNextPageText("下頁"))
        }
    </div>

    if (Model.VAWA004List?.Count() > 0 || Model.VAWA005List?.Count() > 0)
    {
        <div style="height:30px"></div>
        <div class="form-group Div-btn-center">
            @if (BoolbtnModifyQuery && Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString())
            {
                <div class="col-md-offset-3 col-md-3">
                    <input type="button" id="btnSend2" value="設定為已兌換" class="btn btn-default" onclick="btnSend_onclick();" />
                </div>
            }
            <div class="col-md-6">
                @if (BoolbtnModifyQuery)
                {
                    <input type="button" id="btnExcel" value="線上列印通知" class="btn btn-default" onclick="PrintSetView();" />
                    <input type="button" id="btnExcel" value="匯出Excel" class="btn btn-default" onclick="exportExcel();" />
                }
            </div>
        </div>
        <div id="Ex" style="display:none">
        </div>
    }

}