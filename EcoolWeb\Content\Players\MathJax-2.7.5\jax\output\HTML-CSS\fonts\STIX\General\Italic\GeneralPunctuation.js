/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/GeneralPunctuation.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{8208:[257,-191,333,49,282],8209:[257,-191,333,49,282],8210:[258,-192,500,-8,508],8211:[243,-197,500,-6,505],8212:[243,-197,889,-6,894],8216:[666,-436,333,171,310],8217:[666,-436,333,151,290],8218:[101,129,333,44,183],8219:[666,-436,333,169,290],8220:[666,-436,556,166,514],8221:[666,-436,556,151,499],8222:[101,129,556,57,405],8223:[666,-436,556,169,499],8224:[666,159,500,101,488],8225:[666,143,500,22,491],8226:[444,-59,523,70,455],8230:[100,11,889,57,762],8240:[706,19,1117,80,1067],8241:[706,19,1479,80,1429],8249:[403,-37,333,51,281],8250:[403,-37,333,52,282],8254:[820,-770,500,0,500],8260:[676,10,167,-169,337]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/GeneralPunctuation.js");
