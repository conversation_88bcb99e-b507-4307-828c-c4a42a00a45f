﻿@model BatchApplyStudentResultViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BatchWork" });
    }
}

@using (Html.BeginForm("BatchApplyStudentResult", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            報名結果
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="text-center">
                    <div class="">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                報名成功
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">

                                    <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().SHORT_NAME)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().NAME)
                                                    </samp>
                                                </th>

                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().GAME_USER_TYPE)
                                                    </samp>
                                                </th>

                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().USER_NO)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().CLASS_NO)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().SEAT_NO)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().PHONE)
                                                    </samp>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (Model.OK_LIST?.Count() > 0)
                                            {
                                                foreach (var item in Model.OK_LIST)
                                                {
                                            <tr>
                                                <td align="center">
                                                    @if (item.GAME_USER_TYPE == UserType.Student)
                                                    {
                                                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                                    }
                                                    else
                                                    {
                                                        <text>卡片</text>
                                                    }
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.NAME)
                                                </td>
                                                <td align="center">

                                                    @if (string.IsNullOrWhiteSpace(item.GAME_USER_TYPE_DESC))
                                                    {
                                                        @UserType.GetDesc(item.GAME_USER_TYPE)
                                                    }
                                                    else
                                                    {
                                                        @Html.DisplayFor(modelItem => item.GAME_USER_TYPE_DESC)
                                                    }
                                                </td>
                                                <td align="center">
                                                    @if (item.GAME_USER_TYPE == UserType.Student)
                                                    {
                                                        @Html.DisplayFor(modelItem => item.USER_NO)
                                                    }
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.PHONE)
                                                </td>
                                            </tr>
                                                }
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                報名失敗
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">

                                    <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.OK_LIST.First().SHORT_NAME)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.NG_LIST.First().NAME)
                                                    </samp>
                                                </th>

                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.NG_LIST.First().GAME_USER_TYPE)
                                                    </samp>
                                                </th>

                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.NG_LIST.First().USER_NO)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="form-group">
                                                        @Html.DisplayNameFor(model => model.NG_LIST.First().CLASS_NO)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.NG_LIST.First().SEAT_NO)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.NG_LIST.First().PHONE)
                                                    </samp>
                                                </th>
                                                <th>
                                                    <samp class="">
                                                        @Html.DisplayNameFor(model => model.NG_LIST.First().ERROR)
                                                    </samp>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (Model.NG_LIST?.Count() > 0)
                                            {
                                                foreach (var item in Model.NG_LIST)
                                                {
                                                    <tr>
                                                        <td align="center">
                                                            @if (item.GAME_USER_TYPE == UserType.Student)
                                                            {
                                                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                                            }
                                                            else
                                                            {
                                                                <text>卡片</text>
                                                            }
                                                        </td>
                                                        <td align="center">
                                                            @Html.DisplayFor(modelItem => item.NAME)
                                                        </td>

                                                        <td align="center">
                                                            @if (string.IsNullOrWhiteSpace(item.GAME_USER_TYPE_DESC))
                                                            {
                                                                @UserType.GetDesc(item.GAME_USER_TYPE)
                                                            }
                                                            else
                                                            {
                                                                @Html.DisplayFor(modelItem => item.GAME_USER_TYPE_DESC)
                                                            }
                                                        </td>
                                                        <td align="center">
                                                            @if (item.GAME_USER_TYPE == UserType.Student)
                                                            {
                                                                @Html.DisplayFor(modelItem => item.USER_NO)
                                                            }
                                                        </td>
                                                        <td align="center">
                                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                                        </td>
                                                        <td align="center">
                                                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                                                        </td>
                                                        <td align="center">
                                                            @Html.DisplayFor(modelItem => item.PHONE)
                                                        </td>
                                                        <td align="center">
                                                            @Html.DisplayFor(modelItem => item.ERROR)
                                                        </td>
                                                    </tr>
                                                }
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';
    </script>
}