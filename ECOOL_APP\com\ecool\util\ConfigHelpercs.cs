﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP
{
    public class ConfigHelpercs
    {
        public static string GetappSettings(string Key)
        {
            string Value = string.Empty;

            try
            {
                Value = System.Web.Configuration.WebConfigurationManager.AppSettings[Key].ToString();
            }
            catch (Exception)
            {
            }

            return Value;
        }

        public static string GetappSettings(string Key, string DefaultVal)
        {
            string Value = string.Empty;

            try
            {
                Value = System.Web.Configuration.WebConfigurationManager.AppSettings[Key].ToString();
            }
            catch (Exception)
            {
                Value = DefaultVal;
            }

            return Value;
        }
    }
}