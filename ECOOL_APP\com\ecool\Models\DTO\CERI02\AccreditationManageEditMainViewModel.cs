﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class AccreditationManageEditMainViewModel
    {
        /// <summary>
        ///護照名稱ID
        /// </summary>
        [DisplayName("隸屬哪一本護照")]
        [Required]
        public string ACCREDITATION_TYPE { get; set; }

        /// <summary>
        ///護照細項ID
        /// </summary>
        [DisplayName("護照細項ID")]
        public string ACCREDITATION_ID { get; set; }

        /// <summary>
        ///護照細項名稱
        /// </summary>
        [DisplayName("護照細項名稱")]
        [Required]
        public string ACCREDITATION_NAME { get; set; }



        public string IsText { get; set; }

    }
}