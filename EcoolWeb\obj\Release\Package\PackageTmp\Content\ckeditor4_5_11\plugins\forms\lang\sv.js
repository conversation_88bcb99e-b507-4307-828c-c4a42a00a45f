﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'sv', {
	button: {
		title: 'Egenskaper för knapp',
		text: 'Text (värde)',
		type: 'Typ',
		typeBtn: 'Knapp',
		typeSbm: 'Skicka',
		typeRst: 'Återställ'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Egenskaper för kryssruta',
		radioTitle: 'Egenskaper för alternativknapp',
		value: 'Värde',
		selected: 'Vald',
		required: 'Krävs'
	},
	form: {
		title: 'Egenskaper för formulär',
		menu: 'Egenskaper för formulär',
		action: 'Funktion',
		method: 'Metod',
		encoding: 'Kodning'
	},
	hidden: {
		title: 'Egenskaper för dolt fält',
		name: '<PERSON><PERSON>',
		value: 'Värde'
	},
	select: {
		title: 'Egenskaper för flervalslista',
		selectInfo: 'Information',
		opAvail: 'Befintliga val',
		value: 'Värde',
		size: 'Storlek',
		lines: 'Linjer',
		chkMulti: 'Tillåt flerval',
		required: 'Krävs',
		opText: 'Text',
		opValue: 'Värde',
		btnAdd: 'Lägg till',
		btnModify: 'Redigera',
		btnUp: 'Upp',
		btnDown: 'Ner',
		btnSetValue: 'Markera som valt värde',
		btnDelete: 'Radera'
	},
	textarea: {
		title: 'Egenskaper för textruta',
		cols: 'Kolumner',
		rows: 'Rader'
	},
	textfield: {
		title: 'Egenskaper för textfält',
		name: 'Namn',
		value: 'Värde',
		charWidth: 'Teckenbredd',
		maxChars: 'Max antal tecken',
		required: 'Krävs',
		type: 'Typ',
		typeText: 'Text',
		typePass: 'Lösenord',
		typeEmail: 'E-post',
		typeSearch: 'Sök',
		typeTel: 'Telefonnummer',
		typeUrl: 'URL'
	}
} );
