// Modern jQuery code for ADDI02 UPLOAD page
$(document).ready(function() {
    // 頁面初始化模組
    const pageInitializer = {
        init: function(bookSelector) {
            this.bookSelector = bookSelector;
            this.initializePage();
        },

        initializePage: function() {
            // 頁面載入時執行初始化
            if (this.bookSelector) {
                this.bookSelector.handleReadingPassportChange();
            }
        }
    };

    // 書籍選擇模組
    const bookSelector = {
        init: function() {
            window.ddlBOOK_NAME_onchange = this.handleBookNameChange.bind(this);
            window.ddlReadrppBook_onchange = this.handleReadingPassportChange.bind(this);
            this.bindEvents();
            this.setInitialState();
        },

        bindEvents: function() {
            // 綁定書籍相關的事件
            $('#BOOK_ID').on('change', this.handleBookNameChange.bind(this));
            $('#ddlReadrppBook').on('change', this.handleReadingPassportChange.bind(this));
        },

        setInitialState: function() {
            // 設置初始狀態
            const currentValue = $('#ddlReadrppBook').val();
            console.log('初始閱讀護照書籍值:', currentValue);

            if (currentValue == 'N') {
                $('#txtBOOK_NAME').show();
                $('#BOOK_ID').hide();
                console.log('顯示文字輸入框，隱藏下拉選單');
            } else {
                $('#txtBOOK_NAME').hide();
                $('#BOOK_ID').show();
                console.log('隱藏文字輸入框，顯示下拉選單');
            }
        },

        handleBookNameChange: function() {
            // 抓取閱讀護照上所選到的書名
            $("#BOOK_NAME").val($("#BOOK_ID option:selected").text());
        },

        handleReadingPassportChange: function() {
            // 設定是否讀取閱讀護照書籍
            try {
                const value = $('#ddlReadrppBook').val();
                console.log('閱讀護照書籍變更為:', value);

                if (value == 'N') {
                    $('#txtBOOK_NAME').show();
                    $('#BOOK_ID').hide();
                    console.log('顯示文字輸入框，隱藏下拉選單');
                } else {
                    $('#txtBOOK_NAME').hide();
                    $('#BOOK_ID').show();
                    console.log('隱藏文字輸入框，顯示下拉選單');
                }
            } catch (err) {
                console.error('處理閱讀護照變更時發生錯誤:', err);
            }
        }
    };

    // 檔案類型選擇模組
    const fileTypeSelector = {
        init: function() {
            window.FileType_onchange = this.handleFileTypeChange.bind(this);
            this.bindEvents();
        },

        bindEvents: function() {
            // 綁定檔案類型選擇事件
            $('input[name="radFileType"]').on('change', this.handleFileTypeChange.bind(this));
        },

        handleFileTypeChange: function() {
            try {
                if ($('input[name="radFileType"][value="S"]').is(':checked') == true) {
                    $('#TRsClassNo').show();
                } else {
                    $('#TRsClassNo').hide();
                }
            } catch (err) {
                console.error('處理檔案類型變更時發生錯誤:', err);
            }
        }
    };

    // 表單驗證模組
    const formValidator = {
        validateForm: function() {
            let strMsg = '';

            // 檢查上傳檔案
            if ($('input[name="radupdatepic"][value="Y"]').is(':checked') == true && $('#file').val() == '') {
                strMsg += '上傳檔案需為必填\r\n';
            }

            // 檢查閱讀書名（閱讀護照書籍）
            if ($('#ddlReadrppBook').val() == 'Y' && $('#BOOK_ID').val() == '') {
                strMsg += '請選擇閱讀書名\r\n';
            }

            // 檢查閱讀書名（非閱讀護照書籍）
            if ($('#ddlReadrppBook').val() == 'N' &&
                ($('#txtBOOK_NAME').val() == '' || $("#txtBOOK_NAME").val().trim().length == 0)) {
                strMsg += '閱讀書名為必填\r\n';
            }

            return strMsg;
        },

        buildConfirmationMessage: function() {
            let strMsg = "請確定以下資訊是否正確\r\n\r\n";

            // 是否為閱讀護照書籍
            strMsg += "是否為閱讀護照書籍：" + $('#ddlReadrppBook').val() + "\r\n";

            // 閱讀書名
            if ($('#ddlReadrppBook').val() == "Y") {
                strMsg += "閱讀書名：" + $('#BOOK_ID option:selected').text();
            } else {
                strMsg += "閱讀書名：" + $('#txtBOOK_NAME').val();
            }
            strMsg += "\r\n";

            // 檔名上傳類型
            strMsg += "檔名上傳類型：";
            if ($('#radUserNo').is(':checked') == true) {
                strMsg += "學號";
            } else {
                strMsg += "座號";
                strMsg += "\r\n";
                strMsg += "班級：" + $('#Class_No').val() + "\r\n";
            }

            return strMsg;
        }
    };

    // 表單提交模組
    const formSubmitter = {
        init: function() {
            window.btnSend_onclick = this.handleSubmit.bind(this);
            window.btnCancel_onclick = this.handleCancel.bind(this);
            this.bindEvents();
        },

        bindEvents: function() {
            // 綁定按鈕事件
            $('#btnSend').on('click', this.handleSubmit.bind(this));
        },

        handleSubmit: function() {
            try {
                // 表單驗證
                const validationMsg = formValidator.validateForm();
                
                if (validationMsg !== '') {
                    alert(validationMsg);
                    return;
                }

                // 禁用提交按鈕
                $("#btnSend").attr("disabled", true);

                // 建立確認訊息
                const confirmationMsg = formValidator.buildConfirmationMessage();

                // 確認提交
                const confirmed = confirm(confirmationMsg);

                if (confirmed) {
                    this.submitForm();
                } else {
                    $("#btnSend").attr("disabled", false);
                }
            } catch (err) {
                console.error('發生錯誤：', err);
                alert('系統發生錯誤，請稍後再試');
                $("#btnSend").attr("disabled", false);
            }
        },

        submitForm: function() {
            const form = document.contentForm;
            form.enctype = "multipart/form-data";
            form.action = window.ADDI02_UPLOAD_URLS.setUpload;
            form.submit();
        },

        handleCancel: function() {
            const form = document.contentForm;
            form.enctype = "multipart/form-data";
            form.action = window.ADDI02_UPLOAD_URLS.checkPending;
            form.submit();
        }
    };

    // 用戶體驗增強模組
    const uxEnhancer = {
        init: function() {
            this.enhanceFormElements();
            this.addTooltips();
            this.addValidationFeedback();
        },

        enhanceFormElements: function() {
            // 為表單元素添加焦點效果
            $('.form-control').on('focus', function() {
                $(this).closest('.form-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.form-group').removeClass('focused');
            });

            // 文件輸入框變更時的反饋
            $('#file').on('change', function() {
                const fileName = $(this).val().split('\\').pop();
                if (fileName) {
                    $(this).next('.file-feedback').remove();
                    $(this).after('<div class="file-feedback text-success">已選擇檔案: ' + fileName + '</div>');
                }
            });
        },

        addTooltips: function() {
            // 為表單元素添加提示
            $('#ddlReadrppBook').attr('title', '選擇是否為閱讀護照書籍');
            $('#txtBOOK_NAME').attr('title', '請輸入閱讀書名');
            $('#BOOK_ID').attr('title', '請選擇閱讀護照書籍');
            $('#file').attr('title', '請選擇要上傳的ZIP檔案');
        },

        addValidationFeedback: function() {
            // 即時驗證反饋
            $('#txtBOOK_NAME').on('blur', function() {
                const value = $(this).val().trim();
                if ($('#ddlReadrppBook').val() == 'N' && value === '') {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            $('#file').on('change', function() {
                const file = this.files[0];
                if (file) {
                    if (file.size > 100 * 1024 * 1024) { // 100MB
                        alert('檔案大小不能超過100MB');
                        $(this).val('');
                    }
                }
            });
        }
    };

    // 錯誤處理模組
    const errorHandler = {
        init: function() {
            this.setupErrorHandling();
        },

        setupErrorHandling: function() {
            $(window).on('error', function(e) {
                console.error('頁面錯誤:', e);
            });
        }
    };

    // 初始化所有模組（注意順序）
    bookSelector.init();
    fileTypeSelector.init();
    formSubmitter.init();
    uxEnhancer.init();
    errorHandler.init();

    // 最後初始化頁面，確保其他模組已經準備好
    pageInitializer.init(bookSelector);

    // 設置全局 URL 配置（需要在 CSHTML 中定義）
    window.ADDI02_UPLOAD_URLS = window.ADDI02_UPLOAD_URLS || {};
});
