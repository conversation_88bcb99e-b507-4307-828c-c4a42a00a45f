﻿@model ECOOL_APP.EF.ADDT15

@{
    ViewBag.Title = "校外榮譽-新增校外榮譽";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")






@using (Html.BeginForm("ADD", "ADDI07", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-11.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI07">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassNoItem, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.USERNAME, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <select id="USER_NO" name="USER_NO" class = "form-control"></select>
                    @Html.Hidden("USERNAME")
                    @Html.Hidden("SNAME")
                    @Html.ValidationMessageFor(model => model.USERNAME, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.OAWARD_ITEM, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.OAWARD_ITEM, new { htmlAttributes = new { @class = "form-control", @MaxLength = "30" } })
                    @Html.ValidationMessageFor(model => model.OAWARD_ITEM, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.OAWARD_SCORE, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.HiddenFor(model => model.OAWARD_SCORE)
                    @Html.DropDownList("ddlOAWARD_SCORE", (IEnumerable<SelectListItem>)ViewBag.Scoreitems, new { @class = "form-control" })
                    @Html.Label(" ", new { @id = "lbOAWARD_SCORE", @style = "color:blue;" })
                    @Html.ValidationMessageFor(model => model.OAWARD_SCORE, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })

                <div class="col-md-10  col-sm-9" style="padding-top:7px">
                    @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.REMARK, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.REMARK, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.REMARK, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <input class="btn btn-default" type="file" name="files" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>


            <div class="form-group  text-center">
                <div class="col-md-12">

                    <button id="Add" name="Add" value="新增" class="btn btn-default">
                        確定送出
                    </button>

                    <a href='@Url.Action("QUERY", "ADDI07")' class="btn btn-default">
                        放棄編輯
                    </a>
                   
                </div>
            </div>
        </div>
    </div>


    
}


@section Scripts
{
    <script type="text/javascript">
        $(function () {
            ChangeClass_No();
            
            // 選擇班級
            $("#Class_No").on('change', function () { 
                ChangeClass_No(); 
            });
            
            // 選擇成績
            $("#ddlOAWARD_SCORE").on('change', function () { 
                ddlOAWARD_SCORE_onchange(); 
            });
            
            // 設定使用者名稱
            var Namelen = $("#USER_NO option:selected").text().length - 2;
            $("#USER_NO").on('change', function () {
                $("#USERNAME").val($("#USER_NO option:selected").text());
                $("#SNAME").val($("#USER_NO option:selected").text().substr(Namelen, 2));
            });
            
            $("#Add").on('click', function () { 
                return Valid(); 
            });
        });

        function ddlOAWARD_SCORE_onchange() {
            $("#OAWARD_SCORE").val($("#ddlOAWARD_SCORE option:selected").text());
            $("#CASH").val($("#ddlOAWARD_SCORE option:selected").val());
            var ScoreLabel;
            
            if ($("#ddlOAWARD_SCORE option:selected").val() !== '') {
                ScoreLabel = $("#ddlOAWARD_SCORE option:selected").val();
                if ($("#ddlOAWARD_SCORE option:selected").text() === '特殊獎勵') {
                    ScoreLabel = '０-300';
                }
                $("#lbOAWARD_SCORE").html('獎勵數點' + ScoreLabel + '點');
            } else {
                $("#lbOAWARD_SCORE").html('');
            }
        }

        function Valid() {
            var msg = '';
            var blStatus = false;
            
            if ($("#USER_NO").val() === '') {
                msg += '請選擇學生\r\n';
            }

            if ($("#OAWARD_ITEM").val() === '') {
                msg += '優良表現為必填\r\n';
            }

            if ($("#OAWARD_SCORE").val() === '') {
                msg += '請選擇成績\r\n';
            }

            if ($("#CASH").val().length <= 0) {
                msg += '獎勵點數不能為空\r\n';
            }

            if (!isNumber($("#CASH").val())) {
                msg += '獎勵點數不能為數字\r\n';
            }

            if ($("#CASH").val() > 999) {
                msg += '獎勵點數不能超過1000點\r\n';
            }

            if ($("#ddlOAWARD_SCORE option:selected").text() === '特殊獎勵' && ($("#CASH").val() > 300)) {
                msg += '獎勵點數需介於0-300之間\r\n';
            }

            if (msg !== '') {
                alert(msg);
                blStatus = false;
            } else {
                blStatus = true;
            }

            return blStatus;
        }

        function SetClass_NoDDLEmpty() {
            $('#USER_NO').empty();
            $('#USER_NO').append($('<option></option>').val('').text('請選擇學生'));
        }

        function ChangeClass_No() {
            var selectedClass_No = $.trim($('#Class_No option:selected').val());
            if (selectedClass_No.length === 0) {
                SetClass_NoDDLEmpty();
            } else {
                $.getJSON('@Url.Action("GetNameData").ToString()', { Class_No: selectedClass_No })
                    .done(function (data) {
                        $('#USER_NO').empty();
                        $.each(data, function (i, item) {
                            $('#USER_NO').append($('<option></option>').val(item.Value).text(item.Text));
                        });
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        console.error('Error fetching name data:', textStatus, errorThrown);
                    });
            }
        }

        function isNumber(name) {
            if (name.length === 0) {
                return false;
            }
            for (var i = 0; i < name.length; i++) {
                if (name.charAt(i) < "0" || name.charAt(i) > "9") {
                    return false;
                }
            }
            return true;
        }
    </script>
}