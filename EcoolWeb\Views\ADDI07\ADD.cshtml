﻿@model ECOOL_APP.EF.ADDT15

@{
    ViewBag.Title = "校外榮譽-新增校外榮譽";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")






@using (Html.BeginForm("ADD", "ADDI07", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-11.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI07">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassNoItem, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.USERNAME, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <select id="USER_NO" name="USER_NO" class="form-control"></select>
                    @Html.Hidden("USERNAME")
                    @Html.Hidden("SNAME")
                    @Html.ValidationMessageFor(model => model.USERNAME, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.OAWARD_ITEM, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.OAWARD_ITEM, new { htmlAttributes = new { @class = "form-control", @MaxLength = "30" } })
                    @Html.ValidationMessageFor(model => model.OAWARD_ITEM, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.OAWARD_SCORE, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.HiddenFor(model => model.OAWARD_SCORE)
                    @Html.DropDownList("ddlOAWARD_SCORE", (IEnumerable<SelectListItem>)ViewBag.Scoreitems, new { @class = "form-control" })
                    @Html.Label(" ", new { @id = "lbOAWARD_SCORE", @style = "color:blue;" })
                    @Html.ValidationMessageFor(model => model.OAWARD_SCORE, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })

                <div class="col-md-10  col-sm-9" style="padding-top:7px">
                    @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.REMARK, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.REMARK, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.REMARK, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <input class="btn btn-default" type="file" name="files" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>


            <div class="form-group  text-center">
                <div class="col-md-12">

                    <button id="Add" name="Add" value="新增" class="btn btn-default">
                        確定送出
                    </button>

                    <a href='@Url.Action("QUERY", "ADDI07")' class="btn btn-default">
                        放棄編輯
                    </a>

                </div>
            </div>
        </div>
    </div>



}


@section Scripts
{
    <script type="text/javascript" nonce="cmlvaw">
     // 設置全局 URL 配置
        window.ADDI07_ADD_URLS = {
            getNameData: '@Url.Action("GetNameData")',
            add: '@Url.Action("ADD")'
        };
        
    </script>
    <script src="~/Scripts/ADDI07/common.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/ADDI07/ADD.js" nonce="cmlvaw"></script>
}