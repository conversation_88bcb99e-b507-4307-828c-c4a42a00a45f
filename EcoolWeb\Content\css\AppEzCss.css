﻿/*手機版css*/

/*手機app 隱藏*/
.App_hide {
    display: none;
}

/*手機app 顯示*/
.App_show {
    display: block;
}

.btn-sm,
.btn-xs {
    padding: 5px 10px;
    font-size: 16px;
    line-height: 1.5;
    border-radius: 3px;
}

.AppbtnSubmit {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 20px;
    font-weight: normal;
    line-height: 1.428571429;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}

@media screen and (max-width: 1199px) {
    body {
        font-size: 16px;
        line-height: 1.428571429;
        color: #333333;
        background-color: #ffffff;
    }
}

@media screen and (max-width:768px) {
    body {
        font-size: 15px;
        line-height: 1.428571429;
        color: #333333;
        background-color: #ffffff;
    }
}

body {
    -webkit-print-color-adjust: exact;
}

.vertical-middle-sm {
    display: table;
}

.div-hidden {
    display: none;
}

.vertical-middle-sm .div {
    display: table-cell;
    height: 100%;
    min-height: 100%;
    float: none !important;
}

.center {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    float: none;
}

.middle {
    display: table-cell;
    vertical-align: middle;
    float: none;
}

.center_bottom {
    display: table-cell;
    text-align: center;
    vertical-align: bottom;
    float: none;
}

.center_top {
    display: table-cell;
    text-align: center;
    vertical-align: top;
    float: none;
}

.table-eZ {
    margin-bottom: 15px;
    overflow-x: scroll;
    overflow-y: hidden;
    margin: 0px auto;
    /*border: 1px solid #dddddd;*/
}

    .table-eZ > .table {
        margin-bottom: 0;
        background-color: #fff;
    }

        .table-eZ > .table > thead > tr > th,
        .table-eZ > .table > tbody > tr > th,
        .table-eZ > .table > tfoot > tr > th,
        .table-eZ > .table > thead > tr > td,
        .table-eZ > .table > tbody > tr > td,
        .table-eZ > .table > tfoot > tr > td {
            white-space: nowrap;
        }

    .table-eZ > .table-bordered {
        border: 0;
    }

        .table-eZ > .table-bordered > thead > tr > th:first-child,
        .table-eZ > .table-bordered > tbody > tr > th:first-child,
        .table-eZ > .table-bordered > tfoot > tr > th:first-child,
        .table-eZ > .table-bordered > thead > tr > td:first-child,
        .table-eZ > .table-bordered > tbody > tr > td:first-child,
        .table-eZ > .table-bordered > tfoot > tr > td:first-child {
            border-left: 0;
        }

        .table-eZ > .table-bordered > thead > tr > th:last-child,
        .table-eZ > .table-bordered > tbody > tr > th:last-child,
        .table-eZ > .table-bordered > tfoot > tr > th:last-child,
        .table-eZ > .table-bordered > thead > tr > td:last-child,
        .table-eZ > .table-bordered > tbody > tr > td:last-child,
        .table-eZ > .table-bordered > tfoot > tr > td:last-child {
            border-right: 0;
        }

        .table-eZ > .table-bordered > thead > tr:last-child > th,
        .table-eZ > .table-bordered > tbody > tr:last-child > th,
        .table-eZ > .table-bordered > tfoot > tr:last-child > th,
        .table-eZ > .table-bordered > thead > tr:last-child > td,
        .table-eZ > .table-bordered > tbody > tr:last-child > td,
        .table-eZ > .table-bordered > tfoot > tr:last-child > td {
            border-bottom: 0;
        }

/*.table-eZwhitespacinitial*/

.table-eZwhitespacinitial {
    /*width: 100%;*/
    margin-bottom: 15px;
    overflow-x: scroll;
    overflow-y: hidden;
    margin: 0px auto;
    /*border: 1px solid #dddddd;*/
}

    .table-eZwhitespacinitial > .table {
        margin-bottom: 0;
        background-color: #fff;
    }

        .table-eZwhitespacinitial > .table > thead > tr > th,
        .table-eZwhitespacinitial > .table > tbody > tr > th,
        .table-eZwhitespacinitial > .table > tfoot > tr > th,
        .table-eZwhitespacinitial > .table > thead > tr > td,
        .table-eZwhitespacinitial > .table > tbody > tr > td,
        .table-eZwhitespacinitial > .table > tfoot > tr > td {
            white-space: pre-line;
        }

    .table-eZwhitespacinitial > .table-bordered {
        border: 0;
    }

        .table-eZwhitespacinitial > .table-bordered > thead > tr > th:first-child,
        .table-eZwhitespacinitial > .table-bordered > tbody > tr > th:first-child,
        .table-eZwhitespacinitial > .table-bordered > tfoot > tr > th:first-child,
        .table-eZwhitespacinitial > .table-bordered > thead > tr > td:first-child,
        .table-eZwhitespacinitial > .table-bordered > tbody > tr > td:first-child,
        .table-eZwhitespacinitial > .table-bordered > tfoot > tr > td:first-child {
            border-left: 0;
        }

        .table-eZwhitespacinitial > .table-bordered > thead > tr > th:last-child,
        .table-eZwhitespacinitial > .table-bordered > tbody > tr > th:last-child,
        .table-eZwhitespacinitial > .table-bordered > tfoot > tr > th:last-child,
        .table-eZwhitespacinitial > .table-bordered > thead > tr > td:last-child,
        .table-eZwhitespacinitial > .table-bordered > tbody > tr > td:last-child,
        .table-eZwhitespacinitial > .table-bordered > tfoot > tr > td:last-child {
            border-right: 0;
        }

        .table-eZwhitespacinitial > .table-bordered > thead > tr:last-child > th,
        .table-eZwhitespacinitial > .table-bordered > tbody > tr:last-child > th,
        .table-eZwhitespacinitial > .table-bordered > tfoot > tr:last-child > th,
        .table-eZwhitespacinitial > .table-bordered > thead > tr:last-child > td,
        .table-eZwhitespacinitial > .table-bordered > tbody > tr:last-child > td,
        .table-eZwhitespacinitial > .table-bordered > tfoot > tr:last-child > td {
            border-bottom: 0;
        }

/*.table-eZ_Not_overflow*/

.table-eZ_Not_overflow {
    width: 100%;
    margin-bottom: 15px;
    margin: 0px auto;
}

    .table-eZ_Not_overflow > .table {
        margin-bottom: 0;
        background-color: #fff;
    }

    .table-eZ_Not_overflow > .table {
        margin-bottom: 0;
        background-color: #fff;
    }

        .table-eZ_Not_overflow > .table > thead > tr > th,
        .table-eZ_Not_overflow > .table > tbody > tr > th,
        .table-eZ_Not_overflow > .table > tfoot > tr > th,
        .table-eZ_Not_overflow > .table > thead > tr > td,
        .table-eZ_Not_overflow > .table > tbody > tr > td,
        .table-eZ_Not_overflow > .table > tfoot > tr > td {
            /*white-space: nowrap;*/
        }

    .table-eZ_Not_overflow > .table-bordered {
        border: 0;
    }

        .table-eZ_Not_overflow > .table-bordered > thead > tr > th:first-child,
        .table-eZ_Not_overflow > .table-bordered > tbody > tr > th:first-child,
        .table-eZ_Not_overflow > .table-bordered > tfoot > tr > th:first-child,
        .table-eZ_Not_overflow > .table-bordered > thead > tr > td:first-child,
        .table-eZ_Not_overflow > .table-bordered > tbody > tr > td:first-child,
        .table-eZ_Not_overflow > .table-bordered > tfoot > tr > td:first-child {
            border-left: 0;
        }

        .table-eZ_Not_overflow > .table-bordered > thead > tr > th:last-child,
        .table-eZ_Not_overflow > .table-bordered > tbody > tr > th:last-child,
        .table-eZ_Not_overflow > .table-bordered > tfoot > tr > th:last-child,
        .table-eZ_Not_overflow > .table-bordered > thead > tr > td:last-child,
        .table-eZ_Not_overflow > .table-bordered > tbody > tr > td:last-child,
        .table-eZ_Not_overflow > .table-bordered > tfoot > tr > td:last-child {
            border-right: 0;
        }

        .table-eZ_Not_overflow > .table-bordered > thead > tr:last-child > th,
        .table-eZ_Not_overflow > .table-bordered > tbody > tr:last-child > th,
        .table-eZ_Not_overflow > .table-bordered > tfoot > tr:last-child > th,
        .table-eZ_Not_overflow > .table-bordered > thead > tr:last-child > td,
        .table-eZ_Not_overflow > .table-bordered > tbody > tr:last-child > td,
        .table-eZ_Not_overflow > .table-bordered > tfoot > tr:last-child > td {
            border-bottom: 0;
        }

/*.panel*/

.panel-ez {
    margin-bottom: 20px;
    background-color: #ffffff;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.panel-ez-body {
    padding: 15px;
}

    .panel-ez-body:before,
    .panel-ez-body:after {
        display: table;
        content: " ";
    }

    .panel-ez-body:after {
        clear: both;
    }

    .panel-ez-body:before,
    .panel-ez-body:after {
        display: table;
        content: " ";
    }

    .panel-ez-body:after {
        clear: both;
    }

.panel-ez > .list-group {
    margin-bottom: 0;
}

    .panel-ez > .list-group .list-group-item {
        border-width: 1px 0;
    }

        .panel-ez > .list-group .list-group-item:first-child {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }

        .panel-ez > .list-group .list-group-item:last-child {
            border-bottom: 0;
        }

.panel-ez > .table {
    margin-bottom: 0;
}

.panel-ez > .panel-body + .table {
    border-top: 1px solid #dddddd;
}

/*.panel-danger-ez*/

.panel-danger-ez {
    border-color: #f2dede;
}

    .panel-danger-ez > .panel-heading {
        color: #b94a48;
        background-color: #f2dede;
        border-color: #f2dede;
    }

        .panel-danger-ez > .panel-heading + .panel-collapse .panel-body {
            border-top-color: #eed3d7;
        }

    .panel-danger-ez > .panel-footer + .panel-collapse .panel-body {
        border-bottom-color: #eed3d7;
    }

/*.table-ecool_list */

.table-ecool_list > thead > tr > td,
.table-ecool_list > thead > tr > th {
    color: #004DA0;
    font-weight: bold;
    border-top-style: none;
    background-color: #FFFFEC;
    white-space: nowrap;
    text-align: center;
}

.table-ecool_list > tbody > tr:nth-child(even) > td,
.table-ecool_list > tbody > tr:nth-child(even) > th {
    background-color: #FFFFEC;
    white-space: normal;
}

.table-ecool_list > tbody > tr:nth-child(odd) > td,
.table-ecool_list > tbody > tr:nth-child(odd) > th {
    background-color: #EBEBC8;
    white-space: normal;
}

.table-ecool_list-hover > tbody > tr:hover > td,
.table-ecool_list-hover > tbody > tr:hover > th {
    background-color: #f5f5f5;
}

/*.table-ecool_list_Notnowrap */

.table-ecool_list_Notnowrap > thead > tr > td,
.table-ecool_list_Notnowrap > thead > tr > th {
    font-weight: bold;
    border-top-style: none;
    background-color: #FFFFEC;
}

/*table-ecool-info */

.table-ecool-info > thead > tr > td,
.table-ecool-info > thead > tr > th {
    color: #005AB5;
    background-color: #d9edf7;
    border-color: #bce8f1;
    text-align: center;
    font-weight: bold;
    white-space: nowrap;
    text-align: center;
}

.table-ecool-info > tbody > tr:nth-child(even) > td,
.table-ecool-info > tbody > tr:nth-child(even) > th {
    color: #3a87ad;
    background-color: #F0FFFF;
    border-color: #bce8f1;
    white-space: normal;
}

.table-ecool-info > tbody > tr:nth-child(odd) > td,
.table-ecool-info > tbody > tr:nth-child(odd) > th {
    color: #005AB5;
    background-color: #FDFFFF;
    border-color: #bce8f1;
    white-space: normal;
}

.table-ecool-info > tfoot > tr > td,
.table-ecool-info > tfoot > tr > th {
    color: red;
    background-color: #FDFFFF;
    border-color: #bce8f1;
    white-space: normal;
}

.table-ecool-info-hover > tbody > tr:hover > td,
.table-ecool-info-hover > tbody > tr:hover > th {
    background-color: #F5F5DC;
}

/*table-ecool-qa */

.table-ecool-qa-Title {
    color: orangered;
}

.table-ecool-qa > thead > tr > td,
.table-ecool-qa > thead > tr > th {
    background-color: #EEEEEF;
    font-weight: bold;
    white-space: nowrap;
}

.table-ecool-qa > tbody > tr > td,
.table-ecool-qa > tbody > tr > th {
    white-space: pre-line;
}

/*table-ecool-infoA */

.table-ecool-infoA > thead > tr:nth-child(odd) > td,
.table-ecool-infoA > thead > tr:nth-child(odd) > th {
    color: #F0FFFF;
    background-color: #4682b4;
    border-color: #bce8f1;
    text-align: center;
    font-weight: bold;
}

.table-ecool-infoA > thead > tr:nth-child(even) > td,
.table-ecool-infoA > thead > tr:nth-child(even) > th {
    color: #3a87ad;
    background-color: #d9edf7;
    border-color: #bce8f1;
    text-align: center;
    font-weight: bold;
}

.table-ecool-infoA > tbody > tr > td,
.table-ecool-infoA > tbody > tr > th {
    color: #3a87ad;
    background-color: #F0FFFF;
    border-color: #bce8f1;
}

/*table-ecool-danger */

.table-ecool-danger > thead > tr:nth-child(odd) > td,
.table-ecool-danger > thead > tr:nth-child(odd) > th {
    color: #CE0000;
    background-color: #FFE4E1;
    border-color: #FFD2D2;
    text-align: center;
    font-weight: bold;
}

.table-ecool-danger > thead > tr:nth-child(even) > td,
.table-ecool-danger > thead > tr:nth-child(even) > th {
    color: #CE0000;
    background-color: #FFF0F5;
    border-color: #fff;
    text-align: center;
    font-weight: bold;
}

.table-ecool-danger > tbody > tr > td,
.table-ecool-danger > tbody > tr > th {
    color: #CE0000;
    background-color: #fff;
    border-color: #FFD2D2;
}

/*table-ecool-warning */

.table-ecool-warning > thead > tr:nth-child(odd) > td,
.table-ecool-warning > thead > tr:nth-child(odd) > th {
    color: #B8860B;
    background-color: #FAFAD2;
    border-color: #EEE8AA;
    text-align: center;
    font-weight: bold;
}

.table-ecool-warning > thead > tr:nth-child(even) > td,
.table-ecool-warning > thead > tr:nth-child(even) > th {
    color: #B8860B;
    background-color: #FFFFE0;
    border-color: #EEE8AA;
    text-align: center;
    font-weight: bold;
}

.table-ecool-warning > tbody > tr > td,
.table-ecool-warning > tbody > tr > th {
    color: #B8860B;
    background-color: #fff;
    border-color: #EEE8AA;
}

/*table-ecool-Bule */

.table-ecool-Bule > thead > tr:nth-child(odd) > td,
.table-ecool-Bule > thead > tr:nth-child(odd) > th {
    color: #F0FFFF;
    border-color: #E0FFFF;
    text-align: center;
    font-weight: bold;
    background: -webkit-linear-gradient( #00BFFF,#87CEFA);
    background: -o-linear-gradient( #00BFFF,#87CEFA);
    background: -moz-linear-gradient( #00BFFF,#87CEFA);
    background: linear-gradient( #00BFFF,#87CEFA);
}

.table-ecool-Bule > thead > tr:nth-child(even) > td,
.table-ecool-Bule > thead > tr:nth-child(even) > th {
    color: #003377;
    border-color: #FFFFFF;
    text-align: center;
    font-weight: bold;
    background: -webkit-linear-gradient( #B0E0E6,#FFFFFF);
    background: -o-linear-gradient( #B0E0E6,#FFFFFF);
    background: -moz-linear-gradient( #B0E0E6,#FFFFFF);
    background: linear-gradient( #B0E0E6,#FFFFFF);
}

.table-ecool-Bule > tbody > tr > td,
.table-ecool-Bule > tbody > tr > th {
    color: #003377;
    background-color: #FFFFFF;
    border-color: #E0FFFF;
}

/*dropdown-menuEz */

.dropdown-menuEz {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    background-clip: padding-box;
}

    .dropdown-menuEz.pull-right {
        right: 0;
        left: auto;
    }

    .dropdown-menuEz .divider {
        height: 1px;
        margin: 9px 0;
        overflow: hidden;
        background-color: #e5e5e5;
    }

    .dropdown-menuEz > li > a {
        display: block;
        clear: both;
        font-weight: normal;
        line-height: 1.428571429;
        color: #333333;
        white-space: nowrap;
        letter-spacing: 2px;
    }

        .dropdown-menuEz > li > a:hover,
        .dropdown-menuEz > li > a:focus {
            color: #000000;
            text-decoration: none;
            background-color: #fcff5a;
        }

    .dropdown-menuEz > .active > a,
    .dropdown-menuEz > .active > a:hover,
    .dropdown-menuEz > .active > a:focus {
        color: #000000;
        text-decoration: none;
        background-color: #FFFFBF;
        outline: 0;
    }

    .dropdown-menuEz > .disabled > a,
    .dropdown-menuEz > .disabled > a:hover,
    .dropdown-menuEz > .disabled > a:focus {
        color: #999999;
    }

        .dropdown-menuEz > .disabled > a:hover,
        .dropdown-menuEz > .disabled > a:focus {
            text-decoration: none;
            cursor: not-allowed;
            background-color: transparent;
            background-image: none;
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
        }

.open > .dropdown-menuEz {
    display: block;
}

.pull-right > .dropdown-menuEz {
    right: 0;
    left: auto;
}

.dropdown-menuEz,
.dropdown-menuEz {
    top: auto;
    bottom: 100%;
    margin-bottom: 1px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    /*.dropdown-menuEz {
    right: 0\9;
    left: auto\9;
  }*/
}

@media (min-width: 767px) {
    .dropdown-menuEz {
        right: 0;
        left: auto;
    }
}

.dropdown-menuEz {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

@media (max-width: 767px) {
    .navbar-nav .open .dropdown-menuEz {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }

        .navbar-nav .open .dropdown-menuEz > li > a,
        .navbar-nav .open .dropdown-menuEz .dropdown-header {
            padding: 5px 0px 5px 55px;
        }

        .navbar-nav .open .dropdown-menuEz > li > a {
            line-height: 20px;
        }

            .navbar-nav .open .dropdown-menuEz > li > a:hover,
            .navbar-nav .open .dropdown-menuEz > li > a:focus {
                background-image: none;
            }
}

.button_groupEz_pink {
    border: 1px solid #FEE8E9;
    letter-spacing: 1.4px;
    font-weight: bold;
    color: #000000;
    background-color: #FFFFFF;
    min-width: 124px;
    min-height: 29px;
    margin-right: 20px;
}

    .button_groupEz_pink:hover {
        border: 1px solid #FEE8E9;
        background-color: #FEE8E9;
    }

.btn-bold {
    font-weight: bold;
}

/*btn-pink*/

.btn-pink {
    color: #000000;
    background-color: #FFFFFF;
    margin-bottom: 3px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .btn-pink {
        border: 1.3px solid #FEE8E9\9;
    }
}

@media screen and (min-width: 1920px) {
    .btn-pink {
        letter-spacing: 2px;
        border: 1.5px solid #FEE8E9;
    }
}

@media screen and (min-width: 1440px) and (max-width: 1920px) {
    .btn-pink {
        letter-spacing: 1.4px;
        border: 1.5px solid #FEE8E9;
    }
}

@media screen and (max-width: 1439px) {
    .btn-pink {
        border: 1.3px solid #FEE8E9;
    }
}

.btn-pink:hover,
.btn-pink:focus,
.btn-pink:active,
.btn-pink.active,
.open .dropdown-toggle.btn-pink {
    color: #000000;
    background-color: #FEE8E9;
    border-color: #FEE8E9;
    box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-pink:active,
.btn-pink.active,
.open .dropdown-toggle.btn-pink {
    background-image: none;
}

.btn-pink.disabled,
.btn-pink[disabled],
fieldset[disabled] .btn-pink,
.btn-pink.disabled:hover,
.btn-pink[disabled]:hover,
fieldset[disabled] .btn-pink:hover,
.btn-pink.disabled:focus,
.btn-pink[disabled]:focus,
fieldset[disabled] .btn-pink:focus,
.btn-pink.disabled:active,
.btn-pink[disabled]:active,
fieldset[disabled] .btn-pink:active,
.btn-pink.disabled.active,
.btn-pink[disabled].active,
fieldset[disabled] .btn-pink.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

/*btn-Basic*/
.btn-Basic {
    color: #000000;
    background-color: #FFFFFF;
    border-color: #F2F2F2;
    margin-bottom: 3px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .btn-Basic {
        border: 1.3px solid #F2F2F2\9;
    }
}

@media screen and (min-width: 1920px) {
    .btn-Basic {
        letter-spacing: 2px;
        border: 1.5px solid #F2F2F2;
    }
}

@media screen and (min-width: 1440px) and (max-width: 1920px) {
    .btn-Basic {
        letter-spacing: 1.4px;
        border: 1.5px solid #F2F2F2;
    }
}

@media screen and (max-width: 1439px) {
    .btn-Basic {
        border: 1.3px solid #F2F2F2;
    }
}

.btn-Basic:hover,
.btn-Basic:focus,
.btn-Basic:active,
.btn-Basic.active,
.open .dropdown-toggle.btn-Basic {
    color: #000000;
    background-color: #F2F2F2;
    border-color: #FFFFFF;
    box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-Basic:active,
.btn-Basic.active,
.open .dropdown-toggle.btn-Basic {
    background-image: none;
}

.btn-Basic.disabled,
.btn-Basic[disabled],
fieldset[disabled] .btn-Basic,
.btn-Basic.disabled:hover,
.btn-Basic[disabled]:hover,
fieldset[disabled] .btn-Basic:hover,
.btn-Basic.disabled:focus,
.btn-Basic[disabled]:focus,
fieldset[disabled] .btn-Basic:focus,
.btn-Basic.disabled:active,
.btn-Basic[disabled]:active,
fieldset[disabled] .btn-Basic:active,
.btn-Basic.disabled.active,
.btn-Basic[disabled].active,
fieldset[disabled] .btn-Basic.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

/*btn-yellow*/

.btn-yellow {
    color: #000000;
    background-color: #F8F3A5;
    margin-bottom: 3px;
    margin-top: 3px;
}

    .btn-yellow:hover,
    .btn-yellow:focus,
    .btn-yellow:active,
    .btn-yellow.active,
    .open .dropdown-toggle.btn-yellow {
        color: #000000;
        background-color: #FFD700;
        box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
    }

    .btn-yellow:active,
    .btn-yellow.active,
    .open .dropdown-toggle.btn-yellow {
        background-image: none;
    }

    .btn-yellow.disabled,
    .btn-yellow[disabled],
    fieldset[disabled] .btn-yellow,
    .btn-yellow.disabled:hover,
    .btn-yellow[disabled]:hover,
    fieldset[disabled] .btn-yellow:hover,
    .btn-yellow.disabled:focus,
    .btn-yellow[disabled]:focus,
    fieldset[disabled] .btn-yellow:focus,
    .btn-yellow.disabled:active,
    .btn-yellow[disabled]:active,
    fieldset[disabled] .btn-yellow:active,
    .btn-yellow.disabled.active,
    .btn-yellow[disabled].active,
    fieldset[disabled] .btn-yellow.active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }

.font_Last_Day_MEMO {
    line-height: 24px;
}

.font_Menu {
    line-height: 22px;
    color: #000000;
    background-size: 100% 100%; /*for IE*/
    max-width: 100%;
    width: auto\9; /* IE6, IE7, IE8, IE9 */
    width: 100%;
}

    .font_Menu > a {
        position: relative;
        z-index: 1;
    }

.td_Menu_red {
    line-height: 25px;
    color: #000000;
    background-color: rgba(247, 201, 201, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.td_Menu_Coo {
    line-height: 25px;
    color: #000000;
    background-color: rgba(234, 220, 190, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.td_Menu_blue {
    line-height: 25px;
    color: #000000;
    background-color: rgba(226, 225, 254, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.td_Menu_green {
    line-height: 25px;
    color: #000000;
    background-color: rgba(237, 245, 225, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.font_NAME {
    font-size: 24px;
    font-weight: bold;
    color: #333333;
}

.font_Cash {
    font-size: 180%;
    color: #000000;
    vertical-align: bottom;
}

@media screen and (min-width: 1500px) {
    .font_Cash {
        font-size: 16px;
    }
}

@media screen and (max-width: 1499px) {
    .font_Cash {
        font-size: 15px;
    }
}

@media screen and (max-width: 786px) {
    .font_Cash {
        font-size: 14px;
    }
}

@media screen and (max-width: 480px) {
    .font_Cash {
        font-size: 13px;
    }
}

.layout_font_title {
    font-size: 26px;
    font-weight: bold;
    color: #004aa2;
    text-align: center;
}

/*.btn-link-ez*/
.btn-link-ez {
    font-weight: normal;
    color: #428bca;
    cursor: pointer;
    border-radius: 0;
}

    .btn-link-ez,
    .btn-link-ez:active,
    .btn-link-ez[disabled],
    fieldset[disabled] .btn-link {
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

        .btn-link-ez,
        .btn-link-ez:hover,
        .btn-link-ez:focus,
        .btn-link-ez:active {
            border-color: transparent;
        }

            .btn-link-ez:hover,
            .btn-link-ez:focus {
                color: #2a6496;
                text-decoration: underline;
                background-color: transparent;
            }

            .btn-link-ez[disabled]:hover,
            fieldset[disabled] .btn-link-ez:hover,
            .btn-link-ez[disabled]:focus,
            fieldset[disabled] .btn-link-ez:focus {
                color: #999999;
                text-decoration: none;
            }

/*table-ecool*/

.table-ecool {
    width: 100%;
    margin-bottom: 20px;
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    margin: 0 auto;
}

@media (min-width: 767px) {
    .table-ecool thead > tr > th,
    .table-ecool tbody > tr > th,
    .table-ecool tfoot > tr > th,
    .table-ecool thead > tr > td,
    .table-ecool tbody > tr > td,
    .table-ecool tfoot > tr > td {
        line-height: 1.5;
        padding: 5px;
        vertical-align: middle;
    }
}
}

.table-ecool thead > tr > th,
.table-ecool tbody > tr > th,
.table-ecool tfoot > tr > th,
.table-ecool thead > tr > td,
.table-ecool tbody > tr > td,
.table-ecool tfoot > tr > td {
    line-height: 1.428571429;
    padding: 8px;
    vertical-align: top;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .table-ecool thead > tr > th,
    .table-ecool thead > tr > td {
        font-size: 22px\9;
        white-space: nowrap\9;
    }
}

/*大於1920px*/
@media screen and (min-width: 1920px) {
    .table-ecool thead > tr > th,
    .table-ecool thead > tr > td {
        font-size: 22px;
        white-space: nowrap;
    }
}

/*小於1920px SHow*/
@media screen and (min-width: 1280px) and (max-width: 1920px) {
    .table-ecool thead > tr > th,
    .table-ecool thead > tr > td {
        font-size: 21px;
        white-space: nowrap;
    }
}

/*小於1024px SHow*/
@media screen and (min-width: 768px) and (max-width: 1280px) {
    .table-ecool thead > tr > th,
    .table-ecool thead > tr > td {
        font-size: 19px;
        white-space: normal;
    }
}

/*小於768px SHow*/
@media screen and (max-width: 768px) {
    .table-ecool thead > tr > th,
    .table-ecool thead > tr > td {
        font-size: 16px;
        white-space: nowrap;
    }
}

@media screen and (max-width: 480px) {
    .table-ecool thead > tr > th,
    .table-ecool thead > tr > td {
        font-size: 13px;
        white-space: nowrap;
    }
}

/*大於1920px*/
@media screen and (min-width: 1920px) {
    .table-ecool tbody > tr > th,
    .table-ecool tbody > tr > td {
        font-size: 22px;
    }
}

/*小於1920px SHow*/
@media screen and (min-width: 1280px) and (max-width: 1920px) {
    .table-ecool tbody > tr > th,
    .table-ecool tbody > tr > td {
        font-size: 21px;
    }
}

/*小於1024px SHow*/
@media screen and (min-width: 768px) and (max-width: 1280px) {
    .table-ecool tbody > tr > th,
    .table-ecool tbody > tr > td {
        font-size: 19px;
    }
}

/*小於768px SHow*/
@media screen and (max-width: 768px) {
    .table-ecool tbody > tr > th,
    .table-ecool tbody > tr > td {
        font-size: 15px;
    }
}

@media screen and (max-width: 480px) {
    .table-ecool tbody > tr > th,
    .table-ecool tbody > tr > td {
        font-size: 13px;
    }
}

/*table-ecool-sm*/
.table-ecool-sm {
    width: 100%;
    margin-bottom: 20px;
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    margin: 0 auto;
    table-layout: fixed;
    word-wrap: break-word;
}

    .table-ecool-sm thead > tr > th,
    .table-ecool-sm tbody > tr > th,
    .table-ecool-sm tfoot > tr > th,
    .table-ecool-sm thead > tr > td,
    .table-ecool-sm tbody > tr > td,
    .table-ecool-sm tfoot > tr > td {
        line-height: 1.428571429;
        padding: 8px;
    }

/* for  IE8 ~ IE10 */
@media screen\0 {
    .table-ecool-sm thead > tr > th,
    .table-ecool-sm thead > tr > td,
    .table-ecool-sm tbody > tr > td {
        font-size: 18px\9;
    }
}

/*小於1920px SHow*/
@media screen and (max-width: 1920px) {
    .table-ecool-sm thead > tr > th,
    .table-ecool-sm thead > tr > td,
    .table-ecool-sm tbody > tr > td {
        font-size: 18px;
    }
}

/*大於1920px*/
@media screen and (min-width: 1920px) {
    .table-ecool-sm thead > tr > th,
    .table-ecool-sm thead > tr > td,
    .table-ecool-sm tbody > tr > td {
        font-size: 18px;
    }
}

.table-92Per {
    width: 92%;
}

/*table-ecool-thead*/

.table-ecool-thead > thead > tr > td,
.table-ecool-thead > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

/*table-ecool-List*/

.Div-bar-List {
    width: 95%;
    background-color: rgba(232, 209, 230, 1);
    margin: 0px auto;
    margin-top: 0;
    margin-bottom: 0;
    padding: 7px 7px;
    color: #0b24fa;
    border-color: #c6c4e3;
    font-weight: bold;
    text-decoration: none;
    text-shadow: 2px 2px 2px #DDDDDD;
    text-align: center;
}

@media screen and (min-width: 1500px) {
    .Div-bar-List {
        font-size: 16px;
        letter-spacing: 16px;
    }
}

@media screen and (max-width: 1499px) {
    .Div-bar-List {
        font-size: 15px;
        letter-spacing: 15px;
    }
}

@media screen and (max-width: 786px) {
    .Div-bar-List {
        font-size: 14px;
        letter-spacing: 14px;
    }
}

@media screen and (max-width: 480px) {
    .Div-bar-List {
        font-size: 13px;
        letter-spacing: 13px;
    }
}

.table-ecool-List > thead > tr > td,
.table-ecool-List > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-List > tbody > tr > td,
.table-ecool-List > tbody > tr > th {
    color: #333333;
    background-color: rgba(232, 209, 230, 0.2);
}

/*table-ecool-reader*/

.Div-bar-reader {
    width: 95%;
    background-color: rgba(227, 236, 159, 1);
    margin: 0px auto;
    margin-top: 0;
    margin-bottom: 0;
    padding: 7px 7px;
    color: #0b24fa;
    border-color: #c6c4e3;
    font-weight: bold;
    text-decoration: none;
    text-shadow: 2px 2px 2px #DDDDDD;
    text-align: center;
}

@media screen and (min-width: 1500px) {
    .Div-bar-reader {
        font-size: 16px;
        letter-spacing: 16px;
    }
}

@media screen and (max-width: 1499px) {
    .Div-bar-reader {
        font-size: 15px;
        letter-spacing: 15px;
    }
}

@media screen and (max-width: 786px) {
    .Div-bar-reader {
        font-size: 14px;
        letter-spacing: 14px;
    }
}

@media screen and (max-width: 480px) {
    .Div-bar-reader {
        font-size: 13px;
        letter-spacing: 13px;
    }
}

.table-ecool-reader > thead > tr > td,
.table-ecool-reader > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-reader > tbody > tr > td,
.table-ecool-reader > tbody > tr > th {
    color: #333333;
    background-color: rgba(227, 236, 159, 0.2);
}

/*table-ecool-rpp*/

.table-ecool-rpp > thead > tr > td,
.table-ecool-rpp > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-rpp > tbody > tr > td,
.table-ecool-rpp > tbody > tr > th {
    color: #333333;
    background-color: rgba(250,233,180, 0.2);
}

/*table-ecool-rpp-sm*/

.table-ecool-rpp-sm > thead > tr > td,
.table-ecool-rpp-sm > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-rpp-sm > tbody > tr > td,
.table-ecool-rpp-sm > tbody > tr > th {
    color: #333333;
    background-color: rgba(250,233,180, 0.2);
}

/*table-ecool-ADDI06*/

.table-ecool-ADDI06 > thead > tr > td,
.table-ecool-ADDI06 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-ADDI06 > tbody > tr > td,
.table-ecool-ADDI06 > tbody > tr > th {
    color: #333333;
    background-color: rgba(226,197,133, 0.2);
}

/*table-ecool-ADDI07*/

.table-ecool-ADDI07 > thead > tr > td,
.table-ecool-ADDI07 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-ADDI07 > tbody > tr > td,
.table-ecool-ADDI07 > tbody > tr > th {
    color: #333333;
    background-color: rgba(226,197,133, 0.2);
}

/*table-ecool-AWA003*/

.Div-bar-AWA003 {
    width: 95%;
    background-color: rgba(253, 230, 128, 1);
    margin: 0px auto;
    margin-top: 0;
    margin-bottom: 0;
    padding: 7px 7px;
    color: #0b24fa;
    border-color: #c6c4e3;
    font-weight: bold;
    text-decoration: none;
    text-shadow: 2px 2px 2px #DDDDDD;
    text-align: center;
}

@media screen and (min-width: 1500px) {
    .Div-bar-AWA003 {
        font-size: 16px;
        letter-spacing: 16px;
    }
}

@media screen and (max-width: 1499px) {
    .Div-bar-AWA003 {
        font-size: 15px;
        letter-spacing: 15px;
    }
}

@media screen and (max-width: 786px) {
    .Div-bar-AWA003 {
        font-size: 14px;
        letter-spacing: 14px;
    }
}

@media screen and (max-width: 480px) {
    .Div-bar-AWA003 {
        font-size: 13px;
        letter-spacing: 13px;
    }
}

.table-ecool-AWA003 > thead > tr > td,
.table-ecool-AWA003 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-AWA003 > tbody > tr > td,
.table-ecool-AWA003 > tbody > tr > th {
    color: #333333;
    background-color: rgba(253, 230, 128, 0.2);
}

/*table-ecool-ADDO05*/

.table-ecool-ADDO05 > thead > tr > td,
.table-ecool-ADDO05 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-ADDO05 > tbody > tr > td,
.table-ecool-ADDO05 > tbody > tr > th {
    color: #333333;
    background-color: rgba(250, 206, 56, 0.2);
}

/*Div-EZ-AWA004*/
.Div-EZ-AWA004 {
    width: 90%;
    background-color: rgba(243, 200, 176, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-AWA004 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-AWA004 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-AWA004 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-AWA004 .Div-BK {
        background-color: rgba(227, 236, 159, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-AWA004 .form-horizontal .control-label,
    .Div-EZ-AWA004 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-AWA004 .form-horizontal .control-label-D,
    .Div-EZ-AWA004 .form-horizontal .control-label-left-D {
        font-size: 17px;
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-AWA004 .table-ecool > thead > tr > td,
    .Div-EZ-AWA004 .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/*table-ecool-AWA004*/

.table-ecool-AWA004 > thead > tr > td,
.table-ecool-AWA004 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-AWA004 > tbody > tr > td,
.table-ecool-AWA004 > tbody > tr > th {
    color: #333333;
    background-color: rgba(243, 200, 176, 0.2);
}

/*table-ecool-AWA007*/

.table-ecool-AWA007 > thead > tr > td,
.table-ecool-AWA007 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-AWA007 > tbody > tr > td,
.table-ecool-AWA007 > tbody > tr > th {
    color: #333333;
    background-color: rgba(225, 231, 250, 0.2);
}

/*table-ecool-ZZZI04*/

.table-ecool-ZZZI04 > thead > tr > td,
.table-ecool-ZZZI04 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-ZZZI04 > tbody > tr > td,
.table-ecool-ZZZI04 > tbody > tr > th {
    color: #333333;
    background-color: rgba(236, 237, 51, 0.2);
}

/*btn-table-link*/
.btn-table-link {
    font-weight: normal;
    color: #004da0;
    cursor: pointer;
    border-radius: 0;
}

    .btn-table-link,
    .btn-table-link:active,
    .btn-table-link[disabled],
    fieldset[disabled] .btn-link {
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

        .btn-table-link,
        .btn-table-link:hover,
        .btn-table-link:focus,
        .btn-table-link:active {
            border-color: transparent;
        }

            .btn-table-link:hover,
            .btn-table-link:focus {
                color: #2a6496;
                text-decoration: underline;
                background-color: transparent;
            }

            .btn-table-link[disabled]:hover,
            fieldset[disabled] .btn-link:hover,
            .btn-table-link[disabled]:focus,
            fieldset[disabled] .btn-link:focus {
                color: #999999;
                text-decoration: none;
            }

/*table-ecool-Bule-01-SEC*/

.table-ecool-Bule-01-SEC > thead > tr > td,
.table-ecool-Bule-01-SEC > thead > tr > th {
    background-color: rgba(198, 248, 245, 0.5);
    height: 30px;
    font-weight: bold;
}

/*table-ecool-Bule-SEC*/

.table-ecool-Bule-SEC > thead > tr:nth-child(odd) > td,
.table-ecool-Bule-SEC > thead > tr:nth-child(odd) > th {
    background-color: rgba(198, 248, 245, 1);
    text-align: center;
    height: 40px;
    font-weight: bold;
}

.table-ecool-Bule-SEC > thead > tr:nth-child(even) > td,
.table-ecool-Bule-SEC > thead > tr:nth-child(even) > th {
    background-color: rgba(198, 248, 245, 0.5);
    text-align: center;
    height: 30px;
    font-weight: bold;
}

/*table-ecool-pink-SEC*/

.table-ecool-pink-SEC > thead > tr:nth-child(odd) > td,
.table-ecool-pink-SEC > thead > tr:nth-child(odd) > th {
    background-color: rgba(232, 209, 230, 1);
    text-align: center;
    height: 40px;
    font-weight: bold;
}

.table-ecool-pink-SEC > thead > tr:nth-child(even) > td,
.table-ecool-pink-SEC > thead > tr:nth-child(even) > th {
    background-color: rgba(232, 209, 230, 0.5);
    text-align: center;
    height: 30px;
    font-weight: bold;
}

/*table-ecool-yellow-SEC*/

.table-ecool-yellow-SEC > thead > tr:nth-child(odd) > td,
.table-ecool-yellow-SEC > thead > tr:nth-child(odd) > th {
    background-color: rgba(227, 236, 159, 1);
    text-align: center;
    height: 40px;
    font-weight: bold;
}

.table-ecool-yellow-SEC > thead > tr:nth-child(even) > td,
.table-ecool-yellow-SEC > thead > tr:nth-child(even) > th {
    background-color: rgba(227, 236, 159, 0.5);
    text-align: center;
    height: 30px;
    font-weight: bold;
}

/*table-ecool-Tangerine-SEC*/

.table-ecool-Tangerine-SEC > thead > tr:nth-child(odd) > td,
.table-ecool-Tangerine-SEC > thead > tr:nth-child(odd) > th {
    background-color: rgba(228, 199, 130, 1);
    text-align: center;
    height: 40px;
    font-weight: bold;
}

.table-ecool-Tangerine-SEC > thead > tr:nth-child(even) > td,
.table-ecool-Tangerine-SEC > thead > tr:nth-child(even) > th {
    background-color: rgba(228, 199, 130, 0.5);
    text-align: center;
    height: 30px;
    font-weight: bold;
}

/*table-ecool-Tangerine2-SEC*/

.table-ecool-Tangerine2-SEC > thead > tr:nth-child(odd) > td,
.table-ecool-Tangerine2-SEC > thead > tr:nth-child(odd) > th {
    background-color: rgba(245, 201, 175, 1);
    text-align: center;
    height: 40px;
    font-weight: bold;
}

.table-ecool-Tangerine2-SEC > thead > tr:nth-child(even) > td,
.table-ecool-Tangerine2-SEC > thead > tr:nth-child(even) > th {
    background-color: rgba(245, 201, 175, 0.5);
    text-align: center;
    height: 30px;
    font-weight: bold;
}

/*table-ecool-AWA004*/

.table-ecool-AWA004 > thead > tr > td,
.table-ecool-AWA004 > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
}

.table-ecool-AWA004 > tbody > tr > td,
.table-ecool-AWA004 > tbody > tr > th {
    color: #333333;
    background-color: rgba(243, 200, 176, 0.2);
}

.bar-div {
    max-width: 664px;
    margin: 0 auto;
}

.col-height {
    margin-bottom: -10000px;
    padding-bottom: 10000px;
}

.row-hidden {
    overflow: hidden;
}

.prod-caption {
    text-align: left;
    line-height: 1.2;
    margin-left: 6px;
}

@media screen and (max-width: 1200px) {
    .prod-icon {
        text-align: left;
    }
}

@media screen and (min-width: 1200px) {
    .prod-icon {
        text-align: center;
    }
}

.prod-text {
    margin-right: 14px;
    display: inline;
}

.btn-prod {
    margin-top: 10px;
    margin-bottom: 10px
}

.box {
    /*非IE的主流瀏覽器識別的垂直居中的方法*/
    display: table-cell;
    vertical-align: middle;
    /*設置水平居中*/
    text-align: center;
    /*針對IE的Hack */
    * display: block;
    * font-size: 175px; /*約為高度的0.873/
        * font-family : Arial ; /*防止非utf-8引起的hack失效問題，如gbk編碼*/
    width: 270px;
    height: 200px;
    margin-bottom: 12px;
}

    .box > div {
        /*設置圖片垂直居中*/
        width: 100%;
        max-width: 200px;
        max-height: 200px;
        vertical-align: middle;
        text-align: center;
        margin: 0 auto;
    }

        .box > div > img {
            /*設置圖片垂直居中*/
            display: block;
            width: 100%;
            max-width: 200px;
            max-height: 200px;
            vertical-align: middle;
        }

.imgEZ {
    max-width: 100%;
    height: auto;
}

.imgMenu {
    max-width: 100%;
    width: auto\9; /* IE6, IE7, IE8, IE9 */
    width: 100%;
    height: auto;
}

/*認證徵章*/

/* for  IE8 ~ IE10 */
@media screen\0 {
    .imgEz_Badge {
        width: auto\9;
        height: auto\9;
        max-height: 29px\9;
    }
}

@media screen and (min-width: 1500px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 50px;
    }
}

@media screen and (max-width: 1500px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 32px;
    }
}

@media screen and (max-width: 1194px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 29px;
    }
}

@media screen and (max-width: 1024px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 28px;
    }
}

@media screen and (max-width: 767px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 100%;
        max-width: 100%;
    }
}

@media screen and (max-width: 480px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 55px;
    }
}

/*護照徵章*/

/* for  IE8 ~ IE10 */
@media screen\0 {
    .imgEz_BadgeBook {
        width: auto\9;
        height: auto\9;
        max-height: 32px\9;
    }
}

@media screen and (min-width: 1501px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 45px;
    }
}

@media screen and (max-width: 1500px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 38px;
    }
}

@media screen and (max-width: 1194px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 32px;
    }
}

@media screen and (max-width: 1024px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 30px;
    }
}

@media screen and (max-width: 767px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 100%;
        max-width: 100%;
    }
}

@media screen and (max-width: 480px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 45px;
    }
}

.imgfooter {
    display: block;
    width: 100%;
    height: auto;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    margin: 0px auto;
    text-align: center;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .imgfooter {
        padding-top: 2px\9;
        font-size: 19px\9;
        min-height: 25px\9;
        max-width: 905px\9;
    }

        .imgfooter > font {
            font-size: 19px\9;
        }
}

@media screen and (min-width: 1500px) {
    .imgfooter {
        padding-top: 5px;
        font-size: 22px;
        min-height: 34px;
        max-width: 905px;
    }

        .imgfooter > font {
            font-size: 22px;
        }
}

@media screen and (max-width: 1500px) {
    .imgfooter {
        padding-top: 2px;
        font-size: 19px;
        min-height: 25px;
        max-width: 905px;
    }

        .imgfooter > font {
            font-size: 19px;
        }
}

@media screen and (max-width: 480px) {
    .imgfooter {
        font-size: 16px;
        max-width: 480px;
    }

        .imgfooter > font {
            font-size: 16px;
        }
}

@media screen and (max-width: 360px) {
    .imgfooter {
        font-size: 15px;
        max-width: 320px;
    }

        .imgfooter > font {
            font-size: 15px;
        }
}

/*小於768px SHow*/
@media screen and (max-width: 767px) {
    .Div-Menu {
        background-color: #E6EB42;
        top: 50px;
        position: fixed;
        z-index: 1;
    }

    .Top_footer {
        position: fixed;
        bottom: 0;
        right: 10px;
        height: 58px;
    }
}

@media screen and (max-width: 321px) {
    .Div-Menu {
        top: 50px;
        position: fixed;
        z-index: 1;
    }

    .Top_footer {
        position: fixed;
        bottom: 0;
        right: 10px;
        height: 58px;
    }
}

/*大於768px hide*/
@media screen and (min-width: 767px) {
    .Div-Menu {
    }
}

.containerEZ {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .containerEZ {
        width: 1190px\9;
    }
}

@media all and (min-width:1200px) {
    .containerEZ {
        width: 1190px;
    }
}

@media all and (min-width:1910px) {
    .containerEZ {
        width: 1190px;
    }
}

@media all and (min-width:1920px) {
    .containerEZ {
        width: 1190px;
    }
}

.containerEZ-fluid {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

.containerEZ > .navbar-header {
    margin-right: -15px;
    margin-left: -15px;
}

.containerEZ-fluid > .navbar-header {
    margin-right: -15px;
    margin-left: -15px;
}

.containerEZ > .navbar-collapse {
    margin-right: -15px;
    margin-left: -15px;
}

.containerEZ-fluid > .navbar-collapse {
    margin-right: -15px;
    margin-left: -15px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .containerEZ > .navbar-header {
        margin-right: 0px\9;
        margin-left: 0px\9;
    }

    .containerEZ-fluid > .navbar-header {
        margin-right: 0px\9;
        margin-left: 0px\9;
    }

    .containerEZ > .navbar-collapse {
        margin-right: 0px\9;
        margin-left: 0px\9;
    }

    .containerEZ-fluid > .navbar-collapse {
        margin-right: 0px\9;
        margin-left: 0px\9;
    }
}

@media all and (min-width:767px) {
    .containerEZ > .navbar-header {
        margin-right: 0px;
        margin-left: 0px;
    }

    .containerEZ-fluid > .navbar-header {
        margin-right: 0px;
        margin-left: 0px;
    }

    .containerEZ > .navbar-collapse {
        margin-right: 0px;
        margin-left: 0px;
    }

    .containerEZ-fluid > .navbar-collapse {
        margin-right: 0px;
        margin-left: 0px;
    }
}

.containerEZ .jumbotron {
    border-radius: 6px;
}

.containerEZ-fluid .jumbotron {
    border-radius: 6px;
}

.containerEZ .jumbotron {
    padding-right: 60px;
    padding-left: 60px;
}

.containerEZ-fluid .jumbotron {
    padding-right: 60px;
    padding-left: 60px;
}

.containerEZ::before {
    display: table;
    content: " ";
}

.containerEZ::after {
    display: table;
    content: " ";
}

.containerEZ-fluid::before {
    display: table;
    content: " ";
}

.containerEZ-fluid::after {
    display: table;
    content: " ";
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .navbar-ECOOL {
        display: none !important\9;
    }

    .Div-navbar {
        display: none\9;
    }
}

@media (min-width:767px) {
    .navbar-ECOOL {
        display: none !important;
    }
}

/*小於767px SHow*/
@media (max-width: 767px) {
    .Div-navbar {
        display: block;
        display: none\9; /* for  IE8 ~ IE10 */
    }
}

/*大於768px hide*/
@media (min-width: 767px) {
    .Div-navbar {
        display: none;
    }
}

.navbar-brandEZ:hover,
.navbar-brandEZ:focus {
    text-decoration: none;
}

@media (max-width: 767px) {
    .navbar > .container .navbar-brandEZ {
        margin-left: -15px;
    }
}

.vr_pagebreak {
    page-break-before: always;
}

.paginationW {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 4px;
}

    .paginationW > li {
        display: inline;
    }

        .paginationW > li > a,
        .paginationW > li > span {
            position: relative;
            float: left;
            padding: 6px 12px;
            margin-left: -1px;
            line-height: 1.428571429;
            text-decoration: none;
            cursor: pointer;
        }

        .paginationW > li:first-child > a,
        .paginationW > li:first-child > span {
            margin-left: 0;
            border-bottom-left-radius: 4px;
            border-top-left-radius: 4px;
        }

        .paginationW > li:last-child > a,
        .paginationW > li:last-child > span {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        .paginationW > li > a:hover,
        .paginationW > li > span:hover,
        .paginationW > li > a:focus,
        .paginationW > li > span:focus {
            color: #004da0
        }

    .paginationW > .active > a,
    .paginationW > .active > span,
    .paginationW > .active > a:hover,
    .paginationW > .active > span:hover,
    .paginationW > .active > a:focus,
    .paginationW > .active > span:focus {
        z-index: 2;
        color: #333333;
        cursor: default;
        border-color: #333333;
        border: 1px solid;
    }

    .paginationW > .disabled > span,
    .paginationW > .disabled > a,
    .paginationW > .disabled > a:hover,
    .paginationW > .disabled > a:focus {
        color: #999999;
        cursor: not-allowed;
        border-color: #dddddd;
    }

.input-xs {
    height: 25px;
    padding: 5px 10px;
    font-size: 18px;
    line-height: 1.5;
    border-radius: 3px;
}

select.input-xs {
    height: 25px;
    line-height: 25px;
}

textarea.input-xs {
    height: auto;
}

.lobinLabel {
    color: white;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .lobinLabel {
        font-size: 18px\9;
        margin-right: 1px\9;
    }

    .LoginBox {
        position: relative\9;
        max-width: 80px\9;
        overflow: hidden\9;
        width: auto\9; /* IE6, IE7, IE8, IE9 */
        width: 100%\9;
        box-sizing: border-box\9;
        -moz-box-sizing: border-box\9;
        -webkit-box-sizing: border-box\9;
        z-index: 1\9;
    }
}

@media screen and (min-width: 1366px) {
    .lobinLabel {
        font-size: 21px;
        margin-right: 5px
    }

    .LoginBox {
        max-width: 95px;
        overflow: hidden;
        width: auto\9; /* IE6, IE7, IE8, IE9 */
        width: 100%;
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }
}

@media screen and (max-width: 1366px) {
    .lobinLabel {
        font-size: 18px;
        margin-right: 1px
    }

    .LoginBox {
        width: auto\9; /* IE6, IE7, IE8, IE9 */
        width: 100%;
        max-width: 85px;
        overflow: hidden;
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }
}

@media screen and (max-width: 1120px) {
    .LoginBox {
        width: auto\9; /* IE6, IE7, IE8, IE9 */
        width: 100%;
        max-width: 85px;
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        overflow: hidden;
    }
}

.table_Menu {
    max-width: 179px;
    width: auto\9; /* IE6, IE7, IE8, IE9 */
    width: 100%;
}

.dl-horizontal-EZ dt {
    float: left;
    /*width: 160px;*/
    overflow: hidden;
    clear: left;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 6px;
    color: #004da0;
}

.dl-horizontal-EZ dd {
}

    .dl-horizontal-EZ dd:before,
    .dl-horizontal-EZ dd:after {
        display: table;
        content: " ";
    }

    .dl-horizontal-EZ dd:after {
        clear: both;
    }

    .dl-horizontal-EZ dd:before,
    .dl-horizontal-EZ dd:after {
        display: table;
        content: " ";
    }

    .dl-horizontal-EZ dd:after {
        clear: both;
    }

.dt {
    font-weight: bold;
    color: #004da0;
    margin-left: 10px
}

.dd {
    font-weight: bold
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .dt {
        font-size: 20px\9;
    }

    .dd {
        font-size: 20px\9;
    }
}

@media (min-width: 1920px) {
    .dt {
        font-size: 22px
    }

    .dd {
        font-size: 22px
    }
}

@media (max-width: 1920px) {
    .dt {
        font-size: 21px
    }

    .dd {
        font-size: 21px
    }
}

@media (max-width: 1366px) {
    .dt {
        font-size: 20px
    }

    .dd {
        font-size: 20px
    }
}

@media (max-width: 1024px) {
    .dt {
        font-size: 19px
    }

    .dd {
        font-size: 19px
    }
}

@media (max-width: 767px) {
    .dt {
        font-size: 18px
    }

    .dd {
        font-size: 18px
    }
}

@media (max-width: 480px) {
    .dt {
        font-size: 13px
    }

    .dd {
        font-size: 13px
    }
}

.p-context {
    word-wrap: break-word;
    word-break: normal;
    text-align: left;
    width: 95%;
    margin: 0px auto;
    overflow: hidden;
}

    .p-context img {
        height: auto;
        max-width: 100%;
    }

/* for  IE8 ~ IE10 */
@media screen\0 {
    .p-context {
        font-size: 20px\9;
        line-height: 30px\9;
    }
}

@media (min-width: 1366px) {
    .p-context {
        font-size: 20px;
        line-height: 30px;
    }
}

@media (max-width: 1366px) {
    .p-context {
        font-size: 20px;
        line-height: 26px;
    }
}

@media (max-width: 1024px) {
    .p-context {
        font-size: 19px;
        line-height: 22px;
    }
}

@media (max-width: 767px) {
    .p-context {
        font-size: 18px;
        line-height: 22px;
    }
}

.label_dt_font16 {
    font-size: 22px;
    font-weight: bold;
    color: #004da0;
}

.label_dt {
    font-weight: bold;
    color: #004da0;
}

.label_dt_S {
    font-size: 20px;
    font-weight: bold;
    color: #004da0;
}

.label_dd_font18 {
    font-size: 22px;
    font-weight: bold;
    color: #004da0;
    line-height: 24px;
    letter-spacing: 1.5px;
}

.label_dd {
    font-weight: bold;
    color: #333333;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .label_dt {
        font-size: 19px\9;
    }

    .label_dd {
        font-size: 19px\9;
    }
}

@media (min-width: 1920px) {
    .label_dt {
        font-size: 21px
    }

    .label_dd {
        font-size: 21px
    }
}

@media (max-width: 1920px) {
    .label_dt {
        font-size: 20px
    }

    .label_dd {
        font-size: 20px
    }
}

@media (max-width: 1366px) {
    .label_dt {
        font-size: 19px
    }

    .label_dd {
        font-size: 19px
    }
}

@media (max-width: 1024px) {
    .label_dt {
        font-size: 18px
    }

    .label_dd {
        font-size: 18px
    }
}

@media (max-width: 767px) {
    .label_dt {
        font-size: 17px
    }

    .label_dd {
        font-size: 17px
    }
}

.lnkFont {
    font-weight: bold;
    font-size: 22px;
}

.lnkFont2 {
    text-align: left;
    display: inline-block;
    width: 50px;
    font-weight: bold;
    font-size: 21pt;
    color: #0061CA;
}

/*Div-EZ-task*/
.Div-EZ-task {
    width: 90%;
    background-color: rgba(173, 251, 242, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-task .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-task .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-task .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-task .Div-Feedback {
        background-color: rgba(232, 209, 230, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-task .form-horizontal .control-label,
    .Div-EZ-task .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-Pink*/
.Div-EZ-Pink {
    width: 90%;
    background-color: rgba(232, 209, 230, 0.2);
    margin: 0px;
    margin-top: -15px;
}

    .Div-EZ-Pink .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-Pink .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-Pink .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-Pink .Div-Feedback {
        background-color: rgba(232, 209, 230, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-Pink .form-horizontal .control-label,
    .Div-EZ-Pink .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-Pink .form-horizontal .control-label-D,
    .Div-EZ-Pink .form-horizontal .control-label-left-D {
        font-size: 23px;
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-reader*/
.Div-EZ-reader {
    width: 90%;
    background-color: rgba(227, 236, 159, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-reader .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-reader .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-reader .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-reader .Div-BK {
        background-color: rgba(227, 236, 159, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-reader .form-horizontal .control-label,
    .Div-EZ-reader .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-reader .form-horizontal .control-label-D,
    .Div-EZ-reader .form-horizontal .control-label-left-D {
        font-size: 23px;
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-reader .table-ecool > thead > tr > td,
    .Div-EZ-reader .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/*Div-EZ-ArtGallery*/
.Div-EZ-ArtGallery {
    width: 90%;
    margin: 0px auto;
    margin-top: -40px;
}

    .Div-EZ-ArtGallery .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ArtGallery .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ArtGallery .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ArtGallery .Div-BK {
        background-color: rgba(227, 236, 159, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ArtGallery .form-horizontal .control-label,
    .Div-EZ-ArtGallery .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-ArtGallery .form-horizontal .control-label-D,
    .Div-EZ-ArtGallery .form-horizontal .control-label-left-D {
        font-size: 17px;
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-ArtGallery .table-ecool > thead > tr > td,
    .Div-EZ-ArtGallery .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/*Div-EZ-ADDI06*/
.Div-EZ-ADDI06 {
    width: 90%;
    background-color: rgba(226, 197, 133, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-ADDI06 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI06 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ADDI06 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI06 .form-horizontal .control-label,
    .Div-EZ-ADDI06 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-ADDI07*/
.Div-EZ-ADDI07 {
    width: 90%;
    background-color: rgba(226, 197, 133, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-ADDI07 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI07 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ADDI07 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI07 .form-horizontal .control-label,
    .Div-EZ-ADDI07 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-ADDI05*/
.Div-EZ-ADDI05 {
    width: 90%;
    background-color: rgba(250, 206, 56, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-ADDI05 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI05 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ADDI05 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI05 .form-horizontal .control-label,
    .Div-EZ-ADDI05 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-ADDI05 .table-ecool > thead > tr > td,
    .Div-EZ-ADDI05 .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/*Div-EZ-Awat*/
.Div-EZ-Awat {
    width: 90%;
    /*background-color: rgba(243, 200, 176, 0.2);*/
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-Awat .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-Awat .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-Awat .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-Awat .form-horizontal .control-label,
    .Div-EZ-Awat .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-Awat02*/
.Div-EZ-Awat02 {
    width: 90%;
    background-color: rgba(243, 200, 176, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-Awat02 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-Awat02 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-Awat02 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-Awat02 .form-horizontal .control-label,
    .Div-EZ-Awat02 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-AWAI02*/
.Div-EZ-AWAI02 {
    width: 90%;
    background-color: rgba(225, 231, 250, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-AWAI02 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-AWAI02 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-AWAI02 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-AWAI02 .form-horizontal .control-label,
    .Div-EZ-AWAI02 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-ZZZI04*/
.Div-EZ-ZZZI04-Bar {
    width: 95%;
    background-color: rgba(236, 237, 51, 1);
    margin: 0px auto;
    margin-top: 0;
    margin-bottom: 0;
    padding: 7px 7px;
    color: #0b24fa;
    border-color: #c6c4e3;
    font-weight: bold;
    text-decoration: none;
    text-shadow: 2px 2px 2px #DDDDDD;
    text-align: center;
}

@media screen and (min-width: 1500px) {
    .Div-EZ-ZZZI04-Bar {
        font-size: 16px;
        letter-spacing: 16px;
    }
}

@media screen and (max-width: 1499px) {
    .Div-EZ-ZZZI04-Bar {
        font-size: 15px;
        letter-spacing: 15px;
    }
}

@media screen and (max-width: 786px) {
    .Div-EZ-ZZZI04-Bar {
        font-size: 14px;
        letter-spacing: 14px;
    }
}

@media screen and (max-width: 480px) {
    .Div-EZ-ZZZI04-Bar {
        font-size: 13px;
        letter-spacing: 13px;
    }
}

.Div-EZ-ZZZI04 {
    width: 90%;
    background-color: rgba(236, 237, 51, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-ZZZI04 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ZZZI04 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ZZZI04 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ZZZI04 .form-horizontal .control-label,
    .Div-EZ-ZZZI04 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-purpose*/
.Div-EZ-purpose {
    width: 90%;
    background-color: rgba(204, 255, 255, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-purpose .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-purpose .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-purpose .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-purpose .form-horizontal .control-label,
    .Div-EZ-purpose .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-rpp*/
.Div-EZ-rpp {
    width: 90%;
    background-color: rgba(250,233,180, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-rpp .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-rpp .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-rpp .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-rpp .form-horizontal .control-label,
    .Div-EZ-rpp .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

/*Div-EZ-ADDI09*/
.Div-EZ-ADDI09 {
    width: 90%;
    background-color: rgba(226,197,133, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-ADDI09 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI09 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ADDI09 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ADDI09 .form-horizontal .control-label,
    .Div-EZ-ADDI09 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-ADDI09 .table-ecool > thead > tr > td,
    .Div-EZ-ADDI09 .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/*Div-EZ-SECI01*/
.Div-EZ-SECI01 {
    width: 90%;
    background-color: rgba(254,225,233, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-SECI01 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-SECI01 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-SECI01 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-SECI01 .form-horizontal .control-label,
    .Div-EZ-SECI01 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-SECI01 .table-ecool > thead > tr > td,
    .Div-EZ-SECI01 .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/*Div-EZ-ZZZI26*/
.Div-EZ-ZZZI26 {
    width: 90%;
    background-color: rgba(226,234,162, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-ZZZI26 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ZZZI26 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ZZZI26 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ZZZI26 .form-horizontal .control-label,
    .Div-EZ-ZZZI26 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-ZZZI26 .table-ecool > thead > tr > td,
    .Div-EZ-ZZZI26 .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/*Div-EZ-ZZZI09*/
.Div-EZ-ZZZI09 {
    width: 90%;
    background-color: rgba(239,171,184, 0.2);
    margin: 0px auto;
    margin-top: -15px;
}

    .Div-EZ-ZZZI09 .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ZZZI09 .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-EZ-ZZZI09 .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-EZ-ZZZI09 .form-horizontal .control-label,
    .Div-EZ-ZZZI09 .form-horizontal .control-label-left {
        font-weight: bold;
        color: #004da0;
    }

    .Div-EZ-ZZZI09 .table-ecool > thead > tr > td,
    .Div-EZ-ZZZI09 .table-ecool > thead > tr > th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }

/* for  IE8 ~ IE10 */
@media screen\0 {
    .form-horizontal .control-label-left {
        text-align: left\9;
    }
}

@media (min-width: 767px) {
    .form-horizontal .control-label-left {
        text-align: left\9;
    }
}

.btn-sys {
    border-color: #FAE20A;
    border-width: 1px 1px 5px 1px;
    border-style: solid;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    font-size: 16px;
    padding: 2px 6px 2px 6px;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
    color: #242424;
    background-color: #F9F9F9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#FAF219));
    background-image: -webkit-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: -moz-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: -ms-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: -o-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: linear-gradient(to bottom, #F9F9F9, #FAF219);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=#F9F9F9, endColorstr=#FAF219);
    margin-bottom: 15px;
    margin-right: 2px
}

    .btn-sys:hover,
    .btn-sys:focus,
    .btn-sys:active,
    .btn-sys.active,
    .open .dropdown-toggle.btn-sys {
        border-color: #FAE319;
        border-width: 1px 1px 5px 1px;
        border-style: solid;
        background-color: #F9F9F9;
        background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#FAD312));
        background-image: -webkit-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: -moz-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: -ms-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: -o-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: linear-gradient(to bottom, #F9F9F9, #FAD312);
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=#F9F9F9, endColorstr=#FAD312);
    }

    .btn-sys:active,
    .btn-sys.active,
    .open .dropdown-toggle.btn-sys {
        background-image: none;
    }

    .btn-sys.disabled,
    .btn-sys[disabled],
    fieldset[disabled] .btn-sys,
    .btn-sys.disabled:hover,
    .btn-sys[disabled]:hover,
    fieldset[disabled] .btn-sys:hover,
    .btn-sys.disabled:focus,
    .btn-sys[disabled]:focus,
    fieldset[disabled] .btn-sys:focus,
    .btn-sys.disabled:active,
    .btn-sys[disabled]:active,
    fieldset[disabled] .btn-sys:active,
    .btn-sys.disabled.active,
    .btn-sys[disabled].active,
    fieldset[disabled] .btn-sys.active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }

@media screen and (max-width: 767px) {
    .btn-sys {
        font-size: 10px;
    }
}

/***/

.btn-sys-busker {
    border-color: #84c1ff;
    border-width: 1px 1px 5px 1px;
    border-style: solid;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    font-size: 12px;
    padding: 2px 6px 2px 6px;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
    color: #242424;
    background-color: #F9F9F9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#c4e1ff));
    background-image: -webkit-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: -moz-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: -ms-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: -o-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: linear-gradient(to bottom, #F9F9F9, #c4e1ff);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=#F9F9F9, endColorstr=#c4e1ff);
    margin-bottom: 15px;
    margin-right: 2px
}

    .btn-sys-busker:hover,
    .btn-sys-busker:focus,
    .btn-sys-busker:active,
    .btn-sys-busker.active,
    .open .dropdown-toggle.btn-sys-busker {
        border-color: #84c1ff;
        border-width: 1px 1px 5px 1px;
        border-style: solid;
        background-color: #F9F9F9;
        background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#005ab5));
        background-image: -webkit-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: -moz-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: -ms-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: -o-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: linear-gradient(to bottom, #F9F9F9, #005ab5);
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=#F9F9F9, endColorstr=#005ab5);
    }

    .btn-sys-busker:active,
    .btn-sys-busker.active,
    .open .dropdown-toggle.btn-sys-busker {
        background-image: none;
    }

    .btn-sys-busker.disabled,
    .btn-sys-busker[disabled],
    fieldset[disabled] .btn-sys-busker,
    .btn-sys-busker.disabled:hover,
    .btn-sys-busker[disabled]:hover,
    fieldset[disabled] .btn-sys-busker:hover,
    .btn-sys-busker.disabled:focus,
    .btn-sys-busker[disabled]:focus,
    fieldset[disabled] .btn-sys-busker:focus,
    .btn-sys-busker.disabled:active,
    .btn-sys-busker[disabled]:active,
    fieldset[disabled] .btn-sys:active,
    .btn-sys-busker.disabled.active,
    .btn-sys-busker[disabled].active,
    fieldset[disabled] .btn-sys-busker.active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }

@media screen and (max-width: 767px) {
    .btn-sys-busker {
        font-size: 10px;
    }
}

.imgTitle {
    display: block;
    width: 100%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    margin: 0px auto;
    text-align: center;
    min-height: 68px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .imgTitle {
        padding-top: 2px\9;
        font-size: 19px\9;
    }
}

@media screen and (min-width: 1500px) {
    .imgTitle {
        font-size: 22px;
    }
}

@media screen and (max-width: 1500px) {
    .imgTitle {
        padding-top: 2px;
        font-size: 19px;
    }
}

@media screen and (max-width: 480px) {
    .imgTitle {
        font-size: 16px;
    }
}

.imgTitleFont {
    color: #0b24fa;
    font-weight: bold;
    letter-spacing: 14px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .imgTitleFont {
        margin-top: 37px\9;
        font-size: 20px\9;
    }
}

@media screen and (min-width: 480px) {
    .imgTitleFont {
        margin-top: 37px;
        font-size: 20px;
    }
}

@media screen and (max-width: 480px) {
    .imgTitleFont {
        margin-top: 40px;
        font-size: 18px;
    }
}

.Title_Secondary {
    font-size: 22px;
    border-bottom: 1px solid #ccc;
    letter-spacing: 1px;
}

.Caption_Div_Left {
    font-size: 22px;
    font-weight: bold;
    color: #3293fb;
    margin: 5px 5px 2px 25px;
    text-align: left;
}

.Caption_Div {
    font-size: 22px;
    font-weight: bold;
    color: #3293fb;
    margin: 5px 5px 2px 25px;
}

@media screen and (max-width: 767px) {
    .Caption_Div_Left {
        font-size: 20px;
    }

    .Caption_Div {
        font-size: 20px;
    }
}

.panel-Remove {
    background-color: transparent;
}

.panel-Img {
    background-color: rgba(226, 225, 254, 0.2);
}

    .panel-Img .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .panel-Img > .panel-heading {
        margin-top: 0;
        margin-bottom: 0;
        letter-spacing: 16px;
        padding: 2px 2px;
        color: #0b24fa;
        background-color: rgba(226, 225, 254, 1);
        border-color: #c6c4e3;
        font-size: 16px;
        font-weight: bold;
        text-decoration: none;
        text-shadow: 2px 2px 2px #DDDDDD;
    }

        .panel-Img > .panel-heading + .panel-collapse .panel-body {
            border-top-color: #c6c4e3;
        }

    .panel-Img > .panel-footer + .panel-collapse .panel-body {
        border-bottom-color: #bce8f1;
    }

.panel-ZZZ {
    background-color: rgba(226, 225, 254, 0.2);
}

.panel-ZZZ-Color2 {
    background-color: rgba(226, 225, 254, 0.4);
}

.panel-ZZZ-Color3 {
    background-color: rgba(226, 225, 254, 0.6);
}

.panel-ZZZ-Color4 {
    background-color: rgba(226, 225, 254, 0.8);
}

.panel-ZZZ-Color5 {
    background-color: rgba(226, 225, 254, 1);
}

.panel-ZZZ .Details {
    margin: 0px auto;
    padding-top: 20px;
}

.panel-ZZZ > .panel-heading {
    margin-top: 0;
    margin-bottom: 0;
    letter-spacing: 16px;
    padding: 7px 7px;
    color: #0b24fa;
    background-color: rgba(226, 225, 254, 1);
    border-color: #c6c4e3;
    font-size: 22px;
    font-weight: bold;
    text-decoration: none;
    text-shadow: 2px 2px 2px #DDDDDD;
}

    .panel-ZZZ > .panel-heading + .panel-collapse .panel-body {
        border-top-color: #c6c4e3;
    }

.panel-ZZZ > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #bce8f1;
}

.panel-ZZZ .form-horizontal .control-label,
.panel-ZZZ .form-horizontal .control-label-left {
    font-weight: bold;
    color: #004da0;
}

.panel-ZZZ .form-horizontal .Div-btn-center,
.panel-ZZZ .form-horizontal .Div-btn-center {
    text-align: center;
    margin: 0px auto;
    padding-top: 10px;
}

.panel-ZZZ .table-ecool-ZZZ > thead > tr > td,
.panel-ZZZ .table-ecool-ZZZ > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
    text-overflow: ellipsis;
}

@media (max-width: 767px) {
    .panel-ZZZ .table-ecool-ZZZ > thead > tr > td,
    .panel-ZZZ .table-ecool-ZZZ > thead > tr > th {
        text-align: left;
    }

    .panel-ZZZ .table-ecool-ZZZ tr:nth-child(odd) {
        background-color: #FAF2FF;
    }

    .panel-ZZZ .table-ecool-ZZZ {
        border-collapse: separate;
        border-spacing: 0px 2px;
    }
}

.panel-ZZZ .table-ecool-ZZZ tfoot th:before,
.panel-ZZZ .table-ecool-ZZZ tfoot td:before,
.panel-ZZZ .table-ecool-ZZZ tbody td:before {
    color: #220088;
    margin-right: 10px;
}

.panel-ACC {
    background-color: rgba(237, 245, 225, 0.2);
}

.panel-ACC-Color2 {
    background-color: rgba(237, 245, 225, 0.4);
}

.panel-ACC-Color3 {
    background-color: rgba(237, 245, 225, 0.6);
}

.panel-ACC-Color4 {
    background-color: rgba(237, 245, 225, 0.8);
}

.panel-ACC-Color5 {
    background-color: rgba(237, 245, 225, 1);
}

.panel-ACC .Details {
    margin: 0px auto;
    padding-top: 20px;
}

.panel-ACC > .panel-heading {
    margin-top: 0;
    margin-bottom: 0;
    letter-spacing: 16px;
    padding: 7px 7px;
    color: #0b24fa;
    background-color: rgba(237, 245, 225, 1);
    border-color: #c6c4e3;
    font-size: 22px;
    font-weight: bold;
    text-decoration: none;
    text-shadow: 2px 2px 2px #DDDDDD;
}

.panel-ACC > .panel-Group {
    margin-top: 0;
    margin-bottom: 0;
    letter-spacing: 16px;
    padding: 7px 7px;
    color: #004da0;
    background-color: rgba(237, 245, 225, 8);
    border-color: #c6c4e3;
    font-size: 22px;
    font-weight: bold;
    text-decoration: none;
    border-bottom: 1px solid #DDDDDD;
}

.panel-ACC > .panel-heading + .panel-collapse .panel-body {
    border-top-color: #c6c4e3;
}

.panel-ACC > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #bce8f1;
}

.panel-ACC .form-horizontal .control-label,
.panel-ACC .form-horizontal .control-label-left {
    font-weight: bold;
    color: #004da0;
}

.panel-ACC .form-horizontal .Div-btn-center,
.panel-ACC .form-horizontal .Div-btn-center {
    text-align: center;
    margin: 0px auto;
    padding-top: 10px;
}

.panel-ACC .table-ecool-ACC > thead > tr > td,
.panel-ACC .table-ecool-ACC > thead > tr > th {
    font-weight: bold;
    text-align: center;
    color: #004da0;
    text-overflow: ellipsis;
}

.Group {
    font-weight: bold;
    color: #004da0;
    background-color: #ffffff;
    border-bottom: 1px solid #edf5e1
}

/*btn-SchoolList*/

.btn-SchoolList {
    color: #000000;
    background-color: #FFFFFF;
    margin-bottom: 3px;
    letter-spacing: 2px;
    border: 1.5px solid #c3e4f6;
    font-weight: bold;
}

    .btn-SchoolList:hover,
    .btn-SchoolList:focus,
    .btn-SchoolList:active,
    .btn-SchoolList.active,
    .open .dropdown-toggle.btn-SchoolList {
        color: #000000;
        background-color: #c3e4f6;
        border-color: #c3e4f6;
        box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
    }

    .btn-SchoolList:active,
    .btn-SchoolList.active,
    .open .dropdown-toggle.btn-SchoolList {
        background-image: none;
    }

    .btn-SchoolList.disabled,
    .btn-SchoolList[disabled],
    fieldset[disabled] .btn-SchoolList,
    .btn-SchoolList.disabled:hover,
    .btn-SchoolList[disabled]:hover,
    fieldset[disabled] .btn-SchoolList:hover,
    .btn-SchoolList.disabled:focus,
    .btn-SchoolList[disabled]:focus,
    fieldset[disabled] .btn-SchoolList:focus,
    .btn-SchoolList.disabled:active,
    .btn-SchoolList[disabled]:active,
    fieldset[disabled] .btn-SchoolList:active,
    .btn-SchoolList.disabled.active,
    .btn-SchoolList[disabled].active,
    fieldset[disabled] .btn-SchoolList.active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }

.navbar-phone {
    background-color: #FFFFFF;
    border-color: #FFFFFF;
    border-bottom: 1px solid #E6EB42;
}

    .navbar-phone .navbar-brand {
        color: #ffffff;
    }

        .navbar-phone .navbar-brand:hover,
        .navbar-phone .navbar-brand:focus {
            color: #ffffff;
            background-color: none;
        }

    .navbar-phone .navbar-text {
        color: #dddddd;
    }

    .navbar-phone .navbar-nav > li > a {
        color: #ffffff;
    }

        .navbar-phone .navbar-nav > li > a:hover,
        .navbar-phone .navbar-nav > li > a:focus {
            color: #ffffff;
            background-color: #178acc;
        }

    .navbar-phone .navbar-nav > .active > a,
    .navbar-phone .navbar-nav > .active > a:hover,
    .navbar-phone .navbar-nav > .active > a:focus {
        color: #ffffff;
        background-color: #178acc;
    }

    .navbar-phone .navbar-nav > .disabled > a,
    .navbar-phone .navbar-nav > .disabled > a:hover,
    .navbar-phone .navbar-nav > .disabled > a:focus {
        color: #dddddd;
        background-color: transparent;
    }

    .navbar-phone .btn-navbar {
        border-color: #FFFFFF;
    }

        .navbar-phone .btn-navbar:hover,
        .navbar-phone .btn-navbar:focus {
            background-color: #fff;
        }

        .navbar-phone .btn-navbar .icon-bar {
            background-color: #000000;
        }

    .navbar-phone .navbar-collapse,
    .navbar-phone .navbar-form {
        border-color: #1995dc;
    }

    .navbar-phone .navbar-nav > .open > a,
    .navbar-phone .navbar-nav > .open > a:hover,
    .navbar-phone .navbar-nav > .open > a:focus {
        background-color: #178acc;
        color: #ffffff;
    }

@media (max-width: 767px) {
    .navbar-phone .navbar-nav .open .dropdown-menu > li > a {
        color: #ffffff;
    }

        .navbar-phone .navbar-nav .open .dropdown-menu > li > a:hover,
        .navbar-phone .navbar-nav .open .dropdown-menu > li > a:focus {
            color: #ffffff;
            background-color: #178acc;
        }

    .navbar-phone .navbar-nav .open .dropdown-menu > .active > a,
    .navbar-phone .navbar-nav .open .dropdown-menu > .active > a:hover,
    .navbar-phone .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #ffffff;
        background-color: #178acc;
    }

    .navbar-phone .navbar-nav .open .dropdown-menu > .disabled > a,
    .navbar-phone .navbar-nav .open .dropdown-menu > .disabled > a:hover,
    .navbar-phone .navbar-nav .open .dropdown-menu > .disabled > a:focus {
        color: #dddddd;
        background-color: transparent;
    }
}

.navbar-phone .navbar-link {
    color: #ffffff;
}

    .navbar-phone .navbar-link:hover {
        color: #ffffff;
    }

.navbar-phone .btn-link {
    color: #ffffff;
}

    .navbar-phone .btn-link:hover,
    .navbar-phone .btn-link:focus {
        color: #ffffff;
    }

    .navbar-phone .btn-link[disabled]:hover,
    fieldset[disabled] .navbar-phone .btn-link:hover,
    .navbar-phone .btn-link[disabled]:focus,
    fieldset[disabled] .navbar-phone .btn-link:focus {
        color: #dddddd;
    }

.btn-navbar {
    float: left !important;
    margin-top: 12px;
    margin-bottom: 8px;
    background-color: #fff;
}

.btn-logo {
    float: left !important;
    position: relative;
    margin-left: 10px;
}

.btn-font-school {
    margin-top: 12px;
    margin-right: 5px;
    color: #000000;
    font-size: 24px;
    font-weight: bold;
}

.btn-logo-layout,
.btn-logo-school,
.btn-font-school {
    float: left !important;
    position: relative;
}

    .btn-logo > img,
    .btn-logo-layout > img,
    .btn-logo-school > img {
        margin-top: 3px;
        margin-right: 5px;
        margin-bottom: 3px;
        max-height: 47px;
    }

.line-left {
    float: left !important;
    background-color: #d0d0d0;
    height: 37px;
    width: 1px;
    margin-top: 10px;
    margin-bottom: 5px;
    margin-left: 10px;
    margin-right: 10px
}

.line-right {
    float: right !important;
    background-color: #d0d0d0;
    height: 37px;
    width: 1px;
    margin-top: 10px;
    margin-bottom: 5px;
    margin-left: 10px;
    margin-right: 10px
}

.btn-User {
    float: right !important;
    margin-top: 12px;
    margin-right: 25px;
    background-color: #fff;
}

@media (max-width: 370px) {
    .btn-logo-school,
    .line-right,
    .line-left {
        display: none !important;
    }

        .btn-logo > img,
        .btn-logo-layout > img,
        .btn-logo-school > img {
            margin-right: 10px;
            max-height: 40px;
        }
}

.btn-navbar .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px;
}

    .btn-navbar .icon-bar + .icon-bar {
        margin-top: 4px;
    }

/* for  IE8 ~ IE10 */
@media screen\0 {
    .btn-navbar {
        /*display: none\9;*/
    }
}

@media (min-width: 767px) {
    .btn-navbar {
        display: none;
    }
}

.navPhone {
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

    .navPhone:before,
    .navPhone:after {
        display: table;
        content: " ";
    }

    .navPhone:after {
        clear: both;
    }

    .navPhone:before,
    .navPhone:after {
        display: table;
        content: " ";
    }

    .navPhone:after {
        clear: both;
    }

    .navPhone > li {
        position: relative;
        display: block;
        font-size: 21px;
        font-weight: bold;
    }

        .navPhone > li > a {
            position: relative;
            display: block;
            padding: 10px 15px;
            letter-spacing: 2px;
        }

            .navPhone > li > a:hover,
            .navPhone > li > a:focus {
                text-decoration: none;
                background-color: #eeeeee;
            }

        .navPhone > li.disabled > a {
            background-color: #e5e5e5;
        }

            .navPhone > li.disabled > a:hover,
            .navPhone > li.disabled > a:focus {
                background-color: #e5e5e5;
                text-decoration: none;
                cursor: not-allowed;
                background-color: transparent;
            }

    .navPhone .open > a,
    .navPhone .open > a:hover,
    .navPhone .open > a:focus {
        background-color: #eeeeee;
    }

    .navPhone .open .caret {
        border-top: 4px solid #008500;
    }

    .navPhone .navPhone-divider {
        height: 1px;
        margin: 9px 0;
        overflow: hidden;
        background-color: #e5e5e5;
    }

    .navPhone > li > a > img {
        max-width: none;
    }

.iconPhone {
    margin-right: 5px;
    font-size: 1em;
}

.MyName {
    margin-top: 5px;
    margin-left: 5px;
    font-size: 21px;
    font-weight: bold;
}

.Title {
    white-space: nowrap;
    font-weight: bold;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .Title {
        font-size: 22pt\9;
    }

    .navbar-Cach {
        width: 174px\9;
        min-width: 174px\9;
        padding-left: 55px\9;
        text-align: center\9;
        background-position-y: center\9;
        background-position-x: left\9;
        background-repeat: no-repeat\9;
    }

    .navbar-Cach-font {
        display: none !important\9;
    }

    ._Layout_RightBar {
        display: block !important\9;
    }
}

@media (min-width: 1281px) {
    .Title {
        font-size: 22pt;
    }

    .navbar-Cach {
        width: 174px;
        min-width: 174px;
        padding-left: 55px;
        text-align: center;
        background-position-y: center;
        background-position-x: left;
        background-repeat: no-repeat;
    }

    .navbar-Cach-font {
        display: none !important;
    }

    ._Layout_RightBar {
        display: block !important;
    }
}

@media (min-width: 990px) and (max-width: 1280px) {
    .Title {
        font-size: 21pt;
    }

    .navbar-Cach {
        display: none !important;
    }

    .navbar-Cach-font {
        display: inline !important;
    }

    ._Layout_RightBar {
        display: block !important;
    }
}

@media (max-width: 989px) {
    .Title {
        font-size: 16pt;
    }

    .navbar-Cach {
        display: none !important;
    }

    .navbar-Cach-font {
        display: inline !important;
    }

    ._Layout_RightBar {
        display: none !important;
    }
}
/*小於 361px SHow*/
@media screen and (min-width: 361px) {
    .Div-EZ-ArtGallery-pointer {
        cursor: pointer;
        position: relative;
        width: 129px;
        height: 170px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book {
        width: 129px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img {
        top: 22px;
        left: 24px;
        position: absolute;
        width: 89px;
        height: 76px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 76px;
    }

    .Div-EZ-ArtGallery-img {
        max-width: 88px;
        max-height: 76px
    }

    .Div-EZ-ArtGallery-text {
        width: 129px;
    }
}

.form-control-Login {
    width: 180px;
    height: 35px;
    display: inline;
    font-size: 20px;
    line-height: 1.428571429;
    color: #555555;
    vertical-align: middle;
    background-color: #ffffff;
    border: 1px solid #f8b4af;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.btn-default-Login {
    color: #333333;
    background-color: #FEE8E9;
    border-color: #f8b4af;
    font-size: 22px;
    font-weight: bold;
    letter-spacing: 20px;
    padding-left: 30px;
    padding-right: 10px;
}

    .btn-default-Login:hover,
    .btn-default-Login:focus,
    .btn-default-Login:active,
    .btn-default-Login.active,
    .open .dropdown-toggle.btn-default-Login {
        color: #333333;
        background-color: #f8b4af;
        border-color: #f8b4af;
    }

    .btn-default-Login:active,
    .btn-default-Login.active,
    .open .dropdown-toggle.btn-default-Login {
        background-image: none;
    }

    .btn-default-Login.disabled,
    .btn-default-Login[disabled],
    fieldset[disabled] .btn-default-Login,
    .btn-default-Login.disabled:hover,
    .btn-default-Login[disabled]:hover,
    fieldset[disabled] .btn-default-Login:hover,
    .btn-default-Login.disabled:focus,
    .btn-default-Login[disabled]:focus,
    fieldset[disabled] .btn-default-Login:focus,
    .btn-default-Login.disabled:active,
    .btn-default-Login[disabled]:active,
    fieldset[disabled] .btn-default-Login:active,
    .btn-default-Login.disabled.active,
    .btn-default-Login[disabled].active,
    fieldset[disabled] .btn-default-Login.active {
        background-color: #ffffff;
        border-color: #cccccc;
    }

.Div-EZ-Right {
    text-align: right;
    padding-top: 15px;
    padding-bottom: 15px;
    padding-right: 35px;
    padding-left: 15px;
}

@media print {
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
        float: left;
    }

    .col-sm-12 {
        width: 100%;
    }

    .col-sm-11 {
        width: 91.66666667%;
    }

    .col-sm-10 {
        width: 83.33333333%;
    }

    .col-sm-9 {
        width: 75%;
    }

    .col-sm-8 {
        width: 66.66666667%;
    }

    .col-sm-7 {
        width: 58.33333333%;
    }

    .col-sm-6 {
        width: 50%;
    }

    .col-sm-5 {
        width: 41.66666667%;
    }

    .col-sm-4 {
        width: 33.33333333%;
    }

    .col-sm-3 {
        width: 25%;
    }

    .col-sm-2 {
        width: 16.66666667%;
    }

    .col-sm-1 {
        width: 8.33333333%;
    }

    .col-sm-pull-12 {
        right: 100%;
    }

    .col-sm-pull-11 {
        right: 91.66666667%;
    }

    .col-sm-pull-10 {
        right: 83.33333333%;
    }

    .col-sm-pull-9 {
        right: 75%;
    }

    .col-sm-pull-8 {
        right: 66.66666667%;
    }

    .col-sm-pull-7 {
        right: 58.33333333%;
    }

    .col-sm-pull-6 {
        right: 50%;
    }

    .col-sm-pull-5 {
        right: 41.66666667%;
    }

    .col-sm-pull-4 {
        right: 33.33333333%;
    }

    .col-sm-pull-3 {
        right: 25%;
    }

    .col-sm-pull-2 {
        right: 16.66666667%;
    }

    .col-sm-pull-1 {
        right: 8.33333333%;
    }

    .col-sm-pull-0 {
        right: auto;
    }

    .col-sm-push-12 {
        left: 100%;
    }

    .col-sm-push-11 {
        left: 91.66666667%;
    }

    .col-sm-push-10 {
        left: 83.33333333%;
    }

    .col-sm-push-9 {
        left: 75%;
    }

    .col-sm-push-8 {
        left: 66.66666667%;
    }

    .col-sm-push-7 {
        left: 58.33333333%;
    }

    .col-sm-push-6 {
        left: 50%;
    }

    .col-sm-push-5 {
        left: 41.66666667%;
    }

    .col-sm-push-4 {
        left: 33.33333333%;
    }

    .col-sm-push-3 {
        left: 25%;
    }

    .col-sm-push-2 {
        left: 16.66666667%;
    }

    .col-sm-push-1 {
        left: 8.33333333%;
    }

    .col-sm-push-0 {
        left: auto;
    }

    .col-sm-offset-12 {
        margin-left: 100%;
    }

    .col-sm-offset-11 {
        margin-left: 91.66666667%;
    }

    .col-sm-offset-10 {
        margin-left: 83.33333333%;
    }

    .col-sm-offset-9 {
        margin-left: 75%;
    }

    .col-sm-offset-8 {
        margin-left: 66.66666667%;
    }

    .col-sm-offset-7 {
        margin-left: 58.33333333%;
    }

    .col-sm-offset-6 {
        margin-left: 50%;
    }

    .col-sm-offset-5 {
        margin-left: 41.66666667%;
    }

    .col-sm-offset-4 {
        margin-left: 33.33333333%;
    }

    .col-sm-offset-3 {
        margin-left: 25%;
    }

    .col-sm-offset-2 {
        margin-left: 16.66666667%;
    }

    .col-sm-offset-1 {
        margin-left: 8.33333333%;
    }

    .col-sm-offset-0 {
        margin-left: 0%;
    }
}

.progress-bar-gray {
    background-color: #9D9D9D;
}

.progress-striped .progress-bar-gray {
    background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.btn-default-buy {
    color: #333333;
    background-color: #ffffff;
    border-color: #cccccc;
}

    .btn-default-buy:hover,
    .btn-default-buy:focus,
    .btn-default-buy:active,
    .btn-default-buy.active,
    .open .dropdown-toggle.btn-default-buy {
        color: #333333;
        background-color: #ebebeb;
        border-color: #adadad;
    }

    .btn-default-buy:active,
    .btn-default-buy.active,
    .open .dropdown-toggle.btn-default-buy {
        background-image: none;
    }

    .btn-default-buy.disabled,
    .btn-default-buy[disabled],
    fieldset[disabled] .btn-default-buy,
    .btn-default-buy.disabled:hover,
    .btn-default-buy[disabled]:hover,
    fieldset[disabled] .btn-default-buy:hover,
    .btn-default-buy.disabled:focus,
    .btn-default-buy[disabled]:focus,
    fieldset[disabled] .btn-default-buy:focus,
    .btn-default-buy.disabled:active,
    .btn-default-buy[disabled]:active,
    fieldset[disabled] .btn-default-buy:active,
    .btn-default-buy.disabled.active,
    .btn-default-buy[disabled].active,
    fieldset[disabled] .btn-default-buy.active {
        background-color: #ffffff;
        border-color: #cccccc;
    }

.css-table {
    display: table;
    border-collapse: collapse;
    width: 100%
}

    .css-table .thead {
        display: table-header-group;
    }

    .css-table .tbody {
        display: table-row-group;
    }

    .css-table .tr {
        display: table-row;
    }

    .css-table .th, .css-table .td {
        display: table-cell;
        padding-left: 3px;
    }

@media screen and (min-width: 361px) {
    .Div-EZ-ArtGallery-pointer {
        cursor: pointer;
        position: relative;
        width: 129px;
        height: 170px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book {
        width: 129px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img {
        top: 22px;
        left: 24px;
        position: absolute;
        width: 89px;
        height: 76px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 76px;
    }

    .Div-EZ-ArtGallery-img {
        max-width: 88px;
        max-height: 76px
    }

    .Div-EZ-ArtGallery-text {
        width: 129px;
    }
}

/*小於360px SHow*/
@media screen and (max-width: 360px) {
    .Div-EZ-ArtGallery-pointer {
        cursor: pointer;
        position: relative;
        width: 100px;
        height: 131px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book {
        width: 100px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img {
        top: 17px;
        left: 18px;
        position: absolute;
        width: 69px;
        height: 59px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 59px;
    }

    .Div-EZ-ArtGallery-img {
        max-width: 69px;
        max-height: 59px;
    }

    .Div-EZ-ArtGallery-text {
        width: 100px;
    }
}

@media screen and (min-width: 1091px) {
    .Div-EZ-ArtGallery-pointer-all {
        cursor: pointer;
        position: relative;
        width: 170px;
        height: 165px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book-all {
        width: 170px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img-all {
        cursor: pointer;
        top: 15px;
        left: 20px;
        position: absolute;
        width: 130px;
        height: 110px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 110px;
    }

    .Div-EZ-ArtGallery-img-all {
        max-width: 120px;
        max-height: 105px;
    }

    .Div-EZ-ArtGallery-text-all {
        width: 120px;
    }

    .Div-EZ-ArtGallery-icon {
        top: 127px;
        left: 27px;
        position: absolute;
        z-index: 5;
        width: 32px;
        height: 46px
    }
}

/*小於 1090px SHow*/
@media screen and (max-width: 1090px) {
    .Div-EZ-ArtGallery-pointer-all {
        cursor: pointer;
        position: relative;
        width: 136px;
        height: 132px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book-all {
        width: 136px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img-all {
        cursor: pointer;
        top: 12px;
        left: 16px;
        position: absolute;
        width: 104px;
        height: 88px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 88px;
    }

    .Div-EZ-ArtGallery-img-all {
        max-width: 96px;
        max-height: 84px;
    }

    .Div-EZ-ArtGallery-text-all {
        width: 96px;
    }

    .Div-EZ-ArtGallery-icon {
        top: 102px;
        left: 22px;
        position: absolute;
        z-index: 5;
        width: 25px;
        height: 36px
    }
}

/*小於 361px SHow*/
@media screen and (max-width: 361px) {
    .Div-EZ-ArtGallery-pointer-all {
        cursor: pointer;
        position: relative;
        width: 82px;
        height: 79px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book-all {
        width: 82px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img-all {
        cursor: pointer;
        top: 7px;
        left: 10px;
        position: absolute;
        width: 62px;
        height: 53px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 53px;
    }

    .Div-EZ-ArtGallery-img-all {
        max-width: 62px;
        max-height: 53px;
    }

    .Div-EZ-ArtGallery-text-all {
        width: 77px;
    }

    .Div-EZ-ArtGallery-icon {
        top: 43px;
        left: 13px;
        position: absolute;
        z-index: 5;
        width: 22px;
        height: 31px
    }
}

#DivAddButton {
    position: fixed; /*固定在網頁上不隨卷軸移動，若要隨卷軸移動用absolute*/
    bottom: 25px;
    right: -80px; /*設置水平位置，依所放的內容多寡需要自行手動調整*/
    background-color: rgba(255,255,244,0.5);
    padding: 12px 10px 15px 10px;
    z-index: 99;
    border-radius: 10px; /*圓角*/
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
}

    #DivAddButton:hover { /*當滑鼠移至此區塊時，伸縮區塊*/
        right: -10px;
    }

    #DivAddButton #title {
        padding-right: 5px; /*讓標題與連結中間有空隙*/
    }

.use-absolute {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
    top: 40%;
    right: 0;
}

.use-absoluteDiv {
    position: absolute;
    width: 60%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
}

.rwd-table {
    background: #fff;
    overflow: hidden;
}

    .rwd-table tr:nth-of-type(2n) {
        background: #eee;
    }

    .rwd-table th,
    .rwd-table td {
        margin: 0.5em 1em;
    }

.rwd-table {
    min-width: 100%;
}

    .rwd-table th {
        display: none;
    }

    .rwd-table td {
        display: block;
    }

        .rwd-table td:before {
            content: attr(data-th) " : ";
            font-weight: bold;
            width: 6.5em;
            display: inline-block;
        }

    .rwd-table th, .rwd-table td {
        text-align: left;
    }

        .rwd-table th, .rwd-table td:before {
            color: #D20B2A;
            font-weight: bold;
            font-size: 18px
        }

@media (min-width: 480px) {
    .rwd-table td:before {
        display: none;
    }

    .rwd-table th, .rwd-table td {
        display: table-cell;
        padding: 0.25em 0.5em;
    }

        .rwd-table th:first-child,
        .rwd-table td:first-child {
            padding-left: 0;
        }

        .rwd-table th:last-child,
        .rwd-table td:last-child {
            padding-right: 0;
        }

    .rwd-table th,
    .rwd-table td {
        padding: 1em !important;
        font-size: 13px
    }
}

#table-breakpoint th,
#table-breakpoint td {
    padding: 1em !important;
    font-size: 18px
}

@media only screen and (max-width: 568px) {
    #table-breakpoint thead {
        display: none;
    }

    #table-breakpoint tbody td {
        border: none !important;
        display: block;
        vertical-align: top;
    }

        #table-breakpoint tbody td:before {
            content: attr(data-th) ": ";
            display: inline-block;
            font-weight: bold;
            width: 6.5em;
        }

        #table-breakpoint tbody td.bt-hide {
            display: none;
        }

    #table-breakpoint th,
    #table-breakpoint td {
        padding: 1em !important;
        font-size: 13px
    }
}

/* Others */

.ui-icon {
    text-indent: inherit !important;
}

/* href disabled */
.not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: black;
    opacity: 0.5;
}

.btn-social-lg {
    position: relative;
    padding-left: 55px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

    .btn-social-lg > :first-child {
        position: absolute;
        left: 2px;
        top: 2px;
        bottom: 0;
        width: 40px;
        line-height: 46px;
        font-size: 1.6em;
        text-align: center;
        /*border-right: 1px solid rgba(0,0,0,0.2);*/
    }

.btn-dropbox {
    color: #fff;
    background-color: #009eaf;
    border-color: rgba(0,0,0,0.2);
}

.btn-social {
    position: relative;
    padding-left: 44px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

    .btn-social > :first-child {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 32px;
        line-height: 34px;
        font-size: 1.6em;
        text-align: center;
        /*border-right: 1px solid rgba(0,0,0,0.2);*/
    }

.btn-social-lg {
    position: relative;
    padding-left: 5px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

    .btn-social-lg > :first-child {
        position: absolute;
        left: 2px;
        top: 2px;
        bottom: 0;
        width: 40px;
        line-height: 46px;
        font-size: 1.6em;
        text-align: center;
        /*border-right: 1px solid rgba(0,0,0,0.2);*/
    }

.text-nowrap {
    white-space: nowrap !important;
}

.form-control-required {
    background-color: #ffffe7;
}

.control-label-required::after {
    content: "*";
    color: #E04;
}

.hr-line-dashed {
    border: 1px dashed #ddd;
}

label.btn span {
    font-size: 1.5em;
}

label input[type="radio"] ~ i.fa.fa-circle-o {
    color: #c8c8c8;
    display: inline;
}

label input[type="radio"] ~ i.fa.fa-dot-circle-o {
    display: none;
}

label input[type="radio"]:checked ~ i.fa.fa-circle-o {
    display: none;
}

label input[type="radio"]:checked ~ i.fa.fa-dot-circle-o {
    color: #7AA3CC;
    display: inline;
}

label:hover input[type="radio"] ~ i.fa {
    color: #7AA3CC;
}

label input[type="checkbox"] ~ i.fa.fa-square-o {
    color: #c8c8c8;
    display: inline;
}

label input[type="checkbox"] ~ i.fa.fa-check-square-o {
    display: none;
}

label input[type="checkbox"]:checked ~ i.fa.fa-square-o {
    display: none;
}

label input[type="checkbox"]:checked ~ i.fa.fa-check-square-o {
    color: #7AA3CC;
    display: inline;
}

label:hover input[type="checkbox"] ~ i.fa {
    color: #7AA3CC;
}

div[data-toggle="buttons"] label.active {
    color: #7AA3CC;
}

div[data-toggle="buttons"] label {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 2em;
    text-align: left;
    white-space: nowrap;
    vertical-align: top;
    cursor: pointer;
    background-color: none;
    border: 0px solid #c8c8c8;
    border-radius: 3px;
    color: #c8c8c8;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}

    div[data-toggle="buttons"] label:hover {
        color: #7AA3CC;
    }

    div[data-toggle="buttons"] label:active, div[data-toggle="buttons"] label.active {
        -webkit-box-shadow: none;
        box-shadow: none;
    }

.print-only {
    display: none;
}

@media print {
    .no-print, .no-print * {
        display: none !important;
    }

    .print-only {
        display: block;
    }
}

@media (min-width: 767px) {
    .text-center-Mobile-left {
        text-align: center;
    }
}

@media (max-width: 767px) {
    .text-center-Mobile-left {
        text-align: left;
    }
}

table.bt tfoot th:first-of-type:before,
table.bt tfoot th:first-of-type .bt-content,
table.bt tfoot td:first-of-type:before,
table.bt tfoot td:first-of-type .bt-content,
table.bt tbody td:first-of-type:before,
table.bt tbody td:first-of-type .bt-content {
    padding-top: 5px;
}

table.bt tfoot th:last-of-type:before,
table.bt tfoot th:last-of-type .bt-content,
table.bt tfoot td:last-of-type:before,
table.bt tfoot td:last-of-type .bt-content,
table.bt tbody td:last-of-type:before,
table.bt tbody td:last-of-type .bt-content {
    padding-bottom: 5px;
}