﻿@model GameCashQueryViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();
}
<link href="~/Content/css/childrens-month.css" rel="stylesheet" />
<style type="text/css">

    .offline {
        background: red;
        color: aliceblue;
    }
</style>
<link href="~/Content/styles/animate.min.css" rel="stylesheet" />
<link rel="stylesheet" href="~/Content/styles/challenge-pass-check.min.css">
@using (Html.BeginForm("CashQuery", "Game", FormMethod.Post, new { id = "form1", name = "form1", @AutoComplete = "Off" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)

    <div id="MainView">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel with-nav-tabs panel-success" id="panel">
                    <div class="panel-heading">
                        <h1>過關(點數)查詢 <strong id="status"></strong></h1>
                    </div>
                    <div class="panel-body">
                        <div class="">
                            <div class="input-group input-group-lg">
                                <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                @Html.EditorFor(model => model.GameUserID, new { htmlAttributes = new { @class = "form-control", @type = "number", @min = "0", @max = "9999999999", @placeholder = "請刷數位學生證或臨時卡", @onKeyPress = "call(event,this);" } })
                            </div>
                            @Html.ValidationMessageFor(model => model.GameUserID, "", new { @class = "text-danger" })
                        </div>
                        <br />
                        <div class="form-group text-center">
                            <h4>請感應數位學生證或臨時卡。</h4>
                            <br />
                            <div>
                                <img src="~/Content/img/Asset1.png" style="height:100px;padding-right:10px" />
                                <img src="~/Content/img/Asset2.png" style="height:100px;" />
                            </div>
                        </div>
                    </div>
                </div>
                @*<div style="margin: 0px auto;text-align:center">
                        <img id="ViewImg" src="~/Content/img/qr_code_Cash.svg" style="margin: 0px auto;max-height:calc(70vh);" class="img-responsive" />
                    </div>*@
                <div style="margin: 0px auto;text-align:center">
                    <img id="ViewImg" src="@Model.LEVEL_IMG_PATH" class="img-responsive" style="margin: 0 auto;" />
                </div>
            </div>
        </div>

        <div id="DivAddButton">
            <i id="title" class="fa fa-arrow-left fa-3x"></i>
            <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
        </div>
    </div>
}
<!-- 過關動畫&資訊-->
<div id="challenge-panel" class="challenge-panel-bg">
    <div id="challenge-ribbon" class="challenge-ribbon animated"></div>
    <div id="challenge-info" class="challenge-info animated"></div>
</div>
<div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        @*<strong style="color:#80b4fb">查詢中…</strong>*@
        <div class="challenge-loading" id="loading">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" />
            <strong>查詢中…</strong>
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

        $(document).ready(function () {

               $(document).on('keypress', function (e) {
                if (e.which == 13) {

                    var GameUserID = $("#@Html.IdFor(m=>m.GameUserID)");

                    if ($('#loading').is(":hidden")) {

                        if (GameUserID.is(":focus")==false) {
                            GameUserID.focus();
                        }
                    }
                    else {
                        return false;
                    }

                }
            });

               $("#@Html.IdFor(m=>m.GameUserID)").focus();
               Wa_SetImgAutoSize()

               IsfocusInput()

               if (typeof (navigator.onLine) != "undefined") {

                   window.addEventListener('online', updateOnlineStatus);
                   window.addEventListener('offline', updateOnlineStatus);

                   updateOnlineStatus()
               }

        });

        function updateOnlineStatus(event) {

            if (navigator.onLine) {
                $('#status').removeClass('offline')
                $('#status').html('')
                $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', false);
                $('#@Html.IdFor(m=>m.GameUserID)').val('')
            }
            else {
                $('#status').addClass('offline')
                $('#status').html('(網路無訊號)')
                $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', true);
            }
        }

        function IsfocusInput() {
            if ( $("#@Html.IdFor(m=>m.GameUserID)").is(':focus') == false) {
                 $("#@Html.IdFor(m=>m.GameUserID)").focus()
            }

            setTimeout("IsfocusInput()", 5000);
        }

       function Wa_SetImgAutoSize() {
               var img = $('#ViewImg'); //獲取圖片

               var windowHeight = $(window).height();
               var panelHeight = $("#panel").height();

               var MaxHeight = (windowHeight - panelHeight - 30); //設置圖片高度界限

               if (MaxHeight>200) {
                   var HeightWidth = img.offsetHeight / img.offsetWidth; //設置高寬比
                   var WidthHeight = img.offsetWidth / img.offsetHeight; //設置寬高比

                   img.height = MaxHeight;
                   img.width = MaxHeight * WidthHeight;

                   (img).css({
                       "width": img.width, "height": img.height
                   });
               }
           }

        function OnBack() {
            $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")
             $(targetFormID).submit();
         }

        function OnKeyinUse() {
            $('#MainView').fadeOut(2000)
            $('#loading').fadeIn(1000
                , function () {

                    $('#loading').hide();
                    $('#challenge-panel').css({ 'display': 'flex' });
                    $('#challenge-ribbon').css({ 'display': 'flex' });
                    $('#challenge-info').addClass('challenge-info-success').removeClass('challenge-info-failed').text('~ 查詢成功 ~');

                }
            )
            setTimeout(function () {

                GoToCashQueryDataList();
            }, 2000);
        }

        function GoToCashQueryDataList() {

            $(targetFormID).attr("action", "@Url.Action("CashQueryDataList", "Game")")
            $(targetFormID).submit()
        }

        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                event.preventDefault();

                var GameUserID = $('#@Html.IdFor(m=>m.GameUserID)');
                GameUserID.prop('readonly', true);

                var OK = false;

                if (GameUserID.val().length == 10) {
                    OK = true;
                }
                else if (GameUserID.val().length >= 20)
                {
                    var ThisVal = GameUserID.val().substring(0, 10);
                    GameUserID.val(ThisVal)
                    OK = true;
                }
                else {
                    GameUserID.val('')
                }

                if (OK) {
                    setTimeout(function () {
                        OnKeyinUse();
                    });
                }
                else {
                    GameUserID.prop('readonly', false);
                }

            }
        }
    </script>
}