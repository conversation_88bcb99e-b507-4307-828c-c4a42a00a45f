﻿@model GameBuskerLikeViewModel
@{
    Layout = null;
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    Uri contextUri = HttpContext.Current.Request.Url;

    var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
    contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port);

    string ValueStr = baseUri + Url.Action("BuskerLikeView", "Game").ToString() + $"?GAME_NO={Model.GAME_NO}&UnApply=true";

    string ValueImg = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GLike.png");

    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();
}
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    <link rel="stylesheet" href="~/assets/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="~/assets/css/animate.css">
    <link rel="stylesheet" href="~/assets/css/styles.css">

    <link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />
    <link href="~/Content/css/jquery.validationEngine.css" rel="stylesheet" />
    <link href="~/Content/css/bootstrap.css" rel="stylesheet" />

    <link href="~/Content/css/EzCss.css?@DateNowStr" rel="stylesheet" />
    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    <script src="~/Scripts/modernizr-2.6.2.js"></script>

    <script src=@Url.Content("~/Scripts/jquery-3.6.4.min.js")></script>
    <script src="~/Scripts/jquery-ui.min.js"></script>
    <script src="~/Scripts/ECoolBase.js"></script>

    <script src="~/Scripts/bootstrap.js"></script>
    <script src="~/Scripts/respond.js"></script>

    @*
        <script src="~/assets/js/jquery.min.js"></script>
        <script src="~/assets/bootstrap/js/bootstrap.min.js"></script>*@

    <script src="~/Scripts/jquery.simple.timer.js?ver=2"></script>
    <style type="text/css">
        #stage_artist_photo {
            background-image: url("@Model.TITLE_IMG");
        }
    </style>
</head>

<body>
    <div class="use-absolute" id="ErrorDiv">
        <div class="use-absoluteDiv">
            <div class="alert alert-danger" role="alert">
                <h1>
                    <i class="fa fa-exclamation-circle"></i>
                    <strong id="ErrorStr">畫面載入中…</strong>
                </h1>
            </div>
        </div>
    </div>
    <span class="eachtimer" data-seconds-left=10 style="display:none"></span>
    <span class="Likeeachtimer" data-seconds-left=5 style="display:none"></span>

    @using (Html.BeginForm("BuskerLikeView", "Game", FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off" }))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.GAME_NO)
        @Html.HiddenFor(m => m.WhereTITLE_SHOW_ID)
        @Html.HiddenFor(m => m.Show_LIKE_COUNT)
        @Html.HiddenFor(m => m.NOW_LIKE_COUNT)
        @Html.HiddenFor(m => m.UnApply)
    }

    @if (!string.IsNullOrWhiteSpace((string)(TempData["StatusMessage"] ?? "")))
    {
        <div id="MainView">
            <div style="height:calc(10vh)"></div>
            <div class="row">
                <div class="col-md-6 col-md-offset-3">
                    <div class="login-panel panel panel-danger">
                        <div class="panel-heading">
                            <h1 class="panel-title text-center">訊息</h1>
                        </div>
                        <div class="panel-body">
                            <h1 style="color:red;text-align:center">
                                <strong> @Html.Raw(HttpUtility.HtmlDecode((string)TempData["StatusMessage"]))</strong>
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    }
    else
    {

        <div id="MainView" class="Ezcontainer">

            <div class="Ezcontainer d-flex flex-row" id="activity_stage_c1" style="background-image:url('@Url.Content("~/assets/img/activity_stage_2.png")');background-position:center;background-size:contain;background-repeat:no-repeat;margin-top:0;padding:0;background-color:rgb(167,27,35);">
                <div class="d-flex justify-content-center align-items-center" style="width:20%;">
                    <div class="stage_firework_b1 d-flex flex-column justify-content-center align-items-center align-content-center" id="stage_frame_like-1">
                        <div class="row" style="width:100%;height:50%;margin:0;padding:0;">
                            <div class="col" style="padding:0;margin:0;width:100%;height:100%;">
                                <div style="background-image:url('@Url.Content("~/assets/img/click_txt_like.png")');background-position:bottom;background-size:contain;background-repeat:no-repeat;width:100%;height:96%;">
                                </div>
                            </div>
                        </div>
                        <div class="row" style="width:100%;height:50%;margin:0;padding:0;">
                            <div id="likeLeft" class="col d-flex align-self-center" style="padding:0;margin:0;width:100%;height:100%;">
                                <div class="Addlike" style="background-image:url('@Url.Content("~/assets/img/click_txt_like_add.png")');">
                                </div>
                                <div class="likeNum" id="NumLeft_1"></div>
                                <div class="likeNum" id="NumLeft_2"></div>
                                <div class="likeNum" id="NumLeft_3"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-center align-items-start align-content-center" style="width:60%;">
                    <div id="stage_frame_top"></div>
                    <div id="stage_frame_logo"></div>
                    <div class="d-flex justify-content-center align-items-center align-content-center" id="stage_frame_root">
                        <div id="stage_artist_frame" style="margin:0;padding:0;"></div>
                        <div id="stage_artist_photo" class="rounded-50"></div>
                    </div>
                </div>
                <div class="stage_firework_b1 d-flex justify-content-center align-items-center" style="width:20%;">
                    <div class="d-flex flex-column justify-content-center align-items-center align-content-center" id="stage_frame_like-1">
                        <div class="row" style="width:100%;height:50%;margin:0;padding:0;">
                            <div class="col" style="padding:0;margin:0;width:100%;height:100%;">
                                <div style="background-image:url('@Url.Content("~/assets/img/click_txt_like.png")');background-position:bottom;background-size:contain;background-repeat:no-repeat;width:100%;height:96%;">
                                </div>
                            </div>
                        </div>
                        <div class="row" style="width:100%;height:50%;margin:0;padding:0;">
                            <div id="likeRight" class="col d-flex align-self-center" style="padding:0;margin:0;width:100%;height:100%;">
                                <div class="Addlike" style="background-image:url('@Url.Content("~/assets/img/click_txt_like_add.png")');">
                                </div>
                                <div class="likeNum" id="NumRight_1"></div>
                                <div class="likeNum" id="NumRight_2"></div>
                                <div class="likeNum" id="NumRight_3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="panel with-nav-tabs panel-info">
                        <div class="panel-heading">
                            <h3>表演名稱:@Model.TITLE_SHOW_NAME</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div id="MainDiv">
                                        <br />
                                        @if (Model.UnApply == false)
                                        {
                                            <div class="form-group">
                                                <div class="input-group input-group-lg">
                                                    <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                                    @Html.EditorFor(model => model.GameUserID, new { htmlAttributes = new { @class = "form-control", @placeholder = "Username", @onKeyPress = "call(event,this);" } })
                                                </div>
                                                @Html.ValidationMessageFor(model => model.GameUserID, "", new { @class = "text-danger" })
                                            </div>
                                            <div class="form-group text-center">
                                                <h4>請用數位學生證感應；來賓可以用手機QRcode感應， 來支持這組街頭藝人，來賓無此活動QRcode，請洽服務台</h4>
                                                <br />
                                                <div>
                                                    <img src="~/Content/img/Asset1.png" style="height:140px;padding-right:10px" />
                                                    <img src="~/Content/img/Asset2.png" style="height:140px;" />
                                                    <img src="@Model.QRCodeLikeImg" style="height:140px;" />
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="form-group text-center">
                                                <button id="btnGuestLike" type="button" class="btn btn-sm btn-sys" onclick="OnGuestLike()" style="max-width:100%;max-height:calc(30vh);width:500px;height:500px;font-size:40px">按我打賞</button>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:absolute" id="loading">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
            <br />
            <h3 style="color:#80b4fb">表演重新載入中…</h3>
        </div>
    </div>

    <img src="~/Content/img/thumbs-up.svg" style="width:500px;height:500px;top:40%;right:50%;z-index:9999; display:none;position:fixed;" id="Likeloading" />
    <img src="~/Content/img/heart.svg" style="width:500px;height:500px;top:40%;right:50%;z-index:9999; display:none;position:fixed;" id="heartLikeloading" />

    <div id="LikeADD">
    </div>

    <div id="DivAddButton">
        <i id="title" class="fa fa-arrow-left fa-3x"></i>
        <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
    </div>

    <script language="JavaScript">
        var targetFormID = '#form1'

        $(window).load(function () {
            $('#ErrorDiv').fadeOut(1500)
            $("#@Html.IdFor(m => m.GameUserID)").focus();
        });

        $(document).ready(function () {

            $('#ErrorDiv').show()
            Fireworks()

        });

    function Fireworks() {

        var Show_LIKE_COUNT = $('#@Html.IdFor(m => m.Show_LIKE_COUNT)').val();

        var NOW_LIKE_COUNT = $('#@Html.IdFor(m => m.NOW_LIKE_COUNT)').val();

        if (NOW_LIKE_COUNT / 1 >= Show_LIKE_COUNT / 1) {

            $('#stage_artist_photo').css("background-image", "url('@Url.Content("~/Content/img/Busker_GIF.gif")')");
            $('.stage_firework_b1').css("background-image", "url('@Url.Content("~/assets/img/web_activity_stage_firework.gif")')");

            $('#likeRight').hide();
            $('#likeLeft').hide();

            $('.Addlike').hide();
            $('#NumRight_1').hide();
            $('#NumRight_2').hide();
            $('#NumRight_3').hide();

            $('#NumLeft_1').hide();
            $('#NumLeft_2').hide();
            $('#NumLeft_3').hide();

        }
        else {
            FunlikeNum(NOW_LIKE_COUNT)
        }

}

function FunlikeNum(NOW_LIKE_COUNT) {

    $('#NumRight_1').hide();
    $('#NumRight_2').hide();
    $('#NumRight_3').hide();

    $('#NumLeft_1').hide();
    $('#NumLeft_2').hide();
    $('#NumLeft_3').hide();

    var click_txt_like_num = '@Url.Content("~/assets/img/click_txt_like_num-00.png")';

        if (NOW_LIKE_COUNT <= 9) {

            var Num_1_Img = click_txt_like_num.replace("0.png", NOW_LIKE_COUNT + ".png")

            $('#NumRight_1').css("background-image", "url('" + Num_1_Img + "')");
            $('#NumLeft_1').css("background-image", "url('" + Num_1_Img + "')");

            $('#NumRight_1').show();
            $('#NumLeft_1').show();
        }
        else if (NOW_LIKE_COUNT >= 10 && NOW_LIKE_COUNT <= 99) {
            var Num_1 = NOW_LIKE_COUNT.substr(0, 1)
            var Num_1_Img = click_txt_like_num.replace("0.png", Num_1 + ".png")

            $('#NumRight_1').css("background-image", "url('" + Num_1_Img + "')");
            $('#NumLeft_1').css("background-image", "url('" + Num_1_Img + "')");

            $('#NumRight_1').show();
            $('#NumLeft_1').show();

            var Num_2 = NOW_LIKE_COUNT.substr(1, 1)

            var Num_2_Img = click_txt_like_num.replace("0.png", Num_2 + ".png")

            $('#NumRight_2').css("background-image", "url('" + Num_2_Img + "')");
            $('#NumLeft_2').css("background-image", "url('" + Num_2_Img + "')");

            $('#NumRight_2').show();
            $('#NumLeft_2').show();

        }
        else if (NOW_LIKE_COUNT >= 100 && NOW_LIKE_COUNT <= 999) {

            var Num_1 = NOW_LIKE_COUNT.substr(0, 1)
            var Num_1_Img = click_txt_like_num.replace("0.png", Num_1 + ".png")

            $('#NumRight_1').css("background-image", "url('" + Num_1_Img + "')");
            $('#NumLeft_1').css("background-image", "url('" + Num_1_Img + "')");

            $('#NumRight_1').show();
            $('#NumLeft_1').show();

            var Num_2 = NOW_LIKE_COUNT.substr(1, 1)
            var Num_2_Img = click_txt_like_num.replace("0.png", Num_2 + ".png")

            $('#NumRight_2').css("background-image", "url('" + Num_2_Img + "')");
            $('#NumLeft_2').css("background-image", "url('" + Num_2_Img + "')");

            $('#NumRight_2').show();
            $('#NumLeft_2').show();

            var Num_3 = NOW_LIKE_COUNT.substr(2, 1)
            var Num_3_Img = click_txt_like_num.replace("0.png", Num_3 + ".png")

            $('#NumRight_3').css("background-image", "url('" + Num_3_Img + "')");
            $('#NumLeft_3').css("background-image", "url('" + Num_3_Img + "')");

            $('#NumRight_3').show();
            $('#NumLeft_3').show();

        }

    }

    //function Wa_SetImgAutoSize() {

    //    var ImgID=''

    //    if ($('#ViewImg').is(':visible')) {
    //        ImgID = $('#ViewImg');
    //    }
    //    else {
    //        ImgID = $('#NowImg');
    //    }

    //    var img = ImgID;  //獲取圖片

    //      var windowHeight = $(window).height();
    //      var panelHeight = $("#MainView").height();

    //      var MaxHeight = 0

    //      if (panelHeight > windowHeight) {
    //          MaxHeight = (img.height() - (panelHeight - windowHeight) - 10)
    //      }
    //      else {
    //          MaxHeight = (img.height() - (windowHeight - panelHeight) - 10)
    //      }

    //      if (MaxHeight > 0) {
    //          var HeightWidth = img.offsetHeight / img.offsetWidth; //設置高寬比
    //          var WidthHeight = img.offsetWidth / img.offsetHeight; //設置寬高比

    //          img.height = MaxHeight;
    //          img.width = MaxHeight * WidthHeight;

    //          (img).css({
    //              "width": img.width, "height": img.height
    //          });

    //      }

    //}

    function OnBack() {

        $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")

        $(targetFormID).submit();
    }

    $('.eachtimer').startTimer({
        onComplete: function (element) {
            GetLive_Stream()
        },
        loop: true,
    });

    function funGetExchange() {

        $('.eachtimer').trigger('complete');
        $('#MainView').hide()
        $('#loading').fadeIn(1500)
        $(targetFormID).submit();
    }

    function call(e, input) {
        var code = (e.keyCode ? e.keyCode : e.which);

        if (code == 13) // 13 是 Enter 按鍵的值
        {
            event.preventDefault();

            if ($('#@Html.IdFor(m => m.GameUserID)').val().length >= 8) {

            $('#@Html.IdFor(m => m.GameUserID)').prop('readonly', true);

        setTimeout(function () {
            OnLike()
        });
    }

}
}

function random(max, min) {
var Value = Math.floor(Math.random() * (max - min + 1)) + min;
return Value;
}

function OnGuestLike() {
$('#btnGuestLike').attr('disabled', 'disabled');
OnLike()
}

$('.Likeeachtimer').startTimer({
onComplete: function (element) {
    GetLikeCount()
},
loop: true,
});

function GetLikeCount() {

$.ajax({

    url: "@(Url.Action("GetLike", "Game"))",     // url位置

        type: 'post',                   // post/get
        data: {

            TITLE_SHOW_ID: $('#@Html.IdFor(m => m.WhereTITLE_SHOW_ID)').val()

    },     // data
    dataType: 'json',               // xml/json/script/html
    cache: false,                   // 是否允許快取
    success: function (data) {
        var res = jQuery.parseJSON(data);

        FunlikeNum(res.LIKE_COUNT)
        Fireworks()

    },
    error: function (xhr, err) {
        //alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
        //alert("responseText: " + xhr.responseText);
    }
});

}

var oId = 1;
function OnLike() {

    if ($('#@Html.IdFor(m => m.WhereTITLE_SHOW_ID)').val() == '')
    {
        setTimeout(function () { OnLike() }, 100);
    }
    else
    {

       //LIVE_STREAM
        $.ajax({
                url: "@(Url.Action("LikeSave", "Game"))",     // url位置
        type: 'post',                   // post/get
        data: {
            GAME_NO: $('#@Html.IdFor(m => m.GAME_NO)').val(),
            GameUserID: $('#@Html.IdFor(m => m.GameUserID)').val(),
            TITLE_SHOW_ID: $('#@Html.IdFor(m => m.WhereTITLE_SHOW_ID)').val(),
            UnApply: $('#@Html.IdFor(m => m.UnApply)').val()
        },     // data
        dataType: 'json',               // xml/json/script/html
        cache: false,                   // 是否允許快取
        success: function (data) {
            var res = jQuery.parseJSON(data);
            $("#@Html.IdFor(m => m.GameUserID)").focus();
            $('#@Html.IdFor(m => m.GameUserID)').val('');
            $('#@Html.IdFor(m => m.GameUserID)').prop('readonly', false);

            if (res.Success == 'false') {

                if ($('#@Html.IdFor(m => m.UnApply)').val().toLowerCase() == 'true') {
                    $('#ErrorStr').html(res.Error + '，5秒之內會關掉，按讚視窗喔')
                    $('#ErrorDiv').show()
                    setTimeout(function () { close_window() }, 5000);
                }
                else {
                    $('#ErrorStr').html(res.Error)
                    $('#ErrorDiv').show()
                    $('#ErrorDiv').fadeOut(3000)
                }
            }
            else {

                var WidthHeight = random(300, 50) + "px"
                var Top = random(47, 10) + "%"
                var Right = random(80, 10) + "%"

                var img = random(1, 0)

                if (img == 1) {
                    $('#Likeloading').clone(true).attr('id', 'Likeloading' + oId).appendTo('#LikeADD').css({ 'width': '' + WidthHeight + '', 'height': '' + WidthHeight + '', 'top': '' + Top + '', 'right': '' + Right + '' }).show(1000).fadeOut(2000);
                    setTimeout(function () { $('#Likeloading' + oId).remove(); }, 3000);
                }
                else {
                    $('#heartLikeloading').clone(true).attr('id', 'heartLikeloading' + oId).appendTo('#LikeADD').css({ 'width': '' + WidthHeight + '', 'height': '' + WidthHeight + '', 'top': '' + Top + '', 'right': '' + Right + '' }).show(1000).fadeOut(2000);
                    setTimeout(function () { $('#heartLikeloading' + oId).remove(); }, 3000);
                }
                oId += 1;
                $('#@Html.IdFor(m => m.NOW_LIKE_COUNT)').val(res.LIKE_COUNT);
                Fireworks()
                if ($('#@Html.IdFor(m => m.UnApply)').val().toLowerCase() == 'true') {
                    $('#ErrorStr').html('謝謝您的打賞，5秒之內，會將打賞送到「打賞箱」上喔，且會關掉，按讚視窗喔')
                    $('#ErrorDiv').show()
                    setTimeout(function () {
                        $('#ErrorDiv').fadeOut(2000)

                        setTimeout(function () { close_window() }, 3000);

                    }, 3000);
                }
            }
    },
    error: function (xhr, err) {
        alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
        alert("responseText: " + xhr.responseText);
          $("#@Html.IdFor(m => m.GameUserID)").focus();
            $('#@Html.IdFor(m => m.GameUserID)').val('');
            $('#@Html.IdFor(m => m.GameUserID)').prop('readonly', false);
    }
    });
   }
}

function close_window() {
    window.open('', '_self', '');
    window.close();
}

function GetLive_Stream() {
    //LIVE_STREAM
    $.ajax({
    url: "@(Url.Action("GetLive_Stream", "Game"))",     // url位置
            type: 'post',                   // post/get
            data: {
                GAME_NO: $('#@Html.IdFor(m => m.GAME_NO)').val()
            },     // data
            dataType: 'json',               // xml/json/script/html
            cache: false,                   // 是否允許快取
            success: function (data) {
            var res = jQuery.parseJSON(data);

            if ($('#@Html.IdFor(m => m.WhereTITLE_SHOW_ID)').val() != res.TITLE_SHOW_ID) {
                $('#@Html.IdFor(m => m.WhereTITLE_SHOW_ID)').val(res.TITLE_SHOW_ID)
                    funGetExchange()
            }
        },
        error: function (xhr, err) {
            //alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
            //alert("responseText: " + xhr.responseText);
        }
        });
    }
    </script>
</body>
</html>