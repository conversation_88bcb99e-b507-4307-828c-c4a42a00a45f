﻿@model CERI03IndexViewModel

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)

<div class="toolbar text-right">
    <a role="button" class="btn btn-sm btn-sys" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)'>
        <span class="fa fa-plus" aria-hidden="true"></span>
        新增
    </a>
</div>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-white">
                <tr class="thead-primary">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().GRADE, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().TYPE_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ACCREDITATION_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>

                    <th style="width:60px"></th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {

                    byte? GRADE = null;

                    foreach (var item in Model.ListData)
                    {

                        if (GRADE != null && GRADE != item.GRADE && Model.OrderByColumnName == Html.NameFor(m => m.ListData.First().GRADE).ToHtmlString())
                        {
                            <tr>
                                <td colspan="4"><hr class="hr-line-dashed" /></td>
                            </tr>
                        }

            <tr class="text-center">
                <td>@HRMT01.ParserGrade(item.GRADE)</td>
                <td>@Html.DisplayFor(modelItem => item.TYPE_NAME)</td>
                <td>@Html.DisplayFor(modelItem => item.ACCREDITATION_NAME)</td>


                <td class="text-nowrap">
                    <button type="button" onclick="onBtnDel('@item.ACCREDITATION_ID','@item.GRADE')" role="button" class="btn btn-xs btn-Basic" title="刪除">
                        <span class="glyphicon glyphicon-trash" aria-hidden="true"></span> 刪除
                    </button>
                </td>
            </tr>

                        GRADE = item.GRADE;
                    }
                }
            </tbody>
        </table>
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)