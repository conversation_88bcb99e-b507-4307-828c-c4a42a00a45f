﻿@model CER002IndexViewModel
@using ECOOL_APP.com.ecool.util;
@{


    int i = 0;


}
@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<br />
<div class="form-inline">


    <div class="form-group">
        <label class="control-label">年級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.ListDataHRMT01.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.ListDataHRMT01.whereClass_No, (IEnumerable<SelectListItem>)ViewBag.ClassNoItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">學號/姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.ListDataHRMT01.whereKeyword, new { htmlAttributes = new { @class = "form-control  input-sm" } })
    </div>

</div>
<input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
<input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" /><br />

<font style="color:#428bca"> 這個功能是老師以上的權限才有。老師可以看到學生的，但學生只能看到自己的。</font>
<div style="height:5px"></div>
<div class="panel panel-ACC">
    <div class="panel-heading text-center">
  學生酷幣秘書
    </div>


    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ACC">




            <thead>
                <tr>

                    <th style="text-align: center;" onclick="doSort('GRADE');">
                        年級
                        <img id="GRADE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center" onclick="doSort('CLASS_NO');">
                        班級
                        <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                        學號
                        <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                        座號
                        <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center">
                        姓名
                    </th>
                    <th style="text-align: center">



                    </th>
                </tr>
            </thead>

            <tbody>




                @if (Model.ListDataHRMT01 != null && Model.ListDataHRMT01.HRMT01List.Count() > 0)
                {
                    foreach (var item in Model.ListDataHRMT01.HRMT01List)
                    {

                        <tr align="center">

                            <td>
                                @HRMT01.ParserGrade(byte.Parse(item.GRADE))
                            <td>
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.USER_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td>


                                @Html.ActionLink("酷幣秘書", "StudentIndex", new { wSCHOOL_NO=item.SCHOOL_NO, wUSER_NO = item.USER_NO }, new { @class = "btn btn-xs btn-Basic", title = "酷幣秘書" ,target="_blant"})
                            </td>




                        </tr>
                        i++;
                    }


                }






            </tbody>
        </table>




    </div>

</div>
<div>
    @Html.Pager(Model.ListDataHRMT01.HRMT01List.PageSize, Model.ListDataHRMT01.HRMT01List.PageNumber, Model.ListDataHRMT01.HRMT01List.TotalItemCount).Options(o => o
    .DisplayTemplate("BootstrapPagination")
         .MaxNrOfPages(5)
         .SetPreviousPageText("上頁")
         .SetNextPageText("下頁")
)
</div>


@section scripts{
    <script>




    </script>

}
