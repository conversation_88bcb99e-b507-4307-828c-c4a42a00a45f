﻿
@model ECOOL_APP.com.ecool.Models.DTO.ZZZI26EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@if (ViewBag.FailList != null)
{
    <div class="Div-EZ-reader">
        <div class="table-responsive">
            <div class="text-center">
                <table class="table-ecool table-92Per table-hover">
                    <caption class="Caption_Div_Left">
                        失敗清單：
                    </caption>
                    <thead>
                        <tr>
                            <th>檔名</th>
                            <th>訊息</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in (Dictionary<string, string>)ViewBag.FailList)
                        {
                            if (item.Value != string.Empty && item.Value != null)
                            {
                                <tr>
                                    <td>@item.Key</td>
                                    <td>@item.Value</td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
                <div style="height:15px"></div>
                <div class="btn-group btn-group-justified" role="group">
                    共 @ViewBag.ErrCount 人
                </div>
            </div>
        </div>
    </div>
}


@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()


    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(model => model.Search.ModeVal)
    @Html.HiddenFor(model => model.Search.SCHOOL_NO)
    <img src="~/Content/img/web-bar3-revise-21.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ZZZI26">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group">
                @Html.LabelFor(model => model.Search.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.DropDownListFor(model => model.Search.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Search.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("上傳", htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    <input type="file" name="file" id="file" accept="zip/*" class="form-control" value="瀏覽" style="height:45px" />
                    @Html.ValidationMessage("file", "", new { @class = "text-danger" })
                    <br /><br />
                    <label class="text-info">
                        1.批次上傳學生心得(請用ZIP檔)<br />
                        2.ZIP檔名請用英文或數字<br />
                        3.圖檔格式限制jpg，並需小於6MB<br />
                        4.壓縮檔限用ZIP檔，並需小於100MB<br />
                        5.圖檔檔名一定是要【座號】<br />
                    </label>
                  
                </div>
            </div>
            <div class="text-right">
                @Html.PermissionButton("下一步", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-default", onclick = "Add()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            </div>
        </div>
    </div>


}

@section Scripts {
    <script language="JavaScript">
        function Add() {
            form1.action = '@Url.Action("ManyfileIndex", (string)ViewBag.BRE_NO)';
            form1.submit();
        }
    </script>
}

