﻿@model REFT01QueryViewModel

<div class="form-inline">
    <div class="form-group">
        <label class="control-label">學校</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m=>m.whereSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control-Login  selectpicker", @onchange = "FunPageProc(1);" })
    </div>
</div>
<br />
@{

    @Html.HiddenFor(model => Model.REF_TABLE);
    @Html.HiddenFor(model => Model.REF_KEY);
    @Html.HiddenFor(model => Model.BTN_TYPE);
    @Html.HiddenFor(model => Model.STATUS);
    @Html.HiddenFor(model => model.OrderByName)
    @Html.HiddenFor(model => model.SyntaxName)
    @Html.HiddenFor(model => model.Page)


    string sys_roleBtnClass = (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.sys_role) ? "active" : "";
    string roleBtnClass = (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.role) ? "active" : "";
    string gradeBtnClass = (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.grade) ? "active" : "";
    string ClassBtnClass = (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.Class) ? "active" : "";
    string personBtnClass = (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.person) ? "active" : "";

    <div class="text-left">
        @*<button class="btn btn-xs btn-pink @sys_roleBtnClass" onclick="SelectBTN_TYPE('@REFT01_Q.BTN_TYPE_VAL.sys_role')" type="button">系統角色</button>*@
        <button class="btn btn-xs btn-pink @roleBtnClass" onclick="SelectBTN_TYPE('@REFT01_Q.BTN_TYPE_VAL.role')" type="button">學校角色</button>
        <button class="btn btn-xs btn-pink @gradeBtnClass" onclick="SelectBTN_TYPE('@REFT01_Q.BTN_TYPE_VAL.grade')" type="button">年級</button>
        <button class="btn btn-xs btn-pink @ClassBtnClass" onclick="SelectBTN_TYPE('@REFT01_Q.BTN_TYPE_VAL.Class')" type="button">班級</button>
        <button class="btn btn-xs btn-pink @personBtnClass" onclick="SelectBTN_TYPE('@REFT01_Q.BTN_TYPE_VAL.person')" type="button">個人</button>
    </div>
}



@if (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.sys_role)
{
    <div class="panel panel-ACC">
        <div class="panel-Group  text-center">
            系統角色
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">

                    @{

                        string BtnClass = string.Empty;

                        foreach (var item in Model.USER_TYPE_LIST)
                        {
                            <div class="col-md-3">
                                <button onclick="OnClickITEM_VAL('@item')" class="btn btn-default btn-block">@UserType.GetDesc(item)</button>
                            </div>
                        }
                    }

                </div>
            </div>
        </div>
    </div>

                        }
@if (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.role)
{
    <div class="panel panel-ACC">
        <div class="panel-Group  text-center">
            學校角色
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">

                    @{

                        string BtnClass = string.Empty;

                        foreach (var item in Model.Role_LIST)
                        {
                            <div class="col-md-3">
                                <button onclick="OnClickITEM_VAL('@item.ROLE_ID')" class="btn btn-default btn-block">@item.ROLE_NAME</button>
                            </div>
                        }
                    }

                </div>
            </div>
        </div>
    </div>
                        }

@if (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.grade)
{
    <div class="panel panel-ACC">
        <div class="panel-Group  text-center">
            年級
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">

                    @{

                        string BtnClass = string.Empty;

                        foreach (var item in Model.Grade_LIST)
                        {
                            <div class="col-md-3">
                                <button onclick="OnClickITEM_VAL('@item')" class="btn btn-default btn-block">@item</button>
                            </div>
                        }
                    }

                </div>
            </div>
        </div>
    </div>
                        }

@if (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.Class)
{
    <div class="panel panel-ACC">
        <div class="panel-Group  text-center">
            班級
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">

                    @{

                        string BtnClass = string.Empty;

                        foreach (var item in Model.Class_LIST)
                        {
                            <div class="col-md-3">
                                <button onclick="OnClickITEM_VAL('@item')" class="btn btn-default btn-block">@item</button>
                            </div>
                        }
                    }

                </div>
            </div>
        </div>
    </div>
                        }
@if (Model.BTN_TYPE == REFT01_Q.BTN_TYPE_VAL.person)
{

    <div class="panel panel-ACC">
        <div class="panel-Group  text-center">
            個人
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <label class="control-label"> * 查詢條件</label>
                <br /><br />
                @Html.AntiForgeryToken()
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                <div class="form-group">
                    @Html.LabelFor(model => model.whereUSER_TYPE, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.DropDownListFor(model => model.whereUSER_TYPE, (IEnumerable<SelectListItem>)ViewBag.UserTypeItem, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.whereUSER_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.whereUSER_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.whereUSER_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "可模糊查詢" } })
                        @Html.ValidationMessageFor(model => model.whereUSER_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.whereGRADE, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.DropDownListFor(model => model.whereGRADE, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.whereGRADE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.whereCLASS_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.DropDownListFor(model => model.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItem, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.whereCLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.whereNAME, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.whereNAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "可模糊查詢" } })
                        @Html.ValidationMessageFor(model => model.whereNAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group text-center">
                    <button type="button" class="btn btn-default" onclick="FunPageProc(1)">查 詢</button>
                </div>
            </div>
        </div>
    </div>


        <div style="text-align: center; margin-top: 20px; margin-bottom: 30px;">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>

        <div class="panel panel-ACC">
            <div class="form-horizontal">
                <label class="control-label"> * 未選取人員清單，請點選學號/帳號「加入」</label>
                <br /><br />
                <div class="form-group">
                    <div class="col-xs-6">
                        @Html.DropDownList("pageSize", (IEnumerable<SelectListItem>)ViewBag.PageSizeItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                    </div>
                   
                </div>
                <div class="table-responsive">
                    <table class="table-ecool table-hover table-ecool-ACC">
                        <thead>
                            <tr>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SHORT_NAME')">
                                        @Html.DisplayNameFor(model => model.HRMT01List.First().SHORT_NAME)
                                    </samp>
                                </th>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('USER_TYPE')">
                                        @Html.DisplayNameFor(model => model.HRMT01List.First().USER_TYPE)
                                    </samp>
                                </th>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('USER_NO')">
                                        @Html.DisplayNameFor(model => model.HRMT01List.First().USER_NO)
                                    </samp>
                                </th>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('NAME')">
                                        @Html.DisplayNameFor(model => model.HRMT01List.First().NAME)
                                    </samp>
                                </th>

                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CLASS_NO')">
                                        @Html.DisplayNameFor(model => model.HRMT01List.First().CLASS_NO)
                                    </samp>
                                </th>
                                <th>

                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SEAT_NO')">
                                        @Html.DisplayNameFor(model => model.HRMT01List.First().SEAT_NO)
                                    </samp>
                                </th>
                                <th>

                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CARD_NO')">
                                        @Html.DisplayNameFor(model => model.HRMT01List.First().CARD_NO)
                                    </samp>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.HRMT01List)
                            {
                            <tr onclick="OnClickITEM_VAL('@item.USER_NO')" style="cursor:pointer">
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                </td>
                                <td align="center">
                                    @UserType.GetDesc(item.USER_TYPE)
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>

                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.CARD_NO)
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="form-group text-center">
                @Html.Pager(Model.HRMT01List.PageSize, Model.HRMT01List.PageNumber, Model.HRMT01List.TotalItemCount).Options(o => o
            .DisplayTemplate("BootstrapPagination")
            .MaxNrOfPages(5)
            .SetPreviousPageText("上頁")
            .SetNextPageText("下頁")
                        )
            </div>
        </div>
}


<script type="text/javascript">
    $(document).ready(function () {
        $('.selectpicker').selectpicker({
            liveSearch: true,
            showSubtext: true
        });
    });

     //排序
        function FunSort(SortName) {

            OrderByName = $('#OrderByName').val();
            SyntaxName = $('#SyntaxName').val();

            if (OrderByName == SortName) {

                if (SyntaxName == "Desc") {
                    $('#SyntaxName').val("ASC");
                }
                else {
                    $('#SyntaxName').val("Desc");
                }
            }
            else {
                $('#OrderByName').val(SortName);
                $('#SyntaxName').val("Desc");
            }
            funAjax()
        }


     //分頁
    function FunPageProc(pageno) {
        $('#Page').val(pageno);
        funAjax()
    }

    function SelectBTN_TYPE(Val)
    {
       $('#BTN_TYPE').val(Val)
       funAjax()
    }



    //查詢
    function funAjax() {

 
        $.ajax({
            url: '@Url.Action("_QuerySelect", (string)ViewBag.BRE_NO)',
            data: $('#form1').serialize(),
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelect').html(data);
            }
        });
    }



    function OnClickITEM_VAL(val)
    {
        var data = {
            "REF_TABLE": $('#REF_TABLE').val(),
            "REF_KEY": $('#REF_KEY').val(),
            "STATUS": $('#STATUS').val(),
            "BTN_TYPE": $('#BTN_TYPE').val(),
            "ITEM_VAL": val,
            "whereSCHOOL_NO": $('#whereSCHOOL_NO').val(),
            "DivHeight": $('#DivHeight').val(),
            "DataType": '@REFT01EditListViewModel.DataTypeVal.DataTypeAdd'
        };



        $.ajax({
            url: '@Url.Action("_QuerySelectDataList", (string)ViewBag.BRE_NO)',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelectDataList').html(data);

                funAjax()
            }
        });


    }
</script>