/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Symbols/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Symbols={directory:"Symbols/Regular",family:"GyrePagellaMathJax_Symbols",testString:"\u00A0\u2300\u2305\u2306\u2310\u2319\u231C\u231D\u231E\u231F\u2320\u2321\u2329\u232A\u239B",32:[0,0,250,0,0],160:[0,0,250,0,0],8960:[564,64,788,80,708],8965:[444,-56,760,80,680],8966:[554,54,760,80,680],8976:[280,0,790,80,710],8985:[500,-220,790,80,710],8988:[680,-450,390,80,310],8989:[680,-450,390,80,310],8990:[50,180,390,80,310],8991:[50,180,390,80,310],8992:[1212,0,737,318,657],8993:[1194,18,737,80,418],9001:[658,158,391,80,311],9002:[658,158,391,80,311],9115:[1208,0,679,143,583],9116:[396,0,679,143,223],9117:[1208,0,679,143,583],9118:[1208,0,679,96,536],9119:[396,0,679,456,536],9120:[1208,0,679,96,536],9121:[1214,0,521,143,425],9122:[810,0,521,143,223],9123:[1214,0,521,143,425],9124:[1214,0,521,96,378],9125:[810,0,521,298,378],9126:[1214,0,521,96,378],9127:[616,0,589,252,493],9128:[1194,0,589,96,332],9129:[616,0,589,252,493],9130:[596,0,589,257,337],9131:[616,0,589,96,337],9132:[1194,0,589,257,493],9133:[616,0,589,96,337],9134:[580,0,760,328,432],9138:[829,0,1408,80,1248],9139:[896,4,1408,80,1328],9143:[1623,0,770,120,610],10178:[650,0,760,80,680],10200:[650,150,860,80,780],10201:[650,150,860,80,780],10202:[650,150,1203,80,1123],10203:[650,150,1203,80,1123],10204:[410,-90,920,80,840],10205:[650,150,1360,80,1280],10206:[650,150,1360,80,1280],10208:[576,76,595,80,515],10209:[471,-29,603,80,523],10210:[471,-29,698,80,618],10211:[471,-29,698,80,618],10214:[670,170,430,120,350],10215:[670,170,430,80,310],10218:[658,158,611,80,531],10219:[658,158,611,80,531]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Symbols"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Symbols/Regular/Main.js"]);
