﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CER002ListViewModel
    {
        /// <summary>
        ///護照細項ID
        /// </summary>
        [DisplayName("護照細項ID")]
        public string ACCREDITATION_ID { get; set; }

        public string ITEM_NO { get; set; }
        public byte? GRADE { get; set; }
        public string CLASS_NO { get; set; }
        public string NAME { get; set; }
        public string ACCREDITATION_ID_ITEM_NO { get; set; }

        /// <summary>
        ///護照ID
        /// </summary>
        [DisplayName("護照ID")]
        public string ACCREDITATION_TYPE { get; set; }

        [DisplayName("護照名稱")]
        public string TYPE_NAME { get; set; }

        /// <summary>
        ///護照細項名稱
        /// </summary>
        [DisplayName("護照細項名稱")]
        public string ACCREDITATION_NAME { get; set; }

        [DisplayName("通過主旨")]
        public string SUBJECT { get; set; }

        [DisplayName("通過內容")]
        public string CONTENT { get; set; }

        [DisplayName("認證老師/認證時間")]
        public string VERIFIER { get; set; }
        public string IsText { get; set; }

        public string PersonText { get; set; }
        public string USER_NO { get; set; }
        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("是否完成")]
        public bool? IS_PASS { get; set; }

        public List<string> GRADE_SEMESTERs { get; set; }

        public string StringGRADE_SEMESTERs { get; set; }
    }
}