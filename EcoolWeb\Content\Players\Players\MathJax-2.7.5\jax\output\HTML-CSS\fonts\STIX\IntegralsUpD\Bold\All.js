/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpD/Bold/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXIntegralsUpD-bold"],{32:[0,0,250,0,0],160:[0,0,250,0,0],8747:[2000,269,515,58,560],8748:[2000,269,875,58,920],8749:[2000,269,1239,59,1281],8750:[2000,269,626,56,695],8751:[2000,269,1039,39,1039],8752:[2000,269,1384,36,1395],8753:[2000,269,632,47,760],8754:[2000,269,639,56,769],8755:[2000,269,598,56,778],10764:[2000,269,1595,58,1640],10765:[2000,269,552,-35,590],10766:[2000,269,642,35,680],10767:[2000,269,675,25,752],10768:[2000,269,640,56,646],10769:[2000,269,632,47,760],10770:[2000,269,625,58,654],10771:[2000,269,557,58,626],10772:[2000,269,708,58,789],10773:[2000,269,626,56,695],10774:[2000,269,718,56,747],10775:[2000,269,963,24,1057],10776:[2000,269,681,62,692],10777:[2000,269,832,65,898],10778:[2000,269,832,65,898],10779:[2182,269,733,0,773],10780:[2000,451,525,58,831]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsUpD/Bold/All.js");
