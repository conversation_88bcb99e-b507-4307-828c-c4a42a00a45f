/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Size6/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Size6={directory:"Size6/Regular",family:"LatinModernMathJax_Size6",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u2223",32:[0,0,332,0,0],40:[1446,946,736,226,682],41:[1446,946,736,54,510],47:[2777,2277,1997,56,1941],91:[1450,950,528,233,509],92:[2777,2277,1997,56,1941],93:[1450,950,528,19,295],123:[1450,950,750,102,648],124:[1752,1252,278,103,175],125:[1450,950,750,102,648],160:[0,0,332,0,0],770:[748,-570,1581,0,1581],771:[769,-532,1599,0,1599],774:[743,-574,1604,0,1604],780:[742,-564,1581,0,1581],785:[760,-591,1604,0,1604],812:[-96,275,1581,0,1581],813:[-108,287,1581,0,1581],814:[-96,265,1604,0,1604],815:[-118,287,1604,0,1604],816:[-118,355,1599,0,1599],8214:[1752,1252,410,56,354],8260:[2777,2277,1997,56,1941],8425:[764,-513,2610,0,2610],8739:[1752,1252,278,103,175],8741:[1752,1252,410,56,354],8968:[1450,950,583,210,555],8969:[1450,950,583,28,373],8970:[1450,950,583,210,555],8971:[1450,950,583,28,373],9001:[1450,950,750,173,697],9002:[1450,950,750,53,577],9140:[764,-513,2610,0,2610],9141:[-83,334,2610,0,2610],9180:[787,-505,3524,0,3524],9181:[-75,357,3524,0,3524],9182:[845,-498,3502,0,3502],9183:[-67,414,3502,0,3502],9184:[869,-606,3574,0,3574],9185:[-176,439,3574,0,3574],10214:[1450,950,838,282,816],10215:[1450,950,838,22,556],10216:[1450,950,750,173,697],10217:[1450,950,750,53,577],10218:[1450,950,1124,173,1071],10219:[1450,950,1124,53,951],10222:[1472,972,541,235,485],10223:[1472,972,541,56,306]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Size6"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size6/Regular/Main.js"]);
