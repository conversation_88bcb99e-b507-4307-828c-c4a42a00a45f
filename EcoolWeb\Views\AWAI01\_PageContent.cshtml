﻿@model AWAI01IndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = ViewBag.Panel_Title;

}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@if (AppMode)
{

    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        {
            @Html.ActionLink("我的獎品紀錄", "QUERY", "AWA004", new { Awat = "Awat", whereUserNo = user.USER_NO }, new { @class = "btn btn-sm btn-sys" })
        }
        else if (user.USER_TYPE == UserType.Parents)
        {
            @Html.ActionLink("寶貝獎品紀錄", "QUERY", "AWA004", new { Awat = "Awat", whereUserNo = HRMT06.GetStringMyPanyStudent(user) }, new { @class = "btn btn-sm btn-sys" })
        }
    }

}
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@if (AppMode == false)
{
    if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
    {
        @Html.ActionLink("老師酷幣點數排行榜", "QueryTeacher", "AWA003", new { Awat = "Awat" }, new { @class = "btn btn-sm btn-sys" })
        @Html.ActionLink("老師兌獎名單", "QUERY", "AWA006", new { Awat = "Awat" }, new { @class = "btn btn-sm btn-sys" })
    }
    else
    {
        @Html.ActionLink("酷幣點數排行榜", "QUERY", "AWA003", new { Awat = "Awat" }, new { @class = "btn btn-sm btn-sys" })
        @*@Html.ActionLink("兌獎名單", "QUERY", "AWA004", new { Awat = "Awat" }, new { @class = "btn btn-sm btn-sys", target = "_blank" })*@

        if (user != null)
        {
            if (user.USER_TYPE == UserType.Student)
            {
                @Html.ActionLink("我的獎品紀錄", "QUERY", "AWA004", new { Awat = "Awat", whereUserNo = user.USER_NO }, new { @class = "btn btn-sm btn-sys" })
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                @Html.ActionLink("寶貝獎品紀錄", "QUERY", "AWA004", new { Awat = "Awat", whereUserNo = HRMT06.GetStringMyPanyStudent(user) }, new { @class = "btn btn-sm btn-sys" })
            }
        }
    }
    <button class="btn btn-sm btn-sys" onclick="onProductOrderList()">獎品兌獎排行榜</button>
}

@if (user != null)
{
    if (Model.VisableModify == true)
    {
        if (string.IsNullOrWhiteSpace(Model.Search.unProduct))
        {
            <input type="button" class="btn btn-sm btn-sys" value="搜尋已下架商品" onclick="onUnProduct('unProduct')" />
        }
        else
        {
            <input type="button" class="btn btn-sm btn-sys" value="搜尋上架中商品" onclick="onUnProduct('')" />
        }
    }
}

@if (Model.VisableInsert == true)
{
    <button class="btn btn-sm btn-sys" onclick="funGetAdd()">
        新增獎品
    </button>
}

@if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student
    && Model.VisableInsert
    && string.IsNullOrWhiteSpace(Model.Search.unProduct)) //學生功能 - 行動支付的FULLSCREEN頁面
{
    <input type="button" class="btn btn-sm btn-sys" value="前往行動支付頁面" onclick="funGetFullScreen()" />
}

@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.BackAction)
@Html.HiddenFor(m => m.Search.BackController)

@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereExchangeType)
@Html.HiddenFor(m => m.Search.WhereAWARD_NO)
@Html.HiddenFor(m => m.Search.WhereAWARD_STS)
@Html.HiddenFor(m => m.Search.WhereSouTable)
@Html.HiddenFor(m => m.Search.unProduct)
@Html.HiddenFor(m => m.Search.whereAWARD_SCHOOL_NO)
@Html.HiddenFor(m => m.Search.AWARD_TYPE)

@if (AppMode == false)
{
    <ul class="label_dt list-unstyled list-icons-box pt-0 mb-0 border-0">
        <li><img src="~/Content/images/icon-redeem-musterSchool.png" alt="">總召學校獎品</li>
        <li><img src="~/Content/images/icon-redeem-hot.png" alt="">熱門獎品</li>
        <li><img src="~/Content/images/icon-redeem-new.png" alt="">新上架獎品</li>
        <li><img src="~/Content/images/icon-redeem-bidding.png" alt="">競標獎品</li>
        <li><img src="~/Content/images/icon-redeem-lv10.png" alt="">擁有 閱讀認證滿 1-10級，才可兌換該獎品</li>
        <li><img src="~/Content/images/icon-redeem-lv6.png" alt="">擁有 閱讀護照滿 1-6級，才可兌換該獎品</li>
        <li><img src="~/Content/images/icon-redeem-lock.png" alt="">有限制特定的對象</li>
        <li><img src="~/Content/images/icon-redeem-mobile.png" alt="">可以行動支付，也可以網頁上兌換</li>
        <li><img src="~/Content/images/icon-redeem-studentCard.png" alt="">只限定數位學生證兌換</li>
    </ul>
}

<div style="width:95%">
    @if (user != null && AppMode == false)
    {
        //取得兌換獎品學生投稿說明檔
        if (user.USER_TYPE == "S")
        {
            @Html.Raw(HttpUtility.HtmlDecode(Model.AwatQ02SEXPLAIN))
        }
        else if (user.USER_TYPE == "T")
        {
            @Html.Raw(HttpUtility.HtmlDecode(Model.AwatQ02TEXPLAIN))
        }
        <br />
    }
</div>

<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">
            @Html.DisplayNameFor(model => model.Search.whereKeyword)
        </label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.Search.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>

<br />
<div class="row">
    <div class="col-md-6 col-xs-12 text-left">
        <samp>獎品類別：</samp>
        @foreach (var item in Model.AwardSchoolItemQuery)
        {
            string active = item.Selected ? "active" : "";
            <button class="btn btn-xs btn-pink @(active)" type="button" onclick="doSearch('@Html.IdFor(m => m.Search.whereAWARD_SCHOOL_NO)', '@item.Value');">@item.Text</button>
        }
    </div>
    <div class="col-md-6 col-xs-12 text-left">
        <samp>獎品狀態：</samp>
        @foreach (var item in Model.AwardTypeItemQuery)
        {
            string active = item.Selected ? "active" : "";
            <button class="btn btn-xs btn-pink @(active)" type="button" onclick="doSearch('@Html.IdFor(m => m.Search.AWARD_TYPE)', '@item.Value');">@item.Text</button>
        }
    </div>
</div>
<br />

<img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="" />

@if (Model.Search.unProduct == "unProduct" //搜尋已下架商品
    || Model.Search.whereAWARD_SCHOOL_NO == SharedGlobal.ALL  //搜尋總召獎品
    || Model.SpecificListData == null
    || (Model.SpecificListData != null && Model.SpecificListData.Count() == 0)) //總召獎品 =0
{
    Model.ShowMultiple = false;
    Model.MultipleLevel = 2;
    Html.RenderPartial("_ProductList", Model);
}
else
{
    Model.MultipleLevel = 1;
    Html.RenderPartial("_ProductList", Model);

    Model.MultipleLevel = 2;
    Html.RenderPartial("_ProductList", Model);
}