/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Latin={directory:"Latin/Regular",family:"STIXMathJax_Latin",testString:"\u00A0\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00AD\u00B2\u00B3\u00B6\u00B8\u00B9\u00BA",32:[0,0,250,0,0],160:[0,0,250,0,0],161:[468,218,330,96,202],162:[579,138,500,53,448],164:[534,10,500,-22,522],166:[676,14,200,67,133],169:[676,14,760,38,722],170:[676,-394,276,4,270],171:[416,-33,500,42,456],173:[257,-194,333,39,285],178:[676,-270,300,1,296],179:[676,-262,300,13,291],182:[662,154,592,60,532],184:[0,215,333,52,261],185:[676,-270,300,57,248],186:[676,-394,310,6,304],187:[416,-33,500,43,458],188:[676,14,750,42,713],189:[676,14,750,36,741],190:[676,14,750,13,718],191:[467,218,444,30,376],192:[928,0,722,15,707],193:[928,0,722,15,707],194:[924,0,722,15,707],195:[888,0,722,15,707],196:[872,0,722,15,707],197:[961,0,722,15,707],198:[662,0,889,0,863],199:[676,215,667,28,633],200:[928,0,611,12,597],201:[928,0,611,12,597],202:[924,0,611,12,597],203:[872,0,611,12,597],204:[928,0,333,18,315],205:[928,0,333,18,315],206:[924,0,333,10,321],207:[872,0,333,17,315],208:[662,0,722,16,685],209:[888,11,722,12,707],210:[928,14,722,34,688],211:[928,14,722,34,688],212:[924,14,722,34,688],213:[888,14,722,34,688],214:[872,14,722,34,688],216:[734,80,722,34,688],217:[928,14,722,14,705],218:[928,14,722,14,705],219:[924,14,722,14,705],220:[872,14,722,14,705],221:[928,0,722,22,703],222:[662,0,556,16,542],223:[683,9,500,12,468],224:[678,10,444,37,442],225:[678,10,444,37,442],226:[674,10,444,37,442],227:[638,10,444,37,442],228:[622,10,444,37,442],229:[713,10,444,37,442],230:[460,7,667,38,632],231:[460,215,444,25,412],232:[678,10,444,25,424],233:[678,10,444,25,424],234:[674,10,444,25,424],235:[622,10,444,25,424],236:[678,0,278,6,243],237:[678,0,278,16,273],238:[674,0,278,-17,294],239:[622,0,278,-10,288],241:[638,0,500,16,485],242:[678,10,500,29,470],243:[678,10,500,29,470],244:[674,10,500,29,470],245:[638,10,500,29,470],246:[622,10,500,29,470],248:[551,112,500,29,470],249:[678,10,500,9,480],250:[678,10,500,9,480],251:[674,10,500,9,480],252:[622,10,500,9,480],253:[678,218,500,14,475],254:[683,217,500,5,470],255:[622,218,500,14,475],256:[773,0,722,15,707],257:[561,10,444,37,442],258:[876,0,722,15,707],259:[664,10,444,37,442],260:[674,165,722,15,707],261:[460,165,444,37,472],262:[890,14,667,28,633],263:[678,10,444,25,412],264:[886,14,667,28,633],265:[674,10,444,25,412],266:[834,14,667,28,633],267:[622,10,444,25,412],268:[886,14,667,28,633],269:[674,10,444,25,412],270:[886,0,722,16,685],271:[701,10,586,27,604],272:[662,0,722,16,685],273:[683,10,500,27,507],274:[773,0,611,12,597],275:[561,10,444,25,424],276:[876,0,611,12,597],277:[664,10,444,25,424],278:[834,0,611,12,597],279:[622,10,444,25,424],280:[662,165,611,12,597],281:[460,165,444,25,424],282:[886,0,611,12,597],283:[674,10,444,25,424],284:[886,14,722,32,709],285:[674,218,500,28,470],286:[876,14,722,32,709],287:[664,218,500,28,470],288:[834,14,722,32,709],289:[622,218,500,28,470],290:[676,280,722,32,709],291:[766,218,500,28,470],292:[886,0,722,18,703],293:[886,0,500,9,487],294:[662,0,723,17,702],296:[850,0,333,1,331],297:[638,0,278,-25,305],298:[773,0,333,11,322],299:[561,0,278,-21,290],300:[876,0,333,18,315],301:[664,0,278,-1,280],302:[662,165,333,18,315],303:[683,165,278,16,277],304:[834,0,333,18,315],306:[662,14,747,18,728],307:[683,218,538,16,454],308:[886,14,373,-6,367],309:[674,218,278,-70,295],310:[662,280,722,33,723],311:[683,280,500,7,505],312:[459,0,542,5,532],313:[890,0,611,12,598],314:[890,0,278,19,257],315:[662,280,611,12,598],316:[683,280,278,19,257],317:[683,0,611,12,598],318:[702,0,381,19,362],319:[662,0,620,29,615],320:[683,0,370,19,354],321:[662,0,611,10,597],322:[683,0,278,19,259],323:[890,11,722,12,707],324:[678,0,500,16,485],325:[662,280,722,12,707],326:[460,280,500,16,485],327:[886,11,722,12,707],328:[674,0,500,16,485],329:[702,0,590,20,566],330:[678,18,710,16,673],331:[460,218,504,16,424],332:[773,14,722,34,688],333:[561,10,500,29,470],334:[876,14,722,34,688],335:[664,10,500,29,470],336:[890,14,722,34,688],337:[678,10,500,29,470],338:[668,6,889,30,885],339:[460,10,722,30,690],340:[890,0,667,17,660],341:[678,0,333,5,335],342:[662,280,667,17,660],343:[460,280,333,5,335],344:[886,0,667,17,660],345:[674,0,333,5,335],346:[890,14,556,43,491],347:[678,10,389,51,348],348:[886,14,556,43,491],349:[674,10,389,40,351],350:[676,215,556,43,491],351:[459,215,389,51,348],352:[924,14,556,43,491],353:[674,10,389,38,349],354:[662,215,611,17,593],355:[579,215,278,13,279],356:[886,0,611,17,593],357:[701,10,315,13,333],358:[662,0,613,17,593],359:[584,5,279,11,280],360:[849,14,722,14,705],361:[638,10,500,9,480],362:[773,14,722,14,705],363:[561,10,500,9,480],364:[876,14,722,14,705],365:[664,10,500,9,480],366:[898,14,722,14,705],367:[711,10,500,9,480],368:[890,14,722,14,705],369:[678,10,500,9,480],370:[662,165,722,14,705],371:[450,156,500,9,480],372:[886,11,944,5,932],373:[674,14,722,21,694],374:[886,0,722,22,703],375:[674,218,500,14,475],376:[872,0,722,22,703],377:[890,0,612,10,598],378:[678,0,444,27,418],379:[834,0,612,10,598],380:[622,0,444,27,418],381:[924,0,612,10,598],382:[674,0,444,27,418],383:[683,0,334,20,383],384:[683,10,500,-19,472],392:[559,10,500,25,511],400:[684,6,580,33,562],402:[706,159,434,6,426],405:[683,10,735,9,710],409:[683,0,500,7,505],410:[683,0,278,19,257],411:[668,0,520,55,516],414:[460,233,500,16,485],416:[754,14,722,34,688],417:[474,10,545,29,531],421:[669,217,500,5,470],426:[684,233,432,20,412],427:[579,218,290,13,279],429:[683,10,310,14,333],431:[774,14,766,14,810],432:[561,10,500,9,539],437:[662,0,612,10,598],442:[450,234,381,4,360],443:[676,0,500,22,482],446:[539,12,500,73,427],448:[736,0,160,54,105],449:[736,0,280,54,225],450:[736,0,435,34,400],451:[676,9,333,130,236],496:[674,218,278,-70,294],506:[938,0,722,15,707],507:[890,10,444,37,442],508:[890,0,889,0,863],509:[678,7,667,38,632],510:[890,80,722,34,688],511:[678,112,500,29,470],545:[683,150,671,27,652],564:[683,150,429,19,410],565:[460,150,672,16,653],566:[579,150,401,13,382],7808:[890,11,944,5,932],7809:[678,14,722,21,694],7810:[890,11,944,5,932],7811:[678,14,722,21,694],7812:[834,11,944,5,932],7813:[622,14,722,21,694],7922:[890,0,722,22,703],7923:[678,218,500,14,475],42791:[683,233,481,9,427],42898:[676,14,734,18,700],64256:[683,0,605,20,655],64257:[683,0,558,32,523],64258:[683,0,556,31,522],64259:[683,0,832,20,797],64260:[683,0,830,20,796]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Latin"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/Regular/Main.js"]);
