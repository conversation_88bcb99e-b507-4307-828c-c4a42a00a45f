﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public class ADDT06ViewModel
    {
        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ADDT06> ADDT06List;

        public ADDTMyDataViewModel MyData;

        public ADDT06ViewModel()
        {
            Page = 0;
            OrdercColumn = "APPLY_NO";
            SyntaxName = "Desc";
        }

        /// <summary>
        /// 批次作廢選勾
        /// </summary>
        public string StringDelApply { get; set; }

        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereBOOK_NAME { get; set; }
        public DateTime? SDATE { get; set; }
        public DateTime? EDATE { get; set; }
        /// <summary>
        /// 只顯示某種狀態
        /// </summary>
        public string whereAPPLY_STATUS { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示班級
        /// </summary>
        public string whereCLASS_NO { get; set; }
        /// <summary>
        /// 只顯示班級
        /// </summary>
        public string whereSeat_NO { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        public int Page { get; set; }

        /// <summary>
        /// 前一頁Action
        /// </summary>
        public string BackAction { get; set; }

        /// <summary>
        /// 圖片模式
        /// </summary>
        public bool PictureMode { get; set; }


        public DateTime? WhereUP_DATE_START { get; set; }

        public DateTime? WhereUP_DATE_END { get; set; }
    }

    public class ADDTMyDataViewModel
    {
        ///Summary
        ///閱讀本數
        ///Summary
        [Display(Name = "閱讀本數")]
        public Nullable<int> BOOK_QTY { get; set; }

        ///Summary
        ///等級名稱
        ///Summary
        [Display(Name = "等級名稱")]
        public string LEVEL_DESC { get; set; }

        ///Summary
        ///閱讀等級
        ///Summary
        [Display(Name = "閱讀等級")]
        public int LEVEL_ID { get; set; }

        ///Summary
        ///剩多少本升級
        ///Summary
        [Display(Name = "剩多少本升級")]
        public string UNLEVEL_QTY { get; set; }
    }
}