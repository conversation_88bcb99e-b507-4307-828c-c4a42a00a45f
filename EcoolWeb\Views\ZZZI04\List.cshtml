﻿
@model IPagedList<ECOOL_APP.com.ecool.Models.entity.BT02AmdinViewModel>


@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.PermissionActionLink(Resources.Resource.INSERT, "Create", (string)ViewBag.BRE_NO, null, new { @class = "btn btn-sm btn-sys" }, (string)ViewBag.BRE_NO, "Save", 1)


<img src="~/Content/img/web-bar2-revise-02.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-hover table-ecool-ZZZI04">
                <thead>
                    <tr>
                        <th>
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().CLASS_NAME)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().SUBJECT)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().ISPUBLISH)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().S_DATE)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().E_DATE)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().TOP_YN)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().CRE_PERSON_NAME)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                @if (ViewBag.Btn)
                                {
                                    @Html.ActionLink(Resources.Resource.EDIT, "Create", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = item.BULLET_ID }, new { @class = "btn btn-xs btn-Basic" })
                                }
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.CLASS_NAME)
                            </td>
                            <td style="text-align: left;white-space:normal">
                                @Html.ActionLink(item.SUBJECT, "Details", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = item.BULLET_ID, SearchContents = "", page = Model.PageNumber, PrevAction = "List" }, new { @class = "btn-table-link" })
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.ISPUBLISH)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.S_DATE)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.E_DATE)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.TOP_YN)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CRE_PERSON_NAME)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>



<div>
    @Html.Pager(Model.PageSize, Model.PageNumber, Model.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>

@using (Html.BeginForm("List", (string)ViewBag.BRE_NO, FormMethod.Post, new { id= "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.Hidden("page", Model.PageNumber)
}

@section scripts{
    <script type="text/javascript">
        var targetFormID = '#form1';

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#page').val(page)
                $(targetFormID).submit();
            }
        };
    </script>
}












