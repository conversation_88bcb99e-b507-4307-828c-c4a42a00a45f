﻿@model GAAI01WearIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();

}
<style>
    #Data div {
        font-size: 24px
    }
</style>

@Html.Partial("_Notice")

@using (Html.BeginForm("TagWearDetails", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.IsSearch)
    @Html.HiddenFor(m => m.IsWearData)
    @Html.HiddenFor(m => m.WhereWearModelType)
    @Html.HiddenFor(m => m.WhereCLASS_NO)
    @Html.HiddenFor(m => m.WhereALARM_ID)

    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>@ViewBag.Title</h1>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                            @Html.Editor("CARD_NO", new { htmlAttributes = new { @class = "form-control", @placeholder = "請用數位學生證感應", @onKeyPress = "call(event,this);" } })
                        </div>
                        @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                    </div>
                    <div class="form-group">
                        <strong style="color:red">登記@(Model.WhereCLASS_NO)班，登記日期@(Model.ALARM_DATE)</strong>
                    </div>
                </div>
            </div>
            <div id="DetailsView">
                <div style="margin-top:5px;margin-bottom:5px;text-align:center">
                    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
                </div>
                @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                <div class="panel panel-ZZZ">
                    <div class="panel-heading text-center">
                        有配載的學生
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive" id="Data">
                            <div class="css-table" style="width:92%;" id="tbData">
                                <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">

                                    <div class="th" style="text-align:center">
                                        全名
                                    </div>
                                    <div class="th" style="text-align:center">
                                        學號
                                    </div>
                                    <div class="th" style="text-align:center">
                                        班級
                                    </div>
                                    <div class="th" style="text-align:center">
                                        座號
                                    </div>
                                </div>
                                <div id="editorRows" class="tbody">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-xs-12 text-center">
                        <div class="form-inline">
                            <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
                                取消
                            </button>
                            <button type="button" class="btn btn-default" onclick="onSave()">
                                <span class="fa fa-check-circle" aria-hidden="true"></span>下一步
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr" style="font-size:50px"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script src="~/Scripts/buzz/buzz.min.js"></script>
    <script language="JavaScript">
        var targetFormID = '#form1';

        var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );

        $(document).ready(function () {
            $("#CARD_NO").focus();
        });

       function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

       function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("UnWearMemo", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

       function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var CARD_NO = $('#CARD_NO').val();

                event.preventDefault();

                if (CARD_NO != '') {

                    if ($("#Tr" + CARD_NO).length > 0 || $("#@SCHOOL_NO" + CARD_NO).length >0 ) {
                        $('#ErrorStr').html('請勿重複刷卡，謝謝')
                        $('#ErrorDiv').show()

                        setTimeout(function () {
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorStr').val('')
                            $('#ErrorDiv').hide()
                        }, 2000);
                    }
                    else {

                        $('#CARD_NO').prop('readonly', true);

                        setTimeout(function () {
                            OnKeyinUse(CARD_NO)
                        });
                    }

                }
            }
        }

        function OnKeyinUse(CARD_NO) {

            var data = {
                "CARD_NO": CARD_NO,
                "CLASS_NO": $('#@Html.IdFor(m=>m.WhereCLASS_NO)').val(),
            };

            $.ajax({
                url: '@Url.Action("GetTagWearDetailsData", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {

                    $('#ErrorDiv').show()

                    if (html.indexOf('Error') == -1) {
                        $("#editorRows").prepend(html);
                        $('#ErrorStr').html('感應成功…')
                        SwipeOK()
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 300);
                    }
                    else {
                        $('#ErrorStr').html(html)
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 1400);
                    }

                }
            });
        }

         function SwipeOK() {

                 SwipeSound.play();
         }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }
    </script>
}