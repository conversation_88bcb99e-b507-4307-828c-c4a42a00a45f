﻿<!DOCTYPE html>
<html>
<head>

    @{
        ViewBag.Title = "登入";
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

        ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

        ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        List<BDMT01> SchoolList = db.BDMT01.OrderBy(school => school.SHORT_NAME).ToList();

    }

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @Styles.Render("~/Content/css")
    <link href="~/Content/css/EzCss.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss")" rel="stylesheet" />
    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
</head>
@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}
<body style="background-image:url('@Url.Content("~/Content/img/web-01.png")');background-repeat:repeat">
    <div class="visible-xs">
        <!---手機 -->
        <nav class="navbar navbar-phone navbar-fixed-top" role="navigation">
            <span class="btn-logo">
                <a href='@Url.Action("PortalIndex", "Home")' class="btn-logo-layout">
                    <img src="~/Content/img/web-student_png-16.png" class="img-responsive " alt="Responsive image" title="回台北e酷幣首頁" />
                </a>
            </span>
            <a role="button" class="btn-User" href="@Url.Action("LoginPage","Home")">
                <i class="fa fa-user" style="font-size:2.5em" title="會員登入"></i>
            </a>
        </nav>
        <div style="height:50px"></div>
    </div>

    <div>
        <div class="text-center">
            <div class="visible-xs">
                <!---手機 -->
                <img src='@Url.Content("~/Content/img/web-revise-phone0308-00.png")' class="img-responsive" alt="" />
            </div>
            <div class="hidden-xs">
                <a href='@Url.Action("PortalIndex", "Home")'>
                    <img src='@Url.Content("~/Content/img/web_0717_logo-11.png")' class="img-responsive" title="回台北e酷幣首頁" />
                </a>
            </div>
        </div>
        <br>

        <div class="containerEZ">
            @if (ViewBag.UrlReferrer != null)
            {
                <div class="text-center">
                    <h4 style="color:red">
                        <b>

                            @if (TempData["BackInfo"] != null)
                            {
                                string HtmlMsg1 = TempData["BackInfo"].ToString().Replace("\r\n", "<br />");

                                @Html.Raw(HtmlMsg1)

                            }
                            else
                            {
                                string str = ViewBag.UrlReferrer;

                                if (!str.Contains("ADDI10/_PageContent2"))
                                { <i class="glyphicon glyphicon-ban-circle"></i>
                                    @:登入逾時，請重新登入。

                                }


                            }
                        </b>
                    </h4>
                </div>
                <br>
            }
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    @Html.Partial("../Shared/_LoginPhone")
                </div>
            </div>
        </div>
    </div>
</body>
</html>