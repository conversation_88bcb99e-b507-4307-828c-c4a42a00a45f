﻿@model ADDI10StatisticsVoteDetailsViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.HiddenFor(x => x.OrdercColumn)
@Html.HiddenFor(x => x.SyntaxName)
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<div class="form-group">
    <label class="control-label">年級</label>
</div>
<div class="form-group">
    @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
</div>
<div class="form-group">
    <label class="control-label">班級</label>
</div>
<div class="form-group">
    @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
</div>
<div class="form-group">
    <label class="control-label">
        姓名
    </label>
</div>
<div class="form-group">
    @Html.EditorFor(m => m.WhereSearch, new { htmlAttributes = new { @class = "form-control" } })
</div>
<input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
<input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
<div class="Div-EZ-reader" id="form2">
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover">

                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(model => model.VotePeople.First().SHORT_NAME)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.VotePeople.First().SNAME)
                        </th>
                        <th onclick="doSort('CLASS_NO')">
                            @Html.DisplayNameFor(model => model.VotePeople.First().CLASS_NO)
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th onclick="doSort('SEAT_NO')">
                            @Html.DisplayNameFor(model => model.VotePeople.First().SEAT_NO)
                            <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th onclick="doSort('ANSWER_DATE')">
                            @Html.DisplayNameFor(model => model.VotePeople.First().ANSWER_DATE)
                            <img id="ANSWER_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.VotePeople)
                    {
                        <tr>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SNAME)
                            </td>
                            <td align="center">
                                @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                {
                                    <samp>-</samp>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                }
                            </td>
                            <td align="center">
                                @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                {
                                    <samp>-</samp>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.ANSWER_DATE)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
            <div style="height:15px"></div>
            <div class="btn-group btn-group-justified" role="group">
                共 @(Model.VotePeople.Count()) 人
            </div>
            <div style="height:25px"></div>
        </div>
    </div>
</div>
<script>
    function funAjax() {
        var whereGradetxt = $("#whereGrade").val();
        console.log(whereGradetxt);
        var whereCLASS_NOtxt = $("#whereCLASS_NO").val();
        var WhereSearchtxt = $("#WhereSearch").val();
        console.log(WhereSearchtxt);
        $.ajax({
            type: "GET",
                url: '@(Url.Action("StatisticsVotePeople", (string)ViewBag.BRE_NO))',
            data: {
                QUESTIONNAIRE_ID: '@Model.Topic.QUESTIONNAIRE_ID',
                Q_NUM: '@Model.Topic.Q_NUM',
                ANSWER: '@Model.ANSWER',
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
                whereGrade: whereGradetxt,
                whereCLASS_NO: whereCLASS_NOtxt,
                whereSearch: WhereSearchtxt
            },
                async: false,
                cache: false,
                dataType: 'html',
            success: function (data) {

                $("#cboxLoadedContent").html('');

                    //$(".Div-EZ-reader").css("background-color", "transparent");
                $("#cboxLoadedContent").html(data);
                }
            });
    }
    function doSort(SortCol) {

        var sort = "";
        sort = SortCol;
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        $('#OrdercColumn').val(SortCol);
        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {
                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("ASC");
            }
            else {
                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
        }
        else {
            $('#OrdercColumn').val(sort);
            $('#SyntaxName').val("Desc");
        }
        funAjax()
    }


</script>