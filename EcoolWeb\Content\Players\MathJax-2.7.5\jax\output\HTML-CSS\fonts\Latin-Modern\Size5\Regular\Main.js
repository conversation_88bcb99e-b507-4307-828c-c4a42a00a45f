/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Size5/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Size5={directory:"Size5/Regular",family:"LatinModernMathJax_Size5",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u2223",32:[0,0,332,0,0],40:[1296,796,663,201,608],41:[1296,796,663,55,462],47:[2179,1679,1557,57,1502],91:[1300,800,499,230,478],92:[2179,1679,1557,56,1501],93:[1300,800,499,21,269],123:[1300,800,707,100,608],124:[1501,1001,278,107,172],125:[1300,800,707,99,607],160:[0,0,332,0,0],770:[748,-570,1320,0,1320],771:[766,-534,1335,0,1335],774:[742,-575,1341,0,1341],780:[742,-564,1320,0,1320],785:[760,-592,1341,0,1341],812:[-96,274,1320,0,1320],813:[-108,286,1320,0,1320],814:[-96,264,1341,0,1341],815:[-118,286,1341,0,1341],816:[-118,350,1335,0,1335],8214:[1501,1001,396,57,341],8260:[2179,1679,1557,57,1502],8425:[757,-521,2235,0,2235],8739:[1501,1001,278,107,172],8741:[1501,1001,396,57,341],8968:[1300,800,555,203,528],8969:[1300,800,555,27,352],8970:[1300,800,555,203,528],8971:[1300,800,555,27,352],9001:[1300,800,677,165,623],9002:[1300,800,677,54,512],9140:[757,-521,2235,0,2235],9141:[-91,327,2235,0,2235],9180:[780,-506,3020,0,3020],9181:[-76,350,3020,0,3020],9182:[838,-500,3000,0,3000],9183:[-70,408,3000,0,3000],9184:[866,-607,3068,0,3068],9185:[-177,436,3068,0,3068],10214:[1300,800,750,247,727],10215:[1300,800,750,23,503],10216:[1300,800,677,165,623],10217:[1300,800,677,54,512],10218:[1300,800,1011,165,957],10219:[1300,800,1011,54,846],10222:[1320,820,485,206,429],10223:[1320,820,485,56,279]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Size5"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size5/Regular/Main.js"]);
