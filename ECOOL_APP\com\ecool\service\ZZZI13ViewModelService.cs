﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;

namespace com.ecool.service
{
    public class ZZZI13ViewModelService
    {
        public string ErrorMsg;

        #region 取得帳號與角色清單
        /// <summary>
        /// 取得帳號與角色清單
        /// </summary>
        /// <param name="SCHOOL_NO">學校代碼</param>
        /// <param name="USER_NO">帳號代碼</param>
        /// <returns>List<ZZZI12ViewModel></returns>
        public List<ZZZI13ViewModel> USP_ZZZI13ViewModel_QUERY(string SCHOOL_NO, string USER_NO, string ROLE_ID, string NAME, ECOOL_APP.UserProfile User, bool IsTeacher=false)
        {

            List<ZZZI13ViewModel> list_data = new List<ZZZI13ViewModel>();

            ZZZI13ViewModel ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {

                sb.Append("   SELECT A.SCHOOL_NO,A.USER_NO,A.NAME,T.CLASS_NO,R.ROLE_ID,R.ROLE_NAME ");
                sb.Append(" ,CASE WHEN ISNULL(J.ROLE_ID,'')<>'' Then 'true' Else 'false' End as Checked  ");
                sb.Append("  FROM HRMT01 A (NOLOCK) ");
                sb.Append("  CROSS JOIN HRMT24 R  (NOLOCK) ");
                sb.Append("  LEFT OUTER JOIN HRMT25 J  (NOLOCK) ON A.SCHOOL_NO=J.SCHOOL_NO AND A.USER_NO=J.USER_NO AND R.ROLE_ID=J.ROLE_ID ");
                sb.Append("  LEFT OUTER JOIN HRMT03 T (NOLOCK) ON  A.SCHOOL_NO=T.SCHOOL_NO AND A.USER_NO=T.TEACHER_NO ");

                sb.Append(" Where A.SCHOOL_NO+'_'+A.USER_NO IN (SELECT M.SCHOOL_NO+'_'+M.USER_NO FROM HRMT25 M (NOLOCK)");
                sb.Append("                                     INNER JOIN HRMT24 MR (NOLOCK) ON M.ROLE_ID =MR.ROLE_ID "); 
                sb.AppendFormat("                               WHERE MR.ROLE_LEVEL>={0}) ", User.ROLE_LEVEL);
                sb.AppendFormat(" AND R.ROLE_LEVEL>={0} ", User.ROLE_LEVEL);
                sb.Append(" AND R.ROLE_TYPE<>4 and A.USER_TYPE<>'P' AND A.USER_STATUS=1 ");

                if (IsTeacher)
                {
                    sb.Append(" AND ISNULL(T.TEACHER_NO,'')<>'' ");
                }

                if (USER_NO != string.Empty)
                {
                    sb.AppendFormat(" AND A.USER_NO ='{0}' ", USER_NO);
                }

                if (SCHOOL_NO != string.Empty)
                {
                    sb.AppendFormat(" AND A.SCHOOL_NO ='{0}' ", SCHOOL_NO);
                }

                if (ROLE_ID != string.Empty)
                {
                    sb.AppendFormat(" AND J.ROLE_ID='{0}' ", ROLE_ID);
                }


                sb.Append(" ORDER BY A.SCHOOL_NO, ISNULL(T.CLASS_NO ,'z'),A.USER_NO,A.NAME,R.ROLE_ID,R.ROLE_NAME ");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());

                string SCHOOL_NO_USER_NO = string.Empty;

                foreach (DataRow dr in dt.Rows)
                {
                    if (SCHOOL_NO_USER_NO != dr["SCHOOL_NO"].ToString() + "_" + dr["USER_NO"].ToString())
                    {
                        ReturnData = new ZZZI13ViewModel();
                        ReturnData.Details_List = new List<ZZZI13ViewModel_D>();

                        ReturnData.SCHOOL_NO = dr["SCHOOL_NO"].ToString();
                        ReturnData.USER_NO = dr["USER_NO"].ToString();
                        ReturnData.NAME = dr["NAME"].ToString();
                        ReturnData.CLASS_NO = (dr["CLASS_NO"] == DBNull.Value ? "" : (string)dr["CLASS_NO"]); 

                        list_data.Add(ReturnData);
                    }

                    ZZZI13ViewModel_D Details = new ZZZI13ViewModel_D();

                    Details.ROLE_ID = dr["ROLE_ID"].ToString();
                    Details.ROLE_NAME = dr["ROLE_NAME"].ToString();
                    Details.Checked = Convert.ToBoolean(dr["Checked"]);
                    ReturnData.Details_List.Add(Details);

                    SCHOOL_NO_USER_NO = dr["SCHOOL_NO"].ToString() + "_" + dr["USER_NO"].ToString();

                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        #endregion

        #region 新增帳號角色
        /// <summary>
        ///新增帳號角色
        /// </summary>
        /// <param name="Date">uHRMT25</param>
        public void AddDate(uHRMT25 Date)
        {

            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {

                IDbCommand cmd = new SqlCommand(@"INSERT INTO HRMT25(SCHOOL_NO,USER_NO,ROLE_ID,CHG_PERSON,CHG_DATE,DEFAULT_YN)
                                                 VALUES (@SCHOOL_NO,@USER_NO,@ROLE_ID,@CHG_PERSON,@CHG_DATE,@DEFAULT_YN)");

                cmd.Connection = conn;
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;

                cmd.Parameters.Add(
                (Date.SCHOOL_NO == null)
                ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
                : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO));

                cmd.Parameters.Add(
                (Date.USER_NO == null)
                ? new SqlParameter("@USER_NO", DBNull.Value)
                : new SqlParameter("@USER_NO", Date.USER_NO));

                cmd.Parameters.Add(
                (Date.ROLE_ID == null)
                ? new SqlParameter("@ROLE_ID", DBNull.Value)
                : new SqlParameter("@ROLE_ID", Date.ROLE_ID));


                cmd.Parameters.Add(
                (Date.CHG_PERSON == null)
                ? new SqlParameter("@CHG_PERSON", DBNull.Value)
                : new SqlParameter("@CHG_PERSON", Date.CHG_PERSON));

                cmd.Parameters.Add(
                (Date.CHG_DATE == DateTime.MinValue)
                ? new SqlParameter("@CHG_DATE", DateTime.MinValue)
                : new SqlParameter("@CHG_DATE", Date.CHG_DATE));

                cmd.Parameters.Add(
                (Date.DEFAULT_YN == null)
                ? new SqlParameter("@DEFAULT_YN", DBNull.Value)
                : new SqlParameter("@DEFAULT_YN", Date.DEFAULT_YN));


                try
                {
                    cmd.ExecuteNonQuery();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "異動資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion

        #region 刪除帳號角色
        public void DelDate(uHRMT25 Date)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {

                IDbCommand cmd = new SqlCommand(@"DELETE HRMT25 Where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO and ROLE_ID=@ROLE_ID");

                cmd.Connection = conn;
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;

                cmd.Parameters.Add(
                (Date.SCHOOL_NO == null)
                ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
                : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO));

                cmd.Parameters.Add(
                (Date.USER_NO == null)
                ? new SqlParameter("@USER_NO", DBNull.Value)
                : new SqlParameter("@USER_NO", Date.USER_NO));


                cmd.Parameters.Add(
                (Date.ROLE_ID == null)
                ? new SqlParameter("@ROLE_ID", DBNull.Value)
                : new SqlParameter("@ROLE_ID", Date.ROLE_ID));
                try
                {
                    cmd.ExecuteNonQuery();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "異動資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion


        #region 異動角色時更新 DEFAULT_YN
        /// <summary>
        /// 異動角色時更新 DEFAULT_YN
        /// </summary>
        /// <param name="SCHOOL_NO">SCHOOL_NO</param>
        /// <param name="USER_NO">USER_NO</param>
        public void DEFAULT_YN(string SCHOOL_NO, string USER_NO)
        {

            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {

                IDbCommand cmd = new SqlCommand(@"Exec dbo.sp_SetHRMT25_DEFAULT_YN @SCHOOL_NO,@USER_NO ");

                cmd.Connection = conn;
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;

                cmd.Parameters.Add(
                (SCHOOL_NO == null)
                ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
                : new SqlParameter("@SCHOOL_NO", SCHOOL_NO));

                cmd.Parameters.Add(
                (USER_NO == null)
                ? new SqlParameter("@USER_NO", DBNull.Value)
                : new SqlParameter("@USER_NO", USER_NO));


                try
                {
                    cmd.ExecuteNonQuery();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "Exec dbo.sp_SetHRMT25_DEFAULT_YN 失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion

    }
}
