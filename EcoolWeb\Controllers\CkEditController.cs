﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class CkEditController : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "CkEdit";

        /// <summary>
        /// 功能名稱
        /// </summary>
        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CkEditService Service = new CkEditService();
        private UserProfile user;

        private string SCHOOL_NO;

        public static string GetBrowseUrlName(string REF_TYPE)
        {
            if (REF_TYPE == BDMT03.REF_TYPEVal.Audio)
            {
                return "AudioBrowseUrl";
            }
            else if (REF_TYPE == BDMT03.REF_TYPEVal.Vedio)
            {
                return "VedioBrowseUrl";
            }
            else
            {
                return "ImageBrowseUrl";
            }
        }

        public ActionResult AudioBrowseUrl(string CKEditorFuncNum, string CKEditor, string langCode
       , string Controller, string Action)
        {
            CkEditIndexViewModel model = new CkEditIndexViewModel();
            if (model.Search == null) model.Search = new CkEditSearchViewModel();

            model.Search.SeeREF_TYPE = BDMT03.REF_TYPEVal.Audio;
            model.Search.CKEditorFuncNum = CKEditorFuncNum;
            model.Search.CKEditor = CKEditor;
            model.Search.langCode = langCode;
            model.Search.BackController = Controller;
            model.Search.BackAction = Action;

            return View("BrowseUrl", model);
        }

        public ActionResult VedioBrowseUrl(string CKEditorFuncNum, string CKEditor, string langCode
        , string Controller, string Action)
        {
            CkEditIndexViewModel model = new CkEditIndexViewModel();
            if (model.Search == null) model.Search = new CkEditSearchViewModel();

            model.Search.SeeREF_TYPE = BDMT03.REF_TYPEVal.Vedio;
            model.Search.CKEditorFuncNum = CKEditorFuncNum;
            model.Search.CKEditor = CKEditor;
            model.Search.langCode = langCode;
            model.Search.BackController = Controller;
            model.Search.BackAction = Action;

            return View("BrowseUrl", model);
        }

        public ActionResult ImageBrowseUrl(string CKEditorFuncNum, string CKEditor, string langCode
        , string Controller, string Action)
        {
            CkEditIndexViewModel model = new CkEditIndexViewModel();
            if (model.Search == null) model.Search = new CkEditSearchViewModel();

            model.Search.SeeREF_TYPE = BDMT03.REF_TYPEVal.Image;
            model.Search.CKEditorFuncNum = CKEditorFuncNum;
            model.Search.CKEditor = CKEditor;
            model.Search.langCode = langCode;
            model.Search.BackController = Controller;
            model.Search.BackAction = Action;

            return View("BrowseUrl", model);
        }

        public ActionResult _PageBrowseUrl(CkEditIndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new CkEditIndexViewModel();
            if (model.Search == null) model.Search = new CkEditSearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.SeeSCHOOL_NO))
            {
                model.Search.SeeSCHOOL_NO = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.Search.SeeUSER_NO))
            {
                model.Search.SeeUSER_NO = user.USER_NO;
            }

            model = Service.GetSourceData(model, ref db);
            return PartialView(model);
        }

        public ActionResult EditCkEdit(CkEditEditViewModel model)
        {
            this.Shared();
            if (model == null) model = new CkEditEditViewModel();
            if (model.Search == null) model.Search = new CkEditSearchViewModel();
            model = Service.GetCkEditSourceEdit(model, ref db);

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditCkEditSave(CkEditEditViewModel model, List<HttpPostedFileBase> files)
        {
            this.Shared();
            if (model == null) model = new CkEditEditViewModel();
            if (model.Search == null) model.Search = new CkEditSearchViewModel();

            if (files == null && (string.IsNullOrWhiteSpace(model.Data.FILE))) ModelState.AddModelError("files", "請上傳檔案");
            else if (files != null)
            {
                if (model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Audio)
                {
                    Regex regexCode = new Regex(@".*\.(mp3)");
                    bool isFormat = true;

                    foreach (var Thisfile in files)
                    {
                        if (Thisfile != null && Thisfile.ContentLength > 0)
                        {
                            string fileNameExtension = Path.GetExtension(Thisfile.FileName);

                            if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                            {
                                isFormat = false;
                            }
                        }
                    }

                    if (isFormat == false)
                    {
                        ModelState.AddModelError("files", "請上傳Audio格式為mp3");
                    }
                }
                else if (model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Vedio)
                {
                    Regex regexCode = new Regex(@".*\.(mp4)");
                    bool isFormat = true;

                    foreach (var Thisfile in files)
                    {
                        if (Thisfile != null && Thisfile.ContentLength > 0)
                        {
                            string fileNameExtension = Path.GetExtension(Thisfile.FileName);

                            if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                            {
                                isFormat = false;
                            }
                        }
                    }

                    if (isFormat == false)
                    {
                        ModelState.AddModelError("files", "請上傳Vedio格式為mp4");
                    }
                }
                else if (model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Image)
                {
                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                    bool isFormat = true;

                    foreach (var Thisfile in files)
                    {
                        if (Thisfile != null && Thisfile.ContentLength > 0)
                        {
                            string fileNameExtension = Path.GetExtension(Thisfile.FileName);

                            if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                            {
                                isFormat = false;
                            }
                        }
                    }

                    if (isFormat == false)
                    {
                        ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                    }
                }
            }

            if (ModelState.IsValid == false)
            {
                TempData["StatusMessage"] = "警告!!輸入的內容有錯誤";
                return View("EditCkEdit", model);
            }

            string Message = string.Empty;
            bool OK = Service.SaveCkEdit(model, files, user, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "新增完成";

                string CkEditorAction = GetBrowseUrlName(model.Search.SeeREF_TYPE);

                return RedirectToAction(CkEditorAction, new
                {
                    CKEditorFuncNum = model.Search.CKEditorFuncNum,
                    CKEditor = model.Search.CKEditor,
                    langCode = model.Search.langCode,
                    Controller = model.Search.BackController,
                    Action = model.Search.BackAction
                });
            }

            TempData["StatusMessage"] = Message;

            return View("EditCkEdit", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DelCkEditSave(CkEditEditViewModel model)
        {
            this.Shared();
            if (model == null) model = new CkEditEditViewModel();
            if (model.Search == null) model.Search = new CkEditSearchViewModel();

            if (ModelState.IsValid == false)
            {
                TempData["StatusMessage"] = "警告!!輸入的內容有錯誤";
                return View("EditCkEdit", model);
            }

            string Message = string.Empty;
            bool OK = Service.DelCkEdit(model, user, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "刪除完成";

                string CkEditorAction = GetBrowseUrlName(model.Search.SeeREF_TYPE);

                return RedirectToAction(CkEditorAction, new
                {
                    CKEditorFuncNum = model.Search.CKEditorFuncNum,
                    CKEditor = model.Search.CKEditor,
                    langCode = model.Search.langCode,
                    Controller = model.Search.BackController,
                    Action = model.Search.BackAction
                });
            }

            TempData["StatusMessage"] = Message;

            return View("EditCkEdit", model);
        }

        #region Shared

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = (string.IsNullOrWhiteSpace(Panel_Title)) ? Bre_Name : Bre_Name + " - " + Panel_Title;
            ViewBag.Title = ViewBag.Panel_Title;
            user = UserProfileHelper.Get();
            CheckUser();
            SCHOOL_NO = (user != null) ? user.SCHOOL_NO : UserProfileHelper.GetSchoolNo();
        }

        private ActionResult CheckUser()
        {
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            return null;
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}