﻿
@model GameLeveViewModel
@{
    Layout = "~/Views/Shared/_LayoutGame.cshtml";

}

<div class="panel panel-ZZZ" name="TOP" id="cboxLoadedContent">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, "中獎名單")
    </div>
   
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">

            <thead class="bg-primary-dark text-black">
                <tr class="thead-primary">

                    <th class="text-nowrap">序號</th>

                    <th class="text-nowrap" onclick="doSort('CLASS_NO')">
                        @Html.DisplayNameFor(model => model.WinnerItemDetails.FirstOrDefault().CLASS_NO)
                        <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>

                    <th class="text-nowrap" onclick="doSort('SEAT_NO')">
                        @Html.DisplayNameFor(model => model.WinnerItemDetails.FirstOrDefault().SEAT_NO)
                        <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th class="text-nowrap">
                        @Html.DisplayNameFor(model => model.WinnerItemDetails.FirstOrDefault().NAME)
                    </th>
                    <th>  @Html.DisplayNameFor(model => model.WinnerItemDetails.FirstOrDefault().PrizeName)</th>
                    <th class="text-nowrap" onclick="doSort('CHARGETIME')">
                        兌換時間
                        <img id="CHARGETIME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>


                </tr>


            </thead>
            <tbody>
                @{var i = 1; }
                @if (Model.WinnerItemDetails?.Count() > 0)
                {

                    foreach (var item in Model.WinnerItemDetails)
                    {

                        <tr class="text-center">
                            <td align="center"> @i </td>

                            <td align="center">
                                @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                {
                                    <samp>-</samp>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                }
                            </td>
                            <td align="center">
                                @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                {
                                    <samp>-</samp>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td>   @Html.DisplayFor(modelItem => item.PrizeName)</td>
                            <td align="center">


                                @Html.DisplayFor(modelItem => item.UpdateDate)

                            </td>


                        </tr>
                        i++;
                    }
                }
            </tbody>

        </table>


    </div>
</div>

