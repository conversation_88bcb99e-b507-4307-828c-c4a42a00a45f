﻿using AutoMapper;
using <PERSON>pper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class CER002Service
    {
        #region 列表資料

        public IQueryable<CER002ListViewModel> GetMyAccreditationListData(string SCHOOL_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
            string sSQL = $@" select a.ACCREDITATION_ID+'_'+b.ITEM_NO as ACCREDITATION_ID_ITEM_NO, a.ACCREDITATION_ID, a.IsText ,b.ITEM_NO,a.ACCREDITATION_TYPE,c.TYPE_NAME, a.ACCREDITATION_NAME, b.SUBJECT, b.CONTENT
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS , p.PersonText
				                ,(select top 1  vm.VERIFIER_NAME+'，'+  CONVERT(nvarchar(10),vm.VERIFIED_DATE ,111)
								                  from CERT04_V_M vm (nolock)
								                  join CERT04_V_D vd  (nolock) on vm.A_VERIFIER_ID =vm.A_VERIFIER_ID
								                  where vm.ACCREDITATION_ID=a.ACCREDITATION_ID and vm.ITEM_NO=b.ITEM_NO and vd.SCHOOL_NO=@SCHOOL_NO  and vd.USER_NO=@USER_NO and vd.O_IS_PASS!=vd.IS_PASS and vd.IS_PASS =1
								                  order by vm.VERIFIED_DATE desc) as VERIFIER
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='{SharedGlobal.N}'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO and p.USER_NO=@USER_NO
                                where a.SCHOOL_NO = @SCHOOL_NO and a.DEL_YN='{SharedGlobal.N}'
                                order by c.TYPE_NAME,a.ACCREDITATION_NAME,b.ITEM_NO";
            var temp = db.Database.Connection.Query<CER002ListViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = SCHOOL_NO,
                 USER_NO = USER_NO,
             }).AsQueryable();
            return temp;
        }
        public IQueryable<CER002ListViewModel> GetMyAccreditationListSCHOOLData(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {

            string sSQL = $@" select a.ACCREDITATION_ID+'_'+b.ITEM_NO as ACCREDITATION_ID_ITEM_NO, a.ACCREDITATION_ID, a.IsText ,b.ITEM_NO,a.ACCREDITATION_TYPE,c.TYPE_NAME, a.ACCREDITATION_NAME, b.SUBJECT, b.CONTENT,p.USER_NO,kk.CLASS_NO,kk.GRADE,kk.NAME
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS , p.PersonText
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS , p.PersonText
				                ,(select top 1  vm.VERIFIER_NAME+'，'+  CONVERT(nvarchar(10),vm.VERIFIED_DATE ,111)
								                  from CERT04_V_M vm (nolock)
								                  join CERT04_V_D vd  (nolock) on vm.A_VERIFIER_ID =vm.A_VERIFIER_ID
								                  where vm.ACCREDITATION_ID=a.ACCREDITATION_ID and vm.ITEM_NO=b.ITEM_NO and vd.SCHOOL_NO=@SCHOOL_NO and vd.O_IS_PASS!=vd.IS_PASS and vd.IS_PASS =1
								                  order by vm.VERIFIED_DATE desc) as VERIFIER
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='{SharedGlobal.N}'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO 
                                 		join HRMT01 kk on kk.SCHOOL_NO= @SCHOOL_NO  and kk.USER_NO=p.USER_NO                               
                                 where a.SCHOOL_NO = @SCHOOL_NO and a.DEL_YN='{SharedGlobal.N}'
                                order by c.TYPE_NAME,a.ACCREDITATION_NAME,b.ITEM_NO";
            var temp = db.Database.Connection.Query<CER002ListViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = SCHOOL_NO,
            
             }).AsQueryable();
            return temp;
        }
        public CER002IndexViewModel GetListData(CER002IndexViewModel model, string SCHOOL_NO, string USER_NO, byte? GRADE, ref ECOOL_DEVEntities db)
        {
            
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(CER002ListViewModel.TYPE_NAME);
                model.SortType = PageGlobal.SortType.DESC;
            }
            IQueryable<CER002ListViewModel> temp = null;
            if (!string.IsNullOrWhiteSpace(USER_NO))
            {

                temp = this.GetMyAccreditationListData(SCHOOL_NO, USER_NO, ref db);

            }

            else {
                temp = this.GetMyAccreditationListSCHOOLData(SCHOOL_NO, ref db);

                if (model != null) {
                    byte? gradebyte = null;
                    if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereGrade)) {
                        gradebyte = byte.Parse(model.ListDataHRMT01.whereGrade);
                    }
                    temp = temp.Where(x => x.GRADE == gradebyte).AsQueryable();

                }
                if (!string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereClass_No))
                {
                    temp = temp.Where(x => x.CLASS_NO == model.ListDataHRMT01.whereClass_No).AsQueryable();
                    
                }
                if (!string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereKeyword))
                {
                    temp = temp.Where(x => x.NAME.Contains(model.ListDataHRMT01.whereKeyword) || x.USER_NO.Contains(model.ListDataHRMT01.whereKeyword)).AsQueryable();


                }
               

          

                }
            if (!string.IsNullOrWhiteSpace(model.SearchContent))
            {
                temp = temp.Where(a => a.TYPE_NAME.Contains(model.SearchContent.Trim()));
            }

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
            {
                temp = temp.Where(a => a.ACCREDITATION_TYPE == model.WhereACCREDITATION_TYPE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_NAME))
            {
                temp = temp.Where(a => a.ACCREDITATION_ID == model.WhereACCREDITATION_NAME);
            }

            if (model?.WhereGRADE_SEMESTER_TYPE > 0)
            {
                //預設值
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                var cERT03s = db.CERT03_G.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.GRADE == GRADE && x.SEMESTER == Semesters).AsEnumerable().Select(a => $"{a.ACCREDITATION_ID}_{a.ITEM_NO}").ToList();

                temp = temp.Where(a => cERT03s.Contains(a.ACCREDITATION_ID_ITEM_NO));
            }

            temp = temp.OrderBy(x => x.GRADE_SEMESTERs).OrderBy(model.OrderByColumnName, model.SortType);
            model.PageSize = int.MaxValue;
            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.ListData.PageNumber;

            var cERT03G = db.CERT03_G.Where(x => x.SCHOOL_NO == SCHOOL_NO).ToListNoLock();

            if (cERT03G.Count > 0)
            {
                if (model.OrderByColumnName == nameof(CER002ListViewModel.TYPE_NAME))
                {
                    model.ListData.Select(a =>
                    {
                        a.GRADE_SEMESTERs = cERT03G.Where(x => x.ACCREDITATION_ID == a.ACCREDITATION_ID && x.ITEM_NO == a.ITEM_NO)
                        .Select(x => $"{HRMT01.ParserGrade((byte)x.GRADE)}{HRMT01.ParserSemester((byte)x.SEMESTER)}").ToList();
                        a.StringGRADE_SEMESTERs = String.Join(", ", a.GRADE_SEMESTERs.ToArray());
                        return a;
                    }).OrderBy(x => x.TYPE_NAME).ThenBy(x => x.StringGRADE_SEMESTERs).ToList();
                }
                else
                {
                    model.ListData.Select(a =>
                    {
                        a.GRADE_SEMESTERs = cERT03G.Where(x => x.ACCREDITATION_ID == a.ACCREDITATION_ID && x.ITEM_NO == a.ITEM_NO)
                        .Select(x => $"{HRMT01.ParserGrade((byte)x.GRADE)}{HRMT01.ParserSemester((byte)x.SEMESTER)}").ToList();
                        return a;
                    }).ToList();
                }
            }

            return model;
        }

        #endregion 列表資料
    }
}