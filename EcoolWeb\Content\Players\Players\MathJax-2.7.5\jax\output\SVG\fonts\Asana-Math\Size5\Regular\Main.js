/*
 *  /MathJax/jax/output/SVG/fonts/Asana-Math/Size5/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.AsanaMathJax_Size5={directory:"Size5/Regular",family:"AsanaMathJax_Size5",id:"ASANAMATHSIZE5",32:[0,0,249,0,0,""],124:[1673,1039,288,85,203,"203 -1039h-118v2712h118v-2712"],770:[783,-627,3026,0,3026,"3026 627l-1514 93l-1512 -93v47l1512 109l1514 -109v-47"],771:[772,-642,2797,0,2797,"2797 772c-106 -99 -576 -130 -895 -130c-339 0 -939 67 -1221 67c-207 0 -551 -10 -618 -67h-63c106 99 576 130 895 130c333 0 929 -67 1211 -67c207 0 561 10 630 67h61"],780:[792,-627,2940,0,2940,"2940 792v-47l-1472 -118l-1468 118v47l1470 -101"],10181:[1260,1803,450,53,397,"397 -1803c-157 0 -344 278 -344 838c0 772 218 1356 218 2103c0 49 -43 88 -91 88c-28 0 -56 -14 -73 -36c30 -1 50 -24 50 -52c0 -31 -23 -52 -53 -52s-51 21 -51 52c0 69 59 122 127 122s127 -53 127 -122c0 -737 -155 -1372 -155 -2103c0 -419 114 -805 245 -805v-33 "],10182:[1260,1803,450,53,397,"397 -965c0 -560 -187 -838 -344 -838v33c131 0 245 386 245 805c0 731 -155 1366 -155 2103c0 69 59 122 127 122s127 -53 127 -122c0 -31 -21 -52 -51 -52s-53 21 -53 52c0 28 20 51 50 52c-17 22 -45 36 -73 36c-48 0 -91 -39 -91 -88c0 -747 218 -1331 218 -2103"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size5/Regular/Main.js");
