﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'div', 'hi', {
	IdInputLabel: 'Id',
	advisoryTitleInputLabel: 'परामर्श शीर्शक',
	cssClassInputLabel: 'स्टाइल-शीट क्लास',
	edit: 'Edit Div', // MISSING
	inlineStyleInputLabel: 'Inline Style', // MISSING
	langDirLTRLabel: 'बायें से दायें (LTR)',
	langDirLabel: 'भाषा लिखने की दिशा',
	langDirRTLLabel: 'दायें से बायें (RTL)',
	languageCodeInputLabel: ' Language Code', // MISSING
	remove: 'Remove Div', // MISSING
	styleSelectLabel: 'स्टाइल',
	title: 'Create Div Container', // MISSING
	toolbar: 'Create Div Container' // MISSING
} );
