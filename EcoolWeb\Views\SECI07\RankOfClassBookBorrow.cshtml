﻿@model ECOOL_APP.com.ecool.Models.DTO.ClassBorrowBookRankViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

    int BookPageCount = 0;

}

@Html.Partial("_Title_Secondary")
@{
    Html.RenderAction("_Menu", new { NowAction = "RankOfClassBookBorrow" });
}

@using (Html.BeginForm("RankOfClassBookBorrow", "SECI07", FormMethod.Post, new { id = "SECI07", name = "SECI07" }))
{
    @Html.HiddenFor(m => m.OrdercColumn)
    <script>
        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            document.SECI07.submit();
        }
    </script>

    <div class="row">

        <div class="col-md-12">
            <div>
                @Html.LabelFor(m => m.Where_SYEAR, new { @class = "col-md-1" })
            </div>   <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
            </div>
            <div>     @Html.LabelFor(m => m.Where_MONTH, new { @class = "col-md-1" }) </div>  <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_MONTH, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control", @onchange = "this.form.submit();" })
            </div>
        </div>

    </div>
}
<br />

@if (ViewBag.SYearItems == null)
{
    <div>無任何借閱資料</div>
}
else if (string.IsNullOrWhiteSpace(Model.Where_SYEAR))
{
    <div>未輸入學年度</div>
}
else
{
    if (Model.ClassRankList != null && Model.ClassRankList.Count > 0)
    {
        if (Model.ClassRankList.Where(x => x.CLASSQTY > 0).Count() > 0)
        {
            <table class="table table-responsive table-striped table-hover text-center">
                <thead>
                    <tr>

                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                            @Html.DisplayNameFor(m => m.ClassRankList.FirstOrDefault().CLASS_NO)
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.ClassRankList.FirstOrDefault().CLASSQTY)
                        </th>
                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK');">
                            @Html.DisplayNameFor(m => m.ClassRankList.FirstOrDefault().BORROW_BOOK_COUNT)
                            <img id="RANK" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK');">
                            @Html.DisplayNameFor(m => m.ClassRankList.FirstOrDefault().RANK)
                            <img id="RANK" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('AVG_RANK');">
                            @Html.DisplayNameFor(m => m.ClassRankList.FirstOrDefault().BORROW_BOOK_AVG)
                            <img id="AVG_RANK" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('AVG_RANK');">
                            @Html.DisplayNameFor(m => m.ClassRankList.FirstOrDefault().AVG_RANK)
                            <img id="AVG_RANK" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.ClassRankList)
                    {
                        <tr>

                            <td>@item.CLASS_NO</td>
                            <td>@item.CLASSQTY</td>
                            <td>@item.BORROW_BOOK_COUNT</td>
                            <td>@item.RANK</td>
                            <td>@item.BORROW_BOOK_AVG</td>
                            <td>@item.AVG_RANK</td>
                        </tr>
                        BookPageCount += item.BORROW_BOOK_COUNT;
                    }
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger text-center">
                                本頁總計: @BookPageCount 本書　　
                            </div>
                        </td>
                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger text-center">
                                平均
                                @Model.ClassRankList.Average(a => a.BORROW_BOOK_AVG).ToString("#0.0") 本書
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        }
        else
        {
            <div>資料量不足</div>
        }
    }
    else
    {
        <div>資料量不足</div>
    }
}

