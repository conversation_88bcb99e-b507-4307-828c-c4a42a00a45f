﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 護照補登
    /// </summary>
    [SessionExpire]
    [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
    public class CERI05Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "CERI05";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CERI05Service Service = new CERI05Service();
        private CERI02Service cERI02Service = new CERI02Service();
        private CERI01Service eRI01Service = new CERI01Service();

        public ActionResult Index()
        {
            this.Shared();
            return View();
        }
        public ActionResult Index2(CERI04IndexViewModel model)
        {
            this.Shared();

            if (eRI01Service.IsAccreditationTypeforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI01")}」未設定，請先行設定，才執行此作業。";
            }
            else if (cERI02Service.IsAccreditationforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI02")}」未設定，請先行設定，才執行此作業。";
            }

            return View(model);
        }
        public ActionResult Index3(CERI04IndexViewModel model)
        {
            this.Shared();

            if (eRI01Service.IsAccreditationTypeforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI01")}」未設定，請先行設定，才執行此作業。";
            }
            else if (cERI02Service.IsAccreditationforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI02")}」未設定，請先行設定，才執行此作業。";
            }

            return View(model);
        }
        public ActionResult _ADDDetail(CERI05EditAccreditationtViewModel model)
        {
            return PartialView(model);
        }
        public ActionResult _PageContent3(CERI04IndexViewModel model)
        {
            this.Shared();
            CERI04Service Service = new CERI04Service();
            if (model == null) model = new CERI04IndexViewModel();

            if (model.WhereSYEAR == null)
            {
                model.WhereSYEAR = SysHelper.GetNowSYear(DateTime.Now);
            }

            if (user.TEACH_CLASS_NO != null) {
                model.WhereCLASS_NO = user.TEACH_CLASS_NO;

            }
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");
            var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db);

            if (!string.IsNullOrWhiteSpace(user?.TEACH_CLASS_NO))
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);
            ViewBag.ClassItems = ClassItems.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            model = Service.GetListData1(model, user, ref db);
            return PartialView(model);
        }
        public ActionResult _PageContent2(CERI04IndexViewModel model)
        {
            this.Shared();
             CERI04Service Service = new CERI04Service();
            if (model == null) model = new CERI04IndexViewModel();

            if (model.WhereSYEAR == null)
            {
                model.WhereSYEAR = SysHelper.GetNowSYear(DateTime.Now);
            }

            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db)
             .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            model = Service.GetListData1(model, user, ref db);
            return PartialView(model);
        }
        #region 全校補登
        public ActionResult Edit(CERI04EditViewModel model)
        {
            this.Shared();
            string Message = string.Empty;
            if (model == null) model = new CERI04EditViewModel();
            bool GetCERT05Bool = true;
            GetCERT05Bool = this.Service.GetCERT05(SCHOOL_NO, model.ThisACCREDITATION_ID, model.ThisITEM_NO, user, ref db);
            if (GetCERT05Bool)
            {
                var Result = this.Service.SaveSchoolData(SCHOOL_NO, model.ThisACCREDITATION_ID, model.ThisITEM_NO, user, model.ThisCLASS_NO, ref db);
                string str = "";
                string classstr = "";
                if (Result.Success)
                {
                    if (!string.IsNullOrWhiteSpace(model.ThisCLASS_NO)) {

                        classstr = model.ThisCLASS_NO;
                    }

                    str = db.CERT02.Where(x => x.ACCREDITATION_ID == model.ThisACCREDITATION_ID).Select(x => x.ACCREDITATION_NAME).FirstOrDefault();
                    TempData[SharedGlobal.StatusMessageName] = "完成" + classstr + str + "細項補登。 2.只有打勾沒有加點。";

                    return RedirectToAction(nameof(CERI05Controller.Index2), model);
                }
                

                Message += Result.Message;
                TempData[SharedGlobal.StatusMessageName] = Message;
            }
            else {
                TempData[SharedGlobal.StatusMessageName] = "此項目已補登過囉~~";

                return RedirectToAction(nameof(CERI05Controller.Index2), model);


            }
            return RedirectToAction(nameof(CERI05Controller.Index2), model);
            

          

         ///   model = this.Service.GetEditData(model, ref db);
         
        }
        public ActionResult SchoolIndex()
        {
            this.Shared();
            string Message = string.Empty;

            var Result = this.Service.SaveSchoolDataItem(SCHOOL_NO, "","",user, ref db);

            if (Result.Success)
            {
                TempData[SharedGlobal.StatusMessageName] = "完成全校補登：1.「全校」本學期之前的所有指標都打勾(除文字護照以外) 。 2.只有打勾沒有加點。";

                return RedirectToAction(nameof(CERI05Controller.Index));
            }

            Message += Result.Message;
            TempData[SharedGlobal.StatusMessageName] = Message;

            return RedirectToAction(nameof(CERI05Controller.Index));
        }

        #endregion 全校補登

        #region 個人補登

        /// <summary>
        /// 個人補登- 學生列表
        /// </summary>
        /// <returns></returns>
        public ActionResult PersonalIndex()
        {
            this.Shared();
            return View();
        }

        /// <summary>
        /// 個人補登- 學生列表 PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult _PageContent(CERI05PersonalViewModel model)
        {
            this.Shared();
            if (model == null) model = new CERI05PersonalViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (!string.IsNullOrWhiteSpace(user?.TEACH_CLASS_NO))
            {
                model.WhereCLASS_NO = user.TEACH_CLASS_NO;
            }

            var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db);

            if (!string.IsNullOrWhiteSpace(user?.TEACH_CLASS_NO))
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);
            ViewBag.ClassItems = ClassItems.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            model = Service.GetUserListData(model, ref db);

            return PartialView(model);
        }

        public ActionResult PersonalEdit(CERI05PersonalEditViewModel model)
        {
            if (model == null) model = new CERI05PersonalEditViewModel();

            if (string.IsNullOrWhiteSpace(model.ThisSCHOOL_NO) && string.IsNullOrWhiteSpace(model.ThisUSER_NO))
            {
                return RedirectToAction(nameof(CERI05Controller.PersonalIndex));
            }

            model = this.Service.GetPersonalEditData(model, ref db);
            model = this.Service.GetPersonalEditData1(model, ref db);


            if (model.MyData == null)
            {
              
                return RedirectToAction(nameof(CERI05Controller.PersonalIndex));
            }
          

            this.Shared(model.MyData.NAME);
            return View(model);
        }

        public ActionResult _DetailPersonal(CERI05EditAccreditationtViewModel Item)
        {
            return PartialView("_DetailPersonal", Item);
        }

        /// <summary>
        /// 編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult PersonalEditSave(CERI05PersonalEditViewModel model)
        {
            this.Shared();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (model == null) model = new CERI05PersonalEditViewModel();

            if (string.IsNullOrWhiteSpace(model.ThisSCHOOL_NO) && string.IsNullOrWhiteSpace(model.ThisUSER_NO))
            {
                return RedirectToAction(nameof(CERI05Controller.PersonalIndex));
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                List<CERI05EditAccreditationtViewModel> cERI05s = new List<CERI05EditAccreditationtViewModel>();
                foreach (var it in model.Accreditationts) {
                    if (it.item == null) {
                        int cERI05sCount = 0;
                        string str = "";
                        str = it.PersonText;
                        cERI05sCount = cERI05s.Where(x => x.ACCREDITATION_ID == it.ACCREDITATION_ID).Count();
                        if (cERI05sCount > 0) {

                            cERI05s.Where(x => x.ACCREDITATION_ID == it.ACCREDITATION_ID).FirstOrDefault().PersonText = cERI05s.Where(x => x.ACCREDITATION_ID == it.ACCREDITATION_ID).FirstOrDefault().PersonText  +str + ",";
                        }
                    }
                    else {
                        if (it.item.PersonText != null) {
                            it.item.IS_PASS = true;
                            it.item.O_IS_PASS = false;
                            it.item.PersonText = it.item.PersonText + ",";
                        }
                        it.SCHOOL_NO = model.ThisSCHOOL_NO;
                        cERI05s.Add(it.item);

                    }
                  

                }
                model.Accreditationts = cERI05s;
                var Result = this.Service.SavePersonalEditData(model, user, ref db,ref valuesList);
                model = this.Service.GetPersonalEditData(model, ref db);
                model = this.Service.GetPersonalEditData1(model, ref db);
                if (Result.Success)
                {
                    Message = "儲存完成";
                }
                else
                {
                    Message += Result.Message;
                }
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            model = this.Service.GetPersonalEditData(model, ref db);
            this.SetTitle(model.MyData.NAME);
            return View(nameof(CERI05Controller.PersonalEdit), model);
        }

        #endregion 個人補登

        #region Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared
    }
}