﻿@model AWAI01IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
    {
        Layout = null;
    }
    else if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<link href="~/Content/styles/redeem.min.css" rel="stylesheet" />



@using (Html.BeginForm(Model.Search.BackAction, (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO, Model)
    </div>
}



    <script type="text/javascript">

        var targetFormID = '#form1';

    function clickLink(url) {
        $(targetFormID).attr("action", url);
        $(targetFormID).attr("target", "_blank");

    }
    function clickLink1(url) {

        $(targetFormID).attr("action", url);
        $(targetFormID).attr("target", "_self");

    }
         function funGetAdd() {
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_NO)').val('');
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_STS)').val('A');
            $(targetFormID).attr("action", "@Url.Action("Awat02", (string) ViewBag.BRE_NO)")
            $(targetFormID).submit();
    }


        function funGetModify(AWARD_NO) {
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_NO)').val(AWARD_NO);
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_STS)').val('M');
            $(targetFormID).attr("action", "@Url.Action("AwatMana02", (string) ViewBag.BRE_NO)")
            $(targetFormID).submit();
         }

        function funCARD(AWARD_NO, exchangeType = '') {
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_NO)').val(AWARD_NO);
            if (exchangeType == 'SchoolNo') {
                $('#@Html.IdFor(m=>m.Search.WhereExchangeType)').val(exchangeType);
            } else {
                $('#@Html.IdFor(m=>m.Search.WhereExchangeType)').val('');
            }
            $(targetFormID).attr("action", "@Url.Action("AwatExchangeCARD", (string) ViewBag.BRE_NO)")
            $(targetFormID).attr("target", "_blank")
            $(targetFormID).submit();
            $(targetFormID).attr("target", "_self")
        }

        function funGetDelete(AWARD_NO) {
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_NO)').val(AWARD_NO);
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_STS)').val('D');
            $(targetFormID).attr("action", "@Url.Action("AwatMana02", (string) ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function funGetExchange(AWARD_NO) {
            var btnId = '#btn_' + AWARD_NO
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_NO)').val(AWARD_NO);
            $(targetFormID).attr("action", "@Url.Action("AwatExchange02", (string) ViewBag.BRE_NO)")
            $(targetFormID).submit();
            $(btnId).text('我要兌換...讀取中...')
        }

        function funGetFullScreen() {
            $(targetFormID).attr("action", "@Url.Action("ProductListForFullScreen", (string) ViewBag.BRE_NO)")
            $(targetFormID).attr("target", "_blank")
            $(targetFormID).submit();
            $(targetFormID).attr("target", "_self")
        }

        function onProductOrderList() {
            $(targetFormID).attr("action", "@Url.Action("ProductOrderList", (string) ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }



        function onUnProduct(Value)
        {
            $('#@Html.IdFor(m=>m.Search.unProduct)').val(Value)
            FunPageProc(1);
        }


        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1);
        }

        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.Search.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_PageContent", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }



    </script>





