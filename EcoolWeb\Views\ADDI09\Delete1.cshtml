﻿@model ECOOL_APP.EF.AWAT01_LOG

@{
    ViewBag.Title = "即時加點-刪除特殊加扣點";
    ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();
    HRMT01 hRMT01 = new HRMT01();
    hRMT01 = EntitiesDb.HRMT01.Where(x => x.USER_NO == Model.USER_NO && x.SCHOOL_NO == Model.SCHOOL_NO).FirstOrDefault();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="text-center">
    <h3>確定要刪除這筆資料嗎</h3>
</div>

<img src="~/Content/img/web-bar2-revise-2200R.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ADDI06">
    <div class="Details">
        <dl class="dl-horizontal">
            <dt>
                @*@Html.DisplayNameFor(model => model.NAME)*@
               姓名
            </dt>

            <dd>
                @hRMT01.NAME
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.LOG_DESC)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.LOG_DESC)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.CASH_IN)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.CASH_IN)
            </dd>

            @*<dt>
                @Html.DisplayNameFor(model => model.)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.MEMO)
            </dd>*@
        </dl>

        @using (Html.BeginForm("DeleteConfirm1", "ADDI09", FormMethod.Post))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.USER_NO)
         

            <div class="Div-btn-center">
                <button value="Save" type="submit" class="btn btn-default">
                    確定送出
                </button>
                <a href='@Url.Action("ListView", "ADDI09")' class="btn btn-default">
                    放棄編輯
                </a>
            </div>
        }
    </div>



</div>
