﻿using AutoMapper;
using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 我要登記
    /// </summary>
    [SessionExpire]
    [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
    public class CERI04Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "CERI04";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CERI04Service Service = new CERI04Service();
        private CERI02Service cERI02Service = new CERI02Service();
        private CERI01Service eRI01Service = new CERI01Service();

        #region 列表畫面

        public ActionResult Index()
        {
            this.Shared();

            if (eRI01Service.IsAccreditationTypeforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI01")}」未設定，請先行設定，才執行此作業。";
            }
            else if (cERI02Service.IsAccreditationforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI02")}」未設定，請先行設定，才執行此作業。";
            }

            return View();
        }

        public ActionResult Index2()
        {
            this.Shared();

            if (eRI01Service.IsAccreditationTypeforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI01")}」未設定，請先行設定，才執行此作業。";
            }
            else if (cERI02Service.IsAccreditationforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI02")}」未設定，請先行設定，才執行此作業。";
            }

            return View();
        }

        public ActionResult _PageContent2(CERI04IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new CERI04IndexViewModel();

            if (model.WhereSYEAR == null)
            {
                model.WhereSYEAR = SysHelper.GetNowSYear(DateTime.Now);
            }

            var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db);
          var GRadeItems = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);
            if (!string.IsNullOrWhiteSpace(user?.TEACH_CLASS_NO))
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();


                string Grdestr = user.TEACH_CLASS_NO.Substring(0, 1);

                GRadeItems = GRadeItems.Where(x => x.Value == Grdestr).ToList();

                ViewBag.ClassItems = ClassItems.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });
                ViewBag.GradeItem = GRadeItems.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            }

            else {


                ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);
                ViewBag.ClassItems = ClassItems.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });


            }

            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
            ViewBag.GradeItem = GRadeItems.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");

            //ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db)
            // .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            model = Service.GetListData1(model, user, ref db);
            return PartialView(model);
        }

        public ActionResult _ADDDetail(CERI04EditDetailViewModel model) {
            return PartialView(model);
        }

        public ActionResult _PageContent(CERI04IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new CERI04IndexViewModel();

            if (model.WhereSYEAR == null)
            {
                model.WhereSYEAR = SysHelper.GetNowSYear(DateTime.Now);
            }

            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db)
             .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            model = Service.GetListData(model, user, ref db, "Index");
            return PartialView(model);
        }

        #endregion 列表畫面

        #region 編輯畫面及處理

        public ActionResult Edit(CERI04EditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new CERI04EditViewModel();

            if (string.IsNullOrWhiteSpace(model.ThisACCREDITATION_ID) && string.IsNullOrWhiteSpace(model.ThisITEM_NO) && string.IsNullOrWhiteSpace(model.ThisCLASS_NO))
            {
                return RedirectToAction(nameof(CERI04Controller.Index));
            }

            model = this.Service.GetEditData(model, ref db);

            return View(model);
        }
        public ActionResult Edit2(CERI04EditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new CERI04EditViewModel();

            if (string.IsNullOrWhiteSpace(model.ThisACCREDITATION_ID) && string.IsNullOrWhiteSpace(model.ThisITEM_NO) && string.IsNullOrWhiteSpace(model.ThisCLASS_NO))
            {
                return RedirectToAction(nameof(CERI04Controller.Index));
            }

            model = this.Service.GetEditData(model, ref db);

            return View(model);
        }
        public ActionResult _Detail(List<CERI04EditDetailViewModel> Item)
        {
            return PartialView("_Detail", Item);
        }

        /// <summary>
        /// 編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult EditSave(CERI04EditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new CERI04EditViewModel();

            if (string.IsNullOrWhiteSpace(model.ThisACCREDITATION_ID) && string.IsNullOrWhiteSpace(model.ThisITEM_NO) && string.IsNullOrWhiteSpace(model.ThisCLASS_NO))
            {
                return RedirectToAction(nameof(CERI04Controller.Index));
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                var Result = this.Service.SaveEditData(model, user, ref db,ref valuesList);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "儲存完成";

                    return View(nameof(CERI04Controller.Index), ClearToIndex(model));
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(CERI04Controller.Edit), model);
        }
        /// <summary>
        /// 編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult EditAllSave(CERI04EditViewModel model)
        {
            this.Shared("編輯");
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (model == null) model = new CERI04EditViewModel();

            if (string.IsNullOrWhiteSpace(model.ThisACCREDITATION_ID) && string.IsNullOrWhiteSpace(model.ThisITEM_NO) && string.IsNullOrWhiteSpace(model.ThisCLASS_NO))
            {
                return RedirectToAction(nameof(CERI04Controller.Index));
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                var Result = this.Service.SaveEditData(model, user, ref db,ref valuesList);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "儲存完成";

                    return View(nameof(CERI04Controller.Index2), ClearToIndex(model));
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(CERI04Controller.Edit2), model);
        }
        #endregion 編輯畫面及處理

        #region Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private CERI04IndexViewModel ClearToIndex(CERI04EditViewModel model)
        {
            CERI04IndexViewModel Qmodel = new CERI04IndexViewModel();
            model.QData = null;
            model.Details = null;
            model.ThisACCREDITATION_ID = null;
            model.ThisITEM_NO = null;
            model.ThisSCHOOL_NO = null;
            model.ThisGRADE = null;
            model.ThisCLASS_NO = null;

            Mapper.Initialize(cfg =>
            {
                cfg.CreateMap<CERI04EditViewModel, CERI04IndexViewModel>();
            });

            Mapper.Map<CERI04IndexViewModel>(model);

            return Qmodel;
        }

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared
    }
}