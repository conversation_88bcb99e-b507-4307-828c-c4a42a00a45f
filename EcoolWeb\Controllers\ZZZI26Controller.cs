﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using Ionic.Zip;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI26Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ZZZI26";

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg;

        private ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_KEY = string.Empty;
        private string USER_NO = string.Empty;

        /// <summary>
        /// 是否批閱權限，有可上傳檔案
        /// </summary>
        private string VerifyUseYN = string.Empty;

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Index()
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 - 選擇模式";
            this.Shared();
            ViewBag.tModeList = GetModeList();
            return View();
        }

        /// <summary>
        /// 多學生多書本
        /// </summary>
        /// <param name="ModeValString"></param>
        /// <returns></returns>
        // GET: ZZZI26
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ManyStudentIndex(string ModeValString)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            byte? ModeVal = ModeStringTobyte(ModeValString);
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 - " + GetModeName(ModeVal) + ".新增(步驟1)";
            this.Shared();

            ZZZI26EditViewModel Data = new ZZZI26EditViewModel();

            if (Data.Search == null) Data.Search = new ZZZI26SearchViewModel();
            Data.Search.ModeVal = (byte)ModeVal;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;

            if (user != null)
            {
                HRMT03 HRMT03 = EntitiesDb.HRMT03.Where(A => A.SCHOOL_NO == DefaultSCHOOL_NO && A.TEACHER_NO == USER_NO).FirstOrDefault();
                if (HRMT03 != null)
                {
                    Data.Search.CLASS_NO = HRMT03.CLASS_NO;
                }
            }

            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, ref EntitiesDb);
            //是否為導師
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassItems = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassItems = ClassList;
            }
            List<SelectListItem> NumTypeItems = new List<SelectListItem>();

            NumTypeItems.Add(new SelectListItem() { Text = "全班", Value = "ALL" });
            for (int i = 1; i <= 50; i++)
            {
                NumTypeItems.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString() });
            }
            ViewBag.NumTypeItems = NumTypeItems;

            return View(Data);
        }

        /// <summary>
        /// 單一學生多書本
        /// </summary>
        /// <param name="ModeValString"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ManyBookIndex(string ModeValString)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            byte? ModeVal = ModeStringTobyte(ModeValString);
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 - " + GetModeName(ModeVal) + ".新增(步驟1)";
            this.Shared();
            ZZZI26EditViewModel Data = new ZZZI26EditViewModel();

            if (Data.Search == null) Data.Search = new ZZZI26SearchViewModel();
            Data.Search.ModeVal = (byte)ModeVal;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;
            Data.Search.NumBook = 1;

            // ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, ref EntitiesDb);

            //是否為導師
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassItems = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassItems = ClassList;
            }
            ViewBag.USER_NOItems = HRMT01.GetUserNoListData(DefaultSCHOOL_NO, "", ref EntitiesDb);
            if (user != null)
            {
                HRMT03 HRMT03 = EntitiesDb.HRMT03.Where(A => A.SCHOOL_NO == DefaultSCHOOL_NO && A.TEACHER_NO == USER_NO).FirstOrDefault();
                if (HRMT03 != null)
                {
                    Data.Search.CLASS_NO = HRMT03.CLASS_NO;
                }
            }

            return View(Data);
        }

        /// <summary>
        /// 單一學生多書本(批次)
        /// </summary>
        /// <param name="ModeValString"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ManyBookBatchIndex(string ModeValString)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            byte? ModeVal = ModeStringTobyte(ModeValString);
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 - " + GetModeName(ModeVal) + ".新增(步驟1)";
            this.Shared();
            ZZZI26EditViewModel Data = new ZZZI26EditViewModel();

            if (Data.Search == null) Data.Search = new ZZZI26SearchViewModel();
            Data.Search.ModeVal = (byte)ModeVal;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;
            Data.Search.NumBook = 1;

            // ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, ref EntitiesDb);

            //是否為導師
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassItems = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassItems = ClassList;
            }
            ViewBag.USER_NOItems = HRMT01.GetUserNoListData(DefaultSCHOOL_NO, "", ref EntitiesDb);
            if (user != null)
            {
                HRMT03 HRMT03 = EntitiesDb.HRMT03.Where(A => A.SCHOOL_NO == DefaultSCHOOL_NO && A.TEACHER_NO == USER_NO).FirstOrDefault();
                if (HRMT03 != null)
                {
                    Data.Search.CLASS_NO = HRMT03.CLASS_NO;
                }
            }

            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ManyBookBatchIndex(ZZZI26EditViewModel Data, HttpPostedFileBase file)
        {
            byte? ModeVal = Data.Search.ModeVal;
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 - " + GetModeName(ModeVal) + ".新增(步驟1)";
            this.Shared();

            if (Data.Search == null) Data.Search = new ZZZI26SearchViewModel();
            Data.Search.ModeVal = (byte)ModeVal;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;

            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, "", Data.Search.CLASS_NO, ref EntitiesDb);

            if (string.IsNullOrWhiteSpace(Data.Search.CLASS_NO))
            {
                ModelState.AddModelError("Search.CLASS_NO", "*請輸入班級");
            }

            if (string.IsNullOrWhiteSpace(Data.Search.USER_NO))
            {
                ModelState.AddModelError("Search.USER_NO", "*請輸入姓名");
            }

            if (file == null)
            {
                ModelState.AddModelError("file", "*請上傳檔案");
            }
            else
            {
                if (file.ContentLength == 0)
                {
                    ModelState.AddModelError("file", "*請上傳檔案");
                }
                else
                {
                    if (file.ContentType != "application/x-zip-compressed" && file.ContentType != "application/octet-stream" && file.ContentType != "application/zip")
                    {
                        ModelState.AddModelError("file", "*請上傳檔案[ZIP]格式");
                    }
                }
            }

            if (ModelState.IsValid) //沒有錯誤
            {
                string fileName = Path.GetFileName(file.FileName);
                //建立上傳暫存路徑

                string NowFile = DateTime.Now.ToString("yyyyMMdd");

                Data.Search.TEMP_BATCH_KEY = Session.SessionID;

                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);

                string SCHOOL_NOPath = RealUploadImageRoot + @"\" + Bre_NO + @"\" + Data.Search.SCHOOL_NO + @"\";
                string ALLPath = SCHOOL_NOPath + @"\" + NowFile + @"\";

                if (!Directory.Exists(ALLPath))
                {
                    if (Directory.Exists(SCHOOL_NOPath))
                    {
                        Directory.Delete(SCHOOL_NOPath, true);
                    }

                    Directory.CreateDirectory(ALLPath);
                }

                string path = ALLPath + fileName;

                file.SaveAs(path);

                ReadOptions options = new ReadOptions();
                options.Encoding = Encoding.Default;
                ZipFile unzip = ZipFile.Read(path, options);

                string unZipPath = ALLPath + @"\Temp_" + DateTime.Now.ToString("HH_mm_ss") + @"\";

                foreach (ZipEntry e in unzip)
                {
                    e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                }

                unzip.Dispose();

                ArrayList ArrImg = GetFiles(unZipPath);

                Dictionary<string, string> ErrMSG = new Dictionary<string, string>();
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

                bool OK = true;

                List<HRMT01> H01List = EntitiesDb.HRMT01.Where(a => a.SCHOOL_NO == Data.Search.SCHOOL_NO
                   && a.CLASS_NO == Data.Search.CLASS_NO && a.USER_NO == Data.Search.USER_NO).ToList();

                HRMT01 H01 = H01List.Where(a => a.SCHOOL_NO == Data.Search.SCHOOL_NO
                   && a.CLASS_NO == Data.Search.CLASS_NO && a.USER_NO == Data.Search.USER_NO && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();

                if (H01 == null)
                {
                    OK = false;
                    TempData["StatusMessage"] = "無此學生";
                }

                if (H01 != null)
                {
                    if (Data.Details_List == null) Data.Details_List = new List<ZZZI26DetailsViewModel>();

                    foreach (var File in ArrImg)
                    {
                        string FileName = Path.GetFileName(File.ToString()); //取得檔名: "test.jpg"

                        if (regexCode.IsMatch(FileName.ToLower()) == false)
                        {
                            ErrMSG.Add(FileName, "非有效圖片格式");
                            OK = false;
                            continue;
                        }

                        ZZZI26DetailsViewModel Details = new ZZZI26DetailsViewModel();
                        Details.Del = false;
                        Details.SCHOOL_NO = H01.SCHOOL_NO;
                        Details.USER_NO = H01.USER_NO;
                        Details.CLASS_NO = H01.CLASS_NO;
                        Details.SEAT_NO = H01.SEAT_NO;
                        Details.NAME = H01.NAME;
                        Details.SNAME = H01.SNAME;
                        Details.IMG_FILE = UploadImageRoot + GetRelativePath(RealUploadImageRoot, File.ToString());
                        Details.CRE_DATE = DateTime.Now;
                        Data.Details_List.Add(Details);
                    }
                }

                if (OK)
                {
                    var USER_NOItems = HRMT01.GetUserNoListData(Data.Search.SCHOOL_NO, Data.Search.CLASS_NO, ref EntitiesDb);
                    ViewBag.USER_NOItems = USER_NOItems;
                    ViewBag.Panel_Title = "代申請閱讀認證 -" + GetModeName(Data.Search.ModeVal) + ".新增(步驟2)";
                    TempData["TOLTAL"] = Data.Details_List.Count();

                    return View("Edit", Data);
                }
                else
                {
                    if (Directory.Exists(ALLPath))
                    {
                        Directory.Delete(ALLPath, true);
                    }
                    TempData["StatusMessage"] = "上傳失敗!!。<br/>";
                    ViewBag.FailList = ErrMSG;
                    ViewBag.ErrCount = ErrMSG.Count;
                    return View(Data);
                }
            }
            else
            {
                TempData["StatusMessage"] = "錯誤!!請修正。<br/>";
            }

            return View(Data);
        }

        /// <summary>
        /// 「傳座號圖」，打書名
        /// </summary>
        /// <param name="ModeValString"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ManyfileIndex(string ModeValString)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            byte? ModeVal = ModeStringTobyte(ModeValString);
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 - " + GetModeName(ModeVal) + ".新增(步驟1)";
            this.Shared();
            ZZZI26EditViewModel Data = new ZZZI26EditViewModel();

            if (Data.Search == null) Data.Search = new ZZZI26SearchViewModel();
            Data.Search.ModeVal = (byte)ModeVal;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;

            // ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, ref EntitiesDb);

            //是否為導師
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassItems = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassItems = ClassList;
            }
            if (user != null)
            {
                HRMT03 HRMT03 = EntitiesDb.HRMT03.Where(A => A.SCHOOL_NO == DefaultSCHOOL_NO && A.TEACHER_NO == USER_NO).FirstOrDefault();
                if (HRMT03 != null)
                {
                    Data.Search.CLASS_NO = HRMT03.CLASS_NO;
                }
            }

            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ManyfileIndex(ZZZI26EditViewModel Data, HttpPostedFileBase file)
        {
            byte? ModeVal = Data.Search.ModeVal;
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 - " + GetModeName(ModeVal) + ".新增(步驟1)";
            this.Shared();

            if (Data.Search == null) Data.Search = new ZZZI26SearchViewModel();
            Data.Search.ModeVal = (byte)ModeVal;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;

            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, "", Data.Search.CLASS_NO, ref EntitiesDb);

            if (string.IsNullOrWhiteSpace(Data.Search.CLASS_NO))
            {
                ModelState.AddModelError(Data.Search.CLASS_NO, "*請輸入班級");
            }

            if (file == null)
            {
                ModelState.AddModelError("file", "*請上傳檔案");
            }
            else
            {
                if (file.ContentLength == 0)
                {
                    ModelState.AddModelError("file", "*請上傳檔案");
                }
                else
                {
                    if (file.ContentType != "application/x-zip-compressed" && file.ContentType != "application/octet-stream" && file.ContentType != "application/zip")
                    {
                        ModelState.AddModelError("file", "*請上傳檔案[ZIP]格式");
                    }
                }
            }

            if (ModelState.IsValid) //沒有錯誤
            {
                string fileName = Path.GetFileName(file.FileName);
                //建立上傳暫存路徑

                string NowFile = DateTime.Now.ToString("yyyyMMdd");

                Data.Search.TEMP_BATCH_KEY = Session.SessionID;

                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);

                string SCHOOL_NOPath = RealUploadImageRoot + @"\" + Bre_NO + @"\" + Data.Search.SCHOOL_NO + @"\";
                string ALLPath = SCHOOL_NOPath + @"\" + NowFile + @"\";

                if (!Directory.Exists(ALLPath))
                {
                    if (Directory.Exists(SCHOOL_NOPath))
                    {
                        Directory.Delete(SCHOOL_NOPath, true);
                    }

                    Directory.CreateDirectory(ALLPath);
                }

                string path = ALLPath + fileName;

                file.SaveAs(path);

                ReadOptions options = new ReadOptions();
                options.Encoding = Encoding.Default;
                ZipFile unzip = ZipFile.Read(path, options);

                string unZipPath = ALLPath + @"\Temp_" + DateTime.Now.ToString("HH_mm_ss") + @"\";

                foreach (ZipEntry e in unzip)
                {
                    e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                }

                unzip.Dispose();

                ArrayList ArrImg = GetFiles(unZipPath);

                Dictionary<string, string> ErrMSG = new Dictionary<string, string>();
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

                bool OK = true;

                List<HRMT01> H01List = EntitiesDb.HRMT01.Where(a => a.SCHOOL_NO == Data.Search.SCHOOL_NO
                   && a.CLASS_NO == Data.Search.CLASS_NO).ToList();

                if (Data.Details_List == null) Data.Details_List = new List<ZZZI26DetailsViewModel>();

                foreach (var File in ArrImg)
                {
                    string FileName = Path.GetFileName(File.ToString()); //取得檔名: "test.jpg"
                    string SEAT_NO = Path.GetFileNameWithoutExtension(File.ToString());

                    if (regexCode.IsMatch(FileName.ToLower()) == false)
                    {
                        ErrMSG.Add(FileName, "非有效圖片格式");
                        OK = false;
                        continue;
                    }

                    ////並確認學生清單
                    HRMT01 H01 = H01List.Where(a => a.SCHOOL_NO == Data.Search.SCHOOL_NO
                    && a.CLASS_NO == Data.Search.CLASS_NO && a.SEAT_NO == SEAT_NO && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();

                    if (H01 == null)
                    {
                        ErrMSG.Add(FileName, "無此座號");
                        OK = false;
                        continue;
                    }

                    ZZZI26DetailsViewModel Details = new ZZZI26DetailsViewModel();
                    Details.Del = false;
                    Details.SCHOOL_NO = H01.SCHOOL_NO;
                    Details.USER_NO = H01.USER_NO;
                    Details.CLASS_NO = H01.CLASS_NO;
                    Details.SEAT_NO = H01.SEAT_NO;
                    Details.NAME = H01.NAME;
                    Details.SNAME = H01.SNAME;
                    Details.IMG_FILE = UploadImageRoot + GetRelativePath(RealUploadImageRoot, File.ToString());
                    Details.CRE_DATE = DateTime.Now;
                    Data.Details_List.Add(Details);
                }

                if (OK)
                {
                    var USER_NOItems = HRMT01.GetUserNoListData(Data.Search.SCHOOL_NO, Data.Search.CLASS_NO, ref EntitiesDb);
                    ViewBag.USER_NOItems = USER_NOItems;
                    ViewBag.Panel_Title = "代申請閱讀認證 -" + GetModeName(Data.Search.ModeVal) + ".新增(步驟2)";
                    TempData["TOLTAL"] = Data.Details_List.Count();

                    return View("Edit", Data);
                }
                else
                {
                    if (Directory.Exists(ALLPath))
                    {
                        Directory.Delete(ALLPath, true);
                    }
                    TempData["StatusMessage"] = "上傳失敗!!。<br/>";
                    ViewBag.FailList = ErrMSG;
                    ViewBag.ErrCount = ErrMSG.Count;
                    return View(Data);
                }
            }
            else
            {
                TempData["StatusMessage"] = "錯誤!!請修正。<br/>";
            }

            return View(Data);
        }

        private static String GetRelativePath(String basePath, String targetPath)
        {
            Uri baseUri = new Uri(basePath);
            Uri targetUri = new Uri(targetPath);
            return baseUri.MakeRelativeUri(targetUri).ToString().Replace(@"/", @"\");
        }

        //讀取目錄下所有檔案
        private static ArrayList GetFiles(string path)
        {
            ArrayList files = new ArrayList();

            if (Directory.Exists(path))
            {
                files.AddRange(Directory.GetFiles(path, "*", SearchOption.AllDirectories));
            }

            return files;
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string CLASS_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();

            var UserNoListData = HRMT01.GetUserNoListData(DefaultSCHOOL_NO, CLASS_NO, ref EntitiesDb);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, "", true, null);

            return Content(_html);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Edit(ZZZI26EditViewModel Data, IEnumerable<HttpPostedFileBase> files, string DATA_TYPE, string ADDNUM)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 -" + GetModeName(Data.Search.ModeVal) + ".新增(步驟2)";
            this.Shared();

            if (Data.Details_List == null) Data.Details_List = new List<ZZZI26DetailsViewModel>();

            if (string.IsNullOrEmpty(ADDNUM) == false) Data.ADDNUM = Convert.ToInt32(ADDNUM);

            if (string.IsNullOrEmpty(DATA_TYPE) == false)
            {
                if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_A) //新增明細不驗正
                {
                    ModelState.Clear(); //清除驗正

                    if (Data.ADDNUM == null)
                    {
                        ModelState.AddModelError("additem", "【增加明細】請輸入筆數");
                    }
                }
                else if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_S) // 存檔 / 批閱
                {
                    if (string.IsNullOrWhiteSpace(Data.Search.TEMP_BATCH_KEY))
                    {
                        if (VerifyUseYN == "Y")
                        {
                            int num = 0;
                            foreach (var file in files)
                            {
                                if (file != null && file.ContentLength > 0)
                                {
                                    Data.Details_List[num].IMG_FILE = Path.GetFileName(file.FileName);
                                    Data.Details_List[num].files = file;
                                }
                                num++;
                            }
                        }
                    }

                    var OK = this.Save(Data);
                    if (OK)
                    {
                        if (string.IsNullOrEmpty(ErrorMsg) == false)
                        {
                            TempData["StatusMessage"] = "部分申請認証失敗。";
                        }
                        else
                        {
                            TempData["StatusMessage"] = "代申請認証全部成功";
                        }
                        return View("ShowList", Data);
                    }
                    else
                    {
                        TempData["StatusMessage"] = "錯誤!!請修正。<br/>" + ErrorMsg;
                    }
                }
            }
            else //新增預帶值
            {
                if (Data.Search.ModeVal == (byte)Mode.ManyStudentIndex)
                {
                    this.AddItem(Data, Data.Search.NumType);
                }
                else if (Data.Search.ModeVal == (byte)Mode.ManyBookIndex)
                {
                    this.AddItem(Data, Data.Search.NumBook.ToString());
                }
            }

            //計算明細筆數
            int Count = 0;
            int Total = 0;
            if (Data.Details_List != null)
            {
                Count = Data.Details_List.Count();
            }

            Total = (Data.ADDNUM == null) ? Count : (int)Data.ADDNUM + Count;
            TempData["TOLTAL"] = Total;
            if (Data.ADDNUM != null)
            {
                this.AddItem(Data, Data.ADDNUM.ToString());
                Data.ADDNUM = null;
            }

            //姓名 下拉
            IEnumerable<SelectListItem> USER_NOItems = null;

            if (Data.Search.ModeVal == (byte)Mode.ManyStudentIndex || Data.Search.ModeVal == (byte)Mode.ManyfileIndex || Data.Search.ModeVal == (byte)Mode.ManyBookBatchIndex)
            {
                USER_NOItems = HRMT01.GetUserNoListData(Data.Search.SCHOOL_NO, Data.Search.CLASS_NO, "", ref EntitiesDb);
            }
            else if (Data.Search.ModeVal == (byte)Mode.ManyBookIndex)
            {
                USER_NOItems = HRMT01.GetUserNoListData(Data.Search.SCHOOL_NO, Data.Search.CLASS_NO, Data.Search.USER_NO, ref EntitiesDb);
            }

            ViewBag.USER_NOItems = USER_NOItems;

            Data.Details_List.RemoveAll(a => a.Del == true);
            return View(Data);
        }

        /// <summary>
        /// 資料處理
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public bool Save(ZZZI26EditViewModel Data)
        {
            bool ReturnBool = false;

            try
            {
                //移除DEL有勾選的資料
                Data.Details_List.RemoveAll(a => a.Del == true);

#if DEBUG
                var errorsaa = ModelState
                                                .Where(x => x.Value.Errors.Count > 0)
                                                .Select(x => new { x.Key, x.Value.Errors })
                                                .ToArray();
#endif

                if (ModelState.IsValid) //沒有錯誤
                {
                    int CountOK = 0;

                    int SYear;
                    int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                    List<HRMT01> H01ListData = EntitiesDb.HRMT01.Where(p => p.SCHOOL_NO == Data.Search.SCHOOL_NO && (!UserStaus.NGKeyinUserStausList.Contains(p.USER_STATUS))).ToList();

                    List<ADDT03> A03ListData = EntitiesDb.ADDT03.Where(A => A.SCHOOL_NO == Data.Search.SCHOOL_NO).ToList();

                    foreach (var item in Data.Details_List)
                    {
                        bool ErrorBool = false;

                        //取得學生資料
                        HRMT01 H01 = H01ListData.Where(p => p.USER_NO == item.USER_NO && p.SCHOOL_NO == Data.Search.SCHOOL_NO).FirstOrDefault();

                        if (H01 == null)
                        {
                            ErrorBool = true;
                            item.MEMO = "此學生帳號已失效";
                            item.OK_YN = "N";
                            ErrorMsg = ErrorMsg + "學號:" + item.USER_NO + "-帳號已失效。<br/>";
                        }

                        if (H01 != null)
                        {
                            item.SCHOOL_NO = H01.SCHOOL_NO;
                            item.USER_NO = H01.USER_NO;
                            item.CLASS_NO = H01.CLASS_NO;
                            item.SYEAR = (byte)SYear;
                            item.SEMESTER = (byte)Semesters;
                            item.SEAT_NO = H01.SEAT_NO;
                            item.NAME = H01.NAME;
                            item.SNAME = H01.SNAME;

                            ADDT06 AT06 = new ADDT06();
                            ADDTController ADDT = new ADDTController();
                            string StatusMemo = string.Empty;
                            bool boolisReadBooK = ADDT.isReadBooK(Data.Search.SCHOOL_NO, item.USER_NO, item.BOOK_NAME, ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_Substitute, out StatusMemo);

                            if (boolisReadBooK)
                            {
                                //ErrorBool = true;
                                item.MEMO = "<font color='red'>重覆申請，書名改成" + item.BOOK_NAME + DateTime.Now.ToString("yyyyMMdd") + "</font>";
                                //item.OK_YN = "N";
                                //ErrorMsg = ErrorMsg + "班級:" + H01.CLASS_NO + "、姓名:" + H01.NAME + "、座號:" + H01.SEAT_NO + " - 「" + item.BOOK_NAME + "」已閱讀過。"+StatusMemo+"<br/>";

                                item.BOOK_NAME = item.BOOK_NAME + DateTime.Now.ToString("yyyyMMdd");

                                boolisReadBooK = ADDT.isReadBooK(Data.Search.SCHOOL_NO, item.USER_NO, item.BOOK_NAME, ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_Substitute, out StatusMemo);
                                if (boolisReadBooK)
                                {
                                    // item.BOOK_NAME = item.BOOK_NAME.Replace(DateTime.Now.ToString("yyyyMMdd"), "");
                                    ErrorBool = true;
                                    item.MEMO = "今天已重覆申請此本書";
                                    item.OK_YN = "N";
                                    ErrorMsg = ErrorMsg + "班級:" + H01.CLASS_NO + "、姓名:" + H01.NAME + "、座號:" + H01.SEAT_NO + " - 「" + item.BOOK_NAME + "」已閱讀過。" + StatusMemo + "<br/>";
                                }
                            }

                            string PASSPORT_YN = "N";
                            ADDT03 A03 = A03ListData.Where(A => A.SCHOOL_NO == Data.Search.SCHOOL_NO && A.BOOK_NAME.Trim() == item.BOOK_NAME.Trim()).FirstOrDefault();
                            if (A03 != null)
                            {
                                PASSPORT_YN = "Y";
                            }
                            item.PASSPORT_YN = PASSPORT_YN;

                            if (ErrorBool == false)
                            {
                                AT06.SCHOOL_NO = item.SCHOOL_NO;
                                AT06.USER_NO = item.USER_NO;
                                AT06.CLASS_NO = item.CLASS_NO;
                                AT06.SYEAR = item.SYEAR;
                                AT06.SEMESTER = item.SEMESTER;
                                AT06.SEAT_NO = item.SEAT_NO;
                                AT06.NAME = item.NAME;
                                AT06.SNAME = item.SNAME;
                                AT06.PASSPORT_YN = PASSPORT_YN;
                                AT06.BOOK_ID = (PASSPORT_YN == "Y") ? A03.BOOK_ID : null;
                                AT06.BOOK_NAME = (PASSPORT_YN == "Y") ? A03.BOOK_NAME : item.BOOK_NAME;
                                AT06.APPLY_TYPE = (byte)ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_Substitute;
                                AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify;
                                AT06.CRE_DATE = DateTime.Now;
                                AT06.SHARE_YN = item.SHARE_YN;

                                //書名一樣就自動當作是Passport
                                if (AT06.PASSPORT_YN != "Y")
                                {
                                    ADDT03 PassItem =
                                        db.ADDT03.Where(a => a.SCHOOL_NO == AT06.SCHOOL_NO && a.BOOK_NAME == AT06.BOOK_NAME).FirstOrDefault();
                                    if (PassItem != null)
                                    {
                                        AT06.PASSPORT_YN = "Y";
                                        AT06.BOOK_ID = PassItem.BOOK_ID;
                                    }
                                }
                                EntitiesDb.ADDT06.Add(AT06);

                                try
                                {
                                    EntitiesDb.SaveChanges();
                                }
                                catch (Exception ex)
                                {
                                    ErrorBool = true;
                                    item.MEMO = "儲存資料-發生錯誤。錯誤原因如下:" + ex.Message;
                                    item.OK_YN = "N";
                                    ErrorMsg = ErrorMsg + "班級:" + H01.CLASS_NO + "、姓名:" + H01.NAME + "、座號:" + H01.SEAT_NO + " - 「" + item.BOOK_NAME + "」發生錯誤(A.儲存資料-發生錯誤)。錯誤原因如下:<br/>" + ex.Message + "<br/><br/>";
                                }

                                //有上傳檔案為批閱流程
                                if (string.IsNullOrEmpty(item.IMG_FILE) == false && ErrorBool == false)
                                {
                                    bool ans = false;

                                    EntitiesDb.Entry(AT06).GetDatabaseValues();

                                    int APPLY_NO = AT06.APPLY_NO;

                                    //處理上傳檔案
                                    try
                                    {
                                        if (string.IsNullOrWhiteSpace(Data.Search.TEMP_BATCH_KEY))
                                        {
                                            ans = new ADDTController().doNewImage(AT06, item.files);
                                        }
                                        else
                                        {
                                            ans = new ADDTController().doNewImageReal(AT06, item.IMG_FILE);
                                        }

                                        EntitiesDb.SaveChanges();
                                    }
                                    catch (Exception ex)
                                    {
                                        ErrorBool = true;
                                        item.MEMO = "上傳檔案-發生錯誤。錯誤原因如下:" + ex.Message;
                                        item.OK_YN = "N";
                                        ErrorMsg = ErrorMsg + "班級:" + H01.CLASS_NO + "、姓名:" + H01.NAME + "、座號:" + H01.SEAT_NO + " - 「" + item.BOOK_NAME + "」發生錯誤(B.上傳檔案-發生錯誤)。錯誤原因如下:<br/>" + ex.Message + "<br/><br/>";
                                        AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL; //作廢
                                        EntitiesDb.SaveChanges();
                                    }

                                    if (ans && ErrorBool == false)
                                    {
                                        try
                                        {
                                            //狀態更新為 2 (批閱通過)
                                            ADDTService.CheckPendingDetailEDIT(ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass, (int)APPLY_NO, "", item.SHARE_YN, user);
                                        }
                                        catch (Exception ex)
                                        {
                                            ErrorBool = true;
                                            item.MEMO = "狀態更新-發生錯誤。錯誤原因如下:" + ex.Message;
                                            item.OK_YN = "N";
                                            ErrorMsg = ErrorMsg + "班級:" + H01.CLASS_NO + "、姓名:" + H01.NAME + "、座號:" + H01.SEAT_NO + " - 「" + item.BOOK_NAME + "」發生錯誤(C.狀態更新-發生錯誤)。錯誤原因如下:<br/>" + ex.Message + "<br/><br/>";
                                            AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL; //作廢
                                            EntitiesDb.SaveChanges();
                                        }
                                    }
                                    else //上傳失敗
                                    {
                                        AT06.APPLY_STATUS = "9"; //作廢

                                        try
                                        {
                                            EntitiesDb.SaveChanges();
                                        }
                                        catch (Exception ex)
                                        {
                                            ErrorBool = true;
                                            item.MEMO = "上傳檔案-發生錯誤。錯誤原因如下:" + ex.Message;
                                            item.OK_YN = "N";
                                            ErrorMsg = ErrorMsg + "班級:" + H01.CLASS_NO + "、姓名:" + H01.NAME + "、座號:" + H01.SEAT_NO + " - 「" + item.BOOK_NAME + "」發生錯誤(D.上傳檔案-發生錯誤)。錯誤原因如下:<br/>" + ex.Message + "<br/><br/>";
                                        }
                                    }
                                }

                                if (ErrorBool == false)
                                {
                                    item.MEMO = item.MEMO + "代申請成功";
                                    item.OK_YN = "Y";
                                }
                            } // if (ErrorBool == false)
                        }
                        if (!ErrorBool) CountOK++;
                    }

                    if (CountOK > 0) ReturnBool = true;
                }
            }
            catch (Exception ex)
            {
                ErrorMsg = ex.Message;
            }

            return ReturnBool;
        }

        public ActionResult ShowList(ZZZI26EditViewModel Data)
        {
            if (Data == null) Data = new ZZZI26EditViewModel();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請閱讀認證 -" + GetModeName(Data.Search.ModeVal) + ".處理清單";
            this.Shared();
            return View(Data);
        }

        /// <summary>
        /// 增加明細
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="Num"></param>
        private void AddItem(ZZZI26EditViewModel Data, string Num)
        {
            if (Num == "ALL")
            {
                var HRMT01List = EntitiesDb.HRMT01.Where(a => a.SCHOOL_NO == Data.Search.SCHOOL_NO && a.CLASS_NO == Data.Search.CLASS_NO && a.USER_TYPE == UserType.Student
                && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))).ToList().OrderBy(o => o.SEAT_NO);

                if (HRMT01List != null)
                {
                    foreach (var item in HRMT01List)
                    {
                        ZZZI26DetailsViewModel T06 = new ZZZI26DetailsViewModel();
                        T06.USER_NO = item.USER_NO;
                        Data.Details_List.Add(T06);
                    }
                }
            }
            else
            {
                for (int i = 1; i <= Convert.ToInt16(Num); i++)
                {
                    ZZZI26DetailsViewModel T06 = new ZZZI26DetailsViewModel();

                    if (Data.Search.ModeVal == (byte)Mode.ManyStudentIndex)
                    {
                        T06.USER_NO = "";
                    }
                    else if (Data.Search.ModeVal == (byte)Mode.ManyBookIndex)
                    {
                        T06.USER_NO = Data.Search.USER_NO;
                    }

                    Data.Details_List.Add(T06);
                }
            }
        }

        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ADDI02")] //檢查權限
        public ActionResult UPLOAD()
        {
            this.Shared();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            ViewBag.BookID = Request["BookID"];
            ViewBag.BookStatus = Request["BookStatus"];
            UserProfile user = UserProfileHelper.Get();
            List<ADDT03> liADDT03 = new List<ADDT03>();
            try
            {
                liADDT03 = db.ADDT03.Where(a => a.SCHOOL_NO == user.SCHOOL_NO).OrderBy(a => a.BOOK_ID).ToList();
            }
            catch (Exception e)
            {
                throw;
            }

            //是否為導師
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassNoItem = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassNoItem = ClassList;
            }

            return View(liADDT03);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ADDI02")] //檢查權限
        public ActionResult SetUpload(HttpPostedFileBase file)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            List<int> AccessList = new List<int>();
            List<ADDT06> lsA06 = new List<ADDT06>();
            Dictionary<string, string> UpadteListStatus = new Dictionary<string, string>();
            UserProfile user = UserProfileHelper.Get();
            string radFileType = (Request["radFileType"] != null) ? Request["radFileType"] : string.Empty;
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            string ddlReadrppBook = (Request["ddlReadrppBook"] != null) ? Request["ddlReadrppBook"].ToString() : string.Empty;
            string BOOK_ID = (Request["BOOK_ID"] != null) ? Request["BOOK_ID"].ToString() : string.Empty;
            string stdUserNo = string.Empty;
            string strMsg = string.Empty;
            string ErrMsg = string.Empty;

            string BookName = string.Empty;
            if (ddlReadrppBook == "Y")
            {
                BookName = Request["BOOK_NAME"];
                if (string.IsNullOrEmpty(BOOK_ID) == false && string.IsNullOrEmpty(BookName) == false)
                {
                    BookName = BookName.Replace(BOOK_ID, "").Trim();
                }
            }
            else
            {
                BookName = Request["txtBOOK_NAME"];
            }

            ZZZI26EditViewModel Data = new ZZZI26EditViewModel();
            Data.Search = new ZZZI26SearchViewModel();
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;
            Data.Search.ModeVal = 4;
            Data.Details_List = new List<ZZZI26DetailsViewModel>();

            if (file.ContentLength > 0)
            {
                var fileName = Path.GetFileName(file.FileName);
                string NowFile = DateTime.Now.ToString("yyyyMMdd");
                Data.Search.TEMP_BATCH_KEY = Session.SessionID;
                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);

                string SCHOOL_NOPath = RealUploadImageRoot + @"\" + Bre_NO + @"\" + Data.Search.SCHOOL_NO + @"\";
                string ALLPath = SCHOOL_NOPath + @"\" + NowFile + @"\";

                if (!Directory.Exists(ALLPath))
                {
                    if (Directory.Exists(SCHOOL_NOPath))
                    {
                        Directory.Delete(SCHOOL_NOPath, true);
                    }

                    Directory.CreateDirectory(ALLPath);
                }

                string path = ALLPath + fileName;

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                try
                {
                    if (file.ContentType == "application/x-zip-compressed" || file.ContentType == "application/octet-stream" || file.ContentType == "application/zip")
                    {
                        file.SaveAs(path);

                        ReadOptions options = new ReadOptions();
                        options.Encoding = Encoding.Default;
                        ZipFile unzip = ZipFile.Read(path, options);

                        string unZipPath = ALLPath + @"\Temp_" + DateTime.Now.ToString("HH_mm_ss") + @"\";

                        foreach (ZipEntry e in unzip)
                        {
                            e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                        }

                        unzip.Dispose();

                        ArrayList ArrImg = GetFiles(unZipPath);//alReadBook

                        Dictionary<string, string> ErrMSG = new Dictionary<string, string>();
                        Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

                        List<HRMT01> H01List = new List<HRMT01>();
                        List<SelectListItem> H01SelectListItem = new List<SelectListItem>();
                        ViewBag.USER_NOItems = H01SelectListItem;
                        for (int i = 0; i < ArrImg.Count; i++)
                        {
                            ADDT06 ADDTModel = new ADDT06();
                            string strIMG_FILE = string.Empty;
                            ErrMsg = string.Empty;

                            //由上傳的檔案取出學生資訊
                            string stdFileUserNo = Path.GetFileName(ArrImg[i].ToString());
                            if (regexCode.IsMatch(stdFileUserNo.ToLower()) == false)
                            {
                                UpadteListStatus[stdFileUserNo] = "非有效圖片格式";
                                continue;
                            }

                            stdUserNo = stdFileUserNo.Split('.')[0];
                            string[] keys = UpadteListStatus.Keys.ToArray<string>();

                            //若是選擇上傳座號
                            if (radFileType != string.Empty && radFileType == "S")
                            {
                                List<HRMT01> lth01 = db.HRMT01.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.CLASS_NO == Class_No && p.SEAT_NO == stdUserNo &&
                                    p.USER_STATUS != UserStaus.Invalid && p.USER_TYPE == "S").ToList();
                                stdUserNo = (lth01.Count > 0) ? lth01.FirstOrDefault().USER_NO : "0";
                            }

                            try
                            {
                                //並確認學生清單
                                HRMT01 H01 = db.HRMT01.Where(p => p.USER_NO == stdUserNo && p.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();

                                if (H01 != null)
                                {
                                    string StatusMemo = string.Empty;
                                    if (new ADDTController().isReadBooK(H01.SCHOOL_NO, H01.USER_NO, BookName, ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_UPLOAD, out StatusMemo) == true)
                                    {
                                        //書有讀過則改上傳失敗
                                        UpadteListStatus[stdFileUserNo] = "重覆申請" + StatusMemo;
                                    }
                                    else
                                    {
                                        H01List.Add(H01);
                                        H01SelectListItem.Add(new SelectListItem() { Text = H01.CLASS_NO + "-" + H01.SEAT_NO + "-" + H01.SNAME, Value = H01.USER_NO });
                                        #region"寫入資料"
                                        ZZZI26DetailsViewModel Details = new ZZZI26DetailsViewModel();
                                        Details.Del = false;
                                        Details.SCHOOL_NO = H01.SCHOOL_NO;
                                        Details.USER_NO = H01.USER_NO;
                                        Details.CLASS_NO = H01.CLASS_NO;
                                        Details.SEAT_NO = H01.SEAT_NO;
                                        Details.NAME = H01.NAME;
                                        Details.SNAME = H01.SNAME;
                                        Details.IMG_FILE = UploadImageRoot + GetRelativePath(RealUploadImageRoot, ArrImg[i].ToString());
                                        Details.CRE_DATE = DateTime.Now;
                                        Details.BOOK_NAME = BookName;
                                        Data.Details_List.Add(Details);

                                        #endregion
                                    }
                                }
                                else
                                {
                                    //若有問題的則改變上傳檔案的狀態
                                    if (Class_No == string.Empty)
                                    {
                                        UpadteListStatus[stdFileUserNo] = "請確認「學號」是否正確";
                                    }
                                    else
                                    {
                                        UpadteListStatus[stdFileUserNo] = "請確認「座號」是否正確";
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                //this.HandleMessage(e.ToString());
                                //若有問題的則改變上傳檔案的狀態
                                UpadteListStatus[stdFileUserNo] = e.Message;
                                strMsg = "新增失敗請洽管理員";
                            }
                            finally
                            {
                                if (string.IsNullOrWhiteSpace((string)ViewData["ViewMessage"]))
                                {
                                    if (UpadteListStatus.Count() > 0)
                                    {
                                        strMsg += "上傳資料有問題，請查看失敗清單";
                                    }
                                    ViewData["ViewMessage"] = strMsg;
                                }
                            }
                        }
                    }
                    else
                    {
                        UpadteListStatus.Add(fileName, "非有效上傳格式");
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                finally
                {
                }

                if (AccessList.Count > 0)
                {
                    lsA06 = db.ADDT06.Where(p => AccessList.Contains(p.APPLY_NO)).ToList();
                }
                ViewBag.SumCount = UpadteListStatus.Count;
                Dictionary<string, string> FailList = UpadteListStatus.Where(a => a.Value != null && a.Value != "").ToDictionary(t => t.Key, t => t.Value);

                ViewBag.FailList = FailList;
                ViewBag.ErrCount = FailList.ToList().Count;

                ViewBag.Panel_Title = "代申請閱讀認證 -" + GetModeName(Data.Search.ModeVal) + ".新增(步驟2)";
                TempData["TOLTAL"] = Data.Details_List.Count();

                return View("Edit", Data);
            }
            else
            {
                UpadteListStatus.Add(Path.GetFileName(file.FileName), "非有效上傳格式");
                return RedirectToAction("UPLOAD");
            }
        }

        #region  Shared

        private void Shared()
        {
            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            VerifyUseYN = PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", DefaultSCHOOL_NO, USER_NO);
            ViewBag.VerifyUseYN = VerifyUseYN;
        }

        #endregion

        public static class DATA_TYPE
        {
            /// <summary>
            /// 存檔(新增)
            /// </summary>
            public static string DATA_TYPE_S = "Save";

            /// <summary>
            /// (新增明細)
            /// </summary>
            public static string DATA_TYPE_A = "AddItem";
        }

        /// <summary>
        /// 模式 多學生多書本 ,單一學生多書本
        /// </summary>
        public enum Mode : byte
        {
            /// <summary>
            /// 多學生多書本
            /// </summary>
            ManyStudentIndex = 0,

            /// <summary>
            /// 單一學生多書本
            /// </summary>
            ManyBookIndex = 1,

            /// <summary>
            /// 單一學生多書本(ZIP)
            /// </summary>
            ManyBookBatchIndex = 2,

            /// <summary>
            /// 傳座號圖檔後打書名
            /// </summary>
            ManyfileIndex = 3,

            /// <summary>
            /// 多學生單一書本(ZIP)
            /// </summary>
            UPLOAD = 4,
        }

        public static string GetModeName(byte? ModeValue)
        {
            if (ModeValue == 0)
                return "多學生多書本";
            else if (ModeValue == 1)
                return "單一學生多書本";
            else if (ModeValue == 2)
                return "單一學生多書本(ZIP)";
            else if (ModeValue == 3)
                return "傳「座號圖」，打書名";
            else if (ModeValue == 4)
                return "多學生單一書本(ZIP)";
            else
                return "";
        }

        public static string GetModeName(string ModeValue)
        {
            return GetModeName(ModeStringTobyte(ModeValue));
        }

        public static byte? ModeStringTobyte(string ModeValue)
        {
            byte? ReturnVal = null;

            foreach (var it in Enum.GetValues(typeof(Mode)))
            {
                if (it.ToString() == ModeValue)
                {
                    ReturnVal = (byte)it;
                }
            }

            return ReturnVal;
        }

        /// <summary>
        /// 模式類型清單
        /// </summary>
        /// <returns></returns>
        static public List<SelectListItem> GetModeList(byte? SelectedValue = null)
        {
            List<SelectListItem> ItemS = new List<SelectListItem>();

            foreach (var it in Enum.GetValues(typeof(Mode)))
            {
                SelectListItem Item = new SelectListItem();
                Item.Text = GetModeName((byte)it);
                Item.Value = it.ToString();

                if (SelectedValue != null)
                {
                    Item.Selected = (byte)it == SelectedValue ? true : false;
                }

                ItemS.Add(Item);
            }

            return ItemS;
        }
    }
}