﻿@model AWAI01EditViewModel
@using EcoolWeb.Util;

<script src="~/Scripts/timepicker/jquery-ui-sliderAccess.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>

@Html.AntiForgeryToken()
@Html.ValidationSummary(true, "", new { @class = "text-danger" })

@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.BackAction)
@Html.HiddenFor(m => m.Search.BackController)

@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereAWARD_NO)
@Html.HiddenFor(m => m.Search.WhereAWARD_STS)
@Html.HiddenFor(m => m.Search.WhereSouTable)
@Html.HiddenFor(m => m.Search.unProduct)
@Html.HiddenFor(m => m.Search.whereAWARD_SCHOOL_NO)
@Html.HiddenFor(m => m.Search.AWARD_TYPE)

@Html.HiddenFor(m => m.Search.whereKeyword)

@Html.HiddenFor(m => m.Edit.AWARD_NO)
@Html.HiddenFor(m => m.Edit.AWARD_STATUS)

@Html.HiddenFor(m => m.REF_KEY)

<div class="form-horizontal">
    <div class="form-group">
        @Html.LabelFor(m => m.Edit.SCHOOL_NO, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.DropDownListFor(m => m.Edit.SCHOOL_NO, (List<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control" })
            @Html.ValidationMessageFor(m => m.Edit.SCHOOL_NO, "", new { @class = "text-danger" })
            <br />
            <label class="text-info"><img src='~/Content/img/web-revise-prize-03s.png' style="max-height:35px;margin-right:5px">PS.選擇【全部學校】為{總召獎品}</label>
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(m => m.Edit.AWARD_TYPE, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.DropDownListFor(m => m.Edit.AWARD_TYPE, (List<SelectListItem>)Awa.AWARD_TYPE_Val.SelectItemEdit((Model.Edit != null) ? Model.Edit.AWARD_TYPE : Awa.AWARD_TYPE_Val.AWARD_TYPE_S), new { @class = "form-control", @onchange = "onAWARD_TYPE(this.value)" })
            @Html.ValidationMessageFor(m => m.Edit.AWARD_TYPE, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(m => m.Edit.AWARD_NAME, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(m => m.Edit.AWARD_NAME, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(m => m.Edit.AWARD_NAME, "", new { @class = "text-danger" })
            <br />
            <label class="text-info">PS.獎品名稱.最多50個中英文字元</label>
        </div>
    </div>
    <div id="Normal">
        <div class="form-group">
            @Html.LabelFor(m => m.Edit.COST_CASH, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">

                @if (Model.Edit != null)
                {
                    if (Model.Edit.AWARD_STATUS != Awa.AWARD_STATUS_Val.AWARD_STATUS_0 && Model.Edit.AWARD_NO != null)
                    {
                        @Html.EditorFor(m => m.Edit.COST_CASH, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                    }
                    else
                    {
                        @Html.EditorFor(m => m.Edit.COST_CASH, new { htmlAttributes = new { @class = "form-control" } })
                    }
                }
                else
                {
                    @Html.EditorFor(m => m.Edit.COST_CASH, new { htmlAttributes = new { @class = "form-control" } })
                }

                @Html.ValidationMessageFor(m => m.Edit.COST_CASH, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.Edit.QTY_STORAGE, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.EditorFor(m => m.Edit.QTY_STORAGE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(m => m.Edit.QTY_STORAGE, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.Edit.QTY_LIMIT, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.EditorFor(m => m.Edit.QTY_LIMIT, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(m => m.Edit.QTY_LIMIT, "", new { @class = "text-danger" })
                <br />
                <label class="text-info">PS.限制 每一個可兌換的數量，不填，不限制</label>
            </div>
        </div>
    </div>

    <div id="Bid">
        <div class="form-group">
            @Html.LabelFor(m => m.Edit.BID_START_PRICE, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.EditorFor(m => m.Edit.BID_START_PRICE, new { htmlAttributes = new { @class = "form-control", @onchange = "onBID_START_PRICE()" } })
                @Html.ValidationMessageFor(m => m.Edit.BID_START_PRICE, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.Edit.BID_BUY_PRICE, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.EditorFor(m => m.Edit.BID_BUY_PRICE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(m => m.Edit.BID_BUY_PRICE, "", new { @class = "text-danger" })
                <br />
                <label class="text-info">PS.需大於起標價，不填，無直購價</label>
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(m => m.Edit.BID_PER_UNIT_PRICE, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.EditorFor(m => m.Edit.BID_PER_UNIT_PRICE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(m => m.Edit.BID_PER_UNIT_PRICE, "", new { @class = "text-danger" })
                <br />
                <label class="text-info">PS.競標 限制每次增加的點數</label>
            </div>
        </div>

    </div>


    @if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student)
    {
        <div class="form-group">
            @Html.LabelFor(m => m.Edit.READ_LEVEL, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.DropDownListFor(m => m.Edit.READ_LEVEL, (List<SelectListItem>)ViewBag.ReadLevelSelectItem, new { @class = "form-control" })
                @Html.ValidationMessageFor(m => m.Edit.READ_LEVEL, "", new { @class = "text-danger" })
                <br />
                <label class="text-info"><img src="~/Content/img/web-revise-secretary-22.png" style="max-height:35px;margin-right:5px">PS.限制 閱讀認證 滿 1-10級，才可兌換該獎品</label>
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(m => m.Edit.PASSPORT_LEVEL, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.DropDownListFor(m => m.Edit.PASSPORT_LEVEL, (List<SelectListItem>)ViewBag.PassportLevelSelectItem, new { @class = "form-control" })
                @Html.ValidationMessageFor(m => m.Edit.PASSPORT_LEVEL, "", new { @class = "text-danger" })
                <br />
                <label class="text-info"><img src="~/Content/img/web-revise-secretary-38.png" style="max-height:35px;margin-right:5px">PS.限制 閱讀護照 滿 1-6級，才可兌換該獎品</label>
            </div>
        </div>
    }


    <div class="form-group">
        @Html.LabelFor(m => m.Edit.SDATETIME, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(m => m.Edit.SDATETIME, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(m => m.Edit.SDATETIME, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(m => m.Edit.EDATETIME, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(m => m.Edit.EDATETIME, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(m => m.Edit.EDATETIME, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(m => m.Edit.DESCRIPTION, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(m => m.Edit.DESCRIPTION, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(m => m.Edit.DESCRIPTION, "", new { @class = "text-danger" })
            <br />
            <label class="text-info">【備註說明】是否顯示於獎品清單中</label>

            @Html.EditorFor(m => m.Edit.SHOW_DESCRIPTION_YN,
                            "_CheckBoxList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Edit.SHOW_DESCRIPTION_YN)).ToHtmlString(),
                                CheckBoxItems = HtnlHelperService.Y_SelectItem(Model.Edit != null ? Model.Edit.SHOW_DESCRIPTION_YN : ""),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "",
                            })
            @Html.ValidationMessageFor(m => m.Edit.SHOW_DESCRIPTION_YN, "", new { @class = "text-danger" })
        </div>
    </div>



    <div class="form-group">
        @Html.LabelFor(m => m.Edit.HOT_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(m => m.Edit.HOT_YN,
                            "_RadioButtonList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Edit.HOT_YN)).ToHtmlString(),
                                RadioItems = HtnlHelperService.YNSelectItem(Model.Edit != null ? Model.Edit.HOT_YN : ""),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "",
                            })
            <br />
            <label class="text-info"><img src='~/Content/img/icons-07.png' style="max-height:35px;margin-right:5px">PS.此欄位會影響排序，順序【總召獎品、熱門獎項、兌換點數】</label>
        </div>
    </div>
    @if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student)
    {
        <div class="form-group">
            @Html.LabelFor(m => m.Edit.FULLSCREEN_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.EditorFor(m => m.Edit.FULLSCREEN_YN,
                                "_RadioButtonList",
                               new
                               {
                                   TagName = (Html.NameFor(m => m.Edit.FULLSCREEN_YN)).ToHtmlString(),
                                   RadioItems = new List<SelectListItem>()
                                   {
                                       new SelectListItem() { Text = "限誠實商店", Value = "Y", Selected = Model.Edit?.FULLSCREEN_YN?.ToString() == "Y" },
                                       new SelectListItem() { Text = "限一般獎品兌換", Value = "N", Selected = Model.Edit?.FULLSCREEN_YN?.ToString() == "N" || Model.Edit?.FULLSCREEN_YN?.ToString() == null },
                                       new SelectListItem() { Text = "兩者皆可", Value = "B", Selected = Model.Edit?.FULLSCREEN_YN?.ToString() == "B" }

                                   },
                                   Position = Position.Horizontal,
                                   Numbers = int.MaxValue,
                                   onclick = "",
                               })
                <br />
                @*<label class="text-info">PS.此欄位為是否顯示於行動支付頁面</label>*@
            </div>
        </div>
    }
    <div class="form-group">
        @Html.LabelFor(model => model.Edit.BUY_PERSON_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(model => model.Edit.BUY_PERSON_YN,
                "_CheckBoxList",
                new
                {
                    TagName = (Html.NameFor(m => m.Edit.BUY_PERSON_YN)).ToHtmlString(),
                    CheckBoxItems = HtnlHelperService.Y_SelectItem(Model.Edit != null ? Model.Edit.BUY_PERSON_YN : ""),
                    Position = Position.Horizontal,
                    Numbers = int.MaxValue,
                    onclick = "",
                })
            <br>
            <button type="button" class="btn btn-default btn-sm" title="選取對象" id="BTN_BUY_PERSON"
                    href="@Url.Action("Index", "REFT01", new { BTN_ID = "#BTN_BUY_PERSON",REF_TABLE = Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student ? "AWAT02" : "AWAT09" ,REF_KEY= Model.REF_KEY,REF_KEY_ID="#REF_KEY" })">
                選取對象
            </button>
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(m => m.Edit.PUSH_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(m => m.Edit.PUSH_YN,
                            "_CheckBoxList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Edit.PUSH_YN)).ToHtmlString(),
                                CheckBoxItems = HtnlHelperService.Y_SelectItem(Model.Edit != null ? Model.Edit.PUSH_YN : ""),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "",
                            })
            @Html.ValidationMessageFor(m => m.Edit.PUSH_YN, "", new { @class = "text-danger" })
            <br />
            <button type="button" class="btn btn-default btn-sm" title="選取通知人員" id="BTN_PUSH" href="@Url.Action("Index", "APPT03", new { BTN_ID = "#BTN_PUSH",REF_TABLE = AWAI01SearchViewModel.SouTableVal.GetSouTable(Model.Search.WhereSouTable) ,REF_KEY= Model.REF_KEY})">選取通知人員</button>
        </div>

    </div>
    <div class="form-group">
        @Html.Label("上傳圖片", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.Action("Upload", "Comm")
            @Html.ValidationMessageFor(m => m.Edit.IMG_FILE, "", new { @class = "text-danger" })
            <br />
            <label class="text-info">PS.上傳圖片，請上傳約 1:1 圖片，瘦高圖會變形</label>
        </div>
    </div>
    @if (Model.Edit != null)
    {
        if (Model.Edit.IMG_FILE != null)
        {
            string aImgUrl = string.Empty;
            if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
            {
                aImgUrl = ViewBag.ImgUrl + @"/" + Model.Edit.IMG_FILE;
            }
            else
            {
                aImgUrl = ViewBag.ImgUrl + Model.Edit.SCHOOL_NO + @"/" + Model.Edit.IMG_FILE;
            }

    <div class="form-group">
        @Html.Label("原圖片", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        @{
            string Url = aImgUrl + "?v=" + DateTime.Now.ToString();
        }
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.HiddenFor(m => m.Edit.IMG_FILE)
            <img src="@Url" class="img-responsive"  href="@Url" style="max-height:300px;max-width:300px" id="ImageA1"/>
            @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = aImgUrl, ImgURL_S = "", ImgUrl_M = "", ImgID = "ImageA1" })
        </div>
    </div>
        }
    }
    <div class="form-group">
        @Html.Label("影片連結", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
        <div class="col-md-9 col-sm-9 col-lg-10">
            @Html.EditorFor(m => m.Edit.VIDEO_PATH, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(m => m.Edit.VIDEO_PATH, "", new { @class = "text-danger" })
            <br />
            <label class="text-info">PS.複製youtube影片連結</label>
        </div>
    </div>

</div>

<script type="text/javascript">

    $(document).ready(function () {

        $("#BTN_PUSH").colorbox({
            iframe: true, width: "80%", height: "80%", opacity: 0.82
        });
        $("#BTN_BID_TARGET").colorbox({
            iframe: true, width: "80%", height: "80%", opacity: 0.82
        });
        $("#BTN_BUY_PERSON").colorbox({
            iframe: true, width: "80%", height: "80%", opacity: 0.82
        });
       
        onAWARD_TYPE($("#@Html.IdFor(m=>m.Edit.AWARD_TYPE)").val())
      
    });


    var opt = {
        showMonthAfterYear: true,
        format: moment().format('YYYY-MM-DD h:mm'),
        showSecond: true,
        showButtonPanel: true,
        showTime: true,
        beforeShow: function () {
            setTimeout(
                function () {
                    $('#ui-datepicker-div').css("z-index", 15);
                }, 100
            );
        },
        onSelect: function (dateText, inst) {
            $('#' + inst.id).attr('value', dateText);
        }
    };
    var Today1 = new Date();
    var newDate = new Date();
    newDate = Today1.setMonth(Today1.getMonth() + 6);
    console.log(newDate);
    $("#@Html.IdFor(m => m.Edit.SDATETIME)").datetimepicker(opt);
       $("#@Html.IdFor(m => m.Edit.EDATETIME)").datetimepicker(opt);

    if ($("#@Html.IdFor(m => m.Edit.SDATETIME)").val()==''
        && $("#@Html.IdFor(m => m.Edit.EDATETIME)").val()=='')
    {
        var Today = new Date();
    
      @*//  $("#@Html.IdFor(m => m.Edit.SDATETIME)").val(moment(Today).format('YYYY-MM-DD HH:mm:00'));*@
        //Today.setDate(Today.getDate + 14);
       
        //Today.setMonth(Today.getMonth + 6);
        ChooseType();
       // $("#@Html.IdFor(m => m.Edit.EDATETIME)").val(moment(addMonths(6)).format('YYYY/MM/DD 00:00'));
    }



    function ChooseType() {



        // 建立日期物件,並初始化,完成文字轉日期
        var date = new Date();
        var str = "";
        //日期轉文字方式一:
      //  str = date.format("yyyy-MM-dd");
        var year = date.getFullYear();//年
        var month = date.getMonth() + 7;//月 +6個月  因為js裡month從0開始,所以要加1
        if (month > 12) {
            year++;
            month -= 12;
        }
        if (month < 10) {
            month = "0" + month;
        }
        var date2 = new Date(year, month, 0);//新的年月
        var day1 = date.getDate();
        var day2 = date2.getDate();
        if (day1 > day2) {  //防止+6月後沒有31天
            day1 = day2;
        }
        str = year + '-'
            + month + '-'
            + day1;

        //最後賦值文字框顯示
       // time2[0].value = str;
        console.log(str);
        $("#@Html.IdFor(m => m.Edit.EDATETIME)").val(moment(str).format('YYYY/MM/DD  HH:mm'));
    }


    function onAWARD_TYPE(Value)
    {
        if (Value == '@Awa.AWARD_TYPE_Val.AWARD_TYPE_B') {
            $('#Bid').show()
            $('#Normal').hide()
            $('#@Html.IdFor(m=>m.Edit.QTY_STORAGE)').val(1)
        }
        else {
            $('#Bid').hide()
            $('#Normal').show()
        }

    }
    function addMonths(numOfMonths, date = new Date()) {
        date.setMonth(date.getMonth() + numOfMonths);

        return date;
    }
    function onBID_START_PRICE() {

        var BID_START_PRICE = $('#@Html.IdFor(m=>m.Edit.BID_START_PRICE)').val()
        $('#@Html.IdFor(m=>m.Edit.COST_CASH)').val(BID_START_PRICE)
    }


</script>