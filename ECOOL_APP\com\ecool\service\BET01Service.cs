﻿
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace com.ecool.service
{
    public class BET01Service : ServiceBase
    {
        /// <summary>
        /// Description : 查詢公告類別
        /// </summary>
        /// <returns></returns>
        public static List<uBET01> USP_BET01_QUERY()
        {
            List<uBET01> list_data = new List<uBET01>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" select A.CLASS_TYPE,B.CLASS_NAME,A.ORDER_BY,A.SHOW_COUNT ");
                sb.Append("from BET01 A (NOLOCK) ");
                sb.Append("INNER JOIN BET01_LANG B  (NOLOCK) ON A.CLASS_TYPE=B.CLASS_TYPE ");
                sb.Append("WHERE B.LANGUAGE_ID='zh-TW' ");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uBET01()
                    {
                        CLASS_TYPE = dr["CLASS_TYPE"].ToString(),
                        CLASS_NAME = dr["CLASS_NAME"].ToString(),
                        ORDER_BY = Int32.Parse(dr["ORDER_BY"].ToString()),
                        SHOW_COUNT = Int32.Parse(dr["SHOW_COUNT"].ToString()),
                    });
                }
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;

        }

        /// <summary>
        /// 取得公告類別
        /// </summary>
        /// <param name="SelectedVal">預設的選項</param>
        /// <returns></returns>
        public static List<SelectListItem> GetCLASS_TYPE(string SelectedVal)
        {
            List<SelectListItem> CLASS_TYPE_ITEMS = new List<SelectListItem>();

            var dt = BET01Service.USP_BET01_QUERY();

            //CLASS_TYPE_ITEMS.Add(new SelectListItem()
            //    { 
            //       Text="",
            //       Value = ""
            //    });

            foreach (var item in dt)
            {
                SelectListItem NewDATA = new SelectListItem();
                NewDATA.Text = item.CLASS_NAME;
                NewDATA.Value = item.CLASS_TYPE;

                if (string.IsNullOrWhiteSpace(SelectedVal)==false)
                {
                    if (item.CLASS_TYPE==SelectedVal)
                    {
                        NewDATA.Selected = true;
                    }
                }

                CLASS_TYPE_ITEMS.Add(NewDATA);
            }

            return CLASS_TYPE_ITEMS;
        }

    }
}
