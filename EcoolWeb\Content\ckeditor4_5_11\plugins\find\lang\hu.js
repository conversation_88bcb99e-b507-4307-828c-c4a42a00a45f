﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'hu', {
	find: 'Keres<PERSON>',
	findOptions: '<PERSON><PERSON><PERSON><PERSON><PERSON>ás<PERSON>',
	findWhat: 'Keresett szöveg:',
	matchCase: 'Kis- és nagybetű megkülönböztetése',
	matchCyclic: 'Ciklikus keresés',
	matchWord: 'Csak ha ez a teljes szó',
	notFoundMsg: 'A keresett szöveg nem található.',
	replace: 'Csere',
	replaceAll: 'Az összes cseréje',
	replaceSuccessMsg: '%1 egyezőség cserélve.',
	replaceWith: 'Csere erre:',
	title: 'Keresés és csere'
} );
