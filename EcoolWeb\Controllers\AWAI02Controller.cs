﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;

using System.IO;
using System.Text.RegularExpressions;

using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.CustomAttribute;
using com.ecool.service;
using ECOOL_APP.com.ecool.Models.DTO;
using Dapper;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWAI02Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        // GET: AWAI02
        [CheckPermission]
        public ActionResult Index()
        {
            AWAI02IndexViewModel model = new AWAI02IndexViewModel();
            model.PlayerList = db.AWAT06.ToList();
            return View(model);
        }

        /// <summary>
        /// 角色娃娃展示櫥窗
        /// </summary>
        /// <returns></returns>
        [CheckPermission]
        public ActionResult Gallery(string PlayerType)
        {
            List<AWAT06> model = null;
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            //判斷是否有[我的角色娃娃]權限
            ViewBag.VisibleMyGallery = (user != null) ?
                PermissionService.GetPermission_Use_YN("AWAI02", "MyGallery", SchoolNO, user.USER_NO) : "N";
            //判斷是否有[管理角色娃娃]權限
            ViewBag.VisibleIndex = (user != null) ?
                PermissionService.GetPermission_Use_YN("AWAI02", "Index", SchoolNO, user.USER_NO) : "N";

            ViewBag.PlayerTypeList = db.AWAT06.Select(a => a.PLAYER_TYPE).Distinct().ToList();

            IQueryable<AWAT06> Players=db.AWAT06.Where(a => a.AWARD_STATUS=="1" );
            if (string.IsNullOrWhiteSpace(PlayerType)==false)
                Players = Players.Where(a => a.PLAYER_TYPE == PlayerType);

            if (user!=null)
            { 
                List<int> MyPlayers =
                    db.AWAT07.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Select(a=>a.PLAYER_NO).ToList();

                Players = Players.Where(a => MyPlayers.Contains(a.PLAYER_NO) == false);
            }
            model = Players.ToList();

            return View(model);
        }

        /// <summary>
        /// 我的角色娃娃
        /// </summary>
        /// <returns></returns>
        [CheckPermission] //檢查權限
        public ActionResult MyGallery()
        {
            UserProfile user = UserProfileHelper.Get();
            List<int> MyPlayerNoList =
                db.AWAT07.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Select(a => a.PLAYER_NO).ToList();

            List<AWAT06> model = db.AWAT06.Where(a => MyPlayerNoList.Contains(a.PLAYER_NO)).ToList();

            return View(model);
        }

        public ActionResult Setdefault(int? PLAYER_NO)
        {
            if (PLAYER_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            UserProfile user = UserProfileHelper.Get();
            List<AWAT07> MyPlayers =
                db.AWAT07.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).ToList();

            //預設改成新的
            foreach (AWAT07 t7 in MyPlayers)
            {
                t7.DEFAULT_YN = (t7.PLAYER_NO == PLAYER_NO);
            }
            db.SaveChanges();

            UserProfile.RefreshCashInfo(user,ref db);

            List<int> MyPlayerNoList = MyPlayers.Select(a => a.PLAYER_NO).ToList();
            List<AWAT06> model = db.AWAT06.Where(a => MyPlayerNoList.Contains(a.PLAYER_NO)).ToList();

            return RedirectToAction("MyGallery", model);
        }

        [CheckPermission] //檢查權限
        public ActionResult Buy(int? PLAYER_NO)
        {
            if (PLAYER_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            AWAT06 aWAT06 = db.AWAT06.Find(PLAYER_NO);
            if (aWAT06 == null)
            {
                return HttpNotFound();
            }

            UserProfile user = UserProfileHelper.Get();

            if (user.CASH < (int)aWAT06.COST_CASH)
            {
                ViewBag.NGError = "點數不足";
                TempData["StatusMessage"] = ViewBag.NGError;
            }

            return View(aWAT06);
        }
       
        [HttpPost]
        [ValidateAntiForgeryToken]
        [CheckPermission]
        public ActionResult Buy(AWAT06 aWAT06, HttpPostedFileBase Imgfile)
        {
            AWAT06 oldAWAT06 = db.AWAT06.Find(aWAT06.PLAYER_NO);
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (oldAWAT06 == null)
            {
                return HttpNotFound();
            }

            UserProfile LoginUser = UserProfileHelper.Get();
            List<AWAT07> MyPlayers =
                db.AWAT07.Where(a => a.SCHOOL_NO == LoginUser.SCHOOL_NO && a.USER_NO == LoginUser.USER_NO).ToList();

            //已經有了
            if (MyPlayers.Where(a=>a.PLAYER_NO==aWAT06.PLAYER_NO).Any())
            {
                return RedirectToAction("MyGallery");
            }
            //檢查點數
            if ( oldAWAT06.COST_CASH>0)
            {
                bool Enough = false;
                AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == LoginUser.SCHOOL_NO && user.USER_NO == LoginUser.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    Enough = (UserCash.CASH_AVAILABLE.Value > oldAWAT06.COST_CASH);
                }
                if (Enough == false) RedirectToAction("Gallery");
            }


            AWAT07 tran7 = db.AWAT07.Create();
            
            tran7.SCHOOL_NO = LoginUser.SCHOOL_NO;
            tran7.USER_NO = LoginUser.USER_NO;
            tran7.PLAYER_NO = oldAWAT06.PLAYER_NO;
            tran7.TRANS_DATE = DateTime.Now;
            tran7.TRANS_CASH = oldAWAT06.COST_CASH;
            tran7.DEFAULT_YN = true;

            db.AWAT07.Add(tran7);

            //預設改成新的
            foreach(AWAT07 t7 in MyPlayers)
            {
                t7.DEFAULT_YN = false;
            }

            ECOOL_APP.CashHelper.AddCash(LoginUser,Convert.ToInt32(tran7.TRANS_CASH) * -1, tran7.SCHOOL_NO, tran7.USER_NO, "AWAI02", oldAWAT06.PLAYER_NO.ToString(),false, ref db,ref valuesList);

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "新增完成";
            }
            catch (Exception e)
            {
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
            }


            UserProfile.RefreshCashInfo(LoginUser, ref db);
            UserProfileHelper.Set(LoginUser);

            return RedirectToAction("Gallery");
        }

        // GET: AWAI02/Create
        [CheckPermission] //檢查權限
        public ActionResult Create()
        {
            return View();
        }

        // POST: AWAI02/Create
        // 若要免於過量張貼攻擊，請啟用想要繫結的特定屬性，如需
        // 詳細資訊，請參閱 http://go.microsoft.com/fwlink/?LinkId=317598。
        [HttpPost]
        [ValidateAntiForgeryToken]
        [CheckPermission] //檢查權限
        public ActionResult Create([Bind(Include = "PLAYER_NO,SCHOOL_NO,PLAYER_SEX,PLAYER_NAME,PLAYER_TYPE,COST_CASH,TREASURE_TYPE,SDATETIME,EDATETIME,DESCRIPTION,IMG_FILE,HOT_YN")] AWAT06 aWAT06, HttpPostedFileBase Imgfile)
        {
            if (ModelState.IsValid == false)
                return View(aWAT06);

            UserProfile user = UserProfileHelper.Get();
            aWAT06.CRE_USER = user.USER_NO;
            aWAT06.CRE_DATE = DateTime.Now;
            aWAT06.SDATETIME = DateTime.Now;
            aWAT06.AWARD_STATUS = "1";

            //處理上傳檔案
            bool ans = doImage(aWAT06, Imgfile);
            if (ans==false) return View(aWAT06);

            //儲存資料
            db.AWAT06.Add(aWAT06);

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "新增完成";
            }
            catch (Exception e)
            {
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
            }

           

            return RedirectToAction("Index");  
        }

        private bool doImage(AWAT06 aWAT06, HttpPostedFileBase Imgfile)
        {
            if (Imgfile == null) return false;
            //a.組檔案名稱
            aWAT06.IMG_FILE = Path.GetFileName(Imgfile.FileName);
            Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
            if (regexCode.IsMatch(aWAT06.IMG_FILE.ToLower()) == false)
            {
                return false;
            }

            //b.組上傳資料夾路徑
            string UploadImageRoot =
                System.Web.Configuration.WebConfigurationManager.AppSettings["PlayersImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~/Content/Players/";
            string imgPath =Request.MapPath(UploadImageRoot);
            if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

            //c. SAve
            string FilePath = Path.Combine(imgPath, aWAT06.IMG_FILE);
            if (System.IO.File.Exists(FilePath)) return false;
            Imgfile.SaveAs(FilePath);
            
            return true;
        }

        // GET: AWAI02/Edit/5
        [CheckPermission] //檢查權限
        public ActionResult Edit(int? PLAYER_NO)
        {
            if (PLAYER_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            AWAT06 aWAT06 = db.AWAT06.Find(PLAYER_NO);
            if (aWAT06 == null)
            {
                return HttpNotFound();
            }
            return View(aWAT06);
        }

        // POST: AWAI02/Edit/5
        // 若要免於過量張貼攻擊，請啟用想要繫結的特定屬性，如需
        // 詳細資訊，請參閱 http://go.microsoft.com/fwlink/?LinkId=317598。
        [HttpPost]
        [ValidateAntiForgeryToken]
        [CheckPermission] //檢查權限
        public ActionResult Edit([Bind(Include = "PLAYER_NO,SCHOOL_NO,PLAYER_SEX,PLAYER_NAME,PLAYER_TYPE,COST_CASH,TREASURE_TYPE,SDATETIME,EDATETIME,DESCRIPTION,IMG_FILE,IMG2_FILE,AWARD_STATUS,HOT_YN")] AWAT06 aWAT06, HttpPostedFileBase Imgfile)
        {
            if (ModelState.IsValid == false) return View(aWAT06);

            AWAT06 oldAWAT06 = db.AWAT06.Find(aWAT06.PLAYER_NO);
            if (oldAWAT06 == null)
            {
                return HttpNotFound();
            }

            oldAWAT06.PLAYER_NAME = aWAT06.PLAYER_NAME;
            oldAWAT06.PLAYER_TYPE = aWAT06.PLAYER_TYPE;
            oldAWAT06.COST_CASH = aWAT06.COST_CASH;
            oldAWAT06.PLAYER_SEX = aWAT06.PLAYER_SEX;
            oldAWAT06.CHG_DATE = DateTime.Now;

            //處理上傳檔案
            doImage(oldAWAT06, Imgfile);

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "修改完成";
            }
            catch (Exception e)
            {
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
            }
        

            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
