﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using log4net;
using log4net.Config;
using System.Web;

namespace com.ecool.util
{
    public class NcssLogger
    {
        // private static NcssLogger ncssLogger = new NcssLogger();

        // private NcssLogger()
        //{
        //    String fileName=string.Empty;
        //    String filePath = string.Empty;
        //        try
        //        {
        //            // fileName = "tp_framework_config.xml";
        //            // filePath = findLogConfigFileURL();
        //            //XmlConfigurator.Configure(new System.IO.FileInfo(filePath + fileName));
        //        string log4netPath = HttpContext.Current.Server.MapPath("~/log4net.config");
        //        log4net.Config.XmlConfigurator.ConfigureAndWatch(new System.IO.FileInfo(log4netPath));
        //    }
        //        catch (Exception ex)
        //        {
        //            string ErrMSg = filePath + fileName;
        //            ErrMSg += "\r\n" + ex.ToString();
        //            throw new Exception(ErrMSg);
        //        }  
        //}
        
        //public static ILog getLogger(String logger_name)
        //{
        //    return LogManager.GetLogger(logger_name);
        //}

        //public static ILog getLogger(Type classType)
        //{
        //    return LogManager.GetLogger(classType);            
        //}

        //private static string findLogConfigFileURL()
        //{
        //    string LogPath =
        //        System.Web.Configuration.WebConfigurationManager.AppSettings["LogPath"];
        //    if (string.IsNullOrWhiteSpace(LogPath)==false) return LogPath;

        //    String code_base = typeof(NcssLogger).Assembly.CodeBase;
        //    int last_slash = code_base.LastIndexOf("/");
        //    String file_path = code_base.Substring(0, last_slash + 1);
        //    file_path = file_path.Replace("file:///", "");
        //    return file_path;
        //}
    }
}
