﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'it', {
	button: {
		title: 'Proprietà bottone',
		text: '<PERSON><PERSON> (Valore)',
		type: 'Tipo',
		typeBtn: 'Bottone',
		typeSbm: 'Invio',
		typeRst: 'Annulla'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Proprietà checkbox',
		radioTitle: 'Proprietà radio button',
		value: '<PERSON><PERSON>',
		selected: 'Selezionato',
		required: '<PERSON><PERSON>'
	},
	form: {
		title: 'Proprietà modulo',
		menu: 'Proprietà modulo',
		action: 'Azione',
		method: 'Metodo',
		encoding: 'Codifica'
	},
	hidden: {
		title: 'Proprietà campo nascosto',
		name: 'Nome',
		value: '<PERSON><PERSON>'
	},
	select: {
		title: 'Proprietà menu di selezione',
		selectInfo: 'Info',
		opAvail: 'Opzioni disponibili',
		value: 'Valore',
		size: 'Dimensione',
		lines: 'righe',
		chkMulti: 'Permetti selezione multipla',
		required: '<PERSON><PERSON>',
		opText: 'Testo',
		opValue: 'Valore',
		btnAdd: 'Aggiungi',
		btnModify: 'Modifica',
		btnUp: 'Su',
		btnDown: 'Gi',
		btnSetValue: 'Imposta come predefinito',
		btnDelete: 'Rimuovi'
	},
	textarea: {
		title: 'Proprietà area di testo',
		cols: 'Colonne',
		rows: 'Righe'
	},
	textfield: {
		title: 'Proprietà campo di testo',
		name: 'Nome',
		value: 'Valore',
		charWidth: 'Larghezza',
		maxChars: 'Numero massimo di caratteri',
		required: 'Richiesto',
		type: 'Tipo',
		typeText: 'Testo',
		typePass: 'Password',
		typeEmail: 'Email',
		typeSearch: 'Cerca',
		typeTel: 'Numero di telefono',
		typeUrl: 'URL'
	}
} );
