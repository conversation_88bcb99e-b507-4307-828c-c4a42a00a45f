﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SchoolBorrowBookViewModel
    {
        [Display(Name = "學年度")]
        public string Where_SYEAR { get; set; }

        [Display(Name = "月份")]
        public string Where_MONTH { get; set; }

        public List<SchoolBorrowBookRank> SchoolRankList { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }
    }

    public class SchoolBorrowBookRank
    {
        [Display(Name = "月份")]
        public string Months { get; set; }

        [Display(Name = "學校代碼")]
        public string SCHOOL_NO { get; set; }

        [Display(Name = "學校")]
        public string SHORT_NAME { get; set; }

        [Display(Name = "借書量(本)")]
        public int BORROW_BOOK_COUNT { get; set; }

        [Display(Name = "平均借書量(本)")]
        public double BORROW_BOOK_AVG { get; set; }

        [Display(Name = "借書量排名")]
        public int RANK { get; set; }

        [Display(Name = "學生數")]
        public int SCHOOLQTY { get; set; }

        [Display(Name = "平均借書量排名")]
        public int RANK_AVG { get; set; }
    }
}