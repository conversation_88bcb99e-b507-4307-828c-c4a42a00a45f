﻿
@model ZZZI34IndexViewModel
@using ECOOL_APP.com.ecool.service
@using ECOOL_APP.com.ecool.util
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    bool IsAdmin = ViewBag.IsAdmin;

    var ImgOnePath = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-05.png");
    var ImgTwoPath = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-09.png");
    int Num = 1;
    string QU = "";
    Layout = "~/Views/Shared/_LayoutWebView1.cshtml";

}
<script src=@Url.Content("~/Scripts/clipboard.min.js")></script>

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Scripts/grids.js"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<link href="~/Content/styles/newArtgallery.min.css?V=@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
<style type="text/css">
    body {
        background-color: #8a8070;
        background-image: url(../Content/images/wood_bg.png);
        min-height: 100vh;
    }

    .element {
        float: left;
        padding: 3px;
        box-sizing: border-box;
    }

    .modal-dialog {
        right: auto;
        left: 50%;
        width: 600px;
        padding-top: 30px;
        padding-bottom: 30px;
    }

    #DivNextButton {
        position: fixed; /*固定在網頁上不隨卷軸移動，若要隨卷軸移動用absolute*/
        top: 50%; /*設置垂直位置*/
        left: 10px; /*設置水平位置，依所放的內容多寡需要自行手動調整*/
        padding: 10px 20px;
        z-index: 99999;
        border-radius: 10px; /*圓角*/
        -moz-border-radius: 10px;
        -webkit-border-radius: 10px;
    }

    #DivPrevButton {
        position: fixed; /*固定在網頁上不隨卷軸移動，若要隨卷軸移動用absolute*/
        top: 50%; /*設置垂直位置*/
        right: 10px; /*設置水平位置，依所放的內容多寡需要自行手動調整*/
        padding: 10px 20px;
        z-index: 99999;
        border-radius: 10px; /*圓角*/
        -moz-border-radius: 10px;
        -webkit-border-radius: 10px;
    }


    .css-table {
        display: table;
        border-collapse: collapse;
        width: 100%
    }

        .css-table .thead {
            display: table-header-group;
        }

        .css-table .tbody {
            display: table-row-group;
        }

        .css-table .tr {
            display: table-row;
            padding-bottom: 2px
        }

        .css-table .th, .css-table .td {
            display: table-cell;
            padding-left: 3px;
            vertical-align: middle;
            text-align: center;
        }

    .Div-EZ-ArtGallery-pointer {
        cursor: pointer;
        position: relative;
        width: 260px;
        height: 228px;
        margin: 100px auto;
    }

    .Div-EZ-ArtGallery-book {
        width: 387px;
        z-index: 3;
    }

    .Div-EZ-ArtGallery-div-img {
        top: 66px;
        left: 76px;
        position: absolute;
        width: 260px;
        height: 228px;
        z-index: 6;
        text-align: center;
        vertical-align: middle;
        line-height: 76px;
    }

    .Div-EZ-ArtGallery-img {
        max-width: 500px;
        max-height: 500px;
    }

    .Div-EZ-ArtGallery-text {
        width: 129px;
        margin-top: 280px;
    }

    .modal {
        position: fixed;
        top: auto;
        right: 0;
        left: 0;
        z-index: 1040;
        display: none;
        overflow: auto;
        overflow-y: scroll;
    }
</style>
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@*@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereUSER_NO)*@
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_NO)
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_TYPE)
@Html.HiddenFor(m => m.Search.WhereSTATUS)
@Html.HiddenFor(m => m.Search.WhereSearch)
@Html.HiddenFor(m => m.Search.WhereMyWork)
@Html.HiddenFor(m => m.IsPostBack, new { Value = "Y" })

<div class="eCoolArtGallery @(@Model.ListData.Count()== 1 ? "eCoolArtGallery-only" : "")" id="PageContent">
    @foreach (var item in Model.ListData)
    {

        var ImgPath = ImgOnePath;

        if (Num % 2 == 0)
        {
            ImgPath = ImgTwoPath;
        }


        <div class="eCoolArtGallery-item">
            <span class="eCoolArtGallery-sticker" onclick="showArtGalleryWorkList('@item.ART_GALLERY_NO')">點我觀賞</span>

            <button type="button" class="eCoolArtGallery-bookbg" onclick="showArtGalleryWorkList('@item.ART_GALLERY_NO')" title="點我觀賞「@item.ART_SUBJECT」">
                <div class="imgBox">
                    @{
                        string NewImg = "";
                        string SUrl = "";
                        if (item.COVER_FILE != "")
                        {

                            NewImg = item.COVER_FILE.Replace(Path.GetExtension(item.COVER_FILE), "_S" + Path.GetExtension(item.COVER_FILE));
                            SUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.SCHOOL_NO, item.ART_GALLERY_NO, NewImg);
                            // if (Model.Search.WhereSTATUS == "1")
                            //{
                            // SUrl= SUrl+ "&FromURL=ALL";
                            //}
                            //  else {
                            //   SUrl = SUrl + "&FromURL=G";

                            //}
                        }
                        <img src="@SUrl" class="Div-EZ-ArtGallery-img" alt="" />
                    }
                </div>
                <div class="txt-info">
                    <strong class="bookname">@StringHelper.LeftStringR(item.ART_SUBJECT, 15)</strong>
                    <p class="info">@item.ART_DESC</p>
                </div>
            </button>
            <div class="eCoolArtGallery-info">
                <div class="left">
                    <small>上傳者</small>
                    <strong>@item.SNAME</strong>
                </div>
                <p class="eCoolArtGallery-info-footer">
                    <span>@(item.CRE_DATE.Value.ToString("yyyy/MM/dd")) 上架</span><span> @(item.WorkCount) @(item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo ? "張" : "部")作品</span>
                    @if (user != null)
                    {
                        if (IsAdmin || user.USER_KEY == item.CRE_PERSON || (item.STATUS == ADDT21.STATUSVal.Verification && (item.VERIFIER == user.USER_NO || ViewBag.VisibleVerify)))
                        {


                            if (@ADDT21.STATUSVal.GetDesc(item.STATUS) == "已通過")
                            {
                                <span class="label label-danger">已通過</span>
                            }
                            else
                            {
                                <span class="label label-default">@ADDT21.STATUSVal.GetDesc(item.STATUS)</span>
                            }

                        }
                    }
                </p>
            </div>
           
        </div>


        Num++;
    }

    @foreach (var item in Model.ListData)
    {

        var ImgPath = ImgOnePath;

        if (Num % 2 == 0)
        {
            ImgPath = ImgTwoPath;
        }
        <div class="modal fade bs-example-modal-lg" id="myQrCodeModal@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="input-group">
                        @if (ViewBag.WinOpenShareUrlLink != null)
                        {
                            <img src="@Url.Action("Cre", "Barcode", new { Value = item.QRCODEGARYPHOTO})" style="max-width:150px" />
                        }
                    </div><!-- /input-group -->
                </div>
            </div>
        </div>
        <div class="modal fade bs-example-modal-lg" id="myQrCodeModal@(item.ART_GALLERY_NO)" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="input-group">
                        @if (ViewBag.WinOpenShareUrlLink != null)
                        {
                            <img src="@Url.Action("Cre", "Barcode", new { Value = item.QRCODEGARY})" style="max-width:150px" />
                        }
                    </div><!-- /input-group -->
                </div>
            </div>
        </div>
        <div class="modal fade bs-example-modal-lg" id="myShareUrlModal@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
            <div class="modal-dialog modal-sm" role="document" style="top:200px;background-color: #ffffff;">
                <div class="modal-content@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)">

                    <div class="input-group">
                        <span class="input-group-btn">
                            <button type="button" id="id_copy"
                                    data-clipboard-target="#id_text"
                                    data-clipboard-action="copy" onclick="OnCopy('@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)')">
                                點擊複製
                            </button>
                        </span>
                        <div id="id_text@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)">@item.QRCODEGARYPHOTO</div>
                        <div id="success@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" style="display:none">已複製</div>
                        <input id="copyStr@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" type="hidden" value="@item.QRCODEGARYPHOTO">
                    </div><!-- /input-group -->
                </div>
            </div>
        </div>
        <div class="modal fade bs-example-modal-lg" id="myShareUrlModal@(item.ART_GALLERY_NO)" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
            <div class="modal-dialog modal-sm" role="document" style="top:200px;background-color: #ffffff;">
                <div class="modal-content@(item.ART_GALLERY_NO)">

                    <div class="input-group">
                        <span class="input-group-btn">
                            <button type="button" id="id_copy"
                                    data-clipboard-target="#id_text"
                                    data-clipboard-action="copy" onclick="OnCopy('@(item.ART_GALLERY_NO)')">
                                點擊複製
                            </button>
                        </span>
                        <div id="id_text@(item.ART_GALLERY_NO)">@item.QRCODEGARY</div>
                        <div id="success@(item.ART_GALLERY_NO)" style="display:none">已複製</div>
                        <input id="copyStr@(item.ART_GALLERY_NO)" type="hidden" value="@item.QRCODEGARY">
                    </div><!-- /input-group -->
                </div>
            </div>
        </div>
        Num++;
    }

</div>
<div class="eCoolArtGallery-pages" id="page1">
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                                .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                .SetNextPageText(PageGlobal.DfSetNextPageText)
                                )
</div>

<script>
 //   $('.element').responsiveEqualHeightGrid();
    var targetFormID = '#form1';
       function showArtGalleryWorkList(Value)
        {
           $('#@Html.IdFor(m => m.Search.WhereART_GALLERY_NO)').val(Value)
          

            @*$.ajax({
            type: 'POST',
            url: '@Url.Action("ArtGalleryDateMid", (string)ViewBag.BRE_NO)',
            data: $(targetFormID).serialize(),
            success: function (data) {
                $.colorbox({ html: data, width: "80%", height: "80%", opacity: 0.82 });
            }
        });*@
            $(targetFormID).attr("action", "@Url.Action("ArtGalleryDateMid", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
    }
       function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
    };
    $('.element').responsiveEqualHeightGrid();
            function ShowMessege(str) {
                if (str != "" && str != undefined) {
                    alert(str);

                }
                else {
                    alert("本藝廊無說明");

                }

            }
    function onWinOpenShareUrlLink(obj) {
        var $modal = $('#myShareUrlModal' + obj);

        $modal.on('show.bs.modal', function () {
            var $this = $(this);
            var $modal_dialog = $this.find('.modal-dialog');
            $this.css('display', 'block');

            $modal_dialog.css({ 'margin-top': Math.max(0, ($(window).height() - $modal_dialog.height()) - 180) });
        });
        $('.modal-content' + obj).attr("style", "width:500px");
        //$('#id_text').html(obj);
        //$('#copyStr').html(obj);
        $('#success' + obj).hide()
        $('#id_text' + obj).show()

        if ($('#myShareUrlModal' + obj).is(':visible') == false) {
            $('#myShareUrlModal' + obj).modal('show');
        }
    }
    function OnCopy(str) {
        var ArearValue = "";
        var ArearValuesuccess = "";
        ArearValue = str;
        ArearValuesuccess = str;
        ArearValue = "id_text" + ArearValue;
        ArearValuesuccess = "success" + ArearValuesuccess;
        console.log(ArearValuesuccess);
        var content = document.getElementById(ArearValue);
        var content1 = document.getElementById(ArearValuesuccess);
        content = content.innerHTML;
        navigator.clipboard.writeText(content)
            .then(() => {
                $("#" + ArearValuesuccess).show()
                $("#" + ArearValue).hide()
            })
            .catch(err => {
                console.log('Something went wrong', err);
            })
    }
    function onWinOpenShareQRCODE(obj) {

        if ($('#' + obj).is(':visible') == false) {
            $('#' + obj).modal('show');
        }
    }
    function funAjax() {
        $("#page1").html('')
            $.ajax({
                url: '@Url.Action("_WorkListPageContent", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {

                    $('#PageContent').html(data);
                }
            });
        }
       @*function ShowColorbox(ROWID)
    {

        $('#@Html.IdFor(m=>m.WorkSearch.Page)').val(ROWID)

        $.ajax({
            type: 'POST',
            url: '@Url.Action("OneIndex", (string)ViewBag.BRE_NO)',
            data: $(targetFormID).serialize(),
            success: function (data) {
                $.colorbox({ html: data, width: "80%", height: "80%", opacity: 0.82 });
            }
        });
    }*@
</script>
