﻿@using ECOOL_APP.com.ecool.util;
@{
    Layout = "~/Views/Shared/_LayoutChildMonth.cshtml";

    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    List<BET02> BET02List = ViewBag.BET02List;
}

@Html.Partial("_Notice")

<strong class="ribbon ribbon-color1">
    <span class="ribbon-content">活動公告</span>
</strong>
<table class="table mt-1">
    <tbody>

        @if (BET02List != null)
        {
            if (BET02List.Count > 0)
            {
                foreach (var item in BET02List)
                {
                    <tr>
                        <td class="text-center">
                            <span class="control-label">@Html.DisplayFor(modelItem => item.S_DATE, "ShortDateTime")</span>
                        </td>
                        <td>
                            <a href="javascript:void(0);" class="btn-table-link" onclick="postToDetails('@(HtmlUtility.ComputeSHA256(item.BULLET_ID))', '@HtmlUtility.ComputeSHA256((TempData["SearchContents"]?.ToString() ?? ""))', '@HtmlUtility.ComputeSHA256((TempData["page"]?.ToString() ??""))', '@HtmlUtility.ComputeSHA256(ViewBag.PrevAction)', '@HtmlUtility.ComputeSHA256(ViewBag.Layout)')">


                                @item.GetSubject("zh-TW")
                            </a>
                            <a href="javascript:void(0);" class="btn-table-link" onclick="postToDetails('@(HtmlUtility.ComputeSHA256(item.BULLET_ID))', '@HtmlUtility.ComputeSHA256(""))', '@HtmlUtility.ComputeSHA256("")', '@HtmlUtility.ComputeSHA256("ChildMonthIndex")', '@HtmlUtility.ComputeSHA256("_LayoutChildMonth")')">
                                @item.GetSubject("zh-TW")  
                                @*@Html.ActionLink(item.GetSubject("zh-TW"), "Details", new { controller = "ZZZI04", BULLET_ID = item.BULLET_ID, PrevAction = "ChildMonthIndex", Layout = "_LayoutChildMonth" })*@
                                @if (item.TOP_YN == "Y")
                                {
                                    <img src="~/Content/img/icon/hot.jpg" />
                                }
                            </a>
                        </td>
                    </tr>
                }
            }
        }
        <tr>
            <td class="text-right" colspan="2">

                @*<a href="#" class="btn btn-link-ez btn-xs" onclick="postWithFetch()">more</a>*@

                <a href="#" class="btn btn-link-ez btn-xs" onclick="postToZZZI04()">more</a>
            </td>
        </tr>
    </tbody>
</table>

<strong class="ribbon ribbon-color2">
    <span class="ribbon-content">活動專區</span>
</strong>

<div class="row">
    <div class="col-md-12">

        @if (user != null)
        {
            if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin)
            {
                if (ViewBag.GAME_NO != null)
                {
                    <a href="@Url.Action("PassMode","Game",new {GAME_NO=(string) ViewBag.GAME_NO})" role="button" class="btn btn-primary btn-lg btn-block mt-1 btn-map">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.47 253.48" class="btn-svg">
                            <title>path</title>
                            <path d="M305.94,168.21a34.34,34.34,0,0,0-28.22-14.79h-78.6a20.66,20.66,0,0,0-39.85,0H74.13a23.19,23.19,0,0,1-15-5.52,20.66,20.66,0,0,0,.8-36.33,23.29,23.29,0,0,1,14.24-4.86h85.14a20.66,20.66,0,0,0,39.85,0h78.6a34.41,34.41,0,0,0,33.47-26.62,20.67,20.67,0,0,0-5.41-38.21,34.64,34.64,0,0,0-3.86-4.56,34,34,0,0,0-24.44-10c-28.32.21-54.46.37-78.18.49A20.67,20.67,0,0,0,159,28c-41.07.14-72,.11-91-.09a20.67,20.67,0,1,0-.13,11l5.25,0c15.2.13,36.71.17,63.94.12L159.52,39a20.69,20.69,0,0,0,39.41-.17c31.07-.16,60-.36,78.63-.49h.18a23.15,23.15,0,0,1,14.46,5,20.66,20.66,0,0,0,6.53,39.17,23.36,23.36,0,0,1-21,13.16h-78.6a20.67,20.67,0,0,0-39.85,0H74.13A34.31,34.31,0,0,0,47,109a20.66,20.66,0,0,0-.91,40.86,34.32,34.32,0,0,0,28,14.54h85.14a20.67,20.67,0,0,0,39.85,0h78.6a23.24,23.24,0,0,1,14.64,5.17,20.67,20.67,0,0,0,.83,37.82,23.23,23.23,0,0,1-15.47,5.89H198.79a20.67,20.67,0,0,0-39.19,0H68.14a20.67,20.67,0,1,0,.61,11H159a20.68,20.68,0,0,0,40.41,0h78.32a34.38,34.38,0,0,0,29.09-16.11,20.67,20.67,0,0,0-.87-40ZM179.19,41.92a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,41.92Zm0,50.58a8.71,8.71,0,1,1-8.71,8.71A8.71,8.71,0,0,1,179.19,92.5ZM49.63,138.23a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,49.63,138.23Zm129.56,29.4a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,167.63Zm0,61a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,228.59Z" />
                        </svg>
                        闖關地圖
                    </a>
                }
            }

            if (com.ecool.service.PermissionService.GetPermission_Use_YN("Game", "Index", user.SCHOOL_NO, user.USER_NO) == SharedGlobal.Y)
            {
                <a href="@Url.Action("GameIndex","Game",new {GAME_NO=(string) ViewBag.GAME_NO})" role="button" class="btn btn-warning btn-lg btn-block mt-1 btn-map">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.47 253.48" class="btn-svg">
                        <title>path</title>
                        <path d="M305.94,168.21a34.34,34.34,0,0,0-28.22-14.79h-78.6a20.66,20.66,0,0,0-39.85,0H74.13a23.19,23.19,0,0,1-15-5.52,20.66,20.66,0,0,0,.8-36.33,23.29,23.29,0,0,1,14.24-4.86h85.14a20.66,20.66,0,0,0,39.85,0h78.6a34.41,34.41,0,0,0,33.47-26.62,20.67,20.67,0,0,0-5.41-38.21,34.64,34.64,0,0,0-3.86-4.56,34,34,0,0,0-24.44-10c-28.32.21-54.46.37-78.18.49A20.67,20.67,0,0,0,159,28c-41.07.14-72,.11-91-.09a20.67,20.67,0,1,0-.13,11l5.25,0c15.2.13,36.71.17,63.94.12L159.52,39a20.69,20.69,0,0,0,39.41-.17c31.07-.16,60-.36,78.63-.49h.18a23.15,23.15,0,0,1,14.46,5,20.66,20.66,0,0,0,6.53,39.17,23.36,23.36,0,0,1-21,13.16h-78.6a20.67,20.67,0,0,0-39.85,0H74.13A34.31,34.31,0,0,0,47,109a20.66,20.66,0,0,0-.91,40.86,34.32,34.32,0,0,0,28,14.54h85.14a20.67,20.67,0,0,0,39.85,0h78.6a23.24,23.24,0,0,1,14.64,5.17,20.67,20.67,0,0,0,.83,37.82,23.23,23.23,0,0,1-15.47,5.89H198.79a20.67,20.67,0,0,0-39.19,0H68.14a20.67,20.67,0,1,0,.61,11H159a20.68,20.68,0,0,0,40.41,0h78.32a34.38,34.38,0,0,0,29.09-16.11,20.67,20.67,0,0,0-.87-40ZM179.19,41.92a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,41.92Zm0,50.58a8.71,8.71,0,1,1-8.71,8.71A8.71,8.71,0,0,1,179.19,92.5ZM49.63,138.23a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,49.63,138.23Zm129.56,29.4a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,167.63Zm0,61a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,228.59Z" />
                    </svg>
                    闖關活動後台管理
                </a>
            }

        }

        @if (ViewBag.GAME_NO != null)
        {
            <a href="@Url.Action("QueryUserDataList","Game",new {GAME_NO=(string) ViewBag.GAME_NO})" target="_blank" role="button" class="btn btn-primary btn-lg btn-block mt-1 btn-award">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.47 253.48" class="btn-svg">
                    <title>award</title>
                    <path d="M150.46,153.53c8.67,5.51,9.42,39.5,19.59,39.95,9.85.44,12.55-33.21,21.85-38.14s38.66,11.74,43.83,3.33c5.33-8.66-22.37-28.37-22.06-38.63.29-9.94,29.23-27.13,24.22-36.59S202.41,88.29,194,83c-8.67-5.51-9.42-39.49-19.59-39.95-9.85-.44-12.55,33.21-21.85,38.14s-38.66-11.73-43.83-3.33c-5.33,8.66,22.37,28.37,22.06,38.63-.29,10-29.23,27.14-24.22,36.6S142.07,148.2,150.46,153.53Zm14-49.88A16.51,16.51,0,1,1,157.65,126,16.51,16.51,0,0,1,164.5,103.65Z" />
                    <path d="M67.09,151.71l-5.64-16.2a29.59,29.59,0,0,0-12.81,35.17L56,191.77c.78.19,1.53.44,2.29.67a42.1,42.1,0,0,1-.7-10.12A41,41,0,0,1,68.32,156.8,30.42,30.42,0,0,0,67.09,151.71Z" />
                    <path d="M59.77,118.31l1-17.14a29.64,29.64,0,0,0-25.29,27.59l-1.28,22.32c.64.47,1.25,1,1.85,1.5a41.77,41.77,0,0,1,3.24-9.64A41.09,41.09,0,0,1,59,123.47,30.25,30.25,0,0,0,59.77,118.31Z" />
                    <path d="M43,60.89c.12.79.19,1.58.26,2.37a41.58,41.58,0,0,1,9.08-4.51A41,41,0,0,1,80,58.9a30.26,30.26,0,0,0,4.23-3.08L97.07,44.4A29.61,29.61,0,0,0,59.68,46Z" />
                    <path d="M20.28,144.35c.81.22,1.61.49,2.4.76l1-17a29.4,29.4,0,0,0-7.45-21.37c-.57-.64-1.21-1.18-1.82-1.76-1.12-.84-2.2-1.72-3.22-2.65a29,29,0,0,0-9.48-4.51l-1,17.13A29.56,29.56,0,0,0,20.28,144.35Z" />
                    <path d="M43.06,190.66l-5.6-16.08a29.56,29.56,0,0,0-17.08-17.77,41.68,41.68,0,0,1-4.73-1.43A29.1,29.1,0,0,0,5.57,155l5.65,16.2a29.53,29.53,0,0,0,28.84,19.72C41.05,190.79,42.06,190.72,43.06,190.66Z" />
                    <path d="M47.62,202.41a28.27,28.27,0,0,0-3.19,0c-.74.1-1.5.13-2.24.19a29.33,29.33,0,0,0-11.14,3.73l11.43,12.81a29.61,29.61,0,0,0,36.94,5.88L68,212.26A29.32,29.32,0,0,0,47.62,202.41Z" />
                    <path d="M56.25,69.92A29.39,29.39,0,0,0,39.35,85l-9.72,20.12c.42.69.78,1.41,1.16,2.13a41.45,41.45,0,0,1,32.3-18.17,28.93,28.93,0,0,0,2.73-4.46l7.46-15.44A29.56,29.56,0,0,0,56.25,69.92Z" />
                    <path d="M21.28,95.19l7.41-15.35a29.5,29.5,0,0,0,.47-24.66,41.51,41.51,0,0,1-2.28-4.26A29.29,29.29,0,0,0,20,43.44L12.53,58.88a29.56,29.56,0,0,0,6.78,34.66C20,94.07,20.63,94.63,21.28,95.19Z" />
                    <path d="M39.1,48.53,51.81,37.2a29.41,29.41,0,0,0,9.84-20.39A29.41,29.41,0,0,0,57.7.24L44.89,11.66a29.43,29.43,0,0,0-7.38,33.92C38.08,46.54,38.6,47.52,39.1,48.53Z" />
                    <path d="M88.27,217.18a29.61,29.61,0,0,0-1.63-37.39L75.22,167a29.59,29.59,0,0,0,1.62,37.39Z" />
                    <path d="M308.43,152.58c.61-.51,1.21-1,1.85-1.51L309,128.76a29.64,29.64,0,0,0-25.28-27.59l1,17.13a29.92,29.92,0,0,0,.81,5.17,41,41,0,0,1,22.92,29.11Z" />
                    <path d="M314.84,105.11,305.12,85s0,0,0,0a29.58,29.58,0,0,0-33.9-15.77l7.46,15.45a29.73,29.73,0,0,0,2.73,4.46,41.38,41.38,0,0,1,32.31,18.14C314.07,106.52,314.43,105.8,314.84,105.11Z" />
                    <path d="M330.08,104.94c-.61.58-1.25,1.13-1.81,1.77a29.36,29.36,0,0,0-7.46,21.37l1,17c.78-.26,1.56-.53,2.36-.74a29.56,29.56,0,0,0,19.62-29.45l-1-17.13a29,29,0,0,0-9.48,4.5A39.07,39.07,0,0,1,330.08,104.94Z" />
                    <path d="M288.48,191.77l7.35-21.08A29.59,29.59,0,0,0,283,135.52l-5.64,16.2a30,30,0,0,0-1.23,5.09,41.12,41.12,0,0,1,10.74,25.52,42.1,42.1,0,0,1-.7,10.12C287,192.21,287.71,192,288.48,191.77Z" />
                    <path d="M315.78,79.84l6.42,13.31,1,2c.64-.56,1.29-1.14,2-1.66a29.55,29.55,0,0,0,6.77-34.65l-7.46-15.44a29.45,29.45,0,0,0-6.89,7.48,41.89,41.89,0,0,1-2.29,4.28,29.5,29.5,0,0,0,.46,24.61A0,0,0,0,1,315.78,79.84Z" />
                    <path d="M264.44,58.9a41.16,41.16,0,0,1,36.79,4.38c.07-.79.13-1.59.25-2.38L284.79,46A29.61,29.61,0,0,0,247.4,44.4l12.81,11.42A29.68,29.68,0,0,0,264.44,58.9Z" />
                    <path d="M257.83,179.8a29.6,29.6,0,0,0-1.63,37.38l11.43-12.8A29.59,29.59,0,0,0,269.25,167Z" />
                    <path d="M300.15,202.45a27.93,27.93,0,0,0-3.3,0,29.37,29.37,0,0,0-20.39,9.84l-11.41,12.8a29.65,29.65,0,0,0,37-5.88l11.42-12.81a29.4,29.4,0,0,0-11.26-3.74C301.49,202.57,300.82,202.54,300.15,202.45Z" />
                    <path d="M328.29,155.54a38.73,38.73,0,0,1-4.05,1.23A29.57,29.57,0,0,0,307,174.58l-5.61,16.08c1,.06,2,.13,3,.27a29.52,29.52,0,0,0,28.83-19.73L338.9,155A29.27,29.27,0,0,0,328.29,155.54Z" />
                    <path d="M305.39,48.54c.46-.91.92-1.82,1.44-2.7a29.45,29.45,0,0,0-7.25-34.19L286.77.23a29.26,29.26,0,0,0-3.95,16.58,29.31,29.31,0,0,0,9.84,20.38Z" />
                    <path d="M248,223.54a174.1,174.1,0,0,1-151.57,0,47.54,47.54,0,0,1-9.26,8.6c13.11,7,43.8,20.59,85.05,20.59s71.93-13.58,85.05-20.59A47.31,47.31,0,0,1,248,223.54Z" />
                </svg>
                中獎查詢
            </a>
        }
    </div>
</div>

@section Scripts {
    <script type="text/javascript">
        window.onload = function () {

            if ($("#txtSCHOOL_NO").val()=='') {
                  $("#txtSCHOOL_NO").val('@ViewBag.SCHOOL_NO');
            }
        }

         function postToZZZI04() {
        const form = document.createElement("form");
        form.method = "POST";
        form.action = '@Url.Action("Index", "ZZZI04")';

        // 傳遞所有參數（含 PrevAction, Layout）
        form.appendChild(createHidden("PrevAction", ""));
        form.appendChild(createHidden("Layout", "_LayoutChildMonth"));

        // 防偽驗證 token
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        if (token) {
            form.appendChild(createHidden("__RequestVerificationToken", token.value));
        }

        document.body.appendChild(form);
        form.submit();
    }

    function createHidden(name, value) {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = name;
        input.value = value;
        return input;
        }

           function postToDetails(bulletId, searchContents, page, prevAction, layout) {
      

        const form = document.createElement("form");
        form.method = "POST";
        form.action = '@Url.Action("Details", "ZZZI04")';

        const inputs = {
            BULLET_ID: bulletId,
            SearchContents: searchContents,
            page: page,
            PrevAction: prevAction,
            Layout: layout
        };

        for (const key in inputs) {
            const input = document.createElement("input");
            input.type = "hidden";
            input.name = key;
            input.value = inputs[key] ?? "";
            form.appendChild(input);
        }

        document.body.appendChild(form);
        form.submit();
    }
    </script>
}