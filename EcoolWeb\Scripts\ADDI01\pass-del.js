// Modern jQuery code for ADDI01 PASS_DEL page
$(document).ready(function() {
    // CKEditor 配置模組
    const ckeditorHandler = {
        init: function() {
            this.setupCKEditor();
            window.KeyIn = this.updateTextCount.bind(this);
        },

        setupCKEditor: function() {
            $(window).on('load', () => {
                CKEDITOR.config.nonce = 'cmlvaw';
                const editor = CKEDITOR.instances.ARTICLE_VERIFY;
                
                if (editor) {
                    editor.on('change', () => {
                        this.updateTextCount();
                    });
                }
                
                this.updateTextCount();
            });
        },

        updateTextCount: function() {
            const editor = CKEDITOR.instances.ARTICLE_VERIFY;
            if (editor) {
                const content = editor.getData();
                const textWithoutWhitespace = content.replace(/[\n\s]/g, "").replace(/<br\/>/g, "");
                const length = textWithoutWhitespace.length;
                $("#ShowFontLen").text(length);
            }
        }
    };

    // Colorbox 圖片畫廊模組
    const imageGallery = {
        init: function() {
            this.initColorbox();
        },

        initColorbox: function() {
            $("#DivImg img").colorbox({ 
                opacity: 0.82, 
                maxHeight: '95%', 
                maxWidth: '95%' 
            });
        }
    };

    // 下拉選單處理模組
    const dropdownHandler = {
        init: function() {
            window.VCommentDropDownList = this.handleVerifyComment.bind(this);
            window.BackDropDownList = this.handleBackMemo.bind(this);
            this.initializeDropdowns();
        },

        initializeDropdowns: function() {
            $(window).on('load', () => {
                // 初始化作廢原因下拉選單
                const selectedBackValue = $('#BACK_MEMO_DropDownList option:selected').val();
                this.handleBackMemo(selectedBackValue);

                // 初始化評語下拉選單
                const selectedCommentValue = $('#VERIFY_COMMENT_DropDownList option:selected').val();
                if (selectedCommentValue === window.ADDI01_CONFIG.otherValue) {
                    $('#VERIFY_COMMENT').attr("type", "text")
                                       .attr("placeholder", "請輸入評語")
                                       .addClass("form-control");
                }
            });
        },

        handleVerifyComment: function(value) {
            if (value === window.ADDI01_CONFIG.otherValue) {
                $('#VERIFY_COMMENT').val('')
                                   .attr("type", "text")
                                   .attr("placeholder", "請輸入評語")
                                   .addClass("form-control");
            } else {
                $('#VERIFY_COMMENT').attr("type", "hidden")
                                   .removeClass()
                                   .val(value);
            }
        },

        handleBackMemo: function(value) {
            if (value === window.ADDI01_CONFIG.otherValue) {
                $('#BACK_MEMO').val("")
                              .attr("type", "text")
                              .attr("placeholder", "請輸入原因")
                              .addClass("form-control");
            } else {
                $('#BACK_MEMO').attr("type", "hidden")
                              .removeClass()
                              .val(value);
            }
        }
    };

    // 圖片刪除模組
    const imageDeleter = {
        init: function() {
            window.DeleteMyImage = this.deleteImage.bind(this);
            window.DeleteMyOtherFile = this.deleteOtherFile.bind(this);
        },

        deleteImage: function(element, imageName) {
            const oldText = $("#DeleteImage").val();
            $("#DeleteImage").val(oldText + imageName + "|");
            element.closest('div').remove();
            console.log("Deleted images:", $("#DeleteImage").val());
        },

        deleteOtherFile: function(element, fileName) {
            const oldText = $("#DeleteOtherFile").val();
            $("#DeleteOtherFile").val(oldText + fileName + "|");
            element.closest('div').remove();
            console.log("Deleted files:", $("#DeleteOtherFile").val());
        }
    };

    // 表單提交和驗證模組
    const formHandler = {
        init: function() {
            window.UpdateVerify = this.updateVerify.bind(this);
            window.DisableGO = this.disableArticle.bind(this);
            window.DoGO = this.navigateToAction.bind(this);
        },

        updateVerify: function(buttonElement, actionUrl) {
            let errorMsg = '';
            buttonElement.disabled = true;

            if ($('#HIS_MEMO').val() === '') {
                errorMsg += '請輸入異動原因';
            }

            if (errorMsg !== '') {
                buttonElement.disabled = false;
                alert(errorMsg);
                return false;
            } else {
                const confirmed = confirm("你確定要「異動」這篇文章?");
                if (confirmed) {
                    const form = document.form1;
                    form.action = actionUrl;
                    form.submit();
                } else {
                    buttonElement.disabled = false;
                    return false;
                }
            }
        },

        disableArticle: function(buttonElement, actionUrl) {
            let errorMsg = '';
            buttonElement.disabled = true;

            $('#HIS_MEMO').val('');

            if ($('#BACK_MEMO').val() === '') {
                errorMsg += '請輸入作廢原因';
            }

            if (errorMsg !== '') {
                buttonElement.disabled = false;
                alert(errorMsg);
                return false;
            } else {
                const confirmed = window.confirm("你確定要「作廢」這篇文章?");
                if (confirmed) {
                    const form = document.form1;
                    form.action = actionUrl;
                    form.submit();
                } else {
                    buttonElement.disabled = false;
                    return false;
                }
            }
        },

        navigateToAction: function(actionUrl) {
            const form = document.form1;
            form.BACK_MEMO.value = '';
            form.action = actionUrl;
            form.submit();
        }
    };

    // 國語日報投稿模組
    const mdnKidsHandler = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $("#PUBLISH_MDNKIDS_YN").on('click', this.handleMdnKidsToggle.bind(this));
            $("#SEND_EMAIL_YN").on('click', this.handleEmailToggle.bind(this));
        },

        handleMdnKidsToggle: function() {
            const $checkbox = $("#PUBLISH_MDNKIDS_YN");
            
            $("#mdnInfoDiv").toggle();
            $("#MdnKids_Address").val("");
            $("#MdnKids_ResidenceAddress").val("");
            $("#MdnKids_Phone").val("");
            $("#MdnKids_Email").val("");
            
            if ($checkbox.prop("checked") === true) {
                $("#MdnKids_IntroPerson").val(window.ADDI01_CONFIG.userName);
                
                $("#notice").dialog({
                    buttons: {
                        "Ok": function() {
                            $(this).dialog("close");
                        }
                    }
                });
                
                $(".ui-dialog-titlebar-close").attr("hidden", "hidden");
            }
        },

        handleEmailToggle: function() {
            const $checkbox = $("#SEND_EMAIL_YN");
            
            if ($checkbox.prop("checked") === true) {
                $("#sendMailShow").attr("style", "");
            } else {
                $("#sendMailShow").attr("style", "display:none;");
            }
        }
    };

    // 初始化所有模組
    ckeditorHandler.init();
    imageGallery.init();
    dropdownHandler.init();
    imageDeleter.init();
    formHandler.init();
    mdnKidsHandler.init();

    // 設置全局配置（需要在 CSHTML 中定義）
    window.ADDI01_CONFIG = window.ADDI01_CONFIG || {};
});
