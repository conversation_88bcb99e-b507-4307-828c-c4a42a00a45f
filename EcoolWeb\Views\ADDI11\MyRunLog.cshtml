﻿@model ADDI11MyRunLogViewModel

@{  
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode || (Model.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}

@Html.Partial("_Notice")

@{

    Html.RenderAction("_RunMenu", new { NowAction = "MyRunLog" });

}

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageMyRunLog", (string)ViewBag.BRE_NO)
    </div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageMyRunLog", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }
    </script>
}