﻿@model GamePassedViewViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}
<link href="~/Content/css/childrens-month.min.css" rel="stylesheet" />
<script src="~/Scripts/jquery.simple.timer.js?ver=2"></script>
<span class="eachtimer" data-seconds-left=@(Model.PASSED_TIME ?? 6) style="display:none"></span>

@using (Html.BeginForm(Model.ActionName, "Game", FormMethod.Post, new { name = "form1", id = "form1" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)
    @Html.HiddenFor(m => m.LEVEL_NO)
}

<div style="height:calc(10vh)"></div>
<div class="row">
    <div class="col-md-6 col-md-offset-3">
        <div class="login-panel panel panel-danger">
            <div class="panel-heading">
                <h1 class="panel-title text-center">訊息</h1>
            </div>
            <div class="panel-body">
                <h1 style="color:red;text-align:center">
                    <strong>

                        @Html.Raw(HttpUtility.HtmlDecode(Model?.Message))<br /><br />
                        @if (Model.User != null)
                        {
                            var Name = Model?.User.NAME + ", 目前" + Model.CASH_AVAILABLE + " 點";
                            <font style="color:blue;"> @Name</font>
                            @*@Model?.User.NAME
                                , 目前 @Model.CASH_AVAILABLE*@

                        }
                    </strong>
                </h1>
                @if (Model.IsOK)
                {
                    <br />
                    if (Model.LEVEL_TYPE != ADDT26_D.LevelType.Test)
                    {
                        <div style="margin: 0px auto;text-align:center">

                            <div class="form-group">
                                <samp class="dt" style="font-size:26px">
                                    姓名:
                                </samp>
                                <samp class="dd" style="font-size:26px">
                                    <strong>@Model.NAME</strong>
                                </samp>
                            </div>
                            <div class="form-group">
                                <samp class="dt" style="font-size:26px">
                                    本次交易點數:
                                </samp>
                                <samp class="dd" style="font-size:26px">
                                    <strong>@Model.CASH_IN</strong>
                                </samp>
                            </div>
                            <div class="form-group">
                                <samp class="dt" style="font-size:26px">
                                    目前點數:
                                </samp>
                                <samp class="dd" style="font-size:26px">
                                    <strong>@Model.CASH_AVAILABLE</strong>
                                </samp>
                            </div>
                        </div>
                    }

                    if (Model.LEVEL_TYPE == ADDT26_D.LevelType.Apply)
                    {
                        <div style="margin: 0px auto;text-align:center">
                            <img src="~/Content/img/Winning.gif" style="max-height:calc(100vh - 350px);margin: 0px auto;text-align:center" class="img-responsive" />
                        </div>
                    }
                    else if (Model.LEVEL_TYPE == ADDT26_D.LevelType.Pay)
                    {
                        if (Model.CASH_IN < 0)
                        {
                            <div style="margin: 0px auto;text-align:center">
                                <img src="~/Content/img/pay_GIF.gif" style="max-height:calc(100vh - 350px);margin: 0px auto;text-align:center" class="img-responsive" />
                            </div>
                        }
                        else
                        {
                            <div style="margin: 0px auto;text-align:center">
                                <img src="~/Content/img/Winning.gif" style="max-height:calc(100vh - 350px);margin: 0px auto;text-align:center" class="img-responsive" />
                            </div>
                        }

                    }
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1'

        $('.eachtimer').startTimer({
            onComplete: function (element) {
                funGetExchange()
            },
        });

        function funGetExchange() {
            $(targetFormID).submit();
        }
    </script>
}