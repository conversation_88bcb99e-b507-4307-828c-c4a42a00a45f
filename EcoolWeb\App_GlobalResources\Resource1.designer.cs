//------------------------------------------------------------------------------
// <auto-generated>
//     這段程式碼是由工具產生的。
//     執行階段版本:4.0.30319.42000
//
//     對這個檔案所做的變更可能會造成錯誤的行為，而且如果重新產生程式碼，
//     變更將會遺失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   用於查詢當地語系化字串等的強型別資源類別。
    /// </summary>
    // 這個類別是自動產生的，是利用 StronglyTypedResourceBuilder
    // 類別透過 ResGen 或 Visual Studio 這類工具產生。
    // 若要加入或移除成員，請編輯您的 .ResX 檔，然後重新執行 ResGen
    // (利用 /str 選項)，或重建 Visual Studio 專案。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   傳回這個類別使用的快取的 ResourceManager 執行個體。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Resource", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   覆寫目前執行緒的 CurrentUICulture 屬性，對象是所有
        ///   使用這個強型別資源類別的資源查閱。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查詢類似 返回 的當地語系化字串。
        /// </summary>
        internal static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 回查詢列表 的當地語系化字串。
        /// </summary>
        internal static string Back_to_List {
            get {
                return ResourceManager.GetString("Back_to_List", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 退回作廢 的當地語系化字串。
        /// </summary>
        internal static string BookRollBack {
            get {
                return ResourceManager.GetString("BookRollBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 通過審核 的當地語系化字串。
        /// </summary>
        internal static string BookSuccess {
            get {
                return ResourceManager.GetString("BookSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 取消 的當地語系化字串。
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 刪除 的當地語系化字串。
        /// </summary>
        internal static string DELETE {
            get {
                return ResourceManager.GetString("DELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 編輯 的當地語系化字串。
        /// </summary>
        internal static string EDIT {
            get {
                return ResourceManager.GetString("EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 檔案上傳 的當地語系化字串。
        /// </summary>
        internal static string File_input {
            get {
                return ResourceManager.GetString("File_input", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 新增 的當地語系化字串。
        /// </summary>
        internal static string INSERT {
            get {
                return ResourceManager.GetString("INSERT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 英文 的當地語系化字串。
        /// </summary>
        internal static string Languages_enUS {
            get {
                return ResourceManager.GetString("Languages_enUS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 繁體中文 的當地語系化字串。
        /// </summary>
        internal static string Languages_zhTW {
            get {
                return ResourceManager.GetString("Languages_zhTW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 修改 的當地語系化字串。
        /// </summary>
        internal static string MODIFY {
            get {
                return ResourceManager.GetString("MODIFY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 你確定要刪除嗎？ 的當地語系化字串。
        /// </summary>
        internal static string MSGDELETE {
            get {
                return ResourceManager.GetString("MSGDELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 否 的當地語系化字串。
        /// </summary>
        internal static string NO {
            get {
                return ResourceManager.GetString("NO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 列印 的當地語系化字串。
        /// </summary>
        internal static string PRINT {
            get {
                return ResourceManager.GetString("PRINT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 查詢 的當地語系化字串。
        /// </summary>
        internal static string QUERY {
            get {
                return ResourceManager.GetString("QUERY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 角色 的當地語系化字串。
        /// </summary>
        internal static string Role {
            get {
                return ResourceManager.GetString("Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 確定送出 的當地語系化字串。
        /// </summary>
        internal static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查詢類似 是 的當地語系化字串。
        /// </summary>
        internal static string YES {
            get {
                return ResourceManager.GetString("YES", resourceCulture);
            }
        }
    }
}
