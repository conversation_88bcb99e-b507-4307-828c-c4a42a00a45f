/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/NumberForms.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{8531:[676,14,750,36,725],8532:[676,14,750,14,731],8533:[676,14,750,37,715],8534:[676,14,750,14,720],8535:[676,14,750,13,720],8536:[676,14,750,14,720],8537:[676,14,750,37,717],8538:[676,15,750,29,722],8539:[676,14,750,37,722],8540:[676,14,750,13,727],8541:[676,14,750,29,727],8542:[676,14,750,28,727]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/NumberForms.js");
