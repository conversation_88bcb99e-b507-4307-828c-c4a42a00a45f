﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uHRMT01
    {


        ///Summary
        ///SCHOOL_NO
        ///Summary
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        public string USER_NO { get; set; }

        ///Summary
        ///USER_KEY
        ///Summary
        public string USER_KEY { get; set; }

        ///Summary
        ///NAME
        ///Summary
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        public string SNAME { get; set; }

        ///Summary
        ///SEX
        ///Summary
        public string SEX { get; set; }

        ///Summary
        ///GRADE
        ///Summary
        public Nullable<byte> GRADE { get; set; }

        ///Summary
        ///CLASS_NO
        ///Summary
        public string CLASS_NO { get; set; }

        ///Summary
        ///SEAT_NO
        ///Summary
        public string SEAT_NO { get; set; }

        ///Summary
        ///BIRTHDAY
        ///Summary
        public Nullable<DateTime> BIRTHDAY { get; set; }

        ///Summary
        ///USER_STATUS
        ///Summary
        public Nullable<byte> USER_STATUS { get; set; }

        ///Summary
        ///USER_TYPE
        ///Summary
        public string USER_TYPE { get; set; }

        ///Summary
        ///TEACHER_TYPE
        ///Summary
        public string TEACHER_TYPE { get; set; }

        ///Summary
        ///E_MAIL
        ///Summary
        public string E_MAIL { get; set; }

        ///Summary
        ///TEL_NO
        ///Summary
        public string TEL_NO { get; set; }

        ///Summary
        ///PHONE
        ///Summary
        public string PHONE { get; set; }

    }
}
