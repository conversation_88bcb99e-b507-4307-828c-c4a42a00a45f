/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Operators/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Operators={directory:"Operators/Regular",family:"GyreTermesMathJax_Operators",testString:"\u00A0\u2206\u220A\u220C\u220E\u220F\u2210\u2211\u221F\u222C\u222D\u222E\u222F\u2230\u2231",32:[0,0,250,0,0],160:[0,0,250,0,0],8710:[674,0,760,80,680],8714:[425,-75,528,80,448],8716:[650,150,678,80,598],8718:[556,0,716,80,636],8719:[727,227,1023,80,943],8720:[727,227,1023,80,943],8721:[727,227,944,80,864],8735:[526,0,686,80,606],8748:[796,296,1042,80,962],8749:[796,296,1418,80,1338],8750:[796,296,712,80,632],8751:[796,296,1088,80,1008],8752:[796,296,1464,80,1384],8753:[796,296,729,80,689],8754:[796,296,721,80,681],8755:[796,296,692,80,652],8758:[423,-77,272,80,192],8759:[423,-77,506,80,426],8760:[488,-224,660,80,580],8761:[423,-77,760,80,680],8762:[488,-12,660,80,580],8763:[471,-29,642,80,562],8766:[404,-96,784,80,704],8767:[431,-69,660,80,580],8772:[500,0,660,80,580],8775:[500,0,660,80,580],8777:[500,0,642,80,562],8779:[471,-29,642,80,562],8780:[471,-72,660,80,580],8788:[423,-77,760,80,680],8789:[423,-77,760,80,680],8792:[503,-32,660,80,580],8793:[546,46,660,80,580],8794:[546,46,660,80,580],8795:[791,-128,660,80,580],8797:[824,-128,852,80,772],8798:[712,-128,660,80,580],8799:[818,-128,660,80,580],8802:[650,150,660,80,580],8803:[564,64,660,80,580],8813:[650,150,660,80,580],8820:[632,132,665,80,585],8821:[632,132,665,80,585],8824:[681,181,670,80,590],8825:[681,181,670,80,590],8836:[650,150,678,80,598],8837:[650,150,678,80,598],8844:[500,18,660,80,580],8845:[500,18,660,80,580],8860:[568,68,796,80,716],8870:[650,150,586,80,506],8871:[650,150,586,80,506],8875:[650,150,738,80,658],8886:[400,-100,960,80,880],8887:[400,-100,960,80,880],8889:[500,0,660,80,580],8893:[529,29,660,80,580],8894:[526,85,771,80,691],8895:[544,0,704,80,624],8896:[698,190,960,80,880],8897:[690,198,960,80,880],8898:[708,190,960,80,880],8899:[690,208,960,80,880],8903:[521,21,671,80,591],8917:[650,150,660,80,580],8924:[564,54,665,80,585],8925:[564,54,665,80,585],8930:[685,185,660,80,580],8931:[685,185,660,80,580],8932:[576,143,660,80,580],8933:[576,143,660,80,580],8944:[517,17,694,80,614],10752:[688,188,1036,80,956],10753:[688,188,1036,80,956],10754:[688,188,1036,80,956],10755:[690,208,960,80,880],10756:[690,208,960,80,880],10757:[704,176,896,80,816],10758:[676,204,896,80,816],10761:[608,108,876,80,796],10764:[796,296,1794,80,1714],10769:[796,296,729,80,689],10799:[455,-45,570,80,490]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Operators"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Operators/Regular/Main.js"]);
