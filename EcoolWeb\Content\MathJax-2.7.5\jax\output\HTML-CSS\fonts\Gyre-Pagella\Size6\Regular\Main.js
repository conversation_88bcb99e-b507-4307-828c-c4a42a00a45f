/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size6/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Size6={directory:"Size6/Regular",family:"GyrePagellaMathJax_Size6",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[1458,958,679,143,583],41:[1458,958,679,96,536],47:[2272,1772,1620,80,1540],91:[1464,964,521,143,425],92:[2272,1772,1620,80,1540],93:[1464,964,521,96,378],123:[1463,963,589,96,493],124:[1444,944,224,80,144],125:[1463,963,589,96,493],160:[0,0,250,0,0],770:[714,-540,1498,0,1498],771:[710,-534,1496,0,1496],774:[709,-549,1528,0,1528],780:[714,-540,1498,0,1498],785:[724,-563,1528,0,1528],812:[-60,234,1498,0,1498],813:[-70,244,1498,0,1498],814:[-60,221,1528,0,1528],815:[-78,239,1528,0,1528],816:[-78,255,1496,0,1496],8214:[1444,944,408,80,328],8260:[2272,1772,1620,80,1540],8425:[788,-656,2593,0,2593],8730:[1740,1210,770,120,800],8739:[1444,944,224,80,144],8741:[1444,944,408,80,328],8968:[1464,944,521,143,425],8969:[1464,944,521,96,378],8970:[1444,964,521,143,425],8971:[1444,964,521,96,378],9001:[2275,1775,620,96,524],9002:[2275,1775,620,96,524],9140:[788,-656,2593,0,2593],9141:[-186,318,2593,0,2593],9180:[801,-569,3528,0,3528],9181:[-99,331,3528,0,3528],9182:[815,-586,3538,0,3538],9183:[-116,345,3538,0,3538],9184:[744,-522,3580,0,3580],9185:[-52,274,3580,0,3580],10214:[1464,964,521,143,425],10215:[1464,964,521,96,378],10216:[2275,1775,620,96,524],10217:[2275,1775,620,96,524],10218:[2275,1775,941,96,845],10219:[2275,1775,941,96,845],10222:[1458,958,439,143,343],10223:[1458,958,439,96,296],57344:[390,-110,200,0,200],57345:[390,-110,200,0,200],57346:[390,-110,200,0,200],57347:[596,0,589,252,332],57348:[796,0,224,80,144],57349:[796,0,224,80,144],57350:[796,0,224,80,144],57351:[596,0,589,257,337],57352:[784,-640,191,0,191],57353:[688,-640,127,0,127],57354:[688,-640,192,0,192],57355:[688,-640,191,0,191],57356:[688,-640,127,0,127],57357:[784,-640,192,0,192],57358:[784,-544,191,0,191],57359:[688,-640,128,0,128],57360:[688,-640,191,0,191],57361:[688,-640,191,0,191],57362:[688,-640,128,0,128],57363:[784,-544,191,0,191],57364:[784,-544,218,0,218],57365:[688,-640,146,0,146],57366:[784,-544,218,0,218],57367:[-170,218,191,0,191],57368:[-170,218,127,0,127],57369:[-170,314,192,0,192],57370:[-170,314,191,0,191],57371:[-170,218,127,0,127],57372:[-170,218,192,0,192],57373:[-74,314,191,0,191],57374:[-170,218,128,0,128],57375:[-170,218,191,0,191],57376:[-170,218,191,0,191],57377:[-170,218,128,0,128],57378:[-74,314,191,0,191],57379:[400,-100,454,0,454],57380:[280,-220,302,0,302],57381:[280,-220,454,0,454],57382:[280,-220,454,0,454],57383:[280,-220,302,0,302],57384:[400,-100,454,0,454],57385:[454,0,460,200,260],57386:[302,0,460,200,260],57387:[454,0,460,80,380],57388:[454,0,460,80,380],57389:[302,0,460,200,260],57390:[454,0,460,200,260],57391:[400,-100,346,0,346],57392:[280,-220,86,0,86],57393:[400,-100,346,0,346],57394:[280,-220,346,0,346],57395:[280,-220,346,0,346],57396:[280,-220,86,0,86],57397:[400,-100,346,0,346],57398:[400,-100,346,0,346],57399:[400,-100,486,0,486],57400:[280,-220,323,0,323],57401:[400,-100,486,0,486],57402:[485,0,460,80,380],57403:[324,0,460,200,260],57404:[486,0,460,80,380],57405:[400,-100,370,0,370],57406:[280,-220,93,0,93],57407:[400,-100,369,0,369],57408:[400,-100,370,0,370],57409:[400,-100,482,0,482],57410:[280,-220,321,0,321],57411:[280,-220,482,0,482],57412:[280,-220,482,0,482],57413:[280,-220,321,0,321],57414:[400,-100,482,0,482],57415:[481,0,460,200,260],57416:[322,0,460,200,260],57417:[482,0,460,80,380],57418:[482,0,460,80,380],57419:[322,0,460,200,260],57420:[481,0,460,200,260],57421:[400,-100,486,0,486],57422:[280,-220,323,0,323],57423:[400,-100,486,0,486],57424:[400,-100,486,0,486],57425:[280,-220,323,0,323],57426:[400,-100,486,0,486],57427:[400,-100,454,0,454],57428:[280,-220,302,0,302],57429:[400,-100,454,0,454],57430:[400,-100,454,0,454],57431:[280,-220,302,0,302],57432:[400,-100,454,0,454],57433:[454,0,460,80,380],57434:[302,0,460,200,260],57435:[454,0,460,80,380],57436:[454,0,460,80,380],57437:[302,0,460,200,260],57438:[454,0,460,80,380],57439:[490,-220,465,0,465],57440:[280,-220,310,0,310],57441:[400,-100,465,0,465],57442:[400,-100,465,0,465],57443:[280,-220,310,0,310],57444:[490,-220,465,0,465],57445:[490,-40,465,0,465],57446:[280,-220,310,0,310],57447:[400,-100,465,0,465],57448:[400,-100,465,0,465],57449:[280,-220,310,0,310],57450:[490,-40,465,0,465],57451:[400,-220,454,0,454],57452:[280,-220,302,0,302],57453:[280,-220,454,0,454],57454:[280,-220,454,0,454],57455:[280,-220,302,0,302],57456:[400,-220,454,0,454],57457:[280,-100,454,0,454],57458:[280,-220,302,0,302],57459:[280,-220,454,0,454],57460:[280,-220,454,0,454],57461:[280,-220,302,0,302],57462:[280,-100,454,0,454],57463:[454,0,340,80,140],57464:[302,0,340,80,140],57465:[454,0,340,80,260],57466:[454,0,340,80,260],57467:[302,0,340,80,140],57468:[454,0,340,80,140],57469:[454,0,340,200,260],57470:[302,0,340,200,260],57471:[454,0,340,80,260],57472:[454,0,340,80,260],57473:[302,0,340,200,260],57474:[454,0,340,200,260],57475:[450,70,458,0,458],57476:[450,-50,305,0,305],57477:[570,-50,457,0,457],57478:[570,-50,457,0,457],57479:[450,-50,305,0,305],57480:[450,70,458,0,458],57481:[458,0,800,200,720],57482:[305,0,800,200,600],57483:[457,0,800,80,600],57484:[457,0,800,80,600],57485:[305,0,800,200,600],57486:[458,0,800,200,720],57487:[570,70,454,0,454],57488:[450,-50,302,0,302],57489:[450,-50,454,0,454],57490:[450,-50,454,0,454],57491:[450,-50,302,0,302],57492:[570,70,454,0,454],57493:[454,0,800,200,600],57494:[302,0,800,200,600],57495:[454,0,800,80,720],57496:[454,0,800,80,720],57497:[302,0,800,200,600],57498:[454,0,800,200,600],57499:[620,120,454,0,454],57500:[620,120,302,0,302],57501:[740,240,454,0,454],57502:[740,240,454,0,454],57503:[620,120,302,0,302],57504:[620,120,454,0,454],57505:[510,-110,457,0,457],57506:[390,-110,305,0,305],57507:[390,10,457,0,457],57508:[390,10,457,0,457],57509:[390,-110,305,0,305],57510:[510,-110,457,0,457],57511:[450,-50,454,0,454],57512:[370,-130,302,0,302],57513:[370,-130,454,0,454],57514:[370,-130,454,0,454],57515:[370,-130,302,0,302],57516:[450,-50,454,0,454],57517:[454,0,560,160,400],57518:[302,0,560,160,400],57519:[454,0,560,80,480],57520:[454,0,560,80,480],57521:[302,0,560,160,400],57522:[454,0,560,160,400],57523:[450,-50,486,0,486],57524:[370,-130,323,0,323],57525:[450,-50,486,0,486],57526:[485,0,560,80,480],57527:[324,0,560,160,400],57528:[486,0,560,80,480],57529:[450,-50,346,0,346],57530:[370,-130,86,0,86],57531:[550,50,346,0,346],57532:[370,-130,346,0,346],57533:[370,-130,346,0,346],57534:[370,-130,86,0,86],57535:[550,50,346,0,346],57536:[450,-50,346,0,346],57537:[450,-50,370,0,370],57538:[370,-130,93,0,93],57539:[550,50,369,0,369],57540:[450,-50,370,0,370],57541:[450,-50,482,0,482],57542:[370,-130,321,0,321],57543:[450,-50,482,0,482],57544:[450,-50,482,0,482],57545:[370,-130,321,0,321],57546:[450,-50,482,0,482],57547:[525,25,454,0,454],57548:[460,-40,302,0,302],57549:[460,-40,454,0,454],57550:[460,-40,454,0,454],57551:[460,-40,302,0,302],57552:[525,25,454,0,454],57553:[450,-50,501,0,501],57554:[360,-140,335,0,335],57555:[360,-140,501,0,501],57556:[360,-140,501,0,501],57557:[360,-140,335,0,335],57558:[450,-50,501,0,501],57559:[501,0,560,170,390],57560:[334,0,560,170,390],57561:[502,0,560,80,480],57562:[502,0,560,80,480],57563:[334,0,560,170,390],57564:[501,0,560,170,390],57565:[510,0,560,80,480],57566:[340,0,560,170,390],57567:[510,0,560,80,480],57568:[450,-50,510,0,510],57569:[360,-140,339,0,339],57570:[450,-50,510,0,510],57571:[450,-50,482,0,482],57572:[340,-160,321,0,321],57573:[340,-160,482,0,482],57574:[340,-160,482,0,482],57575:[340,-160,321,0,321],57576:[450,-50,482,0,482],57577:[481,0,560,190,370],57578:[322,0,560,190,370],57579:[482,0,560,80,480],57580:[482,0,560,80,480],57581:[322,0,560,190,370],57582:[481,0,560,190,370],57583:[450,-50,486,0,486],57584:[340,-160,323,0,323],57585:[450,-50,486,0,486],57586:[485,0,560,80,480],57587:[324,0,560,190,370],57588:[486,0,560,80,480],57589:[-60,108,167,0,167],57590:[-60,108,166,0,166],57591:[-60,108,167,0,167],57592:[-60,216,167,0,167],57593:[-60,216,166,0,166],57594:[-60,216,167,0,167],57595:[646,-598,167,0,167],57596:[646,-598,166,0,166],57597:[646,-598,167,0,167],57598:[754,-598,167,0,167],57599:[754,-598,166,0,166],57600:[754,-598,167,0,167],57601:[1208,0,439,143,343],57602:[794,0,439,143,223],57603:[1208,0,439,143,343],57604:[1208,0,439,96,296],57605:[794,0,439,216,296],57606:[1208,0,439,96,296],57607:[809,0,521,143,425],57608:[810,0,521,143,324],57609:[809,0,521,143,425],57610:[809,0,521,96,378],57611:[810,0,521,197,378],57612:[809,0,521,96,378],57613:[740,-586,884,0,884],57614:[740,-668,874,0,874],57615:[815,-668,1769,0,1769],57616:[740,-586,885,0,885],57617:[-116,270,884,0,884],57618:[-198,270,874,0,874],57619:[-198,345,1769,0,1769],57620:[-116,270,885,0,885],57621:[801,-569,1764,0,1764],57622:[801,-729,874,0,874],57623:[801,-569,1764,0,1764],57624:[-99,331,1764,0,1764],57625:[-259,331,874,0,874],57626:[-99,331,1764,0,1764],57627:[788,-657,1297,0,1297],57628:[787,-715,865,0,865],57629:[788,-656,1296,0,1296],57630:[-186,317,1297,0,1297],57631:[-245,318,865,0,865],57632:[-186,318,1296,0,1296],57633:[744,-522,1790,0,1790],57634:[744,-664,1194,0,1194],57635:[744,-522,1790,0,1790],57636:[-52,274,1790,0,1790],57637:[-194,274,1194,0,1194],57638:[-52,274,1790,0,1790],57639:[280,-220,200,0,200],57640:[280,-220,200,0,200],57641:[280,-220,200,0,200],57642:[796,0,408,80,328],57643:[796,0,408,80,328],57644:[796,0,408,80,328],57645:[500,0,200,0,200],57646:[500,0,200,0,200],57647:[500,0,200,0,200],57648:[610,110,200,0,200],57649:[610,110,200,0,200],57650:[610,110,200,0,200],57651:[1032,0,770,550,610],57652:[295,0,770,550,800],57653:[1161,0,1969,328,1889],57654:[580,0,1969,328,1641],57655:[1161,0,1969,80,1641],57656:[1161,0,760,328,680],57657:[1161,0,760,80,432],57658:[1161,0,1163,328,1083],57659:[580,0,1163,328,835],57660:[1161,0,1163,80,835],57661:[1161,0,1566,328,1486],57662:[580,0,1566,328,1238],57663:[1161,0,1566,80,1238]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Size6"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size6/Regular/Main.js"]);
