﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'div', 'tr', {
	IdInputLabel: 'Id',
	advisoryTitleInputLabel: '<PERSON>v<PERSON><PERSON> Başlığı',
	cssClassInputLabel: 'Stilltipi Sınıfı',
	edit: 'Div Düzenle',
	inlineStyleInputLabel: 'Inline Stili',
	langDirLTRLabel: '<PERSON><PERSON> sağa (LTR)',
	langDirLabel: 'Dil Yönü',
	langDirRTLLabel: '<PERSON><PERSON><PERSON> sola (RTL)',
	languageCodeInputLabel: ' Dil Kodu',
	remove: 'Div Kaldır',
	styleSelectLabel: 'Stil',
	title: 'Div İçeriği Oluştur',
	toolbar: 'Div İçeriği Oluştur'
} );
