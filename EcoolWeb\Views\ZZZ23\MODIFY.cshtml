﻿@model ECOOL_APP.EF.HRMT24

@{
    ViewBag.Title = "角色維護";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("MODIFY", "ZZZ23", FormMethod.Post, new { name = "ZZZ23Form", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.ROLE_ID)

    <div class="panel panel-ACC">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    <div class="control-label col-md-2">
                        角色名稱
                    </div>
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.ROLE_NAME, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.HiddenFor(model => model.ROLE_NAME)
                        @Html.ValidationMessageFor(model => model.ROLE_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group text-center">
                  
                        <button value="Save" class="btn btn-default">
                          確定送出
                        </button>
                  
                        <a href='@Url.Action("QUERY", "ZZZ23")'  class="btn btn-default" role="button">
                           放棄編輯
                        </a>

                 
                </div>
            </div>
        </div>
    </div>


   
}
