﻿@model ECOOL_APP.EF.CkEditIndexViewModel
@Html.Partial("_Notice")

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>


@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)

@Html.HiddenFor(m => m.Search.SeeREF_TYPE)
@Html.HiddenFor(m => m.Search.SeeSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SeeUSER_NO)
@Html.HiddenFor(m => m.Search.WhereREF_NO)



<div style="text-align:right">
    <div class="form-inline" role="form">
        <div class="form-group">
            @Html.EditorFor(m => m.Search.whereSUBJECT, new { htmlAttributes = new { @class = "form-control input-sm", @placeholder ="標題、檔名" } })
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    </div>
</div>
<br/>
<div class="row">
    <div class="row">


        @foreach (var item in Model.ListData)
        {

            string Url = ECOOL_APP.UrlCustomHelper.Url_Content(item.FILE_PATH);

            <div class="col-sm-6 col-md-3" style="display: table; margin: 0 auto;">
                <div class="thumbnail">
                    @if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Audio)
                    {
                        <audio controls  style="width: 100%; display: block;" title="@item.FILE">
                            <source src="@Url" type="audio/mp3">
                            您的瀏覽器不支援此 HTML5 Audio
                        </audio>
                    }
                    else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Vedio)
                    {
                        <video controls style="max-width:150px;max-height:150px;" title="@item.FILE">
                            <source src="@Url" type="video/mp4">
                            您的瀏覽器不支援此 HTML5 Vedio標籤
                        </video>
                 
                    }
                    else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Image)
                    {
                        <img src="@Url" href="@Url" style="max-width:120px;max-height:120px;width:120px;height:120px;" title="@item.FILE" alt="@item.FILE" />
                    }
                </div>
                <div class="caption">
                    <h3>@item.SUBJECT</h3>
                    <p>
                        <a class="btn btn-primary btn-xs" role="button" onclick="onBtnLink('@Url')">帶入</a>
                        <a class="btn btn-primary btn-xs" role="button" onclick="onEdit('@item.REF_NO')">編輯</a>
                    </p>
                   
                </div>
            </div>

        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
<script type="text/javascript">
        var targetFormID = '#formEdit';


        $(document).ready(function () {
            $(".thumbnail img").colorbox({ photo: true });
    });


</script>

