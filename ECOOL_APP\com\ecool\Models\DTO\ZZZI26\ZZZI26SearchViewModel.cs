﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI26SearchViewModel
    {

        public string TEMP_BATCH_KEY { get; set; }

        /// <summary>
        /// 模式
        /// </summary>
        [DisplayName("模式")]
        [Required]
        public byte ModeVal { get; set; }

        /// <summary>
        /// 學校
        /// </summary>
        [DisplayName("學校")]
        [Required]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        /// 登入帳號
        /// </summary>
        [DisplayName("姓名/座號")]
        public string USER_NO { get; set; }


        /// <summary>
        /// 人數
        /// </summary>
        [DisplayName("筆數")]
        public string NumType { get; set; }

        /// <summary>
        /// 書本數
        /// </summary>
        [DisplayName("書本數")]
        public Int16 NumBook { get; set; }




    }
}
