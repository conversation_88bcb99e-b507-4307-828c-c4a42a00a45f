﻿@model EcoolWeb.Models.HomeRightBarViewModel
@{
    string SchoolNo = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<style>


    .shortcut-links-item {
        flex: 0 0 47px;
        display: block;
        margin: 0.3rem;
        font-size: 0;
        height: 47px;
        max-height: 50px;
        width: 50px;
        max-width: 50px;
        background-color: #ededed;
        border-radius: 3rem;
        box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.18);
    }

</style>
<div class="shortcut-links">
    @if (user == null || (user != null && user.USER_TYPE == "T") || (user != null && user.USER_TYPE == "A"))
    {
    <a href="javascript:void(0);" class="shortcut-links-item"
       onclick="postToProductList('@SchoolNo')"
       title="抽獎點數(獎品)" target="_blank">
        <img src="~/Content/images/img-student-card-pay.png" alt="">
        抽獎點數(獎品)
    </a>
        <a class="shortcut-links-item" href="@Url.Action("Index2", "BarcCodeMyCash", new { WhereSchoolNo = SchoolNo })"
           title="ATM" target="_blank">
            <img src="~/Content/images/img-atm.png" alt="">
            ATM
        </a>
        <a class="shortcut-links-item" href="@Url.Action("Index2", "ADDI13", new { SCHOOL_NO1 = SchoolNo })"
           title="發放紙本點數" target="_blank">
            <img src="~/Content/images/img-paperpoint.png" alt="">
            領取紙本點數
        </a>
    }

    @if (user == null || (user != null && user.USER_TYPE == "T") || (user != null && user.USER_TYPE == "A") || (user != null && user.USER_TYPE == "G") || (user != null && user.USER_TYPE == "S") || (user != null && user.USER_TYPE == "P"))
    {
        <a class="shortcut-links-item" href="@Url.Action("Intro", "Home", new { WhereSchoolNo = SchoolNo })"
           title="動畫介紹" target="_blank">
            <img src="~/Content/images/img-ecool-video.png" alt="">
            動畫介紹
        </a>
        <a class="shortcut-links-item" href="@Url.Action("index2", "DownloadManager", new { WhereSchoolNo = SchoolNo })"
           title="說明文件" target="_blank">
            <img src="~/Content/images/img-help-pdf.png" alt="">
            說明文件
        </a>
    }
</div>
<a class="shortcut-qrcode" href="https://goo.gl/ZBNmnN" target="_blank">
    <img src="~/Content/images/AppQRCode.jpg" alt="">
    APP下載
</a>

@if (Model.LoginUser != null)
{




    if (Model.LoginUser.USER_TYPE == UserType.Student)
    {
        <div>
            @*以下是閱讀等級徽章*@
            <div class="read-certification mx-auto">
                <strong class="read-certification-title">閱讀認證榮譽</strong>
                <ul class="list-unstyled mb-0">
                    @if (Model.ReadImgURL != null)
                    {
                        foreach (var ReadImg in Model.ReadImgURL as Dictionary<byte, string>)
                        {
                            <li class="read-certification-lv@(@ReadImg.Key)">擁有等級獎章Lv<span>@ReadImg.Key</span></li>
                        }
                    }
                </ul>
            </div>

            @*@if (Model.ReadImgURL != null)
                {
                    string BackgroundImage = string.Empty;

                    foreach (var ReadImg in Model.ReadImgURL as Dictionary<byte, string>)
                    {
                        if (string.IsNullOrWhiteSpace(BackgroundImage))
                        {
                            BackgroundImage = @"background-image:url(" + Url.Content(ReadImg.Value) + ")";
                        }
                        else
                        {
                            BackgroundImage = BackgroundImage + @",url(" + Url.Content(ReadImg.Value) + ")";
                        }
                    }
                }*@



            @*以下是認證等級徽章*@
            <div class="read-books mx-auto">
                <strong class="read-books-title">閱讀護照榮譽</strong>
                <ul class="list-unstyled mb-0">
                    @if (Model.PassportImgURL != null)
                    {
                        foreach (var PassportImg in Model.PassportImgURL as Dictionary<byte, string>)
                        {
                            <li class="read-books-lv@(@PassportImg.Key)">擁有等級獎章Lv<span>@PassportImg.Key</span></li>
                        }
                    }
                </ul>
            </div>


            @*以下是運動撲滿*@

            <div class="sport-banker mx-md-auto">

                @if (Model.RUN_TOTAL_METER >= 411.5)
                {
                    <img src="~/Content/img/RunningDoll.jpg" />
                    <img src="@Url.Content(Model.RunMedalImage)" />
                }
                else
                {
                    <img src="@Url.Content(Model.RunMedalImage)" />
                }
                <p class="sport-banker-txt">
                    @if (Model.RUN_TOTAL_METER >= 411.5)
                    {
                        <span>恭喜完成跑步任務</span>
                    }
                    else
                    {
                        <span>目前跑步撲滿@(Model.RUN_TOTAL_METER)公里</span>

                    }
                    <br />
                    <span class="sport-banker-info">
                        @if (Model.RUN_TOTAL_METER >= 411.5)
                        {
                            <span>從台北跑到高雄</span>
                        }
                        else
                        {
                            <span>位於(超過)@(Model.LOCATION_NAME.Trim())站</span>
                        }
                    </span>
                </p>
            </div>
            <img id="user-players" src='@Url.Content(Model.PlayerUrl)' class="user-players" />
        </div>


        if (Model.PlayerUrl.IndexOf("People_P04") > 0 || Model.PlayerUrl.IndexOf("People_P02") > 0 || Model.PlayerUrl.IndexOf("People-C-Kids_P14_2x") > 0)
        {
<script>
                //角色娃娃滑動動畫換圖
                function playerDefault() {
                    //已設定之角色圖
                    this.setAttribute('src', '@Url.Content(Model.PlayerUrl)');
                }
                function playerAnimation() {
                    //動圖角色圖
                    this.setAttribute('src', '@string.Format("{0}.gif",Url.Content(Model.PlayerUrl))');
                }
                var userPlayers = document.getElementById('user-players');
                userPlayers.addEventListener('mouseover', playerAnimation);
                userPlayers.addEventListener('mouseleave', playerDefault);
                userPlayers.addEventListener('touchstart', playerAnimation);
</script>
        }
    }
}


<script>
   function postToProductList(schoolNo) {
        const form = document.createElement("form");
        form.method = "POST";
        form.action = '@Url.Action("ProductListForFullScreen", "AWAI01")';
        form.target = "_blank"; // 若你希望開新視窗

        const input = document.createElement("input");
        input.type = "hidden";
        input.name = "WhereSCHOOL_NO";
        input.value = schoolNo;

        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }

</script>

