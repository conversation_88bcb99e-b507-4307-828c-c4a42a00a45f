﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT16
    {

        ///Summary
        ///
        ///Summary
        [Display(Name = "問題序號")]
        [StringLength(100)]
        public string QUESTIONS_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "學校ID")]
        [StringLength(16)]
        public string SCHOOL_NO { get; set; }


        [Display(Name = "學校")]
        public string SCHOOL_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "主旨")]
        [Required]
        [StringLength(100)]
        public string SUBJECT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "問題")]
        [Required]
        public string QUESTIONS_TXT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "發問者帳號")]
        [StringLength(20)]
        public string USER_NO { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "發問者")]
        [StringLength(40)]
        public string NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "發問者")]
        [StringLength(40)]
        [Required]
        public string SNAME { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "E-MAIL")]
        [StringLength(510)]
        [DataType(DataType.EmailAddress)]
        public string E_MAIL { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "發問日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "建立人員")]
        [StringLength(100)]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "修改人")]
        [StringLength(100)]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "修改日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CHG_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "備註")]
        [StringLength(510)]
        public string MEMO { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "檔名")]
        [StringLength(510)]
        public string FILE_NAME { get; set; }


        [Display(Name = "回覆筆數")]
        public int? ANS_COUNT { get; set; }


        [Display(Name = "狀態")]
        public string STATUS { get; set; }



        public static class STATUS_Val
        {
            /// <summary>
            /// 未回覆
            /// </summary>
            public static string STATUS_1 = "1";

            /// <summary>
            /// 已回覆/未讀取
            /// </summary>
            public static string STATUS_2 = "2";

            /// <summary>
            /// 已讀取
            /// </summary>
            public static string STATUS_3 = "3";

            /// <summary>
            /// 此問題已解決
            /// </summary>
            public static string STATUS_Z = "Z";

            /// <summary>
            /// 此問題未解決
            /// </summary>
            public static string STATUS_UnZ = "UnZ";
            /// <summary>
            /// 問題作廢
            /// </summary>
            public static string STATUS_N = "N";


        }

    }

}
