﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class BT02AmdinViewModel
    {
        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_BULLET_ID", ResourceType = typeof(PageResource))]
        public string BULLET_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "SCHOOL_NO", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string SCHOOL_NO { get; set; }

        [Display(Name = "SCHOOL_NO", ResourceType = typeof(PageResource))]
        public string SHORT_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_CLASS_TYPE", ResourceType = typeof(PageResource))]
        [StringLength(20)]
        [Required]
        public string CLASS_TYPE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_CLASS_TYPE", ResourceType = typeof(PageResource))]
        [StringLength(20)]
        public string CLASS_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_SUBJECT", ResourceType = typeof(PageResource))]
        [StringLength(400)]
        public string SUBJECT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_CONTENT_TXT", ResourceType = typeof(PageResource))]
        // [UIHint("Html")]
        public string CONTENT_TXT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_ISPUBLISH", ResourceType = typeof(PageResource))]
        public string ISPUBLISH { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_PUSH_YN", ResourceType = typeof(PageResource))]
        public string PUSH_YN { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_S_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        [Required]
        public DateTime? S_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_E_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        [Required]
        public DateTime? E_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_TOP_YN", ResourceType = typeof(PageResource))]
        public string TOP_YN { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CRE_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CRE_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CRE_PERSON_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CRE_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CHG_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CHG_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CHG_PERSON_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CHG_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CHG_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "MEMO", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string MEMO { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "LinkContext", ResourceType = typeof(PageResource))]
        public string LinkContext { get; set; }

        public List<uBET02_LANG> Details_LANG { get; set; }

        // public Dictionary<string,uBET02_LANG> Details_LANGA { get; set; }

        public List<uBET02_FILE> Details_FILE { get; set; }

        public string REF_KEY { get; set; }
    }
}