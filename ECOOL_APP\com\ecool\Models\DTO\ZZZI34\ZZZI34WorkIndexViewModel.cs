﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34WorkIndexViewModel
    {
        /// <summary>
        /// 分享網址進入的
        /// </summary>
        public string ShareViewPHOTO_NO { get; set; }
        public string FromURL { get; set; }
        public string ISPersonal_YN { get; set; }
        public string WhereART_GALLERY_NOGallaryN1 { get; set; }
        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        public bool? WhereMyWork { get; set; }
        public string IsPostBack { get; set; }
        public string ZZZI34ImgType { get; set; }
        public ZZZI34SearchViewModel Search { get; set; }

        public ZZZI34WorkSearchViewModel WorkSearch { get; set; }

        public string IP_ADDRESS { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ZZZI34IndexWorkListDataViewModel> WorkListData;

        public ZZZI34WorkIndexViewModel()
        {
            PageSize = 9;
        }
    }
}