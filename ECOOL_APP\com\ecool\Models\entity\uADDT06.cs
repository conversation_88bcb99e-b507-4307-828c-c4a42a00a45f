﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT06
    {

        ///Summary
        ///閱讀認證流水號
        ///Summary
        public int APPLY_NO { get; set; }

        ///Summary
        ///學校代碼
        ///Summary
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///帳號
        ///Summary
        [DisplayName("姓名")]
        public string USER_NO { get; set; }

        ///Summary
        ///班級
        ///Summary
        public string CLASS_NO { get; set; }

        ///Summary
        ///學年度
        ///Summary
        public int SYEAR { get; set; }

        ///Summary
        ///學期
        ///Summary
        public int SEMESTER { get; set; }

        ///Summary
        ///座號
        ///Summary
        public string SEAT_NO { get; set; }

        ///Summary
        ///姓名
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///簡稱
        ///Summary
        public string SNAME { get; set; }

        ///Summary
        ///是否為閱讀護照
        ///Summary
        public string PASSPORT_YN { get; set; }

        ///Summary
        ///閱讀護照代碼
        ///Summary
        public string BOOK_ID { get; set; }

        ///Summary
        ///書名
        ///Summary
        [DisplayName("書名")]
        public string BOOK_NAME { get; set; }

        ///Summary
        ///心得
        ///Summary
        public string REVIEW { get; set; }

        ///Summary
        ///批閱後內容
        ///Summary
        public string REVIEW_VERIFY { get; set; }

        ///Summary
        ///評語
        ///Summary
        public string VERIFY_COMMENT { get; set; }

        ///Summary
        ///圖檔儲存路徑
        ///Summary
        public string IMG_FILE { get; set; }

        ///Summary
        ///錄音檔儲存路徑
        ///Summary
        public string VOICE_FILE { get; set; }

        ///Summary
        ///申請方式 0:學生線上申請, 1:老師代申請 2:批次上傳 3:代申請閱讀認證
        ///Summary
        public byte APPLY_TYPE { get; set; }

        ///Summary
        ///狀態  n:草稿 ,y: 批閱通過 ,2:批次上傳批閱通過, 9: 作廢
        ///Summary
        public string APPLY_STATUS { get; set; }

        ///Summary
        ///批閱教師/批閱者
        ///Summary
        public string VERIFIER { get; set; }

        ///Summary
        ///申請日
        ///Summary
        public DateTime CRE_DATE { get; set; }

        ///Summary
        ///修改日期
        ///Summary
        public DateTime CHG_DATE { get; set; }

        ///Summary
        ///審核通過日期
        ///Summary
        public DateTime VERIFIED_DATE { get; set; }

        ///Summary
        ///推薦與否
        ///Summary
        public string SHARE_YN { get; set; }

    }
}
