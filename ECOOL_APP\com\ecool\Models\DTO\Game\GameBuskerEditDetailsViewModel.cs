﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameBuskerEditDetailsViewModel
    {
        /// <summary>
        ///表演人員隊伍id
        /// </summary>
        [DisplayName("表演人員隊伍id")]
        public string BUSKER_ID { get; set; }

        /// <summary>
        ///表演人員隊伍項次
        /// </summary>
        [DisplayName("表演人員隊伍項次")]
        public string BUSKER_ITEM { get; set; }

        /// <summary>
        ///表演 id
        /// </summary>
        [DisplayName("表演 id")]
        public string TITLE_SHOW_ID { get; set; }

        /// <summary>
        ///暫時使用者id
        /// </summary>
        [DisplayName("暫時使用者id")]
        public string TEMP_USER_ID { get; set; }

        /// <summary>
        ///數位學生證id/QR Code Id
        /// </summary>
        [DisplayName("數位學生證id/QR Code Id")]
        public string GAME_USER_ID { get; set; }

        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///使用者類別
        /// </summary>
        [DisplayName("身份")]
        public string GAME_USER_TYPE { get; set; }

        /// <summary>
        ///身份
        /// </summary>
        [DisplayName("身份")]
        public string GAME_USER_TYPE_DESC { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///全名
        /// </summary>
        [DisplayName("全名")]
        public string NAME { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///電話
        /// </summary>
        [DisplayName("電話")]
        public string PHONE { get; set; }

     
        
        public DateTime? CHG_DATE { get; set; }
    }
}
