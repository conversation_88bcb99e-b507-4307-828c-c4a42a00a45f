﻿using com.ecool.service;
using EcoolWeb.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    public class SampleController : Controller
    {
        // GET: Sample
        public ActionResult Index()
        {
            return View();
        }

        /// <summary>兌換獎品清單</summary>
        /// <returns>
        /// AWARD_NO：流水號
        /// AWARD_NAME：獎品名稱
        /// COST_CASH：兌換點數
        /// QTY_STORAGE：獎品數量
        /// SDATETIME：開始兌換日
        /// EDATETIME：兌換期限
        /// DESCRIPTION：備註說明
        /// IMG_FILE：圖片位置
        /// AWARD_STATUS：狀態
        /// </returns>
        public ActionResult SampleProduct()
        {
            try
            {
                ViewData["PRODUCT_HTB"] = new ProductService().USP_PRODUCT_QUERY(string.Empty);
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }
            return View();
        }

        /// <summary>獎品管理</summary>
        /// <returns>同兌換獎品清單</returns>
        public ActionResult SampleProductMana()
        {
            try
            {
                if (null != Request["AWARD_NO"])
                {
                    ViewData["PRODUCT_HTB"] = new ProductService().USP_PRODUCT_QUERY(Request["AWARD_NO"]);
                }
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }
            return View();
        }

        /// <summary>檔案上傳</summary>
        /// <param name="fileInfo">檔案資料</param>
        /// <param name="UploadType">上傳種類：1、MapPath 2、實體路徑</param>
        public string UploadFile(FileInfo fileInfo, string UploadType)
        {

            string fileName = fileInfo.Name.Substring(0, fileInfo.Name.Length - fileInfo.Extension.Length);
            string fileExtension = fileInfo.Extension;
            if (UploadType == "1")
            {
                string tempPath = Request.MapPath("~\\" + System.Web.Configuration.WebConfigurationManager.AppSettings["ProductImgPath"]);
                string UpLoadFile = tempPath + "\\" + fileName + fileExtension;

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                Request.Files[0].SaveAs(UpLoadFile);
                return "/" + System.Web.Configuration.WebConfigurationManager.AppSettings["ProductImgPath"].Replace("\\", "/") + "/" + fileName + fileExtension;
            }
            else if (UploadType == "2")
            {
                string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"];
                string UpLoadFile = tempPath + "\\" + fileName + fileExtension;

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                Request.Files[0].SaveAs(UpLoadFile);
                return UpLoadFile;
            }
            else { return string.Empty; }

        }

        /// <summary>檔案上傳站台外資料夾</summary>
        public ActionResult SampleUploadFile()
        {
            string strIMG_FILE = string.Empty;
            try
            {
                if (Request.Files[0].FileName != "")
                {
                    FileInfo fileInfo = new FileInfo(Request.Files[0].FileName);
                    strIMG_FILE = UploadFile(fileInfo, "2");
                }
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }

            return View("../Sample/Index");
        }

        /// <summary>獎品管理：新增</summary>
        [HttpPost]
        public ActionResult SampleProductInsert()
        {
            string strIMG_FILE = string.Empty;
            try
            {
                if (Request.Files[0].FileName != "")
                {
                    FileInfo fileInfo = new FileInfo(Request.Files[0].FileName);
                    strIMG_FILE = UploadFile(fileInfo, "1");
                }
                //資料
                new ProductService().USP_PRODUCT_INSERT(Request["ParamAWARD_NAME"], Request["ParamCOST_CASH"], Request["ParamQTY_STORAGE"], Request["ParamSDATETIME"], Request["ParamEDATETIME"], Request["ParamDESCRIPTION"], strIMG_FILE);
                ViewData["ParamAWARD_NAME"] = Request["ParamAWARD_NAME"];
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }
            finally
            {
                if (string.IsNullOrWhiteSpace((string)ViewData["ViewMessage"]))
                {
                    ViewData["ViewMessage"] = "新增成功";
                }
            }
            return View("SampleProductMana");
        }

        /// <summary>獎品管理：修改</summary>
        [HttpPost]
        public ActionResult SampleProductModify()
        {
            string strIMG_FILE = string.Empty;
            try
            {
                if (Request.Files[0].FileName != "")
                {
                    FileInfo fileInfo = new FileInfo(Request.Files[0].FileName);
                    strIMG_FILE = UploadFile(fileInfo, "1");
                }
                new ProductService().USP_PRODUCT_UPDATE(Request["ParamAWARD_NAME"], Request["ParamCOST_CASH"], Request["ParamQTY_STORAGE"], Request["ParamSDATETIME"], Request["ParamEDATETIME"], Request["ParamDESCRIPTION"], strIMG_FILE, Request["ParamAWARD_NO"]);
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }
            finally
            {
                if (string.IsNullOrWhiteSpace((string)ViewData["ViewMessage"]))
                {
                    ViewData["ViewMessage"] = "修改成功";
                }
            }
            return View("SampleProductMana");
        }

        /// <summary>獎品管理：刪除</summary>
        [HttpPost]
        public ActionResult SampleProductDelete()
        {
            try
            {
                new ProductService().USP_PRODUCT_DELETE(Request["ParamAWARD_NO"]);
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }
            finally
            {
                if (string.IsNullOrWhiteSpace((string)ViewData["ViewMessage"]))
                {
                    ViewData["ViewMessage"] = "刪除成功";
                }
            }
            return View("SampleProductMana");
        }



        public ActionResult SampleGrid()
        {
            return View();
        }

        /// <summary>Grid</summary>
        public ActionResult GridGetData(string sidx, string sord, int page, int rows)
        {
            return Content(JsonHelper.JsonForJqgrid(new ProductService().USP_PRODUCT_QUERY_DATATB(sidx, sord, page, rows), rows, new CommService().GetGirdTotalCount("AWAT01"), page), "application/json");
        }


        public ActionResult SampleStyleEdit()
        {
            return View();
        }


    }
}