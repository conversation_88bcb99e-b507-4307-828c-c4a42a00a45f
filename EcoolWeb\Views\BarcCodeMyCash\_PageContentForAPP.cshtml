﻿@model BarcCodeMyCashIndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    //兌換獎品系統路徑~~
    ViewBag.SysAwardPath = EcoolWeb.Controllers.AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);

    string unicornPhoto_sm = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", Model.Level, "sm"));
    string unicornPhoto_bg = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", Model.Level, "lg"));
}
<style type="text/css">
    .td {
        border: 1px solid #483D8B;
        width: 50%;
        height: 30px;
        font-size: 0.9em;
        text-align: left;
        margin: auto;
        vertical-align: middle;
        /*font-size: 15px*/
    }
</style>
@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.WhereSchoolNo)
@Html.HiddenFor(m => m.TimeoutSeconds)
@Html.HiddenFor(m => m.ShowStep2)

@if ((Model.TimeoutSeconds ?? 0) > 0)
{
    <span class="TimeoutSeconds" data-seconds-left="@Model.TimeoutSeconds" style="display:none"></span>
}
@{
    string run_total = (Convert.ToDouble(Model.RUN_TOTAL_METER ?? 0) / 1000.000).ToString("#,0.000");
    int pos = run_total.IndexOf(".");
    string run_km = run_total.Substring(0, pos);
    string run_m = run_total.Substring(pos + 1, run_total.Length - pos - 1);

}

<div class="jumbotron justify-content-center align-items-center align-content-center" style="padding-top:15px;background-color:#45e0e2">
    @*<div class="col d-flex justify-content-center" id="atm_head">
            <div id="atm_logo" style="background-image:url('@Url.Content("~/assets/img/atm_logo_02.png")');background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
        </div>*@

    <div class="container">
        <div class="row">

            <div class="col-md-4 col-md-offset-4">

                <div class="login-panel panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title text-center">
                            <img src="@unicornPhoto_sm" style="max-width:200px" />
                        </h3>
                    </div>

                    <div class="css-table">
                        <div class="tr">
                            <div class="td" style="background-color:white;border:1px solid #483D8B;width:100%;height:30px;text-align:center;margin:auto;vertical-align:middle;font-size:15px">
                                <b>默默 @Model.Level 級</b>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @*<div>
            </div>*@
        @*<span class="text-white text-center" style="font-size:90%;margin-left:140px">
                <b>默默 @Model.Level 級</b>
            </span>*@
    </div>
    @*<div class=".col-xs-6 .col-sm-4">
        </div>*@
    <div class="container">
        <div class="row">
            <div class="col-md-4 col-md-offset-4">
                <div class="login-panel panel panel-default">
                    <div class="css-table">
                        <div class="tr">
                            <div class="td" style="background-color:#00ffff">
                                <b> 目前酷幣:</b>
                            </div>
                            <div class="td"> <span style="color:#ff0000;"><b> @Model.UserCash 點</b></span></div>
                        </div>

                        <div class="tr">
                            <div class="td" style="background-color:#00ffff">
                                <b> 定存酷幣:</b>
                            </div>
                            <div class="td">
                                <b><span style="color:#ff0000;"> @(string.Format("{0:0.###} 點", Model.UserAWAI07))</span></b>
                            </div>
                        </div>
                        <div class="tr">
                            <div class="td" style="background-color:#00ffff">
                                <b>總借書量:</b>
                            </div>
                            <div class="td">

                                <b>
                                    @(Model.AllBookQty) 本，本學期借  @(Model.UserBookQty) 本，   本月份借

                                    <span style="color:#ff0000;">@(Model.MonthBookQty)</span> 本
                                </b>
                            </div>
                        </div>
                        <div class="tr">
                            <div class="td" style="background-color:#00ffff">   <b>閱讀認證:</b></div>
                            <div class="td">
                                <b>
                                    @(Model.MyData.BOOK_QTY ?? 0) 篇，是第 @(Model.MyData.LEVEL_ID ?? "0")級
                                    @(Model.MyData.LEVEL_DESC)，再閱讀 @(Model.MyData.UNLEVEL_QTY == null ? "5" : Model.MyData.UNLEVEL_QTY) 本書就
                                    可以升級囉!
                                </b>
                            </div>
                        </div>
                        <div class="tr">
                            <div class="td" style="background-color:#00ffff"><b>運動撲滿:</b> </div>
                            <div class="td">
                                <b>
                                    <span style="color:#ff0000;">
                                        @(string.Format("{0}公里{1}公尺", run_km, run_m))
                                    </span>
                                    @("," + (string)ViewBag.RUN_UPGRADE_RESULTSTR)
                                </b>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/Scripts/jquery.simple.timer.js?ver=2"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<script type="text/javascript">
      var oTimerId;
      function Timeout() {
        var SCHOOL_NO = '';
        SCHOOL_NO = $("#@Html.IdFor(m=>m.WhereSchoolNo)").val();
        if ('@Model.ChangeMode' == "(1)酷幣點數排行榜+現有點數排行榜") {
            window.location.href = "@Url.Action("LeaderIndex", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index&TimeoutSeconds=" + '@Model.TimeoutSeconds';

        }
        else if ('@Model.ChangeMode' == "(2)閱讀認證排行榜+運動撲滿排行榜") {
   
            window.location.href = "@Url.Action("LeaderIndex2", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }
        else {
           
            window.location.href = "@Url.Action("LeaderIndex3", "BarcCodeMyCash")?" + "WhereSchoolNo = " +@Model.WhereSchoolNo+"&FROMACTION=Index&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }

        x = 0;

        return true;
    }
     x = 0
    function countSecond() {
        var Detime = 0;
        Detime =  @Model.DelayTime* 60;
        if (x < Detime) {
            x = x + 1
            document.getElementById("displayBox").value = x
            setTimeout("countSecond()", 1000)
        }
        else {
            Timeout();

        }
    }
    document.onmouseup = function () {
        x = 0;
    }
    countSecond()
     $('.TimeoutSeconds').startTimer({
        onComplete: function (element) {
            document.location.href = '@Url.Action("Index", "BarcCodeMyCash", new { WhereSchoolNo = Model.WhereSchoolNo})&TimeoutSeconds=@Model.TimeoutSeconds';
        },
      });

     $(document).ready(function () {

         $(".mycolorbox").colorbox({
             opacity: 0.82,
             maxWidth: '70%', maxHeight: '70%'
         });

       if ($('#@Html.IdFor(m => m.TimeoutSeconds)').val() != "") {
           var DivATM2 = document.getElementById("atm_btn_img_1");
           DivATM2.style.display = 'none';

           var DivATM2_2 = document.getElementById("atm_btn_img_2");
           DivATM2_2.style.display = 'none';

         }

     });

     function call(e, input) {
        var code = (e.keyCode ? e.keyCode : e.which);

        if (code == 13) // 13 是 Enter 按鍵的值
        {
            event.preventDefault();
            if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
        }
     }

       function BT1_CLICK()
       {

           if ($('#@Html.IdFor(m => m.ShowStep2)').val().toLowerCase() == "true") {

               $(".board_text").toggle();
               $("#ATM3").toggle();

        }
        else {
            alert('未輸入學號/掃描條碼')
        }

    }

    function BT2_CLICK() {
        $('#@Html.IdFor(m => m.WhereKeyword)').val('')
        $('#form1').attr("action", "@Url.Action("Index", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo})")
        $('#form1').submit();

    }
</script>