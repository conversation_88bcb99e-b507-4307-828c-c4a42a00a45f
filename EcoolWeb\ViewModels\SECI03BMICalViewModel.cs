﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

using ECOOL_APP.EF;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SECI03BMICalViewModel
    {
        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        /// <summary>
        /// 性別
        /// </summary>
        [DisplayName("性別")]
        public string Sex { get; set; }

        /// <summary>
        /// 年齡
        /// </summary>
        [DisplayName("年齡")]
        public float? Old { get; set; }

        /// <summary>
        /// Height
        /// </summary>
        [DisplayName("身高(公分)")]
        public float? Height { get; set; }

        /// <summary>
        /// Weight
        /// </summary>
        [DisplayName("體重(公斤)")]
        public float? Weight { get; set; }

        /// <summary>
        /// BMI
        /// </summary>
        [DisplayName("BMI")]
        public float Result_BMI { get; set; }

        /// <summary>
        /// Karl
        /// </summary>
        [DisplayName("Karl")]
        public float Result_Karl { get; set; }

        /// <summary>
        /// 結果
        /// </summary>
        [DisplayName("結果")]
        public float Result_Comment { get; set; }
    }
}