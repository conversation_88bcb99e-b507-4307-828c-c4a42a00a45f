/*
 Highcharts JS v6.0.6 (2018-02-05)

 (c) 2009-2016 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(S,M){"object"===typeof module&&module.exports?module.exports=S.document?M(S):M:S.Highcharts=M(S)})("undefined"!==typeof window?window:this,function(S){var M=function(){var a="undefined"===typeof S?window:S,z=a.document,F=a.navigator&&a.navigator.userAgent||"",C=z&&z.createElementNS&&!!z.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,p=/(edge|msie|trident)/i.test(F)&&!a.opera,h=-1!==F.indexOf("Firefox"),d=-1!==F.indexOf("Chrome"),m=h&&4>parseInt(F.split("Firefox/")[1],
10);return a.Highcharts?a.Highcharts.error(16,!0):{product:"Highcharts",version:"6.0.6",deg2rad:2*Math.PI/360,doc:z,hasBidiBug:m,hasTouch:z&&void 0!==z.documentElement.ontouchstart,isMS:p,isWebKit:-1!==F.indexOf("AppleWebKit"),isFirefox:h,isChrome:d,isSafari:!d&&-1!==F.indexOf("Safari"),isTouchDevice:/(Mobile|Android|Windows Phone)/.test(F),SVG_NS:"http://www.w3.org/2000/svg",chartCount:0,seriesTypes:{},symbolSizes:{},svg:C,win:a,marginNames:["plotTop","marginRight","marginBottom","plotLeft"],noop:function(){},
charts:[]}}();(function(a){a.timers=[];var z=a.charts,F=a.doc,C=a.win;a.error=function(p,h){p=a.isNumber(p)?"Highcharts error #"+p+": www.highcharts.com/errors/"+p:p;if(h)throw Error(p);C.console&&console.log(p)};a.Fx=function(a,h,d){this.options=h;this.elem=a;this.prop=d};a.Fx.prototype={dSetter:function(){var a=this.paths[0],h=this.paths[1],d=[],m=this.now,v=a.length,r;if(1===m)d=this.toD;else if(v===h.length&&1>m)for(;v--;)r=parseFloat(a[v]),d[v]=isNaN(r)?h[v]:m*parseFloat(h[v]-r)+r;else d=h;this.elem.attr("d",
d,null,!0)},update:function(){var a=this.elem,h=this.prop,d=this.now,m=this.options.step;if(this[h+"Setter"])this[h+"Setter"]();else a.attr?a.element&&a.attr(h,d,null,!0):a.style[h]=d+this.unit;m&&m.call(a,d,this)},run:function(p,h,d){var m=this,v=m.options,r=function(a){return r.stopped?!1:m.step(a)},n=C.requestAnimationFrame||function(a){setTimeout(a,13)},c=function(){for(var f=0;f<a.timers.length;f++)a.timers[f]()||a.timers.splice(f--,1);a.timers.length&&n(c)};p===h?(delete v.curAnim[this.prop],
v.complete&&0===a.keys(v.curAnim).length&&v.complete.call(this.elem)):(this.startTime=+new Date,this.start=p,this.end=h,this.unit=d,this.now=this.start,this.pos=0,r.elem=this.elem,r.prop=this.prop,r()&&1===a.timers.push(r)&&n(c))},step:function(p){var h=+new Date,d,m=this.options,v=this.elem,r=m.complete,n=m.duration,c=m.curAnim;v.attr&&!v.element?p=!1:p||h>=n+this.startTime?(this.now=this.end,this.pos=1,this.update(),d=c[this.prop]=!0,a.objectEach(c,function(a){!0!==a&&(d=!1)}),d&&r&&r.call(v),p=
!1):(this.pos=m.easing((h-this.startTime)/n),this.now=this.start+(this.end-this.start)*this.pos,this.update(),p=!0);return p},initPath:function(p,h,d){function m(a){var b,e;for(u=a.length;u--;)b="M"===a[u]||"L"===a[u],e=/[a-zA-Z]/.test(a[u+3]),b&&e&&a.splice(u+1,0,a[u+1],a[u+2],a[u+1],a[u+2])}function v(a,b){for(;a.length<e;){a[0]=b[e-a.length];var c=a.slice(0,k);[].splice.apply(a,[0,0].concat(c));q&&(c=a.slice(a.length-k),[].splice.apply(a,[a.length,0].concat(c)),u--)}a[0]="M"}function r(a,c){for(var f=
(e-a.length)/k;0<f&&f--;)b=a.slice().splice(a.length/D-k,k*D),b[0]=c[e-k-f*k],A&&(b[k-6]=b[k-2],b[k-5]=b[k-1]),[].splice.apply(a,[a.length/D,0].concat(b)),q&&f--}h=h||"";var n,c=p.startX,f=p.endX,A=-1<h.indexOf("C"),k=A?7:3,e,b,u;h=h.split(" ");d=d.slice();var q=p.isArea,D=q?2:1,K;A&&(m(h),m(d));if(c&&f){for(u=0;u<c.length;u++)if(c[u]===f[0]){n=u;break}else if(c[0]===f[f.length-c.length+u]){n=u;K=!0;break}void 0===n&&(h=[])}h.length&&a.isNumber(n)&&(e=d.length+n*D*k,K?(v(h,d),r(d,h)):(v(d,h),r(h,
d)));return[h,d]}};a.Fx.prototype.fillSetter=a.Fx.prototype.strokeSetter=function(){this.elem.attr(this.prop,a.color(this.start).tweenTo(a.color(this.end),this.pos),null,!0)};a.merge=function(){var p,h=arguments,d,m={},v=function(d,n){"object"!==typeof d&&(d={});a.objectEach(n,function(c,f){!a.isObject(c,!0)||a.isClass(c)||a.isDOMElement(c)?d[f]=n[f]:d[f]=v(d[f]||{},c)});return d};!0===h[0]&&(m=h[1],h=Array.prototype.slice.call(h,2));d=h.length;for(p=0;p<d;p++)m=v(m,h[p]);return m};a.pInt=function(a,
h){return parseInt(a,h||10)};a.isString=function(a){return"string"===typeof a};a.isArray=function(a){a=Object.prototype.toString.call(a);return"[object Array]"===a||"[object Array Iterator]"===a};a.isObject=function(p,h){return!!p&&"object"===typeof p&&(!h||!a.isArray(p))};a.isDOMElement=function(p){return a.isObject(p)&&"number"===typeof p.nodeType};a.isClass=function(p){var h=p&&p.constructor;return!(!a.isObject(p,!0)||a.isDOMElement(p)||!h||!h.name||"Object"===h.name)};a.isNumber=function(a){return"number"===
typeof a&&!isNaN(a)&&Infinity>a&&-Infinity<a};a.erase=function(a,h){for(var d=a.length;d--;)if(a[d]===h){a.splice(d,1);break}};a.defined=function(a){return void 0!==a&&null!==a};a.attr=function(p,h,d){var m;a.isString(h)?a.defined(d)?p.setAttribute(h,d):p&&p.getAttribute&&(m=p.getAttribute(h)):a.defined(h)&&a.isObject(h)&&a.objectEach(h,function(a,d){p.setAttribute(d,a)});return m};a.splat=function(p){return a.isArray(p)?p:[p]};a.syncTimeout=function(a,h,d){if(h)return setTimeout(a,h,d);a.call(0,
d)};a.extend=function(a,h){var d;a||(a={});for(d in h)a[d]=h[d];return a};a.pick=function(){var a=arguments,h,d,m=a.length;for(h=0;h<m;h++)if(d=a[h],void 0!==d&&null!==d)return d};a.css=function(p,h){a.isMS&&!a.svg&&h&&void 0!==h.opacity&&(h.filter="alpha(opacity\x3d"+100*h.opacity+")");a.extend(p.style,h)};a.createElement=function(p,h,d,m,v){p=F.createElement(p);var r=a.css;h&&a.extend(p,h);v&&r(p,{padding:0,border:"none",margin:0});d&&r(p,d);m&&m.appendChild(p);return p};a.extendClass=function(p,
h){var d=function(){};d.prototype=new p;a.extend(d.prototype,h);return d};a.pad=function(a,h,d){return Array((h||2)+1-String(a).length).join(d||0)+a};a.relativeLength=function(a,h,d){return/%$/.test(a)?h*parseFloat(a)/100+(d||0):parseFloat(a)};a.wrap=function(a,h,d){var m=a[h];a[h]=function(){var a=Array.prototype.slice.call(arguments),h=arguments,n=this;n.proceed=function(){m.apply(n,arguments.length?arguments:h)};a.unshift(m);a=d.apply(this,a);n.proceed=null;return a}};a.formatSingle=function(p,
h,d){var m=/\.([0-9])/,v=a.defaultOptions.lang;/f$/.test(p)?(d=(d=p.match(m))?d[1]:-1,null!==h&&(h=a.numberFormat(h,d,v.decimalPoint,-1<p.indexOf(",")?v.thousandsSep:""))):h=(d||a.time).dateFormat(p,h);return h};a.format=function(p,h,d){for(var m="{",v=!1,r,n,c,f,A=[],k;p;){m=p.indexOf(m);if(-1===m)break;r=p.slice(0,m);if(v){r=r.split(":");n=r.shift().split(".");f=n.length;k=h;for(c=0;c<f;c++)k&&(k=k[n[c]]);r.length&&(k=a.formatSingle(r.join(":"),k,d));A.push(k)}else A.push(r);p=p.slice(m+1);m=(v=
!v)?"}":"{"}A.push(p);return A.join("")};a.getMagnitude=function(a){return Math.pow(10,Math.floor(Math.log(a)/Math.LN10))};a.normalizeTickInterval=function(p,h,d,m,v){var r,n=p;d=a.pick(d,1);r=p/d;h||(h=v?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===m&&(1===d?h=a.grep(h,function(a){return 0===a%1}):.1>=d&&(h=[1/d])));for(m=0;m<h.length&&!(n=h[m],v&&n*d>=p||!v&&r<=(h[m]+(h[m+1]||h[m]))/2);m++);return n=a.correctFloat(n*d,-Math.round(Math.log(.001)/Math.LN10))};a.stableSort=function(a,h){var d=
a.length,m,v;for(v=0;v<d;v++)a[v].safeI=v;a.sort(function(a,n){m=h(a,n);return 0===m?a.safeI-n.safeI:m});for(v=0;v<d;v++)delete a[v].safeI};a.arrayMin=function(a){for(var h=a.length,d=a[0];h--;)a[h]<d&&(d=a[h]);return d};a.arrayMax=function(a){for(var h=a.length,d=a[0];h--;)a[h]>d&&(d=a[h]);return d};a.destroyObjectProperties=function(p,h){a.objectEach(p,function(a,m){a&&a!==h&&a.destroy&&a.destroy();delete p[m]})};a.discardElement=function(p){var h=a.garbageBin;h||(h=a.createElement("div"));p&&h.appendChild(p);
h.innerHTML=""};a.correctFloat=function(a,h){return parseFloat(a.toPrecision(h||14))};a.setAnimation=function(p,h){h.renderer.globalAnimation=a.pick(p,h.options.chart.animation,!0)};a.animObject=function(p){return a.isObject(p)?a.merge(p):{duration:p?500:0}};a.timeUnits={millisecond:1,second:1E3,minute:6E4,hour:36E5,day:864E5,week:6048E5,month:24192E5,year:314496E5};a.numberFormat=function(p,h,d,m){p=+p||0;h=+h;var v=a.defaultOptions.lang,r=(p.toString().split(".")[1]||"").split("e")[0].length,n,
c,f=p.toString().split("e");-1===h?h=Math.min(r,20):a.isNumber(h)?h&&f[1]&&0>f[1]&&(n=h+ +f[1],0<=n?(f[0]=(+f[0]).toExponential(n).split("e")[0],h=n):(f[0]=f[0].split(".")[0]||0,p=20>h?(f[0]*Math.pow(10,f[1])).toFixed(h):0,f[1]=0)):h=2;c=(Math.abs(f[1]?f[0]:p)+Math.pow(10,-Math.max(h,r)-1)).toFixed(h);r=String(a.pInt(c));n=3<r.length?r.length%3:0;d=a.pick(d,v.decimalPoint);m=a.pick(m,v.thousandsSep);p=(0>p?"-":"")+(n?r.substr(0,n)+m:"");p+=r.substr(n).replace(/(\d{3})(?=\d)/g,"$1"+m);h&&(p+=d+c.slice(-h));
f[1]&&0!==+p&&(p+="e"+f[1]);return p};Math.easeInOutSine=function(a){return-.5*(Math.cos(Math.PI*a)-1)};a.getStyle=function(p,h,d){if("width"===h)return Math.min(p.offsetWidth,p.scrollWidth)-a.getStyle(p,"padding-left")-a.getStyle(p,"padding-right");if("height"===h)return Math.min(p.offsetHeight,p.scrollHeight)-a.getStyle(p,"padding-top")-a.getStyle(p,"padding-bottom");C.getComputedStyle||a.error(27,!0);if(p=C.getComputedStyle(p,void 0))p=p.getPropertyValue(h),a.pick(d,"opacity"!==h)&&(p=a.pInt(p));
return p};a.inArray=function(p,h){return(a.indexOfPolyfill||Array.prototype.indexOf).call(h,p)};a.grep=function(p,h){return(a.filterPolyfill||Array.prototype.filter).call(p,h)};a.find=Array.prototype.find?function(a,h){return a.find(h)}:function(a,h){var d,m=a.length;for(d=0;d<m;d++)if(h(a[d],d))return a[d]};a.map=function(a,h){for(var d=[],m=0,v=a.length;m<v;m++)d[m]=h.call(a[m],a[m],m,a);return d};a.keys=function(p){return(a.keysPolyfill||Object.keys).call(void 0,p)};a.reduce=function(p,h,d){return(a.reducePolyfill||
Array.prototype.reduce).call(p,h,d)};a.offset=function(a){var h=F.documentElement;a=a.parentElement?a.getBoundingClientRect():{top:0,left:0};return{top:a.top+(C.pageYOffset||h.scrollTop)-(h.clientTop||0),left:a.left+(C.pageXOffset||h.scrollLeft)-(h.clientLeft||0)}};a.stop=function(p,h){for(var d=a.timers.length;d--;)a.timers[d].elem!==p||h&&h!==a.timers[d].prop||(a.timers[d].stopped=!0)};a.each=function(p,h,d){return(a.forEachPolyfill||Array.prototype.forEach).call(p,h,d)};a.objectEach=function(a,
h,d){for(var m in a)a.hasOwnProperty(m)&&h.call(d,a[m],m,a)};a.addEvent=function(p,h,d){var m,v,r=p.addEventListener||a.addEventListenerPolyfill;p.hcEvents&&!Object.prototype.hasOwnProperty.call(p,"hcEvents")&&(v={},a.objectEach(p.hcEvents,function(a,c){v[c]=a.slice(0)}),p.hcEvents=v);m=p.hcEvents=p.hcEvents||{};r&&r.call(p,h,d,!1);m[h]||(m[h]=[]);m[h].push(d);return function(){a.removeEvent(p,h,d)}};a.removeEvent=function(p,h,d){function m(c,n){var k=p.removeEventListener||a.removeEventListenerPolyfill;
k&&k.call(p,c,n,!1)}function v(){var c,d;p.nodeName&&(h?(c={},c[h]=!0):c=n,a.objectEach(c,function(a,c){if(n[c])for(d=n[c].length;d--;)m(c,n[c][d])}))}var r,n=p.hcEvents,c;n&&(h?(r=n[h]||[],d?(c=a.inArray(d,r),-1<c&&(r.splice(c,1),n[h]=r),m(h,d)):(v(),n[h]=[])):(v(),p.hcEvents={}))};a.fireEvent=function(p,h,d,m){var v;v=p.hcEvents;var r,n;d=d||{};if(F.createEvent&&(p.dispatchEvent||p.fireEvent))v=F.createEvent("Events"),v.initEvent(h,!0,!0),a.extend(v,d),p.dispatchEvent?p.dispatchEvent(v):p.fireEvent(h,
v);else if(v)for(v=v[h]||[],r=v.length,d.target||a.extend(d,{preventDefault:function(){d.defaultPrevented=!0},target:p,type:h}),h=0;h<r;h++)(n=v[h])&&!1===n.call(p,d)&&d.preventDefault();m&&!d.defaultPrevented&&m(d)};a.animate=function(p,h,d){var m,v="",r,n,c;a.isObject(d)||(c=arguments,d={duration:c[2],easing:c[3],complete:c[4]});a.isNumber(d.duration)||(d.duration=400);d.easing="function"===typeof d.easing?d.easing:Math[d.easing]||Math.easeInOutSine;d.curAnim=a.merge(h);a.objectEach(h,function(c,
A){a.stop(p,A);n=new a.Fx(p,d,A);r=null;"d"===A?(n.paths=n.initPath(p,p.d,h.d),n.toD=h.d,m=0,r=1):p.attr?m=p.attr(A):(m=parseFloat(a.getStyle(p,A))||0,"opacity"!==A&&(v="px"));r||(r=c);r&&r.match&&r.match("px")&&(r=r.replace(/px/g,""));n.run(m,r,v)})};a.seriesType=function(p,h,d,m,v){var r=a.getOptions(),n=a.seriesTypes;r.plotOptions[p]=a.merge(r.plotOptions[h],d);n[p]=a.extendClass(n[h]||function(){},m);n[p].prototype.type=p;v&&(n[p].prototype.pointClass=a.extendClass(a.Point,v));return n[p]};a.uniqueKey=
function(){var a=Math.random().toString(36).substring(2,9),h=0;return function(){return"highcharts-"+a+"-"+h++}}();C.jQuery&&(C.jQuery.fn.highcharts=function(){var p=[].slice.call(arguments);if(this[0])return p[0]?(new (a[a.isString(p[0])?p.shift():"Chart"])(this[0],p[0],p[1]),this):z[a.attr(this[0],"data-highcharts-chart")]})})(M);(function(a){var z=a.each,F=a.isNumber,C=a.map,p=a.merge,h=a.pInt;a.Color=function(d){if(!(this instanceof a.Color))return new a.Color(d);this.init(d)};a.Color.prototype=
{parsers:[{regex:/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]?(?:\.[0-9]+)?)\s*\)/,parse:function(a){return[h(a[1]),h(a[2]),h(a[3]),parseFloat(a[4],10)]}},{regex:/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/,parse:function(a){return[h(a[1]),h(a[2]),h(a[3]),1]}}],names:{none:"rgba(255,255,255,0)",white:"#ffffff",black:"#000000"},init:function(d){var m,h,r,n;if((this.input=d=this.names[d&&d.toLowerCase?d.toLowerCase():""]||d)&&d.stops)this.stops=C(d.stops,
function(c){return new a.Color(c[1])});else if(d&&d.charAt&&"#"===d.charAt()&&(m=d.length,d=parseInt(d.substr(1),16),7===m?h=[(d&16711680)>>16,(d&65280)>>8,d&255,1]:4===m&&(h=[(d&3840)>>4|(d&3840)>>8,(d&240)>>4|d&240,(d&15)<<4|d&15,1])),!h)for(r=this.parsers.length;r--&&!h;)n=this.parsers[r],(m=n.regex.exec(d))&&(h=n.parse(m));this.rgba=h||[]},get:function(a){var d=this.input,h=this.rgba,r;this.stops?(r=p(d),r.stops=[].concat(r.stops),z(this.stops,function(n,c){r.stops[c]=[r.stops[c][0],n.get(a)]})):
r=h&&F(h[0])?"rgb"===a||!a&&1===h[3]?"rgb("+h[0]+","+h[1]+","+h[2]+")":"a"===a?h[3]:"rgba("+h.join(",")+")":d;return r},brighten:function(a){var d,v=this.rgba;if(this.stops)z(this.stops,function(d){d.brighten(a)});else if(F(a)&&0!==a)for(d=0;3>d;d++)v[d]+=h(255*a),0>v[d]&&(v[d]=0),255<v[d]&&(v[d]=255);return this},setOpacity:function(a){this.rgba[3]=a;return this},tweenTo:function(a,m){var d=this.rgba,h=a.rgba;h.length&&d&&d.length?(a=1!==h[3]||1!==d[3],m=(a?"rgba(":"rgb(")+Math.round(h[0]+(d[0]-
h[0])*(1-m))+","+Math.round(h[1]+(d[1]-h[1])*(1-m))+","+Math.round(h[2]+(d[2]-h[2])*(1-m))+(a?","+(h[3]+(d[3]-h[3])*(1-m)):"")+")"):m=a.input||"none";return m}};a.color=function(d){return new a.Color(d)}})(M);(function(a){var z,F,C=a.addEvent,p=a.animate,h=a.attr,d=a.charts,m=a.color,v=a.css,r=a.createElement,n=a.defined,c=a.deg2rad,f=a.destroyObjectProperties,A=a.doc,k=a.each,e=a.extend,b=a.erase,u=a.grep,q=a.hasTouch,D=a.inArray,K=a.isArray,E=a.isFirefox,H=a.isMS,y=a.isObject,t=a.isString,x=a.isWebKit,
G=a.merge,B=a.noop,I=a.objectEach,g=a.pick,w=a.pInt,L=a.removeEvent,l=a.splat,J=a.stop,P=a.svg,R=a.SVG_NS,Q=a.symbolSizes,O=a.win;z=a.SVGElement=function(){return this};e(z.prototype,{opacity:1,SVG_NS:R,textProps:"direction fontSize fontWeight fontFamily fontStyle color lineHeight width textAlign textDecoration textOverflow textOutline".split(" "),init:function(a,g){this.element="span"===g?r(g):A.createElementNS(this.SVG_NS,g);this.renderer=a},animate:function(b,l,w){l=a.animObject(g(l,this.renderer.globalAnimation,
!0));0!==l.duration?(w&&(l.complete=w),p(this,b,l)):(this.attr(b,null,w),l.step&&l.step.call(this));return this},colorGradient:function(g,b,l){var N=this.renderer,w,c,e,t,q,f,u,d,J,L,x=[],T;g.radialGradient?c="radialGradient":g.linearGradient&&(c="linearGradient");c&&(e=g[c],q=N.gradients,u=g.stops,L=l.radialReference,K(e)&&(g[c]=e={x1:e[0],y1:e[1],x2:e[2],y2:e[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===c&&L&&!n(e.gradientUnits)&&(t=e,e=G(e,N.getRadialAttr(L,t),{gradientUnits:"userSpaceOnUse"})),
I(e,function(a,g){"id"!==g&&x.push(g,a)}),I(u,function(a){x.push(a)}),x=x.join(","),q[x]?L=q[x].attr("id"):(e.id=L=a.uniqueKey(),q[x]=f=N.createElement(c).attr(e).add(N.defs),f.radAttr=t,f.stops=[],k(u,function(g){0===g[1].indexOf("rgba")?(w=a.color(g[1]),d=w.get("rgb"),J=w.get("a")):(d=g[1],J=1);g=N.createElement("stop").attr({offset:g[0],"stop-color":d,"stop-opacity":J}).add(f);f.stops.push(g)})),T="url("+N.url+"#"+L+")",l.setAttribute(b,T),l.gradient=x,g.toString=function(){return T})},applyTextOutline:function(g){var l=
this.element,N,w,c,e,t;-1!==g.indexOf("contrast")&&(g=g.replace(/contrast/g,this.renderer.getContrast(l.style.fill)));g=g.split(" ");w=g[g.length-1];if((c=g[0])&&"none"!==c&&a.svg){this.fakeTS=!0;g=[].slice.call(l.getElementsByTagName("tspan"));this.ySetter=this.xSetter;c=c.replace(/(^[\d\.]+)(.*?)$/g,function(a,g,l){return 2*g+l});for(t=g.length;t--;)N=g[t],"highcharts-text-outline"===N.getAttribute("class")&&b(g,l.removeChild(N));e=l.firstChild;k(g,function(a,g){0===g&&(a.setAttribute("x",l.getAttribute("x")),
g=l.getAttribute("y"),a.setAttribute("y",g||0),null===g&&l.setAttribute("y",0));a=a.cloneNode(1);h(a,{"class":"highcharts-text-outline",fill:w,stroke:w,"stroke-width":c,"stroke-linejoin":"round"});l.insertBefore(a,e)})}},attr:function(a,g,l,b){var w,N=this.element,c,e=this,k,t;"string"===typeof a&&void 0!==g&&(w=a,a={},a[w]=g);"string"===typeof a?e=(this[a+"Getter"]||this._defaultGetter).call(this,a,N):(I(a,function(g,l){k=!1;b||J(this,l);this.symbolName&&/^(x|y|width|height|r|start|end|innerR|anchorX|anchorY)$/.test(l)&&
(c||(this.symbolAttr(a),c=!0),k=!0);!this.rotation||"x"!==l&&"y"!==l||(this.doTransform=!0);k||(t=this[l+"Setter"]||this._defaultSetter,t.call(this,g,l,N))},this),this.afterSetters());l&&l.call(this);return e},afterSetters:function(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)},addClass:function(a,g){var l=this.attr("class")||"";-1===l.indexOf(a)&&(g||(a=(l+(l?" ":"")+a).replace("  "," ")),this.attr("class",a));return this},hasClass:function(a){return-1!==D(a,(this.attr("class")||
"").split(" "))},removeClass:function(a){return this.attr("class",(this.attr("class")||"").replace(a,""))},symbolAttr:function(a){var l=this;k("x y r start end width height innerR anchorX anchorY".split(" "),function(b){l[b]=g(a[b],l[b])});l.attr({d:l.renderer.symbols[l.symbolName](l.x,l.y,l.width,l.height,l)})},clip:function(a){return this.attr("clip-path",a?"url("+this.renderer.url+"#"+a.id+")":"none")},crisp:function(a,g){var l;g=g||a.strokeWidth||0;l=Math.round(g)%2/2;a.x=Math.floor(a.x||this.x||
0)+l;a.y=Math.floor(a.y||this.y||0)+l;a.width=Math.floor((a.width||this.width||0)-2*l);a.height=Math.floor((a.height||this.height||0)-2*l);n(a.strokeWidth)&&(a.strokeWidth=g);return a},css:function(a){var g=this.styles,l={},b=this.element,c,N="",k,t=!g,f=["textOutline","textOverflow","width"];a&&a.color&&(a.fill=a.color);g&&I(a,function(a,b){a!==g[b]&&(l[b]=a,t=!0)});t&&(g&&(a=e(g,l)),c=this.textWidth=a&&a.width&&"auto"!==a.width&&"text"===b.nodeName.toLowerCase()&&w(a.width),this.styles=a,c&&!P&&
this.renderer.forExport&&delete a.width,b.namespaceURI===this.SVG_NS?(k=function(a,g){return"-"+g.toLowerCase()},I(a,function(a,g){-1===D(g,f)&&(N+=g.replace(/([A-Z])/g,k)+":"+a+";")}),N&&h(b,"style",N)):v(b,a),this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),a&&a.textOutline&&this.applyTextOutline(a.textOutline)));return this},getStyle:function(a){return O.getComputedStyle(this.element||this,"").getPropertyValue(a)},strokeWidth:function(){var a=this.getStyle("stroke-width"),
g;a.indexOf("px")===a.length-2?a=w(a):(g=A.createElementNS(R,"rect"),h(g,{width:a,"stroke-width":0}),this.element.parentNode.appendChild(g),a=g.getBBox().width,g.parentNode.removeChild(g));return a},on:function(a,g){var l=this,b=l.element;q&&"click"===a?(b.ontouchstart=function(a){l.touchEventFired=Date.now();a.preventDefault();g.call(b,a)},b.onclick=function(a){(-1===O.navigator.userAgent.indexOf("Android")||1100<Date.now()-(l.touchEventFired||0))&&g.call(b,a)}):b["on"+a]=g;return this},setRadialReference:function(a){var g=
this.renderer.gradients[this.element.gradient];this.element.radialReference=a;g&&g.radAttr&&g.animate(this.renderer.getRadialAttr(a,g.radAttr));return this},translate:function(a,g){return this.attr({translateX:a,translateY:g})},invert:function(a){this.inverted=a;this.updateTransform();return this},updateTransform:function(){var a=this.translateX||0,l=this.translateY||0,b=this.scaleX,c=this.scaleY,w=this.inverted,e=this.rotation,k=this.matrix,t=this.element;w&&(a+=this.width,l+=this.height);a=["translate("+
a+","+l+")"];n(k)&&a.push("matrix("+k.join(",")+")");w?a.push("rotate(90) scale(-1,1)"):e&&a.push("rotate("+e+" "+g(this.rotationOriginX,t.getAttribute("x"),0)+" "+g(this.rotationOriginY,t.getAttribute("y")||0)+")");(n(b)||n(c))&&a.push("scale("+g(b,1)+" "+g(c,1)+")");a.length&&t.setAttribute("transform",a.join(" "))},toFront:function(){var a=this.element;a.parentNode.appendChild(a);return this},align:function(a,l,c){var w,e,k,N,f={};e=this.renderer;k=e.alignedObjects;var q,u;if(a){if(this.alignOptions=
a,this.alignByTranslate=l,!c||t(c))this.alignTo=w=c||"renderer",b(k,this),k.push(this),c=null}else a=this.alignOptions,l=this.alignByTranslate,w=this.alignTo;c=g(c,e[w],e);w=a.align;e=a.verticalAlign;k=(c.x||0)+(a.x||0);N=(c.y||0)+(a.y||0);"right"===w?q=1:"center"===w&&(q=2);q&&(k+=(c.width-(a.width||0))/q);f[l?"translateX":"x"]=Math.round(k);"bottom"===e?u=1:"middle"===e&&(u=2);u&&(N+=(c.height-(a.height||0))/u);f[l?"translateY":"y"]=Math.round(N);this[this.placed?"animate":"attr"](f);this.placed=
!0;this.alignAttr=f;return this},getBBox:function(a,l){var b,w=this.renderer,t,N=this.element,f=this.styles,q,u=this.textStr,d,J=w.cache,x=w.cacheKeys,L;l=g(l,this.rotation);t=l*c;q=N&&z.prototype.getStyle.call(N,"font-size");n(u)&&(L=u.toString(),-1===L.indexOf("\x3c")&&(L=L.replace(/[0-9]/g,"0")),L+=["",l||0,q,f&&f.width,f&&f.textOverflow].join());L&&!a&&(b=J[L]);if(!b){if(N.namespaceURI===this.SVG_NS||w.forExport){try{(d=this.fakeTS&&function(a){k(N.querySelectorAll(".highcharts-text-outline"),
function(g){g.style.display=a})})&&d("none"),b=N.getBBox?e({},N.getBBox()):{width:N.offsetWidth,height:N.offsetHeight},d&&d("")}catch(V){}if(!b||0>b.width)b={width:0,height:0}}else b=this.htmlGetBBox();w.isSVG&&(a=b.width,w=b.height,f&&"11px"===f.fontSize&&17===Math.round(w)&&(b.height=w=14),l&&(b.width=Math.abs(w*Math.sin(t))+Math.abs(a*Math.cos(t)),b.height=Math.abs(w*Math.cos(t))+Math.abs(a*Math.sin(t))));if(L&&0<b.height){for(;250<x.length;)delete J[x.shift()];J[L]||x.push(L);J[L]=b}}return b},
show:function(a){return this.attr({visibility:a?"inherit":"visible"})},hide:function(){return this.attr({visibility:"hidden"})},fadeOut:function(a){var g=this;g.animate({opacity:0},{duration:a||150,complete:function(){g.attr({y:-9999})}})},add:function(a){var g=this.renderer,l=this.element,b;a&&(this.parentGroup=a);this.parentInverted=a&&a.inverted;void 0!==this.textStr&&g.buildText(this);this.added=!0;if(!a||a.handleZ||this.zIndex)b=this.zIndexSetter();b||(a?a.element:g.box).appendChild(l);if(this.onAdd)this.onAdd();
return this},safeRemoveChild:function(a){var g=a.parentNode;g&&g.removeChild(a)},destroy:function(){var a=this,g=a.element||{},l=a.renderer.isSVG&&"SPAN"===g.nodeName&&a.parentGroup,w=g.ownerSVGElement,c=a.clipPath;g.onclick=g.onmouseout=g.onmouseover=g.onmousemove=g.point=null;J(a);c&&w&&(k(w.querySelectorAll("[clip-path],[CLIP-PATH]"),function(a){var g=a.getAttribute("clip-path"),l=c.element.id;(-1<g.indexOf("(#"+l+")")||-1<g.indexOf('("#'+l+'")'))&&a.removeAttribute("clip-path")}),a.clipPath=c.destroy());
if(a.stops){for(w=0;w<a.stops.length;w++)a.stops[w]=a.stops[w].destroy();a.stops=null}for(a.safeRemoveChild(g);l&&l.div&&0===l.div.childNodes.length;)g=l.parentGroup,a.safeRemoveChild(l.div),delete l.div,l=g;a.alignTo&&b(a.renderer.alignedObjects,a);I(a,function(g,l){delete a[l]});return null},xGetter:function(a){"circle"===this.element.nodeName&&("x"===a?a="cx":"y"===a&&(a="cy"));return this._defaultGetter(a)},_defaultGetter:function(a){a=g(this[a+"Value"],this[a],this.element?this.element.getAttribute(a):
null,0);/^[\-0-9\.]+$/.test(a)&&(a=parseFloat(a));return a},dSetter:function(a,g,l){a&&a.join&&(a=a.join(" "));/(NaN| {2}|^$)/.test(a)&&(a="M 0 0");this[g]!==a&&(l.setAttribute(g,a),this[g]=a)},alignSetter:function(a){this.alignValue=a;this.element.setAttribute("text-anchor",{left:"start",center:"middle",right:"end"}[a])},opacitySetter:function(a,g,l){this[g]=a;l.setAttribute(g,a)},titleSetter:function(a){var l=this.element.getElementsByTagName("title")[0];l||(l=A.createElementNS(this.SVG_NS,"title"),
this.element.appendChild(l));l.firstChild&&l.removeChild(l.firstChild);l.appendChild(A.createTextNode(String(g(a),"").replace(/<[^>]*>/g,"").replace(/&lt;/g,"\x3c").replace(/&gt;/g,"\x3e")))},textSetter:function(a){a!==this.textStr&&(delete this.bBox,this.textStr=a,this.added&&this.renderer.buildText(this))},fillSetter:function(a,g,l){"string"===typeof a?l.setAttribute(g,a):a&&this.colorGradient(a,g,l)},visibilitySetter:function(a,g,l){"inherit"===a?l.removeAttribute(g):this[g]!==a&&l.setAttribute(g,
a);this[g]=a},zIndexSetter:function(a,g){var l=this.renderer,b=this.parentGroup,c=(b||l).element||l.box,e,k=this.element,t,f,l=c===l.box;e=this.added;var q;n(a)&&(k.zIndex=a,a=+a,this[g]===a&&(e=!1),this[g]=a);if(e){(a=this.zIndex)&&b&&(b.handleZ=!0);g=c.childNodes;for(q=g.length-1;0<=q&&!t;q--)if(b=g[q],e=b.zIndex,f=!n(e),b!==k)if(0>a&&f&&!l&&!q)c.insertBefore(k,g[q]),t=!0;else if(w(e)<=a||f&&(!n(a)||0<=a))c.insertBefore(k,g[q+1]||null),t=!0;t||(c.insertBefore(k,g[l?3:0]||null),t=!0)}return t},_defaultSetter:function(a,
g,l){l.setAttribute(g,a)}});z.prototype.yGetter=z.prototype.xGetter;z.prototype.translateXSetter=z.prototype.translateYSetter=z.prototype.rotationSetter=z.prototype.verticalAlignSetter=z.prototype.rotationOriginXSetter=z.prototype.rotationOriginYSetter=z.prototype.scaleXSetter=z.prototype.scaleYSetter=z.prototype.matrixSetter=function(a,g){this[g]=a;this.doTransform=!0};F=a.SVGRenderer=function(){this.init.apply(this,arguments)};e(F.prototype,{Element:z,SVG_NS:R,init:function(a,g,l,b,w,c){var e;b=
this.createElement("svg").attr({version:"1.1","class":"highcharts-root"});e=b.element;a.appendChild(e);h(a,"dir","ltr");-1===a.innerHTML.indexOf("xmlns")&&h(e,"xmlns",this.SVG_NS);this.isSVG=!0;this.box=e;this.boxWrapper=b;this.alignedObjects=[];this.url=(E||x)&&A.getElementsByTagName("base").length?O.location.href.replace(/#.*?$/,"").replace(/<[^>]*>/g,"").replace(/([\('\)])/g,"\\$1").replace(/ /g,"%20"):"";this.createElement("desc").add().element.appendChild(A.createTextNode("Created with Highcharts 6.0.6"));
this.defs=this.createElement("defs").add();this.allowHTML=c;this.forExport=w;this.gradients={};this.cache={};this.cacheKeys=[];this.imgCount=0;this.setSize(g,l,!1);var k;E&&a.getBoundingClientRect&&(g=function(){v(a,{left:0,top:0});k=a.getBoundingClientRect();v(a,{left:Math.ceil(k.left)-k.left+"px",top:Math.ceil(k.top)-k.top+"px"})},g(),this.unSubPixelFix=C(O,"resize",g))},definition:function(a){function g(a,w){var c;k(l(a),function(a){var l=b.createElement(a.tagName),e={};I(a,function(a,g){"tagName"!==
g&&"children"!==g&&"textContent"!==g&&(e[g]=a)});l.attr(e);l.add(w||b.defs);a.textContent&&l.element.appendChild(A.createTextNode(a.textContent));g(a.children||[],l);c=l});return c}var b=this;return g(a)},isHidden:function(){return!this.boxWrapper.getBBox().width},destroy:function(){var a=this.defs;this.box=null;this.boxWrapper=this.boxWrapper.destroy();f(this.gradients||{});this.gradients=null;a&&(this.defs=a.destroy());this.unSubPixelFix&&this.unSubPixelFix();return this.alignedObjects=null},createElement:function(a){var g=
new this.Element;g.init(this,a);return g},draw:B,getRadialAttr:function(a,g){return{cx:a[0]-a[2]/2+g.cx*a[2],cy:a[1]-a[2]/2+g.cy*a[2],r:g.r*a[2]}},getSpanWidth:function(a){return a.getBBox(!0).width},applyEllipsis:function(a,g,l,b){var w=a.rotation,c=l,e,k=0,t=l.length,f=function(a){g.removeChild(g.firstChild);a&&g.appendChild(A.createTextNode(a))},q;a.rotation=0;c=this.getSpanWidth(a,g);if(q=c>b){for(;k<=t;)e=Math.ceil((k+t)/2),c=l.substring(0,e)+"\u2026",f(c),c=this.getSpanWidth(a,g),k===t?k=t+
1:c>b?t=e-1:k=e;0===t&&f("")}a.rotation=w;return q},escapes:{"\x26":"\x26amp;","\x3c":"\x26lt;","\x3e":"\x26gt;","'":"\x26#39;",'"':"\x26quot;"},buildText:function(a){var l=a.element,b=this,c=b.forExport,e=g(a.textStr,"").toString(),t=-1!==e.indexOf("\x3c"),f=l.childNodes,q,n,d,J,L=h(l,"x"),x=a.styles,G=a.textWidth,m=x&&x.lineHeight,B=x&&x.textOutline,N=x&&"ellipsis"===x.textOverflow,y=x&&"nowrap"===x.whiteSpace,E,H=f.length,r=G&&!a.added&&this.box,p=function(a){return m?w(m):b.fontMetrics(void 0,
a.getAttribute("style")?a:l).h},K=function(a,g){I(b.escapes,function(l,b){g&&-1!==D(l,g)||(a=a.toString().replace(new RegExp(l,"g"),b))});return a},x=[e,N,y,m,B,x&&x.fontSize,G].join();if(x!==a.textCache){for(a.textCache=x;H--;)l.removeChild(f[H]);t||B||N||G||-1!==e.indexOf(" ")?(q=/<.*class="([^"]+)".*>/,n=/<.*style="([^"]+)".*>/,d=/<.*href="([^"]+)".*>/,r&&r.appendChild(l),e=t?e.replace(/<(b|strong)>/g,'\x3cspan class\x3d"highcharts-strong"\x3e').replace(/<(i|em)>/g,'\x3cspan class\x3d"highcharts-emphasized"\x3e').replace(/<a/g,
"\x3cspan").replace(/<\/(b|strong|i|em|a)>/g,"\x3c/span\x3e").split(/<br.*?>/g):[e],e=u(e,function(a){return""!==a}),k(e,function(g,w){var e,t=0;g=g.replace(/^\s+|\s+$/g,"").replace(/<span/g,"|||\x3cspan").replace(/<\/span>/g,"\x3c/span\x3e|||");e=g.split("|||");k(e,function(g){if(""!==g||1===e.length){var k={},f=A.createElementNS(b.SVG_NS,"tspan"),u,x;q.test(g)&&(u=g.match(q)[1],h(f,"class",u));n.test(g)&&(x=g.match(n)[1].replace(/(;| |^)color([ :])/,"$1fill$2"),h(f,"style",x));d.test(g)&&!c&&(h(f,
"onclick",'location.href\x3d"'+g.match(d)[1]+'"'),h(f,"class","highcharts-anchor"));g=K(g.replace(/<[a-zA-Z\/](.|\n)*?>/g,"")||" ");if(" "!==g){f.appendChild(A.createTextNode(g));t?k.dx=0:w&&null!==L&&(k.x=L);h(f,k);l.appendChild(f);!t&&E&&(!P&&c&&v(f,{display:"block"}),h(f,"dy",p(f)));if(G){k=g.replace(/([^\^])-/g,"$1- ").split(" ");u=1<e.length||w||1<k.length&&!y;var m=[],B,H=p(f),D=a.rotation;for(N&&(J=b.applyEllipsis(a,f,g,G));!N&&u&&(k.length||m.length);)a.rotation=0,B=b.getSpanWidth(a,f),g=
B>G,void 0===J&&(J=g),g&&1!==k.length?(f.removeChild(f.firstChild),m.unshift(k.pop())):(k=m,m=[],k.length&&!y&&(f=A.createElementNS(R,"tspan"),h(f,{dy:H,x:L}),x&&h(f,"style",x),l.appendChild(f)),B>G&&(G=B)),k.length&&f.appendChild(A.createTextNode(k.join(" ").replace(/- /g,"-")));a.rotation=D}t++}}});E=E||l.childNodes.length}),J&&a.attr("title",K(a.textStr,["\x26lt;","\x26gt;"])),r&&r.removeChild(l),B&&a.applyTextOutline&&a.applyTextOutline(B)):l.appendChild(A.createTextNode(K(e)))}},getContrast:function(a){a=
m(a).rgba;return 510<a[0]+a[1]+a[2]?"#000000":"#FFFFFF"},button:function(a,g,l,b,c,w,e,k,t){var f=this.label(a,g,l,t,null,null,null,null,"button"),q=0;f.attr(G({padding:8,r:2},c));C(f.element,H?"mouseover":"mouseenter",function(){3!==q&&f.setState(1)});C(f.element,H?"mouseout":"mouseleave",function(){3!==q&&f.setState(q)});f.setState=function(a){1!==a&&(f.state=q=a);f.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][a||
0])};return f.on("click",function(a){3!==q&&b.call(f,a)})},crispLine:function(a,g){a[1]===a[4]&&(a[1]=a[4]=Math.round(a[1])-g%2/2);a[2]===a[5]&&(a[2]=a[5]=Math.round(a[2])+g%2/2);return a},path:function(a){var g={};K(a)?g.d=a:y(a)&&e(g,a);return this.createElement("path").attr(g)},circle:function(a,g,l){a=y(a)?a:{x:a,y:g,r:l};g=this.createElement("circle");g.xSetter=g.ySetter=function(a,g,l){l.setAttribute("c"+g,a)};return g.attr(a)},arc:function(a,g,l,b,c,w){y(a)?(b=a,g=b.y,l=b.r,a=b.x):b={innerR:b,
start:c,end:w};a=this.symbol("arc",a,g,l,l,b);a.r=l;return a},rect:function(a,g,l,b,c,w){c=y(a)?a.r:c;w=this.createElement("rect");a=y(a)?a:void 0===a?{}:{x:a,y:g,width:Math.max(l,0),height:Math.max(b,0)};c&&(a.r=c);w.rSetter=function(a,g,l){h(l,{rx:a,ry:a})};return w.attr(a)},setSize:function(a,l,b){var c=this.alignedObjects,w=c.length;this.width=a;this.height=l;for(this.boxWrapper.animate({width:a,height:l},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},
duration:g(b,!0)?void 0:0});w--;)c[w].align()},g:function(a){var g=this.createElement("g");return a?g.attr({"class":"highcharts-"+a}):g},image:function(a,g,l,b,c){var w={preserveAspectRatio:"none"};1<arguments.length&&e(w,{x:g,y:l,width:b,height:c});w=this.createElement("image").attr(w);w.element.setAttributeNS?w.element.setAttributeNS("http://www.w3.org/1999/xlink","href",a):w.element.setAttribute("hc-svg-href",a);return w},symbol:function(a,l,b,w,c,t){var f=this,q,u=/^url\((.*?)\)$/,J=u.test(a),
x=!J&&(this.symbols[a]?a:"circle"),L=x&&this.symbols[x],G=n(l)&&L&&L.call(this.symbols,Math.round(l),Math.round(b),w,c,t),m,h;L?(q=this.path(G),e(q,{symbolName:x,x:l,y:b,width:w,height:c}),t&&e(q,t)):J&&(m=a.match(u)[1],q=this.image(m),q.imgwidth=g(Q[m]&&Q[m].width,t&&t.width),q.imgheight=g(Q[m]&&Q[m].height,t&&t.height),h=function(){q.attr({width:q.width,height:q.height})},k(["width","height"],function(a){q[a+"Setter"]=function(a,g){var l={},b=this["img"+g],w="width"===g?"translateX":"translateY";
this[g]=a;n(b)&&(this.element&&this.element.setAttribute(g,b),this.alignByTranslate||(l[w]=((this[g]||0)-b)/2,this.attr(l)))}}),n(l)&&q.attr({x:l,y:b}),q.isImg=!0,n(q.imgwidth)&&n(q.imgheight)?h():(q.attr({width:0,height:0}),r("img",{onload:function(){var a=d[f.chartIndex];0===this.width&&(v(this,{position:"absolute",top:"-999em"}),A.body.appendChild(this));Q[m]={width:this.width,height:this.height};q.imgwidth=this.width;q.imgheight=this.height;q.element&&h();this.parentNode&&this.parentNode.removeChild(this);
f.imgCount--;if(!f.imgCount&&a&&a.onload)a.onload()},src:m}),this.imgCount++));return q},symbols:{circle:function(a,g,l,b){return this.arc(a+l/2,g+b/2,l/2,b/2,{start:0,end:2*Math.PI,open:!1})},square:function(a,g,l,b){return["M",a,g,"L",a+l,g,a+l,g+b,a,g+b,"Z"]},triangle:function(a,g,l,b){return["M",a+l/2,g,"L",a+l,g+b,a,g+b,"Z"]},"triangle-down":function(a,g,l,b){return["M",a,g,"L",a+l,g,a+l/2,g+b,"Z"]},diamond:function(a,g,l,b){return["M",a+l/2,g,"L",a+l,g+b/2,a+l/2,g+b,a,g+b/2,"Z"]},arc:function(a,
l,b,w,c){var e=c.start,k=c.r||b,t=c.r||w||b,f=c.end-.001;b=c.innerR;w=g(c.open,.001>Math.abs(c.end-c.start-2*Math.PI));var q=Math.cos(e),u=Math.sin(e),d=Math.cos(f),f=Math.sin(f);c=.001>c.end-e-Math.PI?0:1;k=["M",a+k*q,l+t*u,"A",k,t,0,c,1,a+k*d,l+t*f];n(b)&&k.push(w?"M":"L",a+b*d,l+b*f,"A",b,b,0,c,0,a+b*q,l+b*u);k.push(w?"":"Z");return k},callout:function(a,g,l,b,c){var w=Math.min(c&&c.r||0,l,b),e=w+6,k=c&&c.anchorX;c=c&&c.anchorY;var t;t=["M",a+w,g,"L",a+l-w,g,"C",a+l,g,a+l,g,a+l,g+w,"L",a+l,g+b-
w,"C",a+l,g+b,a+l,g+b,a+l-w,g+b,"L",a+w,g+b,"C",a,g+b,a,g+b,a,g+b-w,"L",a,g+w,"C",a,g,a,g,a+w,g];k&&k>l?c>g+e&&c<g+b-e?t.splice(13,3,"L",a+l,c-6,a+l+6,c,a+l,c+6,a+l,g+b-w):t.splice(13,3,"L",a+l,b/2,k,c,a+l,b/2,a+l,g+b-w):k&&0>k?c>g+e&&c<g+b-e?t.splice(33,3,"L",a,c+6,a-6,c,a,c-6,a,g+w):t.splice(33,3,"L",a,b/2,k,c,a,b/2,a,g+w):c&&c>b&&k>a+e&&k<a+l-e?t.splice(23,3,"L",k+6,g+b,k,g+b+6,k-6,g+b,a+w,g+b):c&&0>c&&k>a+e&&k<a+l-e&&t.splice(3,3,"L",k-6,g,k,g-6,k+6,g,l-w,g);return t}},clipRect:function(g,l,b,
c){var w=a.uniqueKey(),e=this.createElement("clipPath").attr({id:w}).add(this.defs);g=this.rect(g,l,b,c,0).add(e);g.id=w;g.clipPath=e;g.count=0;return g},text:function(a,g,l,b){var c={};if(b&&(this.allowHTML||!this.forExport))return this.html(a,g,l);c.x=Math.round(g||0);l&&(c.y=Math.round(l));if(a||0===a)c.text=a;a=this.createElement("text").attr(c);b||(a.xSetter=function(a,g,l){var b=l.getElementsByTagName("tspan"),c,w=l.getAttribute(g),e;for(e=0;e<b.length;e++)c=b[e],c.getAttribute(g)===w&&c.setAttribute(g,
a);l.setAttribute(g,a)});return a},fontMetrics:function(a,g){a=g&&z.prototype.getStyle.call(g,"font-size");a=/px/.test(a)?w(a):/em/.test(a)?parseFloat(a)*(g?this.fontMetrics(null,g.parentNode).f:16):12;g=24>a?a+3:Math.round(1.2*a);return{h:g,b:Math.round(.8*g),f:a}},rotCorr:function(a,g,l){var b=a;g&&l&&(b=Math.max(b*Math.cos(g*c),4));return{x:-a/3*Math.sin(g*c),y:b}},label:function(g,l,b,c,w,t,f,q,u){var d=this,J=d.g("button"!==u&&"label"),x=J.text=d.text("",0,0,f).attr({zIndex:1}),m,h,B=0,y=3,H=
0,E,D,P,A,R,v={},r,I=/^url\((.*?)\)$/.test(c),p=I,K,N,Q,O;u&&J.addClass("highcharts-"+u);p=!0;K=function(){return m.strokeWidth()%2/2};N=function(){var a=x.element.style,g={};h=(void 0===E||void 0===D||R)&&n(x.textStr)&&x.getBBox();J.width=(E||h.width||0)+2*y+H;J.height=(D||h.height||0)+2*y;r=y+d.fontMetrics(a&&a.fontSize,x).b;p&&(m||(J.box=m=d.symbols[c]||I?d.symbol(c):d.rect(),m.addClass(("button"===u?"":"highcharts-label-box")+(u?" highcharts-"+u+"-box":"")),m.add(J),a=K(),g.x=a,g.y=(q?-r:0)+a),
g.width=Math.round(J.width),g.height=Math.round(J.height),m.attr(e(g,v)),v={})};Q=function(){var a=H+y,g;g=q?0:r;n(E)&&h&&("center"===R||"right"===R)&&(a+={center:.5,right:1}[R]*(E-h.width));if(a!==x.x||g!==x.y)x.attr("x",a),void 0!==g&&x.attr("y",g);x.x=a;x.y=g};O=function(a,g){m?m.attr(a,g):v[a]=g};J.onAdd=function(){x.add(J);J.attr({text:g||0===g?g:"",x:l,y:b});m&&n(w)&&J.attr({anchorX:w,anchorY:t})};J.widthSetter=function(g){E=a.isNumber(g)?g:null};J.heightSetter=function(a){D=a};J["text-alignSetter"]=
function(a){R=a};J.paddingSetter=function(a){n(a)&&a!==y&&(y=J.padding=a,Q())};J.paddingLeftSetter=function(a){n(a)&&a!==H&&(H=a,Q())};J.alignSetter=function(a){a={left:0,center:.5,right:1}[a];a!==B&&(B=a,h&&J.attr({x:P}))};J.textSetter=function(a){void 0!==a&&x.textSetter(a);N();Q()};J["stroke-widthSetter"]=function(a,g){a&&(p=!0);this["stroke-width"]=a;O(g,a)};J.rSetter=function(a,g){O(g,a)};J.anchorXSetter=function(a,g){w=J.anchorX=a;O(g,Math.round(a)-K()-P)};J.anchorYSetter=function(a,g){t=J.anchorY=
a;O(g,a-A)};J.xSetter=function(a){J.x=a;B&&(a-=B*((E||h.width)+2*y));P=Math.round(a);J.attr("translateX",P)};J.ySetter=function(a){A=J.y=Math.round(a);J.attr("translateY",A)};var T=J.css;return e(J,{css:function(a){if(a){var g={};a=G(a);k(J.textProps,function(l){void 0!==a[l]&&(g[l]=a[l],delete a[l])});x.css(g)}return T.call(J,a)},getBBox:function(){return{width:h.width+2*y,height:h.height+2*y,x:h.x-y,y:h.y-y}},destroy:function(){L(J.element,"mouseenter");L(J.element,"mouseleave");x&&(x=x.destroy());
m&&(m=m.destroy());z.prototype.destroy.call(J);J=d=N=Q=O=null}})}});a.Renderer=F})(M);(function(a){var z=a.attr,F=a.createElement,C=a.css,p=a.defined,h=a.each,d=a.extend,m=a.isFirefox,v=a.isMS,r=a.isWebKit,n=a.pick,c=a.pInt,f=a.SVGRenderer,A=a.win,k=a.wrap;d(a.SVGElement.prototype,{htmlCss:function(a){var b=this.element;if(b=a&&"SPAN"===b.tagName&&a.width)delete a.width,this.textWidth=b,this.updateTransform();a&&"ellipsis"===a.textOverflow&&(a.whiteSpace="nowrap",a.overflow="hidden");this.styles=
d(this.styles,a);C(this.element,a);return this},htmlGetBBox:function(){var a=this.element;return{x:a.offsetLeft,y:a.offsetTop,width:a.offsetWidth,height:a.offsetHeight}},htmlUpdateTransform:function(){if(this.added){var a=this.renderer,b=this.element,k=this.x||0,f=this.y||0,n=this.textAlign||"left",d={left:0,center:.5,right:1}[n],m=this.styles,H=m&&m.whiteSpace;C(b,{marginLeft:this.translateX||0,marginTop:this.translateY||0});this.inverted&&h(b.childNodes,function(c){a.invertChild(c,b)});if("SPAN"===
b.tagName){var m=this.rotation,y=this.textWidth&&c(this.textWidth),t=[m,n,b.innerHTML,this.textAlign].join(),x;(x=y!==this.oldTextWidth)&&!(x=y>this.oldTextWidth)&&((x=this.textPxLength)||(C(b,{width:"",whiteSpace:H||"nowrap"}),x=b.offsetWidth),x=x>y);x&&/[ \-]/.test(b.textContent||b.innerText)&&(C(b,{width:y+"px",display:"block",whiteSpace:H||"normal"}),this.oldTextWidth=y);t!==this.cTT&&(H=a.fontMetrics(b.style.fontSize).b,p(m)&&m!==(this.oldRotation||0)&&this.setSpanRotation(m,d,H),this.getSpanCorrection(this.textPxLength||
b.offsetWidth,H,d,m,n));C(b,{left:k+(this.xCorr||0)+"px",top:f+(this.yCorr||0)+"px"});this.cTT=t;this.oldRotation=m}}else this.alignOnAdd=!0},setSpanRotation:function(a,b,c){var e={},k=this.renderer.getTransformKey();e[k]=e.transform="rotate("+a+"deg)";e[k+(m?"Origin":"-origin")]=e.transformOrigin=100*b+"% "+c+"px";C(this.element,e)},getSpanCorrection:function(a,b,c){this.xCorr=-a*c;this.yCorr=-b}});d(f.prototype,{getTransformKey:function(){return v&&!/Edge/.test(A.navigator.userAgent)?"-ms-transform":
r?"-webkit-transform":m?"MozTransform":A.opera?"-o-transform":""},html:function(a,b,c){var e=this.createElement("span"),f=e.element,u=e.renderer,m=u.isSVG,H=function(a,b){h(["opacity","visibility"],function(c){k(a,c+"Setter",function(a,c,e,g){a.call(this,c,e,g);b[e]=c})})};e.textSetter=function(a){a!==f.innerHTML&&delete this.bBox;this.textStr=a;f.innerHTML=n(a,"");e.doTransform=!0};m&&H(e,e.element.style);e.xSetter=e.ySetter=e.alignSetter=e.rotationSetter=function(a,b){"align"===b&&(b="textAlign");
e[b]=a;e.doTransform=!0};e.attr({text:a,x:Math.round(b),y:Math.round(c)}).css({position:"absolute"});f.style.whiteSpace="nowrap";e.css=e.htmlCss;e.afterSetters=function(){this.doTransform&&(this.htmlUpdateTransform(),this.doTransform=!1)};m&&(e.add=function(a){var b,c=u.box.parentNode,k=[];if(this.parentGroup=a){if(b=a.div,!b){for(;a;)k.push(a),a=a.parentGroup;h(k.reverse(),function(a){function f(b,l){a[l]=b;"translateX"===l?g.left=b+"px":g.top=b+"px";a.doTransform=!0}var g,w=z(a.element,"class");
w&&(w={className:w});b=a.div=a.div||F("div",w,{position:"absolute",left:(a.translateX||0)+"px",top:(a.translateY||0)+"px",display:a.display,opacity:a.opacity,pointerEvents:a.styles&&a.styles.pointerEvents},b||c);g=b.style;d(a,{classSetter:function(a){return function(g){this.element.setAttribute("class",g);a.className=g}}(b),on:function(){k[0].div&&e.on.apply({element:k[0].div},arguments);return a},translateXSetter:f,translateYSetter:f});H(a,g)})}}else b=c;b.appendChild(f);e.added=!0;e.alignOnAdd&&
e.htmlUpdateTransform();return e});return e}})})(M);(function(a){var z=a.defined,F=a.each,C=a.extend,p=a.merge,h=a.pick,d=a.timeUnits,m=a.win;a.Time=function(a){this.update(a,!1)};a.Time.prototype={defaultOptions:{},update:function(d){var r=h(d&&d.useUTC,!0),n=this;this.options=d=p(!0,this.options||{},d);this.Date=d.Date||m.Date;this.timezoneOffset=(this.useUTC=r)&&d.timezoneOffset;this.getTimezoneOffset=this.timezoneOffsetFunction();(this.variableTimezone=!(r&&!d.getTimezoneOffset&&!d.timezone))||
this.timezoneOffset?(this.get=function(a,f){var c=f.getTime(),k=c-n.getTimezoneOffset(f);f.setTime(k);a=f["getUTC"+a]();f.setTime(c);return a},this.set=function(c,f,d){var k;if(-1!==a.inArray(c,["Milliseconds","Seconds","Minutes"]))f["set"+c](d);else k=n.getTimezoneOffset(f),k=f.getTime()-k,f.setTime(k),f["setUTC"+c](d),c=n.getTimezoneOffset(f),k=f.getTime()+c,f.setTime(k)}):r?(this.get=function(a,f){return f["getUTC"+a]()},this.set=function(a,f,d){return f["setUTC"+a](d)}):(this.get=function(a,f){return f["get"+
a]()},this.set=function(a,f,d){return f["set"+a](d)})},makeTime:function(d,m,n,c,f,A){var k,e,b;this.useUTC?(k=this.Date.UTC.apply(0,arguments),e=this.getTimezoneOffset(k),k+=e,b=this.getTimezoneOffset(k),e!==b?k+=b-e:e-36E5!==this.getTimezoneOffset(k-36E5)||a.isSafari||(k-=36E5)):k=(new this.Date(d,m,h(n,1),h(c,0),h(f,0),h(A,0))).getTime();return k},timezoneOffsetFunction:function(){var d=this,h=this.options,n=m.moment;if(!this.useUTC)return function(a){return 6E4*(new Date(a)).getTimezoneOffset()};
if(h.timezone){if(n)return function(a){return 6E4*-n.tz(a,h.timezone).utcOffset()};a.error(25)}return this.useUTC&&h.getTimezoneOffset?function(a){return 6E4*h.getTimezoneOffset(a)}:function(){return 6E4*(d.timezoneOffset||0)}},dateFormat:function(d,m,n){if(!a.defined(m)||isNaN(m))return a.defaultOptions.lang.invalidDate||"";d=a.pick(d,"%Y-%m-%d %H:%M:%S");var c=this,f=new this.Date(m),h=this.get("Hours",f),k=this.get("Day",f),e=this.get("Date",f),b=this.get("Month",f),u=this.get("FullYear",f),q=
a.defaultOptions.lang,D=q.weekdays,r=q.shortWeekdays,E=a.pad,f=a.extend({a:r?r[k]:D[k].substr(0,3),A:D[k],d:E(e),e:E(e,2," "),w:k,b:q.shortMonths[b],B:q.months[b],m:E(b+1),y:u.toString().substr(2,2),Y:u,H:E(h),k:h,I:E(h%12||12),l:h%12||12,M:E(c.get("Minutes",f)),p:12>h?"AM":"PM",P:12>h?"am":"pm",S:E(f.getSeconds()),L:E(Math.round(m%1E3),3)},a.dateFormats);a.objectEach(f,function(a,b){for(;-1!==d.indexOf("%"+b);)d=d.replace("%"+b,"function"===typeof a?a.call(c,m):a)});return n?d.substr(0,1).toUpperCase()+
d.substr(1):d},getTimeTicks:function(a,m,n,c){var f=this,A=[],k={},e,b=new f.Date(m),u=a.unitRange,q=a.count||1,D;if(z(m)){f.set("Milliseconds",b,u>=d.second?0:q*Math.floor(f.get("Milliseconds",b)/q));u>=d.second&&f.set("Seconds",b,u>=d.minute?0:q*Math.floor(f.get("Seconds",b)/q));u>=d.minute&&f.set("Minutes",b,u>=d.hour?0:q*Math.floor(f.get("Minutes",b)/q));u>=d.hour&&f.set("Hours",b,u>=d.day?0:q*Math.floor(f.get("Hours",b)/q));u>=d.day&&f.set("Date",b,u>=d.month?1:q*Math.floor(f.get("Date",b)/q));
u>=d.month&&(f.set("Month",b,u>=d.year?0:q*Math.floor(f.get("Month",b)/q)),e=f.get("FullYear",b));u>=d.year&&f.set("FullYear",b,e-e%q);u===d.week&&f.set("Date",b,f.get("Date",b)-f.get("Day",b)+h(c,1));e=f.get("FullYear",b);c=f.get("Month",b);var r=f.get("Date",b),E=f.get("Hours",b);m=b.getTime();f.variableTimezone&&(D=n-m>4*d.month||f.getTimezoneOffset(m)!==f.getTimezoneOffset(n));b=b.getTime();for(m=1;b<n;)A.push(b),b=u===d.year?f.makeTime(e+m*q,0):u===d.month?f.makeTime(e,c+m*q):!D||u!==d.day&&
u!==d.week?D&&u===d.hour&&1<q?f.makeTime(e,c,r,E+m*q):b+u*q:f.makeTime(e,c,r+m*q*(u===d.day?1:7)),m++;A.push(b);u<=d.hour&&1E4>A.length&&F(A,function(a){0===a%18E5&&"000000000"===f.dateFormat("%H%M%S%L",a)&&(k[a]="day")})}A.info=C(a,{higherRanks:k,totalRange:u*q});return A}}})(M);(function(a){var z=a.merge;a.defaultOptions={symbols:["circle","diamond","square","triangle","triangle-down"],lang:{loading:"Loading...",months:"January February March April May June July August September October November December".split(" "),
shortMonths:"Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),weekdays:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),decimalPoint:".",numericSymbols:"kMGTPE".split(""),resetZoom:"Reset zoom",resetZoomTitle:"Reset zoom level 1:1",thousandsSep:" "},global:{},time:a.Time.prototype.defaultOptions,chart:{borderRadius:0,colorCount:10,defaultSeriesType:"line",ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}},
width:null,height:null},title:{text:"Chart title",align:"center",margin:15,widthAdjust:-44},subtitle:{text:"",align:"center",widthAdjust:-44},plotOptions:{},labels:{style:{position:"absolute",color:"#333333"}},legend:{enabled:!0,align:"center",layout:"horizontal",labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{},itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{}},loading:{},
tooltip:{enabled:!0,animation:a.svg,borderRadius:3,dateTimeLabelFormats:{millisecond:"%A, %b %e, %H:%M:%S.%L",second:"%A, %b %e, %H:%M:%S",minute:"%A, %b %e, %H:%M",hour:"%A, %b %e, %H:%M",day:"%A, %b %e, %Y",week:"Week from %A, %b %e, %Y",month:"%B %Y",year:"%Y"},footerFormat:"",padding:8,snap:a.isTouchDevice?25:10,headerFormat:'\x3cspan class\x3d"highcharts-header"\x3e{point.key}\x3c/span\x3e\x3cbr/\x3e',pointFormat:'\x3cspan class\x3d"highcharts-color-{point.colorIndex}"\x3e\u25cf\x3c/span\x3e {series.name}: \x3cspan class\x3d"highcharts-strong"\x3e{point.y}\x3c/span\x3e\x3cbr/\x3e'},
credits:{enabled:!0,href:"http://www.highcharts.com",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},text:"Highcharts.com"}};a.setOptions=function(F){a.defaultOptions=z(!0,a.defaultOptions,F);a.time.update(z(a.defaultOptions.global,a.defaultOptions.time),!1);return a.defaultOptions};a.getOptions=function(){return a.defaultOptions};a.defaultPlotOptions=a.defaultOptions.plotOptions;a.time=new a.Time(z(a.defaultOptions.global,a.defaultOptions.time));a.dateFormat=function(z,C,p){return a.time.dateFormat(z,
C,p)}})(M);(function(a){var z=a.correctFloat,F=a.defined,C=a.destroyObjectProperties,p=a.isNumber,h=a.pick,d=a.deg2rad;a.Tick=function(a,d,h,n){this.axis=a;this.pos=d;this.type=h||"";this.isNewLabel=this.isNew=!0;h||n||this.addLabel()};a.Tick.prototype={addLabel:function(){var a=this.axis,d=a.options,r=a.chart,n=a.categories,c=a.names,f=this.pos,A=d.labels,k=a.tickPositions,e=f===k[0],b=f===k[k.length-1],c=n?h(n[f],c[f],f):f,n=this.label,k=k.info,u;a.isDatetimeAxis&&k&&(u=d.dateTimeLabelFormats[k.higherRanks[f]||
k.unitName]);this.isFirst=e;this.isLast=b;d=a.labelFormatter.call({axis:a,chart:r,isFirst:e,isLast:b,dateTimeLabelFormat:u,value:a.isLog?z(a.lin2log(c)):c,pos:f});if(F(n))n&&n.attr({text:d});else{if(this.label=n=F(d)&&A.enabled?r.renderer.text(d,0,0,A.useHTML).add(a.labelGroup):null)n.textPxLength=n.getBBox().width;this.rotation=0}},getLabelSize:function(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0},handleOverflow:function(a){var m=this.axis,r=m.options.labels,n=a.x,
c=m.chart.chartWidth,f=m.chart.spacing,A=h(m.labelLeft,Math.min(m.pos,f[3])),f=h(m.labelRight,Math.max(m.isRadial?0:m.pos+m.len,c-f[1])),k=this.label,e=this.rotation,b={left:0,center:.5,right:1}[m.labelAlign||k.attr("align")],u=k.getBBox().width,q=m.getSlotWidth(),D=q,p=1,E,H={};if(e||!1===r.overflow)0>e&&n-b*u<A?E=Math.round(n/Math.cos(e*d)-A):0<e&&n+b*u>f&&(E=Math.round((c-n)/Math.cos(e*d)));else if(c=n+(1-b)*u,n-b*u<A?D=a.x+D*(1-b)-A:c>f&&(D=f-a.x+D*b,p=-1),D=Math.min(q,D),D<q&&"center"===m.labelAlign&&
(a.x+=p*(q-D-b*(q-Math.min(u,D)))),u>D||m.autoRotation&&(k.styles||{}).width)E=D;E&&(H.width=E,(r.style||{}).textOverflow||(H.textOverflow="ellipsis"),k.css(H))},getPosition:function(a,d,h,n){var c=this.axis,f=c.chart,m=n&&f.oldChartHeight||f.chartHeight;return{x:a?c.translate(d+h,null,null,n)+c.transB:c.left+c.offset+(c.opposite?(n&&f.oldChartWidth||f.chartWidth)-c.right-c.left:0),y:a?m-c.bottom+c.offset-(c.opposite?c.height:0):m-c.translate(d+h,null,null,n)-c.transB}},getLabelPosition:function(a,
h,r,n,c,f,A,k){var e=this.axis,b=e.transA,u=e.reversed,q=e.staggerLines,m=e.tickRotCorr||{x:0,y:0},p=c.y,E=n||e.reserveSpaceDefault?0:-e.labelOffset*("center"===e.labelAlign?.5:1);F(p)||(p=0===e.side?r.rotation?-8:-r.getBBox().height:2===e.side?m.y+8:Math.cos(r.rotation*d)*(m.y-r.getBBox(!1,0).height/2));a=a+c.x+E+m.x-(f&&n?f*b*(u?-1:1):0);h=h+p-(f&&!n?f*b*(u?1:-1):0);q&&(r=A/(k||1)%q,e.opposite&&(r=q-r-1),h+=e.labelOffset/q*r);return{x:a,y:Math.round(h)}},getMarkPath:function(a,d,h,n,c,f){return f.crispLine(["M",
a,d,"L",a+(c?0:-h),d+(c?h:0)],n)},renderGridLine:function(a,d,h){var n=this.axis,c=this.gridLine,f={},m=this.pos,k=this.type,e=n.tickmarkOffset,b=n.chart.renderer;c||(k||(f.zIndex=1),a&&(f.opacity=0),this.gridLine=c=b.path().attr(f).addClass("highcharts-"+(k?k+"-":"")+"grid-line").add(n.gridGroup));if(!a&&c&&(a=n.getPlotLinePath(m+e,c.strokeWidth()*h,a,!0)))c[this.isNew?"attr":"animate"]({d:a,opacity:d})},renderMark:function(a,d,h){var n=this.axis,c=n.chart.renderer,f=this.type,m=n.tickSize(f?f+"Tick":
"tick"),k=this.mark,e=!k,b=a.x;a=a.y;m&&(n.opposite&&(m[0]=-m[0]),e&&(this.mark=k=c.path().addClass("highcharts-"+(f?f+"-":"")+"tick").add(n.axisGroup)),k[e?"attr":"animate"]({d:this.getMarkPath(b,a,m[0],k.strokeWidth()*h,n.horiz,c),opacity:d}))},renderLabel:function(a,d,r,n){var c=this.axis,f=c.horiz,m=c.options,k=this.label,e=m.labels,b=e.step,c=c.tickmarkOffset,u=!0,q=a.x;a=a.y;k&&p(q)&&(k.xy=a=this.getLabelPosition(q,a,k,f,e,c,n,b),this.isFirst&&!this.isLast&&!h(m.showFirstLabel,1)||this.isLast&&
!this.isFirst&&!h(m.showLastLabel,1)?u=!1:!f||e.step||e.rotation||d||0===r||this.handleOverflow(a),b&&n%b&&(u=!1),u&&p(a.y)?(a.opacity=r,k[this.isNewLabel?"attr":"animate"](a),this.isNewLabel=!1):(k.attr("y",-9999),this.isNewLabel=!0))},render:function(a,d,r){var n=this.axis,c=n.horiz,f=this.getPosition(c,this.pos,n.tickmarkOffset,d),m=f.x,k=f.y,n=c&&m===n.pos+n.len||!c&&k===n.pos?-1:1;r=h(r,1);this.isActive=!0;this.renderGridLine(d,r,n);this.renderMark(f,r,n);this.renderLabel(f,d,r,a);this.isNew=
!1},destroy:function(){C(this,this.axis)}}})(M);var U=function(a){var z=a.addEvent,F=a.animObject,C=a.arrayMax,p=a.arrayMin,h=a.correctFloat,d=a.defaultOptions,m=a.defined,v=a.deg2rad,r=a.destroyObjectProperties,n=a.each,c=a.extend,f=a.fireEvent,A=a.format,k=a.getMagnitude,e=a.grep,b=a.inArray,u=a.isArray,q=a.isNumber,D=a.isString,K=a.merge,E=a.normalizeTickInterval,H=a.objectEach,y=a.pick,t=a.removeEvent,x=a.splat,G=a.syncTimeout,B=a.Tick,I=function(){this.init.apply(this,arguments)};a.extend(I.prototype,
{defaultOptions:{dateTimeLabelFormats:{millisecond:"%H:%M:%S.%L",second:"%H:%M:%S",minute:"%H:%M",hour:"%H:%M",day:"%e. %b",week:"%e. %b",month:"%b '%y",year:"%Y"},endOnTick:!1,labels:{enabled:!0,x:0},maxPadding:.01,minorTickLength:2,minorTickPosition:"outside",minPadding:.01,startOfWeek:1,startOnTick:!1,tickLength:10,tickmarkPlacement:"between",tickPixelInterval:100,tickPosition:"outside",title:{align:"middle"},type:"linear"},defaultYAxisOptions:{endOnTick:!0,tickPixelInterval:72,showLastLabel:!0,
labels:{x:-8},maxPadding:.05,minPadding:.05,startOnTick:!0,title:{rotation:270,text:"Values"},stackLabels:{allowOverlap:!1,enabled:!1,formatter:function(){return a.numberFormat(this.total,-1)}}},defaultLeftAxisOptions:{labels:{x:-15},title:{rotation:270}},defaultRightAxisOptions:{labels:{x:15},title:{rotation:90}},defaultBottomAxisOptions:{labels:{autoRotation:[-45],x:0},title:{rotation:0}},defaultTopAxisOptions:{labels:{autoRotation:[-45],x:0},title:{rotation:0}},init:function(a,c){var g=c.isX,l=
this;l.chart=a;l.horiz=a.inverted&&!l.isZAxis?!g:g;l.isXAxis=g;l.coll=l.coll||(g?"xAxis":"yAxis");l.opposite=c.opposite;l.side=c.side||(l.horiz?l.opposite?0:2:l.opposite?1:3);l.setOptions(c);var w=this.options,e=w.type;l.labelFormatter=w.labels.formatter||l.defaultLabelFormatter;l.userOptions=c;l.minPixelPadding=0;l.reversed=w.reversed;l.visible=!1!==w.visible;l.zoomEnabled=!1!==w.zoomEnabled;l.hasNames="category"===e||!0===w.categories;l.categories=w.categories||l.hasNames;l.names=l.names||[];l.plotLinesAndBandsGroups=
{};l.isLog="logarithmic"===e;l.isDatetimeAxis="datetime"===e;l.positiveValuesOnly=l.isLog&&!l.allowNegativeLog;l.isLinked=m(w.linkedTo);l.ticks={};l.labelEdge=[];l.minorTicks={};l.plotLinesAndBands=[];l.alternateBands={};l.len=0;l.minRange=l.userMinRange=w.minRange||w.maxZoom;l.range=w.range;l.offset=w.offset||0;l.stacks={};l.oldStacks={};l.stacksTouched=0;l.max=null;l.min=null;l.crosshair=y(w.crosshair,x(a.options.tooltip.crosshairs)[g?0:1],!1);c=l.options.events;-1===b(l,a.axes)&&(g?a.axes.splice(a.xAxis.length,
0,l):a.axes.push(l),a[l.coll].push(l));l.series=l.series||[];a.inverted&&!l.isZAxis&&g&&void 0===l.reversed&&(l.reversed=!0);H(c,function(a,g){z(l,g,a)});l.lin2log=w.linearToLogConverter||l.lin2log;l.isLog&&(l.val2lin=l.log2lin,l.lin2val=l.lin2log)},setOptions:function(a){this.options=K(this.defaultOptions,"yAxis"===this.coll&&this.defaultYAxisOptions,[this.defaultTopAxisOptions,this.defaultRightAxisOptions,this.defaultBottomAxisOptions,this.defaultLeftAxisOptions][this.side],K(d[this.coll],a))},
defaultLabelFormatter:function(){var g=this.axis,b=this.value,c=g.chart.time,l=g.categories,e=this.dateTimeLabelFormat,k=d.lang,f=k.numericSymbols,k=k.numericSymbolMagnitude||1E3,t=f&&f.length,q,u=g.options.labels.format,g=g.isLog?Math.abs(b):g.tickInterval;if(u)q=A(u,this,c);else if(l)q=b;else if(e)q=c.dateFormat(e,b);else if(t&&1E3<=g)for(;t--&&void 0===q;)c=Math.pow(k,t+1),g>=c&&0===10*b%c&&null!==f[t]&&0!==b&&(q=a.numberFormat(b/c,-1)+f[t]);void 0===q&&(q=1E4<=Math.abs(b)?a.numberFormat(b,-1):
a.numberFormat(b,-1,void 0,""));return q},getSeriesExtremes:function(){var a=this,b=a.chart;a.hasVisibleSeries=!1;a.dataMin=a.dataMax=a.threshold=null;a.softThreshold=!a.isXAxis;a.buildStacks&&a.buildStacks();n(a.series,function(g){if(g.visible||!b.options.chart.ignoreHiddenSeries){var l=g.options,c=l.threshold,w;a.hasVisibleSeries=!0;a.positiveValuesOnly&&0>=c&&(c=null);if(a.isXAxis)l=g.xData,l.length&&(g=p(l),w=C(l),q(g)||g instanceof Date||(l=e(l,q),g=p(l),w=C(l)),l.length&&(a.dataMin=Math.min(y(a.dataMin,
l[0],g),g),a.dataMax=Math.max(y(a.dataMax,l[0],w),w)));else if(g.getExtremes(),w=g.dataMax,g=g.dataMin,m(g)&&m(w)&&(a.dataMin=Math.min(y(a.dataMin,g),g),a.dataMax=Math.max(y(a.dataMax,w),w)),m(c)&&(a.threshold=c),!l.softThreshold||a.positiveValuesOnly)a.softThreshold=!1}})},translate:function(a,b,c,l,e,k){var g=this.linkedParent||this,w=1,f=0,t=l?g.oldTransA:g.transA;l=l?g.oldMin:g.min;var d=g.minPixelPadding;e=(g.isOrdinal||g.isBroken||g.isLog&&e)&&g.lin2val;t||(t=g.transA);c&&(w*=-1,f=g.len);g.reversed&&
(w*=-1,f-=w*(g.sector||g.len));b?(a=(a*w+f-d)/t+l,e&&(a=g.lin2val(a))):(e&&(a=g.val2lin(a)),a=q(l)?w*(a-l)*t+f+w*d+(q(k)?t*k:0):void 0);return a},toPixels:function(a,b){return this.translate(a,!1,!this.horiz,null,!0)+(b?0:this.pos)},toValue:function(a,b){return this.translate(a-(b?0:this.pos),!0,!this.horiz,null,!0)},getPlotLinePath:function(a,b,c,l,e){var g=this.chart,w=this.left,k=this.top,f,t,d=c&&g.oldChartHeight||g.chartHeight,u=c&&g.oldChartWidth||g.chartWidth,n;f=this.transB;var x=function(a,
g,b){if(a<g||a>b)l?a=Math.min(Math.max(g,a),b):n=!0;return a};e=y(e,this.translate(a,null,null,c));e=Math.min(Math.max(-1E5,e),1E5);a=c=Math.round(e+f);f=t=Math.round(d-e-f);q(e)?this.horiz?(f=k,t=d-this.bottom,a=c=x(a,w,w+this.width)):(a=w,c=u-this.right,f=t=x(f,k,k+this.height)):(n=!0,l=!1);return n&&!l?null:g.renderer.crispLine(["M",a,f,"L",c,t],b||1)},getLinearTickPositions:function(a,b,c){var g,w=h(Math.floor(b/a)*a);c=h(Math.ceil(c/a)*a);var e=[],k;h(w+a)===w&&(k=20);if(this.single)return[b];
for(b=w;b<=c;){e.push(b);b=h(b+a,k);if(b===g)break;g=b}return e},getMinorTickInterval:function(){var a=this.options;return!0===a.minorTicks?y(a.minorTickInterval,"auto"):!1===a.minorTicks?null:a.minorTickInterval},getMinorTickPositions:function(){var a=this,b=a.options,c=a.tickPositions,l=a.minorTickInterval,e=[],k=a.pointRangePadding||0,f=a.min-k,k=a.max+k,t=k-f;if(t&&t/l<a.len/3)if(a.isLog)n(this.paddedTicks,function(g,b,c){b&&e.push.apply(e,a.getLogTickPositions(l,c[b-1],c[b],!0))});else if(a.isDatetimeAxis&&
"auto"===this.getMinorTickInterval())e=e.concat(a.getTimeTicks(a.normalizeTimeTickInterval(l),f,k,b.startOfWeek));else for(b=f+(c[0]-f)%l;b<=k&&b!==e[0];b+=l)e.push(b);0!==e.length&&a.trimTicks(e);return e},adjustForMinRange:function(){var a=this.options,b=this.min,c=this.max,l,e,k,f,t,q,d,u;this.isXAxis&&void 0===this.minRange&&!this.isLog&&(m(a.min)||m(a.max)?this.minRange=null:(n(this.series,function(a){q=a.xData;for(f=d=a.xIncrement?1:q.length-1;0<f;f--)if(t=q[f]-q[f-1],void 0===k||t<k)k=t}),
this.minRange=Math.min(5*k,this.dataMax-this.dataMin)));c-b<this.minRange&&(e=this.dataMax-this.dataMin>=this.minRange,u=this.minRange,l=(u-c+b)/2,l=[b-l,y(a.min,b-l)],e&&(l[2]=this.isLog?this.log2lin(this.dataMin):this.dataMin),b=C(l),c=[b+u,y(a.max,b+u)],e&&(c[2]=this.isLog?this.log2lin(this.dataMax):this.dataMax),c=p(c),c-b<u&&(l[0]=c-u,l[1]=y(a.min,c-u),b=C(l)));this.min=b;this.max=c},getClosest:function(){var a;this.categories?a=1:n(this.series,function(g){var b=g.closestPointRange,l=g.visible||
!g.chart.options.chart.ignoreHiddenSeries;!g.noSharedTooltip&&m(b)&&l&&(a=m(a)?Math.min(a,b):b)});return a},nameToX:function(a){var g=u(this.categories),c=g?this.categories:this.names,l=a.options.x,e;a.series.requireSorting=!1;m(l)||(l=!1===this.options.uniqueNames?a.series.autoIncrement():g?b(a.name,c):y(c["s"+a.name],-1));-1===l?g||(e=c.length):e=l;void 0!==e&&(this.names[e]=a.name,this.names["s"+a.name]=e);return e},updateNames:function(){var a=this,b=this.names,c=b.length;if(0<c){for(;c--;)delete b["s"+
b[c]];b.length=0;this.minRange=this.userMinRange;n(this.series||[],function(g){g.xIncrement=null;if(!g.points||g.isDirtyData)g.processData(),g.generatePoints();n(g.points,function(b,l){var c;b.options&&(c=a.nameToX(b),void 0!==c&&c!==b.x&&(b.x=c,g.xData[l]=c))})})}},setAxisTranslation:function(a){var g=this,b=g.max-g.min,l=g.axisPointRange||0,c,e=0,k=0,f=g.linkedParent,t=!!g.categories,q=g.transA,d=g.isXAxis;if(d||t||l)c=g.getClosest(),f?(e=f.minPointOffset,k=f.pointRangePadding):n(g.series,function(a){var b=
t?1:d?y(a.options.pointRange,c,0):g.axisPointRange||0;a=a.options.pointPlacement;l=Math.max(l,b);g.single||(e=Math.max(e,D(a)?0:b/2),k=Math.max(k,"on"===a?0:b))}),f=g.ordinalSlope&&c?g.ordinalSlope/c:1,g.minPointOffset=e*=f,g.pointRangePadding=k*=f,g.pointRange=Math.min(l,b),d&&(g.closestPointRange=c);a&&(g.oldTransA=q);g.translationSlope=g.transA=q=g.options.staticScale||g.len/(b+k||1);g.transB=g.horiz?g.left:g.bottom;g.minPixelPadding=q*e},minFromRange:function(){return this.max-this.range},setTickInterval:function(g){var b=
this,c=b.chart,l=b.options,e=b.isLog,t=b.log2lin,d=b.isDatetimeAxis,u=b.isXAxis,x=b.isLinked,G=l.maxPadding,B=l.minPadding,H=l.tickInterval,D=l.tickPixelInterval,A=b.categories,I=b.threshold,r=b.softThreshold,p,v,K,z;d||A||x||this.getTickAmount();K=y(b.userMin,l.min);z=y(b.userMax,l.max);x?(b.linkedParent=c[b.coll][l.linkedTo],c=b.linkedParent.getExtremes(),b.min=y(c.min,c.dataMin),b.max=y(c.max,c.dataMax),l.type!==b.linkedParent.options.type&&a.error(11,1)):(!r&&m(I)&&(b.dataMin>=I?(p=I,B=0):b.dataMax<=
I&&(v=I,G=0)),b.min=y(K,p,b.dataMin),b.max=y(z,v,b.dataMax));e&&(b.positiveValuesOnly&&!g&&0>=Math.min(b.min,y(b.dataMin,b.min))&&a.error(10,1),b.min=h(t(b.min),15),b.max=h(t(b.max),15));b.range&&m(b.max)&&(b.userMin=b.min=K=Math.max(b.dataMin,b.minFromRange()),b.userMax=z=b.max,b.range=null);f(b,"foundExtremes");b.beforePadding&&b.beforePadding();b.adjustForMinRange();!(A||b.axisPointRange||b.usePercentage||x)&&m(b.min)&&m(b.max)&&(t=b.max-b.min)&&(!m(K)&&B&&(b.min-=t*B),!m(z)&&G&&(b.max+=t*G));
q(l.softMin)&&!q(b.userMin)&&(b.min=Math.min(b.min,l.softMin));q(l.softMax)&&!q(b.userMax)&&(b.max=Math.max(b.max,l.softMax));q(l.floor)&&(b.min=Math.max(b.min,l.floor));q(l.ceiling)&&(b.max=Math.min(b.max,l.ceiling));r&&m(b.dataMin)&&(I=I||0,!m(K)&&b.min<I&&b.dataMin>=I?b.min=I:!m(z)&&b.max>I&&b.dataMax<=I&&(b.max=I));b.tickInterval=b.min===b.max||void 0===b.min||void 0===b.max?1:x&&!H&&D===b.linkedParent.options.tickPixelInterval?H=b.linkedParent.tickInterval:y(H,this.tickAmount?(b.max-b.min)/Math.max(this.tickAmount-
1,1):void 0,A?1:(b.max-b.min)*D/Math.max(b.len,D));u&&!g&&n(b.series,function(a){a.processData(b.min!==b.oldMin||b.max!==b.oldMax)});b.setAxisTranslation(!0);b.beforeSetTickPositions&&b.beforeSetTickPositions();b.postProcessTickInterval&&(b.tickInterval=b.postProcessTickInterval(b.tickInterval));b.pointRange&&!H&&(b.tickInterval=Math.max(b.pointRange,b.tickInterval));g=y(l.minTickInterval,b.isDatetimeAxis&&b.closestPointRange);!H&&b.tickInterval<g&&(b.tickInterval=g);d||e||H||(b.tickInterval=E(b.tickInterval,
null,k(b.tickInterval),y(l.allowDecimals,!(.5<b.tickInterval&&5>b.tickInterval&&1E3<b.max&&9999>b.max)),!!this.tickAmount));this.tickAmount||(b.tickInterval=b.unsquish());this.setTickPositions()},setTickPositions:function(){var a=this.options,b,c=a.tickPositions;b=this.getMinorTickInterval();var l=a.tickPositioner,e=a.startOnTick,k=a.endOnTick;this.tickmarkOffset=this.categories&&"between"===a.tickmarkPlacement&&1===this.tickInterval?.5:0;this.minorTickInterval="auto"===b&&this.tickInterval?this.tickInterval/
5:b;this.single=this.min===this.max&&m(this.min)&&!this.tickAmount&&(parseInt(this.min,10)===this.min||!1!==a.allowDecimals);this.tickPositions=b=c&&c.slice();!b&&(b=this.isDatetimeAxis?this.getTimeTicks(this.normalizeTimeTickInterval(this.tickInterval,a.units),this.min,this.max,a.startOfWeek,this.ordinalPositions,this.closestPointRange,!0):this.isLog?this.getLogTickPositions(this.tickInterval,this.min,this.max):this.getLinearTickPositions(this.tickInterval,this.min,this.max),b.length>this.len&&(b=
[b[0],b.pop()],b[0]===b[1]&&(b.length=1)),this.tickPositions=b,l&&(l=l.apply(this,[this.min,this.max])))&&(this.tickPositions=b=l);this.paddedTicks=b.slice(0);this.trimTicks(b,e,k);this.isLinked||(this.single&&2>b.length&&(this.min-=.5,this.max+=.5),c||l||this.adjustTickAmount())},trimTicks:function(a,b,c){var g=a[0],e=a[a.length-1],k=this.minPointOffset||0;if(!this.isLinked){if(b&&-Infinity!==g)this.min=g;else for(;this.min-k>a[0];)a.shift();if(c)this.max=e;else for(;this.max+k<a[a.length-1];)a.pop();
0===a.length&&m(g)&&!this.options.tickPositions&&a.push((e+g)/2)}},alignToOthers:function(){var a={},b,c=this.options;!1===this.chart.options.chart.alignTicks||!1===c.alignTicks||this.isLog||n(this.chart[this.coll],function(g){var l=g.options,l=[g.horiz?l.left:l.top,l.width,l.height,l.pane].join();g.series.length&&(a[l]?b=!0:a[l]=1)});return b},getTickAmount:function(){var a=this.options,b=a.tickAmount,c=a.tickPixelInterval;!m(a.tickInterval)&&this.len<c&&!this.isRadial&&!this.isLog&&a.startOnTick&&
a.endOnTick&&(b=2);!b&&this.alignToOthers()&&(b=Math.ceil(this.len/c)+1);4>b&&(this.finalTickAmt=b,b=5);this.tickAmount=b},adjustTickAmount:function(){var a=this.tickInterval,b=this.tickPositions,c=this.tickAmount,l=this.finalTickAmt,e=b&&b.length,k=y(this.threshold,this.softThreshold?0:null);if(this.hasData()){if(e<c){for(;b.length<c;)b.length%2||this.min===k?b.push(h(b[b.length-1]+a)):b.unshift(h(b[0]-a));this.transA*=(e-1)/(c-1);this.min=b[0];this.max=b[b.length-1]}else e>c&&(this.tickInterval*=
2,this.setTickPositions());if(m(l)){for(a=c=b.length;a--;)(3===l&&1===a%2||2>=l&&0<a&&a<c-1)&&b.splice(a,1);this.finalTickAmt=void 0}}},setScale:function(){var a,b;this.oldMin=this.min;this.oldMax=this.max;this.oldAxisLength=this.len;this.setAxisSize();b=this.len!==this.oldAxisLength;n(this.series,function(b){if(b.isDirtyData||b.isDirty||b.xAxis.isDirty)a=!0});b||a||this.isLinked||this.forceRedraw||this.userMin!==this.oldUserMin||this.userMax!==this.oldUserMax||this.alignToOthers()?(this.resetStacks&&
this.resetStacks(),this.forceRedraw=!1,this.getSeriesExtremes(),this.setTickInterval(),this.oldUserMin=this.userMin,this.oldUserMax=this.userMax,this.isDirty||(this.isDirty=b||this.min!==this.oldMin||this.max!==this.oldMax)):this.cleanStacks&&this.cleanStacks()},setExtremes:function(a,b,e,l,k){var g=this,t=g.chart;e=y(e,!0);n(g.series,function(a){delete a.kdTree});k=c(k,{min:a,max:b});f(g,"setExtremes",k,function(){g.userMin=a;g.userMax=b;g.eventArgs=k;e&&t.redraw(l)})},zoom:function(a,b){var g=this.dataMin,
l=this.dataMax,c=this.options,e=Math.min(g,y(c.min,g)),c=Math.max(l,y(c.max,l));if(a!==this.min||b!==this.max)this.allowZoomOutside||(m(g)&&(a<e&&(a=e),a>c&&(a=c)),m(l)&&(b<e&&(b=e),b>c&&(b=c))),this.displayBtn=void 0!==a||void 0!==b,this.setExtremes(a,b,!1,void 0,{trigger:"zoom"});return!0},setAxisSize:function(){var b=this.chart,c=this.options,e=c.offsets||[0,0,0,0],l=this.horiz,k=this.width=Math.round(a.relativeLength(y(c.width,b.plotWidth-e[3]+e[1]),b.plotWidth)),f=this.height=Math.round(a.relativeLength(y(c.height,
b.plotHeight-e[0]+e[2]),b.plotHeight)),t=this.top=Math.round(a.relativeLength(y(c.top,b.plotTop+e[0]),b.plotHeight,b.plotTop)),c=this.left=Math.round(a.relativeLength(y(c.left,b.plotLeft+e[3]),b.plotWidth,b.plotLeft));this.bottom=b.chartHeight-f-t;this.right=b.chartWidth-k-c;this.len=Math.max(l?k:f,0);this.pos=l?c:t},getExtremes:function(){var a=this.isLog,b=this.lin2log;return{min:a?h(b(this.min)):this.min,max:a?h(b(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,
userMax:this.userMax}},getThreshold:function(a){var b=this.isLog,g=this.lin2log,l=b?g(this.min):this.min,b=b?g(this.max):this.max;null===a?a=l:l>a?a=l:b<a&&(a=b);return this.translate(a,0,1,0,1)},autoLabelAlign:function(a){a=(y(a,0)-90*this.side+720)%360;return 15<a&&165>a?"right":195<a&&345>a?"left":"center"},tickSize:function(a){var b=this.options,g=b[a+"Length"],l=y(b[a+"Width"],"tick"===a&&this.isXAxis?1:0);if(l&&g)return"inside"===b[a+"Position"]&&(g=-g),[g,l]},labelMetrics:function(){var a=
this.tickPositions&&this.tickPositions[0]||0;return this.chart.renderer.fontMetrics(this.options.labels.style&&this.options.labels.style.fontSize,this.ticks[a]&&this.ticks[a].label)},unsquish:function(){var a=this.options.labels,b=this.horiz,c=this.tickInterval,l=c,e=this.len/(((this.categories?1:0)+this.max-this.min)/c),k,f=a.rotation,t=this.labelMetrics(),q,d=Number.MAX_VALUE,u,x=function(a){a/=e||1;a=1<a?Math.ceil(a):1;return a*c};b?(u=!a.staggerLines&&!a.step&&(m(f)?[f]:e<y(a.autoRotationLimit,
80)&&a.autoRotation))&&n(u,function(a){var b;if(a===f||a&&-90<=a&&90>=a)q=x(Math.abs(t.h/Math.sin(v*a))),b=q+Math.abs(a/360),b<d&&(d=b,k=a,l=q)}):a.step||(l=x(t.h));this.autoRotation=u;this.labelRotation=y(k,f);return l},getSlotWidth:function(){var a=this.chart,b=this.horiz,c=this.options.labels,l=Math.max(this.tickPositions.length-(this.categories?0:1),1),e=a.margin[3];return b&&2>(c.step||0)&&!c.rotation&&(this.staggerLines||1)*this.len/l||!b&&(c.style&&parseInt(c.style.width,10)||e&&e-a.spacing[3]||
.33*a.chartWidth)},renderUnsquish:function(){var a=this.chart,b=a.renderer,c=this.tickPositions,l=this.ticks,e=this.options.labels,k=this.horiz,f=this.getSlotWidth(),t=Math.max(1,Math.round(f-2*(e.padding||5))),q={},d=this.labelMetrics(),u=e.style&&e.style.textOverflow,x,h,m=0,G;D(e.rotation)||(q.rotation=e.rotation||0);n(c,function(a){(a=l[a])&&a.label&&a.label.textPxLength>m&&(m=a.label.textPxLength)});this.maxLabelLength=m;if(this.autoRotation)m>t&&m>d.h?q.rotation=this.labelRotation:this.labelRotation=
0;else if(f&&(x=t,!u))for(h="clip",t=c.length;!k&&t--;)if(G=c[t],G=l[G].label)G.styles&&"ellipsis"===G.styles.textOverflow?G.css({textOverflow:"clip"}):G.textPxLength>f&&G.css({width:f+"px"}),G.getBBox().height>this.len/c.length-(d.h-d.f)&&(G.specificTextOverflow="ellipsis");q.rotation&&(x=m>.5*a.chartHeight?.33*a.chartHeight:a.chartHeight,u||(h="ellipsis"));if(this.labelAlign=e.align||this.autoLabelAlign(this.labelRotation))q.align=this.labelAlign;n(c,function(a){var b=(a=l[a])&&a.label;b&&(b.attr(q),
!x||e.style&&e.style.width||!(x<b.textPxLength||"SPAN"===b.element.tagName)||b.css({width:x,textOverflow:b.specificTextOverflow||h}),delete b.specificTextOverflow,a.rotation=q.rotation)});this.tickRotCorr=b.rotCorr(d.b,this.labelRotation||0,0!==this.side)},hasData:function(){return this.hasVisibleSeries||m(this.min)&&m(this.max)&&this.tickPositions&&0<this.tickPositions.length},addTitle:function(a){var b=this.chart.renderer,g=this.horiz,l=this.opposite,c=this.options.title,e;this.axisTitle||((e=c.textAlign)||
(e=(g?{low:"left",middle:"center",high:"right"}:{low:l?"right":"left",middle:"center",high:l?"left":"right"})[c.align]),this.axisTitle=b.text(c.text,0,0,c.useHTML).attr({zIndex:7,rotation:c.rotation||0,align:e}).addClass("highcharts-axis-title").add(this.axisGroup),this.axisTitle.isNew=!0);this.axisTitle.css({width:this.len});this.axisTitle[a?"show":"hide"](!0)},generateTick:function(a){var b=this.ticks;b[a]?b[a].addLabel():b[a]=new B(this,a)},getOffset:function(){var a=this,b=a.chart,c=b.renderer,
l=a.options,e=a.tickPositions,k=a.ticks,f=a.horiz,t=a.side,q=b.inverted&&!a.isZAxis?[1,0,3,2][t]:t,d,u,x=0,h,G=0,B=l.title,E=l.labels,D=0,I=b.axisOffset,b=b.clipOffset,A=[-1,1,1,-1][t],r=l.className,p=a.axisParent,v=this.tickSize("tick");d=a.hasData();a.showAxis=u=d||y(l.showEmpty,!0);a.staggerLines=a.horiz&&E.staggerLines;a.axisGroup||(a.gridGroup=c.g("grid").attr({zIndex:l.gridZIndex||1}).addClass("highcharts-"+this.coll.toLowerCase()+"-grid "+(r||"")).add(p),a.axisGroup=c.g("axis").attr({zIndex:l.zIndex||
2}).addClass("highcharts-"+this.coll.toLowerCase()+" "+(r||"")).add(p),a.labelGroup=c.g("axis-labels").attr({zIndex:E.zIndex||7}).addClass("highcharts-"+a.coll.toLowerCase()+"-labels "+(r||"")).add(p));d||a.isLinked?(n(e,function(b,g){a.generateTick(b,g)}),a.renderUnsquish(),a.reserveSpaceDefault=0===t||2===t||{1:"left",3:"right"}[t]===a.labelAlign,y(E.reserveSpace,"center"===a.labelAlign?!0:null,a.reserveSpaceDefault)&&n(e,function(a){D=Math.max(k[a].getLabelSize(),D)}),a.staggerLines&&(D*=a.staggerLines),
a.labelOffset=D*(a.opposite?-1:1)):H(k,function(a,b){a.destroy();delete k[b]});B&&B.text&&!1!==B.enabled&&(a.addTitle(u),u&&!1!==B.reserveSpace&&(a.titleOffset=x=a.axisTitle.getBBox()[f?"height":"width"],h=B.offset,G=m(h)?0:y(B.margin,f?5:10)));a.renderLine();a.offset=A*y(l.offset,I[t]);a.tickRotCorr=a.tickRotCorr||{x:0,y:0};c=0===t?-a.labelMetrics().h:2===t?a.tickRotCorr.y:0;G=Math.abs(D)+G;D&&(G=G-c+A*(f?y(E.y,a.tickRotCorr.y+8*A):E.x));a.axisTitleMargin=y(h,G);I[t]=Math.max(I[t],a.axisTitleMargin+
x+A*a.offset,G,d&&e.length&&v?v[0]+A*a.offset:0);l=l.offset?0:2*Math.floor(a.axisLine.strokeWidth()/2);b[q]=Math.max(b[q],l)},getLinePath:function(a){var b=this.chart,g=this.opposite,c=this.offset,e=this.horiz,k=this.left+(g?this.width:0)+c,c=b.chartHeight-this.bottom-(g?this.height:0)+c;g&&(a*=-1);return b.renderer.crispLine(["M",e?this.left:k,e?c:this.top,"L",e?b.chartWidth-this.right:k,e?c:b.chartHeight-this.bottom],a)},renderLine:function(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup))},
getTitlePosition:function(){var a=this.horiz,b=this.left,c=this.top,l=this.len,e=this.options.title,k=a?b:c,f=this.opposite,t=this.offset,q=e.x||0,d=e.y||0,u=this.axisTitle,x=this.chart.renderer.fontMetrics(e.style&&e.style.fontSize,u),u=Math.max(u.getBBox(null,0).height-x.h-1,0),l={low:k+(a?0:l),middle:k+l/2,high:k+(a?l:0)}[e.align],b=(a?c+this.height:b)+(a?1:-1)*(f?-1:1)*this.axisTitleMargin+[-u,u,x.f,-u][this.side];return{x:a?l+q:b+(f?this.width:0)+t+q,y:a?b+d-(f?this.height:0)+t:l+d}},renderMinorTick:function(a){var b=
this.chart.hasRendered&&q(this.oldMin),g=this.minorTicks;g[a]||(g[a]=new B(this,a,"minor"));b&&g[a].isNew&&g[a].render(null,!0);g[a].render(null,!1,1)},renderTick:function(a,b){var g=this.isLinked,c=this.ticks,e=this.chart.hasRendered&&q(this.oldMin);if(!g||a>=this.min&&a<=this.max)c[a]||(c[a]=new B(this,a)),e&&c[a].isNew&&c[a].render(b,!0,.1),c[a].render(b)},render:function(){var b=this,c=b.chart,e=b.options,l=b.isLog,k=b.lin2log,f=b.isLinked,t=b.tickPositions,d=b.axisTitle,u=b.ticks,x=b.minorTicks,
h=b.alternateBands,m=e.stackLabels,E=e.alternateGridColor,y=b.tickmarkOffset,D=b.axisLine,I=b.showAxis,A=F(c.renderer.globalAnimation),r,p;b.labelEdge.length=0;b.overlap=!1;n([u,x,h],function(a){H(a,function(a){a.isActive=!1})});if(b.hasData()||f)b.minorTickInterval&&!b.categories&&n(b.getMinorTickPositions(),function(a){b.renderMinorTick(a)}),t.length&&(n(t,function(a,c){b.renderTick(a,c)}),y&&(0===b.min||b.single)&&(u[-1]||(u[-1]=new B(b,-1,null,!0)),u[-1].render(-1))),E&&n(t,function(g,e){p=void 0!==
t[e+1]?t[e+1]+y:b.max-y;0===e%2&&g<b.max&&p<=b.max+(c.polar?-y:y)&&(h[g]||(h[g]=new a.PlotLineOrBand(b)),r=g+y,h[g].options={from:l?k(r):r,to:l?k(p):p,color:E},h[g].render(),h[g].isActive=!0)}),b._addedPlotLB||(n((e.plotLines||[]).concat(e.plotBands||[]),function(a){b.addPlotBandOrLine(a)}),b._addedPlotLB=!0);n([u,x,h],function(a){var b,g=[],l=A.duration;H(a,function(a,b){a.isActive||(a.render(b,!1,0),a.isActive=!1,g.push(b))});G(function(){for(b=g.length;b--;)a[g[b]]&&!a[g[b]].isActive&&(a[g[b]].destroy(),
delete a[g[b]])},a!==h&&c.hasRendered&&l?l:0)});D&&(D[D.isPlaced?"animate":"attr"]({d:this.getLinePath(D.strokeWidth())}),D.isPlaced=!0,D[I?"show":"hide"](!0));d&&I&&(e=b.getTitlePosition(),q(e.y)?(d[d.isNew?"attr":"animate"](e),d.isNew=!1):(d.attr("y",-9999),d.isNew=!0));m&&m.enabled&&b.renderStackTotals();b.isDirty=!1},redraw:function(){this.visible&&(this.render(),n(this.plotLinesAndBands,function(a){a.render()}));n(this.series,function(a){a.isDirty=!0})},keepProps:"extKey hcEvents names series userMax userMin".split(" "),
destroy:function(a){var c=this,g=c.stacks,l=c.plotLinesAndBands,e;a||t(c);H(g,function(a,b){r(a);g[b]=null});n([c.ticks,c.minorTicks,c.alternateBands],function(a){r(a)});if(l)for(a=l.length;a--;)l[a].destroy();n("stackTotalGroup axisLine axisTitle axisGroup gridGroup labelGroup cross".split(" "),function(a){c[a]&&(c[a]=c[a].destroy())});for(e in c.plotLinesAndBandsGroups)c.plotLinesAndBandsGroups[e]=c.plotLinesAndBandsGroups[e].destroy();H(c,function(a,g){-1===b(g,c.keepProps)&&delete c[g]})},drawCrosshair:function(a,
b){var c,l=this.crosshair,g=y(l.snap,!0),e,k=this.cross;a||(a=this.cross&&this.cross.e);this.crosshair&&!1!==(m(b)||!g)?(g?m(b)&&(e=this.isXAxis?b.plotX:this.len-b.plotY):e=a&&(this.horiz?a.chartX-this.pos:this.len-a.chartY+this.pos),m(e)&&(c=this.getPlotLinePath(b&&(this.isXAxis?b.x:y(b.stackY,b.y)),null,null,null,e)||null),m(c)?(b=this.categories&&!this.isRadial,k||(this.cross=k=this.chart.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(b?"category ":"thin ")+l.className).attr({zIndex:y(l.zIndex,
2)}).add()),k.show().attr({d:c}),b&&!l.width&&k.attr({"stroke-width":this.transA}),this.cross.e=a):this.hideCrosshair()):this.hideCrosshair()},hideCrosshair:function(){this.cross&&this.cross.hide()}});return a.Axis=I}(M);(function(a){var z=a.Axis,F=a.getMagnitude,C=a.normalizeTickInterval,p=a.timeUnits;z.prototype.getTimeTicks=function(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)};z.prototype.normalizeTimeTickInterval=function(a,d){var h=d||[["millisecond",[1,2,5,10,20,25,
50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]];d=h[h.length-1];var v=p[d[0]],r=d[1],n;for(n=0;n<h.length&&!(d=h[n],v=p[d[0]],r=d[1],h[n+1]&&a<=(v*r[r.length-1]+p[h[n+1][0]])/2);n++);v===p.year&&a<5*v&&(r=[1,2,5]);a=C(a/v,r,"year"===d[0]?Math.max(F(a/v),1):1);return{unitRange:v,count:a,unitName:d[0]}}})(M);(function(a){var z=a.Axis,F=a.getMagnitude,C=a.map,p=a.normalizeTickInterval,
h=a.pick;z.prototype.getLogTickPositions=function(a,m,v,r){var d=this.options,c=this.len,f=this.lin2log,A=this.log2lin,k=[];r||(this._minorAutoInterval=null);if(.5<=a)a=Math.round(a),k=this.getLinearTickPositions(a,m,v);else if(.08<=a)for(var c=Math.floor(m),e,b,u,q,D,d=.3<a?[1,2,4]:.15<a?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9];c<v+1&&!D;c++)for(b=d.length,e=0;e<b&&!D;e++)u=A(f(c)*d[e]),u>m&&(!r||q<=v)&&void 0!==q&&k.push(q),q>v&&(D=!0),q=u;else m=f(m),v=f(v),a=r?this.getMinorTickInterval():d.tickInterval,
a=h("auto"===a?null:a,this._minorAutoInterval,d.tickPixelInterval/(r?5:1)*(v-m)/((r?c/this.tickPositions.length:c)||1)),a=p(a,null,F(a)),k=C(this.getLinearTickPositions(a,m,v),A),r||(this._minorAutoInterval=a/5);r||(this.tickInterval=a);return k};z.prototype.log2lin=function(a){return Math.log(a)/Math.LN10};z.prototype.lin2log=function(a){return Math.pow(10,a)}})(M);(function(a,z){var F=a.arrayMax,C=a.arrayMin,p=a.defined,h=a.destroyObjectProperties,d=a.each,m=a.erase,v=a.merge,r=a.pick;a.PlotLineOrBand=
function(a,c){this.axis=a;c&&(this.options=c,this.id=c.id)};a.PlotLineOrBand.prototype={render:function(){var d=this,c=d.axis,f=c.horiz,h=d.options,k=h.label,e=d.label,b=h.to,u=h.from,q=h.value,m=p(u)&&p(b),K=p(q),E=d.svgElem,H=!E,y=[],t=r(h.zIndex,0),x=h.events,y={"class":"highcharts-plot-"+(m?"band ":"line ")+(h.className||"")},G={},B=c.chart.renderer,I=m?"bands":"lines",g;g=c.log2lin;c.isLog&&(u=g(u),b=g(b),q=g(q));G.zIndex=t;I+="-"+t;(g=c.plotLinesAndBandsGroups[I])||(c.plotLinesAndBandsGroups[I]=
g=B.g("plot-"+I).attr(G).add());H&&(d.svgElem=E=B.path().attr(y).add(g));if(K)y=c.getPlotLinePath(q,E.strokeWidth());else if(m)y=c.getPlotBandPath(u,b,h);else return;H&&y&&y.length?(E.attr({d:y}),x&&a.objectEach(x,function(a,b){E.on(b,function(a){x[b].apply(d,[a])})})):E&&(y?(E.show(),E.animate({d:y})):(E.hide(),e&&(d.label=e=e.destroy())));k&&p(k.text)&&y&&y.length&&0<c.width&&0<c.height&&!y.flat?(k=v({align:f&&m&&"center",x:f?!m&&4:10,verticalAlign:!f&&m&&"middle",y:f?m?16:10:m?6:-4,rotation:f&&
!m&&90},k),this.renderLabel(k,y,m,t)):e&&e.hide();return d},renderLabel:function(a,c,f,d){var k=this.label,e=this.axis.chart.renderer;k||(k={align:a.textAlign||a.align,rotation:a.rotation,"class":"highcharts-plot-"+(f?"band":"line")+"-label "+(a.className||"")},k.zIndex=d,this.label=k=e.text(a.text,0,0,a.useHTML).attr(k).add());d=c.xBounds||[c[1],c[4],f?c[6]:c[1]];c=c.yBounds||[c[2],c[5],f?c[7]:c[2]];f=C(d);e=C(c);k.align(a,!1,{x:f,y:e,width:F(d)-f,height:F(c)-e});k.show()},destroy:function(){m(this.axis.plotLinesAndBands,
this);delete this.axis;h(this)}};a.extend(z.prototype,{getPlotBandPath:function(a,c){var f=this.getPlotLinePath(c,null,null,!0),d=this.getPlotLinePath(a,null,null,!0),k=[],e=this.horiz,b=1,u;a=a<this.min&&c<this.min||a>this.max&&c>this.max;if(d&&f)for(a&&(u=d.toString()===f.toString(),b=0),a=0;a<d.length;a+=6)e&&f[a+1]===d[a+1]?(f[a+1]+=b,f[a+4]+=b):e||f[a+2]!==d[a+2]||(f[a+2]+=b,f[a+5]+=b),k.push("M",d[a+1],d[a+2],"L",d[a+4],d[a+5],f[a+4],f[a+5],f[a+1],f[a+2],"z"),k.flat=u;return k},addPlotBand:function(a){return this.addPlotBandOrLine(a,
"plotBands")},addPlotLine:function(a){return this.addPlotBandOrLine(a,"plotLines")},addPlotBandOrLine:function(d,c){var f=(new a.PlotLineOrBand(this,d)).render(),h=this.userOptions;f&&(c&&(h[c]=h[c]||[],h[c].push(d)),this.plotLinesAndBands.push(f));return f},removePlotBandOrLine:function(a){for(var c=this.plotLinesAndBands,f=this.options,h=this.userOptions,k=c.length;k--;)c[k].id===a&&c[k].destroy();d([f.plotLines||[],h.plotLines||[],f.plotBands||[],h.plotBands||[]],function(c){for(k=c.length;k--;)c[k].id===
a&&m(c,c[k])})},removePlotBand:function(a){this.removePlotBandOrLine(a)},removePlotLine:function(a){this.removePlotBandOrLine(a)}})})(M,U);(function(a){var z=a.each,F=a.extend,C=a.format,p=a.isNumber,h=a.map,d=a.merge,m=a.pick,v=a.splat,r=a.syncTimeout,n=a.timeUnits;a.Tooltip=function(){this.init.apply(this,arguments)};a.Tooltip.prototype={init:function(a,f){this.chart=a;this.options=f;this.crosshairs=[];this.now={x:0,y:0};this.isHidden=!0;this.split=f.split&&!a.inverted;this.shared=f.shared||this.split},
cleanSplit:function(a){z(this.chart.series,function(c){var f=c&&c.tt;f&&(!f.isActive||a?c.tt=f.destroy():f.isActive=!1)})},applyFilter:function(){var a=this.chart;a.renderer.definition({tagName:"filter",id:"drop-shadow-"+a.index,opacity:.5,children:[{tagName:"feGaussianBlur",in:"SourceAlpha",stdDeviation:1},{tagName:"feOffset",dx:1,dy:1},{tagName:"feComponentTransfer",children:[{tagName:"feFuncA",type:"linear",slope:.3}]},{tagName:"feMerge",children:[{tagName:"feMergeNode"},{tagName:"feMergeNode",
in:"SourceGraphic"}]}]});a.renderer.definition({tagName:"style",textContent:".highcharts-tooltip-"+a.index+"{filter:url(#drop-shadow-"+a.index+")}"})},getLabel:function(){var a=this.chart.renderer,f=this.options;this.label||(this.label=this.split?a.g("tooltip"):a.label("",0,0,f.shape||"callout",null,null,f.useHTML,null,"tooltip").attr({padding:f.padding,r:f.borderRadius}),this.applyFilter(),this.label.addClass("highcharts-tooltip-"+this.chart.index),this.label.attr({zIndex:8}).add());return this.label},
update:function(a){this.destroy();d(!0,this.chart.options.tooltip.userOptions,a);this.init(this.chart,d(!0,this.options,a))},destroy:function(){this.label&&(this.label=this.label.destroy());this.split&&this.tt&&(this.cleanSplit(this.chart,!0),this.tt=this.tt.destroy());clearTimeout(this.hideTimer);clearTimeout(this.tooltipTimeout)},move:function(a,f,d,k){var c=this,b=c.now,u=!1!==c.options.animation&&!c.isHidden&&(1<Math.abs(a-b.x)||1<Math.abs(f-b.y)),q=c.followPointer||1<c.len;F(b,{x:u?(2*b.x+a)/
3:a,y:u?(b.y+f)/2:f,anchorX:q?void 0:u?(2*b.anchorX+d)/3:d,anchorY:q?void 0:u?(b.anchorY+k)/2:k});c.getLabel().attr(b);u&&(clearTimeout(this.tooltipTimeout),this.tooltipTimeout=setTimeout(function(){c&&c.move(a,f,d,k)},32))},hide:function(a){var c=this;clearTimeout(this.hideTimer);a=m(a,this.options.hideDelay,500);this.isHidden||(this.hideTimer=r(function(){c.getLabel()[a?"fadeOut":"hide"]();c.isHidden=!0},a))},getAnchor:function(a,f){var c,k=this.chart,e=k.inverted,b=k.plotTop,d=k.plotLeft,q=0,m=
0,n,E;a=v(a);c=a[0].tooltipPos;this.followPointer&&f&&(void 0===f.chartX&&(f=k.pointer.normalize(f)),c=[f.chartX-k.plotLeft,f.chartY-b]);c||(z(a,function(a){n=a.series.yAxis;E=a.series.xAxis;q+=a.plotX+(!e&&E?E.left-d:0);m+=(a.plotLow?(a.plotLow+a.plotHigh)/2:a.plotY)+(!e&&n?n.top-b:0)}),q/=a.length,m/=a.length,c=[e?k.plotWidth-m:q,this.shared&&!e&&1<a.length&&f?f.chartY-b:e?k.plotHeight-q:m]);return h(c,Math.round)},getPosition:function(a,f,d){var c=this.chart,e=this.distance,b={},u=c.inverted&&
d.h||0,q,h=["y",c.chartHeight,f,d.plotY+c.plotTop,c.plotTop,c.plotTop+c.plotHeight],n=["x",c.chartWidth,a,d.plotX+c.plotLeft,c.plotLeft,c.plotLeft+c.plotWidth],E=!this.followPointer&&m(d.ttBelow,!c.inverted===!!d.negative),H=function(a,c,k,g,t,f){var l=k<g-e,d=g+e+k<c,q=g-e-k;g+=e;if(E&&d)b[a]=g;else if(!E&&l)b[a]=q;else if(l)b[a]=Math.min(f-k,0>q-u?q:q-u);else if(d)b[a]=Math.max(t,g+u+k>c?g:g+u);else return!1},y=function(a,c,k,g){var t;g<e||g>c-e?t=!1:b[a]=g<k/2?1:g>c-k/2?c-k-2:g-k/2;return t},t=
function(a){var b=h;h=n;n=b;q=a},x=function(){!1!==H.apply(0,h)?!1!==y.apply(0,n)||q||(t(!0),x()):q?b.x=b.y=0:(t(!0),x())};(c.inverted||1<this.len)&&t();x();return b},defaultFormatter:function(a){var c=this.points||v(this),d;d=[a.tooltipFooterHeaderFormatter(c[0])];d=d.concat(a.bodyFormatter(c));d.push(a.tooltipFooterHeaderFormatter(c[0],!0));return d},refresh:function(a,f){var c,k=this.options,e=a,b,d={},q=[];c=k.formatter||this.defaultFormatter;var d=this.shared,h;k.enabled&&(clearTimeout(this.hideTimer),
this.followPointer=v(e)[0].series.tooltipOptions.followPointer,b=this.getAnchor(e,f),f=b[0],k=b[1],!d||e.series&&e.series.noSharedTooltip?d=e.getLabelConfig():(z(e,function(a){a.setState("hover");q.push(a.getLabelConfig())}),d={x:e[0].category,y:e[0].y},d.points=q,e=e[0]),this.len=q.length,d=c.call(d,this),h=e.series,this.distance=m(h.tooltipOptions.distance,16),!1===d?this.hide():(c=this.getLabel(),this.isHidden&&c.attr({opacity:1}).show(),this.split?this.renderSplit(d,v(a)):(c.css({width:this.chart.spacingBox.width}),
c.attr({text:d&&d.join?d.join(""):d}),c.removeClass(/highcharts-color-[\d]+/g).addClass("highcharts-color-"+m(e.colorIndex,h.colorIndex)),this.updatePosition({plotX:f,plotY:k,negative:e.negative,ttBelow:e.ttBelow,h:b[2]||0})),this.isHidden=!1))},renderSplit:function(c,f){var d=this,k=[],e=this.chart,b=e.renderer,u=!0,q=this.options,h=0,n=this.getLabel();a.isString(c)&&(c=[!1,c]);z(c.slice(0,f.length+1),function(a,c){if(!1!==a){c=f[c-1]||{isHeader:!0,plotX:f[0].plotX};var y=c.series||d,t=y.tt,x="highcharts-color-"+
m(c.colorIndex,(c.series||{}).colorIndex,"none");t||(y.tt=t=b.label(null,null,null,"callout",null,null,q.useHTML).addClass("highcharts-tooltip-box "+x).attr({padding:q.padding,r:q.borderRadius}).add(n));t.isActive=!0;t.attr({text:a});a=t.getBBox();x=a.width+t.strokeWidth();c.isHeader?(h=a.height,x=Math.max(0,Math.min(c.plotX+e.plotLeft-x/2,e.chartWidth-x))):x=c.plotX+e.plotLeft-m(q.distance,16)-x;0>x&&(u=!1);a=(c.series&&c.series.yAxis&&c.series.yAxis.pos)+(c.plotY||0);a-=e.plotTop;k.push({target:c.isHeader?
e.plotHeight+h:a,rank:c.isHeader?1:0,size:y.tt.getBBox().height+1,point:c,x:x,tt:t})}});this.cleanSplit();a.distribute(k,e.plotHeight+h);z(k,function(a){var b=a.point,c=b.series;a.tt.attr({visibility:void 0===a.pos?"hidden":"inherit",x:u||b.isHeader?a.x:b.plotX+e.plotLeft+m(q.distance,16),y:a.pos+e.plotTop,anchorX:b.isHeader?b.plotX+e.plotLeft:b.plotX+c.xAxis.pos,anchorY:b.isHeader?a.pos+e.plotTop-15:b.plotY+c.yAxis.pos})})},updatePosition:function(a){var c=this.chart,d=this.getLabel(),d=(this.options.positioner||
this.getPosition).call(this,d.width,d.height,a);this.move(Math.round(d.x),Math.round(d.y||0),a.plotX+c.plotLeft,a.plotY+c.plotTop)},getDateFormat:function(a,f,d,k){var c=this.chart.time,b=c.dateFormat("%m-%d %H:%M:%S.%L",f),u,q,h={millisecond:15,second:12,minute:9,hour:6,day:3},m="millisecond";for(q in n){if(a===n.week&&+c.dateFormat("%w",f)===d&&"00:00:00.000"===b.substr(6)){q="week";break}if(n[q]>a){q=m;break}if(h[q]&&b.substr(h[q])!=="01-01 00:00:00.000".substr(h[q]))break;"week"!==q&&(m=q)}q&&
(u=k[q]);return u},getXDateFormat:function(a,f,d){f=f.dateTimeLabelFormats;var c=d&&d.closestPointRange;return(c?this.getDateFormat(c,a.x,d.options.startOfWeek,f):f.day)||f.year},tooltipFooterHeaderFormatter:function(a,f){f=f?"footer":"header";var c=a.series,k=c.tooltipOptions,e=k.xDateFormat,b=c.xAxis,d=b&&"datetime"===b.options.type&&p(a.key),q=k[f+"Format"];d&&!e&&(e=this.getXDateFormat(a,k,b));d&&e&&z(a.point&&a.point.tooltipDateKeys||["key"],function(a){q=q.replace("{point."+a+"}","{point."+
a+":"+e+"}")});return C(q,{point:a,series:c},this.chart.time)},bodyFormatter:function(a){return h(a,function(a){var c=a.series.tooltipOptions;return(c[(a.point.formatPrefix||"point")+"Formatter"]||a.point.tooltipFormatter).call(a.point,c[(a.point.formatPrefix||"point")+"Format"])})}}})(M);(function(a){var z=a.addEvent,F=a.attr,C=a.charts,p=a.css,h=a.defined,d=a.each,m=a.extend,v=a.find,r=a.fireEvent,n=a.isNumber,c=a.isObject,f=a.offset,A=a.pick,k=a.splat,e=a.Tooltip;a.Pointer=function(a,c){this.init(a,
c)};a.Pointer.prototype={init:function(a,c){this.options=c;this.chart=a;this.runChartClick=c.chart.events&&!!c.chart.events.click;this.pinchDown=[];this.lastValidTouch={};e&&(a.tooltip=new e(a,c.tooltip),this.followTouchMove=A(c.tooltip.followTouchMove,!0));this.setDOMEvents()},zoomOption:function(a){var b=this.chart,c=b.options.chart,e=c.zoomType||"",b=b.inverted;/touch/.test(a.type)&&(e=A(c.pinchType,e));this.zoomX=a=/x/.test(e);this.zoomY=e=/y/.test(e);this.zoomHor=a&&!b||e&&b;this.zoomVert=e&&
!b||a&&b;this.hasZoom=a||e},normalize:function(a,c){var b;b=a.touches?a.touches.length?a.touches.item(0):a.changedTouches[0]:a;c||(this.chartPosition=c=f(this.chart.container));return m(a,{chartX:Math.round(b.pageX-c.left),chartY:Math.round(b.pageY-c.top)})},getCoordinates:function(a){var b={xAxis:[],yAxis:[]};d(this.chart.axes,function(c){b[c.isXAxis?"xAxis":"yAxis"].push({axis:c,value:c.toValue(a[c.horiz?"chartX":"chartY"])})});return b},findNearestKDPoint:function(a,e,k){var b;d(a,function(a){var f=
!(a.noSharedTooltip&&e)&&0>a.options.findNearestPointBy.indexOf("y");a=a.searchPoint(k,f);if((f=c(a,!0))&&!(f=!c(b,!0)))var f=b.distX-a.distX,d=b.dist-a.dist,q=(a.series.group&&a.series.group.zIndex)-(b.series.group&&b.series.group.zIndex),f=0<(0!==f&&e?f:0!==d?d:0!==q?q:b.series.index>a.series.index?-1:1);f&&(b=a)});return b},getPointFromEvent:function(a){a=a.target;for(var b;a&&!b;)b=a.point,a=a.parentNode;return b},getChartCoordinatesFromPoint:function(a,c){var b=a.series,e=b.xAxis,b=b.yAxis,k=
A(a.clientX,a.plotX);if(e&&b)return c?{chartX:e.len+e.pos-k,chartY:b.len+b.pos-a.plotY}:{chartX:k+e.pos,chartY:a.plotY+b.pos}},getHoverData:function(b,e,k,f,h,m,n){var q,t=[],x=n&&n.isBoosting;f=!(!f||!b);n=e&&!e.stickyTracking?[e]:a.grep(k,function(a){return a.visible&&!(!h&&a.directTouch)&&A(a.options.enableMouseTracking,!0)&&a.stickyTracking});e=(q=f?b:this.findNearestKDPoint(n,h,m))&&q.series;q&&(h&&!e.noSharedTooltip?(n=a.grep(k,function(a){return a.visible&&!(!h&&a.directTouch)&&A(a.options.enableMouseTracking,
!0)&&!a.noSharedTooltip}),d(n,function(a){var b=v(a.points,function(a){return a.x===q.x&&!a.isNull});c(b)&&(x&&(b=a.getPoint(b)),t.push(b))})):t.push(q));return{hoverPoint:q,hoverSeries:e,hoverPoints:t}},runPointActions:function(b,c){var e=this.chart,k=e.tooltip&&e.tooltip.options.enabled?e.tooltip:void 0,f=k?k.shared:!1,h=c||e.hoverPoint,u=h&&h.series||e.hoverSeries,u=this.getHoverData(h,u,e.series,!!c||u&&u.directTouch&&this.isDirectTouch,f,b,{isBoosting:e.isBoosting}),m,h=u.hoverPoint;m=u.hoverPoints;
c=(u=u.hoverSeries)&&u.tooltipOptions.followPointer;f=f&&u&&!u.noSharedTooltip;if(h&&(h!==e.hoverPoint||k&&k.isHidden)){d(e.hoverPoints||[],function(b){-1===a.inArray(b,m)&&b.setState()});d(m||[],function(a){a.setState("hover")});if(e.hoverSeries!==u)u.onMouseOver();e.hoverPoint&&e.hoverPoint.firePointEvent("mouseOut");if(!h.series)return;h.firePointEvent("mouseOver");e.hoverPoints=m;e.hoverPoint=h;k&&k.refresh(f?m:h,b)}else c&&k&&!k.isHidden&&(h=k.getAnchor([{}],b),k.updatePosition({plotX:h[0],plotY:h[1]}));
this.unDocMouseMove||(this.unDocMouseMove=z(e.container.ownerDocument,"mousemove",function(b){var c=C[a.hoverChartIndex];if(c)c.pointer.onDocumentMouseMove(b)}));d(e.axes,function(c){var e=A(c.crosshair.snap,!0),k=e?a.find(m,function(a){return a.series[c.coll]===c}):void 0;k||!e?c.drawCrosshair(b,k):c.hideCrosshair()})},reset:function(a,c){var b=this.chart,e=b.hoverSeries,f=b.hoverPoint,h=b.hoverPoints,u=b.tooltip,m=u&&u.shared?h:f;a&&m&&d(k(m),function(b){b.series.isCartesian&&void 0===b.plotX&&
(a=!1)});if(a)u&&m&&(u.refresh(m),f&&(f.setState(f.state,!0),d(b.axes,function(a){a.crosshair&&a.drawCrosshair(null,f)})));else{if(f)f.onMouseOut();h&&d(h,function(a){a.setState()});if(e)e.onMouseOut();u&&u.hide(c);this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove());d(b.axes,function(a){a.hideCrosshair()});this.hoverX=b.hoverPoints=b.hoverPoint=null}},scaleGroups:function(a,c){var b=this.chart,e;d(b.series,function(k){e=a||k.getPlotBox();k.xAxis&&k.xAxis.zoomEnabled&&k.group&&(k.group.attr(e),
k.markerGroup&&(k.markerGroup.attr(e),k.markerGroup.clip(c?b.clipRect:null)),k.dataLabelsGroup&&k.dataLabelsGroup.attr(e))});b.clipRect.attr(c||b.clipBox)},dragStart:function(a){var b=this.chart;b.mouseIsDown=a.type;b.cancelClick=!1;b.mouseDownX=this.mouseDownX=a.chartX;b.mouseDownY=this.mouseDownY=a.chartY},drag:function(a){var b=this.chart,c=b.options.chart,e=a.chartX,k=a.chartY,f=this.zoomHor,d=this.zoomVert,h=b.plotLeft,t=b.plotTop,x=b.plotWidth,m=b.plotHeight,n,I=this.selectionMarker,g=this.mouseDownX,
w=this.mouseDownY,r=c.panKey&&a[c.panKey+"Key"];I&&I.touch||(e<h?e=h:e>h+x&&(e=h+x),k<t?k=t:k>t+m&&(k=t+m),this.hasDragged=Math.sqrt(Math.pow(g-e,2)+Math.pow(w-k,2)),10<this.hasDragged&&(n=b.isInsidePlot(g-h,w-t),b.hasCartesianSeries&&(this.zoomX||this.zoomY)&&n&&!r&&!I&&(this.selectionMarker=I=b.renderer.rect(h,t,f?1:x,d?1:m,0).attr({"class":"highcharts-selection-marker",zIndex:7}).add()),I&&f&&(e-=g,I.attr({width:Math.abs(e),x:(0<e?0:e)+g})),I&&d&&(e=k-w,I.attr({height:Math.abs(e),y:(0<e?0:e)+w})),
n&&!I&&c.panning&&b.pan(a,c.panning)))},drop:function(a){var b=this,c=this.chart,e=this.hasPinched;if(this.selectionMarker){var k={originalEvent:a,xAxis:[],yAxis:[]},f=this.selectionMarker,H=f.attr?f.attr("x"):f.x,y=f.attr?f.attr("y"):f.y,t=f.attr?f.attr("width"):f.width,x=f.attr?f.attr("height"):f.height,G;if(this.hasDragged||e)d(c.axes,function(c){if(c.zoomEnabled&&h(c.min)&&(e||b[{xAxis:"zoomX",yAxis:"zoomY"}[c.coll]])){var f=c.horiz,g="touchend"===a.type?c.minPixelPadding:0,d=c.toValue((f?H:y)+
g),f=c.toValue((f?H+t:y+x)-g);k[c.coll].push({axis:c,min:Math.min(d,f),max:Math.max(d,f)});G=!0}}),G&&r(c,"selection",k,function(a){c.zoom(m(a,e?{animation:!1}:null))});n(c.index)&&(this.selectionMarker=this.selectionMarker.destroy());e&&this.scaleGroups()}c&&n(c.index)&&(p(c.container,{cursor:c._cursor}),c.cancelClick=10<this.hasDragged,c.mouseIsDown=this.hasDragged=this.hasPinched=!1,this.pinchDown=[])},onContainerMouseDown:function(a){2!==a.button&&(a=this.normalize(a),this.zoomOption(a),a.preventDefault&&
a.preventDefault(),this.dragStart(a))},onDocumentMouseUp:function(b){C[a.hoverChartIndex]&&C[a.hoverChartIndex].pointer.drop(b)},onDocumentMouseMove:function(a){var b=this.chart,c=this.chartPosition;a=this.normalize(a,c);!c||this.inClass(a.target,"highcharts-tracker")||b.isInsidePlot(a.chartX-b.plotLeft,a.chartY-b.plotTop)||this.reset()},onContainerMouseLeave:function(b){var c=C[a.hoverChartIndex];c&&(b.relatedTarget||b.toElement)&&(c.pointer.reset(),c.pointer.chartPosition=null)},onContainerMouseMove:function(b){var c=
this.chart;h(a.hoverChartIndex)&&C[a.hoverChartIndex]&&C[a.hoverChartIndex].mouseIsDown||(a.hoverChartIndex=c.index);b=this.normalize(b);b.returnValue=!1;"mousedown"===c.mouseIsDown&&this.drag(b);!this.inClass(b.target,"highcharts-tracker")&&!c.isInsidePlot(b.chartX-c.plotLeft,b.chartY-c.plotTop)||c.openMenu||this.runPointActions(b)},inClass:function(a,c){for(var b;a;){if(b=F(a,"class")){if(-1!==b.indexOf(c))return!0;if(-1!==b.indexOf("highcharts-container"))return!1}a=a.parentNode}},onTrackerMouseOut:function(a){var b=
this.chart.hoverSeries;a=a.relatedTarget||a.toElement;this.isDirectTouch=!1;if(!(!b||!a||b.stickyTracking||this.inClass(a,"highcharts-tooltip")||this.inClass(a,"highcharts-series-"+b.index)&&this.inClass(a,"highcharts-tracker")))b.onMouseOut()},onContainerClick:function(a){var b=this.chart,c=b.hoverPoint,e=b.plotLeft,k=b.plotTop;a=this.normalize(a);b.cancelClick||(c&&this.inClass(a.target,"highcharts-tracker")?(r(c.series,"click",m(a,{point:c})),b.hoverPoint&&c.firePointEvent("click",a)):(m(a,this.getCoordinates(a)),
b.isInsidePlot(a.chartX-e,a.chartY-k)&&r(b,"click",a)))},setDOMEvents:function(){var b=this,c=b.chart.container,e=c.ownerDocument;c.onmousedown=function(a){b.onContainerMouseDown(a)};c.onmousemove=function(a){b.onContainerMouseMove(a)};c.onclick=function(a){b.onContainerClick(a)};this.unbindContainerMouseLeave=z(c,"mouseleave",b.onContainerMouseLeave);a.unbindDocumentMouseUp||(a.unbindDocumentMouseUp=z(e,"mouseup",b.onDocumentMouseUp));a.hasTouch&&(c.ontouchstart=function(a){b.onContainerTouchStart(a)},
c.ontouchmove=function(a){b.onContainerTouchMove(a)},a.unbindDocumentTouchEnd||(a.unbindDocumentTouchEnd=z(e,"touchend",b.onDocumentTouchEnd)))},destroy:function(){var b=this;b.unDocMouseMove&&b.unDocMouseMove();this.unbindContainerMouseLeave();a.chartCount||(a.unbindDocumentMouseUp&&(a.unbindDocumentMouseUp=a.unbindDocumentMouseUp()),a.unbindDocumentTouchEnd&&(a.unbindDocumentTouchEnd=a.unbindDocumentTouchEnd()));clearInterval(b.tooltipTimeout);a.objectEach(b,function(a,c){b[c]=null})}}})(M);(function(a){var z=
a.charts,F=a.each,C=a.extend,p=a.map,h=a.noop,d=a.pick;C(a.Pointer.prototype,{pinchTranslate:function(a,d,h,n,c,f){this.zoomHor&&this.pinchTranslateDirection(!0,a,d,h,n,c,f);this.zoomVert&&this.pinchTranslateDirection(!1,a,d,h,n,c,f)},pinchTranslateDirection:function(a,d,h,n,c,f,p,k){var e=this.chart,b=a?"x":"y",m=a?"X":"Y",q="chart"+m,r=a?"width":"height",v=e["plot"+(a?"Left":"Top")],E,H,y=k||1,t=e.inverted,x=e.bounds[a?"h":"v"],G=1===d.length,B=d[0][q],I=h[0][q],g=!G&&d[1][q],w=!G&&h[1][q],A;h=
function(){!G&&20<Math.abs(B-g)&&(y=k||Math.abs(I-w)/Math.abs(B-g));H=(v-I)/y+B;E=e["plot"+(a?"Width":"Height")]/y};h();d=H;d<x.min?(d=x.min,A=!0):d+E>x.max&&(d=x.max-E,A=!0);A?(I-=.8*(I-p[b][0]),G||(w-=.8*(w-p[b][1])),h()):p[b]=[I,w];t||(f[b]=H-v,f[r]=E);f=t?1/y:y;c[r]=E;c[b]=d;n[t?a?"scaleY":"scaleX":"scale"+m]=y;n["translate"+m]=f*v+(I-f*B)},pinch:function(a){var m=this,r=m.chart,n=m.pinchDown,c=a.touches,f=c.length,A=m.lastValidTouch,k=m.hasZoom,e=m.selectionMarker,b={},u=1===f&&(m.inClass(a.target,
"highcharts-tracker")&&r.runTrackerClick||m.runChartClick),q={};1<f&&(m.initiated=!0);k&&m.initiated&&!u&&a.preventDefault();p(c,function(a){return m.normalize(a)});"touchstart"===a.type?(F(c,function(a,b){n[b]={chartX:a.chartX,chartY:a.chartY}}),A.x=[n[0].chartX,n[1]&&n[1].chartX],A.y=[n[0].chartY,n[1]&&n[1].chartY],F(r.axes,function(a){if(a.zoomEnabled){var b=r.bounds[a.horiz?"h":"v"],c=a.minPixelPadding,e=a.toPixels(d(a.options.min,a.dataMin)),k=a.toPixels(d(a.options.max,a.dataMax)),f=Math.max(e,
k);b.min=Math.min(a.pos,Math.min(e,k)-c);b.max=Math.max(a.pos+a.len,f+c)}}),m.res=!0):m.followTouchMove&&1===f?this.runPointActions(m.normalize(a)):n.length&&(e||(m.selectionMarker=e=C({destroy:h,touch:!0},r.plotBox)),m.pinchTranslate(n,c,b,e,q,A),m.hasPinched=k,m.scaleGroups(b,q),m.res&&(m.res=!1,this.reset(!1,0)))},touch:function(h,p){var m=this.chart,n,c;if(m.index!==a.hoverChartIndex)this.onContainerMouseLeave({relatedTarget:!0});a.hoverChartIndex=m.index;1===h.touches.length?(h=this.normalize(h),
(c=m.isInsidePlot(h.chartX-m.plotLeft,h.chartY-m.plotTop))&&!m.openMenu?(p&&this.runPointActions(h),"touchmove"===h.type&&(p=this.pinchDown,n=p[0]?4<=Math.sqrt(Math.pow(p[0].chartX-h.chartX,2)+Math.pow(p[0].chartY-h.chartY,2)):!1),d(n,!0)&&this.pinch(h)):p&&this.reset()):2===h.touches.length&&this.pinch(h)},onContainerTouchStart:function(a){this.zoomOption(a);this.touch(a,!0)},onContainerTouchMove:function(a){this.touch(a)},onDocumentTouchEnd:function(d){z[a.hoverChartIndex]&&z[a.hoverChartIndex].pointer.drop(d)}})})(M);
(function(a){var z=a.addEvent,F=a.charts,C=a.css,p=a.doc,h=a.extend,d=a.noop,m=a.Pointer,v=a.removeEvent,r=a.win,n=a.wrap;if(!a.hasTouch&&(r.PointerEvent||r.MSPointerEvent)){var c={},f=!!r.PointerEvent,A=function(){var e=[];e.item=function(a){return this[a]};a.objectEach(c,function(a){e.push({pageX:a.pageX,pageY:a.pageY,target:a.target})});return e},k=function(c,b,k,f){"touch"!==c.pointerType&&c.pointerType!==c.MSPOINTER_TYPE_TOUCH||!F[a.hoverChartIndex]||(f(c),f=F[a.hoverChartIndex].pointer,f[b]({type:k,
target:c.currentTarget,preventDefault:d,touches:A()}))};h(m.prototype,{onContainerPointerDown:function(a){k(a,"onContainerTouchStart","touchstart",function(a){c[a.pointerId]={pageX:a.pageX,pageY:a.pageY,target:a.currentTarget}})},onContainerPointerMove:function(a){k(a,"onContainerTouchMove","touchmove",function(a){c[a.pointerId]={pageX:a.pageX,pageY:a.pageY};c[a.pointerId].target||(c[a.pointerId].target=a.currentTarget)})},onDocumentPointerUp:function(a){k(a,"onDocumentTouchEnd","touchend",function(a){delete c[a.pointerId]})},
batchMSEvents:function(a){a(this.chart.container,f?"pointerdown":"MSPointerDown",this.onContainerPointerDown);a(this.chart.container,f?"pointermove":"MSPointerMove",this.onContainerPointerMove);a(p,f?"pointerup":"MSPointerUp",this.onDocumentPointerUp)}});n(m.prototype,"init",function(a,b,c){a.call(this,b,c);this.hasZoom&&C(b.container,{"-ms-touch-action":"none","touch-action":"none"})});n(m.prototype,"setDOMEvents",function(a){a.apply(this);(this.hasZoom||this.followTouchMove)&&this.batchMSEvents(z)});
n(m.prototype,"destroy",function(a){this.batchMSEvents(v);a.call(this)})}})(M);(function(a){var z=a.addEvent,F=a.css,C=a.discardElement,p=a.defined,h=a.each,d=a.isFirefox,m=a.marginNames,v=a.merge,r=a.pick,n=a.setAnimation,c=a.stableSort,f=a.win,A=a.wrap;a.Legend=function(a,c){this.init(a,c)};a.Legend.prototype={init:function(a,c){this.chart=a;this.setOptions(c);c.enabled&&(this.render(),z(this.chart,"endResize",function(){this.legend.positionCheckboxes()}))},setOptions:function(a){var c=r(a.padding,
8);this.options=a;this.itemMarginTop=a.itemMarginTop||0;this.padding=c;this.initialItemY=c-5;this.itemHeight=this.maxItemWidth=0;this.symbolWidth=r(a.symbolWidth,16);this.pages=[]},update:function(a,c){var b=this.chart;this.setOptions(v(!0,this.options,a));this.destroy();b.isDirtyLegend=b.isDirtyBox=!0;r(c,!0)&&b.redraw()},colorizeItem:function(a,c){a.legendGroup[c?"removeClass":"addClass"]("highcharts-legend-item-hidden")},positionItem:function(a){var c=this.options,b=c.symbolPadding,c=!c.rtl,k=
a._legendItemPos,f=k[0],k=k[1],d=a.checkbox;(a=a.legendGroup)&&a.element&&a.translate(c?f:this.legendWidth-f-2*b-4,k);d&&(d.x=f,d.y=k)},destroyItem:function(a){var c=a.checkbox;h(["legendItem","legendLine","legendSymbol","legendGroup"],function(b){a[b]&&(a[b]=a[b].destroy())});c&&C(a.checkbox)},destroy:function(){function a(a){this[a]&&(this[a]=this[a].destroy())}h(this.getAllItems(),function(c){h(["legendItem","legendGroup"],a,c)});h("clipRect up down pager nav box title group".split(" "),a,this);
this.display=null},positionCheckboxes:function(){var a=this.group&&this.group.alignAttr,c,b=this.clipHeight||this.legendHeight,f=this.titleHeight;a&&(c=a.translateY,h(this.allItems,function(e){var k=e.checkbox,d;k&&(d=c+f+k.y+(this.scrollOffset||0)+3,F(k,{left:a.translateX+e.checkboxOffset+k.x-20+"px",top:d+"px",display:d>c-6&&d<c+b-6?"":"none"}))},this))},renderTitle:function(){var a=this.options,c=this.padding,b=a.title,f=0;b.text&&(this.title||(this.title=this.chart.renderer.label(b.text,c-3,c-
4,null,null,null,a.useHTML,null,"legend-title").attr({zIndex:1}).add(this.group)),a=this.title.getBBox(),f=a.height,this.offsetWidth=a.width,this.contentGroup.attr({translateY:f}));this.titleHeight=f},setText:function(c){var e=this.options;c.legendItem.attr({text:e.labelFormat?a.format(e.labelFormat,c,this.chart.time):e.labelFormatter.call(c)})},renderItem:function(a){var c=this.chart,b=c.renderer,k=this.options,f="horizontal"===k.layout,d=this.symbolWidth,h=k.symbolPadding,m=this.padding,n=f?r(k.itemDistance,
20):0,y=!k.rtl,t=k.width,x=k.itemMarginBottom||0,G=this.itemMarginTop,B=a.legendItem,p=!a.series,g=!p&&a.series.drawLegendSymbol?a.series:a,w=g.options,A=this.createCheckboxForItem&&w&&w.showCheckbox,w=d+h+n+(A?20:0),l=k.useHTML,J=a.options.className;B||(a.legendGroup=b.g("legend-item").addClass("highcharts-"+g.type+"-series highcharts-color-"+a.colorIndex+(J?" "+J:"")+(p?" highcharts-series-"+a.index:"")).attr({zIndex:1}).add(this.scrollGroup),a.legendItem=B=b.text("",y?d+h:-h,this.baseline||0,l).attr({align:y?
"left":"right",zIndex:2}).add(a.legendGroup),this.baseline||(this.fontMetrics=b.fontMetrics(12,B),this.baseline=this.fontMetrics.f+3+G,B.attr("y",this.baseline)),this.symbolHeight=k.symbolHeight||this.fontMetrics.f,g.drawLegendSymbol(this,a),this.setItemEvents&&this.setItemEvents(a,B,l),A&&this.createCheckboxForItem(a));this.colorizeItem(a,a.visible);B.css({width:(k.itemWidth||k.width||c.spacingBox.width)-w});this.setText(a);b=B.getBBox();d=a.checkboxOffset=k.itemWidth||a.legendItemWidth||b.width+
w;this.itemHeight=b=Math.round(a.legendItemHeight||b.height||this.symbolHeight);f&&this.itemX-m+d>(t||c.spacingBox.width-2*m-k.x)&&(this.itemX=m,this.itemY+=G+this.lastLineHeight+x,this.lastLineHeight=0);this.maxItemWidth=Math.max(this.maxItemWidth,d);this.lastItemY=G+this.itemY+x;this.lastLineHeight=Math.max(b,this.lastLineHeight);a._legendItemPos=[this.itemX,this.itemY];f?this.itemX+=d:(this.itemY+=G+b+x,this.lastLineHeight=b);this.offsetWidth=t||Math.max((f?this.itemX-m-(a.checkbox?0:n):d)+m,this.offsetWidth)},
getAllItems:function(){var a=[];h(this.chart.series,function(c){var b=c&&c.options;c&&r(b.showInLegend,p(b.linkedTo)?!1:void 0,!0)&&(a=a.concat(c.legendItems||("point"===b.legendType?c.data:c)))});return a},getAlignment:function(){var a=this.options;return a.floating?"":a.align.charAt(0)+a.verticalAlign.charAt(0)+a.layout.charAt(0)},adjustMargins:function(a,c){var b=this.chart,e=this.options,f=this.getAlignment();f&&h([/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/],function(k,d){k.test(f)&&
!p(a[d])&&(b[m[d]]=Math.max(b[m[d]],b.legend[(d+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][d]*e[d%2?"x":"y"]+r(e.margin,12)+c[d]+(0===d?b.titleOffset+b.options.title.margin:0)))})},render:function(){var a=this,e=a.chart,b=e.renderer,f=a.group,d,m,n,p,r=a.box,y=a.options,t=a.padding;a.itemX=t;a.itemY=a.initialItemY;a.offsetWidth=0;a.lastItemY=0;f||(a.group=f=b.g("legend").attr({zIndex:7}).add(),a.contentGroup=b.g().attr({zIndex:1}).add(f),a.scrollGroup=b.g().add(a.contentGroup));a.renderTitle();
d=a.getAllItems();c(d,function(a,b){return(a.options&&a.options.legendIndex||0)-(b.options&&b.options.legendIndex||0)});y.reversed&&d.reverse();a.allItems=d;a.display=m=!!d.length;a.lastLineHeight=0;h(d,function(b){a.renderItem(b)});n=(y.width||a.offsetWidth)+t;p=a.lastItemY+a.lastLineHeight+a.titleHeight;p=a.handleOverflow(p);p+=t;r||(a.box=r=b.rect().addClass("highcharts-legend-box").attr({r:y.borderRadius}).add(f),r.isNew=!0);0<n&&0<p&&(r[r.isNew?"attr":"animate"](r.crisp.call({},{x:0,y:0,width:n,
height:p},r.strokeWidth())),r.isNew=!1);r[m?"show":"hide"]();"none"===f.getStyle("display")&&(n=p=0);a.legendWidth=n;a.legendHeight=p;h(d,function(b){a.positionItem(b)});m&&(b=e.spacingBox,/(lth|ct|rth)/.test(a.getAlignment())&&(b=v(b,{y:b.y+e.titleOffset+e.options.title.margin})),f.align(v(y,{width:n,height:p}),!0,b));e.isResizing||this.positionCheckboxes()},handleOverflow:function(a){var c=this,b=this.chart,f=b.renderer,d=this.options,k=d.y,m=this.padding,b=b.spacingBox.height+("top"===d.verticalAlign?
-k:k)-m,k=d.maxHeight,n,p=this.clipRect,y=d.navigation,t=r(y.animation,!0),x=y.arrowSize||12,G=this.nav,B=this.pages,I,g=this.allItems,w=function(a){"number"===typeof a?p.attr({height:a}):p&&(c.clipRect=p.destroy(),c.contentGroup.clip());c.contentGroup.div&&(c.contentGroup.div.style.clip=a?"rect("+m+"px,9999px,"+(m+a)+"px,0)":"auto")};"horizontal"!==d.layout||"middle"===d.verticalAlign||d.floating||(b/=2);k&&(b=Math.min(b,k));B.length=0;a>b&&!1!==y.enabled?(this.clipHeight=n=Math.max(b-20-this.titleHeight-
m,0),this.currentPage=r(this.currentPage,1),this.fullHeight=a,h(g,function(a,b){var c=a._legendItemPos[1],l=Math.round(a.legendItem.getBBox().height),e=B.length;if(!e||c-B[e-1]>n&&(I||c)!==B[e-1])B.push(I||c),e++;a.pageIx=e-1;I&&(g[b-1].pageIx=e-1);b===g.length-1&&c+l-B[e-1]>n&&(B.push(c),a.pageIx=e);c!==I&&(I=c)}),p||(p=c.clipRect=f.clipRect(0,m,9999,0),c.contentGroup.clip(p)),w(n),G||(this.nav=G=f.g().attr({zIndex:1}).add(this.group),this.up=f.symbol("triangle",0,0,x,x).on("click",function(){c.scroll(-1,
t)}).add(G),this.pager=f.text("",15,10).addClass("highcharts-legend-navigation").add(G),this.down=f.symbol("triangle-down",0,0,x,x).on("click",function(){c.scroll(1,t)}).add(G)),c.scroll(0),a=b):G&&(w(),this.nav=G.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0);return a},scroll:function(a,c){var b=this.pages,e=b.length;a=this.currentPage+a;var f=this.clipHeight,d=this.pager,k=this.padding;a>e&&(a=e);0<a&&(void 0!==c&&n(c,this.chart),this.nav.attr({translateX:k,translateY:f+this.padding+
7+this.titleHeight,visibility:"visible"}),this.up.attr({"class":1===a?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"}),d.attr({text:a+"/"+e}),this.down.attr({x:18+this.pager.getBBox().width,"class":a===e?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"}),this.scrollOffset=-b[a-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=a,this.positionCheckboxes())}};a.LegendSymbolMixin={drawRectangle:function(a,c){var b=a.symbolHeight,
e=a.options.squareSymbol;c.legendSymbol=this.chart.renderer.rect(e?(a.symbolWidth-b)/2:0,a.baseline-b+1,e?b:a.symbolWidth,b,r(a.options.symbolRadius,b/2)).addClass("highcharts-point").attr({zIndex:3}).add(c.legendGroup)},drawLineMarker:function(a){var c=this.options.marker,b,f=a.symbolWidth,d=a.symbolHeight;b=d/2;var k=this.chart.renderer,h=this.legendGroup;a=a.baseline-Math.round(.3*a.fontMetrics.b);this.legendLine=k.path(["M",0,a,"L",f,a]).addClass("highcharts-graph").attr({}).add(h);c&&!1!==c.enabled&&
(b=Math.min(r(c.radius,b),b),0===this.symbol.indexOf("url")&&(c=v(c,{width:d,height:d}),b=0),this.legendSymbol=c=k.symbol(this.symbol,f/2-b,a-b,2*b,2*b,c).addClass("highcharts-point").add(h),c.isMarker=!0)}};(/Trident\/7\.0/.test(f.navigator.userAgent)||d)&&A(a.Legend.prototype,"positionItem",function(a,c){var b=this,e=function(){c._legendItemPos&&a.call(b,c)};e();setTimeout(e)})})(M);(function(a){var z=a.addEvent,F=a.animObject,C=a.attr,p=a.doc,h=a.Axis,d=a.createElement,m=a.defaultOptions,v=a.discardElement,
r=a.charts,n=a.defined,c=a.each,f=a.extend,A=a.find,k=a.fireEvent,e=a.grep,b=a.isNumber,u=a.isObject,q=a.isString,D=a.Legend,K=a.marginNames,E=a.merge,H=a.objectEach,y=a.Pointer,t=a.pick,x=a.pInt,G=a.removeEvent,B=a.seriesTypes,I=a.splat,g=a.syncTimeout,w=a.win,L=a.Chart=function(){this.getArgs.apply(this,arguments)};a.chart=function(a,b,c){return new L(a,b,c)};f(L.prototype,{callbacks:[],getArgs:function(){var a=[].slice.call(arguments);if(q(a[0])||a[0].nodeName)this.renderTo=a.shift();this.init(a[0],
a[1])},init:function(b,c){var g,l,e=b.series,f=b.plotOptions||{};b.series=null;g=E(m,b);for(l in g.plotOptions)g.plotOptions[l].tooltip=f[l]&&E(f[l].tooltip)||void 0;g.tooltip.userOptions=b.chart&&b.chart.forExport&&b.tooltip.userOptions||b.tooltip;g.series=b.series=e;this.userOptions=b;l=g.chart;e=l.events;this.margin=[];this.spacing=[];this.bounds={h:{},v:{}};this.labelCollectors=[];this.callback=c;this.isResizing=0;this.options=g;this.axes=[];this.series=[];this.time=b.time&&a.keys(b.time).length?
new a.Time(b.time):a.time;this.hasCartesianSeries=l.showAxes;var d=this;d.index=r.length;r.push(d);a.chartCount++;e&&H(e,function(a,b){z(d,b,a)});d.xAxis=[];d.yAxis=[];d.pointCount=d.colorCounter=d.symbolCounter=0;d.firstRender()},initSeries:function(b){var c=this.options.chart;(c=B[b.type||c.type||c.defaultSeriesType])||a.error(17,!0);c=new c;c.init(this,b);return c},orderSeries:function(a){var b=this.series;for(a=a||0;a<b.length;a++)b[a]&&(b[a].index=a,b[a].name=b[a].getName())},isInsidePlot:function(a,
b,c){var g=c?b:a;a=c?a:b;return 0<=g&&g<=this.plotWidth&&0<=a&&a<=this.plotHeight},redraw:function(b){var g=this.axes,e=this.series,l=this.pointer,d=this.legend,t=this.isDirtyLegend,h,m,x=this.hasCartesianSeries,n=this.isDirtyBox,q,G=this.renderer,u=G.isHidden(),B=[];this.setResponsive&&this.setResponsive(!1);a.setAnimation(b,this);u&&this.temporaryDisplay();this.layOutTitles();for(b=e.length;b--;)if(q=e[b],q.options.stacking&&(h=!0,q.isDirty)){m=!0;break}if(m)for(b=e.length;b--;)q=e[b],q.options.stacking&&
(q.isDirty=!0);c(e,function(a){a.isDirty&&"point"===a.options.legendType&&(a.updateTotals&&a.updateTotals(),t=!0);a.isDirtyData&&k(a,"updatedData")});t&&d.options.enabled&&(d.render(),this.isDirtyLegend=!1);h&&this.getStacks();x&&c(g,function(a){a.updateNames();a.setScale()});this.getMargins();x&&(c(g,function(a){a.isDirty&&(n=!0)}),c(g,function(a){var b=a.min+","+a.max;a.extKey!==b&&(a.extKey=b,B.push(function(){k(a,"afterSetExtremes",f(a.eventArgs,a.getExtremes()));delete a.eventArgs}));(n||h)&&
a.redraw()}));n&&this.drawChartBox();k(this,"predraw");c(e,function(a){(n||a.isDirty)&&a.visible&&a.redraw();a.isDirtyData=!1});l&&l.reset(!0);G.draw();k(this,"redraw");k(this,"render");u&&this.temporaryDisplay(!0);c(B,function(a){a.call()})},get:function(a){function b(b){return b.id===a||b.options&&b.options.id===a}var c,g=this.series,e;c=A(this.axes,b)||A(this.series,b);for(e=0;!c&&e<g.length;e++)c=A(g[e].points||[],b);return c},getAxes:function(){var a=this,b=this.options,g=b.xAxis=I(b.xAxis||
{}),b=b.yAxis=I(b.yAxis||{});c(g,function(a,b){a.index=b;a.isX=!0});c(b,function(a,b){a.index=b});g=g.concat(b);c(g,function(b){new h(a,b)})},getSelectedPoints:function(){var a=[];c(this.series,function(b){a=a.concat(e(b.data||[],function(a){return a.selected}))});return a},getSelectedSeries:function(){return e(this.series,function(a){return a.selected})},setTitle:function(a,b,g){var e=this,l=e.options,f;f=l.title=E(l.title,a);l=l.subtitle=E(l.subtitle,b);c([["title",a,f],["subtitle",b,l]],function(a,
b){var c=a[0],g=e[c],l=a[1];a=a[2];g&&l&&(e[c]=g=g.destroy());a&&!g&&(e[c]=e.renderer.text(a.text,0,0,a.useHTML).attr({align:a.align,"class":"highcharts-"+c,zIndex:a.zIndex||4}).add(),e[c].update=function(a){e.setTitle(!b&&a,b&&a)})});e.layOutTitles(g)},layOutTitles:function(a){var b=0,g,e=this.renderer,l=this.spacingBox;c(["title","subtitle"],function(a){var c=this[a],g=this.options[a];a="title"===a?-3:g.verticalAlign?0:b+2;var d;c&&(d=e.fontMetrics(d,c).b,c.css({width:(g.width||l.width+g.widthAdjust)+
"px"}).align(f({y:a+d},g),!1,"spacingBox"),g.floating||g.verticalAlign||(b=Math.ceil(b+c.getBBox(g.useHTML).height)))},this);g=this.titleOffset!==b;this.titleOffset=b;!this.isDirtyBox&&g&&(this.isDirtyBox=g,this.hasRendered&&t(a,!0)&&this.isDirtyBox&&this.redraw())},getChartSize:function(){var b=this.options.chart,c=b.width,b=b.height,g=this.renderTo;n(c)||(this.containerWidth=a.getStyle(g,"width"));n(b)||(this.containerHeight=a.getStyle(g,"height"));this.chartWidth=Math.max(0,c||this.containerWidth||
600);this.chartHeight=Math.max(0,a.relativeLength(b,this.chartWidth)||(1<this.containerHeight?this.containerHeight:400))},temporaryDisplay:function(b){var c=this.renderTo;if(b)for(;c&&c.style;)c.hcOrigStyle&&(a.css(c,c.hcOrigStyle),delete c.hcOrigStyle),c.hcOrigDetached&&(p.body.removeChild(c),c.hcOrigDetached=!1),c=c.parentNode;else for(;c&&c.style;){p.body.contains(c)||c.parentNode||(c.hcOrigDetached=!0,p.body.appendChild(c));if("none"===a.getStyle(c,"display",!1)||c.hcOricDetached)c.hcOrigStyle=
{display:c.style.display,height:c.style.height,overflow:c.style.overflow},b={display:"block",overflow:"hidden"},c!==this.renderTo&&(b.height=0),a.css(c,b),c.offsetWidth||c.style.setProperty("display","block","important");c=c.parentNode;if(c===p.body)break}},setClassName:function(a){this.container.className="highcharts-container "+(a||"")},getContainer:function(){var c,g=this.options,e=g.chart,f,k;c=this.renderTo;var t=a.uniqueKey(),h;c||(this.renderTo=c=e.renderTo);q(c)&&(this.renderTo=c=p.getElementById(c));
c||a.error(13,!0);f=x(C(c,"data-highcharts-chart"));b(f)&&r[f]&&r[f].hasRendered&&r[f].destroy();C(c,"data-highcharts-chart",this.index);c.innerHTML="";e.skipClone||c.offsetWidth||this.temporaryDisplay();this.getChartSize();f=this.chartWidth;k=this.chartHeight;this.container=c=d("div",{id:t},void 0,c);this._cursor=c.style.cursor;this.renderer=new (a[e.renderer]||a.Renderer)(c,f,k,null,e.forExport,g.exporting&&g.exporting.allowHTML);this.setClassName(e.className);for(h in g.defs)this.renderer.definition(g.defs[h]);
this.renderer.chartIndex=this.index},getMargins:function(a){var b=this.spacing,c=this.margin,g=this.titleOffset;this.resetMargins();g&&!n(c[0])&&(this.plotTop=Math.max(this.plotTop,g+this.options.title.margin+b[0]));this.legend&&this.legend.display&&this.legend.adjustMargins(c,b);this.extraMargin&&(this[this.extraMargin.type]=(this[this.extraMargin.type]||0)+this.extraMargin.value);this.adjustPlotArea&&this.adjustPlotArea();a||this.getAxisMargins()},getAxisMargins:function(){var a=this,b=a.axisOffset=
[0,0,0,0],g=a.margin;a.hasCartesianSeries&&c(a.axes,function(a){a.visible&&a.getOffset()});c(K,function(c,e){n(g[e])||(a[c]+=b[e])});a.setChartSize()},reflow:function(b){var c=this,e=c.options.chart,l=c.renderTo,f=n(e.width)&&n(e.height),d=e.width||a.getStyle(l,"width"),e=e.height||a.getStyle(l,"height"),l=b?b.target:w;if(!f&&!c.isPrinting&&d&&e&&(l===w||l===p)){if(d!==c.containerWidth||e!==c.containerHeight)clearTimeout(c.reflowTimeout),c.reflowTimeout=g(function(){c.container&&c.setSize(void 0,
void 0,!1)},b?100:0);c.containerWidth=d;c.containerHeight=e}},initReflow:function(){var a=this,b;b=z(w,"resize",function(b){a.reflow(b)});z(a,"destroy",b)},setSize:function(b,e,f){var l=this,d=l.renderer;l.isResizing+=1;a.setAnimation(f,l);l.oldChartHeight=l.chartHeight;l.oldChartWidth=l.chartWidth;void 0!==b&&(l.options.chart.width=b);void 0!==e&&(l.options.chart.height=e);l.getChartSize();l.setChartSize(!0);d.setSize(l.chartWidth,l.chartHeight,f);c(l.axes,function(a){a.isDirty=!0;a.setScale()});
l.isDirtyLegend=!0;l.isDirtyBox=!0;l.layOutTitles();l.getMargins();l.redraw(f);l.oldChartHeight=null;k(l,"resize");g(function(){l&&k(l,"endResize",null,function(){--l.isResizing})},F(void 0).duration)},setChartSize:function(a){var b=this.inverted,g=this.renderer,e=this.chartWidth,l=this.chartHeight,f=this.options.chart,d=this.spacing,k=this.clipOffset,t,h,m,x;this.plotLeft=t=Math.round(this.plotLeft);this.plotTop=h=Math.round(this.plotTop);this.plotWidth=m=Math.max(0,Math.round(e-t-this.marginRight));
this.plotHeight=x=Math.max(0,Math.round(l-h-this.marginBottom));this.plotSizeX=b?x:m;this.plotSizeY=b?m:x;this.plotBorderWidth=f.plotBorderWidth||0;this.spacingBox=g.spacingBox={x:d[3],y:d[0],width:e-d[3]-d[1],height:l-d[0]-d[2]};this.plotBox=g.plotBox={x:t,y:h,width:m,height:x};e=2*Math.floor(this.plotBorderWidth/2);b=Math.ceil(Math.max(e,k[3])/2);g=Math.ceil(Math.max(e,k[0])/2);this.clipBox={x:b,y:g,width:Math.floor(this.plotSizeX-Math.max(e,k[1])/2-b),height:Math.max(0,Math.floor(this.plotSizeY-
Math.max(e,k[2])/2-g))};a||c(this.axes,function(a){a.setAxisSize();a.setAxisTranslation()})},resetMargins:function(){var a=this,b=a.options.chart;c(["margin","spacing"],function(g){var e=b[g],l=u(e)?e:[e,e,e,e];c(["Top","Right","Bottom","Left"],function(c,e){a[g][e]=t(b[g+c],l[e])})});c(K,function(b,c){a[b]=t(a.margin[c],a.spacing[c])});a.axisOffset=[0,0,0,0];a.clipOffset=[0,0,0,0]},drawChartBox:function(){var a=this.options.chart,b=this.renderer,c=this.chartWidth,g=this.chartHeight,e=this.chartBackground,
f=this.plotBackground,d=this.plotBorder,k,t,h=this.plotLeft,m=this.plotTop,x=this.plotWidth,n=this.plotHeight,q=this.plotBox,G=this.clipRect,u=this.clipBox,B="animate";e||(this.chartBackground=e=b.rect().addClass("highcharts-background").add(),B="attr");k=t=e.strokeWidth();e[B]({x:t/2,y:t/2,width:c-t-k%2,height:g-t-k%2,r:a.borderRadius});B="animate";f||(B="attr",this.plotBackground=f=b.rect().addClass("highcharts-plot-background").add());f[B](q);G?G.animate({width:u.width,height:u.height}):this.clipRect=
b.clipRect(u);B="animate";d||(B="attr",this.plotBorder=d=b.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add());d[B](d.crisp({x:h,y:m,width:x,height:n},-d.strokeWidth()));this.isDirtyBox=!1},propFromSeries:function(){var a=this,b=a.options.chart,g,e=a.options.series,f,d;c(["inverted","angular","polar"],function(c){g=B[b.type||b.defaultSeriesType];d=b[c]||g&&g.prototype[c];for(f=e&&e.length;!d&&f--;)(g=B[e[f].type])&&g.prototype[c]&&(d=!0);a[c]=d})},linkSeries:function(){var a=this,b=
a.series;c(b,function(a){a.linkedSeries.length=0});c(b,function(b){var c=b.options.linkedTo;q(c)&&(c=":previous"===c?a.series[b.index-1]:a.get(c))&&c.linkedParent!==b&&(c.linkedSeries.push(b),b.linkedParent=c,b.visible=t(b.options.visible,c.options.visible,b.visible))})},renderSeries:function(){c(this.series,function(a){a.translate();a.render()})},renderLabels:function(){var a=this,b=a.options.labels;b.items&&c(b.items,function(c){var g=f(b.style,c.style),e=x(g.left)+a.plotLeft,l=x(g.top)+a.plotTop+
12;delete g.left;delete g.top;a.renderer.text(c.html,e,l).attr({zIndex:2}).css(g).add()})},render:function(){var a=this.axes,b=this.renderer,g=this.options,e,f,d;this.setTitle();this.legend=new D(this,g.legend);this.getStacks&&this.getStacks();this.getMargins(!0);this.setChartSize();g=this.plotWidth;e=this.plotHeight=Math.max(this.plotHeight-21,0);c(a,function(a){a.setScale()});this.getAxisMargins();f=1.1<g/this.plotWidth;d=1.05<e/this.plotHeight;if(f||d)c(a,function(a){(a.horiz&&f||!a.horiz&&d)&&
a.setTickInterval(!0)}),this.getMargins();this.drawChartBox();this.hasCartesianSeries&&c(a,function(a){a.visible&&a.render()});this.seriesGroup||(this.seriesGroup=b.g("series-group").attr({zIndex:3}).add());this.renderSeries();this.renderLabels();this.addCredits();this.setResponsive&&this.setResponsive();this.hasRendered=!0},addCredits:function(a){var b=this;a=E(!0,this.options.credits,a);a.enabled&&!this.credits&&(this.credits=this.renderer.text(a.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",
function(){a.href&&(w.location.href=a.href)}).attr({align:a.position.align,zIndex:8}).add().align(a.position),this.credits.update=function(a){b.credits=b.credits.destroy();b.addCredits(a)})},destroy:function(){var b=this,g=b.axes,e=b.series,f=b.container,d,t=f&&f.parentNode;k(b,"destroy");b.renderer.forExport?a.erase(r,b):r[b.index]=void 0;a.chartCount--;b.renderTo.removeAttribute("data-highcharts-chart");G(b);for(d=g.length;d--;)g[d]=g[d].destroy();this.scroller&&this.scroller.destroy&&this.scroller.destroy();
for(d=e.length;d--;)e[d]=e[d].destroy();c("title subtitle chartBackground plotBackground plotBGImage plotBorder seriesGroup clipRect credits pointer rangeSelector legend resetZoomButton tooltip renderer".split(" "),function(a){var c=b[a];c&&c.destroy&&(b[a]=c.destroy())});f&&(f.innerHTML="",G(f),t&&v(f));H(b,function(a,c){delete b[c]})},firstRender:function(){var a=this,b=a.options;if(!a.isReadyToRender||a.isReadyToRender()){a.getContainer();k(a,"init");a.resetMargins();a.setChartSize();a.propFromSeries();
a.getAxes();c(b.series||[],function(b){a.initSeries(b)});a.linkSeries();k(a,"beforeRender");y&&(a.pointer=new y(a,b));a.render();if(!a.renderer.imgCount&&a.onload)a.onload();a.temporaryDisplay(!0)}},onload:function(){c([this.callback].concat(this.callbacks),function(a){a&&void 0!==this.index&&a.apply(this,[this])},this);k(this,"load");k(this,"render");n(this.index)&&!1!==this.options.chart.reflow&&this.initReflow();this.onload=null}})})(M);(function(a){var z,F=a.each,C=a.extend,p=a.erase,h=a.fireEvent,
d=a.format,m=a.isArray,v=a.isNumber,r=a.pick,n=a.removeEvent;a.Point=z=function(){};a.Point.prototype={init:function(a,f,d){var c=a.chart.options.chart.colorCount;this.series=a;this.applyOptions(f,d);a.options.colorByPoint?(f=a.colorCounter,a.colorCounter++,a.colorCounter===c&&(a.colorCounter=0)):f=a.colorIndex;this.colorIndex=r(this.colorIndex,f);a.chart.pointCount++;return this},applyOptions:function(a,f){var c=this.series,d=c.options.pointValKey||c.pointValKey;a=z.prototype.optionsToObject.call(this,
a);C(this,a);this.options=this.options?C(this.options,a):a;a.group&&delete this.group;d&&(this.y=this[d]);this.isNull=r(this.isValid&&!this.isValid(),null===this.x||!v(this.y,!0));this.selected&&(this.state="select");"name"in this&&void 0===f&&c.xAxis&&c.xAxis.hasNames&&(this.x=c.xAxis.nameToX(this));void 0===this.x&&c&&(this.x=void 0===f?c.autoIncrement(this):f);return this},optionsToObject:function(a){var c={},d=this.series,k=d.options.keys,e=k||d.pointArrayMap||["y"],b=e.length,h=0,n=0;if(v(a)||
null===a)c[e[0]]=a;else if(m(a))for(!k&&a.length>b&&(d=typeof a[0],"string"===d?c.name=a[0]:"number"===d&&(c.x=a[0]),h++);n<b;)k&&void 0===a[h]||(c[e[n]]=a[h]),h++,n++;else"object"===typeof a&&(c=a,a.dataLabels&&(d._hasPointLabels=!0),a.marker&&(d._hasPointMarkers=!0));return c},getClassName:function(){return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+
this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+(this.zone&&this.zone.className?" "+this.zone.className.replace("highcharts-negative",""):"")},getZone:function(){var a=this.series,d=a.zones,a=a.zoneAxis||"y",h=0,k;for(k=d[h];this[a]>=k.value;)k=d[++h];k&&k.color&&!this.options.color&&(this.color=k.color);return k},destroy:function(){var a=this.series.chart,d=a.hoverPoints,h;a.pointCount--;d&&(this.setState(),p(d,this),d.length||(a.hoverPoints=null));if(this===a.hoverPoint)this.onMouseOut();
if(this.graphic||this.dataLabel)n(this),this.destroyElements();this.legendItem&&a.legend.destroyItem(this);for(h in this)this[h]=null},destroyElements:function(){for(var a=["graphic","dataLabel","dataLabelUpper","connector","shadowGroup"],d,h=6;h--;)d=a[h],this[d]&&(this[d]=this[d].destroy())},getLabelConfig:function(){return{x:this.category,y:this.y,color:this.color,colorIndex:this.colorIndex,key:this.name||this.category,series:this.series,point:this,percentage:this.percentage,total:this.total||
this.stackTotal}},tooltipFormatter:function(a){var c=this.series,h=c.tooltipOptions,k=r(h.valueDecimals,""),e=h.valuePrefix||"",b=h.valueSuffix||"";F(c.pointArrayMap||["y"],function(c){c="{point."+c;if(e||b)a=a.replace(c+"}",e+c+"}"+b);a=a.replace(c+"}",c+":,."+k+"f}")});return d(a,{point:this,series:this.series},c.chart.time)},firePointEvent:function(a,d,m){var c=this,e=this.series.options;(e.point.events[a]||c.options&&c.options.events&&c.options.events[a])&&this.importEvents();"click"===a&&e.allowPointSelect&&
(m=function(a){c.select&&c.select(null,a.ctrlKey||a.metaKey||a.shiftKey)});h(this,a,d,m)},visible:!0}})(M);(function(a){var z=a.addEvent,F=a.animObject,C=a.arrayMax,p=a.arrayMin,h=a.correctFloat,d=a.defaultOptions,m=a.defined,v=a.each,r=a.erase,n=a.extend,c=a.fireEvent,f=a.grep,A=a.isArray,k=a.isNumber,e=a.isString,b=a.merge,u=a.objectEach,q=a.pick,D=a.removeEvent,K=a.splat,E=a.SVGElement,H=a.syncTimeout,y=a.win;a.Series=a.seriesType("line",null,{allowPointSelect:!1,showCheckbox:!1,animation:{duration:1E3},
events:{},marker:{enabledThreshold:2,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:50},enabled:!0,radiusPlus:2}}},point:{events:{}},dataLabels:{align:"center",formatter:function(){return null===this.y?"":a.numberFormat(this.y,-1)},verticalAlign:"bottom",x:0,y:0,padding:5},cropThreshold:300,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:50},lineWidthPlus:1,marker:{},halo:{size:10}},select:{marker:{}}},stickyTracking:!0,turboThreshold:1E3,
findNearestPointBy:"x"},{isCartesian:!0,pointClass:a.Point,sorted:!0,requireSorting:!0,directTouch:!1,axisTypes:["xAxis","yAxis"],colorCounter:0,parallelArrays:["x","y"],coll:"series",init:function(a,b){var c=this,e,d=a.series,g;c.chart=a;c.options=b=c.setOptions(b);c.linkedSeries=[];c.bindAxes();n(c,{name:b.name,state:"",visible:!1!==b.visible,selected:!0===b.selected});e=b.events;u(e,function(a,b){z(c,b,a)});if(e&&e.click||b.point&&b.point.events&&b.point.events.click||b.allowPointSelect)a.runTrackerClick=
!0;c.getColor();c.getSymbol();v(c.parallelArrays,function(a){c[a+"Data"]=[]});c.setData(b.data,!1);c.isCartesian&&(a.hasCartesianSeries=!0);d.length&&(g=d[d.length-1]);c._i=q(g&&g._i,-1)+1;a.orderSeries(this.insert(d))},insert:function(a){var b=this.options.index,c;if(k(b)){for(c=a.length;c--;)if(b>=q(a[c].options.index,a[c]._i)){a.splice(c+1,0,this);break}-1===c&&a.unshift(this);c+=1}else a.push(this);return q(c,a.length-1)},bindAxes:function(){var b=this,c=b.options,e=b.chart,d;v(b.axisTypes||[],
function(f){v(e[f],function(a){d=a.options;if(c[f]===d.index||void 0!==c[f]&&c[f]===d.id||void 0===c[f]&&0===d.index)b.insert(a.series),b[f]=a,a.isDirty=!0});b[f]||b.optionalAxis===f||a.error(18,!0)})},updateParallelArrays:function(a,b){var c=a.series,e=arguments,d=k(b)?function(g){var e="y"===g&&c.toYData?c.toYData(a):a[g];c[g+"Data"][b]=e}:function(a){Array.prototype[b].apply(c[a+"Data"],Array.prototype.slice.call(e,2))};v(c.parallelArrays,d)},autoIncrement:function(){var a=this.options,b=this.xIncrement,
c,e=a.pointIntervalUnit,d=this.chart.time,b=q(b,a.pointStart,0);this.pointInterval=c=q(this.pointInterval,a.pointInterval,1);e&&(a=new d.Date(b),"day"===e?d.set("Date",a,d.get("Date",a)+c):"month"===e?d.set("Month",a,d.get("Month",a)+c):"year"===e&&d.set("FullYear",a,d.get("FullYear",a)+c),c=a.getTime()-b);this.xIncrement=b+c;return b},setOptions:function(a){var c=this.chart,e=c.options,f=e.plotOptions,k=(c.userOptions||{}).plotOptions||{},g=f[this.type];this.userOptions=a;c=b(g,f.series,a);this.tooltipOptions=
b(d.tooltip,d.plotOptions.series&&d.plotOptions.series.tooltip,d.plotOptions[this.type].tooltip,e.tooltip.userOptions,f.series&&f.series.tooltip,f[this.type].tooltip,a.tooltip);this.stickyTracking=q(a.stickyTracking,k[this.type]&&k[this.type].stickyTracking,k.series&&k.series.stickyTracking,this.tooltipOptions.shared&&!this.noSharedTooltip?!0:c.stickyTracking);null===g.marker&&delete c.marker;this.zoneAxis=c.zoneAxis;a=this.zones=(c.zones||[]).slice();!c.negativeColor&&!c.negativeFillColor||c.zones||
a.push({value:c[this.zoneAxis+"Threshold"]||c.threshold||0,className:"highcharts-negative"});a.length&&m(a[a.length-1].value)&&a.push({});return c},getName:function(){return this.name||"Series "+(this.index+1)},getCyclic:function(a,b,c){var e,d=this.chart,g=this.userOptions,f=a+"Index",k=a+"Counter",l=c?c.length:q(d.options.chart[a+"Count"],d[a+"Count"]);b||(e=q(g[f],g["_"+f]),m(e)||(d.series.length||(d[k]=0),g["_"+f]=e=d[k]%l,d[k]+=1),c&&(b=c[e]));void 0!==e&&(this[f]=e);this[a]=b},getColor:function(){this.getCyclic("color")},
getSymbol:function(){this.getCyclic("symbol",this.options.marker.symbol,this.chart.options.symbols)},drawLegendSymbol:a.LegendSymbolMixin.drawLineMarker,setData:function(b,c,d,f){var h=this,g=h.points,t=g&&g.length||0,m,l=h.options,n=h.chart,x=null,u=h.xAxis,p=l.turboThreshold,y=this.xData,B=this.yData,G=(m=h.pointArrayMap)&&m.length;b=b||[];m=b.length;c=q(c,!0);if(!1!==f&&m&&t===m&&!h.cropped&&!h.hasGroupedData&&h.visible)v(b,function(a,b){g[b].update&&a!==l.data[b]&&g[b].update(a,!1,null,!1)});
else{h.xIncrement=null;h.colorCounter=0;v(this.parallelArrays,function(a){h[a+"Data"].length=0});if(p&&m>p){for(d=0;null===x&&d<m;)x=b[d],d++;if(k(x))for(d=0;d<m;d++)y[d]=this.autoIncrement(),B[d]=b[d];else if(A(x))if(G)for(d=0;d<m;d++)x=b[d],y[d]=x[0],B[d]=x.slice(1,G+1);else for(d=0;d<m;d++)x=b[d],y[d]=x[0],B[d]=x[1];else a.error(12)}else for(d=0;d<m;d++)void 0!==b[d]&&(x={series:h},h.pointClass.prototype.applyOptions.apply(x,[b[d]]),h.updateParallelArrays(x,d));B&&e(B[0])&&a.error(14,!0);h.data=
[];h.options.data=h.userOptions.data=b;for(d=t;d--;)g[d]&&g[d].destroy&&g[d].destroy();u&&(u.minRange=u.userMinRange);h.isDirty=n.isDirtyBox=!0;h.isDirtyData=!!g;d=!1}"point"===l.legendType&&(this.processData(),this.generatePoints());c&&n.redraw(d)},processData:function(b){var c=this.xData,e=this.yData,d=c.length,f;f=0;var g,k,h=this.xAxis,l,t=this.options;l=t.cropThreshold;var m=this.getExtremesFromAll||t.getExtremesFromAll,n=this.isCartesian,t=h&&h.val2lin,q=h&&h.isLog,u=this.requireSorting,p,y;
if(n&&!this.isDirty&&!h.isDirty&&!this.yAxis.isDirty&&!b)return!1;h&&(b=h.getExtremes(),p=b.min,y=b.max);if(n&&this.sorted&&!m&&(!l||d>l||this.forceCrop))if(c[d-1]<p||c[0]>y)c=[],e=[];else if(c[0]<p||c[d-1]>y)f=this.cropData(this.xData,this.yData,p,y),c=f.xData,e=f.yData,f=f.start,g=!0;for(l=c.length||1;--l;)d=q?t(c[l])-t(c[l-1]):c[l]-c[l-1],0<d&&(void 0===k||d<k)?k=d:0>d&&u&&(a.error(15),u=!1);this.cropped=g;this.cropStart=f;this.processedXData=c;this.processedYData=e;this.closestPointRange=k},cropData:function(a,
b,c,e){var d=a.length,g=0,f=d,k=q(this.cropShoulder,1),l;for(l=0;l<d;l++)if(a[l]>=c){g=Math.max(0,l-k);break}for(c=l;c<d;c++)if(a[c]>e){f=c+k;break}return{xData:a.slice(g,f),yData:b.slice(g,f),start:g,end:f}},generatePoints:function(){var a=this.options,b=a.data,c=this.data,e,d=this.processedXData,g=this.processedYData,f=this.pointClass,k=d.length,l=this.cropStart||0,h,m=this.hasGroupedData,a=a.keys,n,q=[],u;c||m||(c=[],c.length=b.length,c=this.data=c);a&&m&&(this.options.keys=!1);for(u=0;u<k;u++)h=
l+u,m?(n=(new f).init(this,[d[u]].concat(K(g[u]))),n.dataGroup=this.groupMap[u]):(n=c[h])||void 0===b[h]||(c[h]=n=(new f).init(this,b[h],d[u])),n&&(n.index=h,q[u]=n);this.options.keys=a;if(c&&(k!==(e=c.length)||m))for(u=0;u<e;u++)u!==l||m||(u+=k),c[u]&&(c[u].destroyElements(),c[u].plotX=void 0);this.data=c;this.points=q},getExtremes:function(a){var b=this.yAxis,c=this.processedXData,e,d=[],g=0;e=this.xAxis.getExtremes();var f=e.min,h=e.max,l,t,m,n;a=a||this.stackedYData||this.processedYData||[];e=
a.length;for(n=0;n<e;n++)if(t=c[n],m=a[n],l=(k(m,!0)||A(m))&&(!b.positiveValuesOnly||m.length||0<m),t=this.getExtremesFromAll||this.options.getExtremesFromAll||this.cropped||(c[n+1]||t)>=f&&(c[n-1]||t)<=h,l&&t)if(l=m.length)for(;l--;)"number"===typeof m[l]&&(d[g++]=m[l]);else d[g++]=m;this.dataMin=p(d);this.dataMax=C(d)},translate:function(){this.processedXData||this.processData();this.generatePoints();var a=this.options,b=a.stacking,c=this.xAxis,e=c.categories,d=this.yAxis,g=this.points,f=g.length,
n=!!this.modifyValue,l=a.pointPlacement,u="between"===l||k(l),p=a.threshold,y=a.startFromThreshold?p:0,r,E,H,v,A=Number.MAX_VALUE;"between"===l&&(l=.5);k(l)&&(l*=q(a.pointRange||c.pointRange));for(a=0;a<f;a++){var D=g[a],z=D.x,K=D.y;E=D.low;var C=b&&d.stacks[(this.negStacks&&K<(y?0:p)?"-":"")+this.stackKey],F;d.positiveValuesOnly&&null!==K&&0>=K&&(D.isNull=!0);D.plotX=r=h(Math.min(Math.max(-1E5,c.translate(z,0,0,0,1,l,"flags"===this.type)),1E5));b&&this.visible&&!D.isNull&&C&&C[z]&&(v=this.getStackIndicator(v,
z,this.index),F=C[z],K=F.points[v.key],E=K[0],K=K[1],E===y&&v.key===C[z].base&&(E=q(p,d.min)),d.positiveValuesOnly&&0>=E&&(E=null),D.total=D.stackTotal=F.total,D.percentage=F.total&&D.y/F.total*100,D.stackY=K,F.setOffset(this.pointXOffset||0,this.barW||0));D.yBottom=m(E)?Math.min(Math.max(-1E5,d.translate(E,0,1,0,1)),1E5):null;n&&(K=this.modifyValue(K,D));D.plotY=E="number"===typeof K&&Infinity!==K?Math.min(Math.max(-1E5,d.translate(K,0,1,0,1)),1E5):void 0;D.isInside=void 0!==E&&0<=E&&E<=d.len&&0<=
r&&r<=c.len;D.clientX=u?h(c.translate(z,0,0,0,1,l)):r;D.negative=D.y<(p||0);D.category=e&&void 0!==e[D.x]?e[D.x]:D.x;D.isNull||(void 0!==H&&(A=Math.min(A,Math.abs(r-H))),H=r);D.zone=this.zones.length&&D.getZone()}this.closestPointRangePx=A},getValidPoints:function(a,b){var c=this.chart;return f(a||this.points||[],function(a){return b&&!c.isInsidePlot(a.plotX,a.plotY,c.inverted)?!1:!a.isNull})},setClip:function(a){var b=this.chart,c=this.options,e=b.renderer,d=b.inverted,g=this.clipBox,f=g||b.clipBox,
k=this.sharedClipKey||["_sharedClip",a&&a.duration,a&&a.easing,f.height,c.xAxis,c.yAxis].join(),l=b[k],h=b[k+"m"];l||(a&&(f.width=0,d&&(f.x=b.plotSizeX),b[k+"m"]=h=e.clipRect(d?b.plotSizeX+99:-99,d?-b.plotLeft:-b.plotTop,99,d?b.chartWidth:b.chartHeight)),b[k]=l=e.clipRect(f),l.count={length:0});a&&!l.count[this.index]&&(l.count[this.index]=!0,l.count.length+=1);!1!==c.clip&&(this.group.clip(a||g?l:b.clipRect),this.markerGroup.clip(h),this.sharedClipKey=k);a||(l.count[this.index]&&(delete l.count[this.index],
--l.count.length),0===l.count.length&&k&&b[k]&&(g||(b[k]=b[k].destroy()),b[k+"m"]&&(b[k+"m"]=b[k+"m"].destroy())))},animate:function(a){var b=this.chart,c=F(this.options.animation),e;a?this.setClip(c):(e=this.sharedClipKey,(a=b[e])&&a.animate({width:b.plotSizeX,x:0},c),b[e+"m"]&&b[e+"m"].animate({width:b.plotSizeX+99,x:0},c),this.animate=null)},afterAnimate:function(){this.setClip();c(this,"afterAnimate");this.finishedAnimating=!0},drawPoints:function(){var a=this.points,b=this.chart,c,e,d,g,f=this.options.marker,
k,l,h,m=this[this.specialGroup]||this.markerGroup,n,u=q(f.enabled,this.xAxis.isRadial?!0:null,this.closestPointRangePx>=f.enabledThreshold*f.radius);if(!1!==f.enabled||this._hasPointMarkers)for(c=0;c<a.length;c++)e=a[c],g=e.graphic,k=e.marker||{},l=!!e.marker,d=u&&void 0===k.enabled||k.enabled,h=e.isInside,d&&!e.isNull?(d=q(k.symbol,this.symbol),n=this.markerAttribs(e,e.selected&&"select"),g?g[h?"show":"hide"](!0).animate(n):h&&(0<n.width||e.hasImage)&&(e.graphic=g=b.renderer.symbol(d,n.x,n.y,n.width,
n.height,l?k:f).add(m)),g&&g.addClass(e.getClassName(),!0)):g&&(e.graphic=g.destroy())},markerAttribs:function(a,b){var c=this.options.marker,e=a.marker||{},d=e.symbol||c.symbol,g=q(e.radius,c.radius);b&&(c=c.states[b],b=e.states&&e.states[b],g=q(b&&b.radius,c&&c.radius,g+(c&&c.radiusPlus||0)));a.hasImage=d&&0===d.indexOf("url");a.hasImage&&(g=0);a={x:Math.floor(a.plotX)-g,y:a.plotY-g};g&&(a.width=a.height=2*g);return a},destroy:function(){var a=this,b=a.chart,e=/AppleWebKit\/533/.test(y.navigator.userAgent),
d,f,g=a.data||[],k,h;c(a,"destroy");D(a);v(a.axisTypes||[],function(b){(h=a[b])&&h.series&&(r(h.series,a),h.isDirty=h.forceRedraw=!0)});a.legendItem&&a.chart.legend.destroyItem(a);for(f=g.length;f--;)(k=g[f])&&k.destroy&&k.destroy();a.points=null;clearTimeout(a.animationTimeout);u(a,function(a,b){a instanceof E&&!a.survive&&(d=e&&"group"===b?"hide":"destroy",a[d]())});b.hoverSeries===a&&(b.hoverSeries=null);r(b.series,a);b.orderSeries();u(a,function(b,c){delete a[c]})},getGraphPath:function(a,b,c){var e=
this,d=e.options,g=d.step,f,k=[],l=[],h;a=a||e.points;(f=a.reversed)&&a.reverse();(g={right:1,center:2}[g]||g&&3)&&f&&(g=4-g);!d.connectNulls||b||c||(a=this.getValidPoints(a));v(a,function(f,t){var n=f.plotX,q=f.plotY,u=a[t-1];(f.leftCliff||u&&u.rightCliff)&&!c&&(h=!0);f.isNull&&!m(b)&&0<t?h=!d.connectNulls:f.isNull&&!b?h=!0:(0===t||h?t=["M",f.plotX,f.plotY]:e.getPointSpline?t=e.getPointSpline(a,f,t):g?(t=1===g?["L",u.plotX,q]:2===g?["L",(u.plotX+n)/2,u.plotY,"L",(u.plotX+n)/2,q]:["L",n,u.plotY],
t.push("L",n,q)):t=["L",n,q],l.push(f.x),g&&l.push(f.x),k.push.apply(k,t),h=!1)});k.xMap=l;return e.graphPath=k},drawGraph:function(){var a=this,b=(this.gappedPath||this.getGraphPath).call(this),c=[["graph","highcharts-graph"]];v(this.zones,function(a,b){c.push(["zone-graph-"+b,"highcharts-graph highcharts-zone-graph-"+b+" "+(a.className||"")])});v(c,function(c,e){e=c[0];var g=a[e];g?(g.endX=a.preventGraphAnimation?null:b.xMap,g.animate({d:b})):b.length&&(a[e]=a.chart.renderer.path(b).addClass(c[1]).attr({zIndex:1}).add(a.group));
g&&(g.startX=b.xMap,g.isArea=b.isArea)})},applyZones:function(){var a=this,b=this.chart,c=b.renderer,e=this.zones,d,g,f=this.clips||[],k,l=this.graph,h=this.area,m=Math.max(b.chartWidth,b.chartHeight),n=this[(this.zoneAxis||"y")+"Axis"],u,p,y=b.inverted,r,E,H,D,A=!1;e.length&&(l||h)&&n&&void 0!==n.min&&(p=n.reversed,r=n.horiz,l&&l.hide(),h&&h.hide(),u=n.getExtremes(),v(e,function(e,t){d=p?r?b.plotWidth:0:r?0:n.toPixels(u.min);d=Math.min(Math.max(q(g,d),0),m);g=Math.min(Math.max(Math.round(n.toPixels(q(e.value,
u.max),!0)),0),m);A&&(d=g=n.toPixels(u.max));E=Math.abs(d-g);H=Math.min(d,g);D=Math.max(d,g);n.isXAxis?(k={x:y?D:H,y:0,width:E,height:m},r||(k.x=b.plotHeight-k.x)):(k={x:0,y:y?D:H,width:m,height:E},r&&(k.y=b.plotWidth-k.y));f[t]?f[t].animate(k):(f[t]=c.clipRect(k),l&&a["zone-graph-"+t].clip(f[t]),h&&a["zone-area-"+t].clip(f[t]));A=e.value>u.max}),this.clips=f)},invertGroups:function(a){function b(){v(["group","markerGroup"],function(b){c[b]&&(e.renderer.isVML&&c[b].attr({width:c.yAxis.len,height:c.xAxis.len}),
c[b].width=c.yAxis.len,c[b].height=c.xAxis.len,c[b].invert(a))})}var c=this,e=c.chart,d;c.xAxis&&(d=z(e,"resize",b),z(c,"destroy",d),b(a),c.invertGroups=b)},plotGroup:function(a,b,c,e,d){var g=this[a],f=!g;f&&(this[a]=g=this.chart.renderer.g().attr({zIndex:e||.1}).add(d));g.addClass("highcharts-"+b+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(m(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(g.hasClass("highcharts-tracker")?" highcharts-tracker":
""),!0);g.attr({visibility:c})[f?"attr":"animate"](this.getPlotBox());return g},getPlotBox:function(){var a=this.chart,b=this.xAxis,c=this.yAxis;a.inverted&&(b=c,c=this.xAxis);return{translateX:b?b.left:a.plotLeft,translateY:c?c.top:a.plotTop,scaleX:1,scaleY:1}},render:function(){var a=this,b=a.chart,c,e=a.options,d=!!a.animate&&b.renderer.isSVG&&F(e.animation).duration,g=a.visible?"inherit":"hidden",f=e.zIndex,k=a.hasRendered,l=b.seriesGroup,h=b.inverted;c=a.plotGroup("group","series",g,f,l);a.markerGroup=
a.plotGroup("markerGroup","markers",g,f,l);d&&a.animate(!0);c.inverted=a.isCartesian?h:!1;a.drawGraph&&(a.drawGraph(),a.applyZones());a.drawDataLabels&&a.drawDataLabels();a.visible&&a.drawPoints();a.drawTracker&&!1!==a.options.enableMouseTracking&&a.drawTracker();a.invertGroups(h);!1===e.clip||a.sharedClipKey||k||c.clip(b.clipRect);d&&a.animate();k||(a.animationTimeout=H(function(){a.afterAnimate()},d));a.isDirty=!1;a.hasRendered=!0},redraw:function(){var a=this.chart,b=this.isDirty||this.isDirtyData,
c=this.group,e=this.xAxis,d=this.yAxis;c&&(a.inverted&&c.attr({width:a.plotWidth,height:a.plotHeight}),c.animate({translateX:q(e&&e.left,a.plotLeft),translateY:q(d&&d.top,a.plotTop)}));this.translate();this.render();b&&delete this.kdTree},kdAxisArray:["clientX","plotY"],searchPoint:function(a,b){var c=this.xAxis,e=this.yAxis,d=this.chart.inverted;return this.searchKDTree({clientX:d?c.len-a.chartY+c.pos:a.chartX-c.pos,plotY:d?e.len-a.chartX+e.pos:a.chartY-e.pos},b)},buildKDTree:function(){function a(c,
e,d){var g,f;if(f=c&&c.length)return g=b.kdAxisArray[e%d],c.sort(function(a,b){return a[g]-b[g]}),f=Math.floor(f/2),{point:c[f],left:a(c.slice(0,f),e+1,d),right:a(c.slice(f+1),e+1,d)}}this.buildingKdTree=!0;var b=this,c=-1<b.options.findNearestPointBy.indexOf("y")?2:1;delete b.kdTree;H(function(){b.kdTree=a(b.getValidPoints(null,!b.directTouch),c,c);b.buildingKdTree=!1},b.options.kdNow?0:1)},searchKDTree:function(a,b){function c(a,b,k,h){var l=b.point,n=e.kdAxisArray[k%h],q,t,u=l;t=m(a[d])&&m(l[d])?
Math.pow(a[d]-l[d],2):null;q=m(a[g])&&m(l[g])?Math.pow(a[g]-l[g],2):null;q=(t||0)+(q||0);l.dist=m(q)?Math.sqrt(q):Number.MAX_VALUE;l.distX=m(t)?Math.sqrt(t):Number.MAX_VALUE;n=a[n]-l[n];q=0>n?"left":"right";t=0>n?"right":"left";b[q]&&(q=c(a,b[q],k+1,h),u=q[f]<u[f]?q:l);b[t]&&Math.sqrt(n*n)<u[f]&&(a=c(a,b[t],k+1,h),u=a[f]<u[f]?a:u);return u}var e=this,d=this.kdAxisArray[0],g=this.kdAxisArray[1],f=b?"distX":"dist";b=-1<e.options.findNearestPointBy.indexOf("y")?2:1;this.kdTree||this.buildingKdTree||
this.buildKDTree();if(this.kdTree)return c(a,this.kdTree,b,b)}})})(M);(function(a){var z=a.Axis,F=a.Chart,C=a.correctFloat,p=a.defined,h=a.destroyObjectProperties,d=a.each,m=a.format,v=a.objectEach,r=a.pick,n=a.Series;a.StackItem=function(a,d,h,k,e){var b=a.chart.inverted;this.axis=a;this.isNegative=h;this.options=d;this.x=k;this.total=null;this.points={};this.stack=e;this.rightCliff=this.leftCliff=0;this.alignOptions={align:d.align||(b?h?"left":"right":"center"),verticalAlign:d.verticalAlign||(b?
"middle":h?"bottom":"top"),y:r(d.y,b?4:h?14:-6),x:r(d.x,b?h?-6:6:0)};this.textAlign=d.textAlign||(b?h?"right":"left":"center")};a.StackItem.prototype={destroy:function(){h(this,this.axis)},render:function(a){var c=this.axis.chart,d=this.options,k=d.format,k=k?m(k,this,c.time):d.formatter.call(this);this.label?this.label.attr({text:k,visibility:"hidden"}):this.label=c.renderer.text(k,null,null,d.useHTML).css(d.style).attr({align:this.textAlign,rotation:d.rotation,visibility:"hidden"}).add(a)},setOffset:function(a,
d){var c=this.axis,f=c.chart,e=c.translate(c.usePercentage?100:this.total,0,0,0,1),c=c.translate(0),c=Math.abs(e-c);a=f.xAxis[0].translate(this.x)+a;e=this.getStackBox(f,this,a,e,d,c);if(d=this.label)d.align(this.alignOptions,null,e),e=d.alignAttr,d[!1===this.options.crop||f.isInsidePlot(e.x,e.y)?"show":"hide"](!0)},getStackBox:function(a,d,h,k,e,b){var c=d.axis.reversed,f=a.inverted;a=a.plotHeight;d=d.isNegative&&!c||!d.isNegative&&c;return{x:f?d?k:k-b:h,y:f?a-h-e:d?a-k-b:a-k,width:f?b:e,height:f?
e:b}}};F.prototype.getStacks=function(){var a=this;d(a.yAxis,function(a){a.stacks&&a.hasVisibleSeries&&(a.oldStacks=a.stacks)});d(a.series,function(c){!c.options.stacking||!0!==c.visible&&!1!==a.options.chart.ignoreHiddenSeries||(c.stackKey=c.type+r(c.options.stack,""))})};z.prototype.buildStacks=function(){var a=this.series,d=r(this.options.reversedStacks,!0),h=a.length,k;if(!this.isXAxis){this.usePercentage=!1;for(k=h;k--;)a[d?k:h-k-1].setStackedPoints();for(k=0;k<h;k++)a[k].modifyStacks()}};z.prototype.renderStackTotals=
function(){var a=this.chart,d=a.renderer,h=this.stacks,k=this.stackTotalGroup;k||(this.stackTotalGroup=k=d.g("stack-labels").attr({visibility:"visible",zIndex:6}).add());k.translate(a.plotLeft,a.plotTop);v(h,function(a){v(a,function(a){a.render(k)})})};z.prototype.resetStacks=function(){var a=this,d=a.stacks;a.isXAxis||v(d,function(c){v(c,function(d,e){d.touched<a.stacksTouched?(d.destroy(),delete c[e]):(d.total=null,d.cumulative=null)})})};z.prototype.cleanStacks=function(){var a;this.isXAxis||(this.oldStacks&&
(a=this.stacks=this.oldStacks),v(a,function(a){v(a,function(a){a.cumulative=a.total})}))};n.prototype.setStackedPoints=function(){if(this.options.stacking&&(!0===this.visible||!1===this.chart.options.chart.ignoreHiddenSeries)){var c=this.processedXData,d=this.processedYData,h=[],k=d.length,e=this.options,b=e.threshold,m=r(e.startFromThreshold&&b,0),n=e.stack,e=e.stacking,v=this.stackKey,z="-"+v,E=this.negStacks,H=this.yAxis,y=H.stacks,t=H.oldStacks,x,G,B,I,g,w,L;H.stacksTouched+=1;for(g=0;g<k;g++)w=
c[g],L=d[g],x=this.getStackIndicator(x,w,this.index),I=x.key,B=(G=E&&L<(m?0:b))?z:v,y[B]||(y[B]={}),y[B][w]||(t[B]&&t[B][w]?(y[B][w]=t[B][w],y[B][w].total=null):y[B][w]=new a.StackItem(H,H.options.stackLabels,G,w,n)),B=y[B][w],null!==L?(B.points[I]=B.points[this.index]=[r(B.cumulative,m)],p(B.cumulative)||(B.base=I),B.touched=H.stacksTouched,0<x.index&&!1===this.singleStacks&&(B.points[I][0]=B.points[this.index+","+w+",0"][0])):B.points[I]=B.points[this.index]=null,"percent"===e?(G=G?v:z,E&&y[G]&&
y[G][w]?(G=y[G][w],B.total=G.total=Math.max(G.total,B.total)+Math.abs(L)||0):B.total=C(B.total+(Math.abs(L)||0))):B.total=C(B.total+(L||0)),B.cumulative=r(B.cumulative,m)+(L||0),null!==L&&(B.points[I].push(B.cumulative),h[g]=B.cumulative);"percent"===e&&(H.usePercentage=!0);this.stackedYData=h;H.oldStacks={}}};n.prototype.modifyStacks=function(){var a=this,f=a.stackKey,h=a.yAxis.stacks,k=a.processedXData,e,b=a.options.stacking;a[b+"Stacker"]&&d([f,"-"+f],function(c){for(var d=k.length,f,m;d--;)if(f=
k[d],e=a.getStackIndicator(e,f,a.index,c),m=(f=h[c]&&h[c][f])&&f.points[e.key])a[b+"Stacker"](m,f,d)})};n.prototype.percentStacker=function(a,d,h){d=d.total?100/d.total:0;a[0]=C(a[0]*d);a[1]=C(a[1]*d);this.stackedYData[h]=a[1]};n.prototype.getStackIndicator=function(a,d,h,k){!p(a)||a.x!==d||k&&a.key!==k?a={x:d,index:0,key:k}:a.index++;a.key=[h,d,a.index].join();return a}})(M);(function(a){var z=a.addEvent,F=a.Axis,C=a.createElement,p=a.css,h=a.defined,d=a.each,m=a.erase,v=a.extend,r=a.fireEvent,n=
a.inArray,c=a.isNumber,f=a.isObject,A=a.isArray,k=a.merge,e=a.objectEach,b=a.pick,u=a.Point,q=a.Series,D=a.seriesTypes,K=a.setAnimation,E=a.splat;v(a.Chart.prototype,{addSeries:function(a,c,e){var d,f=this;a&&(c=b(c,!0),r(f,"addSeries",{options:a},function(){d=f.initSeries(a);f.isDirtyLegend=!0;f.linkSeries();c&&f.redraw(e)}));return d},addAxis:function(a,c,e,d){var f=c?"xAxis":"yAxis",h=this.options;a=k(a,{index:this[f].length,isX:c});c=new F(this,a);h[f]=E(h[f]||{});h[f].push(a);b(e,!0)&&this.redraw(d);
return c},showLoading:function(a){var b=this,c=b.options,e=b.loadingDiv,d=function(){e&&p(e,{left:b.plotLeft+"px",top:b.plotTop+"px",width:b.plotWidth+"px",height:b.plotHeight+"px"})};e||(b.loadingDiv=e=C("div",{className:"highcharts-loading highcharts-loading-hidden"},null,b.container),b.loadingSpan=C("span",{className:"highcharts-loading-inner"},null,e),z(b,"redraw",d));e.className="highcharts-loading";b.loadingSpan.innerHTML=a||c.lang.loading;b.loadingShown=!0;d()},hideLoading:function(){var a=
this.loadingDiv;a&&(a.className="highcharts-loading highcharts-loading-hidden");this.loadingShown=!1},propsRequireDirtyBox:"backgroundColor borderColor borderWidth margin marginTop marginRight marginBottom marginLeft spacing spacingTop spacingRight spacingBottom spacingLeft borderRadius plotBackgroundColor plotBackgroundImage plotBorderColor plotBorderWidth plotShadow shadow".split(" "),propsRequireUpdateSeries:"chart.inverted chart.polar chart.ignoreHiddenSeries chart.type colors plotOptions time tooltip".split(" "),
update:function(a,f,m){var q=this,t={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle"},u=a.chart,p,g,r=[];if(u){k(!0,q.options.chart,u);"className"in u&&q.setClassName(u.className);if("inverted"in u||"polar"in u)q.propFromSeries(),p=!0;"alignTicks"in u&&(p=!0);e(u,function(a,b){-1!==n("chart."+b,q.propsRequireUpdateSeries)&&(g=!0);-1!==n(b,q.propsRequireDirtyBox)&&(q.isDirtyBox=!0)})}a.plotOptions&&k(!0,this.options.plotOptions,a.plotOptions);e(a,function(a,b){if(q[b]&&"function"===typeof q[b].update)q[b].update(a,
!1);else if("function"===typeof q[t[b]])q[t[b]](a);"chart"!==b&&-1!==n(b,q.propsRequireUpdateSeries)&&(g=!0)});d("xAxis yAxis zAxis series colorAxis pane".split(" "),function(b){a[b]&&(d(E(a[b]),function(a,c){(c=h(a.id)&&q.get(a.id)||q[b][c])&&c.coll===b&&(c.update(a,!1),m&&(c.touched=!0));if(!c&&m)if("series"===b)q.addSeries(a,!1).touched=!0;else if("xAxis"===b||"yAxis"===b)q.addAxis(a,"xAxis"===b,!1).touched=!0}),m&&d(q[b],function(a){a.touched?delete a.touched:r.push(a)}))});d(r,function(a){a.remove(!1)});
p&&d(q.axes,function(a){a.update({},!1)});g&&d(q.series,function(a){a.update({},!1)});a.loading&&k(!0,q.options.loading,a.loading);p=u&&u.width;u=u&&u.height;c(p)&&p!==q.chartWidth||c(u)&&u!==q.chartHeight?q.setSize(p,u):b(f,!0)&&q.redraw()},setSubtitle:function(a){this.setTitle(void 0,a)}});v(u.prototype,{update:function(a,c,e,d){function k(){h.applyOptions(a);null===h.y&&g&&(h.graphic=g.destroy());f(a,!0)&&(g&&g.element&&a&&a.marker&&void 0!==a.marker.symbol&&(h.graphic=g.destroy()),a&&a.dataLabels&&
h.dataLabel&&(h.dataLabel=h.dataLabel.destroy()),h.connector&&(h.connector=h.connector.destroy()));n=h.index;m.updateParallelArrays(h,n);l.data[n]=f(l.data[n],!0)||f(a,!0)?h.options:a;m.isDirty=m.isDirtyData=!0;!m.fixedBox&&m.hasCartesianSeries&&(q.isDirtyBox=!0);"point"===l.legendType&&(q.isDirtyLegend=!0);c&&q.redraw(e)}var h=this,m=h.series,g=h.graphic,n,q=m.chart,l=m.options;c=b(c,!0);!1===d?k():h.firePointEvent("update",{options:a},k)},remove:function(a,b){this.series.removePoint(n(this,this.series.data),
a,b)}});v(q.prototype,{addPoint:function(a,c,e,d){var f=this.options,k=this.data,h=this.chart,g=this.xAxis,g=g&&g.hasNames&&g.names,m=f.data,n,l,q=this.xData,t,u;c=b(c,!0);n={series:this};this.pointClass.prototype.applyOptions.apply(n,[a]);u=n.x;t=q.length;if(this.requireSorting&&u<q[t-1])for(l=!0;t&&q[t-1]>u;)t--;this.updateParallelArrays(n,"splice",t,0,0);this.updateParallelArrays(n,t);g&&n.name&&(g[u]=n.name);m.splice(t,0,a);l&&(this.data.splice(t,0,null),this.processData());"point"===f.legendType&&
this.generatePoints();e&&(k[0]&&k[0].remove?k[0].remove(!1):(k.shift(),this.updateParallelArrays(n,"shift"),m.shift()));this.isDirtyData=this.isDirty=!0;c&&h.redraw(d)},removePoint:function(a,c,e){var d=this,f=d.data,k=f[a],h=d.points,g=d.chart,m=function(){h&&h.length===f.length&&h.splice(a,1);f.splice(a,1);d.options.data.splice(a,1);d.updateParallelArrays(k||{series:d},"splice",a,1);k&&k.destroy();d.isDirty=!0;d.isDirtyData=!0;c&&g.redraw()};K(e,g);c=b(c,!0);k?k.firePointEvent("remove",null,m):
m()},remove:function(a,c,e){function d(){f.destroy();k.isDirtyLegend=k.isDirtyBox=!0;k.linkSeries();b(a,!0)&&k.redraw(c)}var f=this,k=f.chart;!1!==e?r(f,"remove",null,d):d()},update:function(a,c){var e=this,f=e.chart,h=e.userOptions,m=e.oldType||e.type,n=a.type||h.type||f.options.chart.type,g=D[m].prototype,q,u=["group","markerGroup","dataLabelsGroup"],l=["navigatorSeries","baseSeries"],p=e.finishedAnimating&&{animation:!1};if(Object.keys&&"data"===Object.keys(a).toString())return this.setData(a.data,
c);l=u.concat(l);d(l,function(a){l[a]=e[a];delete e[a]});a=k(h,p,{index:e.index,pointStart:e.xData[0]},{data:e.options.data},a);e.remove(!1,null,!1);for(q in g)e[q]=void 0;v(e,D[n||m].prototype);d(l,function(a){e[a]=l[a]});e.init(f,a);a.zIndex!==h.zIndex&&d(u,function(b){e[b]&&e[b].attr({zIndex:a.zIndex})});e.oldType=m;f.linkSeries();b(c,!0)&&f.redraw(!1)}});v(F.prototype,{update:function(a,c){var e=this.chart;a=e.options[this.coll][this.options.index]=k(this.userOptions,a);this.destroy(!0);this.init(e,
v(a,{events:void 0}));e.isDirtyBox=!0;b(c,!0)&&e.redraw()},remove:function(a){for(var c=this.chart,e=this.coll,f=this.series,k=f.length;k--;)f[k]&&f[k].remove(!1);m(c.axes,this);m(c[e],this);A(c.options[e])?c.options[e].splice(this.options.index,1):delete c.options[e];d(c[e],function(a,b){a.options.index=b});this.destroy();c.isDirtyBox=!0;b(a,!0)&&c.redraw()},setTitle:function(a,b){this.update({title:a},b)},setCategories:function(a,b){this.update({categories:a},b)}})})(M);(function(a){var z=a.each,
F=a.map,C=a.pick,p=a.Series,h=a.seriesType;h("area","line",{softThreshold:!1,threshold:0},{singleStacks:!1,getStackPoints:function(d){var h=[],p=[],r=this.xAxis,n=this.yAxis,c=n.stacks[this.stackKey],f={},A=this.index,k=n.series,e=k.length,b,u=C(n.options.reversedStacks,!0)?1:-1,q;d=d||this.points;if(this.options.stacking){for(q=0;q<d.length;q++)d[q].leftNull=d[q].rightNull=null,f[d[q].x]=d[q];a.objectEach(c,function(a,b){null!==a.total&&p.push(b)});p.sort(function(a,b){return a-b});b=F(k,function(){return this.visible});
z(p,function(a,d){var k=0,m,y;if(f[a]&&!f[a].isNull)h.push(f[a]),z([-1,1],function(k){var h=1===k?"rightNull":"leftNull",n=0,t=c[p[d+k]];if(t)for(q=A;0<=q&&q<e;)m=t.points[q],m||(q===A?f[a][h]=!0:b[q]&&(y=c[a].points[q])&&(n-=y[1]-y[0])),q+=u;f[a][1===k?"rightCliff":"leftCliff"]=n});else{for(q=A;0<=q&&q<e;){if(m=c[a].points[q]){k=m[1];break}q+=u}k=n.translate(k,0,1,0,1);h.push({isNull:!0,plotX:r.translate(a,0,0,0,1),x:a,plotY:k,yBottom:k})}})}return h},getGraphPath:function(a){var d=p.prototype.getGraphPath,
h=this.options,r=h.stacking,n=this.yAxis,c,f,A=[],k=[],e=this.index,b,u=n.stacks[this.stackKey],q=h.threshold,D=n.getThreshold(h.threshold),z,h=h.connectNulls||"percent"===r,E=function(c,d,f){var h=a[c];c=r&&u[h.x].points[e];var m=h[f+"Null"]||0;f=h[f+"Cliff"]||0;var t,p,h=!0;f||m?(t=(m?c[0]:c[1])+f,p=c[0]+f,h=!!m):!r&&a[d]&&a[d].isNull&&(t=p=q);void 0!==t&&(k.push({plotX:b,plotY:null===t?D:n.getThreshold(t),isNull:h,isCliff:!0}),A.push({plotX:b,plotY:null===p?D:n.getThreshold(p),doCurve:!1}))};a=
a||this.points;r&&(a=this.getStackPoints(a));for(c=0;c<a.length;c++)if(f=a[c].isNull,b=C(a[c].rectPlotX,a[c].plotX),z=C(a[c].yBottom,D),!f||h)h||E(c,c-1,"left"),f&&!r&&h||(k.push(a[c]),A.push({x:c,plotX:b,plotY:z})),h||E(c,c+1,"right");c=d.call(this,k,!0,!0);A.reversed=!0;f=d.call(this,A,!0,!0);f.length&&(f[0]="L");f=c.concat(f);d=d.call(this,k,!1,h);f.xMap=c.xMap;this.areaPath=f;return d},drawGraph:function(){this.areaPath=[];p.prototype.drawGraph.apply(this);var a=this,h=this.areaPath,v=this.options,
r=[["area","highcharts-area"]];z(this.zones,function(a,c){r.push(["zone-area-"+c,"highcharts-area highcharts-zone-area-"+c+" "+a.className])});z(r,function(d){var c=d[0],f=a[c];f?(f.endX=a.preventGraphAnimation?null:h.xMap,f.animate({d:h})):(f=a[c]=a.chart.renderer.path(h).addClass(d[1]).attr({zIndex:0}).add(a.group),f.isArea=!0);f.startX=h.xMap;f.shiftUnit=v.step?2:1})},drawLegendSymbol:a.LegendSymbolMixin.drawRectangle})})(M);(function(a){var z=a.pick;a=a.seriesType;a("spline","line",{},{getPointSpline:function(a,
C,p){var h=C.plotX,d=C.plotY,m=a[p-1];p=a[p+1];var v,r,n,c;if(m&&!m.isNull&&!1!==m.doCurve&&!C.isCliff&&p&&!p.isNull&&!1!==p.doCurve&&!C.isCliff){a=m.plotY;n=p.plotX;p=p.plotY;var f=0;v=(1.5*h+m.plotX)/2.5;r=(1.5*d+a)/2.5;n=(1.5*h+n)/2.5;c=(1.5*d+p)/2.5;n!==v&&(f=(c-r)*(n-h)/(n-v)+d-c);r+=f;c+=f;r>a&&r>d?(r=Math.max(a,d),c=2*d-r):r<a&&r<d&&(r=Math.min(a,d),c=2*d-r);c>p&&c>d?(c=Math.max(p,d),r=2*d-c):c<p&&c<d&&(c=Math.min(p,d),r=2*d-c);C.rightContX=n;C.rightContY=c}C=["C",z(m.rightContX,m.plotX),z(m.rightContY,
m.plotY),z(v,h),z(r,d),h,d];m.rightContX=m.rightContY=null;return C}})})(M);(function(a){var z=a.seriesTypes.area.prototype,F=a.seriesType;F("areaspline","spline",a.defaultPlotOptions.area,{getStackPoints:z.getStackPoints,getGraphPath:z.getGraphPath,drawGraph:z.drawGraph,drawLegendSymbol:a.LegendSymbolMixin.drawRectangle})})(M);(function(a){var z=a.animObject,F=a.each,C=a.extend,p=a.isNumber,h=a.merge,d=a.pick,m=a.Series,v=a.seriesType,r=a.svg;v("column","line",{borderRadius:0,crisp:!0,groupPadding:.2,
marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1}},dataLabels:{align:null,verticalAlign:null,y:null},softThreshold:!1,startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0},{cropShoulder:0,directTouch:!0,trackerGroups:["group","dataLabelsGroup"],negStacks:!0,init:function(){m.prototype.init.apply(this,arguments);var a=this,c=a.chart;c.hasRendered&&F(c.series,function(c){c.type===a.type&&(c.isDirty=!0)})},getColumnMetrics:function(){var a=
this,c=a.options,f=a.xAxis,h=a.yAxis,k=f.reversed,e,b={},m=0;!1===c.grouping?m=1:F(a.chart.series,function(c){var d=c.options,f=c.yAxis,k;c.type!==a.type||!c.visible&&a.chart.options.chart.ignoreHiddenSeries||h.len!==f.len||h.pos!==f.pos||(d.stacking?(e=c.stackKey,void 0===b[e]&&(b[e]=m++),k=b[e]):!1!==d.grouping&&(k=m++),c.columnIndex=k)});var q=Math.min(Math.abs(f.transA)*(f.ordinalSlope||c.pointRange||f.closestPointRange||f.tickInterval||1),f.len),p=q*c.groupPadding,r=(q-2*p)/(m||1),c=Math.min(c.maxPointWidth||
f.len,d(c.pointWidth,r*(1-2*c.pointPadding)));a.columnMetrics={width:c,offset:(r-c)/2+(p+((a.columnIndex||0)+(k?1:0))*r-q/2)*(k?-1:1)};return a.columnMetrics},crispCol:function(a,c,d,h){var f=this.chart,e=this.borderWidth,b=-(e%2?.5:0),e=e%2?.5:1;f.inverted&&f.renderer.isVML&&(e+=1);this.options.crisp&&(d=Math.round(a+d)+b,a=Math.round(a)+b,d-=a);h=Math.round(c+h)+e;b=.5>=Math.abs(c)&&.5<h;c=Math.round(c)+e;h-=c;b&&h&&(--c,h+=1);return{x:a,y:c,width:d,height:h}},translate:function(){var a=this,c=
a.chart,f=a.options,h=a.dense=2>a.closestPointRange*a.xAxis.transA,h=a.borderWidth=d(f.borderWidth,h?0:1),k=a.yAxis,e=f.threshold,b=a.translatedThreshold=k.getThreshold(e),u=d(f.minPointLength,5),q=a.getColumnMetrics(),p=q.width,r=a.barW=Math.max(p,1+2*h),E=a.pointXOffset=q.offset;c.inverted&&(b-=.5);f.pointPadding&&(r=Math.ceil(r));m.prototype.translate.apply(a);F(a.points,function(f){var h=d(f.yBottom,b),m=999+Math.abs(h),m=Math.min(Math.max(-m,f.plotY),k.len+m),q=f.plotX+E,n=r,v=Math.min(m,h),
D,g=Math.max(m,h)-v;u&&Math.abs(g)<u&&(g=u,D=!k.reversed&&!f.negative||k.reversed&&f.negative,f.y===e&&a.dataMax<=e&&k.min<e&&(D=!D),v=Math.abs(v-b)>u?h-u:b-(D?u:0));f.barX=q;f.pointWidth=p;f.tooltipPos=c.inverted?[k.len+k.pos-c.plotLeft-m,a.xAxis.len-q-n/2,g]:[q+n/2,m+k.pos-c.plotTop,g];f.shapeType="rect";f.shapeArgs=a.crispCol.apply(a,f.isNull?[q,b,n,0]:[q,v,n,g])})},getSymbol:a.noop,drawLegendSymbol:a.LegendSymbolMixin.drawRectangle,drawGraph:function(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")},
drawPoints:function(){var a=this,c=this.chart,d=a.options,m=c.renderer,k=d.animationLimit||250,e;F(a.points,function(b){var f=b.graphic;if(p(b.plotY)&&null!==b.y){e=b.shapeArgs;if(f)f[c.pointCount<k?"animate":"attr"](h(e));else b.graphic=f=m[b.shapeType](e).add(b.group||a.group);d.borderRadius&&f.attr({r:d.borderRadius});f.addClass(b.getClassName(),!0)}else f&&(b.graphic=f.destroy())})},animate:function(a){var c=this,d=this.yAxis,h=c.options,k=this.chart.inverted,e={},b=k?"translateX":"translateY",
m;r&&(a?(e.scaleY=.001,a=Math.min(d.pos+d.len,Math.max(d.pos,d.toPixels(h.threshold))),k?e.translateX=a-d.len:e.translateY=a,c.group.attr(e)):(m=c.group.attr(b),c.group.animate({scaleY:1},C(z(c.options.animation),{step:function(a,f){e[b]=m+f.pos*(d.pos-m);c.group.attr(e)}})),c.animate=null))},remove:function(){var a=this,c=a.chart;c.hasRendered&&F(c.series,function(c){c.type===a.type&&(c.isDirty=!0)});m.prototype.remove.apply(a,arguments)}})})(M);(function(a){a=a.seriesType;a("bar","column",null,
{inverted:!0})})(M);(function(a){var z=a.Series;a=a.seriesType;a("scatter","line",{lineWidth:0,findNearestPointBy:"xy",marker:{enabled:!0},tooltip:{headerFormat:'\x3cspan class\x3d"highcharts-color-{point.colorIndex}"\x3e\u25cf\x3c/span\x3e \x3cspan class\x3d"highcharts-header"\x3e {series.name}\x3c/span\x3e\x3cbr/\x3e',pointFormat:"x: \x3cb\x3e{point.x}\x3c/b\x3e\x3cbr/\x3ey: \x3cb\x3e{point.y}\x3c/b\x3e\x3cbr/\x3e"}},{sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup",
"dataLabelsGroup"],takeOrdinalPosition:!1,drawGraph:function(){this.options.lineWidth&&z.prototype.drawGraph.call(this)}})})(M);(function(a){var z=a.deg2rad,F=a.isNumber,C=a.pick,p=a.relativeLength;a.CenteredSeriesMixin={getCenter:function(){var a=this.options,d=this.chart,m=2*(a.slicedOffset||0),v=d.plotWidth-2*m,d=d.plotHeight-2*m,r=a.center,r=[C(r[0],"50%"),C(r[1],"50%"),a.size||"100%",a.innerSize||0],n=Math.min(v,d),c,f;for(c=0;4>c;++c)f=r[c],a=2>c||2===c&&/%$/.test(f),r[c]=p(f,[v,d,n,r[2]][c])+
(a?m:0);r[3]>r[2]&&(r[3]=r[2]);return r},getStartAndEndRadians:function(a,d){a=F(a)?a:0;d=F(d)&&d>a&&360>d-a?d:a+360;return{start:z*(a+-90),end:z*(d+-90)}}}})(M);(function(a){var z=a.addEvent,F=a.CenteredSeriesMixin,C=a.defined,p=a.each,h=a.extend,d=F.getStartAndEndRadians,m=a.inArray,v=a.noop,r=a.pick,n=a.Point,c=a.Series,f=a.seriesType,A=a.setAnimation;f("pie","line",{center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{distance:30,enabled:!0,formatter:function(){return this.point.isNull?void 0:
this.point.name},x:0},ignoreHiddenPoint:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0}},{isCartesian:!1,requireSorting:!1,directTouch:!0,noSharedTooltip:!0,trackerGroups:["group","dataLabelsGroup"],axisTypes:[],pointAttribs:a.seriesTypes.column.prototype.pointAttribs,animate:function(a){var c=this,b=c.points,d=c.startAngleRad;a||(p(b,function(a){var b=a.graphic,e=a.shapeArgs;b&&(b.attr({r:a.startR||c.center[3]/2,start:d,end:d}),
b.animate({r:e.r,start:e.start,end:e.end},c.options.animation))}),c.animate=null)},updateTotals:function(){var a,c=0,b=this.points,d=b.length,f,h=this.options.ignoreHiddenPoint;for(a=0;a<d;a++)f=b[a],c+=h&&!f.visible?0:f.isNull?0:f.y;this.total=c;for(a=0;a<d;a++)f=b[a],f.percentage=0<c&&(f.visible||!h)?f.y/c*100:0,f.total=c},generatePoints:function(){c.prototype.generatePoints.call(this);this.updateTotals()},translate:function(a){this.generatePoints();var c=0,b=this.options,f=b.slicedOffset,h=f+(b.borderWidth||
0),k,m,n,p=d(b.startAngle,b.endAngle),y=this.startAngleRad=p.start,p=(this.endAngleRad=p.end)-y,t=this.points,x,v=b.dataLabels.distance,b=b.ignoreHiddenPoint,B,A=t.length,g;a||(this.center=a=this.getCenter());this.getX=function(b,c,d){n=Math.asin(Math.min((b-a[1])/(a[2]/2+d.labelDistance),1));return a[0]+(c?-1:1)*Math.cos(n)*(a[2]/2+d.labelDistance)};for(B=0;B<A;B++){g=t[B];g.labelDistance=r(g.options.dataLabels&&g.options.dataLabels.distance,v);this.maxLabelDistance=Math.max(this.maxLabelDistance||
0,g.labelDistance);k=y+c*p;if(!b||g.visible)c+=g.percentage/100;m=y+c*p;g.shapeType="arc";g.shapeArgs={x:a[0],y:a[1],r:a[2]/2,innerR:a[3]/2,start:Math.round(1E3*k)/1E3,end:Math.round(1E3*m)/1E3};n=(m+k)/2;n>1.5*Math.PI?n-=2*Math.PI:n<-Math.PI/2&&(n+=2*Math.PI);g.slicedTranslation={translateX:Math.round(Math.cos(n)*f),translateY:Math.round(Math.sin(n)*f)};m=Math.cos(n)*a[2]/2;x=Math.sin(n)*a[2]/2;g.tooltipPos=[a[0]+.7*m,a[1]+.7*x];g.half=n<-Math.PI/2||n>Math.PI/2?1:0;g.angle=n;k=Math.min(h,g.labelDistance/
5);g.labelPos=[a[0]+m+Math.cos(n)*g.labelDistance,a[1]+x+Math.sin(n)*g.labelDistance,a[0]+m+Math.cos(n)*k,a[1]+x+Math.sin(n)*k,a[0]+m,a[1]+x,0>g.labelDistance?"center":g.half?"right":"left",n]}},drawGraph:null,drawPoints:function(){var a=this,c=a.chart.renderer,b,d,f;p(a.points,function(e){d=e.graphic;e.isNull?d&&(e.graphic=d.destroy()):(f=e.shapeArgs,b=e.getTranslate(),d?d.setRadialReference(a.center).animate(h(f,b)):(e.graphic=d=c[e.shapeType](f).setRadialReference(a.center).attr(b).add(a.group),
e.visible||d.attr({visibility:"hidden"})),d.addClass(e.getClassName()))})},searchPoint:v,sortByAngle:function(a,c){a.sort(function(a,d){return void 0!==a.angle&&(d.angle-a.angle)*c})},drawLegendSymbol:a.LegendSymbolMixin.drawRectangle,getCenter:F.getCenter,getSymbol:v},{init:function(){n.prototype.init.apply(this,arguments);var a=this,c;a.name=r(a.name,"Slice");c=function(b){a.slice("select"===b.type)};z(a,"select",c);z(a,"unselect",c);return a},isValid:function(){return a.isNumber(this.y,!0)&&0<=
this.y},setVisible:function(a,c){var b=this,d=b.series,e=d.chart,f=d.options.ignoreHiddenPoint;c=r(c,f);a!==b.visible&&(b.visible=b.options.visible=a=void 0===a?!b.visible:a,d.options.data[m(b,d.data)]=b.options,p(["graphic","dataLabel","connector","shadowGroup"],function(c){if(b[c])b[c][a?"show":"hide"](!0)}),b.legendItem&&e.legend.colorizeItem(b,a),a||"hover"!==b.state||b.setState(""),f&&(d.isDirty=!0),c&&e.redraw())},slice:function(a,c,b){var d=this.series;A(b,d.chart);r(c,!0);this.sliced=this.options.sliced=
C(a)?a:!this.sliced;d.options.data[m(this,d.data)]=this.options;this.graphic.animate(this.getTranslate())},getTranslate:function(){return this.sliced?this.slicedTranslation:{translateX:0,translateY:0}},haloPath:function(a){var c=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(c.x,c.y,c.r+a,c.r+a,{innerR:this.shapeArgs.r-1,start:c.start,end:c.end})}})})(M);(function(a){var z=a.addEvent,F=a.arrayMax,C=a.defined,p=a.each,h=a.extend,d=a.format,m=a.map,v=a.merge,
r=a.noop,n=a.pick,c=a.relativeLength,f=a.Series,A=a.seriesTypes,k=a.stableSort;a.distribute=function(a,b){function c(a,b){return a.target-b.target}var d,e=!0,f=a,h=[],r;r=0;for(d=a.length;d--;)r+=a[d].size;if(r>b){k(a,function(a,b){return(b.rank||0)-(a.rank||0)});for(r=d=0;r<=b;)r+=a[d].size,d++;h=a.splice(d-1,a.length)}k(a,c);for(a=m(a,function(a){return{size:a.size,targets:[a.target],align:n(a.align,.5)}});e;){for(d=a.length;d--;)e=a[d],r=(Math.min.apply(0,e.targets)+Math.max.apply(0,e.targets))/
2,e.pos=Math.min(Math.max(0,r-e.size*e.align),b-e.size);d=a.length;for(e=!1;d--;)0<d&&a[d-1].pos+a[d-1].size>a[d].pos&&(a[d-1].size+=a[d].size,a[d-1].targets=a[d-1].targets.concat(a[d].targets),a[d-1].align=.5,a[d-1].pos+a[d-1].size>b&&(a[d-1].pos=b-a[d-1].size),a.splice(d,1),e=!0)}d=0;p(a,function(a){var b=0;p(a.targets,function(){f[d].pos=a.pos+b;b+=f[d].size;d++})});f.push.apply(f,h);k(f,c)};f.prototype.drawDataLabels=function(){function c(a,b){var c=b.filter;return c?(b=c.operator,a=a[c.property],
c=c.value,"\x3e"===b&&a>c||"\x3c"===b&&a<c||"\x3e\x3d"===b&&a>=c||"\x3c\x3d"===b&&a<=c||"\x3d\x3d"===b&&a==c||"\x3d\x3d\x3d"===b&&a===c?!0:!1):!0}var b=this,f=b.chart,h=b.options,k=h.dataLabels,m=b.points,r,H,y=b.hasRendered||0,t,x,G=n(k.defer,!!h.animation),B=f.renderer;if(k.enabled||b._hasPointLabels)b.dlProcessOptions&&b.dlProcessOptions(k),x=b.plotGroup("dataLabelsGroup","data-labels",G&&!y?"hidden":"visible",k.zIndex||6),G&&(x.attr({opacity:+y}),y||z(b,"afterAnimate",function(){b.visible&&x.show(!0);
x[h.animation?"animate":"attr"]({opacity:1},{duration:200})})),H=k,p(m,function(e){var g,h=e.dataLabel,m,l,q=e.connector,p=!h,u;r=e.dlOptions||e.options&&e.options.dataLabels;(g=n(r&&r.enabled,H.enabled)&&!e.isNull)&&(g=!0===c(e,r||k));g&&(k=v(H,r),m=e.getLabelConfig(),u=k[e.formatPrefix+"Format"]||k.format,t=C(u)?d(u,m,f.time):(k[e.formatPrefix+"Formatter"]||k.formatter).call(m,k),m=k.rotation,l={r:k.borderRadius||0,rotation:m,padding:k.padding,zIndex:1},a.objectEach(l,function(a,b){void 0===a&&
delete l[b]}));!h||g&&C(t)?g&&C(t)&&(h?l.text=t:(h=e.dataLabel=m?B.text(t,0,-9999).addClass("highcharts-data-label"):B.label(t,0,-9999,k.shape,null,null,k.useHTML,null,"data-label"),h.addClass(" highcharts-data-label-color-"+e.colorIndex+" "+(k.className||"")+(k.useHTML?"highcharts-tracker":""))),h.attr(l),h.added||h.add(x),b.alignDataLabel(e,h,k,null,p)):(e.dataLabel=h=h.destroy(),q&&(e.connector=q.destroy()))})};f.prototype.alignDataLabel=function(a,b,c,d,f){var e=this.chart,k=e.inverted,m=n(a.dlBox&&
a.dlBox.centerX,a.plotX,-9999),q=n(a.plotY,-9999),t=b.getBBox(),p,u=c.rotation,r=c.align,v=this.visible&&(a.series.forceDL||e.isInsidePlot(m,Math.round(q),k)||d&&e.isInsidePlot(m,k?d.x+1:d.y+d.height-1,k)),g="justify"===n(c.overflow,"justify");if(v&&(p=e.renderer.fontMetrics(void 0,b).b,d=h({x:k?this.yAxis.len-q:m,y:Math.round(k?this.xAxis.len-m:q),width:0,height:0},d),h(c,{width:t.width,height:t.height}),u?(g=!1,m=e.renderer.rotCorr(p,u),m={x:d.x+c.x+d.width/2+m.x,y:d.y+c.y+{top:0,middle:.5,bottom:1}[c.verticalAlign]*
d.height},b[f?"attr":"animate"](m).attr({align:r}),q=(u+720)%360,q=180<q&&360>q,"left"===r?m.y-=q?t.height:0:"center"===r?(m.x-=t.width/2,m.y-=t.height/2):"right"===r&&(m.x-=t.width,m.y-=q?0:t.height)):(b.align(c,null,d),m=b.alignAttr),g?a.isLabelJustified=this.justifyDataLabel(b,c,m,t,d,f):n(c.crop,!0)&&(v=e.isInsidePlot(m.x,m.y)&&e.isInsidePlot(m.x+t.width,m.y+t.height)),c.shape&&!u))b[f?"attr":"animate"]({anchorX:k?e.plotWidth-a.plotY:a.plotX,anchorY:k?e.plotHeight-a.plotX:a.plotY});v||(b.attr({y:-9999}),
b.placed=!1)};f.prototype.justifyDataLabel=function(a,b,c,d,f,h){var e=this.chart,k=b.align,m=b.verticalAlign,n,q,p=a.box?0:a.padding||0;n=c.x+p;0>n&&("right"===k?b.align="left":b.x=-n,q=!0);n=c.x+d.width-p;n>e.plotWidth&&("left"===k?b.align="right":b.x=e.plotWidth-n,q=!0);n=c.y+p;0>n&&("bottom"===m?b.verticalAlign="top":b.y=-n,q=!0);n=c.y+d.height-p;n>e.plotHeight&&("top"===m?b.verticalAlign="bottom":b.y=e.plotHeight-n,q=!0);q&&(a.placed=!h,a.align(b,null,f));return q};A.pie&&(A.pie.prototype.drawDataLabels=
function(){var c=this,b=c.data,d,h=c.chart,k=c.options.dataLabels,m=n(k.connectorPadding,10),r=n(k.connectorWidth,1),v=h.plotWidth,y=h.plotHeight,t,x=c.center,A=x[2]/2,B=x[1],z,g,w,L,l=[[],[]],J,P,M,Q,O=[0,0,0,0];c.visible&&(k.enabled||c._hasPointLabels)&&(p(b,function(a){a.dataLabel&&a.visible&&a.dataLabel.shortened&&(a.dataLabel.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),a.dataLabel.shortened=!1)}),f.prototype.drawDataLabels.apply(c),p(b,function(a){a.dataLabel&&a.visible&&(l[a.half].push(a),
a.dataLabel._pos=null)}),p(l,function(b,e){var f,l,q=b.length,t=[],r;if(q)for(c.sortByAngle(b,e-.5),0<c.maxLabelDistance&&(f=Math.max(0,B-A-c.maxLabelDistance),l=Math.min(B+A+c.maxLabelDistance,h.plotHeight),p(b,function(a){0<a.labelDistance&&a.dataLabel&&(a.top=Math.max(0,B-A-a.labelDistance),a.bottom=Math.min(B+A+a.labelDistance,h.plotHeight),r=a.dataLabel.getBBox().height||21,a.positionsIndex=t.push({target:a.labelPos[1]-a.top+r/2,size:r,rank:a.y})-1)}),a.distribute(t,l+r-f)),Q=0;Q<q;Q++)d=b[Q],
l=d.positionsIndex,w=d.labelPos,z=d.dataLabel,M=!1===d.visible?"hidden":"inherit",P=f=w[1],t&&C(t[l])&&(void 0===t[l].pos?M="hidden":(L=t[l].size,P=d.top+t[l].pos)),delete d.positionIndex,J=k.justify?x[0]+(e?-1:1)*(A+d.labelDistance):c.getX(P<d.top+2||P>d.bottom-2?f:P,e,d),z._attr={visibility:M,align:w[6]},z._pos={x:J+k.x+({left:m,right:-m}[w[6]]||0),y:P+k.y-10},w.x=J,w.y=P,n(k.crop,!0)&&(g=z.getBBox().width,f=null,J-g<m?(f=Math.round(g-J+m),O[3]=Math.max(f,O[3])):J+g>v-m&&(f=Math.round(J+g-v+m),
O[1]=Math.max(f,O[1])),0>P-L/2?O[0]=Math.max(Math.round(-P+L/2),O[0]):P+L/2>y&&(O[2]=Math.max(Math.round(P+L/2-y),O[2])),z.sideOverflow=f)}),0===F(O)||this.verifyDataLabelOverflow(O))&&(this.placeDataLabels(),r&&p(this.points,function(a){var b;t=a.connector;if((z=a.dataLabel)&&z._pos&&a.visible&&0<a.labelDistance){M=z._attr.visibility;if(b=!t)a.connector=t=h.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+a.colorIndex).add(c.dataLabelsGroup);t[b?"attr":"animate"]({d:c.connectorPath(a.labelPos)});
t.attr("visibility",M)}else t&&(a.connector=t.destroy())}))},A.pie.prototype.connectorPath=function(a){var b=a.x,c=a.y;return n(this.options.dataLabels.softConnector,!0)?["M",b+("left"===a[6]?5:-5),c,"C",b,c,2*a[2]-a[4],2*a[3]-a[5],a[2],a[3],"L",a[4],a[5]]:["M",b+("left"===a[6]?5:-5),c,"L",a[2],a[3],"L",a[4],a[5]]},A.pie.prototype.placeDataLabels=function(){p(this.points,function(a){var b=a.dataLabel;b&&a.visible&&((a=b._pos)?(b.sideOverflow&&(b._attr.width=b.getBBox().width-b.sideOverflow,b.css({width:b._attr.width+
"px",textOverflow:"ellipsis"}),b.shortened=!0),b.attr(b._attr),b[b.moved?"animate":"attr"](a),b.moved=!0):b&&b.attr({y:-9999}))},this)},A.pie.prototype.alignDataLabel=r,A.pie.prototype.verifyDataLabelOverflow=function(a){var b=this.center,d=this.options,e=d.center,f=d.minSize||80,h,k=null!==d.size;k||(null!==e[0]?h=Math.max(b[2]-Math.max(a[1],a[3]),f):(h=Math.max(b[2]-a[1]-a[3],f),b[0]+=(a[3]-a[1])/2),null!==e[1]?h=Math.max(Math.min(h,b[2]-Math.max(a[0],a[2])),f):(h=Math.max(Math.min(h,b[2]-a[0]-
a[2]),f),b[1]+=(a[0]-a[2])/2),h<b[2]?(b[2]=h,b[3]=Math.min(c(d.innerSize||0,h),h),this.translate(b),this.drawDataLabels&&this.drawDataLabels()):k=!0);return k});A.column&&(A.column.prototype.alignDataLabel=function(a,b,c,d,h){var e=this.chart.inverted,k=a.series,m=a.dlBox||a.shapeArgs,q=n(a.below,a.plotY>n(this.translatedThreshold,k.yAxis.len)),p=n(c.inside,!!this.options.stacking);m&&(d=v(m),0>d.y&&(d.height+=d.y,d.y=0),m=d.y+d.height-k.yAxis.len,0<m&&(d.height-=m),e&&(d={x:k.yAxis.len-d.y-d.height,
y:k.xAxis.len-d.x-d.width,width:d.height,height:d.width}),p||(e?(d.x+=q?0:d.width,d.width=0):(d.y+=q?d.height:0,d.height=0)));c.align=n(c.align,!e||p?"center":q?"right":"left");c.verticalAlign=n(c.verticalAlign,e||p?"middle":q?"top":"bottom");f.prototype.alignDataLabel.call(this,a,b,c,d,h);a.isLabelJustified&&a.contrastColor&&a.dataLabel.css({color:a.contrastColor})})})(M);(function(a){var z=a.Chart,F=a.each,C=a.objectEach,p=a.pick;a=a.addEvent;a(z.prototype,"render",function(){var a=[];F(this.labelCollectors||
[],function(d){a=a.concat(d())});F(this.yAxis||[],function(d){d.options.stackLabels&&!d.options.stackLabels.allowOverlap&&C(d.stacks,function(d){C(d,function(d){a.push(d.label)})})});F(this.series||[],function(d){var h=d.options.dataLabels,v=d.dataLabelCollections||["dataLabel"];(h.enabled||d._hasPointLabels)&&!h.allowOverlap&&d.visible&&F(v,function(h){F(d.points,function(d){d[h]&&(d[h].labelrank=p(d.labelrank,d.shapeArgs&&d.shapeArgs.height),a.push(d[h]))})})});this.hideOverlappingLabels(a)});z.prototype.hideOverlappingLabels=
function(a){var d=a.length,h,p,r,n,c,f,A,k,e,b=function(a,b,c,d,e,f,h,k){return!(e>a+c||e+h<a||f>b+d||f+k<b)};for(p=0;p<d;p++)if(h=a[p])h.oldOpacity=h.opacity,h.newOpacity=1,h.width||(r=h.getBBox(),h.width=r.width,h.height=r.height);a.sort(function(a,b){return(b.labelrank||0)-(a.labelrank||0)});for(p=0;p<d;p++)for(r=a[p],h=p+1;h<d;++h)if(n=a[h],r&&n&&r!==n&&r.placed&&n.placed&&0!==r.newOpacity&&0!==n.newOpacity&&(c=r.alignAttr,f=n.alignAttr,A=r.parentGroup,k=n.parentGroup,e=2*(r.box?0:r.padding||
0),c=b(c.x+A.translateX,c.y+A.translateY,r.width-e,r.height-e,f.x+k.translateX,f.y+k.translateY,n.width-e,n.height-e)))(r.labelrank<n.labelrank?r:n).newOpacity=0;F(a,function(a){var b,c;a&&(c=a.newOpacity,a.oldOpacity!==c&&a.placed&&(c?a.show(!0):b=function(){a.hide()},a.alignAttr.opacity=c,a[a.isOld?"animate":"attr"](a.alignAttr,null,b)),a.isOld=!0)})}})(M);(function(a){var z=a.addEvent,F=a.Chart,C=a.createElement,p=a.css,h=a.defaultOptions,d=a.defaultPlotOptions,m=a.each,v=a.extend,r=a.fireEvent,
n=a.hasTouch,c=a.inArray,f=a.isObject,A=a.Legend,k=a.merge,e=a.pick,b=a.Point,u=a.Series,q=a.seriesTypes,D=a.svg,K;K=a.TrackerMixin={drawTrackerPoint:function(){var a=this,b=a.chart.pointer,c=function(a){var c=b.getPointFromEvent(a);void 0!==c&&(b.isDirectTouch=!0,c.onMouseOver(a))};m(a.points,function(a){a.graphic&&(a.graphic.element.point=a);a.dataLabel&&(a.dataLabel.div?a.dataLabel.div.point=a:a.dataLabel.element.point=a)});a._hasTracking||(m(a.trackerGroups,function(d){if(a[d]&&(a[d].addClass("highcharts-tracker").on("mouseover",
c).on("mouseout",function(a){b.onTrackerMouseOut(a)}),n))a[d].on("touchstart",c)}),a._hasTracking=!0)},drawTrackerGraph:function(){var a=this,b=a.options.trackByArea,c=[].concat(b?a.areaPath:a.graphPath),d=c.length,e=a.chart,f=e.pointer,h=e.renderer,k=e.options.tooltip.snap,g=a.tracker,p,q=function(){if(e.hoverSeries!==a)a.onMouseOver()},l="rgba(192,192,192,"+(D?.0001:.002)+")";if(d&&!b)for(p=d+1;p--;)"M"===c[p]&&c.splice(p+1,0,c[p+1]-k,c[p+2],"L"),(p&&"M"===c[p]||p===d)&&c.splice(p,0,"L",c[p-2]+
k,c[p-1]);g?g.attr({d:c}):a.graph&&(a.tracker=h.path(c).attr({"stroke-linejoin":"round",visibility:a.visible?"visible":"hidden",stroke:l,fill:b?l:"none","stroke-width":a.graph.strokeWidth()+(b?0:2*k),zIndex:2}).add(a.group),m([a.tracker,a.markerGroup],function(a){a.addClass("highcharts-tracker").on("mouseover",q).on("mouseout",function(a){f.onTrackerMouseOut(a)});if(n)a.on("touchstart",q)}))}};q.column&&(q.column.prototype.drawTracker=K.drawTrackerPoint);q.pie&&(q.pie.prototype.drawTracker=K.drawTrackerPoint);
q.scatter&&(q.scatter.prototype.drawTracker=K.drawTrackerPoint);v(A.prototype,{setItemEvents:function(a,c,d){var e=this.chart.renderer.boxWrapper,f="highcharts-legend-"+(a instanceof b?"point":"series")+"-active";(d?c:a.legendGroup).on("mouseover",function(){a.setState("hover");e.addClass(f)}).on("mouseout",function(){e.removeClass(f);a.setState()}).on("click",function(b){var c=function(){a.setVisible&&a.setVisible()};e.removeClass(f);b={browserEvent:b};a.firePointEvent?a.firePointEvent("legendItemClick",
b,c):r(a,"legendItemClick",b,c)})},createCheckboxForItem:function(a){a.checkbox=C("input",{type:"checkbox",checked:a.selected,defaultChecked:a.selected},this.options.itemCheckboxStyle,this.chart.container);z(a.checkbox,"click",function(b){r(a.series||a,"checkboxClick",{checked:b.target.checked,item:a},function(){a.select()})})}});v(F.prototype,{showResetZoom:function(){var a=this,b=h.lang,c=a.options.chart.resetZoomButton,d=c.theme,e=d.states,f="chart"===c.relativeTo?null:"plotBox";this.resetZoomButton=
a.renderer.button(b.resetZoom,null,null,function(){a.zoomOut()},d,e&&e.hover).attr({align:c.position.align,title:b.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(c.position,!1,f)},zoomOut:function(){var a=this;r(a,"selection",{resetSelection:!0},function(){a.zoom()})},zoom:function(a){var b,c=this.pointer,d=!1,h;!a||a.resetSelection?(m(this.axes,function(a){b=a.zoom()}),c.initiated=!1):m(a.xAxis.concat(a.yAxis),function(a){var e=a.axis;c[e.isXAxis?"zoomX":"zoomY"]&&(b=e.zoom(a.min,
a.max),e.displayBtn&&(d=!0))});h=this.resetZoomButton;d&&!h?this.showResetZoom():!d&&f(h)&&(this.resetZoomButton=h.destroy());b&&this.redraw(e(this.options.chart.animation,a&&a.animation,100>this.pointCount))},pan:function(a,b){var c=this,d=c.hoverPoints,e;d&&m(d,function(a){a.setState()});m("xy"===b?[1,0]:[1],function(b){b=c[b?"xAxis":"yAxis"][0];var d=b.horiz,f=a[d?"chartX":"chartY"],d=d?"mouseDownX":"mouseDownY",g=c[d],h=(b.pointRange||0)/2,k=b.getExtremes(),l=b.toValue(g-f,!0)+h,m=b.toValue(g+
b.len-f,!0)-h,n=m<l,g=n?m:l,l=n?l:m,m=Math.min(k.dataMin,h?k.min:b.toValue(b.toPixels(k.min)-b.minPixelPadding)),h=Math.max(k.dataMax,h?k.max:b.toValue(b.toPixels(k.max)+b.minPixelPadding)),n=m-g;0<n&&(l+=n,g=m);n=l-h;0<n&&(l=h,g-=n);b.series.length&&g!==k.min&&l!==k.max&&(b.setExtremes(g,l,!1,!1,{trigger:"pan"}),e=!0);c[d]=f});e&&c.redraw(!1);p(c.container,{cursor:"move"})}});v(b.prototype,{select:function(a,b){var d=this,f=d.series,h=f.chart;a=e(a,!d.selected);d.firePointEvent(a?"select":"unselect",
{accumulate:b},function(){d.selected=d.options.selected=a;f.options.data[c(d,f.data)]=d.options;d.setState(a&&"select");b||m(h.getSelectedPoints(),function(a){a.selected&&a!==d&&(a.selected=a.options.selected=!1,f.options.data[c(a,f.data)]=a.options,a.setState(""),a.firePointEvent("unselect"))})})},onMouseOver:function(a){var b=this.series.chart,c=b.pointer;a=a?c.normalize(a):c.getChartCoordinatesFromPoint(this,b.inverted);c.runPointActions(a,this)},onMouseOut:function(){var a=this.series.chart;this.firePointEvent("mouseOut");
m(a.hoverPoints||[],function(a){a.setState()});a.hoverPoints=a.hoverPoint=null},importEvents:function(){if(!this.hasImportedEvents){var b=this,c=k(b.series.options.point,b.options).events;b.events=c;a.objectEach(c,function(a,c){z(b,c,a)});this.hasImportedEvents=!0}},setState:function(a,b){var c=Math.floor(this.plotX),f=this.plotY,h=this.series,k=h.options.states[a||"normal"]||{},m=d[h.type].marker&&h.options.marker,n=m&&!1===m.enabled,g=m&&m.states&&m.states[a||"normal"]||{},p=!1===g.enabled,q=h.stateMarkerGraphic,
l=this.marker||{},r=h.chart,u=h.halo,v,A=m&&h.markerAttribs;a=a||"";if(!(a===this.state&&!b||this.selected&&"select"!==a||!1===k.enabled||a&&(p||n&&!1===g.enabled)||a&&l.states&&l.states[a]&&!1===l.states[a].enabled)){A&&(v=h.markerAttribs(this,a));if(this.graphic)this.state&&this.graphic.removeClass("highcharts-point-"+this.state),a&&this.graphic.addClass("highcharts-point-"+a),v&&this.graphic.animate(v,e(r.options.chart.animation,g.animation,m.animation)),q&&q.hide();else{if(a&&g)if(m=l.symbol||
h.symbol,q&&q.currentSymbol!==m&&(q=q.destroy()),q)q[b?"animate":"attr"]({x:v.x,y:v.y});else m&&(h.stateMarkerGraphic=q=r.renderer.symbol(m,v.x,v.y,v.width,v.height).add(h.markerGroup),q.currentSymbol=m);q&&(q[a&&r.isInsidePlot(c,f,r.inverted)?"show":"hide"](),q.element.point=this)}(c=k.halo)&&c.size?(u||(h.halo=u=r.renderer.path().add((this.graphic||q).parentGroup)),u.show()[b?"animate":"attr"]({d:this.haloPath(c.size)}),u.attr({"class":"highcharts-halo highcharts-color-"+e(this.colorIndex,h.colorIndex)}),
u.point=this):u&&u.point&&u.point.haloPath&&u.animate({d:u.point.haloPath(0)},null,u.hide);this.state=a}},haloPath:function(a){return this.series.chart.renderer.symbols.circle(Math.floor(this.plotX)-a,this.plotY-a,2*a,2*a)}});v(u.prototype,{onMouseOver:function(){var a=this.chart,b=a.hoverSeries;if(b&&b!==this)b.onMouseOut();this.options.events.mouseOver&&r(this,"mouseOver");this.setState("hover");a.hoverSeries=this},onMouseOut:function(){var a=this.options,b=this.chart,c=b.tooltip,d=b.hoverPoint;
b.hoverSeries=null;if(d)d.onMouseOut();this&&a.events.mouseOut&&r(this,"mouseOut");!c||this.stickyTracking||c.shared&&!this.noSharedTooltip||c.hide();this.setState()},setState:function(a){var b=this;a=a||"";b.state!==a&&(m([b.group,b.markerGroup,b.dataLabelsGroup],function(c){c&&(b.state&&c.removeClass("highcharts-series-"+b.state),a&&c.addClass("highcharts-series-"+a))}),b.state=a)},setVisible:function(a,b){var c=this,d=c.chart,e=c.legendItem,f,h=d.options.chart.ignoreHiddenSeries,k=c.visible;f=
(c.visible=a=c.options.visible=c.userOptions.visible=void 0===a?!k:a)?"show":"hide";m(["group","dataLabelsGroup","markerGroup","tracker","tt"],function(a){if(c[a])c[a][f]()});if(d.hoverSeries===c||(d.hoverPoint&&d.hoverPoint.series)===c)c.onMouseOut();e&&d.legend.colorizeItem(c,a);c.isDirty=!0;c.options.stacking&&m(d.series,function(a){a.options.stacking&&a.visible&&(a.isDirty=!0)});m(c.linkedSeries,function(b){b.setVisible(a,!1)});h&&(d.isDirtyBox=!0);!1!==b&&d.redraw();r(c,f)},show:function(){this.setVisible(!0)},
hide:function(){this.setVisible(!1)},select:function(a){this.selected=a=void 0===a?!this.selected:a;this.checkbox&&(this.checkbox.checked=a);r(this,a?"select":"unselect")},drawTracker:K.drawTrackerGraph})})(M);(function(a){var z=a.Chart,F=a.each,C=a.inArray,p=a.isArray,h=a.isObject,d=a.pick,m=a.splat;z.prototype.setResponsive=function(d){var h=this.options.responsive,m=[],c=this.currentResponsive;h&&h.rules&&F(h.rules,function(c){void 0===c._id&&(c._id=a.uniqueKey());this.matchResponsiveRule(c,m,
d)},this);var f=a.merge.apply(0,a.map(m,function(c){return a.find(h.rules,function(a){return a._id===c}).chartOptions})),m=m.toString()||void 0;m!==(c&&c.ruleIds)&&(c&&this.update(c.undoOptions,d),m?(this.currentResponsive={ruleIds:m,mergedOptions:f,undoOptions:this.currentOptions(f)},this.update(f,d)):this.currentResponsive=void 0)};z.prototype.matchResponsiveRule=function(a,h){var m=a.condition;(m.callback||function(){return this.chartWidth<=d(m.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=d(m.maxHeight,
Number.MAX_VALUE)&&this.chartWidth>=d(m.minWidth,0)&&this.chartHeight>=d(m.minHeight,0)}).call(this)&&h.push(a._id)};z.prototype.currentOptions=function(d){function r(c,d,n,k){var e;a.objectEach(c,function(a,c){if(!k&&-1<C(c,["series","xAxis","yAxis"]))for(a=m(a),n[c]=[],e=0;e<a.length;e++)d[c][e]&&(n[c][e]={},r(a[e],d[c][e],n[c][e],k+1));else h(a)?(n[c]=p(a)?[]:{},r(a,d[c]||{},n[c],k+1)):n[c]=d[c]||null})}var n={};r(d,this.options,n,0);return n}})(M);return M});
