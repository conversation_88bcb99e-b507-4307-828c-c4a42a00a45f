﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWAI07BankListViewModel
    {
        public string whereKeyword { get; set; }
        public string whereGrade { get; set; }
        public string whereCLASS_NO { get; set; }
        public string whereStatus { get; set; }
        public string OrderColumn { get; set; }
        public string SortBy { get; set; }
        public IPagedList<AWAI07BankDetail> BankDetailList { get; set; }
        public int Page { get; set; }

        public AWAI07BankListViewModel()
        {
            Page = 0;
            SortBy = "DESC";
        }
    }

    public class AWAI07BankDetail
    {
        public string A_NO { get; set; }
        public string SCHOOL_NO { get; set; }
        public string USER_NO { get; set; }
        public string NAME { get; set; }
        public byte? GRADE { get; set; }
        public string CLASS_NO { get; set; }
        public byte? PERIOD_TYPE { get; set; }
        public decimal? INTEREST_RATE { get; set; }
        public DateTime? BANK_DATE { get; set; }

        public DateTime? PERIOD_DATES { get; set; }
        public DateTime? PERIOD_DATEE { get; set; }
        public string PERIOD_DESC { get; set; }
        public string STATUS { get; set; }
        public DateTime? CLOSE_DATE { get; set; }
        public decimal? CLOSE_RATE { get; set; }
        public decimal? AMT { get; set; }
        public byte? MATURITY_TYPE { get; set; }
        public byte? ACCT_CODE { get; set; }

    }
}