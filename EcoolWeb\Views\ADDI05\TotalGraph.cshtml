﻿@model ADDI05TotalGraphViewModel
@using ECOOL_APP.com.ecool.util;
@{
    ViewBag.Title = ViewBag.Panel_Title;
    int InputNum = 0;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<link href="~/Scripts/stickysort/stickysort.css" rel="stylesheet" />
<link href="~/Scripts/stickysort/styles2.css?var=@DateTime.Now" rel="stylesheet" />

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="form-group">
    <a class="btn btn-sm btn-sys" role="button" onclick="onGo('Index')">
        回有獎徵答首頁
    </a>
    <a class="btn btn-sm btn-sys" role="button" onclick="onGo('detail')">
        回詳細活動內容
    </a>
</div>

<img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive App_hide" alt="Responsive image" />
<div class="Div-EZ-ADDI05">
    <div class="Details">
        <div style="height:15px"></div>
        <div class="Caption_Div">
            活動名稱：@ViewBag.DIALOG_NAME
        </div>
        <div style="height:15px"></div>

        <div class="Caption_Div">
            全部題目-答對答錯比例<br>
            <h4 style="color:red">全部回答@(Model.AllCount)人次，全部答對@(Model.TrueCount)人次，占全部的@(Model.TrueRate)%</h4>
        </div>
        <div class="table-92Per" style="margin: 0px auto; ">
            <div class="progress" href="@Url.Action("TotalGraphDetails", (string)ViewBag.BRE_NO,new {  DIALOG_ID= Model.DIALOG_ID })">
                <div class="progress-bar progress-bar-info" style="width: @(Model.TrueRate)% ;min-width: 6em;">
                    @(Model.TrueCount)人次(@(Model.TrueRate)%)
                </div>
                <div class="progress-bar progress-bar-gray" style="width: @(Model.FalseRate)% ;min-width: 6em;">
                    @(Model.FalseCount)人次(@(Model.FalseRate)%)
                </div>
            </div>
        </div>

        <div style="height:15px"></div>
        <hr style="color:red">
        <div class="table-92Per" style="margin: 0px auto; ">
            @if (Model.RightCountData != null)
            {
                foreach (var item in Model.UADDT12)
                {
                    <div class="form-group">
                        <strong class="label_dt_S">
                            Q@(InputNum + 1).
                            @Html.Raw(HttpUtility.HtmlDecode(item.Q_TEXT))
                        </strong>
                        <div style="height:15px"></div>
                        @{

                            int MaxAnsNum = (item.Q_TYPE == 1) ? 2 : 4;

                            for (int AnsNum = 1; AnsNum <= MaxAnsNum; AnsNum++)
                            {

                                string labelAns = string.Empty;
                                string WhereAns = string.Empty;

                                if (item.Q_TYPE == 1)
                                {
                                    if (AnsNum == 1)
                                    {
                                        labelAns = "O";
                                        WhereAns = "O";
                                    }
                                    else
                                    {
                                        labelAns = "X";
                                        WhereAns = "X";
                                    }

                                }
                                else
                                {
                                    if (AnsNum == 1)
                                    {
                                        labelAns = item.Q_ANS1;
                                    }
                                    else if (AnsNum == 2)
                                    {
                                        labelAns = item.Q_ANS2;
                                    }
                                    else if (AnsNum == 3)
                                    {
                                        labelAns = item.Q_ANS3;
                                    }
                                    else if (AnsNum == 4)
                                    {
                                        labelAns = item.Q_ANS4;
                                    }

                                    WhereAns = AnsNum.ToString();
                                }

                                <label>
                                    @if (item.Q_TYPE == 2)
                                    {
                                        <strong>@(AnsNum).</strong>
                                    }
                                    @Html.Raw(HttpUtility.HtmlDecode(labelAns))
                                </label>

                                <br />

                                var Ans = Model.RightCountData.Where(a => a.Q_NUM == item.Q_NUM && a.ANSWER == WhereAns).FirstOrDefault();

                                string bar = (ViewBag.IsShowAns && item.TRUE_ANS == WhereAns) ? "progress-bar-info" : "progress-bar-gray";

                                if (Ans != null)
                                {

                                    <div class="progress" href="@Url.Action("TotalGraphDetails", (string)ViewBag.BRE_NO,new {  DIALOG_ID= item.DIALOG_ID,  Q_NUM=item.Q_NUM,  ANSWER = WhereAns })">
                                        <div class="progress-bar @(bar)" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                             style="width: @(Ans.RATE)%;min-width: 6em;">
                                            @(Ans.QTY)人次(@(Ans.RATE)%)
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="progress" href="@Url.Action("TotalGraphDetails", (string)ViewBag.BRE_NO,new {  DIALOG_ID= item.DIALOG_ID,  Q_NUM=item.Q_NUM,  ANSWER = WhereAns })">
                                        <div class="progress-bar @(bar)" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                             style="min-width: 6em;">
                                            0人次(0%)
                                        </div>
                                    </div>
                                }

                            }
                            <div style="height:15px"></div>
                        }
                    </div>
                    InputNum = InputNum + 1;
                }
            }
        </div>
        <div style="height:15px"></div>
    </div>
</div>

@if (Model.AnsLists?.Count > 0 && Model.UADDT12 != null)
{
    double TotCount = Model.UADDT12.Count();
    <br />
    <div class="form-inline no-print" role="form">
        <div class="form-group">
            <label class="control-label">
                班級
            </label>
        </div>
        <div class="form-group">
            @Html.Editor("CLASS_NO1", new { htmlAttributes = new { @class = "form-control input-sm" } })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="onSearch()" />
        <button type="button" class="btn-yellow btn btn-sm" onclick='fn_save()'>另存新檔(Excel)</button>
    </div>

    <div id="final">
        <div id="TableData">
            <table style="border:1px #FFD382 dashed;">
                <thead>
                    <tr style="height:35px" class="text-center">
                        <th>
                        </th>
                        <th>
                            成績
                        </th>
                        @foreach (var item in Model.UADDT12)
                        {
                            <th>
                                <span title='@item.Q_TEXT'>
                                    Q @(item.Q_NUM)
                                </span>
                                <br />
                            </th>
                        }
                        <th>
                            時間
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @{

                        var Students = (from c in Model.AnsLists
                                        group c by new { c.SCHOOL_NO, c.CLASS_NO, c.SEAT_NO, c.USER_NO, c.SNAME } into g
                                        select new
                                        {
                                            SCHOOL_NO = g.Key.SCHOOL_NO,
                                            CLASS_NO = g.Key.CLASS_NO,
                                            SEAT_NO = g.Key.SEAT_NO,
                                            USER_NO = g.Key.USER_NO,
                                            SNAME = g.Key.SNAME,
                                            MaxCRE_DATE = g.Max(x => x.CRE_DATE)
                                        }).OrderBy(x => x.SCHOOL_NO).ThenBy(x => x.CLASS_NO).ThenBy(x => x.SEAT_NO).ToList();

                        if (Students?.Count > 0)
                        {
                            foreach (var Student in Students)
                            {
                                <tr class="CLASS_NO @(Student.CLASS_NO)" style="height:35px;">
                                    <th style="width:170px">
                                        @if (string.IsNullOrWhiteSpace(Student.CLASS_NO))
                                        {
                                            @StringHelper.LeftStringR(Student.SNAME, 5)
                                        }
                                        else
                                        {
                                            @:@(Student.CLASS_NO)班 @Student.SEAT_NO @StringHelper.LeftStringR(Student.SNAME, 5)
                                        }
                                    </th>

                                    <td class="text-nowrap text-center">
                                        @{
                                            double UserRightCount = Model.AnsLists.Where(x => x.SCHOOL_NO == Student.SCHOOL_NO && x.USER_NO == Student.USER_NO
                                               && x.CRE_DATE == Student.MaxCRE_DATE
                                               && x.RIGHT_YN == SharedGlobal.Y).Count();

                                            @Math.Round((UserRightCount / TotCount) * 100, 0, MidpointRounding.AwayFromZero)
                                        }
                                    </td>

                                    @foreach (var item in Model.UADDT12)
                                    {
                                        <td class="text-center">
                                            @if (Model.AnsLists.Where(x => x.Q_NUM == item.Q_NUM && x.SCHOOL_NO == Student.SCHOOL_NO && x.USER_NO == Student.USER_NO
                                            && x.CRE_DATE == Student.MaxCRE_DATE
                                            && x.RIGHT_YN == SharedGlobal.Y).Any())
                                            {
                                                @:O
                                            }
                                        </td>
                                    }
                                    <td class="text-nowrap text-center">
                                        @Student.MaxCRE_DATE
                                    </td>
                                </tr>
                            }
                        }

                    }
                </tbody>
            </table>
        </div>
    </div>
}

@using (Html.BeginForm("TotalGraph", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.HiddenFor(m => m.SearchContents)
    @Html.HiddenFor(m => m.OrderByName)
    @Html.HiddenFor(m => m.SyntaxName)
    @Html.HiddenFor(m => m.page)
    @Html.HiddenFor(m => m.DIALOG_ID)

}

@section Scripts {
    <script src="~/Content/colorbox/jquery.colorbox.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/stickysort/prefixfree.min.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/stickysort/jquery.stickysort.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/stickysort/jquery.ba-throttle-debounce.min.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/Blob.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/FileSaver.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/tableexport.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/ADDI05/common.js" nonce="cmlvaw"></script>
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI05_TOTAL_GRAPH_URLS = {
            totalGraph: '@Url.Action("TotalGraph", (string)ViewBag.BRE_NO)',
            index: '@Url.Action("Index", (string)ViewBag.BRE_NO)',
            detail: '@Url.Action("detail", (string)ViewBag.BRE_NO)',
            totalGraphDetails: '@Url.Action("TotalGraphDetails", (string)ViewBag.BRE_NO)',
            sysUrl: 'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'
        };
    </script>
    <script src="~/Scripts/ADDI05/totalGraph.js" nonce="cmlvaw"></script>
}