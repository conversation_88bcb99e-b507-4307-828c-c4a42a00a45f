﻿@model ADDI11MyRunLogViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();


}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

        @*<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
    <script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>*@

@Html.HiddenFor(m => m.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.WhereKM)
@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)






<img src="~/Content/img/web-bar-Run.png" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center" id="tbData">
        <table class="table-ecool table-92Per table-hover table-ecool-reader">
            <thead>
                <tr>

                    <th>
                        班級

                    </th>
                    <th onclick="doSort('CLASSSUMKM')">
                        班級總跑步公里數
                        <img id="RUN_TOTAL_KM" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th>
                        人數
                    </th>
                    <th onclick="doSort('AVGCLASS')">
                        平均(公里)
                        <img id="LocalNameCount" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody>

                @foreach (var item in Model.RunCity)
                {

                    <tr>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.CLASSSUMKM)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.CLASSCount)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.AVGCLASS)</td>

                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
@*@if (Model.RunCity.Count() == 0)
    {
        <div class="text-center">
            <h3>無任何跑步記錄</h3>
        </div>

    }
    else
    {
        <div style="height:25px"></div>
        <div class="col-sm-12">
            @if (Model.MyRunColumnChart != null)
            {
                @Model.MyRunColumnChart
            }
        </div>
    }*@
<script>

    $(document).ready(function () {

        $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
    });
</script>