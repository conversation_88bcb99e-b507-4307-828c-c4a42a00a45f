﻿@model GameEditViewModel

@{
    ViewBag.Title = "抽獎點數(獎品)-編輯";
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<!-- clipboard.js v1.7.1 複製內容到剪貼簿的小工具 -->
<script src="~/Scripts/clipboard.min.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
<div class="form-group">

    <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="GameIndex1" ? "active":"")" onclick="OnGameIndex()"> <i class="fa fa-edit"></i> 活動一覽表</button>




    @*<button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="PassMod1" ? "active":"")" onclick="OnOnePassMode()"> <i class="fa fa-truck"></i> 過關模式</button>*@





</div>
}

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.Main.GAME_NO)
    @Html.HiddenFor(model => model.Main.SCHOOL_NO)

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.GAME_TYPE)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @ViewBag.Panel_Title
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.Label("獎品主題", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.GAME_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.Main.GAME_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("獎品主題描述", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.TextAreaFor(m => m.Main.GAME_DESC, 2, 100, new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Main.GAME_DESC) })
                        @Html.ValidationMessageFor(model => model.Main.GAME_DESC, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group row">
                    @Html.Label("獎懲類別", htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                      
                            @Html.DropDownListFor(m => m.Main.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.QuickItems, new { @class = "form-control" })
                       
                    </div>
                    <div class="col-md-9">

                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("獎品開始兌換日", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.GAME_DATES, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.Main.GAME_DATES, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.Label("獎品結束兌換日", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.GAME_DATEE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.Main.GAME_DATEE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.GAME_IMG, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @if (Model.Main != null)
                        {
                            if (!string.IsNullOrWhiteSpace(Model.Main.GAME_IMG_PATH))
                            {
                                <img src="@Model.Main.GAME_IMG_PATH" class="img-responsive colorbox" alt="Responsive image" href="@Model.Main.GAME_IMG_PATH" style="max-width:300px" />
                                <br />
                            }
                        }
                        <div>
                            @Html.TextBoxFor(m => m.Main.UploadGamerFile, new { @class = "form-control input-md", @type = "file" })
                            @Html.ValidationMessageFor(m => m.Main.UploadGamerFile, "", new { @class = "text-danger" })
                            <br />
                            <label class="text-info">
                                PS:<br />
                                1.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片<br />
                                2.上傳的圖片，同時也是各關卡的預設圖片<br />
                                3.未上傳，則自動使用系統預設圖片
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

  
   

    
    <div>
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>
        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center" style="background-color:antiquewhite">
                兌換獎品
            </div>
            <div class="panel-body">
                @Html.ValidationMessage("PrizeDetailsError", new { @class = "text-danger" })
                @{

                    var Prizevalue = Html.ValidationMessage("PrizeDetailsError").ToString();

                    if (Prizevalue.Contains("field-validation-error"))
                    {
                        <br /> <br />
                    }
                }
                <div class="table-responsive">
                    <div class="css-table" style="width:92%;">
                        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            <div class="th" style="text-align:center">
                              

                            </div>
                            <div class="th" style="text-align:center;width:50px">
                                可否<br />
                                重複玩

                            </div>
                            <div class="th" style="text-align:center;">
                                主題
                            </div>
                            <div class="th" style="text-align:center;">
                                抽獎一次需要<br />
                                (如果是扣10點，寫「-10」)
                            </div>
                            <div class="th" style="text-align:center">
                                禮物是<br />點數
                            </div>
                            <div class="th" style="text-align:center;width:260px;">
                                獎品名稱<br /><span style="color:red">(如果是酷幣點數，寫阿拉伯數字)</span>
                            </div>
                            <div class="th" style="text-align:center;width:50px;">
                                獎品數量
                            </div>
                            <div class="th" style="text-align:center">

                            </div>

                        </div>
                  
                        <div class="tbody" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            @if (Model.Details == null || Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize && a.Coupons_ITem == "1").Count() == 0)
                            {
                                if (Model.Details == null)
                                {
                                    Model.Details = new List<GameEditDetailsViewModel>();
                                }

                                Model.Details.Add(new GameEditDetailsViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = false,
                                    IsCouppons = true,
                                    LEVEL_ITEM = "006",
                                    Coupons_ITem = "1",
                                    //PrizeName = "1",
                                    //PrizeQty = 1,
                                    //PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.ItemPrize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = true,
                                    CASH = 0,

                                });
                            }

                            @foreach (var Item in Model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize && a.Coupons_ITem == "1").ToList())
                            {
                                Html.RenderPartial("_DetailsPrize3", Item);
                            }
                        </div>
                        <div class="tbody" id="PrizeDetailsEditorRows">

                            @if (Model.PrizeDetails == null || Model.PrizeDetails.Count() == 0)
                            {
                                if (Model.PrizeDetails == null)
                                {
                                    Model.PrizeDetails = new List<GameLotteryPrizeViewModel>();
                                }

                                Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,
                                    Y_CASH = false,
                                    PrizeName = "",
                                    PrizeQty = 1,
                                    LEVEL_NO = "1",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = true,

                                });
                                Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,
                                    Y_CASH = false,
                                    PrizeName = "",
                                    PrizeQty = 1,
                                    LEVEL_NO = "1",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = true,

                                }); Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,
                                    Y_CASH = false,
                                    PrizeName = "",
                                    PrizeQty = 1,
                                    LEVEL_NO = "1",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = true,

                                });
                            }

                            @foreach (var Item in Model.PrizeDetails.Where(x => x.LEVEL_NO == "1").ToList())
                            {
                                Html.RenderPartial("_DetailsPrizeLottery1", Item);
                            }
                        </div>
                       
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-md-12 col-xs-12 text-right">
                            <span class="input-group-btn">
                                <button class="btn btn-info btn-sm" type="button" onclick="onAddDetailsPrizeLotteryItem()">
                                    <i class="fa fa-plus-circle"></i>   增加兌換獎品
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
               
            </div>
        </div>
      
    </div>

  
    

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="存檔" class="btn btn-default" onclick="Save(this)" />
                @if (!string.IsNullOrWhiteSpace(Model?.Main?.GAME_NO) && (!string.IsNullOrWhiteSpace(Model.Search.WhereGAME_NO)))
                {
                    <button class="btn btn-default" type="button" onclick="onDel()">整筆刪除</button>
                }
                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>

}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        $(".colorboxPHOTO").colorbox({ photo: true, maxWidth: "90%", maxHeight: "90%", opacity: 0.82 });
        $(".colorbox").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });

   $("#@Html.IdFor(m => m.Main.GAME_DATES) ,#@Html.IdFor(m => m.Main.GAME_DATEE)").datepicker({
                 dateFormat: "yy/mm/dd",
                 changeMonth: true,
                 changeYear: true,
                 showOn: "button",
                 buttonImage: "../Content/img/icon/calendar.gif",
                 buttonImageOnly: true,
             });

             var Today = new Date();

             if ($("#@Html.IdFor(m => m.Main.GAME_DATES)").val() == "") {
                 $("#@Html.IdFor(m => m.Main.GAME_DATES)").datepicker("setDate", Today);
             }

             if ($("#@Html.IdFor(m => m.Main.GAME_DATEE)").val() == "") {
                 Today.setMonth(Today.getMonth() + 1);
                 $("#@Html.IdFor(m => m.Main.GAME_DATEE)").datepicker('setDate', Today);
             }

        //存檔
        function Save(ButtonName)
        {
            $(targetFormID).attr("action", "@Url.Action("EditSave1", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();

            ButtonName.disabled = true;
        }

        //放棄回上一頁
        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("GameIndex1", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
          function OnOneEdit(val) {

        if (val.toUpperCase()=="EDIT") {
             $('form').attr("action", "@Url.Action("Edit", "Game")");
        }
        else {
            $('form').attr("action", "@Url.Action("EditQA", "Game")");
        }

        $('form').submit();
    }

   

      

    function OnGameIndex() {
        location.href = "@Url.Action("GameIndex1", (string)ViewBag.BRE_NO)";
    }

    
    function OnOnePassMode() {
        $('form').attr("action", "@Url.Action("PassMode1", "Game")");
        $('form').submit();
    }
        //備援
        function onBackupLink() {
             $(targetFormID).attr("action", "@Url.Action("BackupLink", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        //刪除這筆活動資料
        function onDel() {
            var WhereSearchtxt = $("#Main_GAME_NO").val();
              $.ajax({
                url: '@Url.Action("CheckIsWinner")',
                  data: {
                      GAME_NO: WhereSearchtxt,
                      OrdercColumn: $('#OrdercColumn').val(),
                      SyntaxName: $('#SyntaxName').val(),
                  },
                cache: false,
                  success: function (data1) {
                      if (data1 == "true") {
                          var OK = confirm("您確定要刪除這筆活動資料，按確定後，活動資料、報名著資料及報名著參與的活動內容，將全部被刪除!!，刪除後無法還原!!!!")

                          if (OK) {
                              // $(targetFormID).validate().resetForm();
                              $('html, body').scrollTop(0);
                              $(targetFormID).hide()
                              $('#loading').fadeIn(3000)
                              setTimeout(function () {
                                  $(targetFormID).attr("action", "@Url.Action("EditDel", (string)ViewBag.BRE_NO)")
                                  $(targetFormID).submit();
                              }, 3000);
                          }

                      }
                      else {
                          alert("刪除失敗，已被兌換過，無法刪除");

                      }
                }
               });
           
         }

       //增加明細(一般模式)
       function onAddItem() {

           var trLength = $("#editorRows .tr").length
           trLength = trLength + 1;

         var data = {
               "LEVEL_TYPE": '@ADDT26_D.LevelType.Pay',
               "LEVEL_NAME": trLength+"."
            };

            $.ajax({
                url: '@Url.Action("_Details")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }
        
        //新增抽獎兌換
        function onAddPrizeExchangeItem() {
            var trLength = $("#PrizeItemEditorRows .tr").length
            trLength = trLength + 1;
            var data = {
               "IsApply": false,
                "IsAgainst": false,
                "IsCouppons": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };
             var data1 = {
               "IsApply": false,
                 "IsAgainst": true,
                 "PrizeName": "獎品1",
                 "PrizeQty": 1,
                 "PrizeRate": 1,

               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };
            var data2 = {
               "IsApply": false,
                 "IsAgainst": true,
                 "PrizeName": "獎品2",
                 "PrizeQty": 1,
                 "PrizeRate": 1,

               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };
             var data3 = {
               "IsApply": false,
                 "IsAgainst": true,
                 "PrizeName": "獎品3",
                 "PrizeQty": 1,
                 "PrizeRate": 1,

               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };

            $.ajax({
                url: '@Url.Action("_DetailsPrize2")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
            });
               $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                   data: data1,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
               });
              $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                   data: data2,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
              });
              $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                   data: data3,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
              });
            $("#PrizeItemEditorRows").append("");
        }
          //增加兌換
        function onAddPrizeItem() {

            var trLength = $("#PrizeEditorRows .tr").length
            trLength = trLength + 1;

             var data = {
               "IsApply": false,
               "IsAgainst": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
               "LEVEL_NAME": "兌換關卡",
               "Y_REPEAT": true,
               "CASH": 0,
             };

            $.ajax({
                url: '@Url.Action("_DetailsPrize")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeEditorRows").append(html);
                }
            });
        }   //增加兌換
        function onAddDetailsPrizeLotteryItem() {

            var trLength = $("#PrizeDetailsEditorRows .tr").length
            trLength = trLength + 1;

            var data = {
                "Y_CASH": false,
               "IsApply": false,
               "IsAgainst": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.ItemPrize',
                 "LEVEL_NAME": "兌換關卡抽獎",
                 "Y_REPEAT": false,
                 "PrizeName": "",
                 "PrizeQty": "1",
                "PrizeRate": "1",
                "LEVEL_NO": "1"
             };

            $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery1")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeDetailsEditorRows").append(html);
                }
            });
        }
        function onAddDetailsPrizeLotteryItem1() {

            var trLength = $("#PrizeDetailsEditorRows1 .tr").length
            trLength = trLength + 1;

            var data = {
                "Y_CASH": false,
               "IsApply": false,
               "IsAgainst": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.ItemPrize',
                 "LEVEL_NAME": "兌換關卡抽獎",
                 "Y_REPEAT": false,
                 "PrizeName": "",
                 "PrizeQty": "1",
                "PrizeRate": "1",
               "LEVEL_NO":"1"
             };

            $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery1")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeDetailsEditorRows1").append(html);
                }
            });
        }
        //增加是非題
        function onAddItemOX() {

            var data = {
                "InputType": "OX",
                "Y_REPEAT": "true",
            };

             $.ajax({
                 url: '@Url.Action("_DetailsQA")',
                 data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }

        //增加選擇題
        function onAddItemSelect() {

            var data = {
                "InputType": "Select",
                "Y_REPEAT": "true",
            };

             $.ajax({
                 url: '@Url.Action("_DetailsQA")',
                 data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }

        function onIsAgainstCheckBox(This, TrId) {

            if (This.checked) {
                $('#' + TrId).css("background-color", "#B0E0E6");
            }
            else {
                $('#' + TrId).css("background-color", "");
            }
        }

        function onCheckBoxTrue(ThisCheckBox, ClassStr, ClassRETURN_DESC) {

            var ThisCheckBoxId = ('#' + ThisCheckBox.id);

            var RETURN_DESC_ID = ThisCheckBoxId.replace("TRUE_ANS", "RETURN_DESC")

            if (ThisCheckBox.checked) {

                $("." + ClassRETURN_DESC).each(function () {
                    $(this).val('請看清楚題目')
                })

                $("." + ClassStr).each(function () {
                    $(this).prop("checked", false);
                })

                $(ThisCheckBoxId).prop("checked", true);
                $(RETURN_DESC_ID).val('')
            }
            else {
                if ($("." + ClassStr).is(':checked') == false) {
                    alert('需勾選一個選項為正確答案')
                    $(ThisCheckBoxId).prop("checked", true);
                }
            }
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }

        //del 圖片
        function DelFile(Index) {

             $("#DivPHOTO_FILE_URL_" + Index).remove();
             $('#DivUpPhotoFiles_' + Index).show();
             $('#Details_' + Index +'__LEVEL_IMG').val('')

        }
    </script>
}