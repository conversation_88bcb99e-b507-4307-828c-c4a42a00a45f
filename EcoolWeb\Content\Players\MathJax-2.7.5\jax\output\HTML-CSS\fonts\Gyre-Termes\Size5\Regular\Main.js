/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size5/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Size5={directory:"Size5/Regular",family:"GyreTermesMathJax_Size5",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[1253,753,575,139,482],41:[1253,753,575,93,436],47:[1793,1293,1222,80,1142],91:[1266,766,472,139,379],92:[1793,1293,1222,80,1142],93:[1266,766,472,93,333],123:[1255,755,517,93,424],124:[1245,745,216,80,136],125:[1255,755,517,93,424],160:[0,0,250,0,0],770:[699,-536,1249,0,1249],771:[687,-533,1241,0,1241],774:[695,-540,1266,0,1266],780:[694,-531,1249,0,1249],785:[709,-553,1266,0,1266],812:[-70,233,1249,0,1249],813:[-80,243,1249,0,1249],814:[-70,225,1266,0,1266],815:[-88,243,1266,0,1266],816:[-88,242,1241,0,1241],8214:[1245,745,372,80,292],8260:[1793,1293,1222,80,1142],8425:[750,-548,2250,0,2250],8730:[1576,1050,661,120,687],8739:[1245,745,216,80,136],8741:[1245,745,372,80,292],8968:[1266,745,472,139,379],8969:[1266,745,472,93,333],8970:[1245,766,472,139,379],8971:[1245,766,472,93,333],9001:[1796,1296,510,93,417],9002:[1796,1296,510,93,417],9140:[750,-548,2250,0,2250],9141:[-98,300,2250,0,2250],9180:[750,-541,3014,0,3014],9181:[-91,300,3014,0,3014],9182:[784,-527,3019,0,3019],9183:[-77,333,3019,0,3019],9184:[745,-541,3080,0,3080],9185:[-91,295,3080,0,3080],10214:[1266,766,475,139,382],10215:[1266,766,475,93,336],10216:[1796,1296,510,93,417],10217:[1796,1296,510,93,417],10218:[1796,1296,765,93,672],10219:[1796,1296,765,93,672],10222:[1252,752,399,139,306],10223:[1252,752,399,93,260]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Size5"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size5/Regular/Main.js"]);
