﻿@model IEnumerable<ECOOL_APP.EF.BET02>

@{
    ViewBag.Title = "Index";
}

<h2>Index</h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.SCHOOL_NO)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CLASS_TYPE)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.SUBJECT)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CONTENT_TXT)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.ISPUBLISH)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.S_DATE)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.E_DATE)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.TOP_YN)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CRE_PERSON)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CRE_DATE)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CHG_PERSON)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CHG_DATE)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.MEMO)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.SCHOOL_NO)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.CLASS_TYPE)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.SUBJECT)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.CONTENT_TXT)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.ISPUBLISH)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.S_DATE)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.E_DATE)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.TOP_YN)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.CRE_PERSON)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.CRE_DATE)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.CHG_PERSON)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.CHG_DATE)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.MEMO)
        </td>
        <td>
            @Html.ActionLink("Edit", "Edit", new { id=item.BULLET_ID }) |
            @Html.ActionLink("Details", "Details", new { id=item.BULLET_ID }) |
            @Html.ActionLink("Delete", "Delete", new { id=item.BULLET_ID })
        </td>
    </tr>
}

</table>
