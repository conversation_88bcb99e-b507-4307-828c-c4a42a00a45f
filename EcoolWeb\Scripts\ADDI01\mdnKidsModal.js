// @nonce="cmlvaw"
// 身分證繪製 ****
$(function() {
    var fullIDNO = $("#MdnKids_IDNO").val();
    var safeIDNO = "";
    for (var i = 0; i <= fullIDNO.length - 1; i++) {
        if (i > 5) {
            safeIDNO += "*";
        } else {
            safeIDNO += fullIDNO[i];
        }
    }
    $("#inputIDNO").val(safeIDNO);
    $("#MdnKids_IDNO").val(fullIDNO);

    $("#inputIDNO").on('change', function () {
        $("#MdnKids_IDNO").val($("#inputIDNO").val());
    });
});
// 身分證繪製 **** 結束 