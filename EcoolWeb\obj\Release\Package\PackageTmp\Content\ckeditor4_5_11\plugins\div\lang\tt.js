﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'div', 'tt', {
	IdInputLabel: 'Идентификатор',
	advisoryTitleInputLabel: 'Киңәш исем',
	cssClassInputLabel: 'Стильләр класслары',
	edit: 'Edit Div', // MISSING
	inlineStyleInputLabel: 'Эчке стиль',
	langDirLTRLabel: 'Сулдан уңга язылыш (LTR)',
	langDirLabel: 'Язылыш юнəлеше',
	langDirRTLLabel: 'Уңнан сулга язылыш (RTL)',
	languageCodeInputLabel: 'Тел коды',
	remove: 'Remove Div', // MISSING
	styleSelectLabel: 'Стиль',
	title: 'Create Div Container', // MISSING
	toolbar: 'Create Div Container' // MISSING
} );
