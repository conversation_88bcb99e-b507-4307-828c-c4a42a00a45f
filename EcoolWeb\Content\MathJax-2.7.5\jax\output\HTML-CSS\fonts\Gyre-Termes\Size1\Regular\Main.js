/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size1/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Size1={directory:"Size1/Regular",family:"GyreTermesMathJax_Size1",testString:"\u00A0\u0302\u0303\u0305\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u0332\u0333\u033F",32:[0,0,250,0,0],40:[738,238,426,124,344],41:[738,238,426,82,302],47:[774,274,549,80,469],91:[749,249,403,124,321],92:[774,274,549,80,469],93:[749,249,403,82,279],123:[740,240,413,82,331],124:[730,230,206,80,126],125:[740,240,413,82,331],160:[0,0,250,0,0],770:[697,-537,608,0,608],771:[688,-532,601,0,601],773:[632,-588,500,0,500],774:[694,-543,620,0,620],780:[693,-533,608,0,608],785:[706,-554,620,0,620],812:[-70,230,608,0,608],813:[-80,240,608,0,608],814:[-70,222,620,0,620],815:[-88,240,620,0,620],816:[-88,245,601,0,601],818:[-70,114,500,0,500],819:[-70,228,500,0,500],831:[746,-588,500,0,500],8214:[730,230,352,80,272],8260:[774,274,549,80,469],8400:[710,-600,660,80,580],8401:[710,-600,660,80,580],8406:[710,-534,670,80,590],8407:[710,-534,670,80,590],8417:[710,-534,742,80,662],8425:[728,-548,750,0,750],8428:[-150,260,660,80,580],8429:[-150,260,660,80,580],8430:[-84,260,670,80,590],8431:[-84,260,670,80,590],8512:[930,430,1152,80,1071],8592:[430,-70,1170,80,1090],8593:[760,250,520,80,440],8594:[430,-70,1170,80,1090],8595:[750,260,520,80,440],8596:[430,-70,1360,80,1280],8597:[850,350,520,80,440],8598:[611,122,893,80,813],8599:[611,122,893,80,813],8600:[622,111,893,80,813],8601:[622,111,893,80,813],8602:[430,-70,1170,80,1090],8603:[430,-70,1170,80,1090],8606:[430,-70,1350,80,1270],8607:[850,340,520,80,440],8608:[430,-70,1350,80,1270],8609:[840,350,520,80,440],8610:[430,-70,1360,80,1280],8611:[430,-70,1360,80,1280],8612:[430,-70,1170,80,1090],8613:[760,250,520,80,440],8614:[430,-70,1170,80,1090],8615:[750,260,520,80,440],8617:[508,-70,1196,80,1116],8618:[508,-70,1196,80,1116],8619:[508,-18,1196,80,1116],8620:[508,-18,1196,80,1116],8621:[430,-70,1360,80,1280],8622:[430,-70,1360,80,1280],8624:[694,194,668,80,588],8625:[694,194,668,80,588],8626:[694,194,668,80,588],8627:[694,194,668,80,588],8630:[576,-241,1039,80,959],8631:[576,-241,1039,80,959],8636:[430,-224,1160,80,1080],8637:[276,-70,1160,80,1080],8638:[750,250,366,80,286],8639:[750,250,366,80,286],8640:[430,-224,1160,80,1080],8641:[276,-70,1160,80,1080],8642:[750,250,366,80,286],8643:[750,250,366,80,286],8644:[630,130,1180,80,1100],8645:[760,260,920,80,840],8646:[630,130,1180,80,1100],8647:[630,130,1170,80,1090],8648:[760,250,920,80,840],8649:[630,130,1170,80,1090],8650:[750,260,920,80,840],8651:[526,26,1160,80,1080],8652:[526,26,1160,80,1080],8653:[500,0,1170,80,1090],8654:[500,0,1360,80,1280],8655:[500,0,1170,80,1090],8656:[470,-30,1170,80,1090],8657:[760,250,600,80,520],8658:[470,-30,1170,80,1090],8659:[750,260,600,80,520],8660:[470,-30,1360,80,1280],8661:[850,350,600,80,520],8662:[611,176,946,80,866],8663:[611,176,946,80,866],8664:[676,111,946,80,866],8665:[676,111,946,80,866],8666:[572,72,1350,80,1270],8667:[572,72,1350,80,1270],8668:[430,-70,1170,80,1090],8669:[430,-70,1170,80,1090],8678:[470,-30,1393,80,1313],8679:[875,358,600,80,520],8680:[470,-30,1393,80,1313],8681:[858,375,600,80,520],8691:[875,375,600,80,520],8693:[760,260,920,80,840],8694:[830,330,1170,80,1090],8719:[937,437,1204,80,1124],8720:[937,437,1204,80,1124],8721:[937,437,1197,80,1117],8730:[808,282,607,120,633],8739:[730,230,206,80,126],8741:[730,230,352,80,272],8747:[1263,763,750,80,670],8748:[1263,763,1188,80,1108],8749:[1263,763,1626,80,1546],8750:[1263,763,804,80,724],8751:[1263,763,1242,80,1162],8752:[1263,763,1680,80,1600],8753:[1263,763,818,80,778],8754:[1263,763,797,80,757],8755:[1263,763,780,80,740],8866:[650,150,1160,80,1080],8867:[650,150,1160,80,1080],8868:[650,150,960,80,880],8869:[650,150,960,80,880],8896:[839,326,1040,80,960],8897:[826,339,1040,80,960],8898:[844,326,1040,80,960],8899:[826,344,1040,80,960],8968:[749,230,403,124,321],8969:[749,230,403,82,279],8970:[730,249,403,124,321],8971:[730,249,403,82,279],9001:[780,280,381,82,299],9002:[780,280,381,82,299],9140:[728,-548,750,0,750],9141:[-98,278,750,0,750],9180:[713,-571,1014,0,1014],9181:[-121,263,1014,0,1014],9182:[763,-534,1019,0,1019],9183:[-84,312,1019,0,1019],9184:[734,-548,1066,0,1066],9185:[-98,284,1066,0,1066],10145:[470,-30,1350,80,1270],10214:[749,249,413,124,331],10215:[749,249,413,82,289],10216:[780,280,381,82,299],10217:[780,280,381,82,299],10218:[780,280,579,82,497],10219:[780,280,579,82,497],10222:[737,237,334,124,252],10223:[737,237,334,82,210],10502:[470,-30,1350,80,1270],10503:[470,-30,1350,80,1270],10752:[816,316,1292,80,1212],10753:[816,316,1292,80,1212],10754:[816,316,1292,80,1212],10755:[826,344,1040,80,960],10756:[826,344,1040,80,960],10757:[841,311,1040,80,960],10758:[811,341,1040,80,960],10761:[703,203,1065,80,985],10764:[1263,763,2064,80,1984],10769:[1263,763,818,80,778],11012:[470,-30,1411,80,1331],11013:[470,-30,1350,80,1270],11014:[850,340,600,80,520],11015:[840,350,600,80,520],11020:[470,-30,1360,80,1280],11021:[850,350,600,80,520],11057:[830,330,1170,80,1090]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Size1"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size1/Regular/Main.js"]);
