﻿using AutoMapper;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ECOOL_APP.EF
{
    public static class PageListExtensions
    {
        public static IPagedList<TDestination> ToMappedPagedList<TSource, TDestination>(this IQueryable<TSource> data, int page, int pageSize, int? totalCount = null)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (totalCount == null)
                {
                    totalCount = data?.Count() ?? 0;
                }

                var totalPage = (int)Math.Ceiling((double)totalCount / pageSize);

                var pageIndex = page > 0 ? page - 1 : 0;

                if (pageIndex > totalPage - 1 && totalCount > 0)
                {
                    pageIndex = totalPage - 1;
                }

                var pageData = data.Skip((pageIndex) * pageSize).Take(pageSize);

                var ListData = Mapper.Map<IEnumerable<TSource>, IEnumerable<TDestination>>(pageData);

                var _returnList = ListData.ToPagedList(pageIndex, pageSize, totalCount);

                return _returnList;
            }
        }

        public static IPagedList<TSource> ToEzPagedList<TSource>(this IQueryable<TSource> data, int page, int pageSize, int? totalCount = null)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (totalCount == null)
                {
                    totalCount = data?.Count() ?? 0;
                }

                var totalPage = (int)Math.Ceiling((double)totalCount / pageSize);

                var pageIndex = page > 0 ? page - 1 : 0;

                if (pageIndex > totalPage - 1 && totalCount > 0)
                {
                    pageIndex = totalPage - 1;
                }

                var pageData = data.Skip((pageIndex) * pageSize).Take(pageSize);

                var _returnList = pageData.ToPagedList(pageIndex, pageSize, totalCount);

                return _returnList;
            }
        }

        public static IPagedList<TSource> ToEzPagedList<TSource>(this IEnumerable<TSource> data, int page, int pageSize, int? totalCount = null)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (totalCount == null)
                {
                    totalCount = data?.Count() ?? 0;
                }

                var totalPage = (int)Math.Ceiling((double)totalCount / pageSize);

                var pageIndex = page > 0 ? page - 1 : 0;

                if (pageIndex > totalPage - 1 && totalCount > 0)
                {
                    pageIndex = totalPage - 1;
                }

                var pageData = data.Skip((pageIndex) * pageSize).Take(pageSize);

                var _returnList = pageData.ToPagedList(pageIndex, pageSize, totalCount);

                return _returnList;
            }
        }
    }
}