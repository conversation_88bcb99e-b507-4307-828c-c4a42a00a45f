﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace ECOOL_APP.EF
{
    public class BDMT05IndexListViewModel
    {
        public string whereGAME_NO { get; set; }

        public string BackAction { get; set; }

        public string BackController { get; set; }

        /// <summary>
        ///關卡 No.
        /// </summary>
        [DisplayName("關卡 No.")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///關卡名稱
        /// </summary>
        [DisplayName("關卡名稱")]
        [Required(ErrorMessage = "此欄位必輸")]
        public string GAME_NAME { get; set; }

        /// <summary>
        ///關卡圖片
        /// </summary>
        [DisplayName("關卡圖片")]
        public string GAME_IMG { get; set; }

        /// <summary>
        ///關卡網址
        /// </summary>
        [DisplayName("關卡網址")]
        [Required(ErrorMessage = "此欄位必輸")]
        [DataType(DataType.Url)]
        [Url]
        public string LINK_URL { get; set; }

        /// <summary>
        ///關卡說明
        /// </summary>
        [DisplayName("關卡說明")]
        public string GAME_DESC { get; set; }

        /// <summary>
        ///關卡酷幣點數
        /// </summary>
        [DisplayName("關卡酷幣點數(扣點為負數)")]
        public int CASH { get; set; }


        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("建立人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日期
        /// </summary>
        [DisplayName("修改日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CHG_DATE { get; set; }
    }
}
