﻿@model global::ECOOL_APP.com.ecool.Models.DTO.ZZZI09_HISViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<br />

@Html.Partial("_Notice")
<img src="~/Content/img/web-bar2-revise-19.png" style="width:100%" class="img-responsive " alt="Responsive image" />

@using (Html.BeginForm("Index", "ZZZI09_HIS", FormMethod.Post))
{
    @Html.Hidden("IDNO")
    <div class="Div-EZ-ZZZI09" id='_SelectDiv'>
        <div class="form-horizontal">
            <label class="control-label"> * @ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group">
                @Html.Label("選擇匯出轉學或畢業生", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_EXPORTTYPE" })
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.Where_EXPORTTYPE, (IEnumerable<SelectListItem>)ViewBag.ExportTypeItems, new { @class = "form-control", onchange = "$('#Where_SYEAR').val('');this.form.submit();" })
                    @Html.ValidationMessageFor(m => m.Where_EXPORTTYPE, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SYEAR" })
                <div class="col-md-9">
                    @if (ViewBag.CanChangeSchool)
                    {
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();" })
                    }
                    else
                    {
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();", disabled = "disabled" })
                    }
                    @Html.ValidationMessageFor(m => m.Where_SCHOOLNO_FORADMIN, "", new { @class = "text-danger" })
                </div>
            </div>
            @*<div class="form-group">
                @Html.Label("用名字搜尋", htmlAttributes: new { @class = "control-label col-md-3", @for = "SName" })
                <div class="col-md-9">
                    <a class="progress" href="@Url.Action("Index3", (string)ViewBag.BRE_NO)" target="_blank">用姓名搜尋出畢業生資訊</a>
                </div>
            </div>*@
            <div class="form-group">
                @Html.Label("入學年、畢業年", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SYEAR" })
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control", onchange = "$('#Where_CLASSNO').val('');this.form.submit();" })
                    @Html.ValidationMessageFor(m => m.Where_SYEAR, "", new { @class = "text-danger" })
                    <span id="SyearAttr"></span>
                </div>
            </div>

            <div class="form-group">
                @Html.Label("畢業/離校班級", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_CLASSNO" })
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.Where_CLASSNO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();" })
                    @Html.ValidationMessageFor(m => m.Where_CLASSNO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("是否匯出閱讀認證", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                <div class="col-md-9">
                    否<input type="checkbox" id="ReadYN" name="ReadYN" />
                </div>

            </div>
            <div class="form-group">
                @Html.Label("姓名座號", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_USERNO" })
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.Where_USERNO, (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control", onchange = "this.form.submit();" })
                    @Html.ValidationMessageFor(m => m.Where_USERNO, "", new { @class = "text-danger" })
                </div>
            </div>
            @if (Model.Where_USERNO != null)
            {
                <div class="form-group">
                    @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SCHOOLNO" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO, (IEnumerable<SelectListItem>)ViewBag.SchoolItems, new { @class = "form-control" })
                        @Html.ValidationMessageFor(m => m.Where_SCHOOLNO, "", new { @class = "text-danger" })
                    </div>
                </div>
            }

                <div class="form-group">
                    @Html.Label("用名字搜尋", htmlAttributes: new { @class = "control-label col-md-3", @for = "SName" })
                    <div class="col-md-9">

                        <button type="button" class="btn btn-default btn-block" onclick="helper()">求助</button>
                    </div>
                </div>
            <div class="form-group">
                <div class="col-md-offset-3 col-md-9 text-center">
                    <button type="button" class="btn btn-default btn-block" onclick="CK()">匯出</button>
                </div>
            </div>
        </div>
        </div>

}

@section scripts{
    <script>
        function helper() {

                window.open('@Url.Action("Index3", (string)ViewBag.BRE_NO)', '_blank');
        }
        function CK() {
            var Msg = '';
            if ($('#Where_USERNO').val() == '' ) {
                Msg = Msg + '請選擇「' + $("label[for='Where_USERNO']").text() + '」\n';
            }

            if (Msg != '') {
                alert(Msg);
            }
            else {
                var StrWhere = '?School_No=' + $('#Where_SCHOOLNO').val() + '&USER_NO=' + $('#Where_USERNO').val() + '&IDNO=' + $('#IDNO').val() + '&ReadYN=' + $("#ReadYN").prop("checked")
                window.open('@Url.Action("ExportResultView", "ZZZI09")' + StrWhere+'', '_blank');
            }
        }
        function chgName() { }
    </script>
}