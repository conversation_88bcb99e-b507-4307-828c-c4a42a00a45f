﻿// JavaScript for ADDI06 PrintQuery page - 列印查詢
$(document).ready(function () {
    const printQueryModule = {
        init: function () {
            this.setupGlobalFunctions();
            this.bindEvents();
          
        },

        bindEvents: function () {
            // 綁定Excel匯出按鈕事件
            $('#ButtonExcel').on('click', this.handlePrintExcel.bind(this));

            // 綁定列印按鈕事件
            $('button[onclick*="PrintExcel"], input[onclick*="PrintExcel"]').on('click', this.handlePrintExcel.bind(this));

        },

        setupGlobalFunctions: function () {
            // 設置全局函數以保持向後兼容
            window.PrintExcel = this.printExcel.bind(this);
            //window.ChangeClass_No = this.changeClass_No.bind(this);
        },

        printExcel: function () {
            try {
                console.log('PrintQuery 開始匯出Excel');

                // 檢查是否有資料可以匯出
                const tableData = $('table tbody tr');
                if (tableData.length === 0) {
                    ADDI06Common.showMessage('沒有資料可以匯出', 'warning');
                    return false;
                }

                // 使用瀏覽器列印功能
                window.print();

                console.log('PrintQuery Excel匯出完成');
                return true;
            } catch (error) {
                console.error('PrintQuery 匯出Excel時發生錯誤:', error);
                ADDI06Common.showMessage('匯出Excel時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

     

        // 事件處理器
        handlePrintExcel: function (event) {
            event.preventDefault();
            this.printExcel();
        },

       
    };

    // 初始化模組
    printQueryModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('PrintQuery 頁面錯誤:', e);
    });
});