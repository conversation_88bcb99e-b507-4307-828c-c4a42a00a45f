﻿$(document).ready(function () {
    const printQueryModule = {
        init: function () {
            this.bindEvents();
            this.setGlobalFunction();
        },
        bindEvents: function () {

            $('#ButtonExcel').on('click', this.PrintExcel.bind(this));
        },
        setGlobalFunction: function () {
            window.PrintExcel = this.PrintExcel.bind(this);

        },
        PrintExcel: function () {
            window.print();
            this.ADDI06Common.PrintExcel();
        }

    }
    printQueryModule.init();
    window.addEventListener('error', function (e) {
        console.error('printquery 頁面錯誤:', e);

    });
});