﻿@model EcoolWeb.Models.ADDT06ViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<link href="~/Content/css/EzCss.css" rel="stylesheet" />

@Html.HiddenFor(model => model.Page)
@Html.HiddenFor(model => model.OrdercColumn)
@Html.HiddenFor(model => model.SyntaxName)
@Html.HiddenFor(m => m.whereAPPLY_STATUS)
@if (user != null)
{
    if (user.USER_TYPE == UserType.Student)
    {

        <label class="label_dt">您已經閱讀：<font color="red">@(Model.MyData.BOOK_QTY == null ? 0 : Model.MyData.BOOK_QTY) </font>本書，認證等級：<font color="red">@(Model.MyData.LEVEL_DESC == null ? "" : Model.MyData.LEVEL_DESC)(@(Model.MyData.LEVEL_ID)) </font>，再閱讀：<font color="red">@(Model.MyData.UNLEVEL_QTY == null ? "5" : Model.MyData.UNLEVEL_QTY) </font>本書就可以再升級囉</label><br /><br />
    }
}
@{
    string ActiveAll = (Model.whereAPPLY_STATUS == string.Empty || Model.whereAPPLY_STATUS == null) ? "active" : "";
    string ActiveUN = Model.whereAPPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave.ToString() + "," + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back.ToString() ? "active" : "";
    string ActiveIN = (Model.whereAPPLY_STATUS == Convert.ToSByte(ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass).ToString()) ? "active" : "";
}

<div>
    <button class="btn btn-xs btn-pink @ActiveAll" type="button" data-status="">全部</button>
    <button class="btn btn-xs btn-pink @ActiveIN" type="button" data-status="@Convert.ToSByte(ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass)">已通過</button>
    <button class="btn btn-xs btn-pink @ActiveUN" type="button" data-status="@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave">草稿</button>
</div>

<img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover table-ecool-reader">
        <thead>
            <tr>

                <th>
                    <samp class="form-group">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().SYEAR)
                    </samp>
                </th>
                <th>
                    <samp class="form-group">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().SEMESTER)
                    </samp>
                </th>
                <th>
                    <samp class="form-group">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().CLASS_NO)
                    </samp>
                </th>
                <th>
                    <samp class="form-group">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().SEAT_NO)
                    </samp>
                </th>
                <th>
                    <samp class="form-group">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().SNAME)
                    </samp>
                </th>
                <th>
                    <samp class="form-group sortable-header" style="cursor:pointer;" data-sort-column="BOOK_NAME">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().BOOK_NAME)
                    </samp>
                </th>
                <th>
                    <samp class="form-group sortable-header" style="cursor:pointer;" data-sort-column="APPLY_NO">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().CRE_DATE)
                    </samp>
                </th>
                <th>
                    <samp class="form-group sortable-header" style="cursor:pointer;" data-sort-column="APPLY_STATUS">
                        @Html.DisplayNameFor(model => model.ADDT06List.First().APPLY_STATUS)
                    </samp>
                </th>
                <th>
                    <samp class="form-group">
                    </samp>
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ADDT06List)
            {
                <tr onclick="onBtnLink('@item.APPLY_NO','@item.APPLY_STATUS')" style="cursor:pointer">
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SYEAR)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SEMESTER)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                    </td>
                    <td class="name-column" style="text-align: left; vertical-align: top; padding: 8px;">
                        @Html.DisplayFor(modelItem => item.SNAME)
                    </td>
                    <td class="book-name-column" style="text-align: left; white-space: normal; vertical-align: top; padding: 8px;">
                        @Html.DisplayFor(modelItem => item.BOOK_NAME)

                        @if (item.SHARE_YN == "Y" || item.SHARE_YN == "y")
                        {
                            <img src="~/Content/img/icons-like-05.png" />
                        }
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                    </td>
                    <td align="center" style="white-space:nowrap">
                        @ADDStatus.GetADDT06StatusString(item.APPLY_STATUS)
                    </td>
                    @if (item.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave || item.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify)
                    {
                        <td>修改</td>
                    }
                    else
                    {

                        <td></td>
                    }
                </tr>
            }
        </tbody>
    </table>
</div>
<div>
    @Html.Pager(Model.ADDT06List.PageSize, Model.ADDT06List.PageNumber, Model.ADDT06List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>
<div style="height:10px"></div>
<div class="text-center">
    <a href='@Url.Action("ADDTList", "ADDT")' class="btn btn-default">
        返回
    </a>
</div>

<style>
    .table-row-hover {
        background-color: #f5f5f5 !important;
    }

    .sortable-header {
        user-select: none;
        transition: color 0.3s ease;
    }

    .sortable-header:hover {
        color: #007bff;
    }

    .sort-indicator {
        font-weight: bold;
        color: #007bff;
    }

    .btn-pink.active {
        background-color: #e91e63 !important;
        border-color: #e91e63 !important;
        color: white !important;
    }

    .data-row {
        transition: background-color 0.3s ease;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* 表格對齊改善 */
    .table td {
        vertical-align: top !important;
        padding: 8px !important;
    }

    .table td[style*="text-align: left"] {
        text-align: left !important;
    }

    .table td[align="center"] {
        text-align: center !important;
        vertical-align: middle !important;
    }

    /* 姓名和書名欄位特殊樣式 */
    .name-column {
        min-width: 80px;
        max-width: 120px;
        word-wrap: break-word;
    }

    .book-name-column {
        min-width: 200px;
        word-wrap: break-word;
        line-height: 1.4;
    }

    .book-name-column img {
        margin-left: 5px;
        vertical-align: middle;
    }

    /* 修正 samp 標籤對齊問題 */
    .table th samp {
        display: block;
        font-family: inherit;
        font-size: inherit;
        font-weight: inherit;
        line-height: inherit;
        margin: 0;
        padding: 0;
        vertical-align: baseline;
    }

    /* 表格標題對齊 */
    .table th {
        vertical-align: middle !important;
        text-align: center !important;
        padding: 8px !important;
    }
</style>

<script nonce="cmlvaw">
    // 設置全局配置
    window.ADDI02_CONFIG = {
        tempSaveStatus: "@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave",
        withVerifyStatus: "@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify",
        passStatus: "@Convert.ToSByte(ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass)"
    };

    // 設置全局 URL 配置
    window.ADDI02_URLS = {
        baseUrl: "@Url.Action("ADDTALLListDetails", "ADDT")"
    };
</script>
<script src="~/Scripts/ADDI02/pagecontent.js" nonce="cmlvaw"></script>