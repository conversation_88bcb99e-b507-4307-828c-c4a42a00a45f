/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Marks/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Marks={directory:"Marks/Regular",family:"STIXMathJax_Marks",testString:"\u00A0\u02B0\u02B1\u02B2\u02B3\u02B4\u02B5\u02B6\u02B7\u02B8\u02B9\u02BA\u02BB\u02BC\u02BD",32:[0,0,250,0,0],160:[0,0,250,0,0],688:[848,-336,378,7,365],689:[848,-336,378,7,365],690:[852,-169,300,44,244],691:[681,-336,252,5,252],692:[680,-335,277,10,257],693:[680,-168,325,10,338],694:[680,-335,390,6,379],695:[680,-331,520,6,512],696:[680,-176,370,14,361],697:[684,-421,208,90,257],698:[684,-421,305,19,324],699:[686,-443,333,79,218],700:[686,-443,333,79,218],701:[686,-443,333,79,218],702:[680,-485,198,35,163],703:[680,-485,198,35,163],704:[690,-295,326,23,303],705:[690,-295,326,23,303],706:[755,-419,317,33,285],707:[755,-419,317,33,285],708:[713,-461,317,-9,327],709:[713,-461,317,-9,327],712:[713,-448,278,119,159],716:[70,195,278,119,159],717:[-104,159,334,11,323],718:[-21,192,333,25,249],719:[-21,192,333,84,308],720:[460,-19,333,89,244],721:[460,-299,333,89,244],722:[365,-75,333,72,262],723:[365,-75,333,71,261],724:[205,-18,333,51,281],725:[205,-18,333,51,281],726:[218,-26,333,71,263],727:[144,-100,333,71,263],731:[0,165,333,64,249],733:[678,-507,333,-3,376],734:[443,-186,298,0,263],735:[662,-425,333,48,284],736:[684,-219,378,24,335],737:[848,-336,215,19,197],738:[681,-331,291,36,261],739:[680,-336,380,5,372],740:[850,-336,341,45,319],741:[662,0,413,48,373],742:[662,0,405,40,365],743:[662,0,405,40,365],744:[662,0,405,40,365],745:[662,0,405,40,365],748:[70,147,333,21,311],749:[665,-507,405,10,395],759:[-113,219,333,1,331],773:[820,-770,0,-480,20],777:[751,-492,0,-307,-118],781:[700,-500,0,-250,-195],782:[700,-500,0,-326,-133],783:[678,-507,0,-401,-22],784:[767,-507,0,-373,-92],785:[664,-507,0,-373,-92],786:[745,-502,0,-299,-160],787:[745,-502,0,-299,-160],788:[745,-502,0,-299,-160],789:[745,-502,0,-85,54],790:[-53,224,0,-351,-127],791:[-53,224,0,-371,-147],792:[-53,283,0,-397,-210],793:[-53,283,0,-267,-80],794:[735,-531,0,-380,-80],795:[474,-345,0,-44,51],796:[-71,266,0,-360,-232],797:[-53,240,0,-345,-115],798:[-53,240,0,-345,-115],799:[-53,250,0,-326,-134],800:[-124,168,0,-326,-134],801:[75,287,0,-235,1],802:[75,287,0,-54,182],803:[-118,217,0,-280,-181],804:[-119,218,0,-379,-81],805:[-69,268,0,-329,-130],806:[-110,353,0,-299,-160],807:[0,215,0,-334,-125],808:[0,165,0,-322,-137],809:[-102,234,0,-250,-210],810:[-98,235,0,-385,-73],811:[-110,227,0,-380,-75],812:[-73,240,0,-385,-74],813:[-73,240,0,-385,-74],814:[-68,225,0,-370,-89],815:[-59,216,0,-370,-89],816:[-113,219,0,-395,-65],817:[-141,195,0,-385,-74],818:[-141,191,0,-480,20],819:[-141,300,0,-480,20],820:[320,-214,0,-401,-71],821:[274,-230,0,-384,-78],822:[274,-230,0,-480,20],823:[580,74,0,-380,-41],825:[-71,266,0,-280,-152],826:[-53,190,0,-385,-73],827:[-53,227,0,-313,-147],828:[-65,189,0,-380,-79],829:[715,-525,0,-326,-135],830:[829,-499,0,-283,-177],831:[928,-770,0,-480,20],838:[681,-538,0,-350,-68],839:[-140,292,1,11,323],844:[777,-532,0,-386,-56],857:[-65,367,0,-357,-87],860:[-76,233,0,-373,295],864:[633,-517,0,-395,365],865:[664,-507,0,-373,295],866:[-65,270,0,-395,355],8208:[259,-193,333,39,285],8209:[257,-194,333,39,285],8210:[259,-193,500,0,500],8213:[250,-201,2000,0,2000],8215:[-141,300,500,0,500],8218:[102,141,333,79,218],8219:[676,-433,333,79,218],8222:[102,141,444,45,416],8223:[676,-433,444,30,401],8226:[444,-59,523,70,455],8229:[100,11,667,111,555],8240:[706,19,1109,61,1048],8241:[706,19,1471,61,1410],8246:[678,-401,426,75,351],8247:[678,-401,563,75,488],8248:[102,156,511,59,454],8249:[416,-33,333,63,285],8250:[416,-33,333,48,270],8251:[547,41,685,48,635],8252:[676,9,549,130,452],8256:[709,-512,798,72,726],8259:[332,-172,333,39,285],8263:[676,8,839,68,809],8270:[240,171,500,68,433],8271:[459,141,278,60,199],8272:[691,40,790,55,735],8273:[676,171,501,68,433],8274:[706,200,471,54,417],8287:[0,0,1000,0,0],8400:[760,-627,0,-453,-17],8401:[760,-627,0,-453,-17],8402:[662,156,0,-242,-192],8406:[760,-548,0,-453,-17],8411:[622,-523,0,-462,35],8412:[622,-523,0,-600,96],8413:[725,221,0,-723,223],8414:[780,180,0,-730,230],8415:[843,341,0,-840,344],8417:[760,-548,0,-453,25],8420:[1023,155,0,-970,490],8421:[662,156,0,-430,-40],8422:[662,156,0,-335,-102],8423:[725,178,0,-650,166],8424:[-119,218,0,-462,35],8425:[681,-538,0,-480,53],8426:[419,-87,0,-658,118],8427:[756,217,0,-448,193],8428:[-119,252,0,-453,-17],8429:[-119,252,0,-453,-17],8430:[-40,252,0,-453,-17],8431:[-40,252,0,-453,-17],8432:[819,-517,0,-357,-87],12306:[662,0,685,10,672],12336:[417,-93,1412,45,1367],57438:[698,-547,0,95,406],57441:[-141,390,0,11,322],57442:[-141,486,0,11,322],57443:[734,-508,0,94,485],57444:[777,-547,0,95,425],57445:[-141,371,0,1,331],57446:[770,-547,0,101,412],57447:[-141,371,0,1,331],57560:[584,0,400,57,343],57561:[665,0,255,56,199],57562:[665,0,388,56,332],57996:[474,-227,0,53,397],57997:[734,-484,0,94,460]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Marks"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Marks/Regular/Main.js"]);
