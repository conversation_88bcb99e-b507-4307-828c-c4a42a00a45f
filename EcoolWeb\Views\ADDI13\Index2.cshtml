﻿@model ADDI13IndexViewModel

@{
    ViewBag.Title = "紙本酷幣點數匯入個人帳戶";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string LogoAct = "GuestIndex";

}
@section css{
    <link href="@Url.Content("~/Content/styles/PointsCollection.min.css")" rel="stylesheet" />
    <style>
     /*本頁由學校首頁紙本兌換icon按鈕 或 ATM存款進入*/
    body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        background: linear-gradient(to bottom, #ddedff 0%,#7ea6d3 100%);
    }
        .bd2{   display: flex;
        flex-direction: column;
        min-height: 100vh;
        background: linear-gradient(to bottom, #ddedff 0%,#7ea6d3 100%);
        }

    .challenge-loading{
        width:100%;height:100%;
        background-color:@SharedGlobal.Logo_loading_background_color;
        display:none;z-index:999;position:fixed;left:0;top:0;
    }
    </style>
}
@Html.Partial("_Notice")

@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG" style="width:50%" class="img-responsive " alt="Responsive image" />*@
@using (Html.BeginForm("Index1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank", @class = "d-flex flex-column flex-grow-1 justify-content-center align-items-center" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.ROLL_CALL_ID)
    @Html.HiddenFor(m => m.SCHOOL_NO1)
    <div class="col-md-8 col-lg-6 row">
        <div class="points-collection my-3" id="panel">
            <h1 class="points-collection-title">紙本酷幣點數匯入個人帳戶</h1>



            <div class="input-group input-group-lg">
                <span class="input-group-addon"><i class="fa fa-user"></i></span>

                @Html.EditorFor(m => m.CARD_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "請感應數位學生證或輸入學號", @onKeyPress = "call(event,this);" } })
                @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                @Html.EditorFor(m => m.BarCode, new { htmlAttributes = new { @class = "form-control", @placeholder = "請掃描一維條碼，或輸入紙張裡面的代碼", @onKeyPress = "call(event,this);" } })

                @Html.ValidationMessage("BarCode", "", new { @class = "text-danger" })
            </div>
            <div class="text-center py-3">
                <button type="button" class="btn btn-primary mx-2 px-5 py-3" onclick="OnclickCardNO()">送出</button>
                <button type="button" class="btn btn-light border mx-2 px-5 py-3" onclick="myrefresh()">清除</button>
            </div>

            <div class="h5 bg-hint p-3">
                說明：
                <ol>
                    <li>此程式是將紙本的酷幣點數，匯入自己個人的酷幣帳戶。</li>
                    <li>每一個代碼都只能領取1次，領取後代碼就無效了。</li>
                    <li>有些活動限定一人只能領一次；有些活動一人可以領多次。</li>
                    <li>程式有問題請洽各校管理者，代碼有問題請洽發行老師。</li>
                </ol>
                <div class="text-center">
                    <img class="d-inline-block img-responsive" src="@Url.Content("~/Content/images/points-collection-sample.png")?v=20230913" alt="">
                </div>
            </div>

        </div>

    </div>



    <div id="DetailsView" class="bd2" style="width:100%">

        <div id="editorRows" class="tbody">
            @if (Model != null && Model.Details != null && Model.Details.Count() > 0)
            {
                @Html.Action("_EditDetailsList", (string)ViewBag.BRE_NO, new { model = Model.Details.ToList() })
            }
        </div>
    </div>

    <div id="loading" class="challenge-loading">
        <div class="d-block text-center mx-auto">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" height="350" />
            <br />
            <strong class="h3 text-info">讀取中…</strong>
        </div>
    </div>


    <div class="use-absolute" id="ErrorDiv">
        <div class="use-absoluteDiv">
            <div class="alert alert-danger" role="alert">
                <strong class="d-block h1">
                    <i class="fa fa-exclamation-circle"></i>
                    <span id="ErrorStr"></span>
                </strong>
            </div>
        </div>
    </div>

}

@section Scripts {

    <script src="@Url.Content("~/Scripts/buzz/buzz.min.js")"></script>

    <script>

        var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
        var game_trueSound = new buzz.sound("@Url.Content("~/Content/mp3/game_true.mp3")");
        var targetFormID = '#form1';

        var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );

        //(function ($) {
        //    $(window).on("beforeunload", function () {
        //        return true;
        //    })
        //})(jQuery);

        function ExportSave()        {
            $(targetFormID).attr("action", "@Url.Action("Export", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
        function myrefresh() {
            $("#CARD_NO").val('');
            $("#BarCode").val('');
            $("#StatusMessageDiv").remove();
           // window.location.reload();
        }
        function onSave() {
             var OK = confirm("您確定點完名了?")

            if (OK==true)
            {

                 $(targetFormID).attr("target","_self")
                 $(targetFormID).attr("action", "@Url.Action("RollCallSave", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
            }
        }

        $(document).ready(function () {
            //焦點點數領取輸入框
            $("#CARD_NO").focus();
        });
        function OnclickCardNO() {

            var SCHOOL_NO1 = "";
            var CARD_NO = $('#CARD_NO').val();
            SCHOOL_NO1 = $("#SCHOOL_NO1").val();

            if (CARD_NO != '') {

                if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length > 0) {
                    $('#ErrorStr').html('請勿重複刷卡，謝謝')
                    $('#ErrorDiv').show()

                    setTimeout(function () {
                        $('#CARD_NO').val('')
                        $("#CARD_NO").focus();
                        $('#ErrorStr').val('')
                        $('#ErrorDiv').hide()
                    }, 2000);
                }
                else {

                    $('#CARD_NO').prop('readonly', true);
                    $(".row").attr("style", "display:none");
                    setTimeout(function () {
                        OnKeyinUse(CARD_NO, SCHOOL_NO1)
                    });
                }

            }
        }

        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);
            var BarCode = "";
            BarCode = $("#BarCode").val();
            if (BarCode.length > 0) {
                if (code == 13) // 13 是 Enter 按鍵的值
                {
                    var SCHOOL_NO1 = "";
                    var Barcode = "";
                    var CARD_NO = $('#CARD_NO').val();
                    SCHOOL_NO1 = $("#SCHOOL_NO1").val();
                    Barcode = $("#BarCode").val();
                    event.preventDefault();

                    if (CARD_NO != '') {

                        if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length > 0) {
                            $('#ErrorStr').html('請勿重複刷卡，謝謝')
                            $('#ErrorDiv').show()

                            setTimeout(function () {
                                //$('#CARD_NO').val('')
                                $("#CARD_NO").focus();
                                $('#ErrorStr').val('')
                                $('#ErrorDiv').hide()
                            }, 2000);
                        }
                        else {

                            $('#CARD_NO').prop('readonly', true);
                            $(".row").attr("style", "display:none");
                            setTimeout(function () {
                                console.log(Barcode);
                                OnKeyinUse(CARD_NO, SCHOOL_NO1, Barcode)
                            });
                        }

                    }
                }
            }
            else {
                $("#BarCode").focus();
            }
        }

        function OnKeyinUse(CARD_NO, SCHOOL_NO1, Barcode) {
            $("#StatusMessageDiv").val('');

            $(window).unbind('beforeunload');
            var SCHOOL_NO1 = "";
            var Barcode = "";
            var CARD_NO = $('#CARD_NO').val();
            SCHOOL_NO1 = $("#SCHOOL_NO1").val();
            Barcode = $("#BarCode").val();
            var data = {
                "CARD_NO": CARD_NO,
                "SCHOOL_NO1": SCHOOL_NO1,
                "Barcode": Barcode
            };
            $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;");
            $.ajax({
                url: '@Url.Action("_OpenGetCashATM", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").html('');
                    $("#editorRows").prepend(html);
                    $('#loading').hide();
                    $('#ErrorDiv').show();



                    if (html.length > 0) {
            let pattern = /恭喜領取點數 成功！/i;
                        let result = $("#StatusMessageHtmlMsg").html().match(pattern);
                        if (result == "恭喜領取點數 成功！") {


                            game_trueSound.play();
                        }
                        else {
                            game_falseSound.play();
                        }

                        var l = 0;
                        l = $("#StatusMessageDiv").length;
                        if (l > 0) {
                            var i = 0;
                            $(".row").each(function () {
                                if (i == 0) {
                                    $(this).html('');
                                }
                                i++;
                            })
                        }
                        $('#ErrorStr').html('感應成功…')

                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");

                        setTimeout(function () {

                                $('#CARD_NO').prop('readonly', false);

                                $("#CARD_NO").focus();
                                $('#ErrorDiv').hide()
                                $('#ErrorStr').html('');


                        }, 100);
                        if ("@ViewBag.redirectUrl"!= "" && "@ViewBag.redirectUrl"!= "undefined") {

                            @*window.open("@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()@ViewBag.redirectUrl" + "&WhereKeyword=@ViewBag.BarCode");*@
                            setTimeout(function () {
                                window.open("@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()@ViewBag.redirectUrl", '_self');
                            },6000);
                        }
                    }
                    else {
                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");

                        $('#ErrorStr').html('此數位學生證對應不到學生資料…')
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                          //  $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 800);
                        if ("@ViewBag.redirectUrl"!= "" && "@ViewBag.redirectUrl"!= "undefined") {

                            @*window.open("@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()@ViewBag.redirectUrl" + "&WhereKeyword=@ViewBag.BarCode");*@
                            setTimeout(function () {
                                window.open("@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()@ViewBag.redirectUrl", '_self');
                            }, 6000);
                        }
                    }

                }
            });
        }

         function SwipeOK() {

                 SwipeSound.play();
         }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }


    </script>
}
