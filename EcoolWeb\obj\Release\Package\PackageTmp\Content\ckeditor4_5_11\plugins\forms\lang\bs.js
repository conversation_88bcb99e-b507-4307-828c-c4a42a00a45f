﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'bs', {
	button: {
		title: 'Button Properties',
		text: 'Text (Value)',
		type: 'Type',
		typeBtn: 'Button',
		typeSbm: 'Submit',
		typeRst: 'Reset'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Checkbox Properties',
		radioTitle: 'Radio Button Properties',
		value: 'Value',
		selected: 'Selected',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Form Properties',
		menu: 'Form Properties',
		action: 'Action',
		method: 'Method',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'Hidden Field Properties',
		name: 'Name',
		value: 'Value'
	},
	select: {
		title: 'Selection Field Properties',
		selectInfo: 'Select Info',
		opAvail: 'Available Options',
		value: 'Value',
		size: 'Size',
		lines: 'lines',
		chkMulti: 'Allow multiple selections',
		required: 'Required', // MISSING
		opText: 'Text',
		opValue: 'Value',
		btnAdd: 'Add',
		btnModify: 'Modify',
		btnUp: 'Up',
		btnDown: 'Down',
		btnSetValue: 'Set as selected value',
		btnDelete: 'Delete'
	},
	textarea: {
		title: 'Textarea Properties',
		cols: 'Columns',
		rows: 'Rows'
	},
	textfield: {
		title: 'Text Field Properties',
		name: 'Name',
		value: 'Value',
		charWidth: 'Character Width',
		maxChars: 'Maximum Characters',
		required: 'Required', // MISSING
		type: 'Type',
		typeText: 'Text',
		typePass: 'Password',
		typeEmail: 'Email', // MISSING
		typeSearch: 'Search', // MISSING
		typeTel: 'Telephone Number', // MISSING
		typeUrl: 'URL'
	}
} );
