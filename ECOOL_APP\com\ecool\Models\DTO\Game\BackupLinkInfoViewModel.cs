﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class BackupLinkInfoViewModel
    {
        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///備援id
        /// </summary>
        [DisplayName("備援id")]
        public int? BACKUP_ID { get; set; }

        /// <summary>
        ///備援名稱
        /// </summary>
        [DisplayName("備援名稱")]
        [Required]
        public string BACKUP_NAME { get; set; }

        /// <summary>
        ///備援網址
        /// </summary>
        [DisplayName("備援網址")]
        [Required]
        public string BACKUP_URL { get; set; }

        /// <summary>
        ///是否啟用
        /// </summary>
        [DisplayName("是否啟用")]
        [Required]
        public bool IS_ENABLE { get; set; }
    }
}