﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Controllers;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Routing;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.SessionState;
using System.Web.Http.WebHost;
using log4net;
using UAParser;
using EcoolWeb.App_Start;
using System.Net;
using System.IO;

namespace EcoolWeb
{
    public class MvcApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            AutoMapperConfig.Config();
            AreaRegistration.RegisterAllAreas();
            System.Web.Http.GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
            log4net.Config.XmlConfigurator.Configure(new FileInfo(Server.MapPath("~") + @"\log4net.config"));
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12| SecurityProtocolType.Ssl3;

            //System.Net.ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
            //if (System.Net.ServicePointManager.SecurityProtocol == (SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls))
            //    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;


            // System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3;
        }

        protected void Application_BeginRequest(Object sender, EventArgs e)
        {
            //暫時拿掉大概不會再開了~~

            //#if !DEBUG
            //            if (!Context.Request.IsSecureConnection)
            //            {
            //                Response.Redirect(Context.Request.Url.ToString().Replace("http://", "https://"));
            //            }
            //#endif

            //string ReMyLang = Request["LNG_FAMILY"];
            //if (ReMyLang != null)
            //{
            //    HttpCookie LNG_FAMILY = new HttpCookie("LNG_FAMILY");
            //    LNG_FAMILY.Value = ReMyLang.Trim();
            //    LNG_FAMILY.Expires.AddMinutes(30);
            //    Response.Cookies.Add(LNG_FAMILY);
            //    System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo(ReMyLang);
            //    System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(ReMyLang);
            //}
        }

        //為了讓API 有 Session------
        private void MvcApplication_PostAuthenticateRequest(object sender, EventArgs e)
        {
            System.Web.HttpContext.Current.SetSessionStateBehavior(
                SessionStateBehavior.Required);
        }

        //--------為了讓API 有 Session End
        private void Session_End(object sender, EventArgs e)
        {
        }

        private void Session_Start(object sender, EventArgs e)
        {
            //啟動新工作階段時執行的程式碼
#if DEBUG
            Session.Timeout = 60 * 3;

            //object session_user = Session[CONSTANT.SESSION_USER_KEY];
            //HRMT01 FindUser;
            //string SchoolNo = "403605";
            //string UserNo = "0000";
            ////string UserNo = "0002";

            //ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            //FindUser = db.HRMT01.Include("HRMT25").Where(user => user.SCHOOL_NO == SchoolNo && user.USER_NO == UserNo).FirstOrDefault();

            //if (FindUser != null)
            //{
            //    //填入UserProfile
            //    UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
            //    UserProfileHelper.Set(LoginUser);
            //    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = LoginUser;
            //    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] = "403605";
            //}

#endif
        }

        private void Application_Error(object sender, EventArgs e)
        {
            var userAgent = HttpContext.Current.Request.UserAgent;
            var uaParser = Parser.GetDefault();
            ClientInfo c = uaParser.Parse(userAgent);

            log4net.ILog logger = LogManager.GetLogger(typeof(MvcApplication));
            Exception objErr = Server.GetLastError().GetBaseException();
            string ErrorMsg = string.Empty;


            ErrorMsg = ErrorMsg + $@"Error OS:{c.OS.Family.ToString()},UserAgent:{c.UserAgent.Family.ToString()},Device:{c.Device.Family.ToString()}" + Environment.NewLine;
            ErrorMsg = ErrorMsg + $@"Error in:" + Request.Url.ToString() + Environment.NewLine;
            ErrorMsg = ErrorMsg + $@"Error Message:" + objErr.Message.ToString() + Environment.NewLine;

            if (ErrorMsg.Length <= 4000 && objErr.ToString().Length <= 2000)
            {
                logger.Error(ErrorMsg, objErr);
            }
            else
            {
                logger.Error("Error OS:" + c.OS.Family.ToString() + ",UserAgent:" + c.UserAgent.Family.ToString() + ",Device:" + c.Device.Family.ToString());
                logger.Error("Error in:" + Request.Url.ToString());
                logger.Error("Error Source:" + objErr.StackTrace.ToString());
                logger.Error("Error Message:" + objErr.Message.ToString());
            }

            // 發生未處理錯誤時執行的程式碼

            var app = (MvcApplication)sender;
            var ex = app.Server.GetLastError();

            var context = app.Context;
            context.Response.Clear();
            context.ClearError();

            var httpException = ex as HttpException;
            if (httpException == null)
            {
                httpException = new HttpException(null, ex);
            }

            var routeData = new RouteData();

            routeData.Values["controller"] = "Error";
            routeData.Values["action"] = "Index";

            routeData.Values["exception"] = ex;
            routeData.Values["from_Application_Error_Event"] = true;

            if (httpException != null)
            {
                switch (httpException.GetHttpCode())
                {
                    case 404:
                        if (httpException.Message.IndexOf("未實作 IController") > 0)
                        {
                            routeData.Values["action"] = "ControllerNotFound";
                        }
                        else
                        {
                            routeData.Values["action"] = "PageNotFound";
                        }

                        break;

                    case 500:
                        routeData.Values["action"] = "InternalError";
                        break;

                    default:
                        routeData.Values["action"] = "GenericError";
                        break;
                }
                routeData.Values.Add("HttpCode", httpException.GetHttpCode());
            }

            // Pass exception details to the target error View.
            if (ex.Message.IndexOf(objErr.Message.ToString()) == -1)
            {
                routeData.Values.Add("error", objErr.Message.ToString() + Environment.NewLine + ex.Message);
            }
            else
            {
                // Pass exception details to the target error View.
                routeData.Values.Add("error", ex.Message);
            }

            // Avoid IIS7 getting in the middle
            context.Response.TrySkipIisCustomErrors = true;
            IController controller = new ErrorController();
            controller.Execute(new RequestContext(new HttpContextWrapper(context), routeData));
        }

        public class SessionableControllerHandler : HttpControllerHandler, IRequiresSessionState
        {
            public SessionableControllerHandler(RouteData routeData)
                : base(routeData)
            { }
        }

        public class SessionStateRouteHandler : IRouteHandler
        {
            IHttpHandler IRouteHandler.GetHttpHandler(RequestContext requestContext)
            {
                return new SessionableControllerHandler(requestContext.RouteData);
            }
        }
    }
}