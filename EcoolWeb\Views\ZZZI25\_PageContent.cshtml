﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZ25IndexViewModel

@helper  UrlFun(string IMG_ID, string IMG_FILE)
{
    string ImgUrl = string.Empty;

    if (IMG_FILE != null && IMG_FILE != string.Empty)
    {
        ImgUrl = string.Format(@"{0}{1}\{2}", ViewBag.SYSUrl, IMG_ID, IMG_FILE);
        <img src="@Url.Content(ImgUrl)" style="width:110pt;height:80pt" />
    }
}

@if (Model?.IMG_TYPE == ADDT19.IMG_TYPEVal.PremierViewImage)
{

    Html.RenderAction("_Menu", "ADDI12", new { NowAction = ADDI12IndexListViewModel.ActionResultTypeVal.PremierViewImage });
    <font style="color:red">※輪播六張圖以內，橫幅可以直接超連結，最佳大小500*200</font><br />
    @Html.PermissionButton("新增首播圖片輪播設定", "button", (string)ViewBag.PermissionBRE_NO, (string)ViewBag.PermissionAction, new { @class = "btn btn-sm btn-sys", @onclick = "onAdd()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)



}
else
{
    @Html.PermissionButton("新增", "button", (string)ViewBag.PermissionBRE_NO, (string)ViewBag.PermissionAction, new { @class = "btn btn-sm btn-sys", @onclick = "onAdd()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)

}

@Html.HiddenFor(model => model.Search.Q_IMG_ID)
@Html.HiddenFor(model => model.Search.STATUS)
@Html.HiddenFor(model => model.IMG_TYPE)
<br />
<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">搜尋欲瀏覽之相關字串</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.Search.SearchContents, new { htmlAttributes = new { @class = "form-control" } })
        @Html.HiddenFor(model => model.Search.Page)
        @Html.HiddenFor(model => model.Search.OrderByName)
        @Html.HiddenFor(model => model.Search.SyntaxName)
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
<br />

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th>
                        查看
                    </th>
                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('IMG_TITLE')">
                            @Html.DisplayNameFor(model => model.ADDT19List.First().IMG_TITLE)
                        </samp>
                    </th>

                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('IMG_DATES')">
                            @Html.DisplayNameFor(model => model.ADDT19List.First().IMG_DATES)
                        </samp>
                    </th>
                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('IMG_DATEE')">
                            @Html.DisplayNameFor(model => model.ADDT19List.First().IMG_DATEE)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('IMG_FILE')">
                            @Html.DisplayNameFor(model => model.ADDT19List.First().IMG_FILE)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('STATUS')">
                            @Html.DisplayNameFor(model => model.ADDT19List.First().STATUS)
                        </samp>
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.ADDT19List)
                {
                    <tr onclick="onBtnLink('@item.IMG_ID','@EcoolWeb.Controllers.ZZZI11Controller.DATA_TYPE.DATA_TYPE_U')" title="詳細內容" style="cursor:pointer">
                        <td align="center">
                            <button class="btn btn-xs btn-Basic" onclick="onBtnLink('@item.IMG_ID','')">
                                檢示
                            </button>
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.IMG_TITLE)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.IMG_DATES)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.IMG_DATEE)
                        </td>
                        <td align="center">
                            @UrlFun(item.IMG_ID, item.IMG_FILE)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.STATUS_NAME)
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<div>
    @Html.Pager(Model.ADDT19List.PageSize, Model.ADDT19List.PageNumber, Model.ADDT19List.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
    )
</div>