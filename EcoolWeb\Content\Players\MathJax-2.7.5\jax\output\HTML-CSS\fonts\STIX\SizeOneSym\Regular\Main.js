/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeOneSym/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeOneSym={directory:"SizeOneSym/Regular",family:"STIXSizeOneSym",Ranges:[[688,767,"All"],[768,824,"All"],[8254,8254,"All"],[8400,8431,"All"],[8512,8512,"All"],[8730,8732,"All"],[8992,8993,"All"],[9115,9145,"All"],[9180,9185,"All"],[10098,10099,"All"],[10214,10219,"All"],[10627,10630,"All"],[10744,10745,"All"],[10752,10762,"All"],[11004,11007,"All"]],32:[0,0,250,0,0],40:[1066,164,468,139,382],41:[1066,164,468,86,329],47:[1066,164,579,25,552],91:[1066,164,383,180,363],92:[1066,164,579,27,552],93:[1066,164,383,20,203],95:[-127,177,1000,0,1000],123:[1066,164,575,114,466],125:[1066,164,575,109,461],160:[0,0,250,0,0],770:[767,-554,0,-720,-160],771:[750,-598,0,-722,-162],8719:[1500,-49,1355,50,1305],8720:[1500,-49,1355,50,1305],8721:[1499,-49,1292,90,1202],8730:[1552,295,1057,112,1089],8896:[1500,-49,1265,60,1205],8897:[1500,-49,1265,60,1205],8898:[1510,-49,1265,118,1147],8899:[1500,-39,1265,118,1147],8968:[1066,164,453,180,426],8969:[1066,164,453,25,273],8970:[1066,164,453,180,428],8971:[1066,164,453,27,273],9115:[700,305,450,50,400],9116:[705,305,450,50,174],9117:[705,300,450,50,400],9118:[700,305,450,50,400],9119:[705,305,450,276,400],9120:[705,300,450,50,400],9121:[682,323,450,50,415],9122:[687,323,450,50,150],9123:[687,318,450,50,415],9124:[682,323,450,35,400],9125:[687,323,450,300,400],9126:[687,318,450,35,400],9127:[700,305,640,260,600],9128:[705,305,640,40,380],9129:[705,300,640,260,600],9130:[705,305,640,260,380],9131:[700,305,640,40,380],9132:[705,305,640,260,600],9133:[705,300,640,40,380],9134:[610,25,688,294,394],9136:[700,301,600,35,566],9137:[700,301,600,35,566],9143:[1510,345,1184,112,895],9144:[1566,289,721,0,66],9145:[1566,289,721,655,721],9182:[136,89,926,0,925],9183:[789,-564,926,0,925],10216:[1066,164,578,116,462],10217:[1066,164,578,116,462],10752:[1500,-49,1555,52,1503],10753:[1500,-49,1555,52,1503],10754:[1500,-49,1555,52,1503],10756:[1500,-39,1265,118,1147],10757:[1500,-49,1153,82,1071],10758:[1500,-49,1153,82,1071]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeOneSym");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeOneSym/Regular/Main.js");
