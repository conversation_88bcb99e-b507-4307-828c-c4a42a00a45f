﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'pl', {
	access: 'Dostęp skryptów',
	accessAlways: '<PERSON><PERSON><PERSON>',
	accessNever: 'Nigdy',
	accessSameDomain: 'Ta sama domena',
	alignAbsBottom: 'Do dołu',
	alignAbsMiddle: 'Do środka w pionie',
	alignBaseline: 'Do linii bazowej',
	alignTextTop: 'Do góry tekstu',
	bgcolor: 'Kolor tła',
	chkFull: 'Zezwól na pełny ekran',
	chkLoop: 'Pętla',
	chkMenu: 'Włącz menu',
	chkPlay: 'Autoodtwarzanie',
	flashvars: 'Zmienne obiektu Flash',
	hSpace: 'Odstęp poziomy',
	properties: 'Właściwości obiektu Flash',
	propertiesTab: 'Właściwości',
	quality: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	qualityAutoHigh: 'Auto wysoka',
	qualityAutoLow: 'Auto niska',
	qualityBest: 'Najlepsza',
	qualityHigh: 'Wysoka',
	qualityLow: 'Niska',
	qualityMedium: 'Średnia',
	scale: 'Skaluj',
	scaleAll: 'Pokaż wszystko',
	scaleFit: 'Dokładne dopasowanie',
	scaleNoBorder: 'Bez obramowania',
	title: 'Właściwości obiektu Flash',
	vSpace: 'Odstęp pionowy',
	validateHSpace: 'Odstęp poziomy musi być liczbą.',
	validateSrc: 'Podaj adres URL',
	validateVSpace: 'Odstęp pionowy musi być liczbą.',
	windowMode: 'Tryb okna',
	windowModeOpaque: 'Nieprzezroczyste',
	windowModeTransparent: 'Przezroczyste',
	windowModeWindow: 'Okno'
} );
