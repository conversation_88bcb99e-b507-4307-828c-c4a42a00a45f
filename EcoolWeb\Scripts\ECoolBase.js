﻿$(function () {

    try {
        $.datepicker.regional['zh-TW'] = {
            dayNames: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
            dayNamesMin: ["日", "一", "二", "三", "四", "五", "六"],
            monthNames: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            monthNamesShort: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            prevText: "<",
            nextText: ">",
            weekHeader: "週"
        };
        $.datepicker.setDefaults($.datepicker.regional["zh-TW"]);
    } catch (e) {

    }
 
});

function FileUpload_click() {
    var reg = /^.+\.(JPG|JPEG|TIF|TIFF|GIF|PDF|jpg|jpeg|tif|tiff|gif|pdf)/;
    if (!reg.test($("#file").val())) {
        alert('上傳檔案格式錯誤');
        return false;
    }
}