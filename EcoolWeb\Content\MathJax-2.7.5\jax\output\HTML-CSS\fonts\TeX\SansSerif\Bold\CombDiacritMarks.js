/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Bold/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_SansSerif-bold"],{768:[694,-537,0,-458,-218],769:[694,-537,0,-334,-93],770:[694,-537,0,-442,-109],771:[694,-548,0,-458,-93],772:[660,-560,0,-474,-77],774:[694,-552,0,-470,-80],775:[695,-596,0,-356,-194],776:[695,-595,0,-459,-91],778:[694,-538,0,-365,-119],779:[694,-537,0,-440,-94],780:[657,-500,0,-442,-109]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Bold/CombDiacritMarks.js");
