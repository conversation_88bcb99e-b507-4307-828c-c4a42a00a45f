﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI28AddViewModel
    {
        [DisplayName("學校")]
        [Required]
        public string SCHOOL_NO { get; set; }

        [DisplayName("家長帳號")]
        public string PARENTS_USER_NO { get; set; }

        [DisplayName("學生學號")]
        public string STUDENT_USER_NO { get; set; }

        [DisplayName("學生學號")]
        public string STUDENT_USER_NO2 { get; set; }

        [DisplayName("學生班級")]
        public string CLASS_NO { get; set; }

        [DisplayName("學生身份証")]
        public string IDNO { get; set; }

        [DisplayName("學生登入密碼")]
        [DataType(DataType.Password)]
        public string STUDENT_PWD { get; set; }

        [DisplayName("學生生日")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime BIRTHDAY { get; set; }

        public string CRE_PERSON { get; set; }

        [DisplayName("步驟")]
        public int NUM_TYPE { get; set; }

        [DisplayName("來源程式")]
        public string REF_BRE_NO { get; set; }
    }
}
