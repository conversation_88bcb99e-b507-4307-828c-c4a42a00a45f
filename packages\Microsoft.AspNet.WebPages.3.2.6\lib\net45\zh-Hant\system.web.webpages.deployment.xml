﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.WebPages.Deployment</name>
  </assembly>
  <members>
    <member name="T:System.Web.WebPages.Deployment.PreApplicationStartCode">
      <summary>提供適用於網頁部署之應用程式啟動前程式碼的註冊點。</summary>
    </member>
    <member name="M:System.Web.WebPages.Deployment.PreApplicationStartCode.Start">
      <summary>註冊適用於網頁部署的應用程式啟動前程式碼。</summary>
    </member>
    <member name="T:System.Web.WebPages.Deployment.WebPagesDeployment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供用來取得 Web 應用程式的相關部署資訊的方法。</summary>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetAssemblyPath(System.Version)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得網頁部署的組件路徑。</summary>
      <returns>網頁部署的組件路徑。</returns>
      <param name="version">網頁版本。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetExplicitWebPagesVersion(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得給定二進位路徑的網頁版本。</summary>
      <returns>網頁版本。</returns>
      <param name="path">網頁的二進位路徑。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetIncompatibleDependencies(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得給定路徑的組件參照 (無論網頁版本為何)。</summary>
      <returns>字典包含網路和其版本的組件參照。</returns>
      <param name="appPath">網頁應用程式的路徑。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetMaxVersion">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得網頁載入組件的最新版本。</summary>
      <returns>網頁載入組件的最新版本。</returns>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetVersion(System.String)">
      <summary>取得給定路徑的網頁版本。</summary>
      <returns>網頁版本。</returns>
      <param name="path">應用程式之根目錄的路徑。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetVersionWithoutEnabledCheck(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。透過指定路徑，使用組態設定來取得網頁版本。</summary>
      <returns>網頁版本。</returns>
      <param name="path">應用程式設定的路徑。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetWebPagesAssemblies">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回網頁部署的路徑。</summary>
      <returns>包含此網頁部署組件的清單。</returns>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.IsEnabled(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。指出是否要啟用網頁部署。</summary>
      <returns>如果已啟用網頁部署，則為 true，否則為 false。</returns>
      <param name="path">網頁部署的路徑。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.IsExplicitlyDisabled(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。指出是否要明確停用網頁部署。</summary>
      <returns>如果已明確停用網頁部署，則為 true，否則為 false。</returns>
      <param name="path">網頁部署的路徑。</param>
    </member>
  </members>
</doc>