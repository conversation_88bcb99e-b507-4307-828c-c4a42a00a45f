/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Arrows/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Arrows={directory:"Arrows/Regular",family:"NeoEulerMathJax_Arrows",testString:"\u00A0\u21A4\u27FB\u27FD\u27FE",32:[0,0,333,0,0],160:[0,0,333,0,0],8612:[500,0,1000,56,944],10235:[500,0,1690,56,1634],10237:[598,98,1700,76,1643],10238:[598,98,1700,75,1643]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Arrows"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Arrows/Regular/Main.js"]);
