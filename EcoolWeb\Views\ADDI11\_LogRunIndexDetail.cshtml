﻿@model ADDI11IndexViewModel
@using com.ecool.service
@{ string HidStyle = Model.IsPrint == true ? "display:none" : "";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    var STR = PermissionService.GetPermission_Use_YN("ADDI11", "_ADDRunEdit", user.SCHOOL_NO, user.USER_NO);}


<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>
<style type="text/css">


    .showContent {
        overflow: hidden;
    }
</style>
@Html.HiddenFor(m => m.WhereUserNo)
@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.IsPrint)
@Html.HiddenFor(m => m.IsToExcel)
@Html.HiddenFor(m => m.WhereIsMonthTop)

<div class="form-inline" role="form" id="Q_Div" style="@HidStyle">
    <div class="form-group">
        <label class="control-label">學號/姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.WhereKeyword, new { htmlAttributes = new { @class = "form-control" } })
    </div>
    <div class="form-group">
        <label class="control-label">年級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control", onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "FunPageProc(1)" })
    </div>
    <div id="Sdate">
        <div class="form-group">
            <label class="control-label">跑步日期(起)</label>
        </div>

        <div class="form-group">
            @Html.EditorFor(m => m.WhereStartRunDate, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
        </div>
        <div class="form-group">
            <label class="control-label">跑步日期(迄)</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.WhereendRunDate, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
        </div>
        <div>

        </div>
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />

</div>

<div id="btnDiv" class="showContent">


    <div class="form-inline" style="text-align:left">
        <div class="col-xs-6 text-left" style="padding-left: 0px">
            <button class="btn btn-xs btn-pink @(Model.WhereUserType == "S" ? "active":"")" type="button" onclick="todoClear();doUSERTYPEGet('S');">學生</button>
            <button class="btn btn-xs btn-pink @(Model.WhereUserType == "T" ? "active":"")" type="button" onclick="todoClear();doUSERTYPEGet('T');">老師</button>
            <button class="btn btn-xs btn-pink @(Model.WhereUserType == "ALL" ? "active":"")" type="button" onclick="todoClear();doUSERTYPEGet('ALL');">全部</button>

        </div>
       
    </div>
</div>
<img src="~/Content/img/web-bar-Run.png" style="width:100%;@HidStyle" class="img-responsive App_hide " alt="Responsive image" />
<div class="@(Model.IsPrint ? "":"table-responsive")">
    <div class="text-center" id="tbData">
        <table class="@(Model.IsPrint ? "table table-bordered" : "table-ecool table-92Per table-hover table-ecool-reader")">
            <thead>
                <tr>
                    <th></th>
                    <th>編號</th>
                    <th  onclick="doSort('CLASS_NO');">
                        @Html.DisplayNameFor(m => m.ListData.First().CLASS_NO)

                                        <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                    </th>
                    <th  onclick="doSort('SEAT_NO');">
                        @Html.DisplayNameFor(m => m.ListData.First().SEAT_NO)
                       <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                    </th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().SNAME)</th>

                    <th  onclick="doSort('LAP');">
                        @Html.DisplayNameFor(m => m.ListData.First().LAP)

                                        <img id="LAP" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                    </th>
                    <th  onclick="doSort('CHG_DATE');">
                        跑步日期

                        <img id="CHG_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @{int Row = 1; }
                @foreach (var item in Model.ListData)
                {

                <tr>
                    @if (STR == "Y")
                    {
                        <td style="text-align: center">   <a role='button' style="cursor:pointer;" onclick="deleteRow('@item.USER_NO','@item.RUN_DATE','@item.LAP','@item.RUN_ID')"> <i class='glyphicon glyphicon-remove'></i></a></td>
                    }
                    else
                    {

                        <td style="text-align: center">
                            
                        </td>
                    }
                    <td style="text-align: center">
                        @Row
                    </td>
                    <td style="text-align: center">@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                    <td style="text-align: center">@Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                    <td>@Html.DisplayFor(modelItem => item.SNAME)</td>
                    <td style="text-align: center;">@Html.DisplayFor(modelItem => item.LAP)</td>
                    <td style="text-align: center">
                        @(item.CHG_DATE)
                    </td>
                    @if (STR == "Y")
                    {
                        <td style="text-align: center">
                            <a href="@Url.Action("_ADDRunEdit","ADDI11",new { USER_NO=item.USER_NO,RunDtae=item.RUN_DATE,item.LAP,item.RUN_ID})" class="btn btn-sm btn-bold btn-pink colorbox ">編輯</a>
                        </td>}
                </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@if (Model.ListData.Count() == 0)
{
    <div class="text-center">
        <h3>暫時無資料</h3>
    </div>
}

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
</div>
<script type="text/javascript">
      function doUSERTYPEGet(val) {
       
            $("#@Html.IdFor(m=>m.WhereUserType)").val(val);
            FunPageProc(1)
    }
    function doSort(SortCol) {
        $("#OrdercColumn").val(SortCol);
        FunPageProc(1);
    }
    $(function () {
        var MonthDEF = "";
        MonthDEF = $(".btn-pink.active").attr("Mouth");
        if (MonthDEF == "true") {
            $("#Sdate").attr("hidden", "hidden");
        }
        initDatepicker();

    });
    $(document).ready(function () {
        $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });

    function initDatepicker() {
                   var opt = {
                        showMonthAfterYear: true,
                        format: moment().format('YYYY-MM-DD'),
                        showSecond: true,
                        showButtonPanel: true,
                        showTime: true,
                        beforeShow: function () {
                            setTimeout(
                                function () {
                                    $('#ui-datepicker-div').css("z-index", 15);
                                }, 100
                            );
                        },
                        onSelect: function (dateText, inst) {
                            $('#' + inst.id).attr('value', dateText);
                        }
                    };
                    $("#@Html.IdFor(m => m.WhereStartRunDate)").datetimepicker(opt);
                    $("#@Html.IdFor(m => m.WhereendRunDate)").datetimepicker(opt);
        }
</script>