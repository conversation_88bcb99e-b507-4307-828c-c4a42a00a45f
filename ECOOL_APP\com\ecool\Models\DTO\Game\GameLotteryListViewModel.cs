﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameLotteryListViewModel
    {
        /// <summary>
        ///抽獎id
        /// </summary>
        [DisplayName("抽獎id")]
        public string LOTTERY_NO { get; set; }

        public string ITEM_NO { get; set; }

        public string TEMP_USER_ID { get; set; }

        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///建立日
        /// </summary>
        [DisplayName("抽獎時間")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///限制關卡數
        /// </summary>
        [DisplayName("限制關卡數")]
        public int? LEVEL_COUNT { get; set; }

        /// <summary>
        ///限制完成某關卡數
        /// </summary>
        [DisplayName("限制完成某關卡數")]
        public string[] ArrLEVEL { get; set; }

        public string LEVEL { get; set; }

        /// <summary>
        ///抽獎人數
        /// </summary>
        [DisplayName("抽獎人數")]
        [Required(ErrorMessage = "此欄位必輸")]
        public int? PEOPLE_COUNT { get; set; }

        /// <summary>
        ///是否排除中獎人員 1.排除 0.不排除
        /// </summary>
        [DisplayName("排除中獎人員")]
        public bool UNLOTTERY { get; set; }

        /// <summary>
        ///是否排除訪客 1.排除 0.不排除
        /// </summary>
        [DisplayName("排除訪客")]
        public bool UNCUEST { get; set; }

        /// <summary>
        ///是否排除不是滿分 1.排除 0.不排除
        /// </summary>
        [DisplayName("排除非滿分")]
        public bool IS_FULL { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public string STATUS { get; set; }

        /// <summary>
        ///獎品名稱
        /// </summary>
        [DisplayName("獎品名稱")]
        [Required(ErrorMessage = "此欄位必輸")]
        public string LOTTERY_DESC { get; set; }

        /// <summary>
        /// 是否領獎
        /// </summary>
        [DisplayName("是否領獎")]
        public bool RECEIVE_AWARD { get; set; }

        /// <summary>
        /// 可重新抽獎
        /// </summary>
        [DisplayName("可重新抽獎")]
        public bool IS_GIVE_UP_LOTTERY { get; set; }
    }
}