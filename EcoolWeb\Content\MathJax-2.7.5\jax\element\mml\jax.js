/*
 *  /MathJax/jax/element/mml/jax.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.ElementJax.mml=MathJax.ElementJax({mimeType:"jax/mml"},{id:"mml",version:"2.7.5",directory:MathJax.ElementJax.directory+"/mml",extensionDir:MathJax.ElementJax.extensionDir+"/mml",optableDir:MathJax.ElementJax.directory+"/mml/optable"});MathJax.ElementJax.mml.Augment({Init:function(){if(arguments.length===1&&arguments[0].type==="math"){this.root=arguments[0]}else{this.root=MathJax.ElementJax.mml.math.apply(this,arguments)}if(this.root.attr&&this.root.attr.mode){if(!this.root.display&&this.root.attr.mode==="display"){this.root.display="block";this.root.attrNames.push("display")}delete this.root.attr.mode;for(var b=0,a=this.root.attrNames.length;b<a;b++){if(this.root.attrNames[b]==="mode"){this.root.attrNames.splice(b,1);break}}}}},{INHERIT:"_inherit_",AUTO:"_auto_",SIZE:{INFINITY:"infinity",SMALL:"small",NORMAL:"normal",BIG:"big"},COLOR:{TRANSPARENT:"transparent"},VARIANT:{NORMAL:"normal",BOLD:"bold",ITALIC:"italic",BOLDITALIC:"bold-italic",DOUBLESTRUCK:"double-struck",FRAKTUR:"fraktur",BOLDFRAKTUR:"bold-fraktur",SCRIPT:"script",BOLDSCRIPT:"bold-script",SANSSERIF:"sans-serif",BOLDSANSSERIF:"bold-sans-serif",SANSSERIFITALIC:"sans-serif-italic",SANSSERIFBOLDITALIC:"sans-serif-bold-italic",MONOSPACE:"monospace",INITIAL:"initial",TAILED:"tailed",LOOPED:"looped",STRETCHED:"stretched",CALIGRAPHIC:"-tex-caligraphic",OLDSTYLE:"-tex-oldstyle"},FORM:{PREFIX:"prefix",INFIX:"infix",POSTFIX:"postfix"},LINEBREAK:{AUTO:"auto",NEWLINE:"newline",NOBREAK:"nobreak",GOODBREAK:"goodbreak",BADBREAK:"badbreak"},LINEBREAKSTYLE:{BEFORE:"before",AFTER:"after",DUPLICATE:"duplicate",INFIXLINBREAKSTYLE:"infixlinebreakstyle"},INDENTALIGN:{LEFT:"left",CENTER:"center",RIGHT:"right",AUTO:"auto",ID:"id",INDENTALIGN:"indentalign"},INDENTSHIFT:{INDENTSHIFT:"indentshift"},LINETHICKNESS:{THIN:"thin",MEDIUM:"medium",THICK:"thick"},NOTATION:{LONGDIV:"longdiv",ACTUARIAL:"actuarial",RADICAL:"radical",BOX:"box",ROUNDEDBOX:"roundedbox",CIRCLE:"circle",LEFT:"left",RIGHT:"right",TOP:"top",BOTTOM:"bottom",UPDIAGONALSTRIKE:"updiagonalstrike",DOWNDIAGONALSTRIKE:"downdiagonalstrike",UPDIAGONALARROW:"updiagonalarrow",VERTICALSTRIKE:"verticalstrike",HORIZONTALSTRIKE:"horizontalstrike",PHASORANGLE:"phasorangle",MADRUWB:"madruwb"},ALIGN:{TOP:"top",BOTTOM:"bottom",CENTER:"center",BASELINE:"baseline",AXIS:"axis",LEFT:"left",RIGHT:"right"},LINES:{NONE:"none",SOLID:"solid",DASHED:"dashed"},SIDE:{LEFT:"left",RIGHT:"right",LEFTOVERLAP:"leftoverlap",RIGHTOVERLAP:"rightoverlap"},WIDTH:{AUTO:"auto",FIT:"fit"},ACTIONTYPE:{TOGGLE:"toggle",STATUSLINE:"statusline",TOOLTIP:"tooltip",INPUT:"input"},LENGTH:{VERYVERYTHINMATHSPACE:"veryverythinmathspace",VERYTHINMATHSPACE:"verythinmathspace",THINMATHSPACE:"thinmathspace",MEDIUMMATHSPACE:"mediummathspace",THICKMATHSPACE:"thickmathspace",VERYTHICKMATHSPACE:"verythickmathspace",VERYVERYTHICKMATHSPACE:"veryverythickmathspace",NEGATIVEVERYVERYTHINMATHSPACE:"negativeveryverythinmathspace",NEGATIVEVERYTHINMATHSPACE:"negativeverythinmathspace",NEGATIVETHINMATHSPACE:"negativethinmathspace",NEGATIVEMEDIUMMATHSPACE:"negativemediummathspace",NEGATIVETHICKMATHSPACE:"negativethickmathspace",NEGATIVEVERYTHICKMATHSPACE:"negativeverythickmathspace",NEGATIVEVERYVERYTHICKMATHSPACE:"negativeveryverythickmathspace"},OVERFLOW:{LINBREAK:"linebreak",SCROLL:"scroll",ELIDE:"elide",TRUNCATE:"truncate",SCALE:"scale"},UNIT:{EM:"em",EX:"ex",PX:"px",IN:"in",CM:"cm",MM:"mm",PT:"pt",PC:"pc"},TEXCLASS:{ORD:0,OP:1,BIN:2,REL:3,OPEN:4,CLOSE:5,PUNCT:6,INNER:7,VCENTER:8,NONE:-1},TEXCLASSNAMES:["ORD","OP","BIN","REL","OPEN","CLOSE","PUNCT","INNER","VCENTER"],skipAttributes:{texClass:true,useHeight:true,texprimestyle:true},copyAttributes:{displaystyle:1,scriptlevel:1,open:1,close:1,form:1,actiontype:1,fontfamily:true,fontsize:true,fontweight:true,fontstyle:true,color:true,background:true,id:true,"class":1,href:true,style:true},copyAttributeNames:["displaystyle","scriptlevel","open","close","form","actiontype","fontfamily","fontsize","fontweight","fontstyle","color","background","id","class","href","style"],nocopyAttributes:{fontfamily:true,fontsize:true,fontweight:true,fontstyle:true,color:true,background:true,id:true,"class":true,href:true,style:true,xmlns:true},Error:function(d,e){var c=this.merror(d),b=MathJax.Localization.fontDirection(),a=MathJax.Localization.fontFamily();if(e){c=c.With(e)}if(b||a){c=this.mstyle(c);if(b){c.dir=b}if(a){c.style.fontFamily="font-family: "+a}}return c}});(function(a){a.mbase=MathJax.Object.Subclass({type:"base",isToken:false,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,dir:a.INHERIT},noInherit:{},noInheritAttribute:{texClass:true},getRemoved:{},linebreakContainer:false,Init:function(){this.data=[];if(this.inferRow&&!(arguments.length===1&&arguments[0].inferred)){this.Append(a.mrow().With({inferred:true,notParent:true}))}this.Append.apply(this,arguments)},With:function(e){for(var f in e){if(e.hasOwnProperty(f)){this[f]=e[f]}}return this},Append:function(){if(this.inferRow&&this.data.length){this.data[0].Append.apply(this.data[0],arguments)}else{for(var f=0,e=arguments.length;f<e;f++){this.SetData(this.data.length,arguments[f])}}},SetData:function(e,f){if(f!=null){if(!(f instanceof a.mbase)){f=(this.isToken||this.isChars?a.chars(f):a.mtext(f))}f.parent=this;f.setInherit(this.inheritFromMe?this:this.inherit)}this.data[e]=f},Parent:function(){var e=this.parent;while(e&&e.notParent){e=e.parent}return e},Get:function(f,k,l){if(!l){if(this[f]!=null){return this[f]}if(this.attr&&this.attr[f]!=null){return this.attr[f]}}var g=this.Parent();if(g&&g["adjustChild_"+f]!=null){return(g["adjustChild_"+f])(this.childPosition(),k)}var j=this.inherit;var e=j;while(j){var i=j[f];if(i==null&&j.attr){i=j.attr[f]}if(j.removedStyles&&j.getRemoved[f]&&i==null){i=j.removedStyles[j.getRemoved[f]]}if(i!=null&&j.noInheritAttribute&&!j.noInheritAttribute[f]){var h=j.noInherit[this.type];if(!(h&&h[f])){return i}}e=j;j=j.inherit}if(!k){if(this.defaults[f]===a.AUTO){return this.autoDefault(f)}if(this.defaults[f]!==a.INHERIT&&this.defaults[f]!=null){return this.defaults[f]}if(e){return e.defaults[f]}}return null},hasValue:function(e){return(this.Get(e,true)!=null)},getValues:function(){var f={};for(var g=0,e=arguments.length;g<e;g++){f[arguments[g]]=this.Get(arguments[g])}return f},adjustChild_scriptlevel:function(f,e){return this.Get("scriptlevel",e)},adjustChild_displaystyle:function(f,e){return this.Get("displaystyle",e)},adjustChild_texprimestyle:function(f,e){return this.Get("texprimestyle",e)},hasMMLspacing:function(){return false},childPosition:function(){var h=this,g=h.parent;while(g.notParent){h=g;g=h.parent}for(var f=0,e=g.data.length;f<e;f++){if(g.data[f]===h){return f}}return null},setInherit:function(g){if(g!==this.inherit&&this.inherit==null){this.inherit=g;for(var f=0,e=this.data.length;f<e;f++){if(this.data[f]&&this.data[f].setInherit){this.data[f].setInherit(g)}}}},setTeXclass:function(e){this.getPrevClass(e);return(typeof(this.texClass)!=="undefined"?this:e)},getPrevClass:function(e){if(e){this.prevClass=e.Get("texClass");this.prevLevel=e.Get("scriptlevel")}},updateTeXclass:function(e){if(e){this.prevClass=e.prevClass;delete e.prevClass;this.prevLevel=e.prevLevel;delete e.prevLevel;this.texClass=e.Get("texClass")}},texSpacing:function(){var f=(this.prevClass!=null?this.prevClass:a.TEXCLASS.NONE);var e=(this.Get("texClass")||a.TEXCLASS.ORD);if(f===a.TEXCLASS.NONE||e===a.TEXCLASS.NONE){return""}if(f===a.TEXCLASS.VCENTER){f=a.TEXCLASS.ORD}if(e===a.TEXCLASS.VCENTER){e=a.TEXCLASS.ORD}var g=this.TEXSPACE[f][e];if((this.prevLevel>0||this.Get("scriptlevel")>0)&&g>=0){return""}return this.TEXSPACELENGTH[Math.abs(g)]},TEXSPACELENGTH:["",a.LENGTH.THINMATHSPACE,a.LENGTH.MEDIUMMATHSPACE,a.LENGTH.THICKMATHSPACE],TEXSPACE:[[0,-1,2,3,0,0,0,1],[-1,-1,0,3,0,0,0,1],[2,2,0,0,2,0,0,2],[3,3,0,0,3,0,0,3],[0,0,0,0,0,0,0,0],[0,-1,2,3,0,0,0,1],[1,1,0,1,1,1,1,1],[1,-1,2,3,1,0,1,1]],autoDefault:function(e){return""},isSpacelike:function(){return false},isEmbellished:function(){return false},Core:function(){return this},CoreMO:function(){return this},childIndex:function(g){if(g==null){return}for(var f=0,e=this.data.length;f<e;f++){if(g===this.data[f]){return f}}},CoreIndex:function(){return(this.inferRow?this.data[0]||this:this).childIndex(this.Core())},hasNewline:function(){if(this.isEmbellished()){return this.CoreMO().hasNewline()}if(this.isToken||this.linebreakContainer){return false}for(var f=0,e=this.data.length;f<e;f++){if(this.data[f]&&this.data[f].hasNewline()){return true}}return false},array:function(){if(this.inferred){return this.data}else{return[this]}},toString:function(){return this.type+"("+this.data.join(",")+")"},getAnnotation:function(){return null}},{childrenSpacelike:function(){for(var f=0,e=this.data.length;f<e;f++){if(!this.data[f].isSpacelike()){return false}}return true},childEmbellished:function(){return(this.data[0]&&this.data[0].isEmbellished())},childCore:function(){return(this.inferRow&&this.data[0]?this.data[0].Core():this.data[0])},childCoreMO:function(){return(this.data[0]?this.data[0].CoreMO():null)},setChildTeXclass:function(e){if(this.data[0]){e=this.data[0].setTeXclass(e);this.updateTeXclass(this.data[0])}return e},setBaseTeXclasses:function(g){this.getPrevClass(g);this.texClass=null;if(this.data[0]){if(this.isEmbellished()||this.data[0].isa(a.mi)){g=this.data[0].setTeXclass(g);this.updateTeXclass(this.Core())}else{this.data[0].setTeXclass();g=this}}else{g=this}for(var f=1,e=this.data.length;f<e;f++){if(this.data[f]){this.data[f].setTeXclass()}}return g},setSeparateTeXclasses:function(g){this.getPrevClass(g);for(var f=0,e=this.data.length;f<e;f++){if(this.data[f]){this.data[f].setTeXclass()}}if(this.isEmbellished()){this.updateTeXclass(this.Core())}return this}});a.mi=a.mbase.Subclass({type:"mi",isToken:true,texClass:a.TEXCLASS.ORD,defaults:{mathvariant:a.AUTO,mathsize:a.INHERIT,mathbackground:a.INHERIT,mathcolor:a.INHERIT,dir:a.INHERIT},autoDefault:function(f){if(f==="mathvariant"){var e=(this.data[0]||"").toString();return(e.length===1||(e.length===2&&e.charCodeAt(0)>=55296&&e.charCodeAt(0)<56320)?a.VARIANT.ITALIC:a.VARIANT.NORMAL)}return""},setTeXclass:function(f){this.getPrevClass(f);var e=this.data.join("");if(e.length>1&&e.match(/^[a-z][a-z0-9]*$/i)&&this.texClass===a.TEXCLASS.ORD){this.texClass=a.TEXCLASS.OP;this.autoOP=true}return this}});a.mn=a.mbase.Subclass({type:"mn",isToken:true,texClass:a.TEXCLASS.ORD,defaults:{mathvariant:a.INHERIT,mathsize:a.INHERIT,mathbackground:a.INHERIT,mathcolor:a.INHERIT,dir:a.INHERIT}});a.mo=a.mbase.Subclass({type:"mo",isToken:true,defaults:{mathvariant:a.INHERIT,mathsize:a.INHERIT,mathbackground:a.INHERIT,mathcolor:a.INHERIT,dir:a.INHERIT,form:a.AUTO,fence:a.AUTO,separator:a.AUTO,lspace:a.AUTO,rspace:a.AUTO,stretchy:a.AUTO,symmetric:a.AUTO,maxsize:a.AUTO,minsize:a.AUTO,largeop:a.AUTO,movablelimits:a.AUTO,accent:a.AUTO,linebreak:a.LINEBREAK.AUTO,lineleading:a.INHERIT,linebreakstyle:a.AUTO,linebreakmultchar:a.INHERIT,indentalign:a.INHERIT,indentshift:a.INHERIT,indenttarget:a.INHERIT,indentalignfirst:a.INHERIT,indentshiftfirst:a.INHERIT,indentalignlast:a.INHERIT,indentshiftlast:a.INHERIT,texClass:a.AUTO},defaultDef:{form:a.FORM.INFIX,fence:false,separator:false,lspace:a.LENGTH.THICKMATHSPACE,rspace:a.LENGTH.THICKMATHSPACE,stretchy:false,symmetric:false,maxsize:a.SIZE.INFINITY,minsize:"0em",largeop:false,movablelimits:false,accent:false,linebreak:a.LINEBREAK.AUTO,lineleading:"1ex",linebreakstyle:"before",indentalign:a.INDENTALIGN.AUTO,indentshift:"0",indenttarget:"",indentalignfirst:a.INDENTALIGN.INDENTALIGN,indentshiftfirst:a.INDENTSHIFT.INDENTSHIFT,indentalignlast:a.INDENTALIGN.INDENTALIGN,indentshiftlast:a.INDENTSHIFT.INDENTSHIFT,texClass:a.TEXCLASS.REL},SPACE_ATTR:{lspace:1,rspace:2},useMMLspacing:3,hasMMLspacing:function(){if(this.useMMLspacing){return true}return this.form&&(this.OPTABLE[this.form]||{})[this.data.join("")]},autoDefault:function(g,n){var l=this.def;if(!l){if(g==="form"){return this.getForm()}var k=this.data.join("");var f=[this.Get("form"),a.FORM.INFIX,a.FORM.POSTFIX,a.FORM.PREFIX];for(var h=0,e=f.length;h<e;h++){var j=this.OPTABLE[f[h]][k];if(j){l=this.makeDef(j);break}}if(!l){l=this.CheckRange(k)}if(!l&&n){l={}}else{if(!l){l=MathJax.Hub.Insert({},this.defaultDef)}if(this.parent){this.def=l}else{l=MathJax.Hub.Insert({},l)}l.form=f[0]}}this.useMMLspacing&=~(this.SPACE_ATTR[g]||0);if(l[g]!=null){return l[g]}else{if(!n){return this.defaultDef[g]}}return""},CheckRange:function(j){var k=j.charCodeAt(0);if(k>=55296&&k<56320){k=(((k-55296)<<10)+(j.charCodeAt(1)-56320))+65536}for(var g=0,e=this.RANGES.length;g<e&&this.RANGES[g][0]<=k;g++){if(k<=this.RANGES[g][1]){if(this.RANGES[g][3]){var f=a.optableDir+"/"+this.RANGES[g][3]+".js";this.RANGES[g][3]=null;MathJax.Hub.RestartAfter(MathJax.Ajax.Require(f))}var h=a.TEXCLASSNAMES[this.RANGES[g][2]];h=this.OPTABLE.infix[j]=a.mo.OPTYPES[h==="BIN"?"BIN3":h];return this.makeDef(h)}}return null},makeDef:function(f){if(f[2]==null){f[2]=this.defaultDef.texClass}if(!f[3]){f[3]={}}var e=MathJax.Hub.Insert({},f[3]);e.lspace=this.SPACE[f[0]];e.rspace=this.SPACE[f[1]];e.texClass=f[2];if(e.texClass===a.TEXCLASS.REL&&(this.movablelimits||this.data.join("").match(/^[a-z]+$/i))){e.texClass=a.TEXCLASS.OP}return e},getForm:function(){var e=this,g=this.parent,f=this.Parent();while(f&&f.isEmbellished()){e=g;g=f.parent;f=f.Parent()}if(g&&g.type==="mrow"&&g.NonSpaceLength()!==1){if(g.FirstNonSpace()===e){return a.FORM.PREFIX}if(g.LastNonSpace()===e){return a.FORM.POSTFIX}}return a.FORM.INFIX},isEmbellished:function(){return true},hasNewline:function(){return(this.Get("linebreak")===a.LINEBREAK.NEWLINE)},CoreParent:function(){var e=this;while(e&&e.isEmbellished()&&e.CoreMO()===this&&!e.isa(a.math)){e=e.Parent()}return e},CoreText:function(e){if(!e){return""}if(e.isEmbellished()){return e.CoreMO().data.join("")}while((((e.isa(a.mrow)||e.isa(a.TeXAtom)||e.isa(a.mstyle)||e.isa(a.mphantom))&&e.data.length===1)||e.isa(a.munderover))&&e.data[0]){e=e.data[0]}if(!e.isToken){return""}else{return e.data.join("")}},remapChars:{"*":"\u2217",'"':"\u2033","\u00B0":"\u2218","\u00B2":"2","\u00B3":"3","\u00B4":"\u2032","\u00B9":"1"},remap:function(f,e){f=f.replace(/-/g,"\u2212");if(e){f=f.replace(/'/g,"\u2032").replace(/`/g,"\u2035");if(f.length===1){f=e[f]||f}}return f},setTeXclass:function(f){var e=this.getValues("form","lspace","rspace","fence");if(this.hasMMLspacing()){this.texClass=a.TEXCLASS.NONE;return this}if(e.fence&&!this.texClass){if(e.form===a.FORM.PREFIX){this.texClass=a.TEXCLASS.OPEN}if(e.form===a.FORM.POSTFIX){this.texClass=a.TEXCLASS.CLOSE}}this.texClass=this.Get("texClass");if(this.data.join("")==="\u2061"){if(f){f.texClass=a.TEXCLASS.OP;f.fnOP=true}this.texClass=this.prevClass=a.TEXCLASS.NONE;return f}return this.adjustTeXclass(f)},adjustTeXclass:function(f){if(this.texClass===a.TEXCLASS.NONE){return f}if(f){if(f.autoOP&&(this.texClass===a.TEXCLASS.BIN||this.texClass===a.TEXCLASS.REL)){f.texClass=a.TEXCLASS.ORD}this.prevClass=f.texClass||a.TEXCLASS.ORD;this.prevLevel=f.Get("scriptlevel")}else{this.prevClass=a.TEXCLASS.NONE}if(this.texClass===a.TEXCLASS.BIN&&(this.prevClass===a.TEXCLASS.NONE||this.prevClass===a.TEXCLASS.BIN||this.prevClass===a.TEXCLASS.OP||this.prevClass===a.TEXCLASS.REL||this.prevClass===a.TEXCLASS.OPEN||this.prevClass===a.TEXCLASS.PUNCT)){this.texClass=a.TEXCLASS.ORD}else{if(this.prevClass===a.TEXCLASS.BIN&&(this.texClass===a.TEXCLASS.REL||this.texClass===a.TEXCLASS.CLOSE||this.texClass===a.TEXCLASS.PUNCT)){f.texClass=this.prevClass=a.TEXCLASS.ORD}else{if(this.texClass===a.TEXCLASS.BIN){var g=this,e=this.parent;while(e&&e.parent&&e.isEmbellished()&&(e.data.length===1||(e.type!=="mrow"&&e.Core()===g))){g=e;e=e.parent}if(e.data[e.data.length-1]===g){this.texClass=a.TEXCLASS.ORD}}}}return this}});a.mtext=a.mbase.Subclass({type:"mtext",isToken:true,isSpacelike:function(){return true},texClass:a.TEXCLASS.ORD,defaults:{mathvariant:a.INHERIT,mathsize:a.INHERIT,mathbackground:a.INHERIT,mathcolor:a.INHERIT,dir:a.INHERIT}});a.mspace=a.mbase.Subclass({type:"mspace",isToken:true,isSpacelike:function(){return true},defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,width:"0em",height:"0ex",depth:"0ex",linebreak:a.LINEBREAK.AUTO},hasDimAttr:function(){return(this.hasValue("width")||this.hasValue("height")||this.hasValue("depth"))},hasNewline:function(){return(!this.hasDimAttr()&&this.Get("linebreak")===a.LINEBREAK.NEWLINE)}});a.ms=a.mbase.Subclass({type:"ms",isToken:true,texClass:a.TEXCLASS.ORD,defaults:{mathvariant:a.INHERIT,mathsize:a.INHERIT,mathbackground:a.INHERIT,mathcolor:a.INHERIT,dir:a.INHERIT,lquote:'"',rquote:'"'}});a.mglyph=a.mbase.Subclass({type:"mglyph",isToken:true,texClass:a.TEXCLASS.ORD,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,alt:"",src:"",width:a.AUTO,height:a.AUTO,valign:"0em"}});a.mrow=a.mbase.Subclass({type:"mrow",isSpacelike:a.mbase.childrenSpacelike,inferred:false,notParent:false,isEmbellished:function(){var f=false;for(var g=0,e=this.data.length;g<e;g++){if(this.data[g]==null){continue}if(this.data[g].isEmbellished()){if(f){return false}f=true;this.core=g}else{if(!this.data[g].isSpacelike()){return false}}}return f},NonSpaceLength:function(){var g=0;for(var f=0,e=this.data.length;f<e;f++){if(this.data[f]&&!this.data[f].isSpacelike()){g++}}return g},FirstNonSpace:function(){for(var f=0,e=this.data.length;f<e;f++){if(this.data[f]&&!this.data[f].isSpacelike()){return this.data[f]}}return null},LastNonSpace:function(){for(var e=this.data.length-1;e>=0;e--){if(this.data[0]&&!this.data[e].isSpacelike()){return this.data[e]}}return null},Core:function(){if(!(this.isEmbellished())||typeof(this.core)==="undefined"){return this}return this.data[this.core]},CoreMO:function(){if(!(this.isEmbellished())||typeof(this.core)==="undefined"){return this}return this.data[this.core].CoreMO()},toString:function(){if(this.inferred){return"["+this.data.join(",")+"]"}return this.SUPER(arguments).toString.call(this)},setTeXclass:function(g){var f,e=this.data.length;if((this.open||this.close)&&(!g||!g.fnOP)){this.getPrevClass(g);g=null;for(f=0;f<e;f++){if(this.data[f]){g=this.data[f].setTeXclass(g)}}if(!this.hasOwnProperty("texClass")){this.texClass=a.TEXCLASS.INNER}return this}else{for(f=0;f<e;f++){if(this.data[f]){g=this.data[f].setTeXclass(g)}}if(this.data[0]){this.updateTeXclass(this.data[0])}return g}},getAnnotation:function(e){if(this.data.length!=1){return null}return this.data[0].getAnnotation(e)}});a.mfrac=a.mbase.Subclass({type:"mfrac",num:0,den:1,linebreakContainer:true,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,linethickness:a.LINETHICKNESS.MEDIUM,numalign:a.ALIGN.CENTER,denomalign:a.ALIGN.CENTER,bevelled:false},adjustChild_displaystyle:function(e){return false},adjustChild_scriptlevel:function(f){var e=this.Get("scriptlevel");if(!this.Get("displaystyle")||e>0){e++}return e},adjustChild_texprimestyle:function(e){if(e==this.den){return true}return this.Get("texprimestyle")},setTeXclass:a.mbase.setSeparateTeXclasses});a.msqrt=a.mbase.Subclass({type:"msqrt",inferRow:true,linebreakContainer:true,texClass:a.TEXCLASS.ORD,setTeXclass:a.mbase.setSeparateTeXclasses,adjustChild_texprimestyle:function(e){return true}});a.mroot=a.mbase.Subclass({type:"mroot",linebreakContainer:true,texClass:a.TEXCLASS.ORD,adjustChild_displaystyle:function(e){if(e===1){return false}return this.Get("displaystyle")},adjustChild_scriptlevel:function(f){var e=this.Get("scriptlevel");if(f===1){e+=2}return e},adjustChild_texprimestyle:function(e){if(e===0){return true}return this.Get("texprimestyle")},setTeXclass:a.mbase.setSeparateTeXclasses});a.mstyle=a.mbase.Subclass({type:"mstyle",isSpacelike:a.mbase.childrenSpacelike,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,inferRow:true,defaults:{scriptlevel:a.INHERIT,displaystyle:a.INHERIT,scriptsizemultiplier:Math.sqrt(1/2),scriptminsize:"8pt",mathbackground:a.INHERIT,mathcolor:a.INHERIT,dir:a.INHERIT,infixlinebreakstyle:a.LINEBREAKSTYLE.BEFORE,decimalseparator:"."},adjustChild_scriptlevel:function(g){var f=this.scriptlevel;if(f==null){f=this.Get("scriptlevel")}else{if(String(f).match(/^ *[-+]/)){var e=this.Get("scriptlevel",null,true);f=e+parseInt(f)}}return f},inheritFromMe:true,noInherit:{mpadded:{width:true,height:true,depth:true,lspace:true,voffset:true},mtable:{width:true,height:true,depth:true,align:true}},getRemoved:{fontfamily:"fontFamily",fontweight:"fontWeight",fontstyle:"fontStyle",fontsize:"fontSize"},setTeXclass:a.mbase.setChildTeXclass});a.merror=a.mbase.Subclass({type:"merror",inferRow:true,linebreakContainer:true,texClass:a.TEXCLASS.ORD});a.mpadded=a.mbase.Subclass({type:"mpadded",inferRow:true,isSpacelike:a.mbase.childrenSpacelike,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,width:"",height:"",depth:"",lspace:0,voffset:0},setTeXclass:a.mbase.setChildTeXclass});a.mphantom=a.mbase.Subclass({type:"mphantom",texClass:a.TEXCLASS.ORD,inferRow:true,isSpacelike:a.mbase.childrenSpacelike,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,setTeXclass:a.mbase.setChildTeXclass});a.mfenced=a.mbase.Subclass({type:"mfenced",defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,open:"(",close:")",separators:","},addFakeNodes:function(){var f=this.getValues("open","close","separators");f.open=f.open.replace(/[ \t\n\r]/g,"");f.close=f.close.replace(/[ \t\n\r]/g,"");f.separators=f.separators.replace(/[ \t\n\r]/g,"");if(f.open!==""){this.SetData("open",a.mo(f.open).With({fence:true,form:a.FORM.PREFIX,texClass:a.TEXCLASS.OPEN}))}if(f.separators!==""){while(f.separators.length<this.data.length){f.separators+=f.separators.charAt(f.separators.length-1)}for(var g=1,e=this.data.length;g<e;g++){if(this.data[g]){this.SetData("sep"+g,a.mo(f.separators.charAt(g-1)).With({separator:true}))}}}if(f.close!==""){this.SetData("close",a.mo(f.close).With({fence:true,form:a.FORM.POSTFIX,texClass:a.TEXCLASS.CLOSE}))}},texClass:a.TEXCLASS.OPEN,setTeXclass:function(g){this.addFakeNodes();this.getPrevClass(g);if(this.data.open){g=this.data.open.setTeXclass(g)}if(this.data[0]){g=this.data[0].setTeXclass(g)}for(var f=1,e=this.data.length;f<e;f++){if(this.data["sep"+f]){g=this.data["sep"+f].setTeXclass(g)}if(this.data[f]){g=this.data[f].setTeXclass(g)}}if(this.data.close){g=this.data.close.setTeXclass(g)}this.updateTeXclass(this.data.open);this.texClass=a.TEXCLASS.INNER;return g}});a.menclose=a.mbase.Subclass({type:"menclose",inferRow:true,linebreakContainer:true,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,notation:a.NOTATION.LONGDIV,texClass:a.TEXCLASS.ORD},setTeXclass:a.mbase.setSeparateTeXclasses});a.msubsup=a.mbase.Subclass({type:"msubsup",base:0,sub:1,sup:2,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,subscriptshift:"",superscriptshift:"",texClass:a.AUTO},autoDefault:function(e){if(e==="texClass"){return(this.isEmbellished()?this.CoreMO().Get(e):a.TEXCLASS.ORD)}return 0},adjustChild_displaystyle:function(e){if(e>0){return false}return this.Get("displaystyle")},adjustChild_scriptlevel:function(f){var e=this.Get("scriptlevel");if(f>0){e++}return e},adjustChild_texprimestyle:function(e){if(e===this.sub){return true}return this.Get("texprimestyle")},setTeXclass:a.mbase.setBaseTeXclasses});a.msub=a.msubsup.Subclass({type:"msub"});a.msup=a.msubsup.Subclass({type:"msup",sub:2,sup:1});a.mmultiscripts=a.msubsup.Subclass({type:"mmultiscripts",adjustChild_texprimestyle:function(e){if(e%2===1){return true}return this.Get("texprimestyle")}});a.mprescripts=a.mbase.Subclass({type:"mprescripts"});a.none=a.mbase.Subclass({type:"none"});a.munderover=a.mbase.Subclass({type:"munderover",base:0,under:1,over:2,sub:1,sup:2,ACCENTS:["","accentunder","accent"],linebreakContainer:true,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,accent:a.AUTO,accentunder:a.AUTO,align:a.ALIGN.CENTER,texClass:a.AUTO,subscriptshift:"",superscriptshift:""},autoDefault:function(e){if(e==="texClass"){return(this.isEmbellished()?this.CoreMO().Get(e):a.TEXCLASS.ORD)}if(e==="accent"&&this.data[this.over]){return this.data[this.over].CoreMO().Get("accent")}if(e==="accentunder"&&this.data[this.under]){return this.data[this.under].CoreMO().Get("accent")}return false},adjustChild_displaystyle:function(e){if(e>0){return false}return this.Get("displaystyle")},adjustChild_scriptlevel:function(g){var f=this.Get("scriptlevel");var e=(this.data[this.base]&&!this.Get("displaystyle")&&this.data[this.base].CoreMO().Get("movablelimits"));if(g==this.under&&(e||!this.Get("accentunder"))){f++}if(g==this.over&&(e||!this.Get("accent"))){f++}return f},adjustChild_texprimestyle:function(e){if(e===this.base&&this.data[this.over]){return true}return this.Get("texprimestyle")},setTeXclass:a.mbase.setBaseTeXclasses});a.munder=a.munderover.Subclass({type:"munder"});a.mover=a.munderover.Subclass({type:"mover",over:1,under:2,sup:1,sub:2,ACCENTS:["","accent","accentunder"]});a.mtable=a.mbase.Subclass({type:"mtable",defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,align:a.ALIGN.AXIS,rowalign:a.ALIGN.BASELINE,columnalign:a.ALIGN.CENTER,groupalign:"{left}",alignmentscope:true,columnwidth:a.WIDTH.AUTO,width:a.WIDTH.AUTO,rowspacing:"1ex",columnspacing:".8em",rowlines:a.LINES.NONE,columnlines:a.LINES.NONE,frame:a.LINES.NONE,framespacing:"0.4em 0.5ex",equalrows:false,equalcolumns:false,displaystyle:false,side:a.SIDE.RIGHT,minlabelspacing:"0.8em",texClass:a.TEXCLASS.ORD,useHeight:1},adjustChild_displaystyle:function(){return(this.displaystyle!=null?this.displaystyle:this.defaults.displaystyle)},inheritFromMe:true,noInherit:{mover:{align:true},munder:{align:true},munderover:{align:true},mtable:{align:true,rowalign:true,columnalign:true,groupalign:true,alignmentscope:true,columnwidth:true,width:true,rowspacing:true,columnspacing:true,rowlines:true,columnlines:true,frame:true,framespacing:true,equalrows:true,equalcolumns:true,displaystyle:true,side:true,minlabelspacing:true,texClass:true,useHeight:1}},linebreakContainer:true,Append:function(){for(var f=0,e=arguments.length;f<e;f++){if(!((arguments[f] instanceof a.mtr)||(arguments[f] instanceof a.mlabeledtr))){arguments[f]=a.mtr(arguments[f])}}this.SUPER(arguments).Append.apply(this,arguments)},setTeXclass:a.mbase.setSeparateTeXclasses});a.mtr=a.mbase.Subclass({type:"mtr",defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,rowalign:a.INHERIT,columnalign:a.INHERIT,groupalign:a.INHERIT},inheritFromMe:true,noInherit:{mrow:{rowalign:true,columnalign:true,groupalign:true},mtable:{rowalign:true,columnalign:true,groupalign:true}},linebreakContainer:true,Append:function(){for(var f=0,e=arguments.length;f<e;f++){if(!(arguments[f] instanceof a.mtd)){arguments[f]=a.mtd(arguments[f])}}this.SUPER(arguments).Append.apply(this,arguments)},setTeXclass:a.mbase.setSeparateTeXclasses});a.mtd=a.mbase.Subclass({type:"mtd",inferRow:true,linebreakContainer:true,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,rowspan:1,columnspan:1,rowalign:a.INHERIT,columnalign:a.INHERIT,groupalign:a.INHERIT},setTeXclass:a.mbase.setSeparateTeXclasses});a.maligngroup=a.mbase.Subclass({type:"maligngroup",isSpacelike:function(){return true},defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,groupalign:a.INHERIT},inheritFromMe:true,noInherit:{mrow:{groupalign:true},mtable:{groupalign:true}}});a.malignmark=a.mbase.Subclass({type:"malignmark",defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,edge:a.SIDE.LEFT},isSpacelike:function(){return true}});a.mlabeledtr=a.mtr.Subclass({type:"mlabeledtr"});a.maction=a.mbase.Subclass({type:"maction",defaults:{mathbackground:a.INHERIT,mathcolor:a.INHERIT,actiontype:a.ACTIONTYPE.TOGGLE,selection:1},selected:function(){return this.data[this.Get("selection")-1]||a.NULL},isEmbellished:function(){return this.selected().isEmbellished()},isSpacelike:function(){return this.selected().isSpacelike()},Core:function(){return this.selected().Core()},CoreMO:function(){return this.selected().CoreMO()},setTeXclass:function(f){if(this.Get("actiontype")===a.ACTIONTYPE.TOOLTIP&&this.data[1]){this.data[1].setTeXclass()}var e=this.selected();f=e.setTeXclass(f);this.updateTeXclass(e);return f}});a.semantics=a.mbase.Subclass({type:"semantics",notParent:true,isEmbellished:a.mbase.childEmbellished,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,defaults:{definitionURL:null,encoding:null},setTeXclass:a.mbase.setChildTeXclass,getAnnotation:function(g){var l=MathJax.Hub.config.MathMenu.semanticsAnnotations[g];if(l){for(var h=0,e=this.data.length;h<e;h++){var k=this.data[h].Get("encoding");if(k){for(var f=0,o=l.length;f<o;f++){if(l[f]===k){return this.data[h]}}}}}return null}});a.annotation=a.mbase.Subclass({type:"annotation",isChars:true,linebreakContainer:true,defaults:{definitionURL:null,encoding:null,cd:"mathmlkeys",name:"",src:null}});a["annotation-xml"]=a.mbase.Subclass({type:"annotation-xml",linebreakContainer:true,defaults:{definitionURL:null,encoding:null,cd:"mathmlkeys",name:"",src:null}});a.math=a.mstyle.Subclass({type:"math",defaults:{mathvariant:a.VARIANT.NORMAL,mathsize:a.SIZE.NORMAL,mathcolor:"",mathbackground:a.COLOR.TRANSPARENT,dir:"ltr",scriptlevel:0,displaystyle:a.AUTO,display:"inline",maxwidth:"",overflow:a.OVERFLOW.LINEBREAK,altimg:"","altimg-width":"","altimg-height":"","altimg-valign":"",alttext:"",cdgroup:"",scriptsizemultiplier:Math.sqrt(1/2),scriptminsize:"8px",infixlinebreakstyle:a.LINEBREAKSTYLE.BEFORE,lineleading:"1ex",indentshift:"auto",indentalign:a.INDENTALIGN.AUTO,indentalignfirst:a.INDENTALIGN.INDENTALIGN,indentshiftfirst:a.INDENTSHIFT.INDENTSHIFT,indentalignlast:a.INDENTALIGN.INDENTALIGN,indentshiftlast:a.INDENTSHIFT.INDENTSHIFT,decimalseparator:".",texprimestyle:false},autoDefault:function(e){if(e==="displaystyle"){return this.Get("display")==="block"}return""},linebreakContainer:true,setTeXclass:a.mbase.setChildTeXclass,getAnnotation:function(e){if(this.data.length!=1){return null}return this.data[0].getAnnotation(e)}});a.chars=a.mbase.Subclass({type:"chars",Append:function(){this.data.push.apply(this.data,arguments)},value:function(){return this.data.join("")},toString:function(){return this.data.join("")}});a.entity=a.mbase.Subclass({type:"entity",Append:function(){this.data.push.apply(this.data,arguments)},value:function(){if(this.data[0].substr(0,2)==="#x"){return parseInt(this.data[0].substr(2),16)}else{if(this.data[0].substr(0,1)==="#"){return parseInt(this.data[0].substr(1))}else{return 0}}},toString:function(){var e=this.value();if(e<=65535){return String.fromCharCode(e)}e-=65536;return String.fromCharCode((e>>10)+55296)+String.fromCharCode((e&1023)+56320)}});a.xml=a.mbase.Subclass({type:"xml",Init:function(){this.div=document.createElement("div");return this.SUPER(arguments).Init.apply(this,arguments)},Append:function(){for(var f=0,e=arguments.length;f<e;f++){var g=this.Import(arguments[f]);this.data.push(g);this.div.appendChild(g)}},Import:function(j){if(document.importNode){return document.importNode(j,true)}var f,g,e;if(j.nodeType===1){f=document.createElement(j.nodeName);for(g=0,e=j.attributes.length;g<e;g++){var h=j.attributes[g];if(h.specified&&h.nodeValue!=null&&h.nodeValue!=""){f.setAttribute(h.nodeName,h.nodeValue)}if(h.nodeName==="style"){f.style.cssText=h.nodeValue}}if(j.className){f.className=j.className}}else{if(j.nodeType===3||j.nodeType===4){f=document.createTextNode(j.nodeValue)}else{if(j.nodeType===8){f=document.createComment(j.nodeValue)}else{return document.createTextNode("")}}}for(g=0,e=j.childNodes.length;g<e;g++){f.appendChild(this.Import(j.childNodes[g]))}return f},value:function(){return this.div},toString:function(){return this.div.innerHTML}});a.TeXAtom=a.mbase.Subclass({type:"texatom",linebreakContainer:true,inferRow:true,notParent:true,texClass:a.TEXCLASS.ORD,Core:a.mbase.childCore,CoreMO:a.mbase.childCoreMO,isEmbellished:a.mbase.childEmbellished,setTeXclass:function(e){this.data[0].setTeXclass();return this.adjustTeXclass(e)},adjustTeXclass:a.mo.prototype.adjustTeXclass});a.NULL=a.mbase().With({type:"null"});var b=a.TEXCLASS;var d={ORD:[0,0,b.ORD],ORD11:[1,1,b.ORD],ORD21:[2,1,b.ORD],ORD02:[0,2,b.ORD],ORD55:[5,5,b.ORD],OP:[1,2,b.OP,{largeop:true,movablelimits:true,symmetric:true}],OPFIXED:[1,2,b.OP,{largeop:true,movablelimits:true}],INTEGRAL:[0,1,b.OP,{largeop:true,symmetric:true}],INTEGRAL2:[1,2,b.OP,{largeop:true,symmetric:true}],BIN3:[3,3,b.BIN],BIN4:[4,4,b.BIN],BIN01:[0,1,b.BIN],BIN5:[5,5,b.BIN],TALLBIN:[4,4,b.BIN,{stretchy:true}],BINOP:[4,4,b.BIN,{largeop:true,movablelimits:true}],REL:[5,5,b.REL],REL1:[1,1,b.REL,{stretchy:true}],REL4:[4,4,b.REL],RELSTRETCH:[5,5,b.REL,{stretchy:true}],RELACCENT:[5,5,b.REL,{accent:true}],WIDEREL:[5,5,b.REL,{accent:true,stretchy:true}],OPEN:[0,0,b.OPEN,{fence:true,stretchy:true,symmetric:true}],CLOSE:[0,0,b.CLOSE,{fence:true,stretchy:true,symmetric:true}],INNER:[0,0,b.INNER],PUNCT:[0,3,b.PUNCT],ACCENT:[0,0,b.ORD,{accent:true}],WIDEACCENT:[0,0,b.ORD,{accent:true,stretchy:true}]};a.mo.Augment({SPACE:["0em","0.1111em","0.1667em","0.2222em","0.2667em","0.3333em"],RANGES:[[32,127,b.REL,"BasicLatin"],[160,255,b.ORD,"Latin1Supplement"],[256,383,b.ORD],[384,591,b.ORD],[688,767,b.ORD,"SpacingModLetters"],[768,879,b.ORD,"CombDiacritMarks"],[880,1023,b.ORD,"GreekAndCoptic"],[7680,7935,b.ORD],[8192,8303,b.PUNCT,"GeneralPunctuation"],[8304,8351,b.ORD],[8352,8399,b.ORD],[8400,8447,b.ORD,"CombDiactForSymbols"],[8448,8527,b.ORD,"LetterlikeSymbols"],[8528,8591,b.ORD],[8592,8703,b.REL,"Arrows"],[8704,8959,b.BIN,"MathOperators"],[8960,9215,b.ORD,"MiscTechnical"],[9312,9471,b.ORD],[9472,9631,b.ORD],[9632,9727,b.ORD,"GeometricShapes"],[9984,10175,b.ORD,"Dingbats"],[10176,10223,b.ORD,"MiscMathSymbolsA"],[10224,10239,b.REL,"SupplementalArrowsA"],[10496,10623,b.REL,"SupplementalArrowsB"],[10624,10751,b.ORD,"MiscMathSymbolsB"],[10752,11007,b.BIN,"SuppMathOperators"],[11008,11263,b.ORD,"MiscSymbolsAndArrows"],[119808,120831,b.ORD]],OPTABLE:{prefix:{"\u2200":d.ORD21,"\u2202":d.ORD21,"\u2203":d.ORD21,"\u2207":d.ORD21,"\u220F":d.OP,"\u2210":d.OP,"\u2211":d.OP,"\u2212":d.BIN01,"\u2213":d.BIN01,"\u221A":[1,1,b.ORD,{stretchy:true}],"\u2220":d.ORD,"\u222B":d.INTEGRAL,"\u222E":d.INTEGRAL,"\u22C0":d.OP,"\u22C1":d.OP,"\u22C2":d.OP,"\u22C3":d.OP,"\u2308":d.OPEN,"\u230A":d.OPEN,"\u27E8":d.OPEN,"\u27EE":d.OPEN,"\u2A00":d.OP,"\u2A01":d.OP,"\u2A02":d.OP,"\u2A04":d.OP,"\u2A06":d.OP,"\u00AC":d.ORD21,"\u00B1":d.BIN01,"(":d.OPEN,"+":d.BIN01,"-":d.BIN01,"[":d.OPEN,"{":d.OPEN,"|":d.OPEN},postfix:{"!":[1,0,b.CLOSE],"&":d.ORD,"\u2032":d.ORD02,"\u203E":d.WIDEACCENT,"\u2309":d.CLOSE,"\u230B":d.CLOSE,"\u23DE":d.WIDEACCENT,"\u23DF":d.WIDEACCENT,"\u266D":d.ORD02,"\u266E":d.ORD02,"\u266F":d.ORD02,"\u27E9":d.CLOSE,"\u27EF":d.CLOSE,"\u02C6":d.WIDEACCENT,"\u02C7":d.WIDEACCENT,"\u02C9":d.WIDEACCENT,"\u02CA":d.ACCENT,"\u02CB":d.ACCENT,"\u02D8":d.ACCENT,"\u02D9":d.ACCENT,"\u02DC":d.WIDEACCENT,"\u0302":d.WIDEACCENT,"\u00A8":d.ACCENT,"\u00AF":d.WIDEACCENT,")":d.CLOSE,"]":d.CLOSE,"^":d.WIDEACCENT,_:d.WIDEACCENT,"`":d.ACCENT,"|":d.CLOSE,"}":d.CLOSE,"~":d.WIDEACCENT},infix:{"":d.ORD,"%":[3,3,b.ORD],"\u2022":d.BIN4,"\u2026":d.INNER,"\u2044":d.TALLBIN,"\u2061":d.ORD,"\u2062":d.ORD,"\u2063":[0,0,b.ORD,{linebreakstyle:"after",separator:true}],"\u2064":d.ORD,"\u2190":d.WIDEREL,"\u2191":d.RELSTRETCH,"\u2192":d.WIDEREL,"\u2193":d.RELSTRETCH,"\u2194":d.WIDEREL,"\u2195":d.RELSTRETCH,"\u2196":d.RELSTRETCH,"\u2197":d.RELSTRETCH,"\u2198":d.RELSTRETCH,"\u2199":d.RELSTRETCH,"\u21A6":d.WIDEREL,"\u21A9":d.WIDEREL,"\u21AA":d.WIDEREL,"\u21BC":d.WIDEREL,"\u21BD":d.WIDEREL,"\u21C0":d.WIDEREL,"\u21C1":d.WIDEREL,"\u21CC":d.WIDEREL,"\u21D0":d.WIDEREL,"\u21D1":d.RELSTRETCH,"\u21D2":d.WIDEREL,"\u21D3":d.RELSTRETCH,"\u21D4":d.WIDEREL,"\u21D5":d.RELSTRETCH,"\u2208":d.REL,"\u2209":d.REL,"\u220B":d.REL,"\u2212":d.BIN4,"\u2213":d.BIN4,"\u2215":d.TALLBIN,"\u2216":d.BIN4,"\u2217":d.BIN4,"\u2218":d.BIN4,"\u2219":d.BIN4,"\u221D":d.REL,"\u2223":d.REL,"\u2225":d.REL,"\u2227":d.BIN4,"\u2228":d.BIN4,"\u2229":d.BIN4,"\u222A":d.BIN4,"\u223C":d.REL,"\u2240":d.BIN4,"\u2243":d.REL,"\u2245":d.REL,"\u2248":d.REL,"\u224D":d.REL,"\u2250":d.REL,"\u2260":d.REL,"\u2261":d.REL,"\u2264":d.REL,"\u2265":d.REL,"\u226A":d.REL,"\u226B":d.REL,"\u227A":d.REL,"\u227B":d.REL,"\u2282":d.REL,"\u2283":d.REL,"\u2286":d.REL,"\u2287":d.REL,"\u228E":d.BIN4,"\u2291":d.REL,"\u2292":d.REL,"\u2293":d.BIN4,"\u2294":d.BIN4,"\u2295":d.BIN4,"\u2296":d.BIN4,"\u2297":d.BIN4,"\u2298":d.BIN4,"\u2299":d.BIN4,"\u22A2":d.REL,"\u22A3":d.REL,"\u22A4":d.ORD55,"\u22A5":d.REL,"\u22A8":d.REL,"\u22C4":d.BIN4,"\u22C5":d.BIN4,"\u22C6":d.BIN4,"\u22C8":d.REL,"\u22EE":d.ORD55,"\u22EF":d.INNER,"\u22F1":[5,5,b.INNER],"\u25B3":d.BIN4,"\u25B5":d.BIN4,"\u25B9":d.BIN4,"\u25BD":d.BIN4,"\u25BF":d.BIN4,"\u25C3":d.BIN4,"\u2758":d.REL,"\u27F5":d.WIDEREL,"\u27F6":d.WIDEREL,"\u27F7":d.WIDEREL,"\u27F8":d.WIDEREL,"\u27F9":d.WIDEREL,"\u27FA":d.WIDEREL,"\u27FC":d.WIDEREL,"\u2A2F":d.BIN4,"\u2A3F":d.BIN4,"\u2AAF":d.REL,"\u2AB0":d.REL,"\u00B1":d.BIN4,"\u00B7":d.BIN4,"\u00D7":d.BIN4,"\u00F7":d.BIN4,"*":d.BIN3,"+":d.BIN4,",":[0,3,b.PUNCT,{linebreakstyle:"after",separator:true}],"-":d.BIN4,".":[3,3,b.ORD],"/":d.ORD11,":":[1,2,b.REL],";":[0,3,b.PUNCT,{linebreakstyle:"after",separator:true}],"<":d.REL,"=":d.REL,">":d.REL,"?":[1,1,b.CLOSE],"\\":d.ORD,"^":d.ORD11,_:d.ORD11,"|":[2,2,b.ORD,{fence:true,stretchy:true,symmetric:true}],"#":d.ORD,"$":d.ORD,"\u002E":[0,3,b.PUNCT,{separator:true}],"\u02B9":d.ORD,"\u0300":d.ACCENT,"\u0301":d.ACCENT,"\u0303":d.WIDEACCENT,"\u0304":d.ACCENT,"\u0306":d.ACCENT,"\u0307":d.ACCENT,"\u0308":d.ACCENT,"\u030C":d.ACCENT,"\u0332":d.WIDEACCENT,"\u0338":d.REL4,"\u2015":[0,0,b.ORD,{stretchy:true}],"\u2017":[0,0,b.ORD,{stretchy:true}],"\u2020":d.BIN3,"\u2021":d.BIN3,"\u20D7":d.ACCENT,"\u2111":d.ORD,"\u2113":d.ORD,"\u2118":d.ORD,"\u211C":d.ORD,"\u2205":d.ORD,"\u221E":d.ORD,"\u2305":d.BIN3,"\u2306":d.BIN3,"\u2322":d.REL4,"\u2323":d.REL4,"\u2329":d.OPEN,"\u232A":d.CLOSE,"\u23AA":d.ORD,"\u23AF":[0,0,b.ORD,{stretchy:true}],"\u23B0":d.OPEN,"\u23B1":d.CLOSE,"\u2500":d.ORD,"\u25EF":d.BIN3,"\u2660":d.ORD,"\u2661":d.ORD,"\u2662":d.ORD,"\u2663":d.ORD,"\u3008":d.OPEN,"\u3009":d.CLOSE,"\uFE37":d.WIDEACCENT,"\uFE38":d.WIDEACCENT}}},{OPTYPES:d});var c=a.mo.prototype.OPTABLE;c.infix["^"]=d.WIDEREL;c.infix._=d.WIDEREL;c.prefix["\u2223"]=d.OPEN;c.prefix["\u2225"]=d.OPEN;c.postfix["\u2223"]=d.CLOSE;c.postfix["\u2225"]=d.CLOSE})(MathJax.ElementJax.mml);MathJax.ElementJax.mml.loadComplete("jax.js");
