/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Size2={directory:"Size2/Regular",family:"MathJax_Size2",testString:"() [] {}",32:[0,0,250,0,0],40:[1150,649,597,180,561],41:[1150,649,597,35,416],47:[1150,649,811,56,754],91:[1150,649,472,224,455],92:[1150,649,811,54,754],93:[1150,649,472,16,247],123:[1150,649,667,119,547],125:[1150,649,667,119,547],160:[0,0,250,0,0],710:[772,-565,1000,-5,1004],732:[750,-611,1000,0,999],770:[772,-565,0,-1005,4],771:[750,-611,0,-1000,-1],8719:[950,450,1278,56,1221],8720:[950,450,1278,56,1221],8721:[950,450,1444,55,1388],8730:[1150,650,1000,111,1020],8747:[1360,862,556,55,944],8748:[1360,862,1084,55,1472],8749:[1360,862,1592,55,1980],8750:[1360,862,556,55,944],8896:[950,450,1111,55,1055],8897:[950,450,1111,55,1055],8898:[949,450,1111,55,1055],8899:[950,449,1111,55,1055],8968:[1150,649,528,224,511],8969:[1150,649,528,16,303],8970:[1150,649,528,224,511],8971:[1150,649,528,16,303],10216:[1150,649,611,112,524],10217:[1150,649,611,85,498],10752:[949,449,1511,56,1454],10753:[949,449,1511,56,1454],10754:[949,449,1511,56,1454],10756:[950,449,1111,55,1055],10758:[950,450,1111,55,1055]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Size2"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size2/Regular/Main.js"]);
