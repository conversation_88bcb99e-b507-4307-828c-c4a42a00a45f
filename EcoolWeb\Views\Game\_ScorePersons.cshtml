﻿@model GameScoreListsViewModel

@Html.HiddenFor(m => m.Search.WhereGAME_NAME)
@Html.HiddenFor(m => m.Search.WhereGAME_NO)
@Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.WhereTEMP_USER_ID)

@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.OrdercColumn)

<br />
<div class="form-inline" role="form" id="Q_Div">

    <div class="form-group">
        <label class="control-label">學校</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", @onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">學號</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.WhereUSER_NO, new { htmlAttributes = new { @class = "form-control", @style = "min-width:100px" } })
    </div>
    <div class="form-group">
        <label class="control-label">姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.WhereNAME, new { htmlAttributes = new { @class = "form-control", @style = "min-width:100px" } })
    </div>
    <div class="form-group">
        <label class="control-label">年級</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.WhereYEAR, new { htmlAttributes = new { @class = "form-control", @style = "min-width:100px" } })
    </div>
    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "FunPageProc(1)" })
    </div>
    <div style="height:10px"></div>
    <div class="form-group">
        <label class="control-label">成績類型</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.SCORE_TYPE,
                "_RadioButtonList",
                new
                {
                    TagName = (Html.NameFor(model => model.SCORE_TYPE)).ToHtmlString(),
                    RadioItems = ViewBag.SCORE_TYPEItem,
                    Position = Position.Horizontal,
                    Numbers = int.MaxValue,
                    onclick = "",
                })
    </div>
    <div style="height:10px"></div>

    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
<br />

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        成績一覽表
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('SHORT_NAME');">
                            @Html.DisplayNameFor(model => model.ListData.First().SHORT_NAME)
                            <img id="SHORT_NAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('NAME');">
                            @Html.DisplayNameFor(model => model.ListData.First().NAME)
                            <img id="NAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                            @Html.DisplayNameFor(model => model.ListData.First().USER_NO)
                            <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                            @Html.DisplayNameFor(model => model.ListData.First().CLASS_NO)
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                            @Html.DisplayNameFor(model => model.ListData.First().SEAT_NO)
                            <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>

                    @if (Model.Q_DATA?.Count > 0)
                    {
                        foreach (var Qitem in Model.Q_DATA)
                        {
                            <th>
                                <samp class="form-group">
                                    第 @Qitem.G_ORDER_BY 題
                                </samp>
                            </th>
                        }
                    }
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('SCORE');">
                            @Html.DisplayNameFor(model => model.ListData.First().SCORE)
                            <img id="SCORE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('REWARD_CASH');">
                            @Html.DisplayNameFor(model => model.ListData.First().REWARD_CASH)
                            <img id="REWARD_CASH" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                            @Html.DisplayNameFor(model => model.ListData.First().CRE_DATE)
                            <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </samp>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {

                        <tr>
                            <td align="center">
                                @if (!string.IsNullOrWhiteSpace(item.USER_NO))
                                {
                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                }
                                else
                                {
                                    <font color="#0000AD">臨時卡</font>
                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>

                            <td align="center">
                                @Html.DisplayFor(modelItem => item.USER_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>

                            @if (Model.Q_DATA?.Count > 0)
                            {
                                foreach (var Qitem in Model.Q_DATA)
                                {
                                    <td align="center">
                                        @{
                                            var TRUE_ANS = item.ScoreListData.Where(a => a.GROUP_ID == Qitem.GROUP_ID && a.ANSWER_ID == item.ANSWER_ID).Select(a => a.TRUE_ANS).FirstOrDefault();

                                            if (TRUE_ANS == null)
                                            {
                                                <samp class="form-group">
                                                    未作答
                                                </samp>
                                            }
                                            else
                                            {
                                                if ((bool)TRUE_ANS)
                                                {
                                                    <samp class="form-group">
                                                        O
                                                    </samp>
                                                }
                                                else
                                                {
                                                    <samp class="form-group">
                                                        X
                                                    </samp>
                                                }
                                            }
                                        }
                                    </td>
                                }
                            }
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SCORE)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.REWARD_CASH)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CRE_DATE)
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                                              .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                                              .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                                              .SetNextPageText(PageGlobal.DfSetNextPageText)
                                                             )
</div>