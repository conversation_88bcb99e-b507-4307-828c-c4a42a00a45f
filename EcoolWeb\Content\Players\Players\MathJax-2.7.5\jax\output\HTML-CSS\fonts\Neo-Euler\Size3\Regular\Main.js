/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Size3={directory:"Size3/Regular",family:"NeoEulerMathJax_Size3",testString:"\u00A0\u2016\u2044\u2215\u221A\u2223\u2225\u2308\u2309\u230A\u230B\u2329\u232A\u23DC\u23DD",32:[0,0,333,0,0],40:[2199,199,734,208,714],41:[2199,199,734,20,526],47:[2199,200,1044,54,992],91:[2274,125,527,250,513],92:[2199,200,1044,54,992],93:[2274,125,527,14,277],123:[2199,200,750,131,618],124:[2498,208,213,86,126],125:[2199,200,750,131,618],160:[0,0,333,0,0],8214:[2498,208,403,86,316],8260:[2199,200,1044,54,992],8725:[2199,200,1044,54,992],8730:[2402,1,1000,111,1025],8739:[2498,208,213,86,126],8741:[1897,208,403,86,316],8968:[2199,200,583,250,568],8969:[2199,200,583,14,332],8970:[2199,200,583,250,568],8971:[2199,200,583,14,332],9001:[2134,232,757,123,648],9002:[2134,232,818,100,625],9180:[800,-308,2511,56,2455],9181:[248,244,2511,56,2455],9182:[944,-457,2511,56,2455],9183:[97,390,2511,56,2455],10216:[1536,234,629,109,520],10217:[1536,234,693,89,500]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Size3"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size3/Regular/Main.js"]);
