﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public partial class SAQT01
    {
        public class STATUSVal
        {
            /// <summary>
            /// 8 暫存
            /// </summary>
            static public byte TempSave = 8;

            /// <summary>
            /// 0. 擬定中/未開始
            /// </summary>
            static public byte NotStarted = 0;

            /// <summary>
            /// 1.發佈中/ 開始
            /// </summary>
            static public byte StartedOut = 1;

            /// <summary>
            /// 9.停用/作廢
            /// </summary>
            static public byte Disabled = 9;

            /// <summary>
            /// 9.停用/作廢
            /// </summary>
            static public byte NotShow = 3;

            static public string GetDesc(byte? Val)
            {
                if (Val == NotStarted)
                {
                    return "擬定中";
                }
                else if (Val == StartedOut)
                {
                    return "投票中";
                }
                else if (Val == Disabled)
                {
                    return "作廢";
                }
                else
                {
                    return string.Empty;
                }
            }
        }
        /// <summary>
        /// 投票開放察看結果人員
        /// </summary>
        /// 
        public class Resault_TYPE_VAL
        {
            static public string all = "N";

            static public string Teacher = "T";
            static public string Admin = "A";
            static public string GetDesc(string Val)
            {
                if (Val == all)
                {
                    return "結果開放給所有人(學生要活動結束才看的到)";
                }
                else if (Val == Teacher)
                {
                    return "活動期間開放給老師看數據";

                }
                else if (Val == Admin)
                {
                    return "只開放給管理者看數據、結果";

                }
                else
                {
                    return string.Empty;
                }
            }
            static private List<string> SetResault_TYPE()
            {
                List<string> TempList = new List<string>();
                TempList.Add(all);
                TempList.Add(Teacher);
                TempList.Add(Admin);
                return TempList;
            }

            public static List<SelectListItem> SelectItem(string SelectedVal = null)
            {
                List<SelectListItem> ThisSelectItem = new List<SelectListItem>();
                foreach (var item in SetResault_TYPE())
                {
                    ThisSelectItem.Add(new SelectListItem() { Text = GetDesc(item), Value = item, Selected = SelectedVal == item });
                }

                return ThisSelectItem;
            }
        }
         public class REPORT_TYPE_VAL
        {
            /// <summary>
            /// 1.投票
            /// </summary>
            static public byte vote = 1;

            static public string GetDesc(byte? Val)
            {
                if (Val == vote)
                {
                    return "投票";
                }
                else
                {
                    return string.Empty;
                }
            }
        }
    }
}