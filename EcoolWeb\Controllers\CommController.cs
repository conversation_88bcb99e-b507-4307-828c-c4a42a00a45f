﻿using com.ecool.service;
using ECOOL_APP.EF;
using System.Linq;
using EcoolWeb.Models;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.service;

namespace EcoolWeb.Controllers
{
    public class CommController : Controller
    {
        // GET: Comm
        public ActionResult Role()
        {
            return PartialView("../Shared/_Role", new CommService().USP_COMM_ROLE());
        }

        public ActionResult Upload()
        {
            return PartialView("../Shared/_Upload");
        }

        public ActionResult MultiFileUpload()
        {
            return PartialView("../Shared/_MultiFileUpload");
        }

        public ActionResult MultiFiles2Upload()
        {
            return PartialView("../Shared/_MultiFiles2Upload");
        }

        /// <summary>
        /// 全螢幕iframe partial view
        /// </summary>
        /// <param name="src"></param>
        /// <returns></returns>
        public ActionResult IframeFullPage(string src)
        {
            if (src.IndexOf("https://www.youtube.com/embed/") != -1)
            {
                // youtube影片連結 自動撥放
                src += @"?autoplay=1";
            }
            ViewBag.Src = src;
            return PartialView("IframeFullPage");
        }

        public ActionResult _imagesRotateBtnView(string ImgURL, string ImgID, string ImgURL_S, string ImgURL_M,string SourceNO, string user_NO, string schoo_NO, string sourceID)
        {
            imagesRotateBtnViewModel Model = new imagesRotateBtnViewModel();
            Model.ImgURL = ImgURL;
            Model.ImgID = ImgID;
            Model.ImgURL_S = ImgURL_S;
            Model.ImgURL_M = ImgURL_M;
            if (!string.IsNullOrEmpty(SourceNO))
            {
                Model.SouceNO = SourceNO;
            }
            if (!string.IsNullOrEmpty(user_NO))
            {
                Model.user_NO = user_NO;
            }
            if (!string.IsNullOrEmpty(schoo_NO))
            {
                Model.schoo_NO = schoo_NO;
            }
            if (!string.IsNullOrEmpty(sourceID))
            {
                Model.sourceID = sourceID;
            }
            return PartialView("_imagesRotateBtnView", Model);
        }
        public ActionResult imagesRotate1(string ImgURL, float RotateAngle,string sourceNO,string user_NO,string schoo_NO, string sourceID)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            ZZZI34Service zzi34Serc = new ZZZI34Service();
            int SourceIDs = Int32.Parse(sourceID);
            try
            {
                ImgURL = System.Web.HttpUtility.UrlDecode(ImgURL);

                string RealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(ImgURL));
                string surlImg = "";
                string surlImgRealPath = "";
                if (ImgURL.IndexOf(".png", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".png", "_S.png");
                    surlImgRealPath = Server.MapPath(
                        ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg)
                        );
                   
                }
                else if (ImgURL.IndexOf(".jpg", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".jpg", "_S.jpg");
                    surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                }
                else if (ImgURL.IndexOf(".jpeg", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".jpeg", "_S.jpeg");
                    surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                }
                else if (ImgURL.IndexOf(".gif", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".gif", "_S.gif");
                    surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                }
                if (System.IO.File.Exists(surlImgRealPath))

                {
                    Image image2 = Image.FromFile(surlImgRealPath);
                    ImageFormat thisFormat2 = image2.RawFormat;
                    Bitmap imageOutput2 = new Bitmap(image2);
                    image2.Dispose();
                    //釋放掉圖檔
                    imageOutput2 = zzi34Serc.KiRotate(imageOutput2, RotateAngle);

                    //砍掉原圖檔
                    System.IO.File.Delete(surlImgRealPath);

                    imageOutput2.Save(surlImgRealPath, thisFormat2);
                    imageOutput2.Dispose();
                }
                if (ImgURL.IndexOf("Small/", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    string bigImgURL = ImgURL.Replace("Small/", "");
                    string BigRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(bigImgURL));
                    if (System.IO.File.Exists(BigRealPath))
                    {
                        Image image1 = Image.FromFile(BigRealPath);
                        ImageFormat thisFormat1 = image1.RawFormat;
                        Bitmap imageOutput1 = new Bitmap(image1);
                        image1.Dispose();
                        //釋放掉圖檔
                        imageOutput1 = zzi34Serc.KiRotate(imageOutput1, RotateAngle);

                        //砍掉原圖檔
                        System.IO.File.Delete(BigRealPath);

                        imageOutput1.Save(BigRealPath, thisFormat1);
                        imageOutput1.Dispose();
                    }
                }

                Image image = Image.FromFile(RealPath);
                ImageFormat thisFormat = image.RawFormat;
                Bitmap imageOutput = new Bitmap(image);
                image.Dispose();
                //釋放掉圖檔
                imageOutput = zzi34Serc.KiRotate(imageOutput, RotateAngle);

                //砍掉原圖檔
                System.IO.File.Delete(RealPath);
              
                imageOutput.Save(RealPath, thisFormat);
                imageOutput.Dispose();
                
                ImageModifyTemp temp = new ImageModifyTemp();
               
                temp.Source_ID = Guid.NewGuid().ToString("N");
                temp.SCHOOL_NO = schoo_NO;
                temp.USER_NO = user_NO;
                temp.Source = sourceNO;
                temp.CHG_DATE = DateTime.Now;
                temp.Action = "imagesRotate";
                db.ImageModifyTemp.Add(temp);
                if (sourceNO == "ADDT14")
                {
                    ADDT14 aDDT14 = new ADDT14();
                    aDDT14 = db.ADDT14.Where(x => x.IAWARD_ID == SourceIDs && x.SCHOOL_NO == schoo_NO).FirstOrDefault();
                    aDDT14.ImgSourceNO = temp.Source_ID;

                }
                else if (sourceNO == "ADDT15")
                {
                    ADDT15 aDDT15 = new ADDT15();
                    aDDT15 = db.ADDT15.Where(x => x.OAWARD_ID == SourceIDs && x.SCHOOL_NO == schoo_NO).FirstOrDefault();
                    aDDT15.ImgSourceNO = temp.Source_ID;
                }
                db.SaveChanges();

            }
            catch (Exception ex)
            {
                throw ex;
            }

            return Json(ImgURL);
        }
        public ActionResult imagesRotate(string ImgURL, float RotateAngle)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            ZZZI34Service zzi34Serc = new ZZZI34Service();
            try
            {
                ImgURL = System.Web.HttpUtility.UrlDecode(ImgURL);

                string RealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(ImgURL));

                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                //if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                //string ReturnImgUrl = string.Empty;
                //ReturnImgUrl = UploadImageRoot + ImgURL;
                //if (
                //             System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(ReturnImgUrl))
                //    ) { }
       
                string surlImg = "";
                string murlImg = "";
                string surlImgRealPath = "";
                string surlMedImgRealPath = "";
                if (ImgURL.IndexOf(".png", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".png", "_S.png");
                    murlImg = ImgURL.Replace(".png", "_M.png");
                    if (System.IO.File.Exists(Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg))))

                    {
                        surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                    }

                    if (System.IO.File.Exists(Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg))))

                    {
                        surlMedImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg));
                    }
                }
                else if (ImgURL.IndexOf(".jpg", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".jpg", "_S.jpg");
                    murlImg = ImgURL.Replace(".jpg", "_M.jpg");
                    if (System.IO.File.Exists(Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg))))

                    {
                        surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                    }

                    if (System.IO.File.Exists(Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg))))

                    {
                        surlMedImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg));
                    }
                }
                else if (ImgURL.IndexOf(".jpeg", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".jpeg", "_S.jpeg");
                    murlImg = ImgURL.Replace(".jpeg", "_M.jpeg");
                    surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                    if (System.IO.File.Exists(surlImgRealPath))

                    {
                        surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                    }
                    if (System.IO.File.Exists(Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg))))

                    {
                        surlMedImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg));
                    }
                }
                else if (ImgURL.IndexOf(".gif", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    surlImg = ImgURL.Replace(".gif", "_S.gif");
                    murlImg = ImgURL.Replace(".gif", "_M.gif");
                    if (System.IO.File.Exists(Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg))))

                    {
                        surlImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(surlImg));
                    }
                    if (System.IO.File.Exists(Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg))))

                    {
                        surlMedImgRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(murlImg));
                    }
                }



                if (System.IO.File.Exists(surlImgRealPath)) {
                    if (surlImgRealPath.IndexOf("_S.png", StringComparison.OrdinalIgnoreCase) >= 0
                    || surlImgRealPath.IndexOf("_S.jpg", StringComparison.OrdinalIgnoreCase) >= 0 || surlImgRealPath.IndexOf("_S.jpeg", StringComparison.OrdinalIgnoreCase) >= 0 || surlImgRealPath.IndexOf("_S.gif", StringComparison.OrdinalIgnoreCase) >= 0
                    )

                    {

                        Image image2 = Image.FromFile(surlImgRealPath);
                        ImageFormat thisFormat2 = image2.RawFormat;
                        Bitmap imageOutput2 = new Bitmap(image2);
                        image2.Dispose();
                        //釋放掉圖檔
                        imageOutput2 = zzi34Serc.KiRotate(imageOutput2, RotateAngle);

                        //砍掉原圖檔
                        System.IO.File.Delete(surlImgRealPath);

                        imageOutput2.Save(surlImgRealPath, thisFormat2);
                        imageOutput2.Dispose();
                    }
                    if (surlMedImgRealPath.IndexOf("_M.png", StringComparison.OrdinalIgnoreCase) >= 0
    || surlMedImgRealPath.IndexOf("_M.jpg", StringComparison.OrdinalIgnoreCase) >= 0 || surlMedImgRealPath.IndexOf("_M.jpeg", StringComparison.OrdinalIgnoreCase) >= 0 || surlMedImgRealPath.IndexOf("_M.gif", StringComparison.OrdinalIgnoreCase) >= 0
    )
                        {

                            Image image3 = Image.FromFile(surlMedImgRealPath);
                            ImageFormat thisFormat3 = image3.RawFormat;
                            Bitmap imageOutput3 = new Bitmap(image3);
                            image3.Dispose();
                            //釋放掉圖檔
                            imageOutput3 = zzi34Serc.KiRotate(imageOutput3, RotateAngle);

                            //砍掉原圖檔
                            System.IO.File.Delete(surlMedImgRealPath);

                            imageOutput3.Save(surlMedImgRealPath, thisFormat3);
                            imageOutput3.Dispose();


                        }
                 
                }
               

                if (ImgURL.IndexOf("Small/", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    string bigImgURL = ImgURL.ToLower().Replace("Small/", "");
                    string BigRealPath = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(bigImgURL));
                    if (System.IO.File.Exists(BigRealPath))
                    {
                        Image image1 = Image.FromFile(BigRealPath);
                        ImageFormat thisFormat1 = image1.RawFormat;
                        Bitmap imageOutput1 = new Bitmap(image1);
                        image1.Dispose();
                        //釋放掉圖檔
                        imageOutput1 = zzi34Serc.KiRotate(imageOutput1, RotateAngle);

                        //砍掉原圖檔
                        System.IO.File.Delete(BigRealPath);

                        imageOutput1.Save(BigRealPath, thisFormat1);
                        imageOutput1.Dispose();
                    }
                }
                if (!string.IsNullOrWhiteSpace(RealPath)) {
                    if (RealPath.IndexOf("_S.png", StringComparison.OrdinalIgnoreCase)< 0
                  && RealPath.IndexOf("_S.jpg", StringComparison.OrdinalIgnoreCase)< 0 && RealPath.IndexOf("_S.jpeg", StringComparison.OrdinalIgnoreCase) <0 && RealPath.IndexOf("_S.gif", StringComparison.OrdinalIgnoreCase)< 0
                  && RealPath.IndexOf("_M.png", StringComparison.OrdinalIgnoreCase) <0
                  && RealPath.IndexOf("_M.jpg", StringComparison.OrdinalIgnoreCase) < 0 && RealPath.IndexOf("_M.jpeg", StringComparison.OrdinalIgnoreCase) <0 && RealPath.IndexOf("_M.gif", StringComparison.OrdinalIgnoreCase)<0
                  
                  
                  ) {
                        //string RealPathSTR = "";
                        //RealPathSTR = Server.MapPath(ECOOL_APP.UrlCustomHelper.RemoveUrl_Content(ImgURL));
                        //Image Firstimage = Image.FromFile(RealPathSTR);
                        //ImageFormat thisFormat = Firstimage.RawFormat;
                        //Bitmap FistimageOutput = new Bitmap(Firstimage);
                        //Firstimage.Dispose();
                        ////釋放掉圖檔
                        //FistimageOutput = zzi34Serc.KiRotate(FistimageOutput, RotateAngle);

                        ////砍掉原圖檔
                        //System.IO.File.Delete(RealPathSTR);

                        //FistimageOutput.Save(RealPathSTR, thisFormat);
                        //FistimageOutput.Dispose();

                        Image image1 = Image.FromFile(RealPath);
                        ImageFormat thisFormat1 = image1.RawFormat;
                        Bitmap imageOutput1 = new Bitmap(image1);
                        image1.Dispose();
                        //釋放掉圖檔
                        imageOutput1 = zzi34Serc.KiRotate(imageOutput1, RotateAngle);

                        //砍掉原圖檔
                        System.IO.File.Delete(RealPath);

                        imageOutput1.Save(RealPath, thisFormat1);
                        imageOutput1.Dispose();


                    }
                        
                }
            }
            catch (Exception ex)
            {
                throw ex;
               
            }
            
            return Json(ImgURL);
        }

        /// <summary>
        /// 任意角度旋轉
        /// </summary>
        /// <param name="bmp">原始圖Bitmap</param>
        /// <param name="angle">旋轉角度</param>
        /// <returns>輸出Bitmap</returns>
      
    }
}