/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Size2={directory:"Size2/Regular",family:"GyreTermesMathJax_Size2",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[834,334,457,127,372],41:[834,334,457,85,330],47:[936,436,658,80,578],91:[845,345,418,127,333],92:[936,436,658,80,578],93:[845,345,418,85,291],123:[836,336,435,85,350],124:[826,326,209,80,129],125:[836,336,435,85,350],160:[0,0,250,0,0],770:[698,-537,727,0,727],771:[688,-532,720,0,720],774:[694,-542,740,0,740],780:[693,-532,727,0,727],785:[707,-554,740,0,740],812:[-70,231,727,0,727],813:[-80,241,727,0,727],814:[-70,223,740,0,740],815:[-88,241,740,0,740],816:[-88,244,720,0,720],8214:[826,326,358,80,278],8260:[936,436,658,80,578],8425:[732,-548,1125,0,1125],8730:[1000,474,621,120,647],8739:[826,326,209,80,129],8741:[826,326,358,80,278],8968:[845,326,418,127,333],8969:[845,326,418,85,291],8970:[826,345,418,127,333],8971:[826,345,418,85,291],9001:[941,441,409,85,324],9002:[941,441,409,85,324],9140:[732,-548,1125,0,1125],9141:[-98,282,1125,0,1125],9180:[732,-541,1514,0,1514],9181:[-91,282,1514,0,1514],9182:[766,-533,1519,0,1519],9183:[-83,316,1519,0,1519],9184:[736,-547,1568,0,1568],9185:[-97,286,1568,0,1568],10214:[845,345,425,127,340],10215:[845,345,425,85,298],10216:[941,441,409,85,324],10217:[941,441,409,85,324],10218:[941,441,620,85,535],10219:[941,441,620,85,535],10222:[833,333,347,127,262],10223:[833,333,347,85,220]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Size2"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size2/Regular/Main.js"]);
