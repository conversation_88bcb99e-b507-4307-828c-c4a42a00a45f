/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{8705:[760,15,463,59,404],8708:[775,122,560,71,487],8710:[674,0,731,63,665],8714:[459,-45,486,64,422],8716:[662,157,685,60,625],8717:[459,-45,486,64,422],8718:[640,0,545,60,485],8724:[741,41,685,48,636],8731:[973,259,928,112,963],8732:[973,259,928,112,963],8735:[584,0,685,50,634],8737:[547,72,685,22,642],8738:[519,11,685,56,653],8740:[690,189,404,23,381],8742:[690,189,609,23,586],8748:[824,320,701,32,881],8749:[824,320,943,32,1123],8750:[824,320,499,32,639],8751:[824,320,741,32,881],8752:[824,320,982,32,1122],8753:[824,320,499,32,639],8754:[824,320,499,32,639],8755:[824,320,499,32,639],8756:[521,16,620,38,582],8757:[521,16,620,38,582],8758:[521,13,511,192,319],8759:[521,13,685,82,602],8760:[511,-220,685,48,637],8761:[511,5,685,48,637],8762:[511,5,685,48,637],8763:[521,13,685,48,637],8765:[362,-148,685,48,637],8766:[413,-90,685,48,637],8767:[467,-39,685,49,637],8769:[424,-88,685,48,637],8770:[445,-55,685,48,637],8772:[519,35,685,48,637],8774:[604,107,685,47,637],8775:[647,202,685,48,637],8777:[549,49,685,48,637],8778:[552,45,685,48,637],8779:[532,26,685,48,638],8780:[532,27,685,48,637],8782:[471,-35,685,48,637],8783:[471,-120,685,48,637],8785:[611,106,685,48,637],8786:[611,105,685,48,637],8787:[611,106,685,48,637],8788:[417,-89,824,48,776],8789:[417,-89,824,48,776],8790:[416,-90,685,48,637],8791:[752,-120,685,48,637],8792:[729,-120,685,48,637],8793:[853,-120,685,48,637],8794:[853,-120,685,48,637],8795:[756,-120,685,48,637],8796:[853,-120,685,48,637],8797:[823,-120,685,7,678],8798:[703,-120,685,48,637],8799:[863,-120,685,48,637],8802:[662,156,685,48,637],8803:[544,38,685,48,637],8806:[718,211,685,57,622],8807:[718,211,685,57,622],8808:[746,260,685,56,621],8809:[746,260,685,56,621],8812:[730,224,466,85,381],8813:[572,66,685,48,637],8814:[662,156,685,56,621],8815:[662,156,685,56,621],8816:[730,229,685,56,621],8817:[730,229,685,56,622],8818:[664,164,685,48,637],8819:[664,164,685,48,637],8820:[731,228,685,48,637],8821:[730,229,685,48,637],8822:[705,204,685,56,621],8823:[705,204,685,56,621],8824:[750,250,685,48,637],8825:[750,250,685,48,637],8830:[664,164,685,48,637],8831:[664,164,685,48,637],8832:[662,156,685,64,621],8833:[662,156,685,64,621],8836:[662,156,685,65,623],8837:[662,156,685,65,623],8840:[730,229,685,64,621],8841:[730,229,685,64,621],8842:[627,216,685,64,621],8843:[627,216,685,64,621],8844:[536,31,620,48,572],8845:[536,31,620,48,572],8847:[531,25,685,64,621],8848:[531,25,685,64,621],8858:[623,119,842,50,792],8859:[623,119,842,50,792],8860:[623,119,842,50,792],8861:[623,119,842,50,792],8862:[662,158,910,45,865],8863:[662,158,910,45,865],8864:[662,158,910,45,865],8865:[662,157,910,45,865],8870:[662,0,497,64,433],8871:[662,0,498,64,434],8873:[662,0,860,57,814],8874:[662,0,860,45,815],8875:[662,0,860,57,814],8876:[662,0,786,9,723],8877:[662,0,786,9,723],8878:[662,0,968,9,922],8879:[662,0,968,9,922],8880:[551,45,685,64,621],8881:[551,45,685,64,621],8882:[531,25,685,24,631],8883:[531,25,685,54,661],8884:[607,103,685,24,631],8885:[607,103,685,54,661],8886:[403,-103,1145,50,1095],8887:[403,-103,1145,50,1095],8888:[403,-103,849,50,799],8889:[547,41,685,48,636],8890:[450,212,480,74,406],8891:[536,139,620,32,590],8892:[646,29,620,32,590],8893:[646,29,620,32,590],8894:[584,0,685,50,634],8895:[662,158,911,45,865],8903:[545,38,685,51,634],8905:[582,80,810,93,716],8906:[582,80,810,93,716],8907:[582,80,810,74,736],8908:[582,80,810,74,736],8909:[445,-55,685,48,637],8910:[532,25,580,31,549],8911:[532,25,580,31,549],8912:[531,25,685,64,621],8913:[531,25,685,64,621],8914:[536,31,620,48,572],8915:[536,31,620,48,572],8916:[631,31,620,48,572],8917:[690,189,685,48,637],8918:[534,24,685,56,621],8919:[534,24,685,56,621],8920:[534,24,1274,45,1229],8921:[534,24,1274,45,1229],8922:[830,324,685,56,621],8923:[830,324,685,56,621],8924:[607,103,685,64,621],8925:[607,103,685,64,621],8926:[627,121,685,64,621],8927:[627,121,685,64,621],8928:[730,229,685,64,621],8929:[730,229,685,64,621],8930:[730,229,685,65,622],8931:[730,229,685,65,622],8932:[627,216,685,64,621],8933:[627,216,685,64,621],8934:[669,279,685,48,637],8935:[669,279,685,48,637],8936:[670,279,685,48,637],8937:[670,279,685,48,637],8938:[662,156,635,24,581],8939:[662,156,635,54,611],8940:[730,229,635,24,581],8941:[730,229,635,54,611],8944:[520,18,926,194,732],8946:[531,27,823,55,763],8947:[531,27,685,60,625],8948:[459,-45,486,62,420],8949:[716,27,685,60,625],8950:[685,27,685,60,625],8951:[613,-45,486,62,420],8952:[532,180,685,60,625],8953:[531,27,685,61,625],8954:[531,27,823,55,763],8955:[531,27,685,59,624],8956:[459,-45,486,62,420],8957:[685,27,685,61,626],8958:[613,-45,486,67,425],8959:[662,158,910,45,865]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/MathOperators.js");
