﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'tt', {
	armenian: 'Әрмән номерлавы',
	bulletedTitle: 'Маркерлы тезмә үзлекләре',
	circle: 'Түгәрәк',
	decimal: 'Унарлы (1, 2, 3, ...)',
	decimalLeadingZero: 'Ноль белән башланган унарлы (01, 02, 03, ...)',
	disc: 'Диск',
	georgian: 'Georgian numbering (an, ban, gan, etc.)', // MISSING
	lowerAlpha: 'Lower Alpha (a, b, c, d, e, etc.)', // MISSING
	lowerGreek: 'Lower Greek (alpha, beta, gamma, etc.)', // MISSING
	lowerRoman: 'Lower Roman (i, ii, iii, iv, v, etc.)', // MISSING
	none: 'Һичбер',
	notset: '<билгеләнмәгән>',
	numberedTitle: 'Номерлы тезмә үзлекләре',
	square: 'Шакмак',
	start: 'Башлау',
	type: 'Төр',
	upperAlpha: 'Upper Alpha (A, B, C, D, E, etc.)', // MISSING
	upperRoman: 'Upper Roman (I, II, III, IV, V, etc.)', // MISSING
	validateStartNumber: 'List start number must be a whole number.' // MISSING
} );
