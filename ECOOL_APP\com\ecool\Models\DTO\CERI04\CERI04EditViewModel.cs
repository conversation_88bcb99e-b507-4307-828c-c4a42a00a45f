﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CERI04EditViewModel : SearchFormViewModelBase
    {
        /// <summary>
        /// 護照細項名稱
        /// </summary>
        public string WhereACCREDITATION_NAME { get; set; }

        /// <summary>
        /// 護照ID
        /// </summary>
        public string WhereACCREDITATION_TYPE { get; set; }

        /// <summary>
        /// 年級
        /// </summary>
        public byte? WhereGRADE { get; set; }

        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 通過主旨
        /// </summary>
        public string WhereSUBJECT { get; set; }

        /// <summary>
        /// 列出未認證資料
        /// </summary>
        public bool WhereIsUnVerifier { get; set; }

        public string ThisACCREDITATION_ID { get; set; }

        public string ThisITEM_NO { get; set; }

        public string ThisSCHOOL_NO { get; set; }

        public byte? ThisGRADE { get; set; }

        public string ThisCLASS_NO { get; set; }

        public CERI04ListViewModel QData { get; set; }

        public virtual ICollection<CERI04EditDetailViewModel> Details { get; set; }
    }
}