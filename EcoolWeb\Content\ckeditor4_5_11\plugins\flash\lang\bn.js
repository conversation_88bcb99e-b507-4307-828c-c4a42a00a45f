﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'bn', {
	access: 'Script Access', // MISSING
	accessAlways: 'Always', // MISSING
	accessNever: 'Never', // MISSING
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Abs নীচে',
	alignAbsMiddle: 'Abs উপর',
	alignBaseline: 'মূল রেখা',
	alignTextTop: 'টেক্সট উপর',
	bgcolor: 'পৃষ্ঠতলের রং',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'লূপ',
	chkMenu: 'ফ্ল্যাশ মেনু এনাবল কর',
	chkPlay: 'অটো প্লে',
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'হরাইজন্টাল স্পেস',
	properties: 'ফ্লাশ প্রোপার্টি',
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'স্কেল',
	scaleAll: 'সব দেখাও',
	scaleFit: 'নিখুঁত ফিট',
	scaleNoBorder: 'কোনো বর্ডার নেই',
	title: 'ফ্ল্যাশ প্রোপার্টি',
	vSpace: 'ভার্টিকেল স্পেস',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'অনুগ্রহ করে URL লিংক টাইপ করুন',
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
