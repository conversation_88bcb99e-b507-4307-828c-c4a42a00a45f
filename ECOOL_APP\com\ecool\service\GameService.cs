﻿using com.ecool.service;
using Dapper;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using ECOOL_APP.com.ecool.LogicCenter.RandomGame;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using log4net;
using MvcPaging;
using Newtonsoft.Json;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.SqlServer;
using System.Data.SqlClient;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using System.Web.Mvc;
using ZXing;
using ZXing.QrCode.Internal;
using ZXing.Rendering;

namespace ECOOL_APP
{
    public class GameService
    { 
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// 隨機種子
        /// </summary>
        static Random Rand = new Random((int)DateTime.Now.Ticks);
        private string title_of_show = "title_of_show";

        /// <summary>
        /// 取得網站路徑
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="GAME_NO"></param>
        /// <returns></returns>
        public static string GetSetDirectoryGamePath(string SCHOOL_NO, string GAME_NO)
        {
            string ReturnImgUrl = string.Empty;

            ReturnImgUrl = GetSetDirectoryGamePath() + $@"{SCHOOL_NO}\{GAME_NO}";

            return ReturnImgUrl;
        }

        /// <summary>
        /// 取得網站路徑
        /// </summary>
        /// <returns></returns>
        public static string GetSetDirectoryGamePath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string Game = "Game";
            ReturnImgUrl = $@"{UploadImageRoot}{Game}\";

            return ReturnImgUrl;
        }

        public static List<GameEditDetailsAnsLevelViewModel> GetAddAns(string index, string InputType)
        {
            List<GameEditDetailsAnsLevelViewModel> AnsLeveS = new List<GameEditDetailsAnsLevelViewModel>();
            StringHelper stringHelper = new StringHelper();
            if (InputType == "OX")
            {
                for (int i = 1; i <= 2; i++)
                {
                    GameEditDetailsAnsLevelViewModel Ans = new GameEditDetailsAnsLevelViewModel
                    {
                        Index = index,
                        LEVEL_TYPE = ADDT26_D.LevelType.Pay,
                        LEVEL_ITEM = stringHelper.StrRigth("00" + i.ToString(), 3),
                        LEVEL_NAME = (i == 1) ? "O" : "X",
                        TRUE_ANS = (i == 1) ? true : false,
                        CASH = 0,
                        RETURN_DESC = (i == 1) ? "" : "請看清楚題目",
                    };

                    AnsLeveS.Add(Ans);
                }
            }
            else
            {
                for (int i = 1; i <= 4; i++)
                {
                    GameEditDetailsAnsLevelViewModel Ans = new GameEditDetailsAnsLevelViewModel
                    {
                        Index = index,
                        LEVEL_TYPE = ADDT26_D.LevelType.Pay,
                        LEVEL_ITEM = stringHelper.StrRigth("00" + i.ToString(), 3),
                        LEVEL_NAME = i + ".",
                        TRUE_ANS = (i == 1) ? true : false,
                        CASH = 0,
                        RETURN_DESC = (i == 1) ? "" : "請看清楚題目",
                    };

                    AnsLeveS.Add(Ans);
                }
            }

            return AnsLeveS;
        }

        /// <summary>
        /// 活動列表 - 查詢資料
        /// </summary>
        /// <param name="model"></paramGameIndexViewModel
        /// <returns></returns>
        public GameIndexViewModel GetListData(GameIndexViewModel model, ref ECOOL_DEVEntities db, byte? GAME_TYPE)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var temp = (from a in db.ADDT26
                            where (a.GAME_TYPE ?? (byte)ADDT26.GameType.一般) == (GAME_TYPE ?? (byte)ADDT26.GameType.一般)
                            select new GameListDataViewModel()
                            {
                                SCHOOL_NO = a.SCHOOL_NO,
                                GAME_NO = a.GAME_NO,
                                GAME_NAME = a.GAME_NAME,
                                GAME_DATES = a.GAME_DATES,
                                GAME_DATEE = a.GAME_DATEE,
                                CRE_PERSON = a.CRE_PERSON,
                                CRE_DATE = a.CRE_DATE,
                                CHG_PERSON = a.CHG_PERSON,
                                CHG_DATE = a.CHG_DATE,
                                GAME_TYPE = a.GAME_TYPE ?? (byte)ADDT26.GameType.一般
                            });

                if (!string.IsNullOrWhiteSpace(model.Search.WhereGAME_NAME))
                {
                    temp = temp.Where(a => a.GAME_NAME.Contains(model.Search.WhereGAME_NAME.Trim()));
                }

                if (!string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
                {
                    temp = temp.Where(a => a.SCHOOL_NO == model.Search.WhereSCHOOL_NO);
                }

                temp = temp.OrderByDescending(a => a.CRE_DATE);

                model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

                return model;
            }
        }
        public GameIndexViewModel GetListData1(GameIndexViewModel model, ref ECOOL_DEVEntities db, byte? GAME_TYPE)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var temp = (from a in db.ADDT26
                            join b in db.ADDT26_D on new { a.GAME_NO  } equals new { b.GAME_NO }
                            where a.GAME_TYPE  == (GAME_TYPE ?? (byte)ADDT26.GameType.贈品抽獎)
                            select new GameListDataViewModel()
                            {
                                Coupons_ITem= b.Coupons_ITem,
                                LEVEL_NO=b.LEVEL_NO,
                                SCHOOL_NO = a.SCHOOL_NO,
                                GAME_NO = a.GAME_NO,
                                GAME_NAME = a.GAME_NAME,
                                GAME_DATES = a.GAME_DATES,
                                GAME_DATEE = a.GAME_DATEE,
                                CRE_PERSON = a.CRE_PERSON,
                                CRE_DATE = a.CRE_DATE,
                                CHG_PERSON = a.CHG_PERSON,
                                CHG_DATE = a.CHG_DATE,
                                GAME_TYPE = a.GAME_TYPE ?? (byte)ADDT26.GameType.贈品抽獎
                            });

                if (!string.IsNullOrWhiteSpace(model.Search.WhereGAME_NAME))
                {
                    temp = temp.Where(a => a.GAME_NAME.Contains(model.Search.WhereGAME_NAME.Trim()));
                }

                if (!string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
                {
                    temp = temp.Where(a => a.SCHOOL_NO == model.Search.WhereSCHOOL_NO);
                }

                temp = temp.OrderByDescending(a => a.CRE_DATE);

                model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

                return model;
            }
        }
        /// <summary>
        /// 查詢抽獎資料
        /// </summary>
        /// <param name="model"></paramGameIndexViewModel
        /// <returns></returns>
        public GameLotteryViewModel GetLotteryListData(GameLotteryViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var temp = (from a in db.ADDT32
                            where a.STATUS != ((byte)ADDT32.StatusVal.作廢).ToString()
                            select new GameLotteryListViewModel()
                            {
                                LOTTERY_NO = a.LOTTERY_NO,
                                GAME_NO = a.GAME_NO,
                                CRE_DATE = a.CRE_DATE,
                                CHG_PERSON = a.CHG_PERSON,
                                LEVEL = a.LEVEL,
                                PEOPLE_COUNT = a.PEOPLE_COUNT,
                                UNLOTTERY = (bool)a.UNLOTTERY,
                                UNCUEST = (bool)a.UNCUEST,
                                STATUS = a.STATUS,
                                LEVEL_COUNT = a.LEVEL_COUNT,
                                LOTTERY_DESC = a.LOTTERY_DESC,
                                IS_GIVE_UP_LOTTERY = a.IS_GIVE_UP_LOTTERY ?? false,
                            });

                if (!string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
                {
                    temp = temp.Where(a => a.GAME_NO == model.Search.WhereGAME_NO);
                }

                if (string.IsNullOrWhiteSpace(model.Search.OrdercColumn) == false)
                {
                    if (model.Search.SyntaxName == "Desc")
                    {
                        switch (model.Search.OrdercColumn)
                        {
                            case "LEVEL_COUNT":
                                temp = temp.OrderByDescending(a => a.LEVEL_COUNT);
                                break;

                            case "LEVEL":
                                temp = temp.OrderByDescending(a => a.LEVEL);
                                break;

                            case "PEOPLE_COUNT":
                                temp = temp.OrderByDescending(a => a.PEOPLE_COUNT);
                                break;

                            case "UNLOTTERY":
                                temp = temp.OrderByDescending(a => a.UNLOTTERY);
                                break;

                            case "UNCUEST":
                                temp = temp.OrderByDescending(a => a.UNCUEST);
                                break;

                            case "LOTTERY_DESC":
                                temp = temp.OrderByDescending(a => a.LOTTERY_DESC);
                                break;

                            default:
                                temp = temp.OrderByDescending(X => X.CRE_DATE);
                                break;
                        }
                    }
                    else
                    {
                        switch (model.Search.OrdercColumn)
                        {
                            case "LEVEL_COUNT":
                                temp = temp.OrderBy(a => a.LEVEL_COUNT);
                                break;

                            case "LEVEL":
                                temp = temp.OrderBy(a => a.LEVEL);
                                break;

                            case "PEOPLE_COUNT":
                                temp = temp.OrderBy(a => a.PEOPLE_COUNT);
                                break;

                            case "UNLOTTERY":
                                temp = temp.OrderBy(a => a.UNLOTTERY);
                                break;

                            case "UNCUEST":
                                temp = temp.OrderBy(a => a.UNCUEST);
                                break;

                            case "LOTTERY_DESC":
                                temp = temp.OrderBy(a => a.LOTTERY_DESC);
                                break;

                            default:
                                temp = temp.OrderBy(X => X.CRE_DATE);
                                break;
                        }
                    }
                }
                else
                {
                    temp = temp.OrderByDescending(a => a.CRE_DATE);
                }

                model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

                return model;
            }
        }

        public GameLotteryCreViewModel GetLotteryDetailsData(GameLotteryCreViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.Main = GetAddt32Data(model, db);

                var Temp = (from a in db.ADDT33
                            join c in db.ADDT27 on a.TEMP_USER_ID equals c.TEMP_USER_ID
                            where a.LOTTERY_NO == model.Search.WhereLOTTERY_NO
                            select new GameLotteryPeopleViewModel()
                            {
                                LOTTERY_NO = a.LOTTERY_NO,
                                ITEM_NO = a.ITEM_NO,
                                TEMP_USER_ID = a.TEMP_USER_ID,
                                SCHOOL_NO = a.SCHOOL_NO,
                                SHORT_NAME = c.SHORT_NAME,
                                USER_NO = a.USER_NO,
                                GAME_USER_TYPE = a.GAME_USER_TYPE,
                                GAME_USER_TYPE_DESC = c.GAME_USER_TYPE_DESC,
                                NAME = a.NAME,
                                SNAME = a.SNAME,
                                SEX = a.SEX,
                                GRADE = a.GRADE,
                                CLASS_NO = a.CLASS_NO,
                                SEAT_NO = a.SEAT_NO,
                                PHONE = a.PHONE,
                                GAME_USER_ID = c.GAME_USER_ID,
                                RECEIVE_AWARD = a.RECEIVE_AWARD ?? false
                            });

                if (string.IsNullOrEmpty(model.Search.OrdercColumn))
                {
                    Temp = Temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.GAME_USER_TYPE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.NAME);
                }
                else
                {
                    if (model.Search.OrdercColumn == "NAME")
                    {
                        Temp = Temp.OrderBy(a => a.NAME);
                    }
                    else if (model.Search.OrdercColumn == "PHONE")
                    {
                        Temp = Temp.OrderBy(a => a.PHONE);
                    }
                    else if (model.Search.OrdercColumn == "USER_NO")
                    {
                        Temp = Temp.OrderBy(a => a.USER_NO);
                    }
                    else if (model.Search.OrdercColumn == "USER_NO")
                    {
                        Temp = Temp.OrderBy(a => a.USER_NO);
                    }
                    else if (model.Search.OrdercColumn == "GAME_USER_TYPE")
                    {
                        Temp = Temp.OrderBy(a => a.GAME_USER_TYPE).ThenBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.NAME);
                    }
                    else if (model.Search.OrdercColumn == "RECEIVE_AWARD")
                    {
                        Temp = Temp.OrderBy(a => a.RECEIVE_AWARD).ThenBy(a => a.SCHOOL_NO).ThenBy(a => a.GAME_USER_TYPE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.NAME);
                    }
                    else
                    {
                        Temp = Temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.GAME_USER_TYPE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.NAME);
                    }
                }

                model.People = Temp.ToList();

                return model;
            }
        }

        public GameLotteryListViewModel GetAddt32Data(GameLotteryCreViewModel model, ECOOL_DEVEntities db)
        {
            var Main = (from a in db.ADDT32
                        where a.LOTTERY_NO == model.Search.WhereLOTTERY_NO
                        select new GameLotteryListViewModel()
                        {
                            LOTTERY_NO = a.LOTTERY_NO,
                            GAME_NO = a.GAME_NO,
                            CRE_DATE = a.CRE_DATE,
                            CHG_PERSON = a.CHG_PERSON,
                            LEVEL = a.LEVEL,
                            PEOPLE_COUNT = a.PEOPLE_COUNT,
                            UNLOTTERY = (bool)a.UNLOTTERY,
                            UNCUEST = (bool)a.UNCUEST,
                            STATUS = a.STATUS,
                            LEVEL_COUNT = a.LEVEL_COUNT,
                            LOTTERY_DESC = a.LOTTERY_DESC,
                            IS_GIVE_UP_LOTTERY = a.IS_GIVE_UP_LOTTERY ?? false,
                            IS_FULL = a.IS_FULL ?? false,
                        }).FirstOrDefault();

            return Main;
        }

        public GameLotteryListIndexViewModel GetLotteryDetailsListData(GameLotteryListIndexViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var Temp = (from a in db.ADDT33
                            join c in db.ADDT27 on a.TEMP_USER_ID equals c.TEMP_USER_ID
                            join d in db.ADDT32 on a.LOTTERY_NO equals d.LOTTERY_NO
                            where d.GAME_NO == model.Search.WhereGAME_NO
                            && d.STATUS == ((int)ADDT32.StatusVal.已抽獎).ToString()
                            select new GameLotteryPeopleViewModel()
                            {
                                LOTTERY_NO = a.LOTTERY_NO,
                                ITEM_NO = a.ITEM_NO,
                                TEMP_USER_ID = a.TEMP_USER_ID,
                                SCHOOL_NO = a.SCHOOL_NO,
                                SHORT_NAME = c.SHORT_NAME,
                                USER_NO = a.USER_NO,
                                GAME_USER_TYPE = a.GAME_USER_TYPE,
                                GAME_USER_TYPE_DESC = c.GAME_USER_TYPE_DESC,
                                NAME = a.NAME,
                                SNAME = a.SNAME,
                                SEX = a.SEX,
                                GRADE = a.GRADE,
                                CLASS_NO = a.CLASS_NO,
                                SEAT_NO = a.SEAT_NO,
                                PHONE = a.PHONE,
                                GAME_USER_ID = c.GAME_USER_ID,
                                RECEIVE_AWARD = a.RECEIVE_AWARD ?? false
                            });

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereSCHOOL_NO) && model?.CashSearch?.WhereSCHOOL_NO != SharedGlobal.ALL)
                {
                    Temp = Temp.Where(a => a.SCHOOL_NO == model.CashSearch.WhereSCHOOL_NO);
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereCARD_NO))
                {
                    Temp = Temp.Where(a => a.GAME_USER_ID.Contains(model.CashSearch.WhereCARD_NO.Trim()));
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereNAME))
                {
                    Temp = Temp.Where(a => a.NAME.Contains(model.CashSearch.WhereNAME.Trim()));
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereCLASS_NO))
                {
                    Temp = Temp.Where(a => a.CLASS_NO == model.CashSearch.WhereCLASS_NO);
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereSEAT_NO))
                {
                    Temp = Temp.Where(a => a.SEAT_NO == model.CashSearch.WhereSEAT_NO);
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereUSER_NO))
                {
                    Temp = Temp.Where(a => a.USER_NO == model.CashSearch.WhereUSER_NO);
                }

                Temp = Temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.GAME_USER_TYPE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.NAME);

                model.People = Temp.ToList();

                var ArrLottery = model.People.Select(a => a.LOTTERY_NO).Distinct().ToList();

                model.Main = (from a in db.ADDT32
                              where a.GAME_NO == model.Search.WhereGAME_NO
                              && a.STATUS == ((int)ADDT32.StatusVal.已抽獎).ToString()
                              && ArrLottery.Contains(a.LOTTERY_NO)
                              select new GameLotteryListViewModel()
                              {
                                  LOTTERY_NO = a.LOTTERY_NO,
                                  GAME_NO = a.GAME_NO,
                                  LOTTERY_DESC = a.LOTTERY_DESC,
                              }).OrderBy(a => a.LOTTERY_DESC).AsList();

                return model;
            }
        }
        public static int getWeightRandom(List<int> weights)
        {

            //在權重總和的值區間--索引值 map
            Dictionary<int, int> weightIndex = new Dictionary<int, int>();
            int weigntSum = 0;
            //遍歷權重list,保存在權重總和的值區間--索引值 
            for (int i = 0; i < weights.Count(); i++)
            {
                //上區間值
                if (0 == i)
                {
                    weightIndex.Add(1, 0);
                }
                else
                {
                    weightIndex.Add(weigntSum + 1, i);
                }
                //下區間值
                weigntSum += weights[i];
                if (!weightIndex.ContainsKey(weigntSum)) {

                    weightIndex.Add(weigntSum, i);
                }
               
            }
            /*String json = JsonKit.toJson(weightIndex);
            System.out.println(json);*/

            //生成隨機數
            Random r = new Random();
            int num = r.Next(weigntSum) + 1;

            //根據生成的隨機數獲取值區間對應的索引值
            GameService util = new GameService();
            int index = util.getWeightIndex(weightIndex, num);
           
            //返回帶權重的列表索引
            return index;
        }

        private int getWeightIndex(Dictionary<int, int> weightIndex, int num)
        {
            int keyIndex = 0;
            //隨機數所在的值區間
           foreach(int key in weightIndex.Keys)
            {
                if (num <= key)
                {
                    keyIndex = key;
                    break;
                }
            }
            //找到值區間所在的索引
            int index = weightIndex[keyIndex];
            return index;
        }

        public GameLeveViewModel GetLotteryPrize(GameLeveViewModel model, string SCHOOL_NO,ref ECOOL_DEVEntities db)
        {
   
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                Random r = new Random(Guid.NewGuid().GetHashCode());

                var temp = (from a in db.LotteryPrize
                            where a.PrizeQty > 0 && a.GAME_NO == model.GAME_NO && a.LevelNO == model.Coupons_ITem
                            select new GameLotteryPrizeViewModel()
                            {
                                PrizeId = a.PrizeId,
                                PrizeName = a.PrizeName,
                                PrizeQty = a.PrizeQty,
                                PrizeRate = a.PrizeRate,
                                UpdateUserID = a.UpdateUserID,
                                UpdateDate = a.UpdateDate,
                                FixQty=a.FixQty
                            }).OrderByDescending(b => b.PrizeRate).ToList();

                Random rnd = new Random();//亂數種子
                model.LotteryPrize = new GameLotteryPrizeViewModel();
                if (temp.Count() != 0)
                {

                    List<int> weights = new List<int>();
                    foreach (var ii in temp) {

                        weights.Add((int)ii.PrizeQty);

                    }
                    int j = 0;
                    for (int i = 0; i < 20; i++)
                    {
                       j= getWeightRandom(weights);
                    }
                    model.IsOK = true;
                    model.LotteryPrize = temp[j];
                    List<ECOOL_APP.com.ecool.Models.DTO.GetWinnerItemViewModel> GetWinnerItems = new List<ECOOL_APP.com.ecool.Models.DTO.GetWinnerItemViewModel>();
                    string sSQL = @"   SELECT Case when b.PrizeName is null then a.PrizeName else b.PrizeName end as PrizeName,Case when b.Y_CASH is null then a.Y_CASH  else b.Y_CASH end as Y_CASH,b.CreaUserID,d.NAME,k.CLASS_NO,k.SEAT_NO,k.NAME,b.UpdateDate
                                 FROM LotteryWinner b left join LotteryPrize a on b.PrizeId=a.PrizeId
								 inner join ADDT27 d on b.CreaUserID=d.TEMP_USER_ID
								 inner join HRMT01 k on d.GAME_USER_ID=k.CARD_NO
                                where a.GAME_NO=@GAME_NO and k.SCHOOL_NO=@SCHOOL_NO  order by UpdateDate desc";
                    var temp2 = db.Database.Connection.Query<ECOOL_APP.com.ecool.Models.DTO.GetWinnerItemViewModel>(sSQL, new
                    {
                        GAME_NO = model.GAME_NO,
                        SCHOOL_NO = SCHOOL_NO
                    }).ToList();
                    if (temp2.Count() > 0 && temp.Count()>0) { 
                   string PRICNAME= temp2.FirstOrDefault().PrizeName;
                        int FixQty = 0;
                        FixQty =(int)db.LotteryPrize.Where(x => x.GAME_NO == model.GAME_NO && x.LevelNO == model.Coupons_ITem).Sum(x => x.FixQty).Value;
                        while ((FixQty ) >=10) {
                            FixQty = FixQty / 2;

                        }
                        int ModeVlaue = 0;
                        if (temp2.Count() > 0) {
                            int tempCount = 0;
                            tempCount = temp2.Count();

                            ModeVlaue = tempCount % FixQty;
                        }
                        int LotteryPrizeCount= db.LotteryPrize.Where(x => x.GAME_NO == model.GAME_NO && x.LevelNO == model.Coupons_ITem).Where(x => x.PrizeName == PRICNAME).Count();
                        int PrizeQtytemp = 0;
                        if (LotteryPrizeCount > 0)
                        {

                            PrizeQtytemp = (int)db.LotteryPrize.Where(x => x.GAME_NO == model.GAME_NO && x.LevelNO == model.Coupons_ITem).Where(x => x.PrizeName == PRICNAME).Select(x => x.PrizeQty).FirstOrDefault();
                        }
                        else {
                            PRICNAME= db.LotteryPrize.Where(x => x.GAME_NO == model.GAME_NO && x.LevelNO == model.Coupons_ITem).Select(x=>x.PrizeName).FirstOrDefault();

                            PrizeQtytemp = (int)db.LotteryPrize.Where(x => x.GAME_NO == model.GAME_NO && x.LevelNO == model.Coupons_ITem).Where(x => x.PrizeName == PRICNAME).Select(x => x.PrizeQty).FirstOrDefault();
                        }
                     
                        if (ModeVlaue != 0 || temp2.Count() == 0)
                        {
                            int PRiceMAX = 0;
                            if (PRICNAME == temp[j].PrizeName)
                            {
                                PRiceMAX = temp.Max(x => x.PrizeQty).Value;
                                model.LotteryPrize = temp.Where(x => x.PrizeQty == PRiceMAX).FirstOrDefault();
                            }
                            else
                            {
                                if (PrizeQtytemp != 0)
                                {
                                    if (PrizeQtytemp > temp[j].PrizeQty)
                                    {

                                        PRiceMAX = temp.Max(x => x.PrizeQty).Value;
                                        model.LotteryPrize = temp.Where(x => x.PrizeQty == PRiceMAX).FirstOrDefault();

                                    }

                                }


                            }
                        }
                        else {
                            int PRiceMin = 0;
                            PRiceMin = temp.Min(x => x.PrizeQty).Value;
                            model.LotteryPrize = temp.Where(x => x.PrizeQty == PRiceMin).FirstOrDefault();
                        }
                    }
                    // Int32 total = temp.Sum(x => x.PrizeQty??0); //權重和    
                    // List<int> speed = new List<int>(); //隨機種子
                    // int kk = 0;
                    // for (int i = 0; i < total; i++)

                    // {
                    //     kk = i % (temp.Count());
                    //     speed.Add(kk);
                    // }


                    // model.IsOK = true;

                    // int pos = 0;
                    // Dictionary<int, GameLotteryPrizeViewModel> box = new Dictionary<int, GameLotteryPrizeViewModel>();
                    // for (int i = 0; i < speed.Count(); i++) {
                    //     if (temp[speed[i]].PrizeQty != 0) {
                    //     for (int c = 0; c < temp[speed[i]].PrizeQty; c++) //權重越大所占的面積份數就越多
                    //     {

                    //         pos = GameService.Rand.Next(speed.Count); //取隨機種子坐標
                    //         box[pos] = temp[speed[i]]; //亂序 禮品放入索引是speed[pos]的箱子裡面


                    //         }
                    //     }
                    // }
                    //int gameNO= GameService.Rand.Next(total);
                    // try
                    // {

                    //     model.LotteryPrize = box[GameService.Rand.Next(total)];
                    // }
                    // catch (Exception e)
                    // {
                    //     model.LotteryPrize = temp[0];

                    // }
                }
                else
                {
                    model.IsOK = false;
                    model.Message = "商品皆已兌換完畢囉~~";
                }
                //var model = temp.OrderByDescending(a => ((rnd.Next(0, 100))) * (a.PrizeRate / 100)).First();

                ts.Complete();

                return model;
            }
        }
        private int[] RandomNumberGeneration(int Max, int Min, int Amount)
        {
            int[] RandomNumber = new int[Amount];
            Random rnd = new Random();
            for (int i = 0; i < Amount; i++)
            {
                RandomNumber[i] = rnd.Next(Min, Max + 1); //亂數產生
                for (int j = 0; j < i; j++)
                {
                    if (RandomNumber[j] == RandomNumber[i])
                    { //判斷所產生的亂數是否與前幾個重複，是則重新產生
                        RandomNumber[i] = rnd.Next(Min, Max + 1);
                        j = 0; //重新產生後將j設為0，重新再檢查一遍
                    }
                }
            }
            return RandomNumber;
        }

        public GameLeveViewModel CheckInfoCash(string GameUserID, string GAME_NO, bool? Y_REPEAT)
        {
            GameLeveViewModel model = new GameLeveViewModel();
            string Message = "";
            model.IsOK = true;
            ECOOL_DEVEntities Db = new ECOOL_DEVEntities();
            var St = Db.HRMT01.Where(a => a.CARD_NO == GameUserID &&a.USER_STATUS!=UserStaus.Invalid).Count();
            if (St == 0)
            {
                if (GameUserID.Length != 10)
                {
                    Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的 QR CODE 非本活動所用，卡號：{GameUserID},長度{GameUserID.Length} 碼。</font>";
                    model.IsOK = false;
                }

            }
            else if (St >= 2)
            {
                Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的卡片對應到多位來賓之資料，請聯絡系統人員，卡號：{GameUserID}。</font>";
                model.IsOK = false;
            }
            //var LogCount=(
            //    from x in Db.LotteryPrize
            //    join y in Db.LotteryWinners  on  x.PrizeId  equals y.PrizeId 
            //    join c in Db.ADDT27 on new { y.CreaUserID, x.GAME_NO } equals new { c.TEMP_USER_ID , c.GAME_NO }
            //    where  x.GAME_NO == GAME_NO

            //    )

            string sSQL = @" SELECT  COUNT(*)  FROM LotteryPrize a inner join LotteryWinner b on a.PrizeId =b.PrizeId
                                   inner join ADDT27 c on b.CreaUserID= c.TEMP_USER_ID and a.GAME_NO=c.GAME_NO
                                      where a.GAME_NO=@GAME_NO  and c.GAME_USER_ID=@GameUserID ";
            var temp = Db.Database.Connection.Query<int>(sSQL, new
            {
                GAME_NO = GAME_NO,
                GameUserID= GameUserID
            }).FirstOrDefault();

            if ((Y_REPEAT ?? false) == false && temp > 0)
            {
                model.IsOK = false;
                Message = "您已經抽過此獎品囉！";
            }
            if (!string.IsNullOrEmpty(Message)) {


                model.Message = Message;
            }
            
            return model;
        }
        public GameLeveViewModel SaveLotteryPrizeCashInfo(GameLeveViewModel model, ref string Message, ref ECOOL_DEVEntities db,string SessionID, ref List<Tuple<string, string, int>> valuesList)
        {
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                logger.Error(Message + $" SaveLotteryPrizeCashInfo 開始GAME_NO={model.GAME_NO}");
                try
                {
                    if (model.LotteryPrize != null)
                    {
                        var prize = db.LotteryPrize.Where(a => a.PrizeId == model.LotteryPrize.PrizeId).FirstOrDefault();
                        if (prize != null)
                        {
                            ADDT26 T26 = new ADDT26();
                            ADDT26_D T26_D = new ADDT26_D();
                            ADDT27 T27 = new ADDT27();
                            string LogDesc = string.Empty;
                            if (model.User == null || string.IsNullOrWhiteSpace(model.User.TEMP_USER_ID))
                            {
                                logger.Error($"model.User.TEMP_USER_ID是空的");
                            }
                            T26_D = db.ADDT26_D.Where(X => X.GAME_NO == model.GAME_NO && X.LEVEL_NO == model.LEVEL_NO).FirstOrDefault();
                            T27 = db.ADDT27.Where(x => x.GAME_NO == model.GAME_NO && x.TEMP_USER_ID == model.User.TEMP_USER_ID).FirstOrDefault();
                            T26 = db.ADDT26.Where(X => X.GAME_NO == model.GAME_NO).FirstOrDefault();
                            //CashHelper.AddCash(null, (int)T26_D.CASH, T27.SCHOOL_NO, T27.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref db);

                            LogDesc = T26.GAME_NAME + T26_D.LEVEL_NAME;
                            if (prize != null && prize?.Y_CASH != null && (bool)prize.Y_CASH)
                            {
                                int GameGetCash = 0;
                                GameGetCash = Int32.Parse(prize.PrizeName);

                                string LOG_ID = string.Empty;

                                bool IsPayOK = false;
                                bool OK = false;
                                
                                if (T27 != null)
                                {
                                    if (T27.GAME_USER_TYPE == UserType.Student)
                                    {
                                       List<HRMT01> rMT01 = new List<HRMT01>();
                                        rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == T27.SCHOOL_NO && x.USER_NO == T27.USER_NO && x.USER_STATUS == UserStaus.Enabled).ToList();
                                        string rMT01SName = "";
                                        if (rMT01.Count() > 0)
                                        {


                                            rMT01SName = rMT01.FirstOrDefault().SNAME;
                                        }
                                        else { rMT01SName = ""; }
                                        string sourceTable = "";
                                        if (T26.SUBJECT == "小獎勵(班級加點，受點數控管)" || T26.SUBJECT == "小獎勵(學校加點，不受點數控管)")

                                        {


                                            OK = InsertADDT20(T26_D.LEVEL_NO, (byte)SYear, (byte)Semesters, LogDesc, rMT01,  GameGetCash, T26_D, prize, ref db, model.GAME_NO, T26.GAME_NAME,SessionID);
                                            sourceTable = "ADDT20";
                                        }

                                        else
                                        {
                                            OK =InsertADDT14(T26_D.LEVEL_NO, (byte)SYear, (byte)Semesters, LogDesc, rMT01, GameGetCash, T26_D, prize, ref db, model.GAME_NO, T26.GAME_NAME, SessionID);
                                            sourceTable = "ADDT14";
                                        }
                                        LogDesc = T26.SUBJECT + "-" + LogDesc;
                                        CashHelper.AddCash(null, GameGetCash, T27.SCHOOL_NO, T27.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref db, "", sourceTable,ref valuesList);

                                    }

                                }
                            }
                        }
                        db.SaveChanges();
                        model.IsOK = true;
                    }
                    else {
                        logger.Error( $"model.LotteryPrize 是空的 SaveLotteryPrizeCashInfo 結束錯誤GAME_NO={model.GAME_NO}");
                    }
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.InnerException.Message;
                    if (ex.Message != null) {
                        logger.Error(ex.Message + ex.ToString() + $" SaveLotteryPrizeCashInfo 結束錯誤GAME_NO={model.GAME_NO}");
                    }
                    if (ex.StackTrace != null)
                    {
                        logger.Error(ex.StackTrace + $" SaveLotteryPrizeCashInfo 結束錯誤GAME_NO={model.GAME_NO}");
                    }
                    logger.Error(Message + $" SaveLotteryPrizeCashInfo 結束錯誤GAME_NO={model.GAME_NO}");
                    model.IsOK = false;
                    model.Message = Message;
                    return model;
                }
                ts.Complete();
                logger.Error(Message + $" SaveLotteryPrizeCashInfo 結束GAME_NO={model.GAME_NO}");
            }
            return model;
        }

       
        public bool InsertADDT20(string BATCH_CASH_ID, byte SYear, byte Semesters, string LogDesc, List<HRMT01> H01List,  int GameGetCash, ADDT26_D aDDT26_D, LotteryPrize prize, ref ECOOL_DEVEntities db, string GAME_NO,string GAME_NAME, string SessionID)
        {
            bool ReturnVal = false;
            string BATCH_ID = PushService.CreBATCH_ID();
            List<APPT02> T02List = new List<APPT02>();

            int NUM = 0;
            List<ADDT20> ADDT20AddData = new List<ADDT20>();
            string ErrorMsg = "";
            string User_key = "";
            string ROLL_CALL_DESC = "";
            HRMT01 H02 = new HRMT01();
            string LogDescADDI01 = "";
            try { 
            foreach (var item in H01List)
            {
                User_key = aDDT26_D.CHG_PERSON;
                ROLL_CALL_DESC = LogDesc;
                H02 = db.HRMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_KEY == User_key).FirstOrDefault();
                string IAWARD_KIND = db.ADDT26.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.GAME_NO == aDDT26_D.GAME_NO).Select(x => x.SUBJECT).FirstOrDefault();
                HRMT01 H01 = H01List.Where(A => A.SCHOOL_NO == item.SCHOOL_NO && A.USER_NO == item.USER_NO).FirstOrDefault();
                    int ADDT20temp = 0;
                    ADDT20temp= db.ADDT20.Where(x => x.BATCH_CASH_ID == BATCH_CASH_ID && x.USER_NO == H01.USER_NO && x.SCHOOL_NO == H01.SCHOOL_NO).Count();
                    if (ADDT20temp > 0) {
                        Regex rex = new Regex(BATCH_CASH_ID);
                        List<string> str = new List<string>();
                        str = db.ADDT20.Where(x => x.USER_NO == H01.USER_NO && x.SCHOOL_NO == H01.SCHOOL_NO).Select(x => x.BATCH_CASH_ID).Distinct().ToList();
                        List<int> temp20 = new List<int>();
                        int MATINT = 0;
                        bool MATINTBoolen = false;
                        foreach (var item2 in str) {
                            if (rex.IsMatch(item2) && item2!= BATCH_CASH_ID)
                            {   string strint=
                                item2.Substring(BATCH_CASH_ID.Length, item2.Length- BATCH_CASH_ID.Length);
                               int temp20Int= Int32.Parse(strint);
                                temp20.Add(temp20Int);
                                MATINTBoolen = true;
                            }
                        }
                        if (MATINTBoolen)
                        {
                            MATINT = temp20.Max();
                            BATCH_CASH_ID = BATCH_CASH_ID + (MATINT + 1).ToString();
                        }
                        else {

                            BATCH_CASH_ID = BATCH_CASH_ID + (ADDT20temp + 1).ToString();
                        }
                       

                    }
             if (H01 != null)
                {
                    NUM++;


                    ADDT20 T20 = new ADDT20();
                    T20.BATCH_CASH_ID = BATCH_CASH_ID;
                    T20.SCHOOL_NO = H01.SCHOOL_NO;
                    T20.USER_NO = H01.USER_NO;
                    T20.CLASS_NO = H01.CLASS_NO;
                    T20.SYEAR = (byte)SYear;
                    T20.SEMESTER = (byte)Semesters;
                    T20.SEAT_NO = H01.SEAT_NO;
                    T20.NAME = H01.NAME;
                    T20.SNAME = H01.SNAME;
                    T20.CRE_PERSON = User_key;
                    T20.CRE_DATE = DateTime.Today;
                    T20.CHG_PERSON = User_key;
                    T20.CHG_DATE = DateTime.Now;
                    T20.SUBJECT = IAWARD_KIND;
                    // T20.SUBJECT = prize.SUBJECT;
                    T20.CONTENT_TXT = ROLL_CALL_DESC;
                    T20.MEMO = "俄羅斯轉盤抽獎-"+ GAME_NAME+ aDDT26_D.LEVEL_NAME;
                    T20.CASH = GameGetCash;
                    T20.STATUS = "R";
                    T20.NUM = NUM;

                    T20.APPLY_STATUS = "2";
                    ADDT20AddData.Add(T20);
                    logger.Info("俄羅斯轉盤抽獎 ADDT20" + H01.NAME);
                    ADDI13Service aDDI13Service = new ADDI13Service();
                    if (T20.CASH != 0)
                    {

                        aDDI13Service.GreADDT20PushData(BATCH_ID, T20, ref db, T02List);


                    }
                    LogDesc = StringHelper.LeftStringR("特殊加扣點-" + prize.SUBJECT + "-" + ROLL_CALL_DESC, 47);
                    logger.Info("俄羅斯轉盤抽獎 ADDT20" + LogDesc);
                }

            }

            string TableName = "ADDT20";
            db.ADDT20.AddRange(ADDT20AddData);
            //try
            //{

            //    ReturnVal = ADDT20AddData.BatchDatAddCasha("ADDT26_D", BATCH_CASH_ID, TableName, LogDesc, SessionID, TableName, user, out ErrorMsg, T02List, H01List.FirstOrDefault(), Barcode);
            //}
            //catch (Exception e)
            //{

            //    logger.Info("紙本酷幣點數 BatchDatAddCasha" + e.InnerException);

            //}

            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;
            }
            catch (Exception e) {

                logger.Info("ADDT20 exce" + e.InnerException.Message);

                return ReturnVal;

            }
        }

        /// <summary>
        /// 處理ADDT14 (校內)
        /// </summary>
        /// <param name="BATCH_CASH_ID"></param>
        /// <param name="SYear"></param>
        /// <param name="Semesters"></param>
        /// <param name="H01List"></param>
        /// <param name="Data"></param>
        public bool InsertADDT14(string BATCH_CASH_ID, byte SYear, byte Semesters, string LogDesc, List<HRMT01> H01List, int GameGetCash, ADDT26_D aDDT26_D, LotteryPrize prize, ref ECOOL_DEVEntities db, string GAME_NO, string GAME_NAME, string SessionID)
        {
            bool ReturnVal = false;
            string BATCH_ID = PushService.CreBATCH_ID();
            List<APPT02> T02List = new List<APPT02>();
            List<ADDT14> ADDT14AddData = new List<ADDT14>();
            int NUM = 0;

            string ErrorMsg = "";
            string User_key = "";
            string ROLL_CALL_DESC = "";
            HRMT01 H02 = new HRMT01();
            string LogDescADDI01 = "";
            foreach (var item in H01List)
            {
                User_key = aDDT26_D.CHG_PERSON;
                ROLL_CALL_DESC = LogDesc;
                H02 = db.HRMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_KEY == User_key).FirstOrDefault();
             string   IAWARD_KIND= db.ADDT26.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.GAME_NO == aDDT26_D.GAME_NO).Select(x => x.SUBJECT).FirstOrDefault();
                HRMT01 H01 = H01List.Where(A => A.SCHOOL_NO == item.SCHOOL_NO && A.USER_NO == item.USER_NO).FirstOrDefault();
                if (H01 != null)
                {
                    NUM++;

                    ADDT14 T14 = new ADDT14();
                    T14.BATCH_CASH_ID = BATCH_CASH_ID;
                    T14.SCHOOL_NO = H01.SCHOOL_NO;
                    T14.CREATEDATE = DateTime.Now;
                    T14.SYEAR = (byte)SYear;
                    T14.SEMESTER = (byte)Semesters;
                    T14.TNAME = H02.NAME;
                    T14.CLASS_NO = H01.CLASS_NO;
                    T14.USER_NO = H01.USER_NO;
                    T14.USERNAME = H01.NAME;
                    T14.SNAME = H01.SNAME;
                    //T14.IAWARD_KIND = prize.SUBJECT;
                    T14.IAWARD_KIND = IAWARD_KIND;
                    T14.IAWARD_ITEM = ROLL_CALL_DESC;
                    T14.IAWARD_From = "LotteryPrize";
                    T14.CASH = GameGetCash;
                    T14.REMARK = "";
                    T14.APPLY_STATUS = "2";
                    T14.IMG_FILE = "";
                    logger.Info("俄羅斯轉盤抽獎 ADDT20" + H01.NAME);
                    ADDI13Service aDDI13Service = new ADDI13Service();
                    if (T14.CASH != 0)
                    {
                        ADDT14AddData.Add(T14);

                        aDDI13Service.GreADDT14PushData(BATCH_ID, T14, ref db, T02List);
                    }

                    LogDesc = StringHelper.LeftStringR("校內表現-" + prize.SUBJECT + "-" + ROLL_CALL_DESC, 47);

                    logger.Info("俄羅斯轉盤抽獎 ADDT14" + LogDesc);
                }
            }


            db.ADDT14.AddRange(ADDT14AddData);
            string TableName = "ADDT14";
            //try
            //{

          // ReturnVal = ADDT14AddData.BatchDatAddCasha("ADDI13", BATCH_CASH_ID, TableName, LogDesc, SessionID, TableName, user, out ErrorMsg, T02List, H01List.FirstOrDefault(), Barcode);
            //}
            //catch (Exception e)
            //{

            //    logger.Info("紙本酷幣點數 BatchDatAddCasha" + e.InnerException);

            //}

            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;
        }
        public GameLeveViewModel SaveLotteryPrizeCash(GameLeveViewModel model, ref string Message, ref ECOOL_DEVEntities db, ref List<Tuple<string, string, int>> valuesList)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                logger.Error(Message + $" SaveLotteryPrizeCash 開始GAME_NO={model.GAME_NO}");
                try
                {
                    var prize = db.LotteryPrize.Where(a => a.PrizeId == model.LotteryPrize.PrizeId).FirstOrDefault();
                    if (prize != null)
                    {
                      
                        ADDT26 T26 = new ADDT26();
                        ADDT26_D T26_D = new ADDT26_D();
                        ADDT27 T27 = new ADDT27();
                        int CASHIN = 0;
                        string LogDesc = string.Empty;
                        T26_D = db.ADDT26_D.Where(X => X.GAME_NO == model.GAME_NO && X.LEVEL_NO == model.LEVEL_NO).FirstOrDefault();
                        T27 = db.ADDT27.Where(x => x.GAME_NO == model.GAME_NO && x.TEMP_USER_ID == model.User.TEMP_USER_ID).FirstOrDefault();
                        T26 = db.ADDT26.Where(X => X.GAME_NO == model.GAME_NO).FirstOrDefault();
                        CASHIN = (int)T26_D.CASH;
                        LogDesc =  T26.GAME_NAME + T26_D.LEVEL_NAME;
                        if (T27 != null) {

                            List<HRMT01> rMT01 = new List<HRMT01>();
                            rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == T27.SCHOOL_NO && x.USER_NO == T27.USER_NO && x.USER_STATUS == UserStaus.Enabled).ToList();
                            string rMT01SName = "";
                            if (rMT01.Count() > 0)
                            {


                                rMT01SName = rMT01.FirstOrDefault().SNAME;
                            }
                            else { rMT01SName = ""; }
                            CashHelper.AddCash(null, CASHIN, T27.SCHOOL_NO, T27.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref db, rMT01SName, "",ref valuesList);

                        }
                        //    if ((bool)prize.Y_CASH)
                        //    {

                        //        T26_D.CASH = Int32.Parse(prize.PrizeName);

                        //        string LOG_ID = string.Empty;

                        //        bool IsPayOK = false;
                        //        if (T27 != null)
                        //        {
                        //            if (T27.GAME_USER_TYPE == UserType.Student)
                        //            {
                        //                CashHelper.AddCash(null, (int)(-1*T26_D.CASH), T27.SCHOOL_NO, T27.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref db);

                        //            }

                        //        }
                        //    }
                    }
                    db.SaveChanges();
                    model.IsOK = true;
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    logger.Error(Message + $" SaveLotteryPrizeCash 錯誤結束GAME_NO={model.GAME_NO}");
                    model.IsOK = false;
                    model.Message = Message;
                    return model;
                }
                ts.Complete();
            }
            logger.Error(Message + $" SaveLotteryPrizeCash 結束GAME_NO={model.GAME_NO}");
            return model;
         }
       public GameLeveViewModel SaveLotteryPrize(GameLeveViewModel model, ref string Message, ref ECOOL_DEVEntities db)
        {
            //using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            //{
            logger.Error(Message + $" SaveLotteryPrize 開始GAME_NO={model.GAME_NO}");
            try
                {
                    // using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                    // {
                    var prize = db.LotteryPrize.Where(a => a.PrizeId == model.LotteryPrize.PrizeId).FirstOrDefault();
                    if (prize == null)
                    {
                        Message = "系統發生錯誤;原因:無此獎品";
                        model.IsOK = false;
                        model.Message = Message;
                        return model;
                    }

                    if (prize.PrizeQty <= 0)
                    {
                        Message = "系統發生錯誤;原因:此獎品已無庫存";
                        model.IsOK = false;
                        model.Message = Message;
                        return model;
                    }
                    prize.PrizeQty = prize.PrizeQty - 1;

                    db.Entry(prize).State = System.Data.Entity.EntityState.Modified;
                    if (!string.IsNullOrWhiteSpace(model.GameUserID))
                    {
                        if (string.IsNullOrWhiteSpace(model.User?.TEMP_USER_ID)) {
                            if (model.User == null) {
                                model.User = new ADDT27();
                            }
                            model.User.TEMP_USER_ID = model.GameUserID;
                        }
                    }
                    LotteryWinner Cre = new LotteryWinner
                    {
                        WinnerID = Guid.NewGuid().ToString("N"),
                        PrizeId = model.LotteryPrize.PrizeId,
                        CreaDate = DateTime.Now,
                        CreaUserID = model.User.TEMP_USER_ID,
                        UpdateDate = DateTime.Now,
                        UpdateUserID = model.User.TEMP_USER_ID,
                        GAME_NO= prize.GAME_NO,
                        PrizeName= prize.PrizeName,
                        Y_CASH=prize.Y_CASH,
                        Status = 1
                    };
                    if (prize != null)
                    {
                        ADDT26 T26 = new ADDT26();
                        ADDT26_D T26_D = new ADDT26_D();
                        ADDT27 T27 = new ADDT27();
                        if ((bool)prize.Y_CASH)
                        {
                            T26_D = db.ADDT26_D.Where(X => X.GAME_NO == model.GAME_NO && X.LEVEL_NO == model.LEVEL_NO).FirstOrDefault();
                            T27= db.ADDT27.Where(x=>x.GAME_NO== model.GAME_NO && x.TEMP_USER_ID== model.User.TEMP_USER_ID).FirstOrDefault();
                            T26 = db.ADDT26.Where(X => X.GAME_NO == model.GAME_NO).FirstOrDefault();
                           // T26_D.CASH = Int32.Parse(prize.PrizeName);
                            string LogDesc = string.Empty;
                            string LOG_ID = string.Empty;
                            LogDesc = T26.GAME_NAME + T26_D.LEVEL_NAME;
                            bool IsPayOK = false;
                            if (T27 != null)
                            {
                                IsPayOK = CameAddCash(T27.TEMP_USER_ID, T26.SCHOOL_NO, T26.GAME_NO, T26_D.LEVEL_NO, Int32.Parse(prize.PrizeName), LogDesc, ref Message, ref db, out LOG_ID);
                                
                            }
                        }
                    if (prize.Y_CASH != null && prize.Y_CASH == true)
                    {

                        Message += $@"抽中<font  color='#FF0000'>{ prize.PrizeName}</font>點";
                    }
                    else {
                        Message += $@"抽中<font  color='#FF0000'>{prize.PrizeName}</font>";
                    }
                    }
                    db.LotteryWinners.Add(Cre);
                    db.SaveChanges();
                    model.IsOK = true;
                      // }
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                logger.Error(Message + $" SaveLotteryPrize 錯誤結束GAME_NO={model.GAME_NO}");
                model.IsOK = false;
                    model.Message = Message;
                    return model;
                }
            logger.Error(Message + $" SaveLotteryPrize 結束GAME_NO={model.GAME_NO}");
            //    ts.Complete();
            //}
            model.IsOK = true;
            return model;
        }
        public GameEditViewModel GetEditDataItemPrize(GameEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {

                model.Main = (from a in db.ADDT26
                              where a.GAME_NO == model.Search.WhereGAME_NO
                              select new GameEditMainViewModel()
                              {
                                  SCHOOL_NO = a.SCHOOL_NO,
                                  GAME_NO = a.GAME_NO,
                                  GAME_NAME = a.GAME_NAME,
                                  GAME_IMG = a.GAME_IMG,
                                  GAME_DESC = a.GAME_DESC,
                                  GAME_DATES = a.GAME_DATES,
                                  GAME_DATEE = a.GAME_DATEE,
                                  CRE_PERSON = a.CRE_PERSON,
                                  CRE_DATE = a.CRE_DATE,
                                  CHG_PERSON = a.CHG_PERSON,
                                  CHG_DATE = a.CHG_DATE,
                                  SUBJECT =a.SUBJECT,
                                  LIKE_COUNT = a.LIKE_COUNT,
                                  GAME_TYPE = a.GAME_TYPE ?? (byte)ADDT26.GameType.贈品抽獎,
                              }).AsNoTracking().FirstOrDefault();
                if (model.Main != null)
                {
                    if (!string.IsNullOrWhiteSpace(model.Main.GAME_IMG))
                    {
                        model.Main.GAME_IMG_PATH = GetDirectorySysGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO, model.Main.GAME_IMG);
                    }
                    var SysGamePath = GetSetDirectoryGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO);

                    // 不管一般、有獎徵答 報名跟測試都會在這段裡
                    model.Details = (from a in db.ADDT26_D
                                     where a.GAME_NO == model.Search.WhereGAME_NO
                                     select new GameEditDetailsViewModel()
                                     {
                                         LEVEL_NO = a.LEVEL_NO,
                                         LEVEL_TYPE = a.LEVEL_TYPE,
                                         LEVEL_ITEM = a.LEVEL_ITEM,
                                         GAME_NO = a.GAME_NO,
                                         LEVEL_NAME = a.LEVEL_NAME,
                                         LEVEL_IMG = a.LEVEL_IMG,
                                         LEVEL_DESC = a.LEVEL_DESC,
                                         CASH = a.CASH,
                                         CHG_PERSON = a.CHG_PERSON,
                                         CHG_DATE = a.CHG_DATE,
                                         Y_REPEAT = a.Y_REPEAT ?? false,
                                         Y_CASH=a.Y_CASH ?? false,
                                         LOADING_TIME = a.LOADING_TIME,
                                         PASSED_TIME = a.PASSED_TIME,
                                         Coupons_ITem = a.Coupons_ITem,
                                     }).OrderBy(a => a.LEVEL_ITEM).AsNoTracking().ToList();

                    model.Details = model.Details.Select(
                       a =>
                       {
                           a.LEVEL_IMG_PATH = !string.IsNullOrWhiteSpace(a.LEVEL_IMG) ? UrlCustomHelper.Url_Content(SysGamePath + @"\" + a.LEVEL_IMG) : "";
                           a.IsCouppons = a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize ? true : false;
                           a.IsApply = a.LEVEL_TYPE == ADDT26_D.LevelType.Apply ? true : false;
                           a.IsAgainst = a.LEVEL_TYPE == ADDT26_D.LevelType.Prize ? true : false;
                           return a;
                       }
                   ).ToList();
                    model.PrizeDetails = (from a in db.LotteryPrize
                                          where a.GAME_NO == model.Search.WhereGAME_NO
                                          select new GameLotteryPrizeViewModel()
                                          {   Y_CASH=a.Y_CASH??false,
                                              LEVEL_NO = a.LevelNO,
                                              SUBJECT =a.SUBJECT,
                                              PrizeId = a.PrizeId,
                                              PrizeName = a.PrizeName,
                                              PrizeQty = a.PrizeQty,
                                              PrizeRate = a.PrizeRate,
                                              OrderRank =a.OrderRANK
                                          }
                                     ).OrderBy(a => a.PrizeId).AsNoTracking().ToList();
                    int PrizeDetailsCount = 0;
                    PrizeDetailsCount = model.PrizeDetails.Where(x => x.OrderRank != null).Count();
                    if (PrizeDetailsCount > 0)
                    {

                        model.PrizeDetails = model.PrizeDetails.OrderBy(a => a.OrderRank).ToList();

                    }
                }
                return model;
            }

       }
            /// <summary>
            /// 編輯時帶入資料
            /// </summary>
            /// <param name="model"></param>
            /// <returns></returns>
            public GameEditViewModel GetEditData(GameEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.Main = (from a in db.ADDT26
                              where a.GAME_NO == model.Search.WhereGAME_NO
                              select new GameEditMainViewModel()
                              {
                                  SCHOOL_NO = a.SCHOOL_NO,
                                  GAME_NO = a.GAME_NO,
                                  GAME_NAME = a.GAME_NAME,
                                  GAME_IMG = a.GAME_IMG,
                                  GAME_DESC = a.GAME_DESC,
                                  GAME_DATES = a.GAME_DATES,
                                  GAME_DATEE = a.GAME_DATEE,
                                  CRE_PERSON = a.CRE_PERSON,
                                  CRE_DATE = a.CRE_DATE,
                                  CHG_PERSON = a.CHG_PERSON,
                                  CHG_DATE = a.CHG_DATE,
                                  LIKE_COUNT = a.LIKE_COUNT,
                                  GAME_TYPE = a.GAME_TYPE ?? (byte)ADDT26.GameType.一般,
                              }).AsNoTracking().FirstOrDefault();

                if (model.Main != null)
                {
                    if (!string.IsNullOrWhiteSpace(model.Main.GAME_IMG))
                    {
                        model.Main.GAME_IMG_PATH = GetDirectorySysGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO, model.Main.GAME_IMG);
                    }

                    var SysGamePath = GetSetDirectoryGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO);

                    // 不管一般、有獎徵答 報名跟測試都會在這段裡
                    model.Details = (from a in db.ADDT26_D
                                     where a.GAME_NO == model.Search.WhereGAME_NO
                                     select new GameEditDetailsViewModel()
                                     {
                                         LEVEL_NO = a.LEVEL_NO,
                                         LEVEL_TYPE = a.LEVEL_TYPE,
                                         LEVEL_ITEM = a.LEVEL_ITEM,
                                         GAME_NO = a.GAME_NO,
                                         LEVEL_NAME = a.LEVEL_NAME,
                                         LEVEL_IMG = a.LEVEL_IMG,
                                         LEVEL_DESC = a.LEVEL_DESC,
                                         CASH = a.CASH,
                                         CHG_PERSON = a.CHG_PERSON,
                                         CHG_DATE = a.CHG_DATE,
                                         Y_REPEAT = a.Y_REPEAT ?? false,
                                         LOADING_TIME = a.LOADING_TIME,
                                         PASSED_TIME = a.PASSED_TIME,
                                         Coupons_ITem = a.Coupons_ITem,
                                     }).OrderBy(a => a.LEVEL_ITEM).AsNoTracking().ToList();

                    model.Details = model.Details.Select(
                       a =>
                       {
                           a.LEVEL_IMG_PATH = !string.IsNullOrWhiteSpace(a.LEVEL_IMG) ? UrlCustomHelper.Url_Content(SysGamePath + @"\" + a.LEVEL_IMG) : "";
                           a.IsCouppons = a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize ? true : false;
                           a.IsApply = a.LEVEL_TYPE == ADDT26_D.LevelType.Apply ? true : false;
                           a.IsAgainst = a.LEVEL_TYPE == ADDT26_D.LevelType.Prize ? true : false;
                           return a;
                       }
                   ).ToList();
                    model.PrizeDetails = (from a in db.LotteryPrize
                                          where a.GAME_NO == model.Search.WhereGAME_NO
                                          select new GameLotteryPrizeViewModel()
                                          {
                                              LEVEL_NO = a.LevelNO,
                                              PrizeId = a.PrizeId,
                                              PrizeName = a.PrizeName,
                                              PrizeQty = a.PrizeQty,
                                              PrizeRate = a.PrizeRate,
                                          }
                                        ).OrderBy(a => a.PrizeId).AsNoTracking().ToList();
                    if ((model.Main.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答))
                    {
                        var Ans = (from a in db.ADDT26_D
                                   where a.GAME_NO == model.Search.WhereGAME_NO
                                   select new GameEditDetailsAnsLevelViewModel()
                                   {
                                       LEVEL_NO = a.LEVEL_NO,
                                       LEVEL_TYPE = a.LEVEL_TYPE,
                                       LEVEL_ITEM = a.LEVEL_ITEM,
                                       GAME_NO = a.GAME_NO,
                                       LEVEL_NAME = a.LEVEL_NAME,
                                       LEVEL_IMG = a.LEVEL_IMG,
                                       LEVEL_DESC = a.LEVEL_DESC,
                                       CASH = a.CASH,
                                       CHG_PERSON = a.CHG_PERSON,
                                       CHG_DATE = a.CHG_DATE,
                                       Y_REPEAT = a.Y_REPEAT ?? false,
                                     
                                       LOADING_TIME = a.LOADING_TIME,
                                       PASSED_TIME = a.PASSED_TIME,
                                       GROUP_ID = a.GROUP_ID,
                                       RETURN_DESC = a.RETURN_DESC,
                                       TRUE_ANS = a.TRUE_ANS ?? false,
                                   }).OrderBy(a => a.LEVEL_ITEM).AsNoTracking().ToList();

                        Ans = Ans.Select(
                         a =>
                         {
                             a.LEVEL_IMG_PATH = !string.IsNullOrWhiteSpace(a.LEVEL_IMG) ? UrlCustomHelper.Url_Content(SysGamePath + @"\" + a.LEVEL_IMG) : "";
                             return a;
                         }).ToList();

                        var Q_T = (from a in db.ADDT26_Q
                                   where a.GAME_NO == model.Search.WhereGAME_NO
                                   select new GameEditDetailsQAViewModel()
                                   {
                                       GROUP_ID = a.GROUP_ID,
                                       G_SUBJECT = a.G_SUBJECT,
                                       G_ORDER_BY = a.G_ORDER_BY,
                                   }).OrderBy(a => a.G_ORDER_BY).AsNoTracking().ToList();

                        Q_T = Q_T.Select(
                        a =>
                        {
                            a.Y_REPEAT = Ans.Where(b => b.GROUP_ID == a.GROUP_ID).Select(b => b.Y_REPEAT).FirstOrDefault();
                            a.LOADING_TIME = Ans.Where(b => b.GROUP_ID == a.GROUP_ID).Select(b => b.LOADING_TIME).FirstOrDefault();
                            a.PASSED_TIME = Ans.Where(b => b.GROUP_ID == a.GROUP_ID).Select(b => b.PASSED_TIME).FirstOrDefault();
                            a.AnsLeve = Ans.Where(b => b.GROUP_ID == a.GROUP_ID).ToList();
                            a.InputType = Ans.Where(b => b.GROUP_ID == a.GROUP_ID).Count() == 2 ? "OX" : "Select";
                            return a;
                        }).ToList();

                        model.DetailsQA = Q_T;
                    }
                }

                return model;
            }
        }

        public BackupLinkViewModel BackupLinkData(BackupLinkViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.BackupLinkInfo = (from a in Db.ADDT26_BackupLink
                                        where a.GAME_NO == model.Search.WhereGAME_NO
                                        select new BackupLinkInfoViewModel()
                                        {
                                            GAME_NO = a.GAME_NO,
                                            BACKUP_ID = a.BACKUP_ID,
                                            BACKUP_NAME = a.BACKUP_NAME,
                                            BACKUP_URL = a.BACKUP_URL,
                                            IS_ENABLE = a.IS_ENABLE ?? true,
                                        }).OrderBy(a => a.BACKUP_ID).ToList();

                if ((model.BackupLinkInfo?.Count() ?? 0) == 0)
                {
                    BackupLinkInfoViewModel Info = new BackupLinkInfoViewModel();
                    Info.GAME_NO = model.Search.WhereGAME_NO;
                    Info.BACKUP_ID = 1;
                    Info.BACKUP_NAME = "正式區";
                    Info.BACKUP_URL = UrlCustomHelper.GetOwnWebUri();
                    Info.IS_ENABLE = true;
                    model.BackupLinkInfo.Add(Info);
                }

                return model;
            }
        }

        public GameRewardViewModel GetRewardData(GameRewardViewModel model, ref ECOOL_DEVEntities Db)
        {
            GameRewardViewModel gameRewardViewModel;

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.RewardInfo = (
                    from a in Db.ADDT26_R
                    where a.GAME_NO == model.Search.WhereGAME_NO
                    select new GameRewardInfoViewModel()
                    {
                        GAME_NO = a.GAME_NO,
                        ITEM_NO = (byte?)a.ITEM_NO,
                        RATE_S = a.RATE_S,
                        RATE_E = a.RATE_E,
                        REWARD_DESC = a.REWARD_DESC,
                        REWARD_CASH = a.REWARD_CASH
                    } into a
                    orderby a.ITEM_NO
                    select a).ToList<GameRewardInfoViewModel>();

                gameRewardViewModel = model;
            }
            return gameRewardViewModel;
        }

        public bool AutoSaveRewardDat(string GAME_NO, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            try
            {
                List<ADDT26_R> RewardInfo = new List<ADDT26_R>();

                RewardInfo.Add(new ADDT26_R()
                {
                    GAME_NO = GAME_NO,
                    ITEM_NO = 1,
                    RATE_S = 0,
                    RATE_E = 60,
                    REWARD_DESC = "答錯太多，請認真看題目。",
                    REWARD_CASH = 0,
                    CHG_PERSON = user.USER_KEY,
                    CHG_DATE = new DateTime?(DateTime.Now)
                });

                RewardInfo.Add(new ADDT26_R()
                {
                    GAME_NO = GAME_NO,
                    ITEM_NO = 2,
                    RATE_S = (decimal)60.01,
                    RATE_E = (decimal)99.99,
                    REWARD_DESC = "可惜，錯了一些，請再加油喔。",
                    REWARD_CASH = 0,
                    CHG_PERSON = user.USER_KEY,
                    CHG_DATE = new DateTime?(DateTime.Now)
                });

                RewardInfo.Add(new ADDT26_R()
                {
                    GAME_NO = GAME_NO,
                    ITEM_NO = 3,
                    RATE_S = 100,
                    RATE_E = 100,
                    REWARD_DESC = "全部答對！你太棒了，給你按個讚。",
                    REWARD_CASH = 0,
                    CHG_PERSON = user.USER_KEY,
                    CHG_DATE = new DateTime?(DateTime.Now)
                });
                Db.ADDT26_R.AddRange(RewardInfo);
            //    EFBatchOperation.For(Db, Db.ADDT26_R).InsertAll(RewardInfo);
            }
            catch (Exception ex)
            {
                Message = string.Concat("系統發生錯誤;原因:", ex.Message);
                return false;
            }

            return true;
        }

        public bool SaveRewardData(GameRewardViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                Db.ADDT26_R.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                if ((model.RewardInfo?.Count() ?? 0) > 0)
                {
                    List<ADDT26_R> listR = new List<ADDT26_R>();
                    int num = 1;

                    foreach (GameRewardInfoViewModel item in model.RewardInfo)
                    {
                        ADDT26_R aDDT26R = new ADDT26_R()
                        {
                            GAME_NO = model.Search.WhereGAME_NO,
                            ITEM_NO = (byte)num,
                            RATE_S = item.RATE_S,
                            RATE_E = item.RATE_E,
                            REWARD_DESC = item.REWARD_DESC,
                            REWARD_CASH = item.REWARD_CASH,
                            CHG_PERSON = user.USER_KEY,
                            CHG_DATE = new DateTime?(DateTime.Now)
                        };

                        var checkDb = listR.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.ITEM_NO != (byte)num
                           &&
                             (
                                  (item.RATE_S >= a.RATE_S && item.RATE_S <= a.RATE_E)
                               || (item.RATE_E >= a.RATE_S && item.RATE_E <= a.RATE_E)
                             )
                           ).ToList();

                        if ((checkDb?.Count() ?? 0) > 0)
                        {
                            string CheckStr = string.Empty;

                            foreach (var checkItem in checkDb)
                            {
                                CheckStr = string.Format("{0}~{1}，比例區間值有重覆", checkItem.RATE_S, checkItem.RATE_E) + Environment.NewLine;

                                if (Message.IndexOf(CheckStr) == -1)
                                {
                                    Message = Message + CheckStr;
                                }
                            }

                            CheckStr = string.Format("{0}~{1}，比例區間值有重覆", item.RATE_S, item.RATE_E) + Environment.NewLine;

                            if (Message.IndexOf(CheckStr) == -1)
                            {
                                Message = Message + CheckStr;
                            }
                        }

                        num = num + 1;
                        listR.Add(aDDT26R);
                        Db.ADDT26_R.Add(aDDT26R);
                    }
                   
                  //  EFBatchOperation.For(Db, Db.ADDT26_R).InsertAll(listR);
                }
                if (string.IsNullOrWhiteSpace(Message))
                {
                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception exception)
                    {
                        Message = string.Concat("系統發生錯誤;原因:", exception.Message);
                        return false;
                    }
                    tx.Complete();
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        public bool SaveBackupLinkData(BackupLinkViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                Db.ADDT26_BackupLink.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                if ((model.BackupLinkInfo?.Count ?? 0) > 0)
                {
                    List<ADDT26_BackupLink> listBackup = new List<ADDT26_BackupLink>();

                    int Item = 1;

                    foreach (var item in model.BackupLinkInfo)
                    {
                        ADDT26_BackupLink CreD = new ADDT26_BackupLink();
                        CreD.GAME_NO = model.Search.WhereGAME_NO;
                        CreD.BACKUP_ID = Item;
                        CreD.BACKUP_NAME = item.BACKUP_NAME;
                        CreD.BACKUP_URL = item.BACKUP_URL;
                        CreD.IS_ENABLE = item.IS_ENABLE;
                        Item++;
                        listBackup.Add(CreD);
                        Db.ADDT26_BackupLink.Add(CreD);
                    }

                  //  EFBatchOperation.For(Db, Db.ADDT26_BackupLink).InsertAll(listBackup);
                }

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }

        public bool SaveEditPersonData(GameEditPersonViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                ADDT27 SaveUp = null;

                SaveUp = Db.ADDT27.Where(a => a.TEMP_USER_ID == model.Person.TEMP_USER_ID).FirstOrDefault();

                if (SaveUp != null)
                {
                    if (model.Person.GAME_USER_ID.Length != 10)
                    {
                        Message = "系統發生錯誤;原因:卡號不符合10碼";
                        return false;
                    }

                    SaveUp.GAME_USER_ID = model.Person.GAME_USER_ID;
                    SaveUp.NAME = model.Person.NAME;
                    SaveUp.SNAME = model.Person.NAME;
                    SaveUp.PHONE = model.Person.PHONE;
                    SaveUp.CHG_PERSON = user.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;
                    SaveUp.GAME_USER_TYPE_DESC = model.Person.GAME_USER_TYPE_DESC;
                }
                else
                {
                    Message = "系統發生錯誤;原因:找不到此筆帳號";
                    return false;
                }

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }

        /// <summary>
        /// 存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveEditData1(GameEditViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                ADDT26 SaveUp = null;

                SaveUp = Db.ADDT26.Where(a => a.GAME_NO == model.Main.GAME_NO).FirstOrDefault();

                if (model.Main.LIKE_COUNT == null || model.Main.LIKE_COUNT <= 0)
                {
                    model.Main.LIKE_COUNT = 30;
                }

                if (SaveUp == null)
                {
                    SaveUp = new ADDT26
                    {
                        GAME_NO = Guid.NewGuid().ToString("N"),
                        GAME_NAME = model.Main.GAME_NAME,
                        GAME_DESC = model.Main.GAME_DESC,
                        GAME_DATES = model.Main.GAME_DATES,
                        GAME_DATEE = model.Main.GAME_DATEE,
                        CRE_PERSON = user.USER_KEY,
                        CRE_DATE = DateTime.Now,
                        CHG_PERSON = user.USER_KEY,
                        CHG_DATE = DateTime.Now,
                        SUBJECT = model.Main.SUBJECT,
                        SCHOOL_NO = user.SCHOOL_NO,
                        LIKE_COUNT = model.Main.LIKE_COUNT,
                        GAME_TYPE = model.GAME_TYPE ?? (byte)ADDT26.GameType.贈品抽獎,
                    };

                    if (model.Main.UploadGamerFile?.ContentLength > 0)
                    {
                        SaveUp.GAME_IMG = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, model.Main.UploadGamerFile, ref Message);

                        if (string.IsNullOrWhiteSpace(Message) == false)
                        {
                            return false;
                        }
                    }

                    Db.ADDT26.Add(SaveUp);


                }
                else
                {
                    SaveUp.GAME_NAME = model.Main.GAME_NAME;
                    SaveUp.GAME_DESC = model.Main.GAME_DESC;
                    SaveUp.GAME_DATES = model.Main.GAME_DATES;
                    SaveUp.GAME_DATEE = model.Main.GAME_DATEE;
                    SaveUp.CHG_PERSON = user.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;
                    SaveUp.LIKE_COUNT = model.Main.LIKE_COUNT;
                    SaveUp.SUBJECT = model.Main.SUBJECT;
                    if (model.Main.UploadGamerFile?.ContentLength > 0)
                    {
                        if (!string.IsNullOrWhiteSpace(SaveUp.GAME_IMG))
                        {
                            DelFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, SaveUp.GAME_IMG, ref Message);
                            if (string.IsNullOrWhiteSpace(Message) == false)
                            {
                                return false;
                            }
                        }

                        SaveUp.GAME_IMG = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, model.Main.UploadGamerFile, ref Message);

                        if (string.IsNullOrWhiteSpace(Message) == false)
                        {
                            return false;
                        }
                    }
                    List<string> PrizeDetailsItem = new List<string>();
                    PrizeDetailsItem = model.PrizeDetails.Select(x => x.PrizeId).ToList();
                    Db.ADDT26_D.Where(a => a.GAME_NO == model.Main.GAME_NO).Delete();
                    Db.ADDT26_Q.Where(a => a.GAME_NO == model.Main.GAME_NO).Delete();
                    Db.LotteryPrize.Where(a => a.GAME_NO == model.Main.GAME_NO && !PrizeDetailsItem.Contains(a.PrizeId)).Delete();
                }

                GenerateQR(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, "BuskerLikeView");

                int LEVEL_ITEM = 0;

                StringHelper stringHelper = new StringHelper();

                //// 不管一般、有獎徵答 報名跟測試都會在這段裡
                if (model.Details.Count > 0)
                {
                    List<ADDT26_D> listADDT26 = new List<ADDT26_D>();
                    List<LotteryPrize> listLotteryPrize = new List<LotteryPrize>();
                    List < LotteryPrize > ModitifyLotteryPrize = new List<LotteryPrize>();
                    ////若是之後有需要可以打開這邊來改老師控管點數
                    //if (model.PrizeDetails.Count() > 0)
                    //{
                    //    if (model.Details.Where(x => x.IsCouppons == true).Count() > 0) {

                    //        int totalAmont = 0;
                    //        totalAmont = model.PrizeDetails.Where(x => x.Y_CASH == true && x.PrizeName!=null&&　x.PrizeName!="").Sum(x => Int32.Parse(x.PrizeName));

                    //        //檢查給點教師的限制
                    //        //每月給點上限：綁的程式是
                    //        //一、批次加扣點裡面的「特殊加扣點」、「班級小幫手」
                    //        //二、即時加點的「特殊加點--固定」、「特殊加點--隨機」
                    //        string ErrMsg;
                    //        short ThisMonthCash = 0;
                    //        int CashTotal = 0;
                    //        int CashLimit = 0;

                    //        if (model.Main.SUBJECT.Contains("批次特殊加扣點") || model.Main.SUBJECT.Contains("即時加點特殊加扣點") || model.Main.SUBJECT.Contains("特殊加扣點")
                    //          || model.Main.SUBJECT.Contains("批次校內表現班級小幫手") || model.Main.SUBJECT.Contains("批次校內表現班級幫手和榮譽")
                    //          || model.Main.SUBJECT.Contains("校內表現-班級幫手和榮譽") || model.Main.SUBJECT.Contains("班級幫手和榮譽") || model.Main.SUBJECT.Contains("校內表現-班級服務")
                    //             || model.Main.SUBJECT.Contains("批次快速大量加點-特殊加扣點") || model.Main.SUBJECT.Contains("特殊加扣點-小獎勵(班級加點，受點數控管)") || model.Main.SUBJECT.Contains("小獎勵(班級加點，受點數控管") || model.Main.SUBJECT.Contains("班級幫手和榮譽") || model.Main.SUBJECT.Contains("班級服務") )
                    //        {
                    //            CashLimit = UserProfile.GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref Db, out ThisMonthCash, out ErrMsg);
                    //            if (string.IsNullOrWhiteSpace(ErrMsg) == false)
                    //            {

                    //                if ((CashTotal + ThisMonthCash) > CashLimit)
                    //                {
                    //                    Message = "本次給點將超過本月給點限制：" + CashLimit.ToString() + "。您本月已發出點數：" + ThisMonthCash.ToString();

                    //                    return false;
                    //                }


                    //            }
                    //        }


                    //    }


                    //}
                    ////若是之後有需要可以打開這邊來改老師控管點數
                    foreach (var item in model.Details)
                    {
                        ADDT26_D CreD = new ADDT26_D();

                        LEVEL_ITEM++;
                        if (item.IsCouppons)
                        {
                            CreD.LEVEL_TYPE = ADDT26_D.LevelType.ItemPrize;
                        }


                        CreD.LEVEL_NO = string.IsNullOrWhiteSpace(item.LEVEL_NO) ? Guid.NewGuid().ToString("N") : item.LEVEL_NO;
                        CreD.LEVEL_TYPE = CreD.LEVEL_TYPE;
                        CreD.LEVEL_ITEM = stringHelper.StrRigth("00" + LEVEL_ITEM.ToString(), 3);
                        CreD.GAME_NO = SaveUp.GAME_NO;
                        CreD.LEVEL_NAME = item.LEVEL_NAME;
                        CreD.LEVEL_IMG = item.LEVEL_IMG;
                        CreD.LEVEL_DESC = item.LEVEL_DESC;
                        CreD.Coupons_ITem = item.Coupons_ITem;
                        CreD.CASH = item.CASH ?? 0;
                        CreD.CHG_PERSON = user.USER_KEY;
                        CreD.CHG_DATE = DateTime.Now;


                        CreD.LOADING_TIME = item.LOADING_TIME ?? 1;
                        CreD.PASSED_TIME = item.PASSED_TIME ?? 3;
                        CreD.Y_REPEAT = item.Y_REPEAT;
                        CreD.Y_CASH = item.Y_CASH;
                        if (item.PhotoFiles?.ContentLength > 0)
                        {
                            if (!string.IsNullOrWhiteSpace(item.LEVEL_IMG))
                            {
                                DelFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, item.LEVEL_IMG, ref Message);
                                if (string.IsNullOrWhiteSpace(Message) == false)
                                {
                                    return false;
                                }
                            }

                            CreD.LEVEL_IMG = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, item.PhotoFiles, ref Message, CreD.LEVEL_ITEM);

                            if (string.IsNullOrWhiteSpace(Message) == false)
                            {
                                Message = $"關卡「{item.LEVEL_NAME}」，" + Message;
                                return false;
                            }
                        }
                        if (CreD.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize)
                        {

                          
                            int LotteryPrizeCount = 0;
                            LotteryPrizeCount = Db.LotteryPrize.Where(a => a.GAME_NO == model.Main.GAME_NO).Count();
                            if (LotteryPrizeCount > 0)
                            {
                                int i = 0;
                                
                                foreach (var Prizeitem in model.PrizeDetails.Where(x => x.LEVEL_NO == item.Coupons_ITem).ToList())
                                {
                                    int Pricecount = 0;
                                    Pricecount = Db.LotteryPrize.Where(x => x.PrizeId == Prizeitem.PrizeId).Count();
                                   
                                    if (Pricecount > 0)
                                    {
                                        LotteryPrize lotteryPrizeItem = new LotteryPrize();
                                        lotteryPrizeItem = Db.LotteryPrize.Where(x => x.PrizeId == Prizeitem.PrizeId && x.GAME_NO == SaveUp.GAME_NO).FirstOrDefault();
                                        lotteryPrizeItem.GAME_NO = SaveUp.GAME_NO;
                                        lotteryPrizeItem.SUBJECT = SaveUp.SUBJECT;
                                        lotteryPrizeItem.PrizeName = Prizeitem.PrizeName;
                                        lotteryPrizeItem.PrizeQty = Prizeitem.PrizeQty;
                                        lotteryPrizeItem.FixQty = Prizeitem.PrizeQty;
                                        lotteryPrizeItem.PrizeRate = Prizeitem.PrizeRate == null ? Prizeitem.PrizeQty : Prizeitem.PrizeRate;
                                        lotteryPrizeItem.UpdateUserID = user.USER_KEY;
                                        lotteryPrizeItem.UpdateDate = DateTime.Now;
                                        lotteryPrizeItem.LevelNO = Prizeitem.LEVEL_NO;
                                        lotteryPrizeItem.Y_CASH = Prizeitem.Y_CASH;
                                        lotteryPrizeItem.OrderRANK = i;
                                        ModitifyLotteryPrize.Add(lotteryPrizeItem);
                                        Db.Entry(lotteryPrizeItem).State = System.Data.Entity.EntityState.Modified;
                                        i++;
                                    }
                                    else {

                                        LotteryPrize lotteryPrizeItem = new LotteryPrize();
                                        lotteryPrizeItem.GAME_NO = SaveUp.GAME_NO;
                                        lotteryPrizeItem.PrizeId = string.IsNullOrWhiteSpace(Prizeitem.PrizeId) ? Guid.NewGuid().ToString("N") : Prizeitem.PrizeId;
                                        lotteryPrizeItem.PrizeName = Prizeitem.PrizeName;
                                        lotteryPrizeItem.FixQty = Prizeitem.PrizeQty;
                                        lotteryPrizeItem.PrizeQty = Prizeitem.PrizeQty;
                                        lotteryPrizeItem.PrizeRate = Prizeitem.PrizeRate == null ? Prizeitem.PrizeQty : Prizeitem.PrizeRate;
                                        lotteryPrizeItem.UpdateUserID = user.USER_KEY;
                                        lotteryPrizeItem.UpdateDate = DateTime.Now;
                                        lotteryPrizeItem.SUBJECT = SaveUp.SUBJECT;
                                        lotteryPrizeItem.LevelNO = Prizeitem.LEVEL_NO;
                                        lotteryPrizeItem.Y_CASH = Prizeitem.Y_CASH;
                                        lotteryPrizeItem.OrderRANK = i;
                                        listLotteryPrize.Add(lotteryPrizeItem);
                                        Db.LotteryPrize.Add(lotteryPrizeItem);
                                        i++;
                                    }
                                }

                            }
                            else {
                                int i = 0;
                                foreach (var Prizeitem in model.PrizeDetails.Where(x => x.LEVEL_NO == item.Coupons_ITem).ToList())
                            {
                                LotteryPrize lotteryPrizeItem = new LotteryPrize();
                                lotteryPrizeItem.GAME_NO = SaveUp.GAME_NO;
                                lotteryPrizeItem.PrizeId = string.IsNullOrWhiteSpace(Prizeitem.PrizeId) ? Guid.NewGuid().ToString("N") : Prizeitem.PrizeId;
                                lotteryPrizeItem.PrizeName = Prizeitem.PrizeName;
                                    lotteryPrizeItem.SUBJECT = SaveUp.SUBJECT;
                                    lotteryPrizeItem.PrizeQty = Prizeitem.PrizeQty;
                                    lotteryPrizeItem.FixQty = Prizeitem.PrizeQty;
                                    lotteryPrizeItem.PrizeRate = Prizeitem.PrizeRate == null ? Prizeitem.PrizeQty : Prizeitem.PrizeRate;
                                lotteryPrizeItem.UpdateUserID = user.USER_KEY;
                                lotteryPrizeItem.UpdateDate = DateTime.Now;
                                lotteryPrizeItem.LevelNO = Prizeitem.LEVEL_NO;
                                lotteryPrizeItem.Y_CASH = Prizeitem.Y_CASH;
                                    lotteryPrizeItem.OrderRANK = i;
                                    Db.LotteryPrize.Add(lotteryPrizeItem);
                                listLotteryPrize.Add(lotteryPrizeItem);
                                    i++;
                                }
                            }
                        }
                        listADDT26.Add(CreD);
                        Db.ADDT26_D.Add(CreD);
                    }

                   // EFBatchOperation.For(Db, Db.ADDT26_D).InsertAll(listADDT26);
                   // EFBatchOperation.For(Db, Db.LotteryPrize).InsertAll(listLotteryPrize);
                  //  EFBatchOperation.For(Db, Db.LotteryPrize).UpdateAll(ModitifyLotteryPrize, x => x.ColumnsToUpdate(c => c.PrizeName,c=>c.PrizeQty,c=>c.UpdateDate, c => c.UpdateUserID, c => c.LevelNO, c => c.Y_CASH));

                }



                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }
        /// <summary>
        /// 存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveEditData(GameEditViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                ADDT26 SaveUp = null;

                SaveUp = Db.ADDT26.Where(a => a.GAME_NO == model.Main.GAME_NO).FirstOrDefault();

                if (model.Main.LIKE_COUNT == null || model.Main.LIKE_COUNT <= 0)
                {
                    model.Main.LIKE_COUNT = 30;
                }

                if (SaveUp == null)
                {
                    SaveUp = new ADDT26
                    {
                        GAME_NO = Guid.NewGuid().ToString("N"),
                        GAME_NAME = model.Main.GAME_NAME,
                        GAME_DESC = model.Main.GAME_DESC,
                        GAME_DATES = model.Main.GAME_DATES,
                        GAME_DATEE = model.Main.GAME_DATEE.Value.AddHours(23).AddMinutes(59).AddMilliseconds(59),
                        CRE_PERSON = user.USER_KEY,
                        CRE_DATE = DateTime.Now,
                        CHG_PERSON = user.USER_KEY,
                        CHG_DATE = DateTime.Now,
                        SCHOOL_NO = user.SCHOOL_NO,
                        LIKE_COUNT = model.Main.LIKE_COUNT,
                        GAME_TYPE = model.GAME_TYPE ?? (byte)ADDT26.GameType.一般,
                    };

                    if (model.Main.UploadGamerFile?.ContentLength > 0)
                    {
                        SaveUp.GAME_IMG = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, model.Main.UploadGamerFile, ref Message);

                        if (string.IsNullOrWhiteSpace(Message) == false)
                        {
                            return false;
                        }
                    }

                    Db.ADDT26.Add(SaveUp);

                    if (SaveUp.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                    {
                        AutoSaveRewardDat(SaveUp.GAME_NO, user, ref Db, ref Message);

                        if (string.IsNullOrWhiteSpace(Message) == false)
                        {
                            return false;
                        }
                    }
                }
                else
                {
                    SaveUp.GAME_NAME = model.Main.GAME_NAME;
                    SaveUp.GAME_DESC = model.Main.GAME_DESC;
                    SaveUp.GAME_DATES = model.Main.GAME_DATES;
                    SaveUp.GAME_DATEE = model.Main.GAME_DATEE.Value.AddHours(23).AddMinutes(59).AddMilliseconds(59);
                    SaveUp.CHG_PERSON = user.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;
                    SaveUp.LIKE_COUNT = model.Main.LIKE_COUNT;

                    if (model.Main.UploadGamerFile?.ContentLength > 0)
                    {
                        if (!string.IsNullOrWhiteSpace(SaveUp.GAME_IMG))
                        {
                            DelFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, SaveUp.GAME_IMG, ref Message);
                            if (string.IsNullOrWhiteSpace(Message) == false)
                            {
                                return false;
                            }
                        }

                        SaveUp.GAME_IMG = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, model.Main.UploadGamerFile, ref Message);

                        if (string.IsNullOrWhiteSpace(Message) == false)
                        {
                            return false;
                        }
                    }

                    Db.ADDT26_D.Where(a => a.GAME_NO == model.Main.GAME_NO).Delete();
                    Db.ADDT26_Q.Where(a => a.GAME_NO == model.Main.GAME_NO).Delete();
                    Db.LotteryPrize.Where(a => a.GAME_NO == model.Main.GAME_NO).Delete();
                }

                GenerateQR(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, "BuskerLikeView");

                int LEVEL_ITEM = 0;

                StringHelper stringHelper = new StringHelper();

                //// 不管一般、有獎徵答 報名跟測試都會在這段裡
                if (model.Details.Count > 0)
                {
                    List<ADDT26_D> listADDT26 = new List<ADDT26_D>();
                    List<LotteryPrize> listLotteryPrize = new List<LotteryPrize>();
                    foreach (var item in model.Details)
                    {
                        ADDT26_D CreD = new ADDT26_D();

                        LEVEL_ITEM++;
                        if (item.IsCouppons)
                        {
                            CreD.LEVEL_TYPE = ADDT26_D.LevelType.ItemPrize;
                        }
                        else if (item.IsAgainst)
                        {
                            CreD.LEVEL_TYPE = ADDT26_D.LevelType.Prize;
                        }
                        else
                        {
                            if (string.IsNullOrWhiteSpace(item.LEVEL_TYPE))
                            {
                                CreD.LEVEL_TYPE = item.IsApply ? ADDT26_D.LevelType.Apply : ADDT26_D.LevelType.Pay;
                            }
                            else
                            {
                                if (item.LEVEL_TYPE == ADDT26_D.LevelType.Apply || item.LEVEL_TYPE == ADDT26_D.LevelType.Test)
                                {
                                    CreD.LEVEL_TYPE = item.LEVEL_TYPE;
                                }
                                else
                                {
                                    CreD.LEVEL_TYPE = ADDT26_D.LevelType.Pay;
                                }
                            }
                        }

                        CreD.LEVEL_NO = string.IsNullOrWhiteSpace(item.LEVEL_NO) ? Guid.NewGuid().ToString("N") : item.LEVEL_NO;
                        CreD.LEVEL_TYPE = CreD.LEVEL_TYPE;
                        CreD.LEVEL_ITEM = stringHelper.StrRigth("00" + LEVEL_ITEM.ToString(), 3);
                        CreD.GAME_NO = SaveUp.GAME_NO;
                        CreD.LEVEL_NAME = item.LEVEL_NAME;
                        CreD.LEVEL_IMG = item.LEVEL_IMG;
                        CreD.LEVEL_DESC = item.LEVEL_DESC;
                        CreD.Coupons_ITem = item.Coupons_ITem;
                        CreD.CASH = item.CASH ?? 0;
                        CreD.CHG_PERSON = user.USER_KEY;
                        CreD.CHG_DATE = DateTime.Now;

                        if (CreD.LEVEL_TYPE == ADDT26_D.LevelType.Apply)
                        {
                            item.Y_REPEAT = false;
                        }
                        else if (CreD.LEVEL_TYPE == ADDT26_D.LevelType.Test)
                        {
                            item.Y_REPEAT = true;
                        }

                        CreD.LOADING_TIME = item.LOADING_TIME ?? 1;
                        CreD.PASSED_TIME = item.PASSED_TIME ?? 3;
                        CreD.Y_REPEAT = item.Y_REPEAT;
                        CreD.Y_Photo = item.Y_Photo;
                        if (item.PhotoFiles?.ContentLength > 0)
                        {
                            if (!string.IsNullOrWhiteSpace(item.LEVEL_IMG))
                            {
                                DelFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, item.LEVEL_IMG, ref Message);
                                if (string.IsNullOrWhiteSpace(Message) == false)
                                {
                                    return false;
                                }
                            }

                            CreD.LEVEL_IMG = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, item.PhotoFiles, ref Message, CreD.LEVEL_ITEM);

                            if (string.IsNullOrWhiteSpace(Message) == false)
                            {
                                Message = $"關卡「{item.LEVEL_NAME}」，" + Message;
                                return false;
                            }
                        }
                        if (CreD.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize)
                        {
                            foreach (var Prizeitem in model.PrizeDetails.Where(x => x.LEVEL_NO == item.Coupons_ITem).ToList())
                            {
                                LotteryPrize lotteryPrizeItem = new LotteryPrize();
                                lotteryPrizeItem.GAME_NO = SaveUp.GAME_NO;
                                lotteryPrizeItem.PrizeId = string.IsNullOrWhiteSpace(Prizeitem.PrizeId) ? Guid.NewGuid().ToString("N") : Prizeitem.PrizeId;
                                lotteryPrizeItem.PrizeName = Prizeitem.PrizeName;
                                lotteryPrizeItem.PrizeQty = Prizeitem.PrizeQty;
                                lotteryPrizeItem.PrizeRate = Prizeitem.PrizeRate == null ? Prizeitem.PrizeQty : Prizeitem.PrizeRate;
                                lotteryPrizeItem.UpdateUserID = user.USER_KEY;
                                lotteryPrizeItem.UpdateDate = DateTime.Now;
                                lotteryPrizeItem.LevelNO = Prizeitem.LEVEL_NO;
                                listLotteryPrize.Add(lotteryPrizeItem);
                                Db.LotteryPrize.Add(lotteryPrizeItem);
                            }
                        }
                        listADDT26.Add(CreD);
                        Db.ADDT26_D.Add(CreD);
                    }

                    //EFBatchOperation.For(Db, Db.ADDT26_D).InsertAll(listADDT26);
                    //EFBatchOperation.For(Db, Db.LotteryPrize).InsertAll(listLotteryPrize);
                }

                if (SaveUp.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                {
                    if (model.DetailsQA.Count > 0)
                    {
                        List<ADDT26_D> listADDT26s = new List<ADDT26_D>();
                        List<ADDT26_Q> listADDT2_Q = new List<ADDT26_Q>();

                        int OrderNum = 1;

                        foreach (var item in model.DetailsQA)
                        {
                            ADDT26_Q Cre = new ADDT26_Q
                            {
                                GAME_NO = SaveUp.GAME_NO,
                                GROUP_ID = string.IsNullOrWhiteSpace(item.GROUP_ID) ? Guid.NewGuid().ToString("N") : item.GROUP_ID,
                                G_SUBJECT = item.G_SUBJECT,
                                G_ORDER_BY = OrderNum,
                            };

                            LEVEL_ITEM = 1;

                            foreach (var Ans in item.AnsLeve)
                            {
                                ADDT26_D CreD = new ADDT26_D();

                                CreD.LEVEL_NO = string.IsNullOrWhiteSpace(Ans.LEVEL_NO) ? Guid.NewGuid().ToString("N") : Ans.LEVEL_NO;
                                CreD.LEVEL_TYPE = ADDT26_D.LevelType.Pay;
                                CreD.LEVEL_ITEM = stringHelper.StrRigth("00" + LEVEL_ITEM.ToString(), 3);
                                CreD.GAME_NO = SaveUp.GAME_NO;
                                CreD.LEVEL_NAME = Ans.LEVEL_NAME;
                                CreD.LEVEL_IMG = Ans.LEVEL_IMG;
                                CreD.LEVEL_DESC = Ans.LEVEL_DESC;
                                CreD.CASH = Ans.CASH ?? 0;
                                CreD.CHG_PERSON = user.USER_KEY;
                                CreD.CHG_DATE = DateTime.Now;
                                CreD.Y_REPEAT = item.Y_REPEAT;
                                CreD.LOADING_TIME = item.LOADING_TIME ?? 1;
                                CreD.PASSED_TIME = item.PASSED_TIME ?? 3;
                                CreD.GROUP_ID = Cre.GROUP_ID;
                                CreD.RETURN_DESC = Ans.RETURN_DESC;
                                CreD.TRUE_ANS = Ans.TRUE_ANS;

                                if (Ans.PhotoFiles?.ContentLength > 0)
                                {
                                    if (!string.IsNullOrWhiteSpace(Ans.LEVEL_IMG))
                                    {
                                        DelFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, Ans.LEVEL_IMG, ref Message);
                                        if (string.IsNullOrWhiteSpace(Message) == false)
                                        {
                                            return false;
                                        }
                                    }

                                    CreD.LEVEL_IMG = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.GAME_NO, Ans.PhotoFiles, ref Message, CreD.LEVEL_ITEM);

                                    if (string.IsNullOrWhiteSpace(Message) == false)
                                    {
                                        Message = $"題目{item.G_SUBJECT}，選項「{Ans.LEVEL_NAME}」，" + Message;
                                        return false;
                                    }
                                }

                                LEVEL_ITEM++;

                                listADDT26s.Add(CreD);
                                Db.ADDT26_D.Add(CreD);
                            }

                            OrderNum++;

                            listADDT2_Q.Add(Cre);
                            Db.ADDT26_Q.Add(Cre);
                        }

                      //  EFBatchOperation.For(Db, Db.ADDT26_D).InsertAll(listADDT26s);
                       // EFBatchOperation.For(Db, Db.ADDT26_Q).InsertAll(listADDT2_Q);
                    }
                }

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }
       
         public GameLeveViewModel CheckInfo(GameLeveViewModel model, ref ECOOL_DEVEntities Db, ref string Message)
        {
            //檢查是否重複關卡
            ADDT26 T26 = null;
            ADDT27 T27 = null;
            int LogCount = 0;
            int? UNLOG_CASH_AVAILABLE = 0;
            string GAME_USER_ID = string.Empty;
            ADDT26_D T26_D = null;
            model.IsOK = true;
            T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).AsNoTracking().FirstOrDefault();

            if (T26 == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆活動單號";
                logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                model.IsOK = false;
                model.Message = Message;
                return model;
            }
            if (!string.IsNullOrWhiteSpace(model.Coupons_ITem))
            {
                T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.GAME_NO && a.LEVEL_NO == model.LEVEL_NO && a.Coupons_ITem == model.Coupons_ITem).AsNoTracking().FirstOrDefault();
            }
            else
            {
                T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.GAME_NO && a.LEVEL_NO == model.LEVEL_NO).AsNoTracking().FirstOrDefault();
            }
            //檢查 使用者卡號是否正常
            GAME_USER_ID = this.GetUserId(T26, model.GameUserID, ref Db, ref Message);
            T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == GAME_USER_ID && a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().FirstOrDefault();
            if (T27 == null)
            {
                Message = "您尚未報到，請先至「報到處」刷卡報到。";
                // logger.Debug(Message + $" GAME_NO={model.GAME_NO};GAME_USER_ID={GAME_USER_ID}");
                model.IsOK = false;
                model.Message = Message;
                return model;
            }
            LogCount = Db.ADDT28.Where(a => a.GAME_NO == T27.GAME_NO && a.SOURCE_NO == T26_D.LEVEL_NO && a.SCHOOL_NO == T27.SCHOOL_NO && a.TEMP_USER_ID == T27.TEMP_USER_ID).AsNoTracking().Count();

            if ((T26_D.Y_REPEAT ?? false) == false && LogCount > 0)
            {
                if (!string.IsNullOrWhiteSpace(T26_D.GROUP_ID))
                {
                    Message = "這一題已作答，請回答其他題目喔！";
                }
                else
                {
                    Message = "您已經通過本關，請繼續挑戰其他關卡喔！";
                }

                model.IsOK = false;
                model.Message = Message;
                return model;
            }
            //檢查點數
            ADDT27 aw27 = Db.ADDT27.Where(u => u.TEMP_USER_ID == T27.TEMP_USER_ID).FirstOrDefault();
            var EntityTemp = ((IObjectContextAdapter)Db).ObjectContext.ObjectStateManager.GetObjectStateEntries(System.Data.Entity.EntityState.Added).Where(a => a.Entity is ADDT27).Select(a => (ADDT27)a.Entity).Where(u => u.TEMP_USER_ID == T27.TEMP_USER_ID).FirstOrDefault();
            if (EntityTemp != null)
            {
                aw27 = EntityTemp;
            }
            if (aw27 != null)
            {
                if (aw27.CASH_ALL.HasValue == false) aw27.CASH_ALL = 0;
                if (aw27.CASH_AVAILABLE.HasValue == false) aw27.CASH_AVAILABLE = 0;
                UNLOG_CASH_AVAILABLE = aw27.CASH_AVAILABLE;
                //累加這次點數
                if (T26_D.CASH > 0)
                {
                    aw27.CASH_ALL = aw27.CASH_ALL.Value + T26_D.CASH;
                }
                else
                {
                    aw27.CASH_ALL = aw27.CASH_ALL.Value;
                }

                aw27.CASH_AVAILABLE = aw27.CASH_AVAILABLE.Value + T26_D.CASH;
                if (aw27.CASH_AVAILABLE < 0)
                {
                    Message = $"餘額不足!您目前點數為{UNLOG_CASH_AVAILABLE}點";
                    model.IsOK = false;
                    model.Message = Message;
                    return model;
                }
            }
            return model;
        }

        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveEditDeleteData(GameEditViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                ADDT26 Del = Db.ADDT26.Where(a => a.GAME_NO == model.Main.GAME_NO).FirstOrDefault();

                if (Del == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                string tempPath = GetSysGamePath(Del.SCHOOL_NO, Del.GAME_NO);

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.Delete(tempPath, true);
                }

                var listT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).ToList();

                if (listT27.Count() > 0)
                {
                    Db.ADDT28.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                    Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                    (from a in Db.ADDT30
                     join b in Db.ADDT29 on a.TITLE_SHOW_ID equals b.TITLE_SHOW_ID
                     where b.GAME_NO == model.Search.WhereGAME_NO
                     select a).Delete();

                    Db.ADDT29.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                    Db.ADDT31.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                    (from a in Db.ADDT33
                     join b in Db.ADDT32 on a.LOTTERY_NO equals b.LOTTERY_NO
                     where b.GAME_NO == model.Search.WhereGAME_NO
                     select a).Delete();

                    Db.ADDT32.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                }

                Db.ADDT26_D.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                Db.ADDT26.Remove(Del);

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }

        public bool SaveDelPersonDeleteData(GameEditViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                ADDT26 Del = Db.ADDT26.Where(a => a.GAME_NO == model.Main.GAME_NO).FirstOrDefault();

                if (Del == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                string tempPath = GetSysGamePath(Del.SCHOOL_NO, Del.GAME_NO);

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.Delete(tempPath, true);
                }

                var listT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).ToList();

                if (listT27.Count() > 0)
                {
                    Db.ADDT28.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                    Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                    (from a in Db.ADDT30
                     join b in Db.ADDT29 on a.TITLE_SHOW_ID equals b.TITLE_SHOW_ID
                     where b.GAME_NO == model.Search.WhereGAME_NO
                     select a).Delete();

                    Db.ADDT29.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                    Db.ADDT31.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                    (from a in Db.ADDT33
                     join b in Db.ADDT32 on a.LOTTERY_NO equals b.LOTTERY_NO
                     where b.GAME_NO == model.Search.WhereGAME_NO
                     select a).Delete();

                    Db.ADDT32.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                }

                Db.ADDT26_D.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                Db.ADDT26.Remove(Del);

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }

        public string ExcelFastLinkData(string GAME_NO, ref ECOOL_DEVEntities Db, ref string Message)
        {
            string returnFile = string.Empty;

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                try
                {
                    var aDDT26 = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).FirstOrDefault();

                    List<ADDT26_Q> aDDT26_Qs = Db.ADDT26_Q.Where(a => a.GAME_NO == GAME_NO).OrderBy(a => a.G_ORDER_BY).ToList();

                    List<ADDT26_D> aDDT26_Ds = Db.ADDT26_D.Where(a => a.GAME_NO == GAME_NO).OrderBy(a => a.LEVEL_ITEM).ToList();

                    var backupLinks = GetBackupLinkData(GAME_NO, ref Db);

                    if (aDDT26 != null && aDDT26_Ds != null && aDDT26_Ds?.Count > 0)
                    {
                        IWorkbook wb = new XSSFWorkbook();

                        NPOIHelper nPOIHelper = new NPOIHelper();

                        foreach (var Link in backupLinks)
                        {
                            CreateSheetFastLink(Link, aDDT26, aDDT26_Qs, aDDT26_Ds, wb);
                        }

                        string strTMPFile = GetSysGamePath(aDDT26.SCHOOL_NO, aDDT26.GAME_NO) + @"\FastLink\";

                        if (Directory.Exists(strTMPFile))
                        {
                            if (Directory.Exists(strTMPFile))
                            {
                                Directory.Delete(strTMPFile, true);
                            }
                        }

                        Directory.CreateDirectory(strTMPFile);

                        strTMPFile = strTMPFile + $@"\{aDDT26.GAME_NAME}_快速連結" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";

                        System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
                        wb.Write(FS);
                        FS.Close();
                        returnFile = strTMPFile;
                    }
                }
                catch (Exception ex)
                {
                    Message = ex.Message;
                    return string.Empty;
                }
            }

            return returnFile;
        }

        /// <summary>
        /// 報到
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="LOG_ID"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveApplyData(GameLeveViewModel model, ref ECOOL_DEVEntities Db, ref string LOG_ID, ref string Message)
        {
            var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).AsNoTracking().FirstOrDefault();
            if (T26 == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆活動單號";
                logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                return false;
            }

            var T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.GAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).AsNoTracking().FirstOrDefault();
            if (T26_D == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆活動有報名的關卡";
                logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                return false;
            }

            string GAME_USER_ID = string.Empty;

            var T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == model.GameUserID && a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().NoLock(a => a.FirstOrDefault());
            if (T27 != null)
            {
                Message = "報名已成功";
                return false;
            }

            GameQRCodeToGuestViewModel guestModel = new GameQRCodeToGuestViewModel();

            var St = Db.HRMT01.Where(a => a.CARD_NO == model.GameUserID).AsNoTracking().FirstOrDefault();

            if (St == null)
            {
                if (model.GameUserID.IndexOf("GAME_NO") != -1 && model.GameUserID.IndexOf("NAME") != -1)
                {
                    guestModel = JsonConvert.DeserializeObject<GameQRCodeToGuestViewModel>(model.GameUserID);

                    if (T26.GAME_NO != guestModel.GAME_NO)
                    {
                        var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(guestModel);
                        Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的 QR CODE 非本活動所用，卡號：{model.GameUserID},長度{model.GameUserID.Length} 碼。</font>";
                        logger.Error(Message + $" {jsonString}");
                        return false;
                    }
                    GAME_USER_ID = guestModel.PHONE;
                }
                else
                {
                    if (model.GameUserID.Length == 10)
                    {
                        Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的卡片不是本系統認可的數位學生證 或 不是 本校認可的卡片，卡號：{model.GameUserID},長度{model.GameUserID.Length} 碼。</font>";
                        logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                        return false;
                    }
                    else
                    {
                        Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的 QR CODE 非本活動所用，卡號：{model.GameUserID},長度{model.GameUserID.Length} 碼。</font>";
                        logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                        return false;
                    }
                }
            }
            else
            {
                GAME_USER_ID = model.GameUserID;
            }

            T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == GAME_USER_ID && a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().FirstOrDefault();
            if (T27 != null)
            {
                Message = "報名已成功";
                return false;
            }

            return SaveCreApplyData(model.GAME_NO, GAME_USER_ID, T26, T26_D, St, guestModel, ref Db, ref Message, ref LOG_ID);
        }

        /// <summary>
        /// 關卡支付處理
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="LOG_ID"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveLevelData(GameLeveViewModel model, ref ECOOL_DEVEntities Db, ref string LOG_ID, ref string Message)
        {
            ADDT26 T26 = null;

            ADDT26_D T26_D = null;
            ADDT26_Q T26_Q = null;

            ADDT27 T27 = null;
            string GAME_USER_ID = string.Empty;
            int LogCount = 0;

            bool IsRepeat = false;

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).AsNoTracking().FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                    return false;
                }

                if (!string.IsNullOrWhiteSpace(model.Coupons_ITem))
                {
                    T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.GAME_NO && a.LEVEL_NO == model.LEVEL_NO && a.Coupons_ITem == model.Coupons_ITem).AsNoTracking().FirstOrDefault();
                }
                else
                {
                    T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.GAME_NO && a.LEVEL_NO == model.LEVEL_NO).AsNoTracking().FirstOrDefault();
                }
                if (T26_D == null)
                {
                    Message = "系統發生錯誤;原因:找不到此關卡資料";
                    logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                    return false;
                }

                if (!string.IsNullOrWhiteSpace(T26_D.GROUP_ID))
                {
                    T26_Q = Db.ADDT26_Q.Where(a => a.GROUP_ID == T26_D.GROUP_ID && a.GAME_NO == T26.GAME_NO).FirstOrDefault();
                    if (T26_Q == null)
                    {
                        Message = "系統發生錯誤;原因:找不到有獎徵答題目";
                        logger.Error(Message + $" GAME_NO={model.GAME_NO}，GROUP_ID={T26_D.GROUP_ID}");
                        return false;
                    }
                }

                ts.Complete();
            }

            GAME_USER_ID = this.GetUserId(T26, model.GameUserID, ref Db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                logger.Debug(Message + $" GAME_NO={model.GAME_NO}");
                return false;
            }

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == GAME_USER_ID && a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().FirstOrDefault();
                if (T27 == null)
                {
                    Message = "您尚未報到，請先至「報到處」刷卡報到。";
                    // logger.Debug(Message + $" GAME_NO={model.GAME_NO};GAME_USER_ID={GAME_USER_ID}");
                    return false;
                }

                if (!string.IsNullOrWhiteSpace(T26_D.GROUP_ID))
                {
                    if (Db.ADDT26_MAns.Where(a => a.GAME_NO == T26.GAME_NO && a.TEMP_USER_ID == T27.TEMP_USER_ID && a.STATUS == (Byte)ADDT26_MAns.AnsStatus.完成此次活動).Any())
                    {
                        Message = "恭喜你在這一次活動中獲得滿分，你不需要再次作答。";
                        return false;
                    }

                    if (Db.ADDT26_MAns.Where(a => a.GAME_NO == T26.GAME_NO && a.TEMP_USER_ID == T27.TEMP_USER_ID && a.STATUS == (Byte)ADDT26_MAns.AnsStatus.回答完畢).Any())
                    {
                        Message = "您全部回答完畢，請先到查詢機查詢成績。";
                        return false;
                    }
                }

                LogCount = Db.ADDT28.Where(a => a.GAME_NO == T27.GAME_NO && a.SOURCE_NO == T26_D.LEVEL_NO && a.SCHOOL_NO == T27.SCHOOL_NO && a.TEMP_USER_ID == T27.TEMP_USER_ID).AsNoTracking().Count();

                if ((T26_D.Y_REPEAT ?? false) == false && LogCount > 0)
                {
                    if (!string.IsNullOrWhiteSpace(T26_D.GROUP_ID))
                    {
                        Message = "這一題已作答，請回答其他題目喔！";
                    }
                    else
                    {
                        Message = "您已經通過本關，請繼續挑戰其他關卡喔！";
                    }

                    return false;
                }

                ts.Complete();
            }

            using (TransactionScope tx = new TransactionScope())
            {
                if (T26_D.LEVEL_TYPE != ADDT26_D.LevelType.Test)
                {
                    string LogDesc = string.Empty;

                    if (!string.IsNullOrWhiteSpace(T26_D.GROUP_ID))
                    {
                        LogDesc = string.Concat(string.Format("第{0}題，{1}，您認為答案是:", T26_Q.G_ORDER_BY, T26_Q.G_SUBJECT), T26_D.LEVEL_NAME);
                        IsRepeat = this.SaveAns(Db, T26, T26_D, T27);
                    }
                    else
                    {
                        LogDesc = T26.GAME_NAME + T26_D.LEVEL_NAME;
                    }

                    var IsPayOK = CameAddCash(T27.TEMP_USER_ID, T27.SCHOOL_NO, T27.GAME_NO, T26_D.LEVEL_NO, T26_D.CASH, LogDesc, ref Message, ref Db, out LOG_ID);

                    if (!IsPayOK)
                    {
                        logger.Error(Message + $" GAME_NO={model.GAME_NO};GAME_USER_ID={GAME_USER_ID}");
                        return false;
                    }
                }

                //if (T27.GAME_USER_TYPE == UserType.Student)
                //{
                //    CashHelper.AddCash(null, (int)T26_D.CASH, T27.SCHOOL_NO, T27.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref Db);

                //}

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    logger.Error(Message + $" GAME_NO={model.GAME_NO};GAME_USER_ID={GAME_USER_ID}");
                    return false;
                }

                string Desc = string.Empty;

                if (T27.GAME_USER_TYPE == UserType.Student)
                {
                    Desc = $"{T27.NAME}同學";
                }
                else
                {
                    Desc = $"{T27.NAME}先生/女士";
                }

                if (T26.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答 && T26_D.LEVEL_TYPE == ADDT26_D.LevelType.Pay)
                {
                    var AnsCount = (from a in Db.ADDT26_MAns
                                    join b in Db.ADDT26_DAns on a.ANSWER_ID equals b.ANSWER_ID
                                    where a.GAME_NO == model.GAME_NO && a.IS_LAST_SCORE == true
                                    && a.STATUS == (byte)ADDT26_MAns.AnsStatus.作答中
                                    && a.TEMP_USER_ID == T27.TEMP_USER_ID
                                    select b).NoLock(x => x.Count());

                    var GCount = Db.ADDT26_Q.Where(a => a.GAME_NO == model.GAME_NO).NoLock(x => x.Count());

                    //重複答題
                    if (IsRepeat)
                    {
                        Message = string.Concat($"<font color='red'>寫入成功</font><br/>{Desc}<br/>你重新回答了這一題，你的回答是:", T26_D.LEVEL_NAME, "<br/>若有重新答題，則以最後一次答案為主。");
                    }
                    else
                    {
                        Message = string.Concat($"<font color='red'>寫入成功</font><br/>{Desc}<br/>你的回答是:", T26_D.LEVEL_NAME, "。");
                    }

                    //最後一題
                    if (AnsCount == GCount)
                    {
                        var SaveMAns = Db.ADDT26_MAns.Where(a => a.GAME_NO == model.GAME_NO && a.IS_LAST_SCORE == true
                                     && a.STATUS == (byte)ADDT26_MAns.AnsStatus.作答中
                                     && a.TEMP_USER_ID == T27.TEMP_USER_ID
                                     ).FirstOrDefault();

                        if (SaveMAns != null)
                        {
                            //題目數
                            var QCount = Db.ADDT26_Q.Where(a => a.GAME_NO == T26.GAME_NO).NoLock(x => x.Count());

                            //答對數
                            var AnsTrueCount = Db.ADDT26_DAns.Where(a => a.ANSWER_ID == SaveMAns.ANSWER_ID && a.TRUE_ANS == true).NoLock(x => x.Count());

                            //計算分數
                            int SCORE = (int)Math.Round((Convert.ToDouble(AnsTrueCount) / Convert.ToDouble(QCount)) * 100, 0);

                            SaveMAns.SCORE = SCORE;
                            SaveMAns.STATUS = (byte)ADDT26_MAns.AnsStatus.回答完畢;

                            var T26R = Db.ADDT26_R.Where(a => a.GAME_NO == T26.GAME_NO && a.RATE_S <= SCORE && a.RATE_E >= SCORE && a.REWARD_CASH != 0).NoLock(x => x.FirstOrDefault());
                            if (T26R != null) //獲得獎利
                            {
                                string rLOG_ID = string.Empty;
                                string LogDesc = string.Empty;

                                if (T26R.REWARD_CASH > 0)
                                {
                                    LogDesc = $@"你的成績:{SCORE}，獲得{T26R.REWARD_CASH}點";
                                }
                                else
                                {
                                    LogDesc = $@"你的成績:{SCORE}，扣{T26R.REWARD_CASH}點";
                                }

                                SaveMAns.REWARD_CASH = T26R.REWARD_CASH;

                                var IsPayOK = CameAddCash(T27.TEMP_USER_ID, T27.SCHOOL_NO, T27.GAME_NO, SaveMAns.ANSWER_ID, T26R.REWARD_CASH, LogDesc, ref Message, ref Db, out rLOG_ID);

                                if (!IsPayOK)
                                {
                                    logger.Error(Message + $" GAME_NO={model.GAME_NO};TEMP_USER_ID={T27.TEMP_USER_ID}");
                                }
                            }

                            Db.Entry(SaveMAns).State = System.Data.Entity.EntityState.Modified;

                            try
                            {
                                Db.SaveChanges();
                            }
                            catch (Exception ex)
                            {
                                Message = "系統發生錯誤;原因:" + ex.Message;
                                logger.Error(Message + $" GAME_NO={model.GAME_NO};GAME_USER_ID={GAME_USER_ID}");
                                return false;
                            }
                        }

                        Message = Message + "<br/>這是最後一題，恭喜完成<br/>請至查詢機查詢分數。";
                    }
                }
                else
                {
                    if (T26_D.LEVEL_TYPE == ADDT26_D.LevelType.Pay || T26_D.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize)
                    {
                        List<GameCashDetailsViewModel> GameCashDetailsViewModelItemInfo = new List<GameCashDetailsViewModel>();
                        GameCashDetailsViewModelItemInfo = GetPersonCashLogIntoData(T27.TEMP_USER_ID, Db);
                        int CashINInfo = 0;
                        CashINInfo = GameCashDetailsViewModelItemInfo.Sum(x => x.CASH_IN);
                        if (T26_D.CASH <= 0)
                        {
                            Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次支付{ T26_D.CASH.Value.ToString()}點，目前共有{(CashINInfo)}點。";
                        }
                        else
                        {
                            Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次獲得{ T26_D.CASH.Value.ToString()}點，目前共有{(CashINInfo)}點。";
                        }

                        if (LogCount > 0)
                        {

                            if ((T26_D.Y_REPEAT ?? false) == false && LogCount > 0)
                            {
                                Message = Message + @"<br/><div style='background-color:#87CEFA'>您已經通過本關，請繼續挑戰其他關卡喔！</div>";
                            }
                            else {

                                Message = Message + @"<br/><div style='background-color:#87CEFA'>您已經通過本關，請繼續挑戰其他關卡喔！(這個關卡可以重複)</div>";

                            }
                            
                        }
                    }
                    else if (T26_D.LEVEL_TYPE == ADDT26_D.LevelType.Test)
                    {
                        Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>已完成開卡，可以開始闖關了";
                    }
                    else if (T26_D.LEVEL_TYPE == ADDT26_D.LevelType.Prize)
                    {
                        Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>兌換成功";
                    }
                }

                tx.Complete();
            }

            return true;
        }

        public bool SaveBuskerData(GameBuskerAddViewModel model, ref ECOOL_DEVEntities Db, ref string Message, ref string TITLE_SHOW_ID)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                try
                {
                    var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).FirstOrDefault();
                    if (T26 == null)
                    {
                        Message = "系統發生錯誤;原因:找不到此筆活動單號";
                        return false;
                    }

                    if (Db.ADDT29.Where(a => a.GAME_NO == model.GAME_NO && a.TITLE_SHOW_NAME == model.TITLE_SHOW_NAME.Trim()).Any())
                    {
                        Message = "表演名稱重覆";
                        return false;
                    }

                    var arrGAME_USER_ID = model.Details.GroupBy(a => a.GAME_USER_ID).Where(g => g.Count() > 1).Select(g => g.Key).ToArray();

                    if (arrGAME_USER_ID.Length > 0)
                    {
                        Message = "表演者名單重覆";
                        return false;
                    }

                    var ORDER_BY = (Db.ADDT29.Where(a => a.GAME_NO == model.GAME_NO).Select(a => a.ORDER_BY).Max() ?? 0) + 1;

                    var FirstUser = model.Details.FirstOrDefault();

                    ADDT29 Cre = new ADDT29
                    {
                        TITLE_SHOW_ID = Guid.NewGuid().ToString("N"),
                        GAME_NO = model.GAME_NO,
                        TITLE_SHOW_NAME = model.TITLE_SHOW_NAME,
                        ORDER_BY = ORDER_BY,
                        CRE_PERSON = FirstUser.TEMP_USER_ID,
                        CRE_DATE = DateTime.Now,
                        CHG_PERSON = FirstUser.TEMP_USER_ID,
                        CHG_DATE = DateTime.Now
                    };

                    if (!string.IsNullOrWhiteSpace(model.WebCamBase64))
                    {
                        string TITLE_IMG_Path = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + @"\title_of_show\";

                        if (Directory.Exists(TITLE_IMG_Path) == false)
                        {
                            Directory.CreateDirectory(TITLE_IMG_Path);
                        }

                        var t = model.WebCamBase64.Substring(23);  // remove data:image/png;base64,

                        byte[] bytes = Convert.FromBase64String(t);

                        Image image;
                        using (MemoryStream ms = new MemoryStream(bytes))
                        {
                            image = Image.FromStream(ms);
                        }
                        var randomFileName = $"{Cre.TITLE_SHOW_ID}.Jpeg";
                        var fullPath = Path.Combine(TITLE_IMG_Path, randomFileName);
                        if (File.Exists(fullPath))
                        {
                            File.Delete(fullPath);
                        }
                        Bitmap bmp = new Bitmap(image);
                        image.Dispose();

                        bmp.Save(fullPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                        bmp.Dispose();

                        Cre.TITLE_IMG = randomFileName;
                    }
                    else if (model.UploadBuskerFile?.ContentLength > 0)
                    {
                        UploadBuskerFileData(model.UploadBuskerFile, T26, Cre, ref Message);
                        if (!string.IsNullOrWhiteSpace(Message))
                        {
                            return false;
                        }
                    }

                    Db.ADDT29.Add(Cre);

                    if (model.Details.Count() > 0)
                    {
                        List<ADDT30> listADDT30 = new List<ADDT30>();

                        string BUSKER_ID = Guid.NewGuid().ToString("N");
                        int BUSKER_ITEM = 0;

                        foreach (var item in model.Details)
                        {
                            BUSKER_ITEM++;

                            ADDT30 Cre_D = new ADDT30
                            {
                                BUSKER_ID = BUSKER_ID,
                                BUSKER_ITEM = BUSKER_ITEM.ToString(),
                                TITLE_SHOW_ID = Cre.TITLE_SHOW_ID,
                                TEMP_USER_ID = item.TEMP_USER_ID,
                                CHG_PERSON = FirstUser.TEMP_USER_ID,
                                CHG_DATE = DateTime.Now
                            };

                            listADDT30.Add(Cre_D);
                            Db.ADDT30.Add(Cre_D);
                        }

                     //   EFBatchOperation.For(Db, Db.ADDT30).InsertAll(listADDT30);
                    }

                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }

                    TITLE_SHOW_ID = Cre.TITLE_SHOW_ID;
                    ts.Complete();

                    Message = $@"報到成功<br/>表演名稱:{Cre.TITLE_SHOW_NAME}<br/>";
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }
            }

            return true;
        }

        public bool SaveBuskerEditData(GameBuskerEditViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                if (Db.ADDT29.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TITLE_SHOW_NAME == model.Main.TITLE_SHOW_NAME.Trim() && a.TITLE_SHOW_ID != model.Main.TITLE_SHOW_ID).Any())
                {
                    Message = "表演名稱重覆";
                    return false;
                }

                if (model.Details==null|| model.Details?.Count() == 0)
                {
                    Message = "表演者隊伍未輸入";
                    return false;
                }

                var arrTEMP_USER_ID = model.Details.GroupBy(a => a.TEMP_USER_ID).Where(g => g.Count() > 1).Select(g => g.Key).ToArray();

                if (arrTEMP_USER_ID.Length > 0)
                {
                    Message = "表演者名單重覆";
                    return false;
                }

                var ORDER_BY = (Db.ADDT29.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Select(a => a.ORDER_BY).Max() ?? 0) + 1;

                ADDT29 Cre = null;

                Cre = Db.ADDT29.Where(a => a.TITLE_SHOW_ID == model.Main.TITLE_SHOW_ID).FirstOrDefault();
                if (Cre == null)
                {
                    Cre = new ADDT29
                    {
                        TITLE_SHOW_ID = Guid.NewGuid().ToString("N"),
                        GAME_NO = model.Search.WhereGAME_NO,
                        TITLE_SHOW_NAME = model.Main.TITLE_SHOW_NAME,
                        ORDER_BY = ORDER_BY,
                        CRE_PERSON = user.USER_KEY,
                        CRE_DATE = DateTime.Now,
                        CHG_PERSON = user.USER_KEY,
                        CHG_DATE = DateTime.Now
                    };

                    UploadBuskerFileData(model.UploadBuskerFile, T26, Cre, ref Message);
                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return false;
                    }

                    Db.ADDT29.Add(Cre);
                }
                else
                {
                    Cre.TITLE_SHOW_NAME = model.Main.TITLE_SHOW_NAME;
                    Cre.CHG_PERSON = user.USER_KEY;
                    Cre.CHG_DATE = DateTime.Now;

                    UploadBuskerFileData(model.UploadBuskerFile, T26, Cre, ref Message);
                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return false;
                    }

                    Db.Entry(Cre).State = System.Data.Entity.EntityState.Modified;
                }
                Db.ADDT30.Where(a => a.TITLE_SHOW_ID == Cre.TITLE_SHOW_ID).Select(x => x.TEMP_USER_ID).ToList();

                Db.ADDT30.Where(a => a.TITLE_SHOW_ID == Cre.TITLE_SHOW_ID).Delete();

                if (model.Details.Count() > 0)
                {
                    List<ADDT30> listADDT30 = new List<ADDT30>();

                    string BUSKER_ID = model.Details.Where(a => a.BUSKER_ID != null).Select(a => a.BUSKER_ID).FirstOrDefault() ?? Guid.NewGuid().ToString("N");
                    int BUSKER_ITEM = 0;

                    foreach (var item in model.Details)
                    {
                        BUSKER_ITEM++;

                        ADDT30 Cre_D = new ADDT30
                        {
                            BUSKER_ID = BUSKER_ID,
                            BUSKER_ITEM = BUSKER_ITEM.ToString(),
                            TITLE_SHOW_ID = Cre.TITLE_SHOW_ID,
                            TEMP_USER_ID = item.TEMP_USER_ID,
                            CHG_DATE = DateTime.Now
                        };

                        listADDT30.Add(Cre_D);
                        Db.ADDT30.Add(Cre_D);
                    }

                  //  EFBatchOperation.For(Db, Db.ADDT30).InsertAll(listADDT30);
                }

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }

        public bool UploadBuskerFileData(HttpPostedFileBase UploadBuskerFile, ADDT26 T26, ADDT29 Cre, ref string Message)
        {
            if (UploadBuskerFile != null && UploadBuskerFile?.ContentLength > 0)
            {
                try
                {
                    string TITLE_IMG_Path = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + $@"\{title_of_show}\";

                    if (Directory.Exists(TITLE_IMG_Path) == false)
                    {
                        Directory.CreateDirectory(TITLE_IMG_Path);
                    }

                    string fileName = DateTime.Now.ToString("yyyyMMddHHmmss") + Path.GetFileName(UploadBuskerFile.FileName);

                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                    if (regexCode.IsMatch(fileName.ToLower()) == false)
                    {
                        Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                        return false;
                    }

                    if (UploadBuskerFile.ContentLength / 1024 > (1024 * 20)) // 20MB
                    {
                        Message = "上傳檔案不能超過20MB";
                        return false;
                    }

                    if (!string.IsNullOrWhiteSpace(Cre.TITLE_IMG))
                    {
                        string DelFile = TITLE_IMG_Path + "\\" + Cre.TITLE_IMG;

                        if (System.IO.File.Exists(DelFile))
                        {
                            System.IO.File.Delete(DelFile);
                        }
                    }

                    string UpLoadFile = TITLE_IMG_Path + "\\" + fileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    UploadBuskerFile.SaveAs(UpLoadFile);

                    Cre.TITLE_IMG = fileName;
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }
            }

            return true;
        }

        public GamePassModexViewModel GetPassModeData(GamePassModexViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.Main = (from a in db.ADDT26
                              where a.GAME_NO == model.GAME_NO
                              select new GameEditMainViewModel()
                              {
                                  SCHOOL_NO = a.SCHOOL_NO,
                                  GAME_NO = a.GAME_NO,
                                  GAME_NAME = a.GAME_NAME,
                                  GAME_IMG = a.GAME_IMG,
                                  GAME_DESC = a.GAME_DESC,
                                  GAME_DATES = a.GAME_DATES,
                                  GAME_DATEE = a.GAME_DATEE,
                                  CRE_PERSON = a.CRE_PERSON,
                                  CRE_DATE = a.CRE_DATE,
                                  CHG_PERSON = a.CHG_PERSON,
                                  CHG_DATE = a.CHG_DATE,
                                  GAME_TYPE = a.GAME_TYPE ?? (byte)ADDT26.GameType.一般,
                                  TEAM_GAME_DATES = a.TEAM_GAME_DATES,
                                  TEAM_GAME_DATEE = a.TEAM_GAME_DATEE,
                              }).FirstOrDefault();

                if (model.Main != null)
                {
                    if (!string.IsNullOrWhiteSpace(model.Main.GAME_IMG))
                    {
                        model.Main.GAME_IMG_PATH = GetDirectorySysGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO, model.Main.GAME_IMG);
                    }

                    if (model.Main.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                    {
                        model.aDDT26_Qs = (
                           from a in db.ADDT26_Q
                           where a.GAME_NO == model.GAME_NO
                           select a).ToList<ADDT26_Q>();
                    }

                    model.Details = (from a in db.ADDT26_D
                                     join p in db.ADDT26_Q on new { a.GAME_NO, a.GROUP_ID } equals new { p.GAME_NO, p.GROUP_ID } into cp
                                     from p in cp.DefaultIfEmpty()
                                     where a.GAME_NO == model.GAME_NO
                                     select new GameEditDetailsViewModel()
                                     {
                                         LEVEL_NO = a.LEVEL_NO,
                                         LEVEL_TYPE = a.LEVEL_TYPE,
                                         LEVEL_ITEM = a.LEVEL_ITEM,
                                         GAME_NO = a.GAME_NO,
                                         LEVEL_NAME = a.LEVEL_NAME,
                                         LEVEL_IMG = a.LEVEL_IMG,
                                         LEVEL_DESC = a.LEVEL_DESC,
                                         CASH = a.CASH,
                                         CHG_PERSON = a.CHG_PERSON,
                                         CHG_DATE = a.CHG_DATE,
                                         GROUP_ID = a.GROUP_ID,
                                         G_ORDER_BY = p.G_ORDER_BY ?? 0,
                                         G_SUBJECT = p.G_SUBJECT,
                                         Coupons_ITem = a.Coupons_ITem,
                                     }).OrderBy(a => a.G_ORDER_BY).ThenBy(a => a.LEVEL_ITEM).ThenBy(a => a.LEVEL_TYPE).ToList();

                    var SysGamePath = GetSetDirectoryGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO);

                    model.Details = model.Details.Select(
                       a =>
                       {
                           a.LEVEL_IMG_PATH = !string.IsNullOrWhiteSpace(a.LEVEL_IMG) ? UrlCustomHelper.Url_Content(SysGamePath + a.LEVEL_IMG) : "";
                           a.IsApply = a.LEVEL_TYPE == ADDT26_D.LevelType.Apply ? true : false;
                           return a;
                       }
                   ).ToList();
                }
            }

            return model;
        }

        public GameLeveViewModel GetLeveData(GameLeveViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.Main = (from a in db.ADDT26
                              where a.GAME_NO == model.GAME_NO
                              select new GameEditMainViewModel()
                              {
                                  SCHOOL_NO = a.SCHOOL_NO,
                                  GAME_NO = a.GAME_NO,
                                  GAME_NAME = a.GAME_NAME,
                                  GAME_IMG = a.GAME_IMG,
                                  GAME_DESC = a.GAME_DESC,
                                  GAME_DATES = a.GAME_DATES,
                                  GAME_DATEE = a.GAME_DATEE,
                                  CRE_PERSON = a.CRE_PERSON,
                                  CRE_DATE = a.CRE_DATE,
                                  CHG_PERSON = a.CHG_PERSON,
                                  CHG_DATE = a.CHG_DATE,
                                  GAME_TYPE = a.GAME_TYPE,
                                  SUBJECT =a.SUBJECT
                              }).AsNoTracking().FirstOrDefault();
              
                if (model.Main != null)
                {
                    model.SCHOOL_NO = model.Main.SCHOOL_NO;
                    if (!string.IsNullOrWhiteSpace(model.Main.GAME_IMG))
                    {
                        model.Main.GAME_IMG_PATH = GetDirectorySysGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO, model.Main.GAME_IMG);
                    }
                    else
                    {
                        model.Main.GAME_IMG_PATH = UrlCustomHelper.Url_Content(GetSetDirectoryGamePath() + "Game_df.jpg");
                    }

                    string sSQL = @" Select a.* from ADDT26_D a (nolock) Where 1=1 and a.GAME_NO=@GAME_NO";
                    var Details = db.Database.Connection.Query<GameEditDetailsViewModel>(sSQL
                     , new
                     {
                         model.GAME_NO,
                     });

                    if (!string.IsNullOrWhiteSpace(model.LEVEL_NO))
                    {
                        Details = Details.Where(a => a.LEVEL_NO == model.LEVEL_NO);
                        if (!string.IsNullOrWhiteSpace(model.Coupons_ITem))
                        {
                            Details = Details.Where(a => a.Coupons_ITem == model.Coupons_ITem);
                        }
                    }
                    else
                    {
                        Details = Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Apply);
                    }

                    model.Details = Details.FirstOrDefault();

                    model.Details.LOADING_TIME = model.Details.LOADING_TIME ?? 3;

                    if (model.Details != null)
                    {
                        var SysGamePath = GetSetDirectoryGamePath(model.Main.SCHOOL_NO, model.Main.GAME_NO);

                        if (!string.IsNullOrWhiteSpace(model.Details.LEVEL_IMG))
                        {
                            model.Details.LEVEL_IMG_PATH = UrlCustomHelper.Url_Content(SysGamePath + @"\" + model.Details.LEVEL_IMG);
                        }
                        else
                        {
                            if (model.Main.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                            {
                                if (model.Details.LEVEL_NAME.ToUpper() == "O")
                                {
                                    model.Details.LEVEL_IMG_PATH = UrlCustomHelper.Url_Content(@"~\Content\img\o_iocn.png");
                                }
                                else if (model.Details.LEVEL_NAME.ToUpper() == "X")
                                {
                                    model.Details.LEVEL_IMG_PATH = UrlCustomHelper.Url_Content(@"~\Content\img\x_icon.png");
                                }
                                else
                                {
                                    model.Details.LEVEL_IMG_PATH = model.Main?.GAME_IMG_PATH;
                                }
                            }
                            else
                            {
                                model.Details.LEVEL_IMG_PATH = model.Main?.GAME_IMG_PATH;
                            }
                        }

                        if (model.Main.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                        {
                            if (!string.IsNullOrWhiteSpace(model.Details.GROUP_ID))
                            {
                                var aDDT26_Q = db.ADDT26_Q.Where(a => a.GAME_NO == model.Main.GAME_NO && a.GROUP_ID == model.Details.GROUP_ID).FirstOrDefault();

                                if (aDDT26_Q != null)
                                {
                                    model.Details.G_ORDER_BY = (int)aDDT26_Q.G_ORDER_BY;
                                    model.Details.G_SUBJECT = aDDT26_Q.G_SUBJECT;
                                }
                            }
                        }
                    }
                }
            }

            return model;
        }

        public GameBuskerAddViewModel GetBuskerPassedData(bool OK, string GAME_NO, string TITLE_SHOW_ID, ref string Message, ref ECOOL_DEVEntities Db)
        {
            GameBuskerAddViewModel model = new GameBuskerAddViewModel();
            model.IsOK = OK;
            model.GAME_NO = GAME_NO;
            model.Message = Message;

            if (!string.IsNullOrWhiteSpace(TITLE_SHOW_ID))
            {
                using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
                {
                    var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).FirstOrDefault();
                    var T29 = Db.ADDT29.Where(a => a.TITLE_SHOW_ID == TITLE_SHOW_ID).FirstOrDefault();

                    if (T29 != null && T26 != null)
                    {
                        model.TITLE_SHOW_NAME = T29.TITLE_SHOW_NAME;
                        if (!string.IsNullOrWhiteSpace(T29.TITLE_IMG))
                        {
                            model.TITLE_IMG = UrlCustomHelper.Url_Content(GetSetDirectoryGamePath(T26.SCHOOL_NO, T26.GAME_NO) + @"\title_of_show\" + T29.TITLE_IMG);
                        }
                        else
                        {
                            model.TITLE_IMG = UrlCustomHelper.Url_Content(@"~\Content\img\concert.svg");
                        }

                        model.Details = (from a in Db.ADDT30
                                         join b in Db.ADDT27 on a.TEMP_USER_ID equals b.TEMP_USER_ID
                                         where a.TITLE_SHOW_ID == TITLE_SHOW_ID
                                         select new GameBuskerAddDetailsViewModel()
                                         {
                                             TEMP_USER_ID = a.TEMP_USER_ID,
                                             GAME_USER_ID = b.GAME_USER_ID,
                                             NAME = b.NAME,
                                         }).ToList();
                    }
                }
            }

            return model;
        }

        public GameBuskerLikeViewModel GetBuskerLikeData(GameBuskerLikeViewModel model, ref string Message, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                if (model == null) model = new GameBuskerLikeViewModel();

                var temp = Db.ADDT29.Where(a => a.GAME_NO == model.GAME_NO);

                ADDT29 T29;

                if (string.IsNullOrWhiteSpace(model.WhereTITLE_SHOW_ID))
                {
                    temp = temp.Where(a => a.LIVE_STREAM == true);

                    T29 = temp.FirstOrDefault();

                    if (T29 == null)
                    {
                        Message = "目前無直播表演";
                        return model;
                    }
                }
                else
                {
                    temp = temp.Where(a => a.TITLE_SHOW_ID == model.WhereTITLE_SHOW_ID);

                    T29 = temp.FirstOrDefault();

                    if (T29 == null)
                    {
                        Message = "查無此表演節目";
                        return model;
                    }
                }

                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).FirstOrDefault();

                model.TITLE_SHOW_ID = T29.TITLE_SHOW_ID;
                model.TITLE_SHOW_NAME = T29.TITLE_SHOW_NAME;
                if (!string.IsNullOrWhiteSpace(T29.TITLE_IMG))
                {
                    model.TITLE_IMG = UrlCustomHelper.Url_Content(GetSetDirectoryGamePath(T26.SCHOOL_NO, T26.GAME_NO) + @"\title_of_show\" + T29.TITLE_IMG);
                }
                else
                {
                    model.TITLE_IMG = UrlCustomHelper.Url_Content(@"~\Content\img\concert.svg");
                }

                string QRCodeLikeImg = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + "QRCodeLike.jpg";

                if (System.IO.File.Exists(QRCodeLikeImg) == false)
                {
                    GenerateQR(T26.SCHOOL_NO, T26.GAME_NO, "BuskerLikeView");
                }

                model.QRCodeLikeImg = UrlCustomHelper.Url_Content(GetSetDirectoryGamePath(T26.SCHOOL_NO, T26.GAME_NO) + @"\QRCodeLike.jpg");

                model.Show_LIKE_COUNT = T26.LIKE_COUNT ?? 30;
                model.NOW_LIKE_COUNT = Db.ADDT31.Where(a => a.TITLE_SHOW_ID == T29.TITLE_SHOW_ID).Count();

                model.Details = (from a in Db.ADDT30
                                 join b in Db.ADDT27 on a.TEMP_USER_ID equals b.TEMP_USER_ID
                                 where a.TITLE_SHOW_ID == T29.TITLE_SHOW_ID
                                 select new GameBuskerAddDetailsViewModel()
                                 {
                                     TEMP_USER_ID = a.TEMP_USER_ID,
                                     GAME_USER_ID = b.GAME_USER_ID,
                                     NAME = b.NAME,
                                 }).ToList();

                model.WhereTITLE_SHOW_ID = T29.TITLE_SHOW_ID;
                return model;
            }
        }

        public GameBuskerManagerViewModel GetBuskerManagerListData(GameBuskerManagerViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                if (model == null) model = new GameBuskerManagerViewModel();

                string sSQL = @" Select TITLE_SHOW_ID = a.TITLE_SHOW_ID,
                                GAME_NO = a.GAME_NO,
                                TITLE_SHOW_NAME = a.TITLE_SHOW_NAME,
                                ORDER_BY = a.ORDER_BY,
                                CRE_DATE = a.CRE_DATE,
                                TITLE_IMG = a.TITLE_IMG,
                                LIVE_STREAM = a.LIVE_STREAM,
                                LIKE_COUNT = (select Count(*) from ADDT31 b  (nolock) where a.TITLE_SHOW_ID=b.TITLE_SHOW_ID),
                                CASH = a.CASH
                                from ADDT29 a (nolock)

                                where a.GAME_NO=@GAME_NO";
                var temp = Db.Database.Connection.Query<BuskerManagerEditViewModel>(sSQL
                 , new
                 {
                     GAME_NO = model.Search.WhereGAME_NO,
                 });

                if (!string.IsNullOrWhiteSpace(model.WhereKeyIn))
                {
                    temp = temp.Where(a => a.TITLE_SHOW_NAME == model.WhereKeyIn);
                }

                string sSQL1 = @" Select TITLE_SHOW_ID = a.TITLE_SHOW_ID,
                                GAME_NO = a.GAME_NO,
                                TITLE_SHOW_NAME = a.TITLE_SHOW_NAME,
                                ORDER_BY = a.ORDER_BY,
                                CRE_DATE = a.CRE_DATE,
                                TITLE_IMG = a.TITLE_IMG,
                                LIVE_STREAM = a.LIVE_STREAM,
                                LIKE_COUNT = (select Count(*) from ADDT31 b  (nolock) where a.TITLE_SHOW_ID=b.TITLE_SHOW_ID),
                                CASH = a.CASH,
                             GAME_USER_ID=  c.GAME_USER_ID,
							   SNAME=	c.SNAME
                                from ADDT29 a (nolock)
								inner join ADDT30 b on a.TITLE_SHOW_ID=b.TITLE_SHOW_ID
								inner join ADDT27 c on b.TEMP_USER_ID=c.TEMP_USER_ID 

                                where a.GAME_NO=@GAME_NO and c.GAME_NO=@GAME_NO";
                var temp1 = Db.Database.Connection.Query<BuskerManagerEditViewModel>(sSQL1
                 , new
                 {
                     GAME_NO = model.Search.WhereGAME_NO,
                 }).ToList();

                model.EditPeople = temp1;
             
                model.Edit = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);
                
                return model;
            }
        }

        public int GetLikeData(string TITLE_SHOW_ID, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                return Db.ADDT31.Where(a => a.TITLE_SHOW_ID == TITLE_SHOW_ID).Count();
            }
        }

        public int SaveLikeData(string GAME_NO, string GameUserID, string TITLE_SHOW_ID, bool UnApply, ref string Message, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return 0;
                }

                if (UnApply)
                {
                    string IP = HttpContext.Current.Request.Params["HTTP_CLIENT_IP"] ?? HttpContext.Current.Request.UserHostAddress;

                    ADDT31 Cre = Db.ADDT31.Where(a => a.GAME_NO == GAME_NO && a.TITLE_SHOW_ID == TITLE_SHOW_ID && a.TEMP_USER_ID == IP).FirstOrDefault();

                    if (Cre == null)
                    {
                        Cre = new ADDT31();
                        Cre.LIKE_NO = Guid.NewGuid().ToString("N");
                        Cre.TITLE_SHOW_ID = TITLE_SHOW_ID;
                        Cre.GAME_NO = GAME_NO;
                        Cre.TEMP_USER_ID = IP;
                        Cre.NAME = "訪客";
                        Cre.CASH = 1;
                        Cre.CHG_PERSON = "";
                        Cre.CHG_DATE = DateTime.Now;
                        Db.ADDT31.Add(Cre);

                        try
                        {
                            Db.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            Message = "系統發生錯誤;原因:" + ex.Message;
                        }
                    }
                    else
                    {
#if DEBUG
                        Message = "";
#else
                        Message = "您已給過這組表演者讚了，無法重覆給讚";
#endif
                    }
                }
                else
                {
                    string GAME_USER_ID = this.GetUserId(T26, GameUserID, ref Db, ref Message);

                    if (string.IsNullOrWhiteSpace(Message))
                    {
                        var T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == GAME_USER_ID && a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT27.StatusVal.使用中).FirstOrDefault();
                        if (T27 == null)
                        {
                            Message = "您未報到，請先行至報到處「報到」";
                        }
                        else
                        {
                            ADDT31 Cre = Db.ADDT31.Where(a => a.GAME_NO == GAME_NO && a.TITLE_SHOW_ID == TITLE_SHOW_ID && a.TEMP_USER_ID == T27.TEMP_USER_ID).FirstOrDefault();
                            if (Cre == null)
                            {
                                Cre = new ADDT31();

                                Cre.LIKE_NO = Guid.NewGuid().ToString("N");
                                Cre.TITLE_SHOW_ID = TITLE_SHOW_ID;
                                Cre.GAME_NO = GAME_NO;
                                Cre.TEMP_USER_ID = T27.TEMP_USER_ID;
                                Cre.NAME = T27.NAME;
                                Cre.CASH = 1;
                                Cre.CHG_PERSON = GAME_USER_ID;
                                Cre.CHG_DATE = DateTime.Now;
                                Db.ADDT31.Add(Cre);

                                try
                                {
                                    Db.SaveChanges();
                                }
                                catch (Exception ex)
                                {
                                    Message = "系統發生錯誤;原因:" + ex.Message;
                                }
                            }
                            else
                            {
#if DEBUG
                                Message = "";
#else
                                Message = "您已給過這組表演者讚了，無法重覆給讚";
#endif
                            }
                        }
                    }
                }

                ts.Complete();
            }

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                return Db.ADDT31.Where(a => a.TITLE_SHOW_ID == TITLE_SHOW_ID).Count();
            }
        }

        public bool SaveBuskerManagerData(string GAME_NO, string TITLE_SHOW_ID, string TITLE_SHOW_NAME, string ORDER_BY, bool IsLive, bool IsDelete, bool IsUnLive, ref string Message, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                var T29 = Db.ADDT29.Where(a => a.TITLE_SHOW_ID == TITLE_SHOW_ID).FirstOrDefault();
                if (T29 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆此資";
                    return false;
                }

                if (IsDelete)
                {
                    if (!string.IsNullOrWhiteSpace(T29.TITLE_IMG))
                    {
                        string TITLE_IMG_Path = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + $@"\{title_of_show}\";

                        string DelFile = TITLE_IMG_Path + "\\" + T29.TITLE_IMG;

                        if (System.IO.File.Exists(DelFile))
                        {
                            System.IO.File.Delete(DelFile);
                        }
                    }

                    if (T29.CASH > 0)
                    {
                        Message = "無法刪除該隊伍，必須先把給點數設成0";
                        return false;
                    }

                    Db.ADDT29.Remove(T29);

                    Db.ADDT29.Where(a => a.GAME_NO == GAME_NO && a.ORDER_BY > T29.ORDER_BY)
                    .Update(x => new ADDT29 { ORDER_BY = x.ORDER_BY - 1 });

                    Db.ADDT30.Where(a => a.TITLE_SHOW_ID == TITLE_SHOW_ID).Delete();
                }
                else
                {
                    if (IsLive || IsUnLive)
                    {
                        Db.ADDT29.Where(a => a.GAME_NO == GAME_NO).Update(t => new ADDT29 { LIVE_STREAM = null });
                    }

                    T29.TITLE_SHOW_NAME = TITLE_SHOW_NAME;

                    if (T29.ORDER_BY.ToString() != ORDER_BY)
                    {
                        int IntORDER_BY = 0;
                        int.TryParse(ORDER_BY, out IntORDER_BY);

                        Db.ADDT29.Where(a => a.GAME_NO == GAME_NO && a.ORDER_BY == IntORDER_BY).Update(t => new ADDT29 { ORDER_BY = T29.ORDER_BY });

                        T29.ORDER_BY = IntORDER_BY;
                    }

                    if (IsLive)
                    {
                        T29.LIVE_STREAM = true;
                    }
                }

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                }

                ts.Complete();
            }

            return true;
        }

        public bool SaveAllBuskerCashData(string GAME_NO, UserProfile user, ref string Message, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                var T29List = Db.ADDT29.Where(a => a.GAME_NO == GAME_NO && a.CASH == null && (a.LIVE_STREAM ?? false) == false).ToList();
                if (T29List == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆此資";
                    return false;
                }

                foreach (var item in T29List)
                {
                    var listBusker = (from a in Db.ADDT30
                                      join b in Db.ADDT27 on a.TEMP_USER_ID equals b.TEMP_USER_ID
                                      where a.TITLE_SHOW_ID == item.TITLE_SHOW_ID
                                      select b).ToList();

                    if (listBusker.Count() == 0)
                    {
                        Message = "系統發生錯誤;原因:找不到此筆此資";
                        return false;
                    }

                    int TITLE_SHOW_Count = Db.ADDT31.Where(a => a.GAME_NO == GAME_NO && a.TITLE_SHOW_ID == item.TITLE_SHOW_ID).Count();

                    if (TITLE_SHOW_Count > 0)
                    {
                        string KeyNo = DateTime.Now.ToString("yyyyMMddHHmmss");
                        string LOG_ID = string.Empty;

                        string LogDesc = string.Empty;
                        bool IsPayOK = false;

                        int CASH = 0;

                        if (TITLE_SHOW_Count >= T26.LIKE_COUNT)
                        {
                            CASH = (int)T26.LIKE_COUNT;
                        }
                        else
                        {
                            CASH = TITLE_SHOW_Count;
                        }

                        foreach (var Cre in listBusker)
                        {
                            LogDesc = T26.GAME_NAME + $"-街頭藝人表現優異給於 {CASH.ToString()} 點數";
                            IsPayOK = CameAddCash(Cre.TEMP_USER_ID, Cre.SCHOOL_NO, Cre.GAME_NO, KeyNo, CASH, LogDesc, ref Message, ref Db, out LOG_ID);

                            if (!IsPayOK)
                            {
                                return false;
                            }
                        }

                        item.CASH = (short)CASH;
                        item.CHG_PERSON = user.USER_KEY;
                        item.CHG_DATE = DateTime.Now;
                        Db.Entry(item).State = System.Data.Entity.EntityState.Modified;

                        try
                        {
                            Db.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            Message = "系統發生錯誤;原因:" + ex.Message;
                        }
                    }
                }

                ts.Complete();
            }

            return true;
        }

        public bool SaveBuskerCashData(string GAME_NO, string TITLE_SHOW_ID, short CASH, UserProfile user, ref string Message, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                var T29 = Db.ADDT29.Where(a => a.TITLE_SHOW_ID == TITLE_SHOW_ID).FirstOrDefault();
                if (T29 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆此資";
                    return false;
                }

                var listBusker = (from a in Db.ADDT30
                                  join b in Db.ADDT27 on a.TEMP_USER_ID equals b.TEMP_USER_ID
                                  where a.TITLE_SHOW_ID == TITLE_SHOW_ID
                                  select b).ToList();

                if (listBusker.Count() == 0)
                {
                    Message = "系統發生錯誤;原因:找不到此筆此資";
                    return false;
                }

                string KeyNo = DateTime.Now.ToString("yyyyMMddHHmmss");
                string LOG_ID = string.Empty;

                string LogDesc = string.Empty;
                bool IsPayOK = false;

                foreach (var Cre in listBusker)
                {
                    if ((T29.CASH ?? 0) != 0)
                    {
                        LogDesc = T26.GAME_NAME + $"-街頭藝人表現優異給於點數，因修改點數，追回上次已給的點數{T29.CASH}";
                        IsPayOK = CameAddCash(Cre.TEMP_USER_ID, Cre.SCHOOL_NO, Cre.GAME_NO, KeyNo, (T29.CASH * -1), LogDesc, ref Message, ref Db, out LOG_ID);

                        if (!IsPayOK)
                        {
                            return false;
                        }
                    }

                    LogDesc = T26.GAME_NAME + $"-街頭藝人表現優異給於 {CASH.ToString()} 點數";
                    IsPayOK = CameAddCash(Cre.TEMP_USER_ID, Cre.SCHOOL_NO, Cre.GAME_NO, KeyNo, CASH, LogDesc, ref Message, ref Db, out LOG_ID);

                    if (!IsPayOK)
                    {
                        return false;
                    }
                }

                T29.CASH = CASH;
                T29.CHG_PERSON = user.USER_KEY;
                T29.CHG_DATE = DateTime.Now;
                Db.Entry(T29).State = System.Data.Entity.EntityState.Modified;

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                }

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 編輯時帶入資料
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public GameBuskerEditViewModel GetBuskerEditData(GameBuskerEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var T26 = db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();

                model.Main = (from a in db.ADDT29
                              where a.GAME_NO == model.Search.WhereGAME_NO
                              && a.TITLE_SHOW_ID == model.WhereTITLE_SHOW_ID
                              select new GameBuskerMainViewModel()
                              {
                                  TITLE_SHOW_ID = a.TITLE_SHOW_ID,
                                  GAME_NO = a.GAME_NO,
                                  TITLE_SHOW_NAME = a.TITLE_SHOW_NAME,
                                  ORDER_BY = a.ORDER_BY,
                                  CRE_PERSON = a.CRE_PERSON,
                                  CRE_DATE = a.CRE_DATE,
                                  CHG_PERSON = a.CHG_PERSON,
                                  CHG_DATE = a.CHG_DATE,
                                  TITLE_IMG = a.TITLE_IMG,
                                  LIVE_STREAM = a.LIVE_STREAM,
                                  CASH = a.CASH,
                              }).FirstOrDefault();

                if (model.Main != null)
                {
                    if (!string.IsNullOrWhiteSpace(model.Main.TITLE_IMG))
                    {
                        model.Main.TITLE_IMG = UrlCustomHelper.Url_Content(GetSetDirectoryGamePath(T26.SCHOOL_NO, T26.GAME_NO) + @"\title_of_show\" + model.Main.TITLE_IMG);
                    }
                    else
                    {
                        model.Main.TITLE_IMG = UrlCustomHelper.Url_Content(@"~\Content\img\concert.svg");
                    }

                    model.Details = (from a in db.ADDT30
                                     join b in db.ADDT27 on a.TEMP_USER_ID equals b.TEMP_USER_ID
                                     where a.TITLE_SHOW_ID == model.Main.TITLE_SHOW_ID
                                     select new GameBuskerEditDetailsViewModel()
                                     {
                                         BUSKER_ID = a.BUSKER_ID,
                                         BUSKER_ITEM = a.BUSKER_ITEM,
                                         TITLE_SHOW_ID = a.TITLE_SHOW_ID,
                                         TEMP_USER_ID = a.TEMP_USER_ID,
                                         GAME_USER_ID = b.GAME_USER_ID,
                                         SCHOOL_NO = b.SCHOOL_NO,
                                         SHORT_NAME = b.SHORT_NAME,
                                         GAME_USER_TYPE = b.GAME_USER_TYPE,
                                         GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC,
                                         USER_NO = b.USER_NO,
                                         NAME = b.NAME,
                                         GRADE = b.GRADE,
                                         CLASS_NO = b.CLASS_NO,
                                         SEAT_NO = b.SEAT_NO,
                                         PHONE = b.PHONE,
                                         CHG_DATE=a.CHG_DATE
                                     }).OrderBy(a =>a.CHG_DATE).ToList();
                    
                }

                return model;
            }
        }

        public GameBuskerOpenPersonViewViewModel GetBuskerOpenPersonData(GameBuskerOpenPersonViewViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var listUser = db.ADDT30.Where(a => a.TITLE_SHOW_ID == model.WhereTITLE_SHOW_ID).Select(a => a.TEMP_USER_ID).ToList();
                var NowlistUser = model.Details?.Select(a => a.TEMP_USER_ID).ToList();

                var Temp = (from b in db.ADDT27
                            where b.GAME_NO == model.Search.WhereGAME_NO
                            select new GameBuskerEditDetailsViewModel()
                            {
                                TEMP_USER_ID = b.TEMP_USER_ID,
                                GAME_USER_ID = b.GAME_USER_ID,
                                SCHOOL_NO = b.SCHOOL_NO,
                                SHORT_NAME = b.SHORT_NAME,
                                GAME_USER_TYPE = b.GAME_USER_TYPE,
                                GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC,
                                USER_NO = b.USER_NO,
                                NAME = b.NAME,
                                GRADE = b.GRADE,
                                CLASS_NO = b.CLASS_NO,
                                SEAT_NO = b.SEAT_NO,
                                PHONE = b.PHONE,
                            });

                var QTemp = Temp;

                if (listUser != null)
                {
                    QTemp = QTemp.Where(b => !listUser.Any(x => x == b.TEMP_USER_ID));
                }

                if (NowlistUser != null)
                {
                    QTemp = QTemp.Where(b => !NowlistUser.Any(x => x == b.TEMP_USER_ID));
                }

                if (!string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
                {
                    QTemp = QTemp.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO);
                }

                if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
                {
                    QTemp = QTemp.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
                }

                if (!string.IsNullOrWhiteSpace(model.WhereNAME))
                {
                    QTemp = QTemp.Where(a => a.NAME.Contains(model.WhereNAME));
                }

                model.PersonData = QTemp.OrderBy(a => a.GAME_USER_TYPE).ThenBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.TEMP_USER_ID).ToList();

                var DataSelectList = (from b in db.ADDT27
                                      where b.GAME_NO == model.Search.WhereGAME_NO
                                      select new
                                      {
                                          SCHOOL_NO = b.SCHOOL_NO,
                                          SHORT_NAME = b.SHORT_NAME,
                                          CLASS_NO = b.CLASS_NO,
                                      }).Distinct().ToList();

                List<SelectListItem> SchoolNoSelectItem = new List<SelectListItem>();
                SchoolNoSelectItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) });

                SchoolNoSelectItem.AddRange(Temp.Select(a => new { a.SCHOOL_NO, a.SHORT_NAME }).Distinct()
                    .Select(x => new SelectListItem() { Text = x.SHORT_NAME, Value = x.SCHOOL_NO, Selected = x.SCHOOL_NO == model.WhereSCHOOL_NO }).ToList());

                model.SchoolNoSelectItem = SchoolNoSelectItem;

                List<SelectListItem> ClassItems = new List<SelectListItem>();
                ClassItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereCLASS_NO) });

                ClassItems.AddRange(Temp.Select(a => new { a.CLASS_NO }).Distinct()
                   .Select(x => new SelectListItem() { Text = x.CLASS_NO, Value = x.CLASS_NO, Selected = x.CLASS_NO == model.WhereCLASS_NO }).ToList());

                model.ClassItems = ClassItems;

                return model;
            }
        }

        public GameBuskerEditDetailsViewModel GetBuskerAddPersonData(string GAME_NO, string TEMP_USER_ID, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                GameBuskerEditDetailsViewModel model = new GameBuskerEditDetailsViewModel();

                model = (from b in db.ADDT27
                         where b.GAME_NO == GAME_NO && b.TEMP_USER_ID == TEMP_USER_ID
                         select new GameBuskerEditDetailsViewModel()
                         {
                             TEMP_USER_ID = b.TEMP_USER_ID,
                             GAME_USER_ID = b.GAME_USER_ID,
                             SCHOOL_NO = b.SCHOOL_NO,
                             SHORT_NAME = b.SHORT_NAME,
                             GAME_USER_TYPE = b.GAME_USER_TYPE,
                             GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC,
                             USER_NO = b.USER_NO,
                             NAME = b.NAME,
                             GRADE = b.GRADE,
                             CLASS_NO = b.CLASS_NO,
                             SEAT_NO = b.SEAT_NO,
                             PHONE = b.PHONE,
                         }).FirstOrDefault();

                return model;
            }
        }

        /// <summary>
        /// 取得目前 Live
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public string GetLive_StreamByKey(string GAME_NO, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                return Db.ADDT29.Where(a => a.GAME_NO == GAME_NO && a.LIVE_STREAM == true).Select(a => a.TITLE_SHOW_ID).FirstOrDefault() ?? string.Empty;
            }
        }

        /// <summary>
        /// 取得 刷卡 闖關後台資訊
        /// </summary>
        /// <param name="OK"></param>
        /// <param name="GAME_NO"></param>
        /// <param name="LEVEL_NO"></param>
        /// <param name="LEVEL_TYPE"></param>
        /// <param name="Message"></param>
        /// <param name="ActionName"></param>
        /// <param name="LOG_ID"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public GamePassedViewViewModel GetIsPassed(bool OK, string GAME_NO, string LEVEL_NO, string LEVEL_TYPE, string Message, string ActionName, string LOG_ID, ref ECOOL_DEVEntities Db)
        {
            GamePassedViewViewModel model = new GamePassedViewViewModel();
            model.IsOK = OK;
            model.GAME_NO = GAME_NO;
            model.LEVEL_NO = LEVEL_NO;
            model.LEVEL_TYPE = LEVEL_TYPE;
            model.Message = Message;
            model.ActionName = ActionName;

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                if (LEVEL_NO != null)
                {
                    model.PASSED_TIME = Db.ADDT26_D.Where(A => A.LEVEL_NO == LEVEL_NO).FirstOrDefault().PASSED_TIME;
                }
                else
                {
                    model.PASSED_TIME = Db.ADDT26_D.Where(A => A.GAME_NO == GAME_NO && A.LEVEL_TYPE == LEVEL_TYPE).FirstOrDefault().PASSED_TIME;
                }

                if (!string.IsNullOrWhiteSpace(LOG_ID))
                {
                    var Log = Db.ADDT28.Where(a => a.LOG_ID == LOG_ID).FirstOrDefault();

                    if (Log != null)
                    {
                        model.CASH_IN = (int)Log.CASH_IN;
                        model.CASH_AVAILABLE = (int)Log.LOG_CASH_AVAILABLE;

                        var User = Db.ADDT27.Where(a => a.TEMP_USER_ID == Log.TEMP_USER_ID).FirstOrDefault();

                        if (User != null)
                        {
                            model.NAME = User.NAME;
                            model.USER_TYPE = User.GAME_USER_TYPE;
                            model.GAME_USER_TYPE_DESC = User.GAME_USER_TYPE_DESC ?? UserType.GetDesc(User.GAME_USER_TYPE);
                        }
                    }
                }
            }

            return model;
        }

        /// <summary>
        /// 前台 現況查詢 感應/掃卡機
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public GameCashQueryViewModel CashQueryData(GameCashQueryViewModel model, ref ECOOL_DEVEntities Db, ref string Message, bool IsRecommendLevelData)
        {
            if (model == null) model = new GameCashQueryViewModel();

            if ((!string.IsNullOrWhiteSpace(model.GameUserID) || !string.IsNullOrWhiteSpace(model.TEMP_USER_ID)) && !string.IsNullOrWhiteSpace(model.GAME_NO))
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).AsNoTracking().NoLock(x => x.FirstOrDefault());

                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return model;
                }

                ADDT27 T27 = null;

                if (!string.IsNullOrWhiteSpace(model.TEMP_USER_ID))
                {
                    T27 = Db.ADDT27.Where(a => a.TEMP_USER_ID == model.TEMP_USER_ID && a.GAME_NO == T26.GAME_NO).AsNoTracking().NoLock(x => x.FirstOrDefault());
                }
                else
                {
                    string GAME_USER_ID = this.GetUserId(T26, model.GameUserID, ref Db, ref Message);

                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return model;
                    }

                    T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == GAME_USER_ID && a.GAME_NO == T26.GAME_NO && a.STATUS == (sbyte)ADDT27.StatusVal.使用中).AsNoTracking().NoLock(x => x.FirstOrDefault());
                }

                if (T27 == null)
                {
                    Message = "您未報到，請先行至報到處「報到」";
                    return model;
                }

                model.GameInfo = T26;

                model.User = T27;

                if (T26.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答 && model.FromSourcePage != GameCashQueryViewModel.SourcePage.QueryUserGameData)
                {
                    var MAns = Db.ADDT26_MAns.Where(a => a.GAME_NO == T26.GAME_NO && a.IS_LAST_SCORE == true && a.TEMP_USER_ID == T27.TEMP_USER_ID).FirstOrDefault();

                    if (MAns != null)
                    {
                        if (MAns.STATUS == (byte)ADDT26_MAns.AnsStatus.回答完畢)
                        {
                            if (MAns.SCORE >= 100)
                            {
                                MAns.STATUS = (byte)ADDT26_MAns.AnsStatus.完成此次活動;
                            }
                            else
                            {
                                MAns.STATUS = (byte)ADDT26_MAns.AnsStatus.取得成績未完成此次活動;
                            }

                            MAns.IS_LAST_SCORE = false;

                            Db.Entry(MAns).State = System.Data.Entity.EntityState.Modified;

                            try
                            {
                                Db.SaveChanges();
                            }
                            catch (Exception ex)
                            {
                                throw ex;
                            }
                        }
                    }
                }

                model.CASH_AVAILABLE = T27.CASH_AVAILABLE;

                model.CashDetails = GetPersonCashLogIntoData(T27.TEMP_USER_ID, Db);

                if (T26.GAME_TYPE == (byte)ADDT26.GameType.一般)
                {
                    model.LotteryDetails = GetPersonLitteryData(T27.TEMP_USER_ID, Db);
                    model.MeLevelDetails = GetPersonLevelData(T26.GAME_NO, T27.TEMP_USER_ID, Db);

                    model.PrizeLevel = GamePrizeLevelViewModel(T26.GAME_NO, T27.TEMP_USER_ID, Db);

                    if (IsRecommendLevelData)
                    {
                        RecommendLevelData(model);
                    }

                    model.LevelPassCount = model.MeLevelDetails?.Where(a => a.MeIsLevelPass == true).Count() ?? 0;
                    model.UnLevelPassCount = model.MeLevelDetails?.Where(a => a.MeIsLevelPass == false).Count() ?? 0;
                }
                else if (T26.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                {
                    var MAnsList = Db.ADDT26_MAns.Where(a => a.GAME_NO == T26.GAME_NO && a.TEMP_USER_ID == T27.TEMP_USER_ID).OrderByDescending(a => a.CRE_DATE).ToListNoLock();

                    //取出最後一次答題
                    var LastAns = MAnsList.OrderByDescending(a => a.CRE_DATE).FirstOrDefault();
                    string answerId = string.Empty;

                    if (LastAns != null)
                    {
                        answerId = LastAns.ANSWER_ID;

                        model.REWARD_DESC = (from a in Db.ADDT26_R
                                             where a.GAME_NO == T26.GAME_NO && a.RATE_S <= (decimal?)LastAns.SCORE && a.RATE_E >= (decimal?)LastAns.SCORE
                                             orderby a.RATE_E descending
                                             select a.REWARD_DESC).NoLock(x => x.FirstOrDefault());
                    }

                    string sSQL = $@"Select Q.GAME_NO,q.GROUP_ID,Q.G_SUBJECT,Q.G_ORDER_BY,D.LEVEL_NO,D.LEVEL_NAME,D.TRUE_ANS
                                    ,(Case When D.LEVEL_NO is not null Then 1 else 0 end) as MeIsLevelPass,B.RETURN_DESC
                                    from ADDT26_Q Q (nolock)
                                    left outer join  ADDT26_DAns D (nolock) on Q.GROUP_ID = D.GROUP_ID and D.ANSWER_ID = @ANSWER_ID
                                    left outer join ADDT26_D B  (nolock) on D.LEVEL_NO = B.LEVEL_NO
                                    where Q.GAME_NO = @GAME_NO
                                    order by Q.G_ORDER_BY ";
                    model.MeQALevel = Db.Database.Connection.Query<GameMeQALevelViewModel>(sSQL
                    , new
                    {
                        GAME_NO = T26.GAME_NO,
                        ANSWER_ID = answerId
                    }).ToList();

                    //已完成關卡
                    model.LevelPassCount = model.MeQALevel.Where(a => a.MeIsLevelPass).Count();

                    //未完成
                    model.UnLevelPassCount = model.MeQALevel.Count() - model.LevelPassCount;

                    //作答歷史記錄
                    model.MeMAns = MAnsList.Where(a => a.STATUS != (byte)ADDT26_MAns.AnsStatus.作答中).ToList();
                }
            }

            return model;
        }

        /// <summary>
        /// 判斷這個活動是否正常
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public ADDT26 GetIsGAME(string GAME_NO, ref ECOOL_DEVEntities Db, ref string Message, string CookleGameNo = null, bool IsCheckCookie = false )
        {
            if (string.IsNullOrWhiteSpace(GAME_NO) && IsCheckCookie == false)
            {
                Message = $"系統逾時，請重新登入(a)";
                return null;
            }
            else if (IsCheckCookie == true)
            {
                if (string.IsNullOrWhiteSpace(CookleGameNo))
                {
                    Message = $"系統逾時，請重新登入(b)";
                    return null;
                }
                else if (string.IsNullOrWhiteSpace(GAME_NO) && !string.IsNullOrWhiteSpace(CookleGameNo))
                {
                    GAME_NO = CookleGameNo;
                }
                else if (!string.IsNullOrWhiteSpace(GAME_NO) && !string.IsNullOrWhiteSpace(CookleGameNo))
                {
                    if (GAME_NO != CookleGameNo)
                    {
                        Message = $"系統逾時，請重新登入(c)";
                        return null;
                    }
                }
            }

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var T26 = Db.ADDT26.Find(GAME_NO);

                if (T26 == null)
                {
                    Message = $"找不到此資料，KEY:{GAME_NO}";
                    return null;
                }
                else
                {
                    if (DateTime.Now < T26.GAME_DATES)
                    {
                        Message = $"活動未開始";
                    }

                    if (DateTime.Now > T26.GAME_DATEE)
                    {
                        Message = $"活動已結束";
                    }

                    return T26;
                }
            }
        }

        public int GetApplyCash(string GAME_NO, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                int CASH = Db.ADDT26_D.Where(a => a.GAME_NO == GAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).Select(a => a.CASH).FirstOrDefault() ?? 0;

                return CASH;
            }
        }

        /// <summary>
        /// 產生 街頭藝人 按讚~QR CODE  或 查詢 現況  QR CODE
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="GAME_NO"></param>
        public void GenerateQR(string SCHOOL_NO, string GAME_NO, string Stype)
        {
            var bw = new ZXing.BarcodeWriter();

            var encOptions = new ZXing.Common.EncodingOptions
            {
                Margin = 1,
                Width = 1024,
                Height = 1024,
                PureBarcode = false,
            };

            encOptions.Hints.Add(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            encOptions.Hints.Add(EncodeHintType.CHARACTER_SET, "UTF-8");

            Uri contextUri = HttpContext.Current.Request.Url;

            var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
            contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port) + HttpContext.Current.Request.ApplicationPath;

            string ValueStr = string.Empty;

            if (Stype == "BuskerLikeView")
            {
                ValueStr = baseUri + @"/Game/BuskerLikeView" + $"?GAME_NO={GAME_NO}&UnApply=true";
            }
            else
            {
                ValueStr = baseUri + @"/Game/QueryUserDataList" + $"?GAME_NO={GAME_NO}";
            }

            bw.Renderer = new BitmapRenderer();
            bw.Options = encOptions;
            bw.Format = ZXing.BarcodeFormat.QR_CODE;
            Bitmap bm = bw.Write(ValueStr);

            if (Stype == "BuskerLikeView")
            {
                Bitmap overlay = new Bitmap(HttpContext.Current.Server.MapPath("~/Content/img/GLike.png"));

                if ((overlay.Width) > (200 * 0.4) || (overlay.Height) > (200 * 0.4))
                {
                    int NewW = Convert.ToInt16(200 * 0.4);
                    int NewH = Convert.ToInt16(200 * 0.4);

                    overlay = ScaleImage(overlay, NewW, NewH);
                }

                int deltaHeigth = bm.Height - overlay.Height;
                int deltaWidth = bm.Width - overlay.Width;

                Graphics g = Graphics.FromImage(bm);
                g.DrawImage(overlay, new System.Drawing.Point(deltaWidth / 2, deltaHeigth / 2));
                overlay.Dispose();
            }

            var tempPath = GetSysGamePath(SCHOOL_NO, GAME_NO);
            if (Directory.Exists(tempPath) == false)
            {
                Directory.CreateDirectory(tempPath);
            }

            string QRCodeLikeImg = tempPath + "QRCodeLike.jpg";

            if (Stype == "BuskerLikeView")
            {
                QRCodeLikeImg = tempPath + "QRCodeLike.jpg";
            }
            else
            {
                QRCodeLikeImg = tempPath + "QRCodeUserData.jpg";
            }

            if (System.IO.File.Exists(QRCodeLikeImg))
            {
                System.IO.File.Delete(QRCodeLikeImg);
            }

            bm.Save(QRCodeLikeImg, ImageFormat.Jpeg);
            bm.Dispose();
        }

        /// <summary>
        /// 圖片縮放處理
        /// </summary>
        /// <param name="image"></param>
        /// <param name="maxWidth"></param>
        /// <param name="maxHeight"></param>
        /// <returns></returns>
        public Bitmap ScaleImage(Bitmap image, int maxWidth, int maxHeight)
        {
            var ratioX = (double)maxWidth / image.Width;
            var ratioY = (double)maxHeight / image.Height;
            var ratio = Math.Min(ratioX, ratioY);

            var newWidth = (int)(image.Width * ratio);
            var newHeight = (int)(image.Height * ratio);

            var newImage = new Bitmap(maxWidth, maxWidth);
            using (var graphics = Graphics.FromImage(newImage))
            {
                // Calculate x and y which center the image
                int y = (maxHeight / 2) - newHeight / 2;
                int x = (maxWidth / 2) - newWidth / 2;

                // Draw image on x and y with newWidth and newHeight
                graphics.DrawImage(image, x, y, newWidth, newHeight);
            }

            return newImage;
        }

        /// <summary>
        /// 虛擬 Path
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="ART_GALLERY_NO"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysGamePath(string SCHOOL_NO, string GAME_NO, string FileName)
        {
            string TempPath = GetSetDirectoryGamePath(SCHOOL_NO, GAME_NO) + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return UrlCustomHelper.Url_Content(TempPath);
            }

            return string.Empty;
        }

        /// <summary>
        /// 取得實際電腦路徑
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="GAME_NO"></param>
        /// <returns></returns>
        public string GetSysGamePath(string SCHOOL_NO, string GAME_NO)
        {
            return HttpContext.Current.Server.MapPath(GetSetDirectoryGamePath()) + @"\" + SCHOOL_NO + @"\" + GAME_NO + @"\";
        }

        /// <summary>
        /// 上傳圖片 處理
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="GAME_NO"></param>
        /// <param name="file"></param>
        /// <param name="Message"></param>
        /// <param name="LEVEL_ITEM"></param>
        /// <returns></returns>
        public string UpLoadFile(string SCHOOL_NO, string GAME_NO, HttpPostedFileBase file, ref string Message, string LEVEL_ITEM = null)
        {
            string fileName = string.Empty;

            try
            {
                string tempPath = GetSysGamePath(SCHOOL_NO, GAME_NO);

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }

                if (file != null && file.ContentLength > 0)
                {
                    fileName = Path.GetFileName(file.FileName);

                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                    if (regexCode.IsMatch(fileName.ToLower()) == false)
                    {
                        Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                        return null;
                    }

                    if (file.ContentLength / 1024 > (1024 * 20)) // 20MB
                    {
                        Message = "上傳檔案不能超過20MB";
                        return null;
                    }

                    fileName = string.IsNullOrWhiteSpace(LEVEL_ITEM) ? GAME_NO + Path.GetExtension(fileName) : LEVEL_ITEM + Path.GetExtension(fileName);

                    string UpLoadFile = tempPath + "\\" + fileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    file.SaveAs(UpLoadFile);
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }

            return fileName;
        }

        /// <summary>
        /// 刪除 上傳圖片 處理
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="GAME_NO"></param>
        /// <param name="fileName"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool DelFile(string SCHOOL_NO, string GAME_NO, string fileName, ref string Message)
        {
            try
            {
                string tempPath = GetSysGamePath(SCHOOL_NO, GAME_NO);

                string DelFilePath = tempPath + "\\" + fileName;

                if (System.IO.File.Exists(DelFilePath))
                {
                    System.IO.File.Delete(DelFilePath);
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }

            return true;
        }

        /// <summary>
        /// 點數異動處理
        /// </summary>
        /// <param name="TEMP_USER_ID"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="GAME_NO"></param>
        /// <param name="SOURCE_NO"></param>
        /// <param name="CashIn"></param>
        /// <param name="LogDesc"></param>
        /// <param name="Message"></param>
        /// <param name="db"></param>
        /// <param name="LOG_ID"></param>
        /// <returns></returns>
        public bool CameAddCash1(string TEMP_USER_ID, string SCHOOL_NO, string GAME_NO, string SOURCE_NO, int? CashIn, string LogDesc, AWAT01 UserCash, ref string Message, ref ECOOL_DEVEntities db, out string LOG_ID)
        {
            LOG_ID = string.Empty;

            //ADDT27 aw27 = db.ADDT27.Where(u => u.TEMP_USER_ID == TEMP_USER_ID ).FirstOrDefault();

            //var EntityTemp = ((IObjectContextAdapter)db).ObjectContext.ObjectStateManager.GetObjectStateEntries(System.Data.Entity.EntityState.Added).Where(a => a.Entity is ADDT27).Select(a => (ADDT27)a.Entity).Where(u => u.TEMP_USER_ID == TEMP_USER_ID).FirstOrDefault();
            //if (EntityTemp != null)
            //{
            //    aw27 = EntityTemp;
            //}

            if (UserCash != null)
            {
                if (UserCash.CASH_ALL.HasValue == false) UserCash.CASH_ALL = 0;
                if (UserCash.CASH_AVAILABLE.HasValue == false) UserCash.CASH_AVAILABLE = 0;

                //if (aw27.GAME_USER_TYPE == UserType.Student)
                //{
                //    AWAT01 wAT01 = db.AWAT01.Where(u => u.SCHOOL_NO == aw27.SCHOOL_NO && u.USER_NO == aw27.USER_NO).FirstOrDefault();

                //    aw27.CASH_ALL = wAT01.CASH_ALL;
                //    aw27.CASH_AVAILABLE = wAT01.CASH_AVAILABLE;
                //}

                ADDT28 aw1_log = new ADDT28();

                aw1_log.LOG_ID = Guid.NewGuid().ToString("N");
                aw1_log.TEMP_USER_ID = TEMP_USER_ID;
                aw1_log.SCHOOL_NO = SCHOOL_NO;
                aw1_log.GAME_NO = GAME_NO;
                aw1_log.SOURCE_NO = SOURCE_NO;
                aw1_log.CASH_IN = (short?)CashIn;
                aw1_log.LOG_TIME = DateTime.Now;
                aw1_log.LOG_DESC = LogDesc;

                aw1_log.UNLOG_CASH_ALL = UserCash.CASH_ALL;
                aw1_log.UNLOG_CASH_AVAILABLE = UserCash.CASH_AVAILABLE;

                //累加這次點數
                //if (CashIn > 0)
                //{
                //    UserCash.CASH_ALL = UserCash.CASH_ALL.Value + CashIn;
                //}
                //else
                //{
                //    UserCash.CASH_ALL = UserCash.CASH_ALL.Value;
                //}

                //UserCash.CASH_AVAILABLE = UserCash.CASH_AVAILABLE.Value + CashIn;
                int CHECKPrice = 0;
                int CASH_ALL = 0;
                CHECKPrice = (int)UserCash.CASH_AVAILABLE.Value +(int) CashIn;
                CASH_ALL= (int)UserCash.CASH_ALL.Value + (int)CashIn;
                if (CHECKPrice < 0)
                {
                    Message = $"餘額不足!您目前點數為{aw1_log.UNLOG_CASH_AVAILABLE}點";
                    return false;
                }

                aw1_log.LOG_CASH_ALL = CASH_ALL;
                aw1_log.LOG_CASH_AVAILABLE = CHECKPrice;

                db.ADDT28.Add(aw1_log);

                LOG_ID = aw1_log.LOG_ID;
            }

            return true;
        }

        /// <summary>
        /// 點數異動處理
        /// </summary>
        /// <param name="TEMP_USER_ID"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="GAME_NO"></param>
        /// <param name="SOURCE_NO"></param>
        /// <param name="CashIn"></param>
        /// <param name="LogDesc"></param>
        /// <param name="Message"></param>
        /// <param name="db"></param>
        /// <param name="LOG_ID"></param>
        /// <returns></returns>
        public bool CameAddCash(string TEMP_USER_ID, string SCHOOL_NO, string GAME_NO, string SOURCE_NO, int? CashIn, string LogDesc, ref string Message, ref ECOOL_DEVEntities db, out string LOG_ID)
        {
            LOG_ID = string.Empty;

            ADDT27 aw27 = db.ADDT27.Where(u => u.TEMP_USER_ID == TEMP_USER_ID).FirstOrDefault();

            var EntityTemp = ((IObjectContextAdapter)db).ObjectContext.ObjectStateManager.GetObjectStateEntries(System.Data.Entity.EntityState.Added).Where(a => a.Entity is ADDT27).Select(a => (ADDT27)a.Entity).Where(u => u.TEMP_USER_ID == TEMP_USER_ID).FirstOrDefault();
            if (EntityTemp != null)
            {
                aw27 = EntityTemp;
            }

            if (aw27 != null)
            {
                if (aw27.CASH_ALL.HasValue == false) aw27.CASH_ALL = 0;
                if (aw27.CASH_AVAILABLE.HasValue == false) aw27.CASH_AVAILABLE = 0;

                //if (aw27.GAME_USER_TYPE == UserType.Student)
                //{
                //    AWAT01 wAT01 = db.AWAT01.Where(u => u.SCHOOL_NO == aw27.SCHOOL_NO && u.USER_NO == aw27.USER_NO).FirstOrDefault();

                //    aw27.CASH_ALL = wAT01.CASH_ALL;
                //    aw27.CASH_AVAILABLE = wAT01.CASH_AVAILABLE;
                //}

                ADDT28 aw1_log = new ADDT28();

                aw1_log.LOG_ID = Guid.NewGuid().ToString("N");
                aw1_log.TEMP_USER_ID = TEMP_USER_ID;
                aw1_log.SCHOOL_NO = SCHOOL_NO;
                aw1_log.GAME_NO = GAME_NO;
                aw1_log.SOURCE_NO = SOURCE_NO;
                aw1_log.CASH_IN = (short?)CashIn;
                aw1_log.LOG_TIME = DateTime.Now;
                aw1_log.LOG_DESC = LogDesc;

                aw1_log.UNLOG_CASH_ALL = aw27.CASH_ALL;
                aw1_log.UNLOG_CASH_AVAILABLE = aw27.CASH_AVAILABLE;

                //累加這次點數
                if (CashIn > 0)
                {
                    aw27.CASH_ALL = aw27.CASH_ALL.Value + CashIn;
                }
                else
                {
                    aw27.CASH_ALL = aw27.CASH_ALL.Value;
                }

                aw27.CASH_AVAILABLE = aw27.CASH_AVAILABLE.Value + CashIn;

                if (aw27.CASH_AVAILABLE < 0)
                {
                    Message = $"餘額不足!您目前點數為{aw1_log.UNLOG_CASH_AVAILABLE}點";
                    return false;
                }

                aw1_log.LOG_CASH_ALL = aw27.CASH_ALL;
                aw1_log.LOG_CASH_AVAILABLE = aw27.CASH_AVAILABLE;

                db.ADDT28.Add(aw1_log);

                LOG_ID = aw1_log.LOG_ID;
            }
            db.SaveChanges();
            return true;
        }

        /// <summary>
        /// 街頭藝人 前台 刷卡報名處理
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public GameBuskerAddDetailsViewModel GetgameBuskerUser(GameBuskerAddViewModel model, ref ECOOL_DEVEntities Db, ref string Message)
        {
            try
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).AsNoTracking().FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return null;
                }

                string GAME_USER_ID = this.GetUserId(T26, model.GameUserID, ref Db, ref Message);

                if (!string.IsNullOrWhiteSpace(Message))
                {
                    return null;
                }

                var T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == GAME_USER_ID && a.GAME_NO == T26.GAME_NO && a.STATUS == (sbyte)ADDT27.StatusVal.使用中).AsNoTracking().FirstOrDefault();
                if (T27 == null)
                {
                    Message = "您未報到，請先行至報到處「報到」";
                    return null;
                }

                if (model?.Details?.Count() > 0)
                {
                    if (model.Details.Where(a => a.TEMP_USER_ID == T27.TEMP_USER_ID).Any())
                    {
                        Message = "您已在這個隊伍裡，請勿重覆登記";
                        return null;
                    }
                }

                GameBuskerAddDetailsViewModel ReturnModel = new GameBuskerAddDetailsViewModel
                {
                    TEMP_USER_ID = T27.TEMP_USER_ID,
                    GAME_USER_ID = T27.GAME_USER_ID,
                    NAME = T27.NAME
                };

                return ReturnModel;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return null;
            }
        }

        /// <summary>
        /// 新增抽獎中獎名單 抽出人員來
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public GameLotteryCreViewModel GetLotteryPeopleData(GameLotteryCreViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                string sSQL = string.Empty;

                sSQL = @"select  CONVERT(varchar(36),NewID()) as OrderBy,b.TEMP_USER_ID,b.NAME
                        ,b.GAME_USER_TYPE,b.GAME_USER_TYPE_DESC,b.PHONE,b.USER_NO,b.SCHOOL_NO, b.SHORT_NAME
                        ,b.SNAME,b.SEX,b.GRADE,b.CLASS_NO,b.SEAT_NO,b.GAME_USER_ID
                        from  ADDT27 b (nolock)
                        where b.GAME_NO = @GAME_NO ";
                sSQL = sSQL + $@" and b.STATUS = {(byte)ADDT27.StatusVal.使用中} ";
                sSQL = sSQL + $@" and b.GAME_USER_TYPE !='{UserType.VIP}' ";

                if ((model.Main.LEVEL_COUNT ?? 0) > 0)
                {
                    sSQL = sSQL + $@"and b.TEMP_USER_ID in (select a.TEMP_USER_ID
					                                        from ADDT28 a (nolock)
					                                        join ADDT26_D d  (nolock) on a.SOURCE_NO=d.LEVEL_NO and d.LEVEL_TYPE ='{ADDT26_D.LevelType.Pay}'
					                                        where d.GAME_NO=@GAME_NO
					                                        group by a.TEMP_USER_ID
					                                        HAVING count(DISTINCT a.SOURCE_NO)>=@LEVEL_COUNT) ";
                }

                if (!string.IsNullOrWhiteSpace(model.Main.LEVEL))
                {
                    string[] ArrLevel = new string[] { };

                    if (model.Main.LEVEL == SharedGlobal.ALL)
                    {
                        ArrLevel = Db.ADDT26_D.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Pay).OrderBy(a => a.LEVEL_NO).Select(x => x.LEVEL_NO).ToArray();
                    }
                    else
                    {
                        ArrLevel = model.Main.LEVEL.Split(',');
                    }

                    string sSQLLevel = string.Empty;
                    foreach (var item in ArrLevel)
                    {
                        if (!string.IsNullOrWhiteSpace(sSQLLevel))
                        {
                            sSQLLevel = sSQLLevel + " union all ";
                        }

                        sSQLLevel = sSQLLevel + $@" select DISTINCT a.TEMP_USER_ID,1 as Num
															from ADDT28 a
															where  a.GAME_NO='" + model.Search.WhereGAME_NO + "' and a.SOURCE_NO='" + item + "' ";
                    }

                    sSQL = sSQL + $@" and b.TEMP_USER_ID in ( select T1.TEMP_USER_ID from (" + sSQLLevel + ") as T1 group by T1.TEMP_USER_ID HAVING Sum(t1.Num)>= " + ArrLevel.Length + " )";
                }

                if (model.Main.UNCUEST)
                {
                    sSQL = sSQL + $" and  b.GAME_USER_TYPE !='{UserType.Guest}'";
                }

                if (model.Main.UNLOTTERY)
                {
                    sSQL = sSQL + $@" and b.TEMP_USER_ID not in (Select p.TEMP_USER_ID
                                   from ADDT33 p (nolock)
                                   join ADDT32 m  (nolock) on p.LOTTERY_NO=m.LOTTERY_NO
                                   where m.GAME_NO = @GAME_NO )";
                }

                if (model.Main.IS_FULL)
                {
                    sSQL = sSQL + $@" and b.TEMP_USER_ID in (select m.TEMP_USER_ID
                                      from ADDT26_MAns m  (nolock)
                                      where m.GAME_NO=@GAME_NO and m.SCORE>=100)";
                }

                sSQL = sSQL + " order by OrderBy";

                IQueryable<GameLotteryPeopleViewModel> Lottery = Db.Database.Connection.Query<GameLotteryPeopleViewModel>(sSQL
                , new
                {
                    GAME_NO = model.Search.WhereGAME_NO,
                    MaxLimit = (int)model.Main.PEOPLE_COUNT,
                    LEVEL_COUNT = model.Main.LEVEL_COUNT ?? 0
                }).AsQueryable();

                // model.People =  Lottery.OrderBy(a=>a.OrderBy).Take((int)model.Main.PEOPLE_COUNT).ToList();

                RandomGame<GameLotteryPeopleViewModel> rg = new RandomGame<GameLotteryPeopleViewModel>(Lottery.ToList(),
                (h1) =>
                {
                    return !string.IsNullOrEmpty(h1.CLASS_NO) ?
                        h1.SCHOOL_NO + h1.CLASS_NO :
                         h1.SCHOOL_NO + h1.GAME_USER_TYPE;
                });
                var result = rg.LuckyDraw((int)model.Main.PEOPLE_COUNT);

                if (result.Count() < model.Main.PEOPLE_COUNT)
                {
                    int? ThisCount = model.Main.PEOPLE_COUNT - result.Count();

                    result.AddRange(Lottery.Where(a => !result.Any(b => b.TEMP_USER_ID == a.TEMP_USER_ID)).OrderBy(i => i.OrderBy).Take((int)ThisCount).ToList());
                }

                model.People = result;
            }

            return model;
        }

        /// <summary>
        /// 中獎條件 - 限制關卡數
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="SelectedVal"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public List<SelectListItem> GetLevelItem(string GAME_NO, string SelectedVal, ref ECOOL_DEVEntities Db)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            string[] sArray = (SelectedVal ?? "").Split(',');

            SelectItem.Add(new SelectListItem() { Text = "全部通過", Value = "ALL", Selected = SelectedVal == "ALL" });
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var Item = Db.ADDT26_D.Where(a => a.GAME_NO == GAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Pay).OrderBy(a => a.LEVEL_ITEM).Select(x => new SelectListItem { Text = x.LEVEL_NAME, Value = x.LEVEL_NO, Selected = sArray.Contains(x.LEVEL_NO) }).ToList();
                SelectItem.AddRange(Item);
            }

            return SelectItem;
        }

        /// <summary>
        /// 中獎條件 - 限制關卡數
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="SelectedVal"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public List<SelectListItem> GetLevelCountItem(string GAME_NO, int? SelectedVal, ref ECOOL_DEVEntities Db)
        {
            string StrSelectedVal = string.Empty;

            if (SelectedVal != null)
            {
                StrSelectedVal = SelectedVal.ToString();
            }

            List<SelectListItem> SelectItem = new List<SelectListItem>();

            SelectItem.Add(new SelectListItem() { Text = $"無限制", Value = string.Empty, Selected = StrSelectedVal == string.Empty });

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                for (int i = 1; i <= Db.ADDT26_D.Where(a => a.GAME_NO == GAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Pay).Count(); i++)
                {
                    SelectItem.Add(new SelectListItem() { Text = $"{i}關以上(含)", Value = i.ToString(), Selected = StrSelectedVal == i.ToString() });
                }
            }

            return SelectItem;
        }

        public bool SaveLotteryDelData(GameLotteryViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            var Del = Db.ADDT32.Where(x => x.LOTTERY_NO == model.Search.WhereLOTTERY_NO).FirstOrDefault();

            if (Del == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆資料";
                return false;
            }

            if (Del.STATUS == ((byte)ADDT32.StatusVal.作廢).ToString())
            {
                Message = "系統發生錯誤;原因:此筆資料已作廢";
                return false;
            }

            Del.STATUS = ((byte)ADDT32.StatusVal.作廢).ToString();

            Db.Entry(Del).State = System.Data.Entity.EntityState.Modified;

            try
            {
                Db.SaveChanges();
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }

            return true;
        }

        /// <summary>
        /// 新增抽獎中獎名單 Save
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <param name="OutLOTTERY_NO"></param>
        /// <returns></returns>
        public bool SaveLotteryData(GameLotteryCreViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message, out string OutLOTTERY_NO)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    OutLOTTERY_NO = string.Empty;
                    return false;
                }

                ADDT32 dDT32 = SaveADDT32Data(model, user, Db);

                if ((model.Main.IS_GIVE_UP_LOTTERY == false && (model.LotteryType == (byte)ADDT32.LotteryTypeVal.儲存及進入開始抽獎畫面 || model.LotteryType == (byte)ADDT32.LotteryTypeVal.進入開始抽獎畫面))
                     || (model.Main.IS_GIVE_UP_LOTTERY == true && model.LotteryType == (byte)ADDT32.LotteryTypeVal.可重抽模式儲存抽獎結果))
                {
                    dDT32.STATUS = ((int)ADDT32.StatusVal.已抽獎).ToString();

                    int Item = 0;

                    List<ADDT33> listCreD = new List<ADDT33>();

                    foreach (var item in model.People)
                    {
                        Item++;

                        ADDT33 CreD = new ADDT33
                        {
                            LOTTERY_NO = dDT32.LOTTERY_NO,
                            ITEM_NO = Item.ToString(),
                            TEMP_USER_ID = item.TEMP_USER_ID,
                            SCHOOL_NO = item.SCHOOL_NO ?? "",
                            USER_NO = item.USER_NO,
                            GAME_USER_TYPE = item.GAME_USER_TYPE,
                            NAME = item.NAME,
                            SNAME = item.SNAME,
                            SEX = item.SEX,
                            GRADE = item.GRADE,
                            CLASS_NO = item.CLASS_NO,
                            SEAT_NO = item.SEAT_NO,
                            PHONE = item.PHONE
                        };

                        listCreD.Add(CreD);
                        Db.ADDT33.Add(CreD);
                    }

                   // EFBatchOperation.For(Db, Db.ADDT33).InsertAll(listCreD);
                }

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    OutLOTTERY_NO = string.Empty;
                    return false;
                }

                OutLOTTERY_NO = dDT32.LOTTERY_NO;
                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 批次報名(有e酷幣帳號)
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <param name="batchApply"></param>
        /// <returns></returns>
        public bool SaveBatchApplyStudent(BatchApplyStudentViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message, ref BatchApplyStudentResultViewModel batchApply)
        {
            Db.Database.Connection.Open();
            using (TransactionScope ts = new TransactionScope())
            {
                if (Db.REFT01_Q.Where(a => a.REF_TABLE == "ADDT27" && a.REF_KEY == model.REF_KEY).Any() == false)
                {
                    Message = "您未選取任何對象";
                    batchApply = null;
                    return false;
                }

                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    batchApply = null;
                    return false;
                }

                var T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).FirstOrDefault();
                if (T26_D == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動有報名的關卡";
                    batchApply = null;
                    return false;
                }

                string Sou_No = model.Search.WhereGAME_NO + DateTime.Now.ToString("yyyyMMddHHmmss");

                try
                {
                    REFT01Service.UpdateStatus(user.USER_KEY, "ADDT27", model.REF_KEY, Sou_No, (Db.Database.Connection) as SqlConnection);
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    batchApply = null;
                    return false;
                }

                var NowT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).ToList();

                var TempSql = (from a in Db.REFT01
                               join b in Db.HRMT01 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                               where a.REF_TABLE == "ADDT27" && a.REF_KEY == Sou_No && b.CARD_NO != null && b.CARD_NO != ""
                               && b.USER_TYPE == UserType.Student
                               select new GameLevelPersonDetailsViewModel
                               {
                                   GAME_USER_ID = b.CARD_NO,
                                   SCHOOL_NO = b.SCHOOL_NO,
                                   GAME_NO = model.Search.WhereGAME_NO,
                                   GAME_USER_TYPE = b.USER_TYPE,
                                   USER_NO = b.USER_NO,
                                   NAME = b.NAME,
                                   SNAME = b.SNAME,
                                   SEX = b.SEX,
                                   GRADE = b.GRADE,
                                   CLASS_NO = b.CLASS_NO,
                                   SEAT_NO = b.SEAT_NO,
                                   PHONE = b.PHONE,
                               });

                var Temp = TempSql.ToList();

                var listT27 = new List<ADDT27>();

                var bdmt01s = Db.BDMT01.ToList();

                foreach (var item in Temp.Where(b => !NowT27.Where(x => x.SCHOOL_NO == b.SCHOOL_NO && x.USER_NO == b.USER_NO).Any()))
                {
                    ADDT27 Cre = new ADDT27();

                    Cre.TEMP_USER_ID = Guid.NewGuid().ToString("N");
                    Cre.GAME_USER_ID = item.GAME_USER_ID;
                    Cre.SCHOOL_NO = item.SCHOOL_NO;
                    Cre.SHORT_NAME = bdmt01s.Where(a => a.SCHOOL_NO == item.SCHOOL_NO).Select(a => a.SHORT_NAME).FirstOrDefault() ?? "";
                    Cre.GAME_NO = item.GAME_NO;
                    Cre.GAME_USER_TYPE = item.GAME_USER_TYPE;
                    Cre.USER_NO = item.USER_NO;
                    Cre.NAME = item.NAME;
                    Cre.SNAME = item.SNAME;
                    Cre.SEX = item.SEX;
                    Cre.GRADE = item.GRADE;
                    Cre.CLASS_NO = item.CLASS_NO;
                    Cre.SEAT_NO = item.SEAT_NO;
                    Cre.PHONE = item.PHONE;
                    Cre.CASH_ALL = T26_D.CASH;
                    Cre.CASH_AVAILABLE = T26_D.CASH;
                    Cre.CRE_PERSON = user.USER_KEY;
                    Cre.CRE_DATE = DateTime.Now;
                    Cre.CHG_PERSON = user.USER_KEY;
                    Cre.CHG_DATE = DateTime.Now;
                    Cre.STATUS = (byte)ADDT27.StatusVal.使用中;

                    listT27.Add(Cre);
                    Db.ADDT27.Add(Cre);
                }

                if (listT27.Count() == 0)
                {
                    Message = "系統發生錯誤;原因:您選取的對象個人資料沒有一筆有數位卡號!!";
                    batchApply = null;
                    return false;
                }

                //EFBatchOperation.For(Db, Db.ADDT27).InsertAll(listT27);

                var listT28 = new List<ADDT28>();

                foreach (var item in listT27)
                {
                    ADDT28 log = new ADDT28
                    {
                        LOG_ID = Guid.NewGuid().ToString("N"),
                        TEMP_USER_ID = item.TEMP_USER_ID,
                        SCHOOL_NO = item.SCHOOL_NO,
                        GAME_NO = item.GAME_NO,
                        SOURCE_NO = T26_D.LEVEL_NO,
                        CASH_IN = (short)T26_D.CASH,
                        LOG_TIME = DateTime.Now,
                        LOG_DESC = T26.GAME_NAME + BatchWorkTypeVal.GetDesc(BatchWorkTypeVal.BatchApplyStudent),
                        LOG_CASH_ALL = (short)T26_D.CASH,
                        LOG_CASH_AVAILABLE = (short)T26_D.CASH,
                        UNLOG_CASH_ALL = 0,
                        UNLOG_CASH_AVAILABLE = 0
                    };
                    listT28.Add(log);
                    Db.ADDT28.Add(log);
                }

               // EFBatchOperation.For(Db, Db.ADDT28).InsertAll(listT28);

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    batchApply = null;
                    return false;
                }

                batchApply.Search = model.Search;
                batchApply.OK_LIST = (from b in listT27
                                      select new GameLevelPersonDetailsViewModel
                                      {
                                          GAME_USER_ID = b.GAME_USER_ID,
                                          SCHOOL_NO = b.SCHOOL_NO,
                                          SHORT_NAME = b.SHORT_NAME,
                                          GAME_NO = model.Search.WhereGAME_NO,
                                          GAME_USER_TYPE = b.GAME_USER_TYPE,
                                          USER_NO = b.USER_NO,
                                          NAME = b.NAME,
                                          SNAME = b.SNAME,
                                          SEX = b.SEX,
                                          GRADE = b.GRADE,
                                          CLASS_NO = b.CLASS_NO,
                                          SEAT_NO = b.SEAT_NO,
                                          PHONE = b.PHONE,
                                      }).OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.GAME_USER_TYPE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToList();

                List<GameLevelPersonDetailsViewModel> NG_LIST = new List<GameLevelPersonDetailsViewModel>();

                NG_LIST.AddRange((from a in Db.REFT01
                                  join b in Db.HRMT01 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                                  join c in Db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                                  where a.REF_TABLE == "ADDT27" && a.REF_KEY == Sou_No && (b.CARD_NO == null || b.CARD_NO == "")
                                  && b.USER_TYPE == UserType.Student
                                  select new GameLevelPersonDetailsViewModel
                                  {
                                      GAME_USER_ID = b.CARD_NO,
                                      SCHOOL_NO = b.SCHOOL_NO,
                                      SHORT_NAME = c.SHORT_NAME,
                                      GAME_NO = model.Search.WhereGAME_NO,
                                      GAME_USER_TYPE = b.USER_TYPE,
                                      USER_NO = b.USER_NO,
                                      NAME = b.NAME,
                                      SNAME = b.SNAME,
                                      SEX = b.SEX,
                                      GRADE = b.GRADE,
                                      CLASS_NO = b.CLASS_NO,
                                      SEAT_NO = b.SEAT_NO,
                                      PHONE = b.PHONE,
                                      ERROR = "無數位學生證/晶片卡號",
                                  }).OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToList());

                NG_LIST.AddRange(Temp.Where(b => NowT27.Where(x => x.SCHOOL_NO == b.SCHOOL_NO && x.USER_NO == b.USER_NO).Any()).Select(
                    a =>
                    {
                        a.ERROR = "原資料已報名";
                        return a;
                    }
                ).ToList());

                batchApply.NG_LIST = NG_LIST;

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 單筆報名(卡片) Save
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <param name="batchApply"></param>
        /// <returns></returns>
        public bool SaveOneApplyCard(OneApplyCardViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message, ref BatchApplyStudentResultViewModel batchApply)
        {
            ADDT26 T26 = null;
            ADDT26_D T26_D = null;

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).AsNoTracking().FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    batchApply = null;
                    return false;
                }

                T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).AsNoTracking().FirstOrDefault();
                if (T26_D == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動有報名的關卡";
                    batchApply = null;
                    return false;
                }
            }

            using (TransactionScope ts = new TransactionScope())
            {
                List<GameLevelPersonDetailsViewModel> NG_LIST = new List<GameLevelPersonDetailsViewModel>();
                List<ADDT27> listT27 = new List<ADDT27>();

                try
                {
                    if (!string.IsNullOrWhiteSpace(model.Person.GAME_USER_ID))
                    {
                        if (model.Person.GAME_USER_ID.Length == 10)
                        {
                            var NowT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.GAME_USER_ID == model.Person.GAME_USER_ID && a.STATUS == (byte)ADDT27.StatusVal.使用中).FirstOrDefault();

                            if (NowT27 != null)
                            {
                                NowT27.STATUS = (byte)ADDT27.StatusVal.停用;

                                Db.Entry(NowT27).State = System.Data.Entity.EntityState.Modified;
                            }

                            ADDT27 Cre = new ADDT27();
                            Cre.TEMP_USER_ID = Guid.NewGuid().ToString("N");
                            Cre.GAME_USER_ID = model.Person.GAME_USER_ID;
                            Cre.SCHOOL_NO = Db.BDMT01.Where(a => a.SHORT_NAME == model.Person.SHORT_NAME).Select(a => a.SCHOOL_NO).FirstOrDefault() ?? "";
                            Cre.SHORT_NAME = model.Person.SHORT_NAME;
                            Cre.GAME_NO = T26.GAME_NO;
                            Cre.GAME_USER_TYPE = UserType.Guest;
                            Cre.USER_NO = "";
                            Cre.NAME = model.Person.NAME;
                            Cre.SNAME = model.Person.NAME;
                            Cre.PHONE = model.Person.PHONE;
                            Cre.SEX = model.Person.SEX;
                            Cre.GRADE = model.Person.GRADE;
                            Cre.CLASS_NO = model.Person.CLASS_NO;
                            Cre.SEAT_NO = model.Person.SEAT_NO;
                            Cre.CASH_ALL = T26_D.CASH;
                            Cre.CASH_AVAILABLE = T26_D.CASH;
                            Cre.CRE_PERSON = user.USER_KEY;
                            Cre.CRE_DATE = DateTime.Now;
                            Cre.CHG_PERSON = user.USER_KEY;
                            Cre.CHG_DATE = DateTime.Now;
                            Cre.GAME_USER_TYPE_DESC = model.Person.GAME_USER_TYPE_DESC;
                            Cre.STATUS = (byte)ADDT27.StatusVal.使用中;
                            listT27.Add(Cre);
                            Db.ADDT27.Add(Cre);
                        }
                        else
                        {
                            string ERROR = string.Empty;

                            if (model.Person.GAME_USER_ID.Length != 10)
                            {
                                ERROR = "此卡號長度不足１０碼";
                            }

                            GameLevelPersonDetailsViewModel ErrModel = new GameLevelPersonDetailsViewModel
                            {
                                GAME_USER_ID = model.Person.GAME_USER_ID,
                                SCHOOL_NO = Db.BDMT01.Where(a => a.SHORT_NAME == model.Person.SHORT_NAME).Select(a => a.SCHOOL_NO).FirstOrDefault() ?? "",
                                SHORT_NAME = model.Person.SHORT_NAME,
                                GAME_NO = T26.GAME_NO,
                                GAME_USER_TYPE = UserType.Guest,
                                USER_NO = "",
                                NAME = model.Person.NAME,
                                SNAME = model.Person.NAME,
                                PHONE = model.Person.PHONE,
                                SEX = model.Person.SEX,
                                GRADE = model.Person.GRADE,
                                CLASS_NO = model.Person.CLASS_NO,
                                SEAT_NO = model.Person.SEAT_NO,
                                GAME_USER_TYPE_DESC = model.Person.GAME_USER_TYPE_DESC,
                                ERROR = ERROR,
                            };
                            NG_LIST.Add(ErrModel);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    batchApply = null;
                    return false;
                }

                if (listT27.Count() == 0)
                {
                    Message = "錯誤;原因:如下";
                    batchApply.Search = model.Search;
                    batchApply.NG_LIST = NG_LIST;
                    return false;
                }

              //  EFBatchOperation.For(Db, Db.ADDT27).InsertAll(listT27);

                var listT28 = new List<ADDT28>();

                foreach (var item in listT27)
                {
                    ADDT28 log = new ADDT28
                    {
                        LOG_ID = Guid.NewGuid().ToString("N"),
                        TEMP_USER_ID = item.TEMP_USER_ID,
                        SCHOOL_NO = item.SCHOOL_NO,
                        GAME_NO = item.GAME_NO,
                        SOURCE_NO = T26_D.LEVEL_NO,
                        CASH_IN = (short)T26_D.CASH,
                        LOG_TIME = DateTime.Now,
                        LOG_DESC = T26.GAME_NAME + BatchWorkTypeVal.GetDesc(BatchWorkTypeVal.BatchApplyCard),
                        LOG_CASH_ALL = (short)T26_D.CASH,
                        LOG_CASH_AVAILABLE = (short)T26_D.CASH,
                        UNLOG_CASH_ALL = 0,
                        UNLOG_CASH_AVAILABLE = 0
                    };
                    listT28.Add(log);
                    Db.ADDT28.Add(log);
                }

               // EFBatchOperation.For(Db, Db.ADDT28).InsertAll(listT28);

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    batchApply = null;
                    return false;
                }

                batchApply.Search = model.Search;
                batchApply.OK_LIST = (from b in listT27
                                      select new GameLevelPersonDetailsViewModel
                                      {
                                          GAME_USER_ID = b.GAME_USER_ID,
                                          SCHOOL_NO = b.SCHOOL_NO,
                                          GAME_NO = model.Search.WhereGAME_NO,
                                          GAME_USER_TYPE = b.GAME_USER_TYPE,
                                          USER_NO = b.USER_NO,
                                          NAME = b.NAME,
                                          SNAME = b.SNAME,
                                          SEX = b.SEX,
                                          GRADE = b.GRADE,
                                          CLASS_NO = b.CLASS_NO,
                                          SEAT_NO = b.SEAT_NO,
                                          PHONE = b.PHONE,
                                          GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC
                                      }).ToList();

                batchApply.NG_LIST = NG_LIST;

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 統計表 關卡圖表 相關資料
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="IsStatistics"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public GameListLevelPersonCountViewModel GetLevelPersonCountData(string GAME_NO, bool IsStatistics, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                GameListLevelPersonCountViewModel model = new GameListLevelPersonCountViewModel();
                model.GAME_NO = GAME_NO;
                model.IsStatistics = IsStatistics;

                //報名人數
                model.Total_Count = Db.ADDT27.Where(a => a.GAME_NO == GAME_NO && a.STATUS != (byte)ADDT27.StatusVal.未使用).Count();

                model.Student_Total_Count = Db.ADDT27.Where(a => a.GAME_NO == GAME_NO && a.GAME_USER_TYPE == UserType.Student && a.STATUS != (byte)ADDT27.StatusVal.未使用).Count();
                model.Other_Total_Count = Db.ADDT27.Where(a => a.GAME_NO == GAME_NO && a.GAME_USER_TYPE != UserType.Student && a.STATUS != (byte)ADDT27.StatusVal.未使用).Count();

                string sSQL = string.Empty;

                if (model.IsStatistics)
                {
                    //本活動使用次數、本活動使用點數
                    sSQL = $@" select
                                sum(case when (b.LEVEL_TYPE in ('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}')) or (b.LEVEL_TYPE is null and a.LOG_DESC like '%街頭藝人%') then 1 else 0 end) as Use_Total_Count
                                ,sum(case when b.LEVEL_TYPE in ('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}') then 1 else 0 end) as Lv_Use_Total_Count
                                ,sum(case when b.LEVEL_TYPE is null and a.LOG_DESC like '%街頭藝人%'  then 1 else 0 end) as Busker_Use_Total_Count
                                ,sum(case when a.CASH_IN < 0 and  (b.LEVEL_TYPE in ('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}')) then a.CASH_IN * -1
		                                  when a.CASH_IN > 0 and  (b.LEVEL_TYPE in ('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}')) then a.CASH_IN
                                          else 0 end) as Lv_Use_Total_Cash
                                ,sum(case when (b.LEVEL_TYPE is null and a.LOG_DESC like '%街頭藝人%') then a.CASH_IN  else 0 end) as Busker_Use_Total_Cash
                                from ADDT28 a  (nolock)
                                left outer join ADDT26_D b(nolock) on  b.GAME_NO=a.GAME_NO and b.LEVEL_NO=a.SOURCE_NO
                                where a.SOURCE_NO not in (select b.LEVEL_NO from ADDT26_D b(nolock) where b.GAME_NO= @GAME_NO and b.LEVEL_TYPE  in ('{ADDT26_D.LevelType.Apply}', '{ADDT26_D.LevelType.Test}'))
							    and a.GAME_NO = @GAME_NO ";
                    model.UseTotal = Db.Database.Connection.Query<GameUseTotalViewModel>(sSQL
                     , new
                     {
                         GAME_NO
                     }).FirstOrDefault();
                }

                if (model.UseTotal != null)
                {
                    model.UseTotal.Use_Total_Cash = model.UseTotal.Lv_Use_Total_Cash + model.UseTotal.Busker_Use_Total_Cash;
                }

                //此關卡闖關次數

                if (model.IsStatistics)
                {
                    sSQL = $@"select a.*,(select count(*) from ADDT28 s (nolock) where s.SOURCE_NO=a.LEVEL_NO) as Number_Count
                            from ADDT26_D a (nolock)
                            where a.LEVEL_TYPE in ('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}')
                            and a.GAME_NO=@GAME_NO
                            order by a.LEVEL_ITEM";
                    model.ListNumberCountData = Db.Database.Connection.Query<GameLevelPersonCountViewModel>(sSQL
                    , new
                    {
                        GAME_NO
                    }).ToList();

                    model.ListNumberCountData = model.ListNumberCountData.Select(
                     a =>
                     {
                         a.Number_CASH = a.Number_Count * Math.Abs((int)a.CASH);
                         a.RATE = model.UseTotal.Use_Total_Count > 0 ? Math.Round((a.Number_Count / model.UseTotal.Use_Total_Count) * 100) : 0;
                         return a;
                     }
                    ).ToList();
                }

                //此關卡闖關人數
                sSQL = $@"select a.*,(select count(DISTINCT s.TEMP_USER_ID) from ADDT28 s (nolock) where s.SOURCE_NO=a.LEVEL_NO) as Person_Count
                            from ADDT26_D a (nolock)
                            where a.LEVEL_TYPE in ('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}')
                            and a.GAME_NO=@GAME_NO
                            order by a.LEVEL_ITEM";

                model.ListData = Db.Database.Connection.Query<GameLevelPersonCountViewModel>(sSQL
                  , new
                  {
                      GAME_NO
                  }).ToList();

                model.ListData = model.ListData.Select(
               a =>
               {
                   a.RATE = model.Total_Count > 0 ? Math.Round((a.Person_Count / model.Total_Count) * 100) : 0;
                   return a;
               }
                ).ToList();

                //過關人數 完成1關...2..3關 人數

                sSQL = $@"select T1.Level_Count,count(*) Level_Count_Person
							from (
									select a.TEMP_USER_ID,count(DISTINCT a.SOURCE_NO) as Level_Count
					                from ADDT28 a (nolock)
					                join ADDT26_D d  (nolock) on a.SOURCE_NO=d.LEVEL_NO and d.LEVEL_TYPE in ('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}')
					                where d.GAME_NO=@GAME_NO
					                group by a.TEMP_USER_ID
							    ) as T1
								group by T1.Level_Count";
                var ListCountPassData = Db.Database.Connection.Query<GameLevelCountPassPersonViewModel>(sSQL
                , new
                {
                    GAME_NO
                }).ToList();

                model.ListCountPassData = new List<GameLevelCountPassPersonViewModel>();

                for (double i = 1; i <= model.ListData.Count(); i++)
                {
                    GameLevelCountPassPersonViewModel gameLevel = new GameLevelCountPassPersonViewModel
                    {
                        Level_Count = i,
                        Level_Count_Person = ListCountPassData.Where(a => a.Level_Count >= i).Select(a => a.Level_Count_Person).Sum(),
                    };
                    gameLevel.RATE = Math.Round((gameLevel.Level_Count_Person / model.Total_Count) * 100);
                    model.ListCountPassData.Add(gameLevel);
                }

                if (model.IsStatistics)
                {
                    model.CountPassCharts = GetCountPassCharts(model);
                }

                return model;
            }
        }

        /// <summary>
        /// 取得人員圖表相關資料
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="Db"></param>
        /// <param name="Level_Count"></param>
        /// <param name="LEVEL_NO"></param>
        /// <returns></returns>
        public GameListLevelPersonDetailsViewModel GetLevelPersonDetailsData(string GAME_NO, ref ECOOL_DEVEntities Db, double? Level_Count = null, string LEVEL_NO = null, string LEVEL_TYPE = null)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                GameListLevelPersonDetailsViewModel model = new GameListLevelPersonDetailsViewModel();

                string sSQL = string.Empty;
                model.GAME_NO = GAME_NO;
                model.Level_Count = Level_Count;
                model.LEVEL_NO = LEVEL_NO;

                if (Level_Count != null)
                {
                    sSQL = $@"select h.*
							from (
									select a.TEMP_USER_ID,count(DISTINCT a.SOURCE_NO) as Level_Count
					                from ADDT28 a (nolock)
					                join ADDT26_D d  (nolock) on a.SOURCE_NO=d.LEVEL_NO and d.LEVEL_TYPE in('{ADDT26_D.LevelType.Pay}','{ADDT26_D.LevelType.Prize}')
					                where d.GAME_NO=@GAME_NO
					                group by a.TEMP_USER_ID
							    ) as T1
							join ADDT27 h (nolock) on T1.TEMP_USER_ID = h.TEMP_USER_ID
                            where T1.Level_Count>=@Level_Count";
                }
                else if (!string.IsNullOrWhiteSpace(LEVEL_NO))
                {
                    if (!string.IsNullOrWhiteSpace(LEVEL_TYPE))
                    {
                        sSQL = $@"select h.*
                     ,(select count(*) from ADDT28 s where s.GAME_NO=h.GAME_NO and s.TEMP_USER_ID=h.TEMP_USER_ID and s.SOURCE_NO=@LEVEL_NO) as Number_Count
						 from (
									select DISTINCT a.TEMP_USER_ID, a.SOURCE_NO as LEVEL_NO
					                from ADDT28 a (nolock)
					                join ADDT26_D d  (nolock) on a.SOURCE_NO=d.LEVEL_NO and d.LEVEL_TYPE ='{LEVEL_TYPE}'
					                where d.GAME_NO=@GAME_NO and a.SOURCE_NO=@LEVEL_NO
							    ) as T1
					     join ADDT27 h (nolock) on T1.TEMP_USER_ID = h.TEMP_USER_ID";
                    }
                    else
                    {
                        sSQL = $@"select h.*
                     ,(select count(*) from ADDT28 s where s.GAME_NO=h.GAME_NO and s.TEMP_USER_ID=h.TEMP_USER_ID and s.SOURCE_NO=@LEVEL_NO) as Number_Count
						 from (
									select DISTINCT a.TEMP_USER_ID, a.SOURCE_NO as LEVEL_NO
					                from ADDT28 a (nolock)
					                join ADDT26_D d  (nolock) on a.SOURCE_NO=d.LEVEL_NO and d.LEVEL_TYPE ='{ADDT26_D.LevelType.Pay}'
					                where d.GAME_NO=@GAME_NO and a.SOURCE_NO=@LEVEL_NO
							    ) as T1
					     join ADDT27 h (nolock) on T1.TEMP_USER_ID = h.TEMP_USER_ID";
                    }
                }
                else
                {
                    sSQL = $@"select h.* from ADDT27 h (nolock) Where h.GAME_NO=@GAME_NO  ";
                }

                if (!string.IsNullOrEmpty(model.OrdercColumn))
                {
                    if (model.OrdercColumn == "NAME")
                    {
                        sSQL = sSQL + " order by h.NAME,h.SCHOOL_NO,h.GAME_USER_TYPE,h.CLASS_NO,h.SEAT_NO";
                    }
                    else if (model.OrdercColumn == "PHONE")
                    {
                        sSQL = sSQL + " order by h.PHONE,h.SCHOOL_NO,h.GAME_USER_TYPE,h.CLASS_NO,h.SEAT_NO,h.NAME";
                    }
                    else if (model.OrdercColumn == "GAME_USER_TYPE")
                    {
                        sSQL = sSQL + " order by h.GAME_USER_TYPE,h.SCHOOL_NO,h.CLASS_NO,h.SEAT_NO,h.NAME";
                    }
                    else if (model.OrdercColumn == "USER_NO")
                    {
                        sSQL = sSQL + " order by h.USER_NO,h.SCHOOL_NO,h.GAME_USER_TYPE,h.CLASS_NO,h.SEAT_NO,h.NAME";
                    }
                    else if (model.OrdercColumn == "Number_Count")
                    {
                        sSQL = sSQL + @" order by Number_Count Desc, " +
                            "h.USER_NO,h.SCHOOL_NO,h.GAME_USER_TYPE,h.CLASS_NO,h.SEAT_NO,h.NAME";
                    }
                }
                else
                {
                    sSQL = sSQL + " order by h.SCHOOL_NO,h.GAME_USER_TYPE,h.CLASS_NO,h.SEAT_NO,h.NAME";
                }

                model.Person = Db.Database.Connection.Query<GameLevelPersonDetailsViewModel>(sSQL
                 , new
                 {
                     GAME_NO,
                     Level_Count,
                     LEVEL_NO
                 }).ToList();

                return model;
            }
        }

        /// <summary>
        /// 有獎徵答-統計分析
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public GameStatisticsAnsViewModel GetStatisticsAnsData(GameStatisticsAnsViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.GameInfo = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).AsNoTracking().FirstOrDefault();

                if (model.GameInfo != null)
                {
                    var MAns = Db.ADDT26_MAns.Where(a => a.GAME_NO == model.GameInfo.GAME_NO).AsNoTracking().ToList();

                    model.AnsTotalNum = MAns.Count();

                    model.AnsTrueNum = MAns.Where(a => a.SCORE >= 100).Count();

                    model.AnsFalseNum = model.AnsTotalNum - model.AnsTrueNum;

                    if (model.AnsTrueNum > 0)
                    {
                        model.AnsTruerRate = Math.Round(((Double)model.AnsTrueNum / (Double)model.AnsTotalNum * 100), 0);
                    }
                    else
                    {
                        model.AnsTruerRate = 0;
                    }

                    if (model.AnsTotalNum > 0)
                    {
                        model.AnsFalseNRate = 100 - model.AnsTruerRate;
                    }
                    else
                    {
                        model.AnsFalseNRate = 0;
                    }

                    string sSQL = $@" Select a.GAME_NO ,a.GROUP_ID,a.LEVEL_NO,a.LEVEL_NAME,a.LEVEL_ITEM,a.TRUE_ANS,count(b.ANSWER_ID) AnsNum
                                    ,Case When  (select count(*) from  ADDT26_DAns c (nolock) where a.GROUP_ID=c.GROUP_ID  and a.GAME_NO =c.GAME_NO ) >0 Then
			                                   round((count(b.ANSWER_ID) / ((select count(*) from  ADDT26_DAns c (nolock) where a.GROUP_ID=c.GROUP_ID  and a.GAME_NO =c.GAME_NO )* 1.00)) *100,0)
	                                      else 0 end as AnsRate
                                    from ADDT26_D a (nolock)
                                    left outer join ADDT26_DAns b  (nolock) on a.GROUP_ID=b.GROUP_ID and a.GAME_NO =b.GAME_NO and a.LEVEL_NO =b.LEVEL_NO
                                    where a.GAME_NO=@GAME_NO
                                    and a.LEVEL_TYPE='{ADDT26_D.LevelType.Pay}'
                                    group by  a.GAME_NO ,a.GROUP_ID,a.LEVEL_NO,a.LEVEL_NAME,a.LEVEL_ITEM,a.TRUE_ANS
                                    order by a.LEVEL_ITEM";
                    var AllRateData = Db.Database.Connection.Query<GameStatisticsAnsThisRateViewModel>(sSQL
                   , new
                   {
                       model.GameInfo.GAME_NO
                   }).ToList();

                    var AnsRate = (from a in Db.ADDT26_Q
                                   where a.GAME_NO == model.GameInfo.GAME_NO
                                   select new GameStatisticsAnsRateViewModel
                                   {
                                       GAME_NO = a.GAME_NO,
                                       GROUP_ID = a.GROUP_ID,
                                       G_SUBJECT = a.G_SUBJECT,
                                       G_ORDER_BY = a.G_ORDER_BY,
                                   }).AsNoTracking().OrderBy(a => a.G_ORDER_BY).ToList();

                    model.Rate = AnsRate.Select(
                      a =>
                      {
                          a.RateData = AllRateData.Where(b => b.GROUP_ID == a.GROUP_ID).ToList();
                          return a;
                      }
                     ).OrderBy(a => a.G_ORDER_BY).ToList();
                }

                ts.Complete();
                return model;
            }
        }

        public GameTotalGraphDetailsViewModel GetStatisticsAnsData(string GAME_NO, string GROUP_ID, string LEVEL_NO, bool IsSeeGroupId, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                GameTotalGraphDetailsViewModel model = new GameTotalGraphDetailsViewModel();
                model.IsSeeGroupId = IsSeeGroupId;

                model.GameInfo = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).AsNoTracking().FirstOrDefault();

                if (IsSeeGroupId)
                {
                    model.AnsQInfo = Db.ADDT26_Q.Where(a => a.GAME_NO == GAME_NO && a.GROUP_ID == GROUP_ID).AsNoTracking().FirstOrDefault();

                    model.AnsInfo = Db.ADDT26_D.Where(a => a.LEVEL_NO == LEVEL_NO).AsNoTracking().FirstOrDefault();

                    model.AnsPersons = (from a in Db.ADDT26_MAns
                                        join b in Db.ADDT26_DAns on a.ANSWER_ID equals b.ANSWER_ID
                                        where a.GAME_NO == GAME_NO && b.GROUP_ID == GROUP_ID && b.LEVEL_NO == LEVEL_NO
                                        select a).OrderBy(a => a.SHORT_NAME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenByDescending(a => a.CRE_DATE).AsNoTracking().ToList();
                }
                else
                {
                    var AnsPersons = (from a in Db.ADDT26_MAns
                                      where a.GAME_NO == GAME_NO
                                      select a).ToList();

                    model.AnsPersons = AnsPersons.OrderBy(a => (a.SCORE ?? 0)).ThenByDescending(a => a.CRE_DATE).ToList();
                }

                return model;
            }
        }

        public GameScoreListsViewModel GetScorePersonsData(GameScoreListsViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.GameInfo = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).AsNoTracking().FirstOrDefault();

                model.Q_DATA = Db.ADDT26_Q.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).AsNoTracking().OrderBy(a => a.G_ORDER_BY).ToList();

                var MAns = (from a in Db.ADDT26_MAns
                            where a.GAME_NO == model.GameInfo.GAME_NO
                            select new GameScoreListPersonsViewModel
                            {
                                ANSWER_ID = a.ANSWER_ID,
                                SCHOOL_NO = a.SCHOOL_NO,
                                SHORT_NAME = a.SHORT_NAME,
                                USER_NO = a.USER_NO,
                                CLASS_NO = a.CLASS_NO,
                                SEAT_NO = a.SEAT_NO,
                                NAME = a.NAME,
                                CRE_DATE = a.CRE_DATE,
                                SCORE = a.SCORE,
                                STATUS = a.STATUS,
                                IS_LAST_SCORE = a.IS_LAST_SCORE,
                                REWARD_CASH = a.REWARD_CASH ?? 0,
                            });

                if (!string.IsNullOrWhiteSpace(model.SCORE_TYPE))
                {
                    if (model.SCORE_TYPE == GameScoreListsViewModel.ScoreTypeVal.MaxScore)
                    {
                        //取出每個人最好的成績
                        string sSQL = $@" select (select top 1 b.ANSWER_ID from ADDT26_MAns b  (nolock) where b.SCORE =MAX(a.SCORE) and b.GAME_NO=a.GAME_NO and a.TEMP_USER_ID=b.TEMP_USER_ID order by b.CRE_DATE desc ) as ANSWER_ID
                                          from ADDT26_MAns a (nolock)
                                          where a.GAME_NO=@GAME_NO
                                          group by  a.GAME_NO,a.TEMP_USER_ID ";
                        var ArrAnswerId = Db.Database.Connection.Query<string>(sSQL
                        , new
                        {
                            GAME_NO = model.Search.WhereGAME_NO
                        }).ToArray();

                        if (ArrAnswerId.Length > 0)
                        {
                            MAns = MAns.Where(a => ArrAnswerId.Contains(a.ANSWER_ID));
                        }
                    }
                    else if (model.SCORE_TYPE == GameScoreListsViewModel.ScoreTypeVal.PassScore)
                    {
                        MAns = MAns.Where(a => a.SCORE >= 100);
                    }
                }

                if (!string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) && model.WhereSCHOOL_NO != SharedGlobal.ALL)
                {
                    MAns = MAns.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO);
                }

                if (!string.IsNullOrWhiteSpace(model.WhereNAME))
                {
                    MAns = MAns.Where(a => a.NAME.Contains(model.WhereNAME));
                }

                if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
                {
                    MAns = MAns.Where(a => a.USER_NO.Contains(model.WhereUSER_NO));
                }

                if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
                {
                    MAns = MAns.Where(a => a.CLASS_NO.Contains(model.WhereCLASS_NO));
                }

                if (!string.IsNullOrWhiteSpace(model.WhereYEAR))
                {
                    MAns = MAns.Where(a => a.CLASS_NO.Substring(0, 1) == model.WhereYEAR);
                }

                var DAns = Db.ADDT26_DAns.Where(a => a.GAME_NO == model.GameInfo.GAME_NO).AsNoTracking().ToList();

                var ListData = MAns.ToList().Select(
                a =>
                {
                    a.ScoreListData = DAns.Where(b => b.ANSWER_ID == a.ANSWER_ID).ToList();
                    return a;
                }
                );

                if (!string.IsNullOrWhiteSpace(model.OrdercColumn))
                {
                    if (model?.SyntaxName == "DESC")
                    {
                        ListData = ListData.OrderByDescending(p => GetPropertyValue(p, model.OrdercColumn)).ThenBy(a => a.SHORT_NAME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenByDescending(a => a.CRE_DATE);
                    }
                    else
                    {
                        ListData = ListData.OrderBy(p => GetPropertyValue(p, model.OrdercColumn)).ThenBy(a => a.SHORT_NAME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenByDescending(a => a.CRE_DATE);
                    }
                }
                else
                {
                    ListData = ListData.OrderBy(a => a.SHORT_NAME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenByDescending(a => a.CRE_DATE);
                }

                model.ListData = ListData.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

                return model;
            }
        }

        /// <summary>
        /// 取得此人員明細
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public GameEditPersonViewModel GetEditPersonData(GameEditPersonViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.Person = (from b in Db.ADDT27
                                where b.GAME_NO == model.Search.WhereGAME_NO && b.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID
                                select new OneApplyPersonViewModel
                                {
                                    TEMP_USER_ID = b.TEMP_USER_ID,
                                    GAME_USER_ID = b.GAME_USER_ID,
                                    GAME_USER_TYPE = b.GAME_USER_TYPE,
                                    GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC,
                                    NAME = b.NAME,
                                    PHONE = b.PHONE,
                                }).FirstOrDefault();

                if (model.Person != null)
                {
                    if (string.IsNullOrWhiteSpace(model.Person.GAME_USER_TYPE_DESC))
                    {
                        model.Person.GAME_USER_TYPE_DESC = UserType.GetDesc(model.Person.GAME_USER_TYPE);
                    }
                }

                return model;
            }
        }

        public BatchCashIntroToExcel GetBatchCashIntoDatatoExcel(BatchCashIntoIndexViewModel model, ref ECOOL_DEVEntities Db)
        {
            var Temp = (from b in Db.ADDT27
                        where b.GAME_NO == model.Search.WhereGAME_NO
                        select new GameLevelPersonDetailsViewModel
                        {
                            TEMP_USER_ID = b.TEMP_USER_ID,
                            GAME_USER_ID = b.GAME_USER_ID,
                            SCHOOL_NO = b.SCHOOL_NO,
                            SHORT_NAME = b.SHORT_NAME,
                            GAME_NO = model.Search.WhereGAME_NO,
                            GAME_USER_TYPE = b.GAME_USER_TYPE,
                            GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC,
                            USER_NO = b.USER_NO,
                            NAME = b.NAME,
                            SNAME = b.SNAME,
                            SEX = b.SEX,
                            GRADE = b.GRADE,
                            CLASS_NO = b.CLASS_NO,
                            SEAT_NO = b.SEAT_NO,
                            PHONE = b.PHONE,
                            CASH_AVAILABLE = b.CASH_AVAILABLE,
                            STATUS = (b.STATUS ?? (byte)ADDT27.StatusVal.使用中),
                        });

            if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereSCHOOL_NO) && model?.CashSearch?.WhereSCHOOL_NO != SharedGlobal.ALL)
            {
                Temp = Temp.Where(a => a.SCHOOL_NO == model.CashSearch.WhereSCHOOL_NO);
            }

            if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereCARD_NO))
            {
                Temp = Temp.Where(a => a.GAME_USER_ID.Contains(model.CashSearch.WhereCARD_NO.Trim()));
            }

            if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereNAME))
            {
                Temp = Temp.Where(a => a.NAME.Contains(model.CashSearch.WhereNAME.Trim()));
            }

            if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereCLASS_NO))
            {
                Temp = Temp.Where(a => a.CLASS_NO == model.CashSearch.WhereCLASS_NO);
            }

            if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereSEAT_NO))
            {
                Temp = Temp.Where(a => a.SEAT_NO == model.CashSearch.WhereSEAT_NO);
            }

            if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereGAME_USER_TYPE))
            {
                Temp = Temp.Where(a => a.GAME_USER_TYPE == model.CashSearch.WhereGAME_USER_TYPE);
            }

            if (model?.CashSearch?.WhereUnCashZero ?? false)
            {
                Temp = Temp.Where(a => a.CASH_AVAILABLE > 0);
            }

            Temp = Temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.GAME_USER_TYPE).ThenBy(a => a.CLASS_NO ?? "").ThenBy(a => (a.SEAT_NO ?? "").Length).ThenBy(a => a.SEAT_NO ?? "").ThenBy(a => a.NAME);
            BatchCashIntroToExcel model2 = new BatchCashIntroToExcel();
            model2.ListData = Temp.ToList();
            return model2;
        }

        /// <summary>
        /// 此活動 人員清單
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public BatchCashIntoIndexViewModel GetBatchCashIntoData(BatchCashIntoIndexViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var Temp = (from b in Db.ADDT27
                            where b.GAME_NO == model.Search.WhereGAME_NO
                            select new GameLevelPersonDetailsViewModel
                            {
                                TEMP_USER_ID = b.TEMP_USER_ID,
                                GAME_USER_ID = b.GAME_USER_ID,
                                SCHOOL_NO = b.SCHOOL_NO,
                                SHORT_NAME = b.SHORT_NAME,
                                GAME_NO = model.Search.WhereGAME_NO,
                                GAME_USER_TYPE = b.GAME_USER_TYPE,
                                GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC,
                                USER_NO = b.USER_NO,
                                NAME = b.NAME,
                                SNAME = b.SNAME,
                                SEX = b.SEX,
                                GRADE = b.GRADE,
                                CLASS_NO = b.CLASS_NO,
                                SEAT_NO = b.SEAT_NO,
                                PHONE = b.PHONE,
                                CASH_AVAILABLE = b.CASH_AVAILABLE,
                                STATUS = (b.STATUS ?? (byte)ADDT27.StatusVal.使用中),
                            });

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereSCHOOL_NO) && model?.CashSearch?.WhereSCHOOL_NO != SharedGlobal.ALL)
                {
                    Temp = Temp.Where(a => a.SCHOOL_NO == model.CashSearch.WhereSCHOOL_NO);
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereCARD_NO))
                {
                    Temp = Temp.Where(a => a.GAME_USER_ID.Contains(model.CashSearch.WhereCARD_NO.Trim()));
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereNAME))
                {
                    Temp = Temp.Where(a => a.NAME.Contains(model.CashSearch.WhereNAME.Trim()));
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereCLASS_NO))
                {
                    Temp = Temp.Where(a => a.CLASS_NO == model.CashSearch.WhereCLASS_NO);
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereSEAT_NO))
                {
                    Temp = Temp.Where(a => a.SEAT_NO == model.CashSearch.WhereSEAT_NO);
                }

                if (!string.IsNullOrWhiteSpace(model?.CashSearch?.WhereGAME_USER_TYPE))
                {
                    Temp = Temp.Where(a => a.GAME_USER_TYPE == model.CashSearch.WhereGAME_USER_TYPE);
                }

                if (model?.CashSearch?.WhereUnCashZero ?? false)
                {
                    Temp = Temp.Where(a => a.CASH_AVAILABLE > 0);
                }

                Temp = Temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.GAME_USER_TYPE).ThenBy(a => a.CLASS_NO ?? "").ThenBy(a => (a.SEAT_NO ?? "").Length).ThenBy(a => a.SEAT_NO ?? "").ThenBy(a => a.NAME);
                model.PageSize = Temp.Count();
                model.ListData = Temp.ToPagedList(model.CashSearch.Page > 0 ? model.CashSearch.Page - 1 : 0, model.PageSize);

                if (model.CheckBox == null) model.CheckBox = new List<BatchCashIntoCheckBoxViewModel>();

                return model;
            }
        }

        /// <summary>
        /// 人員現況查詢
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public GamePersonIntoViewModel GetPersonStatusInto(GamePersonIntoViewModel model, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.User = Db.ADDT27.Where(a => a.TEMP_USER_ID == model.TEMP_USER_ID).FirstOrDefault();

                if (model.User != null)
                {
                    model.CashDetails = GetPersonCashLogIntoData(model.User.TEMP_USER_ID, Db);

                    model.LotteryDetails = GetPersonLitteryData(model.User.TEMP_USER_ID, Db);
                    model.MeLevelDetails = GetPersonLevelData(model.GAME_NO, model.User.TEMP_USER_ID, Db);

                    model.LevelPassCount = model.MeLevelDetails?.Where(a => a.MeIsLevelPass == true).Count() ?? 0;
                    model.UnLevelPassCount = model.MeLevelDetails?.Where(a => a.MeIsLevelPass == false).Count() ?? 0;
                }
            }

            return model;
        }

        /// <summary>
        /// 餘額轉入功能的人員刪除及這個活動資料刪除
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveDelApplyData(BatchCashIntoIndexViewModel model, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var ListChk = model.CheckBox.Where(a => a.Chk == true).Select(a => a.TEMP_USER_ID).ToList();

                var listT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && ListChk.Contains(a.TEMP_USER_ID)).ToList();

                if (listT27.Count() > 0)
                {
                    var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();
                    if (T26 == null)
                    {
                        Message = "系統發生錯誤;原因:找不到此筆活動單號";
                        return false;
                    }

                    Db.ADDT28.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && ListChk.Contains(a.TEMP_USER_ID)).Delete();
                    Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && ListChk.Contains(a.TEMP_USER_ID)).Delete();

                    if (listT27.Count() >= Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Count())
                    {
                        (from a in Db.ADDT30
                         join b in Db.ADDT29 on a.TITLE_SHOW_ID equals b.TITLE_SHOW_ID
                         where b.GAME_NO == model.Search.WhereGAME_NO
                         select a).Delete();

                        var T29 = Db.ADDT29.Where(a => a.GAME_NO == model.Search.WhereGAME_NO);

                        if (T29.Any())
                        {
                            string TITLE_IMG_Path = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + $@"\{title_of_show}\";

                            try
                            {
                                if (Directory.Exists(TITLE_IMG_Path) == false)
                                {
                                    Directory.Delete(TITLE_IMG_Path, true);
                                }
                            }
                            catch (Exception)
                            {
                            }

                            T29.Delete();
                        }

                        Db.ADDT31.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                        (from a in Db.ADDT33
                         join b in Db.ADDT32 on a.LOTTERY_NO equals b.LOTTERY_NO
                         where b.GAME_NO == model.Search.WhereGAME_NO
                         select a).Delete();

                        Db.ADDT32.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                    }
                    else
                    {
                        (from a in Db.ADDT30
                         join b in Db.ADDT29 on a.TITLE_SHOW_ID equals b.TITLE_SHOW_ID
                         where b.GAME_NO == model.Search.WhereGAME_NO
                         && ListChk.Contains(a.TEMP_USER_ID)
                         select a).Delete();

                        var T29 = (from p in Db.ADDT29
                                   join pl in Db.ADDT30 on p.TITLE_SHOW_ID equals pl.TITLE_SHOW_ID into pp
                                   from pl in pp.DefaultIfEmpty()
                                   where pl == null
                                   && p.GAME_NO == model.Search.WhereGAME_NO
                                   select p);

                        if (T29.Any())
                        {
                            string TITLE_IMG_Path = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + $@"\{title_of_show}\";

                            foreach (var item in T29.Where(a => a.TITLE_IMG != null))
                            {
                                string DelFilePath = TITLE_IMG_Path + "\\" + item.TITLE_IMG;

                                if (System.IO.File.Exists(DelFilePath))
                                {
                                    System.IO.File.Delete(DelFilePath);
                                }
                            }

                            T29.Delete();
                        }

                        Db.ADDT31.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && ListChk.Contains(a.TEMP_USER_ID)).Delete();

                        (from a in Db.ADDT33
                         join b in Db.ADDT32 on a.LOTTERY_NO equals b.LOTTERY_NO
                         where b.GAME_NO == model.Search.WhereGAME_NO
                          && ListChk.Contains(a.TEMP_USER_ID)
                         select a).Delete();

                        (from p in Db.ADDT32
                         join pl in Db.ADDT33 on p.LOTTERY_NO equals pl.LOTTERY_NO into pp
                         from pl in pp.DefaultIfEmpty()
                         where pl == null
                         && p.GAME_NO == model.Search.WhereGAME_NO
                         select p).Delete();
                    }

                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// Delete 人員及這個活動相關記錄
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveDelPersonDeleteData(GameEditPersonViewModel model, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var listT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID).FirstOrDefault();

                if (listT27 != null)
                {
                    var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();
                    if (T26 == null)
                    {
                        Message = "系統發生錯誤;原因:找不到此筆活動單號";
                        return false;
                    }

                    Db.ADDT28.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID).Delete();
                    Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID).Delete();

                    if (Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Count() > 1)
                    {
                        (from a in Db.ADDT30
                         join b in Db.ADDT29 on a.TITLE_SHOW_ID equals b.TITLE_SHOW_ID
                         where b.GAME_NO == model.Search.WhereGAME_NO
                         select a).Delete();

                        var T29 = Db.ADDT29.Where(a => a.GAME_NO == model.Search.WhereGAME_NO);

                        if (T29.Any())
                        {
                            string TITLE_IMG_Path = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + $@"\{title_of_show}\";

                            try
                            {
                                if (Directory.Exists(TITLE_IMG_Path) == false)
                                {
                                    Directory.Delete(TITLE_IMG_Path, true);
                                }
                            }
                            catch (Exception)
                            {
                            }

                            T29.Delete();
                        }

                        Db.ADDT31.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();

                        (from a in Db.ADDT33
                         join b in Db.ADDT32 on a.LOTTERY_NO equals b.LOTTERY_NO
                         where b.GAME_NO == model.Search.WhereGAME_NO
                         select a).Delete();

                        Db.ADDT32.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).Delete();
                    }
                    else
                    {
                        (from a in Db.ADDT30
                         join b in Db.ADDT29 on a.TITLE_SHOW_ID equals b.TITLE_SHOW_ID
                         where b.GAME_NO == model.Search.WhereGAME_NO
                        && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID
                         select a).Delete();

                        //表演人員資料是空的
                        var T29 = (from p in Db.ADDT29
                                   join pl in Db.ADDT30 on p.TITLE_SHOW_ID equals pl.TITLE_SHOW_ID into pp
                                   from pl in pp.DefaultIfEmpty()
                                   where pl == null
                                   && p.GAME_NO == model.Search.WhereGAME_NO
                                   select p);

                        if (T29.Any())
                        {
                            string TITLE_IMG_Path = GetSysGamePath(T26.SCHOOL_NO, T26.GAME_NO) + $@"\{title_of_show}\";

                            foreach (var item in T29.Where(a => a.TITLE_IMG != null))
                            {
                                string DelFilePath = TITLE_IMG_Path + "\\" + item.TITLE_IMG;

                                if (System.IO.File.Exists(DelFilePath))
                                {
                                    System.IO.File.Delete(DelFilePath);
                                }
                            }

                            T29.Delete();
                        }

                        Db.ADDT31.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID).Delete();

                        (from a in Db.ADDT33
                         join b in Db.ADDT32 on a.LOTTERY_NO equals b.LOTTERY_NO
                         where b.GAME_NO == model.Search.WhereGAME_NO
                        && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID
                         select a).Delete();

                        (from p in Db.ADDT32
                         join pl in Db.ADDT33 on p.LOTTERY_NO equals pl.LOTTERY_NO into pp
                         from pl in pp.DefaultIfEmpty()
                         where pl == null
                         && p.GAME_NO == model.Search.WhereGAME_NO
                         select p).Delete();
                    }

                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 臨時卡 停止使用
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveDisablePersonData(GameEditPersonViewModel model, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID).FirstOrDefault();

                if (T27 != null)
                {
                    T27.STATUS = (byte)ADDT27.StatusVal.停用;

                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 臨時卡 停止使用 =>啟用使用
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveEnablePersonData(GameEditPersonViewModel model, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TEMP_USER_ID == model.Search.WhereTEMP_USER_ID).FirstOrDefault();

                if (Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.TEMP_USER_ID != T27.TEMP_USER_ID && a.GAME_USER_ID == T27.GAME_USER_ID && a.STATUS != (byte)ADDT27.StatusVal.停用).Any())
                {
                    Message = "錯誤;原因:這張臨時卡已被其他人使用。";
                    return false;
                }

                if (T27 != null)
                {
                    T27.STATUS = (byte)ADDT27.StatusVal.使用中;

                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 更新抽獎是否領獎
        /// </summary>
        /// <param name="LOTTERY_NO"></param>
        /// <param name="ITEM_NO"></param>
        /// <param name="Checked"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveUpdateReceiveAwardData(string LOTTERY_NO, string ITEM_NO, bool Checked, ref ECOOL_DEVEntities Db, ref string Message)
        {
            var aDDT33 = Db.ADDT33.Where(a => a.LOTTERY_NO == LOTTERY_NO && a.ITEM_NO == ITEM_NO).FirstOrDefault();

            if (aDDT33 != null)
            {
                aDDT33.RECEIVE_AWARD = Checked;

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 酷幣餘額轉入學校酷幣
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveCashLotteryIntoData(BatchCashIntoIndexViewModel model, UserProfile user, string TEMP_USER_ID, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                var ListChk = TEMP_USER_ID;

                var listT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.CASH_AVAILABLE > 0 && ListChk.Contains(a.TEMP_USER_ID)).ToList();

                if (listT27.Count() > 0)
                {
                    string KeyNo = DateTime.Now.ToString("yyyyMMddHHmmss");
                    List<ADDT28> listT28 = new List<ADDT28>();
                    List<AWAT01_LOG> listT01_Log = new List<AWAT01_LOG>();
                    List<AWAT01> listAddW01 = new List<AWAT01>();

                    var listW01 = (from a in Db.AWAT01
                                   join b in Db.ADDT27 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                                   where b.GAME_NO == model.Search.WhereGAME_NO && b.CASH_AVAILABLE > 0 && ListChk.Contains(b.TEMP_USER_ID)
                                   select a).ToList();

                    foreach (var T27 in listT27)
                    {
                        string LogDesc = $"{T26.GAME_NAME}-獎品抽獎轉入學校酷幣";

                        short ThisCash = (short)T27.CASH_AVAILABLE;

                        ADDT28 aw1_log = new ADDT28();

                        aw1_log.LOG_ID = Guid.NewGuid().ToString("N");
                        aw1_log.TEMP_USER_ID = T27.TEMP_USER_ID;
                        aw1_log.SCHOOL_NO = T27.SCHOOL_NO;
                        aw1_log.GAME_NO = T27.GAME_NO;
                        aw1_log.SOURCE_NO = KeyNo;
                        aw1_log.CASH_IN = (short)(T27.CASH_AVAILABLE * -1);
                        aw1_log.LOG_TIME = DateTime.Now;
                        aw1_log.LOG_DESC = LogDesc;

                        aw1_log.UNLOG_CASH_ALL = T27.CASH_ALL;
                        aw1_log.UNLOG_CASH_AVAILABLE = T27.CASH_AVAILABLE;

                        //ADDT27累加這次點數
                        T27.CASH_AVAILABLE = T27.CASH_AVAILABLE + (ThisCash * -1);
                        Db.Entry(T27).State = System.Data.Entity.EntityState.Modified;
                        aw1_log.LOG_CASH_ALL = T27.CASH_ALL;
                        aw1_log.LOG_CASH_AVAILABLE = T27.CASH_AVAILABLE;

                        listT28.Add(aw1_log);

                        Db.ADDT28.Add(aw1_log);

                        if (T27.GAME_USER_TYPE == UserType.Student)
                        {


                            AWAT01 aw1 = Db.AWAT01.Where(u => u.SCHOOL_NO == T27.SCHOOL_NO && u.USER_NO == T27.USER_NO).FirstOrDefault();

                            if (aw1 == null)
                            {
                                aw1 = Db.AWAT01.Create();
                                aw1.SCHOOL_NO = T27.SCHOOL_NO;
                                aw1.USER_NO = T27.USER_NO;
                                aw1.CASH_ALL = ThisCash;
                                aw1.CASH_AVAILABLE = ThisCash;
                                aw1.CASH_WORKHARD = 0;
                                listAddW01.Add(aw1);
                                Db.AWAT01.Add(aw1);
                            }
                            else
                            {
                                if (aw1.CASH_ALL.HasValue == false) aw1.CASH_ALL = 0;
                                if (aw1.CASH_AVAILABLE.HasValue == false) aw1.CASH_AVAILABLE = 0;

                                aw1.CASH_ALL = aw1.CASH_ALL.Value + ThisCash;
                                aw1.CASH_AVAILABLE = aw1.CASH_AVAILABLE.Value + ThisCash;
                                aw1.CASH_WORKHARD = aw1.CASH_WORKHARD.Value + 0;
                                Db.Entry(aw1).State = System.Data.Entity.EntityState.Modified;
                            }

                            //AWAT01_LOG
                            AWAT01_LOG aw01_log = Db.AWAT01_LOG.Create();
                            aw01_log.SCHOOL_NO = aw1.SCHOOL_NO;
                            aw01_log.USER_NO = aw1.USER_NO;
                            aw01_log.LOG_TIME = DateTime.Now;
                            aw01_log.SOURCE_TYPE = "ADDT27";
                            aw01_log.SOURCE_NO = KeyNo;
                            aw01_log.CASH_IN = ThisCash;
                            aw01_log.LOG_DESC = LogDesc;

                            aw01_log.ADD_CASH_ALL = (short?)ThisCash;
                            aw01_log.ADD_CASH_AVAILABLE = (short?)ThisCash;
                            aw01_log.ADD_CASH_WORKHARD = 0;

                            aw01_log.AWAT01_CASH_ALL = aw1.CASH_ALL;
                            aw01_log.AWAT01_CASH_AVAILABLE = aw1.CASH_AVAILABLE;
                            aw01_log.AWAT01_CASH_WORKHARD = aw1.CASH_WORKHARD;

                            aw01_log.LOG_PERSON = "系統給扣點";
                            listT01_Log.Add(aw01_log);
                            Db.AWAT01_LOG.Add(aw01_log);
                        }
                        else if (T27.GAME_USER_TYPE == UserType.Teacher)
                        {
                            CashHelper.TeachAddCash(user, (int)T27.CASH_AVAILABLE, T27.SCHOOL_NO, T27.USER_NO, "ADDT27", KeyNo, "獎品抽獎轉入學校酷幣", true, null, ref Db);
                        }
                    }

                   // EFBatchOperation.For(Db, Db.ADDT27).UpdateAll(listT27, x => x.ColumnsToUpdate(c => c.CASH_AVAILABLE));
                   // EFBatchOperation.For(Db, Db.ADDT28).InsertAll(listT28);

                    if (listAddW01.Count() > 0)
                    {
                        //EFBatchOperation.For(Db, Db.AWAT01).InsertAll(listAddW01);
                    }

                   // EFBatchOperation.For(Db, Db.AWAT01).UpdateAll(listW01, x => x.ColumnsToUpdate(u => u.CASH_AVAILABLE, u => u.CASH_ALL, u => u.CASH_WORKHARD));
                    //EFBatchOperation.For(Db, Db.AWAT01_LOG).InsertAll(listT01_Log);

                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 關卡支付處理
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="LOG_ID"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public ADDT27 SaveLotteryLevelData(GameLeveViewModel model, UserProfile user, AWAT01 Gameawat01,ref ECOOL_DEVEntities Db, ref string LOG_ID, ref string Message)
        {
            ADDT26 T26 = null;

            ADDT26_D T26_D = null;
            ADDT26_Q T26_Q = null;

            ADDT27 T27 = null;
            string GAME_USER_ID = string.Empty;
            int LogCount = 0;

            bool IsRepeat = false;

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                T26 = Db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).AsNoTracking().FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                    return null;
                }

                if (!string.IsNullOrWhiteSpace(model.Coupons_ITem))
                {
                    T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.GAME_NO && a.LEVEL_NO == model.LEVEL_NO && a.Coupons_ITem == model.Coupons_ITem).AsNoTracking().FirstOrDefault();
                }
                else
                {
                    T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.GAME_NO && a.LEVEL_NO == model.LEVEL_NO).AsNoTracking().FirstOrDefault();
                }
                if (T26_D == null)
                {
                    Message = "系統發生錯誤;原因:找不到此關卡資料";
                    logger.Error(Message + $" GAME_NO={model.GAME_NO}");
                    return null;
                }

                if (!string.IsNullOrWhiteSpace(T26_D.GROUP_ID))
                {
                    T26_Q = Db.ADDT26_Q.Where(a => a.GROUP_ID == T26_D.GROUP_ID && a.GAME_NO == T26.GAME_NO).FirstOrDefault();
                    if (T26_Q == null)
                    {
                        Message = "系統發生錯誤;原因:找不到有獎徵答題目";
                        logger.Error(Message + $" GAME_NO={model.GAME_NO}，GROUP_ID={T26_D.GROUP_ID}");
                        return null;
                    }
                }

                ts.Complete();
            }
            if (!string.IsNullOrWhiteSpace(model.GameUserID)) {

                HRMT01 St = Db.HRMT01.Where(x => x.CARD_NO == model.GameUserID && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                if (St == null)
                {
                    St = Db.HRMT01.Where(x => x.USER_NO == model.GameUserID && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                    if (St != null)
                    {

                        model.GameUserID = St.CARD_NO;

                    }
                    else {
                        List<string> CLASSNOLIST = new List<string>();
                        string CLASS_NONOW = "";
                        string SEAT_NONOW = "";
                        CLASSNOLIST = Db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled && x.CLASS_NO != "" && x.CLASS_NO != null).Select(x => x.CLASS_NO).ToList();
                        foreach (var item in CLASSNOLIST)
                        {
                            if (model.GameUserID.Contains(item) == true)
                            {

                                CLASS_NONOW = model.GameUserID.Substring(0, item.Length);

                                SEAT_NONOW = model.GameUserID.Substring(item.Length, model.GameUserID.Length - item.Length);
                                St = Db.HRMT01.Where(x => x.CLASS_NO == CLASS_NONOW && x.SCHOOL_NO == user.SCHOOL_NO && x.SEAT_NO == SEAT_NONOW && x.USER_STATUS == UserStaus.Enabled && x.USER_TYPE == UserType.Student).FirstOrDefault();
                                if (St != null)
                                {

                                    model.GameUserID = St.CARD_NO;
                                }
                            }
                        }

                    }
                }

            }
            GAME_USER_ID = this.GetUserId1(T26, model.GameUserID, ref Db, ref Message);
           
            // GAME_USER_ID = this.GetUserId(T26, model.GameUserID, ref Db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                logger.Debug(Message + $" GAME_NO={model.GAME_NO}");
                return null;
            }



            using (TransactionScope tx = new TransactionScope())
            {
                if (T26_D.LEVEL_TYPE != ADDT26_D.LevelType.Test)
                {
                    string LogDesc = string.Empty;


                    LogDesc = T26.GAME_NAME + T26_D.LEVEL_NAME;
                    var IsPayOK = true;
                    //T27 = Db.ADDT27.Where(a => a.GAME_USER_ID == GAME_USER_ID && a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().FirstOrDefault();
                    //if (T27 == null)
                    //{
                        HRMT01 St = Db.HRMT01.Where(x => x.CARD_NO == GAME_USER_ID && x.SCHOOL_NO== user.SCHOOL_NO).FirstOrDefault();
                    if (St == null)
                    {
                        St= Db.HRMT01.Where(x => x.USER_NO == GAME_USER_ID && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                        if (St != null) {

                            GAME_USER_ID = St.CARD_NO;

                        }
                    }
                    GameQRCodeToGuestViewModel guestModel = new GameQRCodeToGuestViewModel();

                        guestModel.PHONE = GAME_USER_ID;
                        guestModel.GAME_NO = T26.GAME_NO;
                    T27 = SaveCreApplyData1(model.GAME_NO, GAME_USER_ID, T26, T26_D, St, guestModel,Gameawat01, ref Db, ref Message, ref LOG_ID);
                   //Db.ADDT27.Where(a => a.TEMP_USER_ID== guestModel.&& a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().FirstOrDefault();
                    //}
                    
                   
                 

                    //if (!IsPayOK)
                    //{
                    //    logger.Error(Message + $" GAME_NO={model.GAME_NO};GAME_USER_ID={GAME_USER_ID}");
                    //    return null;
                    //}
                    BatchCashIntoIndexViewModel model1 = new BatchCashIntoIndexViewModel();
                    model1.Search = new GameSearchViewModel();
                    model1.Search.WhereGAME_NO = T26.GAME_NO;
                    //if (T27 != null)
                    //{
                    //    SaveCashLotteryIntoData(model1, user, T27.TEMP_USER_ID, ref Db, ref Message);
                    //}
                }



                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    logger.Error(Message + $" GAME_NO={model.GAME_NO};GAME_USER_ID={GAME_USER_ID}");
                    return null;
                }

                string Desc = string.Empty;

                if (T27?.GAME_USER_TYPE == UserType.Student)
                {
                    Desc = $"{T27?.NAME}同學";
                }
                else
                {
                    Desc = $"{T27?.NAME}先生/女士";
                }



                if (T26_D.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize && T27!=null)
                {
                    List<GameCashDetailsViewModel> GameCashDetailsViewModelItemInfo = new List<GameCashDetailsViewModel>();
                    GameCashDetailsViewModelItemInfo = GetPersonCashLogIntoData(T27.TEMP_USER_ID, Db);
                    int CashINInfo = 0;
                    CashINInfo = GameCashDetailsViewModelItemInfo.Sum(x => x.CASH_IN);
                    //if (T26_D.Y_CASH == true)
                    //{
                    //    if (T26_D.CASH <= 0)
                    //    {
                    //        Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次支付{ T26_D.CASH.Value.ToString()}點，目前共有{(CashINInfo)}點";
                    //    }
                    //    else
                    //    {
                    //        Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次獲得{ T26_D.CASH.Value.ToString()}點，目前共有{(CashINInfo)}點";
                    //    }
                    //}
                    //else {
                    //    Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>";


                    //}


                }




                tx.Complete();
            }

            return T27;
        }
        /// <summary>
        /// 酷幣餘額轉入學校酷幣
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveCashIntoData(BatchCashIntoIndexViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            using (TransactionScope ts = new TransactionScope())
            {
                var T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    return false;
                }

                var ListChk = model.CheckBox.Where(a => a.Chk == true).Select(a => a.TEMP_USER_ID).ToList();

                var listT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.CASH_AVAILABLE > 0 && ListChk.Contains(a.TEMP_USER_ID)).ToList();

                if (listT27.Count() > 0)
                {
                    string KeyNo = DateTime.Now.ToString("yyyyMMddHHmmss");
                    List<ADDT28> listT28 = new List<ADDT28>();
                    List<AWAT01_LOG> listT01_Log = new List<AWAT01_LOG>();
                    List<AWAT01> listAddW01 = new List<AWAT01>();

                    var listW01 = (from a in Db.AWAT01
                                   join b in Db.ADDT27 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                                   where b.GAME_NO == model.Search.WhereGAME_NO && b.CASH_AVAILABLE > 0 && ListChk.Contains(b.TEMP_USER_ID)
                                   select a).ToList();

                    foreach (var T27 in listT27)
                    {
                        string LogDesc = $"{T26.GAME_NAME}-活動酷幣餘額轉入學校酷幣(兒童趣闖關)";
                        if (T27.GAME_USER_TYPE != UserType.Student && T27.GAME_USER_TYPE != UserType.Teacher)
                        {
                            LogDesc = "，您不是學生/老師，因此清空此活動酷幣";
                        }

                        short ThisCash = (short)T27.CASH_AVAILABLE;

                        ADDT28 aw1_log = new ADDT28();

                        aw1_log.LOG_ID = Guid.NewGuid().ToString("N");
                        aw1_log.TEMP_USER_ID = T27.TEMP_USER_ID;
                        aw1_log.SCHOOL_NO = T27.SCHOOL_NO;
                        aw1_log.GAME_NO = T27.GAME_NO;
                        aw1_log.SOURCE_NO = KeyNo;
                        aw1_log.CASH_IN = (short)(T27.CASH_AVAILABLE * -1);
                        aw1_log.LOG_TIME = DateTime.Now;
                        aw1_log.LOG_DESC = LogDesc;

                        aw1_log.UNLOG_CASH_ALL = T27.CASH_ALL;
                        aw1_log.UNLOG_CASH_AVAILABLE = T27.CASH_AVAILABLE;

                        //ADDT27累加這次點數
                        T27.CASH_AVAILABLE = T27.CASH_AVAILABLE + (ThisCash * -1);
                        Db.Entry(T27).State = System.Data.Entity.EntityState.Modified;
                        aw1_log.LOG_CASH_ALL = T27.CASH_ALL;
                        aw1_log.LOG_CASH_AVAILABLE = T27.CASH_AVAILABLE;

                        listT28.Add(aw1_log);
                        Db.ADDT28.Add(aw1_log);
                        // CameAddCash(T27.TEMP_USER_ID, T27.SCHOOL_NO, T27.GAME_NO, KeyNo, (T27.CASH_AVAILABLE * -1), "活動酷幣餘額轉入學校酷幣", ref Message, ref Db, out string LOG_ID);

                        if (T27.GAME_USER_TYPE == UserType.Student)
                        {
                            //  CashHelper.AddCash(user, (int)T27.CASH_AVAILABLE, T27.SCHOOL_NO, T27.USER_NO, "ADDT27", KeyNo, "活動酷幣餘額轉入學校酷幣", true, ref Db);

                            AWAT01 aw1 = Db.AWAT01.Where(u => u.SCHOOL_NO == T27.SCHOOL_NO && u.USER_NO == T27.USER_NO).FirstOrDefault();

                            if (aw1 == null)
                            {
                                aw1 = Db.AWAT01.Create();
                                aw1.SCHOOL_NO = T27.SCHOOL_NO;
                                aw1.USER_NO = T27.USER_NO;
                                aw1.CASH_ALL = ThisCash;
                                aw1.CASH_AVAILABLE = ThisCash;
                                aw1.CASH_WORKHARD = 0;
                                listAddW01.Add(aw1);
                                Db.AWAT01.Add(aw1);
                            }
                            else
                            {
                                if (aw1.CASH_ALL.HasValue == false) aw1.CASH_ALL = 0;
                                if (aw1.CASH_AVAILABLE.HasValue == false) aw1.CASH_AVAILABLE = 0;

                                aw1.CASH_ALL = aw1.CASH_ALL.Value + ThisCash;
                                aw1.CASH_AVAILABLE = aw1.CASH_AVAILABLE.Value + ThisCash;
                                aw1.CASH_WORKHARD = aw1.CASH_WORKHARD.Value + 0;
                                Db.Entry(aw1).State = System.Data.Entity.EntityState.Modified;
                            }

                            //AWAT01_LOG
                            AWAT01_LOG aw01_log = Db.AWAT01_LOG.Create();
                            aw01_log.SCHOOL_NO = aw1.SCHOOL_NO;
                            aw01_log.USER_NO = aw1.USER_NO;
                            aw01_log.LOG_TIME = DateTime.Now;
                            aw01_log.SOURCE_TYPE = "ADDT27";
                            aw01_log.SOURCE_NO = KeyNo;
                            aw01_log.CASH_IN = ThisCash;
                            aw01_log.LOG_DESC = LogDesc;

                            aw01_log.ADD_CASH_ALL = (short?)ThisCash;
                            aw01_log.ADD_CASH_AVAILABLE = (short?)ThisCash;
                            aw01_log.ADD_CASH_WORKHARD = 0;

                            aw01_log.AWAT01_CASH_ALL = aw1.CASH_ALL;
                            aw01_log.AWAT01_CASH_AVAILABLE = aw1.CASH_AVAILABLE;
                            aw01_log.AWAT01_CASH_WORKHARD = aw1.CASH_WORKHARD;

                            aw01_log.LOG_PERSON = "系統給扣點";
                            listT01_Log.Add(aw01_log);
                            Db.AWAT01_LOG.Add(aw01_log);
                        }
                        else if (T27.GAME_USER_TYPE == UserType.Teacher)
                        {
                            CashHelper.TeachAddCash(user, (int)T27.CASH_AVAILABLE, T27.SCHOOL_NO, T27.USER_NO, "ADDT27", KeyNo, "活動酷幣餘額轉入學校酷幣(兒童趣闖關)", true, null, ref Db);
                        }
                    }

                   // EFBatchOperation.For(Db, Db.ADDT27).UpdateAll(listT27, x => x.ColumnsToUpdate(c => c.CASH_AVAILABLE));
                   // EFBatchOperation.For(Db, Db.ADDT28).InsertAll(listT28);

                    if (listAddW01.Count() > 0)
                    {
                   //     EFBatchOperation.For(Db, Db.AWAT01).InsertAll(listAddW01);
                    }

                   // EFBatchOperation.For(Db, Db.AWAT01).UpdateAll(listW01, x => x.ColumnsToUpdate(u => u.CASH_AVAILABLE, u => u.CASH_ALL, u => u.CASH_WORKHARD));
                   // EFBatchOperation.For(Db, Db.AWAT01_LOG).InsertAll(listT01_Log);

                    try
                    {
                        Db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                ts.Complete();
            }

            return true;
        }

        /// <summary>
        /// 取得 目前 活動開始日 最後一筆 活動資料
        /// </summary>
        /// <returns></returns>
        public ADDT26 GetNowGameNo()
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
                {
                    var aADDT26 = (from a in db.ADDT26
                                   orderby a.GAME_DATES descending
                                   select a).Take(1).AsNoTracking().FirstOrDefault();

                    return aADDT26;
                }
            }
        }

        /// <summary>
        /// 人員管理 功能選單
        /// </summary>
        /// <param name="SelectedVal"></param>
        /// <returns></returns>
        public List<SelectListItem> GetBatchWorkTypeItem(string SelectedVal = null)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>
            {
                new SelectListItem() { Text = BatchWorkTypeVal.GetDesc(BatchWorkTypeVal.BatchApplyStudent), Value = BatchWorkTypeVal.BatchApplyStudent, Selected = SelectedVal == BatchWorkTypeVal.BatchApplyStudent },
                new SelectListItem() { Text = BatchWorkTypeVal.GetDesc(BatchWorkTypeVal.BatchApplyCard), Value = BatchWorkTypeVal.BatchApplyCard, Selected = SelectedVal == BatchWorkTypeVal.BatchApplyCard },
                new SelectListItem() { Text = BatchWorkTypeVal.GetDesc(BatchWorkTypeVal.AddApplyCard), Value = BatchWorkTypeVal.AddApplyCard, Selected = SelectedVal == BatchWorkTypeVal.AddApplyCard },
                 new SelectListItem() { Text = BatchWorkTypeVal.GetDesc(BatchWorkTypeVal.EditApplyCard), Value = BatchWorkTypeVal.EditApplyCard, Selected = SelectedVal == BatchWorkTypeVal.EditApplyCard }
            };
            return SelectItem;
        }

        public List<SelectListItem> GetBackupLinkItem(string GAME_NO, ref ECOOL_DEVEntities Db, string SelectedVal = null)
        {
            var Data = GetBackupLinkData(GAME_NO, ref Db);

            if (string.IsNullOrEmpty(SelectedVal))
            {
                SelectedVal = UrlCustomHelper.GetOwnWebUri();
            }

            var SelectItem = Data.OrderBy(a => a.BACKUP_ID).Select(x => new SelectListItem { Text = x.BACKUP_NAME, Value = x.BACKUP_URL, Selected = SelectedVal.Contains(x.BACKUP_URL) }).ToList();

            return SelectItem;
        }

        public List<GameWebStatusIntoViewModel> GetBackupLinkData(string GAME_NO, ref ECOOL_DEVEntities Db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var Temp = (from a in Db.ADDT26_BackupLink
                            where a.GAME_NO == GAME_NO
                            && a.IS_ENABLE == true
                            select new GameWebStatusIntoViewModel
                            {
                                GAME_NO = a.GAME_NO,
                                BACKUP_ID = a.BACKUP_ID,
                                BACKUP_NAME = a.BACKUP_NAME,
                                BACKUP_URL = a.BACKUP_URL,
                                IS_ENABLE = a.IS_ENABLE,
                            }).AsNoTracking().ToList();

                string ThisServer = UrlCustomHelper.GetOwnWebUri();

                if ((Temp?.Where(a => ThisServer.Contains(a.BACKUP_URL)).Count() ?? 0) == 0)
                {
                    GameWebStatusIntoViewModel gameWebStatus = new GameWebStatusIntoViewModel
                    {
                        GAME_NO = GAME_NO,
                        BACKUP_ID = 0,
                        BACKUP_NAME = "本伺務器",
                        BACKUP_URL = UrlCustomHelper.GetOwnWebUri(),
                        IS_ENABLE = true
                    };
                    Temp.Add(gameWebStatus);
                }

                return Temp;
            }
        }

        /// <summary>
        /// 開始比賽 / 重新開始
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public bool SaveTeamGameStart(string GAME_NO, ref ECOOL_DEVEntities Db, ref string Message)
        {
            var T26 = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).FirstOrDefault();
            if (T26 != null)
            {
                T26.TEAM_GAME_DATES = DateTime.Now;
                T26.TEAM_GAME_DATEE = null;
                Db.Entry(T26).State = System.Data.Entity.EntityState.Modified;

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = ex.Message;
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 開始結束
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        public bool SaveTeamGameEnd(string GAME_NO, ref ECOOL_DEVEntities Db, ref string Message)
        {
            var T26 = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).FirstOrDefault();
            if (T26 != null)
            {
                T26.TEAM_GAME_DATEE = DateTime.Now;
                Db.Entry(T26).State = System.Data.Entity.EntityState.Modified;

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = ex.Message;
                    return false;
                }
            }

            return true;
        }

        public GameTeamStatusIndexViewModel GetQueryTeamStatus(string GAME_NO, ref ECOOL_DEVEntities Db)
        {
            GameTeamStatusIndexViewModel model = new GameTeamStatusIndexViewModel
            {
                GameInfo = Db.ADDT26.Where(a => a.GAME_NO == GAME_NO).NoLock(x => x.FirstOrDefault())
            };

            if (model.GameInfo != null)
            {
                model.Teams = Db.ADDT27.Where(a => a.GAME_NO == GAME_NO).ToListNoLock();

                if (model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.一般)
                {
                    model.GameInfoDs = Db.ADDT26_D.Where(a => a.GAME_NO == GAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Pay).OrderBy(a => a.LEVEL_NAME).ToListNoLock();
                    model.GameLogs = Db.ADDT28.Where(a => a.GAME_NO == GAME_NO && a.LOG_TIME >= model.GameInfo.GAME_DATES && a.LOG_TIME < model.GameInfo.GAME_DATEE).ToListNoLock();
                }
                else
                {
                    model.GameInfoQDs = Db.ADDT26_Q.Where(a => a.GAME_NO == GAME_NO).OrderBy(a => a.G_ORDER_BY).ToListNoLock();

                    model.GameAnsLogs = Db.ADDT26_DAns.Where(a => a.GAME_NO == GAME_NO && a.TRUE_ANS == true && a.CHG_DATE >= model.GameInfo.GAME_DATES && a.CHG_DATE < model.GameInfo.GAME_DATEE).ToListNoLock();

                    var RANK = (from a in model.GameLogs
                              join b in model.GameInfoDs on a.SOURCE_NO equals b.LEVEL_NO
                              where b.LEVEL_NO.Distinct().Count() >= model.GameInfoDs.Count()
                              select new {a.GAME_NO,a.TEMP_USER_ID,a.LOG_TIME}).OrderByDescending(x=>x.LOG_TIME).ToList().Select((t,i)=>new {

                                  t.TEMP_USER_ID
                                  ,t.GAME_NO
                                  ,t.LOG_TIME
                                  ,RankNum=i+1

                              });
                    model.GameTeamRankList = (List<GameTeamStatusRankList>)RANK;
                }
            }

            return model;
        }

        /// <summary>
        /// 建議要玩的關卡
        /// </summary>
        /// <param name="model"></param>
        private static void RecommendLevelData(GameCashQueryViewModel model)
        {
            if (model.MeLevelDetails != null)
            {
                var TotalCount = Convert.ToDouble(model.MeLevelDetails.Sum(a => a.Person_Count));

                //全部關卡的 3分之1 關卡 ，例 9 關 = 9/3 = 3
                int drawCount = Convert.ToInt16(Math.Ceiling((decimal)model.MeLevelDetails.Count() / 3));

                if (TotalCount <= 0)
                {
                    TotalCount = 1;
                }

                //計算目前過關的比例
                model.MeLevelDetails.Select(a =>
                {
                    a.LevelRate = Convert.ToDouble(a.Person_Count) / TotalCount;
                    return a;
                }).ToList();

                //過關的比例  Average 值
                var Avg = model.MeLevelDetails.Average(a => a.LevelRate);

                //取出 過關的比例 小於 Average 值 ，而且可玩的關卡  top  3分之1 關卡
                var TempList = model.MeLevelDetails.Where(a => a.LevelRate <= Avg && a.MeIsLevelPass == false
                && (a.CASH >= 0) || (a.CASH < 0 && model.CASH_AVAILABLE + a.CASH >= 0)).OrderBy(d => Guid.NewGuid()).Take(drawCount).ToList();

                //更新 建議關卡 至 MeLevelDetails 裡
                model.MeLevelDetails.Select(a =>
                {
                    a.IsRecommendLevel = TempList.Where(b => b.LEVEL_NAME == a.LEVEL_NAME).Any();
                    return a;
                }).ToList();

                //建議關卡小於 (3分之1 關卡)時
                if (model.MeLevelDetails.Where(a => a.IsRecommendLevel).Count() < drawCount)
                {
                    //取出目前 建議關卡
                    drawCount = Convert.ToInt16(drawCount - model.MeLevelDetails.Where(a => a.IsRecommendLevel).Count());

                    //增加目前不是 建議關卡 而還沒玩過關卡
                    var AddList = model.MeLevelDetails.Where(a => (a.IsRecommendLevel) != true && a.MeIsLevelPass == false).OrderBy(a => a.LevelRate).Take(drawCount).ToList();

                    //更新 建議關卡 至 MeLevelDetails 裡
                    model.MeLevelDetails.Select(a =>
                    {
                        a.IsRecommendLevel = a.IsRecommendLevel == false ? AddList.Where(b => b.LEVEL_NAME == a.LEVEL_NAME).Any() : true;
                        return a;
                    }).ToList();
                }
            }
        }

        private static ADDT32 SaveADDT32Data(GameLotteryCreViewModel model, UserProfile user, ECOOL_DEVEntities Db)
        {
            ADDT32 Save = null;

            if (model.Main.LOTTERY_NO != null)
            {
                Save = Db.ADDT32.Where(a => a.LOTTERY_NO == model.Main.LOTTERY_NO).FirstOrDefault();
            }

            if (Save == null)
            {
                Save = new ADDT32
                {
                    LOTTERY_NO = Guid.NewGuid().ToString("N"),
                    GAME_NO = model.Search.WhereGAME_NO,
                    CRE_PERSON = user.USER_KEY,
                    CRE_DATE = DateTime.Now,
                    CHG_PERSON = user.USER_KEY,
                    CHG_DATE = DateTime.Now,
                    LEVEL = model.Main.LEVEL,
                    LEVEL_COUNT = model.Main.LEVEL_COUNT,
                    PEOPLE_COUNT = model.Main.PEOPLE_COUNT,
                    UNLOTTERY = model.Main.UNLOTTERY,
                    UNCUEST = model.Main.UNCUEST,
                    STATUS = ((int)ADDT32.StatusVal.待抽獎).ToString(),
                    LOTTERY_DESC = model.Main.LOTTERY_DESC,
                    IS_GIVE_UP_LOTTERY = model.Main.IS_GIVE_UP_LOTTERY,
                    IS_FULL = model.Main.IS_FULL,
                };

                Db.ADDT32.Add(Save);
            }
            else
            {
                Save.CHG_PERSON = user.USER_KEY;
                Save.CHG_DATE = DateTime.Now;
                Save.LEVEL = model.Main.LEVEL;
                Save.LEVEL_COUNT = model.Main.LEVEL_COUNT;
                Save.PEOPLE_COUNT = model.Main.PEOPLE_COUNT;
                Save.UNLOTTERY = model.Main.UNLOTTERY;
                Save.UNCUEST = model.Main.UNCUEST;
                Save.STATUS = ((int)ADDT32.StatusVal.待抽獎).ToString();
                Save.LOTTERY_DESC = model.Main.LOTTERY_DESC;
                Save.IS_GIVE_UP_LOTTERY = model.Main.IS_GIVE_UP_LOTTERY;
                Save.IS_FULL = model.Main.IS_FULL;
                Db.Entry(Save).State = System.Data.Entity.EntityState.Modified;
            }

            return Save;
        }

        private static object GetPropertyValue(object obj, string property)
        {
            System.Reflection.PropertyInfo propertyInfo = obj.GetType().GetProperty(property);
            return propertyInfo.GetValue(obj, null);
        }

        private void CreateSheetFastLink(GameWebStatusIntoViewModel gameWeb, ADDT26 aDDT26, List<ADDT26_Q> aDDT26_Qs, List<ADDT26_D> aDDT26_Ds, IWorkbook wb)
        {
            UrlHelper u = new UrlHelper(HttpContext.Current.Request.RequestContext);

            string basisUrl = gameWeb.BACKUP_URL + @"/" + u.Action("FastLevel", "Game");

            ISheet ws = wb.CreateSheet(gameWeb.BACKUP_NAME);
            ICellStyle oStyle = this.GetTitleStyle(wb);
            ICellStyle ContentStyle = this.GetContentStyle(wb);

            ws.CreateRow(0);//第一行為欄位名稱

            if (aDDT26.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
            {
                ws.GetRow(0).CreateCell(0).SetCellValue("功能名稱/題目");
                ws.GetRow(0).GetCell(0).CellStyle = oStyle;

                ws.GetRow(0).CreateCell(1).SetCellValue("選項");
                ws.GetRow(0).GetCell(1).CellStyle = oStyle;

                ws.GetRow(0).CreateCell(2).SetCellValue("連結");
                ws.GetRow(0).GetCell(2).CellStyle = oStyle;
            }
            else
            {
                ws.GetRow(0).CreateCell(0).SetCellValue("功能名稱");
                ws.GetRow(0).GetCell(0).CellStyle = oStyle;

                ws.GetRow(0).CreateCell(1).SetCellValue("連結");
                ws.GetRow(0).GetCell(1).CellStyle = oStyle;
            }

            List<Tuple<string, string, string>> tuples = new List<Tuple<string, string, string>>();
            tuples.Add(new Tuple<string, string, string>("闖關地圖", $"{basisUrl}?ActionName=PassMode&GAME_NO={aDDT26.GAME_NO}&Title={aDDT26.GAME_NAME}-闖關地圖", ""));

            tuples.Add(new Tuple<string, string, string>("查詢", $"{basisUrl}?ActionName=CashQuery&GAME_NO={aDDT26.GAME_NO}&Title={aDDT26.GAME_NAME}-查詢", ""));

            foreach (var item in aDDT26_Ds)
            {
                if (item.LEVEL_TYPE == ADDT26_D.LevelType.Apply)
                {
                    tuples.Add(new Tuple<string, string, string>("報名", $"{basisUrl}?ActionName=ApplyView&GAME_NO={aDDT26.GAME_NO}&LEVEL_NO={item.LEVEL_NO}&Title={aDDT26.GAME_NAME}-報名", ""));
                }
                else
                {
                    string G_SUBJECT = aDDT26_Qs.Where(a => a.GROUP_ID == item.GROUP_ID).Select(a => a.G_SUBJECT).FirstOrDefault() ?? "";

                    tuples.Add(new Tuple<string, string, string>(item.LEVEL_NAME, $"{basisUrl}?ActionName=LevelView&GAME_NO={aDDT26.GAME_NO}&LEVEL_NO={item.LEVEL_NO}&Title={aDDT26.GAME_NAME}-{item.LEVEL_NAME}", G_SUBJECT));
                }
            }

            int TrNum = 1;

            foreach (var Temp in tuples)
            {
                IHyperlink link = new XSSFHyperlink(HyperlinkType.Url)
                {
                    Address = Temp.Item2
                };

                ws.CreateRow(TrNum);

                if (aDDT26.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                {
                    ws.GetRow(TrNum).CreateCell(0).SetCellValue(Temp.Item3);
                    ws.GetRow(TrNum).GetCell(0).CellStyle = ContentStyle;

                    ws.GetRow(TrNum).CreateCell(1).SetCellValue(Temp.Item1);
                    ws.GetRow(TrNum).GetCell(1).CellStyle = ContentStyle;

                    ws.GetRow(TrNum).CreateCell(2).Hyperlink = link;
                    ws.GetRow(TrNum).CreateCell(2).SetCellValue(link.Address);
                    ws.GetRow(TrNum).GetCell(2).CellStyle = ContentStyle;
                }
                else
                {
                    ws.GetRow(TrNum).CreateCell(0).SetCellValue(Temp.Item1);
                    ws.GetRow(TrNum).GetCell(0).CellStyle = ContentStyle;
                    ws.GetRow(TrNum).CreateCell(1).Hyperlink = link;
                    ws.GetRow(TrNum).CreateCell(1).SetCellValue(link.Address);
                    ws.GetRow(TrNum).GetCell(1).CellStyle = ContentStyle;
                }

                TrNum++;
            }

            ws.AutoSizeColumn(0); ////自動調整欄寬
            ws.AutoSizeColumn(1);
            ws.AutoSizeColumn(2);
        }

        /// <summary>
        /// excel 表頭 Style
        /// </summary>
        /// <param name="wb"></param>
        /// <returns></returns>
        private ICellStyle GetTitleStyle(IWorkbook wb)
        {
            ICellStyle oStyle = (ICellStyle)wb.CreateCellStyle();

            oStyle.VerticalAlignment = VerticalAlignment.Center;
            oStyle.Alignment = HorizontalAlignment.Center;

            //設定背景顏色
            oStyle.FillForegroundColor = HSSFColor.SkyBlue.Index;
            oStyle.FillPattern = FillPattern.SolidForeground;//顏色參考資料http://www.dotblogs.com.tw/lastsecret/archive/2010/12/20/20250.aspx

            // 加邊框
            oStyle.BorderTop = BorderStyle.Thin;
            oStyle.BorderRight = BorderStyle.Thin;
            oStyle.BorderLeft = BorderStyle.Thin;
            oStyle.BorderBottom = BorderStyle.Thin;

            //字體
            IFont fontR = wb.CreateFont();
            fontR.Color = IndexedColors.Black.Index;
            fontR.Boldweight = (short)FontBoldWeight.Bold;
            fontR.FontHeightInPoints = 13;
            oStyle.SetFont(fontR);
            oStyle.WrapText = true;

            return oStyle;
        }

        private ICellStyle GetContentStyle(IWorkbook wb)
        {
            ICellStyle oStyle = (ICellStyle)wb.CreateCellStyle();

            // 加邊框
            oStyle.BorderTop = BorderStyle.Thin;
            oStyle.BorderRight = BorderStyle.Thin;
            oStyle.BorderLeft = BorderStyle.Thin;
            oStyle.BorderBottom = BorderStyle.Thin;

            //字體
            IFont fontR = wb.CreateFont();
            fontR.Color = IndexedColors.Black.Index;
            fontR.Boldweight = (short)FontBoldWeight.Bold;
            fontR.FontHeightInPoints = 13;
            oStyle.SetFont(fontR);
            oStyle.WrapText = true;

            return oStyle;
        }

        private ADDT27 SaveCreApplyData1(string GAME_NO, string GAME_USER_ID, ADDT26 T26, ADDT26_D T26_D, HRMT01 St, GameQRCodeToGuestViewModel guestModel, AWAT01 Gameawat01, ref ECOOL_DEVEntities Db, ref string Message, ref string LOG_ID)
        {
            if (T26_D == null)
            {
                T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == GAME_NO ).AsNoTracking().NoLock(X => X.FirstOrDefault());
                if (T26_D == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動有報名的關卡";
                    logger.Error(Message + $" GAME_NO={GAME_NO}");
                    return null;
                }
            }

            if (St == null)
            {
                St = Db.HRMT01.Where(a => a.CARD_NO == GAME_USER_ID).AsNoTracking().NoLock(X => X.FirstOrDefault());
               
            }
            ADDT27 Cre = new ADDT27();
            if (St != null || guestModel != null)
            {


                Cre.TEMP_USER_ID = Guid.NewGuid().ToString("N");
                Cre.GAME_USER_ID = GAME_USER_ID;

                Cre.GAME_NO = T26.GAME_NO;

                //學生
                if (St != null)
                {
                    var school = Db.BDMT01.Where(a => a.SCHOOL_NO == St.SCHOOL_NO).NoLock(X => X.FirstOrDefault());

                    if (school == null)
                    {
                        school = Db.BDMT01.Where(a => a.SCHOOL_NO == T26.SCHOOL_NO).NoLock(X => X.FirstOrDefault());
                    }

                    Cre.SCHOOL_NO = school.SCHOOL_NO;
                    Cre.SHORT_NAME = school.SHORT_NAME;
                    Cre.GAME_USER_TYPE = St.USER_TYPE;
                    Cre.GAME_USER_TYPE_DESC = UserType.GetDesc(St.USER_TYPE);
                    Cre.USER_NO = St.USER_NO;
                    Cre.NAME = St.NAME;
                    Cre.SNAME = St.SNAME;
                    Cre.SEX = St.SEX;
                    Cre.GRADE = St.GRADE;
                    Cre.CLASS_NO = St.CLASS_NO;
                    Cre.SEAT_NO = St.SEAT_NO;
                    Cre.PHONE = St.PHONE;
                }
                else //訪客
                {
                    var school = Db.BDMT01.Where(a => a.SCHOOL_NO == St.SCHOOL_NO).NoLock(X => X.FirstOrDefault());

                    Cre.SCHOOL_NO = school.SCHOOL_NO;
                    Cre.SHORT_NAME = school.SHORT_NAME;
                    Cre.GAME_USER_TYPE = UserType.Guest;
                    Cre.GAME_USER_TYPE_DESC = UserType.GetDesc(UserType.Guest);
                    Cre.NAME = string.IsNullOrWhiteSpace(guestModel.NAME) ? guestModel.PHONE : guestModel.NAME;
                    Cre.PHONE = guestModel.PHONE;
                }

                Cre.CRE_PERSON = Cre.TEMP_USER_ID;
                Cre.CRE_DATE = DateTime.Now;
                Cre.CHG_PERSON = Cre.TEMP_USER_ID;
                Cre.CHG_DATE = DateTime.Now;
                Cre.STATUS = (byte)ADDT27.StatusVal.使用中;

                Db.ADDT27.Add(Cre);

                string LogDesc = T26.GAME_NAME + T26_D.LEVEL_NAME;
                var IsPayOK = CameAddCash1(Cre.TEMP_USER_ID, Cre.SCHOOL_NO, Cre.GAME_NO, T26_D.LEVEL_NO, T26_D.CASH, LogDesc, Gameawat01,ref Message, ref Db, out LOG_ID);

                if (!IsPayOK)
                {
                    logger.Error(Message + $" GAME_NO={GAME_NO}");
                    return null;
                }

                //if (Cre.GAME_USER_TYPE == UserType.Student)
                //{
                //    CashHelper.AddCash(null, (int) T26_D.CASH, Cre.SCHOOL_NO, Cre.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref Db);

                //}

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    logger.Error(Message + $" GAME_NO={GAME_NO}");
                    return null;
                }

                if (Cre.GAME_USER_TYPE == UserType.Student)
                {
                    Message = $@"報到成功<br/>{Cre.NAME}同學<br/>恭喜獲得{ T26_D.CASH.Value.ToString()}點";
                }
                else
                {
                    Message = $@"報到成功<br/>{Cre.NAME}先生/女士<br/>恭喜獲得{ T26_D.CASH.Value.ToString()}點";
                }
            }

            return Cre;
        }

        private bool SaveCreApplyData(string GAME_NO, string GAME_USER_ID, ADDT26 T26, ADDT26_D T26_D, HRMT01 St, GameQRCodeToGuestViewModel guestModel, ref ECOOL_DEVEntities Db, ref string Message, ref string LOG_ID)
        {
            if (T26_D == null)
            {
                T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == GAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).AsNoTracking().NoLock(X => X.FirstOrDefault());
                if (T26_D == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動有報名的關卡";
                    logger.Error(Message + $" GAME_NO={GAME_NO}");
                    return false;
                }
            }

            if (St == null)
            {
                St = Db.HRMT01.Where(a => a.CARD_NO == GAME_USER_ID).AsNoTracking().NoLock(X => X.FirstOrDefault());
            }
            ADDT27 Cre = new ADDT27();
            if (St != null || guestModel != null)
            {
             

                Cre.TEMP_USER_ID = Guid.NewGuid().ToString("N");
                Cre.GAME_USER_ID = GAME_USER_ID;

                Cre.GAME_NO = T26.GAME_NO;

                //學生
                if (St != null)
                {
                    var school = Db.BDMT01.Where(a => a.SCHOOL_NO == St.SCHOOL_NO).NoLock(X => X.FirstOrDefault());

                    if (school == null)
                    {
                        school = Db.BDMT01.Where(a => a.SCHOOL_NO == T26.SCHOOL_NO).NoLock(X => X.FirstOrDefault());
                    }

                    Cre.SCHOOL_NO = school.SCHOOL_NO;
                    Cre.SHORT_NAME = school.SHORT_NAME;
                    Cre.GAME_USER_TYPE = St.USER_TYPE;
                    Cre.GAME_USER_TYPE_DESC = UserType.GetDesc(St.USER_TYPE);
                    Cre.USER_NO = St.USER_NO;
                    Cre.NAME = St.NAME;
                    Cre.SNAME = St.SNAME;
                    Cre.SEX = St.SEX;
                    Cre.GRADE = St.GRADE;
                    Cre.CLASS_NO = St.CLASS_NO;
                    Cre.SEAT_NO = St.SEAT_NO;
                    Cre.PHONE = St.PHONE;
                }
                else //訪客
                {
                    var school = Db.BDMT01.Where(a => a.SCHOOL_NO == St.SCHOOL_NO).NoLock(X => X.FirstOrDefault());

                    Cre.SCHOOL_NO = school.SCHOOL_NO;
                    Cre.SHORT_NAME = school.SHORT_NAME;
                    Cre.GAME_USER_TYPE = UserType.Guest;
                    Cre.GAME_USER_TYPE_DESC = UserType.GetDesc(UserType.Guest);
                    Cre.NAME = string.IsNullOrWhiteSpace(guestModel.NAME) ? guestModel.PHONE : guestModel.NAME;
                    Cre.PHONE = guestModel.PHONE;
                }

                Cre.CRE_PERSON = Cre.TEMP_USER_ID;
                Cre.CRE_DATE = DateTime.Now;
                Cre.CHG_PERSON = Cre.TEMP_USER_ID;
                Cre.CHG_DATE = DateTime.Now;
                Cre.STATUS = (byte)ADDT27.StatusVal.使用中;

                Db.ADDT27.Add(Cre);

                string LogDesc = T26.GAME_NAME + T26_D.LEVEL_NAME;
                var IsPayOK = CameAddCash(Cre.TEMP_USER_ID, Cre.SCHOOL_NO, Cre.GAME_NO, T26_D.LEVEL_NO, T26_D.CASH, LogDesc, ref Message, ref Db, out LOG_ID);

                if (!IsPayOK)
                {
                    logger.Error(Message + $" GAME_NO={GAME_NO}");
                    return false;
                }

                //if (Cre.GAME_USER_TYPE == UserType.Student)
                //{
                //    CashHelper.AddCash(null, (int) T26_D.CASH, Cre.SCHOOL_NO, Cre.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref Db);

                //}

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    logger.Error(Message + $" GAME_NO={GAME_NO}");
                    return false;
                }

                if (Cre.GAME_USER_TYPE == UserType.Student)
                {
                    Message = $@"報到成功<br/>{Cre.NAME}同學<br/>恭喜獲得{ T26_D.CASH.Value.ToString()}點";
                }
                else
                {
                    Message = $@"報到成功<br/>{Cre.NAME}先生/女士<br/>恭喜獲得{ T26_D.CASH.Value.ToString()}點";
                }
            }

            return true;
        }

        private bool SaveAns(ECOOL_DEVEntities Db, ADDT26 T26, ADDT26_D T26_D, ADDT27 T27)
        {
            bool IsRepeat = false;

            int SYear;
            int Semesters;

            ADDT26_MAns aDDT26MAn = (from a in Db.ADDT26_MAns
                                     where a.GAME_NO == T26.GAME_NO && a.STATUS == (byte)ADDT26_MAns.AnsStatus.作答中
                                     && a.TEMP_USER_ID == T27.TEMP_USER_ID
                                     select a).FirstOrDefault<ADDT26_MAns>();

            if (aDDT26MAn == null)
            {
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                aDDT26MAn = new ADDT26_MAns()
                {
                    ANSWER_ID = Guid.NewGuid().ToString("N"),
                    GAME_NO = T26.GAME_NO,
                    TEMP_USER_ID = T27.TEMP_USER_ID,
                    SCHOOL_NO = T27.SCHOOL_NO,
                    SHORT_NAME = T27.SHORT_NAME,
                    USER_NO = T27.USER_NO,
                    CLASS_NO = T27.CLASS_NO,
                    SYEAR = (int)(SYear),
                    SEMESTER = (byte)Semesters,
                    SEAT_NO = T27.SEAT_NO,
                    NAME = T27.NAME,
                    SNAME = T27.SNAME,
                    CRE_DATE = DateTime.Now,
                    STATUS = (byte)ADDT26_MAns.AnsStatus.作答中,
                    IS_LAST_SCORE = true,
                };

                Db.ADDT26_MAns.Add(aDDT26MAn);
            }

            ADDT26_DAns DAns = (
                from a in Db.ADDT26_DAns
                where a.GROUP_ID == T26_D.GROUP_ID && a.ANSWER_ID == aDDT26MAn.ANSWER_ID
                select a).FirstOrDefault<ADDT26_DAns>();

            if (DAns != null)
            {
                DAns.LEVEL_NO = T26_D.LEVEL_NO;
                DAns.LEVEL_NAME = T26_D.LEVEL_NAME;
                DAns.TRUE_ANS = T26_D.TRUE_ANS;
                DAns.CHG_PERSON = T27.TEMP_USER_ID;
                DAns.CHG_DATE = new DateTime?(DateTime.Now);
                Db.Entry(DAns).State = System.Data.Entity.EntityState.Modified;

                IsRepeat = true;
            }
            else
            {
                DAns = new ADDT26_DAns()
                {
                    ANSWER_ID = aDDT26MAn.ANSWER_ID,
                    GROUP_ID = T26_D.GROUP_ID,
                    GAME_NO = T26.GAME_NO,
                    LEVEL_NO = T26_D.LEVEL_NO,
                    LEVEL_NAME = T26_D.LEVEL_NAME,
                    TRUE_ANS = T26_D.TRUE_ANS,
                    CHG_PERSON = T27.TEMP_USER_ID,
                    CHG_DATE = DateTime.Now
                };

                Db.ADDT26_DAns.Add(DAns);
            }

            return IsRepeat;
        }

        private List<GamePrizeLevelViewModel> GamePrizeLevelViewModel(string GAME_NO, string TEMP_USER_ID, ECOOL_DEVEntities Db)
        {
            string sSQL = $@" select a.LEVEL_NAME
                    ,Case When (Select Count(*) from  ADDT28 s (nolock) where s.SOURCE_NO=a.LEVEL_NO and s.GAME_NO=a.GAME_NO and s.TEMP_USER_ID=@TEMP_USER_ID) > 0 Then 1 else 0 end as IsPrize
                    ,a.CASH
                    from ADDT26_D a (nolock)
                    where a.GAME_NO=@GAME_NO
                    and a.LEVEL_TYPE='{ADDT26_D.LevelType.Prize}'
                    order by a.LEVEL_ITEM";
            var PrizeLevel = Db.Database.Connection.Query<GamePrizeLevelViewModel>(sSQL
             , new
             {
                 GAME_NO = GAME_NO,
                 TEMP_USER_ID = TEMP_USER_ID
             }).ToList();

            return PrizeLevel;
        }

        /// <summary>
        /// 查詢此人過關現況
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="TEMP_USER_ID"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        private List<GameMeLevelViewModel> GetPersonLevelData(string GAME_NO, string TEMP_USER_ID, ECOOL_DEVEntities Db)
        {
            string sSQL = $@" select a.LEVEL_NAME
                    ,Case When (Select Count(*) from  ADDT28 s (nolock) where s.SOURCE_NO=a.LEVEL_NO and s.GAME_NO=a.GAME_NO and s.TEMP_USER_ID=@TEMP_USER_ID) > 0 Then 1 else 0 end as MeIsLevelPass
                    ,(select count(DISTINCT s.TEMP_USER_ID) from ADDT28 s (nolock) where s.SOURCE_NO=a.LEVEL_NO  and s.GAME_NO=a.GAME_NO) as Person_Count
                    ,a.CASH
                    from ADDT26_D a (nolock)
                    where a.GAME_NO=@GAME_NO
                    and a.LEVEL_TYPE='{ADDT26_D.LevelType.Pay}'
                    order by a.LEVEL_ITEM";
            var MeLevelDetails = Db.Database.Connection.Query<GameMeLevelViewModel>(sSQL
             , new
             {
                 GAME_NO = GAME_NO,
                 TEMP_USER_ID = TEMP_USER_ID
             }).ToList();

            return MeLevelDetails;
        }

        /// <summary>
        /// 此人是否中獎 資料
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <param name="T26"></param>
        /// <param name="T27"></param>
        private List<GameLotteryListViewModel> GetPersonLitteryData(string TEMP_USER_ID, ECOOL_DEVEntities Db)
        {
            var PersonLitteryData = (from a in Db.ADDT32
                                     join b in Db.ADDT33 on a.LOTTERY_NO equals b.LOTTERY_NO
                                     where b.TEMP_USER_ID == TEMP_USER_ID
                                     && a.STATUS == ((int)ADDT32.StatusVal.已抽獎).ToString()
                                     select new GameLotteryListViewModel()
                                     {
                                         LOTTERY_NO = a.LOTTERY_NO,
                                         TEMP_USER_ID = b.TEMP_USER_ID,
                                         ITEM_NO = b.ITEM_NO,
                                         LOTTERY_DESC = a.LOTTERY_DESC,
                                         PEOPLE_COUNT = a.PEOPLE_COUNT,
                                         CRE_DATE = a.CRE_DATE,
                                         RECEIVE_AWARD = b.RECEIVE_AWARD ?? false
                                     }).OrderByDescending(a => a.CRE_DATE).ToList();

            return PersonLitteryData;
        }

        private IEnumerable<DbString> GetAnsiStrings(string[] strings)
        {
            strings = strings ?? throw new ArgumentNullException(nameof(strings));

            foreach (var ansiString in strings)
            {
                yield return new DbString { IsAnsi = true, Value = ansiString };
            }
        }

        #region 批次報名(卡片) 匯入資料處理

        /// <summary>
        /// 是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckErr = false;

        /// <summary>
        /// 格式是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckDataTypeErr = false;

        protected string _ErrorRowCellExcel = string.Empty;

        /// <summary>
        /// 必輸未輸 true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckMustErr = false;

        /// <summary>
        /// 必輸欄位陣列
        /// </summary>
        private string[] MustArray; //必輸欄位

        #region 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_DataTypeConflict(object sender, DataRowCellFilledArgs e)
        {
            if (e.Row.ItemArray.All(i => i is DBNull) == false)
            {
                _CheckErr = true;
                _CheckDataTypeErr = true;
                _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
            }
        }

        #endregion 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        /// <summary>
        /// 批次報名(卡片) 匯入資料處理
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <param name="batchApply"></param>
        /// <returns></returns>
        public bool SaveBatchApplyCard(BatchApplyCardViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message, ref BatchApplyStudentResultViewModel batchApply)
        {
            ADDT26 T26 = null;
            ADDT26_D T26_D = null;

            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                T26 = Db.ADDT26.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).AsNoTracking().FirstOrDefault();
                if (T26 == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動單號";
                    batchApply = null;
                    return false;
                }

                T26_D = Db.ADDT26_D.Where(a => a.GAME_NO == model.Search.WhereGAME_NO && a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).AsNoTracking().FirstOrDefault();
                if (T26_D == null)
                {
                    Message = "系統發生錯誤;原因:找不到此筆活動有報名的關卡";
                    batchApply = null;
                    return false;
                }
            }

            using (TransactionScope ts = new TransactionScope())
            {
                NPOIHelper npoi = new NPOIHelper(); //NEW NPOIHelper

                npoi.onDataTypeConflict += new DataRowCellHandler(this.NPOI_DataTypeConflict); //宣告使用 輸入內容的型態 是否設定 及 Excel存儲格式一樣 ex. 日期 輸入[aaa] ，Excel存儲格式設日期，或欄位第一行有定義 System.DateTime
                npoi.onLineCheckValue += new DataRowCellHandler(this.NPOI_LineCheckValue);

                MustArray = new string[] { "CARD_NO", "NAME", "GAME_USER_TYPE_DESC" }; // Excel 必輸欄位

                var ArraySheetNames = new string[] { "stud_data" }; //Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)

                string _Error;

                List<GameLevelPersonDetailsViewModel> NG_LIST = new List<GameLevelPersonDetailsViewModel>();
                List<ADDT27> listT27 = new List<ADDT27>();

                try
                {
                    DataSet ds = npoi.Excel2Table(model.UploadBatchCardFile.InputStream, 0, 2, 1, ArraySheetNames);

                    if (ds == null)
                    {
                        _Error = "上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                        Message = _Error;
                        batchApply = null;
                        return false;
                    }

                    ///讀取資料筆數為0
                    if (ds.Tables.Count == 0)
                    {
                        _Error = "上傳錯誤，錯誤原因如下:<br><br>讀取資料筆數為0，請確認 上傳Excel的 Sheet Name 是否有以下 ";
                        foreach (string ThisSheet in ArraySheetNames)
                        {
                            _Error = _Error + "【" + ThisSheet.ToString() + "】";
                        }
                        _Error = _Error + " Sheet Name ，請確認";

                        Message = _Error;
                        batchApply = null;
                        return false;
                    }

                    ///EXCEL內容資料型態數誤
                    if (_CheckDataTypeErr || _CheckMustErr)
                    {
                        _Error = "上傳錯誤，錯誤原因如下:<br><br> " + _ErrorRowCellExcel;
                        Message = _Error;
                        batchApply = null;
                        return false;
                    }

                    var NowT27 = Db.ADDT27.Where(a => a.GAME_NO == model.Search.WhereGAME_NO).ToList();

                    DataTable CardNoData = ds.Tables[ArraySheetNames[0]];

                    var bdmt01s = Db.BDMT01.ToList();

                    int NUM = 0;
                    foreach (DataRow Row in CardNoData.Rows)
                    {
                        NUM = NUM + 1;
                        string ThisCARD_NO = (Row["CARD_NO"] == DBNull.Value ? "" : (string)Row["CARD_NO"]);

                        if (!string.IsNullOrWhiteSpace(ThisCARD_NO))
                        {
                            if (!NowT27.Where(a => a.GAME_USER_ID == ThisCARD_NO).Any() && !listT27.Where(a => a.GAME_USER_ID == ThisCARD_NO).Any() && ThisCARD_NO.Length == 10)
                            {
                                string SHORT_NAME = (Row["SHORT_NAME"] == DBNull.Value ? "" : (string)Row["SHORT_NAME"]);
                                string SCHOOL_NO = string.Empty;

                                if (!string.IsNullOrWhiteSpace(SHORT_NAME))
                                {
                                    SCHOOL_NO = bdmt01s.Where(a => a.SHORT_NAME.Contains(SHORT_NAME)).Select(a => a.SCHOOL_NO).FirstOrDefault() ?? "";
                                }

                                ADDT27 Cre = new ADDT27();
                                Cre.TEMP_USER_ID = Guid.NewGuid().ToString("N");
                                Cre.GAME_USER_ID = ThisCARD_NO;
                                Cre.SCHOOL_NO = SCHOOL_NO;
                                Cre.SHORT_NAME = SHORT_NAME;
                                Cre.GAME_NO = T26.GAME_NO;
                                Cre.GAME_USER_TYPE = model.GAME_USER_TYPE ?? UserType.Guest;
                                Cre.USER_NO = "";
                                Cre.NAME = (Row["NAME"] == DBNull.Value ? "" : (string)Row["NAME"]);
                                Cre.SNAME = (Row["NAME"] == DBNull.Value ? "" : (string)Row["NAME"]);

                                string SEX = (Row["SEX"] == DBNull.Value ? "" : (string)Row["SEX"]);

                                if (!string.IsNullOrWhiteSpace(SEX))
                                {
                                    if (SEX != "1" && SEX != "0")
                                    {
                                        if (SEX == "男")
                                        {
                                            SEX = "1";
                                        }
                                        else if (SEX == "女")
                                        {
                                            SEX = "0";
                                        }
                                        else
                                        {
                                            SEX = "1";
                                        }
                                    }
                                }

                                Cre.SEX = SEX;
                                Cre.PHONE = (Row["PHONE"] == DBNull.Value ? "" : (string)Row["PHONE"]);

                                if (Row["GRADE"] != DBNull.Value)
                                {
                                    byte.TryParse(Row["GRADE"].ToString(), out byte GRADE);
                                    Cre.GRADE = GRADE;
                                }

                                Cre.CLASS_NO = (Row["CLASS_NO"] == DBNull.Value ? "" : (string)Row["CLASS_NO"]);
                                Cre.SEAT_NO = (Row["SEAT_NO"] == DBNull.Value ? "" : (string)Row["SEAT_NO"]);
                                Cre.CASH_ALL = T26_D.CASH;
                                Cre.CASH_AVAILABLE = T26_D.CASH;
                                Cre.CRE_PERSON = user.USER_KEY;
                                Cre.CRE_DATE = DateTime.Now;
                                Cre.CHG_PERSON = user.USER_KEY;
                                Cre.CHG_DATE = DateTime.Now;
                                Cre.GAME_USER_TYPE_DESC = (Row["GAME_USER_TYPE_DESC"] == DBNull.Value ? "" : (string)Row["GAME_USER_TYPE_DESC"]);
                                Cre.STATUS = Convert.ToByte(model.STATUS);
                                listT27.Add(Cre);
                                Db.ADDT27.Add(Cre);
                            }
                            else
                            {
                                string ERROR = string.Empty;

                                if (ThisCARD_NO.Length != 10)
                                {
                                    ERROR = "此卡號長度不足１０碼";
                                }
                                else if (listT27.Where(a => a.GAME_USER_ID == ThisCARD_NO).Any())
                                {
                                    ERROR = "此卡號在這個EXCEL重複";
                                }
                                else if (NowT27.Where(a => a.GAME_USER_ID == ThisCARD_NO).Any())
                                {
                                    ERROR = "此卡號已報名";
                                }

                                GameLevelPersonDetailsViewModel ErrModel = new GameLevelPersonDetailsViewModel
                                {
                                    GAME_USER_ID = ThisCARD_NO,
                                    SCHOOL_NO = "",
                                    GAME_NO = T26.GAME_NO,
                                    GAME_USER_TYPE = model.GAME_USER_TYPE ?? UserType.Guest,
                                    USER_NO = "",
                                    NAME = (Row["NAME"] == DBNull.Value ? "" : (string)Row["NAME"]),
                                    SNAME = (Row["NAME"] == DBNull.Value ? "" : (string)Row["NAME"]),
                                    PHONE = (Row["PHONE"] == DBNull.Value ? "" : (string)Row["PHONE"]),
                                    GAME_USER_TYPE_DESC = (Row["GAME_USER_TYPE_DESC"] == DBNull.Value ? "" : (string)Row["GAME_USER_TYPE_DESC"]),
                                    ERROR = ERROR,
                                };
                                NG_LIST.Add(ErrModel);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    batchApply = null;
                    return false;
                }

                if (listT27.Count() == 0)
                {
                    Message = "錯誤;原因:你上傳的EXCEL有問題";
                    batchApply.Search = model.Search;
                    batchApply.NG_LIST = NG_LIST;
                    return false;
                }

                //EFBatchOperation.For(Db, Db.ADDT27).InsertAll(listT27);

                var listT28 = new List<ADDT28>();

                foreach (var item in listT27)
                {
                    ADDT28 log = new ADDT28
                    {
                        LOG_ID = Guid.NewGuid().ToString("N"),
                        TEMP_USER_ID = item.TEMP_USER_ID,
                        SCHOOL_NO = item.SCHOOL_NO,
                        GAME_NO = item.GAME_NO,
                        SOURCE_NO = T26_D.LEVEL_NO,
                        CASH_IN = (short)T26_D.CASH,
                        LOG_TIME = DateTime.Now,
                        LOG_DESC = T26.GAME_NAME + BatchWorkTypeVal.GetDesc(BatchWorkTypeVal.BatchApplyCard),
                        LOG_CASH_ALL = (short)T26_D.CASH,
                        LOG_CASH_AVAILABLE = (short)T26_D.CASH,
                        UNLOG_CASH_ALL = 0,
                        UNLOG_CASH_AVAILABLE = 0
                    };
                    listT28.Add(log);
                    Db.ADDT28.Add(log);
                }

              //  EFBatchOperation.For(Db, Db.ADDT28).InsertAll(listT28);

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    batchApply = null;
                    return false;
                }

                batchApply.Search = model.Search;
                batchApply.OK_LIST = (from b in listT27
                                      select new GameLevelPersonDetailsViewModel
                                      {
                                          GAME_USER_ID = b.GAME_USER_ID,
                                          SCHOOL_NO = b.SCHOOL_NO,
                                          SHORT_NAME = b.SHORT_NAME,
                                          GAME_NO = model.Search.WhereGAME_NO,
                                          GAME_USER_TYPE = b.GAME_USER_TYPE,
                                          USER_NO = b.USER_NO,
                                          NAME = b.NAME,
                                          SNAME = b.SNAME,
                                          SEX = b.SEX,
                                          GRADE = b.GRADE,
                                          CLASS_NO = b.CLASS_NO,
                                          SEAT_NO = b.SEAT_NO,
                                          PHONE = b.PHONE,
                                          GAME_USER_TYPE_DESC = b.GAME_USER_TYPE_DESC
                                      }).ToList();

                batchApply.NG_LIST = NG_LIST;

                ts.Complete();
            }

            return true;
        }

        protected void NPOI_LineCheckValue(object sender, DataRowCellFilledArgs e)
        {
            if (e.Row.ItemArray.All(i => i is DBNull) == false)
            {
                foreach (var item in MustArray)
                {
                    if (Convert.IsDBNull(e.Row[item]) || (e.Row[item] ?? "").ToString() == "")
                    {
                        _CheckErr = true;
                        _CheckMustErr = true;
                        _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                    }
                }
            }
        }

        #endregion 批次報名(卡片) 匯入資料處理

        /// <summary>
        /// 制作過關圖表
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetCountPassCharts(GameListLevelPersonCountViewModel Data)
        {
            if (Data.ListCountPassData.Count == 0)
            {
                return null;
            }

            Highcharts Tempchart = new Highcharts("Tempchart");

            Tempchart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = "完成關卡數量人數趨示圖", X = 0 })
            .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "完成關卡數量" }, Categories = Data.ListCountPassData.Select(a => a.Level_Count + "").ToArray() })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "人數" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "筆" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 {
                                   Name ="完成關卡數量人數",
                                   Data = new DotNet.Highcharts.Helpers.Data(Data.ListCountPassData.Select(a=>a.Level_Count_Person).Cast<object>().ToArray())
                                 }
                        }
            );

            Tempchart.SetCredits(new Credits { Enabled = false, Text = "", Href = "" });

            return Tempchart;
        }

        /// <summary>
        /// 此人點數異動清單
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Db"></param>
        /// <returns></returns>
        private List<GameCashDetailsViewModel> GetPersonCashLogIntoData(string TEMP_USER_ID, ECOOL_DEVEntities Db)
        {
            var CashDetails = (from a in Db.ADDT28
                               where a.TEMP_USER_ID == TEMP_USER_ID
                               && a.CASH_IN != 0
                               select new GameCashDetailsViewModel()
                               {
                                   CASH_IN = (short)a.CASH_IN,
                                   LOG_TIME = a.LOG_TIME,
                                   LOG_DESC = a.LOG_DESC,
                                   LOG_CASH_AVAILABLE = a.LOG_CASH_AVAILABLE,
                               }).OrderByDescending(a => a.LOG_TIME).ToList();

            return CashDetails;
        }
        /// <summary>
        /// 檢查 使用者卡號是否正常
        /// </summary>
        /// <param name="T26"></param>
        /// <param name="GameUserID"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        private string GetUserId(ADDT26 T26, string GameUserID, ref ECOOL_DEVEntities Db, ref string Message)
        {
            string GAME_USER_ID = string.Empty;

            GameQRCodeToGuestViewModel guestModel = new GameQRCodeToGuestViewModel();

            if (!Db.ADDT27.Where(a => a.GAME_NO == T26.GAME_NO && a.GAME_USER_ID == GameUserID && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().NoLock(X => X.Any()))
            {
                var St = Db.HRMT01.Where(a => a.CARD_NO == GameUserID).Count();

                if (St == 0)
                {
                    if (GameUserID.IndexOf("GAME_NO") != -1 && GameUserID.IndexOf("NAME") != -1)
                    {
                        guestModel = JsonConvert.DeserializeObject<GameQRCodeToGuestViewModel>(GameUserID);

                        if (T26.GAME_NO != guestModel.GAME_NO)
                        {
                            Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！!<br><font style='font-size:20px'>您的 QR CODE 非本活動所用，卡號：{GameUserID},長度{GameUserID.Length} 碼。</font>";
                        }
                        GAME_USER_ID = guestModel.PHONE;
                    }
                    else
                    {
                        if (GameUserID.Length == 10)
                        {
                            if (Db.ADDT27.Where(a => a.GAME_NO == T26.GAME_NO && a.GAME_USER_ID == GameUserID && a.STATUS == (byte)ADDT27.StatusVal.未使用).AsNoTracking().NoLock(X => X.Any()))
                            {
                                this.CreAddt27Status(T26.GAME_NO, GameUserID, Db, ref Message);

                                if (string.IsNullOrWhiteSpace(Message))
                                {
                                    GAME_USER_ID = GameUserID;
                                }
                            }
                            else
                            {
                                Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的卡片不是本系統認可的數位學生證 或 卡片，卡號：{GameUserID},長度{GameUserID.Length} 碼，本活動資料庫無此卡號。</font>";
                            }
                        }
                        else
                        {
                            Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的 QR CODE 非本活動所用，卡號：{GameUserID},長度{GameUserID.Length} 碼。</font>";
                        }
                    }
                }
                else if (St >= 2)
                {
                    Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的卡片對應到多位來賓之資料，請聯絡系統人員，卡號：{GameUserID}。</font>";
                }
                else
                {
                    GAME_USER_ID = GameUserID;
                    string LogId = string.Empty;
                    string ApplyMessage = string.Empty;
                    SaveCreApplyData(T26.GAME_NO, GAME_USER_ID, T26, null, null, null, ref Db, ref ApplyMessage, ref LogId);
                }
            }
            else
            {
                GAME_USER_ID = GameUserID;
            }

            return GAME_USER_ID;
        }

        /// <summary>
        /// 檢查 使用者卡號是否正常
        /// </summary>
        /// <param name="T26"></param>
        /// <param name="GameUserID"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        private string GetUserId1(ADDT26 T26, string GameUserID, ref ECOOL_DEVEntities Db, ref string Message)
        {
            string GAME_USER_ID = string.Empty;

            GameQRCodeToGuestViewModel guestModel = new GameQRCodeToGuestViewModel();

            if (!Db.ADDT27.Where(a => a.GAME_NO == T26.GAME_NO && a.GAME_USER_ID == GameUserID && a.STATUS == (byte)ADDT27.StatusVal.使用中).AsNoTracking().NoLock(X => X.Any()))
            {
                //Db.ADDT27.Where(a => a.GAME_NO == T26.GAME_NO && a.GAME_USER_ID == GameUserID && a.STATUS == (byte)ADDT27.StatusVal.使用中)
                var St = Db.HRMT01.Where(a => a.CARD_NO == GameUserID && a.USER_STATUS!=UserStaus.Invalid).Count();

                if (St == 0)
                {
                    if (GameUserID.IndexOf("GAME_NO") != -1 && GameUserID.IndexOf("NAME") != -1)
                    {
                        guestModel = JsonConvert.DeserializeObject<GameQRCodeToGuestViewModel>(GameUserID);

                        if (T26.GAME_NO != guestModel.GAME_NO)
                        {
                            Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！!<br><font style='font-size:20px'>您的 QR CODE 非本活動所用，卡號：{GameUserID},長度{GameUserID.Length} 碼。</font>";
                        }
                        GAME_USER_ID = guestModel.PHONE;
                    }
                    else
                    {
                        if (GameUserID.Length == 10)
                        {
                            if (Db.ADDT27.Where(a => a.GAME_NO == T26.GAME_NO && a.GAME_USER_ID == GameUserID && a.STATUS == (byte)ADDT27.StatusVal.未使用).AsNoTracking().NoLock(X => X.Any()))
                            {
                                this.CreAddt27Status(T26.GAME_NO, GameUserID, Db, ref Message);

                                if (string.IsNullOrWhiteSpace(Message))
                                {
                                    GAME_USER_ID = GameUserID;
                                }
                            }
                            else
                            {
                                Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的卡片不是本系統認可的數位學生證 或 卡片，卡號：{GameUserID},長度{GameUserID.Length} 碼，本活動資料庫無此卡號。</font>";
                            }
                        }
                        else
                        {
                            Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的 QR CODE 非本活動所用，卡號：{GameUserID},長度{GameUserID.Length} 碼。</font>";
                        }
                    }
                }
                else if (St >= 2)
                {
                    Message = $"喔喔！刷卡失敗，請再試一次或洽詢服務臺喔！<br><font style='font-size:20px'>您的卡片對應到多位來賓之資料，請聯絡系統人員，卡號：{GameUserID}。</font>";
                }
                else
                {
                    GAME_USER_ID = GameUserID;
                    string LogId = string.Empty;
                    string ApplyMessage = string.Empty;
                   // SaveCreApplyData1(T26.GAME_NO, GAME_USER_ID, T26, null, null, null, ref Db, ref ApplyMessage, ref LogId);
                }
            }
            else
            {
                GAME_USER_ID = GameUserID;
            }

            return GAME_USER_ID;
        }

        /// <summary>
        /// 啟用臨時卡
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="GameUserID"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        private bool CreAddt27Status(string GAME_NO, string GameUserID, ECOOL_DEVEntities Db, ref string Message)
        {
            var T27 = Db.ADDT27.Where(a => a.GAME_NO == GAME_NO && a.GAME_USER_ID == GameUserID && a.STATUS == (byte)ADDT27.StatusVal.未使用).FirstOrDefault();
            if (T27 != null)
            {
                T27.STATUS = (byte)ADDT27.StatusVal.使用中;
                T27.CHG_PERSON = T27.TEMP_USER_ID;
                T27.CHG_DATE = DateTime.Now;
                Db.Entry(T27).State = System.Data.Entity.EntityState.Modified;

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = ex.Message;
                    return false;
                }
            }

            return true;
        }

        public class BatchWorkTypeVal
        {
            /// <summary>
            /// 學生匯入(有e酷幣帳號)
            /// </summary>
            static public string BatchApplyStudent = "BatchApplyStudent";

            /// <summary>
            /// 訪客匯入(卡片)
            /// </summary>
            static public string BatchApplyCard = "BatchApplyCard";

            /// <summary>
            /// 單筆新增(卡片)
            /// </summary>
            static public string AddApplyCard = "AddApplyCard";

            /// <summary>
            /// 編輯人員
            /// </summary>
            static public string EditApplyCard = "EditApplyCard";

            /// <summary>
            /// 餘額轉入
            /// </summary>
            //static public string CashInto = "CashInto";

            static public string GetDesc(string Val)
            {
                if (Val == BatchApplyStudent)
                {
                    return "學生匯入";
                }
                else if (Val == BatchApplyCard)
                {
                    return "訪客匯入";
                }
                else if (Val == AddApplyCard)
                {
                    return "訪客單筆新增";
                }
                else if (Val == EditApplyCard)
                {
                    return "編輯人員";
                }
                else
                {
                    return string.Empty;
                }
            }
        }
    }
}