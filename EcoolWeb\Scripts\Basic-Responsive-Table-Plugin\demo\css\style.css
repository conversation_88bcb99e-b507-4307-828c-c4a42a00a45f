/*
 * Basic Table Demo CSS.
 */

@import url(http://fonts.googleapis.com/css?family=Lato:400,700|Montserrat:400,700);

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: #e4ebeb;
  color: #636363;
  font-family: 'Lato', sans-serif;
  line-height: 1.5;
}

#page {
  padding: 50px 100px;
}

@media only screen and (max-width: 568px) {
  #page {
    padding: 50px 30px;
  }
}

h1,
h2,
h3,
h4,
h5 {
  line-height: 1.2;
  font-family: 'Montserrat', sans-serif;
  font-weight: normal;
}

h1 {
  color: #5b8e8d;
  font-size: 3rem;
  font-weight: 700;
  letter-spacing: -0.01em;
  margin-bottom: 0.75em;
  text-transform: uppercase;
}

@media only screen and (max-width: 568px) {
  h1 {
    font-size: 2.25rem;
  }
}

h2 {
  color: #82bcba;
  font-size: 1.75rem;
  margin-bottom: 0.75em;
  margin-top: 2em;
}

h3 {
  color: #82bcba;
  font-size: 1.25rem;
  margin-bottom: 0.75em;
  margin-top: 2em;
}

a,
a:visited,
a:active {
  color: #5b8e8d;
  text-decoration: none;
}

a:hover {
  color: #53bcba;
}

p {
  font-size: 1.125rem;
  word-break: break-word;
}

p.credits {
  font-size: .8125rem;
  margin-top: 6em;
}

@media only screen and (max-width: 568px) {
  .phone-block {
    display: block;
  }

  .phone-hide {
    display: none;
  }
}

code {
  background: #c6d8d8;
  border-radius: 5px;
  color: #3d6969;
  display: block;
  margin: 2em 0;
  padding: 15px;
  position: relative;
}

code:after {
  bottom: 5px;
  color: #e4ebeb;
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  position: absolute;
  right: 10px;
  text-transform: uppercase;
}

code.css:after {
  content: 'css';
}

code.js:after {
  content: 'js';
}

table {
  background: white;
  border-collapse: collapse;
  margin: 1.25em 0 0;
  width: 100%;
}

table tr,
table th,
table td {
  border: none;
  border-bottom: 1px solid #e4ebeb;
  font-family: 'Lato', sans-serif;
  font-size: .875rem;
}

table th,
table td {
  padding: 10px 12px;
  text-align: left;
}

table th {
  background: #56a2cf;
  color: #ffffff;
  text-transform: uppercase;
}

table tr td {
  background: #eaf3f5;
  color: #999999;
}

table tr:nth-of-type(2n+2) td {
  background: #ffffff;
}

table.bt tfoot th,
table.bt tfoot td,
table.bt tbody td {
  font-size: .8125rem;
  padding: 0;
}

table.bt tfoot th:before,
table.bt tfoot td:before,
table.bt tbody td:before {
  background: #56a2cf;
  color: white;
  margin-right: 10px;
  padding: 2px 10px;
}

table.bt tfoot th .bt-content,
table.bt tfoot td .bt-content,
table.bt tbody td .bt-content {
  display: inline-block;
  padding: 2px 5px;
}

table.bt tfoot th:first-of-type:before,
table.bt tfoot th:first-of-type .bt-content,
table.bt tfoot td:first-of-type:before,
table.bt tfoot td:first-of-type .bt-content,
table.bt tbody td:first-of-type:before,
table.bt tbody td:first-of-type .bt-content {
  padding-top: 10px;
}

table.bt tfoot th:last-of-type:before,
table.bt tfoot th:last-of-type .bt-content,
table.bt tfoot td:last-of-type:before,
table.bt tfoot td:last-of-type .bt-content,
table.bt tbody td:last-of-type:before,
table.bt tbody td:last-of-type .bt-content {
  padding-bottom: 10px;
}

/*
 * Example 2:
 * Container breakpoint
 */
.clearfix::before,
.clearfix::after {
 content: " ";
 display: table;
}

.clearfix::after {
 clear: both;
}

.col-sm-3 {
  padding-top: 1em;
}

@media (min-width: 768px) {
  .col-sm-3,
  .col-sm-9 {
    float: left;
  }

  .col-sm-3 {
    padding-left: 2em;
    width: 25%;
  }
  .col-sm-9 {
    width: 75%;
  }
}

/*
 * Example 5:
 * Media query over js resize
 */
@media only screen and (max-width: 568px) {
  #table-no-resize thead {
    display: none;
  }

  #table-no-resize tbody td {
    border: none !important;
    display: block;
    font-size: .8125rem;
    padding: 0;
    vertical-align: top;

    /* IE 9 */
    float: left\9;
    width: 100%\9;
  }

  #table-no-resize tbody td:before {
    background: #56a2cf;
    content: attr(data-th) ": ";
    color: white;
    display: inline-block;
    font-weight: bold;
    margin-right: 10px;
    padding: 2px 10px;
    width: 6.5em;
  }

  #table-no-resize tbody td .bt-content {
    display: inline-block;
    padding: 2px 5px;
  }

  #table-no-resize tbody td:first-of-type:before,
  #table-no-resize tbody td:first-of-type .bt-content {
    padding-top: 10px;
  }

  #table-no-resize tbody td:last-of-type:before,
  #table-no-resize tbody td:last-of-type .bt-content {
    padding-bottom: 10px;
  }
}

/*
 * Example 6:
 * Two axis styling
 */
table.two-axis tr td:first-of-type {
  background: #cadde1;
}

@media only screen and (max-width: 568px) {
  table.two-axis tr td:first-of-type,
  table.two-axis tr:nth-of-type(2n+2) td:first-of-type,
  table.two-axis tr td:first-of-type:before {
    background: #3584b3;
    color: #ffffff;
  }

  table.two-axis tr td:first-of-type {
    border-bottom: 1px solid #e4ebeb;
  }

  table.two-axis tr td:first-of-type:before {
    padding-bottom: 10px;
  }
}

/*
 * Example 7:
 * Max height
 */
.bt-wrapper.active {
  margin-top: 1.5em;
}
.bt-wrapper.active table {
  margin: 0;
}


table {
  width: 100%;
}
.table {
  margin: 30px 0;
  border-collapse: collapse;
  border-spacing: 0;
  empty-cells: show;
  border: 1px solid #e0e0e0;
}
.table caption {
  color: #333;
  font-style: italic;
  font-size: 85%;
  line-height: 1;
  padding: 1em 0;
  text-align: center;
}
.table td,
.table th {
  border-left: 1px solid #e0e0e0;
  border-width: 0 0 0 1px;
  font-size: inherit;
  margin: 0;
  overflow: visible;
  padding: 0.5em 1em;
  font-weight: normal;
}
.table td:first-child,
.table th:first-child {
  border-left-width: 0;
}
.table thead,
.table tfoot {
  background-color: #f05050;
  color: #fff;
  text-align: left;
  vertical-align: bottom;
}
.table thead td,
.table tfoot td,
.table thead th,
.table tfoot th {
  border-color: #fff;
}
.table td {
  background-color: transparent;
}
.table_striped tr:nth-child(2n-1) td {
  background-color: #ececec;
}
.table_bordered td {
  border-bottom: 1px solid #e0e0e0;
}
.table_bordered tbody > tr:last-child > td,
.table_bordered thead > tr:last-child > td {
  border-bottom-width: 0;
}
.table_horizontal td,
.table_horizontal th {
  border-width: 0 0 1px 0;
  border-bottom: 1px solid #e0e0e0;
}
.table_horizontal tbody > tr:last-child > td,
.table_horizontal thead > tr:last-child > td {
  border-bottom-width: 0;
}
