﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'ro', {
	border: '<PERSON><PERSON><PERSON><PERSON> marginii',
	caption: 'Tit<PERSON> (Caption)',
	cell: {
		menu: 'Celulă',
		insertBefore: 'Inserează celulă înainte',
		insertAfter: 'Inserează celulă după',
		deleteCell: 'Şterge celule',
		merge: 'Uneşte celule',
		mergeRight: 'Uneşte la dreapta',
		mergeDown: 'Uneşte jos',
		splitHorizontal: 'Împarte celula pe orizontală',
		splitVertical: 'Împarte celula pe verticală',
		title: 'Proprietăți celulă',
		cellType: 'Tipul celulei',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Aliniament orizontal',
		vAlign: 'Aliniament vertical',
		alignBaseline: 'Baseline',
		bgColor: 'Culoare fundal',
		borderColor: 'Culoare bordură',
		data: 'Data',
		header: 'Antet',
		yes: 'Da',
		no: 'Nu',
		invalidWidth: 'Lățimea celulei trebuie să fie un număr.',
		invalidHeight: 'Înălțimea celulei trebuie să fie un număr.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Alege'
	},
	cellPad: 'Spaţiu în cadrul celulei',
	cellSpace: 'Spaţiu între celule',
	column: {
		menu: 'Coloană',
		insertBefore: 'Inserează coloană înainte',
		insertAfter: 'Inserează coloană după',
		deleteColumn: 'Şterge celule'
	},
	columns: 'Coloane',
	deleteTable: 'Şterge tabel',
	headers: 'Antente',
	headersBoth: 'Ambele',
	headersColumn: 'Prima coloană',
	headersNone: 'Nimic',
	headersRow: 'Primul rând',
	invalidBorder: 'Dimensiunea bordurii trebuie să aibe un număr.',
	invalidCellPadding: 'Spațierea celulei trebuie sa fie un număr pozitiv',
	invalidCellSpacing: 'Spațierea celului trebuie să fie un număr pozitiv.',
	invalidCols: 'Numărul coloanelor trebuie să fie mai mare decât 0.',
	invalidHeight: 'Inaltimea celulei trebuie sa fie un numar.',
	invalidRows: 'Numărul rândurilor trebuie să fie mai mare decât 0.',
	invalidWidth: 'Lățimea tabelului trebuie să fie un număr.',
	menu: 'Proprietăţile tabelului',
	row: {
		menu: 'Rând',
		insertBefore: 'Inserează rând înainte',
		insertAfter: 'Inserează rând după',
		deleteRow: 'Şterge rânduri'
	},
	rows: 'Rânduri',
	summary: 'Rezumat',
	title: 'Proprietăţile tabelului',
	toolbar: 'Tabel',
	widthPc: 'procente',
	widthPx: 'pixeli',
	widthUnit: 'unitate lățime'
} );
