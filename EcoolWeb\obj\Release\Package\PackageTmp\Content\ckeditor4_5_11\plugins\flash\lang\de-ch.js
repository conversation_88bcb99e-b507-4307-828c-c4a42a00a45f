﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'de-ch', {
	access: 'Skriptzugriff',
	accessAlways: 'Immer',
	accessNever: 'Nie',
	accessSameDomain: 'Gleiche Domain',
	alignAbsBottom: 'Abs Unten',
	alignAbsMiddle: 'Abs Mitte',
	alignBaseline: 'Basislinie',
	alignTextTop: 'Text oben',
	bgcolor: 'Hintergrundfarbe',
	chkFull: 'Vollbildmodus erlauben',
	chkLoop: 'Endlosschleife',
	chkMenu: 'Flash-Menü aktivieren',
	chkPlay: 'Automatisch Abspielen',
	flashvars: 'Variablen für Flash',
	hSpace: 'Horizontal-Abstand',
	properties: 'Flash-Eigenschaften',
	propertiesTab: 'Eigenschaften',
	quality: 'Qualität',
	qualityAutoHigh: 'Auto Hoch',
	qualityAutoLow: 'Auto Niedrig',
	qualityBest: 'Beste',
	qualityHigh: 'Hoch',
	qualityLow: 'Niedrig',
	qualityMedium: 'Mittel',
	scale: 'Skalierung',
	scaleAll: 'Alles anzeigen',
	scaleFit: 'Passgenau',
	scaleNoBorder: 'Ohne Rand',
	title: 'Flash-Eigenschaften',
	vSpace: 'Vertikal-Abstand',
	validateHSpace: 'HSpace muss eine Zahl sein.',
	validateSrc: 'URL darf nicht leer sein.',
	validateVSpace: 'VSpace muss eine Zahl sein.',
	windowMode: 'Fenstermodus',
	windowModeOpaque: 'Deckend',
	windowModeTransparent: 'Transparent',
	windowModeWindow: 'Fenster'
} );
