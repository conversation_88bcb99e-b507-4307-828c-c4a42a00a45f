/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Size2={directory:"Size2/Regular",family:"LatinModernMathJax_Size2",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,332,0,0],40:[847,347,458,131,395],41:[847,347,458,63,327],47:[1108,608,768,57,713],91:[850,350,417,217,383],92:[1108,608,768,55,711],93:[850,350,417,34,200],123:[850,350,583,98,486],124:[972,472,278,115,164],125:[850,350,583,97,485],160:[0,0,332,0,0],770:[746,-572,768,0,768],771:[753,-548,778,0,778],774:[742,-578,784,0,784],780:[740,-566,768,0,768],785:[757,-592,784,0,784],812:[-96,270,768,0,768],813:[-108,282,768,0,768],814:[-96,260,784,0,784],815:[-118,282,784,0,784],816:[-118,323,778,0,778],8214:[972,472,364,57,309],8260:[1108,608,768,57,713],8425:[735,-541,1110,0,1110],8730:[1150,650,1000,110,1020],8739:[972,472,278,115,164],8741:[972,472,364,57,309],8968:[850,350,472,182,443],8969:[850,350,472,29,290],8970:[850,350,472,182,443],8971:[850,350,472,29,290],9001:[850,350,472,139,411],9002:[850,350,472,61,333],9140:[735,-541,1110,0,1110],9141:[-111,305,1110,0,1110],9180:[761,-511,1508,0,1508],9181:[-81,331,1508,0,1508],9182:[818,-509,1494,0,1494],9183:[-78,387,1494,0,1494],9184:[854,-612,1550,0,1550],9185:[-182,424,1550,0,1550],10214:[850,350,480,142,458],10215:[850,350,480,22,338],10216:[850,350,472,139,411],10217:[850,350,472,61,333],10218:[850,350,682,139,621],10219:[850,350,682,61,543],10222:[864,364,323,118,267],10223:[864,364,323,56,205]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Size2"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size2/Regular/Main.js"]);
