﻿@model ECOOL_APP.EF.ADDT15

@{
    ViewBag.Title = "校外榮譽-刪除校外榮譽內容";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")




<div class="text-center">
    <h3>確定要刪除這筆資料嗎</h3>
</div>
<img src="~/Content/img/web-bar2-revise-11.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ADDI07">
    <div  class="Details">
        <dl class="dl-horizontal">

            <dt>
                @Html.DisplayNameFor(model => model.USERNAME)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.USERNAME)
            </dd>

            <dt>

                @Html.DisplayNameFor(model => model.OAWARD_ITEM)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.OAWARD_ITEM)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.OAWARD_SCORE)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.OAWARD_SCORE)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.CASH)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.CASH)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.REMARK)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.REMARK)
            </dd>
        </dl>

        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()

            <div class="Div-btn-center">
                <button value="Save" type="submit" class="btn btn-default">
                    確定送出
                </button>
                <a href='@Url.Action("QUERY", "ADDI07")' class="btn btn-default">
                    放棄編輯
                </a>
            </div>
        }
    </div>
</div>
