﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI14ViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div>
    @Html.ActionLink("回功能清單列表", "Index", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-sm btn-sys" })
</div>
<br/><br />
@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()


  
        <div class="panel panel-ACC">
            <div class="panel-heading  text-center">
                @Html.BarTitle()
            </div>
            <div class="panel-body">
                <div class="form-horizontal">

                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="form-group">
                        @Html.LabelFor(model => model.BRE_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.BRE_NO, new { htmlAttributes = ViewData["IDObjectBRE_NO"] as IDictionary<string, object> })
                            @Html.ValidationMessageFor(model => model.BRE_NO, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.BRE_NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.BRE_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填"} })
                            @Html.ValidationMessageFor(model => model.BRE_NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.CONTROLLER, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.CONTROLLER, new { htmlAttributes = new { @class = "form-control", @placeholder = "非標題必填" } })
                            @Html.ValidationMessageFor(model => model.CONTROLLER, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.LEVEL_ID, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.LEVEL_ID, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填，請填數字" } })
                            @Html.ValidationMessageFor(model => model.LEVEL_ID, "", new { @class = "text-danger" })
                        </div>
                    </div>

          

                    <div class="form-group">
                        @Html.LabelFor(model => model.LINK_ADDR, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.LINK_ADDR, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.LINK_ADDR, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.BRE_NO_PRE, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.BRE_NO_PRE, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.BRE_NO_PRE, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.ORDER_BY, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.ORDER_BY, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填，請填數字" } })
                            @Html.ValidationMessageFor(model => model.ORDER_BY, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.TARGET, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DropDownListFor(model => model.TARGET, (IEnumerable<SelectListItem>)ViewBag.TARGETSelectList, new { @class = "form-control" })
                            @Html.ValidationMessageFor(model => model.TARGET, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.BRE_TYPE, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DropDownListFor(model => model.BRE_TYPE, (IEnumerable<SelectListItem>)ViewBag.BRE_TYPESelectList, new { @class = "form-control" })
                            @Html.ValidationMessageFor(model => model.BRE_TYPE, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.FUN_DESC, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.FUN_DESC, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.FUN_DESC, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.GROUP_DESC, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.GROUP_DESC, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.GROUP_DESC, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.WORK_DATES, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.WORK_DATES, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                            @Html.ValidationMessageFor(model => model.WORK_DATES, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.WORK_DATEE, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.WORK_DATEE, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                            @Html.ValidationMessageFor(model => model.WORK_DATEE, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.APP_USE_YN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            <div class="checkbox">
                                @Html.CheckBox("APP_USE_YN", DataConvert.YNConvertToBoolean(Model.APP_USE_YN)) 是
                                @Html.ValidationMessageFor(model => model.APP_USE_YN, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    </div>

                </div>
                <div class="panel panel-ACC-Color5">
                    <div class="table-responsive">
                        @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                        <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                            <caption>
                                <div class="Caption_Div">明細資料</div>
                            </caption>
                            <thead>
                                <tr class="text-center">
                                    <th>
                                        序號
                                    </th>
                                    <th>
                                        刪除
                                    </th>
                                    <th>
                                        預設動作
                                    </th>
                                    <th>
                                        @Html.DisplayNameFor(model => model.Details_List.First().ACTION_ID)
                                    </th>
                                    <th>
                                        @Html.DisplayNameFor(model => model.Details_List.First().ACTION_NAME)
                                    </th>
                                    <th>
                                        @Html.DisplayNameFor(model => model.Details_List.First().ACTION_TYPE)
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var Data in Model.Details_List)
                                {
                                    <tr class="selectable">
                                        <td class="text-center">
                                            @(Data.ITEM + 1)
                                            @Html.HiddenFor(model => model.Details_List[Data.ITEM].ITEM)
                                        </td>
                                        <td class="text-center">
                                            @Html.CheckBoxFor(model => model.Details_List[Data.ITEM].Del, Data.Del)
                                        </td>
                                        <td class="text-center">
                                            @Html.CheckBoxFor(model => model.Details_List[Data.ITEM].DF_ACTION_ID_Check, Data.DF_ACTION_ID_Check)
                                        </td>
                                        <td>
                                            @Html.EditorFor(model => model.Details_List[Data.ITEM].ACTION_ID, new { htmlAttributes = new { @class = "form-control" } })
                                            @Html.ValidationMessageFor(model => model.Details_List[Data.ITEM].ACTION_ID, "", new { @class = "text-danger" })
                                        </td>
                                        <td>
                                            @Html.EditorFor(model => model.Details_List[Data.ITEM].ACTION_NAME, new { htmlAttributes = new { @class = "form-control" } })
                                            @Html.ValidationMessageFor(model => model.Details_List[Data.ITEM].ACTION_NAME, "", new { @class = "text-danger" })
                                        </td>
                                        <td>
                                            <select class="form-control" name="Details_List[@Data.ITEM].ACTION_TYPE">
                                                @{
                                                    string SelectedVal = string.Empty;

                                                    foreach (var item in ViewBag.ACTION_TYPE_ITEM as IEnumerable<SelectListItem>)
                                                    {
                                                        SelectedVal = Data.ACTION_TYPE == item.Value ? "selected" : "";
                                                        <option value="@item.Value" @SelectedVal>@item.Text</option>
                                                    }
                                                }
                                            </select>
                                            @Html.ValidationMessageFor(model => model.Details_List[Data.ITEM].ACTION_TYPE, "", new { @class = "text-danger" })
                                        </td>
                                    </tr>
                                                    }
                            </tbody>
                        </table>
                        <div style="height:30px"></div>
                        <div class="row">
                            <div class="col-xs-6"></div>
                            <div class="col-xs-6">
                                <div class="input-group">
                                    @Html.TextBox("ITEM_NUM", TempData["ITEM_NUM"], new { @class = "form-control", @placeholder = "請填筆數", onchange = "if (isNaN(this.value) || this.value<=0) {alert('請填數字!');this.value=''};" })
                                    <div class="input-group-btn">
                                        <input type=button class="btn btn-default" name=additem value="增加筆數" onclick="AddItem()">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
               



                <div class="form-group">
                        <div class="col-xs-12 text-center">
                            @Html.Hidden("TOLTAL", TempData["TOLTAL"])
                            @Html.Hidden("DATA_TYPE")
                            @Html.Hidden("DATA_YN", (string)ViewBag.DATA_YN)

                            @Html.ValidationMessage("additem", new { @class = "text-danger" })
                           
                            <div class="form-inline">
                             
                            

                                @if ((string)ViewBag.DATA_YN == "Y")
                                {
                                    <input type="button" value="存檔" class="btn btn-default" onclick="Save('Update')" />
                                    <input type="button" value="刪除" class="btn btn-default" onclick="Save('Deldate')" />
                                }
                                else
                                {
                                    <input type="button" value="存檔" class="btn btn-default" onclick="Save('Create')" />
                                }

                            </div>
                            
                       
                        </div>
                </div>
            </div>
        </div>
    
}


@section Scripts {
    <script language="JavaScript">
            $(document).ready(function () {

                $("#WORK_DATES,#WORK_DATEE").datepicker({
                    dateFormat: "yy/mm/dd",
                    changeMonth: true,
                    changeYear: true,
                    showOn: "button",
                    buttonImage: "../Content/img/icon/calendar.gif",
                    buttonImageOnly: true,
                });
            });


        function AddItem()
        {
            form1.DATA_TYPE.value = 'AddItem'
            form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
            form1.submit();
        }

        function Save(Val)
        {
            form1.DATA_TYPE.value = Val
            form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
            form1.submit();
        }

    </script>
}