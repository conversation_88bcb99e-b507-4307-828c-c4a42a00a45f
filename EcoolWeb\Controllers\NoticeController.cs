﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EntityFramework.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{

    [SessionExpire]
    public class NoticeController : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "Notice";
        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;
        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;


        private ECOOL_DEVEntities Db = new ECOOL_DEVEntities();

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg = string.Empty;

        [CheckPermission(CheckBRE_NO = "ZZZI32", CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Index()
        {
            this.Shared();
            return View();
        }

        /// <summary>
        /// 列表部分檢示
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="STATUS"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [CheckPermission(CheckBRE_NO = "ZZZI32", CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(NoticeApiIndexViewModel model)
        {
            this.Shared();

            if (model == null) model = new NoticeApiIndexViewModel();

            model.SCHOOL_NO = DefaultSCHOOL_NO;
            model.USER_NO = USER_NO;

            model = new NoticeApiController().Index(model);

            if (model!=null )
            {
                if (model.List.Count()>0)
                {
                    var arrNOTIFICATION_ID = model.List.Select(a => a.RefAPPT02.NOTIFICATION_ID).ToArray();

                    var Updata = Db.APPT02.Where(u => u.SCHOOL_NO == model.SCHOOL_NO
                            && u.USER_NO == model.USER_NO && u.STATUS != APPT02.StatusVal.Cre
                            && arrNOTIFICATION_ID.Contains(u.NOTIFICATION_ID))
                           .Update(u => new APPT02 { STATUS = APPT02.StatusVal.Read });
                }
            }

           

            return PartialView(model);
        }


        #region  Shared
        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (string.IsNullOrWhiteSpace(Panel_Title) == false)
            {

                if (string.IsNullOrWhiteSpace(Bre_Name) == false)
                {
                    ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;
                }
                else
                {
                    ViewBag.Panel_Title = Panel_Title;
                }
            }


            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }
        #endregion


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}