﻿
@{
    ViewBag.Title = "進步加值";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<br/>
<span style="white-space: nowrap;font-size: 12pt;font-weight: bold; color:red">
    好運不常有，努力一定有！酷幣點數大放送來囉！
</span>
<br />
<span style="white-space: nowrap;font-size: 12pt;font-weight: bold;color:blue">
    因為您的努力，積極學習參與，所以你有機會參加酷幣大放送活動！只要你的酷幣每增加<br />
    100點，你就能多玩一次拉霸，獲得更多酷幣！
</span>
<br />
<table align="center">
    @{
        short cols = 3;
        short total = 6;
      
        for (short i = 1; i <= total; i++)
        {
            string filename = Url.Content("~/Content/img/") + "monkey-0" + i.ToString() + ".png";
            if (i % cols == 1 )
            {
                
                @:<tr>
                }

                <td>
                    <a href="@Url.Action("ArrivedChanceRun", "Home")">
                        <img src='@filename' />
                    </a>
                </td>

            @*if (i % cols ==0    )
            {
                @:<\tr>
                }*@
        }
}

</table>
<script type="text/javascript">
    window.history.forward(1);
</script>