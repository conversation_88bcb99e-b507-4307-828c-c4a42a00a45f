/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/fontdata-extra.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(n){var w="2.7.5";var l=n.FONTDATA.DELIMITERS;var o="H",d="V";var b="NeoEulerMathJax_Alphabets",t="NeoEulerMathJax_Arrows",x="NeoEulerMathJax_Fraktur",s="NeoEulerMathJax_Main",i="NeoEulerMathJax_Marks",v="NeoEulerMathJax_NonUnicode",p="NeoEulerMathJax_Normal",y="NeoEulerMathJax_Operators",m="NeoEulerMathJax_Script",c="NeoEulerMathJax_Shapes",j="NeoEulerMathJax_Size1",h="NeoEulerMathJax_Size2",g="NeoEulerMathJax_Size3",f="NeoEulerMathJax_Size4",e="NeoEulerMathJax_Size5",r="NeoEulerMathJax_Symbols",k="NeoEulerMathJax_Variants",u="NeoEulerMathJax_Normal",a="NeoEulerMathJax_Normal",A="NeoEulerMathJax_Normal";var z={8260:{dir:d,HW:[[0.912,s],[1.199,j],[1.799,h],[2.399,g],[2.999,f]]},8417:{dir:o,HW:[[0.449,i]],stretch:{left:[8406,i],rep:[57348,e],right:[8407,s]}},8430:{dir:o,HW:[[0.418,i]],stretch:{left:[8430,i],rep:[57349,e]}},8431:{dir:o,HW:[[0.418,i]],stretch:{rep:[57349,e],right:[8431,i]}},8719:{dir:d,HW:[[1,y],[1.4,j]]},8720:{dir:d,HW:[[1,y],[1.4,j]]},8721:{dir:d,HW:[[1,y],[1.4,j]]},8743:{dir:d,HW:[[0.718,s],[0.998,j],[1.395,h]]},8744:{dir:d,HW:[[0.7,s],[0.998,j],[1.395,h]]},8745:{dir:d,HW:[[0.6,s],[0.965,j],[1.358,h]]},8746:{dir:d,HW:[[0.6,s],[0.965,j],[1.358,h]]},8747:{dir:d,HW:[[1.111,s],[2.222,j]]},8748:{dir:d,HW:[[1.111,y],[2.222,j]]},8749:{dir:d,HW:[[1.111,y],[2.222,j]]},8750:{dir:d,HW:[[1.111,y],[2.222,j]]},8846:{dir:d,HW:[[0.6,s],[0.965,j],[1.358,h]]},8896:{dir:d,HW:[[0.718,y],[0.998,j],[1.395,h]]},8897:{dir:d,HW:[[0.7,y],[0.998,j],[1.395,h]]},8898:{dir:d,HW:[[0.6,y],[0.965,j],[1.358,h]]},8899:{dir:d,HW:[[0.6,y],[0.965,j],[1.358,h]]},9180:{dir:o,HW:[[0.925,s],[1.199,j],[1.799,h],[2.399,g],[2.999,f]],stretch:{left:[57353,e],rep:[57354,e],right:[57355,e]}},9181:{dir:o,HW:[[0.925,s],[1.199,j],[1.799,h],[2.399,g],[2.999,f]],stretch:{left:[57356,e],rep:[57357,e],right:[57358,e]}},10764:{dir:d,HW:[[1.111,y],[2.222,j]]}};for(var q in z){if(z.hasOwnProperty(q)){l[q]=z[q]}}MathJax.Ajax.loadComplete(n.fontDir+"/fontdata-extra.js")})(MathJax.OutputJax["HTML-CSS"]);
