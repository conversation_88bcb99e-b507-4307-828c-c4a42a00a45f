﻿@model UserIndexViewModel

@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string whereFriendsUserNo = HRMT07.GetStringMyADDStudent(user) ?? "";
    string ActiveFriends = (user.USER_NO == whereFriendsUserNo) ? "active" : "";


}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@if (user != null)
{
    if (user.USER_TYPE == UserType.Student)
    {
        <div style="height:15px"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-4 col-md-offset-4">
                    <div class="login-panel panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title text-center">
                                @if (!string.IsNullOrWhiteSpace(Model.PlayerUrl))
                                {
                                    <img src="@Url.Content(Model.PlayerUrl)" style="max-height:200px" />
                                }
                            </h3>
                        </div>
                        <div class="css-table">
                            <div class="tr">
                                <div class="td" style="background-color:#87CEFA;border:1px solid #483D8B;width:50%;height:30px;text-align:center;margin:auto;vertical-align:middle;font-size:15px">
                                    <b>目前酷幣點數 @Model.CASH 點</b>
                                </div>
                                <div class="td" style="background-color:#87CEFA;border:1px solid #483D8B;width:50%;height:30px;text-align:center;margin:auto;vertical-align:middle;font-size:15px">
                                    <b> 您跑 @Model.Run 公里</b>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <div class="col-md-4 col-md-offset-4">
                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("MyPhoto", "SECI01")">
                        個人化編輯
                    </button>

                    <button type="button" class="btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " onclick="funBookW()">
                        我的文章
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("OneIndex", "ZZZI34", new { WhereMyWork = "True", WhereIsColorboxForUser = "True" })">
                        我的藝廊
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("BOOK_APPLY", "SECI01", new { WhereIsColorboxForUser = "True" })">
                        我的閱讀認證
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("RunMap", "ADDI11", new { WhereIsColorboxForUser = "True" })">
                        我的運動撲滿
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("MyVideoView", "ADDI12", new { WhereIsColorboxForUser = "True" })">
                        我的小小舞臺
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("QUERY", "ADDI06", new { WhereIsColorboxForUser = "True", whereUserNo = user.USER_NO })">
                        我的校內表現
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("QUERY", "ADDI07", new { WhereIsColorboxForUser = "True", whereUserNo = user.USER_NO })">
                        我的校外榮譽
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("Details", "SECI03", new { WhereIsColorboxForUser = "True", whereIDNO = Model.IDNO, ShowOnMobile = "True" })">
                        我的健康資料
                    </button>
                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("Index", "BarcCodeMyCash", new { WhereIsColorboxForUser = "True", WhereKeyword = user.USER_NO, ShowOnMobile = "True" })">
                        我的默默
                    </button>

                    @*<button  class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg "  onclick="doSearch('whereUserNo','@whereFriendsUserNo');" id="orderSubject" type="button">訂閱文章</button>*@
                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("Index", "ADDI01", new { WhereIsColorboxForUser="True",whereUserNo=whereFriendsUserNo})">
                        訂閱的文章
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("MyFriendsRunLog", "ADDI11", new { WhereIsColorboxForUser="True"})">
                        朋友運動
                    </button>
                </div>

                <div class="col-md-4 col-md-offset-4" style="margin-top: 10px">
                    <a href="https://www.youtube.com/watch?v=S9IU7HKacwo&t=" target="_blank">
                        <img src="~/Content/img/TreasureBox.gif" class="img-responsive" alt="Responsive image" />
                    </a>
                </div>
            </div>
        </div>
        @*<div class="row" style="margin-top:20px">
                <div class="col-md-4 col-md-offset-4">
                    <a href="https://www.youtube.com/watch?v=S9IU7HKacwo&t=" target="_blank">
                        <img src="~/Content/img/TreasureBox.gif" class="img-responsive" alt="Responsive image" />
                    </a>
                </div>
            </div>*@
        <div style="height:25px"></div>
        <div style="display:none">
            @{
                int No = 1;
                string IdName = string.Empty;

                <div class="arrWRITING_NO">
                    @foreach (var item in Model.arrWRITING_NO)
                    {
                        IdName = "W" + No.ToString();

                        <a id="@IdName" class="groupWRITING_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                            @item
                        </a>
                        No++;
                    }
                </div>
            }
        </div>
    }
    else if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin)
    {
        <div style="margin-top:70px; float:left;"></div>
        <div class="container" style="margin-top:20px">
            <div class="row" style="height:50px">
                <div class="col-md-4 col-md-offset-4" style="float:right">
                    <button type="button" class=" btn btn-default btn-lg"  onclick="location.reload(true);"><i class="fa fa-refresh" aria-hidden="true" style="padding-right:4px"></i>重新整理</button>
                </div>
            </div>
            <div class="row">

                <div class="col-md-4 col-md-offset-4">
                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("VerifyList", "ADDI01",new { whereWritingStatus="0,2"})">
                        線上投稿任務
                        <font style="padding-left:200px;color:#ffd800">@ViewBag.ADDT01Qty</font>
                    </button>
                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("../ADDT/ADDTList_CheckPending", "ADDT",new { whereCLASS_NO=user.TEACH_CLASS_NO})">
                        閱讀認證任務
                        &nbsp;<span style="padding-left:200px;color:#ffd800">@ViewBag.ADDT06Cnt</span>
                    </button>
                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("ArtGalleryList", "ZZZI34",new { WhereVerify = true, WhereFrom="Tasklist"})">
                        線上藝廊任務
                        &nbsp;<span style="padding-left:200px;color:#ffd800">@ViewBag.ADDT21Qty</span>
                    </button>


                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("Edit", "ADDI11",new { ISTASKLIST=true})">
                        新增跑步量
                    </button>
                    @if (ViewBag.isShow=="Y")
                    {
                        <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("SysIndex", "ADDI09",new { ISTASKLIST=true})">
                            快速加點
                        </button>
                    }

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("QuerySelectView1", "ADDI09",new { ISTASKLIST=true})">
                        幹部加點
                    </button>

                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("SearchClassIndex", "GAAI01", new { IsSearch=1})">
                        防身警報器登記
                    </button>
                    <button type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("ModifyQuery", "AWA004", new { FirstPage=true,SouBre_NO="ModifyQuery"})">
                        獎品頒發名單
                        &nbsp;<span style="padding-left:200px;color:#ffd800">@ViewBag.TRA04</span>
                    </button>
                </div>
            </div>
            <div class="row" style="margin-top:20px">
                <div class="col-md-4 col-md-offset-4">
                    <a href="https://www.youtube.com/watch?v=S9IU7HKacwo&t=" target="_blank">
                        <img src="~/Content/img/TreasureBox.gif" class="img-responsive" alt="Responsive image" />
                    </a>
                </div>
            </div>
        </div>

    }

    <script language="javascript">

        $(document).ready(function () {
            $(".groupWRITING_NO").colorbox({ iframe: true, opacity: 0.5, width: "90%", height: "90%", rel: 'groupWRITING_NO' });
            $(".group1").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });
        });

        function funBookW() {

            if ($('#W1').length > 0) {
                $('#W1').click();
            }
            else {
                alert('無任何資料')
            }
        }
    </script>
}