﻿@{
    ViewBag.Title = "閱讀認證待審核項目";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<table border="0" cellspacing="0" cellpadding="0" width="615px" align="center">
    <tr>
        <td align="right">
            <div id="lnk_btn">
                @Html.ActionLink("【認證審核一覽表】", "ADDTALLList", null, new { @class = "btn-primary LinkButton" })
               
            </div>
        </td>
    </tr>
</table>
<img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image"/>
<table id="list" class="scroll" cellpadding="0" cellspacing="0"></table>
<div id="pager" class="scroll" style="text-align: center;"></div>
<input type="hidden" id="hidUserRule" value=@user.USER_TYPE />

<link href="~/Content/css/ui.jqgrid.css" rel="stylesheet" type="text/css" />
@section Scripts
    {
    <script src="~/Scripts/jquery-migrate-1.2.1.min.js" type="text/javascript"></script>
    <script src="~/Scripts/js/i18n/grid.locale-zh-tw.js" type="text/javascript"></script>
    <script src="~/Scripts/jquery.jqGrid.js" type="text/javascript"></script>
    <script type="text/javascript">

        $(function () {
        $("#list").jqGrid({
            url: '../ADDT/GetADDTPendingData/',
            datatype: 'json',
            mtype: 'GET',
            height: 'auto',
            colNames: ['申請日期', '學年', '學期', '班級', '座號', '姓名', '書名','批閱'],//
            colModel: [
            { name: 'CRE_DATE', index: 'CRE_DATE',height: 60, width: 100, align: 'center', cellattr:bgcolorattr },
            { name: 'SYEAR', index: 'SYEAR',height: 60, width: 50, align: 'center' },
            { name: 'CSEMESTER', index: 'CSEMESTER', height: 60, width: 40, align: 'center' },
            { name: 'CLASS_NO', index: 'CLASS_NO', height: 60, width: 40, align: 'center' },
            { name: 'SEAT_NO', index: 'SEAT_NO', height: 60, width: 40, align: 'center' },
            { name: 'NAME', index: 'NAME', height: 60, width: 55, align: 'center' },
            { name: 'BOOK_NAME', index: 'BOOK_NAME', height: 60, width: 200, align: 'left' },
            { name: 'APPLY_NO', index: 'APPLY_NO', height: 60, width: 40, align: 'center', formatter: addLink }],
            pager: jQuery('#pager'),
            rowNum: 10,    // 由Server取回10筆
            rowList: [5, 10, 20, 50],   // 每頁顯示筆數
            sortname: 'APPLY_NO',
            sortorder: "asc",
            viewrecords: true,
            imgpath: '/scripts/themes/coffee/images'
            //caption: '閱讀認證待審查項目'
        });
        
    });

    function bgcolorattr(rowId,cellValue,rawobject,cm,rdata)
    {
        if (rowId % 2 == 1) {
            $("tr.jqgrow:odd").css("background",  "#EBEBC8");
        }
        else {
            $("tr.jqgrow:even").css("background", "#FFFFEC");
        }
    }

    function addLink(cellvalue, options, rowObject) {
        //to get row Id
        //alert(options.rowId);
        // to get product Id
        //alert(rowObject.CRE_DATE);
        //alert(cellvalue);
        //return " <a href='/ADDT/ADDTList_CheckPendingDetail/?APPLY_NO=" + cellvalue + "' style='color:Blue;font-weight:bold;height:25px;width:120px;' type='button' title='Modify' onclick=\"Modify('" + cellvalue + "')\" >修改</a> | " +
        //       " <a href='/ADDT/ADDTList_CheckPendingDetail/?APPLY_NO=" + cellvalue + "' style='color:Blue;font-weight:bold;height:25px;width:120px;' type='button' title='Delete' onclick=\"Delete('" + cellvalue + "')\" >刪除</a>";
        var Mode;
        
        if ($("#hidUserRule").val() == "T") {
            Mode = 'Edit';
        }
        else {
            Mode = 'Del';
        }
        return "<a href='../ADDT/ADDTList_CheckPendingDetail/?Mode=" + Mode + "&APPLY_NO=" + cellvalue + "&Rule=" + $("#hidUserRule").val() + "' style='color:Blue;font-weight:bold;height:25px;width:120px;' type='button' title='Modify' >批閱</a>"// +
               //"<a href='../ADDT/ADDTList_CheckPendingDetail/?Mode=Del&APPLY_NO=" + cellvalue + "' style='color:Blue;font-weight:bold;height:25px;width:120px;' type='button' title='Delete' >刪除</a>";

      
    }
    </script>
}
