/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsUp/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXIntegralsUp,{32:[0,0,250,0,0],160:[0,0,250,0,0],8748:[824,320,596,59,638],8749:[824,320,826,59,868],8751:[824,320,548,35,637],8752:[824,320,876,54,972],8753:[824,320,478,54,547],8754:[824,320,441,35,530],8755:[824,320,475,35,564],10763:[812,332,706,43,661],10764:[812,332,1093,59,1135],10765:[812,332,467,59,509],10766:[812,332,467,59,509],10767:[812,332,529,59,571],10768:[812,332,346,35,435],10769:[812,332,478,54,547],10770:[812,332,365,35,434],10771:[812,332,384,54,453],10772:[812,332,509,54,578],10773:[812,332,396,35,485],10774:[812,332,412,31,481],10775:[812,332,771,45,831],10776:[812,332,455,45,515],10777:[812,332,504,45,569],10778:[812,332,504,45,569],10779:[935,332,453,45,495],10780:[812,455,376,59,509]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsUp/Regular/All.js");
