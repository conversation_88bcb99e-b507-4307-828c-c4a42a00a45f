/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Variants/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Variants-italic"]={directory:"Variants/Italic",family:"STIXMathJax_Variants",style:"italic",testString:"\u00A0\uE22D\uE22E\uE22F\uE230\uE231\uE232\uE233\uE234\uE235\uE236\uE237\uE238\uE239\uE23A",32:[0,0,250,0,0],160:[0,0,250,0,0],57901:[677,45,852,43,812],57902:[670,3,724,35,709],57903:[671,11,569,43,586],57904:[662,0,801,34,788],57905:[670,4,553,40,599],57906:[662,0,652,43,710],57907:[671,131,580,40,580],57908:[664,21,831,41,845],57909:[662,0,575,38,591],57910:[662,120,632,31,785],57911:[670,13,809,30,783],57912:[670,7,693,30,653],57913:[671,45,1166,40,1128],57914:[795,37,957,40,1064],57915:[669,10,737,38,729],57916:[662,0,667,38,709],57917:[671,131,744,43,704],57918:[662,3,854,38,816],57919:[671,0,634,38,671],57920:[721,0,509,41,730],57921:[672,13,817,37,950],57922:[677,33,638,33,680],57923:[685,32,956,33,998],57924:[672,13,692,38,739],57925:[675,131,719,34,763],57926:[664,94,752,38,714],57954:[460,11,570,56,514],57958:[460,0,570,100,415],57962:[460,0,570,59,487],57966:[461,217,570,40,513],57970:[450,217,570,17,542],57974:[450,218,570,23,536],57978:[668,10,570,28,553],57982:[450,217,570,40,543],57986:[668,10,570,50,519],57990:[460,217,570,23,526]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Variants-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Variants/Italic/Main.js"]);
