﻿@model GameRewardViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "Reward" });
    }
}

@using (Html.BeginForm("RewardSave", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    <div class="panel panel-ZZZ">
        <div class="panel-heading text-center">
            鼓勵語與獎勵
        </div>
        <div class="panel-body">
            <div class="table-responsive">
                <div class="css-table" style="width:99%;">
                    <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                        <div class="th" style="text-align:center">
                            刪除
                        </div>
                        <div class="th" style="text-align:center;">
                            答對率(起)
                        </div>
                        <div class="th" style="text-align:center;">
                            答對率(迄)
                        </div>
                        <div class="th" style="text-align:center;">
                            鼓勵語
                        </div>
                        <div class="th" style="text-align:center">
                            獎勵
                        </div>
                    </div>
                    <div id="editorRows" class="tbody">
                        @if (Model.RewardInfo.Count() > 0)
                        {
                            foreach (var Item in Model.RewardInfo)
                            {
                                Html.RenderPartial("_RewardInfo", Item);
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-footer">
            <div class="row">
                <div class="col-md-12 col-xs-12 text-right">
                    <span class="input-group-btn">
                        <button class="btn btn-info btn-sm" type="button" onclick="onAddItem()">
                            增加 答對率
                        </button>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="存檔" class="btn btn-default" onclick="Save(this)" />
                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        function Save(ButtonName)
        {
            $(targetFormID).attr("action", "@Url.Action("RewardSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();

            ButtonName.disabled = true;
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("EditQA", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

       //增加明細
        function onAddItem() {

            var RATE_E = $('#editorRows .tr:last input[name*="RATE_E"]').val();

            if (RATE_E == undefined || RATE_E == "") {
                RATE_E = 0
            }
            else {
                RATE_E = RATE_E/1 + 1;
            }

           var data = {
               "RATE_S": RATE_E,
               "REWARD_CASH": 0,
             };

            $.ajax({
                url: '@Url.Action("_RewardInfo")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }
    </script>
}