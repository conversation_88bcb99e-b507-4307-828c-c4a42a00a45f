﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
   public  class ZZZI09MyDataViewModel
    {
        ///Summary
        ///閱讀本數
        ///Summary
        [Display(Name = "閱讀本數")]
        public Nullable<int> BOOK_QTY { get; set; }

        ///Summary
        ///等級名稱
        ///Summary
        [Display(Name = "等級名稱")]
        public string LEVEL_DESC { get; set; }

        ///Summary
        ///等級
        ///Summary
        [Display(Name = "等級")]
        public string LEVEL_ID { get; set; }

        ///Summary
        ///剩多少本升級
        ///Summary
        [Display(Name = "剩多少本升級")]
        public string UNLEVEL_QTY { get; set; }
    }
}
