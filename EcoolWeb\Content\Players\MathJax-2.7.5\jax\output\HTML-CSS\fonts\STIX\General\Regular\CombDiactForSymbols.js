/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/CombDiactForSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{8400:[760,-627,0,-453,-17],8401:[760,-627,0,-453,-17],8402:[662,156,0,-242,-192],8406:[760,-548,0,-453,-17],8411:[622,-523,0,-462,35],8412:[622,-523,0,-600,96],8413:[725,221,0,-723,223],8414:[780,180,0,-730,230],8415:[843,341,0,-840,344],8417:[760,-548,0,-453,25],8420:[1023,155,0,-970,490],8421:[662,156,0,-430,-40],8422:[662,156,0,-335,-102],8423:[725,178,0,-650,166],8424:[-119,218,0,-462,35],8425:[681,-538,0,-480,53],8426:[419,-87,0,-658,118],8427:[756,217,0,-448,193],8428:[-119,252,0,-453,-17],8429:[-119,252,0,-453,-17],8430:[-40,252,0,-453,-17],8431:[-40,252,0,-453,-17],8432:[819,-517,0,-357,-87]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/CombDiactForSymbols.js");
