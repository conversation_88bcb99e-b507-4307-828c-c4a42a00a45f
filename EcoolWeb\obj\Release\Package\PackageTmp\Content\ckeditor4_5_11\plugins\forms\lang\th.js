﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'th', {
	button: {
		title: 'รายละเอียดของ ปุ่ม',
		text: 'ข้อความ (ค่าตัวแปร)',
		type: 'ข้อความ',
		typeBtn: 'Button',
		typeSbm: 'Submit',
		typeRst: 'Reset'
	},
	checkboxAndRadio: {
		checkboxTitle: 'คุณสมบัติของ เช็คบ๊อก',
		radioTitle: 'คุณสมบัติของ เรดิโอบัตตอน',
		value: 'ค่าตัวแปร',
		selected: 'เลือกเป็นค่าเริ่มต้น',
		required: 'Required' // MISSING
	},
	form: {
		title: 'คุณสมบัติของ แบบฟอร์ม',
		menu: 'คุณสมบัติของ แบบฟอร์ม',
		action: 'แอคชั่น',
		method: 'เมธอด',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'คุณสมบัติของ ฮิดเดนฟิลด์',
		name: 'ชื่อ',
		value: 'ค่าตัวแปร'
	},
	select: {
		title: 'คุณสมบัติของ แถบตัวเลือก',
		selectInfo: 'อินโฟ',
		opAvail: 'รายการตัวเลือก',
		value: 'ค่าตัวแปร',
		size: 'ขนาด',
		lines: 'บรรทัด',
		chkMulti: 'เลือกหลายค่าได้',
		required: 'Required', // MISSING
		opText: 'ข้อความ',
		opValue: 'ค่าตัวแปร',
		btnAdd: 'เพิ่ม',
		btnModify: 'แก้ไข',
		btnUp: 'บน',
		btnDown: 'ล่าง',
		btnSetValue: 'เลือกเป็นค่าเริ่มต้น',
		btnDelete: 'ลบ'
	},
	textarea: {
		title: 'คุณสมบัติของ เท็กแอเรีย',
		cols: 'สดมภ์',
		rows: 'แถว'
	},
	textfield: {
		title: 'คุณสมบัติของ เท็กซ์ฟิลด์',
		name: 'ชื่อ',
		value: 'ค่าตัวแปร',
		charWidth: 'ความกว้าง',
		maxChars: 'จำนวนตัวอักษรสูงสุด',
		required: 'Required', // MISSING
		type: 'ชนิด',
		typeText: 'ข้อความ',
		typePass: 'รหัสผ่าน',
		typeEmail: 'อีเมล',
		typeSearch: 'ค้นหาก',
		typeTel: 'หมายเลขโทรศัพท์',
		typeUrl: 'ที่อยู่อ้างอิง URL'
	}
} );
