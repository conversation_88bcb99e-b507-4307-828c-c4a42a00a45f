﻿@{
    ViewBag.Title = "教師首頁";
    Layout = "~/Views/Shared/_Layout.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    if (user.USER_TYPE == ECOOL_APP.EF.UserType.Student)
    {
        Url.Action("../Access/Error");
    }
}

<style>
    .modal-body {
        position: relative;
        padding: 10px;
    }
</style>
@if (user.USER_TYPE == UserType.Admin)
{
    if (ViewBag.AWAT02Count == 0)
    {
        @Html.ActionLink("獎品尚未設定請設定", "AwatQ02", "Awat", "", new { @class = "btn btn-sm btn-sys" })
        <br />
    }
    if (ViewBag.ADDT03Count == 0)
    {
        @Html.ActionLink("護照尚未設定請設定", "BOOK_MAINTAINQuery", "ADDI03", "", new { @class = "btn btn-sm btn-sys" })
    }

}
<br />
@*<div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">酷幣主機停機(暫停服務)公告：</h4>
                </div>
                <div class="modal-body" id="remind-content">
                    <ol>
                        配合市政府機電檢修停電，酷幣主機預計計於109年1月17日(星期五)下午7時至109年1月19日(星期日)上午9時止暫停服務，不便之處，請包涵。
                    </ol>
                </div>
            </div>
        </div>
    </div>*@
@if (ViewBag.GetDataListShowYN == true || ViewBag.GetALLDataListShowYN == true)
{
    if (ViewBag.RemindItems != null || ViewBag.ALLRemindItems != null)
    {
        <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">
            @*<div class="col-xs-2 text-right"> <img src="~/Content/img/Prohibition_plagiarism@0,25x.png" style="width:100px;height:auto" /></div>*@
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    @if (ViewBag.GetALLDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.ALLRemindItems as List<BDMT02_REF>)
                        {

                            var str = "";
                            str = "https://line.me/R/ti/p/@101hrbss";
                            var str2 = "";
                            str2 = "https://www.youtube.com/watch?v=3m3RLukLqSU";
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT<br />

                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">

                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003" && item.CONTENT_TXT == "【觀看說明影片】")
                                    {

                                    }
                                    else
                                    {
                                        @item.CONTENT_TXT

                                    }


                                    <br />

                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003")
                                    {


                                        <a href="@str2">【觀看說明影片】</a><br /><br />


                                        @:客服連結<a href="@str">@str</a>
                                    }
                                </div>
                            }
                        }
                    }
                    @if (ViewBag.GetDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.RemindItems as List<BDMT02_REF>)
                        {
                            var str = "";
                            str = "https://line.me/R/ti/p/@101hrbss";
                            var str2 = "";
                            str2 = "https://www.youtube.com/watch?v=3m3RLukLqSU";
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT<br />


                                    </h4>

                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">

                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003" && item.CONTENT_TXT == "【觀看說明影片】")
                                    {

                                    }
                                    else
                                    {
                                        @item.CONTENT_TXT

                                    }
                                    <br />
                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003")
                                    {


                                        <a href="@str2">【觀看說明影片】</a><br /><br />
                                        <br />

                                        @:客服連結<a href="@str">@str</a>
                                    }
                                </div>
                            }

                        }
                    }
                </div>
            </div>
            @*@if (!AppMode)
                {
                    <div class="col-xs-2  text-left"> <img src="~/Content/img/Copyright@0,25x.png" style="width:100px;height:auto" /></div>
                }
                else
                {
                    <div class="col-xs-2  text-left" style="margin-left: 200px;"> <img src="~/Content/img/Copyright@0,25x.png" style="width:70px;height:auto" /></div>

                }*@
        </div>

    }
}
@Html.Partial("_BET02Partial")
<br />
@Html.Action("_ImgPlay", "ZZZI25")
<br />

@if (!EcoolWeb.Models.UserProfileHelper.CheckROLE_SCHOOL_ADMIN(user, null))
{
    ViewBag.ShowTurnInCount = 0; // 不是管理者就不能看到
}

@Html.Action("_TaskList", "Home")

@{
    // 處理ViewBag轉json
    var jss = new System.Web.Script.Serialization.JavaScriptSerializer();
    var toastInfoJson = jss.Serialize(ViewBag.ToastData as List<APPT02>);

    string alertwhos = jss.Serialize(ViewBag.alertWhos); // 當日點數超額警告對象
}

@section scripts{
    <script src="@Url.Content("~/Scripts/toastr/toastr.min.js")"></script>

    <script>
        // toastr: 推送網頁 notification

        var peopleExCount = parseInt('@ViewBag.ShowTurnInCount');
        window.onload = function () {
            RemindShow()
        }
        function RemindShow() {
            var remind_font = $("#remind-content").text().length;

            if (remind_font > 0) {
                $('#remind-modal').modal('show');
            }
        }

        if (peopleExCount > 0) {
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": false,
                "progressBar": false,
                "positionClass": "toast-bottom-right",
                "preventDuplicates": false,
                "onclick": function () {
                    window.location.href = "@Url.Action("Index", "ZZZI20")";
                },
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "0",
                "extendedTimeOut": "0",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "tapToDismiss": false,
                "onHidden": false
            };
            toastr["warning"]("有 " + peopleExCount +"筆 轉學生酷幣轉匯資料<br><br><button class='btn btn-warning btn-block'>前往</button>");
        }
        var alertwhos = JSON.parse('@Html.Raw(alertwhos)');
        console.log(alertwhos);
        $.each(alertwhos, function (idx, item) {
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": false,
                "progressBar": false,
                "positionClass": "toast-bottom-right",
                "preventDuplicates": false,
                "onclick": function () {
                    window.location.href = "@Url.Action("Query3", "AWA002")";
                },
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "0",
                "extendedTimeOut": "0",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "tapToDismiss": false,
                "onHidden": false
            };
            toastr["error"]("警告! " + item.CLASS_NO + "班" + item.SEAT_NO + "號(學號" + item.USER_NO + ") 學生，今日點數異常超過500點");
        });
    </script>
}

@section css{
    <link href="@Url.Content("~/Scripts/toastr/toastr.min.css")" rel="stylesheet" />

}