﻿@model ECOOL_APP.com.ecool.Models.DTO.SchoolBorrowBookViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

    int BookPageCount = 0;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@{
    Html.RenderAction("_Menu", new { NowAction = "RankOfSchoolBookBorrow" });
}

@using (Html.BeginForm("RankOfSchoolBookBorrow", "SECI07", FormMethod.Post, new { id = "SECI07", name = "SECI07" }))
{
    @Html.HiddenFor(m => m.OrdercColumn)
    <script>
        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            document.SECI07.submit();
        }
    </script>

    <div class="row">
        <div class="col-md-12">
            @Html.LabelFor(m => m.Where_SYEAR, new { @class = "col-md-1" })
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
            </div>
            @Html.LabelFor(m => m.Where_MONTH, new { @class = "col-md-1" })
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_MONTH, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control", @onchange = "this.form.submit();" })
            </div>
        </div>
    </div>
}
<br />

@if (ViewBag.SYearItems == null)
{
    <div>無任何借閱資料</div>
}
else if (string.IsNullOrWhiteSpace(Model.Where_SYEAR))
{
    <div>未輸入學年度</div>
}
else
{
    if (Model.SchoolRankList != null && Model.SchoolRankList.Count > 0)
    {
        <table class="table table-responsive table-striped table-hover text-center">
            <thead>
                <tr>

                    @if (user != null && user.USER_NO != "0000")
                    {
                    <th class="text-center" onclick="doSort('Months');">
                        @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().Months)
                        <img id="Months" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    }
                    else
                    {
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().SHORT_NAME)
                        </th>
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().SCHOOLQTY)
                        </th>
                    }

                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK');">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().BORROW_BOOK_COUNT)
                            <img id="RANK" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK');">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().RANK)
                            <img id="RANK" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>

                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK_AVG');">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().BORROW_BOOK_AVG)
                            <img id="RANK_AVG" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                    @if ( user.USER_NO == "0000")
                    {
                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK_AVG');">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().RANK_AVG)
                        </th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.SchoolRankList)
                {
                    <tr>

                        @if (user != null && user.USER_NO != "0000")
                        {
                            <th class="text-center">
                                @item.Months
                            </th>
                        }
                        else
                        {

                            <td>@item.SHORT_NAME</td>
                            <td>@item.SCHOOLQTY</td>
                        }
                        <td>@item.BORROW_BOOK_COUNT</td>
                        <td>@item.RANK</td>
                        <td>@item.BORROW_BOOK_AVG</td>
                        <td>@item.RANK_AVG</td>
                    </tr>
                    BookPageCount += item.BORROW_BOOK_COUNT;
                }
                    <tr>
                        <td></td>
                        <td></td>
                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger text-center">
                                本頁總借書量: @BookPageCount 本書　　
                            </div>
                        </td>
                        <td></td>
                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger text-center">
                                總平均借書量
                                @Model.SchoolRankList.Average(a => a.BORROW_BOOK_AVG).ToString("#0.0") 本書
                            </div>
                        </td>
                    </tr>
                <tr>

                    @if (user != null && user.USER_NO != "0000")
                    {
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().Months)
                        </th>
                    }
                    else
                    {
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().SHORT_NAME)
                        </th>
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().SCHOOLQTY)
                        </th>
                    }
                    <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK');">
                        @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().BORROW_BOOK_COUNT)
                    </th>
                    <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK');">
                        @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().RANK)
                    </th>
                    <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK_AVG');">
                        @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().BORROW_BOOK_AVG)
                    </th>
                    @if (user.USER_NO == "0000")
                    {
                        <th class="text-center" style="text-align: center;cursor:pointer;" onclick="doSort('RANK_AVG');">
                            @Html.DisplayNameFor(m => m.SchoolRankList.FirstOrDefault().RANK_AVG)
                        </th>
                    }
                </tr>

            </tbody>
        </table>
    }
    else
    {
        <div>資料量不足</div>
    }
}