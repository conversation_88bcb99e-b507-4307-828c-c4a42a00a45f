﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CER001IndexViewModel
    {
        /// <summary>
        /// 表頭
        /// </summary>
        public List<CER001AccreditationsViewModel> Accreditations { get; set; }

        /// <summary>
        /// 學生清單
        /// </summary>
        public List<HRMT01> Students { get; set; }
           public IPagedList<HRMT01> ListData;
        /// <summary>
        /// 是否通過資料
        /// </summary>
        public List<CER001PassListViewModel> PassList { get; set; }

        public List<CERT03_G> cERT03s { get; set; }
        public string WhereSearch { get; set; }

        public string WhereStatus { get; set; }
        /// <summary>
        /// 學校
        /// </summary>
        public string WhereSCHOOL_NO { get; set; }

        /// <summary>
        /// 護照細項名稱
        /// </summary>
        public string WhereACCREDITATION_NAME { get; set; }

        /// <summary>
        /// 護照ID
        /// </summary>
        public string WhereACCREDITATION_TYPE { get; set; }
        public string WhereSUBJECT_Item { get; set; }
        /// <summary>
        /// 年級
        /// </summary>
        public byte? WhereGRADE { get; set; }
        public byte? WhereSyaer { get; set; }
        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 姓名/學號
        /// </summary>
        public string WhereUser { get; set; }

        /// <summary>
        /// 年級+學期
        /// </summary>
        public List<string> WhereGRADE_SEMESTERs { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        public CER001IndexViewModel()
        {
            PageSize = 30;
        }
    }
}