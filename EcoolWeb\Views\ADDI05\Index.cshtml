@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@*@Html.Action("Index", "Breadcrumb", new { BRE_NO = (string)ViewBag.BRE_NO, ThisActive = ViewBag.Panel_Title })*@

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    <div id="PageContent">
        @Html.Action("PageContent", (string)ViewBag.BRE_NO)
    </div>

}
@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI05_INDEX_URLS = {
            pageContent: '@Url.Action("PageContent", (string)ViewBag.BRE_NO)',
            detail: '@Url.Action("detail", (string)ViewBag.BRE_NO)',
            edit: '@Url.Action("Edit", "ZZZI08")'
        };

        // 設置全局配置
        window.ADDI05_INDEX_CONFIG = {
            viewDataTypeA: "@ECOOL_APP.com.ecool.Models.DTO.ZZZI08.ZZZI08EditViewModel.VIEW_A"
        };
    </script>
    <script src="~/Scripts/ADDI05/index.js" nonce="cmlvaw"></script>
}