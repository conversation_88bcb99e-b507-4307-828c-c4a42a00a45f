@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@*@Html.Action("Index", "Breadcrumb", new { BRE_NO = (string)ViewBag.BRE_NO, ThisActive = ViewBag.Panel_Title })*@

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    <div id="PageContent">
        @Html.Action("PageContent", (string)ViewBag.BRE_NO)
    </div>

}
@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#form1';

        function FunPageProc(pageno) {
            form1.page.value = pageno
            funAjax()
        }

        function FunSort(SortName) {
            if (form1.OrderByName.value == SortName) {
                if (form1.SyntaxName.value == "Desc") {
                    form1.SyntaxName.value = "ASC"
                }
                else {
                    form1.SyntaxName.value = "Desc"
                }
            }
            else {
                form1.OrderByName.value = SortName;
                form1.SyntaxName.value = "Desc";
            }
            funAjax()
        }

        function doSearchBool(ColName, whereValue) {

            $("#" + ColName).val(whereValue);
            form1.page.value = 1;
            funAjax()
        }

        function funAjax()
        {
            var SearchContentsVal = form1.SearchContents.value
            var OrderByNameVal = form1.OrderByName.value
            var SyntaxNameVal = form1.SyntaxName.value
            var pageVal = form1.page.value
            var DIALOG_TYPEVal = form1.DIALOG_TYPE.value
            var whereShowDataVal = form1.whereShowData.value

            $.ajax({
                url: '@Url.Action("PageContent", (string)ViewBag.BRE_NO).ToString()',
                data: {
                    SearchContents: SearchContentsVal,
                    OrderByName: OrderByNameVal,
                    SyntaxName: SyntaxNameVal,
                    page: pageVal,
                    DialogType: DIALOG_TYPEVal,
                    whereShowData: whereShowDataVal
                },
                type: 'post',
                cache: false,
                dataType: 'html'
            })
            .done(function (data) {
                $('#PageContent').html(data);
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                console.error('AJAX request failed:', textStatus, errorThrown);
            });
        }

        function onBtnLink(DIALOG_ID)
        {
            form1.DIALOG_ID.value = DIALOG_ID
            form1.action = '@Url.Action("detail", (string)ViewBag.BRE_NO)';
            form1.submit();
        }

        function todoClear() {
            ////重設

            $(targetFormID).find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            funAjax()
        }

        function onAdd() {

            $('#VIEW_DATA_TYPE').val("@ECOOL_APP.com.ecool.Models.DTO.ZZZI08.ZZZI08EditViewModel.VIEW_A")
            form1.action = '@Url.Action("Edit", "ZZZI08")';
            form1.submit();

        }
    </script>
}