﻿@model List<ADDI13EditPeopleViewModel>

@if (Model != null)
{
    using (Html.BeginCollectionItem("Details"))
    {
        var Index = Html.GetIndex("Details");
        foreach (var item in Model)
        {

<div class="tr" id="Tr@(item.CARD_NO)">
    <div class="td" style="text-align:center">
        <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(item.CARD_NO)')"> <i class='glyphicon glyphicon-remove'></i></a>
    </div>
    <div class="td" style="text-align:center">
        @item.SHORT_NAME
    </div>
    <div class="td" style="text-align:center">
        @item.NAME
    </div>
    <div class="td" style="text-align:center" id="@(item.SCHOOL_NO+item.USER_NO)">
        @item.USER_NO
    </div>
    <div class="td" style="text-align:center" id="@(item.SCHOOL_NO+item.CLASS_NO+item.SEAT_NO)">
        @item.CLASS_NO
    </div>
    <div class="td" style="text-align:center">
        @item.SEAT_NO
    </div>
    <div class="td" style="text-align:center">
        @item.CHG_DATE
    </div>
</div>

        }

    }
}