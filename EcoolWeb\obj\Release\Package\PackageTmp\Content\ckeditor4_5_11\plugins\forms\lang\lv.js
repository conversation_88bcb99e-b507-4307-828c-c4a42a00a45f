﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'lv', {
	button: {
		title: '<PERSON>gas īpašī<PERSON>',
		text: 'Teks<PERSON> (vērtība)',
		type: 'Tips',
		typeBtn: 'Poga',
		typeSbm: 'Nosūtīt',
		typeRst: 'Atcelt'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Atzīmēšanas kastītes īpašības',
		radioTitle: 'Izvēles poga īpašības',
		value: 'Vērtība',
		selected: 'Iezīmēts',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Formas īpašības',
		menu: 'Formas īpašības',
		action: 'Darbī<PERSON>',
		method: 'Metode',
		encoding: 'Kodējums'
	},
	hidden: {
		title: '<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON> teksta rinda<PERSON> ī<PERSON>',
		name: '<PERSON><PERSON><PERSON><PERSON>',
		value: 'Vērtība'
	},
	select: {
		title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lauka ī<PERSON>',
		selectInfo: 'Informācija',
		opAvail: 'Pieejamās iespējas',
		value: 'Vērtība',
		size: 'Izmērs',
		lines: 'rindas',
		chkMulti: 'Atļaut vairākus iezīmējumus',
		required: 'Required', // MISSING
		opText: 'Teksts',
		opValue: 'Vērtība',
		btnAdd: 'Pievienot',
		btnModify: 'Veikt izmaiņas',
		btnUp: 'Augšup',
		btnDown: 'Lejup',
		btnSetValue: 'Noteikt kā iezīmēto vērtību',
		btnDelete: 'Dzēst'
	},
	textarea: {
		title: 'Teksta laukuma īpašības',
		cols: 'Kolonnas',
		rows: 'Rindas'
	},
	textfield: {
		title: 'Teksta rindas  īpašības',
		name: 'Nosaukums',
		value: 'Vērtība',
		charWidth: 'Simbolu platums',
		maxChars: 'Simbolu maksimālais daudzums',
		required: 'Required', // MISSING
		type: 'Tips',
		typeText: 'Teksts',
		typePass: 'Parole',
		typeEmail: 'Epasts',
		typeSearch: 'Meklēt',
		typeTel: 'Tālruņa numurs',
		typeUrl: 'Adrese'
	}
} );
