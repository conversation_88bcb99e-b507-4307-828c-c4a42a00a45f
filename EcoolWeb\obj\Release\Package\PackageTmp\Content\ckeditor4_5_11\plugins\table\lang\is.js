﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'is', {
	border: '<PERSON>reidd ramma',
	caption: 'Titill',
	cell: {
		menu: 'Reitur',
		insertBefore: 'Skjóta inn reiti fyrir aftan',
		insertAfter: 'Skjóta inn reiti fyrir framan',
		deleteCell: 'Fella reit',
		merge: 'Sameina reiti',
		mergeRight: 'Same<PERSON> til hægri',
		mergeDown: 'Sameina niður á við',
		splitHorizontal: 'Klj<PERSON><PERSON> reit lárétt',
		splitVertical: 'Klj<PERSON><PERSON> reit lóðrétt',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'Background Color',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'Reitaspássía',
	cellSpace: 'Bil milli reita',
	column: {
		menu: 'Dálkur',
		insertBefore: 'Skjóta inn dálki vinstra megin',
		insertAfter: 'Skjóta inn dálki hægra megin',
		deleteColumn: 'Fella dálk'
	},
	columns: 'Dálkar',
	deleteTable: 'Fella töflu',
	headers: 'Fyrirsagnir',
	headersBoth: 'Hvort tveggja',
	headersColumn: 'Fyrsti dálkur',
	headersNone: 'Engar',
	headersRow: 'Fyrsta röð',
	invalidBorder: 'Border size must be a number.', // MISSING
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Cell spacing must be a positive number.', // MISSING
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Table height must be a number.', // MISSING
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Table width must be a number.', // MISSING
	menu: 'Eigindi töflu',
	row: {
		menu: 'Röð',
		insertBefore: 'Skjóta inn röð fyrir ofan',
		insertAfter: 'Skjóta inn röð fyrir neðan',
		deleteRow: 'Eyða röð'
	},
	rows: 'Raðir',
	summary: 'Áfram',
	title: 'Eigindi töflu',
	toolbar: 'Tafla',
	widthPc: 'prósent',
	widthPx: 'myndeindir',
	widthUnit: 'width unit' // MISSING
} );
