﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uAWAT05
    {

        ///Summary
        ///來源種類 紀錄來源資料表名稱 ex.ADDT13
        ///Summary
        [Display(Name = "來源資料表")]
        public string SOURCE_TABLE { get; set; }


        ///Summary
        ///來源序號 同一個資料表名稱 可能有不同來源 ex. 1
        ///Summary
        [Display(Name = "來源序號")]
        public int? SOURCE_ITEM { get; set; }


        ///Summary
        ///來源資料表 KEY 欄位  ex.ADDT13.DIALOG_ID
        ///Summary
        [Display(Name = "來源資料表 KEY 欄位")]
        public string SOURCE_KEY { get; set; }



        ///Summary
        ///來源說明 ex. 線上有獎徵答活動完成
        ///Summary
        [Display(Name = "來源說明")]
        public string SOURCE_DESC { get; set; }

        ///Summary
        ///獲得點數
        ///Summary
        [Display(Name = "獲得點數")]
        public int? TO_CASG { get; set; }

        ///Summary
        ///備註
        ///Summary
        [Display(Name = "備註")]
        public string MEMO { get; set; }

        ///Summary
        ///CHG_PERSON
        ///Summary
        [Display(Name = "CHG_PERSON")]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [Display(Name = "CHG_DATE")]
        public DateTime CHG_DATE { get; set; }

        ///Summary
        ///CRE_PERSON
        ///Summary
        [Display(Name = "CRE_PERSON")]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
        [Display(Name = "CRE_DATE")]
        public DateTime CRE_DATE { get; set; }

    }
}
