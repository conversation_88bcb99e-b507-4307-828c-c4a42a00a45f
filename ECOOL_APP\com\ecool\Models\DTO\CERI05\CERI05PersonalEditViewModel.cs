﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CERI05PersonalEditViewModel : SearchFormViewModelBase
    {
        /// <summary>
        /// 年級
        /// </summary>
        public byte? WhereGRADE { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 學校
        /// </summary>
        public string WhereSCHOOL_NO { get; set; }

        /// <summary>
        /// 姓名/學號
        /// </summary>
        public string WhereUser { get; set; }

        public string ThisSCHOOL_NO { get; set; }

        public string ThisUSER_NO { get; set; }

        public HRMT01 MyData { get; set; }

        public virtual ICollection<CERI05EditAccreditationtViewModel> Accreditationts { get; set; }
        public virtual ICollection<CERI05EditAccreditationtViewModel> Accreditationt1s { get; set; }
    }
}