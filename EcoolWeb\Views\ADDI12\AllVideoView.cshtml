﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@if (AppMode)
{
    <a role="button" href='@Url.Action("Index",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Index" ? "active":"")">
        小小舞臺說明
    </a>
    <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
        我要申請小小舞臺
    </a>

    <a role="button" href='@Url.Action("PremierView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.PremierView ? "active":"")">
        首播影片 <span class="badge">@ViewBag.PremierCount</span>
    </a>
    <a role="button" href='@Url.Action("AllVideoView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.AllVideoView ? "active":"")">
        一般影片 <span class="badge">@ViewBag.AllVideoCount</span>
    </a>
    if (user != null)
    {

        if ((user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin))
        {
            <a role="button" href='@Url.Action("MyUploadVideoView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.MyUploadVideoView ? "active":"")">
                我上傳的影片
            </a>
        }
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.Action("_ListBeginForm", (string)ViewBag.BRE_NO, new { ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.AllVideoView })