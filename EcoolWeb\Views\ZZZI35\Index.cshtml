﻿
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("ImportExcel", "SECI03", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="panel-heading text-center" style="background-color:rgba(68, 157, 68, 1);color:rgba(249, 242, 244, 1);font-size:medium;margin-bottom:10px">
        匯入學生健康資訊
    </div>
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6">
                    @Html.ActionLink("身高體重與視力", "ImportExcel", "SECI03", new { ImportType = "HRMT08" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6">
                    @Html.ActionLink("體適能資料", "ImportExcel", "SECI03", new { ImportType = "HRMT09" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-offset-1 col-md-9">
                    <label class="text-danger">
                        上傳說明:<br />
                        1.上傳之 Excel 檔, 請依規定格式填寫(<a href="~/Content/ExcelSample/身高體重視力範本.xls" target="_blank" class="btn-table-link">下載身高體重視力範本S</a>),(<a href="~/Content/ExcelSample/體適能範本.xls" target="_blank" class="btn-table-link">下載體適能範本</a>)<br />
                        2.如果有修改，請下載歷史紀錄修正後上傳；或從健康中心(教育部體適能)系統下載後上傳。<br />
                        3.檔名不要是中文、特殊符號之類
                    </label>
                </div>
            </div>
        </div>
    </div>
}
