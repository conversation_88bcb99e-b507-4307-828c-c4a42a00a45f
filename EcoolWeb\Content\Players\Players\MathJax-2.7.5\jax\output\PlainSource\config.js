/*
 *  /MathJax/jax/output/PlainSource/config.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.PlainSource=MathJax.OutputJax({id:"PlainSource",version:"2.7.5",directory:MathJax.OutputJax.directory+"/PlainSource",extensionDir:MathJax.OutputJax.extensionDir+"/PlainSource",config:{styles:{".MathJax_PlainSource_Display":{"text-align":"center",margin:".75em 0px","white-space":"pre"},".MathJax_PlainSource_Display > span":{display:"inline-block","text-align":"left"}}}});if(!MathJax.Hub.config.delayJaxRegistration){MathJax.OutputJax.PlainSource.Register("jax/mml")}MathJax.OutputJax.PlainSource.loadComplete("config.js");
