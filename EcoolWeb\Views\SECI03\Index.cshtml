﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    if (ViewBag.isshowonMobil == "True")
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    string USERSCHOOLnO = user.SCHOOL_NO;
}

@if (!AppMode)
{
    @Html.Partial("_Title_Secondary")
}

@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = (string)ViewBag.BRE_NO, name = "form1" }))
{

    <div class="form-inline">

        <div class="form-inline" role="form">

            @if (user != null)
            {
                if (HRMT24_ENUM.CheckQAdmin(user) || ViewBag.VisibleImportExcel == "Y")
                {
                    <div class="form-group">
                        <label class="control-label">學號/姓名</label>
                    </div>
                    <div class="form-group">
                        @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                    </div>
                    <div class="form-group">
                        <label class="control-label">年級</label>
                    </div>
                    <div class="form-group">
                        @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                    </div>
                    <div class="form-group">
                        <label class="control-label">班級</label>
                    </div>
                    <div class="form-group">
                        @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                    </div>
                    <br />
                }
            }

            <div class="form-group">
                <label class="control-label">學年</label>
            </div>
            <div class="form-group">

                @Html.DropDownListFor(m => m.whereSYEAR, (IEnumerable<SelectListItem>)ViewBag.SYEARItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            @if (Model.whereShowType != ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness)
            {
                <div class="form-group">
                    <label class="control-label">學期</label>
                </div>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.whereSEMESTER, (IEnumerable<SelectListItem>)ViewBag.SEMESTERItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                </div>
            }
            else
            {
                @Html.HiddenFor(m => m.whereSEMESTER)
            }
            <br />
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear()" />
            @if (AppMode == false)
            {
                if (ViewBag.VisibleImportExcel == "Y")
                {
                    <a role="button" href='@Url.Action("ImportExcel", "SECI03")' class="btn-yellow btn btn-sm" style="float:right;margin-left:.5rem">
                        匯入Excel
                    </a>

                    <button type="button" class="btn-yellow btn btn-sm" onclick="ToExcel()" style="float:right">匯出excel</button>
                    <br />
                }
            }
        </div>
    </div>
    <br />
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.whereShowType)
    @Html.HiddenFor(m => m.whereIDNO)
    @Html.HiddenFor(m => m.whereSCHOOL_NO)
    string ActiveTALL = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL) ? "active" : "";
    string ActiveWEIGHT = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT) ? "active" : "";
    string ActiveVISION = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION) ? "active" : "";
    string ActiveFitness = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness) ? "active" : "";

    <div class="text-right">
        <button class="btn btn-xs btn-pink  @ActiveTALL " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)</button>
        <button class="btn btn-xs btn-pink  @ActiveWEIGHT " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)</button>
        <button class="btn btn-xs btn-pink  @ActiveVISION " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)</button>
        <button class="btn btn-xs btn-pink  @ActiveFitness " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness)</button>
    </div>

}

@if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness)
{
    <div class="panel panel-ACC">
        @if (AppMode == false)
        {
            <div class="panel-heading text-center">
                @Html.BarTitle()
            </div>
        }
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                <thead>
                    <tr>

                        <th></th>

                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().SYEAR)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().SEMESTER)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().NAME)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().V_SET_REACH_TEST)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().S_L_JUMP_TEST)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().SIT_UPS_TEST)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().C_P_F_TEST)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Hrmt09List)
                    {
                        <tr align="center" style="cursor:pointer;" onclick="onDetails('@item.IDNO')">

                            <td>
                                @if (AppMode)
                                {
                                    <button role="button" class="btn btn-default glyphicon glyphicon-search" style="padding:2px;margin:2px" onclick="onDetails('@item.IDNO')"></button>
                                }
                                else
                                {
                                    <button role="button" class="btn btn-default glyphicon glyphicon-search" onclick="onDetails('@item.IDNO')"></button>
                                }
                            </td>

                            <td>
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td>
                                @(item.SEMESTER == 1 ? "上" : "下")
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.V_SET_REACH_TEST)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.S_L_JUMP_TEST)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SIT_UPS_TEST)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.C_P_F_TEST)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    if (Model.Hrmt09List.Count() == 0)
    {
        @*<div class="text-center"><h4>本學期(年)尚無資料，可查詢其他條件</h4></div>*@
        <div class="text-center"><h4>本功能尚未啟用</h4></div>
    }

    <div>
        @Html.Pager(Model.Hrmt09List.PageSize, Model.Hrmt09List.PageNumber, Model.Hrmt09List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>
}
else
{
    <div class="panel panel-ACC">
        @if (AppMode == false)
        {
            <div class="panel-heading text-center">
                @Html.BarTitle()
            </div>
        }
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                <thead>
                    <tr>

                        <th></th>

                        <th style="text-align: center;" class="hidden-xs">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().SYEAR)
                        </th>
                        <th style="text-align: center" class="hidden-xs">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().SEMESTER)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().NAME)
                        </th>
                        @*<th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AGE)
                            </th>*@

                        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)
                        {
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().TALL)
                            </th>
                        }

                        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)
                        {
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().WEIGHT)
                            </th>
                            @*<th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().POSTURE_MEMO)
                                </th>*@
                            @*<th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().S_WEIGHT)
                                </th>*@
                            @*<th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().O_WEIGHT)
                                </th>*@
                            @*<th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().BMI)
                                </th>*@
                            @*<th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().S_BMI)
                                </th>*@
                        }

                        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)
                        {
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().G_VISION_RIGHT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().G_VISION_LEFT)
                            </th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Hrmt08List)
                    {
                        <tr align="center" style="cursor:pointer;" onclick="onDetails('@item.IDNO')">

                            <td>
                                @if (AppMode)
                                {
                                    <button role="button" class="btn btn-default glyphicon glyphicon-search" style="padding:2px;margin:2px" onclick="onDetails('@item.IDNO')"></button>
                                }
                                else
                                {
                                    <button role="button" class="btn btn-default glyphicon glyphicon-search" onclick="onDetails('@item.IDNO')"></button>
                                }
                            </td>

                            <td class="hidden-xs">
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td class="hidden-xs">
                                @Html.DisplayFor(modelItem => item.SEMESTER)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            @*<td>
                                    @Html.DisplayFor(modelItem => item.AGE)
                                </td>*@

                            @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)
                            {
                                <td>
                                    @Html.DisplayFor(modelItem => item.TALL)
                                </td>
                            }

                            @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)
                            {
                                <td>
                                    @Html.DisplayFor(modelItem => item.WEIGHT)
                                </td>
                                @*<td>
                                        @Html.DisplayFor(modelItem => item.POSTURE_MEMO)
                                    </td>*@
                                @*<td>
                                        @Html.DisplayFor(modelItem => item.S_WEIGHT)
                                    </td>*@
                                @*<td>
                                        @Html.DisplayFor(modelItem => item.O_WEIGHT)
                                    </td>*@
                                @*<td>
                                        @Html.DisplayFor(modelItem => item.BMI)
                                    </td>*@
                                @*<td>
                                        @Html.DisplayFor(modelItem => item.S_BMI)
                                    </td>*@
                            }

                            @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)
                            {
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_RIGHT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_LEFT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.G_VISION_RIGHT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.G_VISION_LEFT)
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    if (Model.Hrmt08List.Count() == 0)
    {
        <div class="text-center"><h4>本學期(年)尚無資料，可查詢其他條件</h4></div>
    }
    <div>
        @Html.Pager(Model.Hrmt08List.PageSize, Model.Hrmt08List.PageNumber, Model.Hrmt08List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>

}

<script type="text/javascript">
    var targetFormID = '#@ViewBag.BRE_NO';
    $(function () {
        $("#whereSCHOOL_NO").val('@user.SCHOOL_NO');



    })
    function FunPageProc(page) {
        if ($(targetFormID).size() > 0) {
            $('#Page').val(page)
            $(targetFormID).submit();
        }
    };

    function doSearch(ColName, whereValue) {
        $("#" + ColName).val(whereValue);
        FunPageProc(1)
    }

    function onDetails(IDNO) {

        $("#whereIDNO").val(IDNO);
        $(targetFormID).attr("action", "@Url.Action("Details", (string)ViewBag.BRE_NO)");
        $(targetFormID).submit();
    }

       function ToExcel() {
            $(targetFormID).attr('action', '@Url.Action("PrintExcel", (string)ViewBag.BRE_NO)').attr('target', '_blank');
            $(targetFormID).submit();
         };

    function todoClear() {
        ////重設

        $(targetFormID).find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        $(targetFormID).submit();
    }
</script>