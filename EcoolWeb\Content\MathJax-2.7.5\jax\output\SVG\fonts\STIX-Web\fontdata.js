/*
 *  /MathJax/jax/output/SVG/fonts/STIX-Web/fontdata.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(N,l,an,s){var C="2.7.5";var u="STIXMathJax_Alphabets-bold-italic",I="STIXMathJax_Alphabets-bold",A="STIXMathJax_Alphabets-italic",e="STIXMathJax_Alphabets",U="STIXMathJax_Arrows-bold",x="STIXMathJax_Arrows",g="STIXMathJax_DoubleStruck-bold-italic",i="STIXMathJax_DoubleStruck-bold",c="STIXMathJax_DoubleStruck-italic",b="STIXMathJax_DoubleStruck",q="STIXMathJax_Fraktur-bold",p="STIXMathJax_Fraktur",X="STIXMathJax_Latin-bold-italic",D="STIXMathJax_Latin-bold",E="STIXMathJax_Latin-italic",L="STIXMathJax_Latin",T="STIXMathJax_Main-bold-italic",al="STIXMathJax_Main-bold",R="STIXMathJax_Main-italic",F="STIXMathJax_Main",k="STIXMathJax_Marks-bold-italic",J="STIXMathJax_Marks-bold",n="STIXMathJax_Marks-italic",ab="STIXMathJax_Marks",aj="STIXMathJax_Misc-bold-italic",h="STIXMathJax_Misc-bold",af="STIXMathJax_Misc-italic",y="STIXMathJax_Misc",am="STIXMathJax_Monospace",ad="STIXMathJax_Normal-bold-italic",S="STIXMathJax_Normal-bold",W="STIXMathJax_Normal-italic",j="STIXMathJax_Operators-bold",w="STIXMathJax_Operators",P="STIXMathJax_SansSerif-bold-italic",B="STIXMathJax_SansSerif-bold",ao="STIXMathJax_SansSerif-italic",d="STIXMathJax_SansSerif",a="STIXMathJax_Script-bold-italic",Q="STIXMathJax_Script-italic",Y="STIXMathJax_Script",r="STIXMathJax_Shapes-bold-italic",K="STIXMathJax_Shapes-bold",m="STIXMathJax_Shapes",ag="STIXMathJax_Size1",ae="STIXMathJax_Size2",ac="STIXMathJax_Size3",aa="STIXMathJax_Size4",Z="STIXMathJax_Size5",ah="STIXMathJax_Symbols-bold",v="STIXMathJax_Symbols",G="STIXMathJax_Variants-bold-italic",o="STIXMathJax_Variants-bold",ak="STIXMathJax_Variants-italic",O="STIXMathJax_Variants";var t="H",f="V",M={load:"extra",dir:t},z={load:"extra",dir:f};var ai=[8722,F,0,0,0,-0.26,-0.26];N.Augment({FONTDATA:{version:C,baselineskip:1200,lineH:800,lineD:200,FONTS:{"STIXMathJax_Alphabets-bold-italic":"Alphabets/BoldItalic/Main.js","STIXMathJax_Alphabets-bold":"Alphabets/Bold/Main.js","STIXMathJax_Alphabets-italic":"Alphabets/Italic/Main.js",STIXMathJax_Alphabets:"Alphabets/Regular/Main.js","STIXMathJax_Arrows-bold":"Arrows/Bold/Main.js",STIXMathJax_Arrows:"Arrows/Regular/Main.js","STIXMathJax_DoubleStruck-bold-italic":"DoubleStruck/BoldItalic/Main.js","STIXMathJax_DoubleStruck-bold":"DoubleStruck/Bold/Main.js","STIXMathJax_DoubleStruck-italic":"DoubleStruck/Italic/Main.js",STIXMathJax_DoubleStruck:"DoubleStruck/Regular/Main.js","STIXMathJax_Fraktur-bold":"Fraktur/Bold/Main.js",STIXMathJax_Fraktur:"Fraktur/Regular/Main.js","STIXMathJax_Latin-bold-italic":"Latin/BoldItalic/Main.js","STIXMathJax_Latin-bold":"Latin/Bold/Main.js","STIXMathJax_Latin-italic":"Latin/Italic/Main.js",STIXMathJax_Latin:"Latin/Regular/Main.js","STIXMathJax_Main-bold-italic":"Main/BoldItalic/Main.js","STIXMathJax_Main-bold":"Main/Bold/Main.js","STIXMathJax_Main-italic":"Main/Italic/Main.js",STIXMathJax_Main:"Main/Regular/Main.js","STIXMathJax_Marks-bold-italic":"Marks/BoldItalic/Main.js","STIXMathJax_Marks-bold":"Marks/Bold/Main.js","STIXMathJax_Marks-italic":"Marks/Italic/Main.js",STIXMathJax_Marks:"Marks/Regular/Main.js","STIXMathJax_Misc-bold-italic":"Misc/BoldItalic/Main.js","STIXMathJax_Misc-bold":"Misc/Bold/Main.js","STIXMathJax_Misc-italic":"Misc/Italic/Main.js",STIXMathJax_Misc:"Misc/Regular/Main.js",STIXMathJax_Monospace:"Monospace/Regular/Main.js","STIXMathJax_Normal-bold-italic":"Normal/BoldItalic/Main.js","STIXMathJax_Normal-bold":"Normal/Bold/Main.js","STIXMathJax_Normal-italic":"Normal/Italic/Main.js","STIXMathJax_Operators-bold":"Operators/Bold/Main.js",STIXMathJax_Operators:"Operators/Regular/Main.js","STIXMathJax_SansSerif-bold-italic":"SansSerif/BoldItalic/Main.js","STIXMathJax_SansSerif-bold":"SansSerif/Bold/Main.js","STIXMathJax_SansSerif-italic":"SansSerif/Italic/Main.js",STIXMathJax_SansSerif:"SansSerif/Regular/Main.js","STIXMathJax_Script-bold-italic":"Script/BoldItalic/Main.js","STIXMathJax_Script-italic":"Script/Italic/Main.js",STIXMathJax_Script:"Script/Regular/Main.js","STIXMathJax_Shapes-bold-italic":"Shapes/BoldItalic/Main.js","STIXMathJax_Shapes-bold":"Shapes/Bold/Main.js",STIXMathJax_Shapes:"Shapes/Regular/Main.js",STIXMathJax_Size1:"Size1/Regular/Main.js",STIXMathJax_Size2:"Size2/Regular/Main.js",STIXMathJax_Size3:"Size3/Regular/Main.js",STIXMathJax_Size4:"Size4/Regular/Main.js",STIXMathJax_Size5:"Size5/Regular/Main.js","STIXMathJax_Symbols-bold":"Symbols/Bold/Main.js",STIXMathJax_Symbols:"Symbols/Regular/Main.js","STIXMathJax_Variants-bold-italic":"Variants/BoldItalic/Main.js","STIXMathJax_Variants-bold":"Variants/Bold/Main.js","STIXMathJax_Variants-italic":"Variants/Italic/Main.js",STIXMathJax_Variants:"Variants/Regular/Main.js"},VARIANT:{normal:{fonts:[F,am,L,e,ab,x,w,v,m,y,O,ag],remap:{124:[124,"-STIX-Web-variant"]}},bold:{fonts:[al,S,q,i,B,D,I,J,U,j,ah,K,h,o,ag],offsetA:119808,offsetG:120488,remap:{8706:120539,8711:120513},bold:true},italic:{fonts:[R,W,Q,c,ao,E,A,n,af,ak,ag],offsetA:119860,offsetG:120546,remap:{119893:8462,8706:120597,8711:120571},italic:true},"bold-italic":{fonts:[T,ad,a,g,P,X,u,k,r,aj,G,ag],offsetA:119860,offsetG:120604,remap:{119893:8462,8706:120655,8711:120629},bold:true,italic:true},"double-struck":{fonts:[b],offsetA:120120,offsetN:120792,remap:{120122:8450,120127:8461,120133:8469,120135:8473,120136:8474,120137:8477,120145:8484}},fraktur:{fonts:[p],offsetA:120068,remap:{120070:8493,120075:8460,120076:8465,120085:8476,120093:8488}},"bold-fraktur":{fonts:[q],offsetA:120172,bold:true},script:{fonts:[Q],offsetA:119964,italic:true,remap:{119965:8492,119968:8496,119969:8497,119971:8459,119972:8464,119975:8466,119976:8499,119981:8475,119994:8495,119996:8458,120004:8500}},"bold-script":{fonts:[a],offsetA:120016,bold:true,italic:true},"sans-serif":{fonts:[d],offsetA:120224,offsetN:120802,offsetP:57725,remap:{8706:57724}},"bold-sans-serif":{fonts:[B],offsetA:120276,offsetN:120812,offsetG:120662,remap:{8706:120713,8711:120687},bold:true},"sans-serif-italic":{fonts:[ao],offsetA:120328,offsetN:57780,offsetP:57791,remap:{8706:57790},italic:true},"sans-serif-bold-italic":{fonts:[P],offsetA:120380,offsetN:57846,offsetG:120720,remap:{8706:120771,8711:120745},bold:true,italic:true},monospace:{fonts:[am],offsetA:120432,offsetN:120822},"-STIX-Web-variant":{remap:{10887:57360,10888:57359,9651:9653,9661:9663,124:[124,l.VARIANT.NORMAL]},fonts:[O,m,w,F,am,L,e,ab,x,v,y,ag]},"-tex-caligraphic":{offsetA:57901,noLowerCase:1,fonts:[ak,R,W,Q,c,ao,E,A,n,af,ag],italic:true},"-tex-oldstyle":{offsetN:57953,remap:{57954:57957,57955:57961,57956:57965,57957:57969,57958:57973,57959:57977,57960:57981,57961:57985,57962:57989},fonts:[O,F,am,L,e,ab,x,w,v,m,y,ag]},"-tex-caligraphic-bold":{offsetA:57927,noLowerCase:1,fonts:[G,T,ad,a,g,P,X,u,k,r,aj,ag],italic:true,bold:true},"-tex-oldstyle-bold":{offsetN:57953,remap:{57956:57959,57957:57963,57958:57967,57959:57971,57960:57975,57961:57979,57962:57983,57963:57987,57964:57991},fonts:[o,al,S,q,i,B,D,I,J,U,j,ah,K,h,ag],bold:true},"-tex-mathit":{fonts:[R,W,Q,c,ao,E,A,n,af,ak,ag],italic:true,noIC:true},"-largeOp":{fonts:[ag,F]},"-smallOp":{}},RANGES:[{name:"alpha",low:97,high:122,offset:"A",add:26},{name:"Alpha",low:65,high:90,offset:"A"},{name:"number",low:48,high:57,offset:"N"},{name:"greek-non-unicode",low:945,high:969,offset:"E",add:25},{name:"greek",low:945,high:969,offset:"G",add:26},{name:"Greek",low:913,high:937,offset:"G"},{name:"vargreek",low:977,high:1014,offset:"G",remapOnly:true,remap:{1013:52,977:53,1008:54,981:55,1009:56,982:57,1012:17}},{name:"PUAgreek",low:945,high:969,offset:"P",add:25},{name:"PUAGreek",low:913,high:937,offset:"P"},{name:"varPUAgreek",low:977,high:1014,offset:"P",remapOnly:true,remap:{1013:50,977:51,981:52,1009:53,982:54,1012:17}}],RULECHAR:9135,REMAP:{10:32,12296:10216,12297:10217,10072:8739,755:730,756:714,65079:9182,65080:9183},REMAPACCENT:{"\u007E":"\u0303","\u2192":"\u20D7","\u2190":"\u20D6","\u0060":"\u0300","\u005E":"\u0302","\u00B4":"\u0301","\u2032":"\u0301","\u2035":"\u0300"},REMAPACCENTUNDER:{},DELIMITERS:{40:{dir:f,HW:[[853,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57344,Z],ext:[57345,Z],top:[57346,Z]}},41:{dir:f,HW:[[853,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57347,Z],ext:[57348,Z],top:[57349,Z]}},45:{alias:9135,dir:t},47:{dir:f,HW:[[690,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]]},61:M,91:{dir:f,HW:[[818,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57350,Z],ext:[57351,Z],top:[57352,Z]}},92:{dir:f,HW:[[690,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]]},93:{dir:f,HW:[[818,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57353,Z],ext:[57354,Z],top:[57355,Z]}},94:{alias:710,dir:t},95:{alias:9135,dir:t},123:{dir:f,HW:[[861,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57356,Z],ext:[57357,Z],mid:[57358,Z],top:[57359,Z]}},124:{dir:f,HW:[[690,F]],stretch:{bot:[124,F],ext:[124,F]}},125:{dir:f,HW:[[861,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57360,Z],ext:[57357,Z],mid:[57361,Z],top:[57362,Z]}},126:{alias:732,dir:t},175:{alias:9135,dir:t},710:{dir:t,HW:[[311,F],[560,ag],[979,ae],[1460,ac],[1886,aa],[2328,Z]]},711:M,713:{alias:9135,dir:t},717:M,732:{dir:t,HW:[[330,F],[560,ag],[979,ae],[1460,ac],[1886,aa],[2328,Z]]},759:M,770:{dir:t,HW:[[311,F],[560,ag],[979,ae],[1460,ac],[1886,aa],[2328,Z]]},771:{dir:t,HW:[[330,F],[560,ag],[979,ae],[1460,ac],[1886,aa],[2328,Z]]},773:{dir:t,HW:[[500,ab],[1000,ag],[1500,ae],[2000,ac],[2500,aa],[3000,Z]],stretch:{left:[57363,Z],rep:[57363,Z]}},780:{dir:t,HW:[[311,F],[560,ag],[979,ae],[1460,ac],[1886,aa],[2328,Z]]},816:{dir:t,HW:[[330,ab],[560,ag],[979,ae],[1460,ac],[1886,aa],[2328,Z]]},818:{dir:t,HW:[[500,ab],[1000,ag],[1500,ae],[2000,ac],[2500,aa],[3000,Z]],stretch:{left:[57364,Z],rep:[57364,Z]}},824:{dir:f,HW:[[818,F],[553,ag],[662,ae],[818,ac],[959,aa],[1414,Z]]},8213:{alias:9135,dir:t},8214:{dir:f,HW:[[879,F]],stretch:{bot:[8214,F],ext:[8214,F]}},8215:{alias:9135,dir:t},8254:{dir:t,HW:[[500,F],[1000,ag],[1500,ae],[2000,ac],[2500,aa],[3000,Z]],stretch:{left:[8254,F],rep:[8254,F]}},8400:M,8401:M,8406:M,8407:{dir:t,HW:[[436,F],[872,ag],[1308,ae],[1744,ac],[2180,aa],[3000,Z]],stretch:{rep:[57366,Z],right:[57369,Z]}},8417:M,8428:M,8429:M,8430:M,8431:M,8512:z,8592:{dir:t,HW:[[786,F]],stretch:{left:[8592,F],rep:ai}},8593:{dir:f,HW:[[818,F]],stretch:{ext:[9168,F],top:[8593,F]}},8594:{dir:t,HW:[[786,F]],stretch:{rep:ai,right:[8594,F]}},8595:{dir:f,HW:[[818,F]],stretch:{bot:[8595,F],ext:[9168,F]}},8596:{dir:t,HW:[[850,F]],stretch:{left:[8592,F],rep:ai,right:[8594,F]}},8597:{dir:f,HW:[[954,F]],stretch:{bot:[8595,F],ext:[9168,F],top:[8593,F]}},8606:M,8607:z,8608:M,8609:z,8612:M,8613:z,8614:M,8615:z,8616:z,8617:M,8618:M,8624:z,8625:z,8626:z,8627:z,8628:M,8629:z,8636:M,8637:M,8638:z,8639:z,8640:M,8641:M,8642:z,8643:z,8651:M,8652:M,8656:{dir:t,HW:[[806,F]],stretch:{left:[8656,F],rep:[57375,Z]}},8657:{dir:f,HW:[[818,F]],stretch:{ext:[57376,Z],top:[8657,F]}},8658:{dir:t,HW:[[806,F]],stretch:{rep:[57375,Z],right:[8658,F]}},8659:{dir:f,HW:[[818,F]],stretch:{bot:[8659,F],ext:[57376,Z]}},8660:{dir:t,HW:[[886,F]],stretch:{left:[8656,F],rep:[57375,Z],right:[8658,F]}},8661:{dir:f,HW:[[954,F]],stretch:{bot:[8659,F],ext:[57376,Z],top:[8657,F]}},8666:M,8667:M,8672:M,8673:z,8674:M,8675:z,8676:M,8677:M,8701:M,8702:M,8703:M,8719:z,8720:z,8721:z,8722:{alias:9135,dir:t},8725:{alias:47,dir:f},8730:{dir:f,HW:[[1232,F],[1847,ag],[2460,ae],[3075,ac]],stretch:{bot:[57378,Z],ext:[57379,Z],top:[57380,Z]}},8731:z,8732:z,8739:{dir:f,HW:[[879,F]],stretch:{ext:[8739,F]}},8741:{dir:f,HW:[[879,F]],stretch:{ext:[8741,F]}},8747:z,8748:z,8749:z,8750:z,8751:z,8752:z,8753:z,8754:z,8755:z,8896:z,8897:z,8898:z,8899:z,8968:{dir:f,HW:[[926,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{ext:[57351,Z],top:[57352,Z]}},8969:{dir:f,HW:[[926,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{ext:[57354,Z],top:[57355,Z]}},8970:{dir:f,HW:[[926,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57350,Z],ext:[57351,Z]}},8971:{dir:f,HW:[[926,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]],stretch:{bot:[57353,Z],ext:[57354,Z]}},8978:{alias:9180,dir:t},8994:{alias:9180,dir:t},8995:{alias:9181,dir:t},9001:{alias:10216,dir:f},9002:{alias:10217,dir:f},9130:z,9135:{dir:t,HW:[[315,v]],stretch:{rep:[9135,v]}},9136:{dir:f,HW:[[1000,Z,null,57402]],stretch:{top:[57359,Z],ext:[57357,Z],bot:[57360,Z]}},9137:{dir:f,HW:[[1000,Z,null,57403]],stretch:{top:[57362,Z],ext:[57357,Z],bot:[57356,Z]}},9140:M,9141:M,9168:z,9180:M,9181:M,9182:{dir:t,HW:[[1000,F],[925,ag],[1460,ae],[1886,ac],[2328,aa],[3238,Z]],stretch:{left:[57393,Z],rep:[57384,Z],mid:[57394,Z],right:[57395,Z]}},9183:{dir:t,HW:[[1000,F],[925,ag],[1460,ae],[1886,ac],[2328,aa],[3238,Z]],stretch:{left:[57396,Z],rep:[57387,Z],mid:[57397,Z],right:[57398,Z]}},9184:M,9185:M,9472:{alias:8722,dir:t},10072:{alias:8739,dir:f},10098:z,10099:z,10214:z,10215:z,10216:{dir:f,HW:[[926,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]]},10217:{dir:f,HW:[[926,F],[1230,ag],[1350,ag,1.098],[1845,ae],[2460,ac],[3075,aa]]},10218:z,10219:z,10222:{dir:f,HW:[[853,F]],stretch:{bot:[57344,Z],ext:[57345,Z],top:[57346,Z]}},10223:{dir:f,HW:[[853,F]],stretch:{bot:[57347,Z],ext:[57348,Z],top:[57349,Z]}},10224:z,10225:z,10229:{alias:8592,dir:t},10230:{alias:8594,dir:t},10231:{alias:8596,dir:t},10232:{alias:8656,dir:t},10233:{alias:8658,dir:t},10234:{alias:8660,dir:t},10235:{alias:8612,dir:t},10236:{alias:8614,dir:t},10237:{alias:10502,dir:t},10238:{alias:10503,dir:t},10502:M,10503:M,10506:z,10507:z,10514:z,10515:z,10574:M,10575:z,10576:M,10577:z,10578:M,10579:M,10580:z,10581:z,10582:M,10583:M,10584:z,10585:z,10586:M,10587:M,10588:z,10589:z,10590:M,10591:M,10592:z,10593:z,10624:z,10627:z,10628:z,10629:z,10630:z,10647:z,10648:z,10744:{dir:f,HW:[[1020,F],[1845,ag]]},10745:{dir:f,HW:[[1020,F],[1845,ag]]},10752:z,10753:z,10754:z,10755:z,10756:z,10757:z,10758:z,10759:z,10760:z,10761:z,10762:z,10763:z,10764:z,10765:z,10766:z,10767:z,10768:z,10769:z,10770:z,10771:z,10772:z,10773:z,10774:z,10775:z,10776:z,10777:z,10778:z,10779:z,10780:z,11004:z,11007:z,11077:M,11078:{dir:t,HW:[[818,m]],stretch:{rep:[57401,Z],right:[11078,m]}},12296:{alias:10216,dir:f},12297:{alias:10217,dir:f},65079:{alias:9182,dir:t},65080:{alias:9183,dir:t}}}});MathJax.Hub.Register.LoadHook(N.fontDir+"/Main/Regular/Main.js",function(){N.FONTDATA.FONTS[F][8942][0]+=400;N.FONTDATA.FONTS[F][8945][0]+=500;N.FONTDATA.FONTS[F][8722][0]=N.FONTDATA.FONTS[F][43][0];N.FONTDATA.FONTS[F][8722][1]=N.FONTDATA.FONTS[F][43][1];N.FONTDATA.FONTS[F][61][1]+=100});MathJax.Hub.Register.LoadHook(N.fontDir+"/Size5/Regular/Main.js",function(){var H;H=N.FONTDATA.DELIMITERS[9182].stretch.rep[0];N.FONTDATA.FONTS[Z][H][0]+=200;N.FONTDATA.FONTS[Z][H][1]+=200;H=N.FONTDATA.DELIMITERS[9183].stretch.rep[0];N.FONTDATA.FONTS[Z][H][0]+=200;N.FONTDATA.FONTS[Z][H][1]+=200});an.loadComplete(N.fontDir+"/fontdata.js")})(MathJax.OutputJax.SVG,MathJax.ElementJax.mml,MathJax.Ajax,MathJax.Hub);
