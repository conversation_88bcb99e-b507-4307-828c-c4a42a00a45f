﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'sr-latn', {
	button: {
		title: 'Osobine dugmeta',
		text: 'Tekst (vrednost)',
		type: 'Tip',
		typeBtn: 'Button',
		typeSbm: 'Submit',
		typeRst: 'Reset'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Osobine polja za potvrdu',
		radioTitle: 'Osobine radio-dugmeta',
		value: 'Vrednost',
		selected: '<PERSON><PERSON><PERSON><PERSON>',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Osobine forme',
		menu: 'Osobine forme',
		action: 'Akcija',
		method: 'Metoda',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'Osobine skrivenog polja',
		name: '<PERSON><PERSON>',
		value: 'Vrednost'
	},
	select: {
		title: 'Osobine izbornog polja',
		selectInfo: 'Info',
		opAvail: 'Dostupne opcije',
		value: 'Vrednost',
		size: '<PERSON><PERSON><PERSON><PERSON>',
		lines: 'linija',
		chkMulti: 'Do<PERSON>voli višestruku selekciju',
		required: 'Required', // MISSING
		opText: 'Tekst',
		opValue: 'Vrednost',
		btnAdd: 'Dodaj',
		btnModify: 'Izmeni',
		btnUp: 'Gore',
		btnDown: 'Dole',
		btnSetValue: 'Podesi kao označenu vrednost',
		btnDelete: 'Obriši'
	},
	textarea: {
		title: 'Osobine zone teksta',
		cols: 'Broj kolona',
		rows: 'Broj redova'
	},
	textfield: {
		title: 'Osobine tekstualnog polja',
		name: 'Naziv',
		value: 'Vrednost',
		charWidth: 'Širina (karaktera)',
		maxChars: 'Maksimalno karaktera',
		required: 'Required', // MISSING
		type: 'Tip',
		typeText: 'Tekst',
		typePass: 'Lozinka',
		typeEmail: 'Email',
		typeSearch: 'Pretraži',
		typeTel: 'Broj telefona',
		typeUrl: 'URL'
	}
} );
