﻿@model CERI05EditAccreditationtViewModel
@using (Html.BeginCollectionItem("Accreditationts", true))
{
    var Index = Html.GetIndex("Accreditationts");
  
    @*<div class="col-xs-2 "></div> <div class="col-xs-2 "></div> <div class="col-xs-4 "></div>*@

<div id="Tr@(Index)">
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.ACCREDITATION_ID)
    @*@Html.HiddenFor(m => m.CLASS_NO)
        @Html.HiddenFor(m => m.SEAT_NO)
        @Html.HiddenFor(m => m.NAME)
        @Html.HiddenFor(m => m.SNAME)
    *@
    @Html.HiddenFor(m => m.O_IS_PASS)
   
    @Html.EditorFor(m => m.PersonText,new { @class= "form-control" })

</div>
}
