﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'mn', {
	border: 'Хүрээний хэмжээ',
	caption: 'Тайлбар',
	cell: {
		menu: 'Нүх/зай',
		insertBefore: 'Нүх/зай өмнө нь оруулах',
		insertAfter: 'Нүх/зай дараа нь оруулах',
		deleteCell: 'Нүх устгах',
		merge: 'Нүх нэгтэх',
		mergeRight: 'Баруун тийш нэгтгэх',
		mergeDown: 'Доош нэгтгэх',
		splitHorizontal: 'Нүх/зайг босоогоор нь тусгаарлах',
		splitVertical: 'Нүх/зайг хөндлөнгөөр нь тусгаарлах',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Хэвтээд тэгшлэх арга',
		vAlign: 'Босоод тэгшлэх арга',
		alignBaseline: 'Baseline',
		bgColor: 'Дэвсгэр өнгө',
		borderColor: 'Хүрээний өнгө',
		data: 'Data',
		header: 'Header',
		yes: 'Тийм',
		no: 'Үгүй',
		invalidWidth: 'Нүдний өргөн нь тоо байх ёстой.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Сонгох'
	},
	cellPad: 'Нүх доторлох(padding)',
	cellSpace: 'Нүх хоорондын зай (spacing)',
	column: {
		menu: 'Багана',
		insertBefore: 'Багана өмнө нь оруулах',
		insertAfter: 'Багана дараа нь оруулах',
		deleteColumn: 'Багана устгах'
	},
	columns: 'Багана',
	deleteTable: 'Хүснэгт устгах',
	headers: 'Headers', // MISSING
	headersBoth: 'Both', // MISSING
	headersColumn: 'First column', // MISSING
	headersNone: 'None',
	headersRow: 'First Row', // MISSING
	invalidBorder: 'Border size must be a number.', // MISSING
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Cell spacing must be a positive number.', // MISSING
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Table height must be a number.', // MISSING
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Хүснэгтийн өргөн нь тоо байх ёстой.',
	menu: 'Хүснэгт',
	row: {
		menu: 'Мөр',
		insertBefore: 'Мөр өмнө нь оруулах',
		insertAfter: 'Мөр дараа нь оруулах',
		deleteRow: 'Мөр устгах'
	},
	rows: 'Мөр',
	summary: 'Тайлбар',
	title: 'Хүснэгт',
	toolbar: 'Хүснэгт',
	widthPc: 'хувь',
	widthPx: 'цэг',
	widthUnit: 'өргөний нэгж'
} );
