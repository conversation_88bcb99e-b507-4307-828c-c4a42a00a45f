﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT17
    {

        ///Summary
        ///QUESTIONS_ID
        ///Summary
        [Display(Name = "問題序號")]
        [StringLength(100)]
        public string QUESTIONS_ID { get; set; }

        ///Summary
        ///ITEM_NO
        ///Summary
        [Display(Name = "序號")]
        public int? ITEM_NO { get; set; }



        ///Summary
        ///類別 1.回覆 ,2.詢問
        ///Summary
        [Display(Name = "類別")]
        public string ANS_TYPE { get; set; }


        ///Summary
        ///SCHOOL_NO
        ///Summary
        [Display(Name = "學校ID")]
        [StringLength(16)]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///SCHOOL_NAME
        ///Summary
        [Display(Name = "學校")]
        [StringLength(16)]
        public string SCHOOL_NAME { get; set; }

        ///Summary
        ///ANSWERS
        ///Summary
        [Display(Name = "內容")]
        [Required]
        public string ANSWERS { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [Display(Name = "帳號")]
        [StringLength(20)]
        public string USER_NO { get; set; }

        ///Summary
        ///NAME
        ///Summary
        [Display(Name = "姓名")]
        [StringLength(40)]
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        [Display(Name = "姓名")]
        [Required]
        [StringLength(40)]
        public string SNAME { get; set; }

        ///Summary
        ///E_MAIL
        ///Summary
        [Display(Name = "E-MAIL")]
        [StringLength(510)]
        public string E_MAIL { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
        [Display(Name = "建立日期")]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///CRE_PERSON
        ///Summary
        [Display(Name = "建立人員")]
        [StringLength(100)]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///CHG_PERSON
        ///Summary
        [Display(Name = "修改人")]
        [StringLength(100)]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [Display(Name = "修改日期")]
        public DateTime? CHG_DATE { get; set; }

        ///Summary
        ///MEMO
        ///Summary
        [Display(Name = "備註")]
        [StringLength(510)]
        public string MEMO { get; set; }

        ///Summary
        ///FILE_NAME
        ///Summary
        [Display(Name = "檔名")]
        [StringLength(510)]
        public string FILE_NAME { get; set; }

        ///Summary
        ///  ANS_TYPE =A(回覆) 
        ///  ANS_TYPE =Q(再發問) 
        ///Summary
        [Display(Name = "狀態")]
        public string STATUS { get; set; }


        /// <summary>
        /// A.回覆 ,Q.發問
        /// </summary>
        public static class ANS_TYPE_Val
        {
            /// <summary>
            /// 回覆
            /// </summary>
            public static string ANS_TYPE_A = "A";

            /// <summary>
            /// 再發問
            /// </summary>
            public static string ANS_TYPE_Q = "Q";

        }

        /// <summary>
        /// 1.未回覆 2.已回覆/未讀取 3.已讀取
        /// </summary>
        public static class STATUS_Val
        {
            /// <summary>
            /// 未回覆
            /// </summary>
            public static string STATUS_1 = "1";

            /// <summary>
            /// 已回覆/未讀取
            /// </summary>
            public static string STATUS_2 = "2";

            /// <summary>
            /// 已讀取
            /// </summary>
            public static string STATUS_3 = "3";

        }

    }

}
