(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Parser: roman - updated 6/28/MMXIV (v2.17.3) */
!function(c){"use strict";var p=/^M*(?:D?C{0,3}|C[MD])(?:L?X{0,3}|X[CL])(?:V?I{0,3}|I[XV])$/i,m=/\b([MCDLXVI]+\b)/gi,l={I:1,V:5,X:10,L:50,C:100,D:500,M:1e3};c.tablesorter.addParser({id:"roman",is:function(){return!1},format:function(r){var t,e=r.toUpperCase().split(""),n=0;if(!r||!p.test(r))return r;for(;e.length;)n+=(t=l[e.shift()])*(t<l[e[0]]?-1:1);return n},type:"numeric"}),c.tablesorter.addParser({id:"roman-ignore",is:function(){return!1},format:function(r,t,e,n){var i,a,o=t.config,s=c.isArray(o.roman_ignore)?o.roman_ignore[n]:0,u=(isNaN(s)?c.trim(r.replace(s,"")):c.trim(r.substring(0,r.length-s))).match(m),f=0;if(!p.test(u))return r;for(u=(a=u[0]).toUpperCase().split("");u.length;)(i=l[u.shift()])&&(f+=i*(i<l[u[0]]?-1:1));return f?r.replace(a,f):r},type:"text"}),c.tablesorter.addParser({id:"roman-extract",is:function(){return!1},format:function(r){var t,e=c.grep(r.split(/\b/),function(r){return p.test(r)?r:""}).join("").match(m),n=0;if(!(e?p.test(e):0))return r;for(e=e[0].toUpperCase().split("");e.length;)(t=l[e.shift()])&&(n+=t*(t<l[e[0]]?-1:1));return n||r},type:"numeric"})}(jQuery);return jQuery;}));
