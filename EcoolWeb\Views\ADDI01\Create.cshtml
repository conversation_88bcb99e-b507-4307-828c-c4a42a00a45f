﻿@model ECOOL_APP.EF.ADDT01
@using EcoolWeb.Util;
@using com.ecool.service

@{
    ViewBag.Title = "線上投稿-我要投稿";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string FileFontSize = "";
    string SubmitFontSize = "";

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        FileFontSize = "font-size:16px;";
        SubmitFontSize = "font-size:20px;";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    //判斷是否有[我要寫信給耶誕老人]權限
    ViewBag.VisibleSendChrisMail = PermissionService.GetPermission_Use_YN("ADDI01", "SendChrisMail", user?.SCHOOL_NO ?? "", user?.USER_NO ?? "");

}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@using (Html.BeginForm("Create", "ADDI01", FormMethod.Post, new { name = "ADDI01Form", id = "ADDI01Form", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div style="width:600px">
        @{string Explain = ViewBag.ADDI01SEXPLAIN;}
        @Html.Raw(HttpUtility.HtmlDecode(@Explain))
    </div>

    <img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Pink">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CRE_DATE, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })
                <label class="control-label label_dd col-md-6 col-sm-6 col-lg-6"> @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime")</label>
                @Html.HiddenFor(model => model.CRE_DATE)
            </div>
            @if (ViewBag.VisibleSendChrisMail == SharedGlobal.Y)
            {

                var checkBoxsCHRIS = new List<CheckBoxListInfo>();
                CheckBoxListInfo cheboxCHRISItem = new CheckBoxListInfo();
                cheboxCHRISItem.DisplayText = " ※<font color=\"#FF0000\">不會</font>真的寫電子信給聖誕老公公，只會將本文標誌和聖誕老公公有關，方便學校辦理活動匯出。(這是學校聖誕活動專用選項)";
                cheboxCHRISItem.Value = "Y";
                cheboxCHRISItem.IsChecked = Model.PUBLISH_CHRIS_YN == "Y" ? true : false;
                checkBoxsCHRIS.Add(cheboxCHRISItem);
                var htmlAttributeCHRIS = new Dictionary<string, object>();
                htmlAttributeCHRIS.Add("id", "PUBLISH_CHRIS_YN");

                <div class="form-group">
                    @Html.LabelFor(model => model.PUBLISH_CHRIS_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })

                    <div class="col-md-9 col-sm-6" style="padding-top:7px">
                        @Html.CheckBoxList("PUBLISH_CHRIS_YN", (List<CheckBoxListInfo>)checkBoxsCHRIS, htmlAttributeCHRIS, 1)
                    </div>
                </div>

            }
            else
            {
                @Html.HiddenFor(m => m.PUBLISH_CHRIS_YN)
            }
            <div class="form-group">
                @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })
                <div class="col-md-12 col-sm-12 col-lg-10">
                    @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control", maxlength = "20", onblur = "CheckSubject()", placeholder = "這是必須寫的欄位" } })
                    @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
                </div>
            </div>

        
            <div class="form-group">
                @Html.Label("是否出現作者姓名", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6" style="padding-top:7px">
                    @Html.RadioButtonFor(model => model.AutherYN, false, new { @id = "AutherYN" })
                    @Html.Label("出現作者姓名")
                    @Html.RadioButtonFor(model => model.AutherYN, true, new { @id = "AutherYN" })
                    @Html.Label("不出現作者姓名")
                </div>
            </div>

                    <div class="form-group">
                        <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12">
                            詳細內容:限制1500字，目前字數為<span id="ShowFontLen" style="color:red"></span>字
                        </label>
                        @Html.LabelFor(model => model.ARTICLE, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })

                        <div class="col-md-12 col-sm-12 col-lg-10">
                            @Html.TextAreaFor(model => model.ARTICLE, new { cols = "200", rows = "10", @class = "form-control", @placeholder = "這是必須寫的欄位" })
                            @Html.ValidationMessageFor(model => model.ARTICLE, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <label class="text-info" style="padding-left:3px;">PS.圖檔請少於六張</label>
                    <div class="form-group">
                        <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">上傳圖檔</span>
                        <div class="col-md-10">

                            <div class="col-md-12 col-sm-12 col-lg-10">
                                @Html.Action("MultiFileUpload", "Comm")
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.YOUTUBE_URL, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                        <div class="col-md-10">

                            <div class="input-group">
                                @Html.EditorFor(model => model.YOUTUBE_URL, new { htmlAttributes = new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.YOUTUBE_URL) } })
                                <span class="input-group-btn">
                                    <button class="btn btn-info" type="button" id="CheckYoutube">檢查網址</button>
                                </span>
                            </div><!-- /input-group -->

                            @Html.ValidationMessageFor(model => model.YOUTUBE_URL, "", new { @class = "text-danger" })
                        </div>
                        <br />
                        <label class="text-info" style="padding-left:20px;">PS.請輸入 有效Youtube 網址 ，https://www.youtube.com/watch?v=影片id，影片權限請公開 。</label>
                    </div>
                    <div class="form-group">
                        <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">上傳PPT或pdf(大小限制6MB)</span>
                        <div class="col-md-10" style="padding-left:30px;">
                            @Html.Action("MultiFiles2Upload", "Comm", new { htmlAttributes = new { @style = "padding-left:15px;" } })
                        </div>
                    </div>
                    @if (AppMode == false)
                    {
                        @Html.Action("Index", "Audio")
                    }

                    <div class=" col-md-12col-sm-12" style="margin-left:auto;margin-right:auto;text-align:center;">

                        <button type="button" class="btn btn-default " style='@SubmitFontSize' onclick="TempSave()">
                            暫存草稿
                        </button>
                        <button type="submit" class="btn btn-default btn-success" style='@SubmitFontSize'>
                            確定送出
                        </button>
                        <a href='@Url.Action("Index", "ADDI01")' role="button" class="btn btn-default  " style='@SubmitFontSize'>
                            放棄編輯
                        </a>
                    </div>
                </div>
            </div>
    <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">提醒您，需注意下面內容</h4>
                </div>
                <div class="modal-body" id="remind-content">

                    @if (ViewBag.RemindItems != null)
                    {

                        <div class="row">
                            <div class="col-xs-2 text-right"> <img src="~/Content/img/Prohibition_plagiarism@0,25x.png" style="width:100px;height:auto" /></div>
                            <div class="col-xs-8">
                                <ol style="font-size:25px">

                                    @foreach (var item in ViewBag.RemindItems as List<BDMT02_REF>)
                                    {
                                        @item.CONTENT_TXT<br />

                                    }
                                </ol>
                            </div>
                            @if (!AppMode)
                            {
                                <div class="col-xs-2  text-left"> <img src="~/Content/img/Copyright@0,25x.png" style="width:100px;height:auto" /></div>
                            }
                            else
                            {
                                <div class="col-xs-2  text-left" style="margin-left: 200px;"> <img src="~/Content/img/Copyright@0,25x.png" style="width:70px;height:auto" /></div>

                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI01_URLS = {
            getUrlArgument: "@(Url.Action("GetUrlArgument", "ADDI12"))",
            getCheckSubject: "@(Url.Action("GetCheckSubject", "ADDI01"))",
            audioIndex: "@(Url.Action("Index", "Audio"))"
        };
    </script>
    <script src="~/Scripts/ADDI01/create.js" nonce="cmlvaw"></script>
}