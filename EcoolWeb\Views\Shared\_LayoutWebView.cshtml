﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    @Styles.Render("~/Content/css")

    <link href="~/Content/css/AppEzCss.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    @RenderSection("css", required: false)
    <style type="text/css">
        .App_hide {
            display: none;
        }

        .App_show {
            display: block;
        }

        .AppMode_hide {
            display: none;
        }

        .AppGird {
            font-size: 25px;
            color: red;
        }
    </style>
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @{
        EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData("~/Views/Shared/_LayoutWebView.cshtml");
    }
    @*@RenderSection("css", required: false)*@

    @{ Html.RenderPartial("_GoogleAnalytics"); }
</head>
<body>
    <div style="width:auto;height:3px"></div>
    <div class="containerEZ">
        <div style="width:95%;margin: 0px auto;">
            @RenderBody()
        </div>

        @*@if (System.Web.Configuration.WebConfigurationManager.AppSettings["TestMode"] != "true")
            {
                <div style="width:auto;height:25px"></div>
                <div style="width:95%;margin: 0px auto;">
                        <FOOTER>
                            @Html.Action("_Footer", "Home")
                        </FOOTER>
                </div>
            }*@
    </div>
</body>
</html>
@RenderSection("scripts", required: false)
<script type="text/javascript">
    window.history.forward(1);
</script>