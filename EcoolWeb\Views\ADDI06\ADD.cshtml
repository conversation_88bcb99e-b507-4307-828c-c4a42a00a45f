﻿@model ECOOL_APP.EF.ADDT14

@{
    ViewBag.Title = "校內表現-新增校內表現";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm("ADD", "ADDI06", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-10.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI06">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassNoItem,new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">

                @Html.LabelFor(model => model.USERNAME, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <select id="USER_NO" name="USER_NO"  class = "form-control">
                    </select>
                    @Html.Hidden("USERNAME")
                    @Html.Hidden("SNAME")
                    @Html.ValidationMessageFor(model => model.USERNAME, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IAWARD_KIND, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownList("IAWARD_KIND", (IEnumerable<SelectListItem>)ViewBag.IAWARD_KIND, new { @class = "form-control" })
                    @Html.Label(" ", new { @id = "lbIAWARD_KIND", @style = "color:blue;" })
                    @Html.ValidationMessageFor(model => model.IAWARD_KIND, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IAWARD_ITEM, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">

                    @Html.EditorFor(model => model.IAWARD_ITEM, new { htmlAttributes = new { @class = "form-control", @MaxLength = "30" } })
                    @Html.ValidationMessageFor(model => model.IAWARD_ITEM, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label label_dt col-md-2 col-sm-3" })

                <div class="col-md-10  col-sm-9" style="padding-top:7px">
                    @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.REMARK, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.REMARK, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.REMARK, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <input class="btn btn-default" type="file" name="files"  />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group text-center">
                <div class="col-md-12">
                    <button id="Add" name="Add" value="新增" class="btn btn-default">
                        確定送出
                    </button>

                    <a href='@Url.Action("QUERY", "ADDI06")' class="btn btn-default">
                        放棄編輯
                    </a>
                </div>
            </div>
        </div>
    </div>


  
}
@section Scripts
{
    <script type="text/javascript">
        $(function () {
            ChangeClass_No();
            $("#Class_No").on('change', function () { 
                ChangeClass_No(); 
            });
            
            var Namelen = $("#USER_NO option:selected").text().length - 2;
            $("#USER_NO").on('change', function () {
                $("#USERNAME").val($("#USER_NO option:selected").text());
                $("#SNAME").val($("#USER_NO option:selected").text().substr(Namelen, 2));
            });
            
            $("#IAWARD_KIND").on('change', function () { 
                IAWARD_KIND_onchange(); 
            });
            
            $("#Add").on('click', function () { 
                return Valid(); 
            });
        });

        function IAWARD_KIND_onchange() {
            var IAWARD_KIND;
            switch ($("#IAWARD_KIND option:selected").text()) {
                case "請選擇事蹟":
                    IAWARD_KIND = '';
                    $("#CASH").val('0');
                    break;
                case "志工":
                    IAWARD_KIND = '志工類每學期1次每次50-200點';
                    $("#CASH").val('50');
                    break;
                case "校內競賽":
                    IAWARD_KIND = '校內競賽每次10-200點';
                    $("#CASH").val('10');
                    break;
                case "品德表現":
                    IAWARD_KIND = '品德表現類10-200點';
                    $("#CASH").val('10');
                    break;
                case "學習表現":
                    IAWARD_KIND = '學習表現類10-200點';
                    $("#CASH").val('10');
                    break;
                case "領導人":
                    IAWARD_KIND = '';
                    break;
                case "七個習慣代言人":
                    IAWARD_KIND = '';
                    break;
            }

            $("#lbIAWARD_KIND").html(IAWARD_KIND);
        }

        function SetClass_NoDDLEmpty() {
            $('#USER_NO').empty();
            $('#USER_NO').append($('<option></option>').val('').text('請選擇學生'));
        }

        function ChangeClass_No() {
            var selectedClass_No = $.trim($('#Class_No option:selected').val());
            if (selectedClass_No.length === 0) {
                SetClass_NoDDLEmpty();
            } else {
                $.getJSON('@Url.Action("GetNameData").ToString()', { Class_No: selectedClass_No })
                    .done(function (data) {
                        $('#USER_NO').empty();
                        $.each(data, function (i, item) {
                            $('#USER_NO').append($('<option></option>').val(item.Value).text(item.Text));
                        });
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        console.error('Error fetching name data:', textStatus, errorThrown);
                    });
            }
        }

        function Valid() {
            var msg = '';
            var blStatus = false;
            
            if ($("#USER_NO").val() === '') {
                msg += '請選擇學生\r\n';
            }
            
            if ($("#CASH").val().match(/^\+{0,1}\d+(\.\d{1,2})?$/) === null) {
                msg += '獎勵點數不能為負\r\n';
            }
            
            if ($("#OAWARD_SCORE").val() === '') {
                msg += '請選擇成績\r\n';
            }

            if ($("#CASH").val().length <= 0) {
                msg += '獎勵點數不能為空\r\n';
            }

            if (isNumber($("#CASH").val()) == false) {
                msg += '獎勵點數不為數字\r\n';
            }

            if ($("#CASH").val() > 999) {
                msg += '獎勵點數不能超過1000點\r\n';
            }

            switch($("#IAWARD_KIND option:selected").text()) {
                case "請選擇事蹟":
                    msg += '請選擇事蹟\r\n';
                    break;
                case "志工":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 50) {
                        msg += '志工類獎勵點數需介於50-200點\r\n';
                    }
                    break;
                case "校內競賽":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 10) {
                        msg += '校內競賽類獎勵點數需介於10-200點\r\n';
                    }
                    break;
                case "品德表現":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 10) {
                        msg += '品德表現類獎勵點數需介於10-200點\r\n';
                    }
                    break;
                case "學習表現":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 10) {
                        msg += '學習表現類獎勵點數需介於10-200點\r\n';
                    }
                    break;
            }

            if (msg !== '') {
                alert(msg);
                blStatus = false;
            } else {
                blStatus = true;
            }

            return blStatus;
        }

        function isNumber(name) {
            if (name.length === 0) {
                return false;
            }
            for (var i = 0; i < name.length; i++) {
                if (name.charAt(i) < "0" || name.charAt(i) > "9") {
                    return false;
                }
            }
            return true;
        }
    </script>
}