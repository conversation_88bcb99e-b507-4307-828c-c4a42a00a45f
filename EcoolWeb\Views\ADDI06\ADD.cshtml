@model ECOOL_APP.EF.ADDT14

@{
    ViewBag.Title = "校內表現-新增校內表現";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm("ADD", "ADDI06", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-10.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI06">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassNoItem,new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">

                @Html.LabelFor(model => model.USERNAME, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <select id="USER_NO" name="USER_NO"  class = "form-control">
                    </select>
                    @Html.Hidden("USERNAME")
                    @Html.Hidden("SNAME")
                    @Html.ValidationMessageFor(model => model.USERNAME, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IAWARD_KIND, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownList("IAWARD_KIND", (IEnumerable<SelectListItem>)ViewBag.IAWARD_KIND, new { @class = "form-control" })
                    @Html.Label(" ", new { @id = "lbIAWARD_KIND", @style = "color:blue;" })
                    @Html.ValidationMessageFor(model => model.IAWARD_KIND, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IAWARD_ITEM, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">

                    @Html.EditorFor(model => model.IAWARD_ITEM, new { htmlAttributes = new { @class = "form-control", @MaxLength = "30" } })
                    @Html.ValidationMessageFor(model => model.IAWARD_ITEM, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label label_dt col-md-2 col-sm-3" })

                <div class="col-md-10  col-sm-9" style="padding-top:7px">
                    @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.REMARK, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.REMARK, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.REMARK, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <input class="btn btn-default" type="file" name="files"  />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group text-center">
                <div class="col-md-12">
                    <button id="Add" name="Add" value="新增" class="btn btn-default">
                        確定送出
                    </button>

                    <a href='@Url.Action("QUERY", "ADDI06")' class="btn btn-default">
                        放棄編輯
                    </a>
                </div>
            </div>
        </div>
    </div>


  
}
@section Scripts
{
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI06_ADD_URLS = {
            getNameData: '@Url.Action("GetNameData")',
            add: '@Url.Action("ADD")'
        };

        // 初始化頁面特有的功能
        $(function () {
            // 頁面載入時初始化班級選單
            ChangeClass_No();

            // 學生選擇變更時更新姓名欄位
            $("#USER_NO").on('change', function () {
                const selectedText = $("#USER_NO option:selected").text();
                $("#USERNAME").val(selectedText);

                // 提取姓名（假設格式為 "班級座號 姓名"）
                const nameMatch = selectedText.match(/\s+(.+)$/);
                if (nameMatch) {
                    $("#SNAME").val(nameMatch[1]);
                }
            });
        });
    </script>
    <script src="~/Scripts/ADDI06/common.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/ADDI06/ADD.js" nonce="cmlvaw"></script>
}