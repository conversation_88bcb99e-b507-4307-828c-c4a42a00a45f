﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.LogicCenter.ADDI09;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using log4net;
using MvcPaging;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI09Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ADDI09";

        public static string ImgPath = "ADDT20IMG";

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg;

        /// <summary>
        /// 資料庫相關處理
        /// </summary>
        private ADDI09Service Db = new ADDI09Service();

        private ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_KEY = string.Empty;
        private string USER_NO = string.Empty;
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        protected string STR_SYS_TABLE_TYPE = string.Empty;

        protected string _ErrorRowCellExcel = string.Empty;

        /// <summary>
        /// 是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckErr = false;

        /// <summary>
        /// 格式是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckDataTypeErr = false;

        /// <summary>
        /// 必輸未輸 true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckMustErr = false;

        private string[] ArraySheetNames;

        /// <summary>
        /// 必輸欄位陣列
        /// </summary>
        private string[] MustArray; //必輸欄位

        /// <summary>
        /// 必輸欄位陣列
        /// </summary>
        private string[] MustArrayTwo; //必輸欄位

        /// <summary>
        /// Excel標題
        /// </summary>
        private string[] TitleArray; //標準標題

        private string OkMsg = "批次給點/扣點成功，以下為這次給點/扣點清單";

        public static class DataTypeVal
        {
            public static string DataTypeM = "Modify";
            /// <summary>
            /// 增加
            /// </summary>
            public static string DataTypeA = "ADD";

            /// <summary>
            /// 刪除
            /// </summary>
            public static string DataTypeD = "DEL";
        }

        [CheckPermission(CheckACTION_ID = "SysIndex")] //檢查權限
        public ActionResult SysIndex()
        {
            //先清除暫存資料與Session (Eric.2019.11.23)
            ADDI09Service Db = new ADDI09Service();
            Db.DelADDT20_TEMP(Session.SessionID);

            Session["ADDI09_Game_IsFix"] = null;
            Session["ADDI09_Game_IsRandom"] = null;
            Session["ADDI09_Game_IsRandomHighPoint"] = null;
            Session["ADDI09_Game_SUBJECT"] = null;
            Session["ADDI09_Game_CASH"] = null;
            Session["ADDI09_EditOne_SUBJECT"] = null;
            //end (Eric.2019.11.23)

            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "請選擇「功能類型」";
            this.Shared();
            TempData["StatusMessage"] = null;

            ViewBag.IsAdmin = user.USER_TYPE == UserType.Admin; // HRMT24_ENUM.CheckQAdmin(user);
            if (string.IsNullOrWhiteSpace(user.CLASS_NO))
            {
                ViewBag.IsAdmin = true;
            }
            var BtnItem = ADDI09EditViewModel.SYSTableTypeItem();
            TempData["BtnItem"] = BtnItem;

            // 自訂權限
            var PermissionBtn = PermissionService.GetActionPermissionForBreNO("ADDI09", SCHOOL_NO, USER_NO);
            //判斷是否有[快速大量加點]權限
            ViewBag.SuperGivePoint = (PermissionBtn.Where(a => a.ActionName == "SuperGivePoint").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");
            if (ISTASKLIST != null)
            {
                return RedirectToAction("EditQuickBulk", new { SYS_TABLE_TYPE = "QUICKBULK", Individual_Give = true, DeleteTemp = true });
            }
            else
            {
                return View();
            }
        }

        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        public ActionResult EditOneIndex()
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "請選擇「功能類型」";
            this.Shared();
            TempData["StatusMessage"] = null;

            Session["ADDI09_EditOne_SUBJECT"] = null;
            Session["ADDI09_EditOne_CASH"] = null;
            Session["ADDI09_EditOne_IsFix"] = null;
            Session["ADDI09_EditOne_IsRandom"] = null;
            Session["ADDI09_EditOne_IsRandomHighPoint"] = null;

            Session["ADDI09_EditOne14_SUBJECT"] = null;
            Session["ADDI09_EditOne14_CONTENT_TXT"] = null;
            Session["ADDI09_EditOne14_CASH"] = null;

            Session["ADDI09_EditOne15_SUBJECT"] = null;
            Session["ADDI09_EditOne15_CONTENT_TXT"] = null;
            Session["ADDI09_EditOne15_CASH"] = null;

            var BtnItem = ADDI09EditViewModel.SYSTableTypeItem();
            TempData["BtnItem"] = BtnItem;

            return View();
        }

        [CheckPermission] //檢查權限
        public ActionResult Index(string SYS_TABLE_TYPE, string ADDT14_STYLE = "")
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 請選擇輸入模式";

            this.Shared();

            TempData["StatusMessage"] = TempData["StatusMessage"] ?? null;
            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ExcelView(string SYS_TABLE_TYPE, string ADDT14_STYLE)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 請匯入Excel";

            this.Shared();

            return View();
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ExcelView(HttpPostedFileBase files, string SYS_TABLE_TYPE, string ADDT14_STYLE)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 請匯入Excel";

            this.Shared();

            if (files == null || files.FileName == string.Empty) ModelState.AddModelError("QuFIle", "請上傳檔案");
            else if (files != null)
            {
                Regex regexCode = new Regex(@".*\.(xls|xlsx)");

                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("QuFIle", "請上傳Excel格式為xls、xlsx");
                }
            }

            if (ModelState.IsValid == false)
            {
                ErrorMsg = "錯誤\r\n";
                return View();
            }
            else
            {
                string ThisBATCH_CASH_ID = string.Empty;
                var OK = this.ExcelData(files, SYS_TABLE_TYPE, ADDT14_STYLE, ref ThisBATCH_CASH_ID);
                if (OK)
                {
                    return RedirectToAction("ListView", new { ThisSaveBATCH_CASH_ID = ThisBATCH_CASH_ID, SYS_TABLE_TYPE = SYS_TABLE_TYPE, ADDT14_STYLE = ADDT14_STYLE });
                }
                else
                {
                    return View();
                }
            }
        }

        #region 步驟2-查詢/點選「學號」

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult QuerySelectView(string SYS_TABLE_TYPE, string DataType = "", string ADDT14_STYLE = "")
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 請查詢/點選「學號」";

            this.Shared();

            if (DataType == "")
            {
                logger.Info("Session.SessionID" + Session.SessionID);
                Db.DelADDT20_TEMP(Session.SessionID);
            }

            if ((SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                && DataType != "reelect")
            {
                // 班級資料
                ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 請點選班級";
                string SchoolNO = UserProfileHelper.GetSchoolNo();

                ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, ref EntitiesDb)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value });

                return View("QuerySelectViewForClassSelect");
            }

            return View();
        }
        public ActionResult QuerySelectView1(string SYS_TABLE_TYPE, string DataType = "", string ADDT14_STYLE = "")
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            ViewBag.PersonType=HRMT01.GetSYS_TABLE_Person_TYPEistData(SchoolNO, "ADDI14", "SYS_TABLE_Person_TYPE", ref EntitiesDb).Select(x => new SelectListItem { Text = x.Text, Value = x.Value }); ;
            ViewBag.TABLType=HRMT01.GetSYS_TABLE_Person_TYPEistData(SchoolNO, "ADDI14", "SYS_TABLE_TYPE", ref EntitiesDb).Select(x => new SelectListItem { Text = x.Text, Value = x.Value }); 
            return View();
        }
            public ActionResult QuerySelectViewForClassSelect(string SYS_TABLE_TYPE, string DataType = "", string ADDT14_STYLE = "")
        {
            return RedirectToAction("QuerySelectView", new { SYS_TABLE_TYPE = SYS_TABLE_TYPE, DataType = DataType, ADDT14_STYLE = ADDT14_STYLE });
        }

        //批次班級Edit前將班級的學生塞入TempTable
        public ActionResult ClassImportStudentTemp(string[] classSelect, string SYS_TABLE_TYPE, string ADDT14_STYLE = "")
        {
            int? NUM = 1;
            ADDT20_TEMP NOW = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID)
                .OrderByDescending(a => a.NUM).FirstOrDefault();
            if (NOW != null)
            {
                NUM = NOW.NUM + 1;
            }

            user = UserProfileHelper.Get();

            if (user == null)
                throw new Exception("查詢不到USER資料，請重新登入");

            SCHOOL_NO = user.SCHOOL_NO;
            USER_KEY = user.USER_KEY;
            USER_NO = user.USER_NO;

            string sessionId = Session.SessionID;
            var now = DateTime.Now;

            if (classSelect != null)
            {
                foreach (var @class in classSelect)
                {
                    var classStudents = EntitiesDb.HRMT01
                        .Where(h => h.SCHOOL_NO == SCHOOL_NO && h.CLASS_NO == @class && h.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(h.USER_STATUS)));

                    foreach (var s in classStudents)
                    {
                        var temp = new ADDT20_TEMP()
                        {
                            USER_NO = s.USER_NO,
                            SESSION_ID = sessionId,
                            SCHOOL_NO = SCHOOL_NO,
                            CRE_PERSON = USER_KEY,
                            CRE_DATE = now,
                            NUM = NUM++
                        };
                        EntitiesDb.ADDT20_TEMP.Add(temp);
                    }
                }
                EntitiesDb.SaveChanges();
            }
            return RedirectToAction("QuerySelectView", new { SYS_TABLE_TYPE = SYS_TABLE_TYPE, DataType = "reelect", ADDT14_STYLE = ADDT14_STYLE });
        }
        /// <summary>
        /// 步驟2-查詢/點選「學號」PartialView
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public ActionResult _QuerySelectPageContent1(QuerySelectViewModel Data, int pageSize = 10, string SYS_TABLE_TYPE = "")
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();
           string  SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user= UserProfileHelper.Get();
            if (Data == null) Data = new QuerySelectViewModel();
            if (Data.Search == null) Data.Search = new ADDI09SearchQuerySelectViewModel();

            List<SelectListItem> PageSizeItems = new List<SelectListItem>();

            PageSizeItems.Add(new SelectListItem() { Text = "顯示5筆", Value = "5", Selected = pageSize == 5 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示10筆", Value = "10", Selected = pageSize == 10 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示20筆", Value = "20", Selected = pageSize == 20 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示50筆", Value = "200", Selected = pageSize == 50 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示100筆", Value = "200", Selected = pageSize == 100 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示全部筆數", Value = int.MaxValue.ToString(), Selected = pageSize == int.MaxValue });
            ViewBag.PageSizeItems = PageSizeItems;
            HRMT01 hRMT01 = new HRMT01();
            IQueryable<HRMT01> HRMT01List;
            if (user.USER_TYPE == UserType.Teacher)
            {

                hRMT01 = EntitiesDb.HRMT01.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(hRMT01.CLASS_NO))
                {

                    if (Data.Search == null)
                    {

                        Data.Search = new ADDI09SearchQuerySelectViewModel();
                    }
                    Data.Search.CLASS_NO = hRMT01.CLASS_NO;
                }

            }
            if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG) // 查詢老師List
            {
                HRMT01List = Db.GetTeacherList(Data, SCHOOL_NO, EntitiesDb);
            }
            else
            {
                HRMT01List = Db.GetStudentList(Data, SCHOOL_NO, EntitiesDb);
            }

            Data.HRMT01List = HRMT01List.ToPagedList(Data.Search.Page > 0 ? Data.Search.Page - 1 : 0, pageSize);
        
           
            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, null, Data.Search.CLASS_NO, ref EntitiesDb);
            if (user.USER_TYPE == UserType.Teacher && hRMT01 != null && !string.IsNullOrWhiteSpace(hRMT01.CLASS_NO)) {

                List<SelectListItem> TempClassItems = new List<SelectListItem>();
                TempClassItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace("") });
                TempClassItems.Add(new SelectListItem() { Text = hRMT01.CLASS_NO, Value = hRMT01.CLASS_NO, Selected = string.IsNullOrWhiteSpace("") });
                ViewBag.ClassItems = TempClassItems;
            }
            ViewBag.PersonType = HRMT01.GetSYS_TABLE_Person_TYPEistData(SchoolNO, "ADDI14", "SYS_TABLE_Person_TYPE", ref EntitiesDb).Select(x => new SelectListItem { Text = x.Text, Value = x.Value }); ;
            ViewBag.TABLType = HRMT01.GetSYS_TABLE_Person_TYPEistData(SchoolNO, "ADDI14", "SYS_TABLE_TYPE", ref EntitiesDb).Select(x => new SelectListItem { Text = x.Text, Value = x.Value });

            return PartialView(Data);
        }

        /// <summary>
        /// 步驟2-查詢/點選「學號」PartialView
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public ActionResult _QuerySelectPageContent(QuerySelectViewModel Data, int pageSize = 10, string SYS_TABLE_TYPE = "")
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();

            if (Data == null) Data = new QuerySelectViewModel();
            if (Data.Search == null) Data.Search = new ADDI09SearchQuerySelectViewModel();

            List<SelectListItem> PageSizeItems = new List<SelectListItem>();

            PageSizeItems.Add(new SelectListItem() { Text = "顯示5筆", Value = "5", Selected = pageSize == 5 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示10筆", Value = "10", Selected = pageSize == 10 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示20筆", Value = "20", Selected = pageSize == 20 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示50筆", Value = "200", Selected = pageSize == 50 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示100筆", Value = "200", Selected = pageSize == 100 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示全部筆數", Value = int.MaxValue.ToString(), Selected = pageSize == int.MaxValue });
            ViewBag.PageSizeItems = PageSizeItems;

            IQueryable<HRMT01> HRMT01List;
            if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG) // 查詢老師List
            {
                HRMT01List = Db.GetTeacherList(Data, SCHOOL_NO, EntitiesDb);
            }
            else
            {
                HRMT01List = Db.GetStudentList(Data, SCHOOL_NO, EntitiesDb);
            }

            Data.HRMT01List = HRMT01List.ToPagedList(Data.Search.Page > 0 ? Data.Search.Page - 1 : 0, pageSize);

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, null, Data.Search.CLASS_NO, ref EntitiesDb);

            return PartialView(Data);
        }

        /// <summary>
        /// 步驟2-查詢/點選「學號」PartialView
        /// </summary>
        public ActionResult _QuerySelectDataList(string SYS_TABLE_TYPE, string ShowType, string DivHeight, string USER_NO = "", string DataType = "", string ADDT14_STYLE = "")
        {
            string Mesg = "";
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            this.Shared();

            if (string.IsNullOrWhiteSpace(DataType) == false)
            {
                if (DataType == DataTypeVal.DataTypeA)
                {
                    int? NUM = 1;
                    ADDT20_TEMP NOW = EntitiesDb.ADDT20_TEMP
                        
                        .Where(x => x.SESSION_ID == Session.SessionID).OrderByDescending(a => a.NUM).FirstOrDefault();
                    //var ADDT20T = (from x in EntitiesDb.ADDT20_TEMP
                    //               join y in EntitiesDb.HRMT01 on new { x.SCHOOL_NO, x.USER_NO } equals new { y.SCHOOL_NO, y.USER_NO}
                    //               select new
                    //               {x.USER_NO,x.SESSION_ID ,x.SCHOOL_NO ,x.SCHOOL_NO,x.us}
                                 //);
                    if (NOW != null)
                    {
                        NUM = NOW.NUM + 1;
                    }

                    ADDT20_TEMP TEMP = new ADDT20_TEMP();
                    TEMP.USER_NO = USER_NO;
                    TEMP.SESSION_ID = Session.SessionID;
                    TEMP.SCHOOL_NO = SCHOOL_NO;
                    TEMP.CRE_PERSON = USER_KEY;
                    TEMP.CRE_DATE = DateTime.Now;
                    TEMP.NUM = NUM;
                    TEMP.TempCash = 0;
                    EntitiesDb.ADDT20_TEMP.Add(TEMP);
                }
                else if (DataType == DataTypeVal.DataTypeD)
                {
                    ADDT20_TEMP TEMP = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID && x.SCHOOL_NO == SCHOOL_NO && x.USER_NO == USER_NO).FirstOrDefault();
                    if (TEMP != null)
                    {
                        EntitiesDb.ADDT20_TEMP.Remove(TEMP);
                    }
                }
                try
                {
                    EntitiesDb.SaveChanges();
                }
                catch (Exception e)
                {
                    Mesg = e.InnerException.Message;
                }
               
            }

            ADDI09EditViewModel Data = new ADDI09EditViewModel();
            Data.DataList = new List<ADDI09SelectListViewModel>();

            this.GetTempData(Data, DivHeight, ShowType);

            return PartialView(Data);
        }
        public ActionResult _QuerySelectDataList1(string SYS_TABLE_TYPE, string ShowType, string DivHeight, string USER_NO = "", string DataType = "", string ADDT14_STYLE = "",string cash="", string PersonType="" ,string TABLType="")
        {
            string Mesg = "";
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            this.Shared();

            if (string.IsNullOrWhiteSpace(DataType) == false)
            {
                if (DataType == DataTypeVal.DataTypeA)
                {
                    int? NUM = 1;
                    ADDT20_TEMP NOW = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID).OrderByDescending(a => a.NUM).FirstOrDefault();
                    if (NOW != null)
                    {
                        NUM = NOW.NUM + 1;
                    }

                    ADDT20_TEMP TEMP = new ADDT20_TEMP();
                    TEMP.USER_NO = USER_NO;
                    TEMP.SESSION_ID = Session.SessionID;
                    TEMP.SCHOOL_NO = SCHOOL_NO;
                    TEMP.CRE_PERSON = USER_KEY;
                    TEMP.CRE_DATE = DateTime.Now;
                    TEMP.NUM = NUM;
                    TEMP.TempCash = 0;
                    TEMP.Cash = Int32.Parse(cash == "" ? "0" : cash);
                    TEMP.PersonType = PersonType;
                    TEMP.TABLType = TABLType;
                    EntitiesDb.ADDT20_TEMP.Add(TEMP);
                }
                else if (DataType == DataTypeVal.DataTypeM) {
                    ADDT20_TEMP TEMP = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID && x.SCHOOL_NO == SCHOOL_NO && x.USER_NO == USER_NO).FirstOrDefault();
            
                 
                    TEMP.TempCash = 0;
                    TEMP.Cash = Int32.Parse(cash == "" ? "0" : cash);
                    TEMP.PersonType = PersonType;
                    TEMP.TABLType = TABLType;
                    
                }
                else if (DataType == DataTypeVal.DataTypeD)
                {
                    ADDT20_TEMP TEMP = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID && x.SCHOOL_NO == SCHOOL_NO && x.USER_NO == USER_NO).FirstOrDefault();
                    if (TEMP != null)
                    {
                        EntitiesDb.ADDT20_TEMP.Remove(TEMP);
                    }
                }
                try
                {
                    EntitiesDb.SaveChanges();
                }
                catch (Exception e)
                {
                    Mesg = e.InnerException.Message;
                }

            }

            ADDI09EditViewModel Data = new ADDI09EditViewModel();
            Data.DataList = new List<ADDI09SelectListViewModel>();

            this.GetTempData(Data, DivHeight, ShowType);
            ViewBag.PersonType = HRMT01.GetSYS_TABLE_Person_TYPEistData(SCHOOL_NO, "ADDI14", "SYS_TABLE_Person_TYPE", ref EntitiesDb).Select(x => new SelectListItem { Text = x.Text, Value = x.Value }); ;
            ViewBag.TABLType = HRMT01.GetSYS_TABLE_Person_TYPEistData(SCHOOL_NO, "ADDI14", "SYS_TABLE_TYPE", ref EntitiesDb).Select(x => new SelectListItem { Text = x.Text, Value = x.Value });
            return PartialView(Data);
        }
        #endregion 步驟2-查詢/點選「學號」

        #region 步驟3-更改點數

        [HttpPost]
        public void ChangeTempCash(List<ADDI09ChangeTempCashViewModel> vm)
        {
            if (vm != null)
            {
                IEnumerable<ADDT20_TEMP> now = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID);
                // 更換點數
                foreach (var temp in now)
                {
                    foreach (var chg in vm)
                    {
                        if (chg.User_No == temp.USER_NO)
                        {
                            temp.TempCash = chg.Cash;
                            break;
                        }
                    }
                }

                EntitiesDb.SaveChanges();
            }
        }

        #endregion 步驟3-更改點數

        private void GetTempData(ADDI09EditViewModel Data, string DivHeight, string ShowType)
        {
            Data.DataList = Db.GetListData(Session.SessionID);
            Data.SelectDataCount = Data.DataList.Count();
            Data.DivHeight = DivHeight;
            Data.ShowType = ShowType;
        }

        [HttpGet]
        public ActionResult GameTicket0()
        {
            ViewBag.TicketUrl = "/addi09/gameticket";
            return View();
        }

        [HttpGet]
        public ActionResult GameTicket()
        {
            ViewBag.Ticket = "102063";
            return View();
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        public ActionResult Game()
        {
            ViewBag.BRE_NO = Bre_NO;
            if (string.IsNullOrWhiteSpace(Request["SYS_TABLE_TYPE"]))
                ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");
            else
                ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");
            ViewBag.Panel_Title = "教育週行動支付加扣點"; //ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE) + " - 即時加點";

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO(Bre_NO, user.SCHOOL_NO, user.USER_NO);
            ViewBag.IndexActionName = (PermissionBtn.Where(a => a.ActionName == "SysIndex").Select(a => a.BoolUse).FirstOrDefault() == true ? "SysIndex" : "Index");

            //原因選單
            ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "GAME_SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 20, 10, 0, -10, -20, -30 };

            ADDI09EditOoneViewModel Data = new ADDI09EditOoneViewModel();

            if (Request["IsFix"] == "True") Data.IsFix = true;
            if (Request["IsRandom"] == "True") Data.IsRandom = true;
            if (Request["IsRandomHighPoint"] == "True") Data.IsRandomHighPoint = true;
            if (Data.IsFix == false)
            {
                if (Session["ADDI09_Game_IsFix"] != null) Data.IsFix = Convert.ToBoolean(Session["ADDI09_Game_IsFix"]);
            }
            if (Data.IsRandom == false)
            {
                if (Session["ADDI09_Game_IsRandom"] != null) Data.IsRandom = Convert.ToBoolean(Session["ADDI09_Game_IsRandom"]);
                if (Session["ADDI09_Game_IsRandomHighPoint"] != null) Data.IsRandomHighPoint = Convert.ToBoolean(Session["ADDI09_Game_IsRandomHighPoint"]);
            }

            if (Session["ADDI09_Game_SUBJECT"] != null || Session["ADDI09_Game_CASH"] != null)
            {
                if (Session["ADDI09_Game_SUBJECT"] != null) Data.SUBJECT = Session["ADDI09_Game_SUBJECT"].ToString();
                if (Session["ADDI09_Game_CASH"] != null) Data.CASH = int.Parse(Session["ADDI09_Game_CASH"].ToString());
                return View(Data);
            }
            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult Game(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "教育週行動支付加扣點結果";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            string ThisBATCH_CASH_ID = string.Empty;

            bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
            if (OK)
            {
                Session["ADDI09_Game_SUBJECT"] = Data.SUBJECT;
                Session["ADDI09_Game_CASH"] = Data.CASH;
                Session["ADDI09_Game_IsFix"] = Data.IsFix;
                Session["ADDI09_Game_IsRandom"] = Data.IsRandom;
                Session["ADDI09_Game_IsRandomHighPoint"] = Data.IsRandomHighPoint;

                return View("GameResult", Data);
            }

            TempData["StatusMessage"] = ErrorMsg;

            //原因選單
            ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "GAME_SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 20, 10, 0, -10, -20, -30 };

            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult Game2(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "教育週行動支付加扣點結果";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            string ThisBATCH_CASH_ID = string.Empty;

            int oldCash = Data.CASH;
            int seed = Guid.NewGuid().GetHashCode();
            int basic = 1;
            int min = 2;
            if (Data.IsRandomHighPoint)
            {
                basic = 5;
                min = 1;
            }
            Random Chance = new Random(seed);
            if (oldCash == 0)
            {
                Data.CASH = Chance.Next(min, 6) * basic;
            }
            else
            {
                //Data.CASH = Chance.Next(1, oldCash) * 1;
                Data.CASH = Chance.Next(min, 6) * basic;
            }

            bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
            if (OK)
            {
                Session["ADDI09_EditOne_SUBJECT"] = Data.SUBJECT;
                //Session["ADDI09_EditOne_CASH"] = Data.CASH;
                ViewBag.NextAction = "Game";
                return View("GameResult", Data);
            }

            TempData["StatusMessage"] = ErrorMsg;

            //原因選單
            ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "GAME_SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 20, 10, 0, -10, -20, -30 };

            return View("Game", Data);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult GameResult(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, Data.ADDT14_STYLE) + " - 教育週行動支付加扣點結果";

            return View(Data);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        public ActionResult EditOne()
        {
            ECOOL_DEVEntities Edb = new ECOOL_DEVEntities();
            ViewBag.BRE_NO = Bre_NO;
            if (string.IsNullOrWhiteSpace(Request["SYS_TABLE_TYPE"]))
                ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");
            else
                ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");
            ViewBag.Panel_Title = "即時特殊加點"; //ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE) + " - 即時加點";

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }
            ViewBag.uhRMT25YN = "N";
            decimal? Level = PermissionService.GetROLE_LEVEL(user?.USER_KEY, user?.SCHOOL_NO, user?.USER_NO);
            string ISLEVEL = Level != null ? ((int)Level).ToString() : "";
            if (ISLEVEL == HRMT24_ENUM.QAdminLevel.ToString() || ISLEVEL == HRMT24_ENUM.SuperAdminROLE.ToString())
            {
                ViewBag.uhRMT25YN = "Y";
            }
            var PermissionBtn = PermissionService.GetActionPermissionForBreNO(Bre_NO, user.SCHOOL_NO, user.USER_NO);
            ViewBag.IndexActionName = (PermissionBtn.Where(a => a.ActionName == "SysIndex").Select(a => a.BoolUse).FirstOrDefault() == true ? "SysIndex" : "Index");

            //原因選單
            ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };

            ADDI09EditOoneViewModel Data = new ADDI09EditOoneViewModel();

            if (Request["IsFix"] == "True") Data.IsFix = true;
            if (Request["IsRandom"] == "True") Data.IsRandom = true;
            if (Request["IsRandomHighPoint"] == "True") Data.IsRandomHighPoint = true;
            if (Data.IsFix == false)
            {
                if (Session["ADDI09_EditOne_IsFix"] != null) Data.IsFix = Convert.ToBoolean(Session["ADDI09_EditOne_IsFix"]);
            }
            if (Data.IsRandom == false)
            {
                if (Session["ADDI09_EditOne_IsRandom"] != null) Data.IsRandom = Convert.ToBoolean(Session["ADDI09_EditOne_IsRandom"]);
                if (Session["ADDI09_EditOne_IsRandomHighPoint"] != null) Data.IsRandomHighPoint = Convert.ToBoolean(Session["ADDI09_EditOne_IsRandomHighPoint"]);
            }

            if (Session["ADDI09_EditOne_SUBJECT"] != null || Session["ADDI09_EditOne_CASH"] != null)
            {
                if (Session["ADDI09_EditOne_SUBJECT"] != null) Data.SUBJECT = Session["ADDI09_EditOne_SUBJECT"].ToString();
                if (Session["ADDI09_EditOne_CASH"] != null) Data.CASH = int.Parse(Session["ADDI09_EditOne_CASH"].ToString());
                return View(Data);
            }
            return View(Data);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        public ActionResult EditOne14()
        {
            ViewBag.BRE_NO = Bre_NO;
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            ViewBag.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN;
            ViewBag.Panel_Title = "校內表現--感應";

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO(Bre_NO, user.SCHOOL_NO, user.USER_NO);
            ViewBag.IndexActionName = (PermissionBtn.Where(a => a.ActionName == "SysIndex").Select(a => a.BoolUse).FirstOrDefault() == true ? "SysIndex" : "Index");
            ViewBag.CONTENT_TXT = BDMT02Service.GetRefSelectListItem("ADDI09", "SYS_TABLE_SUBJECT", "ALL", user.SCHOOL_NO, "", true, null, true, null, ref db);
            //原因選單
            ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND().Where(a => a.Text == "學習表現" || a.Text == "校內競賽"||  a.Text == "品德表現" || a.Text == "志工" || a.Text == "領導人" || a.Text == "班級幹部" || a.Text == "班級幫手和榮譽" || a.Text == "七個習慣代言人");
            //ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
            SelectListGroup group1 = new SelectListGroup() { Name = "校內榮譽(不受每個月控管的總點數)" };
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };
            ViewBag.QuickItems = new List<SelectListItem>()
            {
                new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1},
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1},
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1},
                 new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1},
                               new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1},
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }
            };
            List<SelectListItem> CONTENTitems = new List<SelectListItem>();
           
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };

            ADDI09EditOoneViewModel Data = new ADDI09EditOoneViewModel();
            if (Session["ADDI09_EditOne14_SUBJECT"] != null || Session["ADDI09_EditOne14_CASH"] != null)
            {
                Data.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN;
                if (Session["ADDI09_EditOne14_SUBJECT"] != null) Data.SUBJECT = Session["ADDI09_EditOne14_SUBJECT"].ToString();
                if (Session["ADDI09_EditOne14_CONTENT_TXT"] != null) Data.CONTENT_TXT = Session["ADDI09_EditOne14_CONTENT_TXT"].ToString();
                if (Session["ADDI09_EditOne14_CASH"] != null) Data.CASH = int.Parse(Session["ADDI09_EditOne14_CASH"].ToString());
                return View(Data);
            }

            return View(Data);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        public ActionResult EditOne15()
        {
            ViewBag.BRE_NO = Bre_NO;

            ViewBag.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, string.Empty) + " - 即時加點";

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO(Bre_NO, user.SCHOOL_NO, user.USER_NO);
            ViewBag.IndexActionName = (PermissionBtn.Where(a => a.ActionName == "SysIndex").Select(a => a.BoolUse).FirstOrDefault() == true ? "SysIndex" : "Index");

            //原因選單
            ViewBag.Scoreitems = ADDI07Controller.GetScoreitems();

            if (Session["ADDI09_EditOne15_SUBJECT"] != null || Session["ADDI09_EditOne15_CASH"] != null)
            {
                ADDI09EditOoneViewModel Data = new ADDI09EditOoneViewModel();
                Data.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN;
                if (Session["ADDI09_EditOne15_SUBJECT"] != null) Data.SUBJECT = Session["ADDI09_EditOne15_SUBJECT"].ToString();
                if (Session["ADDI09_EditOne15_CONTENT_TXT"] != null) Data.CONTENT_TXT = Session["ADDI09_EditOne15_CONTENT_TXT"].ToString();
                if (Session["ADDI09_EditOne15_CASH"] != null) Data.CASH = int.Parse(Session["ADDI09_EditOne15_CASH"].ToString());
                return View(Data);
            }

            return View();
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult EditOne142(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(Data.SYS_TABLE_TYPE, string.Empty) + "即時加點結果";
            ViewBag.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN;
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            //原因選單
            ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND().Where(a => a.Text == "學習表現" || a.Text == "校內競賽" || a.Text == "品德表現" || a.Text == "志工"|| a.Text == "領導人" || a.Text == "班級幹部" || a.Text == "班級幫手和榮譽" || a.Text == "七個習慣代言人");
            //ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
            SelectListGroup group1 = new SelectListGroup() { Name = "校內榮譽(不受每個月控管的總點數)" };
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };
            ViewBag.QuickItems = new List<SelectListItem>()
            {
                new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1},
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1},
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1},
                new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1},
                new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1},
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }
            };
            ViewBag.CONTENT_TXT = BDMT02Service.GetRefSelectListItem("ADDI09", "SYS_TABLE_SUBJECT", "ALL", user.SCHOOL_NO, "", true, null, true, null, ref db);
            //原因選單
            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }
            if (string.IsNullOrWhiteSpace(Data.CONTENT_TXT)) {
                TempData["StatusMessage"] = "請填入具體事蹟";
                return View("EditOne14", Data);
            }
            string ThisBATCH_CASH_ID = string.Empty;
            bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
            if (OK)
            {
                Session["ADDI09_EditOne14_SUBJECT"] = Data.SUBJECT;
                Session["ADDI09_EditOne14_CONTENT_TXT"] = Data.CONTENT_TXT;
                Session["ADDI09_EditOne14_CASH"] = Data.CASH;
                ViewBag.NextAction = "EditOne14";
                return View("EditOneResult", Data);
            }

            TempData["StatusMessage"] = ErrorMsg;

            //原因選單
            ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND();
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };

            return View("EditOne14", Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult EditOne152(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(Data.SYS_TABLE_TYPE, string.Empty) + "即時加點結果";
            ViewBag.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC;

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            string ThisBATCH_CASH_ID = string.Empty;
            bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
            if (OK)
            {
                Session["ADDI09_EditOne15_SUBJECT"] = Data.SUBJECT;
                Session["ADDI09_EditOne15_CONTENT_TXT"] = Data.CONTENT_TXT;
                //Session["ADDI09_EditOne_CASH"] = Data.CASH;
                ViewBag.NextAction = "EditOne15";
                return View("EditOneResult", Data);
            }

            TempData["StatusMessage"] = ErrorMsg;

            //原因選單
            ViewBag.Scoreitems = ADDI07Controller.GetScoreitems();

            return View("EditOne15", Data);
        }

        [HttpPost]
        public JsonResult ToQueryUser(string USER_NO, string CLASS_SEAT)
        {
            this.Shared();
            string Message = string.Empty;
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };
            HRMT01 hh = QueryUser(USER_NO);

            if (hh != null)
            {
                var data = "{ \"USER_NO\" : \"" + hh.USER_NO + "\" , \"USER_NAME\" : \"" + hh.NAME + "\" }";
                return Json(data, JsonRequestBehavior.AllowGet);
            }
            else
            {
                var data = "{ \"USER_NO\" : \"\" , \"USER_NAME\" : \" 找不到對應的學生 \" }";
                return Json(data, JsonRequestBehavior.AllowGet);
            }
        }

        private bool SaveOne(ADDI09EditOoneViewModel Data, out string OutBATCH_CASH_ID)
        {
            HRMT01 hh = CheckSaveOne(Data);
            if (string.IsNullOrEmpty(ErrorMsg) == false) { ErrorMsg = "錯誤\r\n\r\n" + ErrorMsg; OutBATCH_CASH_ID = null; return false; }

            Data.USER_NO = hh.USER_NO;
            Data.USER_NAME = hh.NAME;
            List<HRMT01> H01List = new List<HRMT01>();
            H01List.Add(hh);
            Data.DataList = new List<ADDI09SelectListViewModel>();
            ADDI09SelectListViewModel mm = new ADDI09SelectListViewModel();
            mm.CLASS_NO = hh.CLASS_NO;
            mm.NAME = hh.NAME;
            mm.SCHOOL_NO = hh.SCHOOL_NO;
            mm.SEAT_NO = hh.SEAT_NO;
            mm.SNAME = hh.SNAME;
            mm.USER_NO = hh.USER_NO;
            Data.DataList.Add(mm);

            //BATCH_CASH_ID = SCHOOL_NO +年 月 日+3 碼流水號
            string BATCH_CASH_ID = GetNewBatchCashId(Data.SYS_TABLE_TYPE);

            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            string LogDesc = ADDI09EditViewModel.GetSysTableTypeString(Data.SYS_TABLE_TYPE, Data.ADDT14_STYLE);

            bool OK = false;
            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER)
            {
                OK = InsertADDT20(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, LogDesc, H01List, Data);
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
            {
                OK = InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, LogDesc, H01List, Data);
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                OK = InsertADDT15(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, LogDesc, H01List, Data);
            }

            if (OK == false)
            {
                OutBATCH_CASH_ID = null;
                return false;
            }

            OutBATCH_CASH_ID = BATCH_CASH_ID;
            return true;
        }

        private HRMT01 CheckSaveOne(ADDI09EditOoneViewModel Data)
        {
            decimal? Level = PermissionService.GetROLE_LEVEL(user?.USER_KEY, user?.SCHOOL_NO, user?.USER_NO);
            string ISLEVEL = Level != null ? ((int)Level).ToString() : "";
            if (ISLEVEL == HRMT24_ENUM.QAdminLevel.ToString() || ISLEVEL == HRMT24_ENUM.SuperAdminROLE)
            {
                ISLEVEL = "Y";
            }
            if (Data.CASH == 0)
            {
                ErrorMsg = ErrorMsg + "「獎懲點數」請輸入大於小於0的數字\r\n";
                ModelState.AddModelError("CASH", "*請輸入獎懲點數");
            }
            if (Data.CASH > 50 && user.USER_TYPE == "T" && ISLEVEL != "Y")
            {
                ErrorMsg = ErrorMsg + "老師給點最高50";
                ModelState.AddModelError("CASH", "*輸入獎懲點數超過限制點數");
            }
            if (Data.CASH > 100 && ISLEVEL == "Y")
            {
                ErrorMsg = ErrorMsg + "管理者給點最高100";
                ModelState.AddModelError("CASH", "*輸入獎懲點數超過限制點數");
            }
            if (string.IsNullOrWhiteSpace(Data.SUBJECT) == false)
            {
                if (Data.SUBJECT.Length > 200)
                {
                    ErrorMsg = ErrorMsg + "「獎懲主旨」輸入的長度不可大於200碼\r\n";
                    ModelState.AddModelError("SUBJECT", "「獎懲主旨」輸入的長度不可大於200碼");
                }
            }
            else
            {
                ErrorMsg = ErrorMsg + "「獎懲主旨」必填\r\n";
                ModelState.AddModelError("SUBJECT", "「獎懲主旨」必填");
            }
            if (string.IsNullOrWhiteSpace(Data.MEMO) == false)
            {
                if (Data.MEMO.Length > 255)
                {
                    ErrorMsg = ErrorMsg + "「備註」輸入的長度不可大於255碼\r\n";
                    ModelState.AddModelError("MEMO", "「備註」輸入的長度不可大於255碼");
                }
            }

            HRMT01 hh = QueryUser(Data.USER_NO);
            if (hh == null)
            {
                ErrorMsg = ErrorMsg + "找不到該帳號 \r\n";
                ModelState.AddModelError("DataList", "*找不到該帳號");
            }
            return hh;
        }

        private HRMT01 QueryUser(string QueryKey)
        {
            HRMT01 hh;
            switch (QueryKey?.Trim().Length ?? 0)
            {
                case (int)ADDI09_QueryType.USER_NO:
                    hh = EntitiesDb.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == QueryKey && a.USER_STATUS != 9 && a.USER_TYPE == "S").FirstOrDefault();
                    break;

                case (int)ADDI09_QueryType.CLASS_SEAT:
                    string classno = QueryKey.Substring(0, 3);
                    string seatno = QueryKey.Substring(3, 2);
                    hh = EntitiesDb.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == classno && a.SEAT_NO == seatno && a.USER_STATUS != 9 && a.USER_TYPE == "S").FirstOrDefault();
                    break;

                case (int)ADDI09_QueryType.STUDENT_CARDNO:
                    hh = EntitiesDb.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CARD_NO == QueryKey && a.USER_STATUS != 9 && a.USER_TYPE == "S").FirstOrDefault();
                    break;

                default:
                    hh = default(HRMT01);
                    break;
            }
            return hh;
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult EditOneRandom(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "即時加點結果";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            string ThisBATCH_CASH_ID = string.Empty;

            int oldCash = Data.CASH;
            int seed = Guid.NewGuid().GetHashCode();
            Random Chance = new Random(seed);
            int basic = 1;
            int min = 2;
            if (Data.IsRandomHighPoint)
            {
                basic = 5;
                min = 1;
            }
            if (oldCash == 0)
            {
                Data.CASH = Chance.Next(min, 6) * basic;
            }
            else
            {
                //Data.CASH = Chance.Next(1, oldCash) * 1;
                Data.CASH = Chance.Next(min, 6) * basic;
            }

            HRMT01 hh = this.CheckSaveOne(Data);
            if (string.IsNullOrEmpty(ErrorMsg) == false)
            {
                TempData["StatusMessage"] = "錯誤\r\n\r\n" + ErrorMsg;
                //原因選單
                ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
                Data.CASH = oldCash;
                return RedirectToAction("EditOne", Data);
            }
            if (Data.IsRandomHighPoint)
            {
                bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
                if (OK)
                {
                    Session["ADDI09_EditOne_SUBJECT"] = Data.SUBJECT;
                    return View("EditOne2MP4", Data);
                }
                TempData["StatusMessage"] = ErrorMsg;

                //原因選單
                ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
                //給予e酷幣選項
                ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };

                return View("EditOne", Data);
            }

            return View(Data);
        }

        //[HttpPost]
        //[CheckPermission(CheckACTION_ID = "EditOne")] //檢查權限
        //[ValidateAntiForgeryToken]
        //[CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        //public ActionResult EditOneRandomMP4()
        //{
        //    ADDI09EditOoneViewModel Data = new ADDI09EditOoneViewModel();

        //    ViewBag.BRE_NO = Bre_NO;
        //    ViewBag.Panel_Title = "即時加點結果";
        //    ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");

        //    user = UserProfileHelper.Get();
        //    SCHOOL_NO = UserProfileHelper.GetSchoolNo();

        //    if (user != null)
        //    {
        //        if (user.SCHOOL_NO != null)
        //        {
        //            SCHOOL_NO = user.SCHOOL_NO;
        //            USER_KEY = user.USER_KEY;
        //            USER_NO = user.USER_NO;
        //        }
        //    }

        //    string ThisBATCH_CASH_ID = string.Empty;

        //    int oldCash = Data.CASH;
        //    int seed = Guid.NewGuid().GetHashCode();
        //    int basic = 1;
        //    int min = 2;
        //    if (Data.IsRandomHighPoint)
        //    {
        //        basic = 5;
        //        min = 1;
        //    }
        //    Random Chance = new Random(seed);
        //    if (oldCash == 0)
        //    {
        //        Data.CASH = Chance.Next(min, 6) * basic;
        //    }
        //    else
        //    {
        //        //Data.CASH = Chance.Next(1, oldCash) * 1;
        //        Data.CASH = Chance.Next(min, 6) * basic;
        //    }

        //    //HRMT01 hh = this.CheckSaveOne(Data);
        //    //if (string.IsNullOrEmpty(ErrorMsg) == false)
        //    //{
        //    //    TempData["StatusMessage"] = "錯誤\r\n\r\n" + ErrorMsg;
        //    //    //原因選單
        //    //    ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
        //    //    Data.CASH = oldCash;
        //    //    return RedirectToAction("EditOne", Data);
        //    //}

        //    return View(Data);
        //}

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult EditOne2(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "即時加點結果";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            string ThisBATCH_CASH_ID = string.Empty;

            int oldCash = Data.CASH;
            int seed = Guid.NewGuid().GetHashCode();
            int basic = 1;
            int min = 2;
            if (Data.IsRandomHighPoint)
            {
                basic = 5;
                min = 1;
            }
            Random Chance = new Random(seed);
            if (oldCash == 0)
            {
                Data.CASH = Chance.Next(min, 6) * basic;
            }
            else
            {
                //Data.CASH = Chance.Next(1, oldCash) * 1;
                Data.CASH = Chance.Next(min, 6) * basic;
            }

            bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
            if (OK)
            {
                Session["ADDI09_EditOne_SUBJECT"] = Data.SUBJECT;
                //Session["ADDI09_EditOne_CASH"] = Data.CASH;
                ViewBag.NextAction = "EditOne";
                return View("EditOneResult", Data);
            }

            TempData["StatusMessage"] = ErrorMsg;

            //原因選單
            ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };

            return View("EditOne", Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult EditOne2MP4(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "即時加點結果";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            string ThisBATCH_CASH_ID = string.Empty;

            int oldCash = Data.CASH;
            int seed = Guid.NewGuid().GetHashCode();
            int basic = 1;
            int min = 2;
            if (Data.IsRandomHighPoint)
            {
                basic = 5;
                min = 1;
            }
            Random Chance = new Random(seed);
            if (oldCash == 0)
            {
                Data.CASH = Chance.Next(min, 6) * basic;
            }
            else
            {
                //Data.CASH = Chance.Next(1, oldCash) * 1;
                Data.CASH = Chance.Next(min, 6) * basic;
            }

            bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
            if (OK)
            {
                Session["ADDI09_EditOne_SUBJECT"] = Data.SUBJECT;
                //Session["ADDI09_EditOne_CASH"] = Data.CASH;
                ViewBag.NextAction = "EditOne";
                return View(Data);
            }

            TempData["StatusMessage"] = ErrorMsg;

            //原因選單
            ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };

            return View("EditOne", Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "EditOneIndex", CheckBRE_NO = "ADDI091")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult EditOne(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "即時加點結果";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();
            ViewBag.uhRMT25YN = "N";
            decimal? Level = PermissionService.GetROLE_LEVEL(user?.USER_KEY, user?.SCHOOL_NO, user?.USER_NO);
            string ISLEVEL = Level != null ? ((int)Level).ToString() : "";
            if (ISLEVEL == HRMT24_ENUM.QAdminLevel.ToString() || ISLEVEL == HRMT24_ENUM.SuperAdminROLE.ToString())
            {
                ViewBag.uhRMT25YN = "Y";
            }
            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            string ThisBATCH_CASH_ID = string.Empty;

            bool OK = this.SaveOne(Data, out ThisBATCH_CASH_ID);
            if (OK)
            {
                Session["ADDI09_EditOne_SUBJECT"] = Data.SUBJECT;
                Session["ADDI09_EditOne_CASH"] = Data.CASH;
                Session["ADDI09_EditOne_IsFix"] = Data.IsFix;
                Session["ADDI09_EditOne_IsRandom"] = Data.IsRandom;
                Session["ADDI09_EditOne_IsRandomHighPoint"] = Data.IsRandomHighPoint;

                return View("EditOneResult", Data);
            }

            TempData["StatusMessage"] = ErrorMsg;

            //原因選單
            ViewBag.SubjectSelectItem = BDMT02Service.GetRefSelectListItem("ADDI09", "SUBJECT", "ALL", SCHOOL_NO, "", true, null, true, null, ref EntitiesDb);
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 };

            return View(Data);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult EditOneResult(ADDI09EditOoneViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf("ADDT20");
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, Data.ADDT14_STYLE) + " - 即時加點";

            return View(Data);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Edit(string SYS_TABLE_TYPE, string ADDT14_STYLE = "", bool Individual_Give = false)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 獎勵點數輸入";
            ViewBag.Individual_Give = Individual_Give;
            this.Shared();

            return View();
        }
        [HttpGet]
        public ActionResult Edit1(string SYS_TABLE_TYPE, string ADDT14_STYLE = "", bool Individual_Give = false)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 獎勵點數輸入";
            ViewBag.Individual_Give = Individual_Give;
            this.Shared();

            return View();
        }
        [HttpPost]
        public ActionResult Edit1(ADDI09EditViewModel Data, HttpPostedFileBase files)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "獎勵點數輸入";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(Data.SYS_TABLE_TYPE);
            ViewBag.Individual_Give = Data.Individual_Give;

            this.Shared();

            Data.DataList = new List<ADDI09SelectListViewModel>();
            Data.DataList = Db.GetListData(Session.SessionID);

            string ThisBATCH_CASH_ID = string.Empty;

            // 快速大量加點 = ADDT14
           
            bool OK = this.Save1(Data,out ThisBATCH_CASH_ID);
            if (OK)
            {
                if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                {
                    TempData["StatusMessage"] = "老師批次加扣點全數匯入成功。";
                    return RedirectToAction("Index", new { SYS_TABLE_TYPE = Data.SYS_TABLE_TYPE });
                }
                else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY)
                {
                    TempData["StatusMessage"] = "好運給點全數匯入成功。";
                    return RedirectToAction("Index", new { SYS_TABLE_TYPE = Data.SYS_TABLE_TYPE });
                }
                return RedirectToAction("ListView", new { ThisSaveBATCH_CASH_ID = ThisBATCH_CASH_ID, SYS_TABLE_TYPE = Data.SYS_TABLE_TYPE });
            }

            TempData["StatusMessage"] = ErrorMsg;

            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
            {
                return View("EditQuickBulk", Data);
            }

            return View(Data);
        }
        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult Edit(ADDI09EditViewModel Data, HttpPostedFileBase QuFIle,HttpPostedFileBase files)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "獎勵點數輸入";
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(Data.SYS_TABLE_TYPE);
            ViewBag.Individual_Give = Data.Individual_Give;

            this.Shared();

            Data.DataList = new List<ADDI09SelectListViewModel>();
            Data.DataList = Db.GetListData(Session.SessionID);

            string ThisBATCH_CASH_ID = string.Empty;

            // 快速大量加點 = ADDT14
            if (QuFIle == null && files != null)
            {
                QuFIle = files;
            }
            bool OK = this.Save(Data, QuFIle, out ThisBATCH_CASH_ID);
            if (OK)
            {
                if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                {
                    TempData["StatusMessage"] = "老師批次加扣點全數匯入成功。";
                    return RedirectToAction("Index", new { SYS_TABLE_TYPE = Data.SYS_TABLE_TYPE });
                }
                else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY)
                {
                    TempData["StatusMessage"] = "好運給點全數匯入成功。";
                    return RedirectToAction("Index", new { SYS_TABLE_TYPE = Data.SYS_TABLE_TYPE });
                }
                return RedirectToAction("ListView", new { ThisSaveBATCH_CASH_ID = ThisBATCH_CASH_ID, SYS_TABLE_TYPE = Data.SYS_TABLE_TYPE });
            }

            TempData["StatusMessage"] = ErrorMsg;

            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
            {
                return View("EditQuickBulk", Data);
            }

            return View(Data);
        }

        public ActionResult EditQuickBulk(string SYS_TABLE_TYPE, string ADDT14_STYLE = "", bool DeleteTemp = false)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.Panel_Title = ADDI09EditViewModel.GetSysTableTypeString(ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE) + " - 獎勵點數輸入";
            ViewBag.Individual_Give = true;
            this.Shared();

            user = UserProfileHelper.Get();

            if (user == null)
                throw new Exception("查詢不到USER資料，請重新登入");

            // 刪除TEMP資料
            IEnumerable<ADDT20_TEMP> temps = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID);
            EntitiesDb.ADDT20_TEMP.RemoveRange(temps);
            EntitiesDb.SaveChanges();

            var teacherClassNo = EntitiesDb.HRMT03.FirstOrDefault(h => h.SCHOOL_NO == SCHOOL_NO && h.TEACHER_NO == user.USER_NO)?.CLASS_NO;
            if (string.IsNullOrEmpty(teacherClassNo))
            {
                return View();
            }

            // 大量快速給點
            var classStudents = EntitiesDb.HRMT01
                    .Where(h => h.SCHOOL_NO == SCHOOL_NO && h.CLASS_NO == teacherClassNo && h.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(h.USER_STATUS)));

            string sessionId = Session.SessionID;
            var now = DateTime.Now;

            // remove before
            if (DeleteTemp)
            {
                var TEMPS = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID && x.SCHOOL_NO == SCHOOL_NO);
                EntitiesDb.ADDT20_TEMP.RemoveRange(TEMPS);
            }

            if (DeleteTemp)
            {
                int? NUM = 1;
                ADDT20_TEMP NOW = EntitiesDb.ADDT20_TEMP.Where(x => x.SESSION_ID == Session.SessionID)
                    .OrderByDescending(a => a.NUM).FirstOrDefault();
                if (NOW != null)
                {
                    NUM = NOW.NUM + 1;
                }
                else {

                    classStudents = classStudents.OrderBy(x => x.SEAT_NO);

                }
                foreach (var s in classStudents)
                {
                    var temp = new ADDT20_TEMP()
                    {
                        USER_NO = s.USER_NO,
                        SESSION_ID = sessionId,
                        SCHOOL_NO = SCHOOL_NO,
                        CRE_PERSON = USER_KEY,
                        CRE_DATE = now,
                        NUM = NUM++
                    };
                    EntitiesDb.ADDT20_TEMP.Add(temp);
                }

                EntitiesDb.SaveChanges();
            }
            return View();
        }

        /// <summary>
        /// 其它畫面 輸入
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _ViewOTHER(ADDI09EditViewModel Data)
        {
            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
            {
                Data.SUBJECT = "班級服務";
            }
            return PartialView(Data);
        }
        public ActionResult _ViewLEADER(ADDI09EditViewModel Data)
        {
            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_LEADER_BATCH)
            {
                Data.SUBJECT = "七個習慣代言人";
            }
            return PartialView(Data);
        }
        /// <summary>
        /// 老師給點 輸入
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _ViewGiveTeacher(ADDI09EditViewModel Data)
        {
            return PartialView(Data);
        }

        /// <summary>
        /// 好運給點 輸入
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _ViewLuckyPoint(ADDI09EditViewModel Data)
        {
            return PartialView(Data);
        }

        /// <summary>
        /// 校內畫面 輸入
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _ViewIN(ADDI09EditViewModel Data)
        {
            if (Data.ADDT14_STYLE == "LEADER")
            {
                ViewBag.IAWARD_KIND = new List<SelectListItem>
                {
                    new SelectListItem { Text = "班級幹部", Value = "班級幹部", Selected = true }
                };
            }
            else if (Data.ADDT14_STYLE == "HELPER")
            {
                ViewBag.IAWARD_KIND = new List<SelectListItem>
                {
                    new SelectListItem { Text = "班級幫手和榮譽", Value = "班級幫手和榮譽", Selected = true }
                };
            }
            else
            {
                ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND(Data.SUBJECT);
            }

            return PartialView(Data);
        }

        /// <summary>
        /// 校外 畫面 輸入
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _ViewOBC(ADDI09EditViewModel Data)
        {
            ViewBag.Scoreitems = ADDI07Controller.GetScoreitems(Data.CONTENT_TXT);
            return PartialView(Data);
        }

        /// <summary>
        /// 快速大量加點
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _ViewQuickBulk(ADDI09EditViewModel Data)
        {
            SelectListGroup group1 = new SelectListGroup() { Name = "校內榮譽(不受每個月控管的總點數)" };
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };

            ViewBag.QuickItems = new List<SelectListItem>()
            {
                new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1},
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1},
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1},
    
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 },
        new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1},
         new SelectListItem() { Text = "", Value = "領導人", Group = group1},
            };

            return PartialView(Data);
        }

        public ActionResult PrintExcel(ADDI09ListViewViewModel Data, string ThisSaveBATCH_CASH_ID, string SOURCE_NO, string SYS_TABLE_TYPE, string ADDT14_STYLE, string ListSchool, string ListUSERNO, int pageSize = 20, string fromSouce = "")
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();
            ViewBag.temfromSouce = fromSouce;
            int count = int.MinValue;

            if (Data.Search == null) Data.Search = new ADDI09SearchViewModel();

            if (Data.Search.SYS_TABLE_TYPE == null)
            {
                Data.Search.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            }
            if (Data.Search.ADDT14_STYLE == null) Data.Search.ADDT14_STYLE = ADDT14_STYLE;

            ViewBag.SysTableTypeItem = ADDI09EditViewModel.SYSTableTypeItem().
        Select(x => new SelectListItem { Text = x.Item2, Value = x.Item1, Selected = x.Item1 == Data.Search.SYS_TABLE_TYPE }).Distinct().OrderBy(o => o.Value);
            if (string.IsNullOrWhiteSpace(ListSchool) == false)
            {
                Data.Search.ListSchool = ListSchool;
            }
            if (string.IsNullOrWhiteSpace(ListUSERNO) == false)
            {
                Data.Search.ListUSERNO = ListUSERNO;
            }
            if (string.IsNullOrWhiteSpace(ThisSaveBATCH_CASH_ID) == false)
            {
                Data.Search.BATCH_CASH_ID = ThisSaveBATCH_CASH_ID;

                pageSize = 999999;

                if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                {
                    Data.Search.OrderByName = "NUM";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    Data.Search.OrderByName = "IAWARD_ID";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    Data.Search.OrderByName = "OAWARD_ID";
                }

                Data.Search.SyntaxName = "ASC";
            }
            if (string.IsNullOrWhiteSpace(SOURCE_NO) == false)
            {
                Data.Search.SOURCE_NO = SOURCE_NO;
                pageSize = 999999;

                if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                {
                    Data.Search.OrderByName = "NUM";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    Data.Search.OrderByName = "IAWARD_ID";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    Data.Search.OrderByName = "OAWARD_ID";
                }

                Data.Search.SyntaxName = "ASC";
            }

            SetColName(Data);

            var DataList = Db.GetListData(Data.Search, pageSize, ref count, SCHOOL_NO);
            if (Data.Search.Search_SUBJECT != null)
            {
                DataList = DataList.Where(x => x.SUBJECT == Data.Search.Search_SUBJECT).ToList();
            }
            Data.ADDT20PrintList = DataList;
            DataTable DataTableExcel = Data.ADDT20PrintList.ToList().AsDataTable();

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/ExtendCash_Log_ExportExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "批次加扣點明細", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\批次加扣點明細" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "批次加扣點明細.xlsx");//輸出檔案給Client端
        }
        public ActionResult PrintQuery2(ADDI09ListViewViewModel Data, string ThisSaveBATCH_CASH_ID, string SOURCE_NO, string SYS_TABLE_TYPE, string ADDT14_STYLE, string ListSchool, string ListUSERNO, int pageSize = 20, string fromSouce = "")
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();
            ViewBag.temfromSouce = fromSouce;
            int count = int.MinValue;

            if (Data.Search == null) Data.Search = new ADDI09SearchViewModel();

            if (Data.Search.SYS_TABLE_TYPE == null)
            {
                Data.Search.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            }
            if (Data.Search.ADDT14_STYLE == null) Data.Search.ADDT14_STYLE = ADDT14_STYLE;

            ViewBag.SysTableTypeItem = ADDI09EditViewModel.SYSTableTypeItem().
        Select(x => new SelectListItem { Text = x.Item2, Value = x.Item1, Selected = x.Item1 == Data.Search.SYS_TABLE_TYPE }).Distinct().OrderBy(o => o.Value);
            if (string.IsNullOrWhiteSpace(ListSchool) == false)
            {
                Data.Search.ListSchool = ListSchool;
            }
            if (string.IsNullOrWhiteSpace(ListUSERNO) == false)
            {
                Data.Search.ListUSERNO = ListUSERNO;
            }
            if (string.IsNullOrWhiteSpace(ThisSaveBATCH_CASH_ID) == false)
            {
                Data.Search.BATCH_CASH_ID = ThisSaveBATCH_CASH_ID;

                pageSize = 999999;

                if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                {
                    Data.Search.OrderByName = "NUM";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    Data.Search.OrderByName = "IAWARD_ID";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    Data.Search.OrderByName = "OAWARD_ID";
                }

                Data.Search.SyntaxName = "ASC";
            }
            if (string.IsNullOrWhiteSpace(SOURCE_NO) == false)
            {
                Data.Search.SOURCE_NO = SOURCE_NO;
                pageSize = 999999;

                if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                {
                    Data.Search.OrderByName = "NUM";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    Data.Search.OrderByName = "IAWARD_ID";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    Data.Search.OrderByName = "OAWARD_ID";
                }

                Data.Search.SyntaxName = "ASC";
            }

            SetColName(Data);

            var DataList = Db.GetListData(Data.Search, pageSize, ref count, SCHOOL_NO);
            if (Data.Search.Search_SUBJECT != null)
            {
                DataList = DataList.Where(x => x.SUBJECT == Data.Search.Search_SUBJECT).ToList();
            }
            Data.ADDT20PrintList = DataList;
            return View(Data);
        }
        public ActionResult _ViewQuickBulk1(ADDI09EditViewModel Data)
        {
          
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };

            ViewBag.QuickItems = new List<SelectListItem>()
            {
               
               
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 },
                 new SelectListItem() { Text = "領導人(不受每個月控管的總點數)", Value = "領導人", Group = group2},
              new SelectListItem() { Text = "七個習慣代言人(不受每個月控管的總點數)", Value = "七個習慣代言人", Group = group2}

            };

            return PartialView(Data);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ListView1(string SOURCE_NO, string ThisSaveBATCH_CASH_ID, string SYS_TABLE_TYPE, string ADDT14_STYLE, string USER_NO, string SCHOOL_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "批次給點/扣點歷史紀錄";
            ViewBag.ThisSaveBATCH_CASH_ID = ThisSaveBATCH_CASH_ID;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.SOURCE_NO = SOURCE_NO;
            ViewBag.ListSchool = SCHOOL_NO;
            ViewBag.ListUSERNO = USER_NO;
            if (ViewBag.fromSOurce != null && ViewBag.USER_NO != null)

            {
            }
            if (string.IsNullOrWhiteSpace(ThisSaveBATCH_CASH_ID) == false)
            {
                TempData["StatusMessage"] = OkMsg;
            }
            return View();
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ListView(string SOURCE_NO, string ThisSaveBATCH_CASH_ID, string SYS_TABLE_TYPE, string ADDT14_STYLE, string USER_NO, string SCHOOL_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "批次給點/扣點歷史紀錄";
            ViewBag.ThisSaveBATCH_CASH_ID = ThisSaveBATCH_CASH_ID;
            ViewBag.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            ViewBag.ADDT14_STYLE = ADDT14_STYLE;
            ViewBag.SOURCE_NO = SOURCE_NO;
            ViewBag.ListSchool = SCHOOL_NO;
            ViewBag.ListUSERNO = USER_NO;
            if (ViewBag.fromSOurce != null && ViewBag.USER_NO != null)

            {
            }
            if (string.IsNullOrWhiteSpace(ThisSaveBATCH_CASH_ID) == false)
            {
                TempData["StatusMessage"] = OkMsg;
            }
            return View();
        }
        public ActionResult _ModifyView(QuerySelectPrintViewModel Item)
        {
            return PartialView(Item);
        }

        /// <summary>
        /// ListView 的_PageContent
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="BATCH_CASH_ID"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(ADDI09ListViewViewModel Data, string ThisSaveBATCH_CASH_ID, string SOURCE_NO, string SYS_TABLE_TYPE, string ADDT14_STYLE, string ListSchool, string ListUSERNO, int pageSize = 20,string fromSouce="")
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();
            ViewBag.temfromSouce = fromSouce;
            int count = int.MinValue;

            if (Data.Search == null) Data.Search = new ADDI09SearchViewModel();

            if (Data.Search.SYS_TABLE_TYPE == null)
            {
                Data.Search.SYS_TABLE_TYPE = isDbNullSysTableTypeDf(SYS_TABLE_TYPE);
            }
            if (Data.Search.ADDT14_STYLE == null) Data.Search.ADDT14_STYLE = ADDT14_STYLE;

            ViewBag.SysTableTypeItem = ADDI09EditViewModel.SYSTableTypeItem().
        Select(x => new SelectListItem { Text = x.Item2, Value = x.Item1, Selected = x.Item1 == Data.Search.SYS_TABLE_TYPE }).Distinct().OrderBy(o => o.Value);
            if (string.IsNullOrWhiteSpace(ListSchool) == false)
            {
                Data.Search.ListSchool = ListSchool;
            }
            if (string.IsNullOrWhiteSpace(ListUSERNO) == false)
            {
                Data.Search.ListUSERNO = ListUSERNO;
            }
            if (string.IsNullOrWhiteSpace(ThisSaveBATCH_CASH_ID) == false)
            {
                Data.Search.BATCH_CASH_ID = ThisSaveBATCH_CASH_ID;

                pageSize = 999999;

                if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                {
                    Data.Search.OrderByName = "NUM";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    Data.Search.OrderByName = "IAWARD_ID";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    Data.Search.OrderByName = "OAWARD_ID";
                }

                Data.Search.SyntaxName = "ASC";
            }
            if (string.IsNullOrWhiteSpace(SOURCE_NO) == false)
            {
                Data.Search.SOURCE_NO = SOURCE_NO;
                pageSize = 999999;

                if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                {
                    Data.Search.OrderByName = "NUM";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                    || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    Data.Search.OrderByName = "IAWARD_ID";
                }
                else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    Data.Search.OrderByName = "OAWARD_ID";
                }

                Data.Search.SyntaxName = "ASC";
            }

            SetColName(Data);

            var DataList = Db.GetListData(Data.Search, pageSize, ref count, SCHOOL_NO);
            if (Data.Search.Search_SUBJECT != null)
            {
                DataList = DataList.Where(x => x.SUBJECT == Data.Search.Search_SUBJECT).ToList();
            }

            Data.ADDT20List = DataList.ToPagedList(Data.Search.Page - 1, pageSize, count);

            return PartialView(Data);
        }

        public ActionResult Details(ADDI09DetailsViewModel Data, string vBATCH_CASH_ID, string vSCHOOL_NO, string vUSER_NO, string ThisSaveBATCH_CASH_ID, int vNUM)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "批次給點/扣點歷史明細紀錄";
            ViewBag.ThisSaveBATCH_CASH_ID = ThisSaveBATCH_CASH_ID;

            if (Data.Search == null) Data.Search = new ADDI09SearchViewModel();

            if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
            {
                Data.aADDT20 = EntitiesDb.ADDT20.Where(a => a.BATCH_CASH_ID == vBATCH_CASH_ID && a.SCHOOL_NO == vSCHOOL_NO && a.USER_NO == vUSER_NO && a.NUM == vNUM).FirstOrDefault();
            }
            else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
            {
                Data.aADDT14 = EntitiesDb.ADDT14.Where(a => a.IAWARD_ID == vNUM).FirstOrDefault();
            }
            else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                Data.aADDT15 = EntitiesDb.ADDT15.Where(a => a.OAWARD_ID == vNUM).FirstOrDefault();
            }

            return View(Data);
        }

        /// <summary>
        /// 其他 明細
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _DetailsOTHER(ADDT20 Data)
        {
            ViewBag.IMG_FILE = new FileHelper().GetDirectorySysPathImg(Data.SCHOOL_NO, ImgPath, Path.Combine(Data.BATCH_CASH_ID, Data.USER_NO), Data.IMG_FILE);
            return PartialView(Data);
        }

        /// <summary>
        /// 校內 明細
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _DetailsIN(ADDT14 Data)
        {
            ViewBag.IMG_FILE = new FileHelper().GetDirectorySysPathImg(Data.SCHOOL_NO, ADDI06Controller.ImgPath, Data.IAWARD_ID.ToString(), Data.IMG_FILE);
            return PartialView(Data);
        }

        /// <summary>
        /// 校外 明細
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _DetailsOBC(ADDT15 Data)
        {
            ViewBag.IMG_FILE = new FileHelper().GetDirectorySysPathImg(Data.SCHOOL_NO, ADDI07Controller.ImgPath, Data.OAWARD_ID.ToString(), Data.IMG_FILE);
            return PartialView(Data);
        }

        public ActionResult Delete(string BATCH_CASH_ID, int NUM)
        {
            if (string.IsNullOrEmpty(BATCH_CASH_ID))
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            ADDT20 aDDT20 = EntitiesDb.ADDT20.SingleOrDefault(p => p.BATCH_CASH_ID == BATCH_CASH_ID && p.NUM == NUM);

            if (aDDT20 == null) return HttpNotFound();

            return View(aDDT20);
        }
        public ActionResult Delete1(string USER_NO)
        {
            if (string.IsNullOrEmpty(USER_NO))
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            this.Shared();
            AWAT01_LOG aWAT01_LOGtemp = new AWAT01_LOG();

            AWAT01 delAWAT01 = EntitiesDb.AWAT01.SingleOrDefault(p => p.SCHOOL_NO == user.SCHOOL_NO && p.USER_NO == USER_NO);
            aWAT01_LOGtemp = EntitiesDb.AWAT01_LOG.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.USER_NO == USER_NO && p.LOG_DESC == "酷幣匯轉區域管理" && p.SOURCE_NO == USER_NO).FirstOrDefault();
            if (aWAT01_LOGtemp == null) return HttpNotFound();

            return View(aWAT01_LOGtemp);
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirm(string BATCH_CASH_ID, int NUM)
        {
            string BATCH_ID = PushService.CreBATCH_ID();
            AWAT01_LOG aWAT01_LOGtemp = new AWAT01_LOG();
            ADDT38 aDDT38TEMP = new ADDT38();
            ADDT20 delADDT20 = EntitiesDb.ADDT20.SingleOrDefault(p => p.BATCH_CASH_ID == BATCH_CASH_ID && p.NUM == NUM);
           
            if ( delADDT20.CHG_PERSON == "系統紙本酷幣點數")
            {


                aWAT01_LOGtemp = EntitiesDb.AWAT01_LOG.Where(x => x.SCHOOL_NO == delADDT20.SCHOOL_NO && x.USER_NO == delADDT20.USER_NO && x.SOURCE_NO == BATCH_CASH_ID).FirstOrDefault();
                if (aWAT01_LOGtemp != null)
                {

                    aDDT38TEMP = EntitiesDb.ADDT38.Where(x=>x.SCHOOL_NO == delADDT20.SCHOOL_NO && x.USER_NO == delADDT20.USER_NO &&x.BATCH_CASH_ID == BATCH_CASH_ID).FirstOrDefault();

                    aDDT38TEMP.IsDel = "y";
                }
            }
            
            delADDT20.APPLY_STATUS = "9";
            EntitiesDb.Entry(delADDT20).State = System.Data.Entity.EntityState.Modified;

            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            try
            {
                ECOOL_APP.CashHelper.AddCash(user, -(Convert.ToInt32(delADDT20.CASH)), delADDT20.SCHOOL_NO, delADDT20.USER_NO, "ADDI09", delADDT20.BATCH_CASH_ID.ToString(), "特殊加扣點-作廢", true, ref EntitiesDb, "", "",ref valuesList);
                EntitiesDb.SaveChanges();

                string BODY_TXT = "特殊加扣點被作廢，具體事蹟: " + delADDT20.CONTENT_TXT + "-、減少酷幣點數" + (delADDT20.CASH).ToString() + "數";
                // Push到訊息Log，可供App查詢訊息
                PushService.InsertPushDataMe(BATCH_ID, delADDT20.SCHOOL_NO, delADDT20.USER_NO, "", BODY_TXT, "", "ADDI09", "Delete", delADDT20.BATCH_CASH_ID.ToString(), "", false, ref EntitiesDb);

                TempData["StatusMessage"] = $"{delADDT20.NAME}-{delADDT20.CONTENT_TXT} 刪除成功";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = $"{delADDT20.NAME}-{delADDT20.CONTENT_TXT} 異動失敗, 系統錯誤原因:" + exceptionMessage;
            }

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            return RedirectToAction("ListView");
        }
        public bool Save1(ADDI09EditViewModel Data, out string OutBATCH_CASH_ID) {

            if (Data.CASH > 50)
            {
                ErrorMsg = ErrorMsg + "「批次給老師點數最多50點\r\n";
                ModelState.AddModelError("CASH", "*批次給老師點數最多50點");
            }

            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
           

            List<HRMT01> H01List = EntitiesDb.HRMT01.Where(A => A.SCHOOL_NO == SCHOOL_NO).ToList();

            if (Data.Individual_Give)
            {
                foreach (var data in Data.DataList)
                {
                    if (data.TempCash > 1000 || data.TempCash < -1000)
                    {
                        ErrorMsg = ErrorMsg + "「獎懲點數」不可超過+-1000\r\n";
                        ModelState.AddModelError("CASH", "*獎懲點數不可超過+-1000");
                        break;
                    }
                }
            }
            else
            {
                if (Data.CASH > 1000 || Data.CASH < -1000)
                {
                    ErrorMsg = ErrorMsg + "「獎懲點數」不可超過+-1000\r\n";
                    ModelState.AddModelError("CASH", "*獎懲點數不可超過+-1000");
                }
            }
            //BATCH_CASH_ID = SCHOOL_NO +年 月 日+3 碼流水號
            string BATCH_CASH_ID = GetNewBatchCashId(Data.SYS_TABLE_TYPE);

            bool OK = false;
            //校內
            foreach (var Info in Data.DataList)
            {
                string LogDesc = "擔任"+Info.PersonType +" , "+ Info.TABLType;
                ADDI09EditViewModel DataItem = new ADDI09EditViewModel();
                DataItem.DataList = new List<ADDI09SelectListViewModel>();
                DataItem.DataList.Add(Info);
                DataItem.CASH = Info.Cash;
                DataItem.SUBJECT= "幹部加點";

                DataItem.CONTENT_TXT = LogDesc;
                DataItem.SYS_TABLE_TYPE = Data.SYS_TABLE_TYPE;
                OK = InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, LogDesc, H01List, DataItem);
            }
            if (OK == false)
            {
                OutBATCH_CASH_ID = null;
                return false;
            }

            OutBATCH_CASH_ID = BATCH_CASH_ID;
            return true;

        }
        #region 給點資料處理

        private bool Save(ADDI09EditViewModel Data, HttpPostedFileBase files, out string OutBATCH_CASH_ID)
        {
            string sourceExists = string.Empty;

            if (files != null && files.ContentLength > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("QuFIle", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                }
            }

            /* 老師給點 */
            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
            {
                // 個別給點
                if (Data.Individual_Give)
                {
                    foreach (var data in Data.DataList)
                    {
                        if (data.TempCash > 50)
                        {
                            ErrorMsg = ErrorMsg + "「批次給老師點數最多50點\r\n";
                            ModelState.AddModelError("CASH", "*批次給老師點數最多50點");
                            break;
                        }
                    }
                }
                else
                {
                    if (Data.CASH > 50)
                    {
                        ErrorMsg = ErrorMsg + "「批次給老師點數最多50點\r\n";
                        ModelState.AddModelError("CASH", "*批次給老師點數最多50點");
                    }
                }
            }

            /* 好運給點 */
            if (Data.SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY)
            {
                // 快速大量加點不須檢查0
                if (Data.SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    if (Data.Individual_Give)
                    {
                        foreach (var data in Data.DataList)
                        {
                            if (data.TempCash == 0)
                            {
                                ErrorMsg = ErrorMsg + "「獎懲點數」請輸入大於小於0的數字\r\n";
                                ModelState.AddModelError("CASH", "*請輸入獎懲點數");
                                break;
                            }
                        }
                    }
                    else
                    {
                        if (Data.CASH == 0)
                        {
                            ErrorMsg = ErrorMsg + "「獎懲點數」請輸入大於小於0的數字\r\n";
                            ModelState.AddModelError("CASH", "*請輸入獎懲點數");
                        }
                    }
                }
                ModelState.Remove(nameof(ADDI09EditViewModel.LuckPointCount));
            }

            if (Data.Individual_Give)
            {
                foreach (var data in Data.DataList)
                {
                    if (data.TempCash > 1000 || data.TempCash < -1000)
                    {
                        ErrorMsg = ErrorMsg + "「獎懲點數」不可超過+-1000\r\n";
                        ModelState.AddModelError("CASH", "*獎懲點數不可超過+-1000");
                        break;
                    }
                }
            }
            else
            {
                if (Data.CASH > 1000 || Data.CASH < -1000)
                {
                    ErrorMsg = ErrorMsg + "「獎懲點數」不可超過+-1000\r\n";
                    ModelState.AddModelError("CASH", "*獎懲點數不可超過+-1000");
                }
            }

            if (Data.DataList.Count() == 0)
            {
                ErrorMsg = ErrorMsg + "未選擇任何「人員清單」\r\n";
                ModelState.AddModelError("DataList", "*未選擇「人員清單」");
            }

            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH)
            {
                if (string.IsNullOrWhiteSpace(Data.CONTENT_TXT) == false)
                {
                    if (Data.CONTENT_TXT.Length > 30)
                    {
                        ErrorMsg = ErrorMsg + "「具體事蹟」輸入的長度不可以「多於30個字」\r\n";
                        ModelState.AddModelError("CONTENT_TXT", "「具體事蹟」輸入的長度不可以「多於30個字」");
                    }
                }
                if (string.IsNullOrWhiteSpace(Data.MEMO) == false)
                {
                    if (Data.MEMO.Length > 50)
                    {
                        ErrorMsg = ErrorMsg + "「備註」輸入的長度不可以「多於50個字」\r\n";
                        ModelState.AddModelError("MEMO", "「備註」輸入的長度不可以「多於50個字」");
                    }
                }
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                if (string.IsNullOrWhiteSpace(Data.SUBJECT) == false)
                {
                    if (Data.SUBJECT.Length > 30)
                    {
                        ErrorMsg = ErrorMsg + "「優良表現」輸入的長度不可大於30碼\r\n";
                        ModelState.AddModelError("SUBJECT", "「優良表現」輸入的長度不可大於30碼");
                    }
                }
                if (string.IsNullOrWhiteSpace(Data.MEMO) == false)
                {
                    if (Data.MEMO.Length > 50)
                    {
                        ErrorMsg = ErrorMsg + "「備註」輸入的長度不可大於50碼\r\n";
                        ModelState.AddModelError("MEMO", "「備註」輸入的長度不可大於50碼");
                    }
                }
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
            {
                if (string.IsNullOrWhiteSpace(Data.SUBJECT) == false)
                {
                    if (Data.SUBJECT.Length > 200)
                    {
                        ErrorMsg = ErrorMsg + "「獎懲主旨」輸入的長度不可大於200碼\r\n";
                        ModelState.AddModelError("SUBJECT", "「獎懲主旨」輸入的長度不可大於200碼");
                    }
                }
                if (string.IsNullOrWhiteSpace(Data.MEMO) == false)
                {
                    if (Data.MEMO.Length > 255)
                    {
                        ErrorMsg = ErrorMsg + "「備註」輸入的長度不可大於255碼\r\n";
                        ModelState.AddModelError("MEMO", "「備註」輸入的長度不可大於255碼");
                    }
                }
            }

            //BATCH_CASH_ID = SCHOOL_NO +年 月 日+3 碼流水號
            string BATCH_CASH_ID = GetNewBatchCashId(Data.SYS_TABLE_TYPE);

            if (ModelState.IsValid)
            {
                if (files != null && files.ContentLength >= 0)
                {
                    sourceExists = Path.Combine(new FileHelper().GetMapPathImg(SCHOOL_NO, "TempIMG"), BATCH_CASH_ID);

                    if (FileHelper.DoLoadFile(files, sourceExists, ref ErrorMsg, true) == false)
                    {
                        ModelState.AddModelError("QuFIle", ErrorMsg);
                        OutBATCH_CASH_ID = null;
                        return false;
                    }

                    Data.IMG_FILE = Path.GetFileName(files.FileName);
                }
            }

            if (ModelState.IsValid == false) { ErrorMsg = "錯誤\r\n\r\n" + ErrorMsg; OutBATCH_CASH_ID = null; return false; }

            List<HRMT01> H01List = EntitiesDb.HRMT01.Where(A => A.SCHOOL_NO == SCHOOL_NO).ToList();

            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            string LogDesc = "批次" + ADDI09EditViewModel.GetSysTableTypeString(Data.SYS_TABLE_TYPE, Data.ADDT14_STYLE, Data.SUBJECT);

            bool OK = false;

            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
            {
                //其它
                OK = InsertADDT20(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, LogDesc, H01List, Data);

                if (files != null && files.ContentLength >= 0 && OK == true)
                {
                    var BatchList = EntitiesDb.ADDT20.Where(A => A.SCHOOL_NO == SCHOOL_NO && A.BATCH_CASH_ID == BATCH_CASH_ID).ToList();

                    string sourceFile = Path.Combine(sourceExists, Data.IMG_FILE);

                    foreach (var item in BatchList)
                    {
                        string destExists = Path.Combine(Path.Combine(new FileHelper().GetMapPathImg(SCHOOL_NO, ImgPath), item.BATCH_CASH_ID), item.USER_NO);
                        if (!System.IO.Directory.Exists(destExists))
                        {
                            System.IO.Directory.CreateDirectory(destExists);
                        }

                        string destFile = Path.Combine(destExists, Data.IMG_FILE);
                        System.IO.File.Copy(sourceFile, destFile, true);
                    }

                    System.IO.Directory.Delete(sourceExists, true);
                }
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
            {
                //校內
                OK = InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, LogDesc, H01List, Data);

                if (files != null && files.ContentLength >= 0 && OK == true)
                {
                    var BatchList = EntitiesDb.ADDT14.Where(A => A.SCHOOL_NO == SCHOOL_NO && A.BATCH_CASH_ID == BATCH_CASH_ID).ToList();

                    string sourceFile = Path.Combine(sourceExists, Data.IMG_FILE);

                    foreach (var item in BatchList)
                    {
                        string destExists = Path.Combine(new FileHelper().GetMapPathImg(SCHOOL_NO, ADDI06Controller.ImgPath), item.IAWARD_ID.ToString());
                        if (!System.IO.Directory.Exists(destExists))
                        {
                            System.IO.Directory.CreateDirectory(destExists);
                        }

                        string destFile = Path.Combine(destExists, Data.IMG_FILE);
                        System.IO.File.Copy(sourceFile, destFile, true);
                    }

                    System.IO.Directory.Delete(sourceExists, true);
                }
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                //校外
                OK = InsertADDT15(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, LogDesc, H01List, Data);

                if (files != null && files.ContentLength >= 0 && OK == true)
                {
                    var BatchList = EntitiesDb.ADDT15.Where(A => A.SCHOOL_NO == SCHOOL_NO && A.BATCH_CASH_ID == BATCH_CASH_ID).ToList();

                    string sourceFile = Path.Combine(sourceExists, Data.IMG_FILE);

                    foreach (var item in BatchList)
                    {
                        string destExists = Path.Combine(new FileHelper().GetMapPathImg(SCHOOL_NO, ADDI07Controller.ImgPath), item.OAWARD_ID.ToString());
                        if (!System.IO.Directory.Exists(destExists))
                        {
                            System.IO.Directory.CreateDirectory(destExists);
                        }

                        string destFile = Path.Combine(destExists, Data.IMG_FILE);
                        System.IO.File.Copy(sourceFile, destFile, true);
                    }

                    System.IO.Directory.Delete(sourceExists, true);
                }
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
            {
                string source_no = BATCH_CASH_ID;
                // 老師批次給點
                foreach (var teacher in Data.DataList)
                {
                    CashHelper.TeachAddCash(user, Data.Individual_Give ? teacher.TempCash : Data.CASH, SCHOOL_NO, teacher.USER_NO, "ADDI09", source_no
                        , "批次加扣點 - " + Data.SUBJECT, true, null, ref EntitiesDb);
                }
                EntitiesDb.SaveChanges();
                OK = true;
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY)
            {
                string source_no = BATCH_CASH_ID;
                LuckCollectPointRepository luckRepo = new LuckCollectPointRepository();
                // 學生批次給點
                foreach (var stu in Data.DataList)
                {
                    // LuckPointCount 目前抽1次100點數
                    luckRepo.GiveWORKHARD_ToStu_InMemory(user.USER_KEY, (Data.Individual_Give ? stu.TempCash : Data.LuckPointCount) * 100, SCHOOL_NO, stu.USER_NO, "ADDI09", source_no
                        , "批次好運集點 - " + Data.SUBJECT);
                }

                luckRepo.GiveBatchInsert();
                OK = true;
            }

            if (OK == false)
            {
                OutBATCH_CASH_ID = null;
                return false;
            }

            OutBATCH_CASH_ID = BATCH_CASH_ID;
            return true;
        }

        #endregion 給點資料處理

        #region 處理ADDT14 (校內)

        /// <summary>
        /// 處理ADDT14 (校內)
        /// </summary>
        /// <param name="BATCH_CASH_ID"></param>
        /// <param name="SYear"></param>
        /// <param name="Semesters"></param>
        /// <param name="H01List"></param>
        /// <param name="Data"></param>
        private bool InsertADDT14(string BATCH_CASH_ID, byte SYear, byte Semesters, string LogDesc, List<HRMT01> H01List, ADDI09EditViewModel Data)
        {
            bool ReturnVal = false;
            string BATCH_ID = PushService.CreBATCH_ID();
            List<APPT02> T02List = new List<APPT02>();
            List<ADDT14> ADDT14AddData = new List<ADDT14>();
            int NUM = 0;

            foreach (var item in Data.DataList)
            {
                HRMT01 H01 = H01List.Where(A => A.SCHOOL_NO == item.SCHOOL_NO && A.USER_NO == item.USER_NO).FirstOrDefault();
                if (H01 != null)
                {
                    NUM++;

                    ADDT14 T14 = new ADDT14();
                    T14.BATCH_CASH_ID = BATCH_CASH_ID;
                    T14.SCHOOL_NO = H01.SCHOOL_NO;
                    T14.CREATEDATE = DateTime.Now;
                    T14.SYEAR = (byte)SYear;
                    T14.SEMESTER = (byte)Semesters;
                    T14.TNAME = user.NAME;
                    T14.CLASS_NO = H01.CLASS_NO;
                    T14.USER_NO = H01.USER_NO;
                    T14.USERNAME = H01.NAME;
                    T14.SNAME = H01.SNAME;
                    T14.IAWARD_KIND = Data.SUBJECT;
                    T14.IAWARD_ITEM = Data.CONTENT_TXT;
                    T14.CASH = Data.Individual_Give ? item.TempCash : Data.CASH;
                    T14.REMARK = Data.MEMO;
                    T14.APPLY_STATUS = "2";
                    T14.IMG_FILE = Data.IMG_FILE;

                    if (T14.CASH != 0)
                    {
                        ADDT14AddData.Add(T14);

                        GreADDT14PushData(BATCH_ID, T14, ref EntitiesDb, T02List);
                    }
                }
            }

            LogDesc = StringHelper.LeftStringR(LogDesc + '-' + Data.SUBJECT??"" + '-' + Data.CONTENT_TXT, 47);

            string TableName = Data.SYS_TABLE_TYPE;
            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH
                || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                TableName = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN;

            ReturnVal = ADDT14AddData.BatchDatAddCasha(Bre_NO, BATCH_CASH_ID, TableName, LogDesc, Session.SessionID, "ADDT14", user, out ErrorMsg, T02List, null,"");
            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;
        }

        public void GreADDT14PushData(string BATCH_ID, ADDT14 aDDT14, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            string BODY_TXT = "校內表現，獎懲類別:" + aDDT14.IAWARD_KIND + "，具體事蹟:" + aDDT14.IAWARD_ITEM + "，獎勵點數:" + aDDT14.CASH.ToString();

            PushService.InsertPushDataParents(BATCH_ID, aDDT14.SCHOOL_NO, aDDT14.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT14", aDDT14.IAWARD_ID.ToString(), "", false, ref db, T02List);
            PushService.InsertPushDataMe(BATCH_ID, aDDT14.SCHOOL_NO, aDDT14.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT14", aDDT14.IAWARD_ID.ToString(), "", false, ref db, T02List);
        }

        #endregion 處理ADDT14 (校內)

        #region 處理ADDT15 (校外)

        /// <summary>
        /// 處理ADDT15 (校外)
        /// </summary>
        /// <param name="BATCH_CASH_ID"></param>
        /// <param name="SYear"></param>
        /// <param name="Semesters"></param>
        /// <param name="H01List"></param>
        /// <param name="Data"></param>
        private bool InsertADDT15(string BATCH_CASH_ID, byte SYear, byte Semesters, string LogDesc, List<HRMT01> H01List, ADDI09EditViewModel Data)
        {
            bool ReturnVal = false;
            string BATCH_ID = PushService.CreBATCH_ID();
            List<APPT02> T02List = new List<APPT02>();
            List<ADDT15> ADDT15AddData = new List<ADDT15>();
            int NUM = 0;

            foreach (var item in Data.DataList)
            {
                HRMT01 H01 = H01List.Where(A => A.SCHOOL_NO == item.SCHOOL_NO && A.USER_NO == item.USER_NO).FirstOrDefault();
                if (H01 != null)
                {
                    NUM++;

                    ADDT15 T15 = new ADDT15();
                    T15.BATCH_CASH_ID = BATCH_CASH_ID;
                    T15.SCHOOL_NO = H01.SCHOOL_NO;
                    T15.CREATEDATE = DateTime.Now;
                    T15.SYEAR = (byte)SYear;
                    T15.SEMESTER = (byte)Semesters;
                    T15.TNAME = user.NAME;
                    T15.CLASS_NO = H01.CLASS_NO;
                    T15.USER_NO = H01.USER_NO;
                    T15.USERNAME = H01.NAME;
                    T15.SNAME = H01.SNAME;
                    T15.OAWARD_ITEM = Data.SUBJECT;
                    T15.OAWARD_SCORE = Data.CONTENT_TXT;
                    T15.CASH = Data.Individual_Give ? item.TempCash : Data.CASH;
                    T15.REMARK = Data.MEMO;
                    T15.APPLY_STATUS = "2";
                    T15.IMG_FILE = Data.IMG_FILE;
                    ADDT15AddData.Add(T15);

                    GreADDT15PushData(BATCH_ID, T15, ref EntitiesDb, T02List);
                }
            }

            LogDesc = StringHelper.LeftStringR(LogDesc + '-' + Data.SUBJECT + ' ' + Data.CONTENT_TXT, 47);

            ReturnVal = ADDT15AddData.BatchDatAddCasha(Bre_NO, BATCH_CASH_ID, Data.SYS_TABLE_TYPE, LogDesc, Session.SessionID, "ADDT15", user, out ErrorMsg, T02List, null,"");
            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;
        }

        public void GreADDT15PushData(string BATCH_ID, ADDT15 aDDT15, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            string BODY_TXT = "校外榮譽，優良表現:" + aDDT15.OAWARD_ITEM + "，成績:" + aDDT15.OAWARD_SCORE + "，獎勵點數:" + aDDT15.CASH.ToString();

            PushService.InsertPushDataParents(BATCH_ID, aDDT15.SCHOOL_NO, aDDT15.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT15", aDDT15.OAWARD_ID.ToString(), "", false, ref db, T02List);
            PushService.InsertPushDataMe(BATCH_ID, aDDT15.SCHOOL_NO, aDDT15.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT15", aDDT15.OAWARD_ID.ToString(), "", false, ref db, T02List);
        }

        #endregion 處理ADDT15 (校外)

        #region 處理ADDT20 (其他)

        /// <summary>
        /// 處理ADDT20 (其他)
        /// </summary>
        /// <param name="BATCH_CASH_ID"></param>
        /// <param name="SYear"></param>
        /// <param name="Semesters"></param>
        /// <param name="H01List"></param>
        /// <param name="Data"></param>
        private bool InsertADDT20(string BATCH_CASH_ID, byte SYear, byte Semesters, string LogDesc, List<HRMT01> H01List, ADDI09EditViewModel Data)
        {
            bool ReturnVal = false;
            string BATCH_ID = PushService.CreBATCH_ID();
            List<APPT02> T02List = new List<APPT02>();
            List<ADDT20> ADDT20AddData = new List<ADDT20>();
            int NUM = 0;
            string MaxString = (from u in EntitiesDb.ADDT20
                         where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID.Contains(BATCH_CASH_ID)
                         orderby u.BATCH_CASH_ID descending
                         select u.BATCH_CASH_ID).FirstOrDefault();
            if (MaxString != null) {

                BATCH_CASH_ID = GetNewBatchCashId(Data.SYS_TABLE_TYPE);


            }
            foreach (var item in Data.DataList)
            {
                HRMT01 H01 = H01List.Where(A => A.SCHOOL_NO == item.SCHOOL_NO && A.USER_NO == item.USER_NO).FirstOrDefault();

                if (H01 != null)
                {
                    NUM++;

                    ADDT20 T20 = new ADDT20();
                    T20.BATCH_CASH_ID = BATCH_CASH_ID;
                    T20.SCHOOL_NO = H01.SCHOOL_NO;
                    T20.USER_NO = H01.USER_NO;
                    T20.CLASS_NO = H01.CLASS_NO;
                    T20.SYEAR = (byte)SYear;
                    T20.SEMESTER = (byte)Semesters;
                    T20.SEAT_NO = H01.SEAT_NO;
                    T20.NAME = H01.NAME;
                    T20.SNAME = H01.SNAME;
                    T20.CRE_PERSON = USER_KEY;
                    T20.CRE_DATE = DateTime.Today;
                    T20.CHG_PERSON = USER_KEY;
                    T20.CHG_DATE = DateTime.Now;
                    T20.SUBJECT = Data.SUBJECT;
                    T20.CONTENT_TXT = Data.CONTENT_TXT;
                    T20.MEMO = Data.MEMO;
                    T20.CASH = Data.Individual_Give ? item.TempCash : Data.CASH;
                    T20.STATUS = "R";
                    T20.NUM = NUM;
                    T20.IMG_FILE = Data.IMG_FILE;
                    T20.APPLY_STATUS = "2";
                    ADDT20AddData.Add(T20);

                    GreADDT20PushData(BATCH_ID, T20, ref EntitiesDb, T02List);
                }
            }

            LogDesc = StringHelper.LeftStringR(LogDesc + '-' + Data.CONTENT_TXT, 47);

            string TableName = Data.SYS_TABLE_TYPE;
            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                TableName = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER;
            ReturnVal = ADDT20AddData.BatchDatAddCasha(Bre_NO, BATCH_CASH_ID, TableName, LogDesc, Session.SessionID, "ADDT20", user, out ErrorMsg, T02List, null,"");
            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;
        }

        public void GreADDT20PushData(string BATCH_ID, ADDT20 aDDT20, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            string BODY_TXT = "特殊加扣點，獎懲主旨:" + aDDT20.SUBJECT + "，獎懲內容:" + aDDT20.CONTENT_TXT + "，獎勵點數:" + aDDT20.CASH.ToString();

            PushService.InsertPushDataParents(BATCH_ID, aDDT20.SCHOOL_NO, aDDT20.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT20", aDDT20.BATCH_CASH_ID, "", false, ref db, T02List);
            PushService.InsertPushDataMe(BATCH_ID, aDDT20.SCHOOL_NO, aDDT20.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT20", aDDT20.BATCH_CASH_ID, "", false, ref db, T02List);
        }

        #endregion 處理ADDT20 (其他)

        #region Excel 產生批次流水號
        private string ExtractNumericValue(string input)
        {
            // 使用正則表達式提取字符串中的數字
            string numericValue = "0";
            System.Text.RegularExpressions.Regex rex =
       new System.Text.RegularExpressions.Regex(@"^\d+$");

            if (rex.IsMatch(input))
            {
                numericValue = input;

            }
            else
            {

                numericValue = "-1";
            }
            return numericValue;
        }
        /// <summary>
        /// 產生批次流水號
        /// </summary>
        /// <param name="SYS_TABLE_TYPE"></param>
        /// <returns></returns>
        private string GetNewBatchCashId(string SYS_TABLE_TYPE)
        {
            string MaxString = string.Empty;
            string ReturnVal = string.Empty;
            string[] sub = new string[2];

            if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH) //其他
            {
                string str = "";
                str = SCHOOL_NO + DateTime.Now.Year + DateTime.Now.Month.ToString("00") + DateTime.Now.Day.ToString("00");

                MaxString = (from u in EntitiesDb.ADDT20
                             where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID.Contains(str)
                             let batchCashIdWithoutPrefix = u.BATCH_CASH_ID
                             orderby batchCashIdWithoutPrefix descending
                             select u.BATCH_CASH_ID).FirstOrDefault();
                if (!string.IsNullOrEmpty(MaxString)) {
                    int MaxStringInt = 0;
                    MaxStringInt = (from u in EntitiesDb.ADDT20
                                 where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID.Contains(str)
                                 let batchCashIdWithoutPrefix = u.BATCH_CASH_ID.Replace(str, "")
                                 orderby batchCashIdWithoutPrefix descending
                                 select batchCashIdWithoutPrefix).AsEnumerable() // Switch to LINQ to Objects
                           
                                 .Select(item => int.Parse(item))
                                .OrderByDescending(item => item)
                                .FirstOrDefault();

                    MaxString = str + MaxStringInt.ToString();
                    sub = MaxString.Split(new string[] { str }, StringSplitOptions.None);
                }

               
            }
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK) //校內
            {
                string str = "";
                str = SCHOOL_NO + DateTime.Now.Year + DateTime.Now.Month.ToString("00") + DateTime.Now.Day.ToString("00");
                MaxString = (from u in EntitiesDb.ADDT14
                             where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID.Contains(str)
                             orderby u.BATCH_CASH_ID descending
                             select u.BATCH_CASH_ID).FirstOrDefault();
                if (!string.IsNullOrEmpty(MaxString))
                {
                    int MaxStringInt = 0;
                    MaxStringInt = (from u in EntitiesDb.ADDT14
                                    where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID.Contains(str)
                                    let batchCashIdWithoutPrefix = u.BATCH_CASH_ID.Replace(str, "")
                                    orderby batchCashIdWithoutPrefix descending
                                    select batchCashIdWithoutPrefix).AsEnumerable() // Switch to LINQ to Objects

                                 .Select(item => int.Parse(item))
                                .OrderByDescending(item => item)
                                .FirstOrDefault();

                    MaxString = str + MaxStringInt.ToString();
                    sub = MaxString.Split(new string[] { str }, StringSplitOptions.None);
                }
            
            }
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC) //校外
            {
                MaxString = (from u in EntitiesDb.ADDT15
                             where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID != null
                             select u.BATCH_CASH_ID).AsEnumerable()
                           .OrderByDescending(u => ExtractNumericValue(u))
                                   .FirstOrDefault();
            }
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG
                || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY) //老師
            {
                return SCHOOL_NO + DateTime.Now.ToString("yyyyMMddhhmmss");
            }

            if (!string.IsNullOrEmpty(MaxString))
            {

                //if (new StringHelper().StrRigth("00" + (Convert.ToInt16(new StringHelper().StrRigth(MaxString, 3)) ).ToString(), 3) == "999"){


                //}

                if (!string.IsNullOrEmpty(sub[1]))
                {
                    ReturnVal = SCHOOL_NO + DateTime.Now.ToString("yyyyMMdd") + (Convert.ToInt16(sub[1]) + 1).ToString();

                }
                else {


                    ReturnVal = SCHOOL_NO + DateTime.Now.ToString("yyyyMMdd") + new StringHelper().StrRigth("00" + (Convert.ToInt16(new StringHelper().StrRigth(MaxString, 3)) + 1).ToString(), 3);
                }
            }
            else
            {
                ReturnVal = SCHOOL_NO + DateTime.Now.ToString("yyyyMMdd") + "001";
            }

            return ReturnVal;
        }

        #endregion Excel 產生批次流水號

        #region Excel 匯入處理

        /// <summary>
        /// Excel 匯入處理
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        private bool ExcelData(HttpPostedFileBase files, string SYS_TABLE_TYPE, string ADDT14_STYLE, ref string OutBATCH_CASH_ID)
        {
            bool ReturnBool = true;

            user = UserProfileHelper.Get();

            NPOIHelper npoi = new NPOIHelper(); //NEW NPOIHelper

            npoi.onDataTypeConflict += new DataRowCellHandler(this.NPOI_DataTypeConflict); //宣告使用 輸入內容的型態 是否設定 及 Excel存儲格式一樣 ex. 日期 輸入[aaa] ，Excel存儲格式設日期，或欄位第一行有定義 System.DateTime
            npoi.onLineCheckValue += new DataRowCellHandler(this.NPOI_LineCheckValue);

            ArraySheetNames = new string[] { "學號", "座號" }; //Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)
            STR_SYS_TABLE_TYPE = SYS_TABLE_TYPE;

            if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
            {
                MustArray = new string[] { "USER_NO", "IAWARD_KIND", "IAWARD_ITEM", "CASH" }; // Excel 必輸欄位
                MustArrayTwo = new string[] { "CLASS_NO", "SEAT_NO", "IAWARD_KIND", "IAWARD_ITEM", "CASH" }; // Excel 必輸欄位
            }
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                MustArray = new string[] { "USER_NO", "OAWARD_ITEM", "OAWARD_SCORE", "CASH" }; // Excel 必輸欄位
                MustArrayTwo = new string[] { "CLASS_NO", "SEAT_NO", "OAWARD_ITEM", "OAWARD_SCORE", "CASH" }; // Excel 必輸欄位
            }
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
            {
                MustArray = new string[] { "USER_NO", "SUBJECT", "CONTENT_TXT", "CASH" }; // Excel 必輸欄位
                MustArrayTwo = new string[] { "CLASS_NO", "SEAT_NO", "SUBJECT", "CONTENT_TXT", "CASH" }; // Excel 必輸欄位
            }

            string _Error;

            try
            {
                DataSet ds = npoi.Excel2Table(files.InputStream, 0, 2, 1, ArraySheetNames);

                if (ds == null)
                {
                    _Error = "上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                ///讀取資料筆數為0
                if (ds.Tables.Count == 0)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br>讀取資料筆數為0，請確認 上傳Excel的 Sheet Name 是否有以下 ";
                    foreach (string ThisSheet in ArraySheetNames)
                    {
                        _Error = _Error + "【" + ThisSheet.ToString() + "】";
                    }
                    _Error = _Error + " Sheet Name ，請確認";

                    TempData["StatusMessage"] = _Error;

                    ReturnBool = false;
                    return ReturnBool;
                }

                ///EXCEL內容資料型態數誤
                if (_CheckDataTypeErr || _CheckMustErr)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> " + _ErrorRowCellExcel;
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                DataTable dtUSER_NO = ds.Tables[ArraySheetNames[0]];
                DataTable dtSEAT_NO = ds.Tables[ArraySheetNames[1]];
                var Ht01 = EntitiesDb.HRMT01.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.USER_TYPE == UserType.Student && (p.USER_STATUS != UserStaus.Invalid)).ToList();

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                string LogDesc = "批次" + ADDI09EditViewModel.GetSysTableTypeString(SYS_TABLE_TYPE, ADDT14_STYLE);
                string BATCH_CASH_ID = GetNewBatchCashId(SYS_TABLE_TYPE);

                string BATCH_ID = PushService.CreBATCH_ID();
                List<APPT02> T02List = new List<APPT02>();
                List<ADDT14> ADDT14AddData = new List<ADDT14>();
                List<ADDT15> ADDT15AddData = new List<ADDT15>();
                List<ADDT20> ADDT20AddData = new List<ADDT20>();

                int NUM = 0;
                foreach (DataRow Row in dtUSER_NO.Rows)
                {
                    NUM = NUM + 1;
                    string ThisUSER_NO = (Row["USER_NO"] == DBNull.Value ? "" : (string)Row["USER_NO"]);

                    if (!string.IsNullOrWhiteSpace(ThisUSER_NO))
                    {
                        var ThisHt01 = Ht01.Where(a => a.USER_NO == ThisUSER_NO).FirstOrDefault();

                        if (ThisHt01 != null)
                        {
                            if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
                            {
                                ADDT14 Cre = new ADDT14()
                                {
                                    SCHOOL_NO = ThisHt01.SCHOOL_NO,
                                    CREATEDATE = DateTime.Now,
                                    SYEAR = (short)SYear,
                                    SEMESTER = (short)Semesters,
                                    TNAME = user.NAME,
                                    SNAME = ThisHt01.SNAME,
                                    CLASS_NO = ThisHt01.CLASS_NO,
                                    USER_NO = ThisHt01.USER_NO,
                                    USERNAME = ThisHt01.NAME,
                                    SEX = ThisHt01.SEX,
                                    IAWARD_KIND = (Row["IAWARD_KIND"] == DBNull.Value ? "" : (string)Row["IAWARD_KIND"]),
                                    IAWARD_ITEM = (Row["IAWARD_ITEM"] == DBNull.Value ? "" : (string)Row["IAWARD_ITEM"]),
                                    CASH = (Row["CASH"] == DBNull.Value ? 0 : Convert.ToInt16(Row["CASH"].ToString())),
                                    REMARK = (Row["REMARK"] == DBNull.Value ? "" : (string)Row["REMARK"]),
                                    APPLY_STATUS = "2",
                                    BATCH_CASH_ID = BATCH_CASH_ID
                                };

                                GreADDT14PushData(BATCH_ID, Cre, ref EntitiesDb, T02List);
                                ADDT14AddData.Add(Cre);
                            }
                            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                            {
                                ADDT15 Cre = new ADDT15()
                                {
                                    SCHOOL_NO = ThisHt01.SCHOOL_NO,
                                    CREATEDATE = DateTime.Now,
                                    SYEAR = (short)SYear,
                                    SEMESTER = (short)Semesters,
                                    TNAME = user.NAME,
                                    SNAME = ThisHt01.SNAME,
                                    CLASS_NO = ThisHt01.CLASS_NO,
                                    USER_NO = ThisHt01.USER_NO,
                                    USERNAME = ThisHt01.NAME,
                                    SEX = ThisHt01.SEX,
                                    OAWARD_ITEM = (Row["OAWARD_ITEM"] == DBNull.Value ? "" : (string)Row["OAWARD_ITEM"]),
                                    OAWARD_SCORE = (Row["OAWARD_SCORE"] == DBNull.Value ? "" : (string)Row["OAWARD_SCORE"]),
                                    CASH = (Row["CASH"] == DBNull.Value ? 0 : Convert.ToInt16(Row["CASH"].ToString())),
                                    REMARK = (Row["REMARK"] == DBNull.Value ? "" : (string)Row["REMARK"]),
                                    APPLY_STATUS = "2",
                                    BATCH_CASH_ID = BATCH_CASH_ID
                                };
                                GreADDT15PushData(BATCH_ID, Cre, ref EntitiesDb, T02List);
                                ADDT15AddData.Add(Cre);
                            }
                            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
                            {
                                ADDT20 Cre = new ADDT20()
                                {
                                    BATCH_CASH_ID = BATCH_CASH_ID,
                                    SCHOOL_NO = ThisHt01.SCHOOL_NO,
                                    USER_NO = ThisHt01.USER_NO,
                                    CLASS_NO = ThisHt01.CLASS_NO,
                                    SYEAR = (short)SYear,
                                    SEMESTER = (byte)Semesters,
                                    SEAT_NO = ThisHt01.SEAT_NO,
                                    NAME = ThisHt01.NAME,
                                    SNAME = ThisHt01.SNAME,
                                    CRE_PERSON = user.USER_KEY,
                                    CRE_DATE = DateTime.Now,
                                    CHG_PERSON = user.USER_KEY,
                                    CHG_DATE = DateTime.Now,
                                    SUBJECT = (Row["SUBJECT"] == DBNull.Value ? "" : (string)Row["SUBJECT"]),
                                    CONTENT_TXT = (Row["CONTENT_TXT"] == DBNull.Value ? "" : (string)Row["CONTENT_TXT"]),
                                    MEMO = (Row["MEMO"] == DBNull.Value ? "" : (string)Row["MEMO"]),
                                    CASH = (Row["CASH"] == DBNull.Value ? 0 : Convert.ToInt16(Row["CASH"].ToString())),
                                    STATUS = "R",
                                    APPLY_STATUS = "2",
                                    NUM = NUM,
                                };

                                GreADDT20PushData(BATCH_ID, Cre, ref EntitiesDb, T02List);
                                ADDT20AddData.Add(Cre);
                            }
                        }
                        else
                        {
                            ErrorMsg = ErrorMsg + ThisUSER_NO + "<br/>";
                        }
                    }
                }

                if (ErrorMsg != "" && ErrorMsg != null)
                {
                    ErrorMsg = "以下「學號」不是有效值，請確認：<br><br>" + ErrorMsg;
                    TempData["StatusMessage"] = ErrorMsg;
                    ReturnBool = false;
                    return ReturnBool;
                }

                foreach (DataRow Row in dtSEAT_NO.Rows)
                {
                    string ThisCLASS_NO = (Row["CLASS_NO"] == DBNull.Value ? "" : (string)Row["CLASS_NO"]);
                    string ThisSEAT_NO = (Row["SEAT_NO"] == DBNull.Value ? "" : (string)Row["SEAT_NO"]);
                    List<string> Ok_SEAT_NO = new List<string>() { ThisSEAT_NO };
                    if (ThisSEAT_NO.Length == 1)
                    {
                        Ok_SEAT_NO.Add(string.Format("{0:00}", Convert.ToInt32(ThisSEAT_NO)));
                    }
                    NUM = NUM + 1;

                    if (!string.IsNullOrWhiteSpace(ThisCLASS_NO) && !string.IsNullOrWhiteSpace(ThisSEAT_NO))
                    {
                        var ThisHt01 = Ht01.Where(a => a.CLASS_NO == ThisCLASS_NO && Ok_SEAT_NO.Contains(a.SEAT_NO)).FirstOrDefault();

                        if (ThisHt01 != null)
                        {
                            if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
                            {
                                ADDT14 Cre = new ADDT14()
                                {
                                    SCHOOL_NO = ThisHt01.SCHOOL_NO,
                                    CREATEDATE = DateTime.Now,
                                    SYEAR = (short)SYear,
                                    SEMESTER = (short)Semesters,
                                    TNAME = user.NAME,
                                    SNAME = ThisHt01.SNAME,
                                    CLASS_NO = ThisHt01.CLASS_NO,
                                    USER_NO = ThisHt01.USER_NO,
                                    USERNAME = ThisHt01.NAME,
                                    SEX = ThisHt01.SEX,
                                    IAWARD_KIND = (Row["IAWARD_KIND"] == DBNull.Value ? "" : (string)Row["IAWARD_KIND"]),
                                    IAWARD_ITEM = (Row["IAWARD_ITEM"] == DBNull.Value ? "" : (string)Row["IAWARD_ITEM"]),
                                    CASH = (Row["CASH"] == DBNull.Value ? 0 : Convert.ToInt16(Row["CASH"].ToString())),
                                    REMARK = (Row["REMARK"] == DBNull.Value ? "" : (string)Row["REMARK"]),
                                    APPLY_STATUS = "2",
                                    BATCH_CASH_ID = BATCH_CASH_ID
                                };

                                GreADDT14PushData(BATCH_ID, Cre, ref EntitiesDb, T02List);
                                ADDT14AddData.Add(Cre);

                                var ADDT14Count = ADDT14AddData.GroupBy(x => new { x.CLASS_NO, x.SCHOOL_NO, x.USER_NO })
                                    .Select(group => new { count = group.Count(), USER_NO = group.Key.USER_NO });
                                List<string> UserNOBTTWO = new List<string>();
                                var returnerror = ADDT14Count.Where(x => x.count > 1).Any();
                                UserNOBTTWO = ADDT14Count.Where(x => x.count > 1).Select(x => x.USER_NO).ToList();
                                if (returnerror && UserNOBTTWO.Contains(ThisHt01.USER_NO))
                                {
                                    ErrorMsg = ErrorMsg + " " + ThisHt01.NAME + "--輸入的資料具有重複的學生<br/>";
                                }
                            }
                            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                            {
                                ADDT15 Cre = new ADDT15()
                                {
                                    SCHOOL_NO = ThisHt01.SCHOOL_NO,
                                    CREATEDATE = DateTime.Now,
                                    SYEAR = (short)SYear,
                                    SEMESTER = (short)Semesters,
                                    TNAME = user.NAME,
                                    SNAME = ThisHt01.SNAME,
                                    CLASS_NO = ThisHt01.CLASS_NO,
                                    USER_NO = ThisHt01.USER_NO,
                                    USERNAME = ThisHt01.NAME,
                                    SEX = ThisHt01.SEX,
                                    OAWARD_ITEM = (Row["OAWARD_ITEM"] == DBNull.Value ? "" : (string)Row["OAWARD_ITEM"]),
                                    OAWARD_SCORE = (Row["OAWARD_SCORE"] == DBNull.Value ? "" : (string)Row["OAWARD_SCORE"]),
                                    CASH = (Row["CASH"] == DBNull.Value ? 0 : Convert.ToInt16(Row["CASH"].ToString())),
                                    REMARK = (Row["REMARK"] == DBNull.Value ? "" : (string)Row["REMARK"]),
                                    APPLY_STATUS = "2",
                                    BATCH_CASH_ID = BATCH_CASH_ID
                                };

                                GreADDT15PushData(BATCH_ID, Cre, ref EntitiesDb, T02List);
                                ADDT15AddData.Add(Cre);
                            }
                            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
                            {
                                ADDT20 Cre = new ADDT20()
                                {
                                    BATCH_CASH_ID = BATCH_CASH_ID,
                                    SCHOOL_NO = ThisHt01.SCHOOL_NO,
                                    USER_NO = ThisHt01.USER_NO,
                                    CLASS_NO = ThisHt01.CLASS_NO,
                                    SYEAR = (short)SYear,
                                    SEMESTER = (byte)Semesters,
                                    SEAT_NO = ThisHt01.SEAT_NO,
                                    NAME = ThisHt01.NAME,
                                    SNAME = ThisHt01.SNAME,
                                    CRE_PERSON = user.USER_KEY,
                                    CRE_DATE = DateTime.Now,
                                    CHG_PERSON = user.USER_KEY,
                                    CHG_DATE = DateTime.Now,
                                    SUBJECT = (Row["SUBJECT"] == DBNull.Value ? "" : (string)Row["SUBJECT"]),
                                    CONTENT_TXT = (Row["CONTENT_TXT"] == DBNull.Value ? "" : (string)Row["CONTENT_TXT"]),
                                    MEMO = (Row["MEMO"] == DBNull.Value ? "" : (string)Row["MEMO"]),
                                    CASH = (Row["CASH"] == DBNull.Value ? 0 : Convert.ToInt16(Row["CASH"].ToString())),
                                    STATUS = "R",
                                    APPLY_STATUS = "2",
                                    NUM = NUM,
                                };

                                GreADDT20PushData(BATCH_ID, Cre, ref EntitiesDb, T02List);
                                ADDT20AddData.Add(Cre);
                            }
                        }
                        else
                        {
                            ErrorMsg = ErrorMsg + (string)Row["CLASS_NO"] + "班，座號：" + (string)Row["SEAT_NO"] + "帳號不存在，或未啟用<br/>";
                        }
                    }
                }

                if (ErrorMsg != "" && ErrorMsg != null)
                {
                    ErrorMsg = "本次上傳失敗，請修正後重新上傳。失敗原因為：<br><br>" + ErrorMsg;
                    TempData["StatusMessage"] = ErrorMsg;
                    ReturnBool = false;
                    return ReturnBool;
                }

                if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
                {
                    string USER_NO = ADDT14AddData.GroupBy(a => a.USER_NO).Where(g => g.Count() > 1).Select(g => g.Key).FirstOrDefault();
                    //if (string.IsNullOrEmpty(USER_NO))
                    //{
                    //沒有重複學號
                    ReturnBool = ADDT14AddData.BatchDatAddCasha(Bre_NO, BATCH_CASH_ID, SYS_TABLE_TYPE, LogDesc, Session.SessionID, "ADDT14", user, out ErrorMsg, T02List,null,"");
                    //}
                    //else
                    //{
                    //    ErrorMsg = "學號重複：" + USER_NO;
                    //}
                }
                else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    string USER_NO = ADDT15AddData.GroupBy(a => a.USER_NO).Where(g => g.Count() > 1).Select(g => g.Key).FirstOrDefault();
                    if (string.IsNullOrEmpty(USER_NO))
                    {
                        //沒有重複學號
                        ReturnBool = ADDT15AddData.BatchDatAddCasha(Bre_NO, BATCH_CASH_ID, SYS_TABLE_TYPE, LogDesc, Session.SessionID, "ADDT15", user, out ErrorMsg, T02List, null,"");
                    }
                    else
                    {
                        if (dtSEAT_NO.Rows.Count > 0)
                        {

                           var HRMT01Temp= EntitiesDb.HRMT01.Where(x => x.USER_NO == USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                            ErrorMsg = "以下名單重複：" + HRMT01Temp.CLASS_NO +"班，座號：" + HRMT01Temp.SEAT_NO;
                        }
                        else {
                            ErrorMsg = "以下名單重複學號：" + USER_NO;
                        }
                       
                    }
                }
                else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
                {
                    string USER_NO = ADDT20AddData.GroupBy(a => a.USER_NO).Where(g => g.Count() > 1).Select(g => g.Key).FirstOrDefault();
                    if (string.IsNullOrEmpty(USER_NO))
                    {
                        //沒有重複學號
                        string TableName = SYS_TABLE_TYPE;
                        if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS) TableName = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER;
                        ReturnBool = ADDT20AddData.BatchDatAddCasha(Bre_NO, BATCH_CASH_ID, TableName, LogDesc, Session.SessionID, "ADDT20", user, out ErrorMsg, T02List, null,"");
                    }
                    else
                    {
                        if (dtSEAT_NO.Rows.Count > 0)
                        {


                            var HRMT01Temp = EntitiesDb.HRMT01.Where(x => x.USER_NO == USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();

                            ErrorMsg = "以下名單重複：" + HRMT01Temp.CLASS_NO + "班，座號：" + HRMT01Temp.SEAT_NO;
                        }
                        else {

                            ErrorMsg = "以下名單重複學號：" + USER_NO;
                        }
                           
                    }
                }

                ErrorMsg = ErrorMsg + Db.ErrorMsg;

                if (ErrorMsg != "" && ErrorMsg != null)
                {
                    TempData["StatusMessage"] = ErrorMsg;
                    ReturnBool = false;
                    return ReturnBool;
                }

                OutBATCH_CASH_ID = BATCH_CASH_ID;

                PushHelper.ToPushServer(BATCH_ID);
            }
            catch (Exception ex)
            {
                _Error = "上傳錯誤，錯誤原因如下:<br><br>" + ex;
                TempData["StatusMessage"] = _Error;
                ReturnBool = false;
                return ReturnBool;
            }

            return ReturnBool;
        }

        #endregion Excel 匯入處理
        public int? ConvertToNullableInt(string input)
        {
            int result;
            if (int.TryParse(input, out result))
            {
                return result;
            }
            return null;
        }
        public ActionResult TempDataToExcel(string SYS_TABLE_TYPE)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();

            DataTable DataTableExcel = Db.GetListData(Session.SessionID).AsDataTable();

            NPOIHelper npoi = new NPOIHelper();

            string Execl_Name = "userslist";

            string TempleteFileFullName = Server.MapPath($@"~/Content/ExcelSample/{Execl_Name}.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "人員清單", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + $@"{Execl_Name}_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", $"{Execl_Name}.xlsx");//輸出檔案給Client端
        }

        #region 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_DataTypeConflict(object sender, DataRowCellFilledArgs e)
        {
            _CheckErr = true;
            _CheckDataTypeErr = true;
            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
        }

        #endregion 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容
        [CheckPermission(CheckACTION_ID = "SysIndex")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirm1(string USER_NO)
        {
            this.Shared();
            string BATCH_ID = PushService.CreBATCH_ID();
            AWAT01_LOG aWAT01_LOGtemp = new AWAT01_LOG();
          
            AWAT01 delAWAT01 = EntitiesDb.AWAT01.SingleOrDefault(p => p.SCHOOL_NO == user.SCHOOL_NO && p.USER_NO == USER_NO);
            aWAT01_LOGtemp=EntitiesDb.AWAT01_LOG.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.USER_NO == USER_NO &&p.LOG_DESC== "酷幣匯轉區域管理" &&p.SOURCE_NO== USER_NO).FirstOrDefault();

            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();

            //delADDT20.APPLY_STATUS = "9";
            //EntitiesDb.Entry(delADDT20).State = System.Data.Entity.EntityState.Modified;
            try
            {
                ECOOL_APP.CashHelper.AddCash(user, -(Convert.ToInt32(aWAT01_LOGtemp.CASH_IN)), aWAT01_LOGtemp.SCHOOL_NO, aWAT01_LOGtemp.USER_NO, "ZZZI20", USER_NO.ToString(), "酷幣匯轉區域管理-作廢", true, ref EntitiesDb, "", "", ref  valuesList);
                EntitiesDb.SaveChanges();

                string BODY_TXT = "酷幣匯轉區域管理被作廢，具體事蹟: " + aWAT01_LOGtemp.LOG_DESC + "-、減少酷幣點數" + (aWAT01_LOGtemp.CASH_IN).ToString() + "數";
                // Push到訊息Log，可供App查詢訊息
                PushService.InsertPushDataMe(BATCH_ID, aWAT01_LOGtemp.SCHOOL_NO, aWAT01_LOGtemp.USER_NO, "", BODY_TXT, "", "ZZZI20", "Delete", aWAT01_LOGtemp.USER_NO.ToString(), "", false, ref EntitiesDb);
                HRMT01 hrmt = EntitiesDb.HRMT01.Where(X => X.USER_NO == aWAT01_LOGtemp.USER_NO && X.SCHOOL_NO == aWAT01_LOGtemp.SCHOOL_NO && X.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                TempData["StatusMessage"] = $"{hrmt.NAME}-{aWAT01_LOGtemp.LOG_DESC} 刪除成功";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = $"{aWAT01_LOGtemp.USER_NO}-{aWAT01_LOGtemp.LOG_DESC} 異動失敗, 系統錯誤原因:" + exceptionMessage;
            }

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            return RedirectToAction("ListView1",new { SOURCE_NO= aWAT01_LOGtemp.USER_NO , ThisSaveBATCH_CASH_ID ="", SYS_TABLE_TYPE = "ZZZI20", ADDT14_STYLE= "ZZZI20", USER_NO= aWAT01_LOGtemp.USER_NO, SCHOOL_NO = aWAT01_LOGtemp.SCHOOL_NO});
        }

        protected void NPOI_LineCheckValue(object sender, DataRowCellFilledArgs e)
        {
            //if (Convert.IsDBNull(e.Row["USER_NO"])) return;
            //if (string.IsNullOrWhiteSpace(e.Row["USER_NO"].ToString())) return;

            if (STR_SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
            {
                if (!Convert.IsDBNull(e.Row["IAWARD_KIND"]))
                {
                    if (!string.IsNullOrWhiteSpace(e.Row["IAWARD_KIND"].ToString()))
                    {
                        var Chk = IawardKind.GetIawardKindList.Where(a => a.Value == e.Row["IAWARD_KIND"].ToString()).FirstOrDefault();

                        if (Chk == null)
                        {
                            _CheckErr = true;
                            _CheckMustErr = true;
                            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【獎懲類別】，第" + e.RowNo.ToString() + "列-獎懲類別無效<br/>";
                        }
                        else
                        {
                            if (Chk.CASH_S != null && !Convert.IsDBNull(e.Row["CASH"]))
                            {
                                int CASH = Convert.ToInt16(e.Row["CASH"] ?? 0);

                                if (CASH > Chk.CASH_E && CASH > Chk.CASH_S)
                                {
                                    _CheckErr = true;
                                    _CheckMustErr = true;
                                    _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【獎勵點數】，第" + e.RowNo.ToString() + "列-獎勵點數不在有效圍範內<br/>";
                                }
                            }
                        }
                    }
                }
            }

            if (e.SheetName == ArraySheetNames[0])
            {
                if (!Convert.IsDBNull(e.Row["USER_NO"]))
                {
                    foreach (var item in MustArray)
                    {
                        if (Convert.IsDBNull(e.Row[item]) || (e.Row[item] ?? "").ToString() == "")
                        {
                            _CheckErr = true;
                            _CheckMustErr = true;
                            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
                    }
                }
            }

            if (e.SheetName == ArraySheetNames[1])
            {
                if (!Convert.IsDBNull(e.Row["CLASS_NO"]) && !Convert.IsDBNull(e.Row["SEAT_NO"]))
                {
                    foreach (var item in MustArrayTwo)
                    {
                        if (Convert.IsDBNull(e.Row[item]) || (e.Row[item] ?? "").ToString() == "")
                        {
                            _CheckErr = true;
                            _CheckMustErr = true;
                            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + item + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
                    }
                }
            }
        }

        #region Shared
        private void Shared()
        {

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO(Bre_NO, user.SCHOOL_NO, user.USER_NO);
            ViewBag.IndexActionName = (PermissionBtn.Where(a => a.ActionName == "SysIndex").Select(a => a.BoolUse).FirstOrDefault() == true ? "SysIndex" : "Index");
        }

        /// <summary>
        /// 當功能類型 是空 or null 回傳 預設值
        /// </summary>
        /// <param name="Val"></param>
        /// <returns></returns>
        private string isDbNullSysTableTypeDf(string Val)
        {
            if (string.IsNullOrWhiteSpace(Val)) return ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN;
            else return Val;
        }

        private void SetColName(ADDI09ListViewViewModel Data)
        {
            if (Data.ColName == null) Data.ColName = new ADDI09ListViewColNameViewModel();

            if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
            {
                Data.ColName.EN_CRE_DATE = "CREATEDATE";
                Data.ColName.EN_CRE_PERSON = "TNAME";
                Data.ColName.EN_NAME = "USERNAME";
                Data.ColName.EN_SUBJECT = "IAWARD_KIND";
                Data.ColName.CI_SUBJECT = "獎懲類別";
            }
            else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                Data.ColName.EN_CRE_DATE = "CREATEDATE";
                Data.ColName.EN_CRE_PERSON = "TNAME";
                Data.ColName.EN_NAME = "USERNAME";
                Data.ColName.EN_SUBJECT = "IAWARD_KIND";
                Data.ColName.CI_SUBJECT = "優良表現 ";
            }
            else if (Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || Data.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
            {
                Data.ColName.EN_CRE_DATE = "CRE_DATE";
                Data.ColName.EN_CRE_PERSON = "CRE_PERSON";
                Data.ColName.EN_NAME = "NAME";
                Data.ColName.EN_SUBJECT = "SUBJECT";
                Data.ColName.CI_SUBJECT = "獎懲主旨";
            }
        }

        #endregion Shared
    }
}