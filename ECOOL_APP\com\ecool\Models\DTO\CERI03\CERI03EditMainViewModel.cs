﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CERI03EditMainViewModel
    {
        /// <summary>
        ///必修ID
        /// </summary>
        [DisplayName("必修ID")]
        public string REQUIRED_ID { get; set; }

        /// <summary>
        ///必修類別
        /// </summary>
        [DisplayName("學生要在何時認證")]
        [Required]
        public byte? REQUIRED_TYPE { get; set; }

        /// <summary>
        ///護照細項ID
        /// </summary>
        [DisplayName("護照細項ID")]
        [Required]
        public string ACCREDITATION_ID { get; set; }
    }
}