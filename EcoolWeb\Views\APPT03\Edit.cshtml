﻿
@model ECOOL_APP.com.ecool.Models.DTO.APPT03IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@helper  buttonFun()
{

    <div class="form-group text-center">
        <button value="Create" class="btn btn-default " type="button" onclick="Save() ">
            發送
        </button>
    </div>
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })


    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null,(string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(model => model.MESSAGE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.TextAreaFor(model => model.MESSAGE, new { cols = "200", rows = "15", @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.MESSAGE, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
        @buttonFun()
    </div>


        @Html.HiddenFor(model => model.REF_TABLE)
        @Html.HiddenFor(model => model.REF_KEY)
        @Html.HiddenFor(model => model.DATA_TYPE)
}

@section Scripts {
    <script language="JavaScript">

      



        function Save() {
           $('#DATA_TYPE').val('@EcoolWeb.Controllers.APPT03Controller.DATA_TYPE_VAL.DATA_TYPE_A')
           form1.submit();
        }
    </script>
}