/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Normal/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Normal={directory:"Normal/Regular",family:"GyreTermesMathJax_Normal",testString:"\u00A0\u210E\uD835\uDC00\uD835\uDC01\uD835\uDC02\uD835\uDC03\uD835\uDC04\uD835\uDC05\uD835\uDC06\uD835\uDC07\uD835\uDC08\uD835\uDC09\uD835\uDC0A\uD835\uDC0B\uD835\uDC0C",32:[0,0,250,0,0],160:[0,0,250,0,0],8462:[683,9,500,19,478],119808:[690,0,722,9,689],119809:[676,0,667,16,619],119810:[691,19,722,49,687],119811:[676,0,722,14,690],119812:[676,0,667,16,641],119813:[676,0,611,16,583],119814:[691,19,778,37,755],119815:[676,0,778,21,759],119816:[676,0,389,20,370],119817:[676,96,500,3,479],119818:[676,0,778,30,769],119819:[676,0,667,19,638],119820:[676,0,944,14,921],119821:[676,18,722,16,701],119822:[691,19,778,35,743],119823:[676,0,611,16,600],119824:[691,176,778,35,743],119825:[676,0,722,26,715],119826:[692,19,556,35,513],119827:[676,0,667,31,636],119828:[676,19,722,16,701],119829:[676,18,722,16,701],119830:[676,15,1000,19,981],119831:[676,0,722,16,699],119832:[676,0,722,15,699],119833:[676,0,667,28,634],119834:[473,14,500,25,488],119835:[676,14,556,17,521],119836:[473,14,444,25,430],119837:[676,14,556,25,534],119838:[473,14,444,25,426],119839:[691,0,333,14,389],119840:[473,206,500,28,483],119841:[676,0,556,16,534],119842:[687,0,278,16,255],119843:[687,203,333,-57,266],119844:[676,0,556,22,543],119845:[676,0,278,16,255],119846:[473,0,833,16,814],119847:[473,0,556,21,539],119848:[473,14,500,25,476],119849:[473,205,556,19,524],119850:[473,205,556,34,536],119851:[473,0,444,29,434],119852:[473,14,389,25,361],119853:[630,12,333,20,332],119854:[461,14,556,16,537],119855:[461,14,500,21,485],119856:[461,14,722,23,707],119857:[461,0,500,12,484],119858:[461,205,500,16,480],119859:[461,0,444,21,420],119860:[668,0,611,-51,564],119861:[653,0,611,-8,588],119862:[666,18,667,66,689],119863:[653,0,722,-8,700],119864:[653,0,611,-1,634],119865:[653,0,611,8,645],119866:[666,18,722,52,722],119867:[653,0,722,-8,767],119868:[653,0,333,-8,384],119869:[653,18,444,-6,491],119870:[653,0,667,7,722],119871:[653,0,556,-8,559],119872:[653,0,833,-18,873],119873:[653,15,667,-20,727],119874:[666,18,722,60,699],119875:[653,0,611,0,605],119876:[666,182,722,59,699],119877:[653,0,611,-13,588],119878:[667,18,500,17,508],119879:[653,0,556,59,633],119880:[653,18,722,102,765],119881:[653,18,611,76,688],119882:[653,18,833,71,906],119883:[653,0,611,-29,655],119884:[653,0,556,78,633],119885:[653,0,556,-6,606],119886:[441,11,500,17,476],119887:[683,11,500,23,473],119888:[441,11,444,30,425],119889:[683,13,500,15,527],119890:[441,11,444,31,412],119891:[678,207,278,-147,424],119892:[441,206,500,8,469],119894:[643,11,278,49,276],119895:[643,207,278,-124,301],119896:[683,11,444,14,461],119897:[683,11,278,41,279],119898:[441,9,722,12,704],119899:[441,9,500,14,474],119900:[441,11,500,27,468],119901:[441,205,500,-75,469],119902:[441,205,500,25,483],119903:[441,0,389,45,412],119904:[442,13,389,16,366],119905:[546,11,278,37,296],119906:[441,11,500,42,475],119907:[441,18,444,21,426],119908:[441,18,667,16,648],119909:[441,11,444,-27,447],119910:[441,206,444,-24,426],119911:[428,81,389,-2,380],119912:[683,0,667,-67,593],119913:[669,0,667,-24,624],119914:[685,18,667,32,677],119915:[669,0,722,-46,685],119916:[669,0,667,-27,653],119917:[669,0,667,-13,660],119918:[685,18,722,21,706],119919:[669,0,778,-24,799],119920:[669,0,389,-32,406],119921:[669,99,500,-46,524],119922:[669,0,667,-21,702],119923:[669,0,611,-22,590],119924:[669,12,889,-29,917],119925:[669,15,722,-27,748],119926:[685,18,722,27,691],119927:[669,0,611,-27,613],119928:[685,208,722,27,691],119929:[669,0,667,-29,623],119930:[685,18,556,2,526],119931:[669,0,611,50,650],119932:[669,18,722,67,744],119933:[669,18,667,65,715],119934:[669,18,889,65,940],119935:[669,0,667,-24,694],119936:[669,0,611,73,659],119937:[669,0,611,-11,590],119938:[462,14,500,-21,455],119939:[699,13,500,-14,444],119940:[462,13,444,-5,392],119941:[699,13,500,-21,517],119942:[462,13,444,5,398],119943:[698,205,333,-169,446],119944:[462,203,500,-52,478],119945:[699,9,556,-13,498],119946:[658,9,278,2,266],119947:[658,207,278,-189,284],119948:[699,8,500,-23,483],119949:[699,9,278,2,290],119950:[462,9,778,-14,722],119951:[462,9,556,-6,493],119952:[462,13,500,-3,441],119953:[462,205,500,-120,446],119954:[462,205,500,1,471],119955:[462,0,389,-21,389],119956:[462,13,389,-19,333],119957:[594,9,278,-11,281],119958:[462,9,556,15,492],119959:[462,13,444,16,401],119960:[462,13,667,16,614],119961:[462,13,500,-46,469],119962:[462,205,444,-94,392],119963:[449,78,389,-43,368],120484:[441,11,278,49,235],120485:[441,207,278,-124,246],120488:[690,0,840,80,760],120489:[676,0,763,80,683],120490:[676,0,737,80,657],120491:[690,0,761,80,681],120492:[676,0,785,80,705],120493:[676,0,766,80,686],120494:[676,0,898,80,818],120495:[691,19,868,80,788],120496:[676,0,510,80,430],120497:[676,0,899,80,819],120498:[690,0,840,80,760],120499:[676,0,1067,80,987],120500:[676,18,845,80,765],120501:[662,0,731,80,651],120502:[691,19,868,80,788],120503:[676,0,898,80,818],120504:[676,0,744,80,664],120505:[691,19,868,80,788],120506:[662,0,784,80,704],120507:[676,0,765,80,685],120508:[676,0,848,80,768],120509:[676,0,814,80,734],120510:[676,0,843,80,763],120511:[676,0,880,80,800],120512:[691,0,850,80,770],120513:[680,10,761,80,681],120514:[473,14,679,80,599],120515:[691,217,650,80,570],120516:[473,232,720,80,640],120517:[691,14,666,80,586],120518:[473,14,573,80,493],120519:[667,215,546,80,466],120520:[473,218,673,80,593],120521:[691,14,640,80,560],120522:[473,14,520,80,440],120523:[473,14,769,80,689],120524:[691,14,720,80,640],120525:[461,218,690,80,610],120526:[473,11,665,80,585],120527:[667,215,544,80,464],120528:[473,14,611,80,531],120529:[481,14,715,80,635],120530:[473,218,616,80,536],120531:[473,215,545,80,465],120532:[471,14,674,80,594],120533:[481,16,655,80,575],120534:[473,14,640,80,560],120535:[476,218,812,80,732],120536:[473,232,720,80,640],120537:[473,218,880,80,800],120538:[475,14,789,80,709],120539:[691,14,616,80,536],120540:[482,14,522,80,442],120541:[693,14,828,80,748],120542:[473,14,649,80,569],120543:[677,218,832,80,752],120544:[473,217,616,80,536],120545:[481,14,955,80,875],120546:[668,0,775,80,695],120547:[653,0,756,80,676],120548:[662,0,868,80,788],120549:[668,0,686,80,606],120550:[653,0,795,80,715],120551:[653,0,772,80,692],120552:[653,0,935,80,855],120553:[666,18,799,80,719],120554:[653,0,552,80,472],120555:[653,0,875,80,795],120556:[668,0,775,80,695],120557:[653,0,1051,80,971],120558:[653,15,907,80,827],120559:[653,0,822,80,742],120560:[666,18,799,80,719],120561:[653,0,935,80,855],120562:[653,0,765,80,685],120563:[666,18,799,80,719],120564:[653,0,848,80,768],120565:[653,0,734,80,654],120566:[653,0,798,80,717],120567:[653,0,708,79,627],120568:[653,0,844,80,764],120569:[653,0,770,80,690],120570:[649,0,855,80,774],120571:[658,10,686,80,606],120572:[441,16,645,79,565],120573:[645,208,694,80,614],120574:[442,224,684,80,604],120575:[645,15,630,80,550],120576:[441,15,568,79,487],120577:[639,201,591,80,510],120578:[441,208,581,80,500],120579:[645,15,577,80,496],120580:[441,16,389,80,309],120581:[441,14,677,80,596],120582:[645,17,672,80,592],120583:[426,209,685,80,605],120584:[441,15,604,80,524],120585:[639,201,594,80,514],120586:[441,11,601,80,521],120587:[452,15,766,80,686],120588:[441,209,654,80,573],120589:[441,201,582,80,501],120590:[452,15,658,80,578],120591:[452,15,647,80,567],120592:[441,15,588,80,508],120593:[441,209,678,80,598],120594:[441,226,806,80,726],120595:[441,209,768,79,688],120596:[444,15,763,79,682],120597:[645,16,586,79,505],120598:[441,15,524,79,444],120599:[645,15,696,80,615],120600:[441,15,654,79,574],120601:[630,208,698,79,617],120602:[441,208,576,79,496],120603:[452,20,906,80,826],120604:[683,0,820,80,740],120605:[669,0,808,80,728],120606:[676,0,918,80,838],120607:[683,0,735,80,655],120608:[669,0,840,80,760],120609:[669,0,761,80,681],120610:[669,0,983,80,903],120611:[685,18,844,80,764],120612:[669,0,598,80,518],120613:[669,0,883,80,803],120614:[683,0,820,80,740],120615:[669,12,1106,80,1026],120616:[669,15,935,80,855],120617:[662,0,874,80,794],120618:[685,18,824,80,744],120619:[669,0,983,80,903],120620:[669,0,800,80,720],120621:[685,18,844,80,764],120622:[662,0,909,80,829],120623:[669,0,760,80,680],120624:[676,0,786,80,705],120625:[676,0,786,79,706],120626:[669,0,878,80,798],120627:[676,0,860,80,780],120628:[691,0,892,80,812],120629:[673,10,735,80,655],120630:[473,14,728,80,648],120631:[691,217,791,80,710],120632:[473,232,751,80,671],120633:[691,14,697,80,617],120634:[473,14,624,80,544],120635:[667,215,604,79,523],120636:[473,218,652,80,571],120637:[691,14,659,79,579],120638:[473,14,443,80,363],120639:[473,14,747,80,666],120640:[691,14,746,80,666],120641:[461,218,757,80,677],120642:[473,11,673,80,592],120643:[667,215,615,79,535],120644:[473,13,608,80,528],120645:[481,14,821,80,741],120646:[473,218,731,80,650],120647:[473,215,610,79,530],120648:[471,14,742,79,662],120649:[481,16,705,80,625],120650:[473,14,648,80,567],120651:[476,218,805,79,724],120652:[473,232,877,80,797],120653:[473,218,874,80,794],120654:[475,14,772,80,692],120655:[691,14,670,80,589],120656:[482,14,580,80,500],120657:[693,14,798,80,717],120658:[473,14,714,80,633],120659:[677,218,822,79,742],120660:[473,217,673,80,593],120661:[481,14,985,80,905],120782:[688,13,500,24,476],120783:[688,0,500,65,442],120784:[688,0,500,17,478],120785:[688,14,500,16,468],120786:[688,0,500,19,475],120787:[676,8,500,22,470],120788:[688,13,500,28,475],120789:[676,0,500,17,477],120790:[688,13,500,28,472],120791:[688,13,500,26,473]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Normal"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/Regular/Main.js"]);
