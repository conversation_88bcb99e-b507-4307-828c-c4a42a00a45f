﻿@model EcoolWeb.ViewModels.ADDTListViewModel

@{
    ViewBag.Title = "閱讀認證獎狀頒發-獎狀處理清單";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@helper  buttonSTATUS()
{
string Active_0 = (Model.a09_his_STATUS == 0 || Model.a09_his_STATUS == null) ? "active" : "";
string Active_1 = (Model.a09_his_STATUS == 1) ? "active" : "";
    <br/>
    <div class="row">
        <div class="col-xs-8">
            @Html.DropDownList("PageSize", (IEnumerable<SelectListItem>)ViewBag.PageSizeItems, new { @class = "form-control input-sm", @style = "width:120px", onchange = "FunPageProc(1)" })
        </div>
        <div class="col-xs-4">
            <div class="text-right">
              
                    <button class="btn btn-xs btn-pink @Active_0" type="button" onclick="FunSTATUS(0)">未處理</button>
                    <button class="btn btn-xs btn-pink @Active_1" type="button" onclick="FunSTATUS(1)">已處理</button>
         
            </div>
        </div>
    </div>
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@using (Html.BeginForm("HandleBookCreditList", "ADDT", FormMethod.Post, new { name = "ADDT", id = "ADDT" }))
{
   

    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.a09_his_STATUS)


    <div class="form-inline">
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">書名/學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick = "FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>

        @buttonSTATUS()
    </div>




  
    <img src="~/Content/img/web-Bar-09.png"  style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-reader">
                <thead>
                    <tr>
                        <th>
                            @if (Model.a09_his_STATUS == 0)
                            {
                                <font>處理</font>
                                <input type="checkbox" id="chkALL" name="chkALL" />
                            }
                            else
                            {
                                <font>處理日期</font>
                            }
                        </th>
                        <th>
                            學年
                        </th>
                        <th>
                            學期
                        </th>
                        <th>
                            班級
                        </th>
                        <th>
                            座號
                        </th>
                        <th>
                            姓名
                        </th>
                        <th>
                            閱讀冊數
                        </th>
                        <th>
                            認證等級
                        </th>
                        <th>
                            升級日期
                        </th>
                        <th>
                            備註
                        </th>
                    </tr>
                </thead>
             
                <tbody>
                    @{
                        int i = 0;

                        foreach (var item in Model.ADDT0809List)
                        {
                            <tr>
                                <td>
                                    @if (Model.a09_his_STATUS == 0)
                                    {
                                        @Html.CheckBoxFor(m => m.A9List[i].Chk)
                                        @Html.HiddenFor(m => m.A9List[i].UPNO, item.UPNO)
                                        @Html.HiddenFor(m => m.A9List[i].SYEAR, item.SYEAR)
                                        @Html.HiddenFor(m => m.A9List[i].SEMESTER, item.SEMESTER)
                                        @Html.HiddenFor(m => m.A9List[i].CLASS_NO, item.CLASS_NO)
                                        @Html.HiddenFor(m => m.A9List[i].SEAT_NO, item.SEAT_NO)
                                        @Html.HiddenFor(m => m.A9List[i].USERNAME, item.USERNAME)
                                        @Html.HiddenFor(m => m.A9List[i].BOOK_QTY, item.BOOK_QTY)
                                        @Html.HiddenFor(m => m.A9List[i].LEVEL_DESC, item.LEVEL_DESC)
                                        @Html.HiddenFor(m => m.A9List[i].UP_DATE, item.UP_DATE)

                                    }
                                    else
                                    {
                                        <font color="red">@Html.DisplayFor(modelItem => item.DP_DATE)</font>
                                    }
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SYEAR)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SEMESTER)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.USERNAME)
                                </td>

                                <td>
                                    @Html.DisplayFor(modelItem => item.BOOK_QTY)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.LEVEL_DESC)
                                </td>
                                <td style="text-align: left;white-space:normal">
                                    @Html.DisplayFor(modelItem => item.UP_DATE, "ShortDateTime")
                                </td>
                                <td style="text-align: left;white-space:normal"></td>
                            </tr>
                            i++;
                        }

                    }
                </tbody>   
            </table>
        </div>
    </div>

   


    <div>
        @Html.Pager(Model.ADDT0809List.PageSize, Model.ADDT0809List.PageNumber, Model.ADDT0809List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>


     if (Model.a09_his_STATUS == 0)
     {
            <div class="form-group">
                <div class="col-md-offset-3 col-md-3">
                    <input type="button" id="btnSend" value="送出完成" class="btn btn-default" onclick="btnSend_onclick();" />
                </div>
                <div class="col-md-offset-1 col-md-3">
                    <input type="button" id="btnExcel" value="匯出Excel" class="btn btn-default" onclick="exportExcel();" />
                </div>
            </div>
       }
}

@section scripts{
    <script type="text/javascript" language="javascript">
            var targetFormID = '#ADDT';

            function exportExcel() {
                //表單提交
                document.ADDT.enctype = "multipart/form-data";
                document.ADDT.action = "ExportExcel";
                document.ADDT.submit();
                //$("form[name='AWA004']").submit();
            }



            function btnSend_onclick() {
                document.ADDT.enctype = "multipart/form-data";
                document.ADDT.action = "HandleBookCreditEDIT";
                document.ADDT.submit();
            }

    

            $("#chkALL").click(function () {

                if ($("#chkALL").prop("checked")) {
                    $("input:checkbox").each(function () {
                        $(this).prop("checked", true);
                    });
                }
                else {
                    $("input:checkbox").each(function () {
                        $(this).prop("checked", false);
                    });
                }
            });


            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };


            function FunSTATUS(Value)
            {
                $("#a09_his_STATUS").val(Value);
                FunPageProc(1)
            }

            function todoClear() {
                ////重設
                $("#ADDT").find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }


    </script>
}
