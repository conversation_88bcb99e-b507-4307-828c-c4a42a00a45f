﻿@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";

}
@ViewData["ViewMessage"]
<h2>範本</h2>
<form action="#" name="contentForm" id="contentForm" method="post">
    <div id="showView"></div><br />
</form>
<li>@Html.ActionLink("兌換獎品", "SampleProduct", "Sample")</li><br />
<li>@Html.ActionLink("獎品管理", "SampleProductMana", "Sample")</li><br />
<li>@Html.ActionLink("jqGrid", "SampleGrid", "Sample")</li><br />
<li>
    <a href="../Sample/Index?LNG_FAMILY=zh-TW">@Resources.Resource.Languages_zhTW</a>/
    <a href="../Sample/Index?LNG_FAMILY=en-US">@Resources.Resource.Languages_enUS</a>
</li>
@section Scripts
{
    <script type="text/javascript">
        $('#showView').load("@Url.Action("Role","Comm")");
        @*$('#showView').load("@Url.Action("Upload","Comm")");*@

        function doInsert() {
            try {

                if ($("#file").val() != "") {
                    if (false == FileUpload_click()) {
                        return;
                    }
                }
            }
            catch (err) {
            }
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "../Sample/SampleUploadFile";
            document.contentForm.submit();
        }


    </script>
}
