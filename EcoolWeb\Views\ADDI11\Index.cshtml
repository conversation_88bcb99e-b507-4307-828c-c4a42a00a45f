﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_RunMenu", new { NowAction = "Index" });
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<img src="~/Content/img/web-bar-Run.png" class="img-responsive" />
<br />

<div style="width:100%;">
    <div>  <img src="~/Content/run/ALL/NOTE.jpg" style="width:50%" /> <img src="~/Content/run/ALL/NOTE1.jpg" style="width:50%;float:left; " /></div>

    1、這是有趣的跑步計畫，我們將從臺北木柵動物園跑步出發，預計短則一年，長則畢業前，完成411.5公里的跑步任務，每天養成跑步運動習慣，<br />
    為自己的健康撲滿加分，趕快約同學一起來參加。(師長輸入跑步圈數，學生掌握自己的跑步地圖。)<br />

    2、本模組是為增強學生運動動機而設計。雖然以跑步為標的，但是，各夥伴學校實施亦可將跳繩、搖呼拉圈等視各校需求做合適的轉換。<br />

    3、運動里程累積到下一站，系統會自動給予3點，完成文湖線，系統再自動給予10點酷幣，到達南港展覽館後會自動轉板南線，到達台北火車站後，系統自動給予10點酷幣，並自動轉換為台鐵西部幹線模式。<br />

    4、本撲滿主要是是登記上課日，不含假日，但假日也保持運動習慣，為自己健康加分。<br />

    5、為鼓勵「適度」運動，每天最高可登錄2.0公里。<br />

    6、<b><a class="group1" Target="_Way" href="@Url.Action("RunDetail", "ADDI11", new { WhereIsColorboxForUser = "True" })" style="color:red;">運動撲滿「各站公里數和加點說明」，請按此超連結 </a></b>

    <br /><br />
</div>
<script language="javascript">
    $(document).ready(function () {
        
        $(".group1").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });
    });</script>