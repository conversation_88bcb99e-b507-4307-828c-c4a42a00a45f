﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = null;
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    <link rel="stylesheet" href="~/assets/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="~/assets/css/animate.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.1.1/aos.css">
    <link rel="stylesheet" href="~/assets/css/multi_image.css">
    <link rel="stylesheet" href="~/assets/css/style.css">
</head>
<body id="contain" style="background-color:rgb(211,241,163);">
    <section class="d-flex flex-column" style="width:100%;">
        <div class="d-flex flex-column justify-content-center align-items-center" id="hero_top" style="height:23vh;">
            <div id="top_1" style="height:100%;">
                <div data-aos="fade-up" data-aos-duration="1100" id="multi_bg_t1" style="height:100%;width:100%;"></div>
            </div>
        </div>
        <div class="container" id="hero_logo" style="width:100%;">
            <div id="logo_title_super" class="animated pulse" style="background-image:url(&quot;@Url.Content("~/assets/img/SuperTitle_1.png")&quot;);background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
        </div>
        <div class="row" id="body_c1">
            <div class="col-3" data-aos="fade-right" data-aos-duration="1100" data-aos-delay="250" style="padding:0;">
                <div id="multi_bg_t2"></div>
            </div>
            <div class="col-6">
                <p class="text-left scrollbar board_text_size-a1" id="style-1">
                    1.本行動支付是給學生體驗電子支付的便利性。<br />
                    2.本活動著重減少紙張的浪費，希望達成「無紙化，愛地球」的闖關活動。<br />
                    3.活動數據可以用來延伸抽獎、了解各關卡的闖關人數，甚至把歷程寄給個人。<br />
                    4.活動設定請依:編輯關卡＞人員管理的順序。
                </p>
            </div>
            <div class="col-3 d-flex align-items-center align-self-center" data-aos="fade-left" data-aos-duration="1100" data-aos-delay="450" style="padding:0;height:100%;">
                <div id="hot_air_balloon" class="animated bounce" style="background-image:url(&quot;@Url.Content("~/assets/img/hot_air_balloon_a1_1.gif")&quot;);background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
            </div>
        </div>
        <div class="row" id="body_c2">
            <div class="col-3 col-sm-3 justify-content-center" style="width:20%;">
                <div id="box_t1" style="background-image:url(&quot;@Url.Content("~/assets/img/Ribon_0003_box_t1.png")&quot;);background-size:contain;background-repeat:no-repeat;background-position:bottom;"></div>
            </div>
            <div class="col justify-content-center" style="padding:0;width:60%;margin-top:-50px;">
                <div id="multi_people_c2"></div>
            </div>
            <div class="col-3 justify-content-center" style="height:180%;margin-top:-41px;">
                <div id="multi_bg_c3"></div>
            </div>
        </div>
        <div class="col animated shake" style="z-index:99999999999999999">
            <div id="logo_title_go" class="animated shake" style=";cursor:pointer;background-image:url(&quot;@Url.Content("~/assets/img/SuperTitle_3.png")&quot;);background-position:center;background-size:contain;background-repeat:no-repeat;" onclick="OnGOPsIndex();">
            </div>
        </div>
        <div class="row" id="foot">
            <div class="col">
                <div id="multi_bg_f1"></div>
            </div>
        </div>
    </section>
    <script src="~/assets/js/jquery.min.js"></script>
    <script src="~/assets/bootstrap/js/bootstrap.min.js"></script>
    <script src="~/assets/js/bs-animation.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.1.1/aos.js"></script>
    <script type="text/javascript">
        function OnGOPsIndex() {
            document.location.href = "@Url.Action("GameIndex1", "Game")"
        }
    </script>
</body>
</html>