﻿using ECOOL_APP;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using EcoolWeb.Models;
using EcoolWeb.CustomAttribute;
using System.Globalization;
using ECOOL_APP.com.ecool.util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZT08Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();
        private string SchoolNo = UserProfileHelper.GetSchoolNo();

        [CheckPermission]
        public ActionResult ResetPassword(string USER_NO)
        {
            ViewBag.NAME = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == USER_NO).First().NAME;
            ViewBag.USER_NO = USER_NO;

            return View();
        }

        [CheckPermission]
        public ActionResult DoResetPassword(string USER_NO)
        {
            HRMT01 h1 = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == USER_NO).First();

            ZZT08 zzt08 = db.ZZT08.Where(p => p.SCHOOL_NO == SchoolNo && p.USER_NO == USER_NO).First();
            int Hrmt25Count = 0;
            Hrmt25Count = db.HRMT25.Where(x => x.USER_NO == USER_NO && x.SCHOOL_NO == SchoolNo && x.ROLE_ID == HRMT24_ENUM.ROLE_SCHOOL_ADMIN).Count();

            if (zzt08.SCHOOL_NO == "393601" || zzt08.SCHOOL_NO == "323601" || zzt08.SCHOOL_NO == "423613" || zzt08.SCHOOL_NO == "413608" || zzt08.SCHOOL_NO == "343610")
            {
                if (Hrmt25Count > 0 || USER_NO == "0000" || USER_NO == "helper")
                {
                    zzt08.PASSWORD = "0000";
                    ViewBag.Msg = string.Format("帳號：{0}，姓名：{1}，已重設為0000", USER_NO, h1.NAME);
                }
                else
                {
                    TaiwanCalendar cal = new TaiwanCalendar();
                    int twYear = cal.GetYear((DateTime)h1.BIRTHDAY);

                    zzt08.PASSWORD = twYear.ToString() + new StringHelper().StrRigth("00" + h1.BIRTHDAY.Value.Month.ToString(), 2) + new StringHelper().StrRigth("00" + h1.BIRTHDAY.Value.Day.ToString(), 2);

                    if (USER_NO.Length >= 10)
                    {
                        ViewBag.Msg = string.Format("帳號：{0}，姓名：{1}，已重設為民國年生日", StringHelper.LeftStringR(USER_NO, 5, "*****"), h1.NAME);
                    }
                    else
                    {
                        ViewBag.Msg = string.Format("帳號：{0}，姓名：{1}，已重設為民國年生日", USER_NO, h1.NAME);
                    }
                }
            }
            else
            {
                if (Hrmt25Count > 0 || USER_NO == "0000" || USER_NO == "helper" || USER_NO.Contains("run"))
                {
                    zzt08.PASSWORD = "0000";
                    ViewBag.Msg = string.Format("帳號：{0}，姓名：{1}，已重設為0000", USER_NO, h1.NAME);
                }
                else
                {
                    zzt08.PASSWORD = h1.IDNO.Substring(h1.IDNO.Length - 4, 4);

                    if (USER_NO.Length >= 10)
                    {
                        ViewBag.Msg = string.Format("帳號：{0}，姓名：{1}，密碼已重設為身分證字號後4碼", StringHelper.LeftStringR(USER_NO, 5, "*****"), h1.NAME);
                    }
                    else
                    {
                        ViewBag.Msg = string.Format("帳號：{0}，姓名：{1}，密碼已重設為身分證字號後4碼", USER_NO, h1.NAME);
                    }
                }
            }

            db.SaveChanges();

            return View();
        }

        [CheckPermission(CheckACTION_ID = "ResetPassword")]
        public ActionResult ResetParentPassword(string USER_NO)
        {
            ViewBag.NAME = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == USER_NO).First().NAME;
            ViewBag.USER_NO = USER_NO;

            return View();
        }

        [CheckPermission(CheckACTION_ID = "ResetPassword")]
        public ActionResult DoResetParentPassword(string USER_NO)
        {
            HRMT01 h1 = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == USER_NO).First();
            ViewBag.NAME = h1.NAME;
            ViewBag.USER_NO = USER_NO;
            ViewBag.PARENT_USER_NO = "A" + USER_NO;

            ZZT08 zzt08_s = db.ZZT08.Where(p => p.SCHOOL_NO == SchoolNo && p.USER_NO == USER_NO).First();
            ZZT08 zzt08_p = db.ZZT08.Where(p => p.SCHOOL_NO == SchoolNo && p.USER_NO == "A" + USER_NO).First();
            zzt08_p.PASSWORD = zzt08_s.PASSWORD;
            db.SaveChanges();

            return View();
        }

        // GET: ZZT08
        public ActionResult MODIFY(string redirectController, string redirectAction)
        {
            // 返回網址
            ViewBag.redirectController = redirectController;
            ViewBag.redirectAction = redirectAction;
           
            string wUserNo = string.Empty, wSchool_No = string.Empty;
            wUserNo = (Request["USER_NO"] != null) ? Request["USER_NO"] : user.USER_NO;
            wSchool_No = (Request["SCHOOL_NO"] != null) ? Request["SCHOOL_NO"] : user.SCHOOL_NO;
            ZZT08 zzt08 = db.ZZT08.Where(p => p.SCHOOL_NO == wSchool_No && p.USER_NO == wUserNo).FirstOrDefault();
            return View(zzt08);
        }

        public ActionResult DOModifyPassword(string Msg, string sActionName, string sControllerName)
        {
            ViewBag.Msg = string.Format(Msg);
            ViewBag.sActionName = sActionName;
            ViewBag.sControllerName = sControllerName;
            return View();
        }

        [HttpPost]
        public ActionResult DOModify(string sActionName, string sControllerName)
        {
            return RedirectToAction(sActionName, sControllerName);
        }

        [HttpPost]
        public ActionResult MODIFYPASSW0RD(FormCollection vZZT08, string STATUS, string redirectController, string redirectAction, string NotModifyStatus)
        {
            string EDITSTATUS = (Request["STATUS"] != null) ? Request["STATUS"] : "";
            string Mesg = "";
            Mesg = "恭喜獲得生日禮，酷幣50點，你可以到你的「數位存摺」確認點數。";
            if (user != null && vZZT08["newtextPASSWORD"] != null)
            {
                string wUserNo = string.Empty, wSchool_No = string.Empty;
                wUserNo = (string.IsNullOrWhiteSpace(Request["USER_NO"]) == false) ? Request["USER_NO"] : user.USER_NO;
                wSchool_No = (string.IsNullOrWhiteSpace(Request["SCHOOL_NO"]) == false) ? Request["SCHOOL_NO"] : user.SCHOOL_NO;
                string newtextPASSWORD = vZZT08["newtextPASSWORD"];
                string Full_Name = "";
                ZZT08 zzt08 = db.ZZT08.Where(p => p.SCHOOL_NO == wSchool_No && p.USER_NO == wUserNo).FirstOrDefault();
                Full_Name = db.HRMT01.Where(x => x.USER_NO == wUserNo && x.SCHOOL_NO == wSchool_No).Select(x => x.NAME).FirstOrDefault();
             
                if (NotModifyStatus == "true" && zzt08 != null)
                {
                    zzt08.EXP_DATE = DateTime.Now.AddMonths(6);//不修改密碼後，6個月後通知要準備更換密碼
                    db.Entry(zzt08).State = EntityState.Modified;
                }
                else
                {
                    if (zzt08 == null)
                    {
                        zzt08 = new ZZT08();
                        zzt08.SCHOOL_NO = wSchool_No;
                        zzt08.USER_NO = wUserNo;
                        zzt08.PASSWORD = newtextPASSWORD;
                        zzt08.EXP_DATE = DateTime.Now.AddMonths(6); // 6個月後通知要準備更換密碼
                        db.ZZT08.Add(zzt08);
                    }
                    else
                    {
                        zzt08.PASSWORD = newtextPASSWORD;
                        zzt08.EXP_DATE = DateTime.Now.AddMonths(6); // 6個月後通知要準備更換密碼

                        db.Entry(zzt08).State = EntityState.Modified;
                    }
                }
                try
                {
                    if (!string.IsNullOrWhiteSpace(NotModifyStatus) && NotModifyStatus == "true")
                    {
                        if (redirectAction == "BirthdayGet")
                        {

                            ViewBag.Msg = string.Format(Mesg);

                        }
                        else {

                            ViewBag.Msg = string.Format("沿用原密碼，歡迎使用酷幣平臺");
                        }
                    }
                    else
                    {
                        if (redirectAction == "BirthdayGet")
                        {
                            ViewBag.Msg = string.Format(Mesg);
                        }
                        else {

                            ViewBag.Msg = string.Format("已經完成密碼重設，歡迎使用酷幣平臺");
                        }
                            
                    }

                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    TempData["StatusMessage"] = "系統發生錯誤;原因:" + ex.Message;
                    return View("MODIFY");
                }
            }
            string sActionName = string.Empty, sControllerName = string.Empty;
            NextAction(user, EDITSTATUS, out sActionName, out sControllerName);
            if (!string.IsNullOrEmpty(redirectController + redirectAction))
            {
                sActionName = redirectAction;
                sControllerName = redirectController;
            }
          
            if (NotModifyStatus == "true")
            {   if (redirectAction == "BirthdayGet")
                {


                  
                    return RedirectToAction("DOModifyPassword", new { msg = Mesg, sActionName = sActionName, sControllerName = sControllerName });

                }
                else{

                    return RedirectToAction("DOModifyPassword", new { msg = "沿用原密碼，歡迎使用酷幣平臺", sActionName = sActionName, sControllerName = sControllerName });
                }
             
            }
            else
            {
                if (redirectAction == "BirthdayGet")
                {



                    return RedirectToAction("DOModifyPassword", new { msg = Mesg, sActionName = sActionName, sControllerName = sControllerName });

                }
                else
                {

                    return RedirectToAction("DOModifyPassword", new { msg = "已經完成密碼重設，歡迎使用酷幣平臺", sActionName = sActionName, sControllerName = sControllerName });
                }
            }
        }

        [HttpPost]
        public ActionResult CheckPasswordCount(string SCHOOL_NO, string UserNo, string sPwd)
        {
            bool PasswordStatus = false;
            try
            {
                IQueryable<ZZT08> ltzzt08 = db.ZZT08.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.USER_NO == UserNo && p.PASSWORD == sPwd);
                if (ltzzt08.Count() > 0)
                {
                    PasswordStatus = true;
                }
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
            }
            return Json(PasswordStatus);
        }

        private void NextAction(UserProfile LoginUser, string EDITSTATUS, out string ActionName, out string ControllerName)
        {
            if (LoginUser.INIT_STATUS == null || LoginUser.INIT_STATUS == 0)
            {
                HRMT01 hrmt01 = new HRMT01();
                hrmt01 = db.HRMT01.Where(p => p.SCHOOL_NO == LoginUser.SCHOOL_NO && p.USER_NO == LoginUser.USER_NO).FirstOrDefault();
                hrmt01.INIT_STATUS = 1;
                db.Entry(hrmt01).State = EntityState.Modified;
                db.SaveChanges();
            }

            switch (LoginUser.USER_TYPE)
            {
                case "S":   //學生
                    if (LoginUser.INIT_STATUS == null || LoginUser.INIT_STATUS == 0)
                    {
                        ActionName = "Gallery";
                        ControllerName = "AWAI02";
                    }
                    else
                    {
                        ActionName = "StudentIndex";
                        ControllerName = "Home";
                    }
                    break;

                case "P":   //家長
                    ActionName = "ParentsIndex";
                    ControllerName = "Home";
                    break;

                case "T":   //教師

                    if (EDITSTATUS == "")
                    {
                        ActionName = "TeacherIndex";
                        ControllerName = "Home";
                    }
                    else
                    {
                        ActionName = "Query";
                        ControllerName = "ZZZI19";
                    }
                    break;

                case "A":   //管理者
                    if (LoginUser.INIT_STATUS == null || LoginUser.INIT_STATUS == 0)
                    {
                        ActionName = "QUERY";
                        ControllerName = "ZZZI01";
                    }
                    else
                    {
                        if (EDITSTATUS == "")
                        {
                            ActionName = "TeacherIndex";
                            ControllerName = "Home";
                        }
                        else
                        {
                            ActionName = "Query";
                            ControllerName = "ZZZI19";
                        }
                    }
                    break;

                default:
                    ActionName = "GuestIndex";
                    ControllerName = "Home";
                    break;
            }
        }
    }
}