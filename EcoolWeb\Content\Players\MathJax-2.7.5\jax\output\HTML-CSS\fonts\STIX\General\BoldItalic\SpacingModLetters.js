/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{688:[852,-328,380,7,365],689:[841,-329,380,7,365],690:[862,-176,350,24,384],691:[690,-344,389,21,384],692:[690,-344,389,2,365],693:[690,-171,389,2,371],694:[684,-345,390,5,466],695:[690,-331,450,15,467],696:[690,-176,350,11,386],699:[685,-369,333,128,332],704:[690,-240,343,-3,323],705:[690,-240,326,20,364],710:[690,-516,333,40,367],711:[690,-516,333,79,411],728:[678,-516,333,71,387],729:[655,-525,333,163,293],730:[754,-541,333,127,340],731:[44,173,333,-40,189],732:[655,-536,333,48,407],733:[697,-516,333,69,498],736:[684,-190,379,14,423],737:[857,-329,222,2,217],738:[690,-331,280,8,274],739:[690,-335,389,3,387],740:[849,-329,328,9,364],748:[70,167,314,5,309],749:[720,-528,395,5,390],759:[-108,227,333,-74,285]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/SpacingModLetters.js");
