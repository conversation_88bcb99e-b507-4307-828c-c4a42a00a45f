﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
     public class ADDI11MyClassRunLogRankDataViewModel
    {
        [DisplayName("排名")]
        public int Ranking { get; set; }


        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///總累計里程(m)
        /// </summary>
        [DisplayName("班級平均累積里程(m)")]
        public double? AVG_Total_M { get; set; }
    }
}