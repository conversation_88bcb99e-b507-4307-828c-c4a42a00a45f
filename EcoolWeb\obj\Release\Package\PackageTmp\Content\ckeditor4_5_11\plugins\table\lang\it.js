﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'it', {
	border: 'Dimensione bordo',
	caption: 'Intestazione',
	cell: {
		menu: 'Cella',
		insertBefore: 'Inserisci Cella Prima',
		insertAfter: 'Inserisci Cella Dopo',
		deleteCell: 'Elimina celle',
		merge: 'Unisce celle',
		mergeRight: 'Unisci a Destra',
		mergeDown: 'Unisci in Basso',
		splitHorizontal: 'Dividi Cella Orizzontalmente',
		splitVertical: 'Dividi Cella Verticalmente',
		title: 'Proprietà della cella',
		cellType: 'Tipo di cella',
		rowSpan: 'Su più righe',
		colSpan: 'Su più colonne',
		wordWrap: 'Ritorno a capo',
		hAlign: 'Allineamento orizzontale',
		vAlign: 'Allineamento verticale',
		alignBaseline: 'Linea Base',
		bgColor: 'Colore di Sfondo',
		borderColor: 'Colore del Bordo',
		data: 'Dati',
		header: 'Intestazione',
		yes: 'Si',
		no: 'No',
		invalidWidth: 'La larghezza della cella dev\'essere un numero.',
		invalidHeight: 'L\'altezza della cella dev\'essere un numero.',
		invalidRowSpan: 'Il numero di righe dev\'essere un numero intero.',
		invalidColSpan: 'Il numero di colonne dev\'essere un numero intero.',
		chooseColor: 'Scegli'
	},
	cellPad: 'Padding celle',
	cellSpace: 'Spaziatura celle',
	column: {
		menu: 'Colonna',
		insertBefore: 'Inserisci Colonna Prima',
		insertAfter: 'Inserisci Colonna Dopo',
		deleteColumn: 'Elimina colonne'
	},
	columns: 'Colonne',
	deleteTable: 'Cancella Tabella',
	headers: 'Intestazione',
	headersBoth: 'Entrambe',
	headersColumn: 'Prima Colonna',
	headersNone: 'Nessuna',
	headersRow: 'Prima Riga',
	invalidBorder: 'La dimensione del bordo dev\'essere un numero.',
	invalidCellPadding: 'Il paging delle celle dev\'essere un numero',
	invalidCellSpacing: 'La spaziatura tra le celle dev\'essere un numero.',
	invalidCols: 'Il numero di colonne dev\'essere un numero maggiore di 0.',
	invalidHeight: 'L\'altezza della tabella dev\'essere un numero.',
	invalidRows: 'Il numero di righe dev\'essere un numero maggiore di 0.',
	invalidWidth: 'La larghezza della tabella dev\'essere un numero.',
	menu: 'Proprietà tabella',
	row: {
		menu: 'Riga',
		insertBefore: 'Inserisci Riga Prima',
		insertAfter: 'Inserisci Riga Dopo',
		deleteRow: 'Elimina righe'
	},
	rows: 'Righe',
	summary: 'Indice',
	title: 'Proprietà tabella',
	toolbar: 'Tabella',
	widthPc: 'percento',
	widthPx: 'pixel',
	widthUnit: 'unità larghezza'
} );
