/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size4/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Size4={directory:"Size4/Regular",family:"GyreTermesMathJax_Size4",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[1087,587,530,135,440],41:[1087,587,530,90,395],47:[1428,928,982,80,902],91:[1099,599,453,135,363],92:[1428,928,982,80,902],93:[1099,599,453,90,318],123:[1089,589,487,90,397],124:[1079,579,213,80,133],125:[1089,589,487,90,397],160:[0,0,250,0,0],770:[699,-536,1041,0,1041],771:[687,-533,1037,0,1037],774:[695,-540,1058,0,1058],780:[694,-531,1041,0,1041],785:[708,-553,1058,0,1058],812:[-70,233,1041,0,1041],813:[-80,243,1041,0,1041],814:[-70,225,1058,0,1058],815:[-88,243,1058,0,1058],816:[-88,243,1037,0,1037],8214:[1079,579,366,80,286],8260:[1428,928,982,80,902],8425:[742,-548,1875,0,1875],8730:[1384,858,647,120,673],8739:[1079,579,213,80,133],8741:[1079,579,366,80,286],8968:[1099,579,453,135,363],8969:[1099,579,453,90,318],8970:[1079,599,453,135,363],8971:[1079,599,453,90,318],9001:[1432,932,474,90,384],9002:[1432,932,474,90,384],9140:[742,-548,1875,0,1875],9141:[-98,292,1875,0,1875],9180:[742,-541,2514,0,2514],9181:[-91,292,2514,0,2514],9182:[777,-530,2519,0,2519],9183:[-80,326,2519,0,2519],9184:[741,-543,2576,0,2576],9185:[-93,291,2576,0,2576],10214:[1099,599,457,135,367],10215:[1099,599,457,90,322],10216:[1432,932,474,90,384],10217:[1432,932,474,90,384],10218:[1432,932,714,90,624],10219:[1432,932,714,90,624],10222:[1086,586,382,135,292],10223:[1086,586,382,90,247]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Size4"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size4/Regular/Main.js"]);
