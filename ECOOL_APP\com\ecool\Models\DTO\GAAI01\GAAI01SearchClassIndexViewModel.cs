﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01SearchClassIndexViewModel
    {
        public byte IsSearch { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("班級")]
        public string WhereCLASS_NO { get; set; }

        public string WhereSYEARSEMESTER { get; set; }

        [DisplayName("學年度")]
        public byte? WhereSYEAR { get; set; }

        [DisplayName("學期")]
        public byte? WhereSEMESTER { get; set; }

        public List<GAAT01> gAAT01s { get; set; }

        /// <summary>
        /// 班級資料
        /// </summary>
        public GAAI01SearchClassMainViewModel ClassMain { get; set; }

        /// <summary>
        /// 此班的配戴資料統計
        /// </summary>
        public List<GAAI01SearchClassListViewModel> ClassList { get; set; }

        /// <summary>
        /// 人員配戴明細
        /// </summary>
        public List<GAAI01SearchWearDetailListViewModel> WearDetailList { get; set; }

        /// <summary>
        /// 管理者統計報表 -  填報的班級數量資料
        /// </summary>
        public List<GAAI01SearchNotReportedViewModel> ClassReportedData { get; set; }

        /// <summary>
        /// 管理者統計報表 -  全校配戴率
        /// </summary>
        public List<GAAI01SearchSchoolRateViewModel> SchoolRate { get; set; }

        /// <summary>
        /// 管理者統計報表 - 各未配載原因統計人數
        /// </summary>
        public List<GAAI01SearchUnWearMemoCountViewModel> UnWearMemoCount { get; set; }
    }
}