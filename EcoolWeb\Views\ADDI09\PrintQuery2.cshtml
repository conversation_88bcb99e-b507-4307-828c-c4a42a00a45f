﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09ListViewViewModel
@{
   
        ViewBag.Title = "批次加扣點明細一覽表";
   

    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
@if (Model.WhereIsPassbook)
{
    <div class="Div-EZ-Right cScreen">
        <button id="ButtonExcel" class="btn btn-sm btn-sys">匯出excel</button>
    </div>
}



<div class="print table-92Per" style="margin: 0px auto; " id="tbData">
                    <table class="table-ecool table-ecool-pink-SEC" border="1">
                        <thead>
                            <tr></tr>
                            <tr class="text-center">
                                <th>
                                    日期
                                </th>
                              
                                    <th>
                                        學號
                                    </th>
                                    <th>
                                        班級
                                    </th>

                                    @*<th>
                                        座號
                                    </th>*@
                                    <th>
                                        姓名
                                    </th>
                                    <th>
                                        點數
                                    </th>
                                    <th>
                                        異動說明
                                    </th>
                                    <th>
                                        給點人員
                                    </th>
                               
                              
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.ADDT20PrintList)
                            {
                                <tr align="center">
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                    </td>
                                 
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.USER_NO)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                    </td>
                                    @*<td align="center">
                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                    </td>*@
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.NAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CASH)
                                    </td>
                                    <td style="text-align: left;white-space:normal">
                                        @Html.DisplayFor(modelItem => item.SUBJECT)
                                    </td>
                                    <td align="center">


                                        @Html.DisplayFor(modelItem => item.CRE_PERSON)
                                    </td>
                                   

                                    
                                </tr>
                                }
                        </tbody>
                    </table>
   
</div>

@section Scripts {
    <script type="text/javascript">
        $(function() {
            // 自動列印
            window.print();

            // Excel 匯出功能
            $('#ButtonExcel').on('click', function() {
                try {
                    var table = document.getElementById('tbData');
                    var html = table.outerHTML;
                    var blob = new Blob([html], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                    });
                    saveAs(blob, "批次加扣點明細一覽表.xls");
                } catch (error) {
                    console.error('Excel export error:', error);
                    alert('匯出 Excel 時發生錯誤，請稍後再試。');
                }
                return false;
            });
        });
    </script>
}

