﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'zh-cn', {
	alt: '替换文本',
	border: '边框大小',
	btnUpload: '上传到服务器',
	button2Img: '确定要把当前图像按钮转换为普通图像吗？',
	hSpace: '水平间距',
	img2Button: '确定要把当前图像改变为图像按钮吗？',
	infoTab: '图像信息',
	linkTab: '链接',
	lockRatio: '锁定比例',
	menu: '图像属性',
	resetSize: '原始尺寸',
	title: '图像属性',
	titleButton: '图像域属性',
	upload: '上传',
	urlMissing: '缺少图像源文件地址',
	vSpace: '垂直间距',
	validateBorder: '边框大小必须为整数格式',
	validateHSpace: '水平间距必须为整数格式',
	validateVSpace: '垂直间距必须为整数格式'
} );
