﻿@model AWAI01IndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    int num = 0;

    var ProductList = (Model.MultipleLevel == 1) ? Model.SpecificListData : Model.ListData;

    bool IsUserAwatExchangeCARD = System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck((string)ViewBag.BRE_NO, "AwatExchangeCARD");
    bool ISUSERAwatExchange = System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck((string)ViewBag.BRE_NO, "AwatExchangeCARD1");

}

@if (Model.ShowMultiple && Model.MultipleLevel == 2)
{
    <div class="mb-5 mt-4 text-center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x text-ribbon" aria-hidden="true"></span>
    </div>
}

<div class="Div-EZ-Awat pb-3">

    @if (AppMode)
    {
        <div style="height:5px"></div>
        <br />
    }
    else
    {
        <div style="height:35px"></div>
    }

    <div class="p-context">
        @if (Model.ShowMultiple && Model.MultipleLevel == 1)
        {
            <label class="label_dd_font18" style="color:red"> * 總召學校獎品</label>
            <br />
        }
        else if (Model.ShowMultiple && Model.MultipleLevel == 2)
        {
            <label class="label_dd_font18" style="color:red"> * 本校獎品</label>
            <br />
        }

        @if (ProductList.Count() == 0)
        {
            <div class="text-center">
                <label class="label_dd_font18">無任何商品</label>
            </div>
        }

        <div class="d-flex flex-wrap">
            @{
                foreach (var award in ProductList)
                {
                    if (award.IsHidden) // 隱藏的就跳過這輪迴圈
                    {
                        continue;
                    }

                    string aImgUrl = string.Empty;
                    if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
                    {
                        aImgUrl = ViewBag.ImgUrl + @"/" + award.IMG_FILE;
                    }
                    else
                    {
                        aImgUrl = ViewBag.ImgUrl + award.SCHOOL_NO + @"/" + award.IMG_FILE;
                    }

                    num++;

                    <div class="col-md-6">

                        @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                        {

                            string NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange(award, user);

                            if (user.USER_TYPE == "A")
                            {

                                NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange1(award, user);
                            }

                            <div class="redeem-box redeem-box-bidding">
                                <img src='@aImgUrl' href='@aImgUrl' alt="@award.AWARD_NAME" class="img-responsive">
                            </div>
                            <span class="float-right text-red">
                                @if (string.IsNullOrWhiteSpace(NG) == false)
                                {
                                    if (award.FULLSCREEN_YN != "Y")
                                    {
                                        if (user.USER_TYPE == "S" || user.USER_TYPE == "A" || user.USER_TYPE == "T")
                                        {

                                           @:完成競標
                                        }
                                    }
                                }
                                else
                                {
                                    @:競標
                                }
                            </span>
                        }
                        else
                        {
                            <div class="redeem-box" style='@if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B) { <TEXT>background-color:lightpink</TEXT> }'>
                                <img src='@aImgUrl' href='@aImgUrl' alt="@award.AWARD_NAME" class="img-responsive">
                            </div>

                        }

                        <div class="prod-caption">
                            <div class="d-block mb-3 center">
                                <label class="label_dd_font18" title="@award.AWARD_NAME">@StringHelper.LeftStringR(award.AWARD_NAME, 13)</label>

                                @if (!string.IsNullOrEmpty(award.VIDEO_PATH))
                                {
                                    <a class="btn-primary btn btn-xs btn-prod" id="watchVideo" style="margin-left:5px;"
                                       href="@Url.Action("IframeFullPage","Comm", new { src = award.VIDEO_PATH })">
                                        <span>觀看影片</span>
                                    </a>
                                }
                            </div>
                            <div class="d-block mb-3">
                                <span class="prod-text">
                                    @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                    {
                                        @:目前出價：@award.COST_CASH
                                    }
                                    else
                                    {
                                        @:兌換點數：@award.COST_CASH
                                    }
                                </span>
                                <span class="prod-text">
                                    @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_P || award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_C)
                                    {
                                        @:已募資點數：@(award.COST_CASH * award.QTY_TRANS)
                                    }
                                    else
                                    {
                                        @:剩餘數量：@award.QTY_STORAGE
                                    }
                                </span>
                                @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && (award.BID_BUY_PRICE ?? 0) > 0)
                                {
                                    <br />
                                    <span class="prod-text">
                                        直購點數：@award.BID_BUY_PRICE
                                    </span>
                                }

                                <br />
                                開始日期：@(award.SDATETIME.HasValue ? award.SDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA" )
                                <br />
                                兌換期限：@(award.EDATETIME.HasValue ? award.EDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA" )
                                <br />
                                @if (award.SHOW_DESCRIPTION_YN == SharedGlobal.Y)
                                {
                                    <p class="text-primary">@award.DESCRIPTION</p>
                                }
                            </div>
                            <div class="prod-icon">
                                @if (award.SCHOOL_NO == SharedGlobal.ALL)
                                {
                                    <img src="~/Content/images/icon-redeem-musterSchool.png" alt="總召學校獎品" title="總召學校獎品">
                                }
                                @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                {
                                    <img src="~/Content/images/icon-redeem-bidding.png" alt="競標獎品" title="競標獎品">
                                }
                                @if (award.HOT_YN == SharedGlobal.Y)
                                {
                                    <img src="~/Content/images/icon-redeem-hot.png" alt="熱門獎品" title="熱門獎品" />
                                }
                                @if (Convert.ToDateTime(award.SDATETIME).AddMonths(+1) >= DateTime.Now)
                                {
                                    <img src="~/Content/images/icon-redeem-new.png" alt="新上架獎品" title="新上架獎品">
                                }
                                @if (award.FULLSCREEN_YN == "Y" || award.FULLSCREEN_YN == "B")
                                {
                                    if (user != null && award.FULLSCREEN_YN == "Y")
                                    {
                                        <img src="~/Content/images/icon-redeem-studentCard.png" alt="只限定數位學生證兌換" title="只限定數位學生證兌換" />

                                        if (user.USER_TYPE == "S")
                                        {
                                            <button role="button" class="btn-danger btn btn-xs btn-prod" title="只限定數位學生證兌換">
                                                無法兌換
                                            </button>
                                        }
                                    }

                                    else
                                    {
                                        <img src="~/Content/images/icon-redeem-mobile.png" alt="可以行動支付，也可以網頁上兌換" title="可以行動支付，也可以網頁上兌換" />
                                    }

                                }
                                @if (award.BUY_PERSON_YN == "Y")
                                {
                                    <img src="~/Content/images/icon-redeem-lock.png" alt="有限制特定的對象" title="有限制特定的對象">
                                }
                                @if (award.READ_LEVEL != null)
                                {
                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgReadUrl(award.READ_LEVEL))" style="max-height:30px;margin-right:5px">
                                }

                                @if (award.PASSPORT_LEVEL != null)
                                {
                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgPassportUrl(award.PASSPORT_LEVEL))" style="max-height:30px;margin-right:5px">
                                }

                                @if (null != user && (
                                              (user.USER_TYPE == UserType.Student && Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student)
                                                  || (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents && Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher))
                                                  )
                                {

                                    string NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange(award, user);

                                    if (string.IsNullOrWhiteSpace(NG) == false)
                                    {
                                        if (award.FULLSCREEN_YN != "Y")
                                        {
                                            if (user.USER_TYPE == "S")
                                            {
                                                <button role="button" class="btn-danger btn btn-xs btn-prod" title="@Html.Raw(StringHelper.UnHtml(HttpUtility.HtmlDecode(NG)))">
                                                    無法兌換
                                                </button>}
                                        }
                                    }
                                    else
                                    {
                                        if (award.FULLSCREEN_YN != "Y")
                                        {
                                            <button class="btn-default btn btn-xs btn-prod" id="@("btn_" + award.AWARD_NO)" type="button" onclick="funGetExchange('@award.AWARD_NO')">我要兌換</button>
                                        }
                                    }
                                }

                                @if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
                                {
                                    string str = "";
                                    if (AppMode)
                                    {
                                        str = "prodlist cboxElement";

                                        <button class="btn-default btn btn-xs btn-prod" onclick="clickLink1('@Url.Action("Query", "AWA006", new { whereKeyword = award.AWARD_NO, Awat = "Awat_Key" })')" >
                                            @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                            {
                                                <span>得標名單</span>
                                            }
                                            else
                                            {
                                                <span>兌獎名單</span>
                                            }
                                        </button>}
                                    else
                                    {
                                        <button class="btn-default btn btn-xs btn-prod" onclick="clickLink('@Url.Action("Query", "AWA006", new { whereKeyword = award.AWARD_NO, Awat = "Awat_Key" })')">
                                            @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                            {
                                                <span>得標名單</span>
                                            }
                                            else
                                            {
                                                <span>兌獎名單</span>
                                            }
                                        </button>
                                    }


                                }
                                else
                                {
                                    string str = "";
                                    if (AppMode)
                                    {
                                        str = "prodlist cboxElement";

                                        <button class="btn-default btn btn-xs btn-prod "   onclick="clickLink1('@Url.Action("Query", "AWA004", new { whereAWARD_NO = award.AWARD_NO, Awat = "Awat_Key" })')" >
                                            @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                            {
                                                <span>得標名單</span>
                                            }
                                            else
                                            {
                                                <span>兌獎名單</span>
                                            }
                                        </button>}
                                    else
                                    {

                                        <button class="btn-default btn btn-xs btn-prod"  onclick="clickLink('@Url.Action("Query", "AWA004", new { whereAWARD_NO = award.AWARD_NO, Awat = "Awat_Key" })')" >
                                            @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                            {
                                                <span>得標名單</span>
                                            }
                                            else
                                            {
                                                <span>兌獎名單</span>
                                            }
                                        </button>
                                    }
                                }
                                @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                {
                                    <samp class="BtnBid">
                                        <a class="btn-default btn btn-xs btn-prod" href="@Url.Action("BidDataList", (string)ViewBag.BRE_NO,new {  AWARD_NO= award.AWARD_NO,  BackController=Model.Search.BackController,  WhereSouTable = Model.Search.WhereSouTable })">
                                            競標名單
                                        </a>
                                    </samp>
                                }

                                @if (Model.VisableModify == true || Model.VisableDelete == true)
                                {

                                    if (Model.VisableModify == true)
                                    {
                                        if (((award.SCHOOL_NO != SharedGlobal.ALL) || (award.SCHOOL_NO == SharedGlobal.ALL && HRMT24_ENUM.QOutSchoolList.Contains((byte)user.ROLE_TYPE))))
                                        {
                                            <button class="btn-default btn btn-xs btn-prod" type="button" onclick="funGetModify('@award.AWARD_NO')">修改</button>
                                        }
                                    }
                                    if (Model.VisableDelete == true)
                                    {
                                        if (string.IsNullOrWhiteSpace(Model.Search.unProduct) && ((award.SCHOOL_NO != SharedGlobal.ALL) || (award.SCHOOL_NO == SharedGlobal.ALL && HRMT24_ENUM.QOutSchoolList.Contains((byte)user.ROLE_TYPE))))
                                        {
                                            <button class="btn-default btn btn-xs btn-prod" type="button" onclick="funGetDelete('@award.AWARD_NO')">下架/刪除</button>
                                        }
                                    }

                                    if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student)
                                    {

                                        if (IsUserAwatExchangeCARD)
                                        {
                                            if (award.AWARD_TYPE != Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                            {
                                                <br />

                                                if (award.QTY_STORAGE < 1)
                                                {
                                                    <button class="btn-default btn btn-danger btn-xs btn-prod" disabled type="button"> 庫存不足 </button>
                                                }
                                                else if (award.FULLSCREEN_YN == "Y")
                                                {

                                                    <button class="btn-default btn btn-success btn-xs btn-prod" type="button" onclick="funCARD('@award.AWARD_NO','')">數位學生證感應兌換</button>
                                                    if (user.USER_NO != "helper")
                                                    {

                                                        <button class="btn-default btn btn-primary btn-xs btn-prod" type="button" onclick="funCARD('@award.AWARD_NO','SchoolNo')">學號兌換，慎用</button>
                                                    }
                                                }

                                                else
                                                { <button class="btn-default btn btn-success btn-xs btn-prod" type="button" onclick="funCARD('@award.AWARD_NO','')">數位學生證感應兌換</button>
                                                    if (user.USER_NO != "helper")
                                                    {
                                                        <button class="btn-default btn btn-primary btn-xs btn-prod" type="button" onclick="funCARD('@award.AWARD_NO','SchoolNo')">學號兌換，慎用</button>
                                                    }
                                                }
                                            }
                                        }
                                    }

                                }
                            </div>
                        </div>
                    </div>

                }


            }
        </div><!--/row-->
        @if (Model.ShowMultiple && Model.MultipleLevel == 1 && Model.SpecificListData.Count() >= 4)
        {
            <div style="height:35px"></div>
            <div class="text-center">
                <button class="btn btn-default" type="button" onclick="doSearch('@Html.IdFor(m => m.Search.whereAWARD_SCHOOL_NO)', '@SharedGlobal.ALL');">更多總召學校獎品</button>
            </div>
        }
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        $(".prodlist").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
        $(".box img").colorbox({ opacity: 0.82 });
        $(".BtnBid a").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
        $("#watchVideo").colorbox({ opacity: 0.82, width: "70%", innerHeight: "700px", scrolling: true });
        $(".img-responsive").colorbox({ opacity: 0.82 });
    });
</script>
