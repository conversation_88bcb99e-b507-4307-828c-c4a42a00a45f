﻿@model ZZZI27DetailsViewModel
@using ECOOL_APP.com.ecool.service

@using (Html.BeginCollectionItem("Details_List"))
{
    var Index = Html.GetIndex("Details_List");

<div class="tr" id="Tr@(Index)">
    <div class="td" style="text-align:right">
        @if (Model.ADD_MODE == BDMT02_ENUM.AddMode.Multi)
        {
            if ((Model.DATA_CODE != "TeacherIndex"))
            {
                <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
            }
            else
            {
                if (Model.ITEM_NO != "001" && Model.ITEM_NO != "002")
                {
                    <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
                }

            }
        }

        @Html.HiddenFor(m => m.BRE_NO)
        @Html.HiddenFor(m => m.DATA_CODE)
        @Html.HiddenFor(m => m.DATA_TYPE)
        @Html.HiddenFor(m => m.SCHOOL_NO)
        @Html.HiddenFor(m => m.ITEM_NO)
        @Html.HiddenFor(m => m.ADD_MODE)
        @Html.HiddenFor(m => m.TXT_MODE)
        @Html.HiddenFor(m => m.VAL_MODE)
        @Html.HiddenFor(m => m.TXT_SCHOOL_SET_YN)
        @Html.HiddenFor(m => m.VAL_SCHOOL_SET_YN)
    </div>
   
        <div class="td" style="@(Model.TXT_SCHOOL_SET_YN == SharedGlobal.Y ? "" : "display:none")">
            @Html.EditorFor(m => m.CONTENT_TXT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Model.DATA_CODE == "TeacherIndex"?"請輸入要跳出公告的內容":"", @Type = @"" + BDMT02_ENUM.Mode.GetInputType(Model.TXT_MODE) + "" } })
    
        </div>
   
        <div class="td" style="@(Model.VAL_SCHOOL_SET_YN == SharedGlobal.Y ? "":"display:none")">
            @if (Model.DATA_CODE == "TeacherIndex")
            {
                @Html.EditorFor(m => m.CONTENT_VAL, new { htmlAttributes = new { @class = "form-control input-md", @Type = @"" + BDMT02_ENUM.Mode.GetInputType(Model.VAL_MODE) + "" } })
               
            }
            else
            {
                @Html.EditorFor(m => m.CONTENT_VAL, new { htmlAttributes = new { @class = "form-control input-md", @Type = @"" + BDMT02_ENUM.Mode.GetInputType(Model.VAL_MODE) + "" } })
         
            }
        </div>

        <div class="td" style="padding-left:10px;@(Model.ADD_MODE == BDMT02_ENUM.AddMode.Fixed ? "":"display:none");">

            @Model.MEMO
           
        </div>
    </div>
}