﻿using DotNet.Highcharts;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWA004QueryViewModel
    {

        /// <summary>
        /// 只顯示某一所學校=>人員
        /// </summary>
        public string whereSchoolNo { get; set; }

        /// <summary>
        /// 顯示某一所學校=>獎品
        /// </summary>
        public string whereAWARD_SCHOOL_NO { get; set; }

        /// <summary>
        /// 顯示某一類獎品
        /// </summary>
        public string whereAWARD_TYPE { get; set; }
        


        /// <summary>
        /// 搜尋狀態
        /// </summary>
        public string whereSTATUS { get; set; }


        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的暱稱
        /// </summary>
        public string whereSNAME { get; set; }


        /// <summary>
        /// 只顯示某一位學生的獎品ID
        /// </summary>
        public string whereAWARD_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的獎品名稱
        /// </summary>
        public string whereAWARD_NAME { get; set; }


        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一位學生的班級
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderColumn { get; set; }


        /// <summary>
        /// 排序: 遞增/遞減
        /// </summary>
        public string SortBy { get; set; }
        public string AWARD_TYPE { get; set; }
        public int Page { get; set; }
        /// <summary>
        /// 每頁筆數
        /// </summary>
        public int PageSize { get; set; }
        /// <summary>
        /// 體適能趨勢圖 仰臥起坐
        /// </summary>
        public Highcharts PieChart;

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<VAWA004> VAWA004List;

        public virtual ICollection<AWA004PrintViewModel> Chk { get; set; }

        public AWA004QueryViewModel()
        {
            PageSize = 20;
            Page = 0;
            OrderColumn =  "TRANS_NO";
            SortBy = "DESC";
        }

    }
}