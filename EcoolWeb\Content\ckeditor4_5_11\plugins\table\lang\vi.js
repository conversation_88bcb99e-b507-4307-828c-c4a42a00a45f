﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'vi', {
	border: '<PERSON>ích thước đường viền',
	caption: 'Đầu đề',
	cell: {
		menu: 'Ô',
		insertBefore: '<PERSON><PERSON><PERSON> ô Phía trước',
		insertAfter: '<PERSON><PERSON><PERSON> ô Phía sau',
		deleteCell: 'Xo<PERSON> ô',
		merge: 'Kết hợp ô',
		mergeRight: '<PERSON><PERSON><PERSON> hợp sang phải',
		mergeDown: 'Kết hợp xuống dưới',
		splitHorizontal: '<PERSON><PERSON> tách ô theo chiều ngang',
		splitVertical: '<PERSON><PERSON> tách ô theo chiều dọc',
		title: '<PERSON>hu<PERSON>c tính của ô',
		cellType: 'Kiểu của ô',
		rowSpan: 'Kết hợp hàng',
		colSpan: '<PERSON><PERSON><PERSON> hợp cột',
		wordWrap: '<PERSON><PERSON> liền hàng',
		hAlign: '<PERSON><PERSON> lề ngang',
		vAlign: '<PERSON><PERSON> lề dọc',
		alignBaseline: '<PERSON><PERSON>ờng cơ sở',
		bgColor: 'Màu nền',
		borderColor: 'Màu viền',
		data: 'Dữ liệu',
		header: 'Đầu đề',
		yes: 'Có',
		no: 'Không',
		invalidWidth: 'Chiều rộng của ô phải là một số nguyên.',
		invalidHeight: 'Chiều cao của ô phải là một số nguyên.',
		invalidRowSpan: 'Số hàng kết hợp phải là một số nguyên.',
		invalidColSpan: 'Số cột kết hợp phải là một số nguyên.',
		chooseColor: 'Chọn màu'
	},
	cellPad: 'Khoảng đệm giữ ô và nội dung',
	cellSpace: 'Khoảng cách giữa các ô',
	column: {
		menu: 'Cột',
		insertBefore: 'Chèn cột phía trước',
		insertAfter: 'Chèn cột phía sau',
		deleteColumn: 'Xoá cột'
	},
	columns: 'Số cột',
	deleteTable: 'Xóa bảng',
	headers: 'Đầu đề',
	headersBoth: 'Cả hai',
	headersColumn: 'Cột đầu tiên',
	headersNone: 'Không có',
	headersRow: 'Hàng đầu tiên',
	invalidBorder: 'Kích cỡ của đường biên phải là một số nguyên.',
	invalidCellPadding: 'Khoảng đệm giữa ô và nội dung phải là một số nguyên.',
	invalidCellSpacing: 'Khoảng cách giữa các ô phải là một số nguyên.',
	invalidCols: 'Số lượng cột phải là một số lớn hơn 0.',
	invalidHeight: 'Chiều cao của bảng phải là một số nguyên.',
	invalidRows: 'Số lượng hàng phải là một số lớn hơn 0.',
	invalidWidth: 'Chiều rộng của bảng phải là một số nguyên.',
	menu: 'Thuộc tính bảng',
	row: {
		menu: 'Hàng',
		insertBefore: 'Chèn hàng phía trước',
		insertAfter: 'Chèn hàng phía sau',
		deleteRow: 'Xoá hàng'
	},
	rows: 'Số hàng',
	summary: 'Tóm lược',
	title: 'Thuộc tính bảng',
	toolbar: 'Bảng',
	widthPc: 'Phần trăm (%)',
	widthPx: 'Điểm ảnh (px)',
	widthUnit: 'Đơn vị'
} );
