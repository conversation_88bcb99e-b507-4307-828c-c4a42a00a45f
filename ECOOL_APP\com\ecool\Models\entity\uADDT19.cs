﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT19
    {
        ///Summary
        ///IMG_ID
        ///Summary
        [DisplayName("流水號")]
        public string IMG_ID { get; set; }

        ///Summary
        ///IMG_FILE
        ///Summary
        [DisplayName("圖片")]
        public string IMG_FILE { get; set; }

        ///Summary
        ///IMG_TITLE
        ///Summary
        [DisplayName("標題")]
        [Required]
        public string IMG_TITLE { get; set; }

        ///Summary
        ///IMG_DATES
        ///Summary
        [DisplayName("開始日期")]
        [Required]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? IMG_DATES { get; set; }

        ///Summary
        ///IMG_DATEE
        ///Summary
        [DisplayName("結束日期")]
        [Required]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? IMG_DATEE { get; set; }

        ///Summary
        ///IMG_LINK
        ///Summary
        [DisplayName("連結")]
        [DataType(DataType.Url, ErrorMessage = "請輸入正確的網址")]
        public string IMG_LINK { get; set; }

        ///Summary
        ///STATUS
        ///Summary
        [DisplayName("是否啟用")]
        public string STATUS { get; set; }

        ///Summary
        ///STATUS_NAME
        ///Summary
        [DisplayName("狀態")]
        public string STATUS_NAME { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///CRE_PERSON
        ///Summary
        [DisplayName("建立人員")]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [DisplayName("修改日期")]
        public DateTime? CHG_DATE { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///CHG_PERSON
        ///Summary
        [DisplayName("修改人員")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///1.首頁圖片輪播 2.小小舞臺首播
        /// </summary>
        [DisplayName("1.首頁圖片輪播 2.小小舞臺首播")]
        public byte? IMG_TYPE { get; set; }

        /// <summary>
        /// 狀態值
        /// </summary>
        public static class STATUS_Val
        {
            /// <summary>
            /// 啟用.Enabled
            /// </summary>
            public static string STATUS_E = "Enabled";

            /// <summary>
            /// 停止.Disabled
            /// </summary>
            public static string STATUS_D = "Disabled";
        }

        /// <summary>
        /// 取得狀態名稱
        /// </summary>
        /// <param name="Val">狀態代碼</param>
        /// <returns></returns>
        public static string GetStatusName(string Val)
        {
            string returnStr = string.Empty;

            if (Val == STATUS_Val.STATUS_E)
            {
                returnStr = "啟用";
            }
            else if (Val == STATUS_Val.STATUS_D)
            {
                returnStr = "停止";
            }

            return returnStr;
        }

        /// <summary>
        /// 下拉式選單
        /// </summary>
        /// <param name="SelectedVal"></param>
        /// <returns></returns>
        public static List<SelectListItem> GetSTATUS(string SelectedVal)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            SelectListItem NewDATA = new SelectListItem();
            NewDATA.Value = uADDT19.STATUS_Val.STATUS_E;
            NewDATA.Text = uADDT19.GetStatusName(uADDT19.STATUS_Val.STATUS_E);

            if (string.IsNullOrWhiteSpace(SelectedVal) == false)
            {
                if (uADDT19.STATUS_Val.STATUS_E == SelectedVal)
                {
                    NewDATA.Selected = true;
                }
            }
            else
            {
                NewDATA.Selected = false;
            }
            SelectItem.Add(NewDATA);

            SelectListItem NewDATA2 = new SelectListItem();
            NewDATA2.Value = uADDT19.STATUS_Val.STATUS_D;
            NewDATA2.Text = uADDT19.GetStatusName(uADDT19.STATUS_Val.STATUS_D);
            if (string.IsNullOrWhiteSpace(SelectedVal) == false)
            {
                if (uADDT19.STATUS_Val.STATUS_E == SelectedVal)
                {
                    NewDATA2.Selected = true;
                }
            }
            else
            {
                NewDATA2.Selected = false;
            }
            SelectItem.Add(NewDATA2);

            return SelectItem;
        }
    }
}