/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Shapes/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Shapes={directory:"Shapes/Regular",family:"NeoEulerMathJax_Shapes",id:"NEOEULERSHAPES",32:[0,0,333,0,0,""],160:[0,0,333,0,0,""],11034:[690,6,816,60,756,"660 192c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM660 342c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM660 492c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM60 192c0 26 22 48 48 48s48 -22 48 -48 s-22 -48 -48 -48s-48 22 -48 48zM60 342c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM60 492c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM510 42c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM360 42 c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM210 42c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM510 642c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM360 642c0 26 22 48 48 48s48 -22 48 -48 s-22 -48 -48 -48s-48 22 -48 48zM210 642c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM660 43c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM660 642c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM60 42 c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48zM60 642c0 26 22 48 48 48s48 -22 48 -48s-22 -48 -48 -48s-48 22 -48 48"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Shapes/Regular/Main.js");
