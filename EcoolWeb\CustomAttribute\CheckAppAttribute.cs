﻿
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;

namespace EcoolWeb.CustomAttribute
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, Inherited = true, AllowMultiple = true)]
    public class CheckAppAttribute : ActionFilterAttribute
    {

        

        public override void OnActionExecuting(HttpActionContext actionContext)
        {
            try
            {

                ApiBaseModel CkDb = new ApiBaseModel();
                CkDb = WebApiHelper.GetApiBaseModel(actionContext);

                if (actionContext.ActionDescriptor.GetCustomAttributes<AllowAnonymousAttribute>().Count > 0)   // 允許匿名訪問
                {

                    WebApiHelper.InsertCkLog(CkDb, "匿名訪問");

                    base.OnActionExecuting(actionContext);
                    return;
                }

               

                if (string.IsNullOrEmpty(CkDb.CheckId) || string.IsNullOrEmpty(CkDb.UUID))
                {
                    actionContext.Response = new HttpResponseMessage(HttpStatusCode.Forbidden);

                    WebApiHelper.InsertCkLog(CkDb, "未傳入有效認證參數");

                    return;
                }

                string RealCheckId;
                bool Ok = ECOOL_APP.AppHelper.checkCheckId(CkDb.CheckId, CkDb.UUID, out RealCheckId);

                //檢查驗証是否與確 進入後 每頁的驗証
                if (Ok == false)
                {
                    actionContext.Response = new HttpResponseMessage(HttpStatusCode.Forbidden);

                    WebApiHelper.InsertCkLog(CkDb, "每頁驗証失敗");

                    return;
                }
                else
                {
                    CkDb.RealCheckId = RealCheckId;

                    WebApiHelper.ApiSetSession(CkDb);

                }



                base.OnActionExecuting(actionContext);
            }
            catch
            {
                actionContext.Response = new HttpResponseMessage(HttpStatusCode.Forbidden);
            }
        }


   }

   
}