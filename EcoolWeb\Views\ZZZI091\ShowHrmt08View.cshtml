﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel



@if (Model.Hrmt08List.Count() != 0 || Model.Hrmt09List.Count() != 0)
{

    <section class="row px-3 healthData">
        <div class="col-lg-12 bgPosition">
            <h2 class="heading-h2">健康資訊</h2>
            @if (Model.Hrmt08List.Count() != 0)
            {

            @*身高*@

            <div class="row mt-3">
                <div class="col-12 text-center">
                    <h3>身高趨勢表</h3>
                </div>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th style="text-align: center;">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().TALL)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_TALL_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_TALL_W)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_TALL_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_TALL_W)
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Hrmt08List)
                        {
                            <tr align="center">
                                <td>
                                    @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.TALL)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_TALL_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_TALL_W)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_TALL_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_TALL_W)
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @*體重*@

            <div class="row mt-3">
                <div class="col-12 text-center">
                    <h3>體重趨勢表</h3>
                </div>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th style="text-align: center;">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().WEIGHT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_WEIGHT_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_WEIGHT_W)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_WEIGHT_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_WEIGHT_W)
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Hrmt08List)
                        {
                            <tr align="center">
                                <td>
                                    @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.WEIGHT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_WEIGHT_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_WEIGHT_W)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_WEIGHT_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_WEIGHT_W)
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @*體適能趨勢表*@

            <div class="row mt-3">

                @if (Model.Hrmt09List.Count() != 0)
                {
                    if (Model.FitnesschartC != null)
                    {
                        <div class="col-12 text-center">
                            <h3>體適能趨勢表</h3>
                        </div>

                        @*@if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness)
                            {*@

                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th style="text-align: center;">
                                        @Html.DisplayNameFor(model => model.Hrmt09List.First().SYEAR)
                                    </th>
                                    <th style="text-align: center;">
                                        @Html.DisplayNameFor(model => model.Hrmt09List.First().SEMESTER)
                                    </th>
                                    <th style="text-align: center">
                                        @Html.DisplayNameFor(model => model.Hrmt09List.First().V_SET_REACH_TEST)
                                    </th>
                                    <th style="text-align: center">
                                        @Html.DisplayNameFor(model => model.Hrmt09List.First().S_L_JUMP_TEST)
                                    </th>
                                    <th style="text-align: center">
                                        @Html.DisplayNameFor(model => model.Hrmt09List.First().SIT_UPS_TEST)
                                    </th>
                                    <th style="text-align: center">
                                        @Html.DisplayNameFor(model => model.Hrmt09List.First().C_P_F_TEST)
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Hrmt09List)
                                {
                                    <tr align="center">
                                        <td>
                                            @Html.DisplayFor(modelItem => item.SYEAR)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.SEMESTER)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.V_SET_REACH_TEST)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.S_L_JUMP_TEST)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.SIT_UPS_TEST)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.C_P_F_TEST)
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>

                    }
                }
                @*}*@

            </div>


            <div class="row mt-3 charts">

                <div class="col-12 text-center">
                    @if (Model.TALLchart != null)
                    {

                        @Model.TALLchart

                        @*<div class="printhight" style="display:none"> @Model.printTALLchart</div>*@
                    }
                </div>
            </div>
            <div class="row mt-3 charts">
                <div class="col-12 text-center">
                    @if (Model.WEIGHTchart != null)
                    {

                        @Model.WEIGHTchart
                        @*<div class="printweight" style="display:none"> @Model.printWEIGHTchart</div>*@
                    }
                </div>
            </div>}
            @if (Model.Hrmt09List.Count() != 0)
            {

                <div class="row mt-3 charts">
                    <div class="col-12 text-center">
                        @if (Model.FitnesschartC != null)
                        {

                            @Model.FitnesschartC
                            @*<div class="printFitnesschartC" style="display:none"> @Model.prinFitnesschartC</div>*@
                        }
                    </div>
                </div>
                <div class="row mt-3 charts">
                    <div class="col-12 text-center">
                        @if (Model.FitnesschartSU != null)
                        {

                            @Model.FitnesschartSU
                            @*<div class="printchartSU" style="display:none"> @Model.printFitnesschartSU</div>*@
                        }
                    </div>
                </div>
                <div class="row mt-3 charts">
                    <div class="col-12 text-center">
                        @if (Model.FitnesschartSL != null)
                        {

                            @Model.FitnesschartSL
                            @*<div class="printchartSL" style="display:none"> @Model.printFitnesschartSL</div>*@
                        }
                    </div>
                </div>
                <div class="row mt-3 charts">
                    <div class="col-12 text-center">
                        @if (Model.FitnesschartV != null)
                        {

                            @Model.FitnesschartV
                            @*<div class="printchartV" style="display:none"> @Model.printFitnesschartV</div>*@
                        }
                    </div>
                </div>
            }
        </div>
    </section>
}

@*2018/10/1視力部分拿掉*@
@*<div class="col-sm-12 text-center">

        <h3> 視力趨勢表(右)</h3>
    </div>

        <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
            <thead>
                <tr>
                    <th style="text-align: center;">
                        @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                    </th>
                    <th style="text-align: center">
                        @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT)
                    </th>
                    <th style="text-align: center">
                        @Html.DisplayNameFor(model => model.Hrmt08List.First().G_VISION_RIGHT)
                    </th>
                    <th style="text-align: center">
                        @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT_08)
                    </th>
                    <th style="text-align: center">
                        @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT_09)
                    </th>
                    <th style="text-align: center">
                        @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT_12)
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.Hrmt08List)
                {
                    <tr align="center">
                        <td>
                            @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                        </td>

                        <td>
                            @Html.DisplayFor(modelItem => item.VISION_RIGHT)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.G_VISION_RIGHT)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.VISION_RIGHT_08)
                            <text>人</text>
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.VISION_RIGHT_09)
                            <text>人</text>
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.VISION_RIGHT_12)
                            <text>人</text>
                        </td>
                    </tr>
                }
            </tbody>
        </table>

    @*<div class="col-sm-12">
        @if (Model.RIGHT_VISIONColumnChart != null)
                {
            <div style="height:30px"></div>
            @Model.RIGHT_VISIONColumnChart
        }
    </div>*@

@*<div class="col-sm-12 text-center">

        <h3> 視力趨勢表(左)</h3>
    </div>
    <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
        <thead>
            <tr>
                <th style="text-align: center;">
                    @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                </th>
                <th style="text-align: center">
                    @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT)
                </th>
                <th style="text-align: center">
                    @Html.DisplayNameFor(model => model.Hrmt08List.First().G_VISION_LEFT)
                </th>
                <th style="text-align: center">
                    @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT_08)
                </th>
                <th style="text-align: center">
                    @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT_09)
                </th>
                <th style="text-align: center">
                    @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT_12)
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.Hrmt08List)
            {
                <tr align="center">
                    <td>
                        @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.VISION_LEFT)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.G_VISION_LEFT)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.VISION_LEFT_08)
                        <text>人</text>
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.VISION_LEFT_09)
                        <text>人</text>
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.VISION_LEFT_12)
                        <text>人</text>
                    </td>
                </tr>
            }
        </tbody>
    </table>*@

@*<div class="col-sm-12">
        @if (Model.LEFT_VISIONColumnChart != null)
                {
            <div style="height:30px"></div>
            @Model.LEFT_VISIONColumnChart
        }
    </div>*@
