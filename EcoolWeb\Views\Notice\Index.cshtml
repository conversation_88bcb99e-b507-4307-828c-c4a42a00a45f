﻿
@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO)

    </div>
}


@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1'

        //分頁
        function FunPageProc(pageno) {

            $('#Page').val(pageno);
            funAjax()
        }

        //排序
        function FunSort(SortName) {

            OrderByName = $('#OrderByName').val();
            SyntaxName = $('#SyntaxName').val();

            if (OrderByName == SortName) {

                if (SyntaxName == "Desc") {
                    $('#SyntaxName').val("ASC");
                }
                else {
                    $('#SyntaxName').val("Desc");
                }
            }
            else {
                $('#OrderByName').val(SortName);
                $('#SyntaxName').val("Desc");
            }
            funAjax()
        }

    

        //查詢
        function funAjax() {

            var data = {
                "OrderByName": $('#OrderByName').val(),
                "SyntaxName": $('#SyntaxName').val() ,
                "Page": $('#Page').val() ,
                "SearchContents": $('#SearchContents').val(),
            };


            $.ajax({
                url: '@Url.Action("_PageContent", (string)ViewBag.BRE_NO)',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }


        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}