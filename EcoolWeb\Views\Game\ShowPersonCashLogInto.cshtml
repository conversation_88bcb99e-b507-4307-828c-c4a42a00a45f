﻿@model GamePersonIntoViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

}
@if (Model.User == null)
{
    <div class="text-center">查無資料</div>
}
else
{

    <div class="containerEZ">
        <h1 style="color:blue;text-align:center">
            <strong> 姓名:  @Model.User.NAME</strong>
            <br />
            <strong> 目前點數 : @Model.User.CASH_AVAILABLE</strong>
        </h1>
        <br />
        <div class="text-right">
            <button type="button" class="btn btn-default btn-sm" onclick="location.reload()">
                <span class="glyphicon glyphicon-repeat"></span>重新整理
            </button>
        </div>
        <h4 style="color:blue"><strong>點數歷史記錄 :</strong></h4>

        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                <thead>
                    <tr>
                        <th style="font-size:20px">
                            @Html.DisplayNameFor(model => model.CashDetails.First().LOG_TIME)
                        </th>

                        <th style="font-size:20px">
                            @Html.DisplayNameFor(model => model.CashDetails.First().LOG_DESC)
                        </th>
                        <th style="font-size:20px">
                            @Html.DisplayNameFor(model => model.CashDetails.First().CASH_IN)
                        </th>
                        <th style="font-size:20px">
                            @Html.DisplayNameFor(model => model.CashDetails.First().LOG_CASH_AVAILABLE)
                        </th>
                    </tr>
                </thead>
                <tbody id="ShowTop3">
                    @foreach (var item in Model.CashDetails)
                    {
                        <tr>
                            <td style="font-size:20px">
                                @Html.DisplayFor(modelItem => item.LOG_TIME)
                            </td>
                            <td style="font-size:20px">
                                @Html.DisplayFor(modelItem => item.LOG_DESC)
                            </td>
                            <td style="font-size:20px">
                                @Html.DisplayFor(modelItem => item.CASH_IN)
                            </td>
                            <td style="font-size:20px">
                                @Html.DisplayFor(modelItem => item.LOG_CASH_AVAILABLE)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <h4 style="color:blue"><strong>中獎清單 :</strong></h4>
        <div>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>
                            獎項
                        </th>

                        <th>
                            @Html.DisplayNameFor(model => model.LotteryDetails.First().PEOPLE_COUNT)
                        </th>
                        <th>
                            抽獎時間
                        </th>
                        <th>
                            是否領獎
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.LotteryDetails?.Count > 0)
                    {
                        foreach (var item in Model.LotteryDetails)
                        {
                            <tr>

                                <td>
                                    @Html.DisplayFor(modelItem => item.LOTTERY_DESC)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.PEOPLE_COUNT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.CRE_DATE)
                                </td>
                                <td>
                                    @if (HRMT24_ENUM.CheckQAdmin(user))
                                    {
                                        <input id="RECEIVE_AWARD_@(item.LOTTERY_NO)" type="checkbox" value="@item." @(item.RECEIVE_AWARD ? "checked" : "") onclick="OnUpdateReceiveAward(this,'@item.LOTTERY_NO','@item.ITEM_NO')" />
                                    }
                                    else
                                    {
                                        <text>@(item.RECEIVE_AWARD ? "Y" : "")</text>
                                    }
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="4" align="center"><h5 style="color:red"><strong>未中任何獎項</strong></h5> </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <h4 style="color:blue;padding-top:20px">
            <strong>
                已完成 <span class="badge">@(Model.LevelPassCount)</span> 關，未完成  <span class="badge"> @(Model.UnLevelPassCount)</span> 關 :
            </strong>
        </h4>
        <div class="row">
            @foreach (var item in Model.MeLevelDetails)
            {

                <div class="col-md-4" style="color:red">
                    @if (item.MeIsLevelPass)
                    {
                        <div class="alert alert-success">
                            @item.LEVEL_NAME
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            @item.LEVEL_NAME
                        </div>
                    }
                </div>
            }
        </div>
    </div>

}
@if (HRMT24_ENUM.CheckQAdmin(user))
{
    <script type="text/javascript">
    function OnUpdateReceiveAward(This, LOTTERY_NO_Val, ITEM_NO_Val) {

            var Checked_Val = This.checked;
            var ID_VAL = This.id;

            $.ajax({
                url: "@(Url.Action("SaveUpdateReceiveAward", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    LOTTERY_NO: LOTTERY_NO_Val
                    , ITEM_NO: ITEM_NO_Val
                    , Checked: Checked_Val
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        alert(res.Error);

                        if (Checked_Val)
                        {
                            $('#' + ID_VAL).prop("checked", false)
                        }
                        else
                        {
                            $('#' + ID_VAL).prop("checked", true)
                        }
                    }
                    else {
                        if (Checked_Val) {
                            alert('異動已領獎')
                        }
                        else {
                            alert('取消領獎')
                        }
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);

                    if (Checked_Val) {
                        $('#' + ID_VAL).prop("checked", false)
                    }
                    else {
                        $('#' + ID_VAL).prop("checked", true)
                    }
                }
            });

        }
    </script>
}