/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Alphabets/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Alphabets={directory:"Alphabets/Regular",family:"GyreTermesMathJax_Alphabets",testString:"\u00A0\u0E3F\u2103\u2107\u2109\u2116\u2117\u211E\u2120\u2122\u2126\u212A\u212B\u212E\uFEFF",32:[0,0,250,0,0],160:[0,0,250,0,0],3647:[745,83,667,17,593],8451:[676,14,1029,57,995],8455:[676,14,475,48,427],8457:[676,0,929,57,919],8470:[669,15,1001,0,957],8471:[686,14,760,30,730],8478:[662,0,667,17,659],8480:[669,-297,939,40,899],8482:[662,-305,980,40,941],8486:[676,0,853,80,773],8490:[662,0,722,34,723],8491:[896,0,722,15,706],8494:[596,0,742,40,702],65279:[0,0,0,0,0]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Alphabets"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Alphabets/Regular/Main.js"]);
