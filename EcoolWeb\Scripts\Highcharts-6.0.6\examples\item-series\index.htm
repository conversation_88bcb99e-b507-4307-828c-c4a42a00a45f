<!DOCTYPE HTML>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>Highcharts Example</title>

		<style type="text/css">
#container {
    max-width: 800px;
    height: 400px;
    margin: 1em auto;
}
		</style>
	</head>
	<body>
<script src="../../code/highcharts.js"></script>
<script src="../../code/modules/item-series.js"></script>
<div id="container"></div>


		<script type="text/javascript">
Highcharts.chart('container', {

    chart: {
        type: 'item'
    },

    title: {
        text: 'Highcharts item chart'
    },

    xAxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    },

    yAxis: {
        gridLineWidth: 0,
        labels: {
            enabled: false
        },
        title: {
            text: null
        }
    },

    plotOptions: {
        series: {
            stacking: 'normal'
        }
    },

    series: [{
        name: 'Items bought',
        data: [5, 3, 4],
        color: 'green'
    }, {
        name: 'Items sold',
        data: [0, 2, 1],
        color: 'red'
    }]

});

		</script>
	</body>
</html>
