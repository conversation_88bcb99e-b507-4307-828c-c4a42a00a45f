// Modern JavaScript for ADDI03 BOOK_MAINTAIN page
$(document).ready(function() {
    // 初始化模組
    const bookMaintain = {
        init: function() {
            this.checkConfiguration();
            this.bindEvents();
            this.setupGlobalFunctions();
        },

        checkConfiguration: function() {
            // 檢查必要的配置
            console.log('檢查ADDI03配置:', window.ADDI03_CONFIG);

            if (!window.ADDI03_CONFIG) {
                console.error('ADDI03_CONFIG未定義');
                alert('系統配置錯誤：配置物件未找到');
                return false;
            }

            const requiredUrls = ['maxBookUrl', 'checkBookIdUrl', 'getBookIsModifyUrl', 'checkBookCountUrl'];
            const missingUrls = [];

            requiredUrls.forEach(url => {
                if (!window.ADDI03_CONFIG[url]) {
                    missingUrls.push(url);
                }
            });

            if (missingUrls.length > 0) {
                console.error('缺少必要的URL配置:', missingUrls);
                alert('系統配置錯誤：缺少必要的URL配置 - ' + missingUrls.join(', '));
                return false;
            }

            console.log('配置檢查通過');
            return true;
        },

        bindEvents: function() {
            // 綁定事件
            $('#btnSend').on('click', this.handleSubmit.bind(this));
            $('#btnCancel').on('click', this.handleCancel.bind(this));
            $('#GRADE').on('change', this.handleGradeChange.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.btnSend_onclick = this.handleSubmit.bind(this);
            window.btnCancel_onclick = this.handleCancel.bind(this);
            window.GetMax = this.getMaxBookId.bind(this);
        },

        handleCancel: function() {
            const form = document.getElementById('contentForm');
            if (form) {
                form.enctype = "multipart/form-data";
                form.action = "../ADDI03/BOOK_MAINTAINQuery";
                form.submit();
            } else {
                console.error('找不到表單元素');
                alert('系統錯誤：找不到表單元素');
            }
        },

        handleGradeChange: function() {
            // 當年級改變時，自動獲取最大書號
            const bookStatus = $('#BOOK_Status').val();
            if (bookStatus === 'ADD') {
                this.getMaxBookId();
            }
        },

        getMaxBookId: function() {
            const grade = $("#GRADE").val();
            if (!grade) {
                console.log('未選擇年級');
                return;
            }

            // 檢查配置
            if (!window.ADDI03_CONFIG || !window.ADDI03_CONFIG.maxBookUrl) {
                console.error('URL配置未找到');
                alert('系統配置錯誤，請聯繫系統管理員');
                return;
            }

            console.log('獲取最大書號:', grade, 'URL:', window.ADDI03_CONFIG.maxBookUrl);

            $.ajax({
                url: window.ADDI03_CONFIG.maxBookUrl,
                data: { Grade: grade },
                method: 'POST',
                cache: false,
                dataType: 'json',
                timeout: 10000,
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(result) {
                    $("#BOOK_ID").val(result);
                    console.log('獲取最大書號成功:', result);
                },
                error: function(xhr, status, error) {
                    console.error('獲取最大書號失敗:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        grade: grade,
                        url: window.ADDI03_CONFIG.maxBookUrl,
                        responseText: xhr.responseText
                    });

                    let errorMessage = '獲取書號失敗';
                    if (xhr.status === 404) {
                        errorMessage = '獲取書號的服務端點未找到';
                    } else if (xhr.status === 500) {
                        errorMessage = '服務器內部錯誤，請聯繫系統管理員';
                    } else if (status === 'timeout') {
                        errorMessage = '請求超時，請稍後再試';
                    } else if (xhr.responseText) {
                        try {
                            const errorData = JSON.parse(xhr.responseText);
                            errorMessage = errorData.message || errorMessage;
                        } catch (e) {
                            // 解析失敗，使用默認錯誤訊息
                        }
                    }

                    alert(errorMessage);
                }
            });
        },

        validateForm: function() {
            let errors = [];

            if ($('#GRADE').val() === '') {
                errors.push('請選擇年級');
            }
            if ($('#BOOK_ID').val() === '') {
                errors.push('書號為必填');
            }
            if ($('#BOOK_ID').val() !== '' && $('#BOOK_ID').val().length !== 3) {
                errors.push('書號必需長度為3');
            }
            if ($('#BOOK_NAME').val() === '') {
                errors.push('書名為必填');
            }

            return errors;
        },

        async checkBookId(bookId) {
            try {
                // 檢查配置和參數
                if (!window.ADDI03_CONFIG || !window.ADDI03_CONFIG.checkBookIdUrl) {
                    throw new Error('URL配置未找到');
                }
                if (!bookId || bookId.trim() === '') {
                    throw new Error('書號不能為空');
                }

                console.log('檢查書號:', bookId, 'URL:', window.ADDI03_CONFIG.checkBookIdUrl);

                const response = await $.ajax({
                    url: window.ADDI03_CONFIG.checkBookIdUrl,
                    data: { BOOK_ID: bookId },
                    method: 'POST',
                    cache: false,
                    dataType: 'json',
                    timeout: 10000, // 10秒超時
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    }
                });

                console.log('檢查書號回應:', response);
                return response === true;
            } catch (error) {
                console.error('檢查書號失敗:', {
                    error: error,
                    bookId: bookId,
                    url: window.ADDI03_CONFIG?.checkBookIdUrl,
                    status: error.status,
                    statusText: error.statusText,
                    responseText: error.responseText
                });

                if (error.status === 404) {
                    throw new Error('檢查書號的服務端點未找到');
                } else if (error.status === 500) {
                    throw new Error('服務器內部錯誤，請聯繫系統管理員');
                } else if (error.statusText === 'timeout') {
                    throw new Error('請求超時，請稍後再試');
                } else {
                    throw new Error('檢查書號時發生錯誤: ' + (error.statusText || '未知錯誤'));
                }
            }
        },

        async checkBookIsModify(bookId) {
            try {
                if (!window.ADDI03_CONFIG || !window.ADDI03_CONFIG.getBookIsModifyUrl) {
                    throw new Error('URL配置未找到');
                }
                if (!bookId || bookId.trim() === '') {
                    throw new Error('書號不能為空');
                }

                console.log('檢查書籍修改狀態:', bookId, 'URL:', window.ADDI03_CONFIG.getBookIsModifyUrl);

                const response = await $.ajax({
                    url: window.ADDI03_CONFIG.getBookIsModifyUrl,
                    data: { BOOK_ID: bookId },
                    method: 'POST',
                    cache: false,
                    dataType: 'json',
                    timeout: 10000,
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    }
                });

                console.log('檢查書籍修改狀態回應:', response);
                return response === true;
            } catch (error) {
                console.error('檢查書籍修改狀態失敗:', {
                    error: error,
                    bookId: bookId,
                    url: window.ADDI03_CONFIG?.getBookIsModifyUrl,
                    status: error.status,
                    statusText: error.statusText,
                    responseText: error.responseText
                });

                if (error.status === 404) {
                    throw new Error('檢查書籍修改狀態的服務端點未找到');
                } else if (error.status === 500) {
                    throw new Error('服務器內部錯誤，請聯繫系統管理員');
                } else if (error.statusText === 'timeout') {
                    throw new Error('請求超時，請稍後再試');
                } else {
                    throw new Error('檢查書籍修改狀態時發生錯誤: ' + (error.statusText || '未知錯誤'));
                }
            }
        },

        async checkBookCount(grade) {
            try {
                if (!window.ADDI03_CONFIG || !window.ADDI03_CONFIG.checkBookCountUrl) {
                    throw new Error('URL配置未找到');
                }
                if (!grade || grade.trim() === '') {
                    throw new Error('年級不能為空');
                }

                console.log('檢查書籍數量限制:', grade, 'URL:', window.ADDI03_CONFIG.checkBookCountUrl);

                const response = await $.ajax({
                    url: window.ADDI03_CONFIG.checkBookCountUrl,
                    data: { bGRADE: grade },
                    method: 'POST',
                    cache: false,
                    dataType: 'json',
                    timeout: 10000,
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    }
                });

                console.log('檢查書籍數量限制回應:', response);
                return response === true;
            } catch (error) {
                console.error('檢查書籍數量限制失敗:', {
                    error: error,
                    grade: grade,
                    url: window.ADDI03_CONFIG?.checkBookCountUrl,
                    status: error.status,
                    statusText: error.statusText,
                    responseText: error.responseText
                });

                if (error.status === 404) {
                    throw new Error('檢查書籍數量限制的服務端點未找到');
                } else if (error.status === 500) {
                    throw new Error('服務器內部錯誤，請聯繫系統管理員');
                } else if (error.statusText === 'timeout') {
                    throw new Error('請求超時，請稍後再試');
                } else {
                    throw new Error('檢查書籍數量限制時發生錯誤: ' + (error.statusText || '未知錯誤'));
                }
            }
        },

        submitForm: function() {
            const form = document.getElementById('contentForm');
            if (form) {
                form.enctype = "multipart/form-data";
                form.action = "BooKMAInsert";
                form.submit();
            } else {
                console.error('找不到表單元素');
                alert('系統錯誤：找不到表單元素');
            }
        },

        handleSubmit: async function() {
            try {
                // 基本驗證
                const errors = this.validateForm();
                if (errors.length > 0) {
                    alert(errors.join('\r\n'));
                    return;
                }

                const bookId = $('#BOOK_ID').val();
                const bookStatus = $('#BOOK_Status').val();
                const grade = $('#GRADE').val();

                // 如果是新增模式，需要進行額外檢查
                if (bookId !== '' && bookStatus === 'ADD') {
                    try {
                        // 檢查書號是否已存在
                        const bookExists = await this.checkBookId(bookId);
                        if (bookExists) {
                            alert('書號已存在，請重新輸入');
                            return;
                        }

                        // 檢查書籍是否可修改
                        const isModifiable = await this.checkBookIsModify(bookId);
                        if (isModifiable) {
                            $("#hidBOOK_ID").val(bookId);
                            $("#BOOK_Status").val("EDIT");
                            this.submitForm();
                            return;
                        }

                        // 檢查該年級書本上限數
                        const exceedsLimit = await this.checkBookCount(grade);
                        if (exceedsLimit) {
                            alert('超過該年級書本上限數');
                            return;
                        }
                    } catch (error) {
                        alert(error.message);
                        return;
                    }
                }

                // 最終提交
                this.submitForm();

            } catch (error) {
                console.error('提交表單時發生錯誤:', error);
                alert('系統發生錯誤，請稍後再試');
            }
        }
    };

    // 初始化模組
    bookMaintain.init();
});