﻿@model ZZT08
@{
    ViewBag.Title = "密碼更換-密碼更換維護內容";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string FirstChange = "";
    if (user != null)
    {
        if (user.INIT_STATUS == 0 || user.INIT_STATUS == null) { FirstChange = "若是登入「 酷幣平台」的帳號密碼第一次登入系統請改密碼，若是其他平台帳號密碼登入不需修改帳號密碼"; }
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@if (string.IsNullOrEmpty(FirstChange) == false)
{
    <h4>@FirstChange</h4>
}

@using (Html.BeginForm("MODIFY", "ZZT08", FormMethod.Post, new { id = "ZZT08", name = "ZZT08" }))
{
    @Html.HiddenFor(model => model.SCHOOL_NO)
    @Html.HiddenFor(model => model.USER_NO)
    @Html.Hidden("redirectController", (string)ViewBag.redirectController)
    @Html.Hidden("redirectAction", (string)ViewBag.redirectAction)
    
    <input type="hidden" id="STATUS" name="STATUS" value="@Request["STATUS"]" />

    if (user.USER_TYPE == UserType.Student)
    {
        <br />
        <label class="label_dt"> 建議更改密碼領取生日禮金；不改密碼也可以領取。！</label>
        <br />
        <label class="label_dt"> 密碼必須是英數混合6個字以上</label>
    }

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.Label("新密碼", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.Password("newtextPASSWORD", "", new { id = "newtextPASSWORD", @class = "form-control", @placeholder = "必填" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("再次確認密碼", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.Password("AgainPASSWORD", "", new { id = "AgainPASSWORD", @class = "form-control", @placeholder = "必填" })
                    </div>
                </div>
                <div class="text-center">
                    <button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick();">
                        確定送出
                    </button>
                </div>
                <div class="text-center">
                    <button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="notReset();">
                        不改密碼
                    </button>
                </div>
            </div>
        </div>
    </div>
}
@section Scripts
{
    <script type="text/javascript">
        function notReset()

        {
             document.ZZT08.enctype = "multipart/form-data";
                    document.ZZT08.action = '@Url.Action("MODIFYPASSW0RD", "ZZT08",new{ NotModifyStatus ="true"})';
                    document.ZZT08.submit();
        }
        function btnSend_onclick() {
            var strMsg = '';
            try {

                if ($('#newtextPASSWORD').val().length < 6 || $('#AgainPASSWORD').val().length < 6) {
                    strMsg += '密碼需大於6個字\r\n';
                }

                var regExp = /^([a-zA-Z]+\d+|\d+[a-zA-Z]+)[a-zA-Z0-9]*$/; ///^[\d|a-zA-Z]+$/;
                var pw = $('#newtextPASSWORD').val();
                var t = regExp.test(pw);
                if (!regExp.test(pw)) {
                    strMsg += '密碼需要是英文數字混合\r\n';
                }

                if ($('#newtextPASSWORD').val() == '') {
                    strMsg += '新密碼為必填\r\n';
                }

                if ($('#AgainPASSWORD').val() == '') {
                    strMsg += '請再次輸入密碼\r\n';
                }

                if ($('#newtextPASSWORD').val() != $('#AgainPASSWORD').val()) {
                    strMsg += '確認密碼不一致\r\n';
                }

                if (strMsg != '') {
                    alert(strMsg);
                    return false;
                }
                else {
                    document.ZZT08.enctype = "multipart/form-data";
                    document.ZZT08.action = '@Url.Action("MODIFYPASSW0RD", "ZZT08", new { NotModifyStatus = "false" })';
                    document.ZZT08.submit();
                }
            }
            catch (err) {
                alert(err.message)
            }
        }
    </script>
}