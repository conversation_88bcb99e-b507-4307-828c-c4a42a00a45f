﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })


@using (Html.BeginForm("EditOne152", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.USER_NAME)

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.USER_NO, htmlAttributes: new { @class = "control-label col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.USER_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_SEAT, htmlAttributes: new { @class = "control-label col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.CLASS_SEAT, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.CLASS_SEAT, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-4">成績</label>
                <div class="col-md-8">

                    @Html.DropDownListFor(model => model.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.Scoreitems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-4">優良表現</label>

                <div class="col-md-8">
                    @Html.TextAreaFor(model => model.CONTENT_TXT, 5, 100, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CONTENT_TXT, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                    @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div>

    <div style="height:20px"></div>
    <div class="form-group text-center">
        <div class="col-md-offset-4 col-md-3 ">
            <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
                確定給點
            </button>
        </div>
    </div>

    <link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />
    <script src=@Url.Content("~/Scripts/jquery-3.6.4.min.js")></script>
    <script src="~/Scripts/jquery-ui.min.js"></script>
    <div id="dialog-confirm" style="display:none;" title="確認給點？">
        <span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>您確定要給點嗎？
    </div>

    <script type="text/javascript">

        $("#dialog-confirm").dialog({
            resizable: true,
            autoOpen: false,
            height: 200,
            width: 200,
            modal: true,
            buttons: {
                '確認': function () {
                    $('#myButton').hide();
                    form1.submit();
                    $(this).dialog('close');
                },
                '取消': function () {
                    $(this).dialog('close');
                }
            }
        });

        $('#myButton').on('click', function () {
            $('#dialog-confirm').text("您確定要給" + $('#@Html.IdFor(m => m.USER_NAME)').val() + "酷幣點數" + $('#@Html.IdFor(m => m.CASH)').val()+"點嗎?");
            $('#dialog-confirm').dialog('open');
    })


    $('#@Html.IdFor(m => m.USER_NO),#@Html.IdFor(m => m.CLASS_SEAT)').change(function () {

        DbQuery();
    });

    function DbQuery() {

        $.ajax({
            url: "@(Url.Action("ToQueryUser", "ADDI09"))",     // url位置
            type: 'post',                   // post/get
            data: { "USER_NO": $('#@Html.IdFor(m => m.USER_NO)').val(), "CLASS_SEAT": $('#@Html.IdFor(m => m.CLASS_SEAT)').val() },
            dataType: 'json',               // xml/json/script/html
            cache: false,                   // 是否允許快取
            success: function (data) {
                var res = jQuery.parseJSON(data);

                if (res.USER_NO == '') {
                    alert(res.USER_NAME);
                    $('#myButton').text("確定給點");
                }
                else {
                    $('#@Html.IdFor(m => m.USER_NAME)').val(res.USER_NAME);
                    $('#myButton').text("確定給點：" + res.USER_NAME);

                }
            },
            error: function (xhr, err) {
                //alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                //alert("responseText: " + xhr.responseText);
            }

        });

    }

    </script>
}

