﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/buzz/1.2.1/buzz.min.js">
</script>
@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.Partial("_Title_Secondary")
<div style="text-align:center">
    <h3>
        恭喜
    </h3>
    <h3 style="color:blue">
        @Model.USER_NO  @Model.USER_NAME
    </h3>
    <h3>
        小朋友，因為
    </h3>
    <h3 style="color:blue">
        @Model.SUBJECT
    </h3>
    <h3>
        獲得
    </h3>
    <h1 style="color:red">
        @Model.CASH  點
    </h1>
    <h4>
        ( 2秒後會自動跳回功能首頁... )
    </h4>
    <br />
    <a href='@Url.Action(ViewBag.NextAction,new { IsRandom = Model.IsRandom, IsFix = Model.IsFix})' role="button" class="btn btn-sm btn-sys">
        下一筆
    </a>
</div>

<script type="text/javascript">
    
        var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
    var game_trueSound = new buzz.sound("@Url.Content("~/Content/mp3/game_true.mp3")");
    var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );
    $(document).ready(function () {
        SwipeSound.play();
        setInterval(function () {
            // 跳回首頁
            window.location.replace('@Url.Action(ViewBag.NextAction,new { IsRandom = Model.IsRandom, IsFix = Model.IsFix})');
        }, 2000);
    });
    window.history.forward(1);
</script>

