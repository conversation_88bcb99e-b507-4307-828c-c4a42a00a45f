﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'de-ch', {
	euro: 'Euro Zeichen',
	lsquo: 'Hochkomma links',
	rsquo: 'Hochkomma rechts',
	ldquo: 'Anführungszeichen links',
	rdquo: 'Anführungszeichen rechts',
	ndash: 'Kleiner Strich',
	mdash: '<PERSON>ttle<PERSON> Strich',
	iexcl: 'Invertiertes Ausrufezeichen',
	cent: 'Cent-Zei<PERSON>',
	pound: 'Pfund-Zeichen',
	curren: 'Währungszeichen',
	yen: 'Yen',
	brvbar: 'Gestrichelte Linie',
	sect: 'Paragrafenzeichen',
	uml: 'Diäresis',
	copy: 'Copyright-Zeichen',
	ordf: 'Feminine ordinal Anzeige',
	laquo: 'Nach links zeigenden Doppel-<PERSON><PERSON>szei<PERSON>',
	not: 'Not-Zeichen',
	reg: '<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>',
	macr: 'Längezeichen',
	deg: 'Grad-<PERSON>ei<PERSON>',
	sup2: 'Hoch 2',
	sup3: 'Hoch 3',
	acute: 'Akzentzeichen ',
	micro: 'Mikro-Zeichen',
	para: 'Pilcrow-Zeichen',
	middot: 'Mittelpunkt',
	cedil: 'Cedilla',
	sup1: 'Hoch 1',
	ordm: 'Männliche Ordnungszahl Anzeige',
	raquo: 'Nach rechts zeigenden Doppel-Winkel Anführungszeichen',
	frac14: 'ein Viertel',
	frac12: 'Hälfte',
	frac34: 'Dreiviertel',
	iquest: 'Umgekehrtes Fragezeichen',
	Agrave: 'Lateinischer Buchstabe A mit AkzentGrave',
	Aacute: 'Lateinischer Buchstabe A mit Akutakzent',
	Acirc: 'Lateinischer Buchstabe A mit Zirkumflex',
	Atilde: 'Lateinischer Buchstabe A mit Tilde',
	Auml: 'Lateinischer Buchstabe A mit Trema',
	Aring: 'Lateinischer Buchstabe A mit Ring oben',
	AElig: 'Lateinischer Buchstabe Æ',
	Ccedil: 'Lateinischer Buchstabe C mit Cedille',
	Egrave: 'Lateinischer Buchstabe E mit AkzentGrave',
	Eacute: 'Lateinischer Buchstabe E mit Akutakzent',
	Ecirc: 'Lateinischer Buchstabe E mit Zirkumflex',
	Euml: 'Lateinischer Buchstabe E Trema',
	Igrave: 'Lateinischer Buchstabe I mit AkzentGrave',
	Iacute: 'Lateinischer Buchstabe I mit Akutakzent',
	Icirc: 'Lateinischer Buchstabe I mit Zirkumflex',
	Iuml: 'Lateinischer Buchstabe I mit Trema',
	ETH: 'Lateinischer Buchstabe Eth',
	Ntilde: 'Lateinischer Buchstabe N mit Tilde',
	Ograve: 'Lateinischer Buchstabe O mit AkzentGrave',
	Oacute: 'Lateinischer Buchstabe O mit Akutakzent',
	Ocirc: 'Lateinischer Buchstabe O mit Zirkumflex',
	Otilde: 'Lateinischer Buchstabe O mit Tilde',
	Ouml: 'Lateinischer Buchstabe O mit Trema',
	times: 'Multiplikation',
	Oslash: 'Lateinischer Buchstabe O durchgestrichen',
	Ugrave: 'Lateinischer Buchstabe U mit Akzentgrave',
	Uacute: 'Lateinischer Buchstabe U mit Akutakzent',
	Ucirc: 'Lateinischer Buchstabe U mit Zirkumflex',
	Uuml: 'Lateinischer Buchstabe a mit Trema',
	Yacute: 'Lateinischer Buchstabe a mit Akzent',
	THORN: 'Lateinischer Buchstabe mit Dorn',
	szlig: 'Kleiner lateinischer Buchstabe scharfe s',
	agrave: 'Kleiner lateinischer Buchstabe a mit Accent grave',
	aacute: 'Kleiner lateinischer Buchstabe a mit Akut',
	acirc: 'Lateinischer Buchstabe a mit Zirkumflex',
	atilde: 'Lateinischer Buchstabe a mit Tilde',
	auml: 'Kleiner lateinischer Buchstabe a mit Trema',
	aring: 'Kleiner lateinischer Buchstabe a mit Ring oben',
	aelig: 'Lateinischer Buchstabe æ',
	ccedil: 'Kleiner lateinischer Buchstabe c mit Cedille',
	egrave: 'Kleiner lateinischer Buchstabe e mit Accent grave',
	eacute: 'Kleiner lateinischer Buchstabe e mit Akut',
	ecirc: 'Kleiner lateinischer Buchstabe e mit Zirkumflex',
	euml: 'Kleiner lateinischer Buchstabe e mit Trema',
	igrave: 'Kleiner lateinischer Buchstabe i mit AkzentGrave',
	iacute: 'Kleiner lateinischer Buchstabe i mit Akzent',
	icirc: 'Kleiner lateinischer Buchstabe i mit Zirkumflex',
	iuml: 'Kleiner lateinischer Buchstabe i mit Trema',
	eth: 'Kleiner lateinischer Buchstabe eth',
	ntilde: 'Kleiner lateinischer Buchstabe n mit Tilde',
	ograve: 'Kleiner lateinischer Buchstabe o mit Accent grave',
	oacute: 'Kleiner lateinischer Buchstabe o mit Akzent',
	ocirc: 'Kleiner lateinischer Buchstabe o mit Zirkumflex',
	otilde: 'Lateinischer Buchstabe i mit Tilde',
	ouml: 'Kleiner lateinischer Buchstabe o mit Trema',
	divide: 'Divisionszeichen',
	oslash: 'Kleiner lateinischer Buchstabe o durchgestrichen',
	ugrave: 'Kleiner lateinischer Buchstabe u mit Accent grave',
	uacute: 'Kleiner lateinischer Buchstabe u mit Akut',
	ucirc: 'Kleiner lateinischer Buchstabe u mit Zirkumflex',
	uuml: 'Kleiner lateinischer Buchstabe u mit Trema',
	yacute: 'Kleiner lateinischer Buchstabe y mit Akut',
	thorn: 'Kleiner lateinischer Buchstabe Dorn',
	yuml: 'Kleiner lateinischer Buchstabe y mit Trema',
	OElig: 'Lateinischer Buchstabe Ligatur OE',
	oelig: 'Kleiner lateinischer Buchstabe Ligatur OE',
	'372': 'Lateinischer Buchstabe W mit Zirkumflex',
	'374': 'Lateinischer Buchstabe Y mit Zirkumflex',
	'373': 'Kleiner lateinischer Buchstabe w mit Zirkumflex',
	'375': 'Kleiner lateinischer Buchstabe y mit Zirkumflex',
	sbquo: 'Tiefergestelltes Komma',
	'8219': 'Rumgedrehtes Komma',
	bdquo: 'Doppeltes Anführungszeichen unten',
	hellip: 'horizontale Auslassungspunkte',
	trade: 'Handelszeichen',
	'9658': 'Dreickspfeil rechts',
	bull: 'Bullet',
	rarr: 'Pfeil rechts',
	rArr: 'Doppelpfeil rechts',
	hArr: 'Doppelpfeil links',
	diams: 'Karo',
	asymp: 'Ungefähr'
} );
