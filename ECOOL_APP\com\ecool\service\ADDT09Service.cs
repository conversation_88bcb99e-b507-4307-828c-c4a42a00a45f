﻿using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace com.ecool.service
{
    public class ADDT09Service
    {
        public static List<uADDT09> USP_ReadADDT09_QUERY(string USER_NO, string SCHOOL_NO)
        {

            List<uADDT09> list_data = new List<uADDT09>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT SCHOOL_NO,USER_NO,LEVEL_ID,LEVEL_QTY,BOOK_QTY,UP_DATE ");
                sb.Append(" FROM   ADDT09 ");
                sb.Append(" WHERE 1 = 1 ");
                if (SCHOOL_NO != string.Empty)
                {
                    sb.AppendFormat(" AND SCHOOL_NO ='{0}' ",SCHOOL_NO);
                }

                if (USER_NO != string.Empty)
                {
                    sb.AppendFormat(" AND USER_NO ='{0}' ", USER_NO);
                }

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uADDT09()
                    {
                        SCHOOL_NO = dr["SCHOOL_NO"].ToString(),
                        USER_NO = dr["USER_NO"].ToString(),
                        LEVEL_ID = Convert.ToInt32(dr["LEVEL_ID"].ToString()),
                        LEVEL_QTY = Convert.ToInt32(dr["LEVEL_QTY"].ToString()),
                        BOOK_QTY = Convert.ToInt32(dr["BOOK_QTY"].ToString()),
                        UP_DATE = Convert.ToDateTime(dr["UP_DATE"].ToString())
                    });
                }

            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }
    }
}
