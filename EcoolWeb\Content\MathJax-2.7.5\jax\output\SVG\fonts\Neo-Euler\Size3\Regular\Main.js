/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Size3={directory:"Size3/Regular",family:"NeoEulerMathJax_Size3",id:"NEOEULERSIZE3",32:[0,0,333,0,0,""],40:[2199,199,734,208,714,"208 1000c0 445 116 900 455 1199h51l-17 -17c-320 -310 -406 -767 -406 -1182s86 -872 406 -1182l17 -17h-51c-340 300 -455 755 -455 1199"],41:[2199,199,734,20,526,"526 1000c0 -445 -116 -900 -455 -1199h-51l17 17c320 310 406 767 406 1182s-86 872 -406 1182l-17 17h51c340 -300 455 -755 455 -1199"],47:[2199,200,1044,54,992,"941 2197l12 2l34 -5l5 -11l-889 -2383l-46 8l-3 14"],91:[2274,125,527,250,513,"250 -125v2399h263v-55h-208v-2289h208v-55h-263"],92:[2199,200,1044,54,992,"105 2197l-12 2l-34 -5l-5 -11l889 -2383l46 8l3 14"],93:[2274,125,527,14,277,"222 -70v2289h-208v55h263v-2399h-263v55h208"],123:[2199,200,750,131,618,"335 110v594c0 29 -10 174 -188 270c-15 8 -16 9 -16 26c0 13 1 14 3 16s3 3 20 13c91 49 170 137 180 256c1 12 1 62 1 93v462c0 61 0 73 2 88c18 129 123 216 224 262c20 9 22 9 35 9c22 0 22 -1 22 -22c0 -11 0 -14 -3 -17c-1 -1 -2 -2 -19 -10 c-23 -12 -82 -45 -123 -96c-59 -75 -59 -131 -59 -184v-576c0 -66 -40 -204 -227 -295c78 -35 127 -82 160 -122c67 -83 67 -159 67 -207v-576c0 -75 56 -184 184 -245c15 -8 16 -9 18 -10c2 -2 2 -4 2 -17c0 -22 -1 -22 -22 -22c-12 0 -14 0 -31 8 c-75 32 -230 125 -230 302"],124:[2498,208,213,86,126,"126 -200l-40 -8v2698l40 8v-2698"],125:[2199,200,750,131,618,"335 129v576c0 66 40 204 227 295c-78 35 -127 82 -160 122c-67 83 -67 159 -67 207v576c0 47 -28 169 -185 245c-18 10 -19 11 -19 27c0 21 1 22 22 22c12 0 14 0 31 -8c75 -32 230 -125 230 -302v-594c0 -27 9 -176 195 -273c8 -5 9 -6 9 -23c0 -15 0 -17 -8 -21 c-114 -59 -196 -155 -196 -291v-528c0 -61 0 -73 -2 -88c-18 -129 -123 -216 -224 -262c-20 -9 -22 -9 -35 -9c-21 0 -22 2 -22 22c0 16 1 17 22 27c24 12 82 45 123 96c59 75 59 131 59 184"],160:[0,0,333,0,0,""],8214:[2498,208,403,86,316,"316 -200l-40 -8v2698l40 8v-2698zM126 -200l-40 -8v2698l40 8v-2698"],8260:[2199,200,1044,54,992,"941 2197l12 2l34 -5l5 -11l-889 -2383l-46 8l-3 14"],8725:[2199,200,1044,54,992,"941 2197l12 2l34 -5l5 -11l-889 -2383l-46 8l-3 14"],8730:[2402,1,1000,111,1025,"986 2402l39 -9l-565 -2394h-36l-231 1073l-68 -107c-1 1 -14 12 -14 16c0 1 0 3 7 12l131 206l216 -1002h1"],8739:[2498,208,213,86,126,"126 -200l-40 -8v2698l40 8v-2698"],8741:[1897,208,403,86,316,"316 -200l-40 -8v2097l40 8v-2097zM126 -200l-40 -8v2097l40 8v-2097"],8968:[2199,200,583,250,568,"250 -200v2399h318v-55h-263v-2344h-55"],8969:[2199,200,583,14,332,"277 -200v2344h-263v55h318v-2399h-55"],8970:[2199,200,583,250,568,"250 -200v2399h55v-2344h263v-55h-318"],8971:[2199,200,583,14,332,"277 -145v2344h55v-2399h-318v55h263"],9001:[2134,232,757,123,648,"181 951l467 -1163l-50 -20l-475 1183l475 1183l50 -20"],9002:[2134,232,818,100,625,"150 2134l475 -1183l-475 -1183l-50 20l467 1163l-467 1163"],9180:[800,-308,2511,56,2455,"66 308c-10 0 -10 8 -10 22s0 16 12 28c36 40 77 78 100 98c299 254 695 344 1088 344c421 0 879 -104 1184 -440c15 -14 15 -16 15 -30s0 -22 -10 -22c-3 0 -5 0 -8 4c-44 44 -126 126 -268 204c-298 166 -645 202 -914 202c-385 0 -710 -74 -968 -234 c-108 -68 -175 -134 -212 -172c-3 -2 -5 -4 -9 -4"],9181:[248,244,2511,56,2455,"1255 -244c-421 0 -879 106 -1184 440c-15 16 -15 18 -15 32c0 12 0 20 10 20c3 0 6 -2 9 -4c42 -42 125 -124 267 -204c298 -164 645 -200 914 -200c385 0 710 72 968 234c108 66 175 134 211 168c2 2 6 6 10 6c10 0 10 -8 10 -20c0 -14 0 -16 -12 -28 c-36 -42 -77 -80 -100 -100c-299 -254 -695 -344 -1088 -344"],9182:[944,-457,2511,56,2455,"366 740h594c29 0 174 10 270 188c8 15 9 16 26 16c13 0 14 -1 16 -3s3 -3 13 -20c49 -91 137 -170 256 -180c12 -1 62 -1 93 -1h462c61 0 73 0 88 -2c129 -18 216 -123 262 -224c9 -20 9 -22 9 -35c0 -22 -1 -22 -22 -22c-11 0 -14 0 -17 3c-1 1 -2 2 -10 19 c-12 23 -45 82 -96 123c-75 59 -131 59 -184 59h-576c-66 0 -204 40 -295 227c-35 -78 -82 -127 -122 -160c-83 -67 -159 -67 -207 -67h-576c-75 0 -184 -56 -245 -184c-8 -15 -9 -16 -10 -18c-2 -2 -4 -2 -17 -2c-22 0 -22 1 -22 22c0 12 0 14 8 31c32 75 125 230 302 230"],9183:[97,390,2511,56,2455,"385 -107h576c66 0 204 -40 295 -227c35 78 82 127 122 160c83 67 159 67 207 67h576c47 0 169 28 245 185c10 18 11 19 27 19c21 0 22 -1 22 -22c0 -12 0 -14 -8 -31c-32 -75 -125 -230 -302 -230h-594c-27 0 -176 -9 -273 -195c-5 -8 -6 -9 -23 -9c-15 0 -17 0 -21 8 c-59 114 -155 196 -291 196h-528c-61 0 -73 0 -88 2c-129 18 -216 123 -262 224c-9 20 -9 22 -9 35c0 21 2 22 22 22c16 0 17 -1 27 -22c12 -24 45 -82 96 -123c75 -59 131 -59 184 -59"],10216:[1536,234,629,109,520,"160 651l360 -867l-43 -18l-368 885l368 885l43 -18"],10217:[1536,234,693,89,500,"133 1536l367 -885l-367 -885l-44 18l360 867l-360 867"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size3/Regular/Main.js");
