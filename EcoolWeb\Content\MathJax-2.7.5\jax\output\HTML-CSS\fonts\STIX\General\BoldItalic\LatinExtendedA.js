/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/LatinExtendedA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{256:[793,0,667,-68,593],257:[586,14,500,-21,486],258:[885,0,667,-68,593],259:[678,14,500,-21,483],260:[683,173,667,-68,640],261:[462,173,500,-21,507],262:[904,18,667,32,677],263:[697,13,444,-5,392],264:[897,18,667,32,677],265:[690,13,444,-5,415],266:[862,18,667,32,677],267:[655,13,444,-5,392],268:[897,18,667,32,677],269:[690,13,444,-5,437],270:[897,0,722,-46,685],271:[710,13,658,-21,726],272:[669,0,722,-31,700],273:[699,13,500,-21,541],274:[793,0,667,-27,653],275:[586,13,444,5,431],276:[885,0,667,-27,653],277:[678,13,444,5,478],278:[862,0,667,-27,653],279:[655,13,444,5,398],280:[669,182,667,-27,653],281:[462,182,444,5,398],282:[897,0,667,-27,653],283:[690,13,444,5,486],284:[897,18,722,21,705],285:[690,203,500,-52,477],286:[885,18,722,21,705],287:[678,203,500,-52,477],288:[862,18,722,21,705],289:[655,203,500,-52,477],290:[685,359,722,21,705],291:[832,203,500,-52,477],292:[897,0,778,-24,799],293:[897,9,556,-13,498],294:[669,0,778,-24,800],295:[699,9,556,-13,498],296:[862,0,389,-32,470],297:[655,9,278,-9,350],298:[793,0,389,-32,451],299:[586,9,278,-11,331],300:[885,0,389,-32,458],301:[678,9,278,2,328],302:[669,173,389,-32,406],303:[684,173,278,2,262],304:[862,0,389,-32,406],305:[462,9,278,2,238],306:[669,99,823,-32,913],307:[685,207,552,2,544],308:[897,99,500,-46,554],309:[690,207,278,-189,314],310:[669,359,667,-21,702],311:[699,359,500,-23,483],312:[470,0,600,6,689],313:[904,0,611,-22,590],314:[904,9,278,2,344],315:[669,359,611,-22,590],316:[699,359,278,-62,290],317:[685,0,611,-22,667],318:[710,9,451,2,499],319:[669,0,611,-22,590],320:[699,9,375,2,382],321:[669,0,611,-22,590],322:[699,9,278,-13,301],323:[904,15,722,-27,748],324:[697,9,556,-6,494],325:[669,359,722,-27,748],326:[462,359,556,-6,494],327:[897,15,722,-27,748],328:[690,9,556,-6,506],329:[710,9,700,42,657],330:[669,203,722,-46,685],331:[462,207,543,-6,474],332:[793,18,722,27,691],333:[586,13,500,-3,461],334:[885,18,722,27,691],335:[678,13,500,-3,488],336:[904,18,722,27,700],337:[697,13,500,-3,519],338:[677,8,944,23,946],339:[462,13,722,6,674],340:[904,0,667,-28,623],341:[697,0,389,-21,389],342:[669,359,667,-28,623],343:[462,359,389,-102,389],344:[897,0,667,-28,623],345:[690,0,389,-21,411],346:[904,18,556,2,526],347:[697,13,389,-19,379],348:[897,18,556,2,526],349:[690,13,389,-19,367],350:[685,218,556,2,526],351:[462,218,389,-19,333],352:[897,18,556,2,526],353:[690,13,389,-19,411],354:[669,218,611,49,650],355:[594,218,278,-75,289],356:[897,0,611,49,650],357:[710,9,411,-11,499],358:[669,0,611,49,650],359:[594,9,278,-30,281],360:[841,18,722,67,744],361:[655,9,556,15,493],362:[793,18,722,67,744],363:[586,9,556,15,493],364:[885,18,722,67,744],365:[678,9,556,15,493],366:[921,18,722,67,744],367:[729,9,556,15,493],368:[889,18,722,67,744],369:[697,9,556,15,527],370:[669,173,722,67,744],371:[462,173,556,15,531],372:[897,18,889,64,940],373:[690,13,667,15,614],374:[897,0,611,71,659],375:[690,205,444,-94,393],376:[862,0,611,71,659],377:[904,0,611,-12,589],378:[697,78,389,-43,379],379:[862,0,611,-12,589],380:[655,78,389,-43,368],381:[897,0,611,-12,589],382:[690,78,389,-43,411],383:[691,0,333,14,536]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/LatinExtendedA.js");
