﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI29Controller : Controller
    {

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI29";
        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;
        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;


        private ECOOL_DEVEntities Db = new ECOOL_DEVEntities();
        [CheckPermission] //檢查權限
        public ActionResult Index(ZZZI29ListViewModel Data)
        {
            this.Shared("己訂閱好友清單");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (Data == null) Data = new ZZZI29ListViewModel();

            if (Data.Q_SCHOOL_NO == null) Data.Q_SCHOOL_NO = user.SCHOOL_NO;
            if (Data.Q_USER_NO == null) Data.Q_USER_NO = user.USER_NO;

            var temp = from a in Db.HRMT07
                       join me in Db.HRMT01 on new { SCHOOL_NO = a.SCHOOL_NO, USER_NO = a.USER_NO } equals new { SCHOOL_NO = me.SCHOOL_NO, USER_NO = me.USER_NO }
                       join f in Db.HRMT01
                       on new { SCHOOL_NO = a.SCHOOL_NO, USER_NO = a.STUDENT_USER_NO } equals new { SCHOOL_NO = f.SCHOOL_NO, USER_NO = f.USER_NO }
                       where a.SCHOOL_NO == Data.Q_SCHOOL_NO
                       select new ZZZI29QueryViewModel()
                       {
                           HRMT07_SCHOOL_NO = a.SCHOOL_NO,
                           HRMT07_USER_NO = a.USER_NO,
                           HRMT07_NAME = me.NAME,
                           HRMT01ds = f
                       };


            if (string.IsNullOrWhiteSpace(Data.Q_USER_NO) ==false)
            {
                temp = temp.Where(a => a.HRMT07_USER_NO == Data.Q_USER_NO);
            }

            Data.ListQuery = temp.ToList();


            return View(Data);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult ADD()
        {
            this.Shared("新增好友");

            ZZZI29DelViewModel Data = new ZZZI29DelViewModel();

            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, null, Data.CLASS_NO, ref Db);
            ViewBag.USER_NOItems = HRMT01.GetUserNoListNoData(DefaultSCHOOL_NO, Data.CLASS_NO, Data.STUDENT_USER_NO, ref Db);


            return View(Data);
        }


        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult ADD(ZZZI29DelViewModel Data)
        {
            this.Shared("新增好友");

            if (Data == null) Data = new ZZZI29DelViewModel();

  
            string StatusMessage = string.Empty;
            var OK = HRMT07.AddFriendsData(user.SCHOOL_NO, user.USER_NO, Data.STUDENT_USER_NO, out StatusMessage);

            TempData["StatusMessage"] = StatusMessage;

            if (OK)
            {
                return RedirectToAction("Index");
            }

            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, null, Data.CLASS_NO, ref Db);
            ViewBag.USER_NOItems = HRMT01.GetUserNoListNoData(DefaultSCHOOL_NO, Data.CLASS_NO, Data.STUDENT_USER_NO, ref Db);


            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }

            var UserNoListData = HRMT01.GetUserNoListNoData(SCHOOL_NO, CLASS_NO, ref Db);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, USER_NO, true, null);

            return Content(_html);
        }

        [HttpGet]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult DEL(ZZZI29DelViewModel Data)
        {
            this.Shared("刪除");

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }


            if (Data == null)
            {
                return RedirectToAction("NotParameterError", "Error");
            }

            if (Data.STUDENT_USER_NO == null)
            {
                return RedirectToAction("NotParameterError", "Error");
            }


            HRMT07 T07 = Db.HRMT07.Where(a => a.SCHOOL_NO == Data.HRMT07_SCHOOL_NO && a.USER_NO == Data.HRMT07_USER_NO && a.STUDENT_USER_NO == Data.STUDENT_USER_NO).FirstOrDefault();

            if (T07 == null)
            {
                TempData["StatusMessage"] = "此筆資料已不存在";
                return RedirectToAction("Index");
            }

            Db.HRMT07.Remove(T07);

            try
            {
                Db.SaveChanges();
                TempData["StatusMessage"] = "刪除成功";
            }
            catch (Exception ex)
            {
                TempData["StatusMessage"] = "刪除失敗" + ex.Message;
                return RedirectToAction("Index");
            }



            return RedirectToAction("Index");
        }





        #region  Shared
        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }
        #endregion


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Db.Dispose();
            }
            base.Dispose(disposing);
        }

    }
}