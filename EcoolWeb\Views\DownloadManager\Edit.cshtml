﻿@model ECOOL_APP.EF.DLMT01
@using ECOOL_APP;
@using ECOOL_APP.EF;
@using EcoolWeb.Models;
<script src="~/Content/ckeditorAdmin/ckeditor.js"></script>
@using (Html.BeginForm("EditSave", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", @class = "form-horizontal", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.DL_ID)
    @Html.HiddenFor(m => m.STATUS)
    @Html.HiddenFor(m => m.CHG_DATE)
<input id="FileType" name="FileType" type="hidden" value="F">
    <section id="feature" class="feature-section feature-section-2">

        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h3 style="color: #6ba1ab;">
                        <strong>@ViewBag.Panel_Title</strong>
                    </h3>
                </div>
                <!-- /.col-lg-12 -->
            </div>
            <hr style="margin: 19px 0px;">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.Label("順序", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.Orderby, new { htmlAttributes = new { @class = "form-control input-md" } })
                        @Html.ValidationMessageFor(m => m.Orderby, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("文件分類", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.DL_GROUP, new { htmlAttributes = new { @class = "form-control input-md" } })
                        @Html.ValidationMessageFor(m => m.DL_GROUP, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.Label("文件名稱", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.DL_SUBJECT, new { htmlAttributes = new { @class = "form-control input-md" } })
                        @Html.ValidationMessageFor(m => m.DL_SUBJECT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("文件說明", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.TextAreaFor(m => m.DL_MEMO, 6, 200, new { @class = "form-control", @placeholder = "回答內容" })
                        @Html.ValidationMessageFor(m => m.DL_MEMO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("更新日期", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.DisplayFor(m => m.CHG_DATE, new { htmlAttributes = new { @class = "form-control input-md", @type = "text", @placeholder = "格式yyyy/MM/dd" } })
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-4 control-label" for="上傳">上傳</label>
                    <br />
                    @if (Model != null)
                    {
                        if (!string.IsNullOrWhiteSpace(Model.DL_ID))
                        {
                            string TPath = "";
                            string TargetPath = @"~\DownloadManager";// + @"\" + entity.DL_ID + @"\" + DirectoryPath + @"\" + NEWS_ID;
                            TargetPath = TargetPath + @"\DLM\" + Model.DL_ID + @"\" + Model.FILENAME;
                            TPath = Uri.EscapeUriString(UrlCustomHelper.Url_Content(TargetPath));
                            <div class="col-md-4">
                                <a href="@TPath" target="_blank">@Model.FILENAME</a>
                            </div>

                        }
                    }
                    <div class="col-md-4">
                        <input name="files" class="input-file btn btn-sm" type="file" multiple>
                        @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                    </div>
                </div>
                <span class="pull-right">
                    <a style="margin: -15px 0px;" role="button" class="btn btn-success btn-sm" onclick="onSave()">儲存</a>
                    @if (Model != null)
                    {

                        if (!string.IsNullOrWhiteSpace(Model.DL_ID))
                        {

                            <a style="margin: -15px 0px;" role="button" class="btn btn-danger btn-sm" onclick="delete_show()"><em class="fa fa-trash"></em>刪除</a>
                        }
                    }

                    <a style="margin: -15px 0px;" role="button" class="btn btn-success btn-sm" onclick="onBack()">回上一頁</a>
                </span>
            </div>
        </div><!-- /.container -->
    </section><!-- /.feature-section -->
    <div style="height:25px"></div>
}

@section scripts{
    <script type="text/javascript">
        var targetFormID = '#formEdit';

        var opt = {
            showMonthAfterYear: true,
            dateFormat: 'yy/mm/dd',
            timeFormat: 'HH:mm:ss',
            showSecond: true,
            showButtonPanel: true,
            showTime: true,
            beforeShow: function () {
                setTimeout(
                function () {
                    $('#ui-datepicker-div').css("z-index", 15);
                }, 100
                );
            },
            onSelect: function (dateText, inst) {
                $('#' + inst.id).attr('value', dateText);
            }
        };

        CKEDITOR.replace('Data_CONTENT_TXT'
            , { toolbar: 'Image', filebrowserImageUploadUrl: '@Url.Content(@"~/"+ (string)ViewBag.BRE_NO + @"/UploadPicture")' }
            );

        function delete_show() {

            if (confirm("您確定要刪除？") == true) {
                $(targetFormID).attr("action", "@Url.Action("Delete", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }

        function onSave() {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

    </script>
}

