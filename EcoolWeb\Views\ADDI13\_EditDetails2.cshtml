﻿@model BarcodeEditPeopleViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
@Html.Partial("_Notice")
@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG" style="width:50%" class="img-responsive " alt="Responsive image" />*@
@if (Model != null)
{

    using (Html.BeginForm("_EditDetails2", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_self" }))
    {
        @Html.HiddenFor(m => m.SCHOOL_NO)
        @Html.HiddenFor(m => m.SHORT_NAME)
        @Html.HiddenFor(m => m.USER_NO)
        @Html.HiddenFor(m => m.NAME)
        @Html.HiddenFor(m => m.GRADE)
        @Html.HiddenFor(m => m.CLASS_NO)
        @Html.HiddenFor(m => m.SEAT_NO)
        @Html.HiddenFor(m => m.CARD_NO)
        @Html.HiddenFor(m => m.BarCode)
        @Html.HiddenFor(m => m.ROLL_CALL_NAME)
        @Html.HiddenFor(m => m.ROLL_CALL_ID)
        @Html.HiddenFor(m => m.CASH)
        @Html.HiddenFor(m => m.txtUSER_NO)
        @Html.HiddenFor(m => m.txtPASSWORD)

        <table align="center">
            <tr>
                <td align="center">
                    <br /><br />
                          <span style="white-space: nowrap;font-size: 30pt;font-weight: bold; color:blue">

                              恭喜 @Model.SHORT_NAME

                              同學 ，
                          </span>

                    <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
                        因為
                    </span>
                    <span style="white-space: nowrap;font-size: 30pt;font-weight: bold; color:red">
                        @Model.ROLL_CALL_NAME 活動，
                    </span>       <br />
                    <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
                        獲得酷幣點數
                    </span>

                    <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;color:blue">


                        @Model.CASH 點，目前共有 @Model.SumCash 點


                    </span>
                    <br />
                    <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">  如有問題請洽    </span>
                    <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;color:blue"> @Model.NAME </span>

                    <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">  老師</span>
                </td>

            </tr>
            @*<tr>
                <td align="center" style="white-space: nowrap;font-size: 16pt;font-weight: bold;color:blue">
                    <button type="submit" class="btn btn-primary btn-lg" id="GetCash" onclick="ExportSave()">按我領取酷幣點數</button>

                </td>
            </tr>*@
            <tr></tr>

        </table>
    }

}
@section Scripts {
    <script language="JavaScript">
                var targetFormID = '#form1';
        $(document).ready(function () {
            var l = 0;
            l = $("#StatusMessageDiv").length;

            if (l > 0)
            {
                var i = 0;
                $(".row").each(function (obj) {
                    if (i == 0) {
                        console.log($(obj).html())
                    }
                    i++;
                })
            }
            var txtUSER_NO = $("#txtUSER_NO").val();
            var txtPASSWORD = $("#txtPASSWORD").val();
            var SCHOOL_NO1 = $("#SCHOOL_NO1").val();
            var NAME = $("#NAME").val();
            var CARD_NO = $("#CARD_NO").val();
            var BarCode = $("#BarCode").val();
            var ROLL_CALL_NAME = $("#ROLL_CALL_NAME").val();
            var ROLL_CALL_ID = $("#ROLL_CALL_ID").val();
            var CASH = $("#CASH").val();
            var data = {
                "txtUSER_NO": txtUSER_NO,
                "txtPASSWORD": txtPASSWORD,
                "SCHOOL_NO": SCHOOL_NO1,
                "NAME": NAME,
                "CARD_NO": CARD_NO,
                "BarCode": BarCode,
                "ROLL_CALL_NAME": ROLL_CALL_NAME,
                "ROLL_CALL_ID": ROLL_CALL_ID,
                "CASH": CASH
            };
            $("#SCHOOL_NO").val("@Model.SCHOOL_NO");
            $("#GetCash").attr("disabled", "disabled");
            $.ajax({
                     url: '@Url.Action("GetCASHFINAL", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    console.log(html);
                    $("#DetailsView").html('');
                    $("#DetailsView").html(html);
                }


            });
           
        });
        function GetInfo() {
            $("#GetCash").attr("disabled", "disabled");
              $(targetFormID).attr("action", "@Url.Action("GetCASHFINAL", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();

        }
       function ExportSave()
       {
           $("#GetCash").attr("disabled", "disabled");
            $(targetFormID).attr("action", "@Url.Action("GetCASHFINAL", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}