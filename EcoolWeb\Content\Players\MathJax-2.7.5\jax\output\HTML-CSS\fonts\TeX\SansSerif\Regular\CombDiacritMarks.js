/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Regular/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_SansSerif,{768:[694,-527,0,-417,-199],769:[694,-527,0,-302,-84],770:[694,-527,0,-422,-79],771:[677,-543,0,-417,-84],772:[631,-552,0,-431,-70],774:[694,-508,0,-427,-74],775:[680,-576,0,-302,-198],776:[680,-582,0,-397,-104],778:[694,-527,0,-319,-99],779:[694,-527,0,-399,-84],780:[654,-487,0,-422,-79]});MathJax.<PERSON>.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Regular/CombDiacritMarks.js");
