/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Typewriter/Regular/Other.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Typewriter,{160:[0,0,250,0,0],305:[431,-1,525,72,462],567:[431,228,525,48,376],915:[611,0,525,25,488],916:[623,0,525,35,489],920:[621,10,525,56,468],923:[623,-1,525,30,495],926:[611,-1,525,33,491],928:[611,-1,525,16,508],931:[611,-1,525,40,484],933:[622,-1,525,38,486],934:[611,-1,525,41,483],936:[611,-1,525,37,487],937:[622,-1,525,32,492],2018:[611,-287,525,175,349],2019:[681,-357,525,176,350],8242:[623,-334,525,211,313]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Typewriter/Regular/Other.js");
