﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace com.ecool.service
{
    public class ZZZI06Service
    {
        public DataTable USPZZZI06_VIEWMODEL_QUERY(string sidx, string sord, int page, int pageSize)
        {

            DataTable dt = new DataTable();
            StringBuilder sb = new StringBuilder();
            try
            {
                int startIndex = ((page - 1) * pageSize) + 1;
                int endIndex = page * pageSize;
                sb.Append("  WITH A046 AS ");
                sb.Append(" ( ");
                sb.Append(" SELECT Distinct A06.SYEAR,A06.SEMESTER,A06.CLASS_NO,A06.SEAT_NO,A06.SNAME,A04.SCHOOL_NO,A04.USER_NO,A04.GRADE,A04.PASS_DATE,A04.ADDT04_NO ");
                sb.Append(" FROM ADDT04 A04 ");
                sb.Append(" LEFT JOIN ADDT06 A06 ON A04.SCHOOL_NO=A06.SCHOOL_NO AND A04.USER_NO=A06.USER_NO ");
                sb.Append(" WHERE A04.PASS_YN='N' ");
                sb.Append(" ) ");
                sb.Append(" , ");
                sb.Append(" A089 AS ");
                sb.Append(" ( ");
                sb.Append(" SELECT LEVEL_DESC,USER_NO,A08.SCHOOL_NO ");
                sb.Append(" FROM ADDT09 A09 ");
                sb.Append(" LEFT JOIN  ADDT08 A08 ON A09.SCHOOL_NO=A08.SCHOOL_NO AND A09.LEVEL_ID=A08.LEVEL_ID  ");
                sb.Append(" ) ");
                sb.Append(" , ");
                sb.Append(" ASZZZI06 AS ");
                sb.Append(" ( ");
                sb.Append(" SELECT A046.ADDT04_NO,A046.SYEAR,A046.SEMESTER,CASE A046.SEMESTER WHEN 1 THEN'上' WHEN 2 THEN'下' END AS CSEMESTER,A046.CLASS_NO,A046.SEAT_NO,A046.SNAME,A089.LEVEL_DESC,A046.GRADE,CONVERT(varchar(12), A046.PASS_DATE, 111 ) as PASS_DATE,ROW_NUMBER() OVER (ORDER BY " + sidx + @" " + sord + @") AS ZZZI06,A046.SCHOOL_NO,A046.USER_NO ");
                sb.Append(" FROM A046  ");
                sb.Append(" LEFT JOIN A089 ON A046.USER_NO=A089.USER_NO AND A046.SCHOOL_NO=A089.SCHOOL_NO ");
                sb.Append(" ) ");
                sb.Append(" SELECT ADDT04_NO,SYEAR,CSEMESTER,CLASS_NO,SEAT_NO,SNAME,LEVEL_DESC,PASS_DATE,SCHOOL_NO,USER_NO,GRADE ");
                sb.Append(" FROM ASZZZI06 ");
                sb.Append(" WHERE ZZZI06 BETWEEN " + startIndex + @" AND " + endIndex + @";");
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return dt;
        }
    }
}
