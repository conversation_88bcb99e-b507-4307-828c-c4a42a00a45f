/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/PhoneticExtensions.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{7424:[468,0,510,15,495],7431:[464,0,504,21,481],7452:[464,14,583,21,560],7553:[683,287,528,27,491],7556:[683,287,542,7,505],7557:[683,287,294,19,257],7562:[459,287,389,51,348],7565:[450,287,516,17,479],7566:[450,287,453,27,416],7576:[755,-425,441,57,387],7587:[757,-279,480,64,398]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/PhoneticExtensions.js");
