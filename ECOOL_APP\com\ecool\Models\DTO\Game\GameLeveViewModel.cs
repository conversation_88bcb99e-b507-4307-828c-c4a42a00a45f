﻿using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameLeveViewModel
    {
        public bool IsOK { get; set; }
        public bool IsQRCODE { get; set; }
        public string GAME_NO { get; set; }
        public bool Y_REPEAT { get; set; }
        [DisplayName("獎品關卡順序")]
        public string Coupons_ITem { get; set; }
        public string SCHOOL_NO { get; set; }
        public string LEVEL_NO { get; set; }
        public string Message { get; set; }
        public int CASH_AVAILABLE { get; set; }

        /// <summary>
        /// 個人資訊
        /// </summary>
        public ADDT27 User { get; set; }

        /// <summary>
        /// 使用者資訊
        /// </summary>
        [DisplayName("使用者資訊")]
        [Required]
        public string GameUserID { get; set; }

        /// <summary>
        /// 主檔
        /// </summary>
        public GameEditMainViewModel Main { get; set; }

        /// <summary>
        /// 明細
        /// </summary>
        public GameEditDetailsViewModel Details { get; set; }

        public GameLotteryPrizeViewModel LotteryPrize { get; set; }

        public virtual ICollection<GetWinnerItemViewModel> WinnerItemDetails { get; set; }
    }
}