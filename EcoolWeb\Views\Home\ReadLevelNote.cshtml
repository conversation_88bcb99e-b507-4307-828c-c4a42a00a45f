﻿@model ECOOL_APP.EF.ADDT09_HIS
@{
    ViewBag.Title = "閱讀認證升級";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string ImagePath = Url.Content("~/Content/img/coolreader-02.png");
    string SchoolName = EcoolWeb.Models.UserProfileHelper.GetSchoolName();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@if (ViewBag.GetDataListShowYN == true || ViewBag.GetALLDataListShowYN == true)
{
    if (ViewBag.RemindItems != null || ViewBag.ALLRemindItems != null)
    {
        <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">

            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    @if (ViewBag.GetALLDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.ALLRemindItems as List<BDMT02_REF>)
                        {
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT
                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">

                                    @item.CONTENT_TXT<br />
                                </div>
                            }
                        }
                    }
                    @if (ViewBag.GetDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.RemindItems as List<BDMT02_REF>)
                        {

                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT
                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">

                                    @item.CONTENT_TXT
                                    <br />
                                </div>
                            }

                        }
                    }
                </div>
            </div>
        </div>
    }

}

        <div style="width:600px;">

            <table align="center">
                <tr>
                    <td>
                        @if (AppMode == false)
                        {
                            <video id="SlotPlayer" style="width:400px ;height: auto" autoplay="autoplay">
                                <source src='~/Content/mp4/ReadUp.mp4' type="video/mp4">
                            </video>
                        }

                    </td>
                </tr>
                <tr>
                    <td>
                        <span style="white-space: nowrap;font-size: 12pt;font-weight: bold; color:#333333;">
                            小朋友恭喜你～<br />
                            恭喜你獲得臺北e閱讀認證，並得到
                            @ViewBag.ReadLevelCash<text>點酷幣！</text><br />
                            請按下確認鍵，右邊顯示您所達成的閱讀護照等級！
                        </span>
                    </td>
                </tr>
                <tr>
                    <td align="center" style="white-space: nowrap;font-size: 16pt;font-weight: bold;color:blue">
                        <a href="@Url.Action("ReadLevelNoteGet", "Home")" role="button" class="btn btn-default">
                            按我確認
                        </a>
                    </td>
                </tr>
            </table>
            @if (AppMode)
            {
                <div style="position:relative">

                    <img style="width:400px ;height: auto" src="~/Content/mp4/coolReedUp.gif" />
                </div>}
                <div style="text-align:center;width:590px;height:441px;background-image:url('@Url.Content(ImagePath)')">
                    @*<img src='@ImagePath' style="z-index:-1;position:absolute;" />*@
                    <div style="font-size:16pt;font-weight:bold ;color:#333333;position:relative;top:150px">
                        閱讀認證榮譽獎
                    </div>
                    <div style="width:500px;position:relative;top:170px;left:50px;text-align:left;white-space: nowrap;font-size: 12pt; color:#333333;white-space:normal;padding-right:10px;padding-left:10px">

                        恭喜&nbsp;@SchoolName &nbsp;<span style="color:blue">@user.NAME </span> &nbsp;小朋友閱讀好書並撰寫<br />
                        閱讀心得報告 @Model.LEVEL_QTY  &nbsp;篇，於 @Model.UP_DATE.Value.ToString("yyyy年MM月dd日") 晉升到第 @Model.LEVEL_ID 級 <span style="color:blue"> @ViewBag.ReadLEVEL_DESC </span>  ！<br />

                        閱讀各種不同領域的書籍，不但可以充實知識，還能將知識融會貫通，希望愛看書的你繼續保持閱讀的好習慣，為自己加油喔！
                    </div>
                </div>
            </div>
            <script type="text/javascript">
                window.history.forward(1);
                window.onload = function () {
                    RemindShow()
                }
                function RemindShow() {
                    var remind_font = $("#remind-content").text().length;

                    if (remind_font > 0) {
                        $('#remind-modal').modal('show');
                    }
                }
            </script>
