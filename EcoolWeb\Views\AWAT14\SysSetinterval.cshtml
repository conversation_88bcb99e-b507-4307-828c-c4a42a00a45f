﻿@model ECOOL_APP.EF.AWAT14SysSetIntervalViewModel
@{
    ViewBag.Title = "酷幣點數升級名單-參數設定";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string HidStyle = "width:100%;font-size:15px;";
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div>


    @**說明視窗*@
    <div id='container' style="@HidStyle">
        @{
            Html.RenderAction("_PageMenu", new { NowAction = "SysSetinterval" });
            string Explain1 = "1.說明：系統預設為1000點為一個級數，但可以依據貴校需求更改級數的點數。";
            string Explain2 = "2.一旦設定後請盡量不要更改以免資料錯誤";

        }
        @Html.Raw(HttpUtility.HtmlDecode(@Explain1))<br />
        @Html.Raw(HttpUtility.HtmlDecode(@Explain2))
    </div>
</div>

@using (Html.BeginForm("SysSetinterval", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.SCHOOL_NO)
<div class="panel panel-ZZZ" name="TOP">

    <div class="panel-heading text-center">
        @Html.BarTitle(null, "級距-參數設定")
    </div>
  
        <div class="form-group row" style="margin-top:50px;">
            @Html.LabelFor(m => m.AWAT14_CASH, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
            <div class="col-md-8 control-label ">
                @Html.EditorFor(m => m.AWAT14_CASH, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "請填數字，最小可輸入0" } })
            </div>
            <div class="col-md-8 control-label ">
                @Html.ValidationMessageFor(m => m.AWAT14_CASH, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="text-center">
            <hr />
            <button type="button" class="btn btn-default" onclick="onSave()">
                <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
            </button>
        </div>
    </div>
    }
    @section Scripts {
        <script language="JavaScript">
        var targetFormID = '#form1';
         function onSave()
         {
        
             var  confirmYN = confirm("你確定要更改嗎？一旦設定後請盡量不要更改以免資料錯誤");

             if (confirmYN) { 
             $(targetFormID).attr("action", "@Url.Action("SysSetintervalSave", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
             }
        }


        </script>
    }
