﻿@model GameCashQueryViewModel

<link href="~/Content/css/bootstrap.min.css" rel="stylesheet" />
<link href="~/Content/css/EzCss.css" rel="stylesheet" />

@if (!string.IsNullOrWhiteSpace((string)(TempData["StatusMessage"] ?? "")))
{
    <h1 style="color:red">@Html.Raw(HttpUtility.HtmlDecode((string)TempData["StatusMessage"]))</h1>
}
else
{
    <div id="ALL" class="containerEZ">
        <h1 style="color:red;text-align:center">
            <strong> 姓名:  @Model.User.NAME</strong>
            <br />
            <strong> 目前點數 : @Model.CASH_AVAILABLE</strong>
        </h1>
        <br />
        <h4 style="color:blue"><strong>點數歷史記錄 :</strong></h4>
        <div>
            <table id="table-breakpoint" class="table table-striped">
                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(model => model.CashDetails.First().LOG_TIME)
                        </th>

                        <th>
                            @Html.DisplayNameFor(model => model.CashDetails.First().LOG_DESC)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.CashDetails.First().CASH_IN)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.CashDetails.First().LOG_CASH_AVAILABLE)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.CashDetails)
                    {
                        <tr>

                            <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_TIME)">
                                @Html.DisplayFor(modelItem => item.LOG_TIME)
                            </td>
                            <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_DESC)">
                                @Html.DisplayFor(modelItem => item.LOG_DESC)
                            </td>
                            <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().CASH_IN)">
                                @Html.DisplayFor(modelItem => item.CASH_IN)
                            </td>
                            <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_CASH_AVAILABLE)">
                                @Html.DisplayFor(modelItem => item.LOG_CASH_AVAILABLE)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <h4 style="color:blue"><strong>中獎清單 :</strong></h4>
        <div>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>
                            獎項
                        </th>
                        <th>
                            是否領獎
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.LotteryDetails?.Count > 0)
                    {
                        foreach (var item in Model.LotteryDetails)
                        {
                            <tr>

                                <td>
                                    @Html.DisplayFor(modelItem => item.LOTTERY_DESC)
                                </td>
                                <td>
                                    @if (item.RECEIVE_AWARD)
                                    {
                                        <text>V</text>
                                    }
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="2" align="center"><h5 style="color:red"><strong>未中任何獎項</strong></h5> </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <h4 style="color:blue;padding-top:20px">
            <strong>
                已完成 <span class="badge">@(Model.LevelPassCount)</span> 關，未完成  <span class="badge"> @(Model.UnLevelPassCount)</span> 關

                @if (Model.UnLevelPassCount > 0)
                {

                }
                else
                {
                    <font style="color:red">，恭喜您，全部的關卡都通過</font>
                }
                :
            </strong>
        </h4>
        <div class="row">
            @foreach (var item in Model.MeLevelDetails)
            {

                <div class="col-md-4" style="color:red">
                    @if (item.IsRecommendLevel)
                    {
                        <div class="alert alert-warning">@item.LEVEL_NAME</div>
                    }
                    else if (item.MeIsLevelPass)
                    {
                        <div class="alert alert-success">
                            @item.LEVEL_NAME
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            @item.LEVEL_NAME
                        </div>
                    }
                </div>
            }
        </div>
    </div>
}