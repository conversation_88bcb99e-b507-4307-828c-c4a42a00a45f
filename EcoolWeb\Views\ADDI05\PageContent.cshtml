﻿@model IPagedList<ECOOL_APP.com.ecool.Models.DTO.ADDI05IndexViewModel>

@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
}

@if (ViewBag.AddDialog == "Y")
{
    @Html.AntiForgeryToken()
    <a role="button" onclick="onAdd()" class="btn btn-sm btn-sys">
        新增有獎徵答活動
    </a>
    @Html.Hidden("VIEW_DATA_TYPE")
}<br />
<font style="color:red">有獎徵答活動，如果有指定回答對象，則只有被指定的人、承辦人員和管理者能在列表中看到此活動。</font>
<div class="form-inline" role="form">
    <div class="form-group">
        <label class="control-label">
            @Html.DisplayNameFor(model => model.First().uADDT11.SYEAR)
            /@Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_NAME)
            /@Html.DisplayNameFor(model => model.First().uADDT11.SNAME)
            /@Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_SDATE)
            /@Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_EDATE)
        </label>
    </div>
    <div class="form-group">
        <input class="form-control " type="text" placeholder="搜尋欲瀏覽之相關字串" name="SearchContents" value="@TempData["SearchContents"]">
    </div>

    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    <br />
    <div class="form-group">
        <label class="control-label">
            @Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_TYPE)
        </label>
    </div>
    <div class="form-group">
        @Html.DropDownList("DIALOG_TYPE", (IEnumerable<SelectListItem>)ViewBag.DialogTypeItems, new { @class = "form-control input-sm" })
    </div>
</div>
<br />
<div class="text-right" style="padding-left: 0px">
    <button class="btn btn-xs btn-pink  @((byte?)TempData["whereShowData"]==((byte)ADDT11.ShowDateType.全部) ? "active":"") ? " active":"")" type="button" onclick="doSearchBool('whereShowData','@((byte)ADDT11.ShowDateType.全部)');" title="全部(不含尚未開始)">全部(不含尚未開始)</button>
    <button class="btn btn-xs btn-pink  @((byte?)TempData["whereShowData"]==((byte)ADDT11.ShowDateType.實施中) ? "active":"")" type="button" onclick="doSearchBool('whereShowData', '@((byte)ADDT11.ShowDateType.實施中)');">@ADDT11.ShowDateType.實施中</button>
    <button class="btn btn-xs btn-pink  @((byte?)TempData["whereShowData"]==((byte)ADDT11.ShowDateType.已截止) ? "active":"")" type="button" onclick="doSearchBool('whereShowData', '@((byte)ADDT11.ShowDateType.已截止)');">@ADDT11.ShowDateType.已截止</button>
    <button class="btn btn-xs btn-pink  @((byte?)TempData["whereShowData"]==((byte)ADDT11.ShowDateType.隱藏刪除) ? "active":"")" type="button" onclick="doSearchBool('whereShowData', '@((byte)ADDT11.ShowDateType.隱藏刪除)');">隱藏(刪除)</button>
    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
        {
            <button class="btn btn-xs btn-pink @((byte?)TempData["whereShowData"]==((byte)ADDT11.ShowDateType.尚未開始) ? "active":"")" type="button" onclick="doSearchBool('whereShowData', '@((byte)ADDT11.ShowDateType.尚未開始)');">@ADDT11.ShowDateType.尚未開始</button>
        }
    }
</div>
<img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover table-ecool-ADDO05">
        <thead>
            <tr>
                <th style="cursor:pointer;" onclick="FunSort('SYEAR')">
                    @Html.DisplayNameFor(model => model.First().uADDT11.SYEAR)
                </th>
                <th style="cursor:pointer;" onclick="FunSort('SEMESTER')">
                    @Html.DisplayNameFor(model => model.First().uADDT11.SEMESTER)
                </th>
                <th style="cursor:pointer;" onclick="FunSort('DIALOG_TYPE')">
                    @Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_TYPE)
                </th>
                <th style="cursor:pointer;" onclick="FunSort('DIALOG_NAME')">
                    @Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_NAME)
                </th>
                <th style="cursor:pointer;" onclick="FunSort('SNAME')">
                    @Html.DisplayNameFor(model => model.First().uADDT11.SNAME)
                </th>
                <th style="cursor:pointer;">
                    <span onclick="FunSort('DIALOG_SDATE')">@Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_SDATE)</span>~ <br />
                    <span onclick="FunSort('DIALOG_EDATE')">@Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_EDATE)</span>
                </th>
                @*<th style="cursor:pointer;" onclick="FunSort('DIALOG_EDATE')">
                        @Html.DisplayNameFor(model => model.First().uADDT11.DIALOG_EDATE)
                    </th>*@
                <th>
                    @Html.DisplayNameFor(model => model.First().MEMO)
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                bool ISASN = false;
                if (user != null) {
                    ISASN = db.ADDT13.Where(a => a.DIALOG_ID == item.uADDT11.DIALOG_ID && a.USER_NO == user.USER_NO && a.SCHOOL_NO == user.SCHOOL_NO).Any();
                }
            <tr>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.uADDT11.SYEAR)
                </td>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.uADDT11.SEMESTER)
                </td>
                <td align="center">
                    @ADDT11.GetDialogTypeDesc((item.uADDT11?.DIALOG_TYPE))
                </td>
                <td>
                    <a href="#" role="button" class="btn-table-link" onclick="onBtnLink('@item.uADDT11.DIALOG_ID')">
                        @item.uADDT11.DIALOG_NAME
                    </a>
                </td>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.uADDT11.SNAME)
                </td>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.uADDT11.DIALOG_SDATE)~ @Html.DisplayFor(modelItem => item.uADDT11.DIALOG_EDATE)
                </td>

                <td align="center">
                    @Html.DisplayFor(modelItem => item.MEMO)
                    @if (Html.DisplayFor(modelItem => item.MEMO) == null || Html.DisplayFor(modelItem => item.MEMO).ToString() == "" &&!ISASN)
                    {

                        <span class="badge">New</span>
                    }

                </td>
              

            </tr>
            }
        </tbody>
    </table>
</div>

<div>
    @Html.Pager(Model.PageSize, Model.PageNumber, Model.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
        .MaxNrOfPages(5)
        .SetPreviousPageText("上頁")
        .SetNextPageText("下頁")
        .AlwaysAddFirstPageNumber()
        )

    @Html.Hidden("OrderByName", (string)TempData["OrderByName"])
    @Html.Hidden("SyntaxName", (string)TempData["SyntaxName"])
    @Html.Hidden("page", (int?)TempData["page"])
    @Html.Hidden("DIALOG_ID")
    @Html.Hidden("whereShowData", (byte?)TempData["whereShowData"])
</div>