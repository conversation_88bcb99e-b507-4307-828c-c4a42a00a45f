﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'et', {
	border: 'Joone suurus',
	caption: '<PERSON><PERSON><PERSON> tiitel',
	cell: {
		menu: 'Lahter',
		insertBefore: '<PERSON><PERSON><PERSON> lahter enne',
		insertAfter: '<PERSON><PERSON><PERSON> lahter peale',
		deleteCell: '<PERSON><PERSON><PERSON> lahtrid',
		merge: 'Ühenda lahtrid',
		mergeRight: 'Ühenda paremale',
		mergeDown: 'Ühenda alla',
		splitHorizontal: '<PERSON><PERSON> lahter horisontaalselt',
		splitVertical: '<PERSON><PERSON> lahter vertikaalselt',
		title: '<PERSON><PERSON>ri omadused',
		cellType: 'Lahtri liik',
		rowSpan: 'Ridade vahe',
		colSpan: 'Tulpade vahe',
		wordWrap: 'Sõnade murdmine',
		hAlign: '<PERSON>risontaal<PERSON> joondus',
		vAlign: 'Vert<PERSON><PERSON><PERSON> joondus',
		alignBaseline: '<PERSON><PERSON><PERSON><PERSON>',
		bgColor: 'Tau<PERSON> värv',
		borderColor: 'Äärise värv',
		data: 'Andmed',
		header: 'Pä<PERSON>',
		yes: 'Jah',
		no: 'Ei',
		invalidWidth: 'Lahtri laius peab olema number.',
		invalidHeight: 'Lahtri kõrgus peab olema number.',
		invalidRowSpan: 'Ridade vahe peab olema täisarv.',
		invalidColSpan: 'Tulpade vahe peab olema täisarv.',
		chooseColor: 'Vali'
	},
	cellPad: 'Lahtri täidis',
	cellSpace: 'Lahtri vahe',
	column: {
		menu: 'Veerg',
		insertBefore: 'Sisesta veerg enne',
		insertAfter: 'Sisesta veerg peale',
		deleteColumn: 'Eemalda veerud'
	},
	columns: 'Veerud',
	deleteTable: 'Kustuta tabel',
	headers: 'Päised',
	headersBoth: 'Mõlemad',
	headersColumn: 'Esimene tulp',
	headersNone: 'Puudub',
	headersRow: 'Esimene rida',
	invalidBorder: 'Äärise suurus peab olema number.',
	invalidCellPadding: 'Lahtrite polsterdus (padding) peab olema positiivne arv.',
	invalidCellSpacing: 'Lahtrite vahe peab olema positiivne arv.',
	invalidCols: 'Tulpade arv peab olema nullist suurem.',
	invalidHeight: 'Tabeli kõrgus peab olema number.',
	invalidRows: 'Ridade arv peab olema nullist suurem.',
	invalidWidth: 'Tabeli laius peab olema number.',
	menu: 'Tabeli omadused',
	row: {
		menu: 'Rida',
		insertBefore: 'Sisesta rida enne',
		insertAfter: 'Sisesta rida peale',
		deleteRow: 'Eemalda read'
	},
	rows: 'Read',
	summary: 'Kokkuvõte',
	title: 'Tabeli omadused',
	toolbar: 'Tabel',
	widthPc: 'protsenti',
	widthPx: 'pikslit',
	widthUnit: 'laiuse ühik'
} );
