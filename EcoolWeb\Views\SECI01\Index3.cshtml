﻿@model BarcCodeMyCashIndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    /**/

    Layout = null;
    string unicornPhoto_sm = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", Model.Level, "sm"));
    string unicornPhoto_bg = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", Model.Level, "lg"));
    string unicornVedio = Url.Content(string.Format("~/Content/img/unicorn/Momo_L1-7_Gif-21-Render_480p .mp4"));
    string bak = Url.Content("~/assets/img/atm_display_hor.png");
}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<!DOCTYPE html>
<html style="height: 100%;">
<head>
    <link rel="stylesheet" href="~/assets/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="~/Content/css/bootstrap.css">

    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link rel="stylesheet" href="~/assets/css/stylesATM.css">
    <style>
        #board_text_size-a4 {
            font-size: 130%;
            padding: 15% 0% 0% 1%;
        }

        .col-md-3 w-100 p-3 {
            width: 150px;
            height: 150px;
            display: block;
            background-color: #eb9700;
            position: relative
        }

        .glyphicon {
            position: relative;
            top: 1px;
            display: inline-block;
            font-family: 'Glyphicons Halflings';
            -webkit-font-smoothing: antialiased;
            font-style: normal;
            font-weight: normal;
            line-height: 1;
        }
    </style>
</head>
<body style="height: 100%;  ">
    <div class="container-fluid" style="height: 100%;overflow: hidden; padding-left:30px;padding-right:30px;padding-bottom:30px">

        <div class="row align-items-center" style=" margin-top:50px;height: 70%;">
            <div class="col-md-2 w-100 p-3" style="background-color: #23b375;height:100%;">
                <div class="text-center">
                    <br /> <br />
                    <span style="font-size:20px">     <b> @Model.UserNAME,第 @Model.Level 級 </b>   </span>
                </div>
                <br />
                <div class="text-center">
                    <span style="font-size:20px">   <b> @Model.CLASS_NO 班  @Model.SEAT_NO 號 </b></span>
                </div>
                <div class="text-center m2" style="  top: calc(50% - 50px); left: calc(50% - 140px); position: absolute;">

                    <img class="img-responsive mycolorbox" src="@unicornPhoto_sm" href="@unicornPhoto_bg" />
                </div>

                <div class="text-white text-center" style="font-size:90% ;  top: calc(85% - 10px); left: calc(40% );position: absolute; color:#000000;font-size:20px">
                    <b>我的默默 @Model.Level 級</b>
                </div>
            </div>
            <div class="col-md-8 w-100 p-3" style="background-color: #23b375;height:100% ;border-right:1px solid #000000;border-left:1px solid #000000">

                <p class="alert alert-success" id="board_text_size-a4" style="padding:5px;color:#5E86C1;margin-top:calc(60% - 700px);margin-left:200px;margin-right:200px">

                    <span><span class="glyphicon glyphicon-user"></span> @Model.UserNAME</span>
                    &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;

                    <span style="padding-left: 140px;">
                        <img src="~/Content/img/web-revise-secretary-08.png" width="25" height="25" />
                        目前酷幣: <span style="color:#ff0000;">@Model.UserCash</span> 點
                    </span>
                    &nbsp;&nbsp;      &nbsp;&nbsp;      &nbsp;&nbsp;
                    <span class="glyphicon glyphicon-usd text-lightgreen" style="padding-left: 160px;"></span>
                    <span style="color: #007bff">
                        定存酷幣:
                    </span>
                    <span style="color:#ff0000"> @(string.Format("{0:0.###} 點", Model.UserAWAI07))</span>

                    <br>
                </p>
                <div id="run" style="margin-left:200px;margin-right:200px;font-size:20px">
                    <span class="glyphicon glyphicon-flash text-lightgreen" style="color:#23f0ad"></span>

                    <span class="" id="board_text_size-a4" style="text-decoration: underline;">
                        <span style="color:#007bff"> 運動撲滿: </span>
                        @{
                            string run_total = (Convert.ToDouble(Model.RUN_TOTAL_METER ?? 0) / 1000.000).ToString("#,0.000");
                            int pos = run_total.IndexOf(".");
                            string run_km = run_total.Substring(0, pos);
                            string run_m = run_total.Substring(pos + 1, run_total.Length - pos - 1);
                        }
                        <span style="color:#ff0000;">
                            @(string.Format("{0}公里{1}公尺", run_km, run_m))
                        </span>
                        @("," + (string)ViewBag.RUN_UPGRADE_RESULTSTR)
                    </span>
                </div>
                <br>
                <br>   <br>
                <br>
                @if (Model.NoBook != "Y")
                {
                    <div id="run" style="margin-left:200px;margin-right:200px;font-size:20px">
                        <span class="glyphicon glyphicon-book text-lightgreen" style="color:#23f0ad"></span>
                        <span class="" id="board_text_size-a4" style="text-decoration: underline;">
                            <span style="color:#007bff">
                                借圖書量:
                            </span>總借書@(Model.AllBookQty) 本，本學期借 @(Model.UserBookQty) 本，本月份借 <span style="color:#ff0000">@(Model.MonthBookQty)</span> 本
                        </span>
                    </div>
                    <br>
                    <br> <br>
                    <br>

                }
                <div id="run" style="margin-left:200px;margin-right:200px;font-size:20px">
                    <span class="glyphicon glyphicon-bookmark text-lightgreen" style="color:#23f0ad"></span>
                    <span class="" id="board_text_size-a4" style="text-decoration: underline;">
                        <span style="color:#007bff">
                            閱讀認證:
                        </span>認證 @(Model.MyData.BOOK_QTY ?? 0) 篇，是第 <span>@(Model.MyData.LEVEL_ID ?? "0")</span> 級 @(Model.MyData.LEVEL_DESC)，再認證 @(Model.MyData.UNLEVEL_QTY == null ? "5" : Model.MyData.UNLEVEL_QTY) 本書就可以升級囉!
                    </span>
                </div>
                <br>
                <br>
                <div id="board_text_frame_a4_b2" style="margin-left:200px;margin-right:200px;font-size:20px">
                    <span style="color:#ffffff;">
                        ※公式：酷幣+定存+運動公里*20+本月借書*100 =
                    </span>

                    <span style="color:#ff0000;">
                        @Model.point
                    </span>
                    <span style="color:#ff0000;padding-left: 5px;">
                        默默 @Model.Level 級
                    </span>
                    <br>
                    <span style="color:#ffffff;">  ※本月借書和運動公里最高採計2000點 </span>
                </div>
            </div>
            <div class="col-md-2 w-100 p-3" style="background-color: #23b375;height:100%">
                <div class="text-center" style="  top: calc(50% -  280px); left: calc(50% - 140px); position: absolute;">
                    @*<video src="@unicornVedio" loop="true" autoplay="autoplay" width="250" controls="controls"> </video>*@
                    <video autoplay="" muted="" loop="" id="" width="250"><source src="@unicornVedio" type="video/mp4"></video>
                </div>
                <br />
                <div class="text-center" style=" top: calc(85% - 230px); left: calc(25% );position: absolute;">
                    <b>
                        <span style="font-size:20px">等級1:0-1000</span>
                    </b>
                    <br />
                    <br />
                    <b>
                        <span style="font-size:20px">   等級2:1001-1500</span>
                    </b>
                    <br />
                    <br />
                    <span style="font-size:20px">
                        <b>等級3:1051-2500    </b>
                    </span>
                    <br />
                    <br />
                    <span style="font-size:20px">
                        <b>     等級4:2501-3000  </b>
                    </span>
                    <br />
                    <br />
                    <span style="font-size:20px">
                        <b>   等級5:2501-3000</b>
                    </span>
                    <br />
                    <br />
                    <span style="font-size:20px">
                        <b>      等級6:2501-3000</b>
                    </span>
                    <br />
                    <br />
                    <span style="font-size:20px">     <b> 等級7:4501以上   </b>   </span>
                </div>
            </div>
        </div>
        <div class="row" style="margin-bottom:30px;height: 20%;">
            <div class="col-md-2 w-100 p-3" style="background-color:#23b375;height:100%">
                @*<div class="text-center">
                        <br /> <br />
                        <span style="font-size:20px">     <b> @Model.UserNAME,第 @Model.Level 級 </b>   </span>
                    </div>
                    <br />
                    <div class="text-center">
                        <span style="font-size:20px">   <b> @Model.CLASS_NO 班  @Model.SEAT_NO 號 </b></span>
                    </div>*@
            </div>
            <div class="col-md-8 w-100 p-3" style="background-color: #23b375;height:100% ;border-right:1px solid #000000;border-left:1px solid #000000;padding-top:450px">
                <div class="text-center"> </div>
                <br />
                <div class="text-center"></div>
                <br />
                <div class="text-center"> </div>
                <br />
                <div class="text-center"></div>
            </div>
            <div class="col-md-2 w-100 p-3" style="background-color: #23b375;height:100%">
                <div class="text-center"> </div>
                <br />
                <div class="text-center"></div>
                <br />
                <div class="text-center"> </div>
                <br />
                <div class="text-center"></div>
            </div>
        </div>
    </div>
</body>
</html>           