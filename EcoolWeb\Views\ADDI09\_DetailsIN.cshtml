﻿
@model ECOOL_APP.EF.ADDT14

<div class="form-group">
    @Html.LabelFor(model => model.CREATEDATE, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.CREATEDATE, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.USER_NO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.USERNAME, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.USERNAME, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.CLASS_NO, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.IAWARD_KIND, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.IAWARD_KIND, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.IAWARD_ITEM, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.TextAreaFor(model => model.IAWARD_ITEM, 5, 100, new { @class = "form-control", @disabled = "true" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.REMARK, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.REMARK, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
@if (!string.IsNullOrWhiteSpace(ViewBag.IMG_FILE))
            {
    <div class="form-group">
        @Html.Label("原上傳圖", htmlAttributes: new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            <img src='@ViewBag.IMG_FILE' class="img-responsive " alt="Responsive image" />
            @Html.HiddenFor(model => model.IMG_FILE)
        </div>
    </div>
}
