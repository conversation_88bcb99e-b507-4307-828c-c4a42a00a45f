﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http.WebHost</name>
  </assembly>
  <members>
    <member name="T:System.Web.Http.GlobalConfiguration">
      <summary> 提供 ASP.NET 應用程式的全域 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
    </member>
    <member name="P:System.Web.Http.GlobalConfiguration.Configuration"></member>
    <member name="M:System.Web.Http.GlobalConfiguration.Configure(System.Action{System.Web.Http.HttpConfiguration})"></member>
    <member name="P:System.Web.Http.GlobalConfiguration.DefaultHandler"></member>
    <member name="P:System.Web.Http.GlobalConfiguration.DefaultServer">
      <summary> 取得全域的 <see cref="T:System.Web.Http.HttpServer" />。 </summary>
    </member>
    <member name="T:System.Web.Http.RouteCollectionExtensions">
      <summary>
        <see cref="T:System.Web.Routing.RouteCollection" /> 的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String)">
      <summary>對應指定的路由範本。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String,System.Object)">
      <summary>對應指定的路由範本並設定預設路由。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="defaults">包含預設路由值的物件。</param>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String,System.Object,System.Object)">
      <summary>對應指定的路由範本並設定預設路由值和條件約束。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="defaults">包含預設路由值的物件。</param>
      <param name="constraints">為 routeTemplate 指定值的一組運算式。</param>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String,System.Object,System.Object,System.Net.Http.HttpMessageHandler)">
      <summary>對應指定的路由範本並設定預設的路由值、條件約束和端點訊息處理常式。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="defaults">包含預設路由值的物件。</param>
      <param name="constraints">為 routeTemplate 指定值的一組運算式。</param>
      <param name="handler">要求將要發送至的處理常式。</param>
    </member>
    <member name="T:System.Web.Http.WebHost.HttpControllerHandler">
      <summary>
        <see cref="T:System.Web.IHttpTaskAsyncHandler" /> 會傳送 ASP.NET 要求至 <see cref="T:System.Web.Http.HttpServer" /> 管線並寫入結果。</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerHandler.#ctor(System.Web.Routing.RouteData)">
      <summary>初始化 <see cref="T:System.Web.Http.WebHost.HttpControllerHandler" /> 類別的新執行個體。</summary>
      <param name="routeData">路徑資料。</param>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerHandler.#ctor(System.Web.Routing.RouteData,System.Net.Http.HttpMessageHandler)">
      <summary>初始化 <see cref="T:System.Web.Http.WebHost.HttpControllerHandler" /> 類別的新執行個體。</summary>
      <param name="routeData">路徑資料。</param>
      <param name="handler">發送要求的訊息處理常式。</param>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerHandler.ProcessRequestAsync(System.Web.HttpContext)">
      <summary>提供處理非同步工作的程式碼</summary>
      <returns>非同步工作。</returns>
      <param name="context">HTTP 內容。</param>
    </member>
    <member name="T:System.Web.Http.WebHost.HttpControllerRouteHandler">
      <summary>
        <see cref="T:System.Web.Routing.IRouteHandler" /> 會傳回可傳送要求至給定 <see cref="T:System.Web.Http.HttpServer" /> 執行個體的 <see cref="T:System.Web.Http.WebHost.HttpControllerHandler" /> 執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerRouteHandler.#ctor">
      <summary> 初始化 <see cref="T:System.Web.Http.WebHost.HttpControllerRouteHandler" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerRouteHandler.GetHttpHandler(System.Web.Routing.RequestContext)">
      <summary> 提供處理要求的物件。</summary>
      <returns> 處理要求的物件。</returns>
      <param name="requestContext">封裝要求相關資訊的物件。</param>
    </member>
    <member name="P:System.Web.Http.WebHost.HttpControllerRouteHandler.Instance">
      <summary> 取得 singleton <see cref="T:System.Web.Http.WebHost.HttpControllerRouteHandler" /> 執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerRouteHandler.System#Web#Routing#IRouteHandler#GetHttpHandler(System.Web.Routing.RequestContext)">
      <summary> 提供處理要求的物件。</summary>
      <returns> 處理要求的物件。 </returns>
      <param name="requestContext">封裝要求相關資訊的物件。</param>
    </member>
    <member name="T:System.Web.Http.WebHost.PreApplicationStartCode">
      <summary>提供簡單成員資格應用程式啟動前程式碼的註冊點。</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.PreApplicationStartCode.Start">
      <summary>註冊簡單成員資格應用程式啟動前程式碼。</summary>
    </member>
    <member name="T:System.Web.Http.WebHost.WebHostBufferPolicySelector">
      <summary>表示 Web 主機緩衝區原則選取器。</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.WebHostBufferPolicySelector.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.WebHost.WebHostBufferPolicySelector" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.WebHostBufferPolicySelector.UseBufferedInputStream(System.Object)">
      <summary>取得值，這個值表示主機是否應緩衝 HTTP 要求的實體內容。</summary>
      <returns>如果應使用緩衝，則為 true，否則應使用串流的要求。</returns>
      <param name="hostContext">主機內容。</param>
    </member>
    <member name="M:System.Web.Http.WebHost.WebHostBufferPolicySelector.UseBufferedOutputStream(System.Net.Http.HttpResponseMessage)">
      <summary>針對 Web 主機使用緩衝的輸出資料流。</summary>
      <returns>緩衝的輸出資料流。</returns>
      <param name="response">回應。</param>
    </member>
    <member name="T:System.Web.Http.WebHost.WebHostExceptionCatchBlocks">
      <summary>提供此組件中使用的捕捉區塊。</summary>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerBufferContent">
      <summary>取得 System.Web.Http.WebHost.HttpControllerHandler.WriteBufferedResponseContentAsync 中捕捉區塊的標籤。</summary>
      <returns>System.Web.Http.WebHost.HttpControllerHandler.WriteBufferedResponseContentAsync 中捕捉區塊的標籤。</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerBufferError">
      <summary>取得 System.Web.Http.WebHost.HttpControllerHandler.WriteErrorResponseContentAsync 中捕捉區塊的標籤。</summary>
      <returns>System.Web.Http.WebHost.HttpControllerHandler.WriteErrorResponseContentAsync 中捕捉區塊的標籤。</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerComputeContentLength">
      <summary>取得 System.Web.Http.WebHost.HttpControllerHandler.ComputeContentLength 中捕捉區塊的標籤。</summary>
      <returns>System.Web.Http.WebHost.HttpControllerHandler.ComputeContentLength 中捕捉區塊的標籤。</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerStreamContent">
      <summary>取得 System.Web.Http.WebHost.HttpControllerHandler.WriteStreamedResponseContentAsync 中捕捉區塊的標籤。</summary>
      <returns>System.Web.Http.WebHost.HttpControllerHandler.WriteStreamedResponseContentAsync 中捕捉區塊的標籤。</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpWebRoute">
      <summary>取得 System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpWebRoute.GetRouteData 中捕捉區塊的標籤。</summary>
      <returns>System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpWebRoute.GetRouteData 中捕捉區塊的標籤。</returns>
    </member>
  </members>
</doc>