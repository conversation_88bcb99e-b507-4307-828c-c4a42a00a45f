﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class MonthBestSellerViewModel
    {
        public string Where_SCHOOLNO { get; set; }
        [Display(Name = "年份")]
        public int? Where_YEAR { get; set; }
        [Display(Name = "月份")]
        public int? Where_Month { get; set; }
        [Display(Name = "性別")]
        public int? Where_Sex { get; set; }

        /// <summary>
        /// 借書總計
        /// </summary>
        public int BorrowBookTotal
        {
            get
            {
                int totalBorrow = 0;
                if (this.MonthOfBookSellList != null)
                {
                    foreach (var item in MonthOfBookSellList)
                    {
                        totalBorrow += item.BORROW_COUNT;
                    }
                }
                return totalBorrow;
            }
        }


        public List<MonthOfBookSell> MonthOfBookSellList { get; set; }
    }

    public class MonthOfBookSell
    {
        [Display(Name ="排名")]
        public int RANK { get; set; }
        [Display(Name = "書本名稱")]
        public string BKNAME { get; set; }
        public int BORROW_YEAR { get; set; }
        [Display(Name = "月份")]
        public int BORROW_MONTH { get; set; }
        [Display(Name = "借書量(本)")]
        public int BORROW_COUNT { get; set; }

    }
}
