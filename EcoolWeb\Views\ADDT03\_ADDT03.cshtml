﻿@using ECOOL_APP.com.ecool.Models.entity;
@{
    List<uADDT03> ADDT03List = ViewBag.ADDT03List;
    int Count = 1;
    var ShowList = ADDT03List.Take(5);
    string str = "";
    str = "~/Content/img/web-bar2-revise-04.png";
    if (ShowList.Count() == 0)
    {
        str = "~/Content/img/web-bar2-revise-114.png";
    }
}
@if (ShowList.Count() == 0)
{
    <img src="~/Content/img/web-bar2-revise-114.png" style="width:100%" class="img-responsive " alt="Responsive image" />

}
else
{

    <img src="~/Content/img/web-bar2-revise-04.png" style="width:100%" class="img-responsive " alt="Responsive image" />

}

<div class="Div-EZ-reader">
    <div class="Details">
        <table class="table-ecool table-92Per">
            <thead>
                <tr>
                    <td align="center">
                        No.
                    </td>
                    <td align="center">
                        閱讀年級
                    </td>
                    <td align="center">
                        書本編號
                    </td>
                    <td align="center">
                        書名
                    </td>
                </tr>
            </thead>
            <tbody>

                @if (ShowList.Count() != 0)
                {
                    foreach (var item in ShowList)
                    {
                        if (!string.IsNullOrWhiteSpace(item.BOOK_NAME))
                        {


                            <tr style="padding-bottom:2px">
                                <td align="center">
                                    @Count
                                </td>
                                <td align="center">
                                    @item.CGRADE
                                </td>
                                <td align="center">
                                    @item.BOOK_ID
                                </td>
                                <td style="text-align: left;white-space:normal">
                                    @Html.ActionLink(item.BOOK_NAME, "ADDTList_apply", new { Controller = "ADDT", BookID = item.BOOK_ID })
                                </td>
                            </tr>
                            Count++;
                        }
                    }
                    <tr>
                        <td colspan="4" align="right" style="padding-bottom:2px;padding-top:2px;border-top-style:none">
                            @Html.ActionLink("more", "Query4", "ADDI03", null, new { @class = "btn btn-link-ez btn-xs" })
                            <br />
                        </td>
                    </tr>
                }
                else
                {
                    <tr style="padding-bottom:2px">
                        <td align="center" colspan="4">
                            <font style="color:orangered" size="5px">恭喜完成本學期的共讀書籍</font>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4" align="right" style="padding-bottom:2px;padding-top:2px;border-top-style:none">
                            @Html.ActionLink("more", "Query4", "ADDI03", null, new { @class = "btn btn-link-ez btn-xs" })
                            <br />
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>


