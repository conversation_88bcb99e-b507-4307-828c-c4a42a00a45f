/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/fontdata-extra.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(p){var z="2.7.5";var o=p.FONTDATA.DELIMITERS;var q="H",d="V";var c="AsanaMathJax_Alphabets",v="AsanaMathJax_Arrows",x="AsanaMathJax_DoubleStruck",A="AsanaMathJax_Fraktur",g="AsanaMathJax_Latin",u="AsanaMathJax_Main",l="AsanaMathJax_Marks",w="AsanaMathJax_Misc",D="AsanaMathJax_Monospace",y="AsanaMathJax_NonUnicode",r="AsanaMathJax_Normal",B="AsanaMathJax_Operators",a="AsanaMathJax_SansSerif",n="AsanaMathJax_Script",b="AsanaMathJax_Shapes",k="AsanaMathJax_Size1",j="AsanaMathJax_Size2",i="AsanaMathJax_Size3",h="AsanaMathJax_Size4",f="AsanaMathJax_Size5",e="AsanaMathJax_Size6",t="AsanaMathJax_Symbols",m="AsanaMathJax_Variants";var C={774:{dir:q,HW:[[0.282,u],[0.384,k],[0.542,j],[0.922,i],[1.762,h]]},819:{dir:q,HW:[[0.433,l],[0.511,k],[0.675,j],[1.127,i]],stretch:{rep:[57347,e],right:[57347,e]}},831:{dir:q,HW:[[0.433,l],[0.511,k],[0.675,j],[1.127,i]],stretch:{rep:[57348,e],right:[57348,e]}},8261:{dir:d,HW:[[0.91,l],[1.344,k],[1.862,j],[2.328,i]],stretch:{bot:[57350,e],ext:[57351,e],mid:[57352,e],top:[57353,e]}},8262:{dir:d,HW:[[0.91,l],[1.344,k],[1.862,j],[2.328,i]],stretch:{bot:[57354,e],ext:[57355,e],mid:[57356,e],top:[57357,e]}},8400:{dir:q,HW:[[0.558,l]],stretch:{left:[8400,l],rep:[57358,e]}},8401:{dir:q,HW:[[0.558,l]],stretch:{rep:[57358,e],right:[8401,l]}},8406:{dir:q,HW:[[0.558,l],[0.807,k],[1.127,j],[1.878,i],[3.579,h]],stretch:{left:[8406,l],rep:[57358,e]}},8407:{dir:q,HW:[[0.558,u],[0.807,k],[1.127,j],[1.878,i],[3.579,h]],stretch:{rep:[57358,e],right:[8407,u]}},8417:{dir:q,HW:[[0.557,l]],stretch:{left:[8406,l],rep:[57358,e],right:[8407,u]}},8425:{dir:q,HW:[[0.63,l]],stretch:{left:[57359,e],rep:[57360,e],right:[57361,e]}},8430:{dir:q,HW:[[0.557,l]],stretch:{left:[8430,l],rep:[57362,e]}},8431:{dir:q,HW:[[0.557,l]],stretch:{rep:[57362,e],right:[8431,l]}},8617:{dir:q,HW:[[0.884,u]],stretch:{left:[57363,e],rep:[9135,t],right:[57370,e]}},8618:{dir:q,HW:[[0.884,u]],stretch:{left:[57371,e],rep:[9135,t],right:[57367,e]}},8720:{dir:d,HW:[[0.937,B],[1.349,k],[1.942,j],[2.797,i]]},8721:{dir:d,HW:[[0.93,B],[1.339,k],[1.928,j],[2.776,i]]},8745:{dir:d,HW:[[0.603,u],[1.559,k],[2.245,j],[2.588,i]]},8747:{dir:d,HW:[[1.327,u],[1.964,k],[2.711,j],[3.47,i]],stretch:{bot:[8993,t],ext:[9134,t],top:[8992,t]}},8748:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8749:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8750:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8751:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8752:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8753:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8754:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8755:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},8896:{dir:d,HW:[[0.939,B],[1.559,k],[2.588,j]]},8897:{dir:d,HW:[[0.939,B],[1.559,k],[2.588,j]]},8898:{dir:d,HW:[[0.939,B],[1.559,k],[2.588,j]]},8899:{dir:d,HW:[[0.939,B],[1.559,k],[2.245,j],[2.588,i]]},9140:{dir:q,HW:[[0.602,u],[0.978,k],[1.353,j],[1.69,i]],stretch:{left:[57359,e],rep:[57360,e],right:[57361,e]}},9141:{dir:q,HW:[[0.602,u],[0.978,k],[1.353,j],[1.69,i]],stretch:{left:[57379,e],rep:[57380,e],right:[57381,e]}},9180:{dir:q,HW:[[0.942,u],[0.973,k],[1.349,j],[1.686,i]],stretch:{left:[57382,e],rep:[57383,e],right:[57384,e]}},9181:{dir:q,HW:[[0.942,u],[0.973,k],[1.349,j],[1.686,i]],stretch:{left:[57385,e],rep:[57386,e],right:[57387,e]}},9184:{dir:q,HW:[[0.9,u],[1.36,k],[2.056,j],[3.108,i]]},9185:{dir:q,HW:[[0.9,u],[1.36,k],[2.056,j],[3.108,i]]},10214:{dir:d,HW:[[0.91,t],[1.025,k],[1.535,j],[2.045,i],[2.556,h]]},10215:{dir:d,HW:[[0.91,t],[1.025,k],[1.535,j],[2.045,i],[2.556,h]]},10218:{dir:d,HW:[[0.885,t],[1.021,k],[2.042,j],[2.552,i]]},10219:{dir:d,HW:[[0.885,t],[1.021,k],[2.042,j],[2.552,i]]},10748:{dir:d,HW:[[0.953,t],[1.372,k],[1.893,j],[2.366,i]]},10749:{dir:d,HW:[[0.953,t],[1.372,k],[1.893,j],[2.366,i]]},10752:{dir:d,HW:[[1.146,B],[1.65,k],[2.376,j]]},10753:{dir:d,HW:[[1.149,B],[1.65,k],[2.376,j]]},10754:{dir:d,HW:[[1.149,B],[1.65,k],[2.376,j]]},10755:{dir:d,HW:[[0.939,B],[1.559,k],[2.588,j]]},10756:{dir:d,HW:[[0.939,B],[1.559,k],[2.588,j]]},10757:{dir:d,HW:[[0.926,B],[1.537,k],[2.552,j]]},10758:{dir:d,HW:[[0.926,B],[1.537,k],[2.552,j]]},10759:{dir:d,HW:[[0.939,B],[1.559,k],[2.588,j]]},10760:{dir:d,HW:[[0.939,B],[1.559,k],[2.588,j]]},10761:{dir:d,HW:[[0.926,B],[1.333,k],[1.92,j]]},10764:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10765:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10766:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10767:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10768:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10769:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10770:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10771:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10772:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10773:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10774:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10775:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10776:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10777:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10778:{dir:d,HW:[[1.327,B],[1.964,k],[2.711,j],[3.47,i]]},10779:{dir:d,HW:[[1.436,B],[2.125,k],[2.933,j],[3.754,i]]},10780:{dir:d,HW:[[1.436,B],[2.125,k],[2.933,j],[3.754,i]]}};for(var s in C){if(C.hasOwnProperty(s)){o[s]=C[s]}}MathJax.Ajax.loadComplete(p.fontDir+"/fontdata-extra.js")})(MathJax.OutputJax["HTML-CSS"]);
