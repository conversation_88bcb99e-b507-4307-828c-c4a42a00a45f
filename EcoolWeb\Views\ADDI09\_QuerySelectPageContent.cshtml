﻿@model ECOOL_APP.com.ecool.Models.DTO.QuerySelectViewModel
@using ECOOL_APP.com.ecool.util
@using ECOOL_APP.com.ecool.Models.DTO

<link href="~/Content/css/EzCss.css" rel="stylesheet" />


@Html.HiddenFor(model => model.Search.OrderByName)
@Html.HiddenFor(model => model.Search.SyntaxName)
@Html.HiddenFor(model => model.Search.Page)

<img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ADDI09">
    <div class="form-horizontal">
        <label class="control-label"> * 查詢條件</label>
        <br /><br />
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        <div class="form-group">
            @Html.LabelFor(model => model.Search.USER_NO, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Search.USER_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "可模糊查詢" } })
                @Html.ValidationMessageFor(model => model.Search.USER_NO, "", new { @class = "text-danger" })
            </div>
        </div>
        @if (Request.Params["SYS_TABLE_TYPE"] != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
        {
            <div class="form-group">
                @Html.LabelFor(model => model.Search.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.Search.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Search.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Search.SYEAR, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Search.SYEAR, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入數字" } })
                    @Html.ValidationMessageFor(model => model.Search.SYEAR, "", new { @class = "text-danger" })
                </div>
            </div>
        }
        <div class="form-group">
            @Html.LabelFor(model => model.Search.NAME, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Search.NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "可模糊查詢" } })
                @Html.ValidationMessageFor(model => model.Search.NAME, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="text-center">
            <button type="button" class="btn btn-default" onclick="funAjax()">查 詢</button>            <button type="button" class="btn btn-default" id="BtnSave" onclick="CheckALLALT()">全選</button>
        </div>
    </div>
</div>

<div style="text-align: center; margin-top: 20px; margin-bottom: 30px;">
    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
</div>

<div class="Div-EZ-ADDI09">

    <div class="form-horizontal">
        <label class="control-label"> * 未選取人員清單，請點選學號「加入」</label>
        <br /><br />
        <div >
            @Html.DropDownList("pageSize", (IEnumerable<SelectListItem>)ViewBag.PageSizeItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-hover">
                <thead>
                    <tr>
                        <th>
                            本頁全選
                            @*<input onclick="CheckALLALT()" value="全選" type="checkbox" />*@
                            <input id="chkALL" value="這頁全選" type="checkbox" />
                        </th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                    <tr>
                        <th>


                            <samp  style="cursor:pointer;" onclick="FunSort('USER_NO')">
                                @Html.DisplayNameFor(model => model.HRMT01List.First().USER_NO)
                            </samp>
                        </th>

                        <th>
                            <samp  style="cursor:pointer;" onclick="FunSort('NAME')">
                                @Html.DisplayNameFor(model => model.HRMT01List.First().NAME)
                            </samp>
                        </th>
                        @if (Request.Params["SYS_TABLE_TYPE"] != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                        {
                            <th>
                                <samp  style="cursor:pointer;" onclick="FunSort('CLASS_NO')">
                                    @Html.DisplayNameFor(model => model.HRMT01List.First().CLASS_NO)
                                </samp>
                            </th>
                            <th>

                                <samp style="cursor:pointer;" onclick="FunSort('SEAT_NO')">
                                    @Html.DisplayNameFor(model => model.HRMT01List.First().SEAT_NO)
                                </samp>
                            </th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.HRMT01List)
                    {

                        //EcoolWeb.ViewModels.AWA004PrintViewModel Chk_Item = new EcoolWeb.ViewModels.AWA004PrintViewModel();
                        //Chk_Item.QueryselectViewModelItem = item;
                        //if (Model.Chk != null)
                        //{
                        //    Chk_Item.CheckBoxNo = Model.Chk.Where(a => a.V004.TRANS_NO == item.TRANS_NO).Select(a => a.CheckBoxNo).FirstOrDefault();
                        //}
                        <tr onclick="onBtnLink('@item.USER_NO','ADD')" style="cursor:pointer" id=tr_@item.USER_NO>
                        @*<tr style="cursor:pointer" id=tr_@item.USER_NO>*@
                            @{
                                ECOOL_APP.com.ecool.Models.DTO.QuerySelectPrintViewModel Chk_Item = new ECOOL_APP.com.ecool.Models.DTO.QuerySelectPrintViewModel();
                                Chk_Item.QuerySelectViewModelHRMT01List = item;
                                if (Model.Chk != null)
                                {
                                    Chk_Item.CheckBoxNo = Model.Chk.Where(a => a.QuerySelectViewModelHRMT01List.USER_NO == item.USER_NO).Select(a => a.CheckBoxNo).FirstOrDefault();
                                }
                            }
                            @*<td class="text-center">*@
                                @*@Html.Partial("_ModifyView", Chk_Item)*@
                            @*</td>*@
                            <td align="center" id="USER_NOlist">
                                @if (item.USER_TYPE == UserType.Student)
                                {
                                    @item.USER_NO
                                }
                                else
                                {
                                    @StringHelper.LeftStringR(item.USER_NO, 5, "*****");
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            @if (Request.Params["SYS_TABLE_TYPE"] != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                            {
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                            }
                        </tr>
                        }
                    </tbody>
                </table>
        </div>
    </div>
    @*<div class="text-center"> <input type="button" id="btnSend2" value="確認選取" class="btn btn-default" onclick="btnSend_onclick();" /></div>*@
    <div class="text-center">
        @Html.Pager(Model.HRMT01List.PageSize, Model.HRMT01List.PageNumber, Model.HRMT01List.TotalItemCount).Options(o => o
            .DisplayTemplate("BootstrapPagination")
            .MaxNrOfPages(5)
            .SetPreviousPageText("上頁")
            .SetNextPageText("下頁")
                        )
    </div>
</div>
<script language="JavaScript">
    function btnSend_onclick()
    {

        $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
        $("#myModal").modal('show');

        var id = setInterval(setTime, 10);
        var percent = 10;
        var percentW = "";
        percent = 10 * id;
        percentW = percent + "%";
        console.log(percentW);
        clearInterval(id);
        $("#barr").css("width", percentW);
        setTimeout(function () {


          //  $("#pageSize").val('2147483647');
         //   FunPageProc(1);
            $("td[id='USER_NOlist']").each(function () {
                var C01 = $(this).text().trim();
                var C02 = "";
                var C03 = "";
                C02 = "tr_" + C01;
                C03 = C01;

                var p = document.getElementById(C02);
                p.onclick = onBtnLink(C03, 'ADD');
            });

        }, 3000);
        setTimeout(function () {
            $("#barr").css("width", "100%");
            $("#myModal").modal('hide');
        }, 3000);
    
    }
    $("#chkALL").click(function () {

        if ($("#chkALL").prop("checked")) {
            $("input:checkbox").each(function () {
                if ($(this).attr("id") != 'cbPicture') {

                    $(this).prop("checked", true);
                }

            });
        }
        else {
            $("input:checkbox").each(function () {
                if ($(this).attr("id") != 'cbPicture') {
                    $(this).prop("checked", false);
                }
            });
        }
        btnSend_onclick();
    });
    
</script>




