/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeTwoSym/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXSizeTwoSym-bold"]={directory:"SizeTwoSym/Bold",family:"STIXSizeTwoSym",weight:"bold",32:[0,0,250,0,0],40:[1604,241,608,110,512],41:[1604,241,608,96,498],47:[1604,241,802,4,798],91:[1604,241,485,197,467],92:[1604,241,802,4,798],93:[1604,241,485,18,288],123:[1604,241,681,69,514],125:[1604,241,681,167,612],160:[0,0,250,0,0],8730:[2095,355,1130,106,1185],8968:[1604,241,538,185,510],8969:[1604,241,538,28,355],8970:[1604,241,538,185,512],8971:[1604,241,538,28,353],10216:[1604,241,622,98,572],10217:[1604,241,622,50,524]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeTwoSym-bold");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeTwoSym/Bold/Main.js");
