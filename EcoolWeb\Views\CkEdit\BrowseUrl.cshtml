﻿@model CkEditIndexViewModel
@{
    ViewBag.Title = ViewBag.Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

@using (Html.BeginForm("BrowseUrl", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.Search.BackAction)
    @Html.HiddenFor(m => m.Search.BackController)
    @Html.HiddenFor(m => m.Search.CKEditorFuncNum)
    @Html.HiddenFor(m => m.Search.CKEditor)
    @Html.HiddenFor(m => m.Search.langCode)



    <div style="padding: 30px 30px;">
        <h3 class="page-header">
            @if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Audio)
            {
                <span class="glyphicon glyphicon-star" aria-hidden="true">Audio 參考來源<small>（僅接受 mp3</small></span>
            }
            else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Vedio)
            {
                <span class="glyphicon glyphicon-star" aria-hidden="true">Vedio 參考來源<small>（僅接受 mp4</small></span>
            }
            else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Image)
            {
                <span class="glyphicon glyphicon-star" aria-hidden="true">Image 參考來源<small>（僅接受 jpg、jpeg、png、gif、bmp</small></span>
            }
            <span class="pull-right"><button type="button" class="btn btn-success btn-sm" onclick="onAdd()">新增</button></span>
        </h3>
        <div id="PageContent">
            @Html.Action("_PageBrowseUrl", (string)ViewBag.BRE_NO, Model)
        </div>
    </div>

}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';
   
        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };

        function onEdit(Value) {
           $('#@Html.IdFor(m=>m.Search.WhereREF_NO)').val(Value)
           $(targetFormID).attr("action", "@Url.Action("EditCkEdit", (string)ViewBag.BRE_NO)")
           $(targetFormID).submit();
        }

        function onAdd() {
            $(targetFormID).attr("action", "@Url.Action("EditCkEdit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBtnLink(fileUrl) {
            var CKEditorFuncNum = $('#Search_CKEditorFuncNum').val();

            if (CKEditorFuncNum != '') {
                window.opener.CKEDITOR.tools.callFunction(CKEditorFuncNum, fileUrl);
                window.close();
            }
            else {
                var Id = $('#Search_CKEditor').val();
                $('#' + Id, opener.document).attr("value", fileUrl);
                window.close();
            }
        }

        //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_PageBrowseUrl", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }
    </script>
}


