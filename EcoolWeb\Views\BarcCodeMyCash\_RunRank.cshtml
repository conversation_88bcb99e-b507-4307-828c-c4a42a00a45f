﻿@using ECOOL_APP.com.ecool.Models;
@model  IQueryable<ADDI11OrderListDataViewModel>
@{

    ECOOL_APP.UserProfile user = new ECOOL_APP.UserProfile();

    Layout = "";

    List<string> RankStr = new List<string>();
    RankStr.Add(" number-first");
    RankStr.Add(" number-second");
    RankStr.Add(" number-third");
    var AWAT02List = Model.ToList();
}
<div class="col-md-6 leaderboard-sunlight-small">
    <strong class="title title-sports text-hide">運動撲滿排行榜</strong>
    <ul class="leaderboard-box leaderboard-box-blue">

        @{int i = 0;}
        @foreach (var Item in AWAT02List.Take(3).ToList())
        {
            var cssRankStr = "";
            var cssRankStrItem = "";
            cssRankStr = "leaderboard-item";
            cssRankStrItem = "text-hide number" + RankStr.Skip(i).Take(1).FirstOrDefault();

        <li class='@cssRankStr'>
            <span class='@cssRankStrItem'>@Item.Rank</span>
            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
            <span class="name">@Item.SNAME</span>
            <span class="value">@((Item.Total_M / 1000).ToString("#,0.00"))<small>公里</small></span>
        </li>
            i++;
        }
       
    </ul>
    <ul class="leaderboard-list leaderboard-list-blue">
        @foreach (var Item in AWAT02List.Skip(3).Take(6).ToList())
        {
            <li>
                <span class="number">@Item.Rank</span>
                <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                <span class="name">@Item.SNAME</span>
                <span class="value">@((Item.Total_M / 1000).ToString("#,0.00")) 公里</span>
            </li>


        }
        
    </ul>
</div>
