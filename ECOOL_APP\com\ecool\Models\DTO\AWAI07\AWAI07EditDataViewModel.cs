﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class AWAI07EditDataViewModel
    {
        /// <summary>
        ///KEY值
        /// </summary>
        [DisplayName("KEY值")]
        public string A_NO { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///學號代碼
        /// </summary>
        [DisplayName("學號代碼")]
        public string USER_NO { get; set; }

        /// <summary>
        ///存款類型
        /// </summary>
        [DisplayName("存款類型")]
        [Required(ErrorMessage = "此欄位必輸")]
        public byte? ACCT_CODE { get; set; }

        /// <summary>
        ///申請日
        /// </summary>
        [DisplayName("申請日")]
        public DateTime? BANK_DATE { get; set; }

        /// <summary>
        ///存款期間類型
        /// </summary>
        [DisplayName("定存期別")]
        [Required(ErrorMessage = "此欄位必輸")]
        public byte? PERIOD_TYPE { get; set; }

        /// <summary>
        ///利率
        /// </summary>
        [DisplayName("利率")]
        public decimal? INTEREST_RATE { get; set; }

        /// <summary>
        ///開始日
        /// </summary>
        [DisplayName("開始日")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? PERIOD_DATES { get; set; }

        /// <summary>
        ///到期日
        /// </summary>
        [DisplayName("到期日")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? PERIOD_DATEE { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public string STATUS { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日
        /// </summary>
        [DisplayName("修改日")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///提早解約日
        /// </summary>
        [DisplayName("提早解約日")]
        public DateTime? CLOSE_DATE { get; set; }

        /// <summary>
        ///解約利率
        /// </summary>
        [DisplayName("解約利率")]
        public decimal? CLOSE_RATE { get; set; }

        /// <summary>
        ///定存酷幣
        /// </summary>
        [DisplayName("定存酷幣")]
        [Required(ErrorMessage = "此欄位必輸")]
        [DisplayFormat(DataFormatString = "{0:#}", ApplyFormatInEditMode = true)]
        public decimal? AMT { get; set; }

        /// <summary>
        ///到期處理方式
        /// </summary>
        [DisplayName("到期處理方式")]
        [Required(ErrorMessage = "此欄位必輸")]
        public byte? MATURITY_TYPE { get; set; }

        /// <summary>
        ///利息
        /// </summary>
        [DisplayName("利息")]
        [DisplayFormat(DataFormatString = "{0:#}", ApplyFormatInEditMode = true)]
        public decimal? INTEREST_AMT { get; set; }

        [DisplayFormat(DataFormatString = "{0:#}", ApplyFormatInEditMode = true)]
        public decimal?  PrincipleAndInterestAmt { get; set; }

        [DisplayFormat(DataFormatString = "{0:#}", ApplyFormatInEditMode = true)]
        public decimal? ClosePrincipleAndInterestAmt { get; set; }
        

    }
}