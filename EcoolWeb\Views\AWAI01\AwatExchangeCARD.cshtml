﻿@model AWAI01ExchangeViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string aImgUrl = string.Empty;
    if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
    {
        aImgUrl = ViewBag.ImgUrl + @"/" + Model.AwaData.IMG_FILE;
    }
    else
    {
        aImgUrl = ViewBag.ImgUrl + Model.AwaData.SCHOOL_NO + @"/" + Model.AwaData.IMG_FILE;
    }
    string HtmlMsg = "";
    if (TempData["StatusMessage"] != null)
    {
        HtmlMsg = TempData["StatusMessage"].ToString().Replace("\r\n", "<br />").Replace("<a>","").Replace("</a>", "");
    }

}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    // 明顯的成功頁面
    if ((TempData["Status"] != null && TempData["StatusMessage"] != null)
        && TempData["Status"] as String == "success")
    {
        <div class="modal fade" id="mySuccessModal" role="dialog">
            <div class="modal-dialog">

                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title text-center">
                            @Html.Raw(HtmlMsg) <br /><span style="font-size:3em; color:indianred;">成功</span>
                        </h2>
                    </div>
                    <div class="modal-body text-center">
                        <img src="@Url.Content("~/Content/img/<EMAIL>")" class="img-responsive" style="margin:0 auto;" />
                    </div>
                </div>
            </div>
        </div>
        <script src="~/Scripts/buzz/buzz.min.js"></script>
        <script>

            var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );
            var windowClose = false;
            if ('@Model.AfterClose') {
                windowClose = true;
            }
            $(document).ready(function () {
                SwipeOK()
                $("#mySuccessModal").modal();
                if (windowClose) {
                    setInterval(function () { $('#mySuccessModal').modal('hide'); window.close(); }, 3000);
                } else {
                    setInterval(function () { $('#mySuccessModal').modal('hide'); }, 3000);

                }
            });

          function SwipeOK() {
                 SwipeSound.play();
          }
        </script>
    }

    // 失敗
    if ((TempData["Status"] != null && TempData["StatusMessage"] != null)
       && TempData["Status"] as String == "error")
    {
        <div class="modal fade" id="myErrorModal" role="dialog">
            <div class="modal-dialog">

                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title text-center">
                            @Html.Raw(HtmlMsg)
                        </h2>
                    </div>
                    <div class="modal-body text-center">
                        <img src="@Url.Content("~/Content/img/x_icon.png")" class="img-responsive" style="margin:0 auto;" />
                    </div>
                </div>
            </div>
        </div>
        <script src="~/Scripts/buzz/buzz.min.js"></script>
        <script>

            var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")" );
            var windowClose = false;
            if ('@Model.AfterClose') {
                windowClose = true;
            }
            $(document).ready(function () {
                SwipeOK()
                $("#myErrorModal").modal();
                if (windowClose) {
                    setInterval(function () { $('#myErrorModal').modal('hide'); window.close(); }, 5000);
                } else {
                    setInterval(function () { $('#myErrorModal').modal('hide'); }, 5000);
                }
            });

          function SwipeOK() {
                 SwipeSound.play();
          }
        </script>
    }
}

@using (Html.BeginForm("ActionSaveAwat", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "contentForm", name = "contentForm", @AutoComplete = "Off" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.BackAction)
    @Html.HiddenFor(m => m.Search.BackController)
    @Html.HiddenFor(m => m.Search.SouController)
    @Html.HiddenFor(m => m.Search.SouAction)
    @Html.HiddenFor(m => m.Search.WhereExchangeType)

    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.WhereAWARD_NO)
    @Html.HiddenFor(m => m.Search.WhereAWARD_STS)
    @Html.HiddenFor(m => m.Search.WhereSouTable)
    @Html.HiddenFor(m => m.Search.unProduct)
    @Html.HiddenFor(m => m.Search.whereAWARD_SCHOOL_NO)
    @Html.HiddenFor(m => m.Search.AWARD_TYPE)
    @Html.HiddenFor(m => m.Search.whereKeyword)
    @Html.HiddenFor(m => m.AwaData.AWARD_NO)
    @Html.HiddenFor(m => m.AwaData.AWARD_TYPE)
    @Html.HiddenFor(m => m.AfterClose)
    <div class="containerEZ">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" id="container">
                <img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
                <div class="Div-EZ-Awat02">
                    <div class="form-horizontal">
                        <div class="col-md-12">
                            <div class="row p-context">
                                <br />
                                <div class="col-sm-12 col-md-5 col-lg-4">
                                    <img src='@aImgUrl' class="img-responsive" href="@aImgUrl" />
                                </div>
                                <div class="col-sm-12 col-md-7 col-lg-8" style="padding:2% 5%">
                                    <div>
                                        <samp class="dt">
                                            獎品名稱　
                                        </samp>
                                        <samp class="dd">
                                            @Html.DisplayFor(model => model.AwaData.AWARD_NAME)
                                        </samp>
                                    </div>
                                    <div>
                                        <samp class="dt">
                                            兌換點數　
                                        </samp>
                                        <samp class="dd" style="color:red;">
                                            @Html.DisplayFor(model => model.AwaData.COST_CASH)
                                        </samp>
                                    </div>

                                    @if (Model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_P || Model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_C)
                                    {
                                        <div>
                                            <samp class="dt">
                                                已募資點數　
                                            </samp>
                                            <samp class="dd">
                                                @(Model.AwaData.COST_CASH * Model.AwaData.QTY_TRANS)
                                            </samp>
                                        </div>
                                    }
                                    else
                                    {
                                        <div>
                                            <samp class="dt">
                                                剩餘數量　
                                            </samp>
                                            <samp class="dd" style="color:blue;">
                                                @Html.DisplayFor(model => model.AwaData.QTY_STORAGE)
                                            </samp>
                                        </div>
                                    }
                                    <!--限制對象-->
                                    <div>
                                        <samp class="dt">
                                            @Html.DisplayNameFor(model => model.AwaData.BUY_PERSON_YN)
                                        </samp>
                                        <samp class="dd">
                                            @if (Model.AwaData.BUY_PERSON_YN == "Y")
                                            {
                                                <span class="text-danger">　是</span>
                                                <span>(@ECOOL_APP.com.ecool.util.StringHelper.LeftStringR(Model.AwaData.BUY_PERSON, 50))</span>
                                            }
                                            else
                                            {
                                                <span class="text-danger">　否</span>
                                            }
                                        </samp>
                                    </div>
                                    @if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student)
                                    {
                                        <div>
                                            <samp class="dt">
                                                閱讀認證　
                                            </samp>
                                            <samp class="dd">
                                                @if (Model.AwaData.READ_LEVEL != null)
                                                {

                                                    <samp>需滿</samp>
                                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgReadUrl(Model.AwaData.READ_LEVEL))" style="max-height:30px;margin-right:1px">
                                                    <samp>級</samp>
                                                }
                                                else
                                                {
                                                    <samp>無限制</samp>
                                                }
                                            </samp>
                                        </div>
                                        <div>
                                            <samp class="dt">
                                                閱讀護照　
                                            </samp>
                                            <samp class="dd">
                                                @if (Model.AwaData.PASSPORT_LEVEL != null)
                                                {
                                                    <samp>需滿</samp>
                                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgPassportUrl(Model.AwaData.PASSPORT_LEVEL))" style="max-height:30px;margin-right:1px">
                                                    <samp>級</samp>
                                                }
                                                else
                                                {
                                                    <samp>無限制</samp>
                                                }
                                            </samp>
                                        </div>
                                    }

                                    @if (Model.AwaData.SHOW_DESCRIPTION_YN == SharedGlobal.Y)
                                    {
                                        <div>
                                            <samp class="dt">
                                                備註說明　
                                            </samp>
                                            <samp class="dd">
                                                @Html.DisplayFor(model => model.AwaData.DESCRIPTION)
                                            </samp>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="row Div-btn-center">
                            <div class="col-sm-2" style="font-size:30px">
                                <div class="text-center">
                                    <label style="color:brown">@(Model.Search.WhereExchangeType=="SchoolNo" ? "學號":"數位學生證" ) </label>
                            </div>
                                </div>
                            <div class="col-lg-10">
                                <div class="">
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-addon" style="background-color:white;"><i class="fa fa-user"></i></span>
                                        @Html.EditorFor(m => m.CARD_ID, new { htmlAttributes = new { @class = "form-control", @placeholder = Model.Search.WhereExchangeType=="SchoolNo" ? "請輸入「學號」":"請用「數位學生證感應」", @onKeyPress = "call(event,this);" } })
                                    </div>
                                    @Html.ValidationMessageFor(m => m.CARD_ID, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <br />
                            <div class="col-sm-12 col-md-12 col-lg-12 center">
                                <img src="~/Content/img/Asset1.png" style="height:150px;padding-right:10px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}

@section css{
    <style>
        .dt, .dd {
            font-family: 'Microsoft JhengHei';
            font-size: 2rem;
        }

        h2, h3 {
            font-family: 'Microsoft JhengHei', Consolas;
        }

        h2 {
            color: #3aa595
        }

        .modal {
            text-align: center;
            padding: 0 !important;
        }

            .modal:before {
                content: '';
                display: inline-block;
                height: 100%;
                vertical-align: middle;
                margin-right: -4px;
            }

        .modal-dialog {
            display: inline-block;
            text-align: left;
            vertical-align: middle;
        }

        .modal-content {
            -webkit-border-radius: 0px !important;
            -moz-border-radius: 0px !important;
            border-radius: 0px !important;
        }

        .p-context img {
            max-height: 30vh;
        }
    </style>

}

<script src="~/Scripts/buzz/buzz.min.js"></script>
<!--資料顯示區END -->
<script type="text/javascript">
    var targetFormID = '#contentForm';
    $(document).ready(function () {
        $("#@Html.IdFor(m=>m.CARD_ID)").val('');
        $("#@Html.IdFor(m=>m.CARD_ID)").focus();
    });

    function funExchangeCard() {


						//  SwipeOK();
                            document.contentForm.action = "SaveExchangeCard";
                            document.contentForm.submit();
						
        
     }

    function call(e, input) {
    var code = (e.keyCode ? e.keyCode : e.which);

    if (code == 13) // 13 是 Enter 按鍵的值
    {
        event.preventDefault();

        if ($('#@Html.IdFor(m=>m.CARD_ID)').val() != '') {
                $('#@Html.IdFor(m=>m.CARD_ID)').prop('readonly', true);

                setTimeout(function () {
                    funExchangeCard()
                });
        }
    }
}
</script>