﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI08.ZZZI08EditViewModel
@using EcoolWeb.Util;

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Content/ckeditor4_5_11/ckeditor.js"></script>

@helper  buttonFun()
{
    if (Model.uADDT11 != null)
    {

        if (Model.uADDT11.DIALOG_ID != null)
        {
            if (Model.VIEW_DATA_TYPE == ViewBag.VIEW_U)
            {
                if (Model.uADDT11.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_D)
                {

                    @Html.PermissionButton("存檔", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_U + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                    @Html.PermissionButton("發佈", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_R + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                }
                else if (Model.uADDT11.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_R)
                {
                    @Html.PermissionButton("存檔(已發佈)", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_E + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                }
                else
                {
                    <button class="btn btn-default active" disabled type="button">已提早結案不能存檔</button>
                }

                if (Model.uADDT11.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_R)
                {
                    @Html.PermissionButton("提早結案", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_Z + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                }
            }

            if (Model.uADDT11.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_D || Model.uADDT11.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_R || Model.uADDT11.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_H)
            {
                @Html.PermissionButton("刪除", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_D + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                @Html.PermissionButton("隱藏", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_H + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            }
            else
            { @Html.PermissionButton("隱藏", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_H + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            <button class="btn btn-default active" disabled type="button">不能刪除</button>
        }

        <button role="button" href='@Url.Action("PreView", (string)ViewBag.BRE_NO, new { DIALOG_ID = Model.uADDT11.DIALOG_ID, PreviewY="Y" })' class="btn btn-default" id="BtnPreview">預覽作答(需存檔後才能看到最新內容)</button>
    }
    else
    {
        @Html.PermissionButton("存檔", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-default", onclick = "Save('" + (string)ViewBag.DATA_TYPE_A + "')" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
    }

}

}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

<button class="btn btn-sm btn-sys" type="button" onclick="Save('Index')">回有獎徵答列表</button>
@if (string.IsNullOrWhiteSpace(Model.uADDT11.DIALOG_ID))
{
    <button class="btn btn-sm btn-sys" type="button" onclick="Save('Excel')">Excel匯入</button>
    <button class="btn btn-sm btn-sys" type="button" onclick="Save('Copy')">從題庫新增</button>
}
<div class="table-92Per">
    <label class="label_dt">PS.</label>
    <br />
    <label class="label_dt">有獎徵答題目發佈後 請勿異動題數及修改考題內容及刪除活動，以免影響已作答權益。</label>
    <br />
    <label class="label_dt">系統提供【提早結案】功能，來關閉活動。</label>
    <br />
    <i class="glyphicon glyphicon-edit fa-1x"></i><label class="label_dt">  此圖示代表可使用編輯器，點選此圖示就能進入編輯器模式，再點選一次可返回原模式。</label>
</div>
<br />
<br />

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(model => model.REF_KEY)

    <img src="~/Content/img/web-bar2-revise-07.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI05">
        <div class="form-horizontal">
            <div style="height:15px"></div>
            <div class="Caption_Div">
                @ViewBag.Panel_Title
            </div>
            <div style="height:15px"></div>
            <div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.SCHOOL_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.uADDT11.SCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoItems, new { @class = "form-control", @placeholder = "必填" })
                        @Html.ValidationMessageFor(model => model.uADDT11.SCHOOL_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.DIALOG_TYPE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.uADDT11.DIALOG_TYPE, (IEnumerable<SelectListItem>)ViewBag.DialogTypeItems, new { @class = "form-control", @placeholder = "必填" })
                        @Html.ValidationMessageFor(model => model.uADDT11.DIALOG_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group" style="display:none">
                    @Html.LabelFor(model => model.uADDT11.SYEAR, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.SYEAR, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.SYEAR, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group" style="display:none">
                    @Html.LabelFor(model => model.uADDT11.SEMESTER, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.SEMESTER, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.SEMESTER, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.DIALOG_NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.DIALOG_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.DIALOG_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-md-3">
                        @Html.DisplayNameFor(model => model.uADDT11.DIALOG_EXPRESS)
                        <a title="一般輸入" style="cursor:pointer;display:none" class="glyphicon glyphicon-pencil fa-1x" role="button" onclick="OnremoveCkeditor('@Html.IdFor(m => m.uADDT11.DIALOG_EXPRESS)')" id="<EMAIL>(m => m.uADDT11.DIALOG_EXPRESS)"> </a>
                        <a title="編輯器輸入" style="cursor:pointer;display:inline" class="glyphicon glyphicon-edit fa-1x" role="button" onclick="OnLondCkeditor('@Html.IdFor(m => m.uADDT11.DIALOG_EXPRESS)')" id="<EMAIL>(m => m.uADDT11.DIALOG_EXPRESS)"></a>
                    </label>
                    <div class="col-md-9">
                        @Html.TextAreaFor(model => model.uADDT11.DIALOG_EXPRESS, new { @class = "form-control", @rows = "5", @placeholder = "必填" })
                        @Html.ValidationMessageFor(model => model.uADDT11.DIALOG_EXPRESS, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.DIALOG_SDATE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.DIALOG_SDATE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.DIALOG_SDATE, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.DIALOG_EDATE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.DIALOG_EDATE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.DIALOG_EDATE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.CASH, new { htmlAttributes = new { @class = "form-control", @placeholder = "空白預設系統預設值" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.CASH, "", new { @class = "text-danger" })
                        <span>*給學生點數(0-10點)</span>
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.GetCASH, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.GetCASH, new { htmlAttributes = new { @class = "form-control", @placeholder = "空白預設系統預設值" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.GetCASH, "", new { @class = "text-danger" })
                      <br /><span>*給老師點數(0-5點)</span>
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.ANSWER_COUNT, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.ANSWER_COUNT, new { htmlAttributes = new { @class = "form-control", @placeholder = "空白不限制" } })
                        @Html.ValidationMessageFor(model => model.uADDT11.ANSWER_COUNT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.ANSWER_PERSON_YN, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.ANSWER_PERSON_YN,
                            "_CheckBoxList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.uADDT11.ANSWER_PERSON_YN)).ToHtmlString(),
                                CheckBoxItems = HtnlHelperService.Y_SelectItem(Model.uADDT11 != null ? Model.uADDT11.ANSWER_PERSON_YN : ""),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "",
                            })
                        @Html.ValidationMessageFor(model => model.uADDT11.ANSWER_PERSON_YN, "", new { @class = "text-danger" })
                        <br>
                        <button type="button" class="btn btn-default btn-sm" title="選取對象" id="BTN_ANSWER_PERSON" href="@Url.Action("Index", "REFT01", new { BTN_ID = "#BTN_ANSWER_PERSON",REF_TABLE = "ADDT11" ,REF_KEY= Model.REF_KEY,REF_KEY_ID="#REF_KEY" })">選取對象</button>
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.COPY_YN, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT11.COPY_YN,
                            "_CheckBoxList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.uADDT11.COPY_YN)).ToHtmlString(),
                                CheckBoxItems = HtnlHelperService.Y_SelectItem(Model.uADDT11 != null ? Model.uADDT11.COPY_YN : ""),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue
                            })
                        @Html.ValidationMessageFor(model => model.uADDT11.COPY_YN, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT11.RANDOM, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        <label class='checkbox-inline'>
                            @Html.CheckBoxFor(model => model.uADDT11.RANDOM, new { htmlAttributes = new { @class = "form-control" } })
                            <label>是</label>
                        </label>
                        @Html.ValidationMessageFor(model => model.uADDT11.RANDOM, "", new { @class = "text-danger" })
                    </div>
                </div>
                @if (Model.FILE != null)
                {
                    <div class="form-group">
                        <label class="control-label col-md-3" for="File input">附件</label>
                        <div class="col-md-9">
                            @{
                                if (Model.FILE != null)
                                {
                                    int Num = 0;

                                    foreach (var FILEItem in Model.FILE)
                                    {
                                        <div id="FILE_@Num">
                                            @Html.Hidden("FILE[" + Num + "].BULLET_ID", FILEItem.DIALOG_ID)
                                            @Html.Hidden("FILE[" + Num + "].FILE_NAME", FILEItem.FILE_NAME)

                                            @Html.ActionLink(FILEItem.FILE_NAME, "DownLoad", new { controller = (string)ViewBag.BRE_NO, DIALOG_ID = FILEItem.DIALOG_ID, name = FILEItem.FILE_NAME }, new { @class = "btn btn-link" })

                                            <a href="#" onclick="DelFile('@FILEItem.DIALOG_ID','@FILEItem.FILE_NAME','FILE_@(Num)')" role="button" class=" btn-sm btn-default">X</a>
                                        </div>
                                        Num++;
                                    }
                                }
                                        <span style="color:red">
                                            *請上傳doc、ppt或圖檔，大小限制10M
                                        </span>
                            }
                        </div>
                    </div>
                }

                <div class="form-group" id="MultiFileUpload">
                    <label class="control-label col-md-3" for="File input">附件上傳</label>
                    <div class="col-md-9">
                        @Html.Action("MultiFileUpload", "Comm")
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top:20px;margin-bottom:30px;text-align:center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
    </div>

    @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
    <div class="Div-EZ-ADDI05">
        <div class="form-horizontal">
            <div class="Caption_Div">
                是非題
            </div>
            <div class="table-92Per" style="margin: 0px auto; ">
                <table class="table-ecool table-92Per table-hover">
                    <tbody>
                        <tr>
                            <td id="TruefalseEditorRows">
                                @if (Model.truefalseAns != null)
                                {
                                    foreach (var Data in Model.truefalseAns)
                                    {
                                        @Html.Partial("_truefalseAns", Data)
                                    }
                                }
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div style="height:1px;background-color:#DDDDDD;width:99%"></div>
                <div style="height:12px"></div>
                <div class="row">
                    <div class="col-md-12 col-xs-12 text-right">
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button" onclick="AddItem('TruefalseEditorRows', '_truefalseAns')">增加是非題筆數</button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top:20px;margin-bottom:30px;text-align:center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
    </div>

    @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
    <div class="Div-EZ-ADDI05">
        <div class="form-horizontal">
            <div class="Caption_Div">
                選擇題
            </div>
            <div class="table-92Per" style="margin: 0px auto; ">
                <table class="table-ecool table-92Per table-hover">
                    <tbody>
                        <tr>
                            <td id="MCQEditorRows">
                                @if (Model.multipleschoiceAns != null)
                                {
                                    foreach (var MCQData in Model.multipleschoiceAns)
                                    {
                                        @Html.Partial("_multipleschoiceAns", MCQData)
                                    }
                                }
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div style="height:1px;background-color:#DDDDDD;width:99%"></div>
                <div style="height:12px"></div>
                <div class="row">
                    <div class="col-md-12 col-xs-12  text-right">
                        <div class="input-group">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" onclick="AddItem('MCQEditorRows', '_multipleschoiceAns')">增加選擇題筆數</button>
                            </span>
                        </div><!-- /input-group -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <br />

    <div class="text-center">
        @buttonFun()
        <a href="#TOP" class="btn btn-default" role="button">
            TOP
        </a>
        <button class="btn btn-default" type="button" onclick="Save('Index')">回有獎徵答列表</button>
    </div>

    <div>
        @{

            @Html.HiddenFor(model => Model.uADDT11.DIALOG_ID)
            @Html.HiddenFor(model => Model.VIEW_DATA_TYPE)
            @Html.HiddenFor(model => Model.DATA_TYPE)
            @Html.HiddenFor(model => Model.uADDT11.STATUS)
            @Html.HiddenFor(model => Model.whereIndex)

            @Html.Hidden("Submit_YN", "Y")

            var QData = ViewBag.QData as ECOOL_APP.com.ecool.Models.DTO.ZZZI08ListViewModel;

            @Html.Hidden("OrderByName", QData.OrderByName)
            @Html.Hidden("SyntaxName", QData.SyntaxName)
            @Html.Hidden("Page", QData.Page)
            @Html.Hidden("SearchType", QData.SearchType)
            @Html.Hidden("Q_COPY_YN", QData.Q_COPY_YN)
            @Html.Hidden("Q_SCHOOL_NO", QData.Q_SCHOOL_NO)
            @Html.Hidden("SearchContents", QData.SearchContents)

        }
    </div>
}
@section Scripts {
    <script language="JavaScript">
        $(document).ready(function () {

            $("#BtnPreview,#BTN_ANSWER_PERSON").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });

            $("#uADDT11_DIALOG_SDATE,#uADDT11_DIALOG_EDATE").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });

            var Today = new Date();

            if ($("#uADDT11_DIALOG_SDATE").val() == "") {
                $("#uADDT11_DIALOG_SDATE").datepicker("setDate", Today);
            }

            if ($("#uADDT11_DIALOG_EDATE").val() == "") {
                Today.setMonth(Today.getMonth() + 1);
                $("#uADDT11_DIALOG_EDATE").datepicker('setDate', Today);
            }

            $('[data-toggle="tooltip"]').tooltip()

        });

        window.onload = function () {
            var VIEW_DATA_TYPE = $('#VIEW_DATA_TYPE').val();
            var STATUS = $('#uADDT11_STATUS').val();

            @* //if (VIEW_DATA_TYPE == '@ViewBag.VIEW_D') {
              //  $('.form-horizontal :input').attr('readonly', 'true');
              //  $('#MultiFileUpload').hide();
            }
           // else {
                if (STATUS == '@ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_R') {
                 //   $('.form-horizontal :input').attr('readonly', 'true');
                 //   $('#MultiFileUpload').hide();
               // }
           // }*@
        }

        //Lond Ckeditor
        function OnLondCkeditor(TxtId) {
            CKEDITOR.replace('' + TxtId + '', {
                toolbar: 'Image',
                filebrowserImageBrowseUrl: '@Url.Content("~/CkEdit/ImageBrowseUrl")',
                filebrowserAudioBrowseUrl: '@Url.Content("~/CkEdit/AudioBrowseUrl")',
                filebrowserVideoBrowseUrl: '@Url.Content("~/CkEdit/VedioBrowseUrl")',
            });
            $('#edit_' + TxtId).show()
            $('#Btn_' + TxtId).hide()
        }

        //remove Ckeditor
        function OnremoveCkeditor(TxtId) {
            if (('#' + TxtId).length > 0) {

                var editorId = $('#' + TxtId).attr("id");
                try {
                    var instance = CKEDITOR.instances[editorId];
                    if (instance) { instance.destroy(true); }
                }
                catch (e) { alert(e) }

                $('#edit_' + TxtId).hide()
                $('#Btn_' + TxtId).show()
            }

        }

        //增加問題
        function AddItem(editorRowsId, PartialView) {

            var UrlStr = "";
            if (PartialView == '_truefalseAns') {
                UrlStr = '@Url.Action("_truefalseAns")'
            }
            else {
                UrlStr = '@Url.Action("_multipleschoiceAns")'
            }

            $.ajax({
                url: '' + UrlStr + '',
                data: null,
                cache: false,
                success: function (html) {
                    $("#" + editorRowsId).append(html);
                }
            });
        }

        //del問題
        function onDelItem(index) {
            $('#' + index).remove();
        }

        //copy問題
        function onCopyItem(index, editorRowsId, PartialView) {

            var UrlStr = "";
            if (PartialView == '_truefalseAnsCopy') {
                UrlStr = '@Url.Action("_truefalseAnsCopy")'
            }
            else {
                UrlStr = '@Url.Action("_multipleschoiceAnsCopy")'
            }

            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].updateElement();
            }

            $('#@Html.IdFor(m => m.whereIndex)').val(index)

            $.ajax({
                url: '' + UrlStr + '',
                data: $('#form1').serialize(),
                cache: false,
                type: 'POST',
                success: function (html) {
                    $("#" + editorRowsId).append(html);
                }
            });
        }

        function Save(Val) {
            form1.DATA_TYPE.value = Val

            if (Val == "Index") {
                form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
            }
            else if (Val == "Copy")
            {
               
                $('#Q_COPY_YN').val("Y");
                form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
            }
            else if (Val == "Excel") {
                form1.action = '@Url.Action("ExcelIndex", (string)ViewBag.BRE_NO)';

            }
            else if (Val == "E") {
              
                if ($('#@Html.IdFor(m=> m.uADDT11.STATUS)').val() == '@ECOOL_APP.com.ecool.Models.entity.uADDT11.STATUS_R') {
                    var OK = confirm('資料已送佈，您確定要異動資料?')

                    if (OK) {
                        form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
                    }
                }
                else {
                  
                  
                        form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
                  
                }
            }

            form1.submit();

        }

        function DelFile(DIALOG_ID_Val, FILE_NAME_Val,IDF) {

            var OK = confirm('您確定要刪除附件?')

            if (OK) {
                $.ajax({
                    url: "@(Url.Action("DeleteFile", (string)ViewBag.BRE_NO))",     // url位置
                    type: 'post',                   // post/get
                    data: {
                        DIALOG_ID: DIALOG_ID_Val
                        , FILE_NAME: FILE_NAME_Val
                    },     // data
                    dataType: 'json',               // xml/json/script/html
                    cache: false,                   // 是否允許快取
                    success: function (data) {
                        var res = jQuery.parseJSON(data);

                        if (res.Success == 'false') {
                            alert(res.Error);
                        }
                        else {
                            $("#" + IDF).remove();
                        }
                    },
                    error: function (xhr, err) {
                        alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                        alert("responseText: " + xhr.responseText);
                    }
                });
            }
        }
    </script>
}