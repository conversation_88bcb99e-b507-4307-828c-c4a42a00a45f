﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace ECOOL_APP.EF
{
    public class ZZZI34EditMainViewModel
    {
        /// <summary>
        ///藝廊 NO.
        /// </summary>
        [DisplayName("藝廊 NO.")]
        public string ART_GALLERY_NO { get; set; }

        /// <summary>
        ///藝廊類別
        /// </summary>
        [DisplayName("藝廊類別")]
        public string ART_GALLERY_TYPE { get; set; }

        /// <summary>
        ///藝廊名稱
        /// </summary>
        [DisplayName("藝廊名稱")]
        [Required(ErrorMessage = "*此欄位必輸")]
        [StringLength(20, ErrorMessage = "*此欄位僅接受20個字以內的文字")]
        public string ART_SUBJECT { get; set; }

        /// <summary>
        ///藝廊描述
        /// </summary>
        [DisplayName("藝廊描述")]
        [StringLength(255, ErrorMessage = "*此欄位僅接受255個字以內的文字")]
        public string ART_DESC { get; set; }
 
        /// <summary>
        ///作品類別
        /// </summary>
        [DisplayName("作品類別")]
        [Required(ErrorMessage = "*此欄位必輸")]
        public string WORK_TYPE { get; set; }

        /// <summary>
        ///封面
        /// </summary>
        [DisplayName("選擇封面")]
        public string COVER_FILE { get; set; }


        public HttpPostedFileBase UploadCoverFile { get; set; }

        /// <summary>
        ///狀況
        /// </summary>
        [DisplayName("狀況")]
        public string STATUS { get; set; }

    
        /// <summary>
        ///批閱教師/批閱者
        /// </summary>
        [DisplayName("批閱教師/批閱者")]
        public string VERIFIER { get; set; }

        public string CRE_PERSON { get; set; }

        public string SCHOOL_NO { get; set; }

        [DisplayName("作廢原因")]
        public string BACK_MEMO { get; set; }
    }
}