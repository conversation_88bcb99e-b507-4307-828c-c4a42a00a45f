﻿@model AccreditationManageEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    var checkBoxsIsText = new List<CheckBoxListInfo>();
    CheckBoxListInfo cheboxIsTextItem = new CheckBoxListInfo();
    cheboxIsTextItem.DisplayText = "是 <span style=\"color:red\">勾選「是」，這個細項就可以用文字登錄，通常不勾</span>";
    cheboxIsTextItem.Value = "Y";
    if (Model != null && Model.Main != null && Model.Main.IsText != null)
    {
        cheboxIsTextItem.IsChecked = Model.Main.IsText == "Y" ? true : false;
    }
    checkBoxsIsText.Add(cheboxIsTextItem);
    var htmlAttributeCHRIS = new Dictionary<string, object>();
    htmlAttributeCHRIS.Add("id", "checkBoxsIsText");
    //htmlAttributeCHRIS.Add("onclick", "checkSEMESTERs();");
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_Ceri02Menu", "CERI02", new { NowAction = "CERI02" });
}
@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.Keyword)
    @Html.HiddenFor(m => m.Main.ACCREDITATION_ID)

    <img src="~/Content/img/AccreditationBook.png" class="img-responsive" style="max-height:150px" />

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            ※護照第一層設定
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ACCREDITATION_NAME, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.ACCREDITATION_NAME, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "必填", maxlength = "15" } })
                        <span style="color:red">(字數最多15個字)</span>
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ACCREDITATION_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ACCREDITATION_TYPE, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.Main.ACCREDITATION_TYPE, (IEnumerable<SelectListItem>)ViewBag.AccreditationTypeItems, new { @class = "form-control form-control-required", @placeholder = "必填" })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ACCREDITATION_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("是否是文字輸入：    ", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">

                        @Html.CheckBoxList("IsText", (List<CheckBoxListInfo>)checkBoxsIsText, htmlAttributeCHRIS, 1)<span></span>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top:20px;margin-bottom:30px;text-align:center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
    </div>

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            ※護照第二層設定
        </div>
        <div class="panel-body">
            <div id="DEditorRows">
                @if (Model.ContentData?.Count > 0)
                {
                    foreach (var Item in Model.ContentData)
                    {
                        Html.RenderPartial("_Details", Item);
                    }
                }
            </div>
        </div>
        <strong style="color:red;margin-left:20px">
            PS.<br />
            &nbsp; &nbsp; 1.如果是一年級要認證，認證時間請選一年級上學期和一年級下學期。<br />
            &nbsp; &nbsp; 2.通過主旨的字數最多15個字。<br />
            &nbsp; &nbsp;  3.通過條件(提示文字)的字數最多50個字。<br />
        </strong>
        <hr class="hr-line-dashed" />
        <div class="row">
            <div class="col-md-12 col-xs-12 text-right">
                <span class="input-group-btn">
                    <button class="btn btn-default" type="button" onclick="AddItemD()">
                        <span class="fa fa-plus" aria-hidden="true"></span>增加通過內容
                    </button>
                </span>
            </div>
        </div>
    </div>

    <div style="margin-top:20px;margin-bottom:30px;text-align:center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
    </div>

    <strong style="color:red">
        PS.不用特別選導師和管理者為登記者，因為導師預設可以登記該班學生，管理者預設可以登記全校學生。
    </strong>
    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            ※設定我要登記的老師
        </div>
        <div class="panel-body">
            <div id="VEditorRows">
                @if (Model.oTeachers?.Count > 0)
                {
                    foreach (var Item in Model.oTeachers)
                    {
                        Html.RenderPartial("_DetailsV", Item);
                    }
                }
            </div>
        </div>
        <hr class="hr-line-dashed" />
        <div class="row">
            <div class="col-md-12 col-xs-12 text-right">
                <span class="input-group-btn">
                    <button class="btn btn-default" type="button" onclick="AddItemV()">
                        <span class="fa fa-plus" aria-hidden="true"></span>增加認證老師
                    </button>
                </span>
            </div>
        </div>
    </div>

    <div class="text-center">
        <hr />
        <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
            取消
        </button>
        <button type="button" class="btn btn-default" onclick="onSave()">
            <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
        </button>
        @if (Model.Main?.ACCREDITATION_ID != null)
        {
            <button type="button" class="btn btn-default" onclick="onDel()">
                <span class="fa fa-trash" aria-hidden="true"></span>作廢
            </button>
        }
    </div>
}

@*挑選認證老師視窗*@
<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myLargeDetailsModalLabel" id="DivDetailsModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">增加認證老師</h4>
            </div>

            <div class="modal-body" id="DetailsModal">
            </div>
        </div><!-- /.modal-content -->
    </div>
</div>

@section Scripts {
    <script src="~/Scripts/jquery.magicsearch.js"></script>

    <script language="JavaScript">

        var targetFormID = '#form1';
        function checkSEMESTERs()
        {
            var checkBoxsIsTextstr = "";
            checkBoxsIsTextstr = $("#checkBoxsIsText").prop("checked");
            if (checkBoxsIsTextstr == true) {
                $(".bs-select-all").click()
                $(".dropdown-toggle").attr("disabled", "disabled");
            }
            else {
                $(".dropdown-toggle").removeAttr("disabled");

            }
        }
        function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onDel()
        {
            var OK = confirm("您確定要作廢?作廢會影響已登記的學生資料喔!!")

            if (OK==true)
            {
                $(targetFormID).attr("action", "@Url.Action("Del", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

         //增加通過內容
        function AddItemD() {
            var checkBoxsIsTextstr = "";
            checkBoxsIsTextstr = $("#checkBoxsIsText").prop("checked");
            if (checkBoxsIsTextstr == true) {

            }
             var data = {
                 IsCopy: true,
                 IsText: checkBoxsIsTextstr
            };

              $.ajax({
                url: '@Url.Action("_Details")',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                  success: function (data) {
                    $('#DEditorRows').append(data);
                }
              });
        }

        //增加認證老師
        function AddItemV() {

            var data = $('#VEditorRows :input').serializeArray();
            data.push({ name: 'DivEditorRowsID', value: '#VEditorRows' });
            data.push({name: 'UrlAction', value: '@Url.Action("_DetailsVData","CERI02")'});

             $.ajax({
                url: '@Url.Action("_TeacherModal","UserOpen")',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#DetailsModal').html(data);
                }
              });

            if ($('#DivDetailsModal').is(':visible') == false) {
                $('#DivDetailsModal').modal('show');
            }
        }

        //del 項目
        function deleteRow(Id, ITEM_NO_VAL) {

            if (ITEM_NO_VAL!='') {
                var OK = confirm("您確定要移除?移除會影響已登記的學生資料喔!!")

                if (OK==true)
                {
                    $('#' + Id).remove();
                }
            }
            else {
                   $('#' + Id).remove();
            }
        }
    </script>
}