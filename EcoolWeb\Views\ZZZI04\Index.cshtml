﻿@model IPagedList<ECOOL_APP.com.ecool.Models.entity.BT02AmdinViewModel>
    @using ECOOL_APP.com.ecool.util;
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    if ((string)ViewBag.Layout == "_LayoutChildMonth")
    {
        Layout = "~/Views/Shared/_LayoutChildMonth.cshtml";
    }
}

@if ((string)ViewBag.Layout != "_LayoutChildMonth")
{
    @Html.Partial("_Title_Secondary")
}
<style>

</style>
@Html.Partial("_Notice")
@Html.Hidden("PrevAction", (string)ViewBag.PrevAction)
@Html.Hidden("Layout", (string)ViewBag.Layout)

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{

    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">主旨</label>
        </div>
        <div class="form-group">
            <input class="form-control input-sm" type="text" placeholder="搜尋欲瀏覽之相關字串" name="SearchContents" value="@TempData["SearchContents"]">
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

    <br />
    <img src="~/Content/img/web-bar2-revise-02.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-ZZZI04 ">
                <thead>
                    <tr>
                        <th>
                            公告日期
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.First().SUBJECT)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.S_DATE, "ShortDateTime")
                            </td>
                            <td style="text-align: left;white-space:normal">

                                @*@using (Html.BeginForm("Details", (string)ViewBag.BRE_NO, FormMethod.Post, new { @class = "inline-form" }))
                                {
                                    @Html.Hidden("BULLET_ID", item.BULLET_ID)
                                    @Html.Hidden("SearchContents", TempData["SearchContents"])
                                    @Html.Hidden("page", TempData["page"])
                                    @Html.Hidden("PrevAction", (string)ViewBag.PrevAction)
                                    @Html.Hidden("Layout", (string)ViewBag.Layout)
                                    <button type="submit" class="btn-table-link" onclick="return confirm('確定要查看詳細資料嗎？');">@item.SUBJECT</button>
                                }*@
                                @*@Html.ActionLink(item.SUBJECT, "Details", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = item.BULLET_ID, SearchContents = TempData["SearchContents"], page = TempData["page"], PrevAction = (string)ViewBag.PrevAction, Layout = (string)ViewBag.Layout }, new { @class = "btn-table-link" })*@
                                <a href="javascript:void(0);" class="btn-table-link" onclick="postToDetails('@(HtmlUtility.ComputeSHA256(item.BULLET_ID))', '@HtmlUtility.ComputeSHA256((TempData["SearchContents"]?.ToString() ?? ""))', '@HtmlUtility.ComputeSHA256((TempData["page"]?.ToString() ??""))', '@HtmlUtility.ComputeSHA256(ViewBag.PrevAction)', '@HtmlUtility.ComputeSHA256(ViewBag.Layout)')">
                                 

                                    @item.SUBJECT
                                </a>
                                
                                @if (item.TOP_YN == "Y")
                                {
                                    <img src="~/Content/img/icon/hot.jpg" />
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <div>
        @Html.Pager(Model.PageSize, Model.PageNumber, Model.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
        @Html.Hidden("page", (int?)TempData["page"])


    </div>

    if ((string)ViewBag.Layout == "_LayoutChildMonth")
    {
    <div class="text-center">
        @Html.ActionLink("返回兒童月首頁", "ChildMonthIndex", new { controller = "Home" }, new { @class = "btn btn-default" })
    </div>
     
    }
}

<script language="JavaScript">

    var targetFormID = '#form1';

    function FunPageProc(pageno) {
        $('#Page').val(pageno)
        $(targetFormID).submit();
    }
     function postToDetails(bulletId, searchContents, page, prevAction, layout) {
      

        const form = document.createElement("form");
        form.method = "POST";
        form.action = '@Url.Action("Details", ViewBag.BRE_NO?.ToString())';

        const inputs = {
            BULLET_ID: bulletId,
            SearchContents: searchContents,
            page: page,
            PrevAction: prevAction,
            Layout: layout
        };

        for (const key in inputs) {
            const input = document.createElement("input");
            input.type = "hidden";
            input.name = key;
            input.value = inputs[key] ?? "";
            form.appendChild(input);
        }

        document.body.appendChild(form);
        form.submit();
    }
    function todoClear() {
        ////重設

        $(targetFormID).find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        $(targetFormID).submit();
    }
</script>