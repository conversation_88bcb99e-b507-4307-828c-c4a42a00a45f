/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Symbols/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Symbols={directory:"Symbols/Regular",family:"GyreTermesMathJax_Symbols",testString:"\u00A0\u2300\u2305\u2306\u2310\u2319\u231C\u231D\u231E\u231F\u2320\u2321\u2329\u232A\u239B",32:[0,0,250,0,0],160:[0,0,250,0,0],8960:[513,13,686,80,606],8965:[450,-50,660,80,580],8966:[546,46,660,80,580],8976:[276,0,686,80,606],8985:[500,-224,686,80,606],8988:[676,-410,426,80,346],8989:[676,-410,426,80,346],8990:[90,176,426,80,346],8991:[90,176,426,80,346],8992:[1212,0,776,325,696],8993:[1194,18,776,80,451],9001:[656,156,357,80,277],9002:[656,156,357,80,277],9115:[1202,0,624,143,528],9116:[396,0,624,143,236],9117:[1202,0,624,143,528],9118:[1202,0,624,96,481],9119:[396,0,624,387,481],9120:[1202,0,624,96,481],9121:[1216,0,491,143,395],9122:[810,0,491,143,227],9123:[1216,0,491,143,395],9124:[1216,0,491,96,348],9125:[810,0,491,264,348],9126:[1216,0,491,96,348],9127:[607,0,547,230,451],9128:[1194,0,547,96,314],9129:[607,0,547,230,451],9130:[596,0,547,233,317],9131:[607,0,547,96,317],9132:[1194,0,547,233,451],9133:[607,0,547,96,317],9134:[580,0,800,334,466],9138:[763,0,1353,80,1202],9139:[884,0,1353,80,1273],9143:[1656,0,674,120,514],10178:[650,0,660,80,580],10200:[650,150,960,80,880],10201:[650,150,960,80,880],10202:[650,150,1164,80,1084],10203:[650,150,1164,80,1084],10204:[400,-100,810,80,730],10205:[650,150,1160,80,1080],10206:[650,150,1160,80,1080],10208:[555,55,564,80,484],10209:[457,-43,574,80,494],10210:[457,-43,664,80,584],10211:[457,-43,664,80,584],10214:[668,168,398,120,318],10215:[668,168,398,80,278],10218:[656,156,543,80,463],10219:[656,156,543,80,463]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Symbols"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Symbols/Regular/Main.js"]);
