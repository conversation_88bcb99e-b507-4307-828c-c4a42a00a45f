﻿
@model  IQueryable<HRMT01QTY>
@*<h2>View</h2>
    <img src="https://bit.ly/3jFwe3d" onclick="openImg()" />*@

@{
    ECOOL_APP.UserProfile user = new ECOOL_APP.UserProfile();

    Layout = "~/Views/Shared/_LayoutSleepATM.cshtml";

    List<string> RankStr = new List<string>();
    RankStr.Add(" number-first");
    RankStr.Add(" number-second");
    RankStr.Add(" number-third");
    var AWAT02List = Model.OrderBy(a => a.den_rank).Take(10).ToList();

    var aWA003HRMT01List = Model.OrderBy(a => a.SUMCASHden_rank).Take(10).ToList();
}

<link href="~/Content/styles/leaderboard.css" rel="stylesheet" />
@*<script src="~/Content/colorbox/jquery.colorbox.js"></script>*@
<body class="leaderboard-page-bg-warm"  onclick="openImg()">
    <h1 class="text-hide">累計與現有排行榜</h1>
    <div class="container-fluid leaderboard-page-layout" >
        <div class="row">
            <div class="col-md-6 leaderboard-sunlight-small">
                <strong class="title title-total text-hide">累計酷幣排行榜</strong>
                <ul class="leaderboard-box leaderboard-box-red">

                    @{int i = 0;}
                    @foreach (var Item in AWAT02List.Take(3).ToList())
                    {
                        var cssRankStr = "";
                        var cssRankStrItem = "";
                        cssRankStr = "leaderboard-item" + RankStr.Skip(i).Take(1).FirstOrDefault();
                        cssRankStrItem = "text-hide number" + RankStr.Skip(i).Take(1).FirstOrDefault();

                        <li class='@cssRankStr'>
                            <span class='@cssRankStrItem'>@Item.den_rank</span>
                            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                            <span class="name">@Item.NAME</span>
                            <span class="value">@Item.GA <small>點數</small></span>
                        </li>
                        i++;





                    }

                </ul>  <ul class="leaderboard-list leaderboard-list-red">
    @foreach (var Item in AWAT02List.Skip(3).Take(6).ToList())
    {

        <li>
            <span class="number">@Item.den_rank</span>
            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
            <span class="name">@Item.NAME</span>
            <span class="value">@Item.GA 點數</span>
        </li>



    }
</ul>

                </div>
            <div class="col-md-6 leaderboard-sunlight-small">
                <strong class="title title-existing text-hide">現有資產排行榜</strong>
          
                <ul class="leaderboard-box leaderboard-box-purple">
                    @{int j = 0;}

                    @foreach (var Item in aWA003HRMT01List.Take(3).ToList())
                    {
                        var cssRankStr = "";
                        var cssRankStrItem = "";
                        cssRankStr = "leaderboard-item" + RankStr.Skip(j).Take(1).FirstOrDefault();
                        cssRankStrItem = "text-hide number" + RankStr.Skip(j).Take(1).FirstOrDefault();

                        <li class='@cssRankStr'>
                            <span class='@cssRankStrItem'>@Item.SUMCASHden_rank</span>
                            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                            <span class="name">@Item.NAME</span>
                            <span class="value">@Item.SUMCASH_AVAILABLE <small>點數</small></span>
                        </li>
                        j++;





                    }
                   
                </ul>
                <ul class="leaderboard-list leaderboard-list-purple">

                    @foreach (var Item in aWA003HRMT01List.Skip(3).Take(6).ToList())
                    {
                        <li>
                            <span class="number">@Item.SUMCASHden_rank</span>
                            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                            <span class="name">@Item.NAME</span>
                            <span class="value">@Item.SUMCASH_AVAILABLE 點數</span>
                        </li>}

                </ul>
            </div>
        </div>
    </div>
</body>
    <script>
        var box = document.querySelector('.leaderboard-page-bg-warm');
        box.addEventListener('mousemove', touch, false);
        function openImg() {
            window.location.href = "@Url.Action(ViewBag.FROMACTION, "BarcCodeMyCash")?WhereSchoolNo=" + "@Model.FirstOrDefault().SCHOOL_NO" + "&FROMACTION=" + "@ViewBag.FROMACTION" + "&TimeoutSeconds=" + "@ViewBag.TimeoutSeconds";
        }
        function touch() {       window.location.href = "@Url.Action(ViewBag.FROMACTION, "BarcCodeMyCash")?WhereSchoolNo=" +"@Model.FirstOrDefault().SCHOOL_NO"+"&FROMACTION="+"@ViewBag.FROMACTION"+"&TimeoutSeconds=" + "@ViewBag.TimeoutSeconds";}
    </script>
