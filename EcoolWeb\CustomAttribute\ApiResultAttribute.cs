﻿using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Web;
using System.Web.Http.Filters;

namespace EcoolWeb.CustomAttribute
{
    public class ApiResultAttribute : System.Web.Http.Filters.ActionFilterAttribute
    {

        
        public override void OnActionExecuted(HttpActionExecutedContext actionExecutedContext)
        {
            // 若發生例外則不在這邊處理
            if (actionExecutedContext.Exception != null)
                return;

            base.OnActionExecuted(actionExecutedContext);

            ApiResultModel result = new ApiResultModel();

            // 取得由 API 返回的狀態碼
            result.Status = actionExecutedContext.ActionContext.Response.StatusCode;

            // 取得由 API 返回的資料
            if (actionExecutedContext.ActionContext.Response.Content!=null)
            {
                result.Data = actionExecutedContext.ActionContext.Response.Content.ReadAsAsync<object>().Result;
            }

            
            // 重新封裝回傳格式
            actionExecutedContext.Response = actionExecutedContext.Request.CreateResponse(result.Status, result);
        }
    }

   
}

