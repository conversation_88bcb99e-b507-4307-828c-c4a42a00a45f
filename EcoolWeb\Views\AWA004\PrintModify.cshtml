﻿@model EcoolWeb.ViewModels.AWA004QueryViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    var itemCount = 0;
}
<style type="text/css">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }

    /*.row {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }

        .row > [class*='col-'] {
            padding-top: 15px;
            padding-bottom: 15px;
            margin-left: 5px;
            background-color: #FFFFFF;
            border-style: solid;
        }*/

    .wrapper {
        max-width: 80%;
        margin: 40px auto;
    }

    .element {
        background: #EEE;
        border: 2px solid #999;
        margin-bottom: 20px;
        width: 200px;
        float: left;
        margin-right: 20px;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 5px;
    }
</style>
<script src="~/Scripts/grids.js"></script>
<div class="container">
    <div class="table-92Per" style="margin: 0px auto; ">
        <div style="height:25px"></div>
        <div class="wrapper">
            @foreach (var item in Model.Chk)
            {
                @*if (itemCount != 0 && itemCount % 3 == 0)
            {
                @Html.Raw("</div><div class='row'>")
            }*@
                if (item.V004 != null)
                {
                    <div class="element">
                        @item.V004.CLASS_NO
                        <span>班</span>  @item.V004.NAME
                        <span>
                            於 @item.V004.TRANS_DATE.Value.ToString("yyyy-MM-dd HH:mm") 兌換 @item.V004.AWARD_NAME 獎品。

                            @if (item.ExpectedDate != null)
                            {
                                @:預計 @item.ExpectedDate.Value.ToString("yyyy-MM-dd HH:mm")
                                @:頒獎

                            }

                            @if (item.Content != null)
                            {
                                @:,請 @item.Content
                                @:領獎(集合)。
                            }
                        </span>
                    </div>
                }
               else if (item.V005 != null)
                {
                    <div class="element">
                        @item.V005.CLASS_NO
                        <span>班</span>  @item.V005.NAME
                        <span>
                            於 @item.V005.TRANS_DATE.Value.ToString("yyyy-MM-dd HH:mm") 兌換 @item.V005.AWARD_NAME 獎品。

                            @if (item.ExpectedDate != null)
                            {
                                @:預計 @item.ExpectedDate.Value.ToString("yyyy-MM-dd HH:mm")
                                @:頒獎

                            }

                            @if (item.Content != null)
                            {
                                @:,請 @item.Content
                                @:領獎(集合)。
                            }
                        </span>
                    </div>
                }
                itemCount++;
            }
        </div>
    </div>
</div>

<script type="text/javascript">

    $('.element').responsiveEqualHeightGrid();

    window.onload = function () {
        window.print()
    }
</script>