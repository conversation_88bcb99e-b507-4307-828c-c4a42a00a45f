﻿@model BarcCodeMyCashIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = null;
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
}
@* 新ATM酷幣查詢 *@
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    <link rel="Shortcut icon" type="image/x-icon" href="../Content/images/icon-ecool.png" />
    <link href="@Url.Content("~/Content/styles/ecc-ATM.min.css?v="+DateNowStr)" rel="stylesheet" />
</head>

<body>
    <script src="@Url.Content("~/Scripts/jquery-3.3.1.min.js")"></script>
    <script src="@Url.Content("~/Scripts/popper.min.js")"></script>
    <script src="@Url.Content("~/Scripts/bootstrap.min.js")"></script>
    <script src="@Url.Content("~/Scripts/jquery.jConveyorTicker.js")"></script>

    @using (Html.BeginForm("Index4", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
    {
        @Html.AntiForgeryToken()

        <div class="container" id="PageContent4">
            @Html.Action("_PageContent4", (string)ViewBag.BRE_NO)
        </div>
    }

    <div class="loader">
        <center>
            <h1 class="loading-image">
                <span class="glyphicon glyphicon-search"></span>查詢中，請稍後 ...
            </h1>
        </center>
    </div>

    <script>

         var targetFormID = '#form1';

        //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_PageContent4", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent4').html(data);
                    if ($("#StatusMessageHtmlMsg").html() != undefined){
                        location.reload();
                    }
                    console.log("#PageContent4");
                },
                beforeSend: function () {
                    $('.loader').show()
                },
                complete: function () {
                    setTimeout(function () { $('.loader').hide(); }, 1000);
                }
            });
        }

        $(function () {
            $('.loader').hide();
            $('.js-conveyor-1').jConveyorTicker();
        });
    </script>
</body>
</html>