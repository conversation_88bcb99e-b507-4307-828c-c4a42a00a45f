﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SECI03Hrmt09ListViewModel
    {
        ///Summary
        ///身份証
        ///Summary
        [DisplayName("身份証")]
        public string IDNO { get; set; }
        ///Summary
        ///學校代碼
        ///Summary
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        /// 學校名稱
        /// </summary>
        [DisplayName("學校名稱")]
        public string SCHOOL_NAME { get; set; }

        ///Summary
        ///學年
        ///Summary
        [DisplayName("學年")]
        public byte? SYEAR { get; set; }

        ///Summary
        ///學期
        ///Summary
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        ///Summary
        ///測驗日期
        ///Summary
         [DisplayName("測驗日期")]
        public string TDATE { get; set; }


        ///Summary
        ///學號
        ///Summary
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        ///Summary
        ///年級
        ///Summary
        [DisplayName("年級")]
        public string GRADE_SEMESTER { get; set; }


        ///Summary
        ///年級
        ///Summary
        [DisplayName("年級")]
        public byte? GRADE { get; set; }


        ///Summary
        ///班級
        ///Summary
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        ///Summary
        ///姓名
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///坐姿體前彎
        ///Summary
        [DisplayName("坐姿體前彎")]
        public decimal? V_SET_REACH_TEST { get; set; }

        ///Summary
        ///立定跳遠
        ///Summary
        [DisplayName("立定跳遠")]
        public decimal? S_L_JUMP_TEST { get; set; }

        ///Summary
        ///仰臥起坐
        ///Summary
        [DisplayName("仰臥起坐")]
        public decimal? SIT_UPS_TEST { get; set; }

        ///Summary
        ///800公尺跑走
        ///Summary
        [DisplayName("800公尺跑走")]
        public decimal? C_P_F_TEST { get; set; }


        public decimal? AVG_V_SET_REACH_TEST { get; set; }

        public decimal? AVG_S_L_JUMP_TEST { get; set; }

        public decimal? AVG_SIT_UPS_TEST { get; set; }

        public decimal? AVG_C_P_F_TEST { get; set; }

    }
}
