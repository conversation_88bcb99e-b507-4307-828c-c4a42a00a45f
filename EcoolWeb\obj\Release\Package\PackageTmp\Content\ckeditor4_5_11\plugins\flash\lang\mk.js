﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'mk', {
	access: 'Script Access', // MISSING
	accessAlways: 'Always', // MISSING
	accessNever: 'Never', // MISSING
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Abs Bottom', // MISSING
	alignAbsMiddle: 'Abs Middle', // MISSING
	alignBaseline: 'Baseline',
	alignTextTop: 'Text Top', // MISSING
	bgcolor: 'Background color', // MISSING
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'Loop', // MISSING
	chkMenu: 'Enable Flash Menu', // MISSING
	chkPlay: 'Auto Play', // MISSING
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'Хоризонтален простор',
	properties: 'Flash Properties', // MISSING
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'Scale', // MISSING
	scaleAll: 'Show all', // MISSING
	scaleFit: 'Exact Fit', // MISSING
	scaleNoBorder: 'No Border', // MISSING
	title: 'Flash Properties', // MISSING
	vSpace: 'Вертикален простор',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'URL must not be empty.', // MISSING
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
