﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI08ListViewModel



<div class="panel panel-primary">
    <div class="panel-heading">
        <h3 class="panel-title"><i class="glyphicon glyphicon-search"></i>查詢條件</h3>
    </div>

    <div class="panel-body">
        <div class="alert alert-dismissible alert-info">
            @Html.AntiForgeryToken()

            <div class="form-horizontal">
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                <div class="form-group">
                    @Html.LabelFor(model => model.Q_DIALOG_ID, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Q_DIALOG_ID, new { htmlAttributes = new { @class = "form-control", @placeholder = "可模糊查詢" } })
                        @Html.ValidationMessageFor(model => model.Q_DIALOG_ID, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Q_SCHOOL_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.DropDownListFor(model => model.Q_SCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoItems, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.Q_SCHOOL_NO, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Q_SYEAR, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Q_SYEAR, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入數字" } })
                        @Html.ValidationMessageFor(model => model.Q_SYEAR, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Q_SEMESTER, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Q_SEMESTER, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入數字" } })
                        @Html.ValidationMessageFor(model => model.Q_SEMESTER, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Q_DIALOG_NAME, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Q_DIALOG_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "可模糊查詢" } })
                        @Html.ValidationMessageFor(model => model.Q_DIALOG_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Q_DIALOG_SDATE, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Q_DIALOG_SDATE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.Q_DIALOG_SDATE, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Q_DIALOG_EDATE, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Q_DIALOG_EDATE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.Q_DIALOG_EDATE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Q_Name_Contents, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Q_Name_Contents, new { htmlAttributes = new { @class = "form-control", @placeholder = "可模糊查詢" } })
                        @Html.ValidationMessageFor(model => model.Q_Name_Contents, "", new { @class = "text-danger" })
                    </div>
                </div>
                 @*<div class="form-group">
                    @Html.LabelFor(model => model.Q_STATUS, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @{
                            string strQ_STATUS=string.Empty;

                            if (Model!=null)
                            {
                                if (Model.Q_STATUS != null)
                                {
                                    strQ_STATUS = "," + Model.Q_STATUS + ",";
                                }
                            }


                            string strchecked = "";

                            foreach (var item in ViewBag.StatuslListItem as IEnumerable<SelectListItem>)
                            {

                                string ThisValue = "," + item.Value + ",";
                                if (strQ_STATUS.ToUpper().IndexOf(ThisValue)>0)
                                {
                                    strchecked = "checked";
                                }
                                else
                                {
                                    strchecked = "";
                                }

                                    <input type="checkbox" name="Q_STATUS" value="@item.Value"  @strchecked />@item.Text
                            }

                        }

                        @Html.ValidationMessageFor(model => model.Q_STATUS, "", new { @class = "text-danger" })
                    </div>
                 </div>*@        
                <div class="form-group text-center">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary btn-block">查 詢</button>
                        @Html.ActionLink("回簡易查詢", "Index", new { controller = (string)ViewBag.BRE_NO, SearchType = "1" }, new { @class = "btn btn-primary btn-block", @role = "button" })
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
