﻿using System;
using System.Collections.Generic;
using System.Collections;

//using com.ecool.model.dao;

namespace com.ecool.service
{
    /// <summary>
    /// CommService
    /// </summary>
    public class SysDBService
    {
        /// <summary>
        /// 學校清單
        /// </summary>
        public List<Hashtable> USP_COMM_SCHOOL()
        {
            List<Hashtable> list_data = new List<Hashtable>();

            try
            {
                //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("select * from School");
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }


    }
}
