/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Misc={directory:"Misc/Regular",family:"STIXMathJax_Misc",testString:"\u00A0\u0250\u0251\u0252\u0253\u0254\u0255\u0256\u0257\u0258\u0259\u025A\u025B\u025C\u025D",32:[0,0,250,0,0],160:[0,0,250,0,0],592:[460,10,444,8,413],593:[460,10,500,27,491],594:[460,10,500,27,491],595:[683,10,500,69,468],596:[459,11,444,10,397],597:[460,160,444,25,417],598:[683,233,553,27,599],599:[683,10,587,27,602],600:[460,10,444,20,419],601:[460,10,444,14,413],602:[460,13,657,36,651],603:[475,14,438,20,389],604:[475,14,438,20,389],605:[475,14,623,20,603],606:[475,14,479,20,430],607:[460,218,315,-49,296],608:[683,212,594,32,634],609:[482,212,537,32,455],610:[450,11,570,30,539],611:[450,234,500,19,480],612:[450,10,500,13,486],613:[450,233,500,13,491],614:[683,0,500,9,487],615:[683,233,481,9,427],616:[683,0,278,16,253],617:[454,10,333,17,311],618:[450,0,258,21,231],619:[683,0,350,10,340],620:[683,0,375,12,362],621:[683,233,302,10,352],622:[683,233,549,19,538],623:[450,10,778,11,770],624:[450,233,803,11,785],625:[460,233,778,16,706],626:[460,233,529,-70,514],627:[460,233,533,16,603],628:[450,8,602,29,561],629:[460,10,500,29,470],630:[450,6,720,23,697],631:[475,4,667,37,629],632:[683,233,667,40,626],633:[450,10,370,30,360],634:[683,10,370,30,364],635:[450,233,418,30,468],636:[460,233,333,5,335],637:[460,233,370,7,339],638:[470,0,315,10,337],639:[470,0,350,5,332],640:[464,0,475,21,470],641:[464,0,475,21,470],642:[458,218,389,50,348],643:[683,233,322,-70,372],644:[683,218,304,-70,372],645:[470,233,400,15,457],646:[683,243,437,-23,422],647:[460,129,278,16,282],648:[579,233,270,13,283],649:[450,10,500,9,480],650:[450,10,537,46,490],651:[460,10,500,32,476],652:[464,0,500,-4,454],653:[464,0,722,21,694],654:[668,0,444,-2,459],655:[464,0,587,23,564],656:[450,218,528,27,569],657:[450,150,507,27,487],658:[450,233,413,12,392],659:[450,305,431,12,410],660:[683,0,450,47,400],661:[683,0,450,48,401],662:[662,14,450,47,400],663:[460,230,450,80,410],664:[679,17,723,33,690],665:[464,0,460,15,444],666:[475,14,479,20,430],667:[523,11,600,29,583],668:[464,0,572,21,560],669:[683,233,387,-23,412],670:[450,233,519,1,499],671:[464,0,470,21,441],672:[582,217,600,24,590],673:[683,0,450,48,401],674:[683,0,450,48,401],675:[683,10,802,27,775],676:[683,233,743,27,722],677:[683,160,864,27,844],678:[579,10,536,13,495],679:[683,233,483,13,540],680:[579,10,650,13,641],686:[469,232,619,15,612],687:[469,233,679,15,729],7424:[468,0,510,15,495],7431:[464,0,504,21,481],7452:[464,14,583,21,560],7553:[683,287,528,27,491],7556:[683,287,542,7,505],7557:[683,287,294,19,257],7562:[459,287,389,51,348],7565:[450,287,516,17,479],7566:[450,287,453,27,416],7576:[755,-425,441,57,387],7587:[757,-279,480,64,398],8319:[676,-270,541,57,484],8355:[662,0,556,11,546],8356:[676,8,500,12,490],8359:[662,10,1182,16,1141],8364:[664,12,500,38,462],8531:[676,14,750,36,725],8532:[676,14,750,14,731],8533:[676,14,750,37,715],8534:[676,14,750,14,720],8535:[676,14,750,13,720],8536:[676,14,750,14,720],8537:[676,14,750,37,717],8538:[676,15,750,29,722],8539:[676,14,750,37,722],8540:[676,14,750,13,727],8541:[676,14,750,29,727],8542:[676,14,750,28,727],9312:[676,14,684,0,684],9313:[676,14,684,0,684],9314:[676,14,684,0,684],9315:[676,14,684,0,684],9316:[676,14,684,0,684],9317:[676,14,684,0,684],9318:[676,14,684,0,684],9319:[676,14,684,0,684],9320:[676,14,684,0,684],9398:[676,14,684,0,684],9399:[676,14,684,0,684],9400:[676,14,684,0,684],9401:[676,14,684,0,684],9402:[676,14,684,0,684],9403:[676,14,684,0,684],9404:[676,14,684,0,684],9405:[676,14,684,0,684],9406:[676,14,684,0,684],9407:[676,14,684,0,684],9408:[676,14,684,0,684],9409:[676,14,684,0,684],9410:[676,14,684,0,684],9411:[676,14,684,0,684],9412:[676,14,684,0,684],9413:[676,14,684,0,684],9414:[676,14,684,0,684],9415:[676,14,684,0,684],9417:[676,14,684,0,684],9418:[676,14,684,0,684],9419:[676,14,684,0,684],9420:[676,14,684,0,684],9421:[676,14,684,0,684],9422:[676,14,684,0,684],9423:[676,14,684,0,684],9424:[676,14,684,0,684],9425:[676,14,684,0,684],9426:[676,14,684,0,684],9427:[676,14,684,0,684],9428:[676,14,684,0,684],9429:[676,14,684,0,684],9430:[676,14,684,0,684],9431:[676,14,684,0,684],9432:[676,14,684,0,684],9433:[676,14,684,0,684],9434:[676,14,684,0,684],9435:[676,14,684,0,684],9436:[676,14,684,0,684],9437:[676,14,684,0,684],9438:[676,14,684,0,684],9439:[676,14,684,0,684],9440:[676,14,684,0,684],9441:[676,14,684,0,684],9442:[676,14,684,0,684],9443:[676,14,684,0,684],9444:[676,14,684,0,684],9445:[676,14,684,0,684],9446:[676,14,684,0,684],9447:[676,14,684,0,684],9448:[676,14,684,0,684],9449:[676,14,684,0,684],9450:[676,14,684,0,684],9986:[612,-82,961,35,905],9993:[555,-138,690,34,638],10026:[613,106,789,35,733],10038:[616,108,695,35,642],10045:[612,108,682,35,626],10098:[719,213,488,188,466],10099:[719,213,488,22,300],10112:[705,14,788,35,733],10113:[705,14,788,35,733],10114:[705,14,788,35,733],10115:[705,14,788,35,733],10116:[705,14,788,35,733],10117:[705,14,788,35,733],10118:[705,14,788,35,733],10119:[705,14,788,35,733],10120:[705,14,788,35,733],10121:[705,14,788,35,733],10122:[705,14,788,35,733],10123:[705,14,788,35,733],10124:[705,14,788,35,733],10125:[705,14,788,35,733],10126:[705,14,788,35,733],10127:[705,14,788,35,733],10128:[705,14,788,35,733],10129:[705,14,788,35,733],10130:[705,14,788,35,733],10131:[705,14,788,35,733],10139:[433,-70,918,35,861],57391:[662,156,685,23,662],57508:[470,233,378,10,358],57509:[669,-426,397,75,338],57510:[216,144,444,38,429],57527:[324,-183,281,70,211],57528:[943,11,1344,67,1302],57529:[943,11,1344,67,1302],57532:[547,41,685,47,635],57563:[610,104,1472,86,1402],57564:[354,-152,1134,65,1069],57586:[936,157,1059,38,1033],57587:[662,156,1059,196,862],57588:[694,168,773,55,718],57589:[672,146,926,55,872],57590:[747,114,909,23,886],57591:[727,102,956,22,934],57592:[474,89,500,163,336],57593:[680,0,767,88,679],57594:[474,89,297,62,235],57595:[680,0,1750,88,1662],57596:[680,0,1625,88,1537],57597:[680,0,1625,88,1537],57598:[680,0,1625,88,1537],57599:[680,0,1625,88,1537],57600:[680,0,2032,88,1944],57601:[680,0,1625,88,1537],57602:[680,0,1608,88,1520],57603:[680,0,2296,88,2208],57605:[680,0,2032,88,1944],57606:[680,0,2032,88,1944],57607:[680,0,1625,88,1537],57608:[680,0,1625,88,1537],57656:[634,-584,480,-10,490],57657:[-127,177,480,-10,490],57658:[943,11,735,67,1302]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Misc"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Misc/Regular/Main.js"]);
