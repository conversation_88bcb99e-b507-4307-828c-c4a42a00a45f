﻿@model ECOOL_APP.com.ecool.Models.DTO.Menu
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<!-- 手機-->
<div class="collapse navbar-ECOOL navbar-ex1-collapse" style="position: fixed;z-index:999;top:50px;left:0px;width:100%;background-color:ghostwhite">

    <ul class="navPhone navbar-nav">

        @if (user != null)
        {
            if ((Model?.ZZZ?.Count() ?? 0) > 0)
            {
                <li class="dropdown">
                    <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown"><i class="glyphicon glyphicon-cog iconPhone"></i>維運管理<b class="caret"></b></a>
                    <ul class="dropdown-menuEz">
                        @foreach (var ITEM in Model.ZZZ)
                        {
                            <li>
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            </li>
                        }
                    </ul>
                </li>
            }
        }
    </ul>
    <div style="height:10px"></div>
</div><!-- /.navbar-collapse -->
<!-- pc-->
<div class="hidden-xs">
    @if (user == null)
    {
        @Html.Partial("../Shared/_Login", "Y")
    }
    else
    {
        <table border="0" cellspacing="0" cellpadding="0" align="center" style="width:100%;max-width:179px">

            @if (Model.ZZZ.Count() > 0)
            {
                <tr>
                    <td align="center">
                        <img src="~/Content/img/web-revise-secretary-05.png" class="imgMenu" />
                        <div class="td_Menu_blue">
                            @{
                                int num = 0;

                                foreach (var ITEM in Model.ZZZ)
                                {
                                    num++;
                                    if (num > 1)
                                    {
                                        <br />
                                    }
                                    <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                                }
                            }
                            <br />
                            <a href='@Url.Action("Logout","Home")'> <i class="fa fa-power-off" title="登出"></i> 登出</a>
                        </div>
                    </td>
                </tr>
          
                <tr>
                    <td height="5px"></td>
                </tr>
            }
        </table>
    }
</div>