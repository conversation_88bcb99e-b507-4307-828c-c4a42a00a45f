/*
 Highcharts JS v6.0.6 (2018-02-05)
 Gantt series

 (c) 2016 Lars <PERSON>

 --- WORK IN PROGRESS ---

 License: www.highcharts.com/license
*/
(function(l){"object"===typeof module&&module.exports?module.exports=l:l(Highcharts)})(function(l){(function(e){var l=e.each,u=e.isObject,p=e.pick,n=e.wrap,m=e.Axis,t=e.Chart,q=e.Tick;m.prototype.isOuterAxis=function(){var a=this,d=-1,c=!0;l(this.chart.axes,function(f,g){f.side===a.side&&(f===a?d=g:0<=d&&g>d&&(c=!1))});return c};q.prototype.getLabelWidth=function(){return this.label.getBBox().width};m.prototype.getMaxLabelLength=function(a){var d=this.tickPositions,c=this.ticks,f=0;if(!this.maxLabelLength||
a)l(d,function(a){(a=c[a])&&a.labelLength>f&&(f=a.labelLength)}),this.maxLabelLength=f;return this.maxLabelLength};m.prototype.addTitle=function(){var a=this.chart.renderer,d=this.axisParent,c=this.horiz,f=this.opposite,g=this.options,b=g.title,k;this.showAxis=k=this.hasData()||p(g.showEmpty,!0);g.title="";this.axisTitle||((g=b.textAlign)||(g=(c?{low:"left",middle:"center",high:"right"}:{low:f?"right":"left",middle:"center",high:f?"left":"right"})[b.align]),this.axisTitle=a.text(b.text,0,0,b.useHTML).attr({zIndex:7,
rotation:b.rotation||0,align:g}).addClass("highcharts-axis-title").add(d),this.axisTitle.isNew=!0);this.axisTitle[k?"show":"hide"](!0)};e.dateFormats={W:function(a){a=new this.Date(a);var d=0===this.get("Day",a)?7:this.get("Day",a),c=a.getTime(),f=new Date(this.get("FullYear",a),0,1,-6);this.set("Date",a,this.get("Date",a)+4-d);return 1+Math.floor(Math.floor((c-f)/864E5)/7)},E:function(a){return this.dateFormat("%a",a,!0).charAt(0)}};n(q.prototype,"addLabel",function(a){var d=this.axis,c=void 0!==
d.options.categories,f=d.tickPositions,f=this.pos!==f[f.length-1];(!d.options.grid||c||f)&&a.apply(this)});n(q.prototype,"getLabelPosition",function(a,d,c,f){var g=a.apply(this,Array.prototype.slice.call(arguments,1)),b=this.axis,k=b.options,h=k.tickInterval||1,r,e;k.grid&&(r=k.labels.style.fontSize,e=b.chart.renderer.fontMetrics(r,f),r=e.b,e=e.h,b.horiz&&void 0===k.categories?(k=b.axisGroup.getBBox().height,h=this.pos+h/2,g.x=b.translate(h)+b.left,h=k/2+e/2-Math.abs(e-r),g.y=0===b.side?c-h:c+h):
(void 0===k.categories&&(h=this.pos+h/2,g.y=b.translate(h)+b.top+r/2),h=this.getLabelWidth()/2-b.maxLabelLength/2,g.x=3===b.side?g.x+h:g.x-h));return g});n(m.prototype,"tickSize",function(a){var d=a.apply(this,Array.prototype.slice.call(arguments,1)),c;this.options.grid&&!this.horiz&&(c=2*Math.abs(this.defaultLeftAxisOptions.labels.x),this.maxLabelLength||(this.maxLabelLength=this.getMaxLabelLength()),c=this.maxLabelLength+c,d[0]=c);return d});n(m.prototype,"getOffset",function(a){var d=this.chart.axisOffset,
c=this.side,f,g,b=this.options,k=b.title,h=k&&k.text&&!1!==k.enabled;this.options.grid&&u(this.options.title)?(g=this.tickSize("tick")[0],d[c]&&g&&(f=d[c]+g),h&&this.addTitle(),a.apply(this,Array.prototype.slice.call(arguments,1)),d[c]=p(f,d[c]),b.title=k):a.apply(this,Array.prototype.slice.call(arguments,1))});n(m.prototype,"renderUnsquish",function(a){this.options.grid&&(this.labelRotation=0,this.options.labels.rotation=0);a.apply(this)});n(m.prototype,"setOptions",function(a,d){d.grid&&this.horiz&&
(d.startOnTick=!0,d.minPadding=0,d.endOnTick=!0);a.apply(this,Array.prototype.slice.call(arguments,1))});n(m.prototype,"render",function(a){var d=this.options,c,f,g,b,k,h,r=this.chart.renderer;if(d.grid){if(c=2*Math.abs(this.defaultLeftAxisOptions.labels.x),c=this.maxLabelLength+c,f=d.lineWidth,this.rightWall&&this.rightWall.destroy(),a.apply(this),a=this.axisGroup.getBBox(),this.horiz&&(this.rightWall=r.path(["M",a.x+this.width+1,a.y,"L",a.x+this.width+1,a.y+a.height]).attr({stroke:d.tickColor||
"#ccd6eb","stroke-width":d.tickWidth||1,zIndex:7,class:"grid-wall"}).add(this.axisGroup)),this.isOuterAxis()&&this.axisLine&&(this.horiz&&(c=a.height-1),f)){a=this.getLinePath(f);k=a.indexOf("M")+1;h=a.indexOf("L")+1;g=a.indexOf("M")+2;b=a.indexOf("L")+2;if(0===this.side||3===this.side)c=-c;this.horiz?(a[g]+=c,a[b]+=c):(a[k]+=c,a[h]+=c);this.axisLineExtra?this.axisLineExtra.animate({d:a}):this.axisLineExtra=r.path(a).attr({stroke:d.lineColor,"stroke-width":f,zIndex:7}).add(this.axisGroup);this.axisLine[this.showAxis?
"show":"hide"](!0)}}else a.apply(this)});n(t.prototype,"render",function(a){var d=25/11,c,f;l(this.axes,function(a){var b=a.options;b.grid&&(f=b.labels.style.fontSize,c=a.chart.renderer.fontMetrics(f),"datetime"===b.type&&(b.units=[["millisecond",[1]],["second",[1]],["minute",[1]],["hour",[1]],["day",[1]],["week",[1]],["month",[1]],["year",null]]),a.horiz?b.tickLength=b.cellHeight||c.h*d:(b.tickWidth=1,b.lineWidth||(b.lineWidth=1)))});a.apply(this)})})(l);(function(e){var l=e.defined,u=e.seriesTypes.column,
p=e.each,n=e.isNumber,m=e.isObject,t=e.merge,q=e.pick,a=e.seriesType,d=e.wrap,c=e.Axis,f=e.Point,g=e.Series;a("xrange","column",{colorByPoint:!0,dataLabels:{verticalAlign:"middle",inside:!0,formatter:function(){var b=this.point.partialFill;m(b)&&(b=b.amount);l(b)||(b=0);return 100*b+"%"}},tooltip:{headerFormat:'\x3cspan style\x3d"font-size: 0.85em"\x3e{point.x} - {point.x2}\x3c/span\x3e\x3cbr/\x3e',pointFormat:'\x3cspan style\x3d"color:{point.color}"\x3e\u25cf\x3c/span\x3e {series.name}: \x3cb\x3e{point.yCategory}\x3c/b\x3e\x3cbr/\x3e'},
borderRadius:3,pointRange:0},{type:"xrange",parallelArrays:["x","x2","y"],requireSorting:!1,animate:e.seriesTypes.line.prototype.animate,cropShoulder:1,getExtremesFromAll:!0,getColumnMetrics:function(){function b(){p(h.series,function(b){var a=b.xAxis;b.xAxis=b.yAxis;b.yAxis=a})}var a,h=this.chart;b();a=u.prototype.getColumnMetrics.call(this);b();return a},cropData:function(b,a,h,c){a=g.prototype.cropData.call(this,this.x2Data,a,h,c);a.xData=b.slice(a.start,a.end);return a},translatePoint:function(b){var a=
this.xAxis,h=this.columnMetrics,c=this.options.minPointLength||0,d=b.plotX,f=q(b.x2,b.x+(b.len||0)),e=a.translate(f,0,0,0,1),f=e-d,g=this.chart.inverted,l=q(this.options.borderWidth,1)%2/2;c&&(c-=f,0>c&&(c=0),d-=c/2,e+=c/2);d=Math.max(d,-10);e=Math.min(Math.max(e,-10),a.len+10);b.shapeArgs={x:Math.floor(Math.min(d,e))+l,y:Math.floor(b.plotY+h.offset)+l,width:Math.round(Math.abs(e-d)),height:Math.round(h.width),r:this.options.borderRadius};c=b.shapeArgs.x;e=c+b.shapeArgs.width;0>c||e>a.len?(c=Math.min(a.len,
Math.max(0,c)),e=Math.max(0,Math.min(e,a.len)),a=e-c,b.dlBox=t(b.shapeArgs,{x:c,width:e-c,centerX:a?a/2:null})):b.dlBox=null;b.tooltipPos[0]+=g?0:f/2;b.tooltipPos[1]-=g?f/2:h.width/2;if(a=b.partialFill)m(a)&&(a=a.amount),n(a)||(a=0),h=b.shapeArgs,b.partShapeArgs={x:h.x,y:h.y,width:h.width,height:h.height,r:this.options.borderRadius},b.clipRectArgs={x:h.x,y:h.y,width:Math.max(Math.round(f*a+(b.plotX-d)),0),height:h.height}},translate:function(){u.prototype.translate.apply(this,arguments);p(this.points,
function(b){this.translatePoint(b)},this)},drawPoint:function(b,a){var c=this.chart.renderer,d=b.graphic,e=b.shapeType,f=b.shapeArgs,g=b.partShapeArgs,k=b.clipRectArgs;if(b.isNull)d&&(b.graphic=d.destroy());else{if(d)b.graphicOriginal[a](t(f));else b.graphic=d=c.g("point").addClass(b.getClassName()).add(b.group||this.group),b.graphicOriginal=c[e](f).addClass(b.getClassName()).addClass("highcharts-partfill-original").add(d);g&&(b.graphicOverlay?(b.graphicOverlay[a](t(g)),b.clipRect.animate(t(k))):
(b.clipRect=c.clipRect(k.x,k.y,k.width,k.height),b.graphicOverlay=c[e](g).addClass("highcharts-partfill-overlay").add(d).clip(b.clipRect)))}},drawPoints:function(){var b=this,a=this.chart.pointCount<(b.options.animationLimit||250)?"animate":"attr";p(b.points,function(c){b.drawPoint(c,a)})}},{init:function(){f.prototype.init.apply(this,arguments);var b=this.series.chart.options.chart.colorCount;this.y||(this.y=0);this.colorIndex=q(this.options.colorIndex,this.y%b);return this},getLabelConfig:function(){var b=
f.prototype.getLabelConfig.call(this),a=this.series.yAxis.categories;b.x2=this.x2;b.yCategory=this.yCategory=a&&a[this.y];return b},tooltipDateKeys:["x","x2"],isValid:function(){return"number"===typeof this.x&&"number"===typeof this.x2}});d(c.prototype,"getSeriesExtremes",function(b){var a=this.series,c,d;b.call(this);this.isXAxis&&(c=q(this.dataMax,-Number.MAX_VALUE),p(a,function(a){a.x2Data&&p(a.x2Data,function(a){a>c&&(c=a,d=!0)})}),d&&(this.dataMax=c))})})(l)});
