/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"],{710:[694,-520,575,126,448],711:[660,-515,575,130,444],713:[607,-540,575,80,494],714:[706,-503,575,236,460],715:[706,-503,575,114,338],728:[694,-500,575,102,472],729:[695,-525,575,202,372],730:[702,-536,575,160,414],732:[694,-552,575,96,478]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/SpacingModLetters.js");
