(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: grouping - updated 9/27/2017 (v2.29.0) */
!function(i){"use strict";var l=i.tablesorter,g=l.grouping={types:{number:function(r,o,e,u){var p,t=o.hasClass(l.css.sortAsc);return 1<u&&""!==e?(p=t?Math.floor(parseFloat(e)/u)*u:Math.ceil(parseFloat(e)/u)*u,p+=" - "+(p+(u-1)*(t?1:-1))):p=parseFloat(e)||e,p},separator:function(r,o,e,u){var p=(e+"").split(r.widgetOptions.group_separator);return i.trim(p[u-1]||"")},text:function(r,o,e){return e},word:function(r,o,e,u){return((e+" ").match(/\w+/g)||[])[u-1]||""},letter:function(r,o,e,u){return e?(e+" ").substring(0,u):""},date:function(r,o,e,u){var p,t,a=r.widgetOptions,s=new Date(e||"");return s instanceof Date&&isFinite(s)?(p=s.getFullYear(),t=g.findMonth(a,s.getMonth()),"year"===u?p:"month"===u?t:"monthyear"===u?t+" "+p:"day"===u?t+" "+s.getDate():"week"===u?g.findWeek(a,s.getDay()):"time"===u?g.findTime(a,s):"hour"===u?g.findTime(a,s,"hour"):a.group_dateString(s,r,o)):a.group_dateInvalid}},findMonth:function(r,o){return r.group_months[o+(""===(r.group_months[0]||"")?1:0)]},findWeek:function(r,o){if(i.isArray(r.group_week))return r.group_week[o];if(!i.isEmptyObject(r.group_week)){return r.group_week[["sun","mon","tue","wed","thu","fri","sat"][o]]}},findTime:function(r,o,e){var u,p=r.group_time.am&&r.group_time.pm,t=o.getHours(),a=12<=t?1:0,s=("00"+(r.group_time24Hour&&12<t?t-12:r.group_time24Hour&&0===t?t+12:t)).slice(-2),n=("00"+o.getMinutes()).slice(-2);return u=r.group_time[p?["am","pm"][a]:a],"hour"===e?s:s+":"+n+(r.group_time24Hour?"":" "+(u||""))},update:function(r){if(!i.isEmptyObject(r.config.cache)){var o=r.config,e=o.widgetOptions,u=void 0!==o.sortList[0],p={},t=i.isArray(e.group_forceColumn)&&void 0!==e.group_forceColumn[0]?e.group_enforceSort&&!u?-1:e.group_forceColumn[0]:u?o.sortList[0][0]:-1;o.$table.find("tr.group-hidden").removeClass("group-hidden").end().find("tr.group-header").remove(),e.group_collapsible&&o.$table.data("pagerSavedHeight",0),0<=t&&t<o.columns&&!o.$headerIndexed[t].hasClass("group-false")&&(e.group_collapsedGroup="",e.group_collapsedGroups={},p.column=t,p.groupClass=(o.$headerIndexed[t].attr("class")||"").match(/(group-\w+(-\w+)?)/g),p.grouping=p.groupClass?p.groupClass[0].split("-"):["group","letter",1],p.savedGroup=g.saveCurrentGrouping(o,e,p),g.findColumnGroups(o,e,p),g.processHeaders(o,e,p),o.$table.triggerHandler(e.group_complete))}},processHeaders:function(r,o,e){var u,p,t,a,s,n,l=r.$table.find("tr.group-header"),g=l.length;for(l.bind("selectstart",!1),u=0;u<g;u++)s=(n=l.eq(u)).nextUntil("tr.group-header").filter(":visible"),(o.group_count||i.isFunction(o.group_callback))&&(t=n.find(".group-count")).length&&(o.group_count&&t.html(o.group_count.toString().replace(/\{num\}/g,s.length)),i.isFunction(o.group_callback)&&o.group_callback(n.find("td"),s,e.column,r.table)),o.group_saveGroups&&!i.isEmptyObject(o.group_collapsedGroups)&&o.group_collapsedGroups[o.group_collapsedGroup].length?(a=n.find(".group-name").text().toLowerCase()+n.attr("data-group-index"),p=-1<i.inArray(a,o.group_collapsedGroups[o.group_collapsedGroup]),n.toggleClass("collapsed",p),s.toggleClass("group-hidden",p)):o.group_collapsed&&o.group_collapsible&&(n.addClass("collapsed"),s.addClass("group-hidden"))},groupHeaderHTML:function(r,o,e){var u=(e.currentGroup||"").toString().replace(/</g,"&lt;").replace(/>/g,"&gt;");return'<tr class="group-header '+r.selectorRemove.slice(1)+" "+(o.columnSelector_classHasSpan||"hasSpan")+'" unselectable="on" '+(r.tabIndex?'tabindex="0" ':"")+'data-group-index="'+e.groupIndex+'"><td colspan="'+r.columns+'">'+(o.group_collapsible?"<i/>":"")+'<span class="group-name">'+u+'</span><span class="group-count"></span></td></tr>'},saveCurrentGrouping:function(r,o,e){var u,p,t=!1;return o.group_collapsible&&o.group_saveGroups&&(o.group_collapsedGroups=l.storage&&l.storage(r.table,"tablesorter-groups")||{},p="dir"+r.sortList[0][1],u=o.group_collapsedGroup=""+r.sortList[0][0]+p+e.grouping.join(""),o.group_collapsedGroups[u]?t=!0:o.group_collapsedGroups[u]=[]),t},findColumnGroups:function(r,o,e){var u,p,t,a,s=l.hasWidget(r.table,"pager"),n=r.pager||{};for(u=e.groupIndex=0;u<r.$tbodies.length;u++)for(p=r.cache[u].normalized,e.group=void 0,t=s&&!n.ajax?n.startRow-1:0,a=s?n.endRow-(n.ajax?n.startRow:0):p.length;t<a;t++)e.rowData=p[t],e.rowData&&(e.$row=e.rowData[r.columns].$row,e.$row.is(":visible")&&g.types[e.grouping[1]]&&g.insertGroupHeader(r,o,e));l.hasWidget(r.table,"columnSelector")&&l.columnSelector.setUpColspan(r,o)},insertGroupHeader:function(r,o,e){var u=r.$headerIndexed[e.column],p=e.rowData[e.column],t=/date/.test(e.groupClass)?e.grouping[2]:parseInt(e.grouping[2]||1,10)||1;e.currentGroup=e.rowData?g.types[e.grouping[1]](r,u,p,t,e.group):e.currentGroup,e.group!==e.currentGroup&&(e.group=e.currentGroup,i.isFunction(o.group_formatter)&&(e.currentGroup=o.group_formatter((e.group||"").toString(),e.column,r.table,r,o,e)||e.group),e.$row.first().before(g.groupHeaderHTML(r,o,e)),o.group_saveGroups&&!e.savedGroup&&o.group_collapsed&&o.group_collapsible&&o.group_collapsedGroups[o.group_collapsedGroup].push(e.currentGroup+e.groupIndex),e.groupIndex++)},bindEvents:function(t,a,s){s.group_collapsible&&(s.group_collapsedGroups=[],a.$table.on("click toggleGroup keyup","tr.group-header",function(r){if(r.stopPropagation(),"keyup"!==r.type||13===r.which){var o,e,u=i(this),p=u.find(".group-name").text().toLowerCase()+u.attr("data-group-index");!r.shiftKey||"click"!==r.type&&"keyup"!==r.type||u.siblings(".group-header").trigger("toggleGroup"),u.toggleClass("collapsed"),u.nextUntil("tr.group-header").toggleClass("group-hidden",u.hasClass("collapsed")),!(o=u.hasClass("collapsed"))&&l.hasWidget(a.$table,"zebra")&&l.applyWidgetId(a.$table,"zebra"),s.group_saveGroups&&l.storage&&(s.group_collapsedGroups[s.group_collapsedGroup]||(s.group_collapsedGroups[s.group_collapsedGroup]=[]),o&&s.group_collapsedGroup?s.group_collapsedGroups[s.group_collapsedGroup].push(p):s.group_collapsedGroup&&-1<(e=i.inArray(p,s.group_collapsedGroups[s.group_collapsedGroup]))&&s.group_collapsedGroups[s.group_collapsedGroup].splice(e,1),l.storage(t,"tablesorter-groups",s.group_collapsedGroups))}})),i(s.group_saveReset).on("click",function(){g.clearSavedGroups(t)}),a.$table.on("pagerChange.tsgrouping",function(){g.update(t)})},clearSavedGroups:function(r){r&&l.storage&&(l.storage(r,"tablesorter-groups",""),g.update(r))}};l.addWidget({id:"group",priority:100,options:{group_collapsible:!0,group_collapsed:!1,group_saveGroups:!0,group_saveReset:null,group_count:" ({num})",group_separator:"-",group_formatter:null,group_callback:null,group_complete:"groupingComplete",group_forceColumn:[],group_enforceSort:!0,group_checkbox:["checked","unchecked"],group_months:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],group_week:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],group_time:["AM","PM"],group_time24Hour:!1,group_dateInvalid:"Invalid Date",group_dateString:function(r){return r.toLocaleString()}},init:function(r,o,e,u){g.bindEvents(r,e,u)},format:function(r){g.update(r)},remove:function(r,o){o.$table.off("click","tr.group-header").off("pagerChange.tsgrouping").find(".group-hidden").removeClass("group-hidden").end().find("tr.group-header").remove()}})}(jQuery);return jQuery;}));
