/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Misc-bold"]={directory:"Misc/Bold",family:"STIXMathJax_Misc",weight:"bold",testString:"\u00A0\u0250\u0251\u0252\u0253\u0254\u0255\u0256\u0257\u0258\u0259\u025A\u025B\u025C\u025D",32:[0,0,250,0,0],160:[0,0,250,0,0],592:[473,14,512,13,476],593:[473,14,608,32,594],594:[473,14,608,32,594],595:[691,14,560,74,523],596:[473,14,472,15,420],597:[473,161,477,30,445],598:[676,233,602,32,660],599:[683,14,650,32,660],600:[473,14,457,25,427],601:[473,14,457,19,421],602:[473,14,688,41,677],603:[475,14,493,25,439],604:[475,14,493,25,439],605:[475,14,683,25,658],606:[475,14,523,25,469],607:[461,203,338,-54,314],608:[683,245,627,37,662],609:[473,245,571,37,484],610:[461,11,590,35,555],611:[461,233,532,24,507],612:[450,10,514,17,497],613:[450,226,550,17,536],614:[683,0,550,14,532],615:[683,205,556,16,485],616:[691,0,292,21,262],617:[456,8,366,22,339],618:[461,0,297,26,264],619:[676,0,395,15,380],620:[676,0,446,17,428],621:[676,233,326,15,384],622:[676,236,619,24,603],623:[473,0,828,16,815],624:[473,233,859,16,836],625:[473,233,847,21,770],626:[473,233,625,-57,586],627:[473,233,610,21,668],628:[461,12,604,34,558],629:[473,14,520,34,485],630:[461,5,741,28,713],631:[477,2,696,42,653],632:[685,231,713,45,667],633:[463,10,456,35,441],634:[676,10,456,35,441],635:[463,233,506,35,564],636:[473,233,426,10,416],637:[473,233,454,12,418],638:[484,0,359,15,386],639:[484,0,404,10,381],640:[464,0,516,21,495],641:[464,0,516,21,495],642:[473,218,389,25,361],643:[683,233,458,-36,406],644:[683,233,373,-57,430],645:[470,233,396,8,452],646:[683,243,399,-16,449],647:[513,129,333,19,332],648:[630,233,339,19,389],649:[461,14,556,9,538],650:[452,8,500,13,487],651:[465,10,534,23,511],652:[475,0,500,21,485],653:[475,0,722,23,707],654:[666,0,500,16,482],655:[464,0,633,46,587],656:[461,218,531,21,577],657:[461,150,538,21,517],658:[450,236,440,8,430],659:[450,307,440,8,430],660:[683,0,417,55,426],661:[683,0,417,55,426],662:[669,14,417,55,426],663:[473,232,479,72,447],664:[680,17,723,13,708],665:[464,0,456,15,441],666:[475,14,465,11,455],667:[537,11,600,29,595],668:[464,0,582,21,561],669:[691,233,394,-60,414],670:[461,215,556,22,543],671:[464,0,470,17,440],672:[582,205,636,34,659],673:[683,0,500,55,426],674:[683,0,500,55,426],675:[676,14,868,25,843],676:[676,236,810,25,794],677:[676,164,960,25,933],678:[630,12,626,19,598],679:[683,233,540,19,626],680:[630,12,700,19,690],7424:[475,0,515,9,503],7431:[461,0,531,20,511],7452:[461,19,600,16,584],8319:[700,-275,491,15,478],8355:[676,0,611,11,583],8356:[684,16,500,21,477],8359:[676,14,1369,16,1341],8364:[672,12,500,29,478],8531:[688,12,750,-7,763],8532:[688,12,750,28,763],8533:[688,12,750,-7,775],8534:[688,12,750,28,775],8535:[688,12,750,23,775],8536:[688,12,750,22,775],8537:[688,12,750,-7,758],8538:[688,12,750,49,758],8539:[688,12,750,-7,775],8540:[688,12,750,23,775],8541:[688,12,750,49,775],8542:[688,12,750,30,775],9312:[690,19,695,0,695],9313:[690,19,695,0,695],9314:[690,19,695,0,695],9315:[690,19,695,0,695],9316:[690,19,695,0,695],9317:[690,19,695,0,695],9318:[690,19,695,0,695],9319:[690,19,695,0,695],9320:[690,19,695,0,695],9398:[690,19,695,0,695],9399:[690,19,695,0,695],9400:[690,19,695,0,695],9401:[690,19,695,0,695],9402:[690,19,695,0,695],9403:[690,19,695,0,695],9404:[690,19,695,0,695],9405:[690,19,695,0,695],9406:[690,19,695,0,695],9407:[690,19,695,0,695],9408:[690,19,695,0,695],9409:[690,19,695,0,695],9410:[690,19,695,0,695],9411:[690,19,695,0,695],9412:[690,19,695,0,695],9413:[690,19,695,0,695],9414:[690,19,695,0,695],9415:[690,19,695,0,695],9417:[690,19,695,0,695],9418:[690,19,695,0,695],9419:[690,19,695,0,695],9420:[690,19,695,0,695],9421:[690,19,695,0,695],9422:[690,19,695,0,695],9423:[690,19,695,0,695],9424:[690,19,695,0,695],9425:[690,19,695,0,695],9426:[690,19,695,0,695],9427:[690,19,695,0,695],9428:[690,19,695,0,695],9429:[690,19,695,0,695],9430:[690,19,695,0,695],9431:[690,19,695,0,695],9432:[690,19,695,0,695],9433:[690,19,695,0,695],9434:[690,19,695,0,695],9435:[690,19,695,0,695],9436:[690,19,695,0,695],9437:[690,19,695,0,695],9438:[690,19,695,0,695],9439:[690,19,695,0,695],9440:[690,19,695,0,695],9441:[690,19,695,0,695],9442:[690,19,695,0,695],9443:[690,19,695,0,695],9444:[690,19,695,0,695],9445:[690,19,695,0,695],9446:[690,19,695,0,695],9447:[690,19,695,0,695],9448:[690,19,695,0,695],9449:[690,19,695,0,695],9450:[690,19,695,0,695]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Misc-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Misc/Bold/Main.js"]);
