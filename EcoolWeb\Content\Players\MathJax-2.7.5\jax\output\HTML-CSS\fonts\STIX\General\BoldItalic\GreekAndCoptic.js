/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/GreekAndCoptic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{900:[680,-516,300,140,319],901:[680,-516,380,27,440],902:[693,0,667,-68,593],903:[459,-311,333,116,264],904:[693,0,700,10,748],905:[693,0,850,9,889],906:[693,0,450,9,503],908:[693,18,722,11,691],910:[693,0,700,8,855],911:[693,0,808,25,774],912:[680,9,278,6,419],913:[683,0,667,-68,593],914:[669,0,667,-25,624],915:[669,0,585,-13,670],916:[683,0,667,-65,549],917:[669,0,667,-27,653],918:[669,0,611,-12,589],919:[669,0,778,-24,799],920:[685,18,718,27,691],921:[669,0,389,-32,406],922:[669,0,667,-21,702],923:[683,0,655,-68,581],924:[669,12,889,-29,917],925:[669,15,722,-27,748],926:[669,0,746,25,740],927:[685,18,722,27,691],928:[669,0,778,-24,799],929:[669,0,611,-28,613],931:[669,0,633,-11,619],932:[669,0,611,49,650],933:[685,0,611,21,697],934:[669,0,771,26,763],935:[669,0,667,-24,694],936:[685,0,661,17,780],937:[685,0,808,25,774],938:[905,0,389,-32,486],939:[905,0,611,21,697],940:[680,13,576,-3,574],941:[680,13,454,-5,408],942:[680,205,488,-7,474],943:[680,9,278,2,286],944:[680,13,536,-7,500],945:[462,13,576,-3,574],946:[698,205,500,-79,480],947:[462,204,438,3,461],948:[698,13,496,-3,456],949:[462,13,454,-5,408],950:[698,205,415,-5,473],951:[462,205,488,-7,474],952:[698,13,501,-3,488],953:[462,9,278,2,238],954:[462,12,500,-23,504],955:[698,18,484,-34,459],956:[449,205,523,-82,483],957:[462,13,469,-23,441],958:[698,205,415,-5,426],959:[462,13,500,-3,441],960:[449,15,558,-6,570],961:[462,205,495,-81,447],962:[462,205,415,-5,447],963:[449,13,499,-3,536],964:[449,9,415,4,455],965:[462,13,536,-7,477],966:[462,205,678,-3,619],967:[462,205,404,-136,515],968:[462,205,652,-5,715],969:[462,13,735,-3,676],970:[655,9,278,2,351],971:[655,13,536,-7,477],972:[680,13,500,-3,441],973:[680,13,536,-7,477],974:[680,13,735,-3,676],976:[696,12,500,42,479],977:[698,13,582,8,589],978:[685,0,611,21,696],981:[699,205,678,-3,619],982:[449,13,828,-2,844],984:[685,200,722,27,691],985:[462,205,500,-3,441],986:[685,205,669,32,665],987:[492,205,475,-5,509],988:[669,0,667,-13,670],989:[450,190,525,32,507],990:[793,18,757,-7,758],991:[698,0,485,16,466],992:[685,205,734,27,710],993:[639,205,530,47,467],1008:[462,15,569,-50,592],1009:[462,206,517,-12,458],1012:[685,18,722,27,691],1013:[463,13,466,-3,429],1014:[460,16,486,-5,427]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/GreekAndCoptic.js");
