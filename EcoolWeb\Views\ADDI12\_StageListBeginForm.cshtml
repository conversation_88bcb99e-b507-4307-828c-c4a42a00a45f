﻿@model ADDI12IndexListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}

@{@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

if ((Model.WhereIsColorboxForUser ?? false) == false)
{
    Html.RenderAction("_Menu", new { NowAction = "StageYoutubeView" });
}

if (Model?.ActionResultType == ADDI12IndexListViewModel.ActionResultTypeVal.PremierView)
{
    @Html.Action("_PremierViewImage")
}

}

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()

    <div id="PageContent">
        @Html.Action("_StagePageContent", (string)ViewBag.BRE_NO, new { ActionResultType = Model?.ActionResultType })
    </div>
}

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">表演人員名單</h4>
            </div>
            <div class="modal-body">
                <div id="PartialYoutubePersonListView"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    var targetFormID = '#form1';

        function onYoutubePersonList(STAGE_ID) {

        var data = {
            "STAGE_ID": STAGE_ID
        };

             $.ajax({
                url: '@Url.Action("_PartialYoutubePersonListView", (string)ViewBag.BRE_NO)',
                 data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PartialYoutubePersonListView').html(data);
                }
            });

        if ($('#myModal').is(':visible') == false) {
            $('#myModal').modal('show');
          }
    }

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };
       function FunPageProcTwo(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };
        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function Edit_show(Value) {
                $('#@Html.IdFor(m => m.Search.WhereSTAGE_ID)').val(Value)
                $(targetFormID).attr("action", "@Url.Action("Edit1", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
        }

        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.Search.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_StagePageContent", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear(IsSubmit) {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            if (IsSubmit) {
                FunPageProc(1);
            }

        }
</script>