﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.service;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZT36Controller : Controller
    {
        public ActionResult _ZZT36()
        {
            //UserProfile user = UserProfile.Get();
            //List<ZZT36> liZZT36 = new List<ZZT36>();
            //try
            //{
            //    liZZT36 = ZZT36Service.USP_ZZT36_QUERY(user.SCHOOL_NO, "HomeContent");
            //}
            //catch (Exception e)
            //{
            //    throw;
            //}
            //return PartialView("../ZZT36/_ZZT36", liZZT36);

            return PartialView("../ZZT36/_ZZT36");
        }
    }
}