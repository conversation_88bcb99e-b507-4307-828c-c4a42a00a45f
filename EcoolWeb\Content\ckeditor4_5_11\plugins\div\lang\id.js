﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'div', 'id', {
	IdInputLabel: 'Id',
	advisoryTitleInputLabel: 'Penase<PERSON>',
	cssClassInputLabel: '<PERSON><PERSON>heet',
	edit: 'Sunting Div',
	inlineStyleInputLabel: 'Inline Style', // MISSING
	langDirLTRLabel: '<PERSON><PERSON> ke <PERSON> (LTR)',
	langDirLabel: 'Arah Bahasa',
	langDirRTLLabel: '<PERSON><PERSON> ke <PERSON> (RTL)',
	languageCodeInputLabel: 'Kode Bahasa',
	remove: 'Hapus Div',
	styleSelectLabel: 'Gaya',
	title: 'Ciptakan Wadah Div',
	toolbar: 'Cipatakan Wadah Div'
} );
