﻿<style type="text/css">
    .classInputCode {
        display: none
    }
</style>
@{
    int iLockCount = (Session["LockCount"] != null) ? Convert.ToInt32(Session["LockCount"]) : 0;
    int iVlidatePasswordMaxCount = Convert.ToInt32(System.Web.Configuration.WebConfigurationManager.AppSettings["VlidatePasswordMaxCount"]);
}

<div class="layout_font_title">
    登入
</div>
<div style="height:20px"></div>
<div class="text-center" style="margin: 0px auto;">
    @using (Html.BeginForm("Login", "Home", FormMethod.Post, new { name = "loginform", defaultbutton = "Button1", defaultfocus = "TextBox1" }))
    {
        string SchoolNo = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
        string returnURL = ViewBag.UrlReferrer;
        @Html.Hidden("iLockCount", iLockCount)
        @Html.Hidden("iVlidatePasswordMaxCount", iVlidatePasswordMaxCount)
        @Html.Hidden("returnURL", returnURL)

        <div>
            @if (SchoolNo != null && SharedGlobal.HomeIndex != "ChildMonthIndex")
            {
                @Html.Hidden("txtSCHOOL_NO", SchoolNo)
            }
            else
            {
                var SchoolList = EcoolWeb.Models.UserProfileHelper.GetSchoolList().Select(x => new SelectListItem { Text = x.Value.SHORT_NAME, Value = x.Value.SCHOOL_NO, Selected = x.Value.SCHOOL_NO == SchoolNo });
                <div class="form-inlineEZ">
                    <div class="form-group">
                        <label>學 校 </label>
                        @Html.DropDownList("txtSCHOOL_NO", SchoolList.OrderBy(a => a.Text), "選擇學校...", new { @class = "form-control-Login" })
                    </div>
                </div>

            }

            <div class="form-inlineEZ">
                <div class="form-group">
                    <label>帳 號</label>
                    @Html.TextBox("txtUSER_NO", "", new { @class = "form-control-Login" })
                </div>
            </div>

            <div class="form-inlineEZ">
                <div class="form-group">
                    <label>密 碼</label>
                    @Html.Password("txtPASSWORD", "", new { @class = "form-control-Login" })
                </div>
            </div>

            <div class="classInputCode form-inlineEZ">
                <div class="form-group">
                    <label>驗 證</label>
                    @Html.TextBox("txtInputCode", "", new { @class = "form-control-Login", placeholder = "輸入驗證碼" })
                </div>
            </div>

            <div class="classInputCode form-inlineEZ">
                <div class="form-group">
                    <label>　 　 </label>
                    <img id="captcha" src="@Url.Action("VerificationCode","Home")" alt="驗證碼" />
                    <input type="button" class="btn btn-default btn-sm " value="重取驗證"
                           onclick="ImgCatcha()" />
                </div>
            </div>

            <div class="text-center">
                <input type="button" class="btn btn-default-Login btn-sm" value="登入" onclick='AuthIdentity()' />

                <a href='@Url.Action(SharedGlobal.HomeIndex, "Home")' class="btn btn-default-Login btn-sm">
                    回首頁
                </a>
            </div>
            @if (SharedGlobal.HomeIndex != "ChildMonthIndex")
            {
                <a href='#' class="btn btn-link" onclick="openForgetPasswordModal()">
                    忘記密碼?
                </a>
            }
        </div>

    }
</div>
<div style="height:50px"></div>
@Html.Partial("__ForgetPasswordModal")

<script type="text/javascript">

        $(function () {
            $("form input").keypress(function (e) {
                if ((e.which && e.which == 13) || (e.keyCode && e.keyCode == 13)) {
                    //$('button[type=submit] .default').click();
                    AuthIdentity();
                    return false;
                } else {
                    return true;
                }
            });
    });

        window.onload = function ()
        {
            if ($('#iLockCount').val() >= $('#iVlidatePasswordMaxCount').val())
            {
                $('.classInputCode').show()
            }
        }

        function ImgCatcha()
        {
            var Url = '@Url.Action("VerificationCode", "Home")' + '?r='+(new Date()).getTime()+''
            $('#captcha').attr('src', Url);
        }

       function AuthIdentity() {
            var strMsg = '';

            if ($("#txtUSER_NO").val() == "") {
                strMsg = '請輸入帳號';
            }
            if ($("#txtPASSWORD").val() == "") {
                strMsg = '請輸入密碼';
            }

            if ($("#txtSCHOOL_NO").val() == "") {
                strMsg = '請選擇學校';
            }

            if ($('.classInputCode').is(":visible") == true) {
                if ($("#txtInputCode").val() == "") {
                    strMsg = '請輸入驗証碼';
                }
            }

            if (strMsg != '') {
                alert(strMsg);
                return false;
           }
           var returnURL= "";

                $.ajax({
                    url: '@Url.Action("CheckUserStatus","Home")',
                    data: { SchoolNo: $("#txtSCHOOL_NO").val(), UserNo: $("#txtUSER_NO").val(), sPwd: $("#txtPASSWORD").val(), InputCode: $("#txtInputCode").val(), CheckCode: true},
                type: 'post',
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {

                    var res = jQuery.parseJSON(data);

                    if (res.ReValue != '') {
                        strMsg += res.ReValue + '\r\n';

                        if (res.IsInputCode == 1) {
                            $('.classInputCode').show()
                        }

                        alert(strMsg);
                        return false;
                    }
                    else {
                        document.loginform.submit();
                    }
                }
            });

        }
</script>