﻿using Dapper;
using ECOOL_APP.com.ecool.util;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class SECI06Service
    {
        public SECI06IndexViewModel GetHealthData(SECI06IndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (model == null) model = new SECI06IndexViewModel();

                model.MyHRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_NO == model.WhereUSER_NO).FirstOrDefault();

                if (model.MyHRMT01 != null)
                {
                    com.ecool.service.SECI03Service sECI03 = new com.ecool.service.SECI03Service();

                    model.MyAge = DateHelper.CalAgeByBirth(model.MyHRMT01.BIRTHDAY);

                    //角色娃娃
                    if (!string.IsNullOrWhiteSpace(model.MyHRMT01.PHOTO))
                    {
                        model.PlayerUrl = new SECI01Service().GetDirectorySysMyPhotoPath(model.MyHRMT01.SCHOOL_NO, model.MyHRMT01.USER_NO, model.MyHRMT01.PHOTO);
                    }
                    else
                    {
                        model.PlayerUrl = GetPlayerUrl(ref db, model.MyHRMT01.SCHOOL_NO, model.MyHRMT01.USER_NO, model.MyHRMT01.SEX, model.MyHRMT01.USER_TYPE);
                    }

                    model.Hrmt08List = sECI03.GetMyHrmt08Data(model.MyHRMT01.IDNO, model.MyHRMT01.SCHOOL_NO, ref db).ToPagedList(0, int.MaxValue);

                    model.NowHrmt08 = model.Hrmt08List.OrderByDescending(a => a.SYEAR + "-" + a.SEMESTER).FirstOrDefault();

                    if (model.NowHrmt08?.SEMESTER != null && model.NowHrmt08?.SYEAR != null)
                    {
                        //計算bmi 年記 ，周 說 上學期用 10/1 下學期用 4/1 來計算
                        DateTime CountDate;
                        if (model.NowHrmt08.SEMESTER == 1)
                        {
                            CountDate = Convert.ToDateTime((model.NowHrmt08.SYEAR + 1911).ToString() + "/10/01");
                        }
                        else
                        {
                            CountDate = Convert.ToDateTime((model.NowHrmt08.SYEAR + 1 + 1911).ToString() + "/04/01");
                        }

                        double? CountAge = DateHelper.CalAgeByBirth(CountDate, model.MyHRMT01.BIRTHDAY);

                        if (CountAge != null)
                        {
                            string BMISQL = @"select b.*
                                        from HRMT08_BMI a (nolock)
                                        inner join HRMT08_BMI_T b (nolock) on a.BMI_TYPE = b.BMI_TYPE
                                        where @CountAge >=a.YEARS-0.25 and @CountAge <a.YEARS+0.25
                                        and a.SEX=@SEX
                                        and @BMI BETWEEN a.BMI_L and a.BMI_U";
                            var tdHRMT08_BMI_T = db.Database.Connection.Query<HRMT08_BMI_T>(BMISQL
                            , new
                            {
                                CountAge = CountAge,
                                SEX = model.MyHRMT01.SEX,
                                BMI = model.NowHrmt08.ShowBMI
                            }).FirstOrDefault();

                            if (tdHRMT08_BMI_T != null)
                            {
                                model.COMMENT = tdHRMT08_BMI_T.COMMENT;
                                model.COMMENT_ACTION = tdHRMT08_BMI_T.COMMENT_ACTION;
                                model.BMI_TYPE_NAME = tdHRMT08_BMI_T.BMI_TYPE_NAME;

                                if (double.Parse(tdHRMT08_BMI_T.BMI_TYPE) != 1)
                                {
                                    string NormalSQL = @"select a.*
                                        from HRMT08_BMI a (nolock)
                                        where @CountAge >=a.YEARS-0.25 and @CountAge <a.YEARS+0.25
                                        and a.SEX=@SEX and a.BMI_TYPE='1' ";
                                    var tdHRMT08_BMI = db.Database.Connection.Query<HRMT08_BMI>(NormalSQL
                                    , new
                                    {
                                        CountAge = CountAge,
                                        SEX = model.MyHRMT01.SEX,
                                    }).FirstOrDefault();

                                    if (tdHRMT08_BMI != null)
                                    {
                                        //太瘦
                                        if ((double.Parse(tdHRMT08_BMI_T.BMI_TYPE) == -1))
                                        {
                                            //(身高/100) X (身高/100) X 標準bmi最小值 = 標準體重

                                            var AddWEIGHT = (((double)model.NowHrmt08.TALL / 100) * ((double)model.NowHrmt08.TALL / 100) * (double)tdHRMT08_BMI.BMI_L) - (double)model.NowHrmt08.WEIGHT;

                                            if (AddWEIGHT < 10)
                                            {
                                                model.SUGGEST_BMI_MEMO = $"你再增胖{AddWEIGHT.ToString("0.00")}公斤，BMI就是標準的喔,加油";
                                            }
                                            else
                                            {
                                                model.SUGGEST_BMI_MEMO = $"你再增胖一些，BMI就是標準的喔,加油";
                                            }
                                        }
                                        else //太胖
                                        {
                                            //round(Sqrt(目前體重/標準bmi最大值) x100 ,2) - 目前身高
                                            var AddTALL = Math.Round(Math.Sqrt((double)(model.NowHrmt08.WEIGHT ?? 0) / (double)tdHRMT08_BMI.BMI_U) * 100, 2) - (double)(model.NowHrmt08.TALL ?? 0);
                                            if (AddTALL < 10)
                                            {
                                                model.SUGGEST_BMI_MEMO = $"你再長高{AddTALL.ToString("0.00")}公分，BMI就是標準的喔,加油";
                                            }
                                            else
                                            {
                                                model.SUGGEST_BMI_MEMO = $"你再長高一些，BMI就是標準的喔,加油";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    model.Hrmt09List = sECI03.GetMyHrmt09Data(model.MyHRMT01.IDNO, ref db).ToPagedList(0, int.MaxValue);

                    model.NowHrmt09 = model.Hrmt09List.OrderByDescending(a => a.TDATE).FirstOrDefault();

                    if (model.NowHrmt09 != null)
                    {
                        var MyGradeHrmt09 = db.HRMT09.Where(a => a.SCHOOL_NO == model.MyHRMT01.SCHOOL_NO && a.SYEAR == model.NowHrmt09.SYEAR && a.GRADE == model.MyHRMT01.GRADE).ToList();

                        decimal? AVG_C_P_F_TEST = MyGradeHrmt09.Select(a => a.C_P_F_TEST).Average();
                        decimal? AVG_SIT_UPS_TEST = MyGradeHrmt09.Select(a => a.SIT_UPS_TEST).Average();
                        decimal? AVG_S_L_JUMP_TEST = MyGradeHrmt09.Select(a => a.S_L_JUMP_TEST).Average();
                        decimal? AVG_V_SET_REACH_TEST = MyGradeHrmt09.Select(a => a.V_SET_REACH_TEST).Average();
                        model.Hrmt09List = model.Hrmt09List.Where(x => x.IDNO == model.MyHRMT01.IDNO && x.SCHOOL_NO == model.MyHRMT01.SCHOOL_NO).ToPagedList(0, int.MaxValue);
                        model.NowHrmt09 = model.Hrmt09List.OrderByDescending(a => a.TDATE).FirstOrDefault();
                        if (model.NowHrmt09 != null)
                        {
                            model.NowHrmt09.AVG_C_P_F_TEST = AVG_C_P_F_TEST ?? 0;
                            model.NowHrmt09.AVG_SIT_UPS_TEST = AVG_SIT_UPS_TEST ?? 0;
                            model.NowHrmt09.AVG_S_L_JUMP_TEST = AVG_S_L_JUMP_TEST ?? 0;
                            model.NowHrmt09.AVG_V_SET_REACH_TEST = AVG_V_SET_REACH_TEST ?? 0;
                        }
                    }

                    // 跑步紀錄
                    string sSQL = $@"select isnull(Sum(Case When CONVERT(nvarchar(10),a.RUN_DATE,111)= CONVERT(nvarchar(10),getdate(),111) Then a.LAP_M Else 0 end),0) as The_day_M
                    ,isnull(Sum(Case When CONVERT(nvarchar(10),a.RUN_DATE,111)= CONVERT(nvarchar(10),DATEADD(DAY,-1, getdate()),111) Then a.LAP_M Else 0 end),0) as Yesterday_M
                    ,isnull(Sum(Case When CONVERT(nvarchar(10),a.RUN_DATE,111)= CONVERT(nvarchar(10),DATEADD(DAY,-2, getdate()),111) Then a.LAP_M Else 0 end),0) as The_day_before_yesterday_M
                    ,isnull(Sum(a.LAP_M),0) Total_M
                    from ADDT24 a (nolock)
                    Where a.SCHOOL_NO=@SCHOOL_NO
                    and a.USER_NO=@USER_NO
                    and a.STATUS='{ADDT24.STATUSVal.OK}'";
                    model.NowAddt24 = db.Database.Connection.Query<SECI06Addt24ViewModel>(sSQL
                    , new
                    {
                        SCHOOL_NO = model.WhereSCHOOL_NO,
                        USER_NO = model.WhereUSER_NO,
                    }).FirstOrDefault();

                    if (model.NowAddt24 == null)
                    {
                        model.NowAddt24 = new SECI06Addt24ViewModel();
                        model.NowAddt24.The_day_M = 0;
                        model.NowAddt24.Yesterday_M = 0;
                        model.NowAddt24.The_day_before_yesterday_M = 0;
                    }
                }

                return model;
            }
        }
        /// <summary>
        /// 角色娃娃路徑
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static string GetPlayerUrl(ref ECOOL_DEVEntities db, string SCHOOL_NO, string USER_NO, string SEX, string USER_TYPE)
        {
            if (USER_TYPE == UserType.Parents)
            {
                return "~/Content/img/web-parent.png";
            }
            //角色娃娃
            List<AWAT07> MyPlayerList =
                db.AWAT07.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).ToList();
            if (MyPlayerList.Any())
            {
                AWAT07 MyPlayer = MyPlayerList.Where(a => a.DEFAULT_YN == true).FirstOrDefault();
                if (MyPlayer == null) MyPlayer = MyPlayerList.First();

                string ImageUrl = @"~/Content/Players/";
                return ImageUrl + db.AWAT06.Find(MyPlayer.PLAYER_NO).IMG_FILE;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(SEX))
                {
                    HRMT01 tHRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

                    if (tHRMT01 != null)
                    {
                        SEX = tHRMT01.SEX;
                    }
                }

                if (SEX == "1") //男預設值
                {
                    return "~/Content/img/web-student-allpage-33plus.png";
                }
                else            //女預設值
                {
                    return "~/Content/img/web-student_allpage-17.png";
                }
            }
        }
    }
}