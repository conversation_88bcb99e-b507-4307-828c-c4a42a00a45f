// JavaScript for ADDI06 QUERY page - 查詢頁面
$(document).ready(function () {
    const queryModule = {
        targetFormID: "#form1",
        
        init: function() {
            this.setupGlobalFunctions();
            this.bindEvents();
            this.initializePage();
        },
        
        bindEvents: function () {
            // 綁定搜尋按鈕事件
            $('input[onclick*="doSearch"], button[onclick*="doSearch"]').on('click', this.handleSearch.bind(this));
            
            // 綁定清除按鈕事件
            $('input[onclick*="todoClear"], button[onclick*="todoClear"]').on('click', this.handleClear.bind(this));
            
            // 綁定分頁按鈕事件
            $('a[onclick*="FunPageProc"]').on('click', this.handlePagination.bind(this));
            
            // 綁定分頁大小變更事件
            $('select[onchange*="FunPageProc"]').on('change', this.handlePageSizeChange.bind(this));
            
            // 綁定排序按鈕事件
            $('th[onclick*="doSort"]').on('click', this.handleSort.bind(this));
            
            // 綁定列印按鈕事件
            $('input[onclick*="PrintBook"], button[onclick*="PrintBook"]').on('click', this.handlePrint.bind(this));
            
            // 綁定班級選擇事件（如果有的話）
            $('#Class_No').on('change', this.handleClassChange.bind(this));
            
            // 綁定表單提交事件
            $('form').on('submit', this.handleFormSubmit.bind(this));
        },
        
        setupGlobalFunctions: function () {
            // 設置全局函數以保持向後兼容
            window.doSearch = this.doSearch.bind(this);
            window.doSort = this.doSort.bind(this);
            window.FunPageProc = this.funPageProc.bind(this);
            window.todoClear = this.todoClear.bind(this);
            window.PrintBook = this.printBook.bind(this);
            window.ChangeClass_No = this.changeClass_No.bind(this);
        },
        
        doSearch: function () {
            try {
                console.log('QUERY 執行搜尋');
                
                const form = ADDI06Common.getForm();
                if (!form) {
                    ADDI06Common.showMessage('系統錯誤：找不到表單元素', 'error');
                    return false;
                }
                
                // 重置分頁到第一頁
                if (form.page) {
                    form.page.value = 1;
                }
                
                form.submit();
                return true;
            } catch (error) {
                console.error('QUERY 搜尋時發生錯誤:', error);
                ADDI06Common.showMessage('搜尋時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },
        
        doSort: function (sortCol) {
            try {
                console.log('QUERY 排序欄位:', sortCol);
                
                const form = ADDI06Common.getForm();
                if (!form) {
                    ADDI06Common.showMessage('系統錯誤：找不到表單元素', 'error');
                    return false;
                }
                
                // 設置排序欄位
                if (form.OrderByName) {
                    form.OrderByName.value = sortCol;
                }
                
                // 處理排序方向
                let orderRank = $("#OrderRank").val();
                if (orderRank === "") {
                    $("#OrderRank").val("desc");
                } else {
                    // 切換排序方向
                    switch (orderRank) {
                        case "desc":
                            $("#OrderRank").val("asc");
                            break;
                        case "asc":
                            $("#OrderRank").val("desc");
                            break;
                    }
                }
                
                // 重置分頁到第一頁
                if (form.page) {
                    form.page.value = 1;
                }
                
                form.submit();
                return true;
            } catch (error) {
                console.error('QUERY 排序時發生錯誤:', error);
                ADDI06Common.showMessage('排序時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },
        
        funPageProc: function (pageno) {
            try {
                const form = ADDI06Common.getForm();
                if (!form) {
                    ADDI06Common.showMessage('系統錯誤：找不到表單元素', 'error');
                    return false;
                }
                
                console.log('QUERY 切換到第', pageno, '頁');
                
                if (form.page) {
                    form.page.value = pageno;
                } else {
                    console.warn('找不到page欄位');
                }
                
                form.submit();
                return true;
            } catch (error) {
                console.error('QUERY 分頁處理時發生錯誤:', error);
                ADDI06Common.showMessage('分頁處理時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },
        
        todoClear: function () {
            try {
                // 使用共用的表單清除功能
                if (ADDI06Common.clearForm(this.targetFormID, `${this.targetFormID} :input`)) {
                    // 清除後重新搜尋
                    this.doSearch();
                }
                
                console.log('QUERY 表單已清除');
                return true;
            } catch (error) {
                console.error('QUERY 清除表單時發生錯誤:', error);
                ADDI06Common.showMessage('清除表單時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },
        
        printBook: function () {
            try {
                console.log('QUERY 列印報表');
                
                const form = ADDI06Common.getForm();
                if (!form) {
                    ADDI06Common.showMessage('系統錯誤：找不到表單元素', 'error');
                    return false;
                }
                
                const url = window.ADDI06_QUERY_URLS ? window.ADDI06_QUERY_URLS.printBook : null;
                if (!url) {
                    ADDI06Common.showMessage('找不到列印報表的URL配置', 'error');
                    return false;
                }
                
                // 設置表單action和target
                const originalAction = form.action;
                const originalTarget = form.target;
                
                $(this.targetFormID).attr('action', url).attr('target', '_blank');
                form.submit();
                
                // 恢復原始設定
                setTimeout(() => {
                    $(this.targetFormID).attr('action', originalAction || '');
                    if (originalTarget) {
                        $(this.targetFormID).attr('target', originalTarget);
                    } else {
                        $(this.targetFormID).removeAttr('target');
                    }
                }, 100);
                
                return true;
            } catch (error) {
                console.error('QUERY 列印報表時發生錯誤:', error);
                ADDI06Common.showMessage('列印報表時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },
        
        changeClass_No: function () {
            try {
                const selectedClass_No = $.trim($('#Class_No option:selected').val());
                
                // 使用共用的學生載入函數
                ADDI06Common.loadStudentsByClass(selectedClass_No, '#USER_NO', window.ADDI06_QUERY_URLS);
                
                console.log('QUERY 班級變更:', selectedClass_No);
            } catch (error) {
                console.error('QUERY 班級變更時發生錯誤:', error);
                ADDI06Common.showMessage('班級變更時發生錯誤，請稍後再試', 'error');
            }
        },
        
        initializePage: function() {
            try {
                console.log('QUERY 頁面已初始化');
                
                // 如果有班級選擇，初始化學生資料
                if ($('#Class_No').length > 0) {
                    const initialClass = $('#Class_No').val();
                    if (initialClass) {
                        this.changeClass_No();
                    }
                }
            } catch (error) {
                console.error('QUERY 初始化時發生錯誤:', error);
            }
        },
        
        // 事件處理器
        handleSearch: function(event) {
            event.preventDefault();
            this.doSearch();
        },
        
        handleClear: function(event) {
            event.preventDefault();
            this.todoClear();
        },
        
        handlePagination: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            if (onclick) {
                const match = onclick.match(/funPageProc\((\d+)\)/);
                if (match) {
                    this.funPageProc(parseInt(match[1]));
                }
            }
        },
        
        handlePageSizeChange: function(event) {
            this.funPageProc(1);
        },
        
        handleSort: function(event) {
            event.preventDefault();
            const onclick = $(event.target).closest('th').attr('onclick');
            if (onclick) {
                const match = onclick.match(/doSort\('([^']*)'\)/);
                if (match) {
                    this.doSort(match[1]);
                }
            }
        },
        
        handlePrint: function(event) {
            event.preventDefault();
            this.printBook();
        },
        
        handleClassChange: function(event) {
            this.changeClass_No();
        },
        
        handleFormSubmit: function (event) {
            try {
                console.log('QUERY 表單提交');
                return true;
            } catch (error) {
                console.error('QUERY 表單提交時發生錯誤:', error);
                event.preventDefault();
                ADDI06Common.showMessage('表單提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        }
    };

    // 初始化模組
    queryModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('QUERY 頁面錯誤:', e);
    });
});
