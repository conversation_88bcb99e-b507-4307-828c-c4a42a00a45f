﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameBuskerLikeViewModel
    {

        public string WhereTITLE_SHOW_ID { get; set; }

        /// <summary>
        /// 未報名訪客給讚用
        /// </summary>
        public bool UnApply { get; set; }


        public string GameUserID { get; set; }

        public string GAME_NO { get; set; }

        public string TITLE_SHOW_ID { get; set; }


        [DisplayName("表演名稱")]
        public string TITLE_SHOW_NAME { get; set; }

        public string TITLE_IMG { get; set; }

        public string QRCodeLikeImg { get; set; }

        public int NOW_LIKE_COUNT { get; set; }

        /// <summary>
        ///  顯示動畫的LIKE
        /// </summary>
        public int? Show_LIKE_COUNT { get; set; }

        /// <summary>
        /// 明細
        /// </summary>
        public virtual List<GameBuskerAddDetailsViewModel> Details { get; set; }

    }
}
