﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'fr', {
	access: 'Accès aux scripts',
	accessAlways: 'Toujours',
	accessNever: 'Jamais',
	accessSameDomain: 'Même domaine',
	alignAbsBottom: 'Bas absolu',
	alignAbsMiddle: 'Milieu absolu',
	alignBaseline: 'Ligne de base',
	alignTextTop: 'Haut du texte',
	bgcolor: 'Couleur d\'arrière-plan',
	chkFull: 'Permettre le plein écran',
	chkLoop: 'Boucle',
	chkMenu: 'Activer le menu Flash',
	chkPlay: 'Lire automatiquement',
	flashvars: 'Variables Flash',
	hSpace: 'Espacement horizontal',
	properties: 'Propriétés du Flash',
	propertiesTab: 'Propriétés',
	quality: 'Qualité',
	qualityAutoHigh: 'Haute automatique',
	qualityAutoLow: 'Basse automatique',
	qualityBest: 'Maximale',
	qualityHigh: 'Haute',
	qualityLow: 'Basse',
	qualityMedium: 'Moyenne',
	scale: 'Échelle',
	scaleAll: 'Afficher tout',
	scaleFit: 'Adaptation automatique',
	scaleNoBorder: 'Aucune bordure',
	title: 'Propriétés du Flash',
	vSpace: 'Espacement vertical',
	validateHSpace: 'L\'espacement horizontal doit être un nombre.',
	validateSrc: 'L\'URL doit être indiquée.',
	validateVSpace: 'L\'espacement vertical doit être un nombre.',
	windowMode: 'Mode fenêtre',
	windowModeOpaque: 'Opaque',
	windowModeTransparent: 'Transparent',
	windowModeWindow: 'Fenêtre'
} );
