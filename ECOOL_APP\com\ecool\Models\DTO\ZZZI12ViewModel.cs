﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI12ViewModel
    {

        public string SCHOOL_NO { get; set; }

        public string USER_NO { get; set; }

        public string ROLE_ID { get; set; }

        public string BRE_NO { get; set; }

        public string BRE_NAME { get; set; }

        public string BRE_TYPE { get; set; }

        public string LEVEL_NO { get; set; }

        public List<ZZZI12_D_ViewModel> Details_List { get; set; }

    }



    public class ZZZI12_D_ViewModel
    {

        public string SCHOOL_NO { get; set; }

        public string USER_NO { get; set; }

        public string ROLE_ID { get; set; }
  
        public string BRE_NO { get; set; }

        public string ACTION_ID { get; set; }
  
        public string ACTION_NAME { get; set; }

        public string ACTION_TYPE { get; set; }

        public bool Checked { get; set; }

        public bool ACTION_Checked { get; set; }

        public bool ROLE_Checked { get; set; }

        public bool USER_Checked { get; set; }

        public bool MY_Permission_Checked { get; set; }


    }

}
