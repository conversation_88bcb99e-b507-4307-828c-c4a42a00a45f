﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http.WebHost</name>
  </assembly>
  <members>
    <member name="T:System.Web.Http.GlobalConfiguration">
      <summary> Provides a global <see cref="T:System.Web.Http.HttpConfiguration" /> for ASP.NET applications. </summary>
    </member>
    <member name="P:System.Web.Http.GlobalConfiguration.Configuration"></member>
    <member name="M:System.Web.Http.GlobalConfiguration.Configure(System.Action{System.Web.Http.HttpConfiguration})"></member>
    <member name="P:System.Web.Http.GlobalConfiguration.DefaultHandler"></member>
    <member name="P:System.Web.Http.GlobalConfiguration.DefaultServer">
      <summary> Gets the global <see cref="T:System.Web.Http.HttpServer" />. </summary>
    </member>
    <member name="T:System.Web.Http.RouteCollectionExtensions">
      <summary> Extension methods for <see cref="T:System.Web.Routing.RouteCollection" /></summary>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String)">
      <summary>Maps the specified route template.</summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String,System.Object)">
      <summary>Maps the specified route template and sets default route.</summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
      <param name="defaults">An object that contains default route values.</param>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String,System.Object,System.Object)">
      <summary>Maps the specified route template and sets default route values and constraints.</summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
      <param name="defaults">An object that contains default route values.</param>
      <param name="constraints">A set of expressions that specify values for routeTemplate.</param>
    </member>
    <member name="M:System.Web.Http.RouteCollectionExtensions.MapHttpRoute(System.Web.Routing.RouteCollection,System.String,System.String,System.Object,System.Object,System.Net.Http.HttpMessageHandler)">
      <summary>Maps the specified route template and sets default route values, constraints,  and end-point message handler.</summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
      <param name="defaults">An object that contains default route values.</param>
      <param name="constraints">A set of expressions that specify values for routeTemplate.</param>
      <param name="handler">The handler to which the request will be dispatched.</param>
    </member>
    <member name="T:System.Web.Http.WebHost.HttpControllerHandler">
      <summary>A <see cref="T:System.Web.IHttpTaskAsyncHandler" /> that passes ASP.NET requests into the <see cref="T:System.Web.Http.HttpServer" /> pipeline and write the result back.</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerHandler.#ctor(System.Web.Routing.RouteData)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.WebHost.HttpControllerHandler" /> class.</summary>
      <param name="routeData">The route data.</param>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerHandler.#ctor(System.Web.Routing.RouteData,System.Net.Http.HttpMessageHandler)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.WebHost.HttpControllerHandler" /> class.</summary>
      <param name="routeData">The route data.</param>
      <param name="handler">The message handler to dispatch requests to.</param>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerHandler.ProcessRequestAsync(System.Web.HttpContext)">
      <summary>Provides code that handles an asynchronous task</summary>
      <returns>The asynchronous task.</returns>
      <param name="context">The HTTP context.</param>
    </member>
    <member name="T:System.Web.Http.WebHost.HttpControllerRouteHandler">
      <summary> A <see cref="T:System.Web.Routing.IRouteHandler" /> that returns instances of <see cref="T:System.Web.Http.WebHost.HttpControllerHandler" /> that can pass requests to a given <see cref="T:System.Web.Http.HttpServer" /> instance. </summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerRouteHandler.#ctor">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.WebHost.HttpControllerRouteHandler" /> class. </summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerRouteHandler.GetHttpHandler(System.Web.Routing.RequestContext)">
      <summary> Provides the object that processes the request. </summary>
      <returns> An object that processes the request. </returns>
      <param name="requestContext">An object that encapsulates information about the request.</param>
    </member>
    <member name="P:System.Web.Http.WebHost.HttpControllerRouteHandler.Instance">
      <summary> Gets the singleton <see cref="T:System.Web.Http.WebHost.HttpControllerRouteHandler" /> instance. </summary>
    </member>
    <member name="M:System.Web.Http.WebHost.HttpControllerRouteHandler.System#Web#Routing#IRouteHandler#GetHttpHandler(System.Web.Routing.RequestContext)">
      <summary> Provides the object that processes the request. </summary>
      <returns> An object that processes the request. </returns>
      <param name="requestContext">An object that encapsulates information about the request.</param>
    </member>
    <member name="T:System.Web.Http.WebHost.PreApplicationStartCode">
      <summary>Provides a registration point for the simple membership pre-application start code.</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.PreApplicationStartCode.Start">
      <summary>Registers the simple membership pre-application start code.</summary>
    </member>
    <member name="T:System.Web.Http.WebHost.WebHostBufferPolicySelector">
      <summary>Represents the web host buffer policy selector.</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.WebHostBufferPolicySelector.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.WebHost.WebHostBufferPolicySelector" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.WebHost.WebHostBufferPolicySelector.UseBufferedInputStream(System.Object)">
      <summary>Gets a value that indicates whether the host should buffer the entity body of the HTTP request.</summary>
      <returns>true if buffering should be used; otherwise a streamed request should be used.</returns>
      <param name="hostContext">The host context.</param>
    </member>
    <member name="M:System.Web.Http.WebHost.WebHostBufferPolicySelector.UseBufferedOutputStream(System.Net.Http.HttpResponseMessage)">
      <summary>Uses a buffered output stream for the web host.</summary>
      <returns>A buffered output stream.</returns>
      <param name="response">The response.</param>
    </member>
    <member name="T:System.Web.Http.WebHost.WebHostExceptionCatchBlocks">
      <summary>Provides the catch blocks used within this assembly.</summary>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerBufferContent">
      <summary>Gets the label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.WriteBufferedResponseContentAsync.</summary>
      <returns>The label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.WriteBufferedResponseContentAsync.</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerBufferError">
      <summary>Gets the label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.WriteErrorResponseContentAsync.</summary>
      <returns>The label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.WriteErrorResponseContentAsync.</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerComputeContentLength">
      <summary>Gets the label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.ComputeContentLength.</summary>
      <returns>The label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.ComputeContentLength.</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpControllerHandlerStreamContent">
      <summary>Gets the label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.WriteStreamedResponseContentAsync.</summary>
      <returns>The label for the catch block in System.Web.Http.WebHost.HttpControllerHandler.WriteStreamedResponseContentAsync.</returns>
    </member>
    <member name="P:System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpWebRoute">
      <summary>Gets the label for the catch block in System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpWebRoute.GetRouteData.</summary>
      <returns>The catch block in System.Web.Http.WebHost.WebHostExceptionCatchBlocks.HttpWebRoute.GetRouteData.</returns>
    </member>
  </members>
</doc>