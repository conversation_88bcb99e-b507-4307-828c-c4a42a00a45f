﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'hu', {
	alt: 'Alternatív szöveg',
	border: 'Keret',
	btnUpload: '<PERSON>üld<PERSON> a szerverre',
	button2Img: 'A kiválasztott képgombból sima képet szeretne csinálni?',
	hSpace: 'Vízsz. táv',
	img2Button: 'A kiválasztott képből képgombot szeretne csinálni?',
	infoTab: 'Alaptulajdonságok',
	linkTab: 'Hivat<PERSON>z<PERSON>',
	lockRatio: 'Ar<PERSON>y megtartása',
	menu: 'Kép tulajdons<PERSON>gai',
	resetSize: 'Eredeti méret',
	title: 'Kép tulajdons<PERSON>gai',
	titleButton: '<PERSON>épgomb tulajdon<PERSON>gai',
	upload: 'Felt<PERSON>lt<PERSON>',
	urlMissing: '<PERSON><PERSON><PERSON>zik a kép URL-je',
	vSpace: 'Függ. táv',
	validateBorder: 'A keret méretének egész számot kell beírni!',
	validateHSpace: 'Vízszintes távolságnak egész számot kell beírni!',
	validateVSpace: 'Függőleges távolságnak egész számot kell beírni!'
} );
