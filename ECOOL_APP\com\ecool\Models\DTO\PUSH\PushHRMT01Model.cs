﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
  public  class PushHRMT01Model
    {
        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///登入帳號
        /// </summary>
        [DisplayName("登入帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///帳號鍵值
        /// </summary>
        [DisplayName("帳號鍵值")]
        public string USER_KEY { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///校務系統轉入 無作用 ，看入學年度請看USER_NO
        /// </summary>
        [DisplayName("校務系統轉入 無作用 ，看入學年度請看USER_NO")]
        public byte? SYEAR { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///性別
        /// </summary>
        [DisplayName("性別")]
        public string SEX { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///生日
        /// </summary>
        [DisplayName("生日")]
        public DateTime? BIRTHDAY { get; set; }

        /// <summary>
        ///狀態 (參考HRMT01_ENUM.cs)
        /// </summary>
        [DisplayName("狀態 (參考HRMT01_ENUM.cs)")]
        public byte? USER_STATUS { get; set; }

        /// <summary>
        ///帳號類型  (參考HRMT01_ENUM.cs)
        /// </summary>
        [DisplayName("帳號類型  (參考HRMT01_ENUM.cs)")]
        public string USER_TYPE { get; set; }

        /// <summary>
        ///教師類別(無使用)
        /// </summary>
        [DisplayName("教師類別(無使用)")]
        public string TEACHER_TYPE { get; set; }

        /// <summary>
        ///電子郵件
        /// </summary>
        [DisplayName("電子郵件")]
        public string E_MAIL { get; set; }

        /// <summary>
        ///電話
        /// </summary>
        [DisplayName("電話")]
        public string TEL_NO { get; set; }

        /// <summary>
        ///手機
        /// </summary>
        [DisplayName("手機")]
        public string PHONE { get; set; }

        /// <summary>
        ///身份証
        /// </summary>
        [DisplayName("身份証")]
        public string IDNO { get; set; }

        /// <summary>
        ///是否第一次登入 0 是, 1 否
        /// </summary>
        [DisplayName("是否第一次登入 0 是, 1 否")]
        public byte? INIT_STATUS { get; set; }

        /// <summary>
        ///
        /// </summary>
        [DisplayName("")]
        public int GIVE_CASH_LIMIT { get; set; }

        /// <summary>
        ///個人照片
        /// </summary>
        [DisplayName("個人照片")]
        public string PHOTO { get; set; }

        /// <summary>
        ///
        /// </summary>
        [DisplayName("")]
        public string CARD_NO { get; set; }


        /// <summary>
        ///是否安裝APP
        /// </summary>
        [DisplayName("是否安裝APP")]
        public string INSTALL_APP_YN { get; set; }
    }
}