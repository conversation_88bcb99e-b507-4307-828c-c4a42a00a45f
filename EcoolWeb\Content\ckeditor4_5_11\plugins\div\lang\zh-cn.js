﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'div', 'zh-cn', {
	IdInputLabel: 'ID',
	advisoryTitleInputLabel: '标题',
	cssClassInputLabel: '样式类名称',
	edit: '编辑 DIV',
	inlineStyleInputLabel: '行内样式',
	langDirLTRLabel: '从左到右 (LTR)',
	langDirLabel: '语言方向',
	langDirRTLLabel: '从右到左 (RTL)',
	languageCodeInputLabel: '语言代码',
	remove: '移除 DIV',
	styleSelectLabel: '样式',
	title: '创建 DIV 容器',
	toolbar: '创建 DIV 容器'
} );
