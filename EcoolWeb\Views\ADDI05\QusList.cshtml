@model IEnumerable<ECOOL_APP.com.ecool.Models.entity.uADDT12>
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    int? NowQ_TYPE = 0;
}
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="form-group">
    <a class="btn btn-sm btn-sys" role="button" onclick="onGo('Index')">
        回有獎徵答首頁
    </a>
    <a class="btn btn-sm btn-sys" role="button" onclick="onGo('detail')">
        回詳細活動內容
    </a>
</div>


<img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive App_hide" alt="Responsive image" />
<div class="Div-EZ-ADDI05">
    <div class="Details">
        <div style="height:15px"></div>
        <div class="Caption_Div">
            活動名稱：@ViewBag.DIALOG_NAME
        </div>
        <div style="height:15px"></div>
        <div class="table-92Per" style="margin: 0px auto; ">
            @foreach (var item in Model)
            {

                <div class="p-context">
                    @if (NowQ_TYPE != item.Q_TYPE)
                    {
                        if (NowQ_TYPE != 0)
                        {
                            <div style="height:15px"></div>
                        }

                        NowQ_TYPE = item.Q_TYPE;
                        <div class="form-group">
                            <samp class="label_dt">@item.Q_TYPE_NAME</samp>
                        </div>
                    }
                    <div style="height:1px;background-color:#DDDDDD;width:99%"></div>
                    @{
                        string label_dt_S = "Q" + item.Q_NUM + ".";
                    }
                    <samp class="label_dt_S">@label_dt_S</samp>
                    @if (ViewBag.AnsShow == true)
                    {
                        <samp style="color:red">答案：@Html.DisplayFor(model => item.TRUE_ANS)</samp>
                    }
                    <br />
                    @Html.Raw(HttpUtility.HtmlDecode(item.Q_TEXT))
                    <div style="height:12px"></div>
                    @if (item.Q_TYPE == 2)
                    {
                        <div class="form-inline">
                            @if (string.IsNullOrWhiteSpace(item.Q_ANS1) == false)
                            {
                                <div class="form-group" style="padding-right:20px;width:100%">
                                    <strong>1.</strong>
                                    @Html.Raw(HttpUtility.HtmlDecode(item.Q_ANS1))
                                </div>
                            }
                            @if (string.IsNullOrWhiteSpace(item.Q_ANS2) == false)
                            {
                                <div class="form-group" style="padding-right:20px;width:100%">
                                    <strong>2.</strong>
                                    @Html.Raw(HttpUtility.HtmlDecode(item.Q_ANS2))
                                </div>
                            }
                            @if (string.IsNullOrWhiteSpace(item.Q_ANS3) == false)
                            {
                                <div class="form-group" style="padding-right:20px;width:100%">
                                    <strong>3.</strong>
                                    @Html.Raw(HttpUtility.HtmlDecode(item.Q_ANS3))
                                </div>
                            }
                            @if (string.IsNullOrWhiteSpace(item.Q_ANS4) == false)
                            {
                                <div class="form-group" style="padding-right:20px;width:100%">
                                    <strong>4.</strong>
                                    @Html.Raw(HttpUtility.HtmlDecode(item.Q_ANS4))
                                </div>
                            }
                        </div>
                    }


                </div>

            }
        </div>
    </div>
</div>

@using (Html.BeginForm("QusList", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.Hidden("SearchContents", (string)TempData["SearchContents"])
    @Html.Hidden("OrderByName", (string)TempData["OrderByName"])
    @Html.Hidden("SyntaxName", (string)TempData["SyntaxName"])
    @Html.Hidden("page", (int?)TempData["page"])
    @Html.Hidden("DIALOG_ID", (string)TempData["DIALOG_ID"])
}
@section Scripts {
    <script language="JavaScript">
    window.ADDI05_Quslist_URLS = {
        quslist: "quslist",
           index: '@Url.Action("Index", (string)ViewBag.BRE_NO)',
            detail: '@Url.Action("detail", (string)ViewBag.BRE_NO)',

             sysUrl: 'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'
    }
    </script>
    <script src="~/Scripts/ADDI05/common.js" nonce="cmlvaw"></script>
   <script src="~/Scripts/ADDI05/QusList.js" nonce="cmlvaw"></script>
}