﻿@model GameBuskerEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BuskerManager" });
    }
}

@using (Html.BeginForm("BuskerEdit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.Main.TITLE_SHOW_ID)

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    @Html.HiddenFor(m => m.WhereTITLE_SHOW_ID)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            街頭藝人管理編輯
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.TITLE_SHOW_NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.TITLE_SHOW_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Main.TITLE_SHOW_NAME) } })
                        @Html.ValidationMessageFor(model => model.Main.TITLE_SHOW_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Main.TITLE_IMG, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @if (Model.Main != null)
                        {
                            if (!string.IsNullOrWhiteSpace(Model.Main.TITLE_IMG))
                            {
                                <img src="@Model.Main.TITLE_IMG" class="img-responsive colorbox" alt="Responsive image" href="@Model.Main.TITLE_IMG" style="max-width:300px" />
                                <br />
                            }
                        }
                        <div>
                            @Html.TextBoxFor(m => m.UploadBuskerFile, new { @class = "form-control input-md", @type = "file" })
                            @Html.ValidationMessageFor(m => m.UploadBuskerFile, "", new { @class = "text-danger" })
                            <br />
                            <label class="text-info">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })

    <div id="DetailsView">
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>
        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center">
                隊伍人員
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <div class="css-table" style="width:92%;">
                        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            <div class="th" style="text-align:center">
                                刪除
                            </div>
                            <div class="th" style="text-align:center">
                                學校
                            </div>
                            <div class="th" style="text-align:center">
                                全名
                            </div>
                            <div class="th" style="text-align:center">
                                身份
                            </div>
                            <div class="th" style="text-align:center">
                                學號/帳號
                            </div>
                            <div class="th" style="text-align:center">
                                班級
                            </div>
                            <div class="th" style="text-align:center">
                                座號
                            </div>
                            <div class="th" style="text-align:center">
                                電話
                            </div>
                            <div class="th" style="text-align:center">
                                卡號
                            </div>
                        </div>
                        <div id="editorRows" class="tbody">
                            @if (Model.Details?.Count() > 0)
                            {
                                foreach (var Item in Model.Details)
                                {
                                    @Html.Partial("_BuskerEditDetails", Item)
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-footer">
                <div class="row">
                    <div class="col-md-12 col-xs-12 text-right">
                        <span class="input-group-btn">
                            <button class="btn btn-info btn-sm" type="button" onclick="onAddItem()">
                                增加人員
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="存檔" class="btn btn-default" onclick="Save()" />
                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>

    <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" id="myModal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">請選擇要加入隊伍的人員</h4>
                </div>
                <div class="modal-body" style="overflow-y:scroll;max-height: calc(40vh)">
                    <div id="BuskerOpenPersonView">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="ModalClose">Close</button>
                </div>
            </div>
        </div>
    </div>

    if (Model.Main?.CASH > 0)
    {
        <div class="text-center">
            <font color="red">*注意，當已給此隊伍點數時，要異動隊伍名單時，請先把給於點數設成0</font>
        </div>
    }

}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 col-lg-offset-3">
        <div class="use-absolute" id="ErrorDiv">
            <div class="use-absoluteDiv">
                <div class="alert alert-danger" role="alert">
                    <h1>
                        <i class="fa fa-exclamation-circle"></i>
                        <strong id="ErrorStr"></strong>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        $(".colorboxPHOTO").colorbox({ photo: true, maxWidth: "90%", maxHeight: "90%", opacity: 0.82 });
        $(".colorbox").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });

        function Save()
        {
            $(targetFormID).attr("action", "@Url.Action("BuskerEditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("BuskerManager", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

       //增加明細
        function onAddItem() {

             $.ajax({
                url: '@Url.Action("_BuskerOpenPersonView", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#BuskerOpenPersonView').html(data);
                }
            });

             if ($('#myModal').is(':visible') == false) {
                 $('#myModal').modal('show');
             }
        }

        function AllAddRow() {

            $('#ErrorStr').html('處理中')
            $('#ErrorDiv').show()
            $('#AllAddRowBtn').prop("disabled", true);

            $(".AddRowBtn").each(function () {
                $(this).click();
            })

            IsAddRowBtnHide()
        }

        function IsAddRowBtnHide() {

            setTimeout(function () {

                if ($(".AddRowBtn").length == 0) {
                    $('#ErrorDiv').hide()
                    $('#AllAddRowBtn').prop("disabled", false);
                }
                else {
                    IsAddRowBtnHide()
                }
            }, 2000);
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }

        function AddRow(TEMP_USER_ID) {

            var data = {
                "GAME_NO" : $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(),
                "TEMP_USER_ID" : TEMP_USER_ID
             };

            $.ajax({
                url: '@Url.Action("_BuskerOpenPersonAddData", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                    deleteRow('Tr' + TEMP_USER_ID)
                }
            });
        }
    </script>
}