﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'is', {
	access: 'Script Access', // MISSING
	accessAlways: 'Always', // MISSING
	accessNever: 'Never', // MISSING
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Abs neðst',
	alignAbsMiddle: 'Abs miðjuð',
	alignBaseline: 'Grunnlína',
	alignTextTop: 'E<PERSON>ri brún texta',
	bgcolor: 'Bakgrunnslitur',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'Endurtekning',
	chkMenu: 'Sýna Flash-valmynd',
	chkPlay: 'Sjálfvirk spilun',
	flashvars: 'Variables for Flash', // MISSING
	hSpace: '<PERSON><PERSON>ri bil',
	properties: 'Eigindi Flash',
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'Skali',
	scaleAll: 'Sýna allt',
	scaleFit: 'Fella skala að stærð',
	scaleNoBorder: 'Án ramma',
	title: 'Eigindi Flash',
	vSpace: 'Hægri bil',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'Sláðu inn veffang stiklunnar!',
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
