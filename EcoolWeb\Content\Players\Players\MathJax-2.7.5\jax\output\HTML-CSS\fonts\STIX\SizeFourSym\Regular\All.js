/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeFourSym/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeFourSym,{710:[796,-573,1886,0,1886],711:[796,-573,1886,0,1886],732:[771,-608,1886,0,1886],759:[-117,280,1886,0,1886],773:[820,-770,0,-2500,0],780:[796,-573,0,-2040,-154],816:[-117,280,0,-2040,-154],818:[-127,177,0,-2500,0],824:[731,228,0,-490,-169],8254:[820,-770,2500,0,2500],8400:[749,-584,0,-2193,-13],8401:[749,-584,0,-2193,-13],8406:[735,-482,0,-2193,-13],8407:[735,-482,0,-2193,-13],8428:[-123,288,0,-2193,-13],8429:[-123,288,0,-2193,-13],8430:[-26,279,0,-2193,-13],8431:[-26,279,0,-2193,-13],8731:[1795,345,1184,112,895],8732:[1790,345,1184,112,895],9140:[766,-544,2692,84,2608],9141:[139,83,2692,84,2608],9180:[76,168,2328,0,2328],9181:[817,-573,2328,0,2328],9184:[66,212,2738,0,2738],9185:[842,-564,2738,0,2738],10098:[2566,509,1031,320,959],10099:[2566,509,1031,72,711],10214:[2566,509,778,295,708],10215:[2566,509,778,70,483],10218:[2566,509,1273,126,1133],10219:[2566,509,1273,140,1147],10627:[2566,509,1225,182,1052],10628:[2566,509,1225,173,1043],10629:[2566,509,1175,195,1050],10630:[2566,509,1175,194,1049]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeFourSym/Regular/All.js");
