﻿using com.ecool.sqlConnection;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.Models.dao
{
    public class Awat2SPDAO : AbstractSPDAO
    {
        private string sp_name = string.Empty;
        private SqlConnection conn = null;

        //public string AWATU01(string ParamAWARD_NAME, string ParamCOST_CASH, string ParamQTY_STORAGE, string ParamSDATETIME, string ParamEDATETIME, string ParamDESCRIPTION, string ParamIMG_FILE, string ParamAWARD_NO, string ParamAWARD_TYPE, string ParamAWARD_STATUS, string ParamHOT_YN)
        //{
        //    sp_name = "AWAS05";
        //    sqlConnection getConn = new sqlConnection();
        //    conn = getConn.getConnection4Query();
        //    SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn);
        //    cmd.CommandType = System.Data.CommandType.StoredProcedure;
        //    cmd.Parameters.Add("AWARD_NO", System.Data.SqlDbType.VarChar, 50).Value = ParamAWARD_NO;
        //    cmd.Parameters.Add("AWARD_NAME", System.Data.SqlDbType.NVarChar, 50).Value = ParamAWARD_NAME;
        //    cmd.Parameters.Add("COST_CASH", System.Data.SqlDbType.Int).Value = ParamCOST_CASH;
        //    cmd.Parameters.Add("QTY_STORAGE", System.Data.SqlDbType.Int).Value = ParamQTY_STORAGE;
        //    cmd.Parameters.Add("SDATETIME", System.Data.SqlDbType.VarChar, 20).Value = ParamSDATETIME;
        //    cmd.Parameters.Add("EDATETIME", System.Data.SqlDbType.VarChar, 20).Value = ParamEDATETIME;
        //    cmd.Parameters.Add("DESCRIPTION", System.Data.SqlDbType.NVarChar, 200).Value = ParamDESCRIPTION;
        //    cmd.Parameters.Add("IMG_FILE", System.Data.SqlDbType.NVarChar, 100).Value = ParamIMG_FILE;
        //    cmd.Parameters.Add("AWARD_TYPE", System.Data.SqlDbType.Char, 1).Value = ParamAWARD_TYPE;
        //    cmd.Parameters.Add("AWARD_STATUS", System.Data.SqlDbType.Char, 1).Value = ParamAWARD_STATUS;
        //    cmd.Parameters.Add("HOT_YN", System.Data.SqlDbType.Char, 1).Value = ParamHOT_YN;


        //    SqlParameter rc = new SqlParameter("DBMSG", SqlDbType.VarChar, 250);
        //    rc.Direction = System.Data.ParameterDirection.Output;
        //    cmd.Parameters.Add(rc);
        //    try
        //    {
        //        cmd.ExecuteNonQuery();
        //        handleException(sp_name, (string)rc.Value);
        //        return (string)rc.Value;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex.GetBaseException();
        //    }
        //    finally
        //    {
        //        getConn.closeConnection4Query(conn);
        //    }
        //}

        public string AWATD02(string ParamAWARD_NO)
        {
            string RtMsg = string.Empty;
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                int iAWARD_NO = Convert.ToInt32(ParamAWARD_NO);
                //取得商品資訊
                AWAT09 awat09 = db.AWAT09.Where(p => p.AWARD_NO == iAWARD_NO).FirstOrDefault();
                //新增至歷史資料
                AWAT09_HIS awat09_His = new AWAT09_HIS();
                awat09_His.AWARD_NO = awat09.AWARD_NO;
                awat09_His.SCHOOL_NO = awat09.SCHOOL_NO;
                awat09_His.AWARD_TYPE = awat09.AWARD_TYPE;
                awat09_His.AWARD_NAME = awat09.AWARD_NAME;
                awat09_His.COST_CASH = awat09.COST_CASH;
                awat09_His.QTY_STORAGE = awat09.QTY_STORAGE;
                awat09_His.SDATETIME = awat09.SDATETIME;
                awat09_His.EDATETIME = awat09.EDATETIME;
                awat09_His.DESCRIPTION = awat09.DESCRIPTION;
                awat09_His.IMG_FILE = awat09.IMG_FILE;
                awat09_His.IMG2_FILE = awat09.IMG2_FILE;
                awat09_His.AWARD_STATUS = awat09.AWARD_STATUS;
                awat09_His.HOT_YN = awat09.HOT_YN;
                awat09_His.BUY_PERSON_YN = awat09.BUY_PERSON_YN;
                awat09_His.CHG_DATE = DateTime.Now;
                db.AWAT09_HIS.Add(awat09_His);

                awat09.AWARD_STATUS = "n";
                db.Entry(awat09).State = System.Data.Entity.EntityState.Modified;
                try
                {
                    db.SaveChanges();
                    RtMsg = "商品刪除成功";
                }
                catch (Exception)
                {
                    RtMsg = "商品刪除失敗";
                    throw;
                }
            }
            return RtMsg;
        }

        public string AWATE02(int ParamAWARD_NO, string ParamSCHOOL_NO, string ParamUSERID)
        {
            sp_name = "AWAS07";
            sqlConnection getConn = new sqlConnection();
            conn = getConn.getConnection4Query();
            SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("AWARD_NO", System.Data.SqlDbType.Int, 50).Value = ParamAWARD_NO;
            cmd.Parameters.Add("SCHOOL_NO", System.Data.SqlDbType.VarChar, 50).Value = ParamSCHOOL_NO;
            cmd.Parameters.Add("USERID", System.Data.SqlDbType.VarChar, 50).Value = ParamUSERID;

            SqlParameter rc = new SqlParameter("DBMSG", SqlDbType.VarChar, 250);
            rc.Direction = System.Data.ParameterDirection.Output;
            cmd.Parameters.Add(rc);
            try
            {
                cmd.ExecuteNonQuery();
                //  handleException(sp_name, (string)rc.Value);

              

                return (string)rc.Value;
            }
            catch (Exception ex)
            {
                throw ex.GetBaseException();
            }
            finally
            {
                getConn.closeConnection4Query(conn);
            }

        }

        //public string AWATE03(int ParamAWARD_NO, string ParamSCHOOL_NO, string ParamUSERID)
        //{
        //    sp_name = "AWAS08";
        //    sqlConnection getConn = new sqlConnection();
        //    conn = getConn.getConnection4Query();
        //    SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn);
        //    cmd.CommandType = System.Data.CommandType.StoredProcedure;
        //    cmd.Parameters.Add("AWARD_NO", System.Data.SqlDbType.Int, 50).Value = ParamAWARD_NO;
        //    cmd.Parameters.Add("SCHOOL_NO", System.Data.SqlDbType.VarChar, 50).Value = ParamSCHOOL_NO;
        //    cmd.Parameters.Add("USERID", System.Data.SqlDbType.VarChar, 50).Value = ParamUSERID;

        //    SqlParameter rc = new SqlParameter("DBMSG", SqlDbType.VarChar, 50);
        //    rc.Direction = System.Data.ParameterDirection.Output;
        //    cmd.Parameters.Add(rc);
        //    try
        //    {
        //        cmd.ExecuteNonQuery();
        //        handleException(sp_name, (string)rc.Value);
        //        return (string)rc.Value;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex.GetBaseException();
        //    }
        //    finally
        //    {
        //        getConn.closeConnection4Query(conn);
        //    }

        //}
    }
}
