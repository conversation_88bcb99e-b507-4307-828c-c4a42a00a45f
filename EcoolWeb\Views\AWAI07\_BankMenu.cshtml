﻿

@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}


<div class="form-group">
    <a role="button" href='@Url.Action("Index", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "Index" ? "active" : "")">
        利率表
    </a>
    <a role="button" href='@Url.Action("Teaching", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "Teaching" ? "active" : "")">
        說明影片
    </a>
    <a role="button" href='@Url.Action("Edit", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "Edit" ? "active" : "")">
        我要定存
    </a>
    <a role="button" href='@Url.Action("Simulator", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "Simulator" ? "active" : "")">
        試算模擬器
    </a>
    <a role="button" href='@Url.Action("MyList", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "MyList" ? "active" : "")">
        我的資產
    </a>


    @if ((bool)ViewBag.BankListPermision)
    {
        <a role="button" href='@Url.Action("BankListView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="BankListView" ? "active":"")">
            學生酷幣定存一覽表
        </a>
    }

</div>