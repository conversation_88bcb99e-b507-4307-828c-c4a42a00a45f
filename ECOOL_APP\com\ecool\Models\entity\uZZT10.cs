﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uZZT10
    {

        ///Summary
        ///ROLE_ID
        ///Summary
        [Display(Name = "ROLE_ID")]
        [Required]
        [StringLength(20)]
        public string ROLE_ID { get; set; }

        ///Summary
        ///BRE_NO
        ///Summary
        [Display(Name = "BRE_NO")]
        [Required]
        [StringLength(100)]
        public string BRE_NO { get; set; }

        ///Summary
        ///ACTION_ID
        ///Summary
        [Display(Name = "ACTION_ID")]
        [Required]
        [StringLength(100)]
        public string ACTION_ID { get; set; }

        ///Summary
        ///CRE_PERSON
        ///Summary
        [Display(Name = "CRE_PERSON")]
        [StringLength(40)]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
        [Display(Name = "CRE_DATE")]
        public DateTime CRE_DATE { get; set; }

    }
}
