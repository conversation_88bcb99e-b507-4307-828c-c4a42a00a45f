﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'gl', {
	button: {
		title: 'Propiedades do botón',
		text: 'Texto (Valor)',
		type: 'Tipo',
		typeBtn: 'Botón',
		typeSbm: 'Enviar',
		typeRst: 'Restabelever'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Propiedades da caixa de selección',
		radioTitle: 'Propiedades do botón de opción',
		value: 'Valor',
		selected: 'Sele<PERSON>ona<PERSON>',
		required: 'Requirido'
	},
	form: {
		title: 'Propiedades do formulario',
		menu: 'Propiedades do formulario',
		action: 'Acción',
		method: 'Método',
		encoding: 'Codificación'
	},
	hidden: {
		title: 'Propiedades do campo agochado',
		name: 'Nome',
		value: '<PERSON>or'
	},
	select: {
		title: 'Propiedades do campo de selección',
		selectInfo: 'Información',
		opAvail: 'Opcións dispoñíbeis',
		value: 'Valor',
		size: 'Tamaño',
		lines: 'liñas',
		chkMulti: 'Permitir múltiplas seleccións',
		required: 'Requirido',
		opText: 'Texto',
		opValue: 'Valor',
		btnAdd: 'Engadir',
		btnModify: 'Modificar',
		btnUp: 'Subir',
		btnDown: 'Baixar',
		btnSetValue: 'Estabelecer como valor seleccionado',
		btnDelete: 'Eliminar'
	},
	textarea: {
		title: 'Propiedades da área de texto',
		cols: 'Columnas',
		rows: 'Filas'
	},
	textfield: {
		title: 'Propiedades do campo de texto',
		name: 'Nome',
		value: 'Valor',
		charWidth: 'Largo do carácter',
		maxChars: 'Núm. máximo de caracteres',
		required: 'Requirido',
		type: 'Tipo',
		typeText: 'Texto',
		typePass: 'Contrasinal',
		typeEmail: 'Correo',
		typeSearch: 'Buscar',
		typeTel: 'Número de teléfono',
		typeUrl: 'URL'
	}
} );
