﻿using com.ecool.service;
using ECOOL_APP;
using EcoolWeb;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace System.Web.Mvc.Html
{
    public static class HtmlHelperExtensions 
    {


        public static MvcHtmlString BarTitle(this HtmlHelper htmlHelper,string BRE_NO = null, string TitleName=null)
        {

            if (string.IsNullOrWhiteSpace(TitleName))
            {
                if (string.IsNullOrWhiteSpace(BRE_NO))
                {
                    object Controller = HttpContext.Current.Request.RequestContext.RouteData.Values["Controller"];
                    BRE_NO = Controller.ToString();
                }

                TitleName = BreadcrumbService.GetBRE_NAME(BRE_NO);
            }

            return MvcHtmlString.Create(TitleName.ToString());

        }



        /// <summary>
        /// button套按鈕權限
        /// </summary>
        /// <param name="htmlHelper"></param>
        /// <param name="Text"></param>
        /// <param name="type"></param>
        /// <param name="CheckBRE_NO"></param>
        /// <param name="CheckACTION_ID"></param>
        /// <param name="htmlAttributess"></param>
        /// <param name="ShowType">1.不顯示 2.disabled </param>
        /// <param name="PermissionData"></param>
        /// <returns></returns>
        public static MvcHtmlString PermissionButton
            (this HtmlHelper htmlHelper
            ,string Text
            ,string type = "button "
            ,string CheckBRE_NO = null
            ,string CheckACTION_ID = null
            ,object htmlAttributess=null
            , byte ShowType = 1
            , List<ControllerPermissionfile> PermissionData = null)  //使用 CheckPermission IsGetAllActionPermission=true 取得ViewBag.BtnPermission 
        {
            TagBuilder builder;
            builder = new TagBuilder("button");
            builder.Attributes.Add("type", type);
            builder.InnerHtml = Text;

            if (htmlAttributess!=null)
            {
                var attrovites = HtmlHelper.AnonymousObjectToHtmlAttributes(htmlAttributess);
                builder.MergeAttributes(attrovites);
            }

            bool isUse= PermissionCheck(CheckBRE_NO, CheckACTION_ID, PermissionData); 

            if (isUse==false)
            {
                if (ShowType == 1)
                {
                    return MvcHtmlString.Empty;
                }
                else
                {
                    builder.Attributes.Add("disabled", "disabled");
                    builder.AddCssClass("active");
                    //view 需要加 $('[data-toggle="tooltip"]').tooltip()
                    TagBuilder TabDiv = new TagBuilder("div");
                    TabDiv.Attributes.Add("title", "您無此按鈕權限");
                    TabDiv.Attributes.Add("data-toggle", "tooltip");
                    TabDiv.Attributes.Add("data-placement", "left");
                    TabDiv.InnerHtml = builder.ToString();
                    return MvcHtmlString.Create(TabDiv.ToString());
                }
            }
           
            return MvcHtmlString.Create(builder.ToString());
        }





        /// <summary>
        /// 超連結套權限按鈕
        /// </summary>
        /// <param name="htmlHelper"></param>
        /// <param name="linkText"></param>
        /// <param name="actionName"></param>
        /// <param name="controllerName"></param>
        /// <param name="routeValues"></param>
        /// <param name="htmlAttributes"></param>
        /// <param name="CheckBRE_NO"></param>
        /// <param name="CheckACTION_ID"></param>
        /// <param name="ShowType"></param>
        /// <param name="PermissionData"></param>
        /// <returns></returns>
        public static MvcHtmlString PermissionActionLink(
            this HtmlHelper htmlHelper
            , string linkText
            , string actionName
            , string controllerName
            , object routeValues 
            , object htmlAttributes 
            , string CheckBRE_NO = null
            , string CheckACTION_ID = null
            , byte ShowType = 1
            , List<ControllerPermissionfile> PermissionData = null
            )
        {

            bool isUse = PermissionCheck(CheckBRE_NO, CheckACTION_ID, PermissionData);

            if (isUse == false)
            {
                if (ShowType == 1)
                {
                    return MvcHtmlString.Empty;
                }
                else
                {
                }
            }
       

            var link = htmlHelper.ActionLink(linkText, actionName, controllerName, routeValues, htmlAttributes);


            return link;
        }

        /// <summary>
        /// 圖片超連結套權限按鈕
        /// </summary>
        /// <param name="helper"></param>
        /// <param name="imageUrl"></param>
        /// <param name="actionName"></param>
        /// <param name="controllerName"></param>
        /// <param name="routeValues"></param>
        /// <param name="htmlAttributes"></param>
        /// <param name="CheckBRE_NO"></param>
        /// <param name="CheckACTION_ID"></param>
        /// <param name="ShowType"></param>
        /// <param name="PermissionData"></param>
        /// <returns></returns>
        public static string PermissionImageActionLink(this HtmlHelper helper, string imageUrl
            , string actionName
            , string controllerName
            , object routeValues
            , object htmlAttributes
            , string CheckBRE_NO = null
            , string CheckACTION_ID = null
            , byte ShowType = 1
            , List<ControllerPermissionfile> PermissionData = null
            )

        {

            var builder = new TagBuilder("img");

            builder.MergeAttribute("src", imageUrl);

            var link = helper.ActionLink("[replaceme]", actionName, controllerName, routeValues, htmlAttributes);

            bool isUse = PermissionCheck(CheckBRE_NO, CheckACTION_ID, PermissionData);

            if (isUse == false)
            {
                if (ShowType == 1)
                {
                    return string.Empty;
                }
                else
                {
                }
            }


            return link.ToHtmlString().Replace("[replaceme]", builder.ToString(TagRenderMode.SelfClosing));

        }


        /// <summary>
        /// 權限判斷
        /// </summary>
        /// <param name="CheckBRE_NO"></param>
        /// <param name="CheckACTION_ID"></param>
        /// <param name="PermissionData"></param>
        /// <returns></returns>
        public static bool  PermissionCheck(string CheckBRE_NO, string CheckACTION_ID, List<ControllerPermissionfile> PermissionData=null)
        {
            bool returnBool;

            try
            {
                object Controller = HttpContext.Current.Request.RequestContext.RouteData.Values["Controller"];
                object action = HttpContext.Current.Request.RequestContext.RouteData.Values["action"];

                string BRE_NO = CheckBRE_NO != null ? CheckBRE_NO : Controller.ToString();
                string ACTION_ID = CheckACTION_ID != null ? CheckACTION_ID : action.ToString();


                if (PermissionData != null)
                {
                    returnBool = PermissionData.Where(a => a.BreNoName == BRE_NO && a.ActionName == ACTION_ID && a.BoolUse == true).Any();
                }
                else
                {
                    string SCHOOL_NO = string.Empty;
                    string USER_NO = string.Empty;

                    UserProfile user = (UserProfile)HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY];
                    SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();

                    if (user != null)
                    {
                        USER_NO = user.USER_NO;
                    }

                    string UseYN;
                    UseYN = PermissionService.GetPermission_Use_YN(BRE_NO, ACTION_ID, SCHOOL_NO, USER_NO);

                    if (UseYN == "Y")
                    {
                        returnBool = true;
                    }
                    else
                    {
                        returnBool = false;
                    }
                }
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }

            return returnBool;

        }


    }
}