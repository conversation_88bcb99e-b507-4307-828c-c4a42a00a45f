﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'gu', {
	access: 'સ્ક્રીપ્ટ એક્સેસ',
	accessAlways: 'હમેશાં',
	accessNever: 'નહી',
	accessSameDomain: 'એજ ડોમેન',
	alignAbsBottom: 'Abs નીચે',
	alignAbsMiddle: 'Abs ઉપર',
	alignBaseline: 'આધાર લીટી',
	alignTextTop: 'ટેક્સ્ટ ઉપર',
	bgcolor: 'બૅકગ્રાઉન્ડ રંગ,',
	chkFull: 'ફૂલ સ્ક્રીન કરવું',
	chkLoop: 'લૂપ',
	chkMenu: 'ફ્લૅશ મેન્યૂ નો પ્રયોગ કરો',
	chkPlay: 'ઑટો/સ્વયં પ્લે',
	flashvars: 'ફલેશ ના વિકલ્પો',
	hSpace: 'સમસ્તરીય જગ્યા',
	properties: 'ફ્લૅશના ગુણ',
	propertiesTab: 'ગુણ',
	quality: 'ગુણધર્મ',
	qualityAutoHigh: 'ઓટો ઊંચું',
	qualityAutoLow: 'ઓટો નીચું',
	qualityBest: 'શ્રેષ્ઠ',
	qualityHigh: 'ઊંચું',
	qualityLow: 'નીચું',
	qualityMedium: 'મધ્યમ',
	scale: 'સ્કેલ',
	scaleAll: 'સ્કેલ ઓલ/બધુ બતાવો',
	scaleFit: 'સ્કેલ એકદમ ફીટ',
	scaleNoBorder: 'સ્કેલ બોર્ડર વગર',
	title: 'ફ્લૅશ ગુણ',
	vSpace: 'લંબરૂપ જગ્યા',
	validateHSpace: 'HSpace આંકડો હોવો જોઈએ.',
	validateSrc: 'લિંક  URL ટાઇપ કરો',
	validateVSpace: 'VSpace આંકડો હોવો જોઈએ.',
	windowMode: 'વિન્ડો મોડ',
	windowModeOpaque: 'અપારદર્શક',
	windowModeTransparent: 'પારદર્શક',
	windowModeWindow: 'વિન્ડો'
} );
