﻿@model global::ECOOL_APP.com.ecool.Models.DTO.ZZZI09_HISViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@{ Html.RenderAction("_RunMenu", "ADDI11", new { NowAction = "ExportRunExcel" }); }
<br />


@Html.Partial("_Notice")
<img src="~/Content/img/web-bar2-revise-35.png" style="width:100%" class="img-responsive " alt="Responsive image" />

@using (Html.BeginForm("ExportRunExcel", "ZZZI09_HIS", FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off" }))
{
    @Html.Hidden("IDNO", "")
    <div class="Div-EZ-ZZZI09" id='_SelectDiv'>
        <div class="form-horizontal">
            <label class="control-label"> * @ViewBag.Panel_Title</label>
            <br /><br />

            <div class="form-group">
                @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SYEAR" })
                <div class="col-md-9">
                    @if (ViewBag.CanChangeSchool)
                    {
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();" })
                    }
                    else
                    {
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();", disabled = "disabled" })
                    }
                    @Html.ValidationMessageFor(m => m.Where_SCHOOLNO_FORADMIN, "", new { @class = "text-danger" })
                </div>
            </div>
            @if (ViewBag.showSYear == "Y")
            {
                <div class="form-group">
                    @Html.Label("學年", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SYEAR" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control", onchange = "$('#Where_CLASSNO').val('');this.form.submit();" })
                        @Html.ValidationMessageFor(m => m.Where_SYEAR, "", new { @class = "text-danger" })
                    </div>
                </div>


            }
            @*else
                {
                    <div class="form-group">
                        @Html.Label("是否匯出閱讀認證", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                        <div class="col-md-9">
                            否<input type="checkbox" id="ReadYN" name="ReadYN" />
                        </div>

                    </div>
                }*@
            <div class="form-group">
                @Html.Label("班級", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_CLASSNO" })
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.Where_CLASSNO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();" })
                    @Html.ValidationMessageFor(m => m.Where_CLASSNO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-offset-3 col-md-9 text-center">
                    <input type="button" value="匯出名單" class="btn btn-default" onclick="CK()" />
                    @*<button type="button" class="btn btn-default btn-block" onclick="CK()">匯出</button>*@
                </div>
            </div>
        </div>
    </div>

}

@section scripts{
    <script language="JavaScript">
        var targetFormID = '#form1';
        function CK() {
            $(targetFormID).attr("action", "@Url.Action("Export", "ZZZI09_HIS")")
            $(targetFormID).submit();

        }
    </script>
}