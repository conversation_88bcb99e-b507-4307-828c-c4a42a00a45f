﻿@model GameLotteryViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "Lottery" });
    }
}

@using (Html.BeginForm("Lottery", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_LotteryPartial", (string)ViewBag.BRE_NO)
    </div>

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <button class="btn btn-default" type="button" onclick="onBack()">回上一頁</button>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1'

         function onBack() {
            $(targetFormID).attr("action", "@Url.Action("GameIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        //分頁
         function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };

        //排序
        function FunSort(SortName) {

            OrderByName = $('#@Html.IdFor(m=>m.Search.OrdercColumn)').val();
            SyntaxName =  $('#@Html.IdFor(m=>m.Search.SyntaxName)').val();

            if (OrderByName == SortName) {

                if (SyntaxName == "Desc") {
                   $('#@Html.IdFor(m=>m.Search.SyntaxName)').val("ASC");
                }
                else {
                   $('#@Html.IdFor(m=>m.Search.SyntaxName)').val("Desc");
                }
            }
            else {
                $('#@Html.IdFor(m=>m.Search.OrdercColumn)').val(SortName);
                $('#@Html.IdFor(m=>m.Search.SyntaxName)').val("Desc");
            }
            FunPageProc(0)
        }

         //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_LotteryPartial", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function OnAddLottery()
        {
            $('#@Html.IdFor(m=>m.Search.WhereLOTTERY_NO)').val('')
            $(targetFormID).attr("action", "@Url.Action("LotteryCreView", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function OnLotteryDetails(id)
        {
            $('#@Html.IdFor(m=>m.Search.WhereLOTTERY_NO)').val(id)
            $(targetFormID).attr("action", "@Url.Action("LotteryDetails", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function OnDel(id)
        {
            var OK = confirm("您確定要刪除這筆抽獎資料，按確定後，抽獎資料、中獎名單，將全部被刪除!!，刪除後無法還原!!!!")

            if (OK) {
                $('#@Html.IdFor(m=>m.Search.WhereLOTTERY_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("LotteryDel", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }

        function OnEditLotteryDetails(id)
        {
            $('#@Html.IdFor(m=>m.Search.WhereLOTTERY_NO)').val(id)
            $(targetFormID).attr("action", "@Url.Action("LotteryCreView", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function OnChiefLotteryDetails(id)
        {
            $('#@Html.IdFor(m=>m.Search.WhereLOTTERY_NO)').val(id)
            $(targetFormID).attr("action", "@Url.Action("ChiefLottery", (string)ViewBag.BRE_NO, new { LotteryType = (byte)ADDT32.LotteryTypeVal.進入開始抽獎畫面 })")
            $(targetFormID).submit();
        }
    </script>
}