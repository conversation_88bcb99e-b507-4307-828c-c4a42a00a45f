﻿

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@if (ViewBag.Title != null)
{
    <div class="Title_Secondary">酷幣查詢</div>
    <br />
}

@Html.Partial("_Notice")

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <p>
    </p>
    <p>
    </p>
    <div class="Div-EZ-ADDI09" style="background-color:#ffee92">
        <div class="form-horizontal" style="height:200px;">
            <label class="control-label">新ATM酷幣查詢</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-查詢", "Index4", new { WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁", "Index4", new { TimeoutSeconds = "6" , WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-查詢(沒有借書)", "Index4", new { NoBook = "Y", WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁(沒有借書)", "Index4", new { TimeoutSeconds = "6", NoBook = "Y" , WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>

                @if (ViewBag.Gift != null)
                {
                    <div class="col-md-6">
                        @Html.ActionLink("酷幣ATM-直換禮物", "Index4", new { TimeoutSeconds = "6", Mode = "gift" , WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                    </div>
                }
            </div>
            <div class="form-group">
                <label class="text-danger">
                </label>
            </div>
        </div>
    </div>
    <br /> <br /> <br />
    <div class="Div-EZ-ADDI09" style="background-color:#95b9e4">
        <div class="form-horizontal" style="height:150px;">
            <label class="control-label" style="color:aliceblue">簡易ATM酷幣查詢</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-查詢", "Index5", new { WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁", "Index5", new { TimeoutSeconds = "6", WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
            </div>
            <div class="form-group">
                <label class="text-danger">
                </label>
            </div>
        </div>
    </div><br /> <br /> <br />
    <div class="Div-EZ-ADDI09" style="background-color:#ffee92">
        <div class="form-horizontal" style="height:200px;">
            <label class="control-label">舊ATM酷幣查詢</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-查詢", "Index", new { WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁", "Index", new { TimeoutSeconds = "6", WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-查詢(沒有借書)", "Index", new { NoBook = "Y", WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:10px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁(沒有借書)", "Index", new { TimeoutSeconds = "6", NoBook = "Y", WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>

                @if (ViewBag.Gift != null)
                {
                    <div class="col-md-6">
                        @Html.ActionLink("酷幣ATM-直換禮物", "Index", new { TimeoutSeconds = "6", Mode = "gift", WhereSchoolNo = ViewBag.school_no }, new { @class = "btn-block btn btn-default", @role = "button" })
                    </div>
                }
            </div>
            <div class="form-group">
                <label class="text-danger">
                </label>
            </div>
        </div>
    </div>
   

}
