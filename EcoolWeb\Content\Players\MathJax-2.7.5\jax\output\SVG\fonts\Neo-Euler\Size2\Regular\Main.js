/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Size2={directory:"Size2/Regular",family:"NeoEulerMathJax_Size2",id:"NEOEULERSIZE2",32:[0,0,333,0,0,""],40:[1599,199,596,180,574,"180 700c0 336 89 675 346 899h48l-17 -17c-230 -225 -301 -556 -301 -882s71 -657 301 -882l17 -17h-48c-257 224 -346 563 -346 899"],41:[1599,199,595,22,415,"415 700c0 -336 -89 -675 -346 -899h-47l16 17c231 225 301 556 301 882s-70 657 -301 882l-16 17h47c257 -224 346 -563 346 -899"],47:[1599,200,811,53,759,"759 1583l-662 -1783l-41 8l-3 14l660 1775l12 2l29 -5"],91:[1674,125,472,226,453,"226 -125v1799h227v-47h-180v-1705h180v-47h-227"],92:[1599,200,811,53,759,"53 1583l662 -1783l41 8l3 14l-660 1775l-12 2l-29 -5"],93:[1674,125,472,18,245,"198 -78v1705h-180v47h227v-1799h-227v47h180"],123:[1599,200,667,119,547,"296 61v416c0 24 -7 127 -167 201c-10 5 -10 7 -10 22c0 12 0 16 8 20c38 17 157 72 168 196c1 8 1 65 1 98v312c0 83 0 120 69 189c35 35 128 84 162 84c19 0 20 -1 20 -20c0 -13 0 -17 -7 -20c-41 -17 -170 -71 -170 -188v-450c-1 -75 -56 -161 -194 -222 c119 -49 192 -133 194 -220v-425c0 -56 0 -145 168 -214c9 -3 9 -6 9 -20c0 -19 -1 -20 -20 -20c-37 0 -127 51 -160 82c-71 69 -71 114 -71 179"],124:[1897,208,213,86,126,"126 -200l-40 -8v2097l40 8v-2097"],125:[1599,200,667,119,547,"296 28v450c1 75 56 161 194 222c-119 49 -192 133 -194 220v425c0 59 0 143 -167 213c-10 4 -10 9 -10 21c0 19 3 20 21 20c34 0 124 -49 159 -82c71 -69 71 -114 71 -179v-416c0 -7 0 -9 3 -26c19 -109 135 -162 167 -177c7 -3 7 -8 7 -20c0 -10 0 -16 -6 -19 c-40 -18 -159 -73 -170 -197c-1 -8 -1 -65 -1 -98v-312c0 -83 0 -120 -69 -189c-36 -36 -129 -84 -161 -84c-18 0 -21 1 -21 20c0 10 0 16 8 20c42 18 169 72 169 188"],160:[0,0,333,0,0,""],8214:[1897,208,403,86,316,"316 -200l-40 -8v2097l40 8v-2097zM126 -200l-40 -8v2097l40 8v-2097"],8260:[1599,200,811,53,759,"759 1583l-662 -1783l-41 8l-3 14l660 1775l12 2l29 -5"],8725:[1599,200,811,53,759,"759 1583l-662 -1783l-41 8l-3 14l660 1775l12 2l29 -5"],8730:[1800,1,1000,110,1024,"458 -1h-34l-230 802l-68 -80l-16 15l139 163l215 -750l522 1651l38 -12"],8739:[1897,208,213,86,126,"126 -200l-40 -8v2097l40 8v-2097"],8741:[1297,208,403,86,316,"316 -200l-40 -8v1497l40 8v-1497zM126 -200l-40 -8v1497l40 8v-1497"],8743:[1128,267,1549,56,1492,"1492 -238l-681 1360l-24 6l-731 -1373l21 -22l76 2l641 1197l606 -1199"],8744:[1069,326,1549,56,1492,"1492 1040l-681 -1360l-24 -6l-731 1373l21 22l76 -2l641 -1197l606 1199"],8745:[1359,-1,1110,56,1053,"56 878c0 271 228 481 499 481c270 0 498 -210 498 -481v-877h-83v877c0 222 -187 398 -415 398s-416 -176 -416 -398v-877h-83v877"],8746:[1317,41,1110,56,1053,"139 440c0 -222 188 -398 416 -398s415 176 415 398v877h83v-877c0 -271 -228 -481 -498 -481c-271 0 -499 210 -499 481v877h83v-877"],8846:[1317,41,1110,56,1053,"139 440c0 -222 188 -398 416 -398s415 176 415 398v877h83v-877c0 -271 -228 -481 -498 -481c-271 0 -499 210 -499 481v877h83v-877zM513 989h83v-249h249v-83h-249v-249h-83v249h-249v83h249v249"],8896:[1128,267,1549,56,1492,"1492 -238l-681 1360l-24 6l-731 -1373l21 -22l76 2l641 1197l606 -1199"],8897:[1069,326,1549,56,1492,"1492 1040l-681 -1360l-24 -6l-731 1373l21 22l76 -2l641 -1197l606 1199"],8898:[1359,-1,1110,56,1053,"56 878c0 271 228 481 499 481c270 0 498 -210 498 -481v-877h-83v877c0 222 -187 398 -415 398s-416 -176 -416 -398v-877h-83v877"],8899:[1317,41,1110,56,1053,"139 440c0 -222 188 -398 416 -398s415 176 415 398v877h83v-877c0 -271 -228 -481 -498 -481c-271 0 -499 210 -499 481v877h83v-877"],8968:[1599,200,527,226,509,"226 -200v1799h283v-47h-236v-1752h-47"],8969:[1599,200,527,18,301,"254 -200v1752h-236v47h283v-1799h-47"],8970:[1599,200,527,226,509,"226 -200v1799h47v-1752h236v-47h-283"],8971:[1599,200,527,18,301,"254 -153v1752h47v-1799h-283v47h236"],9001:[1536,234,629,109,520,"160 651l360 -867l-43 -18l-368 885l368 885l43 -18"],9002:[1536,234,693,89,500,"133 1536l367 -885l-367 -885l-44 18l360 867l-360 867"],9180:[794,-414,1911,56,1855,"67 458l36 40c7 8 23 22 35 32c1 0 42 36 81 64c232 156 500 200 737 200c166 0 327 -24 459 -66c211 -70 350 -176 432 -272c8 -8 8 -10 8 -24c0 -12 0 -18 -10 -18c-2 0 -5 0 -8 2c-177 176 -438 302 -882 302c-240 0 -487 -40 -679 -148c-110 -62 -165 -116 -201 -152 c-3 -2 -5 -4 -9 -4c-9 0 -10 8 -10 10v22c0 2 10 10 11 12"],9181:[144,236,1911,56,1855,"956 -236c-161 0 -323 22 -460 66c-211 70 -350 176 -432 272c-8 8 -8 10 -8 24c0 10 0 18 10 18c3 0 6 -2 8 -4c183 -180 448 -300 882 -300c240 0 487 40 679 148c110 62 165 116 200 150c2 2 6 6 10 6c10 0 10 -8 10 -18c0 -14 0 -16 -6 -22c-21 -24 -60 -62 -76 -76 c-225 -196 -530 -264 -817 -264"],9182:[912,-484,1911,56,1855,"317 736h416c24 0 127 6 201 166c5 10 7 10 22 10c12 0 16 0 20 -8c17 -38 72 -156 196 -168h98h312c83 0 120 0 189 -70c35 -34 84 -128 84 -162c0 -18 -1 -20 -20 -20c-13 0 -17 0 -20 8c-17 40 -71 170 -188 170h-450c-75 0 -161 56 -222 194 c-49 -120 -133 -192 -220 -194h-425c-56 0 -145 0 -214 -168c-3 -10 -6 -10 -20 -10c-19 0 -20 2 -20 20c0 38 51 128 82 160c69 72 114 72 179 72"],9183:[70,358,1911,56,1855,"284 -106h450c75 -2 161 -56 222 -194c49 118 133 192 220 194h425c59 0 143 0 213 166c4 10 9 10 21 10c19 0 20 -2 20 -20c0 -34 -49 -124 -82 -160c-69 -70 -114 -70 -179 -70h-416c-7 0 -9 0 -26 -4c-109 -18 -162 -134 -177 -166c-3 -8 -8 -8 -20 -8 c-10 0 -16 0 -19 6c-18 40 -73 160 -197 170c-8 2 -65 2 -98 2h-312c-83 0 -120 0 -189 68c-36 36 -84 130 -84 162c0 18 1 20 20 20c10 0 16 0 20 -8c18 -42 72 -168 188 -168"],10216:[939,237,501,95,392,"139 351l253 -572l-37 -16l-260 588l260 588l37 -16"],10217:[939,237,568,79,375,"115 939l260 -588l-260 -588l-36 16l252 572l-252 572"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size2/Regular/Main.js");
