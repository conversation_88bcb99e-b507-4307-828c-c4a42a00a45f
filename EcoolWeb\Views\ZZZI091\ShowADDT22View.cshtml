﻿@model ZZZI09ShowADDT22ViewViewModel
@using ECOOL_APP.com.ecool.service
@using ECOOL_APP.com.ecool.util

@{ 
    string UserDomain = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Host + ":" + HttpContext.Current.Request.Url.Port;
}

@if (Model!=null&& Model.ADDT22List!=null && Model.ADDT22List.Count > 0)
{
    <section class="onlineGallery">
        <div class="bgPosition">
            <h2 class="heading-h2 mx-3">線上藝廊</h2>
            <div class="galleryAll">                
                @foreach (var item in Model.ADDT22List)
                {

                    <div class="gallery-item">
                        <div class="card">
                            @if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                            {

                                string NewImg = item.PHOTO_FILE.Replace(Path.GetExtension(item.PHOTO_FILE), "_M" + Path.GetExtension(item.PHOTO_FILE));

                                string ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, NewImg);
                                if (ImgUrl == "")
                                {


                                    ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE.Replace(Path.GetExtension(item.PHOTO_FILE), "_S" + Path.GetExtension(item.PHOTO_FILE)));
                                    if (ImgUrl == "")
                                    {
                                        ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE);


                                    }
                                }
                                <img src="@ImgUrl" title="@item.PHOTO_SUBJECT" alt="@item.PHOTO_SUBJECT" />

                            }
                            @*else if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.video)
                            {
                                string VideoUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE);

                                <video class="Div-EZ-ArtGallery-img-all" controls>
                                    <source src="@VideoUrl" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>

                            }*@
                            else
                            {
                                <iframe src="@item.PHOTO_FILE"></iframe>
                            }

                            <div class="card-body">
                                <strong class="card-title h5"><b>@item.PHOTO_SUBJECT<span class="float-right">班級:@item.PHOTO_CLASS_NO</span></b></strong>
                            </div>
                        </div>
                    </div>

                }
            </div>
        </div>
    </section>

}
