﻿@model EcoolWeb.ViewModels.ZZT17CountLoginQueryListViewModel
@{
    ViewBag.Title = "系統操作紀錄-登入次數統計";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ActionLink("系統操作紀錄一覽表", "QUERY", "ZZT17", null, new { @class = "btn btn-sm btn-sys" })

@using (Html.BeginForm("CountLoginQuery", "ZZT17", FormMethod.Post, new { id = "ZZT17" }))
{
    <br />
    <div class="form-inline" role="form">

        <div class="form-group">
            <label class="control-label">使用者類型</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("ddlUSER_TYPE", (IEnumerable<SelectListItem>)ViewBag.USER_TYPEItems,  new { @class = "form-control input-sm"  })
            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.Page)
        </div>
        <div class="form-group">
            <label class="control-label">年級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <br />

    <div  class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                <thead>
                    <tr>
                        <th style="text-align: center">
                            班級
                        </th>
                        <th style="text-align: center;">
                            座號
                        </th>
                        <th style="text-align: center;">
                            使用這帳號
                        </th>
                        <th style="text-align: center;">
                            名稱
                        </th>
                        <th style="text-align: center">
                            登入次數
                        </th>
                    </tr>
                </thead>
              <tbody>

                  @foreach (var item in Model.uZZT17List)
                  {
                      string UserNOText = item.USER_NO;
                      <tr>
                          <td style="text-align: center">
                              @Html.DisplayFor(modelItem => item.CLASS_NO)
                          </td>
                          <td style="text-align: center">
                              @Html.DisplayFor(modelItem => item.SEAT_NO)
                          </td>
                          <td style="text-align: center">
                              @if (item.USER_NO.Length >= 10)
                              { UserNOText = item.USER_NO.Substring(0, item.USER_NO.Length - 4) + "OOOO"; }
                              @UserNOText
                          </td>
                          <td style="text-align: center">
                              @Html.DisplayFor(modelItem => item.NAME)
                          </td>
                          <td style="text-align: center">
                              @Html.DisplayFor(modelItem => item.LOGINCOUNT)
                          </td>
                      </tr>
                  }
              </tbody>
            </table>
        </div>
    </div>
    <div>
        @Html.Pager(Model.uZZT17List.PageSize, Model.uZZT17List.PageNumber, Model.uZZT17List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>

}

@section scripts{
    <script>
            var targetFormID = '#ZZT17';




            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }
            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

            function todoClear() {
                ////重設

                $(targetFormID).find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }
    </script>
}