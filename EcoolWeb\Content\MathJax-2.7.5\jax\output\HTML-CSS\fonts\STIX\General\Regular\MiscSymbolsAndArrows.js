/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscSymbolsAndArrows.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{11026:[662,157,910,45,865],11027:[662,157,910,45,865],11028:[662,157,910,45,865],11029:[662,157,910,45,865],11030:[744,242,1064,39,1025],11031:[744,242,1064,39,1025],11032:[744,242,1064,39,1025],11033:[744,242,1064,39,1025],11034:[662,157,910,45,865],11035:[780,180,1040,40,1000],11036:[780,180,1040,40,1000],11037:[332,-172,240,50,190],11038:[332,-172,240,50,190],11039:[690,105,910,36,874],11040:[690,105,910,36,874],11041:[680,178,910,82,828],11042:[680,178,910,82,828],11043:[633,127,926,24,902],11044:[785,282,1207,70,1137],11045:[581,96,779,45,734],11046:[581,96,779,45,734],11047:[609,105,544,40,504],11048:[609,105,544,40,504],11049:[488,-16,523,26,497],11050:[488,-16,357,26,331],11051:[488,-16,357,26,331],11052:[500,-4,842,50,792],11053:[500,-4,842,50,792],11054:[623,119,596,50,546],11055:[623,119,596,50,546],11056:[448,-57,926,70,856],11057:[739,232,926,60,866],11058:[569,61,1200,52,1147],11059:[449,-58,1574,55,1519],11060:[450,-57,926,56,871],11061:[450,-57,926,55,871],11062:[450,-57,926,55,871],11063:[449,-57,1412,55,1357],11064:[449,-57,926,55,873],11065:[450,-57,926,55,871],11066:[450,-57,926,55,871],11067:[449,-57,926,55,871],11068:[450,-57,926,55,871],11069:[450,-57,926,50,876],11070:[449,-57,926,55,871],11071:[449,-57,926,55,871],11072:[565,-57,926,55,871],11073:[508,-57,926,55,871],11074:[449,141,926,55,871],11075:[532,26,926,45,871],11076:[532,26,926,45,871],11077:[701,195,928,55,873],11078:[701,195,928,55,873],11079:[508,-57,926,55,871],11080:[449,141,926,55,871],11081:[508,-57,926,55,871],11082:[449,141,926,55,871],11083:[449,2,926,55,871],11084:[449,2,926,55,871],11088:[619,30,794,60,734],11089:[619,30,794,60,734],11090:[597,13,700,35,665],11091:[712,126,865,45,840],11092:[712,127,865,45,840]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/MiscSymbolsAndArrows.js");
