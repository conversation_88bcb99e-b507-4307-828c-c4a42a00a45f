﻿@model ECOOL_APP.EF.ADDT01
@{
    ViewBag.Title = "線上投稿-詳細資料";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    var Search = TempData["Search"] as EcoolWeb.Models.ADDI01IndexViewModel;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}

<style>
    .jfontsize-button {
        font-weight: bold;
        padding: 3px;
        color: #000;
        border: solid 1px #ccc;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }

    .jfontsize-button:hover {
        text-decoration: none;
        background: #333;
        color: #fff;
        border: solid 1px #333;
    }

    .jfontsize-disabled, .jfontsize-disabled:hover {
        color: #aaa;
        border: solid 1px #eee;
        background: #eee;
        cursor: not-allowed;
    }

    .font-control {
        margin-bottom: 10px;
    }

    .content-area {
        font-size: 16px;
        line-height: 1.6;
    }

    .ui-dialog-titlebar-close {
        padding: 0 !important;
    }

        .ui-dialog-titlebar-close:after {
            content: '';
            width: 20px;
            height: 20px;
            display: inline-block;
            /* Change path to image*/
            background-image: url("../../EcoolWeb/Content/images/ui-icons_222222_256x240.png");
            ;
            background-position: -96px -128px;
            background-repeat: no-repeat;
        }
</style>

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js" nonce="cmlvaw"></script>
<script src="~/Scripts/jstorage.js" nonce="cmlvaw"></script>
<script src="~/Scripts/jquery.jfontsize-2.0.js" nonce="cmlvaw"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.7.1/clipboard.min.js" nonce="cmlvaw"></script>
<script src="~/Scripts/ADDI01/details.js" nonce="cmlvaw"></script>
@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")
@if (TempData["StatusMessage"] != null)
{
    string HtmlMsg = TempData["StatusMessage"].ToString().Replace("\r\n", "<br />");
    <div class="modal fade bs-example-modal-lg" tabindex="-1" aria-labelledby="REMINDModalLabel" id="remind-modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body" id="remind-content">
                    <br />
                    <ol>
                        @Html.Raw(HtmlMsg)
                    </ol>
                </div>
            </div>
        </div>
    </div>
}
<img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="Div-EZ-Pink">
    <div class="Details">

        <div class="row">
            @*<h4>學生線上投稿內容</h4>*@
            <div class="col-md-6 col-sm-5 dl-horizontal-EZ">
                <samp class="dt">
                    投稿日期
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime")
                </samp>
            </div>
            <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                <samp class="dt">
                    班級
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.CLASS_NO)
                </samp>
            </div>
            @if (user?.USER_NO == Model.USER_NO || user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
            {

                <div class="col-md-3 col-sm-4 dl-horizontal-EZ">
                    <samp class="dt">
                        姓名
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.SNAME)
                    </samp>
                </div>
            }
        </div>
        <div class="row">
            <div class="col-md-6 col-sm-12  dl-horizontal-EZ">
                <samp class="dt">
                    文章標題
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.SUBJECT)
                </samp>
                @if (Model.SHARE_YN == "y")
                {
                    <img src="~/Content/img/icons-like-05.png" alt="推薦文章" />
                }
            </div>
            @if (user?.USER_NO == Model.USER_NO || user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
            {

                <div class="col-md-3 col-sm-5 dl-horizontal-EZ">
                    <samp class="dt">
                        座號
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.SEAT_NO)
                    </samp>
                </div>
            }
                <div class="col-md-3 col-sm-6  dl-horizontal-EZ">
                    <samp class="dt">
                        狀態
                    </samp>
                    <samp class="dd">
                        @ADDStatus.GetADDT01StatusString(Model.WRITING_STATUS.Value)
                    </samp>
                </div>
                <div class="col-md-3 col-sm-4  dl-horizontal-EZ">

                    <samp class="dt">
                        酷幣值
                    </samp>
                    <samp class="dd" style="color:blue">
                        @Html.DisplayFor(model => model.CASH)
                    </samp>
                </div>
            </div>
    </div>
    <div class="modal fade bs-example-modal-lg" id="myShareUrlModal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
        <div class="modal-dialog modal-sm" role="document" style="top:200px">
            <div class="modal-content">

                <div class="input-group">
                    <span class="input-group-btn">
                        <button type="button" id="id_copy"
                                data-clipboard-target="#id_text"
                                data-clipboard-action="copy" onclick="OnCopy()">
                            點擊複製
                        </button>
                    </span>
                    <div id="id_text">@ViewBag.WinOpenShareUrlLink</div>
                    <div id="success" style="display:none">已複製</div>
                    <input id="copyStr" type="hidden" value="@ViewBag.WinOpenShareUrlLink">
                </div><!-- /input-group -->
            </div>
        </div>
    </div>
    <div style="height:25px;text-align:right">
        <a id="jfontsize-minus" class="jfontsize-button" href="#">A-</a>&nbsp;
        @*<a id="jfontsize-default" class="jfontsize-button" href="#">A</a>*@
        <a id="jfontsize-plus" class="jfontsize-button" href="#">A+</a>&nbsp;
    </div>

    <div class="row">

        @if (ViewBag.VoiceUrl != string.Empty)
        {

            <div class="col-md-12 ">
                @if (Request.Browser.Browser == "InternetExplorer")
                {
                    <object id="Player"
                            classid="CLSID:6BF52A52-394A-11d3-B153-00C04F79FAA6" width="300" height="45">
                        <param name="autoStart" value="false">
                        <param name="URL" value="@ViewBag.VoiceUrl">
                    </object>
                }
                else
                {
                    <audio width="300" height="48" controls="controls">
                        <source src="@ViewBag.VoiceUrl" />
                    </audio>
                }
            </div>

        }

        <div class="col-md-12">
            <div class="p-context">
                <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12">
                    詳細內容:限制1500字，目前字數為<span id="ShowFontLen" style="color:red"> </span>字
                </label>
                <div id="ARTICLE">
                    @if (ViewBag.ShowOriginalArticle == "V")
                    {
                        @Html.Raw(HttpUtility.HtmlDecode(Model.ARTICLE_VERIFY))
                    }
                    else
                    {
                        @Html.Raw(HttpUtility.HtmlDecode(Model.ARTICLE))
                    }
                </div>
            </div>
        </div>
        @if (!string.IsNullOrWhiteSpace(Model.YOUTUBE_URL))
        {

            <div class="col-md-12">
                <div class="p-context">
                    <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12"> youtube網址</label>

                    <div>    <a href="@Html.DisplayFor(model => model.YOUTUBE_URL)" target="_blank">@Html.DisplayFor(model => model.YOUTUBE_URL)</a>  </div>
                </div>
            </div>
        }
    </div>
    @{
        string str = ViewBag.WinOpenShareUrlLink + "?WRITING_NO=" + Model.WRITING_NO;
    }
    <div class="row Div-btn-center">
        <div class="col-md-12">
            <a href='@Url.Action("Index", "ADDI01",new {
                        BackAction = Search.BackAction,
                        OrdercColumn = Search.OrdercColumn,
                        whereKeyword = Search.whereKeyword,
                        whereUserNo = Search.whereUserNo,
                        whereWritingStatus = Search.whereWritingStatus,
                        whereShareYN = Search.whereShareYN,
                        whereComment = Search.whereComment,
                        whereCommentCash = Search.whereCommentCash,
                        whereCLASS_NO = Search.whereCLASS_NO,
                        whereGrade = Search.whereGrade,
                        Page = Search.Page
                    })' role="button" class="btn btn-default">
                返回
            </a>

            @if (ViewBag.ShowOriginalArticle == "O")
            {
                @Html.ActionLink("批閱後文章", "Details", new
           {
               WRITING_NO = Model.WRITING_NO,
               ShowOriginal = false,
               BackAction = Search.BackAction,
               OrdercColumn = Search.OrdercColumn,
               whereKeyword = Search.whereKeyword,
               whereUserNo = Search.whereUserNo,
               whereWritingStatus = Search.whereWritingStatus,
               whereShareYN = Search.whereShareYN,
               whereComment = Search.whereComment,
               whereCommentCash = Search.whereCommentCash,
               whereCLASS_NO = Search.whereCLASS_NO,
               whereGrade = Search.whereGrade,
               Page = Search.Page
           }, new { @role = "button", @class = "btn btn-default" })
            }
            else if (ViewBag.ShowOriginalArticle == "V")
            {

                <a href="@Url.Action("Details", "ADDI01",new {
                           WRITING_NO = Model.WRITING_NO,
                           ShowOriginal = true,
                           BackAction = Search.BackAction,
                           OrdercColumn = Search.OrdercColumn,
                           whereKeyword = Search.whereKeyword,
                            whereUserNo = Search.whereUserNo,
                            whereWritingStatus = Search.whereWritingStatus,
                            whereShareYN = Search.whereShareYN,
                            whereComment = Search.whereComment,
                            whereCommentCash = Search.whereCommentCash,
                            whereCLASS_NO = Search.whereCLASS_NO,
                            whereGrade = Search.whereGrade,
                            Page = Search.Page
                        })" role="button" class="btn btn-default">
                    學生原稿
                </a>
            }

            @if (HRMT07.isFriends(user) && Model.USER_NO != user.USER_NO)
            {<a href='@Url.Action("FriendsData", "ADDI01"
                            , new {
                                WRITING_NO = Model.WRITING_NO,
                                BackAction = Search.BackAction,
                                OrdercColumn = Search.OrdercColumn,
                                whereKeyword = Search.whereKeyword,
                                whereUserNo = Search.whereUserNo,
                                whereWritingStatus = Search.whereWritingStatus,
                                whereShareYN = Search.whereShareYN,
                                whereComment = Search.whereComment,
                                whereCommentCash = Search.whereCommentCash,
                                whereCLASS_NO = Search.whereCLASS_NO,
                                whereGrade = Search.whereGrade,
                                Page = Search.Page
                            })' role="button" class="btn btn-default">
                    訂閱此人文章
                </a>
            }
            @if (user?.USER_NO==Model.USER_NO|| user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
            {
                <a class="btn btn-default" role="button" onclick="onWinOpenShareUrlLink('@str')">
                    分享網址
                </a>
            }
            @if (ViewBag.SharetoNew == "Y")
            {
                <a class="btn btn-default" role="button" onclick="onMdnKidsModal();">
                    投稿國語日報
                </a>

            }
        </div>
    </div>

    <div class="row Div-btn-center">
    </div>
    <div id="DivImg">
        @if (ViewBag.ImageUrl != null && Enumerable.Any(ViewBag.ImageUrl))
        {
            foreach (var Img in ViewBag.ImageUrl as List<string>)
            {
                string ext = Path.GetExtension(Img);
                if (ext == ".ppt" || ext == ".pptx")
                {
                    string name = Path.GetFileName(Img);
                    <div class="col-md-12 col-sm-12 col-xs-12 " style="text-align:center">
                        <br />
                        <a href="@Img" class="btn btn-link btn-lg" target="_blank"><span class="glyphicon glyphicon-download-alt"></span> @name</a>
                        <br /><br />
                    </div>
                }
                else if (Img.Contains("youtube.com"))
                {
                    <div class="col-md-12 col-sm-12 col-xs-12 " style="text-align:center">
                        @Img
                    </div>
                }
                else
                {
                    <div class="col-md-12 col-sm-12 col-xs-12 " style="text-align:center">
                        <img src='@(Img + "?refreshCache=" + DateTime.Now.ToString("hhmmss"))' style="max-height:250px;width:auto;margin-left:auto;margin-right:auto" href="@Img" class=" img-responsive " />
                    </div>
                }
            }

        }
    </div>
    <div id="DivPPT">
        @if (ViewBag.OtherFilesUrl != null && Enumerable.Any(ViewBag.OtherFilesUrl))
        {
            foreach (var OtherFiles in ViewBag.OtherFilesUrl as List<string>)
            {
                string ext = Path.GetExtension(OtherFiles);
                if (ext == ".ppt" || ext == ".pptx" || ext == ".pdf" || ext == ".PDF")
                {
                    string name = Path.GetFileName(OtherFiles);
                    <div class="col-md-12 col-sm-12 col-xs-12 " style="text-align:center">
                        <br />
                        <a href="@OtherFiles" class="btn btn-link btn-lg" target="_blank"><span class="glyphicon glyphicon-download-alt"></span> @name</a>
                        <br /><br />
                    </div>
                }
            }
        }
    </div>
    <div class="Div-Feedback">
        <div>
            <div class="row">
                <div class="col-md-6 col-md-6 text-left">
                    <img src="~/Content/img/stw_fbk2.gif" />
                </div>
                <div class="col-md-6 col-md-6 text-right">

                    <a href='@Url.Action("Comment", "ADDI01"
                            , new {
                                WRITING_NO = Model.WRITING_NO,
                                BackAction = Search.BackAction,
                                OrdercColumn = Search.OrdercColumn,
                                whereKeyword = Search.whereKeyword,
                                whereUserNo = Search.whereUserNo,
                                whereWritingStatus = Search.whereWritingStatus,
                                whereShareYN = Search.whereShareYN,
                                whereComment = Search.whereComment,
                                whereCommentCash = Search.whereCommentCash,
                                whereCLASS_NO = Search.whereCLASS_NO,
                                whereGrade = Search.whereGrade,
                                Page = Search.Page
                            })'>
                        <img src="~/Content/img/stw_feedback.gif" />
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-left">
                    @if (string.IsNullOrWhiteSpace(Model.VERIFY_COMMENT) == false)
                    {
                        <samp>教師評語：</samp>
                        <samp style="white-space: pre-line">@Model.VERIFY_COMMENT</samp>
                    }
                </div>
            </div>
            <div style="height:25px"></div>

            @{List<ADDT02> CommentList = Model.ADDT02.Where(a => a.COMMENT_STATUS != 9).ToList();}

            <div class="row" style="margin: 0px auto">
                @{ int Num = 0;
                    Dictionary<string, string> emotionDict = ViewBag.EmoticonDict as Dictionary<string, string>;

                    foreach (ADDT02 comment in CommentList)
                    {
                        if (Num > 0)
                        {

                            <div style="margin-top:20px;margin-bottom:20px;height:1px;background-color:palevioletred;"></div>

                        }

                            <div class="col-md-12 col-lg-12 p-context">
                                @if (user != null)
                                {
                                    if (!string.IsNullOrEmpty(comment.CLASS_NO) &&( user.USER_TYPE=="T" || user.USER_TYPE == "A"))
                                    {
                                        <div class="col-md-8">
                                            班級： @comment.CLASS_NO
                                        </div>}}
                                <div class="col-md-6">
                                    姓名： @comment.NICK_NAME
                                </div>
                                <div class="col-md-6">
                                    留言日期：@comment.CRE_DATE.Value.ToShortDateString()
                                </div>

                                <div class="col-md-8">
                                    @if (comment.CASH > 0)
                                    {
                                        <img src="~/Content/img/icons-feedback-01.png" />
                                        <span style="color:blue">有幫助的回饋</span>
                                        <span style="color:red">+@comment.CASH.ToString()</span>
                                    }
                                    else
                                    {
                                        if (((List<int>)ViewBag.CanLikes).Contains(comment.COMMENT_NO))
                                        {
                                            <a class="btn-primary btn btn-sm" href='@Url.Action("LikeComment", "ADDI01"
                                                                                   , new {
                                                                                       COMMENT_NO = comment.COMMENT_NO,
                                                                                       BackAction = Search.BackAction,
                                                                                       OrdercColumn = Search.OrdercColumn,
                                                                                       whereKeyword = Search.whereKeyword,
                                                                                       whereUserNo = Search.whereUserNo,
                                                                                       whereWritingStatus = Search.whereWritingStatus,
                                                                                       whereShareYN = Search.whereShareYN,
                                                                                       whereComment = Search.whereComment,
                                                                                       whereCommentCash = Search.whereCommentCash,
                                                                                       whereCLASS_NO = Search.whereCLASS_NO,
                                                                                       whereGrade = Search.whereGrade,
                                                                                       Page = Search.Page
                                                                                   })'>
                                                <img src="~/Content/img/icons-feedback-02.png" />
                                                【有幫助】
                                            </a>
                                        }
                                    }
                                </div>
                            </div>

                        <div class="form-group">
                            <div class="col-md-12 col-lg-12 p-context">
                                <div style="height:14px"></div>
                                @comment.COMMENT
                                <br /><br />
                                <div>
                                    <img class="img-responsive" style="width:15%" src="@emotionDict.FirstOrDefault(e=>e.Key == comment.EMOTICON).Value" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 text-right" style="z-index:99">
                            @if (ViewBag.VisibleDisableComment == "Y")
                            {
                                @Html.ActionLink("刪留言", "DisableComment", new { COMMENT_NO = comment.COMMENT_NO }, new { @class = "btn btn-xs btn-default" })
                            }
                        </div>

                        Num++;
                    }
                }
            </div>
        </div>
    </div>
</div>

<div hidden="hidden" id="notice">
    <span>投稿須知：</span></br>

    <span style="color:red;">
        1.本連結會直接連結到國語日報線上投稿的網頁。
    </span>
    <br />
    <span>
        2.作品經採用刊登，將同時刊於國語日報、國語日報網站及相關行動載具及臉書， 並收錄國語日報知識庫，提供本報相關報刊轉載。若不同意，請於來稿時註明。
    </span> <br />
    <span>
        3.請勿抄襲（投稿圖文須為原創，如有侵權行為，由創作者自負法律責任），勿一稿多投。本報得對來稿有刪修權。　投稿視各版需求，若於一定期限內未通知採用，請自行處理。
    </span>
</div>
@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局狀態消息配置
        window.ADDI01_STATUS_MESSAGE = "@TempData["StatusMessage"]";
    </script>
    <script src="~/Scripts/ADDI01/details-admin.js" nonce="cmlvaw"></script>
}