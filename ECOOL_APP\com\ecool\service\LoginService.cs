﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace com.ecool.service
{
    public class LoginService : ServiceBase
    {
        /// <summary>
        /// 登入驗證查詢
        /// </summary>
        /// <param name="parSCHOOL_NO">學校代號</param>
        /// <param name="parUSER_NO">登入帳號</param>
        /// <param name="parPASSWORD">密碼</param>
        /// <returns>DT</returns>
        public DataTable USP_LOGOIN_DATATB(string parSCHOOL_NO, string parUSER_NO, string parPASSWORD)
        {
            DataTable list_data = new DataTable();
            DataTable list_AuthIdentity = CheckAuthIdentity(parSCHOOL_NO, parUSER_NO, parPASSWORD);
            if (list_AuthIdentity.Rows.Count >0)
            {
                try
                {

                    string sql = @"  select C.SHORT_NAME,A.USER_NO,B.ROLE_ID,A.SCHOOL_NO,<PERSON><PERSON>,A<PERSON>,<PERSON>.<PERSON>" +
                    " ,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>_<PERSON> " +
                    ",<PERSON><PERSON>,A<PERSON>,A.USER_TYPE,A.USER_KEY " +
                    " from HRMT01 A (nolock) " +
                    " inner join HRMT25 B (nolock) on A.SCHOOL_NO=B.SCHOOL_NO and A.USER_NO=B.USER_NO and B.DEFAULT_YN='Y' " +
                    " inner join BDMT01 C (nolock) on A.SCHOOL_NO=C.SCHOOL_NO " +
                    " Where A.USER_NO='" + parUSER_NO + "' ";

                    list_data = new sqlConnection.sqlConnection().executeQueryByDataTableList(sql);
                }
                catch (Exception exception)
                {
                    throw exception;
                }
            }
            return list_data;
        }

        public DataTable CheckAuthIdentity(string parSCHOOL_NO, string parUSER_NO, string parPASSWORD)
        {
            DataTable list_data = new DataTable();
            try
            {
                string sql = @" SELECT USER_NO " +
                              " FROM ZZT08 " +
                              " WHERE SCHOOL_NO = '" + parSCHOOL_NO +"' "+
                              " AND USER_NO = '" + parUSER_NO +"' "+
                              " AND PASSWORD = '" + parPASSWORD + "' ";
                list_data = new sqlConnection.sqlConnection().executeQueryByDataTableList(sql);
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

    }
}
