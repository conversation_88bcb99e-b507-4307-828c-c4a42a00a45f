﻿using System;
using System.Threading.Tasks;
using Microsoft.Owin;
using Owin;
using Hangfire;
using Hangfire.SqlServer;
using EcoolWeb.CustomAttribute;
using Hangfire.Logging;
using log4net;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using System.Diagnostics;

[assembly: OwinStartup(typeof(EcoolWeb.Startup))]

namespace EcoolWeb
{
    /// <summary>
    /// 排程
    /// </summary>
    public partial class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            string conn = string.Empty;

            try
            {
                conn = new com.ecool.sqlConnection.sqlConnection().getDefaultConnectionString();
            }
            catch (Exception ex)
            {
                throw ex;
            }

            if (!string.IsNullOrWhiteSpace(conn))
            {
                try
                {
#if !DEBUG
                    GlobalConfiguration.Configuration
                                        .UseSqlServerStorage(conn
                                            ,
                                            new SqlServerStorageOptions { PrepareSchemaIfNecessary = false, QueuePollInterval = TimeSpan.FromSeconds(1) });

                    //使用 Dashboard，並任何人 Access 它
                    app.UseHangfireDashboard("/ds", new DashboardOptions
                    {
                        Authorization = new[] { new HangfireAuthorizationFilter() }
                    });

                    // 啟用HanfireServer
                    app.UseHangfireServer(new BackgroundJobServerOptions { WorkerCount = Environment.ProcessorCount * 5 });
#endif
#if !DEBUG
                    //每天 晚上 12:00執行
                    RecurringJob.AddOrUpdate<AWAI07Service>("銀行定存到期處理", x => x.CheckAutoBankTask(),
                        Cron.Daily(02, 00),
                        TimeZoneInfo.FindSystemTimeZoneById("Taipei Standard Time"));

                    RecurringJob.AddOrUpdate<CERI02Service>("護照學年度資料", x => x.AutoSetSyearAccreditationData(),
                    Cron.Daily(01, 00),
                 TimeZoneInfo.FindSystemTimeZoneById("Taipei Standard Time"));
                RecurringJob.AddOrUpdate<CERI02Service>(" 排程MergeHRMT01寄信", x => x.sendMERAGE_HRMT01_SQLMail(),
                   Cron.Daily(03, 00),
                   TimeZoneInfo.FindSystemTimeZoneById("Taipei Standard Time"));
                    //每分執行
                    RecurringJob.AddOrUpdate<AWAI01Service>("競標自動得標處理(學生)", x => x.SaveBidToTimeUp(AWAI01SearchViewModel.SouTableVal.Student, null),
                        Cron.Minutely(),
                        TimeZoneInfo.FindSystemTimeZoneById("Taipei Standard Time"));

                    //每10分執行
                    //RecurringJob.AddOrUpdate<PushHelper>("Push", x => x.ToPushServer(),
                    //    Cron.MinuteInterval(10),
                    //    TimeZoneInfo.FindSystemTimeZoneById("Taipei Standard Time"));

                    //每天 晚上 1:00執行

#endif
#if !DEBUG
                    RecurringJob.AddOrUpdate<Util.ExtractCardService>("取卡號", x => x.GetCardNumberSchedual(),
                        Cron.Daily(1, 0),
                        TimeZoneInfo.FindSystemTimeZoneById("Taipei Standard Time"));
#endif
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
      }
    }
}