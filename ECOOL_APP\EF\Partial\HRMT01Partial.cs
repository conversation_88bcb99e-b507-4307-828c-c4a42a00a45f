﻿using ECOOL_APP.com.ecool.util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public partial class HRMT01
    {
        public enum GradeVal : byte
        {
            /// <summary>
            /// 一年級
            /// </summary>
            In1Grade = 1,

            /// <summary>
            /// 二年級
            /// </summary>
            In2Grade = 2,

            /// <summary>
            /// 二年級
            /// </summary>
            In3Grade = 3,

            /// <summary>
            /// 四年級
            /// </summary>
            In4Grade = 4,

            /// <summary>
            /// 五年級
            /// </summary>
            In5Grade = 5,

            /// <summary>
            /// 六年級
            /// </summary>
            In6Grade = 6
        }

        public enum SexVal : byte
        {
            /// <summary>
            /// 男
            /// </summary>
            man = 1,

            /// <summary>
            /// 女
            /// </summary>
            woman = 0
        }

        public enum SemesterVal : byte
        {
            /// <summary>
            /// 上學期
            /// </summary>
            FirstSemester = 1,

            /// <summary>
            /// 下學期
            /// </summary>
            SecondSemester = 2
        }

        static public string ParserSemester(byte? Semester)
        {
            if (Semester.HasValue == false) return string.Empty;
            switch (Semester.Value)
            {
                case (byte)SemesterVal.FirstSemester:
                    return "上學期";

                case (byte)SemesterVal.SecondSemester:
                    return "下學期";
            }

            return Semester.Value.ToString();
        }
        static public string ParseAWAT15(int? VAWAT15Status) {
            if (VAWAT15Status.HasValue == false) return string.Empty;

            switch (VAWAT15Status)
            {

                case (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_Del:
                    return "取消";
                case (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_UN:
                    return "已升級未領";
                case (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_Receive:
                    return "已領取";
            }
            return VAWAT15Status.Value.ToString();
        }
        /// <summary>
        /// 取得年級名稱
        /// </summary>
        /// <param name="grade"></param>
        /// <returns></returns>
        static public string ParserGrade(byte? grade)
        {
            if (grade.HasValue == false) return string.Empty;

            switch (grade.Value)
            {
                case (byte)GradeVal.In1Grade:
                    return "一年級";

                case (byte)GradeVal.In2Grade:
                    return "二年級";

                case (byte)GradeVal.In3Grade:
                    return "三年級";

                case (byte)GradeVal.In4Grade:
                    return "四年級";

                case (byte)GradeVal.In5Grade:
                    return "五年級";

                case (byte)GradeVal.In6Grade:
                    return "六年級";
            }

            return grade.Value.ToString();
        }

        /// <summary>
        /// 取得性別
        /// </summary>
        /// <param name="Sex"></param>
        /// <returns></returns>
        static public string ParserSex(byte? Sex)
        {
            if (Sex.HasValue == false) return string.Empty;

            switch (Sex.Value)
            {
                case (byte)SexVal.man:
                    return "男";

                case (byte)SexVal.woman:
                    return "女";
            }

            return Sex.Value.ToString();
        }

        /// <summary>
        /// 取得性別
        /// </summary>
        /// <param name="Sex"></param>
        /// <returns></returns>
        static public string ParserSex(string Sex)
        {
            if (string.IsNullOrWhiteSpace(Sex)) return string.Empty;

            byte byteSex;
            byte.TryParse(Sex, out byteSex);
            return ParserSex(byteSex);
        }

        static public List<SelectListItem> GetGradeItems(string defaultSelectValue, bool IsAll = true)
        {
            List<SelectListItem> GradeItems = new List<SelectListItem>();

            if (IsAll)
            {
                GradeItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });
            }

            foreach (var Item in Enum.GetValues(typeof(GradeVal)))
            {
                GradeItems.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = ((byte)Item).ToString(), Selected = ((byte)Item).ToString() == defaultSelectValue });
            }

            return GradeItems;
        }

        static public List<SelectListItem> GetSelectGradeItems(List<byte> defaultSelectValue)
        {
            List<SelectListItem> GradeItems = new List<SelectListItem>();

            GradeItems.Add(new SelectListItem() { Text = "不分年級", Value = "", Selected = (defaultSelectValue?.Count ?? 0) >= Enum.GetNames(typeof(GradeVal)).Length });

            foreach (var Item in Enum.GetValues(typeof(GradeVal)))
            {
                if (defaultSelectValue != null)
                {
                    if (defaultSelectValue.Where(a => a == (byte)Item).Any())
                    {
                        GradeItems.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = ((byte)Item).ToString(), Selected = true });
                    }
                    else
                    {
                        GradeItems.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = ((byte)Item).ToString(), Selected = false });
                    }
                }
                else
                {
                    GradeItems.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = ((byte)Item).ToString(), Selected = false });
                }
            }

            return GradeItems;
        }

        static public List<SelectListItem> GetSelectGradeSemesterItems(List<string> defaultSelectValue)
        {
            List<SelectListItem> GradeSemesterItems = new List<SelectListItem>();

            foreach (var Grade in Enum.GetValues(typeof(GradeVal)))
            {
                foreach (var Semester in Enum.GetValues(typeof(SemesterVal)))
                {
                    if (defaultSelectValue != null)
                    {
                        if (defaultSelectValue.Where(a => a == $@"{(byte)Grade}_{(byte)Semester}").Any())
                        {
                            GradeSemesterItems.Add(new SelectListItem() { Text = $"{ParserGrade((byte)Grade)}{ParserSemester((byte)Semester)}", Value = $@"{(byte)Grade}_{(byte)Semester}", Selected = true });
                        }
                        else
                        {
                            GradeSemesterItems.Add(new SelectListItem() { Text = $"{ParserGrade((byte)Grade)}{ParserSemester((byte)Semester)}", Value = $@"{(byte)Grade}_{(byte)Semester}", Selected = false });
                        }
                    }
                    else
                    {
                        GradeSemesterItems.Add(new SelectListItem() { Text = $"{ParserGrade((byte)Grade)}{ParserSemester((byte)Semester)}", Value = $@"{(byte)Grade}_{(byte)Semester}", Selected = false });
                    }
                }
            }

            return GradeSemesterItems;
        }

        static public List<SelectListItem> GetSEMESTERItems(string defaultSelectValue)
        {
            List<SelectListItem> SEMESTERItems = new List<SelectListItem>();

            SEMESTERItems.Add(new SelectListItem() { Text = "1", Value = ((byte)1).ToString(), Selected = ((byte)1).ToString() == defaultSelectValue });
            SEMESTERItems.Add(new SelectListItem() { Text = "2", Value = ((byte)2).ToString(), Selected = ((byte)2).ToString() == defaultSelectValue });

            return SEMESTERItems;
        }

        /// <summary>
        /// 取得班級
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <returns></returns>
        static public List<string> GetClassList(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            var ClassItems = db.HRMT01.Where(u => u.SCHOOL_NO == SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false
            && u.USER_TYPE == UserType.Student
            && u.USER_STATUS != UserStaus.Invalid && u.USER_TYPE == UserType.Student).
            Select(x => x.CLASS_NO).Distinct().OrderBy(x => x).ToList();

            return ClassItems;
        }

        /// <summary>
        /// 取得班級下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <returns></returns>
        static public IOrderedQueryable<SelectListItem> GetSYearClassListData(string SCHOOL_NO, byte? SEYEAR, ref ECOOL_DEVEntities db)
        {
            IOrderedQueryable<SelectListItem> ClassItems;
            if (SEYEAR != null)
            {
                ClassItems = db.HRMT01.Where(u => u.SCHOOL_NO == SCHOOL_NO
              && u.USER_TYPE == UserType.Student
              && string.IsNullOrEmpty(u.CLASS_NO) == false && u.SYEAR == SEYEAR).
              Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
            }
            else
            {
                ClassItems = db.HRMT01.Where(u => u.SCHOOL_NO == SCHOOL_NO
                 && u.USER_TYPE == UserType.Student
                 && string.IsNullOrEmpty(u.CLASS_NO) == false).
                 Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
            }

            return ClassItems;
        }
        static public List<SelectListItem> GetSYS_TABLE_Person_TYPEistData(string SCHOOL_NO,string BRE_NO, string DATA_CODE,ref ECOOL_DEVEntities db)
        {
            int IsSchoolset = 0;
            IsSchoolset = db.BDMT02_REF.Where(u => u.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu
            && u.BRE_NO == BRE_NO
            && u.DATA_CODE == DATA_CODE && u.SCHOOL_NO == SCHOOL_NO).Count();
            if (IsSchoolset > 0)
            {
                List<SelectListItem> PersonItems = new List<SelectListItem>();
                PersonItems.Add(new SelectListItem() { Text = "請選擇..", Value = ""});
                var PersonItemsterm= db.BDMT02_REF.Where(u => u.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu
                && u.BRE_NO == BRE_NO
                && u.DATA_CODE == DATA_CODE && u.SCHOOL_NO == SCHOOL_NO).
                Select(x => new SelectListItem { Text = x.CONTENT_TXT, Value = x.CONTENT_VAL }).Distinct().OrderBy(o => o.Value);
                PersonItems.AddRange(PersonItemsterm);

                return PersonItems;
            }
            else {
                List<SelectListItem> PersonItems = new List<SelectListItem>();
                PersonItems.Add(new SelectListItem() { Text = "請選擇..", Value = "" });
                var PersonItemsterm = db.BDMT02_REF.Where(u => u.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu
               && u.BRE_NO == BRE_NO
               && u.DATA_CODE == DATA_CODE && u.SCHOOL_NO == BDMT02_ENUM.DataType.DataTypeMenu).
               Select(x => new SelectListItem { Text = x.CONTENT_TXT, Value = x.CONTENT_VAL }).Distinct().OrderBy(o => o.Value);

                PersonItems.AddRange(PersonItemsterm);
                return PersonItems;
            }
            
        }
      
        /// <summary>
        /// 取得班級下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <returns></returns>
        static public IOrderedQueryable<SelectListItem> GetClassListData(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
   
            IOrderedQueryable<SelectListItem> ClassItems = db.HRMT01.Where(u => u.SCHOOL_NO == SCHOOL_NO
            && u.USER_TYPE == UserType.Student
            && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS != UserStaus.Invalid).
            Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
    
            return ClassItems;
        }
        static public List<SelectListItem> GetYearListData(string wSCHOOL_NO, string defaultSelectValue, ref ECOOL_DEVEntities db)
        {

            var TempSelect = db.HRMT01.Where(u => string.IsNullOrEmpty(u.CLASS_NO) == false && u.SYEAR != null
            && u.USER_TYPE == UserType.Student
            && u.USER_STATUS == UserStaus.Enabled);
            if (string.IsNullOrWhiteSpace(wSCHOOL_NO) == false && wSCHOOL_NO != SharedGlobal.ALL)
            {
                TempSelect = TempSelect.Where(a => a.SCHOOL_NO == wSCHOOL_NO);
            }
            var YEARItems = TempSelect.
      Select(x => new SelectListItem { Text = x.SYEAR.ToString(), Value = x.SYEAR.ToString() }).Distinct().OrderBy(x => x.Value);
            if (defaultSelectValue != null)
            {
                YEARItems = TempSelect.
              Select(x => new SelectListItem { Text = x.SYEAR.ToString(), Value = x.SYEAR.ToString() }).Distinct().OrderBy(x => x.Value);
            }
            List<SelectListItem> TempYEARItems = new List<SelectListItem>();
            TempYEARItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });
            TempYEARItems.AddRange(YEARItems);
            return TempYEARItems;
        }

        /// <summary>
        /// 取得班級下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO">學校</param>
        /// <param name="Grade">年級</param>
        /// <param name="defaultSelectValue">預設值</param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetGradeClassListData(string wSCHOOL_NO, string wGrade,byte? wSyear, string defaultSelectValue, ref ECOOL_DEVEntities db)
        {
            var TempSelect = db.HRMT01.Where(u => string.IsNullOrEmpty(u.CLASS_NO) == false
            && u.USER_TYPE == UserType.Student
            && u.USER_STATUS == UserStaus.Invalid &&u.SYEAR == wSyear  && u.GRADE==6);

            if (string.IsNullOrWhiteSpace(wSCHOOL_NO) == false && wSCHOOL_NO != SharedGlobal.ALL)
            {
                TempSelect = TempSelect.Where(a => a.SCHOOL_NO == wSCHOOL_NO);
            }

            if (string.IsNullOrWhiteSpace(wGrade) == false)
            {
                byte ThisGrade = byte.Parse(wGrade);
                TempSelect = TempSelect.Where(a => a.GRADE == ThisGrade);
            }
            var ClassItems = TempSelect.
            Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(x => x.Value);
            if (defaultSelectValue != null)
            {
                ClassItems = TempSelect.
              Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO, Selected = x.CLASS_NO == defaultSelectValue }).Distinct().OrderBy(x => x.Value);
            }

            List<SelectListItem> TempClassItems = new List<SelectListItem>();
            TempClassItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            TempClassItems.AddRange(ClassItems);

            return TempClassItems;
        }

        /// <summary>
        /// 取得班級下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO">學校</param>
        /// <param name="Grade">年級</param>
        /// <param name="defaultSelectValue">預設值</param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetClassListData(string wSCHOOL_NO, string wGrade, string defaultSelectValue, ref ECOOL_DEVEntities db)
        {
            var TempSelect = db.HRMT01.Where(u => string.IsNullOrEmpty(u.CLASS_NO) == false
            && u.USER_TYPE == UserType.Student
            && u.USER_STATUS == UserStaus.Enabled);

            if (string.IsNullOrWhiteSpace(wSCHOOL_NO) == false && wSCHOOL_NO != SharedGlobal.ALL)
            {
                TempSelect = TempSelect.Where(a => a.SCHOOL_NO == wSCHOOL_NO);
            }

            if (string.IsNullOrWhiteSpace(wGrade) == false)
            {
                byte ThisGrade = byte.Parse(wGrade);
                TempSelect = TempSelect.Where(a => a.GRADE == ThisGrade);
            }
            var ClassItems = TempSelect.
            Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(x => x.Value);
            if (defaultSelectValue != null)
            {
                ClassItems = TempSelect.
              Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO, Selected = x.CLASS_NO == defaultSelectValue }).Distinct().OrderBy(x => x.Value);
            }

            List<SelectListItem> TempClassItems = new List<SelectListItem>();
            TempClassItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            TempClassItems.AddRange(ClassItems);

            return TempClassItems;
        }

        static public List<SelectListItem> GetClassListData(string wSCHOOL_NO, string defaultSelectValue, ref ECOOL_DEVEntities db)
        {
            var TempSelect = db.HRMT01.Where(u => u.SCHOOL_NO == wSCHOOL_NO
            && string.IsNullOrEmpty(u.CLASS_NO) == false
            && u.USER_TYPE == UserType.Student
            && u.USER_STATUS != UserStaus.Invalid);

            var ClassItems = TempSelect.
            Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO, Selected = x.CLASS_NO == defaultSelectValue }).Distinct().OrderBy(x => x.Value);

            List<SelectListItem> TempClassItems = new List<SelectListItem>();
            TempClassItems.Add(new SelectListItem() { Text = "請選擇..", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            TempClassItems.AddRange(ClassItems);

            return TempClassItems;
        }

        /// <summary>
        /// 取得姓名/座號 下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        static public IQueryable<SelectListItem> GetUserNoClassListData(string SCHOOL_NO, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            IQueryable<SelectListItem> UserNoItems = null;

            if (SCHOOL_NO != SharedGlobal.ALL)
            {
                UserNoItems = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)))
              .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
              .Select(x => new SelectListItem { Text = x.NAME + "/" + CLASS_NO + "/" + x.SEAT_NO, Value = x.USER_NO });
            }
            else
            {
                UserNoItems = db.HRMT01.Where(a => a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)))
              .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
              .Select(x => new SelectListItem { Text = x.NAME + "/" + CLASS_NO + "/" + x.SEAT_NO, Value = x.USER_NO });
            }

            return UserNoItems;
        }
        static public Dictionary<string, List<string>> UserNoListDatestring(string SCHOOL_NO, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            Dictionary<string, List<string>> USerNOList = new Dictionary<string, List<string>>();
            List<string> ClassStr = new List<string>();
            List<string> USER_NOStr = new List<string>();
            ClassStr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)))
             .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).Select(x => x.CLASS_NO).Distinct().ToList();

            foreach (var ite in ClassStr)
            {
                USER_NOStr= db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO &&  a.CLASS_NO == ite  && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)))
             .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).Select(x => x.USER_NO).Distinct().ToList();

                USerNOList.Add(ite, USER_NOStr);
            }

            return USerNOList;
        }
        /// <summary>
        /// 取得姓名/座號 下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        static public IQueryable<SelectListItem> GetUserNoListData(string SCHOOL_NO, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            IQueryable<SelectListItem> UserNoItems = null;

            if (SCHOOL_NO != SharedGlobal.ALL)
            {
                UserNoItems = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)))
              .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
              .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO });
            }
            else
            {
                UserNoItems = db.HRMT01.Where(a => a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)))
              .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
              .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO });
            }

            return UserNoItems;
        }
        static public IQueryable<SelectListItem> GetUserNoListOneData(string SCHOOL_NO, string CLASS_NO,string IDNO, ref ECOOL_DEVEntities db)
        {
            IQueryable<SelectListItem> UserNoItems = null;

            if (SCHOOL_NO != SharedGlobal.ALL)
            {
                if (!string.IsNullOrEmpty(IDNO))
                {
                    UserNoItems = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student && a.IDNO== IDNO)
          .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
          .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO });
                }
                else {

                    UserNoItems = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student)
          .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
          .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO });
                }

            
            }
            else
            {
                if (!string.IsNullOrEmpty(IDNO))
                {
                    UserNoItems = db.HRMT01.Where(a => a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student && a.IDNO == IDNO)
.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
.Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO });


                }

                else {
                    UserNoItems = db.HRMT01.Where(a => a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student)
.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
.Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO });


                }

            }

            return UserNoItems;
        }
        /// <summary>
        /// 取得學號/座號 下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        static public IQueryable<SelectListItem> GetUserNoListNoData(string SCHOOL_NO, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            IQueryable<SelectListItem> UserNoItems = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == CLASS_NO && a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)))
                 .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
                 .Select(x => new SelectListItem { Text = x.SNAME + "/" + x.SEAT_NO, Value = x.USER_NO });

            return UserNoItems;
        }

        /// <summary>
        /// 取得姓名/座號 下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetUserNoListDataMust(string SCHOOL_NO, string CLASS_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
            List<SelectListItem> TempClassItems = new List<SelectListItem>();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                TempClassItems.Add(new SelectListItem() { Text = "請選擇學校..", Value = "", Selected = string.IsNullOrWhiteSpace(SCHOOL_NO) });
                return TempClassItems;
            }

            if (string.IsNullOrWhiteSpace(CLASS_NO))
            {
                TempClassItems.Add(new SelectListItem() { Text = "請選擇班級..", Value = "", Selected = string.IsNullOrWhiteSpace(CLASS_NO) });
                return TempClassItems;
            }

            TempClassItems.AddRange(GetUserNoListData(SCHOOL_NO, CLASS_NO, USER_NO, ref db));

            return TempClassItems;
        }

        /// <summary>
        /// 取得姓名/座號 下拉式選單 (家長)
        /// </summary>
        /// <param name="defaultSelectValue"></param>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public IQueryable<SelectListItem> GetUserNoListDataForP(string defaultSelectValue, UserProfile user, ECOOL_DEVEntities db = null)
        {
            var h06 = HRMT06.GetMyPanyStudent(user, db).Select(a => a.STUDENT_USER_NO).ToList();

            IQueryable<SelectListItem> UserNoItems = null;

            if (h06.Count > 0)
            {
                IQueryable<HRMT01> TempQData = db.HRMT01.Where(a => a.USER_TYPE == UserType.Student && a.SCHOOL_NO == user.SCHOOL_NO
                && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))
                && h06.Contains(a.USER_NO)
                ).AsQueryable();

                UserNoItems = TempQData.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
               .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO, Selected = x.USER_NO == defaultSelectValue });
            }

            return UserNoItems;
        }

        /// <summary>
        /// 取得姓名/座號 下拉式選單(轉學)
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        static public IQueryable<SelectListItem> GetUserNoListDataForSchool(string SCHOOL_NO, string CLASS_NO, string USER_NO, byte? SYEAR, ref ECOOL_DEVEntities db)
        {
            IQueryable<HRMT01> TempQData = db.HRMT01.Where(a => a.USER_TYPE == UserType.Student);

            if (string.IsNullOrEmpty(SCHOOL_NO) == false)
            {
                TempQData = TempQData.Where(a => a.SCHOOL_NO == SCHOOL_NO);
            }

            if (string.IsNullOrEmpty(CLASS_NO) == false)
            {
                TempQData = TempQData.Where(a => a.CLASS_NO == CLASS_NO);
            }

            if (string.IsNullOrEmpty(USER_NO) == false)
            {
                TempQData = TempQData.Where(a => a.USER_NO == USER_NO);
            }
            if (SYEAR != null)
            {
                TempQData = TempQData.Where(a => a.SYEAR == SYEAR);
            }
            IQueryable<SelectListItem> UserNoItems = TempQData.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
             .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO, Selected = x.USER_NO == USER_NO });

            return UserNoItems;
        }

        /// <summary>
        /// 取得姓名/座號 下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetUserNoListData(string SCHOOL_NO, string CLASS_NO, string USER_NO, ref ECOOL_DEVEntities db, bool IsPleaseSelect = false)
        {
            IQueryable<HRMT01> TempQData = db.HRMT01.Where(a => a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)));

            if (string.IsNullOrEmpty(SCHOOL_NO) == false)
            {
                TempQData = TempQData.Where(a => a.SCHOOL_NO == SCHOOL_NO);
            }

            if (string.IsNullOrEmpty(CLASS_NO) == false)
            {
                TempQData = TempQData.Where(a => a.CLASS_NO == CLASS_NO);
            }

            if (string.IsNullOrEmpty(USER_NO) == false)
            {
                TempQData = TempQData.Where(a => a.USER_NO == USER_NO);
            }

            List<SelectListItem> UserNoItems = TempQData.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
                 .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO, Selected = x.USER_NO == USER_NO }).ToListNoLock();

            if (IsPleaseSelect)
            {
                UserNoItems.Add(new SelectListItem { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(USER_NO) });
            }

            return UserNoItems;
        }

        /// <summary>
        /// 取得學號/座號 下拉式選單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        static public IQueryable<SelectListItem> GetUserNoListNoData(string SCHOOL_NO, string CLASS_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
            IQueryable<HRMT01> TempQData = db.HRMT01.Where(a => a.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)));

            if (string.IsNullOrEmpty(SCHOOL_NO) == false)
            {
                TempQData = TempQData.Where(a => a.SCHOOL_NO == SCHOOL_NO);
            }

            if (string.IsNullOrEmpty(CLASS_NO) == false)
            {
                TempQData = TempQData.Where(a => a.CLASS_NO == CLASS_NO);
            }

            if (string.IsNullOrEmpty(USER_NO) == false)
            {
                TempQData = TempQData.Where(a => a.USER_NO == USER_NO);
            }

            IQueryable<SelectListItem> UserNoItems = TempQData.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
                 .Select(x => new SelectListItem { Text = x.SNAME + "/" + x.SEAT_NO, Value = x.USER_NO, Selected = x.USER_NO == USER_NO });

            return UserNoItems;
        }

        /// <summary>
        /// 取得學年 下拉式選單
        /// </summary>
        /// <param name="defaultSelectValue"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetSYearsItems(string defaultSelectValue, ref ECOOL_DEVEntities db)
        {
            List<SelectListItem> SYEARItems = new List<SelectListItem>();
            SYEARItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            Byte MinYear = db.HRMT01.Where(a => a.USER_TYPE == UserType.Student).Min(a => a.SYEAR).Value;
            Byte MaxYear = db.HRMT01.Where(a => a.USER_TYPE == UserType.Student).Max(a => a.SYEAR).Value;
            for (Byte i = MinYear; i <= MaxYear; i++)
            {
                SYEARItems.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString(), Selected = i.ToString() == defaultSelectValue });
            }

            return SYEARItems;
        }

        static public List<SelectListItem> GetUserNoSelectItems(string SCHOOL_NO, string CLASS_NO, List<string> defaultSelectValue, ref ECOOL_DEVEntities db)
        {
            var hRMT01s = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.CLASS_NO == CLASS_NO && x.USER_STATUS != UserStaus.Invalid && x.USER_TYPE == UserType.Student).ToListNoLock()
                 .Select(x => new SelectListItem { Text = $@"{x.SEAT_NO} {x.SNAME}", Value = x.USER_NO }).ToList();

            List<SelectListItem> UserNoItems = new List<SelectListItem>();

            foreach (var Hr in hRMT01s)
            {
                if (defaultSelectValue != null)
                {
                    if (defaultSelectValue.Where(a => a == Hr.Value).Any())
                    {
                        UserNoItems.Add(new SelectListItem() { Text = Hr.Text, Value = Hr.Value, Selected = true });
                    }
                    else
                    {
                        UserNoItems.Add(new SelectListItem() { Text = Hr.Text, Value = Hr.Value, Selected = false });
                    }
                }
                else
                {
                    UserNoItems.Add(new SelectListItem() { Text = Hr.Text, Value = Hr.Value, Selected = false });
                }
            }

            return UserNoItems;
        }

        /// <summary>
        /// 取出班導姓名
        /// </summary>
        /// <returns></returns>

        static public string GetTeacherName(string SCHOOL_NO, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            var TEACHER_NO = db.HRMT03.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.CLASS_NO == CLASS_NO).Select(x => x.TEACHER_NO).NoLock(x => x.FirstOrDefault());

            if (!string.IsNullOrWhiteSpace(TEACHER_NO))
            {
                return GetHrmt01Name(SCHOOL_NO, TEACHER_NO, ref db);
            }
            else
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 取出姓名
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public string GetHrmt01Name(string SCHOOL_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
            return db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.USER_NO == USER_NO).Select(x => x.NAME).NoLock(x => x.FirstOrDefault());
        }

        /// <summary>
        /// 驗証卡號
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public HRMT01 CheckCardNoToHrmt01(string SCHOOL_NO, string CARD_NO, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                Message = "無學校代碼";
                return null;
            }

            if (string.IsNullOrWhiteSpace(CARD_NO))
            {
                Message = "無卡號值";
                return null;
            }

            var hRMT01 = (from b in db.HRMT01
                          join c in db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                          where (
                                         (b.CARD_NO == CARD_NO)
                                      || (b.SCHOOL_NO + b.CLASS_NO + b.SEAT_NO == SCHOOL_NO + CARD_NO)
                                      || (b.SCHOOL_NO + b.USER_NO == SCHOOL_NO + CARD_NO)
                                 )
                          && b.USER_TYPE == UserType.Student
                          && (!UserStaus.NGUserStausList.Contains(b.USER_STATUS))
                          select b).NoLock(x => x.FirstOrDefault());

            if (hRMT01 == null)
            {
                Message = "此數位學生證對應不到學生資料";
                return null;
            }
            else
            {
                return hRMT01;
            }
        }
    }
}