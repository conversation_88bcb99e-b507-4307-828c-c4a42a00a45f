/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Main/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Main={directory:"Main/Regular",family:"NeoEulerMathJax_Main",testString:"\u00A0\u00A7\u00A8\u00AC\u00AF\u00B1\u00B4\u00B5\u00B7\u00D7\u00F7\u0131\u0237\u02C6\u02C7",1:[0,0,333,0,0],32:[0,0,333,0,0],33:[690,11,295,90,204],34:[695,-432,214,9,196],35:[690,0,500,4,493],36:[708,107,557,84,480],37:[708,20,840,58,789],38:[698,11,737,49,734],39:[695,-436,212,68,134],40:[738,187,388,113,301],41:[736,189,388,86,276],42:[692,-448,277,28,239],43:[586,74,755,46,709],44:[138,210,277,67,214],45:[276,-236,544,40,504],46:[119,15,277,77,211],47:[720,192,501,39,463],48:[704,14,499,34,460],49:[712,5,499,92,314],50:[708,-2,499,12,472],51:[702,17,499,18,446],52:[704,5,499,-1,473],53:[687,11,499,12,448],54:[700,13,499,45,471],55:[694,8,499,49,494],56:[706,10,499,40,461],57:[702,9,499,40,462],58:[455,12,216,50,164],59:[457,190,216,48,179],60:[531,36,756,59,680],61:[369,-132,756,54,722],62:[531,36,756,76,698],63:[693,11,362,45,358],64:[688,31,744,26,718],65:[680,10,770,25,743],66:[686,1,655,50,574],67:[699,15,714,87,672],68:[686,-2,828,51,738],69:[688,0,604,46,559],70:[690,2,499,39,467],71:[699,17,765,90,676],72:[690,2,783,52,744],73:[683,1,394,78,348],74:[690,234,402,50,351],75:[683,7,668,49,632],76:[690,5,559,50,534],77:[690,6,1044,79,1010],78:[694,5,829,49,710],79:[700,18,803,72,726],80:[679,6,576,55,550],81:[698,235,828,85,774],82:[679,8,609,54,582],83:[702,15,557,45,483],84:[697,10,492,26,575],85:[694,9,774,34,746],86:[698,16,646,23,729],87:[690,11,986,13,1060],88:[688,11,666,27,636],89:[693,11,555,23,615],90:[695,12,666,46,640],91:[741,125,277,120,279],92:[722,192,501,39,463],93:[741,125,277,-1,158],94:[735,-452,499,0,495],95:[-60,100,450,-7,457],96:[677,-506,201,0,201],97:[466,12,609,87,596],98:[684,10,588,27,523],99:[475,19,486,49,446],100:[684,19,603,86,597],101:[478,12,499,69,463],102:[669,12,419,36,422],103:[465,233,568,83,494],104:[681,10,621,27,637],105:[683,8,360,14,357],106:[683,231,331,-1,226],107:[686,7,555,27,565],108:[686,11,365,41,360],109:[471,18,915,29,922],110:[471,10,664,5,656],111:[465,14,563,56,497],112:[470,237,589,-12,518],113:[504,243,605,88,502],114:[473,3,432,9,415],115:[466,17,455,52,387],116:[611,9,416,17,399],117:[470,10,642,-1,655],118:[469,11,495,-21,466],119:[469,8,812,-13,782],120:[464,12,526,2,512],121:[468,233,593,-17,491],122:[462,27,470,12,479],123:[730,178,320,-3,292],124:[738,167,213,86,126],125:[733,175,320,23,318],126:[347,-178,551,22,530],160:[0,0,333,0,0],167:[701,107,515,56,451],168:[642,-542,299,0,299],172:[401,-205,773,30,709],175:[619,-566,312,0,312],177:[586,74,755,46,710],180:[677,-506,201,0,201],181:[466,199,617,96,635],183:[379,-245,277,72,206],215:[505,-7,755,133,623],247:[537,36,777,56,720],305:[471,8,333,-2,341],567:[468,231,331,-1,223],710:[735,-452,499,0,495],711:[735,-452,495,0,495],728:[671,-513,282,0,282],729:[642,-542,100,0,100],730:[692,-492,200,0,200],732:[640,-540,321,0,322],768:[677,-506,0,-385,-184],769:[677,-506,0,-315,-114],770:[735,-452,499,0,495],771:[640,-540,0,-409,-88],772:[619,-566,0,-405,-93],774:[671,-513,0,-390,-108],775:[642,-542,0,-300,-200],776:[642,-542,0,-399,-100],778:[692,-492,0,-349,-149],779:[683,-502,0,-346,-3],780:[735,-452,499,0,495],824:[720,192,0,39,463],913:[680,10,770,25,743],914:[686,1,655,50,574],915:[697,7,428,74,507],916:[696,4,713,30,689],917:[688,0,604,46,559],918:[694,12,666,46,640],919:[690,2,783,52,744],920:[701,12,757,38,714],921:[683,1,394,78,348],922:[683,7,668,49,632],923:[698,7,770,28,771],924:[690,6,1044,79,1010],925:[694,5,829,49,710],926:[695,6,596,27,569],927:[701,12,803,66,742],928:[690,14,722,26,693],929:[679,6,576,55,550],931:[695,-2,646,49,600],932:[697,10,492,26,575],933:[697,11,716,27,682],934:[721,33,833,73,748],935:[688,11,666,27,636],936:[694,1,703,-28,698],937:[689,2,875,25,844],945:[468,20,658,84,673],946:[698,202,662,113,569],947:[470,198,608,-25,582],948:[694,9,501,56,438],949:[471,15,486,78,430],950:[695,136,512,84,491],951:[466,199,560,-32,479],952:[695,11,554,59,486],953:[474,9,334,101,332],954:[472,4,555,112,516],955:[690,11,541,21,510],956:[466,199,617,96,635],957:[471,8,599,15,561],958:[694,137,553,74,545],959:[465,14,563,56,497],960:[488,5,609,21,628],961:[477,189,548,90,499],963:[476,5,605,82,608],964:[484,9,513,15,519],965:[472,12,587,-12,519],966:[467,197,763,91,685],967:[466,197,576,-18,564],968:[695,189,754,-7,684],969:[472,13,851,67,781],977:[698,14,552,-16,522],981:[694,192,727,66,659],982:[541,12,875,74,790],1013:[471,11,550,88,490],8214:[738,167,392,86,306],8216:[709,-406,214,45,167],8217:[695,-395,214,44,163],8230:[119,15,768,77,691],8242:[782,-422,257,48,211],8243:[782,-422,490,48,404],8244:[782,-422,724,48,596],8245:[782,-422,241,30,193],8260:[720,192,501,39,463],8279:[782,-422,958,48,789],8407:[750,-479,287,0,418],8463:[681,10,621,20,637],8465:[686,27,554,28,533],8467:[704,9,388,30,355],8472:[472,196,604,60,566],8476:[686,27,828,27,826],8487:[689,2,875,25,844],8501:[689,-1,774,78,689],8592:[500,0,1000,57,945],8593:[693,194,500,28,472],8594:[500,0,1000,56,944],8595:[674,193,500,28,472],8596:[500,0,1000,57,944],8597:[771,271,500,28,472],8598:[767,193,1000,-17,943],8599:[767,193,1000,58,1018],8600:[694,267,1000,57,1018],8601:[694,267,1000,-17,944],8614:[500,0,1000,56,944],8617:[554,0,1000,56,944],8618:[554,0,1000,56,944],8636:[500,-230,1000,56,945],8637:[270,0,1000,56,945],8638:[693,194,298,28,270],8639:[693,194,298,28,270],8640:[500,-230,1000,56,945],8641:[270,0,1000,56,945],8642:[693,194,298,28,270],8643:[693,194,298,28,270],8651:[599,98,999,55,944],8652:[599,98,999,55,944],8656:[598,98,1000,55,922],8657:[694,174,667,12,652],8658:[598,98,1000,76,943],8659:[674,194,667,12,652],8660:[598,98,1000,33,965],8661:[772,272,667,12,652],8704:[681,9,555,1,552],8706:[699,7,560,79,485],8707:[694,0,555,75,498],8708:[800,112,555,65,498],8709:[720,192,742,55,687],8711:[696,4,713,30,689],8712:[541,41,666,83,561],8713:[720,192,666,83,561],8715:[541,41,666,103,581],8722:[276,-236,756,46,710],8723:[586,74,755,46,710],8725:[720,192,0,39,463],8726:[722,192,501,39,463],8727:[514,-26,482,30,452],8728:[444,-56,500,55,444],8729:[444,-56,500,55,444],8730:[988,1,833,70,849],8733:[442,11,815,56,760],8734:[442,11,1000,56,945],8739:[698,97,213,86,126],8741:[738,167,392,86,306],8743:[714,4,775,11,768],8744:[688,12,775,6,756],8745:[598,2,666,55,609],8746:[578,22,666,55,609],8747:[950,161,556,49,507],8756:[455,12,569,50,517],8757:[455,12,569,50,517],8764:[347,-178,551,22,530],8765:[347,-178,551,22,530],8768:[422,77,243,54,189],8769:[454,-32,551,22,530],8770:[397,-103,597,54,563],8771:[396,-101,597,54,563],8773:[597,-102,597,54,563],8774:[597,8,597,54,563],8776:[427,-108,551,22,530],8778:[546,-101,597,54,563],8800:[720,192,756,54,722],8801:[465,-33,830,81,749],8804:[648,150,807,79,700],8805:[647,149,807,102,724],8806:[800,0,756,54,722],8807:[800,0,756,54,722],8808:[800,93,756,54,722],8809:[800,93,756,54,722],8814:[720,192,756,59,680],8815:[720,192,756,76,698],8816:[720,192,807,79,700],8817:[720,192,807,102,724],8818:[663,52,807,79,716],8819:[663,52,807,88,725],8822:[766,119,807,71,716],8823:[764,120,807,72,716],8834:[541,41,777,83,673],8835:[541,41,777,103,693],8838:[636,143,777,83,673],8839:[636,143,777,103,693],8840:[720,192,777,83,673],8841:[720,192,777,103,693],8842:[636,222,777,83,673],8843:[636,222,777,103,693],8846:[578,22,665,55,609],8849:[636,143,1000,94,693],8850:[636,143,1000,83,681],8853:[583,83,777,55,722],8854:[583,83,777,55,722],8855:[583,83,777,55,722],8856:[583,83,777,55,722],8857:[583,83,777,55,722],8866:[694,0,673,55,618],8867:[694,0,673,55,618],8868:[684,0,875,55,820],8869:[684,0,875,55,820],8900:[496,-4,500,3,495],8901:[379,-245,277,72,206],8943:[319,-185,768,77,691],8945:[533,-60,627,76,550],8968:[980,0,511,174,401],8969:[980,0,511,41,269],8970:[980,0,511,174,401],8971:[980,0,511,41,269],9180:[770,-582,1037,56,981],9181:[-32,222,1037,56,981],9182:[824,-528,1020,56,964],9183:[26,268,1020,56,964],10216:[737,237,388,107,330],10217:[737,237,388,57,280],10229:[500,0,1610,55,1553],10230:[500,0,1610,55,1553],10231:[500,0,1700,57,1644],10232:[598,98,1700,55,1622],10233:[598,98,1700,55,1622],10234:[598,98,1700,33,1665],10236:[500,0,1690,56,1634]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Main"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Regular/Main.js"]);
