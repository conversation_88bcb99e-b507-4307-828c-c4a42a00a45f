/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Italic/Other.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_SansSerif-italic"],{160:[0,0,250,0,0],305:[444,0,239,74,258],567:[444,204,267,-96,286],915:[691,0,542,87,646],916:[694,0,833,42,790],920:[715,22,778,119,804],923:[694,0,611,28,582],926:[688,0,667,42,765],928:[691,0,708,86,768],931:[694,0,722,55,813],933:[716,0,778,173,843],934:[694,0,722,124,743],936:[694,0,778,171,854],937:[716,0,722,44,769],8211:[312,-236,500,50,565],8212:[312,-236,1000,50,1065],8216:[694,-471,278,190,336],8217:[694,-471,278,190,335],8220:[694,-471,500,274,614],8221:[694,-471,500,133,472]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Italic/Other.js");
