﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'eo', {
	access: 'Atingi skriptojn',
	accessAlways: 'Ĉiam',
	accessNever: 'Neniam',
	accessSameDomain: 'Sama domajno',
	alignAbsBottom: 'Absoluta Malsupro',
	alignAbsMiddle: 'Absoluta Centro',
	alignBaseline: 'TekstoMalsupro',
	alignTextTop: 'TekstoSupro',
	bgcolor: 'Fona Koloro',
	chkFull: 'Permesi tutekranon',
	chkLoop: 'Iteracio',
	chkMenu: 'Ebligi flaŝmenuon',
	chkPlay: 'Aŭtomata legado',
	flashvars: 'Variabloj por Flaŝo',
	hSpace: 'Horizontala Spaco',
	properties: 'Flaŝatributoj',
	propertiesTab: 'Atributoj',
	quality: '<PERSON><PERSON><PERSON>',
	qualityAutoHigh: 'Aŭtomate alta',
	qualityAutoLow: 'Aŭtomate malalta',
	qualityBest: 'Plej bona',
	qualityHigh: 'Alta',
	qualityLow: 'Malalta',
	qualityMedium: 'Meza',
	scale: 'Skalo',
	scaleAll: 'Montri ĉion',
	scaleFit: 'Origina grando',
	scaleNoBorder: 'Neniu bordero',
	title: 'Flaŝatributoj',
	vSpace: 'Vertikala Spaco',
	validateHSpace: 'Horizontala Spaco devas esti nombro.',
	validateSrc: 'Bonvolu entajpi la retadreson (URL)',
	validateVSpace: 'Vertikala Spaco devas esti nombro.',
	windowMode: 'Fenestra reĝimo',
	windowModeOpaque: 'Opaka',
	windowModeTransparent: 'Travidebla',
	windowModeWindow: 'Fenestro'
} );
