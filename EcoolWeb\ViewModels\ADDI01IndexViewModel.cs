﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using com.ecool.service;
using ECOOL_APP.EF;
using MvcPaging;

namespace EcoolWeb.Models
{
    public class ADDI01IndexViewModel
    {
        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        public string LoginYN { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某座號
        /// </summary>
        public string whereSeat_NO { get; set; }
        public string Context { get; set; }
        /// <summary>
        /// 狀態
        /// </summary>
        public string whereWritingStatus { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示被推薦文章
        /// </summary>
        public bool whereShareYN { get; set; }

        /// <summary>
        /// 只顯示被鼓勵文章
        /// </summary>
        public bool whereComment { get; set; }

        /// <summary>
        /// 只顯示有幫助的鼓勵文章
        /// </summary>
        public bool whereCommentCash { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設20筆
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// BackAction 網頁用 ，前一頁網址
        /// </summary>
        public string BackAction { get; set; }

        /// <summary>
        /// 清除條件
        /// </summary>
        public bool doClear { get; set; }

        /// <summary>
        /// 圖片刪除
        /// </summary>
        //public string DeleteImage { get; set; }

        /// <summary>
        /// 圖片排序
        /// </summary>
        public string[] ImageOrders { get; set; }

        /// <summary>
        /// 圖片排序
        /// </summary>
        public string[] OtherFilesOrders { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ADDV03> ADDV03List { get; set; }

        public ADDI01IndexViewModel()
        {
            PageSize = 20;
            BackAction = "Index";
        }

        public void ClearWhere()
        {
            this.whereComment = false;
            this.whereCommentCash = false;
            this.whereKeyword = null;
            this.whereShareYN = false;
            this.whereUserNo = null;
            this.whereWritingStatus = null;
            this.doClear = false;

            this.OrdercColumn = null;
        }
    }
}