// Modern jQuery code for ADDI01 OrderList page
$(document).ready(function() {
    // 全局變數
    const targetFormID = '#OrderList';

    // 日期選擇器模組
    const datepickerHandler = {
        init: function() {
            this.initDatepicker();
            window.initDatepicker = this.initDatepicker.bind(this);
        },

        initDatepicker: function() {
            const opt = {
                showMonthAfterYear: true,
                format: moment().format('YYYY-MM-DD'),
                showSecond: true,
                showButtonPanel: true,
                showTime: true,
                beforeShow: function() {
                    setTimeout(function() {
                        $('#ui-datepicker-div').css("z-index", 15);
                    }, 100);
                },
                onSelect: function(dateText, inst) {
                    $('#' + inst.id).attr('value', dateText);
                }
            };

            // 使用全局配置的欄位ID
            if (window.ADDI01_FIELD_IDS) {
                $("#" + window.ADDI01_FIELD_IDS.whereSTART_CRE_DATE).datetimepicker(opt);
                $("#" + window.ADDI01_FIELD_IDS.whereEND_CRE_DATE).datetimepicker(opt);
            }
        }
    };

    // Excel匯出模組
    const excelExporter = {
        init: function() {
            window.exportExcel = this.exportToExcel.bind(this);
            window.ButtonExcel = this.exportWithBlob.bind(this);
        },

        exportToExcel: function() {
            const $form = $(targetFormID);
            $form.attr("enctype", "multipart/form-data");
            $form.attr("action", window.ADDI01_URLS.exportExcel);
            $form.submit();
        },

        exportWithBlob: function() {
            // 設置匯出標記
            if (window.ADDI01_FIELD_IDS) {
                $('#' + window.ADDI01_FIELD_IDS.IsToExcel).val(true);
            }

            const $form = $(targetFormID);
            $form.attr('action', window.ADDI01_URLS.orderList);
            $form.attr('target', '_blank').submit().removeAttr('target');

            // 使用Blob匯出
            const blob = new Blob([document.getElementById('tbData').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });

            const strFile = "Report.xls";
            if (typeof saveAs === 'function') {
                saveAs(blob, strFile);
            }

            // 重置匯出標記
            if (window.ADDI01_FIELD_IDS) {
                $('#' + window.ADDI01_FIELD_IDS.IsToExcel).val(false);
            }

            return false;
        }
    };

    // 列印功能模組
    const printHandler = {
        init: function() {
            window.PrintBooK = this.printBook.bind(this);
            window.btnSend_onclick = this.handleFormSubmit.bind(this);
        },

        printBook: function() {
            // 設置列印標記
            if (window.ADDI01_FIELD_IDS) {
                $('#' + window.ADDI01_FIELD_IDS.IsPrint).val(true);
            }

            const $form = $(targetFormID);
            $form.attr('action', window.ADDI01_URLS.orderList);
            $form.attr('target', '_blank').submit().removeAttr('target');

            // 重置列印標記
            if (window.ADDI01_FIELD_IDS) {
                $('#' + window.ADDI01_FIELD_IDS.IsPrint).val(false);
            }
        },

        handleFormSubmit: function() {
            const form = document.OrderList;
            form.enctype = "multipart/form-data";
            form.action = "../ADDI01/OrderList";
            form.submit();
        }
    };

    // 分頁處理模組
    const paginationHandler = {
        init: function() {
            window.FunPageProc = this.processPage.bind(this);
        },

        processPage: function(page) {
            const token = $('input[name="__RequestVerificationToken"]').val();
            if ($(targetFormID).length > 0) {
                $('#Page').val(page);
                $(targetFormID).submit();
            }
        }
    };

    // 排序功能模組
    const sortHandler = {
        init: function() {
            window.doSort = this.handleSort.bind(this);
        },

        handleSort: function(sortColumn) {
            $("#OrdercColumn").val(sortColumn);
            paginationHandler.processPage(1);
        }
    };

    // 月排行榜切換模組
    const monthTopHandler = {
        init: function() {
            window.doMonthTop = this.handleMonthTop.bind(this);
        },

        handleMonthTop: function(value) {
            if (window.ADDI01_FIELD_IDS) {
                $("#" + window.ADDI01_FIELD_IDS.WhereIsMonthTop).val(value);
            }
            $("#Sdate").attr("hidden", "hidden");
            paginationHandler.processPage(1);
        }
    };

    // 清除搜尋條件模組
    const searchClearer = {
        init: function() {
            window.todoClear = this.clearSearch.bind(this);
        },

        clearSearch: function() {
            // 重設表單欄位
            $("#OrderList").find(":input:not([name='__RequestVerificationToken']),:selected").each(function(i) {
                const type = $(this).attr('type');
                const isReadonly = $(this).attr('readonly');
                const tag = this.tagName.toLowerCase();

                if (isReadonly == false || isReadonly == undefined) {
                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        } else {
                            this.checked = false;
                        }
                    } else if (tag == 'select') {
                        this.selectedIndex = 0;
                    } else if (type == 'text' || type == 'hidden' || type == 'password' || 
                              type == 'textarea' || type == 'datetime') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    };

    // 頁面載入處理模組
    const pageLoadHandler = {
        init: function() {
            this.handlePageLoad();
        },

        handlePageLoad: function() {
            // 檢查是否需要自動列印
            $(window).on('load', function() {
                if (window.ADDI01_FIELD_IDS) {
                    const isPrint = $('#' + window.ADDI01_FIELD_IDS.IsPrint).val();
                    const isToExcel = $('#' + window.ADDI01_FIELD_IDS.IsToExcel).val();
                    
                    if (isPrint == "true" && isToExcel != "true") {
                        window.print();
                    }
                }
            });
        }
    };

    // 初始化所有模組
    datepickerHandler.init();
    excelExporter.init();
    printHandler.init();
    paginationHandler.init();
    sortHandler.init();
    monthTopHandler.init();
    searchClearer.init();
    pageLoadHandler.init();

    // 設置全局 URL 和欄位ID 配置（需要在 CSHTML 中定義）
    window.ADDI01_URLS = window.ADDI01_URLS || {};
    window.ADDI01_FIELD_IDS = window.ADDI01_FIELD_IDS || {};
});
