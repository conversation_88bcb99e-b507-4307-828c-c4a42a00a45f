﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'pl', {
	armenian: 'Numerowanie armeńskie',
	bulletedTitle: 'Właściwości list wypunktowanych',
	circle: 'Koło',
	decimal: 'Liczby (1, 2, 3 itd.)',
	decimalLeadingZero: 'Liczby z początkowym zerem (01, 02, 03 itd.)',
	disc: 'Okrąg',
	georgian: 'Numerowanie gruzińskie (an, ban, gan itd.)',
	lowerAlpha: 'Małe litery (a, b, c, d, e itd.)',
	lowerGreek: 'Małe litery greckie (alpha, beta, gamma itd.)',
	lowerRoman: 'Małe cyfry rzymskie (i, ii, iii, iv, v itd.)',
	none: 'Brak',
	notset: '<nie ustawiono>',
	numberedTitle: 'Właściwości list numerowanych',
	square: 'Kwadrat',
	start: 'Początek',
	type: 'Typ punktora',
	upperAlpha: 'Du<PERSON>e litery (A, B, C, D, E itd.)',
	upperRoman: 'Duże cyfry rzymskie (I, II, III, IV, V itd.)',
	validateStartNumber: 'Listę musi rozpoczynać liczba całkowita.'
} );
