﻿@using com.ecool.service
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    bool IsTeacherEdit = System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck((string)ViewBag.BRE_NO, "TeacherEdit");

}

<div class="form-group cScreen ">
    @if (ViewBag.ISTASKLIST != "True")
    {
        <a role="button" href='@Url.Action("Index", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "Index" ? "active" : "")">
            運動撲滿說明
        </a>
        <a role="button" href='@Url.Action("RunCountLog", "ADDI11")' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "RunCountLog" ? "active" : "")">
            全校跑步里程一覽
        </a>
        <a role="button" href='@Url.Action("OrderList", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction == "OrderList" ? "active" : "")">
            跑步排行榜
        </a>
        <a role="button" href='@Url.Action("RunCountLogInfo", "ADDI11")' class="btn btn-sm btn-sys @(ViewBag.NowAction == "RunCountLogInfo" ? "active" : "")">
            班級跑步總和排行
        </a>
    }
    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin && ViewBag.ISTASKLIST != "True")
        {
            if (ViewBag.LookSchoolRunPermission)
            {
        <a role="button" href='@Url.Action("TotalRunLog",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="TotalRunLog" ? "active":"")">
            全校跑步情形
        </a>
            }
            if (ViewBag.LookClassRunPermission)
            {
                <a role="button" href='@Url.Action("TotalClassRunLog",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="TotalClassRunLog" ? "active":"")">
                    本班跑步情形
                </a>
            }
        }

        if (ViewBag.ISTASKLIST != "True")
        {
            <a role="button" href='@Url.Action("RunMap",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="RunMap" ? "active":"")">
                我的跑步地圖
            </a>

            <a role="button" href='@Url.Action("MyRunLog",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="MyRunLog" ? "active":"")">
                我的跑步紀錄
            </a>
        }

    }

    @if (user != null)
    {

        if (user.USER_TYPE == UserType.Student && !user.USER_NO.Contains("run"))
        {
            <a role="button" href='@Url.Action("MyFriendsRunLog",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="MyFriendsRunLog" ? "active":"")">
                觀看好友
            </a>

            <a role="button" href='@Url.Action("SecretMission",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="SecretMission" ? "active":"")">
                加點任務 <span class="badge">@ViewBag.SecretMissionCount</span>
            </a>

        }

        else if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin)
        {

            <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
                新增學生跑步量
            </a>

            //if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == "A" || user.ROLE_LEVEL == (decimal)4.00)
            if (IsTeacherEdit)
            {

                <a role="button" href='@Url.Action("TeacherEdit",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="TeacherEdit" ? "active":"")">
                    新增老師跑步量
                </a>
            }

            if (ViewBag.ISTASKLIST != "True")
            {
                if (ViewBag.CanPrintRunForm)
                {
                    <a role="button" href='@Url.Action("PrintRunForm", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "PrintRunForm" ? "active" : "")">
                        列印跑步總表
                    </a>
                }


                <a role="button" href='@Url.Action("ExportRunExcel", "ZZZI09_HIS")' class="btn btn-sm btn-sys @(ViewBag.NowAction=="ExportRunExcel" ? "active":"")">
                    列印班級名冊
                </a>

                <a role="button" href='@Url.Action("ADDRunIndex",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-info btn-sys-style btn-sys-margin @(ViewBag.NowAction=="ADDRunIndex" ? "active":"")">
                    感應新增跑步量
                </a>
                var STR=  PermissionService.GetPermission_Use_YN("ADDI11", "LogRunIndexDetail", user.SCHOOL_NO, user.USER_NO);
                if (STR == "Y") { 
                <a role="button" href='@Url.Action("LogRunIndexDetail",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-info btn-sys-style btn-sys-margin @(ViewBag.NowAction=="LogRunIndexDetail" ? "active":"")">
                    感應圈數紀錄
                </a>}
            }
        }

        if (user.USER_NO.Contains("run") /*運動撲滿小幫手*/)
        {
            <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
                新增跑步量
            </a>
            if (ViewBag.ISTASKLIST != "True")
            {
                <a role="button" href='@Url.Action("ExportRunExcel", "ZZZI09_HIS")' class="btn btn-sm btn-sys @(ViewBag.NowAction=="ExportRunExcel" ? "active":"")">
                    跑步名單列印
                </a>
            }
        }
    }
</div>