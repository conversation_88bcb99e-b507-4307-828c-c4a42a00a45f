﻿using com.ecool.service;
using Dapper;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 我的護照
    /// </summary>
    [SessionExpire]
    public class CER002Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "CER002";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CERI01Service eRI01Service = new CERI01Service();
        private CERI02Service cERI02Service = new CERI02Service();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

     //   private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CER002Service Service = new CER002Service();

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Index()
        {
            this.Shared();
            return View();
        }

        [CheckPermissionSeeion(CheckACTION_ID = "IndexTecher")] //檢查權限
        public ActionResult IndexTecher()
        {
            Bre_Name = "查詢學生護照(在校生)";
            this.Shared();
            return View();
        }
        [CheckPermissionSeeion(CheckACTION_ID = "IndexGradeTecher")] //檢查權限
        public ActionResult IndexGradeTecher()
        {
            Bre_Name = "查詢學生護照(畢業生)";
            this.Shared();
            return View();
        }

        [CheckPermissionSeeion(CheckACTION_ID = "IndexTecher")] //檢查權限
        public ActionResult StudentIndex(string WhereUser )
        {

            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            this.Shared();
            CER002IndexViewModel model = new CER002IndexViewModel();
            ViewBag.userno = WhereUser;
         
          
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db2, "全部");
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db2, "全部");
            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "IndexGradeTecher")] //檢查權限
        public ActionResult StudentGradeInde(string WhereUser)
        {

            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            this.Shared();
            CER002IndexViewModel model = new CER002IndexViewModel();
            ViewBag.userno = WhereUser;


            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db2, "全部");
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db2, "全部");
            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "IndexTecher")] //檢查權限
        public ActionResult _StudentPageContent(CER002IndexViewModel temo)
        {
            this.Shared();
          if(temo==null)   temo = new CER002IndexViewModel();
          
        
                if (!string.IsNullOrEmpty(temo.WhereACCREDITATION_NAME)) {
                temo.WhereACCREDITATION_NAME = temo.WhereACCREDITATION_NAME;
            }
            if (!string.IsNullOrEmpty(temo.WhereACCREDITATION_TYPE)) {

                temo.WhereACCREDITATION_TYPE = temo.WhereACCREDITATION_TYPE;

            }

            if (temo.WhereGRADE_SEMESTER_TYPE != null) {

                temo.WhereGRADE_SEMESTER_TYPE =(byte)temo.WhereGRADE_SEMESTER_TYPE;
            }


            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            HRMT01 hrtm= db2.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == temo.WhereUser && x.USER_STATUS == 1).FirstOrDefault();
            temo = Service.GetListData(temo, hrtm?.SCHOOL_NO, hrtm?.USER_NO, (byte)hrtm?.GRADE, ref db2);
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(temo?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db2, "全部");
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(temo.WhereACCREDITATION_NAME, SCHOOL_NO, ref db2, "全部");
            return PartialView(temo);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "IndexGradeTecher")] //檢查權限
        public ActionResult _StudentGradePageContent(CER002IndexViewModel temo)
        {
            this.Shared();
            if (temo == null) temo = new CER002IndexViewModel();


            if (!string.IsNullOrEmpty(temo.WhereACCREDITATION_NAME))
            {
                temo.WhereACCREDITATION_NAME = temo.WhereACCREDITATION_NAME;
            }
            if (!string.IsNullOrEmpty(temo.WhereACCREDITATION_TYPE))
            {

                temo.WhereACCREDITATION_TYPE = temo.WhereACCREDITATION_TYPE;

            }

            if (temo.WhereGRADE_SEMESTER_TYPE != null)
            {

                temo.WhereGRADE_SEMESTER_TYPE = (byte)temo.WhereGRADE_SEMESTER_TYPE;
            }


            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            HRMT01 hrtm = db2.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == temo.WhereUser ).FirstOrDefault();
            temo = Service.GetListData(temo, hrtm?.SCHOOL_NO, hrtm?.USER_NO, (byte)hrtm?.GRADE, ref db2);
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(temo?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db2, "全部");
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(temo.WhereACCREDITATION_NAME, SCHOOL_NO, ref db2, "全部");
            return PartialView(temo);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(CER002IndexViewModel model)
        {
            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            this.Shared();

            if (model == null) model = new CER002IndexViewModel();

            model = Service.GetListData(model, user?.SCHOOL_NO, user?.USER_NO, (byte)user?.GRADE, ref db2);
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db2, "全部");
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db2, "全部");
            return PartialView(model);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "IndexGradeTecher")] //檢查權限
        public ActionResult _TeacherGradePageContent(CER002IndexViewModel model)
        {
            this.Shared();
            Bre_Name = "全校護照";
            if (model == null)
            {
                model = new CER002IndexViewModel();
            }

            if (model.ListDataHRMT01 == null)
            {
                model.ListDataHRMT01 = new ZZZI19IndexViewModel();


            }

            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereKeyword))
            {
                model.ListDataHRMT01.whereKeyword = model.ListDataHRMT01.whereKeyword;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereGrade))
            {
                model.ListDataHRMT01.whereGrade = model.ListDataHRMT01.whereGrade;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereClass_No))
            {
                model.ListDataHRMT01.whereClass_No = model.ListDataHRMT01.whereClass_No;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.OrdercColumn))
            {
                model.ListDataHRMT01.OrdercColumn = model.ListDataHRMT01.OrdercColumn;

            }
            int SYear;
            int Semesters;

            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            int lastyear = SYear - 6;

            string sSQL = "";
            sSQL = $@"select a.*



                            from HRMT01 a (nolock)
                            Where  a.SCHOOL_NO = @SCHOOL_NO and a.USER_TYPE = 'S'and a.USER_STATUS = '9' and (SYEAR = @SYEAR1 or  SYEAR = @SYEAR2 ) and GRADE = 6";
            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
           
            IQueryable<ZZZI19Hrmt01ViewModel> HRMT01List = db2.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL
            , new
            {
                SCHOOL_NO = user.SCHOOL_NO,
                SYEAR1= lastyear,
                SYEAR2 = lastyear-1
            }).AsQueryable();

            
                // ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
                // ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");
                if (!string.IsNullOrEmpty(user?.TEACH_CLASS_NO))
            {

                model.ListDataHRMT01.whereClass_No = user?.TEACH_CLASS_NO;

            }
            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereKeyword) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO.Contains(model.ListDataHRMT01.whereKeyword.Trim()) || a.NAME.Contains(model.ListDataHRMT01.whereKeyword.Trim()));
            }
            if (model.ListDataHRMT01.WhereSyaer != null)
            {

                HRMT01List = HRMT01List.Where(a => a.SYEAR == (model.ListDataHRMT01.WhereSyaer.HasValue? (lastyear-1).ToString(): model.ListDataHRMT01.WhereSyaer.ToString()));

            }
            //if (string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) == false)
            //{
            //    model.whereClass_No = user.TEACH_CLASS_NO;
            //}

            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereClass_No) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.CLASS_NO == model.ListDataHRMT01.whereClass_No);
            }

            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereGrade) == false)
            {
                byte bGrade = Convert.ToByte(model.ListDataHRMT01.whereGrade);
                string GradeString = "";
                GradeString = bGrade.ToString();
                HRMT01List = HRMT01List.Where(a => a.GRADE == GradeString);
            }
            switch (model.ListDataHRMT01.OrdercColumn)
            {
                case "USER_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.USER_NO);
                    break;

                case "CLASS_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.CLASS_NO);
                    break;
                case "GRADE":
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE);
                    break;
                default:
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                    break;
            }
            if (model.ListDataHRMT01.WhereSyaer == null) {
                HRMT01List = HRMT01List.Where(a => a.SYEAR == (lastyear).ToString());
            }

            List<SelectListItem> GradeItems = new List<SelectListItem>();
            GradeItems.Add(new SelectListItem() { Text = "畢業生", Value = "6", Selected = true });
            ViewBag.GradeItem = GradeItems;

            ViewBag.ClassItems = HRMT01.GetGradeClassListData(SCHOOL_NO,"6", model.ListDataHRMT01.WhereSyaer.HasValue ? model.ListDataHRMT01.WhereSyaer : (byte?)(SYear - 6), model.ListDataHRMT01.whereClass_No, ref db2)
                             .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.ListDataHRMT01.whereClass_No });
            int iPageCount = (model.ListDataHRMT01.ShowPageCount != null) ? Convert.ToInt32(model.ListDataHRMT01.ShowPageCount) : 100;
            model.ListDataHRMT01.HRMT01List = HRMT01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, iPageCount);
            var ClassNoItem =  HRMT01.GetGradeClassListData(SCHOOL_NO, "6", model.ListDataHRMT01.WhereSyaer.HasValue ? model.ListDataHRMT01.WhereSyaer : (byte?)(SYear - 6), model.ListDataHRMT01.whereClass_No, ref db2)
                             .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.ListDataHRMT01.whereClass_No });

            //if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
            //{
            //    ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            //}
            ViewBag.ClassNoItem = ClassNoItem;

            //年級
            //ViewBag.GradeItem = HRMT01.GetGradeItems(model.ListDataHRMT01.whereGrade);

            //狀態
            //   ViewBag.StatusItem = UserStaus.GetUserStausItemsALL(model.whereStatus);

            List<SelectListItem> PageCountItem = new List<SelectListItem>();
            PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = iPageCount.ToString() == "100" });
            PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = iPageCount.ToString() == "200" });
            PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = iPageCount.ToString() == "500" });
            ViewBag.PageCount = PageCountItem;
            return PartialView(model);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "IndexTecher")] //檢查權限
        public ActionResult _TeacherPageContent(CER002IndexViewModel model)
        {
            this.Shared();
            Bre_Name = "全校護照";
            if (model == null) {
                model = new CER002IndexViewModel();
            }

            if (model.ListDataHRMT01 == null)
            {
                model.ListDataHRMT01 = new ZZZI19IndexViewModel();


            }
             
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereKeyword)) {
                model.ListDataHRMT01.whereKeyword = model.ListDataHRMT01.whereKeyword;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereGrade))
            {
                model.ListDataHRMT01.whereGrade = model.ListDataHRMT01.whereGrade;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereClass_No))
            {
                model.ListDataHRMT01.whereClass_No = model.ListDataHRMT01.whereClass_No;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.OrdercColumn))
            {
                model.ListDataHRMT01.OrdercColumn = model.ListDataHRMT01.OrdercColumn;

            }
            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            string sSQL = $@"select * from ufnGetApLog(@SCHOOL_NO)";
            IQueryable<ZZZI19Hrmt01ViewModel> HRMT01List = db2.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL
            , new
            {
                SCHOOL_NO = user.SCHOOL_NO,
            }).AsQueryable();
        
          // ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
           // ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");
            if (!string.IsNullOrEmpty(user?.TEACH_CLASS_NO)) {

                model.ListDataHRMT01.whereClass_No = user?.TEACH_CLASS_NO;

            }
            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereKeyword) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO.Contains(model.ListDataHRMT01.whereKeyword.Trim()) || a.NAME.Contains(model.ListDataHRMT01.whereKeyword.Trim()));
            }

            //if (string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) == false)
            //{
            //    model.whereClass_No = user.TEACH_CLASS_NO;
            //}

            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereClass_No) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.CLASS_NO == model.ListDataHRMT01.whereClass_No);
            }

            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereGrade) == false)
            {
                byte bGrade = Convert.ToByte(model.ListDataHRMT01.whereGrade);
                string GradeString = "";
                GradeString = bGrade.ToString();
                HRMT01List = HRMT01List.Where(a => a.GRADE == GradeString);
            }
            switch (model.ListDataHRMT01.OrdercColumn)
            {
                case "USER_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.USER_NO);
                    break;

                case "CLASS_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.CLASS_NO);
                    break;
                case "GRADE":
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE);
                    break;
                default:
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                    break;
            }
           
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.ListDataHRMT01.whereGrade);
            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.ListDataHRMT01.whereGrade, ref db2)
         .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.ListDataHRMT01.whereClass_No });
            int iPageCount = (model.ListDataHRMT01.ShowPageCount != null) ? Convert.ToInt32(model.ListDataHRMT01.ShowPageCount) : 100;
            model.ListDataHRMT01.HRMT01List = HRMT01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, iPageCount);
            var ClassNoItem = HRMT01.GetClassListData(user.SCHOOL_NO, model.ListDataHRMT01.whereGrade, model.ListDataHRMT01.whereClass_No, ref db2);

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
            {
                ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }
            ViewBag.ClassNoItem = ClassNoItem;

            //年級
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.ListDataHRMT01.whereGrade);

            //狀態
         //   ViewBag.StatusItem = UserStaus.GetUserStausItemsALL(model.whereStatus);

            List<SelectListItem> PageCountItem = new List<SelectListItem>();
            PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = iPageCount.ToString() == "100" });
            PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = iPageCount.ToString() == "200" });
            PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = iPageCount.ToString() == "500" });
            ViewBag.PageCount = PageCountItem;
            return PartialView(model);
        }

        #region Shared

        //protected override void Dispose(bool disposing)
        //{
        //    if (disposing)
        //    {
        //        db.Dispose();
        //    }
        //    base.Dispose(disposing);
        //}

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared
    }
}