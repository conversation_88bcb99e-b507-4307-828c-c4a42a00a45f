// JavaScript for ADDI05 PassList page - 通過名單清單
$(document).ready(function() {
    // 通過名單清單模組
    const passListModule = {
        targetFormID: '#form1',
        
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.initializeLottery();
        },

        bindEvents: function() {
            // 綁定搜尋按鈕事件
            $('input[onclick*="FunPageProc"]').on('click', this.handleSearch.bind(this));
            
            // 綁定清除按鈕事件
            $('input[onclick*="todoClear"]').on('click', this.handleClear.bind(this));
            
            // 綁定分頁大小變更事件
            $('select[onchange*="FunPageProc"]').on('change', this.handlePageSizeChange.bind(this));
            
            // 綁定排序按鈕事件
            $('th[onclick*="doSort"]').on('click', this.handleSort.bind(this));
            
            // 綁定匯出Excel按鈕事件
            $('button[onclick*="ToExcel"]').on('click', this.handleExportExcel.bind(this));
            
            // 綁定抽獎按鈕事件
            $('button[onclick*="ToLotto"]').on('click', this.handleLottery.bind(this));
            
            // 綁定表單提交事件
            $(this.targetFormID).on('submit', this.handleFormSubmit.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.onGo = this.onGo.bind(this);
            window.todoClear = this.todoClear.bind(this);
            window.FunPageProc = this.funPageProc.bind(this);
            window.doSort = this.doSort.bind(this);
            window.ToExcel = this.toExcel.bind(this);
            window.ToLotto = this.toLotto.bind(this);
        },

        onGo: function(actionVal) {
            // 使用共用的onGo函數
            return ADDI05Common.onGo(actionVal);
        },

        todoClear: function() {
            // 使用共用的表單清除功能，只清除搜尋區域
            if (ADDI05Common.clearForm(this.targetFormID, `${this.targetFormID} #DivSearch :input,:selected`)) {
                this.funPageProc(1);
            }
        },

        funPageProc: function(pageno) {
            try {
                const form = ADDI05Common.getForm();
                if (!form) {
                    ADDI05Common.showMessage('系統錯誤：找不到表單元素');
                    return false;
                }
                
                console.log('PassList 切換到第', pageno, '頁');
                
                // PassList使用PassPage欄位而不是Page
                if (form.PassPage) {
                    form.PassPage.value = pageno;
                } else {
                    console.warn('找不到PassPage欄位');
                }
                
                form.submit();
                return true;
            } catch (error) {
                console.error('PassList 分頁處理時發生錯誤:', error);
                ADDI05Common.showMessage('分頁處理時發生錯誤，請稍後再試');
                return false;
            }
        },

        doSort: function(sortCol) {
            try {
                console.log('PassList 排序欄位:', sortCol);
                
                // PassList使用PassOrderByName欄位
                $("#PassOrderByName").val(sortCol);
                
                // 處理排序方向
                let orderRank = $("#OrderRank").val();
                if (orderRank === "") {
                    $("#OrderRank").val("desc");
                } else {
                    // 切換排序方向
                    switch (orderRank) {
                        case "desc":
                            $("#OrderRank").val("asc");
                            break;
                        case "asc":
                            $("#OrderRank").val("desc");
                            break;
                    }
                }
                
                this.funPageProc(1);
                return true;
            } catch (error) {
                console.error('PassList 排序處理時發生錯誤:', error);
                ADDI05Common.showMessage('排序處理時發生錯誤，請稍後再試');
                return false;
            }
        },

        toExcel: function() {
            try {
                const form = ADDI05Common.getForm();
                if (!form) {
                    ADDI05Common.showMessage('系統錯誤：找不到表單元素');
                    return false;
                }
                
                console.log('匯出Excel');
                
                const url = window.ADDI05_PASS_LIST_URLS.printExcel;
                if (!url) {
                    ADDI05Common.showMessage('找不到匯出Excel的URL配置');
                    return false;
                }
                
                // 設置表單action和target
                const originalAction = form.action;
                const originalTarget = form.target;
                
                $(this.targetFormID).attr('action', url).attr('target', '_blank');
                form.submit();
                
                // 恢復原始設定
                setTimeout(() => {
                    $(this.targetFormID).attr('action', originalAction).attr('target', originalTarget || '');
                }, 100);
                
                return true;
            } catch (error) {
                console.error('匯出Excel時發生錯誤:', error);
                ADDI05Common.showMessage('匯出Excel時發生錯誤，請稍後再試');
                return false;
            }
        },

        toLotto: function() {
            try {
                const count = $("#LotteryCount").val();
                const maxCount = window.ADDI05_PASS_LIST_CONFIG.passCount;
                
                // 驗證抽獎人數
                if (!this.validateLotteryCount(count, maxCount)) {
                    return false;
                }
                
                const form = ADDI05Common.getForm();
                if (!form) {
                    ADDI05Common.showMessage('系統錯誤：找不到表單元素');
                    return false;
                }
                
                console.log('開始抽獎，人數:', count);
                
                const url = window.ADDI05_PASS_LIST_URLS.toLotto;
                if (!url) {
                    ADDI05Common.showMessage('找不到抽獎的URL配置');
                    return false;
                }
                
                // 確認抽獎
                if (!confirm(`確定要抽出 ${count} 位得獎者嗎？`)) {
                    return false;
                }
                
                // 設置表單action
                const originalAction = form.action;
                $(this.targetFormID).attr('action', url);
                form.submit();
                
                return true;
            } catch (error) {
                console.error('抽獎時發生錯誤:', error);
                ADDI05Common.showMessage('抽獎時發生錯誤，請稍後再試');
                return false;
            }
        },

        validateLotteryCount: function(count, maxCount) {
            if (count === "" || isNaN(count)) {
                ADDI05Common.showMessage(`請輸入1~${maxCount}之間的數字`, 'warning');
                $("#LotteryCount").focus();
                return false;
            }
            
            const numCount = parseInt(count);
            if (numCount < 1 || numCount > maxCount) {
                ADDI05Common.showMessage(`請輸入1~${maxCount}之間的數字`, 'warning');
                $("#LotteryCount").focus();
                return false;
            }
            
            return true;
        },

        initializeLottery: function() {
            // 如果有抽獎功能，初始化相關設定
            const $lotteryDiv = $('#DivLottery');
            if ($lotteryDiv.length > 0) {
                // 為抽獎人數輸入框添加Enter鍵支援
                $('#LotteryCount').on('keypress', function(e) {
                    if (e.which === 13) { // Enter鍵
                        e.preventDefault();
                        this.toLotto();
                    }
                }.bind(this));
                
                // 為抽獎人數輸入框添加數字驗證
                $('#LotteryCount').on('input', function() {
                    const value = $(this).val();
                    const maxCount = window.ADDI05_PASS_LIST_CONFIG.passCount;
                    
                    // 移除非數字字符
                    const numericValue = value.replace(/[^0-9]/g, '');
                    if (numericValue !== value) {
                        $(this).val(numericValue);
                    }
                    
                    // 即時驗證範圍
                    if (numericValue && (parseInt(numericValue) < 1 || parseInt(numericValue) > maxCount)) {
                        $(this).addClass('input-error');
                    } else {
                        $(this).removeClass('input-error');
                    }
                });
                
                console.log('抽獎功能已初始化');
            }
        },

        // 事件處理器
        handleSearch: function(event) {
            event.preventDefault();
            this.funPageProc(1);
        },

        handleClear: function(event) {
            event.preventDefault();
            this.todoClear();
        },

        handlePageSizeChange: function(event) {
            this.funPageProc(1);
        },

        handleSort: function(event) {
            event.preventDefault();
            const onclick = $(event.target).closest('th').attr('onclick');
            if (onclick) {
                const match = onclick.match(/doSort\('([^']*)'\)/);
                if (match) {
                    this.doSort(match[1]);
                }
            }
        },

        handleExportExcel: function(event) {
            event.preventDefault();
            this.toExcel();
        },

        handleLottery: function(event) {
            event.preventDefault();
            this.toLotto();
        },

        handleFormSubmit: function(event) {
            try {
                console.log('PassList 表單提交');
                return true;
            } catch (error) {
                console.error('PassList 表單提交時發生錯誤:', error);
                event.preventDefault();
                ADDI05Common.showMessage('表單提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        }
    };

    // 初始化模組
    passListModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('PassList 頁面錯誤:', e);
    });
});
