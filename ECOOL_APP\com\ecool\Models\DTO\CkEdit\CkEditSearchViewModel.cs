﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CkEditSearchViewModel
    {
        public string SeeSCHOOL_NO { get; set; }

        public string SeeUSER_NO { get; set; }

        public string SeeREF_TYPE { get; set; }

        public string  WhereREF_NO { get; set; }
        public string whereSUBJECT { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }


        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }


        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        public string BackAction { get; set; }

        public string BackController { get; set; }


        public string CKEditorFuncNum { get; set; }

        public string CKEditor { get; set; }

        public string langCode { get; set; }
    }
}
