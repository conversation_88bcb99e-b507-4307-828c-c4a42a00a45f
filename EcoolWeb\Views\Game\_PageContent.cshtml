﻿@model GameIndexViewModel
@using ECOOL_APP;
@{
    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;

    byte? GAME_TYPE = ViewBag.GAME_TYPE ?? (byte)ADDT26.GameType.一般;

}

@Html.HiddenFor(m => m.Search.WhereGAME_NAME)
@Html.HiddenFor(m => m.Search.WhereGAME_NO)
@Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.OrdercColumn)

<br />
<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">搜尋欲瀏覽之相關字串</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.Search.WhereGAME_NAME, new { htmlAttributes = new { @class = "form-control" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
<br />

@if (Permission.Where(a => a.ActionName == "Edit").Any())
{
    <div class="text-right">
        <a role="button" href='@Url.Action("AddSelect",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys"><i class="fa fa-plus-circle"></i>  新增</a>
        <br />
        <br />
    </div>
}

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_NAME')">
                            @Html.DisplayNameFor(model => model.ListData.First().GAME_NAME)
                        </samp>
                    </th>

                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_DATES')">
                            @Html.DisplayNameFor(model => model.ListData.First().GAME_DATES)
                        </samp>
                    </th>
                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_DATEE')">
                            @Html.DisplayNameFor(model => model.ListData.First().GAME_DATEE)
                        </samp>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {
                        <tr>
                            <td>
                                @if (Permission.Where(a => a.ActionName == "Edit").Any())
                                {
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnEdit('@item.GAME_NO','@item.GAME_TYPE')"> <i class="fa fa-edit"></i> 關卡管理</button>

                                    if (GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnRewar('@item.GAME_NO')"> <i class="fa fa-edit"></i> 鼓勵與獎勵</button>
                                    }
                                }

                                @if (Permission.Where(a => a.ActionName == "BatchWork").Any())
                                {
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnBatchWork('@item.GAME_NO')"> <i class="fa fa-rocket"></i> 人員管理</button>
                                }

                                @if (item?.GAME_DATES != null && item?.GAME_DATEE != null)
                                {
                                    if (DateTime.Now >= item?.GAME_DATES && DateTime.Now < item?.GAME_DATEE.Value.AddDays(1))
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnPassMode('@item.GAME_NO')"> <i class="fa fa-truck"></i> 過關模式</button>
                                    }
                                }

                                <br />

                                @if (GAME_TYPE == (byte)ADDT26.GameType.一般)
                                {
                                    if (Permission.Where(a => a.ActionName == "Statistics").Any())
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnStatistics('@item.GAME_NO')"> <i class="fa fa-bar-chart-o"></i> 統計</button>
                                    }

                                    if (item?.GAME_DATES != null && item?.GAME_DATEE != null)
                                    {
                                        if (DateTime.Now >= item?.GAME_DATES && DateTime.Now < item?.GAME_DATEE.Value.AddDays(1))
                                        {

                                            if (Permission.Where(a => a.ActionName == "Lottery").Any())
                                            {
                                                <button type="button" class="btn btn-xs btn-Basic" onclick="OnLottery('@item.GAME_NO')"> <i class="fa fa-money"></i> 抽獎</button>
                                            }

                                            if (Permission.Where(a => a.ActionName == "BuskerManager").Any())
                                            {
                                                <button type="button" class="btn btn-xs btn-Basic" onclick="OnBuskerManager('@item.GAME_NO')"> <i class="fa fa-group"></i> 街頭藝人</button>
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    if (Permission.Where(a => a.ActionName == "StatisticsAns").Any())
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnStatisticsAns('@item.GAME_NO')"> <i class="fa fa-bar-chart-o"></i> 統計分析</button>
                                    }

                                    if (Permission.Where(a => a.ActionName == "ScoreList").Any())
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnScoreList('@item.GAME_NO')"> <i class="fa fa-bar-chart-o"></i> 成績一覽表</button>
                                    }

                                    if (Permission.Where(a => a.ActionName == "Lottery").Any())
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnLottery('@item.GAME_NO')"> <i class="fa fa-money"></i> 抽獎</button>
                                    }
                                }

                                @if (Permission.Where(a => a.ActionName == "CashIntoView").Any())
                                {
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnCashIntoView('@item.GAME_NO')"> <i class="fa fa-rocket"></i>點數處理</button>
                                }
                                <button type="button" class="btn btn-xs btn-Basic" onclick="onWinOpenYoutubeUrlLink('@item.GAME_NO')"> <i class="fa fa-copy"></i> 複製過關模式連結</button>
                                <button type="button" class="btn btn-xs btn-Basic " onclick="OnQueryTeamSetView('@item.GAME_NO')"> <i class="fa fa-copy"></i> 小組闖關</button>
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.GAME_NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.GAME_DATES, "ShortDateTime")
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.GAME_DATEE, "ShortDateTime")
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>
<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                              .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                              .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                              .SetNextPageText(PageGlobal.DfSetNextPageText)
                                             )
</div>