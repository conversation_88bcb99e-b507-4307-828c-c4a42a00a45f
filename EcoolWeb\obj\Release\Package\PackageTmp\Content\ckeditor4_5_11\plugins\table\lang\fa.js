﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'fa', {
	border: 'اندازهٴ لبه',
	caption: 'عنوان',
	cell: {
		menu: 'سلول',
		insertBefore: 'افزودن سلول قبل از',
		insertAfter: 'افزودن سلول بعد از',
		deleteCell: 'حذف سلولها',
		merge: 'ادغام سلولها',
		mergeRight: 'ادغام به راست',
		mergeDown: 'ادغام به پایین',
		splitHorizontal: 'جدا کردن افقی سلول',
		splitVertical: 'جدا کردن عمودی سلول',
		title: 'ویژگیهای سلول',
		cellType: 'نوع سلول',
		rowSpan: 'محدوده ردیفها',
		colSpan: 'محدوده ستونها',
		wordWrap: 'شکستن کلمه',
		hAlign: 'چینش افقی',
		vAlign: 'چینش عمودی',
		alignBaseline: 'خط مبنا',
		bgColor: 'رنگ زمینه',
		borderColor: 'رنگ خطوط',
		data: 'اطلاعات',
		header: 'سرنویس',
		yes: 'بله',
		no: 'خیر',
		invalidWidth: 'عرض سلول باید یک عدد باشد.',
		invalidHeight: 'ارتفاع سلول باید عدد باشد.',
		invalidRowSpan: 'مقدار محدوده ردیفها باید یک عدد باشد.',
		invalidColSpan: 'مقدار محدوده ستونها باید یک عدد باشد.',
		chooseColor: 'انتخاب'
	},
	cellPad: 'فاصلهٴ پرشده در سلول',
	cellSpace: 'فاصلهٴ میان سلولها',
	column: {
		menu: 'ستون',
		insertBefore: 'افزودن ستون قبل از',
		insertAfter: 'افزودن ستون بعد از',
		deleteColumn: 'حذف ستونها'
	},
	columns: 'ستونها',
	deleteTable: 'پاک کردن جدول',
	headers: 'سرنویسها',
	headersBoth: 'هردو',
	headersColumn: 'اولین ستون',
	headersNone: 'هیچ',
	headersRow: 'اولین ردیف',
	invalidBorder: 'مقدار اندازه خطوط باید یک عدد باشد.',
	invalidCellPadding: 'بالشتک سلول باید یک عدد باشد.',
	invalidCellSpacing: 'مقدار فاصلهگذاری سلول باید یک عدد باشد.',
	invalidCols: 'تعداد ستونها باید یک عدد بزرگتر از 0 باشد.',
	invalidHeight: 'مقدار ارتفاع  جدول باید یک عدد باشد.',
	invalidRows: 'تعداد ردیفها باید یک عدد بزرگتر از 0 باشد.',
	invalidWidth: 'مقدار پهنای جدول باید یک عدد باشد.',
	menu: 'ویژگیهای جدول',
	row: {
		menu: 'سطر',
		insertBefore: 'افزودن سطر قبل از',
		insertAfter: 'افزودن سطر بعد از',
		deleteRow: 'حذف سطرها'
	},
	rows: 'سطرها',
	summary: 'خلاصه',
	title: 'ویژگیهای جدول',
	toolbar: 'جدول',
	widthPc: 'درصد',
	widthPx: 'پیکسل',
	widthUnit: 'واحد پهنا'
} );
