<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            テスト配置ごとに配置項目 (ファイルまたはディレクトリ) を指定するために使用されます。
            テスト クラスまたはテスト メソッドで指定できます。
            属性に複数のインスタンスを指定して、2 つ以上の項目を指定することができます。
            項目のパスには絶対パスまたは相対パスを指定できます。相対パスの場合は、RunConfig.RelativePathRoot からの相対パスです。
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="path">配置するファイルまたはディレクトリ。パスはビルドの出力ディレクトリの相対パスです。項目は配置されたテスト アセンブリと同じディレクトリにコピーされます。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> クラスの新しいインスタンスを初期化する
            </summary>
            <param name="path">配置するファイルまたはディレクトリへの相対パスまたは絶対パス。パスはビルドの出力ディレクトリの相対パスです。項目は配置されたテスト アセンブリと同じディレクトリにコピーされます。</param>
            <param name="outputDirectory">アイテムのコピー先のディレクトリのパス。配置ディレクトリへの絶対パスまたは相対パスのいずれかを指定できます。次で識別されるすべてのファイルとディレクトリは <paramref name="path"/> このディレクトリにコピーされます。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            コピーするソース ファイルまたはフォルダーのパスを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            項目のコピー先のディレクトリのパスを取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames">
            <summary>
            セクション、プロパティ、属性の名前のリテラルが含まれています。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames.SectionName">
            <summary>
            構成セクション名。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames.Beta2SectionName">
            <summary>
            Beta2 の構成セクション名。互換性のために残されています。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames.DataSourcesSectionName">
            <summary>
            データ ソースのセクション名。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames.NameAttributeName">
            <summary>
            'Name' の属性名
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames.ConnectionStringAttributeName">
            <summary>
            'ConnectionString' の属性名
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames.DataAccessMethodAttributeName">
            <summary>
            'DataAccessMethod' の属性名
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ConfigurationNames.DataTableAttributeName">
            <summary>
            'DataTable' の属性名
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement">
            <summary>
            データ ソース要素。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement.Name">
            <summary>
            この構成の名前を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement.ConnectionString">
            <summary>
            .config ファイルの &lt;connectionStrings&gt; セクションの ConnectionStringSettings 要素を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement.DataTableName">
            <summary>
            データ テーブルの名前を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement.DataAccessMethod">
            <summary>
            データ アクセスの種類を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement.Key">
            <summary>
            キー名を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement.Properties">
            <summary>
            構成プロパティを取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection">
            <summary>
            データ ソース要素コレクション。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection"/> クラスの新しいインスタンスを初期化します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.Item(System.String)">
            <summary>
            指定したキーを含む構成要素を返します。
            </summary>
            <param name="name">返される要素のキー。</param>
            <returns>指定したキーを持つ System.Configuration.ConfigurationElement。それ以外の場合は、null。</returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.Item(System.Int32)">
            <summary>
            指定したインデックスの場所の構成要素を取得します。
            </summary>
            <param name="index">返される System.Configuration.ConfigurationElement のインデックスの場所。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.Add(Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement)">
            <summary>
            構成要素を構成要素コレクションに追加します。
            </summary>
            <param name="element">追加する System.Configuration.ConfigurationElement。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.Remove(Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement)">
            <summary>
            コレクションから System.Configuration.ConfigurationElement を削除します。
            </summary>
            <param name="element"> <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement"/> 。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.Remove(System.String)">
            <summary>
            コレクションから System.Configuration.ConfigurationElement を削除します。
            </summary>
            <param name="name">削除する System.Configuration.ConfigurationElement のキー。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.Clear">
            <summary>
            コレクションからすべての構成要素オブジェクトを削除します。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.CreateNewElement">
            <summary>
            新しい <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement"/> を作成します。
            </summary>
            <returns>新しい <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElement"/>。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            指定した構成要素の要素キーを取得します。
            </summary>
            <param name="element">キーを返す対象の System.Configuration.ConfigurationElement。</param>
            <returns>指定した System.Configuration.ConfigurationElement のキーとして機能する System.Object。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.BaseAdd(System.Configuration.ConfigurationElement)">
            <summary>
            構成要素を構成要素コレクションに追加します。
            </summary>
            <param name="element">追加する System.Configuration.ConfigurationElement。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceElementCollection.BaseAdd(System.Int32,System.Configuration.ConfigurationElement)">
            <summary>
            構成要素を構成要素コレクションに追加します。
            </summary>
            <param name="index">指定した System.Configuration.ConfigurationElement を追加するインデックスの場所。</param>
            <param name="element">追加する System.Configuration.ConfigurationElement。</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestConfiguration">
            <summary>
            テストの構成設定のサポート。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestConfiguration.ConfigurationSection">
            <summary>
            テスト用の構成セクションを取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestConfigurationSection">
            <summary>
            テスト用の構成セクション。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestConfigurationSection.DataSources">
            <summary>
            この構成セクションのデータ ソースを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestConfigurationSection.Properties">
            <summary>
            プロパティのコレクションを取得します。
            </summary>
            <returns>
            その <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> (要素のプロパティ)。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject">
            <summary>
            このクラスは、システム内のパブリックでないライブ内部オブジェクトを表します
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.#ctor(System.Object,System.String)">
            <summary>
            プライベート クラスの既存のオブジェクトを含んでいる <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject"/>
            クラスの新しいインスタンスを初期化します
            </summary>
            <param name="obj"> プライベート メンバーに到達するための開始点となるオブジェクト</param>
            <param name="memberToAccess">m_X.m_Y.m_Z として取得するオブジェクトを指し示す "." を使用する逆参照文字列</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.#ctor(System.String,System.String,System.Object[])">
            <summary>
            指定された型をラップする <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject"/> クラスの新しいインスタンスを
            初期化します。
            </summary>
            <param name="assemblyName">アセンブリの名前</param>
            <param name="typeName">完全修飾名</param>
            <param name="args">コンストラクターに渡す引数</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.#ctor(System.String,System.String,System.Type[],System.Object[])">
            <summary>
            指定された型をラップする <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject"/> クラスの新しいインスタンスを
            初期化します。
            </summary>
            <param name="assemblyName">アセンブリの名前</param>
            <param name="typeName">完全修飾名</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するコンストラクターのパラメーターの数、順番、型を表すオブジェクト</param>
            <param name="args">コンストラクターに渡す引数</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.#ctor(System.Type,System.Object[])">
            <summary>
            指定された型をラップする <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject"/> クラスの新しいインスタンスを
            初期化します。
            </summary>
            <param name="type">作成するオブジェクトの型</param>
            <param name="args">コンストラクターに渡す引数</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.#ctor(System.Type,System.Type[],System.Object[])">
            <summary>
            指定された型をラップする <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject"/> クラスの新しいインスタンスを
            初期化します。
            </summary>
            <param name="type">作成するオブジェクトの型</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するコンストラクターのパラメーターの数、順番、型を表すオブジェクト</param>
            <param name="args">コンストラクターに渡す引数</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.#ctor(System.Object)">
            <summary>
            指定されたオブジェクトをラップする <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject"/> クラスの新しいインスタンスを
            初期化します。
            </summary>
            <param name="obj">ラップするオブジェクト</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.#ctor(System.Object,Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType)">
            <summary>
            指定されたオブジェクトをラップする <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject"/> クラスの新しいインスタンスを
            初期化します。
            </summary>
            <param name="obj">ラップするオブジェクト</param>
            <param name="type">PrivateType オブジェクト</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Target">
            <summary>
            ターゲットを取得または設定します
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.RealType">
            <summary>
            基になるオブジェクトの型を取得します
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetHashCode">
            <summary>
            対象オブジェクトのハッシュ コードを返す
            </summary>
            <returns>対象オブジェクトのハッシュコードを表す int</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Equals(System.Object)">
            <summary>
            次の値と等しい
            </summary>
            <param name="obj">比較対象のオブジェクト</param>
            <returns>オブジェクトが等しい場合は True を返します。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Object[])">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Type[],System.Object[])">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するメソッドのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Type[],System.Object[],System.Type[])">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するメソッドのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <param name="typeArguments">ジェネリック引数の型に対応する型の配列。</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <param name="culture">カルチャ情報</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Type[],System.Object[],System.Globalization.CultureInfo)">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するメソッドのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <param name="culture">カルチャ情報</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Reflection.BindingFlags,System.Object[])">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[])">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するメソッドのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Reflection.BindingFlags,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <param name="culture">カルチャ情報</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[],System.Globalization.CultureInfo)">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するメソッドのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <param name="culture">カルチャ情報</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.Invoke(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[],System.Globalization.CultureInfo,System.Type[])">
            <summary>
            指定されたメソッドを呼び出す
            </summary>
            <param name="name">メソッドの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 取得するメソッドのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <param name="culture">カルチャ情報</param>
            <param name="typeArguments">ジェネリック引数の型に対応する型の配列。</param>
            <returns>メソッド呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetArrayElement(System.String,System.Int32[])">
            <summary>
            各ディメンションに下付き文字の配列を使用して配列要素を取得する
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="indices">配列のインデックス</param>
            <returns>要素の配列。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetArrayElement(System.String,System.Object,System.Int32[])">
            <summary>
            各ディメンションに下付き文字の配列を使用して配列要素を設定する
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="value">設定する値</param>
            <param name="indices">配列のインデックス</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetArrayElement(System.String,System.Reflection.BindingFlags,System.Int32[])">
            <summary>
            各ディメンションに下付き文字の配列を使用して配列要素を取得する
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="indices">配列のインデックス</param>
            <returns>要素の配列。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetArrayElement(System.String,System.Reflection.BindingFlags,System.Object,System.Int32[])">
            <summary>
            各ディメンションに下付き文字の配列を使用して配列要素を設定する
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="value">設定する値</param>
            <param name="indices">配列のインデックス</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetField(System.String)">
            <summary>
            フィールドを取得する
            </summary>
            <param name="name">フィールドの名前</param>
            <returns>フィールド。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetField(System.String,System.Object)">
            <summary>
            フィールドを設定する
            </summary>
            <param name="name">フィールドの名前</param>
            <param name="value">設定する値</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetField(System.String,System.Reflection.BindingFlags)">
            <summary>
            フィールドを取得する
            </summary>
            <param name="name">フィールドの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <returns>フィールド。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetField(System.String,System.Reflection.BindingFlags,System.Object)">
            <summary>
            フィールドを設定する
            </summary>
            <param name="name">フィールドの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="value">設定する値</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetFieldOrProperty(System.String)">
            <summary>
            フィールドまたはプロパティを取得する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <returns>フィールドまたはプロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetFieldOrProperty(System.String,System.Object)">
            <summary>
            フィールドまたはプロパティを設定する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <param name="value">設定する値</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetFieldOrProperty(System.String,System.Reflection.BindingFlags)">
            <summary>
            フィールドまたはプロパティを取得する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <returns>フィールドまたはプロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetFieldOrProperty(System.String,System.Reflection.BindingFlags,System.Object)">
            <summary>
            フィールドまたはプロパティを設定する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="value">設定する値</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetProperty(System.String,System.Object[])">
            <summary>
            プロパティを取得する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>プロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetProperty(System.String,System.Type[],System.Object[])">
            <summary>
            プロパティを取得する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> インデックス付きプロパティのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>プロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetProperty(System.String,System.Object,System.Object[])">
            <summary>
            プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="value">設定する値</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetProperty(System.String,System.Type[],System.Object,System.Object[])">
            <summary>
            プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> インデックス付きプロパティのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="value">設定する値</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetProperty(System.String,System.Reflection.BindingFlags,System.Object[])">
            <summary>
            プロパティを取得する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>プロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetProperty(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[])">
            <summary>
            プロパティを取得する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> インデックス付きプロパティのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>プロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetProperty(System.String,System.Reflection.BindingFlags,System.Object,System.Object[])">
            <summary>
            プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="value">設定する値</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.SetProperty(System.String,System.Reflection.BindingFlags,System.Object,System.Type[],System.Object[])">
            <summary>
            プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">1 つまたは複数の以下のもので構成されるビットマスク <see cref="T:System.Reflection.BindingFlags"/> 検索の実行方法を指定します。</param>
            <param name="value">設定する値</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> インデックス付きプロパティのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.ValidateAccessString(System.String)">
            <summary>
            アクセス文字列を検証する
            </summary>
            <param name="access"> アクセス文字列</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.InvokeHelper(System.String,System.Reflection.BindingFlags,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            メンバーを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">追加の属性</param>
            <param name="args">呼び出しの引数</param>
            <param name="culture">カルチャ</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateObject.GetGenericMethodFromCache(System.String,System.Type[],System.Type[],System.Reflection.BindingFlags,System.Reflection.ParameterModifier[])">
            <summary>
            現在のプライベート型から最も適切なジェネリック メソッド シグネチャを抽出します。
            </summary>
            <param name="methodName">シグネチャ キャッシュを検索するメソッドの名前。</param>
            <param name="parameterTypes">検索対象のパラメーターの型に対応する型の配列。</param>
            <param name="typeArguments">ジェネリック引数の型に対応する型の配列。</param>
            <param name="bindingFlags"><see cref="T:System.Reflection.BindingFlags"/> メソッド シグネチャをさらにフィルターするため。</param>
            <param name="modifiers">パラメーターの修飾子。</param>
            <returns>Methodinfo インスタンス。</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType">
            <summary>
            このクラスは、プライベート アクセサー機能のプライベート クラスを表します。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.BindToEveryThing">
            <summary>
            すべてにバインドする
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.type">
            <summary>
            ラップされた型。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.#ctor(System.String,System.String)">
            <summary>
            プライベート型を含む <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="assemblyName">アセンブリ名</param>
            <param name="typeName">完全修飾名: </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType"/> class that contains
            the private type from the type object
            </summary>
            <param name="type">作成するラップされた型。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.ReferencedType">
            <summary>
            参照型を取得する
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Object[])">
            <summary>
            静的メンバーを呼び出す
            </summary>
            <param name="name">InvokeHelper に対するメンバーの名前</param>
            <param name="args">呼び出しに対する引数</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Type[],System.Object[])">
            <summary>
            静的メンバーを呼び出す
            </summary>
            <param name="name">InvokeHelper に対するメンバーの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 呼び出すメソッドのパラメーターの数値、順序、および型を表すオブジェクト</param>
            <param name="args">呼び出しに対する引数</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Type[],System.Object[],System.Type[])">
            <summary>
            静的メンバーを呼び出す
            </summary>
            <param name="name">InvokeHelper に対するメンバーの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 呼び出すメソッドのパラメーターの数値、順序、および型を表すオブジェクト</param>
            <param name="args">呼び出しに対する引数</param>
            <param name="typeArguments">ジェネリック引数の型に対応する型の配列。</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="args">呼び出しに対する引数</param>
            <param name="culture">カルチャ</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Type[],System.Object[],System.Globalization.CultureInfo)">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 呼び出すメソッドのパラメーターの数値、順序、および型を表すオブジェクト</param>
            <param name="args">呼び出しに対する引数</param>
            <param name="culture">カルチャ情報</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Reflection.BindingFlags,System.Object[])">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            <param name="args">呼び出しに対する引数</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[])">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> 呼び出すメソッドのパラメーターの数値、順序、および型を表すオブジェクト</param>
            <param name="args">呼び出しに対する引数</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Reflection.BindingFlags,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            <param name="args">呼び出しに対する引数</param>
            <param name="culture">カルチャ</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[],System.Globalization.CultureInfo)">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            /// <param name="parameterTypes">配列: <see cref="T:System.Type"/> 呼び出すメソッドのパラメーターの数値、順序、および型を表すオブジェクト</param>
            <param name="args">呼び出しに対する引数</param>
            <param name="culture">カルチャ</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeStatic(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[],System.Globalization.CultureInfo,System.Type[])">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            /// <param name="parameterTypes">配列: <see cref="T:System.Type"/> 呼び出すメソッドのパラメーターの数値、順序、および型を表すオブジェクト</param>
            <param name="args">呼び出しに対する引数</param>
            <param name="culture">カルチャ</param>
            <param name="typeArguments">ジェネリック引数の型に対応する型の配列。</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticArrayElement(System.String,System.Int32[])">
            <summary>
            静的配列内の要素を取得する
            </summary>
            <param name="name">配列の名前</param>
            <param name="indices">
            取得する要素の位置を指定するインデックスを表す 32 ビット整数
            の 1 次元配列。たとえば、[10][11] にアクセスする場合には、インデックスは {10,11} になります
            </param>
            <returns>指定した場所の要素</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticArrayElement(System.String,System.Object,System.Int32[])">
            <summary>
            静的配列のメンバーを設定する
            </summary>
            <param name="name">配列の名前</param>
            <param name="value">設定する値</param>
            <param name="indices">
            設定する要素の位置を指定するインデックスを表す 32 ビット整数
            の 1 次元配列。たとえば、[10][11] にアクセスする場合には、配列は {10,11} になります
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticArrayElement(System.String,System.Reflection.BindingFlags,System.Int32[])">
            <summary>
            静的配列の要素を取得します
            </summary>
            <param name="name">配列の名前</param>
            <param name="bindingFlags">追加の InvokeHelper 属性</param>
            <param name="indices">
            取得する要素の位置を指定するインデックスを表す 32 ビット整数
            の 1 次元配列。たとえば、[10][11] にアクセスする場合には、配列は {10,11} になります
            </param>
            <returns>指定した場所の要素</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticArrayElement(System.String,System.Reflection.BindingFlags,System.Object,System.Int32[])">
            <summary>
            静的配列のメンバーを設定する
            </summary>
            <param name="name">配列の名前</param>
            <param name="bindingFlags">追加の InvokeHelper 属性</param>
            <param name="value">設定する値</param>
            <param name="indices">
            設定する要素の位置を指定するインデックスを表す 32 ビット整数
            の 1 次元配列。たとえば、[10][11] にアクセスする場合には、配列は {10,11} になります
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticField(System.String)">
            <summary>
            静的フィールドを取得する
            </summary>
            <param name="name">フィールドの名前</param>
            <returns>静的フィールド。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticField(System.String,System.Object)">
            <summary>
            静的フィールドを設定する
            </summary>
            <param name="name">フィールドの名前</param>
            <param name="value">呼び出しに対する引数</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticField(System.String,System.Reflection.BindingFlags)">
            <summary>
            指定した InvokeHelper 属性を使用して静的フィールドを取得する
            </summary>
            <param name="name">フィールドの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            <returns>静的フィールド。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticField(System.String,System.Reflection.BindingFlags,System.Object)">
            <summary>
            バインド属性を使用して静的フィールドを設定する
            </summary>
            <param name="name">フィールドの名前</param>
            <param name="bindingFlags">追加の InvokeHelper 属性</param>
            <param name="value">呼び出しに対する引数</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticFieldOrProperty(System.String)">
            <summary>
            静的フィールドまたは静的プロパティを取得する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <returns>静的フィールドまたはプロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticFieldOrProperty(System.String,System.Object)">
            <summary>
            静的フィールドまたは静的プロパティを設定する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <param name="value">フィールドまたはプロパティに設定する値</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticFieldOrProperty(System.String,System.Reflection.BindingFlags)">
            <summary>
            指定した InvokeHelper 属性を使用して、静的フィールドまたは静的プロパティを取得する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            <returns>静的フィールドまたはプロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticFieldOrProperty(System.String,System.Reflection.BindingFlags,System.Object)">
            <summary>
            バインド属性を使用して、静的フィールドまたは静的プロパティを設定する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            <param name="value">フィールドまたはプロパティに設定する値</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticProperty(System.String,System.Object[])">
            <summary>
            静的プロパティを取得する
            </summary>
            <param name="name">フィールドまたはプロパティの名前</param>
            <param name="args">呼び出しに対する引数</param>
            <returns>静的プロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticProperty(System.String,System.Object,System.Object[])">
            <summary>
            静的プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="value">フィールドまたはプロパティに設定する値</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticProperty(System.String,System.Object,System.Type[],System.Object[])">
            <summary>
            静的プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="value">フィールドまたはプロパティに設定する値</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> インデックス付きプロパティのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticProperty(System.String,System.Reflection.BindingFlags,System.Object[])">
            <summary>
            静的プロパティを取得する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">追加の呼び出し属性。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>静的プロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.GetStaticProperty(System.String,System.Reflection.BindingFlags,System.Type[],System.Object[])">
            <summary>
            静的プロパティを取得する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">追加の呼び出し属性。</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> インデックス付きプロパティのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
            <returns>静的プロパティ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticProperty(System.String,System.Reflection.BindingFlags,System.Object,System.Object[])">
            <summary>
            静的プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">追加の呼び出し属性。</param>
            <param name="value">フィールドまたはプロパティに設定する値</param>
            <param name="args">インデックス付きプロパティのオプションのインデックス値。インデックス付きプロパティのインデックスは 0 から始まります。インデックスのないプロパティについては、この値は null である必要があります。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.SetStaticProperty(System.String,System.Reflection.BindingFlags,System.Object,System.Type[],System.Object[])">
            <summary>
            静的プロパティを設定する
            </summary>
            <param name="name">プロパティの名前</param>
            <param name="bindingFlags">追加の呼び出し属性。</param>
            <param name="value">フィールドまたはプロパティに設定する値</param>
            <param name="parameterTypes">配列: <see cref="T:System.Type"/> インデックス付きプロパティのパラメーターの数、順番、型を表すオブジェクト。</param>
            <param name="args">呼び出すメンバーに渡す引数。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PrivateType.InvokeHelperStatic(System.String,System.Reflection.BindingFlags,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            静的メソッドを呼び出す
            </summary>
            <param name="name">メンバーの名前</param>
            <param name="bindingFlags">追加の呼び出し属性</param>
            <param name="args">呼び出しに対する引数</param>
            <param name="culture">カルチャ</param>
            <returns>呼び出しの結果</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper">
            <summary>
            ジェネリック メソッドのメソッド シグネチャを検出します。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper.CompareMethodSigAndName(System.Reflection.MethodBase,System.Reflection.MethodBase)">
            <summary>
            これらの 2 つのメソッドのメソッド シグネチャを比較します。
            </summary>
            <param name="m1">Method1</param>
            <param name="m2">Method2</param>
            <returns>類似している場合は True。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper.GetHierarchyDepth(System.Type)">
            <summary>
            指定した型の基本データ型から階層の深さを取得します。
            </summary>
            <param name="t">型。</param>
            <returns>深さ。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper.FindMostDerivedNewSlotMeth(System.Reflection.MethodBase[],System.Int32)">
            <summary>
            指定された情報を使用して最派生型を検索します。
            </summary>
            <param name="match">候補の一致。</param>
            <param name="cMatches">一致の数。</param>
            <returns>最派生メソッド。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper.SelectMethod(System.Reflection.BindingFlags,System.Reflection.MethodBase[],System.Type[],System.Reflection.ParameterModifier[])">
            <summary>
            基本条件に一致するメソッドのセットを指定して、型の配列に
            基づいてメソッドを選択します。条件に
            一致するメソッドがない場合、このメソッドは null を返します。
            </summary>
            <param name="bindingAttr">バインドの指定。</param>
            <param name="match">候補の一致</param>
            <param name="types">型</param>
            <param name="modifiers">パラメーター修飾子。</param>
            <returns>一致するメソッド。一致が見つからない場合は null。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper.FindMostSpecificMethod(System.Reflection.MethodBase,System.Int32[],System.Type,System.Reflection.MethodBase,System.Int32[],System.Type,System.Type[],System.Object[])">
            <summary>
            指定されている 2 つのメソッドのうち、より特定性の高いメソッドを判別します。
            </summary>
            <param name="m1">メソッド 1</param>
            <param name="paramOrder1">メソッド 1 のパラメーターの順序</param>
            <param name="paramArrayType1">パラメーターの配列型。</param>
            <param name="m2">メソッド 2</param>
            <param name="paramOrder2">メソッド 2 のパラメーターの順序</param>
            <param name="paramArrayType2">&gt;パラメーターの配列型。</param>
            <param name="types">検索する型。</param>
            <param name="args">引数。</param>
            <returns>一致を表す int。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper.FindMostSpecific(System.Reflection.ParameterInfo[],System.Int32[],System.Type,System.Reflection.ParameterInfo[],System.Int32[],System.Type,System.Type[],System.Object[])">
            <summary>
            指定されている 2 つのメソッドのうち、より特定性の高いメソッドを判別します。
            </summary>
            <param name="p1">メソッド 1</param>
            <param name="paramOrder1">メソッド 1 のパラメーターの順序</param>
            <param name="paramArrayType1">パラメーターの配列型。</param>
            <param name="p2">メソッド 2</param>
            <param name="paramOrder2">メソッド 2 のパラメーターの順序</param>
            <param name="paramArrayType2">&gt;パラメーターの配列型。</param>
            <param name="types">検索する型。</param>
            <param name="args">引数。</param>
            <returns>一致を表す int。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.RuntimeTypeHelper.FindMostSpecificType(System.Type,System.Type,System.Type)">
            <summary>
            指定されている 2 つのうち、より特定性の高い型を判別します。
            </summary>
            <param name="c1">型 1</param>
            <param name="c2">型 2</param>
            <param name="t">定義する型</param>
            <returns>一致を表す int。</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            単体テストに提供される情報を保存するために使用されます。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            テストのテスト プロパティを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.DataRow">
            <summary>
            テストがデータ ドリブン テストで使用されるときに現在のデータ行を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.DataConnection">
            <summary>
            テストがデータ ドリブン テストで使用されるときに現在のデータ接続行を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestRunDirectory">
            <summary>
            テストの実行の基本ディレクトリを取得します。配置されたファイルと結果ファイルはそのディレクトリに格納されます。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.DeploymentDirectory">
            <summary>
            テストの実行のために配置されたファイルのディレクトリを取得します。通常は、<see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestRunDirectory"/> のサブディレクトリです。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.ResultsDirectory">
            <summary>
            テストの実行の結果の基本ディレクトリを取得します。通常は、<see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestRunDirectory"/> のサブディレクトリです。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestRunResultsDirectory">
            <summary>
            テストの実行の結果ファイル用のディレクトリを取得します。通常は、<see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.ResultsDirectory"/> のサブディレクトリです。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestResultsDirectory">
            <summary>
            テスト結果ファイルのディレクトリを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestDir">
            <summary>
            テストの実行の基本ディレクトリを取得します。配置されたファイルと結果ファイルはそのディレクトリに格納されます。
            <see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestRunDirectory"/> と同じであり、代わりにそのプロパティをご使用ください。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestDeploymentDir">
            <summary>
            テストの実行のために配置されたファイルのディレクトリを取得します。通常は、<see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestRunDirectory"/> のサブディレクトリです。
            <see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.DeploymentDirectory"/> と同じであり、代わりにそのプロパティをご使用ください。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestLogsDir">
            <summary>
            テストの実行の結果ファイル用のディレクトリを取得します。通常は、<see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.ResultsDirectory"/> のサブディレクトリです。
            <see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestRunResultsDirectory"/> と同じであり、テストの実行の結果ファイルのそのプロパティを使用するか、
            その代わりにテスト固有の結果ファイルの <see cref="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestResultsDirectory"/> をご使用ください。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            現在実行されているテスト メソッドを含むクラスの完全修飾名を取得します
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            現在実行中のテスト メソッドの名前を取得します
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            現在のテスト成果を取得します。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            テストの実行中にトレース メッセージを書き込むために使用されます
            </summary>
            <param name="message">書式設定されたメッセージ文字列</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            テストの実行中にトレース メッセージを書き込むために使用されます
            </summary>
            <param name="format">書式設定文字列</param>
            <param name="args">引数</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.AddResultFile(System.String)">
            <summary>
            TestResult.ResultFileNames の一覧にファイル名を追加する
            </summary>
            <param name="fileName">
            ファイル名。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.BeginTimer(System.String)">
            <summary>
            指定した名前のタイマーを開始する
            </summary>
            <param name="timerName"> タイマーの名前。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.EndTimer(System.String)">
            <summary>
            指定した名前のタイマーを終了する
            </summary>
            <param name="timerName"> タイマーの名前。</param>
        </member>
    </members>
</doc>
