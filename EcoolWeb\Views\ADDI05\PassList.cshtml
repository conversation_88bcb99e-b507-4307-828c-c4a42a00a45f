@model ADDI05PassListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("PassList", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{

    @Html.HiddenFor(m => m.SearchContents)
    @Html.HiddenFor(m => m.OrderByName)
    @Html.HiddenFor(m => m.OrderRank)
    @Html.HiddenFor(m => m.SyntaxName)
    @Html.HiddenFor(m => m.page)
    @Html.HiddenFor(m => m.DIALOG_ID)

    @Html.HiddenFor(m => m.PassPage)




    @Html.Partial("_ADDI05Menu", 0)

    <div class="form-inline" role="form" id="DivSearch">
        <div class="form-group">
            <label class="control-label">
                @Html.DisplayNameFor(model => model.uADDT13.First().NAME)
                /@Html.DisplayNameFor(model => model.uADDT13.First().CLASS_NO)
                /@Html.DisplayNameFor(model => model.uADDT13.First().SEAT_NO)
            </label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.PassSearchContents, new { @class = "form-control", @placeholder = "搜尋欲瀏覽之相關字串" })
            @Html.HiddenFor(m => m.PassOrderByName)
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        <div class="col-md-3 col-xs-4">
            <div class="input-group input-group-sm">
                <span class="input-group-addon">顯示</span>
                @Html.DropDownList("PassPageSize", (IEnumerable<SelectListItem>)ViewBag.CountNumItem, new { @class = "form-control", @onchange = "FunPageProc(1)" })
            </div>
        </div>
    </div>
    <div style="height:25px">

    </div>

    <div class="row">
        <div class="col-md-8 col-xs-7">
        </div>

        <div class="col-xs-2">
        </div>
    </div>
    <img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-ADDI05">
        <div class="Details">
            <div class="table-responsive">
                <div class="text-center">
                    <div class="Caption_Div_Left">
                        活動名稱：@ViewBag.DIALOG_NAME
                    </div>
                    <table class="table-ecool table-92Per table-hover">

                        <thead>
                            <tr>

                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SCHOOL_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SHORT_NAME)
                                    <img id="A.SCHOOL_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SYEAR');">

                                    @Html.DisplayNameFor(model => model.uADDT13.First().SYEAR)
                                    <img id="A.SYEAR" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SEMESTER');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SEMESTER)
                                    <img id="A.SEMESTER" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().CLASS_NO)
                                    <img id="A.SCHOOL_NO,A.CLASS_NO DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO,SEAT_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SEAT_NO)
                                    <img id="A.SCHOOL_NO,A.CLASS_NO,A.SEAT_NO,A.CRE_DATE DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                                    姓名
                                    <img id="A.SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                                    答對日期
                                    <img id="A.CRE_DATE DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                @if (ViewBag.VisibleLotto == true)
                                {
                                    <th style="text-align: center;cursor:pointer;" onclick="doSort('LOTTO_ORDER');">
                                        @Html.DisplayNameFor(model => model.uADDT13.First().LOTTO_ORDER)
                                        <img id="A.LOTTO_ORDER" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                    </th>
                                }
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('counts');">
                                    答題次數
                                    <img id="A.CRE_DATE DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                @*<th style="text-align: center;cursor:pointer;" onclick="doSort('A.CRE_DATE DESC');">
                                        分數
                                    </th>*@
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.uADDT13)
                            {

                                <tr>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SYEAR)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SEMESTER)
                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                        }

                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                                        }
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE)
                                    </td>
                                    @if (ViewBag.VisibleLotto == true)
                                    {
                                        <td align="center">
                                            @Html.DisplayFor(modelItem => item.LOTTO_ORDER)
                                        </td>
                                    }
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.Count)
                                    </td>
                                    @*<td align="center">
                                            @Html.DisplayFor(modelItem => item.Grade)
                                        </td>*@
                                </tr>
                            }
                        </tbody>
                    </table>
                    <div style="height:15px"></div>
                    <div class="btn-group btn-group-justified" role="group">
                        共 @ViewBag.PassCount 人
                    </div>
                    <div style="height:15px"></div>
                </div>
            </div>
        </div>
    </div>
    <div>
        @Html.Pager(Model.uADDT13.PageSize, Model.uADDT13.PageNumber, Model.uADDT13.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
        .MaxNrOfPages(5)
        .SetPreviousPageText("上頁")
        .SetNextPageText("下頁")
        .AlwaysAddFirstPageNumber()
        )
    </div>
    if (ViewBag.VisibleLotto == true)
    {
        <!-- 匯出Excel區域 -->
        <div class="row export-section">
            <div class="col-md-12 text-center">
                <button type="button" class="btn btn-primary" onclick="ToExcel()">
                    <i class="fa fa-file-excel-o"></i> 匯出Excel
                </button>
            </div>
        </div>

        <!-- 抽獎區域 -->
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="lottery-section" id="DivLottery">
                    <h4 class="text-center" style="margin-bottom: 20px;">
                        <i class="fa fa-gift"></i> 抽獎功能
                    </h4>

                    <div class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">
                                抽獎人數 (範圍 1~@ViewBag.PassCount 人)：
                            </label>
                            <div class="col-sm-3">
                                @Html.TextBoxFor(m => m.LotteryCount, new {
                                    @class = "form-control text-center",
                                    @placeholder = "請輸入人數",
                                    @maxlength = "3"
                                })
                            </div>
                            <div class="col-sm-3">
                                <button type="button" class="btn btn-success btn-block" onclick="ToLotto()">
                                    <i class="fa fa-random"></i> 開始抽獎
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-8 col-sm-offset-4">
                                <small class="text-muted">
                                    <i class="fa fa-info-circle"></i>
                                    提示：請輸入要抽出的得獎者人數，系統將隨機選出得獎者
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }


}





@section css {
    <style>
        /* 輸入錯誤樣式 */
        .input-error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        /* 抽獎區域樣式 */
        .lottery-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .lottery-section:hover {
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .lottery-section h4 {
            color: #28a745;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .lottery-section .fa-gift {
            color: #ffc107;
            margin-right: 8px;
        }

        /* 匯出區域樣式 */
        .export-section {
            margin: 20px 0;
            padding: 15px 0;
            border-top: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }

        .export-section .btn {
            padding: 10px 20px;
            font-size: 16px;
            font-weight: bold;
        }

        /* 按鈕樣式增強 */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            font-weight: bold;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            font-weight: bold;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* 表單控制項樣式 */
        .lottery-section .form-control {
            border-radius: 5px;
            border: 2px solid #ced4da;
            transition: all 0.3s ease;
        }

        .lottery-section .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        /* 提示文字樣式 */
        .text-muted {
            font-size: 13px;
            line-height: 1.4;
        }

        .text-muted .fa-info-circle {
            color: #17a2b8;
            margin-right: 5px;
        }

        /* 響應式設計 */
        @@media (max-width: 768px) {
            .lottery-section {
                margin: 15px 0;
                padding: 20px 15px;
            }

            .lottery-section .form-horizontal .control-label {
                text-align: left;
                margin-bottom: 5px;
            }

            .export-section .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }

        /* 動畫效果 */
        @@keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .lottery-section .btn-success:active {
            animation: pulse 0.3s ease;
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI05_PASS_LIST_URLS = {
            index: '@Url.Action("Index", (string)ViewBag.BRE_NO)',
            detail: '@Url.Action("detail", (string)ViewBag.BRE_NO)',
            passList: '@Url.Action("PassList", (string)ViewBag.BRE_NO)',
            printExcel: '@Url.Action("PrintExcel", (string)ViewBag.BRE_NO)',
            toLotto: '@Url.Action("PassList", (string)ViewBag.BRE_NO)'
        };

        // 設置全局配置
        window.ADDI05_PASS_LIST_CONFIG = {
            passCount: @ViewBag.PassCount
        };
    </script>
    <script src="~/Scripts/ADDI05/common.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/ADDI05/pass.js" nonce="cmlvaw"></script>
}
