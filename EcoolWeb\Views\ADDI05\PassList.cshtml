@model ADDI05PassListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("PassList", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{

    @Html.HiddenFor(m => m.SearchContents)
    @Html.HiddenFor(m => m.OrderByName)
    @Html.HiddenFor(m => m.OrderRank)
    @Html.HiddenFor(m => m.SyntaxName)
    @Html.HiddenFor(m => m.page)
    @Html.HiddenFor(m => m.DIALOG_ID)

    @Html.HiddenFor(m => m.PassPage)




    @Html.Partial("_ADDI05Menu", 0)

    <div class="form-inline" role="form" id="DivSearch">
        <div class="form-group">
            <label class="control-label">
                @Html.DisplayNameFor(model => model.uADDT13.First().NAME)
                /@Html.DisplayNameFor(model => model.uADDT13.First().CLASS_NO)
                /@Html.DisplayNameFor(model => model.uADDT13.First().SEAT_NO)
            </label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.PassSearchContents, new { @class = "form-control", @placeholder = "搜尋欲瀏覽之相關字串" })
            @Html.HiddenFor(m => m.PassOrderByName)
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        <div class="col-md-3 col-xs-4">
            <div class="input-group input-group-sm">
                <span class="input-group-addon">顯示</span>
                @Html.DropDownList("PassPageSize", (IEnumerable<SelectListItem>)ViewBag.CountNumItem, new { @class = "form-control", @onchange = "FunPageProc(1)" })
            </div>
        </div>
    </div>
    <div style="height:25px">

    </div>

    <div class="row">
        <div class="col-md-8 col-xs-7">
        </div>

        <div class="col-xs-2">
        </div>
    </div>
    <img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-ADDI05">
        <div class="Details">
            <div class="table-responsive">
                <div class="text-center">
                    <div class="Caption_Div_Left">
                        活動名稱：@ViewBag.DIALOG_NAME
                    </div>
                    <table class="table-ecool table-92Per table-hover">

                        <thead>
                            <tr>

                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SCHOOL_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SHORT_NAME)
                                    <img id="A.SCHOOL_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SYEAR');">

                                    @Html.DisplayNameFor(model => model.uADDT13.First().SYEAR)
                                    <img id="A.SYEAR" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SEMESTER');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SEMESTER)
                                    <img id="A.SEMESTER" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().CLASS_NO)
                                    <img id="A.SCHOOL_NO,A.CLASS_NO DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO,SEAT_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SEAT_NO)
                                    <img id="A.SCHOOL_NO,A.CLASS_NO,A.SEAT_NO,A.CRE_DATE DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                                    姓名
                                    <img id="A.SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                                    答對日期
                                    <img id="A.CRE_DATE DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                @if (ViewBag.VisibleLotto == true)
                                {
                                    <th style="text-align: center;cursor:pointer;" onclick="doSort('LOTTO_ORDER');">
                                        @Html.DisplayNameFor(model => model.uADDT13.First().LOTTO_ORDER)
                                        <img id="A.LOTTO_ORDER" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                    </th>
                                }
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('counts');">
                                    答題次數
                                    <img id="A.CRE_DATE DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                @*<th style="text-align: center;cursor:pointer;" onclick="doSort('A.CRE_DATE DESC');">
                                        分數
                                    </th>*@
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.uADDT13)
                            {

                                <tr>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SYEAR)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SEMESTER)
                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                        }

                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                                        }
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE)
                                    </td>
                                    @if (ViewBag.VisibleLotto == true)
                                    {
                                        <td align="center">
                                            @Html.DisplayFor(modelItem => item.LOTTO_ORDER)
                                        </td>
                                    }
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.Count)
                                    </td>
                                    @*<td align="center">
                                            @Html.DisplayFor(modelItem => item.Grade)
                                        </td>*@
                                </tr>
                            }
                        </tbody>
                    </table>
                    <div style="height:15px"></div>
                    <div class="btn-group btn-group-justified" role="group">
                        共 @ViewBag.PassCount 人
                    </div>
                    <div style="height:15px"></div>
                </div>
            </div>
        </div>
    </div>
    <div>
        @Html.Pager(Model.uADDT13.PageSize, Model.uADDT13.PageNumber, Model.uADDT13.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
        .MaxNrOfPages(5)
        .SetPreviousPageText("上頁")
        .SetNextPageText("下頁")
        .AlwaysAddFirstPageNumber()
        )
    </div>
    if (ViewBag.VisibleLotto == true)
    {
        <div class="col-md-1">

        </div>
        <div class="col-md-3 col-md-offset-1" style="margin-left:auto;margin-right:auto;text-align:center">
            <button type="button" class="btn btn-default" onclick="ToExcel()">匯出excel</button>
        </div>

        <div class="col-md-8 col-md-offset-1" id="DivLottery" style="margin-left:auto;margin-right:auto;text-align:center;">
            <table>
                <tr>
                    <td valign="top"><label class="control-label" style="font-size:14px;position:relative;top:5px">抽獎人數(範圍 1~ @ViewBag.PassCount 人)：</label></td>
                    <td valign="top">@Html.TextBoxFor(m => m.LotteryCount, new { @style = "width:40px;Height:32px" })</td>
                    <td valign="top"><button type="button" class="btn btn-default" onclick="ToLotto()">開獎</button></td>
                </tr>
            </table>
        </div>

    }


}





@section Scripts {
    <script language="JavaScript">
var targetFormID = "#form1"

    function onGo(ActionVal) {
        if (ActionVal == "Index") {
            form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
        }
        else if (ActionVal == "detail") {
            form1.action = '@Url.Action("detail", (string)ViewBag.BRE_NO)';
        }
        form1.submit();
    }
     function todoClear() {
        $(targetFormID).find("#DivSearch :input,:selected").each(function () {
            var type = $(this).attr('type');
            var InPreadonly = $(this).attr('readonly');
            var tag = this.tagName.toLowerCase();

            if (InPreadonly == false || InPreadonly == undefined) {
                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

         FunPageProc(1);
    }
      function FunPageProc(pageno) {
        form1.PassPage.value = pageno
        form1.submit();
    }

    function ToExcel() {
        var url = '@Url.Action("PrintExcel", (string)ViewBag.BRE_NO).ToString()';
        $(targetFormID).attr('action', url).attr('target', '_blank');
        $(targetFormID).submit();
    }
    
        function doSort(SortCol) {
            var OrderRankItem = "";
            $("#PassOrderByName").val(SortCol);
            if ($("#OrderRank").val() == "") {
                $("#OrderRank").val("desc");
            }
            else {
                OrderRankItem = $("#OrderRank").val();
                switch (OrderRankItem) {
                    case "desc":
                        $("#OrderRank").val("asc");
                        break;
                    case "asc":
                        $("#OrderRank").val("desc");
                        break;
                }
                FunPageProc(1);
            }
       
        }
    function ToLotto() {
        var count = $("#LotteryCount").val();
        if (count == "" || isNaN(count) || count < 1 || count > @ViewBag.PassCount) {
            alert("請輸入1~@ViewBag.PassCount之間的數字");
            return;
        }
        var url = '@Url.Action("ToLotto", (string)ViewBag.BRE_NO).ToString()';
        $(targetFormID).attr('action', url);
        $(targetFormID).submit();
    }
    </script>
}
