﻿@model EcoolWeb.ViewModels.ZZZ23IndexViewModel
@{
    ViewBag.Title = "角色管理查詢";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")



@using (Html.BeginForm("QUERY", "ZZZ23", FormMethod.Post, new { id = "ZZZ23" }))
{
    if (ViewBag.VisableInsert == true)
    {
        <a href='@Url.Action("INSERT", "ZZZ23")' class="btn btn-sm btn-sys">
            <font>新增角色</font>
        </a>
    }


    <br />
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">角色名稱</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control  input-sm" } })
            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.whereUserNo)
            @Html.HiddenFor(m => m.Page)
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <br />
    <br />

    <div class="panel panel-ACC">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            角色名稱
                        </th>
                        <th style="text-align: center;">
                            維護
                        </th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model.HRMT24List)
                    {
                        <tr>
                            <td>
                                @Html.DisplayFor(modelItem => item.ROLE_NAME)
                            </td>

                            <td  style="text-align: center;">
                                @if (ViewBag.VisableMODIFY == true)
                                {
                                    @Html.ActionLink("維護", "MODIFY", new { ROLE_ID = item.ROLE_ID }, new { @class = "btn btn-xs btn-Basic" })
                                }

                                @if (ViewBag.VisableDelete == true)
                                {
                                    @Html.ActionLink("作廢", "Delete", new { ROLE_ID = item.ROLE_ID }, new { @class = "btn btn-xs btn-Basic" });
                                }

                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <div>
        @Html.Pager(Model.HRMT24List.PageSize, Model.HRMT24List.PageNumber, Model.HRMT24List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>

}

@section scripts{
    <script>
            var targetFormID = '#ZZZ23';


            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }
            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

            function todoClear() {
                ////重設
                $(targetFormID).find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }
    </script>
}