﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO

<div class="form-group">
    @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
    </div>
</div>

@{
    Model.CONTENT_TXT = "AWAT01_LOG_LUCKY";
    Model.CASH = 0;
}
@Html.HiddenFor(model => model.CONTENT_TXT)
@Html.HiddenFor(model => model.CASH)

@if (!Model.Individual_Give)
{
    <div class="form-group">
        @Html.LabelFor(model => model.LuckPointCount, htmlAttributes: new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            @Html.EditorFor(model => model.LuckPointCount, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(model => model.LuckPointCount, "", new { @class = "text-danger" })
        </div>
    </div>

}