﻿@{
    ViewBag.Title = "學生首頁";

    Layout = "~/Views/Shared/_Layout.cshtml";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    string userno = EcoolWeb.Models.UserProfileHelper.Get().USER_NO;
    string schoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string[] USerArra = new string[] { "run1", "run2", "run3", "run4", "run5", "run6" };

    if (USerArra.Contains(userno))
    {
        Layout = "~/Views/Shared/_LayoutRun.cshtml";
    }

    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

}
<style>
    .modal-body {
        position: relative;
        padding: 10px;
    }
</style>
@*<div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">酷幣主機停機(暫停服務)公告：</h4>
        </div>
        <div class="modal-body" id="remind-content">
            <ol>
                配合市政府機電檢修停電，酷幣主機預計計於109年1月17日(星期五)下午7時至109年1月19日(星期日)上午9時止暫停服務，不便之處，請包涵。
            </ol>
        </div>
    </div>*@
@if (ViewBag.GetDataListShowYN == true || ViewBag.GetALLDataListShowYN == true)
{
    if (ViewBag.RemindItems != null || ViewBag.ALLRemindItems != null)
    {
        <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">

            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    @if (ViewBag.GetALLDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.ALLRemindItems as List<BDMT02_REF>)
                        {
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT
                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">
                                   
                                        @item.CONTENT_TXT<br />
                                        
                                    
                                </div>
                            }
                        }
                    }
                    @if (ViewBag.GetDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.RemindItems as List<BDMT02_REF>)
                        {

                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT
                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">
                                 
                                        @item.CONTENT_TXT
                                        <br />
                                   
                                </div>
                            }

                        }
                    }
                </div>
            </div>
        </div>
    }

}
<div class="row">

    <div id="show_ZZT36View">
        @Html.Partial("_BET02Partial")
    </div>
    <br />
    <div id="show_ADDT03View">
        @Html.Partial("../ADDT03/_ADDT03")
    </div>
    <br />
    <div id="show_ImgPlayView">
        @Html.Action("_ImgPlay", "ZZZI25")
    </div>
    <br />

    @*<div class="col-md-6" style="height:65px;">
            <a href='@Url.Action("Index", "QAI01")'>
                <img src="~/Content/img/web-student-todo1.png" class="imgEZ" alt="Responsive image" style="max-width:220px" title="待觀看酷課雲影片" />
                &nbsp;<span class="lnkFont2">@ViewBag.QAT02Qty</span>
            </a>
        </div>*@

    @*<div class="col-md-6" style="height:65px;">
            <a href='@Url.Action("Index", "Notice")'>
                <img src="~/Content/img/web-student-todo2.png" class="imgEZ" alt="Responsive image" style="max-width:220px" title="待讀取通知訊息" />
                &nbsp;<span class="lnkFont2">0</span>
            </a>
        </div>*@

    <div class="row show-grid">
        <img src="~/Content/img/web-bar2-revise-08.png" style="width:100%" class="img-responsive " alt="Responsive image" />
        <div class="Div-EZ-Awat">

            <div style="height:35px"></div>
            <div class="p-context table-92Per ">
                @{
                    string ImageUrl = Url.Content(EcoolWeb.Controllers.AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student));
                    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

                    if (((List<AWAT02>)ViewBag.HotAwards).Count == 0)
                    {
                        <div class="text-center">
                            <label class="label_dd_font18">無任何商品</label>
                        </div>
                    }

                    foreach (AWAT02 award in ViewBag.HotAwards as List<AWAT02>)
                    {
                        string aImgUrl = ImageUrl + award.SCHOOL_NO + @"/" + award.IMG_FILE;

                        <div class="row text-center">
                            <div class="col-md-7 col-xs-6 text-center">
                                <div class="form-group img-Product-div">
                                    <img src='@aImgUrl' title="@award.AWARD_NAME" href="@aImgUrl" style="max-width:50%" />
                                </div>
                            </div>
                            <div class="col-md-5 col-xs-6 text-left">
                                <div class="form-group">
                                    <label class="label_dd_font18">@award.AWARD_NAME</label>
                                </div>
                                <div class="form-group">
                                    兌換點數：@award.COST_CASH
                                    <br />
                                    剩餘數量：@award.QTY_STORAGE
                                    <br />
                                    開始日期：@(award.SDATETIME.HasValue ? award.SDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA" )
                                    <br />
                                    兌換期限：@(award.EDATETIME.HasValue ? award.EDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA" )
                                    <br />
                                </div>
                                <div>
                                    @if (award.SCHOOL_NO == "ALL" || Convert.ToDateTime(award.SDATETIME).AddMonths(+1) >= DateTime.Now || award.READ_LEVEL != null
                                    || award.PASSPORT_LEVEL != null)
                                    {
                                        <div class="form-inline">
                                            <div class="form-group">

                                                @if (award.SCHOOL_NO == "ALL")
                                                {
                                                    <img src='~/Content/img/web-revise-prize-03s.png' style="max-height:30px;" />
                                                }

                                                @if (Convert.ToDateTime(award.SDATETIME).AddMonths(+1) >= DateTime.Now)
                                                {
                                                    <img src='~/Content/img/web-student-prize-05.png' style="height:30px;width:30px;max-height:30px;max-width:30px" />
                                                }

                                                @if (award.READ_LEVEL != null)
                                                {
                                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgReadUrl(award.READ_LEVEL))" style="max-height:30px;margin-right:5px">
                                                }

                                                @if (award.PASSPORT_LEVEL != null)
                                                {
                                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgPassportUrl(award.PASSPORT_LEVEL))" style="max-height:30px;margin-right:5px">
                                                }
                                            </div>
                                        </div>
                                        <div style="height:12px;"></div>
                                    }

                                    <div class="form-group text-left">

                                        @if (null != user && user.USER_TYPE == "S")
                                        {
                                            string NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange(award, user);

                                            if (string.IsNullOrWhiteSpace(NG) == false)
                                            {
                                                <button role="button" class="btn-default btn btn-xs disabled" disabled>
                                                    無法兌換 -
                                                    @Html.Raw(HttpUtility.HtmlDecode(NG))
                                                </button>

                                            }
                                            else
                                            {
                                                <a onclick="funGetExchange('@award.AWARD_NO')" role="button" class="btn-default btn btn-xs">
                                                    我要兌換
                                                </a>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="height:12px;"></div>
                        <div style="height:12px;"></div>
                    }

                }
            </div>
            <div class="row text-right">
                <div class="col-md-11">
                    <a href='@Url.Action("AwatQ02", "Awat")' role="button" class="btn btn-link-ez btn-xs">
                        more
                    </a>
                </div>
            </div>
            <div style="height:12px;"></div>
        </div>
    </div>
</div>
<div style="height:20px;"></div>
@using (Html.BeginForm("AwatExchange02", "AWAI01", FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    @Html.Hidden("Search.WhereAWARD_NO")
    @Html.Hidden("Search.WhereSouTable", AWAI01SearchViewModel.SouTableVal.Student)
    @Html.Hidden("Search.SouController", "Home")
    @Html.Hidden("Search.SouAction", "StudentIndex")
    @Html.Hidden("Search.BackAction", "AwatQ02")
    @Html.Hidden("Search.BackController", "AWAI01")
}

@{
    // 處理ViewBag轉json
    var jss = new System.Web.Script.Serialization.JavaScriptSerializer();
    var toastInfoJson = jss.Serialize(ViewBag.ToastData as List<APPT02>);

}

@section scripts{
    <script src="@Url.Content("~/Scripts/toastr/toastr.min.js")"></script>
    <script type="text/javascript">
        var targetFormID = '#form1';
        window.onload = function () {
            RemindShow()
        }
        function RemindShow() {
            var remind_font = $("#remind-content").text().length;

            if (remind_font > 0) {
                $('#remind-modal').modal('show');
            }
        }
        function funGetExchange(AWARD_NO) {
            $('#Search_WhereAWARD_NO').val(AWARD_NO);
            $(targetFormID).attr("action", "@Url.Action("AwatExchange02", "AWAI01")")
            $(targetFormID).submit();
        }

        if ('@UUID') {

        } else {
            // 不是手機才toast
            //use Json.parse to convert string to Json
            var toastInfo = JSON.parse('@Html.Raw(toastInfoJson)');
            var toPath = "";
            $.each(toastInfo, function (idx, item) {
                // toastr: 推送網頁 notification
                toastr.options = {
                    "closeButton": true,
                    "debug": false,
                    "newestOnTop": false,
                    "progressBar": false,
                    "positionClass": "toast-bottom-right",
                    "preventDuplicates": false,
                    "onclick": null,
                    "showDuration": "300",
                    "hideDuration": "1000",
                    "timeOut": "0",
                    "extendedTimeOut": "0",
                    "showEasing": "swing",
                    "hideEasing": "linear",
                    "showMethod": "fadeIn",
                    "hideMethod": "fadeOut",
                    "tapToDismiss": false,
                    "onHidden": function () {
                        console.log('remove');
                        // close: 呼叫表示已讀api
                        $.ajax({
                            url: '@Url.Content("~/Home/WebToastUpRead")',
                            type: 'POST',
                            contentType: "application/json; charset=utf-8",
                            dataType: 'json',
                            data: JSON.stringify({
                                SCHOOL_NO: '@Html.Raw(schoolNO)',
                                USER_NO: '@Html.Raw(userno)',
                                NOTIFICATION_ID: item.NOTIFICATION_ID,
                            }),
                            success: function (data) {

                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                            },
                            complete: function () {
                            }
                        });
                    },
                }

                if (item.TO_PATH) {
                    toPath = "<br><br><a href='@Url.Content("~/")" + item.TO_PATH + "' class='btn btn-success btn-block'>前往</a>";
                }
                toastr["success"](item.BODY_TXT + toPath);
            });
        }
    </script>
}

@section css{
    <link href="@Url.Content("~/Scripts/toastr/toastr.min.css")" rel="stylesheet" />
    <style>
    </style>
}