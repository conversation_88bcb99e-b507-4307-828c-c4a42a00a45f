/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Operators/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Operators={directory:"Operators/Regular",family:"AsanaMathJax_Operators",testString:"\u2206\u220A\u220C\u220E\u220F\u2210\u2211\u221B\u221C\u221F\u222C\u222D\u222E\u222F\u2230",32:[0,0,249,0,0],8710:[697,4,688,27,662],8714:[482,3,511,66,446],8716:[648,107,563,55,509],8718:[406,0,508,52,457],8719:[626,311,994,54,941],8720:[626,311,994,54,941],8721:[620,310,850,62,788],8731:[1048,59,739,63,772],8732:[1045,59,739,63,771],8735:[368,0,498,65,434],8748:[885,442,1132,54,1058],8749:[885,442,1496,54,1422],8750:[885,442,768,54,694],8751:[885,442,1132,54,1058],8752:[885,442,1496,54,1422],8753:[885,442,787,54,713],8754:[885,442,787,54,713],8755:[885,442,787,54,713],8758:[518,-23,249,66,184],8759:[518,-23,570,76,495],8760:[538,-286,668,65,604],8761:[518,-23,890,65,806],8762:[518,-23,668,65,604],8763:[518,-23,668,58,610],8766:[422,-123,729,32,706],8767:[587,3,784,34,750],8772:[596,55,668,65,604],8775:[596,55,668,65,604],8777:[596,55,668,58,611],8779:[614,-14,668,53,614],8780:[587,-134,668,58,610],8788:[518,-23,890,85,826],8789:[518,-23,890,65,806],8792:[587,-134,668,62,604],8793:[646,-134,687,65,623],8794:[646,-134,687,65,623],8795:[652,-134,687,65,623],8797:[658,-134,687,65,623],8798:[632,-134,687,65,623],8799:[751,-134,687,65,623],8802:[596,55,668,65,604],8803:[566,27,668,65,604],8813:[596,55,668,54,616],8820:[712,171,668,65,604],8821:[712,171,668,65,604],8824:[712,171,668,65,604],8825:[712,171,668,65,604],8836:[648,107,668,55,615],8837:[648,107,668,55,615],8844:[603,0,687,65,623],8845:[603,0,687,65,623],8860:[587,46,668,18,652],8870:[541,0,490,65,425],8871:[620,-1,709,85,624],8875:[541,0,748,64,684],8880:[652,118,748,75,673],8881:[652,118,748,75,674],8886:[446,-94,1363,65,1299],8887:[446,-94,1363,65,1299],8889:[505,-5,687,96,598],8893:[620,78,687,65,623],8894:[410,0,535,63,473],8895:[368,0,498,65,434],8896:[626,313,897,86,813],8897:[626,313,897,86,813],8898:[626,313,897,86,812],8899:[626,313,897,86,812],8903:[547,5,668,59,611],8917:[714,177,641,65,604],8924:[615,74,668,65,604],8925:[615,74,668,65,604],8930:[712,171,668,55,615],8931:[712,171,668,55,615],8932:[602,114,668,55,615],8933:[602,114,668,55,615],8944:[570,14,774,95,680],8946:[580,-22,876,53,824],8947:[533,-8,563,55,509],8948:[482,3,511,66,478],8949:[618,79,563,55,509],8950:[597,55,563,55,509],8951:[583,42,511,66,446],8952:[597,55,563,55,509],8953:[533,-8,563,55,509],8954:[580,-22,876,53,824],8955:[533,-8,563,55,509],8956:[482,3,511,66,478],8957:[597,55,563,55,509],8958:[583,42,511,66,446],8959:[697,0,617,46,572],10752:[830,316,1320,86,1235],10753:[833,316,1320,86,1235],10754:[833,316,1320,86,1235],10755:[741,198,897,86,812],10756:[741,198,897,86,812],10757:[734,192,897,86,812],10758:[734,192,897,86,812],10759:[626,313,1035,86,950],10760:[626,313,1035,86,950],10761:[734,192,1098,86,1013],10762:[882,434,1158,60,1069],10763:[885,442,850,27,764],10764:[885,442,1860,54,1786],10765:[885,442,768,54,694],10766:[885,442,768,54,694],10767:[885,442,768,54,694],10768:[885,442,768,54,694],10769:[885,442,810,54,736],10770:[885,442,768,54,694],10771:[885,442,768,54,694],10772:[885,442,768,54,694],10773:[885,442,768,54,694],10774:[885,442,768,54,694],10775:[885,442,1005,52,936],10776:[885,442,768,54,694],10777:[885,442,768,54,694],10778:[885,442,768,54,694],10779:[994,442,775,54,701],10780:[994,442,775,54,701],10781:[515,-23,758,65,694],10782:[535,-6,668,65,604],10783:[703,355,552,16,521],10784:[556,10,826,48,770],10785:[714,171,524,233,478],10786:[672,129,668,65,604],10787:[609,68,668,65,604],10788:[631,88,668,65,604],10789:[538,180,668,65,604],10790:[538,178,668,65,604],10791:[538,95,668,65,604],10792:[538,0,668,65,604],10793:[570,-233,605,51,555],10794:[289,-74,605,51,555],10795:[492,-30,605,51,555],10796:[492,-30,605,51,555],10797:[587,52,602,26,571],10798:[587,52,602,26,571],10799:[489,-53,554,59,496],10800:[688,5,668,59,611],10801:[545,142,668,59,611],10802:[547,5,760,58,702],10803:[554,11,671,53,619],10804:[587,52,603,54,550],10805:[587,52,603,54,550],10806:[634,192,668,18,652],10807:[587,46,668,18,652],10808:[587,46,668,18,652],10809:[559,18,666,44,623],10810:[559,18,666,44,623],10811:[559,18,666,44,623],10812:[360,-88,672,65,608],10813:[360,-88,672,65,608],10814:[703,166,396,54,344],10816:[573,30,687,65,623],10817:[573,30,687,65,623],10818:[634,91,687,65,623],10819:[634,91,687,65,623],10820:[578,25,687,65,623],10821:[578,25,687,65,623],10822:[622,80,407,64,344],10823:[622,80,407,64,344],10824:[622,80,407,64,344],10825:[622,80,407,64,344],10826:[422,-120,659,64,596],10827:[422,-120,659,64,596],10828:[601,58,779,64,716],10829:[601,58,779,64,716],10830:[559,17,687,65,623],10831:[559,17,687,65,623],10832:[601,58,779,64,716],10833:[570,29,537,57,481],10834:[570,29,537,57,481],10835:[563,22,687,65,623],10836:[563,22,687,65,623],10837:[563,22,836,65,772],10838:[563,22,836,65,772],10839:[598,42,670,66,605],10840:[598,41,669,66,604],10841:[621,79,687,65,623],10842:[563,22,687,65,623],10843:[563,22,687,65,623],10844:[563,22,687,65,623],10845:[563,22,687,65,623],10847:[720,27,687,65,623],10848:[640,267,687,65,623],10849:[497,-45,687,65,623],10850:[636,262,687,65,623],10851:[645,262,687,65,623],10852:[535,-6,668,65,604],10853:[535,-6,668,65,604],10854:[445,19,668,65,604],10855:[571,29,668,65,604],10856:[540,0,668,65,604],10857:[540,0,668,65,604],10858:[429,-113,668,58,611],10859:[500,-41,668,58,611],10860:[514,-14,668,56,614],10861:[581,39,668,65,604],10862:[530,-12,668,65,604],10863:[649,-51,668,58,611],10864:[596,55,668,65,604],10865:[667,126,668,66,604],10866:[667,126,668,66,604],10867:[507,-35,668,65,604],10868:[518,-23,1092,85,1028],10869:[406,-134,1347,85,1263],10870:[406,-134,1986,85,1902],10871:[599,58,668,65,604],10872:[567,25,668,65,604],10873:[535,-5,668,65,604],10874:[535,-5,668,65,604],10875:[623,82,668,65,604],10876:[623,82,668,65,604],10879:[615,74,668,65,604],10880:[615,74,668,65,604],10881:[615,74,668,65,604],10882:[615,74,668,65,604],10883:[700,159,668,65,604],10884:[700,159,668,65,604],10893:[672,186,668,65,604],10894:[672,186,668,65,604],10895:[821,279,668,65,604],10896:[821,279,668,65,604],10897:[755,159,668,65,604],10898:[755,159,668,65,604],10899:[944,279,668,65,604],10900:[944,279,668,65,604],10903:[615,74,668,65,604],10904:[615,74,668,65,604],10905:[672,131,668,65,604],10906:[672,131,668,65,604],10907:[701,147,668,66,605],10908:[701,147,668,66,605],10909:[605,122,668,65,604],10910:[605,122,668,65,604],10911:[801,193,668,65,604],10912:[801,193,668,65,604],10913:[535,-5,668,65,604],10914:[535,-5,668,65,604],10915:[606,61,965,55,912],10916:[535,-5,768,56,713],10917:[535,-5,1251,55,1198],10918:[535,-7,725,64,661],10919:[535,-7,725,64,662],10920:[613,74,725,64,661],10921:[613,74,725,64,662],10922:[553,5,713,65,649],10923:[553,5,713,65,649],10924:[635,61,713,65,649],10925:[635,61,713,65,649],10926:[550,8,668,65,604],10929:[623,134,668,65,604],10930:[623,134,668,65,604],10931:[680,139,668,65,604],10932:[680,139,668,65,604],10939:[553,14,1057,65,993],10940:[553,14,1057,65,993],10941:[533,-8,668,55,615],10942:[533,-8,668,55,615],10943:[588,46,465,65,401],10944:[588,46,465,65,401],10945:[623,81,465,65,401],10946:[623,81,465,65,401],10947:[645,103,607,65,543],10948:[645,103,607,65,543],10951:[656,115,668,55,615],10952:[656,115,668,55,615],10953:[739,227,668,55,615],10954:[739,227,668,55,615],10957:[543,-2,1145,64,1082],10958:[543,-2,1145,64,1082],10959:[533,-8,668,55,615],10960:[533,-8,668,55,615],10961:[603,61,668,55,615],10962:[603,61,668,55,615],10963:[611,69,407,53,355],10964:[611,69,407,53,355],10965:[611,69,407,53,355],10966:[611,69,407,53,355],10967:[410,-130,764,53,711],10968:[410,-130,764,53,711],10969:[498,-44,613,45,569],10970:[656,115,687,65,623],10971:[771,150,687,65,623],10972:[648,107,687,65,623],10973:[571,31,687,65,623],10974:[541,0,400,65,337],10975:[408,-136,670,65,607],10976:[408,-136,670,65,607],10977:[579,0,748,65,684],10978:[580,39,748,85,664],10979:[580,39,859,85,795],10980:[580,39,728,85,664],10981:[580,39,859,85,795],10982:[580,39,730,87,666],10983:[473,-70,670,65,607],10984:[473,-70,670,65,607],10985:[579,37,670,65,607],10986:[559,20,748,65,684],10987:[559,20,748,65,684],10988:[407,-135,672,65,608],10989:[407,-135,672,65,608],10990:[714,171,437,0,438],10991:[715,173,521,85,437],10992:[714,174,521,85,437],10993:[714,174,560,65,496],10994:[714,171,644,70,575],10995:[714,171,668,58,611],10996:[714,171,560,61,500],10997:[714,171,691,65,627],10998:[709,164,286,85,202],10999:[535,-7,668,65,604],11000:[535,-7,668,65,604],11001:[695,153,668,66,605],11002:[695,153,668,66,605],11003:[714,169,885,65,821],11004:[763,222,620,71,550],11005:[714,169,673,65,609],11006:[541,0,383,64,320],11007:[654,112,383,64,320]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Operators"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Operators/Regular/Main.js"]);
