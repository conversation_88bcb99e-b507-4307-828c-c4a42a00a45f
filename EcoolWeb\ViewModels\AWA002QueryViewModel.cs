﻿using ECOOL_APP.EF.Inherit;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWA002QueryViewModel
    {
         /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }
        public int SkipPage { get; set; }
        public string whereLOG_TIME_S { get; set; }

        public string whereLOG_TIME_E { get; set; }

        public bool whereIsExcel { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<HRMT02QTY> VAWA002List;

        public AWA002QueryViewModel()
        {
            Page = 0;
            OrdercColumn = "SumCASH_IN";
        }
    }
}