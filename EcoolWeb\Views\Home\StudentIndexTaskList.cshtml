﻿@{
    ViewBag.Title = "學生首頁";

    Layout = "~/Views/Shared/_Layout.cshtml";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    string userno = EcoolWeb.Models.UserProfileHelper.Get().USER_NO;
    string schoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string[] USerArra = new string[] { "run1", "run2", "run3", "run4", "run5", "run6" };

    if (USerArra.Contains(userno))
    {
        Layout = "~/Views/Shared/_LayoutRun.cshtml";
    }

    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

}
<style>
    .modal-body {
        position: relative;
        padding: 10px;
    }
</style>
@Html.Action("_StudentTaskList", "Home")
@*<div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">酷幣主機停機(暫停服務)公告：</h4>
        </div>
        <div class="modal-body" id="remind-content">
            <ol>
                配合市政府機電檢修停電，酷幣主機預計計於109年1月17日(星期五)下午7時至109年1月19日(星期日)上午9時止暫停服務，不便之處，請包涵。
            </ol>
        </div>
    </div>*@