/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/fontdata-beta.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(e,k){e.FONTDATA.STIXversion="1.0-beta";var p="STIXGeneral",n="STIXGeneral-bold",l="STIXGeneral-italic",d="STIXSizeOneSym",c="STIXSizeTwoSym",b="STIXSizeThreeSym",a="STIXSizeFourSym",q="STIXSizeFiveSym";var m=function(i,r){var j=e.FONTDATA.FONTS[i];for(var s in r){if(r.hasOwnProperty(s)){j[s]=r[s]}}};m(p,{768:[678,-507,0,109,333],769:[678,-507,0,109,333],770:[674,-507,0,94,405],771:[638,-532,0,85,415],772:[601,-547,0,95,406],773:[820,-770,0,0,500],774:[664,-507,0,107,388],775:[622,-523,0,200,299],776:[622,-523,0,101,399],777:[751,-492,0,173,362],778:[711,-512,0,151,350],779:[678,-507,0,79,458],780:[674,-507,0,95,406],781:[700,-500,0,230,285],782:[700,-500,0,154,347],783:[678,-507,0,79,458],784:[767,-507,0,107,388],785:[664,-507,0,107,388],786:[745,-502,0,181,320],787:[745,-502,0,181,320],788:[745,-502,0,181,320],789:[745,-502,0,395,534],790:[-53,224,0,109,333],791:[-53,224,0,109,333],792:[-53,283,0,83,270],793:[-53,283,0,83,270],794:[735,-531,0,100,400],795:[474,-345,0,436,531],796:[-71,266,0,120,248],797:[-53,240,0,135,365],798:[-53,240,0,135,365],799:[-53,250,0,154,346],800:[-124,168,0,154,346],801:[75,287,0,245,481],802:[75,287,0,426,662],803:[-118,217,0,200,299],804:[-119,218,0,101,399],805:[-69,268,0,151,350],806:[-110,353,0,181,320],807:[0,215,0,146,355],808:[0,165,0,158,343],809:[-102,234,0,230,270],810:[-98,235,0,95,407],811:[-110,227,0,100,405],812:[-73,240,0,95,406],813:[-73,240,0,95,406],814:[-68,225,0,110,391],815:[-59,216,0,110,391],816:[-113,219,0,85,415],817:[-141,195,0,95,406],818:[-141,191,0,0,500],819:[-141,300,0,0,500],820:[320,-214,0,79,409],821:[274,-230,0,96,402],822:[274,-230,0,0,500],823:[580,74,0,100,439],824:[662,156,0,100,511],825:[-71,266,0,120,248],826:[-53,190,0,95,407],827:[-53,227,0,167,333],828:[-65,189,0,100,401],829:[715,-525,0,154,345],830:[829,-499,0,197,303],831:[928,-770,0,0,500],838:[681,-538,0,0,282],844:[777,-532,0,94,424],857:[-65,367,0,123,393],860:[-76,233,0,107,775],864:[633,-517,0,85,845],865:[664,-507,0,107,775],866:[-65,270,0,85,835],8400:[760,-627,0,27,463],8401:[760,-627,0,27,463],8402:[662,156,0,238,288],8406:[760,-548,0,27,463],8407:[760,-548,0,27,463],8411:[622,-523,0,18,515],8412:[622,-523,0,0,696],8413:[725,221,1000,27,973],8414:[780,180,0,0,960],8415:[843,341,0,0,1184],8417:[760,-548,0,27,505],8420:[1023,155,1510,25,1485],8421:[662,156,0,50,440],8422:[662,156,0,145,378],8423:[725,178,0,0,816],8424:[-119,218,0,18,515],8425:[681,-538,0,0,533],8426:[419,-87,0,-178,598],8427:[756,217,0,32,673],8428:[-119,252,0,27,463],8429:[-119,252,0,27,463],8430:[-40,252,0,27,463],8431:[-40,252,0,27,463],8432:[819,-517,0,123,393]});m(n,{768:[713,-528,0,141,379],769:[713,-528,0,141,379],770:[704,-528,0,92,429],771:[674,-547,0,78,443],772:[637,-565,0,95,425],773:[838,-788,0,10,510],774:[691,-528,0,109,412],775:[666,-537,0,196,325],776:[666,-537,0,91,430],777:[751,-491,0,174,379],778:[750,-537,0,154,367],779:[713,-528,0,41,479],780:[704,-528,0,92,429],781:[730,-530,0,233,299],782:[730,-530,0,152,368],783:[713,-528,0,41,479],784:[828,-528,0,109,412],785:[691,-528,0,109,412],786:[867,-532,0,168,352],787:[867,-532,0,168,352],788:[867,-532,0,168,352],789:[867,-532,0,394,578],790:[-70,255,0,141,379],791:[-70,255,0,141,379],792:[-58,288,0,85,287],793:[-58,288,0,232,434],794:[752,-531,0,100,417],795:[505,-352,0,448,576],796:[-33,313,0,135,320],797:[-70,272,0,145,375],798:[-70,272,0,145,375],799:[-70,287,0,154,366],800:[-140,206,0,154,366],801:[75,287,0,269,488],802:[75,287,0,416,635],803:[-109,238,0,196,325],804:[-109,238,0,91,430],805:[-66,279,0,154,367],806:[-88,423,0,168,352],807:[0,218,0,147,373],808:[44,173,0,146,375],809:[-107,239,0,233,288],810:[-86,260,0,85,417],811:[-104,242,0,90,415],812:[-83,259,0,92,429],813:[-85,261,0,92,429],814:[-78,241,0,109,412],815:[-78,241,0,109,412],816:[-108,235,0,78,443],817:[-137,209,0,95,425],818:[-137,187,0,10,510],819:[-137,287,0,10,510],820:[316,-189,0,78,443],821:[282,-224,0,96,402],822:[282,-224,0,0,500],823:[580,74,0,100,467],824:[662,156,0,100,541],825:[-33,313,0,135,320],826:[-71,245,0,85,417],827:[-70,264,0,157,343],828:[-89,234,0,100,401],829:[719,-520,0,160,360],830:[881,-516,0,196,323],831:[938,-788,0,10,510],838:[717,-544,0,0,303],844:[837,-547,333,-16,349],857:[-66,368,0,151,421],860:[-79,242,0,109,810],864:[674,-529,0,78,908],865:[691,-534,0,107,775],866:[-54,293,0,78,887],8400:[846,-637,0,40,524],8401:[846,-637,0,40,524],8402:[662,156,0,232,307],8406:[846,-508,0,40,524],8407:[846,-508,0,40,524],8411:[666,-537,0,-2,547],8412:[666,-537,0,-2,757],8413:[760,254,1073,32,1041],8417:[846,-508,0,35,629],8420:[1055,169,1581,32,1549],8421:[662,155,0,40,522],8422:[662,156,0,130,409],8423:[760,172,0,67,910],8424:[-109,238,0,-2,547],8425:[717,-544,0,0,564],8426:[441,-65,1073,-178,658],8427:[775,235,0,25,738],8428:[-166,375,0,40,524],8429:[-166,375,0,40,524],8430:[-35,373,0,40,524],8431:[-35,373,0,40,524],8432:[845,-543,0,125,395]});m(l,{8400:[760,-627,0,27,463],8401:[760,-627,0,54,490],8402:[662,156,266,100,166],8406:[760,-548,0,27,463],8407:[760,-548,0,27,463],8411:[622,-523,0,27,524],8412:[622,-523,0,18,714],8413:[725,221,1000,27,973],8417:[760,-548,0,27,505],8420:[1023,155,1510,25,1485],8421:[662,156,506,50,456],8422:[662,156,523,129,394],8423:[725,178,926,55,871],8424:[-119,218,0,18,515],8425:[681,-538,0,2,535],8426:[419,-87,1000,27,973],8428:[681,-548,0,27,463],8429:[681,-548,0,27,463],8430:[-40,252,490,27,463],8431:[-40,252,490,27,463]});m(d,{770:[767,-554,0,0,560],771:[750,-598,0,-2,558],773:[820,-770,0,0,1000],780:[767,-554,0,0,560],816:[-117,269,0,-2,558],818:[-127,177,0,0,1000],824:[532,21,0,0,563],8400:[749,-584,0,28,899],8401:[749,-584,0,27,898],8406:[735,-482,0,27,899],8407:[736,-482,0,27,899],8428:[-123,288,0,27,898],8429:[-123,288,0,28,899],8430:[-26,279,0,27,899],8431:[-25,279,0,27,899]});m(c,{770:[777,-564,0,0,979],771:[760,-608,0,-2,977],773:[820,-770,0,0,1500],780:[777,-564,0,0,979],816:[-117,269,0,-2,977],818:[-127,177,0,0,1000],824:[662,0,0,0,714],8400:[749,-584,0,27,1335],8401:[749,-584,0,27,1335],8406:[735,-482,0,27,1335],8407:[735,-482,0,27,1335],8428:[-123,288,0,27,1335],8429:[-123,288,0,27,1335],8430:[-26,279,0,27,1335],8431:[-26,279,0,27,1335]});m(b,{770:[777,-564,0,0,1460],771:[774,-608,0,-2,1458],773:[820,-770,0,0,2000],780:[777,-564,0,0,1460],816:[-117,283,0,-2,1458],818:[-127,177,0,0,2000],824:[662,156,0,137,548],8400:[749,-584,0,27,1771],8401:[749,-584,0,27,1771],8406:[735,-482,0,27,1771],8407:[735,-482,0,27,1771],8428:[-123,288,0,27,1771],8429:[-123,288,0,27,1771],8430:[-26,279,0,27,1771],8431:[-26,279,0,27,1771]});m(a,{770:[796,-573,0,0,1886],771:[771,-608,0,0,1886],773:[820,-770,0,0,2500],780:[796,-573,0,0,1886],816:[-117,280,0,0,1886],818:[-127,177,0,0,2500],824:[731,228,0,170,491],8400:[749,-584,0,27,2207],8401:[749,-584,0,27,2207],8406:[735,-482,0,27,2207],8407:[735,-482,0,27,2207],8428:[-123,288,0,27,2207],8429:[-123,288,0,27,2207],8430:[-26,279,0,27,2207],8431:[-26,279,0,27,2207]});m(q,{770:[816,-572,0,0,2328],771:[780,-617,0,0,2328],773:[820,-770,0,0,3000],780:[816,-572,0,0,2328],816:[-117,280,0,0,2328],818:[-127,177,0,0,3000],824:[960,454,0,119,557],8400:[749,-584,0,0,3000],8401:[749,-584,0,0,3000],8406:[735,-482,0,0,3000],8407:[735,-482,0,0,3000],8428:[-123,288,0,0,3000],8429:[-123,288,0,0,3000],8430:[-26,279,0,0,3000],8431:[-26,279,0,0,3000]});MathJax.Hub.Register.LoadHook(e.fontDir+"/General/BoldItalic/CombDiactForSymbols.js",function(){m("STIXGeneral-bold-italic",{8413:[760,254,1073,32,1041]})});var o=[d,c,b,a];var g=[710,711,732,743];for(var h=0;h<o.length;h++){for(var f=0;f<g.length;f++){delete e.FONTDATA.FONTS[o[h]][f]}}MathJax.Ajax.loadComplete(e.fontDir+"/fontdata-beta.js")})(MathJax.OutputJax["HTML-CSS"],MathJax.InputJax.TeX);
