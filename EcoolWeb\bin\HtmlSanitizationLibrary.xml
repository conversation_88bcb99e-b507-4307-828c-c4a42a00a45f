<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HtmlSanitizationLibrary</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Exchange.Data.Globalization.Charset">
            <summary>
            Represents a character set
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.Charset.available">
            <summary>
            Flag indicating if the character set is available.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.Charset.encoding">
            <summary>
            The character set encoding.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.Charset.mapIndex">
            <summary>
            The character set map index.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.Charset"/> class.
            </summary>
            <param name="codePage">
            The code page number.
            </param>
            <param name="name">
            The name of the character set.
            </param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.TryGetCharset(System.String,Microsoft.Exchange.Data.Globalization.Charset@)">
            <summary>
            Looks up the passed-in character set name and sets the corresponding character set.
            </summary>
            <param name="name">
            The name of the character set to find.
            </param>
            <param name="charset">
            The character set associated with the key.
            </param>
            <returns>
            True if the character set is found, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.TryGetCharset(System.Int32,Microsoft.Exchange.Data.Globalization.Charset@)">
            <summary>
            Looks up the passed-in code page and sets the corresponding character set.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <param name="charset">
            The character set associated with the key.
            </param>
            <returns>
            True if a character set for the key is found, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.TryGetEncoding(System.Int32,System.Text.Encoding@)">
            <summary>
            Looks up the passed-in code page and sets the corresponding encoding.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <param name="encoding">
            The encoding asscoiated with the key.
            </param>
            <returns>
            True if a character set for the key is found, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.TryGetEncoding(System.String,System.Text.Encoding@)">
            <summary>
            Looks up the passed in character set name and sets the corresponding encoding.
            </summary>
            <param name="name">
            The character set name.
            </param>
            <param name="encoding">
            The encoding associated with the key.
            </param>
            <returns>
            True if a character set for the key is found, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.GetEncoding(System.Int32)">
            <summary>
            Gets the encoding for the specified code page.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <returns>
            The <see cref="T:System.Text.Encoding"/> for the specified code page.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.GetCharset(System.Int32)">
            <summary>
            Gets the character set for the given code page.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <returns>
            The character set for the given code page.
            </returns>
            <exception cref="T:Microsoft.Exchange.Data.Globalization.InvalidCharsetException">
            Thrown if the character set is not found.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.TryGetEncoding(System.Text.Encoding@)">
            <summary>
            Looks up the encoding class for this character set.
            </summary>
            <param name="attemptedEncoding">
            The encoding class created if one is available.
            </param>
            <returns>
            True if encoding is available, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Charset.GetEncoding">
            <summary>
            Gets the <see cref="T:System.Text.Encoding"/> for this character set.
            </summary>
            <returns>
            The <see cref="T:System.Text.Encoding"/> class for this character set.
            </returns>
            <exception cref="T:Microsoft.Exchange.Data.Globalization.CharsetNotInstalledException">
            Thrown if an encoding class cannot be found for this character set.
            </exception>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Charset.CodePage">
            <summary>
            Gets or sets the character set number
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Charset.Name">
            <summary>
            Gets or sets the character set name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Charset.Culture">
            <summary>
            Gets or sets  the character set culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Charset.IsDetectable">
            <summary>
            Gets a value indicating whether the character set is detectable.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Charset.IsWindowsCharset">
            <summary>
            Gets or sets a value indicating whether the character set is a Windows character set.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Charset.Description">
            <summary>
            Gets or sets the description for the character set.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Charset.MapIndex">
            <summary>
            Gets or sets the map index for this character set.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CharsetNotInstalledException">
            <summary>
            The exception thrown when a character set which is not installed is used.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.InvalidCharsetException">
            <summary>
            Exception thrown when an invalid character set is used.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.ExchangeDataException">
            <summary>
            Thrown when a data exception occurs.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.ExchangeDataException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.ExchangeDataException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.ExchangeDataException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.ExchangeDataException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.ExchangeDataException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.ExchangeDataException"/> class.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.InvalidCharsetException.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.InvalidCharsetException"/> class.
            </summary>
            <param name="codePage">The code page.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.InvalidCharsetException.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.InvalidCharsetException"/> class.
            </summary>
            <param name="codePage">The code page.</param>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.InvalidCharsetException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.InvalidCharsetException"/> class.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CharsetNotInstalledException.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CharsetNotInstalledException"/> class.
            </summary>
            <param name="codePage">The code page.</param>
            <param name="message">The message.</param>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageDetectData">
            <summary>
            Encapsulates code page detection data.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CodePageDetectData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CodePageDetectData"/> class.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageDetectData.CodePages">
            <summary>
            The list of code pages and their masks.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageDetectData.CodePage">
            <summary>
            Represents a code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageDetectData.CodePage.Id">
            <summary>
            The code page identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageDetectData.CodePage.Mask">
            <summary>
            The Mask for this codepage.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageDetectData.CodePage.IsWindowsCodePage">
            <summary>
            True if the codepage is a windows codepage, otherwise false.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CodePageDetectData.CodePage.#ctor(System.UInt16,System.UInt32,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CodePageDetectData.CodePage"/> struct.
            </summary>
            <param name="id">The code page identifier.</param>
            <param name="mask">The code page Mask.</param>
            <param name="isWindowsCodePage">if set to <c>true</c> the code page is a Windows codepage..</param>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageMap">
            <summary>
            Represents the logic to choose a codepage.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageMapData">
            <summary>
            Contains the data and functions necessary to map a code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.AllKnownCodePages">
            <summary>
            An array holding all the known codepages.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CharacterBitmap">
            <summary>
            Character bitmaps.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePages">
            <summary>
            Gets an array containing all the known code pages.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CodePageMapData.Bitmap">
            <summary>
            Gets the character bitmap.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange">
            <summary>
            Represents the range of the code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange.First">
            <summary>
            The start of the code page range.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange.Last">
            <summary>
            The end of the code page range.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange.Offset">
            <summary>
            The offset for the range.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange.Mask">
            <summary>
            The range mask.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange.#ctor(System.UInt16,System.UInt16,System.UInt16,System.Byte)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange"/> struct.
            </summary>
            <param name="first">The start of the code page range.</param>
            <param name="last">The end of the code page range.</param>
            <param name="offset">The code page offset.</param>
            <param name="mask">The code page mask.</param>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage">
            <summary>
            Represents information about a code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.Ranges">
            <summary>
            The ranges for the code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.Kind">
            <summary>
            The type of the code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.AsciiSupport">
            <summary>
            The ASCII support for the code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.UnicodeCoverage">
            <summary>
            The Unicode coverage for the code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.Flags">
            <summary>
            Type flags for the code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.DetectId">
            <summary>
            The detectable code page identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.Id">
            <summary>
            The code page identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.WindowsId">
            <summary>
            The Windows code page identifier.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage.#ctor(System.UInt16,System.UInt16,Microsoft.Exchange.Data.Globalization.CodePageKind,Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport,Microsoft.Exchange.Data.Globalization.CodePageUnicodeCoverage,Microsoft.Exchange.Data.Globalization.CodePageFlags,System.UInt16,Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePageRange[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CodePageMapData.CodePage"/> struct.
            </summary>
            <param name="id">The code page identifier.</param>
            <param name="windowsId">The Windows code page identifier.</param>
            <param name="kind">The type of the code page.</param>
            <param name="asciiSupport">The ASCII support for the code page.</param>
            <param name="unicodeCoverage">The unicode coverage of the code page.</param>
            <param name="flags">Any type flags for the code page.</param>
            <param name="detectId">The detection identifier.</param>
            <param name="ranges">The ranges of the code page.</param>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMap.codePage">
            <summary>
            The current codepage.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMap.ranges">
            <summary>
            The ranges for the current codepage.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMap.lastRangeIndex">
            <summary>
            The previous index used for range operations.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageMap.lastRange">
            <summary>
            The last codepage range used.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CodePageMap.ChoseCodePage(System.Int32)">
            <summary>
            Chooses the current code page.
            </summary>
            <param name="newCodePage">The code page to choose.</param>
            <returns>True if the selection is succesful, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CodePageMap.IsUnsafeExtendedCharacter(System.Char)">
            <summary>
            Decides if an extended chracter is unsafe for the current codepage.
            </summary>
            <param name="ch">The character to check.</param>
            <returns>True if the character is unsafe, otherwise false.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageKind">
            <summary>
            The character set type of the code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageKind.Unknown">
            <summary>
            Unknown character set
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageKind.Sbcs">
            <summary>
            Single Byte character set
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageKind.Dbcs">
            <summary>
            Double Byte character set
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageKind.Mbcs">
            <summary>
            Multi-byte character set
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageKind.Unicode">
            <summary>
            Unicode character set
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport">
            <summary>
            An indication of the code page ASCII support.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport.Unknown">
            <summary>
            Unknown ASCII support
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport.None">
            <summary>
            No ASCII support
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport.Incomplete">
            <summary>
            Incomplete ASCII support
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport.Remap">
            <summary>
            Some remapping is required
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport.Fine">
            <summary>
            ASCII support is fine for most purposes
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageAsciiSupport.Complete">
            <summary>
            Complete ASCII support
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageUnicodeCoverage">
            <summary>
            An indication of the code page Unicode support.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageUnicodeCoverage.Unknown">
            <summary>
            Unknown Unicode coverage
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageUnicodeCoverage.Partial">
            <summary>
            Partial Unicode coverage
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageUnicodeCoverage.Complete">
            <summary>
            Complete Unicode coverage
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CodePageFlags">
            <summary>
            Flags detailing if the codepage is detectable and/or is a 7bit code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageFlags.None">
            <summary>
            The codepage is not detectable or 7 bit
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageFlags.Detectable">
            <summary>
            The code page is detectable
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CodePageFlags.SevenBit">
            <summary>
            The code page is a 7bit codepage
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.Culture">
            <summary>
            Represents a culture 
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.Culture.lcid">
            <summary>
            The locale id for this culture.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.Culture.name">
            <summary>
            The culture name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.Culture.codepageDetectionPriorityOrder">
            <summary>
            The codepage detection order, by priority.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.Culture.cultureInfo">
            <summary>
            The culture info.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Culture.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.Culture"/> class.
            </summary>
            <param name="lcid">
            The locale identifier.
            </param>
            <param name="name">
            The name of the culture.
            </param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Culture.GetCodepageDetectionPriorityOrder(Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData)">
            <summary>
            Gets code page detection priority order for the specified globalization data.
            </summary>
            <param name="data">
            The globalization data.
            </param>
            <returns>
            The code page detection priority.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.Culture.SetCodepageDetectionPriorityOrder(System.Int32[])">
            <summary>
            Sets the code page detection prioity order.
            </summary>
            <param name="newCodepageDetectionPriorityOrder">
            The new code page detection priority order.
            </param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Culture.WindowsCharset">
            <summary>
            Gets or sets the Windows character set for the culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Culture.MimeCharset">
            <summary>
            Gets or sets the MIME character set for the culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Culture.WebCharset">
            <summary>
            Gets or sets the web character set for the culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Culture.Description">
            <summary>
            Gets or sets the description for the culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Culture.NativeDescription">
            <summary>
            Gets or sets the native description.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Culture.ParentCulture">
            <summary>
            Gets or sets the parent culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.Culture.CultureInfo">
            <summary>
            Gets or sets the culture information for this culture.
            </summary>
            <returns>
            The <see cref="P:Microsoft.Exchange.Data.Globalization.Culture.CultureInfo"/> for this culture.
            </returns>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase">
            <summary>
            The culture character set database
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.IntComparerInstance">
            <summary>
            An instance of the integer comparer.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.internalGlobalizationDataStore">
            <summary>
            The internal globalization data.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GetCultureSpecificCodepageDetectionPriorityOrder(Microsoft.Exchange.Data.Globalization.Culture,System.Int32[])">
            <summary>
            Gets the code page detection list, ordered by priority for the specified Culture.
            </summary>
            <param name="culture">
            The culture to get the ordered detection list for.
            </param>
            <param name="originalPriorityList">
            The original priority list.
            </param>
            <returns>
            An ordered list of the code page ids.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.LoadGlobalizationData(System.String)">
            <summary>
            Loads the globalization data with the specified default culture name.
            </summary>
            <param name="defaultCultureName">
            The default culture name.
            </param>
            <returns>
            A <see cref="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData"/> class using the default culture nane specified.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.IsDbcs(System.Int32)">
            <summary>
            Returns a value indicating if the code page is a double byte code page
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <returns>
            True if the code page is a double byte code page otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InList(System.Int32,System.Int32[],System.Int32)">
            <summary>
            Returns a boolean indicating if the specified code page is in the specified list.
            </summary>
            <param name="codePage">
            The code page to detect.
            </param>
            <param name="list">
            The code page list.
            </param>
            <param name="listCount">
            The size of the list.
            </param>
            <returns>
            True if the code page is in the list, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.IsSameLanguage(System.Int32,System.Int32)">
            <summary>
            Dectects if the specified code page and the windows code page are for the same language.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <param name="windowsCodePage">
            The windows code page.
            </param>
            <returns>
            True if the code page and the windows code page are equivilant.
            </returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalGlobalizationData">
            <summary>
            Gets or sets the internal globalization data.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CharsetName">
            <summary>
            The character set name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CharsetName.codePage">
            <summary>
            The code page identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CharsetName.name">
            <summary>
            The code page name.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CharsetName.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CharsetName"/> struct.
            </summary>
            <param name="name">
            The code page name.
            </param>
            <param name="codePage">
            The code page.
            </param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CharsetName.Name">
            <summary>
            Gets the name of the character set name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CharsetName.CodePage">
            <summary>
            Gets the code page identifier.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CodePageCultureOverride">
            <summary>
            The code page culture override.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CodePageCultureOverride.codePage">
            <summary>
            The code page identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CodePageCultureOverride.cultureName">
            <summary>
            The culture name.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CodePageCultureOverride.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CodePageCultureOverride"/> struct.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <param name="cultureName">
            The culture name.
            </param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CodePageCultureOverride.CodePage">
            <summary>
            Gets the code page identifier.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CodePageCultureOverride.CultureName">
            <summary>
            Gets the name of the culture.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride">
            <summary>
            The culture and code page override structure.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride.cultureName">
            <summary>
            The culture name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride.mimeCodePage">
            <summary>
            The mime code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride.webCodePage">
            <summary>
            The web code page.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride"/> struct.
            </summary>
            <param name="cultureName">
            The culture name.
            </param>
            <param name="mimeCodePage">
            The mime code page.
            </param>
            <param name="webCodePage">
            The web code page.
            </param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride.CultureName">
            <summary>
            Gets the name of the culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride.MimeCodePage">
            <summary>
            Gets the MIME code page identifier.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureCodePageOverride.WebCodePage">
            <summary>
            Gets the web code page identifier.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData">
            <summary>
            A structure representing the data for culture.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.description">
            <summary>
            The culture description.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.localeId">
            <summary>
            The locale identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.mimeCodePage">
            <summary>
            The mime code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.name">
            <summary>
            The culture name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.parentCultureName">
            <summary>
            The parent culture name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.webCodePage">
            <summary>
            The web code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.windowsCodePage">
            <summary>
            The Windows code page.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.#ctor(System.Int32,System.String,System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData"/> struct.
            </summary>
            <param name="localeId">
            The local identifier.
            </param>
            <param name="name">
            The culture name.
            </param>
            <param name="windowsCodePage">
            The windows code page.
            </param>
            <param name="mimeCodePage">
            The mime code page.
            </param>
            <param name="webCodePage">
            The web code page.
            </param>
            <param name="parentCultureName">
            The parent culture name.
            </param>
            <param name="description">
            The culture description.
            </param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.LocaleId">
            <summary>
            Gets the culture locale identifier.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.Name">
            <summary>
            Gets the culture name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.WindowsCodePage">
            <summary>
            Gets the Windows code page for this culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.MimeCodePage">
            <summary>
            Gets the MIME code page.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.WebCodePage">
            <summary>
            Gets the web code page.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.ParentCultureName">
            <summary>
            Gets the name of the parent culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.CultureData.Description">
            <summary>
            Gets the description of the culture.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage">
            <summary>
            A structure encapsulting Windows code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.codePage">
            <summary>
            The code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.cultureName">
            <summary>
            The culture name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.genericCultureDescription">
            <summary>
            The generic culture description.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.localeId">
            <summary>
            The locale identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.mimeCodePage">
            <summary>
            The MIME code page.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.name">
            <summary>
            The code page name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.webCodePage">
            <summary>
            The equivilant web code page.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.#ctor(System.Int32,System.String,System.Int32,System.String,System.Int32,System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage"/> struct.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <param name="name">
            The name of the code page.
            </param>
            <param name="localeId">
            The locale id.
            </param>
            <param name="cultureName">
            The culture name.
            </param>
            <param name="mimeCodePage">
            The MIME code page.
            </param>
            <param name="webCodePage">
            The equivilant web code page.
            </param>
            <param name="genericCultureDescription">
            The generic culture description.
            </param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.CodePage">
            <summary>
            Gets the code page number.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.Name">
            <summary>
            Gets the name of the code page.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.LocaleId">
            <summary>
            Gets the locale identifier.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.CultureName">
            <summary>
            Gets the name of the culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.MimeCodePage">
            <summary>
            Gets the MIME code page.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.WebCodePage">
            <summary>
            Gets the web code page.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.InternalWindowsCodePage.GenericCultureDescription">
            <summary>
            Gets the generic culture description.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData">
            <summary>
            Encapsultates globalization data
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.localeIdToCulture">
            <summary>
            The local identifier to culture map.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.nameToCharset">
            <summary>
            The name to character set map.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.nameToCulture">
            <summary>
            The name to culture map.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.codePageToCharset">
            <summary>
            The code page to charset map.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.DefaultCulture">
            <summary>
            Gets or sets the default culture.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.UnicodeCharset">
            <summary>
            Gets or sets the unicode charset.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.Utf8Charset">
            <summary>
            Gets or sets the utf 8 charset.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.DefaultDetectionPriorityOrder">
            <summary>
            Gets or sets the default detection priority order.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.CodePageToCharset">
            <summary>
            Gets or sets the code page to charset map.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.NameToCharset">
            <summary>
            Gets or sets the name to character set map.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.LocaleIdToCulture">
            <summary>
            Gets or sets the local identifier to culture map.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.MaxCharsetNameLength">
            <summary>
            Gets or sets the maximum length for a character set name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.NameToCulture">
            <summary>
            Gets or sets the name to culture map.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.GlobalizationData.AsciiCharset">
            <summary>
            Gets or sets the ascii charset.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.IntComparer">
            <summary>
            An integer comparator.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.IntComparer.Equals(System.Int32,System.Int32)">
            <summary>
            Determines whether the specified integers are equal.
            </summary>
            <param name="x">The first object to compare.</param>
            <param name="y">The second object to compare.</param>
            <returns>True if the integers are equal, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.CultureCharsetDatabase.IntComparer.GetHashCode(System.Int32)">
            <summary>
            Returns a hash code for the specified integer.
            </summary>
            <param name="obj">
            The integer for which a hash code is to be returned.
            </param>
            <returns>
            A hash code for the specified object.
            </returns>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback">
            <summary>
            The fallback ASCII encoder.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.GetCharacterFallback(System.Char)">
            <summary>
            Encodes the specified character.
            </summary>
            <param name="charUnknown">
            The character to encode.
            </param>
            <returns>
            The encoded character.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.CreateFallbackBuffer">
            <summary>
            Creates a fall back encoding buffer.
            </summary>
            <returns>
            A new <see cref="T:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer"/>.
            </returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.MaxCharCount">
            <summary>
            Gets the maximum character count.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer">
            <summary>
            A buffer for the <see cref="T:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.fallbackIndex">
            <summary>
            The fallback index.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.fallbackString">
            <summary>
            The fallback string.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.Fallback(System.Char,System.Int32)">
            <summary>
            Encodes the specified character using the fallback encoder.
            </summary>
            <param name="charUnknown">
            The unknown character to encode.
            </param>
            <param name="index">
            The index position in the buffer to encode into.
            </param>
            <returns>
            The encoded character.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.Fallback(System.Char,System.Char,System.Int32)">
            <summary>
            Encodes the specified high/low character combination using the fallback encoder.
            </summary>
            <param name="charUnknownHigh">
            The high byte of the character to encode.
            </param>
            <param name="charUnknownLow">
            The low byte of the character to encode.
            </param>
            <param name="index">
            The index position in the buffer to encode into.
            </param>
            <returns>
            The encoded character.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.GetNextChar">
            <summary>
            Gets the next character in the buffer.
            </summary>
            <returns>
            The next character in the buffer.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.MovePrevious">
            <summary>
            Moves to the previous character in the buffer.
            </summary>
            <returns>
            True if the move was sucessful, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.Reset">
            <summary>
            Resets the buffer.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.MaxCharCount">
            <summary>
            Gets the maximum character count for the buffer.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.AsciiEncoderFallback.AsciiFallbackBuffer.Remaining">
            <summary>
            Gets the remaining number of characters in the buffer.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.CtsResources.GlobalizationStrings">
            <summary>
            Strings used for globalization.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceManager">
            <summary>
            The resource manager for the globalization strings resources.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.CtsResources.GlobalizationStrings.InvalidCodePage(System.Int32)">
            <summary>
            Gets the string for the Invalid Code Page error.
            </summary>
            <param name="codePage">The code page.</param>
            <returns>The Invalid Code Page error string.</returns>
        </member>
        <member name="T:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceIdentifier">
            <summary>
            Resource identifiers
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceIdentifier.MaxCharactersCannotBeNegative">
            <summary>
            The maximum number of characters cannot be negative.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceIdentifier.PriorityListIncludesNonDetectableCodePage">
            <summary>
            The code page priority list includes a code page which cannot be detected.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceIdentifier.IndexOutOfRange">
            <summary>
            Index out of range.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceIdentifier.CountTooLarge">
            <summary>
            The count is too large.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceIdentifier.OffsetOutOfRange">
            <summary>
            The offset is out of range.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ResourceIdentifier.CountOutOfRange">
            <summary>
            The count is out of range.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier">
            <summary>
            Parameter identifiers
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier.InvalidCharset">
            <summary>
            Invalid character set.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier.InvalidLocaleId">
            <summary>
            Invalid locale identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier.NotInstalledCodePage">
            <summary>
            The code page is not installed.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier.NotInstalledCharset">
            <summary>
            The character set is not installed.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier.InvalidCodePage">
            <summary>
            The code page is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier.NotInstalledCharsetCodePage">
            <summary>
            The code page and the character set are not installed.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.CtsResources.GlobalizationStrings.ParameterIdentifier.InvalidCultureName">
            <summary>
            The culture name is invalid.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.FallbackExceptions">
            <summary>
            Value indidicating which fallback exceptions should be allowed.
            </summary>      
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.FallbackExceptions.None">
            <summary>
            No fallback exceptions are allowed.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.FallbackExceptions.Common">
            <summary>
            Common fallback exceptions are allowed.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.FallbackExceptions.All">
            <summary>
            All fallback exceptions are allowed.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Globalization.RemapEncoding">
            <summary>
            Represents remap instructions
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.RemapEncoding.decodingEncoding">
            <summary>
            The encoding used to decode.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Globalization.RemapEncoding.encodingEncoding">
            <summary>
            The encoding used to encode.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Globalization.RemapEncoding"/> class.
            </summary>
            <param name="codePage">
            The code page.
            </param>
            <exception cref="T:System.ArgumentException">
            Thrown if the code page is unknown and cannot be remapped.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetPreamble">
            <summary>
            Returns a sequence of bytes that specifies the encoding used.
            </summary>
            <returns>
            A byte array containing a sequence of bytes that specifies the encoding used.
            -or- 
            A byte array of length zero, if a preamble is not required.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetMaxByteCount(System.Int32)">
            <summary>
            Calculates the maximum number of bytes produced by encoding the specified number of characters.
            </summary>
            <param name="charCount">
            The number of characters to encode. 
            </param>
            <returns>
            The maximum number of bytes produced by encoding the specified number of characters.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetMaxCharCount(System.Int32)">
            <summary>
            Calculates the maximum number of characters produced by decoding the specified number of bytes.
            </summary>
            <param name="byteCount">
            The number of bytes to decode. 
            </param>
            <returns>
            The maximum number of characters produced by decoding the specified number of bytes.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetByteCount(System.Char[],System.Int32,System.Int32)">
            <summary>
            Calculates the number of bytes produced by encoding a set of characters from the specified character array.
            </summary>
            <param name="chars">
            The character array containing the set of characters to encode. 
            </param>
            <param name="index">
            The index of the first character to encode. 
            </param>
            <param name="count">
            The number of characters to encode. 
            </param>
            <returns>
            The number of bytes produced by encoding the specified characters.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetByteCount(System.String)">
            <summary>
            Calculates the number of bytes produced by encoding the characters in the specified <see cref="T:System.String"/>. 
            </summary>
            <param name="s">
            The <see cref="T:System.String"/> containing the set of characters to encode. 
            </param>
            <returns>
            The number of bytes produced by encoding the specified characters.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetByteCount(System.Char*,System.Int32)">
            <summary>
            Calculates the number of bytes produced by encoding a set of characters starting at the specified character pointer.
            </summary>
            <param name="chars">
            A pointer to the first character to encode. 
            </param>
            <param name="count">
            The number of characters to encode. 
            </param>
            <returns>
            The number of bytes produced by encoding the specified characters.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
            Encodes a set of characters from the specified String into the specified byte array. 
            </summary>
            <param name="s">
            The <see cref="T:System.String"/> containing the set of characters to encode. 
            </param>
            <param name="charIndex">
            The index of the first character to encode. 
            </param>
            <param name="charCount">
            The number of characters to encode. 
            </param>
            <param name="bytes">
            The byte array to contain the resulting sequence of bytes. 
            </param>
            <param name="byteIndex">
            The index at which to start writing the resulting sequence of bytes.
            </param>
            <returns>
            The actual number of bytes written into bytes. 
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
            Encodes a set of characters from the specified character array into the specified byte array.
            </summary>
            <param name="chars">
            The character array containing the set of characters to encode. 
            </param>
            <param name="charIndex">
            The index of the first character to encode. 
            </param>
            <param name="charCount">
            The number of characters to encode. 
            </param>
            <param name="bytes">
            The byte array to contain the resulting sequence of bytes. 
            </param>
            <param name="byteIndex">
            The index at which to start writing the resulting sequence of bytes. 
            </param>
            <returns>
            The actual number of bytes written into bytes. 
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetBytes(System.Char*,System.Int32,System.Byte*,System.Int32)">
            <summary>
            Encodes a set of characters starting at the specified character pointer into a sequence of bytes that are stored starting at the specified byte pointer.
            </summary>
            <param name="chars">
            A pointer to the first character to encode. 
            </param>
            <param name="charCount">
            The number of characters to encode. 
            </param>
            <param name="bytes">
            A pointer to the location at which to start writing the resulting sequence of bytes. 
            </param>
            <param name="byteCount">
            The maximum number of bytes to write. 
            </param>
            <returns>
            The actual number of bytes written at the location indicated by the bytes parameter.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetCharCount(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calculates the number of characters produced by decoding a sequence of bytes from the specified byte array.
            </summary>
            <param name="bytes">
            The byte array containing the sequence of bytes to decode. 
            </param>
            <param name="index">
            The index of the first byte to decode. 
            </param>
            <param name="count">
            The number of bytes to decode. 
            </param>
            <returns>
            The number of characters produced by decoding the specified sequence of bytes.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetCharCount(System.Byte*,System.Int32)">
            <summary>
            Calculates the number of characters produced by decoding a sequence of bytes starting at the specified byte pointer.
            </summary>
            <param name="bytes">
            A pointer to the first byte to decode.
            </param>
            <param name="count">
            The number of bytes to decode.
            </param>
            <returns>
            The number of characters produced by decoding the specified sequence of bytes.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
            <summary>
            Decodes a sequence of bytes from the specified byte array into the specified character array.
            </summary>
            <param name="bytes">
            The byte array containing the sequence of bytes to decode. 
            </param>
            <param name="byteIndex">
            The index of the first byte to decode. 
            </param>
            <param name="byteCount">
            The number of bytes to decode. 
            </param>
            <param name="chars">
            The character array to contain the resulting set of characters. 
            </param>
            <param name="charIndex">
            The index at which to start writing the resulting set of characters.
            </param>
            <returns>
            The actual number of characters written into chars. 
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetChars(System.Byte*,System.Int32,System.Char*,System.Int32)">
            <summary>
            Decodes a sequence of bytes starting at the specified byte pointer into a set of characters that are stored starting at the specified character pointer.
            </summary>
            <param name="bytes">
            The bytes to decode.
            </param>
            <param name="byteCount">
            The number of bytes to decode.
            </param>
            <param name="chars">
            The characters written.
            </param>
            <param name="charCount">
            The maximum number of characters to write. 
            </param>
            <returns>
            The actual number of characters written at the location indicated by the chars parameter. 
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetString(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Decodes a sequence of bytes from the specified byte array into a string.
            </summary>
            <param name="bytes">
            The byte array containing the sequence of bytes to decode.
            </param>
            <param name="index">
            The index of the first byte to decode. 
            </param>
            <param name="count">
            The number of bytes to decode. 
            </param>
            <returns>
            A <see cref="T:System.String"/> containing the results of decoding the specified sequence of bytes. 
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetDecoder">
            <summary>
            Gets the decoder for this map.
            </summary>
            <returns>
            The decoder for this map.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.GetEncoder">
            <summary>
            Gets the encoder for this map.
            </summary>
            <returns>
            The encoder for this map.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Globalization.RemapEncoding.Clone">
            <summary>
            Creates a new object that is a copy of the current instance.
            </summary>
            <returns>
            A new object that is a copy of the current instance.</returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.CodePage">
            <summary>
            Gets the encoding code page.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.BodyName">
            <summary>
            Gets encoding body name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.EncodingName">
            <summary>
            Gets the encoding name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.HeaderName">
            <summary>
            Gets the encoding header name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.WebName">
            <summary>
            Gets the encoding web name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.WindowsCodePage">
            <summary>
            Gets the encoding Windows code page.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.IsBrowserDisplay">
            <summary>
            Gets a value indicating whether the encoding class can be used for browsers displaying content.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.IsBrowserSave">
            <summary>
            Gets a value indicating whether the encoding class can be used for browsers for saving content.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.IsMailNewsDisplay">
            <summary>
            Gets a value indicating whether the encoding class can be used for mail and news clients for displaying content.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.IsMailNewsSave">
            <summary>
            Gets a value indicating whether the encoding class can be used for mail and news clients for saving content.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Globalization.RemapEncoding.IsSingleByte">
            <summary>
            gets a value indicating whether the current encoding uses single-byte code points.
            </summary>
        </member>
        <member name="T:Microsoft.Security.Application.Sanitizer">
            <summary>
             Sanitizes input HTML to make it safe to be displayed on a 
             browser by removing potentially dangerous tags.
            </summary>
            <remarks>
             This santization library uses the Principle of Inclusions, 
             sometimes referred to as "safe-listing" to provide protection 
             against injection attacks.  With safe-listing protection, 
             algorithms look for valid inputs and automatically treat 
             everything outside that set as a potential attack.  This library 
             can be used as a defense in depth approach with other mitigation 
             techniques.
            </remarks>
        </member>
        <member name="M:Microsoft.Security.Application.Sanitizer.GetSafeHtml(System.String)">
            <summary>
            Sanitizes input HTML document for safe display on browser.
            </summary>
            <param name="input">Malicious HTML Document</param>
            <returns>A santizied HTML document</returns>
            <remarks>
            The method transforms and filters HTML of executable scripts. 
            A safe list of tags and attributes are used to strip dangerous 
            scripts from the HTML. HTML is also normalized where tags are 
            properly closed and attributes are properly formatted.
            </remarks>
        </member>
        <member name="M:Microsoft.Security.Application.Sanitizer.GetSafeHtmlFragment(System.String)">
            <summary>
            Sanitizes input HTML fragment for safe display on browser.
            </summary>
            <param name="input">Malicious HTML fragment</param>
            <returns>Safe HTML fragment</returns>
            <remarks>
            The method transforms and filters HTML of executable scripts. 
            A safe list of tags and attributes are used to strip dangerous 
            scripts from the HTML. HTML is also normalized where tags are 
            properly closed and attributes are properly formatted.
            </remarks>
        </member>
        <member name="M:Microsoft.Security.Application.Sanitizer.GetSafeHtml(System.IO.TextReader,System.IO.TextWriter)">
            <summary>
            Sanitizes input HTML document for safe display on browser.
            </summary>
            <param name="sourceReader">Source text reader with malicious HTML</param>
            <param name="destinationWriter">Text Writer to write safe HTML</param>
            <remarks>
            The method transforms and filters HTML of executable scripts. 
            A safe list of tags and attributes are used to strip dangerous 
            scripts from the HTML. HTML is also normalized where tags are 
            properly closed and attributes are properly formatted.
            </remarks>
        </member>
        <member name="M:Microsoft.Security.Application.Sanitizer.GetSafeHtml(System.IO.TextReader,System.IO.Stream)">
            <summary>
            Sanitizes input HTML document for safe display on browser.
            </summary>
            <param name="sourceReader">Source text reader with malicious HTML</param>
            <param name="destinationStream">Stream to write safe HTML</param>
            <remarks>
            The method transforms and filters HTML of executable scripts. 
            A safe list of tags and attributes are used to strip dangerous 
            scripts from the HTML. HTML is also normalized where tags are 
            properly closed and attributes are properly formatted.
            </remarks>
        </member>
        <member name="M:Microsoft.Security.Application.Sanitizer.GetSafeHtmlFragment(System.IO.TextReader,System.IO.TextWriter)">
            <summary>
            Sanitizes input HTML fragment for safe display on browser.
            </summary>
            <param name="sourceReader">Source text reader with malicious HTML</param>
            <param name="destinationWriter">Stream to write safe HTML</param>
            <remarks>
            The method transforms and filters HTML of executable scripts. 
            A safe list of tags and attributes are used to strip dangerous 
            scripts from the HTML. HTML is also normalized where tags are 
            properly closed and attributes are properly formatted.
            </remarks>
        </member>
        <member name="M:Microsoft.Security.Application.Sanitizer.GetSafeHtmlFragment(System.IO.TextReader,System.IO.Stream)">
            <summary>
            Sanitizes input HTML fragment for safe display on browser.
            </summary>
            <param name="sourceReader">Source text reader with malicious HTML</param>
            <param name="destinationStream">Stream to write safe HTML</param>
            <remarks>
            The method transforms and filters HTML of executable scripts. 
            A safe list of tags and attributes are used to strip dangerous 
            scripts from the HTML. HTML is also normalized where tags are 
            properly closed and attributes are properly formatted.
            </remarks>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.ApplicationServices">
            <summary>
            Provides functions for parsing application configuration data.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.ApplicationServices.ServicesProvider">
            <summary>
            Loads the application service provider.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.ApplicationServices.GetSimpleConfigurationSetting(System.String,System.String)">
            <summary>
            Gets the specified configuration setting.
            </summary>
            <param name="subSectionName">Name of the configuration sub section.</param>
            <param name="settingName">Name of the configuration setting.</param>
            <returns>A <see cref="T:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting"/> for the sepecified setting from the specified sub section.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.ApplicationServices.LoadServices">
            <summary>
            Initializes the application services.
            </summary>
            <returns>An instance of the default Application Services class.</returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.ApplicationServices.Provider">
            <summary>
            Gets the application service provider.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.CtsConfigurationSection">
            <summary>
            Provides access to the configuration section.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.CtsConfigurationSection.subSections">
            <summary>
            The subsections in the configuration file.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.CtsConfigurationSection.properties">
            <summary>
            The configuration properties.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.CtsConfigurationSection.DeserializeSection(System.Xml.XmlReader)">
            <summary>
            Deserailizes a configuration section.
            </summary>
            <param name="reader">An XmlReader containing the section to deserialize.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.CtsConfigurationSection.DeserializeSetting(System.Xml.XmlReader)">
            <summary>
            Deserializes an individual configuration setting.
            </summary>
            <param name="reader">The XmlReader containing the setting to deserialize.</param>
            <returns>A <see cref="T:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting"/> instance containing the configuration setting.</returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.CtsConfigurationSection.SubSectionsDictionary">
            <summary>
            Gets a dictionary for the subsections in the configuration file.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.CtsConfigurationSection.Properties">
            <summary>
            Gets a collection of all configuration properties.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.CtsConfigurationArgument">
            <summary>
            Contains a configuration argument and its value.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.CtsConfigurationArgument.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Internal.CtsConfigurationArgument"/> class.
            </summary>
            <param name="name">The argument name.</param>
            <param name="value">The argument value.</param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.CtsConfigurationArgument.Name">
            <summary>
            Gets the argument name.
            </summary>
            <value>The argument name.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.CtsConfigurationArgument.Value">
            <summary>
            Gets the argument value.
            </summary>
            <value>The argument value.</value>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting">
            <summary>
            Contains a configuration name and its arguments.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting.configurationName">
            <summary>
            The configuration name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting.arguments">
            <summary>
            The configuration arguments.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting"/> class.
            </summary>
            <param name="name">The setting name.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting.AddArgument(System.String,System.String)">
            <summary>
            Adds the specified argument to the configuration setting.
            </summary>
            <param name="name">The argument name.</param>
            <param name="value">The argument value.</param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting.Name">
            <summary>
            Gets the name of the setting.
            </summary>
            <value>The name of the setting.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting.Arguments">
            <summary>
            Gets the argument list for the setting.
            </summary>
            <value>The argument list.</value>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.DefaultApplicationServices">
            <summary>
            Wrapper for CTS application settings.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.IApplicationServices">
            <summary>
            An interface for application configuration services.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.IApplicationServices.GetConfiguration(System.String)">
            <summary>
            Gets the configuration subsection specified.
            </summary>
            <param name="subSectionName">Name of the subsection.</param>
            <returns>A list of <see cref="T:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting"/>s for the specified section.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.IApplicationServices.RefreshConfiguration">
            <summary>
            Refreshes the configuration from the application configuration file.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.IApplicationServices.LogConfigurationErrorEvent">
            <summary>
            Logs an error during configuration processing.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.DefaultApplicationServices.EmptySubSection">
            <summary>
            A blank sub section.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.DefaultApplicationServices.lockObject">
            <summary>
            The lock used for thread safe syncronization.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.Internal.DefaultApplicationServices.configurationSubSections">
            <summary>
            The configuration sub sections from the CTS application settings.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.DefaultApplicationServices.GetConfiguration(System.String)">
            <summary>
            Gets the configuration subsection specified.
            </summary>
            <param name="subSectionName">Name of the subsection.</param>
            <returns>
            A list of <see cref="T:Microsoft.Exchange.Data.Internal.CtsConfigurationSetting"/>s for the specified section.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.DefaultApplicationServices.RefreshConfiguration">
            <summary>
            Refreshes the configuration from the application configuration file.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.DefaultApplicationServices.LogConfigurationErrorEvent">
            <summary>
            Logs an error during configuration processing.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.InternalDebug">
            <summary>
            A class to provides internal debugging services.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.InternalDebug.Trace(System.Int64,System.String,System.Object[])">
            <summary>
            Writes information about the trace to the trace listeners.
            </summary>
            <param name="traceType">Type of the trace.</param>
            <param name="format">The format of the trace.</param>
            <param name="traceObjects">The trace objects.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.InternalDebug.Assert(System.Boolean,System.String)">
            <summary>
            Evaluates an expression and, when the result is false, prints a diagnostic message and aborts the program.
            </summary>
            <param name="condition">Expression (including pointers) that evaluates to nonzero or 0.</param>
            <param name="formatString">The format string to throw if the assert fails.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.InternalDebug.Assert(System.Boolean)">
            <summary>
            Evaluates an expression and, when the result is false, prints a diagnostic message and aborts the program.
            </summary>
            <param name="condition">Expression (including pointers) that evaluates to nonzero or 0.</param>
        </member>
        <member name="P:Microsoft.Exchange.Data.Internal.InternalDebug.UseSystemDiagnostics">
            <summary>
            Gets or sets a value indicating whether to use system diagnostics debug and tracing..
            </summary>
            <value>
            <c>true</c> if [use system diagnostics]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Microsoft.Exchange.Data.Internal.InternalDebug.DebugAssertionViolationException">
            <summary>
            An exception thrown when a debug assertion fails.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.InternalDebug.DebugAssertionViolationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Internal.InternalDebug.DebugAssertionViolationException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.InternalDebug.DebugAssertionViolationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Internal.InternalDebug.DebugAssertionViolationException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.Internal.InternalDebug.DebugAssertionViolationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.Internal.InternalDebug.DebugAssertionViolationException"/> class.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.RecognizeInterestingFontName.TextMapping">
            <summary>
            Gets the text mapping.
            </summary>
            <value>The text mapping.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.RecognizeInterestingFontName.IsRejected">
            <summary>
            Gets a value indicating whether this instance is rejected.
            </summary>
            <value>
            	<c>true</c> if this instance is rejected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterInput.#ctor(Microsoft.Exchange.Data.TextConverters.IProgressMonitor)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.ConverterInput"/> class.
            </summary>
            <param name="progressMonitor">The progress monitor.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterInput.SetRestartConsumer(Microsoft.Exchange.Data.TextConverters.IRestartable)">
            <summary>
            Sets the restart consumer.
            </summary>
            <param name="restartConsumer">The restart consumer.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterInput.System#IDisposable#Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterInput.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ConverterInput.EndOfFile">
            <summary>
            Gets a value indicating whether reached end of file.
            </summary>
            <value><c>true</c> if reached end of file; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ConverterInput.MaxTokenSize">
            <summary>
            Gets the max size of the token.
            </summary>
            <value>The max size of the token.</value>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.ITextSink">
            <summary>
            Interface declaration for classes with Test Sink.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ITextSink.Write(System.Char[],System.Int32,System.Int32)">
            <summary>
            Writes the specified buffer.
            </summary>
            <param name="buffer">The buffer.</param>
            <param name="offset">The offset.</param>
            <param name="count">The count.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ITextSink.Write(System.Int32)">
            <summary>
            Writes the specified ucs32 char.
            </summary>
            <param name="ucs32Char">The ucs32 char.</param>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ITextSink.IsEnough">
            <summary>
            Gets a value indicating whether this instance is enough.
            </summary>
            <value><c>true</c> if this instance is enough; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.IReusable">
            <summary>
            Interface declaration for classes that are reusable.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IReusable.Initialize(System.Object)">
            <summary>
            Initializes the specified new source or destination.
            </summary>
            <param name="newSourceOrDestination">The new source or destination.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterOutput.System#IDisposable#Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterOutput.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.IByteSource">
            <summary>
            Interface definition for Byte Source.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.IRestartable">
            <summary>
            Interface declaration for classes that are restartable.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IRestartable.CanRestart">
            <summary>
            Determines whether this instance can restart.
            </summary>
            <returns>
            <c>true</c> if this instance can restart; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IRestartable.Restart">
            <summary>
            Restarts this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IRestartable.DisableRestart">
            <summary>
            Disables the restart.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.IProducerConsumer">
            <summary>
            Interface declaration for Producer Consumer.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IProducerConsumer.Run">
            <summary>
            Runs this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IProducerConsumer.Flush">
            <summary>
            Flushes this instance.
            </summary>
            <returns>
            <c>true</c> if flush is successful; otherwise <c>false</c>.
            </returns>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.IProgressMonitor">
            <summary>
            Interface for classes which can report progress.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IProgressMonitor.ReportProgress">
            <summary>
            Report the progress of the current operation.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.ITextSinkEx">
            <summary>
            Interface declaration for classes needing to write.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ITextSinkEx.Write(System.String)">
            <summary>
            Writes the specified value.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ITextSinkEx.WriteNewLine">
            <summary>
            Writes the new line.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.RecognizeInterestingFontNameInInlineStyle.TextMapping">
            <summary>
            Gets the text mapping.
            </summary>
            <value>The text mapping.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.RecognizeInterestingFontNameInInlineStyle.IsFinished">
            <summary>
            Gets a value indicating whether this instance is finished.
            </summary>
            <value>
            	<c>true</c> if this instance is finished; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextCache.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.TextCache"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.HtmlAttributeId">
            <summary>
            An enumeration for an HTML attribute.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.HtmlTagCallback">
            <summary>
            Delegate callback definition for the HTML tag.
            </summary>
            <param name="tagContext">An instance fo the HtmlTagContext object.</param>
            <param name="htmlWriter">An instance fo the HtmlWriter object.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.HtmlTagContext.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.HtmlTagContext"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.HtmlTagContext.TagNameIndex">
            <summary>
            Gets the index of the tag name.
            </summary>
            <value>The index of the tag name.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.HtmlTagContext.TagParts">
            <summary>
            Gets the tag parts.
            </summary>
            <value>The tag parts.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.HtmlTagContext.IsInvokeCallbackForEndTag">
            <summary>
            Gets a value indicating whether this instance can invoke callback for end tag.
            </summary>
            <value>
            	<c>true</c> if this instance can invoke callback for end tag; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConvertersException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.TextConvertersException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConvertersException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.TextConvertersException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConvertersException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.TextConvertersException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.ConverterReader">
            <summary>
            A conversion class presented as a text reader.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.maxLoopsWithoutProgress">
            <summary>
            The number of conversion loops to attempt without any progress before the conversion is cancelled.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.sourceOutputObject">
            <summary>
            The output of the convertor.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.producer">
            <summary>
            The conversion producer and consume.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.madeProgress">
            <summary>
            Value indicating if any progress has been made during conversion.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.writeBuffer">
            <summary>
            The internal write buffer.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.writeIndex">
            <summary>
            The position within the internal write buffer.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.writeCount">
            <summary>
            A running total of the number of characters written.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.source">
            <summary>
            The conversion source.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.endOfFile">
            <summary>
            Value indicating if the end of the file has been reached.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterReader.inconsistentState">
            <summary>
            Value indicating if the conversion is in an inconsitent state.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.#ctor(System.IO.TextReader,Microsoft.Exchange.Data.TextConverters.TextConverter)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.ConverterReader"/> class.
            </summary>
            <param name="sourceReader">The source reader.</param>
            <param name="converter">The converter to use.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.Peek">
            <summary>
            Reads the next character without changing the state of the reader or the character source. Returns the next available character without actually reading it from the input stream.
            </summary>
            <returns>
            An integer representing the next character to be read, or -1 if no more characters are available or the stream does not support seeking.
            </returns>
            <exception cref="T:System.ObjectDisposedException">
            The <see cref="T:System.IO.TextReader"/> is closed.
            </exception>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.Read">
            <summary>
            Reads the next character from the input stream and advances the character position by one character.
            </summary>
            <returns>
            The next character from the input stream, or -1 if no more characters are available. The default implementation returns -1.
            </returns>
            <exception cref="T:System.ObjectDisposedException">
            The <see cref="T:System.IO.TextReader"/> is closed.
            </exception>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.Read(System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads a maximum of <paramref name="count"/> characters from the current stream and writes the data to <paramref name="buffer"/>, beginning at <paramref name="index"/>.
            </summary>
            <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index"/> and (<paramref name="index"/> + <paramref name="count"/> - 1) replaced by the characters read from the current source.</param>
            <param name="index">The place in <paramref name="buffer"/> at which to begin writing.</param>
            <param name="count">The maximum number of characters to read. If the end of the stream is reached before <paramref name="count"/> of characters is read into <paramref name="buffer"/>, the current method returns.</param>
            <returns>
            The number of characters that have been read. The number will be less than or equal to <paramref name="count"/>, depending on whether the data is available within the stream. This method returns zero if called when no more characters are left to read.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="buffer"/> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            The buffer length minus <paramref name="index"/> is less than <paramref name="count"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index"/> or <paramref name="count"/> is negative.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            The <see cref="T:System.IO.TextReader"/> is closed.
            </exception>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.Microsoft#Exchange#Data#TextConverters#IProgressMonitor#ReportProgress">
            <summary>
            Report the progress of the current operation.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.SetSource(Microsoft.Exchange.Data.TextConverters.ConverterUnicodeOutput)">
            <summary>
            Sets the data source for conversion.
            </summary>
            <param name="converterUnicodeOutputSource">The data source for conversion.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.GetOutputBuffer(System.Char[]@,System.Int32@,System.Int32@)">
            <summary>
            Gets the output buffer.
            </summary>
            <param name="outputBuffer">The output buffer.</param>
            <param name="outputIndex">Current index position in the output buffer.</param>
            <param name="outputCount">The number of characters in the output buffer.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.ReportOutput(System.Int32)">
            <summary>
            Notes that output has been written.
            </summary>
            <param name="outputCount">The number of characters written.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.ReportEndOfFile">
            <summary>
            Reports that the end of the file has been reached.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterReader.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the <see cref="T:System.IO.TextReader"/> and optionally releases the managed resources.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.ConverterStreamAccess">
            <summary>
            Indicates the type of access granted to the convertor stream.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStreamAccess.Read">
            <summary>
            Read access.
            </summary>        
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStreamAccess.Write">
            <summary>
            Write access.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.ConverterStream">
            <summary>
            A stream class used for conversion.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.maxLoopsWithoutProgress">
            <summary>
            The number of conversion loops to attempt without any progress before the conversion is cancelled.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.consumer">
            <summary>
            The conversion consumer.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.madeProgress">
            <summary>
            Value indicating if any progress has been made during conversion.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.chunkToReadBuffer">
            <summary>
            The read buffer
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.chunkToReadOffset">
            <summary>
            The offset to start reading from
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.chunkToReadCount">
            <summary>
            The number of bytes read.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.byteSource">
            <summary>
            The data source to be converted.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.producer">
            <summary>
            The conversion producer.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.writeBuffer">
            <summary>
            The write buffer.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.writeOffset">
            <summary>
            The offset to write from.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.writeCount">
            <summary>
            The number of characters written.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.sourceOrDestination">
            <summary>
            The object source or destination.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.endOfFile">
            <summary>
            Value indicating if the end of the file has been reached.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConverterStream.inconsistentState">
            <summary>
            Value indicating if the conversion is in an inconsitent state.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.#ctor(System.IO.TextReader,Microsoft.Exchange.Data.TextConverters.TextConverter)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.ConverterStream"/> class.
            </summary>
            <param name="sourceReader">The source reader.</param>
            <param name="converter">The converter to use.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the <paramref name="origin"/> parameter.</param>
            <param name="origin">A value of type <see cref="T:System.IO.SeekOrigin"/> indicating the reference point used to obtain the new position.</param>
            <returns>
            The new position within the current stream.
            </returns>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The stream does not support seeking, such as if the stream is constructed from a pipe or console output.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            Methods were called after the stream was closed.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The stream does not support both writing and seeking, such as if the stream is constructed from a pipe or console output.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            Methods were called after the stream was closed.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">An array of bytes. This method copies <paramref name="count"/> bytes from <paramref name="buffer"/> to the current stream.</param>
            <param name="offset">The zero-based byte offset in <paramref name="buffer"/> at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to be written to the current stream.</param>
            <exception cref="T:System.ArgumentException">
            The sum of <paramref name="offset"/> and <paramref name="count"/> is greater than the buffer length.
            </exception>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="buffer"/> is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> or <paramref name="count"/> is negative.
            </exception>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The stream does not support writing.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            Methods were called after the stream was closed.
            </exception>
            <exception cref="T:Microsoft.Exchange.Data.TextConverters.TextConvertersException">
            There were too many iterations without progress during conversion.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.Flush">
            <summary>
            Clears all buffers for this stream and causes any buffered data to be written to the underlying device.
            </summary>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            Methods were called after the stream was closed.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Methods were called on a read-only stream.
            </exception>
            <exception cref="T:Microsoft.Exchange.Data.TextConverters.TextConvertersException">
            There were too many iterations without progress during conversion.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.Close">
            <summary>
            Closes the current stream and releases any resources (such as sockets and file handles) associated with the current stream.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.
            </summary>
            <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values between <paramref name="offset"/> and (<paramref name="offset"/> + <paramref name="count"/> - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in <paramref name="buffer"/> at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
            <returns>
            The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.
            </returns>
            <exception cref="T:System.ArgumentException">
            The sum of <paramref name="offset"/> and <paramref name="count"/> is larger than the buffer length.
            </exception>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="buffer"/> is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> or <paramref name="count"/> is negative.
            </exception>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The stream does not support reading.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            Methods were called after the stream was closed.
            </exception>
            <exception cref="T:Microsoft.Exchange.Data.TextConverters.TextConvertersException">
            There were too many iterations without progress during conversion.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.Microsoft#Exchange#Data#TextConverters#IProgressMonitor#ReportProgress">
            <summary>
            Report the progress of the current operation.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.SetSource(Microsoft.Exchange.Data.TextConverters.IByteSource)">
            <summary>
            Sets the source of the information to be converted.
            </summary>
            <param name="newByteSource">The byte source.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.GetOutputBuffer(System.Byte[]@,System.Int32@,System.Int32@)">
            <summary>
            Gets the output buffer.
            </summary>
            <param name="outputBuffer">The output buffer.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputCount">The output count.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.ReportOutput(System.Int32)">
            <summary>
            Reports the output.
            </summary>
            <param name="outputCount">The output count.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.ReportEndOfFile">
            <summary>
            Reports the end of file.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.GetInputChunk(System.Byte[]@,System.Int32@,System.Int32@,System.Boolean@)">
            <summary>
            Gets the input chunk.
            </summary>
            <param name="chunkBuffer">The chunk buffer.</param>
            <param name="chunkOffset">The chunk offset.</param>
            <param name="chunkCount">The chunk count.</param>
            <param name="eof">Set to true if the EOF was reached.</param>
            <returns>false if there are no more bytes to read, otherwise true.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterStream.ReportRead(System.Int32)">
            <summary>
            Reports the number of characters read.
            </summary>
            <param name="readCount">The read count.</param>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ConverterStream.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            </summary>
            <returns>true if the stream supports reading; otherwise, false.
            </returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ConverterStream.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            </summary>
            <returns>true if the stream supports writing; otherwise, false.
            </returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ConverterStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            </summary>
            <returns>true if the stream supports seeking; otherwise, false.
            </returns>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ConverterStream.Length">
            <summary>
            Gets the length in bytes of the stream.
            </summary>
            <returns>
            A long value representing the length of the stream in bytes.
            </returns>
            <exception cref="T:System.NotSupportedException">
            A class derived from Stream does not support seeking.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            Methods were called after the stream was closed.
            </exception>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.ConverterStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            </summary>
            <returns>
            The current position within the stream.
            </returns>
            <exception cref="T:System.IO.IOException">
            An I/O error occurs.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The stream does not support seeking.
            </exception>
            <exception cref="T:System.ObjectDisposedException">
            Methods were called after the stream was closed.
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.ConverterWriter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.ConverterWriter"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.HtmlToHtml">
            <summary>
            A HTML to HTML converter.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.TextConverter">
            <summary>
            Base class for a text convertor.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.IResultsFeedback">
            <summary>
            An interface for setting results
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.IResultsFeedback.Set(Microsoft.Exchange.Data.TextConverters.ConfigParameter,System.Object)">
            <summary>
            Sets the configuration parameter and its associated value.
            </summary>
            <param name="parameterId">The configuration parameter to set.</param>
            <param name="val">The value for the configuration parameter.</param>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.TextConverter.testBoundaryConditions">
            <summary>
            Value indicating if boundary conditions are to be tested.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.TextConverter.inputBufferSize">
            <summary>
            The input buffer size.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.TextConverter.outputBufferSize">
            <summary>
            The output buffer size.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.Convert(System.IO.TextReader,System.IO.Stream)">
            <summary>
            Converts the specified source reader.
            </summary>
            <param name="sourceReader">The source reader.</param>
            <param name="destinationStream">The destination stream.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.Convert(System.IO.TextReader,System.IO.TextWriter)">
            <summary>
            Converts the specified source reader.
            </summary>
            <param name="sourceReader">The source reader.</param>
            <param name="destinationWriter">The destination writer.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.Microsoft#Exchange#Data#TextConverters#IResultsFeedback#Set(Microsoft.Exchange.Data.TextConverters.ConfigParameter,System.Object)">
            <summary>
            Sets the configuration parameter and its associated value.
            </summary>
            <param name="parameterId">The configuration parameter to set.</param>
            <param name="val">The value for the configuration parameter.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.CreatePushChain(Microsoft.Exchange.Data.TextConverters.ConverterStream,System.IO.Stream)">
            <summary>
            Creates the push chain.
            </summary>
            <param name="converterStream">The converter stream.</param>
            <param name="output">The output.</param>
            <returns>An <see cref="T:Microsoft.Exchange.Data.TextConverters.IProducerConsumer"/> for use in a chain.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.CreatePushChain(Microsoft.Exchange.Data.TextConverters.ConverterStream,System.IO.TextWriter)">
            <summary>
            Creates the push chain.
            </summary>
            <param name="converterStream">The converter stream.</param>
            <param name="output">The output.</param>
            <returns>An <see cref="T:Microsoft.Exchange.Data.TextConverters.IProducerConsumer"/> for use in a chain.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.CreatePullChain(System.IO.Stream,Microsoft.Exchange.Data.TextConverters.ConverterStream)">
            <summary>
            Creates the pull chain.
            </summary>
            <param name="input">The input Stream.</param>
            <param name="converterStream">The converter stream.</param>
            <returns>An <see cref="T:Microsoft.Exchange.Data.TextConverters.IProducerConsumer"/> for use in a chain.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.CreatePullChain(System.IO.TextReader,Microsoft.Exchange.Data.TextConverters.ConverterStream)">
            <summary>
            Creates the pull chain.
            </summary>
            <param name="input">The input TextReader.</param>
            <param name="converterStream">The converter stream.</param>
            <returns>An <see cref="T:Microsoft.Exchange.Data.TextConverters.IProducerConsumer"/> for use in a chain.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.CreatePullChain(System.IO.Stream,Microsoft.Exchange.Data.TextConverters.ConverterReader)">
            <summary>
            Creates the pull chain.
            </summary>
            <param name="input">The input stream.</param>
            <param name="converterReader">The converter reader.</param>
            <returns>An <see cref="T:Microsoft.Exchange.Data.TextConverters.IProducerConsumer"/> for use in a chain.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.CreatePullChain(System.IO.TextReader,Microsoft.Exchange.Data.TextConverters.ConverterReader)">
            <summary>
            Creates the pull chain.
            </summary>
            <param name="input">The input TextReader.</param>
            <param name="converterReader">The converter reader.</param>
            <returns>An <see cref="T:Microsoft.Exchange.Data.TextConverters.IProducerConsumer"/> for use in a chain.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.SetResult(Microsoft.Exchange.Data.TextConverters.ConfigParameter,System.Object)">
            <summary>
            Sets the result for the specified parameter.
            </summary>
            <param name="parameterId">The parameter.</param>
            <param name="val">The value.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.TextConverter.AssertNotLocked">
            <summary>
            Asserts that this instance is not locked.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.TextConverter.InputStreamBufferSize">
            <summary>
            Gets or sets the size of the input stream buffer.
            </summary>
            <value>The size of the input stream buffer.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.TextConverter.OutputStreamBufferSize">
            <summary>
            Gets or sets the size of the output stream buffer.
            </summary>
            <value>The size of the output stream buffer.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.TextConverter.TestBoundaryConditions">
            <summary>
            Gets or sets a value indicating whether boundary conditions should be tested.
            </summary>
            <value>
            <c>true</c> if boundary conditions should be tested; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.TextConverter.Locked">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Microsoft.Exchange.Data.TextConverters.TextConverter"/> is locked.
            </summary>
            <value><c>true</c> if locked; otherwise, <c>false</c>.</value>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.inputEncoding">
            <summary>
            The input encoding.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.detectEncodingFromByteOrderMark">
            <summary>
            Value indicating whether encoding should be detected from the BOM
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.detectEncodingFromMetaTag">
            <summary>
            Value indicating whether encoding should be detected from the charset meta tag/
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.outputEncoding">
            <summary>
            The output encoding.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.outputEncodingSameAsInput">
            <summary>
            Value indicating if the output encoding should be the same as the input encoding.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.normalizeInputHtml">
            <summary>
            Value indicating if the HTML input should be normalized.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.injectionFormat">
            <summary>
            The format to use for header and footer injection.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.injectHead">
            <summary>
            The header to inject.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.injectTail">
            <summary>
            The tail to inject.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.filterHtml">
            <summary>
            Value indicating if HTML should be filtered.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.htmlCallback">
            <summary>
            The call back to use when parsing HTML
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testTruncateForCallback">
            <summary>
            Value indicating if truncation should be tested for when a callback is performed.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.outputFragment">
            <summary>
            Value indicating fragmented output can be generated.
            </summary>        
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testMaxTokenRuns">
            <summary>
            The maximum number of tokenisation runs to perform.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testTraceStream">
            <summary>
            The trace stream for tokenisation
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testTraceShowTokenNum">
            <summary>
            Value indicating if the test traces should show the token number.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testTraceStopOnTokenNum">
            <summary>
            The token number at which test tracing should stop.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.maxHtmlTagSize">
            <summary>
            The maximum size of an HTML tag.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testMaxHtmlTagAttributes">
            <summary>
            The maximum number of attributes for an HTML tag
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testMaxHtmlRestartOffset">
            <summary>
            The maximum offset for parsing restarting.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.testMaxHtmlNormalizerNesting">
            <summary>
            The limit for nested tags.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.smallCssBlockThreshold">
            <summary>
            The threshold for small CSS blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.HtmlToHtml.preserveDisplayNoneStyle">
            <summary>
            Value indicating whether display styles should be reserved.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.ConfigParameter">
            <summary>
            Configuration parameters
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConfigParameter.InputEncoding">
            <summary>
            Input Encoding Parameter
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConfigParameter.OutputEncoding">
            <summary>
            Output Encoding parameter
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConfigParameter.RtfCompressionMode">
            <summary>
            Rich Text Format compression mode
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.ConfigParameter.RtfEncapsulation">
            <summary>
            Rich Text Format encapsulation mode
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.Internal.Text.ImageRenderingCallbackInternal">
            <summary>
            Image rendering callbak delegate.
            </summary>
            <param name="attachmentUrl">The attachement URL.</param>
            <param name="approximateRenderingPosition">The approximate rendering position.</param>
            <returns>
            <c>true</c> when image rendering callback is successful; otherwise <c>false</c>.
            </returns>
        </member>
        <member name="T:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput">
            <summary>
            Manages the preparation of encoding output.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Whitespaces">
            <summary>
            Array of white space characters.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.output">
            <summary>
            The encoded output.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.lineWrapping">
            <summary>
            Use line wrapping.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.rfc2646">
            <summary>
            Use the text-plain format parameter.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.longestNonWrappedParagraph">
            <summary>
            The offset of the longest non wrapped paragraph found.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.wrapBeforePosition">
            <summary>
            The offset before the wrap.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.preserveTrailingSpace">
            <summary>
            Preserve trailing spaces.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.preserveTabulation">
            <summary>
            Preserve the tabs.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.preserveNbsp">
            <summary>
            Preserve the non break spaces.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.lineLength">
            <summary>
            The length of the line.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.lineLengthBeforeSoftWrap">
            <summary>
            The length of the line before the soft wrap.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.flushedLength">
            <summary>
            The lenght flushed.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.tailSpace">
            <summary>
            Number of tail spaces.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.breakOpportunity">
            <summary>
            The break opportunity.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.nextBreakOpportunity">
            <summary>
            The next break opportunity.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.quotingLevel">
            <summary>
            The level of quotes.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.wrapped">
            <summary>
            True if buffer is wrapped.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.wrapBuffer">
            <summary>
            The wrap buffer.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.signaturePossible">
            <summary>
            Possible signature.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.anyNewlines">
            <summary>
            True if new lines.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.endParagraph">
            <summary>
            True if end of paragraph found.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.fallbacks">
            <summary>
            When <c>true</c> write this object; otherwise, write null.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.htmlEscape">
            <summary>
            True if escaping html.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.anchorUrl">
            <summary>
            The anchor URL.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.linePosition">
            <summary>
            The line position.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.imageRenderingCallback">
            <summary>
            The image rendering callback delegate.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.#ctor(Microsoft.Exchange.Data.TextConverters.ConverterOutput,System.Boolean,System.Boolean,System.Int32,System.Int32,Microsoft.Exchange.Data.TextConverters.Internal.Text.ImageRenderingCallbackInternal,System.Boolean,System.Boolean,System.Boolean,System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput"/> class.
            </summary>
            <param name="output">The converted output.</param>
            <param name="lineWrapping">if set to <c>true</c> allow line wrapping.</param>
            <param name="flowed">if set to <c>true</c> allow flowing.</param>
            <param name="wrapBeforePosition">The wrap before position.</param>
            <param name="longestNonWrappedParagraph">The longest non wrapped paragraph.</param>
            <param name="imageRenderingCallback">The image rendering callback.</param>
            <param name="fallbacks">if set to <c>true</c> allow fallbacks.</param>
            <param name="htmlEscape">if set to <c>true</c> escape HTML.</param>
            <param name="preserveSpace">if set to <c>true</c> preserve spaces.</param>
            <param name="testTraceStream">The test trace stream.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IFallback#GetUnsafeAsciiMap(System.Byte@)">
            <summary>
            Gets the unsafe ASCII map.
            </summary>
            <param name="unsafeAsciiMask">The unsafe ASCII mask.</param>
            <returns>
            The unsafe ASCII mask byte[].
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IFallback#HasUnsafeUnicode">
            <summary>
            Determines whether has unsafe unicode.
            </summary>
            <returns>
            <c>true</c> if has unsafe unicode; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IFallback#TreatNonAsciiAsUnsafe(System.String)">
            <summary>
            Treat the non ASCII as unsafe.
            </summary>
            <param name="charset">The charset.</param>
            <returns>
            Always returns False
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IFallback#IsUnsafeUnicode(System.Char,System.Boolean)">
            <summary>
            Determines whether is unsafe unicode.
            </summary>
            <param name="ch">The character.</param>
            <param name="isFirstChar">if set to <c>true</c> is first character.</param>
            <returns>
            <c>true</c> if is unsafe unicode; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IFallback#FallBackChar(System.Char,System.Char[],System.Int32@,System.Int32)">
            <summary>
            Encodes the character.
            </summary>
            <param name="ch">The character to encode.</param>
            <param name="outputBuffer">The output buffer.</param>
            <param name="outputBufferCount">The output buffer count.</param>
            <param name="outputEnd">The output end.</param>
            <returns>
            <c>true</c> if encoding is successful; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.System#IDisposable#Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IRestartable#CanRestart">
            <summary>
            Determines whether this instance can restart.
            </summary>
            <returns>
            <c>true</c> if this instance can restart; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IRestartable#Restart">
            <summary>
            Restarts this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IRestartable#DisableRestart">
            <summary>
            Disables the restart.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Microsoft#Exchange#Data#TextConverters#IReusable#Initialize(System.Object)">
            <summary>
            Initializes the specified new source or destination.
            </summary>
            <param name="newSourceOrDestination">The new source or destination.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.CloseDocument">
            <summary>
            Add line terminator to output.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputNewLine">
            <summary>
            Add new line to the output.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputTabulation(System.Int32)">
            <summary>
            Add a tab to the output.
            </summary>
            <param name="count">The count.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputSpace(System.Int32)">
            <summary>
            Add spaces to the output.
            </summary>
            <param name="count">The number of spaces to add.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputNbsp(System.Int32)">
            <summary>
            Add non breaking spaces to the output.
            </summary>
            <param name="count">The number of non breaking spaces to add.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputNonspace(System.Char[],System.Int32,System.Int32,Microsoft.Exchange.Data.TextConverters.TextMapping)">
            <summary>
            Perform encoding.
            </summary>
            <param name="buffer">The buffer to encode.</param>
            <param name="offset">The offset to start encoding at.</param>
            <param name="count">The number of characters to encode.</param>
            <param name="textMapping">The text mapping.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputNonspace(System.String,System.Int32,System.Int32,Microsoft.Exchange.Data.TextConverters.TextMapping)">
            <summary>
            Perform encoding.
            </summary>
            <param name="text">The text to encode.</param>
            <param name="offset">The offset to start encoding at.</param>
            <param name="length">The number of characters to encode.</param>
            <param name="textMapping">The text mapping.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputNonspace(System.Int32,Microsoft.Exchange.Data.TextConverters.TextMapping)">
            <summary>
            Perform encoding.
            </summary>
            <param name="ucs32Literal">The ucs32 literal to encode.</param>
            <param name="textMapping">The text mapping.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Flush">
            <summary>
            Flushes the converted output.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputImage(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Perform image encoding.
            </summary>
            <param name="imageUrl">The image URL.</param>
            <param name="imageAltText">The image alt text.</param>
            <param name="wdthPixels">The image width in pixels.</param>
            <param name="heightPixels">The image height in pixels.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputNonspace(System.String,Microsoft.Exchange.Data.TextConverters.TextMapping)">
            <summary>
            Add non spaces to the output.
            </summary>
            <param name="text">The text the replace with non spaces.</param>
            <param name="textMapping">The text mapping.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.CloseAnchor">
            <summary>
            Closes the anchor.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.CancelAnchor">
            <summary>
            Resets the anchor to null.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.RenderingPosition">
            <summary>
            Renderings the position.
            </summary>
            <returns>
            Offset for the rendering position.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.Reinitialize">
            <summary>
            Reinitializes this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputNonspaceImpl(System.Char[],System.Int32,System.Int32,Microsoft.Exchange.Data.TextConverters.TextMapping)">
            <summary>
            Perform encoding while handling end of paragraph and line wrapping.
            </summary>
            <param name="buffer">The buffer to encode.</param>
            <param name="offset">The offset to start encoding at.</param>
            <param name="count">The number of characters to encode.</param>
            <param name="textMapping">The text mapping.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.WrapBeforePosition">
            <summary>
            Finds offset before the wrap.
            </summary>
            <returns>
            The offset before the wrap position.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.LongestNonWrappedParagraph">
            <summary>
            Find the Longest non wrapped paragraph.
            </summary>
            <returns>
            The offset at the beginning of the paragraph.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.WrapPrepareToAppendNonspace(System.Int32)">
            <summary>
            Wrap in preparation to append non space.
            </summary>
            <param name="count">The count.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.FlushLine(System.Char)">
            <summary>
            Flushes the line.
            </summary>
            <param name="nextChar">The next char.</param>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.FlushTailSpace">
            <summary>
            Add spaces to the end of the output.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.MapAndOutputSymbolCharacter(System.Char,Microsoft.Exchange.Data.TextConverters.TextMapping)">
            <summary>
            Encodes the symbol character.
            </summary>
            <param name="ch">The character to encode.</param>
            <param name="textMapping">The text mapping.</param>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.AnchorUrl">
            <summary>
            Sets the anchor Url.
            </summary>
            <value>The anchor Url.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputCodePageSameAsInput">
            <summary>
            Gets a value indicating whether output code page is same as input.
            </summary>
            <value>
            <c>true</c> if the output code page is the same as input; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.OutputEncoding">
            <summary>
            Sets the output encoding.
            </summary>
            <value>The output encoding.</value>
        </member>
        <member name="P:Microsoft.Exchange.Data.TextConverters.Internal.Text.TextOutput.ImageRenderingCallbackDefined">
            <summary>
            Gets a value indicating whether image rendering callback is defined.
            </summary>
            <value>
            <c>true</c> if image rendering callback is defined; otherwise, <c>false</c>.
            </value>
        </member>
    </members>
</doc>
