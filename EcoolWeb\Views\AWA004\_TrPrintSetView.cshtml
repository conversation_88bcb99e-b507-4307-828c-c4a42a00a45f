﻿@model EcoolWeb.ViewModels.AWA004PrintViewModel


@using (Html.BeginCollectionItem("Chk"))
{

    if (Model.V004 != null)
    {
       

        @Html.HiddenFor(m => m.CheckBoxNo)
        @Html.HiddenFor(m => m.V004.TRANS_NO, new { Value = Model.V004.TRANS_NO })
        @Html.HiddenFor(m => m.V004.USER_NO, new { Value = Model.V004.USER_NO })
        @Html.HiddenFor(m => m.V004.SCHOOL_NO, new { Value = Model.V004.SCHOOL_NO })
        @Html.HiddenFor(m => m.V004.SHORT_NAME, new { Value = Model.V004.SHORT_NAME })
        @Html.HiddenFor(m => m.V004.CLASS_NO, new { Value = Model.V004.CLASS_NO })
        @Html.HiddenFor(m => m.V004.SEAT_NO, new { Value = Model.V004.SEAT_NO })
        @Html.HiddenFor(m => m.V004.NAME, new { Value = Model.V004.NAME })
        @Html.HiddenFor(m => m.V004.SNAME, new { Value = Model.V004.SNAME })
        
      
        @Html.HiddenFor(m => m.V004.TRANS_DATE, new { Value = Model.V004.TRANS_DATE })
        @Html.HiddenFor(m => m.V004.AWARD_NAME, new { Value = Model.V004.AWARD_NAME })

        <div class="panel panel-default">
            <div class="panel-heading">
                @Model.V004.CLASS_NO <span>班</span>  @Model.V004.NAME <span>於</span> <font color="red">@Model.V004.TRANS_DATE</font> 兌換 <font color="red">@Model.V004.AWARD_NAME</font>
                <span>獎品</span>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-md-4 control-label">預計頒獎日</label>
                    <div class="col-md-8">
                        <div class="input-group">
                            @Html.EditorFor(m => m.ExpectedDate, new { htmlAttributes = new { @class = "form-control input-md" } })
                        </div>
                        @Html.ValidationMessageFor(m => m.ExpectedDate, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-4 control-label">頒獎(集合)地點</label>
                    <div class="col-md-8">
                        <div class="input-group">
                            @Html.EditorFor(m => m.Content, new { htmlAttributes = new { @class = "form-control input-md" } })
                        </div>
                        @Html.ValidationMessageFor(m => m.Content, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    }
    else if (Model.V005 != null)
    { 
        @Html.HiddenFor(m => m.CheckBoxNo)
    @Html.HiddenFor(m => m.V005.TRANS_NO, new { Value = Model.V005.TRANS_NO })
    @Html.HiddenFor(m => m.V005.USER_NO, new { Value = Model.V005.USER_NO })
    @Html.HiddenFor(m => m.V005.SCHOOL_NO, new { Value = Model.V005.SCHOOL_NO })
    @Html.HiddenFor(m => m.V005.SHORT_NAME, new { Value = Model.V005.SHORT_NAME })
    @Html.HiddenFor(m => m.V005.CLASS_NO, new { Value = Model.V005.CLASS_NO })
    @Html.HiddenFor(m => m.V005.SEAT_NO, new { Value = Model.V005.SEAT_NO })
    @Html.HiddenFor(m => m.V005.NAME, new { Value = Model.V005.NAME })
    @Html.HiddenFor(m => m.V005.SNAME, new { Value = Model.V005.SNAME })
    @Html.HiddenFor(m => m.V005.TRANS_DATE, new { Value = Model.V005.TRANS_DATE })
    @Html.HiddenFor(m => m.V005.AWARD_NAME, new { Value = Model.V005.AWARD_NAME })

    <div class="panel panel-default">
        <div class="panel-heading">
            @Model.V005.CLASS_NO <span>班</span>  @Model.V005.NAME <span>於</span> <font color="red">@Model.V005.TRANS_DATE</font> 兌換 <font color="red">@Model.V005.AWARD_NAME</font>
            <span>獎品</span>
        </div>
        <div class="panel-body">
            <div class="form-group">
                <label class="col-md-4 control-label">預計頒獎日</label>
                <div class="col-md-8">
                    <div class="input-group">
                        @Html.EditorFor(m => m.ExpectedDate, new { htmlAttributes = new { @class = "form-control input-md" } })
                    </div>
                    @Html.ValidationMessageFor(m => m.ExpectedDate, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-4 control-label">頒獎(集合)地點</label>
                <div class="col-md-8">
                    <div class="input-group">
                        @Html.EditorFor(m => m.Content, new { htmlAttributes = new { @class = "form-control input-md" } })
                    </div>
                    @Html.ValidationMessageFor(m => m.Content, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div>

}
}