/*
 *  /MathJax/jax/output/SVG/fonts/Latin-Modern/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.LatinModernMathJax_Size3={directory:"Size3/Regular",family:"LatinModernMathJax_Size3",id:"LATINMODERNSIZE3",32:[0,0,332,0,0,""],40:[972,472,523,156,461,"461 -459c0 -7 -6 -13 -13 -13c-3 0 -6 1 -8 3c-147 111 -284 390 -284 633v172c0 243 137 522 284 633c2 2 5 3 8 3c7 0 13 -6 13 -13c0 -4 -2 -8 -5 -10c-140 -105 -236 -383 -236 -613v-172c0 -230 96 -508 236 -613c3 -2 5 -6 5 -10"],41:[972,472,523,62,367,"367 164c0 -243 -137 -522 -284 -633c-3 -2 -5 -3 -8 -3c-7 0 -13 6 -13 13c0 4 2 8 5 10c140 105 236 383 236 613v172c0 230 -96 508 -236 613c-3 2 -5 6 -5 10c0 7 6 13 13 13c3 0 5 -1 8 -3c147 -111 284 -390 284 -633v-172"],47:[1374,874,964,57,909,"909 1348c0 -4 -1 -7 -2 -10l-799 -2195c-4 -10 -14 -17 -25 -17c-15 0 -26 12 -26 26c0 4 0 7 1 10l799 2195c4 10 14 17 25 17c15 0 27 -12 27 -26"],91:[975,475,444,226,414,"414 -451c0 -13 -11 -24 -24 -24h-164v1450h164c13 0 24 -11 24 -24s-11 -24 -24 -24h-116v-1354h116c13 0 24 -11 24 -24"],92:[1374,874,964,56,908,"908 -848c0 -14 -12 -26 -27 -26c-11 0 -21 7 -25 17l-799 2195c-1 3 -1 6 -1 10c0 14 11 26 26 26c11 0 21 -7 25 -17l799 -2195c1 -3 2 -6 2 -10"],93:[975,475,444,30,218,"218 975v-1450h-164c-14 0 -24 11 -24 24s10 24 24 24h116v1354h-116c-14 0 -24 11 -24 24s10 24 24 24h164"],123:[975,475,624,100,524,"524 -459c0 -9 -7 -16 -16 -16h-2c-124 19 -232 96 -232 182v361c0 73 -67 152 -160 166c-8 1 -14 8 -14 16s6 15 14 16c93 14 160 93 160 166v361c0 86 108 163 232 182h2c9 0 16 -7 16 -16c0 -8 -6 -15 -14 -16c-92 -14 -160 -86 -160 -150v-361 c0 -79 -73 -149 -168 -182c95 -33 168 -103 168 -182v-361c0 -64 68 -136 160 -150c8 -1 14 -8 14 -16"],124:[1117,617,278,113,166,"166 -591c0 -15 -12 -26 -27 -26s-26 11 -26 26v1682c0 15 11 26 26 26s27 -11 27 -26v-1682"],125:[975,475,624,100,524,"524 250c0 -8 -6 -15 -14 -16c-93 -14 -160 -93 -160 -166v-361c0 -86 -108 -163 -232 -182h-2c-9 0 -16 7 -16 16c0 8 5 15 13 16c93 14 161 86 161 150v361c0 79 73 149 167 182c-94 33 -167 103 -167 182v361c0 64 -68 136 -161 150c-8 1 -13 8 -13 16c0 9 7 16 16 16 h2c124 -19 232 -96 232 -182v-361c0 -73 67 -152 160 -166c8 -1 14 -8 14 -16"],160:[0,0,332,0,0,""],770:[747,-572,919,0,919,"919 596l-7 -24c-152 33 -303 71 -452 113c-150 -42 -301 -80 -453 -113l-7 24c151 56 305 106 460 151c155 -45 308 -95 459 -151"],771:[757,-543,931,0,931,"931 744c0 -3 -1 -5 -2 -7c-55 -80 -113 -135 -213 -156c-19 -4 -38 -6 -57 -6c-69 0 -137 23 -203 47c-60 22 -121 44 -182 44c-16 0 -31 -1 -47 -5c-88 -18 -155 -42 -203 -112c-3 -4 -7 -6 -11 -6c-7 0 -13 6 -13 13c0 3 1 5 2 7c55 80 113 135 213 156 c19 4 38 6 57 6c69 0 137 -23 204 -47c59 -22 120 -44 181 -44c16 0 31 1 47 5c89 18 155 42 204 112c2 4 6 6 10 6c7 0 13 -6 13 -13"],774:[742,-577,937,0,937,"937 734c-53 -157 -278 -157 -469 -157c-190 0 -416 0 -468 157l25 8c35 -106 267 -106 443 -106c177 0 408 0 444 106"],780:[740,-565,919,0,919,"919 716c-151 -56 -304 -106 -459 -151c-155 45 -309 95 -460 151l7 24c152 -33 303 -71 453 -113c149 42 300 80 452 113"],785:[758,-592,937,0,937,"937 600l-25 -8c-36 106 -267 107 -444 107c-176 0 -408 -1 -443 -107l-25 8c52 157 278 158 468 158c191 0 416 -1 469 -158"],812:[-96,271,919,0,919,"919 -121c-151 -56 -304 -106 -459 -150c-155 44 -309 94 -460 150l7 25c152 -33 303 -71 453 -114c149 43 300 81 452 114"],813:[-108,283,919,0,919,"919 -258l-7 -25c-152 33 -303 71 -452 114c-150 -43 -301 -81 -453 -114l-7 25c151 56 305 106 460 150c155 -44 308 -94 459 -150"],814:[-96,262,937,0,937,"937 -104c-53 -158 -278 -158 -469 -158c-190 0 -416 0 -468 158l25 8c35 -107 267 -107 443 -107c177 0 408 0 444 107"],815:[-118,284,937,0,937,"937 -275l-25 -9c-36 107 -267 107 -444 107c-176 0 -408 0 -443 -107l-25 9c52 157 278 157 468 157c191 0 416 0 469 -157"],816:[-118,332,931,0,931,"931 -131c0 -3 -1 -5 -2 -7c-55 -80 -113 -135 -213 -156c-19 -4 -38 -6 -57 -6c-69 0 -137 23 -203 47c-60 22 -121 44 -182 44c-16 0 -31 -1 -47 -5c-88 -18 -155 -42 -203 -112c-3 -4 -7 -6 -11 -6c-7 0 -13 6 -13 13c0 3 1 5 2 7c55 80 113 135 213 156 c19 4 38 6 57 6c69 0 137 -23 204 -47c59 -22 120 -44 181 -44c16 0 31 1 47 5c89 18 155 42 204 112c2 4 6 6 10 6c7 0 13 -6 13 -13"],8214:[1117,617,372,57,317,"110 -591c0 -15 -12 -26 -27 -26s-26 11 -26 26v1682c0 15 11 26 26 26s27 -11 27 -26v-1682zM317 -591c0 -15 -12 -26 -27 -26s-26 11 -26 26v1682c0 15 11 26 26 26s27 -11 27 -26v-1682"],8260:[1374,874,964,57,909,"909 1348c0 -4 -1 -7 -2 -10l-799 -2195c-4 -10 -14 -17 -25 -17c-15 0 -26 12 -26 26c0 4 0 7 1 10l799 2195c4 10 14 17 25 17c15 0 27 -12 27 -26"],8425:[742,-535,1485,0,1485,"1485 742v-183c0 -13 -11 -24 -24 -24s-24 11 -24 24v135h-1389v-135c0 -13 -11 -24 -24 -24s-24 11 -24 24v183h1485"],8730:[1450,950,1000,111,1020,"1020 1430c0 -5 -2 -14 -4 -21l-550 -2333c-6 -26 -9 -26 -42 -26l-231 1073l-68 -107s-14 12 -14 16c0 0 0 3 7 12l131 206l216 -1002h1l512 2175c3 14 6 27 22 27c12 0 20 -9 20 -20"],8739:[1117,617,278,113,166,"166 -591c0 -15 -12 -26 -27 -26s-26 11 -26 26v1682c0 15 11 26 26 26s27 -11 27 -26v-1682"],8741:[1117,617,372,57,317,"110 -591c0 -15 -12 -26 -27 -26s-26 11 -26 26v1682c0 15 11 26 26 26s27 -11 27 -26v-1682zM317 -591c0 -15 -12 -26 -27 -26s-26 11 -26 26v1682c0 15 11 26 26 26s27 -11 27 -26v-1682"],8968:[975,475,499,189,471,"471 951c0 -13 -11 -24 -24 -24h-210v-1378c0 -13 -11 -24 -24 -24s-24 11 -24 24v1426h258c13 0 24 -11 24 -24"],8969:[975,475,499,28,310,"310 -451c0 -13 -11 -24 -24 -24s-24 11 -24 24v1378h-210c-13 0 -24 11 -24 24s11 24 24 24h258v-1426"],8970:[975,475,499,189,471,"471 -451c0 -13 -11 -24 -24 -24h-258v1426c0 13 11 24 24 24s24 -11 24 -24v-1378h210c13 0 24 -11 24 -24"],8971:[975,475,499,28,310,"310 951v-1426h-258c-13 0 -24 11 -24 24s11 24 24 24h210v1378c0 13 11 24 24 24s24 -11 24 -24"],9001:[975,475,537,154,476,"476 -451c0 -13 -11 -24 -24 -24c-10 0 -19 6 -22 15l-274 701c-1 3 -2 6 -2 9s1 6 2 9l274 701c3 9 12 15 22 15c13 0 24 -11 24 -24c0 -3 -1 -6 -2 -9l-270 -692l270 -692c1 -3 2 -6 2 -9"],9002:[975,475,537,61,383,"383 250c0 -3 -1 -6 -2 -9l-274 -701c-3 -9 -12 -15 -22 -15c-13 0 -24 11 -24 24c0 3 1 6 2 9l270 692l-270 692c-1 3 -2 6 -2 9c0 13 11 24 24 24c10 0 19 -6 22 -15l274 -701c1 -3 2 -6 2 -9"],9140:[742,-535,1485,0,1485,"1485 742v-183c0 -13 -11 -24 -24 -24s-24 11 -24 24v135h-1389v-135c0 -13 -11 -24 -24 -24s-24 11 -24 24v183h1485"],9141:[-105,312,1485,0,1485,"1485 -312h-1485v183c0 13 11 24 24 24s24 -11 24 -24v-135h1389v135c0 13 11 24 24 24s24 -11 24 -24v-183"],9180:[767,-509,2012,0,2012,"2012 525c0 -9 -7 -16 -16 -16c-4 0 -8 2 -11 5c-114 113 -486 189 -826 189h-306c-340 0 -712 -76 -826 -189c-3 -3 -7 -5 -11 -5c-9 0 -16 7 -16 16c0 4 2 8 5 11c118 118 495 231 848 231h306c353 0 730 -113 848 -231c3 -3 5 -7 5 -11"],9181:[-79,337,2012,0,2012,"2012 -95c0 -4 -2 -8 -5 -11c-118 -118 -495 -231 -848 -231h-306c-353 0 -730 113 -848 231c-3 3 -5 7 -5 11c0 9 7 16 16 16c4 0 8 -2 11 -5c114 -113 486 -189 826 -189h306c340 0 712 76 826 189c3 3 7 5 11 5c9 0 16 -7 16 -16"],9182:[825,-506,1996,0,1996,"1996 514c0 -5 -4 -8 -8 -8c-3 0 -6 2 -8 5c-28 79 -162 122 -287 122h-400c-149 0 -264 69 -295 144c-31 -75 -146 -144 -295 -144h-400c-125 0 -259 -43 -287 -122c-2 -3 -5 -5 -8 -5c-4 0 -8 3 -8 8v2c33 89 162 181 303 181h400c153 0 287 41 287 120c0 4 4 8 8 8 s8 -4 8 -8c0 -79 134 -120 287 -120h400c141 0 270 -92 303 -181v-2"],9183:[-75,394,1996,0,1996,"1996 -83v-3c-33 -89 -162 -181 -303 -181h-400c-153 0 -287 -41 -287 -119c0 -5 -4 -8 -8 -8s-8 3 -8 8c0 78 -134 119 -287 119h-400c-141 0 -270 92 -303 181v3c0 4 4 8 8 8c3 0 6 -3 8 -6c28 -79 162 -122 287 -122h400c149 0 264 -69 295 -144 c31 75 146 144 295 144h400c125 0 259 43 287 122c2 3 5 6 8 6c4 0 8 -4 8 -8"],9184:[858,-610,2056,0,2056,"2056 610h-76l-172 172h-1560l-172 -172h-76l248 248h1560"],9185:[-180,428,2056,0,2056,"2056 -180l-248 -248h-1560l-248 248h76l172 -172h1560l172 172h76"],10214:[975,475,555,170,532,"532 -451c0 -13 -11 -24 -24 -24h-338v1450h338c13 0 24 -11 24 -24s-11 -24 -24 -24h-121v-1354h121c13 0 24 -11 24 -24zM339 927h-121v-1354h121v1354"],10215:[975,475,555,23,385,"385 -475h-338c-13 0 -24 11 -24 24s11 24 24 24h121v1354h-121c-13 0 -24 11 -24 24s11 24 24 24h338v-1450zM337 927h-121v-1354h121v1354"],10216:[975,475,537,154,476,"476 -451c0 -13 -11 -24 -24 -24c-10 0 -19 6 -22 15l-274 701c-1 3 -2 6 -2 9s1 6 2 9l274 701c3 9 12 15 22 15c13 0 24 -11 24 -24c0 -3 -1 -6 -2 -9l-270 -692l270 -692c1 -3 2 -6 2 -9"],10217:[975,475,537,61,383,"383 250c0 -3 -1 -6 -2 -9l-274 -701c-3 -9 -12 -15 -22 -15c-13 0 -24 11 -24 24c0 3 1 6 2 9l270 692l-270 692c-1 3 -2 6 -2 9c0 13 11 24 24 24c10 0 19 -6 22 -15l274 -701c1 -3 2 -6 2 -9"],10218:[975,475,781,154,720,"476 -451c0 -13 -11 -24 -24 -24c-10 0 -19 6 -22 15l-274 701c-1 3 -2 6 -2 9s1 6 2 9l274 701c3 9 12 15 22 15c13 0 24 -11 24 -24c0 -3 -1 -6 -2 -9l-270 -692l270 -692c1 -3 2 -6 2 -9zM720 -451c0 -13 -11 -24 -24 -24c-10 0 -19 6 -23 15l-274 701c-1 3 -1 6 -1 9 s0 6 1 9l274 701c4 9 13 15 23 15c13 0 24 -11 24 -24c0 -3 -1 -6 -2 -9l-270 -692l270 -692c1 -3 2 -6 2 -9"],10219:[975,475,781,61,627,"627 250c0 -3 -1 -6 -2 -9l-274 -701c-3 -9 -12 -15 -22 -15c-13 0 -24 11 -24 24c0 3 0 6 1 9l271 692l-271 692c-1 3 -1 6 -1 9c0 13 11 24 24 24c10 0 19 -6 22 -15l274 -701c1 -3 2 -6 2 -9zM383 250c0 -3 -1 -6 -2 -9l-274 -701c-3 -9 -12 -15 -22 -15 c-13 0 -24 11 -24 24c0 3 1 6 2 9l270 692l-270 692c-1 3 -2 6 -2 9c0 13 11 24 24 24c10 0 19 -6 22 -15l274 -701c1 -3 2 -6 2 -9"],10222:[991,491,370,142,314,"314 -475c0 -9 -7 -16 -16 -16c-4 0 -8 2 -11 5c-71 70 -145 336 -145 598v276c0 262 74 528 145 598c3 3 7 5 11 5c9 0 16 -7 16 -16c0 -4 -2 -8 -5 -11c-66 -67 -103 -327 -103 -576v-276c0 -249 37 -509 103 -576c3 -3 5 -7 5 -11"],10223:[991,491,370,56,228,"228 112c0 -262 -74 -528 -145 -598c-3 -3 -7 -5 -11 -5c-9 0 -16 7 -16 16c0 4 2 8 5 11c66 67 103 327 103 576v276c0 249 -37 509 -103 576c-3 3 -5 7 -5 11c0 9 7 16 16 16c4 0 8 -2 11 -5c71 -70 145 -336 145 -598v-276"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size3/Regular/Main.js");
