﻿using DotNet.Highcharts;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SECI03IndexViewModel
    {
        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 只顯示某學校
        /// </summary>
        public string whereSCHOOL_NO { get; set; }

        /// <summary>
        /// 只顯示某人
        /// </summary>
        public string whereUSER_NO { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某身份証
        /// </summary>
        public string whereIDNO { get; set; }

        /// <summary>
        /// 只顯示某學年
        /// </summary>
        public byte? whereSYEAR { get; set; }

        /// <summary>
        /// 只顯示某學年
        /// </summary>
        public byte? whereSEMESTER { get; set; }

        /// <summary>
        /// 只顯示某種類別
        /// </summary>
        public byte whereShowType { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// 顯示在手機
        /// </summary>
        public string ShowOnMobile { get; set; }
        public  string LogimUser { get; set; }
        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設20筆
        /// </summary>
        public int PageSize { get; set; }

        public HRMT01 myHRMT01 { get; set; }

        public string TallMemo { get; set; }

        public string WeightMemo { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<SECI03Hrmt08ListViewModel> Hrmt08List;

        /// <summary>
        /// 身高趨示圖
        /// </summary>
        public Highcharts TALLchart;

        /// <summary>
        /// 體重趨示圖
        /// </summary>
        public Highcharts WEIGHTchart;

        /// <summary>
        /// 體適能趨示圖
        /// </summary>
        public Highcharts Healthchart;

        /// <summary>
        /// 列印身高趨示圖
        /// </summary>
        public Highcharts printTALLchart;

        /// <summary>
        /// 列印體重趨示圖
        /// </summary>
        public Highcharts printWEIGHTchart;

        /// <summary>
        /// 體適能趨勢圖 坐姿體前彎
        /// </summary>
        public Highcharts FitnesschartV;

        /// <summary>
        /// 列印體適能趨勢圖 坐姿體前彎
        /// </summary>
        public Highcharts printFitnesschartV;

        /// <summary>
        /// 體適能趨勢圖 立定跳遠
        /// </summary>
        public Highcharts FitnesschartSL;

        /// <summary>
        /// 列印體適能趨勢圖 立定跳遠
        /// </summary>
        public Highcharts printFitnesschartSL;

        /// <summary>
        /// 體適能趨勢圖 仰臥起坐
        /// </summary>
        public Highcharts FitnesschartSU;

        /// <summary>
        /// 列印體適能趨勢圖 仰臥起坐
        /// </summary>
        public Highcharts printFitnesschartSU;

        /// <summary>
        /// 體適能趨勢圖 800公尺跑走
        /// </summary>
        public Highcharts FitnesschartC;

        /// <summary>
        /// 列印體適能趨勢圖 800公尺跑走
        /// </summary>
        public Highcharts prinFitnesschartC;

        public string VisionMemo { get; set; }

        /// <summary>
        /// 視力長條圖 RIGHT
        /// </summary>
        public Highcharts RIGHT_VISIONColumnChart;

        /// <summary>
        /// 視力長條圖 LEFT
        /// </summary>
        public Highcharts LEFT_VISIONColumnChart;

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<SECI03Hrmt09ListViewModel> Hrmt09List;

        public SECI03IndexViewModel()
        {
            PageSize = 20;
            whereShowType = ShowTypeVal.TALL;
        }

        public class ShowTypeVal
        {
            /// <summary>
            /// 身高
            /// </summary>
            static public byte TALL = 1;

            /// <summary>
            /// 體重
            /// </summary>
            static public byte WEIGHT = 2;

            /// <summary>
            /// 視力
            /// </summary>
            static public byte VISION = 3;

            /// <summary>
            /// 體適能
            /// </summary>
            static public byte Fitness = 4;
        }

        public string GetShowTypeName(byte Val)
        {
            if (Val == ShowTypeVal.TALL)
            {
                return "身高";
            }
            else if (Val == ShowTypeVal.WEIGHT)
            {
                return "體重";
            }
            else if (Val == ShowTypeVal.VISION)
            {
                return "視力";
            }
            else if (Val == ShowTypeVal.Fitness)
            {
                return "體適能";
            }
            else
            {
                return "";
            }
        }
    }
}