﻿using com.ecool.service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;

namespace EcoolWeb.Controllers.rpp
{
    public class rppController : Controller
    {
        private string SchoolNo = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private UserProfile user;

        // GET: rpp
        public ActionResult rpp()
        {
            user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }

            ViewBag.BookExplain = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNo).FirstOrDefault().BOOKEXPLAIN;
            return View();
        }

        //閱讀護照閱讀書單
        public ActionResult rpp_book()
        {
            try
            {
                ViewData["BOOK_HTB"] = new rppService().USP_RPP_BOOK_QUERY(SchoolNo);
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }
            return View();
        }

        //閱讀護照護照認證
        public ActionResult rpp_all()
        {
            return View();
        }
    }
}