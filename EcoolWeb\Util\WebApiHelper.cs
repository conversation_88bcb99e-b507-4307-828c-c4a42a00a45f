﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Web;
using System.Web.Http.Controllers;
using UAParser;

namespace EcoolWeb.Util
{
    public class WebApiHelper
    {
        private static string CheckIdName = "CheckId";
        private static string UUIDName = "UUID";
        private static string OS_TYPEName = "OS_TYPE";
        private static log4net.ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// 寫入HRMT05_CHECK_LOG
        /// </summary>
        public static void InsertCkLog(ApiBaseModel Data, string Msg, string SchoolNO = null, string USER_NO = null, DateTime? LAST_LOGIN_TIME = null, int? TempNO = null, byte? OS_TYPE = null)
        {
            // 判斷 是否啟用
            if (System.Web.Configuration.WebConfigurationManager.AppSettings["EnableIsWriteAppLog"] != null
                || Convert.ToBoolean(System.Web.Configuration.WebConfigurationManager.AppSettings["EnableIsWriteAppLog"]))
            {
                using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                {
                    var userAgent = HttpContext.Current.Request.UserAgent;
                    var uaParser = Parser.GetDefault();
                    ClientInfo c = uaParser.Parse(userAgent);

                    HRMT05_CHECK_LOG log = new HRMT05_CHECK_LOG();
                    log.CRE_DATE = DateTime.Now;
                    log.CHECK_ID = Data.CheckId;
                    log.UUID = Data.UUID;
                    log.IP = System.Web.HttpContext.Current.Request.UserHostAddress;
                    log.SCHOOL_NO = SchoolNO;
                    log.USER_NO = USER_NO;
                    log.OS_TYPE = OS_TYPE;
                    log.LAST_LOGIN_TIME = LAST_LOGIN_TIME;
                    log.TempNO = TempNO;
                    log.MSG = "OS:" + c.OS.Family.ToString() + ",UserAgent:" + c.UserAgent.Family.ToString() + ",Device:" + c.Device.Family.ToString() + Msg;

                    log.URL = System.Web.HttpContext.Current.Request.RawUrl;

                    db.HRMT05_CHECK_LOG.Add(log);

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception)
                    {
                    }
                }
            }
        }

        public static void InsertCkLog(LoginAppFirstInAppViewModel FSd, string Msg, string SchoolNO = null, string USER_NO = null, DateTime? LAST_LOGIN_TIME = null, int? TempNO = null, byte? OS_TYPE = null)
        {
            ApiBaseModel Data = new ApiBaseModel();

            Data.UUID = FSd.UUID;

            if (string.IsNullOrWhiteSpace(SchoolNO))
            {
                SchoolNO = FSd.SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(USER_NO))
            {
                USER_NO = FSd.USER_NO;
            }

            if (OS_TYPE == null)
            {
                OS_TYPE = FSd.OS_TYPE;
            }

            InsertCkLog(Data, Msg, SchoolNO, USER_NO, LAST_LOGIN_TIME, TempNO, OS_TYPE);
        }

        /// <summary>
        /// 寫入HRMT05_CHECK_LOG
        /// </summary>
        public static void UInsertCkLog(ApiBaseModel Data, string Msg, string SchoolNO = null, UserProfile user = null, DateTime? LAST_LOGIN_TIME = null, int? TempNO = null, byte? OS_TYPE = null)
        {
            if (user != null)
            {
                InsertCkLog(Data, Msg, user.SCHOOL_NO, user.USER_NO, LAST_LOGIN_TIME, TempNO, OS_TYPE);
            }
            else
            {
                InsertCkLog(Data, Msg, SchoolNO, "", LAST_LOGIN_TIME, TempNO, OS_TYPE);
            }
        }

        /// <summary>
        /// 取出網址列之驗証碼
        /// </summary>
        /// <param name="actionContext">HttpActionContext</param>
        /// <returns>ApiBaseModel</returns>
        public static ApiBaseModel GetApiBaseModel(HttpActionContext actionContext)
        {
            if (actionContext == null)
            {
                return null;
            }

            ApiBaseModel ReturnDb = new ApiBaseModel();

            var queryString = actionContext.Request
               .GetQueryNameValuePairs()
               .ToDictionary(x => x.Key, x => x.Value);

            if (queryString.Count() > 0)
            {
                ReturnDb.CheckId = queryString.Where(a => a.Key == CheckIdName).Select(a => a.Value).FirstOrDefault();
                ReturnDb.UUID = queryString.Where(a => a.Key == UUIDName).Select(a => a.Value).FirstOrDefault();
            }

            return ReturnDb;
        }

        /// <summary>
        /// 取出網址列之驗証碼
        /// </summary>
        /// <param name="Request"></param>
        /// <returns></returns>
        public static ApiBaseModel GetApiBaseModelALL(HttpRequestMessage Request)
        {
            log4net.ILog logger = LogManager.GetLogger("GetApiBaseModelALL");
            if (Request == null)
            {
                return null;
            }

            ApiBaseModel ReturnDb = new ApiBaseModel();

            var query = HttpUtility.ParseQueryString(Request.RequestUri.Query);
            ReturnDb.CheckId = query[CheckIdName];
            ReturnDb.UUID = query[UUIDName];

            if (query[OS_TYPEName] != null)
            {
                if (!string.IsNullOrWhiteSpace(query[OS_TYPEName]))
                {
                    ReturnDb.OS_TYPE = Convert.ToByte(query[OS_TYPEName]);
                }
            }
            logger.Info("GetApiBaseModelALL" + ReturnDb.CheckId);
            ReturnDb.RealCheckId = ECOOL_APP.AppHelper.GetDecryptCheckId(ReturnDb.CheckId, ReturnDb.UUID, ReturnDb.OS_TYPE);

            return ReturnDb;
        }

        public static ApiBaseModel GetApiBaseModelALL(HttpRequest Request)
        {
            if (Request == null)
            {
                return null;
            }

            ApiBaseModel ReturnDb = new ApiBaseModel();

            ReturnDb.CheckId = Request[CheckIdName];
            ReturnDb.UUID = Request[UUIDName];

            if (string.IsNullOrEmpty(ReturnDb.CheckId) == false)
            {
                if (ReturnDb.CheckId.Length <= 5)
                {
                    ReturnDb.RealCheckId = ReturnDb.CheckId;
                }
                else
                {
                    ReturnDb.RealCheckId = ECOOL_APP.AppHelper.GetDecryptCheckId(ReturnDb.CheckId, ReturnDb.UUID);
                }
            }

            return ReturnDb;
        }

        public static bool GetCKSetSession(HttpRequestBase Request)
        {
            ApiBaseModel Base = new ApiBaseModel();
            Base.CheckId = Request.QueryString[CheckIdName];
            Base.UUID = Request.QueryString[UUIDName];

            string RealCheckId;
            bool Ok = ECOOL_APP.AppHelper.checkCheckId(Base.CheckId, Base.UUID, out RealCheckId);

            //檢查驗証是否與確 進入後 每頁的驗証
            if (Ok == false)
            {
                InsertCkLog(Base, "每頁驗証失敗");
                return false;
            }
            else
            {
                Base.RealCheckId = RealCheckId;
                ApiSetSession(Base);
                return true;
            }
        }

        public static bool GetCKSetSession(HttpRequestMessage Request)
        {
            ApiBaseModel Base = GetApiBaseModelALL(Request);

            string RealCheckId;
            bool Ok = ECOOL_APP.AppHelper.checkCheckId(Base.CheckId, Base.UUID, out RealCheckId);

            //檢查驗証是否與確 進入後 每頁的驗証
            if (Ok == false)
            {
                InsertCkLog(Base, "每頁驗証失敗");
                return false;
            }
            else
            {
                Base.RealCheckId = RealCheckId;
                ApiSetSession(Base);
                return true;
            }
        }

        /// <summary>
        /// 從Web Api 進入 ，從驗証檔 取出資料 塞進 UserProfile Session 裡
        /// </summary>
        /// <param name="souDb"></param>
        public static void ApiSetSession(ApiBaseModel souDb)
        {

            if (string.IsNullOrWhiteSpace(souDb.UUID) == false && string.IsNullOrWhiteSpace(UserProfileHelper.GetUUID()) == true)
            {
                logger.Info("UserProfileHelper.GetUUID()");
                UserProfileHelper.SetUUID(souDb.UUID);
            }
            else {
                if (string.IsNullOrWhiteSpace(souDb.UUID) == true) {
                    logger.Info("UserProfileHelper.GetUUID() 為空 UUID");
                }
                else if (string.IsNullOrWhiteSpace(UserProfileHelper.GetUUID()) == false)
                {
                    souDb.UUID = UserProfileHelper.GetUUID();
                    UserProfileHelper.SetUUID(souDb.UUID);
                    // logger.Info("UserProfileHelper.GetUUID() 為空 GetUUID");
                }
                else if (string.IsNullOrWhiteSpace(souDb.UUID) == false)
                {
                    logger.Info("UserProfileHelper.UUID() 為空 UUID"+ souDb.UUID);
                    UserProfileHelper.SetUUID(souDb.UUID);

                }
            }
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            if (user != null && string.IsNullOrWhiteSpace(SchoolNO) == false)
            {
                logger.Info("Session 已存在 user !null" );
                logger.Info("Session 已存在 user !null"+user.USER_NO);
                UInsertCkLog(souDb, "Session 已存在", SchoolNO, user);
                UserProfileHelper.Set(user);
             
            }

          else  if (user == null || string.IsNullOrWhiteSpace(SchoolNO))
            {
                using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                {
                    logger.Info("Session 已存在 user null"+ souDb.UUID);
                    HRMT05_CHECK ckDb = db.HRMT05_CHECK.Where(a => a.UUID == souDb.UUID
                    // && a.CHECK_ID == souDb.RealCheckId
                    ).FirstOrDefault();

                    if (ckDb != null)
                    {
                        if (string.IsNullOrWhiteSpace(ckDb.USER_NO) == false)
                        {
                            HRMT01 FindUser = db.HRMT01.Where(a => a.SCHOOL_NO == ckDb.SCHOOL_NO && a.USER_NO == ckDb.USER_NO).FirstOrDefault();

                            if (FindUser != null)
                            {
                                //填入UserProfile
                                UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                                UserProfileHelper.Set(LoginUser);
                                InsertCkLog(souDb, "會員 Session 不存在，重寫進Session", FindUser.SCHOOL_NO, FindUser.USER_NO, ckDb.LAST_LOGIN_TIME, ckDb.TempNO, ckDb.OS_TYPE);
                            }
                            else
                            {
                                InsertCkLog(souDb, "會員 Session 不存在，但HRMT01 無此帳號", ckDb.SCHOOL_NO, ckDb.USER_NO, ckDb.LAST_LOGIN_TIME, ckDb.TempNO, ckDb.OS_TYPE);
                            }
                        }
                        else if (string.IsNullOrWhiteSpace(ckDb.SCHOOL_NO) == false)
                        {
                            UserProfileHelper.Set(ckDb.SCHOOL_NO);
                            SchoolNO = UserProfileHelper.GetSchoolNo();
                            InsertCkLog(souDb, "訪客 Session 不存在，重寫進Session", ckDb.SCHOOL_NO, ckDb.USER_NO);
                        }
                        else
                        {
                            InsertCkLog(souDb, "異常，未選學校");
                        }
                    }
                    else
                    {
                        UInsertCkLog(souDb, "Session 不存在，但 HRMT05_CHECK 不存在");
                    }
                }
            }
        }
    }
}