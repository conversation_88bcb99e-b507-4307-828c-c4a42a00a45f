﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Dapper;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;

namespace ECOOL_APP
{
    public class UserProfile : ICloneable
    {
        public static UserProfile FillUserProfile(HRMT01 FindUser)
        {
            UserProfile LoginUser = new UserProfile();
            LoginUser.FromHRMT01(FindUser);
            HrmtImportService hrmtImportServiceItem = new HrmtImportService();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            HRMT24 HT24 = db.HRMT24.Where(A => A.ROLE_ID == LoginUser.RoleID_Default).FirstOrDefault();
            if (HT24 != null)
            {
                LoginUser.ROLE_LEVEL = HT24.ROLE_LEVEL;
                LoginUser.ROLE_TYPE = HT24.ROLE_TYPE;
            }

            BDMT01 theSchool = db.BDMT01.Find(new object[] { LoginUser.SCHOOL_NO });
            if (theSchool != null) LoginUser.SCHOOL_NAME = theSchool.SHORT_NAME;

            LoginUser.TEACH_CLASS_NO = GetTeachCLASS_NO(LoginUser, ref db);

            ADDT09 a9 = db.ADDT09.Where(user => user.SCHOOL_NO == LoginUser.SCHOOL_NO && user.USER_NO == LoginUser.USER_NO).FirstOrDefault();
            if (a9 != null)
            {
                LoginUser.LEVEL_ID = Convert.ToInt16(a9.LEVEL_ID);
            }

            LoginUser.RUN_TOTAL_METER = Math.Round((db.ADDT25.Where(a => a.SCHOOL_NO == LoginUser.SCHOOL_NO && a.USER_NO == LoginUser.USER_NO)
                .Select(a => a.RUN_TOTAL_METER).FirstOrDefault() ?? 0) / 1000.0, 1);

            ADDT25_IMG nexttop = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == SharedGlobal.ALL && a.RUN_TOTAL_KM <= (decimal)LoginUser.RUN_TOTAL_METER).OrderByDescending(a => a.RUN_TOTAL_KM).FirstOrDefault();
            LoginUser.LOCATION_NAME = nexttop.LOCATION_NAME;

            LoginUser.LisTBookPassport = GetOenUseADDT04toShort(LoginUser.USER_NO, LoginUser.SCHOOL_NO, ref db);
            bool IScheckbell = false;
            IScheckbell = hrmtImportServiceItem.CheckStudentTasklist(LoginUser);
            LoginUser.checkstudenttasklist = IScheckbell;
            RefreshCashInfo(LoginUser, ref db);

            return LoginUser;
        }

        /// <summary>
        /// 當是導師取得自已的班級
        /// </summary>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static string GetTeachCLASS_NO(UserProfile User, ref ECOOL_DEVEntities db)
        {
            string ReturnVal = null;
            Regex rx = new Regex(@"_");
            
            if (User.USER_TYPE == UserType.Teacher)
            {
                if (User.SCHOOL_NO != null && rx.Matches(User.SCHOOL_NO).Count > 0)
                {
                    string[] strSchool = new string[2];
                    strSchool = User.SCHOOL_NO.Split('_');
                    User.SCHOOL_NO = strSchool[0];

                }
                //先判斷是否為導師角色 (9)
                if (db.HRMT25.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.USER_NO == User.USER_NO && a.ROLE_ID == HRMT24_ENUM.ROLE_CLASS_TEACH).Any())
                {
                    ReturnVal =
                        db.HRMT03.Where(h3 => h3.SCHOOL_NO == User.SCHOOL_NO && h3.TEACHER_NO == User.USER_NO).Select(a => a.CLASS_NO).FirstOrDefault();
                }

               
            }

            return ReturnVal;
        }
        public static short GetUseCashLimit(string SCHOOL_NO, string USER_NO, string USER_TYPE, ref ECOOL_DEVEntities db, out short ThisMonthCash, out string ErrMeg)
        {
            ErrMeg = string.Empty;
            ThisMonthCash = 0;

            if (string.IsNullOrWhiteSpace(USER_NO))
            {
                return 0;
            }

            if (USER_TYPE == UserType.Student)
            {
                ErrMeg = "學生沒有給點權限";
                return 0;
            }

            if (USER_TYPE == UserType.Admin)
            {
                return short.MaxValue;
            }

            if (db == null)
            {
                db = new ECOOL_DEVEntities();
            }
            BDMT01 theSchool = db.BDMT01.Find(new object[] { SCHOOL_NO });
            if (theSchool == null)
            {
                ErrMeg = "找不到學校";
                return 0;
            }

            if (theSchool.CHECK_CASH_LIMIT.HasValue)
            {
                if (theSchool.CHECK_CASH_LIMIT.Value == false) return short.MaxValue;
            }

            HRMT01 T01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();
            short CashLimit = 0;

            int sumBalance = 0;
            //查詢本月以給點的總數
            string[] myInClause = new string[] { "批次特殊加扣點", "即時加點特殊加扣點", "特殊加扣點", "批次校內表現班級小幫手", "批次快速大量加點" };
            List<AWAT01_LOG> aWAT01_s = new List<AWAT01_LOG>();
            List<AWAT01_LOG> aWAT01_s1 = new List<AWAT01_LOG>();
            DateTime StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            

            string sSQL = $@"select 　 case when count(*)=0 or SUMCASH is null then 0 else SUMCASH end from
(select SUM(CASH_IN) SUMCASH from AWAT01_LOG where SCHOOL_NO=@SCHOOL_NO  and LOG_PERSON =@LOG_PERSON and  YEAR(LOG_TIME)=YEAR(GETDATE()) and  Month(LOG_TIME)=Month(GETDATE()) ) y
group by SUMCASH";
            var temp = db.Database.Connection.Query<int>(sSQL, new
            {
                SCHOOL_NO = SCHOOL_NO,
                LOG_PERSON = T01.USER_KEY

            });
            string sSQl1 = $@"select 　 y.USER_NO,y.SOURCE_NO,y.CASH_IN,y.LOG_TABLE  from
(select *  from AWAT01_LOG where SCHOOL_NO=@SCHOOL_NO  and LOG_PERSON =@LOG_PERSON  and  YEAR(LOG_TIME)=YEAR(GETDATE()) and  Month(LOG_TIME)=Month(GETDATE()) ) y ";
            var temp1 = db.Database.Connection.Query<GetListMony>(sSQl1, new
            {
                SCHOOL_NO = SCHOOL_NO,
                LOG_PERSON = T01.USER_KEY

            });
            int HRMT25Count = 0;
            int ADDT38Count = 0;
            int REDEAYtoPAy = 0;
            DateTime dts = new DateTime();
            DateTime dte= new DateTime();
            dts = DateTime.Now;
            dts = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01"));
            dte = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01 23:59:59")).AddMonths(1).AddDays(-1);
            //HRMT25Count = db.HRMT25.Where(x => x.SCHOOL_NO == T01.SCHOOL_NO && x.USER_NO == T01.USER_NO && x.ROLE_ID == "9").Count();
            var ADDT37Item = (from x in db.ADDT37
                              join y in db.ADDT38 on x.ROLL_CALL_ID equals y.ROLL_CALL_ID into temp2
                              from ds in temp2.DefaultIfEmpty()
                              where x.DEL_YN == "N" && ds.CRE_DATE >=dts && ds.CRE_DATE <= dte
                              //&& ds.SUBJECT == "小獎勵(班級加點，受點數控管)" 
                              && x.CRE_PERSON == T01.USER_KEY &&
                              x.CRE_PERSON == ds.CRE_PERSON && x.SCHOOL_NO == ds.SCHOOL_NO  && x.CRE_PERSON== T01.USER_KEY && x.SCHOOL_NO == SCHOOL_NO
                              select new
                              {

                                  CASH = ds.CASH
                              }
                             ).ToList();
            var ADDT37temp = (from x in db.ADDT37
                              join y in db.ADDT38 on x.ROLL_CALL_ID equals y.ROLL_CALL_ID into temp2
                              from ds in temp2.DefaultIfEmpty()
                              where x.DEL_YN == "N" && ds.CRE_DATE >= dts && ds.CRE_DATE <= dte
                              //&& ds.SUBJECT == "小獎勵(班級加點，受點數控管)" 
                              && x.CRE_PERSON == T01.USER_KEY &&
                              x.CRE_PERSON == ds.CRE_PERSON && x.SCHOOL_NO == ds.SCHOOL_NO && ds.BATCH_CASH_ID != null && x.CRE_PERSON == T01.USER_KEY && x.SCHOOL_NO == SCHOOL_NO
                              select new
                              {

                                  CASH = ds.CASH
                              }
                                 ).ToList();

            if (ADDT37Item != null && ADDT37Item.Count() > 0)
            {

                ADDT38Count = ADDT37Item.Sum(J => J.CASH).Value;
            }
            if (ADDT37temp != null && ADDT37temp.Count() > 0)
            {


                REDEAYtoPAy = ADDT37temp.Sum(y => y.CASH).Value;

            }
           
            foreach (var item in temp1)
            {
              
                if (item.LOG_TABLE == "ADDT14")
                {
                    int? ADD14sum = 0;
                    int ADDT14Str ;
                    string ADDT14Str1 = "";
                    int temp1Count = 0;
                    ADD14sum = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.USER_NO == item.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == item.CASH_IN).Sum(x => x.CASH);
                    ADDT14Str = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.USER_NO == item.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == item.CASH_IN).Select(x => x.IAWARD_ID).FirstOrDefault();
                    ADDT14Str1 = ADDT14Str.ToString();
                    temp1Count = temp1.Where(x => x.SOURCE_NO == ADDT14Str1).Count();
                    if (ADD14sum != null && temp1Count==0)
                    {
                        sumBalance += (int)ADD14sum;

                    }

                }
                else if (item.LOG_TABLE == "ADDT15")
                {
                    int? ADDT15sum = 0;
                   
                    int ADDT15Str;
                    string ADDT15Str1 = "";
                    int temp1Count = 0;
                    ADDT15sum = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.USER_NO == item.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == item.CASH_IN).Sum(x => x.CASH);
                    ADDT15Str = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.USER_NO == item.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == item.CASH_IN).Select(x => x.OAWARD_ID).FirstOrDefault();
                    ADDT15Str1 = ADDT15Str.ToString();
                    temp1Count = temp1.Where(x => x.SOURCE_NO == ADDT15Str1).Count();
                    if (ADDT15sum != null && temp1Count == 0)
                    {
                        sumBalance += (int)ADDT15sum;

                    }
                }


                else if (item.LOG_TABLE == "ADDT20")
                {
                    int? ADDT20sum = 0; 

                    int ADDT20Str;
                    string ADDT20Str1 = "";
                    int temp1Count = 0;
                    ADDT20sum = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.USER_NO == item.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == item.CASH_IN).Sum(x => x.CASH);
                    ADDT20Str1 = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.USER_NO == item.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == item.CASH_IN).Select(x => x.BATCH_CASH_ID).FirstOrDefault();
          
                    temp1Count = temp1.Where(x => x.SOURCE_NO == ADDT20Str1).Count();
                    if (ADDT20sum != null && temp1Count == 0)
                    {
                        sumBalance += (int)ADDT20sum;

                    }
                }
            }

            int?   MonthCash = temp.FirstOrDefault();

            if (MonthCash.HasValue)
            {
                if (MonthCash.Value >= short.MaxValue)
                {
                    ThisMonthCash = short.MaxValue;
                }
                else if (MonthCash.Value <= short.MinValue)
                {
                    ThisMonthCash = short.MinValue;
                }
                else
                {
                    ThisMonthCash = Convert.ToInt16(MonthCash + ADDT38Count -sumBalance- REDEAYtoPAy);
                    if (ThisMonthCash < 0)
                    {
                        ThisMonthCash = 0;
                    }
                }
            }

            return ThisMonthCash;
        }
        /// <summary>
        /// 查詢教師給點每個月給點上限
        /// </summary>
        /// <param name="SCHOOL_NO">學校代號</param>
        /// <param name="USER_NO">使用者代號</param>
        /// <param name="USER_TYPE">使用者類型(限教師 USER_TYPE='T')</param>
        /// <param name="db"></param>
        /// <param name="ThisMonthCash">取得此位教師本月已發出的點數</param>
        /// <param name="ErrMeg"></param>
        /// <returns></returns>
        public static int GetCashLimit(string SCHOOL_NO, string USER_NO, string USER_TYPE, ref ECOOL_DEVEntities db, out short ThisMonthCash, out string ErrMeg)
        {
            ErrMeg = string.Empty;
            ThisMonthCash = 0;

            if (string.IsNullOrWhiteSpace(USER_NO))
            {
                return 0;
            }

            if (USER_TYPE == UserType.Student)
            {
                ErrMeg = "學生沒有給點權限";
                return 0;
            }

            if (USER_TYPE == UserType.Admin)
            {
                return short.MaxValue;
            }

            if (db == null)
            {
                db = new ECOOL_DEVEntities();
            }
            BDMT01 theSchool = db.BDMT01.Find(new object[] { SCHOOL_NO });
            if (theSchool == null)
            {
                ErrMeg = "找不到學校";
                return 0;
            }

            if (theSchool.CHECK_CASH_LIMIT.HasValue)
            {
                if (theSchool.CHECK_CASH_LIMIT.Value == false) return short.MaxValue;
            }

            HRMT01 T01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();
            int CashLimit = 0;
            if (T01.GIVE_CASH_LIMIT.HasValue)
            {
                if (T01.GIVE_CASH_LIMIT.Value > 0) CashLimit = T01.GIVE_CASH_LIMIT.Value;
            }
            else if (theSchool.TEACHER_CASH_LIMIT.HasValue)
            {
                if (theSchool.TEACHER_CASH_LIMIT.Value > 0) CashLimit = theSchool.TEACHER_CASH_LIMIT.Value;
            }
            DateTime dts = new DateTime();
            DateTime dte = new DateTime();
            dts = DateTime.Now;
            dts = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01"));
            dte = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01 23:59:59")).AddMonths(1).AddDays(-1);
            //查詢本月以給點的總數
            string[] myInClause = new string[] { "批次特殊加扣點", "即時加點特殊加扣點", "特殊加扣點", "批次校內表現班級小幫手", "批次快速大量加點" };
            List<AWAT01_LOG> aWAT01_s = new List<AWAT01_LOG>();
            List<AWAT01_LOG> aWAT01_s1 = new List<AWAT01_LOG>();
            DateTime StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            int? NewMonthCash = 0;//紙本點數
            int? NewADDT26MonthCash = 0;//抽獎點數
            int CashSuNum = 0;
            int? MonthCash = 0;
            int AWAT01_LOGCount = 0;//紙本點數
            int? ADDT26MonthCash = 0;
            int AWAT01_ADDT26Count = 0;//抽獎點數
            CashSuNum = db.AWAT01_LOG.Where(a => a.SCHOOL_NO == SCHOOL_NO &&
                   (a.SOURCE_TYPE == "ADDI09" || a.SOURCE_TYPE == "ADDI13" || a.SOURCE_TYPE == "ADDT26_D")
                   && (a.LOG_DESC.Contains("批次特殊加扣點") || a.LOG_DESC.Contains("即時加點特殊加扣點")
                   || a.LOG_DESC.Contains("特殊加扣點") || a.LOG_DESC.Contains("批次校內表現班級小幫手")
                   || a.LOG_DESC.Contains("批次校內表現班級幫手和榮譽") || a.LOG_DESC.Contains("校內表現-班級幫手和榮譽")
                   || a.LOG_DESC.Contains("班級幫手和榮譽") || a.LOG_DESC.Contains("校內表現-班級服務")
                   || a.LOG_DESC.Contains("小獎勵(班級加點，受點數控管)")
                     || a.LOG_DESC.Contains("班級幫手和榮譽") || a.LOG_DESC.Contains("班級服務")
                   || a.LOG_DESC.Contains("批次快速大量加點-特殊加扣點"))
                   && a.LOG_PERSON == T01.USER_KEY && a.LOG_TIME >= StartDate 
                 //&& !a.LOG_DESC.Contains("特殊加扣點-小獎勵(班級加點，受點數控管)")
                   ).Count();
            var AWAT01LOGtemP = db.AWAT01_LOG.Join(db.ADDT26_D, o => o.SOURCE_NO, p => p.LEVEL_NO,
    (c, s) => new { c.SCHOOL_NO, s.LEVEL_NO, c.SOURCE_TYPE, s.GAME_NO, c.CASH_IN, s.CHG_PERSON, c.LOG_TIME, c.LOG_DESC })         //將join完的新集合再做一次join
               .Join(db.ADDT26, o => new { o.GAME_NO, o.SCHOOL_NO }, p => new { p.GAME_NO, p.SCHOOL_NO },//組成新物件做比對
               (o, p) => new { o.CASH_IN, o.SOURCE_TYPE, p.CHG_PERSON, o.LOG_TIME, o.LOG_DESC });
            var yCount =
                 AWAT01LOGtemP.Where(x => x.CHG_PERSON == T01.USER_KEY && x.LOG_TIME >= StartDate && x.SOURCE_TYPE == "ADDT26_D" && (x.LOG_DESC.Contains("班級幫手和榮譽")
                || x.LOG_DESC.Contains("班級服務") || x.LOG_DESC.Contains("小獎勵(班級加點，受點數控管)"))).Select(x => new
                {
                    cash =
                x.CASH_IN == null ? 0 : x.CASH_IN


                }).Count();
            if (CashSuNum > 0) {
                MonthCash =
                     db.AWAT01_LOG.Where(a => a.SCHOOL_NO == SCHOOL_NO &&
                     (a.SOURCE_TYPE == "ADDI09" || a.SOURCE_TYPE == "ADDI13" || a.SOURCE_TYPE == "ADDT26_D")
                     && (a.LOG_DESC.Contains("批次特殊加扣點") || a.LOG_DESC.Contains("即時加點特殊加扣點")
                     || a.LOG_DESC.Contains("特殊加扣點") || a.LOG_DESC.Contains("批次校內表現班級小幫手")
                     || a.LOG_DESC.Contains("批次校內表現班級幫手和榮譽") 
                     || a.LOG_DESC.Contains("校內表現-班級幫手和榮譽") || a.LOG_DESC.Contains("班級幫手和榮譽") 
                     || a.LOG_DESC.Contains("校內表現-班級服務") || a.LOG_DESC.Contains("小獎勵(班級加點，受點數控管)")
                     || a.LOG_DESC.Contains("班級幫手和榮譽") || a.LOG_DESC.Contains("班級服務")
                     || a.LOG_DESC.Contains("批次快速大量加點-特殊加扣點"))
                     && a.LOG_PERSON == T01.USER_KEY && a.LOG_TIME >= StartDate 
                     //&& !a.LOG_DESC.Contains("特殊加扣點-小獎勵(班級加點，受點數控管)")
                     ).Sum(a => a.CASH_IN);
                //db.AWAT01_LOG.Join()
                //    .Where(a => a.SCHOOL_NO == SCHOOL_NO&& a.SOURCE_TYPE == "ADDT26_D")

               
             
            }
            if (yCount > 0) {
     
      

                var CH = AWAT01LOGtemP.Where(x => x.CHG_PERSON == T01.USER_KEY && x.LOG_TIME >= StartDate && x.SOURCE_TYPE == "ADDT26_D" && (x.LOG_DESC.Contains("班級幫手和榮譽")
                || x.LOG_DESC.Contains("班級服務") || x.LOG_DESC.Contains("小獎勵(班級加點，受點數控管)"))).Select(x => new { cash =
                x.CASH_IN==null?0 : x.CASH_IN


                }).ToList();
                ADDT26MonthCash= CH.Sum(j=>j.cash);
                if (MonthCash == null) {
                    MonthCash = 0;

                }
            
            }
            AWAT01_LOGCount = db.AWAT01_LOG.Where(a => a.SCHOOL_NO == SCHOOL_NO &&
                 a.SOURCE_TYPE == "ADDI13"

                 && a.LOG_PERSON == T01.USER_KEY && a.LOG_TIME >= StartDate).Count();

            //AWAT01_ADDT26Count = db.AWAT01_LOG.Where(a => a.SCHOOL_NO == SCHOOL_NO &&
            //      a.SOURCE_TYPE == "ADDT26_D"

            //      && a.LOG_PERSON == T01.USER_KEY && a.LOG_TIME >= StartDate).Count();

            if (AWAT01_LOGCount > 0) {
                NewMonthCash = db.AWAT01_LOG.Where(a => a.SCHOOL_NO == SCHOOL_NO &&
                     a.SOURCE_TYPE == "ADDI13"

                     && a.LOG_PERSON == T01.USER_KEY && a.LOG_TIME >= StartDate).Sum(a => a.CASH_IN);
            }
            //if (AWAT01_ADDT26Count > 0) {

            //    NewADDT26MonthCash = db.AWAT01_LOG.Where(a => a.SCHOOL_NO == SCHOOL_NO &&
            //      a.SOURCE_TYPE == "ADDT26_D"

            //      && a.LOG_PERSON == T01.USER_KEY && a.LOG_TIME >= StartDate).Sum(a => a.CASH_IN);


            //}
            int HRMT25Count = 0;
            int ADDT38Count = 0;
            int REDEAYtoPAy = 0;
            //HRMT25Count = db.HRMT25.Where(x => x.SCHOOL_NO == T01.SCHOOL_NO && x.USER_NO == T01.USER_NO && x.ROLE_ID == "9").Count();
            var ADDT37Item = (from x in db.ADDT37
                              join y in db.ADDT38 on x.ROLL_CALL_ID equals y.ROLL_CALL_ID into temp2
                              from ds in temp2.DefaultIfEmpty()
                              where x.DEL_YN == "N" && x.CRE_DATE >= dts && x.CRE_DATE <= dte
                          && (ds.SUBJECT == "小獎勵(班級加點，受點數控管)" || ds.SUBJECT == "班級幫手和榮譽" || ds.SUBJECT == "班級服務")
                              && x.CRE_PERSON == T01.USER_KEY &&
                              x.CRE_PERSON == ds.CRE_PERSON && x.SCHOOL_NO == ds.SCHOOL_NO
                              select new
                              {

                                  CASH = ds.CASH
                              }
                             ).ToList();
        var ADDT37temp=    (from x in db.ADDT37
             join y in db.ADDT38 on x.ROLL_CALL_ID equals y.ROLL_CALL_ID into temp2
             from ds in temp2.DefaultIfEmpty()
             where x.DEL_YN == "N" && x.CRE_DATE >= dts && x.CRE_DATE <= dte
           && (ds.SUBJECT == "小獎勵(班級加點，受點數控管)" || ds.SUBJECT == "班級幫手和榮譽" || ds.SUBJECT == "班級服務")
             && x.CRE_PERSON == T01.USER_KEY &&
             x.CRE_PERSON == ds.CRE_PERSON && x.SCHOOL_NO == ds.SCHOOL_NO && ds.BATCH_CASH_ID !=null
             select new
             {

                 CASH = ds.CASH
             }
                             ).ToList();

            if (ADDT37Item != null && ADDT37Item.Count() > 0) {

                ADDT38Count = ADDT37Item.Sum(J => J.CASH).Value;
            }
            if (ADDT37temp != null && ADDT37temp.Count() > 0) {


                REDEAYtoPAy= ADDT37temp.Sum(y => y.CASH).Value;

            }
            //var LotteryPrizeCash = from x in db.ADDT26
            //            join y in db.ADDT26_D on new { GAME_NO = x.GAME_NO } equals new { GAME_NO = y.GAME_NO }
            //            join j in db.LotteryPrize on new { GAME_NO = y.GAME_NO } equals new { GAME_NO = j.GAME_NO }
            //            where j.Y_CASH == true && x.SCHOOL_NO == SCHOOL_NO && y.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize 
            //            && j.UpdateDate >= dts && x.UpdateDate <= dte
            //                       select new
            //            {

            //                CASH = int.Parse( j.PrizeName)

            //            }.ToList();


            //ADDT38Count = db.ADDT38.Where(x => x.SUBJECT == "小獎勵(班級加點，受點數控管)" && x.CRE_PERSON == T01.USER_KEY ).Sum(x => x.CASH).Value;
            //  if (HRMT25Count > 0) {
            if (MonthCash != null)
                {
                    MonthCash = NewMonthCash + MonthCash + ADDT38Count;
                    MonthCash = MonthCash-REDEAYtoPAy;
                }
                else {
                    if (NewMonthCash != null) {
                        MonthCash = NewMonthCash + ADDT38Count;
                        MonthCash = MonthCash - REDEAYtoPAy;
                    }

                }
          //  }
            string sSQL = $@"select case when count(c.CASH_IN)=0 then 0 else SUM(c.CASH_IN) end 　from
(select * from AWAT01_LOG where SCHOOL_NO= @SCHOOL_NO and (SOURCE_TYPE='ADDI09' or SOURCE_TYPE='ADDI13') and (LOG_DESC like'%批次特殊加扣點%'or LOG_DESC like'%即時加點特殊加扣點%' or LOG_DESC like'%特殊加扣點%'or LOG_DESC like'%批次校內表現班級小幫手%'or LOG_DESC like'%批次快速大量加點-特殊加扣點%'or LOG_DESC like'%批次校內表現班級幫手和榮譽%' or LOG_DESC like'%班級幫手和榮譽%' or LOG_DESC like'%校內表現-班級幫手和榮譽%' or LOG_DESC like'%校內表現-班級服務%')  and LOG_DESC not like'%特殊加扣點-小獎勵(班級加點，受點數控管)%' and LOG_PERSON =@LOG_PERSON and  LOG_TIME>= @LOG_TIME) y
inner join AWAT01_LOG  c on y.SOURCE_NO=c.SOURCE_NO and y.SCHOOL_NO=c.SCHOOL_NO and y.USER_NO=c.USER_NO AND y.LOG_TIME=c.LOG_TIME";
            var temp = db.Database.Connection.Query<int>(sSQL, new
            {
                SCHOOL_NO = SCHOOL_NO,
                LOG_PERSON = T01.USER_KEY,
                LOG_TIME = StartDate
            });
            string sSQL1= $@"select  C.USER_NO,C.SOURCE_NO,C.LOG_TABLE,C.CASH_IN  　from
(select * from AWAT01_LOG where SCHOOL_NO= @SCHOOL_NO and (SOURCE_TYPE='ADDI09' or SOURCE_TYPE='ADDI13') and (LOG_DESC like'%批次特殊加扣點%'or LOG_DESC like'%即時加點特殊加扣點%' or LOG_DESC like'%特殊加扣點%'or LOG_DESC like'%批次校內表現班級小幫手%'or LOG_DESC like'%批次快速大量加點-特殊加扣點%'or LOG_DESC like'%批次校內表現班級幫手和榮譽%' or LOG_DESC like'%班級幫手和榮譽%' or LOG_DESC like'%校內表現-班級幫手和榮譽%' or LOG_DESC like'%校內表現-班級服務%')  and LOG_DESC not like'%特殊加扣點-小獎勵(班級加點，受點數控管)%' and LOG_PERSON =@LOG_PERSON and  LOG_TIME>= @LOG_TIME) y
inner join AWAT01_LOG  c on y.SOURCE_NO=c.SOURCE_NO and y.SCHOOL_NO=c.SCHOOL_NO and y.USER_NO=c.USER_NO AND y.LOG_TIME=c.LOG_TIME";
            var temp1 = db.Database.Connection.Query<GetListMony>(sSQL1, new
            {
                SCHOOL_NO = SCHOOL_NO,
                LOG_PERSON = T01.USER_KEY,
                LOG_TIME = StartDate
            });
            int sumBalance = 0;
            foreach (var GetListMonyItem in temp1) {
                if (GetListMonyItem.LOG_TABLE == "ADDT14")
                {
                    int? ADD14sum = 0;
                    int ADDT14Str;
                    string ADDT14Str1 = "";
                    int temp1Count = 0;
                    ADD14sum = db.ADDT14.Where(x => x.BATCH_CASH_ID == GetListMonyItem.SOURCE_NO && x.USER_NO == GetListMonyItem.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == GetListMonyItem.CASH_IN).Sum(x => x.CASH);
                    ADDT14Str = db.ADDT14.Where(x => x.BATCH_CASH_ID == GetListMonyItem.SOURCE_NO && x.USER_NO == GetListMonyItem.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == GetListMonyItem.CASH_IN).Select(x => x.IAWARD_ID).FirstOrDefault();
                    ADDT14Str1 = ADDT14Str.ToString();
                    temp1Count = temp1.Where(x => x.SOURCE_NO == ADDT14Str1).Count();
                    if (ADD14sum != null && temp1Count == 0)
                    {
                        sumBalance += (int)ADD14sum;

                    }

                }
                else if (GetListMonyItem.LOG_TABLE == "ADDT15")
                {
                    int? ADDT15sum = 0;

                    int ADDT15Str;
                    string ADDT15Str1 = "";
                    int temp1Count = 0;
                    ADDT15sum = db.ADDT15.Where(x => x.BATCH_CASH_ID == GetListMonyItem.SOURCE_NO && x.USER_NO == GetListMonyItem.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == GetListMonyItem.CASH_IN).Sum(x => x.CASH);
                    ADDT15Str = db.ADDT15.Where(x => x.BATCH_CASH_ID == GetListMonyItem.SOURCE_NO && x.USER_NO == GetListMonyItem.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == GetListMonyItem.CASH_IN).Select(x => x.OAWARD_ID).FirstOrDefault();
                    ADDT15Str1 = ADDT15Str.ToString();
                    temp1Count = temp1.Where(x => x.SOURCE_NO == ADDT15Str1).Count();
                    if (ADDT15sum != null && temp1Count == 0)
                    {
                        sumBalance += (int)ADDT15sum;

                    }
                }


                else if (GetListMonyItem.LOG_TABLE == "ADDT20")
                {
                    int? ADDT20sum = 0;

                    int ADDT20Str;
                    string ADDT20Str1 = "";
                    int temp1Count = 0;
                    ADDT20sum = db.ADDT20.Where(x => x.BATCH_CASH_ID == GetListMonyItem.SOURCE_NO && x.USER_NO == GetListMonyItem.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == GetListMonyItem.CASH_IN).Sum(x => x.CASH);
                    ADDT20Str1 = db.ADDT20.Where(x => x.BATCH_CASH_ID == GetListMonyItem.SOURCE_NO && x.USER_NO == GetListMonyItem.USER_NO && x.SCHOOL_NO == SCHOOL_NO && x.APPLY_STATUS == "9" && x.CASH == GetListMonyItem.CASH_IN).Select(x => x.BATCH_CASH_ID).FirstOrDefault();

                    temp1Count = temp1.Where(x => x.SOURCE_NO == ADDT20Str1).Count();
                    if (ADDT20sum != null && temp1Count == 0)
                    {
                        sumBalance += (int)ADDT20sum;

                    }
                }

            }
            MonthCash = temp.FirstOrDefault();
            int ADDT38ListCount = 0;
            int HRMT25ListCount = 0;
            
            int REDEAYtoListPAy = 0;
            //受控制的點數
            var ADDT37ListItem = (from x in db.ADDT37
                              join y in db.ADDT38 on x.ROLL_CALL_ID equals y.ROLL_CALL_ID into temp2
                              from ds in temp2.DefaultIfEmpty()
                              where x.DEL_YN == "N"
                             &&( ds.SUBJECT == "小獎勵(班級加點，受點數控管)" || ds.SUBJECT == "班級幫手和榮譽" || ds.SUBJECT == "班級幫手和榮譽")
                              && x.CRE_PERSON == T01.USER_KEY &&
                              x.CRE_PERSON == ds.CRE_PERSON && x.SCHOOL_NO == ds.SCHOOL_NO && x.CRE_DATE >= dts && x.CRE_DATE <= dte
                                  select new
                              {

                                  CASH = ds.CASH
                              }
                  ).ToList();//所有受控制的點數
            var ADDT37Listtemp = (from x in db.ADDT37
                              join y in db.ADDT38 on x.ROLL_CALL_ID equals y.ROLL_CALL_ID into temp2
                              from ds in temp2.DefaultIfEmpty()
                              where x.DEL_YN == "N"
                           && (ds.SUBJECT == "小獎勵(班級加點，受點數控管)" || ds.SUBJECT == "班級幫手和榮譽" || ds.SUBJECT == "班級幫手和榮譽")
                              && x.CRE_PERSON == T01.USER_KEY &&
                              x.CRE_PERSON == ds.CRE_PERSON && x.SCHOOL_NO == ds.SCHOOL_NO && ds.BATCH_CASH_ID != null && x.CRE_DATE >= dts && x.CRE_DATE <= dte
                                  select new
                              {

                                  CASH = ds.CASH
                              }
                           ).ToList();//所有未受控制的點數

            if (ADDT37ListItem != null && ADDT37ListItem.Count() > 0)
            {

                ADDT38ListCount = ADDT37ListItem.Sum(J => J.CASH).Value;
            }
            if (ADDT37Listtemp != null && ADDT37Listtemp.Count() > 0)
            {


                REDEAYtoListPAy = ADDT37Listtemp.Sum(y => y.CASH).Value;

            }
            if (ADDT37ListItem != null && ADDT37ListItem.Count() > 0)
            {

                ADDT38ListCount = ADDT37ListItem.Sum(J => J.CASH).Value;
            }
            if (MonthCash.HasValue || ADDT38ListCount > 0)
            {
                if (MonthCash.Value >= short.MaxValue)
                {
                    ThisMonthCash = short.MaxValue;
                }
                else if (MonthCash.Value <= short.MinValue)
                {
                    ThisMonthCash = short.MinValue;
                }
                else
                {
                    ThisMonthCash = Convert.ToInt16(MonthCash+ ADDT26MonthCash + ADDT38ListCount - sumBalance  - REDEAYtoListPAy) ;
                    if (ThisMonthCash < 0) {
                        ThisMonthCash = 0;
                    }
                }
            }
       
            return CashLimit;
        }

        /// <summary>
        ///  檢查該項給點是否納入特殊給點限制
        /// </summary>
        /// <param name="LogDesc"></param>
        /// <returns></returns>
        public static bool CheckCashLimit_LOG_DESC(string LogDesc)
        {
            ECOOL_DEVEntities db1 = new ECOOL_DEVEntities();
            bool ans = false;
            if (string.IsNullOrEmpty(LogDesc)) return false;
            string[] myInClause = new string[] { "批次特殊加扣點", "即時加點特殊加扣點", "特殊加扣點", "批次校內表現班級小幫手", "班級小幫手", "班級幫手和榮譽", "班級服務", "批次校內表現班級幫手和榮譽", "批次快速大量加點-特殊加扣點","校內表現-班級幫手和榮譽" };
           

            if (myInClause.Where(a => LogDesc.Contains(a)).Any())
                ans = true;

            return ans;
        }

        /// <summary>
        /// 當是導師取得自已的班級
        /// </summary>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static string GetStudentCLASS_NO(string SCHOOL_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
            string ReturnVal = null;
            Regex rx = new Regex(@"_");
            if (SCHOOL_NO != null && rx.Matches(SCHOOL_NO).Count > 0)
            {
                string[] strSchool = new string[2];
                strSchool = SCHOOL_NO.Split('_');
                SCHOOL_NO = strSchool[0];

            }
            HRMT01 T01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

            if (T01.USER_TYPE == UserType.Student)
            {
                ReturnVal = T01.CLASS_NO;
            }

            return ReturnVal;
        }

        public static List<ADDT04> GetOenUseADDT04(string USER_NO, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            return db.ADDT04.Where(p => p.USER_NO == USER_NO && p.SCHOOL_NO == SCHOOL_NO && p.PASS_YN == "Y").ToList();
        }

        public static List<short> GetOenUseADDT04toShort(string USER_NO, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            List<ADDT04> ltA04 = GetOenUseADDT04(USER_NO, SCHOOL_NO, ref db);

            if (ltA04 != null)
            {
                return ltA04.Select(a => a.GRADE).Cast<short>().ToList();
            }
            else
            {
                return null;
            }
        }

        public static void RefreshCashInfo(UserProfile LoginUser, ref ECOOL_DEVEntities db)
        {
            HrmtImportService hrmtImportServiceItem = new HrmtImportService();
            int ArrivedCash = 0;
            AWAT04 aw4 = db.AWAT04.Find(new object[] { LoginUser.SCHOOL_NO, LoginUser.USER_NO });

            if (aw4 != null)
            {
                if (aw4.ARRIVED_CASH.HasValue) ArrivedCash = aw4.ARRIVED_CASH.Value;
            }

            //Cash
            if (LoginUser.USER_TYPE == "T")
            {
                AWAT08 TeachUserCash = db.AWAT08.Where(user => user.SCHOOL_NO == LoginUser.SCHOOL_NO && user.USER_NO == LoginUser.USER_NO).FirstOrDefault();
                if (TeachUserCash != null)
                {
                    LoginUser.CASH = TeachUserCash.CASH_AVAILABLE.Value;
                    if (TeachUserCash.CASH_WORKHARD.HasValue)
                    {
                        LoginUser.CASH_Workhard = TeachUserCash.CASH_WORKHARD.Value;
                        // 下次好運集氣會被負數影響，因此如果下次好運集氣大於目前努力點數時，扣除額有可能是大於100的狀況
                        if (ArrivedCash > LoginUser.CASH_Workhard)
                        {
                            LoginUser.CASH_NextChance = (ArrivedCash + 100) - LoginUser.CASH_Workhard;
                        }
                        else
                        {
                            LoginUser.CASH_NextChance = 100 - (LoginUser.CASH_Workhard % 100);
                        }
                    }
                }
            }
            else if (LoginUser.USER_TYPE != "P")
            {
                AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == LoginUser.SCHOOL_NO && user.USER_NO == LoginUser.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    LoginUser.CASH = UserCash.CASH_AVAILABLE.Value;
                    if (UserCash.CASH_WORKHARD.HasValue)
                    {
                        LoginUser.CASH_Workhard = UserCash.CASH_WORKHARD.Value;
                        // 下次好運集氣會被負數影響，因此如果下次好運集氣大於目前努力點數時，扣除額有可能是大於100的狀況
                        if (ArrivedCash > LoginUser.CASH_Workhard)
                        {
                            LoginUser.CASH_NextChance = (ArrivedCash + 100) - LoginUser.CASH_Workhard;
                        }
                        else
                        {
                            LoginUser.CASH_NextChance = 100 - (LoginUser.CASH_Workhard % 100);
                        }
                    }
                }
            }

            //生日與進步加值
            if (aw4 == null)
            {
                aw4 = db.AWAT04.Create();
                aw4.SCHOOL_NO = LoginUser.SCHOOL_NO;
                aw4.USER_NO = LoginUser.USER_NO;
                aw4.CHG_DATE = DateTime.Now;
                db.AWAT04.Add(aw4);
                db.SaveChanges();
            }
            if (aw4 != null && LoginUser.USER_TYPE == "S")
            {
                if (LoginUser.BIRTHDAY.HasValue)
                {
                    if (DateTime.Today.Month == LoginUser.BIRTHDAY.Value.Month)
                    {
                        if (aw4.BIRTHDAY_YEAR.HasValue)
                        {
                            LoginUser.Chance_BIRTHDAY = (DateTime.Today.Year > aw4.BIRTHDAY_YEAR.Value);
                        }
                        else
                        {
                            LoginUser.Chance_BIRTHDAY = true;
                        }
                    }
                }
                if (LoginUser.CASH_Workhard >= 100)
                {
                    LoginUser.Chance_ARRIVED_CASH = (LoginUser.CASH_Workhard - ArrivedCash) / 100;
                }

                //LoginUser.Chance_BIRTHDAY_YEAR = aw4.BIRTHDAY_YEAR;
                //LoginUser.Chance_ARRIVED_CASH = aw4.ARRIVED_CASH;
            }

            //閱讀護照獎勵
            var CashPassList =
                db.ADDT04.Where(a => a.SCHOOL_NO == LoginUser.SCHOOL_NO && a.USER_NO == LoginUser.USER_NO && a.CASH_YN.ToUpper() != "Y")
                    .OrderBy(a => a.GRADE);
            ADDT04 CashPass = CashPassList.FirstOrDefault();
            if (CashPass != null)
            {
                LoginUser.NotePassGrade = CashPass.GRADE;
            }
            else
            {
                LoginUser.NotePassGrade = 0;
            }

            //閱讀認證升級獎勵
            var CashReadLevelList =
                db.ADDT09_HIS.Where(a => a.SCHOOL_NO == LoginUser.SCHOOL_NO && a.USER_NO == LoginUser.USER_NO && a.CASH_YN.ToUpper() != "Y").OrderBy(a => a.LEVEL_ID);
            ADDT09_HIS CashReadLevel = CashReadLevelList.FirstOrDefault();
            if (CashReadLevel != null)
            {
                LoginUser.NoteReadLevel = CashReadLevel.LEVEL_ID;
            }
            else
            {
                LoginUser.NoteReadLevel = 0;
            }
            bool IScheckbell = false;
            IScheckbell = hrmtImportServiceItem.CheckStudentTasklist(LoginUser);
            LoginUser.checkstudenttasklist = IScheckbell;
            LoginUser.PlayerUrl = GetPlayerUrl(ref db, LoginUser.SCHOOL_NO, LoginUser.USER_NO, LoginUser.SEX, LoginUser.USER_TYPE);
        }

        /// <summary>
        /// 角色娃娃路徑
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static string GetPlayerUrl(ref ECOOL_DEVEntities db, string SCHOOL_NO, string USER_NO, string SEX, string USER_TYPE)
        {
            if (USER_TYPE == UserType.Parents)
            {
                return "~/Content/img/web-parent.png";
            }
            //角色娃娃
            List<AWAT07> MyPlayerList =
                db.AWAT07.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).ToList();
            if (MyPlayerList.Any())
            {
                AWAT07 MyPlayer = MyPlayerList.Where(a => a.DEFAULT_YN == true).FirstOrDefault();
                if (MyPlayer == null) MyPlayer = MyPlayerList.First();

                string ImageUrl = @"~/Content/Players/";
                return ImageUrl + db.AWAT06.Find(MyPlayer.PLAYER_NO).IMG_FILE;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(SEX))
                {
                    HRMT01 tHRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

                    if (tHRMT01 != null)
                    {
                        SEX = tHRMT01.SEX;
                    }
                }

                if (SEX == "1") //男預設值
                {
                    return "~/Content/img/web-student-allpage-33plus.png";
                }
                else            //女預設值
                {
                    return "~/Content/img/web-student_allpage-17.png";
                }
            }
        }

        /// <value>
        /// Property <c>USER_NO</c> 目前使用者ID。
        /// </value>
        public string USER_NO;

        /// <value>
        /// Property <c>SCHOOL_NO</c> 目前使用者學校代號。
        /// </value>
        public string SCHOOL_NO;

        /// <value>
        /// Property <c>SCHOOL_NAME</c> 目前使用者學校名稱。
        /// </value>
        public string SCHOOL_NAME;

        /// <value>
        /// Property <c>USER_TYPE_DESC</c>
        /// </value>
        public string USER_TYPE_DESC;

        /// <value>
        /// Property <c>NAME</c> 目前使用者名稱。
        /// </value>
        public string NAME;

        /// <value>
        /// Property <c>NAME</c> 目前使用者名稱。
        /// </value>
        public string SNAME;

        /// <value>
        /// Property <c>SEX</c> 目前使用者性名。
        /// </value>
        public string SEX;

        /// <value>
        /// Property <c>GRADE</c> 目前使用者年級。
        /// </value>
        public short GRADE;

        public string GRADE_DESC;

        /// <value>
        /// Property <c>CLASS_NO</c> 目前使用者角色。
        /// </value>
        public string CLASS_NO;

        /// <value>
        /// Property <c>TEACH_CLASS_NO</c>
        /// </value>
        public string TEACH_CLASS_NO;

        /// <value>
        /// Property <c>SEAT_NO</c> 目前使用者座號。
        /// </value>
        public string SEAT_NO;

        public DateTime? BIRTHDAY;

        /// <value>
        /// Property <c>CASH</c> 目前使用者酷幣點數。
        /// </value>
        public int CASH;

        public int CASH_Workhard;

        /// <value>
        /// Property <c>CASH_NextChance</c> 下次進步加值可抽獎的點數。
        /// </value>
        public int CASH_NextChance;

        public bool Chance_BIRTHDAY = false;

        public int Chance_ARRIVED_CASH = 0;

        public short LEVEL_ID = 0;

        public List<short> LisTBookPassport;
        public bool checkstudenttasklist;

        /// <value>
        /// Property <c>USER_TYPE</c> 帳號類型。
        /// </value>
        public string USER_TYPE;

        /// <value>
        /// Property <c>LANGE</c> 語系。
        /// </value>
        public string LANGE;

        /// <value>
        /// Property <c>USER_KEY</c> 使用者唯一值。
        /// </value>
        public string USER_KEY;

        public string RoleID_Default;

        public int? ROLE_TYPE;

        public decimal? ROLE_LEVEL;

        //閱讀護照完成通知
        public short NotePassGrade;

        //閱讀認證升級通知
        public short NoteReadLevel;

        public string PlayerUrl;

        // 跑步里程數
        public double RUN_TOTAL_METER { get; set; }

        // 目前跑步站別
        public string LOCATION_NAME { get; set; }

        public byte? INIT_STATUS;

        /// <summary>
        /// 紀錄與酷課雲整合的回傳參數
        /// </summary>
        public string SSO_Code;

        public string SSO_series;

        public object Clone()
        {
            return this.MemberwiseClone();
        }
        public class GetListMony
        {

           public string USER_NO { get; set; }
            public string SOURCE_NO { get; set; }
            public string LOG_TABLE { get; set; }
            public int CASH_IN { get; set; }
        }

      protected void FromHRMT01(HRMT01 FindUser)
        {
            //this.CASH
            if (FindUser.USER_TYPE == UserType.Student)
            {
                this.CLASS_NO = FindUser.CLASS_NO;
                this.GRADE = Convert.ToInt16(FindUser.GRADE);
                this.GRADE_DESC = HRMT01.ParserGrade(FindUser.GRADE);
                this.SEAT_NO = FindUser.SEAT_NO;
            }
            this.NAME = FindUser.NAME;
            this.SNAME = FindUser.SNAME;
            this.SCHOOL_NO = FindUser.SCHOOL_NO;

            //this.LANGE

            this.SEX = FindUser.SEX;
            this.BIRTHDAY = FindUser.BIRTHDAY;

            this.USER_KEY = FindUser.USER_KEY;
            this.USER_TYPE = FindUser.USER_TYPE;
            this.USER_TYPE_DESC = UserType.GetDesc(FindUser.USER_TYPE);
            this.USER_NO = FindUser.USER_NO;
            this.INIT_STATUS = FindUser.INIT_STATUS;

            //this.RoleID_Default
            if (FindUser.HRMT25.Count > 0)
            {
                List<HRMT25> HRMT25iNFOS = new List<HRMT25>();
                HRMT25iNFOS = FindUser.HRMT25.OrderBy(x => Int32.Parse(x.ROLE_ID)).ToList();
                this.RoleID_Default = HRMT25iNFOS.FirstOrDefault().ROLE_ID;
            }
            else
            {
                ECOOL_DEVEntities db = new ECOOL_DEVEntities();

                var UN = db.HRMT25_UN.Where(a => a.USER_TYPE == FindUser.USER_TYPE).FirstOrDefault();

                if (UN != null)
                {
                    this.RoleID_Default = UN.ROLE_ID;
                }
            }

            LisTBookPassport = new List<short>();
        }
    }
}