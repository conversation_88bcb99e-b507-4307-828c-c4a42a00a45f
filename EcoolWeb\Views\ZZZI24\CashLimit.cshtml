﻿@model ECOOL_APP.EF.HRMT01
@using ECOOL_APP.com.ecool.util

@{
    ViewBag.Title = "設定每個月給點上限";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<link href='~/Content/css/EzCss.css' rel='stylesheet' />

@using (Html.BeginForm("SetCashLimit", "ZZZI24", FormMethod.Post, new { name = "Form1", enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.USER_NO)

    <table style="width:100%">
        <tr>
            <td align="center" valign="middle">
                <h4>
                    您要設定的帳號：@StringHelper.LeftStringR(@Model.USER_NO, 5, "*****")，姓名：@Model.NAME
                </h4>
                <br />
                <div class="form-group">
                    <div class="ccol-md-9 col-sm-9 col-lg-10">
                        @Html.EditorFor(model => model.GIVE_CASH_LIMIT, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.GIVE_CASH_LIMIT, "", new { @class = "text-danger" })
                    </div>
                    <p></p>
                    <button value="Save" class="btn btn-default">
                        確定送出
                    </button>
                </div>

            </td>
        </tr>
    </table>
}



