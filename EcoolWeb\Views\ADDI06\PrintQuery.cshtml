﻿@model EcoolWeb.ViewModels.ADDI06IndexViewModel
@{
    ViewBag.Title = "校內表現-校內表現一覽表 PRINT";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    int i = 1;

}
<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<div class="Div-EZ-Right cScreen">
    <button id="ButtonExcel" class="btn btn-sm btn-sys">匯出excel</button>
</div>

<div class="print table-92Per" style="margin: 0px auto; " id="tbData">
    <table class='table-ecool table-ecool-pink-SEC' border='1'>
        <thead>
            <tr></tr>
            <tr class='text-center'>
                <th>
                    日期
                </th>
                <th>
                    公告人
                </th>
                <th>
                    班級
                </th>

                <th>
                    姓名
                </th>
                <th>
                    獎懲類別
                </th>
                <th>
                    具體事蹟
                </th>
                <th>
                    獎勵點數
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ADDT14List)
            {
                if (i % 50 == 0)
                {
                    @Html.Raw(@"</tbody></table>");
                    @Html.Raw(@"<div style='page-break-before:always;'></div><br/>");
                    @Html.Raw(@"<table class='table-ecool table-ecool-pink-SEC' border='1'>
                        <thead>
                            <tr></tr>
                            <tr class='text-center'>
                                <th>
                                    日期
                                </th>
                                <th>
                                    公告人
                                </th>
                                <th>
                                    班級
                                </th>

                                <th>
                                    姓名
                                </th>
                                <th>
                                    獎懲類別
                                </th>
                                <th>
                                    具體事蹟
                                </th>
                                <th>
                                    獎勵點數
                                </th>
                            </tr>
                        </thead>
                   <tbody>");
                }

                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.CREATEDATE, "ShortDateTime")
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.TNAME)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.SNAME)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.IAWARD_KIND)
                    </td>
                    <td style="text-align: left;white-space:normal">
                        @Html.DisplayFor(modelItem => item.IAWARD_ITEM)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.CASH)
                    </td>
                </tr>

                i = i + 1;
            }
        </tbody>
    </table>
</div>



<script type="text/javascript">
    $(function() {
        // 自動列印
        window.print();

        // Excel 匯出功能
        $('#ButtonExcel').on('click', function() {
            try {
                var table = document.getElementById('tbData');
                var html = table.outerHTML;
                var blob = new Blob([html], {
                    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                });
                saveAs(blob, "校內表現一覽表.xls");
            } catch (error) {
                console.error('Excel export error:', error);
                alert('匯出 Excel 時發生錯誤，請稍後再試。');
            }
            return false;
        });
    });
</script>

