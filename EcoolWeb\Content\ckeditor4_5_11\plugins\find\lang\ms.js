﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'ms', {
	find: 'Cari',
	findOptions: 'Find Options',
	findWhat: 'Perkataan yang dicari:',
	matchCase: 'Padanan case huruf',
	matchCyclic: 'Match cyclic',
	matchWord: 'Padana <PERSON> perkata<PERSON>',
	notFoundMsg: 'Text yang dicari tidak dijumpai.',
	replace: 'Ganti',
	replaceAll: 'Ganti semua',
	replaceSuccessMsg: '%1 occurrence(s) replaced.',
	replaceWith: '<PERSON><PERSON><PERSON> dengan:',
	title: 'Find and Replace'
} );
