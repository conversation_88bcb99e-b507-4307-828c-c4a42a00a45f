/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Monospace/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Monospace={directory:"Monospace/Regular",family:"AsanaMathJax_Monospace",testString:"\u00A0\uD835\uDE70\uD835\uDE71\uD835\uDE72\uD835\uDE73\uD835\uDE74\uD835\uDE75\uD835\uDE76\uD835\uDE77\uD835\uDE78\uD835\uDE79\uD835\uDE7A\uD835\uDE7B\uD835\uDE7C\uD835\uDE7D",32:[0,0,524,0,0],160:[0,0,524,0,0],120432:[623,0,524,27,497],120433:[611,0,524,23,482],120434:[622,11,524,40,484],120435:[611,0,524,19,485],120436:[611,0,524,26,502],120437:[611,0,524,28,490],120438:[622,11,524,38,496],120439:[611,0,524,22,502],120440:[611,0,524,79,446],120441:[611,11,524,71,478],120442:[611,0,524,26,495],120443:[611,0,524,32,488],120444:[611,0,524,17,507],120445:[611,0,524,28,496],120446:[622,11,524,56,468],120447:[611,0,524,26,480],120448:[622,139,524,56,468],120449:[611,11,524,22,522],120450:[622,11,524,52,472],120451:[611,0,524,26,498],120452:[611,11,524,4,520],120453:[611,8,524,18,506],120454:[611,8,524,11,513],120455:[611,0,524,27,496],120456:[611,0,524,19,505],120457:[611,0,524,48,481],120458:[440,6,524,55,524],120459:[611,6,524,12,488],120460:[440,6,524,73,466],120461:[611,6,524,36,512],120462:[440,6,524,55,464],120463:[617,0,524,42,437],120464:[442,229,524,29,509],120465:[611,0,524,12,512],120466:[612,0,524,78,455],120467:[612,228,524,48,368],120468:[611,0,524,21,508],120469:[611,0,524,58,467],120470:[437,0,524,-4,516],120471:[437,0,524,12,512],120472:[440,6,524,57,467],120473:[437,222,524,12,488],120474:[437,222,524,40,537],120475:[437,0,524,32,487],120476:[440,6,524,72,459],120477:[554,6,524,25,449],120478:[431,6,524,12,512],120479:[431,4,524,24,500],120480:[431,4,524,16,508],120481:[431,0,524,27,496],120482:[431,228,524,26,500],120483:[431,0,524,33,475],120822:[691,12,499,48,451],120823:[691,0,499,100,421],120824:[691,0,499,50,450],120825:[691,12,499,42,457],120826:[692,0,499,28,471],120827:[679,12,499,50,450],120828:[691,12,499,50,449],120829:[697,12,499,42,457],120830:[691,12,499,42,457],120831:[691,12,499,50,449]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Monospace"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Monospace/Regular/Main.js"]);
