﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'si', {
	button: {
		title: 'බොත්තම් ගුණ',
		text: 'වගන්තිය(වටිනාකම)',
		type: 'වර්ගය',
		typeBtn: 'බොත්තම',
		typeSbm: 'යොමුකරනවා',
		typeRst: 'නැවත ආරම්භකතත්වයට පත් කරනවා'
	},
	checkboxAndRadio: {
		checkboxTitle: 'ලකුණු කිරීමේ කොටුවේ ලක්ෂණ',
		radioTitle: 'Radio Button Properties', // MISSING
		value: 'Value', // MISSING
		selected: 'Selected', // MISSING
		required: 'Required' // MISSING
	},
	form: {
		title: 'පෝරමයේ ',
		menu: 'පෝරමයේ ගුණ/',
		action: 'ගන්නා පියවර',
		method: 'ක්‍රමය',
		encoding: 'කේතීකරණය'
	},
	hidden: {
		title: 'සැඟවුණු ප්‍රදේශයේ ',
		name: 'නම',
		value: 'Value' // MISSING
	},
	select: {
		title: 'තේරීම් ප්‍රදේශයේ ',
		selectInfo: 'විස්තර තෝරන්න',
		opAvail: 'ඉතුරුවී ඇති වීකල්ප',
		value: 'Value', // MISSING
		size: 'විශාලත්වය',
		lines: 'lines', // MISSING
		chkMulti: 'Allow multiple selections', // MISSING
		required: 'Required', // MISSING
		opText: 'Text', // MISSING
		opValue: 'Value', // MISSING
		btnAdd: 'Add', // MISSING
		btnModify: 'Modify', // MISSING
		btnUp: 'Up', // MISSING
		btnDown: 'Down', // MISSING
		btnSetValue: 'Set as selected value', // MISSING
		btnDelete: 'මකා දැම්ම'
	},
	textarea: {
		title: 'Textarea Properties', // MISSING
		cols: 'සිරස් ',
		rows: 'Rows' // MISSING
	},
	textfield: {
		title: 'Text Field Properties', // MISSING
		name: 'නම',
		value: 'Value', // MISSING
		charWidth: 'Character Width', // MISSING
		maxChars: 'Maximum Characters', // MISSING
		required: 'Required', // MISSING
		type: 'වර්ගය',
		typeText: 'Text', // MISSING
		typePass: 'Password', // MISSING
		typeEmail: 'Email', // MISSING
		typeSearch: 'Search', // MISSING
		typeTel: 'Telephone Number', // MISSING
		typeUrl: 'URL'
	}
} );
