﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'ko', {
	alt: '대체 문자열',
	border: '테두리',
	btnUpload: '서버로 전송',
	button2Img: '단순 이미지에서 선택한 이미지 버튼을 변환하시겠습니까?',
	hSpace: '가로 여백',
	img2Button: '이미지 버튼에 선택한 이미지를 변환하시겠습니까?',
	infoTab: '이미지 정보',
	linkTab: '링크',
	lockRatio: '비율 유지',
	menu: '이미지 속성',
	resetSize: '원래 크기로',
	title: '이미지 속성',
	titleButton: '이미지 버튼 속성',
	upload: '업로드',
	urlMissing: '이미지 원본 주소(URL)가 없습니다.',
	vSpace: '세로 여백',
	validateBorder: '테두리 두께는 정수여야 합니다.',
	validateHSpace: '가로 길이는 정수여야 합니다.',
	validateVSpace: '세로 길이는 정수여야 합니다.'
} );
