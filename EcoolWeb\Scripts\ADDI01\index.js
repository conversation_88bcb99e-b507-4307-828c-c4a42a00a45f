// Modern jQuery code for ADDI01 Index page
$(document).ready(function() {
    // 全局變數
    const targetFormID = '#ADDI01';

    // 頁面初始化模組
    const pageInitializer = {
        init: function() {
            this.handleAppMode();
        },

        handleAppMode: function() {
            if ($('#AppMode').val() == "True") {
                $('#search').hide();
            } else {
                $('#glyphicon-search').hide();
            }
        }
    };

    // 搜尋條件顯示/隱藏模組
    const searchToggle = {
        init: function() {
            window.showSearch = this.toggleSearch.bind(this);
        },

        toggleSearch: function() {
            const $search = $('#search');
            const $searchImg = $('#search-img');

            if ($search.is(':hidden')) {
                $search.show(500);
                $searchImg.attr("class", "glyphicon glyphicon-minus fa-1x");
                $searchImg.text("隱藏搜尋條件");
            } else {
                $search.hide(500);
                $searchImg.attr("class", "glyphicon glyphicon-search fa-1x");
                $searchImg.text("顯示搜尋條件");
            }
        }
    };

    // 展開/收合功能模組
    const contentToggle = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $('.show-more a').on('click', this.handleToggle.bind(this));
        },

        handleToggle: function(e) {
            e.preventDefault();
            const $this = $(e.target);
            const $div = $('div.hideContent');

            // 取得或計算內容高度
            let contentHeight = $div.data('contentHeight');
            if (!contentHeight) {
                contentHeight = this.determineActualHeight($div);
                $div.data('contentHeight', contentHeight);
            }

            // 執行動畫
            $div.stop().animate({
                height: (contentHeight == $div.height() ? 37 : contentHeight)
            }, 500);

            // 更新按鈕文字
            if (contentHeight == $div.height()) {
                $this.text("更多");
            } else {
                $this.text("關閉");
            }
        },

        determineActualHeight: function($div) {
            const $clone = $div.clone().hide().css('height', 'auto').appendTo($div.parent());
            const height = $clone.height();
            $clone.remove();
            return height;
        }
    };

    // 分頁處理模組
    const paginationHandler = {
        init: function() {
            window.FunPageProc = this.processPage.bind(this);
        },

        processPage: function(page) {
            if ($(targetFormID).length > 0) {
                $('#Page').val(page);
                $(targetFormID).submit();
            }
        }
    };

    // 使用事件委派處理按鈕點擊
    $(document).on('click', '.btn-pink', function(e) {
        e.preventDefault();
        const $btn = $(this);
        const action = $btn.data('action');
        const value = $btn.data('value');

        if (action) {
            if (action.includes('Bool')) {
                doSearchBool(action, value);
            } else {
                doSearch(action, value);
            }
        }
    });

    // 排序功能模組
    const sortHandler = {
        init: function() {
            window.doSort = this.handleSort.bind(this);
        },

        handleSort: function(sortColumn) {
            $("#OrdercColumn").val(sortColumn);
            paginationHandler.processPage(1);
        }
    };

    // 搜尋功能模組
    const searchHandler = {
        init: function() {
            window.doSearch = this.handleSearch.bind(this);
            window.doSearchBool = this.handleBooleanSearch.bind(this);
            window.todoClear = this.clearSearch.bind(this);
            window.Clearleft = this.clearLeftFilters.bind(this);
        },

        handleSearch: function(columnName, whereValue) {
            $("#" + columnName).val(whereValue);
            paginationHandler.processPage(1);
        },

        handleBooleanSearch: function(columnName, whereValue) {
            $("#" + columnName).val(!whereValue);
            paginationHandler.processPage(1);
        },

        clearSearch: function() {
            $("#doClear").val(true);
            $("#OrdercColumn").val('');
            $("#whereKeyword").val('');
            $("#whereUserNo").val('');
            $("#whereWritingStatus").val('');
            $("#whereShareYN").val('');
            $("#whereComment").val('');
            $("#whereCommentCash").val('');
            $("#whereCLASS_NO").val('');
            $("#whereGrade").val('');
            $("#Page").val(1);

            $(targetFormID).submit();
        },

        clearLeftFilters: function() {
            $("#whereShareYN").val('');
            $("#whereComment").val('');
            $("#whereCommentCash").val('');
        }
    };

    // 狀態篩選模組
    const statusFilter = {
        init: function() {
            window.DoWRITING_STATUS = this.handleStatusFilter.bind(this);
            window.DoSEARCH_With_STATUS = this.handleSearchWithStatus.bind(this);
        },

        handleStatusFilter: function(value) {
            $('#whereWritingStatus').val(value);
            paginationHandler.processPage(1);
        },

        handleSearchWithStatus: function(value, userNo) {
            $('#whereWritingStatus').val(value);
            searchHandler.handleSearch('whereUserNo', userNo);
        }
    };

    // 使用 Promise 處理 AJAX 請求
    function doSearch(field, value) {
        return $.ajax({
            url: window.location.pathname,
            method: 'GET',
            data: { [field]: value }
        })
        .done(function(response) {
            updateContent(response);
        })
        .fail(function(xhr, textStatus, errorThrown) {
            console.error('Search failed:', textStatus, errorThrown);
            showError('搜尋失敗，請稍後再試');
        });
    }

    function doSearchBool(field, value) {
        return $.ajax({
            url: window.location.pathname,
            method: 'GET',
            data: {
                [field]: value,
                _: new Date().getTime() // 防止緩存
            }
        })
        .done(function(response) {
            updateContent(response);
        })
        .fail(function(xhr, textStatus, errorThrown) {
            console.error('Boolean search failed:', textStatus, errorThrown);
            showError('搜尋失敗，請稍後再試');
        });
    }

    // 使用現代化的 DOM 操作
    function updateContent(html) {
        const $container = $('#content-container');
        $container.fadeOut(200, function() {
            $container.html(html).fadeIn(200);
        });
    }

    // 錯誤處理
    function showError(message) {
        const $errorDiv = $('<div>')
            .addClass('alert alert-danger')
            .text(message)
            .hide();
        
        $('body').prepend($errorDiv);
        $errorDiv.fadeIn(200).delay(3000).fadeOut(200, function() {
            $(this).remove();
        });
    }

    // 使用 localStorage 替代 jstorage
    const storage = {
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('Error saving to localStorage:', e);
            }
        },
        
        get: function(key) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : null;
            } catch (e) {
                console.error('Error reading from localStorage:', e);
                return null;
            }
        },
        
        remove: function(key) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('Error removing from localStorage:', e);
            }
        }
    };

    // 初始化所有模組
    pageInitializer.init();
    searchToggle.init();
    contentToggle.init();
    paginationHandler.init();
    sortHandler.init();
    searchHandler.init();
    statusFilter.init();

    // 導出需要的函數到全局作用域
    window.doSearch = doSearch;
    window.doSearchBool = doSearchBool;
    window.storage = storage;
});