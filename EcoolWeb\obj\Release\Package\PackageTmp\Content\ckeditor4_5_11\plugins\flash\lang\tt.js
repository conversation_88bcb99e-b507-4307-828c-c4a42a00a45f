﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'tt', {
	access: 'Script Access', // MISSING
	accessAlways: 'Һəрвакыт',
	accessNever: 'Беркайчан да',
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Иң аска',
	alignAbsMiddle: 'Төгәл уртада',
	alignBaseline: 'Таяныч сызыгы',
	alignTextTop: 'Текст өсте',
	bgcolor: 'Фон төсе',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'Әйләнеш',
	chkMenu: 'Enable Flash Menu', // MISSING
	chkPlay: 'Auto Play', // MISSING
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'Горизонталь ара',
	properties: 'Флеш үзлекләре',
	propertiesTab: 'Үзлекләр',
	quality: 'Сыйфат',
	qualityAutoHigh: 'Авто югары сыйфат',
	qualityAutoLow: 'Авто түбән сыйфат',
	qualityBest: 'Иң югары сыйфат',
	qualityHigh: 'Югары',
	qualityLow: 'Түбəн',
	qualityMedium: 'Уртача',
	scale: 'Зурлык',
	scaleAll: 'Барысын күрсәтү',
	scaleFit: 'Exact Fit', // MISSING
	scaleNoBorder: 'Чиксез',
	title: 'Флеш үзлекләре',
	vSpace: 'Вертикаль ара',
	validateHSpace: 'Горизонталь ара  сан булырга тиеш.',
	validateSrc: 'URL буш булмаска тиеш.',
	validateVSpace: 'Вертикаль ара  сан булырга тиеш.',
	windowMode: 'Тəрəзə тәртибе',
	windowModeOpaque: 'Үтә күренмәле',
	windowModeTransparent: 'Үтə күренмəле',
	windowModeWindow: 'Тəрəзə'
} );
