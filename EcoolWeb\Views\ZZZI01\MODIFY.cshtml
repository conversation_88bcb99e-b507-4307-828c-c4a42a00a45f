﻿@model ECOOL_APP.EF.BDMT01

@{
    ViewBag.Title = "學校基本資料修改";
}

@section Scripts {
    <script src="~/Content/ckeditor/ckeditor.js"></script>
    <script language="JavaScript">
        $(function () {
            $("#Save").click(function () { return checkFile(); });
        });

        var isCheckImageType = false;  //是否檢查圖片副檔名
        function checkFile() {
            var f = document.ZZZI01Form;
            //var re = /\.(jpg|gif)$/i;  //允許的圖片副檔名
            var re = /\.(png)$/i;  //允許的圖片副檔名
            if ($("#Imgfile").val()!='' && !re.test(f.Imgfile.value)) {
                alert("只允許上傳PNG圖片檔");
                isCheckImageType = false;
            } else {
                isCheckImageType =  true;
            }
            return isCheckImageType;
        }

        $("#@Html.IdFor(m => m.ZZZI09_USE_S_MMDD),#@Html.IdFor(m => m.ZZZI09_USE_E_MMDD)").datepicker({
            changeMonth: true,
            changeYear: false,
            dateFormat: "mm/dd",
        }).focus(function () {
            $(".ui-datepicker-year").hide();
        });

        $("#MaintanceSDate").datepicker({
            dateFormat: "yy/mm/dd",
            changeMonth: true,
            changeYear: true,
            showOn: "button",
            buttonImage: "../Content/img/icon/calendar.gif",
            buttonImageOnly: true,

        });

        $("#MaintanceEDate").datepicker({
            dateFormat: "yy/mm/dd",
            changeMonth: true,
            changeYear: true,
            showOn: "button",
            buttonImage: "../Content/img/icon/calendar.gif",
            buttonImageOnly: true,

        });
    </script>
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@using (Html.BeginForm("MODIFY", "ZZZI01", FormMethod.Post, new { name = "ZZZI01Form", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    string EXPLAIN = "";
    string ADDI01Context = "";
    string BOOKEXPLAIN = "";
    string AwatQ02SEXPLAIN = "";
    string AwatQ02TEXPLAIN = "";
    string ADDI01SEXPLAIN = "";
    string ADDT06SEXPLAIN = "";
    string ADDT06TEXPLAIN = "";
    EXPLAIN = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.EXPLAIN).FirstOrDefault();
    ADDI01Context = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.ADDI01Context).FirstOrDefault();

    BOOKEXPLAIN = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.BOOKEXPLAIN).FirstOrDefault();
    AwatQ02SEXPLAIN = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.AwatQ02SEXPLAIN).FirstOrDefault();
    AwatQ02TEXPLAIN = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.AwatQ02TEXPLAIN).FirstOrDefault();

    ADDI01SEXPLAIN = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.ADDI01SEXPLAIN).FirstOrDefault();
    ADDT06SEXPLAIN = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.ADDT06SEXPLAIN).FirstOrDefault();
    ADDT06TEXPLAIN = db.BDMT01.Where(x => x.SCHOOL_NO == "403605").Select(x => x.ADDT06TEXPLAIN).FirstOrDefault();
    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div>
            <div class="panel-body">
                <div class="form-horizontal">

                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    @Html.HiddenFor(model => model.SCHOOL_NO)
                    <div class="form-group">
                        @Html.LabelFor(model => model.SCHOOL_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.SCHOOL_NO, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                            @Html.ValidationMessageFor(model => model.SCHOOL_NO, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.SCHOOL_NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.SCHOOL_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "臺北市西湖國民小學" } })
                            @Html.ValidationMessageFor(model => model.SCHOOL_NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.Label("銀行名稱", htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.SHORT_NAMEDESC, new { htmlAttributes = new { @class = "form-control", @placeholder = "西湖小學" } })
                            @Html.ValidationMessageFor(model => model.SHORT_NAMEDESC, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.SHORT_NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.SHORT_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "西湖國小" } })
                            @Html.ValidationMessageFor(model => model.SHORT_NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.SCHOOL_TYPE, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DropDownListFor(model => model.SCHOOL_TYPE, (IEnumerable<SelectListItem>)ViewBag.SchoolTypeItems, new { @class = "form-control" })
                            @Html.ValidationMessageFor(model => model.SCHOOL_TYPE, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.CITY, htmlAttributes: new { @class = "control-label col-md-3", @placeholder = "國小" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.CITY, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.CITY, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.ADDRESS, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.ADDRESS, new { htmlAttributes = new { @class = "form-control", @placeholder = "11443 臺北市內湖區環山路一段25號" } })
                            @Html.ValidationMessageFor(model => model.ADDRESS, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.EngCity, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.EngCity, new { htmlAttributes = new { @class = "form-control", @placeholder = "" } })
                            @Html.ValidationMessageFor(model => model.EngCity, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.EngADDR, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.EngADDR, new { htmlAttributes = new { @class = "form-control", @placeholder = "" } })
                            @Html.ValidationMessageFor(model => model.EngADDR, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.TEL, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.TEL, new { htmlAttributes = new { @class = "form-control", @placeholder = "02-27971267" } })
                            @Html.ValidationMessageFor(model => model.TEL, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.Manager_Name, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.Manager_Name, new { htmlAttributes = new { @class = "form-control", @placeholder = "周逸政" } })
                            @Html.ValidationMessageFor(model => model.Manager_Name, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.Manager_Phone, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.Manager_Phone, new { htmlAttributes = new { @class = "form-control", @placeholder = "02-27971267*174" } })
                            @Html.ValidationMessageFor(model => model.Manager_Phone, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    @*<div class="form-group" title="請使用ipV4格式">
            @Html.LabelFor(model => model.WEBADDRESS, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-9">
                @Html.EditorFor(model => model.WEBADDRESS, new { htmlAttributes = new { @class = "form-control", @placeholder = "sas.hhups.tp.edu.tw" } })
                @Html.ValidationMessageFor(model => model.WEBADDRESS, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.E_MAIL, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-9">
                @Html.EditorFor(model => model.E_MAIL, new { htmlAttributes = new { @class = "form-control", @placeholder = "" } })
                @Html.ValidationMessageFor(model => model.E_MAIL, "", new { @class = "text-danger" })
            </div>
        </div>*@

                    <div class="form-group">
                        @Html.LabelFor(model => model.MaintanceSDate, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.MaintanceSDate, new { htmlAttributes = new { @class = "form-control", @onchange = "dateValidationCheck(this.value);" } })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.MaintanceEDate, htmlAttributes: new { @class = "control-label col-md-3", @onchange = "dateValidationCheck(this.value);" })
                        <div class="col-md-5">
                            @Html.EditorFor(model => model.MaintanceEDate, new { htmlAttributes = new { @class = "form-control" } })
                        </div>
                        <div class="col-md-4">

                            <span style="color:red">  (請謹慎使用，若設了維護區間，代表這個區間，任何帳號都無法登入平台。)</span>
                            @*@Html.ActionLink("暫停投稿的時間", "TOMERGE", null, new { @class = "btn btn-default" })*@
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.ZZZI09_USE_S_MMDD, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.ZZZI09_USE_S_MMDD, new { htmlAttributes = new { @class = "form-control", @onchange = "dateValidationCheck1(this.value);" } })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.ZZZI09_USE_E_MMDD, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.ZZZI09_USE_E_MMDD, new { htmlAttributes = new { @class = "form-control", @onchange = "dateValidationCheck1(this.value);" } })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.ONE_LAP_M, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.ONE_LAP_M, new { htmlAttributes = new { @class = "form-control" } })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.RUN_PEOPLE_STANDARD, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.RUN_PEOPLE_STANDARD, new { htmlAttributes = new { @class = "form-control" } })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.CHECK_CASH_LIMIT, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.CheckBox("CHECK_CASH_LIMIT", Model.CHECK_CASH_LIMIT ?? false, new { @class = "", value = true })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.SendMailPostToTecher, htmlAttributes: new { @class = "control-label col-md-3" })
                    
                        <div class="col-md-9">
                            <label>否</label>
                            @Html.CheckBox("SendMailPostToTecher", Model.SendMailPostToTecher ?? false, new { @class = "", value = true })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.SendMailReadToTecher, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            <label>否</label>
                            @Html.CheckBox("SendMailReadToTecher", Model.SendMailReadToTecher ?? false, new { @class = "", value = true })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.EXPLAIN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @if (!string.IsNullOrWhiteSpace(Model.EXPLAIN))
                            {
                                EXPLAIN = Model.EXPLAIN;

                            }
                            @{
                                Model.EXPLAIN = EXPLAIN;
                            }
                            @Html.TextAreaFor(model => model.EXPLAIN, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.Label("線上投稿的說明頁", htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @if (!string.IsNullOrWhiteSpace(Model.ADDI01Context))
                            {
                                ADDI01Context = Model.ADDI01Context;

                            }
                            @{
                                Model.ADDI01Context = ADDI01Context;
                            }
                            @Html.TextAreaFor(model => model.ADDI01Context, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.BOOKEXPLAIN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @if (!string.IsNullOrWhiteSpace(Model.BOOKEXPLAIN))
                            {
                                BOOKEXPLAIN = Model.BOOKEXPLAIN;

                            }
                            @{
                                Model.BOOKEXPLAIN = BOOKEXPLAIN;
                            }
                            @Html.TextAreaFor(model => model.BOOKEXPLAIN, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>

                    <div class="form-group">
                        @if (!string.IsNullOrWhiteSpace(Model.AwatQ02SEXPLAIN))
                        {
                            AwatQ02SEXPLAIN = Model.AwatQ02SEXPLAIN;

                        }
                        @{
                            Model.AwatQ02SEXPLAIN = AwatQ02SEXPLAIN;
                        }
                        @Html.LabelFor(model => model.AwatQ02SEXPLAIN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(model => model.AwatQ02SEXPLAIN, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>

                    <div class="form-group">
                        @if (!string.IsNullOrWhiteSpace(Model.AwatQ02TEXPLAIN))
                        {
                            AwatQ02TEXPLAIN = Model.AwatQ02TEXPLAIN;

                        }
                        @{
                            Model.AwatQ02TEXPLAIN = AwatQ02TEXPLAIN;
                        }
                        @Html.LabelFor(model => model.AwatQ02TEXPLAIN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(model => model.AwatQ02TEXPLAIN, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>

                    <div class="form-group">
                        @if (!string.IsNullOrWhiteSpace(Model.ADDI01SEXPLAIN))
                        {
                            ADDI01SEXPLAIN = Model.ADDI01SEXPLAIN;

                        }
                        @{
                            Model.ADDI01SEXPLAIN = ADDI01SEXPLAIN;
                        }
                        @Html.LabelFor(model => model.ADDI01SEXPLAIN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(model => model.ADDI01SEXPLAIN, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>

                    <div class="form-group">
                        @if (!string.IsNullOrWhiteSpace(Model.ADDT06SEXPLAIN))
                        {
                            ADDT06SEXPLAIN = Model.ADDT06SEXPLAIN;

                        }
                        @{
                            Model.ADDT06SEXPLAIN = ADDT06SEXPLAIN;
                        }
                        @Html.LabelFor(model => model.ADDT06SEXPLAIN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(model => model.ADDT06SEXPLAIN, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>

                    <div class="form-group">
                        @if (!string.IsNullOrWhiteSpace(Model.ADDT06TEXPLAIN))
                        {
                            ADDT06TEXPLAIN = Model.ADDT06TEXPLAIN;

                        }
                        @{
                            Model.ADDT06TEXPLAIN = ADDT06TEXPLAIN;
                        }
                        @Html.LabelFor(model => model.ADDT06TEXPLAIN, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(model => model.ADDT06TEXPLAIN, new { style = "width:700px;height:200px", @class = "ckeditor" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.LOGO_FILE, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            <input type="file" id="Imgfile" name="Imgfile" class="form-control" style="height:45px" />
                        </div>
                    </div>
                    @if (string.IsNullOrWhiteSpace(ViewBag.ImageUrl) == false)
                    {
                        <div class="form-group ">
                            <span class="control-label col-md-3">原圖檔</span>
                            <img src='@ViewBag.ImageUrl' class="img-responsive" title="原圖檔" />
                        </div>
                    }
                    <div class="text-center">
                        <button id="Save" name="Save" value="Save" class="btn btn-default">
                            存檔
                        </button>
                        <a href='@Url.Action("QUERY", "ZZZI01")' class="btn btn-default">
                            放棄編輯
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

}
<script>

    function dateValidationCheck(str) {
        var re = new RegExp("^([0-9]{4})[./]{1}([0-9]{1,2})[./]{1}([0-9]{1,2})$");
        var strDataValue;
        var infoValidation = true;
        if ((strDataValue = re.exec(str)) != null) {
            var i;
            i = parseFloat(strDataValue[1]);
            if (i <= 0 || i > 9999) { /*年*/
                infoValidation = false;
            }
            i = parseFloat(strDataValue[2]);
            if (i <= 0 || i > 12) { /*月*/
                infoValidation = false;
            }
            i = parseFloat(strDataValue[3]);
            if (i <= 0 || i > 31) { /*日*/
                infoValidation = false;
            }
        } else {
            infoValidation = false;
        }
        if (!infoValidation) {
            alert("請輸入 YYYY/MM/DD 日期格式");
        }
        return infoValidation;
    }
    function dateValidationCheck1(str) {
        var re = new RegExp("^([0-9]{1,2})[./]{1}([0-9]{1,2})$");
        var strDataValue;
        var infoValidation = true;
        if ((strDataValue = re.exec(str)) != null) {
            var i;
          
            i = parseFloat(strDataValue[1]);
            if (i <= 0 || i > 12) { /*月*/
                infoValidation = false;
            }
            i = parseFloat(strDataValue[2]);
            if (i <= 0 || i > 31) { /*日*/
                infoValidation = false;
            }
        } else {
            infoValidation = false;
        }
        if (!infoValidation) {
            alert("請輸入 MM/DD 日期格式");
        }
        return infoValidation;
    }
</script>