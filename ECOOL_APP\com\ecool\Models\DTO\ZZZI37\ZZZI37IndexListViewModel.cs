﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ZZZI37IndexListViewModel
    {
        /// <summary>
        ///線上投稿流水號
        /// </summary>
        [DisplayName("線上投稿流水號")]
        public int? WRITING_NO { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///班級代碼
        /// </summary>
        [DisplayName("班級代碼")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///學年度
        /// </summary>
        [DisplayName("學年度")]
        public byte? SYEAR { get; set; }

        /// <summary>
        ///學期
        /// </summary>
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///文章標題
        /// </summary>
        [DisplayName("文章標題")]
        public string SUBJECT { get; set; }

        /// <summary>
        ///文章內容
        /// </summary>
        [DisplayName("文章內容")]
        public string ARTICLE { get; set; }

        /// <summary>
        ///批閱後內容
        /// </summary>
        [DisplayName("批閱後內容")]
        public string ARTICLE_VERIFY { get; set; }

        /// <summary>
        ///評語
        /// </summary>
        [DisplayName("評語")]
        public string VERIFY_COMMENT { get; set; }

        /// <summary>
        ///圖檔儲存路徑
        /// </summary>
        [DisplayName("圖檔儲存路徑")]
        public string IMG_FILE { get; set; }

        /// <summary>
        ///錄音檔儲存路徑
        /// </summary>
        [DisplayName("錄音檔儲存路徑")]
        public string VOICE_FILE { get; set; }

        /// <summary>
        ///推薦否
        /// </summary>
        [DisplayName("推薦否")]
        public string SHARE_YN { get; set; }

        /// <summary>
        ///獲得點數
        /// </summary>
        [DisplayName("獲得點數")]
        public short? CASH { get; set; }

        /// <summary>
        ///點閱率
        /// </summary>
        [DisplayName("點閱率")]
        public int? READ_COUNT { get; set; }

        /// <summary>
        ///狀態 =>ADDStatus.cs
        /// </summary>
        [DisplayName("狀態 =>ADDStatus.cs")]
        public byte? WRITING_STATUS { get; set; }

        /// <summary>
        ///批閱教師/批閱者
        /// </summary>
        [DisplayName("批閱教師/批閱者")]
        public string VERIFIER { get; set; }

        /// <summary>
        ///審核通過日期
        /// </summary>
        [DisplayName("審核通過日期")]
        public DateTime? VERIFIED_DATE { get; set; }

        /// <summary>
        ///投稿日
        /// </summary>
        [DisplayName("投稿日")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改日期
        /// </summary>
        [DisplayName("修改日期")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///退回/作廢原因
        /// </summary>
        [DisplayName("退回/作廢原因")]
        public string BACK_MEMO { get; set; }

        /// <summary>
        ///作廢人
        /// </summary>
        [DisplayName("作廢人")]
        public string DEL_PERSON { get; set; }

        /// <summary>
        ///作廢日期
        /// </summary>
        [DisplayName("作廢日期")]
        public DateTime? DEL_DATE { get; set; }

        /// <summary>
        ///發送到國語日報
        /// </summary>
        [DisplayName("發送到國語日報")]
        public string PUBLISH_MDNKIDS_YN { get; set; }
    }
}