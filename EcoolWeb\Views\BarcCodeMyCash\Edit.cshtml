﻿@model ECOOL_APP.EF.BDMT05EditViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_EGameMenu", new { NowAction = "BDMT05Index" });
}

<script src="~/Content/ckeditor/ckeditor.js"></script>
@using (Html.BeginForm("EditSave", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", @class = "form-horizontal", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.whereGAME_NO)
    @Html.HiddenFor(m => m.BackAction)
    @Html.HiddenFor(m => m.BackController)
    @Html.HiddenFor(m => m.GAME_NO)

    <section id="feature" class="feature-section feature-section-2">

        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h3 style="color: #6ba1ab;">
                        <strong>@ViewBag.Panel_Title</strong>
                    </h3>
                </div>
                <!-- /.col-lg-12 -->
            </div>
            <hr style="margin: 19px 0px;">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(m => m.GAME_NAME, htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.GAME_NAME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.GAME_NAME) } })
                        @Html.ValidationMessageFor(m => m.GAME_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.CASH, htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.CASH, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.CASH) } })
                        @Html.ValidationMessageFor(m => m.CASH, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.LINK_URL, htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.LINK_URL, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.LINK_URL) } })
                        @Html.ValidationMessageFor(m => m.LINK_URL, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.GAME_IMG, htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.TextBoxFor(m => m.Files, new { @class = "form-control input-md", @type = "file" })
                        @Html.HiddenFor(m => m.GAME_IMG)
                        @Html.ValidationMessageFor(m => m.GAME_IMG, "", new { @class = "text-danger" })

                        @if (string.IsNullOrWhiteSpace(Model != null ? Model.GAME_IMG_PATH : "") == false)
                        {
                            <img src="@Model.GAME_IMG_PATH" class="img-responsive" alt="@Model.GAME_IMG_PATH" />
                        }
                    </div>
                </div>
                <span class="pull-right">
                    <a style="margin: -15px 0px;" role="button" class="btn btn-success btn-sm" onclick="onSave()">儲存</a>

                    @if (Model != null)
                    {
                        if (!string.IsNullOrWhiteSpace(Model.GAME_NO))
                        {
                            <a style="margin: -15px 0px;" role="button" class="btn btn-danger btn-sm" onclick="delete_show()"><em class="fa fa-trash"></em>刪除</a>
                        }
                    }

                    <a style="margin: -15px 0px;" role="button" class="btn btn-success btn-sm" onclick="onBack()">回上一頁</a>
                </span>
            </div>
        </div><!-- /.container -->
    </section><!-- /.feature-section -->
    <div style="height:25px"></div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#formEdit';




        $("#Data_S_DATE").datetimepicker(opt);
        $("#Data_E_DATE").datetimepicker(opt);



        function delete_show() {

            if (confirm("您確定要刪除？") == true) {
                $(targetFormID).attr("action", "@Url.Action("Delete", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }


        function onSave() {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

    </script>
}



