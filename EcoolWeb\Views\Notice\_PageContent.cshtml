﻿
@model EcoolWeb.Models.NoticeApiIndexViewModel



<br />
<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">搜尋欲瀏覽之相關字串</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.SearchContents, new { htmlAttributes = new { @class = "form-control" } })
        @Html.HiddenFor(model => model.Page)
        @Html.HiddenFor(model => model.OrderByName)
        @Html.HiddenFor(model => model.SyntaxName)
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
<br />

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <tbody>
                @foreach (var item in Model.List)
                 {
                    <tr>
                        <td align="left">
                               <b>@APPT02.StatusVal.GetDesc(item.RefAPPT02.STATUS)</b>
                        </td>
                        <td align="right">
                            <b>@Html.DisplayFor(model => item.RefAPPT02.CRE_DATE)</b>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                           
                            @if (string.IsNullOrWhiteSpace(item.RefAPPT02.TO_PATH) == false)
                            {
                            
                                <span class="glyphicon glyphicon-envelope"></span>

                                <a href='@Url.Action("../"+item.RefAPPT02.TO_PATH)' target="_self"
                                class="btn-table-link">
                                    @Html.DisplayFor(model => item.RefAPPT02.BODY_TXT)
                                </a>
                            }
                            else
                            {
                                <span class="glyphicon glyphicon-envelope"></span>
                                @Html.DisplayFor(model => item.RefAPPT02.BODY_TXT)
                            }

                        </td>
                   </tr>
                <tr>
                    <td colspan="2">
                        <br/>
                        <hr>
                    </td>
                </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<div>
    @Html.Pager(Model.List.PageSize, Model.List.PageNumber, Model.List.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
    )
</div>






