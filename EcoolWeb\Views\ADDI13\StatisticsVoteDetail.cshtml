﻿@model RollCallBarcodeEditViewModel
@using ECOOL_APP.EF
@{ 

    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    ViewBag.Title = "發放紙本點數明細";
}
@*@Html.Partial("_Title_Secondary")*@
@Html.Partial("_Notice")
@Html.HiddenFor(x => x.OrdercColumn)
@Html.HiddenFor(x => x.SyntaxName)
@Html.HiddenFor(x => x.Main.ROLL_CALL_ID)

<div class="panel panel-ZZZ" name="TOP" id="cboxLoadedContent">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, "發放紙本點數")
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-black">
                <tr class="thead-primary">
                    <th class="text-nowrap">序號</th>

                    <th class="text-nowrap" onclick="doSort('CLASS_NO')">
                        @Html.DisplayNameFor(model => model.Details.First().CLASS_NO)
                        <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>

                    <th class="text-nowrap" onclick="doSort('SEAT_NO')">
                        @Html.DisplayNameFor(model => model.Details.First().SEAT_NO)
                        <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th class="text-nowrap">
                        @Html.DisplayNameFor(model => model.Details.First().NAME)
                    </th>
                    <th onclick="doSort('ROLL_NUM')">
                        第幾張
                        <img id="ROLL_NUM" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th class="text-nowrap" onclick="doSort('CASH')">
                        @Html.DisplayNameFor(model => model.Details.First().CASH)
                        <img id="CASH" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th class="text-nowrap">
                        @Html.DisplayNameFor(model => model.Details.First().BarCode)
                    </th>
                    <th class="text-nowrap" onclick="doSort('CHARGETIME')">
                        兌換時間
                        <img id="CHARGETIME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                </tr>
            </thead>
            <tbody>
                @{var i = 1; }
                @if (Model.Details?.Count() > 0)
                {
                    string UName = "";
                    string UCLASS_NO = "";
                    string USEAT_NO = "";
                    foreach (var item in Model.Details)
                    {
                        HRMT01 hrtm = new HRMT01();
                        if (!string.IsNullOrWhiteSpace(item.USER_NO) && !string.IsNullOrWhiteSpace(item.SCHOOL_NO))
                        {
                            hrtm = db.HRMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_NO == item.USER_NO).FirstOrDefault();
                            UName = hrtm.NAME;
                            UCLASS_NO = hrtm.CLASS_NO;
                            USEAT_NO = hrtm.SEAT_NO;
                        }
                        else {


                            UName = item.NAME;
                            UCLASS_NO = item.CLASS_NO;
                            USEAT_NO = item.SEAT_NO;

                        }
                        <tr class="text-center">
                            <td align="center"> @i </td>

                            <td align="center">
                                @if (string.IsNullOrWhiteSpace(UCLASS_NO))
                        {
                                    <samp>-</samp>
                                }
                                else
                                {
                                    @UCLASS_NO
                                }
                            </td>
                            <td align="center">
                                @if (string.IsNullOrWhiteSpace(USEAT_NO))
                                {
                                    <samp>-</samp>
                                }
                                else
                                {
                                    @USEAT_NO
                                }
                            </td>
                            <td align="center">
                                @UName
                            </td>
                            <td>   @Html.DisplayFor(modelItem => item.ROLL_NUM)</td>
                            <td align="center">


                                @Html.DisplayFor(modelItem => item.CASH)

                            </td>
                            <td align="center">


                                @Html.DisplayFor(modelItem => item.BarCode)

                            </td>
                            <td align="center">


                                @Html.DisplayFor(modelItem => item.CHG_DATE)

                            </td>

                        </tr>
                        i++;
                    }
                }
            </tbody>
        </table>

    </div>
</div>
<script>
     function funAjax() {
        //var whereGradetxt = $("#whereGrade1").val();
        //console.log(whereGradetxt);
        //var whereCLASS_NOtxt = $("#whereCLASS_NO1").val();
        var WhereSearchtxt = $("#Main_ROLL_CALL_ID").val();
        //console.log(WhereSearchtxt);
        $.ajax({
            type: "GET",
                url: '@(Url.Action("StatisticsVoteDetail", (string)ViewBag.BRE_NO))',
            data: {
                Keyword:WhereSearchtxt,
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
            },
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {

                    $("#cboxLoadedContent").html('');
                    $("#cboxLoadedContent").html(data);
                }
            });
    }
    function doSort(SortCol) {
        var sort = "";
        sort = SortCol;
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        $('#OrdercColumn').val(SortCol);
        console.log(SyntaxName);
        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("ASC");
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
        }
        else {

            $('#OrdercColumn').val(sort);
            $('#SyntaxName').val("Desc");
        }
        funAjax()
    }
</script>