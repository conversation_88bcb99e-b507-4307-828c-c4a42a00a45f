﻿@model CERI04IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}

<style>
    .tooltip-inner {
        text-align: left;
    }
</style>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_Ceri02Menu", "CERI02", new { NowAction = "CERI05" });
}
@using (Html.BeginForm("Index2", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{@Html.AntiForgeryToken()
<div id="PageContent2">
    @Html.Action("_PageContent2", (string)ViewBag.BRE_NO)
</div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function onBtnEdit(ThisACCREDITATION_ID,ThisITEM_NO,ThisSCHOOL_NO,ThisGRADE,ThisCLASS_NO) {
            $(targetFormID).attr("action", "@Url.Action("Edit2", (string)ViewBag.BRE_NO)")
            $('#@Html.IdFor(m=>m.ThisACCREDITATION_ID)').val(ThisACCREDITATION_ID);
            $('#@Html.IdFor(m=>m.ThisITEM_NO)').val(ThisITEM_NO);
            $('#@Html.IdFor(m=>m.ThisSCHOOL_NO)').val(ThisSCHOOL_NO);
            $('#@Html.IdFor(m=>m.ThisGRADE)').val(ThisGRADE);
            $('#@Html.IdFor(m=>m.ThisCLASS_NO)').val(ThisCLASS_NO);
            $(targetFormID).submit();
        }

        function FunPageProc(page) {
            if ($(targetFormID).length > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

        function doSort(SortCol) {

            var OrderByColumnName = $('#@Html.IdFor(m=>m.OrderByColumnName)').val();
            var SortType = $('#@Html.IdFor(m=>m.SortType)').val();

            $('#@Html.IdFor(m=>m.OrderByColumnName)').val(SortCol)

            if (OrderByColumnName == SortCol ) {

                if (SortType.toUpperCase()=="@PageGlobal.SortType.DESC") {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.ASC');
                }
                else {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
                }
            } else {
                 $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
            }

            FunPageProc(1)
        }

        //查詢
        function funAjax() {
            console.log($(targetFormID).serialize());

            $.ajax({
                url: '@Url.Action("_PageContent2")',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent2').html(data);
                }
            });
        }

            function todoClear(IsSubmit) {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            if (IsSubmit) {
                FunPageProc(1);
            }

        }
    </script>
}