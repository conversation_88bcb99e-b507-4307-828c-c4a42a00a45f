﻿@model ECOOL_APP.com.ecool.Models.DTO.ClassBorrowBookViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

}

@Html.Partial("_Title_Secondary")
@{
    Html.RenderAction("_Menu", new { NowAction = "ClassBookBorrow" });
}

@using (Html.BeginForm("ClassBookBorrow", "SECI07", FormMethod.Post, new { id = "form1", name = "form1" }))
{
<div class="row">
    <div class="col-md-12">
        @Html.LabelFor(m => m.Where_SYEAR, new { @class = "col-md-1" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
        </div>
        @Html.LabelFor(m => m.Where_MONTH, new { @class = "col-md-1" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.Where_MONTH, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control", @onchange = "this.form.submit();" })
        </div>
    </div>
</div>
}
<br />

@if (Model.BorrowCountList != null && Model.BorrowCountList.Count > 0)
{
    <table class="table table-responsive table-striped table-hover">
        <thead>
            <tr>
                <th class="text-center">
                    @Html.DisplayNameFor(m => m.BorrowCountList.FirstOrDefault().CLASS_NO)
                </th>
                <th class="text-center">
                    @Html.DisplayNameFor(m => m.BorrowCountList.FirstOrDefault().BORROW_BOOK_COUNT)
                </th>
            </tr>
        </thead>
        <tbody class="text-center">
            @foreach (var item in Model.BorrowCountList)
            {
                <tr>
                    <td>@item.CLASS_NO</td>
                    <td>@item.BORROW_BOOK_COUNT</td>
                </tr>
            }
            <tr>
                <td></td>
                <td style="border-top:2px solid #d56666">
                    <div class="text-danger text-center">
                        本頁總計: @Model.BorrowBookTotal 本書　　
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
}
else
{
    <div>資料量不足</div>
}
