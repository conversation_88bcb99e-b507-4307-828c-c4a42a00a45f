﻿@model GameEditDetailsAnsLevelViewModel

@using (Html.BeginCollectionItemSou("AnsLeve", "DetailsQA", Model.Index, Model.IsCopy))
{
    var AnsLeveIndex = Html.GetIndex("AnsLeve");

    string KeyIndex = $@"{Model.Index}_{AnsLeveIndex}";

    <div class="tr" id="Tr@(KeyIndex)">
        <div class="td" style="text-align:center">
            @Html.CheckBoxFor(m => m.TRUE_ANS, new { @class = "CheckBox_" + Model.Index, @onclick = $"onCheckBoxTrue(this,'CheckBox_{Model.Index}','RETURN_DESC_{Model.Index}')" })
        </div>

        <div class="td">
            @Html.HiddenFor(m => m.LEVEL_NO)
            @Html.HiddenFor(m => m.LEVEL_TYPE)
            @Html.EditorFor(m => m.LEVEL_NAME, new { htmlAttributes = new { @class = "form-control input-md" } })
        </div>

        <div class="td">
            @Html.EditorFor(m => m.CASH, new { htmlAttributes = new { @class = "form-control input-md" } })
        </div>
        <div class="td">

            @if (string.IsNullOrWhiteSpace(Model.LEVEL_IMG_PATH))
            {
                <samp class="DivPHOTO_FILE">
                    @Html.TextBoxFor(m => m.PhotoFiles, new { @class = "form-control input-md", @type = "file" })
                    @Html.ValidationMessageFor(m => m.PhotoFiles, "", new { @class = "text-danger" })
                </samp>

            }
            else
            {
                <div id="DivPHOTO_FILE_URL_@(KeyIndex)" style="display:inline;">
                    <a role='button' style="cursor:pointer;" onclick="DelFile('@KeyIndex')"> <i class='glyphicon glyphicon-remove'></i></a>

                    <div class="colorboxPHOTO thumbnail" style="cursor:pointer;width:150px;height:150px;display:table-cell; vertical-align:middle; " href="@Model.LEVEL_IMG_PATH" title="@Model.LEVEL_IMG">
                        <img src="@Model.LEVEL_IMG_PATH" style="display:inline;max-width:140px;max-height:140px" alt="Responsive image" href="@Model.LEVEL_IMG_PATH" class="colorboxPHOTO" />
                    </div>
                </div>

                <div style="display:none" class="DivPHOTO_FILE" id="DivPHOTO_FILE_@(KeyIndex)">
                    @Html.EditorFor(m => m.LEVEL_IMG, new { htmlAttributes = new { @class = "form-control input-md" } })
                    @Html.ValidationMessageFor(m => m.LEVEL_IMG, "", new { @class = "text-danger" })
                </div>

                <div style="display:none" class="DivUpPhotoFiles" id="DivUpPhotoFiles_@(KeyIndex)">
                    @Html.TextBoxFor(m => m.PhotoFiles, new { @class = "form-control input-md", @type = "file" })
                    @Html.ValidationMessageFor(m => m.PhotoFiles, "", new { @class = "text-danger" })
                </div>
            }
        </div>
        <div class="td" >
            @Html.EditorFor(m => m.RETURN_DESC, new { htmlAttributes = new {@class = $"RETURN_DESC_{Model.Index} form-control input-md" } })
        </div>
    </div>
}