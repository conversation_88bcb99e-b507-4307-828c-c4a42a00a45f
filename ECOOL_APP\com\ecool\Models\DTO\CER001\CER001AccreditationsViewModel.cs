﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CER001AccreditationsViewModel
    {
        /// <summary>
        ///護照細項ID
        /// </summary>
        [DisplayName("護照細項ID")]
        public string ACCREDITATION_ID { get; set; }

        public string ITEM_NO { get; set; }

        /// <summary>
        ///護照ID
        /// </summary>
        [DisplayName("護照ID")]
        public string ACCREDITATION_TYPE { get; set; }

        [DisplayName("護照名稱")]
        public string TYPE_NAME { get; set; }

        /// <summary>
        ///護照細項名稱
        /// </summary>
        [DisplayName("護照細項名稱")]
        public string ACCREDITATION_NAME { get; set; }

        [DisplayName("通過主旨")]
        public string SUBJECT { get; set; }
        public string IsText { get; set; }
        [DisplayName("通過內容")]
        public string CONTENT { get; set; }
    }
}