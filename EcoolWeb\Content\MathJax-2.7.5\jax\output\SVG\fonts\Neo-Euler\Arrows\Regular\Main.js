/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Arrows/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Arrows={directory:"Arrows/Regular",family:"NeoEulerMathJax_Arrows",id:"NEOEULERARROWS",32:[0,0,333,0,0,""],160:[0,0,333,0,0,""],8612:[500,0,1000,56,944,"944 50v400h-40v-180h-778c38 47 90 126 115 230h-40c-31 -130 -113 -220 -145 -250c30 -28 114 -119 145 -250h40c-25 105 -78 184 -115 230h778v-180h40"],10235:[500,0,1690,56,1634,"1634 50v400h-40v-180h-1468c38 47 90 126 115 230h-40c-31 -130 -113 -220 -145 -250c30 -28 114 -119 145 -250h40c-25 105 -78 184 -115 230h1468v-180h40"],10237:[598,98,1700,76,1643,"1603 328v-156h-1352c-31 29 -64 55 -99 78c35 23 68 49 99 78h1352zM76 250c145 -72 257 -196 312 -348h40c-31 85 -78 163 -138 230h1313v-182h40v600h-40v-182h-1313c60 67 107 145 138 230h-40c-55 -152 -167 -276 -312 -348"],10238:[598,98,1700,75,1643,"1643 250c-146 -72 -258 -196 -313 -348h-40c31 85 78 163 138 230h-1313v-182h-40v600h40v-182h1313c-60 67 -107 145 -138 230h40c55 -152 167 -276 313 -348zM115 328v-156h1352c31 29 64 55 99 78c-35 23 -68 49 -99 78h-1352"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Arrows/Regular/Main.js");
