<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="https://ecard.tp.edu.tw:9443/EzCodeStudMap/EzCodeStudMap.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/EzCodeStudMap" />
  <xs:element name="GetData">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="eduNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="grade" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="updateDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userCode" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDataResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/EzCodeStudMap" minOccurs="0" name="GetDataResult" nillable="true" type="q1:ArrayOfEzCodeStudData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>