﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string SchoolName = EcoolWeb.Models.UserProfileHelper.GetSchoolSName();
    string SchoolType = EcoolWeb.Models.UserProfileHelper.GetSchoolType();

    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData("~/Views/Shared/_Layout.cshtml");

    string BodyBackgroundImage = SchoolType == BDMT01.SchoolType.ElementarySchool ? "~/Content/img/web-bg-02-50.png" : "~/Content/img/web-bg-02-50-1.png";
    string NavbarHeaderImage = SchoolType == BDMT01.SchoolType.ElementarySchool ? "~/Content/img/web-16.png" : "~/Content/img/web-16-1.png";

    string LogoAct = "GuestIndex";
    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        { LogoAct = "StudentIndex"; }
        else
        { LogoAct = "TeacherIndex"; }

    }

    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,Chrome=1" />
   <script src="~/Scripts/jquery-3.6.4.min.js"></script>
   <script src="~/Scripts/jquery-ui.min.js"></script>
    <title>@ViewBag.Title</title>

    @Styles.Render("~/Content/css")

    @if (AppMode == false)
    {
        <link href="~/Content/css/EzCss.min.css?@DateNowStr" rel="stylesheet" />
    }
    else
    {
        <link href="~/Content/css/AppEzCss.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />

        <style type="text/css">
            .App_hide {
                display: none;
            }

            .App_show {
                display: block;
            }

            .AppMode_hide {
                display: none;
            }

            .AppGird {
                font-size: 25px;
                color: red;
            }
            /*右版按鈕重疊時區塊div穿透a保持不穿透*/
            ._Layout_RightBar {
                pointer-events: none;
            }

                ._Layout_RightBar > a {
                    display: inline-block;
                    pointer-events: visible;
                }
        </style>
    }

    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    <link href="~/Scripts/Basic-Responsive-Table-Plugin/basictable.css" rel="stylesheet" />
    @RenderSection("css", required: false)

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/bootstrap")

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.10/js/i18n/defaults-zh_TW.js"></script>
    <script src="~/Scripts/Basic-Responsive-Table-Plugin/jquery.basictable.min.js"></script>
    <!--[if lt IE 9]>
      <script src="~/Scripts/html5shiv.min.js"></script>
      <script src="~/Scripts/respond.min.js"></script>
    <![endif]-->
    <script src="~/Scripts/respond.min.js"></script>
    @{ Html.RenderPartial("_GoogleAnalytics"); }
</head>
@Html.Partial("../Shared/_CheckBrowser")
<body style="background-image:url('@Url.Content(BodyBackgroundImage)');background-repeat:repeat">
    <div id="LayoutTOP"></div>

    @if (AppMode == false)
    {
        <div class="visible-xs">
            <!---手機 -->
            <nav class="navbar navbar-phone navbar-fixed-top" role="navigation">

                <div class="navbar-header pull-left">
                    <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".navbar-ex1-collapse" title="Menu">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>

                    <a class="line-left"></a>

                    <a href='@Url.Action("PortalIndex", "Home")' class="btn-logo-layout">
                        <img src="~/Content/img/web-student_png-16.png" class="img-responsive " alt="Responsive image" title="回台北e酷幣首頁" />
                    </a>

                    <a class="line-left"></a>

                    @if (string.IsNullOrWhiteSpace(SchoolNO) == false)
                    {
                        <a class="btn-logo-school" href='@Url.Action(LogoAct, "Home", new { school = SchoolNO })' title="回學校首頁">
                            <img src='@Url.Action("BDMT01IMG", "Content", new { school = SchoolNO + ".png" })' />
                        </a>

                        <a class="btn-font-school" href='@Url.Action("GuestIndex", "Home", new {school= SchoolNO })' title="回學校首頁">
                            @SchoolName
                        </a>
                    }
                </div>

                @if (user == null)
                {
                    <a role="button" class="btn-User" href="@Url.Action("LoginPage","Home")">
                        <i class="fa fa-user" style="font-size:2.5em" title="會員登入"></i>
                    </a>
                }
                else
                {
                    <a role="button" class="btn-User" href="@Url.Action("Logout","Home")">
                        <i class="fa fa-power-off" style="font-size:2.5em" title="登出"></i>
                    </a>
                }
                <a class="line-right"></a>
            </nav>
        </div>

        <div class="hidden-xs">
            <!---PC -->
            <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="navbar-header" style="height:87px;width: 100%; background-image: url('@Url.Content(NavbarHeaderImage)');background-repeat:repeat-x;">
                    <span class="navbar-brandEZ">@Html.Partial("../Shared/_Navbar")</span>
                </div>
            </nav>
        </div>
    }

    @if (AppMode == false)
    {
        <div style="width:auto;height:80px"></div>
        <div class="containerEZ">
            <br>
            <div class="row">
                <div class="col-md-3 col-lg-2 Div-Menu" id="sidebar">
                    @Html.Action("PermissionMenu", "Menu")
                </div>
                <div class="col-md-8" id="container">
                    @RenderBody()
                </div>

                    <div class="col-md-2 text-center _Layout_RightBar">
                        @Html.Action("_RightBar", "Home")
                    </div>
            </div>

            @if (System.Web.Configuration.WebConfigurationManager.AppSettings["TestMode"] != "true")
            {                
                    @Html.Action("_Footer", "Home")
            }
        </div>
    }
    else
    {
        <div style="width:auto;height:3px"></div>
        <div class="containerEZ">
            <br>
            <div style="width:95%;margin: 0px auto;">
                @RenderBody()
            </div>
        </div>
    }

    @RenderSection("scripts", required: false)

    @if (TempData["ErrMessage"] != null)
    {
        <script>
            window.alert('@TempData["ErrMessage"]');
        </script>
    }
    <script type="text/javascript">
        window.history.forward(1);
       
    </script>
</body>
</html>