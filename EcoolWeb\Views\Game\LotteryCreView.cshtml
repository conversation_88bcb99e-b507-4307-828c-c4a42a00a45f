﻿@model GameLotteryCreViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "Lottery" });
    }
}

@using (Html.BeginForm("LotteryCreView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.LotteryType)

    @Html.HiddenFor(m => m.Main.LOTTERY_NO)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            抽獎設定
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                @if (ViewBag.GAME_TYPE == (byte)ADDT26.GameType.一般)
                {
                    <div class="form-group">
                        @Html.LabelFor(model => model.Main.LEVEL_COUNT, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9" id="LevelCountRadio">
                            <div>
                                @Html.EditorFor(model => model.Main.LEVEL_COUNT,
                                "_RadioButtonList",
                                new
                                {
                                TagName = (Html.NameFor(model => model.Main.LEVEL_COUNT)).ToHtmlString(),
                                RadioItems = ViewBag.LevelCountItem,
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "",
                                })
                            </div>
                            @Html.ValidationMessageFor(model => model.Main.LEVEL_COUNT, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Main.ArrLEVEL, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9" id="LevelcheckBox">
                            <div>
                                @Html.EditorFor(model => model.Main.ArrLEVEL,
                              "_CheckBoxList",
                              new
                              {
                                  TagName = (Html.NameFor(model => model.Main.ArrLEVEL)).ToHtmlString(),
                                  CheckBoxItems = ViewBag.LevelItem,
                                  Position = Position.Horizontal,
                                  Numbers = int.MaxValue,
                                  onclick = "",
                              })
                            </div>
                            @Html.ValidationMessageFor(model => model.Main.LEVEL, "", new { @class = "text-danger" })
                        </div>
                    </div>
                }
                else
                {
                    <div class="form-group">
                        @Html.LabelFor(model => model.Main.IS_FULL, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.CheckBoxFor(model => model.Main.IS_FULL, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                            <label>排除</label>
                            @Html.ValidationMessageFor(model => model.Main.IS_FULL, "", new { @class = "text-danger" })
                        </div>
                    </div>
                }
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.PEOPLE_COUNT, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.PEOPLE_COUNT, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.Main.PEOPLE_COUNT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.UNLOTTERY, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.CheckBoxFor(model => model.Main.UNLOTTERY, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        <label>排除</label>
                        @Html.ValidationMessageFor(model => model.Main.UNLOTTERY, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Main.UNCUEST, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.CheckBoxFor(model => model.Main.UNCUEST, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        <label>排除</label>
                        @Html.ValidationMessageFor(model => model.Main.UNCUEST, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.IS_GIVE_UP_LOTTERY, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.CheckBoxFor(model => model.Main.IS_GIVE_UP_LOTTERY, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        <label>可重新抽獎</label>
                        @Html.ValidationMessageFor(model => model.Main.IS_GIVE_UP_LOTTERY, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.LOTTERY_DESC, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.LOTTERY_DESC, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填", @maxlength = "30" } })
                        @Html.ValidationMessageFor(model => model.Main.LOTTERY_DESC, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="儲存設定" class="btn btn-default" onclick="OnLottery('@( (byte)ADDT32.LotteryTypeVal.儲存設定)')" />
                <input type="button" value="儲存及進入開始抽獎畫面" class="btn btn-default" onclick="OnLottery('@((byte)ADDT32.LotteryTypeVal.儲存及進入開始抽獎畫面)')" />
                <button class="btn btn-default" type="button" onclick="onBack()">放棄設定</button>
            </div>
        </div>
    </div>
    <div>
        @{
            if (ViewBag.GAME_TYPE == (byte)ADDT26.GameType.一般)
            {
                Html.RenderAction("_LevelPersonCount", new { GAME_NO = Model.Search.WhereGAME_NO });
            }
        }
    </div>

}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="~/Content/img/Logo_loading2.gif" style="max-height:350px;" />
        <br />
        <h3>抽獎中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        jQuery(document).ready(function () {
            $(".checkbox-inline:eq(1)").before('<br />');

            if ($('#@Html.IdFor(m=>m.Main.ArrLEVEL)').val()=='') {
                $("input[id='Main.ArrLEVEL_ALL']").prop("checked", true);
            }

            $(".radio-inline:eq(1)").before('<br />');

        })

        $("#LevelcheckBox input:checkbox").each(function (index) {
            $(this).bind('click', function () {

                if ($(this).attr("id") != 'Main.ArrLEVEL_ALL') {
                    if ($(this).is(':checked')) {
                        $("input[id='Main.ArrLEVEL_ALL']").prop("checked", false);
                    }
                }
                else {
                    if ($(this).is(':checked')) {
                        $("#LevelcheckBox input:checkbox").not("input[id='Main.ArrLEVEL_ALL']").prop("checked", false);
                    }

                }
            });
        });

        function OnLottery(LotteryTypeVal)
        {

            $('#@Html.IdFor(m=>m.LotteryType)').val(LotteryTypeVal)

            var PEOPLE_COUNT = $('#@Html.IdFor(m=>m.Main.PEOPLE_COUNT)').val();
            if (PEOPLE_COUNT == '') {
                alert('請輸入抽獎人數')
                return false;
            }

            if ($.isNumeric(PEOPLE_COUNT) == false) {
                alert('抽獎人數，請輸入數字')
                return false;
            }

            var LOTTERY_DESC = $('#@Html.IdFor(m=>m.Main.LOTTERY_DESC)').val();
            if (LOTTERY_DESC == '') {
                alert('請輸入抽獎名稱')
                return false;
            }

            LotteryShowPeopleView();
        }

        function LotteryShowPeopleView()
        {
           // $(targetFormID).attr("action", "@Url.Action("LotteryShowPeopleView", (string)ViewBag.BRE_NO)")
            $(targetFormID).attr("action", "@Url.Action("ChiefLottery", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Lottery", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}