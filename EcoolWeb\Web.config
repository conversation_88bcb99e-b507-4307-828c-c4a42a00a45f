﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  如需如何設定 ASP.NET 應用程式的詳細資訊，請瀏覽
  http://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\aspnet-EcoolWeb-20150505020605.mdf;Initial Catalog=aspnet-EcoolWeb-20150505020605;Integrated Security=True;User Instance=True" providerName="System.Data.SqlClient" />
    <add name="ECOOL_DEVEntities" connectionString="metadata=res://*/EF.ECoolDev.csdl|res://*/EF.ECoolDev.ssdl|res://*/EF.ECoolDev.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=.\sqlexpress;initial catalog=ECOOL_DEV;user id=sa;password=********;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <appSettings>
    <add key="TestMode" value="false" />
    <!--  APP認証(正) true ,(測)false -->
    <add key="IsAppProduction" value="true" />
    <!--  Cache 資料 ( 預設 600 秒 )-->
    <add key="EnableCache" value="true" />
    <add key="CacheDurationSeconds" value="600" />
    <!-- 維修日期僅管理員 -->
    <add key="MaintanceSDate" value="2011/07/28" />
    <add key="MaintanceEDate" value="2011/07/31" />
    <!-- 密碼輸入5次鎖定次數 -->
    <add key="VlidatePasswordMaxCount" value="5" />
    <!-- Upload路徑 -->
    <add key="ProductImgPath" value="Content\ProductIMG" />
    <add key="UploadImageRoot" value="~\Content\" />
    <!--<add key ="CopyFilePath" value="D:\Website\EcoolWeb\Content\ArtGallery"/> -->
    <add key="DownloadFilePath" value="" />
    <add key="CopyFilePath" value="D:\workspace2\ECOOL\ECOOL\EcoolWeb\Content\ArtGallery" />
    <!-- FileUpload路徑 -->
    <add key="LogPath" value="C:\ECOOL\FileUpload" />
    <add key="FilePath" value="C:\ECOOL\FileUpload" />
    <add key="GameFilePath" value="~\File\" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <!-- 寄送Mail相關 -->
    <add key="MailEnabled" value="true" />
    <add key="sendMail" value="<EMAIL>" />
    <add key="smtpServer" value="smtp.gmail.com" />
    <add key="smtpPort" value="587" />
    <add key="mailAccount" value="<EMAIL>" />
    <add key="APPVersion" value="1.13" />
    <add key="mailPwd" value="waewqisazqzsgdcx" />
    <!-- 是否要寫入HRMT05_CHECK_LOG-->
    <add key="EnableIsWriteAppLog" value="true" />
    <!-- GA 帳號代碼 SharedGlobal 有預設值 空白
     設 UA-********-3 西湖酷幣

    -->
    <add key="GoogleAnalytics" value="" />
    <!-- Logo 相關 SharedGlobal 有預設值
      設 Logo.png  西湖酷幣
      設 childrensMonth-banner-PC.png"  兒童月
    -->
    <add key="Logo" value="Logo.png " />
    <!-- 過場動畫 loading 圖示 SharedGlobal 有預設值
       設 "~/Content/img/<EMAIL>"  西湖酷幣
       設 "~/Content/img/ChildMonthLogo.svg"  兒童月
    -->
    <add key="Logo_loading" value="~/Content/img/<EMAIL>" />
    <!-- 過場動畫 loading 畫面 ，底色
       設 "black"  西湖酷幣
       設 "#ffffff"  兒童月
    -->
    <add key="Logo_loading_background_color" value="black" />
    <!-- 產生 QrCode 中間的 Logo 路徑  SharedGlobal 有預設值
    設 "~/Content/img/EcoolLogo.png"  西湖酷幣
    設 "~/Content/img/childrensMonthLogo.png"  兒童月
    -->
    <add key="QrCodeLogo" value="~/Content/img/EcoolLogo.png" />
    <!-- 首頁路徑   SharedGlobal 有預設值
    設 GuestIndex 西湖酷幣
    設 HomeIndex  兒童月
    -->
    <add key="HomeIndex" value="GuestIndex" />
    <!-- 首次撈悠遊卡內外碼 -->
    <add key="IsCardNumberSchedual_Init" value="true" />
  </appSettings>
  <!--
    如需 web.config 變更的說明，請參閱 http://go.microsoft.com/fwlink/?LinkId=235367。

    您可以在 <httpRuntime> 標記上設定下列屬性。
      <system.Web>
        <httpRuntime targetFramework="4.5.1" />
      </system.Web>
  -->
  <system.webServer>
   
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="209715200" />
      </requestFiltering>
    </security>
    <modules runAllManagedModulesForAllRequests="false">
      <remove name="TelemetryCorrelationHttpModule" />
      <remove name="ApplicationInsightsWebTracking" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>
  <system.web>
    <customErrors mode="Off" />
    <authentication mode="None" />
    <compilation debug="true" targetFramework="4.6.1" />
    <httpRuntime requestValidationMode="2.0" maxRequestLength="3072000" targetFramework="4.5" />
    <sessionState timeout="300" />
    <pages>
      <namespaces>
        <add namespace="MvcPaging" />
      </namespaces>
    </pages>
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.2.0" newVersion="5.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Spire.Pdf" publicKeyToken="663f351905198cb3" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IEzCodeStudMap" maxBufferSize="**********" maxReceivedMessageSize="**********">
          <readerQuotas maxDepth="32" maxStringContentLength="**********" maxArrayLength="16348" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
        </binding>
        <binding name="BasicHttpsBinding_IEzCodeStudMap" maxBufferSize="**********" maxReceivedMessageSize="**********">
          <security mode="Transport" />
          <readerQuotas maxDepth="32" maxStringContentLength="**********" maxArrayLength="16348" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
        </binding>
      </basicHttpBinding>
    </bindings>
    <client>
      <!--<endpoint address="http://*************:8080/EzCodeStudMap/EzCodeStudMap.svc"
        binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IEzCodeStudMap"
        contract="EzCodeStudMap.IEzCodeStudMap" name="BasicHttpBinding_IEzCodeStudMap" />-->
      <endpoint address="https://ecard.tp.edu.tw:9443/EzCodeStudMap/EzCodeStudMap.svc?wsdl" binding="basicHttpBinding" bindingConfiguration="BasicHttpsBinding_IEzCodeStudMap" contract="EzCodeStudMap.IEzCodeStudMap" name="BasicHttpsBinding_IEzCodeStudMap" />
    </client>
  </system.serviceModel>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
</configuration>