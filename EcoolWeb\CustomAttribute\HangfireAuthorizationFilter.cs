﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using Hangfire.Annotations;
using Hangfire.Dashboard;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.CustomAttribute
{
    public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
    {
        public bool Authorize([NotNull] DashboardContext context)
        {
            UserProfile user = UserProfileHelper.Get();

            if (user?.ROLE_TYPE <= HRMT24_ENUM.RoleTypeVal.AdminLevel)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}