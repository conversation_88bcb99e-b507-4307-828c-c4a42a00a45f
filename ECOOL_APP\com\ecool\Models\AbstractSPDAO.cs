﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.Models
{
    public abstract class AbstractSPDAO
    {

        //檢核錯誤
        public void handleException(string sp_name, string DBMsg)
        {
            //if (null != DBMsg && !"".Equals(DBMsg))
            //{
            //    Console.WriteLine("預儲程式" + sp_name + " 執行時發生錯誤:" + DBMsg.ToString());

            //    if (!"".Equals(DBMsg.ToString().Trim()))
            //    {
            //        throw new Exception("Stored Procedure名稱:" + sp_name + "執行時發生錯誤:" + DBMsg.ToString());
            //        //throw new Exception("請洽系統管理員");
            //    }

            //}
        }


    }
}
