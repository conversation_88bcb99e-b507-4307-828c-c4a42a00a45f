﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.com.ecool.Models.entity;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class Menu
    {
        //我的秘書
        public IEnumerable<uZZT01> SEC { get; set; }

        //加值應用
        public IEnumerable<uZZT01> ADD { get; set; }

        //e酷幣市集
        public IEnumerable<uZZT01> AWA { get; set; }

        //統計與排行
        public IEnumerable<uZZT01> SAR { get; set; }

        //能力認證
        public IEnumerable<uZZT01> CER { get; set; }
        //能力認證
        public IEnumerable<uZZT01> CAR { get; set; }
        //維運管理
        public IEnumerable<uZZT01> ZZZ { get; set; }

        //帳號管理
        public IEnumerable<uZZT01> ACC { get; set; }

        //酷嗶嗶嗶
        public IEnumerable<uZZT01> COO { get; set; }
        public IEnumerable<uZZT01> AWAC { get; set; }
    }
}