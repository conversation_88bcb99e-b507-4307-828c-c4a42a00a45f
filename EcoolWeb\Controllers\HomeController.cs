﻿using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP;
using ECOOL_APP.EF;
using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using System.Data.Entity.Validation;
using System.Data.Entity;
using ECOOL_APP.com.ecool.util;
using EcoolWeb.CustomAttribute;
using ECOOL_APP.com.ecool.Models;
using EcoolWeb.ViewModels;
using System.Drawing;
using System.Drawing.Imaging;
using ECOOL_APP.com.ecool.LogicCenter;
using System.Transactions;
using ECOOL_APP.com.ecool.service;
using Hangfire;
using EcoolWeb.Util;
using log4net;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Dapper;
using System.Xml;
using System.Net;
using System.IO;
using ECOOL_APP.com.ecool.Models.DTO.AWAT14;

namespace EcoolWeb.Controllers
{
    public class HomeController : Controller
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static HRMT01 CheckLogin(string SchoolNo, string UserNo, string Password, out string Message, string SLogingType = "normal")
        {
            Message = string.Empty;
            HRMT01 FindUser;
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                Regex regexCode = new Regex(@",");
                if (regexCode.IsMatch(SchoolNo)) {
                    string[] operands = Regex.Split(SchoolNo, @",");
                    if (operands.Length > 0) {
                        SchoolNo = operands[0];


                    }

                }
                    BDMT01 bdmt01 = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNo).FirstOrDefault();

                DateTime MaintanceSDate = Convert.ToDateTime(bdmt01.MaintanceSDate);
                DateTime MaintanceEDate = Convert.ToDateTime(bdmt01.MaintanceEDate);

                FindUser = db.HRMT01.Include("HRMT25").Where(user => user.SCHOOL_NO == SchoolNo && user.USER_NO == UserNo).FirstOrDefault();

                //找不到帳號
                if (FindUser == null)
                {
                    Message = "找不到帳號";
                    return null;
                }

                if (MaintanceSDate <= DateTime.Now && MaintanceEDate >= DateTime.Now && FindUser.USER_TYPE != UserType.Admin)
                {
                    Message = "目前為資料維護期間，平臺暫停使用。暫停時間：" +string.Format("{0:D}", MaintanceSDate)+ "至" + string.Format("{0:D}", MaintanceEDate) + "。若有問題，請洽學校老師。";
                    return null;
                }

                if (FindUser.USER_STATUS == UserStaus.Lock)
                {
                    Message = "該帳號已鎖定，請聯絡學校管理老師";
                    return null;
                }
                else if (FindUser.USER_STATUS == UserStaus.Disable)
                {
                    Message = "該帳號已停用";
                    return null;
                }
                else if (FindUser.USER_STATUS == UserStaus.Invalid)
                {
                    Message = "該帳號已失效";
                    return null;
                }

                //家長第一次登入(未綁定任何學生前)
                if (FindUser.USER_TYPE == UserType.Parents)
                {
                    if (HRMT06.CkPanyStudent(SchoolNo, UserNo, db) == false)
                    {
                        //需用學生密碼登入
                        if (db.ZZT08.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == UserNo.Substring(1) && a.PASSWORD == Password).Any())
                        {
                            return FindUser;
                        }
                        else
                        {
                            Message = "密碼不符，請注意大小寫區隔，家長第一次登入，請使用你小孩子的密碼登入";
                            return null;
                        }
                    }
                }

                if (FindUser.ZZT08 == null)
                {
                    Message = "帳號密碼檔未有紀錄";
                    return null;
                }
                else if (FindUser.ZZT08.PASSWORD != Password)
                {
                    if (SLogingType == SLogingTypeVal.switching)
                    {
                        Message = "此使用者密碼已改變";
                    }
                    else if (SLogingType == SLogingTypeVal.AppAuto)
                    {
                        Message = "密碼已改變，需重新登入";
                    }
                    else
                    {
                        Message = "密碼不符，請注意大小寫區隔";
                    }

                    return null;
                }
            }

            return FindUser;
        }

        public static HRMT01 GetUser(string SchoolNo, string UserNo, out string Message)
        {
            Message = string.Empty;
            HRMT01 FindUser;
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                BDMT01 bdmt01 = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNo).FirstOrDefault();

                DateTime MaintanceSDate = Convert.ToDateTime(bdmt01.MaintanceSDate);
                DateTime MaintanceEDate = Convert.ToDateTime(bdmt01.MaintanceEDate);

                FindUser = db.HRMT01.Include("HRMT25").Where(user => user.SCHOOL_NO == SchoolNo && user.USER_NO == UserNo).FirstOrDefault();

                //找不到帳號
                if (FindUser == null)
                {
                    Message = "找不到帳號";
                    return null;
                }

                if (MaintanceSDate <= DateTime.Now && MaintanceEDate >= DateTime.Now && FindUser.USER_TYPE != UserType.Admin)
                {
                    Message = "目前為資料維護期間，平臺暫停使用。暫停時間：" + string.Format("{0:D}", MaintanceSDate) + "至" + string.Format("{0:D}", MaintanceEDate) + "。若有問題，請洽學校老師。";
                    return null;
                }

                if (FindUser.USER_STATUS == UserStaus.Lock)
                {
                    Message = "該帳號已鎖定，請聯絡學校管理老師";
                    return null;
                }
                else if (FindUser.USER_STATUS == UserStaus.Disable)
                {
                    Message = "該帳號已停用";
                    return null;
                }
                else if (FindUser.USER_STATUS == UserStaus.Invalid)
                {
                    Message = "該帳號已失效";
                    return null;
                }
            }

            return FindUser;
        }

        public static void NextAction(UserProfile LoginUser, out string ActionName, out string ControllerName, string returnURL = "")
        {
            switch (LoginUser.USER_TYPE)
            {
                case "S":   //學生
                    if (LoginUser.Chance_BIRTHDAY)
                    {
                        ActionName = "Birthday";
                        ControllerName = "Home";
                    }
                    else if (LoginUser.INIT_STATUS == null || LoginUser.INIT_STATUS == 0)
                    {
                        ActionName = "MODIFY";
                        ControllerName = "ZZT08";
                    }
                    else if (LoginUser.NotePassGrade > 0)
                    {
                        ActionName = "PassNote";
                        ControllerName = "Home";
                    }
                    else if (LoginUser.NoteReadLevel > 0)
                    {
                        ActionName = "ReadLevelNote";
                        ControllerName = "Home";
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(returnURL))
                        {
                            string[] Url = returnURL.Split('/');

                            if (Url.Length >= 3)
                            {
                                ActionName = Url[2];
                                ControllerName = Url[1];
                            }
                            else
                            {
                                ControllerName = Url[1];
                                ActionName = "Index";
                            }
                        }
                        else
                        {
                            ActionName = "StudentIndex";
                            ControllerName = "Home";
                        }
                    }
                    break;

                case "T":   //教師
                case "A":   //管理者
                    if (LoginUser.INIT_STATUS == null || LoginUser.INIT_STATUS == 0)
                    {
                        ActionName = "MODIFY";
                        ControllerName = "ZZT08";
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(returnURL))
                        {
                            string[] Url = returnURL.Split('/');

                            if (Url.Length >= 3)
                            {
                                ActionName = Url[2];
                                ControllerName = Url[1];
                            }
                            else
                            {
                                ControllerName = Url[1];
                                ActionName = "Index";
                            }
                        }
                        else
                        {
                            ActionName = "TeacherIndex";
                            ControllerName = "Home";
                        }
                    }
                    break;

                case "P": //家長

                    if (HRMT06.CkPanyStudent(LoginUser.SCHOOL_NO, LoginUser.USER_NO) == false)
                    {
                        ActionName = "Edit_last";
                        ControllerName = "ZZZI28";
                    }
                    else
                    {
                        if (LoginUser.INIT_STATUS == null || LoginUser.INIT_STATUS == 0)
                        {
                            ActionName = "MODIFY";
                            ControllerName = "ZZT08";
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(returnURL))
                            {
                                string[] Url = returnURL.Split('/');

                                if (Url.Length >= 3)
                                {
                                    ActionName = Url[2];
                                    ControllerName = Url[1];
                                }
                                else
                                {
                                    ControllerName = Url[1];
                                    ActionName = "Index";
                                }
                            }
                            else
                            {
                                ActionName = "ParentIndex";
                                ControllerName = "Home";
                            }
                        }
                    }
                    break;

                default:
                    ActionName = "GuestIndex";
                    ControllerName = "Home";
                    break;
            }
        }

        public ActionResult privacyPolicy()
        {
            return View();
        }

        /// <summary>
        /// 兒童月首頁
        /// </summary>
        /// <returns></returns>
        public ActionResult ChildMonthIndex()
        {
            BuildPortalHomeModels();

            var gameService = new GameService();
            var aADDT26 = gameService.GetNowGameNo();

            ViewBag.GAME_NO = aADDT26?.GAME_NO ?? "";
            ViewBag.SCHOOL_NO = aADDT26?.SCHOOL_NO ?? "";
            // EcoolWeb.Models.UserProfileHelper.Set(aADDT26?.SCHOOL_NO ?? "");

            return View();
        }

        /// <summary>
        /// 兒童月手機版登入
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult LoginChildMonthPage()
        {
            return View();
        }

        public ActionResult CheckCard()
        {
            ExtractCardService exservice = new ExtractCardService();
            exservice.GetCardNumberSchedual();
            return View();

          
        }
        private static XmlDocument CreateSoapEnvelope()
        {
            XmlDocument soapEnvelopeDocument = new XmlDocument();
            soapEnvelopeDocument.LoadXml(@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:tem=""http://tempuri.org/"">
<soapenv:Header/>
   <soapenv:Body>
      <tem:GetData>
         <!--Optional:-->
         <tem:eduNumber>403605</tem:eduNumber>
         <!--Optional:-->
         <tem:grade>1</tem:grade>
         <!--Optional:-->
         <tem:updateDate>2023/05/10 00:00:00</tem:updateDate>
         <!--Optional:-->
         <tem:userCode>ecc.hhups.tp.edu.tw</tem:userCode>
      </tem:GetData>
   </soapenv:Body>
</soapenv:Envelope>");
            return soapEnvelopeDocument;


        }

        private static HttpWebRequest CreateSoapRequest(string url, string action)
        {
            var webRequest = (HttpWebRequest)WebRequest.Create(url);

            webRequest.Headers.Add("SOAPAction", action);
            webRequest.Headers.Add("Accept-Encoding", "gzip,deflate");
            webRequest.ContentType = "text/xml;charset=\"utf-8\"";
            webRequest.Accept = "text/xml";
            webRequest.ContentLength = 546;
            webRequest.Method = "POST";
            webRequest.UserAgent = "Apache-HttpClient/4.5.5 (Java/16.0.1)";
            NetworkCredential credential = new NetworkCredential("", "");
            webRequest.Credentials = credential;
            webRequest.KeepAlive = true;
            webRequest.UseDefaultCredentials = true;
            return webRequest;
        }
        private static void InsertSoapEnvelopeIntoSoapRequest(XmlDocument soapEnvelopeXml, HttpWebRequest webRequest)
        {
            using (Stream stream = webRequest.GetRequestStream())
            {
                soapEnvelopeXml.Save(stream);

            }
        }
        public ActionResult PortalIndex1(string school_type, string school_code, string role_type, string uuid, string access_token)
        {
            log4net.ILog logger = LogManager.GetLogger(" PortalIndex 親師生");
            logger.Info("uuid：" + uuid);
            logger.Info("role_type：" + role_type);
            logger.Info("school_code：" + school_code);
            logger.Info("school_type：" + school_type);
            logger.Info("access_token Home：" + access_token);
            //if (!string.IsNullOrEmpty(school_type) || !string.IsNullOrEmpty(access_token))
            //{

            //    return RedirectToAction("SSOBACK", "COOC", new { access_token= access_token });
            //}

            ViewBag.school_type = school_type;
            ViewBag.school_code = school_code;
            ViewBag.role_type = role_type;
            ViewBag.uuid = uuid;
            ViewBag.access_token = access_token;
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ViewBag.ECOOLEXPLAIN = db.HRMT04.FirstOrDefault().ECOOLEXPLAIN;
            }
            BuildPortalHomeModels();
            //  exservice.GetCardNumberSchedual();
            this.RouteData.Values["school"] = null;

            return View();
        }
        public ActionResult PortalIndex(string school_type, string school_code, string role_type, string uuid, string access_token)
        {
            log4net.ILog logger = LogManager.GetLogger(" PortalIndex 親師生");
            logger.Info("uuid：" + uuid);
            logger.Info("role_type：" + role_type);
            logger.Info("school_code：" + school_code);
            logger.Info("school_type：" + school_type);
            logger.Info("access_token Home：" + access_token);
            //if (!string.IsNullOrEmpty(school_type) || !string.IsNullOrEmpty(access_token))
            //{

            //    return RedirectToAction("SSOBACK", "COOC", new { access_token= access_token });
            //}

            if (!string.IsNullOrEmpty(access_token)) {

                ViewBag.access_token = "T";

            }
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ViewBag.ECOOLEXPLAIN = db.HRMT04.FirstOrDefault().ECOOLEXPLAIN;
            }
            BuildPortalHomeModels();
            //  exservice.GetCardNumberSchedual();
            this.RouteData.Values["school"] = null;

            return View();
        }
        public ActionResult test() {
            CERI02Service cre = new CERI02Service();
            cre.sendMERAGE_HRMT01_SQLMail();
            return View();
        }
            
            public ActionResult COOCGuestIndex(string Message)
        {
            if (Message != null)
            {
                TempData["StatusMessage"] = Message;
                return View();
            }

            //BuildGuestHomeModels(school);
            //UserProfileHelper.Set(school);

            //using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            //{
            //    ViewBag.SchoolExplain = db.BDMT01.Where(p => p.SCHOOL_NO == school).FirstOrDefault().EXPLAIN;
            //}

            return View();
        }
      
        public ActionResult ParsentSSO() {
            LoginAppLoginViewModel loginAppLoginViewModel = new LoginAppLoginViewModel();
            try
            {
                List<HRMT01> hRMT01s = new List<HRMT01>();
                   hRMT01s = TempData["list"] as List<HRMT01>;


          
                // List<HRMT01> hRMT01s = new List<HRMT01>();
                ECOOL_DEVEntities db = new ECOOL_DEVEntities();
                //  HRMT01 test1 = new HRMT01();
                // HRMT01 test2 = new HRMT01();
                // test1 = db.HRMT01.Where(x => x.SCHOOL_NO == "111111" && x.USER_NO == "107038").FirstOrDefault();
                //test2 = db.HRMT01.Where(x => x.SCHOOL_NO == "111111" && x.USER_NO == "101044").FirstOrDefault();
                List<SelectListItem> Hrmt01Items = new List<SelectListItem>();
                // hRMT01s.Add(test1);
                //hRMT01s.Add(test2);
                loginAppLoginViewModel.UserNo = hRMT01s.Select(x => x.USER_NO).FirstOrDefault() + "-" + hRMT01s.Select(x => x.SCHOOL_NO).FirstOrDefault();
                foreach (var item in hRMT01s)
                {
                    BDMT01 bDMT01 = new BDMT01();

                    string SCHOOLNAME = "";
                    SCHOOLNAME = db.BDMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO).Select(x => x.SHORT_NAME).FirstOrDefault();
                    Hrmt01Items.Add(new SelectListItem() { Text = SCHOOLNAME + "-" + item.NAME, Value = item.SCHOOL_NO + "-" + item.USER_NO });


                }
                ViewBag.GradeItem = Hrmt01Items;
                return View(loginAppLoginViewModel);
            }
            catch (Exception e) {
                logger.Info("e" + e.InnerException);
            }
            return View(loginAppLoginViewModel);
        }
        public ActionResult Parsentredit(LoginAppLoginViewModel loginAppLoginViewModel)
        {

            string[] strli = new string[2];

            strli= loginAppLoginViewModel.UserNo.Split('-');
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            string str1 = "";
            string str2 = "";
            str1 = strli[0];
            str2 =strli[1];
            HRMT01 rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == str1 && x.USER_NO == str2).FirstOrDefault();
            if (rMT01 != null)
            {
                UserProfile LoginUser = UserProfile.FillUserProfile(rMT01);
                UserProfileHelper.Set(LoginUser);
                LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");
                return RedirectToAction("ParentLadp", "HOME", new { school = rMT01.SCHOOL_NO });
            }
            else {
                TempData["StatusMessageCenter"] = "很抱歉，此帳號無法登入。";
                 return RedirectToAction("COOCGuestIndex", "HOME", new { Message = "該帳號不在系統中，請聯絡相關人員!" });
            }

        }
        public ActionResult Epay()
        {
            delCookie("MyLoginwebsite");
            delCookie("XSRF-TOKEN", "ldap.tp.edu.tw");
            delCookie("laravel_session", "ldap.tp.edu.tw");
            delCookie("laravel_token", "ldap.tp.edu.tw");
            return Redirect(@"https://ldap.tp.edu.tw/api/v2/logout?redirect=https://epay.tp.edu.tw/ePay/Account/LoginSelect");
        }

        public static bool delCookie(string strName, string Domain = "")
        {
            try
            {
                HttpCookie Cookie = new HttpCookie(strName);
                Cookie = System.Web.HttpContext.Current.Request.Cookies[strName];
                if (Cookie != null)
                {
                    Cookie.HttpOnly = true;
                    Cookie.Expires = DateTime.Now.AddYears(-1);

                    if (!string.IsNullOrWhiteSpace(Domain))
                    {
                        Cookie.Domain = Domain;
                    }

                    System.Web.HttpContext.Current.Response.SetCookie(Cookie);
                }
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        //訪客首頁
        public ActionResult GuestIndex(string school)
        {
            if (school == null)
            {
                return RedirectToAction("PortalIndex");
            }

            BuildGuestHomeModels(school);
            UserProfileHelper.Set(school);

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ViewBag.SchoolExplain = db.BDMT01.Where(p => p.SCHOOL_NO == school).FirstOrDefault().EXPLAIN;
            }

            return View();
        }
        public ActionResult ParentLadp()
        {
            UserProfile user = UserProfileHelper.Get();
            if (user == null) return RedirectToAction("Logout");

            BuildGuestHomeModels(user.SCHOOL_NO);
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ViewBag.SchoolExplain = db.BDMT01.Where(p => p.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault().EXPLAIN;
            }

            return View("GuestIndex");
        }
        public ActionResult ParentIndex()
        {
            UserProfile user = UserProfileHelper.Get();
            if (user == null) return RedirectToAction("Logout");

            BuildGuestHomeModels(user.SCHOOL_NO);
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ViewBag.SchoolExplain = db.BDMT01.Where(p => p.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault().EXPLAIN;
            }

            return View("GuestIndex");
        }
        [AllowAnonymous]
        public ActionResult fadoUpload(HttpPostedFileBase UploadCoverFile)
        {
          
            return View();
        }
        [HttpPost]
        public ActionResult fadoUpload1(HttpPostedFileBase UploadCoverFile)
        {
            ZZZI34Service zZZI = new ZZZI34Service();
            HttpPostedFileBase UploadCoverFile1 = Request.Files["UploadCoverFile"];
            string mesg = "";
            if (UploadCoverFile?.ContentLength > 0)
            {
                zZZI.UpLoadfodoFile(UploadCoverFile, ref mesg);
            }
            return Redirect("https://ecc.tp.edu.tw/EcoolWeb/fodo/");
        }
        /// <summary>
        /// Web Toast已讀呼叫這支
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult WebToastUpRead(string SCHOOL_NO, string USER_NO, int NOTIFICATION_ID)
        {
            int UpNum = 0;
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                var Up = (from a in db.APPT02
                          where a.NOTIFICATION_ID == NOTIFICATION_ID && a.STATUS == APPT02.StatusVal.UnRead
                          select a).FirstOrDefault();

                if (Up != null)
                {
                    Up.STATUS = APPT02.StatusVal.Read;
                    UpNum = UpNum + 1;
                }
                db.SaveChanges();
            }
            return Json(UpNum);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Login()
        {
            string SchoolNo = Request["txtSCHOOL_NO"];
            string UserNo = Request["txtUSER_NO"];
            string Password = Request["txtPASSWORD"];
            string WhereAWARD_NO = Request["WhereAWARD_NO"];
            string ChildMonthIndex = Request["ChildMonthIndex"] ?? "";
            string returnURL = Request["returnURL"] ?? "";
            if (!ModelState.IsValid)
            {
                // 回傳表單含錯誤訊息
                return RedirectToAction("GuestIndex", new { school = SchoolNo });
            }
            //string Mode = Request["Mode"] ?? "";
            //string Rule = Request["Rule"] ?? "";
            //string APPLY_NO = Request["APPLY_NO"] ?? "";
            //string BackAction = Request["BackAction"] ?? "";
            //string whereAPPLY_STATUS = Request["whereAPPLY_STATUS"] ?? "";
            //string OrdercColumn = Request["OrdercColumn"] ?? "";
            //string Page = Request["Page"] ?? "";
            if (SharedGlobal.HomeIndex == "ChildMonthIndex" && string.IsNullOrWhiteSpace(ChildMonthIndex))
            {
                ChildMonthIndex = SharedGlobal.Y;
            }
          
            string Message;

            HRMT01 FindUser = CheckLogin(SchoolNo, UserNo, Password, out Message);

            if (FindUser == null)
            {
                TempData["StatusMessage"] = Message;
                LogHelper.AddLogToDB(SchoolNo, UserNo, System.Web.HttpContext.Current.Request.UserHostAddress, "登入首頁失敗", "LoginFail");

                if (ChildMonthIndex == SharedGlobal.Y)
                {
                    return RedirectToAction("ChildMonthIndex", new { school = SchoolNo });
                }
                else if (string.IsNullOrWhiteSpace(WhereAWARD_NO))
                {
                    return RedirectToAction("GuestIndex", new { school = SchoolNo });
                }
                else
                {
                    return RedirectToAction("LoginProduct", "BarcCodeMyCash", new { txtSCHOOL_NO = SchoolNo, txtUSER_NO = UserNo, WhereAWARD_NO = WhereAWARD_NO });
                }
            }
           
            //填入UserProfile
            UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
            UserProfileHelper.Set(LoginUser);

            ////LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, Request.Params["REMOTE_ADDR"].ToString(), "HOME", "LoginSuccess");
            ////LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, Request.UserHostName, "HOME", "LoginSuccess");
            ////LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, Request.UserHostAddress, "HOME", "LoginSuccess");
            LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "登入首頁成功", "LoginSuccess");

            //string next = NextAction(LoginUser);
            //return RedirectToAction(next, "Home", new { school = SchoolNo });
            if (ChildMonthIndex == SharedGlobal.Y)
            {
                return RedirectToAction("ChildMonthIndex");
            }
            else if (User != null)
            {
                string ActionName = string.Empty, ControllerName = string.Empty;
                NextAction(LoginUser, out ActionName, out ControllerName, returnURL);
                var str = Request.UrlReferrer.ToString();
                if (!string.IsNullOrWhiteSpace(WhereAWARD_NO)) {

                    AWAI01ExchangeViewModel Qmode = new AWAI01ExchangeViewModel()
                    {
                        Search = new AWAI01SearchViewModel()
                        {
                            WhereAWARD_NO = Convert.ToInt16(WhereAWARD_NO),
                            WhereSouTable = AWAI01SearchViewModel.SouTableVal.Student,
                            SouController = "BarcCodeMyCash",
                            SouAction = "Index4",
                            BackAction = "AwatQ02",
                            BackController = "AWAI01",
                            WhereSCHOOL_NO = SchoolNo,
                        }
                    };

                    TempData["Qmode"] = Qmode;

                    return RedirectToAction("AwatExchange02", "AWAI01");

                }
                if (str.IndexOf("Mode=Edit") > 0)
                {
                    return Redirect(str);
                }
                else if (returnURL.IndexOf("Mode=Edit") > 0)
                {
                    return Redirect(returnURL);
                }
                else
                {

                    if (ActionName != "TeacherIndex" && ActionName != "StudentIndex" && ActionName != "StudentLadp")
                    {
                       
                        UserProfile userreal = UserProfileHelper.Get();
                        int LoginCount = ADDT03Service.UPLoginCount(userreal.USER_NO, userreal.SCHOOL_NO);
                    }
                    return RedirectToAction(ActionName, ControllerName, new { school = SchoolNo });


                }
            }
            else if (string.IsNullOrWhiteSpace(WhereAWARD_NO))
            {
                string ActionName = string.Empty, ControllerName = string.Empty;
                NextAction(LoginUser, out ActionName, out ControllerName);

                if (ActionName == "Edit_last" && ControllerName == "ZZZI28")
                {
                    return RedirectToAction(ActionName, ControllerName, new { STUDENT_USER_NO = LoginUser.USER_NO.Substring(1) });
                }
                else if (ActionName == "MODIFY" && ControllerName == "ZZT08")
                {
                    return RedirectToAction(ActionName, ControllerName);
                }

                return RedirectToAction(ActionName, ControllerName, new { school = SchoolNo });
            }
            else
            {
                AWAI01ExchangeViewModel Qmode = new AWAI01ExchangeViewModel()
                {
                    Search = new AWAI01SearchViewModel()
                    {
                        WhereAWARD_NO = Convert.ToInt16(WhereAWARD_NO),
                        WhereSouTable = AWAI01SearchViewModel.SouTableVal.Student,
                        SouController = "BarcCodeMyCash",
                        SouAction = "Index",
                        BackAction = "AwatQ02",
                        BackController = "AWAI01",
                        WhereSCHOOL_NO = SchoolNo,
                    }
                };

                TempData["Qmode"] = Qmode;

                return RedirectToAction("AwatExchange02", "AWAI01");
            }
        }

        public ActionResult GenderQRCode()
        {
            return View();
        }

        [HttpPost]
        public ActionResult GenderQRCode(string SchoolNo, string UserNo, string sPwd)
        {
            string str = string.Empty;
            Uri contextUri = System.Web.HttpContext.Current.Request.Url;

            var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
            contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port) + System.Web.HttpContext.Current.Request.ApplicationPath;
            str = baseUri + @"/Home/QRCodeLoginPage?SchoolNo=" + SchoolNo + "&UserNo=" + UserNo + "&sPwd=" + sPwd;
            return RedirectToAction("Cre", "Barcode", new { Value = str });
        }

        public ActionResult QRCodeLoginPage(string SchoolNo, string UserNo, string sPwd)
        {
            string msg = "";

            HRMT01 FindUser = CheckLogin(SchoolNo, UserNo, sPwd, out msg);
            UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
            UserProfileHelper.Set(LoginUser);
            string ActionName = string.Empty, ControllerName = string.Empty;
            NextAction(LoginUser, out ActionName, out ControllerName);

            return RedirectToAction("Index", "User", new { school = SchoolNo });

            //return View();
        }

        [HttpPost]
     
        public ActionResult CheckUserStatus(string SchoolNo, string UserNo, string sPwd, string InputCode, bool CheckCode = false)
        {
            string ReValue = string.Empty;
            int iLockCount = (Session["LockCount"] != null) ? Convert.ToInt32(Session["LockCount"]) : 0;
            int iVlidatePasswordMaxCount = Convert.ToInt32(System.Web.Configuration.WebConfigurationManager.AppSettings["VlidatePasswordMaxCount"]);
            int IsInputCode = 0;

            try
            {
                //DateTime MaintanceSDate = Convert.ToDateTime(System.Web.Configuration.WebConfigurationManager.AppSettings["MaintanceSDate"]);
                //DateTime MaintanceEDate = Convert.ToDateTime(System.Web.Configuration.WebConfigurationManager.AppSettings["MaintanceEDate"]);

                using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                {
                    BDMT01 bdmt01 = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNo).FirstOrDefault();

                    DateTime MaintanceSDate = Convert.ToDateTime(bdmt01.MaintanceSDate);
                    DateTime MaintanceEDate = Convert.ToDateTime(bdmt01.MaintanceEDate);

                    IQueryable<ZZT08> ltzzt08 = db.ZZT08.Where(p => p.SCHOOL_NO == SchoolNo && p.USER_NO == UserNo && p.PASSWORD == sPwd);
                    HRMT01 hrmt01 = db.HRMT01.Where(p => p.SCHOOL_NO == SchoolNo && p.USER_NO == UserNo).FirstOrDefault();
                    var Captcha = (Session["Captcha"] != null) ? Session["Captcha"].ToString().ToUpper() : "";

                    if ((InputCode.ToUpper() ?? "") != Captcha && iLockCount >= iVlidatePasswordMaxCount && CheckCode == true)
                    {
                        ReValue = "驗証碼輸入錯誤";
                    }
                    else if (hrmt01 == null)
                    {
                        ReValue = "找不到帳號";
                    }
                    else if (MaintanceSDate <= DateTime.Now && MaintanceEDate >= DateTime.Now && hrmt01.USER_TYPE != UserType.Admin)
                    {
                        ReValue = "目前為資料維護期間，平臺暫停使用。暫停時間：" + string.Format("{0:D}", MaintanceSDate) + "至" + string.Format("{0:D}", MaintanceEDate) + "。若有問題，請洽學校老師。";
                    }
                    else if (hrmt01.USER_STATUS == UserStaus.Lock)
                    {
                        ReValue = "該帳號已鎖定，請聯絡學校管理老師";
                    }
                    else if (hrmt01.USER_STATUS == UserStaus.Disable)
                    {
                        ReValue = "該帳號已停用";
                    }
                    else if (hrmt01.USER_STATUS == UserStaus.Invalid)
                    {
                        ReValue = "該帳號已失效";
                    }
                    else
                    {
                        bool NG = true;

                        if (ltzzt08.Count() > 0)
                        {
                            if (ltzzt08.FirstOrDefault().PASSWORD == sPwd)
                            {
                                Session["LockCount"] = 0;
                                NG = false;
                            }
                        }

                        if (hrmt01.USER_TYPE == UserType.Parents) //家長
                        {
                            //家長第一次登入(未綁定任何學生前)
                            if (HRMT06.CkPanyStudent(SchoolNo, UserNo, db) == false)
                            {
                                //需用學生密碼登入
                                ltzzt08 = db.ZZT08.Where(p => p.SCHOOL_NO == SchoolNo && p.USER_NO == UserNo.Substring(1) && p.PASSWORD == sPwd);

                                if (ltzzt08.Count() > 0)
                                {
                                    if (ltzzt08.FirstOrDefault().PASSWORD == sPwd)
                                    {
                                        Session["LockCount"] = 0;
                                        NG = false;
                                    }
                                    else
                                    {
                                        NG = true;
                                    }
                                }
                                else
                                {
                                    NG = true;
                                }
                            }
                        }

                        if (NG)
                        {
                            iLockCount++;
                            Session["LockCount"] = iLockCount;

                            if (Session["LockCount"] != null && iLockCount < 5)
                            {
                                ReValue = "登入錯誤次數達到" + Session["LockCount"] + "次，‧注意:密碼會區分大小寫";
                            }

                            if (iLockCount >= iVlidatePasswordMaxCount && hrmt01.USER_TYPE != UserType.Parents)
                            {
                                if (CheckCode == true)
                                {
                                    ReValue = "登入錯誤次數達到" + Session["LockCount"] + "次，‧注意:密碼會區分大小寫，再次登入時需輸入驗証碼";
                                    IsInputCode = 1;
                                }
                                else
                                {
                                    HRMT01 hr01 = new HRMT01();
                                    hr01 = db.HRMT01.Where(p => p.SCHOOL_NO == SchoolNo && p.USER_NO == UserNo).FirstOrDefault();
                                    hr01.USER_STATUS = UserStaus.Lock;
                                    db.Entry(hr01).State = EntityState.Modified;
                                    db.SaveChanges();
                                    ReValue = "登入錯誤次數達到" + iLockCount + "次該帳號已鎖定";
                                }
                            }
                        }
                    }
                }
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
            }

            var data = "{ \"ReValue\" : \"" + ReValue + "\" , \"IsInputCode\" : \"" + IsInputCode.ToString() + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        //老師首頁
        public ActionResult TeacherIndex()
        {
            ZZZI20Controller zZZI20 = new ZZZI20Controller();
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities();
            int TaipeiSchOOLCount = 0;
            TaipeiSchOOLCount = db.BDMT01.Where(x => x.SCHOOL_NO == SchoolNo && x.CITY == "臺北市").Count();
            if (TaipeiSchOOLCount > 0)
            {
                zZZI20.SycTurnIn();
                zZZI20.SycTurnInEnivalid();
              
            }
               UserProfile user = UserProfileHelper.Get();
         
            if (user == null) return RedirectToAction("Logout");

            if (user.USER_TYPE != UserType.Teacher && user.USER_TYPE != UserType.Admin)
                return RedirectToAction("GuestIndex", UserProfileHelper.GetSchoolNo());
           
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db);
            //using (ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities())
            //{
            ViewBag.BET02List = db.BET02.Include("BET02_LANG").
           Where(a => a.S_DATE <= DateTime.Today && a.E_DATE > DateTime.Today && a.ISPUBLISH == "Y" &&
                   (a.CLASS_TYPE == "1" || (a.SCHOOL_NO == SchoolNo && a.CLASS_TYPE == "2"))).
                       OrderByDescending(a => a.TOP_YN).
                           ThenByDescending(a => a.S_DATE).ThenByDescending(a => a.BULLET_ID).Take(5).ToList();

            // -- 轉學生酷幣轉匯 --
            ViewBag.ShowTurnInCount = 0;
            List<HRMV01> TurnInList = new List<HRMV01>();
            //先找出該校有多少轉入學生
            var Students = db.HRMV01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO);
            //篩選還沒處理的
            foreach (HRMV01 s in Students)
            {
                if (s.USER_STATUS != UserStaus.Disable) continue;
                if (s.INIT_STATUS != 0) continue;

                AWAT01 UserCash = db.AWAT01.Where(uu => uu.SCHOOL_NO == s.SCHOOL_NO && uu.USER_NO == s.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    if (UserCash.CASH_ALL > 0) continue;
                }
                TurnInList.Add(s);
            }
            ViewBag.ShowTurnInCount = TurnInList.Count;
            // -- --

            // -- 檢查點數增加異常: 各校管理者以上權限才可看到 --
            string[] exclude_DESC = new string[] { "取消獎品兌換", "定存", "銀行定存到期解約" , "銀行定存解約，本金", "銀行定存", "111年兒童節，家長會贈送", "111年麗山國小兒童節家長會贈點" }; // 排除的細項
            if (Int32.Parse(user.RoleID_Default) == (int)UserRoles.各校管理者 || (Int32.Parse(user.RoleID_Default) < (int)UserRoles.各校管理者 && user.ROLE_TYPE != 3))
            {
                DateTime todayDate = DateTime.Now.Date;
                DateTime todayEnd = DateTime.Now.Date.AddDays(1);
                var alertWhos = db.AWAT01_LOG
                    .Join(db.HRMT01, a => new { a.SCHOOL_NO, a.USER_NO }, h => new { h.SCHOOL_NO, h.USER_NO },
                    (a, h) => new { a.SCHOOL_NO, a.USER_NO, a.LOG_TIME, a.CASH_IN, h.CLASS_NO, h.SEAT_NO, a.LOG_DESC,a.SOURCE_TYPE })
                    .Where(l => l.SCHOOL_NO == SchoolNo && l.LOG_TIME >= todayDate && l.LOG_TIME <= todayEnd && l.CASH_IN > 0
                    && !exclude_DESC.Contains(l.LOG_DESC) && l.SOURCE_TYPE!= "AWAT10")
                    .GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.CASH_IN })
                    .Select(g => new
                    {
                        g.FirstOrDefault().USER_NO,
                        g.FirstOrDefault().CLASS_NO,
                        g.FirstOrDefault().SEAT_NO,
                        CachAddToday = g.Sum(a => a.CASH_IN)
                    })
                    .Where(s => s.CachAddToday > 500).ToList();

                ViewBag.alertWhos = alertWhos.Select(w => new { w.USER_NO, w.CLASS_NO, w.SEAT_NO, w.CachAddToday }).Distinct().ToArray();
                if (alertWhos.Count() > 0)
                {
                    int labnormaCount = 0;
                    labnormaCount = db.AWA01_labnormalog.Where(x => x.StartDate == todayDate && x.SCHOOL_NO== user.SCHOOL_NO).Count();
                    if (labnormaCount == 0)
                    {
                        AWA01_labnormalog aWA = new AWA01_labnormalog();
                        aWA.BATCH_KEY = Guid.NewGuid().ToString("N");
                        aWA.StartDate = todayDate;
                        aWA.EndDate = todayEnd;
                        aWA.SCHOOL_NO = user.SCHOOL_NO;
                       
                        db.AWA01_labnormalog.Add(aWA);
                    }
                    db.SaveChanges();
                }
            }
            //}
            int LoginCount = 0;
            LoginCount = ADDT03Service.UPLoginCount(user.USER_NO, user.SCHOOL_NO);
            return View();
        }
        
        public ActionResult StudentIndexTaskList()
        {
            return View();
        }

        public ActionResult _StudentTaskList()
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            string USER_KEY = user.USER_KEY;
            ECOOL_APP.EF.ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities();
            HrmtImportService hrmtImportService = new HrmtImportService();
            List<ADDT11> ADDT11Temp = new List<ADDT11>();

            ADDI12Service ADDI12Service = new ADDI12Service();
            List<ADDI10IndexListViewModel> ADDI10IndexListViewModelTemp = new List<ADDI10IndexListViewModel>();
            //有獎徵答
            ADDT11Temp = hrmtImportService.CountADDT11Student(SchoolNo, user.USER_NO);
            ViewBag.ADDT11Temp = ADDT11Temp?.Count();
            //投票
            ADDI10IndexListViewModelTemp = hrmtImportService.CountADDI10List(USER_KEY, SchoolNo, user.USER_NO);
            ViewBag.ADDI10IndexListViewModelTemp = ADDI10IndexListViewModelTemp?.Count();
            //小小舞台
            int? PremierCount = 0;
            int? AllVideoCount = 0;
            PremierCount = ADDI12Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            AllVideoCount = ADDI12Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, false, ref db);
            ViewBag.PremierCount = PremierCount ?? 0 + AllVideoCount ?? 0;

            ViewBag.NoteReadLevel = user.NoteReadLevel;
            ViewBag.NotePassGrade = user.NotePassGrade;
            ViewBag.Chance_BIRTHDAY = user.Chance_BIRTHDAY ? "1" : "0";
            return PartialView();
        }

        public ActionResult _TaskList()
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            int Hrt25Count = 0;
            ECOOL_APP.EF.ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities();
            Hrt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && (x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Library) || x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Read))).Count();
          
            if (!string.IsNullOrWhiteSpace(user.SCHOOL_NO) && !string.IsNullOrWhiteSpace(user.USER_NO))
            {
                var hrm = db.HRMT01.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                user.TEACH_CLASS_NO = hrm.CLASS_NO;
            }
            var ADDT06List = from a06 in db.ADDT06
                             join h01 in db.HRMT01
                                 on new { a06.SCHOOL_NO, a06.USER_NO }
                                 equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                             from h01 in h01_join.DefaultIfEmpty()
                             where a06.SCHOOL_NO == user.SCHOOL_NO && a06.APPLY_STATUS == "0" && (!UserStaus.NGKeyinUserStausList.Contains(h01.USER_STATUS))
                             select a06;
            //  IQueryable<ADDT06> ADDT06List = db.ADDT06.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.APPLY_STATUS == "0");

            if ((!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO)&& Hrt25Count == 0) || PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_CheckPendingDetail", user.SCHOOL_NO, user.USER_NO) == "N")
            {
               
                ADDT06List = ADDT06List.Where(a => a.CLASS_NO == user.TEACH_CLASS_NO);
              
            }

            ViewBag.ADDT06Cnt = ADDT06List.Count();
            ViewBag.ADDTHrmt25 = Hrt25Count;
            var AWAT15List =  (
                  from aTemp in db.AWAT15
                  join awat01 in db.AWAT01 
                  
                  on new { aTemp.SCHOOL_NO, aTemp.USER_NO } equals new { awat01.SCHOOL_NO, awat01.USER_NO }
                 
              
               where aTemp.SCHOOL_NO== user.SCHOOL_NO &&aTemp.IsReMark == (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_UN && aTemp.CASH_Rank != 0
                  select new VAWAT15
                  {

                      SCHOOL_NO = aTemp.SCHOOL_NO,
                      USER_NO = aTemp.USER_NO,
                      CASH_Rank = aTemp.CASH_Rank,
                      CreatDate = DateTime.Now,
                      IsReMark = aTemp.IsReMark,
                      GOT_DATE = aTemp.GOT_DATE,
                      CANCEL_DATE = aTemp.CANCEL_DATE,
                      AWAT15NO = aTemp.AWAT15NO,
                      SYEAR = aTemp.SYEAR,
                      SEMESTER = aTemp.SEMESTER,
                      CLASS_NO = aTemp.CLASS_NO,
                      SEAT_NO = aTemp.SEAT_NO,
                      NAME = aTemp.NAME,
                      SNAME = aTemp.SNAME,
                      MEMO = aTemp.MEMO,
                      AWAT14_CASH = aTemp.AWAT14_CASH,
                      CASH_ALL = (int)awat01.CASH_ALL,
                    
                      // Other properties might be assigned here if available in the VAWAT15 class
                  }
                         )


                         .AsQueryable();
          
            ViewBag.awat15Count = AWAT15List.Count();
           //線上投稿待批閱數量
           IQueryable < ADDT01 > ADDT01List = db.ADDT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO &&
             (a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Draft || a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.TurnVerify));
            if ((!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO)&& Hrt25Count==0) || PermissionService.GetPermission_Use_YN("ADDI01", "Verify", user.SCHOOL_NO, user.USER_NO) == "N")
            {

                
                ADDT01List = ADDT01List.Where(a => a.VERIFIER == user.USER_NO);
         
            }
            
            ViewBag.ADDT01Qty = ADDT01List.Count();

            //線上藝廊待批閱數量

            string sSQL = @"Select a.*, '" + UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI34/QRCODEUrl?WhereART_GALLERY_NO=" + "'+ a.ART_GALLERY_NO as QRCODEGARY , '" + UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI34/QRCODEUrl?PHOTO_USER_NO=" + "' + a.USER_NO +" + $@"'&PHOTO_SCHOOL_NO='+ a.SCHOOL_NO as QRCODEGARYPHOTO ";
            sSQL = sSQL + @", WorkCount=(SELECT COUNT(*) FROM ADDT22 b (NOLOCK) Where B.ART_GALLERY_NO =a.ART_GALLERY_NO and b.PHOTO_STATUS=@PHOTO_STATUS)
                            from ADDT21 a (nolock)
                            where a.SCHOOL_NO =@SCHOOL_NO and (SELECT COUNT(*) FROM ADDT22 b (NOLOCK) Where B.ART_GALLERY_NO =a.ART_GALLERY_NO and b.PHOTO_STATUS=@PHOTO_STATUS)>0 and STATUS=1";

            IQueryable<ADDT21> ADDT21List = db.ADDT21.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.STATUS == ADDT21.STATUSVal.Verification
           );
            string str = PermissionService.GetPermission_Use_YN("ZZZI34", "Verify", user.SCHOOL_NO, user.USER_NO);
            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) || PermissionService.GetPermission_Use_YN("ZZZI34", "Verify", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                //ADDT21List = ADDT21List.Where(a => a.VERIFIER == user.USER_NO);
                sSQL = sSQL + " and (a.USER_NO = @USER_NO  or isnull(a.VERIFIER,'')=@USER_NO) ";
            
        }
            else if (user.USER_TYPE == "T" && string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && str != "Y")
            {
                sSQL = sSQL + " and a.USER_NO = @USER_NO ";
            }

            var temp = db.Database.Connection.Query<ZZZI34IndexListDataViewModel>(sSQL
             , new
             {
                
                 USER_NO = user.USER_NO,
                 SCHOOL_NO= user.SCHOOL_NO,
                 Verification = ADDT21.STATUSVal.Verification,
                 PHOTO_STATUS = ADDT22.STATUSVal.OK,
                 Pass = ADDT21.STATUSVal.Pass,
                 
                
              
             });
            ViewBag.ADDT21Qty = temp.Count();

            ViewBag.WFT05 = db.WFT05.Count();

            //兌換獎品待頒發
            var VAWA004 = db.VAWA004.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.TRANS_STATUS == ECOOL_APP.EF.VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN);
            ViewBag.VAWA004 = VAWA004.Count();

            //閱讀認證獎狀待頒發
            IQueryable<ADDT0809> TempList = (from a09_his in db.ADDT09_HIS
                                             join a08 in db.ADDT08
                                                   on new { a09_his.LEVEL_ID, a09_his.SCHOOL_NO }
                                               equals new { a08.LEVEL_ID, a08.SCHOOL_NO }
                                             where a09_his.STATUS == 0 && a09_his.SCHOOL_NO == user.SCHOOL_NO
                                             select new ADDT0809
                                             {
                                                 UPNO = a09_his.UPNO.ToString(),
                                                 SYEAR = a09_his.SYEAR.ToString(),
                                                 SEMESTER = a09_his.SEMESTER.ToString(),
                                                 CLASS_NO = a09_his.CLASS_NO,
                                                 SEAT_NO = a09_his.SEAT_NO,
                                                 USERNAME = a09_his.NAME,
                                                 SNAME = a09_his.SNAME,
                                                 BOOK_QTY = a09_his.BOOK_QTY,
                                                 LEVEL_DESC = a08.LEVEL_DESC,
                                                 UP_DATE = a09_his.UP_DATE,
                                                 STATUS = a09_his.STATUS.ToString(),
                                                 SCHOOL_NO = a09_his.SCHOOL_NO,
                                                 USER_NO = a09_his.USER_NO,
                                                 DP_PERSON = a09_his.DP_PERSON,
                                                 DP_DATE = a09_his.DP_DATE
                                             });
            ViewBag.ADDT0809 = TempList.Count();

            ECOOL_APP.com.ecool.service.ZZZI11Service z11Service = new ECOOL_APP.com.ecool.service.ZZZI11Service();
            if (UserProfileHelper.CheckROLE_SCHOOL_ADMIN(user, db))
            {
                //問題反應 待回覆
                int z11Q = z11Service.QCount(user.SCHOOL_NO, user.USER_NO);
                ViewBag.ZZZI11_Q = z11Q;

                //問題反應 待讀取
                int z11A = z11Service.ACount(user.SCHOOL_NO, user.USER_NO);
                ViewBag.ZZZI11_A = z11A;
                ViewBag.LINK_ADDR_ZZZI11_A = z11A > 0 ? "?STATUS=2" : "";

                ECOOL_APP.com.ecool.service.ZZZI31Service z31Service = new ECOOL_APP.com.ecool.service.ZZZI31Service();
                //填報 待回覆
                int z31Q = z31Service.QCount(user.SCHOOL_NO, user.USER_NO);
                ViewBag.ZZZI31_Q = z31Q;

                //填報 待讀取
                int z31A = z31Service.ACount(user.SCHOOL_NO, user.USER_NO);
                ViewBag.ZZZI31_A = z31A;
                ViewBag.LINK_ADDR_ZZZI31_A = z31A > 0 ? "?STATUS=2" : "";
            }

            //轉學生酷幣轉匯
            ViewBag.ShowTurnInCount = 0;
            ViewBag.StudentStatuInCount = 0;
            List<HRMV01> TurnInList = new List<HRMV01>();
            //先找出該校有多少轉入學生
            var Students = db.HRMV01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO);
            ViewBag.StudentStatuInCount = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_STATUS == UserStaus.Disable && x.USER_TYPE == UserType.Student).Count();
            //篩選還沒處理的
            foreach (HRMV01 s in Students)
            {
                if (s.USER_STATUS != UserStaus.Disable) continue;
                if (s.INIT_STATUS != 0) continue;

                AWAT01 UserCash = db.AWAT01.Where(uu => uu.SCHOOL_NO == s.SCHOOL_NO && uu.USER_NO == s.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    if (UserCash.CASH_ALL > 0) continue;
                }

                TurnInList.Add(s);
            }
            ViewBag.ShowTurnInCount = TurnInList.Count;

            //閱讀護照書本是否有設定
            var ADDT03 = db.ADDT03.Where(p => p.SCHOOL_NO == user.SCHOOL_NO);
            ViewBag.ADDT03Count = ADDT03.Count();

            //閱讀護照獎狀頒發
            //取出該校特定年級所有的閱讀資料
            List<uADDT05Q03TMP> liAD05Q03TMP =
            (from ad05 in db.ADDT05
             join h01 in db.HRMT01 on new { ad05.USER_NO, ad05.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
             from h01 in h01_join.DefaultIfEmpty()
             where ad05.SCHOOL_NO == user.SCHOOL_NO && ad05.BOOK_ID.Substring(0, 1) == user.GRADE.ToString()
             orderby h01.CLASS_NO, h01.SEAT_NO
             select new uADDT05Q03TMP
             {
                 USER_NO = ad05.USER_NO,
                 CLASS_NO = h01.CLASS_NO,
                 SEAT_NO = h01.SEAT_NO,
                 NAME = h01.SNAME,
                 BOOK_ID = ad05.BOOK_ID
             }).ToList();

            int WaitHandleBook = 0;

            if (liAD05Q03TMP.Count > 0)
            {
                //取出書本數
                ADDT18 ltA18 = db.ADDT18.Where(p => p.GRADE == user.GRADE).FirstOrDefault();
                if (ltA18 != null)
                {
                    int? iGRADEBookCount = ltA18.BOOK_SCCOUNT + ltA18.BOOK_SECOUNT;
                    for (int i = 0; i < liAD05Q03TMP.Count; i++)
                    {
                        int iUserReadBookCount = liAD05Q03TMP.Where(p => p.USER_NO == liAD05Q03TMP[i].USER_NO).Count();
                        if (iUserReadBookCount == iGRADEBookCount)
                        {
                            WaitHandleBook++;
                        }
                    }
                }
            }
            ViewBag.WaitHandleBook = WaitHandleBook;

            //獎品尚未設定請設定
            var AWAT02 = db.AWAT02.Where(p => p.SCHOOL_NO == user.SCHOOL_NO || p.SCHOOL_NO == "ALL");
            ViewBag.AWAT02Count = AWAT02.Count();

            return PartialView();
        }

        //學生首頁
        public ActionResult StudentIndex()
        {

          
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities();
            UserProfile user = UserProfileHelper.Get();
            if (user == null) return RedirectToAction("Logout");
            if (user.USER_TYPE != UserType.Student) return RedirectToAction("GuestIndex", UserProfileHelper.GetSchoolNo());
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db);
            List<uADDT03> liADDT03 = new List<uADDT03>();
            int LoginCount = 0;
            liADDT03 = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, user.GRADE.ToString());
           #if release

            LoginCount = ADDT03Service.UPLoginCount(user.USER_NO, user.SCHOOL_NO);
            #endif
            BuildStudentHomeModels(user.SCHOOL_NO);

            return View();
        }
        //學生首頁
        public ActionResult StudentLadp()
        {


            string SchoolNo = UserProfileHelper.GetSchoolNo();
            ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities();
            UserProfile user = UserProfileHelper.Get();
            if (user == null) return RedirectToAction("Logout");
            if (user.USER_TYPE != UserType.Student) return RedirectToAction("GuestIndex", UserProfileHelper.GetSchoolNo());
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db);
           // List<uADDT03> liADDT03 = new List<uADDT03>();
            int LoginCount = 0;
          //liADDT03 = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, user.GRADE.ToString());
            LoginCount = ADDT03Service.UPLoginCount(user.USER_NO, user.SCHOOL_NO);
            BuildStudentHomeModels(user.SCHOOL_NO);

            return View();
        }

        //家長首頁
        public ActionResult ParentsIndex()
        {
            UserProfile user = UserProfileHelper.Get();
            if (user == null) return RedirectToAction("Logout");

            if (user.USER_TYPE != UserType.Parents || HRMT06.CkPanyStudent(user.SCHOOL_NO, user.USER_NO) == false)
            {
                return RedirectToAction("GuestIndex", UserProfileHelper.GetSchoolNo());
            }
            BuildGuestHomeModels(user.SCHOOL_NO);
            // BuildStudentHomeModels(user.SCHOOL_NO);

            return View();
        }

        public ActionResult Birthday()
        {
            return View();
        }

        public ActionResult BirthdayGet()
        {
            UserProfile LoginUser = UserProfileHelper.Get();
            string BATCH_ID = PushService.CreBATCH_ID();
            int CashIn = 50;
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            // 檢查是否超過時間沒更換密碼
            ZZT08 zzt08 = db.ZZT08.Where(z => z.SCHOOL_NO == LoginUser.SCHOOL_NO && z.USER_NO == LoginUser.USER_NO).FirstOrDefault();
            if (zzt08?.EXP_DATE == null || zzt08?.EXP_DATE < DateTime.Now) // 沒換過或太久沒換密碼
            {
                TempData["StatusMessage"] = "建議更改密碼領取生日禮金；不改密碼也可以領取。";

                return RedirectToAction("MODIFY", "ZZT08", new { redirectController = "Home", redirectAction = "BirthdayGet" });
            }
            //AWAT04
            var find4 = db.AWAT04.Where(u => u.SCHOOL_NO == LoginUser.SCHOOL_NO && u.USER_NO == LoginUser.USER_NO);
            AWAT04 aw4 = find4.FirstOrDefault();
            if (aw4 == null)
            {
                aw4 = db.AWAT04.Create();
                aw4.SCHOOL_NO = LoginUser.SCHOOL_NO;
                aw4.USER_NO = LoginUser.USER_NO;
                db.AWAT04.Add(aw4);
            }

            short thisYear = Convert.ToInt16(DateTime.Today.Year);
            bool CanGet = true;
            if (aw4.BIRTHDAY_YEAR != null)
            {
                if (aw4.BIRTHDAY_YEAR >= thisYear) CanGet = false;
            }
            if (CanGet)
            {
                aw4.BIRTHDAY_YEAR = thisYear;

                ECOOL_APP.CashHelper.AddCash(LoginUser, CashIn, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "BIRTHDAY", DateTime.Today.Year.ToString(), "生日賀禮", true, ref db, "", "",ref valuesList);

                string BODY_TXT = "生日賀禮，獲得酷幣點數" + (CashIn).ToString() + "數";

                PushService.InsertPushDataMe(BATCH_ID, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "", BODY_TXT, "", "Home", "BirthdayGet", DateTime.Today.Year.ToString(), "", false, ref db);

                db.SaveChanges();
            }

            //更新顯示
            UserProfile.RefreshCashInfo(LoginUser, ref db);
            UserProfileHelper.Set(LoginUser);

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

            if (string.IsNullOrWhiteSpace(UUID))
            {
                string next = NextAction(LoginUser);
                return RedirectToAction(next);
            }
            else
            {
                return RedirectToAction("Index", "MobileHome");
            }
        }

        public ActionResult ReadLevelNote()
        {
            UserProfile LoginUser = UserProfileHelper.Get();
            string SchoolNo = UserProfileHelper.GetSchoolNo();
            if (LoginUser == null) return RedirectToAction("GuestIndex", SchoolNo);
            if (LoginUser.NoteReadLevel == 0) return RedirectToAction("GuestIndex", SchoolNo);

            ADDT09_HIS model = null;
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                model = db.ADDT09_HIS.Where(a => a.SCHOOL_NO == LoginUser.SCHOOL_NO && a.USER_NO == LoginUser.USER_NO && a.LEVEL_ID == LoginUser.NoteReadLevel).First();
                ViewBag.ReadLevelCash = db.ADDT07.Find(LoginUser.NoteReadLevel).TO_CASH;
                ViewBag.ReadLEVEL_DESC = db.ADDT08.Where(p => p.LEVEL_ID == model.LEVEL_ID && p.SCHOOL_NO == model.SCHOOL_NO).FirstOrDefault()?.LEVEL_DESC;
            }
            ECOOL_DEVEntities db2 = new ECOOL_APP.EF.ECOOL_DEVEntities();
            ViewBag.GetDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", SchoolNo, ref db2);
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", SchoolNo, ref db2);
            //總召公告是否顯示
            ViewBag.GetALLDataListShowYN = BDMT02Service.GetDataListShow("Home", "TeacherIndex", "ALL", "ALL", ref db2);

            ViewBag.ALLRemindItems = BDMT02Service.GetRefDataList("Home", "TeacherIndex", "ALL", "ALL", ref db2);
            return View(model);
        }

        public ActionResult PassNote()
        {
            return View();
        }

        public ActionResult ReadLevelNoteGet()
        {
            UserProfile LoginUser = UserProfileHelper.Get();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (LoginUser == null) return RedirectToAction("StudentIndex");
            if (LoginUser.NoteReadLevel <= 0) return RedirectToAction("StudentIndex");

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            ADDT07 aADDT07 = db.ADDT07.Find(LoginUser.NoteReadLevel);

            if (aADDT07 != null)
            {
                short cash = aADDT07.TO_CASH.Value;
                short Teachcash = (short)aADDT07.TO_TEACH_CASH.Value;

                //閱讀護照獎勵
                var CashReadLevelList =
                    db.ADDT09_HIS.Where(a => a.SCHOOL_NO == LoginUser.SCHOOL_NO && a.USER_NO == LoginUser.USER_NO && a.CASH_YN.ToUpper() != "Y").OrderBy(a => a.LEVEL_ID);

                ADDT09_HIS CashReadLevel = CashReadLevelList.FirstOrDefault();
                if (CashReadLevel != null)
                {
                    if (CashReadLevel.CASH_YN != "Y")
                    {
                        CashReadLevel.CASH_YN = "Y";

                        //學生
                        CashHelper.AddCash(LoginUser, cash, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "ADDT09", DateTime.Today.Year.ToString(), "閱讀認證升級獎勵", true, ref db,"", "",ref valuesList);

                        string BATCH_ID = PushService.CreBATCH_ID();

                        string BODY_TXT = "閱讀認證升級獎勵，獲得酷幣點數" + (cash).ToString() + "數";

                        PushService.InsertPushDataParents(BATCH_ID, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "", BODY_TXT, "", "Home", "ReadLevelNoteGet", aADDT07.LEVEL_ID.ToString(), "", false, ref db);
                        PushService.InsertPushDataMe(BATCH_ID, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "", BODY_TXT, "", "Home", "ReadLevelNoteGet", aADDT07.LEVEL_ID.ToString(), "", false, ref db);

                        //班導
                        CashHelper.ClassTeachAddCash(LoginUser, Teachcash, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "ADDT09", DateTime.Today.Year.ToString(), "學生閱讀認證升級獎勵", true, ref db);

                        db.SaveChanges();

                        //Push
                        PushHelper.ToPushServer(BATCH_ID);
                    }
                }

                //更新顯示
                UserProfile.RefreshCashInfo(LoginUser, ref db);
                UserProfileHelper.Set(LoginUser);
            }

            string next = NextAction(LoginUser);
            return RedirectToAction(next);
        }

        public ActionResult PassNoteGet()
        {
            UserProfile LoginUser = UserProfileHelper.Get();
            string BATCH_ID = PushService.CreBATCH_ID();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (LoginUser == null) return RedirectToAction("StudentIndex");
            if (LoginUser.NotePassGrade <= 0) return RedirectToAction("StudentIndex");

            short cash = 30;
            if (LoginUser.NotePassGrade >= 3) { cash = 40; }
            if (LoginUser.NotePassGrade >= 5) { cash = 50; }

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            //閱讀護照獎勵
            var CashPassList =
                db.ADDT04.Where(a => a.SCHOOL_NO == LoginUser.SCHOOL_NO && a.USER_NO == LoginUser.USER_NO && a.CASH_YN.ToUpper() != "Y")
                    .OrderBy(a => a.GRADE);
            ADDT04 CashPass = CashPassList.FirstOrDefault();
            if (CashPass != null)
            {
                if (CashPass.CASH_YN != "Y")
                {
                    CashPass.CASH_YN = "Y";

                    ECOOL_APP.CashHelper.AddCash(LoginUser, cash, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "ADDT04", DateTime.Today.Year.ToString(), "閱讀護照獎勵", true, ref db, "", "",ref valuesList);

                    string BODY_TXT = "閱讀護照獎勵，獲得酷幣點數" + (cash).ToString() + "數";

                    PushService.InsertPushDataParents(BATCH_ID, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "", BODY_TXT, "", "ADDI07", "Delete", CashPass.ADDT04_NO.ToString(), "", false, ref db);
                    PushService.InsertPushDataMe(BATCH_ID, LoginUser.SCHOOL_NO, LoginUser.USER_NO, "", BODY_TXT, "", "ADDI07", "Delete", CashPass.ADDT04_NO.ToString(), "", false, ref db);

                    db.SaveChanges();

                    //Push
                    PushHelper.ToPushServer(BATCH_ID);
                }
            }

            //更新顯示
            UserProfile.RefreshCashInfo(LoginUser, ref db);
            UserProfileHelper.Set(LoginUser);

            string next = NextAction(LoginUser);
            return RedirectToAction(next);
        }

        public ActionResult ArrivedChance()
        {
            return View();
        }

        public ActionResult ArrivedChance2()
        {
            return View();
        }

        public ActionResult ArrivedChanceRun()
        {
            int seed = Guid.NewGuid().GetHashCode();
            Random Chance = new Random(seed);

            int CashIn = Chance.Next(1, 6) * 5;

            UserProfile LoginUser = UserProfileHelper.Get();

            string SchoolNO = LoginUser.SCHOOL_NO;
            string UserNO = LoginUser.USER_NO;

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            UserProfile.RefreshCashInfo(LoginUser, ref db);
            //AWAT04
            var find4 = db.AWAT04.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO);
            AWAT04 aw4 = find4.FirstOrDefault();
            if (aw4 == null)
            {
                aw4 = db.AWAT04.Create();
                aw4.SCHOOL_NO = SchoolNO;
                aw4.USER_NO = UserNO;
                db.AWAT04.Add(aw4);
            }
            else
            {
                if ((aw4.ARRIVED_CASH + 100) > LoginUser.CASH_Workhard)
                {
                    return RedirectToAction("StudentIndex");
                }
            }
            int before4 = 0;
            if (aw4.ARRIVED_CASH.HasValue) before4 = aw4.ARRIVED_CASH.Value;
            int After4 = before4 + 100;
            aw4.ARRIVED_CASH = After4;
            aw4.CHG_DATE = DateTime.Now;

            string BATCH_ID = PushService.CreBATCH_ID();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();

            //更新資料
            ECOOL_APP.CashHelper.AddCash(LoginUser, CashIn, SchoolNO, UserNO, "ADDI04", After4.ToString(), "好運集氣抽獎", true, ref db, "", "",ref valuesList);

            string BODY_TXT = "好運集氣抽獎，獲得酷幣點數" + (CashIn).ToString() + "數";

            PushService.InsertPushDataMe(BATCH_ID, SchoolNO, UserNO, "", BODY_TXT, "", "Home", "ArrivedChanceRun", After4.ToString(), "", false, ref db);

            db.SaveChanges();

            //更新顯示
            UserProfile.RefreshCashInfo(LoginUser, ref db);
            UserProfileHelper.Set(LoginUser);

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            ViewBag.ChanceResult = CashIn;

            return View();
        }

        public ActionResult ArrivedChanceRun2()
        {
            int seed = Guid.NewGuid().GetHashCode();
            Random Chance = new Random(seed);

            int CashIn = Chance.Next(1, 6) * 5;

            UserProfile LoginUser = UserProfileHelper.Get();

            string SchoolNO = LoginUser.SCHOOL_NO;
            string UserNO = LoginUser.USER_NO;
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            UserProfile.RefreshCashInfo(LoginUser, ref db);
            //AWAT04
            var find4 = db.AWAT04.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO);
            AWAT04 aw4 = find4.FirstOrDefault();
            if (aw4 == null)
            {
                aw4 = db.AWAT04.Create();
                aw4.SCHOOL_NO = SchoolNO;
                aw4.USER_NO = UserNO;
                db.AWAT04.Add(aw4);
            }
            else
            {
                if ((aw4.ARRIVED_CASH + 100) > LoginUser.CASH_Workhard)
                {
                    return RedirectToAction("StudentIndex");
                }
            }
            int before4 = 0;
            if (aw4.ARRIVED_CASH.HasValue) before4 = aw4.ARRIVED_CASH.Value;
            int After4 = before4 + 100;
            aw4.ARRIVED_CASH = After4;
            aw4.CHG_DATE = DateTime.Now;

            string BATCH_ID = PushService.CreBATCH_ID();

            //更新資料
            ECOOL_APP.CashHelper.AddCash(LoginUser, CashIn, SchoolNO, UserNO, "ADDI04", After4.ToString(), "好運集氣抽獎", true, ref db, "", "", ref valuesList);

            string BODY_TXT = "好運集氣抽獎，獲得酷幣點數" + (CashIn).ToString() + "數";

            PushService.InsertPushDataMe(BATCH_ID, SchoolNO, UserNO, "", BODY_TXT, "", "Home", "ArrivedChanceRun", After4.ToString(), "", false, ref db);

            db.SaveChanges();

            //更新顯示
            UserProfile.RefreshCashInfo(LoginUser, ref db);
            UserProfileHelper.Set(LoginUser);

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            ViewBag.ChanceResult = CashIn;

            return View();
        }

        public ActionResult ArrivedChanceGet()
        {
            UserProfile LoginUser = UserProfileHelper.Get();
            BuildGuestHomeModels(LoginUser.SCHOOL_NO);
            return View("StudentIndex");
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <param name="school"></param>
        /// <returns></returns>
        public ActionResult Logout(string school)
        {
            UserProfile LoginUser = UserProfileHelper.Get();
            if (Session["SSOLOGIN"] != null && Session["SSOLOGIN"].ToString() == "SSOLOGIN")
            {
                Session["SSOLOGIN"] = null;
                if (LoginUser != null)
                {
                    LogHelper.AddLogToDB(school, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] = null;
                }
                string WebUrl = @"https://ecc.tp.edu.tw/EcoolWeb/Home/Logout/" + school;
                return Redirect(@"https://ldap.tp.edu.tw/api/v2/logout?redirect=" + WebUrl);
            }
            if (string.IsNullOrEmpty(school)) school = UserProfileHelper.GetSchoolNo();

            //登出前的學校
            string beforeLogOutSchoolNo = (string)(this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] ?? "");
            if (LoginUser == null && string.IsNullOrEmpty(school) == true) return View("PortalIndex");
            else if (LoginUser != null)
            {
                LogHelper.AddLogToDB(school, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
                this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] = null;
            }

            BuildGuestHomeModels(school);

            if (string.IsNullOrWhiteSpace(LoginUser?.SSO_series ?? "") == false)
            {
                //System.Net.WebClient wc = new System.Net.WebClient();
                //wc.Encoding =System.Text.Encoding.UTF8;
                //wc.Headers[System.Net.HttpRequestHeader.ContentType] = "application/x-www-form-urlencoded";
                if (LoginUser != null)
                {
                    LogHelper.AddLogToDB(school, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] = null;
                }
                string backurl = @"http://ecc.hhups.tp.edu.tw/EcoolWeb/Home/GuestIndex/" + school;
                string input = "series" + LoginUser.SSO_series + "&redirect_uri=" + backurl;

                //string result=  wc.UploadString(@"https://sso.tp.edu.tw/oauth2/logout.do", input);
                return Redirect(@"https://sso.tp.edu.tw/oauth2/logout.do?series" + LoginUser.SSO_series + "&redirect_uri=" + backurl);
            }

            if (SharedGlobal.HomeIndex == "ChildMonthIndex")
            {
                return RedirectToAction(SharedGlobal.HomeIndex, new { school = beforeLogOutSchoolNo });
            }
            else
            {
                return RedirectToAction("GuestIndex", new { school = beforeLogOutSchoolNo });
            }
        }

        public ActionResult _Footer()
        {
            List<BDMT01> CacheBDMT01 = SiteHelper.GetCache("CacheBDMT01") as List<BDMT01>;

            string FooterSchool = string.Empty;
            string FooterSHORT_NAME = string.Empty;
            string FooterUSERNANE = string.Empty;
            string FooterTEL = string.Empty;

            if (CacheBDMT01 == null)
            {
                ECOOL_DEVEntities db = new ECOOL_DEVEntities();

                CacheBDMT01 = db.BDMT01.ToList();

                SiteHelper.SetCache("CacheBDMT01", CacheBDMT01);
            }

            if (CacheBDMT01 != null)
            {
                string SchoolNo = UserProfileHelper.GetSchoolNo();

                BDMT01 T01 = CacheBDMT01.Where(a => a.SCHOOL_NO == SchoolNo).FirstOrDefault();
                if (T01 != null)
                {
                    FooterSchool = T01.SCHOOL_NO;
                    FooterSHORT_NAME = T01.SHORT_NAME;
                    FooterUSERNANE = T01.Manager_Name;
                    FooterTEL = T01.Manager_Phone;
                }
            }

            @TempData["FooterSchool"] = FooterSchool;
            @TempData["FooterSHORT_NAME"] = FooterSHORT_NAME;
            @TempData["FooterUSERNANE"] = FooterUSERNANE;
            @TempData["FooterTEL"] = FooterTEL;

            return PartialView();
        }

        /// <summary>
        /// 頁尾各校聯絡資訊。
        /// </summary>
        /// <returns></returns>
        public ActionResult _RightBar()
        {
            HomeRightBarViewModel Data = new HomeRightBarViewModel();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            UserProfile LoginUser = UserProfileHelper.Get();

            if (LoginUser != null)
            {
                Data.LoginUser = LoginUser;

                //角色娃娃
                Data.PlayerUrl = UserProfile.GetPlayerUrl(ref db, LoginUser.SCHOOL_NO, LoginUser.USER_NO, LoginUser.SEX, LoginUser.USER_TYPE);

                //閱讀等級圖示
                Data.ReadImgURL = UserProfileHelper.GetImgReadLEVEL((Byte)LoginUser.LEVEL_ID);

                //「認證」等級圖示
                Data.PassportImgURL = UserProfileHelper.GetImgPassportLEVEL(UserProfile.GetOenUseADDT04toShort(LoginUser.USER_NO, LoginUser.SCHOOL_NO, ref db));

                #region 運動撲滿相關

                Data.RunMedalImage = UserProfileHelper.GetImgRunMedal(LoginUser.RUN_TOTAL_METER);

                Data.RUN_TOTAL_METER = LoginUser.RUN_TOTAL_METER;

                Data.LOCATION_NAME = LoginUser.LOCATION_NAME;

                #endregion 運動撲滿相關
            }

            return PartialView(Data);
        }
        [HttpGet]
      
        public ActionResult _LoginPartial(string returnURL = null)
        {
            if (returnURL != null)
            {
                ViewBag.UrlReferrer = returnURL;
            }
            return View();

        }
        [HttpGet]
     
        public ActionResult LoginPage(string returnURL = null,string SCSCHOOLNO= null)
        {
            if (returnURL != null)
            {
                ViewBag.UrlReferrer = returnURL;
              


            }
            if (SCSCHOOLNO!=null)
            {
                ViewBag.SCHOOLNO = SCSCHOOLNO;

            }
            return View();
        }

        /// <summary>
        /// 清Cache
        /// </summary>
        /// <returns></returns>
        public ActionResult CacheClear()
        {
            List<string> cacheKeys = new List<string>();
            var cacheEnum = HttpRuntime.Cache.GetEnumerator();

            while (cacheEnum.MoveNext())
            {
                cacheKeys.Add(cacheEnum.Key.ToString());
            }
            foreach (string cacheKey in cacheKeys)
            {
                HttpRuntime.Cache.Remove(cacheKey);
            }

            // return View();
            return RedirectToAction("Index", "Home");
        }

        public ActionResult Intro()
        {
            return View();
        }

        /// <summary>
        /// 操作手徹
        /// </summary>
        /// <returns></returns>
        public FileResult User_manual()
        {
            string filePath = Server.MapPath(@"~/Content/臺北e酷幣學生操作手冊完成.docx");//路徑
            return File(filePath, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "臺北e酷幣學生操作手冊完成.docx"); //welcome.txt是客戶端保存的名字
        }

        public ActionResult CheckInfo()
        {
            return View();
        }

        [AllowAnonymous]
        public void VerificationCode()
        {
            Bitmap _bmp = new Bitmap(60, 20);

            // (封裝 GDI+ 繪圖介面) 所有繪圖作業都需透過 Graphics 物件進行操作
            Graphics _graphics = Graphics.FromImage(_bmp);
            _graphics.Clear(Color.Black);
            // 如果想啟用「反鋸齒」功能，可以將以下這行取消註解
            //_graphics.TextRenderingHint = TextRenderingHint.AntiAlias;

            // 設定要出現在圖片上的文字字型、大小與樣式
            Font _font = new Font("Courier New", 12, FontStyle.Bold);

            // 產生一個 5 個字元的亂碼字串，並直接寫入 Session 裡
            // 請參考: http://www.obviex.com/Samples/Password.aspx )
            Session["Captcha"] = RandomPassword.Generate(4, 4);
            Session["CreateTime"] = DateTime.Now;

            // 將亂碼字串「繪製」到之前產生的 Graphics 「繪圖板」上
            _graphics.DrawString(Convert.ToString(Session["Captcha"]),
                _font, Brushes.White, 3, 3);

            // 輸出之前 Captcha 圖示
            Response.ContentType = "image/gif";
            _bmp.Save(Response.OutputStream, ImageFormat.Gif);

            // 釋放所有在 GDI+ 所佔用的記憶體空間 ( 非常重要!! )
            _font.Dispose();
            _graphics.Dispose();
            _bmp.Dispose();

            // 由於我們要輸出的是「圖片」而非「網頁」，所以必須在此中斷網頁執行
            Response.End();
        }

        protected void BuildPortalHomeModels()
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            ViewBag.BET02List = db.BET02.Include("BET02_LANG").
                Where(a => a.S_DATE <= DateTime.Today && a.E_DATE > DateTime.Today && a.ISPUBLISH == "Y" &&
                         a.CLASS_TYPE == "1").
                            OrderByDescending(a => a.TOP_YN).
                                ThenByDescending(a => a.S_DATE).ThenByDescending(a => a.BULLET_ID).Take(5).ToList();
        }

        protected void BuildGuestHomeModels(string SchoolNo)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            ViewBag.BET02List = db.BET02.Include("BET02_LANG").
                Where(a => a.S_DATE <= DateTime.Today && a.E_DATE > DateTime.Today && a.ISPUBLISH == "Y" &&
                        (a.CLASS_TYPE == "1" || (a.SCHOOL_NO == SchoolNo && a.CLASS_TYPE == "2"))).
                            OrderByDescending(a => a.TOP_YN).
                                ThenByDescending(a => a.S_DATE).ThenByDescending(a => a.BULLET_ID).Take(5).ToList();

            ViewBag.ADDV01List = db.ADDV01.Where(a => a.SCHOOL_NO == SchoolNo).OrderByDescending(a => a.WRITING_QTY).Take(5).ToList();

            //ViewBag.ADDT09List = db.ADDT09.OrderByDescending(a => a.BOOK_QTY).Take(5).ToList();
            //var ans = from a9 in db.ADDT09
            //          join h1 in db.HRMT01
            //          on new { a9.SCHOOL_NO, a9.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
            //          into h1Table
            //          from hh1 in h1Table.DefaultIfEmpty()
            //          where a9.SCHOOL_NO == SchoolNo && a9.BOOK_QTY > 0 //&& hh1.USER_STATUS != 8 && hh1.USER_STATUS != 9
            //          select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = a9.BOOK_QTY, USER_STATUS = hh1.USER_STATUS };

            var ans = from a9 in db.ADDT09
                      join h1 in db.HRMT01
                      on new { a9.SCHOOL_NO, a9.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                      where a9.SCHOOL_NO == SchoolNo && a9.BOOK_QTY > 0 && (!UserStaus.NGUserStausList.Contains(h1.USER_STATUS))
                      select new HRMT01QTY { CLASS_NO = h1.CLASS_NO, NAME = h1.SNAME, USER_NO = h1.USER_NO, QTY = a9.BOOK_QTY, USER_STATUS = h1.USER_STATUS };

            ViewBag.ADDT09List = ans.OrderByDescending(a => a.QTY).Take(5).ToList();

            var RankCash = from w1 in db.AWAT01
                            join h1 in db.HRMT01
                           on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                           into h1Table
                           from hh1 in h1Table.DefaultIfEmpty()
                           where w1.SCHOOL_NO == SchoolNo && w1.USER_NO == hh1.USER_NO && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
                           select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };

            ViewBag.AWAT01List = RankCash.OrderByDescending(a => a.CASH_ALL).Take(5).ToList();
        }

        protected void BuildStudentHomeModels(string SchoolNo)
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (user == null) return;

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ViewBag.BET02List = db.BET02.Include("BET02_LANG").
                Where(a => a.S_DATE <= DateTime.Today && a.E_DATE > DateTime.Today && a.ISPUBLISH == "Y" &&
                        (a.CLASS_TYPE == "1" || (a.SCHOOL_NO == SchoolNo && a.CLASS_TYPE == "2"))).
                            OrderByDescending(a => a.TOP_YN).
                                ThenByDescending(a => a.S_DATE).ThenByDescending(a => a.BULLET_ID).Take(5).ToList();

                ViewBag.ADDT03List = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, user.GRADE.ToString());

                ViewBag.QAT02Qty = db.QAT02.Where(q2 => q2.A_USER_NO == user.USER_NO && q2.A_STATUS != "2").Count();

                var HotQuery = db.AWAT02.Where(aw2 => aw2.SCHOOL_NO == SchoolNO || aw2.SCHOOL_NO == "ALL").Where(aw2 => (aw2.AWARD_STATUS == "y" || aw2.AWARD_STATUS == "0") && aw2.EDATETIME >= DateTime.Now).OrderByDescending(aw2 => aw2.HOT_YN).OrderByDescending(aw2 => aw2.COST_CASH).Take(3);
                ViewBag.HotAwards = HotQuery.ToList();

                // 撈出未讀的通知清單: 僅按讚及留言 => 周一正說要拿掉
                IEnumerable<APPT02> toastData = new List<APPT02>();
                //toastData = from a in db.APPT02
                //        where a.SCHOOL_NO == SchoolNo && a.USER_NO == user.USER_NO
                //        && a.STATUS == APPT02.StatusVal.UnRead
                //        && a.DEL_YN == "N"
                //        && a.REF_SOU_BRE_NO == "ADDI01"
                //            select a;
                ViewBag.ToastData = toastData.ToList();
            }
        }

        private string NextAction(UserProfile LoginUser)
        {
            switch (LoginUser.USER_TYPE)
            {
                case "S":   //學生
                    if (LoginUser.Chance_BIRTHDAY)
                    {
                        return "Birthday";
                    }
                    else if (LoginUser.NotePassGrade > 0)
                    {
                        return "PassNote";
                    }
                    else if (LoginUser.NoteReadLevel > 0)
                    {
                        return "ReadLevelNote";
                    }
                    return "StudentIndex";

                case "T":   //教師
                case "A":   //管理者
                    return "TeacherIndex";

                default:
                    return "GuestIndex";
            }
        }

        public class SLogingTypeVal
        {
            /// <summary>
            /// 正常登入  方式
            /// </summary>
            public static string normal = "normal";

            /// <summary>
            ///切換登入  方式
            /// </summary>
            public static string switching = "switching";

            /// <summary>
            ///APP 開起自動登入
            /// </summary>
            public static string AppAuto = "AppAuto";
        }
    }
}