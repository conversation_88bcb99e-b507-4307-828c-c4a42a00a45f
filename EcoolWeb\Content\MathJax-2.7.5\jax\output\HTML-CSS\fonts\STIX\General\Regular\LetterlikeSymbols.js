/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LetterlikeSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{8450:[676,14,705,45,663],8453:[676,14,837,48,795],8455:[676,14,598,28,561],8458:[441,219,738,30,678],8459:[687,15,997,53,991],8460:[695,204,824,43,773],8461:[662,0,718,70,648],8462:[683,10,577,47,545],8463:[683,10,579,47,547],8464:[675,15,897,26,888],8466:[687,15,946,33,931],8467:[687,11,579,48,571],8469:[662,0,698,70,628],8470:[676,14,1012,7,966],8471:[676,14,760,38,722],8473:[662,0,678,70,628],8474:[676,65,765,45,715],8475:[687,15,944,34,876],8477:[662,0,747,70,712],8478:[667,101,780,69,763],8482:[662,-256,980,30,957],8484:[662,0,727,50,677],8485:[662,218,424,35,391],8486:[676,0,744,29,715],8487:[662,14,744,29,715],8488:[695,204,726,50,676],8489:[463,0,360,32,276],8491:[871,0,722,15,707],8492:[687,15,950,34,902],8493:[695,24,717,47,675],8494:[676,17,843,35,808],8495:[441,11,627,30,554],8496:[687,15,750,100,734],8497:[680,0,919,43,907],8498:[662,0,535,13,462],8499:[674,15,1072,38,1056],8500:[441,11,697,30,680],8502:[677,19,639,57,572],8503:[677,19,505,40,463],8504:[677,19,599,52,495],8508:[450,12,673,25,645],8509:[460,218,540,0,526],8510:[662,0,469,70,567],8511:[662,0,718,70,648],8512:[763,259,923,61,882],8513:[676,14,695,68,668],8514:[662,0,559,13,485],8515:[662,0,559,13,485],8516:[662,0,630,21,609],8517:[653,0,713,17,703],8518:[683,11,581,40,634],8519:[441,11,515,40,485],8520:[653,0,293,27,346],8521:[653,217,341,-104,394],8522:[692,0,664,45,602],8523:[676,13,778,28,736]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/LetterlikeSymbols.js");
