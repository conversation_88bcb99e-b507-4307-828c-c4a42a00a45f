﻿@model ECOOL_APP.EF.ADDT20

<div class="form-group">
    @Html.LabelFor(model => model.CRE_DATE, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.CRE_DATE, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.USER_NO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.NAME, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.NAME, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.CLASS_NO, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.SEAT_NO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.SEAT_NO, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.CONTENT_TXT, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.TextAreaFor(model => model.CONTENT_TXT, 5, 100, new { @class = "form-control", @disabled = "true" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.MEMO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.MEMO, new { htmlAttributes = new { @class = "form-control", @disabled = "true" } })
    </div>
</div>
@if (!string.IsNullOrWhiteSpace(ViewBag.IMG_FILE))
            {
    <div class="form-group">
        @Html.Label("原上傳圖", htmlAttributes: new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            <img src='@ViewBag.IMG_FILE' class="img-responsive " alt="Responsive image" />
            @Html.HiddenFor(model => model.IMG_FILE)
        </div>
    </div>
}