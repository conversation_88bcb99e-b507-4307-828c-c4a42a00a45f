﻿@model ZZZI33EditTopic_DViewModel



@using (Html.BeginCollectionItemSou("Topic_D", "Topic", Model.index, Model.isCopy))
{
<div id="<EMAIL>(m=>m.Q_VAL)">
    @Html.HiddenFor(m => m.Q_INPUT_TYPE)
    @if (Model.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.text)
    {
        <fieldset>
            <input type="@(Model.Q_INPUT_TYPE)" class="form-control" placeholder="回覆..." disabled>

            @Html.CheckBoxFor(m => m.Q_Must, new { htmlAttributes = new { @class = "form-control input-md" } })<font color="FireBrick">簡答是否必填，打勾為必填</font>
        </fieldset>


    }
    else if (Model.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld)
    {


        @*<fieldset disabled>
                <input type="file" class="form-control">
            </fieldset>*@
    }
    else if (Model.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage)
    {

        @Html.CheckBoxFor(m => m.Q_Must, new { htmlAttributes = new { @class = "form-control input-md" } })<font color="FireBrick">上傳圖片是否為必填，打勾為必填</font>
        @*<fieldset disabled>
                <input type="file" class="form-control">
            </fieldset>*@
    }
    else if (Model.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.radio)
    {
        <div class="input-group">
            @Html.EditorFor(m => m.Q_VAL, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "請填寫選項內容..." } })

            <span class="input-group-btn" style="vertical-align:top">
                <button class="btn btn-default" type="button" onclick="onInCkeditorModal('@Html.IdFor(m => m.Q_VAL)')" title="載入編輯器/移除編輯器"><i class="glyphicon glyphicon-edit"></i></button>
                <button class="btn btn-default" type="button" onclick="deleteRow('<EMAIL>(m => m.Q_VAL)')"><i class="glyphicon glyphicon-remove"></i></button>
            </span>
        </div>
    }
    else if (Model.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.checkbox)
    {
        <div class="input-group">
            @Html.EditorFor(m => m.Q_VAL, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "請填寫選項內容..." } })
            <span class="input-group-btn" style="vertical-align:top">
                <button class="btn btn-default" type="button" onclick="onInCkeditorModal('@Html.IdFor(m => m.Q_VAL)')" title="載入編輯器/移除編輯器"><i class="glyphicon glyphicon-edit"></i></button>
                <button class="btn btn-default" type="button" onclick="deleteRow('<EMAIL>(m => m.Q_VAL)')"><i class="glyphicon glyphicon-remove"></i></button>
            </span>
        </div>
    }

</div>
}






