﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.EF
{
    public class ADDI12EditMainViewModel
    {
        /// <summary>
        ///小小舞臺ID
        /// </summary>
        [DisplayName("小小舞臺ID")]
        public string STAGE_ID { get; set; }

        public string SCHOOL_NAME { get; set; }
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///標題名稱
        /// </summary>
        [DisplayName("標題名稱")]
        [Required(ErrorMessage = "*此欄位必輸")]
        [StringLength(40, ErrorMessage = "*此欄位僅接受40個字以內的文字")]
        public string STAGE_NAME { get; set; }

        /// <summary>
        ///封面
        /// </summary>
        [DisplayName("Youtube封面")]
        public string YOUTUBE_IMG { get; set; }

        public string YOUTUBE_IMG_Path { get; set; }

        public HttpPostedFileBase UploadYoutubeFile { get; set; }

        /// <summary>
        ///網址
        /// </summary>
        [DisplayName("Youtube網址")]
        [Required(ErrorMessage = "*此欄位必輸")]
        [DataType(DataType.Url, ErrorMessage = "請輸入正確的網址，請加https://")]
        public string YOUTUBE_URL { get; set; }

        /// <summary>
        ///是否首播
        /// </summary>
        [DisplayName("是否首播")]
        public bool IS_PREMIER { get; set; }

        /// <summary>
        ///開始日期
        /// </summary>
        [DisplayName("首播開始日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? STAGE_DATES { get; set; }

        /// <summary>
        ///結束日期
        /// </summary>
        [DisplayName("首播結束日期")]
        [CompareDateLessThan("STAGE_DATES", ErrorMessage = "首播結束日期不能小於首播開始日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? STAGE_DATEE { get; set; }
    }
}