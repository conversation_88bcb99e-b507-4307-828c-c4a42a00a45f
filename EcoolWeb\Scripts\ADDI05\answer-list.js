// JavaScript for ADDI05 AnswerList page - 有獎徵答答題清單
$(document).ready(function() {
    // 有獎徵答答題清單模組
    const answerListModule = {
        targetFormID: '#form1',
        
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
        },

        bindEvents: function() {
            // 綁定搜尋按鈕事件
            $('input[onclick*="FunPageProc"]').on('click', this.handleSearch.bind(this));
            
            // 綁定清除按鈕事件
            $('input[onclick*="todoClear"]').on('click', this.handleClear.bind(this));
            
            // 綁定分頁大小變更事件
            $('select[onchange*="FunPageProc"]').on('change', this.handlePageSizeChange.bind(this));
            
            // 綁定排序按鈕事件
            $('th[onclick*="doSort"]').on('click', this.handleSort.bind(this));
            
            // 綁定表單提交事件
            $(this.targetFormID).on('submit', this.handleFormSubmit.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.onGo = this.onGo.bind(this);
            window.todoClear = this.todoClear.bind(this);
            window.FunPageProc = this.funPageProc.bind(this);
            window.doSort = this.doSort.bind(this);
        },

        onGo: function(actionVal) {
            // 使用共用的onGo函數，並清除排序
            const options = {
                clearSort: true
            };

            return ADDI05Common.onGo(actionVal, options);
        },

        todoClear: function() {
            // 使用共用的表單清除功能，只清除搜尋區域
            if (ADDI05Common.clearForm(this.targetFormID, `${this.targetFormID} #DivSearch :input,:selected`)) {
                this.funPageProc(1);
            }
        },

        funPageProc: function(pageno) {
            // 使用共用的分頁處理功能
            return ADDI05Common.pageProc(pageno, 'Page');
        },

        doSort: function(sortCol) {
            // 使用共用的排序處理功能
            return ADDI05Common.sortProc(sortCol, 'OrderByName');
        },

        // 事件處理器
        handleSearch: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            if (onclick) {
                const match = onclick.match(/FunPageProc\((\d+)\)/);
                if (match) {
                    this.funPageProc(parseInt(match[1]));
                }
            }
        },

        handleClear: function(event) {
            event.preventDefault();
            this.todoClear();
        },

        handlePageSizeChange: function(event) {
            const onclick = $(event.target).attr('onchange');
            if (onclick) {
                const match = onclick.match(/FunPageProc\((\d+)\)/);
                if (match) {
                    this.funPageProc(parseInt(match[1]));
                }
            }
        },

        handleSort: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            if (onclick) {
                const match = onclick.match(/doSort\('([^']*)'\)/);
                if (match) {
                    this.doSort(match[1]);
                }
            }
        },

        handleFormSubmit: function(event) {
            try {
                console.log('表單提交');
                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                this.showMessage('表單提交時發生錯誤，請稍後再試');
                return false;
            }
        },

        showMessage: function(message, type = 'error') {
            // 使用共用的訊息顯示功能
            ADDI05Common.showMessage(message, type);
        }
    };

    // 初始化模組
    answerListModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});
