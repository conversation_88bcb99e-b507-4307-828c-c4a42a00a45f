/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Misc-italic"]={directory:"Misc/Italic",family:"STIXMathJax_Misc",style:"italic",testString:"\u00A0\u0250\u0251\u0252\u0253\u0254\u0255\u0256\u0257\u0258\u0259\u025A\u025B\u025C\u025D",32:[0,0,250,0,0],160:[0,0,250,0,0],592:[460,10,444,19,421],593:[460,10,511,17,487],594:[460,10,511,17,487],595:[683,11,500,23,488],596:[441,11,444,30,425],597:[441,160,444,-3,425],598:[683,233,500,15,527],599:[683,13,500,15,748],600:[441,11,444,31,416],601:[441,11,444,31,412],602:[441,11,639,31,639],603:[475,14,444,31,467],604:[475,14,480,31,447],605:[475,14,666,31,666],606:[475,14,490,30,458],607:[441,207,357,-100,340],608:[683,212,714,8,799],609:[482,212,595,8,579],610:[441,11,562,52,562],611:[441,234,444,15,426],612:[450,10,480,4,475],613:[450,242,500,19,478],614:[683,9,500,19,494],615:[683,233,500,-6,494],616:[654,11,278,16,264],617:[454,10,333,51,266],618:[441,0,247,-8,298],619:[683,11,278,4,331],620:[683,11,375,12,366],621:[683,233,252,8,279],622:[683,233,575,41,537],623:[441,9,722,12,704],624:[441,233,722,12,704],625:[441,233,690,12,672],626:[441,233,606,-110,580],627:[441,233,498,14,487],628:[441,8,539,-20,599],629:[441,11,500,27,468],630:[441,6,718,49,738],631:[475,4,668,30,638],632:[683,233,660,30,630],633:[441,0,402,-45,322],634:[683,0,383,-45,384],635:[441,233,353,-45,342],636:[441,233,333,-20,412],637:[441,233,390,24,412],638:[470,0,401,45,424],639:[470,0,338,66,293],640:[464,0,475,25,501],641:[464,0,475,25,581],642:[442,218,389,9,376],643:[683,233,415,-110,577],644:[683,233,453,-110,595],645:[470,233,339,79,355],646:[683,243,439,-62,602],647:[460,97,330,38,296],648:[546,233,278,6,308],649:[441,11,500,9,479],650:[450,10,537,49,552],651:[441,10,500,52,475],652:[441,18,444,20,426],653:[441,18,667,15,648],654:[647,0,444,10,460],655:[464,0,633,62,603],656:[428,218,405,17,429],657:[428,47,393,17,380],658:[450,233,413,21,517],659:[450,305,457,7,544],660:[683,0,500,55,509],661:[683,0,500,55,495],662:[662,14,393,-25,413],663:[441,238,450,24,459],664:[679,17,723,22,704],665:[464,0,460,19,505],666:[475,14,479,20,470],667:[515,11,570,29,650],668:[464,0,572,25,671],669:[652,233,403,-80,394],670:[439,255,463,26,473],671:[464,0,470,25,473],672:[582,209,480,25,666],673:[683,0,500,55,509],674:[683,0,500,55,495],675:[683,13,743,15,741],676:[683,233,743,15,780],677:[683,47,754,15,741],678:[546,11,500,38,523],679:[683,233,517,-32,655],680:[546,16,632,38,612],8355:[653,0,611,8,645],8356:[670,8,500,10,517],8359:[653,13,1149,0,1126],8364:[664,12,500,16,538],9312:[676,14,684,0,684],9313:[676,14,684,0,684],9314:[676,14,684,0,684],9315:[676,14,684,0,684],9316:[676,14,684,0,684],9317:[676,14,684,0,684],9318:[676,14,684,0,684],9319:[676,14,684,0,684],9320:[676,14,684,0,684],9398:[676,14,684,0,684],9399:[676,14,684,0,684],9400:[676,14,684,0,684],9401:[676,14,684,0,684],9402:[676,14,684,0,684],9403:[676,14,684,0,684],9404:[676,14,684,0,684],9405:[676,14,684,0,684],9406:[676,14,684,0,684],9407:[676,14,684,0,684],9408:[676,14,684,0,684],9409:[676,14,684,0,684],9410:[676,14,684,0,684],9411:[676,14,684,0,684],9412:[676,14,684,0,684],9413:[676,14,684,0,684],9414:[676,14,684,0,684],9415:[676,14,684,0,684],9417:[676,14,684,0,684],9418:[676,14,684,0,684],9419:[676,14,684,0,684],9420:[676,14,684,0,684],9421:[676,14,684,0,684],9422:[676,14,684,0,684],9423:[676,14,684,0,684],9424:[676,14,684,0,684],9425:[676,14,684,0,684],9426:[676,14,684,0,684],9427:[676,14,684,0,684],9428:[676,14,684,0,684],9429:[676,14,684,0,684],9430:[676,14,684,0,684],9431:[676,14,684,0,684],9432:[676,14,684,0,684],9433:[676,14,684,0,684],9434:[676,14,684,0,684],9435:[676,14,684,0,684],9436:[676,14,684,0,684],9437:[676,14,684,0,684],9438:[676,14,684,0,684],9439:[676,14,684,0,684],9440:[676,14,684,0,684],9441:[676,14,684,0,684],9442:[676,14,684,0,684],9443:[676,14,684,0,684],9444:[676,14,684,0,684],9445:[676,14,684,0,684],9446:[676,14,684,0,684],9447:[676,14,684,0,684],9448:[676,14,684,0,684],9449:[676,14,684,0,684],9450:[676,14,684,0,684]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Misc-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Misc/Italic/Main.js"]);
