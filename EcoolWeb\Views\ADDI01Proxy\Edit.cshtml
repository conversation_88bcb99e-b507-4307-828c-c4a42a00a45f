﻿@model EcoolWeb.ViewModels.ADDI01ProxyEditViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js" nonce="cmlvaw"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <br />
    <div class="Div-EZ-ZZZI26">
        <div class="alert alert-success" style="padding:0 2%; margin-bottom:0px"><h3>代申請線上投稿</h3></div>
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="table-responsive">
                @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                <table class="table-ecool table-hover">
                    <thead>
                        <tr class="text-center">
                            <th>
                                序
                            </th>
                            <th>
                                刪
                            </th>
                            <th>
                                姓名 / 座號
                            </th>
                            <th>
                                主題<span class="text-danger"> *</span>
                            </th>
                            <th>
                                內容<span class="text-danger"> *</span>
                            </th>
                            <th>
                                點數
                            </th>
                            @if (ViewBag.VerifyUseYN == "Y")
                            {
                                <th>
                                    上傳
                                </th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            int Num = 0;

                            foreach (var Data in Model.Details_List)
                            {
                                <tr>
                                    <td class="text-center">
                                        @(Num + 1)
                                    </td>
                                    <td class="text-center">
                                        <input id="Details_List_Del_@Num" name="Details_List[@Num].Del" type="checkbox" value="true" @(Data.Del ? "checked=\"checked\"" : "") />
                                    </td>
                                    <td>
                                        <select class="form-control input-sm" name="Details_List[@Num].USER_NO" style="min-width:97px;">
                                            @{
                                                string SelectedVal = string.Empty;

                                                if (Model.Search.ModeVal == (byte)EcoolWeb.Controllers.ZZZI26Controller.Mode.ManyStudentIndex)
                                                {
                                                    <option value="">請選擇姓名...</option>
                                                }

                                                foreach (var item in ViewBag.USER_NOItems as IEnumerable<SelectListItem>)
                                                {
                                                    SelectedVal = Data.USER_NO == item.Value ? "selected" : "";
                                                    <option value="@item.Value" @SelectedVal>@item.Text</option>
                                                }
                                            }
                                        </select>
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].USER_NO, "", new { @class = "text-danger" })
                                    </td>
                                    <td>
                                        @Html.EditorFor(model => model.Details_List[Num].SUBECT, new { htmlAttributes = new { @class = "form-control  input-sm", @style = "min-width:80px;max-width:97pt" } })
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].SUBECT, "", new { @class = "text-danger", required = "required" })
                                    </td>
                                    <td>
                                        @Html.TextAreaFor(model => model.Details_List[Num].ARTICLE,
                                       new { @class = "form-control", rows = "4", cols = "20", required="required" } )
                                    </td>
                                    <td class="text-center">
                                        @Html.EditorFor(model => model.Details_List[Num].GIVE_POINT,
                                       new { htmlAttributes = new { type = "number", min = "-25", max = "50", step="5",
                                           @class = "form-control input-sm", @style = "min-width:40px;max-width:50pt" } })
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].GIVE_POINT, "", new { @class = "text-danger" })

                                    </td>
                                    @if (ViewBag.VerifyUseYN == "Y")
                                    {
                                        <td>
                                            <input class="form-control input-sm" type="file" name="Details_List[@Num].files" id="files_@Num" value="瀏覽" style="min-width:100px" />
                                            @Html.ValidationMessageFor(model => model.Details_List[Num].files, "", new { @class = "text-danger" })
                                        </td>
                                    }

                                </tr>
                                Num++;
                            }
                        }

                    </tbody>
                </table>
            </div>
            <div>
                <div>
                    @Html.HiddenFor(model => model.Search.ModeVal)
                    @Html.HiddenFor(model => model.Search.SCHOOL_NO)
                    @Html.HiddenFor(model => model.Search.NumType)
                    @Html.HiddenFor(model => model.Search.CLASS_NO)
                    @Html.Hidden("TOLTAL", TempData["TOLTAL"])
                    @Html.Hidden("DATA_TYPE")
                    @Html.Hidden("Num", Num)
                    @Html.Hidden("VerifyUseYN", (string)ViewBag.VerifyUseYN)


                    @Html.ValidationMessage("additem", new { @class = "text-danger" })
                    <div class="col-md-6 col-xs-8">
                        <div class="input-group">
                            @Html.TextBox("ADDNUM", TempData["ADDNUM"], new { @class = "form-control", @placeholder = "請填筆數", onchange = "if (isNaN(this.value) || this.value<=0) {alert('請填數字!');this.value=''};" })
                            <span class="input-group-btn">
                                <input type=button class="btn btn-default" name=additem value="增加筆數" onclick="AddItem()">
                            </span>
                        </div><!-- /input-group -->
                    </div>
                    <div class="col-md-6 col-xs-4">
                        <input type="button" value="存檔" class="btn btn-success" onclick="Save('Save')" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="height:20px"></div>
}
<div class="text-center">
    @Html.ActionLink("回選擇模式", "Index", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-default" })
</div>


@section css{
    <style>
        .Div-EZ-ZZZI26 {
            width: 100%;
            overflow-x: auto;
        }

        .table-ecool {
            width: 130% !important;
            max-width: none !important;
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI01PROXY_URLS = {
            editAction: "@Url.Action("Edit", (string)ViewBag.BRE_NO)"
        };
    </script>
    <script src="~/Scripts/ADDI01Proxy/edit.js" nonce="cmlvaw"></script>
}