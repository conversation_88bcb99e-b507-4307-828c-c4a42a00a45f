/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/fontdata-extra.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(h){var n="2.7.5";var m=h.FONTDATA.DELIMITERS;var g="MathJax_Main",i="MathJax_Main-bold",k="MathJax_AMS",e="MathJax_Size1",a="MathJax_Size4";var l="H",f="V";var j=[8722,g,0,0,0,-0.3,-0.3];var c=[61,g,0,0,0,0,0.1];var d={61:{dir:l,HW:[[0.767,g]],stretch:{rep:[61,g]}},8606:{dir:l,HW:[[1,k]],stretch:{left:[8606,k],rep:j}},8608:{dir:l,HW:[[1,k]],stretch:{right:[8608,k],rep:j}},8612:{dir:l,HW:[],stretch:{min:1,left:[8592,g],rep:j,right:[8739,e,0,-0.05,0.9]}},8613:{dir:f,HW:[],stretch:{min:0.6,bot:[8869,i,0,0,0.75],ext:[9168,e],top:[8593,e]}},8614:{dir:l,HW:[[1,g]],stretch:{left:[8739,e,-0.09,-0.05,0.9],rep:j,right:[8594,g]}},8615:{dir:f,HW:[],stretch:{min:0.6,top:[8868,i,0,0,0.75],ext:[9168,e],bot:[8595,e]}},8624:{dir:f,HW:[[0.722,k]],stretch:{top:[8624,k],ext:[9168,e,0.097]}},8625:{dir:f,HW:[[0.722,k]],stretch:{top:[8625,k,0.27],ext:[9168,e]}},8636:{dir:l,HW:[[1,g]],stretch:{left:[8636,g],rep:j}},8637:{dir:l,HW:[[1,g]],stretch:{left:[8637,g],rep:j}},8638:{dir:f,HW:[[0.888,k]],stretch:{top:[8638,k,0.12,0,1.1],ext:[9168,e]}},8639:{dir:f,HW:[[0.888,k]],stretch:{top:[8639,k,0.12,0,1.1],ext:[9168,e]}},8640:{dir:l,HW:[[1,g]],stretch:{right:[8640,g],rep:j}},8641:{dir:l,HW:[[1,g]],stretch:{right:[8641,g],rep:j}},8642:{dir:f,HW:[[0.888,k]],stretch:{bot:[8642,k,0.12,0,1.1],ext:[9168,e]}},8643:{dir:f,HW:[[0.888,k]],stretch:{bot:[8643,k,0.12,0,1.1],ext:[9168,e]}},8666:{dir:l,HW:[[1,k]],stretch:{left:[8666,k],rep:[8801,g]}},8667:{dir:l,HW:[[1,k]],stretch:{right:[8667,k],rep:[8801,g]}},9140:{dir:l,HW:[],stretch:{min:0.5,left:[9484,k,0,-0.1],rep:[8722,g,0,0.35],right:[9488,k,0,-0.1]}},9141:{dir:l,HW:[],stretch:{min:0.5,left:[9492,k,0,0.26],rep:[8722,g,0,0,0,0.25],right:[9496,k,0,0.26]}},9180:{dir:l,HW:[[0.778,k,0,8994],[1,g,0,8994]],stretch:{left:[57680,a],rep:[57684,a],right:[57681,a]}},9181:{dir:l,HW:[[0.778,k,0,8995],[1,g,0,8995]],stretch:{left:[57682,a],rep:[57684,a],right:[57683,a]}},9184:{dir:l,HW:[],stretch:{min:1.25,left:[714,g,-0.1],rep:[713,g,0,0.13],right:[715,g],fullExtenders:true}},9185:{dir:l,HW:[],stretch:{min:1.5,left:[715,g,-0.1,0.1],rep:[713,g],right:[714,g,-0.1,0.1],fullExtenders:true}},10502:{dir:l,HW:[],stretch:{min:1,left:[8656,g],rep:c,right:[8739,e,0,-0.1]}},10503:{dir:l,HW:[],stretch:{min:0.7,left:[8872,k,0,-0.12],rep:c,right:[8658,g]}},10574:{dir:l,HW:[],stretch:{min:0.5,left:[8636,g],rep:j,right:[8640,g]}},10575:{dir:f,HW:[],stretch:{min:0.5,top:[8638,k,0.12,0,1.1],ext:[9168,e],bot:[8642,k,0.12,0,1.1]}},10576:{dir:l,HW:[],stretch:{min:0.5,left:[8637,g],rep:j,right:[8641,g]}},10577:{dir:f,HW:[],stretch:{min:0.5,top:[8639,k,0.12,0,1.1],ext:[9168,e],bot:[8643,k,0.12,0,1.1]}},10586:{dir:l,HW:[],stretch:{min:1,left:[8636,g],rep:j,right:[8739,e,0,-0.05,0.9]}},10587:{dir:l,HW:[],stretch:{min:1,left:[8739,e,-0.05,-0.05,0.9],rep:j,right:[8640,g]}},10588:{dir:f,HW:[],stretch:{min:0.7,bot:[8869,i,0,0,0.75],ext:[9168,e],top:[8638,k,0.12,0,1.1]}},10589:{dir:f,HW:[],stretch:{min:0.7,top:[8868,i,0,0,0.75],ext:[9168,e],bot:[8642,k,0.12,0,1.1]}},10590:{dir:l,HW:[],stretch:{min:1,left:[8637,g],rep:j,right:[8739,e,0,-0.05,0.9]}},10591:{dir:l,HW:[],stretch:{min:1,left:[8739,e,-0.05,-0.05,0.9],rep:j,right:[8641,g]}},10592:{dir:f,HW:[],stretch:{min:0.7,bot:[8869,i,0,0,0.75],ext:[9168,e],top:[8639,k,0.12,0,1.1]}},10593:{dir:f,HW:[],stretch:{min:0.7,top:[8868,i,0,0,0.75],ext:[9168,e],bot:[8643,k,0.12,0,1.1]}}};for(var b in d){if(d.hasOwnProperty(b)){m[b]=d[b]}}MathJax.Ajax.loadComplete(h.fontDir+"/fontdata-extra.js")})(MathJax.OutputJax["HTML-CSS"]);
