/*
  Highcharts JS v6.0.6 (2018-02-05)

 Support for parallel coordinates in Highcharts

 (c) 2010-2017 Pawel Fus

 License: www.highcharts.com/license
*/
(function(l){"object"===typeof module&&module.exports?module.exports=l:l(Highcharts)})(function(l){(function(c){function l(b){var a=this.series&&this.series.chart,d=b.apply(this,Array.prototype.slice.call(arguments,1)),h,v,g;a&&a.hasParallelCoordinates&&!p(d.formattedValue)&&(g=a.yAxis[this.x],h=g.options,a=(v=w(h.tooltipValueFormat,h.labels.format))?c.format(v,r(this,{value:this.y}),a.time):g.isDatetimeAxis?a.time.dateFormat(h.dateTimeLabelFormats[g.tickPositions.info.unitName],this.y):h.categories?
h.categories[this.y]:this.y,d.formattedValue=d.point.formattedValue=a);return d}var t=c.Series.prototype,u=c.Chart.prototype,q=c.Axis.prototype,w=c.pick,m=c.each,e=c.wrap,n=c.merge,z=c.erase,x=c.splat,r=c.extend,p=c.defined,A=c.arrayMin,B=c.arrayMax,y={lineWidth:0,tickLength:0,opposite:!0,type:"category"};c.setOptions({chart:{parallelCoordinates:!1,parallelAxes:{lineWidth:1,title:{text:"",reserveSpace:!1},labels:{x:0,y:4,align:"center",reserveSpace:!1},offset:0}}});e(u,"init",function(b,a,d){var h=
x(a.yAxis||{}),c=h.length,g=[];if(this.hasParallelCoordinates=a.chart&&a.chart.parallelCoordinates){for(this.setParallelInfo(a);c<=this.parallelInfo.counter;c++)g.push({});a=n({legend:{enabled:!1}},a,{boost:{seriesThreshold:Number.MAX_SAFE_INTEGER},plotOptions:{series:{boostThreshold:Number.MAX_SAFE_INTEGER}}});a.yAxis=h.concat(g);a.xAxis=n(y,x(a.xAxis||{})[0])}return b.call(this,a,d)});e(u,"update",function(b,a){a.chart&&(p(a.chart.parallelCoordinates)&&(this.hasParallelCoordinates=a.chart.parallelCoordinates),
this.hasParallelCoordinates&&a.chart.parallelAxes&&(this.options.chart.parallelAxes=n(this.options.chart.parallelAxes,a.chart.parallelAxes),m(this.yAxis,function(a){a.update({},!1)})));return b.apply(this,Array.prototype.slice.call(arguments,1))});r(u,{setParallelInfo:function(b){var a=this;b=b.series;a.parallelInfo={counter:0};m(b,function(d){d.data&&(a.parallelInfo.counter=Math.max(a.parallelInfo.counter,d.data.length-1))})}});q.keepProps.push("parallelPosition");e(q,"setOptions",function(b,a){var d=
this.chart,c=["left","width","height","top"];b.apply(this,Array.prototype.slice.call(arguments,1));d.hasParallelCoordinates&&(d.inverted&&(c=c.reverse()),this.isXAxis?this.options=n(this.options,y,a):(this.options=n(this.options,this.chart.options.chart.parallelAxes,a),this.parallelPosition=w(this.parallelPosition,d.yAxis.length),this.setParallelPosition(c,this.options)))});e(q,"getSeriesExtremes",function(b){if(this.chart&&this.chart.hasParallelCoordinates&&!this.isXAxis){var a=this.parallelPosition,
d=[];m(this.series,function(b){p(b.yData[a])&&d.push(b.yData[a])});this.dataMin=A(d);this.dataMax=B(d)}else b.apply(this,Array.prototype.slice.call(arguments,1))});r(q,{setParallelPosition:function(b,a){a[b[0]]=100*(this.parallelPosition+.5)/(this.chart.parallelInfo.counter+1)+"%";this[b[1]]=a[b[1]]=0;this[b[2]]=a[b[2]]=null;this[b[3]]=a[b[3]]=null}});e(t,"bindAxes",function(b){if(this.chart.hasParallelCoordinates){var a=this;m(this.chart.axes,function(b){a.insert(b.series);b.isDirty=!0});a.xAxis=
this.chart.xAxis[0];a.yAxis=this.chart.yAxis[0]}else b.apply(this,Array.prototype.slice.call(arguments,1))});e(t,"translate",function(b){b.apply(this,Array.prototype.slice.call(arguments,1));var a=this.chart,d=this.points,c=d&&d.length,e=Number.MAX_VALUE,g,f,k;if(this.chart.hasParallelCoordinates){for(k=0;k<c;k++)f=d[k],p(f.y)?(f.plotX=f.clientX=a.inverted?a.plotHeight-a.yAxis[k].top+a.plotTop:a.yAxis[k].left-a.plotLeft,f.plotY=a.yAxis[k].translate(f.y,!1,!0,null,!0),void 0!==g&&(e=Math.min(e,Math.abs(f.plotX-
g))),g=f.plotX,f.isInside=a.isInsidePlot(f.plotX,f.plotY,a.inverted)):f.isNull=!0;this.closestPointRangePx=e}});e(t,"destroy",function(b){if(this.chart.hasParallelCoordinates){var a=this;m(this.chart.axes||[],function(b){b&&b.series&&(z(b.series,a),b.isDirty=b.forceRedraw=!0)})}b.apply(this,Array.prototype.slice.call(arguments,1))});m(["line","spline"],function(b){e(c.seriesTypes[b].prototype.pointClass.prototype,"getLabelConfig",l)})})(l)});
