﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public partial class GAAT02_U
    {
        public enum UnWearType : byte
        {
            忘記帶 = 1,
            遺失 = 2,
            損毀 = 3,
            其它 = 4,
        }

        static public List<SelectListItem> GetUnWearTypeItems(UnWearType? Val)
        {
            string defaultSelectValue = Val.HasValue ? Val.Value.ToString() : string.Empty;

            List<SelectListItem> UnWearTypeItems = new List<SelectListItem>();
            UnWearTypeItems.Add(new SelectListItem() { Text = "", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            foreach (var Item in Enum.GetValues(typeof(UnWearType)))
            {
                UnWearTypeItems.Add(new SelectListItem() { Text = Item.ToString(), Value = ((byte)Item).ToString(), Selected = ((byte)Item).ToString() == defaultSelectValue });
            }

            return UnWearTypeItems;
        }

        static public string GetUnWearTypeDesc(byte? Val)
        {
            if (Val == null)
            {
                return string.Empty;
            }

            try
            {
                return Enum.GetName(typeof(UnWearType), Val);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
    }
}