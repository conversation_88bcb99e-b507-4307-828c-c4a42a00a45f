﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public class ZZZI33EditTopic_DViewModel
    {

        public bool isCopy { get; set; }

        public string index { get; set; }

        public byte? Q_TYPE { get; set; }

        /// <summary>
        ///投票ID
        /// </summary>
        [DisplayName("投票ID")]
        public string QUESTIONNAIRE_ID { get; set; }

        /// <summary>
        ///投票題目序號
        /// </summary>
        [DisplayName("投票題目序號")]
        public int? Q_NUM { get; set; }

        /// <summary>
        ///投票題目INPUT序號
        /// </summary>
        [DisplayName("投票題目INPUT序號")]
        public int? Q_T_NUM { get; set; }

        /// <summary>
        ///描述
        /// </summary>
        [DisplayName("描述")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_TEXT { get; set; }

        /// <summary>
        ///INPUT VALUE
        /// </summary>
        [DisplayName("INPUT VALUE")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_VAL { get; set; }

        /// <summary>
        ///INPUT_TYPE
        /// </summary>
        [DisplayName("INPUT_TYPE")]
        public string Q_INPUT_TYPE { get; set; }

        /// <summary>
        ///INPUT_JS
        /// </summary>
        [DisplayName("INPUT_JS")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_JS { get; set; }

        /// <summary>
        ///CLASS
        /// </summary>
        [DisplayName("CLASS")]
        public string Q_CLASS { get; set; }

        /// <summary>
        ///PLACEHOLDER
        /// </summary>
        [DisplayName("PLACEHOLDER")]
        public string Q_PLACEHOLDER { get; set; }

        /// <summary>
        ///預設
        /// </summary>
        [DisplayName("預設")]
        public string DEFAULT_VAL { get; set; }

        /// <summary>
        ///必填錯誤訊息
        /// </summary>
        [DisplayName("必填錯誤訊息")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_REQUIRED_MSG { get; set; }

        /// <summary>
        ///限制最小長度
        /// </summary>
        [DisplayName("限制最小長度")]
        public int? Q_LENGTH_MIN { get; set; }

        /// <summary>
        ///限制最大長度
        /// </summary>
        [DisplayName("限制最大長度")]
        public int? Q_LENGTH_MAX { get; set; }

        /// <summary>
        ///regex
        /// </summary>
        [DisplayName("regex")]
        public string Q_REGEX { get; set; }

        /// <summary>
        ///regex 錯誤訊息
        /// </summary>
        [DisplayName("regex 錯誤訊息")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_REGEX_MSG { get; set; }

        /// <summary>
        ///格式不符合的錯誤訊息
        /// </summary>
        [DisplayName("格式不符合的錯誤訊息")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_FORMAT_MSG { get; set; }

        /// <summary>
        ///上傳格式 ex  jpg|gif|png
        /// </summary>
        [DisplayName("上傳格式 ex  jpg|gif|png")]
        public string Q_ACCEPT { get; set; }
        public bool Q_Must { get; set; }
    }
}