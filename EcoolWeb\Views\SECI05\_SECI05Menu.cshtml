﻿@model int

@{
    int activeIDNumber = Model;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

}

<a href="@Url.Action("Index","SECI05")" class="btn btn-sm btn-bold btn-pink  @(activeIDNumber==0? "active":"")">學生閱讀狀況</a>
<a href="@Url.Action("booksorder","SECI02",new { from="SEIC05"})" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==1? "active":"")">借閱排行</a>

@if (user != null && (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher))
{

    if (user.USER_TYPE == UserType.Admin)
    {
        <a href="@Url.Action("BorrowIndex","SECI02", new { from = "SEI05" })" class="btn btn-sm btn-bold btn-pink  colorbox @(activeIDNumber==2? "active":"")">借書統計</a>
    }
    <a href="@Url.Action("_EncourageDiv","SECI05")" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==3? "active":"")">加油名單</a>

    <a href="@Url.Action("CareBookIndex","SECI02", new { from = "SEI05", CLASS_NO = user.TEACH_CLASS_NO })" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==6? "active":"")">關懷清單(借書)</a>
    <a href="@Url.Action("_UnusualListDiv","SECI05")" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==5? "active":"")">異常名單</a>
    <a href="@Url.Action("_ReadingPartialEclipseDiv","SECI05")" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==4? "active":"")">疑似閱讀偏食名單</a>

    <a href="@Url.Action("MonthQty","SECI05",new {from = "SEI05" })" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==7? "active":"")">每月借書變化表</a>

}
<br /><br />