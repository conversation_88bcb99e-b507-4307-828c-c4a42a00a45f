﻿using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    public class CarouselController : Controller
    {
        // GET: Carousel
        public ActionResult Index()
        {

            UserProfileHelper.Set("403605");

            //List<string> WebArr = new List<string>();

            //WebArr.Add(Url.Action("QUERY", "AWA003"));
            //WebArr.Add(Url.Action("OrderList", "ADDI01"));
            //WebArr.Add(Url.Action("ADDTList", "ADDT"));

           
            //string WebStr =   string.Join("','", WebArr.ToArray());

            //string manJS = string.Empty;

            //manJS = @"function man() { " + "\r\n";
            //manJS = manJS+@"Web = new Array('"+ WebStr + "')" + ";\r\n";
            //manJS = manJS + @"k=i % " + WebArr.Count() + ";\r\n";
            //manJS = manJS + @"funAjax(web[k]);"+ "\r\n";
            //manJS = manJS + @"i++;" + "\r\n";
            //manJS = manJS + @"setTimeout('man()', 3000);" + "\r\n";
            //manJS = manJS + @"}";
            //ViewBag.manJS = manJS;

            return View();
        }

        public ActionResult Start()
        {
      
                return PartialView();
         
        }
    }
}