/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/GeneralPunctuation.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"],{8194:[0,0,500,0,0],8195:[0,0,999,0,0],8196:[0,0,333,0,0],8197:[0,0,250,0,0],8198:[0,0,167,0,0],8201:[0,0,167,0,0],8202:[0,0,83,0,0],8211:[300,-249,575,0,574],8212:[300,-249,1150,0,1149],8216:[694,-329,319,58,245],8217:[694,-329,319,74,261],8220:[694,-329,603,110,564],8221:[694,-329,603,38,492],8224:[702,211,511,64,446],8225:[702,202,511,64,446],8230:[171,-1,1295,74,1221],8242:[563,-33,344,35,331]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/GeneralPunctuation.js");
