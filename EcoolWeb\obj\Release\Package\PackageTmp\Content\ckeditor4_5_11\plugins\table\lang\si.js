﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'si', {
	border: 'සීමාවවල විශාලත්වය',
	caption: 'Caption', // MISSING
	cell: {
		menu: 'කොටුව',
		insertBefore: 'පෙර කොටුවක් ඇතුල්කිරිම',
		insertAfter: 'පසුව කොටුවක් ඇතුලත් ',
		deleteCell: 'කොටුව මැකීම',
		merge: 'කොටු එකට යාකිරිම',
		mergeRight: 'දකුණට ',
		mergeDown: 'පහලට ',
		splitHorizontal: 'තිරස්ව කොටු පැතිරීම',
		splitVertical: 'සිරස්ව කොටු පැතිරීම',
		title: 'කොටු ',
		cellType: 'කොටු වර්ගය',
		rowSpan: 'පේළි පළල',
		colSpan: 'සිරස් පළල',
		wordWrap: 'වචන ගැලපුම',
		hAlign: 'තිරස්ව ',
		vAlign: 'සිරස්ව ',
		alignBaseline: 'පාද රේඛාව',
		bgColor: 'පසුබිම් වර්ණය',
		borderColor: 'මායිම් ',
		data: 'Data', // MISSING
		header: 'ශීර්ෂක',
		yes: 'ඔව්',
		no: 'නැත',
		invalidWidth: 'කොටු පළල සංඛ්‍ය්ත්මක වටිනාකමක් විය යුතුය',
		invalidHeight: 'කොටු උස සංඛ්‍ය්ත්මක වටිනාකමක් විය යුතුය',
		invalidRowSpan: 'Rows span must be a whole number.', // MISSING
		invalidColSpan: 'Columns span must be a whole number.', // MISSING
		chooseColor: 'තෝරන්න'
	},
	cellPad: 'Cell padding', // MISSING
	cellSpace: 'Cell spacing', // MISSING
	column: {
		menu: 'Column', // MISSING
		insertBefore: 'Insert Column Before', // MISSING
		insertAfter: 'Insert Column After', // MISSING
		deleteColumn: 'Delete Columns' // MISSING
	},
	columns: 'සිරස් ',
	deleteTable: 'වගුව මකන්න',
	headers: 'ශීර්ෂක',
	headersBoth: 'දෙකම',
	headersColumn: 'පළමූ සිරස් තීරුව',
	headersNone: 'කිසිවක්ම නොවේ',
	headersRow: 'පළමූ පේළිය',
	invalidBorder: 'Border size must be a number.', // MISSING
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Cell spacing must be a positive number.', // MISSING
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Table height must be a number.', // MISSING
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Table width must be a number.', // MISSING
	menu: 'Table Properties', // MISSING
	row: {
		menu: 'Row', // MISSING
		insertBefore: 'Insert Row Before', // MISSING
		insertAfter: 'Insert Row After', // MISSING
		deleteRow: 'Delete Rows' // MISSING
	},
	rows: 'Rows', // MISSING
	summary: 'Summary', // MISSING
	title: 'Table Properties', // MISSING
	toolbar: 'Table', // MISSING
	widthPc: 'percent', // MISSING
	widthPx: 'pixels', // MISSING
	widthUnit: 'width unit' // MISSING
} );
