﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    /// <summary>
    /// APP MENU
    /// </summary>
    public class MenuApiController : ApiBase
    {
        [HttpGet, HttpPost]
        [AllowAnonymous]
        public List<uZZT01> Index()
        {
            UserProfile user = UserProfileHelper.Get();
            List<uZZT01> list = new List<uZZT01>();

            string SCHOOL_NO = string.Empty;
            string USER_NO = string.Empty;
            int? ROLE_TYPE = null;

            if (user != null)
            {
                SCHOOL_NO = user.SCHOOL_NO;
                USER_NO = user.USER_NO;
                ROLE_TYPE = user.ROLE_TYPE;
            }

            var Temp = PermissionService.Permission_GetMenuListQUERY(SCHOOL_NO, USER_NO, ROLE_TYPE);

            if (Temp.Count() > 0)
            {
                var ListTemp = Temp.Where(a => a.APP_USE_YN == "Y").ToList();

                list.AddRange(ListTemp);
            }

            return list;
        }
    }
}