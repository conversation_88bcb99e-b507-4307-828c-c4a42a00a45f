﻿@model ECOOL_APP.EF.ADDT01
@using EcoolWeb.Util;
@using com.ecool.service
@{
    ViewBag.Title = "線上投稿-批閱投稿內容";
    string userName = EcoolWeb.Models.UserProfileHelper.Get().NAME;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    ViewBag.VisibleSendChrisMail = PermissionService.GetPermission_Use_YN("ADDI01", "SendChrisMail", user?.SCHOOL_NO ?? "", user?.USER_NO ?? "");
}

<script src="~/Content/ckeditor/ckeditor.js" nonce="cmlvaw"></script>
<link href="~/Content/css/EzCss.min.css" rel="stylesheet" />
@Html.Partial("_Title_Secondary")

@using (Html.BeginForm("Verify", "ADDI01", FormMethod.Post, new { name = "form1" }))
{
    @Html.AntiForgeryToken()

    var Search = TempData["Search"] as EcoolWeb.Models.ADDI01IndexViewModel;

    @Html.Hidden("BackAction", Search.BackAction)
    @Html.Hidden("OrdercColumn", Search.OrdercColumn)
    @Html.Hidden("whereKeyword", Search.whereKeyword)
    @Html.Hidden("whereUserNo", Search.whereUserNo)
    @Html.Hidden("whereWritingStatus", Search.whereWritingStatus)
    @Html.Hidden("whereShareYN", Search.whereShareYN)
    @Html.Hidden("whereComment", Search.whereComment)
    @Html.Hidden("whereCommentCash", Search.whereCommentCash)
    @Html.Hidden("whereCLASS_NO", Search.whereCLASS_NO)
    @Html.Hidden("whereGrade", Search.whereGrade)
    @Html.Hidden("Page", Search.Page)
    @Html.HiddenFor(m => m.USER_NO)

    <img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Pink">
        <div class="form-horizontal">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })

            @Html.HiddenFor(model => model.WRITING_NO)

            <div class="form-group">
                @Html.LabelFor(model => model.CRE_DATE, htmlAttributes: new { @class = "control-label col-md-2 col-sm-2" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime", new { htmlAttributes = new { @class = "form-control" } })
                </div>

                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2 col-sm-2" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.CLASS_NO, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                @Html.LabelFor(model => model.NAME, htmlAttributes: new { @class = "control-label col-md-2 col-sm-2" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.NAME, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-2 col-sm-2" })
                <div class="col-md-6  col-sm-6" style="padding-top:7px">
                    @Html.DisplayFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.HiddenFor(model => model.SUBJECT)
                </div>
                @Html.LabelFor(model => model.SEAT_NO, htmlAttributes: new { @class = "control-label col-md-2 col-sm-2" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.SEAT_NO, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("是否出現作者姓名", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6" style="padding-top:7px">
                    @Html.RadioButtonFor(model => model.AutherYN, false, new { @id = "AutherYN" })
                    @Html.Label("出現作者姓名")
                    @Html.RadioButtonFor(model => model.AutherYN, true, new { @id = "AutherYN" })
                    @Html.Label("不出現作者姓名")
                </div>
            </div>
            <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12">
                詳細內容:限制1500字，目前字數為<span id="ShowFontLen" style="color:red"> </span>字
            </label>
            <div class="form-group">
                @Html.LabelFor(model => model.ARTICLE, htmlAttributes: new { @class = "control-label col-md-2 col-sm-2" })

                <div class="col-md-11 col-sm-11" style="margin:10px">
                    @Html.TextAreaFor(model => model.ARTICLE, new { cols = "200", rows = "25", @class = "ckeditor", style = "white-space: pre-line",@onchange= "KeyIn()" })
                    @Html.ValidationMessageFor(model => model.ARTICLE, "", new { @class = "text-danger" })
                </div>
            </div>
            @if (string.IsNullOrWhiteSpace(Model.IMG_FILE) == false)
            {
                <div class="form-group">
                    <span class="control-label col-md-2 col-sm-3">上傳圖檔</span>
                    <div class="text-center">
                        <br />
                        <br />
                        <br />
                        @if (ViewBag.ImageUrl != null && Enumerable.Any(ViewBag.ImageUrl))
                        {
                            int Num = 1;
                            foreach (var Img in ViewBag.ImageUrl as List<string>)
                            {
                                @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = Img, ImgID = "ImageX" + Num })
                                <img id="@("ImageX"+ Num)" src='@Img' style="max-height:250px;width:auto;margin-left:auto;margin-right:auto;padding-bottom:10px" href="@Img" class="img-responsive " />
                                Num = Num + 1;
                            }
                        }
                    </div>
                </div>
            }
            else
            {

                <div class="form-group">
                    <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">上傳圖檔</span>
                    <div class="col-md-9 col-sm-9 col-lg-10">
                        <div class="form-group">
                            <div class="uploadfiles">
                                <input class="btn btn-default" type="file" name="files" disabled />
                            </div>
                            <button class="btn btn-default" id="addfile">增加檔案</button>
                        </div>

                    </div>
                </div>

            }

            <div class="form-group">

                @Html.LabelFor(model => model.YOUTUBE_URL, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-12 " style="padding-top:7px">
                    <div class="col-md-10">

                        <div class="input-group">
                            @Html.EditorFor(model => model.YOUTUBE_URL, new { htmlAttributes = new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.YOUTUBE_URL) } })
                            <span class="input-group-btn">
                                <button class="btn btn-info" type="button" id="CheckYoutube">檢查網址</button>
                            </span>
                        </div><!-- /input-group -->

                        @Html.ValidationMessageFor(model => model.YOUTUBE_URL, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>



            @if (string.IsNullOrEmpty(ViewBag.VoiceUrl) == false)
            {
                <div class="form-group">
                    <span class="control-label col-md-2 col-sm-3">上傳錄音</span>
                    <div class="col-md-9  col-sm-6" style="padding-top:7px">
                        @if (Request.Browser.Browser == "InternetExplorer")
                        {
                            <object id="Player"
                                    classid="CLSID:6BF52A52-394A-11d3-B153-00C04F79FAA6" width="300" height="45">
                                <param name="autoStart" value="false">
                                <param name="URL" value="@ViewBag.VoiceUrl">
                            </object>
                        }
                        else
                        {
                            <audio width="300" height="48" controls="controls">
                                <source src="@ViewBag.VoiceUrl" />
                            </audio>
                        }
                    </div>
                </div>
            }
            <div class="form-group">
                <span class="control-label label_dt col-md-3col-sm-3 col-lg-2">上傳PPT或pdf(大小限制6MB)</span>
                <div class="col-md-12 col-sm-12 col-lg-10" style="padding-left:30px" disabled="disabled">
                    <div class="form-group">
                        <div class="uploadfiles2">
                            <input class="btn btn-default" type="file" name="files2" accept=".ppt,.pptx,.pdf,.PDF" disabled />
                        </div>

                    </div>
                </div>
                @if (string.IsNullOrWhiteSpace(Model.Upload_FILE) == false)
                {

                    <span class="control-label col-md-2 col-sm-3">上傳其他檔案</span>
                    <div class="text-center">
                        <br />
                        <br />
                        <br />
                        @if (ViewBag.OtherFilesUrl != null && Enumerable.Any(ViewBag.OtherFilesUrl))
                        {
                            int Num = 1;
                            foreach (var OtherFiles in ViewBag.OtherFilesUrl as List<string>)
                            {
                                string name = Path.GetFileName(OtherFiles);
                                <div class="col-md-12 col-sm-12 col-xs-12 " style="text-align:center">
                                    <br />
                                    <a href="@OtherFiles" class="btn btn-link btn-lg"><span class="glyphicon glyphicon-download-alt"></span> @name</a>
                                    <br /><br />
                                </div>
                                Num = Num + 1;
                            }
                        }
                    </div>

                }
            </div>
            <div class="form-group">
                &nbsp;&nbsp;
                @Html.LabelFor(model => model.VERIFY_COMMENT, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3", @style = "padding-top:30px" })
                <div class="col-md-10  col-sm-9">
                    @Html.DropDownList("VERIFY_COMMENT_DropDownList", (IEnumerable<SelectListItem>)ViewBag.VCommentSelectItem, new { @class = "form-control", @onchange = "VCommentDropDownList(this.value)" })
                    @Html.HiddenFor(model => model.VERIFY_COMMENT)
                </div>
            </div>

            @*<div class="form-group">
                    @Html.LabelFor(model => model.VERIFY_COMMENT, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                    <div class="col-md-9  col-sm-6" style="padding-top:7px">
                        @Html.EditorFor(model => model.VERIFY_COMMENT, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                </div>*@
            <div class="form-group">
                <div class="control-label col-md-2 col-sm-3">
                    @Html.LabelFor(model => model.CASH)
                    <span style="color:red">*</span>
                </div>
                <div class="col-md-10  col-sm-9" style="padding-top:7px">
                    @foreach (short c in ViewBag.CashArray)
                    {
                        if (c == 0)
                        {
                            @:
                            <br />
                        }
                        @Html.RadioButtonFor(model => model.CASH, c)
                        @Html.Label(c.ToString())
                        @:&nbsp
                    }
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.SHARE_YN, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6" style="padding-top:7px">

                    @{ var checkBoxs1 = new List<CheckBoxListInfo>
                                                                                              ();
                        CheckBoxListInfo chebox1 = new CheckBoxListInfo();
                        chebox1.DisplayText = "推薦";
                        chebox1.Value = "y";
                        chebox1.IsChecked = Model.SHARE_YN == "y" ? true : false;
                        checkBoxs1.Add(chebox1);
                        var htmlAttribute1 = new Dictionary<string, object>
                            ();
                        htmlAttribute1.Add("id", "SHARE_YN");}
                    @*@Html.RadioButtonFor(model => model.SHARE_YN, "y", new { htmlAttributes = new { @class = "form-control" } })
                        @Html.Label("推薦")*@
                    @Html.CheckBoxList("SHARE_YN", (List<CheckBoxListInfo>)checkBoxs1, htmlAttribute1, 1)
                </div>
            </div>

            @*@{
                    var checkBoxs = new List<CheckBoxListInfo>();
                    CheckBoxListInfo chebox = new CheckBoxListInfo();
                    chebox.DisplayText = "是 <br />五六年級學生文章會直接投稿到edit10 @mdnkids.com。<br />一到四年級學生文章會直接投稿到*****************。";
                    chebox.Value = "Y";
                    chebox.IsChecked = Model.PUBLISH_MDNKIDS_YN == "Y" ? true : false;
                    checkBoxs.Add(chebox);
                    var htmlAttribute = new Dictionary<string, object>();
                    htmlAttribute.Add("id", "PUBLISH_MDNKIDS_YN");
                }*@
            @*<div class="form-group" style="">
                    @Html.LabelFor(model => model.PUBLISH_MDNKIDS_YN, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })

                    <div class="col-md-9 col-sm-6" style="padding-top:7px">
                        @Html.CheckBoxList("PUBLISH_MDNKIDS_YN", (List<CheckBoxListInfo>)checkBoxs, htmlAttribute, 1)
                    </div>
                </div>*@
            @if (ViewBag.VisibleSendChrisMail == SharedGlobal.Y)
                {

                    var checkBoxsCHRIS = new List<CheckBoxListInfo>();
                    CheckBoxListInfo cheboxCHRISItem = new CheckBoxListInfo();
                    cheboxCHRISItem.DisplayText = " ※<font color=\"#FF0000\">不會</font>真的寫電子信給聖誕老公公，只會將本文標誌和聖誕老公公有關，方便學校辦理活動匯出。(這是學校聖誕活動專用選項)";
                    cheboxCHRISItem.Value = "Y";
                    cheboxCHRISItem.IsChecked = Model.PUBLISH_CHRIS_YN == "Y" ? true : false;
                    checkBoxsCHRIS.Add(cheboxCHRISItem);
                    var htmlAttributeCHRIS = new Dictionary<string, object>();
                    htmlAttributeCHRIS.Add("id", "PUBLISH_CHRIS_YN");


                    <div class="form-group">
                        @Html.LabelFor(model => model.PUBLISH_CHRIS_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })

                        <div class="col-md-9 col-sm-6" style="padding-top:7px">
                            @Html.CheckBoxList("PUBLISH_CHRIS_YN", (List<CheckBoxListInfo>)checkBoxsCHRIS, htmlAttributeCHRIS, 1)
                        </div>
                    </div>
                }
            @*<div class="form-group">
                      @Html.LabelFor(model => model.PUBLISH_CHRIS_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })
                      @Html.Label("(測試中)")
                      <div class="col-md-12 col-sm-12 col-lg-10">
                          @Html.EditorFor(model => model.PUBLISH_CHRIS_YN,
                 "_CheckBoxList",
                new
                {
                       TagName = (Html.NameFor(model => model.PUBLISH_CHRIS_YN)).ToHtmlString(),
                         DisplayText = "A",
                              CheckBoxItems = HtnlHelperService.Y_SelectItem(Model != null ? Model.PUBLISH_CHRIS_YN : ""),
                                   Position = Position.Horizontal,
                                   Numbers = int.MaxValue,
                                   onclick = "",
                               })
                          <br />
                          @Html.ValidationMessageFor(model => model.PUBLISH_CHRIS_YN, "", new { @class = "text-danger" })
                      </div>
                  </div>*@

            @*<div id="mdnInfoDiv" style="@(chebox.IsChecked? "":"display:none;")">
                    @Html.Partial("__MdnKidsModal", Model)
                </div>*@
            @if (ViewBag.CashAlert != null)
            {
                <div class="alert alert-danger" style="padding-top:7px;padding-left:15px">
                    @ViewBag.CashAlert
                </div>

            }
            <div>
                <div class="text-center">
                    @if (AppMode)
                    {
                        <button value="Save" class="btn2 btn-default">
                            通過審核
                        </button>

                    }
                    else
                    {
                        <button value="Save" class="btn btn-default">
                            通過審核
                        </button>
                    }

                    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;

                    <a href='#' onclick="DoGO('@Url.Action(Search.BackAction)')" role="button" class=" btn btn-default">
                        返回
                    </a>
                </div>
            </div>
            @if (ViewBag.VisableDelete == true || ViewBag.VisableDisableUpSetDraft == true)
            {
                <div class="form-group">
                    &nbsp;&nbsp;
                    @Html.LabelFor(model => model.BACK_MEMO, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3", @style = "padding-top:30px" })
                    <div class="col-md-10  col-sm-9" style="padding-top:10px">
                        @Html.DropDownList("BACK_MEMO_DropDownList", (IEnumerable<SelectListItem>)ViewBag.BackSelectItem, new { @class = "form-control", @onchange = "BackDropDownList(this.value)" })
                        @Html.HiddenFor(model => model.BACK_MEMO)
                    </div>
                </div>
                <div>

                    <div class="text-center">
                        @if (ViewBag.VisableDisableUpSetDraft == true)
                        {
                            <p style="color:#ad3c3c">※ 請多使用鼓勵的建議或在如何改進上多指導喔</p>
                            <a href="#" role="button" value="DisableUpSetDraft" onclick="DisableGO(this, '@Url.Action("DisableUpSetDraft")')" class="btn2 btn-default">
                                退回再修
                            </a>
                        }

                        @if (ViewBag.VisableDelete == true)
                        {
                            <samp>&nbsp;&nbsp;&nbsp;</samp>

                            <a href="#" role="button" value="DisableUpSetDraft" onclick="DisableGO(this,'@Url.Action("Disable")')" class="btn2 btn-default">
                                直接作廢
                            </a>
                        }
                    </div>
                </div>
            }
        </div>
    </div>

}
<div hidden="hidden" id="notice">
    <span>作品經採用刊登將同時刊於國語日報,國語日報網站及相關行動載具及臉書,並收錄國語日報</span></br>

    <span style="color:red;">
        請勿抄襲(投稿圖文需原創,如有侵權行為,由創作者自負法律責任),及一稿多投,
    </span>
    <span>
        本報
        得對來稿有刪修權,投稿三週內若未通知採用,請自行處理。(文字引自於國語日報網站)
    </span>
</div>
<div class="modal fade bs-example-modal-lg " tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">
                    你確定要「退回/作廢」這篇文章?<br />
                    <button class="btn btn-default" type="button" onclick="btnR("Y");" id="BtnSave">是</button>
                    <button class="btn btn-default" type="button" onclick="btnR("N");" id="BtnSave">否</button>
                </h4>
            </div>
        </div>
    </div>
</div>
@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局配置
        window.ADDI01_CONFIG = {
            otherValue: "@ECOOL_APP.com.ecool.service.BDMT02Service.OtherVal",
            userName: "@userName"
        };

        // 設置全局 URL 配置
        window.ADDI01_URLS = {
            getUrlArgument: "@Url.Action("GetUrlArgument", "ADDI12")"
        };
    </script>
    <script src="~/Scripts/ADDI01/verify.js" nonce="cmlvaw"></script>
}