﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SECI05ReadingPartialViewModel
    {
        public string WhereSEYEAR { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        public string WhereCLASS_NO { get; set; }

        public List<SECI05ReadingPartial> ReadingPartialList { get; set; }

    }

    public class SECI05ReadingPartial
    {

        public int TYPE_ID { get; set; }
        [DisplayName("類別")]
        public string TYPE_NAME { get; set; }
        public string NO_READ { get; set; }
        [DisplayName("學號")]
        public string USER_NO { get; set; }
        [DisplayName("姓名")]
        public string NAME { get; set; }
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }
        [DisplayName("閱讀偏食比率")]
        public double RATE { get; set; }
        [DisplayName("在校總借書量")]
        public int SubQty { get; set; }
    }
}
