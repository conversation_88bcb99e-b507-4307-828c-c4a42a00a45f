﻿@{
    List<string> ImageLinkList = new List<string>();
    ImageLinkList.Add("web-student-allpage-27.png");
    ImageLinkList.Add("web-student-allpage-29.png");
    ImageLinkList.Add("web-student-allpage-30.png");
    ImageLinkList.Add("web-student-allpage-31.png");
    ImageLinkList.Add("web-student-allpage-32.png");
    ImageLinkList.Add("web-student-allpage-28.png");
    ImageLinkList.Add("web-student-allpage-33.png");
    ImageLinkList.Add("web-student-allpage-34.png");
    ImageLinkList.Add("web-student-allpage-35.png");
    ImageLinkList.Add("web-student-allpage-36.png");

    int i = 1;
}
<script type='text/javascript' src='~/Scripts/jquery.simplemodal.js'></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
<script src="~/Scripts/moment.js"></script>

<br />


<table class="table-ecool table-92Per table-hover table-ecool-reader"  style="table-layout:fixed">
    <thead>
        <tr>
            <th style="text-align: center;background-color:rgba(227,236,159,.2);">
                等級獎章
                <br />
            </th>
            <th style="text-align: center;background-color:rgba(227,236,159,.2);">
                等級名稱
                <br />
            </th>
            <th style="text-align: center;background-color:rgba(227,236,159,.2);">
                閱讀本數
                <br />
            </th>
            <th style="text-align: center;background-color:rgba(227,236,159,.2);">
                升級時，額外獎勵<br />
                (系統自動給點)
                <br />
            </th>
        </tr>
    </thead>

    @{IEnumerable<ADDT08> ADDT08 = (IEnumerable<ADDT08>)ViewBag.ADDT08; }
    @{IEnumerable<ADDT07> ADDT07 = (IEnumerable<ADDT07>)ViewBag.ADDT07;

        int j = ADDT08.Count();

        }
    
    @foreach (var item in ADDT08)
    {


        ADDT07 a7 = ADDT07.Where(p => p.LEVEL_ID == item.LEVEL_ID).FirstOrDefault();
        short? sLEVEL_QTY = 0;
        short? LEVELCash = 0;
        if (a7 != null)
        {
            sLEVEL_QTY = a7.LEVEL_QTY;
            LEVELCash = a7.TO_CASH;
        }
       
        <tbody>
            <tr>
                <td style="text-align: center;cursor:pointer; vertical-align: middle;border-bottom-width:12px; ">
                    <img src="~/Content/img/@ImageLinkList[i-1]" style="max-height:35px;  margin: 0 auto;" class="img-responsive " alt="Responsive image" />
                    @if (i != j)
                    {

                        <br />

                    }


                </td>
                <td style="text-align: center;cursor:pointer; vertical-align: middle;border-bottom-width:12px;">
                    @item.LEVEL_DESC
                    @if (i != j)
                    {

                        <br />

                    }


                </td>
                <td style="text-align: center;cursor:pointer; vertical-align: middle;border-bottom-width:12px;">
                    @sLEVEL_QTY
                    @if (i != j)
                    {

                        <br />

                    }


                </td>
                <td style="text-align: center;cursor:pointer; vertical-align: middle;border-bottom-width:12px;">
                    @(LEVELCash + "點")

                    @if (i != j)
                    {

                        <br />

                    }


                </td>
            </tr>
        </tbody>
        i++;
    }
</table>
