﻿@model EcoolWeb.ViewModels.AWA15ModifyQueryViewModel

@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ViewBag.Title = "酷幣點數升級名單-升級名單";
    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    int i = 0;
    string iUser_no = (user != null) ? user.USER_NO : "";

    string role = Model.RoleName;
    string student = "Student";
    string teacher = "Teacher";

    bool BoolbtnTRANS_NO =
        role == student ?
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("AWA004", "ModifyQuery", null) :
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("AWA006", "ModifyQuery", null);

    bool BoolbtnModifyQuery =
        role == teacher ?
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("ModifyQuery", "ModifyQuery", null) :
        System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("ModifyQuery", "ModifyQuery", null);
}

@helper  buttonFun(string SchoolNO)
{

    string STATUS_Receive = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive.ToString()) ? "active" : "";
    string STATUS_UN = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString()) ? "active" : "";
    string STATUS_Del = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del.ToString()) ? "active" : "";

    <div class="row">
        <div class="col-md-12 col-xs-12 text-right">
            <samp>兌換狀態：</samp>
            <button class="btn btn-xs btn-pink  @STATUS_UN" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN');doSearch('whereUserNo', '');">未領取</button>
            <button class="btn btn-xs btn-pink  @STATUS_Receive" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive');doSearch('whereUserNo', '');">已頒發</button>
            @*<button class="btn btn-xs btn-pink  @STATUS_Del" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del');doSearch('whereUserNo', '');">已取消</button>*@
        </div>
    </div>





    <br />

}

@section css{
    <link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
    <style>
        .orderColumn {
            color: #6badcf
        }

        .mybox {
            height: 2rem;
            width: auto;
        }

        .imgCol {
            display: none;
        }
    </style>
}

@section scripts{
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>

    <script type="text/javascript">
        var targetFormID = '#AWAT14';

        $(document).ready(function () {
            $(".mybox").colorbox({ opacity: 0.82 });
        });

        function btnTRANS_NO_onclick(TRANS_NO) {
            var yes = confirm('你確認要取消領取嗎?');
            if (yes) {
            $("#hidSelectTRANS_NO").val(TRANS_NO);
                document.AWAT14.enctype = "multipart/form-data";
                document.AWAT14.action = "CancelTrans";
                document.AWAT14.submit();
            }
        }
        function btnSend_onclick() {
            document.AWAT14.enctype = "multipart/form-data";
            document.AWAT14.action = "Modify";
            document.AWAT14.submit();
        }
        function PrintSetView() {
            if ($(".css-checkbox:checked").length == 0) {
                alert('請勾選要通知的人員')
                return false;
            }
            else {

                document.AWAT14.enctype = "multipart/form-data";
                document.AWAT14.action = "PrintSetView";
                document.AWAT14.submit();
            }
        }
        function exportExcel() {
           if ($(".css-checkbox:checked").length == 0) {
                alert('請勾選要通知的人員')
                return false;
            }
            else {

               document.AWAT14.enctype = "multipart/form-data";
               document.AWAT14.action = "ExportExcel";
               document.AWAT14.submit();
            }
        }
        //↓出自：http://stackoverflow.com/questions/1219860/javascript-jquery-html-encoding
        function htmlEncode(value) {
            return $('<div />').text(value).html();
        }

        $("#chkALL").click(function () {

            if ($("#chkALL").prop("checked")) {
                $("input:checkbox").each(function () {
                    if ($(this).attr("id") != 'cbPicture') {

                        $(this).prop("checked", true);
                    }

                });
            }
            else {
                $("input:checkbox").each(function () {
                    if ($(this).attr("id") != 'cbPicture') {
                        $(this).prop("checked", false);
                    }
                });
            }
        });

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function todoClear() {
            ////重設

            $(targetFormID).find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });
            @if(role == teacher) {
                <text>
                    $("#RoleName").val("Teacher")
                </text>
            }

            $(targetFormID).submit();
        }
    </script>

}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
    <div id='container' style="">
        @{
            Html.RenderAction("_PageMenu", new { NowAction = "ModifyQuery" });
        }
    </div>
@using (Html.BeginForm("ModifyQuery", "AWAT14", FormMethod.Post, new { name = "AWAT14", id = "AWAT14" }))
{
    @Html.HiddenFor(m => m.OrderColumn)
    @Html.HiddenFor(m => m.SortBy)
    @Html.HiddenFor(m => m.Page)

    @Html.HiddenFor(m => m.whereSNAME)
    @Html.HiddenFor(m => m.whereAWARD_NAME)
    @Html.HiddenFor(m => m.whereSTATUS)
    @Html.Hidden("hHtml")
    @Html.Hidden("hidSelectTRANS_NO")
    @Html.HiddenFor(m => m.RoleName)

    <div class="form-inline">
        <div class="form-inline" role="form">

            @if (Request["Awat"] == "Awat_Key")
            {
                <div class="form-group">
                    <label class="control-label">升級ID:@Model.whereAWAT15NO</label>
                    @Html.HiddenFor(m => m.whereAWAT15NO)
                </div>
                <br />
            }



            <div class="form-group">

                <label class="control-label">學號/姓名</label>


            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>


            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">級數</label>
            </div>

            <div class="form-group">
                @Html.EditorFor(m => m.WhereRankNO, new { htmlAttributes = new { id = "WhereRankNO", @class = "form-control input-sm", autocomplete = "off", Placeholder = "請輸入級數的數字" } })
            </div>

            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
    </div>

    @buttonFun(SchoolNO)
    <img src="~/Content/img/web-bar2-revise-78.png" style="width:100%" class="img-responsive App_hide" alt="" />

    <div class="table-responsive">
        <div class="text-center">

            @{ int unrestatus = (int)AWAT15.TRANS_STATUS_Val.TRANS_STATUS_UN;


            }
            <table class="table-ecool table-92Per table-hover table-ecool-AWA004" id="OutList">
                <thead>
                    <tr>
                        @if (Model.whereSTATUS == unrestatus.ToString())
                        {
                            <th>
                                全選 <input type="checkbox" id="chkALL" name="chkALL" />
                            </th>
                        }
                        else
                        {
                            <th>
                                處理日期
                            </th>
                            }

                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SYEAR';document.forms[0].submit()">
                            學年
                        </a>
                    </th>
                    <th>
                        學期
                    </th>

                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='CLASS_NO';document.forms[0].submit()">
                            班級
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SEAT_NO';document.forms[0].submit()">
                            座號
                        </a>
                    </th>
                    <th>
                        姓名
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='CASH_Rank';document.forms[0].submit()">
                            等級
                        </a>
                    </th>
                        <th>

                            <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='CASH_ALL';document.forms[0].submit()">
                                累積點數
                            </a>
                            
                        </th>
                    <th>
                        狀態
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='CreatDate';document.forms[0].submit()">
                            升級日期
                        </a>
                    </th>
      
                  

                    <th>
                        備註
                    </th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model.AWAT15List)
                        {
                            //string imgSrc = ViewBag.ImgUrl + user.SCHOOL_NO + @"/" + item.IMG_FILE;
                            @Html.HiddenFor(modelItem => item.CLASS_NO)
                    <tr>
                        @if (Model.whereSTATUS == unrestatus.ToString())
                        {
                            EcoolWeb.ViewModels.AWAT15PrintViewModel Chk_Item = new EcoolWeb.ViewModels.AWAT15PrintViewModel();
                            Chk_Item.AWAT15 = item;
                            if (Model.Chk != null)
                            {
                                Chk_Item.CheckBoxNo = Model.Chk.Where(a => a.AWAT15.AWAT15NO == item.AWAT15NO).Select(a => a.CheckBoxNo).FirstOrDefault();
                            }
                            <td>
                                @Html.Partial("_ModifyView", Chk_Item)
                            </td>
                        }
                        else
                        {

                            <td>
                                <font color="red">   @Html.DisplayFor(modelItem => item.GOT_DATE, "ShortDateTime")</font>
                            </td>

                                }
                            <td style="text-align: center;font-size:13px">
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td style="text-align: center;font-size:13px">
                                @Html.DisplayFor(modelItem => item.SEMESTER)
                            </td>

                            <td style="text-align: center;font-size:13px;text-align: center;cursor:pointer;">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td style="text-align: center;font-size:13px">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            @if (item.SNAME.Trim() == "")
                            {
                                <td style="text-align: center;font-size:12px">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                            }
                            else
                            {
                                <td style="text-align: center;font-size:13px;white-space:nowrap;cursor:pointer;" >
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>
                            }

                            <td style="text-align: center;font-size:13px">
                                @Html.DisplayFor(modelItem => item.CASH_Rank)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.CASH_ALL)
                            </td>
                            <td style="text-align: center;font-size:13px">
                                @if (item.IsReMark == 0)
                                {
                                    @:已升級未領獎
                                }
                                else if (item.IsReMark == 1)
                                {
                                    @:已領獎

                                }
                                else if (item.IsReMark == 9)
                                {
                                    @:取消頒獎

                                }
                            </td>
                            @*<td style="font-size:13px;text-align: left;white-space:normal;cursor:pointer;" onclick="doSearch('whereAWARD_NAME','@item.AWARD_NAME');">
                @Html.DisplayFor(modelItem => item.AWARD_NAME)
            </td>
            <td style="font-size:13px">
                @Html.DisplayFor(modelItem => item.COST_CASH)
            </td>
            <td style="font-size:13px">
                @Html.DisplayFor(modelItem => item.CTRANS_STATUS)
            </td>*@
                            <td style="font-size:13px">
                               
                                @Html.DisplayFor(modelItem => item.CreatDate, "ShortDateTime")
                            </td>
                           
                            @*<td class="imgCol">
                <div>
                    <img src="@imgSrc" href="@imgSrc" class="imgSmall mybox" />
                </div>
            </td>*@
                            <td style="font-size:13px">
                                @Html.DisplayFor(modelItem => item.MEMO)
                                @*2"已訂未領"1"已領取"*@
                                @if (item.IsReMark == 1 )
                                {
                                    <input type="button" value="取消頒獎" onclick="btnTRANS_NO_onclick('@item.AWAT15NO');" class="btn btn-xs btn-Basic">
                                }
                            </td>
                        </tr>
                        i++;
                    }


                </tbody>
            </table>
            @if (role == student)
            {
                if (Model.AWAT15List.Count() == 0)
                {
                    <div style="margin-top:15px;margin-bottom:5px">
                        <div class="text-danger">查無任何資料需要處理</div>
                    </div>
                }
            }

        </div>
    </div>
    <div>

        @Html.Pager(Model.AWAT15List.PageSize, Model.AWAT15List.PageNumber, Model.AWAT15List.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
        .MaxNrOfPages(5)
        .SetPreviousPageText("上頁")
        .SetNextPageText("下頁"))


    </div>

    if (Model.AWAT15List?.Count() > 0)
    {
        <div style="height:30px"></div>
        <div class="form-group Div-btn-center">
            @if (BoolbtnModifyQuery && Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString())
            {
                <div class="col-md-offset-3 col-md-3">
                    <input type="button" id="btnSend2" value="設定為已兌換" class="btn btn-default" onclick="btnSend_onclick();" />
                </div>
            }
            <div class="col-md-6">
                @if (BoolbtnModifyQuery && Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString())
                {
                    <input type="button" id="btnExcel" value="線上列印通知" class="btn btn-default" onclick="PrintSetView();" />
                    <input type="button" id="btnExcel" value="匯出Excel" class="btn btn-default" onclick="exportExcel();" />
                }
            </div>
        </div>
        <div id="Ex" style="display:none">
        </div>
                    }

                }
