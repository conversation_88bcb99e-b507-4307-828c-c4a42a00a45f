@import "../../node_modules/bootstrap/scss/functions";
@import "../../node_modules/bootstrap/scss/variables";
@import "../../node_modules/bootstrap/scss/mixins";

//基礎設置
body {
    font-family: "微軟正黑體",
        // Safari for OS X and iOS (San Francisco)
        -apple-system,
        // Chrome < 56 for OS X (San Francisco)
        BlinkMacSystemFont,
        // Windows
        "Segoe UI",
        // Android
        "Roboto",
        // Basic web fallback
        "Helvetica Neue",
        Arial,
        sans-serif,
        // Emoji fonts
        "Apple Color Emoji",
        "Segoe UI Emoji",
        "Segoe UI Symbol";
    color: #000;
}

h1 {
    margin: 0;
    padding: 0;
}

// 酷幣環境影響抵銷
.row {
    margin: 0;
}

$imgURL:'../images/';


//全排行的手機動畫 3秒停留 輪播滑動
@keyframes slideUpDown2 {
    0% {
        transform: translateY(0px);
    }

    15% {
        transform: translateY(0px);
    }

    30% {
        transform: translateY(-12%);
    }

    60% {
        transform: translateY(-12%);
    }

    75% {
        transform: translateY(-55%);
    }

    100% {
        transform: translateY(-55%);
    }
}

//頁面背景色
.leaderboard-page-bg {
    &-cold {
        background: linear-gradient(left, rgba(190,247,229,0.2) 0%,rgba(190,247,229,0.2) 50%,rgba(190,247,229,0) 50%,rgba(190,247,229,0) 100%),
        linear-gradient(#34C4DB 0%, #C7FFFB 45%, #D6FFF7 70%, #25C5D1 100%);
        overflow: hidden;
        @include media-breakpoint-down(md) {
            background: linear-gradient(#34C4DB 0%, #C7FFFB 25%, #D6FFF7 35%, #25C5D1 50%, #C7FFFB 75%, #D6FFF7 85%, #25C5D1 100%);
        }

    }

    &-warm {
        background: linear-gradient(left, rgba(247, 190, 190, 0.2) 0%,rgba(247, 190, 190, 0.2) 50%,rgba(247, 190, 190, 0) 50%,rgba(247, 190, 190, 0) 100%),
        linear-gradient(#FFAB17 0%, #FFFBC7 45%, #FFF4D6 80%, #FFE7BE 100%);
        overflow: hidden;
        @include media-breakpoint-down(md) {
            background: linear-gradient(#FFAB17 0%, #FFFBC7 25%, #FFF4D6 40%, #FFE7BE 48%, #FFAB17 55%, #FFFBC7 85%, #FFF4D6 100%);
        }
    }

    &-merge {
        background: linear-gradient(#FFAB17 5%, #FFFBC7 30%, #FFF4D6 40%, #D6FFF7 60%, #C7FFFB 80%, #25C5D1 100%);
        overflow: hidden;
        @include media-breakpoint-down(md) {
            background-attachment: fixed;
            animation-name: slideUpDown2;
            animation-duration: 20s;
            animation-iteration-count: infinite;
            animation-direction: alternate;
        }
    }
}

//光束
.leaderboard-sunlight-small {
    padding: 0;
    background-image: url(#{$imgURL}bg-sunlight.png);
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 101% 49%;
}

//閱讀跟酷幣排行的手機動畫 3秒停留 輪播滑動
@keyframes slideUpDown {
    0% {
        transform: translateY(0px);
    }

    30% {
        transform: translateY(0px);
    }

    70% {
        transform: translateY(-46%);
    }

    100% {
        transform: translateY(-46%);
    }
}

//頁面垂直置中
.leaderboard-page-layout {
    min-height: 100vh;
    padding: 0 2.5rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;

    @include media-breakpoint-down(md) {
        padding: 2rem;
        animation-name: slideUpDown;
        animation-duration: 10s;
        animation-iteration-count: infinite;
        animation-direction: alternate;
    }

    >div {
        flex: 1 1 100%;
    }
}

//排行榜標題
.title {
    display: block;
    width: 100%;
    height: 20vh;
    background-repeat: no-repeat, no-repeat;
    position: absolute;
    z-index: 4;

    @include media-breakpoint-down(md) {
        margin-top: 2rem;
        height: 13rem;
    }

    //累計酷幣
    &-total {
        margin-left: -.5rem;
        background-image: url(#{$imgURL}Leaderboard-title-total.png),
        url(#{$imgURL}shadow.png);
        background-position: top 0 left 0, top 1.7rem left 3rem;
        background-size: auto 89.4%, auto 86%;
    }

    //現有總資產
    &-existing {
        margin-right: -.5rem;
        background-image: url(#{$imgURL}Leaderboard-title-existing.png),
        url(#{$imgURL}shadow.png);
        background-position: top 0 right 0, top 1.7rem right 3rem;
        background-size: auto 89.4%, auto 86%;
    }

    //閱讀認證
    &-read {
        margin-left: -.5rem;
        background-image: url(#{$imgURL}Leaderboard-title-read.png),
        url(#{$imgURL}shadow.png);
        background-position: top 0 left 0, top 1.7rem left 3rem;
        background-size: auto 89.4%, auto 86%;
    }

    //運動撲滿
    &-sports {
        margin-right: -.5rem;
        background-image: url(#{$imgURL}Leaderboard-title-sports.png),
        url(#{$imgURL}shadow.png);
        background-position: top 0 right 0, top 1.7rem right 3rem;
        background-size: auto 89.4%, auto 86%;
    }

    &-ecool {
        background-image: url(#{$imgURL}Leaderboard-title-ecool.png),
        url(#{$imgURL}shadow.png);
        background-position: center, center;
        background-size: auto 89.4%, auto 86%;
    }
}

//上方前3位排行榜區塊
.leaderboard {
    &-item {
        flex: 1 1 40%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 14vh;
        margin: 1.3rem;
        padding: 0.5rem 0 0 0;
        list-style-type: none;
        background: #fff;
        border: .725rem solid #eecc0c;
        border-radius: 4.5rem;
        text-align: center;
        box-shadow: 0 1rem 1rem rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;

        @include media-breakpoint-down(md) {
            padding-top: 0;
            min-height: 11rem;
            border-width: .5rem;
            border-radius: 3rem;
        }

        @include media-breakpoint-down(sm) {
            min-height: 8rem;
        }

        //皇冠插圖
        &::before {
            content: "";
            display: block;
            height: 10vh;
            background-image: url(#{$imgURL}Leaderboard-crown.png);
            background-repeat: no-repeat;
            background-position: center, center;
            background-size: auto 8.5vh;
            position: absolute;
            top: -8vh;
            left: 0;
            right: 0;
            z-index: 2;
        }

        &:first-child {
            flex: 0 1 48%;
            margin: 0 21%;
            z-index: 1;

            //第一名的皇冠大一點
            &::before {
                background-size: auto 9.5vh;
                top: -9vh;
                z-index: 1;
            }

            //固定第一名框下方t8 插圖(小錢堆)
            &::after {
                content: "";
                display: block;
                height: 5.1vh;
                background-image: url(#{$imgURL}Leaderboard-coin-rare.png);
                background-repeat: no-repeat;
                background-position: center, center;
                background-size: auto 5.1vh;
                position: absolute;
                bottom: -3rem;
                left: 0;
                right: 0;
            }
        }

        //文字基本設定
        span {
            display: block;
            line-height: 1.12;
            font-weight: bold;
            position: relative;
            z-index: 3;
            text-shadow: 1px 1px #fff, -1px 1px #fff, 1px -1px #fff, 1px -1px #fff;

            //前三名數字設定及插圖
            &.number {
                display: block;
                height: 4.8vh;
                background-repeat: no-repeat;
                background-position: center, center;
                position: absolute;
                top: -3.2vh;
                left: 0;
                right: 0;
                z-index: 2;

                //第一名的數字尺寸位置微調
                &-first {
                    background-image: url(#{$imgURL}Leaderboard-first.png);
                    background-size: auto 5.1vh;
                    top: -4.3vh;
                }

                &-second {
                    background-image: url(#{$imgURL}Leaderboard-second.png);
                    background-size: auto 4.8vh;
                }

                &-third {
                    background-image: url(#{$imgURL}Leaderboard-third.png);
                    background-size: auto 4.8vh;
                }
            }

            &.class {
                color: #A24C00;
                font-size: 1.525rem;
            }

            &.name {
                font-size: 2.725rem;

                @include media-breakpoint-down(sm) {
                    font-size: 2.125rem;
                }
            }

            &.value {
                font-size: 2.125rem;

                @include media-breakpoint-down(sm) {
                    font-size: 1.725rem;
                }
            }
        }
    }

    //前三位整體
    &-box {
        margin: -3.5rem -0.5rem 0 -0.5rem;
        padding: 23rem 1rem 1rem 1rem;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;

        @include media-breakpoint-down(xl) {
            margin-top: 0;
            padding-top: 17rem;
        }

        @include media-breakpoint-down(md) {
            padding-top: 18rem;
        }

        //紅色
        &-red {
            .leaderboard-item {
                border-color: #C91F74;
            }

            //第一名框後插圖 (錢堆)
            &::before {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-coin-many.png);
                background-repeat: no-repeat;
                background-position: right 4.5vw top 20vh;
                background-size: auto 9.4vh;
                z-index: 0;
                pointer-events: none;

                @include media-breakpoint-down(lg) {
                    background-position: right 4.5vw top 22vh;
                }

                @include media-breakpoint-down(md) {
                    background-position: right 7.5vw top 18rem;
                    background-size: 28% auto;
                }
            }

            //第一名框前插圖 (3個亮點/2個亮點/獎盃)
            &::after {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-starlight-three.png),
                url(#{$imgURL}Leaderboard-starlight-two.png),
                url(#{$imgURL}Leaderboard-img-trophy.png);
                background-repeat: no-repeat;
                background-position: right 10.5vw top 10vh, left 2.5vw top 27vh, left 6.5vw top 14vh;
                background-size: auto 7.1vh, auto 6.1vh, auto 17.8vh;
                z-index: 1;
                pointer-events: none;

                @include media-breakpoint-down(lg) {
                    background-position: right 10.5vw top 12vh, left 2.5vw top 24vh, left 6.5vw top 19vh;
                }

                @include media-breakpoint-down(md) {
                    background-position: right 17.5vw top 10rem, left 4.5rem top 18rem, left 15vw top 15rem;
                    background-size: 12% auto, 13% auto, 21% auto;
                }
            }
        }

        //紫色
        &-purple {
            .leaderboard-item {
                border-color: #7F3ACE;
            }

            .number {
                filter: hue-rotate(285deg);
            }

            //第一名框後插圖 (錢罐/錢堆)
            &::before {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-img-money-jar.png),
                url(#{$imgURL}Leaderboard-coin-many.png);
                background-repeat: no-repeat;
                background-position: left 4.5vw top 20vh, right 4.5vw top 22vh;
                background-size: auto 12vh, auto 9.4vh;
                z-index: 0;
                pointer-events: none;

                @include media-breakpoint-down(sm) {
                    background-position: left 6.5vw top 14rem, right 4.5vw top 16rem;
                    background-size: 28% auto, 31% auto;
                }
            }

            //第一名框前插圖 (3個亮點/2個亮點)
            &::after {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-starlight-three2.png),
                url(#{$imgURL}Leaderboard-starlight-three3.png);
                background-repeat: no-repeat;
                background-position: left 11.5vw top 10.2vh, right 2vw top 21.5vh;
                background-size: auto 7.6vh, auto 7.8vh;
                z-index: 1;
                pointer-events: none;

                @include media-breakpoint-down(sm) {
                    background-position: left 3.5vw top 8rem, right -2.5vw top 15rem;
                    background-size: 17% auto, 17% auto;
                }
            }
        }

        &-green {
            .leaderboard-item {
                border-color: #23972F;
            }

            .number {
                filter: hue-rotate(151deg);
            }

            //第一名框後插圖 (錢堆)
            &::before {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-coin-many.png);
                background-repeat: no-repeat;
                background-position: right 5.5vw top 22vh;
                background-size: auto 9.4vh;
                z-index: 0;
                pointer-events: none;

                @include media-breakpoint-down(md) {
                    background-position: right 14% top 19rem;
                }

            }

            //第一名框前插圖 (3個亮點/2個亮點/飛書女孩/閱讀女孩)
            &::after {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-starlight-three.png),
                url(#{$imgURL}Leaderboard-starlight-two.png),
                url(#{$imgURL}Leaderboard-img-book-girl.png),
                url(#{$imgURL}Leaderboard-img-read-girl.png);
                background-repeat: no-repeat;
                background-position: right 10.5vw top 10vh, left 2.5vw top 27vh, left 4.5vw top 19vh, right 4vw top 10.5vh;
                background-size: auto 7.1vh, auto 6.1vh, auto 14.8vh, auto 18.5vh;
                z-index: 1;
                pointer-events: none;

                @include media-breakpoint-down(lg) {
                    background-position: right 19.5vw top 10vh, left 8.5vw top 22vh, left 5.5vw top 16rem, right 7% top 13rem;
                    background-size: auto 8.1%, auto 5.1%, 17rem auto, 10rem auto;
                }

                @include media-breakpoint-down(sm) {
                    background-position: right 19.5vw top 10vh, left 8.5vw top 22vh, left 0.5vw top 19rem, right 4% top 13rem;
                    background-size: auto 8.1%, auto 5.1%, 13rem auto, 8rem auto;
                }
            }
        }

        &-blue {
            .leaderboard-item {
                border-color: #3498DB;
            }

            .number {
                filter: hue-rotate(215deg);
            }

            //第一名框後插圖 (中等錢堆)
            &::before {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-coin-medium.png);
                background-repeat: no-repeat;
                background-position: left 4.5vw top 20vh;
                background-size: auto 8.3vh;
                z-index: 0;
                pointer-events: none;

                @include media-breakpoint-down(md) {
                    background-position: left 1.5vw top 22rem;
                    background-size: 30.3vw auto;
                }
            }

            //第一名框前插圖 (3個亮點/2個亮點/飛書女孩/閱讀女孩)
            &::after {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background-image: url(#{$imgURL}Leaderboard-starlight-three2.png),
                url(#{$imgURL}Leaderboard-starlight-three3.png),
                url(#{$imgURL}Leaderboard-img-bankroll.png),
                url(#{$imgURL}Leaderboard-img-run-girl.png);
                background-repeat: no-repeat;
                background-position: left 11.5vw top 10.2vh, right 2vw top 21.5vh, right 4vw top 23.8vh, left 7vw top 14.3vh;
                background-size: auto 7.6vh, auto 7.8vh, auto 9.5vh, auto 21vh;
                z-index: 1;
                pointer-events: none;

                @include media-breakpoint-down(md) {
                    background-position: left 11.5vw top 10.2vh, right 2vw top 21.5vh, right 5vw top 18.8rem, left 11vw top 11.3rem;
                    background-size: auto 7.6vh, auto 7.8vh, 7.5rem auto, 8rem auto;
                }
            }
        }
    }
}

//下方後6位列表(預設綠色)
.leaderboard-list {
    margin: -0.5rem 0 0 0;
    padding: 0 1.5rem;
    font-size: 1.925rem;

    @include media-breakpoint-down(md) {
        font-size: 1.5rem;
        padding: 0;
    }

    @include media-breakpoint-down(sm) {
        margin-left: 1.5rem;
    }

    li {

        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: .7rem;
        padding: .5rem;
        background: #FAF5E1;
        box-shadow: 0 0 .5rem rgba(0, 0, 0, 0.2);
        border: .5rem solid #fff;
        position: relative;
        min-height: 6vh;

        @include media-breakpoint-down(sm) {
            margin-top: .2rem;
            padding: 0;
            min-height: auto;
        }

        span {
            display: block;
            padding: 0 1.5rem;
            flex: 1 1 auto;
            text-align: left;
            font-weight: bold;

            @include media-breakpoint-down(md) {
                padding: 0;
            }
        }

        .number {
            flex: 0 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: -.3rem 0 -.3rem -1.7rem;
            padding: 0 1rem;
            font-size: 2.1rem;
            color: #fff;
            border-radius: .5rem;
            background: #666;
            box-shadow: 0 .3rem 0 0 #333;

        }

        .class {
            @include media-breakpoint-down(md) {
                padding-left: 1rem;
            }
        }

        .value {
            padding-right: .5rem;
            text-align: right;
        }
    }

    //紅色
    &-red {
        li .number {
            background: #C91F74;
            box-shadow: 0 .3rem 0 0 #840000;
        }
    }

    //紫色
    &-purple {
        li .number {
            background: #8B1BCB;
            box-shadow: 0 .3rem 0 0 #350554;
        }
    }

    //綠色
    &-green {
        li .number {
            background: #23972F;
            box-shadow: 0 .3rem 0 0 #00630A;
        }
    }

    //藍色
    &-blue {
        li .number {
            background: #3498DB;
            box-shadow: 0 .3rem 0 0 #004081;
        }
    }
}

//滿版酷幣排行榜調整
.title-ecool {
    background-position: center, center top 82%;
    background-size: auto 90%, auto 92%;
    top: 36%;
    height: 29vh;

    @include media-breakpoint-down(md) {
        background-position: center, center top 82%;
        background-size: auto 90%, auto 92%;
        top: 0;
        height: 16rem;
    }
}


.leaderboard-page-layout-merge {
    padding: 0;
    background-image: url(#{$imgURL}bg-sunlight.png);
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100% 54%;
    background-attachment: fixed;

    @include media-breakpoint-down(md) {
        animation-name: none;
        padding-top: 21rem;
    }

    &::before {
        content: "";
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-image: url(#{$imgURL}bg-sunlight.png);
        background-repeat: no-repeat;
        background-position: center top;
        background-size: 100% 43%;
        transform: rotate(180deg);

    }

    //標章尺寸和位置
    .title {
        width: 91%;
        height: 16vh;
        top: 0;

        @include media-breakpoint-down(md) {
            height: 10rem;
            top: -2rem;
        }

        //累計酷幣
        &-total {
            background-position: top 0 left 1rem, top 1.2rem left 3.5rem;
        }

        //現有總資產
        &-existing {
            background-position: top 0 right 0, top 1.1rem right 2rem;
        }

        //閱讀認證
        &-read {
            background-position: top 0 left 7vw, top 1.2rem left 8.5vw;
            top: -4vh;

            @include media-breakpoint-down(lg) {
                background-position: top 0 left 5vw, top 1.2rem left 7.5vw;
                top: -7vh;
            }

            @include media-breakpoint-down(md) {
                top: -2rem;
            }
        }

        //運動撲滿
        &-sports {
            background-position: top 0 right 4vw, top 1.1rem right 5vw;
            top: -4vh;

            @include media-breakpoint-down(lg) {
                background-position: top 0 right 1vw, top 1.1rem right 3vw;
                top: -7vh;
            }

            @include media-breakpoint-down(md) {
                top: -2rem;
            }
        }
    }

    .leaderboard-box {
        margin: 0;
        padding: 8rem 2rem 1rem 1rem;

        @include media-breakpoint-down(md) {
            padding: 10rem 1rem 3rem 1rem;
        }

        &-red {
            &::before {
                background-position: right 9.5vw top 13rem;

                @include media-breakpoint-down(md) {
                    background-position: right 9.5vw top 10rem;
                }
            }

            &::after {
                background-position: right 17.5vw top 10rem, left 2.5rem top 16rem, left 7vw top 9rem;

                @include media-breakpoint-down(md) {
                    background-position: right 17.5vw top 9rem, left 2.5rem top 15rem, left 16vw top 9rem;
                }
            }

        }

        &-purple {
            &::before {
                background-position: left 6.5vw top 12rem, right 4.5vw top 14rem;

                @include media-breakpoint-down(md) {
                    background-position: left 6.5vw top 9rem, right 4.5vw top 10rem;
                }
            }

            &::after {
                background-position: left 8.5vw top 15rem, right 1.5vw top 18rem;

                @include media-breakpoint-down(md) {
                    background-position: left 8.5vw top 13rem, right 1.5vw top 15rem;
                }
            }
        }

        &-green {
            &::before {
                background-position: right 5.5vw top 11vh;

                @include media-breakpoint-down(lg) {
                    background-position: right 5.5vw top 14vh;
                }

                @include media-breakpoint-down(md) {
                    background-position: right 12% top 11rem;
                    right: 0;
                }
            }

            &::after {
                right: -7vw;
                left: 0;
                bottom: -2vw;
                background-position: right 23.5vw top 2vh, left 1.5vw top 2vh, left 1.5vw top 7vh, right 4.5vw top 25.5vh;
                z-index: 2;

                @include media-breakpoint-down(md) {
                    background-position: right 30vw top 5vh, left 8.5vw top 22vh, left 4.5vw top 9rem, right 18% top 7rem;
                    z-index: 1;
                }
            }
        }

        &-blue {
            &::before {
                background-position: left 5.5vw top 11vh;

                @include media-breakpoint-down(lg) {
                    background-position: left 5.5vw top 14vh;
                }

                @include media-breakpoint-down(md) {
                    background-position: left 11.5vw top 14rem;
                }
            }

            &::after {
                background-image: url(#{$imgURL}Leaderboard-starlight-three2.png),
                url(#{$imgURL}Leaderboard-starlight-three3.png),
                url(#{$imgURL}Leaderboard-img-bankroll.png);
                background-position: left 11.5vw top 10.2vh, right 8vw top 10.5vh, right 10vw top 12.8vh;
                background-size: auto 7.6vh, auto 7.8vh, auto 9.5vh;

                @include media-breakpoint-down(md) {
                    background-position: left 6.5vw top 25.2vh, right 2vw top 18.5vh, right 9vw top 10rem;
                }
            }


        }
    }

    .img-run-girl {
        display: block;
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        background-image:url(#{$imgURL}Leaderboard-img-run-girl.png);
        background-repeat: no-repeat;
        background-position: right 4vw top 10vh;
        background-size: auto 21vh;
        z-index: 3;
        pointer-events: none;

        @include media-breakpoint-down(lg) {
            background-position: right 2vw top 10vh;
        }

        @include media-breakpoint-down(md) {
            background-position: left 17vw top 5rem;
            z-index: 1;
        }
    }
}