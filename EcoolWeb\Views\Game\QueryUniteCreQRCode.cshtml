﻿@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    Uri contextUri = HttpContext.Current.Request.Url;

    var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
    contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port);

    string ValueStr = baseUri + Url.Action("ToQuery", "Game").ToString(); ;

    if (SharedGlobal.HomeIndex != "ChildMonthIndex")
    {
        ValueStr = ValueStr + "?GAME_NO=" + EcoolWeb.Models.UserProfileHelper.GetGameNoCookie();
    }

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();

    var ImgPathStr = HttpContext.Current.Server.MapPath(SharedGlobal.QrCodeLogo);

}
<link href="~/Content/css/childrens-month.css" rel="stylesheet" />
@using (Html.BeginForm("QueryUniteCreQRCode", "Game", FormMethod.Post, new { id = "form1", name = "form1", @AutoComplete = "Off" }))
{

}

<div class="panel with-nav-tabs panel-info">
    @if (!string.IsNullOrWhiteSpace((string)TempData["GameName"]))
    {
        <div class="panel-heading">
            <h1>@TempData["GameName"]</h1>
        </div>
    }
    <div class="panel-body" style="background-color:#eef6fa;">
        <div style="margin: 0px auto;text-align:center">
            <img id="QrCodeImg" src="@Url.Action("GenerateQR", "Barcode", new { width=1920,height=1920, Value = ValueStr,ImgPath=ImgPathStr })" />
        </div>
        <br />
        <div class="form-group text-center">
            <h4>中獎清單 QR CODE 碼</h4>
            <h5>掃描此 QR CODE ，可連結此次活動的中獎明細</h5>
        </div>
    </div>
</div>

<div id="DivAddButton">
    <i id="title" class="fa fa-arrow-left fa-3x"></i>
    <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

          function OnBack() {
             $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")
             $(targetFormID).submit();
        }

        $(document).ready(function () {

            AutoQrCodeImg()

            $(window).resize(function () {
                AutoQrCodeImg()
            });
        });

        function AutoQrCodeImg() {
            var width = $(window).width();
            var height = $(window).height();

            var pre
            if (width > height) {
                pre = height
            }
            else {
                pre = width
            }

            if (pre < 300) {
                pre = 300
            }

            $("#QrCodeImg").width(pre * 0.8);
            $("#QrCodeImg").height(pre * 0.8);
        }
    </script>
}