﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http</name>
  </assembly>
  <members>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.Http.InvalidByteRangeException)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Exception)">
      <summary>建立表示例外狀況的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>要求必須與 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體產生關聯。<see cref="T:System.Net.Http.HttpResponseMessage" />，其內容為 <see cref="T:System.Web.Http.HttpError" /> 執行個體的序列化表示。</returns>
      <param name="request">HTTP 要求。</param>
      <param name="statusCode">回應的狀態碼。</param>
      <param name="exception">例外狀況。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.String)">
      <summary>建立表示錯誤訊息的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>要求必須與 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體產生關聯。<see cref="T:System.Net.Http.HttpResponseMessage" />，其內容為 <see cref="T:System.Web.Http.HttpError" /> 執行個體的序列化表示。</returns>
      <param name="request">HTTP 要求。</param>
      <param name="statusCode">回應的狀態碼。</param>
      <param name="message">錯誤訊息。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.String,System.Exception)">
      <summary>建立表示具有錯誤訊息之例外狀況的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>要求必須與 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體產生關聯。<see cref="T:System.Net.Http.HttpResponseMessage" />，其內容為 <see cref="T:System.Web.Http.HttpError" /> 執行個體的序列化表示。</returns>
      <param name="request">HTTP 要求。</param>
      <param name="statusCode">回應的狀態碼。</param>
      <param name="message">錯誤訊息。</param>
      <param name="exception">例外狀況。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Web.Http.HttpError)">
      <summary>建立表示錯誤的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>要求必須與 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體產生關聯。<see cref="T:System.Net.Http.HttpResponseMessage" />，其內容為 <see cref="T:System.Web.Http.HttpError" /> 執行個體的序列化表示。</returns>
      <param name="request">HTTP 要求。</param>
      <param name="statusCode">回應的狀態碼。</param>
      <param name="error">HTTP 錯誤。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>建立表示模型狀態錯誤的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>要求必須與 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體產生關聯。<see cref="T:System.Net.Http.HttpResponseMessage" />，其內容為 <see cref="T:System.Web.Http.HttpError" /> 執行個體的序列化表示。</returns>
      <param name="request">HTTP 要求。</param>
      <param name="statusCode">回應的狀態碼。</param>
      <param name="modelState">模型狀態。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0)">
      <summary>建立已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">導致此回應訊息的 HTTP 要求訊息。</param>
      <param name="statusCode">HTTP 回應狀態碼。</param>
      <param name="value">HTTP 回應訊息的內容。</param>
      <typeparam name="T">HTTP 回應訊息的型別。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
      <summary>建立已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">導致此回應訊息的 HTTP 要求訊息。</param>
      <param name="statusCode">HTTP 回應狀態碼。</param>
      <param name="value">HTTP 回應訊息的內容。</param>
      <param name="formatter">媒體型別格式器。</param>
      <typeparam name="T">HTTP 回應訊息的型別。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>建立已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">導致此回應訊息的 HTTP 要求訊息。</param>
      <param name="statusCode">HTTP 回應狀態碼。</param>
      <param name="value">HTTP 回應訊息的內容。</param>
      <param name="formatter">媒體型別格式器。</param>
      <param name="mediaType">媒體型別標頭值。</param>
      <typeparam name="T">HTTP 回應訊息的型別。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
      <summary>建立已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">導致此回應訊息的 HTTP 要求訊息。</param>
      <param name="statusCode">HTTP 回應狀態碼。</param>
      <param name="value">HTTP 回應訊息的內容。</param>
      <param name="formatter">媒體型別格式器。</param>
      <param name="mediaType">媒體型別。</param>
      <typeparam name="T">HTTP 回應訊息的型別。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>建立已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">導致此回應訊息的 HTTP 要求訊息。</param>
      <param name="statusCode">HTTP 回應狀態碼。</param>
      <param name="value">HTTP 回應訊息的內容。</param>
      <param name="mediaType">媒體型別標頭值。</param>
      <typeparam name="T">HTTP 回應訊息的型別。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.String)">
      <summary>建立已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">導致此回應訊息的 HTTP 要求訊息。</param>
      <param name="statusCode">HTTP 回應狀態碼。</param>
      <param name="value">HTTP 回應訊息的內容。</param>
      <param name="mediaType">媒體型別。</param>
      <typeparam name="T">HTTP 回應訊息的型別。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Web.Http.HttpConfiguration)">
      <summary>建立已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>已連線到相關聯之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">導致此回應訊息的 HTTP 要求訊息。</param>
      <param name="statusCode">HTTP 回應狀態碼。</param>
      <param name="value">HTTP 回應訊息的內容。</param>
      <param name="configuration">HTTP 組態，其中包含用來解析服務的相依性解析程式。</param>
      <typeparam name="T">HTTP 回應訊息的型別。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,``0)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.DisposeRequestResources(System.Net.Http.HttpRequestMessage)">
      <summary>處置與 <paramref name="request" /> 相關聯的所有追蹤資源，其已透過 <see cref="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.IDisposable)" /> 方法新增。</summary>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetActionDescriptor(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetClientCertificate(System.Net.Http.HttpRequestMessage)">
      <summary>從指定的 HTTP 要求取得目前的 X.509 憑證。</summary>
      <returns>目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />，若無法取得憑證則為 null。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetConfiguration(System.Net.Http.HttpRequestMessage)">
      <summary>擷取指定要求的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>指定要求的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetCorrelationId(System.Net.Http.HttpRequestMessage)">
      <summary>擷取 <see cref="T:System.Guid" />，其已指派為與指定的 <paramref name="request" /> 相關聯的相互關聯 ID。會建立值並在第一次呼叫此方法時加以設定。</summary>
      <returns>
        <see cref="T:System.Guid" /> 物件，表示與要求相關聯的相互關聯 ID。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetDependencyScope(System.Net.Http.HttpRequestMessage)">
      <summary>擷取指定要求的 <see cref="T:System.Web.Http.Dependencies.IDependencyScope" />，如果無法取得，即為 null。</summary>
      <returns>指定要求的 <see cref="T:System.Web.Http.Dependencies.IDependencyScope" />，如果無法取得，即為 null。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetQueryNameValuePairs(System.Net.Http.HttpRequestMessage)">
      <summary>取得已剖析的查詢字串，做為索引鍵值組集合。</summary>
      <returns>已剖析的查詢字串做為索引鍵值組集合。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetRequestContext(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetResourcesForDisposal(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetRouteData(System.Net.Http.HttpRequestMessage)">
      <summary>擷取指定要求的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" />，如果無法取得，即為 null。</summary>
      <returns>指定要求的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" />，如果無法取得，即為 null。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetSynchronizationContext(System.Net.Http.HttpRequestMessage)">
      <summary>擷取指定要求的 <see cref="T:System.Threading.SynchronizationContext" />，如果無法取得，即為 null。</summary>
      <returns>指定要求的 <see cref="T:System.Threading.SynchronizationContext" />，如果無法取得，即為 null。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetUrlHelper(System.Net.Http.HttpRequestMessage)">
      <summary>針對 HTTP 要求取得 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 執行個體。</summary>
      <returns>已針對特定的 HTTP 要求初始化 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 執行個體。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.IsBatchRequest(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.IsLocal(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.IDisposable})"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.IDisposable)">
      <summary>將指定的 <paramref name="resource" /> 新增到一旦處置 <paramref name="request" /> 時會由主機處置的資源清單。</summary>
      <param name="request">控制 <paramref name="resource" /> 週期的 HTTP 要求。</param>
      <param name="resource">處置 <paramref name="request" /> 時要處置的資源。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.SetConfiguration(System.Net.Http.HttpRequestMessage,System.Web.Http.HttpConfiguration)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.SetRequestContext(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpRequestContext)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.SetRouteData(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRouteData)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.ShouldIncludeErrorDetail(System.Net.Http.HttpRequestMessage)"></member>
    <member name="T:System.Net.Http.HttpResponseMessageExtensions">
      <summary>表示來自 ASP.NET 作業之 HTTP 回應的訊息擴充程式。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessageExtensions.TryGetContentValue``1(System.Net.Http.HttpResponseMessage,``0@)">
      <summary>嘗試擷取 <see cref="T:System.Net.Http.HttpResponseMessageExtensions" /> 的內容值。</summary>
      <returns>內容值的擷取結果。</returns>
      <param name="response">作業的回應。</param>
      <param name="value">內容的值。</param>
      <typeparam name="T">要擷取的型別值。</typeparam>
    </member>
    <member name="T:System.Net.Http.Formatting.MediaTypeFormatterExtensions">
      <summary>表示用於將 <see cref="T:System.Net.Http.Formatting.MediaTypeMapping" /> 項目加入至 <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 的擴充功能。</summary>
    </member>
    <member name="M:System.Net.Http.Formatting.MediaTypeFormatterExtensions.AddUriPathExtensionMapping(System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Net.Http.Headers.MediaTypeHeaderValue)"></member>
    <member name="M:System.Net.Http.Formatting.MediaTypeFormatterExtensions.AddUriPathExtensionMapping(System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.String)"></member>
    <member name="T:System.Net.Http.Formatting.UriPathExtensionMapping">
      <summary>提供來自 <see cref="T:System.Uri" /> 中出現之路徑副檔名中的 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。</summary>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.#ctor(System.String,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary> 初始化 <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> 類別的新執行個體。</summary>
      <param name="uriPathExtension">與 mediaType 相關的延伸程式。此值不應包含小數點或萬用字元。</param>
      <param name="mediaType">若 uriPathExtension 相符，則會傳回 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。</param>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.#ctor(System.String,System.String)">
      <summary> 初始化 <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> 類別的新執行個體。</summary>
      <param name="uriPathExtension">與 mediaType 相關的延伸程式。此值不應包含小數點或萬用字元。</param>
      <param name="mediaType">若 uriPathExtension 相符，則會傳回媒體類型。</param>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.TryMatchMediaType(System.Net.Http.HttpRequestMessage)">
      <summary> 傳回一個值，指出是否此 <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> 執行個體可針對 request 的 <see cref="T:System.Uri" /> 提供 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。</summary>
      <returns>如果此執行個體可符合 request 中的副檔名，則會傳回 1.0，否則會傳回 0.0。</returns>
      <param name="request">要檢查的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</param>
    </member>
    <member name="P:System.Net.Http.Formatting.UriPathExtensionMapping.UriPathExtension">
      <summary> 取得 <see cref="T:System.Uri" /> 路徑延伸程式。</summary>
      <returns>
        <see cref="T:System.Uri" /> 路徑副檔名。</returns>
    </member>
    <member name="F:System.Net.Http.Formatting.UriPathExtensionMapping.UriPathExtensionKey">
      <summary>
        <see cref="T:System.Uri" /> 路徑副檔名索引鍵。</summary>
    </member>
    <member name="T:System.Web.Http.AcceptVerbsAttribute">
      <summary>表示用來指定動作方法將回應哪些 HTTP 方法的屬性。</summary>
    </member>
    <member name="M:System.Web.Http.AcceptVerbsAttribute.#ctor(System.String)">
      <summary>使用會回應的動作方法初始化 <see cref="T:System.Web.Http.AcceptVerbsAttribute" /> 類別的新執行個體。</summary>
      <param name="method">動作方法會回應的 HTTP 方法。</param>
    </member>
    <member name="M:System.Web.Http.AcceptVerbsAttribute.#ctor(System.String[])">
      <summary>使用動作方法所將回應的 HTTP 方法清單，初始化 <see cref="T:System.Web.Http.AcceptVerbsAttribute" /> 類別的新執行個體。</summary>
      <param name="methods">動作方法所將回應的 HTTP 方法。</param>
    </member>
    <member name="P:System.Web.Http.AcceptVerbsAttribute.HttpMethods">
      <summary>取得或設定動作方法所將回應的 HTTP 方法清單。</summary>
      <returns>取得或設定動作方法所將回應的 HTTP 方法清單。</returns>
    </member>
    <member name="T:System.Web.Http.ActionNameAttribute">
      <summary>表示用來當做動作名稱的屬性。</summary>
    </member>
    <member name="M:System.Web.Http.ActionNameAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.ActionNameAttribute" /> 類別的新執行個體。</summary>
      <param name="name">動作的名稱。</param>
    </member>
    <member name="P:System.Web.Http.ActionNameAttribute.Name">
      <summary>取得或設定動作的名稱。</summary>
      <returns>動作的名稱。</returns>
    </member>
    <member name="T:System.Web.Http.AllowAnonymousAttribute">
      <summary>指定 <see cref="T:System.Web.Http.AuthorizeAttribute" /> 會在授權期間略過動作與控制器。</summary>
    </member>
    <member name="M:System.Web.Http.AllowAnonymousAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.AllowAnonymousAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Web.Http.ApiController">
      <summary>定義 API 控制器的屬性和方法。</summary>
    </member>
    <member name="M:System.Web.Http.ApiController.#ctor"></member>
    <member name="P:System.Web.Http.ApiController.ActionContext">
      <summary>取得動作內容。</summary>
      <returns>動作內容。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.BadRequest">
      <summary>建立 <see cref="T:System.Web.Http.Results.BadRequestResult" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.BadRequestResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.BadRequest(System.String)">
      <summary>建立具有指定錯誤訊息的 <see cref="T:System.Web.Http.Results.ErrorMessageResult" /> (400 錯誤的要求)。</summary>
      <returns>具有指定模型狀態的 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" />。</returns>
      <param name="message">使用者可看見的錯誤訊息。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.BadRequest(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>建立具有指定模型狀態的 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" />。</summary>
      <returns>具有指定模型狀態的 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" />。</returns>
      <param name="modelState">要包含在錯誤中的模型狀態。</param>
    </member>
    <member name="P:System.Web.Http.ApiController.Configuration">
      <summary>取得目前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>目前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Conflict">
      <summary>建立 <see cref="T:System.Web.Http.Results.ConflictResult" /> (409 衝突)。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.ConflictResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0)">
      <summary>建立具有指定值的 &lt;see cref="T:System.Web.Http.NegotiatedContentResult`1" /&gt;。</summary>
      <returns>具有指定值的 &lt;see cref="T:System.Web.Http.NegotiatedContentResult`1" /&gt;。</returns>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="value">在實體主體中協調和格式化的內容值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
      <summary>建立具有指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</summary>
      <returns>具有指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</returns>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="value">在實體主體中格式化的內容值。</param>
      <param name="formatter">格式化內容時會使用的格式器。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>建立具有指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</summary>
      <returns>具有指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</returns>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="value">在實體主體中格式化的內容值。</param>
      <param name="formatter">格式化內容時會使用的格式器。</param>
      <param name="mediaType"> 內容類型標頭值，或 &lt;see langword="null" /&gt; 讓格式器挑選預設值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
      <summary>建立具有指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</summary>
      <returns>具有指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</returns>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="value">在實體主體中格式化的內容值。</param>
      <param name="formatter">格式化內容時會使用的格式器。</param>
      <param name="mediaType">內容類型標頭的值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="P:System.Web.Http.ApiController.ControllerContext">
      <summary>取得目前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>目前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Created``1(System.String,``0)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" /> (201 已建立)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" />。</returns>
      <param name="location">已建立內容的位置。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Created``1(System.Uri,``0)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" /> (201 已建立)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" />。</returns>
      <param name="location">已建立內容的位置。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.CreatedAtRoute``1(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},``0)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" /> (201 已建立)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" />。</returns>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.CreatedAtRoute``1(System.String,System.Object,``0)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" /> (201 已建立)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" />。</returns>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Dispose">
      <summary>執行與免費、釋放或重設 Unmanged 資源相關的應用程式定義的工作。</summary>
    </member>
    <member name="M:System.Web.Http.ApiController.Dispose(System.Boolean)">
      <summary>釋放物件所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Threading.CancellationToken)">
      <summary>以非同步方式執行單一 HTTP 作業。</summary>
      <returns>新開始的工作。</returns>
      <param name="controllerContext">單一 HTTP 作業的控制器內容。</param>
      <param name="cancellationToken">針對 HTTP 作業指派的取消權杖。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Initialize(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>使用指定的 controllerContext 初始化 <see cref="T:System.Web.Http.ApiController" /> 執行個體。</summary>
      <param name="controllerContext">用於初始化的 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 物件。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.InternalServerError">
      <summary>建立 <see cref="T:System.Web.Http.Results.InternalServerErrorResult" /> (500 內部伺服器錯誤)。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.InternalServerErrorResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.InternalServerError(System.Exception)">
      <summary>建立具有指定例外狀況的 <see cref="T:System.Web.Http.Results.ExceptionResult" /> (500 內部伺服器錯誤)。</summary>
      <returns>具有指定例外狀況的 <see cref="T:System.Web.Http.Results.ExceptionResult" />。</returns>
      <param name="exception">要包含在錯誤中的例外狀況。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Json``1(``0)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" /> (200 確定)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />。</returns>
      <param name="content">在實體主體中序列化的內容值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Json``1(``0,Newtonsoft.Json.JsonSerializerSettings)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" /> (200 確定)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />。</returns>
      <param name="content">在實體主體中序列化的內容值。</param>
      <param name="serializerSettings">序列化程式設定。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Json``1(``0,Newtonsoft.Json.JsonSerializerSettings,System.Text.Encoding)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" /> (200 確定)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />。</returns>
      <param name="content">在實體主體中序列化的內容值。</param>
      <param name="serializerSettings">序列化程式設定。</param>
      <param name="encoding">內容編碼方式。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="P:System.Web.Http.ApiController.ModelState">
      <summary>取得模型在進行模型繫結程序後的狀態。</summary>
      <returns>模型在進行模型繫結程序後的狀態。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.NotFound">
      <summary>建立 <see cref="T:System.Web.Http.Results.NotFoundResult" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.NotFoundResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Ok">
      <summary>建立 <see cref="T:System.Web.Http.Results.OkResult" /> (200 確定)。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.OkResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Ok``1(``0)">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" />。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" />。</returns>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Redirect(System.String)">
      <summary>建立帶有指定值的重新導向結果 (找到 302)。</summary>
      <returns>帶有指定值的重新導向結果 (找到 302)。</returns>
      <param name="location">要重新導向至的位置。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Redirect(System.Uri)">
      <summary>建立帶有指定值的重新導向結果 (找到 302)。</summary>
      <returns>帶有指定值的重新導向結果 (找到 302)。</returns>
      <param name="location">要重新導向至的位置。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.RedirectToRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>建立帶有指定值的路由重新導向結果 (找到 302)。</summary>
      <returns>帶有指定值的路由重新導向結果 (找到 302)。</returns>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.RedirectToRoute(System.String,System.Object)">
      <summary>建立帶有指定值的路由重新導向結果 (找到 302)。</summary>
      <returns>帶有指定值的路由重新導向結果 (找到 302)。</returns>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
    </member>
    <member name="P:System.Web.Http.ApiController.Request">
      <summary>取得或設定目前 <see cref="T:System.Web.Http.ApiController" /> 的 HttpRequestMessage。</summary>
      <returns>目前 <see cref="T:System.Web.Http.ApiController" /> 的 HttpRequestMessage。</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.RequestContext">
      <summary>取得要求內容。</summary>
      <returns>要求內容。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.ResponseMessage(System.Net.Http.HttpResponseMessage)">
      <summary>建立具有指定回應的 <see cref="T:System.Web.Http.Results.ResponseMessageResult" />。</summary>
      <returns>進行指定回應的 <see cref="T:System.Web.Http.Results.ResponseMessageResult" />。</returns>
      <param name="response">HTTP 回應訊息。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.StatusCode(System.Net.HttpStatusCode)">
      <summary>建立具有指定狀態碼的 <see cref="T:System.Web.Http.StatusCodeResult" />。</summary>
      <returns>具有指定狀態碼的 <see cref="T:System.Web.Http.StatusCodeResult" />。</returns>
      <param name="status">回應訊息的 HTTP 狀態碼</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Unauthorized(System.Collections.Generic.IEnumerable{System.Net.Http.Headers.AuthenticationHeaderValue})">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" /> (401 未授權)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" />。</returns>
      <param name="challenges">WWW 驗證挑戰。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Unauthorized(System.Net.Http.Headers.AuthenticationHeaderValue[])">
      <summary>建立具有指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" /> (401 未授權)。</summary>
      <returns>具有指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" />。</returns>
      <param name="challenges">WWW 驗證挑戰。</param>
    </member>
    <member name="P:System.Web.Http.ApiController.Url">
      <summary>取得 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的執行個體，以用來產生其他 API 的 URL。</summary>
      <returns>
        <see cref="T:System.Web.Http.Routing.UrlHelper" />，可用來產生其他 API 的 URL。</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.User">
      <summary>傳回與此要求相關的目前主體。</summary>
      <returns>與此要求相關聯的目前主體。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Validate``1(``0)">
      <summary>驗證指定實體並新增驗證錯誤至空白前置字元下的模型狀態 (如果有)。</summary>
      <param name="entity">要驗證的實體。</param>
      <typeparam name="TEntity">要驗證的實體類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Validate``1(``0,System.String)">
      <summary>驗證指定實體並新增驗證錯誤至模型狀態 (如果有)。</summary>
      <param name="entity">要驗證的實體。</param>
      <param name="keyPrefix">在模型狀態中要新增的模型狀態錯誤下的索引鍵前置字元。</param>
      <typeparam name="TEntity">要驗證的實體類型。</typeparam>
    </member>
    <member name="T:System.Web.Http.AuthorizeAttribute">
      <summary>指定授權篩選條件，以驗證要求的 <see cref="T:System.Security.Principal.IPrincipal" />。</summary>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.AuthorizeAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.HandleUnauthorizedRequest(System.Web.Http.Controllers.HttpActionContext)">
      <summary>處理授權失敗的要求。</summary>
      <param name="actionContext">內容。</param>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.IsAuthorized(System.Web.Http.Controllers.HttpActionContext)">
      <summary>表示是否授權指定的控制項。</summary>
      <returns>如果控制項已獲得授權，則為 true，否則為 false。</returns>
      <param name="actionContext">內容。</param>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.OnAuthorization(System.Web.Http.Controllers.HttpActionContext)">
      <summary>在動作獲得授權時呼叫。</summary>
      <param name="actionContext">內容。</param>
      <exception cref="T:System.ArgumentNullException">內容參數是 Null。</exception>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.Roles">
      <summary>取得或設定授權的角色。</summary>
      <returns>角色字串。</returns>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.TypeId">
      <summary>取得此屬性的唯一識別碼。</summary>
      <returns>此屬性的唯一識別碼。</returns>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.Users">
      <summary>取得或設定授權的使用者。</summary>
      <returns>使用者字串。</returns>
    </member>
    <member name="T:System.Web.Http.FromBodyAttribute">
      <summary> 屬性，可指定動作參數只來自傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的實體內容。</summary>
    </member>
    <member name="M:System.Web.Http.FromBodyAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.FromBodyAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.FromBodyAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>取得參數繫結。</summary>
      <returns>參數繫結。</returns>
      <param name="parameter">參數描述。</param>
    </member>
    <member name="T:System.Web.Http.FromUriAttribute">
      <summary>屬性，可指定動作參數來自傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 URI。</summary>
    </member>
    <member name="M:System.Web.Http.FromUriAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.FromUriAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.FromUriAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary>取得模型繫結器的值提供者 Factory。</summary>
      <returns>
        <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 物件的集合。</returns>
      <param name="configuration">設定。</param>
    </member>
    <member name="T:System.Web.Http.HttpBindNeverAttribute">
      <summary>表示屬性 (attribute)，此屬性可指定 HTTP 繫結應排除屬性 (property)。</summary>
    </member>
    <member name="M:System.Web.Http.HttpBindNeverAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpBindNeverAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Web.Http.HttpBindRequiredAttribute">
      <summary>表示 HTTP 繫結的必要屬性。</summary>
    </member>
    <member name="M:System.Web.Http.HttpBindRequiredAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpBindRequiredAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Web.Http.HttpConfiguration">
      <summary>表示 <see cref="T:System.Web.Http.HttpServer" /> 執行個體的設定。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpConfiguration" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.#ctor(System.Web.Http.HttpRouteCollection)">
      <summary>使用 HTTP 路由集合，初始化 <see cref="T:System.Web.Http.HttpConfiguration" /> 類別的新執行個體。</summary>
      <param name="routes">要與此執行個體相關聯的 HTTP 路由集合。</param>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.DependencyResolver">
      <summary>取得或設定與這個執行個體相關聯的相依性解析程式。</summary>
      <returns>相依性解析程式。</returns>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.Dispose">
      <summary>執行與免費、釋放或重設 Unmanged 資源相關的應用程式定義的工作。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.Dispose(System.Boolean)">
      <summary>釋放物件所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.EnsureInitialized">
      <summary>叫用初始設定式連結。從此點之前被視為可列舉。可以呼叫多次。</summary>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Filters">
      <summary>取得篩選條件清單，這些篩選條件會套用到使用 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體所提供的所有要求。</summary>
      <returns>篩選條件清單。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Formatters">
      <summary>取得此執行個體的 media-type 格式器。</summary>
      <returns>
        <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 物件的集合。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.IncludeErrorDetailPolicy">
      <summary>取得或設定值，這個值表示錯誤詳細資料是否應該包含在錯誤訊息中。</summary>
      <returns>表示錯誤詳細資料原則的 <see cref="T:System.Web.Http.IncludeErrorDetailPolicy" /> 值。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Initializer">
      <summary> 取得或設定動作，這個動作會在用來處理要求之前，執行 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體的最終初始化。</summary>
      <returns>將執行 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體之最終初始化的動作。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.MessageHandlers">
      <summary>取得要叫用為向上傳輸至堆疊 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Net.Http.DelegatingHandler" /> 執行個體清單，且 <see cref="T:System.Net.Http.HttpResponseMessage" /> 會在堆疊中向下傳回。</summary>
      <returns>訊息處理常式集合。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.ParameterBindingRules">
      <summary>取得有關如何繫結參數的規則集合。</summary>
      <returns>可以為指定的參數產生參數繫結的函數集合。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Properties">
      <summary>取得與此執行個體相關的屬性。</summary>
      <returns>包含屬性的 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Routes">
      <summary>取得與此 <see cref="T:System.Web.Http.HttpConfiguration" /> 執行個體相關的 <see cref="T:System.Web.Http.HttpRouteCollection" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.HttpRouteCollection" />。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Services">
      <summary>取得或設定與這個執行個體相關聯的預設服務容器。</summary>
      <returns>包含此執行個體之預設服務的 <see cref="T:System.Web.Http.Controllers.ServicesContainer" />。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.VirtualPathRoot">
      <summary>取得根虛擬路徑。</summary>
      <returns>根虛擬路徑。</returns>
    </member>
    <member name="T:System.Web.Http.HttpConfigurationExtensions">
      <summary>包含 <see cref="T:System.Web.Http.HttpConfiguration" /> 類別的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.BindParameter(System.Web.Http.HttpConfiguration,System.Type,System.Web.Http.ModelBinding.IModelBinder)"></member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration)"></member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IDirectRouteProvider)">
      <summary>對應應用程式的屬性定義路徑。</summary>
      <param name="configuration">伺服器組態。</param>
      <param name="directRouteProvider">用於探索及建置路由的 <see cref="T:System.Web.Http.Routing.IDirectRouteProvider" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>對應應用程式的屬性定義路徑。</summary>
      <param name="configuration">伺服器組態。</param>
      <param name="constraintResolver">條件約束解析程式。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IInlineConstraintResolver,System.Web.Http.Routing.IDirectRouteProvider)">
      <summary>對應應用程式的屬性定義路徑。</summary>
      <param name="configuration">伺服器組態。</param>
      <param name="constraintResolver">要用來解析內部條件約束的 <see cref="T:System.Web.Http.Routing.IInlineConstraintResolver" />。</param>
      <param name="directRouteProvider">用於探索及建置路由的 <see cref="T:System.Web.Http.Routing.IDirectRouteProvider" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.SuppressHostPrincipal(System.Web.Http.HttpConfiguration)"></member>
    <member name="T:System.Web.Http.HttpDeleteAttribute">
      <summary>指定支援 DELETE HTTP 方法的動作。</summary>
    </member>
    <member name="M:System.Web.Http.HttpDeleteAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpDeleteAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.HttpDeleteAttribute.HttpMethods">
      <summary>取得對應至此屬性的 HTTP 方法。</summary>
      <returns>對應至此屬性的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpError">
      <summary>為儲存錯誤資訊定義可序列化的容器。此資訊儲存為索引鍵/值組。尋找標準錯誤資訊的字典索引鍵有 <see cref="T:System.Web.Http.HttpErrorKeys" /> 的類型。</summary>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpError" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.Exception,System.Boolean)">
      <summary>初始化 <paramref name="exception" /> 的 <see cref="T:System.Web.Http.HttpError" /> 類別的新執行個體。</summary>
      <param name="exception">用於錯誤資訊的例外狀況。</param>
      <param name="includeErrorDetail">要在錯誤中包含例外狀況資訊，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.String)">
      <summary>為包含錯誤訊息 <paramref name="message" /> 的 <see cref="T:System.Web.Http.HttpError" /> 類別，初始化新執行個體。</summary>
      <param name="message">要與此執行個體相關聯的錯誤訊息。</param>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.Boolean)">
      <summary>初始化 <paramref name="modelState" /> 的 <see cref="T:System.Web.Http.HttpError" /> 類別的新執行個體。</summary>
      <param name="modelState">用於錯誤資訊的無效模型狀態。</param>
      <param name="includeErrorDetail">要在錯誤中包含例外狀況訊息，則為 true，否則為 false。</param>
    </member>
    <member name="P:System.Web.Http.HttpError.ExceptionMessage">
      <summary>取得或設定 <see cref="T:System.Exception" /> 的訊息 (若可用)。</summary>
      <returns>
        <see cref="T:System.Exception" /> 的訊息 (若有的話)。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.ExceptionType">
      <summary>取得或設定 <see cref="T:System.Exception" /> 的類型 (若可用)。</summary>
      <returns>
        <see cref="T:System.Exception" /> 的類型 (若有的話)。</returns>
    </member>
    <member name="M:System.Web.Http.HttpError.GetPropertyValue``1(System.String)">
      <summary>從此錯誤執行個體取得特別的屬性值。</summary>
      <returns>此錯誤執行個體的特別屬性值。</returns>
      <param name="key">錯誤屬性的名稱。</param>
      <typeparam name="TValue">屬性的型別。</typeparam>
    </member>
    <member name="P:System.Web.Http.HttpError.InnerException">
      <summary>取得與此執行個體相關的內部 <see cref="T:System.Exception" /> (若有的話)。</summary>
      <returns>與此執行個體相關的內部 <see cref="T:System.Exception" /> (若有的話)。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.Message">
      <summary>取得或設定高層級、使用者可見的訊息說明了錯誤原因。此欄位中的資訊應該視為可公開，且可透過網際網路加以取用，包括 <see cref="T:System.Web.Http.IncludeErrorDetailPolicy" />。因此，請小心不要透露有關伺服器或應用程式的敏感資訊。</summary>
      <returns>高層級、使用者可見的訊息說明了錯誤原因。此欄位中的資訊應該視為可公開，且可透過網際網路加以取用，包括 <see cref="T:System.Web.Http.IncludeErrorDetailPolicy" />。因此，請小心不要透露有關伺服器或應用程式的敏感資訊。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.MessageDetail">
      <summary>取得或設定錯誤的詳細說明，讓開發人員了解確切的失敗原因。</summary>
      <returns>錯誤的詳細說明，讓開發人員了解確切的失敗原因。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.ModelState">
      <summary>取得 <see cref="P:System.Web.Http.HttpError.ModelState" /> 包含在模型繫結期間發生的錯誤資訊。</summary>
      <returns>
        <see cref="P:System.Web.Http.HttpError.ModelState" /> 包含在模型繫結期間發生的錯誤資訊。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.StackTrace">
      <summary>取得或設定與此執行個體相關的堆疊追蹤資訊 (若有的話)。</summary>
      <returns>與此執行個體相關的堆疊追蹤資訊 (若有的話)。</returns>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#GetSchema">
      <summary>此方法已經保留，所以無法使用。</summary>
      <returns>永遠傳回 null。</returns>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#ReadXml(System.Xml.XmlReader)">
      <summary>從其 XML 表示法產生 <see cref="T:System.Web.Http.HttpError" /> 執行個體。</summary>
      <param name="reader">將物件還原序列化的來源 XmlReader 資料流。</param>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#WriteXml(System.Xml.XmlWriter)">
      <summary>將 <see cref="T:System.Web.Http.HttpError" /> 執行個體轉換為其 XML 表示法。</summary>
      <param name="writer">將物件還原序列化的目標 XmlWriter 資料流。</param>
    </member>
    <member name="T:System.Web.Http.HttpErrorKeys">
      <summary> 提供索引鍵以查詢儲存在 <see cref="T:System.Web.Http.HttpError" /> 字典中的錯誤資訊。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ErrorCodeKey">
      <summary> 提供 ErrorCode 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ExceptionMessageKey">
      <summary> 提供 ExceptionMessag 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ExceptionTypeKey">
      <summary> 提供 ExceptionType 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.InnerExceptionKey">
      <summary> 提供 InnerException 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.MessageDetailKey">
      <summary> 提供 MessageDetail 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.MessageKey">
      <summary> 提供 Message 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.MessageLanguageKey">
      <summary> 提供 MessageLanguage 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ModelStateKey">
      <summary> 提供 ModelState 的索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.StackTraceKey">
      <summary> 提供 StackTrace 的索引鍵。 </summary>
    </member>
    <member name="T:System.Web.Http.HttpGetAttribute">
      <summary>指定支援 GET HTTP 方法的動作。</summary>
    </member>
    <member name="M:System.Web.Http.HttpGetAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpGetAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.HttpGetAttribute.HttpMethods">
      <summary>取得對應至此屬性的 HTTP 方法。</summary>
      <returns>對應至此屬性的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpHeadAttribute">
      <summary> 指定支援 HEAD HTTP 方法的動作。</summary>
    </member>
    <member name="M:System.Web.Http.HttpHeadAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpHeadAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.HttpHeadAttribute.HttpMethods">
      <summary>取得對應至此屬性的 HTTP 方法。</summary>
      <returns>對應至此屬性的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpOptionsAttribute">
      <summary>表示用於限制 HTTP 方法只處理 HTTP OPTIONS 要求的屬性。</summary>
    </member>
    <member name="M:System.Web.Http.HttpOptionsAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpOptionsAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.HttpOptionsAttribute.HttpMethods">
      <summary>取得對應至此屬性的 HTTP 方法。</summary>
      <returns>對應至此屬性的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpPatchAttribute">
      <summary> 指定支援 PATCH HTTP 方法的動作。</summary>
    </member>
    <member name="M:System.Web.Http.HttpPatchAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpPatchAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.HttpPatchAttribute.HttpMethods">
      <summary>取得對應至此屬性的 HTTP 方法。</summary>
      <returns>對應至此屬性的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpPostAttribute">
      <summary>指定支援 POST HTTP 方法的動作。</summary>
    </member>
    <member name="M:System.Web.Http.HttpPostAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpPostAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.HttpPostAttribute.HttpMethods">
      <summary>取得對應至此屬性的 HTTP 方法。</summary>
      <returns>對應至此屬性的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpPutAttribute">
      <summary>表示用於限制 HTTP 方法只處理 HTTP PUT 要求的屬性。</summary>
    </member>
    <member name="M:System.Web.Http.HttpPutAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpPutAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.HttpPutAttribute.HttpMethods">
      <summary>取得對應至此屬性的 HTTP 方法。</summary>
      <returns>對應至此屬性的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpResponseException">
      <summary> 例外狀況，允許將指定的 <see cref="T:System.Net.Http.HttpResponseMessage" /> 傳回用戶端。</summary>
    </member>
    <member name="M:System.Web.Http.HttpResponseException.#ctor(System.Net.Http.HttpResponseMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.HttpResponseException" /> 類別的新執行個體。</summary>
      <param name="response">要傳回用戶端的 HTTP 回應。</param>
    </member>
    <member name="M:System.Web.Http.HttpResponseException.#ctor(System.Net.HttpStatusCode)">
      <summary> 初始化 <see cref="T:System.Web.Http.HttpResponseException" /> 類別的新執行個體。</summary>
      <param name="statusCode">回應的狀態碼。</param>
    </member>
    <member name="P:System.Web.Http.HttpResponseException.Response">
      <summary>取得要傳回用戶端的 HTTP 回應。</summary>
      <returns>表示 HTTP 回應的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.HttpRouteCollection">
      <summary>
        <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體的集合。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpRouteCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.HttpRouteCollection" /> 類別的新執行個體。</summary>
      <param name="virtualPathRoot">虛擬路徑根。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Add(System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>將 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體加入集合中。</summary>
      <param name="name">路徑的名稱。</param>
      <param name="route">要加入集合中的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Clear">
      <summary>從集合移除所有項目。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Contains(System.Web.Http.Routing.IHttpRoute)">
      <summary>判斷集合是否包含特定 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</summary>
      <returns>如果在集合中找到 <see cref="T:System.Web.Http.Routing.IHttpRoute" />，則為 true，否則為 false。</returns>
      <param name="item">要放置在集合中的物件。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.ContainsKey(System.String)">
      <summary>判斷集合是否包含具有指定索引鍵的項目。</summary>
      <returns>如果集合包含具有此索引鍵的項目，則為 true，否則為 false。</returns>
      <param name="name">要放置在集合中的索引鍵。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.Routing.IHttpRoute}[],System.Int32)">
      <summary>從特定的陣列索引開始，將集合的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體複製到陣列中。</summary>
      <param name="array">從集合複製項目之目的地的陣列。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CopyTo(System.Web.Http.Routing.IHttpRoute[],System.Int32)">
      <summary>從特定的陣列索引開始，將集合的路由名稱和 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體複製到陣列中。</summary>
      <param name="array">從集合複製項目之目的地的陣列。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Count">
      <summary>取得集合中的項目數目。</summary>
      <returns>集合中的項目數目。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>建立 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</summary>
      <returns>新的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</returns>
      <param name="routeTemplate">路由範本。</param>
      <param name="defaults">包含預設路由參數的物件。</param>
      <param name="constraints">包含路由條件約束的物件。</param>
      <param name="dataTokens">路由資料權杖。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Net.Http.HttpMessageHandler)">
      <summary>建立 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</summary>
      <returns>新的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</returns>
      <param name="routeTemplate">路由範本。</param>
      <param name="defaults">包含預設路由參數的物件。</param>
      <param name="constraints">包含路由條件約束的物件。</param>
      <param name="dataTokens">路由資料權杖。</param>
      <param name="handler">路由的訊息處理常式。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Object,System.Object)">
      <summary>建立 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</summary>
      <returns>新的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</returns>
      <param name="routeTemplate">路由範本。</param>
      <param name="defaults">包含預設路由參數的物件。</param>
      <param name="constraints">包含路由條件約束的物件。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Dispose">
      <summary>執行與免費、釋放或重設 Unmanged 資源相關的應用程式定義的工作。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Dispose(System.Boolean)">
      <summary>釋放物件所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetEnumerator">
      <summary>傳回會逐一查看集合的列舉值。</summary>
      <returns>可用來逐一查看集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetRouteData(System.Net.Http.HttpRequestMessage)">
      <summary>取得指定之 HTTP 要求的路由資料。</summary>
      <returns>表示路由資料的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" /> 執行個體。</returns>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>取得虛擬路徑。</summary>
      <returns>表示虛擬路徑的 <see cref="T:System.Web.Http.Routing.IHttpVirtualPathData" /> 執行個體。</returns>
      <param name="request">HTTP 要求。</param>
      <param name="name">路徑名稱。</param>
      <param name="values">路徑值。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Insert(System.Int32,System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>將 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體插入集合中。</summary>
      <param name="index">應該插入 <paramref name="value" /> 之以零起始的索引。</param>
      <param name="name">路徑名稱。</param>
      <param name="value">要插入的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。此值不能是 null。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.IsReadOnly">
      <summary>取得值，這個值表示集合是否為唯讀。</summary>
      <returns>如果集合是唯讀的，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Item(System.Int32)">
      <summary>取得或設定位於指定索引處的項目。</summary>
      <returns>位於指定索引處的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="index">索引。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Item(System.String)">
      <summary>取得或設定具有指定之路由名稱的項目。</summary>
      <returns>位於指定索引處的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="name">路徑名稱。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.OnGetEnumerator">
      <summary>在內部呼叫以取得集合的列舉值。</summary>
      <returns>可用來逐一查看集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Remove(System.String)">
      <summary>從集合中移除 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體。</summary>
      <returns>如果成功移除項目，則為 true，否則為 false。如果在集合中找不到 <paramref name="name" />，這個方法也會傳回 false。</returns>
      <param name="name">要移除之路由的名稱。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#Generic#ICollection{T}#Add(System.Web.Http.Routing.IHttpRoute)">
      <summary>將項目加入至集合。</summary>
      <param name="route">要加入集合中的物件。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#Generic#ICollection{T}#Remove(System.Web.Http.Routing.IHttpRoute)">
      <summary>從集合中移除特定物件的第一個符合項目。</summary>
      <returns>如果已成功從集合中移除 <paramref name="route" />，則為 true，否則為 false。如果在原始集合中找不到 <paramref name="route" />，這個方法也會傳回 false。</returns>
      <param name="route">要從集合移除的物件。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> 物件，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.TryGetValue(System.String,System.Web.Http.Routing.IHttpRoute@)">
      <summary>取得具有指定之路由名稱的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</summary>
      <returns>如果集合包含具有指定之名稱的項目，則為 true，否則為 false。</returns>
      <param name="name">路徑名稱。</param>
      <param name="route">當這個方法傳回時，如果找到路由名稱，則包含 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 執行個體，否則為 null。傳遞此參數時不需設定初始值。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.ValidateConstraint(System.String,System.String,System.Object)">
      <summary>驗證條件約束對於由 <see cref="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Net.Http.HttpMessageHandler)" /> 方法呼叫所建立的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 有效。</summary>
      <param name="routeTemplate">路由範本。</param>
      <param name="name">條件約束名稱。</param>
      <param name="constraint">條件約束物件。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.VirtualPathRoot">
      <summary>取得虛擬路徑根。</summary>
      <returns>虛擬路徑根。</returns>
    </member>
    <member name="T:System.Web.Http.HttpRouteCollectionExtensions">
      <summary>
        <see cref="T:System.Web.Http.HttpRouteCollection" /> 的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.IgnoreRoute(System.Web.Http.HttpRouteCollection,System.String,System.String)">
      <summary>忽略指定的路由。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="routeName">要忽略之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.IgnoreRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object)">
      <summary>忽略指定的路由。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="routeName">要忽略之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="constraints">為路由範本指定值的一組運算式。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpBatchRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Web.Http.Batch.HttpBatchHandler)">
      <summary> 對應指定路徑以處理 HTTP 批次要求。</summary>
      <param name="routes">應用程式的路由集合。</param>
      <param name="routeName">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="batchHandler">處理批次要求的 <see cref="T:System.Web.Http.Batch.HttpBatchHandler" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String)">
      <summary>對應指定的路由範本。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object)">
      <summary>對應指定的路由範本並設定預設路由值。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="defaults">包含預設路由值的物件。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object,System.Object)">
      <summary>對應指定的路由範本並設定預設路由值和條件約束。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="defaults">包含預設路由值的物件。</param>
      <param name="constraints">為 <paramref name="routeTemplate" /> 指定值的一組運算式。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object,System.Object,System.Net.Http.HttpMessageHandler)">
      <summary>對應指定的路由範本並設定預設的路由值、條件約束和端點訊息處理常式。</summary>
      <returns>已對應之路徑的參考。</returns>
      <param name="routes">應用程式的路由集合。</param>
      <param name="name">要對應之路由的名稱。</param>
      <param name="routeTemplate">路由的路由範本。</param>
      <param name="defaults">包含預設路由值的物件。</param>
      <param name="constraints">為 <paramref name="routeTemplate" /> 指定值的一組運算式。</param>
      <param name="handler">要求將要發送至的處理常式。</param>
    </member>
    <member name="T:System.Web.Http.HttpServer">
      <summary> 定義 <see cref="T:System.Net.Http.HttpMessageHandler" /> 的實作，其會發送傳入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 並建立 <see cref="T:System.Net.Http.HttpResponseMessage" /> 作為結果。</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor">
      <summary>利用預設組態和發送器，初始化 <see cref="T:System.Web.Http.HttpServer" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary> 利用指定的發送器，初始化 <see cref="T:System.Web.Http.HttpServer" /> 類別的新執行個體。</summary>
      <param name="dispatcher">將會處理傳入要求的 HTTP 發送器。</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>利用指定的組態，初始化 <see cref="T:System.Web.Http.HttpServer" /> 類別的新執行個體。</summary>
      <param name="configuration">用來設定這個執行個體的 <see cref="T:System.Web.Http.HttpConfiguration" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Web.Http.HttpConfiguration,System.Net.Http.HttpMessageHandler)">
      <summary>利用指定的組態和發送器，初始化 <see cref="T:System.Web.Http.HttpServer" /> 類別的新執行個體。</summary>
      <param name="configuration">用來設定這個執行個體的 <see cref="T:System.Web.Http.HttpConfiguration" />。</param>
      <param name="dispatcher">將會處理傳入要求的 HTTP 發送器。</param>
    </member>
    <member name="P:System.Web.Http.HttpServer.Configuration">
      <summary>取得用來設定這個執行個體的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>用來設定這個執行個體的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="P:System.Web.Http.HttpServer.Dispatcher">
      <summary>取得會處理傳入要求的 HTTP 發送器。</summary>
      <returns>會處理傳入要求的 HTTP 發送器。</returns>
    </member>
    <member name="M:System.Web.Http.HttpServer.Dispose(System.Boolean)">
      <summary>釋放物件所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.Initialize">
      <summary>可能是作業的伺服器。</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>發送傳入 <see cref="T:System.Net.Http.HttpRequestMessage" />。</summary>
      <returns> 表示非同步作業的工作。</returns>
      <param name="request">要發送的要求。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="T:System.Web.Http.IHttpActionResult">
      <summary>判斷非同步建立 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的命令。</summary>
    </member>
    <member name="M:System.Web.Http.IHttpActionResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>非同步建立 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>工作完成時，會包含 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="T:System.Web.Http.IncludeErrorDetailPolicy">
      <summary>指定是否應該在錯誤訊息中包含錯誤詳細資料 (例如，例外狀況訊息和堆疊追蹤)。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Always">
      <summary>永遠包含錯誤詳細資料。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Default">
      <summary>對於主機環境使用預設的行為。針對 ASP.NET 代管，使用 Web.config 檔案中來自 customErrors 項目中的值。對於自助代管，使用 <see cref="F:System.Web.Http.IncludeErrorDetailPolicy.LocalOnly" /> 值。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.LocalOnly">
      <summary>只有在回應本機要求時才會包含錯誤詳細資料。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Never">
      <summary>絕對不要包含錯誤詳細資料。 </summary>
    </member>
    <member name="T:System.Web.Http.NonActionAttribute">
      <summary>表示用來指出控制器方法不是動作方法的屬性。</summary>
    </member>
    <member name="M:System.Web.Http.NonActionAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.NonActionAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Web.Http.OverrideActionFiltersAttribute">
      <summary>表示覆寫在較高層級定義的動作篩選屬性。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideActionFiltersAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.OverrideActionFiltersAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.OverrideActionFiltersAttribute.AllowMultiple">
      <summary>取得值，這個值表示動作篩選條件是否允許多個屬性。</summary>
      <returns>如果篩選條件允許多個屬性，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.OverrideActionFiltersAttribute.FiltersToOverride">
      <summary>取得要覆寫的篩選條件類型。</summary>
      <returns>要覆寫的篩選條件類型。</returns>
    </member>
    <member name="T:System.Web.Http.OverrideAuthenticationAttribute">
      <summary>表示覆寫在較高層級定義的驗證篩選屬性。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideAuthenticationAttribute.#ctor"></member>
    <member name="P:System.Web.Http.OverrideAuthenticationAttribute.AllowMultiple"></member>
    <member name="P:System.Web.Http.OverrideAuthenticationAttribute.FiltersToOverride"></member>
    <member name="T:System.Web.Http.OverrideAuthorizationAttribute">
      <summary>表示覆寫在較高層級定義的授權篩選屬性。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideAuthorizationAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.OverrideAuthorizationAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.OverrideAuthorizationAttribute.AllowMultiple">
      <summary>取得或設定布林值，這個值表示是否可以為單一程式元素指定所指出屬性的多個執行個體。</summary>
      <returns>如果可以指定多個執行個體，為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.OverrideAuthorizationAttribute.FiltersToOverride">
      <summary>取得要覆寫屬性的篩選條件類型。</summary>
      <returns>要覆寫屬性的篩選條件類型。</returns>
    </member>
    <member name="T:System.Web.Http.OverrideExceptionFiltersAttribute">
      <summary>表示覆寫在較高層級定義的例外狀況篩選屬性。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideExceptionFiltersAttribute.#ctor"></member>
    <member name="P:System.Web.Http.OverrideExceptionFiltersAttribute.AllowMultiple"></member>
    <member name="P:System.Web.Http.OverrideExceptionFiltersAttribute.FiltersToOverride"></member>
    <member name="T:System.Web.Http.ParameterBindingAttribute">
      <summary>位於可產生 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 之參數或型別上的屬性。如果此屬性位於 type-declaration 上，則該屬性如同存在於該型別的所有動作參數上。</summary>
    </member>
    <member name="M:System.Web.Http.ParameterBindingAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ParameterBindingAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ParameterBindingAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>取得參數繫結。</summary>
      <returns>參數繫結。</returns>
      <param name="parameter">參數描述。</param>
    </member>
    <member name="T:System.Web.Http.RouteAttribute">
      <summary>放置動作以直接透過路由公開。</summary>
    </member>
    <member name="M:System.Web.Http.RouteAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.RouteAttribute" /> 類別的新執行個體。 </summary>
    </member>
    <member name="M:System.Web.Http.RouteAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.RouteAttribute" /> 類別的新執行個體。</summary>
      <param name="template">描述要用於比對 URI 模式的路由範本。</param>
    </member>
    <member name="P:System.Web.Http.RouteAttribute.Name">
      <returns>傳回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Web.Http.RouteAttribute.Order">
      <returns>傳回 <see cref="T:System.Int32" />。</returns>
    </member>
    <member name="M:System.Web.Http.RouteAttribute.System#Web#Http#Routing#IDirectRouteFactory#CreateRoute(System.Web.Http.Routing.DirectRouteFactoryContext)"></member>
    <member name="P:System.Web.Http.RouteAttribute.Template">
      <returns>傳回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.RouteParameter">
      <summary>
        <see cref="T:System.Web.Http.RouteParameter" /> 類別可用來指出路由參數的相關屬性 (常值和預留位置，位於 <see cref="M:IHttpRoute.RouteTemplate" /> 的區段內)。舉例來說，它可以用來指出路由參數為選用參數。</summary>
    </member>
    <member name="F:System.Web.Http.RouteParameter.Optional">
      <summary>選用參數。</summary>
    </member>
    <member name="M:System.Web.Http.RouteParameter.ToString">
      <summary>傳回代表這個執行個體的 <see cref="T:System.String" />。</summary>
      <returns>代表這個執行個體的 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.RoutePrefixAttribute">
      <summary> 以套用到控制器中所有動作的路由前置字元來標註控制器。</summary>
    </member>
    <member name="M:System.Web.Http.RoutePrefixAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.RoutePrefixAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.RoutePrefixAttribute.#ctor(System.String)">
      <summary> 初始化 <see cref="T:System.Web.Http.RoutePrefixAttribute" /> 類別的新執行個體。</summary>
      <param name="prefix">控制器的路由前置字元。</param>
    </member>
    <member name="P:System.Web.Http.RoutePrefixAttribute.Prefix">
      <summary> 取得路由前置字元。</summary>
    </member>
    <member name="T:System.Web.Http.ServicesExtensions">
      <summary>針對從 <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> 物件取得的服務，提供型別安全存取子。</summary>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionInvoker(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionSelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionValueBinder(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetApiExplorer(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetAssembliesResolver(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetBodyModelValidator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Validation.IBodyModelValidator" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Validation.IBodyModelValidator" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetContentNegotiator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetDocumentationProvider(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Description.IDocumentationProvider" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Description.IDocumentationProvider" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetExceptionHandler(System.Web.Http.Controllers.ServicesContainer)">
      <summary>如果有，傳回註冊的未處理例外狀況處理常式。</summary>
      <returns>如果出現則為註冊的未處理例外狀況處理常式，否則為 null。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetExceptionLoggers(System.Web.Http.Controllers.ServicesContainer)">
      <summary>傳回註冊的未處理例外狀況記錄器集合。</summary>
      <returns>註冊的未處理例外狀況記錄器集合。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetFilterProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Filters.IFilterProvider" /> 集合。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Filters.IFilterProvider" /> 物件的集合。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHostBufferPolicySelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerActivator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 執行個體，如果未註冊任何執行個體，則為 null。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerSelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerTypeResolver(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelBinderProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 集合。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 物件的集合。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelMetadataProvider(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelValidatorProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 集合。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 物件的集合。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetTraceManager(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Tracing.ITraceManager" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Tracing.ITraceManager" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetTraceWriter(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> 服務。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> 執行個體。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetValueProviderFactories(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 集合。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 物件的集合。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="T:System.Web.Http.SingleResult">
      <summary>表示包含 0 或一個實體的 <see cref="T:System.Linq.IQueryable" />。與來自 System.Web.Http.OData 或 System.Web.OData 命名空間的 [EnableQuery] 搭配使用。</summary>
    </member>
    <member name="M:System.Web.Http.SingleResult.#ctor(System.Linq.IQueryable)">
      <summary>初始化 <see cref="T:System.Web.Http.SingleResult" /> 類別的新執行個體。</summary>
      <param name="queryable">包含 0 或一個實體的 <see cref="T:System.Linq.IQueryable" />。</param>
    </member>
    <member name="M:System.Web.Http.SingleResult.Create``1(System.Linq.IQueryable{``0})">
      <summary>從 <see cref="T:System.Linq.IQueryable`1" /> 建立 <see cref="T:System.Web.Http.SingleResult`1" />。不必明確指定類型 <paramref name="T" />，即可具現化 <see cref="T:System.Web.Http.SingleResult`1" /> 物件的 Helper 方法。</summary>
      <returns>建立的 <see cref="T:System.Web.Http.SingleResult`1" />。</returns>
      <param name="queryable">包含 0 或一個實體的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="T">資料來源中的資料類型。</typeparam>
    </member>
    <member name="P:System.Web.Http.SingleResult.Queryable">
      <summary>包含 0 或一個實體的 <see cref="T:System.Linq.IQueryable" />。</summary>
    </member>
    <member name="T:System.Web.Http.SingleResult`1">
      <summary>表示包含 0 或一個實體的 <see cref="T:System.Linq.IQueryable`1" />。與來自 System.Web.Http.OData 或 System.Web.OData 命名空間的 [EnableQuery] 搭配使用。</summary>
      <typeparam name="T">資料來源中的資料類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.SingleResult`1.#ctor(System.Linq.IQueryable{`0})">
      <summary>初始化 <see cref="T:System.Web.Http.SingleResult`1" /> 類別的新執行個體。</summary>
      <param name="queryable">包含 0 或一個實體的 <see cref="T:System.Linq.IQueryable`1" />。</param>
    </member>
    <member name="P:System.Web.Http.SingleResult`1.Queryable">
      <summary>包含 0 或一個實體的 <see cref="T:System.Linq.IQueryable`1" />。</summary>
    </member>
    <member name="T:System.Web.Http.Batch.BatchExecutionOrder">
      <summary> 定義批次要求的執行順序。</summary>
    </member>
    <member name="F:System.Web.Http.Batch.BatchExecutionOrder.NonSequential">
      <summary> 非依序執行批次要求。 </summary>
    </member>
    <member name="F:System.Web.Http.Batch.BatchExecutionOrder.Sequential">
      <summary> 依序執行批次要求。</summary>
    </member>
    <member name="T:System.Web.Http.Batch.BatchHttpRequestMessageExtensions">
      <summary> 提供 <see cref="T:System.Net.Http.HttpRequestMessage" /> 類別的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.Batch.BatchHttpRequestMessageExtensions.CopyBatchRequestProperties(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpRequestMessage)">
      <summary> 從其他 <see cref="T:System.Net.Http.HttpRequestMessage" /> 複製屬性。</summary>
      <param name="subRequest">HTTP 子要求。</param>
      <param name="batchRequest">包含要複製的屬性之批次要求。</param>
    </member>
    <member name="T:System.Web.Http.Batch.DefaultHttpBatchHandler">
      <summary>表示 <see cref="T:System.Web.Http.Batch.HttpBatchHandler" /> 的預設實作，會將 HTTP 要求/回應訊息編碼為 MIME 多組件。</summary>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.#ctor(System.Web.Http.HttpServer)">
      <summary>初始化 <see cref="T:System.Web.Http.Batch.DefaultHttpBatchHandler" /> 類別的新執行個體。</summary>
      <param name="httpServer">處理個別批次要求的 <see cref="T:System.Web.Http.HttpServer" />。</param>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.CreateResponseMessageAsync(System.Collections.Generic.IList{System.Net.Http.HttpResponseMessage},System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>建立批次回應訊息。</summary>
      <returns>批次回應訊息。</returns>
      <param name="responses">批次要求的回應。</param>
      <param name="request">包含所有批次要求的原始要求。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ExecuteRequestMessagesAsync(System.Collections.Generic.IEnumerable{System.Net.Http.HttpRequestMessage},System.Threading.CancellationToken)">
      <summary>執行批次要求訊息。</summary>
      <returns>批次要求的 <see cref="T:System.Net.Http.HttpResponseMessage" /> 集合。</returns>
      <param name="requests">批次要求訊息集合。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.Batch.DefaultHttpBatchHandler.ExecutionOrder">
      <summary>取得或設定批次要求的執行順序。預設執行順序為連續的。</summary>
      <returns>批次要求的執行順序。預設執行順序為連續的。</returns>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ParseBatchRequestsAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>將傳入批次要求轉換為要求訊息集合。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> 的集合。</returns>
      <param name="request">包含批次要求訊息的要求。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ProcessBatchAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>處理批次要求。</summary>
      <returns>作業的結果。</returns>
      <param name="request">批次要求。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.Batch.DefaultHttpBatchHandler.SupportedContentTypes">
      <summary>取得批次要求支援的內容類型。</summary>
      <returns>批次要求支援的內容類型。</returns>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ValidateRequest(System.Net.Http.HttpRequestMessage)">
      <summary>驗證包含批次要求訊息的傳入要求。</summary>
      <param name="request">包含批次要求訊息的要求。</param>
    </member>
    <member name="T:System.Web.Http.Batch.HttpBatchHandler">
      <summary>定義指定抽象方法以處理 HTTP 批次要求。</summary>
    </member>
    <member name="M:System.Web.Http.Batch.HttpBatchHandler.#ctor(System.Web.Http.HttpServer)">
      <summary>初始化 <see cref="T:System.Web.Http.Batch.HttpBatchHandler" /> 類別的新執行個體。</summary>
      <param name="httpServer">處理個別批次要求的 <see cref="T:System.Web.Http.HttpServer" />。</param>
    </member>
    <member name="P:System.Web.Http.Batch.HttpBatchHandler.Invoker">
      <summary>取得啟動程式以傳送批次要求到 <see cref="T:System.Web.Http.HttpServer" />。</summary>
      <returns>傳送批次要求到 <see cref="T:System.Web.Http.HttpServer" /> 的啟動程式。</returns>
    </member>
    <member name="M:System.Web.Http.Batch.HttpBatchHandler.ProcessBatchAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>處理做為 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的傳入批次要求。</summary>
      <returns>批次回應。</returns>
      <param name="request">批次要求。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="M:System.Web.Http.Batch.HttpBatchHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>非同步傳送批次處理常式。</summary>
      <returns>作業的結果。</returns>
      <param name="request">傳送要求。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ApiControllerActionInvoker">
      <summary>叫用控制器的動作方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionInvoker.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionInvoker.InvokeActionAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>使用指定的控制器內容，以非同步方式叫用指定的動作。</summary>
      <returns>叫用的動作。</returns>
      <param name="actionContext">控制器內容。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ApiControllerActionSelector">
      <summary>表示反映式動作選取器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.GetActionMapping(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>取得 <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" /> 的動作對應。</summary>
      <returns>動作對應。</returns>
      <param name="controllerDescriptor">描述控制器的資訊。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.SelectAction(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>為 <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" /> 選取動作。</summary>
      <returns>選取的動作。</returns>
      <param name="controllerContext">控制器內容。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ControllerServices">
      <summary> 代表控制器特有服務的容器。此方法會從其父系 <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> 製作服務的陰影。控制器可以在此設定服務，或落入更加全域的服務集。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.#ctor(System.Web.Http.Controllers.ServicesContainer)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ControllerServices" /> 類別的新執行個體。</summary>
      <param name="parent">父系服務容器。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.ClearSingle(System.Type)">
      <summary>移除預設服務中的單一執行個體服務。</summary>
      <param name="serviceType">服務的型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetService(System.Type)">
      <summary>取得指定之型別的服務。</summary>
      <returns>服務的第一個執行個體，如果找不到服務，則為 null。</returns>
      <param name="serviceType">服務的型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetServiceInstances(System.Type)">
      <summary>取得指定之服務型別的服務物件清單，並驗證服務型別。</summary>
      <returns>指定之型別的服務物件清單。</returns>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetServices(System.Type)">
      <summary>取得指定之服務型別的服務物件清單。</summary>
      <returns>指定之型別的服務物件清單，如果找不到服務，則為空白清單。</returns>
      <param name="serviceType">服務的型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.IsSingleService(System.Type)">
      <summary>查詢服務型別是否為單一執行個體。</summary>
      <returns>如果服務型別至少有一個執行個體，則為 true，如果服務型別支援多個執行個體，則為 false。</returns>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.ReplaceSingle(System.Type,System.Object)">
      <summary>取代單一執行個體服務物件。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="service">取代先前執行個體的服務物件。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionBinding">
      <summary>描述繫結將會「如何」發生，而不實際進行繫結。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.#ctor(System.Web.Http.Controllers.HttpActionDescriptor,System.Web.Http.Controllers.HttpParameterBinding[])">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> 類別的新執行個體。</summary>
      <param name="actionDescriptor">對此繫結所適用之動作的返回指標。</param>
      <param name="bindings">每個參數的同步繫結。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionBinding.ActionDescriptor">
      <summary>取得或設定對此繫結所適用之動作的返回指標。</summary>
      <returns>對此繫結所適用之動作的返回指標。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.ExecuteBindingAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>非同步執行指定之要求內容所適用的繫結。</summary>
      <returns>當繫結完成時，會發出信號的工作。</returns>
      <param name="actionContext">繫結的動作內容。這包含會植入的參數字典。</param>
      <param name="cancellationToken">取消繫結作業時使用的取消權杖。或繫結器也可將參數繫結至此。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionBinding.ParameterBindings">
      <summary>取得或設定每個參數的同步繫結。</summary>
      <returns>每個參數的同步繫結。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionContext">
      <summary>包含執行動作的資訊。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContext.#ctor">
      <summary> 初始化 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContext.#ctor(System.Web.Http.Controllers.HttpControllerContext,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 類別的新執行個體。</summary>
      <param name="controllerContext">控制器內容。</param>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ActionArguments">
      <summary>取得動作引數的清單。</summary>
      <returns>動作引數的清單。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ActionDescriptor">
      <summary>取得或設定動作內容的動作描述元。</summary>
      <returns>動作描述元。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ControllerContext">
      <summary>取得或設定控制器內容。</summary>
      <returns>控制器內容。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ModelState">
      <summary>取得內容的模型狀態字典。</summary>
      <returns>模型狀態字典。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.Request">
      <summary>取得動作內容的要求訊息。</summary>
      <returns>動作內容的要求訊息。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.RequestContext">
      <summary>取得目前要求內容。</summary>
      <returns>目前要求內容。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.Response">
      <summary>取得或設定動作內容的回應訊息。</summary>
      <returns>動作內容的回應訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionContextExtensions">
      <summary>包含 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.Bind(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.Bind(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.IModelBinder})"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetMetadataProvider(System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetValidatorProviders(System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetValidators(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Metadata.ModelMetadata)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.TryBindStrongModel``1(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.String,System.Web.Http.Metadata.ModelMetadataProvider,``0@)">
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionDescriptor">
      <summary>提供動作方法的相關資訊。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.#ctor(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>使用可描述動作控制器的指定資訊，初始化 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 類別的新執行個體。</summary>
      <param name="controllerDescriptor">可描述動作控制器的資訊。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ActionBinding">
      <summary>取得或設定描述動作的繫結。</summary>
      <returns>描述動作的繫結。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ActionName">
      <summary>取得動作的名稱。</summary>
      <returns>動作的名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.Configuration">
      <summary>取得或設定動作組態。</summary>
      <returns>動作組態。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ControllerDescriptor">
      <summary>取得可描述動作控制器的資訊。</summary>
      <returns>可描述動作控制器的資訊。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
      <summary>執行所述的動作並傳回 <see cref="T:System.Threading.Tasks.Task`1" />，此工作完成後，就會包含動作的傳回值。</summary>
      <returns>一個 <see cref="T:System.Threading.Tasks.Task`1" />，一旦完成後，就會包含動作的傳回值。</returns>
      <param name="controllerContext">控制器內容。</param>
      <param name="arguments">引數清單。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetCustomAttributes``1">
      <summary>傳回與動作描述元相關聯的自訂屬性。</summary>
      <returns>與動作描述元相關聯的自訂屬性。</returns>
      <typeparam name="T">動作描述元。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetCustomAttributes``1(System.Boolean)">
      <summary>取得動作的自訂屬性。</summary>
      <returns>套用至此動作的自訂屬性集合。</returns>
      <param name="inherit">true 表示要搜尋此動作的繼承鏈結以尋找屬性，否則為 false。</param>
      <typeparam name="T">要搜尋的屬性類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetFilterPipeline">
      <summary>擷取指定之組態和動作的篩選條件。</summary>
      <returns>指定之組態和動作的篩選條件。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetFilters">
      <summary>擷取動作描述元的篩選條件。</summary>
      <returns>動作描述元的篩選條件。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetParameters">
      <summary>擷取動作描述元的參數。</summary>
      <returns>動作描述元的參數。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.Properties">
      <summary>取得與此執行個體相關的屬性。</summary>
      <returns>與此執行個體相關的屬性。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ResultConverter">
      <summary>取得轉換器，以便將呼叫 ExecuteAsync(HttpControllerContext, IDictionaryString, Object)" 的結果正確地轉換為 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的執行個體。</summary>
      <returns>動作結果轉換器。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ReturnType">
      <summary>取得描述元的傳回類型。</summary>
      <returns>描述元的傳回類型。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.SupportedHttpMethods">
      <summary>取得描述元支援的 HTTP 方法集合。</summary>
      <returns>描述元支援的 HTTP 方法集合。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerContext">
      <summary>包含單一 HTTP 作業的資訊。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor(System.Web.Http.Controllers.HttpRequestContext,System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Web.Http.Controllers.IHttpController)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 類別的新執行個體。</summary>
      <param name="requestContext">要求內容。</param>
      <param name="request">HTTP 要求。</param>
      <param name="controllerDescriptor">控制器描述元。</param>
      <param name="controller">控制器。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IHttpRouteData,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 類別的新執行個體。</summary>
      <param name="configuration">設定。</param>
      <param name="routeData">路徑資料。</param>
      <param name="request">要求。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Configuration">
      <summary>取得或進行設定。</summary>
      <returns>設定。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Controller">
      <summary>取得或設定 HTTP 控制器。</summary>
      <returns>HTTP 控制器。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.ControllerDescriptor">
      <summary>取得或設定控制器描述元。</summary>
      <returns>控制器描述元。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Request">
      <summary>取得或設定要求。</summary>
      <returns>要求。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.RequestContext">
      <summary>取得或設定要求內容。</summary>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.RouteData">
      <summary>取得或設定路由資料。</summary>
      <returns>路徑資料。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerDescriptor">
      <summary>表示描述 HTTP 控制器的資訊。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.#ctor(System.Web.Http.HttpConfiguration,System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 類別的新執行個體。</summary>
      <param name="configuration">設定。</param>
      <param name="controllerName">控制器名稱。</param>
      <param name="controllerType">控制器類型。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.Configuration">
      <summary>取得或設定與控制器相關聯的設定。</summary>
      <returns>與控制器相關聯的設定。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.ControllerName">
      <summary>取得或設定控制器的名稱。</summary>
      <returns>控制器的名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.ControllerType">
      <summary>取得或設定控制器的類型。</summary>
      <returns>控制器的型別。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.CreateController(System.Net.Http.HttpRequestMessage)">
      <summary>為指定的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 建立控制器執行個體。</summary>
      <returns>建立的控制器執行個體。</returns>
      <param name="request">要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetCustomAttributes``1">
      <summary>擷取控制器之自訂屬性的集合。</summary>
      <returns>自訂屬性的集合。</returns>
      <typeparam name="T">物件的類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetCustomAttributes``1(System.Boolean)">
      <summary> 傳回可以為此描述元控制器指派 &lt;typeparamref name="T" /&gt; 的屬性集合。</summary>
      <returns>與此控制器相關的屬性集合。</returns>
      <param name="inherit">true 表示要搜尋此控制器的繼承鏈結以尋找屬性，否則為 false。</param>
      <typeparam name="T">用來篩選屬性的集合。使用 <see cref="T:System.Object" /> 的值來擷取所有屬性。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetFilters">
      <summary>傳回與控制器相關聯之篩選條件的集合。</summary>
      <returns>與控制器相關聯之篩選條件的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.Properties">
      <summary>取得與此執行個體相關的屬性。</summary>
      <returns>與此執行個體相關的屬性。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerSettings">
      <summary>包含 HTTP 控制器的設定。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerSettings.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerSettings" /> 類別的新執行個體。</summary>
      <param name="configuration">用來初始化執行個體的組態物件。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.Formatters">
      <summary>取得控制器的 <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 執行個體集合。</summary>
      <returns>
        <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 執行個體的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.ParameterBindingRules">
      <summary>取得控制器的參數繫結函數集合。</summary>
      <returns>參數繫結函數的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.Services">
      <summary>取得控制器的服務執行個體集合。</summary>
      <returns>服務執行個體的集合。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpParameterBinding">
      <summary> 描述如何繫結參數。繫結應該是靜態的 (完全根據描述元) 且可在要求間共用。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 類別的新執行個體。</summary>
      <param name="descriptor">可描述參數的 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" />。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.Descriptor">
      <summary>取得用來初始化此執行個體的 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 執行個體。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.ErrorMessage">
      <summary>如果繫結無效，則取得可描述繫結錯誤的錯誤訊息。</summary>
      <returns>錯誤訊息。如果繫結成功，此值為 null。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>以非同步方式針對指定的要求執行繫結。</summary>
      <returns>代表非同步作業的工作物件。</returns>
      <param name="metadataProvider">用於驗證的中繼資料提供者。</param>
      <param name="actionContext">繫結的動作內容。動作內容包含會填入參數的參數字典。</param>
      <param name="cancellationToken">取消繫結作業的取消基元。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.GetValue(System.Web.Http.Controllers.HttpActionContext)">
      <summary> 從動作內容的引數字典取得參數值。</summary>
      <returns>這個參數在指定的動作內容中的值，如果尚未設定參數，則為 null。</returns>
      <param name="actionContext">動作內容。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.IsValid">
      <summary>取得值，這個值表示繫結是否成功。</summary>
      <returns>如果繫結成功，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.SetValue(System.Web.Http.Controllers.HttpActionContext,System.Object)">
      <summary>在動作內容的引數字典中設定此參數繫結的結果。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="value">參數值。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.WillReadBody">
      <summary>傳回值，這個值表示此 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 執行個體是否會讀取 HTTP 訊息的實體內容。</summary>
      <returns>如果此 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 會讀取實體內容，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpParameterDescriptor">
      <summary>表示 HTTP 參數描述元。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.#ctor(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 類別的新執行個體。</summary>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ActionDescriptor">
      <summary>取得或設定動作描述元。</summary>
      <returns>動作描述元。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Configuration">
      <summary>取得或設定 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.DefaultValue">
      <summary>取得參數的預設值。</summary>
      <returns>參數的預設值。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.GetCustomAttributes``1">
      <summary>擷取參數中自訂屬性的集合。</summary>
      <returns>參數中自訂屬性的集合。</returns>
      <typeparam name="T">自訂屬性的型別。</typeparam>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.IsOptional">
      <summary>取得值，這個值表示參數是否為選用。</summary>
      <returns>如果為選用參數，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterBinderAttribute">
      <summary>取得或設定參數繫結屬性。</summary>
      <returns>參數繫結屬性。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterName">
      <summary>取得參數的名稱。</summary>
      <returns>參數名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterType">
      <summary>取得參數的型別。</summary>
      <returns>參數的型別。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Prefix">
      <summary>取得此參數的前置詞。</summary>
      <returns>此參數的前置詞。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Properties">
      <summary>取得此參數的屬性。</summary>
      <returns>此參數的屬性。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpRequestContext">
      <summary>表示與要求相關聯的內容。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpRequestContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpRequestContext" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.ClientCertificate">
      <summary>取得或設定用戶端憑證。</summary>
      <returns>傳回 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.Configuration">
      <summary>取得或進行設定。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.IncludeErrorDetail">
      <summary>取得或設定一值，表示錯誤詳細資料 (例如例外狀況訊息和推疊追蹤) 是否應該包含在此要求的回應中。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.IsLocal">
      <summary>取得或設定一值，此值表示要求是否源自本機位址。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.Principal">
      <summary>取得或設定主體。</summary>
      <returns>傳回 <see cref="T:System.Security.Principal.IPrincipal" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.RouteData">
      <summary>取得或設定路由資料。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Routing.IHttpRouteData" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.Url">
      <summary>取得或設定用來在其他 API 中產生 URL 的 Factory。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Routing.UrlHelper" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.VirtualPathRoot">
      <summary>取得或設定虛擬路徑根。</summary>
      <returns>傳回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.IActionHttpMethodProvider"></member>
    <member name="P:System.Web.Http.Controllers.IActionHttpMethodProvider.HttpMethods"></member>
    <member name="T:System.Web.Http.Controllers.IActionResultConverter">
      <summary> 可從 &lt;see cref="M:System.Web.Http.Controllers.HttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object})" /&gt; 傳回的動作取得結果的轉換常式合約，並將其轉換為 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IActionResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>將指定的 <see cref="T:System.Web.Http.Controllers.IActionResultConverter" /> 物件轉換為其他物件。</summary>
      <returns>轉換的物件。</returns>
      <param name="controllerContext">控制器內容。</param>
      <param name="actionResult">動作結果。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IActionValueBinder">
      <summary>定義與參數值相關聯的動作繫結間隔方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IActionValueBinder.GetBinding(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>取得 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> 物件。</returns>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IControllerConfiguration">
      <summary> 如果使用具有此介面的屬性來裝飾控制器，則會叫用該控制器以初始化控制器設定。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IControllerConfiguration.Initialize(System.Web.Http.Controllers.HttpControllerSettings,System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary> 叫用回呼以設定此 controllerDescriptor 的各控制器覆寫。</summary>
      <param name="controllerSettings">要初始化的控制器設定。</param>
      <param name="controllerDescriptor">控制器描述元。請注意，倘若已繼承 <see cref="T:System.Web.Http.Controllers.IControllerConfiguration" />，<see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 即可與衍生的控制器類型相關聯。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpActionInvoker">
      <summary>包含用來叫用 HTTP 作業的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionInvoker.InvokeActionAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>以非同步方式執行 HTTP 作業。</summary>
      <returns>新開始的工作。</returns>
      <param name="actionContext">執行內容。</param>
      <param name="cancellationToken">針對 HTTP 作業指派的取消權杖。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpActionSelector">
      <summary>包含選取動作方法的邏輯。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionSelector.GetActionMapping(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>傳回一個對應，該對應由動作字串輸入，是選取器可選取的所有 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" />。這主要是由 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 呼叫，以探索控制器中所有可能的動作。</summary>
      <returns>如果選取器沒有定義完成的 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 對應，則選取器可選取的 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 對應，或是 Null。</returns>
      <param name="controllerDescriptor">控制器描述元。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionSelector.SelectAction(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>選取控制器的動作。</summary>
      <returns>控制器的動作。</returns>
      <param name="controllerContext">控制器的內容。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpController">
      <summary>表示 HTTP 控制器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpController.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Threading.CancellationToken)">
      <summary>執行控制器以便同步處理。</summary>
      <returns>控制器。</returns>
      <param name="controllerContext">測試控制器的目前內容。</param>
      <param name="cancellationToken">可取消作業的通知。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ParameterBindingExtensions">
      <summary>定義 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindAsError(System.Web.Http.Controllers.HttpParameterDescriptor,System.String)">
      <summary>繫結導致錯誤的參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="message">可描述繫結失敗原因的錯誤訊息。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithAttribute(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ParameterBindingAttribute)">
      <summary>繫結參數，就如同在宣告上有指定的屬性一樣。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">要提供繫結的參數。</param>
      <param name="attribute">可描述繫結的屬性。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>藉由剖析 HTTP 主體內容來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>藉由剖析 HTTP 主體內容來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="formatters">可供選取適當格式器的格式器清單，以將參數序列化至物件。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Web.Http.Validation.IBodyModelValidator)">
      <summary>藉由剖析 HTTP 主體內容來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="formatters">可供選取適當格式器的格式器清單，以將參數序列化至物件。</param>
      <param name="bodyModelValidator">用來驗證參數的主體模型驗證程式。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Net.Http.Formatting.MediaTypeFormatter[])">
      <summary>藉由剖析 HTTP 主體內容來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="formatters">可供選取適當格式器的格式器清單，以將參數序列化至物件。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>藉由剖析查詢字串來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>藉由剖析查詢字串來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="valueProviderFactories">可提供查詢字串參數資料的值提供者 Factory。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder)">
      <summary>藉由剖析查詢字串來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="binder">用來將參數製作成物件的模型繫結器。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>藉由剖析查詢字串來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="binder">用來將參數製作成物件的模型繫結器。</param>
      <param name="valueProviderFactories">可提供查詢字串參數資料的值提供者 Factory。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ValueProviders.ValueProviderFactory[])">
      <summary>藉由剖析查詢字串來繫結參數。</summary>
      <returns>HTTP 參數繫結物件。</returns>
      <param name="parameter">可描述要繫結之參數的參數描述元。</param>
      <param name="valueProviderFactories">可提供查詢字串參數資料的值提供者 Factory。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor">
      <summary>表示反映的同步或非同步動作方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.#ctor(System.Web.Http.Controllers.HttpControllerDescriptor,System.Reflection.MethodInfo)">
      <summary>使用指定的描述元和方法詳細資料，初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor" /> 類別的新執行個體。</summary>
      <param name="controllerDescriptor">控制器描述元。</param>
      <param name="methodInfo">動作方法資訊。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ActionName">
      <summary>取得動作的名稱。</summary>
      <returns>動作的名稱。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.Equals(System.Object)"></member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
      <summary>執行所述的動作並傳回 <see cref="T:System.Threading.Tasks.Task`1" />，此工作完成後，就會包含動作的傳回值。</summary>
      <returns>一個 [T:System.Threading.Tasks.Task`1"]，一旦完成後，就會包含動作的傳回值。</returns>
      <param name="controllerContext">內容。</param>
      <param name="arguments">引數。</param>
      <param name="cancellationToken">用來取消動作的取消權杖。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetCustomAttributes``1(System.Boolean)">
      <summary>傳回針對此成員定義的自訂屬性陣列，並依型別識別。</summary>
      <returns>自訂屬性的陣列，如果自訂屬性不存在則為空陣列。</returns>
      <param name="inherit">true 表示要搜尋此動作的繼承鏈結以尋找屬性，否則為 false。</param>
      <typeparam name="T">自訂屬性的型別。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetFilters">
      <summary>擷取動作篩選條件的相關資訊。</summary>
      <returns>篩選條件資訊。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetHashCode"></member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetParameters">
      <summary>擷取動作方法的參數。</summary>
      <returns>動作方法的參數。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.MethodInfo">
      <summary>取得或設定動作方法資訊。</summary>
      <returns>動作方法資訊。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ReturnType">
      <summary>取得此方法的傳回類型。</summary>
      <returns>此方法的傳回類型。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.SupportedHttpMethods">
      <summary>取得或設定支援的 http 方法。</summary>
      <returns>支援的 http 方法。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor">
      <summary>表示反映的 HTTP 參數描述元。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.#ctor(System.Web.Http.Controllers.HttpActionDescriptor,System.Reflection.ParameterInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor" /> 類別的新執行個體。</summary>
      <param name="actionDescriptor">動作描述元。</param>
      <param name="parameterInfo">參數資訊。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.DefaultValue">
      <summary>取得參數的預設值。</summary>
      <returns>參數的預設值。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.GetCustomAttributes``1">
      <summary>擷取參數中自訂屬性的集合。</summary>
      <returns>參數中自訂屬性的集合。</returns>
      <typeparam name="TAttribute">自訂屬性的型別。</typeparam>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.IsOptional">
      <summary>取得值，這個值表示參數是否為選用。</summary>
      <returns>如果為選用參數，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterInfo">
      <summary>取得或設定參數資訊。</summary>
      <returns>參數資訊。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterName">
      <summary>取得參數的名稱。</summary>
      <returns>參數名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterType">
      <summary>取得參數的型別。</summary>
      <returns>參數的型別。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.ResponseMessageResultConverter">
      <summary>表示傳回類型為 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的動作所適用的轉換器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ResponseMessageResultConverter.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ResponseMessageResultConverter" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ResponseMessageResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>將 <see cref="T:System.Web.Http.Controllers.ResponseMessageResultConverter" /> 物件轉換為其他物件。</summary>
      <returns>轉換的物件。</returns>
      <param name="controllerContext">控制器內容。</param>
      <param name="actionResult">動作結果。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ServicesContainer">
      <summary>抽象類別，可提供 ASP.NET Web API 所用服務的容器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Add(System.Type,System.Object)">
      <summary> 將服務新增到指定服務型別的服務清單結尾。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="service">服務執行個體。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.AddRange(System.Type,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> 將指定集合的服務新增到指定服務型別的服務清單結尾。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="services">要新增的服務。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Clear(System.Type)">
      <summary> 移除指定服務型別的所有服務執行個體。</summary>
      <param name="serviceType">要從服務清單中清除的服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ClearMultiple(System.Type)">
      <summary>移除多重執行個體服務型別的所有執行個體。</summary>
      <param name="serviceType">要移除的服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ClearSingle(System.Type)">
      <summary>移除單一執行個體服務型別。</summary>
      <param name="serviceType">要移除的服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Dispose">
      <summary>執行與免費、釋放或重設 Unmanged 資源相關的應用程式定義的工作。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.FindIndex(System.Type,System.Predicate{System.Object})">
      <summary> 搜尋與指定述詞所定義的條件相符的服務，並傳回第一個相符項目的以零起始的索引。</summary>
      <returns>如果有找到，則為第一個相符項目的以零起始的索引，否則為 -1。</returns>
      <param name="serviceType">服務型別。</param>
      <param name="match">定義項目搜尋條件的委派。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetService(System.Type)">
      <summary>取得指定之型別的服務執行個體。</summary>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetServiceInstances(System.Type)">
      <summary>取得指定型別之服務執行個體的可變動清單。</summary>
      <returns>服務執行個體的可變動清單。</returns>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetServices(System.Type)">
      <summary>取得指定型別之服務執行個體的集合。</summary>
      <returns>服務執行個體的集合。</returns>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Insert(System.Type,System.Int32,System.Object)">
      <summary> 將服務插入至集合中的指定索引處。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="index">應該插入服務之以零起始的索引。如果傳遞 <see cref="F:System.Int32.MaxValue" />，請確定有將項目增加到結尾處。</param>
      <param name="service">要插入的服務。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.InsertRange(System.Type,System.Int32,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> 將集合的項目插入至服務清單中的指定索引處。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="index">應該插入新項目之以零起始的索引。如果傳遞 <see cref="F:System.Int32.MaxValue" />，請確定有將項目增加到結尾處。</param>
      <param name="services">要插入的服務集合。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.IsSingleService(System.Type)">
      <summary> 決定是否應使用 GetService 或 GetServices 擷取服務型別。</summary>
      <returns>如果服務為單數，則為 true。</returns>
      <param name="serviceType">要查詢的服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Remove(System.Type,System.Object)">
      <summary> 從指定服務型別的服務清單中，移除指定服務的第一個符合項目。</summary>
      <returns>如果成功移除項目，則為 true，否則為 false。</returns>
      <param name="serviceType">服務型別。</param>
      <param name="service">要移除的服務執行個體。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.RemoveAll(System.Type,System.Predicate{System.Object})">
      <summary> 移除與指定述詞所定義的條件相符的所有項目。</summary>
      <returns>從清單中移除的項目數。</returns>
      <param name="serviceType">服務型別。</param>
      <param name="match">定義項目移除條件的委派。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.RemoveAt(System.Type,System.Int32)">
      <summary> 移除指定之索引處的服務。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="index">要移除的服務之以零起始的索引。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Replace(System.Type,System.Object)">
      <summary>將指定服務型別的所有現有服務取代為指定的服務執行個體。此方法同時適用於單數和負數服務。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="service">服務執行個體。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceMultiple(System.Type,System.Object)">
      <summary>以新執行個體取代多重執行個體服務的所有執行個體。</summary>
      <param name="serviceType">服務的型別。</param>
      <param name="service">將取代此型別之目前服務的服務執行個體。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceRange(System.Type,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> 將指定服務型別的所有現有服務取代為指定的服務執行個體。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="services">服務執行個體。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceSingle(System.Type,System.Object)">
      <summary>取代指定之型別的單一執行個體服務。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="service">服務執行個體。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ResetCache(System.Type)">
      <summary>移除單一服務型別的快取值。</summary>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ValueResultConverter`1">
      <summary> 從傳回任意 <paramref name="T" /> 值的動作建立回應時使用的轉換器。</summary>
      <typeparam name="T">動作的宣告傳回類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.ValueResultConverter`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ValueResultConverter`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ValueResultConverter`1.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>將具有任意傳回類型 <paramref name="T" /> 的動作其結果轉換成 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的執行個體。</summary>
      <returns>新建立的 <see cref="T:System.Net.Http.HttpResponseMessage" /> 物件。</returns>
      <param name="controllerContext">動作控制器內容。</param>
      <param name="actionResult">執行結果。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.VoidResultConverter">
      <summary>表示從不傳回值的動作建立回應時使用的轉換器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.VoidResultConverter.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.VoidResultConverter" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.VoidResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>將從不傳回值的動作建立的回應進行轉換。</summary>
      <returns>轉換的回應。</returns>
      <param name="controllerContext">控制器的內容。</param>
      <param name="actionResult">動作的結果。</param>
    </member>
    <member name="T:System.Web.Http.Dependencies.IDependencyResolver">
      <summary>表示相依性插入容器。</summary>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyResolver.BeginScope">
      <summary> 啟動解析範圍。</summary>
      <returns>相依性範圍。</returns>
    </member>
    <member name="T:System.Web.Http.Dependencies.IDependencyScope">
      <summary>表示相依性範圍的介面。</summary>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyScope.GetService(System.Type)">
      <summary>擷取範圍中的服務。</summary>
      <returns>擷取的服務。</returns>
      <param name="serviceType">要擷取的服務。</param>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyScope.GetServices(System.Type)">
      <summary>擷取範圍中的服務集合。</summary>
      <returns>擷取的服務集合。</returns>
      <param name="serviceType">要擷取的服務集合。</param>
    </member>
    <member name="T:System.Web.Http.Description.ApiDescription">
      <summary> 說明 由相對 URI 路徑定義的 API 及 HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiDescription.#ctor">
      <summary> 初始化 <see cref="T:System.Web.Http.Description.ApiDescription" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ActionDescriptor">
      <summary> 取得或設定會處理 API 的動作描述元。</summary>
      <returns> 動作描述元。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.Documentation">
      <summary> 取得或設定 API 的文件。</summary>
      <returns> 文件。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.HttpMethod">
      <summary> 取得或設定 HTTP 方法。</summary>
      <returns> HTTP 方法。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ID">
      <summary>取得 ID。ID 在 <see cref="T:System.Web.Http.HttpServer" /> 中是唯一的。</summary>
      <returns>ID。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ParameterDescriptions">
      <summary> 取得參數描述元。</summary>
      <returns>參數描述。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.RelativePath">
      <summary> 取得或設定相對路徑。</summary>
      <returns> 相對路徑。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ResponseDescription">
      <summary>取得或設定回應描述。</summary>
      <returns>回應描述。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.Route">
      <summary> 取得或設定 API 的註冊路由。</summary>
      <returns> 路由。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.SupportedRequestBodyFormatters">
      <summary> 取得支援的要求主體參數。</summary>
      <returns>支援的要求主體格式器。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.SupportedResponseFormatters">
      <summary> 取得支援的回應格式器。</summary>
      <returns>支援的回應格式器。</returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiExplorer">
      <summary> 根據系統中可用的路由、控制器和動作來探索服務的 URI 空間。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> 初始化 <see cref="T:System.Web.Http.Description.ApiExplorer" /> 類別的新執行個體。</summary>
      <param name="configuration">設定。</param>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions">
      <summary> 取得 API 描述元。第一次存取時就會初始化描述。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorer.DocumentationProvider">
      <summary> 取得或設定文件提供者。提供者會負責記錄 API。</summary>
      <returns> 文件提供者。</returns>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.GetHttpMethodsSupportedByAction(System.Web.Http.Routing.IHttpRoute,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary> 取得由動作支援的 HttpMethods 集合。當初始化 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 時會呼叫。</summary>
      <returns>由動作支援的 HttpMethods 集合。</returns>
      <param name="route">路由。</param>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.ShouldExploreAction(System.String,System.Web.Http.Controllers.HttpActionDescriptor,System.Web.Http.Routing.IHttpRoute)">
      <summary> 決定是否應針對 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 產生加以考慮動作。當初始化 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 時會呼叫。</summary>
      <returns>如果應該針對 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 產生而考慮動作，則為 true，否則為 false。</returns>
      <param name="actionVariableValue">來自路由的動作變數值。</param>
      <param name="actionDescriptor">動作描述元。</param>
      <param name="route">路由。</param>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.ShouldExploreController(System.String,System.Web.Http.Controllers.HttpControllerDescriptor,System.Web.Http.Routing.IHttpRoute)">
      <summary> 決定是否應針對 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 產生加以考慮控制器。當初始化 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 時會呼叫。</summary>
      <returns>如果應該針對 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 產生而考慮控制器，則為 true，否則為 false。</returns>
      <param name="controllerVariableValue">來自路由的控制器變數值。</param>
      <param name="controllerDescriptor">控制器描述元。</param>
      <param name="route">路由。</param>
    </member>
    <member name="T:System.Web.Http.Description.ApiExplorerSettingsAttribute">
      <summary> 此屬性可用於控制器及動作，以影響 <see cref="T:System.Web.Http.Description.ApiExplorer" /> 的動作。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorerSettingsAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ApiExplorerSettingsAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorerSettingsAttribute.IgnoreApi">
      <summary> 取得或設定一個值，該值指出是否要從 <see cref="T:System.Web.Http.Description.ApiExplorer" /> 產生的 <see cref="T:System.Web.Http.Description.ApiDescription" /> 執行個體執行控制器或動作。</summary>
      <returns>如果應該忽略執行控制器或動作，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiParameterDescription">
      <summary> 說明 API 上由相對 URI 路徑定義的參數及 HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiParameterDescription.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ApiParameterDescription" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Documentation">
      <summary> 取得或設定文件。</summary>
      <returns> 文件。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Name">
      <summary> 取得或設定名稱。</summary>
      <returns> 名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.ParameterDescriptor">
      <summary> 取得或設定參數描述元。</summary>
      <returns> 參數描述元。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Source">
      <summary> 取得或設定參數來源。可能來自要求 URI、要求主體或其他地方。</summary>
      <returns> 來源。 </returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiParameterSource">
      <summary> 說明參數來自何處。</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.FromBody">
      <summary>該參數來自主體。</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.FromUri">
      <summary>該參數來自 URI。</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.Unknown">
      <summary>位置不明。</summary>
    </member>
    <member name="T:System.Web.Http.Description.IApiExplorer">
      <summary> 定義取得 <see cref="T:System.Web.Http.Description.ApiDescription" /> 集合的介面。</summary>
    </member>
    <member name="P:System.Web.Http.Description.IApiExplorer.ApiDescriptions">
      <summary> 取得 API 描述元。 </summary>
    </member>
    <member name="T:System.Web.Http.Description.IDocumentationProvider">
      <summary> 定義負責記錄服務的提供者。</summary>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary> 根據 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 取得文件。</summary>
      <returns>控制器的文件。</returns>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpControllerDescriptor)"></member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary> 根據 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 取得文件。</summary>
      <returns>控制器的文件。</returns>
      <param name="parameterDescriptor">參數描述元。</param>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetResponseDocumentation(System.Web.Http.Controllers.HttpActionDescriptor)"></member>
    <member name="T:System.Web.Http.Description.ResponseDescription">
      <summary>描述 API 回應。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ResponseDescription.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ResponseDescription" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ResponseDescription.DeclaredType">
      <summary>取得或設定宣告回應類型。</summary>
      <returns>宣告回應類型。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ResponseDescription.Documentation">
      <summary>取得或設定回應文件。</summary>
      <returns>回應文件。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ResponseDescription.ResponseType">
      <summary>取得或設定實際回應類型。</summary>
      <returns>實際回應類型。</returns>
    </member>
    <member name="T:System.Web.Http.Description.ResponseTypeAttribute">
      <summary>使用此項目來指定動作傳回的實體類型 (宣告的傳回類型為 <see cref="T:System.Net.Http.HttpResponseMessage" /> 或 <see cref="T:System.Web.Http.IHttpActionResult" />)。<see cref="P:System.Web.Http.Description.ResponseTypeAttribute.ResponseType" /> 會由 <see cref="T:System.Web.Http.Description.ApiExplorer" /> 在產生 <see cref="T:System.Web.Http.Description.ApiDescription" /> 時讀取。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ResponseTypeAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ResponseTypeAttribute" /> 類別的新執行個體。</summary>
      <param name="responseType">回應類型。</param>
    </member>
    <member name="P:System.Web.Http.Description.ResponseTypeAttribute.ResponseType">
      <summary>取得回應類型。</summary>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultAssembliesResolver">
      <summary> 提供沒有外部相依性的 <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> 實作。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultAssembliesResolver.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultAssembliesResolver" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultAssembliesResolver.GetAssemblies">
      <summary> 傳回應用程式可用的組件清單。</summary>
      <returns>組件的 &lt;see cref="T:System.Collections.ObjectModel.Collection`1" /&gt;。</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerActivator">
      <summary>表示 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 的預設實作。不同的實作都可以透過 <see cref="T:System.Web.Http.Services.DependencyResolver" /> 來註冊。我們會針對每個 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 執行個體上都有一個 <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" /> 執行個體的情況進行最佳化，但同時也可支援一個 <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" /> 有多個 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 執行個體的情況。在後者的情況中，查閱的速度可能會因為要查詢整個 <see cref="P:HttpControllerDescriptor.Properties" /> 字典而稍微變慢。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerActivator.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerActivator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerActivator.Create(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Type)">
      <summary> 使用指定的 <paramref name="request" />，來建立由 <paramref name="controllerType" /> 指定的 <see cref="T:System.Web.Http.Controllers.IHttpController" />。</summary>
      <returns>類型 <paramref name="controllerType" /> 的執行個體。</returns>
      <param name="request">要求訊息。</param>
      <param name="controllerDescriptor">控制器描述元。</param>
      <param name="controllerType">控制器的類型。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerSelector">
      <summary>表示在指定 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的情況下，用來選擇 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 的預設 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" />。不同的實作都可以透過 <see cref="P:System.Web.Http.HttpConfiguration.Services" /> 來註冊。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> 初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerSelector" /> 類別的新執行個體。</summary>
      <param name="configuration">設定。</param>
    </member>
    <member name="F:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.ControllerSuffix">
      <summary>指定控制器名稱中的後置詞字串。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.GetControllerMapping">
      <summary>傳回一個對應，該對應由控制器字串輸入，是選取器可選取的所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />。</summary>
      <returns>如果選取器沒有定義完成的 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 對應，則選取器可選取所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 對應，或是 Null。</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.GetControllerName(System.Net.Http.HttpRequestMessage)">
      <summary>取得指定的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 適用之控制器的名稱。</summary>
      <returns>指定的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 適用之控制器的名稱。</returns>
      <param name="request">HTTP 要求的訊息。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.SelectController(System.Net.Http.HttpRequestMessage)">
      <summary>為指定的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 選取 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />。</summary>
      <returns>指定之 <see cref="T:System.Net.Http.HttpRequestMessage" /> 適用的 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 執行個體。</returns>
      <param name="request">HTTP 要求的訊息。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver">
      <summary> 提供沒有外部相依性的 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> 實作。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.#ctor(System.Predicate{System.Type})">
      <summary>使用述詞來初始化新的 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver" /> 執行個體，以篩選控制器類型。</summary>
      <param name="predicate">述詞。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.GetControllerTypes(System.Web.Http.Dispatcher.IAssembliesResolver)">
      <summary> 傳回應用程式可用的控制器清單。</summary>
      <returns>控制器的 &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;。</returns>
      <param name="assembliesResolver">組件解析程式。</param>
    </member>
    <member name="P:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.IsControllerTypePredicate">
      <summary>取得值，這個值表示解析程式類型是否為控制器類型述詞。</summary>
      <returns>如果解析程式類型為控制器類型述詞，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.HttpControllerDispatcher">
      <summary>將傳入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 發送給 <see cref="T:System.Web.Http.Controllers.IHttpController" /> 實作以進行處理。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpControllerDispatcher.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>利用指定的設定，初始化 <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" /> 類別的新執行個體。</summary>
      <param name="configuration">http 設定。</param>
    </member>
    <member name="P:System.Web.Http.Dispatcher.HttpControllerDispatcher.Configuration">
      <summary>取得 HTTP 設定。</summary>
      <returns>HTTP 設定。</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpControllerDispatcher.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>發送傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 至 <see cref="T:System.Web.Http.Controllers.IHttpController" />。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />，表示持續的作業。</returns>
      <param name="request">要發送的要求</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher">
      <summary> 此類別是預設端點訊息處理常式，可檢查相符路由的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />，以及選擇要呼叫哪一個訊息處理常式。如果 <see cref="P:System.Web.Http.Routing.IHttpRoute.Handler" /> 為 null，則會委派到 <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" />。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> 使用提供的 <see cref="T:System.Web.Http.HttpConfiguration" /> 和 <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" /> 作為預設處理常式，初始化 <see cref="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher" /> 類別的新執行個體。</summary>
      <param name="configuration">伺服器組態。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.#ctor(System.Web.Http.HttpConfiguration,System.Net.Http.HttpMessageHandler)">
      <summary> 使用提供的 <see cref="T:System.Web.Http.HttpConfiguration" /> 和 <see cref="T:System.Net.Http.HttpMessageHandler" />，初始化 <see cref="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher" /> 類別的新執行個體。</summary>
      <param name="configuration">伺服器組態。</param>
      <param name="defaultHandler">當 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 沒有 <see cref="P:System.Web.Http.Routing.IHttpRoute.Handler" /> 時，所要使用的預設處理常式。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式傳送 HTTP 要求。</summary>
      <returns>代表非同步作業的工作物件。</returns>
      <param name="request">要傳送的 HTTP 要求訊息。</param>
      <param name="cancellationToken">用於取消作業的取消語彙基元。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IAssembliesResolver">
      <summary>提供用以管理應用程式組件的抽象方法。不同的實作都可以透過 <see cref="T:System.Web.Http.Services.DependencyResolver" /> 來註冊。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IAssembliesResolver.GetAssemblies">
      <summary> 傳回應用程式可用的組件清單。</summary>
      <returns>組件的 &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;。</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerActivator">
      <summary>定義 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerActivator.Create(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Type)">
      <summary>建立 <see cref="T:System.Web.Http.Controllers.IHttpController" /> 物件。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.IHttpController" /> 物件。</returns>
      <param name="request">訊息要求。</param>
      <param name="controllerDescriptor">HTTP 控制器描述元。</param>
      <param name="controllerType">控制器的型別。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerSelector">
      <summary> 定義 <see cref="T:System.Web.Http.Controllers.IHttpController" /> Factory 所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerSelector.GetControllerMapping">
      <summary>傳回一個對應，該對應由控制器字串輸入，是選取器可選取的所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />。這主要是由 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 呼叫，以探索系統中所有可能的控制器。</summary>
      <returns>如果選取器沒有定義完成的 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 對應，則選取器可選取所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 對應，或是 Null。</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerSelector.SelectController(System.Net.Http.HttpRequestMessage)">
      <summary> 為指定的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 選取 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 執行個體。</returns>
      <param name="request">要求訊息。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver">
      <summary> 提供用管理應用程式控制器類型的抽象方法。不同的實作都可以透過 DependencyResolver 來註冊。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerTypeResolver.GetControllerTypes(System.Web.Http.Dispatcher.IAssembliesResolver)">
      <summary> 傳回應用程式可用的控制器清單。</summary>
      <returns>控制器的 &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;。</returns>
      <param name="assembliesResolver">適用於失敗組件的解析程式。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks">
      <summary>提供此組件中使用的捕捉區塊。</summary>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpBatchHandler">
      <summary>取得 System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpBatchHandler.SendAsync 中的捕捉區塊。</summary>
      <returns>System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpBatchHandler.SendAsync 中的捕捉區塊。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpControllerDispatcher">
      <summary>取得 System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpControllerDispatcher.SendAsync 中的捕捉區塊。</summary>
      <returns>System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpControllerDispatcher.SendAsync 中的捕捉區塊。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpServer">
      <summary>取得 System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpServer.SendAsync 中的捕捉區塊。</summary>
      <returns>System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpServer.SendAsync 中的捕捉區塊。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.IExceptionFilter">
      <summary>使用 <see cref="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.IExceptionFilter" /> 時取得 System.Web.Http.ApiController.ExecuteAsync 中的捕捉區塊。</summary>
      <returns>使用 <see cref="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.IExceptionFilter" /> 時 System.Web.Http.ApiController.ExecuteAsync 中的捕捉區塊。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionContext">
      <summary>表示捕捉時的例外狀況以及與其相關的內容資料。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 類別的新執行個體。</summary>
      <param name="exception">捕捉例外狀況。</param>
      <param name="catchBlock">例外狀況取得的捕捉區塊。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 類別的新執行個體。</summary>
      <param name="exception">捕捉例外狀況。</param>
      <param name="catchBlock">例外狀況取得的捕捉區塊。</param>
      <param name="request">捕捉例外狀況時處理的要求。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock,System.Net.Http.HttpRequestMessage,System.Net.Http.HttpResponseMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 類別的新執行個體。</summary>
      <param name="exception">捕捉例外狀況。</param>
      <param name="catchBlock">例外狀況取得的捕捉區塊。</param>
      <param name="request">捕捉例外狀況時處理的要求。</param>
      <param name="response">捕捉例外狀況時傳回的回應。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock,System.Web.Http.Controllers.HttpActionContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 類別的新執行個體。</summary>
      <param name="exception">捕捉例外狀況。</param>
      <param name="catchBlock">例外狀況取得的捕捉區塊。</param>
      <param name="actionContext">例外狀況發生時的動作內容。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.ActionContext">
      <summary>取得例外狀況發生時的動作內容 (如果有)。</summary>
      <returns>例外狀況發生時的動作內容 (如果有)。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.CatchBlock">
      <summary>取得例外狀況取得的捕捉區塊。</summary>
      <returns>例外狀況取得的捕捉區塊。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.ControllerContext">
      <summary>取得例外狀況發生時的控制器內容 (如果有)。</summary>
      <returns>例外狀況發生時的控制器內容 (如果有)。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.Exception">
      <summary>取得捕捉例外狀況。</summary>
      <returns>捕捉例外狀況。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.Request">
      <summary>取得捕捉例外狀況時處理的要求。</summary>
      <returns>捕捉例外狀況時處理的要求。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.RequestContext">
      <summary>取得例外狀況發生時的要求內容。</summary>
      <returns>例外狀況發生時的要求內容。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.Response">
      <summary>取得捕捉例外狀況時傳送的回應。</summary>
      <returns>捕捉例外狀況時傳送的回應。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock">
      <summary>表示例外狀況內容的捕捉區塊位置。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.#ctor(System.String,System.Boolean,System.Boolean)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock" /> 類別的新執行個體。</summary>
      <param name="name">捕捉到例外狀況的捕捉區塊標籤。</param>
      <param name="isTopLevel">一個值，指出捕捉到例外狀況的捕捉區塊是否為主機前的最後一個。</param>
      <param name="callsHandler">一個值，指出捕捉區塊中的例外狀況是否可以在記錄之後才處理。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.CallsHandler">
      <summary>取得一值，指出捕捉區塊中的例外狀況是否可以在記錄之後才處理。</summary>
      <returns>一個值，指出捕捉區塊中的例外狀況是否可以在記錄之後才處理。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.IsTopLevel">
      <summary>取得一值，指出捕捉到例外狀況的捕捉區塊是否為主機前的最後一個。</summary>
      <returns>一個值，指出捕捉到例外狀況的捕捉區塊是否為主機前的最後一個。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.Name">
      <summary>取得捕捉例外狀況的捕捉區塊標籤。</summary>
      <returns>捕捉例外狀況的捕捉區塊標籤。</returns>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.ToString">
      <returns>傳回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionHandler">
      <summary>表示未處理的例外狀況處理常式。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionHandler" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.Handle(System.Web.Http.ExceptionHandling.ExceptionHandlerContext)">
      <summary>在衍生類別中覆寫時，同步處理例外狀況。</summary>
      <param name="context">例外狀況處理常式內容。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.HandleAsync(System.Web.Http.ExceptionHandling.ExceptionHandlerContext,System.Threading.CancellationToken)">
      <summary>在衍生類別中覆寫時，非同步處理例外狀況。</summary>
      <returns>表示非同步例外狀況處理作業的工作。</returns>
      <param name="context">例外狀況處理常式內容。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.ShouldHandle(System.Web.Http.ExceptionHandling.ExceptionHandlerContext)">
      <summary>決定是否應該處理例外狀況。</summary>
      <returns>如果應該處理例外狀況，則為 true，否則為 false。</returns>
      <param name="context">例外狀況處理常式內容。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.System#Web#Http#ExceptionHandling#IExceptionHandler#HandleAsync(System.Web.Http.ExceptionHandling.ExceptionHandlerContext,System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionHandlerContext">
      <summary>表示未處理例外狀況處理中的內容。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.#ctor(System.Web.Http.ExceptionHandling.ExceptionContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionHandlerContext" /> 類別的新執行個體。</summary>
      <param name="exceptionContext">例外狀況內容。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.CatchBlock">
      <summary>取得例外狀況取得的捕捉區塊。</summary>
      <returns>例外狀況取得的捕捉區塊。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.Exception">
      <summary>取得捕捉例外狀況。</summary>
      <returns>捕捉例外狀況。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.ExceptionContext">
      <summary>取得提供例外狀況和相關資料的例外狀況內容。</summary>
      <returns>提供例外狀況和相關資料的例外狀況內容。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.Request">
      <summary>取得捕捉例外狀況時處理的要求。</summary>
      <returns>捕捉例外狀況時處理的要求。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.RequestContext">
      <summary>取得例外狀況發生時的要求內容。</summary>
      <returns>例外狀況發生時的要求內容。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.Result">
      <summary>取得或設定在處理例外狀況時提供回應訊息的結果。</summary>
      <returns>在處理例外狀況時提供回應訊息的結果。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionHandlerExtensions">
      <summary>提供 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> 的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandlerExtensions.HandleAsync(System.Web.Http.ExceptionHandling.IExceptionHandler,System.Web.Http.ExceptionHandling.ExceptionContext,System.Threading.CancellationToken)">
      <summary>呼叫例外狀況處理常式並決定要處理的回應 (如果有)。</summary>
      <returns>工作完成之後，會包含處理例外狀況時要傳回的回應訊息，若例外狀況仍未處理，則為 null。</returns>
      <param name="handler">未處理的例外狀況處理常式。</param>
      <param name="context">例外狀況內容。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionLogger">
      <summary>表示未處理的例外狀況記錄器。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionLogger" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.Log(System.Web.Http.ExceptionHandling.ExceptionLoggerContext)">
      <summary>在衍生類別中覆寫時，同步記錄例外狀況。</summary>
      <param name="context">例外狀況記錄器內容。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.LogAsync(System.Web.Http.ExceptionHandling.ExceptionLoggerContext,System.Threading.CancellationToken)">
      <summary>在衍生類別中覆寫時，非同步記錄例外狀況。</summary>
      <returns>表示非同步例外狀況記錄作業的工作。</returns>
      <param name="context">例外狀況記錄器內容。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.ShouldLog(System.Web.Http.ExceptionHandling.ExceptionLoggerContext)">
      <summary>決定是否應該記錄例外狀況。</summary>
      <returns>如果應該記錄例外狀況，則為 true，否則為 false。</returns>
      <param name="context">例外狀況記錄器內容。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.System#Web#Http#ExceptionHandling#IExceptionLogger#LogAsync(System.Web.Http.ExceptionHandling.ExceptionLoggerContext,System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionLoggerContext">
      <summary>表示未處理例外狀況記錄中的內容。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.#ctor(System.Web.Http.ExceptionHandling.ExceptionContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionLoggerContext" /> 類別的新執行個體。</summary>
      <param name="exceptionContext">例外狀況內容。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.CallsHandler">
      <summary>取得或設定一值，指出例外狀況是否可以由 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> 連續地處理以產生新的回應訊息。</summary>
      <returns>一值，指出例外狀況是否可以由 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> 連續地處理以產生新的回應訊息。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.CatchBlock">
      <summary>取得例外狀況取得的捕捉區塊。</summary>
      <returns>例外狀況取得的捕捉區塊。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.Exception">
      <summary>取得捕捉例外狀況。</summary>
      <returns>捕捉例外狀況。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.ExceptionContext">
      <summary>取得提供例外狀況和相關資料的例外狀況內容。</summary>
      <returns>提供例外狀況和相關資料的例外狀況內容。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.Request">
      <summary>取得捕捉例外狀況時處理的要求。</summary>
      <returns>捕捉例外狀況時處理的要求。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.RequestContext">
      <summary>取得例外狀況發生時的要求內容。</summary>
      <returns>例外狀況發生時的要求內容。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionLoggerExtensions">
      <summary>提供 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionLogger" /> 的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLoggerExtensions.LogAsync(System.Web.Http.ExceptionHandling.IExceptionLogger,System.Web.Http.ExceptionHandling.ExceptionContext,System.Threading.CancellationToken)">
      <summary>呼叫例外狀況記錄器。</summary>
      <returns>表示非同步例外狀況記錄作業的工作。</returns>
      <param name="logger">未處理的例外狀況記錄器。</param>
      <param name="context">例外狀況內容。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionServices">
      <summary>建立從捕捉區塊呼叫記錄並進行處理的例外狀況服務。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetHandler(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得例外狀況處理常式，其會呼叫註冊的處理常式服務 (如果有)，並確定例外狀況不會意外地傳播到主機。</summary>
      <returns>例外狀況處理常式，其會呼叫註冊的處理常式，並確定例外狀況不會意外地傳播到主機。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetHandler(System.Web.Http.HttpConfiguration)">
      <summary>取得例外狀況處理常式，其會呼叫註冊的處理常式服務 (如果有)，並確定例外狀況不會意外地傳播到主機。</summary>
      <returns>例外狀況處理常式，其會呼叫註冊的處理常式，並確定例外狀況不會意外地傳播到主機。</returns>
      <param name="configuration">設定。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetLogger(System.Web.Http.Controllers.ServicesContainer)">
      <summary>取得呼叫所有註冊的記錄器服務的例外狀況記錄器。</summary>
      <returns>複合記錄器。</returns>
      <param name="services">服務容器。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetLogger(System.Web.Http.HttpConfiguration)">
      <summary>取得呼叫所有註冊的記錄器服務的例外狀況記錄器。</summary>
      <returns>複合記錄器。</returns>
      <param name="configuration">設定。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.IExceptionHandler">
      <summary>定義未處理的例外狀況處理常式。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.IExceptionHandler.HandleAsync(System.Web.Http.ExceptionHandling.ExceptionHandlerContext,System.Threading.CancellationToken)">
      <summary>處理未處理的例外狀況，可以允許傳播，或提供回應訊息以傳回來進行處理。</summary>
      <returns>表示非同步例外狀況處理作業的工作。</returns>
      <param name="context">例外狀況處理常式內容。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.IExceptionLogger">
      <summary>定義未處理的例外狀況記錄器。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.IExceptionLogger.LogAsync(System.Web.Http.ExceptionHandling.ExceptionLoggerContext,System.Threading.CancellationToken)">
      <summary>記錄未處理的例外狀況記錄器。</summary>
      <returns>表示非同步例外狀況記錄作業的工作。</returns>
      <param name="context">例外狀況記錄器內容。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ActionDescriptorFilterProvider">
      <summary>提供動作方法的相關資訊，例如其名稱、控制器、參數、屬性和篩選條件。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionDescriptorFilterProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ActionDescriptorFilterProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionDescriptorFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>傳回與此動作方法相關聯的篩選條件。</summary>
      <returns>與此動作方法相關聯的篩選條件。</returns>
      <param name="configuration">設定。</param>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ActionFilterAttribute">
      <summary>表示所有動作篩選條件屬性的基底類別。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ActionFilterAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecuted(System.Web.Http.Filters.HttpActionExecutedContext)">
      <summary>在叫用動作方法之後發生。</summary>
      <param name="actionExecutedContext">動作已執行內容。</param>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecutedAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecuting(System.Web.Http.Controllers.HttpActionContext)">
      <summary>在叫用動作方法之前發生。</summary>
      <param name="actionContext">動作內容。</param>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecutingAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.System#Web#Http#Filters#IActionFilter#ExecuteActionFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>以非同步方式執行篩選條件動作。</summary>
      <returns>針對此作業新建立的工作。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="cancellationToken">針對此工作來指派的取消權杖。</param>
      <param name="continuation">在叫用動作方法之後要繼續的委派函數。</param>
    </member>
    <member name="T:System.Web.Http.Filters.AuthorizationFilterAttribute">
      <summary>提供授權篩選條件的詳細資料。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.OnAuthorization(System.Web.Http.Controllers.HttpActionContext)">
      <summary>在處理序要求授權時呼叫。</summary>
      <param name="actionContext">動作內容，該內容封裝 <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" /> 的使用資訊。</param>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.OnAuthorizationAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.System#Web#Http#Filters#IAuthorizationFilter#ExecuteAuthorizationFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>在同步處理期間執行授權篩選條件。</summary>
      <returns>同步處理期間的授權篩選條件。</returns>
      <param name="actionContext">動作內容，該內容封裝 <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" /> 的使用資訊。</param>
      <param name="cancellationToken">可取消作業的取消權杖。</param>
      <param name="continuation">作業的接續。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ConfigurationFilterProvider">
      <summary>表示組態篩選條件提供者。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ConfigurationFilterProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ConfigurationFilterProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ConfigurationFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>傳回與此設定方法相關聯的篩選條件。</summary>
      <returns>與此設定方法相關聯的篩選條件。</returns>
      <param name="configuration">設定。</param>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ExceptionFilterAttribute">
      <summary>表示例外狀況篩選條件的屬性。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ExceptionFilterAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.OnException(System.Web.Http.Filters.HttpActionExecutedContext)">
      <summary>引發例外狀況事件。</summary>
      <param name="actionExecutedContext">動作的內容。</param>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.OnExceptionAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.System#Web#Http#Filters#IExceptionFilter#ExecuteExceptionFilterAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)">
      <summary>以非同步方式執行例外狀況篩選條件。</summary>
      <returns>執行的結果。</returns>
      <param name="actionExecutedContext">動作的內容。</param>
      <param name="cancellationToken">取消內容。</param>
    </member>
    <member name="T:System.Web.Http.Filters.FilterAttribute">
      <summary>表示動作篩選條件屬性的基底類別。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.FilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.FilterAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.FilterAttribute.AllowMultiple">
      <summary>取得可指出是否允許使用多個篩選條件的值。</summary>
      <returns>如果允許使用多個篩選條件，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.FilterInfo">
      <summary>提供可用動作篩選條件的相關資訊。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.FilterInfo.#ctor(System.Web.Http.Filters.IFilter,System.Web.Http.Filters.FilterScope)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.FilterInfo" /> 類別的新執行個體。</summary>
      <param name="instance">此類別的執行個體。</param>
      <param name="scope">此類別的範圍。</param>
    </member>
    <member name="P:System.Web.Http.Filters.FilterInfo.Instance">
      <summary>取得或設定 <see cref="T:System.Web.Http.Filters.FilterInfo" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Web.Http.Filters.FilterInfo" />。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.FilterInfo.Scope">
      <summary>取得或設定範圍 <see cref="T:System.Web.Http.Filters.FilterInfo" />。</summary>
      <returns>FilterInfo 的範圍。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.FilterScope">
      <summary>定義用來指定在相同的篩選類型和篩選順序中篩選執行順序的值。</summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Action">
      <summary>指定在 Controller 之後的順序。 </summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Controller">
      <summary>指定在 Action 之前且在 Global 之後的動作。</summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Global">
      <summary>指定在 Controller 之前的動作。</summary>
    </member>
    <member name="T:System.Web.Http.Filters.HttpActionExecutedContext">
      <summary>表示 HTTP 已執行內容的動作。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpActionExecutedContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpActionExecutedContext" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpActionExecutedContext.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Exception)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpActionExecutedContext" /> 類別的新執行個體。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="exception">例外狀況。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.ActionContext">
      <summary>取得或設定 HTTP 動作內容。</summary>
      <returns>HTTP 動作內容。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Exception">
      <summary>取得或設定在執行期間引發的例外狀況。</summary>
      <returns>在執行期間引發的例外狀況。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Request">
      <summary>取得內容的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 物件。</summary>
      <returns>內容的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 物件。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Response">
      <summary>取得或設定內容的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>內容的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.HttpAuthenticationChallengeContext">
      <summary>代表包含執行驗證挑戰資訊的驗證挑戰內容。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpAuthenticationChallengeContext.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.IHttpActionResult)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpAuthenticationChallengeContext" /> 類別的新執行個體。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="result">目前動作結果。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationChallengeContext.ActionContext">
      <summary>取得動作內容。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationChallengeContext.Request">
      <summary>取得要求訊息。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationChallengeContext.Result">
      <summary>取得或設定要執行的動作結果。</summary>
    </member>
    <member name="T:System.Web.Http.Filters.HttpAuthenticationContext">
      <summary>代表包含執行驗證資訊的驗證內容。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpAuthenticationContext.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Security.Principal.IPrincipal)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpAuthenticationContext" /> 類別的新執行個體。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="principal">目前的主體。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.ActionContext">
      <summary>取得動作內容。</summary>
      <returns>動作內容。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.ErrorResult">
      <summary>取得或設定動作結果，其會產生錯誤回應 (若驗證失敗，否則為 null)。</summary>
      <returns>會產生錯誤回應的動作結果。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.Principal">
      <summary>取得或設定驗證的主體。</summary>
      <returns>驗證的主體。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.Request">
      <summary>取得要求訊息。</summary>
      <returns>要求訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.HttpFilterCollection">
      <summary>表示 HTTP 篩選條件的集合。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpFilterCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Add(System.Web.Http.Filters.IFilter)">
      <summary>將項目加入至集合的尾端。</summary>
      <param name="filter">要加入集合中的項目。</param>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.AddRange(System.Collections.Generic.IEnumerable{System.Web.Http.Filters.IFilter})"></member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Clear">
      <summary>移除集合中的所有項目。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Contains(System.Web.Http.Filters.IFilter)">
      <summary>判斷集合是否包含指定的項目。</summary>
      <returns>如果集合包含指定的項目，為 true，否則為 false。</returns>
      <param name="filter">要檢查的項目。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpFilterCollection.Count">
      <summary>取得集合中的元素數目。</summary>
      <returns>集合中的元素數目。</returns>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.GetEnumerator">
      <summary>取得會在集合中逐一查看的列舉值。</summary>
      <returns>可用來在集合中逐一查看的列舉值物件。</returns>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Remove(System.Web.Http.Filters.IFilter)">
      <summary>從集合移除指定的項目。</summary>
      <param name="filter">要在集合中移除的項目。</param>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>取得會在集合中逐一查看的列舉值。</summary>
      <returns>可用來在集合中逐一查看的列舉值物件。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.IActionFilter">
      <summary>定義動作篩選中使用的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IActionFilter.ExecuteActionFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>以非同步方式執行篩選條件動作。</summary>
      <returns>針對此作業新建立的工作。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="cancellationToken">針對此工作來指派的取消權杖。</param>
      <param name="continuation">在叫用動作方法之後要繼續的委派函數。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IAuthenticationFilter">
      <summary>定義執行驗證的篩選。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IAuthenticationFilter.AuthenticateAsync(System.Web.Http.Filters.HttpAuthenticationContext,System.Threading.CancellationToken)">
      <summary>驗證要求。</summary>
      <returns>會執行寫入的 Task。</returns>
      <param name="context">驗證內容。</param>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="M:System.Web.Http.Filters.IAuthenticationFilter.ChallengeAsync(System.Web.Http.Filters.HttpAuthenticationChallengeContext,System.Threading.CancellationToken)"></member>
    <member name="T:System.Web.Http.Filters.IAuthorizationFilter">
      <summary>定義授權篩選所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IAuthorizationFilter.ExecuteAuthorizationFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>執行要同步化的授權篩選條件。</summary>
      <returns>要同步化的授權篩選條件。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="cancellationToken">與篩選條件相關聯的取消權杖。</param>
      <param name="continuation">接續。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IExceptionFilter">
      <summary>定義例外狀況篩選條件所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IExceptionFilter.ExecuteExceptionFilterAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)">
      <summary>執行非同步例外狀況篩選條件。</summary>
      <returns>非同步例外狀況篩選條件。</returns>
      <param name="actionExecutedContext">動作已執行內容。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IFilter">
      <summary>定義篩選中使用的方法。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.IFilter.AllowMultiple">
      <summary>取得或設定值，這個值表示是否可以為單一程式元素指定所指出屬性的多個執行個體。</summary>
      <returns>如果可以指定多個執行個體，為 true，否則為 false。預設值為 false。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.IFilterProvider">
      <summary>提供篩選條件資訊。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>傳回篩選條件的列舉。</summary>
      <returns>篩選條件的列舉。</returns>
      <param name="configuration">HTTP 設定。</param>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IOverrideFilter"></member>
    <member name="P:System.Web.Http.Filters.IOverrideFilter.FiltersToOverride"></member>
    <member name="T:System.Web.Http.Hosting.HttpPropertyKeys">
      <summary> 針對儲存於 <see cref="P:System.Net.Http.HttpRequestMessage.Properties" /> 中的屬性提供一般索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.ClientCertificateKey">
      <summary>針對此要求的用戶端憑證提供索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.DependencyScope">
      <summary>針對與此要求相關聯的 <see cref="T:System.Web.Http.Dependencies.IDependencyScope" /> 提供索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.DisposableRequestResourcesKey">
      <summary>針對當處置要求時應該處置的資源集合提供索引鍵，</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpActionDescriptorKey">
      <summary> 針對與此要求相關聯的 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 提供索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpConfigurationKey">
      <summary>針對與此要求相關聯的 <see cref="T:System.Web.Http.HttpConfiguration" /> 提供索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpRouteDataKey">
      <summary>針對與此要求相關聯的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" /> 提供索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IncludeErrorDetailKey">
      <summary>提供一個索引鍵，指出錯誤詳細資料是否要包括在此 HTTP 要求的回應中。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IsBatchRequest">
      <summary> 提供索引鍵，指出要求是否為批次要求。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IsLocalKey">
      <summary>提供索引鍵，這個索引鍵表示要求是否源自本機位址。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.NoRouteMatched">
      <summary> 提供索引鍵，指出要求是否無法符合路徑。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestContextKey">
      <summary>針對此要求的 <see cref="T:System.Web.Http.Controllers.HttpRequestContext" /> 提供索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestCorrelationKey">
      <summary>針對目前儲存於 <see cref="T:System.Net.Http.Properties" /> 中的 <see cref="T:System.Guid" /> 提供索引鍵。這是該要求的相互關聯 ID。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestQueryNameValuePairsKey">
      <summary>針對目前儲存於 <see cref="T:System.Net.Http.Properties" /> 中的已剖析查詢字串提供索引鍵。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RetrieveClientCertificateDelegateKey">
      <summary>提供委派的索引鍵，該委派可以擷取此要求的用戶端憑證。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.SynchronizationContextKey">
      <summary>針對目前儲存於 Properties() 中的 <see cref="T:System.Threading.SynchronizationContext" /> 提供索引鍵。如果 Current() 是 null，則不會儲存任何內容。</summary>
    </member>
    <member name="T:System.Web.Http.Hosting.IHostBufferPolicySelector">
      <summary> 用來控制是否在主機中使用緩衝要求和回應的介面。如果主機提供緩衝要求和/或回應的支援，則可以使用此介面來判斷何時使用緩衝的原則。</summary>
    </member>
    <member name="M:System.Web.Http.Hosting.IHostBufferPolicySelector.UseBufferedInputStream(System.Object)">
      <summary>判斷主機是否應緩衝 <see cref="T:System.Net.Http.HttpRequestMessage" /> 實體內容。</summary>
      <returns>如果應使用緩衝，則為 true，否則應使用串流的要求。</returns>
      <param name="hostContext">主機內容。</param>
    </member>
    <member name="M:System.Web.Http.Hosting.IHostBufferPolicySelector.UseBufferedOutputStream(System.Net.Http.HttpResponseMessage)">
      <summary>判斷主機是否應緩衝 <see cref="T.System.Net.Http.HttpResponseMessage" /> 實體內容。</summary>
      <returns>如果應使用緩衝，則為 true，否則應使用串流的回應。</returns>
      <param name="response">HTTP 回應訊息。</param>
    </member>
    <member name="T:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler">
      <summary>表示隱藏主機驗證結果的訊息處理常式。</summary>
    </member>
    <member name="M:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>非同步傳送要求訊息。</summary>
      <returns>完成非同步作業的工作。</returns>
      <param name="request">要傳送的要求訊息。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.ModelMetadata">
      <summary>表示 ModelMetadata 的中繼資料類型。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.#ctor(System.Web.Http.Metadata.ModelMetadataProvider,System.Type,System.Func{System.Object},System.Type,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> 類別的新執行個體。</summary>
      <param name="provider">提供者。</param>
      <param name="containerType">容器的型別。</param>
      <param name="modelAccessor">模型存取子。</param>
      <param name="modelType">模型的型別。</param>
      <param name="propertyName">屬性的名稱。</param>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.AdditionalValues">
      <summary>取得字典，其中包含模型的其他中繼資料。</summary>
      <returns>字典，包含模型的其他中繼資料。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ContainerType">
      <summary>取得或設定模型的容器類型。</summary>
      <returns>模型的容器類型。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ConvertEmptyStringToNull">
      <summary>取得或設定值，這個值表示是否應該將表單中回傳的空字串轉換成 null。</summary>
      <returns>如果表單中回傳的空字串應該轉換成 null，為 true，否則為 false。預設值為 true。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Description">
      <summary>取得或設定模型的描述。</summary>
      <returns>模型的描述。預設值為 null。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.GetDisplayName">
      <summary>取得模型的顯示名稱。</summary>
      <returns>模型的顯示名稱。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.GetValidators(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>取得模型的驗證程式清單。</summary>
      <returns>模型的驗證程式清單。</returns>
      <param name="validatorProviders">模型的驗證程式提供者。</param>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsComplexType">
      <summary>取得或設定值，這個值表示模型是否為複雜類型。</summary>
      <returns>表示是否將模型視為唯讀的值。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsNullableValueType">
      <summary>取得值，這個值表示類型是否可為 Null。</summary>
      <returns>如果類型可為 Null，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsReadOnly">
      <summary>取得或設定值，這個值表示模型是否為唯讀。</summary>
      <returns>如果模型是唯讀的，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Model">
      <summary>取得模型的值。</summary>
      <returns>模型值可以是 null。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ModelType">
      <summary>取得模型的型別。</summary>
      <returns>模型的型別。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Properties">
      <summary>取得模型中繼資料物件的集合，這類物件描述模型的屬性。</summary>
      <returns>描述模型屬性之模型中繼資料物件的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.PropertyName">
      <summary>取得屬性名稱。</summary>
      <returns>屬性名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Provider">
      <summary>取得或設定提供者。</summary>
      <returns>提供者。</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.ModelMetadataProvider">
      <summary>提供自訂中繼資料提供者的抽象基底類別。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForProperties(System.Object,System.Type)">
      <summary>取得模型中每一個屬性的 ModelMetadata 物件。</summary>
      <returns>模型中每一個屬性的 ModelMetadata 物件。</returns>
      <param name="container">容器。</param>
      <param name="containerType">容器的型別。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForProperty(System.Func{System.Object},System.Type,System.String)">
      <summary>取得指定之屬性的中繼資料。</summary>
      <returns>指定之屬性的中繼資料模型。</returns>
      <param name="modelAccessor">模型存取子。</param>
      <param name="containerType">容器的型別。</param>
      <param name="propertyName">要取得中繼資料模型的屬性。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForType(System.Func{System.Object},System.Type)">
      <summary>取得指定之模型存取子的中繼資料和模型類型。</summary>
      <returns>中繼資料。</returns>
      <param name="modelAccessor">模型存取子。</param>
      <param name="modelType">模式的類型。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1">
      <summary>提供抽象類別來實作中繼資料提供者。</summary>
      <typeparam name="TModelMetadata">模型中繼資料的型別。</typeparam>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.CreateMetadataFromPrototype(`0,System.Func{System.Object})">
      <summary>在衍生類別中覆寫時，使用指定的原型建立屬性的模型中繼資料。</summary>
      <returns>屬性的模型中繼資料。</returns>
      <param name="prototype">用以建立模型中繼資料的原型。</param>
      <param name="modelAccessor">模型存取子。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>在衍生類別中覆寫時，建立屬性的模型中繼資料。</summary>
      <returns>屬性的模型中繼資料。</returns>
      <param name="attributes">屬性集。</param>
      <param name="containerType">容器的型別。</param>
      <param name="modelType">模型的型別。</param>
      <param name="propertyName">屬性的名稱。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForProperties(System.Object,System.Type)">
      <summary>擷取模型的屬性清單。</summary>
      <returns>模型的屬性清單。</returns>
      <param name="container">模型容器。</param>
      <param name="containerType">容器的型別。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForProperty(System.Func{System.Object},System.Type,System.String)">
      <summary>使用容器型別和屬性名稱，擷取指定之屬性的中繼資料。</summary>
      <returns>指定之屬性的中繼資料。</returns>
      <param name="modelAccessor">模型存取子。</param>
      <param name="containerType">容器的型別。</param>
      <param name="propertyName">屬性的名稱。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForType(System.Func{System.Object},System.Type)">
      <summary>使用模型型別，傳回指定之屬性的中繼資料。</summary>
      <returns>指定之屬性的中繼資料。</returns>
      <param name="modelAccessor">模型存取子。</param>
      <param name="modelType">容器的型別。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes">
      <summary>提供 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 的原型快取資料。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.#ctor(System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes" /> 類別的新執行個體。</summary>
      <param name="attributes">用來為初始化作業提供資料的屬性。</param>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.Display">
      <summary>取得或設定中繼資料顯示屬性。</summary>
      <returns>中繼資料顯示屬性。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.DisplayFormat">
      <summary>取得或設定中繼資料顯示格式屬性。</summary>
      <returns>中繼資料顯示格式屬性。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.DisplayName"></member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.Editable">
      <summary>取得或設定中繼資料可編輯屬性。</summary>
      <returns>中繼資料可編輯屬性。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.ReadOnly">
      <summary>取得或設定中繼資料唯讀屬性。</summary>
      <returns>中繼資料唯讀屬性。</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata">
      <summary>針對資料模型的通用中繼資料和 <see cref="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider" /> 類別，提供容器。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.#ctor(System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata,System.Func{System.Object})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata" /> 類別的新執行個體。</summary>
      <param name="prototype">要用來初始化模型中繼資料的原型。</param>
      <param name="modelAccessor">模型存取子。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.#ctor(System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider,System.Type,System.Type,System.String,System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata" /> 類別的新執行個體。</summary>
      <param name="provider">中繼資料提供者。</param>
      <param name="containerType">容器的類型。</param>
      <param name="modelType">模型的類型。</param>
      <param name="propertyName">屬性的名稱。</param>
      <param name="attributes">用來為初始化作業提供資料的屬性。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeConvertEmptyStringToNull">
      <summary>擷取值，這個值表示是否應該將表單中回傳的空字串轉換成 null。</summary>
      <returns>如果表單中回傳的空字串應該轉換成 null，為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeDescription">
      <summary>擷取模型的描述。</summary>
      <returns>模型的描述。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeIsReadOnly">
      <summary>擷取值，這個值表示模型是否為唯讀。</summary>
      <returns>如果模型是唯讀的，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.GetDisplayName"></member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1">
      <summary>提供 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 的原型快取資料。</summary>
      <typeparam name="TPrototypeCache">原型快取的類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.#ctor(System.Web.Http.Metadata.Providers.CachedModelMetadata{`0},System.Func{System.Object})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 類別的新執行個體。</summary>
      <param name="prototype">原型。</param>
      <param name="modelAccessor">模型存取子。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.#ctor(System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider,System.Type,System.Type,System.String,`0)">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 類別的新執行個體。</summary>
      <param name="provider">提供者。</param>
      <param name="containerType">容器的類型。</param>
      <param name="modelType">模型的型別。</param>
      <param name="propertyName">屬性的名稱。</param>
      <param name="prototypeCache">原型快取。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeConvertEmptyStringToNull">
      <summary>表示是否應該計算表單中回傳的空字串並轉換成 null。</summary>
      <returns>如果應該計算表單中回傳的空字串並轉換成 null，為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeDescription">
      <summary>表示計算值。</summary>
      <returns>計算值。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeIsComplexType">
      <summary>取得值，這個值表示模型是否為複雜類型。</summary>
      <returns>值，表示 Web API Framework 是否將模型視為複雜型別。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeIsReadOnly">
      <summary>取得值，這個值表示要計算的模型是否為唯讀。</summary>
      <returns>如果要計算的模型是唯讀，為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ConvertEmptyStringToNull">
      <summary>取得或設定值，這個值表示是否應該將表單中回傳的空字串轉換成 null。</summary>
      <returns>如果表單中回傳的空字串應該轉換成 null，為 true，否則為 false。預設值為 true。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.Description">
      <summary>取得或設定模型的描述。</summary>
      <returns>模型的描述。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.IsComplexType">
      <summary>取得值，這個值表示模型是否為複雜類型。</summary>
      <returns>值，表示 Web API Framework 是否將模型視為複雜型別。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.IsReadOnly">
      <summary>取得或設定值，這個值表示模型是否為唯讀。</summary>
      <returns>如果模型是唯讀的，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.PrototypeCache">
      <summary>取得或設定值，這個值表示原型快取是否正在更新。</summary>
      <returns>如果原型快取正在更新，為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider">
      <summary>實作預設模型中繼資料提供者。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.CreateMetadataFromPrototype(System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata,System.Func{System.Object})">
      <summary>使用指定屬性的原型建立中繼資料。</summary>
      <returns>屬性的中繼資料。</returns>
      <param name="prototype">原型。</param>
      <param name="modelAccessor">模型存取子。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>建立指定屬性的中繼資料。</summary>
      <returns>屬性的中繼資料。</returns>
      <param name="attributes">屬性。</param>
      <param name="containerType">容器的型別。</param>
      <param name="modelType">模型的型別。</param>
      <param name="propertyName">屬性的名稱。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider">
      <summary>表示空的模型中繼資料提供者。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.CreateMetadataFromPrototype(System.Web.Http.Metadata.ModelMetadata,System.Func{System.Object})">
      <summary>從原型建立中繼資料。</summary>
      <returns>中繼資料。</returns>
      <param name="prototype">模型中繼資料原型。</param>
      <param name="modelAccessor">模型存取子。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>建立 <see cref="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider" /> 之中繼資料提供者的原型。</summary>
      <returns>中繼資料提供者的原型。</returns>
      <param name="attributes">屬性。</param>
      <param name="containerType">容器的類型。</param>
      <param name="modelType">模型的型別。</param>
      <param name="propertyName">屬性的名稱。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.CancellationTokenParameterBinding">
      <summary>代表直接對取消權杖的繫結。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CancellationTokenParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.CancellationTokenParameterBinding" /> 類別的新執行個體。</summary>
      <param name="descriptor">繫結描述元。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CancellationTokenParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>在同步處理期間執行繫結。</summary>
      <returns>同步處理期間的繫結。</returns>
      <param name="metadataProvider">中繼資料提供者。</param>
      <param name="actionContext">動作內容。</param>
      <param name="cancellationToken">作業取消後的通知。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.CustomModelBinderAttribute">
      <summary>表示叫用自訂模型繫結器的屬性。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CustomModelBinderAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.CustomModelBinderAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CustomModelBinderAttribute.GetBinder">
      <summary>擷取相關聯的模型繫結器。</summary>
      <returns>實作 <see cref="T:System.Web.Http.ModelBinding.IModelBinder" /> 介面的物件參考。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.DefaultActionValueBinder">
      <summary>表示繫結器的預設動作值。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.GetBinding(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>
        <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> 介面的預設實作。此介面是主要進入點，可供繫結動作參數。</summary>
      <returns>與 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 相關聯的 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" />。</returns>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.GetParameterBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>取得與 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 相關聯的 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />。</summary>
      <returns>與 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 相關聯的 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />。</returns>
      <param name="parameter">參數描述元。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ErrorParameterBinding">
      <summary>定義繫結錯誤。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ErrorParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ErrorParameterBinding" /> 類別的新執行個體。</summary>
      <param name="descriptor">錯誤描述元。</param>
      <param name="message">訊息。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ErrorParameterBinding.ErrorMessage">
      <summary>取得錯誤訊息。</summary>
      <returns>錯誤訊息。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ErrorParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>在同步處理期間執行繫結方法。</summary>
      <param name="metadataProvider">中繼資料提供者。</param>
      <param name="actionContext">動作內容。</param>
      <param name="cancellationToken">取消權杖值。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.FormatterParameterBinding">
      <summary>表示將從主體讀取並叫用格式器的參數繫結。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Web.Http.Validation.IBodyModelValidator)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 類別的新執行個體。</summary>
      <param name="descriptor">描述元。</param>
      <param name="formatters">格式器。</param>
      <param name="bodyModelValidator">主體模型驗證程式。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.BodyModelValidator">
      <summary>取得或設定主體模型驗證程式的介面。</summary>
      <returns>主體模型驗證程式的介面。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.ErrorMessage">
      <summary>取得錯誤訊息。</summary>
      <returns>錯誤訊息。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>以非同步方式執行 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 的繫結。</summary>
      <returns>動作的結果。</returns>
      <param name="metadataProvider">中繼資料提供者。</param>
      <param name="actionContext">與動作相關聯的內容。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.Formatters">
      <summary>取得或設定可列舉的物件，此物件表示參數繫結的格式器。</summary>
      <returns>可列舉的物件，此物件表示參數繫結的格式器。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ReadContentAsync(System.Net.Http.HttpRequestMessage,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger)">
      <summary>以非同步方式讀取 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 的內容。</summary>
      <returns>動作的結果。</returns>
      <param name="request">要求。</param>
      <param name="type">型別。</param>
      <param name="formatters">格式器。</param>
      <param name="formatterLogger">格式記錄工具。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ReadContentAsync(System.Net.Http.HttpRequestMessage,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger,System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.WillReadBody">
      <summary>取得 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 是否會讀取主體。</summary>
      <returns>如果 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 會讀取主體，則為 True，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.FormDataCollectionExtensions">
      <summary>表示表單資料集合的擴充程式。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection)">
      <summary>讀取具有指定型別的集合擴充程式。</summary>
      <returns>讀取集合擴充程式。</returns>
      <param name="formData">表單資料。</param>
      <typeparam name="T">一般類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>讀取具有指定型別的集合擴充程式。</summary>
      <returns>集合擴充程式。</returns>
      <param name="formData">表單資料。</param>
      <param name="modelName">模型的名稱。</param>
      <param name="requiredMemberSelector">必要的成員選取器。</param>
      <param name="formatterLogger">格式器記錄工具。</param>
      <typeparam name="T">一般類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection,System.String,System.Web.Http.Controllers.HttpActionContext)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type)">
      <summary>讀取具有指定型別的集合擴充程式。</summary>
      <returns>具有指定型別的集合擴充程式。</returns>
      <param name="formData">表單資料。</param>
      <param name="type">物件的類型。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>讀取具有指定型別和模型名稱的集合擴充程式。</summary>
      <returns>集合擴充程式。</returns>
      <param name="formData">表單資料。</param>
      <param name="type">物件的類型。</param>
      <param name="modelName">模型的名稱。</param>
      <param name="requiredMemberSelector">必要的成員選取器。</param>
      <param name="formatterLogger">格式器記錄工具。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger,System.Web.Http.HttpConfiguration)">
      <summary>使用模型繫結將表單資料還原序列化為給定的類型。</summary>
      <returns>繫結物件的最佳嘗試。最佳嘗試可能是 null。</returns>
      <param name="formData">含有剖析表單 URL 資料的集合</param>
      <param name="type">要讀取的目標類型</param>
      <param name="modelName">要將整個表單讀取為單一物件的 null 或空白。這常見於主體資料。或針對表單資料執行部分繫結的模型名稱。這在擷取個別欄位時很常見。</param>
      <param name="requiredMemberSelector">用來決定必要成員的 <see cref="T:System.Net.Http.Formatting.IRequiredMemberSelector" />。</param>
      <param name="formatterLogger">記錄事件的 <see cref="T:System.Net.Http.Formatting.IFormatterLogger" />。</param>
      <param name="config">要從中挑選繫結器的 <see cref="T:System.Web.Http.HttpConfiguration" /> 組態。如果尚未建立組態，則可為 null。在該情況下，即會建立新的組態。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.String,System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection,System.Web.Http.Controllers.HttpActionContext)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpBindingBehavior">
      <summary>列舉 HTTP 繫結的行為。</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Never">
      <summary>絕不使用 HTTP 繫結。</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Optional">
      <summary>選擇性的繫結行為</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Required">
      <summary>需要 HTTP 繫結。</summary>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute">
      <summary>提供模型繫結行為屬性的基底類別。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.#ctor(System.Web.Http.ModelBinding.HttpBindingBehavior)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute" /> 類別的新執行個體。</summary>
      <param name="behavior">行為。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.Behavior">
      <summary>取得或設定行為分類。</summary>
      <returns>行為分類。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.TypeId">
      <summary>取得此屬性的唯一識別項。</summary>
      <returns>此屬性的識別項。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpRequestParameterBinding">
      <summary>與要求繫結的參數。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpRequestParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.HttpRequestParameterBinding" /> 類別的新執行個體。</summary>
      <param name="descriptor">參數描述元。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpRequestParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>以非同步方式執行參數繫結。</summary>
      <returns>繫結的參數。</returns>
      <param name="metadataProvider">中繼資料提供者。</param>
      <param name="actionContext">動作內容。</param>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.IModelBinder">
      <summary>定義模型繫結器所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.IModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的控制器內容和繫結內容，將模型繫結至值。</summary>
      <returns>如果模型繫結成功，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.IValueProviderParameterBinding">
      <summary>表示參數繫結的值提供者。</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.IValueProviderParameterBinding.ValueProviderFactories">
      <summary>取得此參數繫結使用的 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 執行個體。</summary>
      <returns>此參數繫結所使用的 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 執行個體。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter">
      <summary>表示用來處理 HTML 表單 URL 結尾資料的 <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 類別，也稱為 application/x-www-form-urlencoded。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.#ctor(System.Web.Http.HttpConfiguration)"></member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.CanReadType(System.Type)">
      <summary> 判斷這個 <see cref="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter" /> 是否可讀取指定 <paramref name="type" /> 的物件。</summary>
      <returns>如果可以讀取此類型的物件，則為 true，否則為 false。</returns>
      <param name="type">會讀取的物件類型。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>從指定的資料流讀取指定 <paramref name="type" /> 的物件。在還原序列化期間會呼叫此方法。</summary>
      <returns>一個 <see cref="T:System.Threading.Tasks.Task" />，其結果將是已經讀取的物件執行個體。</returns>
      <param name="type">要讀取的物件類型。</param>
      <param name="readStream">要從其中讀取的 <see cref="T:System.IO.Stream" />。</param>
      <param name="content">正在讀取的內容。</param>
      <param name="formatterLogger">記錄事件的 <see cref="T:System.Net.Http.Formatting.IFormatterLogger" />。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderAttribute">
      <summary>使用模型繫結器來指定參數。如此可選擇性地指定特定模型繫結器及驅動該模型繫結器的值提供者。衍生的屬性可能提供模型繫結器或值提供者的便利設定。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderAttribute" /> 類別的新執行個體。</summary>
      <param name="binderType">模型繫結器的類型。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.BinderType">
      <summary>取得或設定模型繫結器的類型。</summary>
      <returns>模型繫結器的類型。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>取得參數的繫結。</summary>
      <returns>包含繫結的 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />。</returns>
      <param name="parameter">要繫結的參數。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetModelBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary> 取得此類型的 IModelBinder。</summary>
      <returns> 非 Null 的模型繫結器。</returns>
      <param name="configuration">設定。</param>
      <param name="modelType">繫結器所需繫結的模型類型。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetModelBinderProvider(System.Web.Http.HttpConfiguration)">
      <summary>取得模型繫結器提供者。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 執行個體。</returns>
      <param name="configuration">
        <see cref="T:System.Web.Http.HttpConfiguration" /> 組態物件。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary> 取得將送至模型繫結器的值提供者。</summary>
      <returns>
        <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 執行個體的集合。</returns>
      <param name="configuration">
        <see cref="T:System.Web.Http.HttpConfiguration" /> 組態物件。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.Name">
      <summary>取得或設定在模型繫結期間視為參數名稱的名稱。</summary>
      <returns>要考量的參數名稱。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.SuppressPrefixCheck">
      <summary>取得或設定值，這個值指定是否應該隱藏前置詞檢查。</summary>
      <returns>如果應該隱藏前置詞檢查，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderConfig">
      <summary>提供模型繫結器組態的容器。</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.ResourceClassKey">
      <summary>取得或設定包含當地語系化字串值的資源檔名稱 (類別機碼)。</summary>
      <returns>資源檔的名稱 (類別機碼)。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.TypeConversionErrorMessageProvider">
      <summary>取得或設定類型轉換錯誤訊息的目前提供者。</summary>
      <returns>類型轉換錯誤訊息的目前提供者。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.ValueRequiredErrorMessageProvider">
      <summary>取得或設定需要值之錯誤訊息的目前提供者。</summary>
      <returns>錯誤訊息提供者。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderErrorMessageProvider">
      <summary>提供模型繫結器錯誤訊息提供者的容器。</summary>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderParameterBinding">
      <summary> 描述透過 ModelBinding 繫結的參數。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderParameterBinding" /> 類別的新執行個體。</summary>
      <param name="descriptor">參數描述元。</param>
      <param name="modelBinder">模型繫結器。</param>
      <param name="valueProviderFactories">值提供者 Factory 的集合。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderParameterBinding.Binder">
      <summary>取得模型繫結器。</summary>
      <returns>模型繫結器。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>透過模型繫結器，以非同步方式執行參數繫結。</summary>
      <returns>在繫結完成時發出信號的工作。</returns>
      <param name="metadataProvider">用於驗證的中繼資料提供者。</param>
      <param name="actionContext">繫結的動作內容。</param>
      <param name="cancellationToken">針對此工作指派的取消權杖，以便取消繫結作業。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderParameterBinding.ValueProviderFactories">
      <summary>取得值提供者 Factory 的集合。</summary>
      <returns>值提供者 Factory 的集合。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderProvider">
      <summary>提供模型繫結器提供者的抽象基底類別。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>尋找指定型別的繫結器。</summary>
      <returns>繫結器，可嘗試繫結此型別。如果繫結器以靜態方式知道其永遠無法繫結該型別，則為 null。</returns>
      <param name="configuration">組態物件。</param>
      <param name="modelType">繫結時所要根據的模型型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBindingContext">
      <summary>提供模型繫結器運作所在的內容。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBindingContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBindingContext" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBindingContext.#ctor(System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBindingContext" /> 類別的新執行個體。</summary>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.FallbackToEmptyPrefix">
      <summary>取得或設定值，這個值表示繫結器是否應該使用空白前置字元。</summary>
      <returns>如果繫結器應該使用空白前置字元，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.Model">
      <summary>取得或設定模型。</summary>
      <returns>模型。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelMetadata">
      <summary>取得或設定模型中繼資料。</summary>
      <returns>模型中繼資料。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelName">
      <summary>取得或設定模型的名稱。</summary>
      <returns>模型的名稱。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelState">
      <summary>取得或設定模型的狀態。</summary>
      <returns>模型的狀態。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelType">
      <summary>取得或設定模型的型別。</summary>
      <returns>模型的型別。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.PropertyMetadata">
      <summary>取得屬性中繼資料。</summary>
      <returns>屬性中繼資料。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ValidationNode">
      <summary>取得或設定驗證節點。</summary>
      <returns>驗證節點。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ValueProvider">
      <summary>取得或設定值提供者。</summary>
      <returns>值提供者。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelError">
      <summary>表示在模型繫結期間所發生的錯誤。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.Exception)">
      <summary>使用指定的例外狀況，初始化 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 類別的新執行個體。</summary>
      <param name="exception">例外狀況。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.Exception,System.String)">
      <summary>使用指定的例外狀況和錯誤訊息，初始化 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 類別的新執行個體。</summary>
      <param name="exception">例外狀況。</param>
      <param name="errorMessage">錯誤訊息</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.String)">
      <summary>使用指定的錯誤訊息，初始化 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 類別的新執行個體。</summary>
      <param name="errorMessage">錯誤訊息</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelError.ErrorMessage">
      <summary>取得或設定錯誤訊息。</summary>
      <returns>錯誤訊息。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelError.Exception">
      <summary>取得或設定例外狀況物件。</summary>
      <returns>例外狀況物件。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelErrorCollection">
      <summary>表示 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 執行個體的集合。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelErrorCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.Add(System.Exception)">
      <summary>將指定的 Exception 物件加入至模型錯誤集合。</summary>
      <param name="exception">例外狀況。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.Add(System.String)">
      <summary>將指定的錯誤訊息加入至模型錯誤集合。</summary>
      <param name="errorMessage">錯誤訊息。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelState">
      <summary>封裝繫結至動作方法引數之屬性或繫結至引數本身的模型繫結狀態。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelState.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelState" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelState.Errors">
      <summary>取得 <see cref="T:System.Web.Http.ModelBinding.ModelErrorCollection" /> 物件，其中包含模型繫結期間發生的任何錯誤。</summary>
      <returns>模型狀態錯誤。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelState.Value">
      <summary>取得 <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> 物件，其中會封裝模型繫結期間所繫結的值。</summary>
      <returns>模型狀態值。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelStateDictionary">
      <summary>表示嘗試將已張貼的表單繫結至動作方法的狀態，包括驗證資訊。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>使用從指定的模型狀態字典複製過來的值，初始化 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 類別的新執行個體。</summary>
      <param name="dictionary">字典。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Add(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>將指定的項目加入至模型狀態字典。</summary>
      <param name="item">要加入至模型狀態字典的物件。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Add(System.String,System.Web.Http.ModelBinding.ModelState)">
      <summary>將具有指定索引鍵和值的項目加入至模型狀態字典。</summary>
      <param name="key">要加入的元素的索引鍵。</param>
      <param name="value">要加入的元素值。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.Exception)">
      <summary>針對與指定索引鍵相關聯的模型狀態字典，將指定的模型錯誤加入至該字典的錯誤集合。</summary>
      <param name="key">索引鍵。</param>
      <param name="exception">例外狀況。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.String)">
      <summary>針對與指定索引鍵相關聯的模型狀態字典，將指定的錯誤訊息加入至該字典的錯誤集合。</summary>
      <param name="key">索引鍵。</param>
      <param name="errorMessage">錯誤訊息。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Clear">
      <summary>從模型狀態字典移除所有項目。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>判斷模型狀態字典是否包含特定值。</summary>
      <returns>如果在模型狀態字典中找到項目，則為 true，否則為 false。</returns>
      <param name="item">要在模型狀態字典中尋找的物件。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.ContainsKey(System.String)">
      <summary>判斷模型狀態字典是否包含指定的索引鍵。</summary>
      <returns>如果模型狀態字典包含指定的索引鍵，則為 true，否則為 false。</returns>
      <param name="key">要在模型狀態字典中尋找的索引鍵。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState}[],System.Int32)">
      <summary>從指定的索引開始，將模型狀態字典的項目複製到陣列。</summary>
      <param name="array">陣列。陣列必須有以零起始的索引。</param>
      <param name="arrayIndex">陣列中以零起始的索引，位於複製開始的位置。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Count">
      <summary>取得集合中的索引鍵/值組數目。</summary>
      <returns>集合中索引鍵/值組數目。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.GetEnumerator">
      <summary>傳回可用來逐一查看集合的列舉程式。</summary>
      <returns>可用來逐一查看集合的列舉程式。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.IsReadOnly">
      <summary>取得值，這個值表示集合是否為唯讀。</summary>
      <returns>如果集合是唯讀的，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.IsValid">
      <summary>取得值，這個值表示模型狀態字典的這個執行個體是否有效。</summary>
      <returns>如果這個執行個體有效，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.IsValidField(System.String)">
      <summary>判斷是否有任何 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 物件與指定的索引鍵相關聯或是以指定的索引鍵做為前置字元。</summary>
      <returns>如果模型狀態字典包含與指定之索引鍵相關聯的值，則為 true，否則為 false。</returns>
      <param name="key">索引鍵。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Item(System.String)">
      <summary>取得或設定與指定之索引鍵相關聯的值。</summary>
      <returns>模型狀態項目。</returns>
      <param name="key">索引鍵。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Keys">
      <summary>取得包含字典中的索引鍵之集合。</summary>
      <returns>包含模型狀態字典的索引鍵之集合。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Merge(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>從指定的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 物件將值複製到這個字典，而如果索引鍵相同則覆寫現有的值。</summary>
      <param name="dictionary">字典。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>從模型狀態字典中移除第一次出現的指定物件。</summary>
      <returns>如果已成功從模型狀態字典移除項目，則為 true，否則為 false。如果在模型狀態字典中找不到項目，這個方法也會傳回 false。</returns>
      <param name="item">要從模型狀態字典移除的物件。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Remove(System.String)">
      <summary>從模型狀態字典移除具有指定之索引鍵的項目。</summary>
      <returns>如果成功移除項目，則為 true，否則為 false。如果在模型狀態字典中找不到索引鍵，這個方法也會傳回 false。</returns>
      <param name="key">要移除之項目的索引鍵。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.SetModelValue(System.String,System.Web.Http.ValueProviders.ValueProviderResult)">
      <summary>使用指定的值提供者字典，設定指定之索引鍵的值。</summary>
      <param name="key">索引鍵。</param>
      <param name="value">數值。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉值。</summary>
      <returns>IEnumerator 物件，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.TryGetValue(System.String,System.Web.Http.ModelBinding.ModelState@)">
      <summary>嘗試取得與指定之索引鍵相關聯的值。</summary>
      <returns>如果物件包含具有指定索引鍵的項目，則為 true，否則為 false。</returns>
      <param name="key">要取得之值的索引鍵。</param>
      <param name="value">與指定索引鍵相關聯的值。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Values">
      <summary>取得包含字典中的值之集合。</summary>
      <returns>包含模型狀態字典的值之集合。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ParameterBindingRulesCollection">
      <summary> 可以為指定的參數產生參數繫結的函數集合。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ParameterBindingRulesCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.Add(System.Type,System.Func{System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.Controllers.HttpParameterBinding})">
      <summary> 將函數新增到集合的尾端。新增的函數是 funcInner 的包裝函式，可檢查 parameterType 是否符合 typeMatch。</summary>
      <param name="typeMatch">要與 HttpParameterDescriptor.ParameterType 進行比對的型別</param>
      <param name="funcInner">型別比對成功時所叫用的內部函數</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.Insert(System.Int32,System.Type,System.Func{System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.Controllers.HttpParameterBinding})">
      <summary> 將函數插入至集合中指定的索引處。/// 新增的函數是 funcInner 的包裝函式，可檢查 parameterType 是否符合 typeMatch。</summary>
      <param name="index">要插入的索引處。</param>
      <param name="typeMatch">要與 HttpParameterDescriptor.ParameterType 進行比對的型別</param>
      <param name="funcInner">型別比對成功時所叫用的內部函數</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.LookupBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary> 依序執行各繫結函數，直到其中一個函數傳回非 null 繫結為止。</summary>
      <returns>針對參數產生的第一個非 null 繫結。如果未產生任何繫結，則為 null。</returns>
      <param name="parameter">要繫結的參數。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1">
      <summary>將瀏覽器要求對應至陣列。</summary>
      <typeparam name="TElement">陣列的型別。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>表示是否繫結模型。</summary>
      <returns>如果繫結指定的模型，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{`0})">
      <summary>將集合轉換為陣列。</summary>
      <returns>在所有情況下均為 true。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
      <param name="newCollection">新的集合。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider">
      <summary>提供陣列的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>傳回陣列的模型繫結器。</summary>
      <returns>模型繫結器物件，如果嘗試取得模型繫結器失敗，則為 null。</returns>
      <param name="configuration">設定。</param>
      <param name="modelType">模型的型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1">
      <summary>將瀏覽器要求對應至集合。</summary>
      <typeparam name="TElement">集合的型別。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的執行內容和繫結內容來繫結模型。</summary>
      <returns>如果模型繫結成功，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{`0})">
      <summary>提供方法讓衍生類別先操作集合，再從繫結器執行該集合。</summary>
      <returns>在所有情況下均為 true。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
      <param name="newCollection">新的集合。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider">
      <summary>提供集合的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>擷取集合的模型繫結器。</summary>
      <returns>模型繫結器。</returns>
      <param name="configuration">模型的組態。</param>
      <param name="modelType">模型的型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto">
      <summary>表示複雜模型的資料傳輸物件 (DTO)。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDto.#ctor(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Metadata.ModelMetadata})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 類別的新執行個體。</summary>
      <param name="modelMetadata">模型中繼資料。</param>
      <param name="propertyMetadata">屬性中繼資料的集合。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.ModelMetadata">
      <summary>取得或設定 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的模型中繼資料。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的模型中繼資料。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.PropertyMetadata">
      <summary>取得或設定 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的屬性中繼資料集合。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的屬性中繼資料集合。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.Results">
      <summary>取得或設定 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的結果。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的結果。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder">
      <summary>表示 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 物件的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>判斷是否已繫結指定的模型。</summary>
      <returns>如果繫結指定的模型，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider">
      <summary>表示可叫用模型繫結器提供者的複雜模型。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>擷取相關聯的模型繫結器。</summary>
      <returns>模型繫結器。</returns>
      <param name="configuration">設定。</param>
      <param name="modelType">要擷取的模型型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult">
      <summary>表示 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 物件的結果。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.#ctor(System.Object,System.Web.Http.Validation.ModelValidationNode)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult" /> 類別的新執行個體。</summary>
      <param name="model">物件模型。</param>
      <param name="validationNode">驗證節點。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.Model">
      <summary>取得或設定此物件的模型。</summary>
      <returns>此物件的模型。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.ValidationNode">
      <summary>取得或設定此物件的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" />。</summary>
      <returns>此物件的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" />。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder">
      <summary>表示可委派至其中一個 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 執行個體集合的 <see cref="T:System.Web.Http.ModelBinding.IModelBinder" />。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.IModelBinder})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder" /> 類別的新執行個體。</summary>
      <param name="binders">繫結器的列舉。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.#ctor(System.Web.Http.ModelBinding.IModelBinder[])">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder" /> 類別的新執行個體。</summary>
      <param name="binders">繫結器的陣列。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>表示是否繫結指定的模型。</summary>
      <returns>如果繫結模型，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider">
      <summary>表示複合模型繫結器提供者的類別。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.ModelBinderProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider" /> 類別的新執行個體。</summary>
      <param name="providers">
        <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 的集合。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>取得模型的繫結器。</summary>
      <returns>模型的繫結器。</returns>
      <param name="configuration">繫結器組態。</param>
      <param name="modelType">模型的型別。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.Providers">
      <summary>取得複合模型繫結器的提供者。</summary>
      <returns>提供者的集合。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2">
      <summary>將瀏覽器要求對應至字典資料物件。</summary>
      <typeparam name="TKey">索引鍵的型別。</typeparam>
      <typeparam name="TValue">值的型別。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>將集合轉換成字典。</summary>
      <returns>在所有情況下均為 true。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
      <param name="newCollection">新的集合。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider">
      <summary>提供字典的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>擷取相關聯的模型繫結器。</summary>
      <returns>相關聯的模型繫結器。</returns>
      <param name="configuration">要使用的設定。</param>
      <param name="modelType">模型的型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2">
      <summary>將瀏覽器要求對應至索引鍵/值組資料物件。</summary>
      <typeparam name="TKey">索引鍵的型別。</typeparam>
      <typeparam name="TValue">值的型別。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的執行內容和繫結內容來繫結模型。</summary>
      <returns>如果模型繫結成功，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider">
      <summary>提供索引鍵/值組之集合的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>擷取相關聯的模型繫結器。</summary>
      <returns>相關聯的模型繫結器。</returns>
      <param name="configuration">設定。</param>
      <param name="modelType">模型的型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder">
      <summary>將瀏覽器要求對應至可變動的資料物件。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的動作內容和繫結內容來繫結模型。</summary>
      <returns>如果繫結成功，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.CanUpdateProperty(System.Web.Http.Metadata.ModelMetadata)">
      <summary>擷取可指出屬性是否可以更新的值。</summary>
      <returns>如果屬性可以更新，則為 true，否則為 false。</returns>
      <param name="propertyMetadata">要評估的屬性中繼資料。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.CreateModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>建立模型的執行個體。</summary>
      <returns>新建立的模型物件。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.EnsureModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>如果執行個體尚未存在於繫結內容中，即會建立模型執行個體。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.GetMetadataForProperties(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>擷取模型屬性中繼資料。</summary>
      <returns>模型屬性中繼資料。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.SetProperty(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Web.Http.Metadata.ModelMetadata,System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult,System.Web.Http.Validation.ModelValidator)">
      <summary>設定指定屬性的值。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
      <param name="propertyMetadata">要設定的屬性中繼資料。</param>
      <param name="dtoResult">關於屬性的驗證資訊。</param>
      <param name="requiredValidator">模型的驗證程式。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider">
      <summary>提供可變動物件的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>擷取指定型別的模型繫結器。</summary>
      <returns>模型繫結器。</returns>
      <param name="configuration">設定。</param>
      <param name="modelType">要擷取的模型型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider">
      <summary>提供此模型繫結類別的簡單模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.#ctor(System.Type,System.Func{System.Web.Http.ModelBinding.IModelBinder})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider" /> 類別的新執行個體。</summary>
      <param name="modelType">模型型別。</param>
      <param name="modelBinderFactory">模型繫結器 Factory。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.#ctor(System.Type,System.Web.Http.ModelBinding.IModelBinder)">
      <summary>使用指定的模型型別和模型繫結器，初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider" /> 類別的新執行個體。</summary>
      <param name="modelType">模型型別。</param>
      <param name="modelBinder">模型繫結器。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>使用指定的執行內容和繫結內容，傳回模型繫結器。</summary>
      <returns>模型繫結器，如果嘗試取得模型繫結器失敗，則為 null。</returns>
      <param name="configuration">設定。</param>
      <param name="modelType">模型型別。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.ModelType">
      <summary>取得模型的型別。</summary>
      <returns>模型的型別。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.SuppressPrefixCheck">
      <summary>取得或設定值，這個值指定是否應該隱藏前置詞檢查。</summary>
      <returns>如果應該隱藏前置詞檢查，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder">
      <summary>將瀏覽器要求對應至資料物件。如果模型繫結需要進行使用 .NET Framework 型別轉換器的轉換，即會使用這個型別。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的控制器內容和繫結內容來繫結模型。</summary>
      <returns>如果模型繫結成功，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider">
      <summary>提供需要型別轉換之模型的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>擷取需要型別轉換之模型的模型繫結器。</summary>
      <returns>模型繫結器，如果型別無法轉換或沒有任何可轉換的值，則為 Nothing。</returns>
      <param name="configuration">繫結器的組態。</param>
      <param name="modelType">模型的型別。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder">
      <summary>將瀏覽器要求對應至資料物件。當模型繫結不需要型別轉換時，會使用這個類別。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的執行內容和繫結內容來繫結模型。</summary>
      <returns>如果模型繫結成功，則為 true，否則為 false。</returns>
      <param name="actionContext">動作內容。</param>
      <param name="bindingContext">繫結內容。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider">
      <summary>提供不需要型別轉換之模型的模型繫結器。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>擷取相關聯的模型繫結器。</summary>
      <returns>相關聯的模型繫結器。</returns>
      <param name="configuration">設定。</param>
      <param name="modelType">模型的型別。</param>
    </member>
    <member name="T:System.Web.Http.Results.BadRequestErrorMessageResult">
      <summary>表示傳回 <see cref="F:System.Net.HttpStatusCode.BadRequest" /> 回應和在使用 <see cref="P:System.Web.Http.HttpError.Message" /> 查看 <see cref="T:System.Web.Http.HttpError" /> 上執行內容交涉的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestErrorMessageResult.#ctor(System.String,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestErrorMessageResult" /> 類別的新執行個體。</summary>
      <param name="message">使用者可看見的錯誤訊息。</param>
      <param name="contentNegotiator">處理內容交涉的內容交涉器。</param>
      <param name="request">導致此結果的要求訊息。</param>
      <param name="formatters">會用來交涉並格式化內容的格式器。</param>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestErrorMessageResult.#ctor(System.String,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestErrorMessageResult" /> 類別的新執行個體。</summary>
      <param name="message">使用者可看見的錯誤訊息。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.ContentNegotiator">
      <summary>取得處理內容交涉的內容交涉器。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" />。</returns>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestErrorMessageResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.Formatters">
      <summary>取得會用來交涉並格式化內容的格式器。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.Message">
      <summary>使用者可看見的錯誤訊息。</summary>
      <returns>傳回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.BadRequestResult">
      <summary>表示傳回空 <see cref="F:System.Net.HttpStatusCode.BadRequest" /> 回應的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestResult" /> 類別的新執行個體。</summary>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestResult" /> 類別的新執行個體。</summary>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>非同步執行要求。</summary>
      <returns>完成執行作業的工作。</returns>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>導致此結果的要求訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.ConflictResult">
      <summary>表示傳回空 HttpStatusCode.Conflict 回應的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.ConflictResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ConflictResult" /> 類別的新執行個體。</summary>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.ConflictResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ConflictResult" /> 類別的新執行個體。</summary>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.ConflictResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>非同步執行衝突結果作業。</summary>
      <returns>以非同步方式執行指定工作。</returns>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.ConflictResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>導致 HTTP 結果的要求訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1">
      <summary>表示執行路由產生和內容交涉的動作結果，並在內容交涉成功時傳回 <see cref="F:System.Net.HttpStatusCode.Created" /> 回應。</summary>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},`0,System.Web.Http.ApiController)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},`0,System.Web.Http.Routing.UrlHelper,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <param name="urlFactory">用於產生路由 URL 的 Factory。</param>
      <param name="contentNegotiator">處理內容交涉的內容交涉器。</param>
      <param name="request">導致此結果的要求訊息。</param>
      <param name="formatters">會用來交涉並格式化內容的格式器。</param>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.Content">
      <summary>取得在實體主體中協調和格式化的內容值。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.ContentNegotiator">
      <summary>取得處理內容交涉的內容交涉器。</summary>
    </member>
    <member name="M:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.Formatters">
      <summary>取得會用來交涉並格式化內容的格式器。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.Request">
      <summary>取得導致此結果的要求訊息。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.RouteName">
      <summary>取得用來產生 URL 的路由名稱。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.RouteValues">
      <summary>取得用來產生 URL 的路由資料。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.UrlFactory">
      <summary>取得用於產生路由 URL 的 Factory。</summary>
    </member>
    <member name="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1">
      <summary>表示執行內容交涉的動作結果，並在成功時傳回 <see cref="F:System.Net.HttpStatusCode.Created" /> 回應。</summary>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.CreatedNegotiatedContentResult`1.#ctor(System.Uri,`0,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="location">在實體主體中協調和格式化的內容值。</param>
      <param name="content">已建立內容的位置。</param>
      <param name="contentNegotiator">處理內容交涉的內容交涉器。</param>
      <param name="request">導致此結果的要求訊息。</param>
      <param name="formatters">會用來交涉並格式化內容的格式器。</param>
    </member>
    <member name="M:System.Web.Http.Results.CreatedNegotiatedContentResult`1.#ctor(System.Uri,`0,System.Web.Http.ApiController)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="location">已建立內容的位置。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Content">
      <summary>取得在實體主體中協調和格式化的內容值。</summary>
      <returns>在實體主體中協調和格式化的內容值。</returns>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.ContentNegotiator">
      <summary>取得處理內容交涉的內容交涉器。</summary>
      <returns>處理內容交涉的內容交涉器。</returns>
    </member>
    <member name="M:System.Web.Http.Results.CreatedNegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>非同步執行已建立交涉的內容結果作業。</summary>
      <returns>非同步執行傳回值。</returns>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Formatters">
      <summary>取得會用來交涉並格式化內容的格式器。</summary>
      <returns>會用來交涉並格式化內容的格式器。</returns>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Location">
      <summary>取得已建立內容的位置。</summary>
      <returns>已建立內容的位置。</returns>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>導致 HTTP 結果的要求訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.ExceptionResult">
      <summary>表示傳回 <see cref="F:System.Net.HttpStatusCode.InternalServerError" /> 回應和在 <see cref="T:System.Web.Http.HttpError" /> 上根據 <see cref="P:System.Web.Http.Results.ExceptionResult.Exception" /> 執行內容交涉的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.ExceptionResult.#ctor(System.Exception,System.Boolean,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ExceptionResult" /> 類別的新執行個體。</summary>
      <param name="exception">要包含在錯誤中的例外狀況。</param>
      <param name="includeErrorDetail">如果錯誤應該包含例外狀況訊息，則為 true，否則為 false。</param>
      <param name="contentNegotiator">處理內容交涉的內容交涉器。</param>
      <param name="request">導致此結果的要求訊息。</param>
      <param name="formatters">會用來交涉並格式化內容的格式器。</param>
    </member>
    <member name="M:System.Web.Http.Results.ExceptionResult.#ctor(System.Exception,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ExceptionResult" /> 類別的新執行個體。</summary>
      <param name="exception">要包含在錯誤中的例外狀況。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.ContentNegotiator">
      <summary>取得處理內容交涉的內容交涉器。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.Exception">
      <summary>取得要包含在錯誤中的例外狀況。</summary>
      <returns>傳回 <see cref="T:System.Exception" />。</returns>
    </member>
    <member name="M:System.Web.Http.Results.ExceptionResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.Formatters">
      <summary>取得會用來交涉並格式化內容的格式器。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.IncludeErrorDetail">
      <summary>取得值，這個值表示錯誤是否應該包括例外狀況訊息。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.FormattedContentResult`1">
      <summary>表示傳回格式化內容的動作結果。</summary>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.FormattedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Net.Http.HttpRequestMessage)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.FormattedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="content">在實體主體中格式化的內容值。</param>
      <param name="formatter">格式化內容時會使用的格式器。</param>
      <param name="mediaType">內容類型標頭值，或 <see cref="null" /> 讓格式器挑選預設值。</param>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.FormattedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Web.Http.ApiController)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.FormattedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="content">在實體主體中格式化的內容值。</param>
      <param name="formatter">格式化內容時會使用的格式器。</param>
      <param name="mediaType">內容類型標頭值，或 <see cref="null" /> 讓格式器挑選預設值。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.Content">
      <summary>取得在實體主體中格式化的內容值。</summary>
    </member>
    <member name="M:System.Web.Http.Results.FormattedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.Formatter">
      <summary>取得格式化內容時會使用的格式器。</summary>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.MediaType">
      <summary>取得內容類型標頭值，或 <see cref="null" /> 讓格式器挑選預設值。</summary>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.Request">
      <summary>取得導致此結果的要求訊息。</summary>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.StatusCode">
      <summary>取得回應訊息的 HTTP 狀態碼。</summary>
    </member>
    <member name="T:System.Web.Http.Results.InternalServerErrorResult">
      <summary>表示傳回空 <see cref="F:System.Net.HttpStatusCode.InternalServerError" /> 回應的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.InternalServerErrorResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InternalServerErrorResult" /> 類別的新執行個體。</summary>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.InternalServerErrorResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InternalServerErrorResult" /> 類別的新執行個體。</summary>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.InternalServerErrorResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InternalServerErrorResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.InvalidModelStateResult">
      <summary>表示傳回 <see cref="F:System.Net.HttpStatusCode.BadRequest" /> 回應和在 <see cref="T:System.Web.Http.HttpError" /> 上根據 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 執行內容交涉的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.InvalidModelStateResult.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.Boolean,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" /> 類別的新執行個體。</summary>
      <param name="modelState">要包含在錯誤中的模型狀態。</param>
      <param name="includeErrorDetail">如果錯誤應該包含例外狀況訊息，則為 true，否則為 false。</param>
      <param name="contentNegotiator">處理內容交涉的內容交涉器。</param>
      <param name="request">導致此結果的要求訊息。</param>
      <param name="formatters">會用來交涉並格式化內容的格式器。</param>
    </member>
    <member name="M:System.Web.Http.Results.InvalidModelStateResult.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" /> 類別的新執行個體。</summary>
      <param name="modelState">要包含在錯誤中的模型狀態。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.ContentNegotiator">
      <summary>取得處理內容交涉的內容交涉器。</summary>
      <returns>處理內容交涉的內容交涉器。</returns>
    </member>
    <member name="M:System.Web.Http.Results.InvalidModelStateResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>建立非同步回應訊息。</summary>
      <returns>工作完成時，會包含回應訊息。</returns>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.Formatters">
      <summary>取得會用來交涉並格式化內容的格式器。</summary>
      <returns>會用來交涉並格式化內容的格式器。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.IncludeErrorDetail">
      <summary>取得值，這個值表示錯誤是否應該包括例外狀況訊息。</summary>
      <returns>如果錯誤應該包含例外狀況訊息，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.ModelState">
      <summary>取得要包含在錯誤中的模型狀態。</summary>
      <returns>要包含在錯誤中的模型狀態。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>導致此結果的要求訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.JsonResult`1">
      <summary>表示傳回具有 JSON 資料的 <see cref="F:System.Net.HttpStatusCode.OK" /> 回應的動作結果。</summary>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.JsonResult`1.#ctor(`0,Newtonsoft.Json.JsonSerializerSettings,System.Text.Encoding,System.Net.Http.HttpRequestMessage)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.JsonResult`1" /> 類別的新執行個體。</summary>
      <param name="content">在實體主體中序列化的內容值。</param>
      <param name="serializerSettings">序列化程式設定。</param>
      <param name="encoding">內容編碼方式。</param>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.JsonResult`1.#ctor(`0,Newtonsoft.Json.JsonSerializerSettings,System.Text.Encoding,System.Web.Http.ApiController)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.JsonResult`1" /> 類別的新執行個體。</summary>
      <param name="content">在實體主體中序列化的內容值。</param>
      <param name="serializerSettings">序列化程式設定。</param>
      <param name="encoding">內容編碼方式。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.Content">
      <summary>取得在實體主體中序列化的內容值。</summary>
      <returns>在實體主體中序列化的內容值。</returns>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.Encoding">
      <summary>取得內容編碼方式。</summary>
      <returns>內容編碼方式。</returns>
    </member>
    <member name="M:System.Web.Http.Results.JsonResult`1.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>建立非同步回應訊息。</summary>
      <returns>工作完成時，會包含回應訊息。</returns>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>導致此結果的要求訊息。</returns>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.SerializerSettings">
      <summary>取得序列化程式設定。</summary>
      <returns>序列化程式設定。</returns>
    </member>
    <member name="T:System.Web.Http.Results.NegotiatedContentResult`1">
      <summary>表示執行內容交涉的動作結果。</summary>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.NegotiatedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.NegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <param name="contentNegotiator">處理內容交涉的內容交涉器。</param>
      <param name="request">導致此結果的要求訊息。</param>
      <param name="formatters">會用來交涉並格式化內容的格式器。</param>
    </member>
    <member name="M:System.Web.Http.Results.NegotiatedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Web.Http.ApiController)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.NegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.Content">
      <summary>取得在實體主體中協調和格式化的內容值。</summary>
      <returns>在實體主體中協調和格式化的內容值。</returns>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.ContentNegotiator">
      <summary>取得處理內容交涉的內容交涉器。</summary>
      <returns>處理內容交涉的內容交涉器。</returns>
    </member>
    <member name="M:System.Web.Http.Results.NegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>以非同步方式執行 HTTP 交涉的內容結果。</summary>
      <returns>以非同步方式執行 HTTP 交涉的內容結果。</returns>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.Formatters">
      <summary>取得會用來交涉並格式化內容的格式器。</summary>
      <returns>會用來交涉並格式化內容的格式器。</returns>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>導致 HTTP 結果的要求訊息。</returns>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.StatusCode">
      <summary>取得回應訊息的 HTTP 狀態碼。</summary>
      <returns>回應訊息的 HTTP 狀態碼。</returns>
    </member>
    <member name="T:System.Web.Http.Results.NotFoundResult">
      <summary>表示傳回空 <see cref="F:System.Net.HttpStatusCode.NotFound" /> 回應的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.NotFoundResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 類別的新執行個體。</summary>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.NotFoundResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 類別的新執行個體。</summary>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.NotFoundResult.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.NotFoundResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
    </member>
    <member name="T:System.Web.Http.Results.OkNegotiatedContentResult`1">
      <summary>表示執行內容交涉的動作結果，並在成功時傳回 HttpStatusCode.OK 回應。</summary>
      <typeparam name="T">實體主體中的內容類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.OkNegotiatedContentResult`1.#ctor(`0,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <param name="contentNegotiator">處理內容交涉的內容交涉器。</param>
      <param name="request">導致此結果的要求訊息。</param>
      <param name="formatters">會用來交涉並格式化內容的格式器。</param>
    </member>
    <member name="M:System.Web.Http.Results.OkNegotiatedContentResult`1.#ctor(`0,System.Web.Http.ApiController)">
      <summary>初始化具有提供值的 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" /> 類別的新執行個體。</summary>
      <param name="content">在實體主體中協調和格式化的內容值。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.Content">
      <summary>取得在實體主體中協調和格式化的內容值。</summary>
    </member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.ContentNegotiator">
      <summary>取得處理內容交涉的內容交涉器。</summary>
    </member>
    <member name="M:System.Web.Http.Results.OkNegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.Formatters">
      <summary>取得會用來交涉並格式化內容的格式器。</summary>
    </member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.Request">
      <summary>取得導致此結果的要求訊息。</summary>
    </member>
    <member name="T:System.Web.Http.Results.OkResult">
      <summary>表示傳回空 HttpStatusCode.OK 回應的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.OkResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 類別的新執行個體。</summary>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.OkResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 類別的新執行個體。</summary>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.OkResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>非同步執行。</summary>
      <returns>傳回工作。</returns>
      <param name="cancellationToken">取消權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.OkResult.Request">
      <summary>取得結果的 HTTP 要求訊息。</summary>
      <returns>結果的 HTTP 要求訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.RedirectResult">
      <summary>表示 &lt;see cref="F:System.Net.HttpStatusCode.Redirect"/&gt; 的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.RedirectResult.#ctor(System.Uri,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 &lt;see cref="T:System.Web.Http.Results.RedirectResult"/&gt; 類別的新執行個體。</summary>
      <param name="location">要重新導向的位置。</param>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectResult.#ctor(System.Uri,System.Web.Http.ApiController)">
      <summary>初始化 &lt;see cref="T:System.Web.Http.Results.RedirectResult"/&gt; 類別的新執行個體。</summary>
      <param name="location">要重新導向的位置。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectResult.Location">
      <summary>取得已建立內容的位置。</summary>
      <returns>傳回 <see cref="T:System.Uri" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.RedirectToRouteResult">
      <summary>表示執行路由產生並傳回 &lt;see cref="F:System.Net.HttpStatusCode.Redirect"/&gt;           回應的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.RedirectToRouteResult.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.ApiController)">
      <summary>初始化 &lt;see cref="T:System.Web.Http.Results.RedirectToRouteResult"/&gt; 類別的新執行個體。</summary>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectToRouteResult.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.UrlHelper,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 &lt;see cref="T:System.Web.Http.Results.RedirectToRouteResult"/&gt; 類別的新執行個體。</summary>
      <param name="routeName">用來產生 URL 的路由名稱。</param>
      <param name="routeValues">用來產生 URL 的路由資料。</param>
      <param name="urlFactory">用於產生路由 URL 的 Factory。</param>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectToRouteResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.RouteName">
      <summary>取得用來產生 URL 的路由名稱。</summary>
      <returns>傳回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.RouteValues">
      <summary>取得用來產生 URL 的路由資料。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IDictionary`2" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.UrlFactory">
      <summary>取得用於產生路由 URL 的 Factory。</summary>
      <returns>傳回 <see cref="T:System.Web.Http.Routing.UrlHelper" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.ResponseMessageResult">
      <summary>表示傳回指定回應訊息的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.ResponseMessageResult.#ctor(System.Net.Http.HttpResponseMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ResponseMessageResult" /> 類別的新執行個體。</summary>
      <param name="response">回應訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.ResponseMessageResult.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.ResponseMessageResult.Response">
      <summary>取得回應訊息。</summary>
    </member>
    <member name="T:System.Web.Http.Results.StatusCodeResult">
      <summary>表示傳回指定 HTTP 狀態代碼的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.StatusCodeResult.#ctor(System.Net.HttpStatusCode,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 類別的新執行個體。</summary>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.StatusCodeResult.#ctor(System.Net.HttpStatusCode,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 類別的新執行個體。</summary>
      <param name="statusCode">回應訊息的 HTTP 狀態碼。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.StatusCodeResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>建立非同步回應訊息。</summary>
      <returns>工作完成時，會包含回應訊息。</returns>
      <param name="cancellationToken">用於監控取消要求的權杖。</param>
    </member>
    <member name="P:System.Web.Http.Results.StatusCodeResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>導致此結果的要求訊息。</returns>
    </member>
    <member name="P:System.Web.Http.Results.StatusCodeResult.StatusCode">
      <summary>取得回應訊息的 HTTP 狀態碼。</summary>
      <returns>回應訊息的 HTTP 狀態碼。</returns>
    </member>
    <member name="T:System.Web.Http.Results.UnauthorizedResult">
      <summary>表示傳回 <see cref="F:System.Net.HttpStatusCode.Unauthorized" /> 回應的動作結果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.UnauthorizedResult.#ctor(System.Collections.Generic.IEnumerable{System.Net.Http.Headers.AuthenticationHeaderValue},System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 類別的新執行個體。</summary>
      <param name="challenges">WWW 驗證挑戰。</param>
      <param name="request">導致此結果的要求訊息。</param>
    </member>
    <member name="M:System.Web.Http.Results.UnauthorizedResult.#ctor(System.Collections.Generic.IEnumerable{System.Net.Http.Headers.AuthenticationHeaderValue},System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 類別的新執行個體。</summary>
      <param name="challenges">WWW 驗證挑戰。</param>
      <param name="controller">取得執行所需的相依項的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.UnauthorizedResult.Challenges">
      <summary>取得 WWW 驗證挑戰。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</returns>
    </member>
    <member name="M:System.Web.Http.Results.UnauthorizedResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.UnauthorizedResult.Request">
      <summary>取得導致此結果的要求訊息。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.DefaultDirectRouteProvider">
      <summary>預設 <see cref="T:System.Web.Http.Routing.IDirectRouteProvider" /> 實作。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.#ctor"></member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetActionDirectRoutes(System.Web.Http.Controllers.HttpActionDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Routing.IDirectRouteFactory},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>根據提供的 Factory 及動作建立 <see cref="T:System.Web.Http.Routing.RouteEntry" /> 執行個體。路由項目提供所提供之動作的直接路由。</summary>
      <returns>一組路由項目。</returns>
      <param name="actionDescriptor">動作描述元。</param>
      <param name="factories">直接路由 Factory。</param>
      <param name="constraintResolver">條件約束解析程式。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetActionRouteFactories(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>取得所指定之動作描述元的一組路由 Factory。</summary>
      <returns>一組路由 Factory。</returns>
      <param name="actionDescriptor">動作描述元。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetControllerDirectRoutes(System.Web.Http.Controllers.HttpControllerDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Controllers.HttpActionDescriptor},System.Collections.Generic.IReadOnlyList{System.Web.Http.Routing.IDirectRouteFactory},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>根據提供的 Factory、控制器及動作建立 <see cref="T:System.Web.Http.Routing.RouteEntry" /> 執行個體。路由項目已提供所提供之控制器的直接路由，而且可以到達所提供的動作集。</summary>
      <returns>一組路由項目。</returns>
      <param name="controllerDescriptor">控制器描述元。</param>
      <param name="actionDescriptors">動作描述元。</param>
      <param name="factories">直接路由 Factory。</param>
      <param name="constraintResolver">條件約束解析程式。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetControllerRouteFactories(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>取得所指定之控制器描述元的路由 Factory。</summary>
      <returns>一組路由 Factory。</returns>
      <param name="controllerDescriptor">控制器描述元。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetDirectRoutes(System.Web.Http.Controllers.HttpControllerDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Controllers.HttpActionDescriptor},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>根據 <see cref="T:System.Web.Http.Routing.IDirectRouteFactory" /> 屬性取得所指定之控制器描述元及動作描述元的直接路由。</summary>
      <returns>一組路由項目。</returns>
      <param name="controllerDescriptor">控制器描述元。</param>
      <param name="actionDescriptors">所有動作的動作描述元。</param>
      <param name="constraintResolver">條件約束解析程式。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetRoutePrefix(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>從提供的控制器取得路由前置詞。</summary>
      <returns>路由前置詞或 null。</returns>
      <param name="controllerDescriptor">控制器描述元。</param>
    </member>
    <member name="T:System.Web.Http.Routing.DefaultInlineConstraintResolver">
      <summary>預設 <see cref="T:System.Web.Http.Routing.IInlineConstraintResolver" /> 實作。以剖析常數索引鍵和常數引數的方式解析常數，使用對應來解析常數類型，並呼叫適當的常數類型建構函式。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultInlineConstraintResolver.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.DefaultInlineConstraintResolver" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.DefaultInlineConstraintResolver.ConstraintMap">
      <summary> 取得可變動字典，其可對應常數索引鍵至特殊的常數類型。</summary>
      <returns>可變動字典，其可對應常數索引鍵至特殊的常數類型。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultInlineConstraintResolver.ResolveConstraint(System.String)">
      <summary>解析內部條件約束。</summary>
      <returns>要解析的 <see cref="T:System.Web.Http.Routing.IHttpRouteConstraint" /> 內部條件約束。</returns>
      <param name="inlineConstraint">要解析的內部條件約束。</param>
    </member>
    <member name="T:System.Web.Http.Routing.DirectRouteFactoryContext">
      <summary>表示支援建立直接路由的內容。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.DirectRouteFactoryContext.#ctor(System.String,System.Collections.Generic.IReadOnlyCollection{System.Web.Http.Controllers.HttpActionDescriptor},System.Web.Http.Routing.IInlineConstraintResolver,System.Boolean)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.DirectRouteFactoryContext" /> 類別的新執行個體。</summary>
      <param name="prefix">如果有則為路由前置字元，由該控制器定義。</param>
      <param name="actions">建立路由的動作描述。</param>
      <param name="inlineConstraintResolver">內部條件約束解析程式。</param>
      <param name="targetIsAction">指出路由是否在動作或控制器層級設定的值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.Actions">
      <summary>取得建立路由的動作描述。</summary>
      <returns>建立路由的動作描述。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.DirectRouteFactoryContext.CreateBuilder(System.String)">
      <summary>建立路由建置器，以建置符合此內容的路由。</summary>
      <returns>建置符合此內容路由的路由建置器。</returns>
      <param name="template">路由範本。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DirectRouteFactoryContext.CreateBuilder(System.String,System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>建立路由建置器，以建置符合此內容的路由。</summary>
      <returns>建置符合此內容路由的路由建置器。</returns>
      <param name="template">路由範本。</param>
      <param name="constraintResolver">如果有則為要使用的內部解析程式，否則為 null。</param>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.InlineConstraintResolver">
      <summary>取得內部條件約束解析程式。</summary>
      <returns>內部條件約束解析程式。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.Prefix">
      <summary>如果有則取得路由前置字元，由該控制器定義。</summary>
      <returns>如果有則為路由前置字元，由該控制器定義。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.TargetIsAction">
      <summary>取得一值，指出路由是否在動作或控制器層級設定。</summary>
      <returns>路由在動作層級中設定時為 true，若路由在控制器層級設定時則為 false。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpMethodConstraint">
      <summary>讓您能夠定義當 ASP.NET 路由判斷 URL 是否符合某個路由時，允許使用哪些 HTTP 動詞命令。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.#ctor(System.Net.Http.HttpMethod[])">
      <summary>使用允許用於路由的 HTTP 動詞命令，初始化 <see cref="T:System.Web.Http.Routing.HttpMethodConstraint" /> 類別的新執行個體。</summary>
      <param name="allowedMethods">適用於路由的 HTTP 動詞命令。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpMethodConstraint.AllowedMethods">
      <summary>取得或設定允許用於路由之 HTTP 動詞命令的集合。</summary>
      <returns>允許用於路由之 HTTP 動詞命令的集合。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷要求是否是使用 HTTP 動詞命令所提出，這個動詞命令是允許用於路由之動詞命令的其中一個。</summary>
      <returns>在 ASP.NET 路由處理要求時，如果要求是使用允許的 HTTP 動詞命令所提出，則為 true，否則為 false。在 ASP.NET 路由建構 URL 時，如果提供的值包含與其中一個允許之 HTTP 動詞命令相符的 HTTP 動詞命令，則為 true，否則為 false。預設值為 true。</returns>
      <param name="request">進行檢查以判斷其是否符合 URL 的要求。</param>
      <param name="route">進行檢查以判斷其是否符合 URL 的物件。</param>
      <param name="parameterName">進行檢查的參數名稱。</param>
      <param name="values">包含路徑參數的物件。</param>
      <param name="routeDirection">一個物件，指出在處理傳入要求或產生 URL 時，是否會執行條件約束檢查。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.System#Web#Http#Routing#IHttpRouteConstraint#Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷要求是否是使用 HTTP 動詞命令所提出，這個動詞命令是允許用於路由之動詞命令的其中一個。</summary>
      <returns>在 ASP.NET 路由處理要求時，如果要求是使用允許的 HTTP 動詞命令所提出，則為 true，否則為 false。在 ASP.NET 路由建構 URL 時，如果提供的值包含與其中一個允許之 HTTP 動詞命令相符的 HTTP 動詞命令，則為 true，否則為 false。預設值為 true。</returns>
      <param name="request">進行檢查以判斷其是否符合 URL 的要求。</param>
      <param name="route">進行檢查以判斷其是否符合 URL 的物件。</param>
      <param name="parameterName">進行檢查的參數名稱。</param>
      <param name="values">包含路徑參數的物件。</param>
      <param name="routeDirection">一個物件，指出在處理傳入要求或產生 URL 時，是否會執行條件約束檢查。</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRoute">
      <summary>表示自助代管路由類別 (例如在 ASP.NET 之外代管)。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 類別的新執行個體。</summary>
      <param name="routeTemplate">路由範本。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 類別的新執行個體。</summary>
      <param name="routeTemplate">路由範本。</param>
      <param name="defaults">路由參數的預設值。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 類別的新執行個體。</summary>
      <param name="routeTemplate">路由範本。</param>
      <param name="defaults">路由參數的預設值。</param>
      <param name="constraints">路由參數的條件約束。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 類別的新執行個體。</summary>
      <param name="routeTemplate">路由範本。</param>
      <param name="defaults">路由參數的預設值。</param>
      <param name="constraints">路由參數的條件約束。</param>
      <param name="dataTokens">路由參數的任何其他權杖。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Net.Http.HttpMessageHandler)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 類別的新執行個體。</summary>
      <param name="routeTemplate">路由範本。</param>
      <param name="defaults">路由參數的預設值。</param>
      <param name="constraints">路由參數的條件約束。</param>
      <param name="dataTokens">路由參數的任何其他權杖。</param>
      <param name="handler">將成為要求接收者的訊息處理常式。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Constraints">
      <summary>取得路由參數的條件約束。</summary>
      <returns>路由參數的條件約束。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.DataTokens">
      <summary>取得並未直接用來決定路由是否符合傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的其他資料基元。</summary>
      <returns>並未直接用來決定路由是否符合傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的其他資料權杖。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Defaults">
      <summary>若傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 未提供，則取得路由參數的預設值。</summary>
      <returns>若傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 未提供，則使用路由參數的預設值。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.GetRouteData(System.String,System.Net.Http.HttpRequestMessage)">
      <summary>尋找路由的 <see cref="T:System.Web.Http.Routing.HttpRouteData" />，以判斷此路由是否符合傳入要求。</summary>
      <returns>若相符，則為路由的 <see cref="T:System.Web.Http.Routing.HttpRouteData" />，否則為 null。</returns>
      <param name="virtualPathRoot">虛擬路徑根。</param>
      <param name="request">HTTP 要求。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>嘗試產生 URI，該 URI 表示根據從 <see cref="T:System.Web.Http.Routing.HttpRouteData" /> 的目前值傳送的值，以及使用特定 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 的新值。</summary>
      <returns>如果無法產生 URI，則為 <see cref="T:System.Web.Http.Routing.HttpVirtualPathData" /> 執行個體或 Null。</returns>
      <param name="request">HTTP 要求的訊息。</param>
      <param name="values">路徑值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Handler">
      <summary>取得或設定 HTTP 路由處理常式。</summary>
      <returns>HTTP 路由處理常式。</returns>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRoute.HttpRouteKey">
      <summary>指定 HTTP 路由索引鍵。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.ProcessConstraint(System.Net.Http.HttpRequestMessage,System.Object,System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">HTTP 要求。</param>
      <param name="constraint">路由參數的條件約束。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">
        <see cref="System.Web.Http.Routing.HttpRouteDirection" /> 列舉的其中一個列舉值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.RouteTemplate">
      <summary>取得描述 URI 模式符合的路由範本。</summary>
      <returns>描述要用於比對 URI 模式的路由範本。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteData">
      <summary>封裝關於 HTTP 路由的資訊。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteData.#ctor(System.Web.Http.Routing.IHttpRoute)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteData" /> 類別的新執行個體。</summary>
      <param name="route">定義路由的物件。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteData.#ctor(System.Web.Http.Routing.IHttpRoute,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteData" /> 類別的新執行個體。</summary>
      <param name="route">定義路由的物件。</param>
      <param name="values">數值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRouteData.Route">
      <summary>取得表示路由的物件。</summary>
      <returns>表示路由的物件。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRouteData.Values">
      <summary>取得路由之 URL 參數值和預設值的集合。</summary>
      <returns>物件，其包含從 URL 和預設值剖析而來的值。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteDataExtensions">
      <summary>從路由資料中移除所有沒有值的選用參數。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteDataExtensions.GetSubRoutes(System.Web.Http.Routing.IHttpRouteData)">
      <summary>若路由為其他路由的集合，則傳回一組子路由。</summary>
      <returns>傳回包含在此路由中的一組子路由。</returns>
      <param name="routeData">集合路由資料。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteDataExtensions.RemoveOptionalRoutingParameters(System.Web.Http.Routing.IHttpRouteData)">
      <summary>從路由資料中移除所有沒有值的選用參數。</summary>
      <param name="routeData">要變更的路由資料。</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteDirection">
      <summary>指定路由方向的列舉。</summary>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRouteDirection.UriGeneration">
      <summary>UriGeneration 方向。</summary>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRouteDirection.UriResolution">
      <summary>UriResolution 方向。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteValueDictionary">
      <summary>表示指定索引鍵/值組之自助代管的路由類別。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> 類別的新執行個體。</summary>
      <param name="dictionary">字典。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor(System.Object)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> 類別的新執行個體。</summary>
      <param name="values">索引鍵值。</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpVirtualPathData">
      <summary>顯示關於 HTTP 虛擬路徑的資料。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpVirtualPathData.#ctor(System.Web.Http.Routing.IHttpRoute,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpVirtualPathData" /> 類別的新執行個體。</summary>
      <param name="route">虛擬路徑的路由。</param>
      <param name="virtualPath">根據路由定義建立的 URL。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpVirtualPathData.Route">
      <summary>取得或設定虛擬路徑的路由。</summary>
      <returns>虛擬路徑的路由。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpVirtualPathData.VirtualPath">
      <summary>取得或設定根據路由定義建立的 URL。</summary>
      <returns>根據路由定義建立的 URL。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IDirectRouteBuilder">
      <summary>定義建立動作直接路由的建置器 (屬性路由)。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Actions">
      <summary>取得建立路由的動作描述。</summary>
      <returns>建立路由的動作描述。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.IDirectRouteBuilder.Build">
      <summary>根據目前屬性值建立一個路由項目。</summary>
      <returns>建立的路由項目。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Constraints">
      <summary>取得或設定路由條件約束。</summary>
      <returns>路由條件約束。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.DataTokens">
      <summary>取得或設定路由資料語彙基元。</summary>
      <returns>路由資料權杖。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Defaults">
      <summary>取得或設定路由預設值。</summary>
      <returns>路由預設值。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Name">
      <summary>取得或設定路由名稱 (如果有)，否則為 null。</summary>
      <returns>如果有則為路由名稱，否則為 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Order">
      <summary>取得或設定路由順序。</summary>
      <returns>路由順序。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Precedence">
      <summary>取得或設定路由優先權。</summary>
      <returns>路由優先權。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.TargetIsAction">
      <summary>取得一值，指出路由是否在動作或控制器層級設定。</summary>
      <returns>路由在動作層級中設定時為 true，若路由在控制器層級設定時則為 false。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Template">
      <summary>取得或設定路由範本。</summary>
      <returns>路由範本。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IDirectRouteFactory">
      <summary>定義 Factory，其用來直接建立一組動作描述元的路由 (屬性路由)。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IDirectRouteFactory.CreateRoute(System.Web.Http.Routing.DirectRouteFactoryContext)">
      <summary>建立直接路由項目。</summary>
      <returns>直接路由項目。</returns>
      <param name="context">用於建立路由的內容。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IDirectRouteProvider">
      <summary>定義直接以動作描述元 (屬性路由) 為目標之路由的提供者。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IDirectRouteProvider.GetDirectRoutes(System.Web.Http.Controllers.HttpControllerDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Controllers.HttpActionDescriptor},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>取得控制器的直接路由。</summary>
      <returns>控制器的一組路由項目。</returns>
      <param name="controllerDescriptor">控制器描述元。</param>
      <param name="actionDescriptors">動作描述元。</param>
      <param name="constraintResolver">內部條件約束解析程式。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRoute">
      <summary>
        <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 定義路由的介面，說明如何對應傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 至特殊的控制器和動作。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Constraints">
      <summary> 取得路由參數的條件約束。</summary>
      <returns>路由參數的條件約束。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.DataTokens">
      <summary> 取得並未直接用來決定路由是否符合傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的其他資料基元。</summary>
      <returns>其他資料權杖。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Defaults">
      <summary> 若傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 未提供，則取得路由參數的預設值。</summary>
      <returns>路由參數的預設值。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRoute.GetRouteData(System.String,System.Net.Http.HttpRequestMessage)">
      <summary> 決定此路由是否符合傳入要求，方法為尋找 &lt;see cref="!:IRouteData" /&gt; 的路由。</summary>
      <returns>若相符，則 &lt;see cref="!:RouteData" /&gt; 為路由，否則為 null。</returns>
      <param name="virtualPathRoot">虛擬路徑根。</param>
      <param name="request">要求。</param>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRoute.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>取得以提供的路由和值為基礎的虛擬路徑資料。</summary>
      <returns>虛擬路徑資料。</returns>
      <param name="request">要求訊息。</param>
      <param name="values">值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Handler">
      <summary>取得將成為要求接收者的訊息處理常式。</summary>
      <returns>訊息處理常式。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.RouteTemplate">
      <summary> 取得描述 URI 模式符合的路由範本。</summary>
      <returns>路由範本。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteConstraint">
      <summary>表示基底類別路由條件約束。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteData">
      <summary>提供路由的相關資訊。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteData.Route">
      <summary>取得表示路由的物件。</summary>
      <returns>表示路由的物件。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteData.Values">
      <summary>取得路由之 URL 參數值和預設值的集合。</summary>
      <returns>從 URL 和預設值剖析而來的值。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteInfoProvider">
      <summary> 提供定義路由的相關資訊。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteInfoProvider.Name">
      <summary> 取得要產生的路由名稱。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteInfoProvider.Order">
      <summary> 取得與其他路徑相關的路由順序。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteInfoProvider.Template">
      <summary> 取得描述 URI 模式符合的路由範本。 </summary>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpVirtualPathData">
      <summary>定義 HTTP 路由的屬性。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpVirtualPathData.Route">
      <summary>取得 HTTP 路由。</summary>
      <returns>HTTP 路由。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpVirtualPathData.VirtualPath">
      <summary>取得 URI，該 URI 表示目前 HTTP 路由的虛擬路徑。</summary>
      <returns>表示目前 HTTP 路由之虛擬路徑的 URI。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IInlineConstraintResolver">
      <summary> 定義抽象方法，用來解析做為 <see cref="T:System.Web.Http.Routing.IHttpRouteConstraint" /> 執行個體的內部條件約束。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IInlineConstraintResolver.ResolveConstraint(System.String)">
      <summary> 解析內部條件約束。</summary>
      <returns>要解析的 <see cref="T:System.Web.Http.Routing.IHttpRouteConstraint" /> 內部條件約束。</returns>
      <param name="inlineConstraint">要解析的內部條件約束。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IRoutePrefix">
      <summary>定義路由前置字元。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IRoutePrefix.Prefix">
      <summary>取得路由前置字元。</summary>
      <returns>路由前置字元。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.RouteEntry">
      <summary>表示已命名路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.RouteEntry.#ctor(System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.RouteEntry" /> 類別的新執行個體。</summary>
      <param name="name">如果有則為路由名稱，否則為 null。</param>
      <param name="route">路由。</param>
    </member>
    <member name="P:System.Web.Http.Routing.RouteEntry.Name">
      <summary>取得路由名稱 (如果有)，否則為 null。</summary>
      <returns>如果有則為路由名稱，否則為 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteEntry.Route">
      <summary>取得路由。</summary>
      <returns>路由。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.RouteFactoryAttribute">
      <summary>表示包含自訂條件約束的屬性路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.RouteFactoryAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.RouteFactoryAttribute" /> 類別的新執行個體。</summary>
      <param name="template">路由範本。</param>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Constraints">
      <summary>取得路由條件約束 (如果有)，否則為 null。</summary>
      <returns>如果有則為路由條件約束，否則為 null。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.RouteFactoryAttribute.CreateRoute(System.Web.Http.Routing.DirectRouteFactoryContext)">
      <summary>建立路由項目。</summary>
      <returns>建立的路由項目。</returns>
      <param name="context">內容。</param>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.DataTokens">
      <summary>取得路由資料權杖 (如果有)，否則為 null。</summary>
      <returns>如果有則為資料語彙基元，否則為 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Defaults">
      <summary>取得路由預設值 (如果有)，否則為 null。</summary>
      <returns>如果有則為路由預設值，否則為 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Name">
      <summary>取得或設定路由名稱 (如果有)，否則為 null。</summary>
      <returns>如果有則為路由名稱，否則為 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Order">
      <summary>取得或設定路由順序。</summary>
      <returns>路由順序。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Template">
      <summary>取得路由範本。</summary>
      <returns>路由範本。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.StopRoutingHandler">
      <summary>表示指定路由不應該處理路由範本要求的處理常式。當路由提供此類別做為處理常式時，會忽略符合路由的要求。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.StopRoutingHandler.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.StopRoutingHandler" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.UrlHelper">
      <summary>表示建立 URL 的 Factory。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.#ctor">
      <summary> 初始化 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 類別的新執行個體。</summary>
      <param name="request">此執行個體的 HTTP 要求。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Content(System.String)">
      <summary>使用指定路徑建立絕對 URL。</summary>
      <returns>產生的 URL。</returns>
      <param name="path">URL 路徑可能為相對 URL、根 URL 或虛擬路徑。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Link(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>傳回指定路由的連結。</summary>
      <returns>指定路由的連結。</returns>
      <param name="routeName">路徑的名稱。</param>
      <param name="routeValues">包含路徑參數的物件。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Link(System.String,System.Object)">
      <summary>傳回指定路由的連結。</summary>
      <returns>指定路由的連結。</returns>
      <param name="routeName">路徑的名稱。</param>
      <param name="routeValues">路由值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.UrlHelper.Request">
      <summary>取得或設定目前 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 執行個體。</summary>
      <returns>目前執行個體的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Route(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>傳回 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</summary>
      <returns>
        <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</returns>
      <param name="routeName">路徑的名稱。</param>
      <param name="routeValues">路由值清單。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Route(System.String,System.Object)">
      <summary>傳回 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</summary>
      <returns>
        <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</returns>
      <param name="routeName">路徑的名稱。</param>
      <param name="routeValues">路徑值。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.AlphaRouteConstraint">
      <summary>限制路由參數僅包含小寫或大寫英文字母 A 到 Z。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.AlphaRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.AlphaRouteConstraint" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.BoolRouteConstraint">
      <summary>限制路由參數僅表示布林值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.BoolRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.BoolRouteConstraint" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.BoolRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.CompoundRouteConstraint">
      <summary>以數個子條件約束來限制路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.CompoundRouteConstraint.#ctor(System.Collections.Generic.IList{System.Web.Http.Routing.IHttpRouteConstraint})">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.CompoundRouteConstraint" /> 類別的新執行個體。</summary>
      <param name="constraints">必須符合此條件約束的要符合的子條件約束。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.CompoundRouteConstraint.Constraints">
      <summary>取得必須符合此條件約束的要符合的子條件約束。</summary>
      <returns>必須符合此條件約束的要符合的子條件約束。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.CompoundRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint">
      <summary>限制路由參數僅表示 <see cref="T:System.DateTime" /> 值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.DecimalRouteConstraint">
      <summary>限制路由參數僅表示十進位值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DecimalRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.DecimalRouteConstraint" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DecimalRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.DoubleRouteConstraint">
      <summary>限制路由參數僅表示 64 位元的浮動點值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DoubleRouteConstraint.#ctor"></member>
    <member name="M:System.Web.Http.Routing.Constraints.DoubleRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="T:System.Web.Http.Routing.Constraints.FloatRouteConstraint">
      <summary>限制路由參數僅表示 32 位元的浮動點值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.FloatRouteConstraint.#ctor"></member>
    <member name="M:System.Web.Http.Routing.Constraints.FloatRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="T:System.Web.Http.Routing.Constraints.GuidRouteConstraint">
      <summary>限制路由參數僅表示 <see cref="T:System.Guid" /> 值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.GuidRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.GuidRouteConstraint" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.GuidRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.IntRouteConstraint">
      <summary>限制路由參數僅表示 32 位元的整數值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.IntRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.IntRouteConstraint" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.IntRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.LengthRouteConstraint">
      <summary>限制路由參數為給定長度的字串，或位於給定長度範圍內。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.LengthRouteConstraint.#ctor(System.Int32)"></member>
    <member name="M:System.Web.Http.Routing.Constraints.LengthRouteConstraint.#ctor(System.Int32,System.Int32)">
      <summary>初始 <see cref="T:System.Web.Http.Routing.Constraints.LengthRouteConstraint" /> 類別的新執行個體，其限制路由參數為給定長度的字串。</summary>
      <param name="minLength">路由參數的最小長度。</param>
      <param name="maxLength">路由參數的最大長度。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.LengthRouteConstraint.Length">
      <summary>若已設定，取得路由參數的長度。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.LengthRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="P:System.Web.Http.Routing.Constraints.LengthRouteConstraint.MaxLength">
      <summary>若已設定，取得路由參數的最大長度。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.LengthRouteConstraint.MinLength">
      <summary>若已設定，取得路由參數的最小長度。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.LongRouteConstraint">
      <summary>限制路由參數僅表示 64 位元的整數值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.LongRouteConstraint.#ctor"></member>
    <member name="M:System.Web.Http.Routing.Constraints.LongRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="T:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint">
      <summary>限制路由參數為最大長度的字串。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint" /> 類別的新執行個體。</summary>
      <param name="maxLength">最大長度。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint.MaxLength">
      <summary>取得路由參數的最大長度。</summary>
      <returns>路由參數的最大長度。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.MaxRouteConstraint">
      <summary>限制路由參數為最大值的整數。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxRouteConstraint.#ctor(System.Int64)"></member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="P:System.Web.Http.Routing.Constraints.MaxRouteConstraint.Max">
      <summary>取得路由參數的最大值。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint">
      <summary>限制路由參數為最大長度的字串。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint" /> 類別的新執行個體。</summary>
      <param name="minLength">最小長度。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint.MinLength">
      <summary>取得路由參數的最小長度。</summary>
      <returns>路由參數的最小長度。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.MinRouteConstraint">
      <summary>限制路由參數為最小值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinRouteConstraint.#ctor(System.Int64)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.MinRouteConstraint" /> 類別的新執行個體。</summary>
      <param name="min">路由參數的最小值。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.MinRouteConstraint.Min">
      <summary>取得路由參數的最小值。</summary>
      <returns>路由參數的最小值。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.OptionalRouteConstraint">
      <summary>以選擇性參數設為其預設值時不會失敗的內部常數來限制路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.OptionalRouteConstraint.#ctor(System.Web.Http.Routing.IHttpRouteConstraint)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.OptionalRouteConstraint" /> 類別的新執行個體。</summary>
      <param name="innerConstraint">若參數並非沒有值的選擇性參數，要符合的內部常數</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.OptionalRouteConstraint.InnerConstraint">
      <summary>取得若參數並非沒有值的選擇性參數，要符合的內部常數。</summary>
      <returns>若參數並非沒有值的選擇性參數，要符合的內部常數。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.OptionalRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.RangeRouteConstraint">
      <summary>限制路由參數為給定值範圍的整數。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RangeRouteConstraint.#ctor(System.Int64,System.Int64)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.RangeRouteConstraint" /> 類別的新執行個體。</summary>
      <param name="min">最小值。</param>
      <param name="max">最大值。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RangeRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.RangeRouteConstraint.Max">
      <summary>取得路由參數的最大值。</summary>
      <returns>路由參數的最大值。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.RangeRouteConstraint.Min">
      <summary>取得路由參數的最小值。</summary>
      <returns>路由參數的最小值。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.RegexRouteConstraint">
      <summary>限制路由參數符合規則運算式。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RegexRouteConstraint.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.RegexRouteConstraint" /> 類別的新執行個體。</summary>
      <param name="pattern">模式。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RegexRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>判斷此執行個體是否等於指定的路由。</summary>
      <returns>如果這個執行個體等於指定的路由，則為 true，否則為 false。</returns>
      <param name="request">要求。</param>
      <param name="route">要比較的路由。</param>
      <param name="parameterName">參數名稱。</param>
      <param name="values">參數值清單。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.RegexRouteConstraint.Pattern">
      <summary>取得要符合的規則運算式模式。</summary>
      <returns>要符合的規則運算式模式。</returns>
    </member>
    <member name="T:System.Web.Http.Services.Decorator">
      <summary> 提供方法，用來擷取會由 &lt;see cref="T:System.Web.Http.Services.IDecorator`1" /&gt; 包裝的物件最內緣物件的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Services.Decorator.GetInner``1(``0)">
      <summary> 取得最內緣物件，其不會實作 &lt;see cref="T:System.Web.Http.Services.IDecorator`1" /&gt;。</summary>
      <param name="outer">需要未包裝的物件。</param>
      <typeparam name="T"></typeparam>
    </member>
    <member name="T:System.Web.Http.Services.DefaultServices">
      <summary>表示 <see cref="T:System.Web.Http.HttpConfiguration" /> 所用服務執行個體的容器。請注意，這個容器僅支援已知類型，而且用於取得或設定任意服務類型的方法將會在呼叫時擲回 <see cref="T:System.ArgumentException" />。若要建立任意類型，請改用 <see cref="T:System.Web.Http.Dependencies.IDependencyResolver" />。這個容器支援的類型如下：<see cref="T:System.Web.Http.Controllers.IActionValueBinder" /><see cref="T:System.Web.Http.Description.IApiExplorer" /><see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /><see cref="T:System.Web.Http.Validation.IBodyModelValidator" /><see cref="T:System.Net.Http.Formatting.IContentNegotiator" /><see cref="T:System.Web.Http.Description.IDocumentationProvider" /><see cref="T:System.Web.Http.Filters.IFilterProvider" /><see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /><see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /><see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /><see cref="T:System.Web.Http.Tracing.ITraceManager" /><see cref="T:System.Web.Http.Tracing.ITraceWriter" /><see cref="T:System.Web.Http.Query.IStructuredQueryBuilder" /><see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /><see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /><see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /><see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" />將不在此列的任何類型傳遞到此介面上的任何方法，將導致擲回 <see cref="T:System.ArgumentException" />。</summary>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Services.DefaultServices" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>利用指定的 <see cref="T:System.Web.Http.HttpConfiguration" /> 物件，初始化 <see cref="T:System.Web.Http.Services.DefaultServices" /> 類別的新執行個體。</summary>
      <param name="configuration">
        <see cref="T:System.Web.Http.HttpConfiguration" /> 物件。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ClearSingle(System.Type)">
      <summary>移除預設服務中的單一執行個體服務。</summary>
      <param name="serviceType">服務的類型。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetService(System.Type)">
      <summary>取得指定之型別的服務。</summary>
      <returns>服務的第一個執行個體，如果找不到服務，則為 null。</returns>
      <param name="serviceType">服務的型別。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetServiceInstances(System.Type)">
      <summary>取得指定之服務型別的服務物件清單，並驗證服務型別。</summary>
      <returns>指定之型別的服務物件清單。</returns>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetServices(System.Type)">
      <summary>取得指定之服務型別的服務物件清單。</summary>
      <returns>指定之型別的服務物件清單，如果找不到服務，則為空白清單。</returns>
      <param name="serviceType">服務的型別。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.IsSingleService(System.Type)">
      <summary>查詢服務型別是否為單一執行個體。</summary>
      <returns>如果服務型別至少有一個執行個體，則為 true，如果服務型別支援多個執行個體，則為 false。</returns>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ReplaceSingle(System.Type,System.Object)">
      <summary>取代單一執行個體服務物件。</summary>
      <param name="serviceType">服務型別。</param>
      <param name="service">取代先前執行個體的服務物件。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ResetCache(System.Type)">
      <summary>移除單一服務型別的快取值。</summary>
      <param name="serviceType">服務型別。</param>
    </member>
    <member name="T:System.Web.Http.Services.IDecorator`1">
      <summary> 定義公開內部裝飾物件的裝飾項目。</summary>
      <typeparam name="T">此型別參數是 Covariant。也就是說，您可以使用指定的型別或是任何更具衍生性的型別。如需關於共變數及反變數的詳細資訊，請參閱 。</typeparam>
    </member>
    <member name="P:System.Web.Http.Services.IDecorator`1.Inner">
      <summary> 取得內部物件。 </summary>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceManager">
      <summary>表示效能追蹤類別，以記錄方法進入/結束和持續期間。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceManager.Initialize(System.Web.Http.HttpConfiguration)">
      <summary>使用指定的組態初始化 <see cref="T:System.Web.Http.Tracing.ITraceManager" /> 類別。</summary>
      <param name="configuration">設定。</param>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceWriter">
      <summary>表示追蹤寫入器。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriter.Trace(System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> 如果且僅在給定的 [分類] 和 [層級] 允許追蹤時，叫用指定的 traceAction 以允許在新 <see cref="T:System.Web.Http.Tracing.TraceRecord" /> 中設定值。</summary>
      <param name="request">目前的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 null，但這麼做可預防後續追蹤分析與特定要求相互關聯。</param>
      <param name="category">追蹤的邏輯分類。使用者可自行定義。</param>
      <param name="level">要寫入此追蹤的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="traceAction">若已啟用追蹤，則要叫用的動作。呼叫端可填妥此動作中給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" /> 欄位。</param>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceWriterExtensions">
      <summary>表示 <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> 的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>提供一組有助於進行程式碼偵錯的方法和屬性，包含指定的寫入器、要求、分類和例外狀況。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>提供一組有助於進行程式碼偵錯的方法和屬性，包含指定的寫入器、要求、分類、例外狀況、訊息格式和引數。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>提供一組有助於進行程式碼偵錯的方法和屬性，包含指定的寫入器、要求、分類、例外狀況、訊息格式和引數。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>顯示清單中的錯誤訊息，包含指定的寫入器、要求、分類和例外狀況。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>顯示清單中的錯誤訊息，包含指定的寫入器、要求、分類、例外狀況、訊息格式和引數。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">例外狀況。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息中的引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>顯示清單中的錯誤訊息，包含指定的寫入器、要求、分類、訊息格式和引數。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息中的引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>顯示 <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> 類別中的錯誤訊息，包含指定的寫入器、要求、分類和例外狀況。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的例外狀況。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>顯示 <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> 類別中的錯誤訊息，包含指定的寫入器、要求、分類、例外狀況、訊息格式和引數。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">例外狀況。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>顯示 <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> 類別中的錯誤訊息，包含指定的寫入器、要求、分類及訊息格式和引數。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>在 <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" /> 中顯示詳細資料。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>在 <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" /> 中顯示詳細資料。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>在 <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" /> 中顯示詳細資料。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Exception)">
      <summary>表示 Listeners 集合中的追蹤接聽程式。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="level">追蹤層級。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Exception,System.String,System.Object[])">
      <summary>表示 Listeners 集合中的追蹤接聽程式。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="level">追蹤層級。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.Object[])">
      <summary>表示 Listeners 集合中的追蹤接聽程式。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="level">追蹤的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEnd(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Action,System.Action{System.Web.Http.Tracing.TraceRecord},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary>在特定的作業中追蹤開始和結束追蹤。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="level">追蹤的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="operatorName">執行作業的物件名稱。可能為 Null。</param>
      <param name="operationName">所執行之作業的名稱。可能為 Null。</param>
      <param name="beginTrace">要在執行作業前叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
      <param name="execute">一個 &lt;see cref="T:System.Func`1" /&gt;，會傳回將要執行作業的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="endTrace">要在成功執行作業後叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
      <param name="errorTrace">要在執行作業時遇到錯誤而叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEndAsync``1(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Func{System.Threading.Tasks.Task{``0}},System.Action{System.Web.Http.Tracing.TraceRecord,``0},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> 在特定的作業中追蹤開始和結束追蹤。</summary>
      <returns>作業所傳回的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="level">追蹤的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="operatorName">執行作業的物件名稱。可能為 Null。</param>
      <param name="operationName">所執行之作業的名稱。可能為 Null。</param>
      <param name="beginTrace">要在執行作業前叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
      <param name="execute">一個 &lt;see cref="T:System.Func`1" /&gt;，會傳回將要執行作業的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="endTrace">要在成功執行作業後叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。完成之工作的結果也會傳遞到此動作。此動作可能是 Null。</param>
      <param name="errorTrace">要在執行作業時遇到錯誤而叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task" /> 產生的結果類型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEndAsync(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Func{System.Threading.Tasks.Task},System.Action{System.Web.Http.Tracing.TraceRecord},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> 在特定的作業中追蹤開始和結束追蹤。</summary>
      <returns>作業所傳回的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="level">追蹤的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="operatorName">執行作業的物件名稱。可能為 Null。</param>
      <param name="operationName">所執行之作業的名稱。可能為 Null。</param>
      <param name="beginTrace">要在執行作業前叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
      <param name="execute">一個 &lt;see cref="T:System.Func`1" /&gt;，會傳回將要執行作業的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="endTrace">要在成功執行作業後叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
      <param name="errorTrace">要在執行作業時遇到錯誤而叫用的 <see cref="T:System.Action" />，可允許填妥給定的 <see cref="T:System.Web.Http.Tracing.TraceRecord" />。可能為 Null。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>表示執行的警告層級。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>表示執行的警告層級。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="exception">在執行期間發生的錯誤。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>表示執行的警告層級。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">具備與追蹤關聯的 <see cref="T:System.Net.Http.HttpRequestMessage" />。可能為 Null。</param>
      <param name="category">追蹤的邏輯分類。</param>
      <param name="messageFormat">訊息的格式。</param>
      <param name="messageArguments">訊息引數。</param>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceCategories">
      <summary>指定追蹤分類的列舉。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ActionCategory">
      <summary>動作分類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ControllersCategory">
      <summary>控制器分類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.FiltersCategory">
      <summary>篩選條件分類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.FormattingCategory">
      <summary>格式分類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.MessageHandlersCategory">
      <summary>訊息處理常式分類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ModelBindingCategory">
      <summary>模型繫結分類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.RequestCategory">
      <summary>要求分類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.RoutingCategory">
      <summary>路由分類。</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceKind">
      <summary>指定追蹤作業的種類。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.Begin">
      <summary>追蹤會在部分作業的開始時標記。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.End">
      <summary>追蹤會在部分作業的開始時標記。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.Trace">
      <summary>單一追蹤，不屬於開始/結束追蹤組。</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceLevel">
      <summary>指定追蹤層級的列舉。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Debug">
      <summary>用於追蹤偵錯作業的追蹤層級。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Error">
      <summary>用於錯誤追蹤的追蹤層級。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Fatal">
      <summary>用於嚴重追蹤的追蹤層級。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Info">
      <summary>用於資訊追蹤的追蹤層級。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Off">
      <summary>追蹤已停用。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Warn">
      <summary>用於警告追蹤的追蹤層級。</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceRecord">
      <summary>表示追蹤記錄。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.TraceRecord.#ctor(System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel)">
      <summary>初始化 <see cref="T:System.Web.Http.Tracing.TraceRecord" /> 類別的新執行個體。</summary>
      <param name="request">訊息要求。</param>
      <param name="category">追蹤分類。</param>
      <param name="level">追蹤層級。</param>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Category">
      <summary>取得或設定追蹤分類。</summary>
      <returns>追蹤分類。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Exception">
      <summary>取得或設定例外狀況。</summary>
      <returns>例外狀況。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Kind">
      <summary>取得或設定追蹤。</summary>
      <returns>追蹤種類。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Level">
      <summary>取得或設定追蹤層級。</summary>
      <returns>追蹤層級。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Message">
      <summary>取得或設定訊息。</summary>
      <returns>訊息。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Operation">
      <summary>取得或設定要執行的邏輯作業名稱。</summary>
      <returns>所執行的邏輯作業名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Operator">
      <summary>取得或設定執行作業之物件的邏輯名稱。</summary>
      <returns>執行作業之物件的邏輯名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Properties">
      <summary>取得選擇性的使用者定義屬性。</summary>
      <returns>選擇性的使用者定義屬性。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Request">
      <summary>取得記錄中的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</summary>
      <returns>記錄中的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.RequestId">
      <summary>從 <see cref="P:System.Web.Http.Tracing.TraceRecord.Request" /> 取得相互關聯的 ID。</summary>
      <returns>
        <see cref="P:System.Web.Http.Tracing.TraceRecord.Request" /> 中的相互關聯 ID。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Status">
      <summary>取得或設定與 <see cref="T:System.Net.Http.HttpResponseMessage" /> 相關的 <see cref="T:System.Net.HttpStatusCode" />。</summary>
      <returns>與 <see cref="T:System.Net.Http.HttpResponseMessage" /> 相關聯的 <see cref="T:System.Net.HttpStatusCode" />。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Timestamp">
      <summary>取得這個追蹤 (透過 <see cref="P:System.DateTime.UtcNow" />) 的 <see cref="T:System.DateTime" />。</summary>
      <returns>這個追蹤 (透過 <see cref="P:System.DateTime.UtcNow" />) 的 <see cref="T:System.DateTime" />。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.DefaultBodyModelValidator">
      <summary>表示用來遞迴驗證物件的類別。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.DefaultBodyModelValidator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.ShouldValidateType(System.Type)">
      <summary>決定是否應該驗證特定類型的執行個體。</summary>
      <returns>如果應該驗證類型則為 true，否則為 false。</returns>
      <param name="type">要驗證的類型。</param>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.Validate(System.Object,System.Type,System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.String)">
      <summary>決定 <paramref name="model" /> 是否有效，並將所有驗證錯誤新增至 <paramref name="actionContext" /> 的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" />。</summary>
      <returns>如果模型有效，則為 true，否則為 false。</returns>
      <param name="model">要驗證的模型。</param>
      <param name="type">用來驗證的 <see cref="T:System.Type" />。</param>
      <param name="metadataProvider">用來提供此模型中繼資料的 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" />。</param>
      <param name="actionContext">在要驗證模型中的 <see cref="T:System.Web.Http.Controllers.HttpActionContext" />。</param>
      <param name="keyPrefix">要附加至任何驗證錯誤的索引鍵的 <see cref="T:System.String" />。</param>
    </member>
    <member name="T:System.Web.Http.Validation.IBodyModelValidator">
      <summary>表示模型驗證的介面</summary>
    </member>
    <member name="M:System.Web.Http.Validation.IBodyModelValidator.Validate(System.Object,System.Type,System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.String)">
      <summary> 決定 model 是否有效，並新增任何驗證錯誤至 actionContext 的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /></summary>
      <returns>如果 model 有效，則為 true，否則為 false。</returns>
      <param name="model">要驗證的模型。</param>
      <param name="type">用來驗證的 <see cref="T:System.Type" />。</param>
      <param name="metadataProvider">用來提供此模型中繼資料的 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" />。</param>
      <param name="actionContext">在要驗證模型中的 <see cref="T:System.Web.Http.Controllers.HttpActionContext" />。</param>
      <param name="keyPrefix">要附加至任何驗證錯誤的索引鍵的 <see cref="T:System.String" />。</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelStateFormatterLogger">
      <summary>這個 <see cref="T:System.Net.Http.Formatting.IFormatterLogger" /> 會將格式器錯誤記錄到提供的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" />。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelStateFormatterLogger" /> 類別的新執行個體。</summary>
      <param name="modelState">模型狀態。</param>
      <param name="prefix">前置字元。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.LogError(System.String,System.Exception)">
      <summary>記錄指定的模型錯誤。</summary>
      <param name="errorPath">錯誤路徑。</param>
      <param name="exception">錯誤訊息。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.LogError(System.String,System.String)">
      <summary>記錄指定的模型錯誤。</summary>
      <param name="errorPath">錯誤路徑。</param>
      <param name="errorMessage">錯誤訊息。</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatedEventArgs">
      <summary>提供 <see cref="E:System.Web.Http.Validation.ModelValidationNode.Validated" /> 事件的資料。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatedEventArgs.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidatedEventArgs" /> 類別的新執行個體。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="parentNode">父節點。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatedEventArgs.ActionContext">
      <summary>取得或設定動作的內容。</summary>
      <returns>動作的內容。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatedEventArgs.ParentNode">
      <summary>取得或設定這個節點的父節點。</summary>
      <returns>這個節點的父節點。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatingEventArgs">
      <summary>提供 <see cref="E:System.Web.Http.Validation.ModelValidationNode.Validating" /> 事件的資料。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatingEventArgs.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidatingEventArgs" /> 類別的新執行個體。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="parentNode">父節點。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatingEventArgs.ActionContext">
      <summary>取得或設定動作的內容。</summary>
      <returns>動作的內容。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatingEventArgs.ParentNode">
      <summary>取得或設定這個節點的父節點。</summary>
      <returns>這個節點的父節點。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationNode">
      <summary>提供模型驗證資訊的容器。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.#ctor(System.Web.Http.Metadata.ModelMetadata,System.String)">
      <summary>使用模型中繼資料和狀態索引鍵，初始化 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 類別的新執行個體。</summary>
      <param name="modelMetadata">模型中繼資料。</param>
      <param name="modelStateKey">模型狀態索引鍵。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.#ctor(System.Web.Http.Metadata.ModelMetadata,System.String,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidationNode})">
      <summary>使用模型中繼資料、模型狀態索引鍵及子模型驗證節點，初始化 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 類別的新執行個體。</summary>
      <param name="modelMetadata">模型中繼資料。</param>
      <param name="modelStateKey">模型狀態索引鍵。</param>
      <param name="childNodes">模型子節點。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ChildNodes">
      <summary>取得或設定子節點。</summary>
      <returns>子節點。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.CombineWith(System.Web.Http.Validation.ModelValidationNode)">
      <summary>結合目前的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 執行個體與指定的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 執行個體。</summary>
      <param name="otherNode">要與目前執行個體相結合的模型驗證節點。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ModelMetadata">
      <summary>取得或設定模型中繼資料。</summary>
      <returns>模型中繼資料。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ModelStateKey">
      <summary>取得或設定模型狀態索引鍵。</summary>
      <returns>模型狀態索引鍵。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.SuppressValidation">
      <summary>取得或設定值，這個值可指出是否應該隱藏驗證。</summary>
      <returns>如果應該隱藏驗證，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.Validate(System.Web.Http.Controllers.HttpActionContext)">
      <summary>使用指定的執行內容來驗證模型。</summary>
      <param name="actionContext">動作內容。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.Validate(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>使用指定的執行內容和父節點來驗證模型。</summary>
      <param name="actionContext">動作內容。</param>
      <param name="parentNode">父節點。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ValidateAllProperties">
      <summary>取得或設定值，這個值可指出是否應該驗證模型的所有屬性。</summary>
      <returns>如果應該驗證模型的所有屬性，則為 true，如果應該略過驗證，則為 false。</returns>
    </member>
    <member name="E:System.Web.Http.Validation.ModelValidationNode.Validated">
      <summary>在已驗證模型時發生。</summary>
    </member>
    <member name="E:System.Web.Http.Validation.ModelValidationNode.Validating">
      <summary>在驗證模型時發生。</summary>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationRequiredMemberSelector">
      <summary>表示藉由檢查任何與成員相關聯的必要模型驗證程式來選取必要的成員。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationRequiredMemberSelector.#ctor(System.Web.Http.Metadata.ModelMetadataProvider,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidationRequiredMemberSelector" /> 類別的新執行個體。</summary>
      <param name="metadataProvider">中繼資料提供者。</param>
      <param name="validatorProviders">驗證程式提供者。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationRequiredMemberSelector.IsRequiredMember(System.Reflection.MemberInfo)">
      <summary>指出驗證時是否需要成員。</summary>
      <returns>如果驗證時需要成員，則為 true，否則為 false。</returns>
      <param name="member">成員。</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationResult">
      <summary>提供驗證結果的容器。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationResult.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidationResult" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationResult.MemberName">
      <summary>取得或設定成員的名稱。</summary>
      <returns>成員的名稱。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationResult.Message">
      <summary>取得或設定驗證結果訊息。</summary>
      <returns>驗證結果訊息。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidator">
      <summary>提供用於實作驗證邏輯的基底類別。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidator" /> 類別的新執行個體。</summary>
      <param name="validatorProviders">驗證程式提供者。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.GetModelValidator(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>傳回模型的複合模型驗證程式。</summary>
      <returns>模型的複合模型驗證程式。</returns>
      <param name="validatorProviders">驗證程式提供者的列舉。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidator.IsRequired">
      <summary>取得可指出是否需要模型屬性的值。</summary>
      <returns>如果需要模型屬性，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>驗證指定的物件。</summary>
      <returns>驗證結果的清單。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="container">容器。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidator.ValidatorProviders">
      <summary>取得或設定驗證程式提供者的列舉。</summary>
      <returns>驗證程式提供者的列舉。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatorProvider">
      <summary>提供模型的驗證程式清單。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>取得與此 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 相關聯的驗證程式清單。</summary>
      <returns>驗證程式清單。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="validatorProviders">驗證程式提供者。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.AssociatedValidatorProvider">
      <summary>針對實作驗證提供者的類別提供抽象類別。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.AssociatedValidatorProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetTypeDescriptor(System.Type)">
      <summary>取得指定型別的型別描述項。</summary>
      <returns>指定型別的型別描述項。</returns>
      <param name="type">驗證提供者的型別。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>使用中繼資料和驗證程式提供者，取得模型的驗證程式。</summary>
      <returns>模型的驗證程式。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="validatorProviders">驗證程式提供者的列舉。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>使用中繼資料、驗證程式提供者和屬性清單，取得模型的驗證程式。</summary>
      <returns>模型的驗證程式。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="validatorProviders">驗證程式提供者的列舉。</param>
      <param name="attributes">屬性清單</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory">
      <summary>表示建立 <see cref="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider" /> 執行個體的方法。</summary>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider">
      <summary>表示 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 的實作，其可提供適用於 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 所衍生之屬性的驗證程式。同時也可以針對實作 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的型別提供驗證程式。若要支援用戶端驗證，您可以在此類別上透過靜態方法來註冊介面卡，也可以讓您的驗證屬性實作 <see cref="T:System.Web.Http.Validation.IClientValidatable" />。支援 IClientValidatable 的邏輯實作於 <see cref="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator" /> 中。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>使用指定的中繼資料、驗證程式提供者和屬性，取得模型的驗證程式。</summary>
      <returns>模型的驗證程式。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="validatorProviders">驗證程式提供者。</param>
      <param name="attributes">屬性。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterAdapter(System.Type,System.Type)">
      <summary>註冊配接器來提供用戶端驗證。</summary>
      <param name="attributeType">驗證屬性的型別。</param>
      <param name="adapterType">配接器的型別。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterAdapterFactory(System.Type,System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory)">
      <summary>註冊驗證提供者的配接器 Factory。</summary>
      <param name="attributeType">屬性的型別。</param>
      <param name="factory">Factory，用於建立指定之屬性的 <see cref="T:System.Web.Http.Validation.ModelValidator" /> 物件。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultAdapter(System.Type)">
      <summary>註冊預設配接器。</summary>
      <param name="adapterType">配接器的型別。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultAdapterFactory(System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory)">
      <summary>註冊預設配接器 Factory。</summary>
      <param name="factory">Factory，用於建立預設配接器的 <see cref="T:System.Web.Http.Validation.ModelValidator" /> 物件。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultValidatableObjectAdapter(System.Type)">
      <summary>針對實作 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的物件，註冊預設的介面卡型別。介面卡型別必須衍生自 <see cref="T:System.Web.Http.Validation.ModelValidator" />，而且必須包含公用建構函式，此建構函式可以採用型別 <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> 和 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 的兩個參數。</summary>
      <param name="adapterType">配接器的型別。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultValidatableObjectAdapterFactory(System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory)">
      <summary>針對實作 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的物件，註冊預設的介面卡 Factory。</summary>
      <param name="factory">Factory。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterValidatableObjectAdapter(System.Type,System.Type)">
      <summary>針對必須實作 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的指定 modelType，註冊介面卡型別。介面卡型別必須衍生自 <see cref="T:System.Web.Http.Validation.ModelValidator" />，而且必須包含公用建構函式，此建構函式可以採用型別 <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> 和 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 的兩個參數。</summary>
      <param name="modelType">模型型別。</param>
      <param name="adapterType">配接器的型別。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterValidatableObjectAdapterFactory(System.Type,System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory)">
      <summary>針對必須實作 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的指定 modelType，註冊介面卡 Factory。</summary>
      <param name="modelType">模型型別。</param>
      <param name="factory">Factory。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory">
      <summary>提供以 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 為基礎之驗證程式的 Factory。</summary>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider">
      <summary>表示資料成員模型的驗證程式提供者。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>取得模型的驗證程式。</summary>
      <returns>模型的驗證程式。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="validatorProviders">驗證程式提供者的列舉值。</param>
      <param name="attributes">屬性清單。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider">
      <summary>可提供驗證程式之 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 的實作，而這些驗證程式會在模型無效時擲回例外狀況。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>取得與此 <see cref="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider" /> 相關聯的驗證程式清單。</summary>
      <returns>驗證程式清單。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="validatorProviders">驗證程式提供者。</param>
      <param name="attributes">屬性清單</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider">
      <summary>表示必要成員模型驗證程式的提供者。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider.#ctor(System.Net.Http.Formatting.IRequiredMemberSelector)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider" /> 類別的新執行個體。</summary>
      <param name="requiredMemberSelector">必要的成員選取器。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>取得成員模型的驗證程式。</summary>
      <returns>成員模型的驗證程式。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="validatorProviders">驗證程式提供者</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator">
      <summary>提供模型驗證程式。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.ComponentModel.DataAnnotations.ValidationAttribute)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator" /> 類別的新執行個體。</summary>
      <param name="validatorProviders">驗證程式提供者。</param>
      <param name="attribute">模型的驗證屬性。</param>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.Attribute">
      <summary>取得或設定模型驗證程式的驗證屬性。</summary>
      <returns>模型驗證程式的驗證屬性。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.IsRequired">
      <summary>取得值，這個值表示是否需要模型驗證。</summary>
      <returns>如果需要模型驗證，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>驗證模型並傳回驗證錯誤 (如果有的話)。</summary>
      <returns>模型的驗證錯誤訊息清單，如果未發生錯誤則為空白清單。</returns>
      <param name="metadata">模型中繼資料。</param>
      <param name="container">模型的容器。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.ErrorModelValidator">
      <summary>可代表錯誤的 <see cref="T:System.Web.Http.Validation.ModelValidator" />。不論實際模型值為何，此驗證程式永遠會擲回例外狀況。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ErrorModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.ErrorModelValidator" /> 類別的新執行個體。</summary>
      <param name="validatorProviders">模型驗證程式提供者的清單。</param>
      <param name="errorMessage">例外狀況的錯誤訊息。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ErrorModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>驗證指定的物件。</summary>
      <returns>驗證結果的清單。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="container">容器。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.RequiredMemberModelValidator">
      <summary>表示必要成員的 <see cref="T:System.Web.Http.Validation.ModelValidator" />。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.RequiredMemberModelValidator" /> 類別的新執行個體。</summary>
      <param name="validatorProviders">驗證程式提供者。</param>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.IsRequired">
      <summary>取得或設定值，這個值指示序列化引擎在驗證時必須有成員。</summary>
      <returns>如果需要成員，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>驗證物件。</summary>
      <returns>驗證結果的清單。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="container">容器。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.ValidatableObjectAdapter">
      <summary>提供可接受驗證的物件配接器。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ValidatableObjectAdapter.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.ValidatableObjectAdapter" /> 類別的新執行個體。</summary>
      <param name="validatorProviders">驗證提供者。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ValidatableObjectAdapter.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>驗證指定的物件。</summary>
      <returns>驗證結果的清單。</returns>
      <param name="metadata">中繼資料。</param>
      <param name="container">容器。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IEnumerableValueProvider">
      <summary>表示值提供者的基底類別，該值提供者的值來自實作 <see cref="T:System.Collections.IEnumerable" /> 介面的集合。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IEnumerableValueProvider.GetKeysFromPrefix(System.String)">
      <summary>從指定的 <paramref name="prefix" /> 擷取索引鍵。</summary>
      <returns>指定的 <paramref name="prefix" /> 中的索引鍵。</returns>
      <param name="prefix">前置字元。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IUriValueProviderFactory">
      <summary>表示介面，這個介面由支援 <see cref="T:System.Web.Http.ValueProviders.IValueProvider" /> 建立的任何 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 所實作，以存取傳入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Uri" />。</summary>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IValueProvider">
      <summary>定義 ASP.NET MVC 中值提供者所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IValueProvider.ContainsPrefix(System.String)">
      <summary>判斷集合是否包含指定的前置詞。</summary>
      <returns>如果此集合包含指定的前置詞，則為 true，否則為 false。</returns>
      <param name="prefix">要搜尋的前置字元。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IValueProvider.GetValue(System.String)">
      <summary>使用指定的索引鍵擷取值物件。</summary>
      <returns>指定之索引鍵的值物件，如果找不到索引鍵則為 null。</returns>
      <param name="key">要擷取之值物件的索引鍵。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderAttribute">
      <summary> 此屬性用來設定自訂的 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" />。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderAttribute" /> 的新執行個體。</summary>
      <param name="valueProviderFactory">模型繫結器的型別。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.#ctor(System.Type[])">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderAttribute" /> 的新執行個體。</summary>
      <param name="valueProviderFactories">模型繫結器類型的陣列。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary>取得值提供者 Factory。</summary>
      <returns>值提供者 Factory 的集合。</returns>
      <param name="configuration">組態物件。</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderAttribute.ValueProviderFactoryTypes">
      <summary>取得值提供者 Factory 所傳回的物件類型。</summary>
      <returns>類型集合。</returns>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderFactory">
      <summary>表示用以建立值提供者物件的 Factory。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>針對指定的控制器內容傳回值提供者物件。</summary>
      <returns>值提供者物件。</returns>
      <param name="actionContext">物件，會封裝目前 HTTP 要求之相關資訊。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderResult">
      <summary>表示將值 (例如從表單張貼或查詢字串) 繫結至動作方法引數屬性或繫結至引數本身的結果。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.#ctor(System.Object,System.String,System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> 類別的新執行個體。</summary>
      <param name="rawValue">未經處理的值。</param>
      <param name="attemptedValue">已嘗試的值。</param>
      <param name="culture">文化特性。</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.AttemptedValue">
      <summary>取得或設定轉換為顯示字串之未經處理的值。</summary>
      <returns>轉換為顯示字串之未經處理的值。</returns>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.ConvertTo(System.Type)">
      <summary>將此結果封裝的值轉換為指定的型別。</summary>
      <returns>轉換的值。</returns>
      <param name="type">目標型別。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.ConvertTo(System.Type,System.Globalization.CultureInfo)">
      <summary>使用指定的文化特性資訊，將此結果封裝的值轉換為指定的型別。</summary>
      <returns>轉換的值。</returns>
      <param name="type">目標型別。</param>
      <param name="culture">用於轉換中的文化特性。</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.Culture">
      <summary>取得或設定文化特性。</summary>
      <returns>文化特性。</returns>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.RawValue">
      <summary>取得或設定值提供者所提供之未經處理的值。</summary>
      <returns>值提供者所提供之未經處理的值。</returns>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider">
      <summary>表示值提供者，其值來自實作 <see cref="T:System.Collections.IEnumerable" /> 介面的值提供者清單。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.#ctor(System.Collections.Generic.IList{System.Web.Http.ValueProviders.IValueProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider" /> 類別的新執行個體。</summary>
      <param name="list">值提供者的清單。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.ContainsPrefix(System.String)">
      <summary>判斷集合是否包含指定的 <paramref name="prefix" />。</summary>
      <returns>如果集合包含指定的 <paramref name="prefix" />，則為 true，否則為 false。</returns>
      <param name="prefix">要搜尋的前置字元。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.GetKeysFromPrefix(System.String)">
      <summary>從指定的 <paramref name="prefix" /> 擷取索引鍵。</summary>
      <returns>指定的 <paramref name="prefix" /> 中的索引鍵。</returns>
      <param name="prefix">從中擷取索引鍵的前置詞。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.GetValue(System.String)">
      <summary>使用指定的 <paramref name="key" /> 擷取值物件。</summary>
      <returns>指定之 <paramref name="key" /> 的值物件。</returns>
      <param name="key">要擷取之值物件的索引鍵。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.InsertItem(System.Int32,System.Web.Http.ValueProviders.IValueProvider)">
      <summary>將元素插入至集合中的指定索引處。</summary>
      <param name="index">應該插入 <paramref name="item" /> 之以零起始的索引。</param>
      <param name="item">要插入的物件。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.SetItem(System.Int32,System.Web.Http.ValueProviders.IValueProvider)">
      <summary>取代指定之索引處的項目。</summary>
      <param name="index">要取代的項目之以零起始的索引。</param>
      <param name="item">指定之索引處的項目新值。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory">
      <summary>表示用於建立值提供者物件清單的 Factory。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory" /> 類別的新執行個體。</summary>
      <param name="factories">值提供者 Factory 的集合。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>針對指定的控制器內容擷取值提供者物件清單。</summary>
      <returns>指定之控制器內容的值提供者物件清單。</returns>
      <param name="actionContext">物件，會封裝目前 HTTP 要求之相關資訊。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider">
      <summary>名稱/值組的值提供者。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object},System.Globalization.CultureInfo)"></member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider" /> 類別的新執行個體。</summary>
      <param name="values">提供者的名稱/值組。</param>
      <param name="culture">名稱/值組所用的文化特性。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Func{System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}}},System.Globalization.CultureInfo)">
      <summary>使用函數委派來提供名稱/值組，初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider" /> 類別的新執行個體。</summary>
      <param name="valuesFactory">可傳回名稱/值組集合的函數委派。</param>
      <param name="culture">名稱/值組所用的文化特性。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.ContainsPrefix(System.String)">
      <summary>判斷集合是否包含指定的前置詞。</summary>
      <returns>如果此集合包含指定的前置詞，則為 true，否則為 false。</returns>
      <param name="prefix">要搜尋的前置字元。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.GetKeysFromPrefix(System.String)">
      <summary>從前置字元取得索引鍵。</summary>
      <returns>索引鍵。</returns>
      <param name="prefix">前置字元。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.GetValue(System.String)">
      <summary>使用指定的索引鍵擷取值物件。</summary>
      <returns>指定之索引鍵的值物件。</returns>
      <param name="key">要擷取之值物件的索引鍵。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider">
      <summary>表示 <see cref="T:System.Collections.Specialized.NameValueCollection" /> 物件中所含查詢字串的值提供者。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider" /> 類別的新執行個體。</summary>
      <param name="actionContext">物件，會封裝目前 HTTP 要求之相關資訊。</param>
      <param name="culture">包含目標文化特性相關資訊的物件。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory">
      <summary>表示負責建立查詢字串值提供者物件之新執行個體的類別。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>針對指定的控制器內容擷取值提供者物件。</summary>
      <returns>查詢字串值提供者物件。</returns>
      <param name="actionContext">物件，會封裝目前 HTTP 要求之相關資訊。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider">
      <summary>表示路由資料的值提供者，該路由資料包含在實作 IDictionary(Of TKey、TValue) 介面的物件內。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider" /> 類別的新執行個體。</summary>
      <param name="actionContext">包含 HTTP 要求相關資訊的物件。</param>
      <param name="culture">包含目標文化特性相關資訊的物件。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory">
      <summary>表示用以建立路由資料值提供者物件的 Factory。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>針對指定的控制器內容擷取值提供者物件。</summary>
      <returns>值提供者物件。</returns>
      <param name="actionContext">物件，會封裝目前 HTTP 要求之相關資訊。</param>
    </member>
  </members>
</doc>