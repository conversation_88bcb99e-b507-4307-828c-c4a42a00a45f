(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: lazyload (BETA) - 4/1/2016 (v2.25.7) */
!function(r,o){"use strict";var n=r.tablesorter;n.lazyload={init:function(t,e){"scrollstop"!==e.lazyload_event||n.addScrollStopDone||(n.addScrollStop(),n.addScrollStopDone=!0,r.event.special.scrollstop.latency=e.lazyload_latency||250),n.lazyload.update(t,e);var l=t.namespace+"lazyload ",a=[e.lazyload_update,"pagerUpdate",e.columnSelector_updated||"columnUpdate",""].join(l);t.$table.on(a,function(){n.lazyload.update(t,t.widgetOptions)}).on("filterEnd"+l,function(){r(o).scroll()})},update:function(t,e){var l=(/(\.|#)/.test(e.lazyload_imageClass)?"":".")+e.lazyload_imageClass;t.$table.find(l).lazyload({threshold:e.lazyload_threshold,failure_limit:e.lazyload_failure_limit,event:e.lazyload_event,effect:e.lazyload_effect,container:e.lazyload_container,data_attribute:e.lazyload_data_attribute,skip_invisible:e.lazyload_skip_invisible,appear:e.lazyload_appear,load:e.lazyload_load,placeholder:e.lazyload_placeholder}),setTimeout(function(){r(o).scroll()},1)},remove:function(t){t.$table.off(t.namespace+"lazyload")}},n.addWidget({id:"lazyload",options:{lazyload_imageClass:"lazy",lazyload_update:"lazyloadUpdate",lazyload_latency:250,lazyload_threshold:0,lazyload_failure_limit:0,lazyload_event:"scrollstop",lazyload_effect:"show",lazyload_container:o,lazyload_data_attribute:"original",lazyload_skip_invisible:!0,lazyload_appear:null,lazyload_load:null,lazyload_placeholder:"data:image/gif;base64,R0lGODlhAQABAIABAP///wAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="},init:function(t,e,l,a){n.lazyload.init(l,a)},remove:function(t,e,l){n.lazyload.remove(e,l)}}),n.addScrollStop=function(){var n=r.event.dispatch||r.event.handle,i=r.event.special,o="D"+ +new Date,l="D"+(+new Date+1);i.scrollstart={setup:function(t){var l,a=r.extend({latency:i.scrollstop.latency},t),e=function(t){var e=arguments;l?clearTimeout(l):(t.type="scrollstart",n.apply(this,e)),l=setTimeout(function(){l=null},a.latency)};r(this).bind("scroll",e).data(o,e)},teardown:function(){r(this).unbind("scroll",r(this).data(o))}},i.scrollstop={latency:250,setup:function(t){var a,o=r.extend({latency:i.scrollstop.latency},t),e=function(t){var e=this,l=arguments;a&&clearTimeout(a),a=setTimeout(function(){a=null,t.type="scrollstop",n.apply(e,l)},o.latency)};r(this).bind("scroll",e).data(l,e)},teardown:function(){r(this).unbind("scroll",r(this).data(l))}}}}(jQuery,window),
/*!
* Lazy Load - jQuery plugin for lazy loading images
*
* Copyright (c) 2007-2015 Mika Tuupola
*
* Licensed under the MIT license:
*   http://www.opensource.org/licenses/mit-license.php
*
* Project home:
*   http://www.appelsiini.net/projects/lazyload
*
* Version:  1.9.7
*
*/
function(r,a,o,d){var f=r(a);r.fn.lazyload=function(t){var e,n=this,i={threshold:0,failure_limit:0,event:"scroll",effect:"show",container:a,data_attribute:"original",skip_invisible:!1,appear:null,load:null,placeholder:"data:image/gif;base64,R0lGODlhAQABAIABAP///wAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="};function l(){var e=0;n.each(function(){var t=r(this);if(!i.skip_invisible||t.is(":visible"))if(r.abovethetop(this,i)||r.leftofbegin(this,i));else if(r.belowthefold(this,i)||r.rightoffold(this,i)){if(++e>i.failure_limit)return!1}else t.trigger("appear"),e=0})}return t&&(d!==t.failurelimit&&(t.failure_limit=t.failurelimit,delete t.failurelimit),d!==t.effectspeed&&(t.effect_speed=t.effectspeed,delete t.effectspeed),r.extend(i,t)),e=i.container===d||i.container===a?f:r(i.container),0===i.event.indexOf("scroll")&&e.bind(i.event,function(){return l()}),this.each(function(){var a=this,o=r(a);a.loaded=!1,o.attr("src")!==d&&!1!==o.attr("src")||o.is("img")&&o.attr("src",i.placeholder),o.one("appear",function(){if(!this.loaded){if(i.appear){var t=n.length;i.appear.call(a,t,i)}r("<img />").bind("load",function(){var t=o.attr("data-"+i.data_attribute);o.hide(),o.is("img")?o.attr("src",t):o.css("background-image",'url("'+t+'")'),o[i.effect](i.effect_speed),a.loaded=!0;var e=r.grep(n,function(t){return!t.loaded});if(n=r(e),i.load){var l=n.length;i.load.call(a,l,i)}}).attr("src",o.attr("data-"+i.data_attribute))}}),0!==i.event.indexOf("scroll")&&o.bind(i.event,function(){a.loaded||o.trigger("appear")})}),f.bind("resize",function(){l()}),/(?:iphone|ipod|ipad).*os 5/gi.test(navigator.appVersion)&&f.bind("pageshow",function(t){t.originalEvent&&t.originalEvent.persisted&&n.each(function(){r(this).trigger("appear")})}),r(o).ready(function(){l()}),this},r.belowthefold=function(t,e){return(e.container===d||e.container===a?(a.innerHeight?a.innerHeight:f.height())+f.scrollTop():r(e.container).offset().top+r(e.container).height())<=r(t).offset().top-e.threshold},r.rightoffold=function(t,e){return(e.container===d||e.container===a?f.width()+f.scrollLeft():r(e.container).offset().left+r(e.container).width())<=r(t).offset().left-e.threshold},r.abovethetop=function(t,e){return(e.container===d||e.container===a?f.scrollTop():r(e.container).offset().top)>=r(t).offset().top+e.threshold+r(t).height()},r.leftofbegin=function(t,e){return(e.container===d||e.container===a?f.scrollLeft():r(e.container).offset().left)>=r(t).offset().left+e.threshold+r(t).width()},r.inviewport=function(t,e){return!(r.rightoffold(t,e)||r.leftofbegin(t,e)||r.belowthefold(t,e)||r.abovethetop(t,e))},r.extend(r.expr[":"],{"below-the-fold":function(t){return r.belowthefold(t,{threshold:0})},"above-the-top":function(t){return!r.belowthefold(t,{threshold:0})},"right-of-screen":function(t){return r.rightoffold(t,{threshold:0})},"left-of-screen":function(t){return!r.rightoffold(t,{threshold:0})},"in-viewport":function(t){return r.inviewport(t,{threshold:0})},"above-the-fold":function(t){return!r.belowthefold(t,{threshold:0})},"right-of-fold":function(t){return r.rightoffold(t,{threshold:0})},"left-of-fold":function(t){return!r.rightoffold(t,{threshold:0})}})}(jQuery,window,document);return jQuery;}));
