/*
 *  /MathJax/jax/output/SVG/fonts/TeX/SansSerif/Italic/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_SansSerif-italic"],{768:[694,-527,0,-270,-87,"-262 681L-270 694H-177L-132 612Q-89 530 -87 528Q-87 527 -125 527H-163L-208 598Q-254 670 -262 681"],769:[694,-527,0,-190,63,"-96 625L-29 694H63Q42 673 -31 605L-114 527H-190L-176 541Q-160 559 -96 625"],770:[694,-527,0,-310,33,"-310 527L-140 694H-66L-16 611Q33 528 33 527H-43L-110 632L-115 639L-234 527H-310"],771:[677,-543,0,-301,60,"-170 677Q-144 676 -112 648T-54 619Q-30 619 -16 677H22Q60 677 60 675L57 666Q54 656 49 642T35 613T12 582T-20 558Q-47 543 -71 543T-130 572T-185 601Q-215 601 -225 543H-301V547Q-286 607 -249 642T-170 677"],772:[631,-552,0,-314,64,"-314 553L-297 631H-116Q64 631 64 630Q60 612 56 591L47 553L-133 552Q-314 552 -314 553"],774:[694,-508,0,-284,73,"-142 508Q-205 508 -244 548T-284 652Q-284 666 -281 683L-280 694H-204Q-205 689 -205 677Q-205 650 -196 631T-173 604T-147 593T-125 590Q-85 590 -50 618T-5 686L-2 694H73V690Q53 610 -10 559T-142 508"],775:[680,-576,0,-180,-54,"-180 578Q-179 579 -170 627T-158 678V680H-54V678Q-56 675 -65 627T-76 578V576H-180V578"],776:[680,-582,0,-273,40,"-273 584Q-272 585 -262 632L-252 678V680H-154V678L-164 632Q-174 585 -175 584Q-175 582 -224 582T-273 584ZM-78 586Q-78 587 -69 632T-58 678V680H40L39 677Q39 676 38 670T34 651T29 628L19 583L-30 582H-79L-78 586"],778:[693,-527,0,-227,-2,"-227 597Q-227 639 -186 666T-102 693H-97Q-29 693 -8 649Q-2 637 -2 623Q-2 582 -43 555T-132 527Q-171 527 -199 546T-227 597ZM-59 619Q-59 635 -68 643T-104 652Q-142 652 -156 636T-171 602Q-171 569 -123 569Q-119 569 -111 570T-99 571Q-59 582 -59 619"],779:[694,-527,0,-287,63,"-236 619L-195 694H-149Q-103 694 -103 693L-211 527H-287L-282 536Q-281 539 -236 619ZM-70 619L-29 694H17Q63 694 63 693L-45 527H-121L-116 536Q-115 539 -70 619"],780:[654,-487,0,-283,60,"-283 654H-207L-140 549L-135 542L-16 654H60L-109 487H-147L-184 488L-234 570Q-283 653 -283 654"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/SansSerif/Italic/CombDiacritMarks.js");
