/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekBoldItalic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{120604:[685,0,759,39,724],120605:[669,0,726,42,715],120606:[669,0,634,42,749],120607:[685,0,632,32,589],120608:[669,0,732,42,754],120609:[669,0,797,66,830],120610:[669,0,891,42,946],120611:[685,16,783,55,755],120612:[669,0,502,42,557],120613:[669,0,795,42,839],120614:[685,0,759,39,724],120615:[669,0,1016,42,1071],120616:[669,0,869,42,924],120617:[669,0,718,57,757],120618:[685,16,777,55,755],120619:[669,0,887,39,942],120620:[669,0,612,42,733],120621:[685,16,783,55,755],120622:[669,0,759,64,787],120623:[669,0,568,28,700],120624:[685,0,641,31,784],120625:[669,0,827,28,799],120626:[669,0,808,28,830],120627:[685,0,694,30,781],120628:[685,0,826,57,815],120629:[669,16,632,43,600],120630:[461,12,624,44,630],120631:[685,205,555,28,583],120632:[462,202,490,44,503],120633:[685,8,538,44,538],120634:[462,10,495,28,451],120635:[685,203,472,44,522],120636:[462,205,517,33,511],120637:[686,11,566,44,555],120638:[462,9,318,55,274],120639:[462,0,560,55,577],120640:[685,16,570,55,537],120641:[450,205,636,33,603],120642:[459,10,523,55,534],120643:[685,203,476,28,487],120644:[462,10,561,44,539],120645:[450,13,579,39,590],120646:[462,205,595,33,562],120647:[462,203,480,39,508],120648:[450,10,592,44,603],120649:[450,7,469,33,502],120650:[462,10,552,33,535],120651:[462,205,706,55,667],120652:[462,204,621,33,676],120653:[462,205,701,33,756],120654:[462,10,687,22,665],120655:[686,10,559,44,559],120656:[461,10,481,44,481],120657:[698,13,607,33,584],120658:[462,15,607,-12,630],120659:[685,205,683,44,655],120660:[462,205,585,44,563],120661:[450,10,868,33,879]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/GreekBoldItalic.js");
