﻿@{
    ViewBag.Title = "PortalIndex";
    Layout = "~/Views/Shared/_LayoutPortal.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    List<BET02> BET02List = ViewBag.BET02List;
}
<link href="~/Content/css/EzCss.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss")" rel="stylesheet" />
<link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />

@Html.Partial("_Notice")

<div>
    @Html.Partial("_BET02Partial")
</div>
<br />

<img src="~/Content/img/web-bar2-revise-03.png" class="img-responsive " alt="" />
<div class="row Div-EZ-purpose">
    <div class="col-md-12 Details">
        <div class="p-context">
            @{string Explain = ViewBag.ECOOLEXPLAIN;
                @Html.Raw(HttpUtility.HtmlDecode(@Explain))
            }
        </div>
    </div>
</div>
<br />
<script>
    function closewin() {
        self.window.opener = null;
        self.close();

    }
    function clock() {

        i = i - 1

        if (i > 0) {
            setTimeout("clock();", 1000);


        }
        else {
            closewin();

        }
    }
    var i = 3;
    if ("@ViewBag.access_token" == "T") {
        alert("開啟APP");
    }
   
    //$(document).ready(function () {
    //    if (window.location.protocol != 'https:') {
    //        location.href = location.href.replace("http://", "https://");
    //    }
    //});
</script>