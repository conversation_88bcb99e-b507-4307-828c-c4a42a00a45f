﻿
<div id="imagegallery">

    @model ECOOL_APP.EF.ADDT01
    @{
        ViewBag.Title = "線上投稿-詳細資料";
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

        var Search = TempData["Search"] as EcoolWeb.Models.ADDI01IndexViewModel;
    }

    <link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
    <script src="~/Content/colorbox/jquery.colorbox.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/jquery.touchwipe.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/ADDI01/details.js" nonce="cmlvaw"></script>



    @Html.Partial("_Notice")

    <img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-Pink">
        <div class="Details">

            <div class="row">
                @*<h4>學生線上投稿內容</h4>*@
                <div class="col-md-5 col-sm-5 dl-horizontal-EZ">
                    <samp class="dt">
                        投稿日期
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime")
                    </samp>
                </div>
                <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                    <samp class="dt">
                        班級
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.CLASS_NO)
                    </samp>
                </div>
                <div class="col-md-4 col-sm-4 dl-horizontal-EZ">
                    <samp class="dt">
                        姓名
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.SNAME)
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8 col-sm-12  dl-horizontal-EZ">
                    <samp class="dt">
                        文章標題
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.SUBJECT)
                    </samp>
                </div>
                <div class="col-md-4 col-sm-12  dl-horizontal-EZ">
                    <samp class="dt">
                        酷幣值
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.CASH)
                    </samp>
                </div>
            </div>

            <div style="height:15px"></div>

            <div class="row">

                @if (ViewBag.VoiceUrl != string.Empty)
            {

                    <div class="col-md-12 ">
                        @if (Request.Browser.Browser == "InternetExplorer")
                        {
                            <OBJECT ID="Player"
                                    CLASSID="CLSID:6BF52A52-394A-11d3-B153-00C04F79FAA6" width="300" height="45">
                                <PARAM name="autoStart" value="false">
                                <param name="URL" value="@ViewBag.VoiceUrl">
                            </OBJECT>
                        }
                        else
                        {
                            <audio width="300" height="48" controls="controls">
                                <source src="@ViewBag.VoiceUrl" />
                            </audio>
                        }
                    </div>

                }


                <div class="col-md-12">
                    <div class="p-context">

                        <div>
                            @if (ViewBag.ShowOriginalArticle == "V")
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(Model.ARTICLE_VERIFY))
                            }
                            else
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(Model.ARTICLE))
                            }
                        </div>
                    </div>



                </div>

                <div id="DivImg">
                    @if (ViewBag.ImageUrl != null && Enumerable.Any(ViewBag.ImageUrl))
                    {
                        foreach (var Img in ViewBag.ImageUrl as List<string>)
                        {
                            <div class="col-md-12 col-sm-12 col-xs-12 " style="text-align:center">
                                <img src='@Img' style="max-height:250px;width:auto;margin-left:auto;margin-right:auto" href="@Img" class=" img-responsive " />
                            </div>
                        }

                    }
                </div>
            </div>


            <div class="row Div-btn-center">
                <div class="col-md-12">

                    @if (ViewBag.ShowOriginalArticle == "O")
                    {
                        @Html.ActionLink("批閱後文章", "Details", new
                   {
                       WRITING_NO = Model.WRITING_NO,
                       ShowOriginal = false,
                       BackAction = Search.BackAction,
                       OrdercColumn = Search.OrdercColumn,
                       whereKeyword = Search.whereKeyword,
                       whereUserNo = Search.whereUserNo,
                       whereWritingStatus = Search.whereWritingStatus,
                       whereShareYN = Search.whereShareYN,
                       whereComment = Search.whereComment,
                       whereCommentCash = Search.whereCommentCash,
                       whereCLASS_NO = Search.whereCLASS_NO,
                       whereGrade = Search.whereGrade,
                       Page = Search.Page
                   }, new { @role = "button", @class = "btn btn-default" })
                    }
                    else if (ViewBag.ShowOriginalArticle == "V")
                    {

                        <a href="@Url.Action("Details", "ADDI01",new {
                                WRITING_NO = Model.WRITING_NO,
                                ShowOriginal = true,
                                BackAction = Search.BackAction,
                                OrdercColumn = Search.OrdercColumn,
                                whereKeyword = Search.whereKeyword,
                                whereUserNo = Search.whereUserNo,
                                whereWritingStatus = Search.whereWritingStatus,
                                whereShareYN = Search.whereShareYN,
                                whereComment = Search.whereComment,
                                whereCommentCash = Search.whereCommentCash,
                                whereCLASS_NO = Search.whereCLASS_NO,
                                whereGrade = Search.whereGrade,
                                Page = Search.Page
                            })" role="button" class="btn btn-default">
                            學生原稿
                        </a>
                    }
                </div>
            </div>


            <div class="Div-Feedback">
                <div>
                    <div class="row">
                        <div class="col-md-6 col-md-6 text-left">
                            <img src="~/Content/img/stw_fbk2.gif" />
                        </div>
                        <div class="col-md-6 col-md-6 text-right">
                            <img src="~/Content/img/stw_feedback.gif" />

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 text-left">
                            @if (string.IsNullOrWhiteSpace(Model.VERIFY_COMMENT) == false)
                            {
                                <samp>教師評語：</samp>
                                <samp style="white-space: pre-line">@Model.VERIFY_COMMENT</samp>
                            }
                        </div>
                    </div>
                    <div style="height:25px"></div>

                    @{List<ADDT02> CommentList = Model.ADDT02.Where(a => a.COMMENT_STATUS != 9).ToList();}


                    <div class="row" style="margin: 0px auto">
                        @{  int Num = 0;

                            foreach (ADDT02 comment in CommentList)
                            {
                                if (Num > 0)
                                {

                                    <div style="margin-top:20px;margin-bottom:20px;height:1px;background-color:palevioletred;"></div>

                                }

                                <div class="col-md-12 col-lg-12 p-context">
                                    <div class="col-md-8">
                                        姓名： @comment.NICK_NAME
                                    </div>
                                    <div class="col-md-4">
                                        留言日期：@comment.CRE_DATE.Value.ToShortDateString()
                                    </div>

                                    <div class="col-md-8">
                                        @if (comment.CASH > 0)
                                        {
                                            <img src="~/Content/img/icons-feedback-01.png" />
                                            <span style="color:blue">有幫助的回饋</span>
                                            <span style="color:red">+@comment.CASH.ToString()</span>
                                        }
                                    </div>
                                </div>





                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 p-context">
                                        <div style="height:14px"></div>
                                        @comment.COMMENT
                                    </div>
                                </div>

                                Num++;
                            }
                        }
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>



