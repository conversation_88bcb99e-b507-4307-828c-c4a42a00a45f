<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="EzCodeStudMap" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsp:Policy wsu:Id="BasicHttpsBinding_IEzCodeStudMap_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy>
            <sp:TransportToken>
              <wsp:Policy>
                <sp:HttpsToken RequireClientCertificate="false" />
              </wsp:Policy>
            </sp:TransportToken>
            <sp:AlgorithmSuite>
              <wsp:Policy>
                <sp:Basic256 />
              </wsp:Policy>
            </sp:AlgorithmSuite>
            <sp:Layout>
              <wsp:Policy>
                <sp:Strict />
              </wsp:Policy>
            </sp:Layout>
          </wsp:Policy>
        </sp:TransportBinding>
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="https://ecard.tp.edu.tw:9443/EzCodeStudMap/EzCodeStudMap.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="https://ecard.tp.edu.tw:9443/EzCodeStudMap/EzCodeStudMap.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="https://ecard.tp.edu.tw:9443/EzCodeStudMap/EzCodeStudMap.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/EzCodeStudMap" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IEzCodeStudMap_GetData_InputMessage">
    <wsdl:part name="parameters" element="tns:GetData" />
  </wsdl:message>
  <wsdl:message name="IEzCodeStudMap_GetData_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDataResponse" />
  </wsdl:message>
  <wsdl:portType name="IEzCodeStudMap">
    <wsdl:operation name="GetData">
      <wsdl:input wsaw:Action="http://tempuri.org/IEzCodeStudMap/GetData" message="tns:IEzCodeStudMap_GetData_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IEzCodeStudMap/GetDataResponse" message="tns:IEzCodeStudMap_GetData_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IEzCodeStudMap" type="tns:IEzCodeStudMap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetData">
      <soap:operation soapAction="http://tempuri.org/IEzCodeStudMap/GetData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="BasicHttpsBinding_IEzCodeStudMap" type="tns:IEzCodeStudMap">
    <wsp:PolicyReference URI="#BasicHttpsBinding_IEzCodeStudMap_policy" />
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetData">
      <soap:operation soapAction="http://tempuri.org/IEzCodeStudMap/GetData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="EzCodeStudMap">
   
    <wsdl:port name="BasicHttpsBinding_IEzCodeStudMap" binding="tns:BasicHttpsBinding_IEzCodeStudMap">
      <soap:address location="https://ecard.tp.edu.tw:9443/EzCodeStudMap/EzCodeStudMap.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>