﻿using System;
using System.Collections.Generic;
using ECOOL_APP.EF;
using DotNet.Highcharts;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SECI01IndexViewModel
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string NAME { get; set; }

        /// <summary>
        /// 現有酷幣點數
        /// </summary>
        public int? CASH_AVAILABLE { get; set; }
        public int? SUMCASH_AVAILABLE { get; set; }
        /// <summary>
        /// 現有定存點數
        /// </summary>
        public System.Nullable<int> CASH_DEPOSIT { get; set; }

        /// <summary>
        /// 借閱本數
        /// </summary>
        public int? BOOKS { get; set; }

        /// <summary>
        /// 借閱本數 by 月
        /// </summary>
        public int? BOOKS_MONTH { get; set; }

        /// <summary>
        /// 累計酷幣點數
        /// </summary>
        public int? CASH_ALL { get; set; }

        /// <summary>
        /// 本月給予酷幣點數 (老師用)
        /// </summary>
        public int Month_Given_Cash { get; set; }

        /// <summary>
        /// 本月給予特殊點數 (老師用)
        /// </summary>
        public String Special__Cash_Limit { get; set; }

        /// <summary>
        /// 角色娃娃 圖 路徑
        /// </summary>
        public string PlayerUrl { get; set; }

        /// <summary>
        /// 線上投稿.投稿數
        /// </summary>
        public Int32 WritingCount { get; set; }

        /// <summary>
        /// 線上投稿.推薦數
        /// </summary>
        public Int32 WritingShareCount { get; set; }

        /// <summary>
        /// 閱讀認證.投稿數
        /// </summary>
        public Int32 BookCount { get; set; }

        /// <summary>
        /// 閱讀認證.推薦數
        /// </summary>
        public Int32 BookShareCount { get; set; }

        /// <summary>
        /// 閱讀等級.等級 FOR ADDT09
        /// </summary>
        public Int32 ReadLEVEL { get; set; }

        /// <summary>
        /// 閱讀認證.等級 FOR ADDT04
        /// </summary>
        public Int32 PassportLEVEL { get; set; }

        /// <summary>
        /// 校內筆數
        /// </summary>
        public Int32 SchoolInCount { get; set; }

        /// <summary>
        /// 校外筆數
        /// </summary>
        public Int32 SchoolObcCount { get; set; }

        /// <summary>
        /// 校內外表現明細
        /// </summary>
        public List<SECI01SchoolDataViewModel> SchoolDataList;

        /// <summary>
        /// 最後投稿日 備註
        /// </summary>
        public string Last_Day_MEMO { get; set; }

        /// <summary>
        /// 閱讀等級圖示
        /// </summary>
        public Dictionary<byte, string> ReadImgURL { get; set; }

        /// <summary>
        /// 認識等級圖示
        /// </summary>
        public Dictionary<byte, string> PassportImgURL { get; set; }

        /// <summary>
        /// 我的 線上投稿 全部單號  for 電子書用
        /// </summary>
        public List<int> arrWRITING_NO;

        /// <summary>
        /// 我的 線上投稿 TOP 5 筆
        /// </summary>
        public List<ADDT01> ADDT01List;

        /// <summary>
        /// 我的 閱讀認證 全部單號  for 電子書用
        /// </summary>
        public List<int> arrAPPLY_NO;

        /// <summary>
        /// 我的 閱讀認證 TOP 5筆
        /// </summary>
        public List<ADDT06> ADDT06List;

        /// <summary>
        /// 我的 建議與鼓勵 TOP 5 筆
        /// </summary>
        public List<ADDT02> ADDT02List;

        /// <summary>
        /// 學生可兌換獎品 TOP 5 筆
        /// </summary>
        public List<AWAT02> AWAT02List;

        /// <summary>
        /// 老師可兌換獎品 TOP 5 筆
        /// </summary>
        public List<AWAT09> AWAT09List;

        // //各類別 酷幣點數，及比例
        public List<SECSharedCashPreViewModel> SumItemDescCASHList;

        // //各類別 比例 圓餅圖
        public Highcharts PreCashPieChart;

        // 各類別 點數 長條圖
        public Highcharts CashPreColumnChart;

        /// <summary>
        /// 資料角度
        /// </summary>
        public string DATA_ANGLE_TYPE { get; set; }

        /// <summary>
        /// 資料角度 中文說明
        /// </summary>
        public string DATA_TYPE_NAME { get; set; }

        public string wREF_BRE_NO { get; set; }
        public string wUSER_NO { get; set; }

        public string wSCHOOL_NO { get; set; }

        public string wPRINT { get; set; }
        public string wCLASS_NO { get; set; }
        public string wGrade_NO { get; set; }
        /// <summary>
        /// 可看舊學校 Hmto1.USER_STATUS ='9'
        /// </summary>
        public bool wIsQhisSchool { get; set; }
    }

    public class SECI01SchoolDataViewModel
    {
        public string USER_NO { get; set; }

        public string CLASS_NO { get; set; }

        public string SYS_TABLE { get; set; }

        public int NO { get; set; }

        public Nullable<System.DateTime> CRE_DATE { get; set; }

        public string CONTENT_TXT { get; set; }

        public Nullable<int> CASH { get; set; }
    }
}