﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public class AWA005QueryViewModel
    {
         /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }
        public DateTime? whereLOG_TIME_S { get; set; }
        public DateTime? whereLOG_TIME_E { get; set; }
        public string whereGrade { get; set; }
        public string whereCLASS_NO { get; set; }
        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<HRMT01QTY> VAWA005List;

        public AWA005QueryViewModel()
        {
            Page = 0;
            OrdercColumn = "CASH_AVAILABLE";
        }
    }
}