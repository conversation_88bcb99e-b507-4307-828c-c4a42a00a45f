﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class PushQueryModel
    {

        public string BATCH_ID { get; set; }

        ///Summary
        ///DEVICE_TOKEN
        ///Summary
        [DisplayName("DEVICE_TOKEN")]
        public string DEVICE_TOKEN { get; set; }


        ///Summary
        ///NOTIFICATION_ID
        ///Summary
        [DisplayName("NOTIFICATION_ID")]
        public int? NOTIFICATION_ID { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("SCHOOL_NO")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("USER_NO")]
        public string USER_NO { get; set; }
        

        ///Summary
        ///F_DATE
        ///Summary
        [DisplayName("F_DATE")]
        public DateTime? F_DATE { get; set; }

        ///Summary
        ///REF_SOU_BRE_NO
        ///Summary
        [DisplayName("REF_SOU_BRE_NO")]
        public string REF_SOU_BRE_NO { get; set; }

        ///Summary
        ///REF_SOU_ITEM
        ///Summary
        [DisplayName("REF_SOU_ITEM")]
        public string REF_SOU_ITEM { get; set; }

        ///Summary
        ///STATUS
        ///Summary
        [DisplayName("STATUS")]
        public string STATUS { get; set; }


        public string KeyMapPath { get; set; }

    }


    public class APPT03QueryModel
    {

        public string REF_TABLE  { get; set; }
        public  string REF_KEY  { get; set; }
        public string CRE_PERSON  { get; set; }
        public string SCHOOL_NO { get; set; }
        public string USER_NO { get; set; }

        public string [] ArrUSER_TYPE { get; set; }

        public byte STATUS { get; set; }

}





    public class PushSaveModel
    {

        public string BATCH_ID { get; set; }

        ///Summary
        ///DEVICE_TOKEN
        ///Summary
        [DisplayName("DEVICE_TOKEN")]
        public string DEVICE_TOKEN { get; set; }


        ///Summary
        ///NOTIFICATION_ID
        ///Summary
        [DisplayName("NOTIFICATION_ID")]
        public int? NOTIFICATION_ID { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("SCHOOL_NO")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("USER_NO")]
        public string USER_NO { get; set; }

        ///Summary
        ///TITLE_TXT
        ///Summary
        [DisplayName("TITLE_TXT")]
        public string TITLE_TXT { get; set; }

        ///Summary
        ///BODY_TXT
        ///Summary
        [DisplayName("BODY_TXT")]
        public string BODY_TXT { get; set; }

        ///Summary
        ///SOUND
        ///Summary
        [DisplayName("SOUND")]
        public string SOUND { get; set; }


        public int BADGE { get; set; }
        ///Summary
        ///F_DATE
        ///Summary
        [DisplayName("F_DATE")]
        public DateTime? F_DATE { get; set; }


        ///Summary
        ///CRE_PERSON
        ///Summary
        [DisplayName("CRE_PERSON")]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///REF_SOU_BRE_NO
        ///Summary
        [DisplayName("REF_SOU_BRE_NO")]
        public string REF_SOU_BRE_NO { get; set; }

        ///Summary
        ///REF_SOU_ITEM
        ///Summary
        [DisplayName("REF_SOU_ITEM")]
        public string REF_SOU_ITEM { get; set; }

        public string REF_SOU_KEY { get; set; }
        
        ///Summary
        ///TO_PATH
        ///Summary
        [DisplayName("TO_PATH")]
        public string TO_PATH { get; set; }

        ///Summary
        ///STATUS
        ///Summary
        [DisplayName("STATUS")]
        public string STATUS { get; set; }


        public bool RightAway { get; set; }

    }
}
