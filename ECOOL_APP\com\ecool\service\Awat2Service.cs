﻿using System;
using System.Collections.Generic;
using System.Collections;
using System.Data;
using ECOOL_APP.com.ecool.Models.dao;
using System.Text;
using System.Data.SqlClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.EntityClient;
using System.Reflection;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;

namespace com.ecool.service
{
    public class Awat2Service : ServiceBase
    {
        /// <summary>
        /// 取得老師酷幣點數
        /// </summary>
        //public int GetCash(string ParamSCHOOL_NO, string ParamUSER_NO)
        //{
        //    string sql = @" SELECT CASH_AVAILABLE " +
        //        " FROM AWAT08 " +
        //        " WHERE SCHOOL_NO = " + ParamSCHOOL_NO +
        //        " AND USER_NO = " + ParamUSER_NO;
        //    return (int)new sqlConnection.sqlConnection().executeQueryByDataTableList(sql).Rows[0]["CASH_AVAILABLE"];
        //}

        ///// <summary>
        ///// 產品查詢
        ///// </summary>
        //public List<Hashtable> USP_AWAT09_QUERY(string ParamAWARD_NO)
        //{
        //    List<Hashtable> list_data = new List<Hashtable>();

        //    try
        //    {
        //        if (ParamAWARD_NO != "")
        //            ParamAWARD_NO = "and AWARD_NO = '" + ParamAWARD_NO + "'";
        //        list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS, AWARD_TYPE, HOT_YN from AWAT09 Where AWARD_STATUS <>'n' AND (SDATETIME <= getdate()) AND (EDATETIME >= getdate()) " + ParamAWARD_NO + " ORDER BY COST_CASH DESC");
        //        //以下是原本的sql語法
        //        //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS from AWAT02 WHERE (AWARD_STATUS = 'y' or AWARD_STATUS = '0') AND (SDATETIME <= getdate()) AND (EDATETIME >= getdate()) " + ParamAWARD_NO + " ORDER BY COST_CASH DESC");

        //        //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList(sql);
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //    return list_data;
        //}

        ///// <summary>
        ///// 產品查詢
        ///// </summary>
        //public List<Hashtable> USP_AWAT09_QUERY(string ParamAWARD_NO, string ParamSchool_NO)
        //{
        //    List<Hashtable> list_data = new List<Hashtable>();

        //    try
        //    {
        //        if (ParamAWARD_NO != "")
        //            ParamAWARD_NO = "and AWARD_NO = '" + ParamAWARD_NO + "'";
        //        list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS from AWAT09 WHERE (AWARD_STATUS = 'y' or AWARD_STATUS = '0') AND (SDATETIME <= getdate()) AND (EDATETIME >= getdate()) " + ParamAWARD_NO + "School_NO = " + ParamSchool_NO + "  ORDER BY COST_CASH DESC");

        //        //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList(sql);
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //    return list_data;
        //}

        ///// <summary>
        ///// 產品查詢
        ///// </summary>
        //public List<Hashtable> USP_AWAT09_QUERY(string ParamAWARD_NO, string ParamSchool_NO, string MODE)
        //{
        //    List<Hashtable> list_data = new List<Hashtable>();

        //    try
        //    {
        //        if (ParamAWARD_NO != "")
        //            ParamAWARD_NO = "and AWARD_NO = '" + ParamAWARD_NO + "'";
        //        if (ParamSchool_NO != "")
        //            ParamSchool_NO = "and School_NO = '" + ParamSchool_NO + "'";
        //        list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT '" + MODE + "' AS MODE,AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS, AWARD_TYPE, HOT_YN,QTY_LIMIT,SHOW_DESCRIPTION_YN from AWAT09 Where AWARD_STATUS <>'n' AND  (EDATETIME >= getdate())  " + ParamAWARD_NO + ParamSchool_NO + " ORDER BY COST_CASH DESC");
        //        //以下是原本的sql語法
        //        //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS from AWAT02 WHERE (AWARD_STATUS = 'y' or AWARD_STATUS = '0') AND (SDATETIME <= getdate()) AND (EDATETIME >= getdate()) " + ParamAWARD_NO + " ORDER BY COST_CASH DESC");

        //        //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList(sql);
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //    return list_data;
        //}

        ///// <summary>
        ///// 產品查詢
        ///// </summary>
        //public List<Hashtable> USP_AWAT09UNDERProduct_QUERY(string ParamAWARD_NO, string ParamSchool_NO, string MODE)
        //{
        //    List<Hashtable> list_data = new List<Hashtable>();

        //    try
        //    {
        //        if (ParamAWARD_NO != "")
        //            ParamAWARD_NO = "and AWARD_NO = '" + ParamAWARD_NO + "'";
        //        if (ParamSchool_NO != "")
        //            ParamSchool_NO = "and School_NO = '" + ParamSchool_NO + "'";
        //        list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT '" + MODE + "' AS MODE,AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS, AWARD_TYPE, HOT_YN,QTY_LIMIT,SHOW_DESCRIPTION_YN from AWAT09 Where (AWARD_STATUS ='n' or EDATETIME < getdate()) " + ParamAWARD_NO + ParamSchool_NO + " ORDER BY COST_CASH DESC");
        //        //以下是原本的sql語法
        //        //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS from AWAT02 WHERE (AWARD_STATUS = 'y' or AWARD_STATUS = '0') AND (SDATETIME <= getdate()) AND (EDATETIME >= getdate()) " + ParamAWARD_NO + " ORDER BY COST_CASH DESC");

        //        //list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList(sql);
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //    return list_data;
        //}

        ///// <summary>
        ///// 產品新增
        ///// </summary>
        //public void USP_AWAT09_INSERT(string ParamAWARD_NAME, string ParamCOST_CASH, string ParamQTY_STORAGE, string ParamSDATETIME, string ParamSH, string ParamSM, string ParamEDATETIME, string ParamEH, string ParamEM, string ParamDESCRIPTION, string ParamIMG_FILE, string ParamSCHOOL_NO)
        //{
        //    try
        //    {
        //        new sqlConnection.sqlConnection().execute("INSERT INTO AWAT09(AWARD_NAME, COST_CASH, QTY_STORAGE, QTY_TRANS, SDATETIME, EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS, SCHOOL_NO) VALUES ('" + ParamAWARD_NAME + "', '" + ParamCOST_CASH + "', '" + ParamQTY_STORAGE + "', '0', '" + ParamSDATETIME + " " + ParamSH + ":" + ParamSM + "', '" + ParamEDATETIME + " " + ParamEH + ":" + ParamEM + "', '" + ParamDESCRIPTION + "', '" + ParamIMG_FILE + "', '0','" + ParamSCHOOL_NO + "')");
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }

        //}

        ///// <summary>
        ///// 產品修改
        ///// </summary>
        ////public string USP_AWAT09_UPDATE(string ParamAWARD_NAME, string ParamCOST_CASH, string ParamQTY_STORAGE, string ParamSDATETIME, string ParamEDATETIME, string ParamDESCRIPTION, string ParamIMG_FILE, string ParamAWARD_NO, string ParamAWARD_TYPE, string ParamAWARD_STATUS, string ParamHOT_YN)
        ////{
        ////    try
        ////    {
        ////        Awat2SPDAO USP = new Awat2SPDAO();
        ////        return USP.AWATU01(ParamAWARD_NAME, ParamCOST_CASH, ParamQTY_STORAGE, ParamSDATETIME,
        ////            ParamEDATETIME, ParamDESCRIPTION, ParamIMG_FILE, ParamAWARD_NO, ParamAWARD_TYPE, ParamAWARD_STATUS, ParamHOT_YN);

        ////    }
        ////    catch (Exception exception)
        ////    {
        ////        throw exception;
        ////    }
        ////}

        ///// <summary>
        ///// 產品刪除
        ///// </summary>
        ///// <param name="ParamAWARD_NO">商品編號</param>
        //public string USP_AWAT09_DELETE(string ParamAWARD_NO)
        //{
        //    try
        //    {
        //        Awat2SPDAO USP = new Awat2SPDAO();
        //        return USP.AWATD02(ParamAWARD_NO);
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }

        //}

        ///// <summary>
        ///// 兌換商品
        ///// </summary>
        //public string USP_AWAT09_EXCHANGE_START(int ParamAWARD_NO, string ParamSCHOOL_NO, string ParamUSERID)
        //{
        //    try
        //    {
        //        Awat2SPDAO USP = new Awat2SPDAO();
        //        return USP.AWATE02(ParamAWARD_NO, ParamSCHOOL_NO, ParamUSERID);
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //}

        ///// <summary>
        ///// 取消兌換商品
        ///// </summary>
        ////public string USP_AWAT03_EXCHANGE_START(int ParamAWARD_NO, string ParamSCHOOL_NO, string ParamUSERID)
        ////{
        ////    try
        ////    {
        ////        Awat2SPDAO USP = new Awat2SPDAO();
        ////        return USP.AWATE03(ParamAWARD_NO, ParamSCHOOL_NO, ParamUSERID);
        ////    }
        ////    catch (Exception exception)
        ////    {
        ////        throw exception;
        ////    }
        ////}

        ///// <summary>
        ///// 酷幣點數 更新
        ///// </summary>
        //public string USP_AWAT08_UPDATE(int SCHOOL_NO, string USER_NO, int CoolCash)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        StringBuilder sb = new StringBuilder();
        //        sb.Append(" UPDATE AWAT08 ");
        //        sb.Append(" SET ");
        //        if (CoolCash != 0)
        //        {
        //            sb.AppendFormat(" CASH_ALL = CASH_ALL + {0},CASH_AVAILABLE = CASH_AVAILABLE + {0} ,CASH_WORKHARD = CASH_WORKHARD + {0} ", CoolCash);
        //        }
        //        sb.AppendFormat(" WHERE SCHOOL_NO = '{0}' AND USER_NO = '{1}' ", SCHOOL_NO, USER_NO);

        //        new sqlConnection.sqlConnection().execute(sb.ToString());
        //        strMsg = "酷幣新增成功";
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //    return strMsg;
        //}

        ///// <summary>
        ///// 酷幣點數紀錄
        ///// </summary>
        //public string USP_AWAT08_LOG_INSERT(int SCHOOL_NO, string USER_NO, string SOURCE_TYPE, int APPLY_NO, int CASH_IN, string LOG_DESC)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        StringBuilder sb = new StringBuilder();
        //        sb.Append(" INSERT INTO AWAT08_LOG(SCHOOL_NO,USER_NO,SOURCE_TYPE,SOURCE_NO,CASH_IN,LOG_TIME,LOG_DESC) ");
        //        sb.AppendFormat(" VALUES('{0}','{1}','{2}',{3},{4},GETDATE(),'{5}') ", SCHOOL_NO, USER_NO, SOURCE_TYPE, APPLY_NO, CASH_IN, LOG_DESC);

        //        new sqlConnection.sqlConnection().execute(sb.ToString());
        //        strMsg = "酷幣新增成功";
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //    return strMsg;
        //}

        ///// <summary>
        ///// 酷幣點數紀錄
        ///// </summary>
        //public string USP_AWAT08_LOG_INSERT(int SCHOOL_NO, string USER_NO, string SOURCE_TYPE, long APPLY_NO, int CASH_IN, string LOG_DESC)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        StringBuilder sb = new StringBuilder();
        //        sb.Append(" INSERT INTO AWAT08_LOG(SCHOOL_NO,USER_NO,SOURCE_TYPE,SOURCE_NO,CASH_IN,LOG_TIME,LOG_DESC) ");
        //        sb.AppendFormat(" VALUES('{0}','{1}','{2}',{3},{4},GETDATE(),'{5}') ", SCHOOL_NO, USER_NO, SOURCE_TYPE, APPLY_NO, CASH_IN, LOG_DESC);

        //        new sqlConnection.sqlConnection().execute(sb.ToString());
        //        strMsg = "酷幣新增成功";
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //    return strMsg;
        //}

        /// <summary>
        /// 取得 AWAT05 酷幣來源對照檔資料
        /// </summary>
        /// <param name="SOURCE_TYPE"></param>
        /// <param name="SOURCE_NO"></param>
        /// <returns></returns>
        //public uAWAT05 GetSouData(string SOURCE_TABLE, int SOURCE_ITEM)
        //{
        //    uAWAT05 ReturnData = new uAWAT05();


        //    StringBuilder sb = new StringBuilder();
        //    DataTable dt;
        //    try
        //    {

        //        sb.Append("   SELECT a.SOURCE_TABLE,a.SOURCE_ITEM,a.SOURCE_KEY,a.SOURCE_DESC,a.TO_CASG,a.MEMO ");
        //        sb.Append("   FROM AWAT05 a (nolock) ");
        //        sb.Append("   Where 1=1 ");
        //        sb.AppendFormat(" AND A.SOURCE_TABLE ='{0}' ", SOURCE_TABLE);
        //        sb.AppendFormat(" AND A.SOURCE_ITEM ={0} ", SOURCE_ITEM);

        //        dt = new sqlConnection.sqlConnection().SysDbexecuteQueryByDataTableList(sb.ToString());

        //        foreach (DataRow dr in dt.Rows)
        //        {

        //            ReturnData.SOURCE_TABLE = (dr["SOURCE_TABLE"] == DBNull.Value ? "" : (string)dr["SOURCE_TABLE"]);
        //            ReturnData.SOURCE_ITEM = (dr["SOURCE_ITEM"] == DBNull.Value ? (int?)null : (int)dr["SOURCE_ITEM"]);
        //            ReturnData.SOURCE_KEY = (dr["SOURCE_KEY"] == DBNull.Value ? "" : (string)dr["SOURCE_KEY"]);
        //            ReturnData.SOURCE_DESC = (dr["SOURCE_DESC"] == DBNull.Value ? "" : (string)dr["SOURCE_DESC"]);
        //            ReturnData.TO_CASG = (dr["TO_CASG"] == DBNull.Value ? (int?)null : (int)dr["TO_CASG"]);
        //            ReturnData.MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]);
        //        }

        //        dt.Clear();
        //        dt.Dispose();

        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }

        //    return ReturnData;

        //}

    }
}
