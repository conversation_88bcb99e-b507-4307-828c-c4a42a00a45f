﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDStatus
    {
        /// <summary>
        ///  0:草稿 , 1: 教師批閱通過 ,2:轉批閱 ,8:退回 9: 作廢
        /// </summary>
        [DefaultValue(Draft)]
        public enum eADDT01Status : byte
        {
            /// <summary>
            /// 草稿 (未發送)
            /// </summary>
            DraftUnSubmit = 3,

            /// <summary>
            /// 草稿 (已發送等待批閱)
            /// </summary>
            Draft = 0,

            /// <summary>
            /// 教師批閱通過
            /// </summary>
            Verified = 1,

            /// <summary>
            /// 轉批閱
            /// </summary>
            TurnVerify = 2,

            /// <summary>
            /// 退回
            /// </summary>
            Back = 8,

            /// <summary>
            /// 作廢
            /// </summary>
            Disable = 9
        }

        static public string GetADDT01StatusString(byte status)
        {
            if (status == (byte)eADDT01Status.Draft || status == (byte)eADDT01Status.TurnVerify)
                return "批閱中";
            else if (status == (byte)eADDT01Status.Verified)
                return "批閱通過";
            else if (status == (byte)eADDT01Status.Back)
                return "退回";
            else if (status == (byte)eADDT01Status.Disable)
                return "作廢";
            else if (status == (byte)eADDT01Status.DraftUnSubmit)
                return "草稿";
            else
                return "";
        }

        static public string GetADDT06StatusString(string status)
        {
            if (status == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify)
                return "批閱中";
            else if (status == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass || status == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_oPass_X)
                return "批閱通過";
            else if (status == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back)
                return "退回";
            else if (status == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL)
                return "作廢";
            else if (status == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave)
                return "暫存草稿";
            else
                return "";
        }
    }
}