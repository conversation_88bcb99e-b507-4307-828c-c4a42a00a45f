﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace ECOOL_APP.EF
{
    internal class NoLockQuery<TResult> : IQuery<TResult>
    {
        public NoLockQuery(IQuery<TResult> parent)
        {
            Parent = parent;
        }

        public TResult Execute()
        {
            using (var scope = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions
            {
                IsolationLevel = IsolationLevel.ReadUncommitted
            }))
            {
                var result = Parent.Execute();
                scope.Complete();
                return result;
            }
        }

        private IQuery<TResult> Parent { get; }
    }
}