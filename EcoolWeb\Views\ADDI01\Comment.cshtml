﻿@model EcoolWeb.Models.ADDI01CommentViewModel
@{
    ViewBag.Title = "線上投稿-建議與鼓勵";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")

@using (Html.BeginForm("Comment", "ADDI01", FormMethod.Post, new { WRITING_NO = "ADDI01" }))
{
    var Search = TempData["Search"] as EcoolWeb.Models.ADDI01IndexViewModel;

    @Html.Hidden("BackAction", Search.BackAction)
    @Html.Hidden("OrdercColumn", Search.OrdercColumn)
    @Html.Hidden("whereKeyword", Search.whereKeyword)
    @Html.Hidden("whereUserNo", Search.whereUserNo)
    @Html.Hidden("whereWritingStatus", Search.whereWritingStatus)
    @Html.Hidden("whereShareYN", Search.whereShareYN)
    @Html.Hidden("whereComment", Search.whereComment)
    @Html.Hidden("whereCommentCash", Search.whereCommentCash)
    @Html.Hidden("whereCLASS_NO", Search.whereCLASS_NO)
    @Html.Hidden("whereGrade", Search.whereGrade)
    @Html.Hidden("Page", Search.Page)


    @Html.AntiForgeryToken()
    <img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Pink">
        <div class="form-horizontal" style="left:0px">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.HiddenFor(model => model.WRITING_NO)
            <div style="margin:10px">
                <span style="color:blue">
                    建議小朋友先登入網站對投稿文章提供建議與回饋，被作者或老師推薦為有幫助的回饋，將可獲得 1 點酷幣鼓勵！
                </span>
                <span style="color:red">
                    網路上公然侮辱或者毀謗他人涉及刑法第309條的公然侮辱罪及第310條的毀謗罪，司法機關有追訴犯罪的義務！
                </span>
            </div>

            <div class="col-md-offset-6 col-md-4">
                <img src="~/Content/img/stw_feedback.gif" />
            </div>
            <hr />
            <div class="form-group">
                @Html.Label("編輯日期", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-10" style="padding-top: 7px;">
                    @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime", new { @class = "control-label" })
                    @Html.HiddenFor(model => model.CRE_DATE)
                    <input type="hidden" name="Question1" value=@Model.Question1 />
                    <input type="hidden" name="Question2" value=@Model.Question2 />
                </div>
            </div>

            <div class="form-group">
                @Html.Label("通關數字", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })



                <div class="col-md-2 col-sm-3" style="padding-top: 14px;">

                    @Model.Question1 Ｘ @Model.Question2 ＝
                </div>
                <div class="col-md-3 col-sm-3" style="padding-top: 14px;">
                    @Html.EditorFor(model => model.UserAnswer, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.UserAnswer, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.Label(" ", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <font style="color:red">※為杜絕廣告張貼問題，請填寫下列九九乘法的結果才能回應！</font>

            </div>

                <div class="form-group">
                    @Html.Label("訪客姓名", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                    <div class="col-md-9  col-sm-6">
                        @if (user == null)
                        {
                            @Html.EditorFor(model => model.NICK_NAME, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.NICK_NAME, "", new { @class = "text-danger" })
                        }
                        else
                        {
                            @Html.DisplayFor(model => model.NICK_NAME, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.HiddenFor(model => model.NICK_NAME, new { htmlAttributes = new { @class = "form-control" } })
                        }

                    </div>
                </div>

                <div class="form-group">
                    @Html.Label("詳細內容", htmlAttributes: new { @class = "control-label  col-md-2 col-sm-3" })

                    <div class="col-md-9  col-sm-6">
                        @Html.TextAreaFor(model => model.UserComment, new { cols = "200", rows = "5", @class = "form-control", @placeholder = "1000字以內" })
                        @Html.ValidationMessageFor(model => model.UserComment, "", new { @class = "text-danger" })
                    </div>

                </div>
                @foreach (var key in Model.Emoticon_Dictionary.Keys)
                {
                    @Html.HiddenFor(model => model.Emoticon_Dictionary[key]);
                }
                <div class="form-group">
                    @Html.Label("選擇一個貼圖", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                    <div class="col-md-9  col-sm-6">
                        <div class="row">
                            @foreach (var item in Model.Emoticon_Dictionary)
                            {
                                @Html.RadioWithImageButtonFor(model => model.EMOTICON, item.Value, item.Key)
                            }
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-offset-3 col-md-3">
                        <button value="Save" class="btn btn-default">
                            確定送出
                        </button>

                    </div>
                    <div class="col-md-offset-1 col-md-3">
                        <a href='@Url.Action("Details", "ADDI01"
                        , new {
                            WRITING_NO = Model.WRITING_NO,
                            BackAction = Search.BackAction,
                            OrdercColumn = Search.OrdercColumn,
                            whereKeyword = Search.whereKeyword,
                            whereUserNo = Search.whereUserNo,
                            whereWritingStatus = Search.whereWritingStatus,
                            whereShareYN = Search.whereShareYN,
                            whereComment = Search.whereComment,
                            whereCommentCash = Search.whereCommentCash,
                            whereCLASS_NO = Search.whereCLASS_NO,
                            whereGrade = Search.whereGrade,
                            Page = Search.Page
                        })' role="button" class="btn btn-default">
                            放棄編輯
                        </a>

                    </div>
                </div>

            </div>
        </div>
}



