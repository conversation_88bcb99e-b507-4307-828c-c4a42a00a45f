﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI02IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

}

<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>
<br />

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1" }))
{

    @Html.Partial("_Title_Secondary")

    Html.RenderAction("_Menu", new { NowAction = "Index" });

    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("SCHOOL_NO", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", @onchange = "SelectSCHOOL_NO(this.value)" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("CLASS_NO", (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeUSER_NOUseReplaceWith()" })
        </div>
        <div class="form-group">
            <label class="control-label">姓名</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("USER_NO", (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

    <br />

    <div class="form-inline">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔(Html)</button>
        </div>
    </div>

}
<br />

<div id="tbData">
    <div class="row">
        <div class="col-sm-4">
            @if (Model.SumCashBarChart != null)
            {
                @Model.SumCashBarChart
            }
        </div>
        <div class="col-sm-8">
            @if (Model.PreCashPieChart != null)
            {
                @Model.PreCashPieChart
            }
        </div>
    </div>
    <div style="height:10px"></div>
    <div class="col-sm-12">
        @if (Model.CashPreColumnChart != null)
        {
            @Model.CashPreColumnChart
        }
    </div>
    <div style="height:10px"></div>
    <div class="col-sm-12">
        <div class="panel table-responsive">
            <table class="table table-striped table-hover">
                <caption><strong style="font-size:18px">各類別酷幣統計表(在校生)</strong></caption>
                <thead>
                    <tr>
                        <th>

                            類別
                        </th>
                        <th style="text-align: center">

                            酷幣
                        </th>
                        <th style="text-align: center">
                            佔比
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.PerData)
                    {
                        <tr>
                            <td>
                                @Html.Raw(HttpUtility.HtmlDecode(item.LOG_DESC))
                            </td>

                            <td align="right">
                                @item.SUM_ADD_CASH_ALL.Value.ToString("#,#")
                            </td>
                            <td align="right">
                                @(item.PRE.ToString("#,#0.0"))%
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<script language="javascript">

    var targetFormID = '#form1';

       window.onload = function () {
            ChangeUSER_NOUseReplaceWith()
    }

    function PrintBooK()
    {
        $('#tbData').printThis();
    }

    function SelectSCHOOL_NO(Val)
    {
        if (Val == '@SharedGlobal.ALL') {
            $('#CLASS_NO').val('').change();
            $('#USER_NO').val('').change();
            $('#CLASS_NO').attr('disabled', 'disabled');
            $('#USER_NO').attr('disabled', 'disabled');
        }
        else {
            $('#CLASS_NO').val('').change();
            $('#USER_NO').val('').change();
            $('#CLASS_NO').removeAttr('disabled');
            $('#USER_NO').removeAttr('disabled');
        }
        var selectedCLASS_NO = $.trim($('#CLASS_NO option:selected').val());
        if (selectedCLASS_NO.length == 0) {
            SetUSER_NODDLEmpty();
        }

        form1.submit();
    }

    function todoClear() {
        ////重設
        $("#form1").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }

    function SetUSER_NODDLEmpty() {
        $('#USER_NO').empty();
        $('#USER_NO').append($('<option></option>').val(' ').text('全部' + $("label[for='Search_CLASS_NO']").text() + '...').prop('selected', true));
    }

    function ChangeUSER_NOUseReplaceWith() {

        var selectedSCHOOL_NO = $.trim($('#SCHOOL_NO option:selected').val());
        var selectedCLASS_NO = $.trim($('#CLASS_NO option:selected').val());
        var selectedUSER_NO = $.trim($('#USER_NO option:selected').val());

        if (selectedSCHOOL_NO == '@SharedGlobal.ALL') {
            $('#CLASS_NO').val('')
            $('#USER_NO').val('')
            $('#CLASS_NO').attr('disabled', 'disabled');
            $('#USER_NO').attr('disabled', 'disabled');
        }
        else {
            $('#CLASS_NO').removeAttr('disabled');
            $('#USER_NO').removeAttr('disabled');
        }

        if (selectedCLASS_NO.length == 0) {
            SetUSER_NODDLEmpty();
        }
        else {
            $.ajax(
            {
                url: '@Url.Action("_GetUSER_NODDLHtml", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: 'USER_NO',
                        tagName: 'USER_NO',
                        SCHOOL_NO: selectedSCHOOL_NO,
                        CLASS_NO: selectedCLASS_NO,
                        USER_NO: selectedUSER_NO
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        console.log(data);
                        if (data.length > 0) {
                            data= data.replace("請選擇", "全部");
                            $('#USER_NO').replaceWith(data);
                        }
                    }
                });
        }
    }

    function fn_save() {
        var blob = new Blob([document.getElementById('tbData').innerHTML], {
            type: "html/plain;charset=utf-8"
        });
        var strFile = "Report.html";
        saveAs(blob, strFile);
        return false;
    }
</script>