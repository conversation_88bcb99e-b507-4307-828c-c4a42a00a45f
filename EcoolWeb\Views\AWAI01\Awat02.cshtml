﻿@model AWAI01EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title; ;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("ActionSaveAwat", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "contentForm", name = "contentForm", enctype = "multipart/form-data" }))
{
    <img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Awat02" id="showView">
       
        @Html.Partial("_Awat02Form",Model)
      
            <div class="row Div-btn-center">
                <div class="col-md-offset-3 col-md-3">
                    <input type="submit" value="新增獎品" class="btn btn-default" onclick="doInsert();" />
                </div>
                <div class="col-md-offset-1 col-md-3">
                    <input type="button" value="返回" class="btn btn-default" onclick="goBack();" />
                </div>
            </div>
       

    </div>
}


@section Scripts
{
    <script type="text/javascript">
        var targetFormID = '#contentForm';

        function goBack() {
            $(targetFormID).attr("action", "@Url.Action(Model.Search.BackAction, (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function doInsert() {
            $(targetFormID).attr("action", "@Url.Action("ActionSaveAwat", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}