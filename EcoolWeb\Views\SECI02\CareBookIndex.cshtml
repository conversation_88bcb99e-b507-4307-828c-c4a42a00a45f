﻿@model SECI02CareIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string data = Request.Params["from"];
    string classno = Request.Params["CLASS_NO"];
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else if (ViewBag.from != null)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

}

<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<br />

@using (Html.BeginForm("CareBookIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1" }))
{

    @Html.Partial("_Title_Secondary")
    @Html.Hidden("from", data)
    if (data == "SEI05" && ViewBag.from == null)
    {
        @Html.Partial("../SECI05/_SECI05Menu", 6)

    }
    else if (ViewBag.from != null)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
    else { Html.RenderAction("_Menu", "SECI07", new { NowAction = "CareBookIndex" }); }
    @Html.HiddenFor(m => m.fromStr)
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("SCHOOL_NO", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", onchange = "form1.submit();" })
        </div>

        <div class="form-group">
            <label class="control-label">年級</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("GRADE", (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "form1.submit();" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("CLASS_NO", (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "form1.submit();" })
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

    <br />

    <div class="form-inline">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔(Excel)</button>
        </div>
    </div>
    <br />

    <div class="text-right">
        <strong>資料統計至@((DateTime.Now.AddDays(-1)).ToLongDateString().ToString())</strong>
    </div>
    <div id="tbData">
        <div style="height:10px"></div>
        <div class="col-sm-12">
            <div class="panel table-responsive">
                <table class="table table-striped table-hover">
                    <caption><strong style="font-size:20px">1個月內未借書清單</strong></caption>
                    <thead>
                        <tr>
                            <th>
                                班級
                            </th>
                            <th style="text-align: center">
                                學號
                            </th>
                            <th style="text-align: center">
                                座號
                            </th>
                            <th style="text-align: center">
                                姓名
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.PeopleUnused)
                        {
                            <tr>
                                <td>
                                    @item.CLASS_NO
                                </td>

                                <td align="center">
                                    @item.USER_NO
                                </td>
                                <td align="center">
                                    @item.SEAT_NO
                                </td>
                                <td align="center">
                                    @item.NAME
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

<script language="javascript">

    var targetFormID = '#form1';

    function PrintBooK() {
        $('#tbData').printThis();
    }

    function todoClear() {
        ////重設
        $("#form1").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }

    function SelectSCHOOL_NO(Val)
    {
        if (Val == '@SharedGlobal.ALL') {
            $('#CLASS_NO').val('').change();
            $('#USER_NO').val('').change();
            $('#CLASS_NO').attr('disabled', 'disabled');
            $('#USER_NO').attr('disabled', 'disabled');
        }
        else {
            $('#CLASS_NO').removeAttr('disabled');
            $('#USER_NO').removeAttr('disabled');
        }

        form1.submit();
    }
    $(document).ready(function () {
        $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });

                var selectedSCHOOL_NO = $.trim($('#SCHOOL_NO option:selected').val());
        var selectedCLASS_NO = $.trim($('#CLASS_NO option:selected').val());
        var selectedUSER_NO = $.trim($('#USER_NO option:selected').val());

        if (selectedSCHOOL_NO != '@SharedGlobal.ALL' && selectedCLASS_NO !='') {
            $("#CLASS_NO option").remove();
            $("#CLASS_NO ").append("<option value=\"" + selectedCLASS_NO + "\">" + selectedCLASS_NO+"</option>");

        }
          $('#fromStr').val("@ViewBag.from");
    });

    function fn_save() {
        var blob = new Blob([document.getElementById('tbData').innerHTML], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        });
        var strFile = "Report.xls";
        saveAs(blob, strFile);
        return false;
    }
</script>