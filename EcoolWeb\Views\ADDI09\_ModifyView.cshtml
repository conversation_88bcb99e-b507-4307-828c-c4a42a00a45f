﻿@model ECOOL_APP.com.ecool.Models.DTO.QuerySelectPrintViewModel

@using (Html.BeginCollectionItem("Chk"))
{
    @Html.CheckBoxFor(m => m.CheckBoxNo, new { @class = "css-checkbox" })
    if (Model.QuerySelectViewModelHRMT01List!= null)
    {
        @Html.HiddenFor(m => m.QuerySelectViewModelHRMT01List.USER_NO)
        @Html.HiddenFor(m => m.QuerySelectViewModelHRMT01List.NAME)
        @Html.HiddenFor(m => m.QuerySelectViewModelHRMT01List.CLASS_NO)
        @Html.HiddenFor(m => m.QuerySelectViewModelHRMT01List.SEAT_NO)
        @Html.HiddenFor(m => m.QuerySelectViewModelHRMT01List.CLASS_NO)
   
    }

  
}