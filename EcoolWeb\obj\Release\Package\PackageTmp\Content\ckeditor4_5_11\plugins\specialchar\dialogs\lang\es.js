﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'es', {
	euro: 'Símbolo de euro',
	lsquo: 'Comilla simple izquierda',
	rsquo: 'Comilla simple derecha',
	ldquo: 'Comilla doble izquierda',
	rdquo: 'Comilla doble derecha',
	ndash: 'Guión corto',
	mdash: 'Guión medio largo',
	iexcl: 'Signo de admiración invertido',
	cent: 'Símbolo centavo',
	pound: 'Símbolo libra',
	curren: 'Símbolo moneda',
	yen: 'Símbolo yen',
	brvbar: 'Barra vertical rota',
	sect: 'Símbolo sección',
	uml: 'Diéresis',
	copy: 'Signo de derechos de autor',
	ordf: 'Indicador ordinal femenino',
	laquo: 'Abre comillas angulares',
	not: 'Signo negación',
	reg: 'Signo de marca registrada',
	macr: 'Guión alto',
	deg: 'Signo de grado',
	sup2: 'Superíndice dos',
	sup3: 'Superíndice tres',
	acute: 'Acento agudo',
	micro: 'Signo micro',
	para: 'Signo de pi',
	middot: 'Punto medio',
	cedil: 'Cedilla',
	sup1: 'Superíndice uno',
	ordm: 'Indicador orginal masculino',
	raquo: 'Cierra comillas angulares',
	frac14: 'Fracción ordinaria de un quarto',
	frac12: 'Fracción ordinaria de una mitad',
	frac34: 'Fracción ordinaria de tres cuartos',
	iquest: 'Signo de interrogación invertido',
	Agrave: 'Letra A latina mayúscula con acento grave',
	Aacute: 'Letra A latina  mayúscula con acento agudo',
	Acirc: 'Letra A latina mayúscula con acento circunflejo',
	Atilde: 'Letra A latina mayúscula con tilde',
	Auml: 'Letra A latina mayúscula con diéresis',
	Aring: 'Letra A latina mayúscula con aro arriba',
	AElig: 'Letra Æ latina mayúscula',
	Ccedil: 'Letra C latina mayúscula con cedilla',
	Egrave: 'Letra E latina mayúscula con acento grave',
	Eacute: 'Letra E latina mayúscula con acento agudo',
	Ecirc: 'Letra E latina mayúscula con acento circunflejo',
	Euml: 'Letra E latina mayúscula con diéresis',
	Igrave: 'Letra I latina mayúscula con acento grave',
	Iacute: 'Letra I latina mayúscula con acento agudo',
	Icirc: 'Letra I latina mayúscula con acento circunflejo',
	Iuml: 'Letra I latina mayúscula con diéresis',
	ETH: 'Letra Eth latina mayúscula',
	Ntilde: 'Letra N latina mayúscula con tilde',
	Ograve: 'Letra O latina mayúscula con acento grave',
	Oacute: 'Letra O latina mayúscula con acento agudo',
	Ocirc: 'Letra O latina mayúscula con acento circunflejo',
	Otilde: 'Letra O latina mayúscula con tilde',
	Ouml: 'Letra O latina mayúscula con diéresis',
	times: 'Signo de multiplicación',
	Oslash: 'Letra O latina mayúscula con barra inclinada',
	Ugrave: 'Letra U latina mayúscula con acento grave',
	Uacute: 'Letra U latina mayúscula con acento agudo',
	Ucirc: 'Letra U latina mayúscula con acento circunflejo',
	Uuml: 'Letra U latina mayúscula con diéresis',
	Yacute: 'Letra Y latina mayúscula con acento agudo',
	THORN: 'Letra Thorn latina mayúscula',
	szlig: 'Letra s latina fuerte pequeña',
	agrave: 'Letra a latina pequeña con acento grave',
	aacute: 'Letra a latina pequeña con acento agudo',
	acirc: 'Letra a latina pequeña con acento circunflejo',
	atilde: 'Letra a latina pequeña con tilde',
	auml: 'Letra a latina pequeña con diéresis',
	aring: 'Letra a latina pequeña con aro arriba',
	aelig: 'Letra æ latina pequeña',
	ccedil: 'Letra c latina pequeña con cedilla',
	egrave: 'Letra e latina pequeña con acento grave',
	eacute: 'Letra e latina pequeña con acento agudo',
	ecirc: 'Letra e latina pequeña con acento circunflejo',
	euml: 'Letra e latina pequeña con diéresis',
	igrave: 'Letra i latina pequeña con acento grave',
	iacute: 'Letra i latina pequeña con acento agudo',
	icirc: 'Letra i latina pequeña con acento circunflejo',
	iuml: 'Letra i latina pequeña con diéresis',
	eth: 'Letra eth latina pequeña',
	ntilde: 'Letra n latina pequeña con tilde',
	ograve: 'Letra o latina pequeña con acento grave',
	oacute: 'Letra o latina pequeña con acento agudo',
	ocirc: 'Letra o latina pequeña con acento circunflejo',
	otilde: 'Letra o latina pequeña con tilde',
	ouml: 'Letra o latina pequeña con diéresis',
	divide: 'Signo de división',
	oslash: 'Letra o latina minúscula con barra inclinada',
	ugrave: 'Letra u latina pequeña con acento grave',
	uacute: 'Letra u latina pequeña con acento agudo',
	ucirc: 'Letra u latina pequeña con acento circunflejo',
	uuml: 'Letra u latina pequeña con diéresis',
	yacute: 'Letra u latina pequeña con acento agudo',
	thorn: 'Letra thorn latina minúscula',
	yuml: 'Letra y latina pequeña con diéresis',
	OElig: 'Diptongo OE latino en mayúscula',
	oelig: 'Diptongo oe latino en minúscula',
	'372': 'Letra W latina mayúscula con acento circunflejo',
	'374': 'Letra Y latina mayúscula con acento circunflejo',
	'373': 'Letra w latina pequeña con acento circunflejo',
	'375': 'Letra y latina pequeña con acento circunflejo',
	sbquo: 'Comilla simple baja-9',
	'8219': 'Comilla simple alta invertida-9',
	bdquo: 'Comillas dobles bajas-9',
	hellip: 'Puntos suspensivos horizontales',
	trade: 'Signo de marca registrada',
	'9658': 'Apuntador negro apuntando a la derecha',
	bull: 'Viñeta',
	rarr: 'Flecha a la derecha',
	rArr: 'Flecha doble a la derecha',
	hArr: 'Flecha izquierda derecha doble',
	diams: 'Diamante negro',
	asymp: 'Casi igual a'
} );
