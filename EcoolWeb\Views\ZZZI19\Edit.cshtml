﻿@model ZZZI19Hrmt01ViewModel
@using EcoolWeb.Util;
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutWebView.cshtml";
}

@Html.Partial("_Notice")
<body>

    @using (Html.BeginForm("EditSave", (string)ViewBag.BRE_NO, FormMethod.Post, new {  name = "ZZZI19E", id = "ZZZI19E", enctype = "multipart/form-data", @AutoComplete = "Off" }))
    {
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })


        @*@Html.ValidationSummary(true, "", new { @class = "text-danger" })*@
        @*@Html.ValidationSummary(true, "", new { @class = "text-danger" })*@
        <div>
    
            <img src="~/Content/img/web-bar3-revise-114.png" style="width:100%" class="img-responsive " alt="Responsive image" />
            <div class="Div-EZ-ADDI05">
                <div class = "form-horizontal">
                    <div style="height:15px"></div>
                    <div class="Caption_Div">
                        @*@ViewBag.Panel_Title*@
                    </div>
                    <div style="height:15px"></div>
                    <div>
                        <div class="form-group">
                            @Html.Label("說明：  新增轉入生請注意，「學號」絕對不可以重複，包括不能和轉出生重複。例如103005  的學號已經有人用過，縱使這學號的主人已經轉出了，也不能給下一個學生使用。", htmlAttributes: new { @class = "control-label col-md-9" ,@style = "padding-left:100px" })
                          
                        </div>
                            <div class="form-group">
                                @Html.Label("學號", htmlAttributes: new { @class = "control-label col-md-3" })
                                <div class="col-md-9">
                                    @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })

                                    @Html.ValidationMessageFor(model => model.USER_NO, "", new { @class = "text-danger" })
                                </div>
                            </div>
                            <div class="form-group">
                                @Html.LabelFor(model => model.NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                                <div class="col-md-9">
                                    @Html.EditorFor(model => model.NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })

                                    @Html.ValidationMessageFor(model => model.NAME, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.GRADE, htmlAttributes: new { @class = "control-label col-md-3" })
                                <div class="col-md-9">
                                    @Html.DropDownListFor(model => model.GRADE, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control", @placeholder = "必填" })
                                    @Html.ValidationMessageFor(model => model.GRADE, "", new { @class = "text-danger" })
                                </div>
                            </div>
                            <div class="form-group">

                                @Html.LabelFor(model => model.SEX, htmlAttributes: new { @class = "control-label col-md-3" })

                                <div class="col-md-9">
                                    <input name="sex" id="sex" value="0" type="radio" autocomplete="off" checked />
                                    <samp>女</samp>
                                    <input name="sex" id="sex" value="0" type="radio" autocomplete="off" />
                                    <samp>男</samp>
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                                <div class="col-md-9">
                                    @Html.DropDownListFor(model => model.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @placeholder = "必填" })
                                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                                    <span style="color:red">*例如:一年六班 ，請填106</span>
                                </div>
                            </div>
                            <div class="form-group">
                                @Html.LabelFor(model => model.SEAT_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                                <div class="col-md-9">
                                    @Html.EditorFor(model => model.SEAT_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                                    @Html.ValidationMessageFor(model => model.SEAT_NO, "", new { @class = "text-danger" })
                                    <span style="color:red">*例如:座位2，請填02</span>
                                </div>
                            </div>

                            <div class="form-group">

                                @Html.Label("學年度", htmlAttributes: new { @class = "control-label col-md-3" })


                                <div class="col-md-9">
                                    @Html.DropDownListFor(model => model.SYEAR, (IEnumerable<SelectListItem>)ViewBag.YearItems, new { @class = "form-control", @placeholder = "必填" })
                                    @Html.ValidationMessageFor(model => model.SYEAR, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.IDNO, htmlAttributes: new { @class = "control-label col-md-3" })
                                <div class="col-md-9">
                                    @Html.EditorFor(model => model.IDNO, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                                    @Html.ValidationMessageFor(model => model.IDNO, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.BIRTHDAY, htmlAttributes: new { @class = "control-label col-md-3" })
                                <div class="col-md-9">
                                    @Html.EditorFor(model => model.BIRTHDAY, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                                    @Html.ValidationMessageFor(model => model.BIRTHDAY, "", new { @class = "text-danger" })
                                    <span style="color:red">*格式要為1981-12-31</span>
                                </div>
                            </div>


                        </div>
                        <div class="text-center">
                            <button class="btn btn-default" type="button" onclick="btnADDUser_onclick();" id="BtnSave">儲存</button>
                            @*<input type="button" id="ADD" value="新增" class="btn btn-default" onclick="btnADDUser_onclick();" />*@
                            <input type="button" id="back" value="返回" class="btn btn-default"  onclick="btnback()" />

                        </div>
                    </div>
            </div>

        </div>}
</body>
        @section scripts{
<script>
        var targetFormID = '#ZZZI19E';
        function btnADDUser_onclick() {

            @*$(targetFormID).attr("action", "@Url.Action("CheckInfo", (string)ViewBag.BRE_NO)")*@
            $(targetFormID).submit();
           // document.ZZZI19.enctype = "multipart/form-data";
          //  document.ZZZI19.action = "CheckInfo";
           // document.ZZZI19.submit();

                }
                function btnback() {
                    window.location.replace("@Url.Action("Query", (string)ViewBag.BRE_NO)");
                     

                }
                function removeObj(obj) {
                    var classAttr = "";
                    classAttr = $(obj).attr("class", "btn");
                    //console.log(classAttr);
                }
                $(document).ready(function () {

                    $("#BIRTHDAY").datepicker({
                        dateFormat: "yy-mm-dd",
                        changeMonth: true,
                        changeYear: true,
                        showOn: "both",
                        buttonImage: "../Content/img/icon/calendar.gif",
                        buttonImageOnly: true,
                    });
                });
</script>}
