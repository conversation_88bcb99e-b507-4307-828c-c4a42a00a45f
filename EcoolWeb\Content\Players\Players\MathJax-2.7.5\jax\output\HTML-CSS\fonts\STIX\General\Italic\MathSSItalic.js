/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathSSItalic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{120328:[674,0,666,31,635],120329:[662,0,604,74,641],120330:[676,14,671,96,755],120331:[662,0,692,74,751],120332:[662,0,583,74,678],120333:[662,0,535,74,679],120334:[676,14,695,97,755],120335:[662,0,658,74,749],120336:[662,0,401,59,512],120337:[662,14,398,22,470],120338:[662,0,634,74,729],120339:[662,0,559,74,564],120340:[662,0,843,75,933],120341:[662,14,675,74,766],120342:[676,14,714,99,779],120343:[662,0,525,74,638],120344:[676,175,716,99,779],120345:[662,0,589,74,639],120346:[676,14,541,62,597],120347:[662,0,608,161,748],120348:[662,14,661,117,757],120349:[662,11,654,196,788],120350:[662,11,921,194,1057],120351:[662,0,700,31,806],120352:[662,0,630,186,774],120353:[662,0,637,28,763],120354:[463,10,448,55,467],120355:[684,10,496,74,535],120356:[463,10,456,67,503],120357:[684,11,494,72,600],120358:[463,10,444,69,487],120359:[683,0,336,101,526],120360:[463,216,496,-7,575],120361:[684,0,487,63,510],120362:[679,0,220,69,325],120363:[679,216,254,-118,354],120364:[684,0,453,63,556],120365:[684,0,205,61,313],120366:[464,0,756,65,775],120367:[464,0,487,63,510],120368:[463,10,499,76,536],120369:[464,216,498,14,538],120370:[464,216,498,72,549],120371:[464,0,336,63,439],120372:[463,10,389,61,432],120373:[580,10,291,96,376],120374:[453,11,491,89,536],120375:[453,14,474,143,555],120376:[453,14,702,140,787],120377:[453,0,482,30,544],120378:[453,216,484,-19,565],120379:[453,0,447,25,517]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/MathSSItalic.js");
