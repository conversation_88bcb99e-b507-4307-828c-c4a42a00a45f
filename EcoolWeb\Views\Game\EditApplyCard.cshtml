﻿@model BatchCashIntoIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BatchWork" });
    }
}

@using (Html.BeginForm("EditApplyCard", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div id="PageContent">
        @Html.Action("_PageEditApplyCard", (string)ViewBag.BRE_NO, Model)
    </div>
}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
    var targetFormID = '#form1'

        //分頁
         function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.CashSearch.Page)').val(page)
                funAjax()
            }
        };

         //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_PageEditApplyCard", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function OnEdit(TEMP_USER_ID) {
            $('#@Html.IdFor(m=>m.Search.WhereTEMP_USER_ID)').val(TEMP_USER_ID)
            $(targetFormID).attr("action", "@Url.Action("EditPerson", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function OndDel(TEMP_USER_ID) {

            var OK = confirm("您確定要刪除選取的報名資料，按確定後，報名著資料及報名著參與的活動內容，將全部被刪除!!，刪除後無法還原!!!!")

            if (OK) {
                $('html, body').scrollTop(0);
                $(targetFormID).hide()
                $('#loading').fadeIn(3000)
                setTimeout(function () {
                     $('#@Html.IdFor(m=>m.Search.WhereTEMP_USER_ID)').val(TEMP_USER_ID)
                     $(targetFormID).attr("action", "@Url.Action("DelPerson", (string)ViewBag.BRE_NO)")
                     $(targetFormID).submit();
                }, 3000);
            }
        }

        function OndDisable(TEMP_USER_ID) {
            $('#@Html.IdFor(m=>m.Search.WhereTEMP_USER_ID)').val(TEMP_USER_ID)
            $(targetFormID).attr("action", "@Url.Action("DisablePerson", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function OnEnable(TEMP_USER_ID) {
            $('#@Html.IdFor(m=>m.Search.WhereTEMP_USER_ID)').val(TEMP_USER_ID)
            $(targetFormID).attr("action", "@Url.Action("EnablePerson", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("BatchWork", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}