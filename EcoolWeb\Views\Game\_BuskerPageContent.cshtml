﻿
@model GameBuskerManagerViewModel

@Html.HiddenFor(m => m.Search.WhereGAME_NAME)
@Html.HiddenFor(m => m.Search.WhereGAME_NO)
@Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.OrdercColumn)

@Html.Hidden("WhereTITLE_SHOW_ID")

<br />
<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">搜尋欲瀏覽之相關字串</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.WhereKeyIn, new { htmlAttributes = new { @class = "form-control" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
<br />

<div class="text-right">
    <a role="button" onclick="onBuskerEdit('')" class="btn btn-sm btn-sys"><i class="fa fa-plus-circle"></i>  新增街頭藝人小隊</a>
    <br />
    <br />
</div>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        街頭藝人管理
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th></th>
                    <th></th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('TITLE_SHOW_NAME')">
                            @Html.DisplayNameFor(model => model.Edit.First().TITLE_SHOW_NAME)
                        </samp>
                    </th>

                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('ORDER_BY')">
                            @Html.DisplayNameFor(model => model.Edit.First().ORDER_BY)
                        </samp>
                    </th>
                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('LIVE_STREAM')">
                            @Html.DisplayNameFor(model => model.Edit.First().LIVE_STREAM)
                        </samp>
                    </th>
                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('LIKE_COUNT')">
                            @Html.DisplayNameFor(model => model.Edit.First().LIKE_COUNT)
                        </samp>
                    </th>
                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CASH')">
                            獲得點數
                        </samp>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (Model.Edit.Count() > 0)
                {
                    foreach (var item in Model.Edit)
                    {
                        <tr id="Tr@(item.TITLE_SHOW_ID)">
                            <td>
                                @Html.HiddenFor(modelItem => item.TITLE_SHOW_ID)


                                <button type="button" class="btn btn-xs btn-Basic" onclick="onBuskerEdit('@item.TITLE_SHOW_ID')"> <i class="fa fa-edit"></i>編輯</button>

                                <button type="button" class="btn btn-xs btn-Basic" onclick="OnSave('@item.TITLE_SHOW_ID','Delete')"> <i class="fa fa-trash-o"></i>刪除</button>

                                <br />

                                @if (item.LIVE_STREAM == true)
                                {
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnSave('@item.TITLE_SHOW_ID','UnLive')"> <i class="fa fa-video-camera"></i>取消直播</button>
                                }
                                else
                                {
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnSave('@item.TITLE_SHOW_ID','Live')"> <i class="fa fa-video-camera"></i>設定直播</button>
                                }

                                <button type="button" class="btn btn-xs btn-Basic" onclick="OnShowCash('@item.TITLE_SHOW_ID','@item.LIKE_COUNT')"> <i class="fa fa-money"></i>給點數</button>
                            </td>
                            <td align="center">
                                @{var Item3 = "";
                                    var EditPeopleleng = 0;
                                    EditPeopleleng = Model.EditPeople.Where(X => X.GAME_NO == item.GAME_NO && X.TITLE_SHOW_ID == item.TITLE_SHOW_ID).ToList().Count();
                                    var i = 0;
                                }


                                @foreach (var Item2 in Model.EditPeople.Where(X => X.GAME_NO == item.GAME_NO && X.TITLE_SHOW_ID == item.TITLE_SHOW_ID).ToList().Take(5))
                                {
                                    if (i == EditPeopleleng - 1) { Item3 += Item2.SNAME; }
                                    else
                                    {
                                        Item3 += Item2.SNAME + ",";
                                    }
                                    i++;
                                }
                                @Item3
                                @if (EditPeopleleng > 5) {
                                <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkHrmt08More('')">more</button>

                                }
                            </td>
                            <td align="center">
                                @item.TITLE_SHOW_NAME
                                @Html.HiddenFor(modelItem => item.TITLE_SHOW_NAME)
                            </td>
                            <td align="center">
                                @Html.EditorFor(modelItem => item.ORDER_BY, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填", @style = "width:60px", @onblur = "OnSave('"+item.TITLE_SHOW_ID+"','Save')" } })
                            </td>
                            <td align="center">
                                @if (item.LIVE_STREAM == true)
                                {
                                    <code>live</code>
                                }
                            </td>
                            <td align="center">
                                @item.LIKE_COUNT
                            </td>
                            <td align="center">
                                @item.CASH
                            </td>
                        </tr>
                    }
                }

            </tbody>
        </table>
    </div>
</div>
<div>
    @Html.Pager(Model.Edit.PageSize, Model.Edit.PageNumber, Model.Edit.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                                              .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                                              .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                                              .SetNextPageText(PageGlobal.DfSetNextPageText)
                                                             )
</div>
