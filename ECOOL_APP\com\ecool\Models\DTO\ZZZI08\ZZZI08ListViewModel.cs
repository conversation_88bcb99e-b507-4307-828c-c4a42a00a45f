﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.com.ecool.Models.entity;
using MvcPaging;
using System.ComponentModel.DataAnnotations;



namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI08ListViewModel
    {



        public string VIEW_DATA_TYPE { get; set; }

        ///// <summary>
        ///// 搜尋條件
        ///// </summary>
        //[DisplayName("活動代碼")]
        //public string Q_DIALOG_ID { get; set; }



        //[DisplayName("學年")]
        //public Nullable<int> Q_SYEAR { get; set; }

        //[DisplayName("學期")]
        //public Nullable<byte> Q_SEMESTER { get; set; }

        //[DisplayName("活動名稱")]
        //public string Q_DIALOG_NAME { get; set; }

        //[DisplayName("開始日期")]
        //[DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        //public Nullable<DateTime> Q_DIALOG_SDATE { get; set; }

        //[DisplayName("截止日期")]
        //[DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        //public Nullable<DateTime> Q_DIALOG_EDATE { get; set; }


        //[DisplayName("承辦人員")]
        //public string Q_Name_Contents { get; set; }

        [DisplayName("學校代碼")]
        public string Q_SCHOOL_NO { get; set; }
        public string Q_STATUS { get; set; }

        [DisplayName("是否允許共享")]
        public string Q_COPY_YN { get; set; }


        [DisplayName("簡易查詢")]
        public string SearchContents { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderByName  { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName  { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 查詢類別 1.簡易 2. 進階
        /// </summary>
        public int SearchType { get; set; }


        /// <summary>
        /// 結果清單
        /// </summary>
        public IPagedList<uADDT11> ADDT11List { get; set; }

        public ZZZI08ListViewModel()
        {
            SearchType = 1;
            Page = 1;
            OrderByName = "DIALOG_EDATE";
            SyntaxName = "Desc";
            Q_COPY_YN = "N";
        }
    }
}
