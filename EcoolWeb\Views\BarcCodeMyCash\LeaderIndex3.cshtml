﻿@model  List<BarCoderankViewModel>
@{
    ECOOL_APP.UserProfile user = new ECOOL_APP.UserProfile();

    Layout = "~/Views/Shared/_LayoutSleepATM.cshtml";

    List<string> RankStr = new List<string>();
    RankStr.Add(" number-first");
    RankStr.Add(" number-second");
    RankStr.Add(" number-third");
}


<body class="leaderboard-page-bg-merge" onclick="openImg()">
    <h1 class="title title-ecool text-hide">累計與現有排行榜</h1>
    <div class="container-fluid leaderboard-page-layout leaderboard-page-layout-merge">
        <div class="row">
            <div class="col-md-6">
                <strong class="title title-total text-hide">累計酷幣排行榜</strong>
                <ul class="leaderboard-box leaderboard-box-red">
                    @{int i = 0;}
                    @foreach (var Item in Model.Where(x => x.Ranktype == "DenRank").ToList())
                    {
                        var cssRankStr = "";
                        var cssRankStrItem = "";
                        cssRankStr = "leaderboard-item" ;
                        cssRankStrItem = "text-hide number" + RankStr.Skip(i).Take(1).FirstOrDefault();

                        <li class='@cssRankStr'>
                            <span class='@cssRankStrItem'>@Item.ALLRANK</span>
                            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                            <span class="name">@Item.SNAME</span>
                            <span class="value">@Item.ALLNumberInfo <small>點數</small></span>
                        </li>
                        i++;
                    }
                   
                </ul>
            </div>
            <div class="col-md-6">
                <strong class="title title-existing text-hide">現有資產排行榜</strong>
                <ul class="leaderboard-box leaderboard-box-purple">

                    @{int j = 0;}
                    @foreach (var Item in Model.Where(x => x.Ranktype == "SumCashRank").ToList())
                    {
                        var cssRankStr = "";
                        var cssRankStrItem = "";
                        cssRankStr = "leaderboard-item" ;
                        cssRankStrItem = "text-hide number" + RankStr.Skip(j).Take(1).FirstOrDefault();

                        <li class='@cssRankStr'>
                            <span class='@cssRankStrItem'>@Item.ALLRANK</span>
                            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                            <span class="name">@Item.SNAME</span>
                            <span class="value">@Item.ALLNumberInfo <small>酷幣</small></span>
                        </li>
                        j++;
                    }
                   
                </ul>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <strong class="title title-read text-hide">閱讀認證排行榜</strong>
                <ul class="leaderboard-box leaderboard-box-green">

                    @{int k = 0;}
                    @foreach (var Item in Model.Where(x => x.Ranktype == "BookRank").ToList())
                    {
                        var cssRankStr = "";
                        var cssRankStrItem = "";
                        cssRankStr = "leaderboard-item";
                        cssRankStrItem = "text-hide number" + RankStr.Skip(k).Take(1).FirstOrDefault();

                        <li class='@cssRankStr'>
                            <span class='@cssRankStrItem'>@Item.ALLRANK</span>
                            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                            <span class="name">@Item.SNAME</span>
                            <span class="value">@Item.ALLNumberInfo <small>本</small></span>
                        </li>
                       k++;
                    }
                   
                </ul>
            </div>
            <div class="col-md-6">
                <strong class="title title-sports text-hide">運動撲滿排行榜</strong>
                <ul class="leaderboard-box leaderboard-box-blue">
                    @{int l = 0;}
                    @foreach (var Item in Model.Where(x => x.Ranktype == "RunRank").ToList())
                    {
                        var cssRankStr = "";
                        var cssRankStrItem = "";
                        cssRankStr = "leaderboard-item";
                        cssRankStrItem = "text-hide number" + RankStr.Skip(l).Take(1).FirstOrDefault();

                        <li class='@cssRankStr'>
                            <span class='@cssRankStrItem'>@Item.ALLRANK</span>
                            <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                            <span class="name">@Item.SNAME</span>
                            <span class="value">@Item.Total_M <small>公里</small></span>
                        </li>
                       l++;
                    }
                   
                </ul>
                <span class="img-run-girl"></span>
            </div>
        </div>
    </div>
</body>
<script>
    var box = document.querySelector('.leaderboard-page-bg-merge');
    box.addEventListener('mousemove', touch, false);
     function openImg() {
  
        window.location.href =     "@Url.Action(ViewBag.FROMACTION, "BarcCodeMyCash")?WhereSchoolNo=" + "@Model.FirstOrDefault().SCHOOL_NO" + "&FROMACTION=" + "@ViewBag.FROMACTION" + "&TimeoutSeconds=" + "@ViewBag.TimeoutSeconds";


    }
    function touch() {



           window.location.href =     "@Url.Action(ViewBag.FROMACTION, "BarcCodeMyCash")?WhereSchoolNo=" + "@Model.FirstOrDefault().SCHOOL_NO" + "&FROMACTION=" + "@ViewBag.FROMACTION" + "&TimeoutSeconds=" + "@ViewBag.TimeoutSeconds";
    }
</script>