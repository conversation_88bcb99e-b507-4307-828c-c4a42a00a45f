/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Operators/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Operators={directory:"Operators/Regular",family:"GyrePagellaMathJax_Operators",testString:"\u00A0\u2206\u220A\u220C\u220E\u220F\u2210\u2211\u221F\u222C\u222D\u222E\u222F\u2230\u2231",32:[0,0,250,0,0],160:[0,0,250,0,0],8710:[700,3,629,6,617],8714:[450,-50,578,80,498],8716:[650,150,778,80,698],8718:[585,0,745,80,665],8719:[750,250,1113,80,1033],8720:[750,250,1113,80,1033],8721:[750,250,983,80,903],8735:[630,0,790,80,710],8748:[796,296,952,80,872],8749:[796,296,1270,80,1190],8750:[796,296,684,80,604],8751:[796,296,1002,80,922],8752:[796,296,1320,80,1240],8753:[796,296,726,80,686],8754:[796,296,747,80,707],8755:[796,296,689,80,649],8758:[468,-32,280,80,200],8759:[468,-32,596,80,516],8760:[520,-220,760,80,680],8761:[441,-59,880,80,800],8762:[520,20,760,80,680],8763:[497,-3,758,80,678],8766:[390,-110,751,80,671],8767:[467,-33,760,80,680],8772:[550,50,760,80,680],8775:[550,50,760,80,680],8777:[550,50,758,80,678],8779:[517,17,758,80,678],8780:[518,-40,760,80,680],8788:[441,-59,880,80,800],8789:[441,-59,880,80,800],8792:[540,0,760,80,680],8793:[554,54,760,80,680],8794:[554,54,760,80,680],8795:[853,-110,760,80,680],8797:[867,-110,925,80,845],8798:[745,-110,760,80,680],8799:[870,-110,760,80,680],8802:[650,150,760,80,680],8803:[610,110,760,80,680],8813:[650,150,760,80,680],8820:[706,206,766,80,686],8821:[706,206,766,80,686],8824:[761,261,771,80,691],8825:[761,261,771,80,691],8836:[650,150,778,80,698],8837:[650,150,778,80,698],8844:[550,68,760,80,680],8845:[550,68,760,80,680],8860:[568,68,796,80,716],8870:[650,150,590,80,510],8871:[650,150,590,80,510],8875:[650,150,770,80,690],8886:[410,-90,1080,80,1000],8887:[410,-90,1080,80,1000],8889:[550,50,760,80,680],8893:[584,84,760,80,680],8894:[630,103,893,80,813],8895:[651,0,812,80,732],8896:[744,230,860,80,780],8897:[730,244,860,80,780],8898:[748,230,860,80,780],8899:[730,248,860,80,780],8903:[556,56,772,80,692],8917:[650,150,760,80,680],8924:[623,113,766,80,686],8925:[623,113,766,80,686],8930:[690,190,760,80,680],8931:[690,190,760,80,680],8932:[640,220,760,80,680],8933:[640,220,760,80,680],8944:[536,36,733,80,653],10752:[708,208,1076,80,996],10753:[708,208,1076,80,996],10754:[708,208,1076,80,996],10755:[730,248,860,80,780],10756:[730,248,860,80,780],10757:[747,213,860,80,780],10758:[713,247,860,80,780],10761:[635,135,929,80,849],10764:[796,296,1588,80,1508],10769:[796,296,726,80,686],10799:[490,-10,641,80,561]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Operators"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Operators/Regular/Main.js"]);
