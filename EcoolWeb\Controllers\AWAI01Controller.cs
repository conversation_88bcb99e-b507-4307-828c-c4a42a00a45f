﻿using AutoMapper;
using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWAI01Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "AWAI01";

        public static string SouBre_NO { get; set; }

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private AWAI01Service Service = new AWAI01Service();

        /// <summary>
        /// 學生獎品清單
        /// </summary>
        /// <returns></returns>
        public ActionResult AwatQ02(AWAI01IndexViewModel model)
        {
            if (model.Search == null)
            {
                model.Search = new AWAI01SearchViewModel()
                {
                    BackController = Bre_NO,
                    BackAction = RouteData.Values["action"].ToString(),
                    WhereSouTable = AWAI01SearchViewModel.SouTableVal.Student
                };
            }

            SouBre_NO = model.Search.BackController;

            return View(model);
        }

        /// <summary>
        /// 老師獎品清單
        /// </summary>
        /// <returns></returns>
        public ActionResult Awat2Q02(AWAI01IndexViewModel model)
        {
            if (model.Search == null)
            {
                model.Search = new AWAI01SearchViewModel()
                {
                    BackController = "AWAI03",
                    BackAction = RouteData.Values["action"].ToString(),
                    WhereSouTable = AWAI01SearchViewModel.SouTableVal.Teacher
                };
            }
            SouBre_NO = model.Search.BackController;

            return View(model);
        }

        /// <summary>
        /// 商品列表PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult _PageContent(AWAI01IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new AWAI01IndexViewModel();
            if (model.Search == null) model.Search = new AWAI01SearchViewModel();

            if (PermissionService.GetPermission_Use_YN(model.Search.BackController, model.Search.BackAction, SCHOOL_NO, USER_NO) == "N")
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            if (string.IsNullOrWhiteSpace(model.Search.BackController) || string.IsNullOrWhiteSpace(model.Search.BackAction))
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error");
            }

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            model = Service.GetProductListData(model, user, ref db);

            model.AwardSchoolItemQuery = Awa.AwardSchoolSelectIte(SCHOOL_NO, model.Search.whereAWARD_SCHOOL_NO);
            model.AwardTypeItemQuery = Awa.AWARD_TYPE_Val.SelectItemQuery(model.Search.AWARD_TYPE);

            BDMT01 drBDMT01 = db.BDMT01.Where(p => p.SCHOOL_NO == SCHOOL_NO).FirstOrDefault();
            if (drBDMT01 != null)
            {
                model.AwatQ02SEXPLAIN = drBDMT01.AwatQ02SEXPLAIN;
                model.AwatQ02TEXPLAIN = drBDMT01.AwatQ02TEXPLAIN;
            }

            ViewBag.ImgUrl = Url.Content(GetImagePathUrl(model.Search.WhereSouTable));

            string BtnAction = (model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher) ? "Awat202" : "Awat02";

            string UseYN = PermissionService.GetPermission_Use_YN(model.Search.BackController, BtnAction, SCHOOL_NO, USER_NO);
            model.VisableInsert = (UseYN == "Y");
            model.VisableModify = model.VisableInsert;
            model.VisableDelete = model.VisableInsert;
            model.VisableFullScreen = model.VisableInsert;

            if (!string.IsNullOrEmpty(model.Search.unProduct))
            {
                SetTitle(" - 兌換獎品一覽表(下架商品) ");
            }

            return PartialView(model);
        }

        /// <summary>
        /// 商品列表PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult _ProductList(AWAI01IndexViewModel model)
        {
            if (PermissionService.GetPermission_Use_YN(model.Search.BackController, model.Search.BackAction, SCHOOL_NO, USER_NO) == "N")
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            if (string.IsNullOrWhiteSpace(model.Search.BackController) || string.IsNullOrWhiteSpace(model.Search.BackAction))
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error");
            }

            return PartialView(model);
        }

        /// <summary>
        /// 行動商品列表 For FullScreen
        /// </summary>
        /// <returns></returns>
        
        [HttpPost]
        public ActionResult ProductListForFullScreen(AWAI01IndexViewModel model)
        {
            this.Shared(" - 行動支付列表 ");
            // 權限比照AWAT02 => 目前來源SouTable皆會為"Student"
            string SouTable = "";
            if (model.Search == null)
            {

                model.Search = new AWAI01SearchViewModel();
                model.Search.WhereSouTable = "Student";
                   SouTable = "Student";

            }
            else {
                SouTable = model.Search.WhereSouTable;

            }
            if (!string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO)) {

                model.Search = new AWAI01SearchViewModel();
                model.Search.WhereSouTable = "Student";
                SouTable = "Student";
                model.Search.WhereSCHOOL_NO = model.WhereSCHOOL_NO;
            }
            if (PermissionService.GetPermission_Use_YN("AWAI021", "ProductListForFullScreen", SCHOOL_NO, USER_NO) == "N")
               return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");

            ViewBag.ImgUrl = Url.Content(GetImagePathUrl(SouTable));

            model = Service.GetProductListDataForFullScreen(model, user, ref db);

            return View(model);
        }

        public ActionResult ProductOrderList(AWAI01OrderListViewModel model)
        {
            this.Shared(" - 獎品兌獎排行榜 ");
            return View(model);
        }

        public ActionResult _ProductOrderListPartial(AWAI01OrderListViewModel model)
        {
            this.Shared(" - 獎品兌獎排行榜 ");
            if (model == null) model = new AWAI01OrderListViewModel();
            if (model.Search == null) model.Search = new AWAI01SearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            model = Service.GetProductOrderList(model, ref db);
            model.AwardSchoolItemQuery = Awa.AwardSchoolSelectIte(SCHOOL_NO, model.Search.whereAWARD_SCHOOL_NO);
            model.AwardTypeItemQuery = Awa.AWARD_TYPE_Val.SelectItemQuery(model.Search.AWARD_TYPE);

            ViewBag.ImgUrl = Url.Content(GetImagePathUrl(model.Search.WhereSouTable));

            return PartialView(model);
        }

        public ActionResult BidDataList(int AWARD_NO, string BackController, string WhereSouTable)
        {
            this.Shared(" - 競標清單");

            if (string.IsNullOrWhiteSpace(BackController)
                || string.IsNullOrWhiteSpace(WhereSouTable)
                || (BackController != "AWAI01" && BackController != "AWAI03")
                || (WhereSouTable != AWAI01SearchViewModel.SouTableVal.Student && WhereSouTable != AWAI01SearchViewModel.SouTableVal.Teacher))
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            if (user == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            //if (PermissionService.GetPermission_Use_YN(BackController, AWAI01SearchViewModel.SouTableVal.GetAddSave(WhereSouTable), SCHOOL_NO, USER_NO) == "N")
            //{
            //    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            //}

            var model = Service.GetBidDataList(AWARD_NO, WhereSouTable, ref db);

            if (model.AwaData == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
            }

            return PartialView(model);
        }

        public ActionResult ProductOrderListPrintExcel(AWAI01OrderListViewModel model)
        {
            this.Shared(" - 獎品兌獎排行榜 ");
            if (model == null) model = new AWAI01OrderListViewModel();
            if (model.Search == null) model.Search = new AWAI01SearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            model = Service.GetProductOrderList(model, ref db);

            DataTable DataTableExcel = model.ListData.Select(
           a =>
           {
               a.AWARD_TYPE = Awa.AWARD_TYPE_Val.GetDesc(a.AWARD_TYPE);
               return a;
           }
            ).ToList().AsDataTable();

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/ProductOrderList.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "獎品兌獎排行榜", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\獎品兌獎排行榜_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "獎品兌獎排行榜.xlsx");//輸出檔案給Client端
        }

        /// <summary>
        /// 兌換明細畫面
        /// </summary>
        /// <returns></returns>
        public ActionResult AwatExchange02(AWAI01ExchangeViewModel model)
        {
            this.Shared(" - 確認 兌換/出價 - 獎品內容");

            if (TempData["Qmode"] != null)
            {
                model = TempData["Qmode"] as AWAI01ExchangeViewModel;
            }

            if (user == null)
            {
                if (model.Search.SouController == "BarcCodeMyCash")
                {
                    TempData["StatusMessage"] = "未登入";
                    return RedirectToAction("Index", "BarcCodeMyCash", new { WhereSchoolNo = model.Search.WhereSCHOOL_NO });
                }
                else
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.SessionTimeOutError, "Error");
                }
            }

            if (model.Search?.WhereAWARD_NO == null)
            {
                if (model.Search.SouController == "BarcCodeMyCash")
                {
                    TempData["StatusMessage"] = "未選取要對兌的獎品";
                    return RedirectToAction("Index", "BarcCodeMyCash", new { WhereSchoolNo = model.Search.WhereSCHOOL_NO });
                }
                else
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error");
                }
            }

            GetExchangeData(model);

            return View(model);
        }

        /// <summary>
        /// 兌換明細畫面(數位學生證直接對換)
        /// </summary>
        /// <returns></returns>
        public ActionResult AwatExchangeCARD(AWAI01ExchangeViewModel model)
        {
            this.Shared(" - 確認 兌換/出價 - 獎品內容");

            if (TempData["Qmode"] != null)
            {
                model = TempData["Qmode"] as AWAI01ExchangeViewModel;
            }

            GetExchangeData(model, true);

            return View(model);
        }
        public ActionResult CheckUSerInfo(AWAI01ExchangeViewModel model)
        {
            string Message = string.Empty;
            string Success = "flase";
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();
            var hr = db.HRMT01.Where(a => a.SCHOOL_NO == model.Search.WhereSCHOOL_NO && a.USER_NO == model.CARD_ID).FirstOrDefault();
            var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
            if (hr == null)
            {

                hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CARD_NO == model.CARD_ID).FirstOrDefault();
                if (hr == null)
                {
                    Message = "警告!!輸入的內容有錯誤<br/>;此卡號或學號不正確，請重新輸入正確的卡號或學號";
                    Success = "true";
                    data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
                    return Json(@data, JsonRequestBehavior.AllowGet);


                }
                else
                {
                    data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + hr.NAME + "\"}";

                }
            }
            else
            {
                data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + hr.NAME + "\"}";


            }


            return Json(@data, JsonRequestBehavior.AllowGet);
        }
        public ActionResult SaveExchangeCard(AWAI01ExchangeViewModel model)
        {
            string Message = string.Empty;

            GetExchangeData(model, true);

            HRMT01 FindUser;
            if(model.Search.WhereExchangeType == "SchoolNo")
            {
                FindUser = db.HRMT01.Include("HRMT25").Where(a => a.SCHOOL_NO == model.Search.WhereSCHOOL_NO &&  a.USER_NO == model.CARD_ID).FirstOrDefault();
            }
            else
            {
                FindUser = db.HRMT01.Include("HRMT25").Where(a => a.SCHOOL_NO == model.Search.WhereSCHOOL_NO && a.CARD_NO == model.CARD_ID ).FirstOrDefault();
            }

            if (FindUser == null)
            {
                if(model.Search.WhereExchangeType == "SchoolNo")
                {
                    Message = "此學號無效:" + model.CARD_ID;
                }
                else
                {
                    Message = string.Format("此張數位學生證卡號無效，{0} 卡號:{1} ", Environment.NewLine, model.CARD_ID) ;
                }
                TempData["StatusMessage"] = Message;
                TempData["Status"] = "error";
            }
            else
            {
                //填入UserProfile
                UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);

                Message = SCheckCxchange(model.AwaData, LoginUser, model.Search.WhereSouTable);

                if (string.IsNullOrWhiteSpace(Message) == false)
                {
                    TempData["StatusMessage"] = string.Format("{0} 兌換-{1}    <font style=\"color: red\">失敗</font>  <p style=\"color: blue\"> 原因:{2}</p>", LoginUser.NAME, model.AwaData.AWARD_NAME, Message);
                    TempData["Status"] = "error";
                }
                else
                {
                    bool OK = false;
                    List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                    model = Service.SaveExchange( model, LoginUser, ref db, ref Message,ref valuesList);
                    OK = model.Status;
                    AWAT01 aWAT01Info = new AWAT01();
                    aWAT01Info = db.AWAT01.Where(x => x.SCHOOL_NO == FindUser.SCHOOL_NO && x.USER_NO == FindUser.USER_NO).FirstOrDefault();
                    if (OK)
                    {
                        model.AwaData.QTY_STORAGE -= 1; // 數量減少
                        TempData["StatusMessage"] = string.Format("{0} 兌換-{1}，{2}扣 {3} 點，剩餘 <font style=\"color: blue\">{4}</font>點", LoginUser.NAME, model.AwaData.AWARD_NAME, Environment.NewLine, model.CostCash, aWAT01Info.CASH_AVAILABLE);
                        TempData["Status"] = "success";
                        EventGeneralCoordinatorNoticeToMail((int)model.AwaData.AWARD_NO, LoginUser.SCHOOL_NO, LoginUser.USER_NO);
                    }
                    else
                    {
                        TempData["StatusMessage"] = string.Format("{0} 兌換-{1}   <font style=\"color: red\">失敗</font> <p style=\"color: blue\">原因:{2}</p>", LoginUser.NAME, model.AwaData.AWARD_NAME, Message);
                        TempData["Status"] = "error";
                    }
                }
            }

            ModelState.Clear();
            model.CARD_ID = null;

            return View("AwatExchangeCARD", model);
        }

        public void GetExchangeData(AWAI01ExchangeViewModel model, bool IsCARD = false)
        {
            try
            {
                ViewBag.ImgUrl = ECOOL_APP.UrlCustomHelper.Url_Content(GetImagePathUrl(model.Search.WhereSouTable));
                Service.GetProductExchangeData(model, ref db);

                if (IsCARD == false)
                {
                    //更新顯示
                    UserProfile.RefreshCashInfo(user, ref db);
                    UserProfileHelper.Set(user);

                    string NGError = SCheckCxchange(model.AwaData, user, model.Search.WhereSouTable);

                    if (string.IsNullOrWhiteSpace(NGError) == false)
                    {
                        ViewBag.NGError = NGError;
                        TempData["StatusMessage"] = NGError;
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 總召學校獎品 通知  總召
        /// </summary>
        /// <param name="AWARD_NO"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        public void EventGeneralCoordinatorNoticeToMail(int AWARD_NO, string SCHOOL_NO, string USER_NO)
        {
            var B01 = db.BDMT01.Where(B => B.SCHOOL_NO == SCHOOL_NO).FirstOrDefault();
            var H01 = db.HRMT01.Where(B => B.SCHOOL_NO == SCHOOL_NO && B.USER_NO == USER_NO).FirstOrDefault();

            var Tt = db.AWAT02.Where(a => a.AWARD_NO == AWARD_NO && a.SCHOOL_NO == "ALL").FirstOrDefault();
            if (Tt != null && B01 != null && H01 != null)
            {
                var DbMail = ECOOL_APP.com.ecool.service.BDMT02Service.GetRefDataList("AWAT", "EVENT_GENERAL_COORDINATOR_NOTICE_MAIL", "ALL", "ALL", ref db);

                if (DbMail.Count > 0)
                {
                    List<string> Mail = new List<string>();

                    Mail = (from c in DbMail
                            where (!string.IsNullOrEmpty(c.CONTENT_VAL))
                            select c.CONTENT_VAL.ToString()).ToList<string>();

                    if (Mail.Count > 0)
                    {
                        MailHelper MailHelper = new MailHelper();

                        StringBuilder MailBodyHtml = new StringBuilder();

                        string SUBJECT = "總召學校獎品被兌換通知";

                        MailBodyHtml.Append("<table border='0' cellspacing='0' cellpadding='0' width='640'>");
                        MailBodyHtml.Append("<tr>");
                        MailBodyHtml.AppendFormat("<td><p><font color='#0066FF'>{0}─臺北e酷幣-總召學校獎品被兌換通知！</font></p><br/></td>", B01.SCHOOL_NAME);
                        MailBodyHtml.Append("</tr>");
                        MailBodyHtml.Append("<tr>");
                        MailBodyHtml.AppendFormat("<td><p> 親愛的 總召人員 您好<br/><br/>");
                        MailBodyHtml.AppendFormat("您的總召學校獎品 <font color='#0066FF'>{0}</font> 被<font color='#0066FF'>{1}</font>的<font color='#0066FF'>{2}</font> 兌換<br/>", Tt.AWARD_NAME, B01.SHORT_NAME, H01.CLASS_NO + " 班 " + H01.NAME + " 座號" + H01.SEAT_NO);
                        MailBodyHtml.AppendFormat("</p></td>");
                        MailBodyHtml.Append("</tr>");
                        MailBodyHtml.Append("<tr>");
                        MailBodyHtml.AppendFormat("<td><p align = 'right'><br/>by 臺北e酷幣</p></td>");
                        MailBodyHtml.Append("</tr>");
                        MailBodyHtml.Append("</table>");

                        MailHelper.SendMailByGmail(Mail, SUBJECT, MailBodyHtml.ToString());
                    }
                }
            }
        }

        /// <summary>
        /// 新增畫面
        /// </summary>
        /// <returns></returns>
        public ActionResult Awat02(AWAI01EditViewModel model)
        {
            this.Shared(" - 新增");

            if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetAddSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            if (model == null) model = new AWAI01EditViewModel();
            if (model.Search == null) model.Search = new AWAI01SearchViewModel();

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SCHOOL_NO, null, user);
            ViewBag.ReadLevelSelectItem = UserProfileHelper.GetSelectListItemImgReadLEVEL();
            ViewBag.PassportLevelSelectItem = UserProfileHelper.GetSelectListItemImgPassportLEVEL();
            model.Edit = new AWAI01EditDataViewModel();
            model.Edit.SDATETIME = DateTime.Now;
            model.Edit.EDATETIME = DateTime.Now.AddMonths(6);
            return View(model);
        }

        /// <summary>
        /// 修改畫面
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult AwatMana02(AWAI01EditViewModel model)
        {
            this.Shared(" - 修改");

            if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetEditSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            if (model == null) model = new AWAI01EditViewModel();
            if (model.Search == null) model.Search = new AWAI01SearchViewModel();

            model = Service.GetProduct(model, ref db);

            model.REF_KEY = model.Edit.AWARD_NO.ToString();

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.Edit.SCHOOL_NO, null, user);
            ViewBag.ReadLevelSelectItem = UserProfileHelper.GetSelectListItemImgReadLEVEL(model.Edit.READ_LEVEL?.ToString());
            ViewBag.PassportLevelSelectItem = UserProfileHelper.GetSelectListItemImgPassportLEVEL(model.Edit.PASSPORT_LEVEL?.ToString());
            ViewBag.ImgUrl = Url.Content(GetImagePathUrl(model.Search.WhereSouTable));

            return View(model);
        }

        /// <summary>
        /// 下架資料處理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult AwatDelete(AWAI01EditViewModel model)
        {
            this.Shared(" - 修改");

            if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetEditSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            string Message = string.Empty;

            if (model.Edit.AWARD_NO == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error", new { error = "未傳入AWARD_NO" });
            }

            bool OK = Service.SaveAwatUpdataStatus(model, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "下架完成";

                AWAI01IndexViewModel Qmodel = new AWAI01IndexViewModel()
                {
                    Search = model.Search
                };

                if (!string.IsNullOrEmpty(model.Search.unProduct))
                {
                    SetTitle(" - 兌換獎品一覽表(下架商品) ");
                }
                else
                {
                    SetTitle();
                }
                return View(model.Search.BackAction, Qmodel);
            }

            TempData["StatusMessage"] = Message;

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.Edit.SCHOOL_NO, null, user);
            ViewBag.ReadLevelSelectItem = UserProfileHelper.GetSelectListItemImgReadLEVEL(model.Edit?.READ_LEVEL?.ToString());
            ViewBag.PassportLevelSelectItem = UserProfileHelper.GetSelectListItemImgPassportLEVEL(model.Edit?.PASSPORT_LEVEL?.ToString());
            return View("AwatMana02", model);
        }

        /// <summary>
        /// 刪除資料處理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult AwatRealDelete(AWAI01EditViewModel model)
        {
            this.Shared(" - 修改");

            if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetEditSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            string Message = string.Empty;

            if (model.Edit.AWARD_NO == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error", new { error = "未傳入AWARD_NO" });
            }

            bool OK = Service.SaveAwatRealDelete(model, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "刪除完成";

                AWAI01IndexViewModel Qmodel = new AWAI01IndexViewModel()
                {
                    Search = model.Search
                };

                if (!string.IsNullOrEmpty(model.Search.unProduct))
                {
                    SetTitle(" - 兌換獎品一覽表(下架商品) ");
                }
                else
                {
                    SetTitle();
                }

                return View(model.Search.BackAction, Qmodel);
            }

            TempData["StatusMessage"] = Message;

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.Edit.SCHOOL_NO, null, user);
            ViewBag.ReadLevelSelectItem = UserProfileHelper.GetSelectListItemImgReadLEVEL(model.Edit?.READ_LEVEL?.ToString());
            ViewBag.PassportLevelSelectItem = UserProfileHelper.GetSelectListItemImgPassportLEVEL(model.Edit?.PASSPORT_LEVEL?.ToString());
            return View("AwatMana02", model);
        }

        public ActionResult AwatHide(AWAI01EditViewModel model)
        {
            this.Shared(" - 修改");

            if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetEditSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            string Message = string.Empty;

            if (model.Edit.AWARD_NO == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error", new { error = "未傳入AWARD_NO" });
            }

            bool OK = Service.SaveAwatHide(model, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "隱藏完成";

                AWAI01IndexViewModel Qmodel = new AWAI01IndexViewModel()
                {
                    Search = model.Search
                };

                if (!string.IsNullOrEmpty(model.Search.unProduct))
                {
                    SetTitle(" - 兌換獎品一覽表(下架商品) ");
                }
                else
                {
                    SetTitle();
                }

                return View(model.Search.BackAction, Qmodel);
            }

            TempData["StatusMessage"] = Message;

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.Edit.SCHOOL_NO, null, user);
            ViewBag.ReadLevelSelectItem = UserProfileHelper.GetSelectListItemImgReadLEVEL(model.Edit?.READ_LEVEL?.ToString());
            ViewBag.PassportLevelSelectItem = UserProfileHelper.GetSelectListItemImgPassportLEVEL(model.Edit?.PASSPORT_LEVEL?.ToString());
            return View("AwatMana02", model);

        }

        /// <summary>
        /// 存檔資料處理
        /// </summary>
        /// <param name="model"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ActionSaveAwat(AWAI01EditViewModel model, HttpPostedFileBase file)
        {
            if (model.Edit?.AWARD_NO == null)
            {
                this.Shared(" - 新增");

                if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetAddSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }
            }
            else
            {
                this.Shared(" - 修改");

                if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetEditSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }
            }

            if (model == null) model = new AWAI01EditViewModel();
            if (model.Search == null) model.Search = new AWAI01SearchViewModel();

            if (model.Edit.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
            {
                model.Edit.COST_CASH = model.Edit.BID_START_PRICE;
                model.Edit.QTY_STORAGE = 1;
            }
            else
            {
                model.Edit.BID_START_PRICE = null;
                model.Edit.BID_PER_UNIT_PRICE = null;
            }

            if (model.Edit?.AWARD_NO == null)
            {
                if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetAddSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }

                if (file == null || file.ContentLength == 0)
                {
                    ModelState.AddModelError("Edit.IMG_FILE", "請上傳獎品照片");
                }
                else
                {
                    string FileName = Path.GetFileName(file.FileName);
                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                    if (regexCode.IsMatch(FileName.ToLower()) == false)
                    {
                        ModelState.AddModelError("Edit.IMG_FILE", "請上傳 jpg|jpeg|png|gif|bmp 等格式");
                    }
                }

                //if (model.Edit.SDATETIME < DateTime.Now)
                //{
                //    ModelState.AddModelError("Edit.SDATETIME", "開始兌換需大於系統日");
                //}
            }
            else
            {
                if (PermissionService.GetPermission_Use_YN(model.Search.BackController, AWAI01SearchViewModel.SouTableVal.GetEditSave(model.Search.WhereSouTable), SCHOOL_NO, USER_NO) == "N")
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }
            }

            if (model.Edit.EDATETIME < DateTime.Now)
            {
                ModelState.AddModelError("Edit.EDATETIME", "兌換期限需大於系統日");
            }

            if (model.Edit.EDATETIME < model.Edit.SDATETIME)
            {
                ModelState.AddModelError("Edit.SDATETIME", "開始兌換不能大於兌換期限");
            }

            if (model.Edit.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
            {
                if ((model.Edit.BID_PER_UNIT_PRICE ?? 0) == 0)
                {
                    ModelState.AddModelError("Edit.BID_PER_UNIT_PRICE", "此欄位必輸");
                }

                if ((model.Edit.BID_START_PRICE ?? 0) <= 0)
                {
                    ModelState.AddModelError("Edit.BID_START_PRICE", "起標價要大於0");
                }

                if (model.Edit.BID_BUY_PRICE != null)
                {
                    if ((model.Edit.BID_START_PRICE ?? 0) > (model.Edit.BID_BUY_PRICE ?? 0))
                    {
                        ModelState.AddModelError("Edit.BID_BUY_PRICE", "直購價要大於起標價");
                    }
                }
            }

            // 檢查youtube影片格式是否正確
            if (!string.IsNullOrEmpty(model.Edit.VIDEO_PATH))
            {
                string youtube_url = model.Edit.VIDEO_PATH;
                string error;
                if (!LogicCenter.YoutubeUrlConvert(ref youtube_url, out error))
                {
                    ModelState.AddModelError("Edit.VIDEO_PATH", error);
                }
                model.Edit.VIDEO_PATH = youtube_url;
            }

            string Message = string.Empty;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = Service.SaveAwat(model, file, user, ref db, ref Message);

                if (OK)
                {
                    TempData["StatusMessage"] = "儲存完成";

                    AWAI01IndexViewModel Qmodel = new AWAI01IndexViewModel()
                    {
                        Search = model.Search
                    };

                    if (!string.IsNullOrEmpty(model.Search.unProduct))
                    {
                        SetTitle(" - 兌換獎品一覽表(下架商品) ");
                    }
                    else
                    {
                        SetTitle();
                    }

                    return RedirectToAction(model.Search.BackAction, Qmodel);
                }
            }

            TempData["StatusMessage"] = Message;

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.Edit.SCHOOL_NO, null, user);
            ViewBag.ReadLevelSelectItem = UserProfileHelper.GetSelectListItemImgReadLEVEL(model.Edit?.READ_LEVEL?.ToString());
            ViewBag.PassportLevelSelectItem = UserProfileHelper.GetSelectListItemImgPassportLEVEL(model.Edit?.PASSPORT_LEVEL?.ToString());

            if (model.Edit?.AWARD_NO == null)
            {
                return View("Awat02", model);
            }
            else
            {
                return View("AwatMana02", model);
            }
        }

        /// <summary>
        /// 兌換獎品資料處理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult AwatExchange02Start(AWAI01ExchangeViewModel model)
        {
            this.Shared(" - 確認 兌換/出價 - 獎品內容");

            if (user == null)
            {
                if (model.Search.SouController == "BarcCodeMyCash")
                {
                    TempData["StatusMessage"] = "未登入";
                    return RedirectToAction("Index", "BarcCodeMyCash", new { WhereSchoolNo = model.Search.WhereSCHOOL_NO });
                }
                else
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.SessionTimeOutError, "Error");
                }
            }

            if (model.Search.WhereAWARD_NO == null)
            {
                if (model.Search.SouController == "BarcCodeMyCash")
                {
                    TempData["StatusMessage"] = "未選取要對兌的獎品";
                    return RedirectToAction("Index", "BarcCodeMyCash", new { WhereSchoolNo = model.Search.WhereSCHOOL_NO });
                }
                else
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error");
                }
            }

            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);
            ViewBag.ImgUrl = Url.Content(GetImagePathUrl(model.Search.WhereSouTable));

            string Message = string.Empty;

            model = Service.GetProductExchangeData(model, ref db);

            if (model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && (model.THIS_PRICE_CASH ?? 0) <= 0)
            {
                ModelState.AddModelError("THIS_PRICE_CASH", Environment.NewLine + "我要出價要大於0");
            }

            if (model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && (model.THIS_PRICE_CASH ?? 0) <= model.AwaData.MAX_PRICE)
            {
                ModelState.AddModelError("THIS_PRICE_CASH", Environment.NewLine + $"最高出價已變動為{model.AwaData.MAX_PRICE}，您的出價為{model.THIS_PRICE_CASH}，請重新進入來出更高價");
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                Message = SCheckCxchange(model.AwaData, user, model.Search.WhereSouTable);

                if (string.IsNullOrWhiteSpace(Message) == false)
                {
                    ViewBag.NGError = "兌換/出價-失敗，原因:" + Message;
                    TempData["StatusMessage"] = Message;
                }
                else
                {
                    bool OK = false;
                    List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                    model  = Service.SaveExchange( model, user, ref db, ref Message,ref valuesList);
                    OK = model.Status;
                    if (OK)
                    {
                        TempData["StatusMessage"] = user.NAME + "兌換/出價-" + model.AwaData.AWARD_NAME + " 成功";

                        AWAI01IndexViewModel Qmodel = new AWAI01IndexViewModel()
                        {
                            Search = model.Search
                        };

                        SetTitle();

                        //更新顯示
                        UserProfile.RefreshCashInfo(user, ref db);
                        UserProfileHelper.Set(user);

                        EventGeneralCoordinatorNoticeToMail((int)model.AwaData.AWARD_NO, user.SCHOOL_NO, user.USER_NO);

                        if (model.Search.SouController == "BarcCodeMyCash")
                        {
                            return RedirectToAction("Index", "BarcCodeMyCash", new { WhereSchoolNo = model.Search.WhereSCHOOL_NO });
                        }
                        else
                        {
                            return View(model.Search.BackAction, Qmodel);
                        }
                    }

                    ViewBag.NGError = "兌換/出價-失敗，原因:" + Message;
                    TempData["StatusMessage"] = Message;
                }
            }

            TempData["StatusMessage"] = Message;
            return View("AwatExchange02", model);
        }

        /// <summary>
        /// 圖片路徑
        /// </summary>
        /// <param name="WhereSouTable"></param>
        /// <returns></returns>
        static public string GetImagePathUrl(string WhereSouTable)
        {
            try
            {
                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["ProductImgPath"];

                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\ProductIMG\";
                if (UploadImageRoot.StartsWith("~")) UploadImageRoot = UploadImageRoot.Replace("~", "");
                if (UploadImageRoot.EndsWith(@"\") == false && UploadImageRoot.EndsWith(@"/") == false) UploadImageRoot += @"\";
                if (UploadImageRoot.StartsWith(@"\") == false && UploadImageRoot.StartsWith(@"/") == false) UploadImageRoot = @"\" + UploadImageRoot;

                string imgUrl = string.Empty;

                if (WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
                {
                    imgUrl = "~" + UploadImageRoot + "TeachAwat" + @"\";
                }
                else
                {
                    imgUrl = "~" + UploadImageRoot;
                }

                string imgPath = System.Web.HttpContext.Current.Server.MapPath(imgUrl);
                if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

                return imgUrl;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static string CheckCxchange(AWAT02 award, UserProfile user = null)
        {
            Mapper.Initialize(cfg =>
            {
                cfg.CreateMap<AWAT02, AWAI01IndexListViewModel>();
            });
            AWAI01IndexListViewModel dAWAT = Mapper.Map<AWAI01IndexListViewModel>(award);

            return CheckCxchange(dAWAT, user, AWAI01SearchViewModel.SouTableVal.Student);
        }

        public static string CheckCxchange(AWAT09 award, UserProfile user = null)
        {
            Mapper.Initialize(cfg =>
            {
                cfg.CreateMap<AWAT09, AWAI01IndexListViewModel>();
            });
            AWAI01IndexListViewModel dAWAT = Mapper.Map<AWAI01IndexListViewModel>(award);

            return CheckCxchange(dAWAT, user, AWAI01SearchViewModel.SouTableVal.Teacher);
        }
        public static string CheckCxchange1(AWAI01IndexListViewModel award, UserProfile user = null, string WhereSouTable = null)
        {
            StringBuilder NgStr = new StringBuilder();
            if (DateTime.Now > award.EDATETIME)
            {
                NgStr.Append("兌換時間已超過");
            }
            else if (award.SDATETIME >= DateTime.Now)
            {
                NgStr.Append("兌換時間還沒到");
            }
            else if ((int)award.QTY_STORAGE <= 0)
            {
                NgStr.Append("已兌換完畢");
            }
            if (NgStr.Length > 0)
            {
                TagBuilder builer = new TagBuilder("a");
                builer.SetInnerText(NgStr.ToString());
                return builer.ToString();
            }
            else
            {
                return string.Empty;
            }
            if (NgStr.Length > 0)
            {
                TagBuilder builer = new TagBuilder("a");
                builer.SetInnerText(NgStr.ToString());
                return builer.ToString();
            }
            else
            {
                return string.Empty;
            }
        }
            /// <summary>
            /// 驗証
            /// </summary>
            /// <param name="award"></param>
            /// <param name="user"></param>
            /// <returns></returns>
            public static string CheckCxchange(AWAI01IndexListViewModel award, UserProfile user = null, string WhereSouTable = null)
        {
            StringBuilder NgStr = new StringBuilder();

            if (user == null)
            {
                user = UserProfileHelper.Get();

                if (user == null)
                {
                    NgStr.Append("無權兌換");
                }
            }

            if (user?.CASH < (int)award.COST_CASH)
            {
                int retCost = 0;
                if (user != null) {

                    retCost = (int)award.COST_CASH - (int)(user?.CASH??0);

                    if (retCost != 0)
                    {
                        NgStr.Append(string.Format("你的點數只有{0}點，不足{1}點", (int)(user?.CASH ?? 0),retCost));

                    }
                    else
                    {
                        NgStr.Append(string.Format("你的點數只有{0}點，不足{1}點", (int)(user?.CASH ?? 0), (int)award.COST_CASH));

                    }
                }
              
                else {
                    NgStr.Append(string.Format("你的點數只有{0}點，不足{1}點", 0, (int)award.COST_CASH));

                }
              
            }
            else if (award.READ_LEVEL != null && user.LEVEL_ID < award.READ_LEVEL)
            {
                NgStr.Append("閱讀認證等級不足");
            }
            else if (award.PASSPORT_LEVEL != null && user.LisTBookPassport.Count < award.PASSPORT_LEVEL)
            {
                NgStr.Append("閱讀護照等級不足");
            }
            else if (DateTime.Now > award.EDATETIME)
            {
                NgStr.Append("兌換時間已超過");
            }
            else if (award.SDATETIME >= DateTime.Now)
            {
                NgStr.Append("兌換時間還沒到");
            }
            else if ((int)award.QTY_STORAGE <= 0)
            {
                NgStr.Append("已兌換完畢");
            }
            else if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && user?.CASH < ((award.BID_PER_UNIT_PRICE ?? 0) + (int)award.COST_CASH))
            {
                NgStr.Append("點數不足");
            }

            if (NgStr.Length > 0)
            {
                TagBuilder builer = new TagBuilder("a");
                builer.SetInnerText(NgStr.ToString());
                return builer.ToString();
            }
            else
            {
                return string.Empty;
            }
        }

        public string SCheckCxchange(AWAI01IndexListViewModel award, UserProfile user, string WhereSouTable = null)
        {
            string NGError = CheckCxchange(award, user);

            if (string.IsNullOrWhiteSpace(NGError))
            {
                if (award.QTY_LIMIT != null)
                {
                    int ThisUserCount = db.AWAT03.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO && a.AWARD_NO == award.AWARD_NO && a.TRANS_STATUS != 9).Count();
                    if (ThisUserCount >= award.QTY_LIMIT)
                    {
                        NGError = "此獎品有限制領用數量，你已領用過此獎品";
                    }
                }
            }

            if (string.IsNullOrWhiteSpace(NGError))
            {
                if (user != null && award.BUY_PERSON_YN == "Y")
                {
                    string SouTable = AWAI01SearchViewModel.SouTableVal.GetSouTable(WhereSouTable);

                    if (db.REFT01.Where(A => A.REF_KEY == award.AWARD_NO.ToString()
                                   && A.REF_TABLE == SouTable
                                   && A.SCHOOL_NO == user.SCHOOL_NO
                                   && A.USER_NO == user.USER_NO).Any() == false)
                    {
                        NGError = "無權兌換";
                    }
                }
            }

            return NGError;
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = BreadcrumbService.GetBRE_NAME(SouBre_NO) + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = BreadcrumbService.GetBRE_NAME(SouBre_NO) + " - 兌換獎品一覽表 ";
            }
        }

        public void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (string.IsNullOrWhiteSpace(SouBre_NO))
            {
                SouBre_NO = "AWAI01";
            }

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = BreadcrumbService.GetBRE_NAME(SouBre_NO) + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = BreadcrumbService.GetBRE_NAME(SouBre_NO) + " - 兌換獎品一覽表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}