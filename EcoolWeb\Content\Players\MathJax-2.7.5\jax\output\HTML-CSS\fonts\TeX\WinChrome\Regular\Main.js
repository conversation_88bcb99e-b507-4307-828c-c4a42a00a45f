/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/WinChrome/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_WinChrome={directory:"WinChrome/Regular",family:"MathJax_WinChrome",testString:"> T d \u23A6 \u2A00",skew:{84:0.0278,58096:0.0319},32:[0,0,250,0,0],62:[540,40,778,83,694],84:[717,68,545,34,833],100:[694,11,511,101,567],160:[0,0,250,0,0],8899:[750,249,833,55,777],9126:[1155,644,667,0,347],10752:[949,449,1511,56,1454],58096:[720,69,644,38,947],58097:[587,85,894,96,797]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_WinChrome"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/WinChrome/Regular/Main.js"]);
