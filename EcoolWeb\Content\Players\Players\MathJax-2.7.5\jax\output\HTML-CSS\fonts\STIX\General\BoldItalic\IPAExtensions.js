/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/IPAExtensions.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{592:[473,14,512,13,492],593:[473,14,612,25,592],594:[473,14,612,25,592],595:[691,13,500,-14,449],596:[462,13,444,-5,392],597:[462,157,444,-5,406],598:[699,233,500,-21,517],599:[683,13,570,-21,653],600:[462,13,444,5,421],601:[462,13,444,5,398],602:[462,13,626,5,626],603:[475,14,444,5,482],604:[475,14,480,5,469],605:[475,14,689,5,689],606:[475,14,486,7,475],607:[462,207,367,-100,364],608:[683,245,720,-52,751],609:[472,245,549,-52,520],610:[462,11,561,21,544],611:[462,234,444,20,400],612:[450,10,493,10,488],613:[459,249,556,-13,498],614:[683,9,556,-13,498],615:[683,205,533,-13,475],616:[684,9,278,-10,262],617:[456,8,253,2,237],618:[462,0,304,-32,321],619:[699,9,320,9,368],620:[699,9,445,17,417],621:[699,233,291,-47,290],622:[699,236,623,2,585],623:[462,9,778,-14,723],624:[462,233,778,-14,723],625:[462,233,759,-14,704],626:[462,233,694,-109,632],627:[462,233,505,-6,486],628:[462,12,588,-27,614],629:[462,13,500,-3,441],630:[462,5,749,23,751],631:[477,2,685,-3,626],632:[685,231,691,-3,632],633:[462,0,427,0,410],634:[699,0,493,0,476],635:[462,233,436,0,417],636:[462,233,389,-87,389],637:[462,233,389,-47,389],638:[484,0,360,-21,417],639:[484,0,338,10,292],640:[464,0,498,8,515],641:[464,0,498,8,597],642:[462,218,389,-32,333],643:[683,233,424,-104,584],644:[683,207,394,-90,576],645:[470,233,415,79,344],646:[683,243,521,-40,641],647:[513,90,310,7,299],648:[594,233,311,-60,281],649:[462,9,556,-16,514],650:[452,8,500,15,552],651:[462,10,534,18,492],652:[462,13,444,15,401],653:[462,13,667,15,614],654:[667,0,444,16,502],655:[464,0,633,65,606],656:[449,218,440,-24,405],657:[449,97,411,-24,376],658:[450,236,499,-10,558],659:[450,307,499,-10,528],660:[685,0,530,25,520],661:[685,0,530,65,509],662:[669,14,487,25,453],663:[462,237,479,20,544],664:[680,17,723,13,734],665:[464,0,493,-10,486],666:[475,14,465,16,504],667:[538,11,580,29,690],668:[464,0,582,21,676],669:[685,233,475,-50,463],670:[457,250,500,22,528],671:[464,0,485,10,468],672:[582,205,488,1,674],673:[685,0,530,25,520],674:[685,0,530,65,507],675:[699,13,750,-21,735],676:[699,236,820,-21,813],677:[699,97,817,-21,743],678:[594,13,560,-3,524],679:[683,233,453,-30,670],680:[594,18,600,-3,618]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/IPAExtensions.js");
