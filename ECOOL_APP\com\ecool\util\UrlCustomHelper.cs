﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP
{
    public class UrlCustomHelper
    {
        /// <summary>
        /// 組出 檔案在網站的路徑 => http://xxx/img/aa.jpg
        /// </summary>
        /// <param name="VirtualPathStr"></param>
        /// <returns></returns>
        public static string Url_Content(string VirtualPathStr)
        {
            if (string.IsNullOrWhiteSpace(VirtualPathStr))
            {
                return string.Empty;
            }

            Uri contextUri = HttpContext.Current.Request.Url;

            var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
            contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port);

    

            return string.Format("{0}{1}", baseUri, VirtualPathUtility.ToAbsolute(VirtualPathStr));
        }


        public static string GetOwnWebUri()
        {
            Uri contextUri = HttpContext.Current.Request.Url;

            var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
            contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port);

            return baseUri;
        }


        public static string RemoveUrl_Content(string Url)
        {
            Uri contextUri = HttpContext.Current.Request.Url;

            var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
            contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port)+ HttpContext.Current.Request.ApplicationPath;
           
            //if(contextUri.Port==5321){
            //     baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
            // contextUri.Host, contextUri.Port == 5321 ? string.Empty : ":" + contextUri.Port) + HttpContext.Current.Request.ApplicationPath;
            // }
            string str = Url.Replace(baseUri, "~");
            if (contextUri.Port == 5321)
            {
                var baseUri1 = string.Format("{0}://{1}{2}", contextUri.Scheme,
           contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port);
                str = Url.Replace(baseUri1, "~/");
            }
            if (contextUri.Port == 5321)
            {
                str = Url.Replace("http://localhost:5321/", "~/");

            }
         
            return str;
        }
    }
}
