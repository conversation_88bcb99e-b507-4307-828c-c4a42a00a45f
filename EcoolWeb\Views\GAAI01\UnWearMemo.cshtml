﻿@model GAAI01WearIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("UnWearMemo", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.WhereWearModelType)
    @Html.HiddenFor(m => m.WhereALARM_ID)
    @Html.HiddenFor(m => m.WhereCLASS_NO)
    @Html.HiddenFor(m => m.WhereSYEARSEMESTER)
    @Html.HiddenFor(m => m.ALARM_DATE)
    @Html.HiddenFor(m => m.STUDENT_NUMBER)
    @Html.HiddenFor(m => m.<PERSON>ear<PERSON>)

    if (Model.USER_NOs?.Count() > 0)
    {
        foreach (var UserNo in Model.USER_NOs)
        {
            @Html.HiddenFor(m => m.USER_NOs, UserNo)
        }
    }

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">

                @*<div class="row">

                        <div class="col-md-6 col-sm-6 dl-horizontal-EZ">
                            <samp class="dt">
                                @Html.LabelFor(m => m.WhereCLASS_NO)
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(m => m.WhereCLASS_NO)
                            </samp>
                        </div>
                        <div class="col-md-6 col-sm-6  dl-horizontal-EZ ">
                            <samp class="dt">
                                @Html.LabelFor(m => m.WhereALARM_ID)
                            </samp>
                            <samp class="dd">
                                @Model.ALARM_DATE
                            </samp>
                        </div>
                    </div>*@
                <h4>
                    這是<strong style="color:red">@(Model.WhereCLASS_NO) 班</strong>，登記 <strong style="color:red"> @(Model.ALARM_DATE)</strong>，
                    @if (Model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.未配戴點選登記
                        || Model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.未配戴感應登記)
                    {
                        <strong style="color:blue">未配戴</strong>
                    }
                    else if (Model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.有配戴感應登記
                        || Model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.有配戴點選登記
                        || Model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.全部都配戴登記)
                    {
                        <strong style="color:blue">有配戴</strong>
                    }

                    防身警報器的結果，請再次確認喔。
                </h4>
                <hr class="hr-line-dashed" />

                @if (Model.Users?.Count() > 0)
                {

                    if (Model.Users.Count() == Model.Users.Where(a => a.IS_WEAR == true).Count())
                    {
                        <div class="text-center">
                            <strong style="color:red">再次確認，本班都有配帶。</strong>
                        </div>
                    }
                    else
                    {
                        <div class="text-center">
                            <strong style="color:red">說明:未配載預設原因為「@GAAT02_U.UnWearType.忘記帶.ToString()」，老師可以在本頁面更改原因。</strong>
                        </div>
                    }

                    <ul>
                        <li class="list-group-item clearfix">
                            <div class="row">
                                <div class="col-sm-1">
                                    座號
                                </div>
                                <div class="col-sm-3">
                                    姓名
                                </div>
                                <div class="col-sm-3">
                                    是否配載
                                </div>
                                <div class="col-sm-5">
                                    未配載原因
                                </div>
                            </div>
                        </li>

                        @if (Model.Users?.Count() > 0)
                        {
                            foreach (var item in Model.Users.OrderBy(x=>x.SEAT_NO).ToList())
                            {
                                @Html.Partial("_WearDetail", item)
                            }
                        }
                    </ul>
                }
            </div>
        </div>
    </div>

    <div class="text-center">
        <hr />
        <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
            取消
        </button>

        <button type="button" class="btn btn-default" onclick="onSave()">
            <span class="fa fa-check-circle" aria-hidden="true"></span>送出
        </button>
    </div>
}

@section Scripts {

    <script language="JavaScript">

        var targetFormID = '#form1';

        function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("UnWearMemoSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}