/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Latin/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Latin={directory:"Latin/Regular",family:"GyreTermesMathJax_Latin",testString:"\u00A0\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00B6\u00B8\u00BA\u00BB\u00BF\u00C0\u00C1",32:[0,0,250,0,0],160:[0,0,250,0,0],161:[459,226,333,96,203],162:[579,138,500,53,448],164:[602,-58,500,-22,522],166:[662,156,200,67,133],169:[686,14,760,30,730],170:[676,-349,276,4,270],171:[411,-33,500,57,468],182:[662,154,453,-22,450],184:[5,215,333,52,261],186:[676,-349,310,6,304],187:[411,-33,500,45,456],191:[458,226,444,30,376],192:[851,0,722,15,706],193:[851,0,722,15,706],194:[847,0,722,15,706],195:[835,0,722,15,706],196:[832,0,722,15,706],197:[896,0,722,15,706],198:[662,0,889,0,863],199:[676,215,667,28,633],200:[851,0,611,12,597],201:[851,0,611,12,597],202:[847,0,611,12,597],203:[832,0,611,12,597],204:[851,0,333,12,315],205:[851,0,333,18,321],206:[847,0,333,-6,340],207:[832,0,333,18,316],208:[662,0,722,16,685],209:[835,11,722,12,707],210:[851,14,722,34,688],211:[851,14,722,34,688],212:[847,14,722,34,688],213:[835,14,722,34,688],214:[832,14,722,34,688],216:[734,80,722,34,688],217:[851,14,722,14,705],218:[851,14,722,14,705],219:[847,14,722,14,705],220:[832,14,722,14,705],221:[851,0,722,22,703],222:[662,0,556,16,542],223:[683,9,500,12,468],224:[675,10,444,37,442],225:[675,10,444,37,442],226:[674,10,444,37,442],227:[643,10,444,37,442],228:[640,10,444,37,442],229:[690,10,444,37,442],230:[460,10,667,38,632],231:[460,215,444,25,412],232:[675,10,444,25,424],233:[675,10,444,25,424],234:[674,10,444,25,424],235:[640,10,444,25,424],236:[675,0,278,-13,253],237:[675,0,278,16,288],238:[674,0,278,-18,293],239:[640,0,278,-11,287],241:[643,0,500,16,485],242:[675,10,500,29,470],243:[675,10,500,29,470],244:[674,10,500,29,470],245:[643,10,500,29,470],246:[640,10,500,29,470],248:[551,112,500,29,470],249:[675,10,500,9,479],250:[675,10,500,9,479],251:[674,10,500,9,479],252:[640,10,500,9,479],253:[675,218,500,14,475],254:[683,217,500,5,470],255:[640,218,500,14,475],256:[809,0,722,15,706],257:[617,10,444,37,442],258:[851,0,722,15,706],259:[669,10,444,37,442],260:[674,245,722,15,706],261:[460,245,444,37,442],262:[851,14,667,28,633],263:[675,10,444,25,412],268:[847,14,667,28,633],269:[674,10,444,25,412],270:[847,0,722,16,685],271:[683,10,500,27,603],272:[662,0,722,16,685],273:[683,10,500,27,500],274:[809,0,611,12,597],275:[617,10,444,25,424],278:[833,0,611,12,597],279:[641,10,444,25,424],280:[662,245,611,12,597],281:[460,245,444,25,424],282:[847,0,611,12,597],283:[674,10,444,25,424],286:[851,14,722,32,709],287:[669,218,500,28,470],290:[676,281,722,32,709],291:[733,218,500,28,470],296:[835,0,333,1,331],297:[643,0,278,-28,302],298:[809,0,333,11,322],299:[617,0,278,-18,293],302:[662,245,333,18,315],303:[641,245,278,16,253],304:[833,0,333,18,315],306:[662,14,787,40,747],307:[641,218,535,40,504],310:[662,281,722,34,723],311:[683,281,500,7,505],313:[851,0,611,12,598],314:[872,0,278,19,283],315:[662,281,611,12,598],316:[683,281,278,19,257],317:[662,0,611,12,598],318:[683,0,278,19,361],321:[662,0,611,12,598],322:[683,0,278,15,264],323:[851,11,722,12,707],324:[675,0,500,16,485],325:[662,281,722,12,707],326:[460,281,500,16,485],327:[847,11,722,12,707],328:[674,0,500,16,485],330:[662,218,722,12,707],331:[460,218,500,16,424],332:[809,14,722,34,688],333:[617,10,500,29,470],336:[851,14,722,34,688],337:[676,10,500,29,511],338:[668,6,889,30,885],339:[460,10,722,30,690],340:[851,0,667,17,659],341:[675,0,333,5,335],342:[662,281,667,17,659],343:[460,281,333,5,335],344:[847,0,667,17,659],345:[674,0,333,5,335],346:[851,14,556,42,491],347:[675,10,389,51,348],350:[676,225,556,42,491],351:[459,215,389,51,348],352:[847,14,556,42,491],353:[674,10,389,34,348],354:[662,225,611,17,593],355:[579,215,278,13,279],356:[847,0,611,17,593],357:[713,10,278,13,313],360:[835,14,722,14,705],361:[643,10,500,9,479],362:[809,14,722,14,705],363:[617,10,500,9,479],366:[896,14,722,14,705],367:[690,10,500,9,479],368:[851,14,722,14,705],369:[676,10,500,9,505],370:[662,245,722,14,705],371:[450,245,500,9,479],376:[832,0,722,22,703],377:[851,0,611,9,597],378:[675,0,444,27,418],379:[833,0,611,9,597],380:[641,0,444,27,418],381:[847,0,611,9,597],382:[674,0,444,27,418],383:[683,0,333,20,383],402:[676,189,500,7,490],416:[771,14,722,34,688],417:[559,10,500,29,473],431:[771,14,706,14,706],432:[560,10,513,9,513],536:[676,281,556,42,491],537:[459,281,389,51,348],538:[662,281,611,17,593],539:[579,281,278,13,279],7840:[674,191,722,15,706],7841:[460,191,444,37,442],7842:[896,0,722,15,706],7843:[684,10,444,37,442],7844:[1006,0,722,15,706],7845:[794,10,444,37,442],7846:[1006,0,722,15,706],7847:[794,10,444,37,442],7848:[1020,0,722,15,706],7849:[808,10,444,37,442],7850:[983,0,722,15,706],7851:[771,10,444,37,442],7852:[847,191,722,15,706],7853:[674,191,444,37,442],7854:[1010,0,722,15,706],7855:[798,10,444,37,442],7856:[1010,0,722,15,706],7857:[798,10,444,37,442],7858:[984,0,722,15,706],7859:[772,10,444,37,442],7860:[984,0,722,15,706],7861:[772,10,444,37,442],7862:[851,191,722,15,706],7863:[669,191,444,37,442],7864:[662,191,611,12,597],7865:[460,191,444,25,424],7866:[896,0,611,12,597],7867:[684,10,444,25,424],7868:[835,0,611,12,597],7869:[643,10,444,25,424],7870:[1006,0,611,12,597],7871:[794,10,444,25,424],7872:[1006,0,611,12,597],7873:[794,10,444,25,424],7874:[1020,0,611,12,597],7875:[808,10,444,25,424],7876:[983,0,611,12,597],7877:[771,10,444,25,424],7878:[847,191,611,12,597],7879:[674,191,444,25,424],7880:[896,0,333,18,315],7881:[684,0,278,16,253],7882:[662,191,333,18,315],7883:[641,191,278,16,253],7884:[676,191,722,34,688],7885:[460,191,500,29,470],7886:[896,14,722,34,688],7887:[684,10,500,29,470],7888:[1006,14,722,34,688],7889:[794,10,500,29,470],7890:[1006,14,722,34,688],7891:[794,10,500,29,470],7892:[1020,14,722,34,688],7893:[808,10,500,29,470],7894:[983,14,722,34,688],7895:[771,10,500,29,470],7896:[847,191,722,34,688],7897:[674,191,500,29,470],7898:[851,14,722,34,688],7899:[675,10,500,29,473],7900:[851,14,722,34,688],7901:[675,10,500,29,473],7902:[896,14,722,34,688],7903:[684,10,500,29,473],7904:[835,14,722,34,688],7905:[643,10,500,29,473],7906:[771,191,722,34,688],7907:[559,191,500,29,473],7908:[662,191,722,14,705],7909:[450,191,500,9,479],7910:[896,14,722,14,705],7911:[684,10,500,9,479],7912:[851,14,706,14,706],7913:[675,10,513,9,513],7914:[851,14,706,14,706],7915:[675,10,513,9,513],7916:[896,14,706,14,706],7917:[684,10,513,9,513],7918:[835,14,706,14,706],7919:[643,10,513,9,513],7920:[771,191,706,14,706],7921:[560,191,513,9,513],7922:[851,0,722,22,703],7923:[675,218,500,14,475],7924:[662,191,722,22,703],7925:[450,218,500,14,475],7926:[896,0,722,22,703],7927:[684,218,500,14,475],7928:[835,0,722,22,703],7929:[643,218,500,14,475],64256:[683,0,600,31,650],64257:[683,0,556,31,521],64258:[683,0,556,32,521],64259:[683,0,827,31,792],64260:[683,0,827,31,792]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Latin"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/Regular/Main.js"]);
