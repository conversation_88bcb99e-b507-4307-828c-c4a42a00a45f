﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class OneApplyPersonViewModel
    {
        public string TEMP_USER_ID { get; set; }

        public string GAME_USER_TYPE { get; set; }

        /// <summary>
        ///數位學生證id/QR Code Id
        /// </summary>
        [DisplayName("*卡號")]
        [Required]
        public string GAME_USER_ID { get; set; }

        [DisplayName("*身份")]
        [Required]
        public string GAME_USER_TYPE_DESC { get; set; }

        /// <summary>
        ///全名
        /// </summary>
        [DisplayName("*全名")]
        [Required]
        public string NAME { get; set; }

        /// <summary>
        ///電話
        /// </summary>
        [DisplayName("電話")]
        public string PHONE { get; set; }

        /// <summary>
        /// 學校名稱 非加盟學校
        /// </summary>
        [DisplayName("學校名稱")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///性別
        /// </summary>
        [DisplayName("*性別")]
        public string SEX { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        public string STATUS { get; set; }
    }
}