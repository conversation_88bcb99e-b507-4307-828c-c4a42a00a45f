﻿@model IPagedList<GameLotteryPeopleViewModel>
@using ECOOL_APP.com.ecool.util
<div class="carousel-inner" role="listbox">
    @if (Model.Count() > 0)
    {
        <div class='item'>
            <ul class='list-award'>
                @foreach (var item in Model)
                {

                    <li class="clearfix">
                        <span>
                            @if (item.GAME_USER_TYPE == UserType.Student)
                            {
                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                            }
                            else
                            {
                                <text>卡片</text>
                            }
                        </span>
                        <small class="pull-right">
                            <span>
                                @if (item.GAME_USER_TYPE == UserType.Student)
                                {
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.GAME_USER_ID)
                                }
                            </span>
                            <span> @StringHelper.MaskName(item.NAME)</span>
                        </small>
                    </li>

                }
            </ul>
        </div>
    }
</div>