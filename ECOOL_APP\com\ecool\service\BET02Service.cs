﻿using com.ecool.service;
using com.ecool.sqlConnection;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using Microsoft.Security.Application;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace com.ecool.service
{
    public class BET02Service : ServiceBase
    {
        public string ErrorMsg;
        private int ErrorInt = 0;
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        #region 取得公告資料(無權限)

        /// <summary>
        /// Modify By Orin
        /// Modify Date :2015/05/18
        /// Description : 查詢公告
        /// </summary>
        /// <param name="collection">畫面上的欄位</param>
        /// <returns></returns>
        public static List<uBET02> USP_BET02_QUERY(FormCollection collection)
        {
            List<uBET02> list_data = new List<uBET02>();
            StringBuilder sb = new StringBuilder();
            ECOOL_DEVEntities db1 = new ECOOL_DEVEntities();
            DataTable dt;
            try
            {
               IQueryable<BET02> bet02Q= db1.BET02.AsQueryable();
                IQueryable<BET02_LANG> langQ = db1.BET02_LANG.AsQueryable();

                IQueryable<BET02_FILE> fangQ = db1.BET02_FILE.AsQueryable();

                sb.Append(" SELECT  BULLET_ID,SCHOOL_NO, ");
                sb.Append("        CLASS_TYPE,SUBJECT,CONTENT_TXT, ");
                sb.Append("        ISPUBLISH,S_DATE,E_DATE, ");
                sb.Append("        TOP_YN,CRE_PERSON,CRE_DATE, ");
                sb.Append("        CHG_PERSON,CHG_DATE,MEMO ");
                sb.Append(" FROM BET02 ");
                sb.Append(" WHERE 1 = 1 ");
                if (collection["BULLET_ID"] != null)
                {
                    string BULLET_ID = collection["BULLET_ID"];
                    bet02Q = bet02Q.Where(x => x.BULLET_ID == BULLET_ID);
                
                }

                if (collection["SCHOOL_NO"] != null)
                {
                    string schoolNo = collection["SCHOOL_NO"];
                    bet02Q = bet02Q.Where(x => x.BULLET_ID == schoolNo);

                  
                }

                if (collection["CLASS_TYPE"] != null)
                {
                    string CLASSTYE = collection["CLASS_TYPE"];
                    bet02Q = bet02Q.Where(x => x.BULLET_ID == CLASSTYE);
                  
                }

                if (collection["SUBJECT"] != null)
                {
                    string SUBJECT = collection["SUBJECT"];
                    langQ = langQ.Where(x => x.SUBJECT == SUBJECT);
                    
                }

                if (collection["CONTENT_TXT"] != null)
                {
                    string CONTENT_TXT = collection["CONTENT_TXT"];
                    langQ = langQ.Where(x => x.CONTENT_TXT == CONTENT_TXT);
                 
                }

                if (collection["ISPUBLISH"] != null)
                {
                    string ISPUBLISH = collection["ISPUBLISH"];
                    bet02Q = bet02Q.Where(x => x.ISPUBLISH == ISPUBLISH);

      
                }

                if (collection["S_DATE"] != null)
                {
                    DateTime SdateTime1 = new DateTime();
                    SdateTime1 = DateTime.Parse(collection["S_DATE"]);
                    bet02Q = bet02Q.Where(x => x.S_DATE == SdateTime1);
                   
                }

                if (collection["E_DATE"] != null)
                {
                    DateTime EdateTime1 = new DateTime();
                    EdateTime1 = DateTime.Parse(collection["E_DATE"]);
                    bet02Q = bet02Q.Where(x => x.E_DATE == EdateTime1);

                  
                }

                if (collection["TOP_YN"] != null)
                {
           
                    string TOP_YN = collection["TOP_YN"];
                    bet02Q = bet02Q.Where(x => x.TOP_YN == TOP_YN);
                }

                if (collection["CRE_PERSON"] != null)
                {
                    string CRE_PERSON = collection["CRE_PERSON"];
                    bet02Q = bet02Q.Where(x => x.CRE_PERSON == CRE_PERSON);
                 
                }

                if (collection["CHG_DATE"] != null)
                {

                    DateTime CHG_DATE1 = new DateTime();
                       CHG_DATE1 = DateTime.Parse(collection["CHG_DATE"]);
                    bet02Q = bet02Q.Where(x => x.CHG_DATE == CHG_DATE1);

                  
                }

                if (collection["MEMO"] != null)
                {
                    string MEMO = collection["MEMO"];
                    bet02Q = bet02Q.Where(x => x.MEMO == MEMO);
           
                }
                var result =
                   from b in bet02Q
                    join l in langQ on b.BULLET_ID equals l.BULLET_ID
                    join f in fangQ on l.BULLET_ID equals f.BULLET_ID
                   select new
                   {
                       BET02 = b,
                       LANG = l,
                       FIL= f
                   };
                var list = result.ToList();
                foreach (var dr in result)
                {
                    list_data.Add(new uBET02()
                    {
                        BULLET_ID = dr.BET02.BULLET_ID,
                        SCHOOL_NO = dr.BET02.SCHOOL_NO,
                        CLASS_TYPE = dr.BET02.CLASS_TYPE,
                        SUBJECT = dr.LANG.SUBJECT,
                        CONTENT_TXT = dr.LANG.CONTENT_TXT,
                        ISPUBLISH = dr.BET02.ISPUBLISH,
                        S_DATE =(DateTime) dr.BET02.S_DATE,
                        E_DATE = (DateTime) dr.BET02.E_DATE,
                        TOP_YN = dr.BET02.TOP_YN,
                        CRE_PERSON = dr.BET02.CRE_PERSON,
                        CRE_DATE = (DateTime)dr.BET02.CRE_DATE,
                        CHG_PERSON = dr.BET02.CHG_PERSON,
                        CHG_DATE = (DateTime)dr.BET02.CHG_DATE,
                        MEMO = dr.BET02.MEMO
                    });
                }
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }

        #endregion 取得公告資料(無權限)

        #region 取得公告資料(有資料權限)

        /// <summary>
        ///
        /// </summary>
        /// <param name="Form">表單資料</param>
        /// <param name="BULLET_ID">Get 傳BULLET_ID</param>
        /// <param name="User">UserProfile</param>
        /// <returns></returns>
        public static List<BT02AmdinViewModel> USP_BET02DTO_QUERY(FormCollection Form, UserProfile User, int page, int pageSize, ref int Count)
        {
            List<BT02AmdinViewModel> list_data = new List<BT02AmdinViewModel>();

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT  A.BULLET_ID,A.SCHOOL_NO,S.SHORT_NAME, ");
                sb.Append("        A.CLASS_TYPE,BL.CLASS_NAME,AL.SUBJECT,AL.CONTENT_TXT, ");
                sb.Append("        A.ISPUBLISH,A.S_DATE,A.E_DATE, ");
                sb.Append("        A.TOP_YN,A.CRE_PERSON,isnull(CR.NAME,'此建立人已不存') AS CRE_PERSON_NAME,A.CRE_DATE, ");
                sb.Append("        A.CHG_PERSON,CH.NAME AS CHG_PERSON_NAME,A.CHG_DATE,A.MEMO ");
                sb.Append(" FROM BET02 A  (NOLOCK) ");
                sb.Append(" INNER JOIN BET02_LANG AL  (NOLOCK) ON  A.BULLET_ID=AL.BULLET_ID ");
                sb.Append(" INNER JOIN BET01 B    (NOLOCK) ON A.CLASS_TYPE=B.CLASS_TYPE ");
                sb.Append(" INNER JOIN BET01_LANG BL  (NOLOCK) ON  A.CLASS_TYPE=BL.CLASS_TYPE AND AL.LANGUAGE_ID=BL.LANGUAGE_ID ");
                sb.Append(" LEFT OUTER JOIN HRMT01 CR  (NOLOCK) ON  A.CRE_PERSON=CR.USER_KEY  ");
                sb.Append(" LEFT OUTER JOIN HRMT01 CH  (NOLOCK) ON  A.CHG_PERSON=CH.USER_KEY  ");
                sb.Append(" LEFT OUTER JOIN BDMT01 S  (NOLOCK) ON  A.SCHOOL_NO=S.SCHOOL_NO ");
                sb.Append(" WHERE 1 = 1 ");

                if (User != null)
                {
                    if (SharedGlobal.HomeIndex != "ChildMonthIndex")
                    {
                        if (string.IsNullOrWhiteSpace(User.RoleID_Default) == false && User.RoleID_Default != "")
                        {
                            if (User.ROLE_TYPE == 3) //學校管理人員
                            {
                                sb.Append(" AND A.CLASS_TYPE = '2' "); //學校公告
                                sb.Append(" AND A.SCHOOL_NO = '" + User.SCHOOL_NO + "' "); //學校公告
                            }
                            else if (User.ROLE_TYPE == 1 || User.ROLE_TYPE == 2) //系統管理人員
                            {
                                sb.Append(" AND A.CLASS_TYPE = '1' "); //系統公告
                            }
                        }
                    }

                    if (string.IsNullOrWhiteSpace(User.LANGE) == false)
                    {
                        sb.Append(" AND AL.LANGUAGE_ID = '" + User.LANGE + "' ");
                    }
                }
                else
                {
                    sb.Append(" AND AL.LANGUAGE_ID = 'zh-TW' ");
                }

                if (string.IsNullOrWhiteSpace(Form["SCHOOL_NO"]) == false)
                {
                    sb.Append(" AND A.SCHOOL_NO = '" + Form["SCHOOL_NO"] + "' ");
                }

                if (string.IsNullOrWhiteSpace(Form["CLASS_TYPE"]) == false)
                {
                    sb.Append(" AND A.CLASS_TYPE = '" + Form["CLASS_TYPE"] + "' ");
                }

                if (string.IsNullOrWhiteSpace(Form["ISPUBLISH"]) == false)
                {
                    sb.Append(" AND A.ISPUBLISH = '" + Form["ISPUBLISH"] + "' ");
                }

                if (string.IsNullOrWhiteSpace(Form["SUBJECT"]) == false)
                {
                    sb.Append(" AND AL.SUBJECT like '%" + Form["SUBJECT"] + "%' ");
                }

                string ThisError = "";
                dt = new sqlConnection.sqlConnection().executeQueryBSqlDataReaderListPage(page, pageSize, sb.ToString(), sb.ToString(), "A.S_DATE DESC", ref Count, ref ThisError);
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new BT02AmdinViewModel()
                    {
                        BULLET_ID = (dr["BULLET_ID"] == DBNull.Value ? "" : (string)dr["BULLET_ID"]),
                        SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]),
                        SHORT_NAME = (dr["SHORT_NAME"] == DBNull.Value ? "" : (string)dr["SHORT_NAME"]),
                        CLASS_TYPE = (dr["CLASS_TYPE"] == DBNull.Value ? "" : (string)dr["CLASS_TYPE"]),
                        CLASS_NAME = (dr["CLASS_NAME"] == DBNull.Value ? "" : (string)dr["CLASS_NAME"]),
                        SUBJECT = (dr["SUBJECT"] == DBNull.Value ? "" : (string)dr["SUBJECT"]),
                        CONTENT_TXT = (dr["CONTENT_TXT"] == DBNull.Value ? "" : (string)dr["CONTENT_TXT"]),
                        ISPUBLISH = (dr["ISPUBLISH"] == DBNull.Value ? "" : (string)dr["ISPUBLISH"]),
                        S_DATE = (dr["S_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["S_DATE"]),
                        E_DATE = (dr["E_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["E_DATE"]),
                        TOP_YN = (dr["TOP_YN"] == DBNull.Value ? "" : (string)dr["TOP_YN"]),
                        CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]),
                        CRE_PERSON_NAME = (dr["CRE_PERSON_NAME"] == DBNull.Value ? "" : (string)dr["CRE_PERSON_NAME"]),
                        CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]),
                        CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]),
                        CHG_PERSON_NAME = (dr["CHG_PERSON_NAME"] == DBNull.Value ? "" : (string)dr["CHG_PERSON_NAME"]),
                        CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]),
                        MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]),
                    });
                }
                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="Form">表單資料</param>
        /// <param name="BULLET_ID">Get 傳BULLET_ID</param>
        /// <param name="User">UserProfile</param>
        /// <returns></returns>
        public static List<BT02AmdinViewModel> USP_BET02DTO_QUERY4Users(FormCollection Form, UserProfile User, int page, int pageSize, ref int Count)
        {
            List<BT02AmdinViewModel> list_data = new List<BT02AmdinViewModel>();

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT  A.BULLET_ID,A.SCHOOL_NO,S.SHORT_NAME, ");
                sb.Append("        A.CLASS_TYPE,BL.CLASS_NAME,AL.SUBJECT,AL.CONTENT_TXT, ");
                sb.Append("        A.ISPUBLISH,A.S_DATE,A.E_DATE, ");
                sb.Append("        A.TOP_YN,A.CRE_PERSON,CR.NAME AS CRE_PERSON_NAME,A.CRE_DATE, ");
                sb.Append("        A.CHG_PERSON,CH.NAME AS CHG_PERSON_NAME,A.CHG_DATE,A.MEMO ");
                sb.Append(" FROM BET02 A  (NOLOCK) ");
                sb.Append(" INNER JOIN BET02_LANG AL  (NOLOCK) ON  A.BULLET_ID=AL.BULLET_ID ");
                sb.Append(" INNER JOIN BET01 B    (NOLOCK) ON A.CLASS_TYPE=B.CLASS_TYPE ");
                sb.Append(" INNER JOIN BET01_LANG BL  (NOLOCK) ON  A.CLASS_TYPE=BL.CLASS_TYPE AND AL.LANGUAGE_ID=BL.LANGUAGE_ID ");
                sb.Append(" LEFT OUTER JOIN HRMT01 CR  (NOLOCK) ON  A.CRE_PERSON=CR.USER_KEY  ");
                sb.Append(" LEFT OUTER JOIN HRMT01 CH  (NOLOCK) ON  A.CHG_PERSON=CH.USER_KEY  ");
                sb.Append(" LEFT OUTER JOIN BDMT01 S  (NOLOCK) ON  A.SCHOOL_NO=S.SCHOOL_NO ");
                sb.Append(string.Format(" WHERE A.S_DATE<='{0}' AND A.E_DATE>'{0}'", DateTime.Today.ToShortDateString()));
                sb.Append(string.Format(" AND ( A.CLASS_TYPE='1' or (A.CLASS_TYPE='2' and A.SCHOOL_NO='{0}') ) ", Form["SCHOOL_NO"]));

                if (User != null)
                {
                    if (string.IsNullOrWhiteSpace(User.LANGE) == false)
                    {
                        sb.Append(" AND AL.LANGUAGE_ID = '" + User.LANGE + "' ");
                    }
                }
                else
                {
                    sb.Append(" AND AL.LANGUAGE_ID = 'zh-TW' ");
                }

                //if (string.IsNullOrWhiteSpace(Form["SCHOOL_NO"]) == false)
                //{
                //    sb.Append(" AND A.SCHOOL_NO = '" + Form["SCHOOL_NO"] + "' ");
                //}

                if (string.IsNullOrWhiteSpace(Form["ISPUBLISH"]) == false)
                {
                    sb.Append(" AND A.ISPUBLISH = '" + Form["ISPUBLISH"] + "' ");
                }

                if (string.IsNullOrWhiteSpace(Form["SUBJECT"]) == false)
                {
                    sb.Append(" AND AL.SUBJECT like '%" + Form["SUBJECT"] + "%' ");
                }

                string ThisError = "";
                dt = new sqlConnection.sqlConnection().executeQueryBSqlDataReaderListPage(page, pageSize, sb.ToString(), sb.ToString(), "A.TOP_YN DESC,A.S_DATE DESC", ref Count, ref ThisError);
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new BT02AmdinViewModel()
                    {
                        BULLET_ID = (dr["BULLET_ID"] == DBNull.Value ? "" : (string)dr["BULLET_ID"]),
                        SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]),
                        SHORT_NAME = (dr["SHORT_NAME"] == DBNull.Value ? "" : (string)dr["SHORT_NAME"]),
                        CLASS_TYPE = (dr["CLASS_TYPE"] == DBNull.Value ? "" : (string)dr["CLASS_TYPE"]),
                        CLASS_NAME = (dr["CLASS_NAME"] == DBNull.Value ? "" : (string)dr["CLASS_NAME"]),
                        SUBJECT = (dr["SUBJECT"] == DBNull.Value ? "" : (string)dr["SUBJECT"]),
                        CONTENT_TXT = (dr["CONTENT_TXT"] == DBNull.Value ? "" : (string)dr["CONTENT_TXT"]),
                        ISPUBLISH = (dr["ISPUBLISH"] == DBNull.Value ? "" : (string)dr["ISPUBLISH"]),
                        S_DATE = (dr["S_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["S_DATE"]),
                        E_DATE = (dr["E_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["E_DATE"]),
                        TOP_YN = (dr["TOP_YN"] == DBNull.Value ? "" : (string)dr["TOP_YN"]),
                        CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]),
                        CRE_PERSON_NAME = (dr["CRE_PERSON_NAME"] == DBNull.Value ? "" : (string)dr["CRE_PERSON_NAME"]),
                        CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]),
                        CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]),
                        CHG_PERSON_NAME = (dr["CHG_PERSON_NAME"] == DBNull.Value ? "" : (string)dr["CHG_PERSON_NAME"]),
                        CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]),
                        MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]),
                    });
                }
                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }

        #endregion 取得公告資料(有資料權限)

        #region 取得公告明細資料

        public BT02AmdinViewModel GetData(string BULLET_ID, UserProfile User)
        {
            BT02AmdinViewModel ReturnData = new BT02AmdinViewModel();
            ReturnData.Details_LANG = new List<uBET02_LANG>();
            ReturnData.Details_FILE = new List<uBET02_FILE>();
            
            StringBuilder sb = new StringBuilder();
            DataTable dt;
         

          
            try
            {
                BET02 BET02s = new BET02();

                var BET02TMEP= db.BET02.ToList();

                   BET02s = BET02TMEP.Where(x => HtmlUtility.ComputeSHA256(x.BULLET_ID) == BULLET_ID).FirstOrDefault();
                if (BET02s == null) {
                    BET02s = BET02TMEP.Where(x => HtmlUtility.ComputeSHA256(x.BULLET_ID) == HtmlUtility.ComputeSHA256(BULLET_ID)).FirstOrDefault();
                }

                ReturnData.BULLET_ID = BET02s.BULLET_ID;
                ReturnData.SCHOOL_NO = BET02s.SCHOOL_NO;
                ReturnData.CLASS_TYPE = BET02s.CLASS_TYPE;
                ReturnData.ISPUBLISH = BET02s.ISPUBLISH;
                ReturnData.S_DATE = BET02s.S_DATE;
                ReturnData.E_DATE = BET02s.E_DATE;
                ReturnData.TOP_YN = BET02s.TOP_YN;
                ReturnData.MEMO = BET02s.MEMO;
                ReturnData.LinkContext = BET02s.LinkContext;
                ReturnData.CRE_PERSON = BET02s.CRE_PERSON;
                ReturnData.PUSH_YN = BET02s.PUSH_YN;
                List<BET02_LANG> BET02LANGs = new List<BET02_LANG>();
                var BET02_LANGS = db.BET02_LANG.ToList();
                BET02LANGs = BET02_LANGS.Where(x => HtmlUtility.ComputeSHA256(x.BULLET_ID) == BULLET_ID).OrderByDescending(x=>x.LANGUAGE_ID).ToList();
                if (BET02LANGs == null || BET02LANGs.Count()==0)
                {
                    BET02LANGs = BET02_LANGS.Where(x => HtmlUtility.ComputeSHA256(x.BULLET_ID) == HtmlUtility.ComputeSHA256(BULLET_ID)).OrderByDescending(x => x.LANGUAGE_ID).ToList();
                }


                foreach (var item in BET02LANGs) {
                    ReturnData.Details_LANG.Add(new uBET02_LANG()
                    {
                        LANGUAGE_ID = item.LANGUAGE_ID,
                        SUBJECT = item.SUBJECT,
                        CONTENT_TXT = item.CONTENT_TXT
                    });
                }

                List<BET02_FILE> BET02_FILEs = new List<BET02_FILE>();
                var allFiles = db.BET02_FILE.ToList(); // 或先加個初步 Where 篩選再 ToList()

                BET02_FILEs = allFiles
                    .Where(x => HtmlUtility.ComputeSHA256(x.BULLET_ID) == BULLET_ID)
                    .OrderBy(x => x.ITEM_NO)
                    .ToList();
                if (BET02_FILEs == null || BET02_FILEs.Count() == 0)
                {
                    BET02_FILEs = allFiles
                      .Where(x => HtmlUtility.ComputeSHA256(x.BULLET_ID) == HtmlUtility.ComputeSHA256(BULLET_ID))
                      .OrderBy(x => x.ITEM_NO)
                      .ToList();
                }

                foreach (var  dr in BET02_FILEs)
                {
                    ReturnData.Details_FILE.Add(new uBET02_FILE()
                    {
                        BULLET_ID = dr.BULLET_ID,
                        ITEM_NO = dr.ITEM_NO,
                        FILE_NAME = dr.FILE_NAME
                    }
                    );
                }
               
                return ReturnData;
            }
            catch (Exception exception)
            {
                throw exception;
            }
        }

        #endregion 取得公告明細資料

        #region 新增資料

        public void CreateAddDate(BT02AmdinViewModel CreateDate, UserProfile User)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    CreateDate.BULLET_ID = GetNewBULLET_ID(CreateDate.SCHOOL_NO, cmd);

                    this.INSERT_INTO_BET02(conn, CreateDate, User, transaction);

                    this.INSERT_INTO_BET02_LANG(CreateDate, cmd, User);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "新增資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "新增資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 新增資料

        #region 修改資料處理

        public void UpDate(BT02AmdinViewModel Date, UserProfile User)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    this.UPDATE_SET_BET02(conn, Date, User, transaction);

                    this.DELETE_BET02_LANG(Date.BULLET_ID, cmd);

                    this.INSERT_INTO_BET02_LANG(Date, cmd, User);

                    this.FILEAddDate(Date.Details_FILE, Date.BULLET_ID, cmd);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "修改資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "修改資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 修改資料處理

        #region 刪除資料處理

        public void DelDate(string BULLET_ID)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    string sSQL;

                    sSQL = " Delete BET02 Where BULLET_ID='" + BULLET_ID + "' ";
                    cmd.CommandText = sSQL;
                    cmd.ExecuteNonQuery();

                    this.DELETE_BET02_LANG(BULLET_ID, cmd);

                    sSQL = "Delete BET02_FILE Where BULLET_ID='" + BULLET_ID + "' ";
                    cmd.CommandText = sSQL;
                    cmd.ExecuteNonQuery();

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "修改資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "修改資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 刪除資料處理

        #region 上傳檔案資料

        public void FILEAddDate(List<uBET02_FILE> CreateDate, string BULLET_ID, SqlCommand cmd)
        {
            string NUM = "1";

            string sSQL;
            sSQL = " SELECT ISNULL(MAX(RIGHT(A.ITEM_NO,3)),0) AS NUM ";
            sSQL = sSQL + " FROM BET02_FILE A ";
            sSQL = sSQL + "  WHERE 1=1 ";
            sSQL = sSQL + "  and BULLET_ID='" + BULLET_ID + "'  ";
            cmd.CommandText = sSQL;
            var drDate_Header = cmd.ExecuteReader();

            if (drDate_Header.HasRows == true)
            {
                while (drDate_Header.Read())
                {
                    NUM = drDate_Header["NUM"].ToString();
                }
            }
            drDate_Header.Close();
            drDate_Header.Dispose();

            foreach (var Temp in CreateDate)
            {
                int Now_ITEM_NO = Convert.ToInt32(NUM) / 1 + Convert.ToInt32(Temp.ITEM_NO) / 1;

                sSQL = " INSERT INTO BET02_FILE ";
                sSQL = sSQL + " (BULLET_ID,ITEM_NO,FILE_NAME) ";
                sSQL = sSQL + " VALUES ('" + Temp.BULLET_ID + "','" + Now_ITEM_NO + "','" + Temp.FILE_NAME + "') ";
                cmd.CommandText = sSQL;

                try
                {
                    cmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    ErrorInt++;
                    ErrorMsg = "INSERT INTO BET02_LANG 失敗;" + ex.Message;
                }
            }
        }

        #endregion 上傳檔案資料

        #region 上傳檔案資料刪除

        public void Del_FILE(string BULLET_ID, string ITEM_NO)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    string sSQL = "Delete BET02_FILE Where BULLET_ID='" + BULLET_ID + "' and ITEM_NO='" + ITEM_NO + "' ";
                    cmd.CommandText = sSQL;
                    cmd.ExecuteNonQuery();

                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "Delete BET02_FILE;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 上傳檔案資料刪除

        #region INSERT_INTO_BET02

        public void MERGE_TEMPHRMT_BySchool(string SCHOOL_NO, string USER_NO)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
            }
        }

        /// <summary>
        /// INSERT_INTO_BET02
        /// </summary>
        /// <param name="conn">SqlConnection</param>
        /// <param name="CreateDate">BT02AmdinViewModel</param>
        /// <param name="User">UserProfile</param>
        /// <param name="transaction">SqlTransaction</param>
        public void INSERT_INTO_BET02(SqlConnection conn, BT02AmdinViewModel CreateDate, UserProfile User, SqlTransaction transaction)
        {
            IDbCommand IDbcmd = new SqlCommand(@"INSERT INTO BET02 (BULLET_ID,SCHOOL_NO,CLASS_TYPE,ISPUBLISH,S_DATE,E_DATE,TOP_YN,CRE_PERSON,CRE_DATE,CHG_PERSON,CHG_DATE,MEMO,PUSH_YN)
                     VALUES (@BULLET_ID,@SCHOOL_NO,@CLASS_TYPE,@ISPUBLISH,@S_DATE,@E_DATE,@TOP_YN,@CRE_PERSON,@CRE_DATE,@CHG_PERSON,GETDATE(),@MEMO,@PUSH_YN)");
            IDbcmd.Connection = conn;
            IDbcmd.Transaction = transaction;

            IDbcmd.Parameters.Add(
                    (CreateDate.BULLET_ID == null)
                    ? new SqlParameter("@BULLET_ID", DBNull.Value)
                    : new SqlParameter("@BULLET_ID", CreateDate.BULLET_ID));

            IDbcmd.Parameters.Add(
                    (CreateDate.SCHOOL_NO == null)
                    ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
                    : new SqlParameter("@SCHOOL_NO", CreateDate.SCHOOL_NO));

            IDbcmd.Parameters.Add(
                    (CreateDate.CLASS_TYPE == null)
                    ? new SqlParameter("@CLASS_TYPE", DBNull.Value)
                    : new SqlParameter("@CLASS_TYPE", CreateDate.CLASS_TYPE));

            IDbcmd.Parameters.Add(
            (CreateDate.LinkContext == null)
            ? new SqlParameter("@LinkContext", DBNull.Value)
            : new SqlParameter("@LinkContext", CreateDate.LinkContext));

            IDbcmd.Parameters.Add(
                    (CreateDate.ISPUBLISH == null)
                    ? new SqlParameter("@ISPUBLISH", DBNull.Value)
                    : new SqlParameter("@ISPUBLISH", CreateDate.ISPUBLISH == "true" ? "Y" : "N"));

            IDbcmd.Parameters.Add(
                    (CreateDate.S_DATE == DateTime.MinValue)
                    ? new SqlParameter("@S_DATE", DBNull.Value)
                    : new SqlParameter("@S_DATE", CreateDate.S_DATE));

            IDbcmd.Parameters.Add(
                    (CreateDate.E_DATE == DateTime.MinValue)
                    ? new SqlParameter("@E_DATE", DBNull.Value)
                    : new SqlParameter("@E_DATE", CreateDate.E_DATE));

            IDbcmd.Parameters.Add(
                    (CreateDate.TOP_YN == null)
                    ? new SqlParameter("@TOP_YN", DBNull.Value)
                    : new SqlParameter("@TOP_YN", CreateDate.TOP_YN == "true" ? "Y" : "N"));

            IDbcmd.Parameters.Add(
                    (CreateDate.CRE_PERSON == null)
                    ? new SqlParameter("@CRE_PERSON", DBNull.Value)
                    : new SqlParameter("@CRE_PERSON", CreateDate.CRE_PERSON));

            IDbcmd.Parameters.Add(
                    (CreateDate.CRE_DATE == DateTime.MinValue)
                    ? new SqlParameter("@CRE_DATE", DBNull.Value)
                    : new SqlParameter("@CRE_DATE", CreateDate.CRE_DATE));

            IDbcmd.Parameters.Add(
                    (CreateDate.CHG_PERSON == null)
                    ? new SqlParameter("@CHG_PERSON", DBNull.Value)
                    : new SqlParameter("@CHG_PERSON", CreateDate.CHG_PERSON));

            IDbcmd.Parameters.Add(
                    (CreateDate.MEMO == null)
                    ? new SqlParameter("@MEMO", DBNull.Value)
                    : new SqlParameter("@MEMO", CreateDate.MEMO));

            IDbcmd.Parameters.Add(
                           (CreateDate.PUSH_YN == null)
                           ? new SqlParameter("@PUSH_YN", DBNull.Value)
                           : new SqlParameter("@PUSH_YN", CreateDate.PUSH_YN == "true" ? "Y" : "N"));

            try
            {
                IDbcmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt++;
                ErrorMsg = "INSERT INTO BET02 失敗;" + ex.Message;
            }
            finally
            {
                IDbcmd.Dispose();
            }
        }

        #endregion INSERT_INTO_BET02

        #region UPDATE_SET_BET02

        /// <summary>
        /// UPDATE_SET_BET02
        /// </summary>
        /// <param name="conn">SqlConnection</param>
        /// <param name="Item">BT02AmdinViewModel</param>
        /// <param name="User">UserProfile</param>
        /// <param name="transaction">SqlTransaction</param>
        public void UPDATE_SET_BET02(SqlConnection conn, BT02AmdinViewModel Item, UserProfile User, SqlTransaction transaction)
        {
            IDbCommand cmd = new SqlCommand(@" UPDATE BET02 set
                SCHOOL_NO=@SCHOOL_NO,CLASS_TYPE=@CLASS_TYPE,ISPUBLISH=@ISPUBLISH,S_DATE=@S_DATE,E_DATE=@E_DATE,TOP_YN=@TOP_YN,CHG_PERSON=@CHG_PERSON,CHG_DATE=@CHG_DATE,MEMO=@MEMO,PUSH_YN=@PUSH_YN,LinkContext=@LinkContext
                 Where BULLET_ID=@BULLET_ID");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Item.BULLET_ID == null)
            ? new SqlParameter("@BULLET_ID", DBNull.Value)
            : new SqlParameter("@BULLET_ID", Item.BULLET_ID));

            cmd.Parameters.Add(
            (Item.SCHOOL_NO == null)
            ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
            : new SqlParameter("@SCHOOL_NO", Item.SCHOOL_NO));

            cmd.Parameters.Add(
            (Item.CLASS_TYPE == null)
            ? new SqlParameter("@CLASS_TYPE", DBNull.Value)
            : new SqlParameter("@CLASS_TYPE", Item.CLASS_TYPE));

            cmd.Parameters.Add(
            (Item.ISPUBLISH == null)
            ? new SqlParameter("@ISPUBLISH", DBNull.Value)
            : new SqlParameter("@ISPUBLISH", Item.ISPUBLISH == "true" ? "Y" : "N"));

            cmd.Parameters.Add(
            (Item.S_DATE == DateTime.MinValue)
            ? new SqlParameter("@S_DATE", DBNull.Value)
            : new SqlParameter("@S_DATE", Item.S_DATE));

            cmd.Parameters.Add(
            (Item.LinkContext == null)
            ? new SqlParameter("@LinkContext", DBNull.Value)
            : new SqlParameter("@LinkContext", Item.LinkContext));
            cmd.Parameters.Add(
            (Item.E_DATE == DateTime.MinValue)
            ? new SqlParameter("@E_DATE", DBNull.Value)
            : new SqlParameter("@E_DATE", Item.E_DATE));

            cmd.Parameters.Add(
            (Item.TOP_YN == null)
            ? new SqlParameter("@TOP_YN", DBNull.Value)
            : new SqlParameter("@TOP_YN", Item.TOP_YN == "true" ? "Y" : "N"));

            cmd.Parameters.Add(
            (Item.CHG_PERSON == null)
            ? new SqlParameter("@CHG_PERSON", DBNull.Value)
            : new SqlParameter("@CHG_PERSON", Item.CHG_PERSON));

            cmd.Parameters.Add(
            (Item.CHG_DATE == DateTime.MinValue)
            ? new SqlParameter("@CHG_DATE", DBNull.Value)
            : new SqlParameter("@CHG_DATE", Item.CHG_DATE));

            cmd.Parameters.Add(
            (Item.MEMO == null)
            ? new SqlParameter("@MEMO", DBNull.Value)
            : new SqlParameter("@MEMO", Item.MEMO));

            cmd.Parameters.Add(
               (Item.PUSH_YN == null)
               ? new SqlParameter("@PUSH_YN", DBNull.Value)
               : new SqlParameter("@PUSH_YN", Item.PUSH_YN == "true" ? "Y" : "N"));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt++;
                ErrorMsg = "UPDATE_SET_BET02 失敗;" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion UPDATE_SET_BET02

        #region INSERT_INTO_BET02_LANG

        /// <summary>
        /// INSERT_INTO_BET02_LANG
        /// </summary>
        /// <param name="Date">BT02AmdinViewModel</param>
        /// <param name="cmd">SqlCommand</param>
        /// <param name="User">UserProfile</param>
        public void INSERT_INTO_BET02_LANG(BT02AmdinViewModel Date, SqlCommand cmd, UserProfile User)
        {
            string sSQL;

            foreach (var Temp in Date.Details_LANG)
            {
                //過濾XSS
                // Temp.CONTENT_TXT = Sanitizer.GetSafeHtmlFragment(Temp.CONTENT_TXT);
                //Temp.CONTENT_TXT = new HtmlUtility().Instance.SanitizeHtml("AA");

                // Temp.CONTENT_TXT = HttpUtility.HtmlDecode(Temp.CONTENT_TXT);

                // Sanitizer.ReferenceEquals
                Temp.CONTENT_TXT = HtmlUtility.SanitizeHtml(Temp.CONTENT_TXT);

                sSQL = " INSERT INTO BET02_LANG ";
                sSQL = sSQL + " (BULLET_ID,LANGUAGE_ID,SUBJECT,CONTENT_TXT,CRE_PERSON,CRE_DATE,CHG_PERSON,CHG_DATE) ";
                sSQL = sSQL + " VALUES ('" + Date.BULLET_ID + "','" + Temp.LANGUAGE_ID + "','" + Temp.SUBJECT + "','" + Temp.CONTENT_TXT + "' ";
                sSQL = sSQL + " ,'" + User.USER_KEY + "',GETDATE(),'" + User.USER_KEY + "',GETDATE()) ";
                cmd.CommandText = sSQL;

                try
                {
                    cmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    ErrorInt++;
                    ErrorMsg = "INSERT INTO BET02_LANG 失敗;" + ex.Message;
                }
            }
        }

        #endregion INSERT_INTO_BET02_LANG

        #region DELETE_BET02_LANG

        /// <summary>
        /// DELETE_BET02_LANG
        /// </summary>
        /// <param name="Date">BT02AmdinViewModel</param>
        /// <param name="cmd">SqlCommand</param>
        /// <param name="User">UserProfile</param>
        public void DELETE_BET02_LANG(string BULLET_ID, SqlCommand cmd)
        {
            string sSQL;

            sSQL = "DELETE BET02_LANG WHERE BULLET_ID='" + BULLET_ID + "'";
            cmd.CommandText = sSQL;

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt++;
                ErrorMsg = "DELETE_BET02_LANG 失敗;" + ex.Message;
            }
        }

        #endregion DELETE_BET02_LANG

        #region 取得新的BULLET_ID

        /// <summary>
        /// 取得得新的BULLET_ID
        /// </summary>
        /// <param name="SCHOOL_NO">學校代碼</param>
        /// <param name="cmd">SqlCommand</param>
        /// <returns></returns>
        public string GetNewBULLET_ID(string SCHOOL_NO, SqlCommand cmd)
        {
            try
            {
                string NUM = "001";
                //公告類別= 系統
                //BULLET_ID = 年 月 日+3 碼流水號
                //公告類別= 學校
                //BULLET_ID = SCHOOL_NO+ 年 月 日+3 碼流水號
                string sSQL;

                sSQL = " SELECT RIGHT('00'+CONVERT(nvarchar(3),ISNULL(MAX(RIGHT(A.BULLET_ID,3)),0)+1),3) AS NUM ";
                sSQL = sSQL + " FROM BET02 A ";
                sSQL = sSQL + "  WHERE 1=1 ";
                sSQL = sSQL + "  and CONVERT(varchar(10),A.CRE_DATE,112)='" + DateTime.Now.ToString("yyyyMMdd") + "'  ";

                if (string.IsNullOrWhiteSpace(SCHOOL_NO) == false)
                {
                    sSQL = sSQL + " and A.SCHOOL_NO='" + SCHOOL_NO + "'";
                }
                cmd.CommandText = sSQL;
                var drDate_Header = cmd.ExecuteReader();

                if (drDate_Header.HasRows == true)
                {
                    while (drDate_Header.Read())
                    {
                        NUM = drDate_Header["NUM"].ToString();
                    }
                }

                drDate_Header.Close();

                string BULLET_ID = string.Empty;

                if (string.IsNullOrWhiteSpace(SCHOOL_NO) == false)
                {
                    BULLET_ID = SCHOOL_NO + "_" + DateTime.Now.ToString("yyyyMMdd") + NUM;
                }
                else
                {
                    BULLET_ID = DateTime.Now.ToString("yyyyMMdd") + NUM;
                }

                return BULLET_ID;
            }
            catch (Exception ex)
            {
                ErrorInt++;
                ErrorMsg = " 取得新的BULLET_ID 失敗;" + ex.Message;
                return null;
            }
        }

        #endregion 取得新的BULLET_ID
    }
}