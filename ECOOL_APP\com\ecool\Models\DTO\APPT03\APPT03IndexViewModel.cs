﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class APPT03IndexViewModel
    {

        /// <summary>
        /// 原視窗按鈕ID
        /// </summary>
        public string BTN_ID { get; set; }


        /// <summary>
        /// 來源TABLE
        /// </summary>
        public string REF_TABLE { get; set; }

        /// <summary>
        /// 來源KEY 
        /// </summary>
        public string REF_KEY { get; set; }


        /// <summary>
        /// TABLE 資料狀態 
        /// </summary>
        public byte? STATUS { get; set; }

        /// <summary>
        /// 抬頭 
        /// </summary>
        public string Panel_Title { get; set; }

        /// <summary>
        /// 訊息 
        /// </summary>
       [DisplayName("訊息內容")]
        [Required]
        public string MESSAGE { get; set; }


        public string DATA_TYPE { get; set; }

        public APPT03IndexViewModel()
        {
            REF_TABLE = "APPT03";
        }
    }
}
