﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class ZZZI36ImpoertExcelViewModel
    {
        [DisplayName("學年")]
        public string SYEAR { get; set; }

        [DisplayName("學期")]
        public string SEMESTER { get; set; }

        public string OrdercColumn { get; set; }

        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ECOOL_APP.EF.Temp_Info_LOG> LogList;
        public IPagedList<ECOOL_APP.EF.Temp_ModifyInfo_LOG> ModifyInfoList;
        /// <summary>
        /// HRMT08 健康資料
        /// HRMT09 體適能資料
        /// </summary>
        [DisplayName("匯入資料種類")]
        public string ImportType { get; set; }
    }
}