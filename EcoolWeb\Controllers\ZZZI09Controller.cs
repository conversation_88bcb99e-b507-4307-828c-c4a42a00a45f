﻿using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using com.ecool.service;
using ECOOL_APP.com.ecool.service;
using MvcPaging;
using Dapper;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using EcoolWeb.Util;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;

namespace EcoolWeb.Controllers
{
    //[SessionExpire]
    public class ZZZI09Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string Title = "學習成果匯出";

        // GET: ZZZI09
        //[CheckPermission]
        public ActionResult Index()
        {
            ViewBag.Panel_Title = Title;
            this.Shared();
            ViewBag.SCHOOL_NO = user.SCHOOL_NO;

            return View();
        }
       
        public ActionResult ExportResultView1(ZZZI09ExportResultViewViewModel model)
        {
            this.Shared();

            string Message = string.Empty;
            // string classNO = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.USER_NO == model.User_No && x.USER_STATUS == UserStaus.Enabled).Select(x => x.CLASS_NO).FirstOrDefault();

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = Message });
            }

            if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = "您無權查看此學生資料，必需是「管理者」或「班導」或「本人」才有權查看" });
            }

            if (string.IsNullOrEmpty(model.User_No) == false)
            {
                HRMT01 st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.USER_NO == model.User_No).FirstOrDefault();
                if (st != null)
                {
                    ViewBag.Panel_Title = st.CLASS_NO + "班-" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";
                    ViewBag.Title = ViewBag.Panel_Title;
                }
                string classNO = st.CLASS_NO;
                model.Class_NO = classNO;
            }

            return View(model);
        }

        public ActionResult _SelectDiv(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE,string ReadYN)
        {
            ViewBag.Panel_Title = "選擇匯出功能";

            ViewBag.SCHOOL_NO = wSCHOOL_NO;
            ViewBag.USER_NO = wUSER_NO;
            ViewBag.CLASS_NO = wCLASS_NO;
            ViewBag.DATA_ANGLE_TYPE = wDATA_ANGLE_TYPE;

            user = EcoolWeb.Models.UserProfileHelper.Get();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            var ClassItems = HRMT01.GetClassListData(wSCHOOL_NO, ref db).AsQueryable();

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData || wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                ClassItems = ClassItems.Where(a => a.Value == wCLASS_NO).AsQueryable();
            }
            if (user.USER_TYPE == "S")
            {
                ClassItems = ClassItems.Where(a => a.Value == user.CLASS_NO).AsQueryable();
            }

            ViewBag.ClassItems = ClassItems;

            var USER_NOItems = HRMT01.GetUserNoListData(wSCHOOL_NO, "", ref db);

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                USER_NOItems = USER_NOItems.Where(a => a.Value == wUSER_NO).AsQueryable();
            }
            if (user.USER_TYPE == "S")
            {
                USER_NOItems = HRMT01.GetUserNoListData(wSCHOOL_NO, user.CLASS_NO, user.USER_NO, ref db).AsQueryable();
            }

            ViewBag.USER_NOItems = USER_NOItems;

            bool wIsQhisSchool = false;

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                var H01 = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO).FirstOrDefault();

                if (H01 != null)
                {
                    wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == H01.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

                    ViewBag.IDNO = H01.IDNO;

                    var SCHOOL_ITME = (from a in db.BDMT01
                                       join b in db.HRMT01 on new { a.SCHOOL_NO } equals new { b.SCHOOL_NO }
                                       where b.IDNO == H01.IDNO && b.USER_STATUS == UserStaus.Invalid
                                       select a).Select(x => new SelectListItem { Text = x.SHORT_NAME, Value = x.SCHOOL_NO }).Distinct().OrderBy(o => o.Value);

                    ViewBag.SCHOOL_NOItems = SCHOOL_ITME.ToList();
                }
            }

            ViewBag.wIsQhisSchool = wIsQhisSchool;

            return PartialView();
        }

        public ActionResult ExportResultView(ZZZI09ExportResultViewViewModel model)
        {
            this.Shared();

            string Message = string.Empty;
            // string classNO = db.HRMT01.Where(x => x.SCHOOL_NO == model.School_No && x.USER_NO == model.User_No && x.USER_STATUS == UserStaus.Enabled).Select(x => x.CLASS_NO).FirstOrDefault();

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = Message });
            }

            if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
            {
                return RedirectToAction("NotSeeDataError", "Error", new { error = "您無權查看此學生資料，必需是「管理者」或「班導」或「本人」才有權查看" });
            }
            if (string.IsNullOrEmpty(model.IDNO) == false) {
                List<HRMT01> st = db.HRMT01.Where(a => a.IDNO == model.IDNO).ToList();

                HRMT01 st1 = st.Where(a => a.SCHOOL_NO == model.School_No).FirstOrDefault();
                if (st1 != null)
                {
                    ViewBag.Panel_Title = st1.CLASS_NO + "班-" + st1.SEAT_NO + "號-" + st1.NAME + "-學習成果";
                    ViewBag.Title = ViewBag.Panel_Title;
                }
                string classNO = st1.CLASS_NO;
                model.Class_NO = classNO;
            }

            if (string.IsNullOrEmpty(model.User_No) == false && string.IsNullOrEmpty(model.User_No) == true)
            {
                HRMT01 st = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.USER_NO == model.User_No).FirstOrDefault();
                if (st != null)
                {
                    ViewBag.Panel_Title = st.CLASS_NO + "班-" + st.SEAT_NO + "號-" + st.NAME + "-學習成果";
                    ViewBag.Title = ViewBag.Panel_Title;
                }
                string classNO = st.CLASS_NO;
                model.Class_NO = classNO;
            }

            return View(model);
        }

        public ActionResult ShowSportsView(string WhereSCHOOL_NO, string WhereUSER_NO)
        {
            this.Shared();
            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            SECI06Service sECI06Service = new SECI06Service();

            SECI06IndexViewModel model = new SECI06IndexViewModel();
            model.WhereSCHOOL_NO = WhereSCHOOL_NO;
            model.WhereUSER_NO = WhereUSER_NO;

            model = sECI06Service.GetHealthData(model, user, ref db);
            SECI03Controller sECI03Controller = new SECI03Controller();
            model.TALLchart = sECI03Controller.GetTALLchart(model.Hrmt08List, 600, 3000, "TALLchart");
            model.WEIGHTchart = sECI03Controller.GetWEIGHTchart(model.Hrmt08List, 600, 3000, "WEIGHTchart");

            return PartialView(model);
        }

        public ActionResult ShowADDT14View(ZZZI09ShowADDT14ViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowADDT14ViewViewModel();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var Temp =
                        from a14 in db.ADDT14
                        join h01 in db.HRMT01 on new { a14.USER_NO, a14.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                        from h01 in h01_join.DefaultIfEmpty()
                        where a14.SCHOOL_NO == model.School_No && a14.APPLY_STATUS != "9" 
                        //&& h01.USER_STATUS != UserStaus.Disable && h01.USER_STATUS != UserStaus.Invalid
                        orderby a14.CREATEDATE descending
                        select a14;

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };

                    if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
                    {
                        TempData["StatusMessage"] = "校內表現 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                        return PartialView(model);
                    }
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE <= dateFromE_DATE);
                }

                model.ADDT14List = (Temp.ToList() ?? new List<ADDT14>());

                //組圖檔系統目錄路徑
                ViewBag.ImageUrl = new FileHelper().GetDirectorySysControllersFile(model.School_No, EcoolWeb.Controllers.ADDI06Controller.ImgPath);

                return PartialView(model);
            }
        }

        public ActionResult ShowADDT15View(ZZZI09ShowADDT15ViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowADDT15ViewViewModel();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var Temp =
                     from a15 in db.ADDT15
                     join h01 in db.HRMT01 on new { a15.USER_NO, a15.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                     from h01 in h01_join.DefaultIfEmpty()
                     where a15.SCHOOL_NO == model.School_No && a15.APPLY_STATUS != "9" 
                     //&& h01.USER_STATUS != UserStaus.Disable && h01.USER_STATUS != UserStaus.Invalid
                     orderby a15.CREATEDATE descending
                     select a15;

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };

                    if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
                    {
                        TempData["StatusMessage"] = "校外榮譽 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                        return PartialView(model);
                    }
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Temp = Temp.Where(a => a.CREATEDATE <= dateFromE_DATE);
                }

                model.ADDT15List = (Temp.OrderByDescending(x => x.CREATEDATE).ToList() ?? new List<ADDT15>());

                //組圖檔系統目錄路徑
                ViewBag.ImageUrl = new FileHelper().GetDirectorySysControllersFile(model.School_No, EcoolWeb.Controllers.ADDI07Controller.ImgPath);

                return PartialView(model);
            }
        }

        public ActionResult ShowAWAT01_LOGView(ZZZI09ShowAWAT01_LOGViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowAWAT01_LOGViewViewModel();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var Temp = (from aw01_log in db.AWAT01_LOG
                            join h01 in db.HRMT01 on new { aw01_log.SCHOOL_NO, aw01_log.USER_NO } equals new { h01.SCHOOL_NO, h01.USER_NO }
                            join P_h01 in db.HRMT01 on aw01_log.LOG_PERSON equals P_h01.USER_KEY into temp
                            from ds in temp.DefaultIfEmpty()
                            join P_REF in db.AWAT01_X_REF on new { aw01_log.SOURCE_TYPE } equals new { P_REF.SOURCE_TYPE } into temp_ref
                            from ds_ref in temp_ref.DefaultIfEmpty()
                            where aw01_log.SCHOOL_NO == model.School_No
                            select new uAWAT01_Detail_LOG
                            {
                                CLASS_NO = h01.CLASS_NO,
                                SEAT_NO = h01.SEAT_NO,
                                SCHOOL_NO = aw01_log.SCHOOL_NO,
                                USER_NO = aw01_log.USER_NO,
                                USER_TYPE = h01.USER_TYPE,
                                SOURCE_TYPE = aw01_log.SOURCE_TYPE,
                                SOURCE_NO = aw01_log.SOURCE_NO,
                                CASH_IN = aw01_log.CASH_IN,
                                LOG_TIME = aw01_log.LOG_TIME,
                                LOG_DESC = aw01_log.LOG_DESC,
                                CHART_DESC = ds_ref.CHART_DESC,
                                LOG_PERSON = aw01_log.LOG_PERSON,
                                LOG_PERSON_NAME = ds.NAME ?? aw01_log.LOG_PERSON,
                                USERNAME = h01.NAME,
                                SNAME = h01.SNAME,
                                SYEAR = aw01_log.LOG_TIME.Year - 1911,
                                SEMESTER = (byte)((aw01_log.LOG_TIME.Month >= 2 && aw01_log.LOG_TIME.Month < 8) ? 2 : 1),
                                AWAT01_CASH_AVAILABLE = aw01_log.AWAT01_CASH_AVAILABLE,
                            });

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };

                    if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
                    {
                        TempData["StatusMessage"] = "數位存摺 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                        return PartialView(model);
                    }
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Temp = Temp.Where(a => a.LOG_TIME >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Temp = Temp.Where(a => a.LOG_TIME <= dateFromE_DATE);
                }

                model.AWAT01_LOG_List = (Temp.ToList() ?? new List<uAWAT01_Detail_LOG>());

                return PartialView(model);
            }
        }

        /// <summary>
        ///  借閱類別統計表
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetBorrowColumnChart(SECI05IndexViewModel model)
        {
            var returnPoint = new List<Point>();

            foreach (var item in model.BorrowTypeQty)
            {
                returnPoint.Add(new Point
                {
                    Y = item.QTY
                    ,
                    Name = item.TYPE_NAME
                });
            }

            Data data = new Data(returnPoint.ToArray());

            Highcharts TempPreColumnChart = new Highcharts("TempBorrowColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
           })
           .SetTitle(new Title { Text = "借閱類別統計表" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category
                        ,
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借閱數量" }, AllowDecimals = false })
           .SetSeries(new[]
                   {
                        new Series {
                            Data = data,
                            Name = "借閱數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
           .SetLegend(new Legend { Enabled = false });

            chartsHelper.SetCopyright(TempPreColumnChart);

            return TempPreColumnChart;
        }

        public ActionResult _SEI05Partial(SECI05IndexViewModel model)
        {
            string Bre_NO = "ZZZI09";
            SECI05Service Service = new SECI05Service();
            ViewBag.BRE_NO = Bre_NO;
            //  ViewBag.Panel_Title = "學生閱讀狀況";
            //  this.Shared(ViewBag.Panel_Title);

            user = UserProfileHelper.Get();
            List<string> MyPanyStudent = new List<string>();

            //if (user == null)
            //{
            //    return RedirectToAction("SessionTimeOutError", "Error");
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            //{
            //    model.WhereSCHOOL_NO = user.SCHOOL_NO;
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO ?? user.TEACH_CLASS_NO;
            //}

            //if (user.USER_TYPE == UserType.Student)
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO;
            //    model.WhereUSER_NO = user.USER_NO;
            //}
            //else if (user.USER_TYPE == UserType.Parents)
            //{
            //    var Hr06 = HRMT06.GetMyPanyStudent(user, db);

            //    if (Hr06 != null)
            //    {
            //        if (Hr06.Count() > 0)
            //        {
            //            MyPanyStudent = Hr06.Select(a => a.STUDENT_USER_NO).ToList();
            //        }
            //    }

            //    if (MyPanyStudent.Count >= 1 && string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            //    {
            //        model.WhereUSER_NO = MyPanyStudent.FirstOrDefault();
            //    }
            //}

            //List<SelectListItem> SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.WhereSCHOOL_NO, null, user, true, null, null, false);

            //if (!HRMT24_ENUM.CheckQQutSchool(user))
            //{
            //    SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == model.WhereSCHOOL_NO).ToList();
            //}
            //ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            //var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            //if (!HRMT24_ENUM.CheckQAdmin(user))
            //{
            //    if (user.TEACH_CLASS_NO == null)
            //    {
            //        if (HRMT24_ENUM.IsOtherTeacher(user) == false)
            //        {
            //            ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //        }
            //    }
            //    else
            //    {
            //        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //    }
            //}
            //ViewBag.ClassItems = ClassItems;

            //List<SelectListItem> USER_NOItems = null;

            //if (user.USER_TYPE == UserType.Parents)
            //{
            //    USER_NOItems = HRMT01.GetUserNoListDataForP(model.WhereUSER_NO, user, db).ToListNoLock();
            //}
            //else
            //{
            //    USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db, true);
            //}

            //ViewBag.USER_NOItems = USER_NOItems;

            if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                model = Service.GetBorrowData(model, user, ref db);

                if (model.MyBorrow != null)
                {
                    model.BorrowColumnChart = GetBorrowColumnChart(model);
                    model.GradeQtyCharts = GetGradeQtyCharts(model.MyBorrow);
                }

                List<SelectListItem> GradeSeyearItem = new List<SelectListItem>();

                int InSchoolYear = Convert.ToInt32(model.WhereUSER_NO.Substring(0, 3));

                GradeSeyearItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSEYEAR) });

                foreach (var Item in Enum.GetValues(typeof(HRMT01.GradeVal)))
                {
                    GradeSeyearItem.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = (InSchoolYear + ((byte)Item - 1)).ToString(), Selected = (InSchoolYear + ((byte)Item - 1)).ToString() == model.WhereSEYEAR });
                }

                ViewBag.GradeSeyearItem = GradeSeyearItem;
            }

            return View(model);
        }

        /// <summary>
        /// 取得年級名稱
        /// </summary>
        /// <param name="grade"></param>
        /// <returns></returns>
        static public string ParserGrade(byte? grade)
        {
            if (grade.HasValue == false) return string.Empty;

            switch (grade.Value)
            {
                case (byte)HRMT01.GradeVal.In1Grade:
                    return "一年級";

                case (byte)HRMT01.GradeVal.In2Grade:
                    return "二年級";

                case (byte)HRMT01.GradeVal.In3Grade:
                    return "三年級";

                case (byte)HRMT01.GradeVal.In4Grade:
                    return "四年級";

                case (byte)HRMT01.GradeVal.In5Grade:
                    return "五年級";

                case (byte)HRMT01.GradeVal.In6Grade:
                    return "六年級";
            }

            return grade.Value.ToString();
        }

        /// <summary>
        /// 產生 個人借閱數量趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetGradeQtyCharts(SECI05MyBorrowViewModel Data)
        {
            if (Data == null)
            {
                return null;
            }

            List<int> GradeQtys = new List<int>();
            GradeQtys.Add(Data.QTY_GRADE_1);
            GradeQtys.Add(Data.QTY_GRADE_2);
            GradeQtys.Add(Data.QTY_GRADE_3);
            GradeQtys.Add(Data.QTY_GRADE_4);
            GradeQtys.Add(Data.QTY_GRADE_5);
            GradeQtys.Add(Data.QTY_GRADE_6);

            List<string> Grades = new List<string>();
            foreach (var Item in Enum.GetValues(typeof(HRMT01.GradeVal)))
            {
                Grades.Add(ParserGrade((byte)Item));
            }

            Highcharts GradeQtyChart = new Highcharts("GradeQtyChart");

            GradeQtyChart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = "個人借閱數量趨示圖", X = 0 })
            .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = Grades.ToArray() })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "本" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "本" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 {
                                     Name ="各年級借閱數量",
                                   Data = new DotNet.Highcharts.Helpers.Data( GradeQtys.Cast<object>().ToArray())
                                 }
                        }
            );

            chartsHelper.SetCopyright(GradeQtyChart);

            return GradeQtyChart;
        }

        public ActionResult ShowCashPreView(ZZZI09ShowCashPreViewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI09ShowCashPreViewViewModel();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var TempHRMT01 = db.HRMT01.Where(B => B.SCHOOL_NO == model.School_No); 
                //&& (!UserStaus.NGUserStausList.Contains(B.USER_STATUS)));

                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(model.User_No) && string.IsNullOrWhiteSpace(model.IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == model.School_No && a.IDNO == model.IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { model.User_No };

                    if (UserProfileHelper.CheckSeeStudentData(user, model.School_No, model.User_No, ref db) == false)
                    {
                        TempData["StatusMessage"] = "點數統計 - " + ErrorHelper.ErrorVal.NotSeeDataErrorDesc();

                        return PartialView(model);
                    }
                }

                if (ArrUSER_NO.Length > 0)
                {
                    TempHRMT01 = TempHRMT01.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                var Log = from A in db.AWAT01_LOG
                          join B in TempHRMT01 on new { A.SCHOOL_NO, A.USER_NO } equals new { B.SCHOOL_NO, B.USER_NO }
                          select A;

                if (string.IsNullOrWhiteSpace(model.S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(model.S_DATE);
                    Log = Log.Where(a => a.LOG_TIME >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(model.E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(model.E_DATE);
                    Log = Log.Where(a => a.LOG_TIME <= dateFromE_DATE);
                }

                //各類別 酷幣點數，及比例
                model.PerData = (from A in Log
                                 join C in db.AWAT01_X_REF on new { A.SOURCE_TYPE } equals new { C.SOURCE_TYPE } into REF
                                 from X in REF.DefaultIfEmpty()
                                 group A by new
                                 {
                                     LOG_DESC = X.CHART_DESC != null ? X.CHART_DESC : A.LOG_DESC,
                                 } into g
                                 select new SECSharedCashPreViewModel
                                 {
                                     LOG_DESC = g.Key.LOG_DESC,
                                     SUM_ADD_CASH_ALL = g.Sum(a => a.ADD_CASH_ALL),
                                 }).Where(t => t.SUM_ADD_CASH_ALL > 0).OrderByDescending(t => t.SUM_ADD_CASH_ALL).ToList();

                model.CashPreColumnChart = new SECI02Controller().GetCashPreColumnChart(model.PerData);

                return PartialView(model);
            }
        }

        public ActionResult ShowHrmt08View(SECI03IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new SECI03IndexViewModel();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (string.IsNullOrWhiteSpace(model.whereIDNO))
                {
                    model.whereIDNO = db.HRMT01.Where(a => a.SCHOOL_NO == model.whereSCHOOL_NO && a.USER_NO == model.whereUSER_NO).Select(a => a.IDNO).FirstOrDefault();
                }

                IEnumerable<SECI03Hrmt08ListViewModel> QTemp = new SECI03Service().GetMyHrmt08Data(model.whereIDNO, model.whereSCHOOL_NO, ref db);
                IEnumerable<SECI03Hrmt09ListViewModel> Hrmt09 = new SECI03Service().GetMyHrmt09Data(model.whereIDNO, ref db);

                model.Hrmt08List = QTemp.ToPagedList(0, int.MaxValue);
                model.Hrmt09List = Hrmt09.Where(x => x.IDNO == model.whereIDNO).ToPagedList(0, int.MaxValue);
                SECI03Controller sECI03Controller = new SECI03Controller();

                model.TALLchart = sECI03Controller.GetTALLchart(model.Hrmt08List, 600, 1800, "TALLchart1");
                model.WEIGHTchart = sECI03Controller.GetWEIGHTchart(model.Hrmt08List, 600, 1800, "WEIGHTchart1");
                model.printTALLchart = sECI03Controller.GetTALLchart(model.Hrmt08List, 600, 1200, "PrintTALLchart");
                model.printWEIGHTchart = sECI03Controller.GetWEIGHTchart(model.Hrmt08List, 600, 1200, "PrintWEIGHTchart");
                model.RIGHT_VISIONColumnChart = sECI03Controller.GetRIGHT_VISION_ColumnChart(model.whereIDNO);
                model.LEFT_VISIONColumnChart = sECI03Controller.GetLEFT_VISION_ColumnChart(model.whereIDNO);
                model.FitnesschartV = sECI03Controller.GetFitnesschartV(model, 600, 1800, "FitnesschartV");
                model.printFitnesschartV = sECI03Controller.GetFitnesschartV(model, 600, 1200, "printFitnesschartV");
                model.FitnesschartSL = sECI03Controller.GetFitnesschartSL(model, 600, 1800, "Fitnesschart");
                model.printFitnesschartSL = sECI03Controller.GetFitnesschartSL(model, 600, 1200, "printFitnesschartSL");
                model.FitnesschartSU = sECI03Controller.GetFitnesschartSU(model, 600, 1800, "FitnesschartSU");
                model.printFitnesschartSU = sECI03Controller.GetFitnesschartSU(model, 600, 1200, "printFitnesschartSU");
                model.FitnesschartC = sECI03Controller.GetFitnesschartC(model, 600, 1800, "FitnesschartC");
                model.prinFitnesschartC = sECI03Controller.GetFitnesschartC(model, 600, 1200, "prinFitnesschartC");
                return PartialView(model);
            }
        }

        public ActionResult ShowADDT22View(ZZZI09ShowADDT22ViewViewModel model)
        {
            this.Shared();

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (model == null) model = new ZZZI09ShowADDT22ViewViewModel();

                string sSQL = @"select a.ART_GALLERY_NO ,a.ART_SUBJECT,a.WORK_TYPE,b.PHOTO_NO,b.PHOTO_FILE,b.PHOTO_SUBJECT
									 ,b.PHOTO_DESC,a.CRE_DATE,b.PHOTO_SCHOOL_NO,b.PHOTO_USER_NO
									 from  ADDT21 a (nolock)
									 join ADDT22 b (nolock) on a.ART_GALLERY_NO=b.ART_GALLERY_NO
                                     join HRMT01 c (nolock) on b.PHOTO_SCHOOL_NO=c.SCHOOL_NO and b.PHOTO_USER_NO=c.USER_NO
                                     where 1=1 and a.DEL_DATE is null ";

                if (!string.IsNullOrWhiteSpace(model.IDNO))
                {
                    sSQL = sSQL + @" and c.IDNO=@IDNO ";
                }
                else
                {
                    sSQL = sSQL + @" and b.PHOTO_SCHOOL_NO = @PHOTO_SCHOOL_NO
                                 and b.PHOTO_USER_NO = @PHOTO_USER_NO  ";
                }

                if (model.S_DATE != null)
                {
                    sSQL = sSQL + @" and CONVERT(nvarchar(10),a.CRE_DATE,111)>=@S_DATE ";
                }

                if (model.E_DATE != null)
                {
                    sSQL = sSQL + @" and CONVERT(nvarchar(10),a.CRE_DATE,111)<=@E_DATE ";
                }

                sSQL = sSQL + " order by a.CRE_DATE,b.CRE_DATE";
                model.ADDT22List = db.Database.Connection.Query<ZZZI09ShowADDT22PHOTOViewViewModel>(sSQL
               , new
               {
                   PHOTO_SCHOOL_NO = model.School_No,
                   PHOTO_USER_NO = model.User_No,
                   IDNO = model.IDNO,
                   S_DATE = model.S_DATE,
                   E_DATE = model.E_DATE,
               }).ToList();

                return PartialView(model);
            }
        }

        public ActionResult ShowADDT25_TREFView(ZZZI09ShowADDT25_TREFViewViewModel model)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                if (model == null) model = new ZZZI09ShowADDT25_TREFViewViewModel();

                var temp = from a in db.ADDT25_TREF
                           join b in db.HRMT01 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                           select new { Task = a, Hr = b };

                if (!string.IsNullOrWhiteSpace(model.IDNO))
                {
                    temp = temp.Where(a => a.Hr.IDNO == model.IDNO);
                }
                else
                {
                    temp = temp.Where(a => a.Hr.SCHOOL_NO == model.School_No && a.Hr.USER_NO == model.User_No);
                }

                if (model.S_DATE != null)
                {
                    var s_datetime = Convert.ToDateTime(model.S_DATE);
                    temp = temp.Where(a => a.Task.TASK_DATE >= s_datetime);
                }

                if (model.E_DATE != null)
                {
                    var e_datetime = Convert.ToDateTime(model.E_DATE);
                    temp = temp.Where(a => a.Task.TASK_DATE <= e_datetime);
                }

                model.Datalist = temp.Select(a => new ZZZI09ShowADDT25_TREFListViewModel()
                {
                    SCHOOL_NO = a.Task.SCHOOL_NO,
                    USER_NO = a.Task.USER_NO,
                    SOU_SCHOOL_NO = a.Task.SOU_SCHOOL_NO,
                    SOU_ITEM_NO = a.Task.SOU_ITEM_NO,
                    TASK_DATE = a.Task.TASK_DATE,
                    TASK_DESC = a.Task.TASK_DESC,
                    ANSWERS = a.Task.ANSWERS,
                    UPLOAD_FILES = a.Task.UPLOAD_FILES,
                }).ToList();

                model.Datalist = model.Datalist.Select(
                    a =>
                    {
                        a.FilePaths = ADDI11Service.GetFilePath(a.SCHOOL_NO, a.USER_NO, a.SOU_SCHOOL_NO, a.SOU_ITEM_NO, 2);
                        a.FileName = (a.UPLOAD_FILES ?? "").Split('|');
                        return a;
                    }
                    ).ToList();

                return PartialView(model);
            }
        }

        public ActionResult ShowCERT05View(ZZZI09ShowCERT05ViewModel model)
        {
            this.Shared();

            string Message = string.Empty;

            if (new SECSharedService().IsUseZZZI09(user, ref db, ref Message) == false)
            {
                TempData["StatusMessage"] = Message;
                return PartialView();
            }

            CER002Service cER002Service = new CER002Service();
            model.ListData = cER002Service.GetMyAccreditationListData(model.School_No, model.User_No, ref db).ToList();

            var cERT03G = db.CERT03_G.Where(x => x.SCHOOL_NO == model.School_No).ToListNoLock();

            if (cERT03G.Count > 0)
            {
                model.ListData.Select(a =>
                {
                    a.GRADE_SEMESTERs = cERT03G.Where(x => x.ACCREDITATION_ID == a.ACCREDITATION_ID && x.ITEM_NO == a.ITEM_NO)
                    .Select(x => $"{HRMT01.ParserGrade((byte)x.GRADE)}{HRMT01.ParserSemester((byte)x.SEMESTER)}").ToList();
                    return a;
                }).ToList();
            }

            return PartialView(model);
        }
        [HttpPost]
        public ActionResult _GetSCHOOL_NODDLHtml(string tagId, string tagName, string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE)
        {
            user = EcoolWeb.Models.UserProfileHelper.Get();

            this.Shared();
            IQueryable<SelectListItem> SCHOOLNONoItems = null;
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var H01 = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO).FirstOrDefault();

                var UserNoListData = HRMT01.GetUserNoListData(wSCHOOL_NO, wCLASS_NO, ref db);
               var wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == H01.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

                ViewBag.IDNO = H01.IDNO;


                UserNoListData = db.HRMT01.Where(a => a.IDNO == H01.IDNO && a.USER_TYPE == UserType.Student && a.USER_STATUS == UserStaus.Enabled && a.USER_STATUS ==UserStaus.Invalid)
              .OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO)
              .Select(x => new SelectListItem { Text = x.NAME + "/" + x.SEAT_NO, Value = x.USER_NO });

                
                string _html = string.Empty;
                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                   UserNoListData = UserNoListData.Where(a => a.Value == user.SCHOOL_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, H01.SCHOOL_NO, false, null);
                }
                else
                {
                    if (user.USER_TYPE == "S")
                    {
                        UserNoListData = UserNoListData.Where(a => a.Value == user.SCHOOL_NO);
                    }
                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, "", true, null);
                }

                return Content(_html);
            }
        }

        [HttpPost]
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE)
        {
            user = EcoolWeb.Models.UserProfileHelper.Get();

            this.Shared();

            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var UserNoListData = HRMT01.GetUserNoListData(wSCHOOL_NO, wCLASS_NO, ref db);

                string _html = string.Empty;
                if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                    UserNoListData = UserNoListData.Where(a => a.Value == wUSER_NO).AsQueryable();

                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, wUSER_NO, false, null);
                }
                else
                {
                    if (user.USER_TYPE == "S")
                    {
                        UserNoListData = UserNoListData.Where(a => a.Value == user.USER_NO).AsQueryable();
                    }
                    _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, "", true, null);
                }

                return Content(_html);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Shared

        private void Shared()
        {
            ViewBag.Panel_Title = Title;
            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                }
            }
        }

        #endregion Shared
    }
}