﻿@model GAAI01ClassWearIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<link href="~/Scripts/tablesorter/dist/css/theme.blue.css" rel="stylesheet" />
<link href="~/Scripts/tablesorter/dist/css/theme.green.min.css" rel="stylesheet" />
<link href="~/Scripts/tablesorter/dist/css/theme.blackice.min.css" rel="stylesheet" />
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "ClassWearIndex" });
}

@using (Html.BeginForm("ClassWearIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.IsSearch)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    <label class="col-md-3 control-label">
                        時間
                    </label>
                    <div class="col-md-9">
                        @if (ViewBag.AlarmSyearSemesterItem != null)
                        {
                            @Html.DropDownListFor(m => m.WhereSYEARSEMESTER, (IEnumerable<SelectListItem>)ViewBag.AlarmSyearSemesterItem, new { @class = "form-control" })
                        }
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereSYEARSEMESTER, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="button" class="btn btn-default" onclick="onSearch()">
                    <span class="fa fa-check-circle" aria-hidden="true"></span>搜尋
                </button>
            </div>
        </div>
    </div>

    if (Model.IsSearch == 1)
    {
        if (Model.dMT01 != null && Model.AvgClassData?.Count > 0)
        {
            <br />
            <div class="text-center">
                <div class="row">

                    <div class="col-md-12 text-center">
                        <button type="button" class="btn btn-default btn-xs no-print" onclick="onprint('#AvgClassDiv')">
                            列印
                        </button>
                    </div>
                </div>
                <div id="AvgClassDiv" class="text-center">
                    <h4>
                        @(Model.dMT01.SCHOOL_NAME)  學生配戴防身警報器  各班總平均資料(整學期平均)
                    </h4>

                    <table id="ClassListTable" class="tablesorter-blue">
                        <thead>
                            <tr class="text-center">
                                <td>班級</td>
                                <td>學生人數</td>
                                <td>平均配戴率</td>
                                <td>共完成幾週</td>
                                <td>目前第幾週</td>
                            </tr>
                        </thead>
                        @if (Model.AvgClassData?.Count > 0)
                        {
                            <tbody>
                                @foreach (var item in Model.AvgClassData)
                                {
                                    int STUDNUMber = 0;
                                    decimal wareRate = 0;
                                    STUDNUMber = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.CLASS_NO == item.CLASS_NO && x.USER_TYPE == "S" && x.USER_STATUS == UserStaus.Enabled).Count();

                                    <tr class="text-center">
                                        <td>@item.CLASS_NO</td>
                                        <td>@STUDNUMber</td>
                                        <td>
                                            @if (item.WEAR_RATE != null)
                                            {
                                                @item.WEAR_RATE.Value.ToString("#0%")
                                            }
                                        </td>
                                        <td>@item.OK_CYCLE</td>
                                        <td>@item.CYCLE</td>
                                    </tr>
                                }
                            </tbody>

                        }
                    </table>
                    <div class="text-center print-only">
                        <table style="width:95%">
                            <tr>
                                <td>填表人</td>
                                <td>主任</td>
                                <td>校長</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <br />
            </div>

            <hr class="hr-line-dashed" />
            <br />
            <div id="ClassReportedDataDiv">
                <div class="row no-print">
                    <div class="col-md-11 col-md-offset-1 text-center">
                        <button type="button" class="btn btn-default btn-xs" onclick="onprint('#ClassReportedDataTable')">
                            列印
                        </button>
                    </div>
                </div>
                <table id="ClassReportedDataTable" class="tablesorter-green">
                    <thead>
                        <tr class="text-center">
                            <td>日期</td>
                            <td>未填報班級數</td>
                            <td>有填報班級數</td>
                            <td>填報率</td>
                        </tr>
                    </thead>
                    @if (Model.gAAT01s?.Count > 0)
                    {
                        <tbody>
                            @foreach (var item in Model.gAAT01s)
                            {
                                var ClassReportedData = Model.ClassReportedData.Where(a => a.ALARM_ID == item.ALARM_ID).FirstOrDefault();

                                <tr class="text-center">
                                    <td nowrap="nowrap">@(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATES)) - @(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATEE))</td>

                                    @if (ClassReportedData != null)
                                    {
                                        <td>
                                            <button role="button" class="btn btn-link" type="button" onclick="onShowClassIsWearList('@Model.WhereSCHOOL_NO','@item.ALARM_ID',0) ">
                                                @ClassReportedData.NotReportedClassCount
                                            </button>
                                        </td>
                                        <td>
                                            <button role="button" class="btn btn-link" type="button" onclick="onShowClassIsWearList('@Model.WhereSCHOOL_NO','@item.ALARM_ID',1) ">
                                                @ClassReportedData.ReportedClassCount
                                            </button>
                                        </td>
                                        <td>@ClassReportedData.ReportedClassRate.Value.ToString("0%")</td>

                                    }
                                    else
                                    {
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                    }
                                </tr>
                            }
                        </tbody>

                    }
                </table>
            </div>
            <br />
            <div id="SchoolRateDiv">
                <div class="row no-print">
                    <div class="col-md-11 col-md-offset-1 text-center">
                        <button type="button" class="btn btn-default btn-xs" onclick="onprint('#SchoolRateTable')">
                            列印
                        </button>
                    </div>
                </div>
                <table id="SchoolRateTable" class="tablesorter-green">
                    <thead>
                        <tr class="text-center">
                            <td>日期</td>
                            <td>全校人數</td>
                            <td>未填報人數</td>
                            <td>配戴人數</td>
                            <td>未配戴人數</td>
                            <td>配戴率</td>
                        </tr>
                    </thead>
                    @if (Model.gAAT01s?.Count > 0)
                    {
                        <tbody>
                            @foreach (var item in Model.gAAT01s)
                            {
                                var SchoolRate = Model.SchoolRate.Where(a => a.ALARM_ID == item.ALARM_ID).FirstOrDefault();

                                <tr class="text-center">
                                    <td nowrap="nowrap">@(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATES)) - @(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATEE))</td>

                                    @if (SchoolRate != null)
                                    {
                                        <td>@SchoolRate.STUDENT_NUMBER</td>
                                        <td>@SchoolRate.NotReported_NUMBER</td>
                                        <td>@SchoolRate.WEAR_NUMBER</td>
                                        <td>@SchoolRate.UN_WEAR_NUMBER</td>
                                        <td>@SchoolRate.WEAR_RATE.Value.ToString("0%")</td>

                                    }
                                    else
                                    {
                                        <td>-</td>
                                        <td>-</td>
                                    }
                                </tr>
                            }
                        </tbody>

                    }
                </table>
            </div>
            <br />
            <div id="UnWearMemoCountDiv">
                <div class="row no-print">
                    <div class="col-md-11 col-md-offset-1 text-center">
                        <button type="button" class="btn btn-default btn-xs" onclick="onprint('#UnWearMemoCountTable')">
                            列印
                        </button>
                    </div>
                </div>
                <table id="UnWearMemoCountTable" class="tablesorter-green">
                    <thead>
                        <tr class="text-center">
                            <td>日期</td>
                            @foreach (var ItemUnWearType in Enum.GetValues(typeof(GAAT02_U.UnWearType)))
                            {
                                <td>@ItemUnWearType.ToString()</td>
                            }
                        </tr>
                    </thead>
                    @if (Model.gAAT01s?.Count > 0)
                    {
                        <tbody>
                            @foreach (var item in Model.gAAT01s)
                            {
                                var UnWearMemoCount = Model.UnWearMemoCount.Where(a => a.ALARM_ID == item.ALARM_ID).ToList();

                                <tr class="text-center">
                                    <td nowrap="nowrap">@(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATES)) - @(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATEE))</td>

                                    @if (UnWearMemoCount?.Count > 0)
                                    {
                                        foreach (var ItemUnWearType in Enum.GetValues(typeof(GAAT02_U.UnWearType)))
                                        {
                                            var UnWearMemoCountData = UnWearMemoCount.Where(a => a.ALARM_ID == item.ALARM_ID && a.UN_WEAR_TYPE == (GAAT02_U.UnWearType)ItemUnWearType).FirstOrDefault();

                                            if (UnWearMemoCountData != null)
                                            {
                                                <td>@(UnWearMemoCountData.WEAR_TYPE_STUDENT_NUMBER)人</td>
                                            }
                                            else
                                            {
                                                <td>0人</td>
                                            }
                                        }

                                    }
                                    else
                                    {
                                        foreach (var ItemUnWearType in Enum.GetValues(typeof(GAAT02_U.UnWearType)))
                                        {
                                            <td>-</td>
                                        }
                                    }
                                </tr>
                            }
                        </tbody>

                    }
                </table>
            </div>
        }
        else
        {
            <div class="text-center">
                <h2>
                    查無任何資料
                </h2>
            </div>
        }
    }
}
<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" id="myModal">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="modal-title"></h4>
            </div>
            <div class="modal-body">
                <div id="ShowClassIsWearList">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" id="ModalClose">Close</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/Scripts/printThis/printThis.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.widgets.js"></script>
    <script language="JavaScript">

        var targetFormID = '#form1';

        $(function() {
            $(".tablesorter-blue").tablesorter();
            $(".tablesorter-green").tablesorter();
        });

        function onSearch()
        {
            $('#@Html.IdFor(m=>m.IsSearch)').val(1)
            $(targetFormID).attr("action", "@Url.Action("ClassWearIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onprint(DivId) {
              $(DivId).printThis();
        }

         function onShowClassIsWearList(SCHOOL_NO,ALARM_ID,IsWear) {

            if (IsWear==1) {
                $('#modal-title').html('有填報班級')
            }
            else {
                $('#modal-title').html('未填報班級')
            }

             $.ajax({
                url: '@Url.Action("_ShowClassIsWearList", (string)ViewBag.BRE_NO)',
              data: {
                  SCHOOL_NO: SCHOOL_NO,
                  ALARM_ID: ALARM_ID,
                  IsWear: IsWear,
                        },
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#ShowClassIsWearList').html(data);
                }
            });

             if ($('#myModal').is(':visible') == false) {
                 $('#myModal').modal('show');
             }
        }
    </script>
}