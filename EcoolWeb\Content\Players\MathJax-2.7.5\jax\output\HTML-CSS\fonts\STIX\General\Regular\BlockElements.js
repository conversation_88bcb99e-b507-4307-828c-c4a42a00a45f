/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/BlockElements.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{9600:[910,-304,1213,0,1213],9604:[303,303,1213,0,1213],9608:[910,303,1213,0,1213],9612:[910,303,1212,0,606],9616:[910,303,1212,606,1212],9617:[860,258,1200,0,1200],9618:[874,273,1200,0,1200],9619:[874,273,1200,0,1200]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/BlockElements.js");
