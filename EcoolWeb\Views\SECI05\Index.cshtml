﻿@model SECI05IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }
}
<script src="~/Scripts/Pring.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.Partial("_SECI05Menu", 0)

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{

    <div class="form-inline" role="form" style="@(user.USER_TYPE == UserType.Student ? "display:none":"")">
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.WhereSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", @onchange = "SelectSCHOOL_NO(this.value)" })
        </div>

        @if (user.USER_TYPE != UserType.Parents)
        {
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeUSER_NOUseReplaceWith()" })
            </div>
        }

        <div class="form-group">
            <label class="control-label">姓名</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.WhereUSER_NO, (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

    if (string.IsNullOrWhiteSpace(Model.WhereUSER_NO))
    {
        <div class="text-center"><h3>請選擇查詢同學姓名</h3></div>
    }

    if (!string.IsNullOrWhiteSpace(Model.WhereUSER_NO))
    {
        <div class="form-inline">
            <div class="col-xs-12 text-right">
                <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            </div>
        </div>

        <div id="tbData">
            <div class="row">
                <div class="col-sm-3 col-xs-12 text-center">
                    <div class="text-center">
                        @if (!string.IsNullOrWhiteSpace(Model.MyBorrow != null ? Model.MyBorrow.PlayerUrl : ""))
                        {
                            <img src='@Url.Content(Model.MyBorrow.PlayerUrl)' style="max-height:200px" class="imgEZ" />
                        }
                        <br />
                        最後借書日期為：@(Model.MyBorrow != null ? Model.MyBorrow.LAST_BORROW_DATE != null ? Model.MyBorrow.LAST_BORROW_DATE.Value.ToString("yyyy/MM/dd") : "" : "")
                    </div>
                </div>

                <div class="col-sm-9 col-xs-12">
                    <div class="row">
                        <div class="col-sm-5 col-xs-12" style="background-color:lightskyblue;padding:5px 5px 5px 5px;">
                            <table class="table-ecool table-ecool-Bule-01-SEC" style="min-width:290px">
                                <thead>
                                    <tr>
                                        <td colspan="2" class="text-center">
                                            <h5>
                                                <strong>基本資料</strong>
                                            </h5>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            班級
                                        </td>
                                        <td class="text-center">
                                            @Html.DisplayFor(model => model.MyBorrow.CLASS_NO)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="css_td">
                                            姓名
                                        </td>
                                        <td class="text-center">
                                            @Html.DisplayFor(model => model.MyBorrow.NAME)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="css_td">
                                            在校借書量
                                        </td>
                                        <td class="text-center">
                                            @Html.DisplayFor(model => model.MyBorrow.ALL_QTY)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            本學期借書量
                                        </td>
                                        <td class="text-center">
                                            @Html.DisplayFor(model => model.MyBorrow.THIS_SESEM_QTY)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            本月份借書量
                                        </td>
                                        <td class="text-center">
                                            @Html.DisplayFor(model => model.MyBorrow.THIS_MM_QTY)
                                        </td>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div style="height:25px"></div>
            <div class="text-right">
                <strong>資料統計至@((DateTime.Now.AddDays(-1)).ToLongDateString().ToString())</strong>
            </div>
            <div class="row">
                <div class="col-sm-12 col-xs-12">
                    <table class="table-ecool table-ecool-Bule-SEC">
                        <thead>
                            <tr>
                                <th colspan="4">
                                    目前借閱書籍
                                </th>
                            </tr>
                            <tr>
                                <th>
                                    分類編號
                                </th>
                                <th>
                                    書名
                                </th>
                                <th>
                                    借閱時間
                                </th>
                                <th>
                                    備註
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @if ((Model.NowBorrowList != null ? Model.NowBorrowList.Count : 0) > 0)
                            {
                                foreach (var item in Model.NowBorrowList.Take(5))
                                {
                                    <tr class="text-center">
                                        <td title="分類編號">@item.BK_GRP</td>
                                        <td title="書名">@item.BKNAME</td>
                                        <td title="借閱時間">
                                            @if (item.BORROW_DATE != null)
                                            {
                                                @item.BORROW_DATE.ToString("yyyy/MM/dd")
                                            }
                                        </td>
                                        <td title="備註">
                                            @if (item.EXPIRED_DAY >= 14)
                                            {
                                                <font>借閱天數超過14天</font>
                                            }
                                        </td>
                                    </tr>
                                }

                            }
                            else
                            {
                                <tr class="text-center">
                                    <td colspan="4">暫時無正在借書的數量</td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr class="text-right">
                                <td colspan="4">
                                    <button type="button" class="colorbox btn btn-link-ez btn-xs" href='@Url.Action("BorrowList", (string)ViewBag.BRE_NO,new { WhereSCHOOL_NO=Model.WhereSCHOOL_NO, WhereUSER_NO=Model.WhereUSER_NO })'>more</button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div style="height:25px"></div>
            @if (Model.BorrowTypeQty != null)
            {
                SECI05BorrowTypeQtyViewModel Top1 = Model.BorrowTypeQty.OrderByDescending(a => a.Rate).FirstOrDefault();
                SECI05BorrowTypeQtyViewModel Top2=null;
                if ( Model.BorrowTypeQty.Count>1)
                {
                    Top2 = Model.BorrowTypeQty.OrderByDescending(a => a.Rate).Skip(1).FirstOrDefault();
                }

                if (Top1.Rate>=0.85)
                {
                    <div class="text-left" style="color:red;font-size:25px;margin-left:20px">
                        您的@(Top1.TYPE_NAME)：比例為@(Top1.Rate.ToString("P"))，<br />注意是否有閱讀偏食，建議多類別閱讀。
                    </div>
                }
                else
                {
                    <div class="text-left" style="color:red;font-size:25px;margin-left:20px">
                        你的閱讀量最高類別為@(Top1.TYPE_NAME)，比例為@(Top1.Rate.ToString("P"))；<br />
                        @if (Top2 != null)
                        {
                            <text> 第二高為  @(Top2.TYPE_NAME)：比例為@(Top2.Rate.ToString("P"))；</text> <br />
                        }
                        沒有閱讀偏食，請繼續多元閱讀，豐富人生。
                    </div>
                }

                <div class="row">
                    <div class="col-sm-12 col-xs-12">
                        <div class="text-right">
                            <div class="form-inline" role="form" id="search">
                                <div class="form-group">
                                    <label class="control-label">請選擇時間</label>
                                </div>
                                <div class="form-group">
                                    @Html.DropDownListFor(m => m.WhereSEYEAR, (IEnumerable<SelectListItem>)ViewBag.GradeSeyearItem, new { @class = "form-control input-sm", onchange = "form1.submit();" })
                                </div>
                            </div>
                        </div>
                        <table class="table-ecool table-ecool-Bule-SEC">
                            <thead>

                                <tr>
                                    <th>
                                    </th>
                                    @foreach (var item in Model.BorrowTypeQty)
                                    {
                                        <th>
                                            @item.TYPE_NAME
                                        </th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-center">
                                        <strong>數量</strong>
                                    </td>
                                    @foreach (var item in Model.BorrowTypeQty)
                                    {
                                        <td class="text-center">
                                            @item.QTY
                                        </td>
                                    }
                                </tr>
                                <tr>
                                    <td class="text-center">
                                        <strong>比例</strong>
                                    </td>
                                    @foreach (var item in Model.BorrowTypeQty)
                                    {
                                        <td class="text-center">
                                            @((item.Rate).ToString("P"))
                                        </td>
                                    }
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="text-right">
                                    <td colspan="@(Model.BorrowTypeQty.Count+1)">
                                        <button type="button" class="colorbox btn btn-link-ez btn-xs" href='@Url.Action("TypeList", (string)ViewBag.BRE_NO,new { WhereSEYEAR =Model.WhereSEYEAR,WhereSCHOOL_NO=Model.WhereSCHOOL_NO, WhereUSER_NO=Model.WhereUSER_NO })'>more</button>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            }

            <div style="height:25px"></div>
            <div class="row">
                <div class="col-sm-12">
                    @if (Model.BorrowColumnChart != null)
                    {
                        @Model.BorrowColumnChart
                    }
                </div>
            </div>
            <div style="height:25px"></div>
            <div class="row">
                <div class="col-sm-12">
                    @if (Model.GradeQtyCharts != null)
                    {
                        @Model.GradeQtyCharts
                    }
                </div>
            </div>
        </div>
    }

}

<script language="javascript">

    window.onload = function () {

        if ('@user.USER_TYPE' != '@UserType.Parents'  && '@user.USER_TYPE' != '@UserType.Student') {
            ChangeUSER_NOUseReplaceWith()
        }
    }

    $(document).ready(function () {
        $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });

    function PrintBooK() {
        $('#tbData').printThis();
    }

  function todoClear() {
        ////重設
        $("#form1").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }

    function SetUSER_NODDLEmpty() {
        $('#@Html.IdFor(m=>m.WhereUSER_NO)').empty();
        $('#@Html.IdFor(m => m.WhereUSER_NO)').append($('<option></option>').val(' ').text('請選擇' + $("label[for='Search_CLASS_NO']").text() + '...').prop('selected', true));
    }

    function ChangeUSER_NOUseReplaceWith() {

        var selectedSCHOOL_NO = $.trim($('#@Html.IdFor(m => m.WhereSCHOOL_NO) option:selected').val());
        var selectedCLASS_NO = $.trim($('#@Html.IdFor(m => m.WhereCLASS_NO) option:selected').val());
        var selectedUSER_NO = $.trim($('#@Html.IdFor(m => m.WhereUSER_NO) option:selected').val());

        if (selectedCLASS_NO.length == 0) {
            SetUSER_NODDLEmpty();
        }
        else {

            console.log("@ViewBag.from");
            $.ajax(
            {
                url: '@Url.Action("_GetUSER_NODDLHtml", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: '@Html.IdFor(m => m.WhereUSER_NO)',
                        tagName: '@Html.IdFor(m => m.WhereUSER_NO)',
                        SCHOOL_NO: selectedSCHOOL_NO,
                        CLASS_NO: selectedCLASS_NO,
                        USER_NO: selectedUSER_NO
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        if (data.length > 0) {
                            $('#@Html.IdFor(m => m.WhereUSER_NO)').replaceWith(data);
                        }
                    }
                });
        }
    }
</script>