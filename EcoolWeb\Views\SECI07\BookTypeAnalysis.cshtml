﻿@model ECOOL_APP.com.ecool.Models.DTO.BookTypeBorrowAnalysisViewModel
@using ECOOL_APP.com.ecool.Models.DTO
@using ECOOL_APP.com.ecool.LogicCenter
@using DotNet.Highcharts;
@using DotNet.Highcharts.Enums;
@using DotNet.Highcharts.Helpers;
@using DotNet.Highcharts.Options;

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

    var MaleList = Model.AnalysisListDictionary.FirstOrDefault(d => d.Key == BorrowAnalysisType.MaleBorrowList).Value;
    var FemaleList = Model.AnalysisListDictionary.FirstOrDefault(d => d.Key == BorrowAnalysisType.FemaleBorrowList).Value;

    var GradeList = Model.AnalysisListDictionary.FirstOrDefault(d => d.Key == BorrowAnalysisType.GradeBorrowList).Value;
    var MonthList = Model.AnalysisListDictionary.FirstOrDefault(d => d.Key == BorrowAnalysisType.MonthBorrowList).Value;
}

@functions{
    Highcharts MaleFemaleColumnChart(List<BookTypeBorrowAnalysis> MaleList, List<BookTypeBorrowAnalysis> FemaleList)
    {
        List<Series> Series = new List<Series>();
        Series.Add(new Series()
        {
            Data = new Data(MaleList.Select(s => new Point() { Y = s.QTY, Name = s.TYPE_NAME }).ToArray()),
            Name = "男生"
        });
        Series.Add(new Series()
        {
            Data = new Data(FemaleList.Select(s => new Point() { Y = s.QTY, Name = s.TYPE_NAME }).ToArray()),
            Name = "女生"
        });

        Highcharts GenderAnalysisChart = new Highcharts("GenderAnalysisChart")
           .InitChart(new DotNet.Highcharts.Options.Chart
           {
               DefaultSeriesType = ChartTypes.Column,
               BackgroundColor = new DotNet.Highcharts.Helpers.BackColorOrGradient(System.Drawing.Color.Transparent)
           })
           .SetTitle(new Title { Text = "借書類別分析(性別)" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category,
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借書量(本)" }, Min = 0, AllowDecimals = false })
           .SetSeries(Series.ToArray())
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
           .SetLegend(new Legend { Enabled = true });

        GenderAnalysisChart.SetCredits(new Credits { Enabled = false, Text = "", Href = "" });

        return GenderAnalysisChart;
    }

    Highcharts GenderPieChart(List<BookTypeBorrowAnalysis> List, Gender gender)
    {
        string genderName = LogicCenter.GetEnumName(gender);
        List<Series> Series = new List<Series>();
        Series.Add(new Series()
        {
            Data = new Data(List.Select(s => new Point() { Y = s.RATE * 100, Name = s.TYPE_NAME }).ToArray()),
            Name = genderName
        });

        Highcharts GenderAnalysisChart = new Highcharts("GenderPieChartFor" + genderName)
           .InitChart(new DotNet.Highcharts.Options.Chart
           {
               DefaultSeriesType = ChartTypes.Pie,
               BackgroundColor = new DotNet.Highcharts.Helpers.BackColorOrGradient(System.Drawing.Color.Transparent)
           })
           .SetTitle(new Title { Text = genderName + "借書類別比例" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category,
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借書量(本)" }, Min = 0, AllowDecimals = false })
           .SetSeries(Series.ToArray())
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} %</b>" })
           .SetLegend(new Legend { Enabled = true });

        GenderAnalysisChart.SetCredits(new Credits { Enabled = false, Text = "", Href = "" });

        return GenderAnalysisChart;
    }

    Highcharts GradeColumnChart(List<BookTypeBorrowAnalysis> List)
    {
        List<Series> Series = new List<Series>();
        List = List.OrderBy(s => s.GRADE).ToList();
        for (int i = 1; i<=6;i++)
        {
            // 篩選過分類的年級
            var gradeList = List.Where(s => s.GRADE == (int)i).ToList();

            Series.Add(new Series()
            {
                Data = new Data(gradeList.Select(s => new Point() { Y = s.QTY, Name = $"{s.GRADE}年級{s.TYPE_NAME}借書量" }).ToArray()),
                Name = i + "年級"
            });
        }


        Highcharts GradeAnalysisChart = new Highcharts("GradeAnalysisChart")
           .InitChart(new DotNet.Highcharts.Options.Chart
           {
               DefaultSeriesType = ChartTypes.Column,
               BackgroundColor = new DotNet.Highcharts.Helpers.BackColorOrGradient(System.Drawing.Color.Transparent)
           })
           .SetTitle(new Title { Text = "各年級借書類別分析" })
           .SetXAxis(new XAxis
           {
               Categories = ((BookType[])Enum.GetValues(typeof(BookType))).Select(s=>s.ToString()).ToArray(),
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借書量(本)" }, Min = 0, AllowDecimals = false })
           .SetSeries(Series.ToArray())
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
           .SetLegend(new Legend { Enabled = true });

        GradeAnalysisChart.SetCredits(new Credits { Enabled = false, Text = "", Href = "" });

        return GradeAnalysisChart;
    }


    Highcharts MonthLineChart(List<BookTypeBorrowAnalysis> List)
    {

        List<Series> Series = new List<Series>();
        foreach(var bookType in (BookType[])Enum.GetValues(typeof(BookType)))
        {
            var typeList = List.Where(s => s.TYPE_ID == (int)bookType).ToList();
            // 填滿月份
            for(int month = 1; month <= 12; month++)
            {
                if(!typeList.Any(s=>s.BORROW_MONTH == month))
                {
                    typeList.Add(new BookTypeBorrowAnalysis() { BORROW_MONTH = month, QTY = 0 });
                }
            }
            typeList = typeList.OrderBy(t => t.BORROW_MONTH).ToList();

            string bookTypeName = LogicCenter.GetEnumName(bookType);

            Series.Add(new Series()
            {
                Data = new Data(typeList.Select(s => new Point() { Y = s.QTY, Name = s.BORROW_MONTH + $"月{bookTypeName}借書量" }).ToArray()),
                Name = bookTypeName
            });
        }

        Highcharts MonthAnalysisChart = new Highcharts("MonthAnalysisChart")
           .InitChart(new DotNet.Highcharts.Options.Chart
           {
               DefaultSeriesType = ChartTypes.Line,
               BackgroundColor = new DotNet.Highcharts.Helpers.BackColorOrGradient(System.Drawing.Color.Transparent)
           })
           .SetTitle(new Title { Text = "借書類別月統計圖表" })
           .SetXAxis(new XAxis
           {
               Categories = new List<string> { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" }.ToArray()
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借書量(本)" }, Min = 0, AllowDecimals = false })
           .SetSeries(Series.ToArray())
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '14px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.0f} 本 </b>" })
           .SetLegend(new Legend { Enabled = true });

        MonthAnalysisChart.SetCredits(new Credits { Enabled = false, Text = "", Href = "" });

        return MonthAnalysisChart;
    }
}

<div class="containerEZ">
    @Html.Partial("_Title_Secondary")
    @{
        Html.RenderAction("_Menu", new { NowAction = "BookTypeAnalysis" });
    }

    @using (Html.BeginForm("BookTypeAnalysis", "SECI07", FormMethod.Post, new { id = "form1", name = "form1" }))
    {
        <div class="row">
            @Html.LabelFor(m => m.Where_SYEAR, new { @class = "col-md-12" })
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
            </div>
        </div>
    }
    <br />
    <div class="row">
        <div class="col-md-6 text-danger alert alert-danger">
            本學年總借書量(男): @MaleList.Sum(m => m.QTY) 本
        </div>
        <div class="col-md-6 text-danger alert alert-warning">
            本學年總借書量(女): @FemaleList.Sum(m => m.QTY) 本
        </div>
    </div>
    <div class="row" style="background-color:#ecf6ff">
        @this.MaleFemaleColumnChart(MaleList, FemaleList)
    </div>

    <div class="row" style="background-color:#e0efd6">
        <div class="col-md-6">
            @this.GenderPieChart(MaleList, Gender.男生)
        </div>
        <div class="col-md-6">
            @this.GenderPieChart(FemaleList, Gender.女生)
        </div>
    </div>

    <br />
    <div class="row">
        <div class="col-md-12 text-danger alert alert-info">
            各年級總借書量:
            <pre>
                @for (int i = 1; i<=6;i++)
                {
                    // 篩選過分類的年級
                    var gradeList = GradeList.Where(s => s.GRADE == (int)i).ToList();

                     @Html.Raw($"{i}年級: {gradeList.Sum(m => m.QTY)} 本　　")
                }
            </pre>
        </div>

    </div>
    <div class="row" style="background-color:#e2e1de">
        <div class="col-md-12">
            @this.GradeColumnChart(GradeList)
        </div>
    </div>

    <br />
    <div class="row">
        <div class="col-md-12 text-danger alert alert-info">
            本學年總借書量: @MonthList.Sum(m => m.QTY) 本
            <pre>
                @foreach (var bookType in (BookType[])Enum.GetValues(typeof(BookType)))
                {
                    var typeList = MonthList.Where(s => s.TYPE_ID == (int)bookType).ToList();

                    string bookTypeName = LogicCenter.GetEnumName(bookType);
                    @Html.Raw($"{bookTypeName}: {typeList.Sum(m => m.QTY)} 本　　")
                }
            </pre>
        </div>

    </div>

    <div class="row" style="background-color:#e2e1de">
        <div class="col-md-12">
            @this.MonthLineChart(MonthList)
        </div>
    </div>
</div>


@section scripts{
    <script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
    <script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
}