﻿/*
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'scayt', 'de', {
	btn_about: 'Über SCAYT',
	btn_dictionaries: 'Wö<PERSON>bücher',
	btn_disable: 'SCAYT ausschalten',
	btn_enable: 'SCAYT einschalten',
	btn_langs:'Sprachen',
	btn_options: 'Optionen',
	text_title: 'Rechtschreibprüfung während der Texteingabe (SCAYT)'
});
