﻿@model ECOOL_APP.EF.ADDT01
@using EcoolWeb.Util;
@using com.ecool.service
@{
    ViewBag.Title = "線上投稿-修改投稿內容";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    //判斷是否有[我要寫信給耶誕老人]權限
    ViewBag.VisibleSendChrisMail = PermissionService.GetPermission_Use_YN("ADDI01", "SendChrisMail", user?.SCHOOL_NO ?? "", user?.USER_NO ?? "");

}
@Html.Partial("_Title_Secondary")

@using (Html.BeginForm("Edit", "ADDI01", FormMethod.Post, new { name = "ADDI01Form", id = "ADDI01Form", enctype = "multipart/form-data", @AutoComplete = "Off" }))
{
    var Search = TempData["Search"] as EcoolWeb.Models.ADDI01IndexViewModel;

    @Html.Hidden("BackAction", Search.BackAction)
    @Html.Hidden("OrdercColumn", Search.OrdercColumn)
    @Html.Hidden("whereKeyword", Search.whereKeyword)
    @Html.Hidden("whereUserNo", Search.whereUserNo)
    @Html.Hidden("whereWritingStatus", Search.whereWritingStatus)
    @Html.Hidden("whereShareYN", Search.whereShareYN)
    @Html.Hidden("whereComment", Search.whereComment)
    @Html.Hidden("whereCommentCash", Search.whereCommentCash)
    @Html.Hidden("whereCLASS_NO", Search.whereCLASS_NO)
    @Html.Hidden("whereGrade", Search.whereGrade)
    @Html.Hidden("Page", Search.Page)
    @Html.Hidden("DeleteImage", "")
    @Html.Hidden("DeleteOtherFile", "")
    @Html.HiddenFor(m => m.WRITING_STATUS)
    @Html.Partial("_Notice")
    @Html.AntiForgeryToken()

    <img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Pink">
        <div class="form-horizontal">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })

            @Html.HiddenFor(model => model.WRITING_NO)
            @if (ViewBag.VisibleSendChrisMail == SharedGlobal.Y)
            {

                var checkBoxsCHRIS = new List<CheckBoxListInfo>();
                CheckBoxListInfo cheboxCHRISItem = new CheckBoxListInfo();
                cheboxCHRISItem.DisplayText = " ※<font color=\"#FF0000\">不會</font>真的寫電子信給聖誕老公公，只會將本文標誌和聖誕老公公有關，方便學校辦理活動匯出。(這是學校聖誕活動專用選項)";
                cheboxCHRISItem.Value = "Y";
                cheboxCHRISItem.IsChecked = Model.PUBLISH_CHRIS_YN == "Y" ? true : false;
                checkBoxsCHRIS.Add(cheboxCHRISItem);
                var htmlAttributeCHRIS = new Dictionary<string, object>();
                htmlAttributeCHRIS.Add("id", "PUBLISH_CHRIS_YN");

                <div class="form-group">
                    @Html.LabelFor(model => model.PUBLISH_CHRIS_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })

                    <div class="col-md-9 col-sm-6" style="padding-top:7px">
                        @Html.CheckBoxList("PUBLISH_CHRIS_YN", (List<CheckBoxListInfo>)checkBoxsCHRIS, htmlAttributeCHRIS, 1)
                    </div>
                </div>
            }
            else
            {
                @Html.HiddenFor(m => m.PUBLISH_CHRIS_YN)
            }
            <div class="form-group">
                @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control", @placeholder = "這是必須寫的欄位" } })
                    @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
                </div>
            </div>

            @*<div class="form-group">
            @Html.Label("不出現作者姓名", htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })
            <div class="col-md-12 col-sm-12 col-lg-10">
                <div class="col-md-9 col-sm-6" style="padding-top:7px">


                    @Html.CheckBox("AutherYN", Model.AutherYN ?? false)

                </div>
            </div>
        </div>*@

            <div class="form-group">
                @Html.Label("是否出現作者姓名", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6" style="padding-top:7px">
                    @Html.RadioButtonFor(model => model.AutherYN, false, new { @id = "AutherYN" })
                    @Html.Label("出現作者姓名")
                    @Html.RadioButtonFor(model => model.AutherYN, true, new { @id = "AutherYN" })
                    @Html.Label("不出現作者姓名")
                </div>
            </div>
            <div class="form-group">
                <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12">
                    詳細內容:限制1500字，目前字數為<span id="ShowFontLen" style="color:red"></span>字
                </label>
                @Html.LabelFor(model => model.ARTICLE, htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })

                <div class="col-md-9 col-sm-9 col-lg-10">
                    @Html.TextAreaFor(model => model.ARTICLE, new { cols = "200", rows = "10", @class = "form-control", @placeholder = "這是必須寫的欄位" })
                    @Html.ValidationMessageFor(model => model.ARTICLE, "", new { @class = "text-danger" })
                </div>
            </div>
            <label class="text-info">PS.圖檔必須「少」於六張</label>

            <div class="form-group">
                <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">上傳圖檔</span>
                <div class="col-md-9 col-sm-9 col-lg-10">
                    @Html.Action("MultiFileUpload", "Comm")
                </div>
            </div>
            @if (string.IsNullOrWhiteSpace(Model.IMG_FILE) == false)
            {
                <div class="form-group">
                    <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">原圖檔</span>
                    <div class="text-center">
                        <br />
                        <br />
                        <br />
                        @if (ViewBag.ImageUrl != null && Enumerable.Any(ViewBag.ImageUrl))
                        {
                            var images = ViewBag.Images as List<string>;
                            var orders = ViewBag.ImageOrders as List<string> ?? new List<string>();
                            int Num = 1;

                            foreach (var Img in ViewBag.ImageUrl as List<string>)
                            {
                                string ext = Path.GetExtension(Img);
                                @*if (ext == ".ppt" || ext == ".pptx" || ext == ".pdf" || ext == ".PDF")
                            {
                                string name = Path.GetFileName(Img);
                                <div style="position:relative">
                                    <br />
                                    <a href="@Img" class="btn btn-link btn-lg" target="_blank"><span class="glyphicon glyphicon-download-alt"></span> @name</a>
                                    <button type="button" class="btn btn-danger" style="position:absolute; right:35px; top:5px"
                                            onclick="DeleteMyImage($(this), '@images[Num - 1]')">
                                        ✖
                                    </button>
                                    <input type="number" name="Search.ImageOrders" value="@(images.Count == orders.Count ? orders[Num - 1] : "")" placeholder="排序" class="form-inline form-control" style="position:absolute; right:20px; top:40px;width:70px" />
                                </div>

                            }
                            else
                            {*@
                                <div style="position:relative">
                                    @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = Img, ImgID = "ImageX" + Num })

                                    <img id="@("ImageX" + Num)" src='@(Img + "?refreshCache=" + DateTime.Now.ToString("hhmmss"))' style="max-height:250px;width:auto;margin-left:auto;margin-right:auto;padding-bottom:10px" href="@Img" class="img-responsive " />
                                    <button type="button" class="btn btn-danger" style="position:absolute; right:35px; top:40px"
                                            onclick="DeleteMyImage($(this), '@images[Num - 1]')">
                                        ✖
                                    </button>
                                    <input type="number" name="Search.ImageOrders" value="@(images.Count == orders.Count&&orders[Num - 1]!="" ? orders[Num - 1] : Num.ToString())" placeholder="排序" class="form-inline form-control" style="position:absolute; right:20px; top:75px;width:70px" />
                                </div>

                                //}
                                <br />
                                <br />

                                Num = Num + 1;
                            }
                        }
                    </div>
                </div>
            }
            <div class="form-group">
                @Html.LabelFor(model => model.YOUTUBE_URL, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })
                <div class="col-md-10">

                    <div class="input-group">
                        @Html.EditorFor(model => model.YOUTUBE_URL, new { htmlAttributes = new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.YOUTUBE_URL) } })
                        <span class="input-group-btn">
                            <button class="btn btn-info" type="button" id="CheckYoutube">檢查網址</button>
                        </span>
                    </div><!-- /input-group -->

                    @Html.ValidationMessageFor(model => model.YOUTUBE_URL, "", new { @class = "text-danger" })
                </div>
                <br />
                <label class="text-info">PS.請輸入 有效Youtube 網址 ，https://www.youtube.com/watch?v=影片id，影片權限請公開 。</label>
            </div>
            @if (string.IsNullOrWhiteSpace(Model.Upload_FILE) == false)
            {
                <div class="form-group">
                    <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">原檔案</span>
                    <div class="text-center">
                        <br />
                        <br />
                        <br />
                        @if (ViewBag.OtherFilesUrl != null && Enumerable.Any(ViewBag.OtherFilesUrl))
                        {
                            var Otherfiles = ViewBag.Otherfiles as List<string>;
                            var orders = ViewBag.Upload_FILE_ORDER as List<string> ?? new List<string>();
                            int Num = 1;
                            foreach (var OtherFiles in ViewBag.OtherFilesUrl as List<string>)
                            {
                                string ext = Path.GetExtension(OtherFiles);
                                if (ext == ".ppt" || ext == ".pptx" || ext == ".pdf" || ext == ".PDF")
                                {
                                    string name = Path.GetFileName(OtherFiles);
                                    <div style="position:relative">
                                        <br />
                                        <a href="@OtherFiles" class="btn btn-link btn-lg" target="_blank"><span class="glyphicon glyphicon-download-alt"></span> @name</a>
                                        <button type="button" class="btn btn-danger" style="position:absolute; right:35px; top:5px"
                                                onclick="DeleteMyOtherFile($(this), '@Otherfiles[Num - 1]')">
                                            ✖
                                        </button>
                                        <input type="number" name="Search.OtherFilesOrders" value="@((Otherfiles.Count == orders.Count &&orders[Num - 1]!="") ? orders[Num - 1] :Num.ToString())" placeholder="排序" class="form-inline form-control" style="position:absolute; right:20px; top:40px;width:70px" />
                                    </div>
                                }
                                <br />
                                <br />
                                Num = Num + 1;
                            }
                        }
                    </div>
                </div>}
            @if (string.IsNullOrWhiteSpace(Model.BACK_MEMO) == false && (Model.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Back))
            {
                <label class="text-info">@Model.BACK_MEMO</label>
                <br />
            }

            <div class="form-group">
                <span class="control-label label_dt col-md-3col-sm-3 col-lg-2">上傳PPT或pdf(大小限制6MB)</span>
                <div class="col-md-12 col-sm-12 col-lg-10">
                    @Html.Action("MultiFiles2Upload", "Comm")
                </div>
            </div>
            @Html.Action("Index", "Audio")

            <div class="form-group">
                <div class="col-md-8 text-center">
                    @if (Model.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.DraftUnSubmit || Model.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Back)
                    {
                        <button type="button" class="btn btn-default" onclick="TempSave()">
                            暫存草稿
                        </button>
                    }
                    <button value="Save" class="btn btn-default btn-success">
                        確定送出
                    </button>

                    <button value="DisableUpSetDraft" onclick="DisableGO(this,'@Url.Action("Disable")')" class="btn btn-default btn-danger">
            直接作廢
        </button>
                   

                </div>

                <div class="col-md-offset-1 col-md-3">
                    <a href='@Url.Action("Index", "ADDI01"
                        , new {
                            BackAction = Search.BackAction,
                            OrdercColumn = Search.OrdercColumn,
                            whereKeyword = Search.whereKeyword,
                            whereUserNo = Search.whereUserNo,
                            whereWritingStatus = Search.whereWritingStatus,
                            whereShareYN = Search.whereShareYN,
                            whereComment = Search.whereComment,
                            whereCommentCash = Search.whereCommentCash,
                            whereCLASS_NO = Search.whereCLASS_NO,
                            whereGrade = Search.whereGrade,
                            Page = Search.Page
                    })' role="button" class="btn btn-default">
                        放棄編輯
                    </a>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI01_URLS = {
            getUrlArgument: "@(Url.Action("GetUrlArgument", "ADDI12"))"
        };
    </script>
    <script src="~/Scripts/ADDI01/edit.js" nonce="cmlvaw"></script>
}