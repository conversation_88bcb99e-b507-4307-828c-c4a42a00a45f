﻿@using System.Collections;
@using ECOOL_APP.com.ecool.Models.entity;
@{
    ViewBag.Title = "批次線上藝廊-檔案上傳";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    var checkBoxsISPersonal = new List<CheckBoxListInfo>();
    CheckBoxListInfo cheboxISPersonalItem = new CheckBoxListInfo();
    cheboxISPersonalItem.DisplayText = "是";
    cheboxISPersonalItem.Value = "Y";
    if (Model != null && Model.ISPersonal_YN != null)
    {
        cheboxISPersonalItem.IsChecked = Model.ISPersonal_YN == "Y" ? true : false;
    }

    checkBoxsISPersonal.Add(cheboxISPersonalItem);
    var htmlAttributeCHRIS = new Dictionary<string, object>();
    htmlAttributeCHRIS.Add("id", "ISPersonal_YN");

}  
@{   Html.RenderAction("_ArtGalleryMenu", new { NowAction = "MenuIndex" });}
@model ZZZI34WorkIndexViewModel
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
{
    @Html.HiddenFor(model => model.ZZZI34ImgType)
    @Html.HiddenFor(m => m.Search.WhereCLASS_NO)
    <div class="modal fade" id="myModal" role="dialog">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">上傳進度</h4>
            </div>
        </div>
        <div class="modal-body">
            <div class="progress progress-striped active">
                <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                    <span class="sr-only">40% 完成</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">

            <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
        </div>
    </div>
    <br />
    <img src="~/Content/img/web-bar2-revise-40.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="form-group">
                <label class="col-md-3 control-label">
                    <font color="red">*</font>
                    @Html.Label("藝廊名稱", htmlAttributes: new { @class = "control-label-left label_dt " })
                </label>
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        <input type="text" id="ART_SUBJECT" name="ART_SUBJECT" value="" class="form-control input-sm" />
                    </div>
            </div>

            <div class="form-group">
                <label class="col-md-3 control-label">
           
                    @Html.Label("藝廊描述", htmlAttributes: new { @class = "control-label-left label_dt" })
                     </label>
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        <samp class="dd">
                            <input type="text" id="Main_ART_DESC" name="Main_ART_DESC" value="" class="form-control input-sm" />

                            <input type="hidden" name="Main_ART_DESC">
                        </samp>
                    </div>
            </div>
            @*<div class="form-group">
                <label class="col-md-3 control-label">
                    <font color="red">*</font>
                    @Html.Label("檔名上傳類型", htmlAttributes: new { @class = "control-label-left label_dt " })
                </label>
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        <input type="radio" id="radUserNo" name="radFileType" onchange="FileType_onchange();" value="U" checked />學號
                        <input type="radio" id="radSeatNo" name="radFileType" onchange="FileType_onchange();" value="S" />座號
                    </div>
            </div>*@
            @*<div class="form-group">
                    @Html.Label("上傳個人相本", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">

                        @Html.CheckBoxList("ISPersonal_YN", (List<CheckBoxListInfo>)checkBoxsISPersonal, htmlAttributeCHRIS, 1)
                    </div>
                </div>*@
       
         
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        <font color="red">*</font>
                        <span class="control-label-left label_dt">封面圖片</span>
                        <span> <font color="red">(請選擇一張圖片，當此畫展的封面)</font></span>
                    </label>
                        <div class="col-md-8 col-sm-8 col-lg-8">
                            <input type="file" name="file2" id="file2" accept=".jpg,.jpeg,.png,.bmp" class="form-control" style="height:45px" />
                            <label class="text-info">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片</label>
                        </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        <font color="red">*</font>
                        <span class="control-label-left label_dt ">上傳藝廊作品的壓縮檔</span>
                    </label>
                        <div class="col-md-8 col-sm-8 col-lg-8">
                            <input type="file" name="file" id="file" accept="zip/*" class="form-control" style="height:45px"  />
                            <label class="text-info">
                                <br />
                                1.批次上傳藝廊作品的壓縮檔，請用ZIP檔。<br />
                                2.圖檔格式限制jpg，並需小於6MB。<br />
                                3.壓縮檔大小必須小於100MB。<br />
                             
                            </label>
                        </div>
                </div>
            <div class="text-center">

                <button id="btnSend" class="btn btn-default" onclick="btnSend_onclick();">
                    確定送出
                </button>
            </div>
        </div>
    </div>

}
@section Scripts
{
    <script type="text/javascript">
        $(function () {
            $("#ISPersonal_YN").click(function () {
                var ISCH = $("#ISPersonal_YN").prop("checked");
                if (ISCH == true) {

                    $('#TRsClassNo').show();
                    $('#TRsSTDNo').show();
                }
                else {
                    $('#TRsClassNo').hide();
                    $('#TRsSTDNo').hide();
                }
            })

        })
        function UpLoadAction() {
            $("#myModal").modal('show');
            setTimeout(function () {
                $("#barr").css("width", "100%");
            }, 3000);
        }
         function Details_show() {
             var checkP = $("#ISPersonal_YN").prop("checked");
            var ClassNO= $("#Class_No").val();
             $("#Search_WhereCLASS_NO").val(ClassNO);
             if (checkP == true) {
             $("#contentForm").attr("action", "@Url.Action("UPLOAD", (string)ViewBag.BRE_NO)")
             $("#contentForm").submit();
         }
        }

        function FileType_onchange() {
            try {
                if ($('input[name="radFileType"][value="S"]').is(':checked') == true) {
                    $('#TRsClassNo').show();
                }
                else {
                    $('#TRsClassNo').hide();
                }
            }
            catch (err) {
            }
        }

        function btnSend_onclick() {
          
            $("#myModal").modal('show');
            var strMsg = '';
            $("#ZZZI34ImgType").val("G");
            try {
                if ($('#file').val() == '' || $('#file2').val() == '') {
                    strMsg += '上傳檔案需為必填\r\n';
                }

                if ($('#ART_SUBJECT').val() == '' && $("#ART_SUBJECT").val().trim().length == 0) {
                    strMsg += '藝廊名稱為必填\r\n';
                }
             
                if (strMsg != '') {
                    alert(strMsg);
                    return;
                }
                else {
                    $("#btnSend").attr('disabled', true).html("Loading...傳送中，請勿重複點選");
                   
                    strMsg = "請確定以下資訊是否正確\r\n\r\n";

                    strMsg = strMsg + "藝廊名稱：" + $('#ART_SUBJECT').val();

                    strMsg = strMsg + "\r\n";

                   // strMsg = strMsg + "檔名上傳類型："

                
                    YN = confirm(strMsg)

                    if (YN == true) {
                        document.contentForm.enctype = "multipart/form-data";
                        document.contentForm.action = "SetUpload2";
                        document.contentForm.submit();
                        setTimeout(function () {
                            $("#barr").css("width", "80%");
                        }, 5000);
                        setTimeout(function () {
                            $("#barr").css("width", "90%");
                        }, 1000);
                        
                        $("#btnSend").attr("disabled", false);
                  
                    }
                    else {
                        $("#btnSend").attr("disabled", false);
                        $("#myModal").modal('hide');
                    }
                    setTimeout(function () {
                        $("#myModal").modal('hide');
                    }, 5000);
                    //setTimeout(function () {
                    //    $("#barr").css("width", "100%");
                    //}, 1000);
                }
            }
            catch (err) {
            }
        }
    </script>

}