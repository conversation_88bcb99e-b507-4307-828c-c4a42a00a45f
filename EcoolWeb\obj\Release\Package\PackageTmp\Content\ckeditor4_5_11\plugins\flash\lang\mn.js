﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'mn', {
	access: 'Script Access', // MISSING
	accessAlways: 'Онцлогууд',
	accessNever: 'Хэзээ ч үгүй',
	accessSameDomain: 'Байнга',
	alignAbsBottom: 'Abs доод талд',
	alignAbsMiddle: 'Abs Дунд талд',
	alignBaseline: 'Baseline',
	alignTextTop: 'Текст дээр',
	bgcolor: 'Дэвсгэр өнгө',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'Давтах',
	chkMenu: 'Флаш цэс идвэхжүүлэх',
	chkPlay: 'Автоматаар тоглох',
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'Хөндлөн зай',
	properties: 'Флаш шинж чанар',
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'Өргөгтгөх',
	scaleAll: 'Бүгдийг харуулах',
	scaleFit: 'Яг тааруулах',
	scaleNoBorder: 'Хүрээгүй',
	title: 'Флаш  шинж чанар',
	vSpace: 'Босоо зай',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'Линк URL-ээ төрөлжүүлнэ үү',
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
