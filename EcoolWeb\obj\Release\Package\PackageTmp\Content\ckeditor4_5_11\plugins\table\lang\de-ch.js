﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'de-ch', {
	border: 'Rahm<PERSON>rösse',
	caption: '<PERSON>berschrift',
	cell: {
		menu: '<PERSON><PERSON>',
		insertBefore: '<PERSON><PERSON> davor einfügen',
		insertAfter: '<PERSON><PERSON> danach einfügen',
		deleteCell: '<PERSON>elle löschen',
		merge: '<PERSON>ellen verbinden',
		mergeRight: 'Nach rechts verbinden',
		mergeDown: 'Nach unten verbinden',
		splitHorizontal: 'Zelle horizontal teilen',
		splitVertical: 'Zelle vertikal teilen',
		title: 'Zelleneigenschaften',
		cellType: 'Zellart',
		rowSpan: 'Anzahl Zeilen verbinden',
		colSpan: 'Anzahl Spalten verbinden',
		wordWrap: 'Zeilenumbruch',
		hAlign: 'Horizontale Ausrichtung',
		vAlign: 'Vertika<PERSON> Ausrichtung',
		alignBaseline: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
		bgColor: 'Hintergrundfarbe',
		borderColor: 'Rahm<PERSON>farbe',
		data: 'Daten',
		header: 'Überschrift',
		yes: 'Ja',
		no: 'Nein',
		invalidWidth: 'Zellenbreite muss eine Zahl sein.',
		invalidHeight: 'Zellenhöhe muss eine Zahl sein.',
		invalidRowSpan: '"Anzahl Zeilen verbinden" muss eine Ganzzahl sein.',
		invalidColSpan: '"Anzahl Spalten verbinden" muss eine Ganzzahl sein.',
		chooseColor: 'Wählen'
	},
	cellPad: 'Zellenabstand innen',
	cellSpace: 'Zellenabstand aussen',
	column: {
		menu: 'Spalte',
		insertBefore: 'Spalte links davor einfügen',
		insertAfter: 'Spalte rechts danach einfügen',
		deleteColumn: 'Spalte löschen'
	},
	columns: 'Spalte',
	deleteTable: 'Tabelle löschen',
	headers: 'Kopfzeile',
	headersBoth: 'Beide',
	headersColumn: 'Erste Spalte',
	headersNone: 'Keine',
	headersRow: 'Erste Zeile',
	invalidBorder: 'Die Rahmenbreite muss eine Zahl sein.',
	invalidCellPadding: 'Der Zellenabstand innen muss eine positive Zahl sein.',
	invalidCellSpacing: 'Der Zellenabstand aussen muss eine positive Zahl sein.',
	invalidCols: 'Die Anzahl der Spalten muß grösser als 0 sein..',
	invalidHeight: 'Die Tabellenbreite muss eine Zahl sein.',
	invalidRows: 'Die Anzahl der Zeilen muß grösser als 0 sein.',
	invalidWidth: 'Die Tabellenbreite muss eine Zahl sein.',
	menu: 'Tabellen-Eigenschaften',
	row: {
		menu: 'Zeile',
		insertBefore: 'Zeile oberhalb einfügen',
		insertAfter: 'Zeile unterhalb einfügen',
		deleteRow: 'Zeile entfernen'
	},
	rows: 'Zeile',
	summary: 'Inhaltsübersicht',
	title: 'Tabellen-Eigenschaften',
	toolbar: 'Tabelle',
	widthPc: '%',
	widthPx: 'Pixel',
	widthUnit: 'Breite Einheit'
} );
