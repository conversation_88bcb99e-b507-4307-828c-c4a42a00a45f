﻿using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Dapper;
using DotNet.Highcharts.Options;
using EcoolWeb.Util;
using EcoolWeb.Models;
using com.ecool.service;
using static ECOOL_APP.EF.HRMT01;
using EcoolWeb.CustomAttribute;

namespace EcoolWeb.Controllers
{

    [SessionExpire]
    public class SECI04Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "SECI04";
        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        #region  我要看每一學年


        public ActionResult Index(SECI04IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我要看每一學年";
            this.Shared(ViewBag.Panel_Title);

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }


            if (model == null) model = new SECI04IndexViewModel();

            if (string.IsNullOrWhiteSpace(model.whereSCHOOL_NO))
            {
                model.whereSCHOOL_NO = DefaultSCHOOL_NO;
            }

            if (model.whereSYEAR == null)
            {
                //預設值
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                if (model.whereSYEAR == null)
                {
                    model.whereSYEAR = Convert.ToByte(SYear);
                }
            }

            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.whereSCHOOL_NO, null, user);

            string DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            if (DATA_ANGLE_TYPE != UserProfileHelper.AngleVal.AllSchoolData)
            {
                SchoolNoSelectItem.Where(a => a.Value == user.SCHOOL_NO);
            }

            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            model = GetHrmt08List(model);
            model = GetVisionHrmt08List(model);

            model.TALLchart = GetTALLchart(model, "Index");

            model.WEIGHTchart = GetWEIGHTchart(model, "Index");


            model.RIGHT_VisionChart = GetRIGHT_VisionChart(model, "Index");
            model.LEFT_VisionChart = GetLERT_VisionChart(model, "Index");

        

            return View(model);
        }

        #endregion

        #region  我要看每一年級 
        public ActionResult Index2(SECI04IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我要看每一年級";
            this.Shared(ViewBag.Panel_Title);

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }


            if (model == null) model = new SECI04IndexViewModel();

            if (string.IsNullOrWhiteSpace(model.whereSCHOOL_NO))
            {
                model.whereSCHOOL_NO = DefaultSCHOOL_NO;
            }


            if (string.IsNullOrWhiteSpace(model.whereGrade))
            {
                model.whereGrade = ((int)GradeVal.In1Grade).ToString();
            }

            var GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            GradeItem.Remove(GradeItem.Where(a => a.Text == "全部").FirstOrDefault());
            ViewBag.GradeItem = GradeItem;

           var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.whereSCHOOL_NO, null, user);

            string DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            if (DATA_ANGLE_TYPE != UserProfileHelper.AngleVal.AllSchoolData)
            {
                SchoolNoSelectItem.Where(a => a.Value == user.SCHOOL_NO);
            }

            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;


            model = GetHrmt08List(model);
            model = GetVisionHrmt08List(model);

            model.TALLchart = GetTALLchart(model, "Index2");

            model.WEIGHTchart = GetWEIGHTchart(model, "Index2");


            model.RIGHT_VisionChart = GetRIGHT_VisionChart(model, "Index2");
            model.LEFT_VisionChart = GetLERT_VisionChart(model, "Index2");

       
            return View(model);
        }
        #endregion


        #region  我要看一屆 
        public ActionResult Index3(SECI04IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我要看一屆";
            this.Shared(ViewBag.Panel_Title);

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }


            if (model == null) model = new SECI04IndexViewModel();

            if (string.IsNullOrWhiteSpace(model.whereSCHOOL_NO))
            {
                model.whereSCHOOL_NO = DefaultSCHOOL_NO;
            }

            if (model.whereInSYEAR == null)
            {
                model.whereInSYEAR = 100;
            }


            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(model.whereSCHOOL_NO, null, user);

            string DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            if (DATA_ANGLE_TYPE != UserProfileHelper.AngleVal.AllSchoolData)
            {
                SchoolNoSelectItem.Where(a => a.Value == user.SCHOOL_NO);
            }

            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;


            model = GetHrmt08List(model);
            model = GetVisionHrmt08List(model);

            model.TALLchart = GetTALLchart(model, "Index3");

            model.WEIGHTchart = GetWEIGHTchart(model, "Index3");


            model.RIGHT_VisionChart = GetRIGHT_VisionChart(model, "Index3");
            model.LEFT_VisionChart = GetLERT_VisionChart(model, "Index3");


            return View(model);
        }
        #endregion



        //身高體重
        private SECI04IndexViewModel GetHrmt08List(SECI04IndexViewModel model)
        {

            string sSQL = @" Select ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @" L.SCHOOL_NO, ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" T.SYEAR, ";
            }

            sSQL = sSQL + @" L.GRADE,L.SEMESTER ";
            sSQL = sSQL + @",GRADE_SEMESTER = L.GRADE + CASE WHEN L.SEMESTER = 1 Then '上' Else '下' END  ";
            sSQL = sSQL + @",L.AVG_TALL_M,L.AVG_TALL_W,L.AVG_WEIGHT_M,L.AVG_WEIGHT_W  ";
            sSQL = sSQL + @" ,T.This_AVG_TALL_M,T.This_AVG_TALL_W,T.This_AVG_WEIGHT_M,T.This_AVG_WEIGHT_W  ";


            //歷屆
            sSQL = sSQL + @" from (  ";
            sSQL = sSQL + @"         select  ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"         m.SCHOOL_NO,  ";
            }

            sSQL = sSQL + @"         m.GRADE,m.SEMESTER  ";
            sSQL = sSQL + @"        ,isnull(round(AVG(Case When h.SEX=1 Then isnull(m.TALL,0) end) , 1),0) as AVG_TALL_M  ";
            sSQL = sSQL + @"        ,isnull(round(AVG(Case When h.SEX=0 Then isnull(m.TALL,0) end) , 1),0) as AVG_TALL_W  ";
            sSQL = sSQL + @"        ,isnull(round(AVG(Case When h.SEX=1 Then isnull(m.WEIGHT,0) end) , 1),0) as AVG_WEIGHT_M  ";
            sSQL = sSQL + @"        ,isnull(round(AVG(Case When h.SEX=0 Then isnull(m.WEIGHT,0) end) , 1),0) as AVG_WEIGHT_W  ";
            sSQL = sSQL + @"         from HRMT08 m  (nolock)  ";
            sSQL = sSQL + @"         inner join (Select IDNO,SEX from DB2_STUDENT (nolock)  group by  IDNO,SEX ) h on m.IDNO = h.IDNO   ";

            sSQL = sSQL + @"         where 1=1 ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"         and m.SCHOOL_NO = @SCHOOL_NO  ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" and m.GRADE =@GRADE     ";
            }

            sSQL = sSQL + @"         group by ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"         m.SCHOOL_NO,  ";
            }

            sSQL = sSQL + @"         m.GRADE,m.SEMESTER  ";
            sSQL = sSQL + @"         ) L  ";


            //條件下
            sSQL = sSQL + @"  left outer join (  ";
            sSQL = sSQL + @"                      select ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                      s.SCHOOL_NO, ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" s.SYEAR, ";
            }

            sSQL = sSQL + @"                      s.GRADE,s.SEMESTER  ";
            sSQL = sSQL + @"                     ,isnull(round(AVG(Case When h.SEX=1 Then isnull(s.TALL,0) end) , 1),0) as This_AVG_TALL_M  ";
            sSQL = sSQL + @"                     ,isnull(round(AVG(Case When h.SEX=0 Then isnull(s.TALL,0) end) , 1),0) as This_AVG_TALL_W  ";
            sSQL = sSQL + @"                     ,isnull(round(AVG(Case When h.SEX=1 Then isnull(s.WEIGHT,0) end) , 1),0) as This_AVG_WEIGHT_M  ";
            sSQL = sSQL + @"                     ,isnull(round(AVG(Case When h.SEX=0 Then isnull(s.WEIGHT,0) end) , 1),0) as This_AVG_WEIGHT_W   ";
            sSQL = sSQL + @"                      from HRMT08 s  (nolock)  ";
            sSQL = sSQL + @"                      inner  join (Select IDNO,SEX from DB2_STUDENT (nolock)  group by  IDNO,SEX ) h on s.IDNO = h.IDNO   ";
            sSQL = sSQL + @"                      where 1=1 ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                      and s.SCHOOL_NO=@SCHOOL_NO  ";
            }

            //1.我要看每一學年 => 我要看 這學年 每個年級
            if (model.whereSYEAR != null)
            {
                sSQL = sSQL + @"                      and s.SYEAR=@SYEAR  ";
            }


            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" and s.GRADE =@GRADE     ";
            }

            //3.我要看一屆 => 我要看 這學年入學的1~6年級資料
            if (model.whereInSYEAR != null)
            {
                sSQL = sSQL + @"                      and left(s.USER_NO,3)=@InSYEAR  ";
            }

            sSQL = sSQL + @"                      group by ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                      s.SCHOOL_NO,  ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" s.SYEAR,     ";
            }


            sSQL = sSQL + @"                     s.GRADE,s.SEMESTER  ";
            sSQL = sSQL + @"        ) as T on  L.GRADE=T.GRADE and L.SEMESTER=T.SEMESTER  ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"        and L.SCHOOL_NO=T.SCHOOL_NO ";
            }

            sSQL = sSQL + @"  order by ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"  L.SCHOOL_NO,  ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @"   T.SYEAR Desc,    ";
            }

            sSQL = sSQL + @"  L.GRADE,L.SEMESTER";

            model.Hrmt08List = db.Database.Connection.Query<SECI04Hrmt08ListViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = model.whereSCHOOL_NO,
                    SYEAR = model.whereSYEAR,
                    GRADE = model.whereGrade,
                    InSYEAR = model.whereInSYEAR
                }).ToList();



            return model;
        }

        // 視力
        private SECI04IndexViewModel GetVisionHrmt08List(SECI04IndexViewModel model)
        {

            string sSQL = @"SELECT   ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @" m.SCHOOL_NO,  ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" t.SYEAR, ";
            }

            sSQL = sSQL + @" m.SEMESTER  ";
            sSQL = sSQL + @",m.GRADE,GRADE_SEMESTER = m.GRADE + CASE WHEN m.SEMESTER = 1 Then '上' Else '下' END  ";
            sSQL = sSQL + @", m.VisionLeft_08,m.VisionLeft_09,m.VisionRight_08,m.VisionRight_09 ";
            sSQL = sSQL + @",t.This_VisionLeft_08,t.This_VisionLeft_09,t.This_VisionRight_08,t.This_VisionRight_09  ";

            //歷屆
            sSQL = sSQL + @" FROM (  ";
            sSQL = sSQL + @"   Select   ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                        s.SCHOOL_NO,  ";
            }

            sSQL = sSQL + @"                        s.GRADE ,s.SEMESTER  ";
            sSQL = sSQL + @"                      , round(CONVERT(money,Sum(case when CAST(s.VISION_LEFT as float) <= 0.8 Then 1 else 0 end))/count(*)*100,2)  as VisionLeft_08                          
                                                  , round(CONVERT(money,Sum(case when CAST(s.VISION_LEFT as float) >= 0.9 Then 1 else 0 end))/count(*)*100,2)  as VisionLeft_09                           		                
                                                  , round(CONVERT(money,Sum(case when CAST(s.VISION_RIGHT as float) <= 0.8 Then 1 else 0 end))/count(*)*100,2)  as VisionRight_08                          
                                                  , round(CONVERT(money,Sum(case when CAST(s.VISION_RIGHT as float) >= 0.9 Then 1 else 0 end))/count(*)*100,2)  as VisionRight_09    ";
            sSQL = sSQL + @"                       from HRMT08 s (NOLOCK)  ";
            sSQL = sSQL + @"                       where 1=1  ";
            sSQL = sSQL + @"                       and isnull(s.VISION_RIGHT, '') <> '' and isnull(s.VISION_LEFT, '') <> '' ";
            sSQL = sSQL + @"                       and isNumeric(s.VISION_RIGHT) =1 and isNumeric(s.VISION_LEFT) =1  ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"  and s.SCHOOL_NO=@SCHOOL_NO  ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" and s.GRADE =@GRADE     ";
            }


            sSQL = sSQL + @"                      group by   ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                      s.SCHOOL_NO,   ";
            }

            sSQL = sSQL + @"                      s.GRADE ,s.SEMESTER  ";
            sSQL = sSQL + @"               ) AS m  ";


            //條件下
            sSQL = sSQL + @"                left outer join (  ";
            sSQL = sSQL + @"                 Select  ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                s.SCHOOL_NO, ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" s.SYEAR, ";
            }

            sSQL = sSQL + @"                s.GRADE ,s.SEMESTER  ";
            sSQL = sSQL + @"                ,round(CONVERT(money,Sum(case when CAST(s.VISION_LEFT as float) <= 0.8 Then 1 else 0 end))/count(*)*100,2)  as This_VisionLeft_08                  
                                            ,round(CONVERT(money,Sum(case when CAST(s.VISION_LEFT as float) >= 0.9 Then 1 else 0 end))/count(*)*100,2) as This_VisionLeft_09                 
                                            ,round(CONVERT(money, Sum(case when CAST(s.VISION_RIGHT as float) <= 0.8 Then 1 else 0 end))/count(*)*100,2) as This_VisionRight_08                 
                                            ,round(CONVERT(money, Sum(case when CAST(s.VISION_RIGHT as float) >= 0.9 Then 1 else 0 end))/count(*)*100,2) as This_VisionRight_09                   ";
            sSQL = sSQL + @"                from HRMT08 s (NOLOCK) ";
            sSQL = sSQL + @"                where 1=1 ";
            sSQL = sSQL + @"                and isnull(s.VISION_RIGHT, '') <> '' and isnull(s.VISION_LEFT, '') <> '' ";
            sSQL = sSQL + @"                and isNumeric(s.VISION_RIGHT) =1 and isNumeric(s.VISION_LEFT) =1  ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                and s.SCHOOL_NO=@SCHOOL_NO  ";
            }

            //1.我要看每一學年 => 我要看 這學年 每個年級
            if (model.whereSYEAR != null)
            {
                sSQL = sSQL + @"                and s.SYEAR=@SYEAR  ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" and s.GRADE =@GRADE     ";
            }

            //3.我要看一屆 => 我要看 這學年入學的1~6年級資料
            if (model.whereInSYEAR != null)
            {
                sSQL = sSQL + @"                      and left(s.USER_NO,3)=@InSYEAR  ";
            }

            sSQL = sSQL + @"                group by  ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"                s.SCHOOL_NO,  ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @" s.SYEAR,     ";
            }


            sSQL = sSQL + @"                s.GRADE ,s.SEMESTER  ";
            sSQL = sSQL + @"   ) AS t ON  m.GRADE=t.GRADE and m.SEMESTER=t.SEMESTER ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @" and m.SCHOOL_NO=t.SCHOOL_NO ";
            }

            sSQL = sSQL + @"   order by ";

            //看某間學校
            if (model.whereSCHOOL_NO != "ALL")
            {
                sSQL = sSQL + @"  m.SCHOOL_NO, ";
            }

            //2.我要看每一年級 => 我要看 某年級 每個學年
            if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQL = sSQL + @"   t.SYEAR Desc,    ";
            }

            sSQL = sSQL + @"  m.GRADE,m.SEMESTER";


            model.VisionHrmt08List = db.Database.Connection.Query<SECI04Hrmt08ListViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = model.whereSCHOOL_NO,
                    SYEAR = model.whereSYEAR,
                    GRADE = model.whereGrade,
                    InSYEAR = model.whereInSYEAR
                }).ToList();


            return model;
        }

        /// <summary>
        /// RightVisionChart
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private Highcharts GetRIGHT_VisionChart(SECI04IndexViewModel model, string View)
        {

            Highcharts chart = new Highcharts("RightVisionChart");

            if (model.VisionHrmt08List.Count() > 0)
            {
                Series[] ArrSeries = new Series[] { };
                PlotOptions ThisPlotOptions = new PlotOptions();
                string XTitle = string.Empty;

                string[] ArrXCategories = new string[] { }; //X  一上 …


                var ThisArr_8 = (from a in model.VisionHrmt08List
                                 select a.This_VisionRight_08).Cast<object>().ToArray();

                var ThisArr_9 = (from a in model.VisionHrmt08List
                                 select a.This_VisionRight_09).Cast<object>().ToArray();



                var HisArr_8 = (from a in model.VisionHrmt08List
                                select a.VisionRight_08).Cast<object>().ToArray();

                var HisArr_9 = (from a in model.VisionHrmt08List
                                select a.VisionRight_09).Cast<object>().ToArray();

              

                if (View == "Index" )
                {

                    XTitle = "年級";
                    ArrXCategories = (from a in model.VisionHrmt08List
                                      select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

                   ArrSeries = new Series[]
                  {
                        new Series
                        {
                        Name = model.whereSYEAR.ToString()+"學年各年級-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_8)
                        },
                        new Series
                        {
                        Name = model.whereSYEAR.ToString()+"學年各年級-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_9)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_8)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_9)
                        }
                    };
                }
                else if (View == "Index2")
                {
                    XTitle = "學年";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.SYEAR + (a.SEMESTER == 1 ? "上" : "下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[]
                   {
                        new Series
                        {
                        Name = model.whereGrade.ToString()+"年級各學年-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_8)
                        },
                        new Series
                        {
                        Name = model.whereGrade.ToString()+"年級各學年-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_9)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_8)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_9)
                        }
                  };
                }
                else if (View == "Index3")
                {
                    XTitle = "學年";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.SYEAR + (a.SEMESTER == 1 ? "上" : "下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[]
               {
                        new Series
                        {
                        Name = model.whereInSYEAR.ToString()+"學年入學各年級-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_8)
                        },
                        new Series
                        {
                        Name = model.whereSYEAR.ToString()+"學年入學各年級-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_9)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_8)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_9)
                        }
                 };
                }

                chart
                .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Column, Height = 400 })
                .SetTitle(new Title { Text = "視力分布狀況(右眼)" })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = XTitle }, Categories = ArrXCategories })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "百分比" }, Min = 0 })
                .SetPlotOptions(ThisPlotOptions)
                .SetTooltip(new Tooltip { ValueSuffix = "%", Shared = true })
                .SetSeries(ArrSeries);

                chartsHelper.SetCopyright(chart);
            }
            else
            {
                chart = null;
            }


            return chart;
        }


        /// <summary>
        /// LeftVisionChart
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private Highcharts GetLERT_VisionChart(SECI04IndexViewModel model, string View)
        {

            Highcharts chart = new Highcharts("LeftVisionChart");

            if (model.VisionHrmt08List.Count() > 0)
            {
                Series[] ArrSeries = new Series[] { };
                PlotOptions ThisPlotOptions = new PlotOptions();
                string XTitle = string.Empty;

                string[] ArrXCategories = new string[] { }; //x  一上 …


                var ThisArr_8 = (from a in model.VisionHrmt08List
                                 select a.This_VisionLeft_08).Cast<object>().ToArray();

                var ThisArr_9 = (from a in model.VisionHrmt08List
                                 select a.This_VisionLeft_09).Cast<object>().ToArray();



                var HisArr_8 = (from a in model.VisionHrmt08List
                                select a.VisionLeft_08).Cast<object>().ToArray();

                var HisArr_9 = (from a in model.VisionHrmt08List
                                select a.VisionLeft_09).Cast<object>().ToArray();

               

                if (View == "Index")
                {

                    XTitle = "年級";
                    ArrXCategories = (from a in model.VisionHrmt08List
                                      select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[]
                           {
                                 new Series
                                 {
                                   Name = model.whereSYEAR.ToString()+"學年各年級-0.8以下",
                                   Data = new DotNet.Highcharts.Helpers.Data(ThisArr_8)
                                 },
                                 new Series
                                 {
                                   Name = model.whereSYEAR.ToString()+"學年各年級-0.9以上",
                                   Data = new DotNet.Highcharts.Helpers.Data(ThisArr_9)
                                 },
                                 new Series
                                 { Name = "歷屆平均-0.8以下",
                                   Data = new DotNet.Highcharts.Helpers.Data(HisArr_8)
                                 },
                                 new Series
                                 { Name = "歷屆平均-0.9以上",
                                   Data = new DotNet.Highcharts.Helpers.Data(HisArr_9)
                                 }
                           };
                }
                else if (View == "Index2")
                {
                    XTitle = "學年";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.SYEAR + (a.SEMESTER == 1 ? "上" : "下")).Distinct().Cast<string>().ToArray();

                  ArrSeries = new Series[]
                  {
                        new Series
                        {
                        Name = model.whereGrade.ToString()+"年級各學年-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_8)
                        },
                        new Series
                        {
                        Name = model.whereGrade.ToString()+"年級各學年-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_9)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_8)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_9)
                        }
                };
                }
                else if (View == "Index3")
                {
                    XTitle = "年級";
                    ArrXCategories = (from a in model.VisionHrmt08List
                                      select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[]
             {
                        new Series
                        {
                        Name = model.whereInSYEAR.ToString()+"學年入學各年級-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_8)
                        },
                        new Series
                        {
                        Name = model.whereSYEAR.ToString()+"學年入學各年級-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(ThisArr_9)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.8以下",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_8)
                        },
                        new Series
                        {
                        Name = "歷屆平均-0.9以上",
                        Data = new DotNet.Highcharts.Helpers.Data(HisArr_9)
                        }
               };
                }

                chart
                .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Column, Height = 400 })
                .SetTitle(new Title { Text = "視力分布狀況(左眼)" })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = XTitle }, Categories = ArrXCategories })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "百分比" }, Min = 0 })
                .SetPlotOptions(ThisPlotOptions)
                .SetTooltip(new Tooltip { ValueSuffix = "%", Shared = true })
                .SetSeries(ArrSeries);

                chartsHelper.SetCopyright(chart);
            }
            else
            {
                chart = null;
            }


            return chart;
        }

        /// <summary>
        /// 身高
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private Highcharts GetTALLchart(SECI04IndexViewModel model, string View)
        {

            Highcharts chart = new Highcharts("TALLchart");



            if (model.Hrmt08List.Count() > 0)
            {
                Series[] ArrSeries = new Series[] { };
                PlotOptions ThisPlotOptions = new PlotOptions();

                object[] ArrTALL_M = new object[] { }; //條件下男
                Series TALL_M = new Series();

                object[] ArrTALL_W = new object[] { }; //條件下女
                Series TALL_W = new Series();

                object[] ArrAVG_TALL_M = new object[] { }; //歷屆男
                Series AVG_TALL_M = new Series();

                object[] ArrAVG_TALL_W = new object[] { }; //歷屆女
                Series AVG_TALL_W = new Series();

                string[] ArrXCategories = new string[] { }; //X  一上 …
                string XTitle = string.Empty;

                ArrTALL_M = (from a in model.Hrmt08List
                             select a.This_AVG_TALL_M).Cast<object>().ToArray();

                ArrTALL_W = (from a in model.Hrmt08List
                             select a.This_AVG_TALL_W).Cast<object>().ToArray();

                ArrAVG_TALL_M = (from a in model.Hrmt08List
                                 select a.AVG_TALL_M).Cast<object>().ToArray();

                ArrAVG_TALL_W = (from a in model.Hrmt08List
                                 select a.AVG_TALL_W).Cast<object>().ToArray();

                if (View == "Index")
                {
                    XTitle = "年級";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

                    TALL_M.Type = ChartTypes.Column;
                    TALL_M.Name = model.whereSYEAR.ToString() + "學年各年級-平均身高(男)";
                    TALL_M.Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC");
                    TALL_M.Data = new DotNet.Highcharts.Helpers.Data(ArrTALL_M);

                    TALL_W.Type = ChartTypes.Column;
                    TALL_W.Name = model.whereSYEAR.ToString() + "學年各年級-平均身高(女)";
                    TALL_W.Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80");
                    TALL_W.Data = new DotNet.Highcharts.Helpers.Data(ArrTALL_W);

                    AVG_TALL_M.Type = ChartTypes.Line;
                    AVG_TALL_M.Name = "歷屆平均身高(男)";
                    AVG_TALL_M.Color = ECOOL_APP.HtmlHelper.HexColor("#ACD6FF");
                    AVG_TALL_M.Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_M);


                    AVG_TALL_W.Type = ChartTypes.Line;
                    AVG_TALL_W.Name = "歷屆平均身高(女)";
                    AVG_TALL_W.Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA");
                    AVG_TALL_W.Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_W);

                    ArrSeries = new Series[] { TALL_M, TALL_W, AVG_TALL_M, AVG_TALL_W };


                }
                else if (View == "Index2")
                {
                    XTitle = "學年";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.SYEAR + (a.SEMESTER == 1 ? "上" : "下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[] {
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name =  model.whereGrade.ToString() + "級各學年-平均身高(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrTALL_M),
                        },
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name = "歷屆平均身高(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#AAAAFF"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_M),
                        },
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name =  model.whereGrade.ToString() + "級各學年-平均身高(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrTALL_W),
                        },
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name = "歷屆平均身高(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_W),
                        }
                    };
                }
                else if (View == "Index3")
                {
                    XTitle = "年級";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

                    TALL_M.Type = ChartTypes.Line;
                    TALL_M.Name =  model.whereSYEAR.ToString() + "學年入學各年級-平均身高(男)";
                    TALL_M.Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC");
                    TALL_M.Data = new DotNet.Highcharts.Helpers.Data(ArrTALL_M);

                    TALL_W.Type = ChartTypes.Line;
                    TALL_W.Name =  model.whereSYEAR.ToString() + "學年入學各年級-平均身高(女)";
                    TALL_W.Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80");
                    TALL_W.Data = new DotNet.Highcharts.Helpers.Data(ArrTALL_W);

                    AVG_TALL_M.Type = ChartTypes.Line;
                    AVG_TALL_M.Name = "歷屆平均身高(男)";
                    AVG_TALL_M.Color = ECOOL_APP.HtmlHelper.HexColor("#ACD6FF");
                    AVG_TALL_M.Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_M);


                    AVG_TALL_W.Type = ChartTypes.Line;
                    AVG_TALL_W.Name = "歷屆平均身高(女)";
                    AVG_TALL_W.Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA");
                    AVG_TALL_W.Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_TALL_W);

                    ArrSeries = new Series[] { TALL_M, TALL_W, AVG_TALL_M, AVG_TALL_W };
                }



                chart
                .InitChart(new DotNet.Highcharts.Options.Chart { Height = 800 })
                .SetTitle(new Title { Text = "身高狀況圖" })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = XTitle }, Categories = ArrXCategories })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "身高(cm)" }, TickInterval = 1, Min = 100 })
                .SetPlotOptions(ThisPlotOptions)
                .SetTooltip(new Tooltip { ValueSuffix = "cm", Crosshairs = new Crosshairs(true), Shared = true })
                .SetSeries(ArrSeries);

                chartsHelper.SetCopyright(chart);
            }
            else
            {
                chart = null;
            }


            return chart;
        }

        /// <summary>
        /// 體重
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private Highcharts GetWEIGHTchart(SECI04IndexViewModel model, string View)
        {

            Highcharts chart = new Highcharts("WEIGHTchart");


            if (model.Hrmt08List.Count() > 0)
            {
                Series[] ArrSeries = new Series[] { };
                PlotOptions ThisPlotOptions = new PlotOptions();

                //條件下男
                object[] ArrWEIGHT_M = new object[] { };

                //條件下女
                object[] ArrWEIGHT_W = new object[] { };

                //歷屆男
                object[] ArrAVG_WEIGHT_M = new object[] { };

                //歷屆女
                object[] ArrAVG_WEIGHT_W = new object[] { };


                //X  一上 …
                string[] ArrXCategories = new string[] { };
                string XTitle = string.Empty;

                ArrWEIGHT_M = (from a in model.Hrmt08List
                               select a.This_AVG_WEIGHT_M).Cast<object>().ToArray();

                ArrWEIGHT_W = (from a in model.Hrmt08List
                               select a.This_AVG_WEIGHT_W).Cast<object>().ToArray();

                ArrAVG_WEIGHT_M = (from a in model.Hrmt08List
                                   select a.AVG_WEIGHT_M).Cast<object>().ToArray();

                ArrAVG_WEIGHT_W = (from a in model.Hrmt08List
                                   select a.AVG_WEIGHT_W).Cast<object>().ToArray();

                if (View == "Index")
                {
                    XTitle = "年級";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[]
                    {
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name =  model.whereSYEAR.ToString() + "學年各年級-平均體重(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT_M)
                        },
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name =  model.whereSYEAR.ToString() + "學年各年級-平均體重(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT_W)
                        },
                        new Series
                        {
                            Type = ChartTypes.Line,
                            Name = "歷屆平均體重(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#AAAAFF"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_M)
                        },
                        new Series
                        {
                            Type = ChartTypes.Line,
                            Name = "歷屆平均體重(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_W)
                        }
                    };



                }
                else if (View == "Index2")
                {
                    XTitle =  "學年";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.SYEAR + (a.SEMESTER == 1 ? "上" : "下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[]
                    {
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name = model.whereGrade.ToString() + "級各學年-平均體重(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT_M)
                        },

                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name = "歷屆平均體重(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#AAAAFF"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_M)
                        },
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name =  model.whereSYEAR.ToString() + "級各學年-平均體重(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT_W)
                        },
                        new Series
                        {
                            Type = ChartTypes.Column,
                            Name = "歷屆平均體重(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_W)
                        }
                    };

                }
                else if (View == "Index3")
                {
                    XTitle = "年級";
                    ArrXCategories = (from a in model.Hrmt08List
                                      select a.GRADE_SEMESTER.Replace("上", "<br/>上").Replace("下", "<br/>下")).Distinct().Cast<string>().ToArray();

                    ArrSeries = new Series[]
                    {
                        new Series
                        {
                            Type = ChartTypes.Line,
                            Name =  model.whereSYEAR.ToString() + "學年入學-平均體重(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#7CB5EC"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT_M)
                        },
                        new Series
                        {
                            Type = ChartTypes.Line,
                            Name =  model.whereSYEAR.ToString() + "學年入學-平均體重(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F15C80"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrWEIGHT_W)
                        },
                        new Series
                        {
                            Type = ChartTypes.Line,
                            Name = "歷屆平均體重(男)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#AAAAFF"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_M)
                        },
                        new Series
                        {
                            Type = ChartTypes.Line,
                            Name = "歷屆平均體重(女)",
                            Color = ECOOL_APP.HtmlHelper.HexColor("#F871EA"),
                            Data = new DotNet.Highcharts.Helpers.Data(ArrAVG_WEIGHT_W)
                        }
                    };
                }


                chart
                .InitChart(new DotNet.Highcharts.Options.Chart { Height = 800 })
                .SetTitle(new Title { Text = "體重狀況圖" })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = XTitle }, Categories = ArrXCategories })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "體重(kg)" }, TickInterval = 1, Min = 15 })
                .SetPlotOptions(ThisPlotOptions)
                .SetTooltip(new Tooltip { ValueSuffix = "kg", Crosshairs = new Crosshairs(true), Shared = true })
                .SetSeries(ArrSeries);

                chartsHelper.SetCopyright(chart);
            }
            else
            {
                chart = null;
            }



            return chart;
        }



        #region  Shared
        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }
        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}