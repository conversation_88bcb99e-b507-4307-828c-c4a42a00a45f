﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace EcoolWeb
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.MapRoute(
             name: "Root",
              url: "",
              defaults: new { controller = "Home", action = ECOOL_APP.EF.SharedGlobal.HomeIndex });
         
            routes.MapRoute(
 name: "SchoolRoute",
 url: "school/{school}",
 defaults: new { controller = "Home", action = "GuestIndex" }
);

            routes.MapRoute(
              name: "Default",
                 url: "{controller}/{action}/{school}/{id}",
                 defaults: new { action = "Index", school = UrlParameter.Optional, id = UrlParameter.Optional }

                 );
      
        }
    }
}