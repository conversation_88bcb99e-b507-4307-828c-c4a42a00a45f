﻿
using DotNet.Highcharts;
using System.Collections.Generic;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SECI01StatisticalViewModel
    {
        /// <summary>
        /// 月份 字串
        /// </summary>
        public List<string> TEachMonth { get; set; }

        /// <summary>
        /// 每週 週六日期
        /// </summary>
        public List<string> TEachWeekToDate { get; set; }
        
        /// <summary>
        /// 月統計表資料
        /// </summary>
        public List<SECSharedChartLineViewModel> MonthList;

        /// <summary>
        /// 月趨示圖
        /// </summary>
        public Highcharts Monthcharts;


        /// <summary>
        /// 週統計表
        /// </summary>
        public List<SECSharedChartLineViewModel> WeekList;

        /// <summary>
        /// 週趨示圖
        /// </summary>
        public Highcharts Weekcharts;

        /// <summary>
        /// 資料角度 中文說明
        /// </summary>
        public string DATA_TYPE_NAME { get; set; }



    }
}