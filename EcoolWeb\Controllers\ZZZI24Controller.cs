﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Net;
using System.IO;
using System.Data.Entity.Validation;
using System.Data.Entity;
using com.ecool.service;
using System.ComponentModel.DataAnnotations;
using AutoMapper;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI24Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();
        private UserProfile user = UserProfileHelper.Get();

        [CheckPermission(CheckACTION_ID = "Query")]
        public ActionResult CashLimit(string USER_NO)
        {
            HRMT01 h1 = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_NO == USER_NO).First();
            return View(h1);
        }

        [CheckPermission(CheckACTION_ID = "Query")]
        public ActionResult SetCashLimit(HRMT01 h1)
        {
            HRMT01 h2 = db.HRMT01.Where(a => a.SCHOOL_NO == h1.SCHOOL_NO && a.USER_NO == h1.USER_NO).First();
            h2.GIVE_CASH_LIMIT = h1.GIVE_CASH_LIMIT;
            db.SaveChanges();

            ViewBag.Msg = "設定成功";
            return View();
        }

        //CashLimitMany
        [HttpPost]
        public ActionResult CashLimitMany(FormCollection zzzi19, string HowMuch, HRMT01QueryViewModel model)
        {
            int hidListCount = (zzzi19["hidListCount"] != null) ? Convert.ToInt32(zzzi19["hidListCount"]) : 500;

            int  HowMuchCash = -1;
            HowMuchCash = Int32.Parse(HowMuch);

     
            if (HowMuchCash == -1) return RedirectToAction("Query", "ZZZI24", model);

            for (int i = 0; i < hidListCount; i++)
            {
                if (zzzi19["[" + i + "].chkTRANS_NO"] == "on")
                {
                    if ((zzzi19["[" + i + "].SCHOOL_NO"] != null || zzzi19["[" + i + "].SCHOOL_NO"] != string.Empty) && (zzzi19["[" + i + "].USER_NO"] != null || zzzi19["[" + i + "].USER_NO"] != string.Empty))
                    {
                        HRMT01 HT01 = new HRMT01();
                        string SCHOOL_NO = zzzi19["[" + i + "].SCHOOL_NO"];
                        string USER_NO = zzzi19["[" + i + "].USER_NO"];
                        HT01 = db.HRMT01.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.USER_NO == USER_NO).FirstOrDefault();
                        HT01.GIVE_CASH_LIMIT = HowMuchCash;
                        db.Entry(HT01).State = EntityState.Modified;
                        db.SaveChanges();
                    }
                }
            }

            //更新顯示
            return RedirectToAction("Query", "ZZZI24", model);
        }

        [HttpPost]
        public ActionResult SetCashLimitVal(string CashLimitVal)
        {
            bool CashLimitValbool = false;
            string SCHOOL_NO = UserProfileHelper.GetSchoolNo();
            CashLimitValbool = bool.Parse(CashLimitVal);
            BDMT01 oldbDMT01 = db.BDMT01.Find(SCHOOL_NO);
            oldbDMT01.CHECK_CASH_LIMIT = CashLimitValbool;
            db.SaveChanges();
            return Json(CashLimitValbool);
        }

        // GET: ZZZI24
        [CheckPermission] //檢查權限
        public ActionResult Query(HRMT01QueryViewModel model)
        {
            if (model == null) model = new HRMT01QueryViewModel();

            int nowYear = DateTime.Now.Year;
            int nowMonth = DateTime.Now.Month;
            var groupAWAT01_LOG = from al in db.AWAT01_LOG
                                  join h01 in db.HRMT01 on new { al.USER_NO, al.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into alh
                                  from h01 in alh
                                  where al.LOG_TIME.Year == nowYear
                                    && al.LOG_TIME.Month == nowMonth
                                    && h01.USER_TYPE == UserType.Student
                                  group al by new { al.LOG_PERSON } into g
                                  select new { LOG_PERSON = g.Key.LOG_PERSON, ADD_CASH_ALL = g.Sum(d => d.ADD_CASH_ALL) };
      
            var HRMT01List = from h in db.HRMT01
                             where h.SCHOOL_NO == SchoolNO && h.USER_TYPE == UserType.Teacher && (!UserStaus.NGKeyinUserStausList.Contains(h.USER_STATUS))
                             join a in groupAWAT01_LOG on h.USER_KEY equals a.LOG_PERSON into ha
                             
                             from a in ha.DefaultIfEmpty()
                             select new ZZZI24QueryListViewModel
                             {
                                 SCHOOL_NO = h.SCHOOL_NO,
                                 USER_STATUS = h.USER_STATUS,
                                 USER_TYPE = h.USER_TYPE,
                                 CLASS_NO = h.CLASS_NO,
                                 USER_NO = h.USER_NO,
                                 NAME = h.NAME,
                                 GRADE = h.GRADE,
                                 GIVE_CASH_LIMIT = h.GIVE_CASH_LIMIT,
                                 USE_CASH = a.ADD_CASH_ALL ?? 0
                             };
            model.CHECK_CASH_LIMIT = db.BDMT01.Where(x => x.SCHOOL_NO == SchoolNO).Select(x => x.CHECK_CASH_LIMIT).FirstOrDefault();
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(Request["ddlClass_No"]) == false)
            {
                string strClass_No = Request["ddlClass_No"];
                model.whereClass_No = strClass_No;
                HRMT01List = HRMT01List.Where(a => a.CLASS_NO == strClass_No);
            }

            if (string.IsNullOrWhiteSpace(Request["ddlGrade"]) == false)
            {
                byte bGrade = Convert.ToByte(Request["ddlGrade"]);
                model.whereGrade = bGrade.ToString();
                HRMT01List = HRMT01List.Where(a => a.GRADE == bGrade);
            }

            if (string.IsNullOrWhiteSpace(Request["ddlStatus"]) == false)
            {
                model.whereStatus = Request["ddlStatus"];
            }
            if (string.IsNullOrWhiteSpace(model.whereStatus) == false)
            {
                byte bUSER_STATUS = Convert.ToByte(model.whereStatus);
                HRMT01List = HRMT01List.Where(a => a.USER_STATUS == bUSER_STATUS);
            }

            switch (model.OrdercColumn)
            {
                default:
                    HRMT01List = HRMT01List.OrderByDescending(a => a.CLASS_NO != null).ThenBy(a => a.CLASS_NO).ThenBy(a => a.NAME);
                    break;
            }

            int iPageCount = (Request["ddlPageCount"] != null) ? Convert.ToInt32(Request["ddlPageCount"]) : 100;

            model.HRMT01List = HRMT01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, iPageCount);
            model.ShowPageCount = iPageCount.ToString();

            //班級的項目
            //var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
            //Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);

            //ViewBag.ClassNoItem = items;
            List<SelectListItem> items = new List<SelectListItem>();
            var ltCLASS_NO = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
                 Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
            foreach (var item in ltCLASS_NO)
            {
                items.Add(new SelectListItem() { Text = item.Text, Value = item.Value });
            }
            if (!items.Count.Equals(0))
            {
                items.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
            }
            ViewBag.ClassNoItem = items;

            List<SelectListItem> StatusItem = new List<SelectListItem>();
            StatusItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = (string.IsNullOrWhiteSpace(Request["ddlStatus"])) });
            StatusItem.Add(new SelectListItem() { Text = "啟用", Value = "1", Selected = (Request["ddlStatus"] == "1") });
            StatusItem.Add(new SelectListItem() { Text = "帳號鎖定", Value = "7", Selected = (Request["ddlStatus"] == "7") });
            StatusItem.Add(new SelectListItem() { Text = "停用", Value = "8", Selected = (Request["ddlStatus"] == "8") });
            StatusItem.Add(new SelectListItem() { Text = "失效(畢業，轉出)", Value = "9", Selected = (Request["ddlStatus"] == "9") });
            ViewBag.StatusItem = StatusItem;

            List<SelectListItem> PageCountItem = new List<SelectListItem>();
            PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = (iPageCount == 100) });
            PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = (iPageCount == 200) });
            PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = (iPageCount == 500) });
            ViewBag.PageCount = PageCountItem;

            BDMT01 School = UserProfileHelper.GetSchool(SchoolNO);
            if (School != null) ViewBag.DefaultCashLimit = School.TEACHER_CASH_LIMIT ?? 0;

            return View(model);
        }

        [HttpPost]
        public ActionResult StartUser(FormCollection zzzi19, HRMT01QueryViewModel model)
        {
            int hidListCount = (zzzi19["hidListCount"] != null) ? Convert.ToInt32(zzzi19["hidListCount"]) : 500;
            //設定使用者狀況啟用
            SetUserStatus(zzzi19, 1, hidListCount);
            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);
            return RedirectToAction("Query", "ZZZI24", model);
        }

        [HttpPost]
        public ActionResult CancelUser(FormCollection zzzi19, HRMT01QueryViewModel model)
        {
            int hidListCount = (zzzi19["hidListCount"] != null) ? Convert.ToInt32(zzzi19["hidListCount"]) : 500;
            //設定使用者狀況停用
            SetUserStatus(zzzi19, 8, hidListCount);
            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);
            return RedirectToAction("Query", "ZZZI24", model);
        }

        public void SetUserStatus(FormCollection zzzi19, int iStatus, int iListCount)
        {
            for (int i = 0; i < iListCount; i++)
            {
                if (zzzi19["[" + i + "].chkTRANS_NO"] == "on")
                {
                    if ((zzzi19["[" + i + "].SCHOOL_NO"] != null || zzzi19["[" + i + "].SCHOOL_NO"] != string.Empty) && (zzzi19["[" + i + "].USER_NO"] != null || zzzi19["[" + i + "].USER_NO"] != string.Empty))
                    {
                        HRMT01 HT01 = new HRMT01();
                        string SCHOOL_NO = zzzi19["[" + i + "].SCHOOL_NO"];
                        string USER_NO = zzzi19["[" + i + "].USER_NO"];
                        HT01 = db.HRMT01.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.USER_NO == USER_NO).FirstOrDefault();
                        HT01.USER_STATUS = (byte)iStatus;
                        db.Entry(HT01).State = EntityState.Modified;
                        db.SaveChanges();
                    }
                }
            }
        }
    }
}