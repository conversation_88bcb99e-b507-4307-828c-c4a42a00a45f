/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/LetterlikeSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{8450:[666,18,702,35,702],8453:[676,14,855,47,808],8458:[441,219,738,30,678],8459:[687,15,997,53,991],8461:[653,0,732,17,767],8462:[668,11,513,45,483],8464:[675,15,897,26,888],8466:[687,15,946,33,931],8469:[653,0,727,25,755],8470:[668,15,1046,19,1031],8473:[653,0,687,17,686],8474:[666,71,723,35,713],8475:[687,15,944,34,876],8477:[653,0,687,17,686],8482:[653,-247,980,30,957],8484:[653,0,754,7,750],8492:[687,15,950,34,902],8495:[441,11,627,30,554],8496:[687,15,750,100,734],8497:[680,0,919,43,907],8499:[674,15,1072,38,1056],8500:[441,11,697,30,680],8508:[428,12,635,40,630],8511:[653,0,750,30,780],8517:[653,0,713,17,703],8518:[683,11,581,40,634],8519:[441,11,515,40,485],8520:[653,0,293,27,346],8521:[653,217,341,-104,394]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/LetterlikeSymbols.js");
