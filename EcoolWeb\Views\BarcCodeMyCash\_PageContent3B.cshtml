﻿@model BarcCodeMyCashIndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    //兌換獎品系統路徑
    ViewBag.SysAwardPath = EcoolWeb.Controllers.AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);

    string unicornPhoto_sm = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", Model.Level, "sm"));
    string unicornPhoto_bg = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", Model.Level, "lg"));
}

@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.WhereSchoolNo)
@Html.HiddenFor(m => m.TimeoutSeconds)
@Html.HiddenFor(m => m.ShowStep2)
@Html.HiddenFor(m => m.WhereKeyword)
@if ((Model.TimeoutSeconds ?? 0) > 0)
{
    <span class="TimeoutSeconds" data-seconds-left="@Model.TimeoutSeconds" style="display:none"></span>
}
@if (ViewBag.ATMUID == "S")
{
    <div class="jumbotron justify-content-center align-items-center align-content-center" id="atm_bg_a1" style="padding-top:15px;">
        <div class="col d-flex justify-content-center" id="atm_head">
            <div id="atm_logo" style="background-image:url('@Url.Content("~/assets/img/atm_logo_02.png")');background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
        </div>
        <div class="row d-flex align-items-center align-content-center" id="atm_contain_a1">
            <div class="col d-flex justify-content-center align-items-center align-content-center col-lg-8 col-md-8 col-sm-12 col-12" id="atm_content_l_b1">
                <div class="row" id="atm_con_l_row_b1">
                    <div class="col d-flex flex-column align-items-center" style="height:100%">
                        <div class="" style="height:66%;width:100%;">
                            <div id="ATM3" style="display:none;margin: 0px auto;text-align:center;width:100%;">
                                <div id="divAward" style="height: 100%; position: relative">

                                    @if (Model.AWAT02List != null)
                                    {
                                        <div style="overflow-y:scroll;overflow-x:auto;height: 100%;">

                                            <table class="table-ecool table-hover table-ecool-Tangerine2-SEC">
                                                <thead>
                                                    <tr class="text-center">
                                                        <th colspan="6">
                                                            可兌換獎品
                                                        </th>
                                                    </tr>
                                                    <tr class="text-center">
                                                        <th>
                                                            圖示
                                                        </th>
                                                        <th>
                                                            品名
                                                        </th>
                                                        <th>
                                                            兌換點數
                                                        </th>
                                                        <th class="hidden-xs">
                                                            剩餘數量
                                                        </th>
                                                        <th class="hidden-xs">
                                                            兌換期限
                                                        </th>
                                                        <th>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @if (Model.AWAT02List != null)
                                                    {
                                                        int AwatCount = 0;

                                                        if (Model.AWAT02List != null)
                                                        {
                                                            AwatCount = Model.AWAT02List.Count;
                                                        }

                                                        if (AwatCount > 0)
                                                        {
                                                            if (Model.AWAT02List != null)
                                                            {
                                                                foreach (var aAWAT02 in Model.AWAT02List)
                                                                {
                                                                    <tr>
                                                                        <td title="可兌換獎品.圖示" align="center" valign="middle">
                                                                            <img src='@Url.Content(ViewBag.SysAwardPath + aAWAT02.SCHOOL_NO + @"/" + aAWAT02.IMG_FILE)' class="img-responsive" alt="Responsive image" style="width:100%;max-width:70px;max-height:70px;" />
                                                                        </td>
                                                                        <td title="可兌換獎品.品名">@StringHelper.LeftStringR(aAWAT02.AWARD_NAME, 15)</td>
                                                                        <td title="可兌換獎品.兌換點數" class="text-center">@aAWAT02.COST_CASH</td>
                                                                        <td title="可兌換獎品.剩餘數量" class="text-center hidden-xs">@aAWAT02.QTY_STORAGE</td>

                                                                        <td title="可兌換獎品.兌換期限" class="hidden-xs text-center">@(aAWAT02.EDATETIME.HasValue ? aAWAT02.EDATETIME.Value.ToString("yyyy/MM/dd HH:mm:ss") : "NA" )</td>

                                                                        <td class="text-center">
                                                                            <button type="button" class="btn-default btn btn-xs" onclick="funGetExchange('@aAWAT02.AWARD_NO')">
                                                                                我要登入兌換
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                }
                                                            }
                                                        }
                                                        else
                                                        {
                                                            <tr class="text-center">
                                                                <td colspan="6">無可兌換獎品</td>
                                                            </tr>
                                                        }
                                                    }
                                                </tbody>
                                            </table>
                                            @if (Model.AWAT02List != null)
                                            {
                                                using (Html.BeginForm("LoginProduct", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1Product", name = "form1Product" }))
                                                {
                                                    @Html.AntiForgeryToken()
                                                    @Html.Hidden("WhereAWARD_NO")
                                                    @Html.Hidden("txtSCHOOL_NO")
                                                    @Html.Hidden("txtUSER_NO")
                                                }
                                                <script type="text/javascript">
                                    var targetFormID = '#form1Product';
                                    function funGetExchange(AWARD_NO) {
                                        $('#WhereAWARD_NO').val(AWARD_NO);
                                        $('#txtSCHOOL_NO').val($('#@Html.IdFor(m => m.WhereSchoolNo)').val());
                                        $('#txtUSER_NO').val($('#@Html.IdFor(m => m.WhereKeyword)').val());
                                        $(targetFormID).submit();
                                    }
                                                </script>
                                            }
                                        </div>
                                    }
                                </div>
                            </div>

                            <div class="row board_text" id="row board_text">

                                <div class="col-md-9 col-xs-8" id="board_text_frame_a4_b1">

                                    <p class="alert alert-success" id="board_text_size-a4" style="padding:5px;color:#5E86C1">

                                        <span><span class="glyphicon glyphicon-user"></span> @Model.UserNAME</span>
                                        &nbsp;&nbsp;

                                        <img src="~/Content/img/web-revise-secretary-08.png" width="25" height="25" />
                                        <span>
                                            目前酷幣: <span style="color:#ff0000;">@Model.UserCash</span> 點
                                        </span>

                                        <span class="glyphicon glyphicon-usd text-lightgreen" style="padding-left: 50px;"></span>
                                        <span style="color: #007bff">
                                            定存酷幣:
                                        </span>
                                        <span style="color:#ff0000"> @(string.Format("{0:0.###} 點", Model.UserAWAI07))</span>

                                        <br>
                                    </p>
                                    <div id="run">
                                        <span class="glyphicon glyphicon-flash text-lightgreen"></span>

                                        <span class="" id="board_text_size-a4" style="text-decoration: underline;">
                                            <span style="color:#007bff"> 運動撲滿: </span>
                                            @{
                                                string run_total = (Convert.ToDouble(Model.RUN_TOTAL_METER ?? 0) / 1000.000).ToString("#,0.000");
                                                int pos = run_total.IndexOf(".");
                                                string run_km = run_total.Substring(0, pos);
                                                string run_m = run_total.Substring(pos + 1, run_total.Length - pos - 1);
                                            }
                                            <span style="color:#ff0000;">
                                                @(string.Format("{0}公里{1}公尺", run_km, run_m))
                                            </span>
                                            @("," + (string)ViewBag.RUN_UPGRADE_RESULTSTR)
                                        </span>
                                    </div>
                                    <br>

                                    @if (Model.NoBook != "Y")
                                    {<div id="run">
                                            <span class="glyphicon glyphicon-book text-lightgreen"></span>
                                            <span class="" id="board_text_size-a4" style="text-decoration: underline;">
                                                <span style="color:#007bff">
                                                    借圖書量:
                                                </span>總借書@(Model.AllBookQty) 本，本學期借 @(Model.UserBookQty) 本，本月份借 <span style="color:#ff0000">@(Model.MonthBookQty)</span> 本
                                            </span>
                                        </div>
                                        <br>

                                    }
                                    <div id="run">
                                        <span class="glyphicon glyphicon-bookmark text-lightgreen"></span>
                                        <span class="" id="board_text_size-a4" style="text-decoration: underline;">
                                            <span style="color:#007bff">
                                                閱讀認證:
                                            </span>認證 @(Model.MyData.BOOK_QTY ?? 0) 篇，是第 <span>@(Model.MyData.LEVEL_ID ?? "0")</span> 級 @(Model.MyData.LEVEL_DESC)，再認證 @(Model.MyData.UNLEVEL_QTY == null ? "5" : Model.MyData.UNLEVEL_QTY) 本書就可以升級囉!
                                        </span>
                                    </div>
                                    <br>

                                    <div id="board_text_frame_a4_b2">
                                        <span style="color:#ffffff;">
                                            ※公式：酷幣+定存+運動公里*20+本月借書*100 =
                                        </span>

                                        <span style="color:#ff0000;">
                                            @Model.point
                                        </span>
                                        <span style="color:#ff0000;padding-left: 5px;">
                                            默默 @Model.Level 級
                                        </span>
                                        <br>
                                        <span style="color:#ffffff;">  ※本月借書和運動公里最高採計2000點 </span>
                                    </div>
                                </div>
                                <div class="col-md-3 col-xs-4" id="board_text_2-1_b1">
                                    <div class="text-center">
                                        <img class="img-responsive mycolorbox" src="@unicornPhoto_sm" href="@unicornPhoto_bg" />
                                    </div>
                                    <div class="text-white text-center" style="font-size:90%;color:#000000">
                                        <b>默默 @Model.Level 級</b>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4" id="atm_content_r" style="padding:1vw;">
                <div class="row d-flex justify-content-center align-items-start align-content-center align-self-center" id="atm_content_r-1">
                    <div class="col-7 col-sm-5 col-md-11 col-lg-12 d-flex justify-content-center align-items-center align-content-center" id="atm_content_r-c1" style="background-position:center;background-size:contain;background-repeat:no-repeat;background-image:url('@Url.Content("~/assets/img/atm_frame_video.png")');padding-right:3px;padding-left:3px;padding-top:0;padding-bottom:0;">
                        <video autoplay="" muted="" loop="" id="atm_video_v1"><source src="@Url.Content("~/assets/video/091117_ATM_Scanning-barcode.mp4")" type="video/mp4"></video>
                    </div>
                    <div class="col-12 col-sm-7 col-md-11 col-lg-12" id="atm_content_r-c2" style="padding:0;">
                        <div class="row no-gutters justify-content-center align-items-start align-content-center align-self-center" style="height:100%;width:100%;padding:10px;margin:0;">

                            <div class="col-5 col-sm-5 justify-content-center align-items-center" id="atm_btn_img_4" style="font-size:16px;background-position:center;background-size:contain;background-repeat:no-repeat;height:100%;margin:5px;" onclick="BT3_CLICK('@Model.WhereKeyword')">
                                <div></div>
                            </div>

                            <div class="col-4 col-sm-5" id="atm_btn_img_2" style="padding:0;font-size:16px;background-position:center;background-size:contain;background-repeat:no-repeat;height:100%;margin:5px;" onclick="BT2_CLICK()">
                                <div></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else if (ViewBag.ATMUID == "T")
{

    <div class="jumbotron justify-content-center align-items-center align-content-center" id="atm_bg_a1" style="padding-top:15px;">
        <div class="col d-flex justify-content-center" id="atm_head">
            <div id="atm_logo" style="background-image:url('@Url.Content("~/assets/img/atm_logo_02.png")');background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
        </div>
    </div>
    <div class="row d-flex align-items-center align-content-center" id="atm_contain_a1">

        <div class="row" id="atm_con_l_row_a1">
            <div id="ATM3">
                <div id="divAward" style="height: 100%; position: relative">
                    <table>
                        <tr>
                            <th colspan="6">
                                可兌換獎品
                            </th>
                        </tr>
                    </table>
                    @*@if (Model.AWAT02List != null)
                        {*@

                    @*}*@
                </div>
            </div>
        </div>
    </div>
}
<script src="~/Scripts/jquery.simple.timer.js?ver=2"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<script type="text/javascript">
     var oTimerId;
    

      function Timeout() {
        var SCHOOL_NO = '';
        SCHOOL_NO = $("#@Html.IdFor(m=>m.WhereSchoolNo)").val();
        if ('@Model.ChangeMode' == "(1)酷幣點數排行榜+現有點數排行榜") {
            window.location.href = "@Url.Action("LeaderIndex", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index3&TimeoutSeconds=" + '@Model.TimeoutSeconds';

        }
        else if ('@Model.ChangeMode' == "(2)閱讀認證排行榜+運動撲滿排行榜") {
   
            window.location.href = "@Url.Action("LeaderIndex2", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index3&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }
        else if ('@Model.ChangeMode' == "(3)酷幣點數排行榜+現有點数排行榜+閱讀認證排行榜+運動撲滿排行榜") {
           
            window.location.href = "@Url.Action("LeaderIndex3", "BarcCodeMyCash")?" + "WhereSchoolNo = " +@Model.WhereSchoolNo+"&FROMACTION=Index3&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }

        x = 0;

        return true;
    }


    x = 0
    function countSecond() {
        var Detime = 0;
        Detime =  @Model.DelayTime* 60;
        if (x < Detime) {
            x = x + 1
            document.getElementById("displayBox").value = x
            setTimeout("countSecond()", 1000)
        }
        else {
            Timeout();

        }
    }
    document.onmouseup = function () {
        x = 0;
    }
if ('@Model.DelayTime' != 0) {


        countSecond()

    }
     $('.TimeoutSeconds').startTimer({
         onComplete: function (element) {
             $('.loader').hide();
            document.location.href = '@Url.Action("Index", "BarcCodeMyCash", new { WhereSchoolNo = Model.WhereSchoolNo})&TimeoutSeconds=@Model.TimeoutSeconds';
        },
      });

     $(document).ready(function () {
         $('.loader').hide();
         $(".mycolorbox").colorbox({
             opacity: 0.82,
             maxWidth: '70%', maxHeight: '70%'
         });

       if ($('#@Html.IdFor(m => m.TimeoutSeconds)').val() != "") {
           var DivATM2 = document.getElementById("atm_btn_img_1");
           DivATM2.style.display = 'none';

           var DivATM2_2 = document.getElementById("atm_btn_img_2");
           DivATM2_2.style.display = 'none';

         }
             if ('@Model.DelayTime' != 0) {
            document.onmousedown = ReCalculate();
            document.onmousemove = ReCalculate();
        }
     });

     function call(e, input) {
        var code = (e.keyCode ? e.keyCode : e.which);

        if (code == 13) // 13 是 Enter 按鍵的值
        {
            event.preventDefault();
            if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
        }
     }
      function BT3_CLICK(str) {

            $('#@Html.IdFor(m => m.WhereKeyword)').val(str)
            $('#form1').attr("action", "@Url.Action("IstoryUserIndex", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo })",)
            $('#form1').attr('target', '_blank').submit().removeAttr('target');

    }
       function BT1_CLICK()
       {

           if ($('#@Html.IdFor(m => m.ShowStep2)').val().toLowerCase() == "true") {

               $(".board_text").toggle();
               $("#ATM3").toggle();

        }
        else {
            alert('未輸入學號/掃描條碼')
        }

    }

    function BT2_CLICK() {
        $('#@Html.IdFor(m => m.WhereKeyword)').val('')
        $('#form1').attr("action", "@Url.Action("Index3Page", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo})")
        $('#form1').submit();

    }
</script>