﻿@{
    ViewBag.Title = "SSO登入";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<style>
    .bg-white {
        background-color: #fff
    }

</style>

<div class="container">
    <div class="text-center padding-lg">
        <h1 class="text-success"><b>登入</b></h1>
    </div>
    <ul>
        <li>
            <a href="@Url.Action("SSOLoginPage")">
                <div class="row card bg-white">
                    <div class="col-md-2">
                        <img src="@Url.Content("~/Content/images/oidc-v.png")" class="img-responsive" />
                    </div>
                    <div class="col-md-10">
                        <strong>學校登入</strong><br />
                        <span class="detailText">使用臺北市政府教育局單一身分驗證服務登入系統。若無帳號或其他問題請洽詢臺北市教育局資訊教育科謝琪文老師,電話:(02)27208889轉1234 E-Mail:edu_ict.19＠mail.taipei.gov.tw</span>
                    </div>
                </div>
            </a>
        </li>
    </ul>

    @Html.Partial("_StatusMessage")
</div>

@section css{
    <style>
    body{
        background-color:black;
        background-image:url('@Url.Content("~/images/patterns/login-bg-w.png")');
        font-family: 'Microsoft JhengHei','sans-serif'
    }
    </style>
    <style>
        .card {
            border-radius: 10px 10px;
            padding: 2% 10%;
            margin: 3%;
        }

            .card:hover {
                cursor: pointer;
                box-shadow: 0px 4px 30px yellow;
            }

        li strong {
            font-size: 2em;
            line-height: 1.5em;
        }

        li .detailText {
            font-size: 1.2em;
        }

        .text-success {
            color: #b6ff00;
            font-family: 'Microsoft JhengHei','sans-serif';
            font-size: 2.5em;
            margin-top: 5%;
        }
    </style>
}

@section scripts{
    <!--登出sso-->
    <script>
        $(function () {
            logoutsso();
        });

        function logoutsso() {
            $.ajax({
                url: 'https://ldap.tp.edu.tw/api/logout',
                type: 'get',
                dataType: 'jsonp',
            })
        }
    </script>
}