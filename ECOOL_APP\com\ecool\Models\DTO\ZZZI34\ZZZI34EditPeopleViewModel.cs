﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34EditPeopleViewModel
    {

        public int ShowBtn { get; set; }

        public string ART_GALLERY_TYPE { get; set; }
        public bool? AutherYN { get; set; }
       
        /// <summary>
        ///照片作者學校代碼
        /// </summary>
        [DisplayName("照片作者學校代碼")]
        public string PHOTO_SCHOOL_NO { get; set; }
        public DateTime? PHOTO_CHG_DATE { get; set; }
        /// <summary>
        ///照片作者帳號
        /// </summary>
        [DisplayName("照片作者帳號")]
        [Required(ErrorMessage = "*此欄位必輸")]
        public string PHOTO_USER_NO { get; set; }
        public string PHOTO_SNAME { get; set; }

        /// <summary>
        ///照片作者班級
        /// </summary>
        [DisplayName("照片作者班級")]
        public string PHOTO_CLASS_NO { get; set; }


        public virtual List<ZZZI34EditPhotoViewModel> Photo { get; set; }
    }
}