﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'a11yhelp', 'zh', {
	title: '輔助工具指南',
	contents: '說明內容。若要關閉此對話框請按「ESC」。',
	legend: [
		{
		name: '一般',
		items: [
			{
			name: '編輯器工具列',
			legend: '請按 ${toolbarFocus} 以導覽到工具列。利用 TAB 或 SHIFT+TAB 以便移動到下一個及前一個工具列群組。利用右方向鍵或左方向鍵以便移動到下一個及上一個工具列按鈕。按下空白鍵或 ENTER 鍵啟用工具列按鈕。'
		},

			{
			name: '編輯器對話方塊',
			legend:
				'在對話框中，按下 TAB 鍵以導覽到下一個對話框元素，按下 SHIFT+TAB 以移動到上一個對話框元素，按下 ENTER 以遞交對話框，按下 ESC 以取消對話框。當對話框有多個分頁時，可以使用 ALT+F10 或是在對話框分頁順序中的一部份按下 TAB 以使用分頁列表。焦點在分頁列表上時，分別使用右方向鍵及左方向鍵移動到下一個及上一個分頁。'
		},

			{
			name: '編輯器內容功能表',
			legend: '請按下「${contextMenu}」或是「應用程式鍵」以開啟內容選單。以「TAB」或是「↓」鍵移動到下一個選單選項。以「SHIFT + TAB」或是「↑」鍵移動到上一個選單選項。按下「空白鍵」或是「ENTER」鍵以選取選單選項。以「空白鍵」或「ENTER」或「→」開啟目前選項之子選單。以「ESC」或「←」回到父選單。以「ESC」鍵關閉內容選單」。'
		},

			{
			name: '編輯器清單方塊',
			legend: '在清單方塊中，使用 TAB 或下方向鍵移動到下一個列表項目。使用 SHIFT+TAB 或上方向鍵移動到上一個列表項目。按下空白鍵或 ENTER 以選取列表選項。按下 ESC 以關閉清單方塊。'
		},

			{
			name: '編輯器元件路徑工具列',
			legend: '請按 ${elementsPathFocus} 以瀏覽元素路徑列。利用 TAB 或右方向鍵以便移動到下一個元素按鈕。利用 SHIFT 或左方向鍵以便移動到上一個按鈕。按下空白鍵或 ENTER 鍵來選取在編輯器中的元素。'
		}
		]
	},
		{
		name: '命令',
		items: [
			{
			name: '復原命令',
			legend: '請按下「${undo}」'
		},
			{
			name: '重複命令',
			legend: '請按下「 ${redo}」'
		},
			{
			name: '粗體命令',
			legend: '請按下「${bold}」'
		},
			{
			name: '斜體',
			legend: '請按下「${italic}」'
		},
			{
			name: '底線命令',
			legend: '請按下「${underline}」'
		},
			{
			name: '連結',
			legend: '請按下「${link}」'
		},
			{
			name: '隱藏工具列',
			legend: '請按下「${toolbarCollapse}」'
		},
			{
			name: '存取前一個焦點空間命令',
			legend: '請按下 ${accessPreviousSpace} 以存取最近但無法靠近之插字符號前的焦點空間。舉例：二個相鄰的 HR 元素。\r\n重複按鍵以存取較遠的焦點空間。'
		},
			{
			name: '存取下一個焦點空間命令',
			legend: '請按下 ${accessNextSpace} 以存取最近但無法靠近之插字符號後的焦點空間。舉例：二個相鄰的 HR 元素。\r\n重複按鍵以存取較遠的焦點空間。'
		},
			{
			name: '協助工具說明',
			legend: '請按下「${a11yHelp}」'
		}
		]
	}
	],
	backspace: '退格鍵',
	tab: 'Tab',
	enter: 'Enter',
	shift: 'Shift',
	ctrl: 'Ctrl',
	alt: 'Alt',
	pause: 'Pause',
	capslock: 'Caps Lock',
	escape: 'Esc',
	pageUp: 'Page Up',
	pageDown: 'Page Down',
	end: 'End',
	home: 'Home',
	leftArrow: '向左箭號',
	upArrow: '向上鍵號',
	rightArrow: '向右鍵號',
	downArrow: '向下鍵號',
	insert: '插入',
	'delete': '刪除',
	leftWindowKey: '左方 Windows 鍵',
	rightWindowKey: '右方 Windows 鍵',
	selectKey: '選擇鍵',
	numpad0: 'Numpad 0',
	numpad1: 'Numpad 1',
	numpad2: 'Numpad 2',
	numpad3: 'Numpad 3',
	numpad4: 'Numpad 4',
	numpad5: 'Numpad 5',
	numpad6: 'Numpad 6',
	numpad7: 'Numpad 7',
	numpad8: 'Numpad 8',
	numpad9: 'Numpad 9',
	multiply: '乘號',
	add: '新增',
	subtract: '減號',
	decimalPoint: '小數點',
	divide: '除號',
	f1: 'F1',
	f2: 'F2',
	f3: 'F3',
	f4: 'F4',
	f5: 'F5',
	f6: 'F6',
	f7: 'F7',
	f8: 'F8',
	f9: 'F9',
	f10: 'F10',
	f11: 'F11',
	f12: 'F12',
	numLock: 'Num Lock',
	scrollLock: 'Scroll Lock',
	semiColon: '分號',
	equalSign: '等號',
	comma: '逗號',
	dash: '虛線',
	period: '句點',
	forwardSlash: '斜線',
	graveAccent: '抑音符號',
	openBracket: '左方括號',
	backSlash: '反斜線',
	closeBracket: '右方括號',
	singleQuote: '單引號'
} );
