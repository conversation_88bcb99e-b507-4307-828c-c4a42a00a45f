﻿/*
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'scayt', 'ru', {
	btn_about: 'О SCAYT',
	btn_dictionaries: 'Словари',
	btn_disable: 'Отключить SCAYT',
	btn_enable: 'Включить SCAYT',
	btn_langs:'Языки',
	btn_options: 'Настройки',
	text_title: 'Проверка орфографии по мере ввода (SCAYT)'
});
