﻿@model BarcCodeMyCashIndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    /**/

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    //兌換獎品系統路徑
    ViewBag.SysAwardPath = EcoolWeb.Controllers.AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);
}

@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.WhereSchoolNo)
@Html.HiddenFor(m => m.TimeoutSeconds)
@Html.HiddenFor(m => m.NoBook)
@Html.HiddenFor(m => m.ShowStep2)

@Html.Partial("_Notice")

<div class="jumbotron justify-content-center align-items-center align-content-center" id="atm_bg_a1" style="padding-top:15px;">
    <div class="col d-flex justify-content-center" id="atm_head">
        <div id="atm_logo" style="background-image:url('@Url.Content("~/assets/img/atm_logo_02.png")');background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
    </div>
    <div class="row d-flex align-items-center align-content-center" id="atm_contain_a1">
        <div class="col d-flex justify-content-center align-items-center align-content-center col-lg-8 col-md-8 col-sm-12 col-12" id="atm_content_l_a1">
            <div class="row" id="atm_con_l_row_a1">
                <div class="col d-flex flex-column justify-content-center align-items-center" style="height:100%">

                    <div class="row" style="height:33%;width:100%;background-image:url('@Url.Content("~/assets/img/atm_frame_txt_01.png")');background-position:center;background-size:contain;background-repeat:no-repeat;">
                        <div class="col offset-xl-0 col-2" id="board_text_1-1_a1" style="height:100%;background-image:url('@Url.Content("~/assets/img/atm_title_txt_01.png")');background-position:center;background-size:contain;background-repeat:no-repeat;margin-left:8px;">
                            <div></div>
                        </div>
                        <div class="col-8 col-sm-9 d-flex align-items-center" id="board_text_1-2_a1" style="height:100%;padding:0px;">
                            <p class="text-truncate text-left align-items-center align-content-center" id="board_text_size-a1" style="width:100%;">@ViewBag.SortBoard<br></p>
                        </div>
                    </div>
                    <div class="row" style="height:33%;width:100%;background-image:url('@Url.Content("~/assets/img/atm_frame_txt_02-1.png")');background-position:center;background-size:contain;background-repeat:no-repeat;">
                        <div class="col d-flex justify-content-center align-items-center" id="board_text_1-3_a1" style="height:100%;padding:0px;">
                            @Html.EditorFor(m => m.WhereKeyword, new { htmlAttributes = new { @style = "padding:0;", @onKeyPress = "call(event,this);" } })
                        </div>
                    </div>

                    <div class="row" style="height:33%;width:100%;background-image:url('@Url.Content("~/assets/img/atm_frame_txt_03.png")');background-position:center;background-size:contain;background-repeat:no-repeat;">
                        <div class="col d-flex justify-content-center align-items-center align-content-center" id="board_text_1-4_a1">
                            <marquee scrollamount="15" width="100%">
                                @* <p id="board_text_size-a1-1">@ViewBag.SortBoard2</p>*@
                                <p id="board_text_size-a1-1">新年快樂，臺北e酷幣祝大家平平安安、學業進步</p>
                            </marquee>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4" id="atm_content_r" style="padding:1vw;">
            <div class="row justify-content-center" id="atm_content_r-1">
                <div class="col-7 col-sm-5 col-md-11 col-lg-12 d-flex justify-content-center align-items-center align-content-center" id="atm_content_r-c1" style="background-position:center;background-size:contain;background-repeat:no-repeat;background-image:url('@Url.Content("~/assets/img/atm_frame_video.png")');padding-right:3px;padding-left:3px;padding-top:0;padding-bottom:0;">
                    <video autoplay="" muted="" loop="" id="atm_video_v1"><source src="@Url.Content("~/assets/video/091117_ATM_Scanning-barcode.mp4")" type="video/mp4"></video>
                </div>
                <div class="col-12 col-sm-12 col-md-11 col-lg-12" id="atm_content_r-c2" style="padding:0;">
                    <div class="row no-gutters justify-content-center align-items-center align-content-center align-self-center" style="height:100%;width:100%;padding:10px;margin:0;">
                        <div class="col-5 col-sm-5 justify-content-center align-items-center" id="atm_btn_img_1"
                             style="font-size:16px;background-position:center;background-size:contain;background-repeat:no-repeat;height:100%;margin:5px;" onclick="BT1_CLICK()">
                            <div></div>
                        </div>

                        <div class="col-4 col-sm-5" id="atm_btn_img_2" style="padding:0;font-size:16px;background-position:center;background-size:contain;background-repeat:no-repeat;height:100%;margin:5px;" onclick="BT2_CLICK()">
                            <div></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<input type="text" id="displayBox" name="displayBox" value="0" style="display:none">
@*<div class="jumbotron justify-content-center align-items-center align-content-center" id="atm_bg_a1" style="padding-top:15px;">
        <div class="col d-flex justify-content-center" id="atm_head">
            <div id="atm_logo" style="background-image:url('@Url.Content("~/assets/img/atm_logo_02.png")');background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
        </div>
    </div>*@
@*<div class="row d-flex align-items-center align-content-center" id="atm_contain_a1">

        <div class="row" id="atm_con_l_row_a1">
        </div>
    </div>*@

<script type="text/javascript">
      var oTimerId;
    function Timeout() {
        var SCHOOL_NO = '';
        SCHOOL_NO = $("#@Html.IdFor(m=>m.WhereSchoolNo)").val();
       if ('@Model.ChangeMode' == "(1)酷幣點數排行榜+現有點數排行榜") {
            
                 window.location.href = "@Url.Action("LeaderIndex", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index&TimeoutSeconds=" + '@Model.TimeoutSeconds';
         
           

        }
        else if ('@Model.ChangeMode' == "(2)閱讀認證排行榜+運動撲滿排行榜") {
         
            
         
            window.location.href =    "@Url.Action("LeaderIndex2", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }
        else if ('@Model.ChangeMode' == "(3)酷幣點數排行榜+現有點数排行榜+閱讀認證排行榜+運動撲滿排行榜")  {
           
                    window.location.href =    "@Url.Action("LeaderIndex3", "BarcCodeMyCash")?" + "WhereSchoolNo = " +@Model.WhereSchoolNo+"&FROMACTION=Index&TimeoutSeconds=" + '@Model.TimeoutSeconds';


       
                
        }
        x = 0;

        return true;
    }



    x = 0
    function countSecond() {
        var Detime = 0;
        Detime =  @Model.DelayTime* 60;
        if (x < Detime) {
            x = x + 1
            document.getElementById("displayBox").value = x
            setTimeout("countSecond()", 1000)
        }
        else {
            Timeout();

        }
    }
    document.onmouseup = function () {
        x = 0;
    }
    countSecond()
    $(document).ready(function () {
          $("#@Html.IdFor(m=>m.WhereKeyword)").val('');
          $("#@Html.IdFor(m=>m.WhereKeyword)").focus();

           if ($('#@Html.IdFor(m => m.TimeoutSeconds)').val() != "") {
              var DivATM2 = document.getElementById("atm_btn_img_1");
              DivATM2.style.display = 'none';

              var DivATM2_2 = document.getElementById("atm_btn_img_2");
              DivATM2_2.style.display = 'none';

            }

        });

     function call(e, input) {
        var code = (e.keyCode ? e.keyCode : e.which);

        if (code == 13) // 13 是 Enter 按鍵的值
        {
            event.preventDefault();
            if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
        }
    }

    // 點選查詢Div Row也submit
    $("#board_text_1-3_a1").on('click', function (e) {
        // 點選的是input則返回
        if (e.target.id == "WhereKeyword") return;
        $('#@Html.IdFor(m => m.WhereKeyword)').focus();
        if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
    });

   function BT1_CLICK()
   {
       alert('未輸入學號/掃描條碼')
   }
    function BT3_CLICK() {
        var wkey = $("#WhereKeyword").val();
        if (wkey == '' || wkey == undefined) {
            alert('未輸入學號/掃描條碼');
        }
        else {
            $('#form1').attr("action", "@Url.Action("IstoryUserIndex", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo })",)
            $('#form1').attr('target', '_blank').submit().removeAttr('target');

        }
    }
    function BT2_CLICK() {
        $('#@Html.IdFor(m => m.WhereKeyword)').val('')
        $('#form1').attr("action", "@Url.Action("Index", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo})")
        $('#form1').submit();

    }
</script>