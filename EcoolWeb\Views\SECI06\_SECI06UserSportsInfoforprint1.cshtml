﻿@model SECI06IndexViewModel
@{
    string UserDomain = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Host + ":" + HttpContext.Current.Request.Url.Port;
}
@if (!string.IsNullOrWhiteSpace(Model.WhereUSER_NO))
{
    <section class="profile mb-4">
        <img src="@<EMAIL>("~/Content/images/header-bg.png")" alt="">
        <div class="row px-5">
            <div class="col-lg-12 bg-custom-yellow bgPosition-profile">
                <h1 class="text-center mt-5 py-3 page-break-before-auto">個人資料</h1>
                <h2 class="heading-h2 mx-3">基本資料</h2>
                <div class="bg-custom-white break-avoid d-flex justify-content-center align-items-center">
                    <div class="row flex-grow-1">
                        <div class="col-5">
                            <div class="profilePhoto">
                                @if (Model!=null&&Model.PlayerUrl != null && Model.PlayerUrl != "")
                                {
                            
                                        <img src="@Url.Content(Model.PlayerUrl)" alt="個人相片">

                           
                                    

                                }
                            </div>
                        </div>
                        <div class="col-7 d-flex align-items-center">
                            @if (Model != null && Model.MyHRMT01 != null)
                            {
                                <table class="table">
                                    <tr>
                                        <th scope="row" width="45%">姓名</th>
                                        <td>@Html.DisplayFor(modelItem => Model.MyHRMT01.SNAME)</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">生日</th>
                                       @if (Model.MyHRMT01.BIRTHDAY != null)
                                       {
                                        <td>@Model.MyHRMT01.BIRTHDAY.Value.ToString("yyyy/MM/dd")</td>}
                                       else { <td></td>}
                                    </tr>
                                    <tr>
                                        <th scope="row">性別</th>

                                        <td>@HRMT01.ParserSex(Model.MyHRMT01.SEX)</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">入學年</th>
                                        <td>@(Model.MyHRMT01.USER_NO.Substring(0, 3)) /8 月 </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">畢業年</th>
                                        <td>@(Int32.Parse(Model.MyHRMT01.USER_NO.Substring(0, 3)) + 6)/6 月 </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">目前班級</th>
                                        <td>@Html.DisplayFor(modelItem => Model.MyHRMT01.CLASS_NO)</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">目前座號</th>
                                        <td>@Html.DisplayFor(modelItem => Model.MyHRMT01.SEAT_NO)</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">目前班導師</th>
                                        <td>@Html.DisplayFor(modelItem => Model.TeacherSTR)</td>
                                    </tr>
                                </table>
                            }
                            else
                            {
                                <div class="h2 text-muted text-center flex-grow-1">
                                    無資料
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <h2 class="heading-h2 mx-3">身高體重</h2>
                <div class="bg-custom-white break-avoid d-flex justify-content-center align-items-center">
                    <div class="row flex-grow-1">
                        <div class="col-4">
                            <div class="img-outBox">
                                <img src="@<EMAIL>("~/Content/images/viewBody1.png")" alt="decoration giraffe">
                            </div>
                        </div>
                        <div class="col-8 d-flex align-items-center">
                            @if (Model != null && Model.NowHrmt08 != null)
                            {
                                <table class="table">
                                    <tr>
                                        <th scope="row" width="45%">測量時間</th>
                                        <td>
                                            @Model.NowHrmt08.SYEAR
                                            @(Model.NowHrmt08.SEMESTER == 1 ? "上" : "下")
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">身高</th>

                                        <td>
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.TALL)cm
                                            @if (Model.NowHrmt08.TALL == 0)
                                            {
                                                @:(未測量)
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">體重</th>
                                        <td>
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.WEIGHT)kg

                                            @if (Model.NowHrmt08.WEIGHT == 0)
                                            {
                                                @:(未測量)
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">左眼裸視</th>
                                        <td>
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.VISION_LEFT)
                                            @if (Model.NowHrmt08.VISION_LEFT == "0")
                                            {
                                                @:(未測量)
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">右眼裸視</th>
                                        <td>
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.VISION_RIGHT)
                                            @if (Model.NowHrmt08.VISION_RIGHT == "0")
                                            {
                                                @:(未測量)
                                            }
                                        </td>
                                    </tr>
                                </table>
                            }
                            else
                            {
                                <div class="h2 text-muted text-center flex-grow-1">
                                    無資料
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <h2 class="heading-h2 mx-3">體適能</h2>
                <div class="bg-custom-white break-avoid">
                    <div class="row">
                        <div class="col-4">
                            <div class="img-outBox">
                                <img src="@<EMAIL>("~/Content/images/viewBody2.png")" alt="decoration boy">
                            </div>
                        </div>
                        <div class="col-8 d-flex align-items-center">
                            @if (Model != null && Model.NowHrmt09 != null)
                            {
                                <table class="table">
                                    <tr>
                                        <th scope="row" width="45%">測量時間</th>
                                        <td> @Html.DisplayFor(modelItem => Model.NowHrmt09.SYEAR) 學年</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th scope="row"></th>
                                        <td>
                                            <b>結果</b>
                                        </td>
                                        <td>
                                            <b>當屆平均</b>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">坐姿體前彎(cm)</th>
                                        <td>
                                            @if (Model.NowHrmt09.V_SET_REACH_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.V_SET_REACH_TEST)
                                                if (Model.NowHrmt09.V_SET_REACH_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                        <td>
                                            @if (Model.NowHrmt09.AVG_V_SET_REACH_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_V_SET_REACH_TEST)
                                                if (Model.NowHrmt09.AVG_V_SET_REACH_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">立定跳遠(cm)</th>
                                        <td>
                                            @if (Model.NowHrmt09.S_L_JUMP_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.S_L_JUMP_TEST)
                                                if (Model.NowHrmt09.S_L_JUMP_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                        <td>
                                            @if (Model.NowHrmt09.AVG_S_L_JUMP_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_S_L_JUMP_TEST)
                                                if (Model.NowHrmt09.AVG_S_L_JUMP_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">仰臥起坐(次/min)</th>
                                        <td>
                                            @if (Model.NowHrmt09.SIT_UPS_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.SIT_UPS_TEST)
                                                if (Model.NowHrmt09.SIT_UPS_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                        <td>
                                            @if (Model.NowHrmt09.AVG_SIT_UPS_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_SIT_UPS_TEST)
                                                if (Model.NowHrmt09.AVG_SIT_UPS_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">800 公尺跑走(min)</th>
                                        <td>
                                            @if (Model.NowHrmt09.C_P_F_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.C_P_F_TEST)
                                                if (Model.NowHrmt09.C_P_F_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                        <td>
                                            @if (Model.NowHrmt09.AVG_C_P_F_TEST != null)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_C_P_F_TEST)
                                                if (Model.NowHrmt09.AVG_C_P_F_TEST == (decimal)0.00)
                                                {
                                                    @:(未測量)
                                                }
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </td>
                                    </tr>
                                </table>
                            }
                            else
                            {
                                <div class="h2 text-muted text-center flex-grow-1">
                                    無資料
                                </div>
                            }
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
}