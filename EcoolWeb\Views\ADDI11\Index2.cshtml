﻿@model ADDI11IndexViewModel
@{


    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    Double Max_LAP_M = Math.Floor(Convert.ToDouble((2000.0 / Model.ONE_LAP_M) * 10)) / 10;
}
@Html.Partial("_Notice")
@using (Html.BeginForm("Index2", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off"}))
{
   
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.RunDate)
    @Html.HiddenFor(m => m.LAP)
    @Html.HiddenFor(m=>m.Page)
    @Html.HiddenFor(m => m.ONE_LAP_M)
    @Html.Hidden("Max_LAP_M", Max_LAP_M)
<div class="row">
    <div class="col-md-8 col-md-offset-2">
        <div class="panel with-nav-tabs panel-info" id="panel">
            <div class="panel-heading">
                <h1 style="font-size:60px">@ViewBag.Title</h1>
            </div>
            <div class="panel-body">
                <div >
                    <span style="color:orangered;font-size:large">每日跑步公里數不得大於2公里</span>
                    <div class="input-group input-group-lg">
                        <span class="input-group-addon"><i class="fa fa-user"></i></span>

                        @Html.EditorFor(m => m.CARD_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "本欄位可感應數位學生證，也可以輸入學號", @onKeyPress = "call(event,this);" } })
                        @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                        @Html.ValidationMessage("LAP", "", new { @class = "text-danger" })
                    </div>

                </div>
                <input type="button" value="送出" onclick="OnclickCardNO()" />
                <input type="button" value="清除" onclick="myrefresh()" />
            </div>
        </div>

    </div>
    <br/><br/><br/><br/>
    <div id="editorRows" class="col-md-8 col-md-offset-2">
        @Html.Action("_ADDRunDetailLog", (string)ViewBag.BRE_NO)
    </div>
</div>
    <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
            <br />


            <h3 style="color:#80b4fb">讀取中…</h3>

        </div>
    </div>
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>
}


@section Scripts {

    <script src="~/Scripts/buzz/buzz.min.js"></script>

    <script language="JavaScript">

     

         var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
          var game_trueSound = new buzz.sound("@Url.Content("~/Content/mp3/game_true.mp3")");
        var targetFormID = '#form1';

        var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );

        (function ($) {

            $(window).on("beforeunload", function () {
    return true;
})
        })(jQuery);

        function AutoVal(Val) {
            var re = /^[0-9]+(\.[0-9]{1,1})?$/;
            if (!re.test(Val)) {
                alert("只能輸入數字，且小數位數只能到第1位");
                $("#LAP").val('')
                return false;
            }

            var Max_LAP_M = $("#Max_LAP_M").val()

            if ((Val / 1) > (Max_LAP_M / 1) || (Val / 1) < (0)) {
                alert("輸入0~" + Max_LAP_M + "的數字");
                $("#LAP").val('')
                return false;
            }

            //$(".LAP_Edit").each(function (i) {
            //    this.value = Val;
            //});
        }
        function deleteRow(user_no, runDate) {
            var result = confirm('確認要刪除嗎?');
            var data = {
                "USER_NO": user_no,
                "RunDtae": runDate,
               
            };
            if (result) {
         
            $.ajax({

                url: '@Url.Action("ADDRunDelete", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                contentType: 'json',
                success: function (hdt) {
                    console.log(hdt);
                    var res = $.parseJSON(hdt);
                    if (res.Success == "flase") {
                        
                                    $.ajax({

                                        url: '@Url.Action("_ADDRunDetailLog", (string)ViewBag.BRE_NO)',
                                        data: data,
                                        cache: false,
                                        success: function (html) {
                                            $("#editorRows").html('');
                                            $("#editorRows").prepend(html);
                                        }


                                    });
                        $('#ErrorStr').html(res.Name + '紀錄已刪除…');
                        $('#ErrorDiv').show();
                        setTimeout(function () {

                    
                            $('#ErrorDiv').hide()
                        }, 800);
                    }
                }
            });
            }
        }
        function myrefresh() {
            $("#CARD_NO").val('');
            $("#BarCode").val('');
            $("#StatusMessageDiv").remove();
           // window.location.reload();
        }

        $(document).ready(function () {
            $("#CARD_NO").focus();

        });

        function OnclickCardNO() {
            $("#StatusMessageDiv").val('');
            $(window).unbind('beforeunload');

            var SCHOOL_NO1 = "";
            var CARD_NO = $('#CARD_NO').val();
            var LAP = $("#LAP").val();
            var ONE_LAP_M = $("#ONE_LAP_M").val();
            SCHOOL_NO1 = $("#SCHOOL_NO1").val();
            $('#CARD_NO').prop('readonly', true);
            var data = {
                "CARD_NO": CARD_NO,
                "SCHOOL_NO1": SCHOOL_NO1,
                "LAP": LAP,
                "ONE_LAP_M":ONE_LAP_M
            };
           // $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;");
            if (CARD_NO.length == 0) {
                alert("請輸入學號或數位學生證");

            }

            else {
            
                $.ajax({

                                  url: '@Url.Action("CheckUSerInfo", (string)ViewBag.BRE_NO)',
                data: data,
                    cache: false,
                    contentType: 'json',
                    success: function (hdt) {
                        console.log(hdt);
                        var res = $.parseJSON(hdt);

                        if (res.Success == "flase") {
                            // $('#loading').hide();
                            var Names = res.Name;
                            $.ajax({

                                url: '@Url.Action("ADDRunEditSave", (string)ViewBag.BRE_NO)',
                                data: data,
                                cache: false,
                                success: function (hdt) {

                                    console.log(hdt);
                                    var res1 = $.parseJSON(hdt);
                                    if (res1.Success == "flase") {
                                        setTimeout(function () {
                                            $('#ErrorStr').html(Names + '感應成功…');
                                            $('#ErrorDiv').show();
                                        }, 100);
                                        SwipeOK();

                                        setTimeout(function () {
                                            $('#CARD_NO').prop('readonly', false);
                                            $('#ErrorDiv').hide()

                                        }, 800);
                                        $.ajax({

                                            url: '@Url.Action("_ADDRunDetailLog", (string)ViewBag.BRE_NO)',
                                            data: data,
                                            cache: false,
                                            success: function (html) {
                                                $("#editorRows").html('');
                                                $("#editorRows").prepend(html);
                                            }


                                        });
                                        //
                                    }
                                    else {

                                        $('#ErrorStr').html(res1.Error);
                                        $('#ErrorDiv').show()
                                        setTimeout(function () {

                                            $('#CARD_NO').prop('readonly', false);
                                            $('#ErrorDiv').hide()
                                        }, 800);
                                    }
                                }
                            });
                        }
                        else {
                            $('#ErrorStr').html('此數位學生證對應不到學生資料…');
                            $('#ErrorDiv').show()
                            setTimeout(function () {

                            $('#CARD_NO').prop('readonly', false);
                                $('#ErrorDiv').hide()
                            }, 800);
                        }
                    }



                });

            }

            $('#CARD_NO').val('');
            }
            function call(e, input) {
                var code = (e.keyCode ? e.keyCode : e.which);

                var CARD_NO = $('#CARD_NO').val();
                if (CARD_NO.length > 0) {
                    if (code == 13) // 13 是 Enter 按鍵的值
                    {
                        event.preventDefault();

                        $('#CARD_NO').prop('readonly', true);
                    //    $(".row").attr("style", "display:none");
                        setTimeout(function () {

                            OnclickCardNO();
                        });
                    }

                }

            }
            function SwipeOK() {

                SwipeSound.play();
            }
    </script>




}
