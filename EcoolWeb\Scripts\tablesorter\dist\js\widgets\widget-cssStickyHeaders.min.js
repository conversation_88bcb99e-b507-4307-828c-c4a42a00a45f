(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: cssStickyHeaders - updated 6/16/2018 (v2.30.6) */
!function(d,o){"use strict";var a=d.tablesorter;a.addWidget({id:"cssStickyHeaders",priority:10,options:{cssStickyHeaders_offset:0,cssStickyHeaders_addCaption:!1,cssStickyHeaders_attachTo:null,cssStickyHeaders_filteredToTop:!0},init:function(e,t,s,i){!function e(r,l){var t,p,h=r.$table,f=d(l.cssStickyHeaders_attachTo),g="ActiveXObject"in o||-1<o.navigator.userAgent.indexOf("Edge"),s=r.namespace+"cssstickyheader ",k=h.children("thead"),y=h.children("caption"),H=f.length?f:d(o),u=h.parent().closest("table."+a.css.table),S=u.length&&a.hasWidget(u[0],"cssStickyHeaders")?u.children("thead"):[],b=parseInt(h.css("border-top-width"),10)||0,m=h.height(),_=l.cssStickyHeaders_addCaption,C=!1,T=!1,v=function(e,t){var s=0===t?"":"translate(0px,"+t+"px)";e.css({transform:s,"-ms-transform":s,"-webkit-transform":s})};y.length&&(y.hide(),T=h.height()===m,y.show(),t=h.offset().top,v(y,20),C=h.offset().top!==t,v(y,0)),H.unbind("scroll resize ".split(" ").join(s).replace(/\s+/g," ")).bind("scroll resize ".split(" ").join(s),function(){l=r.widgetOptions,C&&(v(y,0),p=h.offset().top),H.scrollTop()<y.outerHeight(!0)&&(m=h.height());var e=f.length?f.offset().top:H.scrollTop(),t=(y.outerHeight(!0)||0)+(parseInt(h.css("padding-top"),10)||0)+(parseInt(h.css("border-spacing"),10)||0),s=m+(T&&l.cssStickyHeaders_addCaption?t:0)-k.height()-(h.children("tfoot").height()||0)-(l.cssStickyHeaders_addCaption?t:T?0:t),i=S.length?S.height():0,a=S.length?g?u.data("cssStickyHeaderBottom")+i:S.offset().top+i-H.scrollTop():0,d=C?p:h.offset().top,o=e-(T?d-(l.cssStickyHeaders_addCaption?t:0):d)+a+b+(l.cssStickyHeaders_offset||0)-(l.cssStickyHeaders_addCaption?T?t:0:t),c=0<o&&o<=s?o:0,n=g?k.children().children():k;g&&r.$table.data("cssStickyHeaderBottom",(S.length?i:0)-(l.cssStickyHeaders_addCaption?t:0)),l.cssStickyHeaders_addCaption&&(n=n.add(y)),_!==l.cssStickyHeaders_addCaption&&((_=l.cssStickyHeaders_addCaption)||v(y,0)),v(n,c)}),h.unbind("filterEnd updateComplete ".split(" ").join(s).replace(/\s+/g," ")).bind("filterEnd"+s,function(){l.cssStickyHeaders_filteredToTop&&o.scrollTo(0,h.position().top)}).bind("updateComplete"+s,function(){e(r,r.widgetOptions)})}(s,i)},remove:function(e,t,s,i){if(!i){var a=t.namespace+"cssstickyheader ";d(o).unbind("scroll resize ".split(" ").join(a).replace(/\s+/g," ")),t.$table.unbind("filterEnd scroll resize updateComplete ".split(" ").join(a).replace(/\s+/g," ")).add(t.$table.children("thead").children().children()).children("thead, caption").css({transform:"","-ms-transform":"","-webkit-transform":""})}}})}(jQuery,window);return jQuery;}));
