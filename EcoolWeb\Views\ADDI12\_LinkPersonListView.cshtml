﻿@model  List<ADDI12LinkPersonViewModel>

<div class="table-responsive">
    <div class="css-table" style="width:92%;">
        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
            <div class="th" style="text-align:center">
                學校
            </div>
            <div class="th" style="text-align:center">
                姓名
            </div>
            <div class="th" style="text-align:center">
                班級
            </div>
            <div class="th" style="text-align:center">
                時間
            </div>
            <div class="th" style="text-align:center">
                是否首播按讚
            </div>
        </div>
        <div id="editorRows" class="tbody">
            @if (Model.Count() > 0)
            {
                foreach (var Item in Model)
                {
                    <div class="tr">
                        <div class="td" style="text-align:center">
                            @Item.SHORT_NAME
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.NAME
                        </div>

                        <div class="td" style="text-align:center">
                            @Item.CLASS_NO
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.CRE_DATE
                        </div>
                        <div class="td" style="text-align:center">
                            @if ((Item.IS_PREMIER ?? false))
                            {
                                <b>首播按讚</b>
                            }
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>