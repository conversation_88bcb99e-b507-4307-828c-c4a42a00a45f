﻿@model EcoolWeb.ViewModels.ZZZI20DetailsViewModel
@{
    ViewBag.Title = "酷幣匯轉功能-異常學生處理狀況";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <caption>
                <div class="Caption_Div_Left">
                    轉入學生
                </div>
            </caption>
            <thead>
                <tr>
                    <th style="text-align: center">
                        班級
                    </th>
                    <th style="text-align: center">
                        學號
                    </th>
                    <th style="text-align: center">
                        座號
                    </th>
                    <th style="text-align: center">
                        姓名
                    </th>
                    <th style="text-align: center">
                        狀態
                    </th>
                </tr>
            </thead>
           <tbody>
               <tr style="text-align: center">
                   <td>
                       @Html.DisplayFor(modelItem => Model.TurnIn.CLASS_NO)
                   </td>
                   <td>
                       @Html.DisplayFor(modelItem => Model.TurnIn.USER_NO)
                   </td>
                   <td>
                       @Html.DisplayFor(modelItem => Model.TurnIn.SEAT_NO)
                   </td>
                   <td>
                       @Html.DisplayFor(modelItem => Model.TurnIn.NAME)
                   </td>
                   <td>
                       @UserStaus.GetDesc(Model.TurnIn.USER_STATUS)
                   </td>
               </tr>
           </tbody>
        </table>
    </div>
</div>

<div style="margin-top:20px;margin-bottom:30px;text-align:center">
    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
</div>

<div class="panel panel-ZZZ panel-ZZZ-Color2" name="TOP">
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <caption>
                <div class="Caption_Div_Left">
                    轉出學校 @Model.TurnOutSchool
                </div>
                <div class="Caption_Div_Left">
                    轉出酷幣 @Model.TurnOutCash
                </div>
            </caption>
            <thead>
                <tr>
                    <th style="text-align: center">
                        班級
                    </th>
                    <th style="text-align: center">
                        學號
                    </th>
                    <th style="text-align: center">
                        座號
                    </th>
                    <th style="text-align: center">
                        姓名
                    </th>
                    <th style="text-align: center">
                        狀態
                    </th>
                </tr>
            </thead>
         <tbody>
             <tr style="text-align: center">
                 <td>
                     @Html.DisplayFor(modelItem => Model.TurnOut.CLASS_NO)
                 </td>
                 <td>
                     @Html.DisplayFor(modelItem => Model.TurnOut.USER_NO)
                 </td>
                 <td>
                     @Html.DisplayFor(modelItem => Model.TurnOut.SEAT_NO)
                 </td>
                 <td>
                     @Html.DisplayFor(modelItem => Model.TurnOut.NAME)
                 </td>
                 <td>
                     @UserStaus.GetDesc(Model.TurnOut.USER_STATUS)
                 </td>
             </tr>
         </tbody>
        </table>
    </div>
</div>

<div class="text-center">
    <a class="btn btn-default" href='@Url.Action("TurnIn", new { IDNO=Model.TurnIn.IDNO})'>
        確定匯入並啟用帳號
    </a>

    <a href='@Url.Action("Index", "ZZZI20")' role="button" class="btn btn-default">
        放棄編輯
    </a>
</div>

