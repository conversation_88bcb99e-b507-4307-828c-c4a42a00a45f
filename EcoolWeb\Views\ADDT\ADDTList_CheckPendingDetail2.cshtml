﻿@using ECOOL_APP.com.ecool.Models.entity;
@model IEnumerable<uADDT06>

@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    var ADDTList_applyYN = com.ecool.service.PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_apply", user?.SCHOOL_NO, user?.USER_NO);

    string PASSPORT_YN = (Model.FirstOrDefault().PASSPORT_YN == "N") ? "否" : "是";
    bool SHARE_Y = (Model.FirstOrDefault().SHARE_YN == "y") ? true : false;
    bool SHARE_N = (Model.FirstOrDefault().SHARE_YN == "n") ? true : false;
    ViewBag.Title = "閱讀認證-修改認證內容";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@*<div style="width:600px">
    @{string Explain = ViewBag.ADDT06TEXPLAIN;}
    @Html.Raw(HttpUtility.HtmlDecode(@Explain))
</div>*@
<img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
@using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm", enctype = "multipart/form-data" }))
{
    <input type="hidden" id="Mode" name="Mode" value="@Request["Mode"]" />
    <input type="hidden" id="APPLY_NO" name="APPLY_NO" value="@Request["APPLY_NO"]" />
    @Html.Hidden("TempSave")
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.Label("是否為閱讀護照書籍", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @PASSPORT_YN
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("班級座號", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @Model.FirstOrDefault().<EMAIL>().SEAT_NO
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("姓名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @Model.FirstOrDefault().NAME
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("閱讀書名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @Model.FirstOrDefault().BOOK_NAME
                    </samp>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12">
                    詳細內容:限制6000字，目前字數為<span id="ShowFontLen" style="color:red"></span>字,如果沒有上傳圖片，心得必須20個字以上
                </label>
                <div class="col-md-12 col-sm-12 col-lg-12">
                    @Html.TextArea("txtARTICLE", @Model.FirstOrDefault().REVIEW, new { rows = "15", @class = "form-control" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("原上傳圖檔", htmlAttributes: new { @class = "control-label-left label_dt col-md-12 col-sm-12 col-lg-12" })
                <div class="col-md-12 col-sm-12 col-lg-12">
                    @if (string.IsNullOrWhiteSpace(ViewBag.ImageUrl) == false)
                    {
                        if (user != null)
                        {
                            if (user.USER_TYPE == UserType.Student || ADDTList_applyYN=="Y")
                            {
                                @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = ViewBag.ImageUrl, ImgID = "ImgX" })
                            }
                        }

                        <img id="ImgX" src='@ViewBag.ImageUrl' class="img-responsive " alt="Responsive image" />
                    }
                </div>
            </div>

            @if (user != null)
            {
                if (user.USER_TYPE == UserType.Student  || ADDTList_applyYN=="Y")
                {
                    <div class="form-group">
                        @Html.Label("上傳圖檔", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                        <div class="col-md-8 col-sm-8 col-lg-8">
                            @Html.Action("Upload", "Comm")
                        </div>
                    </div>
                    <div class="form-group Div-btn-center">
                            <button class="btn2 btn-default" type="button" name="btnSendTempSave" onclick="btnSend_onclick(true);">
                                暫存草稿
                            </button>
                            <button class="btn2 btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick(false);">確定修改</button>
                            <input type=button class="btn btn-default" name=DisableUpSetDraft value="直接作廢" onclick="DisableGO(this,'@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL')">
                            <a href='@Url.Action("QUERY", "ADDI02")' class="btn btn-default" role="button">
                                放棄編輯
                            </a>
                    </div>
                }
            }
        </div>
    </div>
}

@section Scripts
{
    <script type="text/javascript">
    $(function () {

        $('#txtARTICLE').on("keyup", KeyIn).on("change", KeyIn);
    });

    window.onload = function () {
            KeyIn()
    }

        function KeyIn() {
            var NowTextLen = $("#txtARTICLE").val().length;
           $("#ShowFontLen").text(NowTextLen);
        }
        function DisableGO(BtnThis, Mode) {
            var ErrorMsg = ''
            BtnThis.disabled = true;

            if (ErrorMsg != '') {
                BtnThis.disabled = false;
                alert(ErrorMsg)
                return false;
            }
            else {
                var YN = confirm("你確定要「作廢」這篇文章?")
                if (YN) {
                    $("#Mode").val(Mode);
                    btnSend_onclick();
                }
                else {
                    BtnThis.disabled = false;
                    return false;
                }
            }
        }
        function btnSend_onclick(BoolVal) {
            var strMsg = '';
            try {

                if ($('select[name="ddlReadrppBook"]').val() == 'Y' && $('select[name="BOOK_ID"]').val() == '') {
                    strMsg += '請選擇閱讀書名\r\n';
                }
                if ($('select[name="ddlReadrppBook"]').val() == 'N' && $('#txtBOOK_NAME').val() == '' && $("#txtBOOK_NAME").val().trim().length == 0) {
                    strMsg += '閱讀書名為必填\r\n';
                }

                if ($('input[name="radupdatepic"][value="Y"]').is(':checked') == true) {

                    if ($('#file').val() == '') {
                        strMsg += '上傳檔案需為必填\r\n';
                    }
                }
               
                else {
                    if (($('#ImgX').attr("src") == '' || $('#ImgX').attr("src") == undefined) && ($('#txtARTICLE').val() == '' || $('#txtARTICLE').val().trim() == '' || $('#txtARTICLE').val().trim().length < 20)) {
                        strMsg += '如果沒有上傳圖片，心得必須20個字以上\r\n';
                    }
                }

                if ($('#txtARTICLE').val().length >= 6000) {
                    strMsg += '心得字數超過6000\r\n';
                }

                if (strMsg != '') {
                    alert(strMsg);
                    return;
                }
                else {
                    $('#TempSave').val(BoolVal)
                    document.contentForm.enctype = "multipart/form-data";
                    document.contentForm.action = "ADDTList_CheckPendingDetailEDIT2";
                    document.contentForm.submit();
                }
            }
            catch (err) {
            }

        }
    </script>
}