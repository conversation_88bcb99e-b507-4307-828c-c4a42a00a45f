﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using Dapper;
using System.IO;
using System.Text.RegularExpressions;
using System.Drawing;
using System.Drawing.Imaging;
using NPOI.SS.UserModel;
using MvcPaging;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.CustomAttribute;
using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models;
using Microsoft.Security.Application;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.com.ecool.service;
using System.Text;
using Epub.Net;
using Epub.Net.Models;
using System.Data.Entity.SqlServer;
using ECOOL_APP.com.ecool.LogicCenter;
using EcoolWeb.ViewModels;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI01Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private bool AppMode = string.IsNullOrEmpty(EcoolWeb.Models.UserProfileHelper.GetUUID()) == false;
        private static string Bre_NO = "ADDI01";

        public static string GetSysADDI01IMGPath(string SCHOOL_NO)
        {
            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";

            return string.Format(@"{0}ADDI01IMG\{1}\", UploadImageRoot, SCHOOL_NO);
        }

        /// <summary>
        /// 線上投稿搜尋畫面
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
       [CheckPermission]
        public ActionResult Index(ADDI01IndexViewModel model)
        {
            if (model == null) model = new ADDI01IndexViewModel();
            if (AppMode) model.PageSize = 15;
            model.BackAction = "Index";
          
            this.IndexShared(model);
            UserProfile user = UserProfileHelper.Get();
            string Context = "";
            string SchoolNO = UserProfileHelper.GetSchoolNo();
         
            Context = db.BDMT01.Where(x => x.SCHOOL_NO == SchoolNO).Select(x => x.ADDI01Context).FirstOrDefault();
           
            model.Context = Context;
            return View(model);
        }

        /// <summary>
        /// 老師 批改列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult VerifyList(ADDI01IndexViewModel model)
        {
            if (model == null) model = new ADDI01IndexViewModel();
            UserProfile user = UserProfileHelper.Get();
            model.BackAction = "VerifyList";
            ViewBag.userName = user.NAME;
            this.IndexShared(model);
            return View("Index", model);
        }

        /// <summary>
        /// 線上投稿搜尋畫面 / 老師 批改列表 共用
        /// </summary>
        /// <param name="model"></param>
        public void IndexShared(ADDI01IndexViewModel model)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();
            string UserNO = string.Empty;
            if (user != null) UserNO = user.USER_NO;

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO("ADDI01", SchoolNO, UserNO);
            decimal? Level = PermissionService.GetROLE_LEVEL(user?.USER_KEY, user?.SCHOOL_NO, user?.USER_NO);
            //判斷是否有[我要投稿]權限
            ViewBag.VisibleADD = (PermissionBtn.Where(a => a.ActionName == "Create").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");
            //判斷是否有批閱的權限
            ViewBag.VisibleVerify = (PermissionBtn.Where(a => a.ActionName == "Verify").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");
            //判斷是否有查詢已作廢資料權限
            ViewBag.VisibleSearchDelList = (PermissionBtn.Where(a => a.ActionName == "SearchDelList").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

            //判斷是否批閱後作廢資料權限
            ViewBag.VisiblePASS_DEL = (PermissionBtn.Where(a => a.ActionName == "PASS_DEL").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");
            //判斷是否有各校管理者權限
            ViewBag.ISLEVEL = Level != null ? ((decimal)Level).ToString() : "";
            this.IndexGetData(model, SchoolNO, user);

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
        }

        public void IndexGetData(ADDI01IndexViewModel model, string SchoolNO, UserProfile user)
        {
            IQueryable<ADDV03> ADDV03List = null;

            if (model.BackAction == "Index")
            {
                ADDV03List = from a01 in db.ADDV03
                             join h01 in db.HRMT01 on new { a01.USER_NO, a01.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                             from h01 in h01_join.DefaultIfEmpty()
                             where a01.SCHOOL_NO == SchoolNO
                             select a01;

                if (string.IsNullOrWhiteSpace(model.whereWritingStatus))
                {
                    ADDV03List = ADDV03List.Where(a => a.WRITING_STATUS != 9);
                }
            }
            else if (model.BackAction == "VerifyList")
            {
                ADDV03List = db.ADDV03.Where(a => a.SCHOOL_NO == SchoolNO && (a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Draft || a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.TurnVerify));

                if (user != null && ViewBag.VisibleVerify == "Y")
                {
                    int Hrt25Count = 0;
                    Hrt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && (x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Library) || x.ROLE_ID.Contains(HRMT24_ENUM.Helper_User)|| x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Read))).Count();
                    if (string.IsNullOrEmpty(user.TEACH_CLASS_NO) == true || Hrt25Count >0)
                    {
                    }
                    else
                    {
                        ADDV03List = ADDV03List.Where(a => a.VERIFIER == user.USER_NO || a.CLASS_NO == user.TEACH_CLASS_NO);

                        model.PageSize = int.MaxValue;
                    }
                }
            }

            if (model.doClear)
            {
                model.ClearWhere();
            }

            if (string.IsNullOrWhiteSpace(model.whereWritingStatus) == false)
            {
                var arrWRITING_STATUS = model.whereWritingStatus.Split(',');
                ADDV03List = ADDV03List.Where(a => arrWRITING_STATUS.Contains(a.WRITING_STATUS.ToString()) && a.WRITING_STATUS != null);

                if (user != null && ViewBag.VisibleVerify == "Y")
                {
                    int Hrt25Count = 0;
                    Hrt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO &&(x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Library)||x.ROLE_ID.Contains(HRMT24_ENUM.Helper_User) || x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Read))).Count();
                    if (string.IsNullOrEmpty(user.TEACH_CLASS_NO) == true|| Hrt25Count>0)
                    {
                    }
                    else
                    {
                        ADDV03List = ADDV03List.Where(a => a.VERIFIER == user.USER_NO || a.CLASS_NO == user.TEACH_CLASS_NO);

                        model.PageSize = int.MaxValue;
                    }
                }
            }
            else
            {
                // 草稿不顯示
                ADDV03List = ADDV03List.Where(a => a.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.DraftUnSubmit);
            }

            // 被退回(不優質的)的文章=> 學生或訪客濾掉
            if (user == null
                || user.ROLE_TYPE == (int)UserRoles.學生)
            {
                ADDV03List = ADDV03List.Where(a => a.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Back);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                if (user?.USER_TYPE == UserType.Teacher || user?.USER_TYPE == UserType.Admin)
                {
                    ADDV03List = ADDV03List.Where(a => a.SUBJECT.Contains(model.whereKeyword.Trim()) || a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()));
                }
                else

                {
                    ADDV03List = ADDV03List.Where(a => a.SUBJECT.Contains(model.whereKeyword.Trim()) || a.USER_NO.Contains(model.whereKeyword.Trim()));
                }
            }

            if (model.whereShareYN)
            {
                ADDV03List = ADDV03List.Where(a => a.SHARE_YN == "y");
            }
            if (model.whereComment)
            {
                ADDV03List = ADDV03List.Where(a => a.CommentCount > 0);
            }
            if (model.whereCommentCash)
            {
                ADDV03List = ADDV03List.Where(a => a.CashCommentCount > 0);
            }

            //帶出目前班級「同學」 現在至以現資料
            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO == model.whereCLASS_NO).Select(a => a.USER_NO).ToList();

                ADDV03List = ADDV03List.Where(a => HrList.Contains(a.USER_NO));
                if (string.IsNullOrWhiteSpace(model.whereSeat_NO) == false)
                {
                    ADDV03List = ADDV03List.Where(a => a.SEAT_NO == model.whereSeat_NO);
                }
                model.PageSize = int.MaxValue;
            }
            else if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO.Substring(0, 1) == model.whereGrade).Select(a => a.USER_NO).ToList();
                ADDV03List = ADDV03List.Where(a => HrList.Contains(a.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false || (model?.WhereIsColorboxForUser ?? false) == true)
            {
                var arrUSER_NO = (model.whereUserNo ?? "").Split(',');
                ADDV03List = ADDV03List.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);

                ADDV03List = ADDV03List.OrderByDescending(a => a.WRITING_STATUS).ThenByDescending(a => a.CRE_DATE);
            }
            //else
            //{
                switch (model.OrdercColumn)
                {
                    case "CRE_DATE":
                        ADDV03List = ADDV03List.OrderByDescending(a => a.CRE_DATE);
                        break;

                    case "CLASS_NO":
                        ADDV03List = ADDV03List.OrderByDescending(a => a.CLASS_NO);
                        break;

                    case "SNAME":
                        ADDV03List = ADDV03List.OrderByDescending(a => a.SNAME);
                        break;

                    case "SUBJECT":
                        ADDV03List = ADDV03List.OrderByDescending(a => a.SUBJECT);
                        break;

                    case "READ_COUNT":
                        ADDV03List = ADDV03List.OrderByDescending(a => a.READ_COUNT);
                        break;
                    case "comment":
                        ADDV03List = ADDV03List.OrderByDescending(a => a.CommentCount);
                        break;

                    default:

                        ADDV03List = ADDV03List.OrderByDescending(a => a.CRE_DATE).ThenByDescending(a => a.WRITING_NO);
                        break;
                }
            //}

            model.ADDV03List = ADDV03List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
        }

        public ActionResult MaxDetail()
        {
            return View();
        }
        [HttpGet]
        
        public ActionResult OrderList() {
            ADDI01OrderListViewModel model = new ADDI01OrderListViewModel();

            IQueryable<ADDV01> ADDV01List;
            int pageSize = 20;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                string sSQL = @"SELECT  dbo.HRMT01.SCHOOL_NO,dbo.HRMT01.SYEAR, dbo.HRMT01.USER_NO, dbo.HRMT01.NAME, dbo.HRMT01.SNAME, dbo.HRMT01.SEX,
                            dbo.HRMT01.GRADE, dbo.HRMT01.CLASS_NO, dbo.HRMT01.SEAT_NO, dbo.HRMT01.USER_STATUS,
                            dbo.HRMT01.USER_TYPE, AG.WRITING_QTY, AG.READ_COUNT, AG.SHARE_QTY, AG.CASH_QTY,AG.COMMENT_COUNT
                            FROM  dbo.HRMT01  (nolock)
                            INNER JOIN (
                            SELECT  a.SCHOOL_NO, a.USER_NO, COUNT(a.WRITING_NO) AS WRITING_QTY
							, SUM(a.READ_COUNT) AS READ_COUNT
							, SUM(CASE WHEN (lower(a.SHARE_YN) = 'y') THEN 1 ELSE 0 END) AS SHARE_QTY
                             ,SUM(a.CASH) AS CASH_QTY
							 ,count(DISTINCT b.COMMENT_NO) as COMMENT_COUNT
                            FROM dbo.ADDT01 a(nolock)
							left outer join ADDT02 b (nolock) on a.WRITING_NO=b.WRITING_NO and isnull(b.COMMENT_STATUS,'')<>9
                            WHERE (a.WRITING_STATUS = 1)
                            and CONVERT(nvarchar(6),a.CRE_DATE,112) = CONVERT(nvarchar(6),GETDATE(),112)
                            GROUP BY a.SCHOOL_NO, a.USER_NO) AS AG ON AG.USER_NO = dbo.HRMT01.USER_NO AND AG.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO
                            WHERE (dbo.HRMT01.USER_TYPE = 'S') AND (dbo.HRMT01.USER_STATUS <> 8) AND (dbo.HRMT01.USER_STATUS <> 9)
                            and dbo.HRMT01.SCHOOL_NO=@SCHOOL_NO ";
                ADDV01List = db.Database.Connection.Query<ADDV01>(sSQL, new { SCHOOL_NO = SchoolNO }).AsQueryable();
            }
            else
            {
                string extensionSQL = (model.whereSTART_CRE_DATE.HasValue ? " AND CRE_DATE >= @START_CRE_DATE" : "") +
                    (model.whereEND_CRE_DATE.HasValue ? " AND CRE_DATE <= @END_CRE_DATE" : "");

                string sSQL = $@"SELECT dbo.HRMT01.SCHOOL_NO, dbo.HRMT01.SYEAR, dbo.HRMT01.USER_NO, dbo.HRMT01.NAME, dbo.HRMT01.SNAME,
                            dbo.HRMT01.SEX, dbo.HRMT01.GRADE, dbo.HRMT01.CLASS_NO, dbo.HRMT01.SEAT_NO,
                            dbo.HRMT01.USER_STATUS, dbo.HRMT01.USER_TYPE, AG.WRITING_QTY, AG.READ_COUNT, AG.SHARE_QTY,
                            AG.CASH_QTY, a2.COMMENT_COUNT
                            FROM dbo.HRMT01 INNER JOIN
                                (SELECT SCHOOL_NO, USER_NO, COUNT(WRITING_NO) AS WRITING_QTY, SUM(READ_COUNT)
                                   AS READ_COUNT, SUM(CASE WHEN (lower(SHARE_YN) = 'y') THEN 1 ELSE 0 END) AS SHARE_QTY, SUM(CASH) AS CASH_QTY
                                  FROM dbo.ADDT01
                                  WHERE (WRITING_STATUS = 1 {extensionSQL})
                                  GROUP BY SCHOOL_NO, USER_NO) AS AG ON AG.USER_NO = dbo.HRMT01.USER_NO AND AG.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO LEFT OUTER JOIN
                                (SELECT SCHOOL_NO, USER_NO, COUNT(*) AS COMMENT_COUNT
                                  FROM dbo.ADDT02
                                  WHERE (ISNULL(COMMENT_STATUS, N'') <> 9) AND (USER_NO IS NOT NULL)
                                  GROUP BY SCHOOL_NO, USER_NO) AS a2 ON a2.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO AND
                            a2.USER_NO = dbo.HRMT01.USER_NO
                            WHERE (dbo.HRMT01.USER_TYPE = 'S') AND (dbo.HRMT01.USER_STATUS <> 8) AND (dbo.HRMT01.USER_STATUS <> 9)
                            and dbo.HRMT01.SCHOOL_NO=@SCHOOL_NO";

                ADDV01List = db.Database.Connection.Query<ADDV01>(sSQL, new
                {
                    SCHOOL_NO = SchoolNO,
                    START_CRE_DATE = model.whereSTART_CRE_DATE,
                    END_CRE_DATE = model.whereEND_CRE_DATE
                }).AsQueryable();
            }
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
           
            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
            model.ADDV01List = ADDV01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, pageSize);
            return View(model);
        }
        /// <summary>
        /// 排行
        /// </summary>
        /// <param name="model"></param>
        /// <param name="vOrderList"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult OrderList(ADDI01OrderListViewModel model, FormCollection vOrderList)
        {
            if (!ModelState.IsValid)
            {
                // 回傳表單含錯誤訊息
                return View(model);
            }
            if (model == null) model = new ADDI01OrderListViewModel();

            int pageSize = 20;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            string Grade = string.Empty;
            string CLASS_NO = string.Empty;

            IQueryable<ADDV01> ADDV01List;

            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                string sSQL = @"SELECT  dbo.HRMT01.SCHOOL_NO,dbo.HRMT01.SYEAR, dbo.HRMT01.USER_NO, dbo.HRMT01.NAME, dbo.HRMT01.SNAME, dbo.HRMT01.SEX,
                            dbo.HRMT01.GRADE, dbo.HRMT01.CLASS_NO, dbo.HRMT01.SEAT_NO, dbo.HRMT01.USER_STATUS,
                            dbo.HRMT01.USER_TYPE, AG.WRITING_QTY, AG.READ_COUNT, AG.SHARE_QTY, AG.CASH_QTY,AG.COMMENT_COUNT
                            FROM  dbo.HRMT01  (nolock)
                            INNER JOIN (
                            SELECT  a.SCHOOL_NO, a.USER_NO, COUNT(a.WRITING_NO) AS WRITING_QTY
							, SUM(a.READ_COUNT) AS READ_COUNT
							, SUM(CASE WHEN (lower(a.SHARE_YN) = 'y') THEN 1 ELSE 0 END) AS SHARE_QTY
                             ,SUM(a.CASH) AS CASH_QTY
							 ,count(DISTINCT b.COMMENT_NO) as COMMENT_COUNT
                            FROM dbo.ADDT01 a(nolock)
							left outer join ADDT02 b (nolock) on a.WRITING_NO=b.WRITING_NO and isnull(b.COMMENT_STATUS,'')<>9
                            WHERE (a.WRITING_STATUS = 1)
                            and CONVERT(nvarchar(6),a.CRE_DATE,112) = CONVERT(nvarchar(6),GETDATE(),112)
                            GROUP BY a.SCHOOL_NO, a.USER_NO) AS AG ON AG.USER_NO = dbo.HRMT01.USER_NO AND AG.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO
                            WHERE (dbo.HRMT01.USER_TYPE = 'S') AND (dbo.HRMT01.USER_STATUS <> 8) AND (dbo.HRMT01.USER_STATUS <> 9)
                            and dbo.HRMT01.SCHOOL_NO=@SCHOOL_NO ";
                ADDV01List = db.Database.Connection.Query<ADDV01>(sSQL, new { SCHOOL_NO = SchoolNO }).AsQueryable();
            }
            else
            {
                string extensionSQL = (model.whereSTART_CRE_DATE.HasValue ? " AND CRE_DATE >= @START_CRE_DATE" : "") +
                    (model.whereEND_CRE_DATE.HasValue ? " AND CRE_DATE <= @END_CRE_DATE" : "");

                string sSQL = $@"SELECT dbo.HRMT01.SCHOOL_NO, dbo.HRMT01.SYEAR, dbo.HRMT01.USER_NO, dbo.HRMT01.NAME, dbo.HRMT01.SNAME,
                            dbo.HRMT01.SEX, dbo.HRMT01.GRADE, dbo.HRMT01.CLASS_NO, dbo.HRMT01.SEAT_NO,
                            dbo.HRMT01.USER_STATUS, dbo.HRMT01.USER_TYPE, AG.WRITING_QTY, AG.READ_COUNT, AG.SHARE_QTY,
                            AG.CASH_QTY, a2.COMMENT_COUNT
                            FROM dbo.HRMT01 INNER JOIN
                                (SELECT SCHOOL_NO, USER_NO, COUNT(WRITING_NO) AS WRITING_QTY, SUM(READ_COUNT)
                                   AS READ_COUNT, SUM(CASE WHEN (lower(SHARE_YN) = 'y') THEN 1 ELSE 0 END) AS SHARE_QTY, SUM(CASH) AS CASH_QTY
                                  FROM dbo.ADDT01
                                  WHERE (WRITING_STATUS = 1 {extensionSQL})
                                  GROUP BY SCHOOL_NO, USER_NO) AS AG ON AG.USER_NO = dbo.HRMT01.USER_NO AND AG.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO LEFT OUTER JOIN
                                (SELECT SCHOOL_NO, USER_NO, COUNT(*) AS COMMENT_COUNT
                                  FROM dbo.ADDT02
                                  WHERE (ISNULL(COMMENT_STATUS, N'') <> 9) AND (USER_NO IS NOT NULL)
                                  GROUP BY SCHOOL_NO, USER_NO) AS a2 ON a2.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO AND
                            a2.USER_NO = dbo.HRMT01.USER_NO
                            WHERE (dbo.HRMT01.USER_TYPE = 'S') AND (dbo.HRMT01.USER_STATUS <> 8) AND (dbo.HRMT01.USER_STATUS <> 9)
                            and dbo.HRMT01.SCHOOL_NO=@SCHOOL_NO";

                ADDV01List = db.Database.Connection.Query<ADDV01>(sSQL, new
                {
                    SCHOOL_NO = SchoolNO,
                    START_CRE_DATE = model.whereSTART_CRE_DATE,
                    END_CRE_DATE = model.whereEND_CRE_DATE
                }).AsQueryable();
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.USER_NO == model.whereUserNo);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                pageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "WRITING_QTY":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.WRITING_QTY).ThenBy(a => a.SNAME);
                    break;

                case "SHARE_QTY":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.SHARE_QTY).ThenBy(a => a.SNAME);
                    break;

                case "CASH_QTY":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.CASH_QTY).ThenBy(a => a.SNAME);
                    break;

                case "READ_COUNT":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.READ_COUNT).ThenBy(a => a.SNAME);
                    break;

                case "COMMENT_COUNT":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.COMMENT_COUNT).ThenBy(a => a.SNAME);
                    break;

                default:
                    ADDV01List = ADDV01List.OrderByDescending(a => a.WRITING_QTY).ThenBy(a => a.SNAME);
                    break;
            }

            if (model.IsPrint!=null && model.IsPrint==true)
            {
                pageSize = int.MaxValue;
            }

            if (model.IsPrint!=null && model.IsPrint==true)
            {
                pageSize = ADDV01List.Count();
                model.ADDV01List = ADDV01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, pageSize);
                if (model.ADDV01List.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    model.ADDV01List = ADDV01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            if (model.IsToExcel != null && model.IsToExcel == true)
            {
                pageSize = ADDV01List.Count();
                model.ADDV01List = ADDV01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, pageSize);
                if (model.ADDV01List.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    model.ADDV01List = ADDV01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }
            else
            {
                model.ADDV01List = ADDV01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, pageSize);
            }
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            if (model.isCarousel)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }
        }

        public ActionResult ExportExcel(ADDI01OrderListViewModel model)
        {
            if (model == null) model = new ADDI01OrderListViewModel();

            int pageSize = 20;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            string Grade = string.Empty;
            string CLASS_NO = string.Empty;

            IQueryable<ADDI01004QueryViewModel> ADDV01List;

            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                string sSQL = @"SELECT   dbo.HRMT01.SCHOOL_NO,dbo.HRMT01.SYEAR, dbo.HRMT01.USER_NO, dbo.HRMT01.NAME, dbo.HRMT01.SNAME, dbo.HRMT01.SEX,
                            dbo.HRMT01.GRADE, dbo.HRMT01.CLASS_NO, dbo.HRMT01.SEAT_NO, dbo.HRMT01.USER_STATUS,
                            dbo.HRMT01.USER_TYPE, AG.WRITING_QTY, AG.READ_COUNT, AG.SHARE_QTY, AG.CASH_QTY,AG.COMMENT_COUNT
                            FROM  dbo.HRMT01  (nolock)
                            INNER JOIN (
                            SELECT  a.SCHOOL_NO, a.USER_NO, COUNT(a.WRITING_NO) AS WRITING_QTY
							, SUM(a.READ_COUNT) AS READ_COUNT
							, SUM(CASE WHEN (lower(a.SHARE_YN) = 'y') THEN 1 ELSE 0 END) AS SHARE_QTY
                             ,SUM(a.CASH) AS CASH_QTY
							 ,count(DISTINCT b.COMMENT_NO) as COMMENT_COUNT
                            FROM dbo.ADDT01 a(nolock)
							left outer join ADDT02 b (nolock) on a.WRITING_NO=b.WRITING_NO and isnull(b.COMMENT_STATUS,'')<>9
                            WHERE (a.WRITING_STATUS = 1)
                            and CONVERT(nvarchar(6),a.CRE_DATE,112) = CONVERT(nvarchar(6),GETDATE(),112)
                            GROUP BY a.SCHOOL_NO, a.USER_NO) AS AG ON AG.USER_NO = dbo.HRMT01.USER_NO AND AG.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO
                            WHERE (dbo.HRMT01.USER_TYPE = 'S') AND (dbo.HRMT01.USER_STATUS <> 8) AND (dbo.HRMT01.USER_STATUS <> 9)
                            and dbo.HRMT01.SCHOOL_NO=@SCHOOL_NO ";
                ADDV01List = db.Database.Connection.Query<ADDI01004QueryViewModel>(sSQL, new { SCHOOL_NO = SchoolNO }).AsQueryable();
            }
            else
            {
                string extensionSQL = (model.whereSTART_CRE_DATE.HasValue ? " AND CRE_DATE >= @START_CRE_DATE" : "") +
                    (model.whereEND_CRE_DATE.HasValue ? " AND CRE_DATE <= @END_CRE_DATE" : "");

                string sSQL = $@"SELECT  dbo.HRMT01.SCHOOL_NO, dbo.HRMT01.SYEAR, dbo.HRMT01.USER_NO, dbo.HRMT01.NAME, dbo.HRMT01.SNAME,
                            dbo.HRMT01.SEX, dbo.HRMT01.GRADE, dbo.HRMT01.CLASS_NO, dbo.HRMT01.SEAT_NO,
                            dbo.HRMT01.USER_STATUS, dbo.HRMT01.USER_TYPE, AG.WRITING_QTY, AG.READ_COUNT, AG.SHARE_QTY,
                            AG.CASH_QTY, a2.COMMENT_COUNT
                            FROM dbo.HRMT01 INNER JOIN
                                (SELECT SCHOOL_NO, USER_NO, COUNT(WRITING_NO) AS WRITING_QTY, SUM(READ_COUNT)
                                   AS READ_COUNT, SUM(CASE WHEN (lower(SHARE_YN) = 'y') THEN 1 ELSE 0 END) AS SHARE_QTY, SUM(CASH) AS CASH_QTY
                                  FROM dbo.ADDT01
                                  WHERE (WRITING_STATUS = 1 {extensionSQL})
                                  GROUP BY SCHOOL_NO, USER_NO) AS AG ON AG.USER_NO = dbo.HRMT01.USER_NO AND AG.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO LEFT OUTER JOIN
                                (SELECT SCHOOL_NO, USER_NO, COUNT(*) AS COMMENT_COUNT
                                  FROM dbo.ADDT02
                                  WHERE (ISNULL(COMMENT_STATUS, N'') <> 9) AND (USER_NO IS NOT NULL)
                                  GROUP BY SCHOOL_NO, USER_NO) AS a2 ON a2.SCHOOL_NO = dbo.HRMT01.SCHOOL_NO AND
                            a2.USER_NO = dbo.HRMT01.USER_NO
                            WHERE (dbo.HRMT01.USER_TYPE = 'S') AND (dbo.HRMT01.USER_STATUS <> 8) AND (dbo.HRMT01.USER_STATUS <> 9)
                            and dbo.HRMT01.SCHOOL_NO=@SCHOOL_NO";

                ADDV01List = db.Database.Connection.Query<ADDI01004QueryViewModel>(sSQL, new
                {
                    SCHOOL_NO = SchoolNO,
                    START_CRE_DATE = model.whereSTART_CRE_DATE,
                    END_CRE_DATE = model.whereEND_CRE_DATE
                }).AsQueryable();
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.USER_NO == model.whereUserNo);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                pageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                ADDV01List = ADDV01List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "WRITING_QTY":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.WRITING_QTY).ThenBy(a => a.SNAME);
                    break;

                case "SHARE_QTY":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.SHARE_QTY).ThenBy(a => a.SNAME);
                    break;

                case "CASH_QTY":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.CASH_QTY).ThenBy(a => a.SNAME);
                    break;

                case "READ_COUNT":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.READ_COUNT).ThenBy(a => a.SNAME);
                    break;

                case "COMMENT_COUNT":
                    ADDV01List = ADDV01List.OrderByDescending(a => a.COMMENT_COUNT).ThenBy(a => a.SNAME);
                    break;

                default:
                    ADDV01List = ADDV01List.OrderByDescending(a => a.WRITING_QTY).ThenBy(a => a.SNAME);
                    break;
            }

            pageSize = ADDV01List.Count();

            //model.PageSize = int.MaxValue;
            //model.Page = 1;

            var DataList = ADDV01List.ToList();
            DataTable DataTableExcel = DataList?.AsDataTable();
            DataTableExcel.Columns.Add("RowNum", typeof(int));
            int i = 0;
            foreach (DataRow row in DataTableExcel.Rows)
            { row["RowNum"] = ++i; }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/OrderListExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "線上投稿排行榜", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\線上投稿排行榜_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "線上投稿排行榜.xlsx");//輸出檔案給Client端
        }

        /// <summary>
        /// 明細畫面
        /// </summary>
        /// <param name="WRITING_NO"></param>
        /// <param name="ShowOriginal"></param>
        /// <param name="Search_Data"></param>
        /// <returns></returns>
        // GET: ADDI01/Details/5
        public ActionResult Details(int? WRITING_NO, bool? ShowOriginal, ADDI01IndexViewModel Search_Data)
        {
            UserProfile user = UserProfileHelper.Get();
            ViewBag.WinOpenShareUrlLink = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb//{Bre_NO}/Details";
            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT01 aDDT01 = db.ADDT01.Include("ADDT02").Where(a => a.WRITING_NO == WRITING_NO).FirstOrDefault();
            if (aDDT01 == null)
            {
                return HttpNotFound();
            }
            if (user == null)
            {
                ViewBag.SharetoNew = "N";
            }
            else
            {
                if (aDDT01.USER_NO == user.USER_NO || "A" + aDDT01.USER_NO == user.USER_NO || user.USER_TYPE != "S")
                {
                    ViewBag.SharetoNew = "Y";
                }
            }

            this.DetailsShared(aDDT01, ShowOriginal, Search_Data);
            ViewBag.contextLenght = aDDT01.ARTICLE.Length;
            return View(aDDT01);
        }

        public ActionResult FriendsData(int? WRITING_NO, bool? ShowOriginal, ADDI01IndexViewModel Search_Data)
        {
            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT01 aDDT01 = db.ADDT01.Include("ADDT02").Where(a => a.WRITING_NO == WRITING_NO).FirstOrDefault();
            if (aDDT01 == null)
            {
                return HttpNotFound();
            }

            this.DetailsShared(aDDT01, ShowOriginal, Search_Data);

            UserProfile user = UserProfileHelper.Get();

            string StatusMessage = string.Empty;
            HRMT07.AddFriendsData(user.SCHOOL_NO, user.USER_NO, aDDT01.USER_NO, out StatusMessage);
            TempData["StatusMessage"] = StatusMessage;

            return View("Details", aDDT01);
        }

        /// <summary>
        /// 明細畫面
        /// </summary>
        /// <param name="WRITING_NO"></param>
        /// <returns></returns>
        public ActionResult BOOK_Details(int? WRITING_NO, bool? ShowOriginal, ADDI01IndexViewModel Search_Data)
        {
            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT01 aDDT01 = db.ADDT01.Include("ADDT02").Where(a => a.WRITING_NO == WRITING_NO).FirstOrDefault();
            if (aDDT01 == null)
            {
                return HttpNotFound();
            }

            this.DetailsShared(aDDT01, ShowOriginal, Search_Data);

            return View(aDDT01);
        }

        public void DetailsShared(ADDT01 aDDT01, bool? ShowOriginal, ADDI01IndexViewModel Search_Data)
        {
            this.SearchData(Search_Data);

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT01);
            SetImageOrders(aDDT01);
            ViewBag.OtherFilesUrl = GetOtherfilesUrl(aDDT01);
            SetOtherFilesOrders(aDDT01);
            //組聲音檔路徑
            ViewBag.VoiceUrl = GetVoiceUrl(aDDT01);

            this.DetailsData(aDDT01);

            //預設顯示老師批閱的文章
            if (string.IsNullOrWhiteSpace(aDDT01.ARTICLE_VERIFY) == false)
                ViewBag.ShowOriginalArticle = (ShowOriginal == true) ? "O" : "V";

            ViewBag.CanLikes = GetCanLikes(aDDT01);

            //..........................................................刪除留言權限
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();
            string UserNO = string.Empty;
            if (user != null) UserNO = user.USER_NO;
            var PermissionBtn = PermissionService.GetActionPermissionForBreNO("ADDI01", SchoolNO, UserNO);

            //判斷是否有[我要投稿]權限
            ViewBag.VisibleDisableComment = (PermissionBtn.Where(a => a.ActionName == "DisableComment").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

            // 表情圖片s
            Dictionary<string, string> emoticons_Dictionary = new Dictionary<string, string>();
            string directorypath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Content\\img\\emoticons");
            string[] emoticonFiles = Directory.GetFiles(directorypath, "*.*")
                                     .Select(Path.GetFileName)
                                     .ToArray();
            foreach (string icon in emoticonFiles)
            {
                emoticons_Dictionary.Add(icon, Url.Content("~/Content/img/emoticons/" + icon));
            }
            ViewBag.EmoticonDict = emoticons_Dictionary;

            if (aDDT01.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Disable && string.IsNullOrWhiteSpace(aDDT01.BACK_MEMO) == false)
            {
                TempData["StatusMessage"] = aDDT01.BACK_MEMO;
            }
        }

        public void DetailsData(ADDT01 aDDT01)
        {
            //點閱數+1
            if (aDDT01.READ_COUNT.HasValue == false) aDDT01.READ_COUNT = 1;
            else aDDT01.READ_COUNT = aDDT01.READ_COUNT + 1;
            db.SaveChanges();

            if (aDDT01.ARTICLE_VERIFY != null) aDDT01.ARTICLE_VERIFY = aDDT01.ARTICLE_VERIFY.Replace("\r\n", "<br/><br/>");
            if (aDDT01.ARTICLE != null) aDDT01.ARTICLE = aDDT01.ARTICLE.Replace("\r\n", "<br/><br/>");
        }

        [ValidateInput(false)]
        public ActionResult SaveEpub(string School_No, string User_No, string S_DATE, string E_DATE, string IDNO, string HTMLStr)
        {
            UserProfile user = UserProfileHelper.Get();

            string[] ArrUSER_NO;

            if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
            {
                ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
            }
            else
            {
                ArrUSER_NO = new[] { User_No };

                if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                {
                    return RedirectToAction("NotSeeDataError", "Error");
                }
            }

            HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && ArrUSER_NO.Contains(a.USER_NO)).FirstOrDefault();

            IQueryable<ADDT01> Temp;

            Temp = db.ADDT01.Where(a => a.SCHOOL_NO == School_No && a.WRITING_STATUS != 9);

            if (string.IsNullOrWhiteSpace(User_No) == false)
            {
                Temp = Temp.Where(a => a.USER_NO == User_No);
            }
            else
            {
                User_No = ArrUSER_NO.FirstOrDefault();
            }

            if (ArrUSER_NO.Length > 0)
            {
                Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(S_DATE) == false)
            {
                DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
            }

            if (string.IsNullOrWhiteSpace(E_DATE) == false)
            {
                DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
            }

            EBook Book = new EBook();

            if (string.IsNullOrWhiteSpace(HTMLStr) == false)
            {
                HTMLStr.Replace("\r\n", "<br/>");

                Chapter This = new Chapter();
                This.Name = "總表";
                This.Content = HTMLStr;
                Book.Chapters.Add(This);
            }

            foreach (var item in Temp.ToList())
            {
                Chapter This = new Chapter();
                This.Name = item.SUBJECT;

                string ARTICLE = string.IsNullOrWhiteSpace(item.ARTICLE_VERIFY) ? item.ARTICLE : item.ARTICLE_VERIFY;
                List<string> ArrImage = GetImagePath(item, false);

                string HtmlStr = ARTICLE.Replace("\r\n", "<br/>");

                if (ArrImage != null && !ArrImage.Any())
                {
                    foreach (var Image in ArrImage)
                    {
                        HtmlStr = HtmlStr + String.Format("<img src = '{0}' />", Image);
                    }
                }

                This.Content = HtmlStr;
                Book.Chapters.Add(This);
            }

            Book.Title = H01.NAME + "線上設稿";
            Book.Language = new Language("zh-TW");
            Book.Publisher = "ecc.hhups.tp.edu.tw";
            Book.Creator = H01.NAME;

            Book.CssLinkS.Add(Server.MapPath("~/Content/css/jquery-ui.min.css"));
            Book.CssLinkS.Add(Server.MapPath("~/Content/css/jquery.validationEngine.css"));
            Book.CssLinkS.Add(Server.MapPath("~/Content/css/bootstrap.css"));
            Book.CssLinkS.Add(Server.MapPath("~/Content/css/Site.css"));
            Book.CssLinkS.Add(Server.MapPath("~/Content/css/EzCss.css"));

            string filepath = Server.MapPath($@"~\Content\Epub\{H01.SCHOOL_NO}\{H01.USER_NO}\");
            if (Directory.Exists(filepath) == false)
            {
                Directory.CreateDirectory(filepath);
            }

            string destinationFile = filepath + Book.Title + ".epub";

            Book.GenerateEpub(destinationFile);

            //取得檔案名稱
            string filename = System.IO.Path.GetFileName(destinationFile);

            //讀成串流
            Stream iStream = new FileStream(destinationFile, FileMode.Open, FileAccess.Read, FileShare.Read);

            //回傳出檔案
            return File(iStream, "application/unknown", filename);
        }

        /// <summary>
        /// 【線上投稿成果匯出】
        /// </summary>
        /// <param name="School_No"></param>
        /// <param name="ShowOriginal"></param>
        /// <returns></returns>
        public ActionResult Details2(string School_No, string User_No, string S_DATE, string E_DATE, bool? ShowOriginal, string IDNO)
        {
            UserProfile user = UserProfileHelper.Get();
            ViewBag.IDNO = IDNO;
            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            string[] ArrUSER_NO;

            if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
            {
                ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
            }
            else
            {
                ArrUSER_NO = new[] { User_No };

                if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                {
                    return RedirectToAction("NotSeeDataError", "Error");
                }
            }

            IQueryable<ADDT01> Temp;

            Temp = db.ADDT01.Include("ADDT02").Where(a => a.SCHOOL_NO == School_No && a.WRITING_STATUS != 9 && a.WRITING_STATUS != 8);

            if (string.IsNullOrWhiteSpace(User_No) == false)
            {
                Temp = Temp.Where(a => a.USER_NO == User_No);
            }
            else
            {
                User_No = ArrUSER_NO.FirstOrDefault();
            }

            if (ArrUSER_NO.Length > 0)
            {
                Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(S_DATE) == false)
            {
                DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
            }

            if (string.IsNullOrWhiteSpace(E_DATE) == false)
            {
                DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
            }

            List<ADDT01> ltaDDT01 = Temp.ToList();
            if (ltaDDT01 == null)
            {
                return HttpNotFound();
            }

            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageDictionaryUrl(ltaDDT01);

            //取出學校名稱
            ViewBag.SchoolName = db.BDMT01.Where(p => p.SCHOOL_NO == School_No).FirstOrDefault().SHORT_NAME;

            //取出學生名稱 及 學習期間

            //取出學生名稱 及 學習期間
            ViewBag.UserName = db.HRMT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Select(p => p.NAME).FirstOrDefault();

            if (ltaDDT01.Count() > 0)
            {
                //學習期間
                ViewBag.SYear = ltaDDT01.Min(a => a.SYEAR).Value.ToString() + "學年度 ~ " + ltaDDT01.Max(a => a.SYEAR).Value.ToString() + "學年度";
            }

            //取出酷幣點數
            AWAT01 UserCash = db.AWAT01.Where(a => a.SCHOOL_NO == School_No && a.USER_NO == User_No).FirstOrDefault();
            if (UserCash != null)
            {
                ViewBag.CoolCash = UserCash.CASH_AVAILABLE.Value;
            }
            //線上投稿總篇數
            ViewBag.SumAcount = db.ADDT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Count();

            //閱讀認證等級
            var TempLEVEL = (from a09 in db.ADDT09
                             join a08 in db.ADDT08
                                   on new { a09.SCHOOL_NO, LEVEL_ID = (byte)a09.LEVEL_ID }
                               equals new { a08.SCHOOL_NO, LEVEL_ID = a08.LEVEL_ID }
                             where a09.USER_NO == User_No && a09.SCHOOL_NO == School_No
                             select new ADDT0809
                             {
                                 LEVEL_DESC = a08.LEVEL_DESC
                             }).FirstOrDefault();

            if (TempLEVEL != null)
            {
                ViewBag.PassPort = TempLEVEL.LEVEL_DESC;
            }
            else
            {
                ViewBag.PassPort = "";
            }

            //預設顯示老師批閱的文章
            bool ChangeARTICLE_VERIFY = true;
            if (ShowOriginal.HasValue) if (ShowOriginal.Value) ChangeARTICLE_VERIFY = false;
            if (ChangeARTICLE_VERIFY)
            {
                foreach (var aDDT01 in ltaDDT01)
                {
                    if (string.IsNullOrWhiteSpace(aDDT01.ARTICLE_VERIFY) == false)
                    {
                        if (aDDT01.ARTICLE_VERIFY != null) aDDT01.ARTICLE_VERIFY = aDDT01.ARTICLE_VERIFY.Replace("\r\n", "");
                        //db.Entry(aDDT01).State = EntityState.Detached; //會造成ADDT02不見
                        aDDT01.ARTICLE = aDDT01.ARTICLE_VERIFY;
                    }
                }
            }

            return PartialView(ltaDDT01);
        }

        /// <summary>
        /// 【線上投稿成果匯出】
        /// </summary>
        /// <param name="School_No"></param>
        /// <param name="ShowOriginal"></param>
        /// <returns></returns>
        public ActionResult Details3(string School_No, string User_No, string S_DATE, string E_DATE, bool? ShowOriginal, string IDNO, string LogimUser)
        {
            UserProfile user=new UserProfile();
            if (LogimUser != "Y")
            {
              user = UserProfileHelper.Get();
            }
            ViewBag.IDNO = IDNO;
            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;
           
            string[] ArrUSER_NO;

            if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
            {
                ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
            }
            else
            {
                ArrUSER_NO = new[] { User_No };
                if (LogimUser != "Y") { 
                string IDNOSTR = "";
                IDNOSTR = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
                if (IDNO == IDNOSTR)
                {

                    if (UserProfileHelper.CheckSeeStudentIDNOData(user, School_No, User_No,IDNO, ref db) == false)
                    {
                        return RedirectToAction("NotSeeDataError", "Error");
                    }

                }
                else {
                    if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                    {
                        return RedirectToAction("NotSeeDataError", "Error");
                    }

                }
                }
            }

            IQueryable<ADDT01> Temp;

            Temp = db.ADDT01.Include("ADDT02").Where(a => a.SCHOOL_NO == School_No && a.WRITING_STATUS != 9 && a.WRITING_STATUS != 8);

            if (string.IsNullOrWhiteSpace(User_No) == false)
            {
                Temp = Temp.Where(a => a.USER_NO == User_No);
            }
            else
            {
                User_No = ArrUSER_NO.FirstOrDefault();
            }

            if (ArrUSER_NO.Length > 0)
            {
                Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(S_DATE) == false)
            {
                DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
            }

            if (string.IsNullOrWhiteSpace(E_DATE) == false)
            {
                DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
            }

            List<ADDT01> ltaDDT01 = Temp.ToList();
            if (ltaDDT01 == null)
            {
                return HttpNotFound();
            }

            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageDictionaryUrl(ltaDDT01);

            //取出學校名稱
            ViewBag.SchoolName = db.BDMT01.Where(p => p.SCHOOL_NO == School_No).FirstOrDefault().SHORT_NAME;

            //取出學生名稱 及 學習期間

            //取出學生名稱 及 學習期間
            ViewBag.UserName = db.HRMT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Select(p => p.NAME).FirstOrDefault();

            if (ltaDDT01.Count() > 0)
            {
                //學習期間
                ViewBag.SYear = ltaDDT01.Min(a => a.SYEAR).Value.ToString() + "學年度 ~ " + ltaDDT01.Max(a => a.SYEAR).Value.ToString() + "學年度";
            }

            //取出酷幣點數
            AWAT01 UserCash = db.AWAT01.Where(a => a.SCHOOL_NO == School_No && a.USER_NO == User_No).FirstOrDefault();
            if (UserCash != null)
            {
                ViewBag.CoolCash = UserCash.CASH_AVAILABLE.Value;
            }
            //線上投稿總篇數
            ViewBag.SumAcount = db.ADDT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Count();

            //閱讀認證等級
            var TempLEVEL = (from a09 in db.ADDT09
                             join a08 in db.ADDT08
                                   on new { a09.SCHOOL_NO, LEVEL_ID = (byte)a09.LEVEL_ID }
                               equals new { a08.SCHOOL_NO, LEVEL_ID = a08.LEVEL_ID }
                             where a09.USER_NO == User_No && a09.SCHOOL_NO == School_No
                             select new ADDT0809
                             {
                                 LEVEL_DESC = a08.LEVEL_DESC
                             }).FirstOrDefault();

            if (TempLEVEL != null)
            {
                ViewBag.PassPort = TempLEVEL.LEVEL_DESC;
            }
            else
            {
                ViewBag.PassPort = "";
            }

            //預設顯示老師批閱的文章
            bool ChangeARTICLE_VERIFY = true;
            if (ShowOriginal.HasValue) if (ShowOriginal.Value) ChangeARTICLE_VERIFY = false;
            if (ChangeARTICLE_VERIFY)
            {
                foreach (var aDDT01 in ltaDDT01)
                {
                    if (string.IsNullOrWhiteSpace(aDDT01.ARTICLE_VERIFY) == false)
                    {
                        if (aDDT01.ARTICLE_VERIFY != null) aDDT01.ARTICLE_VERIFY = aDDT01.ARTICLE_VERIFY.Replace("\r\n", "");
                        //db.Entry(aDDT01).State = EntityState.Detached; //會造成ADDT02不見
                        aDDT01.ARTICLE = aDDT01.ARTICLE_VERIFY;
                    }
                }
            }

            return PartialView(ltaDDT01);
        }

        /// <summary>
        /// 有幫助
        /// </summary>
        /// <param name="COMMENT_NO"></param>
        /// <returns></returns>
        public ActionResult LikeComment(int? COMMENT_NO, ADDI01IndexViewModel Search)
        {
            if (COMMENT_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            UserProfile user = UserProfileHelper.Get();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            ADDT02 comment = db.ADDT02.Where(a => a.COMMENT_NO == COMMENT_NO).FirstOrDefault();
            if (comment == null)
            {
                return HttpNotFound();
            }
            ADDT01 a1 = db.ADDT01.Find(comment.WRITING_NO);
            List<int> Likes = GetCanLikes(a1);
            if (Likes.Contains(comment.COMMENT_NO))
            {
                string BATCH_ID = PushService.CreBATCH_ID();

                comment.CASH = 1;
                ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(comment.CASH), comment.SCHOOL_NO, comment.USER_NO, "ADDT02", comment.COMMENT_NO.ToString(), "線上投稿有幫助的留言", true, ref db, "", "",ref valuesList);

                string BODY_TXT = "線上投稿有幫助的留言，標題: " + a1.SUBJECT + "、因此獲得酷幣點數" + (comment.CASH).ToString() + "點";

                PushService.InsertPushDataParents(BATCH_ID, comment.SCHOOL_NO, comment.USER_NO, "", BODY_TXT, "", "ADDI01", "LikeComment", a1.WRITING_NO.ToString(), null, false, ref db);
                PushService.InsertPushDataMe(BATCH_ID, comment.SCHOOL_NO, comment.USER_NO, "", BODY_TXT, "", "ADDI01", "LikeComment", a1.WRITING_NO.ToString(), null, false, ref db);

                db.SaveChanges();

                //Push
                PushHelper.ToPushServer(BATCH_ID);
            }

            return RedirectToAction("Details", new
            {
                WRITING_NO = comment.WRITING_NO,
                BackAction = Search.BackAction,
                OrdercColumn = Search.OrdercColumn,
                whereKeyword = Search.whereKeyword,
                whereUserNo = Search.whereUserNo,
                whereWritingStatus = Search.whereWritingStatus,
                whereShareYN = Search.whereShareYN,
                whereComment = Search.whereComment,
                whereCommentCash = Search.whereCommentCash,
                whereCLASS_NO = Search.whereCLASS_NO,
                whereGrade = Search.whereGrade,
                Page = Search.Page
            });
        }

        /// <summary>
        /// 刪留言
        /// </summary>
        /// <param name="COMMENT_NO"></param>
        /// <returns></returns>
        public ActionResult DisableComment(int? COMMENT_NO, ADDI01IndexViewModel Search)
        {
            if (COMMENT_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            ADDT02 comment = db.ADDT02.Where(a => a.COMMENT_NO == COMMENT_NO).FirstOrDefault();
            if (comment == null)
            {
                return HttpNotFound();
            }
            comment.COMMENT_STATUS = 9;

            if (comment.CASH == 1)
            {
                UserProfile user = UserProfileHelper.Get();
                ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(comment.CASH) * -1, comment.SCHOOL_NO, comment.USER_NO, "ADDI01", comment.WRITING_NO.ToString(), "線上投稿有幫助的留言被作廢", true, ref db, "", "",ref valuesList);
                //ADDT01 a1 = db.ADDT01.Find(comment.WRITING_NO);
                //string BODY_TXT = "被作廢線上投稿有幫助的留言，標題： " + a1.SUBJECT + "、因此扣回酷幣點數" + (comment.CASH).ToString() + "點";
            }

            db.SaveChanges();

            TempData["StatusMessage"] = "已刪除";

            return RedirectToAction("Details", new
            {
                WRITING_NO = comment.WRITING_NO,
                BackAction = Search.BackAction,
                OrdercColumn = Search.OrdercColumn,
                whereKeyword = Search.whereKeyword,
                whereUserNo = Search.whereUserNo,
                whereWritingStatus = Search.whereWritingStatus,
                whereShareYN = Search.whereShareYN,
                whereComment = Search.whereComment,
                whereCommentCash = Search.whereCommentCash,
                whereCLASS_NO = Search.whereCLASS_NO,
                whereGrade = Search.whereGrade,
                Page = Search.Page
            });
        }

        /// <summary>
        /// 新增
        /// </summary>
        /// <returns></returns>
        // GET: ADDI01/Create
        [CheckPermission] //檢查權限
        public ActionResult Create()
        {
            ADDT01 aDDT01 = new ADDT01();

            //取得線上投稿學生投稿說明檔
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            ViewBag.ADDI01SEXPLAIN = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNO).FirstOrDefault().ADDI01SEXPLAIN;
            aDDT01.CRE_DATE = DateTime.Now;
            aDDT01.AutherYN = false;
            ViewBag.RemindItems = BDMT02Service.GetRefDataList("ADDI01", "REMIND", "ALL", SchoolNO, ref db);

            return View(aDDT01);
        }

        public string GetCheckSubject(string Subject)
        {
            UserProfile user = UserProfileHelper.Get();

            if (db.ADDT01.Where(a => a.SUBJECT == Subject.Trim() && a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Any())
            {
                return string.Format("注意您的標題重複。");
            }

            return string.Empty;
        }

        /// <summary>
        /// 新增畫面
        /// </summary>
        /// <param name="aDDT01"></param>
        /// <param name="files"></param>
        /// <param name="TempVoicefile"></param>
        /// <returns></returns>
        // POST: ADDI01/Create
        // 若要免於過量張貼攻擊，請啟用想要繫結的特定屬性，如需
        // 詳細資訊，請參閱 http://go.microsoft.com/fwlink/?LinkId=317598。
        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind(Include = "SUBJECT,ARTICLE,IMG_FILE,VOICE_FILE,Upload_FILE,CRE_DATE,PUBLISH_CHRIS_YN,YOUTUBE_URL,AutherYN")] ADDT01 aDDT01, List<HttpPostedFileBase> files, List<HttpPostedFileBase> files2, string TempVoicefile = "", bool tempSave = false)
        {
            // ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            string Message = string.Empty;
            if (ModelState.IsValid == false)
                return View(aDDT01);

            if (files != null)
            {
                if (files.Count() > 6)
                {
                    TempData["StatusMessage"] = "您上傳的圖片不可超過六張";
                    return View(aDDT01);
                }
            }

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            aDDT01.SEMESTER = Convert.ToByte(Semesters);
            aDDT01.SYEAR = Convert.ToByte(SYear);
            AWAT07 aWAT07Item = new AWAT07();

            UserProfile user = UserProfileHelper.Get();
            aWAT07Item = db.AWAT07.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO && a.DEFAULT_YN == true).FirstOrDefault();
            aDDT01.CLASS_NO = user.CLASS_NO;
            aDDT01.NAME = user.NAME;
            aDDT01.SCHOOL_NO = user.SCHOOL_NO;
            aDDT01.SEAT_NO = user.SEAT_NO;
            aDDT01.SNAME = user.SNAME;
            aDDT01.USER_NO = user.USER_NO;
            aDDT01.CRE_DATE = DateTime.Now;
            if (aWAT07Item == null)
            {
                aDDT01.PLAYER_NO = 1;
            }
            else {
                aDDT01.PLAYER_NO = aWAT07Item.PLAYER_NO;
            }
         


            aDDT01.WRITING_STATUS = tempSave ? (byte)ADDStatus.eADDT01Status.DraftUnSubmit : (byte)ADDStatus.eADDT01Status.Draft;

            //過濾XSS
            //aDDT01.ARTICLE = Sanitizer.GetSafeHtmlFragment(aDDT01.ARTICLE);

            //找老師
            HRMT03 TeachInfo =
                db.HRMT03.Where(a => a.SCHOOL_NO == aDDT01.SCHOOL_NO && a.CLASS_NO == aDDT01.CLASS_NO).FirstOrDefault();
            if (TeachInfo != null)
            {
                aDDT01.VERIFIER = TeachInfo.TEACHER_NO;
            }
            bool checkFile = false;

            //if (files2 != null)
            //{
            checkFile = CheckFileUpload(files2, ref Message);

            //if (!checkFile)
            //{
            //    TempData["StatusMessage"] = Message;
            //    return View(aDDT01);
            //}
            //}
            //  checkFile = CheckFileUpload(files2, ref Message);
            //同一天同使用者同文章標題不能重複
            ADDT01 oldA1 = db.ADDT01.Where(a => a.USER_NO == aDDT01.USER_NO && a.SUBJECT == aDDT01.SUBJECT && (a.CRE_DATE >= DateTime.Today || a.CRE_DATE == null)).FirstOrDefault();

            if (oldA1 != null)
            {
                TempData["StatusMessage"] = "您這個主題已經投稿過了喔！";
                return View(aDDT01);
            }
            if (!checkFile)
            {
                TempData["StatusMessage"] = Message;
                return View(aDDT01);
            }

            //儲存資料
            db.ADDT01.Add(aDDT01);

            try
            {
                db.SaveChanges();
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException ex)
            {
                LogHelper.AddLogToDB(user.SCHOOL_NO, user.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "ADDI10", "ex.Message"+ ex.Message.ToString());
                //LogHelper.LogToTxt("ex.Message" + ex.Message);
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
            }
            ADDT01 oldADDT01 = db.ADDT01.Find(aDDT01.WRITING_NO);

            //處理上傳檔案
            bool ans = doImage(aDDT01, files);

            if (ans) db.SaveChanges();

            if (checkFile)
            {
                //LogHelper.LogToTxt("checkFile" + checkFile);
                bool ans2 = doFilesUpload(aDDT01, files2);
                if (ans2) db.SaveChanges();
            }

            //處理錄音檔搬移
            ans = doVoice(aDDT01, TempVoicefile);
            if (ans) db.SaveChanges();

            // 草稿到這裡停止
            if (tempSave)
            {
                TempData["StatusMessage"] = "暫存成功，期待你下次的編輯";
                return RedirectToAction("Index", new { whereWritingStatus = aDDT01.WRITING_STATUS, whereUserNo = user.USER_NO });
            }
            if (string.IsNullOrEmpty(aDDT01.SHARE_YN)) {


                aDDT01.SHARE_YN = "n";
            }
            string str = "../ADDI01/Edit?WRITING_NO=" + aDDT01.WRITING_NO + "&BackAction=Index &whereShareYN=" + aDDT01.SHARE_YN + "&whereComment=False&whereCommentCash=False&Page=0";
            TempData["StatusMessage"] = "※投稿成功，請耐心等待評分，老師審核前，可自行修改內容。</br>※若有上傳圖片照片，請注意版權，並可以用「<a href=\"" + str + "\" style=\"color: blue\" >修改  </a>」功能調整圖片的方向。</br>※文章必須自行創作，切勿抄襲。";
            bool? SendMailPostToTecher = false;
            SendMailPostToTecher = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.SendMailPostToTecher).FirstOrDefault();
            //發mail 給班導
            if (SendMailPostToTecher==null || !(bool)SendMailPostToTecher) {
                MailToTeacher(aDDT01, user);
            }
           

            //產是要 Push 批號

            string BATCH_ID = PushService.CreBATCH_ID();

            string BODY_TXT = "線上投稿，有篇新文章，文章標題:" + aDDT01.SUBJECT;

            PushService.InsertPushDataParents(BATCH_ID, aDDT01.SCHOOL_NO, aDDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "Create", aDDT01.WRITING_NO.ToString(), "ADDI01/Details?WRITING_NO=" + aDDT01.WRITING_NO.ToString(), false, ref db);

            PushService.InsertPushDatafriend(BATCH_ID, aDDT01.SCHOOL_NO, aDDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "Create", aDDT01.WRITING_NO.ToString(), "ADDI01/Details?WRITING_NO=" + aDDT01.WRITING_NO.ToString(), false, ref db);

            PushService.InsertPushDataTEACHER(BATCH_ID, aDDT01.SCHOOL_NO, aDDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "Create", aDDT01.WRITING_NO.ToString(), "ADDI01/VerifyList", false, ref db);
            db.SaveChanges();

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            return RedirectToAction("Details", new { WRITING_NO = aDDT01.WRITING_NO });
        }

        /// <summary>
        /// 直接作廢
        /// </summary>
        /// <param name="WRITING_NO"></param>
        /// <param name="Search_Data"></param>
        /// <returns></returns>
        public ActionResult Disable(ADDT01 aDDT01, ADDI01IndexViewModel Search_Data)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            ADDT01 oldADDT01 = db.ADDT01.Find(aDDT01.WRITING_NO);
            if (oldADDT01 == null)
            {
                return HttpNotFound();
            }

            if (user?.USER_NO != oldADDT01.USER_NO)
            {
                int VerfierCount = 0;
                VerfierCount = db.ADDT01.Where(x => x.WRITING_NO == aDDT01.WRITING_NO && x.VERIFIER == user.USER_NO).Count();
                //權限
                if (PermissionService.GetActionPermissionMyClassNO(oldADDT01.CLASS_NO, SchoolNO, "ADDI01", "Disable", user, oldADDT01.USER_NO) == false && VerfierCount==0)
                {
                    return RedirectToAction("PermissionError", "Error");
                }
            }

            //更新屬性
            oldADDT01.WRITING_STATUS = (byte)ADDStatus.eADDT01Status.Disable;
            if (user?.USER_NO == oldADDT01.USER_NO)
            {
                oldADDT01.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(作廢 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + "作者直接作廢";
            }
            else
            {
                oldADDT01.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(作廢 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + aDDT01.BACK_MEMO;
            }

            oldADDT01.DEL_DATE = DateTime.Now;
            oldADDT01.DEL_PERSON = user.USER_KEY;

            db.SaveChanges();
            //更新顯示

            return RedirectToAction(Search_Data.BackAction, Search_Data);
        }

        /// <summary>
        /// 退回再修
        /// </summary>
        /// <param name="WRITING_NO"></param>
        /// <param name="Search_Data"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        [ValidateInput(false)]
        public ActionResult DisableUpSetDraft(ADDT01 aDDT01, ADDI01IndexViewModel Search_Data)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            ADDT01 oldADDT01 = db.ADDT01.Find(aDDT01.WRITING_NO);
            if (oldADDT01 == null)
            {
                return HttpNotFound();
            }
            ADDT01 aDDT02 = new ADDT01();
            aDDT02 = db.ADDT01.Where(x => x.WRITING_NO == aDDT01.WRITING_NO).FirstOrDefault();
            int VerfierCount = 0;
            VerfierCount = db.ADDT01.Where(x => x.WRITING_NO == aDDT01.WRITING_NO && x.VERIFIER == user.USER_NO).Count();
            //權限
            if (!(aDDT02.USER_NO== user.USER_NO&& aDDT02.SCHOOL_NO== user.SCHOOL_NO) && PermissionService.GetActionPermissionMyClassNO(oldADDT01.CLASS_NO, SchoolNO, "ADDI01", "DisableUpSetDraft", user, oldADDT01.USER_NO) == false && VerfierCount==0)
            {
                return RedirectToAction("PermissionError", "Error");
            }

            //更新屬性
            oldADDT01.WRITING_STATUS = (byte)ADDStatus.eADDT01Status.Back;
            if (aDDT02.USER_NO != user.USER_NO) { 
            oldADDT01.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(退回" + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + aDDT01.BACK_MEMO;
            }
            //老師有修改文章
            if (oldADDT01.ARTICLE.Replace("\r\n", "<br/>") != aDDT01.ARTICLE)
            {
                //過濾XSS
                // oldADDT01.ARTICLE_VERIFY = Sanitizer.GetSafeHtmlFragment(aDDT01.ARTICLE);
                oldADDT01.ARTICLE_VERIFY = HtmlUtility.SanitizeHtml(aDDT01.ARTICLE);
               
                //oldADDT01.ARTICLE_VERIFY = aDDT01.ARTICLE;
            }

            CreHis("退回再修", user, oldADDT01);

            db.SaveChanges();
            //更新顯示

            return RedirectToAction(Search_Data.BackAction, Search_Data);
        }

        /// <summary>
        /// 批改畫面
        /// </summary>
        /// <param name="WRITING_NO"></param>
        /// <param name="Search_Data"></param>3k
        /// <returns></returns>
        public ActionResult Verify(int? WRITING_NO, ADDI01IndexViewModel Search_Data)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            this.SearchData(Search_Data);

            ADDT01 aDDT01 = db.ADDT01.Find(WRITING_NO);

            if (aDDT01 == null)
            {
                return HttpNotFound();
            }
            //評語選單
            ViewBag.VCommentSelectItem = BDMT02Service.GetRefSelectListItem("ADD01", "VERIFY_COMMENT", "ALL", SchoolNO, aDDT01.VERIFY_COMMENT, true, null, true, null, ref db);

            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDI01", "BACK_REASON", "ALL", SchoolNO, aDDT01.BACK_MEMO, true, null, true, null, ref db);

            if (aDDT01.ARTICLE != null) aDDT01.ARTICLE = aDDT01.ARTICLE.Replace("\r\n", "<br/>");

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT01);
            SetImageOrders(aDDT01);
            //組圖檔路徑
            ViewBag.OtherFilesUrl = GetOtherfilesUrl(aDDT01);
            SetOtherFilesOrders(aDDT01);
            //組聲音檔路徑
            ViewBag.VoiceUrl = GetVoiceUrl(aDDT01);

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10, -15, -20, -25 };

            HRMT01 student = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_NO == aDDT01.USER_NO).FirstOrDefault();
            if (student == null)
            {
                ADDT01 aDDT01temp= db.ADDT01.Where(x => x.WRITING_NO == WRITING_NO && x.SCHOOL_NO == aDDT01.SCHOOL_NO && x.USER_NO == aDDT01.USER_NO).FirstOrDefault();
                if (aDDT01temp == null)
                {

                    return RedirectToAction("PermissionError", "Error");
                }
                else {
                    student = new HRMT01();
                    student.USER_NO = aDDT01.USER_NO;
                    student.SCHOOL_NO = aDDT01.SCHOOL_NO;
                    student.CLASS_NO = aDDT01.CLASS_NO;
                    student.IDNO = "";
                }

            }
           


             

            //批改權限
            if (PermissionService.GetActionPermissionMyClassNO(aDDT01.CLASS_NO, SchoolNO, "ADDI01", "Verify", user, aDDT01.USER_NO) == false)
            {
                int VerfierCount1 = 0;

                VerfierCount1 = db.ADDT01.Where(x => x.WRITING_NO == WRITING_NO && x.VERIFIER == user.USER_NO).Count();
                if (PermissionService.GetActionPermissionMyClassNO(student.CLASS_NO, SchoolNO, "ADDI01", "Verify", user, aDDT01.USER_NO) == false && VerfierCount1　==0)
                {
                    return RedirectToAction("PermissionError", "Error");
                }
            }

            //直接作廢
            ViewBag.VisableDelete = PermissionService.GetActionPermissionMyClassNO(aDDT01.CLASS_NO, SchoolNO, "ADDI01", "Disable", user, aDDT01.USER_NO);
            if (ViewBag.VisableDelete == false) ViewBag.VisableDelete = PermissionService.GetActionPermissionMyClassNO(student.CLASS_NO, SchoolNO, "ADDI01", "Disable", user, student.USER_NO);

            //退回再修
            ViewBag.VisableDisableUpSetDraft = PermissionService.GetActionPermissionMyClassNO(aDDT01.CLASS_NO, SchoolNO, "ADDI01", "DisableUpSetDraft", user, aDDT01.USER_NO);
            if (ViewBag.VisableDisableUpSetDraft == false) ViewBag.VisableDisableUpSetDraft = PermissionService.GetActionPermissionMyClassNO(student.CLASS_NO, SchoolNO, "ADDI01", "DisableUpSetDraft", user, student.USER_NO);
            int VerfierCount = 0;

            VerfierCount = db.ADDT01.Where(x => x.WRITING_NO == WRITING_NO && x.VERIFIER == user.USER_NO).Count();
            if (VerfierCount != 0) {
                ViewBag.VisableDelete = true;
                ViewBag.VisableDisableUpSetDraft = true;
            }
            //國語日報模組
            HRMT01 teacher = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).FirstOrDefault();
            var mdnModule = new MdnKidsModule();

            aDDT01.MdnKids = mdnModule.Mapping(student, teacher, UserProfileHelper.GetSchoolName(), aDDT01.SUBJECT, aDDT01.ARTICLE);
            aDDT01.MdnKids.Email2 = aDDT01.E_MAIL2;
            aDDT01.MdnKids.IntroPerson = aDDT01.IntroPerson;
            return View(aDDT01);
        }

        /// <summary>
        /// 批改資料處理
        /// </summary>
        /// <param name="aDDT01"></param>
        /// <param name="Search_Data"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        [ValidateInput(false)]
        public ActionResult Verify(ADDT01 aDDT01, ADDI01IndexViewModel Search_Data)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>(); 
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10, -15, -20, -25 };

            this.SearchData(Search_Data);

            ADDT01 oldADDT01 = db.ADDT01.Find(aDDT01.WRITING_NO);
            if (oldADDT01 == null)
            {
                return HttpNotFound();
            }

            HRMT01 student = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_NO == aDDT01.USER_NO).FirstOrDefault();
            if (student == null) return RedirectToAction("PermissionError", "Error");

            //批改權限
            if (PermissionService.GetActionPermissionMyClassNO(oldADDT01.CLASS_NO, SchoolNO, "ADDI01", "Verify", user, oldADDT01.USER_NO) == false)
            {
                int VerfierCount = 0;
                VerfierCount = db.ADDT01.Where(x => x.WRITING_NO == aDDT01.WRITING_NO && x.VERIFIER == user.USER_NO).Count();
                if (PermissionService.GetActionPermissionMyClassNO(student.CLASS_NO, SchoolNO, "ADDI01", "Verify", user, aDDT01.USER_NO) == false && VerfierCount == 0)
                {

                    return RedirectToAction("PermissionError", "Error");
                }
            }

            //直接退回
            ViewBag.VisableDelete = PermissionService.GetActionPermissionMyClassNO(oldADDT01.CLASS_NO, SchoolNO, "ADDI01", "Disable", user, oldADDT01.USER_NO);
            if (ViewBag.VisableDelete == false) ViewBag.VisableDelete = PermissionService.GetActionPermissionMyClassNO(student.CLASS_NO, SchoolNO, "ADDI01", "Disable", user, student.USER_NO);

            //退回再修
            ViewBag.VisableDisableUpSetDraft = PermissionService.GetActionPermissionMyClassNO(oldADDT01.CLASS_NO, SchoolNO, "ADDI01", "DisableUpSetDraft", user, oldADDT01.USER_NO);
            if (ViewBag.VisableDisableUpSetDraft) ViewBag.VisableDisableUpSetDraft = PermissionService.GetActionPermissionMyClassNO(student.CLASS_NO, SchoolNO, "ADDI01", "DisableUpSetDraft", user, student.USER_NO);

            //評語選單
            ViewBag.VCommentSelectItem = BDMT02Service.GetRefSelectListItem("ADD01", "VERIFY_COMMENT", "ALL", SchoolNO, aDDT01.VERIFY_COMMENT, true, null, true, null, ref db);

            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDI01", "BACK_REASON", "ALL", SchoolNO, aDDT01.BACK_MEMO, true, null, true, null, ref db);

            //沒打勾則不需驗證
            if (aDDT01.PUBLISH_MDNKIDS_YN != "Y")
            {
                ModelState.Remove("MdnKids.IDNO");
                ModelState.Remove("MdnKids.Address");
                ModelState.Remove("MdnKids.ResidenceAddress");
                ModelState.Remove("MdnKids.Phone");
                ModelState.Remove("MdnKids.Email");
            }
            if (aDDT01.SEND_EMAIL_YN != "Y")
            {
                ModelState.Remove("MdnKids.Email2");
            }
            if (aDDT01.CASH == null)
            {
                ViewBag.CashAlert = "e酷幣必填";
                return View(aDDT01);
            }
            if (ModelState.IsValid == false)
            {
                string ErrMsg = string.Empty;
                foreach (var err in ModelState.Values.Where(a => a.Errors.Count > 0))
                {
                    ErrMsg += "   " + err.Errors.First().ErrorMessage + Environment.NewLine;
                }
                ViewBag.CashAlert = ErrMsg;
                return View(aDDT01);
            }

            //更新屬性
            oldADDT01.VERIFY_COMMENT = aDDT01.VERIFY_COMMENT;
            oldADDT01.CASH = aDDT01.CASH;
            oldADDT01.SHARE_YN = aDDT01.SHARE_YN;
            oldADDT01.MdnKids = aDDT01.MdnKids;
            oldADDT01.PUBLISH_CHRIS_YN = aDDT01.PUBLISH_CHRIS_YN;
            if (aDDT01.AutherYN == null) {
                aDDT01.AutherYN = false;
            }
            oldADDT01.AutherYN = aDDT01.AutherYN;
            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(oldADDT01);
            SetImageOrders(aDDT01);
            //組圖檔路徑
            ViewBag.OtherFilesUrl = GetOtherfilesUrl(oldADDT01);
            SetOtherFilesOrders(aDDT01);
            //組聲音檔路徑
            ViewBag.VoiceUrl = GetVoiceUrl(oldADDT01);

            if (aDDT01.ARTICLE != null) aDDT01.ARTICLE = aDDT01.ARTICLE.Replace("\r\n", "");

            //老師有修改文章
            if (oldADDT01.ARTICLE.Replace("\r\n", "<br/>") != aDDT01.ARTICLE)
            {
                //過濾XSS
                // oldADDT01.ARTICLE_VERIFY = Sanitizer.GetSafeHtmlFragment(aDDT01.ARTICLE);
                oldADDT01.ARTICLE_VERIFY = HtmlUtility.SanitizeHtml(aDDT01.ARTICLE);
                //oldADDT01.ARTICLE_VERIFY = aDDT01.ARTICLE;
            }

            //更新屬性
            oldADDT01.VERIFIER = user.USER_NO;
            oldADDT01.VERIFIED_DATE = DateTime.Now;
            oldADDT01.WRITING_STATUS = (byte)ADDStatus.eADDT01Status.Verified;
            if (aDDT01.PUBLISH_MDNKIDS_YN == "Y" && aDDT01.SEND_EMAIL_YN == "Y")
            {
                oldADDT01.IntroPerson = aDDT01.MdnKids.IntroPerson;
                oldADDT01.E_MAIL2 = aDDT01.MdnKids.Email2;
                db.Entry(oldADDT01).Property(p => p.IntroPerson).IsModified = true;
                db.Entry(oldADDT01).Property(p => p.E_MAIL2).IsModified = true;
            }
            ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(oldADDT01.CASH), oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "ADDI01", oldADDT01.WRITING_NO.ToString(), true, ref db,ref valuesList);

            //線上投稿批閱通過給予的點數
            int iSetCoolCash = 1;

            CashHelper.TeachAddCash(user, iSetCoolCash, user.SCHOOL_NO, user.USER_NO, "ADDI01", oldADDT01.WRITING_NO.ToString(), "線上投稿批閱通過", true, null, ref db);

            //產是要 Push 批號
            string BATCH_ID = PushService.CreBATCH_ID();

            string BODY_TXT = "在線上投稿的文章:" + oldADDT01.SUBJECT + "，已經被老師批閱，給酷幣點數:" + oldADDT01.CASH.ToString() + "點。";

            PushService.InsertPushDataParents(BATCH_ID, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "Verify", aDDT01.WRITING_NO.ToString(), "ADDI01/Details?WRITING_NO=" + oldADDT01.WRITING_NO.ToString(), false, ref db);
            PushService.InsertPushDataMe(BATCH_ID, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "Verify", aDDT01.WRITING_NO.ToString(), "ADDI01/Details?WRITING_NO=" + oldADDT01.WRITING_NO.ToString(), false, ref db);

            CreHis("批改", user, oldADDT01);
            try
            {
                db.SaveChanges();
            }
            catch (Exception err)
            {
                System.Diagnostics.Debug.Write(err.Message);
            }

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);

            Tuple<bool, string> sendStatus = Tuple.Create(true, "批改成功。");

            // 發送國語日報邏輯 Is True
            if (aDDT01.PUBLISH_MDNKIDS_YN == "Y")
            {
                MdnKidsField mdnkidField = aDDT01.MdnKids;
                mdnkidField.ArticleContent = HtmlUtility.SanitizeHtml(aDDT01.ARTICLE); // 文章填滿
                var mdnModule = new MdnKidsModule(mdnkidField);
                sendStatus = mdnModule.SendMail();
                //發送Mail到國語日報
                if (sendStatus.Item1)
                {
                    //更新屬性
                    oldADDT01.PUBLISH_MDNKIDS_YN = aDDT01.PUBLISH_MDNKIDS_YN;
                    db.Entry(oldADDT01).Property(p => p.PUBLISH_MDNKIDS_YN).IsModified = true;
                }
                if (aDDT01.SEND_EMAIL_YN == "Y")
                {
                    Tuple<bool, string> sendEMAIL2Status = Tuple.Create(true, "發送成功。");
                    sendEMAIL2Status = mdnModule.SendMailPerson(oldADDT01.E_MAIL2);
                    //發送Mail到國語日報
                    if (sendEMAIL2Status.Item1)
                    {  //更新屬性
                        oldADDT01.SEND_EMAIL_YN = aDDT01.SEND_EMAIL_YN;
                        db.Entry(oldADDT01).Property(p => p.SEND_EMAIL_YN).IsModified = true;
                    }
                }
                //填寫資料存檔到老師Table
                HRMT01 teacher = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).FirstOrDefault();
                db.Entry(teacher).State = System.Data.Entity.EntityState.Modified;
                mdnModule.UpdateEntity(teacher);

                db.SaveChanges();
            }
            TempData["StatusMessage"] = sendStatus.Item2;

            return RedirectToAction(Search_Data.BackAction, Search_Data);
        }

        public ActionResult PASS_DEL(int? WRITING_NO, ADDI01IndexViewModel Search_Data)
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            this.SearchData(Search_Data);

            ADDT01 aDDT01 = db.ADDT01.Find(WRITING_NO);

            if (aDDT01 == null)
            {
                return HttpNotFound();
            }
           
            // 是否為首次發送國語日報
            bool IsFirstPublish = ViewBag.IsFirstPublish = aDDT01.PUBLISH_MDNKIDS_YN != "Y";

            //評語選單
            ViewBag.VCommentSelectItem = BDMT02Service.GetRefSelectListItem("ADD01", "VERIFY_COMMENT", "ALL", SchoolNO, aDDT01.VERIFY_COMMENT, true, null, true, null, ref db);

            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDI01", "BACK_REASON", "ALL", SchoolNO, aDDT01.BACK_MEMO, true, null, true, null, ref db);

            if (aDDT01.ARTICLE != null) aDDT01.ARTICLE = aDDT01.ARTICLE.Replace("\r\n", "<br/>");
            //預設顯示老師批閱的文章
            if (string.IsNullOrWhiteSpace(aDDT01.ARTICLE_VERIFY))
            {

                aDDT01.ARTICLE_VERIFY = aDDT01.ARTICLE;
            }
            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT01);
            ViewBag.Images = aDDT01.IMG_FILE?.Split('|').ToList() ?? new List<string>();
            ViewBag.ImageOrders = aDDT01.IMG_FILE_ORDER?.Split('|').ToList() ?? new List<string>();
            SetImageOrders(aDDT01);
            //組圖檔路徑
            ViewBag.OtherFilesUrl = GetOtherfilesUrl(aDDT01);
            SetOtherFilesOrders(aDDT01);
            //組聲音檔路徑
            ViewBag.VoiceUrl = GetVoiceUrl(aDDT01);

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10, -15, -20, -25 };

            //權限
            if (PermissionService.GetActionPermissionMyClassNO(aDDT01.CLASS_NO, SchoolNO, "ADDI01", "PASS_DEL", user, aDDT01.USER_NO) == false)
            {
                return RedirectToAction("PermissionError", "Error");
            }

            //國語日報模組
            HRMT01 student = db.HRMT01.Where(h => h.SCHOOL_NO == aDDT01.SCHOOL_NO && h.USER_NO == aDDT01.USER_NO).FirstOrDefault();
            HRMT01 teacher = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).FirstOrDefault();
            var mdnModule = new MdnKidsModule();
            aDDT01.MdnKids = mdnModule.Mapping(student, teacher, UserProfileHelper.GetSchoolName(), aDDT01.SUBJECT, aDDT01.ARTICLE);
            aDDT01.MdnKids.IntroPerson = aDDT01.IntroPerson;
            aDDT01.MdnKids.Email2 = aDDT01.E_MAIL2;
            if (aDDT01.PUBLISH_MDNKIDS_YN != "Y")
            {
                ViewBag.userName = user.NAME;
            }
            return View(aDDT01);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        //[ValidateInput(false)]
        public ActionResult PASS_DEL(ADDT01 aDDT01, List<HttpPostedFileBase> files, List<HttpPostedFileBase> files2, string[] youtubelinks, ADDI01IndexViewModel Search_Data, string HIS_MEMO,string TempVoicefile = "", string DeleteImage = "", string DeleteOtherFile = "")
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10, -15, -20, -25 };

            this.SearchData(Search_Data);
            ADDT01 oldADDT01 = db.ADDT01.Find(aDDT01.WRITING_NO);
            if (oldADDT01 == null)
            {
                return HttpNotFound();
            }
            //評語選單
            ViewBag.VCommentSelectItem = BDMT02Service.GetRefSelectListItem("ADD01", "VERIFY_COMMENT", "ALL", SchoolNO, aDDT01.VERIFY_COMMENT, true, null, true, null, ref db);
            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDI01", "BACK_REASON", "ALL", SchoolNO, aDDT01.BACK_MEMO, true, null, true, null, ref db);
            //預設顯示老師批閱的文章
            if (string.IsNullOrWhiteSpace(aDDT01.ARTICLE_VERIFY))
            {

                aDDT01.ARTICLE_VERIFY = aDDT01.ARTICLE;
            }
           
              

            if (aDDT01.ARTICLE != null) aDDT01.ARTICLE = aDDT01.ARTICLE.Replace("\r\n", "<br/>");
            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT01);
            SetImageOrders(aDDT01);
            ViewBag.OtherFilesUrl = GetOtherfilesUrl(aDDT01);
            SetImageOrders(aDDT01);
            //組聲音檔路徑
            ViewBag.VoiceUrl = GetVoiceUrl(aDDT01);
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10, -15, -20, -25 };

            //權限
            if (PermissionService.GetActionPermissionMyClassNO(oldADDT01.CLASS_NO, SchoolNO, "ADDI01", "PASS_DEL", user, oldADDT01.USER_NO) == false)
            {
                return RedirectToAction("PermissionError", "Error");
            }

            // 是否為首次發送國語日報
            bool IsFirstPublish = ViewBag.IsFirstPublish = oldADDT01.PUBLISH_MDNKIDS_YN != "Y";

            ModelState.Remove("ARTICLE");
            //沒打勾則不需驗證
            if (aDDT01.PUBLISH_MDNKIDS_YN != "Y")
            {
                ModelState.Remove("MdnKids.IDNO");
                ModelState.Remove("MdnKids.Address");
                ModelState.Remove("MdnKids.ResidenceAddress");
                ModelState.Remove("MdnKids.Phone");
                ModelState.Remove("MdnKids.Email");
            }
            if (aDDT01.SEND_EMAIL_YN != "Y")
            {
                ModelState.Remove("MdnKids.Email2");
            }
            if (!ModelState.IsValid)
            {
                return View(aDDT01);
            }
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (!string.IsNullOrWhiteSpace(HIS_MEMO))
            {
                CreHis(HIS_MEMO, user, oldADDT01);

                //更新屬性

                oldADDT01.ARTICLE_VERIFY = HtmlUtility.SanitizeHtml(aDDT01.ARTICLE_VERIFY);
                oldADDT01.VERIFY_COMMENT = aDDT01.VERIFY_COMMENT;
                oldADDT01.PUBLISH_CHRIS_YN = aDDT01.PUBLISH_CHRIS_YN;
                oldADDT01.SHARE_YN = aDDT01.SHARE_YN;
                oldADDT01.YOUTUBE_URL = aDDT01.YOUTUBE_URL;
                oldADDT01.MdnKids = aDDT01.MdnKids;
                oldADDT01.AutherYN = aDDT01.AutherYN;
                string Message = string.Empty;
                bool checkFile = false;
                if (files != null)
                {
                    if (files.Count() > 6)
                    {
                        TempData["StatusMessage"] = "您上傳的圖片不可超過六張";
                        return View(aDDT01);
                    }
                }
                if (files2 != null)
                {
                    checkFile = CheckFileUpload(files2, ref Message);
                    if (!checkFile)
                    {
                        TempData["StatusMessage"] = Message;
                        return View(aDDT01);
                    }
                }
                //處理上傳檔案
                bool ans = doImage(oldADDT01, files, youtubelinks, DeleteImage ?? "", Search_Data.ImageOrders);
                if (ans) db.SaveChanges();

                //處理錄音檔搬移
                ans = doVoice(oldADDT01, TempVoicefile);
                if (ans) db.SaveChanges();
                //處理錄音檔搬移
                bool ans2 = doFilesUpload(oldADDT01, files2, DeleteOtherFile ?? "", Search_Data.OtherFilesOrders);
                if (ans2) db.SaveChanges();
                if (aDDT01.PUBLISH_MDNKIDS_YN == "Y" && aDDT01.SEND_EMAIL_YN == "Y")
                {
                    oldADDT01.IntroPerson = aDDT01.MdnKids.IntroPerson;
                    oldADDT01.E_MAIL2 = aDDT01.MdnKids.Email2;
                    db.Entry(oldADDT01).Property(p => p.IntroPerson).IsModified = true;
                    db.Entry(oldADDT01).Property(p => p.E_MAIL2).IsModified = true;
                }

                short Cash = Convert.ToInt16((aDDT01.CASH ?? 0) - (oldADDT01.CASH ?? 0));

                if (Cash != 0)
                {
                    oldADDT01.CASH = aDDT01.CASH;
                    ECOOL_APP.CashHelper.AddCash(user, Cash, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "ADDI01", oldADDT01.WRITING_NO.ToString(), true, ref db,ref valuesList);

                    //產是要 Push 批號
                    string BATCH_ID = PushService.CreBATCH_ID();

                    string BODY_TXT = "在線上投稿的文章:" + oldADDT01.SUBJECT + $"，已經被{user.NAME}老師修改，酷幣點數異動為:" + oldADDT01.CASH.ToString() + "點。";

                    PushService.InsertPushDataParents(BATCH_ID, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "Verify", aDDT01.WRITING_NO.ToString(), "ADDI01/Details?WRITING_NO=" + oldADDT01.WRITING_NO.ToString(), false, ref db);
                    PushService.InsertPushDataMe(BATCH_ID, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "Verify", aDDT01.WRITING_NO.ToString(), "ADDI01/Details?WRITING_NO=" + oldADDT01.WRITING_NO.ToString(), false, ref db);

                    db.SaveChanges();
                    //Push
                    PushHelper.ToPushServer(BATCH_ID);
                }
                else
                {
                    db.SaveChanges();
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(aDDT01.BACK_MEMO)) {


                //更新屬性
                oldADDT01.WRITING_STATUS = (byte)ADDStatus.eADDT01Status.Disable;
                oldADDT01.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(作廢 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + aDDT01.BACK_MEMO;
                oldADDT01.DEL_DATE = DateTime.Now;
                oldADDT01.DEL_PERSON = user.USER_KEY;

                ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(oldADDT01.CASH) * -1, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "ADDI01", oldADDT01.WRITING_NO.ToString(), "線上投稿批閱後作廢", true, ref db, "", "",ref valuesList);

                string BATCH_ID = PushService.CreBATCH_ID();
                string BODY_TXT = "線上投稿被作廢，標題: " + oldADDT01.SUBJECT + "、減少酷幣點數" + (oldADDT01.CASH).ToString() + "數";

                //PushService.InsertPushDataParents(BATCH_ID, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "PASS_DEL", oldADDT01.WRITING_NO.ToString(), "", false, ref db);
                PushService.InsertPushDataMe(BATCH_ID, oldADDT01.SCHOOL_NO, oldADDT01.USER_NO, "", BODY_TXT, "", "ADDI01", "PASS_DEL", oldADDT01.WRITING_NO.ToString(), "", false, ref db);

                //線上投稿批閱通過給予的點數
                //int iSetCoolCash = 1;

                //作廢動作老師不用扣點
                //而且不是扣user而是扣oldADDT01.VERIFIER
                //CashHelper.TeachAddCash(user, iSetCoolCash*-1, user.SCHOOL_NO, user.USER_NO, "ADDI01", oldADDT01.WRITING_NO.ToString(), "線上投稿批閱後作廢", true, ref db);

                db.SaveChanges();
                //更新顯示

                //Push
                PushHelper.ToPushServer(BATCH_ID);
                }
            }

            Tuple<bool, string> sendStatus = Tuple.Create(true, "異動成功。");
            // 發送國語日報邏輯 Is True
            if (aDDT01.PUBLISH_MDNKIDS_YN == "Y" && IsFirstPublish)
            {
                MdnKidsField mdnkidField = aDDT01.MdnKids;
                mdnkidField.ArticleContent = HtmlUtility.SanitizeHtml(
                    string.IsNullOrEmpty(aDDT01.ARTICLE_VERIFY) ? aDDT01.ARTICLE : aDDT01.ARTICLE_VERIFY); // 文章填滿
                var mdnModule = new MdnKidsModule(mdnkidField);
                sendStatus = mdnModule.SendMail();
                //發送Mail到國語日報
                if (sendStatus.Item1)
                {
                    //更新屬性
                    oldADDT01.PUBLISH_MDNKIDS_YN = aDDT01.PUBLISH_MDNKIDS_YN;
                    db.Entry(oldADDT01).Property(p => p.PUBLISH_MDNKIDS_YN).IsModified = true;
                }
                if (aDDT01.SEND_EMAIL_YN == "Y")
                {
                    Tuple<bool, string> sendEMAIL2Status = Tuple.Create(true, "異動成功。");
                    sendEMAIL2Status = mdnModule.SendMailPerson(oldADDT01.E_MAIL2);
                    //發送Mail到國語日報
                    if (sendEMAIL2Status.Item1)
                    {  //更新屬性
                        oldADDT01.SEND_EMAIL_YN = aDDT01.SEND_EMAIL_YN;
                        db.Entry(oldADDT01).Property(p => p.SEND_EMAIL_YN).IsModified = true;
                    }
                }
                //填寫資料存檔到老師Table
                HRMT01 teacher = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == user.USER_NO).FirstOrDefault();
                mdnModule.UpdateEntity(teacher);
                db.Entry(teacher).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
            }
            TempData["StatusMessage"] = sendStatus.Item2;

            return RedirectToAction(Search_Data.BackAction, Search_Data);
        }

        /// <summary>
        /// 修改畫面
        /// </summary>
        /// <param name="WRITING_NO"></param>
        /// <param name="Search_Data"></param>
        /// <returns></returns>
        // GET: ADDI01/Edit/5
        public ActionResult Edit(int? WRITING_NO, ADDI01IndexViewModel Search_Data)
        {
            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT01 aDDT01 = db.ADDT01.Find(WRITING_NO);
            if (aDDT01 == null)
            {
                return HttpNotFound();
            }
            if (!string.IsNullOrWhiteSpace(aDDT01.ARTICLE_VERIFY))
            {


                aDDT01.ARTICLE = HttpUtility.HtmlDecode(aDDT01.ARTICLE_VERIFY); 

            }
            this.SearchData(Search_Data);
            if (aDDT01.AutherYN == null) {
                aDDT01.AutherYN = false;
            }
            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT01);
            ViewBag.Images = aDDT01.IMG_FILE?.Split('|').ToList() ?? new List<string>();
            ViewBag.ImageOrders = aDDT01.IMG_FILE_ORDER?.Split('|').ToList() ?? new List<string>();

            //組圖檔路徑
            ViewBag.OtherFilesUrl = GetOtherfilesUrl(aDDT01);
            ViewBag.Otherfiles = aDDT01.Upload_FILE?.Split('|').ToList() ?? new List<string>();
            ViewBag.Upload_FILE_ORDER = aDDT01.Upload_FILE_ORDER?.Split('|').ToList() ?? new List<string>();
            if (string.IsNullOrEmpty(aDDT01.BACK_MEMO) == false)
            {
                TempData["StatusMessage"] = aDDT01.BACK_MEMO;
            }

            return View(aDDT01);
        }

        private void SetImageOrders(ADDT01 aDDT01)
        {
            ViewBag.ImageOrders = aDDT01.IMG_FILE_ORDER?.Split('|').ToList() ?? new List<string>();
            // 圖片排序
            var orderHelper = new Dictionary<string, string>();

            if (((List<string>)ViewBag.ImageOrders).Count > 0 &&
                ((List<string>)ViewBag.ImageOrders).Count == ((List<string>)ViewBag.ImageUrl).Count)
            {
                for (int i = 0; i < ((List<string>)ViewBag.ImageOrders).Count; i++)
                {
                    if (orderHelper.Count() > 0 && orderHelper.ContainsKey(((List<string>)ViewBag.ImageOrders)[i]))
                    {
                    }
                    else
                    {
                        orderHelper.Add(((List<string>)ViewBag.ImageOrders)[i], ((List<string>)ViewBag.ImageUrl)[i]);
                    }
                }
                orderHelper = orderHelper.OrderBy(o => o.Key).ToDictionary(o => o.Key, p => p.Value);
                ViewBag.ImageUrl = orderHelper.Select(o => o.Value).ToList();
                ViewBag.ImageOrders = orderHelper.Select(o => o.Key).ToList();
            }
        }

        private void SetOtherFilesOrders(ADDT01 aDDT01)
        {
            ViewBag.Upload_FILE_ORDER = aDDT01.Upload_FILE_ORDER?.Split('|').ToList() ?? new List<string>();
            // 圖片排序
            var orderHelper = new Dictionary<string, string>();

            if (((List<string>)ViewBag.Upload_FILE_ORDER).Count > 0 &&
                ((List<string>)ViewBag.Upload_FILE_ORDER).Count == ((List<string>)ViewBag.OtherFilesUrl).Count)
            {
                for (int i = 0; i < ((List<string>)ViewBag.Upload_FILE_ORDER).Count; i++)
                {
                    orderHelper.Add(((List<string>)ViewBag.Upload_FILE_ORDER)[i], ((List<string>)ViewBag.OtherFilesUrl)[i]);
                }
                orderHelper = orderHelper.OrderBy(o => o.Key).ToDictionary(o => o.Key, p => p.Value);
                ViewBag.OtherFilesUrl = orderHelper.Select(o => o.Value).ToList();
                ViewBag.Upload_FILE_ORDER = orderHelper.Select(o => o.Key).ToList();
            }
        }

        /// <summary>
        /// 修改資料處理
        /// </summary>
        /// <param name="aDDT01"></param>
        /// <param name="files"></param>
        /// <param name="TempVoicefile"></param>
        /// <returns></returns>
        // POST: ADDI01/Edit/5
        // 若要免於過量張貼攻擊，請啟用想要繫結的特定屬性，如需
        // 詳細資訊，請參閱 http://go.microsoft.com/fwlink/?LinkId=317598。
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind(Include = "WRITING_NO,SUBJECT,ARTICLE,IMG_FILE,VOICE_FILE,Upload_FILE,CHG_DATE,WRITING_STATUS,PUBLISH_CHRIS_YN,YOUTUBE_URL,AutherYN")]
        ADDT01 aDDT01, List<HttpPostedFileBase> files, List<HttpPostedFileBase> files2, string[] youtubelinks, string TempVoicefile = "", ADDI01IndexViewModel Search = null, string DeleteImage = "", string DeleteOtherFile = "", bool tempSave = false)
        {
            UserProfile user = UserProfileHelper.Get();
            this.SearchData(Search);
            string Message = string.Empty;
            bool checkFile = false;

            if (files == null)
            {
                ModelState.Remove("IMG_FILE");
            }
            if (files2 == null)
            {
                ModelState.Remove("Upload_FILE");
            }

            if (ModelState.IsValid == false)
            {
                var msg = string.Empty;
                foreach (var value in ModelState.Values)
                {
                    if (value.Errors.Count > 0)
                    {
                        foreach (var error in value.Errors)
                        {
                            msg = msg + error.ErrorMessage;
                        }
                    }
                }
                return View(aDDT01);
            }
            AWAT07 aWAT07Item = new AWAT07();
            aWAT07Item = db.AWAT07.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO && a.DEFAULT_YN==true).FirstOrDefault();
            ADDT01 oldADDT01 = db.ADDT01.Find(aDDT01.WRITING_NO);
            if (aDDT01 == null)
            {
                return HttpNotFound();
            }

            if (files != null)
            {
                if (files.Count() > 6)
                {
                    TempData["StatusMessage"] = "您上傳的圖片不可超過六張";
                    return View(aDDT01);
                }
            }
            if (files2 != null)
            {
                checkFile = CheckFileUpload(files2, ref Message);
                if (!checkFile)
                {
                    TempData["StatusMessage"] = Message;
                    return View(aDDT01);
                }
            }
            //如果老師已經批閱，就不能再更動
            if (!(oldADDT01.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Draft || oldADDT01.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.DraftUnSubmit)
                && oldADDT01.WRITING_STATUS.Value != (byte)ADDStatus.eADDT01Status.Back)
            {
                TempData["StatusMessage"] = "老師已經批閱，就不能再更動。";

                return RedirectToAction("Details", new { WRITING_NO = aDDT01.WRITING_NO });
            }

            if (!tempSave) CreHis("修改前", user, oldADDT01);
            if (aWAT07Item == null)
            {
                oldADDT01.PLAYER_NO = 1;
            }
            else
            {
                oldADDT01.PLAYER_NO = aWAT07Item.PLAYER_NO;
            }
         //   oldADDT01.PLAYER_NO = aWAT07Item.PLAYER_NO;
            oldADDT01.SUBJECT = aDDT01.SUBJECT;
            oldADDT01.YOUTUBE_URL = aDDT01.YOUTUBE_URL;
            //原本是草稿 發布日期就要更新
            if (oldADDT01.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.DraftUnSubmit)
            {
                oldADDT01.CRE_DATE = DateTime.Now;
            }
            oldADDT01.CHG_DATE = DateTime.Now;
            oldADDT01.WRITING_STATUS = tempSave ? (byte)ADDStatus.eADDT01Status.DraftUnSubmit : (byte)ADDStatus.eADDT01Status.Draft;
            oldADDT01.BACK_MEMO = null;

            //過濾XSS
            //oldADDT01.ARTICLE = Sanitizer.GetSafeHtmlFragment(aDDT01.ARTICLE);
            oldADDT01.ARTICLE = HtmlUtility.SanitizeHtml(aDDT01.ARTICLE);
            //oldADDT01.ARTICLE = aDDT01.ARTICLE;

            oldADDT01.PUBLISH_CHRIS_YN = aDDT01.PUBLISH_CHRIS_YN;
            if (aDDT01.AutherYN == null)
            {
                aDDT01.AutherYN = false;
            }
            if (!string.IsNullOrWhiteSpace(oldADDT01.ARTICLE_VERIFY))
            {

                aDDT01.ARTICLE_VERIFY = HtmlUtility.SanitizeHtml(aDDT01.ARTICLE);
                oldADDT01.ARTICLE_VERIFY = HtmlUtility.SanitizeHtml(aDDT01.ARTICLE);

            }
            oldADDT01.AutherYN = aDDT01.AutherYN;
            //處理上傳檔案
            bool ans = doImage(oldADDT01, files, youtubelinks, DeleteImage ?? "", Search.ImageOrders);
            if (ans) db.SaveChanges();

            //處理錄音檔搬移
            ans = doVoice(oldADDT01, TempVoicefile);
            if (ans) db.SaveChanges();
            //處理錄音檔搬移
            bool ans2 = doFilesUpload(oldADDT01, files2, DeleteOtherFile ?? "", Search.OtherFilesOrders);
            if (ans2) db.SaveChanges();
            if (!tempSave) CreHis("修改後", user, oldADDT01);
            string TeachTch = "";
            
            TeachTch = db.HRMT01.Where(x => x.SCHOOL_NO == oldADDT01.SCHOOL_NO && x.CLASS_NO == user.CLASS_NO && x.USER_TYPE == "T").Select(x => x.USER_NO).FirstOrDefault();
            if (TeachTch != oldADDT01.VERIFIER)
            {//預設值
                int SYear;
                int Semesters;
                oldADDT01.VERIFIER = TeachTch;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                oldADDT01.SEMESTER = Convert.ToByte(Semesters);
                oldADDT01.SYEAR = Convert.ToByte(SYear);
            }
            // 草稿到這裡停止
            if (tempSave)
            {
                TempData["StatusMessage"] = "暫存成功，期待你下次的編輯";
                if (aDDT01.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Back)
                {
                    return RedirectToAction("Index", new { whereWritingStatus = (byte)ADDStatus.eADDT01Status.DraftUnSubmit });
                }
                else
                {
                    return RedirectToAction("Index", new { whereWritingStatus = aDDT01.WRITING_STATUS, whereUserNo = Search.whereUserNo });
                }
            }

            db.SaveChanges();
            //發mail 給班導
            bool? SendMailPostToTecher = false;
            SendMailPostToTecher = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.SendMailPostToTecher).FirstOrDefault();
            //發mail 給班導
            if (SendMailPostToTecher == null || !(bool)SendMailPostToTecher)
            {
               
              
                MailToTeacher(oldADDT01, user);
            }
          
            string str = "../ADDI01/Edit?WRITING_NO=" + aDDT01.WRITING_NO + "&BackAction=Index &whereShareYN=" + Search.whereShareYN + "&whereComment=" + Search.whereComment + "&whereCommentCash=" + Search.whereCommentCash + "&Page=" + Search.Page;
            TempData["StatusMessage"] = "※投稿成功，請耐心等待評分，老師審核前，可自行修改內容。</br>※若有上傳圖片照片，請注意版權，並可以用「<a href=\"" + str + "\" style=\"color: blue\">修改  </a>」功能調整圖片的方向。</br>※文章必須自行創作，切勿抄襲。";

            return RedirectToAction("Details", new
            {
                WRITING_NO = aDDT01.WRITING_NO,
                BackAction = Search.BackAction,
                OrdercColumn = Search.OrdercColumn,
                whereKeyword = Search.whereKeyword,
                whereUserNo = Search.whereUserNo,
                whereWritingStatus = Search.whereWritingStatus,
                whereShareYN = Search.whereShareYN,
                whereComment = Search.whereComment,
                whereCommentCash = Search.whereCommentCash,
                whereCLASS_NO = Search.whereCLASS_NO,
                whereGrade = Search.whereGrade,
                Page = Search.Page
            });
        }

        public List<Image_File_Multiple> GetImageDictionaryUrl(List<ADDT01> liaDDT01)
        {
            List<Image_File_Multiple> dicImage = new List<Image_File_Multiple>();

            foreach (var item in liaDDT01)
            {
                dicImage.Add(new Image_File_Multiple { APPLY_NO = item.WRITING_NO, ImageUrl = GetImageUrl(item) });
            }
            return dicImage;
        }

        public string GetVoiceUrl(ADDT01 aDDT01)
        {
            string Path = GetVoicePath(aDDT01);

            if (string.IsNullOrEmpty(Path) == false)
            {
                return Url.Content(Path);
            }

            return "";
        }

        public string GetVoicePath(ADDT01 aDDT01)
        {
            if (string.IsNullOrEmpty(aDDT01.VOICE_FILE) == false)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
                string imgPath = string.Format(@"{0}ADDI01Audio\{1}\{2}\{3}\", System.Web.HttpContext.Current.Server.MapPath(UploadImageRoot), aDDT01.SCHOOL_NO.ToString(), aDDT01.USER_NO.ToString(), aDDT01.WRITING_NO.ToString());

                if (System.IO.File.Exists(Path.Combine(imgPath, aDDT01.VOICE_FILE)))
                {
                    string voiceUrl = string.Format(@"{0}ADDI01Audio\{1}\{2}\{3}\{4}", UploadImageRoot, aDDT01.SCHOOL_NO, aDDT01.USER_NO, aDDT01.WRITING_NO.ToString(), aDDT01.VOICE_FILE);
                    return voiceUrl;
                }
            }

            return "";
        }

        public List<string> GetImageUrl(ADDT01 aDDT01)
        {
            return GetImagePath(aDDT01);
        }

        public List<string> GetOtherfilesUrl(ADDT01 aDDT01)
        {
            return GetOtherPath(aDDT01);
        }

        public List<string> GetOtherPath(ADDT01 aDDT01, bool Player = true)
        {
            List<string> OtherfilePath = new List<string>();
            if (string.IsNullOrEmpty(aDDT01.Upload_FILE) == false)
            {  //組上傳資料夾路徑
                string UploadImageRoot =
                    System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
                string imgPath = string.Format(@"{0}ADDI01OTHERFILE\{1}\", Request.MapPath(UploadImageRoot), aDDT01.SCHOOL_NO.ToString());

                List<string> ArrOtherfile = aDDT01.Upload_FILE.Split('|').ToList();
                foreach (var Otherfiles in ArrOtherfile)
                {
                    string url = Otherfiles;
                    string error;
                    if (System.IO.File.Exists(Path.Combine(imgPath, Otherfiles)))
                    {
                        string imgUrl = ECOOL_APP.UrlCustomHelper.Url_Content(string.Format(@"{0}ADDI01OTHERFILE\{1}\{2}", UploadImageRoot, aDDT01.SCHOOL_NO, Otherfiles));
                        OtherfilePath.Add(imgUrl);
                    }
                }
            }
            if (OtherfilePath.Count > 0)
            {
                return OtherfilePath;
            }
            return OtherfilePath;
        }

        public List<string> GetImagePath(ADDT01 aDDT01, bool Player = true)
        {
            List<string> ImagePath = new List<string>();

            if (string.IsNullOrEmpty(aDDT01.IMG_FILE) == false)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];

                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";

                string imgPath = string.Format(@"{0}ADDI01IMG\{1}\", System.Web.HttpContext.Current.Server.MapPath(UploadImageRoot), aDDT01.SCHOOL_NO.ToString());

                List<string> ArrImg = aDDT01.IMG_FILE.Split('|').ToList();

                foreach (var Img in ArrImg)
                {
                    string url = Img;
                    string error;
                    if (LogicCenter.YoutubeUrlConvert(ref url, out error))
                    {
                        ImagePath.Add(url);
                        continue;
                    }

                    if (System.IO.File.Exists(Path.Combine(imgPath, Img)))
                    {
                        string imgUrl = ECOOL_APP.UrlCustomHelper.Url_Content(string.Format(@"{0}ADDI01IMG\{1}\{2}", UploadImageRoot, aDDT01.SCHOOL_NO, Img));
                        ImagePath.Add(imgUrl);
                    }
                }

                if (ImagePath.Count > 0)
                {
                    return ImagePath;
                }
            }
            if (aDDT01.PLAYER_NO != null && Player==true) {
             
              

              
                    string ImageUrl = @"~/Content/Players/";

                    ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(ImageUrl + db.AWAT06.Find(aDDT01.PLAYER_NO).IMG_FILE));
                    return ImagePath;
             

                //ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(string.Format("~/Content/SchoolIMG/{0}/ADDI01.jpg", aDDT01.SCHOOL_NO)));

            }
            
            if (aDDT01.PLAYER_NO==null&& Player)
            {
                //沒有上傳圖片則改放角色娃娃
                AWAT07 WriterPlayer =
                    db.AWAT07.Where(a => a.SCHOOL_NO == aDDT01.SCHOOL_NO && a.USER_NO == aDDT01.USER_NO && a.DEFAULT_YN == true)
                    .FirstOrDefault();

                if (WriterPlayer != null)
                {
                    string ImageUrl = @"~/Content/Players/";

                    ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(ImageUrl + db.AWAT06.Find(WriterPlayer.PLAYER_NO).IMG_FILE));
                    return ImagePath;
                }

                ImagePath.Add(ECOOL_APP.UrlCustomHelper.Url_Content(string.Format("~/Content/SchoolIMG/{0}/ADDI01.jpg", aDDT01.SCHOOL_NO)));
            }
            return ImagePath;
        }

        /// <summary>
        /// 建議與鼓勵
        /// </summary>
        /// <param name="WRITING_NO"></param>
        /// <returns></returns>
        public ActionResult Comment(int? WRITING_NO, ADDI01IndexViewModel Search_Data)
        {
            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            this.SearchData(Search_Data);

            // 表情圖片s
            Dictionary<string, string> emoticons_Dictionary = new Dictionary<string, string>();
            string directorypath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Content\\img\\emoticons");
            string[] emoticonFiles = Directory.GetFiles(directorypath, "*.*")
                                     .Select(Path.GetFileName)
                                     .ToArray();
            foreach (string icon in emoticonFiles)
            {
                emoticons_Dictionary.Add(icon, Url.Content("~/Content/img/emoticons/" + icon));
            }

            ADDI01CommentViewModel comment = new ADDI01CommentViewModel();
            comment.WRITING_NO = WRITING_NO.Value;
            comment.CRE_DATE = DateTime.Now;
            comment.Emoticon_Dictionary = emoticons_Dictionary;

            QA(comment);

            UserProfile user = UserProfileHelper.Get();
            if (user != null)
            {
                comment.NICK_NAME = user.SNAME;
                if (user.USER_TYPE == UserType.Parents) comment.NICK_NAME = comment.NICK_NAME + "家長";
            }
            return View(comment);
        }

        /// <summary>
        /// 建議與鼓勵資料處理
        /// </summary>
        /// <param name="comment"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Comment(ADDI01CommentViewModel comment, ADDI01IndexViewModel Search)
        {
            this.SearchData(Search);

            if (ModelState.IsValid == false)
            {
                QA(comment);
                return View(comment);
            }

            if (QAnswer(comment.Question1, comment.Question2, comment.UserAnswer) == false)
            {
                QA(comment);
                return View(comment);
            }

            //產是要 Push 批號
            string BATCH_ID = PushService.CreBATCH_ID();

            ADDT02 UserComment = db.ADDT02.Create();
            UserComment.WRITING_NO = comment.WRITING_NO;
            UserComment.NICK_NAME = comment.NICK_NAME;
            UserComment.COMMENT = comment.UserComment;
            UserComment.EMOTICON = comment.EMOTICON;

            UserProfile user = UserProfileHelper.Get();
            if (user != null)
            {
                UserComment.SCHOOL_NO = user.SCHOOL_NO;
                UserComment.USER_NO = user.USER_NO;
                UserComment.NICK_NAME = user.SNAME;
                if (user.USER_TYPE == UserType.Parents) UserComment.NICK_NAME = UserComment.NICK_NAME + "家長";
                if (user.USER_TYPE == UserType.Student)
                    UserComment.CLASS_NO = user.CLASS_NO;
            }
            UserComment.IP_ADDRESS = Request.ServerVariables["REMOTE_ADDR"];
            UserComment.CRE_DATE = DateTime.Now;

            //儲存資料
            db.ADDT02.Add(UserComment);

            ADDT01 t01 = db.ADDT01.Where(a => a.WRITING_NO == UserComment.WRITING_NO).FirstOrDefault();
            if (t01 != null)
            {
                string BODY_TXT = "線上投稿-建議與鼓勵，文章:" + t01.SUBJECT + "，訪客姓名:" + UserComment.NICK_NAME + "，建議與鼓勵內容:" + UserComment.COMMENT;

                PushService.InsertPushDataMe(BATCH_ID, t01.SCHOOL_NO, t01.USER_NO, "", BODY_TXT, "", "ADDI01", "Comment", t01.WRITING_NO.ToString(), "ADDI01/Details?WRITING_NO=" + t01.WRITING_NO.ToString(), false, ref db);
            }

            db.SaveChanges();

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            return RedirectToAction("Details"
                , new
                {
                    WRITING_NO = comment.WRITING_NO,
                    BackAction = Search.BackAction,
                    OrdercColumn = Search.OrdercColumn,
                    whereKeyword = Search.whereKeyword,
                    whereUserNo = Search.whereUserNo,
                    whereWritingStatus = Search.whereWritingStatus,
                    whereShareYN = Search.whereShareYN,
                    whereComment = Search.whereComment,
                    whereCommentCash = Search.whereCommentCash,
                    whereCLASS_NO = Search.whereCLASS_NO,
                    whereGrade = Search.whereGrade,
                    Page = Search.Page
                });
        }

        // GET: ADDI01/Delete/5
        public ActionResult Delete(int? WRITING_NO)
        {
            if (WRITING_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            ADDT01 aDDT01 = db.ADDT01.Include("ADDT02").Where(a => a.WRITING_NO == WRITING_NO).FirstOrDefault();
            if (aDDT01 == null)
            {
                return HttpNotFound();
            }
            return View(aDDT01);
        }

        // POST: ADDI01/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int WRITING_NO)
        {
            UserProfile user = UserProfileHelper.Get();
            ADDT01 aDDT01 = db.ADDT01.Find(WRITING_NO);

            CreHis("刪除前", user, aDDT01);

            db.ADDT01.Remove(aDDT01);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        public void SearchData(ADDI01IndexViewModel Data)
        {
            if (Data == null) Data = new ADDI01IndexViewModel();

            TempData["Search"] = Data;
            ViewBag.BackAction = Data.BackAction;
            ViewBag.OrdercColumn = Data.OrdercColumn;
            ViewBag.whereKeyword = Data.whereKeyword;
            ViewBag.whereUserNo = Data.whereUserNo;
            ViewBag.whereWritingStatus = Data.whereWritingStatus;
            ViewBag.whereShareYN = Data.whereShareYN;
            ViewBag.whereComment = Data.whereComment;
            ViewBag.whereCommentCash = Data.whereCommentCash;
            ViewBag.whereCLASS_NO = Data.whereCLASS_NO;
            ViewBag.whereGrade = Data.whereGrade;
            ViewBag.Page = Data.Page;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private static void QA(ADDI01CommentViewModel comment)
        {
            //準備問題與答案
            string[] word = new string[] { "０", "１", "２", "３", "４", "５", "６", "７", "８", "９" };
            int seed = Guid.NewGuid().GetHashCode();
            Random Chance = new Random(seed);
            int num1 = Chance.Next(0, 9);
            int num2 = Chance.Next(0, 9);
            comment.Question1 = word[num1];
            comment.Question2 = word[num2];
        }

        private static bool QAnswer(string q1, string q2, string UserAnswer)
        {
            //準備問題與答案
            string[] word = new string[] { "０", "１", "２", "３", "４", "５", "６", "７", "８", "９" };

            int n1 = 0;
            int n2 = 0;
            for (int i = 0; i < word.Length; i++)
            {
                if (q1 == word[i]) n1 = i;
                if (q2 == word[i]) n2 = i;
            }

            if ((n1 * n2).ToString() == UserAnswer.Trim()) return true;

            return false;
        }

        private List<int> GetCanLikes(ADDT01 aDDT01)
        {
            UserProfile user = UserProfileHelper.Get();
            List<int> Likes = new List<int>();

            //只有作者與批閱老師可以給讚!
            if (user == null) return Likes;
            if (aDDT01.USER_NO != user.USER_NO && aDDT01.VERIFIER != user.USER_NO) return Likes;

            List<string> Users =
            aDDT01.ADDT02.Where(c => c.CASH > 0 && string.IsNullOrEmpty(c.USER_NO) == false).Select(c => c.USER_NO).ToList();
            if (User != null && aDDT01.ADDT02.Count > 0)
            {
                foreach (ADDT02 comment in aDDT01.ADDT02)
                {
                    if (string.IsNullOrWhiteSpace(comment.USER_NO)) continue;
                    if (string.IsNullOrWhiteSpace(comment.CLASS_NO)) continue;//排除老師
                    if (comment.CASH > 0) continue;
                    if (comment.USER_NO == aDDT01.USER_NO) continue;//自己不能給自己

                    if (Users.Contains(comment.USER_NO)) continue;
                    Likes.Add(comment.COMMENT_NO);
                }
            }
            return Likes;
        }

        /// <summary>
        /// 發mail 給班導
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="VERIFIER"></param>
        /// <param name="WRITING_NO"></param>
        /// <param name="user"></param>
        private void MailToTeacher(ADDT01 aDDT01, UserProfile user)
        {
            HRMT01 TEACHER = (from c in db.HRMT01
                              where c.SCHOOL_NO == aDDT01.SCHOOL_NO && c.USER_NO == aDDT01.VERIFIER && (!string.IsNullOrEmpty(c.E_MAIL))
                              select c).FirstOrDefault();

            if (TEACHER != null)
            {
                if (string.IsNullOrWhiteSpace(TEACHER.E_MAIL) == false)
                {
                    string SUBJECT = "線上投稿通知";

                    StringBuilder MailBodyHtml = new StringBuilder();

                    string UrlString = @"http://" + Request.Url.Authority + Request.ApplicationPath + string.Format(@"/ADDI01/Verify?WRITING_NO={0}", aDDT01.WRITING_NO);

                    MailBodyHtml.Append("<table border='0' cellspacing='0' cellpadding='0' width='640'>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p><font color='#0066FF'>{0}─臺北e酷幣線上投稿通知！</font></p><br/></td>", user.SCHOOL_NAME);
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p> 親愛的 <font color='#0066FF'>{0}</font> 老師您好:<br/><br/>", TEACHER.NAME);
                    MailBodyHtml.AppendFormat("您的學生 <font color='#0066FF'>{0}</font> 在臺北e酷幣線上投稿投稿了一篇文章：<font color='#0066FF'>{1}</font> <br/>", aDDT01.NAME, aDDT01.SUBJECT);
                    MailBodyHtml.Append("希望您能撥空前往閱讀學生的文章，並給予鼓勵與建議。謝謝！ <br/><br/>");
                    MailBodyHtml.AppendFormat(@"詳細資料： <a href = '{0}' target = '_blank'>{0}</a></p></td>", UrlString);
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p align = 'right'><br/>by 臺北e酷幣</p></td>");
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("</table>");

                    List<string> Mail = new List<string>();
                    Mail.Add(TEACHER.E_MAIL);

                    MailHelper MailHelper = new MailHelper();
                    MailHelper.SendMailByGmail(Mail, SUBJECT, MailBodyHtml.ToString());
                }
            }
        }

        private bool doVoice(ADDT01 aDDT01, string Voicefile)
        {
            if (Voicefile == null) return false;

            string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
            string USER_NO = string.Empty;

            UserProfile user = UserProfileHelper.Get();
            if (user != null) USER_NO = user.USER_NO;

            //b.資料夾路徑
            string UploadImageRoot =
                System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";

            //來源暫存檔
            string sourceFile = string.Format(@"{0}ADDI01Audio\{1}\{2}\Temp", Request.MapPath(UploadImageRoot), SCHOOL_NO, USER_NO);

            if (Directory.Exists(sourceFile)) //判斷有此路徑
            {
                if (System.IO.File.Exists(Path.Combine(sourceFile, Voicefile)))  //判斷有此檔案
                {
                    //a.組新檔案名稱
                    aDDT01.VOICE_FILE = aDDT01.WRITING_NO.ToString() + "_" + Voicefile;

                    string destinationFile = string.Format(@"{0}ADDI01Audio\{1}\{2}\{3}\", Request.MapPath(UploadImageRoot), SCHOOL_NO, USER_NO, aDDT01.WRITING_NO.ToString());
                    if (Directory.Exists(destinationFile)) Directory.Delete(destinationFile, true); //砍此目錄+檔案

                    Directory.CreateDirectory(destinationFile); //重建目錄

                    //Copy檔案
                    System.IO.File.Copy(Path.Combine(sourceFile, Voicefile), Path.Combine(destinationFile, aDDT01.VOICE_FILE), true);

                    Directory.Delete(sourceFile, true);
                }
            }

            return true;
        }

        private bool CheckFileUpload(List<HttpPostedFileBase> Otherfile, ref string message)
        {
            long FILEMAXLength = (1024 * 1024 * 6);
            long NowFileLength = 0;
            foreach (var fileItem in Otherfile)
            {
                if (fileItem != null && fileItem.ContentLength > 0)
                {
                    NowFileLength = NowFileLength + fileItem.ContentLength;
                    if (fileItem.ContentLength > FILEMAXLength)
                    {
                        message = string.Format("警告!PPT檔案超過{0}", FileHelper.Cvt(FILEMAXLength));
                        return false;
                    }
                }
            }
            return true;
        }

        private bool doFilesUpload(ADDT01 aDDT01, List<HttpPostedFileBase> Otherfile, string deletes = "", string[] orders = null)
        {
            if (Otherfile == null) return false;
            List<string> DEL_file = deletes.Length > 0 ? deletes.Split('|').ToList() : new List<string>();
            List<string> Otherfile_FILE = new List<string>();
            // 排序方式 [ 1, 2, 3 ]
            if (orders != null) aDDT01.Upload_FILE_ORDER = string.Join("|", orders);
            //組上傳資料夾路徑
            string UploadImageRoot =
                System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
            string imgPath = string.Format(@"{0}ADDI01OTHERFILE\{1}\", Request.MapPath(UploadImageRoot), aDDT01.SCHOOL_NO.ToString());
            if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);
            foreach (var file in Otherfile)
            {
                if (file != null && file.ContentLength > 0)
                {
                    string FileName = aDDT01.WRITING_NO.ToString() + "_" + Path.GetFileName(file.FileName);

                    Regex regexCode = new Regex(@".*\.(ppt|pptx|pdf|PDF)");
                    string ext = Path.GetExtension(file.FileName);
                    if (regexCode.IsMatch(FileName.ToLower()) == false
                     && ext != ".jpg" && ext != ".jpeg" && ext != ".png" && ext != ".bmp" && ext != ".gif")

                    {
                        return false;
                    }

                    //組檔案名稱
                    Otherfile_FILE.Add(FileName);
                    if (regexCode.IsMatch(FileName.ToLower()))
                    {
                        file.SaveAs(Path.Combine(imgPath, FileName));
                    }
                }
            }
            var Otherfiles = aDDT01.Upload_FILE?.Split('|').Where(i => i != "").ToList() ?? new List<string>();
            foreach (var del in DEL_file)
            {
                var find_del = Otherfiles.FirstOrDefault(i => i == del);
                if (find_del != null)
                {
                    Otherfiles.Remove(find_del);
                }
            }
            if (Otherfile_FILE.Count > 0)
            {
                foreach (var other in Otherfile_FILE)
                {
                    if (!Otherfiles.Contains(other))
                    {
                        Otherfiles.Add(other);
                    }
                }
            }
            aDDT01.Upload_FILE = string.Join("|", Otherfiles);
            return true;
        }

        private bool doImage(ADDT01 aDDT01, List<HttpPostedFileBase> Imgfile, string[] youtubelinks = null, string deletes = "", string[] orders = null)
        {
            if (Imgfile == null) return false;

            List<string> DEL_IMG = deletes.Length > 0 ? deletes.Split('|').ToList() : new List<string>();

            List<string> IMG_FILE = new List<string>();

            // 排序方式 [ 1, 2, 3 ]
            if (orders != null) aDDT01.IMG_FILE_ORDER = string.Join("|", orders);

            //組上傳資料夾路徑
            string UploadImageRoot =
                System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
            string imgPath = string.Format(@"{0}ADDI01IMG\{1}\", Request.MapPath(UploadImageRoot), aDDT01.SCHOOL_NO.ToString());
            if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

            foreach (var file in Imgfile)
            {
                if (file != null && file.ContentLength > 0)
                {
                    string FileName = aDDT01.WRITING_NO.ToString() + "_" + Path.GetFileName(file.FileName);

                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

                    string ext = Path.GetExtension(file.FileName);

                    if (regexCode.IsMatch(FileName.ToLower()) == false
                        && ext != ".ppt" && ext != ".pptx")
                    {
                        return false;
                    }

                    //組檔案名稱
                    IMG_FILE.Add(FileName);

                    if (regexCode.IsMatch(FileName.ToLower()))
                    {
                        //縮圖
                        System.Drawing.Image image = System.Drawing.Image.FromStream(file.InputStream);
                        double FixWidth = 1000;
                        double FixHeight = 1000;
                        double rate = 1;
                        if (image.Width > FixWidth || image.Height > FixHeight)
                        {
                            if (image.Width > FixWidth) rate = FixWidth / image.Width;
                            else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                            int w = Convert.ToInt32(image.Width * rate);
                            int h = Convert.ToInt32(image.Height * rate);
                            Bitmap imageOutput = new Bitmap(image, w, h);
                            imageOutput.Save(Path.Combine(imgPath, FileName), image.RawFormat);
                            imageOutput.Dispose();
                        }
                        else
                        {
                            //直接儲存
                            file.SaveAs(Path.Combine(imgPath, FileName));
                        }
                        image.Dispose();
                    }
                    else
                    {
                        file.SaveAs(Path.Combine(imgPath, FileName));
                    }
                }
            }

            var images = aDDT01.IMG_FILE?.Split('|').Where(i => i != "").ToList() ?? new List<string>();
            foreach (var del in DEL_IMG)
            {
                var find_del = images.FirstOrDefault(i => i == del);
                if (find_del != null)
                {
                    images.Remove(find_del);
                }
            }

            if (IMG_FILE.Count > 0)
            {
                foreach (var img in IMG_FILE)
                {
                    if (!images.Contains(img))
                    {
                        images.Add(img);
                    }
                }
            }

            if (youtubelinks != null)
            {
                foreach (var link in youtubelinks)
                {
                    images.Add(link);
                }
            }

            aDDT01.IMG_FILE = string.Join("|", images);

            return true;
        }

        private void CreHis(string HIS_MEMO, UserProfile user, ADDT01 oldADDT01)
        {
            ADDT01_HIS Cre = new ADDT01_HIS()
            {
                HIS_NO = Guid.NewGuid().ToString("N"),
                HIS_PERSON = user.USER_KEY,
                HIS_DATE = DateTime.Now,
                HIS_MEMO = HIS_MEMO,
                WRITING_NO = oldADDT01.WRITING_NO,
                SCHOOL_NO = oldADDT01.SCHOOL_NO,
                USER_NO = oldADDT01.USER_NO,
                CLASS_NO = oldADDT01.CLASS_NO,
                SYEAR = oldADDT01.SYEAR,
                SEMESTER = oldADDT01.SEMESTER,
                SEAT_NO = oldADDT01.SEAT_NO,
                NAME = oldADDT01.NAME,
                SNAME = oldADDT01.SNAME,
                SUBJECT = oldADDT01.SUBJECT,
                ARTICLE = oldADDT01.ARTICLE,
                ARTICLE_VERIFY = oldADDT01.ARTICLE_VERIFY,
                VERIFY_COMMENT = oldADDT01.VERIFY_COMMENT,
                IMG_FILE = oldADDT01.IMG_FILE,
                VOICE_FILE = oldADDT01.VOICE_FILE,
                SHARE_YN = oldADDT01.SHARE_YN,
                CASH = oldADDT01.CASH,
                READ_COUNT = oldADDT01.READ_COUNT,
                WRITING_STATUS = oldADDT01.WRITING_STATUS,
                VERIFIER = oldADDT01.VERIFIER,
                VERIFIED_DATE = oldADDT01.VERIFIED_DATE,
                CRE_DATE = oldADDT01.CRE_DATE,
                CHG_DATE = oldADDT01.CHG_DATE,
                BACK_MEMO = oldADDT01.BACK_MEMO,
                DEL_PERSON = oldADDT01.DEL_PERSON,
                DEL_DATE = oldADDT01.DEL_DATE,
                PUBLISH_CHRIS_YN = oldADDT01.PUBLISH_CHRIS_YN,
                PUBLISH_MDNKIDS_YN = oldADDT01.PUBLISH_MDNKIDS_YN,
                PLAYER_NO=oldADDT01.PLAYER_NO
            };
            db.ADDT01_HIS.Add(Cre);
        }
    }
}