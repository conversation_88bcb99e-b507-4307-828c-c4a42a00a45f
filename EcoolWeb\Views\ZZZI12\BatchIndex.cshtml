﻿@{
    ViewBag.Title = "帳號權限維護 - 批次權限新增";
    TempData["StatusMessage"] = "【勾選/取消】核取方塊後立即【增加權限/取消權限】<br/>";

}
@Html.Partial("_Title_Secondary")

@Html.ActionLink("切換單一權限修改", "Index", "ZZZI12", new { SouBre_NO = "ZZZI12" }, new { @class = "btn btn-success btn-xs" })

@using (Html.BeginForm("BatchIndex", "ZZZI12", FormMethod.Get))
{
    <br />

    <div class="form-inline">
        <div class="form-group">
            <span>角色</span>
        </div>
        <div class="form-group">
            <select class="form-control input-sm" id="whereRoleID" onchange="refreshTable();">
                @{
                    string Selected_Val;

                    foreach (var item in ViewBag.HRMT24ListItems as List<ECOOL_APP.com.ecool.Models.entity.uHRMT24>)
                    {
                        if (ViewBag.Q_ROLE_ID == item.ROLE_ID)
                        {
                            Selected_Val = "selected";
                        }
                        else
                        {
                            Selected_Val = "";
                        }

                        <option value="@item.ROLE_ID" @Selected_Val>@item.ROLE_NAME</option>
                    }
                }

            </select>
        </div>
        <br /><br />
        <div class="form-group">
            <span>學校</span>
        </div>
        <div class="form-group">
            <select class="form-control input-sm" id="whereSchoolNo" onchange="refreshTable()">
                @{
                    foreach (var item in ViewBag.SchoolNoItems as List<SelectListItem>)
                    {
                        if (ViewBag.Q_SCHOOL_NO == item.Value)
                        {
                            Selected_Val = "selected";
                        }
                        else
                        {
                            Selected_Val = "";
                        }
                        <option value="@item.Value" @Selected_Val>@item.Text</option>
                    }
                }
            </select>
        </div>
        <br /><br />
    </div>

}

<div class="panel panel-ACC" name="top">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="panel-Group  text-center">
        人員清單
    </div>
    <div class="panel-body" style="margin:2%">
        <div class="form-horizontal">
            <div class="form-group">
                <table id="peopleTable">
                    <thead>
                        <tr>
                            <th class="cth">勾選人員</th>
                            <th class="cth">姓名</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@Html.Partial("_Notice")

<div class="panel panel-ACC">
    <div class="panel-Group text-center">
        權限清單
    </div>
    <div class="panel-body">
        <table class="table-ecool table-hover table-ecool-ACC">
            <thead>
                <tr>
                    <th style="text-align:left">功能名稱</th>
                    <th style="text-align:left">動作</th>
                </tr>
            </thead>
            <tbody id="authTableBody"></tbody>
        </table>
    </div>
</div>

<div class="loader">
    <center>
        <h1 class="loading-image"><span class="glyphicon glyphicon-search"></span>處理中，請稍後 ...</h1>
    </center>
</div>
@section css{
    <link href="~/Content/datatables.min.css" rel="stylesheet" />
    <link href="@Url.Content("~/Scripts/toastr/toastr.min.css")" rel="stylesheet" />

    <style>
        .ctd, .cth {
            text-align: center;
        }
        .loading-image {
            opacity: 0.8;
            margin: 15% 20%;
            z-index: 10;
            color: white;
            background-color: black;
            padding: 2%;
        }

        .loader {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1; /* Sit on top */
            padding-top: 100px; /* Location of the box */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgb(0,0,0); /* Fallback color */
            background-color: rgba(0,0,0,0.8); /* Black w/ opacity */
        }
        #peopleTable input[type="checkbox"] {
            width: 20px; /*Desired width*/
            height: 20px; /*Desired height*/
            cursor: pointer;
        }
    </style>
}

@section scripts{
    <script src="~/Scripts/datatables.min.js"></script>
    <script src="@Url.Content("~/Scripts/toastr/toastr.min.js")"></script>

    <script>
        var peopleTable;

        $(document).ready(function (){
            refreshTable();

            peopleTable = $('#peopleTable').DataTable({
                "language": {
                    "search": "搜尋關鍵字 ",
                    "lengthMenu": "顯示 _MENU_ 筆數",
                },
                searching: true,
                lengthChange: true,
                info: false,
                pageLength: 10,
                paging: true,
            });
        });

        function loaderShow() {
            $('.loader').show();
        }
        function loaderHide() {
            $('.loader').fadeOut('slow');
        }

       // 更新人員清單Table
       function refreshTable() {
            var selSchoolNo = $("#whereSchoolNo").val();
            var selRoleId = $("#whereRoleID").val();
            loaderShow();

             $.ajax({
                 url: '@Url.Content("~/ZZZI12/GetPeopleList")?schoolNo=' + selSchoolNo + "&roleid=" + selRoleId,
                 type: 'GET',
                 contentType: "application/json; charset=utf-8",
                 success: function (data) {
                     $("#authTableBody").empty();
                     peopleTable.clear();
                     $.each(data, function (idx, item) {
                         var tr = $(`
                                 <tr>
                                    <td class="ctd">
                                        <input type="checkbox" class="isUserCheck" onchange="GetPeopleAuthList()" />
                                    </td>
                                    <td class="ctd">
                                        <a href="#" role="button">` + item.NAME + `</a>
                                        <input type="hidden" value="` + item.USER_NO + `" />
                                    </td>
                                </tr>
                                `);

                         peopleTable.row.add(tr).draw();
                     })
                },
                 error: function (jqXHR, textStatus, errorThrown) {
                     alert(errorThrown);
                },
                 complete: function () {
                     loaderHide();
                }
            });
        }

        // 得到勾選的userno
        function getSelectedUserNo() {
            var arr = [];
            $.each($(".isUserCheck:checked").parent().next().children("input"), function (idx, element) {
                arr.push(element.value)
            });
            return arr;
        }

        var myRoleID = parseInt('@ViewBag.MyRoleID');
        // 建置權限清單 tr row
        function trBreActionBuilder(item) {
            var selRoleId = $("#whereRoleID").val();
            var tr = $("<tr>");
            var isGroup = false;
            var actionstr = "";
            var canEdit = false;
            $.each(item.Details_List, function (idx, item_d) {
                isGroup = false;
                canEdit = parseInt(selRoleId) > myRoleID ;
                if (item_d.ACTION_NAME != '') {
                    actionstr += `
                             <input type="hidden" name="ACTION_ID" value="` + item_d.ACTION_ID +`" />
                             <input type="hidden" name="BRE_NO" value="` + item_d.BRE_NO +`" />
                             <input type="checkbox" class="changeAuthCheckBox" ` + (item_d.Checked == true ? "checked" : "") + ` ` + (canEdit == false ? "disabled title='權限不足'" : "") +` />
                             <span>` + item_d.ACTION_NAME + `</span>
                           `;
                } else {
                    isGroup = true;
                }
            });

            tr.attr('class',  (isGroup == true)? "Group":""  )
            tr.append(
                `
                     <td width="30%">
                         ` + item.BRE_NAME + `
                     </td>
                     <td width="70%"> ` + actionstr + `</td>
                 `
            )
            return tr;
        }

        // 取得user[0]的權限
        function GetPeopleAuthList() {
            var usernos = getSelectedUserNo();
            var selSchoolNo = $("#whereSchoolNo").val();
            var selRoleId = $("#whereRoleID").val();

            if (usernos.length < 1) {
                $("#authTableBody").empty();
                return;
            }

            $.ajax({
                 url: '@Url.Content("~/ZZZI12/GetPeopleAuthList")',
                 type: 'POST',
                 contentType: "application/json; charset=utf-8",
                 dataType: 'json',
                 data: JSON.stringify({
                     schoolNo: selSchoolNo,
                     roleid: selRoleId,
                     user_Nos: usernos
                 }),
                 success: function (data) {
                     $("#authTableBody").empty();
                     $.each(data, function (idx, item) {
                         $("#authTableBody").append(trBreActionBuilder(item));
                     })
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    alert(errorThrown);
                },
                complete: function () {
                }
            });
        }

        //更改權限
        $("#authTableBody").on('change', '.changeAuthCheckBox', function () {
            var usernos = getSelectedUserNo();
            var selSchoolNo = $("#whereSchoolNo").val();
            var action_id = $(this).prev().prev("input[name='ACTION_ID']").val();
            var bre_no = $(this).prev("input[name='BRE_NO']").val();
            var ischecked = $(this).is(":checked");

            var action_name = $(this).next("span").text();
            loaderShow();
             $.ajax({
                 url: '@Url.Content("~/ZZZI12/BatchSave")',
                 type: 'POST',
                 contentType: "application/json; charset=utf-8",
                 dataType: 'json',
                 data: JSON.stringify({
                     SCHOOL_NO: selSchoolNo,
                     USER_NO: usernos,
                     BRE_NO: bre_no,
                     ACTION_ID: action_id,
                     Checked: ischecked
                 }),
                 success: function (data) {
                     toastr.options = {
                         "positionClass": "toast-bottom-right",
                     };
                     
                     if (JSON.parse(data).Success == "false") {

                         toastr["error"](data.ErrorMsg);
                     } else {
                         if (ischecked) {
                             toastr["success"]("增加權限: " + action_name);
                         } else {
                             toastr["warning"](" 移除權限: " + action_name);
                         }
                     }
                     
                     
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    toastr["error"](errorThrown);
                },
                 complete: function () {
                     loaderHide();
                }
            });
        });
    </script>
}