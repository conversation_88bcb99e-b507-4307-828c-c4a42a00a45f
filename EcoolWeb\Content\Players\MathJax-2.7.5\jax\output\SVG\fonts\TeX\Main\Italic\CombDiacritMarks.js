/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Main/Italic/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Main-italic"],{768:[697,-500,0,-222,-74,"-222 651Q-222 668 -206 682T-174 697Q-155 697 -145 680Q-140 671 -107 599T-74 526Q-74 522 -88 511T-107 500Q-109 500 -113 502T-167 568T-219 637Q-222 643 -222 651"],769:[697,-500,0,-173,39,"-148 500Q-154 500 -163 511T-173 528Q-173 529 -172 530V532Q-170 534 -97 610T-21 688Q-8 697 4 697Q19 697 29 688T39 663T30 638Q26 631 -50 573L-135 507Q-144 500 -148 500"],770:[694,-527,0,-251,17,"17 555Q17 549 3 538T-15 527Q-20 527 -41 554Q-53 569 -62 580L-97 625L-158 578Q-172 567 -188 555T-212 536L-221 529Q-225 527 -226 527Q-232 527 -238 533T-248 546L-251 553Q-251 559 -248 562Q-246 564 -169 628T-90 693T-86 694Q-81 694 -78 691Q17 563 17 555"],771:[668,-558,0,-264,60,"-245 558Q-249 558 -256 565T-264 578Q-264 583 -261 586T-237 609Q-176 668 -142 668Q-117 668 -91 642T-47 615Q-26 615 5 641T39 668Q43 668 51 661T60 647Q59 643 40 623T-9 581T-60 559H-64Q-90 559 -115 585T-156 611Q-177 611 -210 585T-245 558"],772:[589,-544,0,-282,54,"-275 544Q-282 548 -282 554Q-282 561 -279 573T-271 588Q-269 589 -111 589H-27H12Q38 589 46 587T54 578Q54 574 51 563T47 550Q45 546 32 545Q15 544 -118 544H-275"],774:[694,-515,0,-237,62,"-237 641Q-237 694 -218 694H-213Q-195 694 -195 684Q-195 683 -195 679T-197 667T-198 650Q-198 611 -176 589T-117 566Q-74 566 -34 597T23 678Q27 689 30 691T43 694Q62 694 62 684Q62 671 49 645T14 589T-46 537T-123 515Q-175 515 -206 550T-237 641"],775:[669,-548,0,-165,-41,"-165 599Q-162 631 -139 650T-93 669Q-65 669 -53 653T-41 620Q-41 593 -63 571T-116 548Q-134 548 -149 559T-165 599"],776:[669,-554,0,-251,45,"-251 601Q-251 626 -230 647T-180 669Q-139 669 -133 625Q-133 595 -155 575T-203 554Q-223 554 -237 567T-251 601ZM-72 599Q-72 632 -48 650T-2 669Q18 669 31 657T45 623Q45 592 22 573T-25 554Q-68 554 -72 599"],778:[716,-542,0,-199,3,"-199 610Q-199 654 -161 685T-79 716Q-39 716 -16 693Q3 674 3 647Q3 607 -34 575T-118 542Q-199 542 -199 610ZM-41 631T-41 655T-83 679H-89Q-129 679 -142 656Q-146 650 -151 632T-156 604Q-156 578 -113 578H-108Q-94 578 -86 579T-69 586T-52 605Q-41 631 -41 655"],779:[697,-503,0,-248,65,"-217 503Q-221 503 -234 510T-248 523Q-248 528 -205 602Q-200 610 -192 623T-180 644T-170 661T-159 676T-151 686T-142 694T-134 696Q-132 697 -121 697Q-88 694 -88 664Q-88 652 -97 640T-152 574Q-214 504 -217 503ZM-64 503Q-68 503 -81 510T-95 523Q-95 528 -52 602Q-47 610 -39 623T-27 644T-17 661T-6 676T2 686T11 694T19 696Q21 697 32 697Q65 694 65 664Q65 652 56 640T1 574Q-61 504 -64 503"],780:[638,-502,0,-236,29,"11 637Q16 637 22 624T29 607Q29 606 27 602Q26 600 -47 552T-125 502H-127Q-133 502 -184 553Q-236 602 -236 608Q-236 612 -224 625T-206 638L-202 637L-196 632Q-190 628 -179 620T-158 603L-116 570Q-109 572 -52 604T11 637"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Italic/CombDiacritMarks.js");
