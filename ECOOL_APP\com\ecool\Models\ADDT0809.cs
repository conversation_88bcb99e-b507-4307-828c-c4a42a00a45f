﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.com.ecool.Models;
using System.Web;


namespace ECOOL_APP.com.ecool.Models
{
    public class ADDT0809
    {

        public bool Chk { get; set; }

        ///Summary
        ///流水號
        ///Summary
        [Display(Name = "流水號")]
        public string UPNO { get; set; }

        ///Summary
        ///學年
        ///Summary
        [Display(Name = "學年")]
        public string SYEAR { get; set; }

        ///Summary
        ///學年
        ///Summary
        [Display(Name = "學期")]
        public string SEMESTER { get; set; }

        ///Summary
        ///班級
        ///Summary
        [Display(Name = "班級")]
        public string CLASS_NO { get; set; }
        
        ///Summary
        ///座號
        ///Summary
        [Display(Name = "座號")]
        public string SEAT_NO { get; set; }

        ///Summary
        ///姓名
        ///Summary
        [Display(Name = "姓名")]
        public string USERNAME { get; set; }

        ///Summary
        ///簡稱
        ///Summary
        [Display(Name = "簡稱")]
        public string SNAME { get; set; }

        ///Summary
        ///閱讀本數
        ///Summary
        [Display(Name = "閱讀本數")]
        public Nullable<int> BOOK_QTY { get; set; }

        ///Summary
        ///等級名稱
        ///Summary
        [Display(Name = "等級名稱")]
        public string LEVEL_DESC { get; set; }

         ///Summary
        ///授權日期
        ///Summary
        [Display(Name = "授權日期")]
        public Nullable<System.DateTime> UP_DATE { get; set; }

        ///Summary
        ///狀態
        ///Summary
        [Display(Name = "狀態")]
        public string STATUS { get; set; }

        ///Summary
        ///學校代號
        ///Summary
        [Display(Name = "學校代號")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///帳號
        ///Summary
        [Display(Name = "帳號")]
        public string USER_NO { get; set; }

        ///Summary
        ///處理日期
        ///Summary
        [Display(Name = "處理日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? DP_DATE { get; set; }

        ///Summary
        ///處理人
        ///Summary
        [Display(Name = "處理人")]
        public string DP_PERSON { get; set; }
        public Nullable<int> ShareCountSUM { get; set; }

        ///Summary
        ///被推薦數
        ///Summary
        [Display(Name = "被推薦數")]
        public Nullable<int> ShareCount { get; set; }
        public int BOOK_QTYRANK { get; set; }
    }
}
