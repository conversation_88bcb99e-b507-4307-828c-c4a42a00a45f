﻿using DotNet.Highcharts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models
{
    public class SECI04IndexViewModel
    {

        public bool IsPrint { get; set; }


        /// <summary>
        /// 只顯示某學校
        /// </summary>
        public string whereSCHOOL_NO { get; set; }

        /// <summary>
        /// 只顯示某學年
        /// </summary>
        public byte? whereSYEAR { get; set; }


        /// <summary>
        /// 只顯示某一年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某學年入學
        /// </summary>
        public byte? whereInSYEAR { get; set; }



        public List<SECI04Hrmt08ListViewModel> Hrmt08List;

        public List<SECI04Hrmt08ListViewModel> VisionHrmt08List;



        /// <summary>
        /// 身高趨示圖
        /// </summary>
        public Highcharts TALLchart;

        /// <summary>
        /// 體重趨示圖
        /// </summary>
        public Highcharts WEIGHTchart;


        /// <summary>
        /// 體適能趨勢圖 坐姿體前彎
        /// </summary>
        public Highcharts FitnesschartV;

        /// <summary>
        /// 體適能趨勢圖 立定跳遠
        /// </summary>
        public Highcharts FitnesschartSL;

        /// <summary>
        /// 體適能趨勢圖 仰臥起坐
        /// </summary>
        public Highcharts FitnesschartSU;

        /// <summary>
        /// 體適能趨勢圖 800公尺跑走
        /// </summary>
        public Highcharts FitnesschartC;

        /// <summary>
        /// 視力 RIGHT 
        /// </summary>
        public Highcharts RIGHT_VisionChart;

        /// <summary>
        /// 視力 LEFT
        /// </summary>
        public Highcharts LEFT_VisionChart;
    }
}
