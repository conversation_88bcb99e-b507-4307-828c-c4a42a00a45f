﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI31DetailsViewModel
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@helper  buttonFun()
{
    if (Model.uQAT16 != null)
    {
        <div class="panel-footer text-center">
                @if (Model.uQAT16.STATUS != ECOOL_APP.com.ecool.Models.entity.uQAT16.STATUS_Val.STATUS_Z)
                {
                    @Html.PermissionButton("新增填報", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-default", @onclick = "onAdd('QUESTIONS')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                    @Html.PermissionButton("回覆", "button", (string)ViewBag.BRE_NO, "ANSWERS", new { @class = "btn btn-default", @onclick = "onAdd('ANSWERS')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)

                    if ((bool)TempData["boolAns"] == true)
                    {
                        @Html.PermissionButton("關閉填報", "button", (string)ViewBag.BRE_NO, "ANSWERS", new { @class = "btn btn-default", @onclick = "onAdd('CLOSE')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                    }
                    else
                    {
                        @Html.PermissionButton("關閉填報", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-default", @onclick = "onAdd('CLOSE')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                    }


                    @Html.PermissionButton("作廢此填報", "button", (string)ViewBag.BRE_NO, "ANSWERS", new { @class = "btn btn-default", @onclick = "onAdd('INVALID')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                }
        </div>
    }
}

@helper btnUpQ()
{
    if (ViewBag.USER_KEY == Model.uQAT16.CRE_PERSON && Model.uQAT16.STATUS != ECOOL_APP.com.ecool.Models.entity.uQAT16.STATUS_Val.STATUS_Z)
    {
        <button type="button" class="btn btn-xs btn-Basic" onclick="Save('QUESTIONS','','')" }>修改</button>
    }
}

@helper btnUpQAT17(string CRE_PERSON,int? ITEM_NO,string ANS_TYPE)
{
    if (ViewBag.USER_KEY == CRE_PERSON && Model.uQAT16.STATUS != ECOOL_APP.com.ecool.Models.entity.uQAT16.STATUS_Val.STATUS_Z)
    {
        <button type="button" class="btn btn-xs btn-Basic" onclick="Save('ANSWERS', '@ITEM_NO','@ANS_TYPE')">修改</button>
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.PermissionButton("回查詢列表", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-sm btn-sys", onclick = "Index()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)


@using (Html.BeginForm("Details", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.Search.Page)
    @Html.HiddenFor(model => model.Search.OrderByName)
    @Html.HiddenFor(model => model.Search.Q_QUESTIONS_ID)
    @Html.HiddenFor(model => model.Search.SearchContents)
    @Html.HiddenFor(model => model.Search.SyntaxName)
    @Html.HiddenFor(model => model.Search.DetailsPage)
    @Html.HiddenFor(model => model.Search.STATUS)




    @Html.HiddenFor(model => model.uQAT16.QUESTIONS_ID)
    @Html.Hidden("ITEM_NO")
    @Html.Hidden("ANS_TYPE")
    
}


<div class="panel panel-ZZZ">
    <div class="panel-heading text-center" name="TOP" style="background-color:rgba(68, 157, 68, 1);color:rgba(249, 242, 244, 1);text-shadow:0px 0px 0px #dddddd">
        @Html.BarTitle()
    </div>
    <div class="Caption_Div">
        主旨：@Html.DisplayFor(model => model.uQAT16.SUBJECT)
    </div>
    <div class="panel-body">
    

            <div class="table-responsive">
                <table class="table table-ecool-qa">
                    <thead>
                        <tr>
                            <td>
                                <div class="col-sm-8 text-left table-ecool-qa-Title">
                                    <h5><b>@Html.DisplayFor(model => model.uQAT16.SCHOOL_NAME) /
                                           @Html.DisplayFor(model=> model.uQAT16.SNAME)</b></h5>
                                            @Html.DisplayFor(model => model.uQAT16.CRE_DATE)
                                </div>
                                <div class="col-sm-4 text-right">
                                    @btnUpQ()
                                </div>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="table-ecool-qa-Title">
                                @Html.DisplayFor(model => model.uQAT16.QUESTIONS_TXT)
                                @if (Model.uQAT16.FILE_NAME != null && Model.uQAT16.FILE_NAME!=string.Empty )
                                {
                                    @Html.PermissionActionLink(Model.uQAT16.FILE_NAME, "DownLoad", (string)ViewBag.BRE_NO, new {  QUESTIONS_ID = Model.uQAT16.QUESTIONS_ID, name = Model.uQAT16.FILE_NAME }, new { @class = "btn btn-info glyphicon glyphicon-file" }, (string)ViewBag.BRE_NO, "Index", 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                                }
                               
                            </td>
                        </tr>
                      </tbody>
                    @foreach (var item in Model.uQAT17List)
                    {
                        <thead>
                            <tr>
                                <td>
                                    <div class="col-sm-8 text-left">
                                        <h5><b>
                                            @Html.DisplayFor(modelitem => item.SCHOOL_NAME) /
                                            @Html.DisplayFor(modelitem => item.SNAME)
                                            </b></h5>
                                        @Html.DisplayFor(modelitem => item.CRE_DATE)
                                    </div>
                                    <div class="col-sm-4 text-right">
                                        @btnUpQAT17(item.CRE_PERSON, item.ITEM_NO,item.ANS_TYPE)
                                    </div>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    @if (ViewBag.USER_KEY == Model.uQAT16.CRE_PERSON || ViewBag.USER_KEY == item.CRE_PERSON )
                                    {
                                        <text>@Html.DisplayFor(modelitem => item.ANSWERS)</text>

                                        if (item.FILE_NAME != null && item.FILE_NAME != string.Empty)
                                        {
                                            @Html.PermissionActionLink(item.FILE_NAME, "DownLoad", (string)ViewBag.BRE_NO, new { QUESTIONS_ID = item.QUESTIONS_ID, ITEM_NO = item.ITEM_NO, name = item.FILE_NAME }, new { @class = "btn btn-info glyphicon glyphicon-file" }, (string)ViewBag.BRE_NO, "Index", 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                                        }
                                    }
                                    else
                                    {
                                       <span style="color:gray">隱藏</span> 
                                    }
                                 

                                </td>
                            </tr>
                        </tbody>
                    }


                </table>
            
            </div>
    </div>
    @buttonFun()
</div>

<div>
    @Html.Pager(Model.uQAT17List.PageSize, Model.uQAT17List.PageNumber, Model.uQAT17List.TotalItemCount).Options(o => o
          .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
      )
</div>





@section Scripts {
    <script language="JavaScript">

        function FunPageProc(pageno) {
            $('#Search_DetailsPage').val(pageno);
            form1.submit();
        }

        function Index() {
            form1.action = '@Html.Raw(@Url.Action("Index", (string)ViewBag.BRE_NO))'
            
            form1.submit();
        }

        function onAdd(Val) {

            $('#ITEM_NO').val("")

            if (Val == "QUESTIONS") {
                $('#ANS_TYPE').val("@ECOOL_APP.com.ecool.Models.entity.uQAT17.ANS_TYPE_Val.ANS_TYPE_Q")
                form1.action = '@Url.Action("Add_QUESTIONS", (string)ViewBag.BRE_NO)';
            }
            else if (Val == "ANSWERS") {
                $('#ANS_TYPE').val("@ECOOL_APP.com.ecool.Models.entity.uQAT17.ANS_TYPE_Val.ANS_TYPE_A")
                form1.action = '@Url.Action("ANSWERS", (string)ViewBag.BRE_NO)';
            }
            else if (Val == "CLOSE") {
                form1.action = '@Url.Action("CLOSE", (string)ViewBag.BRE_NO)';
            }
            else if (Val == "INVALID") {
                form1.action = '@Url.Action("INVALID", (string)ViewBag.BRE_NO)';
            }

            form1.submit();

        }


        function Save(Val, Item_no, ANS_TYPE) {


            var QUESTIONS_ID = $('#uQAT16_QUESTIONS_ID').val()
            $('#Search_Q_QUESTIONS_ID').val(QUESTIONS_ID)
            $('#ITEM_NO').val(Item_no)

            if (Val == "QUESTIONS") {
                form1.action = '@Html.Raw(@Url.Action("QUESTIONS", (string)ViewBag.BRE_NO))'
            }
            else if (Val == "ANSWERS") {
                $('#ANS_TYPE').val(ANS_TYPE)
                form1.action = '@Url.Action("ANSWERS", (string)ViewBag.BRE_NO)';
            }


            form1.submit();
        }
    </script>
}