// JavaScript for ADDI03 MaintainOneApple page
$(document).ready(function() {
    // 書號調整模組
    const maintainOneApple = {
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.enhanceUserExperience();
        },

        bindEvents: function() {
            // 綁定按鈕事件
            $('#btnSend').on('click', this.handleSubmit.bind(this));
            $('#btnCancel').on('click', this.handleCancel.bind(this));
            
            // 綁定下拉選單變更事件
            $('#NewBookID').on('change', this.handleBookIdChange.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.btnCancel_onclick = this.handleCancel.bind(this);
            window.btnSend_onclick = this.handleSubmit.bind(this);
        },

        handleCancel: function() {
            try {
                const form = document.contentForm;
                if (form) {
                    form.enctype = "multipart/form-data";
                    form.action = window.ADDI03_MAINTAIN_URLS.query3;
                    form.submit();
                } else {
                    console.error('找不到表單元素');
                    alert('系統錯誤：找不到表單元素');
                }
            } catch (error) {
                console.error('取消操作時發生錯誤:', error);
                alert('系統發生錯誤，請稍後再試');
            }
        },

        handleSubmit: function() {
            try {
                // 驗證表單
                const validationResult = this.validateForm();
                if (!validationResult.isValid) {
                    alert(validationResult.message);
                    return;
                }

                // 確認提交
                const newBookId = $('#NewBookID').val();
                let confirmMessage = '請確認要進行以下操作：\n\n';
                
                if (newBookId === 'del') {
                    confirmMessage += '刪除此書號記錄';
                } else {
                    confirmMessage += '將書號調整為：' + $('#NewBookID option:selected').text();
                }

                if (confirm(confirmMessage)) {
                    this.submitForm();
                }
            } catch (error) {
                console.error('提交表單時發生錯誤:', error);
                alert('系統發生錯誤，請稍後再試');
            }
        },

        validateForm: function() {
            const newBookId = $('#NewBookID').val();
            
            if (!newBookId || newBookId === '') {
                return {
                    isValid: false,
                    message: '請選擇新書號'
                };
            }

            return {
                isValid: true,
                message: ''
            };
        },

        submitForm: function() {
            const form = document.contentForm;
            if (form) {
                form.enctype = "multipart/form-data";
                form.action = window.ADDI03_MAINTAIN_URLS.maintainOneAppleOK;
                form.submit();
            } else {
                console.error('找不到表單元素');
                alert('系統錯誤：找不到表單元素');
            }
        },

        handleBookIdChange: function() {
            const selectedValue = $('#NewBookID').val();
            const selectedText = $('#NewBookID option:selected').text();
            
            // 提供視覺反饋
            if (selectedValue === 'del') {
                $('#NewBookID').addClass('text-danger');
                this.showMessage('注意：選擇此選項將刪除書號記錄', 'warning');
            } else {
                $('#NewBookID').removeClass('text-danger');
                if (selectedValue && selectedValue !== '') {
                    this.showMessage('已選擇：' + selectedText, 'info');
                }
            }
        },

        enhanceUserExperience: function() {
            // 添加載入狀態
            $('#btnSend').on('click', function() {
                $(this).prop('disabled', true).text('處理中...');
                setTimeout(() => {
                    $(this).prop('disabled', false).text('確定送出');
                }, 3000);
            });

            // 添加表單元素焦點效果
            $('.form-control').on('focus', function() {
                $(this).closest('.form-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.form-group').removeClass('focused');
            });

            // 添加下拉選單提示
            $('#NewBookID').attr('title', '請選擇要調整的新書號，或選擇刪除');
        },

        showMessage: function(message, type = 'info') {
            // 移除現有訊息
            $('.alert-message').remove();
            
            // 添加新訊息
            const alertClass = type === 'warning' ? 'alert-warning' : 
                              type === 'error' ? 'alert-danger' : 'alert-info';
            
            const messageHtml = `
                <div class="alert ${alertClass} alert-message" style="margin-top: 10px;">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    ${message}
                </div>
            `;
            
            $('#NewBookID').closest('.form-group').after(messageHtml);
            
            // 3秒後自動隱藏
            setTimeout(() => {
                $('.alert-message').fadeOut();
            }, 3000);
        },

        // 錯誤處理
        handleError: function(error, context = '') {
            console.error(`${context}發生錯誤:`, error);
            
            let errorMessage = '系統發生錯誤，請稍後再試';
            if (error.message) {
                errorMessage = error.message;
            }
            
            this.showMessage(errorMessage, 'error');
        }
    };

    // 初始化模組
    maintainOneApple.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});
