/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/NonUnicode/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_NonUnicode={directory:"NonUnicode/Regular",family:"AsanaMathJax_NonUnicode",testString:"\uE000\uE001\uE002\uE003\uE004\uE005\uE006\uE007\uE008\uE009\uE00A\uE00B\uE00C\uE00D\uE00E",32:[0,0,249,0,0],57344:[705,3,751,28,724],57345:[692,6,610,26,559],57346:[706,18,687,45,651],57347:[692,3,777,28,741],57348:[692,3,610,30,570],57349:[692,3,555,0,548],57350:[706,18,721,50,694],57351:[692,3,777,-3,800],57352:[692,3,332,7,354],57353:[692,206,332,-35,358],57354:[692,3,666,13,683],57355:[692,3,555,16,523],57356:[698,18,953,-19,950],57357:[692,11,777,2,802],57358:[706,18,777,53,748],57359:[692,3,610,9,594],57360:[706,201,777,53,748],57361:[691,3,666,9,639],57362:[706,18,555,42,506],57363:[692,3,610,53,635],57364:[692,19,777,88,798],57365:[692,8,721,75,754],57366:[700,8,943,71,980],57367:[692,3,721,20,734],57368:[705,3,666,52,675],57369:[692,3,666,20,637],57370:[482,11,443,4,406],57371:[733,11,462,37,433],57372:[482,11,406,25,389],57373:[733,11,499,17,483],57374:[483,11,388,15,374],57375:[733,276,550,-25,550],57376:[482,276,499,-37,498],57377:[733,9,499,10,471],57378:[712,9,277,34,264],57379:[712,276,277,-70,265],57380:[733,9,468,14,455],57381:[733,9,277,36,251],57382:[482,9,777,24,740],57383:[482,9,555,24,514],57384:[482,11,443,17,411],57385:[482,276,499,-7,465],57386:[482,276,462,24,432],57387:[482,9,388,26,384],57388:[482,11,388,9,345],57389:[646,9,332,41,310],57390:[482,11,555,32,512],57391:[482,11,499,21,477],57392:[482,11,721,21,699],57393:[482,11,499,9,484],57394:[482,276,499,-8,490],57395:[482,11,443,-1,416],57396:[705,3,751,28,724],57397:[692,6,610,26,559],57398:[706,18,687,45,651],57399:[692,3,777,28,741],57400:[692,3,610,30,570],57401:[692,3,555,0,548],57402:[706,18,721,50,694],57403:[692,3,777,-3,800],57404:[692,3,332,7,354],57405:[692,206,332,-35,358],57406:[692,3,666,13,683],57407:[692,3,555,16,523],57408:[698,18,953,-19,950],57409:[692,11,777,2,802],57410:[706,18,777,53,748],57411:[692,3,610,9,594],57412:[706,201,777,53,748],57413:[691,3,666,9,639],57414:[706,18,555,42,506],57415:[692,3,610,53,635],57416:[692,19,777,88,798],57417:[692,8,721,75,754],57418:[700,8,943,71,980],57419:[692,3,721,20,734],57420:[705,3,666,52,675],57421:[692,3,666,20,637],57422:[482,11,443,4,406],57423:[733,11,462,37,433],57424:[482,11,406,25,389],57425:[733,11,499,17,483],57426:[483,11,388,15,374],57427:[733,276,550,-25,550],57428:[482,276,499,-37,498],57429:[733,9,499,10,471],57430:[712,9,277,34,264],57431:[712,276,277,-70,265],57432:[733,9,468,14,455],57433:[733,9,277,36,251],57434:[482,9,777,24,740],57435:[482,9,555,24,514],57436:[482,11,443,17,411],57437:[482,276,499,-7,465],57438:[482,276,462,24,432],57439:[482,9,388,26,384],57440:[482,11,388,9,345],57441:[646,9,332,41,310],57442:[482,11,555,32,512],57443:[482,11,499,21,477],57444:[482,11,721,21,699],57445:[482,11,499,9,484],57446:[482,276,499,-8,490],57447:[482,11,443,-1,416],57448:[689,20,499,29,465],57449:[694,3,499,60,418],57450:[689,3,499,16,468],57451:[689,20,499,15,462],57452:[694,3,499,2,472],57453:[689,20,499,13,459],57454:[689,20,499,32,468],57455:[689,3,499,44,497],57456:[689,20,499,30,464],57457:[689,20,499,20,457],57458:[689,20,499,29,465],57459:[694,3,499,60,418],57460:[689,3,499,16,468],57461:[689,20,499,15,462],57462:[694,3,499,2,472],57463:[689,20,499,13,459],57464:[689,20,499,32,468],57465:[689,3,499,44,497],57466:[689,20,499,30,464],57467:[689,20,499,20,457],57468:[469,12,499,32,471],57469:[726,27,552,-15,508],57470:[497,20,443,25,413],57471:[726,12,610,34,579],57472:[469,20,478,26,448],57473:[737,3,332,23,341],57474:[469,283,555,32,544],57475:[726,3,581,6,572],57476:[687,3,290,21,271],57477:[688,283,233,-40,167],57478:[726,12,555,21,549],57479:[726,3,290,21,271],57480:[469,3,882,16,869],57481:[469,3,581,6,572],57482:[469,20,545,32,514],57483:[476,281,600,8,556],57484:[477,281,600,44,593],57485:[469,3,394,21,374],57486:[469,20,423,30,391],57487:[621,12,325,22,319],57488:[469,12,602,18,581],57489:[459,7,564,6,539],57490:[469,7,833,6,808],57491:[469,3,515,20,496],57492:[459,283,555,12,544],57493:[462,3,499,16,466],57494:[469,12,499,32,471],57495:[726,27,552,-15,508],57496:[497,20,443,25,413],57497:[726,12,610,34,579],57498:[469,20,478,26,448],57499:[737,3,332,23,341],57500:[469,283,555,32,544],57501:[726,3,581,6,572],57502:[687,3,290,21,271],57503:[688,283,233,-40,167],57504:[726,12,555,21,549],57505:[726,3,290,21,271],57506:[469,3,882,16,869],57507:[469,3,581,6,572],57508:[469,20,545,32,514],57509:[476,281,600,8,556],57510:[477,281,600,44,593],57511:[469,3,394,21,374],57512:[469,20,423,30,391],57513:[621,12,325,22,319],57514:[469,12,602,18,581],57515:[459,7,564,6,539],57516:[469,7,833,6,808],57517:[469,3,515,20,496],57518:[459,283,555,12,544],57519:[462,3,499,16,466],57520:[689,4,870,18,852],57521:[704,6,824,34,791],57522:[692,3,555,22,536],57523:[697,4,688,27,662],57524:[709,20,785,22,764],57525:[697,4,753,17,729],57526:[689,4,692,42,651],57527:[689,4,812,25,788],57528:[689,4,684,45,643],57529:[691,4,733,3,735],57530:[689,4,755,23,723],57531:[692,3,555,22,536],57532:[697,4,688,27,662],57533:[709,20,785,22,764],57534:[697,4,753,17,729],57535:[689,4,692,42,651],57536:[689,4,812,25,788],57537:[689,4,684,45,643],57538:[691,4,733,3,735],57539:[689,4,755,23,723],57540:[689,4,870,18,852],57541:[704,6,824,34,791],57542:[700,3,777,15,756],57543:[692,3,610,26,576],57544:[709,20,708,22,670],57545:[692,3,773,22,751],57546:[692,3,610,22,572],57547:[692,3,555,22,536],57548:[709,20,762,22,728],57549:[692,3,831,22,810],57550:[692,3,336,22,315],57551:[692,194,347,0,326],57552:[692,3,725,22,719],57553:[692,3,610,22,586],57554:[692,13,945,16,926],57555:[692,6,830,17,813],57556:[709,20,785,22,764],57557:[692,3,603,22,580],57558:[709,176,785,22,764],57559:[692,3,667,22,669],57560:[709,20,524,24,503],57561:[692,3,612,18,595],57562:[692,20,777,12,759],57563:[692,9,721,8,706],57564:[700,9,1000,8,984],57565:[700,3,666,14,648],57566:[705,3,666,9,654],57567:[692,3,666,15,638],57568:[700,3,777,15,756],57569:[692,3,610,26,576],57570:[709,20,708,22,670],57571:[692,3,773,22,751],57572:[692,3,610,22,572],57573:[692,3,555,22,536],57574:[709,20,762,22,728],57575:[692,3,831,22,810],57576:[692,3,336,22,315],57577:[692,194,347,0,326],57578:[692,3,725,22,719],57579:[692,3,610,22,586],57580:[692,13,945,16,926],57581:[692,6,830,17,813],57582:[691,2,765,41,745],57583:[692,3,603,22,580],57584:[709,176,785,22,764],57585:[692,3,667,22,669],57586:[709,20,524,24,503],57587:[692,3,612,18,595],57588:[692,20,777,12,759],57589:[692,9,721,8,706],57590:[700,9,1000,8,984],57591:[700,3,666,14,648],57592:[705,3,666,9,654],57593:[692,3,666,15,638],57594:[713,15,448,19,462],57595:[713,13,438,16,455],57596:[701,13,562,30,492],57597:[702,15,620,62,583],57598:[485,277,681,8,606],57599:[431,0,524,78,455],57600:[431,228,524,48,368],57601:[451,0,401,80,370],57602:[451,227,301,-110,305],57603:[444,0,237,81,250],57604:[444,205,265,-97,278],57605:[458,0,254,61,194],57606:[458,205,285,-71,224],57607:[444,0,237,81,156],57608:[444,205,265,-61,184],57609:[540,11,312,22,302],57610:[539,168,322,35,289],57611:[442,0,388,42,346],57612:[442,210,409,-35,316],57613:[501,4,270,32,258],57614:[504,169,270,32,229],57615:[431,19,444,55,394],57616:[431,307,870,55,820],57617:[451,22,455,41,391],57618:[451,343,943,41,869],57619:[469,17,332,26,293],57620:[469,271,332,-64,274],57621:[471,3,332,34,298],57622:[471,266,332,3,227],57623:[469,283,233,-40,159]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_NonUnicode"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/NonUnicode/Regular/Main.js"]);
