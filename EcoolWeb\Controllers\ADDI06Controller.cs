﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Net;
using System.IO;
using System.Data.Entity.Validation;
using System.Data.Entity;
using com.ecool.service;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Transactions;
using ECOOL_APP.com.ecool.util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI06Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ADDI06";

        public static string ImgPath = "ADDT14IMG";

        /// <summary>
        /// 校內表現
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult QUERY(ADDI06IndexViewModel model)
        {
           
            if (TempData["fromSOurce"] != null && TempData["fromSOurce"].ToString().Contains("ADDI06") == false)
            {
                return Redirect(TempData["fromSOurce"].ToString());
            }
            if (model == null) model = new ADDI06IndexViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }
            int StudentUploadIMG = 0;
            //IQueryable<ADDT14> ADDT14List = db.ADDT14.Where(a => a.SCHOOL_NO == SchoolNO && a.APPLY_STATUS != "9");
            StudentUploadIMG = db.BDMT02_REF.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.BRE_NO == "ADDI06" && x.DATA_CODE == "IMAGE_Student" && x.CONTENT_VAL == "true").Count();
            if (StudentUploadIMG > 0)
            {
                ViewBag.StudentUploadIMG = true;
            }
            else
            {
                ViewBag.StudentUploadIMG = false;
            }
            GetAddt14(model, SchoolNO);

            string UseYN = "N";
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "ADD", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableInsert = false;
            }
            else
            {
                ViewBag.VisableInsert = true;
            }
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "Delete", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableDelete = false;
            }
            else
            {
                ViewBag.VisableDelete = true;
            }
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "MODIFY", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableModify = false;
            }
            else
            {
                ViewBag.VisableModify = true;
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            return View(model);
        }

        static public void GetAddt14(ADDI06IndexViewModel model, string SchoolNO, int PageSize = 20)
        {
            IQueryable<ADDT14> ADDT14List = null;
            List<ImageModifyTemp> ImageModifyTempInfo = null;
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ADDT14List =
                           from a14 in db.ADDT14
                           join h01 in db.HRMT01 on new { a14.USER_NO, a14.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                           from h01 in h01_join.DefaultIfEmpty()
                           where a14.SCHOOL_NO == SchoolNO && a14.APPLY_STATUS != "9" && h01.USER_STATUS != UserStaus.Disable && h01.USER_STATUS != UserStaus.Invalid && a14.IAWARD_ITEM != "生日禮有取消"
                           select a14;

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    ADDT14List = ADDT14List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                    || a.USERNAME.Contains(model.whereKeyword.Trim())
                    || a.TNAME.Contains(model.whereKeyword.Trim())
                    || a.IAWARD_KIND.Contains(model.whereKeyword.Trim())
                    || a.IAWARD_ITEM.Contains(model.whereKeyword.Trim())
                    );
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO == model.whereCLASS_NO.Trim()).Select(a => a.USER_NO).ToList();
                    ADDT14List = ADDT14List.Where(a => HrList.Contains(a.USER_NO));
                    PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO.Substring(0, 1) == model.whereGrade).Select(a => a.USER_NO).ToList();
                    ADDT14List = ADDT14List.Where(a => HrList.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
                {
                    var arrUSER_NO = model.whereUserNo.Split(',');
                    ADDT14List = ADDT14List.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);
                }

                if (string.IsNullOrWhiteSpace(model.ClassKind) == false)
                {
                    ADDT14List = ADDT14List.Where(a => a.IAWARD_KIND.Contains("班級") || a.IAWARD_KIND.Contains("幹部加點"));
                }
                else
                {
                    ADDT14List = ADDT14List.Where(a => a.IAWARD_KIND.Contains("班級") == false && a.IAWARD_KIND.Contains("幹部加點") == false);
                }

                switch (model.OrdercColumn)
                {
                    case "CREATEDATE":
                        ADDT14List = ADDT14List.OrderByDescending(a => a.CREATEDATE);
                        break;

                    case "CLASS_NO":
                        ADDT14List = ADDT14List.OrderByDescending(a => a.CLASS_NO);
                        break;

                    case "USERNAME":
                        ADDT14List = ADDT14List.OrderByDescending(a => a.USERNAME);
                        break;

                    default:
                        ADDT14List = ADDT14List.OrderByDescending(a => a.CREATEDATE);
                        break;
                }

                model.ADDT14List = ADDT14List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
                foreach (var item in model.ADDT14List.Where(x=>x.ImgSourceNO!= null).ToList())
                {
                    ImageModifyTempInfo = new List<ImageModifyTemp>();

                       ImageModifyTemp Images = new ImageModifyTemp();
                    Images = db.ImageModifyTemp.Where(x => x.Source_ID == item.ImgSourceNO).FirstOrDefault();
                    ImageModifyTempInfo.Add(Images);


                }
                if (ImageModifyTempInfo != null) { 
                model.ImageModifyTempList = ImageModifyTempInfo;
                }
            }
        }

        public ActionResult PrintQuery(ADDI06IndexViewModel model)
        {
            if (model == null) model = new ADDI06IndexViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            GetAddt14(model, SchoolNO, int.MaxValue);

            return View(model);
        }

        [CheckPermission] //檢查權限
        public ActionResult ADD()
        {
            var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);

            ViewBag.ClassNoItem = items;

            ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND();

            ViewBag.ClassNoItem = items;

            ADDT14 aDDT14 = new ADDT14();

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10 };

            //預設值
            aDDT14.CASH = 0;
            //aDDT01.SHARE_YN = "n";

            return View(aDDT14);
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ADD([Bind(Include = "IAWARD_ID,SCHOOL_NO,CREATEDATE,SYEAR,SEMESTER,SNAME,CLASS_NO,USER_NO,USERNAME,SEX,IAWARD_KIND,IAWARD_ITEM,CASH,REMARK")] ADDT14 aDDT14, HttpPostedFileBase files)
        {
            string Message = string.Empty;
            string BATCH_ID = PushService.CreBATCH_ID();

            if (files != null && files.ContentLength > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                }
            }

            if (ModelState.IsValid)
            {
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                aDDT14.SCHOOL_NO = user.SCHOOL_NO;
                aDDT14.CREATEDATE = DateTime.Now;
                aDDT14.SEMESTER = Convert.ToByte(Semesters);
                aDDT14.SYEAR = Convert.ToByte(SYear);
                aDDT14.TNAME = user.NAME;
                aDDT14.APPLY_STATUS = "2";

                var hr = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == aDDT14.USER_NO).FirstOrDefault();

                aDDT14.USERNAME = hr.NAME;
                aDDT14.SNAME = hr.SNAME;

                db.ADDT14.Add(aDDT14);

                using (TransactionScope tx = new TransactionScope())
                {
                    try
                    {
                        db.SaveChanges();
                    }
                    catch (DbEntityValidationException ex)
                    {
                        var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                        var getFullMessage = string.Join("; ", entityError);
                        var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);

                        TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
                        return View(aDDT14);
                    }

                    if (files != null && files.ContentLength >= 0)
                    {
                        if (FileHelper.DoLoadFile(files, new FileHelper().GetMapPathImg(aDDT14.SCHOOL_NO, ImgPath), ref Message, true, aDDT14.IAWARD_ID.ToString()) == false)
                        {
                            TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + Message;
                            return View(aDDT14);
                        }

                        aDDT14.IMG_FILE = Path.GetFileName(files.FileName);
                    }
                    List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                    ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(aDDT14.CASH), aDDT14.SCHOOL_NO, aDDT14.USER_NO, "ADDI06", aDDT14.IAWARD_ID.ToString(), StringHelper.LeftStringR("校內表現 - " + aDDT14.IAWARD_ITEM, 47), true, ref db, "", "",ref valuesList);

                    GrePushData(BATCH_ID, aDDT14, ref db);

                    try
                    {
                        db.SaveChanges();
                        TempData["StatusMessage"] = "新增完成";
                    }
                    catch (DbEntityValidationException ex)
                    {
                        var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                        var getFullMessage = string.Join("; ", entityError);
                        var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);

                        TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
                        return View(aDDT14);
                    }

                    tx.Complete();
                }

                PushHelper.ToPushServer(BATCH_ID);

                return RedirectToAction("Query");
            }

            var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO, Selected = x.CLASS_NO == aDDT14.CLASS_NO }).Distinct().OrderBy(o => o.Value);

            ViewBag.ClassNoItem = items;

            ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND(aDDT14.IAWARD_KIND);

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10 };

            return View(aDDT14);
        }

        public void GrePushData(string BATCH_ID, ADDT14 aDDT14, ref ECOOL_DEVEntities db)
        {
            string BODY_TXT = "校內表現，獎懲類別:" + aDDT14.IAWARD_KIND + "，具體事蹟:" + aDDT14.IAWARD_ITEM + "，獎勵點數:" + aDDT14.CASH.ToString();

            PushService.InsertPushDataParents(BATCH_ID, aDDT14.SCHOOL_NO, aDDT14.USER_NO, "", BODY_TXT, "", Bre_NO, "ADD", aDDT14.IAWARD_ID.ToString(), "", false, ref db);
            PushService.InsertPushDataMe(BATCH_ID, aDDT14.SCHOOL_NO, aDDT14.USER_NO, "", BODY_TXT, "", Bre_NO, "ADD", aDDT14.IAWARD_ID.ToString(), "", false, ref db);
        }
        [HttpPost]
 
        [ValidateAntiForgeryToken]
        public ActionResult MODIFY1([Bind(Include = "IAWARD_ID,SCHOOL_NO,USER_NO,IAWARD_KIND,IAWARD_ITEM,REMARK,CASH")] ADDT14 aDDT14, HttpPostedFileBase files)
        {
            string Message = string.Empty;

            if (files != null && files.ContentLength > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                }
            }

            if (ModelState.IsValid == false) return View(aDDT14);

            ADDT14 oldADDT14 = db.ADDT14.Find(aDDT14.IAWARD_ID);

            if (aDDT14 == null)
            {
                return HttpNotFound();
            }


            oldADDT14.UPDATEDATE = DateTime.Now;
            ImageModifyTemp temp = new ImageModifyTemp();
            if (files != null && files.ContentLength >= 0)
            {
                if (FileHelper.DoLoadFile(files, new FileHelper().GetMapPathImg(oldADDT14.SCHOOL_NO, ImgPath), ref Message, true, oldADDT14.IAWARD_ID.ToString()) == false)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + Message;
                    return RedirectToAction("Query");
                }
                temp.Source_ID = Guid.NewGuid().ToString("N");
                temp.SCHOOL_NO = user.SCHOOL_NO;
                temp.USER_NO = user.USER_NO;
                temp.Source = "ADDT14";
                temp.CHG_DATE = DateTime.Now;
                temp.Action = "Modify";
                db.ImageModifyTemp.Add(temp);
          
                oldADDT14.ImgSourceNO = temp.Source_ID;
                oldADDT14.IMG_FILE = Path.GetFileName(files.FileName);
            }

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "修改完成";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }
            ADDI06IndexViewModel model = new ADDI06IndexViewModel();
            model.whereUserNo = user.USER_NO;
            return RedirectToAction("QUERY" ,new { whereUserNo = user.USER_NO } );
        }
        [HttpPost]
        [Display(Name = "IAWARD_ID")]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult MODIFY([Bind(Include = "IAWARD_ID,SCHOOL_NO,USER_NO,IAWARD_KIND,IAWARD_ITEM,REMARK,CASH")] ADDT14 aDDT14, HttpPostedFileBase files)
        {
            string Message = string.Empty;
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (files != null && files.ContentLength > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                }
            }

            if (ModelState.IsValid == false) return View(aDDT14);

            ADDT14 oldADDT14 = db.ADDT14.Find(aDDT14.IAWARD_ID);
      
            if (aDDT14 == null)
            {
                return HttpNotFound();
            }
            if (aDDT14.CASH != oldADDT14.CASH)
            {
                ECOOL_APP.CashHelper.AddCash(user, -(Convert.ToInt32(oldADDT14.CASH)), oldADDT14.SCHOOL_NO, oldADDT14.USER_NO, "ADDI06", oldADDT14.IAWARD_ID.ToString(), "校內表現-修改", true, ref db, "", "",ref valuesList);
                db.SaveChanges();
                string BATCH_ID = PushService.CreBATCH_ID();
                string BODY_TXT = "校內表現被修改，具體事蹟: " + oldADDT14.IAWARD_ITEM + "-、減少酷幣點數" + (oldADDT14.CASH).ToString() + "數";
                PushService.InsertPushDataMe(BATCH_ID, oldADDT14.SCHOOL_NO, oldADDT14.USER_NO, "", BODY_TXT, "", "ADDI06", "Delete", oldADDT14.IAWARD_ID.ToString(), "", false, ref db);
                PushHelper.ToPushServer(BATCH_ID);
                ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(aDDT14.CASH), oldADDT14.SCHOOL_NO, oldADDT14.USER_NO, "ADDI06", oldADDT14.IAWARD_ID.ToString(), "校內表現-修改", true, ref db, "", "", ref valuesList);
                db.SaveChanges();
                BATCH_ID = PushService.CreBATCH_ID();
                string BODY_TXTafter = "校內表現被修改，具體事蹟: " + oldADDT14.IAWARD_ITEM + "-、修改後的幣點數" + (oldADDT14.CASH).ToString() + "數";
                PushService.InsertPushDataMe(BATCH_ID, aDDT14.SCHOOL_NO, aDDT14.USER_NO, "", BODY_TXTafter, "", "ADDI06", "ADD", aDDT14.IAWARD_ID.ToString(), "", false, ref db);
                PushHelper.ToPushServer(BATCH_ID);
                oldADDT14.CASH = aDDT14.CASH;
            }

            oldADDT14.IAWARD_ITEM = aDDT14.IAWARD_ITEM;
            oldADDT14.IAWARD_KIND = aDDT14.IAWARD_KIND;
            oldADDT14.REMARK = aDDT14.REMARK;
            oldADDT14.UPDATEDATE = DateTime.Now;
            ImageModifyTemp temp = new ImageModifyTemp();
            if (files != null && files.ContentLength >= 0)
            {
                if (FileHelper.DoLoadFile(files, new FileHelper().GetMapPathImg(oldADDT14.SCHOOL_NO, ImgPath), ref Message, true, oldADDT14.IAWARD_ID.ToString()) == false)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + Message;
                    return RedirectToAction("Query");
                }
                
                temp.Source_ID = Guid.NewGuid().ToString("N");
                temp.SCHOOL_NO = user.SCHOOL_NO;
                temp.USER_NO = user.USER_NO;
                temp.Source = "ADDT14";
                temp.CHG_DATE = DateTime.Now;
                temp.Action = "Modify";
                db.ImageModifyTemp.Add(temp);
                oldADDT14.IMG_FILE = Path.GetFileName(files.FileName);
                oldADDT14.ImgSourceNO = temp.Source_ID;
            }

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "修改完成";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }

            ;
            return RedirectToAction("QUERY");
        }

        // GET: ADDI14/MODIFY/5
        public ActionResult MODIFY(int? IAWARD_ID)
        {
            if (IAWARD_ID == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            if (Request.UrlReferrer?.ToString().Contains("ADDI06") == false)
            {
                ViewBag.backUrl = Request.UrlReferrer?.ToString();
            }
            //ADDT14 aDDT14 = db.ADDT14.Find(id);
            ADDT14 aDDT14 = db.ADDT14.Where(p => p.IAWARD_ID == IAWARD_ID).FirstOrDefault();

            ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND(aDDT14.IAWARD_KIND);

            if (aDDT14 == null)
            {
                return HttpNotFound();
            }

            ViewBag.IMG_FILE = new FileHelper().GetDirectorySysPathImg(aDDT14.SCHOOL_NO, ImgPath, aDDT14.IAWARD_ID.ToString(), aDDT14.IMG_FILE);

            return View(aDDT14);
        }

        // GET: ADDI14/MODIFY/5
        public ActionResult MODIFY1(int? IAWARD_ID)
        {
            if (IAWARD_ID == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            if (Request.UrlReferrer?.ToString().Contains("ADDI06") == false)
            {
                ViewBag.backUrl = Request.UrlReferrer?.ToString();
            }
            //ADDT14 aDDT14 = db.ADDT14.Find(id);
            ADDT14 aDDT14 = db.ADDT14.Where(p => p.IAWARD_ID == IAWARD_ID).FirstOrDefault();

            ViewBag.IAWARD_KIND = IawardKind.GetIAWARD_KIND(aDDT14.IAWARD_KIND);

            if (aDDT14 == null)
            {
                return HttpNotFound();
            }

            ViewBag.IMG_FILE = new FileHelper().GetDirectorySysPathImg(aDDT14.SCHOOL_NO, ImgPath, aDDT14.IAWARD_ID.ToString(), aDDT14.IMG_FILE);

            return View(aDDT14);
        }
        // GET: ADDI14/Delete/5
        public ActionResult Delete(int? IAWARD_ID)
        {
            if (IAWARD_ID == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT14 aDDT14 = db.ADDT14.Where(p => p.IAWARD_ID == IAWARD_ID).FirstOrDefault();
            if (aDDT14 == null)
            {
                return HttpNotFound();
            }
            return View(aDDT14);
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult Delete(int IAWARD_ID)
        {
            string BATCH_ID = PushService.CreBATCH_ID();
            AWAT01_LOG aWAT01_LOGtemp = new AWAT01_LOG();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            ADDT38 aDDT38TEMP = new ADDT38();
            ADDT14 delaDDT14 = db.ADDT14.Where(p => p.IAWARD_ID == IAWARD_ID).FirstOrDefault();
            delaDDT14.APPLY_STATUS = "9";
            db.Entry(delaDDT14).State = EntityState.Modified;
            if (delaDDT14.IAWARD_From == "紙本酷幣點數")
            {


                aWAT01_LOGtemp = db.AWAT01_LOG.Where(x => x.SCHOOL_NO == delaDDT14.SCHOOL_NO && x.USER_NO == delaDDT14.USER_NO && x.SOURCE_NO == delaDDT14.BATCH_CASH_ID).FirstOrDefault();
                if (aWAT01_LOGtemp != null)
                {

                    aDDT38TEMP = db.ADDT38.Where(x => x.SCHOOL_NO == delaDDT14.SCHOOL_NO && x.USER_NO == delaDDT14.USER_NO && x.BATCH_CASH_ID == delaDDT14.BATCH_CASH_ID).FirstOrDefault();

                    aDDT38TEMP.IsDel = "y";
                }

            }
            try
            {
                ECOOL_APP.CashHelper.AddCash(user, -(Convert.ToInt32(delaDDT14.CASH)), delaDDT14.SCHOOL_NO, delaDDT14.USER_NO, "ADDI06", delaDDT14.IAWARD_ID.ToString(), "校內表現-作廢", true, ref db, "", "",ref valuesList);
                db.SaveChanges();

                string BODY_TXT = "校內表現被作廢，具體事蹟: " + delaDDT14.IAWARD_ITEM + "-、減少酷幣點數" + (delaDDT14.CASH).ToString() + "數";

                //  PushService.InsertPushDataParents(BATCH_ID, delaDDT14.SCHOOL_NO, delaDDT14.USER_NO, "", BODY_TXT, "", "ADDI06", "Delete", delaDDT14.IAWARD_ID.ToString(), "", false, ref db);
                PushService.InsertPushDataMe(BATCH_ID, delaDDT14.SCHOOL_NO, delaDDT14.USER_NO, "", BODY_TXT, "", "ADDI06", "Delete", delaDDT14.IAWARD_ID.ToString(), "", false, ref db);

                TempData["StatusMessage"] = "刪除完成";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            return RedirectToAction("QUERY");
        }

        [HttpGet]
        public JsonResult GetNameData(string Class_No)
        {
            List<SelectListItem> items = new List<SelectListItem>();
            if (!string.IsNullOrWhiteSpace(Class_No))
            {
                var ltCLASS_NO = db.HRMT01.Where(p => p.CLASS_NO == Class_No && p.SCHOOL_NO == user.SCHOOL_NO && p.USER_TYPE == UserType.Student
                                                && (!UserStaus.NGUserStausList.Contains(p.USER_STATUS))).OrderBy(p => p.SEAT_NO).ToList();
                foreach (var item in ltCLASS_NO)
                {
                    items.Add(new SelectListItem() { Text = item.SEAT_NO + " " + item.NAME, Value = item.USER_NO });
                }
                if (!items.Count.Equals(0))
                {
                    items.Insert(0, new SelectListItem() { Text = "請選擇學生", Value = "" });
                }
            }

            return Json(items, JsonRequestBehavior.AllowGet);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}