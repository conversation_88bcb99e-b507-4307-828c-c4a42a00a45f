/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{120224:[674,0,666,31,635],120225:[662,0,604,74,547],120226:[676,14,671,27,637],120227:[662,0,692,74,656],120228:[662,0,583,74,540],120229:[662,0,535,74,523],120230:[676,14,695,27,627],120231:[662,0,658,74,584],120232:[662,0,401,45,356],120233:[662,14,398,12,305],120234:[662,0,634,74,630],120235:[662,0,559,74,546],120236:[662,0,843,75,768],120237:[662,14,675,74,601],120238:[676,14,714,30,684],120239:[662,0,525,74,512],120240:[676,175,716,30,691],120241:[662,0,589,74,581],120242:[676,14,541,32,481],120243:[662,0,608,15,593],120244:[662,14,661,69,592],120245:[662,11,654,31,623],120246:[662,11,921,29,892],120247:[662,0,700,31,669],120248:[662,0,630,21,609],120249:[662,0,637,28,603],120250:[463,10,448,35,391],120251:[684,10,496,63,466],120252:[463,10,456,23,432],120253:[684,11,494,28,437],120254:[463,10,444,23,428],120255:[683,0,336,20,369],120256:[463,216,496,21,467],120257:[684,0,487,63,424],120258:[679,0,220,64,156],120259:[679,216,254,-74,185],120260:[684,0,453,63,452],120261:[684,0,205,61,144],120262:[464,0,756,65,691],120263:[464,0,487,63,424],120264:[463,10,499,28,471],120265:[464,216,498,67,470],120266:[464,216,498,28,435],120267:[464,0,336,63,328],120268:[463,10,389,49,350],120269:[580,10,291,1,287],120270:[453,11,491,63,430],120271:[453,14,474,31,443],120272:[453,14,702,28,675],120273:[453,0,482,30,452],120274:[453,216,484,28,453],120275:[453,0,447,25,417],120802:[676,14,500,23,477],120803:[677,0,500,108,302],120804:[676,0,500,35,469],120805:[676,14,500,31,441],120806:[676,0,500,11,489],120807:[676,14,500,36,458],120808:[684,14,500,32,470],120809:[662,8,500,38,451],120810:[676,14,500,49,447],120811:[676,21,500,28,466]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/MathSS.js");
