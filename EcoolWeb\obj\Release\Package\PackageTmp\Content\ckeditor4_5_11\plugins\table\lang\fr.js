﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'fr', {
	border: 'Taille de la bordure',
	caption: 'Titre du tableau',
	cell: {
		menu: 'Cellule',
		insertBefore: 'Insérer une cellule avant',
		insertAfter: 'Insérer une cellule après',
		deleteCell: 'Supprimer les cellules',
		merge: 'Fusionner les cellules',
		mergeRight: 'Fusionner vers la droite',
		mergeDown: 'Fusionner vers le bas',
		splitHorizontal: 'Scinder la cellule horizontalement',
		splitVertical: 'Scinder la cellule verticalement',
		title: 'Propriétés de la cellule',
		cellType: 'Type de cellule',
		rowSpan: 'Lignes occupées',
		colSpan: 'Colonnes occupées',
		wordWrap: 'Césure',
		hAlign: 'Alignement horizontal',
		vAlign: 'Alignement vertical',
		alignBaseline: 'Ligne de base',
		bgColor: 'Couleur d\'arrière-plan',
		borderColor: 'Couleur de bordure',
		data: 'Données',
		header: 'En-tête',
		yes: 'Oui',
		no: 'Non',
		invalidWidth: 'La largeur de la cellule doit être un nombre.',
		invalidHeight: 'La hauteur de la cellule doit être un nombre.',
		invalidRowSpan: 'Le nombre de colonnes occupées doit être un nombre entier.',
		invalidColSpan: 'Le nombre de colonnes occupées doit être un nombre entier.',
		chooseColor: 'Choisir'
	},
	cellPad: 'Marge interne des cellules',
	cellSpace: 'Espacement entre les cellules',
	column: {
		menu: 'Colonne',
		insertBefore: 'Insérer une colonne avant',
		insertAfter: 'Insérer une colonne après',
		deleteColumn: 'Supprimer les colonnes'
	},
	columns: 'Colonnes',
	deleteTable: 'Supprimer le tableau',
	headers: 'En-têtes',
	headersBoth: 'Les deux',
	headersColumn: 'Première colonne',
	headersNone: 'Aucun',
	headersRow: 'Première ligne',
	invalidBorder: 'La taille de la bordure doit être un nombre.',
	invalidCellPadding: 'La marge interne des cellules doit être un nombre positif.',
	invalidCellSpacing: 'L\'espacement entre les cellules doit être un nombre positif.',
	invalidCols: 'Le nombre de colonnes doit être supérieur à 0.',
	invalidHeight: 'La hauteur du tableau doit être un nombre.',
	invalidRows: 'Le nombre de lignes doit être supérieur à 0.',
	invalidWidth: 'La largeur du tableau doit être un nombre.',
	menu: 'Propriétés du tableau',
	row: {
		menu: 'Ligne',
		insertBefore: 'Insérer une ligne avant',
		insertAfter: 'Insérer une ligne après',
		deleteRow: 'Supprimer les lignes'
	},
	rows: 'Lignes',
	summary: 'Résumé (description)',
	title: 'Propriétés du tableau',
	toolbar: 'Tableau',
	widthPc: 'pour cent',
	widthPx: 'pixels',
	widthUnit: 'unité de largeur'
} );
