﻿using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using MvcPaging;
namespace EcoolWeb.ViewModels
{
    public class ZZT17CountLoginQueryListViewModel
    {
             /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }



        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<uZZT17> uZZT17List;

        public ZZT17CountLoginQueryListViewModel()
        {
            Page = 0;
            OrdercColumn = "USER_NO";
        }
    }
}