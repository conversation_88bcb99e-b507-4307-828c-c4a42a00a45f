﻿

@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <br />
    <div class="Div-EZ-ZZZI26">
        <div class="alert alert-success" style="padding:0 2%; margin-bottom:0px"><h3>代申請線上投稿</h3></div>
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group text-center">
                @{
                    var BtnItem = ViewBag.tModeList as List<SelectListItem>;
                    foreach (var Btn in BtnItem)
                    {
                        <div class="col-md-4">
                            @Html.ActionLink(Btn.Text, Btn.Value, new { mode = Btn.Value }, new { @class = "btn btn-default  btn-block", @role = "button" })
                        </div>
                    }
                }
            </div>

        </div>
    </div>

}

@section css {
    <style>
        .Div-EZ-ZZZI26 {
            transition: all 0.3s ease;
        }

        .btn-block {
            transition: all 0.3s ease;
            margin-bottom: 15px;
            padding: 15px;
            font-size: 16px;
            border-radius: 8px;
        }

        .btn-block:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn-block:active {
            transform: translateY(0);
        }

        .col-md-4 {
            margin-bottom: 20px;
        }

        @@media (max-width: 768px) {
            .col-md-4 {
                margin-bottom: 10px;
            }

            .btn-block {
                padding: 12px;
                font-size: 14px;
            }
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局配置
        window.ADDI01PROXY_CONFIG = {
            pageTitle: "@ViewBag.Panel_Title",
            action: "@ViewBag.Action",
            breNo: "@ViewBag.BRE_NO"
        };
    </script>
    <script src="~/Scripts/ADDI01Proxy/index.js" nonce="cmlvaw"></script>
}