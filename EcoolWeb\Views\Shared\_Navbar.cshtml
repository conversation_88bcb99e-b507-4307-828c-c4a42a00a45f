﻿@using ECOOL_APP
@using System.Web.Mvc
@using System.Web.Routing

<style type="text/css">
      .navbar-ClassCach {
        width: 211px;
        min-width: 211px;
        padding-left: 25px;
        background-repeat: no-repeat;
        background-image: url('@Url.Content("~/Content/img/monkey-lucky.png")');
    }

    .navbar-Logout {
        width: 65px;
        height: 33px;
        background-image: url('@Url.Content("~/Content/img/web-student_png-12a.png")');
        background-repeat: no-repeat;
        background-position: right;
    }

        .ChanceQty {
        position:relative;
        left:38px;
        top:-2px;
        color:#0000FF;
        font-size:12pt;
        width:50px;
        text-align:center;
    }

    .NextChance
    {
        position:relative;
        left:95px;
        top:0px;
        font-size:12pt;
        color:red;
        width:58px;
        text-align:center;
    }

         .navbar-Cach-img{
            width:174px;
            height:71px ;
           background-image: url('@Url.Content("~/Content/img/web-student_png-07.png")');
    }
      .btn-task {
            padding: 0 1.5rem 0 .8rem;
            background-color: #fff;
            border: rgba(0, 0, 0, 0.3);
            border-radius: 3rem;
            font-weight: 600;
            font-size: 1.7rem;
            color: #0260c5;
            box-shadow: 0 1px 2px rgb(0 0 0 / 25%);
        }

        .btn-task img {
            height: 100%;
            width: auto;
        }
</style>

@{
    UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string SchoolName = EcoolWeb.Models.UserProfileHelper.GetSchoolSName();
    int CASH_NextChance = 0;
    int Chance_ARRIVED_CASH = 0;
    string Grade_desc = "";
    string NAME = "";
    string USER_TYPE_DESC = "";
    int CASH = 0;
    string USER_TYPE = "";
    string USER_NO = "";
    string LogoAct = "GuestIndex";
    bool checkstudenttasklist = false;
    UrlHelper url = new UrlHelper(Request.RequestContext);

    string routeUrl = url.RouteUrl("SchoolRoute", new { school = SchoolNO });
    if (user != null)
    {
        CASH_NextChance = user.CASH_NextChance;
        Grade_desc = user.GRADE_DESC;
        NAME = user.NAME;
        USER_TYPE_DESC = user.USER_TYPE_DESC;
        CASH = user.CASH;
        Chance_ARRIVED_CASH = user.Chance_ARRIVED_CASH;
        USER_TYPE = user.USER_TYPE;
        USER_NO = user.USER_NO;
        checkstudenttasklist = user.checkstudenttasklist;
        if (user.USER_TYPE == UserType.Student)
        { LogoAct = "StudentIndex"; }
        else
        { LogoAct = "TeacherIndex"; }

    }
}

<!-- 小於等於 767px 隱藏  -->
<div class="hidden-xs">
    <div class="containerEZ">
        <table border="0" style="width:100%">
            <tr>
                <td style="width:100%" align="left">
                    <table border="0" style="width:100%">
                        <tr>
                            <td class="Title" align="left" style="width:250px">
                                <a href='@Url.Action("PortalIndex", "Home")'>
                                    @if (System.Web.Configuration.WebConfigurationManager.AppSettings["TestMode"] == "true")
                                    {
                                        <img src="~/Content/img/web-19.png" style="position:relative; left: 20px;height:50px" />
                                    }
                                    else
                                    {
                                        <img src="~/Content/img/web-student_png-16.png" style="position:relative; left: 20px" />
                                    }
                                </a>
                                <span> &nbsp; &nbsp; &nbsp;</span>

                                @if (string.IsNullOrWhiteSpace(SchoolNO) == false)
                                {
                                    <a href="@routeUrl"
                                       style="color:#004DA0;font-family:DFKai-SB;" id="goToSchool">
                                        <img style="width:auto;max-height:75px;margin-top: 5px;"
                                             src='@Url.Action("BDMT01IMG", "Content", new { school = SchoolNO + ".png" })' />
                                    </a>

                                    @*<a href='@Url.Action(LogoAct, "Home", new { school = SchoolNO })'>

                                        <img style="width:auto;max-height:75px;margin-top: 5px;" src='@Url.Action("BDMT01IMG", "Content", new { school = SchoolNO + ".png" })' />
                                           </a>*@
                                }

                                @*@Html.RouteLink(SchoolName, "SchoolRoute", new { school = SchoolNO },

                                    new { style = "color:#004DA0;font-family:DFKai-SB;" } , null)*@

                                @Html.RouteLink(
                        linkText: SchoolName,
                        routeName: "SchoolRoute",
                        routeValues: new { school = SchoolNO },
                        htmlAttributes: new { @style = "color:#004DA0;font-family:DFKai-SB;", id = "goToSchool" }
                        )

                                @*<a style="color:#004DA0;font-family:DFKai-SB;" href='@Url.Action("GuestIndex", "Home", new {school= SchoolNO })'>
                                        @SchoolName
                                    </a>*@

                                @if (user != null)
                                {
                                    <div style="display:inline;">
                                        @Grade_desc
                                        @NAME
                                        @USER_TYPE_DESC
                                        @if (USER_TYPE != ECOOL_APP.EF.UserType.Admin && USER_TYPE != ECOOL_APP.EF.UserType.Parents)
                                        {
                                            <div class="navbar-Cach-font">，酷幣點數:@CASH</div>
                                        }
                                    </div>

                                }
                            </td>
                            @if (user != null)
                            {
                                if (USER_TYPE != ECOOL_APP.EF.UserType.Admin && USER_TYPE != ECOOL_APP.EF.UserType.Parents)
                                {
                                    <td class="navbar-Cach Title navbar-Cach-img">@CASH</td>
                                }
                                if (USER_TYPE == ECOOL_APP.EF.UserType.Student)
                                {
                                    <td class="navbar-ClassCach Title" nowrap="nowrap">
                                        @if (Chance_ARRIVED_CASH > 0)
                                        {
                                            <a href='@Url.Action("ArrivedChance2", "Home")'>
                                                <div class="ChanceQty">
                                                    @Chance_ARRIVED_CASH
                                                </div>
                                            </a>
                                        }
                                        else
                                        {
                                            <div class="ChanceQty">0</div>
                                        }

                                        <div class="NextChance">@CASH_NextChance</div>
                                    </td>
                                }
                            }
                            <td style="text-align:center;padding-left:20px">
                                @if (user != null && USER_TYPE == "S")
                                {
                                    if (checkstudenttasklist == false)
                                    {<a class="btn btn-Basic btn-task"
                                        href='@Url.Action("StudentIndexTaskList", "Home")'>

                                            <img src="~/Content/img/icon-task-none.png" />
                                            任務區
                                        </a>

                                    }
                                    else
                                    { <a class="btn btn-Basic btn-task"
                                         href='@Url.Action("StudentIndexTaskList", "Home")'>

                                            <img src="~/Content/img/icon-task-have.gif" />
                                            有任務
                                        </a>

                                    }
                                }
                            </td>

                            <td style="width:40px">    <a href='@Url.Action("Logout", "Home")'><img class="navbar-Logout" /></a>  </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</div>
<script src="~/Scripts/jquery.cycle.all.js"></script>
<script type="text/javascript">
    //$(document).ready(function () {

    //    $('#fade').cycle({
    //        fx: 'fade',
    //        delay: -7000
    //    });
    //});
</script>