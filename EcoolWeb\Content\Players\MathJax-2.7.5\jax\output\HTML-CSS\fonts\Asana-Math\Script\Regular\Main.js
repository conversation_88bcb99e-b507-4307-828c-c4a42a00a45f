/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Script/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Script={directory:"Script/Regular",family:"AsanaMathJax_Script",testString:"\u210A\u210B\u2110\u2112\u211B\u212C\u212F\u2130\u2131\u2133\u2134\uD835\uDC9C\uD835\uDC9E\uD835\uDC9F\uD835\uDCA2",32:[0,0,249,0,0],8458:[410,344,896,56,842],8459:[732,12,961,46,1161],8464:[729,15,937,46,1123],8466:[758,10,1025,46,1163],8475:[723,16,946,49,1090],8492:[730,6,972,46,1116],8495:[411,19,516,35,481],8496:[746,15,680,46,824],8497:[724,19,850,32,1110],8499:[726,5,1046,45,1186],8500:[411,19,578,36,543],119964:[713,12,885,46,1055],119966:[744,15,751,45,874],119967:[733,8,923,44,1050],119970:[740,15,703,46,871],119973:[724,199,944,46,1095],119974:[721,15,1016,45,1180],119977:[723,13,988,46,1173],119978:[716,15,711,46,840],119979:[717,19,949,42,1038],119980:[716,33,708,46,838],119982:[741,15,939,46,1168],119983:[745,13,828,46,1136],119984:[727,6,836,46,907],119985:[727,10,784,46,1071],119986:[722,12,862,46,1187],119987:[721,12,908,46,1095],119988:[723,249,908,46,1085],119989:[719,5,978,46,1078],119990:[480,12,774,42,735],119991:[854,14,747,36,827],119992:[480,20,608,42,569],119993:[785,10,733,36,900],119995:[853,341,957,36,1123],119997:[847,13,681,36,845],119998:[708,22,438,42,508],119999:[708,350,872,42,986],120000:[854,17,831,36,877],120001:[860,17,757,36,811],120002:[477,16,1147,42,1106],120003:[477,15,843,42,804],120005:[477,401,1143,42,1104],120006:[480,401,817,42,777],120007:[468,0,747,42,708],120008:[603,15,542,42,503],120009:[714,20,656,42,615],120010:[459,20,745,42,705],120011:[469,22,652,42,612],120012:[469,22,959,42,920],120013:[479,20,817,42,777],120014:[459,403,991,42,952],120015:[498,17,781,42,741],120016:[713,12,881,46,1051],120017:[732,6,994,55,1119],120018:[744,15,754,46,874],120019:[735,8,910,46,1041],120020:[746,15,693,46,824],120021:[726,19,862,45,1120],120022:[740,15,721,46,869],120023:[733,12,950,45,1150],120024:[730,15,929,46,1116],120025:[726,194,898,46,1085],120026:[722,15,982,46,1169],120027:[758,9,1019,46,1152],120028:[727,5,1055,45,1175],120029:[723,13,975,46,1162],120030:[717,15,709,46,838],120031:[717,15,949,46,1042],120032:[717,32,709,46,838],120033:[724,15,951,46,1083],120034:[741,15,926,46,1157],120035:[747,13,814,46,1126],120036:[728,6,816,46,904],120037:[728,12,777,46,1064],120038:[723,11,887,42,1173],120039:[722,12,898,46,1085],120040:[735,242,898,46,1075],120041:[721,5,969,46,1069],120042:[480,13,782,55,735],120043:[846,15,775,51,839],120044:[480,20,633,55,578],120045:[779,11,951,51,906],120046:[480,20,633,59,578],120047:[844,332,903,47,1117],120048:[479,379,1003,59,952],120049:[839,13,891,47,851],120050:[693,20,403,59,567],120051:[693,329,807,59,969],120052:[846,16,823,49,883],120053:[800,17,718,59,782],120054:[474,15,1137,55,1084],120055:[473,11,848,55,799],120056:[480,20,699,59,636],120057:[477,378,1129,55,1079],120058:[480,381,828,56,775],120059:[469,0,759,55,707],120060:[596,17,576,63,520],120061:[704,20,678,65,625],120062:[462,20,761,59,709],120063:[470,20,674,57,620],120064:[470,20,968,49,904],120065:[479,20,835,60,780],120066:[463,378,992,59,940],120067:[494,18,799,59,742]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Script"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Regular/Main.js"]);
