﻿using Dapper;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.ViewModels.COOCApi;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace EcoolWeb.Controllers
{
    public class ADDT14Group
    {
        public string IAWARD_ITEM;

        public DateTime CREATEDATE;

        public List<ADDT14> SubList;

    }

    public class ADDI01ApiController : ApiBase
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        [HttpPost, HttpGet]
        [AllowAnonymous]
        public ADDI01IndexViewModel Index(ADDI01IndexViewModel model)
        {

            if (model == null) model = new ADDI01IndexViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            new ADDI01Controller().IndexGetData(model, SchoolNO, user);

            return model;
        }

        [HttpPost, HttpGet]
        [AllowAnonymous]
        public ADDI01ApiDetailsViewModel Details(ADDI01ApiDetailsViewModel Data)
        {
            if (Data.WRITING_NO == null)
            {
                return null;
            }
            Data.aDDT01  = db.ADDT01.Include("ADDT02").Where(a => a.WRITING_NO == Data.WRITING_NO).FirstOrDefault();

            if (Data.aDDT01 == null)
            {
                return null;
            }


            ADDI01Controller UseADDI01 = new ADDI01Controller();

            UseADDI01.DetailsData(Data.aDDT01);

            //組圖檔路徑
            Data.aDDT01.IMG_FILE = UseADDI01.GetImagePath(Data.aDDT01).FirstOrDefault();

            //組聲音檔路徑
            string VOICE_Path = UseADDI01.GetVoicePath(Data.aDDT01);
            if (string.IsNullOrWhiteSpace(VOICE_Path)==false)
            {
                 Data.aDDT01.VOICE_FILE = Url.Content(VOICE_Path);
            }

            return Data;
        }

        [HttpPost]
        [AllowAnonymous]
        public SECI01IndexViewReturnModel GetCashCount(LoginAppLoginViewModel loginViewModel)
        {
            SECI01IndexViewReturnModel Data = new SECI01IndexViewReturnModel();
            AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == loginViewModel.SchoolNo && a.USER_NO == loginViewModel.UserNo).FirstOrDefault();
            string sSQL = @" select case when Sum(w.AMT) IS NULL   then 0 else  Sum(w.AMT) end from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=@SCHOOL_NO and w.USER_NO=@USER_NO";
            int? Deposit = 0;
            string strMessage = null;
            HRMT01 aStudent = HomeController.GetUser(loginViewModel.SchoolNo, loginViewModel.UserNo, out strMessage);
            if (string.IsNullOrWhiteSpace(strMessage) == false)
            {
                Data.ErrorCode = (int)eErrorCode.E201;
                Data.ErrorMessage = strMessage;
                return Data;
            }
            var DepositQuryable = db.Database.Connection.Query<int>(sSQL, new { SCHOOL_NO = loginViewModel.SchoolNo, USER_NO = loginViewModel.UserNo });

            if (DepositQuryable != null)
            {
                Deposit = DepositQuryable.FirstOrDefault();
            }
            if (tCASH != null)
            {
                Data.CASH_DEPOSIT = (Deposit != null) ? Deposit : 0;
                Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
            }
            else
            {
                Data.CASH_DEPOSIT = 0;
                Data.CASH_ALL = 0;
                Data.CASH_AVAILABLE = 0;
            }
            return Data;

        }
        [HttpPost]
        [AllowAnonymous]
        public SECI01IndexViewReturnModel GetMonthBookCount(LoginAppLoginViewModel loginViewModel)
        {
            SECI01IndexViewReturnModel Data = new SECI01IndexViewReturnModel();
            int SYear;
            int Semesters;
            string strMessage = null;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            HRMT01 aStudent = HomeController.GetUser(loginViewModel.SchoolNo, loginViewModel.UserNo, out strMessage);
            if (string.IsNullOrWhiteSpace(strMessage) == false)
            {
                Data.ErrorCode = (int)eErrorCode.E201;
                Data.ErrorMessage = strMessage;
                return Data;
            }
            List<DB2_L_WORK2> BookWorks = db.DB2_L_WORK2.Where(a => a.SCHOOL_NO == loginViewModel.SchoolNo && a.NO_READ == aStudent.IDNO && a.SEYEAR == SYear.ToString() && a.SESEM == Semesters.ToString() && a.RET_YYMM != "      ").ToList();
            if (BookWorks != null)
            {
                Data.BOOKS = BookWorks.Sum(a => a.QTY);
                string YYYYMM = DateTime.Today.ToString("yyyyMM");
                DB2_L_WORK2 mm = BookWorks.Where(a => a.RET_YYMM == YYYYMM).FirstOrDefault();
                if (mm != null)
                    Data.BOOKS_MONTH = mm.QTY;
                else
                    Data.BOOKS_MONTH = 0;
            }
            return Data;
        }
        [HttpPost, HttpGet]
        [AllowAnonymous]
        public List<ADDT14Group> ADDT14Query(string SchoolNO,int TopCount)
        {
            EcoolWeb.ViewModels.ADDI06IndexViewModel model = new ViewModels.ADDI06IndexViewModel();
            
            EcoolWeb.Controllers.ADDI06Controller.GetAddt14(model, SchoolNO, TopCount);

            var GroupResult = from GR in model.ADDT14List
                              group GR by new
                              {
                                  IAWARD_ITEM = GR.IAWARD_ITEM,
                                  CREATEDATE = GR.CREATEDATE.Value.Date
                              }
                              into g
                              select new ADDT14Group
                              {
                                  IAWARD_ITEM = g.Key.IAWARD_ITEM,
                                  CREATEDATE=g.Key.CREATEDATE,
                                  SubList = g.ToList()
                              };

            return GroupResult.OrderByDescending(a=>a.CREATEDATE).ToList();
            //return model.ADDT14List.ToList();
        }

        [HttpPost, HttpGet]
        [AllowAnonymous]
        public string ADDT14QueryText(string SchoolNO, int TopCount)
        {
            EcoolWeb.ViewModels.ADDI06IndexViewModel model = new ViewModels.ADDI06IndexViewModel();

            EcoolWeb.Controllers.ADDI06Controller.GetAddt14(model, SchoolNO, TopCount);

            System.Text.StringBuilder sBuilder = new System.Text.StringBuilder();
            sBuilder.AppendLine("日期,公告人,班級,姓名,獎懲類別,具體事蹟,獎勵點數");
            foreach(ADDT14 a14 in model.ADDT14List)
            {
                sBuilder.Append(a14.CREATEDATE.Value.ToString("yyyy/MM/dd"));
                sBuilder.Append(",");
                sBuilder.Append(a14.TNAME);
                sBuilder.Append(",");
                sBuilder.Append(a14.CLASS_NO);
                sBuilder.Append(",");
                sBuilder.Append(a14.SNAME);
                sBuilder.Append(",");
                sBuilder.Append(a14.IAWARD_KIND);
                sBuilder.Append(",");
                sBuilder.Append(a14.IAWARD_ITEM);
                sBuilder.Append(",");
                sBuilder.AppendLine(a14.CASH.ToString());
            }

            return sBuilder.ToString();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
