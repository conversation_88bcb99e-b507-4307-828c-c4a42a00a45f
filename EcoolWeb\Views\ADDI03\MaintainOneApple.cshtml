﻿@model EcoolWeb.Models.ADDI03BookMaintainViewModel
@{
    ViewBag.Title = "調整書號";
    //Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

@Html.Partial("_Title_Secondary")
<br />

@using (Html.BeginForm("MaintainOneAppleOK", "ADDI03", FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
{
    @Html.HiddenFor(model => model.whereKeyword)
    @Html.HiddenFor(model => model.Class_No)
    <div class="Div-EZ-rpp">
        <img src="~/Content/img/web-bar2-revise-05.png" style="width:580px" class="img-responsive " alt="Responsive image" />
        <div class="form-horizontal">
            <div class="form-group">
                @Html.HiddenFor(model=> model.USER_NO)
                @Html.Label("學生姓名", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @Html.TextBoxFor(model => model.USER_NAME, new { @readonly = "readonly", @class = "form-control" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("護照年級", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @Html.TextBoxFor(model=>model.GRADE, new { @readonly = "readonly", @class = "form-control" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("原書號", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @Html.TextBoxFor(model=>model.BOOK_ID, new { @readonly = "readonly", @class = "form-control" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("原書名", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @Html.TextBoxFor(model=>model.BOOK_NAME, new { @readonly = "readonly", @class = "form-control" })
                </div>
            </div>

            <div class="form-group">
                @Html.Label("新書號", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @Html.DropDownList("NewBookID", (IEnumerable<SelectListItem>)Model.NewBookIDList, new { @class = "form-control" })
                </div>
            </div>
           
            <div class="form-group text-center">
                <button class="btn btn-default" id="btnSend" name="btnSend" value="Save">
                    確定送出
                </button>
                <button class="btn btn-default" type="button" id="btnCancel">
                    放棄編輯
                </button>
            </div>
        </div>
    </div>

}

@section css {
    <style>
        .form-group.focused {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            transition: all 0.3s ease;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .alert-message {
            margin-top: 10px;
            animation: slideDown 0.3s ease;
        }

        @@keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI03_MAINTAIN_URLS = {
            query3: "../ADDI03/Query3",
            maintainOneAppleOK: "../ADDI03/MaintainOneAppleOK"
        };
    </script>
    <script src="~/Scripts/ADDI03/maintain-one-apple.js" nonce="cmlvaw"></script>
}
