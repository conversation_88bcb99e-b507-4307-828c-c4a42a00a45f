﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01ClassWearIndexViewModel
    {
        public byte IsSearch { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        public string WhereSYEARSEMESTER { get; set; }

        [DisplayName("學年度")]
        public byte? WhereSYEAR { get; set; }

        [DisplayName("學期")]
        public byte? WhereSEMESTER { get; set; }

        public BDMT01 dMT01 { get; set; }

        public List<GAAT01> gAAT01s { get; set; }

        public List<GAAI01AVGClassWearrRateViewModel> AvgClassData { get; set; }

        /// <summary>
        /// 管理者統計報表 -  填報的班級數量資料
        /// </summary>
        public List<GAAI01SearchNotReportedViewModel> ClassReportedData { get; set; }

        /// <summary>
        /// 管理者統計報表 -  全校配戴率
        /// </summary>
        public List<GAAI01SearchSchoolRateViewModel> SchoolRate { get; set; }

        /// <summary>
        /// 管理者統計報表 - 各未配載原因統計人數
        /// </summary>
        public List<GAAI01SearchUnWearMemoCountViewModel> UnWearMemoCount { get; set; }
    }
}