/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"],{8704:[694,16,639,1,640],8706:[710,17,628,60,657],8707:[694,-1,639,64,574],8709:[767,73,575,46,528],8711:[686,24,958,56,901],8712:[587,86,767,97,670],8713:[711,210,767,97,670],8715:[587,86,767,96,670],8722:[281,-221,894,96,797],8723:[537,227,894,64,829],8725:[750,250,575,63,511],8726:[750,250,575,63,511],8727:[472,-28,575,73,501],8728:[474,-28,575,64,510],8729:[474,-28,575,64,510],8730:[820,180,958,78,988],8733:[451,8,894,65,830],8734:[452,8,1150,65,1084],8736:[714,0,722,55,676],8739:[750,249,319,129,190],8741:[750,248,575,145,430],8743:[604,17,767,64,702],8744:[604,16,767,64,702],8745:[603,16,767,64,702],8746:[604,16,767,64,702],8747:[711,211,569,64,632],8764:[391,-109,894,64,828],8768:[583,82,319,64,254],8771:[502,3,894,64,829],8773:[638,27,1000,64,829],8776:[524,-32,894,64,829],8781:[533,32,894,64,829],8784:[721,-109,894,64,829],8800:[711,210,894,64,829],8801:[505,3,894,64,829],8804:[697,199,894,96,797],8805:[697,199,894,96,797],8810:[617,116,1150,64,1085],8811:[618,116,1150,64,1085],8826:[585,86,894,96,797],8827:[586,86,894,96,797],8834:[587,85,894,96,797],8835:[587,86,894,96,796],8838:[697,199,894,96,797],8839:[697,199,894,96,796],8846:[604,16,767,64,702],8849:[697,199,894,96,828],8850:[697,199,894,66,797],8851:[604,-1,767,70,696],8852:[604,-1,767,70,696],8853:[632,132,894,64,828],8854:[632,132,894,64,828],8855:[632,132,894,64,828],8856:[632,132,894,64,828],8857:[632,132,894,64,828],8866:[693,-1,703,65,637],8867:[693,-1,703,64,638],8868:[694,-1,894,64,829],8869:[693,-1,894,65,829],8872:[750,249,974,129,918],8900:[523,21,575,15,560],8901:[336,-166,319,74,245],8902:[502,0,575,24,550],8904:[540,39,1000,33,967],8942:[951,29,319,74,245],8943:[336,-166,1295,74,1221],8945:[871,-101,1323,129,1194]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/MathOperators.js");
