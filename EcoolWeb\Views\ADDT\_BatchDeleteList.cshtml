﻿@model EcoolWeb.ViewModels.ADDTBatchDeleteListDataViewViewModel

@using (Html.BeginCollectionItem("BatchDeleteList", true))
{
    var Index = Html.GetIndex("BatchDeleteList");

    <tr id="Tr@(Model.APPLY_NO)">
        <td style="text-align:left;white-space:nowrap;">

            @Html.HiddenFor(modelItem => modelItem.APPLY_NO)
            @Html.DisplayFor(modelItem => modelItem.aDDT06.CRE_DATE, "ShortDateTime")
        </td>
        <td style="cursor:pointer;">
            @Html.DisplayFor(modelItem => modelItem.aDDT06.CLASS_NO)
        </td>
        <td>
            @Html.DisplayFor(modelItem => modelItem.aDDT06.SEAT_NO)
        </td>
        <td style="text-align:center;">
            @Html.DisplayFor(modelItem => modelItem.aDDT06.NAME)
        </td>
        <td align="left">
            @Html.DisplayFor(modelItem => modelItem.aDDT06.BOOK_NAME)

            @if (Model.aDDT06.SHARE_YN == "Y" || Model.aDDT06.SHARE_YN == "y")
            {
                <img src="~/Content/img/icons-like-05.png" />
            }
        </td>
        <td style="text-align:center;">
            @ECOOL_APP.EF.ADDStatus.GetADDT06StatusString(Model.aDDT06.APPLY_STATUS)
        </td>
    </tr>

}