﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'pt', {
	find: 'Pesquisar',
	findOptions: 'Opções de pesquisa',
	findWhat: 'Texto a procurar:',
	matchCase: 'Maiúsculas/Minúsculas',
	matchCyclic: 'Match cyclic',
	matchWord: 'Coincidir com toda a palavra',
	notFoundMsg: 'O texto especificado não foi encontrado.',
	replace: 'Substituir',
	replaceAll: 'Substituir tudo',
	replaceSuccessMsg: '%1 ocurrências(s) substituídas.',
	replaceWith: 'Substituir por:',
	title: 'Pesquisar e substituir'
} );
