﻿@model CER002IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@if (user != null)
{
    if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin && ViewBag.ISTASKLIST != "True")
    {

        <a role="button" href='@Url.Action("Index", "CERI01", new { FirstPage = "true", SouBre_NO = "CERI01" })' class="btn btn-sm btn-sys ">
            護照名稱設定
        </a>
        <a role="button" href='@Url.Action("Index", "CERI02", new { FirstPage = "true", SouBre_NO = "CERI02" })' class="btn btn-sm btn-sys ">
            護照明細設定
        </a>

        <a role="button" href='@Url.Action("Index", "CERI04", new { FirstPage = "true", SouBre_NO = "CERI04" })' class="btn btn-sm btn-sys  ">
            我要登記
        </a>

        <a role="button" href='@Url.Action("Index", "CERI05", new { FirstPage = "true", SouBre_NO = "CERI05" })' class="btn btn-sm btn-sys ">
            護照補登
        </a>

        <a role="button" href='@Url.Action("Index", "CER001", new { FirstPage = "true", SouBre_NO = "CER001" })' class="btn btn-sm btn-sys ">
            護照通過情形(在校生)
        </a>
        <a role="button" href='@Url.Action("GraduateIndex", "CERI013", new { FirstPage = "true", SouBre_NO = "CER001" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "GraduateIndex" ? "active" : "")">
            護照通過情形(畢業生)
        </a>
        <a role="button" href='@Url.Action("IndexTecher", "CER002", new { FirstPage = "true", SouBre_NO = "CERI03" })' class="btn btn-sm btn-sys  active">
            查詢學生護照
        </a>

        <a role="button" href='@Url.Action("IndexGradeTecher", "CER002", new { FirstPage = "true", SouBre_NO = "CER0021" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CERI05" ? "active" : "")">
            查詢學生護照(畢業生)
        </a>
    }
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@if (user?.USER_TYPE == UserType.Teacher || user?.USER_TYPE == UserType.Admin)
{
    using (Html.BeginForm("IndexTeacher", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
    {
        <div id="PageContent">
            @Html.Action("_TeacherPageContent", (string)ViewBag.BRE_NO)
        </div>
    }
}
else
{
    <div style="height:15px"></div>
    <div class="text-center"><h3>此功能目前只有老師可使用</h3></div>

}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';
        $(document).ready(function () {

            $("[id^='ResetPassword']").colorbox({ iframe: true, width: '100%', height: '100%', opacity: 0.82 });
        });

        function FunPageProc(page) {
            if ($(targetFormID).length > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

        function doSort(SortCol) {

            var OrderByColumnName = $('#@Html.IdFor(m=>m.OrderByColumnName)').val();
            var SortType = $('#@Html.IdFor(m=>m.SortType)').val();

            $('#@Html.IdFor(m=>m.OrderByColumnName)').val(SortCol)

            if (OrderByColumnName == SortCol ) {

                if (SortType.toUpperCase()=="@PageGlobal.SortType.DESC") {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.ASC');
                }
                else {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
                }
            } else {
                 $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
            }

            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_TeacherPageContent")',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }


         //查詢
        function funAjaxTeacher() {

            $.ajax({
                url: '@Url.Action("_TeacherPageContent")',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }
    </script>
}