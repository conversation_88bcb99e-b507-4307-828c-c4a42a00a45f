﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI28AddViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm("Edit_last", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()

    @Html.HiddenFor(model => model.SCHOOL_NO)
    @Html.HiddenFor(model => model.PARENTS_USER_NO)
    @Html.HiddenFor(model => model.REF_BRE_NO)

    <div class="panel panel-ACC">
        <div class="panel-heading  text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-body">
            <div class="form-horizontal">

                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                @Html.HiddenFor(model => model.CLASS_NO)
                @Html.HiddenFor(model => model.STUDENT_USER_NO)

                @Html.HiddenFor(model => model.NUM_TYPE, new { @Value = Model.NUM_TYPE })

                @if (Model.NUM_TYPE == 2)
                {
                    <div class="form-group">
                        @Html.LabelFor(model => model.IDNO, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.IDNO, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                            @Html.ValidationMessageFor(model => model.IDNO, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.STUDENT_USER_NO2, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.STUDENT_USER_NO2, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                            @Html.ValidationMessageFor(model => model.STUDENT_USER_NO2, "", new { @class = "text-danger" })
                        </div>
                    </div>
                }
                else if (Model.NUM_TYPE == 3)
                {
                    @Html.HiddenFor(model => model.IDNO)
                    @Html.HiddenFor(model => model.STUDENT_USER_NO2)
                    @*<div class="form-group">
                        @Html.LabelFor(model => model.BIRTHDAY, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.BIRTHDAY, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                            @Html.ValidationMessageFor(model => model.BIRTHDAY, "", new { @class = "text-danger" })
                            <br />
                            <label class="text-info">日期請輸入yyyy/mm/dd格式，例2005/01/01 </label>
                        </div>
                    </div>*@
                    <div class="form-group">
                        @Html.LabelFor(model => model.STUDENT_PWD, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.PasswordFor(model => model.STUDENT_PWD, new { @class = "form-control", @placeholder = "必填(區分大小寫)" })
                            @Html.ValidationMessageFor(model => model.STUDENT_PWD, "", new { @class = "text-danger" })
                        </div>
                    </div>

                }





            </div>

            <div class="form-group">
                <div class="col-md-12 text-center">
                    <div class="form-inline">
                        @if (Model.NUM_TYPE == 2)
                        {
                            <input type="submit" value="下一步" class="btn btn-default" />
                        }
                        else if (Model.NUM_TYPE == 3)
                        {
                            <input type="submit" value="新增綁定" class="btn btn-default" />
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">
        $(document).ready(function () {

            $("#BIRTHDAY").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });


            var Today = new Date();
            $("#BIRTHDAY").datepicker("setDate", Today);

        });



    </script>
}