// Modern jQuery code for ADDI01 Details page
$(document).ready(function() {
    // 字體大小控制
    const fontControls = {
        init: function() {
            this.bindEvents();
            this.loadSavedSize();
        },

        bindEvents: function() {
            $('#jfontsize-minus').on('click', (e) => {
                e.preventDefault();
                this.changeSize(-1);
            });

            $('#jfontsize-plus').on('click', (e) => {
                e.preventDefault();
                this.changeSize(1);
            });
        },

        changeSize: function(delta) {
            const currentSize = parseInt(storage.get('fontSize') || 16);
            const newSize = Math.max(12, Math.min(24, currentSize + delta));
            
            $('.content-area').css('font-size', newSize + 'px');
            storage.set('fontSize', newSize);
        },

        loadSavedSize: function() {
            const savedSize = storage.get('fontSize');
            if (savedSize) {
                $('.content-area').css('font-size', savedSize + 'px');
            }
        }
    };

    // 分享功能
    const shareFeature = {
        init: function() {
            this.clipboard = new Clipboard('#id_copy');
            this.bindEvents();
        },

        bindEvents: function() {
            this.clipboard.on('success', (e) => {
                $('#success').fadeIn().delay(2000).fadeOut();
                e.clearSelection();
            });

            this.clipboard.on('error', (e) => {
                console.error('Copy failed:', e);
                showError('複製失敗，請手動複製');
            });
        }
    };

    // 錯誤處理
    function showError(message) {
        const $errorDiv = $('<div>')
            .addClass('alert alert-danger')
            .text(message)
            .hide();
        
        $('body').prepend($errorDiv);
        $errorDiv.fadeIn(200).delay(3000).fadeOut(200, function() {
            $(this).remove();
        });
    }

    // 初始化所有功能
    fontControls.init();
    shareFeature.init();

    // 使用 localStorage 替代 jstorage
    const storage = {
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('Error saving to localStorage:', e);
            }
        },
        
        get: function(key) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : null;
            } catch (e) {
                console.error('Error reading from localStorage:', e);
                return null;
            }
        },
        
        remove: function(key) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('Error removing from localStorage:', e);
            }
        }
    };

    // 圖片畫廊功能
    const imageGallery = {
        init: function() {
            this.initColorbox();
            this.initTouchSwipe();
        },

        initColorbox: function() {
            // 初始化 Colorbox 圖片畫廊
            $("#DivImg img").colorbox({
                opacity: 0.82,
                maxHeight: '95%',
                maxWidth: '95%'
            });
        },

        initTouchSwipe: function() {
            // 初始化觸控滑動功能
            $("#imagegallery").touchwipe({
                wipeLeft: function () {
                    parent.$.fn.colorbox.next();
                },
                wipeRight: function () {
                    parent.$.fn.colorbox.prev();
                },
                preventDefaultEvents: false
            });
        }
    };

    // 初始化所有功能
    fontControls.init();
    shareFeature.init();
    imageGallery.init();

    // 導出需要的函數到全局作用域
    window.storage = storage;
});