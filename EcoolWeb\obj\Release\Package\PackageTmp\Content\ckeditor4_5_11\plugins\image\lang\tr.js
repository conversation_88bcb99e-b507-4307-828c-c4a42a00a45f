﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'tr', {
	alt: 'Alternatif Yazı',
	border: 'Kenar',
	btnUpload: '<PERSON>uc<PERSON><PERSON>',
	button2Img: 'Seçili resim butonunu basit resime çevirmek istermisiniz?',
	hSpace: '<PERSON><PERSON><PERSON>',
	img2Button: 'Se<PERSON><PERSON> olan resimi, resimli butona çevirmek istermisiniz?',
	infoTab: 'Resim Bilgisi',
	linkTab: 'Köprü',
	lockRatio: '<PERSON><PERSON><PERSON>litle',
	menu: 'Resim Özellikleri',
	resetSize: 'Boyutu Başa Döndür',
	title: '<PERSON>si<PERSON> Özellikleri',
	titleButton: 'Resimli Düğme Özellikleri',
	upload: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	urlMissing: '<PERSON><PERSON>in URL kaynağı eksiktir.',
	vSpace: '<PERSON><PERSON> Boşluk',
	validateBorder: 'Çerçeve tam sayı olmalıdır.',
	validateHSpace: 'HSpace tam sayı olmalıdır.',
	validateVSpace: 'VSpace tam sayı olmalıdır.'
} );
