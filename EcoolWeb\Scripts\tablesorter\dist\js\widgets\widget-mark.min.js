(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: mark.js - updated 9/23/2016 (v2.27.7) */
!function(c){"use strict";var d=c.tablesorter;d.mark={init:function(r){if("function"==typeof c.fn.mark){var e,n=r.widgetOptions.mark_tsUpdate;r.$table.on("filterEnd.tsmark pagerComplete.tsmark"+(n?" "+n:""),function(e,t){d.mark.update(r,e.type===n?t:"")}),e="(?:<|=|>|\\||\"|\\'|\\s+(?:&&|-|"+(d.language.and||"and")+"|"+(d.language.or||"or")+"|"+(d.language.to||"to")+")\\s+)",d.mark.regex.filter=new RegExp(e,"gim")}else console.warn('Widget-mark not initialized: missing "jquery.mark.js"')},regex:{mark:/^mark_(.+)$/,pure:/^\/((?:\\\/|[^\/])+)\/([mig]{0,3})?$/},checkRegex:function(e){if(e instanceof RegExp){var t="".match(e);return null===t||t.length<5}return!1},cleanMatches:function(e){for(var t=[],r=e&&e.length||0;r--;)""!==e[r]&&(t[t.length]=e[r]);return t},ignoreColumns:function(e){for(var t=e.widgetOptions,r=e.columns,n=[];r--;)(t.mark_tsIgnore[r]||c(e.$headerIndexed[r]).hasClass("mark-ignore"))&&(n[n.length]=":nth-child("+(r+1)+")");return n.length?":not("+n.join(",")+")":""},update:function(o,e){var l={},g=o.widgetOptions,m=d.mark.regex,s=o.$table.find("tbody tr").unmark().not("."+(o.widgetOptions.filter_filteredRow||"filtered"));e=e||c.tablesorter.getFilters(o.$table),c.each(o.widgetOptions,function(e,t){var r=e.match(m.mark);r&&void 0!==r[1]&&(l[r[1]]=t)}),c.each(e,function(e,t){if(t&&!c(o.$headerIndexed[e]).hasClass("mark-ignore")&&!g.mark_tsIgnore[e]){var r=null,n=t,a=!1,i=e===o.columns?d.mark.ignoreColumns(o):":nth-child("+(e+1)+")";if(m.pure.test(t)){".*"===(n=m.pure.exec(t))[1]&&(n[1]="");try{r=new RegExp(n[1],"gim"),n=new RegExp(n[1],n[2])}catch(e){n=null}return void(d.mark.checkRegex(r)&&s.children(i).markRegExp(n,l))}n=0===t.indexOf("~")?(a=!0,t.replace(/~/g,"").split("")):(-1<t.indexOf("?")&&(a=!0,t=t.replace(/\?/g,"\\S{1}")),-1<t.indexOf("*")&&(a=!0,t=t.replace(/\*/g,"\\S*")),t.split(m.filter)),a&&n&&n.length?(n=new RegExp(d.mark.cleanMatches(n).join(".*"),"gm"),d.mark.checkRegex(n)&&s.children(i).markRegExp(n,l)):s.children(i).mark(d.mark.cleanMatches(n),l)}})}},d.addWidget({id:"mark",options:{mark_tsUpdate:"markUpdate",mark_tsIgnore:{}},init:function(e,t,r,n){d.mark.init(r,n)},remove:function(e,t){var r=t.widgetOptions.mark_tsUpdate;t.$table.off("filterEnd.tsmark pagerComplete.tsmark"+(r?" "+r:""))}})}(jQuery);return jQuery;}));
