﻿@model EcoolWeb.Models.DownloadManagerIndex2ViewModel
@using ECOOL_APP;
@using ECOOL_APP.EF;
@using EcoolWeb.Models;
@{
    ViewBag.Title = "教學文件下載";
}

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
@*<link rel="stylesheet" href="/resources/demos/style.css">
<script src="https://code.jquery.com/jquery-3.6.0.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>*@


<div id="tabs">
    <ul>
        <li class="@(Model.ACT=="V"?"ui-state-default ui-corner-top ui-tabs-active ui-state-active ui-state-hover":"")">


            <a href="#tabs-1">教學影片下載</a><img src='~/Content/img/gif-new-star.gif' style="height:30px;width:30px;max-height:30px;max-width:30px" />
        </li>

        <li class="@(Model.ACT=="F"?"ui-state-default ui-corner-top ui-tabs-active ui-state-active ui-state-hover":"")"><a href="#tabs-2">教學文件下載</a></li>

    </ul>
    <div id="tabs-2">
        @Html.Partial("_Title_Secondary")

        @Html.Partial("_Notice")
        @using (Html.BeginForm("Index2", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "" }))
        {
            @Html.AntiForgeryToken()

            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.Page)
            @Html.HiddenFor(m => m.ShowDisable)
            @Html.HiddenFor(m => m.BackAction)
            @Html.HiddenFor(m => m.BackController)
            @Html.HiddenFor(m => m.DL_ID)
            @Html.HiddenFor(m => m.ShowType)
            @Html.HiddenFor(m => m.FileType)
            <div class="form-inline " role="form" id="Q_Div">
                <div class="form-group">
                    <label class="control-label">
                        @Html.DisplayNameFor(model => model.whereSearch)
                    </label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereSearch, new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1,'F')" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('F');" />
            </div>
            foreach (var Item in Model.GroupDataItem.Where(x => x.Key == "F"))
            {

                foreach (var groupIt in Item.Value)
                {
                    List<DLMT01> ListData = groupIt.Value;
                    <div class="panel panel-ACC" style="margin-bottom:10px;margin-top:10px;">
                        <div class="panel-heading text-center">
                            @groupIt.Key
                        </div>
                        <div class="table-responsive">
                            <table class="table-ecool table-92Per table-hover table-ecool-reader">
                                <thead>
                                    <tr>
                                        <th width="15%"></th>
                                        <th width="40%">文件名稱</th>
                                        <th width="15%">上傳日</th>
                                        <th width="30%">說明</th>


                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in ListData.OrderBy(x => x.CHG_DATE))
                                    {
                                        <tr>
                                            <td align="center" width="15%">
                                                @Html.ActionLink("下載", "DownLoad", (string)ViewBag.BRE_NO, new { DL_ID = item.DL_ID, FILE_NAME = item.FILENAME }, new { @class = "btn btn-xs btn-Basic" })

                                            </td>
                                            <td width="40%">
                                                @Html.DisplayFor(modelItem => item.DL_SUBJECT)
                                            </td>
                                            <td width="15%">@Html.DisplayFor(modelItem => item.CHG_DATE, "ShortDateTime")</td>
                                            <td width="30%">@Html.DisplayFor(modelItem => item.DL_MEMO)</td>


                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>

                }


            }
        }


    </div>
    <div id="tabs-1" >
        <div class="Title_Secondary">教學影片下載</div>
        @Html.Partial("_Notice")
        @using (Html.BeginForm("VedioADD", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "" }))
        {
            @Html.AntiForgeryToken()

            @*@Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.Page)
            @Html.HiddenFor(m => m.ShowDisable)
            @Html.HiddenFor(m => m.BackAction)
            @Html.HiddenFor(m => m.BackController)
            @Html.HiddenFor(m => m.DL_ID)*@

            <div class="form-inline " role="form" id="Q_Div">
                <div class="form-group">
                    <label class="control-label">
                        @Html.DisplayName("教學影片名稱")
                    </label>
                </div>
                <div class="form-group">
                    <input class="form-control text-box single-line" id="whereSearch1" name="whereSearch1" type="text" value="">
                    @*@Html.EditorFor(m => m.whereSearch, new { htmlAttributes = new { @class = "form-control" } })*@
                </div>
                <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1,'V')" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('V');" />
            </div>
            foreach (var Item in Model.GroupDataItem.Where(x => x.Key == "V"))
            {

                foreach (var groupIt in Item.Value)
                {
                    List<DLMT01> ListData = groupIt.Value;
                    <div class="panel panel-ACC" style="margin-bottom:10px;margin-top:10px; background-color:#fee8e93b;">
                        <div class="panel-heading text-center" style="background-color:#fee8e9">
                            @groupIt.Key
                        </div>
                        <div class="table-responsive">
                            <table class="table-ecool table-92Per table-hover table-ecool-reader" >
                                <thead>
                                    <tr>
                                        <th width="15%"></th>
                                        <th width="40%">文件名稱</th>
                                        <th width="15%">上傳日</th>
                                        <th width="30%">說明</th>


                                    </tr>
                                </thead>
                                <tbody style="background-color:#fee8e9">
                                    @foreach (var item in ListData.OrderBy(x => x.Orderby))
                                    {
                                        <tr>
                                            <td align="center" width="15%">
                                                @if (item.FILENAME != null && item.FILENAME != "")
                                                {
                                                    @Html.ActionLink("下載", "DownLoad", (string)ViewBag.BRE_NO, new { DL_ID = item.DL_ID, FILE_NAME = item.FILENAME }, new { @class = "btn btn-xs btn-Basic" })
                                                }
                                                @if (item.YOUTUBE_URL != null && item.YOUTUBE_URL != "")
                                                {
                                                    <a href="@item.YOUTUBE_URL" target="_blank" class="btn btn-xs btn-Basic">觀看</a>

                                                }
                                            </td>
                                            <td width="40%">
                                                @Html.DisplayFor(modelItem => item.DL_SUBJECT)
                                            </td>
                                            <td width="15%">@Html.DisplayFor(modelItem => item.CHG_DATE, "ShortDateTime")</td>
                                            <td width="30%">@Html.DisplayFor(modelItem => item.DL_MEMO)</td>


                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>

                }


            }
        }
    </div>
  
</div>



@section scripts{
    <script type="text/javascript">
        $(function () {
            $("#tabs").tabs();
        });
        var targetFormID = '#form1';

            function FunPageProc(page,FileType) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                $('#@Html.IdFor(m=>m.ShowType)').val(page)
                 $('#@Html.IdFor(m => m.FileType)').val("F")
                if (FileType == "V") {
                             var str = "";
                             str =  $("#whereSearch1").val();
                    $('#@Html.IdFor(m=>m.whereSearch)').val(str)
                    $('#@Html.IdFor(m => m.FileType)').val("V")
                }
            $(targetFormID).attr("action", "@Url.Action("Index2", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
                     }
        };

                 function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
                     FunPageProc(1);
                 }

                 function onBtnDetails(Value) {
            $('#DL_ID').val(Value)
                     $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
                     $(targetFormID).submit();
                 }

                 function delete_show(Value) {

                     if (confirm("您確定要刪除？") == true) {
                $('#DL_ID').val(Value)
                         $(targetFormID).attr("action", "@Url.Action("Delete", (string)ViewBag.BRE_NO)")
                         $(targetFormID).submit();
                     }
                 }

                 function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                         var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                         if (InPreadonly == false || InPreadonly == undefined) {

                             if (type == 'radio' || type == 'checkbox') {
                                 if ($(this).attr("title") == 'Default') {
                                     this.checked = true;
                                     }
                        else {
                                         this.checked = false;
                                         }
                                     }
                    else if (tag == 'select') { //下拉式選單
                                         this.selectedIndex = 0;
                                     }
                                     else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                                         this.value = '';
                                     }
                                 }
                             });

                             FunPageProc(1);
                         }

    </script>
}




