/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{8704:[676,0,599,5,594],8705:[785,29,539,63,476],8706:[686,10,559,44,559],8707:[676,0,599,76,523],8708:[803,127,599,76,523],8709:[594,90,787,50,737],8710:[676,0,681,23,658],8711:[676,0,681,23,658],8712:[547,13,750,82,668],8713:[680,146,750,82,668],8714:[499,-35,500,60,440],8715:[547,13,750,82,668],8716:[680,146,750,82,668],8717:[499,-35,500,60,440],8719:[763,259,1000,37,963],8720:[763,259,982,28,954],8721:[763,259,914,40,873],8722:[297,-209,750,66,685],8723:[657,12,770,65,685],8724:[793,57,750,65,685],8725:[732,193,584,78,506],8726:[411,-93,452,25,427],8727:[502,-34,585,82,503],8728:[409,-95,394,40,354],8729:[414,-91,493,85,408],8730:[946,259,965,130,1016],8733:[450,0,772,80,692],8734:[450,0,964,80,884],8735:[584,0,685,50,634],8736:[569,0,792,50,708],8737:[569,74,792,50,708],8738:[534,26,695,27,667],8739:[690,189,288,100,188],8740:[690,189,411,23,388],8741:[690,189,487,100,387],8742:[690,189,617,23,594],8743:[536,28,640,52,588],8744:[536,28,640,52,588],8745:[541,33,650,66,584],8746:[541,33,650,66,584],8747:[824,320,553,32,733],8748:[824,320,863,32,1043],8749:[824,320,1174,32,1354],8750:[824,320,591,30,731],8751:[824,320,903,32,1043],8752:[824,320,1214,32,1354],8753:[824,320,593,32,733],8754:[824,320,593,32,733],8755:[824,320,593,32,733],8756:[575,41,750,66,685],8757:[575,41,750,66,685],8758:[575,41,554,190,364],8759:[575,41,750,68,683],8760:[543,-209,750,66,685],8761:[543,37,750,66,686],8762:[575,41,750,66,685],8763:[565,59,750,67,682],8764:[374,-132,750,67,682],8765:[374,-132,750,67,682],8766:[419,-85,750,68,683],8767:[484,-67,750,66,684],8768:[575,40,348,53,295],8769:[444,-62,750,67,682],8770:[463,-45,750,68,683],8771:[463,-45,750,68,683],8772:[530,54,750,68,683],8773:[568,60,750,68,683],8774:[568,150,750,68,683],8775:[642,152,750,68,683],8776:[508,-26,750,68,683],8777:[583,48,750,68,683],8778:[568,75,750,68,683],8779:[613,109,750,68,683],8780:[568,60,750,68,683],8781:[518,13,750,68,683],8782:[484,-22,750,68,683],8783:[484,-107,750,68,683],8784:[667,-107,750,68,682],8785:[667,161,750,68,682],8786:[667,161,750,68,682],8787:[667,161,750,68,682],8788:[483,-50,932,68,864],8789:[483,-50,932,68,864],8790:[471,-63,750,68,682],8791:[809,-107,750,68,682],8792:[761,-107,750,68,682],8793:[836,-107,750,68,682],8794:[836,-107,750,68,682],8795:[841,-107,750,68,682],8796:[844,-107,750,68,682],8797:[838,-107,750,55,735],8798:[721,-107,750,68,682],8799:[880,-107,750,68,682],8800:[662,156,750,68,682],8801:[507,-27,750,68,682],8802:[688,156,750,68,682],8803:[592,57,750,68,682],8804:[627,121,750,80,670],8805:[627,120,750,80,670],8806:[729,222,750,80,670],8807:[729,222,750,80,670],8808:[729,294,750,80,670],8809:[729,294,750,80,670],8810:[534,24,1000,38,961],8811:[534,24,1000,38,961],8812:[732,193,417,46,371],8813:[591,87,750,68,683],8814:[625,115,750,80,670],8815:[625,115,750,80,670],8816:[717,235,750,80,670],8817:[717,235,750,80,670],8818:[690,182,750,67,682],8819:[690,182,750,67,682],8820:[780,282,750,67,682],8821:[780,282,750,67,682],8822:[734,226,750,80,670],8823:[734,226,750,80,670],8824:[824,316,750,80,670],8825:[824,316,750,80,670],8826:[531,23,750,80,670],8827:[531,23,750,80,670],8828:[645,138,750,80,670],8829:[645,138,750,80,670],8830:[676,169,750,67,682],8831:[676,169,750,67,682],8832:[625,115,750,80,670],8833:[625,115,750,80,670],8834:[547,13,750,82,668],8835:[547,13,750,82,668],8836:[680,146,750,82,668],8837:[680,146,750,82,668],8838:[647,101,750,82,668],8839:[647,101,750,82,668],8840:[747,201,750,82,668],8841:[747,201,750,82,668],8842:[734,200,750,82,668],8843:[734,200,750,82,668],8844:[541,33,650,66,584],8845:[541,33,650,66,584],8846:[541,33,650,66,584],8847:[532,27,750,87,663],8848:[532,27,750,87,663],8849:[644,93,750,87,663],8850:[644,93,750,87,663],8851:[541,33,650,66,584],8852:[541,33,650,66,584],8853:[634,130,864,50,814],8854:[634,130,864,50,814],8855:[634,130,864,50,814],8856:[634,130,864,50,814],8857:[594,90,784,50,734],8858:[634,130,842,39,803],8859:[634,130,864,50,814],8860:[634,130,864,50,814],8861:[634,130,864,50,814],8862:[661,158,910,45,865],8863:[661,158,910,45,865],8864:[661,158,910,45,865],8865:[661,158,910,45,865],8866:[676,0,750,91,659],8867:[676,0,750,91,659],8868:[676,0,750,91,659],8869:[676,0,750,91,659],8870:[676,0,555,91,464],8871:[676,0,555,91,464],8872:[676,0,750,91,659],8873:[676,0,972,91,882],8874:[676,0,944,91,856],8875:[676,0,944,91,856],8876:[676,0,913,21,822],8877:[676,0,912,21,822],8878:[676,0,1096,21,1024],8879:[676,0,1104,21,1016],8880:[543,38,750,98,652],8881:[543,38,750,98,652],8882:[534,24,750,81,669],8883:[534,24,750,81,669],8884:[621,113,750,81,669],8885:[621,113,750,81,669],8886:[436,-96,1216,50,1166],8887:[436,-96,1216,50,1166],8888:[436,-96,884,50,834],8889:[563,57,750,65,685],8890:[461,216,498,74,424],8891:[536,189,640,52,588],8892:[697,28,640,52,588],8893:[697,28,640,52,588],8894:[630,0,750,60,690],8895:[662,158,910,45,865],8896:[763,259,977,54,923],8897:[763,259,977,54,923],8898:[768,264,961,94,867],8899:[768,264,961,94,867],8900:[515,-17,584,43,541],8903:[595,63,750,66,685],8904:[604,72,870,67,803],8905:[604,72,870,57,817],8906:[604,72,870,53,813],8907:[604,72,870,97,773],8908:[604,72,870,97,773],8909:[463,-45,750,68,683],8910:[536,28,640,41,599],8911:[536,28,640,41,599],8912:[600,67,750,63,687],8913:[600,67,750,63,687],8914:[541,33,750,65,685],8915:[541,33,750,65,685],8916:[643,33,650,66,584],8917:[690,189,685,48,637],8918:[534,24,750,80,670],8919:[534,24,750,80,670],8920:[534,24,1336,40,1296],8921:[534,24,1336,40,1296],8922:[916,408,750,80,670],8923:[916,408,750,80,670],8924:[627,120,750,80,670],8925:[627,120,750,80,670],8926:[645,138,750,80,670],8927:[645,138,750,80,670],8928:[735,199,750,80,670],8929:[735,199,750,80,670],8930:[792,241,750,87,663],8931:[792,241,750,87,663],8934:[690,200,750,67,682],8935:[690,200,750,67,682],8936:[676,187,750,67,682],8937:[676,187,750,67,682],8938:[625,115,750,81,669],8939:[625,115,750,81,669],8940:[711,228,750,81,669],8941:[711,228,750,81,669],8942:[678,174,584,205,375],8943:[351,-181,977,62,914],8944:[579,75,977,162,815],8945:[579,75,977,162,815],8950:[735,13,750,82,668],8957:[735,13,750,82,668]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/MathOperators.js");
