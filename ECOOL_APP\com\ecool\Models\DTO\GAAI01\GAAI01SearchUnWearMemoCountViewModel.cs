﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01SearchUnWearMemoCountViewModel
    {
        /// <summary>
        ///防身周期ID
        /// </summary>
        [DisplayName("防身周期ID")]
        public string ALARM_ID { get; set; }

        /// <summary>
        ///週期
        /// </summary>
        [DisplayName("週期")]
        public byte? CYCLE { get; set; }

        /// <summary>
        ///登記日期_開始
        /// </summary>
        [DisplayName("週期日期_開始")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATES { get; set; }

        /// <summary>
        ///登記日期_結束
        /// </summary>
        [DisplayName("週期日期_結束")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATEE { get; set; }

        /// <summary>
        /// 未配戴原因
        /// </summary>
        public GAAT02_U.UnWearType UN_WEAR_TYPE { get; set; }

        /// <summary>
        /// 全校學生數
        /// </summary>
        public int STUDENT_NUMBER { get; set; }

        /// <summary>
        /// 各原因未配載人數
        /// </summary>
        public int WEAR_TYPE_STUDENT_NUMBER { get; set; }
    }
}