/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/SansSerif/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_SansSerif-bold-italic"]={directory:"SansSerif/BoldItalic",family:"STIXMathJax_SansSerif",weight:"bold",style:"italic",testString:"\u00A0\uE1F6\uE1F7\uE1F8\uE1F9\uE1FA\uE1FB\uE1FC\uE1FD\uE1FE\uE1FF\uD835\uDE3C\uD835\uDE3D\uD835\uDE3E\uD835\uDE3F",32:[0,0,250,0,0],160:[0,0,250,0,0],57846:[688,13,500,89,578],57847:[688,0,500,204,505],57848:[688,0,500,20,581],57849:[688,13,500,32,586],57850:[688,0,500,55,583],57851:[676,13,500,27,651],57852:[688,13,500,80,638],57853:[676,0,500,120,639],57854:[688,13,500,63,594],57855:[688,13,500,28,588],120380:[690,0,690,25,665],120381:[676,0,636,80,691],120382:[691,19,723,119,797],120383:[676,0,709,80,772],120384:[676,0,635,80,728],120385:[676,0,582,80,725],120386:[691,19,746,107,785],120387:[676,0,715,80,803],120388:[676,0,440,79,534],120389:[676,96,481,15,574],120390:[676,0,712,80,816],120391:[676,0,603,80,612],120392:[676,0,913,80,1001],120393:[676,18,724,80,812],120394:[692,18,778,106,840],120395:[676,0,581,80,695],120396:[691,176,779,105,839],120397:[676,0,670,80,698],120398:[691,19,554,66,637],120399:[676,0,641,157,785],120400:[676,19,699,123,792],120401:[676,18,690,193,833],120402:[676,15,997,198,1135],120403:[676,0,740,40,853],120404:[676,0,694,188,842],120405:[676,0,653,25,769],120406:[473,14,489,48,507],120407:[676,13,512,51,558],120408:[473,14,462,71,524],120409:[676,14,518,69,625],120410:[473,13,452,71,492],120411:[692,0,340,72,533],120412:[473,206,504,2,599],120413:[676,0,510,55,542],120414:[688,0,245,59,366],120415:[688,202,324,-90,440],120416:[676,0,519,55,599],120417:[676,0,235,55,348],120418:[473,0,776,55,809],120419:[473,0,510,55,542],120420:[473,14,501,72,542],120421:[473,205,512,3,559],120422:[473,205,512,69,574],120423:[473,0,411,55,519],120424:[473,13,385,37,442],120425:[631,12,386,98,447],120426:[462,15,518,81,569],120427:[462,14,462,129,561],120428:[462,14,701,131,798],120429:[462,0,506,20,582],120430:[462,204,472,-27,569],120431:[462,0,441,21,530],120720:[690,0,690,25,665],120721:[676,0,706,60,671],120722:[676,0,602,60,705],120723:[690,0,720,40,680],120724:[676,0,683,60,708],120725:[676,0,707,25,769],120726:[676,0,748,60,783],120727:[691,19,847,90,822],120728:[676,0,435,50,505],120729:[676,0,712,60,796],120730:[690,0,686,20,646],120731:[676,0,933,60,981],120732:[676,18,744,60,792],120733:[676,0,690,47,737],120734:[692,18,849,90,824],120735:[676,0,745,60,783],120736:[676,0,581,60,675],120737:[691,19,847,90,822],120738:[676,0,696,21,748],120739:[676,0,641,87,715],120740:[691,0,671,91,799],120741:[676,0,835,72,835],120742:[676,0,740,20,833],120743:[691,0,791,125,901],120744:[691,0,816,47,816],120745:[664,30,780,120,760],120746:[473,14,678,47,703],120747:[692,205,552,-12,581],120748:[473,204,525,84,571],120749:[692,14,507,30,547],120750:[473,14,504,45,508],120751:[692,205,480,49,539],120752:[473,205,532,38,525],120753:[692,14,560,65,553],120754:[462,14,325,56,302],120755:[473,0,537,38,582],120756:[692,14,574,18,540],120757:[462,205,594,-12,569],120758:[473,14,525,41,565],120759:[692,205,481,43,525],120760:[473,14,543,45,515],120761:[462,14,632,45,656],120762:[473,205,560,-33,536],120763:[473,205,517,52,554],120764:[462,14,614,45,639],120765:[462,14,523,42,547],120766:[473,14,550,61,526],120767:[473,205,683,55,659],120768:[473,205,575,-80,626],120769:[473,205,703,75,751],120770:[461,14,756,64,732],120771:[691,14,548,45,539],120772:[473,14,468,45,470],120773:[692,14,579,54,579],120774:[473,10,646,-10,665],120775:[692,205,678,48,654],120776:[473,205,544,38,520],120777:[462,14,889,40,912]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_SansSerif-bold-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/BoldItalic/Main.js"]);
