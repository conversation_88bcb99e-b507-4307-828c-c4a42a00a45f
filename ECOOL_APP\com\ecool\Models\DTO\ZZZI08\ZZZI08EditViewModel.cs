﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.com.ecool.Models.entity;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.DTO.ZZZI08
{
    public class ZZZI08EditViewModel
    {
        public string REF_KEY { get; set; }

        public string whereIndex { get; set; }

        public uADDT11 uADDT11 { get; set; }

        public List<ZZZI08ADDT11_FILEViewModel> FILE { get; set; }

        /// <summary>
        /// 判斷畫面是否第一次進入
        /// </summary>
        public string Submit_YN { get; set; }

        public List<ZZZI08ADDT12ViewModel> truefalseAns { get; set; }

        public List<ZZZI08ADDT12ViewModel> multipleschoiceAns { get; set; }

        /// <summary>
        /// A.新增 ,U 修改 D.刪除 資料處理
        /// </summary>
        public string DATA_TYPE { get; set; }

        /// <summary>
        /// 新增_Excel
        /// </summary>
        public static string DATA_TYPE_A_E = "A_E";

        /// <summary>
        /// 新增
        /// </summary>
        public static string DATA_TYPE_A = "A";

        /// <summary>
        /// 修改
        /// </summary>
        public static string DATA_TYPE_U = "U";

        /// <summary>
        /// 刪除
        /// </summary>
        public static string DATA_TYPE_D = "D";

        /// <summary>
        /// 隱藏
        /// </summary>
        public static string DATA_TYPE_H = "H";

        /// <summary>
        ///提早結案
        /// </summary>
        public static string DATA_TYPE_Z = "Z";

        /// <summary>
        ///更新截止日期
        /// </summary>
        public static string DATA_TYPE_E = "E";

        /// <summary>
        ///發佈
        /// </summary>
        public static string DATA_TYPE_R = "R";

        /// <summary>
        /// VIEW_A.新增畫面 ,VIEW_U 修改畫面 VIEW_D.刪除畫面 ,VIEW_P(已發佈),VIEW_Z(提早結案)
        /// PS.已發佈只能修改截止日期 ,及按提早結案
        /// </summary>
        public string VIEW_DATA_TYPE { get; set; }

        /// <summary>
        /// 新增畫面
        /// </summary>
        public static string VIEW_A = "VIEW_A";

        /// <summary>
        /// 複製畫面
        /// </summary>
        public static string VIEW_C = "VIEW_C";

        /// <summary>
        ///修改畫面
        /// </summary>
        public static string VIEW_U = "VIEW_U";

        /// <summary>
        /// 刪除畫面
        /// </summary>
        public static string VIEW_D = "VIEW_D";
    }

    public class ZZZI08ADDT12ViewModel
    {
        [DisplayName("刪除")]
        public bool Del { get; set; }

        [DisplayName("ITEM")]
        public int ITEM { get; set; }

        ///Summary
        ///DIALOG_ID
        ///Summary
        [DisplayName("活動代碼")]
        public string DIALOG_ID { get; set; }

        ///Summary
        ///Q_NUM
        ///Summary
        [DisplayName("NO")]
        public int Q_NUM { get; set; }

        ///Summary
        ///Q_TYPE
        ///Summary
        [DisplayName("題型代碼")]
        public int? Q_TYPE { get; set; }

        ///Summary
        ///Q_TYPE
        ///Summary
        [DisplayName("題型")]
        public string Q_TYPE_NAME { get; set; }

        ///Summary
        ///TRUE_ANS
        ///Summary
        [DisplayName("答案")]
        public string TRUE_ANS { get; set; }

        ///Summary
        ///Q_TEXT
        ///Summary
        [DisplayName("題目")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_TEXT { get; set; }

        ///Summary
        ///Q_ANS1
        ///Summary
        [DisplayName("選項1")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_ANS1 { get; set; }

        ///Summary
        ///Q_ANS2
        ///Summary
        [DisplayName("選項2")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_ANS2 { get; set; }

        ///Summary
        ///Q_ANS3
        ///Summary
        [DisplayName("選項3")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_ANS3 { get; set; }

        ///Summary
        ///Q_ANS4
        ///Summary
        [DisplayName("選項4")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_ANS4 { get; set; }

        public bool isCopy { get; set; }

        public bool ShowBtn { get; set; }

        public string Html_ID { get; set; }

        public ZZZI08ADDT12ViewModel()
        {
            ShowBtn = true;
            isCopy = false;
        }
    }

    public class ZZZI08ADDT11_FILEViewModel
    {
        ///Summary
        ///DIALOG_ID
        ///Summary
        [DisplayName("刪除")]
        public bool Del { get; set; }

        ///Summary
        ///DIALOG_ID
        ///Summary
        [DisplayName("活動代碼")]
        public string DIALOG_ID { get; set; }

        ///Summary
        ///檔名
        ///Summary
        public string FILE_NAME { get; set; }
    }
}