﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public partial class REFT01_Q
    {

        public class BTN_TYPE_VAL
        {
            /// <summary>
            /// 系統角色
            /// </summary>
            public static string sys_role = "sys_role";

            /// <summary>
            /// 學校角色
            /// </summary>
            public static string role = "role";

            /// <summary>
            /// 年級
            /// </summary>
            public static string grade = "grade";


            /// <summary>
            /// 班級
            /// </summary>
            public static string Class = "Class";

            /// <summary>
            /// 個人
            /// </summary>
            public static string person = "person";


            public static string GetDesc(string val)
            {
                if (val == sys_role)
                {
                    return "系統角色";
                }
                else if (val == role)
                {
                    return "學校角色";
                }
                else if (val == grade)
                {
                    return "年級";
                }
                else if (val == Class)
                {
                    return "班級";
                }
                else if (val == person)
                {
                    return "個人";
                }
                else
                {
                    return "";
                }
            }
        }
    }
}
