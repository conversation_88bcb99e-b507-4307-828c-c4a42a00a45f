﻿using ECOOL_APP.com.ecool.Models.DTO;
using PushSharp.Apple;
using System;
using ECOOL_APP.com.ecool.service;
using System.Text;
using Newtonsoft.Json.Linq;
using System.Threading;
using ECOOL_APP.EF;
using System.Collections.Generic;
using System.Linq;
using System.Data.SqlClient;
using com.ecool.sqlConnection;
using ECOOL_APP.com.ecool.util;
using System.Data;
using PushSharp.Google;
using PushSharp.Core;
using com.ecool.util;
using log4net;
using Hangfire;

namespace ECOOL_APP
{
    public class PushHelper
    {
        /// <summary>
        /// ios p12密碼
        /// </summary>
        private static string IosKeyPassword = "m8412374";

        private static bool IsProduction = GetProduction();

        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public void ToPushServer()
        {
            try
            {
                PushQueryModel Query = new PushQueryModel();
                IosPushServer(Query);
                AndroidPushServer(Query);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// 執行 Push
        /// </summary>
        /// <param name="BATCH_ID"></param>
        public static void ToPushServer(string BATCH_ID)
        {
            PushQueryModel Query = new PushQueryModel();
            Query.BATCH_ID = BATCH_ID;

            ToPushServer(Query);
        }

        /// <summary>
        /// 執行 Push
        /// </summary>
        /// <param name="Query"></param>
        public static void ToPushServer(PushQueryModel Query)
        {
            if (IsProduction)
            {
                Query.KeyMapPath = System.Web.HttpContext.Current.Request.MapPath(@"~\bin\ECoolPushKey.p12");
            }
            else
            {
                Query.KeyMapPath = System.Web.HttpContext.Current.Request.MapPath(@"~\bin\ECoolTestPushKey.p12");
            }

            logger.Info("發送開始");
            logger.Info("多執行前BATCH_ID=" + Query.BATCH_ID);

            object o = Query;
            ParameterizedThreadStart ParStart = new ParameterizedThreadStart(ThreadMethod);
            Thread PushThread = new Thread(ParStart);

            PushThread.Start(o);
        }

        /// <summary>
        /// 執行 Push Thread
        /// </summary>
        /// <param name="o"></param>
        private static void ThreadMethod(object o)
        {
            PushQueryModel Query = (PushQueryModel)o;

            logger.Info("多執行後BATCH_ID=" + Query.BATCH_ID);

            IosPushServer(Query);
            AndroidPushServer(Query);
        }

        private static bool GetProduction()
        {
            bool boolProduction = false;

            // 判斷 Cache 是否啟用
            if (System.Web.Configuration.WebConfigurationManager.AppSettings["IsAppProduction"] != null)
            {
                boolProduction = Convert.ToBoolean(System.Web.Configuration.WebConfigurationManager.AppSettings["IsAppProduction"]);
            }

            return boolProduction;
        }

        /// <summary>
        /// ios p12 路徑
        /// </summary>
        /// <param name="Query"></param>
        /// <returns></returns>
        private static string GetIosKeyPath(PushQueryModel Query)
        {
            if (Query == null) Query = new PushQueryModel();

            if (string.IsNullOrWhiteSpace(Query.KeyMapPath))
            {
                if (IsProduction)
                {
                    if (System.Web.HttpRuntime.AppDomainAppId != null)
                    {
                        Query.KeyMapPath = System.Web.Hosting.HostingEnvironment.MapPath(@"~\bin\ECoolPushKey.p12");
                    }
                    else
                    {
                        Query.KeyMapPath = System.Configuration.ConfigurationManager.AppSettings["WebSitePath"] + @"\bin\ECoolPushKey.p12";
                    }
                }
                else
                {
                    if (System.Web.HttpRuntime.AppDomainAppId != null)
                    {
                        Query.KeyMapPath = System.Web.Hosting.HostingEnvironment.MapPath(@"~\bin\ECoolTestPushKey.p12");
                    }
                    else
                    {
                        Query.KeyMapPath = System.Configuration.ConfigurationManager.AppSettings["WebSitePath"] + @"\bin\ECoolTestPushKey.p12";
                    }
                }
            }

            return Query.KeyMapPath;
        }

        private static void IosPushServer(PushQueryModel Query)
        {
            logger.Info("IOS");
            //檢查Token
            CkIosToken();

            try
            {
                string BATCH_ID = PushService.CreBATCH_ID();
                List<APPT02_PUSH_TEMP> ListTmp = new List<APPT02_PUSH_TEMP>();
                List<APPT02_ERROR> LisErr = new List<APPT02_ERROR>();
                string Msg = string.Empty;
                string sysPath = GetIosKeyPath(Query);
                int PushDb_Count = 0;
                int OK_Count = 0;
                int NG_Count = 0;

                var ApnsServer = IsProduction == true ? ApnsConfiguration.ApnsServerEnvironment.Production : ApnsConfiguration.ApnsServerEnvironment.Sandbox;

                // Configuration (NOTE: .pfx can also be used here)0
                var config = new ApnsConfiguration(ApnsServer,
                   sysPath, IosKeyPassword);
                config.InternalBatchSize = 1;
                config.ConnectionTimeout = 120;

                // Create a new broker
                var apnsBroker = new ApnsServiceBroker(config);

                // Wire up events
                apnsBroker.OnNotificationFailed += (notification, aggregateEx) =>
                {
                    aggregateEx.Handle(ex =>
                    {
                        JObject jobject = JObject.Parse(notification.Payload.ToString());
                        int NOTIFICATION_ID = int.Parse(jobject["aps"]["NOTIFICATION_ID"].ToString());

                        // See what kind of exception it was to further diagnose
                        if (ex is ApnsNotificationException)
                        {
                            var notificationException = (ApnsNotificationException)ex;

                            // Deal with the failed notification
                            var apnsNotification = notificationException.Notification;
                            var statusCode = notificationException.ErrorStatusCode;

                            Msg = "Apple Notification Failed: DeviceToken=" + apnsNotification.DeviceToken + ",NOTIFICATION_ID=" + NOTIFICATION_ID.ToString() + ", Code=" + statusCode + ",Message=" + notificationException.Message;

                            logger.Error(Msg);

                            var ThisListTmp = ListTmp.Where(w => w.BATCH_ID == BATCH_ID && w.DEVICE_TOKEN == notification.DeviceToken && w.NOTIFICATION_ID == NOTIFICATION_ID).FirstOrDefault();

                            if (ThisListTmp != null)
                            {
                                ThisListTmp.MSG = Msg;
                                ThisListTmp.SENTOK = 0;

                                PushService.PushAfterData(ThisListTmp, "0");
                            }
                        }
                        else
                        {
                            // Inner exception might hold more useful information like an ApnsConnectionException
                            Console.WriteLine("Apple Notification Failed for some unknown reason : {ex.InnerException}");
                            Msg = "Apple Notification Failed for some unknown reason : {" + ex.InnerException.ToString() + "}";

                            logger.Error(Msg);

                            var ThisListTmp = ListTmp.Where(w => w.BATCH_ID == BATCH_ID && w.DEVICE_TOKEN == notification.DeviceToken && w.NOTIFICATION_ID == NOTIFICATION_ID).FirstOrDefault();

                            if (ThisListTmp != null)
                            {
                                ThisListTmp.MSG = Msg;
                                ThisListTmp.SENTOK = 0;

                                PushService.PushAfterData(ThisListTmp, "0");
                            }
                        }

                        return true;
                    });
                };

                //成功
                apnsBroker.OnNotificationSucceeded += (notification) =>
                {
                    JObject jobject = JObject.Parse(notification.Payload.ToString());

                    int NOTIFICATION_ID = int.Parse(jobject["aps"]["NOTIFICATION_ID"].ToString());

                    Console.WriteLine("Apple Notification Sent!");
                    Msg = $"Apple Notification Sent : DeviceToken=[{ notification.DeviceToken}],NOTIFICATION_ID=[{NOTIFICATION_ID}] ,Code=[成功]";

                    logger.Info(Msg);

                    var ThisListTmp = ListTmp.Where(w => w.BATCH_ID == BATCH_ID && w.DEVICE_TOKEN == notification.DeviceToken && w.NOTIFICATION_ID == NOTIFICATION_ID).FirstOrDefault();

                    if (ThisListTmp != null)
                    {
                        ThisListTmp.MSG = Msg;
                        ThisListTmp.SENTOK = 1;

                        PushService.PushAfterData(ThisListTmp, "1");
                    }
                };

                // Start the broker
                apnsBroker.Start();

                var PushDb = new PushService().GetIOSListData(Query);
                PushDb_Count = PushDb.Count();

                logger.Info("IOS PushDb.Count=" + PushDb.Count().ToString());

                foreach (var Item in PushDb)
                {
                    try
                    {
                        APPT02_PUSH_TEMP Tmp = new APPT02_PUSH_TEMP();
                        Tmp.NOTIFICATION_ID = Convert.ToInt32(Item.NOTIFICATION_ID);
                        Tmp.BATCH_ID = BATCH_ID;
                        Tmp.DEVICE_TOKEN = Item.DEVICE_TOKEN;
                        Tmp.CRE_DATE = DateTime.Now;
                        Tmp.SENTOK = 0;
                        ListTmp.Add(Tmp);

                        Item.TITLE_TXT = StringHelper.MakePlainText(Item.TITLE_TXT ?? "");
                        Item.BODY_TXT = StringHelper.MakePlainText(Item.BODY_TXT ?? "");
                        int MaxByte = 4096;

                        string sb = "{\"aps\" : {\"alert\" : {\"title\" : \"" + Item.TITLE_TXT + "\",\"body\" : \"" + Item.BODY_TXT + "\"},\"sound\" : \"" + Item.SOUND + "\",\"badge\" : " + Item.BADGE.ToString() + ",\"NOTIFICATION_ID\" : " + Item.NOTIFICATION_ID.ToString() + "}}";
                        int sbLength = Encoding.Default.GetBytes(sb).Length;
                        int NowLength = 0;
                        if (sbLength >= MaxByte)
                        {
                            int TitLength = Encoding.Default.GetBytes(Item.TITLE_TXT).Length;
                            int BodyLength = Encoding.Default.GetBytes(Item.BODY_TXT).Length;

                            NowLength = sbLength - BodyLength;

                            if (NowLength < MaxByte) //ex 7000 -3700 = 3300 < 4096
                            {
                                //ex 4096-3300 = 796
                                Item.BODY_TXT = StringHelper.SubStr(Item.BODY_TXT, 0, MaxByte - NowLength);
                            }
                            else
                            {
                                //ex2. 7000- 2300 =4700  > 4096 =>BODY_TXT 改空值

                                Item.BODY_TXT = "";
                                //ex2. 4700 - 2400 = 2300

                                NowLength = NowLength - TitLength;

                                //ex2. 4096 -2300 =1796
                                if (MaxByte - NowLength > 0)
                                {
                                    Item.TITLE_TXT = StringHelper.SubStr(Item.TITLE_TXT, 0, MaxByte - NowLength);
                                }
                                else
                                {
                                    Item.TITLE_TXT = ""; //<== 不可能進來的
                                }
                            }

                            sb = "{\"aps\" : {\"alert\" : {\"title\" : \"" + Item.TITLE_TXT + "\",\"body\" : \"" + Item.BODY_TXT + "\"},\"sound\" : \"" + Item.SOUND + "\",\"badge\" : " + Item.BADGE.ToString() + "}}";
                        }

                        // Queue a notification to send
                        apnsBroker.QueueNotification(new ApnsNotification
                        {
                            DeviceToken = Item.DEVICE_TOKEN,
                            Payload = JObject.Parse(sb)
                        });
                    }
                    catch (System.Data.Entity.Validation.DbEntityValidationException ex)
                    {
                        var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                        var getFullMessage = string.Join("; ", entityError);
                        var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);

                        APPT02_ERROR Err = new APPT02_ERROR();
                        Err.NOTIFICATION_ID = Convert.ToInt32(Item.NOTIFICATION_ID);
                        Err.DEVICE_TOKEN = Item.DEVICE_TOKEN;
                        Err.ERROR = exceptionMessage;
                        LisErr.Add(Err);
                    }
                }

                //檢查Token
                CkIosToken();

                // Stop the broker, wait for it to finish
                // This isn't done after every message, but after you're
                // done with the broker

                apnsBroker.Stop();
            }
            catch (Exception ex)
            {
                logger.Error("IOS Push Error");
                logger.Error(ex);
            }
        }

        private static void AndroidPushServer(PushQueryModel Query)
        {
            logger.Info("Android");

            try
            {
                string BATCH_ID = PushService.CreBATCH_ID();
                List<APPT02_PUSH_TEMP> ListTmp = new List<APPT02_PUSH_TEMP>();
                List<APPT02_ERROR> LisErr = new List<APPT02_ERROR>();
                string Msg = string.Empty;
                string sysPath = GetIosKeyPath(Query);
                int PushDb_Count = 0;
                int OK_Count = 0;
                int NG_Count = 0;

                // Configuration
                var config = new GcmConfiguration("firebase-ecool", "AIzaSyB-X3S2uzyVeDvfPDie6j8pkZit9o5gE7c", null);
                //
                // Create a new broker
                var gcmBroker = new GcmServiceBroker(config);

                // Wire up events
                gcmBroker.OnNotificationFailed += (notification, aggregateEx) =>
                {
                    aggregateEx.Handle(ex =>
                    {
                        // See what kind of exception it was to further diagnose
                        if (ex is GcmNotificationException)
                        {
                            var notificationException = (GcmNotificationException)ex;

                            // Deal with the failed notification
                            var gcmNotification = notificationException.Notification;
                            var description = notificationException.Description;

                            foreach (var DeviceToken in gcmNotification.RegistrationIds)
                            {
                                Msg = "GCM Notification Failed: DeviceToken={" + DeviceToken + "},Message={" + notificationException.Message + "}";

                                logger.Error(Msg);

                                ListTmp.Where(w => w.BATCH_ID == BATCH_ID && w.DEVICE_TOKEN == DeviceToken).ToList().ForEach(u =>
                                {
                                    u.MSG = Msg;
                                    u.SENTOK = 0;
                                });
                            }
                        }
                        else if (ex is GcmMulticastResultException)
                        {
                            var multicastException = (GcmMulticastResultException)ex;

                            foreach (var succeededNotification in multicastException.Succeeded)
                            {
                                Msg = "GCM Notification Succeeded: ID=" + succeededNotification.MessageId;
                                logger.Info(Msg);

                                foreach (var DeviceToken in succeededNotification.RegistrationIds)
                                {
                                    Msg = "GCM Notification Succeeded: DeviceToken={" + DeviceToken + "},Message={Succeeded}";

                                    logger.Info(Msg);

                                    ListTmp.Where(w => w.BATCH_ID == BATCH_ID && w.DEVICE_TOKEN == DeviceToken).ToList().ForEach(u =>
                                    {
                                        u.MSG = Msg;
                                        u.SENTOK = 1;
                                    });
                                }
                            }

                            foreach (var failedKvp in multicastException.Failed)
                            {
                                var n = failedKvp.Key;
                                var e = failedKvp.Value;
                                Msg = "GCM Notification Failed: ID=" + n.MessageId + ", Desc=" + e.Message;
                                logger.Error(Msg);

                                foreach (var DeviceToken in n.RegistrationIds)
                                {
                                    Msg = "GCM Notification Failed: DeviceToken={" + DeviceToken + "},Message={" + e.Message + "}";

                                    logger.Error(Msg);

                                    ListTmp.Where(w => w.BATCH_ID == BATCH_ID && w.DEVICE_TOKEN == DeviceToken).ToList().ForEach(u =>
                                    {
                                        u.MSG = Msg;
                                        u.SENTOK = 0;
                                    });
                                }
                            }
                        }
                        else if (ex is DeviceSubscriptionExpiredException)
                        {
                            var expiredException = (DeviceSubscriptionExpiredException)ex;

                            var oldId = expiredException.OldSubscriptionId;
                            var newId = expiredException.NewSubscriptionId;

                            Msg = "Device RegistrationId Expired: " + oldId;
                            logger.Error(Msg);

                            if (!string.IsNullOrWhiteSpace(newId))
                            {
                                // If this value isn't null, our subscription changed and we should update our database
                                Msg = "Device RegistrationId Changed To: " + newId;
                                logger.Error(Msg);
                            }
                        }
                        else if (ex is RetryAfterException)
                        {
                            var retryException = (RetryAfterException)ex;
                            // If you get rate limited, you should stop sending messages until after the RetryAfterUtc date
                            Msg = "GCM Rate Limited, don't send more until after " + retryException.RetryAfterUtc;
                            logger.Error(Msg);
                        }
                        else
                        {
                            Msg = "GCM Notification Failed for some unknown reason";
                            logger.Error(Msg);
                        }

                        NG_Count = NG_Count + 1;

                        if (PushDb_Count == OK_Count + NG_Count)
                        {
                            logger.Info("Android 發送完畢，有發生錯誤");
                            PushService.PushAfterData(ListTmp, LisErr, BATCH_ID);
                        }

                        // Mark it as handled
                        return true;
                    });
                };

                gcmBroker.OnNotificationSucceeded += (notification) =>
                {
                    Console.WriteLine("GCM Notification Sent!");

                    foreach (var DeviceToken in notification.RegistrationIds)
                    {
                        Msg = "GCM Notification Sent : DeviceToken={" + DeviceToken + "}, Code={成功}";
                        logger.Info(Msg);

                        ListTmp.Where(w => w.BATCH_ID == BATCH_ID && w.DEVICE_TOKEN == DeviceToken).ToList().ForEach(u =>
                        {
                            u.MSG = Msg;
                            u.SENTOK = 1;
                        });
                    }

                    OK_Count = OK_Count + 1;

                    if (PushDb_Count == OK_Count)
                    {
                        logger.Info("Android 發送完畢，沒有錯誤");
                        PushService.PushAfterData(ListTmp, LisErr, BATCH_ID);
                    }
                    else if (PushDb_Count == OK_Count + NG_Count && NG_Count > 0)
                    {
                        logger.Info("Android 發送完畢，有發生錯誤");
                        PushService.PushAfterData(ListTmp, LisErr, BATCH_ID);
                    }
                };

                // Start the broker
                gcmBroker.Start();

                var PushDb = new PushService().GetAndroidListData(Query);
                PushDb_Count = PushDb.Count();
                logger.Info("Android PushDb.Count=" + PushDb_Count.ToString());

                foreach (var Item in PushDb)
                {
                    try
                    {
                        APPT02_PUSH_TEMP Tmp = new APPT02_PUSH_TEMP();
                        Tmp.NOTIFICATION_ID = Convert.ToInt32(Item.NOTIFICATION_ID);
                        Tmp.BATCH_ID = BATCH_ID;
                        Tmp.DEVICE_TOKEN = Item.DEVICE_TOKEN;
                        Tmp.CRE_DATE = DateTime.Now;
                        ListTmp.Add(Tmp);

                        Item.TITLE_TXT = StringHelper.MakePlainText(Item.TITLE_TXT ?? " ");
                        Item.BODY_TXT = StringHelper.MakePlainText(Item.BODY_TXT);

                        string sb = "{\"notification\" : {\"title\" : \"" + Item.TITLE_TXT + "\",\"body\" : \"" + Item.BODY_TXT + "\"},\"sound\" : \"" + Item.SOUND + "\",\"badge\" : " + Item.BADGE.ToString() + ",\"NOTIFICATION_ID\" : " + Item.NOTIFICATION_ID.ToString() + "}";
                        var sbLength = Encoding.Default.GetBytes(sb).Length;
                        if (sbLength >= 4096)
                        {
                            if (Encoding.Default.GetBytes(Item.TITLE_TXT).Length >= 3584)
                            {
                                Item.TITLE_TXT = Item.TITLE_TXT.Substring(0, 1700) + "...";
                            }

                            if (Encoding.Default.GetBytes(Item.BODY_TXT).Length >= 3584)
                            {
                                Item.BODY_TXT = Item.BODY_TXT.Substring(0, 1700) + "...";
                            }

                            sb = "{\"notification\" : {\"title\" : \"" + Item.TITLE_TXT + "\",\"body\" : \"" + Item.BODY_TXT + "\"},\"sound\" : \"" + Item.SOUND + "\",\"badge\" : " + Item.BADGE.ToString() + "}";
                        }

                        // Queue a notification to send
                        gcmBroker.QueueNotification(new GcmNotification
                        {
                            RegistrationIds = new List<string> {
                                Item.DEVICE_TOKEN
                            },
                            Data = JObject.Parse(sb)
                        });
                    }
                    catch (System.Data.Entity.Validation.DbEntityValidationException ex)
                    {
                        var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                        var getFullMessage = string.Join("; ", entityError);
                        var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);

                        APPT02_ERROR Err = new APPT02_ERROR();
                        Err.NOTIFICATION_ID = Convert.ToInt32(Item.NOTIFICATION_ID);
                        Err.ERROR = exceptionMessage;
                        LisErr.Add(Err);
                    }
                }

                // Stop the broker, wait for it to finish
                // This isn't done after every message, but after you're
                // done with the broker

                gcmBroker.Stop();
            }
            catch (Exception ex)
            {
                logger.Error("Android Push Error");
                logger.Error(ex);
            }
        }

        /// <summary>
        ///  Token 無效處理
        /// </summary>
        public static void CkIosToken()
        {
            string sysPath = GetIosKeyPath(null);

            var ApnsServer = IsProduction == true ? ApnsConfiguration.ApnsServerEnvironment.Production : ApnsConfiguration.ApnsServerEnvironment.Sandbox;

            var config = new ApnsConfiguration(
            ApnsServer, sysPath, IosKeyPassword);

            var fbs = new FeedbackService(config);

            fbs.FeedbackReceived += (string deviceToken, DateTime DateTime) =>
            {
                using (ECOOL_DEVEntities Db = new ECOOL_DEVEntities())
                {
                    HRMT05 t05 = Db.HRMT05.Where(a => a.DEVICE_TOKEN == deviceToken && a.STATUS == HRMT05.StatusVal.normal).FirstOrDefault();

                    if (t05 != null)
                    {
                        t05.STATUS = HRMT05.StatusVal.Invalid;
                        Db.SaveChanges();
                    }
                }
            };

            try
            {
                fbs.Check();
            }
            catch
            {
            }
        }
    }
}