(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Dragtable Mod for TableSorter - updated 10/31/2015 (v2.24.0) */
!function(w){"use strict";var p=w.tablesorter;p.dragtable={create:function(t){var a,e=t.originalTable.el,r=t.options.dragHandle.replace(".","");e.children("thead").children().children("th,td").each(function(){var e=w(this);e.find(t.options.dragHandle+",."+r+"-disabled").length||(a=!t.options.dragaccept||e.hasClass(t.options.dragaccept.replace(".","")),e.wrapI<PERSON>('<div class="'+t.options.sortClass.replace(".","")+'"/>').prepend('<div class="'+r+(a?"":"-disabled")+'"></div>'))})},start:function(e){(e=w(e)[0])&&e.config&&(e.config.widgetOptions.dragtableLast={search:w(e).data("lastSearch"),order:p.dragtable.getOrder(e)})},update:function(e){var t,a,r,i=e.originalTable,n=i.el[0],o=w(n),l=n.config,s=l&&l.widgetOptions,d=i.startIndex-1,c=i.endIndex-1,h=p.dragtable.getOrder(n)||[],b=p.hasWidget(o,"filter")||!1,u=s&&s.dragtableLast||{},g=[];(u.order||[]).join("")!==h.join("")&&(l.sortList.length&&(a=w.extend(!0,[],l.sortList),w.each(h,function(e,t){r=p.isValueInArray(parseInt(t,10),a),t!==u.order[e]&&0<=r&&(l.sortList[r][0]=e)})),b&&w.each(u.search||[],function(e){g[e]=u.search[h[e]]}),(t=!!p.hasWidget(l.$table,"editable")&&s.editable_columnsArray)&&(l.widgetOptions.editable_columnsArray=p.dragtable.reindexArrayItem(t,d,c)),(t=!!p.hasWidget(l.$table,"math")&&s.math_ignore)&&(l.widgetOptions.math_ignore=p.dragtable.reindexArrayItem(t,d,c)),(t=!!p.hasWidget(l.$table,"resizable")&&s.resizable_widths)&&(s.resizable_widths=p.dragtable.moveArrayItem(t,d,c)),p.updateAll(l,!1,function(){b&&setTimeout(function(){l.lastCombinedFilter=null,l.$table.data("lastSearch",g),p.setFilters(o,g),w.isFunction(e.options.tablesorterComplete)&&e.options.tablesorterComplete(l.table)},10)}))},getOrder:function(e){return w(e).children("thead").children("."+p.css.headerRow).children().map(function(){return w(this).attr("data-column")}).get()||[]},startColumnMove:function(e){var t,a=e.el[0].config,r=e.startIndex-1,i=e.endIndex-1,n=a.columns-1,o=i!==n&&i<=r,l=a.$table.children().children("tr");a.debug&&console.log("Inserting column "+r+(o?" before":" after")+" column "+i),l.each(function(){(t=w(this).children()).eq(r)[o?"insertBefore":"insertAfter"](t.eq(i))}),(t=a.$table.children("colgroup").children()).eq(r)[o?"insertBefore":"insertAfter"](t.eq(i))},swapNodes:function(e,t){var a,r,i,n=e.length;for(a=0;a<n;a++)r=e[a].parentNode,i=e[a].nextSibling===t[a]?e[a]:e[a].nextSibling,t[a].parentNode.insertBefore(e[a],t[a]),r.insertBefore(t[a],i)},moveArrayItem:function(e,t,a){var r,i=e.length;if(i<=a)for(r=a-i;1+r--;)e.push(void 0);return e.splice(a,0,e.splice(t,1)[0]),e},reindexArrayItem:function(e,a,r){var t=w.inArray(r,e),i=w.inArray(a,e),n=(Math.max.apply(Math,e),[]);return 0<=t&&0<=i?e:(w.each(e,function(e,t){r<a?r<=t?n.push(t+(t<a?1:0)):n.push(t):a<r&&(t===a?n.push(r):t<r&&a<=t?n.push(t-1):t<=r?n.push(t):a<t&&n.push(t+(t<r?0:1)))}),n.sort())}},
/*! dragtable v2.0.14 Mod */
w.widget("akottr.dragtable",{options:{revert:!1,dragHandle:".table-handle",maxMovingRows:40,excludeFooter:!1,onlyHeaderThreshold:100,dragaccept:null,persistState:null,restoreState:null,exact:!0,clickDelay:10,containment:null,cursor:"move",cursorAt:!1,distance:0,tolerance:"pointer",axis:"x",beforeStart:w.noop,beforeMoving:w.noop,beforeReorganize:w.noop,beforeStop:w.noop,tablesorterComplete:null,sortClass:".sorter"},originalTable:{el:null,selectedHandle:null,sortOrder:null,startIndex:0,endIndex:0},sortableTable:{el:w(),selectedHandle:w(),movingRow:w()},persistState:function(){var t=this;this.originalTable.el.find("th").each(function(e){""!==this.id&&(t.originalTable.sortOrder[this.id]=e)}),w.ajax({url:this.options.persistState,data:this.originalTable.sortOrder})},_restoreState:function(e){for(var t in e)t in e&&(this.originalTable.startIndex=w("#"+t).closest("th").prevAll().length+1,this.originalTable.endIndex=parseInt(e[t],10)+1,this._bubbleCols())},_bubbleCols:function(){p.dragtable.startColumnMove(this.originalTable)},_rearrangeTableBackroundProcessing:function(){var e=this;return function(){e._bubbleCols(),e.options.beforeStop(e.originalTable),e.sortableTable.el.remove(),function(){w("#__dragtable_disable_text_selection__").remove(),t?w(document.body).attr("onselectstart",t):w(document.body).removeAttr("onselectstart");a?w(document.body).attr("unselectable",a):w(document.body).removeAttr("unselectable")}(),p.dragtable.update(e),w.isFunction(e.options.persistState)?e.options.persistState(e.originalTable):e.persistState()}},_rearrangeTable:function(){var e=this;return function(){e.originalTable.selectedHandle.removeClass("dragtable-handle-selected"),e.sortableTable.el.sortable("disable"),e.sortableTable.el.addClass("dragtable-disabled"),e.options.beforeReorganize(e.originalTable,e.sortableTable),e.originalTable.endIndex=e.sortableTable.movingRow.prevAll().length+1,setTimeout(e._rearrangeTableBackroundProcessing(),50)}},_generateSortable:function(e){e.cancelBubble?e.cancelBubble=!0:e.stopPropagation();for(var t=this,a=this.originalTable.el[0].attributes,r="",i=0;i<a.length;i++)(a[i].value||a[i].nodeValue)&&"id"!=a[i].nodeName&&"width"!=a[i].nodeName&&(r+=a[i].nodeName+'="'+(a[i].value||a[i].nodeValue)+'" ');var n=[],o=[];t.originalTable.el.children("thead, tbody").children("tr:visible").slice(0,t.options.maxMovingRow).each(function(){for(var e=this.attributes,t="",a=0;a<e.length;a++)(e[a].value||e[a].nodeValue)&&"id"!=e[a].nodeName&&(t+=" "+e[a].nodeName+'="'+(e[a].value||e[a].nodeValue)+'"');n.push(t),o.push(w(this).height())});var l=[],s=0,d=t.originalTable.el.children(),c=d.filter("thead").children("tr:visible"),h=d.filter("tbody").children("tr:visible");if(c.eq(0).children("th, td").filter(":visible").each(function(){var e=w(this).outerWidth();l.push(e),s+=e}),t.options.exact){var b=s-t.originalTable.el.outerWidth();l[0]-=b}s+=2;var u=0;d.filter("caption").each(function(){u+=w(this).outerHeight()});var g,p='<ul class="dragtable-sortable" style="position:absolute; width:'+s+'px;">',f=[],m=c.eq(0).children("th, td").length;for(i=0;i<m;i++){var v=c.children(":nth-child("+(i+1)+")");v.is(":visible")&&(g=0,f[i]='<li style="width:'+v.outerWidth()+'px;"><table '+r+">"+(u?'<caption style="height:'+u+'px;"></caption>':"")+"<thead>",c.each(function(e){f[i]+="<tr "+n[g++]+(o[e]?' style="height:'+o[e]+'px;"':"")+">"+v[e].outerHTML+"</tr>"}),f[i]+="</thead><tbody>",v=h.children(":nth-child("+(i+1)+")"),1<t.options.maxMovingRows&&(v=v.add(h.children(":nth-child("+(i+1)+")").slice(0,t.options.maxMovingRows-1))),v.each(function(e){f[i]+="<tr "+n[g++]+(o[e]?' style="height:'+o[e]+'px;"':"")+">"+this.outerHTML+"</tr>"}),f[i]+="</tbody>",t.options.excludeFooter||(f[i]+="<tfoot><tr "+n[g++]+">"+d.filter("tfoot").children("tr:visible").children()[i].outerHTML+"</tr></tfoot>"),f[i]+="</table></li>")}p+=f.join("")+"</ul>",this.sortableTable.el=this.originalTable.el.before(p).prev(),this.sortableTable.el.find("> li > table").each(function(e){w(this).css("width",l[e]+"px")}),this.sortableTable.selectedHandle=this.sortableTable.el.find("th .dragtable-handle-selected");var T,y=this.options.dragaccept?"li:has("+this.options.dragaccept+")":"li";this.sortableTable.el.sortable({items:y,stop:this._rearrangeTable(),revert:this.options.revert,tolerance:this.options.tolerance,containment:this.options.containment,cursor:this.options.cursor,cursorAt:this.options.cursorAt,distance:this.options.distance,axis:this.options.axis}),this.originalTable.startIndex=w(e.target).closest("th,td").prevAll().length+1,this.options.beforeMoving(this.originalTable,this.sortableTable),this.sortableTable.movingRow=this.sortableTable.el.children("li:nth-child("+this.originalTable.startIndex+")"),T=w('<style id="__dragtable_disable_text_selection__" type="text/css">body { -ms-user-select:none;-moz-user-select:-moz-none;-khtml-user-select:none;-webkit-user-select:none;user-select:none; }</style>'),w(document.head).append(T),w(document.body).attr("onselectstart","return false;").attr("unselectable","on"),window.getSelection?window.getSelection().removeAllRanges():document.selection.empty(),this.sortableTable.movingRow.trigger(w.extend(w.Event(e.type),{which:1,clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY,screenX:e.screenX,screenY:e.screenY}));var x=this.sortableTable.el.find(".ui-sortable-placeholder");0<x.height()&&x.css("height",this.sortableTable.el.find(".ui-sortable-helper").height()),x.html('<div class="outer" style="height:100%;"><div class="inner" style="height:100%;"></div></div>')},bindTo:{},_create:function(){var t=this;t.originalTable={el:t.element,selectedHandle:w(),sortOrder:{},startIndex:0,endIndex:0},p.dragtable.create(t),t.bindTo="> thead > tr > "+(t.options.dragaccept||"th, td"),t.element.find(t.bindTo).find(t.options.dragHandle).length&&(t.bindTo+=" "+t.options.dragHandle),w.isFunction(t.options.restoreState)?t.options.restoreState(t.originalTable):t._restoreState(t.options.restoreState),t.originalTable.el.on("mousedown.dragtable",t.bindTo,function(e){1===e.which&&(p.dragtable.start(t.originalTable.el),!1!==t.options.beforeStart(t.originalTable)&&(clearTimeout(t.downTimer),t.downTimer=setTimeout(function(){t.originalTable.selectedHandle=w(t),t.originalTable.selectedHandle.addClass("dragtable-handle-selected"),t._generateSortable(e)},t.options.clickDelay)))}).on("mouseup.dragtable",t.options.dragHandle,function(){clearTimeout(t.downTimer)})},redraw:function(){this.destroy(),this._create()},destroy:function(){this.originalTable.el.off("mousedown.dragtable mouseup.dragtable",this.bindTo),w.Widget.prototype.destroy.apply(this,arguments)}});var t=w(document.body).attr("onselectstart"),a=w(document.body).attr("unselectable")}(jQuery);return jQuery;}));
