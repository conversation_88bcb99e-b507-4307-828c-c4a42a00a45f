﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class AccreditationTypeListViewModel
    {
        /// <summary>
        ///類別ID
        /// </summary>
        [DisplayName("護照ID")]
        public string TYPE_ID { get; set; }

        /// <summary>
        ///護照名稱
        /// </summary>
        [DisplayName("護照名稱")]
        public string TYPE_NAME { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///建立人員
        /// </summary>
        [DisplayName("建立人員")]
        public string CRE_PERSON { get; set; }

        [DisplayName("建立人員")]
        public string CRE_PERSON_NAME { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人員
        /// </summary>
        [DisplayName("修改人員")]
        public string CHG_PERSON { get; set; }

        [DisplayName("修改人員")]
        public string CHG_PERSON_NAME { get; set; }

        /// <summary>
        ///修改時間
        /// </summary>
        [DisplayName("修改時間")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CHG_DATE { get; set; }

        [DisplayName("一個✓獲得的點數")]
        public short CASH { get; set; }
    }
}