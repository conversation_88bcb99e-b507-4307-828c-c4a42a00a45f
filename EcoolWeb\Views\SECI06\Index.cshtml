﻿@model SECI06IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    Layout = "~/Views/Shared/_LayoutSEO.cshtml";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }
}
<script src="~/Scripts/grids.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.HiddenFor(m => m.WhereIsColorboxForUser)

    <div class="form-inline" role="form" style="@(user.USER_TYPE == UserType.Student ? "display:none":"")">
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.WhereSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", @onchange = "SelectSCHOOL_NO(this.value)" })
        </div>

        @if (user.USER_TYPE != UserType.Parents)
        {
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeUSER_NOUseReplaceWith()" })
            </div>
        }

        <div class="form-group">
            <label class="control-label">姓名</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.WhereUSER_NO, (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        @Html.ActionLink("成人BMI計算", "BMICal", "SECI03", null, new { @class = "btn-danger btn btn-sm", @role = "button" })
    </div>

    if (string.IsNullOrWhiteSpace(Model.WhereUSER_NO))
    {
        <div class="text-center">
            <h3>請選擇查詢同學姓名</h3>
        </div>
    }

    if (!string.IsNullOrWhiteSpace(Model.WhereUSER_NO))
    {
        @Html.Partial("_SECI06UserSportsInfo", Model)
    }

}

<script language="javascript">

    window.onload = function () {
          if ('@user.USER_TYPE' != '@UserType.Parents'  && '@user.USER_TYPE' != '@UserType.Student') {
            ChangeUSER_NOUseReplaceWith()
        }
    }
    $("#BMIbtn").colorbox({ opacity: 0.82, maxWidth: '90%', maxHeight: '90%', width:"877px", height:"843px" });

        function onInModal() {
            $('#CkeditorModal').modal('show');
        }

    function PrintBooK() {
        $('#tbData').printThis();
    }

  function todoClear() {
        ////重設
        $("#form1").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }

    function SetUSER_NODDLEmpty() {
        $('#@Html.IdFor(m=>m.WhereUSER_NO)').empty();
        $('#@Html.IdFor(m => m.WhereUSER_NO)').append($('<option></option>').val(' ').text('請選擇' + $("label[for='Search_CLASS_NO']").text() + '...').prop('selected', true));
    }

    function ChangeUSER_NOUseReplaceWith() {

        var selectedSCHOOL_NO = $.trim($('#@Html.IdFor(m => m.WhereSCHOOL_NO) option:selected').val());
        var selectedCLASS_NO = $.trim($('#@Html.IdFor(m => m.WhereCLASS_NO) option:selected').val());
        var selectedUSER_NO = $.trim($('#@Html.IdFor(m => m.WhereUSER_NO) option:selected').val());

        if (selectedCLASS_NO.length == 0) {
            SetUSER_NODDLEmpty();
        }
        else {
            $.ajax(
            {
                url: '@Url.Action("_GetUSER_NODDLHtml", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: '@Html.IdFor(m => m.WhereUSER_NO)',
                        tagName: '@Html.IdFor(m => m.WhereUSER_NO)',
                        SCHOOL_NO: selectedSCHOOL_NO,
                        CLASS_NO: selectedCLASS_NO,
                        USER_NO: selectedUSER_NO
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        if (data.length > 0) {
                            $('#@Html.IdFor(m => m.WhereUSER_NO)').replaceWith(data);
                        }
                    }
                });
        }
    }

        function onBtnLinkHrmt08More(id) {

               window.open('@Url.Action("Details", "SECI03")' + '?whereIDNO=' + id  + '', '_blank');
        }

           function onBtnLinkHrmt09More(id)
           {
               window.open('@Url.Action("Details", "SECI03")' + '?whereIDNO=' + id + '&whereShowType=@ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness', '_blank');
        }

           function onBtnLinkAddt24More(SCHOOL_NO, USER_NO)
           {
               window.open('@Url.Action("MyRunLog", "ADDI11")' + '?WhereSCHOOL_NO=' + SCHOOL_NO + '&WhereUSER_NO=' + USER_NO+'', '_blank');
           }
</script>