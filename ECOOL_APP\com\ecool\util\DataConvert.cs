﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class DataConvert
    {
        public static bool YNConvertToBoolean(string Val)
        {
            if (string.IsNullOrWhiteSpace(Val))
            {
                return false;
            }
            else
            {
                if (Val == "Y")
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        public static string BoolConvertToYN(bool? Val)
        {

            if (Val == null) return "N";

            if ((bool)Val)
            {
                return "Y";
            }
            else
            {
                return "N";
            }
        }

        public static string BoolConvertToYN(string Val)
        {

            if (string.IsNullOrWhiteSpace(Val)) return "N";

            bool ThisBool =false;
            bool.TryParse(Val, out ThisBool);

            return BoolConvertToYN(ThisBool);
            
        }

    }
}
