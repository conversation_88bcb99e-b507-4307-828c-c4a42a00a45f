﻿@model GameLeveViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    Uri contextUri = HttpContext.Current.Request.Url;

    var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
    contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port);

    string ValueStr = baseUri + Url.Action("LevelView", "Game").ToString() + $"?LEVEL_NO={Model.LEVEL_NO}&&GAME_NO={Model.GAME_NO}&&IsQRCODE=true";

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();
}
<link href="~/Content/css/childrens-month.css" rel="stylesheet" />
@using (Html.BeginForm("CreQRCode", "Game", FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off" }))
{
    @Html.Hidden("GAME_NO", (string)Model.GAME_NO)
}

<div class="panel with-nav-tabs panel-info">
 
    <div class="panel-body" style="background-color:#eef6fa;">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Action("Cre", "Barcode", new { Value = ValueStr })" />
        </div>
        <br />
        <div class="form-group text-center">
            <h4>請掃描此 QR CODE 來產生，參加闖關的 QR CODE 碼</h4>
        </div>
    </div>
</div>

<div id="DivAddButton">
    <i id="title" class="fa fa-arrow-left fa-3x"></i>
    <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

          function OnBack() {
             $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")
             $(targetFormID).submit();
         }
    </script>
}