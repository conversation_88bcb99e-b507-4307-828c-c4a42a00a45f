﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using ECOOL_APP.EF;
using System.Data.SqlClient;
using ECOOL_APP;

namespace com.ecool.service
{
    public class ZZZI14ViewModelService
    {
        public string ErrorMsg;
        private int ErrorInt = 0;

        #region 取得功能清單

        /// <summary>
        /// 取得功能清單
        /// </summary>
        /// <param name="BRE_NO"></param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="Count"></param>
        /// <returns></returns>
        public List<ZZZI14ViewModel> GetListData(string BRE_NO, int page, int pageSize, ref int Count)
        {
            List<ZZZI14ViewModel> list_data = new List<ZZZI14ViewModel>();

            ZZZI14ViewModel ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("   SELECT A.BRE_NO,A.BRE_NAME,A.CONTROLLER ,A.ACTION_ID AS DF_ACTION_ID,A.LEVEL_ID,A.LEVEL_NO,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY ");
                sb.Append(" ,A.TARGET,A.BRE_TYPE,A.FUN_DESC,isnull(A.ENABLE,1) ENABLE ");
                sb.Append(" ,A.GROUP_DESC ,A.WORK_DATES,A.WORK_DATEE,A.CHG_PERSON,A.CHG_DATE,A.CRE_PERSON,A.CRE_DATE ");
                sb.Append(" FROM ZZT01 A (NOLOCK)");
                sb.Append(" WHERE 1=1");

                if (BRE_NO != string.Empty)
                {
                    sb.AppendFormat(" AND A.BRE_NO ='{0}' ", BRE_NO);
                }

                string bef_BRE_NO = string.Empty;
                string ThisError = "";
                dt = new sqlConnection.sqlConnection().executeQueryBSqlDataReaderListPage(page, pageSize, sb.ToString(), sb.ToString(), "A.LEVEL_NO", ref Count, ref ThisError);
                foreach (DataRow dr in dt.Rows)
                {
                    ReturnData = new ZZZI14ViewModel();

                    ReturnData.BRE_NO = (dr["BRE_NO"] == DBNull.Value ? "" : (string)dr["BRE_NO"]);
                    ReturnData.BRE_NAME = (dr["BRE_NAME"] == DBNull.Value ? "" : (string)dr["BRE_NAME"]);
                    ReturnData.CONTROLLER = (dr["CONTROLLER"] == DBNull.Value ? "" : (string)dr["CONTROLLER"]);
                    ReturnData.ACTION_ID = (dr["DF_ACTION_ID"] == DBNull.Value ? "" : (string)dr["DF_ACTION_ID"]);
                    ReturnData.LEVEL_ID = (dr["LEVEL_ID"] == DBNull.Value ? Decimal.MaxValue : (Decimal)dr["LEVEL_ID"]);
                    ReturnData.LEVEL_NO = (dr["LEVEL_NO"] == DBNull.Value ? "" : (string)dr["LEVEL_NO"]);
                    ReturnData.LINK_ADDR = (dr["LINK_ADDR"] == DBNull.Value ? "" : (string)dr["LINK_ADDR"]);
                    ReturnData.BRE_NO_PRE = (dr["BRE_NO_PRE"] == DBNull.Value ? "" : (string)dr["BRE_NO_PRE"]);
                    ReturnData.ORDER_BY = (dr["ORDER_BY"] == DBNull.Value ? Decimal.MaxValue : (Decimal)dr["ORDER_BY"]);
                    ReturnData.TARGET = (dr["TARGET"] == DBNull.Value ? "" : (string)dr["TARGET"]);
                    ReturnData.BRE_TYPE = (dr["BRE_TYPE"] == DBNull.Value ? "" : (string)dr["BRE_TYPE"]);
                    ReturnData.BRE_TYPE_NAME = uZZT01.ParserBRE_TYPE(Convert.ToByte(dr["BRE_TYPE"]));
                    ReturnData.FUN_DESC = (dr["FUN_DESC"] == DBNull.Value ? "" : (string)dr["FUN_DESC"]);
                    ReturnData.GROUP_DESC = (dr["GROUP_DESC"] == DBNull.Value ? "" : (string)dr["GROUP_DESC"]);
                    ReturnData.WORK_DATES = (dr["WORK_DATES"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["WORK_DATES"]);
                    ReturnData.WORK_DATEE = (dr["WORK_DATEE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["WORK_DATEE"]);
                    ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                    ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);
                    ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                    ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                    ReturnData.ENABLE = (dr["ENABLE"] == DBNull.Value ? true : (bool)dr["ENABLE"]);
                    list_data.Add(ReturnData);
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        #endregion 取得功能清單

        #region 取得功能清單.明細資料

        /// <summary>
        /// 取得功能清單.明細資料
        /// </summary>
        /// <param name="BRE_NO"></param>
        /// <returns></returns>
        public ZZZI14ViewModel GetDetailsData(string BRE_NO)
        {
            ZZZI14ViewModel ReturnData = new ZZZI14ViewModel();
            ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("   SELECT A.BRE_NO,A.BRE_NAME,A.CONTROLLER ,A.ACTION_ID AS DF_ACTION_ID,A.LEVEL_ID,A.LEVEL_NO,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY ");
                sb.Append(" ,A.TARGET,A.BRE_TYPE,A.FUN_DESC ");
                sb.Append(" ,A.GROUP_DESC ,A.WORK_DATES,A.WORK_DATEE,A.CHG_PERSON,A.CHG_DATE,A.CRE_PERSON,A.CRE_DATE,A.APP_USE_YN ");
                sb.Append(" ,B.ACTION_ID,B.ACTION_NAME,B.ACTION_TYPE,A.ENABLE");
                sb.Append(" FROM ZZT01 A (NOLOCK)");
                sb.Append(" LEFT OUTER JOIN  ZZT34 B  (NOLOCK)  ON A.BRE_NO=B.BRE_NO  ");
                sb.Append(" WHERE 1=1");

                if (BRE_NO != string.Empty)
                {
                    sb.AppendFormat(" AND A.BRE_NO ='{0}' ", BRE_NO);
                }
                string bef_BRE_NO = string.Empty;
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    if (bef_BRE_NO != dr["BRE_NO"].ToString())
                    {
                        ReturnData = new ZZZI14ViewModel();
                        ReturnData.Details_List = new List<ZZZI14ViewModel_D>();

                        ReturnData.BRE_NO = (dr["BRE_NO"] == DBNull.Value ? "" : (string)dr["BRE_NO"]);
                        ReturnData.BRE_NAME = (dr["BRE_NAME"] == DBNull.Value ? "" : (string)dr["BRE_NAME"]);
                        ReturnData.CONTROLLER = (dr["CONTROLLER"] == DBNull.Value ? "" : (string)dr["CONTROLLER"]);
                        ReturnData.ACTION_ID = (dr["DF_ACTION_ID"] == DBNull.Value ? "" : (string)dr["DF_ACTION_ID"]);
                        ReturnData.LEVEL_ID = (dr["LEVEL_ID"] == DBNull.Value ? Decimal.MaxValue : (Decimal)dr["LEVEL_ID"]);
                        ReturnData.LEVEL_NO = (dr["LEVEL_NO"] == DBNull.Value ? "" : (string)dr["LEVEL_NO"]);
                        ReturnData.LINK_ADDR = (dr["LINK_ADDR"] == DBNull.Value ? "" : (string)dr["LINK_ADDR"]);
                        ReturnData.BRE_NO_PRE = (dr["BRE_NO_PRE"] == DBNull.Value ? "" : (string)dr["BRE_NO_PRE"]);
                        ReturnData.ORDER_BY = (dr["ORDER_BY"] == DBNull.Value ? Decimal.MaxValue : (Decimal)dr["ORDER_BY"]);
                        ReturnData.TARGET = (dr["TARGET"] == DBNull.Value ? "" : (string)dr["TARGET"]);
                        ReturnData.BRE_TYPE = (dr["BRE_TYPE"] == DBNull.Value ? "" : (string)dr["BRE_TYPE"]);
                        ReturnData.BRE_TYPE_NAME = uZZT01.ParserBRE_TYPE(Convert.ToByte(dr["BRE_TYPE"]));
                        ReturnData.FUN_DESC = (dr["FUN_DESC"] == DBNull.Value ? "" : (string)dr["FUN_DESC"]);
                        ReturnData.GROUP_DESC = (dr["GROUP_DESC"] == DBNull.Value ? "" : (string)dr["GROUP_DESC"]);
                        ReturnData.WORK_DATES = (dr["WORK_DATES"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["WORK_DATES"]);
                        ReturnData.WORK_DATEE = (dr["WORK_DATEE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["WORK_DATEE"]);
                        ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                        ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);
                        ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                        ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                        ReturnData.APP_USE_YN = (dr["APP_USE_YN"] == DBNull.Value ? "" : (string)dr["APP_USE_YN"]);
                        ReturnData.ENABLE = (dr["ENABLE"] == DBNull.Value ? true : (bool)dr["ENABLE"]);
                    }

                    if (dr["ACTION_ID"] != DBNull.Value && string.IsNullOrWhiteSpace((string)dr["ACTION_ID"]) == false)
                    {
                        ZZZI14ViewModel_D Details = new ZZZI14ViewModel_D();

                        Details.ACTION_ID = (dr["ACTION_ID"] == DBNull.Value ? "" : (string)dr["ACTION_ID"]);
                        Details.ACTION_NAME = (dr["ACTION_NAME"] == DBNull.Value ? "" : (string)dr["ACTION_NAME"]);
                        Details.ACTION_TYPE = (dr["ACTION_TYPE"] == DBNull.Value ? "" : (string)dr["ACTION_TYPE"]);

                        if (ReturnData.ACTION_ID == Details.ACTION_ID)
                        {
                            Details.DF_ACTION_ID = Details.ACTION_ID;
                            Details.DF_ACTION_ID_Check = true;
                        }

                        ReturnData.Details_List.Add(Details);
                    }

                    bef_BRE_NO = (string)dr["BRE_NO"];
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return ReturnData;
        }

        #endregion 取得功能清單.明細資料

        #region 查詢條件來源SQL

        /// <summary>
        /// 查詢條件來源SQL
        /// </summary>
        /// <param name="BRE_NO"></param>
        /// <returns></returns>
        public string SouSqlZZZI14(string BRE_NO)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("   SELECT A.BRE_NO,A.BRE_NAME,A.CONTROLLER ,A.ACTION_ID AS DF_ACTION_ID,A.LEVEL_ID,A.LEVEL_NO,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY ");
            sb.Append(" ,A.TARGET,A.BRE_TYPE,A.FUN_DESC ");
            sb.Append(" ,A.GROUP_DESC ,A.WORK_DATES,A.WORK_DATEE,A.CHG_PERSON,A.CHG_DATE,A.CRE_PERSON,A.CRE_DATE,A.APP_USE_YN ");
            sb.Append(" ,B.ACTION_ID,B.ACTION_NAME,B.ACTION_TYPE");
            sb.Append(" FROM ZZT01 A (NOLOCK)");
            sb.Append(" LEFT OUTER JOIN  ZZT34 B  (NOLOCK)  ON A.BRE_NO=B.BRE_NO AND A.ACTION_ID=B.ACTION_ID ");
            sb.Append(" WHERE 1=1");

            if (BRE_NO != string.Empty)
            {
                sb.AppendFormat(" AND A.BRE_NO ='{0}' ", BRE_NO);
            }

            return sb.ToString();
        }

        #endregion 查詢條件來源SQL

        #region 新增資料

        /// <summary>
        /// 新增資料
        /// </summary>
        /// <param name="Date">ZZZI14ViewModel</param>
        /// <param name="User">UserProfile</param>
        public void CreateDate(ZZZI14ViewModel Date, UserProfile User)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    this.INSERT_NTO_ZZT01(conn, Date, User, transaction);

                    if (Date.Details_List != null)
                    {
                        foreach (var Ditem in Date.Details_List)
                        {
                            Date.Details_List[Ditem.ITEM].BRE_NO = Date.BRE_NO;
                            this.INSERT_NTO_ZZT34(conn, Ditem, User, transaction);
                        }
                    }

                    this.SetZZT01_LEVEL_NO(conn, transaction);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "新增資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "新增資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 新增資料

        #region 修改資料處理

        public void UpDate(ZZZI14ViewModel Date, UserProfile User)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    this.UPDATE_SET_ZZT01(conn, Date, User, transaction);
                    this.DELETE_ZZT34(conn, Date.BRE_NO, transaction);

                    if (Date.Details_List != null)
                    {
                        foreach (var Ditem in Date.Details_List)
                        {
                            Ditem.BRE_NO = Date.BRE_NO;
                            this.INSERT_NTO_ZZT34(conn, Ditem, User, transaction);
                        }
                    }

                    this.SetZZT01_LEVEL_NO(conn, transaction);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "修改資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "修改資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 修改資料處理

        #region 刪除資料處理

        public void DelDate(string Bre_NO)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    string sSQL;

                    sSQL = " Delete ZZT01 Where Bre_NO='" + Bre_NO + "' ";
                    cmd.CommandText = sSQL;
                    cmd.ExecuteNonQuery();

                    sSQL = "Delete ZZT34 Where Bre_NO='" + Bre_NO + "' ";
                    cmd.CommandText = sSQL;
                    cmd.ExecuteNonQuery();

                    this.SetZZT01_LEVEL_NO(conn, transaction);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "刪除資料處理;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "刪除資料處理;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 刪除資料處理

        #region 檢查是否有此程式代碼

        /// <summary>
        /// 檢查是否有此程式代碼
        /// </summary>
        /// <param name="BRE_NO"></param>
        /// <returns></returns>
        public bool CheckisBRE_NO(string BRE_NO)
        {
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            bool ReturnVal;

            sb.AppendFormat(" SELECT BRE_NO FROM ZZT01 A (NOLOCK) WHERE BRE_NO='{0}' ", BRE_NO);
            dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());

            if (dt.Rows.Count > 0)
            {
                ReturnVal = true;
            }
            else
            {
                ReturnVal = false;
            }

            dt.Clear();
            dt.Dispose();
            sb.Clear();

            return ReturnVal;
        }

        #endregion 檢查是否有此程式代碼

        #region 給畫面明細序號

        public void SetSEQ_NO(ZZZI14ViewModel Data, int Num, int Count)
        {
            if (Data.Details_List == null)
            {
                Data.Details_List = new List<ZZZI14ViewModel_D>();
            }

            int i = 0;
            foreach (var item in Data.Details_List)
            {
                Data.Details_List[i].ITEM = i;
                i++;
            }

            if (Num > Count)
            {
                for (int x = Count; x < Num; x++)
                {
                    Data.Details_List.Add(new ZZZI14ViewModel_D()
                    {
                        ITEM = x
                    });
                }
            }
        }

        #endregion 給畫面明細序號

        #region INSERT INTO ZZT01

        public void INSERT_NTO_ZZT01(SqlConnection conn, ZZZI14ViewModel Date, UserProfile User, SqlTransaction transaction)
        {
            IDbCommand cmd = new SqlCommand(@"INSERT INTO ZZT01 ( BRE_NO,BRE_NAME,CONTROLLER,ACTION_ID,LEVEL_ID,LEVEL_NO,LINK_ADDR,BRE_NO_PRE,ORDER_BY,TARGET,BRE_TYPE,FUN_DESC,GROUP_DESC,WORK_DATES,WORK_DATEE,CHG_PERSON,CHG_DATE,CRE_PERSON,CRE_DATE,APP_USE_YN,ENABLE)
            VALUES (@BRE_NO,@BRE_NAME,@CONTROLLER,@ACTION_ID,@LEVEL_ID,@LEVEL_NO,@LINK_ADDR,@BRE_NO_PRE,@ORDER_BY,@TARGET,@BRE_TYPE,@FUN_DESC,@GROUP_DESC,@WORK_DATES,@WORK_DATEE,@CHG_PERSON,@CHG_DATE,@CRE_PERSON,@CRE_DATE,@APP_USE_YN,@ENABLE)");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Date.BRE_NO == null)
            ? new SqlParameter("@BRE_NO", DBNull.Value)
            : new SqlParameter("@BRE_NO", Date.BRE_NO));

            cmd.Parameters.Add(
            (Date.BRE_NAME == null)
            ? new SqlParameter("@BRE_NAME", DBNull.Value)
            : new SqlParameter("@BRE_NAME", Date.BRE_NAME));

            cmd.Parameters.Add(
            (Date.CONTROLLER == null)
            ? new SqlParameter("@CONTROLLER", DBNull.Value)
            : new SqlParameter("@CONTROLLER", Date.CONTROLLER));

            cmd.Parameters.Add(
            (Date.ACTION_ID == null)
            ? new SqlParameter("@ACTION_ID", DBNull.Value)
            : new SqlParameter("@ACTION_ID", Date.ACTION_ID));

            cmd.Parameters.Add(
            (Date.LEVEL_ID == null)
            ? new SqlParameter("@LEVEL_ID", DBNull.Value)
            : new SqlParameter("@LEVEL_ID", Date.LEVEL_ID));

            cmd.Parameters.Add(
            (Date.LEVEL_NO == null)
            ? new SqlParameter("@LEVEL_NO", DBNull.Value)
            : new SqlParameter("@LEVEL_NO", Date.LEVEL_NO));

            cmd.Parameters.Add(
            (Date.LINK_ADDR == null)
            ? new SqlParameter("@LINK_ADDR", DBNull.Value)
            : new SqlParameter("@LINK_ADDR", Date.LINK_ADDR));

            cmd.Parameters.Add(
            (Date.BRE_NO_PRE == null)
            ? new SqlParameter("@BRE_NO_PRE", DBNull.Value)
            : new SqlParameter("@BRE_NO_PRE", Date.BRE_NO_PRE));

            cmd.Parameters.Add(
            (Date.ORDER_BY == null)
            ? new SqlParameter("@ORDER_BY", DBNull.Value)
            : new SqlParameter("@ORDER_BY", Date.ORDER_BY));

            cmd.Parameters.Add(
            (Date.TARGET == null)
            ? new SqlParameter("@TARGET", DBNull.Value)
            : new SqlParameter("@TARGET", Date.TARGET));

            cmd.Parameters.Add(
            (Date.BRE_TYPE == null)
            ? new SqlParameter("@BRE_TYPE", DBNull.Value)
            : new SqlParameter("@BRE_TYPE", Date.BRE_TYPE));

            cmd.Parameters.Add(
            (Date.FUN_DESC == null)
            ? new SqlParameter("@FUN_DESC", DBNull.Value)
            : new SqlParameter("@FUN_DESC", Date.FUN_DESC));

            cmd.Parameters.Add(
            (Date.GROUP_DESC == null)
            ? new SqlParameter("@GROUP_DESC", DBNull.Value)
            : new SqlParameter("@GROUP_DESC", Date.GROUP_DESC));

            cmd.Parameters.Add(
            (Date.WORK_DATES == null)
            ? new SqlParameter("@WORK_DATES", DBNull.Value)
            : new SqlParameter("@WORK_DATES", Date.WORK_DATES));

            cmd.Parameters.Add(
            (Date.WORK_DATEE == null)
            ? new SqlParameter("@WORK_DATEE", DBNull.Value)
            : new SqlParameter("@WORK_DATEE", Date.WORK_DATEE));

            cmd.Parameters.Add(new SqlParameter("@CHG_PERSON", User.USER_KEY));

            cmd.Parameters.Add(new SqlParameter("@CHG_DATE", DateTime.Now));

            cmd.Parameters.Add(new SqlParameter("@CRE_PERSON", User.USER_KEY));

            cmd.Parameters.Add(new SqlParameter("@CRE_DATE", DateTime.Now));

            cmd.Parameters.Add(
            (Date.APP_USE_YN == null)
            ? new SqlParameter("@APP_USE_YN", "N")
            : new SqlParameter("@APP_USE_YN", DataConvert.BoolConvertToYN(Date.APP_USE_YN)));

            cmd.Parameters.Add(new SqlParameter("@ENABLE", 1));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = "INSERT_NTO_ZZT01 失敗;" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion INSERT INTO ZZT01

        #region Update set ZZT01

        public void UPDATE_SET_ZZT01(SqlConnection conn, ZZZI14ViewModel Item, UserProfile User, SqlTransaction transaction)
        {
            IDbCommand cmd = new SqlCommand(@" UPDATE ZZT01 set
            BRE_NAME=@BRE_NAME,CONTROLLER=@CONTROLLER,ACTION_ID=@ACTION_ID,LEVEL_ID=@LEVEL_ID
            ,LEVEL_NO=@LEVEL_NO,LINK_ADDR=@LINK_ADDR,BRE_NO_PRE=@BRE_NO_PRE,ORDER_BY=@ORDER_BY
            ,TARGET=@TARGET,BRE_TYPE=@BRE_TYPE,FUN_DESC=@FUN_DESC,GROUP_DESC=@GROUP_DESC
            ,WORK_DATES=@WORK_DATES,WORK_DATEE=@WORK_DATEE,CHG_PERSON=@CHG_PERSON
            ,APP_USE_YN=@APP_USE_YN
            Where BRE_NO=@BRE_NO ");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Item.BRE_NO == null)
            ? new SqlParameter("@BRE_NO", DBNull.Value)
            : new SqlParameter("@BRE_NO", Item.BRE_NO));

            cmd.Parameters.Add(
            (Item.BRE_NAME == null)
            ? new SqlParameter("@BRE_NAME", DBNull.Value)
            : new SqlParameter("@BRE_NAME", Item.BRE_NAME));

            cmd.Parameters.Add(
            (Item.CONTROLLER == null)
            ? new SqlParameter("@CONTROLLER", DBNull.Value)
            : new SqlParameter("@CONTROLLER", Item.CONTROLLER));

            cmd.Parameters.Add(
            (Item.ACTION_ID == null)
            ? new SqlParameter("@ACTION_ID", DBNull.Value)
            : new SqlParameter("@ACTION_ID", Item.ACTION_ID));

            cmd.Parameters.Add(
            (Item.LEVEL_ID == null)
            ? new SqlParameter("@LEVEL_ID", DBNull.Value)
            : new SqlParameter("@LEVEL_ID", Item.LEVEL_ID));

            cmd.Parameters.Add(
            (Item.LEVEL_NO == null)
            ? new SqlParameter("@LEVEL_NO", DBNull.Value)
            : new SqlParameter("@LEVEL_NO", Item.LEVEL_NO));

            cmd.Parameters.Add(
            (Item.LINK_ADDR == null)
            ? new SqlParameter("@LINK_ADDR", DBNull.Value)
            : new SqlParameter("@LINK_ADDR", Item.LINK_ADDR));

            cmd.Parameters.Add(
            (Item.BRE_NO_PRE == null)
            ? new SqlParameter("@BRE_NO_PRE", DBNull.Value)
            : new SqlParameter("@BRE_NO_PRE", Item.BRE_NO_PRE));

            cmd.Parameters.Add(
            (Item.ORDER_BY == null)
            ? new SqlParameter("@ORDER_BY", DBNull.Value)
            : new SqlParameter("@ORDER_BY", Item.ORDER_BY));

            cmd.Parameters.Add(
            (Item.TARGET == null)
            ? new SqlParameter("@TARGET", DBNull.Value)
            : new SqlParameter("@TARGET", Item.TARGET));

            cmd.Parameters.Add(
            (Item.BRE_TYPE == null)
            ? new SqlParameter("@BRE_TYPE", DBNull.Value)
            : new SqlParameter("@BRE_TYPE", Item.BRE_TYPE));

            cmd.Parameters.Add(
            (Item.FUN_DESC == null)
            ? new SqlParameter("@FUN_DESC", DBNull.Value)
            : new SqlParameter("@FUN_DESC", Item.FUN_DESC));

            cmd.Parameters.Add(
            (Item.GROUP_DESC == null)
            ? new SqlParameter("@GROUP_DESC", DBNull.Value)
            : new SqlParameter("@GROUP_DESC", Item.GROUP_DESC));

            cmd.Parameters.Add(
            (Item.WORK_DATES == null)
            ? new SqlParameter("@WORK_DATES", DBNull.Value)
            : new SqlParameter("@WORK_DATES", Item.WORK_DATES));

            cmd.Parameters.Add(
            (Item.WORK_DATEE == null)
            ? new SqlParameter("@WORK_DATEE", DBNull.Value)
            : new SqlParameter("@WORK_DATEE", Item.WORK_DATEE));

            cmd.Parameters.Add(new SqlParameter("@CHG_PERSON", User.USER_KEY));

            cmd.Parameters.Add(new SqlParameter("@CHG_DATE", DateTime.Now));

            cmd.Parameters.Add(
            (Item.APP_USE_YN == null)
            ? new SqlParameter("@APP_USE_YN", "N")
            : new SqlParameter("@APP_USE_YN", DataConvert.BoolConvertToYN(Item.APP_USE_YN)));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = "UPDATE_SET_ZZT01 失敗;" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion Update set ZZT01

        #region INSERT INTO ZZT34

        public void INSERT_NTO_ZZT34(SqlConnection conn, ZZZI14ViewModel_D Date, UserProfile User, SqlTransaction transaction)
        {
            IDbCommand cmd = new SqlCommand(@"INSERT INTO ZZT34 ( BRE_NO,ACTION_ID,ACTION_NAME,ACTION_TYPE,FUN_DESC,CHG_PERSON,CHG_DATE,CRE_PERSON,CRE_DATE)
            VALUES (@BRE_NO,@ACTION_ID,@ACTION_NAME,@ACTION_TYPE,@FUN_DESC,@CHG_PERSON,@CHG_DATE,@CRE_PERSON,@CRE_DATE)");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Date.BRE_NO == null)
            ? new SqlParameter("@BRE_NO", DBNull.Value)
            : new SqlParameter("@BRE_NO", Date.BRE_NO));

            cmd.Parameters.Add(
            (Date.ACTION_ID == null)
            ? new SqlParameter("@ACTION_ID", DBNull.Value)
            : new SqlParameter("@ACTION_ID", Date.ACTION_ID));

            cmd.Parameters.Add(
            (Date.ACTION_NAME == null)
            ? new SqlParameter("@ACTION_NAME", DBNull.Value)
            : new SqlParameter("@ACTION_NAME", Date.ACTION_NAME));

            cmd.Parameters.Add(
            (Date.ACTION_TYPE == null)
            ? new SqlParameter("@ACTION_TYPE", DBNull.Value)
            : new SqlParameter("@ACTION_TYPE", Date.ACTION_TYPE));

            cmd.Parameters.Add(
            (Date.FUN_DESC == null)
            ? new SqlParameter("@FUN_DESC", DBNull.Value)
            : new SqlParameter("@FUN_DESC", Date.FUN_DESC));

            cmd.Parameters.Add(new SqlParameter("@CHG_PERSON", User.USER_KEY));

            cmd.Parameters.Add(new SqlParameter("@CHG_DATE", DateTime.Now));

            cmd.Parameters.Add(new SqlParameter("@CRE_PERSON", User.USER_KEY));

            cmd.Parameters.Add(new SqlParameter("@CRE_DATE", DateTime.Now));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = "INSERT_NTO_ZZT34 失敗;" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion INSERT INTO ZZT34

        #region Delete ZZT34

        public void DELETE_ZZT34(SqlConnection conn, string BRE_NO, SqlTransaction transaction)
        {
            IDbCommand cmd = new SqlCommand(@" DELETE ZZT34 Where BRE_NO=@BRE_NO ");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (BRE_NO == null)
            ? new SqlParameter("@BRE_NO", DBNull.Value)
            : new SqlParameter("@BRE_NO", BRE_NO));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = "DELETE_ZZT34 失敗;" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion Delete ZZT34

        #region sp_SetZZT01_LEVEL_NO

        /// <summary>
        /// 計算LEVEL_NO
        /// </summary>
        /// <param name="conn"></param>
        /// <param name="transaction"></param>
        public void SetZZT01_LEVEL_NO(SqlConnection conn, SqlTransaction transaction)
        {
            string sp_name = "sp_SetZZT01_LEVEL_NO";
            SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn, transaction);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = "SetZZT01_LEVEL_NO 失敗;" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion sp_SetZZT01_LEVEL_NO
    }
}