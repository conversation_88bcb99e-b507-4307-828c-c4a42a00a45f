﻿@model ZZZI34OrderListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (Model != null)
    {
        if (Model.IsPrint)
        {
            Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        }
    }
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageOrderList", (string)ViewBag.BRE_NO)
    </div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

          window.onload = function () {
                if ($('#@Html.IdFor(m=>m.IsPrint)').val() == "true" && $('#@Html.IdFor(m=>m.IsToExcel)').val() != "true"  ) {
                    window.print()
              }

        }
    function exportExcel() {

            $("#form1").attr("enctype", "multipart/form-data");
            $("#form1").attr("action", "@Url.Action("ExportExcel", (string)ViewBag.BRE_NO)");
            $("#form1").submit();

        }
            $(function () {

            });

        function PrintBooK()
        {
            $('#@Html.IdFor(m=>m.IsPrint)').val(true)
            $(targetFormID).attr('action','@Html.Raw(@Url.Action("OrderList", (string)ViewBag.BRE_NO))')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            $('#@Html.IdFor(m=>m.IsPrint)').val(false)
        }

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

         function doMonthTop(val) {
                $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);
                FunPageProc(1)
            }

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageOrderList", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }
    </script>
}