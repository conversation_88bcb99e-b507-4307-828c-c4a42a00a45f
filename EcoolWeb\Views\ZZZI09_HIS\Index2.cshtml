﻿@model global::ECOOL_APP.com.ecool.Models.DTO.ZZZI09_HISViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<br />

@Html.Partial("_Notice")
<img src="~/Content/img/web-bar2-revise-19.png" style="width:100%" class="img-responsive " alt="Responsive image" />
@*@using (Html.BeginForm("ExportResultView", "ZZZI091", FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
    {*@
@using (Html.BeginForm("Index2", "ZZZI09_HIS", FormMethod.Post))
{
    @Html.Hidden("IDNO", "")
    <div class="Div-EZ-ZZZI09" id='_SelectDiv'>
        <div class="form-horizontal">
            <label class="control-label"> * @ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group">
                @Html.Label("選擇匯出轉學或畢業生", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_EXPORTTYPE" })
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.Where_EXPORTTYPE, (IEnumerable<SelectListItem>)ViewBag.ExportTypeItems, new { @class = "form-control", onchange = "$('#Where_SYEAR').val('');this.form.submit();" })
                    @Html.ValidationMessageFor(m => m.Where_EXPORTTYPE, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SYEAR" })
                <div class="col-md-9">
                    @if (ViewBag.CanChangeSchool)
                    {
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();" })
                    }
                    else
                    {
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();", disabled = "disabled" })
                    }
                    @Html.ValidationMessageFor(m => m.Where_SCHOOLNO_FORADMIN, "", new { @class = "text-danger" })
                </div>
            </div>

            @*<div class="progress" href="@Url.Action("Index3", (string)ViewBag.BRE_NO)">
            <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">

                用姓名搜尋出畢業生資訊
            </div>
        </div>*@
            @*<div class="form-group">
            @Html.Label("用名字搜尋", htmlAttributes: new { @class = "control-label col-md-3", @for = "SName"  })
            <div class="col-md-9">
                @Html.EditorFor(m => m.SName, new { @class = "control-label col-md-3", @for = "SName" })
            </div>
        </div>*@
            <div id="NOSHOW">
                <div class="form-group">
                    @Html.Label("入學年、畢業年", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SYEAR" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control", onchange = "$('#Where_CLASSNO').val('');this.form.submit();" })
                        @Html.ValidationMessageFor(m => m.Where_SYEAR, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("畢業/離校班級", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_CLASSNO" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.Where_CLASSNO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();" })
                        @Html.ValidationMessageFor(m => m.Where_CLASSNO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("是否匯出閱讀認證", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                    <div class="col-md-9">
                        否<input type="checkbox" id="ReadYN" name="ReadYN" />
                    </div>

                </div>
                <div class="form-group">
                    @Html.Label("姓名座號", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_USERNO" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.Where_USERNO, (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
                        @Html.ValidationMessageFor(m => m.Where_USERNO, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            @if (Model.Where_USERNO != null)
            {
                <div class="form-group">
                    @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SCHOOLNO" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.Where_SCHOOLNO, (IEnumerable<SelectListItem>)ViewBag.SchoolItems, new { @class = "form-control" })
                        @Html.ValidationMessageFor(m => m.Where_SCHOOLNO, "", new { @class = "text-danger" })
                    </div>
                </div>
            }
            <div class="form-group">
                @Html.Label("封面", htmlAttributes: new { @class = "control-label col-md-3", @for = "COVERJPG" })
                <div class="col-md-9">
                    <input id="COVERJPG1" name="COVERJPG1" type="radio" value="coverA.jpg" checked>
                    <img style="Max-width:70px" class="cboxElement " id="cover" src="~/Content/img/coverA.jpg" href="~/Content/img/coverA.jpg" />
                    <input id="COVERJPG1" name="COVERJPG1" type="radio" value="coverB.jpg">
                    <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverB.jpg" href="~/Content/img/coverB.jpg" />
                    <input id="COVERJPG1" name="COVERJPG1" type="radio" value="coverC.jpg">
                    <img style="Max-width:70px" class="cboxElement " id="cover" src="~/Content/img/coverC.jpg" href="~/Content/img/coverC.jpg" />

                    <input id="COVERJPG1" name="COVERJPG1" type="radio" value="coverD.jpg">
                    <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverD.jpg" href="~/Content/img/coverD.jpg" />
                    <br />
                    <input id="COVERJPG1" name="COVERJPG1" type="radio" value="coverE.jpg">
                    <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverE.jpg" href="~/Content/img/coverE.jpg" />
                    <br />
                    @using (Html.BeginForm("ExportResultView", "ZZZI091", FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
                    {
                        @Html.HiddenFor(m => m.School_No)
                        @Html.HiddenFor(m => m.User_No)
                        @Html.HiddenFor(m => m.Class_NO)
                        @Html.HiddenFor(m => m.COVERJPG)
                        @Html.HiddenFor(m => m.Where_SCHOOLNO_FORADMIN)
                        <input id="COVERJPG1" name="COVERJPG1" type="radio" value="coverF.jpg"> <b>自行上傳封面</b>
                        @*<input class="form-control input-md" id="COVERJPG" name="COVERJPG" type="file" value="">*@
                        <input class="form-control input-md" id="UploadCoverFile" name="UploadCoverFile" type="file" value="" onclick="checkRadio(this)">
                    }
                    <label class="text-info">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片;(長:1569px;寬1110px)</label>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("用名字搜尋", htmlAttributes: new { @class = "control-label col-md-3", @for = "SName" })
                <div class="col-md-9">

                    <button type="button" class="btn btn-default btn-block" onclick="helper()">求助</button>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-offset-3 col-md-9 text-center">
                    <button type="button" class="btn btn-default btn-block" onclick="CK()">匯出</button>
                </div>
            </div>
        </div>
        </div>

}

@section scripts{
    <script>
      function helper() {

                window.open('@Url.Action("Index3", (string)ViewBag.BRE_NO)', '_blank');
        }
         window.onload = function () {

             $("#IDNO").val("@Model.IDNO");
             if ("@ViewBag.Where_EXPORTTYPE"!= "") {

                  $("#Where_EXPORTTYPE").val("@ViewBag.Where_EXPORTTYPE");
             }
             if ('@ViewBag.Where_SYEAR'!= '') {

                  $("#Where_SYEAR").val("@ViewBag.Where_SYEAR");
             }

             if ('@Model.User_No'!= '') {
                 $("#Where_USERNO").val("@Model.User_No");
             }
             if ('@Model.Class_NO'!= '') {
                   $("#Where_CLASSNO").val("@Model.Class_NO");
             }
             if ('@Model.School_No'!= '') {
                 $("#Where_SCHOOLNO").val("@Model.School_No");
             }

        }
        function CK() {
            var Msg = '';
            var COVERJPGValue = "";
            COVERJPGValue = $('input[name*=COVERJPG]:checked').val();

            var formdata = new FormData($('form').get(0));
            if ($('#Where_USERNO').val() == '' && $('#SName').val().length=='0') {
                Msg = Msg + '請選擇「' + $("label[for='Where_USERNO']").text() + '」\n';
            }

            if (Msg != '') {
                alert(Msg);
            }
            else {
                 var COVERJPGValue = "";
                COVERJPGValue = $('input[name*=COVERJPG1]:checked').val();
                var Where_SCHOOLNO = $('#Where_SCHOOLNO').val();
                var Where_USERNO = $('#Where_USERNO').val();
                var Where_CLASSNO = $('#Where_CLASSNO').val();
                var Where_SCHOOLNO_FORADMIN = $("#Where_SCHOOLNO_FORADMIN").val();
                $("#User_No").val(Where_USERNO);
                $("#School_No").val(Where_SCHOOLNO);
                $("#Class_NO").val(Where_CLASSNO);
                $("#COVERJPG").val(COVERJPGValue);
                var formdata = new FormData($('form').get(0));
                $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
                  $.ajax({
                url: '@Url.Action("ExportResultView", "ZZZI091")',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                      success: function (data) {

                          var res = $.parseJSON(data);
                          console.log(res);
                      }

                  });
                var file = $("#UploadCoverFile").val();
                var fileName = getFileName(file);
                $('#IDNO').val('@ViewBag.IDNO');
                var StrWhere = '?School_No=' + $('#Where_SCHOOLNO').val() + '&USER_NO=' + $('#Where_USERNO').val() + '&IDNO=' + $('#IDNO').val() + '&COVERJPG=' + COVERJPGValue + '&UploadCoverFileName=' + fileName + '&redirect=s' + '&ReadYN=' + $("#ReadYN").prop("checked") + '&Where_SCHOOLNO_FORADMIN=' + $("#Where_SCHOOLNO_FORADMIN").val()
                //var StrWhere = '?School_No=' + $('#Where_SCHOOLNO').val() + '&USER_NO=' + $('#Where_USERNO').val() + '&IDNO=' + $('#IDNO').val()
                window.open('@Url.Action("ExportResultView", "ZZZI091")' + StrWhere+'', '_blank');
            }
        }

        function getFileName(o) {
            var pos = o.lastIndexOf("\\");
            return o.substring(pos + 1);
        }
    </script>
}