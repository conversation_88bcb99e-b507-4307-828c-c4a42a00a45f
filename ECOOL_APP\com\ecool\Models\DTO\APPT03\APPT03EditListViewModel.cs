﻿using ECOOL_APP.EF;
using System.Collections.Generic;


namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class APPT03EditListViewModel
    {

        /// <summary>
        /// 來源TABLE
        /// </summary>
        public string REF_TABLE { get; set; }

        /// <summary>
        /// 來源KEY 
        /// </summary>
        public string REF_KEY { get; set; }


        /// <summary>
        /// TABLE 資料狀態 
        /// </summary>
        public byte STATUS { get; set; }



        /// <summary>
        /// 點選的功能類別
        /// </summary>
        public string BTN_TYPE { get; set; }

        /// <summary>
        /// 點選的功能類別細項
        /// </summary>
        public string ITEM_VAL { get; set; }

        /// <summary>
        /// 學校
        /// </summary>
        public string whereSCHOOL_NO { get; set; }

        /// <summary>
        /// 已選取人數
        /// </summary>
        public int SelectDataCount { get; set; }

        /// <summary>
        /// 選取清單
        /// </summary>
        public List<APPT03QListViewModel> DataList { get; set; }


        /// <summary>
        /// 已選Div 高度 + 跟筆數一起拉大 , - 固定200pt
        /// </summary>
        public string DivHeight { get; set; }

        /// <summary>
        /// ErrorMsg
        /// </summary>
        public string ErrorMsg { get; set; }


        public string DataType { get; set; }

        static public class DataTypeVal
        {
            public static string DataTypeAdd = "Add";

            public static string DataTypeDel = "Del";
        }

        static public class DivHeightVal
        {
            public static string DivHeightP = "+";

            public static string DivHeightM = "-";
        }

        public APPT03EditListViewModel()
        {
            REF_TABLE = "APPT03";
            STATUS = APPT03.StatusVal.TempKey;
            BTN_TYPE = APPT03_Q.BTN_TYPE_VAL.sys_role;
            ErrorMsg = string.Empty;
        }

    }
}
