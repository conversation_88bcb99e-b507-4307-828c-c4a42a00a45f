﻿@model EcoolWeb.ViewModels.AWAT15QueryViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    var itemCount = 0;
}
<style type="text/css">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }

    /*.row {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }

        .row > [class*='col-'] {
            padding-top: 15px;
            padding-bottom: 15px;
            margin-left: 5px;
            background-color: #FFFFFF;
            border-style: solid;
        }*/

    .wrapper {
        max-width:945px;
        margin: 0 auto;
    }

    .element {
        margin-top:5px;
        background: #EEE;
        border: 2px solid #999;
        margin-bottom: 2px;
        width: 180px;
        min-height:120px;
        float: left;
        margin-right: 9px;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 5px;
        page-break-inside:avoid;
    }
</style>
<script src="~/Scripts/grids.js"></script>
<div class="container">
    <div class="table-92Per" style="margin: 0px auto; ">
        <div style="height:25px"></div>
        <div class="wrapper">
            @foreach (var item in Model.Chk)
            {
                @*if (itemCount != 0 && itemCount % 3 == 0)
            {
                @Html.Raw("</div><div class='row'>")
            }*@
                if (item.AWAT15 != null)
                {
                    <div class="element">
                        @item.AWAT15.CLASS_NO
                        <span>班</span>  @item.AWAT15.NAME
                        <span>
                            於 @item.AWAT15.CreatDate.Value.ToString("yyyy-MM-dd HH:mm")　升第 @item.AWAT15.CASH_Rank 級。

                            @if (item.ExpectedDate != null)
                            {
                                @:預計 @item.ExpectedDate.Value.ToString("yyyy-MM-dd HH:mm")
                                @:頒獎

                            }

                            @if (item.Content != null)
                            {
                                @:,屆時請至 @item.Content
                                @:(集合)。<br />
                                if (!string.IsNullOrEmpty(item.MEMO))
                                {
                                    @:<EMAIL>


                                }

                            }
                        </span>
                    </div>
                }
             
                itemCount++;
            }
        </div>
    </div>
</div>

<script type="text/javascript">

    $('.element').responsiveEqualHeightGrid();

    window.onload = function () {
        window.print()
    }
</script>