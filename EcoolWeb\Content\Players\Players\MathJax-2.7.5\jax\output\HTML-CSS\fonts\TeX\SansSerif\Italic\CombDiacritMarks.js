/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Italic/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_SansSerif-italic"],{768:[694,-527,0,-270,-87],769:[694,-527,0,-190,63],770:[694,-527,0,-310,33],771:[677,-543,0,-301,60],772:[631,-552,0,-314,64],774:[694,-508,0,-284,73],775:[680,-576,0,-180,-54],776:[680,-582,0,-273,40],778:[693,-527,0,-227,-2],779:[694,-527,0,-287,63],780:[654,-487,0,-283,60]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Italic/CombDiacritMarks.js");
