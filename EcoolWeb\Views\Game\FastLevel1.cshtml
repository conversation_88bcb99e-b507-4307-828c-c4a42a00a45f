﻿@model GameFastLevelViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}
<link href="~/Scripts/jquery-loading/loading.css" rel="stylesheet" />
<script src="~/Scripts/jquery-loading/loading.js"></script>
<div id="loading-always" class="loading-div">
</div>

<H1 id="TitleStr" class="text-center" style="padding-top: 50px">
    @Model.Title <br />5秒內將自動轉入
</H1>
<div class="text-center" id="btnGoto" style="display: none">
    <button class="btn btn-default" onclick="onGoUrl()">繼續轉入</button>
</div>
@using (Html.BeginForm("FastLevel", "Game", FormMethod.Post, new { name = "formEdit", id = "formEdit" }))
{

    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.ActionName)
    @Html.HiddenFor(model => model.GAME_NO)
    @Html.HiddenFor(model => model.ActionName)
    @Html.HiddenFor(model => model.LEVEL_NO)

}

<script type="text/javascript">
    var targetFormID = '#formEdit';

    $(document).ready(function () {

        var browser = get_browser()

        if (browser.name != 'Chrome' ) {
            $('#TitleStr').text('請使用Google Chrome  瀏覽器')
                $('#btnGoto').show();
            }
        else {
                $('#btnGoto').show();
                if (browser.version < 91) {
                    $('#TitleStr').text('請更新 Google Chrome  版本 Chrome 72，目前版本為' + browser.version)
                }
                else {
                       $(document).attr("title", "@Model.Title");
                    $('#loading-always').loading();
                    setTimeout('onGoUrl()', 5000);
                }
            }
        });

    function onGoUrl() {
          $(targetFormID).attr("action", "@Url.Action("PassMode1", "Game")")
          $(targetFormID).submit();
    }

    function get_browser() {
        var ua = navigator.userAgent, tem, M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
        if (/trident/i.test(M[1])) {
            tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
            return { name: 'IE', version: (tem[1] || '') };
        }
        if (M[1] === 'Chrome') {
            tem = ua.match(/\bOPR|Edge\/(\d+)/)
            if (tem != null) { return { name: 'Opera', version: tem[1] }; }
        }
        M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
        if ((tem = ua.match(/version\/(\d+)/i)) != null) { M.splice(1, 1, tem[1]); }
        return {
            name: M[0],
            version: M[1]
        };
    }
</script>