/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Size5/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Size5={directory:"Size5/Regular",family:"NeoEulerMathJax_Size5",id:"NEOEULERSIZE5",32:[0,0,333,0,0,""],160:[0,0,333,0,0,""],8741:[3098,208,403,86,316,"316 -200l-40 -8v3298l40 8v-3298zM126 -200l-40 -8v3298l40 8v-3298"],10216:[2730,228,803,137,694,"202 1251l492 -1459l-59 -20l-498 1479l498 1479l59 -20"],10217:[2730,228,859,109,666,"168 2730l498 -1479l-498 -1479l-59 20l492 1459l-492 1459"],57344:[3098,208,213,86,126,"126 -200l-40 -8v3298l40 8v-3298"],57345:[138,167,213,86,126,"126 -159l-40 -8v297l40 8v-297"],57346:[3098,208,403,86,316,"316 -200l-40 -8v3298l40 8v-3298zM126 -200l-40 -8v3298l40 8v-3298"],57347:[138,167,403,86,316,"316 -159l-40 -8v297l40 8v-297zM126 -159l-40 -8v297l40 8v-297"],57348:[635,-595,150,0,150,"0 635h150v-40h-150v40"],57349:[-65,105,150,0,150,"0 -65h150v-40h-150v40"],57350:[1820,0,1055,111,742,"702 1820h40v-1820h-40l-493 1495l-83 -162l-15 16l139 273l451 -1367h1v1565"],57351:[572,-2,1055,702,742,"702 2v570h40v-570h-40"],57352:[583,2,1055,702,1076,"702 583h374v-40h-334v-545h-40v585"],57353:[827,-276,1799,0,1809,"1759 827h39c11 -1 11 -7 11 -28v-55c0 -22 0 -28 -14 -28c-677 -1 -1277 -79 -1763 -427c-18 -13 -20 -13 -22 -13c-10 0 -10 8 -10 23c0 16 1 17 2 19c2 4 7 8 25 23c206 167 440 278 662 346c101 31 459 140 1070 140"],57354:[828,-718,600,-10,610,"17 828h566c26 0 27 0 27 -28v-54c0 -28 -1 -28 -27 -28h-566c-26 0 -27 0 -27 28v54c0 28 1 28 27 28"],57355:[828,-277,1799,-10,1799,"1 828h39c237 0 445 -21 476 -24c364 -37 667 -120 931 -252c201 -101 347 -228 350 -232c2 -2 2 -4 2 -20c0 -15 0 -23 -10 -23c-2 0 -4 0 -13 7c-307 219 -665 352 -1182 406c-249 26 -487 27 -590 27c-14 0 -14 6 -14 28v55c0 21 0 27 11 28"],57356:[280,271,1799,0,1809,"1809 -188v-55c0 -21 0 -27 -11 -28h-39c-237 0 -445 21 -476 24c-364 37 -667 120 -931 252c-201 101 -347 228 -350 232c-2 2 -2 4 -2 20s0 23 10 23c2 0 4 0 13 -7c489 -350 1071 -431 1772 -433c14 0 14 -6 14 -28"],57357:[-160,271,600,-10,610,"17 -160h566c26 0 27 -1 27 -28v-55c0 -27 -1 -28 -27 -28h-566c-26 0 -27 1 -27 28v55c0 27 1 28 27 28"],57358:[281,270,1799,-10,1799,"-10 -242v55c0 22 0 28 14 28c754 1 1310 101 1772 434c9 6 11 6 13 6c10 0 10 -7 10 -23s-1 -17 -2 -19c-2 -4 -7 -8 -25 -23c-206 -167 -440 -278 -662 -346c-101 -31 -459 -140 -1070 -140h-39c-11 1 -11 7 -11 28"],57359:[758,-436,450,-24,460,"460 729v-39c0 -20 0 -29 -12 -29c-80 -1 -166 -14 -252 -58c-103 -54 -147 -121 -172 -159c-5 -8 -7 -8 -24 -8s-23 0 -24 10v32c104 177 277 280 478 280c6 -6 6 -8 6 -29"],57360:[758,-660,300,-10,310,"304 660h-308c-6 6 -6 8 -6 30v38c0 28 1 30 27 30h266c26 0 27 -2 27 -30v-38c0 -22 0 -24 -6 -30"],57361:[983,-661,1800,-10,1810,"447 661h-451c-6 6 -6 8 -6 29v39c0 28 1 29 27 29h423c56 0 148 9 250 57c109 53 165 132 188 163c3 4 4 5 22 5s19 -1 27 -11c122 -175 320 -214 433 -214h423c26 0 27 -1 27 -29v-39c0 -21 0 -23 -6 -29h-451c-72 0 -298 21 -453 264c-160 -250 -394 -264 -453 -264"],57362:[758,-436,450,-10,474,"474 478v-34c-2 -8 -7 -8 -24 -8c-15 0 -19 0 -23 7c-29 43 -73 111 -181 164c-105 51 -205 53 -242 54c-14 0 -14 6 -14 29v39c0 21 0 23 6 29c212 0 380 -112 478 -280"],57363:[120,202,450,-24,460,"460 -134v-39c0 -27 0 -29 -21 -29c-72 0 -314 26 -463 280v34c2 8 7 8 24 8c15 0 19 0 23 -7c29 -43 67 -101 161 -153c78 -42 165 -62 252 -64c24 -1 24 -2 24 -30"],57364:[-106,202,300,-10,310,"304 -202h-308c-6 6 -6 7 -6 28v40c0 28 1 28 27 28h266c26 0 27 0 27 -28v-40c0 -21 0 -22 -6 -28"],57365:[-106,428,1800,-10,1810,"425 -202h-429c-6 6 -6 8 -6 28v40c0 28 1 28 27 28h422c171 0 354 -90 461 -264c106 174 288 264 461 264h422c26 0 27 0 27 -28v-40c0 -20 0 -22 -6 -28h-429c-114 0 -207 -26 -287 -70c-96 -54 -140 -116 -166 -152c-3 -4 -4 -4 -22 -4s-19 0 -22 4 c-27 38 -72 100 -170 154c-88 48 -184 68 -283 68"],57366:[120,202,450,-10,474,"474 110v-32c-147 -250 -384 -280 -463 -280c-21 0 -21 2 -21 29v39c0 23 1 24 6 28c3 1 23 2 35 2c10 0 115 3 228 60c99 51 144 121 167 156c5 8 7 8 24 8s23 0 24 -10"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size5/Regular/Main.js");
