/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Alphabets/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Alphabets={directory:"Alphabets/Regular",family:"STIXMathJax_Alphabets",testString:"\u00A0\u0384\u0385\u0386\u0387\u0388\u0389\u038A\u038C\u038E\u038F\u0390\u03AA\u03AB\u03AC",32:[0,0,250,0,0],160:[0,0,250,0,0],900:[662,-507,277,113,240],901:[662,-507,333,18,316],902:[683,0,722,15,707],903:[459,-348,278,81,192],904:[683,0,750,8,737],905:[683,0,850,8,836],906:[683,0,470,8,449],908:[683,14,722,8,688],910:[683,0,840,8,818],911:[683,0,744,8,715],912:[662,10,340,18,316],938:[873,0,333,18,316],939:[873,0,722,29,703],940:[662,10,543,29,529],941:[662,10,439,25,407],942:[662,217,512,10,452],943:[662,10,275,20,267],944:[662,10,524,16,494],970:[622,10,340,18,316],971:[622,10,524,16,494],972:[662,10,505,35,473],973:[662,10,524,16,494],974:[662,10,625,29,595],1025:[872,0,629,22,607],1026:[662,189,756,18,700],1027:[928,0,571,19,544],1028:[676,14,651,38,621],1029:[676,14,556,62,510],1030:[662,0,333,18,315],1031:[872,0,333,25,323],1032:[662,14,373,-6,354],1033:[662,14,988,10,954],1034:[662,0,1017,19,983],1035:[662,0,803,18,786],1036:[928,0,690,19,686],1038:[915,15,711,15,694],1039:[662,153,715,19,696],1040:[674,0,713,9,701],1041:[662,0,611,19,577],1042:[662,0,651,19,595],1043:[662,0,571,19,544],1044:[662,153,665,14,646],1045:[662,0,629,22,607],1046:[676,0,1021,8,1013],1047:[676,14,576,28,545],1048:[662,0,723,19,704],1049:[915,0,723,19,704],1050:[676,0,690,19,686],1051:[662,14,683,9,664],1052:[662,0,893,19,871],1053:[662,0,726,19,704],1054:[676,14,729,36,690],1055:[662,0,724,19,705],1056:[662,0,571,19,535],1057:[676,14,677,36,641],1058:[662,0,618,30,592],1059:[662,15,711,15,694],1060:[662,0,769,38,731],1061:[662,0,716,9,703],1062:[662,153,715,19,696],1063:[662,0,657,3,639],1064:[662,0,994,29,965],1065:[662,153,994,29,965],1066:[662,0,737,13,703],1067:[662,0,884,19,865],1068:[662,0,612,19,578],1069:[676,14,651,30,613],1070:[676,14,902,19,863],1071:[662,0,637,3,618],1072:[460,10,450,37,446],1073:[685,10,507,39,478],1074:[450,0,474,24,438],1075:[450,0,394,17,387],1076:[450,137,462,14,439],1077:[460,10,466,38,437],1078:[456,0,721,14,707],1079:[460,10,390,14,357],1080:[450,0,525,23,502],1081:[704,0,525,23,502],1082:[456,0,503,23,495],1083:[450,10,499,8,476],1084:[450,0,617,23,594],1085:[450,0,525,23,502],1086:[460,10,512,35,476],1087:[450,0,525,23,502],1088:[460,217,499,-2,463],1089:[460,10,456,41,428],1090:[450,0,434,8,426],1091:[450,218,491,8,483],1092:[662,217,678,43,635],1093:[450,0,489,14,476],1094:[450,137,525,23,502],1095:[450,0,512,18,489],1096:[450,0,768,23,745],1097:[450,137,768,23,745],1098:[450,0,539,8,507],1099:[450,0,670,23,646],1100:[450,0,457,23,425],1101:[460,10,444,14,410],1102:[460,10,738,23,703],1103:[450,0,471,4,448],1105:[622,10,466,38,437],1106:[683,218,512,6,439],1107:[679,0,394,17,387],1108:[460,10,444,34,430],1109:[459,10,389,49,346],1110:[683,0,278,29,266],1111:[622,0,278,1,299],1112:[683,218,278,-77,187],1113:[450,10,702,8,670],1114:[450,0,721,23,689],1115:[683,0,512,6,499],1116:[679,0,503,23,495],1118:[704,218,491,8,483],1119:[450,137,518,23,495],1122:[662,0,746,26,713],1123:[683,0,539,8,507],1130:[662,0,998,6,992],1131:[450,0,722,14,708],1138:[676,14,729,36,690],1139:[460,10,512,35,476],1140:[676,11,766,16,760],1141:[456,14,539,19,532],1168:[803,0,571,19,544],1169:[558,0,394,17,387],8453:[676,14,837,48,795],8455:[676,14,598,28,561],8470:[676,14,1012,7,966],8471:[676,14,760,38,722],8478:[667,101,780,69,763],8482:[662,-256,980,30,957],8485:[662,218,424,35,391],8486:[676,0,744,29,715],8489:[463,0,360,32,276],8491:[871,0,722,15,707],8494:[676,17,843,35,808],8514:[662,0,559,13,485],8515:[662,0,559,13,485],8516:[662,0,630,21,609],8522:[692,0,664,45,602],8523:[676,13,778,28,736],12398:[661,41,901,37,840],57427:[450,0,632,26,604],57428:[516,10,688,37,679],57429:[475,14,571,20,563],57430:[459,11,632,10,624],57431:[459,12,624,29,595],57437:[459,10,452,16,436],57448:[683,287,524,9,487],57506:[460,218,561,24,539],57523:[683,218,541,32,457],57533:[662,218,710,15,660],57534:[757,218,1102,15,1073],57565:[933,0,516,73,445],57566:[933,0,500,57,439],57567:[754,0,778,92,699],57568:[920,0,500,40,444],57569:[757,0,389,81,318],57570:[754,0,500,60,429],58108:[756,218,722,15,707],58110:[756,217,667,17,593],58112:[756,217,587,11,577],58114:[756,218,722,48,675],58116:[756,217,611,12,597],58118:[756,217,612,10,598],58120:[756,217,722,18,703],58122:[756,218,722,34,688],58124:[756,218,333,-24,438],58126:[756,217,731,33,723],58128:[756,218,702,15,687],58130:[756,217,889,12,864],58132:[756,218,722,12,707],58134:[756,217,643,29,614],58136:[756,218,722,34,688],58138:[756,217,722,18,703],58140:[756,218,557,16,565],58142:[756,217,624,30,600],58144:[756,218,611,17,593],58146:[756,218,722,29,703],58148:[756,217,763,35,728],58150:[756,217,722,10,704],58152:[756,217,743,22,724],58154:[756,217,744,29,715],58212:[756,240,673,55,665],58216:[756,218,557,8,645],58220:[773,218,645,-72,675],58224:[756,218,708,7,668],58368:[683,218,541,32,457]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Alphabets"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Alphabets/Regular/Main.js"]);
