(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: filter, select2 formatter function - updated 1/18/2018 (v2.29.4) */
!function(g){"use strict";var h=g.tablesorter||{};h.filterFormatter=h.filterFormatter||{},h.filterFormatter.select2=function(l,c,e){var t,i,a=g.extend({cellText:"",match:!0,value:"",multiple:!0,width:"100%"},e),n=l.addClass("select2col"+c).closest("table")[0].config,s=n.widgetOptions,d=g('<input class="filter" type="hidden">').appendTo(l).bind("change"+n.namespace+"filter",function(){var e=b(this.value);n.$table.find(".select2col"+c+" .select2").select2("val",e),$()}),r=n.$headerIndexed[c],o=r.hasClass(s.filter_onlyAvail),f=a.match?"":"^",p=a.match?"":"$",u=s.filter_ignoreCase?"i":"",b=function(e){return e.replace(/^\/\(\^?/,"").replace(/\$\|\^/g,"|").replace(/\$?\)\/i?$/g,"").replace(/\\/g,"").split("|")},$=function(){var e=!1,t=n.$table.find(".select2col"+c+" .select2").select2("val")||a.value||"";g.isArray(t)&&(e=!0,t=t.join("\0")),t=t.replace(/[-[\]{}()*+?.,/\\^$|#\s]/g,"\\$&"),e&&(t=t.split("\0")),h.isEmptyObject(l.find(".select2").data())||(d.val(g.isArray(t)&&t.length&&""!==t.join("")?"/("+f+(t||[]).join(p+"|"+f)+p+")/"+u:"").trigger("search"),l.find(".select2").select2("val",t),n.widgetOptions.$sticky&&n.widgetOptions.$sticky.find(".select2col"+c+" .select2").select2("val",t))},v=function(){i=[],t=h.filter.getOptionSource(n.$table[0],c,o)||[],g.each(t,function(e,t){i.push({id:""+t.parsed,text:t.text})}),a.data=i};return r.toggleClass("filter-match",a.match),a.cellText&&l.prepend("<label>"+a.cellText+"</label>"),a.ajax&&!g.isEmptyObject(a.ajax)||a.data||(v(),n.$table.bind("filterEnd",function(){v(),n.$table.find(".select2col"+c).add(n.widgetOptions.$sticky&&n.widgetOptions.$sticky.find(".select2col"+c)).find(".select2").select2(a)})),g('<input class="select2 select2-'+c+'" type="hidden" />').val(a.value).appendTo(l).select2(a).bind("change",function(){$()}),n.$table.bind("filterFomatterUpdate",function(){var e=b(n.$table.data("lastSearch")[c]||"");(l=n.$table.find(".select2col"+c)).find(".select2").select2("val",e),$(),h.filter.formatterUpdated(l,c)}),n.$table.bind("stickyHeadersInit",function(){var e=n.widgetOptions.$sticky.find(".select2col"+c).empty();g('<input class="select2 select2-'+c+'" type="hidden">').val(a.value).appendTo(e).select2(a).bind("change",function(){n.$table.find(".select2col"+c).find(".select2").select2("val",n.widgetOptions.$sticky.find(".select2col"+c+" .select2").select2("val")),$()}),a.cellText&&e.prepend("<label>"+a.cellText+"</label>")}),n.$table.bind("filterReset",function(){n.$table.find(".select2col"+c).find(".select2").select2("val",a.value||""),setTimeout(function(){$()},0)}),$(),d}}(jQuery);return jQuery;}));
