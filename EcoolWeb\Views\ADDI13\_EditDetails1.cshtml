﻿@model BarcodeEditPeopleViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    //紙本掃描美化-Page2
}
@Html.Partial("_Notice")
@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG" style="width:50%" class="img-responsive " alt="Responsive image" />*@
@if (Model != null && Model.ROLL_CALL_NAME != null)
{

    using (Html.BeginForm("GetCashPerson", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_self" }))
    {
        @Html.HiddenFor(m => m.SCHOOL_NO)
        @Html.HiddenFor(m => m.SHORT_NAME)
        @Html.HiddenFor(m => m.USER_NO)
        @Html.HiddenFor(m => m.NAME)
        @Html.HiddenFor(m => m.GRADE)
        @Html.HiddenFor(m => m.CLASS_NO)
        @Html.HiddenFor(m => m.SEAT_NO)
        @Html.HiddenFor(m => m.CARD_NO)
        @Html.HiddenFor(m => m.BarCode)
        @Html.HiddenFor(m => m.ROLL_CALL_NAME)
        @Html.HiddenFor(m => m.ROLL_CALL_ID)
        @Html.HiddenFor(m => m.CASH)


<div class="points-collection my-3">
    <h1 class="points-collection-title mt-2">酷幣帳號登入</h1>
    @if (user == null )
    {
        <div class="input-group input-group-lg p-3 alert-info">
            @*<p class="h4">酷幣帳號登入</p>*@
            <input class="form-control mb-3" id="txtUSER_NO" name="txtUSER_NO" type="text" placeholder="請輸入帳號" aria-label="請輸入帳號">
            <input class="form-control" id="txtPASSWORD" name="txtPASSWORD" type="password" placeholder="請輸入密碼" aria-label="請輸入密碼">
        </div>
        @*<a class="d-table mx-auto my-3" href="@Url.Action("SSOLoginPage","SSO")">
                <img src="@Url.Content("~/Content/images/oidc-v.png")" class="text-center" />
            </a>*@
        <div class="text-center py-3">
            <button type="button" class="btn btn-primary btn-lg btn-block px-5 py-3" onclick="ExportSave()" id="loginButton">登入</button>
        </div>

        @*<p class="text-danger text-center h4">
                本功能尚未支援單一身分認證，<br />規劃中，請見諒。
            </p>*@



    }
    else
    {

        @user.NAME

    }

</div>

<div class="points-collection my-3">
    <h1 class="points-collection-title mt-2">單一身分認證登入</h1>
    @if (user == null)
    {

        string strPath = "";


        strPath = Url.Action("Index1Point", (string)ViewBag.BRE_NO, new { ROLL_CALL_ID = ViewBag.ROLL_CALL_ID, SCHOOL_NO1 = ViewBag.SCHOOL_NO, SCHOOL_NO = ViewBag.SCHOOL_NO, Barcode = ViewBag.Barcode });


        <p class="text-danger text-center h4">
            <a href='@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()/EcoolWeb/SSO/SSOPointLoginPage1?str="@strPath"'>
                <img src='@Url.Content("~/Content/images/oidc-v.png")' />
            </a>
        </p>

    }
</div>
    }
}
@section Scripts {
    <script>
        var targetFormID = '#form1';
        $(document).ready(function () {
            $("#mainBody").attr("style", "display:none");
            var l = 0;
            l = $("#StatusMessageDiv").length;

            if (l > 0){
                var i = 0;
                $(".row").each(function (obj) {
                        if (i == 0) {
                            console.log($(obj).html())
                        }
                        i++;
                })
            }
            $("#SCHOOL_NO").val("@Model.SCHOOL_NO");

            if ('@user'!= 'null' && '@user' != '' ) {
                if ('@(user?.USER_TYPE)' != 'P') {

                    ExportSave();
                }
             

            }

        });
        function GetInfo() {
        var txtUSER_NO = $("#txtUSER_NO").val();
        var txtPASSWORD = $("#txtPASSWORD").val();
        var SCHOOL_NO1 = $("#SCHOOL_NO1").val();
        var NAME = $("#NAME").val();
        var CARD_NO = $("#CARD_NO").val();
        var BarCode = $("#BarCode").val();
        var ROLL_CALL_NAME = $("#ROLL_CALL_NAME").val();
        var ROLL_CALL_ID = $("#ROLL_CALL_ID").val();
        var CASH = $("#CASH").val();
        var data = {
            "txtUSER_NO": txtUSER_NO,
            "txtPASSWORD": txtPASSWORD,
            "SCHOOL_NO": SCHOOL_NO1,
            "NAME": NAME,
            "CARD_NO": CARD_NO,
            "BarCode": BarCode,
            "ROLL_CALL_NAME": ROLL_CALL_NAME,
            "ROLL_CALL_ID": ROLL_CALL_ID,
            "CASH": CASH
        };
        $("#loginButton").attr("disabled", "disabled");

        $.ajax({
            url: '@Url.Action("GetCashPerson", (string)ViewBag.BRE_NO)',
            data: data,
            cache: false,
            success: function (html) {
                console.log(html);
            }

        });
        //   $(targetFormID).attr("action", "@Url.Action("GetCashPerson", (string)ViewBag.BRE_NO)")
        //  $(targetFormID).submit();

       }
       function ExportSave(){
           var txtUSER_NO = $("#txtUSER_NO").val();
           var txtPASSWORD = $("#txtPASSWORD").val();
           var SCHOOL_NO1 = $("#SCHOOL_NO1").val();
           var NAME = $("#NAME").val();
           var CARD_NO = $("#CARD_NO").val();
           var BarCode = $("#BarCode").val();
           var ROLL_CALL_NAME = $("#ROLL_CALL_NAME").val();
           var ROLL_CALL_ID = $("#ROLL_CALL_ID").val();
           var CASH = $("#CASH").val();
           var data = {
               "txtUSER_NO": txtUSER_NO,
               "txtPASSWORD": txtPASSWORD,
               "SCHOOL_NO": SCHOOL_NO1,
               "NAME": NAME,
               "CARD_NO": CARD_NO,
               "BarCode": BarCode,
               "ROLL_CALL_NAME": ROLL_CALL_NAME,
               "ROLL_CALL_ID": ROLL_CALL_ID,
               "CASH": CASH
           };
           $("#loginButton").attr("disabled", "disabled");
            $.ajax({
                   url: '@Url.Action("GetCashPerson", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    console.log(html);
                    $("#DetailsView").html('');
                    $("#DetailsView").html(html);
                }

            });

          //  $(targetFormID).attr("action", "@Url.Action("GetCashPerson", (string)ViewBag.BRE_NO)")
          //  $(targetFormID).submit();
       }
    </script>
}