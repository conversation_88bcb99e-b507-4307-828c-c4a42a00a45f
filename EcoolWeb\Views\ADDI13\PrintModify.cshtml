﻿@model RollCallBarcodeCashViewModel
@using ECOOL_APP
@{
    Layout = "~/Views/Shared/_LayoutEmpty1.cshtml";
    var itemCount = 0;
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
}
@section head{
    <link href="@Url.Content("~/Content/styles/PrintModify.min.css?v="+DateNowStr)" rel="stylesheet" />
}
<div class="print-nav">
    列印調整：<br />
    <button type="button" id="BtnSwitchQRcode">隱藏 QRcode</button>
    <button type="button" id="BtnSwitchBarCode">隱藏 BarCode</button>
    <button type="button" id="BtnSwitchTxt">隱藏 內文</button>
</div>
<ul class="list-unstyled print-modify-list">
    @if (Model != null && Model.Details != null && Model.Details.Count() > 0)

    {
        foreach (var item in Model.Details.OrderBy(x => x.CASH))
        {
            @:<li class="col-6">
                string str = "";
                str = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ADDI13/Index1?ROLL_CALL_ID=" + item.ROLL_CALL_ID + "&SCHOOL_NO1=" + item.SCHOOL_NO + "&Barcode=" + item.BarCode;
                <div class="d-flex align-items-center SwitchQRcode">
                    <div class="col-4">
                        <img src="@Url.Action("Cre", "Barcode", new { Value = str })" class="QRCode" />
                    </div>
                    <p class="col-8 print-modify-title">
                        恭喜你<span class="print-modify-icon"></span><br />
                        因為 @Model.Main.ROLL_CALL_NAME 活動<br />


                        @if (!Model.Main.IS_SHOW)
                        {


                            <span class="text-light">獲得酷幣 @item.CASH 點</span>
                        }
                        else
                        {
                            <span class="text-light">獲得酷幣神秘點數</span>

                        }
                    </p>
                </div>
                <div class="text-center SwitchBarCode">
                    <img src="@Url.Action("GenerateCode39", "Barcode", new { text = item.BarCode })" class="barCode my-0" />
                </div>
                <strong class="d-block text-center text-date text-info">請於 @string.Format("{0:yyyy-MM-dd HH:mm}", Model.Main.ROLL_CALL_DATEE) 前完成點數取得</strong>
                <div class="txt SwitchTxt">
                    你可以用以下的方式完成點數取得：
                    <ol>
                        <li>「先登入自己的E酷幣帳號」利用 酷幣秘書<br />取得點數 功能裡面打上代碼。
                            <span class="text-info text-number">@item.BarCode</span>
                        </li>
                        <li>用手機、平板掃描QRCODE，輸入帳號密碼完成點數取得。</li>
                        <li>
                            到學校指定的電腦輸入代碼( @item.BarCode )和感應你的數位學生證完成點數匯入。
                            <br />發行老師：@item.Cre_Person，第 @item.ROLL_NUM 張。
                        </li>
                    </ol>
                </div>
                <div class="d-block text-center">
                    <span class="text-info text-number SwitchTxt2 d-none">@item.BarCode</span>
                </div>
            @:</li>
            itemCount++;
        }
    }
</ul>
@section scripts{
    <script>
        //代碼變色
        let numberList = document.querySelectorAll('.text-number');
        numberList.forEach(function (e) {
            let str = e.textContent;
          
            let number1 = str.substring(0, 3);
            let number2 = str.substring(3, 6);
            let number3 = str.substring(6, 9);
            e.innerHTML = `${number1}<span class="text-pink">${number2}</span>${number3}`;

        });
        //節省版面-顯示隱藏QRCode
        let BtnSwitchQRcode = document.getElementById('BtnSwitchQRcode');
        let SwitchQRcodeState = true;
        BtnSwitchQRcode.addEventListener('click', function (e) {
            let QRcodeDoms = document.querySelectorAll('.SwitchQRcode');
            if (SwitchQRcodeState == true) {
                e.target.innerText = "顯示 QRcode";
                SwitchQRcodeState = false;
                QRcodeDoms.forEach(function (dom) {
                    dom.classList.remove('d-flex');
                    dom.classList.add('d-none');
                });
            } else {
                e.target.innerText = "隱藏 QRcode";
                SwitchQRcodeState = true;
                QRcodeDoms.forEach(function (dom) {
                    dom.classList.remove('d-none');
                    dom.classList.add('d-flex');

                });
            }
        });
        //節省版面-顯示隱藏BarCode
        let BtnSwitchBarCode = document.getElementById('BtnSwitchBarCode');
        let SwitchBarCodeState = true;
        BtnSwitchBarCode.addEventListener('click', function (e) {
            let BarCodeDoms = document.querySelectorAll('.SwitchBarCode');
            if (SwitchBarCodeState == true) {
                e.target.innerText = "顯示 BarCode";
                SwitchBarCodeState = false;
                BarCodeDoms.forEach(function (dom) {
                    dom.classList.add('d-none');
                });
            } else {
                e.target.innerText = "隱藏 BarCode";
                SwitchBarCodeState = true;
                BarCodeDoms.forEach(function (dom) {
                    dom.classList.remove('d-none');
                });
            }
        });
        //節省版面-顯示隱藏內文
        let BtnSwitchTxt = document.getElementById('BtnSwitchTxt');
        let SwitchTxtState = true;
        BtnSwitchTxt.addEventListener('click', function (e) {
            let BarCodeDoms = document.querySelectorAll('.SwitchTxt');
            let BarCodeDoms2 = document.querySelectorAll('.SwitchTxt2');
            if (SwitchTxtState == true) {
                e.target.innerText = "顯示 內文";
                SwitchTxtState = false;
                BarCodeDoms.forEach(function (dom) {
                    dom.classList.add('d-none');
                });
                BarCodeDoms2.forEach(function (dom) {
                    dom.classList.remove('d-none');
                });
            } else {
                e.target.innerText = "隱藏 內文";
                SwitchTxtState = true;
                BarCodeDoms.forEach(function (dom) {
                    dom.classList.remove('d-none');
                });
                BarCodeDoms2.forEach(function (dom) {
                    dom.classList.add('d-none');
                });
            }
        });
    </script>
}
