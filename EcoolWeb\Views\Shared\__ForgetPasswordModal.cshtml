﻿@{
    Layout = null;
}

<!--忘記密碼Modal-->
<div class="modal fade" id="fogetPasswordModal" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <span class="glyphicon glyphicon-wrench pull-left text-primary" style="font-size:2.5rem;padding:2%;" aria-hidden="true"></span>
                <h2 class="modal-title text-primary"><b>忘記密碼</b></h2>
            </div>
            <div class="modal-body">
                <label for="idNumber">身分證字號　</label>
                <input type="text" id="idNumber" class="fa-border" aria-describedby="helpidNumber" >
                <span id="idNumberError" class="text-danger" style="display:none;"></span><br />

                <label for="birthDay">出生年月日　</label>
                <input type="text" id="birthDay" class="fa-border" aria-describedby="helpbirthDay" ><span> (格式範例: 2006-1-1)</span>
                <span id="birthDayError" class="text-danger" style="display:none;"></span><br />

                <label for="theClassNo">班級座號(五碼)　</label>
                <input type="text" id="theClassNo" class="fa-border" aria-describedby="helptheClassNo" >
                <span> 
                    (範例: 601班1號，輸入60101，<span class="text-danger">老師和家長</span>免填本欄位)
                </span>
                <span id="theClassNoError" class="text-danger" style="display:none;"></span><br />

                <div>
                    <span for="verifyCode">驗證碼　</span>
                    <input type="text" class="fa-border" id="verifyCode" aria-describedby="helpverifyCode">
                    <img id="verifyCodeImg" src="@Url.Action("VerificationCode","Home")" height="80" width="180" style="margin:5px;border:1px solid lightgray;" />
                    <button class="btn btn-link" title="重取驗證" onclick="refreshVerifyCode()"><span class="glyphicon glyphicon-refresh"></span></button>
                    <span id="verifyCodeError" class="text-danger" style="display:none;"></span>
                </div>
                <span class="help-block">※填寫上面資料按下送出後，可回復您的密碼預設值。</span>

                <button class="btn btn-primary btn-block" onclick="submitForgetPassword()">送出</button>
                <br />
                <div id="reInfoDiv" class="">
                    <span></span>
                </div>
            </div>
        </div>
    </div>
</div>
<!--忘記密碼Modal結束-->
<script>
    /** 
     * 忘記密碼Modal彈出顯示
     */
    function openForgetPasswordModal() {
        $("#fogetPasswordModal").modal();
    }
    /** 
     * 重取驗證碼
     */
    function refreshVerifyCode() {
        var Url = '@Url.Action("VerificationCode", "Home")' + '?r='+(new Date()).getTime()+''
        $('#verifyCodeImg').attr('src', Url);
        $("#verifyCode").val('');
    }
    /**
     * Submit忘記密碼
     */
    function submitForgetPassword() {
        var idNumber = $("#idNumber").val();
        var birthDay = $("#birthDay").val();
        var theClassNo = $("#theClassNo").val();
        var verifyCode = $("#verifyCode").val();

        $("#idNumberError").hide();
        $("#birthDayError").hide();
        $("#theClassNoError").hide();
        $("#verifyCodeError").hide();

        var errorFlag = true;
        //驗證-身分證
        errorFlag &= verifyWithRegular(idNumber, /^[A-Z]{1}[1-2]{1}[0-9]{8}$/, $("#idNumberError"));
        //驗證-生日
        errorFlag &= verifyWithRegular(birthDay, /^\d{4}[\-](0?[1-9]|1[012])[\-](0?[1-9]|[12][0-9]|3[01])$/, $("#birthDayError"));
        //驗證-班級座號
        if (theClassNo.length > 0) {
            errorFlag &= verifyWithRegular(theClassNo, /^[0-9]+$/, $("#theClassNoError"));
        }
        //驗證-驗證碼
        errorFlag &= verifyWithRegular(verifyCode, null, $("#verifyCodeError")) ;

        if (!errorFlag) return;

        $.ajax({
            url: '@Url.Content("~/api/LoginApi/ForgetPassword")',
            type: 'POST',
            contentType: "application/json; charset=utf-8",
            dataType: 'json',
            data: JSON.stringify({
                IdNumber: idNumber,
                BirthDay: new Date(birthDay).toDateString(),
                ClassNo: theClassNo,
                Captcha: verifyCode
            }),
            success: function (data) {
                if (data.Data.Success) {
                    $("#reInfoDiv").attr("class", "alert alert-success");
                    // 訊息顯示並清空
                    $("#idNumber").val('')
                    $("#birthDay").val('');
                    $("#theClassNo").val('');
                } else {
                    $("#reInfoDiv").attr("class","alert alert-danger");
                }
                $("#reInfoDiv>span").text(data.Data.Message);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $("#reInfoDiv").addClass("alert alert-danger");
                $("#reInfoDiv span").text("發生了例外的錯誤。");
            },
            complete: function () {
                refreshVerifyCode();
            }
        });

        /**
         * 驗證方法
         * param input 輸入值
         * param regex 驗證規則
         * param displayElement 錯誤顯示Element
         */
        function verifyWithRegular(input, regex, displayElement) {
            if (input.length < 1) {
                displayElement.show();
                displayElement.text("*必填");
                return false;
            }
            if (regex != null) {
                if (!regex.test(input)) {
                    displayElement.show();
                    displayElement.text("*格式錯誤");
                    return false;
                }
            }
            return true;
        }
    }


</script>