﻿@model EcoolWeb.ViewModels.AWA004QueryViewModel

<script src="~/Scripts/timepicker/jquery-ui-sliderAccess.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@

<script src="~/Scripts/moment.min.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("PrintSetView", "AWA004", FormMethod.Post, new { name = "AWA004", id = "AWA004" }))
{
    @Html.HiddenFor(m => m.whereAWARD_NO)
    @Html.HiddenFor(m => m.whereSchoolNo)
    @Html.HiddenFor(m => m.whereGrade)
    @Html.HiddenFor(m => m.whereCLASS_NO)
    @Html.HiddenFor(m => m.whereKeyword)
    @Html.HiddenFor(m => m.OrderColumn)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.whereAWARD_SCHOOL_NO)
    @Html.HiddenFor(m => m.whereAWARD_TYPE)
    @Html.HiddenFor(m => m.whereSNAME)
    @Html.HiddenFor(m => m.whereAWARD_NAME)
    @Html.HiddenFor(m => m.whereSTATUS)
    @Html.Hidden("hHtml")
    @Html.Hidden("hidSelectTRANS_NO")


    <img src="~/Content/img/web-bar2-revise-17.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-AWA004">
        <div class="form-horizontal">
            <div class="Caption_Div">
                線上列印通知設定
            </div>
            <div style="height:25px"></div>

            <div id="editorRows" class="col-md-12">
                <div class="panel panel-info">
                    <div class="panel-heading">快速全部設定</div>
                    <div class="panel-body">
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-md-4 control-label">預計頒獎日</label>
                                <div class="col-md-8">
                                    <div class="input-group"> 
                                        @Html.Editor("SetExpectedDate", new { htmlAttributes = new { @class = "form-control input-md", @onChange = "SetAllDate()" } })
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-4 control-label">頒獎(集合)地點</label>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        @Html.Editor("SetContent", new { htmlAttributes = new { @class = "form-control input-md", @onChange = "SelAllContent()" } })
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="DivContent">
                @foreach (var item in Model.Chk.Where(a => a.CheckBoxNo == true).ToList())
                {
                        @Html.Partial("_TrPrintSetView", item)
                }
                </div>

            </div>
            <div style="height:25px"></div>
            <div class="row">
                <div class="col-md-12 col-xs-12 text-center">
                    <span class="input-group-btn">
                        <button class="btn btn-default btn-sm" type="button" onclick="onPrint()">列印</button>
                        <button class="btn btn-default btn-sm" type="button" onclick="onBack()">放棄 </button>
                    </span>
                </div>
            </div>
        </div>
    </div>
}

<script type="text/javascript">
    var targetFormID = '#AWA004';

    function onPrint() {
        $(targetFormID).attr('action','@Html.Raw(@Url.Action("PrintModify", "AWA004"))')
              $(targetFormID).attr('target', '_blank').submit().removeAttr('target');

        }

        function onBack() {
            $(targetFormID).attr('action','@Html.Raw(@Url.Action("ModifyQuery", "AWA004"))')
              $(targetFormID).submit();
    }

        var opt = {
            showMonthAfterYear: true,
            format: moment().format('YYYY-MM-DD h:mm:ss'),
            showSecond: true,
            showButtonPanel: true,
            showTime: true,
            beforeShow: function () {
                setTimeout(
                    function () {
                        $('#ui-datepicker-div').css("z-index", 15);
                    }, 100
                );
            },
            onSelect: function (dateText, inst) {
                $('#' + inst.id).attr('value', dateText);
            }
        };


        $('#SetExpectedDate').datetimepicker(opt);
        $('input[type=datetime]').datetimepicker(opt);

        function SetAllDate()
        {

            $('#DivContent input[type=datetime]').val($("#SetExpectedDate").val())
        }

        function SelAllContent()
        {
            $('#DivContent input[type=text]').val($("#SetContent").val())
        }
</script>













