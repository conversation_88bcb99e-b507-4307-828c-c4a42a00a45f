/*
 *  /MathJax/jax/output/CommonHTML/fonts/TeX/Fraktur-Regular.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(b){var a="MathJax_Fraktur";b.FONTDATA.FONTS[a]={className:b.FONTDATA.familyName(a),centerline:258,ascent:740,descent:224,32:[0,0,250,0,0],33:[689,12,296,91,204],34:[695,-432,215,8,196],38:[698,11,738,49,733],39:[695,-436,212,69,134],40:[737,186,389,114,293],41:[735,187,389,89,276],42:[692,-449,278,33,234],43:[598,82,756,47,709],44:[107,191,278,99,213],45:[275,-236,756,46,706],46:[102,15,278,87,200],47:[721,182,502,34,466],48:[492,13,502,42,456],49:[468,2,502,47,460],50:[474,-1,502,60,484],51:[473,182,502,39,429],52:[476,191,502,10,481],53:[458,184,502,47,440],54:[700,13,502,45,471],55:[468,181,502,37,498],56:[705,10,502,40,461],57:[469,182,502,28,466],58:[457,12,216,50,168],59:[458,189,216,47,179],61:[368,-132,756,54,725],63:[693,11,362,46,357],65:[696,26,718,22,708],66:[691,27,884,48,820],67:[685,24,613,59,607],68:[685,27,832,27,745],69:[685,24,663,86,634],70:[686,153,611,11,612],71:[690,26,785,66,710],72:[666,133,720,1,644],73:[686,26,554,30,532],74:[686,139,552,-10,522],75:[680,27,668,17,682],76:[686,26,666,33,644],77:[692,27,1050,27,1048],78:[686,25,832,27,825],79:[729,27,827,12,744],80:[692,218,828,28,804],81:[729,69,827,11,782],82:[686,26,828,27,824],83:[692,27,829,66,756],84:[701,27,669,34,676],85:[697,27,646,-25,665],86:[686,26,831,26,825],87:[686,27,1046,32,1054],88:[688,27,719,28,709],89:[686,218,833,27,740],90:[729,139,602,11,532],91:[740,130,278,117,278],93:[738,131,278,-4,160],94:[734,-452,500,0,495],97:[470,35,500,66,497],98:[685,31,513,87,442],99:[466,29,389,72,359],100:[609,33,499,13,428],101:[467,30,401,70,364],102:[681,221,326,30,323],103:[470,209,504,17,455],104:[688,205,521,77,434],105:[673,20,279,14,267],106:[672,208,281,-9,196],107:[689,25,389,24,362],108:[685,20,280,98,276],109:[475,26,767,8,753],110:[475,22,527,20,514],111:[480,28,489,67,412],112:[541,212,500,12,430],113:[479,219,489,60,419],114:[474,21,389,17,387],115:[478,29,443,-18,406],116:[640,20,333,27,348],117:[474,23,517,9,513],118:[530,28,512,55,434],119:[532,28,774,45,688],120:[472,188,389,10,363],121:[528,218,499,45,431],122:[471,214,391,-7,314],160:[0,0,250,0,0],8216:[708,-410,215,45,158],8217:[692,-395,215,49,163],58112:[683,32,497,75,430],58113:[616,30,498,35,432],58114:[680,215,333,29,339],58115:[679,224,329,28,318],58116:[471,214,503,52,449],58117:[686,20,333,26,315],58118:[577,21,334,29,347],58119:[475,22,501,10,514]};b.fontLoaded("TeX/"+a.substr(8))})(MathJax.OutputJax.CommonHTML);
