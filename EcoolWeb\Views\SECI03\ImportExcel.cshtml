﻿@model EcoolWeb.Models.SECI03ImportExcelViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ActionLink("回健康秘書首頁", "Index", null, new { @class = "btn btn-sm btn-sys", @role = "button" })
@using (Html.BeginForm("ImportExcel", "SECI03", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })


    <div class="panel-heading text-center" style="background-color:rgba(68, 157, 68, 1);color:rgba(249, 242, 244, 1);font-size:medium;margin-bottom:10px">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">
        <div class="form-horizontal">

            <div class="form-group">
                <label class="control-label col-md-3" for="QUESTIONS_TXT">學年</label>
                <div class="col-md-9">
                    @*@Html.EditorFor(model => model.SYEAR, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入數字" } })
                        @Html.ValidationMessageFor(model => model.SYEAR, "", new { @class = "text-danger" })*@
                    @Html.DropDownListFor(model => model.SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYEARItems, new { @class = "form-control input-sm" })
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-3" for="QUESTIONS_TXT">學期</label>
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.SEMESTER, (IEnumerable<SelectListItem>)ViewBag.SEMESTERItems, new { @class = "form-control input-sm" })
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳Excel檔</label>
                <div class="col-md-9">
                    <input class="btn btn-default form-control" type="file" name="files" placeholder="必填" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">資料類型</label>
                <div class="col-md-3">
                    @Html.RadioButtonFor(model => model.ImportType, "HRMT08", new { htmlAttributes = new { @class = "form-control" } })
                    @Html.Label("身高體重視力資料")
                </div>
                <div class="col-md-3">
                    @Html.RadioButtonFor(model => model.ImportType, "HRMT09", new { htmlAttributes = new { @class = "form-control" } })
                    @Html.Label("體適能資料")
                </div>
            </div>
            <div class="form-group text-center">
                <button class="btn btn-default">上 傳 Excel </button>
            </div>
            <div class="form-group">
                <div class="col-md-offset-1 col-md-9">
                    <label class="text-danger">
                        上傳說明:<br />
                        1.上傳之 Excel 檔, 請依規定格式填寫(<a href="~/Content/ExcelSample/身高體重視力範本.xls" target="_blank" class="btn-table-link">下載身高體重視力範本S</a>),(<a href="~/Content/ExcelSample/體適能範本.xls" target="_blank" class="btn-table-link">下載體適能範本</a>)<br />
                        2.如果有修改，請下載歷史紀錄修正後上傳；或從健康中心(教育部體適能)系統下載後上傳。<br />
                        3.檔名不要是中文、特殊符號之類
                    </label>
                </div>
            </div>

        </div>
    </div>

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            匯入Excel歷史紀錄
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                <thead>
                    <tr>
                        <th style="text-align: center">
                            學校代號
                        </th>
                        <th style="text-align: center">
                            學年
                        </th>
                        <th style="text-align: center;">
                            學期
                        </th>
                        <th style="text-align: center;">
                            筆數
                        </th>
                        <th style="text-align: center;">
                            操作時間
                        </th>
                        <th style="text-align: center">
                            資料類型
                        </th>
                        <th style="text-align: center">
                            下載
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model != null)
                    {
                        foreach (var item in Model.LogList)
                        {
                            if (item.IMPORT_TYPE == "HRMT08")
                            {
                                item.IMPORT_TYPE = "健康資料";
                            }
                            else if (item.IMPORT_TYPE == "HRMT09")
                            {
                                item.IMPORT_TYPE = "體適能資料";
                            }
                            <tr>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SCHOOL_NO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SYEAR)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SEMESTER)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.RowsCount)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.EVENT_TIME)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.IMPORT_TYPE)
                                </td>
                                <td style="text-align: left;white-space:normal">
                                    @Html.ActionLink("下載", "DownloadExcel", new { FilePath = item.FILE_PATH }) @*, new { @class="btn btn-sm btn-sys"})*@
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>


    <div>
        @if (Model != null)
        {
            @Html.Pager(Model.LogList.PageSize, Model.LogList.PageNumber, Model.LogList.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
        }
      
    </div>
}
