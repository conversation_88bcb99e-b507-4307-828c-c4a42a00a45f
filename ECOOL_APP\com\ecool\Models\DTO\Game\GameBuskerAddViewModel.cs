﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.EF
{
    public class GameBuskerAddViewModel
    {
        public string GAME_NO { get; set; }

        [DisplayName("表演名稱")]
        public string TITLE_SHOW_NAME { get; set; }


        public string WebCamBase64 { get; set; }

        public HttpPostedFileBase UploadBuskerFile { get; set; }


        public string GameUserID { get; set; }

        public string TITLE_IMG { get; set; }
        /// <summary>
        /// 明細
        /// </summary>
        public virtual ICollection<GameBuskerAddDetailsViewModel> Details { get; set; }


        public bool IsOK { get; set; }


        public string Message { get; set; }

    }
}
