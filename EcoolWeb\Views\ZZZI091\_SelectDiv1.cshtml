﻿
<style>
    #progress-container {
        width: 100%;
        background-color: #ccc;
    }

    #progress-bar {
        width: 0%;
        height: 30px;
        background-color: #4caf50;
        text-align: center;
        line-height: 30px;
        color: white;
    }
</style>
@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}
else
{
    using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
    {
        {

            @Html.Hidden("wIsQhisSchool", (bool)ViewBag.wIsQhisSchool)
            <link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
            <script src="~/Content/colorbox/jquery.colorbox.js"></script>
            <label class="control-label"> * @ViewBag.Panel_Title</label>
            <br /><br />
            if (ViewBag.wIsQhisSchool == false)
            {
                //@Html.Hidden("SCHOOL_NO", (string)ViewBag.SCHOOL_NO)
                @Html.Hidden("IDNO", "")
                <div class="form-group">
                    @Html.Label("班級", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                    <div class="col-md-9">
                        @Html.DropDownList("CLASS_NO", (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control"})
                        @Html.ValidationMessage("CLASS_NO", "", new { @class = "text-danger" })
                    </div>
                </div>
                @Html.Hidden("USER_NO", "")
                @*<div class="form-group">
                        @Html.Label("姓名", htmlAttributes: new { @class = "control-label col-md-3", @for = "USER_NO" })
                        <div class="col-md-9">
                            @Html.DropDownList("USER_NO", (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
                            @Html.ValidationMessage("USER_NO", "", new { @class = "text-danger" })
                        </div>
                    </div>*@
                <div class="form-group">
                    @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "Where_SCHOOLNO" })
                    <div class="col-md-9">
                        @Html.DropDownList("SCHOOL_NO", (IEnumerable<SelectListItem>)ViewBag.SchoolItems, new { @class = "form-control", onchange = "$('#CLASS_NO').val('');this.form.submit();" })
                        @Html.ValidationMessage("SCHOOL_NO", "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.Label("是否匯出閱讀認證", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                    <div class="col-md-9">
                        否<input type="checkbox" id="ReadYN" name="ReadYN" />
                    </div>

                </div>

                <div class="form-group">
                    @Html.Label("封面", htmlAttributes: new { @class = "control-label col-md-3", @for = "COVERJPG" })
                    <div class="col-md-9">
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverA.jpg" checked>
                        <img style="Max-width:70px" class="cboxElement " id="cover" src="~/Content/img/coverA.jpg" href="~/Content/img/coverA.jpg" />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverB.jpg">
                        <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverB.jpg" href="~/Content/img/coverB.jpg" />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverC.jpg">
                        <img style="Max-width:70px" class="cboxElement " id="cover" src="~/Content/img/coverC.jpg" href="~/Content/img/coverC.jpg" />

                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverD.jpg">
                        <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverD.jpg" href="~/Content/img/coverD.jpg" />
                        <br />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverE.jpg">
                        <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverE.jpg" href="~/Content/img/coverE.jpg" />
                        <br />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverF.jpg"> <b>自行上傳封面</b>
                        @*<input class="form-control input-md" id="COVERJPG" name="COVERJPG" type="file" value="">*@
                        <input class="form-control input-md" id="UploadCoverFile" name="UploadCoverFile" type="file" value="" onclick="checkRadio(this)">

                        <label class="text-info"><b style="font-size:20px">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片;(長:1569px;寬1110px)</b></label>
                    </div>
                </div>
                <div class="form-group text-left">
                    @Html.Label("開始日期", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.Editor("S_DATE", new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                    </div>
                </div>
                <div class="form-group text-left">
                    @Html.Label("結束日期", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.Editor("E_DATE", new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                    </div>
                </div>
            }
            else
            {
                <div class="form-group">
                    @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "SCHOOL_NO" })
                    <div class="col-md-9">
                        @Html.DropDownList("SCHOOL_NO", (List<SelectListItem>)ViewBag.SCHOOL_NOItems, new { @class = "form-control" })
                        @Html.ValidationMessage("SCHOOL_NO", "", new { @class = "text-danger" })
                    </div>
                </div>


                @Html.Hidden("CLASS_NO", "")
                @Html.Hidden("USER_NO", "")
                @Html.Hidden("S_DATE", "")
                @Html.Hidden("E_DATE", "")
                @Html.Hidden("IDNO", (string)ViewBag.IDNO)
            }

            <div class="form-group">
                <div class="col-md-offset-3 col-md-9 text-center">
                    <button type="button" class="btn btn-default btn-block" id="BtnSave" onclick="ChangeUSER_NOUseReplaceWith()">匯出</button>
                </div>
            </div>
            <div class="loader">
                <center>
                    <h1 class="loading-image">
                        <span class="glyphicon glyphicon-search"></span>查詢中，請稍後 ...
                    </h1>
                </center>
            </div>
        }
    }
            <div class="modal fade" id="myModal" role="dialog">
                <div class="modal-content">

                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">正在備份中，這步驟需要一段時間，會先跑完程式，才開始下載，請靜待。</h4>
                    </div>
                    <div id="counter"></div>
                </div>
                <div class="modal-body">
                    <div class="progress progress-striped active">
                        <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                            <span class="sr-only">0% 完成</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
                </div>
            </div>
    <script type="text/javascript">
        var targetFormID = '#formEdit';
        $("#S_DATE,#E_DATE").datepicker({
            dateFormat: "yy/mm/dd",
            changeMonth: true,
            changeYear: true,
            showOn: "button",
            buttonImage: "../Content/img/icon/calendar.gif",
            buttonImageOnly: true,
        });
        $(document).ready(function () {
            $('.loader').hide();
            $("#cover").colorbox({ opacity: 0.82 });
        });
        window.onload = function () {
            if ($("#CLASS_NO").val() != "") {
          
                $("#IDNO").val("@ViewBag.IDNO")
            }
        }
        function checkRadio(obj) {

            $("input[value='coverF.jpg']").attr("checked", "checked")

        }
        function CKTEMP() {
            var formdata = new FormData($('form').get(0));
            $.ajax({
                url: '@Url.Action("useBatGenertPdftemp", (string)ViewBag.BRE_NO)',
                  data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',
                success: function (data) {
                    console.log(data.resault);
                    var res = data.resault;
                    return data.resault;
                },
                beforeSend: function () {

                },
                complete: function () {
                    //  setTimeout(function () { $('.loader').hide(); }, 1000);
                }
            });
        }
        function ExcuBat() {


               $.ajax({
                url: '@Url.Action("ExcuBat", (string)ViewBag.BRE_NO)',
               
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                      success: function (data) {
                          console.log(data.resault);
                          var res = data.resault;
                         
                          return data.dcount;
                      },
                      beforeSend: function () {
                        
                      },
                      complete: function () {
                        //  setTimeout(function () { $('.loader').hide(); }, 1000);
                      }

               });
        }
        function LogFileSize() {
            var COVERJPGValue = "";
            COVERJPGValue = $('input[name*=COVERJPG]:checked').val();
            var formdata = new FormData($('form').get(0));
              var COVERJPGValue = "";
                COVERJPGValue = $('input[name*=COVERJPG]:checked').val();
                var formdata = new FormData($('form').get(0));
              //  $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
                  $.ajax({
                url: '@Url.Action("logFileSize", (string)ViewBag.BRE_NO)',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                      success: function (data) {
                          console.log(data.resault);
                          var res = data.resault;
                         
                          return data.dcount;
                      },
                      beforeSend: function () {
                        
                      },
                      complete: function () {
                        //  setTimeout(function () { $('.loader').hide(); }, 1000);
                      }

                  });



        }
        function CK() {
      
                 var Msg = '';
          
            if ($('#USER_NO').val() == '' && $('#IDNO').val() == '') {
                Msg = Msg + '請選擇「' + $("label[for='USER_NO']").text() + '」\n';
            }

            if (Msg != '') {
                alert(Msg);
            }
            else {

                var COVERJPGValue = "";
                COVERJPGValue = $('input[name*=COVERJPG]:checked').val();
                var formdata = new FormData($('form').get(0));
              //  $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
                  $.ajax({
                url: '@Url.Action("useBatGenertPdf", (string)ViewBag.BRE_NO)',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                      success: function (data) {
                          console.log(data.resault);
                          var res = data.resault;
                         
                          return data.dcount;
                      },
                      beforeSend: function () {
                        
                      },
                      complete: function () {
                        //  setTimeout(function () { $('.loader').hide(); }, 1000);
                      }

                  });
                var file = $("#UploadCoverFile").val();
                var fileName = getFileName(file);
                var StrWhere = '?School_No=' + $('#SCHOOL_NO').val() + '&USER_NO=' + $('#USER_NO').val() + '&S_DATE=' + $('#S_DATE').val() + '&E_DATE=' + $('#E_DATE').val() + '&IDNO=' + $('#IDNO').val() + '&COVERJPG=' + COVERJPGValue + '&UploadCoverFileName=' + fileName + '&redirect=s&ReadYN=' + $("#ReadYN").prop("checked")
                var StrWhere = formdata;
                @*setTimeout(function () {
                 window.open('@Url.Action("useBatGenertPdf")' + StrWhere + '', '_blank');
                }, 8000); // 在3秒后执行匿名函数*@








            }
        }
        function getFileName(o) {
            var pos = o.lastIndexOf("\\");
            return o.substring(pos + 1);
        }
        function SetUSER_NODDLEmpty() {
            $('#USER_NO').empty();
            $('#USER_NO').append($('<option></option>').val(' ').text('請選擇' + $("label[for='CLASS_NO']").text() + '...').prop('selected', true));
        }
        function SHowModel() {
            $("#myModal").modal('show');
          

        }

        function createCounter(element,init, options = {}) {
            const { initialCount = init, interval = 1000 } = options
            let counter = 0
            let showCount = 0;
            let counter2 = 0;
            let timer
            const setCounter = (count) => {
                counter = count
                showCount = counter / 1000;
                counter2 = counter / 60000
                render()
            }
            const render = () => {
                element.innerHTML = `<button>${showCount}秒</button><span>${counter2}分鐘</span>`
            }
            //setCounter(initialCount)
            //timer = setInterval(() => {
            //    if (counter <= 0) {
            //        return clearInterval(timer)
            //    }
            //    setCounter(counter - 1000)
            //}, interval)
        }
        function ChangeUSER_NOUseReplaceWith() {
            SHowModel();
            const counterElement = document.querySelector('#counter')
          

            var selectedCLASS_NO = $.trim($('#CLASS_NO option:selected').val());
            var SCHOOLNO = "";
            var USER_NO = "";
            if (selectedCLASS_NO.length == 0) {
                SetUSER_NODDLEmpty();
            }
            else {


                var array ;
                $.ajax(
                {
                    url: '@Url.Action("_GetUSER_NODDLHtmlForALLstr", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: 'USER_NO',
                        tagName: 'USER_NO',
                        wSCHOOL_NO: '@ViewBag.SCHOOL_NO',
                        wUSER_NO: '@ViewBag.USER_NO',
                        wCLASS_NO: selectedCLASS_NO,
                        wIDNO : '@ViewBag.IDNO',
                        wDATA_ANGLE_TYPE: '@ViewBag.DATA_ANGLE_TYPE'
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        if (data.length > 0) {
                           // $('#USER_NO').replaceWith(data);
                            var width = 0;
                            var ARCount = 0;
                            var init = 0;
                            var init2 = 0;
                            var Minit1 = 0;
                            var CheckInit = 0;
                            array = JSON.parse(data);
                            if (Array.isArray(array)) {
                                init = array.length;
                                init2 = array.length;
                                console.log("數組的長度是: " + init); // 輸出: 數組的長度是: 3
                            } else {
                                console.log("解析結果不是數組");
                            }
                           
                            var setTimetimer = 8000;
                            ARCount = array.length + 1;

                            if (array.length != 0) { 
                                width = 100 / ARCount;
                                ARCount * 5000;
                                init = (init ) * 300000
                                init2 = init2 / 3;
                                width = init + 60000
                                Minit1 = Math.round(init / 60000);
                                CheckInit = init - 6000;
                            }
                            $(".modal-title").html("正在備份中，這步驟需要一段時間，會先跑完程式，才開始下載，請靜待。預估費時約" + Minit1+"分鐘");
                          
                           
                            array.forEach(function (object) {
                             //   console.log(object);
                                $("#USER_NO").val(object)
                                var str = CK();
                            
                                console.log("隊的" + str);
                                setTimeout(function () {
                                    console.log("隊的");

                                }, 300000);
                               
                                USER_NO = "";
                                SCHOOLNO = $('#SCHOOL_NO').val()
                              
                                //init += width;
                                //$("#barr").css("width", init+"%");
                            }

                            );
                            init
                       
                       
                    createCounter(counterElement, width);
                            $("#barr").css("width", "50%");

                               setTimeout(function () {
                                   ExcuBat();
                               }, 3000);
                            //setTimeout(function () {

                            //    LogFileSize()

                            //}, CheckInit);
                            setTimeout(function () {
                                window.location = "@Url.Action("ExportPdfZip", (string)ViewBag.BRE_NO )?SCHOOL_NO=" + $('#SCHOOL_NO').val() + '&USER_NO=' + $('#USER_NO').val();
                            }, init);
                            $("#barr").css("width", "80%");
                       
                            //setTimeout(function () {
                              
                            //}, 90000);
                            
                            setTimeout(function () {
                                $("#barr").css("width", "100%");
                                $("#myModal").modal('hide');
                                     window.location = "@Url.Action("DeleteZip", (string)ViewBag.BRE_NO )?SCHOOL_NO=" + $('#SCHOOL_NO').val() + '&USER_NO=' + $('#USER_NO').val();
                            }, width);
                            }
                    }
                });
            }
        }
    </script>

}