﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public class HRMT01QTYTemp2
    {
        public string SCHOOL_NO { get; set; }
        public string USER_NO { get; set; }
        public string USER_KEY { get; set; }
        public string NAME { get; set; }
        public Nullable<byte> SYEAR { get; set; }
        public string SNAME { get; set; }
        public string SEX { get; set; }
        public Nullable<byte> GRADE { get; set; }
        public string CLASS_NO { get; set; }
        public string SEAT_NO { get; set; }
        public Nullable<System.DateTime> BIRTHDAY { get; set; }
        public Nullable<byte> USER_STATUS { get; set; }
        public string USER_TYPE { get; set; }
        public System.Nullable<int> ADI06 { get; set; }
        public System.Nullable<int> ADI07 { get; set; }
        public System.Nullable<int> Extend { get; set; }
        public System.Nullable<int> SUMCash { get; set; }
        public System.Nullable<int> AWT08Exced { get; set; }
        public System.Nullable<int> AWT08ExcedREADYBATCHID{ get; set; }
         public System.Nullable<int> AWT08Exted { get; set; }
        public System.Nullable<int> AWT08ExtedRedey { get; set; }
    }
}