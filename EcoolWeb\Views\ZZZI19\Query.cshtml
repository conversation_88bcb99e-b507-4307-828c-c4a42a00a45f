@model ZZZI19IndexViewModel
@{

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "學生清單維護";
    int i = 0;


    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}


<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" nonce="cmlvaw" />
<script src="~/Content/colorbox/jquery.colorbox.js" nonce="cmlvaw"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<br />
<label class="label_dt">登入次數說明:登入次數為「網頁登入」與「APP登入」，一年內登入加總。</label>


@using (Html.BeginForm("QUERY", "ZZZI19", FormMethod.Post, new { id = "ZZZI19", name = "ZZZI19" }))
{
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.whereUserNo)
    @Html.HiddenFor(m => m.Page)


    <br />
    <div class="form-inline" role="form" id="Q_Div">
        @if (ViewBag.VisibleTurnIn=="Y" && ViewBag.ShowTurnInCount > 0)
        {
            <a href='@Url.Action("Index", "ZZZI20")' role="button" class="btn btn-sm btn-sys">
                轉學生酷幣轉匯(@ViewBag.ShowTurnInCount )
            </a>
            <br /><br />
        }
        <div class="form-group">
            <label class="control-label">學號/姓名</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control  input-sm" } })
        </div>
        <br/><br />
        <div class="form-group">
            <label class="control-label">狀態</label>
        </div>
        <div class="form-group">
          @Html.DropDownListFor(m => m.whereStatus, (IEnumerable<SelectListItem>)ViewBag.StatusItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">年級</label>
        </div>
        <div class="form-group">
          @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereClass_No, (IEnumerable<SelectListItem>)ViewBag.ClassNoItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <br />
    
    <div class="row">
        <div class="col-xs-12 text-right">
            顯示 @Html.DropDownListFor(m => m.ShowPageCount, (IEnumerable<SelectListItem>)ViewBag.PageCount, new { onchange = "btnSearch_onclick();" }) 筆
        </div>
    </div>
   <div style="height:5px"></div>
    <div class="panel panel-ACC">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            選擇
                            <input type="checkbox" id="chkALL" name="chkALL" />
                        </th>
                        <th style="text-align: center;" onclick="doSort('GRADE');">
                            年級
                            <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center" onclick="doSort('CLASS_NO');">
                            班級
                            <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                            學號
                            <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                            座號
                            <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center">
                            姓名
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('USER_STATUS');">
                            狀態
                            <img id="USER_STATUS" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('LoginCount');">
                            登入次數
                            <img id="LoginCount" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center">
                            密碼異動
                        </th>
                        <th style="text-align: center">
                            家長密碼
                        </th>
                    </tr>
                </thead>
              <tbody>
                  @foreach (var item in Model.HRMT01List)
                {
                      <tr align="center">
                          <td>
                              <input name="DataList[@i].Chk" type="checkbox" value="true" @(Model.DataList[i].Chk ? "checked=\"checked\"" : "") />
                              <input name="DataList[@i].USER_NO" type="hidden" value="@Model.DataList[i].USER_NO" />
                              <input name="DataList[@i].SCHOOL_NO" type="hidden" value="@Model.DataList[i].SCHOOL_NO" />
                          </td>
                          <td>
                              @HRMT01.ParserGrade(byte.Parse(item.GRADE))
                          <td>
                              @Html.DisplayFor(modelItem => item.CLASS_NO)
                          </td>
                          <td>
                              @Html.DisplayFor(modelItem => item.USER_NO)
                          </td>
                          <td>
                              @Html.DisplayFor(modelItem => item.SEAT_NO)
                          </td>
                          <td>
                              @Html.DisplayFor(modelItem => item.NAME)
                          </td>
                          <td>
                              @UserStaus.GetDesc(item.USER_STATUS)
                          </td>
                          <td>
                              @Html.DisplayFor(modelItem => item.LoginCount)
                          </td>
                          <td>
                              <button id="ResetPassword_@i" class="btn btn-xs btn-Basic" type="button" href="@Url.Action("ResetPassword", "ZZT08", new { STATUS = "EDIT", SCHOOL_NO = item.SCHOOL_NO, USER_NO = item.USER_NO })">重設密碼</button>
                          </td>
                          <td>
                              <button id="ResetPasswordP_@i" class="btn btn-xs btn-Basic" type="button" href="@Url.Action("ResetParentPassword", "ZZT08", new { STATUS = "EDIT", SCHOOL_NO = item.SCHOOL_NO, USER_NO = item.USER_NO })">重設家長密碼</button>
                          </td>
                          <td>
                              @if (ViewBag.IsADDOne == true)
                              {
                                  if (UserStaus.GetDesc(item.USER_STATUS) == "停用")
                                  {


                                  }
                                  else
                                  {

                                      <button id="OUT_@i" class="btn btn-xs btn-Basic" type="button" href="@Url.Action("Edit1", "ZZZI19", new { whereKeyword = item.SCHOOL_NO, whereUserNo = item.USER_NO })">轉出</button>
                                  }}
                              </td>
                      </tr>
                      i++;
                  }
              </tbody>
            </table>
        </div>
        <div class="panel-footer text-center">
            <input type="button" id="btnStartUser" value="啟用" class="btn btn-default" onclick="btnStartUser_onclick();" />
            <input type="button" id="btnUnLockUser" value="解鎖定" class="btn btn-default" onclick="btnStartUser_onclick();" />
            <input type="button" id="btnCancelUser" value="停用" class="btn btn-default" onclick="btnCancelUser_onclick();" />
            @if (ViewBag.IsADDOne == true)
            {
                <input type="button" id="btnAddStudent" value="新增單筆學生" class="btn btn-default" onclick="btnADDUser_onclick()" />
            }


            @*<input type="button" id="ADD"  value="新增" class="btn btn-default" onclick="btnADDUser_onclick();" />*@
        </div>
    </div>
    <div>
        @Html.Pager(Model.HRMT01List.PageSize, Model.HRMT01List.PageNumber, Model.HRMT01List.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
    )
    </div>








}

@section scripts{
    <script nonce="cmlvaw">
        var targetFormID = '#ZZZI19';

        $(function() {
            if ("@ViewBag.Msg" === "轉出成功" || "@TempData["StatusMessage"]" === "轉出成功") {
                setTimeout(function() {
                    window.parent.location.reload();
                }, 2000);
            }
            
            $('#@Html.IdFor(m=>m.OrdercColumn)').val('');
            $("[id^='ResetPassword']").colorbox({ 
                iframe: true, 
                width: '50%', 
                height: '50%', 
                opacity: 0.82 
            });
            $("[id^='OUT']").colorbox({ 
                iframe: true, 
                width: '50%', 
                height: '50%', 
                opacity: 0.82 
            });
        });

        function doSort(VAL) {
            $('#@Html.IdFor(m=>m.OrdercColumn)').val(VAL);
            document.ZZZI19.enctype = "multipart/form-data";
            document.ZZZI19.action = "QUERY";
            document.ZZZI19.submit();
        }

        function btnCancelUser_onclick() {
            document.ZZZI19.enctype = "multipart/form-data";
            document.ZZZI19.action = "CancelUser";
            document.ZZZI19.submit();
        }

        function btnADDUser_onclick() {
            $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)");
            $(targetFormID).submit();
        }

        function btnStartUser_onclick() {
            document.ZZZI19.enctype = "multipart/form-data";
            document.ZZZI19.action = "StartUser";
            document.ZZZI19.submit();
        }

        function btnSearch_onclick() {
            document.ZZZI19.enctype = "multipart/form-data";
            document.ZZZI19.action = "Query";
            document.ZZZI19.submit();
        }

        $("#chkALL").on('click', function() {
            var isChecked = $(this).prop("checked");
            $("input:checkbox").prop("checked", isChecked);
        });

        function FunPageProc(page) {
            if ($(targetFormID).length > 0) {
                $('#Page').val(page);
                $(targetFormID).submit();
            }
        }

        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1);
        }

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1);
        }

        function todoClear() {
            $('#Q_Div').find(":input,:selected").each(function() {
                var type = $(this).attr('type');
                var InPreadonly = $(this).attr('readonly');
                var tag = this.tagName.toLowerCase();

                if (InPreadonly === false || InPreadonly === undefined) {
                    if (type === 'radio' || type === 'checkbox') {
                        if ($(this).attr("title") === 'Default') {
                            $(this).prop("checked", true);
                        } else {
                            $(this).prop("checked", false);
                        }
                    } else if (tag === 'select') {
                        this.selectedIndex = 0;
                    } else if (type === 'text' || type === 'hidden' || type === 'password' || type === 'textarea') {
                        $(this).val('');
                    }
                }
            });

            FunPageProc(1);
        }
    </script>
}