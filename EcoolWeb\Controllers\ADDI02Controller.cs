﻿using com.ecool.service;
using ECOOL_APP;
using EcoolWeb.Models;
using EcoolWeb.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Ionic.Zlib;
using Ionic.Zip;
using System.IO;
using System.Collections;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;

using System.Data.Entity.Validation;
using System.Text;
using System.Text.RegularExpressions;
using System.Drawing;
using MvcPaging;
using Dapper;
using ECOOL_APP.com.ecool.service;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI02Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        // GET: ADDI02

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ADDI02";

        public ActionResult QUERY()
        {
            return View();
        }

        public ActionResult _PageContent(ADDT06ViewModel Data, int pageSize = 20)
        {
            UserProfile user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            var TenpADDT06 = db.ADDT06.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL);
            if (!string.IsNullOrWhiteSpace(Data.whereAPPLY_STATUS))
            {
                TenpADDT06 = TenpADDT06.Where(X => X.APPLY_STATUS == Data.whereAPPLY_STATUS);
            }
            if (user.USER_TYPE == UserType.Student)
            {
                TenpADDT06 = TenpADDT06.Where(a => a.USER_NO == user.USER_NO);

          
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                var arrUSER_NO = HRMT06.GetArrMyPanyStudent(user, db);

                if (arrUSER_NO != null)
                {
                    TenpADDT06 = TenpADDT06.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);
                }
                else
                {
                    TenpADDT06 = TenpADDT06.Where(a => a.USER_NO == null);
                }
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
                {
                    TenpADDT06 = TenpADDT06.Where(a => a.CLASS_NO == user.TEACH_CLASS_NO);
                }

                TenpADDT06 = TenpADDT06.Where(a => a.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave || a.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back);
            }

            if (string.IsNullOrWhiteSpace(Data.whereKeyword) == false)
            {
                TenpADDT06 = TenpADDT06.Where(a => a.BOOK_NAME.Contains(Data.whereKeyword));
            }

            if (string.IsNullOrWhiteSpace(Data.OrdercColumn) == false)
            {
                if (Data.SyntaxName == "Desc")
                {
                    switch (Data.OrdercColumn)
                    {
                        case "BOOK_NAME":
                            TenpADDT06 = TenpADDT06.OrderByDescending(a => a.BOOK_NAME).ThenBy(a => a.APPLY_NO);
                            break;

                        default:
                            TenpADDT06 = TenpADDT06.OrderByDescending(X => X.APPLY_NO);
                            break;
                    }
                }
                else
                {
                    switch (Data.OrdercColumn)
                    {
                        case "BOOK_NAME":
                            TenpADDT06 = TenpADDT06.OrderBy(a => a.BOOK_NAME).ThenBy(a => a.APPLY_NO);
                            break;

                        default:
                            TenpADDT06 = TenpADDT06.OrderBy(X => X.APPLY_NO);
                            break;
                    }
                }
            }
            else
            {
                TenpADDT06 = TenpADDT06.OrderByDescending(X => X.APPLY_NO);
            }

            Data.ADDT06List = TenpADDT06.ToPagedList(Data.Page > 0 ? Data.Page - 1 : 0, pageSize);

            if (user.USER_TYPE == UserType.Student)
            {
                string sSQL = @"select a.BOOK_QTY,b.LEVEL_DESC, b.LEVEL_ID
							 ,Case when a.LEVEL_ID<10 Then (select Top 1 L.LEVEL_QTY from ADDT07 L (nolock) Where L.LEVEL_ID>a.LEVEL_ID order by L.LEVEL_ID)-a.BOOK_QTY
							       else 0 end as UNLEVEL_QTY
							 from ADDT09 a (nolock)
							 left outer join ADDT08 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.LEVEL_ID=b.LEVEL_ID
							 where a.SCHOOL_NO=@SCHOOL_NO  and a.USER_NO=@USER_NO ";
                Data.MyData = db.Database.Connection.Query<ADDTMyDataViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = user.SCHOOL_NO,
                    USER_NO = user.USER_NO,
                }).FirstOrDefault();
            }

            if (Data.MyData == null) Data.MyData = new ADDTMyDataViewModel();

            return PartialView(Data);
        }

        /// <summary>
        /// 上傳成功畫面
        /// </summary>
        /// <returns></returns>
        public ActionResult QUERY2()
        {
            return View();
        }

        /// <summary>
        /// 上傳成功畫面 - 修改推薦
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult QUERY2(IEnumerable<ADDT06> model)
        {
            UserProfile user = UserProfileHelper.Get();
            short ChangeShare = 5;
            string seatstr = "";
            if (model != null && model.Count() > 0) {
                if (model.Where(x=>x.SHARE_YN == "Y").Count()>0)
                {
                    seatstr = "您已經幫" + model?.FirstOrDefault().CLASS_NO + "班,座號  ";
                }
            }
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            foreach (var item in model)
            {
                ADDT06 oModel = db.ADDT06.Where(a => a.APPLY_NO == item.APPLY_NO).FirstOrDefault();
                // 推薦給點
                if (item.SHARE_YN == "Y")
                {
                    seatstr +=   oModel.SEAT_NO + ",";
                    oModel.SHARE_YN = item.SHARE_YN;
               
                    CashHelper.AddCash(user, ChangeShare, oModel.SCHOOL_NO,
                                            oModel.USER_NO, "ADDI02", oModel.APPLY_NO.ToString(), "閱讀認證-推薦", true, ref db, "", "", ref valuesList);
                    db.SaveChanges();
                }
            }
            TempData["StatusMessage"] = seatstr + "設定推薦完成";
            return RedirectToAction("UPLOAD");
        }

        ////取得小小讀書人個人閱讀成果
        //public ActionResult GetADDT06Data(string sidx, string sord, int page, int rows)
        //{
        //    UserProfile user = UserProfileHelper.Get();

        //    TempData["sidx"] = sidx;
        //    TempData["sord"] = sord;
        //    TempData["page"] = page;
        //    TempData["rows"] = rows;

        //    string strJoinTable = " ADDT06 WHERE SCHOOL_NO='" + user.SCHOOL_NO + "' AND USER_NO='" + user.USER_NO + "'";
        //    return Content(JsonHelper.JsonForJqgrid(new ADDT06Service().USP_ADDT06VIEWMODEL_QUERY(sidx, sord, page, rows, user.SCHOOL_NO, user.USER_NO), rows, new CommService().GetGirdTotalCount(strJoinTable), page), "application/json");
        //}

        [CheckPermission(CheckACTION_ID = "UPLOAD")] //檢查權限
        public ActionResult UPLOAD()
        {
            ViewBag.BookID = Request["BookID"];
            ViewBag.BookStatus = Request["BookStatus"];
            UserProfile user = UserProfileHelper.Get();
            List<ADDT03> liADDT03 = new List<ADDT03>();
            try
            {
                if (TempData["StatusMessage"] != null)
                {
                    TempData["StatusMessage"] = TempData["StatusMessage"];
                }
                liADDT03 = db.ADDT03.Where(a => a.SCHOOL_NO == user.SCHOOL_NO).OrderBy(a => a.BOOK_ID).ToList();
            }
            catch (Exception e)
            {
                throw;
            }

            //是否為導師
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassNoItem = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassNoItem = ClassList;
            }

            return View(liADDT03);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "UPLOAD")] //檢查權限
        public ActionResult SetUpload(HttpPostedFileBase file)
        {
            string Message = "";
            List<int> AccessList = new List<int>();
            List<ADDT06> lsA06 = new List<ADDT06>();
            Dictionary<string, string> UpadteListStatus = new Dictionary<string, string>();
            UserProfile user = UserProfileHelper.Get();
            string radFileType = (Request["radFileType"] != null) ? Request["radFileType"] : string.Empty;
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            string ddlReadrppBook = (Request["ddlReadrppBook"] != null) ? Request["ddlReadrppBook"].ToString() : string.Empty;
            string BOOK_ID = (Request["BOOK_ID"] != null) ? Request["BOOK_ID"].ToString() : string.Empty;
            string stdUserNo = string.Empty;
            string strMsg = string.Empty;
            string ErrMsg = string.Empty;

            //是否為導師
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassNoItem = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassNoItem = ClassList;
            }
            string BookName = string.Empty;
            if (ddlReadrppBook == "Y")
            {
                BookName = Request["BOOK_NAME"];
                if (string.IsNullOrEmpty(BOOK_ID) == false && string.IsNullOrEmpty(BookName) == false)
                {
                    BookName = BookName.Replace(BOOK_ID, "").Trim();
                }
            }
            else
            {
                BookName = Request["txtBOOK_NAME"];
            }
        

            if (file.ContentLength > 0)
            {
                var fileName = Path.GetFileName(file.FileName);
                //var path = Path.Combine(Server.MapPath("~/FileUploads"), fileName);
                if (file.ContentLength / 1024 > (1024 * 100)) // 5MB
                {
                    List<ADDT03> liADDT03 = new List<ADDT03>();
                    liADDT03 = db.ADDT03.Where(a => a.SCHOOL_NO == user.SCHOOL_NO).OrderBy(a => a.BOOK_ID).ToList();
                    TempData["StatusMessage"] = "上傳檔案不能超過100MB";
                    return View("UPLOAD", liADDT03);
                }
                //建立上傳暫存路徑
                string ZipPath = @"\TMPZip\" + user.SCHOOL_NO.ToString() + @"\" + DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + @"\";
                var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ZipPath;
                CreateDirectory(tempPath);
                file.SaveAs(tempPath + fileName);

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                try
                {
                    if (file.ContentType == "image/jpeg" || file.ContentType == "image/png")
                    {
                        #region"JPG上傳"

                        stdUserNo = fileName.Split('.')[0];

                        //若是選擇上傳座號
                        if (radFileType != string.Empty && radFileType == "S")
                        {
                            List<HRMT01> lth01 = db.HRMT01.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.CLASS_NO == Class_No && p.SEAT_NO == stdUserNo &&
                                    p.USER_STATUS != UserStaus.Invalid && p.USER_TYPE == "S").ToList();
                            stdUserNo = (lth01.Count > 0) ? lth01.FirstOrDefault().USER_NO : "0";
                        }

                        string stdFileUserNo = Path.GetFileName(fileName);
                        UpadteListStatus.Add(fileName, string.Empty);
                        //並確認學生清單
                        HRMT01 H01 = db.HRMT01.Where(p => p.USER_NO == stdUserNo && p.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                        if (H01 != null)
                        {
                            string StatusMemo = string.Empty;
                            if (new ADDTController().isReadBooK(H01.SCHOOL_NO, H01.USER_NO, BookName, ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_UPLOAD, out StatusMemo) == true)
                            {
                                //書有讀過則改上傳失敗
                                UpadteListStatus[stdFileUserNo] = "重覆申請" + StatusMemo;
                            }
                            else
                            {
                                ADDT06 ADDTModel = new ADDT06();

                                ADDTModel.SCHOOL_NO = H01.SCHOOL_NO;
                                ADDTModel.USER_NO = H01.USER_NO;
                                ADDTModel.CLASS_NO = H01.CLASS_NO;
                                ADDTModel.SYEAR = (byte)SYear;
                                ADDTModel.SEMESTER = (byte)Semesters;
                                ADDTModel.SEAT_NO = H01.SEAT_NO;
                                ADDTModel.NAME = H01.NAME;
                                ADDTModel.SNAME = H01.SNAME;
                                ADDTModel.PASSPORT_YN = ddlReadrppBook;
                                ADDTModel.BOOK_ID = (ddlReadrppBook == "Y") ? Request["BOOK_ID"].ToString() : null;
                                ADDTModel.BOOK_NAME = BookName;
                                ADDTModel.IMG_FILE = tempPath + fileName;
                                ADDTModel.APPLY_TYPE = (byte)ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_UPLOAD;
                                ADDTModel.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify;
                                ADDTModel.CRE_DATE = DateTime.Now;

                                //書名一樣就自動當作是Passport
                                if (ADDTModel.PASSPORT_YN != "Y")
                                {
                                    ADDT03 PassItem =
                                        db.ADDT03.Where(a => a.SCHOOL_NO == ADDTModel.SCHOOL_NO && a.BOOK_NAME == ADDTModel.BOOK_NAME).FirstOrDefault();
                                    if (PassItem != null)
                                    {
                                        ADDTModel.PASSPORT_YN = "Y";
                                        ADDTModel.BOOK_ID = PassItem.BOOK_ID;
                                    }
                                }
                                db.ADDT06.Add(ADDTModel);

                                try
                                {
                                    db.SaveChanges();
                                }
                                catch (Exception e)
                                {
                                    UpadteListStatus[stdFileUserNo] = e.Message;
                                    strMsg = "新增失敗";
                                }

                                db.Entry(ADDTModel).GetDatabaseValues();

                                int APPLY_NO = ADDTModel.APPLY_NO;

                                try
                                {
                                    //處理上傳檔案
                                    bool ans = doNewImage(ADDTModel, stdFileUserNo, out ErrMsg);
                                    if (ans)
                                    {
                                        try
                                        {
                                            db.SaveChanges();
                                        }
                                        catch (Exception e)
                                        {
                                            DelAPPLY_NO(APPLY_NO);
                                            UpadteListStatus[stdFileUserNo] = e.Message;
                                            strMsg = "更新上傳檔案失敗";
                                        }

                                        ADDTService.CheckPendingDetailEDIT("2", APPLY_NO, "", "n", user);
                                        AccessList.Add(APPLY_NO);
                                        UpadteListStatus[stdFileUserNo] = string.Empty;
                                    }
                                    else
                                    {
                                        DelAPPLY_NO(APPLY_NO);
                                        UpadteListStatus[stdFileUserNo] = ErrMsg;
                                        strMsg = "上傳檔案失敗";
                                    }
                                }
                                catch (Exception e)
                                {
                                    DelAPPLY_NO(APPLY_NO);
                                    UpadteListStatus[stdFileUserNo] = e.Message;
                                    strMsg = "更新狀態失敗";
                                }
                            }
                        }
                        else
                        {
                            if (Class_No == string.Empty)
                            {
                                UpadteListStatus[stdFileUserNo] = "請確認「學號」是否正確";
                            }
                            else
                            {
                                UpadteListStatus[stdFileUserNo] = "請確認「座號」是否正確";
                            }
                        }

                        #endregion
                    }
                    else if (file.ContentType == "application/x-zip-compressed" || file.ContentType == "application/octet-stream" || file.ContentType == "application/zip")
                    {
                        #region"ZIP解壓縮"

                        //UnZipFiles(tempPath + fileName, string.Empty);
                        UpadteListStatus = UnZipFilesRetrunList(tempPath + fileName, string.Empty);
                        ArrayList alReadBook = GetFiles(tempPath + fileName.Split('.')[0] + "\\");// + fileName.Split('.')[0]);

                        for (int i = 0; i < alReadBook.Count; i++)
                        {
                            ADDT06 ADDTModel = new ADDT06();
                            string strIMG_FILE = string.Empty;
                            ErrMsg = string.Empty;

                            //由上傳的檔案取出學生資訊
                            string stdFileUserNo = Path.GetFileName(alReadBook[i].ToString());
                            stdUserNo = stdFileUserNo.Split('.')[0];
                            string[] keys = UpadteListStatus.Keys.ToArray<string>();

                            //若是選擇上傳座號
                            if (radFileType != string.Empty && radFileType == "S")
                            {
                                List<HRMT01> lth01 = db.HRMT01.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.CLASS_NO == Class_No && p.SEAT_NO == stdUserNo &&
                                    p.USER_STATUS != UserStaus.Invalid && p.USER_TYPE == "S").ToList();
                                stdUserNo = (lth01.Count > 0) ? lth01.FirstOrDefault().USER_NO : "0";
                            }

                            try
                            {
                                //並確認學生清單
                                HRMT01 H01 = db.HRMT01.Where(p => p.USER_NO == stdUserNo && p.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();

                                if (H01 != null)
                                {
                                    string StatusMemo = string.Empty;
                                    string BookNameItem = "";
                                    if (new ADDTController().isReadBooK(H01.SCHOOL_NO, H01.USER_NO, BookName, ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_UPLOAD, out StatusMemo) == true)
                                    {
                                        //書有讀過則改上傳失敗
                                        UpadteListStatus[stdFileUserNo] = "重覆申請" + StatusMemo + "書名改成" + BookName + DateTime.Now.ToString("yyyyMMdd");
                                        BookNameItem = BookName + DateTime.Now.ToString("yyyyMMdd");
                                    }
                                    else
                                    {
                                        BookNameItem = BookName;
                                    }
                                    #region"寫入資料"

                                    ADDTModel.SCHOOL_NO = H01.SCHOOL_NO;
                                    ADDTModel.USER_NO = H01.USER_NO;
                                    ADDTModel.CLASS_NO = H01.CLASS_NO;
                                    ADDTModel.SYEAR = (byte)SYear;
                                    ADDTModel.SEMESTER = (byte)Semesters;
                                    ADDTModel.SEAT_NO = H01.SEAT_NO;
                                    ADDTModel.NAME = H01.NAME;
                                    ADDTModel.SNAME = H01.SNAME;
                                    ADDTModel.PASSPORT_YN = ddlReadrppBook;
                                    ADDTModel.BOOK_ID = (ddlReadrppBook == "Y") ? Request["BOOK_ID"].ToString() : null;
                                    ADDTModel.BOOK_NAME = BookNameItem;
                                    ADDTModel.IMG_FILE = alReadBook[i].ToString();
                                    ADDTModel.APPLY_TYPE = (byte)ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Batch_UPLOAD;
                                    ADDTModel.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify;
                                    ADDTModel.CRE_DATE = DateTime.Now;
                                    //書名一樣就自動當作是Passport
                                    if (ADDTModel.PASSPORT_YN != "Y")
                                    {
                                        ADDT03 PassItem =
                                            db.ADDT03.Where(a => a.SCHOOL_NO == ADDTModel.SCHOOL_NO && a.BOOK_NAME == ADDTModel.BOOK_NAME).FirstOrDefault();
                                        if (PassItem != null)
                                        {
                                            ADDTModel.PASSPORT_YN = "Y";
                                            ADDTModel.BOOK_ID = PassItem.BOOK_ID;
                                        }
                                    }
                                    db.ADDT06.Add(ADDTModel);

                                    try
                                    {
                                        db.SaveChanges();
                                    }
                                    catch (Exception e)
                                    {
                                        UpadteListStatus[stdFileUserNo] = e.Message;
                                        strMsg = "新增失敗";
                                    }

                                    db.Entry(ADDTModel).GetDatabaseValues();

                                    int APPLY_NO = ADDTModel.APPLY_NO;

                                    try
                                    {
                                        //處理上傳檔案
                                        bool ans = doNewImage(ADDTModel, stdFileUserNo, out ErrMsg);
                                        if (ans)
                                        {
                                            try
                                            {
                                                db.SaveChanges();
                                            }
                                            catch (Exception e)
                                            {
                                                DelAPPLY_NO(APPLY_NO);
                                                UpadteListStatus[stdFileUserNo] = e.Message;
                                                strMsg = "更新上傳檔案失敗";
                                            }

                                            ADDTService.CheckPendingDetailEDIT("2", APPLY_NO, "", "n", user);
                                            AccessList.Add(APPLY_NO);
                                            UpadteListStatus[stdFileUserNo] = string.Empty;
                                        }
                                        else
                                        {
                                            DelAPPLY_NO(APPLY_NO);
                                            UpadteListStatus[stdFileUserNo] = ErrMsg;
                                            strMsg = "上傳檔案失敗";
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        DelAPPLY_NO(APPLY_NO);
                                        UpadteListStatus[stdFileUserNo] = e.Message;
                                        strMsg = "更新狀態失敗";
                                    }

                                    #endregion
                                    //}
                                }
                                else
                                {
                                    //若有問題的則改變上傳檔案的狀態
                                    if (Class_No == string.Empty)
                                    {
                                        UpadteListStatus[stdFileUserNo] = "請確認「學號」是否正確";
                                    }
                                    else
                                    {
                                        UpadteListStatus[stdFileUserNo] = "請確認「座號」是否正確";
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                //this.HandleMessage(e.ToString());
                                //若有問題的則改變上傳檔案的狀態
                                UpadteListStatus[stdFileUserNo] = e.Message;
                                strMsg = "新增失敗請洽管理員";
                            }
                            finally
                            {
                                if (string.IsNullOrWhiteSpace((string)ViewData["ViewMessage"]))
                                {
                                    ViewData["ViewMessage"] = strMsg;
                                }
                            }
                        }

                        #endregion
                    }
                    else
                    {
                        UpadteListStatus.Add(fileName, "非有效上傳格式");
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                finally
                {
                    if (Directory.Exists(tempPath) == true)
                    {
                        //刪除暫存路徑目錄，只會刪除最後一層目錄
                        Directory.Delete(tempPath, true);
                    }
                }
            }

            if (AccessList.Count > 0)
            {
                lsA06 = db.ADDT06.Where(p => AccessList.Contains(p.APPLY_NO)).ToList();
            }
            ViewBag.SumCount = UpadteListStatus.Count;
            Dictionary<string, string> FailList = UpadteListStatus.Where(a => a.Value != null && a.Value != "").ToDictionary(t => t.Key, t => t.Value);

            ViewBag.FailList = FailList;
            ViewBag.ErrCount = FailList.ToList().Count;

            return View("QUERY2", lsA06);
            //return View("../ADDT/ADDTList");
        }

        //解壓縮檔案
        //path: 解壓縮檔案目錄路徑
        //password: 密碼
        public Dictionary<string, string> UnZipFilesRetrunList(string path, string password)
        {
            Dictionary<string, string> dicZipList = new Dictionary<string, string>();
            ReadOptions options = new ReadOptions();
            options.Encoding = Encoding.Default;
            ZipFile unzip = ZipFile.Read(path, options);

            try
            {
                if (password != null && password != string.Empty) unzip.Password = password;
                string unZipPath = path.Replace(".zip", "");
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

                foreach (ZipEntry e in unzip)
                {
                    string[] GetFileNameArr = e.FileName.Split('/');
                    if (GetFileNameArr[GetFileNameArr.Length - 1] != string.Empty)
                    {
                        if (regexCode.IsMatch(GetFileNameArr[GetFileNameArr.Length - 1].Split('.')[1].ToLower()) == false)
                        {
                            dicZipList.Add(GetFileNameArr[GetFileNameArr.Length - 1], string.Empty);
                            e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                        }
                        else
                        {
                            dicZipList.Add(GetFileNameArr[GetFileNameArr.Length - 1], "非有效圖檔格式");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                unzip.Dispose();
            }

            return dicZipList;
        }

        //重新存圖到ADDT06
        public bool doNewImage(ADDT06 aDDT06, string stdFileUserNo, out string Msg)
        {
            if (stdFileUserNo == string.Empty)
            {
                Msg = "stdFileUserNo is string.Empty";
                return false;
            }

            Bitmap imageOutput = null;
            System.Drawing.Image image = System.Drawing.Image.FromFile(Path.Combine(aDDT06.IMG_FILE, aDDT06.IMG_FILE));

            try
            {
                //a.組檔案名稱
                aDDT06.IMG_FILE = aDDT06.APPLY_NO.ToString() + "_" + stdFileUserNo;
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                if (regexCode.IsMatch(aDDT06.IMG_FILE.ToLower()) == false)
                {
                    Msg = "非有效圖片格式";
                    return false;
                }

                //b.組上傳資料夾路徑
                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
                string imgPath = string.Format(@"{0}ADDT06IMG\{1}\", Request.MapPath(UploadImageRoot), aDDT06.SCHOOL_NO.ToString());
                if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);
                //c.縮圖
                //File file = new File();

                double FixWidth = 1000;
                double FixHeight = 1400;
                double rate = 1;
                if (image.Width > FixWidth || image.Height > FixHeight)
                {
                    if (image.Width > FixWidth) rate = FixWidth / image.Width;
                    else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;
                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    imageOutput = new Bitmap(image, w, h);
                    imageOutput.Save(Path.Combine(imgPath, aDDT06.IMG_FILE), image.RawFormat);
                }
                else
                {
                    //直接儲存
                    imageOutput = new Bitmap(image);
                    imageOutput.Save(Path.Combine(imgPath, aDDT06.IMG_FILE), image.RawFormat);
                }

                Msg = "";
                return true;
            }
            catch (Exception ex)
            {
                Msg = "圖片處理失敗;" + ex;
                return false;
            }
            finally
            {
                imageOutput.Dispose();
                image.Dispose();
            }
        }

        //讀取目錄下所有檔案
        private static ArrayList GetFiles(string path)
        {
            ArrayList files = new ArrayList();

            if (Directory.Exists(path))
            {
                files.AddRange(Directory.GetFiles(path, "*", SearchOption.AllDirectories));
            }

            return files;
        }

        //建立目錄
        private static void CreateDirectory(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        private void DelAPPLY_NO(int APPLY_NO)
        {
            var T06 = db.ADDT06.Where(a => a.APPLY_NO == APPLY_NO).FirstOrDefault();
            if (T06 != null)
            {
                db.ADDT06.Remove(T06);
                db.SaveChanges();
            }
        }

        //壓縮檔案
        //path: 壓縮檔案路徑
        //password: 密碼
        //comment: 註解
        private void ZipFiles(string path, string password, string comment)
        {
            string zipPath = path + @"\" + Path.GetFileName(path) + ".zip";
            ZipFile zip = new ZipFile();
            if (password != null && password != string.Empty) zip.Password = password;
            if (comment != null && comment != "") zip.Comment = comment;
            ArrayList files = GetFiles(path);
            foreach (string f in files)
            {
                zip.AddFile(f, string.Empty);//第二個參數設為空值表示壓縮檔案時不將檔案路徑加入
            }
            zip.Save(zipPath);
        }

        //解壓縮檔案
        //path: 解壓縮檔案目錄路徑
        //password: 密碼
        private void UnZipFiles(string path, string password)
        {
            ZipFile unzip = ZipFile.Read(path);
            if (password != null && password != string.Empty) unzip.Password = password;
            string unZipPath = path.Replace(".zip", "");

            foreach (ZipEntry e in unzip)
            {
                e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
            }
            unzip.Dispose();
        }
    }
}