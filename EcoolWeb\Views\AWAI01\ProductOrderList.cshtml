﻿@model AWAI01OrderListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")



@using (Html.BeginForm("ProductOrderList", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_ProductOrderListPartial", (string)ViewBag.BRE_NO)
    </div>
}

<div style="text-align:center;">
    <button type="button" class="btn btn-default" onclick="onBack()">
        返回
    </button>
    <button type="button" class="btn btn-default" onclick="ToExcel()">匯出excel</button>

</div>


@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';
       
    




        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
                     FunPageProc(1);
                 }

    function onBack() {
        if (@Model!= null && @Model.Search!= null) {
            $(targetFormID).attr("action", "@Url.Action("AWAI01", (string) ViewBag.BRE_NO)".attr('target', '_self'));
            $(targetFormID).submit();
        }
                 }

                 function ToExcel() {
            $(targetFormID).attr('action', '@Url.Action("ProductOrderListPrintExcel", (string)ViewBag.BRE_NO)').attr('target', '_blank');
            $(targetFormID).submit();
                 };



             function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                     var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                     if (InPreadonly == false || InPreadonly == undefined) {

                         if (type == 'radio' || type == 'checkbox') {
                             if ($(this).attr("title") == 'Default') {
                                 this.checked = true;
                                 }
                        else {
                                     this.checked = false;
                                     }
                                 }
                    else if (tag == 'select') { //下拉式選單
                                     this.selectedIndex = 0;
                                 }
                                 else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                                     this.value = '';
                                 }
                             }
                         });

                         FunPageProc(1);
                     }
        

    </script>
}




