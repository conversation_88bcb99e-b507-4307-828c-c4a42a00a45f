﻿using com.ecool.sqlConnection;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.service
{
    public class NoticeApiService
    {

        public string ErrorMsg;
        private int ErrorInt = 0;

        //#region Del_YN =>Y
        //public void DelAllDate (string SCHOOL_NO, string USER_NO)
        //{
        //    using (SqlConnection conn = new sqlConnection().getConnection4Query())
        //    {
        //        SqlCommand cmd = conn.CreateCommand();
        //        SqlTransaction transaction = conn.BeginTransaction();
        //        cmd.Transaction = transaction;
        //        try
        //        {

        //            this.UpdateDEL_YN(conn, transaction, SCHOOL_NO, USER_NO);

        //            if (ErrorInt == 0)
        //            {
        //                transaction.Commit();
        //            }
        //            else
        //            {
        //                transaction.Rollback();
        //                ErrorMsg = "刪除資料處理;\r\n" + ErrorMsg;
        //            }
        //        }
        //        catch (Exception ex)
        //        {
        //            transaction.Rollback();
        //            ErrorMsg = "刪除資料處理;\r\n" + ex.Message;
        //        }
        //        finally
        //        {
        //            if (cmd != null)
        //            {
        //                cmd.Dispose();
        //            }

        //            if (conn != null)
        //            {
        //                conn.Close();
        //                conn.Dispose();
        //            }
        //        }
        //    }
        //}
        //#endregion

        //#region Del_YN =>Y
        //private void UpdateDEL_YN(SqlConnection conn, SqlTransaction transaction, string SCHOOL_NO,string USER_NO)
        //{
        //    IDbCommand cmd = new SqlCommand(@" Update APPT02 set DEL_YN = 'Y' Where SCHOOL_NO=@SCHOOL_NO AND  USER_NO=@USER_NO AND a.STATUS !=@Status
        //                AND a.DEL_YN = 'N' ");
        //    cmd.Connection = conn;
        //    cmd.Transaction = transaction;

        //    cmd.Parameters.Add(
        //    (SCHOOL_NO == null)
        //    ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
        //    : new SqlParameter("@SCHOOL_NO", SCHOOL_NO));


        //    cmd.Parameters.Add(
        //    (USER_NO == null)
        //    ? new SqlParameter("@USER_NO", DBNull.Value)
        //    : new SqlParameter("@USER_NO", USER_NO));

        //    string Status = APPT02.StatusVal.Cre;
        //    cmd.Parameters.Add(
        //    (Status == null)
        //    ? new SqlParameter("@Status", DBNull.Value)
        //    : new SqlParameter("@Status", Status));

        //    try
        //    {
        //        cmd.ExecuteNonQuery();
        //    }
        //    catch (Exception ex)
        //    {
        //        ErrorInt = ErrorInt + 1;
        //        ErrorMsg = ErrorMsg + "Update_APPT02 失敗;\r\n" + ex.Message;
        //    }
        //    finally
        //    {
        //        cmd.Dispose();
        //    }
        //}
        //#endregion

    }
}
