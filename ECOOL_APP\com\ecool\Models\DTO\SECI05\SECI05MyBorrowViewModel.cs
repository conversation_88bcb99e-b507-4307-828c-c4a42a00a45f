﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class SECI05MyBorrowViewModel
    {
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///身份証
        /// </summary>
        [DisplayName("身份証")]
        public string IDNO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        public string PHOTO { get; set; }

        /// <summary>
        /// 在校借書量
        /// </summary>
        [DisplayName("在校借書量")]
        public int ALL_QTY { get; set; }

        /// <summary>
        /// 本學期借書量
        /// </summary>
        [DisplayName("本學期借書量")]
        public int THIS_SESEM_QTY { get; set; }

        /// <summary>
        /// 總借書量
        /// </summary>
        [DisplayName("在校總借書量")]
        public int SumQTY { get; set; }

        /// <summary>
        /// 本月份借書量
        /// </summary>
        [DisplayName("本月份借書量")]
        public int THIS_MM_QTY { get; set; }

        /// <summary>
        /// 本學期最後借書日期
        /// </summary>
        [DisplayName("本學期最後借書日期")]
        public DateTime? LAST_BORROW_DATE { get; set; }

        [DisplayName("還書日期")]
        public DateTime? DATE_RET { get; set; }

        [DisplayName("書本名稱")]
        public string BKNAME { get; set; }

        /// <summary>
        /// 逾期 未借/還書 月份
        /// </summary>
        [DisplayName("逾期(月)")]
        public int? DiffMonth { get; set; }

        /// <summary>
        /// 角色娃娃 圖 路徑
        /// </summary>
        public string PlayerUrl { get; set; }

        public int QTY_GRADE_1 { get; set; }
        public int QTY_GRADE_2 { get; set; }
        public int QTY_GRADE_3 { get; set; }
        public int QTY_GRADE_4 { get; set; }
        public int QTY_GRADE_5 { get; set; }
        public int QTY_GRADE_6 { get; set; }
    }
}