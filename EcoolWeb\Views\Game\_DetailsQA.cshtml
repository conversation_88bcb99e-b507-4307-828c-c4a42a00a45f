﻿@model GameEditDetailsQAViewModel

@using (Html.BeginCollectionItem("DetailsQA", Model.IsCopy))
{
    var index = Html.GetIndex("DetailsQA");

    <div id="@index">
        <div class="panel panel-info">
            <div class="panel-heading">
                @if (Model.InputType == "OX")
                {
                    <strong style="font-size:20px;">是非題</strong>
                }
                else
                {
                    <strong style="font-size:20px;">
                        選擇題
                    </strong>
                }
            </div>
            <div class="panel-heading">
                <div class="form-group" style="padding-top:25px">
                    <label class="col-md-2 control-label">
                        題目
                    </label>
                    <div class="col-md-10">
                        @Html.HiddenFor(m => m.GROUP_ID)
                        @Html.HiddenFor(m => m.G_ORDER_BY)
                        @Html.TextAreaFor(m => m.G_SUBJECT, new { @class = "form-control", @rows = "2" })
                        @Html.ValidationMessageFor(m => m.G_SUBJECT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group" style="padding-top:45px">
                    <label class="col-md-2 control-label">可重複闖關</label>
                    <div class="col-md-10">
                        <label class='checkbox-inline'>
                            @Html.CheckBoxFor(m => m.Y_REPEAT)
                            <label>是</label>
                        </label>
                        @Html.ValidationMessageFor(m => m.Y_REPEAT, "", new { @class = "text-danger" })
                        <label class="text-info">
                            &nbsp;&nbsp; (PS:建議這裡打勾，則可以反悔作答，或挑戰第二次)
                        </label>
                    </div>
                </div>
                <div class="form-group" style="padding-top:25px">
                    <label class="col-md-2 control-label">過場動畫秒數(秒)</label>
                    <div class="col-md-10">
                        @Html.EditorFor(m => m.LOADING_TIME, new { htmlAttributes = new { @class = "form-control", @placeholder = "未填預設1秒" } })
                        @Html.ValidationMessageFor(m => m.LOADING_TIME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group" style="padding-top:25px">
                    <label class="col-md-2 control-label">結果停留時間(秒)</label>
                    <div class="col-md-10">
                        @Html.EditorFor(m => m.PASSED_TIME, new { htmlAttributes = new { @class = "form-control", @placeholder = "未填預設3秒" } })
                        @Html.ValidationMessageFor(m => m.PASSED_TIME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div style="height:25px"></div>
                <div class="panel-body">

                    <div class="form-group">

                        <div class="table-responsive">
                            <div class="css-table" style="width:92%;">
                                <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                                    <div class="th" style="text-align:center;width:50px">
                                        正確
                                    </div>
                                    <div class="th" style="text-align:center">
                                        選項內容
                                    </div>
                                    <div class="th" style="text-align:center">
                                        酷幣點數
                                    </div>
                                    <div class="th" style="text-align:center">
                                        選項內容圖片(未上傳，則自動使用系統預設圖片)
                                    </div>
                                    <div class="th" style="text-align:center">
                                        回饋字串
                                    </div>
                                </div>
                                <div id="editorRows" class="tbody">
                                    @if (Model.AnsLeve != null && Model.AnsLeve?.Count() > 0)
                                    {
                                        foreach (var D_item in Model.AnsLeve)
                                        {
                                            D_item.Index = index;

                                            @Html.Partial("_AnsLeve", D_item)
                                        }
                                    }
                                    else
                                    {
                                        Model.AnsLeve = ECOOL_APP.GameService.GetAddAns(index, Model.InputType);

                                        foreach (var D_item in Model.AnsLeve)
                                        {
                                            @Html.Partial("_AnsLeve", D_item)
                                        }
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-footer">
                <div class="form-group text-right">
                    <a role="button" class="btn btn-danger btn-sm glyphicon glyphicon-trash" onclick="deleteRow('@index')" title="刪除"></a>
                    &nbsp;&nbsp;&nbsp;
                </div>
            </div>
        </div>
    </div>

}