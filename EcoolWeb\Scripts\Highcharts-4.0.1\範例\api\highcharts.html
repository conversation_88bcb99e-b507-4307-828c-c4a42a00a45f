<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, minimum-scale=1, maximum-scale=1">
<title>Highcharts API Reference</title>
<link href="css/source-sans-pro.css" rel="stylesheet" type="text/css">
<link href="css/font-awesome.min.css" rel="stylesheet">
<link rel="stylesheet" href="css/jquery-ui.min.css" />
<link rel="apple-touch-icon" sizes="57x57" href="images/apple-touch-icon-57x57.png">
<link rel="apple-touch-icon" sizes="114x114" href="images/apple-touch-icon-114x114.png">
<link rel="apple-touch-icon" sizes="72x72" href="images/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="144x144" href="images/apple-touch-icon-144x144.png">
<link rel="apple-touch-icon" sizes="60x60" href="images/apple-touch-icon-60x60.png">
<link rel="apple-touch-icon" sizes="120x120" href="images/apple-touch-icon-120x120.png">
<link rel="apple-touch-icon" sizes="76x76" href="images/apple-touch-icon-76x76.png">
<link rel="apple-touch-icon" sizes="152x152" href="images/apple-touch-icon-152x152.png">
<link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon-180x180.png">
<link rel="icon" type="image/png" href="images/favicon-192x192.png" sizes="192x192">
<link rel="icon" type="image/png" href="images/favicon-160x160.png" sizes="160x160">
<link rel="icon" type="image/png" href="images/favicon-96x96.png" sizes="96x96">
<link rel="icon" type="image/png" href="images/favicon-16x16.png" sizes="16x16">
<link rel="icon" type="image/png" href="images/favicon-32x32.png" sizes="32x32">
<meta name="msapplication-TileColor" content="#2b5797">
<meta name="msapplication-TileImage" content="/mstile-144x144.png">
<link href="css/ref.css" rel="stylesheet"
	type="text/css" />
<script src="js/jquery-1.11.3.min.js"></script>
<script src="js/jquery-ui.min.js"></script>
<script type="text/javascript">
	var PRODUCTNAME = 'Highcharts'.toLowerCase(),
		runDB = false;
</script>
<script src="js/ref.js"
	type="text/javascript"></script>
</head>
<body>
	<div id="top">
		<div class="container">
			<div class="cell">
				<a href="http://www.highcharts.com/products/highcharts" title="Highcharts Home Page" id="logo"><img
					alt="Highcharts Home Page"
					src="images/Highcharts.svg"
					border="0"></a>
			</div>
			<div class="cell" style="text-align: center;">
				<h1>Options Reference v.4.2.0</h1>
			</div>
			<div class="cell hidden-offline">See also options for <a href="/highstock">Highstock</a>, <a href="/highmaps">Highmaps</a>
			</div>
			<div class="clearfix"></div>
		</div>
	</div>
	<div id="wrapper">
		<div class="container">
			<div id="wrapper-inner">
				<div id="nav-wrap">
					<div id="nav">						
						<div class="nav-section first">
							<div class="ui-widget" id="search-div">
								<div id="search-wrap">
									<i class="fa fa-search"></i><input id="search"/>
								</div>
							</div>
							<div class="nav-section-inner">
								<h3 id="options-header">Configuration options</h3>
								<p>For initial declarative chart setup. View as <a class="dump" href="highcharts/option/dump.json">JSON</a>.</p>
								<div id="global-options-tree">
									<code>Highcharts.setOptions({</code>
									<div id="global-options" class="tree"></div>
									<code>});</code>
								</div>
								<div id="options-tree">
									<code>$("#container").highcharts({</code>
									<div id="options" class="tree"></div>
									<code>});</code>
								</div>
							</div>
						</div>

						<div class="nav-section">
							<div class="nav-section-inner">
								<h3>Methods and properties</h3>

								<p>For dynamically modifying the chart. View as <a class="dump" href="highcharts/object/dump.json">JSON</a>. </p>
								<div id="methods-and-properties-toc"></div>

								<div id="objects-tree">
									<div id="objects" class="tree"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div id="details-wrap">
					<div id="details">
					   <div id="splashText" class="section">
						   <img src="images/splash.svg" />
					   </div>
				   </div>
				</div>
				<div class="clearfix"></div>
			</div>
		</div>
	</div>
	<div id="footer">
		<div class="container">
			<div id="footer-copy">
				&copy;&nbsp;Highcharts 2013. All rights reserved.
			</div>
			<div id="footer-social">
				<a href="https://www.facebook.com/Highcharts" title="Facebook" class="social-icon"><i class="fa fa-facebook"></i></a>
				<a href="https://twitter.com/Highcharts" title="Twitter" class="social-icon"><i class="fa fa-twitter"></i></a>
				<a href="http://www.linkedin.com/company/highsoft-solutions-as" title="LinkedIn" class="social-icon"><i class="fa fa-linkedin"></i></a>
				<a href="https://github.com/highslide-software/highcharts.com" title="Github" class="social-icon"><i class="fa fa-github"></i></a>
			</div>
			<div class="clearfix"></div>
		</div>
	</div>
	<a id="scrollTop" href="#top" style="display: none;"><i class="fa fa-arrow-up"></i></a>
</body>
</html>
