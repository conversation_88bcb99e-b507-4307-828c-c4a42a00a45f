/*
 Highcharts JS v6.0.6 (2018-02-05)
 Drag-panes module

 (c) 2010-2017 Highsoft AS
 Author: <PERSON><PERSON><PERSON>j

 License: www.highcharts.com/license
*/
(function(f){"object"===typeof module&&module.exports?module.exports=f:f(Highcharts)})(function(f){(function(b){var f=b.hasTouch,v=b.merge,m=b.wrap,n=b.each,w=b.isNumber,k=b.addEvent,t=b.relativeLength,x=b.objectEach,p=b.Axis,u=b.Pointer;v(!0,p.prototype.defaultYAxisOptions,{minLength:"10%",maxLength:"100%",resize:{controlledAxis:{next:[],prev:[]},enabled:!1,x:0,y:0}});b.AxisResizer=function(a){this.init(a)};b.AxisResizer.prototype={init:function(a,c){this.axis=a;this.options=a.options.resize;this.render();
c||this.addMouseEvents()},render:function(){var a=this.axis,c=a.chart,d=this.options,b=d.x,e=d.y,d=Math.min(Math.max(a.top+a.height+e,c.plotTop),c.plotTop+c.plotHeight),l={};this.lastPos=d-e;this.controlLine||(this.controlLine=c.renderer.path().addClass("highcharts-axis-resizer"));this.controlLine.add(a.axisGroup);e=this.controlLine.strokeWidth();l.d=c.renderer.crispLine(["M",a.left+b,d,"L",a.left+a.width+b,d],e);this.controlLine.attr(l)},addMouseEvents:function(){var a=this,c=a.controlLine.element,
d=a.axis.chart.container,b=[],e,l,q;a.mouseMoveHandler=e=function(c){a.onMouseMove(c)};a.mouseUpHandler=l=function(c){a.onMouseUp(c)};a.mouseDownHandler=q=function(c){a.onMouseDown(c)};b.push(k(d,"mousemove",e),k(d.ownerDocument,"mouseup",l),k(c,"mousedown",q));f&&b.push(k(d,"touchmove",e),k(d.ownerDocument,"touchend",l),k(c,"touchstart",q));a.eventsToUnbind=b},onMouseMove:function(a){a.touches&&0===a.touches[0].pageX||!this.grabbed||(this.hasDragged=!0,this.updateAxes(this.axis.chart.pointer.normalize(a).chartY-
this.options.y))},onMouseUp:function(a){this.hasDragged&&this.updateAxes(this.axis.chart.pointer.normalize(a).chartY-this.options.y);this.grabbed=this.hasDragged=this.axis.chart.activeResizer=null},onMouseDown:function(){this.axis.chart.pointer.reset(!1,0);this.grabbed=this.axis.chart.activeResizer=!0},updateAxes:function(a){var c=this,d=c.axis.chart,f=c.options.controlledAxis,e=0===f.next.length?[b.inArray(c.axis,d.yAxis)+1]:f.next,f=[c.axis].concat(f.prev),l=[],q=!1,k=d.plotTop,m=d.plotHeight,p=
k+m,r;a=Math.max(Math.min(a,p),k);r=a-c.lastPos;1>r*r||(n([f,e],function(b,f){n(b,function(b,h){var g=(b=w(b)?d.yAxis[b]:f||h?d.get(b):b)&&b.options,e,n;g&&"navigator-y-axis"!==g.id&&(h=b.top,n=Math.round(t(g.minLength,m)),e=Math.round(t(g.maxLength,m)),f?(r=a-c.lastPos,g=Math.round(Math.min(Math.max(b.len-r,n),e)),h=b.top+r,h+g>p&&(e=p-g-h,a+=e,h+=e),h<k&&(h=k,h+g>p&&(g=m)),g===n&&(q=!0),l.push({axis:b,options:{top:Math.round(h),height:g}})):(g=Math.round(Math.min(Math.max(a-h,n),e)),g===e&&(q=!0),
a=h+g,l.push({axis:b,options:{height:g}})))})}),q||(n(l,function(a){a.axis.update(a.options,!1)}),d.redraw(!1)))},destroy:function(){var a=this;delete a.axis.resizer;this.eventsToUnbind&&n(this.eventsToUnbind,function(a){a()});a.controlLine.destroy();x(a,function(b,d){a[d]=null})}};p.prototype.keepProps.push("resizer");m(p.prototype,"render",function(a){a.apply(this,Array.prototype.slice.call(arguments,1));var c=this.resizer,d=this.options.resize;d&&(d=!1!==d.enabled,c?d?c.init(this,!0):c.destroy():
d&&(this.resizer=new b.AxisResizer(this)))});m(p.prototype,"destroy",function(a,b){!b&&this.resizer&&this.resizer.destroy();a.apply(this,Array.prototype.slice.call(arguments,1))});m(u.prototype,"runPointActions",function(a){this.chart.activeResizer||a.apply(this,Array.prototype.slice.call(arguments,1))});m(u.prototype,"drag",function(a){this.chart.activeResizer||a.apply(this,Array.prototype.slice.call(arguments,1))})})(f)});
