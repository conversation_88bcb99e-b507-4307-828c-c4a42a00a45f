﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.26430.12
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ECOOL_APP", "ECOOL_APP\ECOOL_APP.csproj", "{ABEA0C1B-6580-4D00-828F-1D5481A19DD5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EcoolWeb", "EcoolWeb\EcoolWeb.csproj", "{4899B3C8-EFA1-4D6B-95DB-C2F70E4EA197}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "ECOOL_DEV", "ECOOL_DEV\ECOOL_DEV.sqlproj", "{9A66B672-DB69-4196-AA45-1B65986187D4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Epub.Net", "Epub.Net\Epub.Net.csproj", "{0D33AADD-F636-4D69-AE42-E0898DF37CEB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".nuget", ".nuget", "{24DC558E-4FB1-42AC-AC6B-951EF2CE2B2C}"
	ProjectSection(SolutionItems) = preProject
		NuGet.config = NuGet.config
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8826F98C-4DD9-4873-B081-B3C868719A02}"
	ProjectSection(SolutionItems) = preProject
		.tfignore = .tfignore
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{F348B280-E816-4644-8810-2D57F4855BD3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EcoolUnitTest", "EcoolUnitTest\EcoolUnitTest.csproj", "{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		CHINA|Any CPU = CHINA|Any CPU
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{ABEA0C1B-6580-4D00-828F-1D5481A19DD5}.CHINA|Any CPU.ActiveCfg = CHINA|Any CPU
		{ABEA0C1B-6580-4D00-828F-1D5481A19DD5}.CHINA|Any CPU.Build.0 = CHINA|Any CPU
		{ABEA0C1B-6580-4D00-828F-1D5481A19DD5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABEA0C1B-6580-4D00-828F-1D5481A19DD5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABEA0C1B-6580-4D00-828F-1D5481A19DD5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABEA0C1B-6580-4D00-828F-1D5481A19DD5}.Release|Any CPU.Build.0 = Release|Any CPU
		{4899B3C8-EFA1-4D6B-95DB-C2F70E4EA197}.CHINA|Any CPU.ActiveCfg = Release|Any CPU
		{4899B3C8-EFA1-4D6B-95DB-C2F70E4EA197}.CHINA|Any CPU.Build.0 = Release|Any CPU
		{4899B3C8-EFA1-4D6B-95DB-C2F70E4EA197}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4899B3C8-EFA1-4D6B-95DB-C2F70E4EA197}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4899B3C8-EFA1-4D6B-95DB-C2F70E4EA197}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4899B3C8-EFA1-4D6B-95DB-C2F70E4EA197}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.CHINA|Any CPU.ActiveCfg = CHINA|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.CHINA|Any CPU.Build.0 = CHINA|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.CHINA|Any CPU.Deploy.0 = CHINA|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A66B672-DB69-4196-AA45-1B65986187D4}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{0D33AADD-F636-4D69-AE42-E0898DF37CEB}.CHINA|Any CPU.ActiveCfg = CHINA|Any CPU
		{0D33AADD-F636-4D69-AE42-E0898DF37CEB}.CHINA|Any CPU.Build.0 = CHINA|Any CPU
		{0D33AADD-F636-4D69-AE42-E0898DF37CEB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D33AADD-F636-4D69-AE42-E0898DF37CEB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D33AADD-F636-4D69-AE42-E0898DF37CEB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D33AADD-F636-4D69-AE42-E0898DF37CEB}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA}.CHINA|Any CPU.ActiveCfg = Release|Any CPU
		{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA}.CHINA|Any CPU.Build.0 = Release|Any CPU
		{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{6FDFA12E-D9EF-43BE-8E00-2B524DFC12DA} = {F348B280-E816-4644-8810-2D57F4855BD3}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {86BF933B-3EF7-4E2F-90CA-289E28101A7C}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 6
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://61.222.174.1:8080/tfs/cooc
		SccLocalPath0 = .
		SccProjectUniqueName1 = ECOOL_APP\\ECOOL_APP.csproj
		SccProjectName1 = ECOOL_APP
		SccLocalPath1 = ECOOL_APP
		SccProjectUniqueName2 = ECOOL_DEV\\ECOOL_DEV.sqlproj
		SccProjectName2 = ECOOL_DEV
		SccLocalPath2 = ECOOL_DEV
		SccProjectUniqueName3 = EcoolWeb\\EcoolWeb.csproj
		SccProjectName3 = EcoolWeb
		SccLocalPath3 = EcoolWeb
		SccProjectUniqueName4 = Epub.Net\\Epub.Net.csproj
		SccProjectName4 = Epub.Net
		SccLocalPath4 = Epub.Net
		SccProjectUniqueName5 = EcoolUnitTest\\EcoolUnitTest.csproj
		SccProjectTopLevelParentUniqueName5 = ECOOL.sln
		SccProjectName5 = EcoolUnitTest
		SccLocalPath5 = EcoolUnitTest
	EndGlobalSection
EndGlobal
