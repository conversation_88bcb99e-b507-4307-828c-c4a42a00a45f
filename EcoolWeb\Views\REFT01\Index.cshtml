﻿@model REFT01IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string style = "width:90%;margin: 0px auto;";
   
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<br />

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div style="@style">

    @using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1",id= "form1", enctype = "multipart/form-data" }))
    {
        @Html.AntiForgeryToken()


        <div id="QuerySelect">
            @Html.Action("_QuerySelect", (string)ViewBag.BRE_NO, new { REF_TABLE = Model.REF_TABLE, REF_KEY = Model.REF_KEY, STATUS = Model.STATUS })
        </div>

        <div id="QuerySelectDataList">
            @Html.Action("_QuerySelectDataList", (string)ViewBag.BRE_NO, new { REF_TABLE = Model.REF_TABLE, REF_KEY = Model.REF_KEY, STATUS = Model.STATUS })
        </div>
        <div class="text-center">
                <a id="OKbutton" onclick="OKbuttonFun()" role="button" class="btn btn-default">選取完成</a>
        </div>
    }

</div>
<script type="text/javascript">
  function OKbuttonFun()
    {
      var URL = '@Url.Action("Index", "REFT01")' +'?REF_TABLE=@Model.REF_TABLE&REF_KEY=@Model.REF_KEY&BTN_ID=@Model.BTN_ID&REF_KEY_ID=@Model.REF_KEY_ID'

        $('@Model.REF_KEY_ID', window.parent.document).attr( "value", "@Model.REF_KEY");
        $('@Model.BTN_ID', window.parent.document).attr("href", URL);


       // var originalClose = $.colorbox.close;
        //$.colorbox.remove();
        parent.$.fn.colorbox.close();//關閉視窗

       
    }
</script>
