﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ZZZI37IndexViewModel
    {
        public string SCHOOL_NO { get; set; }

        [DisplayName("國家")]
        public string CountryN<PERSON> { get; set; }

        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        [DisplayName("寄送地址")]
        public string ADDRESS { get; set; }

        [DisplayName("寄件人城市")]
        public string EngCity { get; set; }

        [DisplayName("寄件人地址")]
        public string EngADDRESS { get; set; }

        /// <summary>
        ///投稿日(起)
        /// </summary>
        [DisplayName("投稿日(起)")]
        public DateTime? CRE_DATEs { get; set; }

        /// <summary>
        ///投稿日(迄)
        /// </summary>
        [DisplayName("投稿日(迄)")]
        public DateTime? CRE_DATEe { get; set; }

        public List<ZZZI37IndexListViewModel> DataList { get; set; }
    }
}