﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;
using ECOOL_APP;
using ECOOL_APP.EF;

namespace EcoolWeb.Models
{
    public class DownloadManagerIndexViewModel
    {
        [DisplayName("文件名稱")]
        public string whereSearch { get; set; }

        public string DL_ID { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// 是否顯示作廢說明文件，Y=是
        /// </summary>
        public string ShowDisable { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        public string BackAction { get; set; }

        public string BackController { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public List<DLMT01> ListData;
        //public IPagedList<FAQT01> ListData;

        public DownloadManagerIndexViewModel()
        {
            PageSize =50;
        }
    }
}