﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'ka', {
	border: 'ჩარჩოს ზომა',
	caption: 'სათაური',
	cell: {
		menu: 'უჯრა',
		insertBefore: 'უჯრის ჩასმა მანამდე',
		insertAfter: 'უჯრის ჩასმა მერე',
		deleteCell: 'უჯრების წაშლა',
		merge: 'უჯრების შეერთება',
		mergeRight: 'შეერთება მარჯვენასთან',
		mergeDown: 'შეერთება ქვემოთასთან',
		splitHorizontal: 'გაყოფა ჰორიზონტალურად',
		splitVertical: 'გაყოფა ვერტიკალურად',
		title: 'უჯრის პარამეტრები',
		cellType: 'უჯრის ტიპი',
		rowSpan: 'სტრიქონების ოდენობა',
		colSpan: 'სვეტების ოდენობა',
		wordWrap: 'სტრიქონის გადატანა (Word Wrap)',
		hAlign: 'ჰორიზონტალური სწორება',
		vAlign: 'ვერტიკალური სწორება',
		alignBaseline: 'ძირითადი ხაზის გასწვრივ',
		bgColor: 'ფონის ფერი',
		borderColor: 'ჩარჩოს ფერი',
		data: 'მონაცემები',
		header: 'სათაური',
		yes: 'დიახ',
		no: 'არა',
		invalidWidth: 'უჯრის სიგანე რიცხვით უნდა იყოს წარმოდგენილი.',
		invalidHeight: 'უჯრის სიმაღლე რიცხვით უნდა იყოს წარმოდგენილი.',
		invalidRowSpan: 'სტრიქონების რაოდენობა მთელი რიცხვი უნდა იყოს.',
		invalidColSpan: 'სვეტების რაოდენობა მთელი რიცხვი უნდა იყოს.',
		chooseColor: 'არჩევა'
	},
	cellPad: 'უჯრის კიდე (padding)',
	cellSpace: 'უჯრის სივრცე (spacing)',
	column: {
		menu: 'სვეტი',
		insertBefore: 'სვეტის ჩამატება წინ',
		insertAfter: 'სვეტის ჩამატება მერე',
		deleteColumn: 'სვეტების წაშლა'
	},
	columns: 'სვეტი',
	deleteTable: 'ცხრილის წაშლა',
	headers: 'სათაურები',
	headersBoth: 'ორივე',
	headersColumn: 'პირველი სვეტი',
	headersNone: 'არაფერი',
	headersRow: 'პირველი სტრიქონი',
	invalidBorder: 'ჩარჩოს ზომა რიცხვით უდნა იყოს წარმოდგენილი.',
	invalidCellPadding: 'უჯრის კიდე (padding) რიცხვით უნდა იყოს წარმოდგენილი.',
	invalidCellSpacing: 'უჯრის სივრცე (spacing) რიცხვით უნდა იყოს წარმოდგენილი.',
	invalidCols: 'სვეტების რაოდენობა დადებითი რიცხვი უნდა იყოს.',
	invalidHeight: 'ცხრილის სიმაღლე რიცხვით უნდა იყოს წარმოდგენილი.',
	invalidRows: 'სტრიქონების რაოდენობა დადებითი რიცხვი უნდა იყოს.',
	invalidWidth: 'ცხრილის სიგანე რიცხვით უნდა იყოს წარმოდგენილი.',
	menu: 'ცხრილის პარამეტრები',
	row: {
		menu: 'სტრიქონი',
		insertBefore: 'სტრიქონის ჩამატება წინ',
		insertAfter: 'სტრიქონის ჩამატება მერე',
		deleteRow: 'სტრიქონების წაშლა'
	},
	rows: 'სტრიქონი',
	summary: 'შეჯამება',
	title: 'ცხრილის პარამეტრები',
	toolbar: 'ცხრილი',
	widthPc: 'პროცენტი',
	widthPx: 'წერტილი',
	widthUnit: 'საზომი ერთეული'
} );
