﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameLevelPersonCountViewModel
    {
        /// <summary>
        ///關卡id
        /// </summary>
        [DisplayName("關卡id")]
        public string LEVEL_NO { get; set; }

        /// <summary>
        ///關卡類別
        /// </summary>
        [DisplayName("關卡類別")]
        public string LEVEL_TYPE { get; set; }

        /// <summary>
        ///關卡序順
        /// </summary>
        [DisplayName("關卡序順")]
        public string LEVEL_ITEM { get; set; }

        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///關卡名稱
        /// </summary>
        [DisplayName("關卡名稱")]
        public string LEVEL_NAME { get; set; }

        /// <summary>
        ///關卡圖片
        /// </summary>
        [DisplayName("關卡圖片")]
        public string LEVEL_IMG { get; set; }

        /// <summary>
        ///關卡說明
        /// </summary>
        [DisplayName("關卡說明")]
        public string LEVEL_DESC { get; set; }

        /// <summary>
        ///酷幣點數
        /// </summary>
        [DisplayName("酷幣點數")]
        public int? CASH { get; set; }


        /// <summary>
        ///可重複闖關
        /// </summary>
        [DisplayName("可重複闖關")]
        public bool? Y_REPEAT { get; set; }

        /// <summary>
        /// 此關卡闖關人數
        /// </summary>
        public double Person_Count  { get; set; }

        /// <summary>
        /// 此關卡闖關次數
        /// </summary>
        public double Number_Count { get; set; }

        /// <summary>
        /// 此關卡闖關總點數
        /// </summary>
        public double Number_CASH { get; set; }


        public double RATE { get; set; }


    }
}
