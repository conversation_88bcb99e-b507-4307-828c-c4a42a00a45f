﻿
using ECOOL_APP.EF;
using MvcPaging;
using System.Collections.Generic;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class QuerySelectViewModel
    {
        /// <summary>
        /// 結果清單
        /// </summary>
        public IPagedList<HRMT01> HRMT01List { get; set; }
        public virtual ICollection<QuerySelectPrintViewModel> Chk { get; set; }

        /// <summary>
        /// 查詢條件
        /// </summary>
        public ADDI09SearchQuerySelectViewModel Search { get; set; }

        /// <summary>
        /// 選取清單
        /// </summary>
        public List<ADDI09EditViewModel> EditData { get; set; }

    }
}
