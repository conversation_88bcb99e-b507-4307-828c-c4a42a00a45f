﻿@model BarcCodeMyCashIndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    //兌換獎品系統路徑
    ViewBag.SysAwardPath = EcoolWeb.Controllers.AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);
    List<HRMT01QTY> HRMT01QTYItems = new List<HRMT01QTY>();
    //HRMT01QTYItems = (List<HRMT01QTY>)ViewBag.AWAT01List
}

@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.WhereSchoolNo)
@Html.HiddenFor(m => m.TimeoutSeconds)
@Html.HiddenFor(m => m.NoBook)
@Html.HiddenFor(m => m.ShowStep2)
@Html.HiddenFor(m => m.ISGIF)
@Html.Partial("_Notice")

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />

<header>
    <h1 class="page-title"><i class="icon-size-lg icon-search mr-2"></i>酷幣查詢</h1>
</header>
<section class="bg-tertiary rounded atm-honorRoll">
    <!-- 累積榮譽榜 -->
    <div class="row">
        <div class="col-sm-6 col-md-3 d-flex justify-content-between justify-content-md-end align-items-center flex-row atm-honorRoll-box">
            <h2 class="atm-honorRoll-title text-center d-inline-block text-orange">累計點數<br class="d-none d-md-block" />榮譽榜</h2>
            <i class="icon icon-bigMedal"></i>
        </div>
        <div class="col-sm-6 col-md-9 d-flex align-items-center">
            <div class="atm-honorRoll-bg my-2">
                <ol class="atm-honorRoll-list">
                    <li>
                        <span style="color:#8a4000"> @ViewBag.SortBoard</span>
                    </li>
                </ol>
            </div>
        </div>
    </div>
</section>

<section class="row align-items-stretch">
    <div class="col-md-7 col-lg-8 d-flex bg-tertiary rounded">
        <div class="flex-fill">
            <!-- 我要查詢 -->
            <div class="p-2 mt-1">
                <div class="row searchArea">
                    <div class="col-md-4 col-lg-3 text-center">
                        <h2 class="searchArea-title mb-2 mb-md-2 mb-lg-0 text-nowrap d-none d-md-block">我要查詢</h2>
                    </div>
                    <div class="col-md-8 col-lg-9">
                        <div class="input-group">
                            <span class="placeholderIcon"><i class="fa fa-2x fa-user" aria-hidden="true"></i></span>
                            @Html.EditorFor(m => m.WhereKeyword, new { htmlAttributes = new { @class = "form-control", @onKeyPress = "call(event,this);", @placeholder = "請感應數位學生證或輸入學號", @ariaLabel = "請感應數位學生證或輸入學號，輸入後按下搜尋鈕" } })
                            <div class="input-group-append">
                                <button class="btn btn-primary text-white" type="submit" id="button-addon2" title="搜尋">
                                    <i class="fa fa-2x fa-search" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 跑馬燈 -->
            <div class="p-2 mt-1 mb-3 mb-md-0">
                <div class="marquee">
                    <i class="icon-size-m icon-announce"></i>
                    <span class="text-danger text-nowrap">現有資產排名:</span>
                    <div class="js-conveyor-1">
                        <ul>
                            <li>

                                <span>@ViewBag.SortBoard2</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-5 col-lg-4 col-lg-3 px-0 pl-md-3 mt-3 mt-md-0 sideArea flex-fill d-flex flex-column flex-fill justify-content-between">
        <div class="img-box mb-auto">
            <img class="img-fluid" src="@Url.Content("~/Content/images/082819_RFID_TagCard-網頁.gif")" alt="卡片感應示意圖">
        </div>
        <div class="d-flex flex-wrap mt-3">
            <div class="col-4 text-center px-1">
                <button type="button" class="btn btn-info btn-block" onclick="BT3_CLICK()" title="iStory">
                    <i class="icon-size-m icon-iStory"></i><br class="d-block d-sm-none d-md-block" />iStory
                </button>
            </div>
            <div class="col-4 text-center px-1">
                <button type="button" class="btn btn-deposit btn-block" onclick="BT4_CLICK()" title="存紙本點數">
                    <i class="icon-size-m icon-wallet"></i><br class="d-block d-sm-none d-md-block" />存款
                </button>
            </div>
            <div class="col-4 text-center px-1">
                <button type="button" class="btn btn-light-orange btn-block text-orange text-nowrap" onclick="BT1_CLICK()" title="兌換獎品">
                    <i class="icon-size-m icon-gift"></i><br class="d-block d-sm-none d-md-block" />兌換
                </button>
            </div>
        </div>
        <button type="button" class="btn btn-light btn-block my-3 mb-md-0" onclick="BT2_CLICK()">
            <i class="fa fa-reply mr-3" aria-hidden="true"></i>回首頁
        </button>

    </div>
</section>
<input type="text" id="displayBox" name="displayBox" value="0" style="display:none">


<script src="~/Scripts/grids.js"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script>
    var oTimerId;

    function Timeout() {
        var SCHOOL_NO = '';
        SCHOOL_NO = $("#@Html.IdFor(m=>m.WhereSchoolNo)").val();
        if ('@Model.ChangeMode' == "(1)酷幣點數排行榜+現有點數排行榜") {

                 window.location.href = "@Url.Action("LeaderIndex", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index4&TimeoutSeconds=" + '@Model.TimeoutSeconds';



        }
        else if ('@Model.ChangeMode' == "(2)閱讀認證排行榜+運動撲滿排行榜") {



            window.location.href =    "@Url.Action("LeaderIndex2", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index4&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }
        else if ('@Model.ChangeMode' == "(3)酷幣點數排行榜+現有點数排行榜+閱讀認證排行榜+運動撲滿排行榜")  {

                    window.location.href =    "@Url.Action("LeaderIndex3", "BarcCodeMyCash")?" + "WhereSchoolNo = " +@Model.WhereSchoolNo+"&FROMACTION=Index4&TimeoutSeconds=" + '@Model.TimeoutSeconds';




        }

        x = 0;

        return true;
    }



    x = 0
    function countSecond() {
        var Detime = 0;
        Detime =  '@Model.DelayTime'* 60;
        if (x < Detime) {
            x = x + 1
            document.getElementById("displayBox").value = x
            setTimeout("countSecond()", 1000)
        }
        else {
            Timeout();

        }
    }
    document.onmouseup = function () {
        x = 0;
    }
    if ('@Model.DelayTime' != '0.00') {


        countSecond()

    }

    $(document).ready(function () {
        $(".colorbox").colorbox({ photo: false, maxWidth: "80%", maxHeight: "80%", opacity: 0.82 });
          $("#@Html.IdFor(m=>m.WhereKeyword)").val('');
          $("#@Html.IdFor(m=>m.WhereKeyword)").focus();

           if ($('#@Html.IdFor(m => m.TimeoutSeconds)').val() != "") {
              var DivATM2 = document.getElementById("atm_btn_img_1");
              DivATM2.style.display = 'none';

              var DivATM2_2 = document.getElementById("atm_btn_img_2");
              DivATM2_2.style.display = 'none';

        }
        if ('@Model.DelayTime' != '0.00'){
            document.onmousedown = ReCalculate();
            document.onmousemove = ReCalculate();
        }
        });

    function call(e, input) {
        x = 0;
        var code = (e.keyCode ? e.keyCode : e.which);

        if (code == 13) // 13 是 Enter 按鍵的值
        {
            event.preventDefault();
            if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
        }
    }

    // 點選查詢Div Row也submit
    $("#board_text_1-3_a1").on('click', function (e) {
        // 點選的是input則返回
        if (e.target.id == "WhereKeyword") return;
        $('#@Html.IdFor(m => m.WhereKeyword)').focus();
        if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
    });

   function BT1_CLICK()
   {
     //  $(".colorbox").colorbox({ photo: true, maxWidth: "80%", maxHeight: "80%", opacity: 0.82 });
       var wkey = $("#WhereKeyword").val();
       if (wkey == '' || wkey == undefined) {
           alert('未輸入學號/掃描條碼');
       }
       else {
           $("#ISGIF").val('true');
           funAjax();

       }
   }
    function BT3_CLICK() {
        var wkey = $("#WhereKeyword").val();
        if (wkey == '' || wkey == undefined) {
            alert('未輸入學號/掃描條碼');
        }
        else {
            $('#form1').attr("action", "@Url.Action("IstoryUserIndex", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo })")
              $('#form1').attr('target', '_blank').submit().removeAttr('target');
                  // $('#form1').attr('target', '_blank').submit();

        }
    }
    function BT4_CLICK() {
        var wkey = $("#WhereKeyword").val();
        var Itemlocal = "@Url.Action("Index2", "ADDI13")?" + "SCHOOL_NO1=" +@Model.WhereSchoolNo+"&CARD_NO=" + $("#WhereKeyword").val()+"&redirect="+"@Url.Action("Index4", "BarcCodeMyCash", new { WhereSchoolNo = Model.WhereSchoolNo })";
        window.open(Itemlocal, '_self');
        @*$('#form1').attr("action", "@Url.Action("Index2", "ADDI13", new { SCHOOL_NO1 = Model.WhereSchoolNo })")
        $('#form1').attr('target', '_blank').submit().removeAttr('target');*@

    }
    function BT2_CLICK() {
        $('#@Html.IdFor(m => m.WhereKeyword)').val('')
        $('#form1').attr("action", "@Url.Action("Index4", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo})")
        $('#form1').submit();

        }
</script>