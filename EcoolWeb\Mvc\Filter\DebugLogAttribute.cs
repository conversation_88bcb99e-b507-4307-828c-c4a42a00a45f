﻿using EcoolWeb.Models;
using log4net;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Mvc.Filter
{
    public class DebugLogAttribute : System.Web.Mvc.ActionFilterAttribute
    {
        private log4net.ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public override void OnActionExecuting(ActionExecutingContext ctx)
        {
            string func_debug_flag = (string)ctx.RequestContext.HttpContext.Session[CONSTANT.FUNCTION_DEBUG_FLAG];
            if (func_debug_flag == null)
            {
                func_debug_flag = "OFF";
                ctx.RequestContext.HttpContext.Session[CONSTANT.FUNCTION_DEBUG_FLAG] = func_debug_flag;
            }
            logger.Info("func_debug_flag : " + func_debug_flag);
            if ("ON".Equals(func_debug_flag))
            {
                ctx.RequestContext.HttpContext.Session[CONSTANT.FUNCTION_DEBUG_FLAG] = "OFF";//turn off
                NameValueCollection NVC = ctx.RequestContext.HttpContext.Request.Params;
                string[] all_keys = NVC.AllKeys;
                //UserProfile user = UserProfile.Get();
                /*
                logger.Info(" -----------  Function Name : " + user.PGMNM + "(" + user.PGMID + ") ------------- ");

                Dictionary<string, string> KV = RequestHelper.RetrieveKeyValues(new string[] { });
                foreach (string key in KV.Keys)
                {
                    logger.Info("   " + key + " = " + KV[key]);
                }
                */
            }
        }


        public override void OnActionExecuted(ActionExecutedContext ctx)
        {

        }
    }
}