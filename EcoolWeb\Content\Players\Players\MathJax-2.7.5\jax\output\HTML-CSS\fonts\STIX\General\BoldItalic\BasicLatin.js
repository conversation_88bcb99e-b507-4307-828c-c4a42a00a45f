/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/BasicLatin.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{32:[0,0,250,0,0],33:[684,13,389,67,370],34:[685,-398,555,136,536],35:[700,0,500,-32,532],36:[733,100,500,-20,497],37:[706,29,757,80,707],38:[682,19,849,76,771],39:[685,-398,278,128,268],40:[685,179,333,28,344],41:[685,179,333,-44,271],42:[685,-252,500,101,492],43:[506,0,570,33,537],44:[134,182,250,-60,144],45:[282,-166,333,2,271],46:[135,13,250,-9,139],47:[685,18,278,-64,342],48:[683,14,500,17,477],49:[683,0,500,5,419],50:[683,0,500,-27,446],51:[683,13,500,-14,450],52:[683,0,500,-15,503],53:[669,13,500,-11,486],54:[679,15,500,23,509],55:[669,0,500,52,525],56:[683,13,500,3,476],57:[683,10,500,-12,475],58:[459,13,333,23,264],59:[459,183,333,-25,264],60:[518,12,570,31,539],61:[399,-107,570,33,537],62:[518,12,570,31,539],63:[684,13,500,79,470],64:[685,18,939,118,825],65:[683,0,667,-68,593],66:[669,0,667,-25,624],67:[685,18,667,32,677],68:[669,0,722,-46,685],69:[669,0,667,-27,653],70:[669,0,667,-13,660],71:[685,18,722,21,705],72:[669,0,778,-24,799],73:[669,0,389,-32,406],74:[669,99,500,-46,524],75:[669,0,667,-21,702],76:[669,0,611,-22,590],77:[669,12,889,-29,917],78:[669,15,722,-27,748],79:[685,18,722,27,691],80:[669,0,611,-28,613],81:[685,208,722,27,691],82:[669,0,667,-28,623],83:[685,18,556,2,526],84:[669,0,611,49,650],85:[669,18,722,67,744],86:[669,18,667,66,715],87:[669,18,889,64,940],88:[669,0,667,-24,694],89:[669,0,611,71,659],90:[669,0,611,-12,589],91:[674,159,333,-37,362],92:[685,18,278,-1,279],93:[674,157,333,-56,343],94:[669,-304,570,67,503],95:[-75,125,500,0,500],96:[697,-516,333,85,297],97:[462,14,500,-21,456],98:[699,13,500,-14,444],99:[462,13,444,-5,392],100:[699,13,500,-21,517],101:[462,13,444,5,398],102:[698,205,333,-169,446],103:[462,203,500,-52,477],104:[699,9,556,-13,498],105:[684,9,278,2,262],106:[685,207,278,-189,279],107:[699,8,500,-23,483],108:[699,9,278,2,290],109:[462,9,778,-14,723],110:[462,9,556,-6,494],111:[462,13,500,-3,441],112:[462,205,500,-120,446],113:[462,205,500,1,471],114:[462,0,389,-21,389],115:[462,13,389,-19,333],116:[594,9,278,-11,281],117:[462,9,556,15,493],118:[462,13,444,15,401],119:[462,13,667,15,614],120:[462,13,500,-45,469],121:[462,205,444,-94,392],122:[449,78,389,-43,368],123:[686,187,348,4,436],124:[685,18,220,66,154],125:[686,187,348,-129,303],126:[331,-175,570,54,516]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/BasicLatin.js");
