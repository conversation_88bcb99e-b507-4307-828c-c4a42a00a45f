<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EntityFramework.Extended</name>
    </assembly>
    <members>
        <member name="T:EntityFramework.Audit.AlwaysAuditAttribute">
            <summary>
            Indicates that a field in an audited class should always be included in the audit data even if it hasn't been changed.
            </summary>
            <seealso cref="T:EntityFramework.Audit.AuditAttribute"/>
            <seealso cref="T:EntityFramework.Audit.NotAuditedAttribute"/>
        </member>
        <member name="T:EntityFramework.Audit.AuditAction">
            <summary>
            A list of entity actions for the audit log.
            </summary>
        </member>
        <member name="F:EntityFramework.Audit.AuditAction.Added">
            <summary>
            The entity was inserted/added.
            </summary>
        </member>
        <member name="F:EntityFramework.Audit.AuditAction.Modified">
            <summary>
            The entity was updated/modifed.
            </summary>
        </member>
        <member name="F:EntityFramework.Audit.AuditAction.Deleted">
            <summary>
            The entity was deleted.
            </summary>
        </member>
        <member name="T:EntityFramework.Audit.AuditAttribute">
            <summary>
            Indicates that a class can be audited.
            </summary>
            <remarks>
            Use the <see cref="T:EntityFramework.Audit.NotAuditedAttribute"/> attribute to prevent a field from being included in the audit
            </remarks>
            <seealso cref="T:EntityFramework.Audit.NotAuditedAttribute"/>
            <seealso cref="T:EntityFramework.Audit.AlwaysAuditAttribute"/>
        </member>
        <member name="T:EntityFramework.Audit.AuditConfiguration">
            <summary>
            A class to configure the output of an <see cref="T:EntityFramework.Audit.AuditLog"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditConfiguration.IsAuditable``1(System.Boolean)">
            <summary>
            Fluent method to set whether the entity class is auditable.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="value">if set to <c>true</c> make the entity auditable.</param>
            <returns>A fluent object to further configure the auditing for the entity type.</returns>
        </member>
        <member name="P:EntityFramework.Audit.AuditConfiguration.IncludeInserts">
            <summary>
            Gets or sets a value indicating whether to include inserted entities. Default is <c>true</c>.
            </summary>
            <value>
              <c>true</c> to include inserted entities; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditConfiguration.IncludeDeletes">
            <summary>
            Gets or sets a value indicating whether to include deleted entities. Default is <c>true</c>.
            </summary>
            <value>
              <c>true</c> to include deleted entities; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditConfiguration.IncludeRelationships">
            <summary>
            Gets or sets a value indicating whether to include relationship properties.
            </summary>
            <value>
              <c>true</c> to include relationship properties; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditConfiguration.LoadRelationships">
            <summary>
            Gets or sets a value indicating whether to load a relationship when it is not already
            loaded to the context. Please not that this will make database call.
            </summary>
            <value>
              <c>true</c> to load relationships; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditConfiguration.DefaultAuditable">
            <summary>
            Gets or sets a value indicating whether an entity is auditable by default.
            </summary>
            <value>
              <c>true</c> if default auditable; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditConfiguration.MaintainAcrossSaves">
            <summary>
            Gets or sets a value indicating whether the log is maintain across multiple calls to <see cref="M:System.Data.Entity.DbContext.SaveChanges"/>.
            Each additional call will add to the <see cref="P:EntityFramework.Audit.AuditLogger.LastLog"/> instance.
            </summary>
            <value>
              <c>true</c> to maintain log across saves; otherwise, <c>false</c> to create a new log on eash save.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditConfiguration.Default">
            <summary>
            Gets the default instance of AuditConfiguration.
            </summary>
            <value>The default instance.</value>
        </member>
        <member name="T:EntityFramework.Audit.AuditConfiguration.AuditEntityConfiguration`1">
            <summary>
            A class to configure the auditing for the entity type.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
        </member>
        <member name="M:EntityFramework.Audit.AuditConfiguration.AuditEntityConfiguration`1.NotAudited``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Indicates that a field in an audited class should not be included in the audit log.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="propertyExpression">The property expression.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Audit.AuditConfiguration.AuditEntityConfiguration`1.AlwaysAudited``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Indicates that a field in an audited class should always be included in the audit data even if it hasn't been changed.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="propertyExpression">The property expression.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Audit.AuditConfiguration.AuditEntityConfiguration`1.FormatWith``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Linq.Expressions.Expression{System.Func{EntityFramework.Audit.AuditPropertyContext,System.Object}})">
            <summary>
            Indicates a method to control the output format of the <see cref="T:EntityFramework.Audit.AuditProperty"/> values.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="propertyExpression">The property expression.</param>
            <param name="formatMethod">The format method.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Audit.AuditConfiguration.AuditEntityConfiguration`1.DisplayMember``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Indicates that a field in an audited class should be used as the relationship display member.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="propertyExpression">The property expression.</param>
            <returns></returns>
        </member>
        <member name="T:EntityFramework.Audit.AuditEntity">
            <summary>
            A class for logging the changes to an entity.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditEntity.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditEntity"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditEntity.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditEntity"/> class.
            </summary>
            <param name="current">The current entity the AuditEntity is based on.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditEntity.Equals(EntityFramework.Audit.AuditEntity)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <returns>
            true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditEntity.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />.
            </summary>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />; otherwise, false.
            </returns>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />. </param>
        </member>
        <member name="M:EntityFramework.Audit.AuditEntity.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object" />.
            </returns>
        </member>
        <member name="P:EntityFramework.Audit.AuditEntity.Action">
            <summary>
            Gets or sets the action that was taken on the entity.
            </summary>
            <value>The action that was taken on the entity.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditEntity.Type">
            <summary>
            Gets or sets the data type of the entity.
            </summary>
            <value>The data type of the entity.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditEntity.Keys">
            <summary>
            Gets the list of properties that are the key for the entity.
            </summary>
            <value>The list of properties that are the key for the entity.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditEntity.Current">
            <summary>
            Gets the entity in its current modified state. Value is held as a WeakReference and can be disposed.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditEntity.EntityType">
            <summary>
            Gets the type of the entity.
            </summary>
            <value>
            The type of the entity.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditEntity.Properties">
            <summary>
            Gets the list of properties that action was taken on.
            </summary>
            <value>The list of properties that action was taken on.</value>
        </member>
        <member name="T:EntityFramework.Audit.AuditKey">
            <summary>
            A class used to hold audit key values.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditKey.Name">
            <summary>
            Gets or sets the name of the property.
            </summary>
            <value>The name of the property.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditKey.Type">
            <summary>
            Gets or sets the type of the property.
            </summary>
            <value>The type of the property.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditKey.Value">
            <summary>
            Gets or sets the current/changed value of the property.
            </summary>
            <value>The current value of the property.</value>
        </member>
        <member name="T:EntityFramework.Audit.AuditKeyCollection">
            <summary>
            A keyed collection of <see cref="T:EntityFramework.Audit.AuditKey"/>
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditKeyCollection.GetKeyForItem(EntityFramework.Audit.AuditKey)">
            <summary>
            When implemented in a derived class, extracts the key from the specified element.
            </summary>
            <param name="item">The element from which to extract the key.</param>
            <returns>The key for the specified element.</returns>
        </member>
        <member name="T:EntityFramework.Audit.AuditLog">
            <summary>
            A class representing a log of the changes.
            </summary>
        </member>
        <member name="F:EntityFramework.Audit.AuditLog.AuditNamespace">
            <summary>
            The schema namespace for the audit log.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditLog.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditLog"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditLog.Refresh">
            <summary>
            Refresh key and property values. Call after Save to capture database updated keys and values.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditLog.ToXml">
            <summary>
            Returns an XML string of the <see cref="T:EntityFramework.Audit.AuditLog"/>.
            </summary>
            <returns>An XML string of the <see cref="T:EntityFramework.Audit.AuditLog"/>.</returns>
        </member>
        <member name="M:EntityFramework.Audit.AuditLog.ToXml(System.Xml.XmlWriter)">
            <summary>
            Saves the <see cref="T:EntityFramework.Audit.AuditLog"/> to the specified <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="writer">The writer to save <see cref="T:EntityFramework.Audit.AuditLog"/> to.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditLog.FromXml(System.String)">
            <summary>
            Returns an <see cref="T:EntityFramework.Audit.AuditLog"/> object created from an XML string.
            </summary>
            <param name="auditLog">
            An XML string
            </param>
            <returns>
            An <see cref="T:EntityFramework.Audit.AuditLog"/> object created from an XML string.
            </returns>
        </member>
        <member name="M:EntityFramework.Audit.AuditLog.FromXml(System.Xml.XmlReader)">
            <summary>
            Returns an <see cref="T:EntityFramework.Audit.AuditLog"/> object created from an XML string.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> to create the <see cref="T:EntityFramework.Audit.AuditLog"/> from.</param>
            <returns>
            An <see cref="T:EntityFramework.Audit.AuditLog"/> object created from an <see cref="T:System.Xml.XmlReader"/>.
            </returns>
        </member>
        <member name="P:EntityFramework.Audit.AuditLog.Username">
            <summary>
            Gets or sets the user name that made the changes.
            </summary>
            <value>The user name that made the changes.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditLog.Date">
            <summary>
            Gets or sets the date when the changes were made.
            </summary>
            <value>The date when the changes were made.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditLog.Entities">
            <summary>
            Gets the list entities that have changes.
            </summary>
            <value>The list entities that have changes.</value>
        </member>
        <member name="T:EntityFramework.Audit.AuditLogger">
            <summary>
            A class used to create an AuditLog.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.#ctor(System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditLogger"/> class.
            </summary>
            <param name="objectContext">The <see cref="P:EntityFramework.Audit.AuditLogger.ObjectContext"/> to create the <see cref="T:EntityFramework.Audit.AuditLog"/> from.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.#ctor(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Audit.AuditConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditLogger"/> class.
            </summary>
            <param name="objectContext">The <see cref="P:EntityFramework.Audit.AuditLogger.ObjectContext"/> to create the <see cref="T:EntityFramework.Audit.AuditLog"/> from.</param>
            <param name="configuration">The <see cref="T:EntityFramework.Audit.AuditConfiguration"/> to use when creating the <see cref="T:EntityFramework.Audit.AuditLog"/>.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.#ctor(System.Data.Entity.DbContext)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditLogger"/> class.
            </summary>
            <param name="dbContext">The <see cref="T:System.Data.Entity.DbContext"/> to create the <see cref="T:EntityFramework.Audit.AuditLog"/> from.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.#ctor(System.Data.Entity.DbContext,EntityFramework.Audit.AuditConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditLogger"/> class.
            </summary>
            <param name="dbContext">The <see cref="T:System.Data.Entity.DbContext"/> to create the <see cref="T:EntityFramework.Audit.AuditLog"/> from.</param>
            <param name="configuration">The <see cref="T:EntityFramework.Audit.AuditConfiguration"/> to use when creating the <see cref="T:EntityFramework.Audit.AuditLog"/>.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.OnSavingChanges(System.Object,System.EventArgs)">
            <summary>
            Called when OjbectContext is saving changes.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.CreateLog">
            <summary>
            Creates the <see cref="T:EntityFramework.Audit.AuditLog"/> from the current <see cref="P:EntityFramework.Audit.AuditLogger.ObjectContext"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.UpdateLog(EntityFramework.Audit.AuditLog)">
            <summary>
            Updates the <see cref="T:EntityFramework.Audit.AuditLog"/> from the current <see cref="P:EntityFramework.Audit.AuditLogger.ObjectContext"/> changes. 
            </summary>
            <param name="auditLog">The audit log.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:EntityFramework.Audit.AuditLogger.Finalize">
            <summary>
            Releases unmanaged resources and performs other cleanup operations before the
            <see cref="T:EntityFramework.Audit.AuditLogger"/> is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditLogger.ObjectContext">
            <summary>
            Gets the ObjectContext to create the <see cref="T:EntityFramework.Audit.AuditLog"/> from.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditLogger.Configuration">
            <summary>
            Gets the <see cref="T:EntityFramework.Audit.AuditConfiguration"/> to use when creating the <see cref="T:EntityFramework.Audit.AuditLog"/>.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditLogger.LastLog">
            <summary>
            Gets the last <see cref="T:EntityFramework.Audit.AuditLog"/> created.
            </summary>
        </member>
        <member name="T:EntityFramework.Batch.IBatchRunner">
            <summary>
            An interface to abstract the batch execute from expressions.
            </summary>
        </member>
        <member name="M:EntityFramework.Batch.IBatchRunner.Delete``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0})">
            <summary>
            Create and runs a batch delete statement.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <returns>The number of rows deleted.</returns>
        </member>
        <member name="M:EntityFramework.Batch.IBatchRunner.DeleteAsync``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0})">
            <summary>
            Create and runs a batch delete statement asynchronously.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <returns>The number of rows deleted.</returns>
        </member>
        <member name="M:EntityFramework.Batch.IBatchRunner.Update``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Create and runs a batch update statement.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <param name="updateExpression">The update expression.</param>
            <returns>The number of rows updated.</returns>
        </member>
        <member name="M:EntityFramework.Batch.IBatchRunner.UpdateAsync``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Create and runs a batch update statement asynchronously.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <param name="updateExpression">The update expression.</param>
            <returns>The number of rows updated.</returns>
        </member>
        <member name="T:EntityFramework.Batch.MySqlBatchRunner">
            <summary>
            A batch execution runner for MySQL Server.
            </summary>
        </member>
        <member name="M:EntityFramework.Batch.MySqlBatchRunner.Delete``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0})">
            <summary>
            Create and run a batch delete statement.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <returns>
            The number of rows deleted.
            </returns>
        </member>
        <member name="M:EntityFramework.Batch.MySqlBatchRunner.DeleteAsync``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0})">
            <summary>
            Create and run a batch delete statement asynchronously.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <returns>
            The number of rows deleted.
            </returns>
        </member>
        <member name="M:EntityFramework.Batch.MySqlBatchRunner.Update``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Create and run a batch update statement.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <param name="updateExpression">The update expression.</param>
            <returns>
            The number of rows updated.
            </returns>
        </member>
        <member name="M:EntityFramework.Batch.MySqlBatchRunner.UpdateAsync``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Create and run a batch update statement asynchronously.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <param name="updateExpression">The update expression.</param>
            <returns>
            The number of rows updated.
            </returns>
        </member>
        <member name="T:EntityFramework.Batch.SqlServerBatchRunner">
            <summary>
            A batch execution runner for SQL Server.
            </summary>
        </member>
        <member name="M:EntityFramework.Batch.SqlServerBatchRunner.Delete``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0})">
            <summary>
            Create and run a batch delete statement.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <returns>
            The number of rows deleted.
            </returns>
        </member>
        <member name="M:EntityFramework.Batch.SqlServerBatchRunner.DeleteAsync``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0})">
            <summary>
            Create and run a batch delete statement asynchronously.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <returns>
            The number of rows deleted.
            </returns>
        </member>
        <member name="M:EntityFramework.Batch.SqlServerBatchRunner.Update``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Create and run a batch update statement.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <param name="updateExpression">The update expression.</param>
            <returns>
            The number of rows updated.
            </returns>
        </member>
        <member name="M:EntityFramework.Batch.SqlServerBatchRunner.UpdateAsync``1(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Mapping.EntityMap,System.Data.Entity.Core.Objects.ObjectQuery{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Create and run a batch update statement asynchronously.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get connection and metadata information from.</param>
            <param name="entityMap">The <see cref="T:EntityFramework.Mapping.EntityMap"/> for <typeparamref name="TEntity"/>.</param>
            <param name="query">The query to create the where clause from.</param>
            <param name="updateExpression">The update expression.</param>
            <returns>
            The number of rows updated.
            </returns>
        </member>
        <member name="T:EntityFramework.Caching.CacheExpirationMode">
            <summary>
            The cache expiration mode.
            </summary>
        </member>
        <member name="F:EntityFramework.Caching.CacheExpirationMode.None">
            <summary>
            The cache item will not expire.
            </summary>
        </member>
        <member name="F:EntityFramework.Caching.CacheExpirationMode.Duration">
            <summary>
            The cache item will expire using the Duration property to calculate
            the absolute expiration from DateTimeOffset.Now.
            </summary>
        </member>
        <member name="F:EntityFramework.Caching.CacheExpirationMode.Sliding">
            <summary>
            The cache item will expire using the Duration property as the
            sliding expiration.
            </summary>
        </member>
        <member name="F:EntityFramework.Caching.CacheExpirationMode.Absolute">
            <summary>
            The cache item will expire on the AbsoluteExpiration DateTime.
            </summary>
        </member>
        <member name="T:EntityFramework.Caching.CacheKey">
            <summary>
            A class representing a unique key for cache items.
            </summary>
        </member>
        <member name="M:EntityFramework.Caching.CacheKey.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Caching.CacheKey"/> class.
            </summary>
            <param name="key">The key for a cache item.</param>
        </member>
        <member name="M:EntityFramework.Caching.CacheKey.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Caching.CacheKey"/> class.
            </summary>
            <param name="key">The key for a cache item.</param>
            <param name="tags">The tags for the cache item.</param>
        </member>
        <member name="P:EntityFramework.Caching.CacheKey.Key">
            <summary>
            Gets the key for a cached item.
            </summary>
        </member>
        <member name="P:EntityFramework.Caching.CacheKey.Tags">
            <summary>
            Gets the tags for a cached item.
            </summary>
        </member>
        <member name="T:EntityFramework.Caching.CacheManager">
            <summary>
            The CacheManager with support for providers used by FromCache extension methods.
            </summary>
            <remarks>
            <para>
            This CacheManager uses the <see cref="T:EntityFramework.Locator"/> container to resolve an <see cref="T:EntityFramework.Caching.ICacheProvider"/> to store the cache items.
            </para>
            <para>
            The CacheManager also supports tagging the cache entry to support expiring by tag. <see cref="T:EntityFramework.Caching.CacheKey"/> supports a list
            of tags to associate with the cache entry.  Use <see cref="F:Expire"/> to evict the cache entry by <see cref="T:EntityFramework.Caching.CacheTag"/>.
            </para>
            </remarks>
            <example>
            Replace cache provider with Memcached provider
            <code><![CDATA[
            Locator.Current.Register<ICacheProvider>(() => new MemcachedProvider());
            ]]>
            </code>
            </example>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Add(System.String,System.Object)">
            <summary>
            Inserts a cache entry into the cache without overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <returns><c>true</c> if insertion succeeded, or <c>false</c> if there is an already an entry in the cache that has the same key as key.</returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Add(System.String,System.Object,System.DateTimeOffset)">
            <summary>
            Inserts a cache entry into the cache without overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="absoluteExpiration">The fixed date and time at which the cache entry will expire.</param>
            <returns>
              <c>true</c> if insertion succeeded, or <c>false</c> if there is an already an entry in the cache that has the same key as key.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Add(System.String,System.Object,System.TimeSpan)">
            <summary>
            Inserts a cache entry into the cache without overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="slidingExpiration">A span of time within which a cache entry must be accessed before the cache entry is evicted from the cache.</param>
            <returns>
              <c>true</c> if insertion succeeded, or <c>false</c> if there is an already an entry in the cache that has the same key as key.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Add(System.String,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache without overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
              <c>true</c> if insertion succeeded, or <c>false</c> if there is an already an entry in the cache that has the same key as key.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Add(EntityFramework.Caching.CacheKey,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache without overwriting any existing cache entry.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
              <c>true</c> if insertion succeeded, or <c>false</c> if there is an already an entry in the cache that has the same key as key.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Get(System.String)">
            <summary>
            Gets the cache value for the specified key
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <returns>The cache value for the specified key, if the entry exists; otherwise, <see langword="null"/>.</returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Object)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value if the key was not in the dictionary.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Object,System.DateTimeOffset)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value if the key was not in the dictionary.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="absoluteExpiration">The fixed date and time at which the cache entry will expire.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Object,System.TimeSpan)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value if the key was not in the dictionary.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="slidingExpiration">A span of time within which a cache entry must be accessed before the cache entry is evicted from the cache.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value if the key was not in the dictionary.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(EntityFramework.Caching.CacheKey,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value if the key was not in the dictionary.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Func{System.String,System.Object})">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned by <paramref name="valueFactory"/>.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The function used to generate a value to insert into cache.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Func{System.String,System.Object},System.DateTimeOffset)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned by <paramref name="valueFactory"/>.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The function used to generate a value to insert into cache.</param>
            <param name="absoluteExpiration">The fixed date and time at which the cache entry will expire.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Func{System.String,System.Object},System.TimeSpan)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned by <paramref name="valueFactory"/>.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The function used to generate a value to insert into cache.</param>
            <param name="slidingExpiration">A span of time within which a cache entry must be accessed before the cache entry is evicted from the cache.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(System.String,System.Func{System.String,System.Object},EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned by <paramref name="valueFactory"/>.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The function used to generate a value to insert into cache.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAdd(EntityFramework.Caching.CacheKey,System.Func{EntityFramework.Caching.CacheKey,System.Object},EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned by <paramref name="valueFactory"/>.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The function used to generate a value to insert into cache.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.GetOrAddAsync(EntityFramework.Caching.CacheKey,System.Func{EntityFramework.Caching.CacheKey,System.Threading.Tasks.Task{System.Object}},EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned asynchronously by <paramref name="valueFactory"/>.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The asynchronous function used to generate a value to insert into cache.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the dictionary, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the dictionary.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Remove(System.String)">
            <summary>
            Removes a cache entry from the cache. 
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <returns>If the entry is found in the cache, the removed cache entry; otherwise, <see langword="null"/>.</returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Remove(EntityFramework.Caching.CacheKey)">
            <summary>
            Removes a cache entry from the cache. 
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <returns>If the entry is found in the cache, the removed cache entry; otherwise, <see langword="null"/>.</returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Expire(System.String)">
            <summary>
            Expires the specified cache tag.
            </summary>
            <param name="tag">The cache tag.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Expire(EntityFramework.Caching.CacheTag)">
            <summary>
            Expires the specified cache tag.
            </summary>
            <param name="cacheTag">The cache tag.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Set(System.String,System.Object)">
            <summary>
            Inserts a cache entry into the cache overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Set(System.String,System.Object,System.DateTimeOffset)">
            <summary>
            Inserts a cache entry into the cache overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="absoluteExpiration">The fixed date and time at which the cache entry will expire.</param>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Set(System.String,System.Object,System.TimeSpan)">
            <summary>
            Inserts a cache entry into the cache overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="slidingExpiration">A span of time within which a cache entry must be accessed before the cache entry is evicted from the cache.</param>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Set(System.String,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache overwriting any existing cache entry.
            </summary>
            <param name="key">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Set(EntityFramework.Caching.CacheKey,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache overwriting any existing cache entry.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.Clear">
            <summary>
            Clears all entries from the cache
            </summary>
        </member>
        <member name="M:EntityFramework.Caching.CacheManager.ResolveProvider">
            <summary>
            Gets the registered <see cref="T:EntityFramework.Caching.ICacheProvider"/> from <see cref="T:EntityFramework.Locator"/>.
            </summary>
            <returns>An instance from <see cref="T:EntityFramework.Locator"/>.</returns>
        </member>
        <member name="P:EntityFramework.Caching.CacheManager.Current">
            <summary>
            Gets the current singleton instance of <see cref="T:EntityFramework.Caching.CacheManager"/>.
            </summary>
            <value>The current singleton instance.</value>
        </member>
        <member name="T:EntityFramework.Caching.CachePolicy">
            <summary>
            Represents a set of eviction and expiration details for a specific cache entry.
            </summary>
        </member>
        <member name="M:EntityFramework.Caching.CachePolicy.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Caching.CachePolicy"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Caching.CachePolicy.WithDurationExpiration(System.TimeSpan)">
            <summary>
            Creates a <see cref="T:EntityFramework.Caching.CachePolicy"/> with the absolute expiration.
            </summary>
            <param name="expirationSpan">The <see cref="T:System.TimeSpan"/> used to calculate absolute expiration from now.</param>
            <returns>An instance of <see cref="T:EntityFramework.Caching.CachePolicy"/>.</returns>
        </member>
        <member name="M:EntityFramework.Caching.CachePolicy.WithAbsoluteExpiration(System.DateTimeOffset)">
            <summary>
            Creates a <see cref="T:EntityFramework.Caching.CachePolicy"/> with the absolute expiration.
            </summary>
            <param name="absoluteExpiration">The absolute expiration.</param>
            <returns>An instance of <see cref="T:EntityFramework.Caching.CachePolicy"/>.</returns>
        </member>
        <member name="M:EntityFramework.Caching.CachePolicy.WithSlidingExpiration(System.TimeSpan)">
            <summary>
            Creates a <see cref="T:EntityFramework.Caching.CachePolicy"/> with the sliding expiration.
            </summary>
            <param name="slidingExpiration">The sliding expiration.</param>
            <returns>An instance of <see cref="T:EntityFramework.Caching.CachePolicy"/>.</returns>
        </member>
        <member name="P:EntityFramework.Caching.CachePolicy.Default">
            <summary>
            Gets the default <see cref="T:EntityFramework.Caching.CachePolicy"/>.
            </summary>
        </member>
        <member name="P:EntityFramework.Caching.CachePolicy.Mode">
            <summary>
            Gets or sets the cache expiration mode.
            </summary>
            <value>The cache expiration mode.</value>
        </member>
        <member name="P:EntityFramework.Caching.CachePolicy.AbsoluteExpiration">
            <summary>
            Gets or sets a value that indicates a cache entry should be evicted after a specified duration.
            </summary>
        </member>
        <member name="P:EntityFramework.Caching.CachePolicy.SlidingExpiration">
            <summary>
            Gets or sets a value that indicates a cache entry should be evicted if it has not been accessed in a given span of time. 
            </summary>
        </member>
        <member name="P:EntityFramework.Caching.CachePolicy.Duration">
            <summary>
            Gets or sets a value that indicates a cache entry should be evicted after a given span of time. 
            </summary>
        </member>
        <member name="T:EntityFramework.Caching.ICacheProvider">
            <summary>
            An <see langword="interface"/> defining a cache provider.
            </summary>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.Add(EntityFramework.Caching.CacheKey,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache without overwriting any existing cache entry.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
              <c>true</c> if insertion succeeded, or <c>false</c> if there is an already an entry in the cache that has the same key as key.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.Get(EntityFramework.Caching.CacheKey)">
            <summary>
            Gets the cache value for the specified key
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <returns>The cache value for the specified key, if the entry exists; otherwise, <see langword="null"/>.</returns>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.GetOrAdd(EntityFramework.Caching.CacheKey,System.Func{EntityFramework.Caching.CacheKey,System.Object},EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned by <paramref name="valueFactory"/>.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The function used to generate a value to insert into cache.</param>
            <param name="cachePolicy">A <see cref="T:EntityFramework.Caching.CachePolicy"/> that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the cache, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the cache.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.GetOrAddAsync(EntityFramework.Caching.CacheKey,System.Func{EntityFramework.Caching.CacheKey,System.Threading.Tasks.Task{System.Object}},EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned asynchronously by <paramref name="valueFactory"/>.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The asynchronous function used to generate a value to insert into cache.</param>
            <param name="cachePolicy">A <see cref="T:EntityFramework.Caching.CachePolicy"/> that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the cache, 
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the cache.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.Remove(EntityFramework.Caching.CacheKey)">
            <summary>
            Removes a cache entry from the cache. 
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <returns>If the entry is found in the cache, the removed cache entry; otherwise, <see langword="null"/>.</returns>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.Expire(EntityFramework.Caching.CacheTag)">
            <summary>
            Expires the specified cache tag.
            </summary>
            <param name="cacheTag">The cache tag.</param>
            <returns>The number of items expired.</returns>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.Set(EntityFramework.Caching.CacheKey,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache overwriting any existing cache entry.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">A <see cref="T:EntityFramework.Caching.CachePolicy"/> that contains eviction details for the cache entry.</param>
        </member>
        <member name="M:EntityFramework.Caching.ICacheProvider.ClearCache">
            <summary>
            Clears all entries from the cache
            </summary>
            <returns>The number of items removed.</returns>
        </member>
        <member name="T:EntityFramework.Caching.CacheTag">
            <summary>
            
            </summary>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Caching.CacheTag"/> class.
            </summary>
            <param name="tag">The tag name.</param>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.Equals(EntityFramework.Caching.CacheTag)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.op_Equality(EntityFramework.Caching.CacheTag,EntityFramework.Caching.CacheTag)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.op_Inequality(EntityFramework.Caching.CacheTag,EntityFramework.Caching.CacheTag)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.CacheTag.op_Implicit(EntityFramework.Caching.CacheTag)~System.String">
            <summary>
            Performs an implicit conversion from <see cref="T:EntityFramework.Caching.CacheTag"/> to <see cref="T:System.String"/>.
            </summary>
            <param name="cacheTag">The cache tag.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="T:EntityFramework.Caching.MemoryCacheProvider">
            <summary>
            A cache provider based on <see cref="T:System.Runtime.Caching.MemoryCache"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.Add(EntityFramework.Caching.CacheKey,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache without overwriting any existing cache entry.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">An object that contains eviction details for the cache entry.</param>
            <returns>
              <c>true</c> if insertion succeeded, or <c>false</c> if there is an already an entry in the cache that has the same key as key.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.Get(EntityFramework.Caching.CacheKey)">
            <summary>
            Gets the cache value for the specified key
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <returns>
            The cache value for the specified key, if the entry exists; otherwise, <see langword="null"/>.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.GetOrAdd(EntityFramework.Caching.CacheKey,System.Func{EntityFramework.Caching.CacheKey,System.Object},EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned by <paramref name="valueFactory"/>.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The function used to generate a value to insert into cache.</param>
            <param name="cachePolicy">A <see cref="T:EntityFramework.Caching.CachePolicy"/> that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the cache,
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the cache.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.GetOrAddAsync(EntityFramework.Caching.CacheKey,System.Func{EntityFramework.Caching.CacheKey,System.Threading.Tasks.Task{System.Object}},EntityFramework.Caching.CachePolicy)">
            <summary>
            Gets the cache value for the specified key that is already in the dictionary or the new value for the key as returned asynchronously by <paramref name="valueFactory"/>.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="valueFactory">The asynchronous function used to generate a value to insert into cache.</param>
            <param name="cachePolicy">A <see cref="T:EntityFramework.Caching.CachePolicy"/> that contains eviction details for the cache entry.</param>
            <returns>
            The value for the key. This will be either the existing value for the key if the key is already in the cache,
            or the new value for the key as returned by <paramref name="valueFactory"/> if the key was not in the cache.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.Remove(EntityFramework.Caching.CacheKey)">
            <summary>
            Removes a cache entry from the cache.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <returns>
            If the entry is found in the cache, the removed cache entry; otherwise, <see langword="null"/>.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.Expire(EntityFramework.Caching.CacheTag)">
            <summary>
            Expires the specified cache tag.
            </summary>
            <param name="cacheTag">The cache tag.</param>
            <returns>
            The number of items expired.
            </returns>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.Set(EntityFramework.Caching.CacheKey,System.Object,EntityFramework.Caching.CachePolicy)">
            <summary>
            Inserts a cache entry into the cache overwriting any existing cache entry.
            </summary>
            <param name="cacheKey">A unique identifier for the cache entry.</param>
            <param name="value">The object to insert.</param>
            <param name="cachePolicy">A <see cref="T:EntityFramework.Caching.CachePolicy"/> that contains eviction details for the cache entry.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Caching.MemoryCacheProvider.ClearCache">
            <summary>
            Clears all entries from the cache
            </summary>
            <returns></returns>
        </member>
        <member name="T:EntityFramework.Caching.Evaluator">
            <summary>
            Enables the partial evaluation of queries.
            </summary>
            <remarks>
            From http://msdn.microsoft.com/en-us/library/bb546158.aspx
            Copyright notice http://msdn.microsoft.com/en-gb/cc300389.aspx#O
            </remarks>
        </member>
        <member name="M:EntityFramework.Caching.Evaluator.PartialEval(System.Linq.Expressions.Expression,System.Func{System.Linq.Expressions.Expression,System.Boolean})">
            <summary>
            Performs evaluation and replacement of independent sub-trees
            </summary>
            <param name="expression">The root of the expression tree.</param>
            <param name="fnCanBeEvaluated">A function that decides whether a given expression node can be part of the local function.</param>
            <returns>A new tree with sub-trees evaluated and replaced.</returns>
        </member>
        <member name="M:EntityFramework.Caching.Evaluator.PartialEval(System.Linq.Expressions.Expression)">
            <summary>
            Performs evaluation and replacement of independent sub-trees
            </summary>
            <param name="expression">The root of the expression tree.</param>
            <returns>A new tree with sub-trees evaluated and replaced.</returns>
        </member>
        <member name="T:EntityFramework.Caching.Evaluator.SubtreeEvaluator">
            <summary>
            Evaluates and replaces sub-trees when first candidate is reached (top-down)
            </summary>
        </member>
        <member name="T:EntityFramework.Caching.Evaluator.Nominator">
            <summary>
            Performs bottom-up analysis to determine which nodes can possibly
            be part of an evaluated sub-tree.
            </summary>
        </member>
        <member name="T:EntityFramework.Caching.LocalCollectionExpander">
            <summary>
            Enables cache key support for local collection values.
            </summary>
        </member>
        <member name="T:EntityFramework.Caching.LocalCollectionExpander.Printer`1">
            <summary>
            Overrides ToString to print each element of a collection.
            </summary>
            <remarks>
            Inherits List in order to support List.Contains instance method as well
            as standard Enumerable.Contains/Any extension methods.
            </remarks>
        </member>
        <member name="T:EntityFramework.Caching.QueryCache">
            <summary>
            Extension methods for query cache.
            </summary>
            <remarks>
            Copyright (c) 2010 Pete Montgomery.
            http://petemontgomery.wordpress.com
            Licenced under GNU LGPL v3.
            http://www.gnu.org/licenses/lgpl.html
            </remarks>
        </member>
        <member name="M:EntityFramework.Caching.QueryCache.GetCacheKey(System.Linq.IQueryable)">
            <summary>
            Gets a cache key for the specified <see cref="T:System.Linq.IQueryable"/>.
            </summary>
            <param name="query">The query to get the key from.</param>
            <returns>A unique key for the specified <see cref="T:System.Linq.IQueryable"/></returns>
        </member>
        <member name="T:EntityFramework.Future.IFutureRunner">
            <summary>
            A interface encapsulating the execution of future queries.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.IFutureRunner.ExecuteFutureQueries(System.Data.Entity.Core.Objects.ObjectContext,System.Collections.Generic.IList{EntityFramework.Future.IFutureQuery})">
            <summary>
            Executes the future queries.
            </summary>
            <param name="context">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to run the queries against.</param>
            <param name="futureQueries">The future queries list.</param>
        </member>
        <member name="T:EntityFramework.IQueryUnwrapper">
            <summary>
            A generic <see langword="interface"/> for unwrapping an underlying queries.
            </summary>
        </member>
        <member name="M:EntityFramework.IQueryUnwrapper.Unwrap``1(System.Linq.IQueryable{``0})">
            <summary>
            Unwrap the specified <paramref name="query"/>.
            </summary>
            <typeparam name="TEntity">the entity type</typeparam>
            <param name="query">the query to unwrap</param>
            <returns>The unwrapped query</returns>
        </member>
        <member name="M:EntityFramework.IQueryUnwrapper.Unwrap(System.Linq.IQueryable)">
            <summary>
            Unwrap the specified <paramref name="query"/>.
            </summary>
            <param name="query">the query to unwrap</param>
            <returns>The unwrapped query</returns>
        </member>
        <member name="T:EntityFramework.Locator">
            <summary>
            Service locator pattern.
            </summary>
            <example>
            Replace cache provider with Memcached provider
            <code><![CDATA[
            Locator.Current.Register<ICacheProvider>(() => new MemcachedProvider());
            ]]>
            </code>
            Replace the built in <see cref="T:EntityFramework.IContainer"/> with other Locator framework.
            <code><![CDATA[
            var container = new OtherContainer();
            // register all default providers
            Locator.RegisterDefaults(container);
            // overide cache provider
            container.Register<ICacheProvider>(() => new MemcachedProvider());
            // make Locator use new custom container
            Locator.SetContainer(container);
            ]]>
            </code>
            </example>
        </member>
        <member name="M:EntityFramework.Locator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Locator"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Locator.SetContainer(EntityFramework.IContainer)">
            <summary>
            Sets the <see cref="P:EntityFramework.Locator.Current"/> <see cref="T:EntityFramework.IContainer"/>.
            </summary>
            <param name="container">The <see cref="T:EntityFramework.IContainer"/> to set.</param>
        </member>
        <member name="M:EntityFramework.Locator.SetInnerContainer(EntityFramework.IContainer)">
            <summary>
            Sets the <see cref="T:EntityFramework.IContainer"/> for this instance.
            </summary>
            <param name="container">The <see cref="T:EntityFramework.IContainer"/> for this instance.</param>
        </member>
        <member name="M:EntityFramework.Locator.RegisterDefaults(EntityFramework.IContainer)">
            <summary>
            Registers the default service provider resolvers.
            </summary>
            <param name="container">The <see cref="T:EntityFramework.IContainer"/> to register the default service resolvers with.</param>
        </member>
        <member name="P:EntityFramework.Locator.Current">
            <summary>
            Gets the current Locator <see cref="T:EntityFramework.IContainer"/>.
            </summary>
        </member>
        <member name="P:EntityFramework.Locator.Container">
            <summary>
            Gets the <see cref="T:EntityFramework.IContainer"/> for this instance.
            </summary>
        </member>
        <member name="T:EntityFramework.Container">
            <summary>
            The default <see cref="T:EntityFramework.IContainer"/> for resolving dependencies.
            </summary>
        </member>
        <member name="T:EntityFramework.IContainer">
            <summary>
            An <see langword="interface"/> for a simple implementation of inversion of control.
            </summary>
        </member>
        <member name="M:EntityFramework.IContainer.Register``1(System.Func{``0})">
            <summary>
            Register the specified <paramref name="factory"/> for resolving <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <param name="factory">The factory <see langword="delegate"/> for resolving.</param>
        </member>
        <member name="M:EntityFramework.IContainer.Resolve``1">
            <summary>
            Resolves an instance for the specified <typeparamref name="TService"/> type.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <returns>A resolved instance of <typeparamref name="TService"/>.</returns>
        </member>
        <member name="M:EntityFramework.Container.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Container"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Container.Resolve``1">
            <summary>
            Resolves an instance for the specified <typeparamref name="TService"/> type.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <returns>
            A resolved instance of <typeparamref name="TService"/>.
            </returns>
        </member>
        <member name="M:EntityFramework.Container.Register``1(System.Func{``0})">
            <summary>
            Register the specified <paramref name="factory"/> for resolving <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <param name="factory">The factory <see langword="delegate"/> for resolving.</param>
        </member>
        <member name="M:EntityFramework.Container.Register``1(System.Func{EntityFramework.IContainer,``0})">
            <summary>
            Register the specified <paramref name="factory"/> for resolving <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <param name="factory">The factory <see langword="delegate"/> for resolving.</param>
        </member>
        <member name="M:EntityFramework.Container.Register``2(System.Func{``1,``0})">
            <summary>
            Register the specified <paramref name="factory"/> for resolving <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <typeparam name="TArg">The type of the constructor argument.</typeparam>
            <param name="factory">The factory <see langword="delegate"/> for resolving.</param>
        </member>
        <member name="M:EntityFramework.Container.Register``3(System.Func{``1,``2,``0})">
            <summary>
            Register the specified <paramref name="factory"/> for resolving <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <typeparam name="TArg1">The type of the first constructor argument.</typeparam>
            <typeparam name="TArg2">The type of the second constructor argument.</typeparam>
            <param name="factory">The factory <see langword="delegate"/> for resolving.</param>
        </member>
        <member name="M:EntityFramework.Container.Register``4(System.Func{``1,``2,``3,``0})">
            <summary>
            Register the specified <paramref name="factory"/> for resolving <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <typeparam name="TArg1">The type of the first constructor argument.</typeparam>
            <typeparam name="TArg2">The type of the second constructor argument.</typeparam>
            <typeparam name="TArg3">The type of the third constructor argument.</typeparam>
            <param name="factory">The factory <see langword="delegate"/> for resolving.</param>
        </member>
        <member name="M:EntityFramework.Container.Register``5(System.Func{``1,``2,``3,``4,``0})">
            <summary>
            Register the specified <paramref name="factory"/> for resolving <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The type of the service.</typeparam>
            <typeparam name="TArg1">The type of the first constructor argument.</typeparam>
            <typeparam name="TArg2">The type of the second constructor argument.</typeparam>
            <typeparam name="TArg3">The type of the third constructor argument.</typeparam>
            <typeparam name="TArg4">The type of the fourth constructor argument.</typeparam>
            <param name="factory">The factory <see langword="delegate"/> for resolving.</param>
        </member>
        <member name="T:EntityFramework.Extensions.AuditExtensions">
            <summary>
            An extension class for <see cref="T:EntityFramework.Audit.AuditLog"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Extensions.AuditExtensions.BeginAudit(System.Data.Entity.Core.Objects.ObjectContext,EntityFramework.Audit.AuditConfiguration)">
            <summary>
            Begin audit logging by attaching an <see cref="T:EntityFramework.Audit.AuditLogger"/> to the <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/>.
            </summary>
            <param name="objectContext">The ObjectContext to create the AuditLog from.</param>
            <param name="configuration">The AuditConfiguration to use when creating the AuditLog.</param>
            <returns>An Auditlogger instance to create an AuditLog from.</returns>
        </member>
        <member name="M:EntityFramework.Extensions.AuditExtensions.BeginAudit(System.Data.Entity.DbContext,EntityFramework.Audit.AuditConfiguration)">
            <summary>
            Begin audit logging by attaching an <see cref="T:EntityFramework.Audit.AuditLogger"/> to the <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/>.
            </summary>
            <param name="dbContext">The DbContext to create the AuditLog from.</param>
            <param name="configuration">The AuditConfiguration to use when creating the AuditLog.</param>
            <returns>An Auditlogger instance to create an AuditLog from.</returns>
        </member>
        <member name="T:EntityFramework.Audit.AuditProperty">
            <summary>
            A class for logging the changes to a property on an entity.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditProperty.Name">
            <summary>
            Gets or sets the name of the property.
            </summary>
            <value>The name of the property.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditProperty.Type">
            <summary>
            Gets or sets the type of the property.
            </summary>
            <value>The type of the property.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditProperty.IsRelationship">
            <summary>
            Gets or sets a value indicating whether this property is an association.
            </summary>
            <value>
            	<c>true</c> if this property is an association; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:EntityFramework.Audit.AuditProperty.ForeignKey">
            <summary>
            Gets or sets the property names that this association maps to.
            </summary>
            <value>The property names that this association maps to..</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditProperty.Current">
            <summary>
            Gets or sets the current/changed value of the property.
            </summary>
            <value>The current value of the property.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditProperty.Original">
            <summary>
            Gets or sets the original value of the property.
            </summary>
            <value>The original value of the property.</value>
        </member>
        <member name="T:EntityFramework.Audit.AuditPropertyCollection">
            <summary>
            A keyed collection of <see cref="T:EntityFramework.Audit.AuditProperty"/>
            </summary>
        </member>
        <member name="M:EntityFramework.Audit.AuditPropertyCollection.GetKeyForItem(EntityFramework.Audit.AuditProperty)">
            <summary>
            Extracts the key from the specified element.
            </summary>
            <param name="item">The element from which to extract the key.</param>
            <returns>The key for the specified element.</returns>
        </member>
        <member name="T:EntityFramework.Audit.AuditPropertyFormatAttribute">
            <summary>
            An attribute to control the output format of the <see cref="T:EntityFramework.Audit.AuditProperty"/> values.
            </summary>
            <example>The following example is used to mask out the password for use in the audit log.
            <code><![CDATA[
            [Audit]
            public partial class User
            {
                [AuditPropertyFormat(typeof(CustomFormat), "FormatPassword")]
                public string Password { get; set; }
            }
            
            public class CustomFormat
            {
                // signature can be static object MethodName(AuditPropertyContext).
                public static object FormatPassword(AuditPropertyContext auditProperty)
                {
                    string v = auditProperty.Value as string;
                    if (string.IsNullOrEmpty(v))
                        return value;
            
                    return v.Substring(0, 1) + "*****";
                }
            }
            ]]>
            </code>
            </example>
            <remarks>
            The method signature must be <c>static object MethodName(AuditPropertyContext auditProperty)</c>.
            </remarks>
        </member>
        <member name="M:EntityFramework.Audit.AuditPropertyFormatAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Audit.AuditPropertyFormatAttribute"/> class.
            </summary>
            <param name="formatType">The <see cref="T:System.Type"/> that contains format method.</param>
            <param name="methodName">
            The name of the method to call to format the value.  Method signature can be either 
            <c>static object MethodName(MemberInfo memberInfo, object value)</c> or <c>static object MethodName(object value)</c>.
            </param>
        </member>
        <member name="P:EntityFramework.Audit.AuditPropertyFormatAttribute.FormatType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> that contains format method.
            </summary>
            <value>The <see cref="T:System.Type"/> that contains format method.</value>
        </member>
        <member name="P:EntityFramework.Audit.AuditPropertyFormatAttribute.MethodName">
            <summary>
            Gets or sets the name of the method to call to format the value. Must be a static method.
            </summary>
            <value>The name of the method to call to format the value.</value>
            <remarks>
            The method signature must be <c>static object MethodName(AuditPropertyContext auditProperty)</c>.
            </remarks>
        </member>
        <member name="T:EntityFramework.Audit.AuditPropertyContext">
            <summary>
            The audit property context.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditPropertyContext.Entity">
            <summary>
            Gets or sets the entity.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditPropertyContext.Value">
            <summary>
            Gets or sets the value.
            </summary>
        </member>
        <member name="P:EntityFramework.Audit.AuditPropertyContext.ValueType">
            <summary>
            Gets or sets the type of the value.
            </summary>
        </member>
        <member name="T:EntityFramework.Audit.NotAuditedAttribute">
            <summary>
            Indicates that a field in an audited class should not be included in the audit log.
            </summary>
            <remarks>
            Use the NotAuditedAttribute attribute to prevent a field from being included in the audit.
            </remarks>
            <seealso cref="T:EntityFramework.Audit.AuditAttribute"/>
            <seealso cref="T:EntityFramework.Audit.AlwaysAuditAttribute"/>
        </member>
        <member name="T:EntityFramework.Extensions.CacheExtensions">
            <summary>
            Extension methods for query cache.
            </summary>
        </member>
        <member name="M:EntityFramework.Extensions.CacheExtensions.FromCache``1(System.Linq.IQueryable{``0},EntityFramework.Caching.CachePolicy,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Returns the result of the <paramref name="query"/>; if possible from the cache,
            otherwise the query is materialized and the result cached before being returned.
            </summary>
            <typeparam name="TEntity">The type of the data in the data source.</typeparam>
            <param name="query">The query to be materialized.</param>
            <param name="cachePolicy">The cache policy for the query.</param>
            <param name="tags">The list of tags to use for cache expiration.</param>
            <returns>
            The result of the query.
            </returns>
        </member>
        <member name="M:EntityFramework.Extensions.CacheExtensions.FromCacheAsync``1(System.Linq.IQueryable{``0},EntityFramework.Caching.CachePolicy,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Returns the result of the <paramref name="query"/>; if possible from the cache,
            otherwise the query is materialized asynchronously and the result cached before being returned.
            </summary>
            <typeparam name="TEntity">The type of the data in the data source.</typeparam>
            <param name="query">The query to be materialized.</param>
            <param name="cachePolicy">The cache policy for the query.</param>
            <param name="tags">The list of tags to use for cache expiration.</param>
            <returns>
            The result of the query.
            </returns>
        </member>
        <member name="M:EntityFramework.Extensions.CacheExtensions.FromCacheFirstOrDefault``1(System.Linq.IQueryable{``0},EntityFramework.Caching.CachePolicy,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Returns the first element of the <paramref name="query"/>; if possible from the cache,
            otherwise the query is materialized and the result cached before being returned.
            </summary>
            <typeparam name="TEntity">The type of the data in the data source.</typeparam>
            <param name="query">The query to be materialized.</param>
            <param name="cachePolicy">The cache policy for the query.</param>
            <param name="tags">The list of tags to use for cache expiration.</param>
            <returns>default(T) if source is empty; otherwise, the first element in source.</returns>
        </member>
        <member name="M:EntityFramework.Extensions.CacheExtensions.FromCacheFirstOrDefaultAsync``1(System.Linq.IQueryable{``0},EntityFramework.Caching.CachePolicy,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Returns the first element of the <paramref name="query"/>; if possible from the cache,
            otherwise the query is materialized asynchronously and the result cached before being returned.
            </summary>
            <typeparam name="TEntity">The type of the data in the data source.</typeparam>
            <param name="query">The query to be materialized.</param>
            <param name="cachePolicy">The cache policy for the query.</param>
            <param name="tags">The list of tags to use for cache expiration.</param>
            <returns>default(T) if source is empty; otherwise, the first element in source.</returns>
        </member>
        <member name="M:EntityFramework.Extensions.CacheExtensions.RemoveCache``1(System.Linq.IQueryable{``0})">
            <summary>
            Removes the cached query from cache.
            </summary>
            <typeparam name="TEntity">The type of the data in the data source.</typeparam>
            <param name="query">The query to be materialized.</param>
            <returns>
            The original <paramref name="query"/> for fluent chaining.
            </returns>
        </member>
        <member name="M:EntityFramework.Extensions.CacheExtensions.RemoveCache``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0}@)">
            <summary>
            Removes the cached query from cache.
            </summary>
            <typeparam name="TEntity">The type of the data in the data source.</typeparam>
            <param name="query">The query to be materialized.</param>
            <param name="removed">The removed items for cache.</param>
            <returns>
            The original <paramref name="query"/> for fluent chaining.
            </returns>
        </member>
        <member name="T:EntityFramework.Extensions.DataRecordExtensions">
            <summary>
            Extension methods for IDataRecord.
            </summary>
        </member>
        <member name="M:EntityFramework.Extensions.DataRecordExtensions.GetValue(System.Data.IDataRecord,System.String)">
            <summary>
            Gets the value of the specified field name.
            </summary>
            <param name="record">The IDataRecord to read.</param>
            <param name="name">The field name.</param>
            <returns></returns>
        </member>
        <member name="T:EntityFramework.Extensions.BatchExtensions">
            <summary>
            An extensions class for batch queries.
            </summary>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.Delete``1(System.Linq.IQueryable{``0},System.Linq.IQueryable{``0})">
            <summary>
            The API was refactored to no longer need this extension method. Use query.Where(expression).Delete() syntax instead.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The source used to determine the table to delete from.</param>
            <param name="query">The IQueryable used to generate the where clause for the delete statement.</param>
            <returns>The number of row deleted.</returns>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.Delete``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Executes a delete statement using an expression to filter the rows to be deleted.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The source used to determine the table to delete from.</param>
            <param name="filterExpression">The filter expression used to generate the where clause for the delete statement.</param>
            <returns>The number of row deleted.</returns>
            <example>Delete all users with email domain @test.com.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users.Delete(u => u.Email.EndsWith(emailDomain));
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.Delete``1(System.Linq.IQueryable{``0})">
            <summary>
            Executes a delete statement using the query to filter the rows to be deleted.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The <see cref="T:IQueryable`1"/> used to generate the where clause for the delete statement.</param>
            <returns>The number of row deleted.</returns>
            <example>Delete all users with email domain @test.com.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users.Where(u => u.Email.EndsWith(emailDomain)).Delete();
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.DeleteAsync``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Executes a delete statement asynchronously using an expression to filter the rows to be deleted.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The source used to determine the table to delete from.</param>
            <param name="filterExpression">The filter expression used to generate the where clause for the delete statement.</param>
            <returns>The number of row deleted.</returns>
            <example>Delete all users with email domain @test.com.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users.Delete(u => u.Email.EndsWith(emailDomain));
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.DeleteAsync``1(System.Linq.IQueryable{``0})">
            <summary>
            Executes a delete statement asynchronously using the query to filter the rows to be deleted.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The <see cref="T:IQueryable`1"/> used to generate the where clause for the delete statement.</param>
            <returns>The number of row deleted.</returns>
            <example>Delete all users with email domain @test.com.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users.Where(u => u.Email.EndsWith(emailDomain)).Delete();
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.Update``1(System.Linq.IQueryable{``0},System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            The API was refactored to no longer need this extension method. Use query.Where(expression).Update(updateExpression) syntax instead.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The source used to determine the table to update.</param>
            <param name="query">The query used to generate the where clause.</param>
            <param name="updateExpression">The MemberInitExpression used to indicate what is updated.</param>
            <returns>The number of row updated.</returns>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.Update``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Executes an update statement using an expression to filter the rows that are updated.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The source used to determine the table to update.</param>
            <param name="filterExpression">The filter expression used to generate the where clause.</param>
            <param name="updateExpression">The <see cref="T:System.Linq.Expressions.MemberInitExpression"/> used to indicate what is updated.</param>
            <returns>The number of row updated.</returns>
            <example>Update all users in the test.com domain to be inactive.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users.Update(
              u => u.Email.EndsWith(emailDomain),
              u => new User { IsApproved = false, LastActivityDate = DateTime.Now });
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.Update``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Executes an update statement using the query to filter the rows to be updated.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The query used to generate the where clause.</param>
            <param name="updateExpression">The <see cref="T:System.Linq.Expressions.MemberInitExpression"/> used to indicate what is updated.</param>
            <returns>The number of row updated.</returns>
            <example>Update all users in the test.com domain to be inactive.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users
              .Where(u => u.Email.EndsWith(emailDomain))
              .Update(u => new User { IsApproved = false, LastActivityDate = DateTime.Now });
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.UpdateAsync``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Executes an update statement asynchronously using an expression to filter the rows that are updated.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The source used to determine the table to update.</param>
            <param name="filterExpression">The filter expression used to generate the where clause.</param>
            <param name="updateExpression">The <see cref="T:System.Linq.Expressions.MemberInitExpression"/> used to indicate what is updated.</param>
            <returns>The number of row updated.</returns>
            <example>Update all users in the test.com domain to be inactive.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users.Update(
              u => u.Email.EndsWith(emailDomain),
              u => new User { IsApproved = false, LastActivityDate = DateTime.Now });
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="M:EntityFramework.Extensions.BatchExtensions.UpdateAsync``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            Executes an update statement asynchronously using the query to filter the rows to be updated.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="source">The query used to generate the where clause.</param>
            <param name="updateExpression">The <see cref="T:System.Linq.Expressions.MemberInitExpression"/> used to indicate what is updated.</param>
            <returns>The number of row updated.</returns>
            <example>Update all users in the test.com domain to be inactive.
            <code><![CDATA[
            var db = new TrackerContext();
            string emailDomain = "@test.com";
            int count = db.Users
              .Where(u => u.Email.EndsWith(emailDomain))
              .Update(u => new User { IsApproved = false, LastActivityDate = DateTime.Now });
            ]]></code>
            </example>
            <remarks>
            When executing this method, the statement is immediately executed on the database provider
            and is not part of the change tracking system.  Also, changes will not be reflected on 
            any entities that have already been materialized in the current context.        
            </remarks>
        </member>
        <member name="T:EntityFramework.Extensions.ObjectContextExtensions">
            <summary>
            Extensions methods for <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> and <see cref="T:System.Data.Entity.DbContext"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Extensions.ObjectContextExtensions.BeginTransaction(System.Data.Entity.Core.Objects.ObjectContext,System.Data.IsolationLevel)">
            <summary>
            Starts a database transaction from the database provider connection.
            </summary>
            <param name="context">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to get the database connection from.</param>
            <param name="isolationLevel">Specifies the isolation level for the transaction.</param>
            <returns>
            An object representing the new transaction.
            </returns>
        </member>
        <member name="M:EntityFramework.Extensions.ObjectContextExtensions.BeginTransaction(System.Data.Entity.DbContext,System.Data.IsolationLevel)">
            <summary>
            Starts a database transaction from the database provider connection.
            </summary>
            <param name="context">The <see cref="T:System.Data.Entity.DbContext"/> to get the database connection from.</param>
            <param name="isolationLevel">Specifies the isolation level for the transaction.</param>
            <returns>
            An object representing the new transaction.
            </returns>
        </member>
        <member name="T:EntityFramework.Mapping.EntityMap">
            <summary>
            A class that defines entity mapping.
            </summary>
        </member>
        <member name="M:EntityFramework.Mapping.EntityMap.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Mapping.EntityMap"/> class.
            </summary>
            <param name="entityType">Type of the entity.</param>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.ModelSet">
            <summary>
            Gets or sets the conceptual model EntitySet.
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.StoreSet">
            <summary>
            Gets or sets the store model EntitySet.
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.ModelType">
            <summary>
            Gets or sets the conceptual model EntityType.
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.StoreType">
            <summary>
            Gets or sets the store model EntityType.
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.EntityType">
            <summary>
            Gets the type of the entity.
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.TableName">
            <summary>
            Gets or sets the name of the table.
            </summary>
            <value>
            The name of the table.
            </value>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.PropertyMaps">
            <summary>
            Gets the property maps.
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.EntityMap.KeyMaps">
            <summary>
            Gets the key maps.
            </summary>
        </member>
        <member name="T:EntityFramework.Extensions.ObjectQueryExtensions">
            <summary>
            Extension methods for for ObjectQuery.
            </summary>
        </member>
        <member name="M:EntityFramework.Extensions.ObjectQueryExtensions.ToObjectQuery``1(System.Linq.IQueryable{``0})">
            <summary>
            Convert the query into an ObjectQuery.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="query">The query to convert.</param>
            <returns>The converted ObjectQuery; otherwise null if it can't be converted.</returns>
        </member>
        <member name="M:EntityFramework.Extensions.ObjectQueryExtensions.ToObjectQuery(System.Linq.IQueryable)">
            <summary>
            Convert the query into an ObjectQuery.
            </summary>
            <param name="query">The query to convert.</param>
            <returns>The converted ObjectQuery; otherwise null if it can't be converted.</returns>
        </member>
        <member name="M:EntityFramework.Extensions.ObjectQueryExtensions.CreateQuery(System.Linq.IQueryable,System.Linq.Expressions.Expression,System.Type)">
            <summary>
            Creates an ObjectQuery from an expression.
            </summary>
            <param name="source">The source.</param>
            <param name="expression">The expression.</param>
            <param name="type">The type.</param>
            <returns>An ObjectQuery created from the expression.</returns>
        </member>
        <member name="M:EntityFramework.Extensions.ObjectQueryExtensions.GetContext``1(System.Linq.IQueryable{``0})">
            <summary>
            Gets the ObjectContext for the specifed query.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="query">The query.</param>
            <returns>The ObjectContext for the query.</returns>
        </member>
        <member name="T:EntityFramework.Mapping.IMappingProvider">
            <summary>
            An <see langword="interface"/> defining a provider to get entity mapping data.
            </summary>
        </member>
        <member name="M:EntityFramework.Mapping.IMappingProvider.GetEntityMap``1(System.Data.Entity.Core.Objects.ObjectQuery)">
            <summary>
            Gets the <see cref="T:EntityFramework.Mapping.EntityMap"/> for the specified <typeparamref name="TEntity"/>.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="query">The query to use to help load the mapping data.</param>
            <returns>An <see cref="T:EntityFramework.Mapping.EntityMap"/> with the mapping data.</returns>
        </member>
        <member name="M:EntityFramework.Mapping.IMappingProvider.GetEntityMap(System.Type,System.Data.Entity.DbContext)">
            <summary>
            Gets the <see cref="T:EntityFramework.Mapping.EntityMap"/> for the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of the entity.</param>
            <param name="dbContext">The database context to load metadata from.</param>
            <returns>An <see cref="T:EntityFramework.Mapping.EntityMap"/> with the mapping data.</returns>
        </member>
        <member name="M:EntityFramework.Mapping.IMappingProvider.GetEntityMap(System.Type,System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
            Gets the <see cref="T:EntityFramework.Mapping.EntityMap"/> for the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of the entity.</param>
            <param name="objectContext">The object context to load metadata from.</param>
            <returns>An <see cref="T:EntityFramework.Mapping.EntityMap"/> with the mapping data.</returns>
        </member>
        <member name="T:EntityFramework.Mapping.MetadataMappingProvider">
            <summary>
                Use <see cref="T:System.Data.Entity.Core.Metadata.Edm.MetadataWorkspace"/> to resolve mapping information.
            </summary>
        </member>
        <member name="M:EntityFramework.Mapping.MetadataMappingProvider.GetEntityMap``1(System.Data.Entity.Core.Objects.ObjectQuery)">
            <summary>
                Gets the <see cref="T:EntityFramework.Mapping.EntityMap"/> for the specified <typeparamref name="TEntity"/>.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="query">The query to use to help load the mapping data.</param>
            <returns>
                An <see cref="T:EntityFramework.Mapping.EntityMap"/> with the mapping data.
            </returns>
        </member>
        <member name="M:EntityFramework.Mapping.MetadataMappingProvider.GetEntityMap(System.Type,System.Data.Entity.DbContext)">
            <summary>
                Gets the <see cref="T:EntityFramework.Mapping.EntityMap"/> for the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of the entity.</param>
            <param name="dbContext">The database context to load metadata from.</param>
            <returns>
                An <see cref="T:EntityFramework.Mapping.EntityMap"/> with the mapping data.
            </returns>
        </member>
        <member name="M:EntityFramework.Mapping.MetadataMappingProvider.GetEntityMap(System.Type,System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
                Gets the <see cref="T:EntityFramework.Mapping.EntityMap"/> for the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of the entity.</param>
            <param name="objectContext">The object context to load metadata from.</param>
            <returns>
                An <see cref="T:EntityFramework.Mapping.EntityMap"/> with the mapping data.
            </returns>
        </member>
        <member name="T:EntityFramework.Mapping.MappingResolver">
            <summary>
            Static class to resolve Entity Framework mapping.
            </summary>
        </member>
        <member name="M:EntityFramework.Mapping.MappingResolver.GetEntityMap``1(System.Data.Entity.Core.Objects.ObjectQuery)">
            <summary>
            Gets an <see cref="T:EntityFramework.Mapping.EntityMap"/> for the entity type used in the specified <paramref name="query"/>.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="query">The query used to create the <see cref="T:EntityFramework.Mapping.EntityMap"/> from.</param>
            <returns>An <see cref="T:EntityFramework.Mapping.EntityMap"/> for the specified <paramref name="query"/>.</returns>
        </member>
        <member name="M:EntityFramework.Mapping.MappingResolver.GetEntitySet``1(System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
            Gets the underling <see cref="T:System.Data.Entity.Core.Metadata.Edm.EntitySet"/> for the type of entity.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="objectContext">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> context to get the <see cref="T:System.Data.Entity.Core.Metadata.Edm.EntitySet"/> from.</param>
            <returns>The <see cref="T:System.Data.Entity.Core.Metadata.Edm.EntitySet"/> for the type of entity specified.</returns>
        </member>
        <member name="T:EntityFramework.Mapping.PropertyMap">
            <summary>
            A class representing a property map
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.PropertyMap.PropertyName">
            <summary>
            Gets or sets the name of the property.
            </summary>
        </member>
        <member name="P:EntityFramework.Mapping.PropertyMap.ColumnName">
            <summary>
            Gets or sets the name of the column.
            </summary>
        </member>
        <member name="T:EntityFramework.Future.FutureCount">
            <summary>
            Provides for defering the execution of a count query to a batch of queries.
            </summary>
            <example>The following is an example of how to use FutureCount to page a 
            list and get the total count in one call.
            <code><![CDATA[
            var db = new TrackerDataContext { Log = Console.Out };
            // base query
            var q = db.Task.ByPriority(Priority.Normal).OrderBy(t => t.CreatedDate);
            // get total count
            var q1 = q.FutureCount();
            // get first page
            var q2 = q.Skip(0).Take(10).Future();
            // triggers sql execute as a batch
            var tasks = q2.ToList();
            int total = q1.Value;
            ]]>
            </code>
            </example>
        </member>
        <member name="T:EntityFramework.Future.FutureValue`1">
            <summary>
            Provides for defering the execution of a query to a batch of queries.
            </summary>
            <typeparam name="T">The type for the future query.</typeparam>
            <example>The following is an example of how to use FutureValue.
            <code><![CDATA[
            var db = new TrackeContext;
            // build up queries
            var q1 = db.User.ByEmailAddress("<EMAIL>").FutureValue();
            var q2 = db.Task.Where(t => t.Summary == "Test").Future();
            // this triggers the loading of all the future queries
            User user = q1.Value;
            var tasks = q2.ToList();
            ]]>
            </code>
            </example>
        </member>
        <member name="T:EntityFramework.Future.FutureQueryBase`1">
            <summary>
            Base class for future queries.
            </summary>
            <typeparam name="T">The type for the future query.</typeparam>
        </member>
        <member name="T:EntityFramework.Future.IFutureQuery">
            <summary>
            Interface for defering the future execution of a batch of queries.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.IFutureQuery.GetPlan(System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
            Gets the data command for this query.
            </summary>
            <param name="dataContext">The data context to get the plan from.</param>
            <returns>The requested FuturePlan object.</returns>
        </member>
        <member name="M:EntityFramework.Future.IFutureQuery.SetResult(System.Data.Entity.Core.Objects.ObjectContext,System.Data.Common.DbDataReader)">
            <summary>
            Sets the underling value after the query has been executed.
            </summary>
            <param name="dataContext">The data context to translate the results with.</param>
            <param name="reader">The <see cref="T:System.Data.Common.DbDataReader"/> to get the result from.</param>
        </member>
        <member name="P:EntityFramework.Future.IFutureQuery.IsLoaded">
            <summary>
            Gets a value indicating whether this instance is loaded.
            </summary>
            <value><c>true</c> if this instance is loaded; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:EntityFramework.Future.IFutureQuery.Query">
            <summary>
            Gets the query source to use when materializing.
            </summary>
            <value>The query source to use when materializing.</value>
        </member>
        <member name="M:EntityFramework.Future.FutureQueryBase`1.#ctor(System.Linq.IQueryable,System.Action)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureQuery`1"/> class.
            </summary>
            <param name="query">The query source to use when materializing.</param>
            <param name="loadAction">The action to execute when the query is accessed.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureQueryBase`1.GetResult">
            <summary>
            Gets the result by invoking the <see cref="P:EntityFramework.Future.FutureQueryBase`1.LoadAction"/> if not already loaded.
            </summary>
            <returns>
            An <see cref="T:System.Collections.Generic.IEnumerable`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:EntityFramework.Future.FutureQueryBase`1.EntityFramework#Future#IFutureQuery#GetPlan(System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
            Gets the data command for this query.
            </summary>
            <param name="dataContext">The data context to get the command from.</param>
            <returns>The requested command object.</returns>
        </member>
        <member name="M:EntityFramework.Future.FutureQueryBase`1.GetPlan(System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
            Gets the data command for this query.
            </summary>
            <param name="dataContext">The data context to get the command from.</param>
            <returns>The requested command object.</returns>
        </member>
        <member name="M:EntityFramework.Future.FutureQueryBase`1.EntityFramework#Future#IFutureQuery#SetResult(System.Data.Entity.Core.Objects.ObjectContext,System.Data.Common.DbDataReader)">
            <summary>
            Sets the underling value after the query has been executed.
            </summary>
            <param name="dataContext">The data context to translate the results with.</param>
            <param name="reader">The <see cref="T:System.Data.Common.DbDataReader"/> to get the result from.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureQueryBase`1.SetResult(System.Data.Entity.Core.Objects.ObjectContext,System.Data.Common.DbDataReader)">
            <summary>
            Sets the underling value after the query has been executed.
            </summary>
            <param name="dataContext">The data context to translate the results with.</param>
            <param name="reader">The <see cref="T:System.Data.Common.DbDataReader"/> to get the result from.</param>
        </member>
        <member name="P:EntityFramework.Future.FutureQueryBase`1.LoadAction">
            <summary>
            Gets the action to execute when the query is accessed.
            </summary>
            <value>The load action.</value>
        </member>
        <member name="P:EntityFramework.Future.FutureQueryBase`1.IsLoaded">
            <summary>
            Gets a value indicating whether this instance is loaded.
            </summary>
            <value><c>true</c> if this instance is loaded; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:EntityFramework.Future.FutureQueryBase`1.Exception">
            <summary>
            Gets or sets the query execute exception. 
            </summary>
            <value>The query execute exception.</value>      
        </member>
        <member name="P:EntityFramework.Future.FutureQueryBase`1.EntityFramework#Future#IFutureQuery#Query">
            <summary>
            Gets the query source to use when materializing.
            </summary>
            <value>The query source to use when materializing.</value>
        </member>
        <member name="M:EntityFramework.Future.FutureValue`1.#ctor(System.Linq.IQueryable,System.Action)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureValue`1"/> class.
            </summary>
            <param name="query">The query source to use when materializing.</param>
            <param name="loadAction">The action to execute when the query is accessed.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureValue`1.#ctor(`0)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureValue`1"/> class.
            </summary>
            <param name="underlyingValue">The underlying value.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureValue`1.op_Implicit(EntityFramework.Future.FutureValue{`0})~`0">
            <summary>
            Performs an implicit conversion from <see cref="T:EntityFramework.Future.FutureValue`1" /> to T.
            </summary>
            <param name="futureValue">The future value.</param>
            <returns>
            The result of forcing this lazy value.
            </returns>
        </member>
        <member name="P:EntityFramework.Future.FutureValue`1.Value">
            <summary>
            Gets or sets the value assigned to or loaded by the query.
            </summary>
            <returns>
            The value of this deferred property.
            </returns>
        </member>
        <member name="P:EntityFramework.Future.FutureValue`1.UnderlyingValue">
            <summary>
            Gets the underling value. This property will not trigger the loading of the future query.
            </summary>
            <value>The underling value.</value>
        </member>
        <member name="M:EntityFramework.Future.FutureCount.#ctor(System.Linq.IQueryable,System.Action)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureCount"/> class.
            </summary>
            <param name="query">The query source to use when materializing.</param>
            <param name="loadAction">The action to execute when the query is accessed.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureCount.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureCount"/> class.
            </summary>
            <param name="underlyingValue">The underlying value.</param>
        </member>
        <member name="T:EntityFramework.Future.FutureException">
            <summary>
            An exception during future query execution.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.FutureException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureException"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.FutureException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureException"/> class.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
        </member>
        <member name="T:EntityFramework.Extensions.FutureExtensions">
            <summary>
            Extension methods for future queries.
            </summary>
            <seealso cref="T:EntityFramework.Future.FutureQuery`1"/>
            <seealso cref="T:EntityFramework.Future.FutureValue`1"/>
            <seealso cref="T:EntityFramework.Future.FutureCount"/>
        </member>
        <member name="M:EntityFramework.Extensions.FutureExtensions.Future``1(System.Linq.IQueryable{``0})">
            <summary>
            Provides for defering the execution of the <paramref name="source" /> query to a batch of future queries.
            </summary>
            <typeparam name="TEntity">The type of the elements of <paramref name="source" />.</typeparam>
            <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to add to the batch of future queries.</param>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements from the input sequence.</returns>
            <seealso cref="T:EntityFramework.Future.FutureQuery`1"/>
        </member>
        <member name="M:EntityFramework.Extensions.FutureExtensions.FutureCount``1(System.Linq.IQueryable{``0})">
            <summary>
            Provides for defering the execution of the <paramref name="source"/> query to a batch of future queries.
            </summary>
            <typeparam name="TEntity">The type of the elements of <paramref name="source"/>.</typeparam>
            <param name="source">An <see cref="T:System.Linq.IQueryable`1"/> to add to the batch of future queries.</param>
            <returns>An instance of <see cref="M:EntityFramework.Extensions.FutureExtensions.FutureCount``1(System.Linq.IQueryable{``0})"/> that contains the result of the query.</returns>
            <seealso cref="T:EntityFramework.Future.FutureCount"/>
        </member>
        <member name="M:EntityFramework.Extensions.FutureExtensions.FutureValue``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{System.Linq.IQueryable{``0},``1}})">
            <summary>
            Provides for defering the execution of the <paramref name="source" /> query to a batch of future queries.
            </summary>
            <typeparam name="TEntity">The type of the elements of <paramref name="source" />.</typeparam>
            <typeparam name="TResult">The type of the result value wrapped in a <see cref="T:EntityFramework.Future.FutureValue`1"/>.</typeparam>
            <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to add to the batch of future queries.</param>
            <param name="selector">A lambda expression with one of the Min, Max, Count, Sum, Average aggregate functions</param>
            <returns>An instance of <see cref="T:EntityFramework.Future.FutureValue`1" /> that contains the result of the query</returns>
        </member>
        <member name="M:EntityFramework.Extensions.FutureExtensions.FutureFirstOrDefault``1(System.Linq.IQueryable{``0})">
            <summary>
            Provides for defering the execution of the <paramref name="source" /> query to a batch of future queries.
            </summary>
            <typeparam name="TEntity">The type of the elements of <paramref name="source" />.</typeparam>
            <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to add to the batch of future queries.</param>
            <returns>An instance of <see cref="T:EntityFramework.Future.FutureValue`1"/> that contains the result of the query.</returns>
            <seealso cref="T:EntityFramework.Future.FutureValue`1"/>
        </member>
        <member name="T:EntityFramework.Future.FuturePlan">
            <summary>
            The command plan for a future query.
            </summary>
        </member>
        <member name="P:EntityFramework.Future.FuturePlan.CommandText">
            <summary>
            Gets or sets the command text.
            </summary>
            <value>
            The command text.
            </value>
        </member>
        <member name="P:EntityFramework.Future.FuturePlan.Parameters">
            <summary>
            Gets or sets the parameters.
            </summary>
            <value>
            The parameters.
            </value>
        </member>
        <member name="T:EntityFramework.Future.FutureQuery`1">
            <summary>
            Provides for defering the execution to a batch of queries.
            </summary>
            <typeparam name="T">The type for the future query.</typeparam>
            <example>The following is an example of how to use FutureQuery.
            <code><![CDATA[
            var db = new TrackerDataContext { Log = Console.Out };
            // build up queries
            var q1 = db.User.ByEmailAddress("<EMAIL>").Future();
            var q2 = db.Task.Where(t => t.Summary == "Test").Future();
            // this triggers the loading of all the future queries as a batch
            var users = q1.ToList();
            var tasks = q2.ToList();
            ]]>
            </code>
            </example>    
        </member>
        <member name="M:EntityFramework.Future.FutureQuery`1.#ctor(System.Linq.IQueryable,System.Action)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureQuery`1"/> class.
            </summary>
            <param name="query">The query source to use when materializing.</param>
            <param name="loadAction">The action to execute when the query is accessed.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureQuery`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:EntityFramework.Future.FutureQuery`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="T:EntityFramework.Future.FutureRunner">
            <summary>
            A class encapsulating the execution of future queries.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.FutureRunner.ExecuteFutureQueries(System.Data.Entity.Core.Objects.ObjectContext,System.Collections.Generic.IList{EntityFramework.Future.IFutureQuery})">
            <summary>
            Executes the future queries.
            </summary>
            <param name="context">The <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/> to run the queries against.</param>
            <param name="futureQueries">The future queries list.</param>
        </member>
        <member name="T:EntityFramework.Future.FutureStore">
            <summary>
            A class to store the collection of <see cref="T:EntityFramework.Future.FutureContext"/> class that are linked to an <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.FutureStore.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureStore"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.FutureStore.GetOrCreate(System.Data.Entity.Core.Objects.ObjectQuery)">
            <summary>
            Gets or create the future context that is linked to the underlying <see cref="T:System.Data.Entity.Core.Objects.ObjectContext"/>.
            </summary>
            <param name="objectQuery">The query source to get the future context from.</param>
            <returns>An instance of <see cref="T:EntityFramework.Future.IFutureContext"/> to store waiting future queries.</returns>
        </member>
        <member name="M:EntityFramework.Future.FutureStore.Cleanup">
            <summary>
            Cleanup values that have been disposed.
            </summary>
        </member>
        <member name="P:EntityFramework.Future.FutureStore.Threshold">
            <summary>
            A percentage value between 0 and 100 inclusive.  When the percentage of Values collected is greater than
            or equal to this percentage then a collection will occur and the underlying structure
            will be shrunk to only valid values.
            </summary>
        </member>
        <member name="P:EntityFramework.Future.FutureStore.Default">
            <summary>
            Gets the current default instance of <see cref="T:EntityFramework.Future.FutureStore"/>.
            </summary>
        </member>
        <member name="T:EntityFramework.Future.IFutureContext">
            <summary>
            An interface defining storage for future queries.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.IFutureContext.ExecuteFutureQueries">
            <summary>
            Executes the future queries.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.IFutureContext.AddQuery(EntityFramework.Future.IFutureQuery)">
            <summary>
            Adds the future query to the waiting queries list on this context.
            </summary>
            <param name="query">The future query.</param>
        </member>
        <member name="P:EntityFramework.Future.IFutureContext.FutureQueries">
            <summary>
            Gets the future queries.
            </summary>
            <value>The future queries.</value>
        </member>
        <member name="T:EntityFramework.Future.FutureContext">
            <summary>
            A class to hold waiting future queries for an <see cref="P:EntityFramework.Future.FutureContext.ObjectContext"/>.
            </summary>
            <remarks>
            This class creates a link between the <see cref="P:EntityFramework.Future.FutureContext.ObjectContext"/> and
            the waiting future queries. Since the ObjectContext could
            be displosed before this class, ObjectContext is stored as
            a <see cref="T:System.WeakReference"/>. 
            </remarks>
        </member>
        <member name="M:EntityFramework.Future.FutureContext.#ctor(System.Data.Entity.Core.Objects.ObjectContext)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Future.FutureContext"/> class.
            </summary>
            <param name="objectContext">The object context for the future queries.</param>
        </member>
        <member name="M:EntityFramework.Future.FutureContext.ExecuteFutureQueries">
            <summary>
            Executes the future queries as a single batch.
            </summary>
        </member>
        <member name="M:EntityFramework.Future.FutureContext.AddQuery(EntityFramework.Future.IFutureQuery)">
            <summary>
            Adds the future query to the waiting queries list on this context.
            </summary>
            <param name="query">The future query.</param>
        </member>
        <member name="P:EntityFramework.Future.FutureContext.FutureQueries">
            <summary>
            Gets the future queries.
            </summary>
            <value>
            The future queries.
            </value>
        </member>
        <member name="P:EntityFramework.Future.FutureContext.ObjectContext">
            <summary>
            Gets the <see cref="P:EntityFramework.Future.FutureContext.ObjectContext"/> for the FutureQueries.
            </summary>
            <remarks>
            ObjectContext is stored as a WeakReference.  The value can be disposed.
            </remarks>
        </member>
        <member name="P:EntityFramework.Future.FutureContext.IsAlive">
            <summary>
            Gets an indication whether the object referenced ObjectContext has been garbage collected.
            </summary>
            <value>
              <c>true</c> if this instance is alive; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:EntityFramework.Reflection.DynamicProxy">
            <summary>
            A class used to wrap an object as a <see cref="T:System.Dynamic.DynamicObject"/> allowing access to internal and private members.
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.DynamicProxy.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Reflection.DynamicProxy"/> class.
            </summary>
            <param name="wrapped">The wrapped object.</param>
        </member>
        <member name="M:EntityFramework.Reflection.DynamicProxy.#ctor(System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Reflection.DynamicProxy"/> class.
            </summary>
            <param name="wrapped">The wrapped object.</param>
            <param name="safeMode">if set to <c>true</c>, return null when name not found.</param>
        </member>
        <member name="M:EntityFramework.Reflection.DynamicProxy.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
            <summary>
            Provides the implementation for operations that invoke a member. Classes derived from the <see cref="T:System.Dynamic.DynamicObject"/> class can override this method to specify dynamic behavior for operations such as calling a method.
            </summary>
            <param name="binder">Provides information about the dynamic operation. The binder.Name property provides the name of the member on which the dynamic operation is performed. For example, for the statement sampleObject.SampleMethod(100), where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject"/> class, binder.Name returns "SampleMethod". The binder.IgnoreCase property specifies whether the member name is case-sensitive.</param>
            <param name="args">The arguments that are passed to the object member during the invoke operation. For example, for the statement sampleObject.SampleMethod(100), where sampleObject is derived from the <see cref="T:System.Dynamic.DynamicObject"/> class, args[0] is equal to 100.</param>
            <param name="result">The result of the member invocation.</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a language-specific run-time exception is thrown.)
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.DynamicProxy.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
            <summary>
            Provides the implementation for operations that get member values. Classes derived from the <see cref="T:System.Dynamic.DynamicObject"/> class can override this method to specify dynamic behavior for operations such as getting a value for a property.
            </summary>
            <param name="binder">Provides information about the object that called the dynamic operation. The binder.Name property provides the name of the member on which the dynamic operation is performed. For example, for the Console.WriteLine(sampleObject.SampleProperty) statement, where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject"/> class, binder.Name returns "SampleProperty". The binder.IgnoreCase property specifies whether the member name is case-sensitive.</param>
            <param name="result">The result of the get operation. For example, if the method is called for a property, you can assign the property value to <paramref name="result"/>.</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a run-time exception is thrown.)
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.DynamicProxy.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
            <summary>
            Provides the implementation for operations that set member values. Classes derived from the <see cref="T:System.Dynamic.DynamicObject"/> class can override this method to specify dynamic behavior for operations such as setting a value for a property.
            </summary>
            <param name="binder">Provides information about the object that called the dynamic operation. The binder.Name property provides the name of the member to which the value is being assigned. For example, for the statement sampleObject.SampleProperty = "Test", where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject"/> class, binder.Name returns "SampleProperty". The binder.IgnoreCase property specifies whether the member name is case-sensitive.</param>
            <param name="value">The value to set to the member. For example, for sampleObject.SampleProperty = "Test", where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject"/> class, the <paramref name="value"/> is "Test".</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a language-specific run-time exception is thrown.)
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.DynamicProxy.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
            <summary>
            Provides implementation for type conversion operations. Classes derived from the <see cref="T:System.Dynamic.DynamicObject"/> class can override this method to specify dynamic behavior for operations that convert an object from one type to another.
            </summary>
            <param name="binder">Provides information about the conversion operation. The binder.Type property provides the type to which the object must be converted. For example, for the statement (String)sampleObject in C# (CType(sampleObject, Type) in Visual Basic), where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject"/> class, binder.Type returns the <see cref="T:System.String"/> type. The binder.Explicit property provides information about the kind of conversion that occurs. It returns true for explicit conversion and false for implicit conversion.</param>
            <param name="result">The result of the type conversion operation.</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a language-specific run-time exception is thrown.)
            </returns>
        </member>
        <member name="P:EntityFramework.Reflection.DynamicProxy.Wrapped">
            <summary>
            Gets the object where access is wrapped from.
            </summary>
        </member>
        <member name="P:EntityFramework.Reflection.DynamicProxy.SafeMode">
            <summary>
            Gets or sets a value indicating whether to return null when name is not found.
            </summary>
            <value>
              <c>true</c> to return null when name not found; otherwise, <c>false</c> to throw an exception.
            </value>
        </member>
        <member name="T:EntityFramework.Reflection.FieldAccessor">
            <summary>
            An accessor class for <see cref="T:System.Reflection.FieldInfo"/>.
            </summary>
        </member>
        <member name="T:EntityFramework.Reflection.MemberAccessor">
            <summary>
            A base class for member accessors.
            </summary>
        </member>
        <member name="T:EntityFramework.Reflection.IMemberAccessor">
            <summary>
            An interface for member accessors.
            </summary>
        </member>
        <member name="T:EntityFramework.Reflection.IMemberInfo">
            <summary>
            An interface defining member information
            </summary>
        </member>
        <member name="P:EntityFramework.Reflection.IMemberInfo.MemberType">
            <summary>
            Gets the type of the member.
            </summary>
            <value>The type of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.IMemberInfo.MemberInfo">
            <summary>
            Gets the member info.
            </summary>
            <value>The member info.</value>
        </member>
        <member name="P:EntityFramework.Reflection.IMemberInfo.Name">
            <summary>
            Gets the name of the member.
            </summary>
            <value>The name of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.IMemberInfo.HasGetter">
            <summary>
            Gets a value indicating whether this member has getter.
            </summary>
            <value>
            	<c>true</c> if this member has getter; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:EntityFramework.Reflection.IMemberInfo.HasSetter">
            <summary>
            Gets a value indicating whether this member has setter.
            </summary>
            <value>
            	<c>true</c> if this member has setter; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:EntityFramework.Reflection.IMemberAccessor.GetValue(System.Object)">
            <summary>
            Returns the value of the member.
            </summary>
            <param name="instance">The object whose member value will be returned.</param>
            <returns>The member value for the instance parameter.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.IMemberAccessor.SetValue(System.Object,System.Object)">
            <summary>
            Sets the value of the member.
            </summary>
            <param name="instance">The object whose member value will be set.</param>
            <param name="value">The new value for this member.</param>
        </member>
        <member name="M:EntityFramework.Reflection.MemberAccessor.GetValue(System.Object)">
            <summary>
            Returns the value of the member.
            </summary>
            <param name="instance">The object whose member value will be returned.</param>
            <returns>
            The member value for the instance parameter.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.MemberAccessor.SetValue(System.Object,System.Object)">
            <summary>
            Sets the value of the member.
            </summary>
            <param name="instance">The object whose member value will be set.</param>
            <param name="value">The new value for this member.</param>
        </member>
        <member name="M:EntityFramework.Reflection.MemberAccessor.Equals(EntityFramework.Reflection.IMemberAccessor)">
            <summary>
            Determines whether the specified <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.MemberAccessor.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.MemberAccessor.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="P:EntityFramework.Reflection.MemberAccessor.MemberType">
            <summary>
            Gets the type of the member.
            </summary>
            <value>The type of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.MemberAccessor.MemberInfo">
            <summary>
            Gets the member info.
            </summary>
            <value>The member info.</value>
        </member>
        <member name="P:EntityFramework.Reflection.MemberAccessor.Name">
            <summary>
            Gets the name of the member.
            </summary>
            <value>The name of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.MemberAccessor.HasGetter">
            <summary>
            Gets a value indicating whether this member has getter.
            </summary>
            <value><c>true</c> if this member has getter; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:EntityFramework.Reflection.MemberAccessor.HasSetter">
            <summary>
            Gets a value indicating whether this member has setter.
            </summary>
            <value><c>true</c> if this member has setter; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:EntityFramework.Reflection.FieldAccessor.#ctor(System.Reflection.FieldInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Reflection.FieldAccessor"/> class.
            </summary>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance to use for this accessor.</param>
        </member>
        <member name="M:EntityFramework.Reflection.FieldAccessor.GetValue(System.Object)">
            <summary>
            Returns the value of the member.
            </summary>
            <param name="instance">The object whose member value will be returned.</param>
            <returns>
            The member value for the instance parameter.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.FieldAccessor.SetValue(System.Object,System.Object)">
            <summary>
            Sets the value of the member.
            </summary>
            <param name="instance">The object whose member value will be set.</param>
            <param name="value">The new value for this member.</param>
        </member>
        <member name="P:EntityFramework.Reflection.FieldAccessor.MemberType">
            <summary>
            Gets the type of the member.
            </summary>
            <value>The type of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.FieldAccessor.MemberInfo">
            <summary>
            Gets the member info.
            </summary>
            <value>The member info.</value>
        </member>
        <member name="P:EntityFramework.Reflection.FieldAccessor.Name">
            <summary>
            Gets the name of the member.
            </summary>
            <value>The name of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.FieldAccessor.HasGetter">
            <summary>
            Gets a value indicating whether this member has getter.
            </summary>
            <value><c>true</c> if this member has getter; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:EntityFramework.Reflection.FieldAccessor.HasSetter">
            <summary>
            Gets a value indicating whether this member has setter.
            </summary>
            <value><c>true</c> if this member has setter; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:EntityFramework.Reflection.IMethodAccessor">
            <summary>
            An interface for method accessor
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.IMethodAccessor.Invoke(System.Object,System.Object[])">
            <summary>
            Invokes the method on the specified instance.
            </summary>
            <param name="instance">The object on which to invoke the method. If a method is static, this argument is ignored.</param>
            <param name="arguments">An argument list for the invoked method.</param>
            <returns>An object containing the return value of the invoked method.</returns>
        </member>
        <member name="P:EntityFramework.Reflection.IMethodAccessor.MethodInfo">
            <summary>
            Gets the method info.
            </summary>
        </member>
        <member name="P:EntityFramework.Reflection.IMethodAccessor.Name">
            <summary>
            Gets the name of the member.
            </summary>
            <value>The name of the member.</value>
        </member>
        <member name="T:EntityFramework.Reflection.LateBinder">
            <summary>
            A class for late bound operations on a type.
            </summary>
        </member>
        <member name="F:EntityFramework.Reflection.LateBinder.DefaultPublicFlags">
            <summary>
            Default Flags for pulic binding.
            </summary>
        </member>
        <member name="F:EntityFramework.Reflection.LateBinder.DefaultNonPublicFlags">
            <summary>
            Default Flags for nonpublic binding.
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.FindMethod(System.Type,System.String,System.Object[])">
            <summary>
            Searches for the specified method with the specified name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the method in.</param>
            <param name="name">The name of the method to find.</param>
            <param name="arguments">The arguments.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMethodAccessor"/> instance for the method if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.FindMethod(System.Type,System.String,System.Reflection.BindingFlags,System.Object[])">
            <summary>
            Searches for the specified method, using the specified binding constraints.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the method in.</param>
            <param name="name">The name of the method to find.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <param name="arguments">The arguments.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMethodAccessor"/> instance for the method if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.FindProperty``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Searches for the property using a property expression.
            </summary>
            <typeparam name="T">The object type containing the property specified in the expression.</typeparam>
            <param name="propertyExpression">The property expression (e.g. p =&gt; p.PropertyName)</param>
            <returns>An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property if found; otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="propertyExpression"/> is null.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
                Not a <see cref="T:System.Linq.Expressions.MemberExpression"/><br/>
                The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
                Or, the property is static.
            </exception>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.FindProperty(System.Type,System.String)">
            <summary>
            Searches for the public property with the specified name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the property in.</param>
            <param name="name">The name of the property to find.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.FindProperty(System.Type,System.String,System.Reflection.BindingFlags)">
            <summary>
            Searches for the specified property, using the specified binding constraints.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the property in.</param>
            <param name="name">The name of the property to find.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.FindField(System.Type,System.String)">
            <summary>
            Searches for the field with the specified name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the field in.</param>
            <param name="name">The name of the field to find.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the field if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.FindField(System.Type,System.String,System.Reflection.BindingFlags)">
            <summary>
            Searches for the field, using the specified binding constraints.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the field in.</param>
            <param name="name">The name of the field to find.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the field if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.Find(System.Type,System.String)">
            <summary>
            Searches for the property or field with the specified name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the property or field in.</param>
            <param name="name">The name of the property or field to find.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property or field if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.Find(System.Type,System.String,System.Reflection.BindingFlags)">
            <summary>
            Searches for the property or field, using the specified binding constraints.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to search for the property or field in.</param>
            <param name="name">The name of the property or field to find.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property or field if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.SetProperty(System.Object,System.String,System.Object)">
            <summary>
            Sets the property value with the specified name.
            </summary>
            <param name="target">The object whose property value will be set.</param>
            <param name="name">The name of the property to set.</param>
            <param name="value">The new value to be set.</param>
            <remarks>This method supports nested property names. An exmample name would be 'Person.Address.ZipCode'.</remarks>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.SetProperty(System.Object,System.String,System.Object,System.Reflection.BindingFlags)">
            <summary>
            Sets the property value with the specified name.
            </summary>
            <param name="target">The object whose property value will be set.</param>
            <param name="name">The name of the property to set.</param>
            <param name="value">The new value to be set.</param>
            <remarks>This method supports nested property names. An exmample name would be 'Person.Address.ZipCode'.</remarks>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.SetField(System.Object,System.String,System.Object)">
            <summary>
            Sets the field value with the specified name.
            </summary>
            <param name="target">The object whose field value will be set.</param>
            <param name="name">The name of the field to set.</param>
            <param name="value">The new value to be set.</param>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.SetField(System.Object,System.String,System.Object,System.Reflection.BindingFlags)">
            <summary>
            Sets the field value with the specified name.
            </summary>
            <param name="target">The object whose field value will be set.</param>
            <param name="name">The name of the field to set.</param>
            <param name="value">The new value to be set.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.Set(System.Object,System.String,System.Object)">
            <summary>
            Sets the property or field value with the specified name.
            </summary>
            <param name="target">The object whose property or field value will be set.</param>
            <param name="name">The name of the property or field to set.</param>
            <param name="value">The new value to be set.</param>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.Set(System.Object,System.String,System.Object,System.Reflection.BindingFlags)">
            <summary>
            Sets the property or field value with the specified name.
            </summary>
            <param name="target">The object whose property or field value will be set.</param>
            <param name="name">The name of the property or field to set.</param>
            <param name="value">The new value to be set.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.GetProperty(System.Object,System.String)">
            <summary>
            Returns the value of the property with the specified name.
            </summary>
            <param name="target">The object whose property value will be returned.</param>
            <param name="name">The name of the property to read.</param>
            <returns>The value of the property.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.GetProperty(System.Object,System.String,System.Reflection.BindingFlags)">
            <summary>
            Returns the value of the property with the specified name.
            </summary>
            <param name="target">The object whose property value will be returned.</param>
            <param name="name">The name of the property to read.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>The value of the property.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.GetField(System.Object,System.String)">
            <summary>
            Returns the value of the field with the specified name.
            </summary>
            <param name="target">The object whose field value will be returned.</param>
            <param name="name">The name of the field to read.</param>
            <returns>The value of the field.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.GetField(System.Object,System.String,System.Reflection.BindingFlags)">
            <summary>
            Returns the value of the field with the specified name.
            </summary>
            <param name="target">The object whose field value will be returned.</param>
            <param name="name">The name of the field to read.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>The value of the field.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.Get(System.Object,System.String)">
            <summary>
            Returns the value of the property or field with the specified name.
            </summary>
            <param name="target">The object whose property or field value will be returned.</param>
            <param name="name">The name of the property or field to read.</param>
            <returns>The value of the property or field.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.Get(System.Object,System.String,System.Reflection.BindingFlags)">
            <summary>
            Returns the value of the property or field with the specified name.
            </summary>
            <param name="target">The object whose property or field value will be returned.</param>
            <param name="name">The name of the property or field to read.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>The value of the property or field.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.CreateInstance(System.Type)">
            <summary>
            Creates an instance of the specified type.
            </summary>
            <param name="type">The type to create.</param>
            <returns>A new instance of the specified type.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.LateBinder.InvokeMethod(System.Object,System.String,System.Object[])">
            <summary>
            Invokes a method by <paramref name="name"/>.
            </summary>
            <param name="target">The target instance to invoke the method on.</param>
            <param name="name">The name of the method to invoke.</param>
            <param name="arguments">The arguments for the method.</param>
            <returns>The result of the method call</returns>
        </member>
        <member name="T:EntityFramework.Reflection.MethodAccessor">
            <summary>
            An accessor class for <see cref="P:EntityFramework.Reflection.MethodAccessor.MethodInfo"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.MethodAccessor.#ctor(System.Reflection.MethodInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Reflection.MethodAccessor"/> class.
            </summary>
            <param name="methodInfo">The method info.</param>
        </member>
        <member name="M:EntityFramework.Reflection.MethodAccessor.Invoke(System.Object,System.Object[])">
            <summary>
            Invokes the method on the specified instance.
            </summary>
            <param name="instance">The object on which to invoke the method. If a method is static, this argument is ignored.</param>
            <param name="arguments">An argument list for the invoked method.</param>
            <returns>
            An object containing the return value of the invoked method.
            </returns>
        </member>
        <member name="P:EntityFramework.Reflection.MethodAccessor.MethodInfo">
            <summary>
            Gets the method info.
            </summary>
        </member>
        <member name="P:EntityFramework.Reflection.MethodAccessor.Name">
            <summary>
            Gets the name of the member.
            </summary>
            <value>
            The name of the member.
            </value>
        </member>
        <member name="T:EntityFramework.Reflection.ObjectCloner">
            <summary>
            A class to support cloning, which creates a new instance of a class with the same value as an existing instance.
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCloner.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Reflection.ObjectCloner"/> class.
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCloner.Clone(System.Object)">
            <summary>
            Creates a new object that is a copy of the source instance.
            </summary>
            <param name="source">The source object to copy.</param>
            <returns>A new object that is a copy of the source instance.</returns>
        </member>
        <member name="T:EntityFramework.Reflection.ObjectCopier">
            <summary>
            Copy data from a source into a target object by copying public property values.
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Clone``1(``0)">
            <summary>
            Creates a new object that is a copy of the source instance.
            </summary>
            <typeparam name="T">The type of the object to clone.</typeparam>
            <param name="source">The source object to copy.</param>
            <returns>A new object that is a copy of the source instance.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Clone(System.Object)">
            <summary>
            Creates a new object that is a copy of the source instance.
            </summary>
            <param name="source">The source object to copy.</param>
            <returns>A new object that is a copy of the source instance.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Object)">
            <summary>
            Copies values from the source into the properties of the target.
            </summary>
            <param name="source">An object containing the source values.</param>
            <param name="target">An object with properties to be set from the source.</param>
            <remarks>
            The property names and types of the source object must match the property names and types
            on the target object. Source properties may not be indexed. 
            Target properties may not be readonly or indexed.
            </remarks>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Object,System.String[])">
            <summary>
            Copies values from the source into the properties of the target.
            </summary>
            <param name="source">An object containing the source values.</param>
            <param name="target">An object with properties to be set from the source.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be set on the target object.</param>
            <remarks>
            The property names and types of the source object must match the property names and types
            on the target object. Source properties may not be indexed. 
            Target properties may not be readonly or indexed.
            </remarks>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Object,System.Boolean,System.String[])">
            <summary>
            Copies values from the source into the properties of the target.
            </summary>
            <param name="source">An object containing the source values.</param>
            <param name="target">An object with properties to be set from the source.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be set on the target object.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
            <remarks>
            <para>
            The property names and types of the source object must match the property names and types
            on the target object. Source properties may not be indexed. 
            Target properties may not be readonly or indexed.
            </para><para>
            Properties to copy are determined based on the source object. Any properties
            on the source object marked with the <see cref="T:System.ComponentModel.BrowsableAttribute"/> equal
            to false are ignored.
            </para>
            </remarks>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Object,System.Boolean,System.Func{System.String,System.Boolean})">
            <summary>
            Copies values from the source into the properties of the target.
            </summary>
            <param name="source">An object containing the source values.</param>
            <param name="target">An object with properties to be set from the source.</param>
            <param name="propertyFilter">A delegate to determine if the property name should be ignored. 
            When the delegate returns true, the property will not be set in the target.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
            <remarks>
            <para>
            The property names and types of the source object must match the property names and types
            on the target object. Source properties may not be indexed. 
            Target properties may not be readonly or indexed.
            </para><para>
            Properties to copy are determined based on the source object. Any properties
            on the source object marked with the <see cref="T:System.ComponentModel.BrowsableAttribute"/> equal
            to false are ignored.
            </para>
            </remarks>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Copies values from the source into the target <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <param name="source">The source object.</param>
            <param name="target">The target <see cref="T:System.Collections.IDictionary"/>.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Collections.Generic.IDictionary{System.String,System.Object},System.String[])">
            <summary>
            Copies values from the source into the target <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <param name="source">The source object.</param>
            <param name="target">The target <see cref="T:System.Collections.IDictionary"/>.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be added to the targeted <see cref="T:System.Collections.IDictionary"/>.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Collections.Generic.IDictionary{System.String,System.Object},System.Boolean,System.String[])">
            <summary>
            Copies values from the source into the target <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <param name="source">The source object.</param>
            <param name="target">The target <see cref="T:System.Collections.IDictionary"/>.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be added to the targeted <see cref="T:System.Collections.IDictionary"/>.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Object,System.Collections.Generic.IDictionary{System.String,System.Object},System.Boolean,System.Func{System.String,System.Boolean})">
            <summary>
            Copies values from the source into the target <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <param name="source">The source object.</param>
            <param name="target">The target <see cref="T:System.Collections.IDictionary"/>.</param>
            <param name="propertyFilter">A delegate to determine if the property name should be ignored. 
            When the delegate returns true, the property will not be added to the targeted <see cref="T:System.Collections.IDictionary"/>.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Specialized.NameValueCollection,System.Object)">
            <summary>
            Copies values from the <see cref="T:System.Collections.Specialized.NameValueCollection"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.Specialized.NameValueCollection"/> source.</param>
            <param name="target">The target object.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Specialized.NameValueCollection,System.Object,System.String[])">
            <summary>
            Copies values from the <see cref="T:System.Collections.Specialized.NameValueCollection"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.Specialized.NameValueCollection"/> source.</param>
            <param name="target">The target object.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be set on the target object.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Specialized.NameValueCollection,System.Object,System.Boolean,System.String[])">
            <summary>
            Copies values from the <see cref="T:System.Collections.Specialized.NameValueCollection"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.Specialized.NameValueCollection"/> source.</param>
            <param name="target">The target object.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be set on the target object.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Specialized.NameValueCollection,System.Object,System.Boolean,System.Func{System.String,System.Boolean})">
            <summary>
            Copies values from the <see cref="T:System.Collections.Specialized.NameValueCollection"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.Specialized.NameValueCollection"/> source.</param>
            <param name="target">The target object.</param>
            <param name="propertyFilter">A delegate to determine if the property name should be ignored. 
            When the delegate returns true, the property will not be set in the target.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Generic.IDictionary{System.String,System.Object},System.Object)">
            <summary>
            Copies values from the <see cref="T:System.Collections.IDictionary"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.IDictionary"/> source.</param>
            <param name="target">The target object.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Generic.IDictionary{System.String,System.Object},System.Object,System.String[])">
            <summary>
            Copies values from the <see cref="T:System.Collections.IDictionary"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.IDictionary"/> source.</param>
            <param name="target">The target object.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be set on the target object.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Generic.IDictionary{System.String,System.Object},System.Object,System.Boolean,System.String[])">
            <summary>
            Copies values from the <see cref="T:System.Collections.IDictionary"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.IDictionary"/> source.</param>
            <param name="target">The target object.</param>
            <param name="ignoreList">A list of property names to ignore. 
            These properties will not be set on the target object.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
        </member>
        <member name="M:EntityFramework.Reflection.ObjectCopier.Copy(System.Collections.Generic.IDictionary{System.String,System.Object},System.Object,System.Boolean,System.Func{System.String,System.Boolean})">
            <summary>
            Copies values from the <see cref="T:System.Collections.IDictionary"/> into the properties of the target.
            </summary>
            <param name="source">The <see cref="T:System.Collections.IDictionary"/> source.</param>
            <param name="target">The target object.</param>
            <param name="propertyFilter">A delegate to determine if the property name should be ignored. 
            When the delegate returns true, the property will not be set in the target.</param>
            <param name="suppressExceptions">If <see langword="true"/>, any exceptions will be suppressed.</param>
        </member>
        <member name="T:EntityFramework.Reflection.PropertyAccessor">
            <summary>
            An accessor class for <see cref="T:System.Reflection.PropertyInfo"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.PropertyAccessor.#ctor(System.Reflection.PropertyInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Reflection.PropertyAccessor"/> class.
            </summary>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo"/> instance to use for this accessor.</param>
        </member>
        <member name="M:EntityFramework.Reflection.PropertyAccessor.GetValue(System.Object)">
            <summary>
            Returns the value of the member.
            </summary>
            <param name="instance">The object whose member value will be returned.</param>
            <returns>
            The member value for the instance parameter.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.PropertyAccessor.SetValue(System.Object,System.Object)">
            <summary>
            Sets the value of the member.
            </summary>
            <param name="instance">The object whose member value will be set.</param>
            <param name="value">The new value for this member.</param>
        </member>
        <member name="P:EntityFramework.Reflection.PropertyAccessor.MemberType">
            <summary>
            Gets the type of the member.
            </summary>
            <value>The type of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.PropertyAccessor.MemberInfo">
            <summary>
            Gets the member info.
            </summary>
            <value>The member info.</value>
        </member>
        <member name="P:EntityFramework.Reflection.PropertyAccessor.Name">
            <summary>
            Gets the name of the member.
            </summary>
            <value>The name of the member.</value>
        </member>
        <member name="P:EntityFramework.Reflection.PropertyAccessor.HasGetter">
            <summary>
            Gets a value indicating whether this member has getter.
            </summary>
            <value><c>true</c> if this member has getter; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:EntityFramework.Reflection.PropertyAccessor.HasSetter">
            <summary>
            Gets a value indicating whether this member has setter.
            </summary>
            <value><c>true</c> if this member has setter; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:EntityFramework.Reflection.ReflectionHelper">
            <summary>
            A class for reflection helper methods
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.ExtractPropertyName``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Extracts the property name from a property expression.
            </summary>
            <typeparam name="TValue">The type of the property value.</typeparam>
            <param name="propertyExpression">The property expression (e.g. p =&gt; p.PropertyName)</param>
            <returns>The name of the property.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="propertyExpression"/> is null.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
                Not a <see cref="T:System.Linq.Expressions.MemberExpression"/><br/>
                The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
                Or, the property is static.
            </exception>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.ExtractPropertyName``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Extracts the property name from a property expression.
            </summary>
            <typeparam name="TSource">The type of the source.</typeparam>
            <typeparam name="TValue">The type of the property value.</typeparam>
            <param name="propertyExpression">The property expression (e.g. p =&gt; p.PropertyName)</param>
            <returns>
            The name of the property.
            </returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="propertyExpression"/> is null.</exception>
              
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
            Not a <see cref="T:System.Linq.Expressions.MemberExpression"/><br/>
            The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
            Or, the property is static.
              </exception>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.ExtractPropertyName(System.Linq.Expressions.MemberExpression)">
            <summary>
            Extracts the property name from a property expression.
            </summary>
            <param name="memberExpression">The member expression</param>
            <returns>
            The name of the property.
            </returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="memberExpression"/> is null.</exception>
              
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
            Not a <see cref="T:System.Linq.Expressions.MemberExpression"/><br/>
            The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
            Or, the property is static.
              </exception>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.ExtractPropertyInfo``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Extracts the <see cref="T:System.Reflection.PropertyInfo"/> from a property expression.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="propertyExpression">The property expression.</param>
            <returns>The <see cref="T:System.Reflection.PropertyInfo"/> from the expression.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.ExtractPropertyInfo``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Extracts the <see cref="T:System.Reflection.PropertyInfo"/> from a property expression.
            </summary>
            <typeparam name="TSource">The type of the source.</typeparam>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="propertyExpression">The property expression.</param>
            <returns>The <see cref="T:System.Reflection.PropertyInfo"/> from the expression.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.ExtractPropertyInfo(System.Linq.Expressions.MemberExpression)">
            <summary>
            Extracts the property info.
            </summary>
            <param name="memberExpression">The member expression.</param>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.GetUnderlyingType(System.Type)">
            <summary>
            Gets the underlying type dealing with <see cref="T:System.Nullable"/>.
            </summary>
            <param name="type">The type.</param>
            <returns>Returns a type dealing with <see cref="T:System.Nullable"/>.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.IsCollection(System.Type)">
            <summary>
            Determines whether the specified type is a collection.
            </summary>
            <param name="type">The type to check.</param>
            <returns>
              <c>true</c> if the specified type is collection; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.IsDictionary(System.Type)">
            <summary>
            Determines whether the specified type is a dictionary.
            </summary>
            <param name="type">The type to check.</param>
            <returns>
              <c>true</c> if the specified type is dictionary; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.ReflectionHelper.CoerceValue(System.Type,System.Type,System.Object)">
            <summary>
            Attempts to coerce a value of one type into
            a value of a different type.
            </summary>
            <param name="desiredType">
            Type to which the value should be coerced.
            </param>
            <param name="valueType">
            Original type of the value.
            </param>
            <param name="value">
            The value to coerce.
            </param>
            <remarks>
            <para>
            If the desired type is a primitive type or Decimal, 
            empty string and null values will result in a 0 
            or equivalent.
            </para>
            <para>
            If the desired type is a <see cref="T:System.Nullable"/> type, empty string
            and null values will result in a null result.
            </para>
            <para>
            If the desired type is an <c>enum</c> the value's ToString()
            result is parsed to convert into the <c>enum</c> value.
            </para>
            </remarks>
        </member>
        <member name="T:EntityFramework.Reflection.TypeAccessor">
            <summary>
            A class holding all the accessors for a <see cref="P:EntityFramework.Reflection.TypeAccessor.Type"/>.
            </summary>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:EntityFramework.Reflection.TypeAccessor"/> class.
            </summary>
            <param name="type">The <see cref="P:EntityFramework.Reflection.TypeAccessor.Type"/> this accessor is for.</param>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.Create">
            <summary>
            Creates a new instance of accessors type.
            </summary>
            <returns>A new instance of accessors type.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindMethod(System.String)">
            <summary>
            Searches for the public property with the specified name.
            </summary>
            <param name="name">The name of the property to find.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMethodAccessor"/> instance for the method if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindMethod(System.String,System.Type[])">
            <summary>
            Searches for the public property with the specified name.
            </summary>
            <param name="name">The name of the property to find.</param>
            <param name="parameterTypes">The parameter argument types.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMethodAccessor"/> instance for the method if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindMethod(System.String,System.Type[],System.Reflection.BindingFlags)">
            <summary>
            Searches for the specified method, using the specified binding constraints.
            </summary>
            <param name="name">The name of the property to find.</param>
            <param name="parameterTypes">The parameter argument types.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMethodAccessor"/> instance for the method if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.Find(System.String)">
            <summary>
            Searches for the public property or field with the specified name.
            </summary>
            <param name="name">The name of the property or field to find.</param>
            <returns>An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property or field if found; otherwise <c>null</c>.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.Find(System.String,System.Reflection.BindingFlags)">
            <summary>
            Searches for the specified property or field, using the specified binding constraints.
            </summary>
            <param name="name">The name of the property or field to find.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property or field if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindProperty``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Searches for the property using a property expression.
            </summary>
            <typeparam name="T">The object type containing the property specified in the expression.</typeparam>
            <param name="propertyExpression">The property expression (e.g. p =&gt; p.PropertyName)</param>
            <returns>An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property if found; otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="propertyExpression"/> is null.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
                Not a <see cref="T:System.Linq.Expressions.MemberExpression"/><br/>
                The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
                Or, the property is static.
            </exception>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindProperty``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Searches for the property using a property expression.
            </summary>
            <typeparam name="TSource">The object type containing the property specified in the expression.</typeparam>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="propertyExpression">The property expression (e.g. p =&gt; p.PropertyName)</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property if found; otherwise <c>null</c>.
            </returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="propertyExpression"/> is null.</exception>
              
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
            Not a <see cref="T:System.Linq.Expressions.MemberExpression"/><br/>
            The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
            Or, the property is static.
              </exception>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindProperty(System.String)">
            <summary>
            Searches for the public property with the specified name.
            </summary>
            <param name="name">The name of the property to find.</param>
            <returns>An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property if found; otherwise <c>null</c>.</returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindProperty(System.String,System.Reflection.BindingFlags)">
            <summary>
            Searches for the specified property, using the specified binding constraints.
            </summary>
            <param name="name">The name of the property to find.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.GetAccessor(System.Reflection.PropertyInfo)">
            <summary>
            Gets the <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> for the specified <see cref="T:System.Reflection.PropertyInfo"/>.
            </summary>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo"/> to get the <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> for.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the property.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.GetProperties">
            <summary>
            Gets the property member accessors for the Type.
            </summary>
            <returns>
            An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instances for the Type.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.GetProperties(System.Reflection.BindingFlags)">
            <summary>
            Gets the property member accessors for the Type using the specified flags.
            </summary>
            <returns>
            An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instances for the Type.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindField(System.String)">
            <summary>
            Searches for the specified field with the specified name.
            </summary>
            <param name="name">The name of the field to find.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the field if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.FindField(System.String,System.Reflection.BindingFlags)">
            <summary>
            Searches for the specified field, using the specified binding constraints.
            </summary>
            <param name="name">The name of the field to find.</param>
            <param name="flags">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags"/> that specify how the search is conducted.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the field if found; otherwise <c>null</c>.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.GetAccessor(System.Reflection.FieldInfo)">
            <summary>
            Gets the <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> for the specified <see cref="T:System.Reflection.FieldInfo"/>.
            </summary>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> to get the <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> for.</param>
            <returns>
            An <see cref="T:EntityFramework.Reflection.IMemberAccessor"/> instance for the field.
            </returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.GetAccessor``1">
            <summary>
            Gets the <see cref="T:EntityFramework.Reflection.TypeAccessor"/> for the specified Type.
            </summary>
            <typeparam name="T">The Type to get the accessor for.</typeparam>
            <returns></returns>
        </member>
        <member name="M:EntityFramework.Reflection.TypeAccessor.GetAccessor(System.Type)">
            <summary>
            Gets the <see cref="T:EntityFramework.Reflection.TypeAccessor"/> for the specified Type.
            </summary>
            <param name="type">The Type to get the accessor for.</param>
            <returns></returns>
        </member>
        <member name="P:EntityFramework.Reflection.TypeAccessor.Type">
            <summary>
            Gets the <see cref="P:EntityFramework.Reflection.TypeAccessor.Type"/> this accessor is for.
            </summary>
            <value>The <see cref="P:EntityFramework.Reflection.TypeAccessor.Type"/> this accessor is for.</value>
        </member>
    </members>
</doc>
