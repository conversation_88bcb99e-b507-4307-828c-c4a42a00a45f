/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Fraktur/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Fraktur-bold"]={directory:"Fraktur/Bold",family:"STIXMathJax_Fraktur",weight:"bold",testString:"\u00A0\u210C\u2128\u212D\uD835\uDD6C\uD835\uDD6D\uD835\uDD6E\uD835\uDD6F\uD835\uDD70\uD835\uDD71\uD835\uDD72\uD835\uDD73\uD835\uDD74\uD835\uDD75\uD835\uDD76",32:[0,0,250,0,0],160:[0,0,250,0,0],8460:[701,205,843,42,795],8488:[701,195,755,44,703],8493:[701,19,773,54,731],120172:[701,25,856,50,805],120173:[701,19,849,50,794],120174:[701,19,773,54,731],120175:[701,19,891,54,836],120176:[701,19,788,54,731],120177:[701,205,803,54,748],120178:[701,19,833,54,781],120179:[701,205,843,42,795],120180:[701,25,790,54,735],120181:[701,205,803,54,748],120182:[701,25,864,42,814],120183:[701,25,699,51,645],120184:[701,25,1133,50,1081],120185:[701,25,862,50,810],120186:[701,19,909,54,854],120187:[701,205,850,50,795],120188:[701,59,930,54,902],120189:[701,25,884,50,841],120190:[701,19,852,54,802],120191:[701,25,793,54,740],120192:[701,25,860,54,809],120193:[701,19,855,50,800],120194:[701,19,1121,50,1066],120195:[701,25,819,50,775],120196:[701,205,837,50,782],120197:[701,195,755,44,703],120198:[475,24,600,55,545],120199:[695,24,559,45,504],120200:[475,24,464,55,412],120201:[694,25,557,48,502],120202:[475,24,476,55,427],120203:[700,214,370,33,352],120204:[475,219,566,55,506],120205:[695,219,576,45,516],120206:[697,24,429,35,379],120207:[697,219,389,40,337],120208:[695,24,456,48,402],120209:[695,24,433,45,379],120210:[475,24,984,40,932],120211:[475,24,696,40,644],120212:[475,24,554,45,499],120213:[593,219,640,36,585],120214:[475,219,574,55,522],120215:[475,24,525,40,493],120216:[643,31,557,52,505],120217:[656,23,438,45,378],120218:[475,24,681,35,629],120219:[593,24,573,55,526],120220:[593,24,850,55,795],120221:[475,209,521,50,489],120222:[593,219,596,55,536],120223:[475,219,484,36,437]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Fraktur-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Fraktur/Bold/Main.js"]);
