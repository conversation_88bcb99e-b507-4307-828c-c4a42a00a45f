﻿using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace EcoolWeb.Controllers
{
    public class SharedApiController : ApiBase
    {

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();


        /// <summary>
        /// 取得學校清單
        /// </summary>
        /// <returns></returns>
        [HttpGet, HttpPost]
        [AllowAnonymous]
        public List<SchooModel> GetSCHOOL()
        {
 
             string Path2= Url.Content(@"~/Content/img/e01.gif");
            string Path = Url.Content(@"~/Content/css/bootstrap.css");
            Path = Path.Replace("/css/bootstrap.css", "/BDMT01IMG/");
            List<BDMT01> bDMTsListSort = new List<BDMT01>();
            bDMTsListSort = db.BDMT01.OrderBy(x => x.SHORT_NAME).ToList();
            List<SchooModel> SchoolList = new List<SchooModel>();
            int i = 0;

            var bDMTsListSortITEMS=bDMTsListSort.GroupBy(a => new { a.ZONE_ID, a.CITY }).Select(a => new { a.Key.CITY, a.Key.ZONE_ID }).OrderByDescending(a => a.CITY).ThenBy(a => a.ZONE_ID).ToList();
            foreach (var ThisZONE_ID in bDMTsListSortITEMS)
            {

                SchooModel SchoolListItem = new SchooModel();
                SchoolListItem.SCHOOL_NO = i.ToString();
                SchoolListItem.SHORT_NAME = ThisZONE_ID.CITY + ThisZONE_ID.ZONE_ID;
                SchoolListItem.SCHOOL_NAME = ThisZONE_ID.CITY + ThisZONE_ID.ZONE_ID;
                SchoolListItem.SCHOOL_IMG = Path2;
                SchoolList.Add(SchoolListItem);
                foreach (var school in bDMTsListSort.Where(a => a.ZONE_ID == ThisZONE_ID.ZONE_ID && a.CITY == ThisZONE_ID.CITY).ToList())
                {
                    SchooModel  SchoolListItem1 = new SchooModel();
                    SchoolListItem1.SCHOOL_NO = school.SCHOOL_NO;
                    SchoolListItem1.SHORT_NAME = school.SHORT_NAME;
                    SchoolListItem1.SCHOOL_NAME = school.SCHOOL_NAME;
                    SchoolListItem1.SCHOOL_IMG = Path + school.SCHOOL_NO + ".png";
                    SchoolList.Add(SchoolListItem1);
                }
               i++;
            }
            
            return SchoolList;
        }
        [HttpGet, HttpPost]
        [AllowAnonymous]
        public List<SchooLDistModel> GetDistList()
        {
            string Path2 = Url.Content(@"~/Content/img/e01.gif");
            string Path = Url.Content(@"~/Content/css/bootstrap.css");
            Path = Path.Replace("/css/bootstrap.css", "/BDMT01IMG/");
            List<BDMT01> bDMTsListSort = new List<BDMT01>();
            List<SchooLDistModel> schooLDistModels = new List<SchooLDistModel>();
            var temp2 = (from x in db.BDMT01
                        where x.ZONE_ID == " 內湖區"
                        select new SchooLDistModel
                        {

                            DIST_NO = x.ZipCode,
                            DIST_NAME = x.CITY + " " + x.ZONE_ID,
                            DIST_IMG = Path2

                        }


          ).ToList();


            IEnumerable<IGrouping<string, SchooLDistModel>> result2 = temp2.GroupBy(x => x.DIST_NO);
            foreach (IGrouping<string, SchooLDistModel> group in result2)
            {
                SchooLDistModel schooLDistModelItem = new SchooLDistModel();
                schooLDistModelItem.DIST_NO = group.FirstOrDefault().DIST_NO;
                schooLDistModelItem.DIST_NAME = group.FirstOrDefault().DIST_NAME;
                schooLDistModelItem.DIST_IMG = group.FirstOrDefault().DIST_IMG;
                schooLDistModels.Add(group.FirstOrDefault());
            }
                var temp = (from x in db.BDMT01
                       where x.CITY== "臺北市" && x.ZONE_ID != " 內湖區"
                            select new SchooLDistModel
                        {

                            DIST_NO = x.ZipCode,
                            DIST_NAME = x.CITY +" "+ x.ZONE_ID,
                            DIST_IMG = Path2

                        }
                   

            ).ToList();
            IEnumerable<IGrouping<string, SchooLDistModel>> result = temp.GroupBy(x => x.DIST_NO);
                foreach (IGrouping<string, SchooLDistModel> group in result)
            {

              
                    SchooLDistModel schooLDistModelItem = new SchooLDistModel();
                    schooLDistModelItem.DIST_NO = group.FirstOrDefault().DIST_NO;
                    schooLDistModelItem.DIST_NAME = group.FirstOrDefault().DIST_NAME;
                    schooLDistModelItem.DIST_IMG = group.FirstOrDefault().DIST_IMG;
                    schooLDistModels.Add(group.FirstOrDefault());
         


                }
            var temp1 = (from x in db.BDMT01
                        where x.CITY != "臺北市"
                        select new SchooLDistModel
                        {

                            DIST_NO = x.ZipCode,
                            DIST_NAME = x.CITY + " " + x.ZONE_ID,
                            DIST_IMG = Path2

                        }


       ).ToList();
            IEnumerable<IGrouping<string, SchooLDistModel>> result1 = temp1.GroupBy(x => x.DIST_NO);
            foreach (IGrouping<string, SchooLDistModel> group in result1)
            {


                SchooLDistModel schooLDistModelItem = new SchooLDistModel();
                schooLDistModelItem.DIST_NO = group.FirstOrDefault().DIST_NO;
                schooLDistModelItem.DIST_NAME = group.FirstOrDefault().DIST_NAME;
                schooLDistModelItem.DIST_IMG = group.FirstOrDefault().DIST_IMG;
                schooLDistModels.Add(group.FirstOrDefault());



            }
            //schooLDistModels = temp.Distinct().ToList();
            return schooLDistModels;
        }
        [HttpGet, HttpPost]
        [AllowAnonymous]
        public List<SchooModel> GetSchoolListByDisNO(string ZipCode)
        {
            string Path2 = Url.Content(@"~/Content/img/e01.gif");
            string Path = Url.Content(@"~/Content/css/bootstrap.css");
            Path = Path.Replace("/css/bootstrap.css", "/BDMT01IMG/");
            List<BDMT01> bDMTsListSort = new List<BDMT01>();
            bDMTsListSort = db.BDMT01.OrderBy(x => x.SHORT_NAME).ToList();
            List<SchooModel> SchoolList = new List<SchooModel>();
            foreach (var school in bDMTsListSort.Where(x => x.ZipCode == ZipCode).ToList())
            {

                SchooModel SchoolListItem1 = new SchooModel();
                SchoolListItem1.SCHOOL_NO = school.SCHOOL_NO;
                SchoolListItem1.SHORT_NAME = school.SHORT_NAME;
                SchoolListItem1.SCHOOL_NAME = school.SCHOOL_NAME;
                SchoolListItem1.SCHOOL_IMG = Path + school.SCHOOL_NO + ".png";
                SchoolList.Add(SchoolListItem1);

            }
            return SchoolList;
        }
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
