/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeThreeSym/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeThreeSym={directory:"SizeThreeSym/Regular",family:"STIXSizeThreeSym",Ranges:[[688,767,"All"],[768,824,"All"],[8254,8254,"All"],[8400,8431,"All"],[8730,8732,"All"],[9115,9145,"All"],[9180,9185,"All"],[10098,10099,"All"],[10214,10219,"All"],[10627,10630,"All"]],32:[0,0,250,0,0],40:[2066,394,750,182,667],41:[2066,394,750,83,568],47:[2066,394,1101,30,1071],91:[2066,394,508,225,491],92:[2066,394,1101,30,1071],93:[2066,394,508,17,283],95:[-127,177,2000,0,2000],123:[2066,394,906,143,717],125:[2066,394,906,189,763],160:[0,0,250,0,0],770:[777,-564,0,-1610,-150],771:[774,-608,0,-1612,-152],8730:[2565,510,1076,112,1110],8968:[2066,394,565,225,550],8969:[2066,394,565,15,340],8970:[2066,394,565,225,550],8971:[2066,394,565,15,340],9182:[157,86,1886,0,1886],9183:[815,-572,1886,0,1886],10216:[2066,394,765,96,670],10217:[2066,394,765,95,669]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeThreeSym");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeThreeSym/Regular/Main.js");
