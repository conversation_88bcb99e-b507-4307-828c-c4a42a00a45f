﻿@model SECI01BorrowIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

@using (Html.BeginForm("BorrowIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageBorrow", (string)ViewBag.BRE_NO)
    </div>
}


@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageBorrow", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }
    </script>
}


@section css{
    <style>
        th {
            text-align:left!important;
        }
    </style>
    }

