﻿using com.ecool.sqlConnection;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.dao
{
    public class ADDTSPDAO : AbstractSPDAO
    {
        private string sp_name = string.Empty;
        private SqlConnection conn = null;
        public string ADDTPendingDetail(string Mode, int APPLY_NO, string txtARTICLE)
        {
            sp_name = "ADDTPendingDetail";
            sqlConnection getConn = new sqlConnection();
            conn = getConn.getConnection4Query();
            SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("APPLY_STATUS", System.Data.SqlDbType.VarChar, 1).Value = Mode;
            cmd.Parameters.Add("REVIEW_VERIFY", System.Data.SqlDbType.VarChar, 200).Value = txtARTICLE;
            cmd.Parameters.Add("APPLY_NO", System.Data.SqlDbType.Int).Value = APPLY_NO;
            SqlParameter rc = new SqlParameter("DBMsg", SqlDbType.VarChar, 50);
            rc.Direction = System.Data.ParameterDirection.Output;
            cmd.Parameters.Add(rc);
            try
            {
                cmd.ExecuteNonQuery();
                handleException(sp_name, (string)rc.Value);
                return (string)rc.Value;
            }
            catch (Exception ex)
            {
                throw ex.GetBaseException();
            }
            finally
            {
                getConn.closeConnection4Query(conn);
            }
        }
    }
}
