﻿@model AWAI07DescViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_BankMenu", new { NowAction = "Index" });
}


<img src="~/Content/img/web-bar-Bank-A.png" class="img-responsive" />
<br />
<div style="width:100%;">
   @if (!string.IsNullOrWhiteSpace(Model.AWAT10SEXPLAIN))
   {
    @Html.Raw(HttpUtility.HtmlDecode(Model.AWAT10SEXPLAIN))
   }
</div>
<br/>
<div>
    <table class="table-ecool table-92Per table-hover table-bordered">
        <thead>
            <tr style="background-color:#990099">
                <th colspan="2"><font color="white">利率表</font></th>
            </tr>
            <tr style="background-color:#9a97cb">
                <th><font color="white">定存期別</font></th>
                <th><font color="white">年利率</font></th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.PeriodTypeList)
            {
                <tr style="background-color:#FFCCCC">
                    <td>@Html.DisplayFor(modelItem => item.PERIOD_DESC)</td>
                    <td>@item.PERIOD_RATE.Value.ToString("P")</td>
                </tr>
            }
        </tbody>
    </table>
    *本功能為坊間銀行定存的簡易版，主要目的為了讓學生感受定存理財的概念。</br>
    *本酷幣定存之年利率遠高於坊間銀行，原因為學生酷幣點數不高，若比照坊間利率，可能利息趨近於零，達不到定存效果，故提高年利率實施。
</div>


