/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Latin/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Latin={directory:"Latin/Regular",family:"AsanaMathJax_Latin",testString:"\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00B2\u00B3\u00B6\u00B8\u00B9\u00BA\u00BB\u00BC",32:[0,0,249,0,0],161:[469,225,277,81,197],162:[562,101,499,61,448],164:[531,-96,499,30,470],166:[713,172,210,76,135],169:[705,164,906,18,889],170:[709,-422,332,24,310],171:[428,-71,499,50,450],178:[686,-271,299,6,284],179:[686,-261,299,5,281],182:[694,150,627,39,589],184:[-10,225,332,96,304],185:[689,-271,299,32,254],186:[709,-416,332,10,323],187:[428,-71,499,50,450],188:[692,3,749,30,727],189:[692,3,749,15,735],190:[689,3,749,15,735],191:[469,231,443,43,395],192:[908,3,777,15,756],193:[908,3,777,15,756],194:[908,3,777,15,756],195:[871,3,777,15,756],196:[868,3,777,15,756],197:[943,3,777,15,756],198:[692,3,943,-10,908],199:[709,225,708,22,670],200:[908,3,610,22,572],201:[908,3,610,22,572],202:[908,3,610,22,572],203:[868,3,610,22,572],204:[908,3,336,22,315],205:[908,3,336,22,315],206:[908,3,336,13,325],207:[868,3,336,19,318],208:[692,3,773,14,751],209:[871,20,830,17,813],210:[908,20,785,22,764],211:[908,20,785,22,764],212:[908,20,785,22,764],213:[871,20,785,22,764],214:[868,20,785,22,764],216:[709,20,832,30,797],217:[908,20,777,12,759],218:[908,20,777,12,759],219:[908,20,777,12,759],220:[868,20,777,12,759],221:[908,3,666,9,654],222:[692,3,603,32,574],223:[731,9,555,23,519],224:[677,12,499,32,471],225:[677,12,499,32,471],226:[677,12,499,32,471],227:[640,12,499,32,471],228:[637,12,499,32,471],229:[712,12,499,32,471],230:[469,20,757,30,732],231:[469,225,443,26,413],232:[677,20,478,26,448],233:[677,20,478,26,448],234:[677,20,478,26,448],235:[637,20,478,26,448],236:[677,3,286,8,271],237:[677,3,286,21,279],238:[677,3,286,-12,300],239:[657,3,286,-6,293],241:[640,3,581,6,572],242:[677,20,545,32,514],243:[677,20,545,32,514],244:[677,20,545,32,514],245:[640,20,545,32,514],246:[637,20,545,32,514],248:[474,23,555,16,530],249:[677,12,602,18,581],250:[677,12,602,18,581],251:[677,12,602,18,581],252:[637,12,602,18,581],253:[682,283,555,12,544],254:[726,281,600,-2,544],255:[637,283,555,12,544]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Latin"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/Regular/Main.js"]);
