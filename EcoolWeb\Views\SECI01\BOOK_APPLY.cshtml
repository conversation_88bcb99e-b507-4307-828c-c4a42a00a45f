﻿@using ECOOL_APP.com.ecool.Models.entity;

@model EcoolWeb.Models.ADDT06ViewModel
@{
    ViewBag.Title = "我的閱讀認證";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    int i = 0;
}

<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>


<!-- 巡覽置頂，會固到頂部 -->
<nav class="navbar navbar-default navbar-fixed-top" role="navigation" id="fixed-top">
    <div class="row">
        <br />
        <div class="col-xs-2 text-center">
            <a role="button" onclick="FunNext()" class="btn btn-sm btn-default">
                〈
            </a>
        </div>
        <div class="col-xs-8 text-center">

            @if (!(Model.WhereIsColorboxForUser ?? false))
            {
                <a role="button" href='@Url.Action("index", "SECI01")' class="btn btn-sm btn-default">
                    回我的秘書
                </a>
            }
        </div>
        <div class="col-xs-2 text-center">
            <a role="button" onclick="FunPrev()" class="btn btn-sm btn-default">
                〉
            </a>
        </div>
    </div>
</nav>
<div style="height:80px"></div>

<div class="bar-div" id="content" style="z-index: 1">

    @Html.Partial("_Notice")
    @if (Model.ADDT06List.Count() == 0)
    {
        <div class="text-center">
            <h1>無任何資料</h1>
        </div>
    }
    else
    {


        foreach (var item in Model.ADDT06List)
        {
            List<Image_File> ImageModel = (List<Image_File>)ViewBag.ImageUrl;

            string ImaStr = string.Empty;

            if (ImageModel.Where(a => a.APPLY_NO == item.APPLY_NO).Any())
            {
                ImaStr = ImageModel.Where(a => a.APPLY_NO == item.APPLY_NO).Select(a => a.ImageUrl).FirstOrDefault();
            }

            <img src="~/Content/img/web-Bar-09.png" style="width:100%;" class="img-responsive " alt="Responsive image" />
            <div class="Div-EZ-reader">
                <div class="Details">
                    <div class="row">
                        <div class="col-md-5 col-sm-5 dl-horizontal-EZ">
                            <samp class="dt">
                                申請日
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(model => item.CRE_DATE, "ShortDateTime")
                            </samp>
                        </div>
                        <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                            <samp class="dt">
                                班級
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(model => item.CLASS_NO)
                            </samp>
                        </div>
                        <div class="col-md-4 col-sm-4 dl-horizontal-EZ">
                            <samp class="dt">
                                姓名
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(model => item.SNAME)
                            </samp>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-sm-12  dl-horizontal-EZ">
                            <samp class="dt">
                                書名
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(model => item.BOOK_NAME)
                            </samp>
                        </div>
                    </div>
                    <div style="height:15px"></div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="p-context">
                                @if (string.IsNullOrWhiteSpace(item.REVIEW))
                                {
                                    if (string.IsNullOrWhiteSpace(ImaStr) == false)
                                    {
                                        <img src="@Url.Content(ImaStr)" id="imgArticle" title="閱讀認證上傳圖片" style="float:right;margin:5px" href="@Url.Content(ImaStr)?wmode=transparent" class="img-responsive " />
                                    }
                                }
                                else
                                {
                                    <div class="col-md-12 p-context">
                                        @if (string.IsNullOrWhiteSpace(ImaStr) == false)
                                        {
                                            <img src="@Url.Content(ImaStr)" id="imgArticle" title="閱讀認證上傳圖片" style="float:right;margin:10px;max-height:250px;width:auto" href="@Url.Content(ImaStr)?wmode=transparent" class="img-responsive " />
                                        }
                                        @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW.Replace("\r\n", "<br />")))
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="row Div-btn-center">
                        @if (ViewBag.ShowOriginalArticle == "O")
                        {
                            @Html.ActionLink("批閱後文章", "BOOK_APPLY", new { APPLY_NO = item.APPLY_NO, ShowOriginal = false, Page = Model.Page }, new { @role = "button", @class = "btn btn-default" })
                        }
                        else if (ViewBag.ShowOriginalArticle == "V")
                        {
                            <a href="@Url.Action("BOOK_APPLY", (string)ViewBag.BRE_NO, new { APPLY_NO = item.APPLY_NO,ShowOriginal = true, Page = Model.Page })" role="button" class="btn btn-default">
                                學生原稿
                            </a>
                        }
                    </div>
                </div>
            </div>
            i++;
        }
    }
    @using (Html.BeginForm("BOOK_APPLY", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
    {
        @Html.HiddenFor(model => model.Page)
        @Html.HiddenFor(model => model.OrdercColumn)
        @Html.HiddenFor(model => model.SyntaxName)
        @Html.HiddenFor(model => model.WhereIsColorboxForUser)
    }
</div>

<!-- 巡覽置底 -->
<nav class="navbar navbar-default navbar-fixed-bottom" role="navigation" id="fixed-bottom">
    <br />
    <div class="row text-center">
        @Html.Pager(Model.ADDT06List.PageSize, Model.ADDT06List.PageNumber, Model.ADDT06List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(1)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
                )
    </div>
</nav>

<script type="text/javascript">

    $(document).ready(function () {

        var BarDiv = $('#content').height();
        var FixedBottom = $('#fixed-bottom').height();
        var FixedTop = $('#fixed-top').height();

        if (window.innerHeight > (BarDiv + FixedBottom + FixedTop)) {

            var NewHeight = window.innerHeight - (FixedBottom + FixedTop)

            $('#content').height(NewHeight + "px")
        }
    });

    $("#content").touchwipe({
        wipeLeft: function () {
            $("#prev").click();
        },
        wipeRight: function () {
            $("#next").click();
        },
        preventDefaultEvents: false
    });

    $(document).ready(function () {
        $("#imgArticle").colorbox({ opacity: 0.82 });
    });

    //分頁
    function FunPageProc(pageno) {

        $('#Page').val(pageno);
        form1.submit();
    }

    function FunNext() {
        $('#next').click();
    }

    function FunPrev() {
        $('#prev').click();
    }
</script>