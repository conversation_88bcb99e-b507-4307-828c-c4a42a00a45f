﻿
@model AWAI01BidDataIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}



@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


<div class="Div-EZ-reader">
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover">
                <caption class="Caption_Div_Left">
                    @Html.DisplayNameFor(m => m.AwaData.AWARD_NAME)：@Model.AwaData.AWARD_NAME <br />
                    @Html.DisplayNameFor(m => m.AwaData.BID_START_PRICE)：@Model.AwaData.BID_START_PRICE <br />
                    <br /><br />
                </caption>
                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().SHORT_NAME)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().SYEAR)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().SEMESTER)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().SEAT_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().NAME)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().BID_DATE)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.BidList.First().BID_CASH)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.BidList)
                    {
                        <tr>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SEMESTER)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.BID_DATE)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.BID_CASH)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
            <div style="height:15px"></div>
            <div class="btn-group btn-group-justified" role="group">
                共 @(Model.BidList.Count()) 人
            </div>
            <div style="height:25px"></div>
        </div>
    </div>
</div>








