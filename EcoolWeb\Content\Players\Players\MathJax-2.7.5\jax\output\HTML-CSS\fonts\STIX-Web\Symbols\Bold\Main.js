/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Symbols/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Symbols-bold"]={directory:"Symbols/Bold",family:"STIXMathJax_Symbols",weight:"bold",testString:"\u00A0\u2302\u2310\u2319\u2329\u232A\u2336\u233D\u233F\u23AF\u27C8\u27C9\u2980\u29B6\u29B7",32:[0,0,250,0,0],160:[0,0,250,0,0],8962:[774,0,926,55,871],8976:[399,-108,750,65,685],8985:[399,-108,750,65,685],9001:[732,193,445,69,399],9002:[732,193,445,46,376],9014:[751,156,926,85,841],9021:[694,190,924,80,844],9023:[732,200,728,55,673],9135:[297,-209,315,0,315],10184:[547,13,1025,62,943],10185:[547,13,1025,62,943],10624:[705,200,675,105,570],10678:[634,130,864,50,814],10679:[634,130,864,50,814],10680:[634,130,864,50,814],10688:[634,130,864,50,814],10689:[634,130,864,50,814],10692:[661,158,910,45,865],10693:[661,158,910,45,865],10694:[661,158,910,45,865],10695:[661,158,910,45,865]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Symbols-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Symbols/Bold/Main.js"]);
