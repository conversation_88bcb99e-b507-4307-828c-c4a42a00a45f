﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@using (Html.BeginForm("EditOne2", "ADDI09", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.USER_NAME)
    @Html.HiddenFor(model => model.CLASS_SEAT)
    @Html.HiddenFor(model => model.CASH)
    @Html.HiddenFor(model => model.CONTENT_TXT)
    @Html.HiddenFor(model => model.MEMO)
    @Html.HiddenFor(model => model.SUBJECT)
    @Html.HiddenFor(model => model.USER_NO)
    @Html.HiddenFor(model => model.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.IsFix)
    @Html.HiddenFor(model => model.IsRandom)
    @Html.HiddenFor(model => model.IsRandomHighPoint)

    <br />
    <span style="white-space: nowrap;font-size: 12pt;font-weight: bold; color:red">
        好運不常有，努力一定有！來試試好運囉！
    </span>
    <br />
    short total = 6;
    for (short i = 1; i <= total; i++)
    {
        string filename = Url.Content("~/Content/img/") + "monkey-0" + i.ToString() + ".png";
        <img src='@filename' class="col-md-3 col-sm-4 col-xs-6 img-responsive center-block" style="cursor:pointer;max-width:150px;height:auto" onclick="form1.submit();" />

    }

}
