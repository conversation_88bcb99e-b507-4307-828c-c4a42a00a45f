/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsSm/Bold/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXIntegralsSm-bold"],{32:[0,0,250,0,0],160:[0,0,250,0,0],8747:[732,193,562,41,618],8748:[732,193,870,41,926],8749:[732,193,1179,41,1235],8750:[732,193,626,41,618],8751:[732,193,934,41,926],8752:[732,193,1243,41,1235],8753:[732,193,626,41,618],8754:[732,193,626,41,618],8755:[732,193,626,41,618],10764:[732,193,1488,41,1544],10765:[732,193,578,41,618],10766:[732,193,578,41,618],10767:[732,193,626,41,618],10768:[732,193,562,41,618],10769:[732,193,626,41,618],10770:[732,193,579,41,618],10771:[732,193,581,41,618],10772:[732,193,688,41,652],10773:[732,193,626,41,618],10774:[732,193,579,41,618],10775:[732,193,646,8,646],10776:[732,193,578,41,618],10777:[732,193,559,41,618],10778:[732,193,559,41,618],10779:[802,193,555,41,611],10780:[732,268,556,41,612]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsSm/Bold/All.js");
