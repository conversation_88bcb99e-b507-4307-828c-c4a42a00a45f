@model IEnumerable<ECOOL_APP.com.ecool.Models.DTO.ADDI05AnswerViewModel>
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    int No = 0;
    int? NowQ_TYPE = 0;

    if (ViewBag.PreviewY == "Y")
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@if (ViewBag.PreviewY != "Y")
{
    @Html.Partial("_Title_Secondary")
    @Html.Partial("_Notice")
    <div class="form-group">
        <a class="btn btn-sm btn-sys" role="button" onclick="onGo('Index')">
            回有獎徵答首頁
        </a>
        <a class="btn btn-sm btn-sys" role="button" onclick="onGo('detail')">
            回詳細活動內容
        </a>
    </div>
}

@using (Html.BeginForm("Answer", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div style="text-align:center; margin: 0px auto;">
        <img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive App_hide" alt="Responsive image" style="text-align:center; margin: 0px auto;" />
        <div class="Div-EZ-ADDI05">
            <div class="Details">
                <div style="height:15px"></div>
                <div class="Caption_Div">
                    活動名稱：@ViewBag.DIALOG_NAME
                </div>
                <div style="height:15px"></div>
                <div class="table-92Per" style="margin: 0px auto; ">
                    @foreach (var item in Model)
                    {

                        No++;
                        <div class="p-context">
                            @if (NowQ_TYPE != item.uADDT12.Q_TYPE)
                            {
                                if (NowQ_TYPE != 0)
                                {
                                    <div style="height:15px"></div>
                                }

                                NowQ_TYPE = item.uADDT12.Q_TYPE;

                                <div class="form-group">
                                    <samp class="label_dt">@item.uADDT12.Q_TYPE_NAME </samp>
                                </div>
                            }
                            @Html.Hidden("[" + (No - 1).ToString() + "].uADDT12.DIALOG_ID", item.uADDT12.DIALOG_ID)
                            @Html.Hidden("[" + (No - 1).ToString() + "].uADDT12.Q_NUM", item.uADDT12.Q_NUM)
                            @Html.Hidden("[" + (No - 1).ToString() + "].uADDT12.O_Orderby", (No - 1).ToString())

                            <div style="height:1px;background-color:#DDDDDD;width:99%"></div>
                            @Html.ValidationMessage(item.uADDT12.Q_NUM.ToString(), "", new { @class = "text-danger" })
                            <br />
                            @{
                                string label_dt_S = "Q" + No + ".";
                            }
                            <samp class="label_dt_S">@label_dt_S</samp>
                            @if (ViewBag.PreviewY == "Y")
                            {
                                <font style="color:red">答案:@item.uADDT12.TRUE_ANS</font>
                            }
                            <br />
                            @Html.Raw(HttpUtility.HtmlDecode(item.uADDT12.Q_TEXT))
                            <div style="height:5px"></div>
                            @if (item.uADDT12.Q_TYPE == 1)
                            {
                                <div class="form-group">
                                    <div>
                                        @Html.RadioButton("[" + (No - 1).ToString() + "].Ans", "O")
                                        <samp>是</samp>
                                    </div>
                                    <div>
                                        @Html.RadioButton("[" + (No - 1).ToString() + "].Ans", "X")
                                        <samp>否</samp>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="form-inline">

                                    @if (string.IsNullOrWhiteSpace(item.uADDT12.Q_ANS1) == false)
                                    {
                                        <div class="form-group" style="padding-right:20px;width:100%">
                                            <strong>1.</strong>
                                            <label class="radio-inline">
                                                @Html.RadioButton("[" + (No - 1).ToString() + "].Ans", "1")<samp>@Html.Raw(item.uADDT12.Q_ANS1)</samp>
                                            </label>
                                        </div>
                                    }

                                    @if (string.IsNullOrWhiteSpace(item.uADDT12.Q_ANS2) == false)
                                    {
                                        <div class="form-group" style="padding-right:20px;width:100%">
                                            <strong>2.</strong>
                                            <label class="radio-inline">
                                                @Html.RadioButton("[" + (No - 1).ToString() + "].Ans", "2")<samp>@Html.Raw(item.uADDT12.Q_ANS2)</samp>
                                            </label>
                                        </div>

                                    }

                                    @if (string.IsNullOrWhiteSpace(item.uADDT12.Q_ANS3) == false)
                                    {
                                        <div class="form-group" style="padding-right:20px;width:100%">
                                            <strong>3.</strong>
                                            <label class="radio-inline">
                                                @Html.RadioButton("[" + (No - 1).ToString() + "].Ans", "3")<samp>@Html.Raw(item.uADDT12.Q_ANS3)</samp>
                                            </label>
                                        </div>

                                    }

                                    @if (string.IsNullOrWhiteSpace(item.uADDT12.Q_ANS4) == false)
                                    {
                                        <div class="form-group" style="padding-right:20px;width:100%">
                                            <strong>4.</strong>
                                            <label class="radio-inline">
                                                @Html.RadioButton("[" + (No - 1).ToString() + "].Ans", "4")<samp>@Html.Raw(item.uADDT12.Q_ANS4)</samp>
                                            </label>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
            <div style="height:25px"></div>
        </div>
        <div style="height:25px"></div>
        <div class="text-center">
            @if (ViewBag.PreviewY == "Y")
            {
            }
            else
            {
                if (ViewBag.DataBool)
                {
                    <a class="btn btn-default" role="button" onclick="form1.mode.value = 'again'; onGo('Answer')">
                        重新回到有獎徵答頁
                    </a>
                }
                else
                {
                    <a class="btn btn-default" role="button" onclick="onGo('Answer')">
                        送出
                    </a>
                }
            }
        </div>
    </div>

    @Html.Hidden("SearchContents", (string)TempData["SearchContents"])
    @Html.Hidden("OrderByName", (string)TempData["OrderByName"])
    @Html.Hidden("SyntaxName", (string)TempData["SyntaxName"])
    @Html.Hidden("page", (int?)TempData["page"])
    @Html.Hidden("DIALOG_ID", (string)TempData["DIALOG_ID"])
    @Html.Hidden("mode")

}

@section script{
    <sceript none="cmlvaw">

        windows.ADDI05_Answer_URL = {
        answer:"Answer",
        Index:'@Url.Action("Index", (string)ViewBag.BRE_NO)',
        detail:'@Url.Action("detail", (string)ViewBag.BRE_NO)',
        Answer:'@Url.Action("Answer", (string)ViewBag.BRE_NO)',
        SysUrl:'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'
        };

    </sceript>
    
    }