﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'sk', {
	button: {
		title: 'Vlastnosti tlačidla',
		text: 'Text (Hodnota)',
		type: 'Typ',
		typeBtn: 'Tlačidlo',
		typeSbm: 'Odosla<PERSON>',
		typeRst: 'Resetovať'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Vlastnosti zaškrtávacieho políčka',
		radioTitle: 'Vlastnosti prepínača (radio button)',
		value: '<PERSON>dn<PERSON>',
		selected: '<PERSON><PERSON><PERSON><PERSON><PERSON> (selected)',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Vlastnosti formulára',
		menu: 'Vlastnosti formulára',
		action: 'Akcia (action)',
		method: 'Metóda (method)',
		encoding: 'Kódovanie (encoding)'
	},
	hidden: {
		title: 'Vlastnosti skryté<PERSON> poľa',
		name: '<PERSON><PERSON><PERSON><PERSON> (name)',
		value: 'Hodnota'
	},
	select: {
		title: 'Vlastnosti rozbaľovacieho zoznamu',
		selectInfo: 'Informácie o výbere',
		opAvail: 'Dostupné možnosti',
		value: 'Hodnota',
		size: 'Veľkosť',
		lines: 'riadkov',
		chkMulti: 'Povoliť viacnásobný výber',
		required: 'Required', // MISSING
		opText: 'Text',
		opValue: 'Hodnota',
		btnAdd: 'Pridať',
		btnModify: 'Upraviť',
		btnUp: 'Hore',
		btnDown: 'Dole',
		btnSetValue: 'Nastaviť ako vybranú hodnotu',
		btnDelete: 'Vymazať'
	},
	textarea: {
		title: 'Vlastnosti textovej oblasti (textarea)',
		cols: 'Stĺpcov',
		rows: 'Riadkov'
	},
	textfield: {
		title: 'Vlastnosti textového poľa',
		name: 'Názov (name)',
		value: 'Hodnota',
		charWidth: 'Šírka poľa (podľa znakov)',
		maxChars: 'Maximálny počet znakov',
		required: 'Required', // MISSING
		type: 'Typ',
		typeText: 'Text',
		typePass: 'Heslo',
		typeEmail: 'Email',
		typeSearch: 'Hľadať',
		typeTel: 'Telefónne číslo',
		typeUrl: 'URL'
	}
} );
