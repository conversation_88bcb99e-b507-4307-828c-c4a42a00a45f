﻿@model GAAI01SearchClassIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<style>
    .modal {
        text-align: center;
    }

    @@media screen and (min-width: 768px) {
        .modal:before {
            display: inline-block;
            vertical-align: middle;
            content: " ";
            height: 100%;
        }
    }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }
</style>
<link href="~/Scripts/tablesorter/dist/css/theme.blue.css" rel="stylesheet" />
<link href="~/Scripts/tablesorter/dist/css/theme.green.min.css" rel="stylesheet" />
<link href="~/Scripts/tablesorter/dist/css/theme.blackice.min.css" rel="stylesheet" />
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "SearchClassIndex" });
}

@using (Html.BeginForm("SearchClassIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.IsSearch)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.WhereCLASS_NO, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereCLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-md-3 control-label">
                        時間
                    </label>
                    <div class="col-md-9">
                        @if (ViewBag.AlarmSyearSemesterItem != null)
                        {
                            @Html.DropDownListFor(m => m.WhereSYEARSEMESTER, (IEnumerable<SelectListItem>)ViewBag.AlarmSyearSemesterItem, new { @class = "form-control" })
                        }
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereSYEARSEMESTER, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="button" class="btn btn-default" onclick="onSearch()">
                    <span class="fa fa-check-circle" aria-hidden="true"></span>搜尋
                </button>
            </div>
        </div>
    </div>

    if (Model.IsSearch == 1)
    {
        if (Model.ClassMain != null && Model.gAAT01s?.Count > 0)
        {
            <br />
            <div class="text-center">
                <div id="ClassListDiv" class="text-center">
                    <h4>
                        @(Model.WhereSYEAR)學年度 第@(Model.WhereSEMESTER)學期 @(Model.ClassMain.SCHOOL_NAME) 學生防身警報器班級配戴檢核表
                    </h4>
                    <div class="row">
                        <div class="col-md-offset-1 col-md-8 text-left">
                            @(HRMT01.ParserGrade(Model.ClassMain.GRADE)) @(Model.ClassMain.CLASS_NO)班，全班人數：@(Model.ClassMain.STUDENT_NUMBER)人 導師：@(Model.ClassMain.TEACHER_NAME)
                        </div>
                        <div class="col-md-2 col-md-offset-1 text-right">
                            <button type="button" class="btn btn-default btn-xs no-print" onclick="onprint('#ClassListDiv')">
                                列印
                            </button>
                        </div>
                    </div>
                    <table id="ClassListTable" class="tablesorter-blue">
                        <thead>
                            <tr class="text-center">
                                <td>日期</td>
                                <td>有填報資料</td>
                                <td>未配戴人數</td>
                                <td>週配戴率</td>
                                <td>未配戴原因</td>
                                <td class="no-print sorter-false">編輯</td>
                            </tr>
                        </thead>
                        @if (Model.gAAT01s?.Count > 0)
                        {
                            <tbody>
                                @foreach (var item in Model.gAAT01s)
                                {

                                    var ItemClassList = Model.ClassList.Where(a => a.ALARM_ID == item.ALARM_ID).FirstOrDefault();

                                    <tr class="text-center">
                                        <td nowrap="nowrap">@(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATES)) - @(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATEE))</td>

                                        @if (ItemClassList != null)
                                        {
                                            <td>V</td>
                                            <td>@ItemClassList.UN_WEAR_NUMBER</td>
                                            <td>@ItemClassList.WEAR_RATE.Value.ToString("#.#%")</td>
                                            <td class="text-left" style="max-width:200px">
                                                @foreach (var ItemUnWearType in Enum.GetValues(typeof(GAAT02_U.UnWearType)))
                                                {
                                                    var ItemUnWearTypeCount = Model.WearDetailList.Where(a => a.ALARM_ID == item.ALARM_ID && a.CLASS_NO == Model.ClassMain.CLASS_NO && a.UN_WEAR_TYPE == (byte)ItemUnWearType).Count();
                                                    <span>@(ItemUnWearType.ToString()) : @(ItemUnWearTypeCount)人</span>

                                                }
                                            </td>
                                            <td nowrap="nowrap" class="no-print"><a class="btn-primary btn-xs" href="@Url.Action("WearIndex",new {WhereALARM_ID=item.ALARM_ID,WhereCLASS_NO=Model.WhereCLASS_NO })">編輯</a></td>

                                        }
                                        else
                                        {
                                            <td>X</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td nowrap="nowrap" class="no-print"><a class="btn-primary btn-xs" href="@Url.Action("WearIndex",new {WhereALARM_ID=item.ALARM_ID,WhereCLASS_NO=Model.WhereCLASS_NO })">編輯</a></td>
                                        }
                                    </tr>
                                }
                            </tbody>

                        }
                    </table>
                    <div class="text-center print-only">
                        <table style="width:95%">
                            <tr>
                                <td>導師</td>
                                <td>生教組長</td>
                                <td>學務主任</td>
                                <td>校長</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <br />
                <div id="WearDetailListDiv">
                    <div class="row no-print">
                        <div class="col-md-11 col-md-offset-1 text-right">
                            <button type="button" class="btn btn-default btn-xs" onclick="onprint('#WearDetailListTable')">
                                列印
                            </button>
                        </div>
                    </div>
                    <table id="WearDetailListTable" class="tablesorter-blue">
                        <thead>
                            <tr class="text-center">
                                <td>日期</td>
                                <td>沒配戴學生/原因</td>
                            </tr>
                        </thead>
                        @if (Model.gAAT01s?.Count > 0)
                        {
                            <tbody>
                                @foreach (var item in Model.gAAT01s)
                                {
                                    var boolClassList = Model.ClassList.Where(a => a.ALARM_ID == item.ALARM_ID).Any();

                                    <tr class="text-center">
                                        <td nowrap="nowrap">@(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATES)) - @(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATEE))</td>

                                        @if (boolClassList)
                                        {
                                            var WearDetailLists = Model.WearDetailList.Where(a => a.ALARM_ID == item.ALARM_ID && a.CLASS_NO == Model.ClassMain.CLASS_NO).Select(a => $"{a.NAME} {a.SEAT_NO} ({GAAT02_U.GetUnWearTypeDesc(a.UN_WEAR_TYPE)})").ToList();

                                            if (WearDetailLists?.Count > 0)
                                            {
                                                <td class="text-left">@String.Join("、", WearDetailLists.ToArray())</td>
                                            }
                                            else
                                            {
                                                <td class="text-left">全配戴</td>
                                            }

                                        }
                                        else
                                        {
                                            <td class="text-left">未填報</td>
                                        }
                                    </tr>
                                }
                            </tbody>

                        }
                    </table>
                </div>
            </div>
        }
        else
        {
            <div class="text-center">
                <h2>
                    查無任何資料
                </h2>
            </div>
        }

    }

}

@section Scripts {
    <script src="~/Scripts/printThis/printThis.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.widgets.js"></script>
    <script language="JavaScript">

        var targetFormID = '#form1';

        $(function() {
            $(".tablesorter-blue").tablesorter();
        });

        function onSearch()
        {
            $('#@Html.IdFor(m=>m.IsSearch)').val(1)
            $(targetFormID).attr("action", "@Url.Action("SearchClassIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onprint(DivId) {
              $(DivId).printThis();
        }
    </script>
}