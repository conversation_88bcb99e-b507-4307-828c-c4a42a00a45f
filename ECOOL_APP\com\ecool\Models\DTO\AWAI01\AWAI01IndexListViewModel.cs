﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public  class AWAI01IndexListViewModel
    {
        /// <summary>
        ///獎品代號
        /// </summary>
        [DisplayName("獎品代號")]
        public int? AWARD_NO { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///獎品類型
        /// </summary>
        [DisplayName("獎品類型")]
        public string AWARD_TYPE { get; set; }

        /// <summary>
        ///獎品名稱
        /// </summary>
        [DisplayName("獎品名稱")]
        public string AWARD_NAME { get; set; }

        /// <summary>
        ///兌換點數
        /// </summary>
        [DisplayName("兌換點數")]
        public int? COST_CASH { get; set; }

        /// <summary>
        ///獎品庫存數
        /// </summary>
        [DisplayName("庫存數")]
        public int? QTY_STORAGE { get; set; }

        /// <summary>
        ///獎品登記兌換數
        /// </summary>
        [DisplayName("兌換數")]
        public int? QTY_TRANS { get; set; }

        /// <summary>
        ///兌換開始時間
        /// </summary>
        [DisplayName("兌換開始時間")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm}", ApplyFormatInEditMode = true)]
        public DateTime? SDATETIME { get; set; }

        /// <summary>
        ///截止段換時間
        /// </summary>
        [DisplayName("截止段換時間")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm}", ApplyFormatInEditMode = true)]
        public DateTime? EDATETIME { get; set; }

        /// <summary>
        ///獎品描述
        /// </summary>
        [DisplayName("獎品描述")]
        public string DESCRIPTION { get; set; }

        /// <summary>
        ///獎品描述
        /// </summary>
        [DisplayName("獎品描述")]
        public string IMG_FILE { get; set; }

        /// <summary>
        ///圖檔儲存路徑
        /// </summary>
        [DisplayName("圖檔儲存路徑")]
        public string IMG2_FILE { get; set; }

        /// <summary>
        /// YOUTUBE影片路徑
        /// </summary>
        [DisplayName("影片儲存路徑")]
        public string VIDEO_PATH { get; set; }

        /// <summary>
        ///獎品狀態
        /// </summary>
        [DisplayName("獎品狀態")]
        public string AWARD_STATUS { get; set; }

        /// <summary>
        ///熱門獎項
        /// </summary>
        [DisplayName("熱門獎項")]
        public string HOT_YN { get; set; }

        /// <summary>
        /// 行動FULLSCREEN頁面
        /// Y: 限行動支付頁面  N:限一般獎品兌換  B:兩者皆可
        /// </summary>
        [DisplayName("行動頁面")]
        [Description("是否顯示於行動支付頁面")]
        public string FULLSCREEN_YN { get; set; }

        /// <summary>
        ///是否限制對象
        /// </summary>
        [DisplayName("是否限制對象")]
        public string BUY_PERSON_YN { get; set; }

        /// <summary>
        /// 限制對象
        /// </summary>
        public string BUY_PERSON { get; set; }

        /// <summary>
        /// 是限制對象 => Hidden
        /// </summary>
        public bool IsHidden { get; set; }

        /// <summary>
        ///限制可領取閱讀認證等級
        /// </summary>
        [DisplayName("限制可領取閱讀認證等級")]
        public byte? READ_LEVEL { get; set; }

        /// <summary>
        ///限制可領取閱讀護照等級
        /// </summary>
        [DisplayName("限制可領取閱讀護照等級")]
        public byte? PASSPORT_LEVEL { get; set; }

        /// <summary>
        ///限制數量(每一個可兌換的數量)
        /// </summary>
        [DisplayName("限制數量(每一個可兌換的數量)")]
        public int? QTY_LIMIT { get; set; }

        /// <summary>
        ///【備註說明】是否顯示於獎品清單中
        /// </summary>
        [DisplayName("【備註說明】是否顯示於獎品清單中")]
        public string SHOW_DESCRIPTION_YN { get; set; }

        /// <summary>
        ///是否通知
        /// </summary>
        [DisplayName("是否通知")]
        public string PUSH_YN { get; set; }

        /// <summary>
        ///起標價
        /// </summary>
        [DisplayName("起標價")]
        public int? BID_START_PRICE { get; set; }

        /// <summary>
        ///增加每單位的點數
        /// </summary>
        [DisplayName("增加每單位的點數")]
        public int? BID_PER_UNIT_PRICE { get; set; }

        [DisplayName("直購價")]
        public int? BID_BUY_PRICE { get; set; }

        /// <summary>
        /// 最高出價者
        /// </summary>
        [DisplayName("最高出價")]
        public double MAX_PRICE { get; set; }

        [DisplayName("對兌率")]
        public double RATE { get; set; }
    }
}