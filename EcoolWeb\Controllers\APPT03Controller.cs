﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using EntityFramework.Extensions;
using EcoolWeb.CustomAttribute;
using Dapper;
using log4net;
using System.Data.Entity.SqlServer;

namespace EcoolWeb.Controllers
{
    [SessionExpire]

    public class APPT03Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "APPT03";
        private string Bre_Name = BreadcrumbService.GetBRE_NAME("ZZZI30");
     
        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;
        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;


        private ECOOL_DEVEntities Db = new ECOOL_DEVEntities();

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg = string.Empty;

        public ActionResult Index(APPT03IndexViewModel Model)
        {
            this.Shared(Model.Panel_Title);
            if (Model == null) Model = new APPT03IndexViewModel();


            //只保留6小時
            var DiffDate = DateTime.Now.AddHours(-6);

            //Delete 暫存檔
            Db.APPT03_Q.Where(a => a.STATUS == APPT03.StatusVal.TempKey
            && a.CRE_DATE < DiffDate).Delete();


            if (Model.STATUS==null)
            {
                Model.STATUS = APPT03.StatusVal.TempKey;
            }


            if (string.IsNullOrWhiteSpace(Model.REF_KEY))
             {
                    Model.REF_KEY = Session.SessionID;
 
                    //Delete 暫存檔
                    Db.APPT03_Q.Where(a => a.STATUS == APPT03.StatusVal.TempKey
                    && (a.REF_KEY == Model.REF_KEY && a.REF_TABLE == Model.REF_TABLE)).Delete();
              }
             
            
            return View(Model);
        }

        /// <summary>
        /// 功能清單/選取清單
        /// </summary>
        /// <param name="APPT03QueryViewModel"></param>
        /// <returns></returns>
        public ActionResult _QuerySelect(APPT03QueryViewModel Model)
        {
            this.Shared();

           


            if (string.IsNullOrWhiteSpace(Model.whereSCHOOL_NO))
            {
                Model.whereSCHOOL_NO = DefaultSCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(Model.whereUSER_TYPE))
            {
                Model.whereUSER_TYPE = UserType.Student;
            }

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(Model.whereSCHOOL_NO, null, user);

            if (Model == null) Model = new APPT03QueryViewModel();

            if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.sys_role)
            {
                Model.USER_TYPE_LIST = UserType.GetList().Where(a=>a != UserType.Guest).ToList();
            }
            else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.role)
            {
                Model.Role_LIST = (from a in Db.HRMT24 orderby a.ROLE_LEVEL, a.ROLE_ID select a).ToList();

            }
            else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.grade)
            {
                Model.Grade_LIST = Enum.GetValues(typeof(HRMT01.GradeVal)).Cast<byte>().Select(x => x).ToList();
            }
            else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.Class)
            {
                Model.Class_LIST = HRMT01.GetClassList(DefaultSCHOOL_NO, ref Db);
            }
            else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.person)
            {



                ViewBag.UserTypeItem = UserType.GetList().Where(a => a != UserType.Guest)
                    .Select(x => new SelectListItem { Text = UserType.GetDesc(x), Value = x, Selected = x == Model.whereUSER_TYPE })
                    .Distinct().OrderBy(x => x.Value);

                ViewBag.GradeItem = HRMT01.GetGradeItems(Model.whereGRADE);
                ViewBag.ClassItem = HRMT01.GetClassListData(DefaultSCHOOL_NO, Model.whereGRADE, Model.whereCLASS_NO, ref Db);

                List<SelectListItem> PageSizeItems = new List<SelectListItem>();


                PageSizeItems.Add(new SelectListItem() { Text = "顯示5筆", Value = "5", Selected = Model.PageSize == 5 });
                PageSizeItems.Add(new SelectListItem() { Text = "顯示10筆", Value = "10", Selected = Model.PageSize == 10 });
                PageSizeItems.Add(new SelectListItem() { Text = "顯示20筆", Value = "20", Selected = Model.PageSize == 20 });
                PageSizeItems.Add(new SelectListItem() { Text = "顯示50筆", Value = "50", Selected = Model.PageSize == 50 });
                PageSizeItems.Add(new SelectListItem() { Text = "顯示100筆", Value = "100", Selected = Model.PageSize == 100 });
                PageSizeItems.Add(new SelectListItem() { Text = "顯示全部筆數", Value = int.MaxValue.ToString(), Selected = Model.PageSize == int.MaxValue });
                ViewBag.PageSizeItems = PageSizeItems;


                var Temp = (from b in  Db.HRMT01
                            join s in Db.BDMT01 on b.SCHOOL_NO equals s.SCHOOL_NO
                            where b.USER_STATUS == UserStaus.Enabled
                            && b.SCHOOL_NO == Model.whereSCHOOL_NO
                            && b.USER_TYPE == Model.whereUSER_TYPE
                             && !(from t in Db.APPT03_Q
                                  where t.REF_TABLE == Model.REF_TABLE
                                  && t.REF_KEY == Model.REF_KEY
                                  && t.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.person
                                  select t.SCHOOL_NO + "_" + t.USER_NO).Contains(b.USER_KEY)
                            orderby b.SCHOOL_NO, b.USER_TYPE, b.GRADE, b.CLASS_NO, b.SEAT_NO
                            select new APPT03Hrmt01ListViewModel
                            {
                                SCHOOL_NO =b.SCHOOL_NO,
                                SHORT_NAME = s.SHORT_NAME,
                                USER_TYPE = b.USER_TYPE,
                                USER_NO = b.USER_NO,
                                NAME = b.NAME,
                                GRADE = b.GRADE,
                                CLASS_NO = b.CLASS_NO,
                                SEAT_NO = b.SEAT_NO,
                            }
                         );


                if (string.IsNullOrWhiteSpace(Model.whereUSER_NO) == false)
                {
                    Temp = Temp.Where(x => x.USER_NO.Contains(Model.whereUSER_NO));
                }

                if (string.IsNullOrWhiteSpace(Model.whereCLASS_NO) == false)
                {
                    Temp = Temp.Where(x => x.CLASS_NO == Model.whereCLASS_NO);
                }

                if (string.IsNullOrWhiteSpace(Model.whereGRADE) == false)
                {
                    Temp = Temp.Where(x => x.GRADE.Value.ToString() == Model.whereGRADE);
                }

                if (string.IsNullOrWhiteSpace(Model.whereNAME) == false)
                {
                    Temp = Temp.Where(x => x.NAME.Contains(Model.whereNAME));
                }


                if (string.IsNullOrWhiteSpace(Model.OrderByName)==false)
                {
                    Temp = Temp.dynamicOrderBy(Model.OrderByName, Model.SyntaxName);
                }

                if (!string.IsNullOrWhiteSpace(Model.OrderByName))
                {
                    if (Model.SyntaxName == "Desc")
                    {
                        switch (Model.OrderByName)
                        {
                            case "SHORT_NAME":
                                Temp = Temp.OrderByDescending(b => b.SHORT_NAME).ThenByDescending(b => b.USER_TYPE).ThenByDescending(b => b.CLASS_NO).ThenByDescending(b => b.SEAT_NO);
                                break;
                            case "USER_TYPE":

                                Temp = Temp.OrderByDescending(b => b.USER_TYPE).ThenByDescending(b => b.SCHOOL_NO).ThenByDescending(b => b.CLASS_NO).ThenByDescending(b => b.SEAT_NO);
                                break;
                            case "NAME":

                                Temp = Temp.OrderByDescending(b => b.NAME).ThenByDescending(b => b.CLASS_NO).ThenByDescending(b => b.SEAT_NO);
                                break;
                            case "CLASS_NO":

                                Temp = Temp.OrderByDescending(b => b.CLASS_NO).ThenByDescending(b => b.SEAT_NO);
                                break;
                            case "SEAT_NO":

                                Temp = Temp.OrderByDescending(b => b.SEAT_NO).ThenByDescending(b => b.CLASS_NO);
                                break;
                            default:
                                Temp = Temp.OrderByDescending(b => b.SCHOOL_NO).ThenByDescending(b => b.USER_TYPE).ThenByDescending(b => b.CLASS_NO).ThenByDescending(b => b.SEAT_NO);
                                break;
                        }
                    }
                    else
                    {
                        switch (Model.OrderByName)
                        {
                            case "SHORT_NAME":
                                Temp = Temp.OrderBy(b => b.SHORT_NAME).ThenBy(b => b.USER_TYPE).ThenBy(b => b.CLASS_NO).ThenBy(b => b.SEAT_NO);
                                break;
                            case "USER_TYPE":

                                Temp = Temp.OrderBy(b => b.USER_TYPE).ThenBy(b => b.SCHOOL_NO).ThenBy(b => b.CLASS_NO).ThenBy(b => b.SEAT_NO);
                                break;
                            case "NAME":
                           
                                Temp = Temp.OrderBy(b => b.NAME).ThenBy(b => b.CLASS_NO).ThenBy(b => b.SEAT_NO);
                                break;
                            case "CLASS_NO":
                       
                                Temp = Temp.OrderBy(b => b.CLASS_NO).ThenBy(b => b.SEAT_NO);
                                break;
                            case "SEAT_NO":
                             
                                Temp = Temp.OrderBy(b => b.SEAT_NO).ThenBy(b => b.CLASS_NO);
                                break;
                            default:
                                Temp = Temp.OrderBy(b => b.SCHOOL_NO).ThenBy(b => b.USER_TYPE).ThenBy(b => b.CLASS_NO).ThenBy(b => b.SEAT_NO);
                                break;
                        }
                    }
                }
                else
                {
                    //orderby b.SCHOOL_NO, b.USER_TYPE, b.GRADE, b.CLASS_NO, b.SEAT_NO
                    Temp = Temp.OrderBy(b => b.SCHOOL_NO).ThenBy(b => b.USER_TYPE).ThenBy(b => b.CLASS_NO).ThenBy(b => b.SEAT_NO);
                }


                Model.HRMT01List = Temp.ToPagedList(Model.Page > 0 ? Model.Page - 1 : 0, Model.PageSize);
            }



            return PartialView(Model);
        }




        /// <summary>
        /// 已選取清單
        /// </summary>
        /// <param name="REF_TABLE"></param>
        /// <param name="REF_KEY"></param>
        /// <returns></returns>
        public ActionResult _QuerySelectDataList(APPT03EditListViewModel Model)
        {
            this.Shared();

            if (Model == null) Model = new APPT03EditListViewModel();

            if (string.IsNullOrWhiteSpace(Model.ITEM_VAL) == false)
            {

  
                    APPT03_Q DT = new APPT03_Q();

                    DT.REF_TABLE = Model.REF_TABLE;
                    DT.REF_KEY = Model.REF_KEY;
                    DT.BTN_TYPE = Model.BTN_TYPE;
                    DT.SCHOOL_NO = Model.whereSCHOOL_NO;
                    DT.USER_TYPE = "";
                    DT.ROLE_ID = "";
                    DT.GRADE = 0;
                    DT.CLASS_NO = "";
                    DT.USER_NO = "";

                    if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.sys_role)
                    {
                        DT.USER_TYPE = Model.ITEM_VAL;
                        DT.CONTENT_TXT = UserType.GetDesc(DT.USER_TYPE);
                    }
                    else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.role)
                    {
                        DT.ROLE_ID = Model.ITEM_VAL;

                       var HT24 = Db.HRMT24.Where(a => a.ROLE_ID == DT.ROLE_ID).FirstOrDefault();

                        DT.CONTENT_TXT = HT24.ROLE_NAME;

                    }
                    else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.grade)
                    {
                        DT.GRADE =Convert.ToByte(Model.ITEM_VAL);

                        DT.CONTENT_TXT = HRMT01.ParserGrade(DT.GRADE);
                    }
                    else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.Class)
                    {
                        DT.CLASS_NO = Model.ITEM_VAL;

                        var HT03 = Db.HRMT03.Where(a => a.SCHOOL_NO == DT.SCHOOL_NO && a.CLASS_NO == DT.CLASS_NO).FirstOrDefault();

                        DT.CONTENT_TXT = HT03.CLASSNAME;
                    }
                    else if (Model.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.person)
                    {
                        DT.USER_NO = Model.ITEM_VAL;

                        var HT01 = Db.HRMT01.Where(a => a.SCHOOL_NO == DT.SCHOOL_NO && a.USER_NO == DT.USER_NO).FirstOrDefault();

                        if (HT01.USER_TYPE==UserType.Student)
                        {
                            DT.CONTENT_TXT =  HT01.USER_NO + " " + HT01.NAME + " / " + HT01.CLASS_NO + " / " + HT01.SEAT_NO;
                        }
                        else
                        {
                            DT.CONTENT_TXT =  HT01.USER_NO + " " + HT01.NAME;
                        }

                    }


                    DT.CRE_PERSON = user.USER_KEY;
                    DT.CRE_DATE = DateTime.Now;
                    DT.STATUS = Model.STATUS;


                  var Q= (from q in Db.APPT03_Q
                    where  q.REF_TABLE == DT.REF_TABLE
                        && q.REF_KEY == DT.REF_KEY
                        && q.BTN_TYPE == DT.BTN_TYPE
                        && q.SCHOOL_NO == DT.SCHOOL_NO
                        && q.USER_TYPE == DT.USER_TYPE
                        && q.ROLE_ID == DT.ROLE_ID
                        &&  q.GRADE == DT.GRADE
                        && q.CLASS_NO == DT.CLASS_NO
                        && q.USER_NO == DT.USER_NO
                           select q).FirstOrDefault();


                    if (Q == null  && Model.DataType == APPT03EditListViewModel.DataTypeVal.DataTypeAdd)
                    {
                        Db.APPT03_Q.Add(DT);
                        Db.SaveChanges();
                    }
                    else if (Q != null && Model.DataType == APPT03EditListViewModel.DataTypeVal.DataTypeDel)
                    {
                        Db.APPT03_Q.Remove(Q);
                        Db.SaveChanges();
                    }
            }



            Model.DataList = (from t in Db.APPT03_Q
                              join s in Db.BDMT01 on t.SCHOOL_NO equals s.SCHOOL_NO into ps
                              from s in ps.DefaultIfEmpty()
                              where t.REF_TABLE == Model.REF_TABLE
                              && t.REF_KEY == Model.REF_KEY
                              select new APPT03QListViewModel
                              {
                                  SCHOOL_NO = t.SCHOOL_NO,
                                  SHORT_NAME = s.SHORT_NAME==null ? "全部學校": s.SHORT_NAME,
                                  BTN_TYPE = t.BTN_TYPE,
                                  ITEM_VAL = (t.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.sys_role ? t.USER_TYPE :
                                              t.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.role ? t.ROLE_ID :
                                              t.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.grade ? t.GRADE.ToString() :
                                              t.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.Class ? t.CLASS_NO :
                                              t.BTN_TYPE == APPT03_Q.BTN_TYPE_VAL.person ? t.USER_NO : ""),
                                  CONTENT_TXT =t.CONTENT_TXT
                              }).ToList();

            Model.SelectDataCount = Model.DataList.Count();

            return PartialView(Model);
        }


        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Edit(APPT03IndexViewModel Model)
        {
            this.Shared("訊息編輯");

            if (string.IsNullOrWhiteSpace(Model.DATA_TYPE)==false)
            {
                if (Model.DATA_TYPE == DATA_TYPE_VAL.DATA_TYPE_A)
                {

                    if (ModelState.IsValid == false) ErrorMsg = "錯誤\r\n";

                    if (ModelState.IsValid) //沒有錯誤
                    {
                        PushService.QAppt03ToAPPT03Push(Model.REF_TABLE, Model.REF_KEY,null, ref Db, user, out ErrorMsg, Model.MESSAGE, "APPT03", "Edit");
                    }

                    if (string.IsNullOrWhiteSpace(ErrorMsg))
                    {
                        TempData["StatusMessage"] = "發送成功";
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        TempData["StatusMessage"] = ErrorMsg;
                    }
                }
            }
            else
            {
                ModelState.Clear();
            }


            return View(Model);
        }

    


        #region  Shared
        private void Shared(string Panel_Title="")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (string.IsNullOrWhiteSpace(Panel_Title)==false)
            {

                if (string.IsNullOrWhiteSpace(Bre_Name) == false)
                {
                    ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;
                }
                else
                {
                    ViewBag.Panel_Title =  Panel_Title;
                }
            }
           

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }
        #endregion


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Db.Dispose();
            }
            base.Dispose(disposing);
        }

        public static class DATA_TYPE_VAL
        {
            /// <summary>
            /// 新增
            /// </summary>
            public static string DATA_TYPE_A = "A";

        }


    }
}