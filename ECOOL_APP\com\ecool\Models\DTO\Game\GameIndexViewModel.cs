﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameIndexViewModel
    {

        /// <summary>
        /// 查詢條件
        /// </summary>
        public GameSearchViewModel Search { get; set; }
        public string GAME_NO { get; set; }
        public string LEVEL_NO { get; set; }
        public string Coupons_ITem { get; set; }
        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<GameListDataViewModel> ListData;

        /// <summary>
        /// 建構式 預設值 
        /// </summary>
        public GameIndexViewModel()
        {
            PageSize = PageGlobal.DfTeamPageSize;
        }


    }
}
