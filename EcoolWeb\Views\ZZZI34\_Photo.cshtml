﻿
@model ZZZI34EditPhotoViewModel
@using ECOOL_APP.com.ecool.service



@using (Html.BeginCollectionItemSou("Photo", "People", Model.index, false))
{
    var PhotoIndex = Html.GetIndex("Photo");

    <div class="row border-bottom mb-4 pb-1" id="Tr@(PhotoIndex)">
        @Html.HiddenFor(m => m.ART_GALLERY_NO)
        @Html.HiddenFor(m => m.ART_GALLERY_TYPE)
        @Html.HiddenFor(m => m.WORK_TYPE)
        @Html.HiddenFor(m => m.PHOTO_NO)
        @Html.HiddenFor(m => m.PHOTO_SCHOOL_NO)
        @Html.HiddenFor(m => m.index)
        @Html.HiddenFor(m => m.ShowBtn)
        <div class="col-1 pr-0 text-right">
            @if (Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True)
            {
                <a class="btn px-0" role='button' onclick="deleteRow('Tr@(PhotoIndex)')" title="刪除作品"> <i class='glyphicon glyphicon-remove'></i></a>
            }
        </div>
        <div class="d-flex flex-wrap px-4">

            @if (Model.ShowBtn != ZZZI34EditViewModel.ShowBtnVal.True && Model.ShowBtn != ZZZI34EditViewModel.ShowBtnVal.Verify)
            {
                <fieldset class="col-6 col-md-8  mb-3" @((Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash) ? "" : "disabled")>
                    <div class="input-group">
                        <span class="input-group-addon p-1"><font color="red">*</font>主題</span>
                        @Html.EditorFor(m => m.PHOTO_SUBJECT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必填，限10個字內" } })
                    </div>
                    @Html.ValidationMessageFor(m => m.PHOTO_SUBJECT, "", new { @class = "text-danger" })
                </fieldset>
            }
            else
            {


                <fieldset class="col-6 col-md-8  mb-3" @((Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash) ? "" : "disabled")>
                    <div class="input-group">
                        <span class="input-group-addon p-1"><font color="red">*</font>主題</span>
                        @Html.EditorFor(m => m.PHOTO_SUBJECT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必填，限10個字內" } })
                    </div>
                    @Html.ValidationMessageFor(m => m.PHOTO_SUBJECT, "", new { @class = "text-danger" })
                </fieldset>
            }
            @if (Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.Verify)
            {


                <fieldset class="col-6 col-md-4 mb-3" @(Model.ShowBtn != ZZZI34EditViewModel.ShowBtnVal.True && Model.ShowBtn != ZZZI34EditViewModel.ShowBtnVal.Verify ? "disabled" : "")>
                    @if (Model.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal && Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True)
                    {

                        @Html.EditorFor(m => m.PHOTO_CASH, new { htmlAttributes = new { @class = "form-control input-md", @style = "width:55px;display:none" } })
                    }
                    else
                    {
                        <div class="input-group">
                            <span class="input-group-addon p-1"><font color="red">*</font>酷幣</span>
                            @Html.EditorFor(m => m.PHOTO_CASH, new { htmlAttributes = new { @class = "form-control input-md CASH", @placeholder = "0-10" } })
                        </div>
                    }
                    @Html.ValidationMessageFor(m => m.PHOTO_CASH, "", new { @class = "text-danger" })
                </fieldset>
            }
            <fieldset class="col-12 mb-3" @((Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash) ? "" : "disabled")>
                <div class="input-group">
                    <span class="input-group-addon px-2">內容</span>
                    @Html.TextAreaFor(m => m.PHOTO_DESC, 3, 50, new { @class = "form-control", @placeholder = "非必填，0~255個字" })
                </div>
                @Html.ValidationMessageFor(m => m.PHOTO_DESC, "", new { @class = "text-danger" })
            </fieldset>
            <fieldset class="col-12 mb-3">
                @if (!string.IsNullOrEmpty(Model.PHOTO_FILE))
                {

                    <div id="DivPHOTO_FILE_URL_@(PhotoIndex)" style="display:inline;">

                        @if (Model.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                        {
                            if (ViewBag.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash)
                            {
                                <a role='button' style="cursor:pointer;" onclick="DelFile('@Model.ART_GALLERY_NO','@Model.PHOTO_NO','@Model.index','@PhotoIndex')"> <i class='glyphicon glyphicon-remove'></i></a>
                                <br> }
                            string NewImg = Model.PHOTO_FILE;
                            string NewImg_S = Model.PHOTO_FILE.Replace(Path.GetExtension(Model.PHOTO_FILE), "_S" + Path.GetExtension(Model.PHOTO_FILE));
                            string NewImg_M = Model.PHOTO_FILE.Replace(Path.GetExtension(Model.PHOTO_FILE), "_M" + Path.GetExtension(Model.PHOTO_FILE));
                            string sourceDir = "";
                            string ImgUrls = string.Empty;
                            string ImgUrl = string.Empty;
                            ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(Model.PHOTO_SCHOOL_NO, Model.ART_GALLERY_NO, NewImg);
                            string ImgUrl_S = new ZZZI34Service().GetDirectorySysArtGalleryPath(Model.PHOTO_SCHOOL_NO, Model.ART_GALLERY_NO, NewImg_S);
                            string ImgUrl_M = new ZZZI34Service().GetDirectorySysArtGalleryPath(Model.PHOTO_SCHOOL_NO, Model.ART_GALLERY_NO, NewImg_M);
                            <input value="@NewImg_M" hidden />
                            if (string.IsNullOrEmpty(ImgUrl))
                            {


                                sourceDir = System.Web.Configuration.WebConfigurationManager.AppSettings["CopyFilePath"] + @"\" + Model.PHOTO_SCHOOL_NO + @"\" + Model.ART_GALLERY_NO;
                                ImgUrl = ImgUrl_M;
                                // ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(Model.PHOTO_SCHOOL_NO, Model.ART_GALLERY_NO, NewImg);
                                //  ImgUrls = ImgUrl;
                            }
                            <input value="@ImgUrls" hidden />
                            <input value="@sourceDir" hidden />
                            <div class="colorboxPHOTO thumbnail" style="cursor:pointer;width:300px;height:150px;display:table-cell; vertical-align:middle; " href="@ImgUrl" title="@Model.PHOTO_SUBJECT">
                                <img src="@(ImgUrl + "?refreshCache=" + DateTime.Now.ToString("mmddss"))" id="ImageX@(Model.PHOTO_NO)" style="display:inline;max-width:140px;max-height:140px" alt="Responsive image" href="@ImgUrl" class="colorboxPHOTO" />

                            </div>
                            <div style="padding-left:3px;padding-top:2%;">
                                @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = ImgUrl, ImgURL_S = ImgUrl_S, ImgUrl_M = ImgUrl_M, ImgID = "ImageX" + Model.PHOTO_NO })
                            </div>
                        }
                        @*else if (Model.WORK_TYPE == ADDT21.WORK_TYPE_VAL.video)
                    {
                        if (ViewBag.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash)
                        {
                            <a role='button' style="cursor:pointer;" onclick="DelFile('@Model.ART_GALLERY_NO','@Model.PHOTO_NO','@Model.index','@PhotoIndex')"> <i class='glyphicon glyphicon-remove'></i></a>
                        }

                        string VideoUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(Model.PHOTO_SCHOOL_NO, Model.ART_GALLERY_NO, Model.PHOTO_FILE);

                        <div class="colorbox thumbnail" style="cursor:pointer;width:150px;height:150px;display:table-cell; vertical-align:middle; " href="@Url.Action("_VideoView", (string)ViewBag.BRE_NO,new { Url= VideoUrl })" title="@Model.PHOTO_SUBJECT">
                            <video style="padding-top: 56.25%;width:140px" controls>
                                <source src="@VideoUrl" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>


                    }*@
                        else
                        {
                            if (ViewBag.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash)
                            {
                                <a role='button' style="cursor:pointer;" onclick="DelYoutube('@Model.index','@PhotoIndex')"> <i class='glyphicon glyphicon-remove'></i></a>
                            }

                            <div class="colorbox thumbnail" style="cursor:pointer;width:150px;height:150px;display:table-cell; vertical-align:middle; " href="@Model.PHOTO_FILE" title="@Model.PHOTO_SUBJECT">
                                <div class="videoWrapper">
                                    <iframe class="colorbox" type="text/html" src="@Model.PHOTO_FILE" frameborder="0" href="@Model.PHOTO_FILE"> </iframe>
                                </div>
                            </div>
                        }
                    </div>


                    <div style="display:none" class="DivPHOTO_FILE" id="DivPHOTO_FILE_@(PhotoIndex)">
                        @Html.EditorFor(m => m.PHOTO_FILE, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "Youtube 網址" } })
                        @Html.ValidationMessageFor(m => m.PHOTO_FILE, "", new { @class = "text-danger" })
                    </div>



                    <div style="display:none" class="DivUpPhotoFiles" id="DivUpPhotoFiles_@(PhotoIndex)">
                        @Html.TextBoxFor(m => m.PhotoFiles, new { @class = "form-control input-md", @type = "file" })
                        @Html.ValidationMessageFor(m => m.PhotoFiles, "", new { @class = "text-danger" })
                    </div>

                }

                @if (string.IsNullOrEmpty(Model.PHOTO_FILE))
                {
                    <samp class="DivPHOTO_FILE" style="@(Model.WORK_TYPE != ADDT21.WORK_TYPE_VAL.Youtube ? "display:none":"")">
                        @Html.EditorFor(m => m.PHOTO_FILE, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "Youtube 網址" } })
                        @Html.ValidationMessageFor(m => m.PHOTO_FILE, "", new { @class = "text-danger" })
                    </samp>



                    <samp class="DivUpPhotoFiles" style="@(Model.WORK_TYPE == ADDT21.WORK_TYPE_VAL.Youtube ? "display:none":"")">
                        @Html.TextBoxFor(m => m.PhotoFiles, new { @class = "form-control input-md", @type = "file" })
                        @Html.ValidationMessageFor(m => m.PhotoFiles, "", new { @class = "text-danger" })
                    </samp>
                }
            </fieldset>
        </div>
    </div>
}
