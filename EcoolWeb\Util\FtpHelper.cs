﻿using System;
using System.Data;
using System.IO;
using System.Text;

namespace EcoolWeb.Util
{
    public class FtpHelper
    {
        /// <summary>檔案上傳</summary>
        /// <param name="fileInfo">檔案資料</param>
        /// <param name="UploadType">上傳種類：1、MapPath 2、實體路徑</param>
        /// <param name="ReqMap">路徑</param>
        public static string UploadFile(FileInfo fileInfo, string UploadType, string ReqMap)
        {
            string fileName = fileInfo.Name.Substring(0, fileInfo.Name.Length - fileInfo.Extension.Length);
            string fileExtension = fileInfo.Extension;
            if (UploadType == "1")
            {
                string tempPath = ReqMap;
                string UpLoadFile = tempPath + "\\" + fileName + fileExtension;

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                return UpLoadFile;
                //return "/" + System.Web.Configuration.WebConfigurationManager.AppSettings["ProductImgPath"].Replace("\\", "/") + "/" + fileName + fileExtension;
            }
            else if (UploadType == "2")
            {
                string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"];
                string UpLoadFile = tempPath + "\\" + fileName + fileExtension;

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                return UpLoadFile;
            }
            else { return string.Empty; }
        }

        /// <summary>檔案上傳</summary>
        /// <param name="fileInfo">檔案資料</param>
        /// <param name="UploadType">上傳種類：1、MapPath 2、實體路徑</param>
        /// <param name="ReqMap">路徑</param>
        /// <param name="NewFileName">新檔案名稱</param>
        public static string UploadFile(FileInfo fileInfo, string UploadType, string ReqMap,string NewFileName)
        {
            string fileName = NewFileName.Substring(0, NewFileName.Length - fileInfo.Extension.Length);
            string fileExtension = fileInfo.Extension;
            if (UploadType == "1")
            {
                string tempPath = ReqMap;
                string UpLoadFile = tempPath + "\\" + fileName + fileExtension;

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                return UpLoadFile;
                //return "/" + System.Web.Configuration.WebConfigurationManager.AppSettings["ProductImgPath"].Replace("\\", "/") + "/" + fileName + fileExtension;
            }
            else if (UploadType == "2")
            {
                string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"];
                string UpLoadFile = tempPath + "\\" + fileName + fileExtension;

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                return UpLoadFile;
            }
            else { return string.Empty; }
        }
    }
}