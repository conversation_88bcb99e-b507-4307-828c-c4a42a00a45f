/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Latin1Supplement.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{161:[468,218,330,96,202],162:[579,138,500,53,448],163:[676,8,500,12,490],164:[534,10,500,-22,522],165:[662,0,500,-53,512],166:[676,14,200,67,133],167:[676,148,500,70,426],169:[676,14,760,38,722],170:[676,-394,276,4,270],171:[416,-33,500,42,456],173:[257,-194,333,39,285],174:[676,14,760,38,722],176:[676,-390,400,57,343],178:[676,-270,300,1,296],179:[676,-262,300,13,291],180:[678,-507,333,93,317],181:[450,218,500,36,512],182:[662,154,592,60,532],184:[0,215,333,52,261],185:[676,-270,300,57,248],186:[676,-394,310,6,304],187:[416,-33,500,43,458],188:[676,14,750,42,713],189:[676,14,750,36,741],190:[676,14,750,13,718],191:[467,218,444,30,376],192:[928,0,722,15,707],193:[928,0,722,15,707],194:[924,0,722,15,707],195:[888,0,722,15,707],196:[872,0,722,15,707],197:[961,0,722,15,707],198:[662,0,889,0,863],199:[676,215,667,28,633],200:[928,0,611,12,597],201:[928,0,611,12,597],202:[924,0,611,12,597],203:[872,0,611,12,597],204:[928,0,333,18,315],205:[928,0,333,18,315],206:[924,0,333,10,321],207:[872,0,333,17,315],208:[662,0,722,16,685],209:[888,11,722,12,707],210:[928,14,722,34,688],211:[928,14,722,34,688],212:[924,14,722,34,688],213:[888,14,722,34,688],214:[872,14,722,34,688],216:[734,80,722,34,688],217:[928,14,722,14,705],218:[928,14,722,14,705],219:[924,14,722,14,705],220:[872,14,722,14,705],221:[928,0,722,22,703],222:[662,0,556,16,542],223:[683,9,500,12,468],224:[678,10,444,37,442],225:[678,10,444,37,442],226:[674,10,444,37,442],227:[638,10,444,37,442],228:[622,10,444,37,442],229:[713,10,444,37,442],230:[460,7,667,38,632],231:[460,215,444,25,412],232:[678,10,444,25,424],233:[678,10,444,25,424],234:[674,10,444,25,424],235:[622,10,444,25,424],236:[678,0,278,6,243],237:[678,0,278,16,273],238:[674,0,278,-17,294],239:[622,0,278,-10,288],240:[686,10,500,29,471],241:[638,0,500,16,485],242:[678,10,500,29,470],243:[678,10,500,29,470],244:[674,10,500,29,470],245:[638,10,500,29,470],246:[622,10,500,29,470],248:[551,112,500,29,470],249:[678,10,500,9,480],250:[678,10,500,9,480],251:[674,10,500,9,480],252:[622,10,500,9,480],253:[678,218,500,14,475],254:[683,217,500,5,470],255:[622,218,500,14,475]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/Latin1Supplement.js");
