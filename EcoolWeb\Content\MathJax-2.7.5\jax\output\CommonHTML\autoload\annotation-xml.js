/*
 *  /MathJax/jax/output/CommonHTML/autoload/annotation-xml.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",function(){var c="2.7.5";var a=MathJax.ElementJax.mml,b=MathJax.OutputJax.CommonHTML;a["annotation-xml"].Augment({toCommonHTML:function(e){var d=this.Get("encoding");e=this.CHTMLdefaultNode(e,{childOptions:{encoding:d}});if(this.CHTML.rscale!==1){this.CHTML.rescale(1/this.CHTML.rscale)}return e}});a.xml.Augment({toCommonHTML:function(l,g){var n=this.CHTML=b.BBOX.zero();for(var j=0,d=this.data.length;j<d;j++){l.appendChild(this.data[j].cloneNode(true))}var e=l.offsetWidth,k=l.offsetHeight;var f=b.addElement(l,"mjx-hd-test",{style:{height:k+"px"}});n.d=n.b=(l.offsetHeight-k)/b.em;n.w=n.r=e/b.em;n.h=n.t=k/b.em-n.d;l.removeChild(f)}});MathJax.Hub.Startup.signal.Post("CommonHTML annotation-xml Ready");MathJax.Ajax.loadComplete(b.autoloadDir+"/annotation-xml.js")});
