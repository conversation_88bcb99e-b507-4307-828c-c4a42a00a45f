﻿$(document).ready(function () {
   
    const queryModule = {
        tarForm: "#ADDI06",
    init: function() { },
    bindEvents: function () { },
     setupGlobalFunction: function () {

        },
        doSearch: function () { },
        doSort: function () { },
        funPageProc: function (pageno) {
            const form= document.form1;
            if (!form) {
                console.error('找不到表單元素');
                show.Message('系統錯誤:找不到表單元素');
                return;

            }
            form.page.value = pageno;
            this.submit();
        },
        handleFormSubmit: function (event) {
            try {
                console.log('TotalGraph 表單提交');
                return true;
            } catch (error) {
                console.error('TotalGraph 表單提交時發生錯誤:', error);
                event.preventDefault();
                ADDI05Common.showMessage('表單提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },
        submitForm: function () {
            const $form = $(this.tarForm);
            $form.attr('enctype', 'multipart/form-data');
            $form.attr('action', window.ADDI06_Query_URLS);
            $form.submit();
          },
        todoClear: function () { },
        PrintBooK: function () { }
    }


});