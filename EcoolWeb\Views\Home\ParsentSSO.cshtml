﻿@using EcoolWeb.Models
@model LoginAppLoginViewModel

@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<style>
    .swal2-container {
        display: flex;
        position: fixed;
        z-index: 1060;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: 0.625em;
        overflow-x: hidden;
        transition: background-color .1s;
        background-color: transparent;
        -webkit-overflow-scrolling: touch;
    }

    .swal2-actions {
        display: flex;
        z-index: 1;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin: 1.25em auto 0
    }

    .swal2-select {
        min-width: 50%;
        max-width: 100%;
        padding: 0.375em 0.625em;
        background: inherit;
        color: inherit;
        font-size: 1.125em;
    }

    .swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen) > .swal2-modal {
        margin: auto;
    }


    .swal2-show {
        -webkit-animation: swal2-show .3s;
        animation: swal2-show .3s;
    }

    .swal2-popup {
        display: none;
        position: relative;
        box-sizing: border-box;
        flex-direction: column;
        justify-content: center;
        width: 32em;
        max-width: 100%;
        padding: 1.25em;
        border: none;
        border-radius: 0.3125em;
        background: #fff;
        font-family: inherit;
        font-size: 1rem;
    }

    .swal2-range {
        margin: 1em auto;
        background: inherit;
    }

    .swal2-file {
        background: inherit;
        font-size: 1.125em;
    }

    body {
        margin: 0;
        font-family: "微軟正黑體",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        text-align: left;
        background-color: #fff;
    }
</style>
<script src="~/Scripts/jquery-3.6.4.min.js"></script>
<script src="~/Scripts/jquery-ui.min.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@using (Html.BeginForm("Parsentredit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    <div class="modal-dialog modal-sm" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <h2 class="swal2-title" id="swal2-title" style="display: flex;">請選擇登入身份</h2>
            <div class="modal-content" style="padding:1rem">

                @Html.DropDownListFor(x => x.UserNo, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "swal2-select" })
                <br /><button class="btn btn-primary" onclick=""> 確定</button>
            </div>
          
        </div>
    </div>
}
<script>
    $(document).ready(function () {
        $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });



</script>
