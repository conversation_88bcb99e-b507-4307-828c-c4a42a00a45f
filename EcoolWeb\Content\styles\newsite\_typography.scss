.text-black {
    color: #000;
}

.text-ribbon {
    color: #2448a4;
}

.ribbon {
    width: 64%;
    margin: 1.5rem auto;
    text-align: center;
    // font-size: 1.125rem;
    font-size: 19px;
    letter-spacing: .5rem;
    position: relative;
    // background-color: #ebf1ff;
    // color: #2448a4;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.4);

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: -.5em;
        width: 20vw;
        border: 1rem solid;
        // border: 1rem solid desaturate(darken(#ebf1ff, 3%), 15%);
        z-index: -1;
        box-shadow: 0 .1rem 0 0 rgba(0, 0, 0, 0.2);

        @include media-breakpoint-up(sm) {
            width: 19vw;
        }

        @include media-breakpoint-up(md) {
            width: 33.5%;
        }
    }

    &::before {
        left: -28%;
        border-left-color: transparent;
        border-left-width: 0.5em;
    }

    &::after {
        right: -28%;
        border-right-color: transparent;
        border-right-width: 0.5em;
    }

    &-img {
        position: absolute;
        min-width: 47px;
        width: 15%;
        height: auto;
        left: -19%;
        top: -1em;
        z-index: 1;
    }

    &-title {

        &:before,
        &:after {
            content: '';
            position: absolute;
            border-style: solid;
            top: -0.5em;
        }

        &:before {
            left: 0;
            border-width: 0.5em 1.2em 0 0;

            @include media-breakpoint-up(lg) {
                border-width: 0.5em 1.44em 0 0;
            }
        }

        &:after {
            right: 0;
            border-width: 0 1.2em .5em 0;

            @include media-breakpoint-up(lg) {
                border-width: 0 1.44em .5em 0;
            }
        }
    }

    &-content {
        padding: 2rem;
        &-info {
            position: absolute;
            right: 41px;
            margin-top: -50px;
            font-weight: 600;
        }
    }


}

@each $name,
$value in $ribbonColors {
    $class: map-get($value, class);
    $bgcolor: map-get($value, bgcolor);
    $txtcolor: map-get($value, txtcolor);

    .ribbon-#{$class} {
        background-color: $bgcolor;
        color: $txtcolor;

        &::before,
        &::after {
            border-color: desaturate(darken($bgcolor, 3%), 15%);
        }

        &::before {
            border-left-color: transparent;
        }

        &::after {
            border-right-color: transparent;
        }

        .ribbon-title {
            &:before {
                border-color: transparent desaturate(darken($bgcolor, 20%), 45%) transparent transparent;
            }

            &:after {
                border-color: transparent transparent desaturate(darken($bgcolor, 20%), 45%) desaturate(darken($bgcolor, 20%), 45%);
            }
        }

    }

    .ribbon-#{$class}+.ribbon-content {
        background-color: rgba($bgcolor, 0.2);
    }
}

.ribbon+.ribbon-content {
    padding-top: 31px;
    margin: -31px 0 0 0;

    @include media-breakpoint-up(md) {
        margin: -31px 3.5rem 0 3.5rem;
    }
}

//編排修正
.col-md-4>.ribbon {
    @include media-breakpoint-up(md) {
        margin-top: 3rem;
    }

    .ribbon-img {
        @include media-breakpoint-up(md) {
            left: -16%;
            top: -2.3em;
        }
    }

    >.ribbon-title {
        letter-spacing: 0;

        @include media-breakpoint-up(md) {
            font-size: .925rem;

            &:before {
                border-width: 0.5em .4em 0 0;
            }

            &:after {
                border-width: 0 .4em .5em 0;
            }
        }
    }
}

.list-table {
    padding: 0;
    margin: 0;
    display: table;
    width: 100%;
    font-size: 0.825rem;

    li {
        padding: 0;
        margin: 0;
        display: table-row-group;

        >* {
            padding: 0.2rem;
            display: table-cell;
            color: #000;
        }
    }
}

.badge-hot {
    color: #fff;
    background: linear-gradient(red 0, #ad0303 100%) !important;
    transform: scale(.725);
    padding: 0 0.3rem;
    border-radius: 0.5rem;
    border: 3px red groove;

    &::before {
        content: "HOT!";
    }

    &:empty {
        display: inline-block;
    }
}