﻿@model GameRewardInfoViewModel

@using (Html.BeginCollectionItem("RewardInfo"))
{
    var Index = Html.GetIndex("RewardInfo");

    <div class="tr" id="Tr@(Index)">
        <div class="td" style="text-align:center">
            <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
        </div>
        <div class="td" style="text-align:center">
            @Html.EditorFor(m => m.RATE_S, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必輸,範圍 0~100" } })
            @Html.ValidationMessageFor(m => m.RATE_S, "", new { @class = "text-danger" })
        </div>
        <div class="td" style="text-align:center">
            @Html.EditorFor(m => m.RATE_E, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必輸,範圍 0~100" } })
            @Html.ValidationMessageFor(m => m.RATE_E, "", new { @class = "text-danger" })
        </div>
        <div class="td" style="text-align:center">
            @Html.EditorFor(m => m.REWARD_DESC, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必輸" } })
            @Html.ValidationMessageFor(m => m.REWARD_DESC, "", new { @class = "text-danger" })
        </div>
        <div class="td" style="text-align:center">
            @Html.EditorFor(m => m.REWARD_CASH, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必輸,請輸入數字 " } })
            @Html.ValidationMessageFor(m => m.REWARD_CASH, "", new { @class = "text-danger" })
        </div>
    </div>
}