﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'pl', {
	button: {
		title: 'Wła<PERSON>ciwości przycisku',
		text: '<PERSON><PERSON><PERSON> (Wartość)',
		type: 'Typ',
		typeBtn: 'Przycisk',
		typeSbm: 'Wyślij',
		typeRst: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Właściwości pola wyboru (checkbox)',
		radioTitle: 'Właściwości przycisku opcji (radio)',
		value: '<PERSON><PERSON><PERSON><PERSON>',
		selected: 'Zaznaczone',
		required: 'Wymagane'
	},
	form: {
		title: 'Wła<PERSON>ciwości formularza',
		menu: 'Wła<PERSON><PERSON><PERSON><PERSON>ci formularza',
		action: 'Akcja',
		method: 'Metoda',
		encoding: 'Kodowanie'
	},
	hidden: {
		title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ci pola ukrytego',
		name: '<PERSON><PERSON><PERSON>',
		value: '<PERSON><PERSON><PERSON>'
	},
	select: {
		title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> listy wyboru',
		selectInfo: 'Informacje',
		opAvail: 'Dostępne opcje',
		value: 'Wartość',
		size: 'Rozmiar',
		lines: 'wierszy',
		chkMulti: 'Wielokrotny wybór',
		required: 'Wymagane',
		opText: 'Tekst',
		opValue: 'Wartość',
		btnAdd: 'Dodaj',
		btnModify: 'Zmień',
		btnUp: 'Do góry',
		btnDown: 'Do dołu',
		btnSetValue: 'Ustaw jako zaznaczoną',
		btnDelete: 'Usuń'
	},
	textarea: {
		title: 'Właściwości obszaru tekstowego',
		cols: 'Liczba kolumn',
		rows: 'Liczba wierszy'
	},
	textfield: {
		title: 'Właściwości pola tekstowego',
		name: 'Nazwa',
		value: 'Wartość',
		charWidth: 'Szerokość w znakach',
		maxChars: 'Szerokość maksymalna',
		required: 'Wymagane',
		type: 'Typ',
		typeText: 'Tekst',
		typePass: 'Hasło',
		typeEmail: 'Email',
		typeSearch: 'Szukaj',
		typeTel: 'Numer telefonu',
		typeUrl: 'Adres URL'
	}
} );
