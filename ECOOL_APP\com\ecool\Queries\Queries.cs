﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public static class Queries
    {
        public static IConnection Log(this IConnection connection, Action<string> log) =>
            new ConfiguringConnection(connection, c => c.Database.Log = log);

        public static IConnection NoTracking(this IConnection connection) =>
            new ConfiguringConnection(connection, c => c.Configuration.AutoDetectChangesEnabled = false);

        public static IQuery<TResult> Query<TContext, TResult>(this IConnection connection, Func<TContext, TResult> selector)
            where TContext : DbContext =>
            new Query<TContext, TResult>(connection, selector);

        public static IQuery<TResult> WithNoLock<TResult>(this IQuery<TResult> query) =>
            new NoLockQuery<TResult>(query);

        public static System.Transactions.TransactionScope GetNewReadUncommittedScope()
        {
            return new System.Transactions.TransactionScope(
                System.Transactions.TransactionScopeOption.RequiresNew,
                new System.Transactions.TransactionOptions
                {
                    IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted
                });
        }

        public static List<T> ToListNoLock<T>(this IQueryable<T> query)
        {
            using (var txn = GetNewReadUncommittedScope())
            {
                return query.ToList();
            }
        }

        public static U NoLock<T, U>(this IQueryable<T> query, Func<IQueryable<T>, U> expr)
        {
            using (var txn = GetNewReadUncommittedScope())
            {
                return expr(query);
            }
        }

        public static void NoLockInvokeDB(Action action)
        {
            var transactionOptions = new System.Transactions.TransactionOptions();
            transactionOptions.IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted;
            using (var transactionScope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, transactionOptions))
            {
                try
                {
                    action();
                }
                finally
                {
                    transactionScope.Complete();
                }
            }
        }
    }
}