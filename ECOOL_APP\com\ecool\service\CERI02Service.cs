﻿using AutoMapper;
using com.ecool.sqlConnection;
using Dapper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using log4net;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class CERI02Service
    {
        #region 列表資料

        public AccreditationManageIndexViewModel GetListData(AccreditationManageIndexViewModel model, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(AccreditationManageListViewModel.CHG_DATE);
                model.SortType = PageGlobal.SortType.DESC;
            }

            string sSQL = $@" Select  ACCREDITATION_ID = a.ACCREDITATION_ID,
                            ACCREDITATION_TYPE = a.ACCREDITATION_TYPE,
                            TYPE_NAME = t.TYPE_NAME,
                            ACCREDITATION_NAME = a.ACCREDITATION_NAME,
                            CHG_PERSON = a.CHG_PERSON,
                            CHG_DATE = a.CHG_DATE,
                            CHG_PERSON_NAME = b.NAME
                            from CERT02 a (nolock)
                            join CERT01 t (nolock) on a.ACCREDITATION_TYPE = t.TYPE_ID
                            left join  HRMT01 b (nolock) on a.CHG_PERSON = b.USER_KEY
                            where a.SCHOOL_NO = @SCHOOL_NO and a.DEL_YN = '{SharedGlobal.N}'";
            var tempDapper = db.Database.Connection.Query<AccreditationManageListViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = SCHOOL_NO,
             });

            var temp = tempDapper.AsQueryable();
            if (!string.IsNullOrWhiteSpace(model.search))
            {
                temp = temp.Where(x => x.ACCREDITATION_NAME.Contains(model.search) || x.ACCREDITATION_TYPE.Contains(model.search) || x.TYPE_NAME.Contains(model.search));

            }


                if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_NAME))
            {
                temp = temp.Where(a => a.ACCREDITATION_ID == model.WhereACCREDITATION_NAME);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
            {
                temp = temp.Where(a => a.ACCREDITATION_TYPE == model.WhereACCREDITATION_TYPE);
            }

            temp = temp.OrderBy(model.OrderByColumnName, model.SortType);
            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.ListData.PageNumber;

            var Accreditations = model.ListData.Select(a => a.ACCREDITATION_ID).ToList();

            if (Accreditations?.Count() > 0)
            {
                var Hr = (from a in db.CERT02_V
                          join b in db.HRMT01 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                          select new { a.ACCREDITATION_ID, b.NAME }).ToListNoLock();

                if (Hr?.Count() > 0)
                {
                    model.ListData.Select(a =>
                    {
                        a.VERIFIERs = Hr.Where(x => x.ACCREDITATION_ID == a.ACCREDITATION_ID).Select(X => X.NAME).ToList();

                        return a;
                    }).ToList();
                }
            }

            return model;
        }

        #endregion 列表資料

        #region 取得編輯資料
        private static log4net.ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public AccreditationManageEditViewModel GetEditData(AccreditationManageEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.Main = (from a in db.CERT02
                              where a.ACCREDITATION_ID == model.Keyword
                              select new AccreditationManageEditMainViewModel()
                              { 
                                  ACCREDITATION_ID = a.ACCREDITATION_ID,
                                  ACCREDITATION_TYPE = a.ACCREDITATION_TYPE,
                                  ACCREDITATION_NAME = a.ACCREDITATION_NAME,
                                  IsText=a.IsText
                              }).FirstOrDefault();

                model.ContentData = (from x in db.CERT02_D
                                     where x.ACCREDITATION_ID == model.Keyword
                                     select new AccreditationManageEditDetailsViewModel()
                                     {
                                         ACCREDITATION_ID = x.ACCREDITATION_ID,
                                         ITEM_NO = x.ITEM_NO,
                                         SUBJECT = x.SUBJECT,
                                         CONTENT = x.CONTENT,
                                     }).ToList();

                var cERT03G = db.CERT03_G.Where(x => x.ACCREDITATION_ID == model.Keyword).ToList();

                if (cERT03G.Count > 0)
                {
                    model.ContentData.Select(a =>
                    {
                        a.GRADE_SEMESTERs = cERT03G.Where(x => x.ACCREDITATION_ID == a.ACCREDITATION_ID && x.ITEM_NO == a.ITEM_NO).Select(x => $"{(byte)x.GRADE}_{(byte)x.SEMESTER}").ToList();
                        return a;
                    }).ToList();
                }
               
                model.oTeachers = (from a in db.CERT02_V
                                   join b in db.HRMT01 on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                                   where a.ACCREDITATION_ID == model.Keyword
                                   select b).ToList();
            }

            return model;
        }

        #endregion 取得編輯資料

        #region Save處理

        public IResult SaveEditData(AccreditationManageEditViewModel model, string SCHOOL_NO, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    //主檔
                    CERT02 SaveUp = null;

                    SaveUp = db.CERT02.Where(a => a.ACCREDITATION_ID == model.Main.ACCREDITATION_ID).FirstOrDefault();

                    if (SaveUp == null)
                    {
                        SaveUp = new CERT02();

                        SaveUp.ACCREDITATION_ID = Guid.NewGuid().ToString("N");
                        SaveUp.ACCREDITATION_TYPE = model.Main.ACCREDITATION_TYPE;
                        SaveUp.ACCREDITATION_NAME = model.Main.ACCREDITATION_NAME;
                        SaveUp.SCHOOL_NO = SCHOOL_NO;
                        SaveUp.DEL_YN = SharedGlobal.N;
                        SaveUp.CHG_PERSON = user?.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.IsText = model.IsText;

                        db.CERT02.Add(SaveUp);
                    }
                    else
                    {
                        this.CreHis(SaveUp, ref db);

                        SaveUp.ACCREDITATION_TYPE = model.Main.ACCREDITATION_TYPE;
                        SaveUp.ACCREDITATION_NAME = model.Main.ACCREDITATION_NAME;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        SaveUp.IsText = model.IsText;
                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                        List<CERT02_D> cERT02_Ds = new List<CERT02_D>();
                        List<CERT02_V> cERT02_Vs = new List<CERT02_V>();
                        List<CERT03_G> cERT03_Gs = new List<CERT03_G>();
                        cERT02_Ds = db.CERT02_D.Where(a => a.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID).ToList();
                        cERT02_Vs= db.CERT02_V.Where(a => a.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID).ToList();
                        cERT03_Gs= db.CERT03_G.Where(a => a.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID).ToList();
                        db.CERT02_D.RemoveRange(cERT02_Ds);
                        db.CERT02_V.RemoveRange(cERT02_Vs);
                        db.CERT03_G.RemoveRange(cERT03_Gs);
                      //  db.CERT02_D.Where(a => a.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID).Delete();
                       // db.CERT02_V.Where(a => a.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID).Delete();
                     //   db.CERT03_G.Where(a => a.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID).Delete();
                    }
                    try {

                        db.SaveChanges();

                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        //return result;
                    }
                    //明細
                    if (model.ContentData?.Count() > 0)
                    {
                        StringHelper stringHelper = new StringHelper();

                        List<CERT02_D> aCCREDITATION_DETAILs = new List<CERT02_D>();
                        List<CERT03_G> cERT03_Gs = new List<CERT03_G>();

                        int Item = 0;

                        foreach (var item in model.ContentData)
                        {
                            CERT02_D CreD = new CERT02_D();
                            Item += 1;

                            CreD.ACCREDITATION_ID = SaveUp.ACCREDITATION_ID;
                            CreD.SUBJECT = item.SUBJECT;
                            CreD.CONTENT = item.CONTENT;

                            if (string.IsNullOrWhiteSpace(item.ITEM_NO))
                            {
                                if (model.ContentData.Where(a => a.ITEM_NO == Item.ToString()).Any())
                                {
                                    var maxItem = model.ContentData.Max(a => a.ITEM_NO);
                                    Item = Convert.ToInt32(maxItem) + 1;
                                }
                                CreD.ITEM_NO = Item.ToString();
                            }
                            else
                            {
                                CreD.ITEM_NO = item.ITEM_NO;
                            }

                            if (item.GRADE_SEMESTERs?.Count() > 0)
                            {
                                foreach (var ThisGS in item.GRADE_SEMESTERs.Where(a => a != ""))
                                {
                                    CERT03_G CreG = new CERT03_G();
                                    CreG.ACCREDITATION_ID = CreD.ACCREDITATION_ID;
                                    CreG.ITEM_NO = CreD.ITEM_NO;
                                    CreG.SCHOOL_NO = SCHOOL_NO;
                                    CreG.GRADE = Convert.ToByte(stringHelper.StrLeft(ThisGS, 1));
                                    CreG.SEMESTER = Convert.ToByte(stringHelper.StrRigth(ThisGS, 1));
                                    CreG.CHG_PERSON = user?.USER_KEY;
                                    CreG.CHG_DATE = DateTime.Now;
                                    cERT03_Gs.Add(CreG);
                                    db.CERT03_G.Add(CreG);
                                }
                            }

                            aCCREDITATION_DETAILs.Add(CreD);
                            db.CERT02_D.Add(CreD);
                        }

                        //EFBatchOperation.For(db, db.CERT02_D).InsertAll(aCCREDITATION_DETAILs);
                      

                        //if (cERT03_Gs.Count > 0)
                        //{
                        //    EFBatchOperation.For(db, db.CERT03_G).InsertAll(cERT03_Gs);
                        //}
                    }

                    //認證老師
                    if (model.oTeachers?.Count() > 0)
                    {
                        List<CERT02_V> aCCREDITATION_VERIFIER_s = new List<CERT02_V>();

                        foreach (var itemVerifier in model.oTeachers)
                        {
                            CERT02_V CreVerifier = new CERT02_V();
                            CreVerifier.ACCREDITATION_ID = SaveUp.ACCREDITATION_ID;
                            CreVerifier.SCHOOL_NO = itemVerifier.SCHOOL_NO;
                            CreVerifier.USER_NO = itemVerifier.USER_NO;
                            CreVerifier.CRE_PERSON = user?.USER_KEY;
                            CreVerifier.CRE_DATE = DateTime.Now;
                            aCCREDITATION_VERIFIER_s.Add(CreVerifier);
                            db.CERT02_V.Add(CreVerifier);
                        }
                      //  EFBatchOperation.For(db, db.CERT02_V).InsertAll(aCCREDITATION_VERIFIER_s);
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                this.SetSyearAccreditationData(db);

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        #endregion Save處理

        #region 作廢處理

        public IResult DelDate(AccreditationManageEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    model.Keyword = (!string.IsNullOrWhiteSpace(model.Keyword)) ? model.Keyword : model.Main?.ACCREDITATION_ID;

                    CERT02 SaveUp = db.CERT02.Where(x => x.ACCREDITATION_ID == model.Keyword).FirstOrDefault();
                    if (SaveUp == null)
                    {
                        result.Message += "查無此資料";
                        return result;
                    }

                    this.CreHis(SaveUp, ref db);

                    SaveUp.DEL_YN = SharedGlobal.Y;
                    SaveUp.DEL_DATE = DateTime.Now;
                    SaveUp.DEL_PERSON = user?.USER_KEY;

                    db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }
                this.SetSyearAccreditationData(db);
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
            }

            return result;
        }

        #endregion 作廢處理

        /// <summary>
        /// 判斷是否有設護照
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public bool IsAccreditationforSchool(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            return db.CERT02.Where(x => x.DEL_YN == SharedGlobal.N && x.SCHOOL_NO == SCHOOL_NO).NoLock(x => x.Any());
        }

        /// <summary>
        /// Open 護照 小視窗
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public AccreditationModalViewModel GetAccreditationModalListData(AccreditationModalViewModel model, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(AccreditationEditModalViewModel.ACCREDITATION_NAME);
                model.SortType = PageGlobal.SortType.ASC;
            }

            var Temp = (from a in db.CERT02
                        join t in db.CERT01 on a.ACCREDITATION_TYPE equals t.TYPE_ID
                        where a.SCHOOL_NO == model.SCHOOL_NO && a.DEL_YN == SharedGlobal.N
                        select new AccreditationEditModalViewModel()
                        {
                            ACCREDITATION_ID = a.ACCREDITATION_ID,
                            ACCREDITATION_TYPE = a.ACCREDITATION_TYPE,
                            TYPE_NAME = t.TYPE_NAME,
                            ACCREDITATION_NAME = a.ACCREDITATION_NAME,
                        });

            if (!string.IsNullOrWhiteSpace(model.SearchContent))
            {
                Temp = Temp.Where(a => a.ACCREDITATION_NAME.Contains(model.SearchContent));
            }

            if (model.oAccreditations?.Count() > 0)
            {
                var Keys = model.oAccreditations.Select(a => a.ACCREDITATION_ID).ToList();

                Temp = Temp.Where(a => !Keys.Contains(a.ACCREDITATION_ID));
            }
            Temp = Temp.OrderBy(model.OrderByColumnName, model.SortType);

            model.Accreditations = Temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.Accreditations.PageNumber;

            return model;
        }

        public AccreditationEditModalViewModel GetAccreditation(string ACCREDITATION_ID, ref ECOOL_DEVEntities db)
        {
            return (from a in db.CERT02
                    join t in db.CERT01 on a.ACCREDITATION_TYPE equals t.TYPE_ID
                    where a.ACCREDITATION_ID == ACCREDITATION_ID && a.DEL_YN == SharedGlobal.N
                    select new AccreditationEditModalViewModel()
                    {
                        ACCREDITATION_ID = a.ACCREDITATION_ID,
                        ACCREDITATION_TYPE = a.ACCREDITATION_TYPE,
                        TYPE_NAME = t.TYPE_NAME,
                        ACCREDITATION_NAME = a.ACCREDITATION_NAME,
                    }).NoLock(x => x.FirstOrDefault());
        }

        public bool CreHis(CERT02 His, ref ECOOL_DEVEntities db)
        {
            if (His != null)
            {
                CERT02_HIS cERT02_HIS = new CERT02_HIS();
                cERT02_HIS.HIS_ID = Guid.NewGuid().ToString("N");
                cERT02_HIS.HIS_DATE = DateTime.Now;
                cERT02_HIS.ACCREDITATION_ID = His.ACCREDITATION_ID;
                cERT02_HIS.ACCREDITATION_TYPE = His.ACCREDITATION_TYPE;
                cERT02_HIS.ACCREDITATION_NAME = His.ACCREDITATION_NAME;
                cERT02_HIS.SCHOOL_NO = His.SCHOOL_NO;
                cERT02_HIS.CRE_PERSON = His.CRE_PERSON;
                cERT02_HIS.CRE_DATE = His.CRE_DATE;
                cERT02_HIS.CHG_PERSON = His.CHG_PERSON;
                cERT02_HIS.CHG_DATE = His.CHG_DATE;
                cERT02_HIS.DEL_YN = His.DEL_YN;
                cERT02_HIS.DEL_DATE = His.DEL_DATE;
                cERT02_HIS.DEL_PERSON = His.DEL_PERSON;

                db.CERT02_HIS.Add(cERT02_HIS);

                var oldCERT02_Ds = db.CERT02_D.Where(a => a.ACCREDITATION_ID == His.ACCREDITATION_ID).ToList();

                if (oldCERT02_Ds?.Count > 0)
                {
                    List<CERT02_D_HIS> cERT02_D_HISs = new List<CERT02_D_HIS>();

                    cERT02_D_HISs = (from a in oldCERT02_Ds
                                     select new CERT02_D_HIS()
                                     {
                                         HIS_ID = cERT02_HIS.HIS_ID,
                                         ACCREDITATION_ID = a.ACCREDITATION_ID,
                                         ITEM_NO = a.ITEM_NO,
                                         SUBJECT = a.SUBJECT,
                                         CONTENT = a.CONTENT,
                                     }).ToList();
                    db.CERT02_D_HIS.AddRange(cERT02_D_HISs);
                   // EFBatchOperation.For(db, db.CERT02_D_HIS).InsertAll(cERT02_D_HISs);
                }
            }

            return true;
        }
        public bool sendMERAGE_HRMT01_SQLMail() {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            SqlConnection conn = new sqlConnection().getConnection4Query();
            SqlTransaction transaction = conn.BeginTransaction();

            StringBuilder sSQL = new StringBuilder();
            StringBuilder sSQL1 = new StringBuilder();
            string str = "";
            DateTime dateTimeNow = new DateTime();
            dateTimeNow = DateTime.Now;
            str = dateTimeNow.Year.ToString() + dateTimeNow.Month.ToString() + dateTimeNow.Day.ToString();
            DataTable dt;
            DataTable dt1;
            int HrmtMergLogs = 0;
            //先查詢是否有送過信件
            HrmtMergLogs = db.HrmtMergLogs.Where(x => x.LogTime == str).Count();
            if (HrmtMergLogs == 0) {

                sSQL1.Append(@"	SELECT	

                                his.step_name as SCHOOLNO,

                              	(SELECT [SHORT_NAME]   FROM BDMT01 b1 where b1.SCHOOL_NO=SUBSTRING(his.step_name,1,6) ) as SCHOOLNAME

                                    FROM msdb.dbo.sysjobs jb INNER JOIN msdb.dbo.sysjobhistory his
                                           ON jb.job_id = his.job_id
                                        where jb.name='MERAGE_HRMT01_SQL' and run_date>= replace(CONVERT (Date, GETDATE()),'-','')  and his.run_status=0 and his.step_name<>'(作業結果)'-- and his.run_status<>1 
                                        order by his.step_name,his.run_date desc");
                // IDbCommand cmd = new SqlCommand(@" INSERT INTO　HrmtMergLog(LogTime,ErrorCount,SendMail)")
                sSQL.Append(@"SELECT replace(CONVERT (Date, GETDATE()),'-','') as LogTime

, count(*) as ErrorCount
FROM msdb.dbo.sysjobs jb INNER JOIN msdb.dbo.sysjobhistory his
 ON jb.job_id = his.job_id
 where jb.name = 'MERAGE_HRMT01_SQL' and run_date >= replace(CONVERT(Date, GETDATE()), '-', '')  and his.run_status = 0 and his.step_name <> '(作業結果)'-- and his.run_status <> 1
 --order by his.step_name, his.run_date desc

 group by his.run_date");
                dt = new sqlConnection().executeQueryByDataTableList(sSQL.ToString());
                dt1 = new sqlConnection().executeQueryByDataTableList(sSQL1.ToString());
                int? ErrorCount = 0;
                if (dt != null)
                {
                    foreach (DataRow drB in dt.Rows)
                    {

                        HrmtMergLog hrmtMergLog = new HrmtMergLog();
                        string LOGTIME = (drB["LogTime"] == DBNull.Value ? "" : (string)drB["LogTime"]);
                        ErrorCount = (drB["ErrorCount"] == DBNull.Value ? (int?)null : Convert.ToInt32(drB["ErrorCount"]));
                        hrmtMergLog.LogTime = LOGTIME;
                        hrmtMergLog.ErrorCount =(int) ErrorCount;
                        hrmtMergLog.SendMail = true;
                        db.HrmtMergLogs.Add(hrmtMergLog);

                        try
                        {
                            db.SaveChanges();
                        }
                        catch (Exception e)
                        {
                            logger.Error("CRE" + e.Message);
                        }
                        StringBuilder sb = new StringBuilder();
                        string schoolNMAEstr = "";
                        string schoolNOstr = "";
                        if (dt1 != null)
                        {
                            foreach (DataRow drB1 in dt1.Rows)
                            {


                                schoolNMAEstr +=  (drB1["SCHOOLNAME"] == DBNull.Value ? "" : (string)drB1["SCHOOLNAME"]);
                                schoolNOstr +=  (drB1["SCHOOLNO"] == DBNull.Value ? "" : (string)drB1["SCHOOLNO"]);
                            }

                            sb.Append($"<div>");

                            sb.Append($"   MERAGE_HRMT01_SQL 發生錯誤學校數 <div>:  {ErrorCount}</div>");
                            sb.Append($"  SCHOOL_NO學校名稱清單 <div>: {schoolNMAEstr} </div>");
                            sb.Append($"   SCHOOL_NO學校清單 <div>: {schoolNOstr} </div>");

                            MailHelper mailHelper = new MailHelper();
                            List<string> mailTo = new List<string>();
                            mailTo.Add("<EMAIL>");
                            bool state = mailHelper.SendMailByGmail(mailTo, "酷幣同步錯誤", sb.ToString());
                        }
                        //        StringBuilder sb = new StringBuilder();
                        //        sb.Append(" INSERT INTO　HrmtMergLog(LogTime,ErrorCount,SendMail) ");
                        //        sb.AppendFormat(" VALUES('{0}','{1}','{2}') ", (string)drB["LogTime"], (string)drB["ErrorCount"], SOURCE);

                        //        new sqlConnection.sqlConnection().execute(sb.ToString());
                        //        strMsg = "酷幣新增成功";
                    }

                }
                else {


                    IDbCommand cmd = new SqlCommand(@" INSERT INTO　HrmtMergLog(LogTime,ErrorCount,SendMail) VALUES (@LogTime,@ErrorCount,@SendMail)");
                    cmd.Connection = conn;
                    cmd.Transaction = transaction;
                    cmd.Parameters.Add(

          new SqlParameter("@LogTime", str));

          

                    cmd.Parameters.Add(

new SqlParameter("@ErrorCount", 0));
                    cmd.Parameters.Add(new SqlParameter("@SendMail", 1));

                }
            }
         
           //if(dt)
            return true;
        }
        public bool SetSyearAccreditationData(ECOOL_DEVEntities db)
        {
            var NowSYear = SysHelper.GetNowSYear(DateTime.Now);

            var results = db.Database.Connection.Execute("sp_SetSyearAccreditationData"
                 , new { SYEAR = NowSYear }
                 , commandType: CommandType.StoredProcedure);

            return true;
        }

        public bool AutoSetSyearAccreditationData()
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                var NowSYear = SysHelper.GetNowSYear(DateTime.Now);

                if (db.CERT02_SYEAR.Where(a => a.SYEAR == NowSYear).Any() == false)
                {
                    SetSyearAccreditationData(db);
                }
            }

            return true;
        }
        public List<SelectListItem> GetSelectSUBJECTItems(string defaultSelectValue, string ACCREDITATION_ID, ref ECOOL_DEVEntities db, string EmptyValueIsText = "") {
            List<SelectListItem> SubjectItems = new List<SelectListItem>();
            SubjectItems.Add(new SelectListItem() { Text = EmptyValueIsText, Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });
           
                var Temp = db.CERT02_D.Where(x => x.ACCREDITATION_ID == ACCREDITATION_ID);
          
            var cERT02s = Temp.ToListNoLock();
            if (cERT02s?.Count() > 0)
            {
                foreach (var item in cERT02s)
                {
                    SubjectItems.Add(new SelectListItem() { Text = item.SUBJECT, Value = item.ITEM_NO, Selected = item.ACCREDITATION_ID == defaultSelectValue });
                }
            }

            return SubjectItems;
        }
        public List<SelectListItem> GetSelectAccreditationsItems(string defaultSelectValue, string SCHOOL_NO, ref ECOOL_DEVEntities db, string EmptyValueIsText = "", string AccreditationType = "")
        {
            List<SelectListItem> AccreditationsItems = new List<SelectListItem>();

            AccreditationsItems.Add(new SelectListItem() { Text = EmptyValueIsText, Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            var Temp = db.CERT02.Where(a => a.DEL_YN == SharedGlobal.N && a.SCHOOL_NO == SCHOOL_NO);
            string[] chineseNumber = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
            if (!string.IsNullOrWhiteSpace(AccreditationType))
            {
                Temp = Temp.Where(a => a.ACCREDITATION_TYPE == AccreditationType);
            }

            var cERT02s = Temp.ToListNoLock();

            if (cERT02s?.Count() > 0)
            {
               
                foreach (var item in cERT02s.OrderBy(x=>x.ACCREDITATION_NAME))
                {
                    AccreditationsItems.Add(new SelectListItem() { Text = item.ACCREDITATION_NAME, Value = item.ACCREDITATION_ID, Selected = item.ACCREDITATION_ID == defaultSelectValue });
                }
            }

            return AccreditationsItems;
        }
    }
}