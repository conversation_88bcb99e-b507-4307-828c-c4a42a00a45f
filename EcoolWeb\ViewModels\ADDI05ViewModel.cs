﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
namespace EcoolWeb.ViewModels
{
    public class AWAI05ListViewModel
    {
        public string whereKeyword { get; set; }
        public string whereGrade { get; set; }
        public string whereCLASS_NO { get; set; }
        public string OrderColumn { get; set; }
        public string SortBy { get; set; }

        public int Page { get; set; }

        public AWAI05ListViewModel()
        {
            Page = 0;
            SortBy = "DESC";
        }
    }

    public class AWAI05Detail
    {
        public string HIS_NO { get; set; }
        public string DIALOG_ID { get; set; }
        public string SCHOOL_NO { get; set; }
        public string USER_NO { get; set; }
        public string NAME { get; set; }
        public byte? GRADE { get; set; }
        public string CLASS_NO { get; set; }
        public int grade { get; set; }
        public DateTime? CRE_DATE { get; set; }

    }
 }