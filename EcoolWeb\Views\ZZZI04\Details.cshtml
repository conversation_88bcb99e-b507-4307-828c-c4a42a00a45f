﻿@model ECOOL_APP.com.ecool.Models.entity.BT02AmdinViewModel
@using System.Text.RegularExpressions;
@{
    /**/

    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    if ((string)ViewBag.Layout == "_LayoutChildMonth")
    {
        Layout = "~/Views/Shared/_LayoutChildMonth.cshtml";
    }

}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@if ((string)ViewBag.Layout == "_LayoutChildMonth")
{
    @Html.Partial("_Title_Secondary")
}

@Html.Partial("_Notice")
<br />

<img src="~/Content/img/web-bar2-revise-02.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ZZZI04">
    <div class="Details">
        <div class="dl-horizontal-EZ">
            <samp class="dt">
                @Html.DisplayNameFor(model => model.Details_LANG[0].SUBJECT)
            </samp>
            <samp class="dd">
                @Html.DisplayFor(model => model.Details_LANG[0].SUBJECT)
            </samp>
        </div>
        <div class="dl-horizontal-EZ">
            <samp class="dt">
                公告日期
            </samp>
            <samp class="dd">
                @Html.DisplayFor(model => model.S_DATE)
            </samp>
        </div>

        <div class="dl-horizontal-EZ">
            <samp class="dt">
                詳細活動內容
            </samp>
        </div>
        <div style="height:15px"></div>
        <div class="p-context">
            @Html.Raw(HttpUtility.HtmlDecode(Model.Details_LANG[0].CONTENT_TXT))

            @if (Model.Details_FILE.Count() > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                <br />
                <samp>附件下載:</samp>
                foreach (var FILEItem in Model.Details_FILE)
                {
                    if (regexCode.IsMatch(FILEItem.FILE_NAME.ToLower()))
                    {
                        string ImgSrc = Url.Action("DownLoad", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = Model.BULLET_ID, name = FILEItem.FILE_NAME });

                        <img src='@ImgSrc' style="max-width:300px" class="img-responsive" href="@ImgSrc" />
                    }
                    else
                    {
                        @Html.ActionLink(FILEItem.FILE_NAME, "DownLoad", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = Model.BULLET_ID, name = FILEItem.FILE_NAME }, new { @class = "btn btn-link" })
                    }

                }
            }
        </div>
        <div class="dl-horizontal-EZ">
            <samp class="dt">
                相關連結
            </samp>
            <samp class="dd">
                <a href="@Model.LinkContext">@Model.LinkContext</a>
            </samp>
        </div>
    </div>
</div>
<div style="height:15px"></div>
<div class="text-center">
    @if ((string)ViewBag.Layout == "_LayoutChildMonth")
    {
        if ((string)ViewBag.PrevAction == "ChildMonthIndex")
        {
            @Html.ActionLink("返回", "ChildMonthIndex", new { controller = "Home" }, new { @class = "btn btn-default" })
        }
        else
        {
            @Html.ActionLink("返回", (string)ViewBag.PrevAction, new { page = (int)ViewBag.page, SearchContents = (string)ViewBag.SearchContents, PrevAction = (string)ViewBag.PrevAction, Layout = (string)ViewBag.Layout }, new { @class = "btn btn-default" })
        }

    }
    else
    {
         
    }
</div>

<script language="javascript">
    $(document).ready(function () {

        var SysUrl = 'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'

        $(".p-context img").each(function () {

            var ThisSrc = $(this).attr("src")

            if (ThisSrc.indexOf("http") != 0) {
                $(this).attr("href", ThisSrc);
            }
            else if (ThisSrc.indexOf("data:image") != 0) {
                $(this).attr("href", ThisSrc);
            }
            else {
                $(this).attr("href", SysUrl + ThisSrc);
            }
        });

        $(".p-context img").colorbox({ photo: true });
    });
</script>