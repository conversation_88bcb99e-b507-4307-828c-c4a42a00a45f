﻿<?xml version="1.0" encoding="utf-8"?>

<!-- 如需使用 Web.config 轉換的詳細資訊，請瀏覽 http://go.microsoft.com/fwlink/?LinkId=301874 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    在以下範例中， "SetAttributes" 轉換會變更 "connectionString"
    的值，以在 "Match" 定位器
    找到具有 "MyDB" 值的屬性時僅使用 "ReleaseSQLServer"。

    <connectionStrings>
      <add name="MyDB"
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True"
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
    <!--
      在下列範例中，"Replace" 轉換會取代
      Web.config 檔案的全部 <customErrors> 區段。
      注意，由於
      <system.web> 節點下只有一個 customErrors 區段，則無需使用 "xdt:Locator" 屬性。

      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
-->
  </system.web>
</configuration>
