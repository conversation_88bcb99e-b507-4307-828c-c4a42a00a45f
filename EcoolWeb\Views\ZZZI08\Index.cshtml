﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id= "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("PageContent", (string)ViewBag.BRE_NO)
    </div>
}

@section Scripts {
    <script language="JavaScript">
    $(document).ready(function () {

        


        if (form1.SearchType.value == "2") {
            $("#Q_DIALOG_SDATE,#Q_DIALOG_EDATE").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });
        }
    });


    function FunPageProc(pageno) {
        form1.Page.value = pageno
        funAjax()
    }

    function FunSort(SortName) {
        if (form1.OrderByName.value == SortName) {
            if (form1.SyntaxName.value == "Desc") {
                form1.SyntaxName.value = "ASC"
            }
            else {
                form1.SyntaxName.value = "Desc"
            }
        }
        else {
            form1.OrderByName.value = SortName;
            form1.SyntaxName.value = "Desc";
        }
        funAjax()
    }

    function funAjax() {


            $.ajax({
                url: '@Url.Action("PageContent", (string)ViewBag.BRE_NO)',
                data: $('#form1').serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        


    }

   function onAdd() {

       $('#VIEW_DATA_TYPE').val("@ViewBag.VIEW_A")
       form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
       form1.submit();

    }

        function onBtnLink(DIALOG_ID, VIEW_DATA_TYPE) {

            $('#uADDT11_DIALOG_ID').val(DIALOG_ID)
            $('#VIEW_DATA_TYPE').val(VIEW_DATA_TYPE)

 
            form1.action = '@Html.Raw(@Url.Action("Edit", (string)ViewBag.BRE_NO))' 
            form1.submit();
        }

        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            form1.submit();
        }
    </script>
}