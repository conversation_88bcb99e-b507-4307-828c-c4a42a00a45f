﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.com.ecool.Models.entity;
using System.Data;
using System.Data.SqlClient;
using com.ecool.sqlConnection;
using ECOOL_APP.EF;

namespace com.ecool.service
{
    public class BreadcrumbService
    {
        /// <summary>
        /// 取得程式名稱
        /// </summary>
        /// <param name="BRE_NO"></param>
        /// <returns></returns>
        public static string GetBRE_NAME(string BRE_NO)
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                string ReturnVal = db.ZZT01.Where(a => a.BRE_NO == BRE_NO || a.CONTROLLER == BRE_NO).Select(a => a.BRE_NAME).FirstOrDefault() ?? "";
                return ReturnVal;
            }
        }

        public static string GetBRE_NAME_forBRE_NO(string BRE_NO)
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                string ReturnVal = db.ZZT01.Where(a => a.BRE_NO == BRE_NO).Select(a => a.BRE_NAME).FirstOrDefault() ?? "";
                return ReturnVal;
            }
        }

        public static string GetBRE_NAME_forSessionBRE_NO(string SessionBRE_NO, string BRE_NO)
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                string ReturnVal = string.Empty;

                if (!string.IsNullOrWhiteSpace(SessionBRE_NO))
                {
                    ReturnVal = db.ZZT01.Where(a => a.BRE_NO == SessionBRE_NO).Select(a => a.BRE_NAME).FirstOrDefault() ?? "";
                }
                else
                {
                    ReturnVal = db.ZZT01.Where(a => a.BRE_NO == BRE_NO).Select(a => a.BRE_NAME).FirstOrDefault() ?? "";
                }

                return ReturnVal;
            }
        }

        public static List<uZZT01> GetBreadcrumbQUERY(string BRE_NO)
        {
            List<uZZT01> list_data = new List<uZZT01>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.AppendFormat(" select * from dbo.GetBreadcrumbData('{0}') A ORDER BY A.LEVEL_NO", BRE_NO);
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uZZT01()
                    {
                        BRE_NO = dr["BRE_NO"].ToString(),
                        BRE_NAME = dr["BRE_NAME"].ToString(),
                        CONTROLLER = dr["CONTROLLER"].ToString(),
                        ACTION_ID = dr["ACTION_ID"].ToString(),
                        LEVEL_ID = Convert.ToDecimal(dr["LEVEL_ID"].ToString()),
                        LINK_ADDR = dr["LINK_ADDR"].ToString(),
                        BRE_NO_PRE = dr["BRE_NO_PRE"].ToString(),
                        TARGET = dr["TARGET"].ToString(),
                        BRE_TYPE = dr["BRE_TYPE"].ToString(),
                        LEVEL_NO = dr["LEVEL_NO"].ToString(),
                    });
                }
                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }
    }
}