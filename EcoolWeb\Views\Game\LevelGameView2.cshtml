﻿@model GameLeveViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

   // var panel_css = (Model?.Details?.CASH >= 0) ? "panel-info" : "panel-danger";
    var panel_css = "panel-danger";
    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();
}
<link href="~/Content/css/childrens-month.min.css?V=@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
<style>
    .sbc_field {
        ime-mode: disabled;
    }
</style>
<link href="~/Content/styles/animate.min.css?V=@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
<link rel="stylesheet" href="~/Content/styles/challenge-pass-check.min.css?V=@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");">
<link href="~/Content/styles/newRoulette.min.css?V=@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />

@using (Html.BeginForm("LevelGameView2", "Game", FormMethod.Post, new { id = "form1", name = "form1", @AutoComplete = "Off" }))
{
    @Html.HiddenFor(m => m.GAME_NO)
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.LEVEL_NO)
    @Html.HiddenFor(m => m.Coupons_ITem)
    @Html.HiddenFor(m => m.Details.LOADING_TIME)
    @Html.HiddenFor(m => m.LotteryPrize.PrizeId)
    @Html.HiddenFor(m => m.User.TEMP_USER_ID)
    <div id="MainView">
        <div class="row">
            <div class="col-md-8 col-md-offset-2" id="MainView2">
                <div class="panel with-nav-tabs @panel_css panel-Roulette" id="panel">
                    <div class="panel-heading">
                        <h1>
                            @if (Model.Main.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答 && Model.Details.LEVEL_TYPE == ADDT26_D.LevelType.Pay)
                            {
                                <strong class="hint">
                                    第@(Model.Details.G_ORDER_BY)題: <br />
                                    @(Model.Details.G_SUBJECT)
                                </strong>
                            }
                            else
                            {
                                @Html.Raw(Model.Details.LEVEL_NAME)
                            }
                        </h1>
                        @if (Model.Main.GAME_TYPE != (byte)ADDT26.GameType.有獎徵答 && Model.Details.LEVEL_TYPE != ADDT26_D.LevelType.Pay)
                        {
                            if (Model.Details.CASH > 0)
                            {
                                <strong class="hint">刷卡後將獲得@(Model.Details.CASH)點 </strong>
                            }
                            else
                            {
                                <strong class="hint">刷卡後將支付@(Math.Abs((int)Model.Details.CASH))點</strong>
                            }
                        }
                    </div>
                    <div class="panel-body">
                        <div >
                            <div class="input-group input-group-lg">
                                <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                @Html.EditorFor(model => model.GameUserID, new { htmlAttributes = new { @class = "form-control", @placeholder = "請感應數位學生證，或輸入學號，輸入班級+座號也可以。", @type = "number", @min = "0", @max = "9999999999", @onKeyPress = "call(event,this);" } })
                            </div>
                            @Html.ValidationMessageFor(model => model.GameUserID, "", new { @class = "text-danger" })
                        </div>
                        <br />
                        <div class="text-center">

                            @if (Model.Main.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答 && Model.Details.LEVEL_TYPE == ADDT26_D.LevelType.Pay)
                            {
                                <h2 style="color:red">我認為答案是:  @Html.Raw(Model.Details.LEVEL_NAME)</h2>
                            }
                            else
                            {
                                <h4>請感應數位學生證，或輸入學號，輸入班級+座號也可以。(EX:輸入班級+座號，若為一年一班1號，可輸入10101)</h4>
                                <br />
                                <div>
                                    <img src="~/Content/img/Asset1.png" style="height:100px;padding-right:10px" />
                                    <img src="~/Content/img/Asset2.png" style="height:100px;" />
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div style="margin: 0px auto;text-align:center">
                    <img id="ViewImg" src="@Model.Details.LEVEL_IMG_PATH" style="max-height:calc(100vh - 200px);margin: 0 auto;" class="img-responsive" />
                </div>
            </div>
        </div>

        @*<div id="DivAddButton">
            <i id="title" class="fa fa-arrow-left fa-3x"></i>
            <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
        </div>*@
    </div>
}
<div id="lottery" style="display:none">
    <div class="ready-box">
        <img src="~/Content/img/laba-run.gif" alt="">
        <button id="BtnStart" type="button" class="btn btn-start btn-lg btn-block">開始抽獎</button>
    </div>

    <div class="award-box">
        <strong class="award-head">恭喜抽中</strong>

        <div id="carousel-example-generic" class="carousel slide" data-ride="carousel">
            <!-- Wrapper for slides -->
            <div class="carousel-inner" role="listbox">
                <div class="item active">
                    <ul class="list-award">
                        <li class="clearfix">
                            <span id="pricename2">@Model.LotteryPrize?.PrizeName</span>
                            <span>X 1</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="award-foot">
            <button id="BtnSuccess" class="btn btn-success" style="display:none">確定領獎</button>
            <button id="Cancel" class="btn btn-default" style="display:none">放棄</button>
        </div>
    </div>
</div>
<div id="challenge-panel" class="challenge-panel-bg">
    <div id="challenge-ribbon" class="challenge-ribbon animated"></div>
    <div id="challenge-info" class="challenge-info animated"></div>
</div>

<div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />

        @if (Model?.Main?.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
        {
            <h3 style="color:#80b4fb">寫入中…</h3>
        }
        else
        {
            if (Model?.Details?.CASH > 0)
            {
                <h3 style="color:#80b4fb">讀取中…</h3>
            }
            else
            {
                <h3 style="color:#80b4fb">扣款中…</h3>
                @*<div class="challenge-loading" id="loading">
                        <img src="@Url.Content(SharedGlobal.Logo_loading)" />
                        <strong>扣款中…</strong>
                    </div>*@

            }
        }
    </div>
</div>

@Html.DropDownList("BackupLink", (IEnumerable<SelectListItem>)ViewBag.BackupLinkItem, new { @class = "pull-right btn btn-warning btn-sm", style = "position:fixed; bottom:0px; right:0;font-size:12px!important;" })
<div class="btn btn-success statusButton">
    連線正常
</div>

<div class="queueList" style="display:none;">
    <div class="alert alert-danger">處理駐列名單</div>
    <ul class="queueList_ul"></ul>
</div>
<span id="ErrorTimeStr" class="ErrorTimeStr">
    <br />
    <span id="ErrorTime"></span>秒
</span>


@section css{
    <link href="@Url.Content("~/Scripts/toastr/toastr.min.css")" rel="stylesheet" />

    <style>
        .ErrorTimeStr {
            position: fixed;
            right: 3px;
            top: 0px;
            font-size: 10px;
            color: #cecece;
            display: none;
        }

        .queueList {
            position: fixed;
            bottom: 80px;
            left: 9px;
            width: 300px;
            height: 70%;
            border: 1px solid #527459;
            background-color: white;
        }

        .queueList_ul {
            overflow: auto;
            width: 280px;
            height: 80%;
        }

    </style>
}

@section Scripts {
    <script src="@Url.Content("~/Scripts/toastr/toastr.min.js")"></script>
    <script src="~/Scripts/buzz/buzz.min.js"></script>
    <script language="JavaScript">

        // 網頁連結
        var nowRoot = $('#BackupLink :selected').val();

        var targetFormID = '#form1';
        var jQueueList = []; // 等待POST駐列
        var backGroundQueueInterval; // 背景觸發post ajax queue interval
        var lock = false; // 鎖定

        var game_trueSound = new buzz.sound("@Url.Content("~/Content/mp3/game_true.mp3")");
        var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
        var game_errorSound = new buzz.sound("@Url.Content("~/Content/mp3/game_error.mp3")");
        var game_OfflineSound = new buzz.sound("@Url.Content("~/Content/mp3/game_Offline.mp3")");

        $(document).ready(function () {
            $('.award-box').hide();
            const el = document.getElementById('GameUserID');
            el.focus();
            if (GameUserID.length > 0) {

                el.focus();
                el.setSelectionRange(10, 10);
            }
            else { el.focus(); }
            $(document).on('keypress', function (e) { //keypress
                if (e.which == 13) { //enter

                    var GameUserID = $("#@Html.IdFor(m=>m.GameUserID)");
                    //查詢動畫
                    //$('#loading').fadeIn(1000, function () {

                        //模擬成功&失敗(請修改)
                        //var yes = confirm('模擬成功失敗');
                        //if (yes) {

                        //} else {

                        //}

                    //}).css({ 'display': 'flex' });
                    if ($('#loading').is(":hidden")) { //判斷是否在讀取畫面

                        if (GameUserID.is(":focus")==false) { //判斷是否在刷卡的的input
                            GameUserID.focus();
                        }
                    }
                    else {
                        return false;
                    }

                }
            });
            function BtnStartBtn() {

                 $('.bg-lottery').addClass('bg-lottery-playing');
            $('.ready-box').addClass('ready-box-playing');
            $('.ready-box img').attr('src', '@Url.Content("~/Content/img/laba-run.gif")');

            var LOADING_TIME = $('#@Html.IdFor(m=>m.Details.LOADING_TIME)').val();

                var gameuserid = $('#@Html.IdFor(m=>m.GameUserID)').val();
                var TEMP_USER_ID = $("#User_TEMP_USER_ID").val();
            var level_no  = $('#@Html.IdFor(m=>m.LEVEL_NO)').val();
                var game_no = $('#@Html.IdFor(m=>m.GAME_NO)').val();
                var Coupons_ITem = $('#@Html.IdFor(m=>m.Coupons_ITem)').val();
                var PrizeId = $('#@Html.IdFor(m=>m.LotteryPrize.PrizeId)').val();


            jQueueList.unshift(
                {
                    gameuserid: gameuserid,
                    level_no: level_no,
                    game_no: game_no,
                    Coupons_ITem: Coupons_ITem,
                    User: { TEMP_USER_ID: TEMP_USER_ID },
                    LotteryPrize: { PrizeId: PrizeId },
                    Details: { LOADING_TIME: LOADING_TIME }
                });
                $("#lottery").attr("style", "");
                //$("#panel").attr("style", "display:none");
                //$("#MainView2").attr("style", "display:none");
                 $('.bg-lottery').addClass('bg-lottery-playing');
            //$('.ready-box').addClass('ready-box-playing');
            $('.ready-box img').attr('src', '@Url.Content("~/Content/img/laba-run.gif")');

                GotoGetPricName(jQueueList[0]);
           // GotoGetPricName();
            t = 2;//重製動畫秒數
            showTime();
            }
            //抽獎開始
            $('#BtnStart').on('click', function () {
               //game_trueSound.play();
            $('.bg-lottery').addClass('bg-lottery-playing');
            $('.ready-box').addClass('ready-box-playing');
            $('.ready-box img').attr('src', '@Url.Content("~/Content/img/laba-run.gif")');

            var LOADING_TIME = $('#@Html.IdFor(m=>m.Details.LOADING_TIME)').val();

                var gameuserid = $('#@Html.IdFor(m=>m.GameUserID)').val();
                var TEMP_USER_ID = $("#User_TEMP_USER_ID").val();
            var level_no  = $('#@Html.IdFor(m=>m.LEVEL_NO)').val();
                var game_no = $('#@Html.IdFor(m=>m.GAME_NO)').val();
                var Coupons_ITem = $('#@Html.IdFor(m=>m.Coupons_ITem)').val();
                var PrizeId = $('#@Html.IdFor(m=>m.LotteryPrize.PrizeId)').val();


            jQueueList.unshift(
                {
                    gameuserid: gameuserid,
                    level_no: level_no,
                    game_no: game_no,
                    Coupons_ITem: Coupons_ITem,
                    User: { TEMP_USER_ID: TEMP_USER_ID },
                    LotteryPrize: { PrizeId: PrizeId },
                    Details: { LOADING_TIME: LOADING_TIME }
                });
       //$("#lottery").attr("style", "");
       //         $("#panel").attr("style", "display:none");
       //         $("#MainView2").attr("style", "display:none");
                setTimeout('       GotoGetPricName(jQueueList[0]);', 1000);
              //  GotoGetPricName(jQueueList[0]);
           // GotoGetPricName();
            t = 2;//重製動畫秒數
            showTime();
        });
              //確定
            $('#BtnSuccess').on('click', function () {
                  var LOADING_TIME = $('#@Html.IdFor(m=>m.Details.LOADING_TIME)').val();
            var TEMP_USER_ID= $("#User_TEMP_USER_ID").val();
            var gameuserid = $('#@Html.IdFor(m=>m.GameUserID)').val();
            var level_no  = $('#@Html.IdFor(m=>m.LEVEL_NO)').val();
            var game_no = $('#@Html.IdFor(m=>m.GAME_NO)').val();
            var LotteryPrize_PrizeId = $("#LotteryPrize_PrizeId").val();

                var Coupons_ITem = $('#@Html.IdFor(m=>m.Coupons_ITem)').val();
                //console.log(LotteryPrize_PrizeId);
                //console.log("jQueueList長度" + jQueueList.length);
                if (jQueueList.length > 0) {
                    //console.log("jQueueList長度" + jQueueList.length);
                    for (var i = 0; i <= jQueueList.length; i++) {
                        jQueueList.shift();
                    }



                }
            jQueueList.unshift(
                {
                    GameUserID: gameuserid,
                    level_no: level_no,
                    game_no: game_no,
                    Coupons_ITem: Coupons_ITem,
                    User: { TEMP_USER_ID: TEMP_USER_ID},
                    User: { TEMP_USER_ID: TEMP_USER_ID},
                    LotteryPrize: { PrizeId: LotteryPrize_PrizeId},
                    Details: { LOADING_TIME: LOADING_TIME }
                });
                GoToLevelSave(jQueueList[0]);
              @*$(targetFormID).attr("action", "@Url.Action("LotteryPrizeSave")")

                jQueueList.unshift(
                    {
                        GameUserID: gameuserid,
                        level_no: level_no,
                        game_no: game_no,
                        Coupons_ITem: Coupons_ITem,
                        User: { TEMP_USER_ID: TEMP_USER_ID },
                        User: { TEMP_USER_ID: TEMP_USER_ID },
                        LotteryPrize: { PrizeId: LotteryPrize_PrizeId },
                        Details: { LOADING_TIME: LOADING_TIME }
                    });
            SavePricItem(jQueueList[0]);*@
            });
        
       //取消
            $('#Cancel').on('click', function () {






                        $("#lottery").attr("style", "display:none");
                        $("#panel").attr("style", "");
                        $("#MainView2").attr("style", "");
                        $('.ready-box').removeClass('ready-box-playing');
                        $('#@Html.IdFor(m=>m.GameUserID)').val('');
                  $('#@Html.IdFor(m=>m.GameUserID)').removeAttr("readonly");




        });

            ////監控網路狀態
            //if (typeof (navigator.onLine) != "undefined") {
            //    window.addEventListener('online', doOnline);
            //    window.addEventListener('offline', doOnline);
            //    doOnline()
            //}

            toastr.options = {
                "progressBar": true,
                "positionClass": "toast-bottom-center",
                "timeOut": "2500",
                "extendedTimeOut": "1000",
                "closeButton": true,
            };

            if ($("#BackupLink option").size() <= 1) {
                $('#BackupLink').hide()
            }

            $("#@Html.IdFor(m=>m.GameUserID)").focus();
            Wa_SetImgAutoSize();

            // 資料localStorage載入
            // Check browser support
            if (typeof (Storage) !== "undefined") {
                var queJson = localStorage.getItem("jQueueList");

                if (queJson !== null) {
                    jQueueList = JSON.parse(queJson);
                }

                var queListUlJson = localStorage.getItem("queueList_ul");
                if (queListUlJson !== null) {
                    $(".queueList_ul").html(JSON.parse(queListUlJson));
                    statusButtonChangeColor();
                }
            }

            setBackGroundQueueService(false);
        });

        //切換伺務器-計時器
        var timeNum = 0
        var timeObject
        var EorrMaxTime = 600 //10分(600) ，當進入紅燈超過10分，切換伺務器

        //開始計算 切換伺務器-計時器
        function timedCount() {

            $('#ErrorTime').text(timeNum)
            timeNum = timeNum + 1
            timeObject = setTimeout("timedCount()", 1000)

            if (timeNum > EorrMaxTime) {
                AutoBackupLink() // 切換Server
                timeNum = timeNum - 60 //切換伺務器後 -60秒，當60秒後未正常在切換換另一台
            }
        }
        var t = 8; //預設動畫秒數
        //動畫後開獎倒數
        function showTime() {
            if (t == 0) {
                $('.ready-box').hide();
                $('.award-box').show();
                $('.bg-lottery').removeClass('bg-lottery-playing');
                $('.bg-lottery').addClass('bg-lottery-award');
                clearTimeout('showTime()');
            } else {
                t -= 1;
                setTimeout('showTime()', 1000);
            }
        }
        //停用 切換伺務器 ，時間歸 0
        function clearTime() {

            clearTimeout(timeObject)
            ResetZ()
        }
        //時間歸 0, 切換伺務器-計時器
        function ResetZ() {

            timeNum = 0
            $('#ErrorTime').text(timeNum)
        }
            function BtnSuccess() {
                  var LOADING_TIME = $('#@Html.IdFor(m=>m.Details.LOADING_TIME)').val();
            var TEMP_USER_ID= $("#User_TEMP_USER_ID").val();
            var gameuserid = $('#@Html.IdFor(m=>m.GameUserID)').val();
            var level_no  = $('#@Html.IdFor(m=>m.LEVEL_NO)').val();
            var game_no = $('#@Html.IdFor(m=>m.GAME_NO)').val();
            var LotteryPrize_PrizeId = $("#LotteryPrize_PrizeId").val();

                var Coupons_ITem = $('#@Html.IdFor(m=>m.Coupons_ITem)').val();
                //console.log(LotteryPrize_PrizeId);
                //console.log("jQueueList長度" + jQueueList.length);
                if (jQueueList.length > 0) {
                    //console.log("jQueueList長度" + jQueueList.length);
                    for (var i = 0; i <= jQueueList.length; i++) {
                        jQueueList.shift();
                    }



                }
            jQueueList.unshift(
                {
                    GameUserID: gameuserid,
                    level_no: level_no,
                    game_no: game_no,
                    Coupons_ITem: Coupons_ITem,
                    User: { TEMP_USER_ID: TEMP_USER_ID},
                    User: { TEMP_USER_ID: TEMP_USER_ID},
                    LotteryPrize: { PrizeId: LotteryPrize_PrizeId},
                    Details: { LOADING_TIME: LOADING_TIME }
                });
                GoToLevelSave(jQueueList[0]);

            }
        // 切換Server
        $("#BackupLink").change(function () {
            var nowSer = $('#BackupLink :selected').text();

            if (confirm("您要切換到 " + nowSer+" 嗎")) {
                nowRoot = $('#BackupLink :selected').val();
            }
        });

        function AutoBackupLink() {
            var nowIndex = $('#BackupLink option:selected').index();
            var maxIndex = $("#BackupLink option").size()-1;

            if (nowIndex < maxIndex) {
                nowIndex++;
                $("#BackupLink")[0].selectedIndex = nowIndex;
                nowRoot = $('#BackupLink :selected').val();
                //console.log('切換到' + $('#BackupLink :selected').text());
            }
            else {
                $("#BackupLink")[0].selectedIndex = 0;
                nowRoot = $('#BackupLink :selected').val();
                //console.log('切換到' + $('#BackupLink :selected').text());
            }
        }

        function doOnline() {
            if (jQueueList.length > 0) {
                if (lock == false) {
                    GoToLevelSave_InBackGround(jQueueList);
                }
            }
        }

        function setBackGroundQueueService(turnOn) {
            if (turnOn == true) {
                // 每15秒重新post ajax queue資料
                backGroundQueueInterval = setInterval(function () {
                    //console.log("背景處理觸發，剩餘駐列筆數:" + jQueueList.length);
                    if (!navigator.onLine) {
                        statusButtonChangeColor();
                        return;
                    }
                    if (jQueueList.length > 0) {
                        if (lock == false) {
                            GoToLevelSave_InBackGround(jQueueList);
                        }
                    }
                }, 15 * 1000);
            } else {
                clearInterval(backGroundQueueInterval);
            }
        }

       function Wa_SetImgAutoSize() {
           var img = $('#ViewImg'); //獲取圖片

           var windowHeight = $(window).height();
           var panelHeight = $("#panel").height();

           var MaxHeight = (windowHeight - panelHeight - 30); //設置圖片高度界限

           if (MaxHeight>200) {
               var HeightWidth = img.offsetHeight / img.offsetWidth; //設置高寬比
               var WidthHeight = img.offsetWidth / img.offsetHeight; //設置寬高比

               img.height = MaxHeight;
               img.width = MaxHeight * WidthHeight;

               (img).css({
                   "width": img.width, "height": img.height
               });
           }
       }

        function OnBack() {
            $(targetFormID).attr("action", "@Url.Action("PassModeForUSER", "Game")")
            $('#@Html.IdFor(m=>m.LEVEL_NO)').val('')
             $(targetFormID).submit();
         }

        function call(e, input) {
            console.log("call");
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                event.preventDefault();

                var GameUserID = $('#@Html.IdFor(m=>m.GameUserID)');
                GameUserID.prop('readonly', true);

                var OK = false;

                if (GameUserID.val().length == 10) {
                    
                    $.ajax({
                        type: "POST",
                        url: nowRoot + "@Url.Action("FindPersonFromStudentNumberBase", "Game")",

                       data: {
                           USER_NO: GameUserID.val(),
                           SCHOOL_NO: $('#SCHOOL_NO').val(),

                    }, // serializes the form's elements.
                        async: false, // 非同步
                        timeout: 3000,
                        success: function (data) {
                            console.log(data);
                            if (data.IsOK == false) {
                                alert(data.Message);
                                GameUserID.val('')
                                OK = false;
                            }
                            else {
                           

                            
                                $("#lottery").attr("style", "");
                                $('.ready-box').show();
                                $("#panel").attr("style", "display:none");
                                $("#MainView2").attr("style", "display:none");
                                OK = true;
                            }
                        }

                    });
                 //   OK = true;
                }
                //else if (GameUserID.val().length >= 20) //超過20碼 抓前10碼
                //{
                    var ThisVal = GameUserID.val().substring(0, 10);
                    GameUserID.val(ThisVal)

                         $.ajax({
                        type: "POST",
                        url: nowRoot + "@Url.Action("FindPersonFromStudentNumberBase", "Game")",

                       data: {
                           USER_NO: GameUserID.val(),
                           SCHOOL_NO: $('#SCHOOL_NO').val(),

                    }, // serializes the form's elements.
                        async: false, // 非同步
                        timeout: 3000,
                        success: function (data) {
                            console.log(data);
                            if (data.IsOK == false) {
                                alert(data.Message);
                                GameUserID.val('')
                                OK = false;
                            }
                            else {


                                $("#lottery").attr("style", "");
                                $('.ready-box').show();
                                $("#panel").attr("style", "display:none");
                                $("#MainView2").attr("style", "display:none");
                                OK = true;
                            }
                        }

                    });
                //}
                //else {
                //    alert("對應不到相應的數位學生證!");
                //    GameUserID.val('')
                //}

                if (OK) {
                    setTimeout(function () {
                        OnKeyinUse();
                    });
                }
                else {
                    GameUserID.prop('readonly', false);
                }

            }
        }
        function errorOccur(game_UserID) {
            $(".queueList_ul").append($("<li class='text-danger'><span>" + game_UserID + "</span> (連線失敗)</li>"));
            toastrSuccess("卡號 " + game_UserID + "<br />感應成功。");
            //console.log("卡號 " + game_UserID + "假的完成");

            statusButtonChangeColor();
            apiEndingJob();

        }
        function OnKeyinUse() {
            //$('#loading').fadeIn(500);
            console.log("OnKeyinUse");
            var LOADING_TIME = $('#@Html.IdFor(m=>m.Details.LOADING_TIME)').val();

            var gameuserid = $('#@Html.IdFor(m=>m.GameUserID)').val();
            var level_no  = $('#@Html.IdFor(m=>m.LEVEL_NO)').val();
            var game_no = $('#@Html.IdFor(m=>m.GAME_NO)').val();
            var Coupons_ITem = $('#@Html.IdFor(m=>m.Coupons_ITem)').val();
              var PrizeId=$('#@Html.IdFor(m=>m.LotteryPrize.PrizeId)').val();

            t = 8;//重製動畫秒數
          //  $('#@Html.IdFor(m=>m.GameUserID)').val('');
            //console.log("jQueueList長度" + jQueueList.length);
            if (jQueueList.length > 0) {
                //console.log("jQueueList長度" + jQueueList.length);
                for (var i = 0; i <= jQueueList.length; i++) {
                    jQueueList.shift();
                }



            }
            //setTimeout(
                //function () {
                    lock = true;

                    // push header
                    jQueueList.unshift(
                        {
                            gameuserid: gameuserid,
                            level_no: level_no,
                            game_no: game_no,
                            Coupons_ITem: Coupons_ITem,
                            LotteryPrize: { PrizeId: LotteryPrize_PrizeId },
                            Details: { LOADING_TIME: LOADING_TIME },
                            SCHOOL_NO: $('#SCHOOL_NO').val(),
                           
                        });
                    $.ajax({
                        type: "POST",
                    url: nowRoot + "@Url.Action("FindPersonForUSERNO", "Game")",
                    contentType: 'application/json',
                        data: JSON.stringify(jQueueList[0]), // serializes the form's elements.
                    async: false, // 非同步
                    timeout: 3000,
                        success: function (data) {
                            console.log(data.IsOK);
                            if (data.IsOK == true) {
                                $('.bg-lottery').addClass('bg-lottery-playing');
            $('.ready-box').addClass('ready-box-playing');
            $('.ready-box img').attr('src', '@Url.Content("~/Content/img/laba-run.gif")');

            var LOADING_TIME = $('#@Html.IdFor(m=>m.Details.LOADING_TIME)').val();

                var gameuserid = $('#@Html.IdFor(m=>m.GameUserID)').val();
                var TEMP_USER_ID = $("#User_TEMP_USER_ID").val();
            var level_no  = $('#@Html.IdFor(m=>m.LEVEL_NO)').val();
                var game_no = $('#@Html.IdFor(m=>m.GAME_NO)').val();
                var Coupons_ITem = $('#@Html.IdFor(m=>m.Coupons_ITem)').val();
                var PrizeId = $('#@Html.IdFor(m=>m.LotteryPrize.PrizeId)').val();


            jQueueList.unshift(
                {
                    gameuserid: gameuserid,
                    level_no: level_no,
                    game_no: game_no,
                    Coupons_ITem: Coupons_ITem,
                    User: { TEMP_USER_ID: TEMP_USER_ID },
                    LotteryPrize: { PrizeId: PrizeId },
                    Details: { LOADING_TIME: LOADING_TIME }
                });
                                $("#lottery").attr("style", "");
                                $("#panel").attr("style", "display:none");
                                $("#MainView2").attr("style", "display:none");
                GotoGetPricName(jQueueList[0]);
           // GotoGetPricName();
            t = 2;//重製動畫秒數
                                showTime();



                               // Lottery(jQueueList[0]);

                            }
                            else {
                                game_falseSound.play();
                                toastrError(data.Message);

                                $('#@Html.IdFor(m=>m.GameUserID)').val('');
                                $('#@Html.IdFor(m=>m.GameUserID)').removeAttr("readonly");
                                $("#MainView2").attr("style", "");
                                $("#panel").attr("style", "");
                                $("#lottery").attr("style", "display:none");
                                return;
                            }
                        }
                    });
                    //InfoCheck(jQueueList[0]);
                    //Lottery(jQueueList[0]);
                   // Lottery(jQueueList[0]);
                //},
                LOADING_TIME * 1000
            //);

        }
        @*function InfoCheck(obj) {
            var hasError = false;
            if (navigator.onLine) {
              $.ajax({
                    type: "POST",
                    url: nowRoot + "@Url.Action("CheckInfodetail", "Game")",
                    contentType: 'application/json',
                    data: JSON.stringify(obj), // serializes the form's elements.
                    async: false, // 非同步
                    timeout: 3000,
                    success: function (data) {
                        //console.log(data);
                        if (data.IsOK == true) {
                            Lottery(obj);
                        }
                        else {
                            toastrError(data.Message);
                            $('#@Html.IdFor(m=>m.GameUserID)').val('');
                            return;
                        }
                        //if (data.LotteryPrize != null && data.LotteryPrize != undefined) {

                        //    toastrSuccess(data.Message);
                        //}

                        // else if (data.IsOK == false) {
                        //    game_falseSound.play();
                        //    toastrError(data.Message);

                        //} else {
                        //    game_falseSound.play();
                        //    toastrError(data);
                        //}
                    },
                    error: function (jqXHR, exception) {
                        hasError = true;
                    },
                    complete: function (data) {
                        //發生連線錯誤
                        if (hasError) {
                            errorOccur(obj.gameuserid);
                            return; // 不繼續往下執行ajax作業
                        }

                        jQueueList.splice(0, 1);
                        $(".queueList_ul li span:contains('" + obj.gameuserid + "')").parent().remove();

                        if (jQueueList.length === 0) {
                            // finished all queue
                            statusButtonChangeColor();
                            apiEndingJob();

                        } else {
                           // $('#@Html.IdFor(m=>m.GameUserID)').val('');
                            // 處理剩下的
                            setTimeout(GoToLevelSave_InBackGround(jQueueList));
                        }
                    }
                });
            }

        }*@
        function Lottery(obj) {
            console.log("Lottery");
            $.ajax({

                   type: "POST",
                    url: nowRoot + "@Url.Action("FindPersonForUSERNO", "Game")",
                    contentType: 'application/json',
                    data: JSON.stringify(obj), // serializes the form's elements.
                    async: false, // 非同步
                    timeout: 3000,
                success: function (data) {
                    if (data.IsOK == true ) {
                        $("#lottery").attr("style", "");
                        $("#panel").attr("style", "display:none");
                        $("#MainView2").attr("style", "display:none");
                        setTimeout(function () {

                            BtnSuccess();

                            //location.reload();
                        }, 1000);
                    }
                    else {
                        game_falseSound.play();
                        $('#@Html.IdFor(m=>m.GameUserID)').val('');
                        $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', false);
                        toastrError(data.Message);
                    }
                }
            });


        }
         function SavePricItem(obj)
        {
            //console.log("Level Saving...   isAuto: false");
            //console.log('目前伺務器:' + nowRoot);
             $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', true);

            var hasError = false;
            if (navigator.onLine) {
              $.ajax({
                    type: "POST",
                    url: nowRoot + "@Url.Action("LotteryPrizeSave", "Game")",
                    contentType: 'application/json',
                    data: JSON.stringify(obj), // serializes the form's elements.
                    async: false, // 非同步
                    timeout: 3000,
                    success: function (data) {
                        //console.log(data);
                        if (data.IsOK == true) {


                            $("#lottery").attr("style", "display:none");
                            $("#panel").attr("style", "");
                            $("#MainView2").attr("style", "");
                            $('.ready-box').removeClass('ready-box-playing');
                            $('#@Html.IdFor(m=>m.GameUserID)').val('');
                                 $('#@Html.IdFor(m=>m.GameUserID)').removeAttr("readonly");
                            toastrSuccess(data.Message);

                        }
                        else {
                             $("#lottery").attr("style", "display:none");
                            $("#panel").attr("style", "");
                            $("#MainView2").attr("style", "");
                            $('.ready-box').removeClass('ready-box-playing');
                            $('#@Html.IdFor(m=>m.GameUserID)').val('');
                                 $('#@Html.IdFor(m=>m.GameUserID)').removeAttr("readonly");
                            toastrError(data.Message);


                        }
                        //if (data.LotteryPrize != null && data.LotteryPrize != undefined) {

                        //    toastrSuccess(data.Message);
                        //}

                        // else if (data.IsOK == false) {
                        //    game_falseSound.play();
                        //    toastrError(data.Message);

                        //} else {
                        //    game_falseSound.play();
                        //    toastrError(data);
                        //}
                    },
                    error: function (jqXHR, exception) {
                        hasError = true;
                    },
                    complete: function (data) {
                        //發生連線錯誤
                        if (hasError) {
                            errorOccur(obj.gameuserid);
                            return; // 不繼續往下執行ajax作業
                        }

                        jQueueList.splice(0, 1);
                        $(".queueList_ul li span:contains('" + obj.gameuserid + "')").parent().remove();

                        if (jQueueList.length === 0) {
                            // finished all queue
                            statusButtonChangeColor();
                            apiEndingJob();

                        } else {
                           // $('#@Html.IdFor(m=>m.GameUserID)').val('');
                            // 處理剩下的
                            //setTimeout(GoToLevelSave_InBackGround(jQueueList));
                        }
                    }
                });
            }
        }
        function GetPersonInfo(obj) {
            $.ajax({
                 type: "POST",
                    url: nowRoot + "@Url.Action("GetLotteryPrice", "Game")",
                    contentType: 'application/json',
                    data: JSON.stringify(obj), // serializes the form's elements.
                    async: false, // 非同步
                    timeout: 3000,
                success: function (data) {
                    //console.log(data);
                    if (data.IsOK != true) {

                        game_falseSound.play();
                        $("#lottery").attr("style", "display:none");
                        $("#panel").attr("style", "");
                        $("#MainView2").attr("style", "");
                        $('.ready-box').removeClass('ready-box-playing');
                        toastrError(data.Message);
                        $('#@Html.IdFor(m=>m.GameUserID)').val('');
                    }
                    else {
                        console.log("GetPersonInfo");
                        $("#lottery").attr("style", "");

                        $("#panel").attr("style", "display:none");
                        $("#MainView2").attr("style", "display:none");
                        GotoGetPricName(obj);
                    }
                }
            });

        }
        function GotoGetPricName(obj)
        {
        
            var jQueueList = [];
         
            //console.log("Level Saving...   isAuto: false");
            //console.log('目前伺務器:' + nowRoot);
             $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', true);
      
            var hasError = false;
            if (navigator.onLine) {
              $.ajax({
                    type: "POST",
                    url: nowRoot + "@Url.Action("GetPricName", "Game")",
                    contentType: 'application/json',
                    data: JSON.stringify(obj), // serializes the form's elements.
                    async: false, // 非同步
                    timeout: 3000,
                    success: function (data) {
                        //console.log(data);
                        //console.log(data.LotteryPrize);
                        //console.log(data.LotteryPrize.PrizeName);
                          $("#lottery").attr("style", "");
                     $('.bg-lottery').addClass('bg-lottery-playing');
                           $('.ready-box img').attr('src', '@Url.Content("~/Content/img/laba-run.gif")');
                        if (data.IsOK == true && data.LotteryPrize != null && data.LotteryPrize != undefined) {
                            game_trueSound.play();
                            const el = document.getElementById('GameUserID');
                            GameUserID.focus();
                            $("#LotteryPrize_PrizeId").val(data.LotteryPrize.PrizeId);
                         //game_trueSound.play();
                            $("#pricename2").html("");
                            $("#pricename2").html(data.LotteryPrize.PrizeName);
                            setTimeout(function () {
                                $('.ready-box').hide();
                                $('.award-box').show();
                                $("#panel").attr("style", "display:none");
                                $("#MainView2").attr("style", "display:none");
                                $("#lottery").attr("style", "");
                            }, 4000);
                            setTimeout(function () {
                             
                               BtnSuccess();

                                //location.reload();
                            }, 4000);

                            // toastrSuccess(data.Message);
                        }
                        else {

                            game_falseSound.play();
                             $("#lottery").attr("style", "display:none");
                            $("#panel").attr("style", "");
                            $("#MainView2").attr("style", "");
                            $('.ready-box').removeClass('ready-box-playing');
                            toastrError(data.Message);
                            const el = document.getElementById('GameUserID');
                            GameUserID.focus();
                            $('#@Html.IdFor(m=>m.GameUserID)').val('');
                            $('#@Html.IdFor(m=>m.GameUserID)').removeAttr('readonly');
                        }
                        // else if (data.IsOK == false) {
                        //    game_falseSound.play();
                        //    toastrError(data.Message);

                        //} else {
                        //    game_falseSound.play();
                        //    toastrError(data);
                        //}
                    },
                    error: function (jqXHR, exception) {
                        hasError = true;
                    },
                    complete: function (data) {
                        @*//發生連線錯誤
                        if (hasError) {
                            errorOccur(obj.gameuserid);
                            return; // 不繼續往下執行ajax作業
                        }

                        jQueueList.splice(0, 1);
                        $(".queueList_ul li span:contains('" + obj.gameuserid + "')").parent().remove();

                        if (jQueueList.length === 0) {
                            // finished all queue
                            statusButtonChangeColor();
                            apiEndingJob();

                        } else {
                          //  $('#@Html.IdFor(m=>m.GameUserID)').val('');
                            // 處理剩下的
                            setTimeout(GoToLevelSave_InBackGround(jQueueList));*@
                        //}
                    }
                });
            }
        }
        /**
         * 單次傳卡號
         * */
        function GoToLevelSave(obj) {
            console.log("Level Saving...   isAuto: false");
            console.log('目前伺務器:' + nowRoot);
            $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', true);

            var hasError = false;
            var LOADING_TIME = $('#@Html.IdFor(m=>m.Details.LOADING_TIME)').val();
            var TEMP_USER_ID= $("#User_TEMP_USER_ID").val();
            var gameuserid = $('#@Html.IdFor(m=>m.GameUserID)').val();
            var level_no  = $('#@Html.IdFor(m=>m.LEVEL_NO)').val();
            var game_no = $('#@Html.IdFor(m=>m.GAME_NO)').val();
            var LotteryPrize_PrizeId = $("#LotteryPrize_PrizeId").val();

            jQueueList.unshift(
                {
                    GameUserID: gameuserid,
                    level_no: level_no,
                    game_no: game_no,
                    Coupons_ITem: Coupons_ITem,
                    User: { TEMP_USER_ID: TEMP_USER_ID },

                    LotteryPrize: { PrizeId: LotteryPrize_PrizeId },
                    Details: { LOADING_TIME: LOADING_TIME }
                });

            // 判斷網路連線是否正常
            if (navigator.onLine) {
                jQueueList.splice(0, 1);
                $(".queueList_ul li span:contains('" + obj.gameuserid + "')").parent().remove();
                //if (jQueueList.length === 0) {
                    // finished all queue
                    //statusButtonChangeColor();
                    //apiEndingJob();

                      //console.log("Level Saving...   isAuto: true");
            //console.log('目前伺務器:'+nowRoot);
            console.log(JSON.stringify(jQueueList));
            var hasError = false;
             $.ajax({
                 type: "POST",
                 url: nowRoot + "@Url.Action("LotteryLevelSaveInBackGround", "Game")",
                 contentType: 'application/json',
                 data: JSON.stringify(jQueueList), // serializes the form's elements.
                 async: false, // 非同步
                 timeout: 3000,
                 success: function (data) {
                     console.log(data.TEMP_USER_ID);
                     if (data.IsOK == true) {
                      $("#lottery").attr("style", "display:none");
                            $("#panel").attr("style", "");
                            $("#MainView2").attr("style", "");
                            $('.ready-box').removeClass('ready-box-playing');
                            $('#@Html.IdFor(m=>m.GameUserID)').val('');
                         $('#@Html.IdFor(m=>m.GameUserID)').removeAttr("readonly");
                         GameUserID.focus();
                         toastrSuccess(data.Message);
                 
                     }
                     else if (data.IsOK == false) {
                          $('#@Html.IdFor(m=>m.GameUserID)').val('');
                         $('#@Html.IdFor(m=>m.GameUserID)').removeAttr("readonly");
                         const el = document.getElementById('GameUserID');
                         GameUserID.focus();
                         toastrError(data.Message);

                     }
                     else {
                          $('#@Html.IdFor(m=>m.GameUserID)').val('');
                         $('#@Html.IdFor(m=>m.GameUserID)').removeAttr("readonly");
                         const el = document.getElementById('GameUserID');
                         GameUserID.focus();
                         toastrError(data.Message);
                     }
                 },
                 error: function (jqXHR, exception) {
                     hasError = true;
                 },
                 complete: function (data) {
                     //發生連線錯誤
                     if (hasError) {
                         apiEndingJob();
                         return; // 不繼續往下執行ajax作業
                     }

                     // finished all queue
                     jQueueList = [];
                     $(".queueList_ul").empty();

                     statusButtonChangeColor();
                     apiEndingJob();
                 }
             });

                @*} else { $('#@Html.IdFor(m=>m.GameUserID)').val('');}*@

            }
            else {
                //網路連線不正常，直接跳錯
                errorOccur(obj.gameuserid);
            }

            /**
             * 錯誤發生指令
             * */


        }

        /**
         * 批次傳卡號
         */
        function GoToLevelSave_InBackGround(list) {
            //console.log("Level Saving...   isAuto: true");
            //console.log('目前伺務器:'+nowRoot);
            var hasError = false;
             $.ajax({
                 type: "POST",
                 url: nowRoot + "@Url.Action("LotteryLevelSaveInBackGround", "Game")",
                 contentType: 'application/json',
                 data: JSON.stringify(list), // serializes the form's elements.
                 async: false, // 非同步
                 timeout: 3000,
                 success: function (data) {
                     //console.log(data.TEMP_USER_ID);
                     if (data.IsOK == true) {
                         $("#User_TEMP_USER_ID").val(data.TEMP_USER_ID);
                     }
                     else if (data.IsOK == false) {
                         toastrError(data.Message);
                         GameUserID.focus();
                     }
                     else { toastrError(data.Message); GameUserID.focus();}
                 },
                 error: function (jqXHR, exception) {
                     hasError = true;
                 },
                 complete: function (data) {
                     //發生連線錯誤
                     if (hasError) {
                         apiEndingJob();
                         return; // 不繼續往下執行ajax作業
                     }

                     // finished all queue
                     jQueueList = [];
                     $(".queueList_ul").empty();

                     statusButtonChangeColor();
                     apiEndingJob();
                 }
             });
        }

        // toastr
        function toastrSuccess(msg) {
            $('#loading').hide();
           // $('#challenge-panel').css({ 'display': 'flex' });
           // $('#challenge-ribbon').css({ 'display': 'flex' });
           // $('#challenge-info').addClass('challenge-info-success').removeClass('challenge-info-failed').text('~ 感應成功 ~');
           //$('#challenge-panel').fadeOut(2000);
           toastr["success"](msg).css("width", "80%").css("margin", "2% 10%");
        }
        function toastrError(msg) {
            //$('#loading').hide();
            //$('#challenge-ribbon').css({ 'display': 'none' });
            //$('#challenge-panel').css({ 'display': 'flex' });
            //$('#challenge-info').addClass('challenge-info-failed').removeClass('challenge-info-success').text(msg);
            //$('#challenge-panel').fadeOut(4000);
            toastr["error"](msg).css("width", "80%").css("margin", "2% 10%");
        }

        // 顏色改變
        function statusButtonChangeColor() {
            var hasError = $(".queueList_ul li").length > 0;
            var sbtn_el = $(".statusButton");
            sbtn_el.removeClass("btn-success");
            sbtn_el.removeClass("btn-danger");
            sbtn_el.removeClass("btn-warning");

            // 網路異常為橘色
            if (!navigator.onLine) {
                sbtn_el.addClass("btn-warning");
                sbtn_el.text("網路異常");
                //console.log("navigator.onLine 為 false");
                game_OfflineSound.play();
                return;
            }

            if (hasError) {
                sbtn_el.addClass("btn-danger");
                sbtn_el.text("服務異常");
                game_errorSound.play();
                ErrorTimeApi(true)

                $("#BackupLink").prop("disabled", false);

            } else {
                sbtn_el.addClass("btn-success");
                sbtn_el.text("連線正常");
                $("#BackupLink").prop("disabled", true);
                ErrorTimeApi(false)
            }

        }

        function apiEndingJob() {
            $('#loading').fadeOut(500);
            $('#@Html.IdFor(m=>m.GameUserID)').removeAttr('readonly');
            lock = false;

            if (typeof (Storage) !== "undefined") {
                // 更新storage
                localStorage.setItem("jQueueList", JSON.stringify(jQueueList));
                localStorage.setItem("queueList_ul", JSON.stringify($(".queueList_ul").html()));

            }
        }

        // 狀態駐列
        $(".statusButton")
            .hover(function () {
                var hasError = $(".queueList_ul li").length > 0;
                if (hasError) {
                    $(".queueList").show();
                }
            }, function () {
                $(".queueList").hide();
            });

        //當紅燈時 (Error) 開始計時切換伺務器計時器
        function ErrorTimeApi(hasError) {

            var maxIndex = $("#BackupLink option").size();

             //伺務器大於1台才跑
            if (maxIndex >1) {
                if (hasError) { //紅燈 Error
                    if ($("#ErrorTimeStr").is(":hidden")) {
                        $("#ErrorTimeStr").show();
                        timedCount()
                    }
                }
                else {

                    if ($("#ErrorTimeStr").is(":hidden") == false) {
                        $("#ErrorTimeStr").hide()
                        clearTime()
                    }
                }
            }
        }
    </script>
}