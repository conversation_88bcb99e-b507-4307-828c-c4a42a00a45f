// JavaScript for ADDI03 Query2 page - 閱讀護照完成一覽表
$(document).ready(function() {
    // 護照完成一覽表模組
    const query2Module = {
        targetFormID: '#ADDI03',
        
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.enhanceUserExperience();
        },

        bindEvents: function() {
            // 綁定下拉選單變更事件
            $("#ddlGrade").on('change', this.handleSubmitOnChange.bind(this));
            $("#ddlCLASS_NO").on('change', this.handleSubmitOnChange.bind(this));
            
            // 綁定按鈕事件
            $('.btn-pink').on('click', this.handleGradeFilter.bind(this));
            $('input[value="清除搜尋"]').on('click', this.handleClear.bind(this));
            
            // 綁定搜尋表單提交
            $(this.targetFormID).on('submit', this.handleFormSubmit.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.submit_onchange = this.handleSubmitOnChange.bind(this);
            window.DoBooKGrade = this.handleGradeFilter.bind(this);
            window.todoClear = this.handleClear.bind(this);
        },

        handleSubmitOnChange: function() {
            try {
                this.submitForm();
            } catch (error) {
                console.error('下拉選單變更提交時發生錯誤:', error);
                this.showMessage('系統發生錯誤，請稍後再試', 'error');
            }
        },

        handleGradeFilter: function(gradeValue) {
            try {
                // 如果是事件對象，從按鈕的data屬性獲取值
                if (typeof gradeValue === 'object' && gradeValue.target) {
                    gradeValue = $(gradeValue.target).data('grade') || $(gradeValue.target).attr('data-grade') || '';
                }

                console.log('設置年級篩選:', gradeValue);
                $('#ddlBooKGrade').val(gradeValue);

                // 更新按鈕狀態
                this.updateButtonStates(gradeValue);

                this.submitForm();
            } catch (error) {
                console.error('年級篩選時發生錯誤:', error);
                this.showMessage('篩選時發生錯誤，請稍後再試', 'error');
            }
        },

        updateButtonStates: function(activeValue) {
            // 移除所有按鈕的active狀態
            $('.btn-pink').removeClass('active');
            
            // 根據值設置對應按鈕為active
            if (activeValue === '' || !activeValue) {
                $('.btn-pink').first().addClass('active'); // 全部按鈕
            } else if (activeValue === 'ALL') {
                $('.btn-pink').last().addClass('active'); // 全部完成護照學生按鈕
            }
        },

        handleClear: function() {
            try {
                console.log('清除搜尋條件');
                
                // 重設表單元素
                $(this.targetFormID).find(":input,:selected").each(function(i) {
                    const $element = $(this);
                    const type = $element.attr('type');
                    const isReadonly = $element.attr('readonly');
                    const tag = this.tagName.toLowerCase();

                    // 只處理非唯讀元素
                    if (isReadonly !== 'readonly' && isReadonly !== true) {
                        if (type === 'radio' || type === 'checkbox') {
                            if ($element.attr("title") === 'Default') {
                                this.checked = true;
                            } else {
                                this.checked = false;
                            }
                        } else if (tag === 'select') {
                            // 下拉式選單重設為第一個選項
                            this.selectedIndex = 0;
                        } else if (type === 'text' || type === 'hidden' || type === 'password' || type === 'textarea') {
                            this.value = '';
                        }
                    }
                });

                // 重設年級篩選
                $('#ddlBooKGrade').val('');
                this.updateButtonStates('');

                // 提交表單
                this.submitForm();
                
                this.showMessage('搜尋條件已清除', 'success');
            } catch (error) {
                console.error('清除搜尋條件時發生錯誤:', error);
                this.showMessage('清除時發生錯誤，請稍後再試', 'error');
            }
        },

        handleFormSubmit: function(event) {
            try {
                // 顯示載入狀態
                this.showLoadingState(true);
                
                // 記錄搜尋條件
                const searchData = {
                    keyword: $('#whereKeyword').val(),
                    grade: $('#ddlGrade').val(),
                    classNo: $('#ddlCLASS_NO').val(),
                    bookGrade: $('#ddlBooKGrade').val()
                };
                
                console.log('提交搜尋條件:', searchData);
                
                // 表單會自然提交，這裡只是記錄和顯示狀態
                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                this.showLoadingState(false);
                this.showMessage('提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        submitForm: function() {
            const $form = $(this.targetFormID);
            $form.attr('enctype', 'multipart/form-data');
            $form.attr('action', window.ADDI03_QUERY2_URLS.query2);
            $form.submit();
        },

        showLoadingState: function(isLoading) {
            const $submitBtn = $('input[type="submit"]');
            const $clearBtn = $('input[value="清除搜尋"]');
            
            if (isLoading) {
                $submitBtn.prop('disabled', true).val('搜尋中...');
                $clearBtn.prop('disabled', true);
            } else {
                $submitBtn.prop('disabled', false).val('搜尋');
                $clearBtn.prop('disabled', false);
            }
        },

        enhanceUserExperience: function() {
            // 添加鍵盤事件支援
            $('#whereKeyword').on('keypress', function(e) {
                if (e.which === 13) { // Enter鍵
                    e.preventDefault();
                    $(this.targetFormID).submit();
                }
            }.bind(this));

            // 添加表單元素焦點效果
            $('.form-control').on('focus', function() {
                $(this).closest('.form-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.form-group').removeClass('focused');
            });

            // 添加按鈕hover效果增強
            $('.btn-pink').on('mouseenter', function() {
                if (!$(this).hasClass('active')) {
                    $(this).addClass('btn-hover');
                }
            }).on('mouseleave', function() {
                $(this).removeClass('btn-hover');
            });

            // 添加表格行hover效果
            $('.table-ecool tbody tr').on('mouseenter', function() {
                $(this).addClass('table-row-hover');
            }).on('mouseleave', function() {
                $(this).removeClass('table-row-hover');
            });

            // 添加工具提示
            this.addTooltips();
        },

        addTooltips: function() {
            // 為按鈕添加提示
            $('.btn-pink').each(function() {
                const text = $(this).text();
                $(this).attr('title', '顯示' + text + '的學生');
            });

            // 為表格標題添加提示
            $('th').each(function() {
                const text = $(this).text().replace(/\n/g, '');
                if (text.includes('護照')) {
                    $(this).attr('title', text + '完成狀況');
                }
            });
        },

        showMessage: function(message, type = 'info') {
            // 移除現有訊息
            $('.alert-message').remove();
            
            // 添加新訊息
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 'alert-info';
            
            const messageHtml = `
                <div class="alert ${alertClass} alert-message" style="margin: 10px 0;">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    ${message}
                </div>
            `;
            
            $('.form-inline').first().after(messageHtml);
            
            // 3秒後自動隱藏
            setTimeout(() => {
                $('.alert-message').fadeOut();
            }, 3000);
        },

        // 錯誤處理
        handleError: function(error, context = '') {
            console.error(`${context}發生錯誤:`, error);
            
            let errorMessage = '系統發生錯誤，請稍後再試';
            if (error.message) {
                errorMessage = error.message;
            }
            
            this.showMessage(errorMessage, 'error');
        }
    };

    // 初始化模組
    query2Module.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});
