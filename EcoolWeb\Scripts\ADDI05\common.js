// ADDI05 Common JavaScript - 有獎徵答共用功能
window.ADDI05Common = (function() {
    'use strict';
    
    // 共用變數
    const common = {
        // 表單元素快取
        _form: null,
        
        // 獲取表單元素
        getForm: function() {
            if (!this._form) {
                this._form = document.form1;
                if (!this._form) {
                    console.error('找不到表單元素 form1');
                    return null;
                }
            }
            return this._form;
        },
        
        // 重置表單快取
        resetFormCache: function() {
            this._form = null;
        },
        
        // 通用的onGo函數
        onGo: function(actionVal, options = {}) {
            try {
                const form = this.getForm();
                if (!form) {
                    this.showMessage('系統錯誤：找不到表單元素');
                    return false;
                }

                console.log('ADDI05 導航到:', actionVal);

                // 處理特殊模式（如Answer頁面的again模式）
                if (options.mode) {
                    if (form.mode) {
                        form.mode.value = options.mode;
                    }
                }

                // 清除排序（如果有的話）
                if (options.clearSort && form.OrderByName) {
                    form.OrderByName.value = '';
                }

                // 根據動作設置表單action
                const urls = this.getUrls();
                if (actionVal === "Index" && urls.index) {
                    form.action = urls.index;
                } else if (actionVal === "detail" && urls.detail) {
                    form.action = urls.detail;
                } else if (actionVal === "Answer" && urls.answer) {
                    // 如果是Answer頁面，可能需要驗證
                    if (options.validate && typeof options.validate === 'function') {
                        if (!options.validate()) {
                            return false; // 驗證失敗，不提交
                        }
                    }
                    form.action = urls.answer;
                } else if (actionVal === "AnswerList" && urls.answerList) {
                    // 設置分頁為第一頁
                    if (form.page) {
                        form.page.value = '1';
                    }
                    form.action = urls.answerList;
                } else if (actionVal === "QusList" && urls.qusList) {
                    form.action = urls.qusList;
                } else if (actionVal === "PassList" && urls.passList) {
                    form.action = urls.passList;
                } else if (actionVal === "TotalGraph" && urls.totalGraph) {
                    form.action = urls.totalGraph;
                } else {
                    console.warn('未知的導航動作或缺少URL配置:', actionVal);
                    this.showMessage('導航配置錯誤，請聯繫系統管理員');
                    return false;
                }

                form.submit();
                return true;
            } catch (error) {
                console.error('ADDI05 導航時發生錯誤:', error);
                this.showMessage('導航時發生錯誤，請稍後再試');
                return false;
            }
        },
        
        // 獲取URL配置
        getUrls: function() {
            // 優先順序：Answer > Index > AnswerList > Detail > EachAnswerList > PassList
            return window.ADDI05_ANSWER_URLS ||
                   window.ADDI05_INDEX_URLS ||
                   window.ADDI05_ANSWER_LIST_URLS ||
                   window.ADDI05_DETAIL_URLS ||
                   window.ADDI05_EACH_ANSWER_LIST_URLS ||
                   window.ADDI05_PASS_LIST_URLS ||
                   {};
        },
        
        // 通用的表單清除功能
        clearForm: function(targetSelector = '#form1', searchAreaSelector = null) {
            try {
                const searchSelector = searchAreaSelector || `${targetSelector} :input,:selected`;
                
                $(searchSelector).each(function() {
                    const $element = $(this);
                    const type = $element.attr('type');
                    const isReadonly = $element.attr('readonly');
                    const tag = this.tagName.toLowerCase();

                    // 只處理非唯讀元素
                    if (isReadonly !== 'readonly' && isReadonly !== true) {
                        if (type === 'radio' || type === 'checkbox') {
                            if ($element.attr("title") === 'Default') {
                                this.checked = true;
                            } else {
                                this.checked = false;
                            }
                        } else if (tag === 'select') {
                            // 下拉式選單重設為第一個選項
                            this.selectedIndex = 0;
                        } else if (type === 'text' || type === 'hidden' || type === 'password' || type === 'textarea') {
                            this.value = '';
                        }
                    }
                });

                console.log('ADDI05 表單已清除');
                return true;
            } catch (error) {
                console.error('ADDI05 清除表單時發生錯誤:', error);
                this.showMessage('清除表單時發生錯誤，請稍後再試');
                return false;
            }
        },
        
        // 通用的分頁處理
        pageProc: function(pageno, pageFieldName = 'Page') {
            try {
                const form = this.getForm();
                if (!form) {
                    this.showMessage('系統錯誤：找不到表單元素');
                    return false;
                }
                
                console.log('ADDI05 切換到第', pageno, '頁');
                
                if (form[pageFieldName]) {
                    form[pageFieldName].value = pageno;
                } else {
                    console.warn(`找不到分頁欄位: ${pageFieldName}`);
                }
                
                form.submit();
                return true;
            } catch (error) {
                console.error('ADDI05 分頁處理時發生錯誤:', error);
                this.showMessage('分頁處理時發生錯誤，請稍後再試');
                return false;
            }
        },
        
        // 通用的排序處理
        sortProc: function(sortCol, orderByFieldName = 'OrderByName') {
            try {
                console.log('ADDI05 排序欄位:', sortCol);
                
                if ($(`#${orderByFieldName}`).length > 0) {
                    $(`#${orderByFieldName}`).val(sortCol);
                } else {
                    console.warn(`找不到排序欄位: ${orderByFieldName}`);
                }
                
                this.pageProc(1);
                return true;
            } catch (error) {
                console.error('ADDI05 排序處理時發生錯誤:', error);
                this.showMessage('排序處理時發生錯誤，請稍後再試');
                return false;
            }
        },
        
        // 通用的AJAX處理
        ajaxProc: function(options = {}) {
            try {
                const form = this.getForm();
                if (!form) {
                    this.showMessage('系統錯誤：找不到表單元素');
                    return false;
                }

                const defaultOptions = {
                    url: this.getUrls().pageContent,
                    type: 'POST',
                    cache: false,
                    dataType: 'html',
                    timeout: 10000,
                    data: {},
                    targetSelector: '#PageContent'
                };

                const ajaxOptions = Object.assign(defaultOptions, options);

                // 如果沒有提供data，從表單收集數據
                if (Object.keys(ajaxOptions.data).length === 0) {
                    ajaxOptions.data = {
                        SearchContents: form.SearchContents ? form.SearchContents.value : '',
                        OrderByName: form.OrderByName ? form.OrderByName.value : '',
                        SyntaxName: form.SyntaxName ? form.SyntaxName.value : '',
                        page: form.page ? form.page.value : (form.Page ? form.Page.value : '1'),
                        DialogType: form.DIALOG_TYPE ? form.DIALOG_TYPE.value : '',
                        whereShowData: form.whereShowData ? form.whereShowData.value : ''
                    };
                }

                $.ajax(ajaxOptions)
                .done(function(data) {
                    $(ajaxOptions.targetSelector).html(data);
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('ADDI05 AJAX請求失敗:', { textStatus, errorThrown, status: jqXHR.status });
                    
                    let errorMessage = 'AJAX請求失敗';
                    if (jqXHR.status === 404) {
                        errorMessage = '請求的頁面未找到';
                    } else if (jqXHR.status === 500) {
                        errorMessage = '服務器內部錯誤，請聯繫系統管理員';
                    } else if (textStatus === 'timeout') {
                        errorMessage = '請求超時，請稍後再試';
                    }
                    
                    this.showMessage(errorMessage);
                }.bind(this));

                return true;
            } catch (error) {
                console.error('ADDI05 AJAX處理時發生錯誤:', error);
                this.showMessage('AJAX處理時發生錯誤，請稍後再試');
                return false;
            }
        },
        
        // 通用的訊息顯示
        showMessage: function(message, type = 'error') {
            // 移除現有的訊息
            $('.addi05-message').remove();
            
            // 確定訊息顏色
            const messageColor = type === 'success' ? 'green' : 
                                type === 'warning' ? 'orange' : 'red';
            
            // 在頁面頂部顯示訊息
            const messageHtml = `<div class="addi05-message" style="color: ${messageColor}; background-color: #f9f9f9; padding: 10px; margin: 10px 0; border: 1px solid ${messageColor}; border-radius: 4px; position: relative; z-index: 1000;">${message}</div>`;
            
            // 尋找合適的插入位置
            let $insertTarget = $('form[name="form1"]');
            if ($insertTarget.length === 0) {
                $insertTarget = $('body');
            }
            
            $insertTarget.prepend(messageHtml);
            
            // 滾動到訊息位置
            $('html, body').animate({
                scrollTop: $('.addi05-message').offset().top - 50
            }, 300);
            
            // 5秒後自動移除
            setTimeout(() => {
                $('.addi05-message').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },
        
        // 通用的事件綁定輔助函數
        bindClickEvent: function(selector, handler) {
            $(document).off('click', selector).on('click', selector, function(event) {
                event.preventDefault();
                handler.call(this, event);
            });
        },
        
        // 解析onclick屬性中的函數調用
        parseOnClick: function(onclickStr, functionName) {
            if (!onclickStr) return null;
            
            const regex = new RegExp(`${functionName}\\(([^)]*)\\)`, 'g');
            const match = regex.exec(onclickStr);
            
            if (match && match[1]) {
                // 處理參數，移除引號並分割
                const params = match[1].split(',').map(param => 
                    param.trim().replace(/^['"]|['"]$/g, '')
                );
                return params;
            }
            
            return null;
        },
        
        // 通用的圖片處理功能
        handleImages: function(containerSelector = '.p-context') {
            try {
                $(`${containerSelector} img`).each(function() {
                    const $img = $(this);
                    let thisSrc = $img.attr("src");

                    if (!thisSrc) return;

                    // 處理圖片URL
                    if (thisSrc.indexOf("http") === 0) {
                        // 已經是完整URL
                        $img.attr("href", thisSrc);
                    } else if (thisSrc.indexOf("data:image") === 0) {
                        // Base64圖片
                        $img.attr("href", thisSrc);
                    } else {
                        // 相對路徑，需要加上系統URL
                        const urls = this.getUrls();
                        if (urls.sysUrl) {
                            const fullUrl = urls.sysUrl + thisSrc;
                            $img.attr("href", fullUrl);
                        } else {
                            console.warn('找不到系統URL配置，使用相對路徑');
                            $img.attr("href", thisSrc);
                        }
                    }
                });

                // 初始化colorbox
                if (typeof $.colorbox === 'function') {
                    $(`${containerSelector} img`).colorbox({
                        photo: true,
                        maxWidth: '90%',
                        maxHeight: '90%',
                        title: function() {
                            // 嘗試從alt或title屬性獲取圖片說明
                            return $(this).attr('alt') || $(this).attr('title') || '圖片';
                        }
                    });
                } else {
                    console.warn('Colorbox 插件未載入，圖片燈箱功能不可用');
                }

                console.log('ADDI05 圖片處理完成');
                return true;
            } catch (error) {
                console.error('ADDI05 處理圖片時發生錯誤:', error);
                return false;
            }
        },

        // 初始化共用功能
        init: function() {
            // 設置全局錯誤處理
            window.addEventListener('error', function(e) {
                console.error('ADDI05 頁面錯誤:', e);
            });

            console.log('ADDI05 Common 已初始化');
        }
    };
    
    // 返回公開的API
    return common;
})();

// 自動初始化
$(document).ready(function() {
    ADDI05Common.init();
});
