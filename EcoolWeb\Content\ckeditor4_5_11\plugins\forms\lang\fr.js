﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'fr', {
	button: {
		title: 'Propriétés du bouton',
		text: 'Texte',
		type: 'Type',
		typeBtn: 'Bouton',
		typeSbm: 'Validation',
		typeRst: 'Remise à zéro'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Propriétés de la case à cocher',
		radioTitle: 'Propriétés du bouton radio',
		value: 'Valeur',
		selected: 'Sélectionné',
		required: 'Requis'
	},
	form: {
		title: 'Propriétés du formulaire',
		menu: 'Propriétés du formulaire',
		action: 'Action',
		method: 'Méthode',
		encoding: 'Encodage'
	},
	hidden: {
		title: 'Propriétés du champ invisible',
		name: 'Nom',
		value: 'Valeur'
	},
	select: {
		title: 'Propriétés du menu déroulant',
		selectInfo: 'Informations sur le menu déroulant',
		opAvail: 'Options disponibles',
		value: 'Valeur',
		size: 'Taille',
		lines: 'lignes',
		chkMulti: 'Permettre les sélections multiples',
		required: 'Requis',
		opText: 'Texte',
		opValue: 'Valeur',
		btnAdd: 'Ajouter',
		btnModify: 'Modifier',
		btnUp: 'Haut',
		btnDown: 'Bas',
		btnSetValue: 'Définir comme valeur sélectionnée',
		btnDelete: 'Supprimer'
	},
	textarea: {
		title: 'Propriétés de la zone de texte',
		cols: 'Colonnes',
		rows: 'Lignes'
	},
	textfield: {
		title: 'Propriétés du champ texte',
		name: 'Nom',
		value: 'Valeur',
		charWidth: 'Largeur des caractères',
		maxChars: 'Nombre maximum de caractères',
		required: 'Requis',
		type: 'Type',
		typeText: 'Texte',
		typePass: 'Mot de passe',
		typeEmail: 'Courriel',
		typeSearch: 'Rechercher',
		typeTel: 'Numéro de téléphone',
		typeUrl: 'URL'
	}
} );
