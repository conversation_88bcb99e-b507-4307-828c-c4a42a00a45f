﻿
using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class SECI05TypeListIndexViewModel
    {
        public string WhereSCHOOL_NO { get; set; }

        public string WhereUSER_NO { get; set; }

        public string WhereSEYEAR { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        public IPagedList<SECI05BTypeListViewModel> ListData;

        public SECI05TypeListIndexViewModel()
        {
            PageSize = int.MaxValue;
        }
    }
}