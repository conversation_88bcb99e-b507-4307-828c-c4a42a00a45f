// Modern jQuery code for ADDI01 Verify page
$(document).ready(function() {
    // CKEditor 配置模組
    const ckeditorHandler = {
        init: function() {
            this.setupCKEditor();
            window.KeyIn = this.updateTextCount.bind(this);
        },

        setupCKEditor: function() {
            $(window).on('load', () => {
                CKEDITOR.config.nonce = 'cmlvaw';
                const editor = CKEDITOR.instances.ARTICLE;
                
                if (editor) {
                    editor.on('change', () => {
                        this.updateTextCount();
                    });
                }
                
                this.updateTextCount();
            });
        },

        updateTextCount: function() {
            const editor = CKEDITOR.instances.ARTICLE;
            if (editor) {
                const content = editor.getData();
                const textWithoutWhitespace = content.replace(/[\n\s]/g, "").replace(/<br\/>/g, "");
                const length = textWithoutWhitespace.length;
                $("#ShowFontLen").text(length);
            }
        }
    };

    // 下拉選單處理模組
    const dropdownHandler = {
        init: function() {
            window.VCommentDropDownList = this.handleVerifyComment.bind(this);
            window.BackDropDownList = this.handleBackMemo.bind(this);
            this.initializeDropdowns();
        },

        initializeDropdowns: function() {
            $(window).on('load', () => {
                // 初始化退回原因下拉選單
                const selectedBackValue = $('#BACK_MEMO_DropDownList option:selected').val();
                this.handleBackMemo(selectedBackValue);

                // 初始化評語下拉選單
                const selectedCommentValue = $('#VComment_DropDownList option:selected').val();
                this.handleVerifyComment(selectedCommentValue);
            });
        },

        handleVerifyComment: function(value) {
            if (value === window.ADDI01_CONFIG.otherValue) {
                $('#VERIFY_COMMENT').val('')
                                   .attr("type", "text")
                                   .attr("placeholder", "請輸入評語")
                                   .addClass("form-control");
            } else {
                $('#VERIFY_COMMENT').attr("type", "hidden")
                                   .removeClass()
                                   .val(value);
            }
        },

        handleBackMemo: function(value) {
            if (value === window.ADDI01_CONFIG.otherValue) {
                $('#BACK_MEMO').val("")
                              .attr("type", "text")
                              .attr("placeholder", "請輸入原因")
                              .addClass("form-control");
            } else {
                $('#BACK_MEMO').attr("type", "hidden")
                              .removeClass()
                              .val(value);
            }
        }
    };

    // 國語日報投稿模組
    const mdnKidsHandler = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $(window).on('load', () => {
                $("#PUBLISH_MDNKIDS_YN").on('click', this.handleMdnKidsToggle.bind(this));
                $("#SEND_EMAIL_YN").on('click', this.handleEmailToggle.bind(this));
            });
        },

        handleMdnKidsToggle: function() {
            const $checkbox = $("#PUBLISH_MDNKIDS_YN");
            
            $("#mdnInfoDiv").toggle();
            $("#MdnKids_Address").val("");
            $("#MdnKids_ResidenceAddress").val("");
            $("#MdnKids_Phone").val("");
            $("#MdnKids_Email").val("");
            
            if ($checkbox.prop("checked") === true) {
                $("#MdnKids_IntroPerson").val(window.ADDI01_CONFIG.userName);
                
                $("#notice").dialog({
                    buttons: {
                        "Ok": function() {
                            $(this).dialog("close");
                        }
                    }
                });
                
                $(".ui-dialog-titlebar-close").attr("hidden", "hidden");
            }
        },

        handleEmailToggle: function() {
            const $checkbox = $("#SEND_EMAIL_YN");
            
            if ($checkbox.prop("checked") === true) {
                $("#sendMailShow").attr("style", "");
            } else {
                $("#sendMailShow").attr("style", "display:none;");
            }
        }
    };

    // 表單提交和驗證模組
    const formHandler = {
        init: function() {
            window.DisableGO = this.disableArticle.bind(this);
            window.DoGO = this.navigateToAction.bind(this);
            window.btnR = this.handleModalResponse.bind(this);
        },

        disableArticle: function(buttonElement, actionUrl) {
            let errorMsg = '';
            buttonElement.disabled = true;

            if ($('#BACK_MEMO').val() === '') {
                errorMsg += '請輸入退回/作廢原因';
            }

            if (errorMsg !== '') {
                buttonElement.disabled = false;
                alert(errorMsg);
                return false;
            } else {
                const confirmed = confirm("你確定要「退回/作廢」這篇文章?");
                if (confirmed) {
                    const form = document.form1;
                    form.action = actionUrl;
                    form.submit();
                } else {
                    buttonElement.disabled = false;
                    return false;
                }
            }
        },

        navigateToAction: function(actionUrl) {
            const form = document.form1;
            form.BACK_MEMO.value = '';
            form.VERIFY_COMMENT.value = '';
            form.action = actionUrl;
            form.submit();
        },

        handleModalResponse: function(value) {
            if (value === "Y") {
                const form = document.form1;
                form.action = window.ADDI01_CONFIG.actionUrl;
                form.submit();
            } else {
                // 假設有一個全局的 BtnThis 變數
                if (typeof window.BtnThis !== 'undefined') {
                    window.BtnThis.disabled = false;
                }
                return false;
            }
        }
    };

    // 文件上傳模組
    const fileUploadHandler = {
        init: function() {
            this.bindFileUploadEvents();
        },

        bindFileUploadEvents: function() {
            // 增加檔案按鈕事件
            $('#addfile').on('click', this.addFileInput.bind(this));
        },

        addFileInput: function(e) {
            e.preventDefault();
            const $uploadFiles = $('.uploadfiles');
            const $newInput = $('<input class="btn btn-default" type="file" name="files" />');
            $uploadFiles.append($newInput);
        }
    };

    // YouTube 檢查模組
    const youtubeChecker = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $('#CheckYoutube').on('click', this.checkYoutubeUrl.bind(this));
        },

        checkYoutubeUrl: function() {
            const youtubeUrl = $('#YOUTUBE_URL').val();
            
            $.ajax({
                url: window.ADDI01_URLS.getUrlArgument,
                type: 'post',
                data: { StrUrl: youtubeUrl },
                dataType: 'json',
                cache: false,
                success: this.handleYoutubeResponse.bind(this),
                error: this.handleYoutubeError.bind(this)
            });
        },

        handleYoutubeResponse: function(data) {
            try {
                const res = jQuery.parseJSON(data);
                
                if (res.Success == 0) {
                    $('#YOUTUBE_URL').val('');
                    alert(res.Error);
                } else if (res.Success == 1) {
                    alert('正確');
                    $('#YOUTUBE_URL').val(res.EmbedYouTubeUrl);
                }
            } catch (e) {
                console.error('YouTube response parsing error:', e);
                alert('處理YouTube回應時發生錯誤');
            }
        },

        handleYoutubeError: function(xhr, err) {
            console.error('YouTube check error:', xhr, err);
            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
            alert("responseText: " + xhr.responseText);
        }
    };

    // 初始化所有模組
    ckeditorHandler.init();
    dropdownHandler.init();
    mdnKidsHandler.init();
    formHandler.init();
    fileUploadHandler.init();
    youtubeChecker.init();

    // 設置全局配置（需要在 CSHTML 中定義）
    window.ADDI01_CONFIG = window.ADDI01_CONFIG || {};
    window.ADDI01_URLS = window.ADDI01_URLS || {};
});
