﻿using DotNet.Highcharts;
using ECOOL_APP.com.ecool.Models.DTO;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class SECI06IndexViewModel
    {
        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        /// <summary>
        /// 只顯示某學校
        /// </summary>
        public string WhereSCHOOL_NO { get; set; }

        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某人
        /// </summary>
        public string WhereUSER_NO { get; set; }

        public HRMT01 MyHRMT01 { get; set; }

        public double? MyAge { get; set; }

        /// <summary>
        /// 角色娃娃 圖 路徑
        /// </summary>
        public string PlayerUrl { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<SECI03Hrmt08ListViewModel> Hrmt08List;

        public SECI03Hrmt08ListViewModel NowHrmt08;

        /// <summary>
        /// BMI 建議
        /// </summary>
        public string COMMENT { get; set; }

        /// <summary>
        /// BMI 建議 詳情
        /// </summary>
        public string COMMENT_ACTION { get; set; }

        /// <summary>
        /// BMI 類別
        /// </summary>
        public string BMI_TYPE_NAME { get; set; }

        /// <summary>
        /// 針對過重同學  建議身高多多少，就達到標準
        /// </summary>
        public string SUGGEST_BMI_MEMO { get; set; }

        /// <summary>
        /// 身高趨示圖
        /// </summary>
        public Highcharts TALLchart;

        /// <summary>
        /// 體重趨示圖
        /// </summary>
        public Highcharts WEIGHTchart;

        public string SYEARSTR { get; set; }
        public string TeacherSTR { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<SECI03Hrmt09ListViewModel> Hrmt09List;

        public SECI03Hrmt09ListViewModel NowHrmt09;

        public SECI06Addt24ViewModel NowAddt24;
    }
}