﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.EF;
using System.Data;
using System.Data.SqlClient;
using ECOOL_APP.com.ecool.util;
using System.Data.Entity.Infrastructure;
using EntityFramework.Utilities;
using log4net;
using ECOOL_APP.com.ecool.Models.DTO.AWAT14;

namespace ECOOL_APP
{
    public class CashHelper
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        ///  學生 酷幣增/減函數
        /// </summary>
        /// <param name="user"></param>
        /// <param name="CashIn"></param>
        /// <param name="SchoolNO"></param>
        /// <param name="UserNO"></param>
        /// <param name="SourceType"></param>
        /// <param name="SourceNo"></param>
        /// <param name="ChangeCashAll"></param>
        /// <param name="db"></param>
        public static void AddCash(UserProfile user, int CashIn, string SchoolNO, string UserNO, string SourceType, string SourceNo, bool ChangeCashAll, ref ECOOL_DEVEntities db, ref List<Tuple<string, string, int>> valuesList)
        {
            string LogDesc = string.Empty;
            ZZT01 z1 = db.ZZT01.Find(SourceType);
            if (z1 != null) LogDesc = z1.BRE_NAME;
            //按讚１５次加兩點
            if (SourceType == "ADDT23")
            {

                LogDesc = "線上藝廊的作品，大家都說很讚";
            }

            AddCash(user, CashIn, SchoolNO, UserNO, SourceType, SourceNo, LogDesc, ChangeCashAll, ref db,"", "ADDT23",ref valuesList);
          
            
           
           
        }

        public static void AddCashT20( int CashIn, string SchoolNO, string UserNO, string SourceType, string SourceNo, bool ChangeCashAll, ref ECOOL_DEVEntities db,ref List<Tuple<string, string, int>> valuesList)
        {
            string LogDesc = string.Empty;
            ZZT01 z1 = db.ZZT01.Find(SourceType);
            if (z1 != null) LogDesc = z1.BRE_NAME;
            //按讚１５次加兩點
            if (SourceType == "ADDT23")
            {

                LogDesc = "線上藝廊的作品，大家都說很讚";
            }
            AddCashZZI20( CashIn, SchoolNO, UserNO, SourceType, SourceNo, LogDesc, ChangeCashAll, ref db, "", "ADDT23", ref valuesList);
        }
        public static void AddCashZZI20( int CashIn, string SchoolNO, string UserNO, string SourceType, string SourceNo, string LogDesc, bool ChangeCashAll, ref ECOOL_DEVEntities db, string OutSysLOG_PERSON, string LOG_TABLE, ref List<Tuple<string, string, int>> valuesList)
        {
            //try
            //{
            short? ADD_CASH_ALL = 0;
            short? ADD_CASH_AVAILABLE = 0;
            short? ADD_CASH_WORKHARD = 0;
            string OutSysLOG_PERSONStr = "";
            OutSysLOG_PERSONStr = OutSysLOG_PERSON;
            SetSharedCASH(out ADD_CASH_ALL, out ADD_CASH_AVAILABLE, out ADD_CASH_WORKHARD, out OutSysLOG_PERSON, (short?)CashIn, ChangeCashAll, SourceType);
            if (SourceType == "ADDT26_D" && !string.IsNullOrWhiteSpace(OutSysLOG_PERSONStr))
            {
                OutSysLOG_PERSON = OutSysLOG_PERSONStr;

            }
            //AWAT01
            var find1 = db.AWAT01.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO);
            AWAT01 aw1 = find1.FirstOrDefault();

            var EntityAWAT01 = ((IObjectContextAdapter)db).ObjectContext.ObjectStateManager.GetObjectStateEntries(System.Data.Entity.EntityState.Added).Where(a => a.Entity is AWAT01).Select(a => (AWAT01)a.Entity).Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO).FirstOrDefault();
            if (EntityAWAT01 != null)
            {
                aw1 = EntityAWAT01;
            }

            if (aw1 == null)
            {
                aw1 = db.AWAT01.Create();
                aw1.SCHOOL_NO = SchoolNO;
                aw1.USER_NO = UserNO;
                aw1.CASH_ALL = ADD_CASH_ALL;
                aw1.CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                aw1.CASH_WORKHARD = ADD_CASH_WORKHARD;
                db.AWAT01.Add(aw1);
                
                ADDCashAWAT14(SchoolNO, UserNO, 0,(int)ADD_CASH_ALL, ref db,ref valuesList);
            }
            else
            {
                if (aw1.CASH_ALL.HasValue == false) aw1.CASH_ALL = 0;
                if (aw1.CASH_AVAILABLE.HasValue == false) aw1.CASH_AVAILABLE = 0;

                aw1.CASH_ALL = aw1.CASH_ALL.Value + ADD_CASH_ALL;
                aw1.CASH_AVAILABLE = aw1.CASH_AVAILABLE.Value + ADD_CASH_AVAILABLE;
                aw1.CASH_WORKHARD = aw1.CASH_WORKHARD.Value + ADD_CASH_WORKHARD;
                int CASH_ALL = getCASHALL(SchoolNO, UserNO, ref db);
                ADDCashAWAT14(SchoolNO, UserNO, (int)(CASH_ALL), (int)ADD_CASH_ALL, ref db,ref valuesList);
            }
            AWAT01_LOG CheckaWAT01_LOG = new AWAT01_LOG();
            CheckaWAT01_LOG = db.AWAT01_LOG.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO).OrderByDescending(x => x.LOG_TIME).FirstOrDefault();
            //AWAT01_LOG
            DateTime LogTimeDateTime = new DateTime();
            if (CheckaWAT01_LOG != null && CheckaWAT01_LOG.LOG_TIME == DateTime.Now)
            {


                logger.Error($" SaveLotteryPrizeCashInfo 時間 相同LogTimeDateTime" + LogTimeDateTime);
            }
            else
            {
                LogTimeDateTime = DateTime.Now;
                logger.Error($" SaveLotteryPrizeCashInfo 時間 LogTimeDateTime" + LogTimeDateTime);
            }
            LogTimeDateTime = LogTimeDateTime.AddMinutes(5);
            AWAT01_LOG aw1_log = db.AWAT01_LOG.Create();
            aw1_log.SCHOOL_NO = aw1.SCHOOL_NO;
            aw1_log.USER_NO = aw1.USER_NO;
            aw1_log.LOG_TIME = LogTimeDateTime;
            aw1_log.SOURCE_TYPE = SourceType;

            aw1_log.SOURCE_NO = SourceNo;
            aw1_log.CASH_IN = (short)CashIn;
            aw1_log.LOG_DESC = LogDesc;
            aw1_log.ADD_CASH_ALL = ADD_CASH_ALL;
            aw1_log.ADD_CASH_AVAILABLE = ADD_CASH_AVAILABLE;
            aw1_log.ADD_CASH_WORKHARD = ADD_CASH_WORKHARD;
            aw1_log.AWAT01_CASH_ALL = aw1.CASH_ALL;
            aw1_log.AWAT01_CASH_AVAILABLE = aw1.CASH_AVAILABLE;
            aw1_log.AWAT01_CASH_WORKHARD = aw1.CASH_WORKHARD;
            aw1_log.LOG_PERSON = string.IsNullOrWhiteSpace(OutSysLOG_PERSON) == false ? OutSysLOG_PERSON : "系統給扣點";
            aw1_log.LOG_TABLE = LOG_TABLE;
            db.AWAT01_LOG.Add(aw1_log);
            //  db.SaveChanges();
            //}
            //catch (Exception e) {
            //    logger.Error(e.Message + $" SaveLotteryPrizeCashInfo ADDCASH 結束錯誤GAME_NO");
            //}
            //EFBatchOperation.For(db, db.AWAT01).InsertAll(listAddW01);
            // PushService.addCashPush(aw1_log.LOG_PERSON, CashIn, aw1.SCHOOL_NO, aw1.USER_NO, SourceType, SourceNo, LogDesc, ref db);
        }
   

        public static void ADDCashAWAT14(string SchoolNO, string UserNO, int CashIn,int ADD_CASH_ALL, ref ECOOL_DEVEntities db2, ref List<Tuple<string, string, int>> valuesList) {
            AWAT01 aWAT01 = new AWAT01();
            AWAT14 AT14 = new AWAT14();
            AWAT14_LOG aWAT14_LOG = new AWAT14_LOG();
            AWAT15 wAT15 = new AWAT15();
            int? AWAT14_CASH = 0;
            int? RankNO = 0;
            // aWAT01 = db2.AWAT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == UserNO).FirstOrDefault();
            int CashALLSum = 0;
            CashALLSum = CashIn + ADD_CASH_ALL;
            try
            {
                AT14 = db2.AWAT14.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == UserNO).FirstOrDefault();
                AWAT14_CASH = db2.BDMT01.Where(x => x.SCHOOL_NO == SchoolNO).Select(x => x.AWAT14_CASH).FirstOrDefault();

                if (AT14 == null)
                {
                    AT14 = new AWAT14();
                    RankNO = (CashALLSum / AWAT14_CASH);
                    AT14.SCHOOL_NO = SchoolNO;
                    AT14.USER_NO = UserNO;
                    AT14.AWAT14_CASH = (int)AWAT14_CASH;
                    AT14.ARRIVED_CASH = (CashALLSum / AWAT14_CASH + 1) * AWAT14_CASH - CashALLSum;
                    AT14.CASH_Rank = RankNO;
                    AT14.CHG_DATE = DateTime.Now;
                    db2.AWAT14.Add(AT14);
                }
                else
                {
                    RankNO = (CashALLSum / AWAT14_CASH);
                    AT14.AWAT14_CASH = (int)AWAT14_CASH;
                    AT14.ARRIVED_CASH = (CashALLSum / AWAT14_CASH + 1) * AWAT14_CASH - CashALLSum;
                    AT14.CASH_Rank = RankNO;
                    AT14.CHG_DATE = DateTime.Now;

                }
                RankNO = (CashALLSum / AWAT14_CASH);
                aWAT14_LOG.ARRIVED_CASH = (CashALLSum / AWAT14_CASH + 1) * AWAT14_CASH - CashALLSum;
                aWAT14_LOG.AWAT14_CASH = (int)AWAT14_CASH;
                aWAT14_LOG.CASH_ALL = CashALLSum;
                aWAT14_LOG.CASH_Rank = RankNO;
                aWAT14_LOG.CHG_DATE = DateTime.Now;
                aWAT14_LOG.SCHOOL_NO = SchoolNO;
                aWAT14_LOG.USER_NO = UserNO;
                db2.AWAT14_LOG.Add(aWAT14_LOG);
                List<Tuple<string, string, int>> existingRecord = null;
                //  if (valuesList != null) {
                existingRecord = valuesList.Where(x => x.Item1 == SchoolNO && x.Item2 == UserNO && x.Item3 == RankNO).ToList();

                //  }

                if (existingRecord == null ||(existingRecord!=null&& existingRecord.Count()==0))
                {
                    //if (UserNO == "110057")
                    //{


                    //    wAT15 = db2.AWAT15.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == UserNO && x.CASH_Rank == RankNO).FirstOrDefault();
                    //}

                    wAT15 = db2.AWAT15.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == UserNO && x.CASH_Rank == RankNO).FirstOrDefault();

                    AWAT15 wAT15Item = new AWAT15();
                    AWAT01 aWAT01temp = new AWAT01();
                    aWAT01temp = db2.AWAT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == UserNO).FirstOrDefault();
                    int ATM = 0;
                    ATM = (db2.AWAT10.Where(w => w.STATUS == "1 " && w.SCHOOL_NO == SchoolNO && w.USER_NO == UserNO)
                                         .Sum(w => (int?)w.AMT) ?? 0);
                    IQueryable<VAWAT15> AWAT15List = (from y in db2.AWAT01

                                                      join aLog in
                    (from log in db2.AWAT01_LOG
                     where 1 == 1
                     group log by new { log.SCHOOL_NO, log.USER_NO } into logGroup
                     select new
                     {
                         logGroup.Key.SCHOOL_NO,
                         logGroup.Key.USER_NO,
                         CASH_ALL = logGroup.Sum(x => x.ADD_CASH_ALL),
                         CASH_AVAILABLE = logGroup.Sum(x => x.ADD_CASH_AVAILABLE),
                         CASH_WORKHARD = logGroup.Sum(x => x.ADD_CASH_WORKHARD)
                     })

                       on new { y.SCHOOL_NO, y.USER_NO } equals new { aLog.SCHOOL_NO, aLog.USER_NO }
                                                      where y.SCHOOL_NO == SchoolNO && y.USER_NO == UserNO
                                                      select new VAWAT15
                                                      {
                                                          CASH_ALL = (int)y.CASH_ALL,
                                                          SUMCASH_AVAILABLE = ((aLog.CASH_AVAILABLE < 0) ? (y.CASH_AVAILABLE ?? 0) : (aLog.CASH_AVAILABLE ?? 0))
                                                          + ATM


                                                      }
                        ).AsQueryable();
                    var mainQuery = from temp in AWAT15List
                                    select new VAWAT15
                                    {
                                        CASH_ALL = (int)(temp.SUMCASH_AVAILABLE > temp.CASH_ALL ? temp.SUMCASH_AVAILABLE : temp.CASH_ALL),

                                        SUMCASH_AVAILABLE = temp.SUMCASH_AVAILABLE,
                                    };
                    List<VAWAT15> AWAT15Listtemp = mainQuery.ToList();
                    int CASH_ALL = 0;
                    if (AWAT15Listtemp != null && AWAT15Listtemp.Count() != 0)
                    {

                        CASH_ALL = AWAT15Listtemp.FirstOrDefault().CASH_ALL;

                    }
                    RankNO = (CASH_ALL+ADD_CASH_ALL) / AWAT14_CASH;
                    if (wAT15 == null)
                    {
                        //if (!USER_NO.Contains(UserNO)) {


                        int SYear;
                        int Semesters;
                        
                        SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                        HRMT01 hRMT01 = new HRMT01();
                        hRMT01 = db2.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == UserNO && x.USER_STATUS==1 ).FirstOrDefault();


                        if (hRMT01 != null)
                        {

                                wAT15Item.USER_NO = UserNO;
                                wAT15Item.SCHOOL_NO = SchoolNO;
                                wAT15Item.CASH_Rank = (int)RankNO;
                                wAT15Item.CreatDate = DateTime.Now;
                                wAT15Item.SYEAR = Convert.ToByte(SYear);
                                wAT15Item.SEMESTER = Convert.ToByte(Semesters);
                                wAT15Item.CLASS_NO = hRMT01.CLASS_NO;
                                wAT15Item.SEAT_NO = hRMT01.SEAT_NO;
                                wAT15Item.NAME = hRMT01.NAME;
                                wAT15Item.SNAME = hRMT01.SNAME;
                                wAT15Item.IsReMark = 0;
                                wAT15Item.AWAT14_CASH = (int)AWAT14_CASH;
                                db2.AWAT15.Add(wAT15Item);


                          
                        }

                        // }
                    }

                }

                logger.Error("ADDAWAT14 SchoolNO" + SchoolNO+ "UserNO"+ UserNO+ "RankNO "+(int)RankNO);
                valuesList.Add(Tuple.Create(SchoolNO, UserNO, (int)RankNO));
            }
            catch (Exception e) {

                logger.Error("ADDAWAT14"+e.InnerException.Message);
            }
        }
        public static void ADDCashAWAT15(string SchoolNO, string UserNO, int CashIn, Dictionary<string,string> pairs ,ref ECOOL_DEVEntities db2) {

            //wAT15 = db2.AWAT15.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == UserNO && x.CASH_Rank == RankNO).FirstOrDefault();

            //AWAT15 wAT15Item = new AWAT15();

            //if (wAT15 == null) {
            //        //if (!USER_NO.Contains(UserNO)) {




            //    RankNO = (CashIn / AWAT14_CASH);
            //    wAT15Item.USER_NO = UserNO;
            //    wAT15Item.SCHOOL_NO = SchoolNO;
            //    wAT15Item.CASH_Rank =(int)RankNO;
            //    wAT15Item.CreatDate = DateTime.Now;
            //    db2.AWAT15.Add(wAT15Item);

            //       // }
            //    }

        }
        /// <summary>
        /// AddCash 學生 酷幣增/減函數
        /// </summary>
        /// <param name="CashIn">增/減酷幣數值，如果是扣酷幣點數，則傳入負值</param>
        /// <param name="SchoolNO">學校代號(六碼，如西湖為403605)</param>
        /// <param name="UserNO">帳號</param>
        /// <param name="SourceType">來源種類(如線上投稿，閱讀認證，獎品兌換)</param>
        /// <param name="SourceNo">來源資料鍵值</param>
        /// <param name="LogDesc">在Log紀錄上的補充說明</param>
        /// <param name="db"></param>
        public static void AddCash(UserProfile user, int CashIn, string SchoolNO, string UserNO, string SourceType, string SourceNo, string LogDesc, bool ChangeCashAll, ref ECOOL_DEVEntities db,string OutSysLOG_PERSON,string LOG_TABLE, ref List<Tuple<string, string, int>> valuesList)
        {
            //try
            //{
                short? ADD_CASH_ALL = 0;
                short? ADD_CASH_AVAILABLE = 0;
                short? ADD_CASH_WORKHARD = 0;
            string OutSysLOG_PERSONStr = "";
            OutSysLOG_PERSONStr = OutSysLOG_PERSON;
          
                SetSharedCASH(out ADD_CASH_ALL, out ADD_CASH_AVAILABLE, out ADD_CASH_WORKHARD, out OutSysLOG_PERSON, (short?)CashIn, ChangeCashAll, SourceType);
            if (SourceType == "ADDT26_D" && !string.IsNullOrWhiteSpace(OutSysLOG_PERSONStr))
            {
                OutSysLOG_PERSON = OutSysLOG_PERSONStr;

            }
            //AWAT01
            var find1 = db.AWAT01.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO);
        
            AWAT01 aw1 = find1.FirstOrDefault();

                var EntityAWAT01 = ((IObjectContextAdapter)db).ObjectContext.ObjectStateManager.GetObjectStateEntries(System.Data.Entity.EntityState.Added).Where(a => a.Entity is AWAT01).Select(a => (AWAT01)a.Entity).Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO).FirstOrDefault();
                if (EntityAWAT01 != null)
                {
                    aw1 = EntityAWAT01;
                }

                if (aw1 == null)
                {
                    aw1 = db.AWAT01.Create();
                    aw1.SCHOOL_NO = SchoolNO;
                    aw1.USER_NO = UserNO;
                    aw1.CASH_ALL = ADD_CASH_ALL;
                    aw1.CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                    aw1.CASH_WORKHARD = ADD_CASH_WORKHARD;
                    db.AWAT01.Add(aw1);
                    ADDCashAWAT14(SchoolNO, UserNO,0, (int)ADD_CASH_ALL, ref db,ref valuesList);
                }
                else
                {
                    if (aw1.CASH_ALL.HasValue == false) aw1.CASH_ALL = 0;
                    if (aw1.CASH_AVAILABLE.HasValue == false) aw1.CASH_AVAILABLE = 0;

                    aw1.CASH_ALL = aw1.CASH_ALL.Value + ADD_CASH_ALL;
                    aw1.CASH_AVAILABLE = aw1.CASH_AVAILABLE.Value + ADD_CASH_AVAILABLE;
                    aw1.CASH_WORKHARD = aw1.CASH_WORKHARD.Value + ADD_CASH_WORKHARD;
                int CASH_ALL = getCASHALL(SchoolNO, UserNO, ref db);
                ADDCashAWAT14(SchoolNO, UserNO, (int)(CASH_ALL), (int)ADD_CASH_ALL, ref db,ref valuesList);
                }
                AWAT01_LOG CheckaWAT01_LOG = new AWAT01_LOG();
                CheckaWAT01_LOG = db.AWAT01_LOG.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO).OrderByDescending(x => x.LOG_TIME).FirstOrDefault();
                //AWAT01_LOG
                DateTime LogTimeDateTime = new DateTime();
                if (CheckaWAT01_LOG!=null&&CheckaWAT01_LOG.LOG_TIME == DateTime.Now)
                {

                   
                    logger.Error( $" SaveLotteryPrizeCashInfo 時間 相同LogTimeDateTime"+ LogTimeDateTime);
                }
                else {
                    LogTimeDateTime = DateTime.Now;
                    logger.Error($" SaveLotteryPrizeCashInfo 時間 LogTimeDateTime" + LogTimeDateTime);
                }
                LogTimeDateTime = LogTimeDateTime.AddMinutes(5);
                AWAT01_LOG aw1_log = db.AWAT01_LOG.Create();
                aw1_log.SCHOOL_NO = aw1.SCHOOL_NO;
                aw1_log.USER_NO = aw1.USER_NO;
                aw1_log.LOG_TIME = LogTimeDateTime;
                aw1_log.SOURCE_TYPE = SourceType;
                
                aw1_log.SOURCE_NO =SourceNo;
                aw1_log.CASH_IN = (short)CashIn;
                aw1_log.LOG_DESC = LogDesc;
                aw1_log.ADD_CASH_ALL = ADD_CASH_ALL;
                aw1_log.ADD_CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                aw1_log.ADD_CASH_WORKHARD = ADD_CASH_WORKHARD;
                aw1_log.AWAT01_CASH_ALL = aw1.CASH_ALL;
                aw1_log.AWAT01_CASH_AVAILABLE = aw1.CASH_AVAILABLE;
                aw1_log.AWAT01_CASH_WORKHARD = aw1.CASH_WORKHARD;
                aw1_log.LOG_PERSON = string.IsNullOrWhiteSpace(OutSysLOG_PERSON) == false ? OutSysLOG_PERSON : (user?.USER_KEY ?? "系統給扣點");
            aw1_log.LOG_TABLE = LOG_TABLE;
                db.AWAT01_LOG.Add(aw1_log);
       
          //  db.SaveChanges();
            //}
            //catch (Exception e) {
            //    logger.Error(e.Message + $" SaveLotteryPrizeCashInfo ADDCASH 結束錯誤GAME_NO");
            //}
            //EFBatchOperation.For(db, db.AWAT01).InsertAll(listAddW01);
            // PushService.addCashPush(aw1_log.LOG_PERSON, CashIn, aw1.SCHOOL_NO, aw1.USER_NO, SourceType, SourceNo, LogDesc, ref db);
        }


        private static void SetSharedCASH(out short? ADD_CASH_ALL, out short? ADD_CASH_AVAILABLE, out short? ADD_CASH_WORKHARD, out string OutSysLOG_PERSON, short? CashIn, bool ChangeCashAll, string SourceType)
        {
            // 目前不是 獎品兌換,角色娃娃 可累加 CASH_ALL
            if (ChangeCashAll)
            {
                ADD_CASH_ALL = CashIn;
            }
            else
            {
                ADD_CASH_ALL = 0;
            };

            ADD_CASH_AVAILABLE = CashIn;

            //判斷是否增加到撲克牌抽獎點數 CASH_WORKHARD   (生日賀禮、撲克牌、獎品取消兌換、獎品,娃娃兌換 不給好運)
            //學生被扣點數，好運集氣次數與點數不可以被影響
            if (SourceType != "BIRTHDAY" && SourceType != "ADDI04" && SourceType != "AWAT02" && SourceType != "AWAI02" && SourceType != "AWAT10")
            {
                if (SourceType == "ADDT04" || SourceType == "ADDT09")
                {
                    OutSysLOG_PERSON = "系統給扣點";
                }
                
                else
                {


                    OutSysLOG_PERSON = string.Empty;
                }
                ADD_CASH_WORKHARD = CashIn;
            }
            //else if (SourceType== "ADDI13") {

            //}
            else
            {
                OutSysLOG_PERSON = "系統給扣點";
                ADD_CASH_WORKHARD = 0;
            }
        }
        public static void BatchAddCashTolottery(string schoolno, string user_no, string USER_TYPE,string USER_KEY, string SESSION_ID, DataTable dt, string SourceType, string SourceNo, string LogDesc, SqlBulkCopy sbCopy, SqlConnection Conn, SqlTransaction Trans,string Barcode)
        {
           
            if (LogDesc == null || LogDesc == string.Empty)
            {
                ZZT01 z1 = new ECOOL_DEVEntities().ZZT01.Find(SourceType);
                LogDesc = z1 != null ? z1.BRE_NAME : "";
            }

            List<AWAT01_BATCH_TEMP> BATCH_TEMPList = new List<AWAT01_BATCH_TEMP>();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            short? CASH_IN = 0;
            short? ADD_CASH_ALL = 0;
            short? ADD_CASH_AVAILABLE = 0;
            short? ADD_CASH_WORKHARD = 0;
            string OutSysLOG_PERSON;

            string SCHOOL_NO = string.Empty;
            string USER_NO = string.Empty;

            IDbCommand cmdTmp = new SqlCommand(@" DELETE AWAT01_BATCH_TEMP Where SESSION_ID=@SESSION_ID or Datediff(HOUR,@CRE_DATE,GETDATE())>=1", Conn, Trans);
            cmdTmp.Parameters.Add(new SqlParameter("@SESSION_ID", SESSION_ID));
            cmdTmp.Parameters.Add(new SqlParameter("@CRE_DATE", DateTime.Now));
            cmdTmp.ExecuteNonQuery();
            cmdTmp.Dispose();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            short CashTotal = 0;
            foreach (DataRow dr in dt.Rows)
            {
                CASH_IN = (dr["CASH"] == null) ? (short?)null : Convert.ToInt16(dr["CASH"]);
                string SCHOOLNOs = "";
                SCHOOLNOs = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                SetSharedCASH(out ADD_CASH_ALL, out ADD_CASH_AVAILABLE, out ADD_CASH_WORKHARD, out OutSysLOG_PERSON, CASH_IN, true, SourceType);
               
                ADDT38 aDDT38Item = new ADDT38();
                aDDT38Item = db.ADDT38.Where(x => x.BarCode == Barcode && x.SCHOOL_NO == SCHOOLNOs).FirstOrDefault();
                OutSysLOG_PERSON = aDDT38Item.CRE_PERSON;
                AWAT01_BATCH_TEMP BATCH_TEMP = new AWAT01_BATCH_TEMP();
                BATCH_TEMP.SESSION_ID = SESSION_ID;
                BATCH_TEMP.CRE_DATE = DateTime.Now;
                BATCH_TEMP.SCHOOL_NO = SCHOOLNOs;
                BATCH_TEMP.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                BATCH_TEMP.LOG_TIME = DateTime.Now;
                logger.Info("紙本酷幣點數 BATCH_TEMP");
                BATCH_TEMP.SOURCE_TYPE = SourceType;
                BATCH_TEMP.SOURCE_NO = (SourceNo ?? "");
                logger.Info("紙本酷幣點數 BATCH_TEMP");
                BATCH_TEMP.CASH_IN = CASH_IN;
                BATCH_TEMP.LOG_DESC = LogDesc;
                logger.Info("紙本酷幣點數 BATCH_TEMP");
                BATCH_TEMP.LOG_PERSON = string.IsNullOrWhiteSpace(OutSysLOG_PERSON) == false ? OutSysLOG_PERSON : USER_KEY;
                BATCH_TEMP.ADD_CASH_ALL = ADD_CASH_ALL;
                BATCH_TEMP.ADD_CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                BATCH_TEMP.ADD_CASH_WORKHARD = ADD_CASH_WORKHARD;
                BATCH_TEMPList.Add(BATCH_TEMP);
                logger.Info("紙本酷幣點數 BATCH_TEMP");
                logger.Info("紙本酷幣點數 BATCH_TEMP"+ UserProfile.CheckCashLimit_LOG_DESC(LogDesc));
                if (UserProfile.CheckCashLimit_LOG_DESC(LogDesc))
                {
                    if (CASH_IN > 0) CashTotal += CASH_IN.Value;
                }

                AWAT01 wAT01 = new AWAT01();
                wAT01 = db.AWAT01.Where(x => x.SCHOOL_NO == SCHOOLNOs && x.USER_NO == BATCH_TEMP.USER_NO).FirstOrDefault();
                int CASH_ALL = getCASHALL(SCHOOLNOs, BATCH_TEMP.USER_NO, ref db);
                ADDCashAWAT14(SCHOOLNOs, BATCH_TEMP.USER_NO, (int)(CASH_ALL), (int)ADD_CASH_ALL, ref db,ref valuesList);

            }

          //  db.AWAT01_BATCH_TEMP.AddRange(BATCH_TEMPList);
          //  db.SaveChanges();
            //檢查給點教師的限制
            //每月給點上限：綁的程式是
            //一、批次加扣點裡面的「特殊加扣點」、「班級小幫手」
            //二、即時加點的「特殊加點--固定」、「特殊加點--隨機」
            string ErrMsg;
            short ThisMonthCash;
         
            //short CashLimit = UserProfile.GetCashLimit(schoolno, user_no, USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
            //if (string.IsNullOrWhiteSpace(ErrMsg) == false) throw new Exception(ErrMsg);
            //if (CashLimit > 0 && CashLimit != short.MaxValue)
            //{
            //    if ((CashTotal + ThisMonthCash) > CashLimit)
            //    {
            //        throw new Exception("本次給點將超過本月給點限制：" + CashLimit.ToString() + "。您本月已發出點數：" + ThisMonthCash.ToString());
            //    }
            //}

            sbCopy.DestinationTableName = "AWAT01_BATCH_TEMP";
            sbCopy.WriteToServer(BATCH_TEMPList.AsDataTable());

            IDbCommand cmd = new SqlCommand();
            cmd.Parameters.Add(new SqlParameter("@SOURCE_TYPE", SourceType));
            cmd.Parameters.Add(new SqlParameter("@SOURCE_NO", (SourceNo ?? "")));
            cmd.Parameters.Add(new SqlParameter("@SESSION_ID", SESSION_ID));
            cmd.Connection = Conn;
            cmd.Transaction = Trans;
            logger.Info("紙本酷幣點數 BATCH_TEMP" + SourceType+" "+ SourceNo+"  "+ SESSION_ID);
            string sSQL = @" UPDATE AWAT01 SET ";
            sSQL = sSQL + "  CASH_ALL = CASH_ALL + S.ADD_CASH_ALL ";
            sSQL = sSQL + " ,CASH_AVAILABLE = CASH_AVAILABLE + S.ADD_CASH_AVAILABLE  ";
            sSQL = sSQL + " ,CASH_WORKHARD = CASH_WORKHARD +S.ADD_CASH_WORKHARD  ";
            sSQL = sSQL + " FROM AWAT01 U  ";
            sSQL = sSQL + " INNER JOIN AWAT01_BATCH_TEMP S ON U.SCHOOL_NO = S.SCHOOL_NO AND U.USER_NO = S.USER_NO ";
            sSQL = sSQL + " WHERE S.SOURCE_TYPE = @SOURCE_TYPE ";
            sSQL = sSQL + " and S.SOURCE_NO = @SOURCE_NO";
            sSQL = sSQL + " and S.SESSION_ID = @SESSION_ID";
            cmd.CommandText = sSQL;
            try
            {




                cmd.ExecuteNonQuery();


            }
            catch (Exception e)
            {

                logger.Info("紙本酷幣點數 BATCH_TEMP exception1" + e.InnerException);

            }

            sSQL = @" INSERT INTO AWAT01(SCHOOL_NO, USER_NO, CASH_ALL, CASH_AVAILABLE, CASH_WORKHARD)
            SELECT S.SCHOOL_NO,S.USER_NO
            ,S.ADD_CASH_ALL
            ,S.ADD_CASH_AVAILABLE
            ,S.ADD_CASH_WORKHARD
            FROM AWAT01_BATCH_TEMP S
            LEFT OUTER JOIN AWAT01 U ON U.SCHOOL_NO = S.SCHOOL_NO AND U.USER_NO = S.USER_NO
            WHERE S.SOURCE_TYPE = @SOURCE_TYPE
            and S.SOURCE_NO = @SOURCE_NO
            and S.SESSION_ID = @SESSION_ID
            and U.USER_NO IS NULL ";
            cmd.CommandText = sSQL;
            try
            {




                cmd.ExecuteNonQuery();


            }
            catch (Exception e)
            {

                logger.Info("紙本酷幣點數 BATCH_TEMP exception2" + e.InnerException);

            }

            sSQL = @"INSERT INTO AWAT01_LOG
            (SCHOOL_NO, USER_NO, SOURCE_TYPE, SOURCE_NO, CASH_IN, LOG_TIME, LOG_DESC,LOG_PERSON
            , ADD_CASH_ALL, ADD_CASH_AVAILABLE, ADD_CASH_WORKHARD
            , AWAT01_CASH_ALL
            , AWAT01_CASH_AVAILABLE
            , AWAT01_CASH_WORKHARD)
            SELECT S.SCHOOL_NO, S.USER_NO, S.SOURCE_TYPE, S.SOURCE_NO, S.CASH_IN, S.LOG_TIME, S.LOG_DESC,S.LOG_PERSON
            , S.ADD_CASH_ALL, S.ADD_CASH_AVAILABLE, S.ADD_CASH_WORKHARD
            , U.CASH_ALL
            ,  U.CASH_AVAILABLE
            ,  U.CASH_WORKHARD
            FROM AWAT01_BATCH_TEMP S
            INNER JOIN AWAT01 U ON U.SCHOOL_NO = S.SCHOOL_NO AND U.USER_NO = S.USER_NO
            WHERE S.SOURCE_TYPE = @SOURCE_TYPE
            and S.SOURCE_NO = @SOURCE_NO
            and S.SESSION_ID = @SESSION_ID ";
            cmd.CommandText = sSQL;
            try
            {




                cmd.ExecuteNonQuery();


            }
            catch (Exception e)
            {

                logger.Info("紙本酷幣點數 BATCH_TEMP exception3" + e.InnerException);

            }

            cmd.CommandText = "DELETE AWAT01_BATCH_TEMP Where SESSION_ID = @SESSION_ID";
            try
            {




                cmd.ExecuteNonQuery();

               
            }
            catch (Exception e) {

                logger.Info("紙本酷幣點數 BATCH_TEMP exception4"+e.InnerException);

            }
            cmd.Dispose();
          
        }

        public static void BatchAddCash(UserProfile user, string SESSION_ID, DataTable dt, string SourceType, string SourceNo, string LogDesc,string LOG_TABLE, SqlBulkCopy sbCopy, SqlConnection Conn, SqlTransaction Trans)
        {
            if (LogDesc == null || LogDesc == string.Empty)
            {
                ZZT01 z1 = new ECOOL_DEVEntities().ZZT01.Find(SourceType);
                LogDesc = z1 != null ? z1.BRE_NAME : "";
            }

            List<AWAT01_BATCH_TEMP> BATCH_TEMPList = new List<AWAT01_BATCH_TEMP>();

            short? CASH_IN = 0;
            short? ADD_CASH_ALL = 0;
            short? ADD_CASH_AVAILABLE = 0;
            short? ADD_CASH_WORKHARD = 0;
            string OutSysLOG_PERSON;

            string SCHOOL_NO = string.Empty;
            string USER_NO = string.Empty;

            IDbCommand cmdTmp = new SqlCommand(@" DELETE AWAT01_BATCH_TEMP Where SESSION_ID=@SESSION_ID or Datediff(HOUR,@CRE_DATE,GETDATE())>=1", Conn, Trans);
            cmdTmp.Parameters.Add(new SqlParameter("@SESSION_ID", SESSION_ID));
            cmdTmp.Parameters.Add(new SqlParameter("@CRE_DATE", DateTime.Now));

            cmdTmp.ExecuteNonQuery();
            cmdTmp.Dispose();

            short CashTotal = 0;

            //檢查給點教師的限制
            //每月給點上限：綁的程式是
            //一、批次加扣點裡面的「特殊加扣點」、「班級小幫手」
            //二、即時加點的「特殊加點--固定」、「特殊加點--隨機」
            string ErrMsg;
            short ThisMonthCash;
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            foreach (DataRow dr in dt.Rows)
            {
                CASH_IN = (dr["CASH"] == null) ? (short?)null : Convert.ToInt16(dr["CASH"]);

                SetSharedCASH(out ADD_CASH_ALL, out ADD_CASH_AVAILABLE, out ADD_CASH_WORKHARD, out OutSysLOG_PERSON, CASH_IN, true, SourceType);

                AWAT01_BATCH_TEMP BATCH_TEMP = new AWAT01_BATCH_TEMP();
                BATCH_TEMP.SESSION_ID = SESSION_ID;
                BATCH_TEMP.CRE_DATE = DateTime.Now;
                BATCH_TEMP.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                BATCH_TEMP.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                BATCH_TEMP.LOG_TIME = DateTime.Now;
                BATCH_TEMP.SOURCE_TYPE = SourceType;
                BATCH_TEMP.SOURCE_NO = (SourceNo ?? "");
                BATCH_TEMP.CASH_IN = CASH_IN;
                BATCH_TEMP.LOG_DESC = LogDesc;
                BATCH_TEMP.LOG_PERSON = string.IsNullOrWhiteSpace(OutSysLOG_PERSON) == false ? OutSysLOG_PERSON : user.USER_KEY;
                BATCH_TEMP.ADD_CASH_ALL = ADD_CASH_ALL;
                BATCH_TEMP.ADD_CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                BATCH_TEMP.ADD_CASH_WORKHARD = ADD_CASH_WORKHARD;
                BATCH_TEMPList.Add(BATCH_TEMP);

                if (UserProfile.CheckCashLimit_LOG_DESC(LogDesc))
                {
                    if (CASH_IN > 0) CashTotal += CASH_IN.Value;
                }
                AWAT01 wAT01 = new AWAT01();
                int AWATCASHALL = 0;
                wAT01 = db.AWAT01.Where(x => x.SCHOOL_NO == BATCH_TEMP.SCHOOL_NO && x.USER_NO == BATCH_TEMP.USER_NO).FirstOrDefault();
                if (wAT01 != null) {
                    AWATCASHALL = (int)wAT01.CASH_ALL;


                }
             int CASH_ALL=   getCASHALL(BATCH_TEMP.SCHOOL_NO, BATCH_TEMP.USER_NO, ref db);
                ADDCashAWAT14(BATCH_TEMP.SCHOOL_NO, BATCH_TEMP.USER_NO, (int)(CASH_ALL),(int) ADD_CASH_ALL,ref db,ref valuesList);


            }

           
        

            if (LogDesc.Contains("批次特殊加扣點") || LogDesc.Contains("即時加點特殊加扣點") || LogDesc.Contains("特殊加扣點")
               || LogDesc.Contains("批次校內表現班級小幫手") || LogDesc.Contains("批次校內表現班級幫手和榮譽")
               || LogDesc.Contains("校內表現-班級幫手和榮譽") || LogDesc.Contains("班級幫手和榮譽") || LogDesc.Contains("校內表現-班級服務")
                  || LogDesc.Contains("批次快速大量加點-特殊加扣點") || LogDesc.Contains("特殊加扣點-小獎勵(班級加點，受點數控管)") || LogDesc.Contains("小獎勵(班級加點，受點數控管")) {
            int CashLimit = UserProfile.GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
            if (string.IsNullOrWhiteSpace(ErrMsg) == false) throw new Exception(ErrMsg);
            if (CashLimit > 0 && CashLimit != short.MaxValue)
            {
                if ((CashTotal + ThisMonthCash) > CashLimit)
                {
                    throw new Exception("本次給點將超過本月給點限制：" + CashLimit.ToString() + "。您本月已發出點數：" + ThisMonthCash.ToString());
                }
                }
            }

            sbCopy.DestinationTableName = "AWAT01_BATCH_TEMP";
            sbCopy.WriteToServer(BATCH_TEMPList.AsDataTable());

            IDbCommand cmd = new SqlCommand();
            cmd.Parameters.Add(new SqlParameter("@SOURCE_TYPE", SourceType));
            cmd.Parameters.Add(new SqlParameter("@SOURCE_NO", (SourceNo ?? "")));
            cmd.Parameters.Add(new SqlParameter("@SESSION_ID", SESSION_ID));
            cmd.Parameters.Add(new SqlParameter("@LOG_TABLE", LOG_TABLE));
            cmd.Connection = Conn;
            cmd.Transaction = Trans;

            string sSQL = @" UPDATE AWAT01 SET ";
            sSQL = sSQL + "  CASH_ALL = CASH_ALL + S.ADD_CASH_ALL ";
            sSQL = sSQL + " ,CASH_AVAILABLE = CASH_AVAILABLE + S.ADD_CASH_AVAILABLE  ";
            sSQL = sSQL + " ,CASH_WORKHARD = CASH_WORKHARD +S.ADD_CASH_WORKHARD  ";
            sSQL = sSQL + " FROM AWAT01 U  ";
            sSQL = sSQL + " INNER JOIN AWAT01_BATCH_TEMP S ON U.SCHOOL_NO = S.SCHOOL_NO AND U.USER_NO = S.USER_NO ";
            sSQL = sSQL + " WHERE S.SOURCE_TYPE = @SOURCE_TYPE ";
            sSQL = sSQL + " and S.SOURCE_NO = @SOURCE_NO";
            sSQL = sSQL + " and S.SESSION_ID = @SESSION_ID";
            cmd.CommandText = sSQL;
            cmd.ExecuteNonQuery();

            sSQL = @" INSERT INTO AWAT01(SCHOOL_NO, USER_NO, CASH_ALL, CASH_AVAILABLE, CASH_WORKHARD)
            SELECT S.SCHOOL_NO,S.USER_NO
            ,S.ADD_CASH_ALL
            ,S.ADD_CASH_AVAILABLE
            ,S.ADD_CASH_WORKHARD
            FROM AWAT01_BATCH_TEMP S
            LEFT OUTER JOIN AWAT01 U ON U.SCHOOL_NO = S.SCHOOL_NO AND U.USER_NO = S.USER_NO
            WHERE S.SOURCE_TYPE = @SOURCE_TYPE
            and S.SOURCE_NO = @SOURCE_NO
            and S.SESSION_ID = @SESSION_ID
            and U.USER_NO IS NULL ";
            cmd.CommandText = sSQL;
            cmd.ExecuteNonQuery();

            sSQL = @"INSERT INTO AWAT01_LOG
            (SCHOOL_NO, USER_NO, SOURCE_TYPE, SOURCE_NO, CASH_IN, LOG_TIME, LOG_DESC,LOG_PERSON
            , ADD_CASH_ALL, ADD_CASH_AVAILABLE, ADD_CASH_WORKHARD , LOG_TABLE
            , AWAT01_CASH_ALL
            , AWAT01_CASH_AVAILABLE
            , AWAT01_CASH_WORKHARD)
            SELECT S.SCHOOL_NO, S.USER_NO, S.SOURCE_TYPE, S.SOURCE_NO, S.CASH_IN, S.LOG_TIME, S.LOG_DESC,S.LOG_PERSON
            , S.ADD_CASH_ALL, S.ADD_CASH_AVAILABLE, S.ADD_CASH_WORKHARD , @LOG_TABLE 
            , U.CASH_ALL
            ,  U.CASH_AVAILABLE
            ,  U.CASH_WORKHARD
            FROM AWAT01_BATCH_TEMP S
            INNER JOIN AWAT01 U ON U.SCHOOL_NO = S.SCHOOL_NO AND U.USER_NO = S.USER_NO
            WHERE S.SOURCE_TYPE = @SOURCE_TYPE
            and S.SOURCE_NO = @SOURCE_NO
            and S.SESSION_ID = @SESSION_ID ";
            cmd.CommandText = sSQL;
            cmd.ExecuteNonQuery();

            cmd.CommandText = "DELETE AWAT01_BATCH_TEMP Where SESSION_ID = @SESSION_ID";
            cmd.ExecuteNonQuery();

            cmd.Dispose();
            try {

                db.SaveChanges();
            }
            catch (Exception e) {

                string str = e.Message;

            }
          
        }

        /// <summary>
        /// TeachAddCash 老師 酷幣增/減函數
        /// </summary>
        /// <param name="user">登入者</param>
        /// <param name="CashIn">增/減酷幣數值，如果是扣酷幣點數，則傳入負值</param>
        /// <param name="SchoolNO">學校代號(六碼，如西湖為403605)</param>
        /// <param name="UserNO">帳號</param>
        /// <param name="SourceType">來源種類(如線上投稿，閱讀認證，獎品兌換)</param>
        /// <param name="SourceNo">來源資料鍵值</param>
        /// <param name="LogDesc">在Log紀錄上的補充說明</param>
        /// <param name="ChangeCashAll">是否累加CASH_ALL</param>
        /// <param name="db"></param>
        public static void TeachAddCash(UserProfile user, int CashIn, string SchoolNO, string UserNO, string SourceType, string SourceNo, string LogDesc, bool ChangeCashAll, DateTime? nowtime, ref ECOOL_DEVEntities db)
        {
            short? ADD_CASH_ALL = 0;
            short? ADD_CASH_AVAILABLE = 0;
            short? ADD_CASH_WORKHARD = 0;
            string OutSysLOG_PERSON;

            SetSharedCASH(out ADD_CASH_ALL, out ADD_CASH_AVAILABLE, out ADD_CASH_WORKHARD, out OutSysLOG_PERSON, (short?)CashIn, ChangeCashAll, SourceType);

            var find1 = db.AWAT08.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO);
            AWAT08 aw8 = find1.FirstOrDefault();
            var EntityAWAT08 = ((IObjectContextAdapter)db).ObjectContext.ObjectStateManager.GetObjectStateEntries(System.Data.Entity.EntityState.Added).Where(a => a.Entity is AWAT08).Select(a => (AWAT08)a.Entity).Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO).FirstOrDefault();
            if (EntityAWAT08 != null)
            {
                aw8 = EntityAWAT08;
            }
            
            if (aw8 == null)
            {
                aw8 = db.AWAT08.Create();
                aw8.SCHOOL_NO = SchoolNO;
                aw8.USER_NO = UserNO;
                aw8.CASH_ALL = ADD_CASH_ALL;
                aw8.CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                aw8.CASH_WORKHARD = ADD_CASH_WORKHARD;
                db.AWAT08.Add(aw8);
            }
            else
            {
                if (aw8.CASH_ALL.HasValue == false) aw8.CASH_ALL = 0;
                if (aw8.CASH_AVAILABLE.HasValue == false) aw8.CASH_AVAILABLE = 0;

                aw8.CASH_ALL = aw8.CASH_ALL.Value + ADD_CASH_ALL;
                aw8.CASH_AVAILABLE = aw8.CASH_AVAILABLE.Value + ADD_CASH_AVAILABLE;
                aw8.CASH_WORKHARD = aw8.CASH_WORKHARD.Value + ADD_CASH_WORKHARD;
            }
            DateTime dt = nowtime == null ? DateTime.Now.AddMinutes(3) : (DateTime)nowtime;
            //AWAT01_LOG
            int AWAT08_LOGCOUNT = db.AWAT08_LOG.Where(x => x.SCHOOL_NO == aw8.SCHOOL_NO && x.USER_NO == aw8.USER_NO && x.LOG_TIME == dt).Count();
            if (AWAT08_LOGCOUNT > 0)
            {
                dt = db.AWAT08_LOG.Where(x => x.SCHOOL_NO == aw8.SCHOOL_NO && x.USER_NO == aw8.USER_NO).OrderByDescending(x => x.LOG_TIME).Select(x => x.LOG_TIME).FirstOrDefault();
                dt = dt.AddMinutes(2);
            }
            
            AWAT08_LOG aw8_log = db.AWAT08_LOG.Create();
            aw8_log.SCHOOL_NO = aw8.SCHOOL_NO;
            aw8_log.USER_NO = aw8.USER_NO;
            aw8_log.LOG_TIME = dt;
            aw8_log.SOURCE_TYPE = SourceType;
            aw8_log.SOURCE_NO = (SourceNo ?? "");
            aw8_log.CASH_IN = (short?)CashIn;
            aw8_log.LOG_DESC = LogDesc;
            aw8_log.ADD_CASH_ALL = ADD_CASH_ALL;
            aw8_log.ADD_CASH_AVAILABLE = ADD_CASH_AVAILABLE;
            aw8_log.ADD_CASH_WORKHARD = ADD_CASH_WORKHARD;
            aw8_log.AWAT08_CASH_ALL = aw8.CASH_ALL;
            aw8_log.AWAT08_CASH_AVAILABLE = aw8.CASH_AVAILABLE;
            aw8_log.AWAT08_CASH_WORKHARD = aw8.CASH_WORKHARD;
           
            aw8_log.LOG_PERSON = string.IsNullOrWhiteSpace(OutSysLOG_PERSON) == false ? OutSysLOG_PERSON : user.USER_KEY;

            db.AWAT08_LOG.Add(aw8_log);
        }
        public static void TeachAddCashTolottery(string schoolno, string user_no, string USER_TYPE, string USER_KEY, string SESSION_ID, DataTable dt, string SourceType, string SourceNo, string LogDesc, SqlBulkCopy sbCopy, SqlConnection Conn, SqlTransaction Trans,string Barcode)
        {

            string Message = string.Empty;
            short? ADD_CASH_ALL = 0;
            short? ADD_CASH_AVAILABLE = 0;
            short? ADD_CASH_WORKHARD = 0;
            string OutSysLOG_PERSON;
            short CashTotal = 0;
            short? CashIn = 0;
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            foreach (DataRow dr in dt.Rows)
            {
                CashIn = (dr["CASH"] == null) ? (short?)null : Convert.ToInt16(dr["CASH"]);
                SetSharedCASH(out ADD_CASH_ALL, out ADD_CASH_AVAILABLE, out ADD_CASH_WORKHARD, out OutSysLOG_PERSON, (short?)CashIn, true, SourceType);
           
            var find1 = db.AWAT08.Where(u => u.SCHOOL_NO == schoolno && u.USER_NO == user_no);
            AWAT08 aw8 = find1.FirstOrDefault();
            var EntityAWAT08 = ((IObjectContextAdapter)db).ObjectContext.ObjectStateManager.GetObjectStateEntries(System.Data.Entity.EntityState.Added).Where(a => a.Entity is AWAT08).Select(a => (AWAT08)a.Entity).Where(u => u.SCHOOL_NO == schoolno && u.USER_NO == user_no).FirstOrDefault();
            if (EntityAWAT08 != null)
            {
                aw8 = EntityAWAT08;
            }

            if (aw8 == null)
            {
                aw8 = db.AWAT08.Create();
                aw8.SCHOOL_NO = schoolno;
                aw8.USER_NO = user_no;
                aw8.CASH_ALL = ADD_CASH_ALL;
                aw8.CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                aw8.CASH_WORKHARD = ADD_CASH_WORKHARD;
                db.AWAT08.Add(aw8);
            }
            else
            {
                if (aw8.CASH_ALL.HasValue == false) aw8.CASH_ALL = 0;
                if (aw8.CASH_AVAILABLE.HasValue == false) aw8.CASH_AVAILABLE = 0;

                aw8.CASH_ALL = aw8.CASH_ALL.Value + ADD_CASH_ALL;
                aw8.CASH_AVAILABLE = aw8.CASH_AVAILABLE.Value + ADD_CASH_AVAILABLE;
                aw8.CASH_WORKHARD = aw8.CASH_WORKHARD.Value + ADD_CASH_WORKHARD;
            }
                DateTime todaytime = new DateTime();
                todaytime = DateTime.Now;
            //AWAT01_LOG
            int AWAT08_LOGCOUNT = db.AWAT08_LOG.Where(x => x.SCHOOL_NO == aw8.SCHOOL_NO && x.USER_NO == aw8.USER_NO && x.LOG_TIME == todaytime).Count();
            if (AWAT08_LOGCOUNT > 0)
            {
                    todaytime = db.AWAT08_LOG.Where(x => x.SCHOOL_NO == aw8.SCHOOL_NO && x.USER_NO == aw8.USER_NO).OrderByDescending(x => x.LOG_TIME).Select(x => x.LOG_TIME).FirstOrDefault();
                    todaytime = todaytime.AddMinutes(2);
            }

            AWAT08_LOG aw8_log = db.AWAT08_LOG.Create();
            aw8_log.SCHOOL_NO = aw8.SCHOOL_NO;
            aw8_log.USER_NO = aw8.USER_NO;
            aw8_log.LOG_TIME = todaytime;
            aw8_log.SOURCE_TYPE = SourceType;
            aw8_log.SOURCE_NO = (SourceNo ?? "");
            aw8_log.CASH_IN = (short?)CashIn;
            aw8_log.LOG_DESC = LogDesc;
            aw8_log.ADD_CASH_ALL = ADD_CASH_ALL;
            aw8_log.ADD_CASH_AVAILABLE = ADD_CASH_AVAILABLE;
            aw8_log.ADD_CASH_WORKHARD = ADD_CASH_WORKHARD;
            aw8_log.AWAT08_CASH_ALL = aw8.CASH_ALL;
            aw8_log.AWAT08_CASH_AVAILABLE = aw8.CASH_AVAILABLE;
            aw8_log.AWAT08_CASH_WORKHARD = aw8.CASH_WORKHARD;
            aw8_log.LOG_PERSON = string.IsNullOrWhiteSpace(OutSysLOG_PERSON) == false ? OutSysLOG_PERSON : USER_KEY;

            db.AWAT08_LOG.Add(aw8_log);
            }
            try
            {
                db.SaveChanges();
                
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            
            }
        }
        /// <summary>
        /// 給班導師 點數
        /// </summary>
        /// <param name="user">登入者</param>
        /// <param name="CashIn">增/減酷幣數值，如果是扣酷幣點數，則傳入負值</param>
        /// <param name="SchoolNO">學校代號(六碼，如西湖為403605)</param>
        /// <param name="UserNO">帳號</param>
        /// <param name="SourceType">來源種類(如線上投稿，閱讀認證，獎品兌換)</param>
        /// <param name="SourceNo">來源資料鍵值</param>
        /// <param name="LogDesc">在Log紀錄上的補充說明</param>
        /// <param name="ChangeCashAll">是否累加CASH_ALL</param>
        /// <param name="db"></param>
        public static void ClassTeachAddCash(UserProfile user, int CashIn, string SchoolNO, string UserNO, string SourceType, string SourceNo, string LogDesc, bool ChangeCashAll, ref ECOOL_DEVEntities db)
        {
            //取出此學生班級
            string ClassNo = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_NO == UserNO).Select(a => a.CLASS_NO).FirstOrDefault();

            if (string.IsNullOrWhiteSpace(ClassNo) == false)
            {
                List<HRMT03> Hrmt03List = db.HRMT03.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO == ClassNo).ToList();

                foreach (HRMT03 Teach in Hrmt03List)
                {
                    if (db.HRMT25.Where(a => a.SCHOOL_NO == Teach.SCHOOL_NO && a.USER_NO == Teach.TEACHER_NO && a.ROLE_ID == HRMT24_ENUM.ROLE_CLASS_TEACH).Any())
                    {
                        TeachAddCash(user, CashIn, Teach.SCHOOL_NO, Teach.TEACHER_NO, SourceType, SourceNo, LogDesc, ChangeCashAll, null, ref db);
                    }
                }
            }
        }
        public static int getCASHALL(string SCHOOL_NO ,string USER_NO, ref ECOOL_DEVEntities db) {
            int ATM = 0;
            ATM = (db.AWAT10.Where(w => w.STATUS == "1 " && w.SCHOOL_NO == SCHOOL_NO && w.USER_NO == USER_NO)
                                 .Sum(w => (int?)w.AMT) ?? 0);
            IQueryable<VAWAT15> AWAT15List = (from y in db.AWAT01

                                              join aLog in
            (from log in db.AWAT01_LOG
             where 1 == 1
             group log by new { log.SCHOOL_NO, log.USER_NO } into logGroup
             select new
             {
                 logGroup.Key.SCHOOL_NO,
                 logGroup.Key.USER_NO,
                 CASH_ALL = logGroup.Sum(x => x.ADD_CASH_ALL),
                 CASH_AVAILABLE = logGroup.Sum(x => x.ADD_CASH_AVAILABLE),
                 CASH_WORKHARD = logGroup.Sum(x => x.ADD_CASH_WORKHARD)
             })

               on new { y.SCHOOL_NO, y.USER_NO } equals new { aLog.SCHOOL_NO, aLog.USER_NO }
                                              where y.SCHOOL_NO == SCHOOL_NO && y.USER_NO == USER_NO
                                              select new VAWAT15
                                              {
                                                  CASH_ALL = (int)y.CASH_ALL,
                                                  SUMCASH_AVAILABLE = ((aLog.CASH_AVAILABLE < 0) ? (y.CASH_AVAILABLE ?? 0) : (aLog.CASH_AVAILABLE ?? 0))
                                                  + ATM


                                              }
                      ).AsQueryable();

            var mainQuery = from temp in AWAT15List
                            select new VAWAT15
                            {
                                CASH_ALL = (int)(temp.SUMCASH_AVAILABLE > temp.CASH_ALL ? temp.SUMCASH_AVAILABLE : temp.CASH_ALL),

                                SUMCASH_AVAILABLE = temp.SUMCASH_AVAILABLE,
                            };
            List<VAWAT15> AWAT15Listtemp = mainQuery.ToList();
            int CASH_ALL = 0;
            if (AWAT15Listtemp != null && AWAT15Listtemp.Count() != 0)
            {

                CASH_ALL = AWAT15Listtemp.FirstOrDefault().CASH_ALL;

            }


            return CASH_ALL;

        }
    }
}