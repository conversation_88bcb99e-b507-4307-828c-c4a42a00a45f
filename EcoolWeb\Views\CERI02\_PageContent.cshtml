﻿@model AccreditationManageIndexViewModel
@using ECOOL_APP.com.ecool.util;
@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)

<div class="toolbar text-right">
    <a role="button" class="btn btn-sm btn-sys" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)'>
        <span class="fa fa-plus" aria-hidden="true"></span>
        新增護照明細
    </a>
</div>

<div id="Q_Div">
    <div role="form">

        <div class="row">
            <div class="col-md-2">
                <label class="control-label">護照名稱</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereACCREDITATION_TYPE, (IEnumerable<SelectListItem>)ViewBag.AccreditationTypeItems, new { @class = "form-control input-sm" })
            </div>
            <div class="col-md-2">
                <label class="control-label">護照明細名稱</label>
            </div>
            <div class="col-md-5">

                <select class="selectpicker show-menu-arrow form-control" title="" date-style="input-sm" data-size="auto" data-live-search="true" id="@Html.IdFor(m=>m.WhereACCREDITATION_NAME)" name="@Html.NameFor(m=>m.WhereACCREDITATION_NAME)">
                    @if (ViewBag.AccreditationsItems != null)
                    {
                        foreach (var item in ViewBag.AccreditationsItems as IEnumerable<SelectListItem>)
                        {
                            <option data-tokens="@item.Text" value="@item.Value" @(item.Selected ? "selected" : "")>@item.Text</option>
                        }
                    }
                </select>
            </div>
            
                <div class="col-md-2">
                    <label class="control-label">關鍵字(護照細項名稱，隸屬護照):</label>
                </div>
                <div class="col-md-3">
                    @Html.EditorFor(m => m.search, new { htmlAttributes = new { @class = "form-control input-sm" } })
                </div>
     
        </div>
        <br>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('true')" />
    </div>
</div>
<br />

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-white">
                <tr class="thead-primary">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().TYPE_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ACCREDITATION_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap" style="color:black">認證老師</th>
                    <th style="width:60px"></th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    var TotalGradeSemester = (Enum.GetNames(typeof(HRMT01.GradeVal)).Length) + (Enum.GetNames(typeof(HRMT01.SemesterVal)).Length);
                    var TotalGrade = (Enum.GetNames(typeof(HRMT01.GradeVal)).Length);

                    foreach (var item in Model.ListData)
                    {

                        <tr class="text-center">
                            <td>@Html.DisplayFor(modelItem => item.TYPE_NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.ACCREDITATION_NAME)</td>
                            <td>
                                @if (item.VERIFIERs?.Count() > 0)
                                {

                                    var strVERIFIER = String.Join("、", item.VERIFIERs);

                                    <span data-html="true" data-toggle="tooltip" data-placement="right" title='@(strVERIFIER)'>
                                        @StringHelper.LeftStringR(strVERIFIER, 8, $"…共{item.VERIFIERs.Count()}位")
                                    </span>

                                }
                            </td>
                            <td class="text-nowrap">
                                <a href="@Url.Action("Edit",new {Keyword = item.ACCREDITATION_ID })" role="button" class="btn btn-xs btn-Basic" title="編輯">
                                    <span class="fa fa-pencil" aria-hidden="true"></span> 編輯
                                </a>
                                <button type="button" onclick="onBtnDel('@item.ACCREDITATION_ID')" role="button" class="btn btn-xs btn-Basic" title="刪除">
                                    <span class="glyphicon glyphicon-trash" aria-hidden="true"></span> 刪除
                                </button>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center" style="padding:10px"><strong>目前無資料</strong></div>
        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)

<script>
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
        $('#@Html.IdFor(m=>m.WhereACCREDITATION_NAME)').selectpicker('refresh');
    })
</script>