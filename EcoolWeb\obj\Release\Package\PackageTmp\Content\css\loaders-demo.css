main {
    width: 95%;
    max-width: 1000px;
    margin: 4em auto;
    opacity: 0;
    background-color: #ed5565;
    color: #fff;
    font-size: 16px;
}

    main.loaded {
        transition: opacity .25s linear;
        opacity: 1;
    }

    main header {
        width: 100%;
    }

        main header > div {
            width: 50%;
        }

        main header > .left,
        main header > .right {
            height: 100%;
        }

    main .loaders {
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex: 0 1 auto;
        flex-direction: row;
        flex-wrap: wrap;
    }

        main .loaders .loader {
            box-sizing: border-box;
            display: flex;
            flex: 0 1 auto;
            flex-direction: column;
            flex-grow: 1;
            flex-shrink: 0;
            flex-basis: 25%;
            max-width: 25%;
            height: 200px;
            align-items: center;
            justify-content: center;
            perspective: 500px;
        }

            main .loaders .loader .tooltip {
                -webkit-transition: all 200ms ease;
                transition: all 200ms ease;
                -webkit-transform: translate3d(-50%, 0%, 0);
                transform: translate3d(-50%, 0%, 0);
                -webkit-transform-origin: 0 10px;
                transform-origin: 0 10px;
                background-color: #fff;
                border-radius: 4px;
                color: #2f2f2f;
                display: block;
                font-size: 14px;
                line-height: 1;
                left: 50%;
                opacity: 0;
                padding: 4px 20px;
                position: absolute;
                text-align: left;
                top: 80%;
                pointer-events: none;
                white-space: nowrap;
            }

                main .loaders .loader .tooltip:before {
                    border: 6px solid;
                    border-color: transparent;
                    border-bottom-color: #fff;
                    content: ' ';
                    display: block;
                    height: 0;
                    left: 50%;
                    margin-left: -10px;
                    position: absolute;
                    top: -12px;
                    width: 0;
                }

                main .loaders .loader .tooltip:after {
                    content: ' ';
                    display: block;
                    position: absolute;
                    bottom: -20px;
                    left: 0;
                    width: 100%;
                    height: 20px;
                }

                main .loaders .loader .tooltip:hover {
                    -webkit-transform: rotateX(0deg) translate3d(-50%, -10%, 0);
                    transform: rotateX(0deg) translate3d(-50%, -10%, 0);
                    opacity: 1;
                    pointer-events: auto;
                }

            main .loaders .loader:hover .tooltip {
                -webkit-transform: translate3d(-50%, -10%, 0);
                transform: translate3d(-50%, -10%, 0);
                opacity: 1;
                pointer-events: auto;
            }

/**
 * Util classes
 */
.left {
    float: left;
}

.right {
    float: right;
}

.cf, main header {
    content: "";
    display: table;
    clear: both;
}

/**
 * Buttons
 */
.btn {
    color: #fff;
    padding: .75rem 1.25rem;
    border: 2px solid #fff;
    border-radius: 4px;
    text-decoration: none;
    transition: transform .1s ease-out, border .1s ease-out, background-color .15s ease-out, color .1s ease-out;
    margin: 2rem 0;
}

    .btn:hover {
        transform: scale(1.01562);
        background-color: #fff;
        color: #ed5565;
    }