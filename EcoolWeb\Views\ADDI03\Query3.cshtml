@using ECOOL_APP.com.ecool.Models.entity;
@using System.Collections;
@using System;
@using System.Reflection;


@model  List<ADDV02>
@{


    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string DivQcss = string.Empty;

    if (ViewBag.QShowYN == "N")
    {
        ViewBag.Title = "閱讀護照-本學年閱讀護照";
        DivQcss = "div-hidden";
    }
    else
    {
        ViewBag.Title = "閱讀護照-閱讀護照現況一覽表";
        DivQcss = "";
    }



    //Layout = "~/Views/Shared/_Layout.cshtml";
    string CGRADE = string.Empty;
    string sStatus = string.Empty;
    int iReGRADE = (string.IsNullOrWhiteSpace(Request["GRADE"]) == false) ? (Request["GRADE"] == "A") ? 1 : Convert.ToInt16(Request["GRADE"]) : 1;
    int iBookCount = 0;
    int ADDT03iBookCount = 0;
    int ADDT03ItemCount = 0;
    List<ADDT03> ADDT03Items = new List<ADDT03>();
    List<ADDV02> ADDT02Items = new List<ADDV02>();


    ADDT02Items = ViewBag.aDDV02sDetail;

    iBookCount = ViewBag.BookCount;
    ADDT03Items = ViewBag.BookDetail;
    ADDT03iBookCount = ADDT03Items.Count() / 10;
    ADDT03ItemCount = ADDT03Items.Count() - (ADDT03iBookCount * 10);
    if (ADDT03ItemCount > 0)
    {

        ADDT03iBookCount = ADDT03iBookCount + 2;
    }
    string ddlCLASS_NO = string.Empty;
    string ddlGrade = string.Empty;
    string whereKeyword = string.Empty;

    if (user != null)
    {
        ddlCLASS_NO = user.TEACH_CLASS_NO ?? user.CLASS_NO;
        ddlGrade = string.IsNullOrWhiteSpace(ddlCLASS_NO) == false ? new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(ddlCLASS_NO, 1) : "";

        if (user.USER_TYPE == UserType.Student)
        {
            //whereKeyword = user.USER_NO;
        }
    }

    int WhereGRADE = string.IsNullOrWhiteSpace(ddlGrade) == false ? Convert.ToInt32(ddlGrade) : 1;
}
<style>
    ul.pagination {
        display: flex;
        list-style: none;
        padding: 0;
    }

    li.page-item {
        margin: 5px;
    }

    a.page-link {
        text-decoration: none;
        padding: 5px 10px;
        background-color: #007BFF;
    }
</style>
@helper  ImgFun(string Pass, string SCHOOL_NO, string USER_NO, short? GRADE, string TWO_D_BOOK_ID)
{
    if (Pass == "Y")
    {
        string ImgID = "Img" + USER_NO + GRADE.ToString() + TWO_D_BOOK_ID;

        <a id="@ImgID" href="#" role="button" onmouseover="ShowBOOK_NAME('@SCHOOL_NO','@USER_NO','@GRADE.ToString()','@TWO_D_BOOK_ID')" onclick="Chg_BOOK_NO('@USER_NO', '@TWO_D_BOOK_ID')">
            <img src="~/Content/img/web-student-submit-10.png">
        </a>
    }


}

@helper GradeFun(int vGrade, string vClass_No, string whereKeyword)
{
    string GradeClass1 = vGrade == 1 ? "active" : "";
    string GradeClass2 = vGrade == 2 ? "active" : "";
    string GradeClass3 = vGrade == 3 ? "active" : "";
    string GradeClass4 = vGrade == 4 ? "active" : "";
    string GradeClass5 = vGrade == 5 ? "active" : "";
    string GradeClass6 = vGrade == 6 ? "active" : "";
    <div style="height:20px"></div>
    <div class="row">
        <div class="col-xs-12 text-left">
            查詢完成:
            <a id="btnGRADE1" onclick="ClickGRADE('1')" role="button" class="btn btn-xs btn-pink @GradeClass1">一年級</a>
            <a id="btnGRADE2" onclick="ClickGRADE('2')" role="button" class="btn btn-xs btn-pink @GradeClass2">二年級</a>
            <a id="btnGRADE3" onclick="ClickGRADE('3')" role="button" class="btn btn-xs btn-pink @GradeClass3">三年級</a>
            <a id="btnGRADE4" onclick="ClickGRADE('4')" role="button" class="btn btn-xs btn-pink @GradeClass4">四年級</a>
            <a id="btnGRADE5" onclick="ClickGRADE('5')" role="button" class="btn btn-xs btn-pink @GradeClass5">五年級</a>
            <a id="btnGRADE6" onclick="ClickGRADE('6')" role="button" class="btn btn-xs btn-pink @GradeClass6">六年級</a>
            護照的學生
        </div>
    </div>

}

@*<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>*@

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI03_QUERY3_URLS = {
            query2: "Query3"
        };
    </script>
    <script src="~/Scripts/ADDI03/query3.js" nonce="cmlvaw"></script>

  
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}

@using (Html.BeginForm("Query3", "ADDI03", FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
{
    @Html.Hidden("GRADE", iReGRADE)
    @Html.Hidden("Page")
    @Html.Hidden("hClass_No", (String)ViewBag.hClass_No)

    <div class="form-inline">


        <a role="button" href='@Url.Action("rpp", "rpp")' class="btn btn-sm btn-sys">
            護照說明
        </a>

        <a href='@Url.Action("rpp_book", "rpp")' role="button" class="btn btn-sm btn-sys">
            閱讀書單
        </a>

        @if (string.IsNullOrWhiteSpace(ddlCLASS_NO) == false)
        {

            if (ViewBag.QShowYN == "N")
            {
                @Html.ActionLink("本學年護照", "Query3", "ADDI03", new { GRADE = WhereGRADE, QShowYN = "N" }, new { @role = "button", @class = "btn btn-sm btn-sys active" })
                @Html.ActionLink("護照現況一覽表", "Query3", "ADDI03", new { GRADE = WhereGRADE, whereKeyword = whereKeyword }, new { @role = "button", @class = "btn btn-sm btn-sys " })
            }
            else
            {
                @Html.ActionLink("本學年護照", "Query3", "ADDI03", new { GRADE = WhereGRADE, QShowYN = "N" }, new { @role = "button", @class = "btn btn-sm btn-sys" })
                @Html.ActionLink("護照現況一覽表", "Query3", "ADDI03", new { GRADE = WhereGRADE, whereKeyword = whereKeyword }, new { @role = "button", @class = "btn btn-sm btn-sys active" })
            }

        }
        else
        {
            @Html.ActionLink("護照現況一覽表", "Query3", "ADDI03", new { GRADE = WhereGRADE, whereKeyword = whereKeyword }, new { @role = "button", @class = "btn btn-sm btn-sys active" })
        }



        <a href='@Url.Action("Query2", "ADDI03", new { ddlGrade = ddlGrade, ddlCLASS_NO = ddlCLASS_NO, whereKeyword = whereKeyword })' role="button" class="btn btn-sm btn-sys">
            護照完成一覽表
        </a>

        @if (user != null)
        {
            if (user.USER_TYPE == UserType.Student)
            {


                <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                    我的護照
                </a>


            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                    寶貝護照
                </a>
            }
        }


        <div class="form-inline @DivQcss" role="form">
            <div class="form-group">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.Editor("whereKeyword", (string)ViewBag.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
            <div class="form-group">
                <label class="control-label">年級：</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("whereGrade", (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", @onchange = "ChangeGrade(this.value)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級：</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassNoItem, new { @class = "form-control input-sm", @onchange = "ChangeClassNo(this.value)" })
            </div>
            <input type="submit" class="btn-yellow btn btn-sm" value="搜尋" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>

    </div>
}
<div id="ShowW">

</div>
<div class="@DivQcss">
    @GradeFun(iReGRADE, (string)ViewBag.hClass_No, (string)ViewBag.whereKeyword)
</div>


@if (user != null)
{
    if (user.USER_TYPE != UserType.Student && AppMode == false)
    {
        <div class="row">
            <div class="col-xs-12 text-right">
                <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            </div>
        </div>

    }
}
@if (AppMode == false)
{
    <img src="~/Content/img/web-bar2-revise-30.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
}

@*<div class="imgTitle" style="background-image:url('@Url.Content("~/content/img/web-bar2-revise-32.png")');">
        <div class="imgTitleFont">
           @ViewBag.Title
        </div>
    </div>*@
@{


    int pageskip = 0;
    pageskip = Model.ToList().Select(x => x.SkipPage).FirstOrDefault();

}
@if (ADDT03Items.Count() > 0)
{
    <div class="row">
        <div class="col-xs-12 text-center">

            @if (ADDT03iBookCount == 0)
            {
                <a id="btnGRADE1" onclick="ClickPage(0)" role="button" class="btn btn-xs btn-pink active"> 第一頁</a>
            }
            else if (ADDT03iBookCount == 1)
            {

                <a id="btnGRADE1" onclick="ClickPage(0)" role="button" class="btn btn-xs btn-pink active"> 第一頁</a>

            }
            else
            {

                for (int i = 1; i < ADDT03iBookCount; i++)
                {

                    int BookStrID = 0;
                    BookStrID = (i - 1) * 10;
                    int PageSTART = 0;
                    PageSTART = Model.Select(x => x.Page).FirstOrDefault();
                    if (pageskip == BookStrID)
                    {
                        <a id="btnGRADE1" onclick="ClickPage('@BookStrID')" role="button" class="btn btn-xs btn-pink active"> 第@(i)頁</a>
                    }
                    else
                    {

                        <a id="btnGRADE1" onclick="ClickPage('@BookStrID')" role="button" class="btn btn-xs btn-pink"> 第@(i)頁</a>
                    }





                }
            }


        </div>
    </div>      }
<div class="table-responsive">
    <div class="text-center">
        <table class="table-ecool table-92Per table-hover table-ecool-rpp">
            <thead>
                <tr>
                    <th align="center" width="50px" style="white-space:nowrap">班級</th>
                    <th align="center" width="90px" style="white-space:nowrap">姓名</th>

                    @{
                        int ret = 0;
                        int TakeSize = 0;

                        ret = (ADDT03Items.Count() - pageskip);

                    }

                    @for (int i = 0; i < ADDT03Items.Select(x => x.BOOK_ID).Skip(pageskip).Take(10).Count(); i++)
                    {
                        <th align="center">@(i + 1 + pageskip)    @*@ADDT03Items.Select(x => x.BOOK_ID).Skip(pageskip).Take(10).ToList()[i]*@   </th>
                    }
                    <th align="center">備註</th>
                </tr>
            </thead>
            <tbody>
                @{



                    int Num = 0;

                    foreach (ADDV02 item in Model)
                    {
                        Num++;
                        int ADDT03ItemsCounts = 0;
                        ADDT03ItemsCounts = ADDT03Items.Where(y => y.SCHOOL_NO == item.SCHOOL_NO && y.GRADE == item.GRADE).Count();

                        string uname = item.SEAT_NO + " " + item.SNAME;




                        @*<a id="btnGRADE1" onclick="ClickGRADE1('1')" role="button" class="btn btn-xs btn-pink"> 第@(i)頁</a>*@

                        <tr style="height:30px;vertical-align:middle">


                            <td align="left">@item.CLASS_NO</td>
                            <td align="left">@uname</td>
                            @{ string str = "";

                                List<ADDV02> ADDV02Items = new List<ADDV02>();
                                List<ADDT03> ADDT03ItemS = new List<ADDT03>();
                                int BOOKCount = 0;
                                int ADDV02ItemsCount = 0;
                                ADDV02Items = db.ADDV02.Where(x => x.USER_NO == item.USER_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.GRADE == item.GRADE).ToList();
                                ADDV02ItemsCount = db.ADDT05.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.GRADE == item.GRADE && x.USER_NO == item.USER_NO).Count();
                                BOOKCount = db.ADDT03.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.GRADE == item.GRADE && x.BOOK_NAME != "").Count();
                                ADDT03ItemS = ADDT03Items.Where(y => y.SCHOOL_NO == item.SCHOOL_NO && y.GRADE == item.GRADE).Skip(pageskip).Take(10).ToList();

                                int j = 1;
                            }

                            @foreach (var itemS2 in ADDT03ItemS)
                            {
                                string BooKSTR = "";
                                string BooKSTRCVal = "";
                                if (itemS2.BOOK_ID.Length > 2)
                                {


                                    BooKSTR = "C" + itemS2.BOOK_ID.Substring(1, 2);

                                    BooKSTRCVal = ADDT02Items.Where(x => x.USER_NO == item.USER_NO && x.BOOK_ID == itemS2.BOOK_ID.Substring(1, 2)).Select(x => x.READ).FirstOrDefault();
                                }
                                else
                                {


                                    BooKSTR = "C" + itemS2.BOOK_ID;

                                    BooKSTRCVal = ADDT02Items.Where(x => x.USER_NO == item.USER_NO && x.BOOK_ID == itemS2.BOOK_ID).Select(x => x.READ).FirstOrDefault();

                                }
                                <td align="center">
                                    @if (itemS2.BOOK_ID.Length > 2)
                                    {


                                        @ImgFun(BooKSTRCVal, item.SCHOOL_NO, item.USER_NO, item.GRADE, itemS2.BOOK_ID.Substring(1, 2))}
                                    else
                                    {

                                        @ImgFun(BooKSTRCVal, item.SCHOOL_NO, item.USER_NO, item.GRADE, itemS2.BOOK_ID)
                                    }
                                </td>



                                j++;

                            }

                            @{
                                DateTime? dateTimeItem = new DateTime();
                                string dateTimeItemtemp = "";
                                dateTimeItem =
                                                ADDV02Items.Where(x => x.USER_NO == item.USER_NO).Select(x => x.PASS_DATE).FirstOrDefault();
                                if (dateTimeItem != null)
                                {
                                    dateTimeItemtemp = ((DateTime)dateTimeItem).ToShortDateString();
                                }

                            }

                            <td align="center">
                                @if (
                          BOOKCount ==
                              ADDV02ItemsCount || ADDV02ItemsCount > BOOKCount)
                                {
                                    @dateTimeItemtemp


                                }



                            </td>
                        </tr>







                        if (Num % 20 == 0)
                        {
                            Response.Flush();
                        }

                    }









                }
            </tbody>
        </table>
        @if (ADDT03Items.Count() == 0)
        {
            <h3>查無資料!!</h3>
        }
    </div>
</div>

