﻿@model GameTeamStatusIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();


    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

}

@if (IsBtnGoHide == false)
{
    <div id="DivAddButton">
        <i id="title" class="fa fa-arrow-left fa-3x"></i>
        <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
    </div>

}
<br />

@if (user == null)
{
    <div class="row">
        <div class="col-md-6 col-md-offset-3">
            <button type="button" onclick="OnRenew()" class="btn btn-default btn-lg btn-block">重新整理</button>
        </div>
    </div>
    <br />
}


<script type="text/javascript">

       var targetFormID = '#form1';

          function OnBack() {
                $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")
                $(targetFormID).submit();
        }
</script>
@using (Html.BeginForm("QueryTeamStatus", "Game", FormMethod.Post, new { id = "form1", name = "form1", @AutoComplete = "Off" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)

    if (!string.IsNullOrWhiteSpace((string)(TempData["StatusMessage"] ?? "")))
    {
        <div id="MainView">
            <div style="height:calc(10vh)"></div>
            <div class="row">
                <div class="col-md-6 col-md-offset-3">
                    <div class="login-panel panel panel-danger">
                        <div class="panel-heading">
                            <h1 class="panel-title text-center">訊息</h1>
                        </div>
                        <div class="panel-body">
                            <h1 style="color:red;text-align:center">
                                <strong> @Html.Raw(HttpUtility.HtmlDecode((string)TempData["StatusMessage"]))</strong>
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    }
    else
    {
        <div id="MainView">
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    <div class="panel with-nav-tabs panel-success" id="panel">
                        <div class="panel-heading">
                            <h1>
                                @(Model.GameInfo.GAME_NAME)-小組闖關狀態
                            </h1>
                        </div>
                        <div class="panel-body">

                            @if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.一般)
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <samp class="form-group">
                                                        小組名稱/關卡名稱
                                                    </samp>
                                                </th>

                                                @foreach (var Ds in Model.GameInfoDs)
                                                {
                                                    <th>
                                                        <samp class="form-group">
                                                            @Ds.LEVEL_NAME
                                                        </samp>
                                                    </th>
                                                }

                                                <th>
                                                    <samp class="form-group">
                                                        全部過關
                                                    </samp>
                                                </th>
                                                <th onclick="doSort('SEAT_NO')">
                                                    名次
                                                    <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                                </th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var Team in Model.Teams.OrderBy(a => a.NAME))
                                            {
                                                <tr>
                                                    <td>
                                                        @Team.NAME
                                                    </td>
                                                    @foreach (var Ds in Model.GameInfoDs)
                                                    {
                                                        <td align="center">
                                                            @if (Model.GameLogs.Where(a => a.TEMP_USER_ID == Team.TEMP_USER_ID && a.SOURCE_NO == Ds.LEVEL_NO).Any())
                                                            {
                                                                <fomt style="color:red">
                                                                    V
                                                                </fomt>
                                                            }
                                                            else
                                                            {
                                                                <fomt style="color:grey">
                                                                    X
                                                                </fomt>
                                                            }
                                                        </td>
                                                    }
                                                    <td>
                                                        @{   string  RankNumstr="";
                                                            var OK = (from a in Model.GameLogs
                                                                      join b in Model.GameInfoDs on a.SOURCE_NO equals b.LEVEL_NO
                                                                      where a.TEMP_USER_ID == Team.TEMP_USER_ID
                                                                      select b.LEVEL_NO).Distinct().Count() >= Model.GameInfoDs.Count();

                                                            if (OK)
                                                            {
                                                                GameTeamStatusRankList gameTeamStatusRankLists = new GameTeamStatusRankList();


                                                                gameTeamStatusRankLists = Model.GameTeamRankList?.Where(x => x.TEMP_USER_ID == Team.TEMP_USER_ID).FirstOrDefault();
                                                                int RankNum = 0;
                                                                //RankNum = Model.GameTeamRankList.Where(x => x.TEMP_USER_ID == Team.TEMP_USER_ID).Select(x => x.RankNum).FirstOrDefault();
                                                                if (gameTeamStatusRankLists?.GAME_NO != null)
                                                                {
                                                                    RankNum = gameTeamStatusRankLists.RankNum;
                                                                    RankNumstr = RankNum.ToString();
                                                                }
                                                                <text>@(Model.GameLogs.Where(a => a.TEMP_USER_ID == Team.TEMP_USER_ID && Model.GameInfoDs.Any(x => x.LEVEL_NO == a.SOURCE_NO)).OrderByDescending(a => a.LOG_TIME).Select(a => a.LOG_TIME).FirstOrDefault())</text>
                                                            }
                                                        }
                                                    </td>
                                                    <td>
                                                        @RankNumstr

                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>

                            }
                            else
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <samp class="form-group">
                                                        小組名稱/關卡名稱
                                                    </samp>
                                                </th>

                                                @foreach (var QDs in Model.GameInfoQDs)
                                                {
                                                    <th>
                                                        <samp class="form-group">
                                                            @QDs.G_SUBJECT
                                                        </samp>
                                                    </th>
                                                }

                                                <th>
                                                    <samp class="form-group">
                                                        全部過關
                                                    </samp>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var Team in Model.Teams.OrderBy(a => a.NAME))
                                            {
                                                <tr>
                                                    <td>
                                                        @Team.NAME
                                                    </td>
                                                    @foreach (var QDs in Model.GameInfoQDs)
                                                    {
                                                        <td align="center">
                                                            @if (Model.GameAnsLogs.Where(a => a.CHG_PERSON == Team.TEMP_USER_ID && a.GROUP_ID == QDs.GROUP_ID).Any())
                                                            {
                                                                <text>O</text>
                                                            }
                                                            else
                                                            {
                                                                <fomt style="color:red">
                                                                    X
                                                                </fomt>
                                                            }
                                                        </td>
                                                    }

                                                    <td>
                                                        @{
                                                            string RankNumstr1 = "";
                                                            var OK = (from a in Model.GameAnsLogs
                                                                      join b in Model.GameInfoQDs on a.GROUP_ID equals b.GROUP_ID
                                                                      where a.CHG_PERSON == Team.TEMP_USER_ID
                                                                      select b.GROUP_ID).Distinct().Count() >= Model.GameInfoQDs.Count();

                                                            if (OK)
                                                            {
                                                                int RankNum1 = 0;
                                                                //RankNum1 = Model.GameTeamRankList.Where(x => x.TEMP_USER_ID == Team.TEMP_USER_ID).Select(x => x.RankNum).FirstOrDefault();

                                                                //RankNumstr1 = RankNum1.ToString();
                                                                <text>@(Model.GameAnsLogs.Where(a => a.CHG_PERSON == Team.TEMP_USER_ID && Model.GameInfoQDs.Any(x => x.GROUP_ID == a.GROUP_ID)).OrderByDescending(a => a.CHG_DATE).Select(a => a.CHG_DATE).FirstOrDefault())</text>
                                                            }
                                                        }
                                                    </td><td>
                                                        @RankNumstr1

                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}