﻿@model CERI05PersonalViewModel

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)

@Html.HiddenFor(m => m.ThisSCHOOL_NO)
@Html.HiddenFor(m => m.ThisUSER_NO)

<div id="Q_Div">
    <div role="form">

        <div class="row" style="padding-top:10px">
            <div class="col-md-2">
                <label class="control-label">年級</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereGRADE, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="col-md-2">
                <label class="control-label">班級</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
        </div>
        <div class="row" style="padding-top:10px">
            <div class="col-md-2">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="col-md-3">
                @Html.EditorFor(m => m.WhereUser, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
        </div>
        <br />
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('true')" />
    </div>
</div>
<br />

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-white">
                <tr class="thead-primary">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CLASS_NO, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().SEAT_NO, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th style="width:60px"></th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {
                        <tr class="text-center">
                            <td>@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                            <td>@Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                            <td>@Html.DisplayFor(modelItem => item.NAME)</td>
                            <td class="text-nowrap">
                                <button type="button" role="button" class="btn btn-xs btn-Basic" title="進入補登"
                                        onclick="onBtnEdit('@item.SCHOOL_NO','@item.USER_NO')">
                                    <span class="fa fa-pencil" aria-hidden="true"></span> 進入補登
                                </button>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center" style="padding:10px"><strong>查無任何資料</strong></div>
        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)