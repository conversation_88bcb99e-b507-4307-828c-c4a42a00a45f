﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'cs', {
	button: {
		title: 'Vlastnosti tlačítka',
		text: '<PERSON>ise<PERSON>',
		type: 'Typ',
		typeBtn: 'Tla<PERSON><PERSON><PERSON><PERSON>',
		typeSbm: 'Odes<PERSON>',
		typeRst: 'Obnovit'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Vlastnosti zaškrtávacího políčka',
		radioTitle: 'Vlastnosti přepínače',
		value: '<PERSON>dnota',
		selected: 'Za<PERSON>krtnuto',
		required: 'Vyžadováno'
	},
	form: {
		title: 'Vlastnosti formuláře',
		menu: 'Vlastnosti formuláře',
		action: 'Akce',
		method: 'Metoda',
		encoding: 'Kódování'
	},
	hidden: {
		title: 'Vlastnosti skrytého pole',
		name: '<PERSON><PERSON><PERSON><PERSON>',
		value: '<PERSON>dn<PERSON>'
	},
	select: {
		title: 'Vlastnosti seznamu',
		selectInfo: 'Info',
		opAvail: 'Dostupná nastavení',
		value: 'Hodnota',
		size: 'Velikost',
		lines: 'Řádků',
		chkMulti: 'Povolit mnohonásobné výběry',
		required: 'Vyžadováno',
		opText: 'Text',
		opValue: 'Hodnota',
		btnAdd: 'Přidat',
		btnModify: 'Změnit',
		btnUp: 'Nahoru',
		btnDown: 'Dolů',
		btnSetValue: 'Nastavit jako vybranou hodnotu',
		btnDelete: 'Smazat'
	},
	textarea: {
		title: 'Vlastnosti textové oblasti',
		cols: 'Sloupců',
		rows: 'Řádků'
	},
	textfield: {
		title: 'Vlastnosti textového pole',
		name: 'Název',
		value: 'Hodnota',
		charWidth: 'Šířka ve znacích',
		maxChars: 'Maximální počet znaků',
		required: 'Vyžadováno',
		type: 'Typ',
		typeText: 'Text',
		typePass: 'Heslo',
		typeEmail: 'Email',
		typeSearch: 'Hledat',
		typeTel: 'Telefonní číslo',
		typeUrl: 'URL'
	}
} );
