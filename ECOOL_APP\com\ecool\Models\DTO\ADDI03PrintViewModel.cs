﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ADDI03PrintViewModel
    {

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("SCHOOL_NO")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///GRADE
        ///Summary
        [DisplayName("GRADE")]
        public short? GRADE { get; set; }

        ///Summary
        ///CLASS_NO
        ///Summary
        [DisplayName("CLASS_NO")]
        public string CLASS_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("USER_NO")]
        public string USER_NO { get; set; }

        ///Summary
        ///NAME
        ///Summary
        [DisplayName("NAME")]
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        [DisplayName("SNAME")]
        public string SNAME { get; set; }

        ///Summary
        ///SEAT_NO
        ///Summary
        [DisplayName("SEAT_NO")]
        public string SEAT_NO { get; set; }


        public DateTime? PASS_DATE { get; set; }
        

        public Dictionary<string, int> IsReadBooK { get; set; }

        public Dictionary<string, int> IsReadBooKName { get; set; }
    }

}
