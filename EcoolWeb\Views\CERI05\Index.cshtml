﻿@using EcoolWeb.Models;
@{
    ViewBag.Title = ViewBag.Panel_Title;

    var user = UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_Ceri02Menu", "CERI02", new { NowAction = "CERI05" });
}
<style>

    label.text-primary {
        font-size:medium;
    }
    ol.text-primary {
        font-size: medium;
    }
</style>
@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <label class="text-primary"><b> 請選擇「功能類型」</b></label>
            <br /><br />
                  <div class="text-center">
                      @if (HRMT24_ENUM.CheckQAdmin(user))
                      {
                          <button type="button" class="btn btn-default btn-lg" onclick="onSchoolIndex()">全校批次補登</button>
                          <button type="button" class="btn btn-default btn-lg" onclick="onSingleSchoolIndex()">單項目批次補登</button>
                      }
                      <button type="button" class="btn btn-default btn-lg" onclick="onClassIndex()">班級補登</button>
                      <button type="button" class="btn btn-default btn-lg" onclick="onPersonalIndex()">個人補登</button>


                  </div>

            <br /><br />
                  <div class="form-group "　>
                      @*<label class="text-primary">
           說明:

        </label> <br /> <br />*@
                      <label class="text-primary"　> 說明: <br />一、第一次使用時，護照裡面的細項，預設是全部空白(全部沒打勾)，如果需要</label>
                      <br />  <label class="text-primary">

                      </label>
                      <br />


                      @if (HRMT24_ENUM.CheckQAdmin(user))
                      {

                          <ol class="text-primary"　style="font-size:15px">

                              <li>


                                  「全校批次補登」：<br />
                                  (1)自動補齊所有學生，現在年級的以往資料，例如五年級學生，一到四年級各細項自動全部完成<br />
                                  (2)只註記打勾，<font style="color:red">沒有給酷幣點數</font>。<br />



                              </li>
                              <li>


                                  「單項目批次補登」：<br />
                                  (1) 自動補齊該項目的勾勾。<br />
                                  (2)只註記打勾，<font style="color:red">沒有給酷幣點數</font>。<br />



                              </li>
                          </ol>



                      }

                      else
                      {

                          <ol class="text-primary"　>
                              <li>
                                  若選擇補登個人：<br />
                                  (1)通常是用在轉學生或之前沒完成的。<br />
                                  (2)補登除註記打勾外，會<font style="color:red">額外給貴校設定的酷幣點數</font>。<br />
                              </li>
                          </ol>


                      }

                      <label class="text-primary"　> 二、如果已經啟用，要補登之前上學期以前的勾勾，可以用「班級補登」或</label>
                      <br />  <label class="text-primary"　>
                          「個人補登」。
                      </label>
                      <br />
                      <ol class="text-primary"　>

                          (1)	使用時機： A學生之前沒達標，現在已經達標，B或者轉學生。
                          <br />

                          (2) 除註記打勾外，
                          <font style="color:red">有給酷幣點數(一個勾勾多少點是依據貴校設定值)。</font>
                      </ol>
                      <label class="text-primary"　>三、特殊護照(文字)，是沒有全校補登的。</label>
                  </div>
           
         
        </div>
    </div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function onSchoolIndex() {
            var OKSWTCH = confirm("您確定要進行全校補登嗎？按確定後就會「把前學年的達成指標」通通打勾喔。只會打勾，不會給點。");
            
            if (OKSWTCH == true) {
                var OK = confirm("再確認一次，這個動作會進行全校補登，會將本學年之前的指標通通變成「達成」，按確定就一鍵執行。");
                if (OK == true) {
                 @*$(targetFormID).attr("action", "@Url.Action("Index2", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();*@
                    $(targetFormID).attr("action", "@Url.Action("SchoolIndex", (string)ViewBag.BRE_NO)")
                    $(targetFormID).submit()
                }
            }
        }

        function onSingleSchoolIndex() {

            var OK = confirm("將進行單項目的批次補登，只打勾，不會給點，請在下個畫面選擇要補登的項目。")

            if (OK==true)
            {
                 $(targetFormID).attr("action", "@Url.Action("Index2", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
             
          }
        }
        function onClassIndex() {

               $(targetFormID).attr("action", "@Url.Action("Index2", "CERI04" ,new { FirstPage=true, SouBre_NO= "CERI05" } )")
                    $(targetFormID).submit()

        }
        function onPersonalIndex() {

            var OK = confirm("將進行個人補登，會打勾也會給點，請在下一個畫面選擇要補登的學生。")
            if (OK == true) {
                $(targetFormID).attr("action", "@Url.Action("PersonalIndex", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }
    </script>
}