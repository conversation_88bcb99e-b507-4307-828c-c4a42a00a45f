﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'zh', {
	access: '腳本存取',
	accessAlways: '永遠',
	accessNever: '從不',
	accessSameDomain: '相同網域',
	alignAbsBottom: '絕對下方',
	alignAbsMiddle: '絕對置中',
	alignBaseline: '基準線',
	alignTextTop: '上層文字',
	bgcolor: '背景顏色',
	chkFull: '允許全螢幕',
	chkLoop: '重複播放',
	chkMenu: '啟用 Flash 選單',
	chkPlay: '自動播放',
	flashvars: 'Flash 變數',
	hSpace: 'HSpace',
	properties: 'Flash 屬性​​',
	propertiesTab: '屬性',
	quality: '品質',
	qualityAutoHigh: '自動高',
	qualityAutoLow: '自動低',
	qualityBest: '最佳',
	qualityHigh: '高',
	qualityLow: '低',
	qualityMedium: '中',
	scale: '縮放比例',
	scaleAll: '全部顯示',
	scaleFit: '最適化',
	scaleNoBorder: '無框線',
	title: 'Flash 屬性​​',
	vSpace: 'VSpace',
	validateHSpace: 'HSpace 必須為數字。',
	validateSrc: 'URL 不可為空白。',
	validateVSpace: 'VSpace  必須為數字。',
	windowMode: '視窗模式',
	windowModeOpaque: '不透明',
	windowModeTransparent: '透明',
	windowModeWindow: '視窗'
} );
