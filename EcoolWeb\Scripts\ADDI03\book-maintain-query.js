// JavaScript for ADDI03 BOOK_MAINTAINQuery page - 護照閱讀-書單維護一覽表
$(document).ready(function() {
    // 書單維護一覽表模組
    const bookMaintainQuery = {
        init: function() {
            this.bindEvents();
            this.enhanceUserExperience();
            this.addConfirmationDialogs();
            this.addTableEnhancements();
        },

        bindEvents: function() {
            // 綁定刪除按鈕事件
            $('.btn:contains("刪除")').on('click', this.handleDelete.bind(this));
            
            // 綁定維護按鈕事件
            $('.btn:contains("維護")').on('click', this.handleMaintain.bind(this));
            
            // 綁定新增按鈕事件
            $('.btn:contains("新增書單")').on('click', this.handleAdd.bind(this));
            
            // 綁定回到頂部按鈕
            $('a[href="#top"]').on('click', this.handleGoToTop.bind(this));
        },

        handleDelete: function(event) {
            event.preventDefault();
            
            try {
                const $button = $(event.target);
                const $row = $button.closest('tr');
                const bookId = $row.find('td:nth-child(2)').text().trim();
                const bookName = $row.find('td:nth-child(3)').text().trim();
                const grade = $row.find('td:nth-child(1)').text().trim();
                
                // 顯示確認對話框
                const confirmMessage = `確定要刪除以下書籍嗎？\n\n` +
                                     `年級：${grade}\n` +
                                     `書本編號：${bookId}\n` +
                                     `書名：${bookName}\n\n` +
                                     `注意：刪除後將無法復原！`;
                
                if (confirm(confirmMessage)) {
                    // 顯示載入狀態
                    $button.prop('disabled', true).text('刪除中...');
                    
                    // 獲取原始連結並導航
                    const originalHref = $button.attr('href');
                    if (originalHref) {
                        window.location.href = originalHref;
                    } else {
                        // 如果沒有href，嘗試從ActionLink獲取
                        const deleteUrl = window.ADDI03_BOOK_MAINTAIN_QUERY_URLS.bookMaintain + 
                                        '?Book_Status=DEL&BOOK_ID=' + encodeURIComponent(bookId);
                        window.location.href = deleteUrl;
                    }
                } else {
                    console.log('用戶取消刪除操作');
                }
            } catch (error) {
                console.error('刪除操作時發生錯誤:', error);
                this.showMessage('刪除操作時發生錯誤，請稍後再試', 'error');
            }
        },

        handleMaintain: function(event) {
            try {
                const $button = $(event.target);
                const $row = $button.closest('tr');
                const bookId = $row.find('td:nth-child(2)').text().trim();
                const bookName = $row.find('td:nth-child(3)').text().trim();
                
                console.log('維護書籍:', { bookId, bookName });
                
                // 顯示載入狀態
                $button.prop('disabled', true).text('載入中...');
                
                // 短暫延遲後恢復按鈕狀態（如果頁面沒有跳轉）
                setTimeout(() => {
                    $button.prop('disabled', false).text('維護');
                }, 3000);
                
                // 讓原始連結正常工作
                return true;
            } catch (error) {
                console.error('維護操作時發生錯誤:', error);
                this.showMessage('維護操作時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        handleAdd: function(event) {
            try {
                const $button = $(event.target);
                
                console.log('新增書單');
                
                // 顯示載入狀態
                $button.prop('disabled', true).text('載入中...');
                
                // 短暫延遲後恢復按鈕狀態（如果頁面沒有跳轉）
                setTimeout(() => {
                    $button.prop('disabled', false).text('新增書單');
                }, 3000);
                
                // 讓原始連結正常工作
                return true;
            } catch (error) {
                console.error('新增操作時發生錯誤:', error);
                this.showMessage('新增操作時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        handleGoToTop: function(event) {
            event.preventDefault();
            
            try {
                // 平滑滾動到頂部
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
                
                console.log('滾動到頂部');
            } catch (error) {
                console.error('滾動到頂部時發生錯誤:', error);
                // 如果動畫失敗，使用原生方法
                window.scrollTo(0, 0);
            }
        },

        addConfirmationDialogs: function() {
            // 為刪除按鈕添加視覺提示
            $('.btn:contains("刪除")').each(function() {
                $(this).addClass('btn-danger').attr('title', '點擊刪除此書籍（需要確認）');
            });
            
            // 為維護按鈕添加提示
            $('.btn:contains("維護")').each(function() {
                $(this).addClass('btn-primary').attr('title', '點擊維護此書籍');
            });
            
            // 為新增按鈕添加提示
            $('.btn:contains("新增書單")').each(function() {
                $(this).attr('title', '點擊新增新的書籍');
            });
        },

        addTableEnhancements: function() {
            // 添加表格行hover效果
            $('.table-ecool tbody tr').on('mouseenter', function() {
                $(this).addClass('table-row-hover');
            }).on('mouseleave', function() {
                $(this).removeClass('table-row-hover');
            });
            
            // 添加表格排序提示（如果需要的話）
            $('.table-ecool thead th').each(function() {
                $(this).attr('title', '表格欄位：' + $(this).text());
            });
            
            // 統計書籍數量
            this.addBookStatistics();
        },

        addBookStatistics: function() {
            try {
                const $rows = $('.table-ecool tbody tr:not(:contains("查無資料"))');
                const totalBooks = $rows.length;
                
                if (totalBooks > 0) {
                    // 統計各年級書籍數量
                    const gradeStats = {};
                    $rows.each(function() {
                        const grade = $(this).find('td:first').text().trim();
                        gradeStats[grade] = (gradeStats[grade] || 0) + 1;
                    });
                    
                    // 創建統計信息
                    let statsHtml = `<div class="alert alert-info book-statistics" style="margin-top: 15px;">
                        <h5><i class="fa fa-bar-chart"></i> 書籍統計</h5>
                        <p><strong>總書籍數：${totalBooks} 本</strong></p>
                        <div class="row">`;
                    
                    Object.keys(gradeStats).forEach(grade => {
                        statsHtml += `<div class="col-md-2 col-sm-4 col-xs-6">
                            <span class="label label-default">${grade}：${gradeStats[grade]} 本</span>
                        </div>`;
                    });
                    
                    statsHtml += `</div></div>`;
                    
                    // 插入統計信息
                    $('.table-responsive').after(statsHtml);
                }
            } catch (error) {
                console.error('添加書籍統計時發生錯誤:', error);
            }
        },

        enhanceUserExperience: function() {
            // 添加載入動畫CSS
            this.addLoadingStyles();
            
            // 添加按鈕hover效果
            $('.btn').on('mouseenter', function() {
                $(this).addClass('btn-hover-effect');
            }).on('mouseleave', function() {
                $(this).removeClass('btn-hover-effect');
            });
            
            // 添加鍵盤快捷鍵支援
            $(document).on('keydown', function(e) {
                // Ctrl + N 新增書單
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    $('.btn:contains("新增書單")').click();
                }
                
                // ESC 鍵滾動到頂部
                if (e.key === 'Escape') {
                    $('html, body').animate({ scrollTop: 0 }, 300);
                }
            });
            
            // 添加工具提示
            this.addTooltips();
        },

        addLoadingStyles: function() {
            // 動態添加CSS樣式
            const styles = `
                <style>
                .btn-hover-effect {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                    transition: all 0.2s ease;
                }
                
                .table-row-hover {
                    background-color: #f5f5f5 !important;
                    transition: background-color 0.2s ease;
                }
                
                .btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
                
                .book-statistics .label {
                    display: inline-block;
                    margin: 2px;
                    padding: 4px 8px;
                    font-size: 12px;
                }
                
                .alert-message {
                    animation: slideDown 0.3s ease;
                }
                
                @keyframes slideDown {
                    from { opacity: 0; transform: translateY(-10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                </style>
            `;
            
            $('head').append(styles);
        },

        addTooltips: function() {
            // 為說明文字添加更好的樣式
            $('.text-primary').each(function() {
                $(this).css({
                    'font-weight': 'bold',
                    'margin': '5px 0'
                });
            });
            
            // 為表格添加響應式提示
            if ($(window).width() < 768) {
                $('.table-responsive').before(
                    '<div class="alert alert-warning"><small>' +
                    '<i class="fa fa-mobile"></i> 提示：在手機上可以左右滑動查看完整表格' +
                    '</small></div>'
                );
            }
        },

        showMessage: function(message, type = 'info') {
            // 移除現有訊息
            $('.alert-message').remove();
            
            // 添加新訊息
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 'alert-info';
            
            const messageHtml = `
                <div class="alert ${alertClass} alert-message" style="margin: 10px 0;">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    ${message}
                </div>
            `;
            
            $('.form-group').first().after(messageHtml);
            
            // 3秒後自動隱藏
            setTimeout(() => {
                $('.alert-message').fadeOut();
            }, 3000);
        }
    };

    // 初始化模組
    bookMaintainQuery.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});
