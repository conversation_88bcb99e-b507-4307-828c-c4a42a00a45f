/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Size2={directory:"Size2/Regular",family:"NeoEulerMathJax_Size2",testString:"\u00A0\u2016\u2044\u2215\u221A\u2223\u2225\u2227\u2228\u2229\u222A\u228E\u22C0\u22C1\u22C2",32:[0,0,333,0,0],40:[1599,199,596,180,574],41:[1599,199,595,22,415],47:[1599,200,811,53,759],91:[1674,125,472,226,453],92:[1599,200,811,53,759],93:[1674,125,472,18,245],123:[1599,200,667,119,547],124:[1897,208,213,86,126],125:[1599,200,667,119,547],160:[0,0,333,0,0],8214:[1897,208,403,86,316],8260:[1599,200,811,53,759],8725:[1599,200,811,53,759],8730:[1800,1,1000,110,1024],8739:[1897,208,213,86,126],8741:[1297,208,403,86,316],8743:[1128,267,1549,56,1492],8744:[1069,326,1549,56,1492],8745:[1359,-1,1110,56,1053],8746:[1317,41,1110,56,1053],8846:[1317,41,1110,56,1053],8896:[1128,267,1549,56,1492],8897:[1069,326,1549,56,1492],8898:[1359,-1,1110,56,1053],8899:[1317,41,1110,56,1053],8968:[1599,200,527,226,509],8969:[1599,200,527,18,301],8970:[1599,200,527,226,509],8971:[1599,200,527,18,301],9001:[1536,234,629,109,520],9002:[1536,234,693,89,500],9180:[794,-414,1911,56,1855],9181:[144,236,1911,56,1855],9182:[912,-484,1911,56,1855],9183:[70,358,1911,56,1855],10216:[939,237,501,95,392],10217:[939,237,568,79,375]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Size2"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size2/Regular/Main.js"]);
