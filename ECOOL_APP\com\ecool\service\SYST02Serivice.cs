﻿
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace com.ecool.service
{
    public  class SYST02Serivice : ServiceBase
    {

        /// <summary>
        /// Description : 查詢多國語系類別
        /// </summary>
        /// <returns></returns>
        public static List<uSYST02> USP_SYST02_QUERY(string SCHOOL_NO)
        {
            List<uSYST02> list_data = new List<uSYST02>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {

                string sSQL;

                
                sSQL=" SELECT T.LANGUAGE_ID,T.LANGUAGE_NAME,T.FUN_DESC  ";
                sSQL = sSQL + ",ROW_NUMBER() OVER(ORDER BY T.DF_COUNT DESC,T.LANGUAGE_ID)-1 ORDER_BY  ";
                sSQL = sSQL + "FROM   ";
                sSQL = sSQL + "(  ";
                sSQL = sSQL + " SELECT  A.LANGUAGE_ID,A.LANGUAGE_NAME,A.FUN_DESC   ";
                sSQL = sSQL + "  ,(SELECT COUNT(*)   ";
                sSQL = sSQL + "    FROM SYST01 B (NOLOCK)   ";
                sSQL = sSQL + "    JOIN BDMT01 C (NOLOCK) ON B.SYS_ID=C.SYS_ID   ";
                sSQL = sSQL + "    WHERE A.LANGUAGE_ID=B.LANGUAGE_DEF  ";
                sSQL = sSQL + "    AND C.SCHOOL_NO='" + SCHOOL_NO + "'  ";
                sSQL = sSQL + "   ) AS DF_COUNT ";
                sSQL = sSQL + "	FROM SYST02 A (NOLOCK) ";
                sSQL = sSQL + "	WHERE 1=1 ";
                sSQL = sSQL + "	GROUP BY A.LANGUAGE_ID,A.LANGUAGE_NAME,A.FUN_DESC ";
                sSQL = sSQL + " ) AS T ";


                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sSQL);
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uSYST02()
                    {
                        LANGUAGE_ID = dr["LANGUAGE_ID"].ToString(),
                        LANGUAGE_NAME = dr["LANGUAGE_NAME"].ToString(),
                        FUN_DESC = dr["FUN_DESC"].ToString(),
                        ORDER_BY = Int32.Parse(dr["ORDER_BY"].ToString()),
                    });
                }
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;

        }


        /// <summary>
        /// 取得多國語系
        /// </summary>
        /// <param name="SelectedVal">預設的選項</param>
        /// <returns></returns>
        public static List<SelectListItem> GetLANGUAGE(string SelectedVal, string SCHOOL_NO)
        {
            List<SelectListItem> SELECT_ITEMS = new List<SelectListItem>();

            var dt = SYST02Serivice.USP_SYST02_QUERY(SCHOOL_NO);

            foreach (var item in dt)
            {
                SelectListItem NewDATA = new SelectListItem();
                NewDATA.Text = item.LANGUAGE_ID;
                NewDATA.Value = item.LANGUAGE_NAME;

                if (string.IsNullOrWhiteSpace(SelectedVal) == false)
                {
                    if (item.LANGUAGE_ID == SelectedVal)
                    {
                        NewDATA.Selected = true;
                    }
                }

                SELECT_ITEMS.Add(NewDATA);
            }

            return SELECT_ITEMS;
        }
    }
}
