﻿using ECOOL_APP.com.ecool.util;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Xml;

namespace com.ecool.sqlConnection
{
    public class sqlConnection
    {
        private string connection_string = "";
        private bool isDBTransaction = false;
        private bool isShowSql = false;

        /// <summary>
        /// 各校SqlConnection
        /// </summary>
        private SqlConnection conn = null;

        /// <summary>
        /// 各校SqlTransaction
        /// </summary>
        private SqlTransaction transaction = null;

        /// <summary>
        /// SySDb SqlConnection
        /// </summary>
        private SqlConnection Sysconn = null;

        ///// <summary>
        ///// SySDb SqlTransaction
        ///// </summary>
        //private SqlTransaction Systransaction = null;

        private void showSql(string sql)
        {
            if (isShowSql)
            {
                System.Diagnostics.Debug.WriteLine("[" + DateTime.Now + "][SHOW-SQL]" + sql);
            }
        }

        private static string findConfigFileURL(string file_name)
        {
            String code_base = new sqlConnection().GetType().Assembly.CodeBase;
            int last_slash = code_base.LastIndexOf("/");
            String file_path = code_base.Substring(0, last_slash + 1);
            return file_path + file_name;
        }

        public String getDefaultConnectionString()
        {
            Hashtable hmConfiguration = getConnection_string(null);

            string ConnString = "Data Source=" + hmConfiguration["server"] + ";Initial Catalog=" + hmConfiguration["database"] + ";Persist Security Info=True;User ID=" + hmConfiguration["username"] + ";Password=" + hmConfiguration["password"] + "";

            return ConnString;
        }

        public static Hashtable getConnection_string(string occAlias)
        {
            string file_name = "fastdao_config.xml";
            string url = "";
            Hashtable context = null;
            try
            {
                url = findConfigFileURL(file_name);
                context = _get(url, occAlias);
            }
            catch (Exception e)
            {
                throw new Exception("Can not get config context from config files : " + url + " -> fastdao_config.xml");
            }
            return context;
        }

        private static Hashtable _get(string file_url, string occAlias)
        {
            return retrieveXML(file_url, occAlias);
        }

        private static Hashtable retrieveXML(string file_url, string occAlias)
        {
            Hashtable hmConfiguration = null;

            int iDefaultConnCount = 0;
            int iMatchedName = 0;
            XmlTextReader textReader = new XmlTextReader(file_url);
            textReader.Read();
            while (textReader.Read())
            {
                bool isRetrieve = false;
                textReader.MoveToElement();

                if ("ole-connection-configuration".Equals(textReader.Name))
                {
                    string occ_alias = textReader.GetAttribute("occ-alias");
                    string default_connection = textReader.GetAttribute("default-connection");
                    if ("true".Equals(default_connection))
                    {
                        iDefaultConnCount++;
                    }

                    if (occAlias == null || "".Equals(occAlias))
                    {
                        if ("true".Equals(default_connection))
                        {
                            iMatchedName++;
                            isRetrieve = true;
                        }
                    }
                    else if (occ_alias.Equals(occAlias))
                    {
                        iMatchedName++;
                        isRetrieve = true;
                    }
                    if (isRetrieve)
                    {
                        hmConfiguration = new Hashtable();
                        hmConfiguration.Add("occ-alias", textReader.GetAttribute("occ-alias"));
                        hmConfiguration.Add("default-connection", textReader.GetAttribute("default-connection"));
                        hmConfiguration.Add("server", textReader.GetAttribute("server"));
                        hmConfiguration.Add("username", textReader.GetAttribute("username"));
                        hmConfiguration.Add("password", textReader.GetAttribute("password"));
                        hmConfiguration.Add("database", textReader.GetAttribute("database"));
                        hmConfiguration.Add("integrated-security", textReader.GetAttribute("integrated-security"));
                        hmConfiguration.Add("debug", textReader.GetAttribute("debug"));
                        hmConfiguration.Add("show-sql", textReader.GetAttribute("show-sql"));
                        hmConfiguration.Add("password-base64-encoded", textReader.GetAttribute("password-base64-encoded"));
                        hmConfiguration.Add("username-base64-encoded", textReader.GetAttribute("username-base64-encoded"));
                    }
                }
            }
            textReader.Close();
            if (iDefaultConnCount > 1)
            {
                throw new Exception("There are duplicated default-connection in " + file_url);
            }
            if (iMatchedName == 0)
            {
                throw new Exception("Can not find occ-alias named '" + occAlias + "' in " + file_url);
            }
            else if (iMatchedName > 1)
            {
                throw new Exception("There are duplicated occ-alias named '" + occAlias + "' in " + file_url);
            }

            return hmConfiguration;
        }

        /// <summary>
        ///  抓取 各校 連線字串
        /// </summary>
        /// <returns></returns>
        public SqlConnection getConnection4Query()
        {
            SqlConnection _conn = null;
            try
            {
                Hashtable hmConfiguration = getConnection_string(null);
                //未來需要改寫從BDMT01(學校資料) JOIN SYST01 取出各校連線字串

                _conn = new SqlConnection();
                _conn.ConnectionString = connection_string = "Data Source=" + hmConfiguration["server"] + ";Initial Catalog=" + hmConfiguration["database"] + ";Persist Security Info=True;User ID=" + hmConfiguration["username"] + ";Password=" + hmConfiguration["password"] + " ;Max Pool Size=30000;Connection Timeout=40000 ";
                _conn.Open();
            }
            catch (Exception e)
            {
                //debug("Exception in getConnection():" + e);
                throw e;
            }
            return _conn;
        }

        /// <summary>
        /// 抓取 SysDb 連線字串
        /// </summary>
        /// <returns></returns>
        public SqlConnection getConnection4System()
        {
            SqlConnection _conn = null;
            try
            {
                Hashtable hmConfiguration = getConnection_string(null);
                _conn = new SqlConnection();
                //未來要改寫抓SysDb 連線字串
                _conn.ConnectionString = connection_string = "Data Source=" + hmConfiguration["server"] + ";Initial Catalog=" + hmConfiguration["database"] + ";Persist Security Info=True;User ID=" + hmConfiguration["username"] + ";Password=" + hmConfiguration["password"] + "";
                _conn.Open();
            }
            catch (Exception e)
            {
                //debug("Exception in getConnection():" + e);
                throw e;
            }
            return _conn;
        }

        /// <summary>
        /// 使用各校連線字串，傳入t-sql 來更新資料
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public int execute(String sql)
        {
            this.showSql(sql);
            SqlConnection _conn = null;
            SqlCommand cmd = null;
            int rows = 0;

            try
            {
                _conn = conn ?? getConnection4Query();

                cmd = new SqlCommand(sql, _conn);
                if (isDBTransaction)
                {
                    cmd.Transaction = transaction;
                }
                rows = cmd.ExecuteNonQuery();
            }
            catch (Exception e)
            {
                //debug("Exception in update():" + e);
                rows = -1;
                throw e;
            }
            finally
            {
                closeCommand(cmd);
                closeConnection(_conn);
            }
            return rows;
        }

        /// <summary>
        /// 使用各校連線字串，傳入t-sql抓取資料 放進 Hashtable
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public List<Hashtable> executeQueryBySQLToHashtableList(string sql)
        {
            this.showSql(sql);
            safeSqlForQuery(sql);
            List<Hashtable> listData = new List<Hashtable>();
            SqlConnection conn = null;
            SqlDataAdapter dataAdapter = null;

            try
            {
                conn = getConnection4Query();
                dataAdapter = new SqlDataAdapter(sql, conn);
                DataTable dtResult = new DataTable();
                dataAdapter.Fill(dtResult);
                foreach (DataRow drRow in dtResult.Rows)
                {
                    Hashtable data = new Hashtable();
                    foreach (DataColumn column in dtResult.Columns)
                    {
                        string col_name = column.ColumnName;
                        object col_value = drRow[col_name];
                        data.Add(col_name.ToUpper(), col_value);
                        //debug("[" + col_name.ToUpper() + "]" + col_value);
                    }
                    listData.Add(data);
                }
            }
            catch (Exception e)
            {
                //debug("Exception:" + e);
                throw e;
            }
            finally
            {
                closeAdapter(dataAdapter);
                closeConnection4Query(conn);
            }
            return listData;
        }

        /// <summary>
        /// 使用 SysDb 連線字串, 傳入t-sql 抓取資料 放進 DataTable
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataTable SysDbexecuteQueryByDataTableList(string sql)
        {
            safeSqlForQuery(sql);
            SqlConnection _conn = null;
            SqlDataAdapter dataAdapter = null;
            DataTable dtResult;
            try
            {
                _conn = Sysconn ?? getConnection4System();
                dataAdapter = new SqlDataAdapter(sql, _conn);
                dtResult = new DataTable();
                dataAdapter.Fill(dtResult);
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                closeAdapter(dataAdapter);

                if (Sysconn == null)
                {
                    closeConnection4Query(_conn);
                }
            }
            return dtResult;
        }

        /// <summary>
        /// 使用各校連線字串, 傳入t-sql 抓取資料 放進 DataTable
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataTable executeQueryByDataTableList(string sql)
        {
            safeSqlForQuery(sql);
            SqlConnection _conn = null;
            SqlDataAdapter dataAdapter = null;
            DataTable dtResult;
            try
            {
                _conn = conn ?? getConnection4Query();

                dataAdapter = new SqlDataAdapter(sql, _conn);
                dataAdapter.SelectCommand.CommandTimeout = 3000;
                dtResult = new DataTable();
                dataAdapter.Fill(dtResult);
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                closeAdapter(dataAdapter);

                if (conn == null)
                {
                    closeConnection4Query(_conn);
                }
            }
            return dtResult;
        }

        /// <summary>
        /// 使用傳進去的連線字串, 傳入t-sql抓取資料 放進 DataTable
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="conn"></param>
        /// <returns></returns>
        public DataTable MyDbexecuteQueryByDataTableList(string sql, SqlConnection conn)
        {
            safeSqlForQuery(sql);
            SqlDataAdapter dataAdapter = null;
            DataTable dtResult;
            try
            {
                dataAdapter = new SqlDataAdapter(sql, conn);
                dtResult = new DataTable();
                dataAdapter.Fill(dtResult);
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                closeAdapter(dataAdapter);
            }

            return dtResult;
        }

        public DataTable executeQueryGetCash(string SCHOOL_NO, ref int Count) {
            string sp_name = string.Empty;
            sp_name = "sp_GetCash_AllSchool";
            SqlConnection _conn = null;
            SqlDataReader Result = null;
            _conn = conn ?? getConnection4Query();

            SqlCommand cmd = new SqlCommand("dbo." + sp_name, _conn);

            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("@SCHOOL_NO", System.Data.SqlDbType.NVarChar, 8).Value = SCHOOL_NO;
            
            try
            {

                Result = cmd.ExecuteReader();
                DataTable dt = new DataTable();
                dt.Load(Result);
                Count = 1;
                Result.Close();
                return dt;
            }
            catch (Exception ex)
            {
                Count = 0;
                throw ex;
            }
            finally
            {
                if (conn == null)
                {
                    closeConnection4Query(_conn);
                }
            }


        }
        /// <summary>
        ///  呼叫分頁SP return SqlDataReader 使用各校連線字串
        /// </summary>
        /// <param name="Page">目前頁次</param>
        /// <param name="Pagesize">一頁幾筆</param>
        /// <param name="CommandText">T-SQL,PS 不可有ORDER BY </param>
        /// <param name="CommandCountText">計算總比數T-SQL,可等於CommandText ,會排序有ORDER BY </param>
        /// <param name="OrderbyText">ORDER BY 語法</param>
        /// <param name="Count">回傳總筆數</param>
        /// <param name="ErrorMsg">回傳錯務訊息</param>
        /// <returns></returns>
        public DataTable executeQueryBSqlDataReaderOrderListPage(int Page, int Pagesize, string CommandText, string CommandCountText, string OrderbyText, ref int Count, ref string ErrorMsg,string Orderby2Text)
        {
            safeSqlForQuery(CommandText);
            safeSqlForQuery(CommandCountText);

            string sp_name = string.Empty;
           // SqlConnection conn = null;
            SqlDataReader Result = null;

            sp_name = "sp_QueryListPageOrderby";
            SqlConnection _conn = null;

            _conn = conn ?? getConnection4Query();

            SqlCommand cmd = new SqlCommand("dbo." + sp_name, _conn);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("@CommandText", System.Data.SqlDbType.NVarChar, -1).Value = CommandText;
            cmd.Parameters.Add("@CommandCountText", System.Data.SqlDbType.NVarChar, -1).Value = CommandCountText;
            cmd.Parameters.Add("@OrderbyText", System.Data.SqlDbType.NVarChar, -1).Value = OrderbyText;
            cmd.Parameters.Add("@Orderby2Text", System.Data.SqlDbType.NVarChar, -1).Value = Orderby2Text;
            cmd.Parameters.Add("@Page", System.Data.SqlDbType.Int).Value = Page;
            cmd.Parameters.Add("@Pagesize", System.Data.SqlDbType.Int).Value = Pagesize;

            SqlParameter retValParam = cmd.Parameters.Add("@Count", System.Data.SqlDbType.Int);
            retValParam.Direction = ParameterDirection.Output;

            string sql = "Exec dbo.sp_QueryListPageOrderby '" + CommandText + "','" + CommandCountText + "','" + OrderbyText + "'," + Page + "," + Pagesize + "," + Count + ",'" + ErrorMsg + "' ";

            try
            {
                //DataSet ds = new DataSet();
                //ds.Clear();
                //SqlDataAdapter adapter = new SqlDataAdapter();
                //adapter.SelectCommand = cmd;
                //adapter.Fill(ds);
                //DataTable dt = ds.Tables[0];

                Result = cmd.ExecuteReader();
                DataTable dt = new DataTable();
                dt.Load(Result);
              
                Result.Close();
                Count = Convert.ToInt32(retValParam.Value);
                
                return dt;
            }
            catch (Exception ex)
            {
                Count = 0;
                throw ex;
            }
            finally
            {
                if (conn == null)
                {
                    closeConnection4Query(_conn);
                }
            }

        }
            /// <summary>
            ///  呼叫分頁SP return SqlDataReader 使用各校連線字串
            /// </summary>
            /// <param name="Page">目前頁次</param>
            /// <param name="Pagesize">一頁幾筆</param>
            /// <param name="CommandText">T-SQL,PS 不可有ORDER BY </param>
            /// <param name="CommandCountText">計算總比數T-SQL,可等於CommandText ,PS 不可有ORDER BY </param>
            /// <param name="OrderbyText">ORDER BY 語法</param>
            /// <param name="Count">回傳總筆數</param>
            /// <param name="ErrorMsg">回傳錯務訊息</param>
            /// <returns></returns>
            public DataTable executeQueryBSqlDataReaderListPage(int Page, int Pagesize, string CommandText, string CommandCountText, string OrderbyText, ref int Count, ref string ErrorMsg)
        {
            safeSqlForQuery(CommandText);
            safeSqlForQuery(CommandCountText);

            string sp_name = string.Empty;
            SqlConnection conn = null;
            SqlDataReader Result = null;

            sp_name = "sp_QueryListPage";
            SqlConnection _conn = null;

            _conn = conn ?? getConnection4Query();

            SqlCommand cmd = new SqlCommand("dbo." + sp_name, _conn);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("@CommandText", System.Data.SqlDbType.NVarChar, -1).Value = CommandText;
            cmd.Parameters.Add("@CommandCountText", System.Data.SqlDbType.NVarChar, -1).Value = CommandCountText;
            cmd.Parameters.Add("@OrderbyText", System.Data.SqlDbType.NVarChar, -1).Value = OrderbyText;
            cmd.Parameters.Add("@Page", System.Data.SqlDbType.Int).Value = Page;
            cmd.Parameters.Add("@Pagesize", System.Data.SqlDbType.Int).Value = Pagesize;

            SqlParameter retValParam = cmd.Parameters.Add("@Count", System.Data.SqlDbType.Int);
            retValParam.Direction = ParameterDirection.Output;

            string sql = "Exec dbo.sp_QueryListPage '" + CommandText + "','" + CommandCountText + "','" + OrderbyText + "'," + Page + "," + Pagesize + "," + Count + ",'" + ErrorMsg + "' ";
            cmd.CommandTimeout = 6000;
            try
            {
                //DataSet ds = new DataSet();
                //ds.Clear();
                //SqlDataAdapter adapter = new SqlDataAdapter();
                //adapter.SelectCommand = cmd;
                //adapter.Fill(ds);
                //DataTable dt = ds.Tables[0];

                Result = cmd.ExecuteReader();
                
                DataTable dt = new DataTable();
                dt.Load(Result);
                Result.Close();
                Count = Convert.ToInt32(retValParam.Value);
                return dt;
            }
            catch (Exception ex)
            {
                Count = 0;
                throw ex;
            }
            finally
            {
                if (conn == null)
                {
                    closeConnection4Query(_conn);
                }
            }
        }

        private void closeAdapter(SqlDataAdapter adpater)
        {
            try
            {
                if (adpater != null)
                {
                    adpater.Dispose();
                }
            }
            catch (Exception e)
            {
                //debug("Exception in closeAdapter():" + e);
            }
        }

        public void closeConnection4Query(SqlConnection _conn)
        {
            try
            {
                if (_conn != null)
                {
                    _conn.Close();
                }
            }
            catch (Exception e)
            {
                //debug("Exception in closeConnection():" + e);
            }

            try
            {
                if (_conn != null)
                {
                    _conn.Dispose();
                }
            }
            catch (Exception e)
            {
                //debug("Exception in closeConnection4Query():" + e);
            }
        }

        private void closeCommand(SqlCommand cmd)
        {
            try
            {
                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }
            catch (Exception e)
            {
                //debug("Exception in closeCommand():" + e);
            }
        }

        public void closeConnection(SqlConnection _conn)
        {
            if (isDBTransaction)
            {
                return;
            }
            try
            {
                if (_conn != null)
                {
                    _conn.Close();
                }
            }
            catch (Exception e)
            {
                //debug("Exception in closeConnection():" + e);
            }
            try
            {
                if (_conn != null)
                {
                    _conn.Dispose();
                }
            }
            catch (Exception e)
            {
                //debug("Exception in closeConnection():" + e);
            }
        }

        private void safeSqlForQuery(String sql)
        {
            if (sql == null)
            {
                return;
            }
            if (sql.ToUpper().IndexOf("UPDATE ") != -1 ||
                sql.ToUpper().IndexOf("INSERT ") != -1 ||
                sql.ToUpper().IndexOf("EXEC ") != -1 ||
                sql.ToUpper().IndexOf("EXECUTE ") != -1)
            {
                throw new Exception("Invalid SQL in query command:" + sql);
            }
        }

        public string SQLString(System.DBNull sString)
        {
            return "";
        }

        public string SQLString(string sString)
        {
            string tmpString = "";

            if (Convert.IsDBNull(sString) == true)
            {
                return "";
            }
            else
            {
                foreach (char k in sString)
                {
                    if (k.ToString() == "'")
                    {
                        tmpString += k.ToString().Replace("'", "'+char(39)+'");
                    }
                    else if (k.ToString() == ",")
                    {
                        tmpString += k.ToString().Replace(",", "'+char(44)+'");
                    }
                    else if (k.ToString() == "(")
                    {
                        tmpString += k.ToString().Replace("(", "'+char(40)+'");
                    }
                    else if (k.ToString() == ")")
                    {
                        tmpString += k.ToString().Replace(")", "'+char(41)+'");
                    }
                    else
                    {
                        tmpString += k.ToString();
                    }
                }

                return tmpString;
            }
        }
    }
}