﻿@model BarcCodeMyCashLoginViewModel

<!DOCTYPE html>
<html lang="zh-Hant-TW">
<head>
    @{
        ViewBag.Title = "兌換獎品登入";
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

        ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

        ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        int iLockCount = (Session["LockCount"] != null) ? Convert.ToInt32(Session["LockCount"]) : 0;
        int iVlidatePasswordMaxCount = Convert.ToInt32(System.Web.Configuration.WebConfigurationManager.AppSettings["VlidatePasswordMaxCount"]);

    }
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @Styles.Render("~/Content/css")
    <link href="@Url.Content("~/Content/css/EzCss.css")?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss")" rel="stylesheet" />
    <link href="@Url.Content("~/Content/font-awesome/css/font-awesome.min.css")" rel="stylesheet" />
    <style type="text/css">
         body {
                background-image: url("@Url.Content("~/Content/img/web-01.png")");
                background-repeat: repeat;
            }
        .classInputCode {
            display: none
        }
    </style>
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script src="@Url.Content("~/Scripts/jquery.simple.timer.js")"></script>
</head>


<body>
    <div class="visible-xs">
        <!---手機 -->
        <nav class="navbar navbar-phone navbar-fixed-top" role="navigation">
            <span class="btn-logo">
                <img src="@Url.Content("~/Content/img/web-student_png-16.png")" class="img-responsive " alt="Responsive image" title="回台北e酷幣首頁" />
            </span>
        </nav>
        <div style="height:50px"></div>
    </div>
    <div>
        <div class="text-center">
            <div class="visible-xs">
                <!---手機 -->
                <img src='@Url.Content("~/Content/img/web-revise-phone0308-00.png")' class="img-responsive " alt="Responsive image" />
            </div>
            <div class="hidden-xs">
                <img src='@Url.Content("~/Content/img/web_0717_logo-11.png")' title="回台北e酷幣首頁" />
            </div>
        </div>
        <br>
        <div class="containerEZ">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

                    <div class="layout_font_title">
                        登入
                    </div>
                    <div style="height:20px"></div>
                    <div class="text-center" style="margin: 0px auto;">
                        @using (Html.BeginForm("Login", "Home", FormMethod.Post, new { name = "loginform", defaultbutton = "Button1", defaultfocus = "TextBox1" }))
                        {
                            string SchoolNo = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
                            @Html.HiddenFor(m => m.WhereAWARD_NO)
                            @Html.Hidden("iLockCount", iLockCount)
                            @Html.Hidden("iVlidatePasswordMaxCount", iVlidatePasswordMaxCount)
                            <div>
                                @if (!string.IsNullOrWhiteSpace(Model.txtSCHOOL_NO))
                                {
                                    @Html.HiddenFor(m => m.txtSCHOOL_NO)




                                    var SchoolList = EcoolWeb.Models.UserProfileHelper.GetSchoolList().Where(x => x.Key == Model.txtSCHOOL_NO).Select(x => new SelectListItem { Text = x.Value.SHORT_NAME, Value = x.Value.SCHOOL_NO, Selected = x.Value.SCHOOL_NO == SchoolNo });
                                    <div class="form-inlineEZ">
                                        <div class="form-group">
                                            <label>學 校 </label>
                                            @Html.DropDownListFor(m => m.txtSCHOOL_NO, SchoolList.OrderBy(a => a.Text), "選擇學校...", new { @class = "form-control-Login" })
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    var SchoolList = EcoolWeb.Models.UserProfileHelper.GetSchoolList().Select(x => new SelectListItem { Text = x.Value.SHORT_NAME, Value = x.Value.SCHOOL_NO, Selected = x.Value.SCHOOL_NO == SchoolNo });

                                    <div class="form-inlineEZ">
                                        <div class="form-group">
                                            <label>學 校 </label>
                                            @Html.DropDownListFor(m => m.txtSCHOOL_NO, SchoolList.OrderBy(a => a.Text), "選擇學校...", new { @class = "form-control-Login" })
                                        </div>
                                    </div>

                                }

                                <div class="form-inlineEZ">
                                    <div class="form-group">
                                        <label>帳 號</label>
                                        @Html.EditorFor(m => m.txtUSER_NO, new { htmlAttributes = new { @class = "form-control-Login" } })
                                    </div>
                                </div>

                                <div class="form-inlineEZ">
                                    <div class="form-group">
                                        <label>密 碼</label>
                                        @Html.Password("txtPASSWORD", "", new { @class = "form-control-Login" })
                                    </div>
                                </div>

                                <div class="classInputCode form-inlineEZ">
                                    <div class="form-group">
                                        <label>驗 證</label>
                                        @Html.TextBox("txtInputCode", "", new { @class = "form-control-Login", placeholder = "輸入驗證碼" })
                                    </div>
                                </div>

                                <div class="classInputCode form-inlineEZ">
                                    <div class="form-group">
                                        <label>　 　 </label>
                                        <img id="captcha" src="@Url.Action("VerificationCode","Home")" alt="驗證碼" />
                                        <input type="button" class="btn btn-default btn-sm " value="重取驗證"
                                               onclick="ImgCatcha()" />
                                    </div>
                                </div>

                                <div class="text-center">
                                    <input type="button" class="btn btn-default-Login btn-sm" value="登入" onclick='AuthIdentity()' />

                                    <a role="button" id="btnBack" href='@Url.Action("Index", "BarcCodeMyCash",new { WhereSchoolNo= Model.txtSCHOOL_NO })' class="btn btn-default-Login btn-sm">
                                        放棄
                                    </a>
                                </div>
                            </div>
                        }
                    </div>
                    <div style="height:50px">



                    </div>
                    <div class="form-inlineEZ text-center">

                        <a href="@Url.Action("SSOLoginPage","SSO")">


                            <img src="@Url.Content("~/Content/images/oidc-v.png")" class="text-center" />



                        </a>
                    </div>

                </div>


            </div>
        </div>
    </div>
</body>
</html>
<span class="eachtimer" data-seconds-left=300 style="display:none"></span>
<script type="text/javascript">

        $('.eachtimer').startTimer({
            onComplete: function (element) {
                document.getElementById("btnBack").click();
            },
        });

        $(function () {
            $("form input").keypress(function (e) {
                if ((e.which && e.which == 13) || (e.keyCode && e.keyCode == 13)) {
                    AuthIdentity();
                    return false;
                } else {
                    return true;
                }
            });
        });

        window.onload = function ()
        {
            if ($('#iLockCount').val() >= $('#iVlidatePasswordMaxCount').val())
            {
                $('.classInputCode').show()
            }
        }

        function ImgCatcha()
        {
            var Url = '@Url.Action("VerificationCode", "Home")' + '?r='+(new Date()).getTime()+''
            $('#captcha').attr('src', Url);
        }

        function AuthIdentity() {
            var strMsg = '';


            if ($("#txtUSER_NO").val() == "") {
                strMsg = '請輸入帳號';
            }
            if ($("#txtPASSWORD").val() == "") {
                strMsg = '請輸入密碼';
            }



            if ($('.classInputCode').is(":visible") == true) {
                if ($("#txtInputCode").val() == "") {
                    strMsg = '請輸入驗証碼';
                }
            }



            if (strMsg != '') {
                alert(strMsg);
                return false;
            }


                $.ajax({
                    url: '@Url.Action("CheckUserStatus","Home")',
                    data: { SchoolNo: $("#txtSCHOOL_NO").val(), UserNo: $("#txtUSER_NO").val(), sPwd: $("#txtPASSWORD").val(), InputCode: $("#txtInputCode").val(), CheckCode: true},
                type: 'post',
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {


                    var res = jQuery.parseJSON(data);
                    console.log("Login Model 結果");
                    console.log(res);

                    if (res.ReValue != '') {
                        strMsg += res.ReValue + '\r\n';

                        if (res.IsInputCode == 1) {
                            $('.classInputCode').show()
                        }

                        alert(strMsg);
                        return false;
                    }
                    else {
                        document.loginform.submit();
                    }
                }
            });

        }
</script>








