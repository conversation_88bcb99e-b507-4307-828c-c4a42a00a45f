﻿using com.ecool.sqlConnection;
using Dapper;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using NPOI;
using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.service
{
    public class ADDI09Service
    {
        public string ErrorMsg;

        public void DelADDT20_TEMP(string SESSION_ID, SqlConnection Conn = null, SqlTransaction Transaction = null)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            ADDT20_TEMP ADDT02 = new ADDT20_TEMP();
            bool boolConn = true;

            if (Conn == null)
            {
                boolConn = false;
                Conn = new sqlConnection().getConnection4Query();
            }

            if (Transaction == null) Transaction = Conn.BeginTransaction();
            string sSQL = $@"select *FROM　ADDT20_TEMP Where SESSION_ID=@SESSION_ID";
            var temp = db.Database.Connection.Query<ADDT20_TEMP>(sSQL, new
            {
                SESSION_ID = SESSION_ID
            });
            if (temp.Count() > 0)
            {
                ADDT02.CRE_DATE = temp.FirstOrDefault().CRE_DATE;
                IDbCommand cmd = new SqlCommand(@" DELETE ADDT20_TEMP Where SESSION_ID=@SESSION_ID or Datediff(HOUR,@CRE_DATE,GETDATE())>=2", Conn, Transaction);
                cmd.Parameters.Add(new SqlParameter("@SESSION_ID", SESSION_ID));
                cmd.Parameters.Add(new SqlParameter("@CRE_DATE", ADDT02.CRE_DATE));
                cmd.ExecuteNonQuery();

                if (boolConn == false)
                {
                    Transaction.Commit();

                    if (Conn != null)
                    {
                        Conn.Close();
                        Conn.Dispose();
                    }
                }
            }
            else
            {
                IDbCommand cmd = new SqlCommand(@" DELETE ADDT20_TEMP Where SESSION_ID=@SESSION_ID ", Conn, Transaction);

                cmd.Parameters.Add(new SqlParameter("@SESSION_ID", SESSION_ID));
                cmd.ExecuteNonQuery();

                if (boolConn == false)
                {
                    Transaction.Commit();

                    if (Conn != null)
                    {
                        Conn.Close();
                        Conn.Dispose();
                    }
                }
            }
        }

        #region 取得學生名單List

        public IQueryable<HRMT01> GetStudentList(QuerySelectViewModel Data, string SCHOOL_NO, ECOOL_DEVEntities EntitiesDb)
        {
            IQueryable<HRMT01> HRMT01List = null;
            string sessionid = System.Web.HttpContext.Current.Session.SessionID;
            //學生
            HRMT01List = from h in EntitiesDb.HRMT01
                         where h.SCHOOL_NO == SCHOOL_NO && h.USER_TYPE == UserType.Student && (!UserStaus.NGKeyinUserStausList.Contains(h.USER_STATUS))
                         && !(from t in EntitiesDb.ADDT20_TEMP
                              where t.SESSION_ID == sessionid
                              select t.USER_NO).Contains(h.USER_NO)
                         select h;

            if (string.IsNullOrWhiteSpace(Data.Search.USER_NO) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO.Contains(Data.Search.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(Data.Search.CLASS_NO) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.CLASS_NO == Data.Search.CLASS_NO);
            }

            if (string.IsNullOrWhiteSpace(Data.Search.SYEAR) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.GRADE.Value.ToString() == Data.Search.SYEAR);
            }

            if (string.IsNullOrWhiteSpace(Data.Search.NAME) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.NAME.Contains(Data.Search.NAME));
            }

            if (string.IsNullOrWhiteSpace(Data.Search.OrderByName) == false)
            {
                if (Data.Search.SyntaxName == "Desc")
                {
                    switch (Data.Search.OrderByName)
                    {
                        case "USER_NO":
                            HRMT01List = HRMT01List.OrderByDescending(a => a.USER_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;

                        case "NAME":
                            HRMT01List = HRMT01List.OrderByDescending(a => a.NAME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;

                        case "CLASS_NO":
                            HRMT01List = HRMT01List.OrderByDescending(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;

                        case "SEAT_NO":
                            HRMT01List = HRMT01List.OrderByDescending(a => a.SEAT_NO).ThenBy(a => a.CLASS_NO);
                            break;

                        default:
                            HRMT01List = HRMT01List.OrderByDescending(a => a.USER_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;
                    }
                }
                else
                {
                    switch (Data.Search.OrderByName)
                    {
                        case "USER_NO":
                            HRMT01List = HRMT01List.OrderBy(a => a.USER_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;

                        case "NAME":
                            HRMT01List = HRMT01List.OrderBy(a => a.NAME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;

                        case "CLASS_NO":
                            HRMT01List = HRMT01List.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;

                        case "SEAT_NO":
                            HRMT01List = HRMT01List.OrderBy(a => a.SEAT_NO).ThenBy(a => a.CLASS_NO);
                            break;

                        default:
                            HRMT01List = HRMT01List.OrderBy(a => a.USER_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                            break;
                    }
                }
            }
            else
            {
                HRMT01List = HRMT01List.OrderBy(X => X.CLASS_NO).ThenBy(X => X.SEAT_NO);
            }

            return HRMT01List;
        }

        #endregion 取得學生名單List

        #region 取得老師名單List

        public IQueryable<HRMT01> GetTeacherList(QuerySelectViewModel Data, string SCHOOL_NO, ECOOL_DEVEntities EntitiesDb)
        {
            IQueryable<HRMT01> HRMT01List = null;
            string sessionid = System.Web.HttpContext.Current.Session.SessionID;
            //老師
            HRMT01List = from h in EntitiesDb.HRMT01
                         where h.SCHOOL_NO == SCHOOL_NO && h.USER_TYPE == UserType.Teacher && (!UserStaus.NGKeyinUserStausList.Contains(h.USER_STATUS))
                         && !(from t in EntitiesDb.ADDT20_TEMP
                              where t.SESSION_ID == sessionid
                              select t.USER_NO).Contains(h.USER_NO)
                         select h;

            if (string.IsNullOrWhiteSpace(Data.Search.USER_NO) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO.Contains(Data.Search.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(Data.Search.NAME) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.NAME.Contains(Data.Search.NAME));
            }

            if (string.IsNullOrWhiteSpace(Data.Search.OrderByName) == false)
            {
                if (Data.Search.SyntaxName == "Desc")
                {
                    switch (Data.Search.OrderByName)
                    {
                        case "USER_NO":
                            HRMT01List = HRMT01List.OrderByDescending(a => a.USER_NO);
                            break;

                        case "NAME":
                            HRMT01List = HRMT01List.OrderByDescending(a => a.NAME).ThenBy(a => a.CLASS_NO);
                            break;

                        default:
                            HRMT01List = HRMT01List.OrderByDescending(a => a.USER_NO);
                            break;
                    }
                }
                else
                {
                    switch (Data.Search.OrderByName)
                    {
                        case "USER_NO":
                            HRMT01List = HRMT01List.OrderBy(a => a.USER_NO);
                            break;

                        case "NAME":
                            HRMT01List = HRMT01List.OrderBy(a => a.NAME).ThenBy(a => a.CLASS_NO);
                            break;

                        default:
                            HRMT01List = HRMT01List.OrderBy(a => a.USER_NO);
                            break;
                    }
                }
            }
            else
            {
                HRMT01List = HRMT01List.OrderBy(X => X.CLASS_NO);
            }

            return HRMT01List;
        }

        #endregion 取得老師名單List

        #region BatchADDT20_TEMP

        public void BatchADDT20_TEMP(List<ADDT20_TEMP> Data, string SESSION_ID)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlTransaction transaction = conn.BeginTransaction();

                try
                {
                    using (SqlBulkCopy sbCopy = new SqlBulkCopy(conn, SqlBulkCopyOptions.Default, transaction))
                    {
                        try
                        {
                            this.DelADDT20_TEMP(SESSION_ID, conn, transaction);

                            sbCopy.DestinationTableName = "ADDT20_TEMP";
                            sbCopy.WriteToServer(Data.AsDataTable());
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            ErrorMsg = "新增資料失敗;\r\n" + ex.Message;
                        }
                        transaction.Commit();
                    }
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion BatchADDT20_TEMP

        #region 取得ADDT20_TEMP清單

        /// <summary>
        /// 取得ADDT20_TEMP清單
        /// </summary>
        /// <param name="SESSION_ID"></param>
        /// <returns></returns>
        /// 批次加扣點2023/10/13不排序座號只依照點擊順序排序
        public List<ADDI09SelectListViewModel> GetListData(string SESSION_ID, string OrderBY = "a.NUM")
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(" select a.SCHOOL_NO, a.USER_NO ");
            sb.Append(", b.CLASS_NO,b.SEAT_NO,b.NAME,b.SNAME,b.NAME,a.TempCash,a.Cash,a.PersonType,a.TABLType ");
            sb.Append("  from ADDT20_TEMP(nolock) a ");
            sb.Append(" inner join HRMT01 (nolock)b on a.SCHOOL_NO = b.SCHOOL_NO and a.USER_NO = b.USER_NO  ");
            sb.Append(" Where 1=1");
            sb.AppendFormat(" and a.SESSION_ID = '{0}' ", SESSION_ID);
            sb.AppendFormat(" ORDER BY {0} ", OrderBY);
            return new sqlConnection().executeQueryByDataTableList(sb.ToString()).ToList<ADDI09SelectListViewModel>();
        }

        #endregion 取得ADDT20_TEMP清單

        #region 取得 ADDT20 清單

        /// <summary>
        /// 取得 ADDT20 清單
        /// </summary>
        /// <param name="SESSION_ID"></param>
        /// <returns></returns>
        public List<ADDT20_Extension> GetListData(ADDI09SearchViewModel Data, int pageSize, ref int Count, string SCHOOL_NO)
        {
            StringBuilder sb = new StringBuilder();
            string OrderBy = string.Empty;

            if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
            {
                sb.Append(" SELECT A.BATCH_CASH_ID,A.SCHOOL_NO,A.CRE_DATE,B.NAME AS CRE_PERSON ");
                sb.Append(", A.USER_NO,A.NAME,A.CLASS_NO,A.SEAT_NO,A.SUBJECT,A.CASH,A.NUM ,A.IMG_FILE");
                sb.Append(" FROM ADDT20 A ");
                sb.Append(" INNER JOIN HRMT01 B(NOLOCK) ON  CASE when  A.CRE_PERSON = '系統紙本酷幣點數' then  '系統紙本酷幣點數'");
                sb.Append(" else A.CHG_PERSON end = B.USER_KEY ");
                sb.Append(" WHERE 1 = 1 ");
                sb.Append(" and A.APPLY_STATUS = 2 ");
                sb.AppendFormat(" and A.SCHOOL_NO = '{0}' ", SCHOOL_NO);


                sb.Append(GetWhereADDT20(Data));
                OrderBy = GetOrderbyADD20(Data.OrderByName, Data.SyntaxName);
            }
            else if (Data.SYS_TABLE_TYPE == "ZZZI20")
            {
                sb.Append("select 'ZZZI20' SYS_TABLE_TYPE,  A.SCHOOL_NO ,LOG_TIME as CRE_DATE ,A.USER_NO ,LOG_DESC  as CRE_PERSON ,SNAME   as NAME, B.CLASS_NO, B.SEAT_NO,A.LOG_DESC as SUBJECT , CASH_IN as CASH from AWAT01_LOG A INNER JOIN HRMT01 B(NOLOCK) ON  A.SCHOOL_NO = B.SCHOOL_NO AND A.USER_NO = B.USER_NO ");

                sb.AppendFormat("where A.SCHOOL_NO='{0}' and A.USER_NO='{1}' and LOG_DESC='酷幣匯轉區域管理'", SCHOOL_NO,Data.SOURCE_NO);

            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH || Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
            {
                sb.Append(" SELECT  'ADDT14' SYS_TABLE_TYPE, A.IAWARD_ID, A.BATCH_CASH_ID, A.SCHOOL_NO, A.CREATEDATE as CRE_DATE, A.TNAME AS CRE_PERSON ");
                sb.Append(" , A.USER_NO, A.USERNAME as NAME, A.CLASS_NO, B.SEAT_NO, A.IAWARD_KIND AS SUBJECT, A.CASH,A.IAWARD_ID AS NUM,A.IMG_FILE");
                sb.Append(" FROM ADDT14 A(NOLOCK) ");
                sb.Append(" INNER JOIN HRMT01 B(NOLOCK) ON  A.SCHOOL_NO = B.SCHOOL_NO AND A.USER_NO = B.USER_NO ");
                sb.Append(" WHERE 1 = 1 ");
                sb.Append(" and A.APPLY_STATUS = 2 ");
                sb.AppendFormat(" and A.SCHOOL_NO = '{0}' ", SCHOOL_NO);
                sb.Append(GetWhereADDT14(Data));
                OrderBy = GetOrderbyADD14(Data.OrderByName, Data.SyntaxName);
            }
            else if (Data.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                sb.Append(" SELECT  'ADDT15' SYS_TABLE_TYPE, A.OAWARD_ID, A.BATCH_CASH_ID, A.SCHOOL_NO, A.CREATEDATE as CRE_DATE, A.TNAME AS CRE_PERSON ");
                sb.Append(" , A.USER_NO, A.USERNAME as NAME, A.CLASS_NO, '' SEAT_NO, A.OAWARD_ITEM AS SUBJECT, A.CASH,A.OAWARD_ID AS NUM,A.IMG_FILE");
                sb.Append(" FROM ADDT15 A(NOLOCK) ");
                sb.Append(" WHERE 1 = 1 ");
                sb.Append(" and A.APPLY_STATUS = 2 ");
                sb.AppendFormat(" and A.SCHOOL_NO = '{0}' ", SCHOOL_NO);
                sb.Append(GetWhereADDT15(Data));
                OrderBy = GetOrderbyADD15(Data.OrderByName, Data.SyntaxName);
            }

            string ThisError = "";
            if (Data.SYS_TABLE_TYPE != "ZZZI20")
            {
                DataTable dt = new sqlConnection().executeQueryBSqlDataReaderListPage(Data.Page, pageSize, sb.ToString(), sb.ToString(), OrderBy, ref Count, ref ThisError);

                return dt.ToList<ADDT20_Extension>();
            }
            else {
                DataTable dt = new sqlConnection().executeQueryByDataTableList(sb.ToString());
                Count = dt.ToList<ADDT20_Extension>().Count();
                return dt.ToList<ADDT20_Extension>();

            }
        }

        #endregion 取得 ADDT20 清單

        #region 取得 ADDT20 字串

        /// <summary>
        ///  ADDT20 Where 字串
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private string GetWhereADDT20(ADDI09SearchViewModel Data)
        {
            StringBuilder sbw = new StringBuilder();

            if (Data.SearchContents != string.Empty && Data.SearchContents != null)
            {
                sbw.Append(" AND (");

                sbw.AppendFormat("  A.USER_NO LIKE '%{0}%' ", Data.SearchContents);
                sbw.Append(" OR ");
                sbw.AppendFormat("  A.NAME LIKE '%{0}%' ", Data.SearchContents);
                sbw.Append(" OR ");
                sbw.AppendFormat("  A.SUBJECT LIKE '%{0}%' ", Data.SearchContents);

                DateTime dtDate;
                if (DateTime.TryParse(Data.SearchContents, out dtDate))
                {
                    sbw.Append(" OR ");
                    sbw.AppendFormat("  CONVERT(nvarchar(10),A.CRE_DATE,111) = '{0}' ", Data.SearchContents.Trim());
                }

                sbw.Append(" )");
            }

            if (Data.BATCH_CASH_ID != string.Empty && Data.BATCH_CASH_ID != null)
            {
                sbw.AppendFormat(" AND A.BATCH_CASH_ID = '{0}' ", Data.BATCH_CASH_ID);
            }
            if (Data.ListUSERNO != string.Empty && Data.ListUSERNO != null)
            {
                sbw.AppendFormat(" AND A.USER_NO = '{0}' ", Data.ListUSERNO);
            }

            if (Data.ListSchool != string.Empty && Data.ListSchool != null)
            {
                sbw.AppendFormat(" AND A.SCHOOL_NO = '{0}' ", Data.ListSchool);
            }

            return sbw.ToString();
        }

        #endregion 取得 ADDT20 字串

        #region 取得 eADDT14 字串

        /// <summary>
        ///  eADDT14 Where 字串
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>

        private string GetWhereADDT14(ADDI09SearchViewModel Data)
        {
            StringBuilder sbw = new StringBuilder();

            if (Data.SearchContents != string.Empty && Data.SearchContents != null)
            {
                sbw.Append(" AND (");

                sbw.AppendFormat("  A.USER_NO LIKE '%{0}%' ", Data.SearchContents);
                sbw.Append(" OR ");
                sbw.AppendFormat("  A.USERNAME LIKE '%{0}%' ", Data.SearchContents);
                sbw.Append(" OR ");
                sbw.AppendFormat("  A.IAWARD_KIND LIKE '%{0}%' ", Data.SearchContents);

                DateTime dtDate;
                if (DateTime.TryParse(Data.SearchContents, out dtDate))
                {
                    sbw.Append(" OR ");
                    sbw.AppendFormat("  CONVERT(nvarchar(10),A.CREATEDATE,111) = '{0}' ", Data.SearchContents.Trim());
                }

                sbw.Append(" )");
            }
            if (Data.SOURCE_NO != string.Empty && Data.SOURCE_NO != null)
            {
                sbw.AppendFormat(" AND A.IAWARD_ID  = '{0}' ", Data.SOURCE_NO);
            }
            if (Data.BATCH_CASH_ID != string.Empty && Data.BATCH_CASH_ID != null)
            {
                sbw.AppendFormat(" AND A.BATCH_CASH_ID = '{0}' ", Data.BATCH_CASH_ID);
            }
            if (Data.ListUSERNO != string.Empty && Data.ListUSERNO != null)
            {
                sbw.AppendFormat(" AND A.USER_NO = '{0}' ", Data.ListUSERNO);
            }

            if (Data.ListSchool != string.Empty && Data.ListSchool != null)
            {
                sbw.AppendFormat(" AND A.SCHOOL_NO = '{0}' ", Data.ListSchool);
            }
            return sbw.ToString();
        }

        #endregion 取得 eADDT14 字串

        #region 取得 ADDT15 字串

        /// <summary>
        ///  ADDT15 Where 字串
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private string GetWhereADDT15(ADDI09SearchViewModel Data)
        {
            StringBuilder sbw = new StringBuilder();

            if (Data.SearchContents != string.Empty && Data.SearchContents != null)
            {
                sbw.Append(" AND (");

                sbw.AppendFormat("  A.USER_NO LIKE '%{0}%' ", Data.SearchContents);
                sbw.Append(" OR ");
                sbw.AppendFormat("  A.USERNAME LIKE '%{0}%' ", Data.SearchContents);
                sbw.Append(" OR ");
                sbw.AppendFormat("  A.OAWARD_ITEM LIKE '%{0}%' ", Data.SearchContents);

                DateTime dtDate;
                if (DateTime.TryParse(Data.SearchContents, out dtDate))
                {
                    sbw.Append(" OR ");
                    sbw.AppendFormat(" CONVERT(nvarchar(10),A.CREATEDATE,111) = '{0}' ", Data.SearchContents.Trim());
                }

                sbw.Append(" )");
            }

            if (Data.SOURCE_NO != string.Empty && Data.SOURCE_NO != null)
            {
                sbw.AppendFormat(" AND A.OAWARD_ID  = '{0}' ", Data.SOURCE_NO);
            }
            if (Data.BATCH_CASH_ID != string.Empty && Data.BATCH_CASH_ID != null)
            {
                sbw.AppendFormat(" AND A.BATCH_CASH_ID = '{0}' ", Data.BATCH_CASH_ID);
            }
            if (Data.ListUSERNO != string.Empty && Data.ListUSERNO != null)
            {
                sbw.AppendFormat(" AND A.USER_NO = '{0}' ", Data.ListUSERNO);
            }

            if (Data.ListSchool != string.Empty && Data.ListSchool != null)
            {
                sbw.AppendFormat(" AND A.SCHOOL_NO = '{0}' ", Data.ListSchool);
            }
            return sbw.ToString();
        }

        #endregion 取得 ADDT15 字串

        #region 取得ADD20 Orderby 字串

        /// <summary>
        /// 取得ADD20 Orderby 字串
        /// </summary>
        /// <param name="OrderByName"></param>
        /// <param name="SyntaxName"></param>
        /// <returns></returns>
        private string GetOrderbyADD20(string OrderByName, string SyntaxName)
        {
            string ReturnString = string.Empty;

            if (string.IsNullOrWhiteSpace(OrderByName) == false)
            {
                ReturnString = "A." + OrderByName + " " + SyntaxName;
            }
            else
            {
                ReturnString = "A.BATCH_CASH_ID DESC,A.NUM ASC";
            }
            return ReturnString;
        }

        #endregion 取得ADD20 Orderby 字串

        #region 取得ADD14 Orderby 字串

        /// <summary>
        /// 取得ADD14 Orderby 字串
        /// </summary>
        /// <param name="OrderByName"></param>
        /// <param name="SyntaxName"></param>
        /// <returns></returns>
        private string GetOrderbyADD14(string OrderByName, string SyntaxName)
        {
            string ReturnString = string.Empty;

            if (string.IsNullOrWhiteSpace(OrderByName) == false)
            {
                if (OrderByName == "CRE_DATE")
                {
                    ReturnString = "A.CREATEDATE " + SyntaxName;
                }
                else if (OrderByName == "CRE_PERSON")
                {
                    ReturnString = "A.TNAME " + SyntaxName;
                }
                else if (OrderByName == "NAME")
                {
                    ReturnString = "A.USERNAME " + SyntaxName;
                }
                else if (OrderByName == "SEAT_NO")
                {
                    ReturnString = "A.USER_NO " + SyntaxName;
                }
                else if (OrderByName == "SUBJECT")
                {
                    ReturnString = "A.IAWARD_KIND " + SyntaxName;
                }
                else
                {
                    ReturnString = "A." + OrderByName + " " + SyntaxName;
                }
            }
            else
            {
                ReturnString = "A.IAWARD_ID DESC";
            }
            return ReturnString;
        }

        #endregion 取得ADD14 Orderby 字串

        #region 取得ADD15 Orderby 字串

        /// <summary>
        /// 取得ADD15 Orderby 字串
        /// </summary>
        /// <param name="OrderByName"></param>
        /// <param name="SyntaxName"></param>
        /// <returns></returns>
        private string GetOrderbyADD15(string OrderByName, string SyntaxName)
        {
            string ReturnString = string.Empty;

            if (string.IsNullOrWhiteSpace(OrderByName) == false)
            {
                if (OrderByName == "CRE_DATE")
                {
                    ReturnString = "A.CREATEDATE " + SyntaxName;
                }
                else if (OrderByName == "CRE_PERSON")
                {
                    ReturnString = "A.TNAME " + SyntaxName;
                }
                else if (OrderByName == "NAME")
                {
                    ReturnString = "A.USERNAME " + SyntaxName;
                }
                else if (OrderByName == "SEAT_NO")
                {
                    ReturnString = "A.USER_NO " + SyntaxName;
                }
                else if (OrderByName == "SUBJECT")
                {
                    ReturnString = "A.OAWARD_ITEM " + SyntaxName;
                }
                else
                {
                    ReturnString = "A." + OrderByName + " " + SyntaxName;
                }
            }
            else
            {
                ReturnString = "A.OAWARD_ID DESC";
            }
            return ReturnString;
        }

        #endregion 取得ADD15 Orderby 字串
    }
}