﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class SECI03Hrmt08ListViewModel
    {

      

        ///Summary
        ///學校代碼
        ///Summary
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        /// 學校名稱
        /// </summary>
        [DisplayName("學校名稱")]
        public string SCHOOL_NAME { get; set; }

        ///Summary
        ///身份証
        ///Summary
        [DisplayName("身份証")]
        public string IDNO { get; set; }

        ///Summary
        ///學年
        ///Summary
        [DisplayName("學年")]
        public byte? SYEAR { get; set; }

        ///Summary
        ///學期
        ///Summary
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        ///Summary
        ///學號
        ///Summary
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        ///Summary
        ///年級
        ///Summary
        [DisplayName("年級")]
        public string GRADE_SEMESTER { get; set; }

        ///Summary
        ///年級
        ///Summary
        [DisplayName("年級")]
        public string GRADE { get; set; }

        ///Summary
        ///班級
        ///Summary
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }


        public string SEAT_NO { get; set; }

        ///Summary
        ///姓名
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///姓名
        ///Summary
        [DisplayName("姓名")]
        public string SNAME { get; set; }


        ///Summary
        ///性別
        ///Summary
        [DisplayName("性別")]
        public string SEX { get; set; }


        ///Summary
        ///年齡
        ///Summary
        [DisplayName("年齡")]
        public decimal? AGE { get; set; }

        ///Summary
        ///身高
        ///Summary
        [DisplayName("身高")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? TALL { get; set; }

        ///Summary
        ///當屆平均身高(男)
        ///Summary
        [DisplayName("當屆平均(男)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? AVG_TALL_M { get; set; }

        ///Summary
        ///當屆平均身高(女)
        ///Summary
        [DisplayName("當屆平均(女)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? AVG_TALL_W { get; set; }


        ///Summary
        ///歷屆平均身高(男)
        ///Summary
        [DisplayName("歷屆平均(男)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? SUM_AVG_TALL_M { get; set; }

        ///Summary
        ///歷屆平均身高(女)
        ///Summary
        [DisplayName("歷屆平均(女)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? SUM_AVG_TALL_W { get; set; }


        ///Summary
        ///體重
        ///Summary
        [DisplayName("體重")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? WEIGHT { get; set; }

        ///Summary
        ///當屆平均體重(男)
        ///Summary
        [DisplayName("當屆平均(男)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? AVG_WEIGHT_M { get; set; }


        ///Summary
        ///當屆平均體重(女)
        ///Summary
        [DisplayName("當屆平均(女)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? AVG_WEIGHT_W { get; set; }


        ///Summary
        ///歷屆平均體重(男)
        ///Summary
        [DisplayName("歷屆平均體重(男)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? SUM_AVG_WEIGHT_M { get; set; }


        ///Summary
        ///歷屆平均體重(男)
        ///Summary
        [DisplayName("歷屆平均體重(女)")]
        [DisplayFormat(DataFormatString = "{0:0.0}", ApplyFormatInEditMode = true)]
        public decimal? SUM_AVG_WEIGHT_W { get; set; }


        ///Summary
        ///體位判讀
        ///Summary
        [DisplayName("體位判讀")]
        public string POSTURE_MEMO { get; set; }


        ///Summary
        ///建議體重
        ///Summary
        [DisplayName("建議體重")]
        public decimal? S_WEIGHT { get; set; }

        ///Summary
        ///過重量
        ///Summary
        [DisplayName("過重量")]
        public decimal? O_WEIGHT { get; set; }

        ///Summary
        ///BMI
        ///Summary
        [DisplayName("BMI")]
        public decimal? BMI { get; set; }

        ///Summary
        ///建議BMI
        ///Summary
        [DisplayName("建議BMI")]
        public decimal? S_BMI { get; set; }

        ///Summary
        ///BMI
        ///Summary
        [DisplayName("BMI")]
        public decimal? ShowBMI { get; set; }
        


        ///Summary
        ///裸視右
        ///Summary
        [DisplayName("裸視右")]
        public string VISION_RIGHT { get; set; }

        ///Summary
        ///裸視左
        ///Summary
        [DisplayName("裸視左")]
        public string VISION_LEFT { get; set; }

        ///Summary
        ///戴鏡右
        ///Summary
        [DisplayName("戴鏡右")]
        public string G_VISION_RIGHT { get; set; }

        ///Summary
        ///戴鏡右
        ///Summary
        [DisplayName("戴鏡左")]
        public string G_VISION_LEFT { get; set; }


        [DisplayName("右 0.8以下")]
        public int VISION_RIGHT_08 { get; set; }

        [DisplayName("右 0.9~1.1")]
        public int VISION_RIGHT_09 { get; set; }

        [DisplayName("右 1.2以上")]
        public int VISION_RIGHT_12 { get; set; }


        [DisplayName("左 0.8以下")]
        public int VISION_LEFT_08 { get; set; }

        [DisplayName("左 0.9~1.1")]
        public int VISION_LEFT_09 { get; set; }

        [DisplayName("左 1.2以上")]
        public int VISION_LEFT_12 { get; set; }
            						
    }
}
