﻿
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public  class ADDI05PassListViewModel
    {

        /// <summary>
        /// NO
        /// </summary>
        public string DIALOG_ID { get; set; }

        /// <summary>
        /// Index 的 條件
        /// </summary>
        public string SearchContents { get; set; }

        /// <summary>
        /// Index 的  Order
        /// </summary>
        public string OrderByName { get; set; }

        /// <summary>
        /// Index 的 Syntax
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// Index 的 page
        /// </summary>
        public int? page { get; set; }

        public string OrderRank { get; set; }
        /// <summary>
        /// PassList 的 Page 
        /// </summary>
        public int PassPage { get; set; }

        /// <summary>
        /// PassList 的 PageSize
        /// </summary>
        public  int PassPageSize { get; set; }

        /// <summary>
        /// PassList 的 OrderByName
        /// </summary>
        public string PassOrderByName { get; set; }

        /// <summary>
        /// PassList 的 條件
        /// </summary>
        public string PassSearchContents { get; set; }

        /// <summary>
        /// 抽獎的個數
        /// </summary>
        public int LotteryCount { get; set; }

        public IPagedList<ECOOL_APP.com.ecool.Models.entity.uADDT13> uADDT13;

        public ADDI05PassListViewModel()
        {
            PassPage = 1;
            PassPageSize = 100;
        }

    }
}