﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ActionLink("回「新增批次給點/扣點」首頁", (string)ViewBag.IndexActionName, null, new { @class = "btn btn-sm btn-sys", @role = "button" })

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.Hidden("ADDT14_STYLE", (string)ViewBag.ADDT14_STYLE)
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br />
            <br />
            <div class="form-group">
                <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳Excel檔</label>
                <div class="col-md-9">
                    <input class="btn btn-default" type="file" name="files" placeholder="必填" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>
            <div class=" text-center">
                <button class="btn btn-default">送出</button>
            </div>
            <div class="form-group">
                <div class="col-md-offset-1 col-md-9">
                    <label class="text-danger">
                        上傳說明:<br />
                        @if (ViewBag.SYS_TABLE_TYPE == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
                        {
                            if (ViewBag.ADDT14_STYLE == "LEADER")
                            {
                                <span>
                                    1.上傳之 Excel 檔, 請依班級幹部格式填寫(<a href="@Url.Content("~/Content/ExcelSample/leader.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                                </span><br />
                                <span>  (<a href="@Url.Content("~/Content/ExcelSample/hops.xlsx")" target="_blank" class="btn-table-link">下載溪山實小檔案</a>) </span>
                                @:觀看溪山國小教學檔<br />
                                <span>
                                    2.單一學生給點上限為200點，請斟酌使用，謝謝。
                                </span>
                            }
                            else if (ViewBag.ADDT14_STYLE == "HELPER")
                            {
                                <span>
                                    1.上傳之 Excel 檔, 請依班級幫手和榮譽格式填寫(<a href="@Url.Content("~/Content/ExcelSample/helper.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                                </span><br />
                                <span>  (<a href="@Url.Content("~/Content/ExcelSample/hops.xlsx")" target="_blank" class="btn-table-link">下載溪山實小檔案</a>) </span>
                                @:觀看溪山國小教學檔
                            }
                            else
                            {
                                <span>
                                    1.上傳之 Excel 檔, 請依規定格式填寫(<a href="@Url.Content("~/Content/ExcelSample/BatchCashADDI14Sample.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                                </span><br />
                                <span>  (<a href="@Url.Content("~/Content/ExcelSample/hops.xlsx")" target="_blank" class="btn-table-link">下載溪山實小檔案</a>) </span>
                                @:觀看溪山國小教學檔
                            }
                        }

                        @if (ViewBag.SYS_TABLE_TYPE == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                        {
                            <span>
                                1.上傳之 Excel 檔, 請依規定格式填寫(<a href="@Url.Content("~/Content/ExcelSample/BatchCashADDI15Sample.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                            </span><br />
                            <span>  (<a href="@Url.Content("~/Content/ExcelSample/hops.xlsx")" target="_blank" class="btn-table-link">下載溪山實小檔案</a>) </span>
                            @:觀看溪山國小教學檔
                        }

                        @if (ViewBag.SYS_TABLE_TYPE == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER)
                        {
                            <span>
                                1.上傳之 Excel 檔, 請依規定格式填寫(<a href="@Url.Content("~/Content/ExcelSample/BatchCashADDI20Sample.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                            </span><br />
                            <span>  (<a href="@Url.Content("~/Content/ExcelSample/hops.xlsx")" target="_blank" class="btn-table-link">下載溪山實小檔案</a>) </span>
                            @:觀看溪山國小教學檔
                        }
                        @if (ViewBag.SYS_TABLE_TYPE == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
                        {
                            <span>
                                1.上傳之 Excel 檔, 請依規定格式填寫(<a href="@Url.Content("~/Content/ExcelSample/leader20.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                            </span><br />
                            <span>  (<a href="@Url.Content("~/Content/ExcelSample/hops.xlsx")" target="_blank" class="btn-table-link">下載溪山實小檔案</a>) </span>
                            @:觀看溪山國小教學檔
                        }
                        <br />

                        <span>

                            2.檔名不要是中文、特殊符號之類
                        </span>
                    </label>
                </div>
            </div>
        </div>
    </div>

}