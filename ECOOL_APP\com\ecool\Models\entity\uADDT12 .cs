﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
     public class uADDT12 
    {

        ///Summary
        ///DIALOG_ID
        ///Summary
        [DisplayName("活動代碼")]
        public string DIALOG_ID { get; set; }

        ///Summary
        ///Q_NUM
        ///Summary
        [DisplayName("NO")]
        public int? Q_NUM { get; set; }

        ///Summary
        ///Q_TYPE
        ///Summary
        [DisplayName("題型代碼")]
        public int? Q_TYPE { get; set; }

        ///Summary
        ///Q_TYPE
        ///Summary
        [DisplayName("題型")]
        public string Q_TYPE_NAME { get; set; }

        ///Summary
        ///TRUE_ANS
        ///Summary
        [DisplayName("答案")]
        public string TRUE_ANS { get; set; }

        ///Summary
        ///Q_TEXT
        ///Summary
        [DisplayName("題目")]
        public string Q_TEXT { get; set; }

        ///Summary
        ///Q_ANS1
        ///Summary
        [DisplayName("選項1")]
        public string Q_ANS1 { get; set; }

        ///Summary
        ///Q_ANS2
        ///Summary
        [DisplayName("選項2")]
        public string Q_ANS2 { get; set; }

        ///Summary
        ///Q_ANS3
        ///Summary
        [DisplayName("選項3")]
        public string Q_ANS3 { get; set; }

        ///Summary
        ///Q_ANS4
        ///Summary
        [DisplayName("選項4")]
        public string Q_ANS4 { get; set; }


        public enum enumQ_TYPE_NAME : int
        {
            是非 = 1,
            選擇 = 2,
           
        }

        // (1.是否題、2.選擇題、)
        static public string ParserQ_TYPE_NAME(int? Q_TYPE_NAME)
        {
            if (Q_TYPE_NAME.HasValue == false) return string.Empty;

            string NAME = ((enumQ_TYPE_NAME)Q_TYPE_NAME).ToString();

            return NAME;
        }
    }

}
