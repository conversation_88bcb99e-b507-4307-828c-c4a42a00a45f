﻿@model AWAI01ExchangeViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        if (Model.Search.SouController == "BarcCodeMyCash")
        {
            Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        }

    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string aImgUrl = string.Empty;
    if ( Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
    {
        aImgUrl = ViewBag.ImgUrl + @"/" + Model.AwaData.IMG_FILE;
    }
    else
    {
        aImgUrl = ViewBag.ImgUrl + Model.AwaData.SCHOOL_NO + @"/" + Model.AwaData.IMG_FILE;
    }



}
<style type="text/css">
    .timer, .timer-done, .timer-loop {
        font-size: 30px;
        color: black;
        font-weight: bold;
    }

  

    .jst-hours {
        float: left;
    }

    .jst-minutes {
        float: left;
    }

    .jst-seconds {
        float: left;
    }

    .jst-clearDiv {
        clear: both;
    }

    .jst-timeout {
        color: red;
    }
</style>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/jquery.simple.timer.js?ver=2" ></script>
<div class="@(Model.Search.SouController == "BarcCodeMyCash" ? "bar-div":"")" >
    @Html.Partial("_Title_Secondary")
    @Html.Partial("_Notice")

    @if (Model.Search.SouController == "BarcCodeMyCash")
    {
        <span class="BarcCodeMyCashTimer" data-seconds-left=60 style="display:none"></span>
    }

    @if (Model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
    {
        DateTime startTime = DateTime.Now;

        DateTime endTime = (DateTime)Model.AwaData.EDATETIME;

        var TotalSeconds = endTime.Subtract(startTime).TotalSeconds;

        if (TotalSeconds > 0)
        {
            <div class="row">
                <label class="col-xs-2 text-right">
                    倒數:
                </label>
                <div class="col-xs-10 text-left">
                    <div class="timer" data-seconds-left=@TotalSeconds>
                    </div>
                    <span class="eachtimer" data-seconds-left=250 style="display:none"></span>
                </div>
            </div>

        }


    }


    @using (Html.BeginForm("ActionSaveAwat", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "contentForm", name = "contentForm", enctype = "multipart/form-data" }))
    {
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        @Html.HiddenFor(m => m.Search.OrdercColumn)
        @Html.HiddenFor(m => m.Search.SyntaxName)
        @Html.HiddenFor(m => m.Search.Page)
        @Html.HiddenFor(m => m.Search.BackAction)
        @Html.HiddenFor(m => m.Search.BackController)
        @Html.HiddenFor(m => m.Search.SouController)
        @Html.HiddenFor(m => m.Search.SouAction)

        @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
        @Html.HiddenFor(m => m.Search.WhereAWARD_NO)
        @Html.HiddenFor(m => m.Search.WhereAWARD_STS)
        @Html.HiddenFor(m => m.Search.WhereSouTable)
        @Html.HiddenFor(m => m.Search.unProduct)
        @Html.HiddenFor(m => m.Search.whereAWARD_SCHOOL_NO)
        @Html.HiddenFor(m => m.Search.AWARD_TYPE)
        @Html.HiddenFor(m => m.Search.whereKeyword)
        @Html.HiddenFor(m => m.AwaData.AWARD_NO)
        @Html.HiddenFor(m => m.AwaData.AWARD_TYPE)

        <img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
        <div class="Div-EZ-Awat02">
            <div class="form-horizontal">
                <div class="col-md-12">
                    <div class="p-context">
                        <img src='@aImgUrl' style="float:right;margin:10px;max-width:200px;width:auto;" href="@aImgUrl" class="img-responsive" />
                        <div>
                            <samp class="dt">
                                @if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
                                {
                                    <text>老師姓名</text>
                                }
                                else
                                {
                                    <text>學生姓名</text>
                                }
                            </samp>
                            <samp class="dd">
                                @user.NAME
                            </samp>
                        </div>
                        <div>
                            <samp class="dt">
                                獎品名稱
                            </samp>
                            <samp class="dd">
                                @Html.DisplayFor(model => model.AwaData.AWARD_NAME)
                            </samp>
                        </div>
                        @if (Model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                        {
                            <div>
                                <samp class="dt">
                                    目前出價點數
                                </samp>
                                <samp class="dd">
                                    @Html.DisplayFor(model => model.AwaData.COST_CASH)
                                </samp>
                            </div>



                            if (Model.AwaData.BID_PER_UNIT_PRICE != null)
                            {
                                <div>
                                    <samp class="dt">
                                        出價增額點數
                                    </samp>
                                    <samp class="dd">
                                        @Html.DisplayFor(model => model.AwaData.BID_PER_UNIT_PRICE)
                                    </samp>
                                </div>
                            }


                            if (Model.AwaData.BID_BUY_PRICE != null)
                            {
                                <div>
                                    <samp class="dt">
                                        直購價
                                    </samp>
                                    <samp class="dd">
                                        @Html.DisplayFor(model => model.AwaData.BID_BUY_PRICE)

                                        @if (string.IsNullOrWhiteSpace(ViewBag.NGError))
                                        {
                                            if (Model.AwaData.BID_BUY_PRICE != null && user.CASH >= Model.AwaData.BID_BUY_PRICE)
                                            {
                                                <button role="button" type="button" value="Create" class="btn btn-default-buy btn-sm" onclick="doBuy()">
                                                    直接購買
                                                </button>
                                            }
                                        }
                                    </samp>

                                </div>
                            }
                            <div>
                                <samp class="dt">
                                    @Html.LabelFor(m => m.THIS_PRICE_CASH)
                                </samp>
                                <samp class="dd">
                                    @if ((Model.AwaData.BID_PER_UNIT_PRICE ?? 0) > 0)
                                    {<strong style="color:red">
                                            @*<strong style="color:red">@Model.THIS_PRICE_CASH</strong>*@
                                            @Html.EditorFor(m => m.THIS_PRICE_CASH, new { htmlAttributes = new { @class = "form-control;col-xs1;input-xs", @style = "color:red", @disabled= "disabled", @min = @Html.DisplayFor(model => model.AwaData.COST_CASH), @max = Model.THIS_PRICE_CASH } })
                                        </strong>
                                        <input type="button" value="增加 @Html.DisplayFor(model => model.AwaData.BID_PER_UNIT_PRICE)" onclick="ADDPrice('@Html.DisplayFor(model => model.AwaData.COST_CASH)','@Html.DisplayFor(model => model.THIS_PRICE_CASH)','@Html.DisplayFor(model => model.AwaData.BID_PER_UNIT_PRICE)')">
                                        <input type="button" value="減少 @Html.DisplayFor(model => model.AwaData.BID_PER_UNIT_PRICE)" onclick="CutPrice('@Html.DisplayFor(model => model.AwaData.COST_CASH)','@Html.DisplayFor(model => model.THIS_PRICE_CASH)','@Html.DisplayFor(model => model.AwaData.BID_PER_UNIT_PRICE)')">
                                    }
                                    else
                                    {
                                        @Html.EditorFor(m => m.THIS_PRICE_CASH, new { htmlAttributes = new { @class = "form-control;col-xs1;input-xs" } })
                                    }
                                    <span style="white-space: pre-line">@Html.ValidationMessageFor(m => m.THIS_PRICE_CASH, "", new { @class = "text-danger" })</span>

                                </samp>

                            </div>
                        }
                        else
                        {
                            <div>
                                <samp class="dt">
                                    兌換點數
                                </samp>
                                <samp class="dd">
                                    @Html.DisplayFor(model => model.AwaData.COST_CASH)
                                </samp>
                            </div>
                        }
                        <div>
                            <samp class="dt">
                                兌換後剩餘點數
                            </samp>
                            <samp class="dd">
                                @(user.CASH - Convert.ToInt32(Model.AwaData.COST_CASH))
                            </samp>
                        </div>
                        @if (Model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_P || Model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_C)
                        {
                            <div>
                                <samp class="dt">
                                    已募資點數
                                </samp>
                                <samp class="dd">
                                    @(Model.AwaData.COST_CASH * Model.AwaData.QTY_TRANS)
                                </samp>
                            </div>
                        }
                        else
                        {
                            <div>
                                <samp class="dt">
                                    剩餘數量
                                </samp>
                                <samp class="dd">
                                    @Html.DisplayFor(model => model.AwaData.QTY_STORAGE)
                                </samp>
                            </div>
                        }
                        <!--限制對象-->
                        <div>
                            <samp class="dt">
                                @Html.DisplayNameFor(model => model.AwaData.BUY_PERSON_YN)
                            </samp>
                            <samp class="dd">
                                @if (Model.AwaData.BUY_PERSON_YN == "Y")
                                {
                                    <span class="text-danger">　是</span>
                                    <span>(@ECOOL_APP.com.ecool.util.StringHelper.LeftStringR(Model.AwaData.BUY_PERSON, 50))</span>
                                }
                                else
                                {
                                    <span class="text-danger">　否</span>
                                }
                            </samp>
                        </div>
                        @if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student)
                        {
                            <div>
                                <samp class="dt">
                                    閱讀認證
                                </samp>
                                <samp class="dd">
                                    @if (Model.AwaData.READ_LEVEL != null)
                                    {

                                        <samp>需滿</samp>
                                        <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgReadUrl(Model.AwaData.READ_LEVEL))" style="max-height:30px;margin-right:1px">
                                        <samp>級</samp>
                                    }
                                    else
                                    {
                                        <samp>無限制</samp>
                                    }
                                </samp>
                            </div>
                            <div>
                                <samp class="dt">
                                    閱讀護照
                                </samp>
                                <samp class="dd">
                                    @if (Model.AwaData.PASSPORT_LEVEL != null)
                                    {
                                        <samp>需滿</samp>
                                        <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgPassportUrl(Model.AwaData.PASSPORT_LEVEL))" style="max-height:30px;margin-right:1px">
                                        <samp>級</samp>
                                    }
                                    else
                                    {
                                        <samp>無限制</samp>
                                    }
                                </samp>
                            </div>
                        }

                        @if (Model.AwaData.SHOW_DESCRIPTION_YN == SharedGlobal.Y)
                        {
                            <div>
                                <samp class="dt">
                                    備註說明
                                </samp>
                                <samp class="dd">
                                    @Html.DisplayFor(model => model.AwaData.DESCRIPTION)
                                </samp>
                            </div>
                        }
                    </div>
                </div>
                <div class="row Div-btn-center">
                    <div class="form-group">

                        @if (string.IsNullOrWhiteSpace(ViewBag.NGError))
                        {

                            if (Model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                            {
                                <div class="col-md-offset-3 col-md-3">
                                    <button role="button" type="button" value="Create" class="btn btn-default" onclick="doAwaData()">
                                        出價
                                    </button>
                                </div>

                            }
                            else
                            {
                                <div class="col-md-offset-3 col-md-3">
                                    <button role="button" type="button" value="Create" class="btn btn-default" onclick="doAwaData()">
                                        確定送出
                                    </button>
                                </div>
                            }


                            <div class="col-md-offset-1 col-md-3">
                                @if (!string.IsNullOrWhiteSpace(Model.Search.SouController))
                                {
                                    <a href='@Url.Action(Model.Search.SouAction,Model.Search.SouController)' role="button" class="btn btn-default">
                                        放棄
                                    </a>
                                }
                                else
                                {
                                    <a href='@Url.Action(Model.Search.BackAction,(string)ViewBag.BRE_NO)' role="button" class="btn btn-default">
                                        放棄
                                    </a>
                                }
                            </div>
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(Model.Search.SouController))
                            {
                                <a href='@Url.Action(Model.Search.SouAction,Model.Search.SouController)' role="button" class="btn btn-default">
                                    返回
                                </a>
                            }
                            else
                            {
                                <a href='@Url.Action(Model.Search.BackAction,(string)ViewBag.BRE_NO)' role="button" class="btn btn-default">
                                    返回
                                </a>
                            }

                        }


                    </div>
                </div>
            </div>
        </div>
    }
</div>



   

<!--資料顯示區END -->
<script type="text/javascript">

    $(document).ready(function () {
        $(".img-responsive").colorbox({ opacity: 0.82 });
    });

    $('.timer').startTimer({
        onComplete: function (element) {
            element.addClass('is-complete');
            funGetExchange()
        },
        loop: true,
        loopInterval: 3,
    });

    $('.eachtimer').startTimer({
        onComplete: function (element) {
            funGetExchange()
        },
    });

    $('.BarcCodeMyCashTimer').startTimer({
        onComplete: function (element) {
            document.location.href = "@Url.Action("Index", "BarcCodeMyCash", new { WhereSchoolNo = Model.Search.WhereSCHOOL_NO })";
        },
    });

    function ADDPrice(obj, val,Up) {
        var thobj = 0;
        var thobval = 0;
        thobval = parseInt($("#THIS_PRICE_CASH").val());
        thobj = thobval + parseInt(Up);
        $("#THIS_PRICE_CASH").val(thobj);
    }
    function CutPrice(obj, val, Up) {
        var thobj = 0;
        var thobval = 0;
        thobval = parseInt($("#THIS_PRICE_CASH").val());
        if ((thobval - parseInt(Up)) < val) {
            thobj = val;
        }
        else { 
            thobj = thobval - parseInt(Up);
        }
        $("#THIS_PRICE_CASH").val(thobj);
    }

         function funGetExchange() {
            document.contentForm.action = "AwatExchange02";
            document.contentForm.submit();
        }


    function doAwaData() {
        $("#THIS_PRICE_CASH").removeAttr("disabled");  
        document.contentForm.action = "AwatExchange02Start";
        document.contentForm.submit();
    }

    function doBuy() {
        var result = confirm('若以直購價購買後，即無法取消兌換喔~~');
        if (result) {
            $('#@Html.IdFor(m=>m.THIS_PRICE_CASH)').val('@Model.AwaData.BID_BUY_PRICE')
            doAwaData()
        }
      
    }



</script>