/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/LatinExtendedB.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{384:[699,13,500,-14,444],392:[576,13,560,-5,627],400:[686,4,512,54,676],402:[707,156,500,-87,537],405:[699,10,735,-13,692],409:[691,8,500,-23,483],410:[699,9,278,2,290],411:[666,0,480,16,452],414:[462,205,536,-6,474],416:[716,18,722,27,806],417:[507,13,537,24,595],421:[673,205,520,-100,466],426:[684,233,400,46,380],427:[594,218,286,-49,289],429:[691,9,360,-3,450],431:[803,18,775,67,893],432:[583,9,556,15,656],442:[450,237,496,-52,458],443:[683,0,500,-27,469],446:[541,10,500,37,463],448:[740,0,208,14,278],449:[740,0,345,14,415],450:[740,0,368,14,438],451:[684,13,300,45,355],496:[690,207,350,-104,474],506:[972,0,667,-68,593],507:[909,14,500,-21,456],508:[904,0,944,-64,918],509:[697,13,722,-5,673],510:[904,125,722,27,691],511:[697,119,500,-3,441],567:[462,207,278,-189,239]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/LatinExtendedB.js");
