﻿@model EcoolWeb.ViewModels.ADDI01ProxyEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()


    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(model => model.Search.ModeVal)
    @Html.HiddenFor(model => model.Search.SCHOOL_NO)
    <br />
    <div class="Div-EZ-ZZZI26">
        <div class="alert alert-success" style="padding:0 2%; margin-bottom:0px"><h3>代申請線上投稿</h3></div>
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group">
                @Html.LabelFor(model => model.Search.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.DropDownListFor(model => model.Search.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Search.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Search.NumType, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.DropDownListFor(model => model.Search.NumType, (IEnumerable<SelectListItem>)ViewBag.NumTypeItems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Search.NumType, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="text-right">
                @Html.PermissionButton("下一步", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-default", onclick = "Add()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            </div>
        </div>
    </div>


}
<div class="text-center">
    @Html.ActionLink("回選擇模式", "Index", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-default" })
</div>
@section Scripts {
    <script language="JavaScript">
        function Add() {
            form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
            form1.submit();
        }
    </script>
}

