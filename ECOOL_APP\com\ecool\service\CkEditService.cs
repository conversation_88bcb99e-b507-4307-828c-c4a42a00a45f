﻿using Dapper;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.com.ecool.service
{
    public class CkEditService
    {
        public CkEditIndexViewModel GetSourceData(CkEditIndexViewModel model, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"SELECT A.REF_NO,A.SCHOOL_NO,A.USER_NO,A.REF_TYPE
            ,A.SUBJECT,A.CRE_DATE,A.CRE_PERSON,A.[FILE],A.FILE_SIZE ";
            sSQL = sSQL + @" FROM BDMT03 A (NOLOCK) ";
            sSQL = sSQL + @" WHERE A.REF_TYPE =@REF_TYPE ";
            sSQL = sSQL + @" and A.SCHOOL_NO=@SCHOOL_NO ";
            sSQL = sSQL + @" and A.USER_NO=@USER_NO ";
            sSQL = sSQL + @" ORDER BY A.CRE_DATE DESC";

            var temp = db.Database.Connection.Query<CkEditIndexListViewModel>(sSQL
                      , new
                      {
                          SCHOOL_NO = model.Search.SeeSCHOOL_NO,
                          USER_NO = model.Search.SeeUSER_NO,
                          REF_TYPE = model.Search.SeeREF_TYPE
                      });

            if (!string.IsNullOrWhiteSpace(model.Search.whereSUBJECT))
            {
                temp = temp.Where(a => a.SUBJECT.Contains(model.Search.whereSUBJECT.Trim()) || a.FILE.Contains(model.Search.whereSUBJECT.Trim()));
            }

           temp = temp.Select(
               a => {
                   a.FILE_PATH = GetDirectorySysCkEditPath(a.REF_TYPE,a.SCHOOL_NO,a.USER_NO,a.FILE);
                   return a;
               }
           ).ToList();

            model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

            return model;
        }

        public CkEditEditViewModel GetCkEditSourceEdit(CkEditEditViewModel model, ref ECOOL_DEVEntities db)
        {
            model.Data = (from a in db.BDMT03
                          where a.REF_NO == model.Search.WhereREF_NO
                          select new CkEditEditDataViewModel()
                          {
                              REF_NO = a.REF_NO,
                              SCHOOL_NO = a.SCHOOL_NO,
                              USER_NO = a.USER_NO,
                              REF_TYPE = a.REF_TYPE,
                              SUBJECT = a.SUBJECT,
                              FILE = a.FILE,
                              FILE_SIZE = a.FILE_SIZE,
                              CRE_DATE = a.CRE_DATE,
                              CRE_PERSON = a.CRE_PERSON,
                              CHG_DATE = a.CHG_DATE,
                              CHG_PERSON = a.CHG_PERSON,
                          }).FirstOrDefault();

            if (model.Data != null)
            {
                if (model.Data.FILE != null)
                {
                    model.Data.FILE_PATH = UrlCustomHelper.Url_Content(GetDirectorySysCkEditPath(model.Data.REF_TYPE, model.Data.SCHOOL_NO, model.Data.USER_NO, model.Data.FILE));
                }
            }

            return model;
        }

        public bool SaveCkEdit(CkEditEditViewModel data, List<HttpPostedFileBase> files, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            BDMT03 SaveUp = null;

            SaveUp = db.BDMT03.Where(a => a.REF_NO == data.Data.REF_NO).FirstOrDefault();


            if (SaveUp != null)
            {
                SaveUp.SUBJECT = data.Data.SUBJECT;
                if (files != null)
                {
                  
                    var Thisfile = files.FirstOrDefault();

                    if (Thisfile!=null && Thisfile.ContentLength>0)
                    {
                        bool FileOk = DelFile(SaveUp.REF_TYPE, SaveUp.SCHOOL_NO, SaveUp.USER_NO, SaveUp.FILE, ref Message);
                        if (FileOk)
                        {
                            CreHis(SaveUp, User, "異動", ref db);

                            SaveUp.FILE = Path.GetFileName(Thisfile.FileName);
                            SaveUp.FILE_SIZE = UpLoadFile(SaveUp.REF_TYPE, User, Thisfile, ref Message);

                            if (!string.IsNullOrWhiteSpace(Message))
                            {
                                return false;
                            }

                            if (string.IsNullOrWhiteSpace(data.Data.SUBJECT))
                            {
                                SaveUp.SUBJECT = SaveUp.FILE;
                            }
                            else
                            {
                                SaveUp.SUBJECT = data.Data.SUBJECT;
                            }
                        }
                        else
                        {
                            return false;
                        }
                    }
                }
                SaveUp.CHG_DATE = DateTime.Now;
                SaveUp.CHG_PERSON = User.USER_NO;
            }
            else if (SaveUp == null)
            {

                foreach (var Thisfile in files)
                {
                    if (Thisfile!=null && Thisfile.ContentLength>0)
                    {
                        SaveUp = new BDMT03();
                        SaveUp.REF_NO = Guid.NewGuid().ToString("N");
                        SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                        SaveUp.USER_NO = User.USER_NO;
                        SaveUp.REF_TYPE = data.Search.SeeREF_TYPE;

                        SaveUp.FILE = Path.GetFileName(Thisfile.FileName);
                        SaveUp.FILE_SIZE = UpLoadFile(SaveUp.REF_TYPE, User, Thisfile, ref Message);

                        if (!string.IsNullOrWhiteSpace(Message))
                        {
                            return false;
                        }

                        if (string.IsNullOrWhiteSpace(data.Data.SUBJECT))
                        {
                            SaveUp.SUBJECT = SaveUp.FILE;
                        }
                        else
                        {
                            SaveUp.SUBJECT = data.Data.SUBJECT;
                        }

                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = User.USER_NO;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CHG_PERSON = User.USER_NO;

                        db.BDMT03.Add(SaveUp);
                    }
                }
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public bool DelCkEdit(CkEditEditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            BDMT03 SaveUp = null;

            SaveUp = db.BDMT03.Where(a => a.REF_NO == data.Data.REF_NO).FirstOrDefault();

            if (SaveUp == null)
            {
                Message = "系統發生錯誤;原因:此檔案已不存在!!";
                return false;
            }

            if (SaveUp != null)
            {
                bool FileOk = DelFile(SaveUp.REF_TYPE, SaveUp.SCHOOL_NO, SaveUp.USER_NO, SaveUp.FILE, ref Message);

                if (FileOk)
                {
                    CreHis(SaveUp, User, "刪除", ref db);

                    db.BDMT03.Remove(SaveUp);
                }
                else
                {
                    return false;
                }

                
            }
           
            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public void CreHis(BDMT03 SaveUp, UserProfile User, string LOG_MEMO, ref ECOOL_DEVEntities db)
        {
            BDMT03_HIS Cre = new BDMT03_HIS();

            Cre.REF_NO = SaveUp.REF_NO;
            Cre.SCHOOL_NO = SaveUp.SCHOOL_NO;
            Cre.USER_NO = SaveUp.USER_NO;
            Cre.REF_TYPE = SaveUp.REF_TYPE;
            Cre.SUBJECT = SaveUp.SUBJECT;
            Cre.FILE = SaveUp.FILE;
            Cre.FILE_SIZE = SaveUp.FILE_SIZE;
            Cre.CRE_DATE = SaveUp.CRE_DATE;
            Cre.CRE_PERSON = SaveUp.CRE_PERSON;
            Cre.CHG_DATE = SaveUp.CHG_DATE;
            Cre.CHG_PERSON = SaveUp.CHG_PERSON;
            Cre.LOG_TIME = DateTime.Now;
            Cre.LOG_PERSON = User.USER_NO;
            Cre.LOG_MEMO = LOG_MEMO;

            db.BDMT03_HIS.Add(Cre);
        }

        public long? UpLoadFile(string REF_TYPE,UserProfile User, HttpPostedFileBase files, ref string Message)
        {
            try
            {
                string tempPath = GetSysCkEditPath(REF_TYPE, User.SCHOOL_NO, User.USER_NO);

                if (files != null)
                {
                    if (Directory.Exists(tempPath) == false)
                    {
                        Directory.CreateDirectory(tempPath);
                    }

                    if (files != null && files.ContentLength > 0)
                    {
                        string fileName = Path.GetFileName(files.FileName);
                        string UpLoadFile = tempPath + "\\" + fileName;


                        if (System.IO.File.Exists(UpLoadFile))
                        {
                            System.IO.File.Delete(UpLoadFile);
                        }

                        files.SaveAs(UpLoadFile);

                        FileInfo fInfo = new FileInfo(UpLoadFile);

                        return fInfo.Length;
                    }
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return null;
            }


            return null;
        }

        public bool DelFile(string REF_TYPE, string SCHOOL_NO, string USER_NO, string FILE, ref string Message)
        {
            try
            {
                string TempPath = GetSysCkEditPath(REF_TYPE, SCHOOL_NO, USER_NO) + "\\" + FILE;

                if (System.IO.File.Exists(TempPath))
                {
                    System.IO.File.Delete(TempPath);
                }

                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }

        
        }

        /// <summary>
        /// 虛擬 Path
        /// </summary>
        /// <param name="REF_TYPE"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysCkEditPath(string REF_TYPE, string SCHOOL_NO, string USER_NO, string FileName,bool isExists = false)
        {
            string TempPath = SharedGlobal.CkEditFilePath + @"\" + SCHOOL_NO + @"\" + USER_NO + @"\" + REF_TYPE + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return TempPath;
            }

            return string.Empty;
        }

        public string GetSysCkEditPath(string REF_TYPE, string SCHOOL_NO, string USER_NO)
        {
            return HttpContext.Current.Server.MapPath(SharedGlobal.CkEditFilePath) + @"\" + SCHOOL_NO + @"\" + USER_NO + @"\" + REF_TYPE;
        }
    }
}
