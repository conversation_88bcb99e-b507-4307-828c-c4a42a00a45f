﻿@model ADDI11EditViewModel
@using EcoolWeb.Util;
@using ECOOL_APP.com.ecool.service

@{
    /**/
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    string UUIDstr = "N";
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {

        UUIDstr = "Y";

    }
    ViewBag.Title = ViewBag.Panel_Title;
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    Double Max_LAP_M = Math.Floor(Convert.ToDouble((3000.0 / Model.ONE_LAP_M) * 10)) / 10;
}


<style type="text/css">
    .css-table {
        display: table;
        border-collapse: collapse;
        width: 100%
    }

        .css-table .thead {
            display: table-header-group;
        }

        .css-table .tbody {
            display: table-row-group;
        }

        .css-table .tr {
            display: table-row;
            padding-bottom: 2px
        }

        .css-table .th, .css-table .td {
            display: table-cell;
            padding-left: 3px;
        }

        .css-table .th, .css-table .td_title {
            display: table-cell;
            padding-left: 3px;
            border: 1px solid #cccccc;
            color: black;
            background-color: chartreuse;
            text-align: center;
            line-height: 28px;
        }

    .input-group-btn {
        position: relative;
    }
</style>


@Html.Partial("_Notice")
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@{ Html.RenderAction("_RunMenu", new { NowAction = "Edit" }); }

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.ONE_LAP_M)
    @Html.Hidden("Max_LAP_M", Max_LAP_M)
    <img src="~/Content/img/web-bar-Run.png" class="img-responsive" />
    <fieldset>
        <div class="Div-EZ-reader">
            <div class="form-horizontal">
                <div style="height:15px"></div>
                <div class="Caption_Div">
                    @ViewBag.Panel_Title
                </div>
                <div style="height:15px"></div>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        <font color="red">*</font>
                        我要登錄
                    </label>
                    <div class="col-md-9">
                        <div class="input-group">
                            @Html.DropDownListFor(model => model.SearchCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-md" })
                            <span class="input-group-addon">班</span>
                        </div>
                        @Html.ValidationMessageFor(model => model.SearchCLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        <font color="red">*</font>
                        跑步日期
                    </label>
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.RunDate, new { htmlAttributes = new { @class = "form-control input-md", @type = "text", @onchange = "onSure();" } })
                        @Html.ValidationMessageFor(m => m.RunDate, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="text-center" style="padding-bottom:30px">
                <input type="button" class="btn-yellow btn" value="確定" onclick="onSure();" />
            </div>
        </div>
    </fieldset>

    if (!string.IsNullOrWhiteSpace(Model.SearchCLASS_NO))
    {
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>

        <div class="Div-EZ-reader">
            <div class="form-horizontal">
                <div style="height:15px"></div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="Caption_Div">
                            批次給圈數的貼心工具
                        </div>
                    </div>
                    <div class="col-md-9">

                        <div class=@(UUIDstr=="Y"?" ":"input-group")>
                            @Html.Editor("LAP", new { htmlAttributes = new { @class = "form-control input-sm", @placeholder = "0~" + Max_LAP_M.ToString() } })



                            <span class="input-group-btn">
                                <button class="btn btn-default btn-sm" type="button" onclick="AutoVal($('#LAP').val())">全部自動帶入這個圈數</button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>
        <div class="Div-EZ-reader">
            <div class="form-horizontal">
                <div class="table-responsive">
                    <div class="alert alert-success">班級：@(Model.SearchCLASS_NO)，日期：@(Model.RunDate?.ToString("yyyy/MM/dd")) 的跑步新增</div>
                    <div class="css-table">
                        <div class="tr">
                            <div class="td_title">
                                姓名/座號
                            </div>
                            <div class="td_title">
                                圈數(目前一圈@(Model.ONE_LAP_M)公尺)<br />
                                最多不能超過@(Max_LAP_M )圈
                            </div>
                        </div>
                        @if (Model.People != null)
                        {
                            foreach (var item in Model.People)
                            {
                                @Html.Action("_People", item)
                            }
                        }
                    </div>
                </div>
                <div style="height:25px"></div>
                <div class="text-center">
                    @if (Model.People != null && Model.People.Count() != 0)
                    {
                        <button id="BtnSave" class="btn btn-default" type="button" onclick="onSave()">存檔</button>
                    }
                    @if (ViewBag.ISTASKLIST == "True")
                    {
                        <a type="button" class="btn btn-default" href="@Url.Action("Index", "USER")">放棄返回</a>
                    }
                </div>
            </div>
        </div>

    }
}
<script type="text/javascript">

    var targetFormID = '#formEdit';

    function AutoVal(Val) {
        var re = /^[0-9]+(\.[0-9]{1,1})?$/;
        if (!re.test(Val)) {
            alert("只能輸入數字，且小數位數只能到第1位");
            $("#LAP").val('')
            return false;
        }

        var Max_LAP_M= $("#Max_LAP_M").val()

        if ((Val / 1) > (Max_LAP_M / 1) || (Val / 1) < (0)) {
            alert("輸入0~" + Max_LAP_M+"的數字");
            $("#LAP").val('')
            return false;
        }

        $(".LAP_Edit").each(function (i) {
            this.value = Val;
        });
    }

    function onSure()
    {
        var Today = new Date();
        Today = Today.setDate(Today.getDate() + 7);
        Today = new Date(Today);
        var NextDay = new Date();
        NextDay = $("#RunDate").val();
        Today = Today.toLocaleDateString();
        console.log(Today);
        if (NextDay > Today) {
            confirm("超過一週以上未來日期無法新增。");
        }
        else {
            if ("@UUIDstr"=="Y") {
                  $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO, new { ISTASKLIST = "True" })")
            $(targetFormID).submit();

            }
            else{

                  $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO, new { ISTASKLIST = "" })")
            $(targetFormID).submit();
            }

        }
    }

    function onSave()
    {
        var Today = new Date();
        Today = Today.setDate(Today.getDate() + 7);
        Today = new Date(Today);
        var NextDay = new Date();
        NextDay = $("#RunDate").val();
        Today = Today.toLocaleDateString();

        if (NextDay > Today) {
            alert("超過一週以上未來日期無法新增。");
        }
        else {
        $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
         $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    }

    $("#@Html.IdFor(m => m.RunDate)").datepicker({
        dateFormat: "yy/mm/dd",
        changeMonth: true,
        changeYear: true,
        showOn: "button",
        buttonImage: "../Content/img/icon/calendar.gif",
        buttonImageOnly: true
    });

    if ($("#@Html.IdFor(m => m.RunDate)").val()=='') {
           var Today = new Date();
           $("#@Html.IdFor(m => m.RunDate)").datepicker("setDate", Today);
    }
</script>