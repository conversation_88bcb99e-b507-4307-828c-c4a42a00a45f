﻿
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace com.ecool.service 
{
    public class BDMT01Service : ServiceBase
    {
        /// <summary>
        /// Description : 取得學校資料
        /// </summary>
        /// <returns></returns>
        public static List<uBDMT01> USP_BDMT01_QUERY(string SCHOOL_NO, UserProfile User,bool IsAllSchool=false)
        {
            List<uBDMT01> list_data = new List<uBDMT01>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT A.SCHOOL_NO ,A.SCHOOL_NAME,A.SHORT_NAME,A.CITY,A.ADDRESS,A.TEL,A.FAX,A.LOGO_FILE,A.E_MAIL");
                sb.Append(",A.CHG_PERSON");
                sb.Append(",A.CHG_DATE");
                sb.Append(",A.CRE_PERSON");
                sb.Append(",A.CRE_DATE");
                sb.Append(",A.SYS_ID");
                sb.Append(" FROM BDMT01 A (NOLOCK)");
                sb.Append(" WHERE 1=1 ");

                if (SCHOOL_NO != null && SCHOOL_NO!="")
                {
                    sb.Append(" AND A.SCHOOL_NO = '" + SCHOOL_NO+"' ");
                }

                if (User!=null)
                {
                    if (!IsAllSchool) //是否可跨校
                    {
                        if (ECOOL_APP.EF.HRMT24_ENUM.QMeSchoolList.Contains((byte)User.ROLE_TYPE))
                        {
                            sb.Append(" AND A.SCHOOL_NO = '" + User.SCHOOL_NO + "' ");
                        }
                    }

                   
                }


                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uBDMT01()
                    {
                        

                        SCHOOL_NO = dr["SCHOOL_NO"].ToString(),
                        SCHOOL_NAME = dr["SCHOOL_NAME"].ToString(),
                        SHORT_NAME = dr["SCHOOL_NAME"].ToString(),
                        CITY = dr["CITY"].ToString(),
                        ADDRESS = dr["ADDRESS"].ToString(),
                        TEL = dr["TEL"].ToString(),
                        FAX = dr["FAX"].ToString(),
                        LOGO_FILE = dr["LOGO_FILE"].ToString(),
                        E_MAIL = dr.IsNull("E_MAIL") ? "" : dr["E_MAIL"].ToString(),
                        SYS_ID = dr["SYS_ID"].ToString()
                    });
                }
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;

        }

        /// <summary>
        /// 取得學校資料選單
        /// </summary>
        /// <param name="SelectedVal">預設值</param>
        /// <param name="SCHOOL_NO">指定學校</param>
        /// <param name="User">UserProfile</param>
        /// <param name="appEndOptionLabel">是否全部選項</param>
        /// <param name="EndoptionLabel">是否全部選項顯示名稱</param>
        /// <param name="EndoptionVal">是否全部選項Val</param>
        /// <returns></returns>
        public static List<SelectListItem> GetSCHOOL_NO(string SelectedVal, string SCHOOL_NO, UserProfile User, bool appEndOptionLabel, string EndoptionLabel, string EndoptionVal,bool Permission, bool IsAllSchool = false)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            var dt = BDMT01Service.USP_BDMT01_QUERY(SCHOOL_NO, User, IsAllSchool);

            if (Permission)
            {
                if (User != null)
                {
                    if ((ECOOL_APP.EF.HRMT24_ENUM.QOutSchoolList.Contains((byte)User.ROLE_TYPE) && appEndOptionLabel))
                    {


                        SelectItem.Add(new SelectListItem()
                        {
                            Text = EndoptionLabel ?? "全部",
                            Value = EndoptionVal ?? ""
                        });


                    }
                    else if (User.USER_NO == "stage") {

                        SelectItem.Add(new SelectListItem()
                        {
                            Text = "全部",
                            Value = "ALL"
                        });

                    }
                }
            }
            else
            {
                if (appEndOptionLabel)
                {
                    SelectItem.Add(new SelectListItem()
                    {
                        Text = EndoptionLabel ?? "全部",
                        Value = EndoptionVal ?? ""
                    });
                }
            }

         


            foreach (var item in dt)
            {
                SelectListItem NewDATA = new SelectListItem();
                NewDATA.Text = item.SHORT_NAME;
                NewDATA.Value = item.SCHOOL_NO;

                if (string.IsNullOrWhiteSpace(SelectedVal) == false)
                {
                    if (item.SCHOOL_NO == SelectedVal)
                    {
                        NewDATA.Selected = true;
                    }
                }

                SelectItem.Add(NewDATA);
            }

            return SelectItem;
        }


        /// <summary>
        /// 取得學校資料選單(會顯示全部)
        /// </summary>
        /// <param name="SelectedVal">預設的選項</param>
        /// <param name="SCHOOL_NO">過濾學校</param>
        /// <param name="UserProfile">UserProfile</param>
        /// <returns></returns>
        public static List<SelectListItem> GetSCHOOL_NO(string SelectedVal, string SCHOOL_NO, UserProfile User)
        {
            return GetSCHOOL_NO( SelectedVal,  SCHOOL_NO,  User,true, null, null,true);
        }

        /// <summary>
        /// 取得學校資料選單(不顯示全部)
        /// </summary>
        /// <param name="SelectedVal">預設的選項</param>
        /// <param name="SCHOOL_NO">過濾學校</param>
        /// <param name="UserProfile">UserProfile</param>
        /// <returns></returns>
        public static List<SelectListItem> GetSCHOOL_NO_NotALL(string SelectedVal, string SCHOOL_NO, UserProfile User)
        {
            return GetSCHOOL_NO(SelectedVal, SCHOOL_NO, User, false, null, null, true);
        }


        /// <summary>
        /// 取得學校資料選單 會顯示 全部學校 項選 ,Val = ALL
        /// </summary>
        /// <param name="SelectedVal">預設的選項</param>
        /// <param name="SCHOOL_NO">過濾學校</param>
        /// <param name="UserProfile">UserProfile</param>
        /// <param name="IsAllSchool">是否可跨校</param>
        /// <returns></returns>
        public static List<SelectListItem> GetSCHOOL_NO_ALL(string SelectedVal, string SCHOOL_NO, UserProfile User, bool IsAllSchool = false)
        {
            return GetSCHOOL_NO(SelectedVal, SCHOOL_NO, User, true, "全部學校", "ALL", true, IsAllSchool);
        }

        /// <summary>
        /// 取得學校資料選單 會顯示 全部學校 項選 ,Val = ALL
        /// </summary>
        /// <param name="SelectedVal">預設的選項</param>
        /// <param name="SCHOOL_NO">過濾學校</param>
        /// <param name="UserProfile">UserProfile</param>
        /// <returns></returns>
        public static List<SelectListItem> GetSCHOOL_NO_ALL_Not_P(string SelectedVal, string SCHOOL_NO)
        {
            return GetSCHOOL_NO(SelectedVal, SCHOOL_NO, null, true, "全部學校", "ALL", false);
        }

    }
}
