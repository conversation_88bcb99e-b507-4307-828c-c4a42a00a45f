﻿
using com.ecool.sqlConnection;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;



namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI31Service
    {
        public string ErrorMsg;
        private int ErrorInt = 0;


        #region 取得uQAT16清單
        public List<uQAT16> GetListData(ZZZI31ListViewModel Data, UserProfile user, int pageSize, ref int Count)
        {
            List<uQAT16> list_data = new List<uQAT16>();

            uQAT16 ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {

                sb.Append(" SELECT A.QUESTIONS_ID,A.SCHOOL_NO,B.SHORT_NAME,A.SUBJECT,A.QUESTIONS_TXT ");
                sb.Append(" ,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,A.<PERSON>_<PERSON>,<PERSON><PERSON>,A.CRE_PERSON ");
                sb.Append(" ,A.CHG_PERSON,A.CHG_DATE ");
                sb.Append(" ,A.FILE_NAME,A.STATUS ");
                sb.Append(" ,(SELECT COUNT(*) FROM QAT17 B (NOLOCK) WHERE A.QUESTIONS_ID=B.QUESTIONS_ID ) AS ANS_COUNT ");

                if (user!=null)
                {
                    sb.Append(" ,Q_wait = isnull(CASE WHEN A.STATUS = '1' ");
                    sb.Append("                  or(SELECT COUNT(*) FROM QAT17 B(NOLOCK) WHERE A.QUESTIONS_ID = B.QUESTIONS_ID and b.ANS_TYPE = 'Q' and STATUS = '1') > 0 Then ");
                    sb.AppendFormat("                         CASE WHEN dbo.fn_Permission_Use('ZZZI11', 'ANSWERS', '{0}', '{1}') = 'Y' Then '待回覆' End ",user.SCHOOL_NO,user.USER_NO);
                    sb.Append("           End, '') ");
                    sb.AppendFormat(" , A_wait = isnull(Case When A.CRE_PERSON = '{0}' Then ", user.USER_KEY);
                    sb.Append("                         Case When  A.STATUS = '2' OR (SELECT COUNT(*) FROM QAT17 B(NOLOCK) WHERE A.QUESTIONS_ID = B.QUESTIONS_ID and b.ANS_TYPE = 'A' and STATUS = '2') > 0 Then ");
                    sb.Append("                             '待讀取' End ");
                    sb.Append("            End, '')  ");
                }
                else
                {
                    sb.Append(" ,'' Q_wait,'' A_wait ");
                }
                sb.Append(" ,MEMO=Case When A.STATUS = 'Z' Then '填報已關閉' Else '' End ");



                sb.Append(" FROM QAT16 A (NOLOCK)");
                sb.Append(" INNER JOIN BDMT01 B (NOLOCK) ON A.SCHOOL_NO=B.SCHOOL_NO");
                sb.Append(" WHERE 1=1 ");
                sb.Append(" and A.STATUS <> '" + uQAT16.STATUS_Val.STATUS_N + "' ");
                

                if (Data.Search.SearchContents != string.Empty && Data.Search.SearchContents != null)
                {
                    sb.Append(" AND (");
                    sb.AppendFormat("  A.SUBJECT LIKE '%{0}%' ", Data.Search.SearchContents);
                    sb.Append(" OR ");
                    sb.AppendFormat("  A.QUESTIONS_TXT LIKE '%{0}%' ", Data.Search.SearchContents);

                    sb.Append(" )");
                }

                if (Data.Search.STATUS != string.Empty && Data.Search.STATUS !=null)
                {
                    if (Data.Search.STATUS==uQAT16.STATUS_Val.STATUS_1)
                    {
                        sb.Append(" and isnull(CASE WHEN A.STATUS = '1' ");
                        sb.Append("                  or(SELECT COUNT(*) FROM QAT17 B(NOLOCK) WHERE A.QUESTIONS_ID = B.QUESTIONS_ID and b.ANS_TYPE = 'A' and STATUS = '3') > 0 Then ");
                        sb.AppendFormat("                         CASE WHEN dbo.fn_Permission_Use('ZZZI31', 'ANSWERS', '{0}', '{1}') = 'Y' Then '待回覆' End ", user.SCHOOL_NO, user.USER_NO);
                        sb.Append("           End, '')<>'' ");
                    }

                    if (Data.Search.STATUS == uQAT16.STATUS_Val.STATUS_2)
                    {
                        sb.AppendFormat(" and isnull(Case When A.CRE_PERSON = '{0}' Then ", user.USER_KEY);
                        sb.Append("                           Case When  A.STATUS = '2' OR (SELECT COUNT(*) FROM QAT17 B(NOLOCK) WHERE A.QUESTIONS_ID = B.QUESTIONS_ID and b.ANS_TYPE = 'A' and STATUS = '2') > 0 Then");
                        sb.Append("                             '待讀取' End ");
                        sb.Append("            End, '')<>''  ");
                    }

                    if (Data.Search.STATUS == uQAT16.STATUS_Val.STATUS_Z)
                    {
                        sb.Append(" and A.STATUS = '"+ uQAT16.STATUS_Val.STATUS_Z + "' ");
                    }
                }


                    string OrderBy = "A.CRE_DATE DESC";

                if (string.IsNullOrWhiteSpace(Data.Search.OrderByName)==false)
                {
                    if (Data.Search.OrderByName == "ANS_COUNT")
                    {
                        OrderBy = " (SELECT COUNT(*) FROM QAT17 B (NOLOCK) WHERE A.QUESTIONS_ID=B.QUESTIONS_ID ) " + Data.Search.SyntaxName;
                    }
                    else
                    {
                        OrderBy = "A." + Data.Search.OrderByName + " " + Data.Search.SyntaxName;
                    }

                }

                string ThisError = "";


                dt = new sqlConnection().executeQueryBSqlDataReaderListPage(Data.Search.Page, pageSize, sb.ToString(), sb.ToString(), OrderBy, ref Count, ref ThisError);
                if (dt != null)
                {
                    foreach (DataRow dr in dt.Rows)
                    {
                        ReturnData = new uQAT16();

                        ReturnData.QUESTIONS_ID = (dr["QUESTIONS_ID"] == DBNull.Value ? "" : (string)dr["QUESTIONS_ID"]);
                        ReturnData.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                        ReturnData.SCHOOL_NAME = (dr["SHORT_NAME"] == DBNull.Value ? "" : (string)dr["SHORT_NAME"]);

                        ReturnData.SUBJECT = (dr["SUBJECT"] == DBNull.Value ? "" : (string)dr["SUBJECT"]);
                        ReturnData.QUESTIONS_TXT = (dr["QUESTIONS_TXT"] == DBNull.Value ? "" : (string)dr["QUESTIONS_TXT"]);

                        ReturnData.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                        ReturnData.NAME = (dr["NAME"] == DBNull.Value ? "" : (string)dr["NAME"]);
                        ReturnData.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                        ReturnData.E_MAIL = (dr["E_MAIL"] == DBNull.Value ? "" : (string)dr["E_MAIL"]);
                        ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                        ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);

                        ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                        ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);

                        if (dr["MEMO"].ToString()!="")
                        {
                            ReturnData.MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]);
                        }
                        else
                        {
                            if (dr["Q_wait"].ToString() != "" && (string)dr["A_wait"] != "")
                            {
                                ReturnData.MEMO = (string)dr["Q_wait"] + " / " + (string)dr["A_wait"];
                            }
                            else
                            {
                                ReturnData.MEMO = (string)dr["Q_wait"] + (string)dr["A_wait"];
                            }
                        }

                        ReturnData.FILE_NAME = (dr["FILE_NAME"] == DBNull.Value ? "" : (string)dr["FILE_NAME"]);
                        ReturnData.ANS_COUNT = (dr["ANS_COUNT"] == DBNull.Value ? (int?)null : (int)dr["ANS_COUNT"]);
                        ReturnData.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);

                        list_data.Add(ReturnData);
                    }
                }

                dt.Clear();
                dt.Dispose();

            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;

        }

        #endregion

        #region 取得.uQAT16 詳細資料
        public uQAT16 GetGetDetailsData(string QUESTIONS_ID)
        {
            uQAT16 ReturnData = new uQAT16();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT A.QUESTIONS_ID,A.SCHOOL_NO,B.SHORT_NAME,A.SUBJECT,A.QUESTIONS_TXT ");
                sb.Append(" ,A.USER_NO,A.NAME,A.SNAME,A.E_MAIL,A.CRE_DATE,A.CRE_PERSON ");
                sb.Append(" ,A.CHG_PERSON,A.CHG_DATE ");
                sb.Append(" ,A.MEMO,A.FILE_NAME,A.STATUS ");
                sb.Append(" FROM QAT16 A (NOLOCK)");
                sb.Append(" INNER JOIN BDMT01 B (NOLOCK) ON A.SCHOOL_NO=B.SCHOOL_NO");
                sb.Append(" WHERE 1=1 ");
                sb.AppendFormat(" AND A.QUESTIONS_ID= '{0}' ", QUESTIONS_ID);

                dt = new sqlConnection().executeQueryByDataTableList(sb.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    ReturnData.QUESTIONS_ID = (dr["QUESTIONS_ID"] == DBNull.Value ? "" : (string)dr["QUESTIONS_ID"]);
                    ReturnData.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                    ReturnData.SCHOOL_NAME = (dr["SHORT_NAME"] == DBNull.Value ? "" : (string)dr["SHORT_NAME"]);

                    ReturnData.SUBJECT = (dr["SUBJECT"] == DBNull.Value ? "" : (string)dr["SUBJECT"]);
                    ReturnData.QUESTIONS_TXT = (dr["QUESTIONS_TXT"] == DBNull.Value ? "" : (string)dr["QUESTIONS_TXT"]);

                    ReturnData.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                    ReturnData.NAME = (dr["NAME"] == DBNull.Value ? "" : (string)dr["NAME"]);
                    ReturnData.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                    ReturnData.E_MAIL = (dr["E_MAIL"] == DBNull.Value ? "" : (string)dr["E_MAIL"]);
                    ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                    ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);

                    ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                    ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                    ReturnData.MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]);
                    ReturnData.FILE_NAME = (dr["FILE_NAME"] == DBNull.Value ? "" : (string)dr["FILE_NAME"]);
                    ReturnData.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);
                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();

            }
            catch (Exception exception)
            {
                throw exception;
            }

            return ReturnData;
        }
        #endregion

        #region 取得uQAT17 List

        public List<uQAT17> GetListQAT17Data(string QUESTIONS_ID, int Page, int pageSize, ref int Count)
        {
            List<uQAT17> list_data = new List<uQAT17>();

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {

                sb.Append(" SELECT A.QUESTIONS_ID,A.ITEM_NO,A.ANS_TYPE,A.SCHOOL_NO,B.SHORT_NAME,A.ANSWERS ");
                sb.Append(", A.USER_NO,A.NAME,A.SNAME,A.E_MAIL,A.CRE_DATE,A.CRE_PERSON  ");
                sb.Append(", A.CHG_PERSON,A.CHG_DATE  ");
                sb.Append(", A.MEMO,A.FILE_NAME,A.STATUS  ");
                sb.Append(" FROM QAT17 A  (nolock) ");
                sb.Append(" INNER JOIN BDMT01 B (NOLOCK) ON A.SCHOOL_NO=B.SCHOOL_NO");
                sb.AppendFormat(" Where A.QUESTIONS_ID= '{0}' ", QUESTIONS_ID);

                string OrderBy = "A.ITEM_NO";
                string ThisError = "";

                dt = new sqlConnection().executeQueryBSqlDataReaderListPage(Page, pageSize, sb.ToString(), sb.ToString(), OrderBy, ref Count, ref ThisError);
                if (dt != null)
                {
                    foreach (DataRow dr in dt.Rows)
                    {
                        uQAT17 Item = new uQAT17();

                        Item.QUESTIONS_ID = (dr["QUESTIONS_ID"] == DBNull.Value ? "" : (string)dr["QUESTIONS_ID"]);
                        Item.ITEM_NO = (dr["ITEM_NO"] == DBNull.Value ? (int?)null : (int)dr["ITEM_NO"]);
                        Item.ANS_TYPE = (dr["ANS_TYPE"] == DBNull.Value ? "" : (string)dr["ANS_TYPE"]);
                        Item.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                        Item.SCHOOL_NAME = (dr["SHORT_NAME"] == DBNull.Value ? "" : (string)dr["SHORT_NAME"]);

                        Item.ANSWERS = (dr["ANSWERS"] == DBNull.Value ? "" : (string)dr["ANSWERS"]);

                        Item.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                        Item.NAME = (dr["NAME"] == DBNull.Value ? "" : (string)dr["NAME"]);
                        Item.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                        Item.E_MAIL = (dr["E_MAIL"] == DBNull.Value ? "" : (string)dr["E_MAIL"]);
                        Item.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                        Item.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);

                        Item.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                        Item.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                        Item.MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]);
                        Item.FILE_NAME = (dr["FILE_NAME"] == DBNull.Value ? "" : (string)dr["FILE_NAME"]);
                        Item.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);

                        list_data.Add(Item);
                    }
                }

                dt.Clear();
                dt.Dispose();

            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;

        }

        #endregion

        #region GetDetailQAT17Data

        public uQAT17 GetDetailQAT17Data(string QUESTIONS_ID, string ITEM_NO)
        {
            uQAT17 ReturnData = new uQAT17();

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT A.QUESTIONS_ID,A.ITEM_NO,A.ANS_TYPE,A.SCHOOL_NO,B.SHORT_NAME,A.ANSWERS ");
                sb.Append(", A.USER_NO,A.NAME,A.SNAME,A.E_MAIL,A.CRE_DATE,A.CRE_PERSON  ");
                sb.Append(", A.CHG_PERSON,A.CHG_DATE  ");
                sb.Append(", A.MEMO,A.FILE_NAME,A.STATUS  ");
                sb.Append(" FROM QAT17 A  (nolock) ");
                sb.Append(" INNER JOIN BDMT01 B (NOLOCK) ON A.SCHOOL_NO=B.SCHOOL_NO");
                sb.AppendFormat(" Where A.QUESTIONS_ID= '{0}' ", QUESTIONS_ID);
                sb.AppendFormat(" AND A.ITEM_NO= {0} ", ITEM_NO);

                dt = new sqlConnection().executeQueryByDataTableList(sb.ToString());
                if (dt != null)
                {
                    foreach (DataRow dr in dt.Rows)
                    {
                        ReturnData.QUESTIONS_ID = (dr["QUESTIONS_ID"] == DBNull.Value ? "" : (string)dr["QUESTIONS_ID"]);
                        ReturnData.ITEM_NO = (dr["ITEM_NO"] == DBNull.Value ? (int?)null : (int)dr["ITEM_NO"]);
                        ReturnData.ANS_TYPE = (dr["ANS_TYPE"] == DBNull.Value ? "" : (string)dr["ANS_TYPE"]);
                        ReturnData.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                        ReturnData.SCHOOL_NAME = (dr["SHORT_NAME"] == DBNull.Value ? "" : (string)dr["SHORT_NAME"]);
                        ReturnData.ANSWERS = (dr["ANSWERS"] == DBNull.Value ? "" : (string)dr["ANSWERS"]);

                        ReturnData.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                        ReturnData.NAME = (dr["NAME"] == DBNull.Value ? "" : (string)dr["NAME"]);
                        ReturnData.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                        ReturnData.E_MAIL = (dr["E_MAIL"] == DBNull.Value ? "" : (string)dr["E_MAIL"]);
                        ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                        ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);

                        ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                        ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                        ReturnData.MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]);
                        ReturnData.FILE_NAME = (dr["FILE_NAME"] == DBNull.Value ? "" : (string)dr["FILE_NAME"]);
                        ReturnData.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);
                    }
                }

                dt.Clear();
                dt.Dispose();

            }
            catch (Exception exception)
            {
                throw exception;
            }
            return ReturnData;

        }

        #endregion

        #region 新增QAT16 問題反應維護主檔
        public void CreateDateQAT16(uQAT16 Date)
        {

            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {

                    Date.QUESTIONS_ID = this.GetNewQUESTIONS_ID(Date.SCHOOL_NO, cmd);

                    this.INSERT_NTO_QAT16(conn, transaction, Date);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        Date.QUESTIONS_ID = null;
                        transaction.Rollback();
                        ErrorMsg = "新增資料失敗;\r\n" + ErrorMsg;
                    }

                }
                catch (Exception ex)
                {
                    Date.QUESTIONS_ID = null;
                    transaction.Rollback();
                    ErrorMsg = "新增資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion

        #region 修改問題資料 QAT16
        public void UpDate(uQAT16 Date)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {

                    this.UPDATE_SET_QAT16(conn, transaction, Date);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "修改資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "修改資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion

        #region 刪除全部資料 QAT16、ADDT1７
        public void DelAllDate(string QUESTIONS_ID)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {

                    this.DELETE_QAT16(conn, transaction, QUESTIONS_ID);
                    this.DELETE_QAT17(conn, transaction, QUESTIONS_ID);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "刪除資料處理;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "刪除資料處理;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }
        #endregion

        #region 清空檔名　QAT16 OR ADDT1７
        public void FILE_NAME_Empty(string QUESTIONS_ID, string ITEM_NO)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {

                    if (ITEM_NO != string.Empty && ITEM_NO != null)
                    {
                        this.UPDATE_SET_QAT17_FILE_NAME_Empty(conn, transaction, QUESTIONS_ID, ITEM_NO);
                    }
                    else
                    {
                        this.UPDATE_SET_QAT16_FILE_NAME_Empty(conn, transaction, QUESTIONS_ID);
                    }



                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "刪除附件處理;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "刪除附件處理;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }
        #endregion

        #region 新增QAT17 新增 回覆問題

        public int QCount(string SchoolNO,string UserNO)
        {
            int Result = 0;
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                cmd.Connection = conn;
                cmd.CommandText = string.Format(
                    "SELECT count(*) FROM [QAT16]  where status <> 'Z' and status <>'N' and QUESTIONS_ID not in ( select QUESTIONS_ID from QAT17 where SCHOOL_NO='{0}' and USER_NO='{1}') "
                    , SchoolNO, UserNO);

                try
                {
                   object ans=  cmd.ExecuteScalar();
                   Result = Convert.ToInt32(ans);
                }
                catch 
                {
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
            return Result;
        }
        
        public int ACount(string SchoolNO, string UserNO)
        {
            int Result = 0;
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                cmd.Connection = conn;
                cmd.CommandText = string.Format(
                    "SELECT count(*) FROM [QAT16]  where status <> 'Z' and status <>'N' and SCHOOL_NO='{0}' and USER_NO='{1}' and QUESTIONS_ID  in ( select QUESTIONS_ID from QAT17 where status<>3)"
                    , SchoolNO, UserNO);

                try
                {
                    object ans = cmd.ExecuteScalar();
                    Result = Convert.ToInt32(ans);
                }
                catch
                {
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
            return Result;
        }


        public void CreateDateQAT17(uQAT17 Date)
        {

            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {

                    Date.ITEM_NO = this.GetNewITEM_NO(Date.QUESTIONS_ID, cmd);

                    this.INSERT_NTO_QAT17(conn, transaction, Date);

                    if (Date.ANS_TYPE== uQAT17.ANS_TYPE_Val.ANS_TYPE_A)
                    {
                        UPDATE_SET_QAT17_ANS_TYPE_Q_STATUS(conn, transaction, Date.QUESTIONS_ID);
                    }


                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        Date.QUESTIONS_ID = null;
                        transaction.Rollback();
                        ErrorMsg = "新增回覆資料失敗;\r\n" + ErrorMsg;
                    }

                }
                catch (Exception ex)
                {
                    Date.QUESTIONS_ID = null;
                    transaction.Rollback();
                    ErrorMsg = "新增回覆資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion


        #region 更新 讀取 狀態

        public void Update_A_STATUS(string QUESTIONS_ID)
        {

            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    this.UPDATE_SET_QAT17_ANS_TYPE_A_STATUS(conn, transaction, QUESTIONS_ID);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "更新讀取狀態失敗;\r\n" + ErrorMsg;
                    }

                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "更新讀取狀態失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion

        #region 更新 結案/作廢

        public void Update_STATUS(string QUESTIONS_ID, string Action)
        {

            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    if (Action== "CLOSE")
                    {
                        this.UPDATE_SET_QAT16_STATUS(conn, transaction, QUESTIONS_ID, uQAT16.STATUS_Val.STATUS_Z);
                    }
                    else if (Action == "INVALID")
                    {
                        this.UPDATE_SET_QAT16_STATUS(conn, transaction, QUESTIONS_ID, uQAT16.STATUS_Val.STATUS_N);
                    }
                  

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "更新狀態失敗;\r\n" + ErrorMsg;
                    }

                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "更新態失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion

        #region 修改回覆資料 QAT17
        public void UpQAT17Date(uQAT17 Date)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {

                    this.UPDATE_SET_QAT17(conn, transaction, Date);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "修改回覆資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "修改回覆資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion

        #region 刪除回覆資料 QAT17
        public void DelQAT17Date(string QUESTIONS_ID, string ITEM_NO)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {

                    this.DELETE_QAT17_ITEM_NO(conn, transaction, QUESTIONS_ID, ITEM_NO);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "刪除回覆資料處理;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "刪除回覆資料處理;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }
        #endregion

        #region INSERT INTO QAT16
        private void INSERT_NTO_QAT16(SqlConnection conn, SqlTransaction transaction, uQAT16 Date)
        {
            IDbCommand cmd = new SqlCommand(@"INSERT INTO QAT16 ( QUESTIONS_ID,SCHOOL_NO,SUBJECT,QUESTIONS_TXT,USER_NO,NAME,SNAME,E_MAIL
            ,CRE_DATE,CRE_PERSON,CHG_PERSON,CHG_DATE,MEMO,FILE_NAME,STATUS) 
            VALUES (@QUESTIONS_ID,@SCHOOL_NO,@SUBJECT,@QUESTIONS_TXT,@USER_NO,@NAME,@SNAME,@E_MAIL,@CRE_DATE,@CRE_PERSON,@CHG_PERSON,@CHG_DATE,@MEMO,@FILE_NAME,@STATUS)");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Date.QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", Date.QUESTIONS_ID));

            cmd.Parameters.Add(
            (Date.SCHOOL_NO == null)
            ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
            : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO));

            cmd.Parameters.Add(
            (Date.SUBJECT == null)
            ? new SqlParameter("@SUBJECT", DBNull.Value)
            : new SqlParameter("@SUBJECT", Date.SUBJECT));

            cmd.Parameters.Add(
            (Date.QUESTIONS_TXT == null)
            ? new SqlParameter("@QUESTIONS_TXT", DBNull.Value)
            : new SqlParameter("@QUESTIONS_TXT", Date.QUESTIONS_TXT));

            cmd.Parameters.Add(
            (Date.USER_NO == null)
            ? new SqlParameter("@USER_NO", DBNull.Value)
            : new SqlParameter("@USER_NO", Date.USER_NO));

            cmd.Parameters.Add(
            (Date.NAME == null)
            ? new SqlParameter("@NAME", DBNull.Value)
            : new SqlParameter("@NAME", Date.NAME));

            cmd.Parameters.Add(
            (Date.SNAME == null)
            ? new SqlParameter("@SNAME", DBNull.Value)
            : new SqlParameter("@SNAME", Date.SNAME));

            cmd.Parameters.Add(
            (Date.E_MAIL == null)
            ? new SqlParameter("@E_MAIL", DBNull.Value)
            : new SqlParameter("@E_MAIL", Date.E_MAIL));

            cmd.Parameters.Add(
            (Date.CRE_DATE == null)
            ? new SqlParameter("@CRE_DATE", DBNull.Value)
            : new SqlParameter("@CRE_DATE", Date.CRE_DATE));

            cmd.Parameters.Add(
            (Date.CRE_PERSON == null)
            ? new SqlParameter("@CRE_PERSON", DBNull.Value)
            : new SqlParameter("@CRE_PERSON", Date.CRE_PERSON));

            cmd.Parameters.Add(
            (Date.CHG_PERSON == null)
            ? new SqlParameter("@CHG_PERSON", DBNull.Value)
            : new SqlParameter("@CHG_PERSON", Date.CHG_PERSON));

            cmd.Parameters.Add(
            (Date.CHG_DATE == null)
            ? new SqlParameter("@CHG_DATE", DBNull.Value)
            : new SqlParameter("@CHG_DATE", Date.CHG_DATE));

            cmd.Parameters.Add(
            (Date.MEMO == null)
            ? new SqlParameter("@MEMO", DBNull.Value)
            : new SqlParameter("@MEMO", Date.MEMO));

            cmd.Parameters.Add(
            (Date.FILE_NAME == null)
            ? new SqlParameter("@FILE_NAME", DBNull.Value)
            : new SqlParameter("@FILE_NAME", Date.FILE_NAME));


            cmd.Parameters.Add(
            (Date.STATUS == null)
            ? new SqlParameter("@STATUS", DBNull.Value)
            : new SqlParameter("@STATUS", Date.STATUS));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "INSERT_NTO_QAT16 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }
        #endregion

        #region UPDATE SET QAT16
        private void UPDATE_SET_QAT16(SqlConnection conn, SqlTransaction transaction, uQAT16 Item)
        {
            IDbCommand cmd = new SqlCommand(@" UPDATE QAT16 set 
            SUBJECT=@SUBJECT,QUESTIONS_TXT=@QUESTIONS_TXT
            ,USER_NO=@USER_NO,NAME=@NAME,SNAME=@SNAME
            ,E_MAIL=@E_MAIL,CHG_PERSON=@CHG_PERSON,CHG_DATE=@CHG_DATE,MEMO=@MEMO
            ,FILE_NAME=@FILE_NAME
            Where QUESTIONS_ID=@QUESTIONS_ID
            ");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Item.QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", Item.QUESTIONS_ID));



            cmd.Parameters.Add(
            (Item.SUBJECT == null)
            ? new SqlParameter("@SUBJECT", DBNull.Value)
            : new SqlParameter("@SUBJECT", Item.SUBJECT));

            cmd.Parameters.Add(
            (Item.QUESTIONS_TXT == null)
            ? new SqlParameter("@QUESTIONS_TXT", DBNull.Value)
            : new SqlParameter("@QUESTIONS_TXT", Item.QUESTIONS_TXT));

            cmd.Parameters.Add(
            (Item.USER_NO == null)
            ? new SqlParameter("@USER_NO", DBNull.Value)
            : new SqlParameter("@USER_NO", Item.USER_NO));

            cmd.Parameters.Add(
            (Item.NAME == null)
            ? new SqlParameter("@NAME", DBNull.Value)
            : new SqlParameter("@NAME", Item.NAME));

            cmd.Parameters.Add(
            (Item.SNAME == null)
            ? new SqlParameter("@SNAME", DBNull.Value)
            : new SqlParameter("@SNAME", Item.SNAME));

            cmd.Parameters.Add(
            (Item.E_MAIL == null)
            ? new SqlParameter("@E_MAIL", DBNull.Value)
            : new SqlParameter("@E_MAIL", Item.E_MAIL));


            cmd.Parameters.Add(
            (Item.CHG_PERSON == null)
            ? new SqlParameter("@CHG_PERSON", DBNull.Value)
            : new SqlParameter("@CHG_PERSON", Item.CHG_PERSON));

            cmd.Parameters.Add(
            (Item.CHG_DATE == null)
            ? new SqlParameter("@CHG_DATE", DBNull.Value)
            : new SqlParameter("@CHG_DATE", Item.CHG_DATE));

            cmd.Parameters.Add(
            (Item.MEMO == null)
            ? new SqlParameter("@MEMO", DBNull.Value)
            : new SqlParameter("@MEMO", Item.MEMO));

            cmd.Parameters.Add(
            (Item.FILE_NAME == null)
            ? new SqlParameter("@FILE_NAME", DBNull.Value)
            : new SqlParameter("@FILE_NAME", Item.FILE_NAME));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_QAT16 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }

        }
        #endregion

        #region UPDATE_SET_QAT16_FILE_NAME_Empty
        private void UPDATE_SET_QAT16_FILE_NAME_Empty(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID)
        {
            IDbCommand cmd = new SqlCommand(@" UPDATE QAT16 set 
            ,FILE_NAME=@FILE_NAME
            Where QUESTIONS_ID=@QUESTIONS_ID
            ");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", QUESTIONS_ID));

            cmd.Parameters.Add(new SqlParameter("@FILE_NAME", DBNull.Value));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_QAT16_FILE_NAME_Empty 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }

        }
        #endregion

        #region DELETE QAT16
        private void DELETE_QAT16(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID)
        {
            IDbCommand cmd = new SqlCommand(@" DELETE QAT16 Where QUESTIONS_ID=@QUESTIONS_ID");
            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", QUESTIONS_ID));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "DELETE_QAT16 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }
        #endregion

        #region INSERT INTO QAT17
        private void INSERT_NTO_QAT17(SqlConnection conn, SqlTransaction transaction, uQAT17 Date)
        {
            IDbCommand cmd = new SqlCommand(@"INSERT INTO QAT17 ( QUESTIONS_ID,ITEM_NO,ANS_TYPE,SCHOOL_NO,ANSWERS,USER_NO,NAME,SNAME,E_MAIL,CRE_DATE,CRE_PERSON,CHG_PERSON,CHG_DATE,MEMO,FILE_NAME,STATUS) 
            VALUES (@QUESTIONS_ID,@ITEM_NO,@ANS_TYPE,@SCHOOL_NO,@ANSWERS,@USER_NO,@NAME,@SNAME,@E_MAIL,@CRE_DATE,@CRE_PERSON,@CHG_PERSON,@CHG_DATE,@MEMO,@FILE_NAME,@STATUS)");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Date.QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", Date.QUESTIONS_ID));

            cmd.Parameters.Add(
            (Date.ITEM_NO == null)
            ? new SqlParameter("@ITEM_NO", DBNull.Value)
            : new SqlParameter("@ITEM_NO", Date.ITEM_NO));

            cmd.Parameters.Add(
            (Date.ANS_TYPE == null)
            ? new SqlParameter("@ANS_TYPE", DBNull.Value)
            : new SqlParameter("@ANS_TYPE", Date.ANS_TYPE));


            cmd.Parameters.Add(
            (Date.SCHOOL_NO == null)
            ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
            : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO));

            cmd.Parameters.Add(
            (Date.ANSWERS == null)
            ? new SqlParameter("@ANSWERS", DBNull.Value)
            : new SqlParameter("@ANSWERS", Date.ANSWERS));

            cmd.Parameters.Add(
            (Date.USER_NO == null)
            ? new SqlParameter("@USER_NO", DBNull.Value)
            : new SqlParameter("@USER_NO", Date.USER_NO));

            cmd.Parameters.Add(
            (Date.NAME == null)
            ? new SqlParameter("@NAME", DBNull.Value)
            : new SqlParameter("@NAME", Date.NAME));

            cmd.Parameters.Add(
            (Date.SNAME == null)
            ? new SqlParameter("@SNAME", DBNull.Value)
            : new SqlParameter("@SNAME", Date.SNAME));

            cmd.Parameters.Add(
            (Date.E_MAIL == null)
            ? new SqlParameter("@E_MAIL", DBNull.Value)
            : new SqlParameter("@E_MAIL", Date.E_MAIL));

            cmd.Parameters.Add(
            (Date.CRE_DATE == null)
            ? new SqlParameter("@CRE_DATE", DBNull.Value)
            : new SqlParameter("@CRE_DATE", Date.CRE_DATE));

            cmd.Parameters.Add(
            (Date.CRE_PERSON == null)
            ? new SqlParameter("@CRE_PERSON", DBNull.Value)
            : new SqlParameter("@CRE_PERSON", Date.CRE_PERSON));

            cmd.Parameters.Add(
            (Date.CHG_PERSON == null)
            ? new SqlParameter("@CHG_PERSON", DBNull.Value)
            : new SqlParameter("@CHG_PERSON", Date.CHG_PERSON));

            cmd.Parameters.Add(
            (Date.CHG_DATE == null)
            ? new SqlParameter("@CHG_DATE", DBNull.Value)
            : new SqlParameter("@CHG_DATE", Date.CHG_DATE));

            cmd.Parameters.Add(
            (Date.MEMO == null)
            ? new SqlParameter("@MEMO", DBNull.Value)
            : new SqlParameter("@MEMO", Date.MEMO));

            cmd.Parameters.Add(
            (Date.FILE_NAME == null)
            ? new SqlParameter("@FILE_NAME", DBNull.Value)
            : new SqlParameter("@FILE_NAME", Date.FILE_NAME));

            cmd.Parameters.Add(
            (Date.STATUS == null)
            ? new SqlParameter("@STATUS", DBNull.Value)
            : new SqlParameter("@STATUS", Date.STATUS));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "INSERT_NTO_QAT17 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }
        #endregion

        #region UPDATE SET QAT17

        private void UPDATE_SET_QAT17(SqlConnection conn, SqlTransaction transaction, uQAT17 Item)
        {
            IDbCommand cmd = new SqlCommand(@" UPDATE QAT17 set 
                                             ANSWERS=@ANSWERS
                                            ,USER_NO=@USER_NO,NAME=@NAME,SNAME=@SNAME
                                            ,E_MAIL=@E_MAIL
                                            ,CHG_PERSON=@CHG_PERSON,CHG_DATE=@CHG_DATE
                                            ,MEMO=@MEMO,FILE_NAME=@FILE_NAME
                                            WHERE QUESTIONS_ID=@QUESTIONS_ID AND ITEM_NO=@ITEM_NO
                                            ");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Item.QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", Item.QUESTIONS_ID));

            cmd.Parameters.Add(
            (Item.ITEM_NO == null)
            ? new SqlParameter("@ITEM_NO", DBNull.Value)
            : new SqlParameter("@ITEM_NO", Item.ITEM_NO));


            cmd.Parameters.Add(
            (Item.ANSWERS == null)
            ? new SqlParameter("@ANSWERS", DBNull.Value)
            : new SqlParameter("@ANSWERS", Item.ANSWERS));

            cmd.Parameters.Add(
            (Item.USER_NO == null)
            ? new SqlParameter("@USER_NO", DBNull.Value)
            : new SqlParameter("@USER_NO", Item.USER_NO));

            cmd.Parameters.Add(
            (Item.NAME == null)
            ? new SqlParameter("@NAME", DBNull.Value)
            : new SqlParameter("@NAME", Item.NAME));

            cmd.Parameters.Add(
            (Item.SNAME == null)
            ? new SqlParameter("@SNAME", DBNull.Value)
            : new SqlParameter("@SNAME", Item.SNAME));

            cmd.Parameters.Add(
            (Item.E_MAIL == null)
            ? new SqlParameter("@E_MAIL", DBNull.Value)
            : new SqlParameter("@E_MAIL", Item.E_MAIL));


            cmd.Parameters.Add(
            (Item.CHG_PERSON == null)
            ? new SqlParameter("@CHG_PERSON", DBNull.Value)
            : new SqlParameter("@CHG_PERSON", Item.CHG_PERSON));

            cmd.Parameters.Add(
            (Item.CHG_DATE == null)
            ? new SqlParameter("@CHG_DATE", DBNull.Value)
            : new SqlParameter("@CHG_DATE", Item.CHG_DATE));

            cmd.Parameters.Add(
            (Item.MEMO == null)
            ? new SqlParameter("@MEMO", DBNull.Value)
            : new SqlParameter("@MEMO", Item.MEMO));

            cmd.Parameters.Add(
            (Item.FILE_NAME == null)
            ? new SqlParameter("@FILE_NAME", DBNull.Value)
            : new SqlParameter("@FILE_NAME", Item.FILE_NAME));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_QAT17 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }

        }
        #endregion

        #region DELETE QAT17
        private void DELETE_QAT17(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID)
        {
            IDbCommand cmd = new SqlCommand(@" DELETE QAT17 Where QUESTIONS_ID=@QUESTIONS_ID");
            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", QUESTIONS_ID));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "DELETE_QAT17 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }
        #endregion

        #region DELETE_QAT17_ITEM_NO
        private void DELETE_QAT17_ITEM_NO(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID, string ITEM_NO)
        {
            IDbCommand cmd = new SqlCommand(@" DELETE QAT17 Where QUESTIONS_ID=@QUESTIONS_ID AND ITEM_NO=@ITEM_NO");
            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", QUESTIONS_ID));

            cmd.Parameters.Add(
            (ITEM_NO == null)
            ? new SqlParameter("@ITEM_NO", DBNull.Value)
            : new SqlParameter("@ITEM_NO", ITEM_NO));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "DELETE_QAT17_ITEM_NO 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }
        #endregion

        #region UPDATE_SET_QAT17_FILE_NAME_Empty
        private void UPDATE_SET_QAT17_FILE_NAME_Empty(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID, string ITEM_NO)
        {
            IDbCommand cmd = new SqlCommand(@" UPDATE QAT17 set 
            ,FILE_NAME=@FILE_NAME
             Where QUESTIONS_ID=@QUESTIONS_ID AND ITEM_NO=@ITEM_NO
            ");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (QUESTIONS_ID == null)
            ? new SqlParameter("@QUESTIONS_ID", DBNull.Value)
            : new SqlParameter("@QUESTIONS_ID", QUESTIONS_ID));

            cmd.Parameters.Add(
            (ITEM_NO == null)
            ? new SqlParameter("@ITEM_NO", DBNull.Value)
            : new SqlParameter("@ITEM_NO", ITEM_NO));

            cmd.Parameters.Add(new SqlParameter("@FILE_NAME", DBNull.Value));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_QAT17_FILE_NAME_Empty 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }

        }
        #endregion

        #region UPDATE_SET_QAT17_ANS_TYPE_Q_STATUS 更新發問狀態 未回覆==>已回覆/未讀取
        private void UPDATE_SET_QAT17_ANS_TYPE_Q_STATUS(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID)
        {

            string sSQL = " UPDATE QAT16 SET STATUS='" + uQAT17.STATUS_Val.STATUS_2 + "'  Where QUESTIONS_ID='" + QUESTIONS_ID + "' AND STATUS='"+ uQAT17.STATUS_Val.STATUS_1 +"' ";
            sSQL = sSQL + " UPDATE QAT17 SET STATUS='" + uQAT17.STATUS_Val.STATUS_2 + "'  Where QUESTIONS_ID='" + QUESTIONS_ID + "' AND STATUS='" + uQAT17.STATUS_Val.STATUS_1 + "' ";
            SqlCommand cmd = new SqlCommand(sSQL);

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_QAT17_ANS_TYPE_Q_STATUS 更新發問狀態 未回覆==>已回覆 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }

        }
        #endregion

        #region UPDATE_SET_QAT17_ANS_TYPE_A_STATUS 更新回覆狀態 已回覆/未讀取==>已讀取
        public void UPDATE_SET_QAT17_ANS_TYPE_A_STATUS(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID)
        {

            string sSQL = " UPDATE QAT16 SET STATUS='" + uQAT17.STATUS_Val.STATUS_3 + "' Where QUESTIONS_ID='" + QUESTIONS_ID + "' AND STATUS='" + uQAT17.STATUS_Val.STATUS_2 + "' ";
            sSQL = sSQL + " UPDATE QAT17 SET STATUS='" + uQAT17.STATUS_Val.STATUS_3 + "' Where QUESTIONS_ID='" + QUESTIONS_ID + "' AND STATUS='" + uQAT17.STATUS_Val.STATUS_2 + "' ";
            SqlCommand cmd = new SqlCommand(sSQL);

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_QAT17_ANS_TYPE_Q_STATUS 更新發問狀態 未回覆==>已回覆 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }

        }
        #endregion

        #region UPDATE_SET_QAT16_STATUS 更新狀態
        private void UPDATE_SET_QAT16_STATUS(SqlConnection conn, SqlTransaction transaction, string QUESTIONS_ID,string STATUS)
        {

            string sSQL = " UPDATE QAT16 SET STATUS='" + STATUS + "'  Where QUESTIONS_ID='" + QUESTIONS_ID + "' ";
          
            SqlCommand cmd = new SqlCommand(sSQL);

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_QAT16_STATUS 更新狀態 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }

        }
        #endregion

        #region 取得新的ID
        /// <summary>
        /// 取得得新的ID
        /// </summary>
        /// <param name="SCHOOL_NO">學校代碼</param>
        /// <param name="cmd">SqlCommand</param>
        /// <returns></returns>
        private string GetNewQUESTIONS_ID(string SCHOOL_NO, SqlCommand cmd)
        {
            try
            {
                string NUM = "001";
                //ID = SCHOOL_NO+ 年 月 日+3 碼流水號
                string sSQL;

                sSQL = " SELECT RIGHT('00'+CONVERT(nvarchar(3),ISNULL(MAX(RIGHT(A.QUESTIONS_ID,3)),0)+1),3) AS NUM ";
                sSQL = sSQL + " FROM QAT16 A ";
                sSQL = sSQL + "  WHERE 1=1 ";
                sSQL = sSQL + "  and CONVERT(varchar(10),A.CRE_DATE,112)='" + DateTime.Now.ToString("yyyyMMdd") + "'  ";
                sSQL = sSQL + "  and A.SCHOOL_NO='" + SCHOOL_NO + "'";
                cmd.CommandText = sSQL;
                var drDate_Header = cmd.ExecuteReader();


                if (drDate_Header.HasRows == true)
                {
                    while (drDate_Header.Read())
                    {
                        NUM = drDate_Header["NUM"].ToString();
                    }
                }

                drDate_Header.Close();

                string ID = string.Empty;

                if (string.IsNullOrWhiteSpace(SCHOOL_NO) == false)
                {
                    ID = SCHOOL_NO + "_" + DateTime.Now.ToString("yyyyMMdd") + NUM;
                }
                else
                {
                    ID = DateTime.Now.ToString("yyyyMMdd") + NUM;
                }

                return ID;
            }
            catch (Exception ex)
            {
                ErrorMsg = " 取得新的QUESTIONS_ID 失敗;\r\n" + ex.Message;
                return null;
            }
        }

        #endregion

        #region 取得新的Item_No
        /// <summary>
        /// 取得得新的ID
        /// </summary>
        /// <param name="QUESTIONS_ID"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        private int? GetNewITEM_NO(string QUESTIONS_ID, SqlCommand cmd)
        {
            try
            {
                int NUM = 0;

                string sSQL = " SELECT ISNULL(MAX(ITEM_NO),0)+1 NUM FROM QAT17 WHERE QUESTIONS_ID='" + QUESTIONS_ID + "' ";
                cmd.CommandText = sSQL;
                var drDate_Header = cmd.ExecuteReader();


                if (drDate_Header.HasRows == true)
                {
                    while (drDate_Header.Read())
                    {
                        NUM = (int)drDate_Header["NUM"];
                    }
                }

                drDate_Header.Close();

                return NUM;
            }
            catch (Exception ex)
            {
                ErrorMsg = " 取得新的ITEM_NO 失敗;\r\n" + ex.Message;
                return null;
            }
        }

        #endregion

        #region 取得最小發問 ITEM_NO 
        /// <summary>
        /// 取得最小發問 ITEM_NO 
        /// </summary>
        /// <param name="QUESTIONS_ID"></param>
        /// <param name="cmd"></param>
        /// <returns>回傳0代表是QAT16</returns>
        private int? GetMinITEM_NO(string QUESTIONS_ID, SqlCommand cmd)
        {
            try
            {
                int NUM = 0;

                string sSQL = " SELECT ISNULL(MIN(ITEM_NO),0) NUM FROM QAT17 WHERE QUESTIONS_ID='" + QUESTIONS_ID + "' and ANS_TYPE='2' ";
                cmd.CommandText = sSQL;
                var drDate_Header = cmd.ExecuteReader();


                if (drDate_Header.HasRows == true)
                {
                    while (drDate_Header.Read())
                    {
                        NUM = (int)drDate_Header["NUM"];
                    }
                }

                drDate_Header.Close();

                return NUM;
            }
            catch (Exception ex)
            {
                ErrorMsg = " 取得最小發問 ITEM_NO  失敗;\r\n" + ex.Message;
                return null;
            }
        }

        #endregion


    }
}
