/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Main/Bold/MiscSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Main-bold"],{9824:[719,130,894,64,829,"675 -18Q536 -18 527 62V70H477V55Q479 14 487 -21T502 -75T509 -101Q509 -120 491 -127Q487 -129 447 -129Q446 -129 439 -129T427 -130Q384 -130 384 -101Q384 -95 391 -76T406 -21T416 55V70H366Q364 52 360 40T342 14T300 -8T230 -17H218Q110 -17 75 117Q64 163 64 209Q64 290 116 357T261 495Q363 574 414 690Q425 719 445 719Q467 719 478 693Q507 627 547 578T623 503T702 438T777 357Q829 285 829 202V197Q826 128 808 81T762 15T714 -11T675 -18"],9825:[711,24,894,65,828,"65 491Q65 602 121 656T246 710Q375 710 440 624L447 615Q519 711 638 711Q723 711 775 652T828 491Q828 390 770 313T581 129Q539 95 514 63T483 14T469 -13T446 -24Q434 -24 427 -17T416 0T400 32T371 74Q352 97 310 131T229 199T151 276T89 374T65 491ZM249 649Q188 649 157 603T125 489Q125 409 181 338T352 176Q408 131 437 87L446 73L456 87Q479 121 507 147T579 207T659 278Q768 387 768 489Q768 506 766 524T756 566T731 611T687 642Q668 649 638 649Q609 649 593 644Q547 633 516 604T478 534Q473 505 447 505H445Q420 505 416 534Q407 577 372 608T285 648Q277 649 249 649"],9826:[719,154,894,64,828,"409 686Q410 688 412 691T415 696T418 701T421 706T424 709T427 713T431 715T435 717T440 718T446 719Q455 719 460 717T472 704T488 679T516 633T563 567Q624 485 687 422T787 330T826 296T828 282Q828 270 825 265T801 245Q696 161 612 59T477 -133Q465 -154 447 -154Q439 -154 434 -152T425 -146T414 -130T399 -104T372 -62T330 -3Q270 78 207 142T107 234T70 265Q64 274 64 282Q64 296 90 317Q284 472 409 686ZM749 282Q745 286 721 307T681 343T635 388T581 446T525 516T465 601Q462 606 457 613T450 624L447 627V628Q446 628 436 611T402 561T348 489T266 396T155 292L145 282Q147 280 185 245T257 177T343 79T442 -57Q446 -64 447 -64V-63Q450 -59 475 -22T530 56T619 160T749 282"],9827:[719,130,894,32,861,"240 527Q240 611 301 665T446 719T590 665T652 527Q652 431 571 373Q578 363 584 352T593 335T597 329L604 335Q611 341 617 345T637 356T667 366Q672 366 680 367T694 368Q767 368 814 310T861 177Q861 109 819 57T713 -12Q690 -17 656 -17Q535 -13 527 62V70H477V55Q479 14 487 -21T502 -75T509 -101Q509 -120 491 -127Q487 -129 447 -129Q446 -129 439 -129T427 -130Q384 -130 384 -101Q384 -95 391 -76T406 -21T416 55V70H366V62Q356 -12 237 -17Q130 -17 71 60Q32 111 32 178Q32 251 78 309T198 368Q217 368 233 364T260 354T279 343T291 333T296 329L300 336Q304 343 310 354T322 373Q240 432 240 527"],9837:[750,17,447,64,381,"230 480Q293 480 337 440T381 330V322Q381 240 323 161Q258 71 123 -11L114 -16L97 -17Q70 -17 66 -7Q64 -3 64 366V641Q64 717 65 731T75 748Q78 750 95 750Q117 750 122 742T127 694Q127 685 127 653T126 595V454Q183 480 230 480ZM242 333Q242 405 212 405H207Q147 405 130 370L127 364L126 219Q126 77 128 77Q133 82 140 90T167 127T202 183T229 253T242 333"],9838:[741,223,447,57,389,"345 -223Q333 -223 330 -214T327 -178V-116Q327 -23 326 -23L203 -82Q90 -134 77 -140Q65 -142 59 -130Q57 -126 57 295V595Q57 643 57 667T58 704T60 719T63 724Q93 741 101 741Q113 741 116 732T119 680V597Q119 467 120 467Q121 468 180 495T301 552T369 584Q381 586 387 574Q389 570 389 187V-88Q389 -132 389 -154T388 -188T386 -202T383 -206Q353 -223 345 -223ZM327 271Q327 421 326 421L120 323L119 173V23Q120 23 223 72L327 121V271"],9839:[724,224,447,64,382,"140 628Q151 628 154 620T158 591V549V484L166 488Q175 492 192 500T223 516L288 548V622V674Q288 681 288 685T289 693T289 699T291 703T295 707T298 709T304 712T311 716Q326 724 332 724Q343 724 346 715T350 685V644V579Q358 583 364 583Q376 583 380 574Q382 570 382 514V481Q382 459 380 454T363 441L350 435V135Q358 139 364 139Q376 139 380 130Q382 126 382 70V37Q382 15 380 10T363 -3L350 -9V-76Q350 -102 348 -106T328 -119Q312 -128 306 -128Q288 -128 288 -99V-77V-40L280 -44Q271 -48 254 -56T223 -72L158 -104V-150V-180Q158 -198 155 -202T135 -216Q119 -224 114 -224Q96 -224 96 -192V-172V-135Q86 -140 81 -140Q70 -140 66 -129Q64 -126 64 -70V-54Q64 -18 66 -12T83 3L96 9V309Q86 304 81 304Q70 304 66 315Q64 318 64 374V407Q64 429 66 434T83 447L96 453V602Q99 609 100 610T118 619Q134 628 140 628ZM288 254Q288 404 287 404L158 340V40L166 44Q175 48 192 56T223 72L288 104V254"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/MiscSymbols.js");
