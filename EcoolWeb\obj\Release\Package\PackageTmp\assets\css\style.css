#contain {
    background-color: rgb(211,241,163);
    background-image: url("../../assets/img/webactivity_bg_verticla.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top;
    width: 100%;
    height: 109vh;
}

@media (min-width: 576px) {
    #contain {
        width: 100%;
        height: 173vh;
    }
}

@media (min-width: 768px) {
    #contain {
        background-image: url(../../assets/img/webactivity_bg_horizon.jpg);
        background-repeat: repeat;
        width: 100%;
        height: 109vh;
    }
}

@media (min-width: 992px) {
    #contain {
        background-image: url("../../assets/img/webactivity_bg_horizon.jpg");
        width: 100%;
        height: 109vh;
    }
}

@media (min-width: 1200px) {
    #contain {
        background-image: url("../../assets/img/webactivity_bg_horizon.jpg");
        width: 100%;
        height: 109vh;
    }
}

#hero_logo {
    margin-top: -99px;
}

@media (min-width: 576px) {
    #hero_logo {
        margin-top: -75px;
    }
}

@media (min-width: 768px) {
    #hero_logo {
        margin-top: -125px;
    }
}

@media (min-width: 992px) {
    #hero_logo {
        margin-top: -119px;
    }
}

@media (min-width: 1200px) {
    #hero_logo {
        margin-top: -116px;
    }
}

#body_c2 {
    width: 100%;
    height: 11vh;
    margin: 0;
}

@media (min-width: 576px) {
    #body_c2 {
        width: 100%;
        height: 22vh;
        margin: 0;
        margin-top: -39px;
    }
}

@media (min-width: 768px) {
    #body_c2 {
        width: 100%;
        height: 11vh;
        margin: 0;
    }
}

@media (min-width: 768px) {
    #body_c2 {
        width: 100%;
        height: 12vh;
        margin: 0;
    }
}

@media (min-width: 1200px) {
    #body_c2 {
        width: 100%;
        height: 12vh;
        margin: 0;
    }
}

#logo_title_super {
    height: 70px;
    /*margin-top:-70px;*/
    /*background-image:url(../../assets/img/SuperTitle_1.png);*/
    -webkit-animation-duration: 2s;
    -webkit-animation-delay: 5s;
    -webkit-animation-iteration-count: infinite;
}

@media (min-width: 576px) {
    #logo_title_super {
        height: 100px;
    }
}

@media (min-width: 992px) {
    #logo_title_super {
        height: 110px;
    }
}

@media (min-width: 1200px) {
    #logo_title_super {
        height: 118px;
    }
}

#logo_title_go {
    -webkit-animation-duration: 8s;
    -webkit-animation-delay: 3s;
    -webkit-animation-iteration-count: infinite;
    width: 100%;
    height: 9vh;
    margin-top: -112px;
}

@media (min-width: 576px) {
    #logo_title_go {
        width: 100%;
        height: 15vh;
        margin-top: -170px;
    }
}

@media (min-width: 768px) {
    #logo_title_go {
        width: 100%;
        height: 12vh;
        margin-top: -110px;
    }
}

@media (min-width: 992px) {
    #logo_title_go {
        width: 100%;
        height: 15vh;
        margin-top: -81px;
    }
}

@media (min-width: 1200px) {
    #logo_title_go {
        width: 100%;
        height: 15vh;
        margin-top: -150px;
    }
}

#hot_air_balloon {
    -webkit-animation-duration: 30s;
    -webkit-animation-iteration-count: infinite;
    width: 100%;
    height: 53%;
    margin-left: -27px;
}

@media (min-width: 576px) {
    #hot_air_balloon {
        width: 100%;
        height: 55%;
        margin-top: -40px;
        margin-left: -27px;
    }
}

@media (min-width: 768px) {
    #hot_air_balloon {
        width: 100%;
        height: 65%;
        margin-top: -60px;
        margin-left: -27px;
    }
}

@media (min-width: 1200px) {
    #hot_air_balloon {
        width: 100%;
        height: 84%;
        margin-top: -60px;
        margin-left: -37px;
    }
}

#top_1 {
    width: 100%;
}

#body_c1 {
    height: 44vh;
}

@media (min-width: 576px) {
    #body_c1 {
        height: 55vh;
    }
}

@media (min-width: 768px) {
    #body_c1 {
        height: 43vh;
    }
}

@media (min-width: 992px) {
    #body_c1 {
        height: 44vh;
    }
}

#style-1::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    border-radius: 3px;
    background-color: rgba(245,245,245,0.44);
}

#style-1::-webkit-scrollbar {
    width: 5px;
    background-color: rgba(245,245,245,0.43);
}

#style-1::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #c4f8bf;
}

#style-1 {
    /*border:2px solid rgb(171,206,30);*/
    height: 66%;
    border-radius: 10px;
    background-color: rgba(128,148,104,0.38);
    overflow-y: scroll;
    margin-top: 10px;
    padding: 5px 5px 15px 5px;
    font-size: 0.7em;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #8cee29;
    text-shadow: 0 0.5px 0 #517d45,0 1px 0 #4b6134, 0 1.5px 0 #276226, 0 2px 0 #234c27, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25);
}

@media (min-width: 576px) {
    #style-1 {
        height: 50%;
        font-size: 1em;
        padding: 5px 10px 15px 10px;
        margin-top: 9px;
    }
}

@media (min-width: 768px) {
    #style-1 {
        height: 50%;
        font-size: 1.2em;
        padding: 5px 10px 15px 10px;
        margin-top: -3px;
    }
}

@media (min-width: 992px) {
    #style-1 {
        height: 50%;
        font-size: 1.2em;
        padding: 5px 10px 15px 10px;
        margin-top: 5px;
    }
}

@media (min-width: 1200px) {
    #style-1 {
        height: 47%;
        font-size: 1.2em;
        padding: 5px 10px 15px 10px;
        margin-top: 5px;
    }
}
