(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: storage - updated 2018-03-18 (v2.30.0) */
!function(m,y,O){"use strict";var w=m.tablesorter||{};m.extend(!0,w.defaults,{fixedUrl:"",widgetOptions:{storage_fixedUrl:"",storage_group:"",storage_page:"",storage_storageType:"",storage_tableId:"",storage_useSessionStorage:""}}),w.storage=function(e,t,r,o){var s,a,i,g=!1,n={},p=(e=m(e)[0]).config,l=p&&p.widgetOptions,d=w.debug(p,"storage"),u=(o&&o.storageType||l&&l.storage_storageType).toString().charAt(0).toLowerCase(),S=u?"":o&&o.useSessionStorage||l&&l.storage_useSessionStorage,c=m(e),_=o&&o.id||c.attr(o&&o.group||l&&l.storage_group||"data-table-group")||l&&l.storage_tableId||e.id||m(".tablesorter").index(c),f=o&&o.url||c.attr(o&&o.page||l&&l.storage_page||"data-table-page")||l&&l.storage_fixedUrl||p&&p.fixedUrl||y.location.pathname;if("c"!==u&&(u="s"===u||S?"sessionStorage":"localStorage")in y)try{y[u].setItem("_tmptest","temp"),g=!0,y[u].removeItem("_tmptest")}catch(e){console.warn(u+" is not supported in this browser")}if(d&&console.log("Storage >> Using",g?u:"cookies"),m.parseJSON&&(n=g?m.parseJSON(y[u][t]||"null")||{}:(a=O.cookie.split(/[;\s|=]/),0!==(s=m.inArray(t,a)+1)&&m.parseJSON(a[s]||"null")||{})),void 0===r||!y.JSON||!JSON.hasOwnProperty("stringify"))return n&&n[f]?n[f][_]:"";n[f]||(n[f]={}),n[f][_]=r,g?y[u][t]=JSON.stringify(n):((i=new Date).setTime(i.getTime()+31536e6),O.cookie=t+"="+JSON.stringify(n).replace(/\"/g,'"')+"; expires="+i.toGMTString()+"; path=/")}}(jQuery,window,document);return jQuery;}));
