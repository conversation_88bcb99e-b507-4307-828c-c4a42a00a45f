﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    if (ViewBag.isshowonMobil == "true")
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>

@if (!AppMode)
{
    @Html.Partial("_Title_Secondary")
}

@Html.Partial("_Notice")

@if ((Model?.WhereIsColorboxForUser ?? false) == false)
{
    <div class="form-inline">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔</button>
        </div>
    </div>
}

@using (Html.BeginForm("Details", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = (string)ViewBag.BRE_NO, name = "form1" }))
{
    @Html.HiddenFor(m => m.WhereIsColorboxForUser)
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.whereShowType)
    @Html.HiddenFor(m => m.whereIDNO)
    @Html.HiddenFor(m => m.ShowOnMobile)
    @Html.HiddenFor(m => m.whereKeyword)
    @Html.HiddenFor(m => m.whereGrade)
    @Html.HiddenFor(m => m.whereCLASS_NO)
    @Html.HiddenFor(m => m.whereSYEAR)
    @Html.HiddenFor(m => m.whereSEMESTER)
    @Html.HiddenFor(m => m.whereSCHOOL_NO)
    string ActiveTALL = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL) ? "active" : "";
    string ActiveWEIGHT = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT) ? "active" : "";
    string ActiveVISION = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION) ? "active" : "";
    string ActiveFitness = (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness) ? "active" : "";

    <div class="text-left">
        <button class="btn btn-xs btn-pink  @ActiveTALL " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)</button>
        <button class="btn btn-xs btn-pink  @ActiveWEIGHT " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)</button>
        <button class="btn btn-xs btn-pink  @ActiveVISION " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)</button>
        <button class="btn btn-xs btn-pink  @ActiveFitness " type="button" onclick="doSearch('whereShowType', @ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness);">@Model.GetShowTypeName(ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness)</button>
    </div>

}
<div id="tbData">
    <div class="panel panel-ACC">
        @if (AppMode == false)
        {
            <div class="panel-heading text-center">
                @Html.BarTitle()
            </div>
        }

        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover">
                <tr>
                    <th class="dt">
                        年級
                    </th>
                    <th class="dd">
                        @Html.DisplayFor(modelItem => Model.myHRMT01.GRADE)
                    </th>
                    <th class="dt">
                        班級
                    </th>
                    <th class="dd">
                        @Html.DisplayFor(modelItem => Model.myHRMT01.CLASS_NO)
                    </th>
                    <th class="dt">
                        座號
                    </th>
                    <th class="dd">
                        @Html.DisplayFor(modelItem => Model.myHRMT01.SEAT_NO)
                    </th>
                </tr>
                <tr>
                    <th class="dt">
                        學生
                    </th>
                    <th class="dd">
                        @Html.DisplayFor(modelItem => Model.myHRMT01.SNAME)
                    </th>
                    <th class="dt">
                        生日
                    </th>
                    <th class="dd">
                        @Html.DisplayFor(modelItem => Model.myHRMT01.BIRTHDAY, "ShortDateTime")
                    </th>
                    <th class="dt">
                        性別
                    </th>
                    <th class="dd">
                        @HRMT01.ParserSex(Model.myHRMT01.SEX)
                    </th>
                </tr>
            </table>
        </div>
    </div>
    <div style="height:25px"></div>

    @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness)
    {
        <div class="panel panel-ACC">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().SYEAR)
                        </th>
                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().SEMESTER)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().V_SET_REACH_TEST)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().S_L_JUMP_TEST)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().SIT_UPS_TEST)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt09List.First().C_P_F_TEST)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Hrmt09List)
                    {
                        <tr align="center">
                            <td>
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SEMESTER)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.V_SET_REACH_TEST)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.S_L_JUMP_TEST)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SIT_UPS_TEST)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.C_P_F_TEST)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="panel panel-ACC table-responsive">
            @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)
            {
                <label class="text-danger">
                    @Model.VisionMemo
                    <br />PS.視力測量結果不代表視力好壞，如有疑慮則建議到診所追蹤
                </label>
            }
            else if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)
            {
                <label class="text-danger">
                    @Model.TallMemo
                </label>
            }
            else if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)
            {
                <label class="text-danger">
                    @Model.WeightMemo
                </label>
            }
            <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                        </th>
                        @*<th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AGE)
                            </th>*@

                        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)
                        {
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().TALL)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_TALL_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_TALL_W)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_TALL_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_TALL_W)
                            </th>
                        }

                        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)
                        {
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().WEIGHT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_WEIGHT_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_WEIGHT_W)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_WEIGHT_M)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().SUM_AVG_WEIGHT_W)
                            </th>
                            @*<th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().POSTURE_MEMO)
                                </th>
                                <th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().S_WEIGHT)
                                </th>
                                <th style="text-align: center">
                                    @Html.DisplayNameFor(model => model.Hrmt08List.First().O_WEIGHT)
                                </th>
                                    <th style="text-align: center">
                                        @Html.DisplayNameFor(model => model.Hrmt08List.First().BMI)
                                    </th>
                                    <th style="text-align: center">
                                        @Html.DisplayNameFor(model => model.Hrmt08List.First().S_BMI)
                                    </th>*@
                        }

                        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)
                        {
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().G_VISION_RIGHT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT_08)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT_09)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_RIGHT_12)
                            </th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Hrmt08List)
                    {
                        <tr align="center">
                            <td>
                                @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                            </td>
                            @*<td>
                                    @Html.DisplayFor(modelItem => item.AGE)
                                </td>*@

                            @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)
                            {
                                <td>
                                    @Html.DisplayFor(modelItem => item.TALL)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_TALL_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_TALL_W)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_TALL_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_TALL_W)
                                </td>
                            }

                            @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)
                            {
                                <td>
                                    @Html.DisplayFor(modelItem => item.WEIGHT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_WEIGHT_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.AVG_WEIGHT_W)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_WEIGHT_M)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SUM_AVG_WEIGHT_W)
                                </td>
                                @*<td>
                                        @Html.DisplayFor(modelItem => item.POSTURE_MEMO)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.S_WEIGHT)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.O_WEIGHT)
                                    </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.BMI)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.S_BMI)
                                        </td>*@
                            }

                            @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)
                            {
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_RIGHT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.G_VISION_RIGHT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_RIGHT_08)
                                    <text>人</text>
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_RIGHT_09)
                                    <text>人</text>
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_RIGHT_12)
                                    <text>人</text>
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }

    <div class="col-sm-12">
        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.TALL)
        {
            if (Model.TALLchart != null)
            {<div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.TALLchart != null)
                                {
                                    @Model.TALLchart
                                }
                            </div>
                        </div>
                    </div>
                </div>

            }
        }
        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.WEIGHT)
        {
            if (Model.WEIGHTchart != null)
            {

                <div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.WEIGHTchart != null)
                                {
                                    @Model.WEIGHTchart
                                }
                            </div>
                        </div>
                    </div>
                </div>

            }
        }

        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.VISION)
        {
            if (Model.RIGHT_VISIONColumnChart != null)
            {
                <div style="height:30px"></div>

                <div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.RIGHT_VISIONColumnChart != null)
                                {
                                    @Model.RIGHT_VISIONColumnChart
                                }
                            </div>
                        </div>
                    </div>
                </div>

            }

            <div class="panel panel-ACC table-responsive">
                <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
                    <thead>
                        <tr>
                            <th style="text-align: center;">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().G_VISION_LEFT)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT_08)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT_09)
                            </th>
                            <th style="text-align: center">
                                @Html.DisplayNameFor(model => model.Hrmt08List.First().VISION_LEFT_12)
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Hrmt08List)
                        {
                            <tr align="center">
                                <td>
                                    @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_LEFT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.G_VISION_LEFT)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_LEFT_08)
                                    <text>人</text>
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_LEFT_09)
                                    <text>人</text>
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.VISION_LEFT_12)
                                    <text>人</text>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            if (Model.LEFT_VISIONColumnChart != null)
            {

                <div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.LEFT_VISIONColumnChart != null)
                                {
                                    @Model.LEFT_VISIONColumnChart
                                }
                            </div>
                        </div>
                    </div>
                </div>

            }
        }

        @if (Model.whereShowType == ECOOL_APP.com.ecool.Models.DTO.SECI03IndexViewModel.ShowTypeVal.Fitness)
        {
            if (Model.FitnesschartV != null)
            {

                <div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.FitnesschartV != null)
                                {
                                    @Model.FitnesschartV
                                }
                            </div>
                        </div>
                    </div>
                </div>

            }

            if (Model.FitnesschartSL != null)
            {

                <div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.FitnesschartSL != null)
                                {
                                    @Model.FitnesschartSL
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }

            if (Model.FitnesschartSU != null)
            {

                <div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.FitnesschartSU != null)
                                {
                                    @Model.FitnesschartSU
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }

            if (Model.FitnesschartC != null)
            {

                <div style="height:30px"></div>
                <p style="page-break-after:always"></p>
                <div class="row" id="secI06">
                    <div class="col-md-12" style="padding-bottom:50px">
                        <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                            <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                            <div class="chart">
                                @if (Model.FitnesschartC != null)
                                {
                                    @Model.FitnesschartC
                                }
                            </div>
                        </div>
                    </div>
                </div>

            }
        }
    </div>
</div>

<div style="height:25px"></div>
@if (user != null)
{
    if (user.USER_TYPE != UserType.Student)
    {
        <div class="text-center  col-md-offset-2 col-sm-offset-1 col-md-3 col-sm-4 ">
            <button role="button" class="btn btn-default" onclick="BackGO()">
                返回一覽表
            </button>
        </div>
    }
}

<div class="text-center col-md-3 col-sm-4">
    @Html.ActionLink("成人BMI計算", "BMICal", "SECI03", new { WhereIsColorboxForUser = (Model?.WhereIsColorboxForUser ?? false) }, new { @class = "btn btn-default", @role = "button" })
</div>

<script type="text/javascript">
        var targetFormID = '#@ViewBag.BRE_NO';

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            $(targetFormID).submit();
        }

         function BackGO()
         {
           $(targetFormID).attr ("action","@Url.Action("Index", (string)ViewBag.BRE_NO)");
           $(targetFormID).submit();
         }

         function PrintBooK() {
             $('#tbData').printThis();
         }

         function fn_save() {
             var blob = new Blob([document.getElementById('tbData').innerHTML], {
                 type: "html/plain;charset=utf-8"
             });
             var strFile = "Report.html";
             saveAs(blob, strFile);
             return false;
         }
</script>