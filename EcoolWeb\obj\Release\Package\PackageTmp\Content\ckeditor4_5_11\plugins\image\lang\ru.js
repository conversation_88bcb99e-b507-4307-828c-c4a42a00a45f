﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'ru', {
	alt: 'Альтернативный текст',
	border: 'Граница',
	btnUpload: 'Загрузить на сервер',
	button2Img: 'Вы желаете преобразовать это изображение-кнопку в обычное изображение?',
	hSpace: 'Гориз. отступ',
	img2Button: 'Вы желаете преобразовать это обычное изображение в изображение-кнопку?',
	infoTab: 'Данные об изображении',
	linkTab: 'Ссылка',
	lockRatio: 'Сохранять пропорции',
	menu: 'Свойства изображения',
	resetSize: 'Вернуть обычные размеры',
	title: 'Свойства изображения',
	titleButton: 'Свойства изображения-кнопки',
	upload: 'Загрузить',
	urlMissing: 'Не указана ссылка на изображение.',
	vSpace: 'Вертик. отступ',
	validateBorder: 'Размер границ должен быть задан числом.',
	validateHSpace: 'Горизонтальный отступ должен быть задан числом.',
	validateVSpace: 'Вертикальный отступ должен быть задан числом.'
} );
