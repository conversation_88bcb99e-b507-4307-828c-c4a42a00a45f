﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'vi', {
	access: 'Truy cập mã',
	accessAlways: 'Luôn luôn',
	accessNever: 'Không bao giờ',
	accessSameDomain: 'Cùng tên miền',
	alignAbsBottom: 'Dưới tuyệt đối',
	alignAbsMiddle: 'Giữa tuyệt đối',
	alignBaseline: 'Đường cơ sở',
	alignTextTop: '<PERSON><PERSON> trên chữ',
	bgcolor: '<PERSON><PERSON><PERSON> nền',
	chkFull: 'Cho phép toàn màn hình',
	chkLoop: 'Lặp',
	chkMenu: 'Cho phép bật menu của Flash',
	chkPlay: 'Tự động chạy',
	flashvars: '<PERSON><PERSON><PERSON> biến số dành cho Flash',
	hSpace: '<PERSON><PERSON>ảng đệm ngang',
	properties: '<PERSON>huộc tính Flash',
	propertiesTab: 'Thuộc tính',
	quality: 'Chất lượng',
	qualityAutoHigh: '<PERSON> tự động',
	qualityAutoLow: 'Thấp tự động',
	qualityBest: 'Tốt nhất',
	qualityHigh: 'Cao',
	qualityLow: 'Thấp',
	qualityMedium: 'Trung bình',
	scale: 'Tỷ lệ',
	scaleAll: 'Hiển thị tất cả',
	scaleFit: 'Vừa vặn',
	scaleNoBorder: 'Không đường viền',
	title: 'Thuộc tính Flash',
	vSpace: 'Khoảng đệm dọc',
	validateHSpace: 'Khoảng đệm ngang phải là số nguyên.',
	validateSrc: 'Hãy đưa vào đường dẫn liên kết',
	validateVSpace: 'Khoảng đệm dọc phải là số nguyên.',
	windowMode: 'Chế độ cửa sổ',
	windowModeOpaque: 'Mờ đục',
	windowModeTransparent: 'Trong suốt',
	windowModeWindow: 'Cửa sổ'
} );
