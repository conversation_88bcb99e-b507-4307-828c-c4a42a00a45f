/*
 *  /MathJax/jax/output/SVG/fonts/TeX/AMS/Regular/LetterlikeSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS.MathJax_AMS,{8463:[695,13,540,42,562,"150 475Q147 475 118 466T82 457Q73 457 64 467T54 487Q54 490 55 492Q63 506 64 506Q67 512 118 526Q162 541 169 546Q173 559 175 575Q181 596 181 604Q181 613 166 617Q164 617 153 618T135 619Q119 619 114 621T109 630Q109 636 114 656T122 681Q125 685 202 688Q272 695 286 695Q304 695 304 684Q304 682 291 628L278 577L386 612Q466 635 476 635T492 627T499 607Q499 593 489 586Q485 583 373 546L262 512Q262 511 248 455T233 397T236 397T244 404Q295 441 357 441Q405 441 445 417T485 333Q485 284 449 178T412 58T426 44Q447 44 466 68Q485 87 500 130L509 152H531H543Q562 152 562 144Q562 128 546 93T494 23T415 -13Q385 -13 359 3T322 44Q318 52 318 77Q318 99 352 196T386 337Q386 386 346 386Q318 386 286 370Q267 361 245 338T211 292Q207 287 193 235T162 113T138 21Q128 7 122 4Q105 -12 83 -12Q66 -12 54 -2T42 26Q42 45 98 257L151 475H150"],8487:[685,22,722,44,675,"126 584Q119 584 110 539T97 493Q95 490 73 490Q44 490 44 501Q44 515 62 590Q75 672 82 679Q84 684 177 684Q193 684 214 684T241 685Q265 685 271 682T277 664V648Q271 572 229 434T186 231Q186 173 203 132T247 70T302 42T360 33Q391 33 419 42T474 72T517 133T533 231Q533 297 491 437T442 648Q442 675 446 679Q448 684 542 684Q635 684 637 681Q640 678 657 594T675 501Q675 490 646 490Q624 490 622 493Q620 493 609 538T593 584Q591 585 585 585T569 586T551 588H513Q514 586 518 573T538 531T582 453Q647 340 660 277Q663 259 663 232Q663 194 657 177Q652 151 629 112T560 39Q495 -5 424 -19Q403 -22 360 -22Q318 -22 297 -19Q239 -8 193 18T120 74T80 131T62 177Q56 194 56 229Q56 281 74 328T137 453Q160 491 174 518T193 555T201 575T206 588H168Q160 587 150 587T134 586T126 584"],8498:[695,1,556,55,497,"457 681Q471 695 477 695Q485 695 497 681V12L484 -1H68Q55 14 55 19T68 39H457V328H215L211 335Q198 346 211 359L217 368H457V681"],8502:[763,21,667,-22,687,"56 706V726Q56 763 76 763Q83 763 87 759T98 741Q108 726 116 721L127 717L340 715Q547 712 564 709Q575 705 587 692Q599 680 605 663L609 650V137H676Q687 124 687 115Q687 110 678 100T622 43L558 -21H-9Q-22 -6 -22 -1T-13 14T42 72L107 137H569V339Q569 541 567 546Q558 555 554 557L545 563H329Q118 566 101 569Q90 573 78 586Q54 610 54 661Q54 670 56 706"],8503:[764,43,444,-22,421,"56 750Q68 764 76 764Q88 764 97 743T125 717Q131 715 240 715T358 713Q421 691 421 640Q421 608 399 588T358 566Q353 566 352 565T351 557L356 526Q356 488 379 346T402 97Q400 21 385 -12Q366 -43 351 -43Q335 -43 329 -10Q316 40 316 64Q316 67 315 67Q313 67 269 26L222 -21H-9Q-22 -7 -22 -1Q-22 4 -14 14T42 73L107 137H311V564H211H164Q115 564 93 573T60 615Q56 630 56 690V750"],8504:[764,43,667,54,640,"62 757Q69 764 75 764Q87 764 97 741Q102 731 105 728T117 721L129 715H349Q569 715 580 710Q618 701 635 670Q640 661 640 639Q640 609 622 590Q617 583 604 575T580 566H573V553Q575 547 576 531T582 469T600 353Q624 205 624 104Q624 46 617 17T591 -32Q581 -43 573 -43Q550 -43 540 44Q535 73 533 319V564H322Q117 566 100 570Q90 573 77 586Q54 609 54 663Q54 689 55 706Q55 738 56 745T62 757"],8513:[705,23,639,37,577,"239 665Q194 665 154 653T90 629T66 617Q59 617 53 623T46 637Q46 652 66 659Q129 695 197 701Q218 705 248 705Q293 705 335 693Q371 684 435 644Q543 562 573 417Q577 393 577 341Q577 290 573 266Q531 83 384 10Q346 -9 315 -16T234 -23H206Q202 -23 183 -23T152 -21T120 -18T88 -10T63 3T44 24L37 35V297L50 310H235Q248 297 248 290Q248 285 235 270H77V103Q77 88 77 80T77 63T78 50T80 43T82 38T85 35T89 32T95 30Q126 20 206 17Q289 17 330 30Q407 55 460 120T533 275Q538 305 538 342Q538 486 452 575T239 665"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/LetterlikeSymbols.js");
