﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWA01labViewModel
    {
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }

        public string whereLOG_TIME_S { get; set; }

        public string whereLOG_TIME_E { get; set; }

        public bool whereIsExcel { get; set; }

        public IPagedList<AWA01_labnormalog> labnormaloglist { get; set; }
        public AWA01labViewModel()
        {
            Page = 0;
            OrdercColumn = "SumCASH_IN";
        }
    }
}