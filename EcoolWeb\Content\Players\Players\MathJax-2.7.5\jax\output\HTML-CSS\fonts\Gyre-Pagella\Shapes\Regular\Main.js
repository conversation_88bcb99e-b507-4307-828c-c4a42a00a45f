/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Shapes/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Shapes={directory:"Shapes/Regular",family:"GyrePagellaMathJax_Shapes",testString:"\u00A0\u2422\u2423\u2500\u2502\u251C\u2524\u252C\u2534\u253C\u2581\u2588\u2591\u2592\u2593",32:[0,0,250,0,0],160:[0,0,250,0,0],9250:[726,12,553,-15,508],9251:[133,97,500,40,460],9472:[280,-220,600,0,600],9474:[650,150,600,270,330],9500:[650,150,600,270,600],9508:[650,150,600,0,330],9516:[280,150,600,0,600],9524:[650,-220,600,0,600],9532:[650,150,600,0,600],9601:[88,0,700,0,700],9608:[700,0,700,0,700],9617:[700,0,700,0,700],9618:[700,0,700,0,700],9619:[700,0,700,0,700],9642:[410,-90,480,80,400],9643:[410,-90,480,80,400],9644:[400,-100,760,80,680],9645:[400,-100,760,80,680],9655:[643,143,841,80,761],9665:[643,143,841,80,761],9675:[568,68,796,80,716],9679:[568,68,796,80,716],9702:[450,-50,560,80,480],9828:[668,0,800,80,720],9829:[666,0,760,80,680],9830:[670,0,746,80,666],9831:[668,0,842,80,762],9834:[692,20,600,56,561],9901:[475,-26,500,-116,616],9902:[699,199,500,-170,670],11012:[450,-50,1069,80,989],11013:[450,-50,995,80,915],11014:[673,162,560,80,480],11015:[662,173,560,80,480],11020:[450,-50,1005,80,925],11021:[673,172,560,80,480],11034:[660,160,940,60,880],11057:[740,240,920,80,840],11059:[400,-100,1370,80,1290]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Shapes"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Shapes/Regular/Main.js"]);
