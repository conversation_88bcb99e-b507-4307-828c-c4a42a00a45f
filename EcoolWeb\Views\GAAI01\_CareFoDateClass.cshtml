﻿@model GAAI01CareFoDateClassViewModel

@using (Html.BeginCollectionItem("CareFoDateClass"))
{
    var Index = Html.GetIndex("CareFoDateClass");

    @Html.CheckBoxFor(m => m.Checked)

    @Html.HiddenFor(m => m.ALARM_ID)
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.CLASS_NO)
    @Html.HiddenFor(m => m.CYCLE)

    @Html.HiddenFor(m => m.ALARM_DATES)
    @Html.HiddenFor(m => m.ALARM_DATEE)
    @Html.HiddenFor(m => m.UN_WEAR_USERs)
    @Html.HiddenFor(m => m.UN_WEAR_MEMOs)
}