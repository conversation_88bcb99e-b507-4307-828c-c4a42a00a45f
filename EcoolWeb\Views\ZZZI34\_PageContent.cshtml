﻿@model ZZZI34IndexViewModel
@using ECOOL_APP.com.ecool.service
@using ECOOL_APP.com.ecool.util

@{  ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    bool IsAdmin = ViewBag.IsAdmin;

    var ImgOnePath = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-05.png");
    var ImgTwoPath = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-09.png");
    int Num = 1;
    string QU = "";
}  
<style type="text/css">
    .element {
        float: left;
        padding: 3px;
        box-sizing: border-box;
    }
    .modal-dialog {
        right: auto;
        left: 50%;
        width: 600px;
        padding-top: 30px;
        padding-bottom: 30px;
    }
</style>
<script src="~/Scripts/grids.js"></script>

@{
    if ((Model.Search.WhereMyWork ?? false))
    {
        Html.RenderAction("_ArtGalleryMenu", new { NowAction = "MyWorkList" });
    }
    else
    {
        Html.RenderAction("_ArtGalleryMenu", new { NowAction = "ArtGalleryList" });
    }
}
<br>
<br>
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereUSER_NO)
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_NO)
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_TYPE)
@Html.HiddenFor(m => m.Search.WhereSTATUS)
@Html.HiddenFor(m => m.Search.WhereMyWork)
@Html.HiddenFor(m => m.IsPostBack, new { Value = "Y" })

<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">學年</label>
    </div>
    <div class="form-group">

        @Html.DropDownListFor(m => m.Search.WhereSYEAR, (IEnumerable<SelectListItem>)ViewBag.SYEARItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">學號/姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.Search.WhereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
    </div>
    <div class="form-group">
        <label class="control-label">年級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.Search.WhereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.Search.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
    </div>
    <br />
    <div class="form-group">
        <label class="control-label">
            @Html.DisplayNameFor(model => model.Search.WhereSearch)
        </label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.Search.WhereSearch, new { htmlAttributes = new { @class = "form-control input-sm" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear(true);" />
</div>

<br>
<div class="row">
    <div class="col-md-7" style="text-align:left">
        @foreach (var item in ViewBag.StatusItem as List<SelectListItem>)
        {
            <button class="btn btn-xs btn-pink  @(item.Selected ? "active" : "")" type="button" onclick="todoClear(false);doSearch('@Html.IdFor(m => m.Search.WhereSTATUS)', '@item.Value');" title="@item.Text">@item.Text</button>
        }
    </div>
    <div class="col-md-5" style="text-align:right">
        @foreach (var item in ViewBag.ART_GALLERY_TYPE as List<SelectListItem>)
        {
            <button class="btn btn-xs btn-pink  @(item.Selected ? "active" : "")" type="button" onclick="todoClear(false);doSearch('@Html.IdFor(m => m.Search.WhereART_GALLERY_TYPE)', '@item.Value');" title="@item.Text">@item.Text</button>
        }
    </div>
</div>

<img src="~/Content/img/GalleryBook_A2-1_Ass-03.png" class="img-responsive" alt="Responsive image" style="min-width:300px;" />
<div class="Div-EZ-ArtGallery" style="min-width:288px;background-image:url('@ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_Asset-03.gif")');background-repeat:repeat;border:#dba3ac 1.6px solid;">
    <div style="height:35px"></div>
    <div class="form-horizontal" style="display: table; margin: 0 auto;">
        <div class="row">
            @if (Model.ListData.Count() == 0)
            {
                <div class="col-xs-12 text-center">
                    <h3>目前尚未有作品</h3>
                </div>
            }
            @foreach (var item in Model.ListData)
            {

                var ImgPath = ImgOnePath;

                if (Num % 2 == 0)
                {
                    ImgPath = ImgTwoPath;
                }

                <div class="col-md-4 col-xs-6">
                    <div class="element">
                        <div onclick="showArtGalleryWorkList('@item.ART_GALLERY_NO')" class="Div-EZ-ArtGallery-pointer">
                            @{
                                <img src="@ImgPath" class="Div-EZ-ArtGallery-book" />
                                string NewImg = "";
                                string SUrl = "";
                                string desc = "";
                                if (item.COVER_FILE != "")
                                {

                                    NewImg = item.COVER_FILE.Replace(Path.GetExtension(item.COVER_FILE), "_S" + Path.GetExtension(item.COVER_FILE));
                                    SUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.SCHOOL_NO, item.ART_GALLERY_NO, NewImg);
                                }

                                if (!string.IsNullOrEmpty(item.ART_DESC))
                                {
                                    desc = @item.ART_SUBJECT + "-" + item.ART_DESC;

                                }
                                else { desc = @item.ART_SUBJECT ; }
                                <div class="Div-EZ-ArtGallery-div-img">
                                    <img src="@SUrl?v=@DateTime.Now.ToString()" class="Div-EZ-ArtGallery-img" title="@desc" alt="@item.ART_SUBJECT" />
                                </div>

                            }
                        </div>
                        <div>
                            <div class="Div-EZ-ArtGallery-text">
                                <strong style="color:#01b796">@StringHelper.LeftStringR(item.ART_SUBJECT, 15)</strong>
                                <br />
                                <strong>
                                    @if (item.CLASS_NO != null)
                                    {
                                        @item.CLASS_NO
                                    }
                                
                                        @item.SNAME

                               
                                  
                                    @if (user != null)
                                    {
                                        if (IsAdmin || user.USER_KEY == item.CRE_PERSON || (item.STATUS == ADDT21.STATUSVal.Verification && (item.VERIFIER == user.USER_NO || ViewBag.VisibleVerify)))
                                        {
                                            <font color="red">(@ADDT21.STATUSVal.GetDesc(item.STATUS))</font>
                                        }
                                    }
                                </strong>
                                <br />
                                <strong>@(item.CRE_DATE.Value.ToString("yyyyMMdd"))上架</strong>
                                <br />
                                <strong>@(item.WorkCount)@(item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo ? "張" : "部")作品</strong>
                                 <a  class="btn2 btn-primary btn-xs" role="button"  style="background-color: rgb(255, 255, 128);border:2px #9999FF; padding: 3px 3px;" onclick="ShowMessege('@item.ART_DESC')" >說明</a>
                                <br />

                                <p>

                                    @if (user != null)
                                    {
                                        int ADDT22Count = 0;
                                        ADDT22Count = db.ADDT22.Where(x => x.ART_GALLERY_NO == item.ART_GALLERY_NO && x.PHOTO_USER_NO == user.USER_NO && x.PHOTO_SCHOOL_NO == user.SCHOOL_NO && x.PHOTO_STATUS== "0").Count();

                                        if (
                                            (user.USER_KEY == item.CRE_PERSON && (item.STATUS == ADDT21.STATUSVal.Pass))
                                            ||( IsAdmin && (item.STATUS == ADDT21.STATUSVal.Pass))
                                            || (ADDT22Count >0 && (item.STATUS == ADDT21.STATUSVal.Pass))
)
                                        {
                                            
                                            
                                            <a class="btn2 btn-primary btn-xs" role="button" onclick="onWinOpenShareUrlLink('@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)')">
                                              分享此作者所有作品網址
                                            </a>
                                            <a class="btn2 btn-primary btn-xs" role="button" onclick="onWinOpenShareUrlLink('@(item.ART_GALLERY_NO)')">
                                                分享本相簿網址
                                            </a>
                                            <a class="btn2 btn-primary btn-xs" role="button" onclick="onWinOpenShareQRCODE('myQrCodeModal@(item.ART_GALLERY_NO)')">
                                                分享本相簿 QR CODE
                                            </a>}
                                        string str = com.ecool.service.PermissionService.GetPermission_Use_YN("ZZZI34", "Edit", user.SCHOOL_NO, user.USER_NO);

                                        if (
                                                                                (user.USER_KEY == item.CRE_PERSON && (item.STATUS == ADDT21.STATUSVal.NotStarted || item.STATUS == ADDT21.STATUSVal.Verification))
                                                                                ||
                                                                                (item.STATUS != ADDT21.STATUSVal.NotStarted && item.STATUS != ADDT21.STATUSVal.Disabled && IsAdmin)
                                                                                || (item.STATUS != ADDT21.STATUSVal.NotStarted && item.STATUS != ADDT21.STATUSVal.Disabled && str=="Y" && user.USER_TYPE=="A" && user.USER_TYPE == "T")
                                                                                )
                                        {
                                            <a class="btn2 btn-primary btn-xs" role="button" onclick="Edit_show('@item.ART_GALLERY_NO')">修改</a>
                                    }

                                        if ((item.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal && item.STATUS == ADDT21.STATUSVal.Verification)
                                            && (item.VERIFIER == user.USER_NO || IsAdmin || ViewBag.VisibleVerify))
                                        {
                                            <a class="btn2 btn-primary btn-xs" role="button" onclick="Verify_show('@item.ART_GALLERY_NO')">批閱</a>
                                        }
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal fade bs-example-modal-lg" id="myQrCodeModal@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
                    <div class="modal-dialog modal-sm" role="document">
                        <div class="modal-content">
                            <div class="input-group">
                                @if (ViewBag.WinOpenShareUrlLink != null)
                                {
                                    <img src="@Url.Action("Cre", "Barcode", new { Value = item.QRCODEGARYPHOTO})" style="max-width:150px" />
                                }
                            </div><!-- /input-group -->
                        </div>
                    </div>
                </div>
                <div class="modal fade bs-example-modal-lg" id="myQrCodeModal@(item.ART_GALLERY_NO)" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
                    <div class="modal-dialog modal-sm" role="document">
                        <div class="modal-content">
                            <div class="input-group">
                                @if (ViewBag.WinOpenShareUrlLink != null)
                                {
                                    <img src="@Url.Action("Cre", "Barcode", new { Value = item.QRCODEGARY})" style="max-width:150px" />
                                }
                            </div><!-- /input-group -->
                        </div>
                    </div>
                </div>
                <div class="modal fade bs-example-modal-lg" id="myShareUrlModal@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
                    <div class="modal-dialog modal-sm" role="document" style="top:200px;background-color: #ffffff;">
                        <div class="modal-content@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)">

                            <div class="input-group">
                                <span class="input-group-btn">
                                    <button type="button" id="id_copy"
                                            data-clipboard-target="#id_text"
                                            data-clipboard-action="copy" onclick="OnCopy('@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)')">
                                        點擊複製
                                    </button>
                                </span>
                                <div id="id_text@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)">@item.QRCODEGARYPHOTO</div>
                                <div id="success@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" style="display:none">已複製</div>
                                <input id="copyStr@(item.ART_GALLERY_NO)@(item.SCHOOL_NO)@(item.USER_NO)" type="hidden" value="@item.QRCODEGARYPHOTO">
                            </div><!-- /input-group -->
                        </div>
                    </div>
                </div>
                <div class="modal fade bs-example-modal-lg" id="myShareUrlModal@(item.ART_GALLERY_NO)" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
                    <div class="modal-dialog modal-sm" role="document" style="top:200px;background-color: #ffffff;">
                        <div class="modal-content@(item.ART_GALLERY_NO)">

                            <div class="input-group">
                                <span class="input-group-btn">
                                    <button type="button" id="id_copy"
                                            data-clipboard-target="#id_text"
                                            data-clipboard-action="copy" onclick="OnCopy('@(item.ART_GALLERY_NO)')">
                                        點擊複製
                                    </button>
                                </span>
                                <div id="id_text@(item.ART_GALLERY_NO)">@item.QRCODEGARY</div>
                                <div id="success@(item.ART_GALLERY_NO)" style="display:none">已複製</div>
                                <input id="copyStr@(item.ART_GALLERY_NO)" type="hidden" value="@item.QRCODEGARY">
                            </div><!-- /input-group -->
                        </div>
                    </div>
                </div>
                Num++;
            }
        </div>
    </div>
</div>


<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                                .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                .SetNextPageText(PageGlobal.DfSetNextPageText)
                                )
</div>

<script type="text/javascript">
    $('.element').responsiveEqualHeightGrid();

    function onWinOpenShareUrlLink(obj) {
        var $modal = $('#myShareUrlModal' + obj);

        $modal.on('show.bs.modal', function () {
            var $this = $(this);
            var $modal_dialog = $this.find('.modal-dialog');
            $this.css('display', 'block');

            $modal_dialog.css({ 'margin-top': Math.max(0, ($(window).height() - $modal_dialog.height()) - 180) });
        });
        $('.modal-content' + obj).attr("style", "width:500px");
        //$('#id_text').html(obj);
        //$('#copyStr').html(obj);
        $('#success' + obj).hide()
        $('#id_text' + obj).show()

        if ($('#myShareUrlModal' + obj).is(':visible') == false) {
            $('#myShareUrlModal' + obj).modal('show');
        }
    }
    function OnCopy(str) {
        var ArearValue = "";
        var ArearValuesuccess = "";
        ArearValue = str;
        ArearValuesuccess = str;
        ArearValue = "id_text" + ArearValue;
        ArearValuesuccess = "success" + ArearValuesuccess;
        console.log(ArearValuesuccess);
        var content = document.getElementById(ArearValue);
        var content1 = document.getElementById(ArearValuesuccess);
        var re = /amp;/g;
        var newStr = content.innerHTML.match(re);
       

        if (newStr!=null&&newStr.length >0) {

            content = content.innerHTML.replace('amp;', '');
        }
        else { content = content.innerHTML;}
        navigator.clipboard.writeText(content)
            .then(() => {
                $("#" +ArearValuesuccess).show()
                $("#"+ArearValue).hide()
            })
            .catch(err => {
                console.log('Something went wrong', err);
            })
    }
    function ShowMessege(str) {
        if (str != "" && str != undefined) {
            alert(str);

        }
        else {
            alert("本藝廊無說明");

        }

    }
    function onWinOpenShareQRCODE(obj) {
   
        if ($('#' + obj).is(':visible') == false) {
            $('#'+obj).modal('show');
        }
    }
</script>