﻿@model EcoolWeb.ViewModels.ADDI01ProxyEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.Search.ModeVal)
    @Html.HiddenFor(model => model.Search.SCHOOL_NO)

    <br />
    <div class="Div-EZ-ZZZI26">
        <div class="alert alert-success" style="padding:0 2%; margin-bottom:0px"><h3>代申請線上投稿</h3></div>
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group">
                @Html.LabelFor(model => model.Search.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.DropDownListFor(model => model.Search.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeUSER_NOUseReplaceWith()" })
                    @Html.ValidationMessageFor(model => model.Search.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Search.USER_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.DropDownListFor(model => model.Search.USER_NO, (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Search.USER_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Search.NumArticle, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.TextBoxFor(model => model.Search.NumArticle, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Search.NumArticle, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="text-right">
                @Html.PermissionButton("下一步", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-default", onclick = "Add()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            </div>
        </div>
    </div>




}
<div class="text-center">
    @Html.ActionLink("回選擇模式", "Index", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-default" })
</div>
@section css {
    <style>
        .form-group.focused {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            transition: all 0.3s ease;
        }

        .input-validation-error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .field-validation-error {
            display: block;
            margin-top: 5px;
            font-size: 0.875em;
        }

        .btn {
            transition: all 0.3s ease;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .Div-EZ-ZZZI26 {
            transition: all 0.3s ease;
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI01PROXY_URLS = {
            editAction: "@Url.Action("Edit", (string)ViewBag.BRE_NO)",
            getUserDropdownHtml: "@Url.Action("_GetUSER_NODDLHtml", (string)ViewBag.BRE_NO)",
            pageContentAction: "@Url.Action("_PageContent", (string)ViewBag.BRE_NO)"
        };
    </script>
    <script src="~/Scripts/ADDI01Proxy/manyarticle.js" nonce="cmlvaw"></script>
}



