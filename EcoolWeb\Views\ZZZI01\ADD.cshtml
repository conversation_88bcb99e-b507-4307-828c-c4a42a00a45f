﻿@model ECOOL_APP.EF.BDMT01

@{
    ViewBag.Title = "學校基本資料新增";
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="Details">
            <div class="panel-body">
                <div class="form-horizontal">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="form-group">
                        @Html.LabelFor(model => model.SCHOOL_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.SCHOOL_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "403605" } })
                            @Html.ValidationMessageFor(model => model.SCHOOL_NO, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.SCHOOL_NAME, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.SCHOOL_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "臺北市西湖國民小學" } })
                            @Html.ValidationMessageFor(model => model.SCHOOL_NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.SHORT_NAME, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.SHORT_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "西湖國小" } })
                            @Html.ValidationMessageFor(model => model.SHORT_NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.SCHOOL_TYPE, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.SCHOOL_TYPE, (IEnumerable<SelectListItem>)ViewBag.SchoolTypeItems, new { @class = "form-control", @placeholder = "國小" })
                            @Html.ValidationMessageFor(model => model.SCHOOL_TYPE, "", new { @class = "text-danger" })
                        </div>
                    </div>


                   
                    <div class="form-group">
                        @Html.LabelFor(model => model.CITY, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.CITY, new { htmlAttributes = new { @class = "form-control", @placeholder = "臺北市" } })
                            @Html.ValidationMessageFor(model => model.CITY, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Manager_Name, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.Manager_Name, new { htmlAttributes = new { @class = "form-control", @placeholder = "周逸政" } })
                            @Html.ValidationMessageFor(model => model.Manager_Name, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Manager_Phone, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.Manager_Phone, new { htmlAttributes = new { @class = "form-control", @placeholder ="02-27971267*174" } })
                            @Html.ValidationMessageFor(model => model.Manager_Phone, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.E_MAIL, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.E_MAIL, new { htmlAttributes = new { @class = "form-control", @placeholder = "" } })
                            @Html.ValidationMessageFor(model => model.E_MAIL, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">

                        <div class="form-group text-center">

                            <button value="Add" class="btn btn-default">
                                新增
                            </button>

                            <a href='@Url.Action("QUERY", "ZZZI01")' role="button" class="btn btn-default">
                                放棄編輯
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

 
    
}


@section Scripts
{
    <script type="text/javascript">
        $(function () {
            $("#Add").click(function () { return Valid(); });
        });

        function Valid() {

            var msg = '';
            var blStatus = false;
            if ($("#SCHOOL_NO").val() == '') {
                msg += '學校代號為必填\r\n';
            }

            if ($("#SCHOOL_NAME").val() == '') {
                msg += '學校名稱為必填\r\n';
            }

            if ($("#SHORT_NAME").val() == '') {
                msg += '學校簡稱為必填\r\n';
            }

            if ($("#CITY").val()  == '') {
                msg += '縣市不能為空\r\n';
            }

            //if ($("#Manager_Name").val() == '') {
            //    msg += '管理員不能為空\r\n';
            //}

            //if ($("#Manager_Phone").val() == '') {
            //    msg += '管理員手機不能為空\r\n';
            //}

            //if ($("#Manager_Phone").val()  == '') {
            //    msg += '管理員Email不能為空\r\n';
            //}

            if (msg != '') {
                alert(msg);
                blStatus = false;
            }
            else {
                blStatus = true;
            }

            return blStatus;
        }

       
    </script>
}