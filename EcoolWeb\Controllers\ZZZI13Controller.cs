﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI13Controller : Controller
    {

        private string Bre_NO = "ZZZI13";
        private string ErrorMsg = string.Empty;
        PeopleViewModelServuce PeopleDb = new PeopleViewModelServuce();
        ZZZI13ViewModelService Db = new ZZZI13ViewModelService();
        private UserProfile user;

        // GET: ZZZI13
        [CheckPermission] //檢查權限
        public ActionResult Index()
        {
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "帳號角色設定檔";

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }



            //學校清單
            List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO_NotALL(user.SCHOOL_NO,null, user);
            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
       

            ViewBag.MY_SCHOOL_NO = user.SCHOOL_NO;
            ViewBag.MY_USER_NO = user.USER_NO;
            ViewBag.MY_ROLE_ID = user.RoleID_Default;
            ViewBag.IsTeacher = false;

            //取得帳號與角色清單
            var model = Db.USP_ZZZI13ViewModel_QUERY(user.SCHOOL_NO, "", "", "", user);
            TempData["StatusMessage"] = "【勾選/取消】核取方塊後立即【增加角色/取消角色】";

            return View(model);
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        public ActionResult index(string SCHOOL_NO,string NAME, bool IsTeacher = false)
       {
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "帳號角色設定檔";

            //學校清單
            List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO_NotALL(SCHOOL_NO, null, user);
            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
     

            ViewBag.MY_SCHOOL_NO = user.SCHOOL_NO;
            ViewBag.MY_USER_NO = user.USER_NO;
            ViewBag.MY_ROLE_ID = user.RoleID_Default;

            ViewBag.IsTeacher = IsTeacher;

            

            //取得帳號與角色清單
            List<ZZZI13ViewModel> model = Db.USP_ZZZI13ViewModel_QUERY(SCHOOL_NO, "", "", "", user, IsTeacher);

            if (string.IsNullOrWhiteSpace(NAME)==false)
            {
                model= model.Where(a => a.NAME.Contains(NAME.Trim())).ToList();
            }

            

            return View(model);
        }


        [HttpPost]
        [CheckPermission(ResultType = "Json")] //檢查權限
        public JsonResult Save(string SCHOOL_NO, string USER_NO, string ROLE_ID, string Checked)
        {

            string Success = string.Empty;
            user = UserProfileHelper.Get();



            if (Success != "false")
            {
                try
                {
                    uHRMT25 Date = new uHRMT25();

                    Date.SCHOOL_NO = SCHOOL_NO;
                    Date.USER_NO = USER_NO;
                    Date.ROLE_ID = ROLE_ID;
                    Date.CHG_DATE = DateTime.Now;
                    Date.CHG_PERSON = user.USER_KEY;
                    Date.DEFAULT_YN = "N";


                    if (Checked == "true")
                    {
                        Db.AddDate(Date);
                    }
                    else
                    {
                        Db.DelDate(Date);
                    }

                    Db.DEFAULT_YN(Date.SCHOOL_NO, Date.USER_NO);


                    if (Db.ErrorMsg != null && Db.ErrorMsg != string.Empty)
                    {
                        Success = "false";
                        ErrorMsg = ErrorMsg + Db.ErrorMsg;
                    }
                    else
                    {
                        Success = "true";
                    }

                }
                catch (Exception ex)
                {
                    Success = "false";
                    ErrorMsg = ErrorMsg + ex;
                }
            }


            var data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"" + ErrorMsg + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }




    }
}