/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MiscSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"],{9824:[719,129,894,64,829],9825:[711,24,894,65,828],9826:[719,154,894,64,828],9827:[719,129,894,32,861],9837:[750,17,447,64,381],9838:[741,223,447,57,389],9839:[724,224,447,63,382]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/MiscSymbols.js");
