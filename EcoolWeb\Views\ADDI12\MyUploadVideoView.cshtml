﻿@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

 if (AppMode)
 {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.Action("_ListBeginForm", (string)ViewBag.BRE_NO, new { ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.MyUploadVideoView })