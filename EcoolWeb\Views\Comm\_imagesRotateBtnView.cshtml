﻿@model EcoolWeb.Models.imagesRotateBtnViewModel


@if (string.IsNullOrWhiteSpace(Model.ImgURL) == false)
{
    if (string.IsNullOrWhiteSpace(Model.SouceNO) == false)

    {
        <a role="button"
           onclick="imagesRotateJS1('@Model.ImgURL','-90','@Model.ImgID','@Model.SouceNO','@Model.user_NO',@Model.schoo_NO,@Model.sourceID);imagesRotateJS1('@Model.ImgURL_S','-90','@Model.ImgID','@Model.SouceNO','@Model.user_NO',@Model.schoo_NO,@Model.sourceID);imagesRotateJS1('@Model.ImgURL_M','-90','@Model.ImgID','@Model.SouceNO','@Model.user_NO',@Model.schoo_NO,@Model.sourceID);"
           class="btn btn-sm btn-default">
            影像逆時針旋轉90度 <i style="font-size:14px" class="fa">&#xf0e2;</i>
        </a>

        <a role="button"
           onclick="imagesRotateJS1('@Model.ImgURL','90','@Model.ImgID','@Model.SouceNO','@Model.user_NO',@Model.schoo_NO,@Model.sourceID);imagesRotateJS1('@Model.ImgURL_S','90','@Model.ImgID','@Model.SouceNO','@Model.user_NO',@Model.schoo_NO,@Model.sourceID);imagesRotateJS1('@Model.ImgURL_M','90','@Model.ImgID','@Model.SouceNO','@Model.user_NO',@Model.schoo_NO,@Model.sourceID);"
           class="btn btn-sm btn-default">
            影像順時針旋轉90度 <i style="font-size:14px" class="fa">&#xf01e;</i>
        </a>
    }
    else if (string.IsNullOrWhiteSpace(Model.ImgURL_S) && !string.IsNullOrWhiteSpace(Model.ImgURL))
    {
        <a role="button"
           onclick="imagesRotateJS('@Model.ImgURL','-90','@Model.ImgID');"
           class="btn btn-sm btn-default">
            影像逆時針旋轉90度 <i style="font-size:14px" class="fa">&#xf0e2;</i>
        </a>
        <a role="button"
           onclick="imagesRotateJS('@Model.ImgURL','90','@Model.ImgID');"
           class="btn btn-sm btn-default">
            影像順時針旋轉90度 <i style="font-size:14px" class="fa">&#xf01e;</i>
        </a>
    }
    else
    {
        <a role="button"
           onclick="imagesRotateJS('@Model.ImgURL','-90','@Model.ImgID');imagesRotateJS('@Model.ImgURL_S','-90','@Model.ImgID');imagesRotateJS('@Model.ImgURL_M','-90','@Model.ImgID');"
           class="btn btn-sm btn-default">
            影像逆時針旋轉90度 <i style="font-size:14px" class="fa">&#xf0e2;</i>
        </a>

        <a role="button"
           onclick="imagesRotateJS('@Model.ImgURL','90','@Model.ImgID');imagesRotateJS('@Model.ImgURL_S','90','@Model.ImgID');imagesRotateJS('@Model.ImgURL_M','90','@Model.ImgID');"
           class="btn btn-sm btn-default">
            影像順時針旋轉90度 <i style="font-size:14px" class="fa">&#xf01e;</i>
        </a>



    }

    <script type="text/javascript">
        function imagesRotateJS1(ImgURL_Val, RotateAngle_Val, IDX_Val, Source_NO, user_NO, schoo_NO, sourceID) {
        if (!ImgURL_Val) {
            return;
        }
        $.ajax({
            url: "@Url.Action("imagesRotate1", "Comm")",     // url位置
            type: 'post',                   // post/get
        data: {
                ImgURL: ImgURL_Val
            , RotateAngle: RotateAngle_Val
            , sourceNO: Source_NO
            , user_NO: user_NO
            , schoo_NO: schoo_NO
            , sourceID: sourceID
        },     // data
        async: false,
        dataType: 'json',               // xml/json/script/html
        cache: false,                   // 是否允許快取
        success: function (data) {


            $('#' + IDX_Val).attr("src", data + '?' + new Date());

            },
        error: function (xhr, err) {
                alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                alert("responseText: " + xhr.responseText);
            }
        });
    }
    function imagesRotateJS(ImgURL_Val, RotateAngle_Val, IDX_Val) {
        if (!ImgURL_Val) {
            return;
        }
        $.ajax({
            url: "@Url.Action("imagesRotate", "Comm")",     // url位置
            type: 'post',                   // post/get
        data: {
                ImgURL: ImgURL_Val
            , RotateAngle: RotateAngle_Val
        },     // data
        async: false,
        dataType: 'json',               // xml/json/script/html
        cache: false,                   // 是否允許快取
        success: function (data) {


            $('#' + IDX_Val).attr("src", data + '?' + new Date());

            },
        error: function (xhr, err) {
                alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                alert("responseText: " + xhr.responseText);
            }
        });
    }
    </script>
}

