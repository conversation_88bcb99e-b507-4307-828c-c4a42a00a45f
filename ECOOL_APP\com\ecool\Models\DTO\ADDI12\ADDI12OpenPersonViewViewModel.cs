﻿
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public  class ADDI12OpenPersonViewViewModel
    {
        public string WhereSTAGE_ID { get; set; }


        public string WhereSCHOOL_NO { get; set; }


        public string WhereCLASS_NO { get; set; }


        public string WhereNAME { get; set; }


        public List<SelectListItem> SchoolNoSelectItem { get; set; }

        public List<SelectListItem> ClassItems { get; set; }
        public string PersonRoleType { get; set; }

        /// <summary>
        /// 查詢
        /// </summary>
        public ADDI12SearchViewModel Search { get; set; }

        public virtual ICollection<ADDI12EditPeopleViewModel> Details { get; set; }

        public List<ADDI12EditPeopleViewModel> PersonData { get; set; }

    }
}