﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.EF
{
    public class GameEditMainViewModel
    {
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///活動名稱
        /// </summary>
        [DisplayName("活動名稱")]
        [Required]
        public string GAME_NAME { get; set; }

        [DisplayName("按讚數達到這個數字顯示動畫")]
        public int? LIKE_COUNT { get; set; }

        /// <summary>
        ///活動圖片
        /// </summary>
        [DisplayName("活動圖片")]
        public string GAME_IMG { get; set; }

        public string GAME_IMG_PATH { get; set; }

        public HttpPostedFileBase UploadGamerFile { get; set; }

        /// <summary>
        ///活動說明
        /// </summary>
        [DisplayName("活動說明")]
        public string GAME_DESC { get; set; }

        /// <summary>
        ///活動開始日
        /// </summary>
        [DisplayName("活動開始日")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        [Required]
        public DateTime? GAME_DATES { get; set; }

        /// <summary>
        ///活動結束日
        /// </summary>
        [DisplayName("活動結束日")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        [Required]
        public DateTime? GAME_DATEE { get; set; }

        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("建立人")]
        public string CRE_PERSON { get; set; }
        public string SUBJECT { get; set; }
        /// <summary>
        ///建立日
        /// </summary>
        [DisplayName("建立日")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日
        /// </summary>
        [DisplayName("修改日")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///活動類行 1.一般 2.有獎徵答
        /// </summary>
        [DisplayName("活動類行 1.一般 2.有獎徵答")]
        public byte? GAME_TYPE { get; set; }

        public DateTime? TEAM_GAME_DATES { get; set; }

        public DateTime? TEAM_GAME_DATEE { get; set; }
    }
}