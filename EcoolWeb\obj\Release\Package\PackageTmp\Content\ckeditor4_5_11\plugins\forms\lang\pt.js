﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'pt', {
	button: {
		title: 'Propriedades do botão',
		text: 'Texto (valor)',
		type: 'Tipo',
		typeBtn: 'Bot<PERSON>',
		typeSbm: 'Enviar',
		typeRst: 'Repor'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Propriedades da caixa de verificação',
		radioTitle: 'Propriedades do botão de rádio',
		value: 'Valor',
		selected: 'Selecion<PERSON>',
		required: 'Obrigatório'
	},
	form: {
		title: 'Propriedades do formulário',
		menu: 'Propriedades do formulário',
		action: 'Ação',
		method: 'Méto<PERSON>',
		encoding: 'Codificação'
	},
	hidden: {
		title: 'Propriedades do campo oculto',
		name: '<PERSON><PERSON>',
		value: '<PERSON><PERSON>'
	},
	select: {
		title: 'Propriedades da caixa de seleção',
		selectInfo: 'Informação',
		opAvail: 'Opções disponíveis',
		value: 'Valor',
		size: 'Tamanho',
		lines: 'linhas',
		chkMulti: 'Permitir seleções múltiplas',
		required: 'Obrigatório',
		opText: 'Texto',
		opValue: 'Valor',
		btnAdd: 'Adicionar',
		btnModify: 'Modificar',
		btnUp: 'Subir',
		btnDown: 'Descer',
		btnSetValue: 'Definir como valor selecionado',
		btnDelete: 'Apagar'
	},
	textarea: {
		title: 'Propriedades da área de texto',
		cols: 'Colunas',
		rows: 'Linhas'
	},
	textfield: {
		title: 'Propriedades do campo de texto',
		name: 'Nome',
		value: 'Valor',
		charWidth: 'Tamanho do caracter',
		maxChars: 'Máximo de caracteres',
		required: 'Obrigatório',
		type: 'Tipo',
		typeText: 'Texto',
		typePass: 'Senha',
		typeEmail: 'Email',
		typeSearch: 'Pesquisar',
		typeTel: 'Telefone',
		typeUrl: 'URL'
	}
} );
