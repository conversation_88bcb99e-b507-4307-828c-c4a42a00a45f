﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT04
    {

        ///Summary
        ///學校代碼代碼
        ///Summary
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///帳號
        ///Summary
        public string USER_NO { get; set; }

        ///Summary
        ///名稱
        ///Summary
        public string NAME { get; set; }
        

        ///Summary
        ///匿名名稱
        ///Summary
        public string SONAME { get; set; }


        ///Summary
        ///學生年級
        ///Summary
        public byte? HrGRADE { get; set; }

        ///Summary
        ///護照年級
        ///Summary
        public short? GRADE { get; set; }

        ///Summary
        ///完成否
        ///Summary
        public string PASS_YN { get; set; }

        ///Summary
        ///全部認證通過
        ///Summary
        public string FullApprove { get; set; }

        ///Summary
        ///學校代碼代碼
        ///Summary
        public string CLASS_NO { get; set; }

        ///Summary
        ///
        ///Summary
        public string CASH_YN { get; set; }

        ///Summary
        ///該年級完成日期
        ///Summary
        public Nullable<System.DateTime> PASS_DATE { get; set; }

    }
}
