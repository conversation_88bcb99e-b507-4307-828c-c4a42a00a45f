﻿@model EcoolWeb.ViewModels.AWAI07BankListViewModel
@{
    ViewBag.Title = "酷幣定存-學生酷幣定存一覽表";
}

@Html.Partial("_Title_Secondary")

@{
    Html.RenderAction("_BankMenu", new { NowAction = "BankListView" });
}


@using (Html.BeginForm("BankListView", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "BankList" }))
{
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.OrderColumn)
    @Html.HiddenFor(m => m.SortBy)

    <div class="form-inline" role="form" id="search">
        <div class="form-group">
            <label class="control-label">學號/姓名</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
        </div>
        <div class="form-group">
            <label class="control-label">年級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">狀態</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereStatus, (IEnumerable<SelectListItem>)ViewBag.StatusItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear()" />
    </div>
    <br />
    if (Model.BankDetailList!=null && Model.BankDetailList.Count() > 0)
    {
        <table class="table table-bordered table-hover table-responsive">
            <caption align="top">學生定存表</caption>
            <thead class="thead">
                <tr>
                    <th>
                        <a href="#" class="orderColumn" data-colname="NAME" onclick="doSort('NAME')">
                            學生
                            <img id="NAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="CLASS_NO" onclick="doSort('CLASS_NO')">
                            班級
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="BANK_DATE" onclick="doSort('BANK_DATE')">
                            申請日
                            <img id="BANK_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="ACCT_CODE" onclick="doSort('ACCT_CODE')">
                            存款類型
                            <img id="BANK_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="PERIOD_TYPE" onclick="doSort('PERIOD_TYPE')">
                            定存期別
                            <img id="PERIOD_TYPE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="INTEREST_RATE" onclick="doSort('INTEREST_RATE')">
                            利率
                            <img id="INTEREST_RATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="PERIOD_DATES" onclick="doSort('PERIOD_DATES')">
                            開始日
                            <img id="PERIOD_DATES" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="PERIOD_DATEE" onclick="doSort('PERIOD_DATEE')">
                            到期日
                            <img id="PERIOD_DATEE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="AMT" onclick="doSort('AMT')">
                            定存酷幣
                            <img id="AMT" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" data-colname="STATUS" onclick="doSort('STATUS')">
                            狀態
                            <img id="STATUS" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </a>
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.BankDetailList)
                {
                    <tr>
                        <td>@item.NAME</td>
                        <td>@item.CLASS_NO</td>
                        <td class="text-primary">@(item.BANK_DATE != null ? item.BANK_DATE.Value.ToString("yyyy-MM-dd") : "" )</td>
                        <td>@AWAT10.AcctCodeVal.GetDesc(item.ACCT_CODE)</td>
                        <td class="text-success">@Html.DisplayFor(modelItem => item.PERIOD_DESC)</td>
                        <td class="text-danger">@(item.INTEREST_RATE != null ? item.INTEREST_RATE.Value.ToString("P") : "" )</td>
                        <td>@(item.PERIOD_DATES != null ? item.PERIOD_DATES.Value.ToString("yyyy-MM-dd") : "" )</td>
                        <td>@(item.PERIOD_DATEE != null ? item.PERIOD_DATEE.Value.ToString("yyyy-MM-dd") : "" )</td>
                        <td class="text-danger">
                            @item.AMT.Value.ToString("#")
                        </td>
                        <td>@AWAT10.StatusVal.GetDesc(item.STATUS)</td>
                    </tr>
                }
            </tbody>
        </table>

        <div>
            @Html.Pager(Model.BankDetailList.PageSize, Model.BankDetailList.PageNumber, Model.BankDetailList.TotalItemCount).Options(o => o
                                   .DisplayTemplate("BootstrapPagination")
                                   .MaxNrOfPages(5)
                                   .SetPreviousPageText("上頁")
                                   .SetNextPageText("下頁")
                               )
        </div>
    }
    else
    {   
        <span class="text-danger">搜尋無符合條件的資料。</span>
    }
}


@section css{

    <style>
        table th {
            background-color: #44946b;
            color: white;
        }

        .table-bordered {
            background-color: white;
        }

        .orderColumn {
            color: #bdf5de;
        }
    </style>
}

@section scripts{
    <script>
        var targetFormID = "#BankList";

        $(document).ready(function () {
            var colname = document.getElementById('OrderColumn').value;
            var col = $("th").find("[data-colname=" + colname + "]");
            if (document.getElementById('SortBy').value == "ASC") {
                col.text(col.text() + '▲')
            } else {
                col.text(col.text() + '▼')
            }
        });

        // 換頁處理
        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSort(column) {

            var sorttype = document.getElementById('SortBy').value;

            // 再次選欄位再改排序
            if (document.getElementById('OrderColumn').value == column) {
                if (sorttype == "ASC") {
                    sorttype = "DESC";
                } else {
                    sorttype = "ASC";
                }
            } else {
                sorttype = "DESC";
            }
            document.getElementById('SortBy').value = sorttype;
            document.getElementById('OrderColumn').value = column;

            FunPageProc(1);
        }

        function todoClear() {
            $(':input').val('');
            $('option').attr('selected', false);
            $(targetFormID).submit();
        }
    </script>
}