/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Size1/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Size1={directory:"Size1/Regular",family:"STIXMathJax_Size1",testString:"\u00A0\u02C6\u02C7\u02DC\u02F7\u0302\u0303\u0305\u030C\u0330\u0332\u0338\u203E\u20D0\u20D1",32:[0,0,250,0,0],40:[1066,164,468,139,382],41:[1066,164,468,86,329],47:[1066,164,579,25,552],91:[1066,164,383,180,363],92:[1066,164,579,27,552],93:[1066,164,383,20,203],123:[1066,164,575,114,466],125:[1066,164,575,109,461],160:[0,0,250,0,0],710:[767,-554,560,0,560],711:[767,-554,560,0,560],732:[750,-598,558,-2,558],759:[-117,269,558,-2,558],770:[767,-554,560,0,560],771:[750,-598,560,0,560],773:[820,-770,1000,0,1000],780:[767,-554,560,0,560],816:[-117,269,560,0,560],818:[-127,177,1000,0,1000],824:[532,21,0,-720,-157],8254:[820,-770,1000,0,1000],8400:[749,-584,870,0,871],8401:[749,-584,871,0,871],8406:[735,-482,871,0,872],8407:[736,-482,871,0,872],8428:[-123,288,871,0,871],8429:[-123,288,871,0,871],8430:[-26,279,871,0,872],8431:[-25,279,871,0,872],8512:[1500,-50,1259,55,1204],8719:[1500,-49,1355,50,1305],8720:[1500,-49,1355,50,1305],8721:[1499,-49,1292,90,1202],8730:[1552,295,1057,112,1089],8731:[1552,295,1057,112,1089],8732:[1552,295,1057,112,1089],8747:[2000,269,585,56,1035],8748:[2000,269,895,56,1345],8749:[2000,269,1205,56,1655],8750:[2000,269,635,56,1035],8751:[2000,269,945,56,1345],8752:[2000,269,1255,56,1655],8753:[2000,269,635,56,1035],8754:[2000,269,635,56,1035],8755:[2000,269,635,56,1035],8896:[1500,-49,1265,60,1205],8897:[1500,-49,1265,60,1205],8898:[1510,-49,1265,118,1147],8899:[1500,-39,1265,118,1147],8968:[1066,164,453,180,426],8969:[1066,164,453,25,273],8970:[1066,164,453,180,428],8971:[1066,164,453,27,273],9140:[766,-544,1063,69,994],9141:[139,83,1063,68,993],9168:[676,14,200,67,133],9180:[60,153,926,0,926],9181:[777,-564,926,0,926],9182:[136,89,926,0,925],9183:[789,-564,926,0,925],9184:[66,212,1460,0,1460],9185:[842,-564,1460,0,1460],10098:[1066,164,566,205,539],10099:[1066,164,566,27,361],10214:[1066,164,515,180,486],10215:[1066,164,515,29,335],10216:[1066,164,578,116,462],10217:[1066,164,578,116,462],10218:[1066,164,798,116,670],10219:[1066,164,798,128,682],10627:[1066,164,712,114,587],10628:[1066,164,712,114,587],10629:[1066,164,632,135,546],10630:[1066,164,632,86,497],10744:[1566,279,806,25,781],10745:[1566,279,806,25,781],10752:[1500,-49,1555,52,1503],10753:[1500,-49,1555,52,1503],10754:[1500,-49,1555,52,1503],10755:[1500,-39,1265,118,1147],10756:[1500,-39,1265,118,1147],10757:[1500,-49,1153,82,1071],10758:[1500,-49,1153,82,1071],10759:[1500,-49,1530,60,1470],10760:[1500,-49,1530,60,1470],10761:[1500,-49,1482,60,1422],10762:[1500,-50,1292,90,1202],10763:[2000,269,914,56,1035],10764:[2000,269,1515,56,1965],10765:[2000,269,635,56,1035],10766:[2000,269,635,56,1035],10767:[2000,269,635,56,1035],10768:[2000,269,635,56,1035],10769:[2000,269,635,56,1035],10770:[2000,269,735,56,1035],10771:[2000,269,635,56,1035],10772:[2000,269,844,56,1054],10773:[2000,269,635,56,1035],10774:[2000,269,735,56,1035],10775:[2000,269,819,24,1039],10776:[2000,269,635,56,1035],10777:[2000,269,735,56,1035],10778:[2000,269,735,56,1035],10779:[2157,269,636,56,1036],10780:[2000,426,585,56,1035],11004:[867,363,690,133,557],11007:[867,363,410,100,310]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Size1"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size1/Regular/Main.js"]);
