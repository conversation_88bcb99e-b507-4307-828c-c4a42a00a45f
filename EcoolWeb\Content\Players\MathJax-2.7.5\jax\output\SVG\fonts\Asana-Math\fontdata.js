/*
 *  /MathJax/jax/output/SVG/fonts/Asana-Math/fontdata.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(z,e,E,o){var B="2.7.5";var c="AsanaMathJax_Alphabets",w="AsanaMathJax_Arrows",y="AsanaMathJax_DoubleStruck",C="AsanaMathJax_Fraktur",h="AsanaMathJax_Latin",v="AsanaMathJax_Main",n="AsanaMathJax_Marks",x="AsanaMathJax_Misc",F="AsanaMathJax_Monospace",A="AsanaMathJax_NonUnicode",s="AsanaMathJax_Normal",D="AsanaMathJax_Operators",a="AsanaMathJax_SansSerif",q="AsanaMathJax_Script",b="AsanaMathJax_Shapes",m="AsanaMathJax_Size1",l="AsanaMathJax_Size2",k="AsanaMathJax_Size3",i="AsanaMathJax_Size4",g="AsanaMathJax_Size5",f="AsanaMathJax_Size6",u="AsanaMathJax_Symbols",p="AsanaMathJax_Variants";var r="H",d="V",t={load:"extra",dir:r},j={load:"extra",dir:d};z.Augment({FONTDATA:{version:B,baselineskip:1200,lineH:800,lineD:200,FONTS:{AsanaMathJax_Alphabets:"Alphabets/Regular/Main.js",AsanaMathJax_Arrows:"Arrows/Regular/Main.js",AsanaMathJax_DoubleStruck:"DoubleStruck/Regular/Main.js",AsanaMathJax_Fraktur:"Fraktur/Regular/Main.js",AsanaMathJax_Latin:"Latin/Regular/Main.js",AsanaMathJax_Main:"Main/Regular/Main.js",AsanaMathJax_Marks:"Marks/Regular/Main.js",AsanaMathJax_Misc:"Misc/Regular/Main.js",AsanaMathJax_Monospace:"Monospace/Regular/Main.js",AsanaMathJax_NonUnicode:"NonUnicode/Regular/Main.js",AsanaMathJax_Normal:"Normal/Regular/Main.js",AsanaMathJax_Operators:"Operators/Regular/Main.js",AsanaMathJax_SansSerif:"SansSerif/Regular/Main.js",AsanaMathJax_Script:"Script/Regular/Main.js",AsanaMathJax_Shapes:"Shapes/Regular/Main.js",AsanaMathJax_Size1:"Size1/Regular/Main.js",AsanaMathJax_Size2:"Size2/Regular/Main.js",AsanaMathJax_Size3:"Size3/Regular/Main.js",AsanaMathJax_Size4:"Size4/Regular/Main.js",AsanaMathJax_Size5:"Size5/Regular/Main.js",AsanaMathJax_Size6:"Size6/Regular/Main.js",AsanaMathJax_Symbols:"Symbols/Regular/Main.js",AsanaMathJax_Variants:"Variants/Regular/Main.js"},VARIANT:{normal:{fonts:[v,s,F,h,c,n,w,D,u,b,x,p,A,m]},bold:{fonts:[v,s,F,h,c,n,w,D,u,b,x,p,A,m],bold:true,offsetA:119808,offsetG:120488,offsetN:120782},italic:{fonts:[v,s,F,h,c,n,w,D,u,b,x,p,A,m],italic:true,offsetA:119860,offsetG:120546,remap:{119893:8462}},"bold-italic":{fonts:[v,s,F,h,c,n,w,D,u,b,x,p,A,m],bold:true,italic:true,offsetA:119912,offsetG:120604},"double-struck":{fonts:[y],offsetA:120120,offsetN:120792,remap:{120122:8450,120127:8461,120133:8469,120135:8473,120136:8474,120137:8477,120145:8484}},fraktur:{fonts:[C],offsetA:120068,remap:{120070:8493,120075:8460,120076:8465,120085:8476,120093:8488}},"bold-fraktur":{fonts:[C],bold:true,offsetA:120172},script:{fonts:[q],italic:true,offsetA:119964,remap:{119965:8492,119968:8496,119969:8497,119971:8459,119972:8464,119975:8466,119976:8499,119981:8475,119994:8495,119996:8458,120004:8500}},"bold-script":{fonts:[q],bold:true,italic:true,offsetA:120016},"sans-serif":{fonts:[a],offsetA:120224,offsetN:120802},"bold-sans-serif":{fonts:[a],bold:true,offsetA:120276,offsetN:120812,offsetG:120662},"sans-serif-italic":{fonts:[a],italic:true,offsetA:120328},"sans-serif-bold-italic":{fonts:[a],bold:true,italic:true,offsetA:120380,offsetG:120720},monospace:{fonts:[F],offsetA:120432,offsetN:120822},"-Asana-Math-variant":{fonts:[v,s,F,h,c,n,w,D,u,b,x,p,A,m]},"-tex-caligraphic":{offsetA:57866,noLowerCase:1,fonts:[p,v,s,F,h,c,n,w,D,u,b,x,A,m],italic:true},"-tex-oldstyle":{offsetN:57856,fonts:[p,v,s,F,h,c,n,w,D,u,b,x,A,m]},"-tex-caligraphic-bold":{offsetA:57892,noLowerCase:1,fonts:[p,v,s,F,h,c,n,w,D,u,b,x,A,m],italic:true,bold:true},"-tex-oldstyle-bold":{fonts:[v,s,F,h,c,n,w,D,u,b,x,p,A,m],bold:true},"-tex-mathit":{fonts:[v,s,F,h,c,n,w,D,u,b,x,p,A,m],italic:true,noIC:true},"-largeOp":{fonts:[m,v]},"-smallOp":{}},RANGES:[{name:"alpha",low:97,high:122,offset:"A",add:26},{name:"Alpha",low:65,high:90,offset:"A"},{name:"number",low:48,high:57,offset:"N"},{name:"greek",low:945,high:969,offset:"G",add:26},{name:"Greek",low:913,high:1014,offset:"G",remap:{1013:52,977:53,1008:54,981:55,1009:56,982:57,1012:17}}],RULECHAR:8722,REMAP:{10:32,9666:9664,9667:9665,65080:9183,12296:10216,12297:10217,9642:9632,175:772,8432:42,10072:8739,978:933,9652:9650,9653:9651,65079:9182,9656:9654,697:8242,9662:9660,9663:9661},REMAPACCENT:{"\u007E":"\u0303","\u2192":"\u20D7","\u0060":"\u0300","\u005E":"\u0302","\u00B4":"\u0301","\u2032":"\u0301","\u2035":"\u0300"},REMAPACCENTUNDER:{},DELIMITERS:{40:{dir:d,HW:[[941,v],[1471,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{bot:[9117,u],ext:[9116,u],top:[9115,u]}},41:{dir:d,HW:[[941,v],[1471,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{bot:[9120,u],ext:[9119,u],top:[9118,u]}},45:{alias:8722,dir:r},47:{alias:8260,dir:r},61:{dir:r,HW:[[539,v]],stretch:{rep:[61,v]}},91:{dir:d,HW:[[910,v],[1476,m],[2045,l],[2556,k],[2615,k,1.023]],stretch:{bot:[9123,u],ext:[9122,u],top:[9121,u]}},92:{dir:d,HW:[[883,v],[1270,v,1.439],[1719,v,1.946],[2167,v,2.454],[2615,v,2.961]]},93:{dir:d,HW:[[910,v],[1476,m],[2045,l],[2556,k],[2615,k,1.023]],stretch:{bot:[9126,u],ext:[9125,u],top:[9124,u]}},94:{alias:770,dir:r},95:{alias:818,dir:r},123:{dir:d,HW:[[901,v],[1471,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{bot:[9129,u],ext:[9130,u],mid:[9128,u],top:[9127,u]}},124:{dir:d,HW:[[885,v],[1275,m],[1555,l],[1897,k],[2315,i],[2712,g],[3177,f]],stretch:{ext:[57344,f],top:[57344,f]}},125:{dir:d,HW:[[901,v],[1471,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{bot:[9133,u],ext:[9130,u],mid:[9132,u],top:[9131,u]}},126:{alias:771,dir:r},175:{alias:8722,dir:r},710:{alias:770,dir:r},713:{alias:8722,dir:r},732:{alias:771,dir:r},770:{dir:r,HW:[[312,v],[453,m],[633,l],[1055,k],[2017,i],[3026,g]]},771:{dir:r,HW:[[330,v],[701,m],[1053,l],[1403,k],[1865,i],[2797,g]]},773:{dir:r,HW:[[433,n],[511,m],[675,l],[1127,k]],stretch:{rep:[57345,f],right:[57345,f]}},774:t,780:{dir:r,HW:[[312,v],[737,m],[1105,l],[1474,k],[1960,i],[2940,g]]},818:{dir:r,HW:[[433,n],[511,m],[675,l],[1127,k]],stretch:{rep:[57346,f],right:[57346,f]}},819:t,831:t,8213:{alias:8722,dir:r},8214:{dir:d,HW:[[885,v],[1275,m],[1555,l],[1897,k],[2315,i]],stretch:{ext:[57349,f],top:[57349,f]}},8215:{alias:8722,dir:r},8254:{alias:8722,dir:r},8260:{dir:d,HW:[[837,v],[1205,m],[1471,l],[1795,k],[2189,i],[2615,i,1.195]]},8261:j,8262:j,8400:t,8401:t,8406:t,8407:t,8417:t,8425:t,8430:t,8431:t,8592:{dir:r,HW:[[884,v]],stretch:{left:[57363,f],rep:[9135,u],right:[57364,f]}},8593:{dir:d,HW:[[885,v]],stretch:{ext:[57365,f],top:[8593,v]}},8594:{dir:r,HW:[[884,v]],stretch:{left:[57366,f],rep:[9135,u],right:[57367,f]}},8595:{dir:d,HW:[[885,v]],stretch:{bot:[8595,v],ext:[57365,f]}},8596:{dir:r,HW:[[884,v]],stretch:{left:[57363,f],rep:[9135,u],right:[57367,f]}},8597:{dir:d,HW:[[884,v]],stretch:{top:[8593,v],ext:[57365,f],bot:[8595,v]}},8612:{dir:r,HW:[[942,w]],stretch:{left:[57363,f],rep:[9135,u],right:[57368,f]}},8614:{dir:r,HW:[[942,v]],stretch:{left:[57369,f],rep:[9135,u],right:[57367,f]}},8617:t,8618:t,8656:{dir:r,HW:[[884,v]],stretch:{left:[57372,f],rep:[57373,f],right:[57374,f]}},8657:{dir:d,HW:[[885,v]],stretch:{ext:[57375,f],top:[8657,v]}},8658:{dir:r,HW:[[884,v]],stretch:{left:[57376,f],rep:[57373,f],right:[57377,f]}},8659:{dir:d,HW:[[885,v]],stretch:{bot:[8659,v],ext:[57375,f]}},8660:{dir:r,HW:[[895,v]],stretch:{left:[57372,f],rep:[57373,f],right:[57377,f]}},8661:{dir:d,HW:[[884,v,null,8597]],stretch:{top:[8657,v],ext:[57375,f],bot:[8659,v]}},8719:{dir:d,HW:[[937,D],[1349,m],[1942,l],[2797,k]]},8720:j,8721:j,8722:{dir:r,HW:[],stretch:{rep:[8722,v,0,0,0,-0.23,-0.23]}},8725:{alias:8260,dir:d},8730:{dir:d,HW:[[1138,v],[1280,m],[1912,l],[2543,k],[3175,i]],stretch:{bot:[9143,u],ext:[8403,n],top:[57378,f]}},8739:{dir:d,HW:[[885,v]],stretch:{ext:[8739,v],top:[8739,v]}},8741:{dir:d,HW:[[885,v]],stretch:{ext:[8741,v],top:[8741,v]}},8745:j,8747:j,8748:j,8749:j,8750:j,8751:j,8752:j,8753:j,8754:j,8755:j,8896:j,8897:j,8898:j,8899:j,8968:{dir:d,HW:[[885,v],[1470,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{ext:[9122,u],top:[9121,u]}},8969:{dir:d,HW:[[885,v],[1470,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{ext:[9125,u],top:[9124,u]}},8970:{dir:d,HW:[[885,v],[1470,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{bot:[9123,u],ext:[9122,u]}},8971:{dir:d,HW:[[885,v],[1470,m],[2041,l],[2552,k],[2615,k,1.025]],stretch:{bot:[9126,u],ext:[9125,u]}},8978:{alias:9180,dir:r},8994:{alias:9180,dir:r},8995:{alias:9181,dir:r},9001:{alias:10216,dir:d},9002:{alias:10217,dir:d},9130:{dir:d,HW:[[688,u]],stretch:{ext:[9130,u]}},9135:{dir:r,HW:[[638,u]],stretch:{rep:[9135,u]}},9136:{alias:10182,dir:d},9137:{alias:10181,dir:d},9140:t,9141:t,9168:{dir:d,HW:[[885,v,null,124],[1270,v,1.435,124],[1719,v,1.942,124],[2167,v,2.448,124],[2615,v,2.955,124]],stretch:{ext:[124,v]}},9180:t,9181:t,9182:{dir:r,HW:[[902,v],[1471,m],[2041,l],[2552,k]],stretch:{left:[57382,f],rep:[57383,f],mid:[57388,f],right:[57384,f]}},9183:{dir:r,HW:[[902,v],[1471,m],[2041,l],[2552,k]],stretch:{left:[57385,f],rep:[57386,f],mid:[57389,f],right:[57387,f]}},9184:t,9185:t,9472:{alias:8722,dir:r},10072:{alias:8739,dir:d},10181:{dir:d,HW:[[910,u],[1020,m],[1531,l],[2041,k],[2552,i],[3063,g]]},10182:{dir:d,HW:[[910,u],[1020,m],[1531,l],[2041,k],[2552,i],[3063,g]]},10214:j,10215:j,10216:{dir:d,HW:[[885,v],[1020,m],[1270,m,1.244],[2041,l],[2552,k],[2615,k,1.025]]},10217:{dir:d,HW:[[885,v],[1020,m],[1270,m,1.244],[2041,l],[2552,k],[2615,k,1.025]]},10218:j,10219:j,10222:{alias:40,dir:d},10223:{alias:41,dir:d},10229:{alias:8592,dir:r},10230:{alias:8594,dir:r},10231:{alias:8596,dir:r},10232:{alias:8656,dir:r},10233:{alias:8658,dir:r},10234:{alias:8660,dir:r},10235:{alias:8612,dir:r},10236:{alias:8614,dir:r},10237:{alias:10502,dir:r},10238:{alias:10503,dir:r},10502:{dir:r,HW:[[884,w]],stretch:{left:[57372,f],rep:[57373,f],right:[57390,f]}},10503:{dir:r,HW:[[884,w]],stretch:{left:[57391,f],rep:[57373,f],right:[57377,f]}},10748:j,10749:j,10752:j,10753:j,10754:j,10755:j,10756:j,10757:j,10758:j,10759:j,10760:j,10761:j,10764:j,10765:j,10766:j,10767:j,10768:j,10769:j,10770:j,10771:j,10772:j,10773:j,10774:j,10775:j,10776:j,10777:j,10778:j,10779:j,10780:j,12296:{alias:10216,dir:d},12297:{alias:10217,dir:d},65079:{alias:9182,dir:r},65080:{alias:9183,dir:r}}}});MathJax.Hub.Register.LoadHook(z.fontDir+"/Main/Regular/Main.js",function(){z.FONTDATA.FONTS[v][8722][0]=z.FONTDATA.FONTS[v][43][0];z.FONTDATA.FONTS[v][8722][1]=z.FONTDATA.FONTS[v][43][1]});MathJax.Hub.Register.LoadHook(z.fontDir+"/Size6/Regular/Main.js",function(){var G;G=z.FONTDATA.DELIMITERS[9182].stretch.rep[0];z.FONTDATA.FONTS[f][G][0]+=100;z.FONTDATA.FONTS[f][G][1]+=100;G=z.FONTDATA.DELIMITERS[9183].stretch.rep[0];z.FONTDATA.FONTS[f][G][0]+=100;z.FONTDATA.FONTS[f][G][1]+=100});MathJax.Hub.Register.LoadHook(z.fontDir+"/Size1/Regular/Main.js",function(){var G;z.FONTDATA.FONTS[m][8747][2]-=300;for(G=8748;G<=8755;G++){z.FONTDATA.FONTS[m][G][2]-=420}for(G=10764;G<=10780;G++){z.FONTDATA.FONTS[m][G][2]-=420}});E.loadComplete(z.fontDir+"/fontdata.js")})(MathJax.OutputJax.SVG,MathJax.ElementJax.mml,MathJax.Ajax,MathJax.Hub);
