﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class BarcCodeMyCashIndexViewModel
    {
        /// <summary>
        /// 只顯示某一所學校
        /// </summary>
        public string WhereSchoolNo { get; set; }

        public string WhereUrlReferrer { get; set; }
        public string LoginYN { get; set; }

        /// <summary>
        /// 搜尋字串
        /// </summary>
        [DisplayName("學號/姓名")]
        public string WhereKeyword { get; set; }
        public string FROMACTION { get; set; }
     
        /// <summary>
        /// 多少秒自動轉頁
        /// </summary>
        public int? TimeoutSeconds { get; set; }
        public decimal? DelayTime { get; set; }
        public string ChangeMode { get; set; }

        /// <summary>
        /// 沒有借書
        /// </summary>
        public string NoBook { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// 是否顯示在手機
        /// </summary>
        public string ShowOnMobile { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        public int PageSize { get; set; }

        /// <summary>
        /// 酷幣清單
        /// </summary>
        public IPagedList<HRMT01QTY> ListData;

        /// <summary>
        /// 學生可兌換獎品 TOP 5 筆
        /// </summary>
        public List<AWAT02> AWAT02List;

        public string CLASS_NO { get; set; }
        public string SEAT_NO { get; set; }

        /// <summary>
        /// 使用者名稱
        /// </summary>
        public string UserNAME { get; set; }

        /// <summary>
        /// 顯示查詢結果
        /// </summary>
        public bool ShowStep2 = false;

        /// <summary>
        /// 總借書量
        /// </summary>
        public int AllBookQty { get; set; }

        public bool ISGIF { get; set; }

        /// <summary>
        /// 本學期借書量
        /// </summary>
        public int UserBookQty { get; set; }

        /// <summary>
        /// 月借書量
        /// </summary>
        public int MonthBookQty { get; set; }

        public int? SUMCASH_AVAILABLE { get; set; }

        /// <summary>
        /// 使用者現有酷幣數
        /// </summary>
        public int UserCash { get; set; }

        /// <summary>
        /// 使用者定存量
        /// </summary>
        public decimal UserAWAI07 { get; set; }

        /// <summary>
        /// 使用者總點數
        /// </summary>
        public int point { get; set; }

        /// <summary>
        ///總累計里程(m)
        /// </summary>
        [DisplayName("總累計里程(m)")]
        public double? RUN_TOTAL_METER { get; set; }

        [DisplayName("升級所需里數(m)")]
        public double? RUN_UPGRADE_METER { get; set; }

        /// <summary>
        /// 對應等級的獨角獸
        /// </summary>
        public int Level { get; set; }

        public ZZZI09MyDataViewModel MyData { get; set; }

        public BarcCodeMyCashIndexViewModel()
        {
            PageSize = int.MaxValue;
        }
    }
}