<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.ILoggingBuilder">
            <summary>
            An interface for configuring logging providers.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.ILoggingBuilder.Services">
            <summary>
            Gets the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> where Logging services are configured.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.CheckDisposed">
            <summary>
            Check if the factory has been disposed.
            </summary>
            <returns>True when <see cref="M:Microsoft.Extensions.Logging.LoggerFactory.Dispose"/> as been called</returns>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.MinLevel">
            <summary>
            Gets or sets the minimum level of log messages if none of the rules match.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.Rules">
            <summary>
            Gets the collection of <see cref="T:Microsoft.Extensions.Logging.LoggerFilterRule"/> used for filtering log messages.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFilterRule">
            <summary>
            Defines a rule used to filter log messages
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.ProviderName">
            <summary>
            Gets the logger provider type or alias this rule applies to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.CategoryName">
            <summary>
            Gets the logger category this rule applies to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel">
            <summary>
            Gets the minimum <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/> of messages.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.Filter">
            <summary>
            Gets the filter delegate that would be applied to messages that passed the <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggingBuilderExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" />.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.ProviderAliasAttribute">
            <summary>
            Defines alias for <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> implementation to be used in filtering rules.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <param name="configure">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> configuration delegate.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
    </members>
</doc>
