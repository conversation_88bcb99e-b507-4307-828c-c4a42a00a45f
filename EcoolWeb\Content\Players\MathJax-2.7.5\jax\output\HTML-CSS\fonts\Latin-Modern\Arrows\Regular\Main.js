/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Arrows/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Arrows={directory:"Arrows/Regular",family:"LatinModernMathJax_Arrows",testString:"\u00A0\u219F\u21A1\u21A4\u21A5\u21A7\u21B2\u21B3\u21B4\u21B5\u21C5\u21D6\u21D7\u21D8\u21D9",32:[0,0,332,0,0],160:[0,0,332,0,0],8607:[689,213,572,56,516],8609:[713,189,572,56,516],8612:[510,10,977,56,921],8613:[679,183,632,55,576],8615:[683,179,632,55,576],8626:[679,179,564,56,508],8627:[679,179,564,56,508],8628:[530,6,882,56,826],8629:[650,150,650,56,594],8645:[703,203,896,56,840],8662:[682,272,1066,56,1010],8663:[682,272,1066,56,1010],8664:[772,182,1066,56,1010],8665:[772,182,1066,56,1010],8668:[510,10,997,56,941],8678:[520,20,1050,56,994],8679:[725,213,652,56,596],8680:[520,20,1050,56,994],8681:[713,225,652,56,596],8691:[725,225,652,56,596],8693:[703,203,896,56,840],8694:[990,490,997,56,941],10228:[592,92,1121,56,1065],10235:[510,10,1443,56,1387],10237:[520,20,1437,56,1381],10238:[520,20,1437,56,1381],10239:[510,10,1463,56,1407],10502:[520,20,991,56,935],10503:[520,20,991,56,935]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Arrows"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Arrows/Regular/Main.js"]);
