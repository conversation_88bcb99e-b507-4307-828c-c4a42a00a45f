/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GeneralPunctuation.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{8208:[259,-193,333,39,285],8209:[257,-194,333,39,285],8210:[259,-193,500,0,500],8211:[250,-201,500,0,500],8212:[250,-201,1000,0,1000],8213:[250,-201,2000,0,2000],8214:[690,189,523,129,394],8215:[-141,300,500,0,500],8216:[676,-433,333,115,254],8217:[676,-433,333,79,218],8218:[102,141,333,79,218],8219:[676,-433,333,79,218],8220:[676,-433,444,43,414],8221:[676,-433,444,30,401],8222:[102,141,444,45,416],8223:[676,-433,444,30,401],8226:[444,-59,523,70,455],8229:[100,11,667,111,555],8240:[706,19,1109,61,1048],8241:[706,19,1471,61,1410],8243:[678,-401,426,75,351],8244:[678,-401,563,75,488],8245:[678,-402,289,75,214],8246:[678,-401,426,75,351],8247:[678,-401,563,75,488],8248:[102,156,511,59,454],8249:[416,-33,333,63,285],8250:[416,-33,333,48,270],8251:[547,41,685,48,635],8252:[676,9,549,130,452],8256:[709,-512,798,72,726],8259:[332,-172,333,39,285],8260:[676,14,167,-168,331],8263:[676,8,839,68,809],8270:[240,171,500,68,433],8271:[459,141,278,60,199],8272:[691,40,790,55,735],8273:[676,171,501,68,433],8274:[706,200,471,54,417],8279:[678,-401,710,75,635],8287:[0,0,1000,0,0]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/GeneralPunctuation.js");
