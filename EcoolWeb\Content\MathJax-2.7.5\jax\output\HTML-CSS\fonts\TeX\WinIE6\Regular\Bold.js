/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/WinIE6/Regular/Bold.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_WinIE6,{57920:[518,17,1150,64,1084],57921:[694,193,575,14,561],57922:[518,17,1150,65,1085],57923:[694,194,575,14,561],57924:[518,17,1150,64,1085],57925:[767,267,575,14,561],57926:[724,194,1150,64,1084],57927:[724,193,1150,64,1085],57928:[694,224,1150,65,1085],57929:[694,224,1150,64,1085],57930:[547,46,1150,64,1084],57931:[547,46,1150,47,1102],57932:[694,16,639,1,640],57933:[710,17,628,60,657],57934:[694,-1,639,64,574],57935:[686,24,958,56,901],57936:[587,86,767,97,670],57937:[587,86,767,96,670],57938:[750,250,575,63,511],57939:[820,180,958,78,988],57940:[451,8,894,65,830],57941:[452,8,1150,65,1084],57942:[714,0,722,55,676],57943:[750,249,319,129,190],57944:[750,248,575,145,430],57945:[604,17,767,64,702],57946:[604,16,767,64,702],57947:[603,16,767,64,702],57948:[604,16,767,64,702],57949:[711,211,569,64,632],57950:[391,-109,894,64,828],57951:[524,-32,894,64,829],57952:[711,210,894,64,829],57953:[505,3,894,64,829],57954:[697,199,894,96,797],57955:[697,199,894,96,797],57956:[617,116,1150,64,1085],57957:[618,116,1150,64,1085],57958:[587,85,894,96,797],57959:[587,86,894,96,796],57960:[697,199,894,96,797],57961:[697,199,894,96,796],57962:[632,132,894,64,828],57963:[632,132,894,64,828],57964:[693,-1,894,65,829],57965:[711,-1,1022,69,953],57966:[500,210,1022,68,953],57967:[711,211,1150,65,1084],57968:[719,129,894,64,829],57969:[711,24,894,65,828],57970:[719,154,894,64,828],57971:[719,129,894,32,861],57972:[750,17,447,64,381],57973:[741,223,447,57,389],57974:[724,224,447,63,382]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/WinIE6/Regular/Bold.js");
