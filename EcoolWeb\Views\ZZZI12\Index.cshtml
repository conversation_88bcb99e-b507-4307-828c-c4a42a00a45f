﻿@model IEnumerable<ECOOL_APP.com.ecool.Models.DTO.ZZZI12ViewModel>

@using global::ECOOL_APP.com.ecool.LogicCenter;
@{
    ViewBag.Title = "帳號權限維護";
    var user = EcoolWeb.Models.UserProfileHelper.Get();
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ActionLink("切換批次權限新增", "BatchIndex", "ZZZI12", null, new { @class = "btn btn-success btn-xs" })

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1" }))
{
    <br />
    <div class="form-inline" role="form" id="Q_Div">
        <div class="form-group">
            <label class="control-label">角色</label>
        </div>
        <div class="form-group">
            <select class="form-control input-sm" name="Q_ROLE_ID">
                @{
                    string Selected_Val;

                    foreach (var item in ViewBag.HRMT24ListItems as List<ECOOL_APP.com.ecool.Models.entity.uHRMT24>)
                    {
                        if (ViewBag.Q_ROLE_ID == item.ROLE_ID)
                        {
                            Selected_Val = "selected";
                        }
                        else
                        {
                            Selected_Val = "";
                        }

                        <option value="@item.ROLE_ID" @Selected_Val>@item.ROLE_NAME</option>
                    }
                }

            </select>
        </div>
        <br /><br />
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            <select class="form-control input-sm" name="Q_SCHOOL_NO">
                @{
                    foreach (var item in ViewBag.SchoolNoItems as List<SelectListItem>)
                    {
                        if (ViewBag.Q_SCHOOL_NO == item.Value)
                        {
                            Selected_Val = "selected";
                        }
                        else
                        {
                            Selected_Val = "";
                        }

                        <option value="@item.Value" @Selected_Val>@item.Text</option>
                    }
                }
            </select>
        </div>
        <br /><br />
        <div class="form-group">
            <label class="control-label">帳號</label>
        </div>
        <div class="form-group">
            <input class="form-control input-sm" name="Q_USER_NO" type="text">
        </div>
        <div class="form-group">
            <label class="control-label">姓名</label>
        </div>
        <div class="form-group">
            <input class="form-control input-sm" name="Q_NAME" type="text">
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <br />
    <br />
    <div class="panel panel-ACC" name="top">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-Group  text-center">
            人員清單
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    <input id="SCHOOL_NO" name="SCHOOL_NO" type="hidden" value="@ViewBag.SCHOOL_NO" />
                    <input id="USER_NO" name="USER_NO" type="hidden" value="@ViewBag.USER_NO" />
                    @{
                        string BtnClass;

                        foreach (var item in ViewBag.PeopleList as List<ECOOL_APP.com.ecool.Models.DTO.PeopleViewModel>)
                        {

                            if (ViewBag.SCHOOL_NO == item.SCHOOL_NO && ViewBag.USER_NO == item.USER_NO)
                            {
                                BtnClass = "btn btn-default btn-block active";
                            }
                            else
                            {
                                BtnClass = "btn btn-default btn-block";
                            }

                            <div class="col-md-3">
                                <a class="@BtnClass" href="#" role="button" onclick="SelectPeople('@item.SCHOOL_NO','@item.USER_NO')">@item.NAME</a>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>


    <div class="panel panel-ACC">
        <div class="panel-Group  text-center">
            權限清單
        </div>
        <div class="panel-body">
            <table class="table-ecool table-hover table-ecool-ACC">
                <thead>
                    <tr>
                        <th style="text-align:left">功能名稱</th>
                        <th style="text-align:left">動作</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        int num = 0;

                        foreach (var item in Model)
                        {
                            if (item.BRE_TYPE == "3") // (1.全部、2.依角色、3.標題)
                            {
                                <tr class="Group">
                                    <td colspan="2">@Html.DisplayFor(modelItem => item.BRE_NAME)</td>
                                </tr>
                            }
                            else
                            {
                                <tr style="text-align:left">
                                    <td width="30%">@Html.DisplayFor(modelItem => item.BRE_NAME)</td>
                                    <td width="70%">
                                        @{
                                            foreach (var D_item in item.Details_List)
                                            {
                                                if (D_item.ACTION_ID != "")
                                                {
                                                    string CheckBoxName = D_item.BRE_NO + '_' + D_item.ACTION_ID;




                                                    if (D_item.ACTION_Checked == true || D_item.ROLE_Checked == true || D_item.MY_Permission_Checked == false || (ViewBag.MY_RoleID == "0" && ViewBag.Q_ROLE_ID == "0") || (ViewBag.SCHOOL_NO == ViewBag.MY_SCHOOL_NO && ViewBag.USER_NO == ViewBag.MY_USER_NO))
                                                    {

                                                        string titleValue;


                                                        if (ViewBag.MY_RoleID == "0" && ViewBag.Q_ROLE_ID == "0")
                                                        {
                                                            titleValue = "您是" + ViewBag.MaxROLEName + "，擁有最大權限，無需異動。";
                                                        }
                                                        else if ((ViewBag.SCHOOL_NO == ViewBag.MY_SCHOOL_NO && ViewBag.USER_NO == ViewBag.MY_USER_NO))
                                                        {
                                                            titleValue = "自已無法異動自已權限。需要比你更高權限者，才能異動你的權限。";
                                                        }
                                                        else if (D_item.ACTION_Checked == true)
                                                        {
                                                            titleValue = "此功能不限制權限，全部的使用者可使用。";
                                                        }
                                                        else if (D_item.ROLE_Checked == true)
                                                        {
                                                            titleValue = "此功能角色權限已勾選。";
                                                        }
                                                        else
                                                        {
                                                            titleValue = "您無權異動此功能。";
                                                        }


                                                        @Html.CheckBox(CheckBoxName, D_item.Checked, new { @disabled = "disabled", @title = titleValue, @class = "initialism" });
                                                        <abbr class="initialism" title="@titleValue" style="color:#A9A9A9">
                                                            @Html.DisplayFor(modelItem => D_item.ACTION_NAME, new { @title = titleValue, @class = "initialism" })
                                                        </abbr>


                                                    }
                                                    else
                                                    {
                                                        @Html.CheckBox(CheckBoxName, D_item.Checked, new { @id = CheckBoxName, @class = "initialism", @onclick = "DbSave('" + D_item.SCHOOL_NO + "','" + D_item.USER_NO + "','" + D_item.BRE_NO + "','" + D_item.ACTION_ID + "',this.checked,this.id)" }) ;
                                                        @Html.DisplayFor(modelItem => D_item.ACTION_NAME) <article></article>

                                                    }

                                                    @Html.HiddenFor(modelItem => D_item.ACTION_ID);
                                                    @Html.HiddenFor(modelItem => D_item.ACTION_TYPE);
                                                    @Html.HiddenFor(modelItem => D_item.BRE_NO);
                                                    @Html.HiddenFor(modelItem => D_item.ROLE_ID);

                                                }
                                            }
                                        }
                                    </td>
                                </tr>
                            }
                            num++;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <div class="form-group text-center">
        <a class="btn btn-default" href="@Url.Action("Index", (string)ViewBag.BRE_NO)#top">Top</a>
    </div>
}

@section Scripts {
    <script language="JavaScript">
        function SelectPeople(SCHOOL_NO, USER_NO)
        {
            $('#SCHOOL_NO').val(SCHOOL_NO)
            $('#USER_NO').val(USER_NO)

            form1.submit();
        }


        function DbSave(SCHOOL_NO_Val, USER_NO_Val, BRE_NO_Val, ACTION_ID_Val, Checked_Val, ID_VAL) {

            $.ajax({
                url: "@(Url.Action("Save", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    SCHOOL_NO: SCHOOL_NO_Val
                    , USER_NO: USER_NO_Val
                    , BRE_NO: BRE_NO_Val
                    , ACTION_ID: ACTION_ID_Val
                    , Checked: Checked_Val
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        alert(res.Error);

                        if (Checked_Val) {
                            $('#' + ID_VAL).prop("checked", false)
                        }
                        else {
                            $('#' + ID_VAL).prop("checked", true)
                        }
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }

            });

        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            form1.submit();
        }
    </script>
}
