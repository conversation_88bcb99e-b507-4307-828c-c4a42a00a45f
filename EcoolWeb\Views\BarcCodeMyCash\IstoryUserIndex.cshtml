﻿@model UserIndexViewModel

@{
    //ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ECOOL_APP.UserProfile user = new ECOOL_APP.UserProfile();
    user.USER_NO = Model.USER_NO;
    user.SCHOOL_NO = Model.SCHOOL_NO;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string whereFriendsUserNo = HRMT07.GetStringMyADDStudent(user) ?? "";
    string ActiveFriends = (user.USER_NO == whereFriendsUserNo) ? "active" : "";
    string unicornPhoto_bg = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", ViewBag.Level, "lg"));
    string unicornPhoto_sm = Url.Content(string.Format("~/Content/img/unicorn/level{0}_{1}.png", ViewBag.Level, "sm"));
}
@*<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />*@
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<style>
    @@media (min-width: 1367px) {
        html {
            font-size: 24px !important;
        }
    }

    @@media (max-width: 1366px) {
        .mobileHidden-xs {
            display: none !important;
        }

        .card {
            height: 480px !important;
        }
    }

    @@media (max-width: 1000px) {
        .card {
            height: 420px !important;
        }
    }

    @@media (max-width: 600px) {
        .prod-icon {
            display: none;
        }

        img {
            height: 70px !important;
        }

        .titleLable {
            font-size: 20px;
        }

        .actionbarItem {
            display: none;
        }

        .card {
            height: 230px !important;
        }

        label {
            font-size: 12px;
            margin-bottom: 0px;
        }

        .form-group {
            margin-bottom: 1px;
        }
    }

    /*html, body, pageBackGround {
            background-color: rgba(243, 200, 176, 0.2);
        }*/

    .titleRow {
        margin: 0px;
        box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
    }

    body {
        overflow-y: hidden;
        overflow-x: hidden;
    }

    .actionbarItem {
        margin: 4px 1%;
    }

    .btn {
        border-radius: 0;
    }

    .btn-prod {
        margin-top: 2px;
    }

    .titleLable {
        font-size: 45px;
        color: white;
        text-shadow: 0px 0px 5px black;
    }

    .orderSel {
        height: 35px;
        margin-top: 3px;
        width: 140px;
    }

    .center {
        display: inline-block;
        margin: auto;
        margin-top: 20px;
    }

    img.center {
        height: 30vh;
    }

    .card {
        box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
        transition: 0.3s;
        width: 100%;
        background-color: white;
        padding: 2%;
        height: 45vh;
        position: relative;
    }

        .card:hover {
            box-shadow: 0 0 8px 0 #ea8a66;
            transform: scale(1.04);
            z-index: 2;
        }

    .prizebox {
        margin-top: 3px;
        padding: 5px;
    }

    .mybox {
        display: inline-block;
    }

    #cboxOverlay {
        background-color: black
    }

    .label_dd_font48 {
        font-size: 48px;
        font-weight: bold;
        line-height: 24px;
        letter-spacing: 1.5px;
    }
</style>
<div class="row titleRow">
    <label class="pull-left actionbarItem titleLable">
        i story
    </label>

    <label class="actionbarItem" style="font-size:28px;padding-top:20px;">
        目前酷幣點數 @Model.CASH 點, 定存酷幣  @(string.Format("{0:0.###} 點", Model.UserAWAI07)),你的運動撲滿(跑步)累積 @Model.Run 公里
    </label>

    <b class="actionbarItem" style="font-size:28px;padding-top:20px;"> </b>
</div>

<div class="container-fluid">
    <div class="row centered">

        <div class="col-sm-4 col-md-4">

            @*<div class="text-center">
                    <br /> <br />
                    <span style="font-size:20px">     <b> @Model.UserNAME,第 @ViewBag.Level 級 </b>   </span>
                </div>
                <br />
                <div class="text-center">
                    <span style="font-size:20px">   <b> @Model.CLASS_NO 班  @Model.SEAT_NO 號 </b></span>
                </div>*@

            @*<div class="text-white text-center" style="font-size:90% ;  top: calc(85% - 10px); left: calc(40% );position: absolute; color:#000000;font-size:20px">
                    <b>我的默默 @ViewBag.Level 級</b>
                </div>*@
        </div>
    </div>
</div>
<div class="container-fluid">
    <div class="row centered">
        <div class="col-md-3" style="border-bottom-style: solid;border-right-style: solid;border-color:#08eeee;text-align:center">

            <div class="mybox">
                <img src="@Url.Content("~/assets/img/線上投稿.png")" href="@Url.Content("~/assets/img/線上投稿.png")" title="" alt="icon圖片" class="center img-responsive">
            </div>

            <div class="center">
                <div class="form-group">
                    <label class="label_dd_font48" title="我的文章">

                        <a href="javascript:void(0)" onclick="funBookW()">我的文章</a>
                    </label>
                </div>
                <div class="form-group" style="margin-bottom:0px;">
                    <div class="prod-text">
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <div class=" col-md-3" style="border-bottom-style: solid;border-right-style: solid;border-color:#08eeee;text-align:center">

            <div class="mybox">
                <img src="@Url.Content("~/assets/img/藝廊.png")" href="@Url.Content("~/assets/img/藝廊.png")" title="" alt="icon圖片" class="center img-responsive">
            </div>
            <div class="center">
                <div class="form-group">
                    <label class="label_dd_font48" title="我的藝廊">
                        <a href="@Url.Action("OneIndex", "ZZZI34", new { WhereMyWork = "True", WhereIsColorboxForUser = "True" })" class="group1">

                            <span id="fackbook">我的藝廊</span>
                        </a>
                    </label>
                    @*<label class="label_dd_font48" title="我的藝廊">我的藝廊</label>*@
                </div>
                <div class="form-group" style="margin-bottom:0px;">

                    <div class="prod-text">
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3" style="border-bottom-style: solid;border-right-style: solid;border-color:#08eeee;text-align:center">
            <div>
                <div class="mybox">
                    <img src="@Url.Content("~/assets/img/閱讀.png")" href="@Url.Content("~/assets/img/閱讀.png")" title="" alt="icon圖片" class="center img-responsive">
                </div>
                <div class="center">
                    <div class="form-group">
                        <label class="label_dd_font48" title="我的閱讀認證">
                            <a href="@Url.Action("BOOK_APPLY", "SECI01", new { WhereIsColorboxForUser = "True" })" class="group1">
                                <span id="fackbook"> 我的閱讀認證</span>
                            </a>
                        </label>
                    </div>
                    <div class="form-group" style="margin-bottom:0px;">
                        <div class="prod-text">
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class=" col-md-3" style="border-bottom-style: solid;border-right-style: solid;border-color:#08eeee;text-align:center">
            <div>
                <div class="mybox">
                    <img src="@Url.Content("~/assets/img/跑步.png")" href="@Url.Content("~/assets/img/跑步.png")" title="" alt="icon圖片" class="center img-responsive">
                </div>
                <div class="center">
                    <div class="form-group">
                        <label class="label_dd_font48" title="我的運動撲滿">
                            <a href="@Url.Action("RunMapIstory", "ADDI11", new { WhereIsColorboxForUser = "True" })" class="group1">
                                <span id="fackbook"> 我的運動撲滿</span>
                            </a>
                        </label>
                    </div>
                    <div class="form-group" style="margin-bottom:0px;">
                        <div class="prod-text">
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3" style="border-right-style: solid;border-color:#08eeee;text-align:center">

            <div class="mybox">
                <img src="@Url.Content("~/assets/img/e酷幣.png")" href="@Url.Content("~/assets/img/e酷幣.png")" title="" alt="icon圖片" class="center img-responsive">
            </div>
            <div class="center">
                <div class="form-group">
                    <label class="label_dd_font48" title="我的小小舞臺">
                        <a href="@Url.Action("MyVideoView", "ADDI12", new { WhereIsColorboxForUser = "True" })" class="group1">
                            <span id="fackbook">我的小小舞臺</span>
                        </a>
                    </label>
                </div>
                <div class="form-group" style="margin-bottom:0px;">
                    <div class="prod-text">
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3" style="border-right-style: solid;border-color:#08eeee;text-align:center">
            <div>
                <div class="mybox">
                    <img src="@Url.Content("~/assets/img/校內.png")" href="@Url.Content("~/assets/img/校內.png")" title="" alt="icon圖片" class="center img-responsive">
                </div>
                <div class="center">
                    <div class="form-group">
                        <label class="label_dd_font48" title="我的校內表現">
                            <a href="@Url.Action("QUERY", "ADDI06", new { WhereIsColorboxForUser = "True", whereUserNo = user.USER_NO })" class="group1">
                                <span id="fackbook">我的校內表現</span>
                            </a>
                        </label>
                    </div>
                    <div class="form-group" style="margin-bottom:0px;">
                        <div class="prod-text">
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class=" col-md-3 " style="border-right-style: solid;border-color:#08eeee;text-align:center">
            <div>
                <div class="mybox">
                    <img src="@Url.Content("~/assets/img/校外.png")" href="@Url.Content("~/assets/img/校外.png")" title="" alt="icon圖片" class="center img-responsive">
                </div>
                <div class="center">
                    <div class="form-group">
                        <label class="label_dd_font48" title="我的校外表現">
                            <a href="@Url.Action("QUERY", "ADDI07", new { WhereIsColorboxForUser = "True", whereUserNo = user.USER_NO })" class="group1">
                                <span id="fackbook">我的校外榮譽</span>
                            </a>
                        </label>
                    </div>
                    <div class="form-group" style="margin-bottom:0px;">
                        <div class="prod-text">
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class=" col-md-3" style="border-right-style: solid;border-color:#08eeee;text-align:center">
            <div>
                <div class="mybox">
                    <img src="@Url.Content("~/assets/img/chart.png")" href="@Url.Content("~/assets/img/chart.png")" title="" alt="icon圖片" class="center img-responsive">
                </div>
                <div class="center">
                    <div class="form-group">
                        <label class="label_dd_font48" title="我的健康資料">
                            <a href="@Url.Action("Details", "SECI03", new { WhereIsColorboxForUser = "True", whereIDNO = Model.IDNO, ShowOnMobile = "True" })" class="group1">
                                <span id="fackbook"> 我的健康資料</span>
                            </a>
                        </label>
                    </div>
                    <div class="form-group" style="margin-bottom:0px;">
                        <div class="prod-text"> <span></span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class=" col-sm-6">

            <img src="@Url.Content("~/assets/img/boy.png")" href="@Url.Content("~/assets/img/boy.png")" style="width:12%;height:auto" title="" alt="icon圖片" class="center img-responsive">
        </div>
    </div>
</div>

<div style="display:none">
    @{
        int No = 1;
        string IdName = string.Empty;

        <div class="arrWRITING_NO">
            @foreach (var item in Model.arrWRITING_NO.OrderByDescending(x => x).ToList())
            {
                IdName = "W" + No.ToString();

                <a id="@IdName" class="groupWRITING_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                    @item
                </a>
                No++;
            }
        </div>
    }
</div>

<script>
    $(document).ready(function () {
        $(".groupWRITING_NO").colorbox({ iframe: true, opacity: 0.5, width: "90%", height: "90%", rel: 'groupWRITING_NO' });
        $(".group1").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });
    });

    function funBookW() {

        if ($('#W1').length > 0) {
            $('#W1').click();
        }
        else {
            alert('無任何資料')
        }
    }
</script>