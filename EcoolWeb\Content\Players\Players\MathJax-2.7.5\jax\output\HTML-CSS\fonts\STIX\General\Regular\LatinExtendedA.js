/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LatinExtendedA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{256:[773,0,722,15,707],257:[561,10,444,37,442],258:[876,0,722,15,707],259:[664,10,444,37,442],260:[674,165,722,15,707],261:[460,165,444,37,472],262:[890,14,667,28,633],263:[678,10,444,25,412],264:[886,14,667,28,633],265:[674,10,444,25,412],266:[834,14,667,28,633],267:[622,10,444,25,412],268:[886,14,667,28,633],269:[674,10,444,25,412],270:[886,0,722,16,685],271:[701,10,586,27,604],272:[662,0,722,16,685],273:[683,10,500,27,507],274:[773,0,611,12,597],275:[561,10,444,25,424],276:[876,0,611,12,597],277:[664,10,444,25,424],278:[834,0,611,12,597],279:[622,10,444,25,424],280:[662,165,611,12,597],281:[460,165,444,25,424],282:[886,0,611,12,597],283:[674,10,444,25,424],284:[886,14,722,32,709],285:[674,218,500,28,470],286:[876,14,722,32,709],287:[664,218,500,28,470],288:[834,14,722,32,709],289:[622,218,500,28,470],290:[676,280,722,32,709],291:[766,218,500,28,470],292:[886,0,722,18,703],293:[886,0,500,9,487],294:[662,0,723,17,702],295:[683,0,500,8,487],296:[850,0,333,1,331],297:[638,0,278,-25,305],298:[773,0,333,11,322],299:[561,0,278,-21,290],300:[876,0,333,18,315],301:[664,0,278,-1,280],302:[662,165,333,18,315],303:[683,165,278,16,277],304:[834,0,333,18,315],306:[662,14,747,18,728],307:[683,218,538,16,454],308:[886,14,373,-6,367],309:[674,218,278,-70,295],310:[662,280,722,33,723],311:[683,280,500,7,505],312:[459,0,542,5,532],313:[890,0,611,12,598],314:[890,0,278,19,257],315:[662,280,611,12,598],316:[683,280,278,19,257],317:[683,0,611,12,598],318:[702,0,381,19,362],319:[662,0,620,29,615],320:[683,0,370,19,354],321:[662,0,611,10,597],322:[683,0,278,19,259],323:[890,11,722,12,707],324:[678,0,500,16,485],325:[662,280,722,12,707],326:[460,280,500,16,485],327:[886,11,722,12,707],328:[674,0,500,16,485],329:[702,0,590,20,566],330:[678,18,710,16,673],331:[460,218,504,16,424],332:[773,14,722,34,688],333:[561,10,500,29,470],334:[876,14,722,34,688],335:[664,10,500,29,470],336:[890,14,722,34,688],337:[678,10,500,29,470],338:[668,6,889,30,885],339:[460,10,722,30,690],340:[890,0,667,17,660],341:[678,0,333,5,335],342:[662,280,667,17,660],343:[460,280,333,5,335],344:[886,0,667,17,660],345:[674,0,333,5,335],346:[890,14,556,43,491],347:[678,10,389,51,348],348:[886,14,556,43,491],349:[674,10,389,40,351],350:[676,215,556,43,491],351:[459,215,389,51,348],352:[924,14,556,43,491],353:[674,10,389,38,349],354:[662,215,611,17,593],355:[579,215,278,13,279],356:[886,0,611,17,593],357:[701,10,315,13,333],358:[662,0,613,17,593],359:[584,5,279,11,280],360:[849,14,722,14,705],361:[638,10,500,9,480],362:[773,14,722,14,705],363:[561,10,500,9,480],364:[876,14,722,14,705],365:[664,10,500,9,480],366:[898,14,722,14,705],367:[711,10,500,9,480],368:[890,14,722,14,705],369:[678,10,500,9,480],370:[662,165,722,14,705],371:[450,156,500,9,480],372:[886,11,944,5,932],373:[674,14,722,21,694],374:[886,0,722,22,703],375:[674,218,500,14,475],376:[872,0,722,22,703],377:[890,0,612,10,598],378:[678,0,444,27,418],379:[834,0,612,10,598],380:[622,0,444,27,418],381:[924,0,612,10,598],382:[674,0,444,27,418],383:[683,0,334,20,383]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/LatinExtendedA.js");
