﻿@{
    ViewBag.Title = "獎品兌換-修改獎品兌換內容";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    string cMODE = Request["MODE"];
    string cunProduct = Request["unProduct"];
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


<form action="#" name="contentForm" id="contentForm" method="post">
    <input type="hidden" id="txt" name="txt" value="@Request["hidAWARD_NO"]" />
    <input type="hidden" id="txtMODE" name="txtMODE" value="@Request["MODE"]" />
    <input type="hidden" id="txtunProduct" name="txtunProduct" value="@Request["unProduct"]" />


    <img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Awat02">
        <div id="showView">
        </div>

        <div class="row Div-btn-center">
            <div class="form-group">
         
                    @if (Request["hidAWARD_STS"] == "M")
                    {
                        if (string.IsNullOrWhiteSpace(cunProduct))
                        {
                            <div class="col-md-offset-3 col-md-5">
                                <input type="submit" value="確定修改" class="btn btn-default" onclick="doModify();" />
                            </div>
                        }
                        else
                        {
                            <div class="col-md-offset-1 col-md-7">
                                <input type="submit" value="修改同時重新上架" class="btn btn-default" onclick="doModify();" />
                                <input type="submit" value="刪除獎品" class="btn btn-default" onclick="doRealDelete();" />
                            </div>
                            
                        }
                    }
                    else if (Request["hidAWARD_STS"] == "D")
                    {
                        <div class="col-md-offset-1 col-md-7">
                            <input type="submit" value="下架獎品" class="btn btn-default" onclick="doDelete();" />
                            <input type="submit" value="刪除獎品" class="btn btn-default" onclick="doRealDelete();" />
                        </div>
                        
                    }
           
                <div class="col-md-offset-1 col-md-3">
                    <a class="btn btn-default" href='@Url.Action("AwatQ02", "Awat")'>
                        返回
                    </a>
                </div>
            </div>
        </div>
    </div>
   


</form>

@section Scripts
{
    <script type="text/javascript">

    var url = "@(Html.Raw(@Url.Action("_Awat02FormData", "Awat", new { AWARD_NO = @Request["hidAWARD_NO"], MODE = cMODE, unProduct = cunProduct })))";
        $('#showView').load(url);

    function doModify() {
        try {
            if ($("#ParamAWARD_NO").val() == "") {
                alert('修改程序不正確');
                return;
            }
            if ($("#file").val() != "") {
                if (false == FileUpload_click()) {
                    return;
                }
            }
        }
        catch (err) {
        }
        document.contentForm.enctype = "multipart/form-data";
        document.contentForm.action = "AwatModify02";
        document.contentForm.submit();
    }

    function doDelete() {
        if ($("#ParamAWARD_NO").val() == "") {
            alert('刪除程序不正確');
            return;
        }

        document.contentForm.action = "AwatDelete02";
        document.contentForm.submit();
    }

    function doRealDelete() {
        if ($("#ParamAWARD_NO").val() == "") {
            alert('刪除程序不正確');
            return;
        }

        document.contentForm.action = "AwatDelete";
        document.contentForm.submit();
    }

    

    </script>
}
