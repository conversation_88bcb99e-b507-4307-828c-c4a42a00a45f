﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDI12SearchViewModel
    {

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string WhereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string WhereCLASS_NO { get; set; }


        /// <summary>
        /// 只顯示某學年
        /// </summary>
        public byte? WhereSYEAR { get; set; }



        public string WhereSCHOOL_NO { get; set; }


        public string WhereUSER_NO { get; set; }

        public string WhereSTAGE_ID { get; set; }



        public string WhereSTATUS { get; set; }


        [DisplayName("標題目稱")]
        public string WhereSearch { get; set; }

        [DisplayName("學號/姓名")]
        public string WhereKeyword { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }
    }
}
