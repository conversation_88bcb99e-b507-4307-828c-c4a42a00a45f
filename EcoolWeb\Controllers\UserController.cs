﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using com.ecool.service;
using Dapper;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using log4net;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 我的
    /// </summary>
    [SessionExpire]
    public class UserController : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = " User";

        private static string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;
        private static log4net.ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public ActionResult Index()
        {
            logger.Info(" Url=" + HttpContext.Request.Url.ToString());
            this.Shared();
           // user = UserProfileHelper.Get();
            logger.Info("UserController user.USER_NO=" + user?.USER_NO ?? "");
            logger.Info("UserController user.SCHOOL_NO=" + user?.SCHOOL_NO ?? "");
            if (user == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.SessionTimeOutError, "Error");
            }

            UserIndexViewModel model = new UserIndexViewModel();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            model.CASH = user.CASH;
            model.Run = (Convert.ToDouble(db.ADDT25.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO)
                .Select(a => a.RUN_TOTAL_METER).FirstOrDefault() ?? 0) / 1000.00).ToString("#,0.000");
            IQueryable<ADDT06> ADDT06List = db.ADDT06.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.APPLY_STATUS == "0");

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) || PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_CheckPendingDetail", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                ADDT06List = ADDT06List.Where(a => a.CLASS_NO == user.TEACH_CLASS_NO);
            }

            ViewBag.ADDT06Cnt = ADDT06List.Count();
            //線上投稿資料
            model.arrWRITING_NO = (from a01 in db.ADDT01
                                   where a01.SCHOOL_NO == user.SCHOOL_NO && a01.USER_NO == user.USER_NO
                                    && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable
                                   orderby a01.WRITING_NO
                                   select a01.WRITING_NO).ToList();
            //線上投稿待批閱數量
            IQueryable<ADDT01> ADDT01List = db.ADDT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO &&
            (a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Draft || a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.TurnVerify));
            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) || PermissionService.GetPermission_Use_YN("ADDI01", "Verify", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                ADDT01List = ADDT01List.Where(a => a.VERIFIER == user.USER_NO);
            }

            ViewBag.ADDT01Qty = ADDT01List.Count();
            //線上藝廊待批閱數量

            string sSQL = @"Select a.*, '" + UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI34/QRCODEUrl?WhereART_GALLERY_NO=" + "'+ a.ART_GALLERY_NO as QRCODEGARY , '" + UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI34/QRCODEUrl?PHOTO_USER_NO=" + "' + a.USER_NO +" + $@"'&PHOTO_SCHOOL_NO='+ a.SCHOOL_NO as QRCODEGARYPHOTO ";
            sSQL = sSQL + @", WorkCount=(SELECT COUNT(*) FROM ADDT22 b (NOLOCK) Where B.ART_GALLERY_NO =a.ART_GALLERY_NO and b.PHOTO_STATUS=@PHOTO_STATUS)
                            from ADDT21 a (nolock)
                            where a.SCHOOL_NO =@SCHOOL_NO and (SELECT COUNT(*) FROM ADDT22 b (NOLOCK) Where B.ART_GALLERY_NO =a.ART_GALLERY_NO and b.PHOTO_STATUS=@PHOTO_STATUS)>0 and STATUS=1";

            IQueryable<ADDT21> ADDT21List = db.ADDT21.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.STATUS == ADDT21.STATUSVal.Verification
           );
            string str = PermissionService.GetPermission_Use_YN("ZZZI34", "Verify", user.SCHOOL_NO, user.USER_NO);
            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) || PermissionService.GetPermission_Use_YN("ZZZI34", "Verify", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                //ADDT21List = ADDT21List.Where(a => a.VERIFIER == user.USER_NO);
                sSQL = sSQL + " and (a.USER_NO = @USER_NO  or isnull(a.VERIFIER,'')=@USER_NO) ";

            }
            else if (user.USER_TYPE == "T" && string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && str != "Y")
            {
                sSQL = sSQL + " and a.USER_NO = @USER_NO ";
            }

            var temp = db.Database.Connection.Query<ZZZI34IndexListDataViewModel>(sSQL
             , new
             {

                 USER_NO = user.USER_NO,
                 SCHOOL_NO = user.SCHOOL_NO,
                 Verification = ADDT21.STATUSVal.Verification,
                 PHOTO_STATUS = ADDT22.STATUSVal.OK,
                 Pass = ADDT21.STATUSVal.Pass,



             });
            ViewBag.ADDT21Qty = temp.Count();
            ViewBag.WFT05 = db.WFT05.Count();
            //閱讀認證獎狀待頒發
            IQueryable<ADDT0809> TempList = (from a09_his in db.ADDT09_HIS
                                             join a08 in db.ADDT08
                                                   on new { a09_his.LEVEL_ID, a09_his.SCHOOL_NO }
                                               equals new { a08.LEVEL_ID, a08.SCHOOL_NO }
                                             where a09_his.STATUS == 0 && a09_his.SCHOOL_NO == user.SCHOOL_NO
                                             select new ADDT0809
                                             {
                                                 UPNO = a09_his.UPNO.ToString(),
                                                 SYEAR = a09_his.SYEAR.ToString(),
                                                 SEMESTER = a09_his.SEMESTER.ToString(),
                                                 CLASS_NO = a09_his.CLASS_NO,
                                                 SEAT_NO = a09_his.SEAT_NO,
                                                 USERNAME = a09_his.NAME,
                                                 SNAME = a09_his.SNAME,
                                                 BOOK_QTY = a09_his.BOOK_QTY,
                                                 LEVEL_DESC = a08.LEVEL_DESC,
                                                 UP_DATE = a09_his.UP_DATE,
                                                 STATUS = a09_his.STATUS.ToString(),
                                                 SCHOOL_NO = a09_his.SCHOOL_NO,
                                                 USER_NO = a09_his.USER_NO,
                                                 DP_PERSON = a09_his.DP_PERSON,
                                                 DP_DATE = a09_his.DP_DATE
                                             });
            ViewBag.ADDT0809 = TempList.Count();
            IQueryable<VAWA004> VAWA004List = db.VAWA004;
            int TRANS_STATUS = Convert.ToInt32(VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString());
            //人員
            if (SchoolNO != "ALL")
            {
                VAWA004List = VAWA004List.Where(a => a.SCHOOL_NO == SchoolNO);

            }
            VAWA004List = VAWA004List.Where(a => a.TRANS_STATUS == TRANS_STATUS);
            ViewBag.TRA04 = VAWA004List.Count();

            HRMT01 hRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();
            if (hRMT01 != null)
            {
                model.IDNO = hRMT01.IDNO;

                if (!string.IsNullOrWhiteSpace(hRMT01.PHOTO))
                {
                    SECI01Service Service = new SECI01Service();
                    model.PlayerUrl = Service.GetDirectorySysMyPhotoPath(hRMT01.SCHOOL_NO, hRMT01.USER_NO, hRMT01.PHOTO);
                }
            }

            if (string.IsNullOrWhiteSpace(model.PlayerUrl))
            {
                model.PlayerUrl = user.PlayerUrl;
            }
            //行政人員不要看到group2
            int NteacherCount = 0;
            int YteacheCount = 0;
            NteacherCount = db.HRMT25.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.ROLE_ID == "8").Count();
            YteacheCount = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.CLASS_NO != null).Count();
            int ADMINteacherCount = 0;
            ADMINteacherCount = db.HRMT25.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && (x.ROLE_ID == "1" || x.ROLE_ID == "0" || x.ROLE_ID == "2" || x.ROLE_ID == "4")).Count();
            if (YteacheCount > 0 || ADMINteacherCount > 0)
            {

                ViewBag.isShow = "Y";

            }
            else {

                ViewBag.isShow = "N";
            }
          

            return View(model);
        }

        public ActionResult CooCIndex()
        {
            logger.Info(" Url=" + HttpContext.Request.Url.ToString());
            this.Shared();
            user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            logger.Info("UserController user.USER_NO=" + user?.USER_NO ?? "");

            if (user == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.SessionTimeOutError, "Error");
            }

            UserIndexViewModel model = new UserIndexViewModel();

            model.CASH = user.CASH;
            model.Run = (Convert.ToDouble(db.ADDT25.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO)
                .Select(a => a.RUN_TOTAL_METER).FirstOrDefault() ?? 0) / 1000.00).ToString("#,0.000");
            IQueryable<ADDT06> ADDT06List = db.ADDT06.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.APPLY_STATUS == "0");

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) || PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_CheckPendingDetail", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                ADDT06List = ADDT06List.Where(a => a.CLASS_NO == user.TEACH_CLASS_NO);
            }

            ViewBag.ADDT06Cnt = ADDT06List.Count();
            //線上投稿資料
            model.arrWRITING_NO = (from a01 in db.ADDT01
                                   where a01.SCHOOL_NO == user.SCHOOL_NO && a01.USER_NO == user.USER_NO
                                    && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable
                                   orderby a01.WRITING_NO
                                   select a01.WRITING_NO).ToList();
            //線上投稿待批閱數量
            IQueryable<ADDT01> ADDT01List = db.ADDT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO &&
            (a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Draft || a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.TurnVerify));
            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) || PermissionService.GetPermission_Use_YN("ADDI01", "Verify", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                ADDT01List = ADDT01List.Where(a => a.VERIFIER == user.USER_NO);
            }

            ViewBag.ADDT01Qty = ADDT01List.Count();
            //線上藝廊待批閱數量
            IQueryable<ADDT21> ADDT21List = db.ADDT21.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.STATUS == ADDT21.STATUSVal.Verification
            );

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) || PermissionService.GetPermission_Use_YN("ZZZI34", "Verify", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                ADDT21List = ADDT21List.Where(a => a.VERIFIER == user.USER_NO);
            }

            ViewBag.ADDT21Qty = ADDT21List.Count();
            IQueryable<VAWA004> VAWA004List = db.VAWA004;
            int TRANS_STATUS = Convert.ToInt32(VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString());
            //人員
            if (SchoolNO != "ALL")
            {
                VAWA004List = VAWA004List.Where(a => a.SCHOOL_NO == SchoolNO);
               
            }
            VAWA004List = VAWA004List.Where(a => a.TRANS_STATUS == TRANS_STATUS);
            ViewBag.TRA04 = VAWA004List.Count();


            ViewBag.WFT05 = db.WFT05.Count();
            //閱讀認證獎狀待頒發
            IQueryable<ADDT0809> TempList = (from a09_his in db.ADDT09_HIS
                                             join a08 in db.ADDT08
                                                   on new { a09_his.LEVEL_ID, a09_his.SCHOOL_NO }
                                               equals new { a08.LEVEL_ID, a08.SCHOOL_NO }
                                             where a09_his.STATUS == 0 && a09_his.SCHOOL_NO == user.SCHOOL_NO
                                             select new ADDT0809
                                             {
                                                 UPNO = a09_his.UPNO.ToString(),
                                                 SYEAR = a09_his.SYEAR.ToString(),
                                                 SEMESTER = a09_his.SEMESTER.ToString(),
                                                 CLASS_NO = a09_his.CLASS_NO,
                                                 SEAT_NO = a09_his.SEAT_NO,
                                                 USERNAME = a09_his.NAME,
                                                 SNAME = a09_his.SNAME,
                                                 BOOK_QTY = a09_his.BOOK_QTY,
                                                 LEVEL_DESC = a08.LEVEL_DESC,
                                                 UP_DATE = a09_his.UP_DATE,
                                                 STATUS = a09_his.STATUS.ToString(),
                                                 SCHOOL_NO = a09_his.SCHOOL_NO,
                                                 USER_NO = a09_his.USER_NO,
                                                 DP_PERSON = a09_his.DP_PERSON,
                                                 DP_DATE = a09_his.DP_DATE
                                             });
            ViewBag.ADDT0809 = TempList.Count();
            HRMT01 hRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();
            if (hRMT01 != null)
            {
                model.IDNO = hRMT01.IDNO;

                if (!string.IsNullOrWhiteSpace(hRMT01.PHOTO))
                {
                    SECI01Service Service = new SECI01Service();
                    model.PlayerUrl = Service.GetDirectorySysMyPhotoPath(hRMT01.SCHOOL_NO, hRMT01.USER_NO, hRMT01.PHOTO);
                }
            }

            if (string.IsNullOrWhiteSpace(model.PlayerUrl))
            {
                model.PlayerUrl = user.PlayerUrl;
            }

            return View(model);
        }

        public ActionResult ADDI01()
        {
            string Path = Url.Action("VerifyList", "ADDI01", new { whereWritingStatus = "0,2" });
            return Redirect(Path);
        }

        public ActionResult TaskList()
        {
            this.Shared();
            return View();
        }

        public ActionResult AppAtm()
        {
            this.Shared();
            string Path = Url.Action("Index", "BarcCodeMyCash", new { WhereIsColorboxForUser = "True", WhereKeyword = user.USER_NO });
            return Redirect(Path);
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}