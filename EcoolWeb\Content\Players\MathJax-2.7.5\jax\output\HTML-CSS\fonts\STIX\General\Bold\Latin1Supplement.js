/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Latin1Supplement.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{160:[0,0,250,0,0],161:[501,203,333,82,252],162:[588,140,500,53,458],163:[684,16,500,21,477],164:[542,10,500,-26,526],165:[676,0,500,-64,547],166:[691,19,220,66,154],167:[691,132,500,57,443],168:[666,-537,333,-2,337],169:[691,19,747,26,721],170:[688,-397,300,-1,301],171:[415,-36,500,23,473],172:[399,-108,750,65,685],173:[287,-171,333,44,287],174:[691,19,747,26,721],175:[637,-565,333,1,331],176:[688,-402,400,57,343],177:[518,151,770,65,685],178:[688,-275,300,0,300],179:[688,-268,300,3,297],180:[713,-528,333,86,324],181:[461,206,556,33,536],182:[676,186,639,60,579],183:[417,-248,250,41,210],184:[0,218,333,68,294],185:[688,-275,300,28,273],186:[688,-397,330,18,312],187:[415,-36,500,27,477],188:[688,12,750,28,743],189:[688,12,750,-7,775],190:[688,12,750,23,733],191:[501,201,500,55,443],192:[963,0,722,9,689],193:[963,0,722,9,689],194:[954,0,722,9,689],195:[924,0,722,9,689],196:[916,0,722,9,689],197:[1000,0,722,9,689],198:[676,0,1000,4,951],199:[691,218,722,49,687],200:[963,0,667,16,641],201:[963,0,667,16,641],202:[954,0,667,16,641],203:[916,0,667,16,641],204:[963,0,389,20,370],205:[963,0,389,20,370],206:[954,0,389,20,370],207:[916,0,389,20,370],208:[676,0,722,6,690],209:[924,18,722,16,701],210:[963,19,778,35,743],211:[963,19,778,35,743],212:[954,19,778,35,743],213:[924,19,778,35,743],214:[916,19,778,35,743],215:[538,33,702,66,636],216:[737,74,778,35,743],217:[963,19,722,16,701],218:[963,19,722,16,701],219:[954,19,722,16,701],220:[916,19,722,16,701],221:[963,0,722,15,699],222:[676,0,611,16,600],223:[691,12,556,19,517],224:[713,14,500,25,488],225:[713,14,500,25,488],226:[704,14,500,25,488],227:[674,14,500,25,488],228:[666,14,500,25,488],229:[752,14,500,25,488],230:[473,14,722,33,694],231:[473,218,444,25,430],232:[713,14,444,25,427],233:[713,14,444,25,427],234:[704,14,444,25,427],235:[666,14,444,25,427],236:[713,0,278,14,257],237:[713,0,278,15,258],238:[704,0,278,-29,308],239:[666,0,278,-29,310],240:[691,14,500,25,476],241:[674,0,556,21,539],242:[713,14,500,25,476],243:[713,14,500,25,476],244:[704,14,500,25,476],245:[674,14,500,25,476],246:[666,14,500,25,476],247:[537,31,570,33,537],248:[549,92,500,25,476],249:[713,14,556,16,538],250:[713,14,556,16,538],251:[704,14,556,16,538],252:[666,14,556,16,538],253:[713,205,500,16,482],254:[676,205,556,19,524],255:[666,205,500,16,482]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/Latin1Supplement.js");
