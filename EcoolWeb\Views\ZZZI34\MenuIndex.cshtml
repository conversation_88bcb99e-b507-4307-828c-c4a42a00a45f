﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ValidationSummary(true, "", new { @class = "text-danger" })
@{
    Html.RenderAction("_ArtGalleryMenu", new { NowAction = "MenuIndex" });
}
<div class="ribbon ribbon-online-gallery-green">
    <strong class="ribbon-title">
        線上藝廊申請模式
    </strong>
</div>
<div class="ribbon-content">    
        <div class="d-flex flex-wrap mt-3">
            @{
                <div class="col col-lg-4">
                    @*@Html.ActionLink("zip上傳(只能上傳影像批次檔)", "Index2", new { mode = "批次線上藝廊" }, new { @class = "btn btn-default  btn-block", @role = "button" })*@
                    <a class="btn btn-default  btn-block" href="../ZZZI34/Index2?mode=批次線上藝廊" role="button">
                        多檔案壓縮上傳<br />
                              (ZIP上傳)

                    </a>
                </div>
                <div class="col col-lg-4 d-flex">
                    @*@Html.ActionLink("非zip上傳", "Edit", new { FirstPage = "true", SouBre_NO = "ZZZI34" }, new { @class = "btn btn-default  btn-block", @role = "button" })*@
                    <a class="btn btn-default flex-grow-1 d-flex justify-content-center align-items-center mr-0" href="../ZZZI34/Edit?FirstPage=true&SouBre_NO=ZZZI34" role="button">
                        非zip上傳<br />
                    </a>
                </div>
            }
    </div>
</div>
<div class="mx-3 px-5 py-3">
    說明：<br />
    1.	Zip上傳是將影像檔案打包壓縮上傳， 非zip上傳，則是一個個檔案慢慢上傳。<br />
    2.	電腦版才有zip上傳；APP只能用非Zip上傳。<br />
</div>