﻿

@using com.ecool.service
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    //bool IsTeacherEdit = System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck((string)ViewBag.BRE_NO, "TeacherEdit");

}



<div class="form-group cScreen ">
    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin && ViewBag.ISTASKLIST != "True")
        {

            <a role="button" href='@Url.Action("Index", "CERI01", new { FirstPage = "true", SouBre_NO = "CERI01" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CERI01" ? "active" : "")">
                護照名稱設定
            </a>
            <a role="button" href='@Url.Action("Index", "CERI02", new { FirstPage = "true", SouBre_NO = "CERI02" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CERI02" ? "active" : "")">
                護照明細設定
            </a>

            <a role="button" href='@Url.Action("Index", "CERI04", new { FirstPage = "true", SouBre_NO = "CERI04" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CERI04" ? "active" : "")">
                我要登記
            </a>

            <a role="button" href='@Url.Action("Index", "CERI05", new { FirstPage = "true", SouBre_NO = "CERI05" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CERI05" ? "active" : "")">
                護照補登
            </a>

            <a role="button" href='@Url.Action("Index", "CER001", new { FirstPage = "true", SouBre_NO = "CER001" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CER001" ? "active" : "")">
                護照通過情形(在校生)
            </a>

            <a role="button" href='@Url.Action("GraduateIndex", "CERI013", new { FirstPage = "true", SouBre_NO = "CER001" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "GraduateIndex" ? "active" : "")">
                護照通過情形(畢業生)
            </a>
            <br />
            <a role="button" href='@Url.Action("IndexTecher", "CER002", new { FirstPage = "true", SouBre_NO = "CERI03" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CER002" ? "active" : "")">
                查詢學生護照
            </a>

            <a role="button" href='@Url.Action("IndexGradeTecher", "CER002", new { FirstPage = "true", SouBre_NO = "CER0021" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CERI05" ? "active" : "")">
                查詢學生護照(畢業生)
            </a>
        }
    }
    @*@if (user != null)
        {

            if (user.USER_TYPE == UserType.Student)
            {
                <a role="button" href='@Url.Action("Index", "CER002", new { FirstPage = "true", SouBre_NO = "CER002" })' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "CER002" ? "active" : "")">
                  我的護照
                </a>

            }
        }*@
</div>