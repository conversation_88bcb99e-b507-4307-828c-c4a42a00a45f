/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Dingbats.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{9986:[612,-82,961,35,905],9993:[555,-138,690,34,638],10003:[707,12,755,34,704],10016:[592,87,767,53,714],10026:[613,106,789,35,733],10038:[616,108,695,35,642],10045:[612,108,682,35,626],10098:[719,213,488,188,466],10099:[719,213,488,22,300],10112:[705,14,788,35,733],10113:[705,14,788,35,733],10114:[705,14,788,35,733],10115:[705,14,788,35,733],10116:[705,14,788,35,733],10117:[705,14,788,35,733],10118:[705,14,788,35,733],10119:[705,14,788,35,733],10120:[705,14,788,35,733],10121:[705,14,788,35,733],10122:[705,14,788,35,733],10123:[705,14,788,35,733],10124:[705,14,788,35,733],10125:[705,14,788,35,733],10126:[705,14,788,35,733],10127:[705,14,788,35,733],10128:[705,14,788,35,733],10129:[705,14,788,35,733],10130:[705,14,788,35,733],10131:[705,14,788,35,733],10139:[433,-70,918,35,861]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/Dingbats.js");
