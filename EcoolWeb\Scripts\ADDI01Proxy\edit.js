// Modern jQuery code for ADDI01Proxy Edit page
$(document).ready(function() {
    // Colorbox 圖片畫廊模組
    const imageGallery = {
        init: function() {
            this.initColorbox();
        },

        initColorbox: function() {
            $(".img-responsive").colorbox({ 
                opacity: 0.82, 
                width: "90%" 
            });
        }
    };

    // 表單操作模組
    const formHandler = {
        init: function() {
            window.AddItem = this.addItem.bind(this);
            window.Save = this.save.bind(this);
            this.bindEvents();
        },

        bindEvents: function() {
            // 數字輸入驗證
            $('#ADDNUM').on('change', this.validateNumber.bind(this));
        },

        validateNumber: function(e) {
            const value = e.target.value;
            if (isNaN(value) || value <= 0) {
                alert('請填數字!');
                e.target.value = '';
            }
        },

        addItem: function() {
            const form = document.form1;
            form.DATA_TYPE.value = 'AddItem';
            form.action = window.ADDI01PROXY_URLS.editAction;
            form.submit();
        },

        save: function(value) {
            let msg = '';
            const form = document.form1;
            const numItems = parseInt(form.Num.value);

            // 執行表單驗證
            msg = this.validateForm(numItems);

            // 檢查文件上傳（如果需要）
            if ($('#VerifyUseYN').val() === 'Y') {
                msg += this.validateFileUploads(numItems);
            }

            // 決定是否繼續提交
            let shouldContinue = false;
            if (msg !== '') {
                shouldContinue = confirm(msg + '\n 請問是否繼續');
            } else {
                shouldContinue = true;
            }

            if (shouldContinue) {
                form.DATA_TYPE.value = value;
                form.action = window.ADDI01PROXY_URLS.editAction;
                form.submit();
            }
        }
    };

    // 表單驗證模組
    const formValidator = {
        validateForm: function(numItems) {
            let msg = '';

            for (let i = 0; i < numItems; i++) {
                // 跳過已刪除的項目
                if ($("#Details_List_Del_" + i).is(':checked')) {
                    continue;
                }

                // 檢查標題
                if ($('#Details_List_' + i + '__SUBECT').val() === '') {
                    msg += '第' + (i + 1) + '筆未填寫標題\n';
                }

                // 檢查內容
                if ($('#Details_List_' + i + '__ARTICLE').val() === '') {
                    msg += '第' + (i + 1) + '筆未填寫內容\n';
                }

                // 檢查給點數
                const pointElement = $('#Details_List_' + i + '__GIVE_POINT');
                const pointValue = parseInt(pointElement.val());
                if (pointValue < 0 || pointValue > 50) {
                    msg += '第' + (i + 1) + '給點數須介於0~50\n';
                }
            }

            return msg;
        },

        validateFileUploads: function(numItems) {
            let msg = '';

            for (let i = 0; i < numItems; i++) {
                // 跳過已刪除的項目
                if ($("#Details_List_Del_" + i).prop('checked')) {
                    continue;
                }

                // 檢查文件上傳
                if ($('#files_' + i).val() === '') {
                    msg += '第' + (i + 1) + '筆未上傳檔案\n';
                }
            }

            return msg;
        }
    };

    // 將驗證方法添加到表單處理器
    formHandler.validateForm = formValidator.validateForm;
    formHandler.validateFileUploads = formValidator.validateFileUploads;

    // 輸入驗證模組
    const inputValidator = {
        init: function() {
            this.bindValidationEvents();
        },

        bindValidationEvents: function() {
            // 為所有給點數輸入框添加驗證
            $(document).on('change', 'input[name*="GIVE_POINT"]', this.validateGivePoint.bind(this));
            
            // 為所有必填欄位添加驗證
            $(document).on('blur', 'input[name*="SUBECT"], textarea[name*="ARTICLE"]', this.validateRequired.bind(this));
        },

        validateGivePoint: function(e) {
            const value = parseInt(e.target.value);
            const $element = $(e.target);
            
            if (isNaN(value) || value < -25 || value > 50) {
                $element.addClass('is-invalid');
                this.showValidationMessage($element, '給點數須介於-25~50之間');
            } else {
                $element.removeClass('is-invalid');
                this.hideValidationMessage($element);
            }
        },

        validateRequired: function(e) {
            const $element = $(e.target);
            const value = $element.val().trim();
            
            if (value === '') {
                $element.addClass('is-invalid');
                this.showValidationMessage($element, '此欄位為必填');
            } else {
                $element.removeClass('is-invalid');
                this.hideValidationMessage($element);
            }
        },

        showValidationMessage: function($element, message) {
            const $parent = $element.closest('td');
            let $errorMsg = $parent.find('.validation-error');
            
            if ($errorMsg.length === 0) {
                $errorMsg = $('<div class="validation-error text-danger small"></div>');
                $parent.append($errorMsg);
            }
            
            $errorMsg.text(message);
        },

        hideValidationMessage: function($element) {
            const $parent = $element.closest('td');
            $parent.find('.validation-error').remove();
        }
    };

    // 用戶選擇模組
    const userSelector = {
        init: function() {
            this.bindUserSelectEvents();
        },

        bindUserSelectEvents: function() {
            // 監聽用戶選擇變更
            $(document).on('change', 'select[name*="USER_NO"]', this.handleUserChange.bind(this));
        },

        handleUserChange: function(e) {
            const $select = $(e.target);
            const selectedText = $select.find('option:selected').text();
            
            // 可以在這裡添加用戶選擇後的邏輯
            console.log('Selected user:', selectedText);
        }
    };

    // 初始化所有模組
    imageGallery.init();
    formHandler.init();
    inputValidator.init();
    userSelector.init();

    // 設置全局 URL 配置（需要在 CSHTML 中定義）
    window.ADDI01PROXY_URLS = window.ADDI01PROXY_URLS || {};
});
