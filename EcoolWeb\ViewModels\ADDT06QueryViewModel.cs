﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public enum ADDT06ListType
    {
        推薦清單文,
        推薦清單圖
    }
    public class ADDT06QueryViewModel
    {
         /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }

        public ADDT06ListType ListType { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ADDT06> ADDT06List;

        public ADDT06QueryViewModel()
        {
            Page = 0;
            OrdercColumn = "CRE_DATE";
        }
    }
}