﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI33SearchViewModel
    {


        public string whereSOU_KEY { get; set; }
        public string whereSOU_Person { get; set; }
        public string whereQUESTIONNAIRE_ID { get; set; }

        [DisplayName("活動名稱")]
        public string whereSearch { get; set; }

        public string whereIndex { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        public string BackAction { get; set; }

        public string BackController { get; set; }
    }
}