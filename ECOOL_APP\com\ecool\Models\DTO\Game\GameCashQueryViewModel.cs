﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameCashQueryViewModel
    {
        public string GAME_NO { get; set; }
        public string TEMP_USER_ID { get; set; }

        public string LEVEL_IMG_PATH { get; set; }

        public string E_MAIL { get; set; }

        /// <summary>
        /// 使用者資訊
        /// </summary>
        [DisplayName("使用者資訊")]
        [Required]
        public string GameUserID { get; set; }

        /// <summary>
        /// 活動資訊
        /// </summary>
        public ADDT26 GameInfo { get; set; }

        /// <summary>
        /// 個人資訊
        /// </summary>
        public ADDT27 User { get; set; }

        /// <summary>
        /// 目前點數
        /// </summary>
        public Nullable<int> CASH_AVAILABLE { get; set; }

        /// <summary>
        /// 點數歷史清單
        /// </summary>
        public List<GameCashDetailsViewModel> CashDetails { get; set; }

        /// <summary>
        /// 中獎內容
        /// </summary>
        public List<GameLotteryListViewModel> LotteryDetails { get; set; }

        /// <summary>
        /// 過關資訊
        /// </summary>
        public List<GameMeLevelViewModel> MeLevelDetails { get; set; }

        /// <summary>
        /// 兌換獎品狀態
        /// </summary>
        public List<GamePrizeLevelViewModel> PrizeLevel { get; set; }

        /// <summary>
        /// 作答歷史記錄
        /// </summary>
        public List<ADDT26_MAns> MeMAns { get; set; }

        /// <summary>
        /// 本次答題資訊
        /// </summary>
        public List<GameMeQALevelViewModel> MeQALevel { get; set; }

        /// <summary>
        /// 鼓勵語
        /// </summary>
        public string REWARD_DESC { get; set; }

        /// <summary>
        /// 已完成關卡
        /// </summary>
        public int LevelPassCount { get; set; }

        /// <summary>
        /// 未完成關卡
        /// </summary>
        public int UnLevelPassCount { get; set; }

        /// <summary>
        /// 查詢參數
        /// </summary>
        public BatchCashIntoSearchViewModel CashSearch { get; set; }
        /// 上頁Page來源
        /// </summary>
        public SourcePage FromSourcePage { get; set; }

        public enum SourcePage
        {
            /// <summary>
            /// 查詢機
            /// </summary>
            CashQuery = 0,

            /// <summary>
            /// 其他頁面，查詢此人現況
            /// </summary>
            QueryUserGameData = 1
        }
    }
}