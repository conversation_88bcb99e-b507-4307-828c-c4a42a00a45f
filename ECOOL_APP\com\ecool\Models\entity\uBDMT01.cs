﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{

        public class uBDMT01
        {

            ///Summary
            ///
            ///Summary 
            public string SCHOOL_NO { get; set; }

            ///Summary
            ///
            ///Summary 
            public string SCHOOL_NAME { get; set; }

            ///Summary
            ///
            ///Summary 
            public string SHORT_NAME { get; set; }

            ///Summary
            ///
            ///Summary 
            public string CITY { get; set; }

            ///Summary
            ///
            ///Summary 
            public string ADDRESS { get; set; }

            ///Summary
            ///
            ///Summary 
            public string TEL { get; set; }

            ///Summary
            ///
            ///Summary 
            public string FAX { get; set; }

            ///Summary
            ///
            ///Summary 
            public string LOGO_FILE { get; set; }

            ///Summary
            ///
            ///Summary 
            public string E_MAIL { get; set; }

            ///Summary
            ///
            ///Summary 
            public string CHG_PERSON { get; set; }

            ///Summary
            ///
            ///Summary 
            public DateTime CHG_DATE { get; set; }

            ///Summary
            ///
            ///Summary 
            public string CRE_PERSON { get; set; }

            ///Summary
            ///
            ///Summary 
            public DateTime CRE_DATE { get; set; }

            ///Summary
            ///
            ///Summary 
            public string SYS_ID { get; set; }

        }

}
