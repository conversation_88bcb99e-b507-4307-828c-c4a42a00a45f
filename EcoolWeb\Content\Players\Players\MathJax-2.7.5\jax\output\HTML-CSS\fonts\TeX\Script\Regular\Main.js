/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Script/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Script={directory:"Script/Regular",family:"MathJax_Script",testString:"MATHJAX SCRIPT",skew:{65:0.389,66:0.194,67:0.278,68:0.111,69:0.139,70:0.222,71:0.25,72:0.333,73:0.333,74:0.417,75:0.361,76:0.306,77:0.444,78:0.389,79:0.167,80:0.222,81:0.278,82:0.194,83:0.333,84:0.222,85:0.25,86:0.222,87:0.25,88:0.278,89:0.194,90:0.306},Ranges:[[0,127,"BasicLatin"],[128,65535,"Other"]]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Script"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Regular/Main.js"]);
