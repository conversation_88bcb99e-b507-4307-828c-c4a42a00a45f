﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.LogicCenter.ADDI11
{
    /// <summary>
    /// 基本儲存格
    /// </summary>
    public class Storage
    {
        public Storage(CellLetter cell, int row)
        {
            this.Cell = cell;
            this.Row = row;

        }
        public int Row { get; set; }
        public CellLetter Cell { get; set; }
        public bool IsFormula { get; set; }

        public string Value { get; set; }
    }

    /// <summary>
    /// 串鏈型客製化儲存格
    /// </summary>
    public class LinkedStorage : Storage
    {
        public LinkedStorage(CellLetter cell, int row) : base(cell, row) { }

        public StorageGridType StorageGridType
        {
            get
            {
                if (Cell == CellLetter.X)
                    return StorageGridType.總計;
                if (Row == 18)
                    return StorageGridType.總次數;

                return Row % 2 == 0 ? StorageGridType.日期 : StorageGridType.是否達標;
            }
        }

        public DateTime Date { get; set; }
        public LinkedStorage Previous { get; set; }
        public LinkedStorage Next { get; set; }
    }


}
