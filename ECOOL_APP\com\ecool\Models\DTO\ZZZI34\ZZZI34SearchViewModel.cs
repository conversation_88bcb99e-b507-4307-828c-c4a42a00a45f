﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34SearchViewModel
    {
        public string WherePHOTO_NO { get; set; }
        public string WherePHOTO_USER_NO { get; set; }
        public string WherePHOTO_SCHOOL_NO { get; set; }
        public string TEMP_BATCH_KEY { get; set; }

        /// <summary>
        /// 人數
        /// </summary>
        [DisplayName("筆數")]
        public string NumType { get; set; }

        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string WhereKeyword { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string WhereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某學年
        /// </summary>
        public byte? WhereSYEAR { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        public string WhereUSER_NO { get; set; }

        public string WhereART_GALLERY_NO { get; set; }

        public bool? WhereMyWork { get; set; }

        public string WhereART_GALLERY_TYPE { get; set; }

        public string WhereSTATUS { get; set; }

        [DisplayName("藝廊標題")]
        public string WhereSearch { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }
    }
}