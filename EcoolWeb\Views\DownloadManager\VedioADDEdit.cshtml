﻿@model ECOOL_APP.EF.DLMT01
@using ECOOL_APP;
@using ECOOL_APP.EF;
@using EcoolWeb.Models;
<script src="~/Content/ckeditorAdmin/ckeditor.js"></script>
@using (Html.BeginForm("EditSave", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", @class = "form-horizontal", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.DL_ID)
    @Html.HiddenFor(m => m.STATUS)
    @Html.HiddenFor(m => m.CHG_DATE)
    <input id="FileType" name="FileType" type="hidden" value="V">
    <section id="feature" class="feature-section feature-section-2">

        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h3 style="color: #6ba1ab;">
                        <strong>@ViewBag.Panel_Title</strong>
                    </h3>
                </div>
                <!-- /.col-lg-12 -->
            </div>
            <hr style="margin: 19px 0px;">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.Label("順序", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.Orderby, new { htmlAttributes = new { @class = "form-control input-md" } })
                        @Html.ValidationMessageFor(m => m.Orderby, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("影片分類", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.DL_GROUP, new { htmlAttributes = new { @class = "form-control input-md" } })
                        @Html.ValidationMessageFor(m => m.DL_GROUP, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.Label("影片名稱", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.EditorFor(m => m.DL_SUBJECT, new { htmlAttributes = new { @class = "form-control input-md" } })
                        @Html.ValidationMessageFor(m => m.DL_SUBJECT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("影片說明", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.TextAreaFor(m => m.DL_MEMO, 6, 200, new { @class = "form-control", @placeholder = "回答內容" })
                        @Html.ValidationMessageFor(m => m.DL_MEMO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("更新日期", htmlAttributes: new { @class = "col-md-4 control-label" })
                    <div class="col-md-8">
                        @Html.DisplayFor(m => m.CHG_DATE, new { htmlAttributes = new { @class = "form-control input-md", @type = "text", @placeholder = "格式yyyy/MM/dd" } })
                    </div>
                </div>

                <div class="form-group">

                    @Html.Label("網址", htmlAttributes: new { @class = "col-md-4 control-label" })

                    <div class="col-md-8">

                        <div class="input-group">
                            @Html.EditorFor(model => model.YOUTUBE_URL, new { htmlAttributes = new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.YOUTUBE_URL) } })
                            <span class="input-group-btn">
                                <button class="btn btn-info" type="button" id="CheckYoutube">檢查網址</button>
                            </span>
                        </div><!-- /input-group -->

                        @Html.ValidationMessageFor(model => model.YOUTUBE_URL, "", new { @class = "text-danger" })
                    </div>

                </div>
                <div class="form-group">
                    <label class="col-md-4 control-label" for="上傳">上傳</label>

                    @if (Model != null)
                    {
                        if (!string.IsNullOrWhiteSpace(Model.DL_ID))
                        {
                            string TPath = "";
                            string TargetPath = @"~\DownloadManager";// + @"\" + entity.DL_ID + @"\" + DirectoryPath + @"\" + NEWS_ID;
                            TargetPath = TargetPath + @"\DLM\" + Model.DL_ID + @"\" + Model.FILENAME;
                            TPath = Uri.EscapeUriString(UrlCustomHelper.Url_Content(TargetPath));
                            <div class="col-md-4">
                                <a href="@TPath" target="_blank">@Model.FILENAME</a>
                            </div>

                        }
                    }
                    <div class="col-md-4">
                        <input name="files" class="input-file btn btn-sm" type="file" multiple>
                        @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                    </div>
                </div>
                <span class="pull-right">
                    <a style="margin: -15px 0px;" role="button" class="btn btn-success btn-sm" onclick="onSave()">儲存</a>
                    @if (Model != null)
                    {
                        if (!string.IsNullOrWhiteSpace(Model.DL_ID))
                        {
                            <a style="margin: -15px 0px;" role="button" class="btn btn-danger btn-sm" onclick="delete_show()"><em class="fa fa-trash"></em>刪除</a>
                        }
                    }

                    <a style="margin: -15px 0px;" role="button" class="btn btn-success btn-sm" onclick="onBack()">回上一頁</a>
                </span>
            </div>
        </div><!-- /.container -->
    </section><!-- /.feature-section -->
    <div style="height:25px"></div>
}

@section scripts{
    <script type="text/javascript">
        var targetFormID = '#formEdit';

        var opt = {
            showMonthAfterYear: true,
            dateFormat: 'yy/mm/dd',
            timeFormat: 'HH:mm:ss',
            showSecond: true,
            showButtonPanel: true,
            showTime: true,
            beforeShow: function () {
                setTimeout(
                function () {
                    $('#ui-datepicker-div').css("z-index", 15);
                }, 100
                );
            },
            onSelect: function (dateText, inst) {
                $('#' + inst.id).attr('value', dateText);
            }
        };

        @*CKEDITOR.replace('Data_CONTENT_TXT'
            , { toolbar: 'Image', filebrowserImageUploadUrl: '@Url.Content(@"~/"+ (string)ViewBag.BRE_NO + @"/UploadPicture")' }
            );*@

        function delete_show() {

            if (confirm("您確定要刪除？") == true) {
                $(targetFormID).attr("action", "@Url.Action("Delete", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }
          $('#CheckYoutube').click(function () {

                      $.ajax({
                        url: "@(Url.Action("GetUrlArgument", "ADDI12"))",     // url位置
                        type: 'post',                   // post/get
                        data: {
                            StrUrl: $('#@Html.IdFor(model => model.YOUTUBE_URL)').val(),
                        },     // data
                        dataType: 'json',               // xml/json/script/html
                        cache: false,                   // 是否允許快取
                        success: function (data) {
                            var res = jQuery.parseJSON(data);

                            if (res.Success == 0) { //失敗
                                $('#@Html.IdFor(model => model.YOUTUBE_URL)').val('')
                                alert(res.Error);
                            }
                            else if (res.Success == 1) //成功
                            {
                               alert('正確');
                             //  $('#@Html.IdFor(model => model.YOUTUBE_URL)').val(res.EmbedYouTubeUrl)
                            }
                        },
                        error: function (xhr, err) {
                            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                            alert("responseText: " + xhr.responseText);
                        }
                    });

            });
        function onSave() {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("VedioADD", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

    </script>
}

