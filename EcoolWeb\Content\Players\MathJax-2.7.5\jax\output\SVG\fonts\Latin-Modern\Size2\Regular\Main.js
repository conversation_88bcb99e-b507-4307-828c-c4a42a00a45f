/*
 *  /MathJax/jax/output/SVG/fonts/Latin-Modern/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.LatinModernMathJax_Size2={directory:"Size2/Regular",family:"LatinModernMathJax_Size2",id:"LATINMODERNSIZE2",32:[0,0,332,0,0,""],40:[847,347,458,131,395,"395 -336c0 -6 -5 -11 -11 -11c-3 0 -5 1 -7 2c-127 96 -246 330 -246 531v128c0 201 119 435 246 531c2 1 4 2 7 2c6 0 11 -5 11 -11c0 -4 -2 -7 -4 -9c-120 -90 -202 -324 -202 -513v-128c0 -189 82 -423 202 -513c2 -2 4 -5 4 -9"],41:[847,347,458,63,327,"327 186c0 -201 -119 -435 -246 -531c-2 -1 -4 -2 -7 -2c-6 0 -11 5 -11 11c0 4 2 7 4 9c120 90 202 324 202 513v128c0 189 -82 423 -202 513c-2 2 -4 5 -4 9c0 6 5 11 11 11c3 0 5 -1 7 -2c127 -96 246 -330 246 -531v-128"],47:[1108,608,768,57,713,"713 1084c0 -3 -1 -6 -2 -9l-607 -1667c-3 -9 -12 -16 -23 -16c-14 0 -24 11 -24 24c0 3 0 6 1 9l607 1667c3 9 12 16 23 16c14 0 25 -11 25 -24"],91:[850,350,417,217,383,"383 -327c0 -13 -10 -23 -23 -23h-143v1200h143c13 0 23 -10 23 -23s-10 -23 -23 -23h-97v-1108h97c13 0 23 -10 23 -23"],92:[1108,608,768,55,711,"711 -584c0 -13 -10 -24 -24 -24c-11 0 -20 7 -23 16l-607 1667c-1 3 -2 6 -2 9c0 13 11 24 25 24c11 0 20 -7 23 -16l607 -1667c1 -3 1 -6 1 -9"],93:[850,350,417,34,200,"200 850v-1200h-143c-13 0 -23 10 -23 23s10 23 23 23h97v1108h-97c-13 0 -23 10 -23 23s10 23 23 23h143"],123:[850,350,583,98,486,"486 -336c0 -8 -6 -14 -14 -14h-1c-115 12 -214 75 -214 150v300c0 62 -62 128 -146 136c-7 1 -13 7 -13 14s6 13 13 14c84 8 146 74 146 136v300c0 75 99 138 214 150h1c8 0 14 -6 14 -14c0 -7 -6 -13 -13 -14c-84 -8 -146 -67 -146 -122v-300c0 -67 -63 -124 -146 -150 c83 -26 146 -83 146 -150v-300c0 -55 62 -114 146 -122c7 -1 13 -7 13 -14"],124:[972,472,278,115,164,"164 -448c0 -14 -11 -24 -25 -24s-24 10 -24 24v1396c0 14 10 24 24 24s25 -10 25 -24v-1396"],125:[850,350,583,97,485,"485 250c0 -7 -6 -13 -13 -14c-84 -8 -146 -74 -146 -136v-300c0 -75 -99 -138 -214 -150h-1c-8 0 -14 6 -14 14c0 7 6 13 13 14c84 8 146 67 146 122v300c0 67 63 124 146 150c-83 26 -146 83 -146 150v300c0 55 -62 114 -146 122c-7 1 -13 7 -13 14c0 8 6 14 14 14h1 c115 -12 214 -75 214 -150v-300c0 -62 62 -128 146 -136c7 -1 13 -7 13 -14"],160:[0,0,332,0,0,""],770:[746,-572,768,0,768,"768 596l-8 -24c-127 34 -252 72 -376 114c-124 -42 -249 -80 -376 -114l-8 24c126 56 254 106 384 150c130 -44 258 -94 384 -150"],771:[753,-548,778,0,778,"778 740c0 -2 0 -4 -1 -6c-45 -81 -90 -134 -172 -154c-16 -4 -33 -6 -49 -6c-61 0 -121 25 -178 50c-50 22 -101 45 -153 45c-13 0 -26 -2 -38 -5c-70 -16 -125 -41 -164 -110c-2 -4 -6 -6 -10 -6c-7 0 -13 5 -13 12c0 2 1 4 2 6c45 81 90 134 171 154c17 4 33 6 49 6 c61 0 121 -25 179 -50c49 -22 100 -45 153 -45c12 0 25 2 38 5c69 16 124 41 163 110c2 4 6 7 11 7c7 0 12 -6 12 -13"],774:[742,-578,784,0,784,"784 734c-49 -145 -230 -156 -392 -156s-344 11 -392 156l24 8c36 -107 219 -107 368 -107c148 0 332 0 368 107"],780:[740,-566,768,0,768,"768 716c-126 -56 -254 -106 -384 -150c-130 44 -258 94 -384 150l8 24c127 -34 252 -72 376 -114c124 42 249 80 376 114"],785:[757,-592,784,0,784,"784 600l-24 -8c-36 107 -220 108 -368 108c-149 0 -332 -1 -368 -108l-24 8c48 145 230 157 392 157s343 -12 392 -157"],812:[-96,270,768,0,768,"768 -120c-126 -55 -254 -106 -384 -150c-130 44 -258 95 -384 150l8 24c127 -33 252 -71 376 -114c124 43 249 81 376 114"],813:[-108,282,768,0,768,"768 -258l-8 -24c-127 34 -252 71 -376 114c-124 -43 -249 -80 -376 -114l-8 24c126 55 254 106 384 150c130 -44 258 -95 384 -150"],814:[-96,260,784,0,784,"784 -104c-49 -145 -230 -156 -392 -156s-344 11 -392 156l24 8c36 -107 219 -107 368 -107c148 0 332 0 368 107"],815:[-118,282,784,0,784,"784 -275l-24 -7c-36 107 -220 107 -368 107c-149 0 -332 0 -368 -107l-24 7c48 145 230 157 392 157s343 -12 392 -157"],816:[-118,323,778,0,778,"778 -130c0 -3 0 -5 -1 -7c-45 -80 -90 -134 -172 -153c-16 -4 -33 -6 -49 -6c-61 0 -121 24 -178 49c-50 22 -101 45 -153 45c-13 0 -26 -1 -38 -4c-70 -17 -125 -42 -164 -111c-2 -3 -6 -6 -10 -6c-7 0 -13 6 -13 13c0 2 1 4 2 6c45 80 90 134 171 153c17 4 33 6 49 6 c61 0 121 -24 179 -49c49 -22 100 -45 153 -45c12 0 25 1 38 4c69 17 124 42 163 111c2 3 6 6 11 6c7 0 12 -6 12 -12"],8214:[972,472,364,57,309,"106 -448c0 -14 -11 -24 -25 -24s-24 10 -24 24v1396c0 14 10 24 24 24s25 -10 25 -24v-1396zM309 -448c0 -14 -11 -24 -25 -24s-24 10 -24 24v1396c0 14 10 24 24 24s25 -10 25 -24v-1396"],8260:[1108,608,768,57,713,"713 1084c0 -3 -1 -6 -2 -9l-607 -1667c-3 -9 -12 -16 -23 -16c-14 0 -24 11 -24 24c0 3 0 6 1 9l607 1667c3 9 12 16 23 16c14 0 25 -11 25 -24"],8425:[735,-541,1110,0,1110,"1110 735v-171c0 -12 -10 -23 -23 -23s-23 11 -23 23v125h-1018v-125c0 -12 -10 -23 -23 -23s-23 11 -23 23v171h1110"],8730:[1150,650,1000,110,1020,"1020 1130c0 0 0 -5 -5 -20l-549 -1736c-8 -24 -9 -24 -42 -24l-230 802l-68 -80l-16 15l139 163l215 -750l513 1624c4 13 8 26 23 26c12 0 20 -9 20 -20"],8739:[972,472,278,115,164,"164 -448c0 -14 -11 -24 -25 -24s-24 10 -24 24v1396c0 14 10 24 24 24s25 -10 25 -24v-1396"],8741:[972,472,364,57,309,"106 -448c0 -14 -11 -24 -25 -24s-24 10 -24 24v1396c0 14 10 24 24 24s25 -10 25 -24v-1396zM309 -448c0 -14 -11 -24 -25 -24s-24 10 -24 24v1396c0 14 10 24 24 24s25 -10 25 -24v-1396"],8968:[850,350,472,182,443,"443 827c0 -13 -10 -23 -23 -23h-192v-1131c0 -13 -10 -23 -23 -23s-23 10 -23 23v1177h238c13 0 23 -10 23 -23"],8969:[850,350,472,29,290,"290 -327c0 -13 -10 -23 -23 -23s-23 10 -23 23v1131h-192c-13 0 -23 10 -23 23s10 23 23 23h238v-1177"],8970:[850,350,472,182,443,"443 -327c0 -13 -10 -23 -23 -23h-238v1177c0 13 10 23 23 23s23 -10 23 -23v-1131h192c13 0 23 -10 23 -23"],8971:[850,350,472,29,290,"290 827v-1177h-238c-13 0 -23 10 -23 23s10 23 23 23h192v1131c0 13 10 23 23 23s23 -10 23 -23"],9001:[850,350,472,139,411,"411 -327c0 -13 -10 -23 -23 -23c-10 0 -18 6 -21 15l-226 577c-1 2 -2 5 -2 8s1 6 2 8l226 577c3 9 11 15 21 15c13 0 23 -10 23 -23c0 -3 -1 -6 -2 -8l-222 -569l222 -569c1 -2 2 -5 2 -8"],9002:[850,350,472,61,333,"333 250c0 -3 -1 -6 -2 -8l-226 -577c-3 -9 -11 -15 -21 -15c-13 0 -23 10 -23 23c0 3 1 6 2 8l222 569l-222 569c-1 2 -2 5 -2 8c0 13 10 23 23 23c10 0 18 -6 21 -15l226 -577c1 -2 2 -5 2 -8"],9140:[735,-541,1110,0,1110,"1110 735v-171c0 -12 -10 -23 -23 -23s-23 11 -23 23v125h-1018v-125c0 -12 -10 -23 -23 -23s-23 11 -23 23v171h1110"],9141:[-111,305,1110,0,1110,"1110 -305h-1110v171c0 12 10 23 23 23s23 -11 23 -23v-125h1018v125c0 12 10 23 23 23s23 -11 23 -23v-171"],9180:[761,-511,1508,0,1508,"1508 525c0 -8 -6 -14 -14 -14c-4 0 -7 2 -10 4c-112 112 -398 188 -633 188h-194c-235 0 -521 -76 -633 -188c-3 -2 -6 -4 -10 -4c-8 0 -14 6 -14 14c0 4 2 7 4 10c117 117 407 226 653 226h194c246 0 536 -109 653 -226c2 -3 4 -6 4 -10"],9181:[-81,331,1508,0,1508,"1508 -95c0 -4 -2 -7 -4 -10c-117 -117 -407 -226 -653 -226h-194c-246 0 -536 109 -653 226c-2 3 -4 6 -4 10c0 8 6 14 14 14c4 0 7 -2 10 -4c112 -112 398 -188 633 -188h194c235 0 521 76 633 188c3 2 6 4 10 4c8 0 14 -6 14 -14"],9182:[818,-509,1494,0,1494,"1494 516c0 -4 -3 -7 -7 -7c-3 0 -6 1 -7 4c-27 76 -132 121 -222 121h-282c-115 0 -204 63 -229 136c-25 -73 -114 -136 -229 -136h-282c-90 0 -195 -45 -222 -121c-1 -3 -4 -4 -7 -4c-4 0 -7 3 -7 7v2c32 87 132 174 236 174h282c117 0 222 43 222 119c0 3 3 7 7 7 s7 -4 7 -7c0 -76 105 -119 222 -119h282c104 0 204 -87 236 -174v-2"],9183:[-78,387,1494,0,1494,"1494 -85v-3c-32 -87 -132 -174 -236 -174h-282c-117 0 -222 -43 -222 -118c0 -4 -3 -7 -7 -7s-7 3 -7 7c0 75 -105 118 -222 118h-282c-104 0 -204 87 -236 174v3c0 3 3 7 7 7c3 0 6 -2 7 -5c27 -76 132 -121 222 -121h282c115 0 204 -63 229 -136 c25 73 114 136 229 136h282c90 0 195 45 222 121c1 3 4 5 7 5c4 0 7 -4 7 -7"],9184:[854,-612,1550,0,1550,"1550 612h-70l-172 172h-1066l-172 -172h-70l242 242h1066"],9185:[-182,424,1550,0,1550,"1550 -182l-242 -242h-1066l-242 242h70l172 -172h1066l172 172h70"],10214:[850,350,480,142,458,"458 -327c0 -13 -10 -23 -23 -23h-293v1200h293c13 0 23 -10 23 -23s-10 -23 -23 -23h-100v-1108h100c13 0 23 -10 23 -23zM289 804h-101v-1108h101v1108"],10215:[850,350,480,22,338,"338 -350h-293c-13 0 -23 10 -23 23s10 23 23 23h100v1108h-100c-13 0 -23 10 -23 23s10 23 23 23h293v-1200zM292 -304v1108h-101v-1108h101"],10216:[850,350,472,139,411,"411 -327c0 -13 -10 -23 -23 -23c-10 0 -18 6 -21 15l-226 577c-1 2 -2 5 -2 8s1 6 2 8l226 577c3 9 11 15 21 15c13 0 23 -10 23 -23c0 -3 -1 -6 -2 -8l-222 -569l222 -569c1 -2 2 -5 2 -8"],10217:[850,350,472,61,333,"333 250c0 -3 -1 -6 -2 -8l-226 -577c-3 -9 -11 -15 -21 -15c-13 0 -23 10 -23 23c0 3 1 6 2 8l222 569l-222 569c-1 2 -2 5 -2 8c0 13 10 23 23 23c10 0 18 -6 21 -15l226 -577c1 -2 2 -5 2 -8"],10218:[850,350,682,139,621,"411 -327c0 -13 -10 -23 -23 -23c-10 0 -18 6 -21 15l-226 577c-1 2 -2 5 -2 8s1 6 2 8l226 577c3 9 11 15 21 15c13 0 23 -10 23 -23c0 -3 -1 -6 -2 -8l-222 -569l222 -569c1 -2 2 -5 2 -8zM621 -327c0 -13 -10 -23 -23 -23c-10 0 -18 6 -22 15l-226 577c-1 2 -1 5 -1 8 s0 6 1 8l226 577c4 9 12 15 22 15c13 0 23 -10 23 -23c0 -3 -1 -6 -2 -8l-222 -569l222 -569c1 -2 2 -5 2 -8"],10219:[850,350,682,61,543,"543 250c0 -3 -1 -6 -2 -8l-226 -577c-3 -9 -11 -15 -21 -15c-13 0 -23 10 -23 23c0 3 0 6 1 8l223 569l-223 569c-1 2 -1 5 -1 8c0 13 10 23 23 23c10 0 18 -6 21 -15l226 -577c1 -2 2 -5 2 -8zM333 250c0 -3 -1 -6 -2 -8l-226 -577c-3 -9 -11 -15 -21 -15 c-13 0 -23 10 -23 23c0 3 1 6 2 8l222 569l-222 569c-1 2 -2 5 -2 8c0 13 10 23 23 23c10 0 18 -6 21 -15l226 -577c1 -2 2 -5 2 -8"],10222:[864,364,323,118,267,"267 -350c0 -8 -6 -14 -14 -14c-4 0 -7 2 -10 4c-60 60 -125 290 -125 517v186c0 227 65 457 125 517c3 2 6 4 10 4c8 0 14 -6 14 -14c0 -4 -2 -7 -4 -10c-57 -57 -87 -281 -87 -497v-186c0 -216 30 -440 87 -497c2 -3 4 -6 4 -10"],10223:[864,364,323,56,205,"205 157c0 -227 -65 -457 -125 -517c-3 -2 -6 -4 -10 -4c-8 0 -14 6 -14 14c0 4 2 7 4 10c57 57 87 281 87 497v186c0 216 -30 440 -87 497c-2 3 -4 6 -4 10c0 8 6 14 14 14c4 0 7 -2 10 -4c60 -60 125 -290 125 -517v-186"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size2/Regular/Main.js");
