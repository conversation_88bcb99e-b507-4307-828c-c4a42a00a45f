﻿@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <p>
    </p>
    <p>
    </p>
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">新ATM酷幣查詢與獎品對兌</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-查詢", "Index4", null, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁", "Index4", new { TimeoutSeconds = "6" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:60px">
                    @Html.ActionLink("酷幣ATM-查詢(沒有借書)", "Index4", new { NoBook = "Y" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:60px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁(沒有借書)", "Index4", new { TimeoutSeconds = "6", NoBook = "Y" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>

                @if (ViewBag.Gift != null)
                {
                    <div class="col-md-6">
                        @Html.ActionLink("酷幣ATM-直換禮物", "Index4", new { TimeoutSeconds = "6", Mode = "gift" }, new { @class = "btn-block btn btn-default", @role = "button" })
                    </div>
                }
            </div>
            <div class="form-group">
                <label class="text-danger">
                </label>
            </div>
        </div>
    </div>
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">簡易ATM酷幣查詢與獎品對兌</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-查詢", "Index5", null, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁", "Index5", new { TimeoutSeconds = "6" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
            </div>
            <div class="form-group">
                <label class="text-danger">
                </label>
            </div>
        </div>
    </div>
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-查詢", "Index", null, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁", "Index", new { TimeoutSeconds = "6" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:60px">
                    @Html.ActionLink("酷幣ATM-查詢(沒有借書)", "Index", new { NoBook = "Y" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:60px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁(沒有借書)", "Index", new { TimeoutSeconds = "6", NoBook = "Y" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>

                @if (ViewBag.Gift != null)
                {
                    <div class="col-md-6">
                        @Html.ActionLink("酷幣ATM-直換禮物", "Index", new { TimeoutSeconds = "6", Mode = "gift" }, new { @class = "btn-block btn btn-default", @role = "button" })
                    </div>
                }
            </div>
            <div class="form-group">
                <label class="text-danger">
                </label>
            </div>
        </div>
    </div>

}