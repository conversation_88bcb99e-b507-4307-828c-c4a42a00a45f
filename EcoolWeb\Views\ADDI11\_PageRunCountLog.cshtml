﻿@model ADDI11MyRunLogViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();


}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@*<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
    <script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>*@

@Html.HiddenFor(m => m.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.WhereKM)
@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)





<img src="~/Content/img/web-bar-Run.png" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center" id="tbData">
        <table class="table-ecool table-92Per table-hover table-ecool-reader">
            <thead>
                <tr>
                    <th>
                        @Html.DisplayNameFor(m => m.RunCity.First().LocalName)
                    </th>
                    <th onclick="doSort1('RUN_TOTAL_KM')">
                        @Html.DisplayNameFor(m => m.RunCity.First().RUN_TOTAL_KM)
                        <img id="RUN_TOTAL_KM" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th onclick="doSort1('LocalNameCount')">
                        @(Html.DisplayNameFor(m => m.RunCity.First().LocalNameCount))
                        <img id="LocalNameCount" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @{ int Num = 0; }
                @foreach (var item in Model.RunCity)
                {
                    string clor = "";
                    clor =@item.BGCOLOR;
                    //if (Num == 0 || Num % 2 == 0 )
                    //{
                    //    clor = "background-color:#ffff";

                    //}
                    //else {

                    //    clor = "background-color:#fafec9";

                    //}
                    <tr class="@clor">

                        <td class="text-left">@Html.DisplayFor(modelItem => item.LocalName)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.RUN_TOTAL_KM)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.LocalNameCount)</td>
                        <td style="text-align: center">
                            <a href="@Url.Action("_PageRunDetailLog", (string)ViewBag.BRE_NO,new {  WhereSCHOOL_NO= Model.WhereSCHOOL_NO,  WhereKMS=item.RUN_TOTAL_KM})" class="progress cboxElement" type="button">  詳細</a>
                            @*<button type="button" class="btn btn-default" onclick="@Url.Action("_PageRunDetailLog", (string)ViewBag.BRE_NO,new {  WhereSCHOOL_NO= Model.WhereSCHOOL_NO,  WhereKM=Model.WhereKM})">
                                    詳細
                                </button>*@
                        </td>
                    </tr>
                    Num++;
                }
            </tbody>
        </table>
    </div>
</div>
@*@if (Model.RunCity.Count() == 0)
    {
        <div class="text-center">
            <h3>無任何跑步記錄</h3>
        </div>

    }
    else
    {
        <div style="height:25px"></div>
        <div class="col-sm-12">
            @if (Model.MyRunColumnChart != null)
            {
                @Model.MyRunColumnChart
            }
        </div>
    }*@
<script>

    $(document).ready(function () {

        $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
    });
    function doSort1(SortCol) {
        var sort = "";
        sort = SortCol;
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        $('#OrdercColumn').val(SortCol);

        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("ASC");
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
        }
        else {

            $('#OrdercColumn').val(sort);
            $('#SyntaxName').val("Desc");
        }
        funAjax1()
    }
     function funAjax1() {
        
       
        $.ajax({
            type: "GET",
                url: '@(Url.Action("_PageRunCountLog", (string)ViewBag.BRE_NO))',
            data: {
             
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
               
                WhereSCHOOL_NO:$('#WhereSCHOOL_NO').val(),
            },
                async: false,
                cache: false,
                dataType: 'html',
            success: function (data) {
              
                $("#form1").html('');
               
                $("#form1").html(data);
                }
            });
    }
</script>