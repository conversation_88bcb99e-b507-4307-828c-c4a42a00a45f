﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI31ANSWERSViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@helper  buttonFun()
    {

      <div class="form-group text-center">
          @if (Model.uQAT17.ITEM_NO != null)
          {
              @Html.PermissionButton("送出", "button", (string)ViewBag.BRE_NO, (string)ViewBag.Permission, new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI31Controller.DATA_TYPE.DATA_TYPE_U + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
              @Html.PermissionButton("刪除", "button", (string)ViewBag.BRE_NO, (string)ViewBag.Permission, new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI31Controller.DATA_TYPE.DATA_TYPE_D + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
          }
          else
          {
              @Html.PermissionButton("送出", "button", (string)ViewBag.BRE_NO, (string)ViewBag.Permission, new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI31Controller.DATA_TYPE.DATA_TYPE_A + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
          }
          @Html.PermissionButton("回上一頁", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-default", onclick = "Details()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
      </div>
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.PermissionButton("回查詢列表", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-sm btn-sys", onclick = "Index()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

   
        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center">
                @Html.BarTitle()
            </div>
            <div class="panel-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        @Html.LabelFor(model => model.uQAT17.SNAME, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.uQAT17.SNAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                            @Html.ValidationMessageFor(model => model.uQAT17.SNAME, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.uQAT17.E_MAIL, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.uQAT17.E_MAIL, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.uQAT17.E_MAIL, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.uQAT17.ANSWERS, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(model => model.uQAT17.ANSWERS, new { @class = "form-control", @rows = "5", @cols = "20", @placeholder = "必填" })
                            @Html.ValidationMessageFor(model => model.uQAT17.ANSWERS, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳附件</label>
                        <div class="col-md-9">
                            <input class="btn btn-default" type="file" name="files" />
                            @if (Model.uQAT17.FILE_NAME != null && Model.uQAT17.FILE_NAME != string.Empty)
                            {
                                @Html.PermissionButton("X", "button", (string)ViewBag.BRE_NO, (string)ViewBag.Permission, new { @class = "btn btn-info btn-xs", onclick = "DelFile('" + Model.uQAT17.QUESTIONS_ID + "', '"+ Model.uQAT17.ITEM_NO+ "', '" + Model.uQAT17.FILE_NAME + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                                @Html.DisplayFor(model => model.uQAT17.FILE_NAME)
                                @Html.HiddenFor(model => model.uQAT17.FILE_NAME)
                            }

                            @Html.ValidationMessageFor(model => model.uQAT17.FILE_NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            </div>
             @buttonFun()
        </div>
  

   

    @Html.Hidden("DATA_TYPE")
    @Html.Hidden("ANS_TYPE", (string)ViewBag.ANS_TYPE)


    @Html.HiddenFor(model => model.uQAT17.QUESTIONS_ID)
    @Html.HiddenFor(model => model.uQAT17.ITEM_NO)
    @Html.HiddenFor(model => model.uQAT17.ANS_TYPE)
    @Html.HiddenFor(model => model.ITEM_NO)

    @Html.HiddenFor(model => model.Search.Page)
    @Html.HiddenFor(model => model.Search.OrderByName)
    @Html.HiddenFor(model => model.Search.Q_QUESTIONS_ID)
    @Html.HiddenFor(model => model.Search.SearchContents)
    @Html.HiddenFor(model => model.Search.SyntaxName)
    @Html.HiddenFor(model => model.Search.DetailsPage)
    @Html.HiddenFor(model => model.Search.STATUS)


}

@section Scripts {
    <script language="JavaScript">

        function Index() {
            form1.action = '@Html.Raw(@Url.Action("Index", (string)ViewBag.BRE_NO))'
            form1.submit();
        }

        function Details() {
            form1.action = '@Html.Raw(@Url.Action("Details", (string)ViewBag.BRE_NO))'
            form1.submit();
        }


        function DelFile(QUESTIONS_ID, ITEM_NO, FileName) {
            form1.action = '@Html.Raw(@Url.Action("DelFile", (string)ViewBag.BRE_NO))' + '?QUESTIONS_ID=' + QUESTIONS_ID + '&ITEM_NO=' + ITEM_NO + '&FileName=' + FileName
            form1.submit();
        }


        function Save(Val) {
            form1.DATA_TYPE.value = Val
            form1.submit();
        }
    </script>
}