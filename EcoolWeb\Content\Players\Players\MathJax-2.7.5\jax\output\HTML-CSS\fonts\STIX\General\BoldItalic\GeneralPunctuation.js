/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/GeneralPunctuation.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{8208:[282,-166,333,-4,273],8209:[282,-166,333,-4,273],8210:[282,-166,500,-40,477],8211:[269,-178,500,-40,477],8212:[269,-178,1000,-40,977],8216:[685,-369,333,128,332],8217:[685,-369,333,98,302],8218:[134,182,333,-5,199],8219:[685,-369,333,128,302],8220:[685,-369,500,53,513],8221:[685,-369,500,53,513],8222:[134,182,500,-57,403],8223:[685,-369,500,92,513],8224:[685,145,500,91,494],8225:[685,139,500,10,493],8226:[462,-42,560,70,490],8230:[135,13,1000,40,852],8240:[706,29,1118,80,1068],8241:[706,29,1480,80,1430],8249:[415,-32,333,32,303],8250:[415,-32,333,10,281],8254:[838,-766,500,0,500],8260:[688,12,183,-168,345]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/GeneralPunctuation.js");
