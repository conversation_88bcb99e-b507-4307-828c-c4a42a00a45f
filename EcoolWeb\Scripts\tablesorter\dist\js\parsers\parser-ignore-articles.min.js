(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Parser: ignoreArticles - updated 9/15/2014 (v2.17.8) */
!function(o){"use strict";var g=o.tablesorter;g.ignoreArticles={en:"the, a, an",de:"der, die, das, des, dem, den, ein, eine, einer, eines, einem, einen",nl:"de, het, de, een",es:"el, la, lo, los, las, un, una, unos, unas",pt:"o, a, os, as, um, uma, uns, umas",fr:"le, la, l'_, les, un, une, des",it:"il, lo, la, l'_, i, gli, le, un', uno, una, un",hu:"a, az, egy"},g.addParser({id:"ignoreArticles",is:function(){return!1},format:function(e,r,s,a){var n,i,t,l=r.config,d=e||"";return l.headers&&l.headers[a]&&l.headers[a].ignoreArticlesRegex||(l.headers||(l.headers={}),l.headers[a]||(l.headers[a]={}),t=g.getData(l.$headers.eq(a),g.getColumnData(r,l.headers,a),"ignoreArticles"),n=(g.ignoreArticles[t]||"the, a, an")+"",l.headers[a].ignoreArticlesRegex=new RegExp("^("+o.trim(n.split(/\s*\,\s*/).join("\\s|")+"\\s").replace("_\\s","")+")","i"),i=g.getData(l.$headers.eq(a),g.getColumnData(r,l.headers,a),"ignoreArticlesExcept"),l.headers[a].ignoreArticlesRegex2=""!==i?new RegExp("^("+i.replace(/\s/g,"\\s")+")","i"):""),!(n=l.headers[a].ignoreArticlesRegex).test(d)||(i=l.headers[a].ignoreArticlesRegex2)&&i.test(d)?d:d.replace(n,"")},type:"text"})}(jQuery);return jQuery;}));
