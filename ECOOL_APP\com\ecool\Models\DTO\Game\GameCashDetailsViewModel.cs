﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameCashDetailsViewModel
    {

        /// <summary>
        ///進帳點數
        /// </summary>
        [DisplayName("點數")]
        public short CASH_IN { get; set; }

        /// <summary>
        ///時間
        /// </summary>
        [DisplayName("時間")]
        public DateTime? LOG_TIME { get; set; }

        /// <summary>
        ///異動說明
        /// </summary>
        [DisplayName("異動說明")]
        public string LOG_DESC { get; set; }



        /// <summary>
        ///異動時可用酷幣點數(含這次)
        /// </summary>
        [DisplayName("可用點數")]
        public int? LOG_CASH_AVAILABLE { get; set; }

   
    }
}
