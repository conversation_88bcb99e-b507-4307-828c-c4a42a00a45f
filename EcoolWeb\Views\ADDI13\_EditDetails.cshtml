﻿@model ADDI13EditPeopleViewModel

    @if (Model != null)
    {
        using (Html.BeginCollectionItem("Details"))
        {
            var Index = Html.GetIndex("Details");

<div class="tr" id="Tr@(Model.CARD_NO)">
    <div class="td" style="text-align:center">
        <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Model.CARD_NO)')"> <i class='glyphicon glyphicon-remove'></i></a>
        @Html.HiddenFor(m => m.SCHOOL_NO)
        @Html.HiddenFor(m => m.SHORT_NAME)
        @Html.HiddenFor(m => m.USER_NO)
        @Html.HiddenFor(m => m.NAME)
        @Html.HiddenFor(m => m.GRADE)
        @Html.HiddenFor(m => m.CLASS_NO)
        @Html.HiddenFor(m => m.SEAT_NO)
        @Html.HiddenFor(m => m.CARD_NO)
        @Html.HiddenFor(m => m.CHG_DATE)
    </div>
    <div class="td" style="text-align:center">
        @Model.SHORT_NAME
    </div>
    <div class="td" style="text-align:center">
        @Model.NAME
    </div>
    <div class="td" style="text-align:center" id="@(Model.SCHOOL_NO+Model.USER_NO)">
        @Model.USER_NO
    </div>
    <div class="td" style="text-align:center" id="@(Model.SCHOOL_NO+Model.CLASS_NO+Model.SEAT_NO)">
        @Model.CLASS_NO
    </div>
    <div class="td" style="text-align:center">
        @Model.SEAT_NO
    </div>
    <div class="td" style="text-align:center">
        @Model.CHG_DATE
    </div>
</div>
        }
    }