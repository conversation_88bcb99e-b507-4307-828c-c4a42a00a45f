﻿@model RollCallIndexViewModel
@using ECOOL_APP;
@{
    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;
}

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)

<div class="toolbar text-right">
    <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)'>
        @*<span class="fa fa-plus" aria-hidden="true"></span>*@
        新增活動
    </a>
   
</div>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-white">
                <tr class="thead-primary">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ROLL_CALL_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ROLL_CALL_DATES, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ROLL_CALL_DATEE, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ROLL_CALL_COUNT, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CASH, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CRE_PERSON_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th style="width:60px"></th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {

                        <tr class="text-center">
                            <td>@Html.DisplayFor(modelItem => item.ROLL_CALL_NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.ROLL_CALL_DATES)</td>
                            <td>@Html.DisplayFor(modelItem => item.ROLL_CALL_DATEE)</td>
                            <td>@Html.DisplayFor(modelItem => item.ROLL_CALL_COUNT)</td>
                            <td>@Html.DisplayFor(modelItem => item.CASH)</td>
                            <td>@Html.DisplayFor(modelItem => item.CRE_PERSON_NAME)</td>

                            <td class="text-nowrap">

                                @if (Permission.Where(a => a.ActionName == "Edit").Any())
                                {
                                    <a href="@Url.Action("Edit",new {Keyword = item.ROLL_CALL_ID })" role="button" class="btn btn-xs btn-Basic" title="編輯">
                                        <span class="fa fa-pencil" aria-hidden="true"></span> 編輯
                                    </a>
                                }

                                @if (item.STATUS == (byte)AWAT12.StatusVal.點名前 && Permission.Where(a => a.ActionName == "Index").Any())
                                {
                                    <button type="button" onclick="onBtnRollCall('@item.ROLL_CALL_ID')" role="button" class="btn btn-xs btn-Basic" title="點名">
                                        <span class="glyphicon glyphicon glyphicon-user" aria-hidden="true">點名</span>
                                    </button>
                                }
                               
                                else if (item.ROLL_CALL_DATEE > DateTime.Now && item.STATUS != (byte)AWAT12.StatusVal.己給點)
                                {

                                    <button type="button" onclick="onBtnRollCall('@item.ROLL_CALL_ID')" role="button" class="btn btn-xs btn-Basic" title="點名">
                                        <span class="glyphicon glyphicon glyphicon-user" aria-hidden="true">點名</span>
                                    </button>
                                    <a href="@Url.Action("RollCallCash", new { Keyword = item.ROLL_CALL_ID })" role="button" class="btn btn-xs btn-Basic" title="給點">
                                        <span class="fa fa-pencil" aria-hidden="true"></span> 給點
                                    </a>
                                }
                                else if (item.STATUS == (byte)AWAT12.StatusVal.點名後 && Permission.Where(a => a.ActionName == "RollCallCash").Any())
                                {

                                    <a href="@Url.Action("RollCallCash", new { Keyword = item.ROLL_CALL_ID })" role="button" class="btn btn-xs btn-Basic" title="給點">
                                        <span class="fa fa-pencil" aria-hidden="true"></span> 給點
                                    </a>
                                }
                                else if (item.STATUS == (byte)AWAT12.StatusVal.己給點 && Permission.Where(a => a.ActionName == "RollCallCash").Any())
                                {

                                    <a href="@Url.Action("RollCallCash", new { Keyword = item.ROLL_CALL_ID })" role="button" class="btn btn-xs btn-Basic" title="給點結果">
                                        <span class="glyphicon glyphicon-search" aria-hidden="true"></span> 給點結果
                                    </a>
                                }
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center" style="padding:10px"><strong>目前無資料</strong></div>
        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)