﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
   public class GetWinnerItemViewModel
    {


        [DisplayName("學號")]
        public string USER_NO { get; set; }

        [DisplayName("班級")]
        public string CLASS_NO { get; set; }
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }
        [DisplayName("姓名")]

        public string NAME { get; set; }

       
        public string SNAME { get; set; }


        public string SCHOOL_NO { get; set; }

        public string PrizeId { get; set; }
        [DisplayName("獎品名稱")]
        public string PrizeName { get; set; }
        public Nullable<int> PrizeQty { get; set; }
        public Nullable<decimal> PrizeRate { get; set; }
        public string UpdateUserID { get; set; }
        [DisplayName("兌換時間")]
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public string GAME_NO { get; set; }
        public string LevelNO { get; set; }
        public Nullable<bool> Y_CASH { get; set; }
        public int? FixQty { get; set; }

        public string CreaUserID { get; set; }

    }
}
