﻿@model ZZZI33IndexViewModel


<a role="button" onclick="onAdd()" class="btn btn-sm btn-sys">
    <i class="fa fa-fw -square -circle fa-plus-square"></i>新增
</a>

@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.whereSOU_KEY)
@Html.HiddenFor(m => m.Search.whereQUESTIONNAIRE_ID)
@Html.HiddenFor(m => m.Search.BackAction)
@Html.HiddenFor(m => m.Search.BackController)

<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">
            @Html.DisplayNameFor(model => model.Search.whereSearch)
        </label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.Search.whereSearch, new { htmlAttributes = new { @class = "form-control" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>


<img src="~/Content/img/web-bar-vote.png" class="img-responsive" alt="Responsive image" />
<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover table-ecool-reader">
        <thead>
            <tr>
                <th></th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().QUESTIONNAIRE_NAME)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().QUESTIONNAIRE_SDATE)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().QUESTIONNAIRE_EDATE)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().STATUS)</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ListData)
            {

                <tr>
                    <td align="center">
                        <button class="btn btn-xs btn-Basic" onclick="Edit_show('@item.QUESTIONNAIRE_ID')">編輯</button>
                        @*<button class="btn btn-xs btn-Basic" onclick="Details_show('@item.QUESTIONNAIRE_ID')">預覽</button>*@
                    </td>
                    <td>@Html.DisplayFor(modelItem => item.QUESTIONNAIRE_NAME)</td>
                    <td>@Html.DisplayFor(modelItem => item.QUESTIONNAIRE_SDATE)</td>

                    <td>@Html.DisplayFor(modelItem => item.QUESTIONNAIRE_EDATE)</td>
                    <td>
                    @if (item.QUESTIONNAIRE_EDATE <= DateTime.Now)
                    {
                        <samp>投票結束</samp>
                    }
                    else
                    {
                        @SAQT01.STATUSVal.GetDesc(item.STATUS)
                    }

                </td>
            </tr>
            }
        </tbody>
    </table>
</div>

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
</div>







