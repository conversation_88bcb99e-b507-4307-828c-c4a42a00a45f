﻿@model ZZZI34LikeListViewModel


@Html.HiddenFor(m => m.PHOTO_NO)
<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover">
        <thead>
            <tr>
                <th>@Html.DisplayNameFor(m => m.LikeList.First().SHORT_NAME)</th>
                <th>@Html.DisplayNameFor(m => m.LikeList.First().CLASS_NO)</th>
                <th>@Html.DisplayNameFor(m => m.LikeList.First().NICK_NAME)</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.LikeList)
            {
                <tr>
                    <td>
                        @if (string.IsNullOrWhiteSpace(item.SHORT_NAME))
	                    {
		                    <text>訪客</text>
                         }
                         else
                         {
                            @Html.DisplayFor(modelItem => item.SHORT_NAME)
                         }
                    </td>
                    <td>@if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                    {
                        <text>-</text>
                    }
                    else
                    {
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    }
                    </td>

                    <td>@Html.DisplayFor(modelItem => item.NICK_NAME)</td>
                </tr>
            }
        </tbody>
    </table>
</div>








