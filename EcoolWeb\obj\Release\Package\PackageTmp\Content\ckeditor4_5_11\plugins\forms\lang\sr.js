﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'sr', {
	button: {
		title: 'Особине дугмета',
		text: 'Текст (вредност)',
		type: 'Tип',
		typeBtn: 'Button',
		typeSbm: 'Submit',
		typeRst: 'Reset'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Особине поља за потврду',
		radioTitle: 'Особине радио-дугмета',
		value: 'Вредност',
		selected: 'Означено',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Особине форме',
		menu: 'Особине форме',
		action: 'Aкција',
		method: 'Mетода',
		encoding: 'Encoding'
	},
	hidden: {
		title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> скривеног поља',
		name: 'Назив',
		value: 'Вредност'
	},
	select: {
		title: 'Особине изборног поља',
		selectInfo: 'Инфо',
		opAvail: 'Доступне опције',
		value: 'Вредност',
		size: 'Величина',
		lines: 'линија',
		chkMulti: 'Дозволи вишеструку селекцију',
		required: 'Required', // MISSING
		opText: 'Текст',
		opValue: 'Вредност',
		btnAdd: 'Додај',
		btnModify: 'Измени',
		btnUp: 'Горе',
		btnDown: 'Доле',
		btnSetValue: 'Подеси као означену вредност',
		btnDelete: 'Обриши'
	},
	textarea: {
		title: 'Особине зоне текста',
		cols: 'Број колона',
		rows: 'Број редова'
	},
	textfield: {
		title: 'Особине текстуалног поља',
		name: 'Назив',
		value: 'Вредност',
		charWidth: 'Ширина (карактера)',
		maxChars: 'Максимално карактера',
		required: 'Required', // MISSING
		type: 'Тип',
		typeText: 'Текст',
		typePass: 'Лозинка',
		typeEmail: 'Е-пошта',
		typeSearch: 'Претрага',
		typeTel: 'Број телефона',
		typeUrl: 'УРЛ'
	}
} );
