﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.LogicCenter.Interfaces
{
    /// <summary>
    /// Excel匯入基底類別
    /// </summary>
    public abstract class AbstractExcelAccess
    {
        protected bool CheckDataTypeErr;
        protected bool HasErr;
        protected string ErrorRowCellExcel;
        protected string[] SheetNames;
        protected string[][] SheetMustColumnNames;

        public AbstractExcelAccess()
        {
            CheckDataTypeErr = false;
            HasErr = false;
        }
    }
}
