﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.util
{
    public static class DateHelper
    {
        /// <summary>
        /// 計算間隔秒數 或天數
        /// </summary>
        /// <param name="Sdate">開始時間</param>
        /// <param name="Edate">結束時間</param>
        /// <param name="Flag">傳回的類別 EX: sec , day,min,hour</param>
        /// <returns>Result</returns>
        public static long Get_DateDiff(DateTime Sdate, DateTime Edate, string Flag)
        {
            TimeSpan t = Edate - Sdate;
            long Result = 0;

            switch (Flag)
            {
                case "sec"://傳回間隔的秒數
                    Result = (long)t.TotalSeconds;
                    break;

                case "day"://傳回間隔的天數
                    Result = (long)t.TotalDays;
                    break;

                case "min": //傳回間隔的分鐘數
                    Result = (long)t.TotalMinutes;
                    break;

                case "hour": //傳回間隔的分鐘數
                    Result = (long)t.TotalHours;
                    break;
            }
            return Result;
        }

        // 計算兩個日期時間差
        public static Tuple<int, int, int> TimespanToDate(this DateTime self, DateTime target)
        {
            int years, months, days;
            // 因為只需取量，不決定誰大誰小，所以如果self < target時要交換將大的擺前面
            if (self < target)
            {
                DateTime tmp = target;
                target = self;
                self = tmp;
            }

            // 將年轉換成月份以便用來計算
            months = 12 * (self.Year - target.Year) + (self.Month - target.Month);

            // 如果天數要相減的量不夠時要向月份借天數補滿該月再來相減
            if (self.Day < target.Day)
            {
                months--;
                days = DateTime.DaysInMonth(target.Year, target.Month) - target.Day + self.Day;
            }
            else
            {
                days = self.Day - target.Day;
            }

            // 天數計算完成後將月份轉成年
            years = months / 12;
            months = months % 12;

            return Tuple.Create(years, months, days);
        }

        public static double? CalAgeByBirth(DateTime? birth)
        {
            if (birth == null) return null;

            DateTime birthDate = (DateTime)birth.Value.Date;
            // 以下兩種將字串轉換 成DateTime格式都可以。
            // DateTime.TryParseExact(birth, new string []{"yyyyMMdd"}, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out birthDate);

            TimeSpan ts = DateTime.Now.Date - birthDate;
            double age = ts.TotalDays / 365.2422;
            return age;
        }

        public static double? CalAgeByBirth(DateTime CountDate, DateTime? birth)
        {
            if (birth == null) return null;

            DateTime birthDate = (DateTime)birth.Value.Date;
            // 以下兩種將字串轉換 成DateTime格式都可以。
            // DateTime.TryParseExact(birth, new string []{"yyyyMMdd"}, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out birthDate);

            TimeSpan ts = CountDate.Date - birthDate;
            double age = ts.TotalDays / 365.2422;
            return age;
        }

        /// <summary>
        /// 取得目前 年週數
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static int GetWeekOfYear(DateTime date)
        {
            if (date == null)
                return 0;

            DateTimeFormatInfo dfi = DateTimeFormatInfo.CurrentInfo;
            Calendar cal = dfi.Calendar;

            return cal.GetWeekOfYear(date, dfi.CalendarWeekRule, dfi.FirstDayOfWeek);
        }

        /// <summary>
        /// 取得 幾個月後 且指定取幾星期幾 日期
        /// </summary>
        /// <param name="dt">要計算日期</param>
        /// <param name="WhatMonthVal">幾個月</param>
        /// <param name="Whatday">星期幾</param>
        /// <returns></returns>
        public static DateTime GetDateWhatoMonth(DateTime dt, int WhatMonthVal, int Whatday)
        {
            DateTime MinDD = dt.AddMonths(WhatMonthVal).Date; //取得 X個月的日期

            DateTime dtStart = MinDD.AddDays(-1 * (((int)MinDD.DayOfWeek) - 1));  //取得週一 日期

            DateTime endWeek = dtStart.AddDays(Whatday - 1); //取得星期幾

            return endWeek;
        }

        /// <summary>
        /// 這二個日期 差幾週
        /// </summary>
        /// <param name="dt1"></param>
        /// <param name="dt2"></param>
        /// <returns></returns>
        public static int GetWeekInBTime(DateTime dt1, DateTime dt2)
        {
            TimeSpan ts = dt1 - dt2;
            DateTime dt = ts.TotalDays > 0 ? dt1 : dt2;
            int days = (int)System.Math.Abs(ts.TotalDays) + (7 - ((int)dt.DayOfWeek + 1));

            return days / 7 + (days % 7 == 0 ? 0 : 1);
        }

        /// <summary>
        /// 取得年週週未日期陣列=>傳入日期及要取幾個月的資料
        /// </summary>
        /// <param name="dt">日期</param>
        /// <param name="MonthVal">要傳回幾個月的資料</param>
        /// <returns></returns>
        public static List<string> GetArrWeekToDate(DateTime dt, int MonthVal)
        {
            return GetArrWeekToArr(dt, MonthVal, "DATE");
        }

        /// <summary>
        /// 取得年週陣列=>傳入日期及要取幾個月的資料
        /// </summary>
        /// <param name="dt">日期</param>
        /// <param name="MonthVal">要傳回幾個月的資料</param>
        /// <returns></returns>
        public static List<string> GetArrWeek(DateTime dt, int MonthVal)
        {
            return GetArrWeekToArr(dt, MonthVal, "YEARWEEK");
        }

        /// <summary>
        /// 取得 月份陣列 ex. 201501 、201502...
        /// </summary>
        /// <returns></returns>
        public static List<string> GetArrMonth(DateTime dt, int MonthVal)
        {
            List<string> ArrMonth = new List<string>();

            for (int i = 0; i < Math.Abs(MonthVal); i++)
            {
                if (MonthVal < 0)
                {
                    ArrMonth.Add(dt.AddMonths(i * -1).ToString("yyyyMM"));
                }
                else
                {
                    ArrMonth.Add(dt.AddMonths(i).ToString("yyyyMM"));
                }
            }

            ArrMonth.Sort();

            return ArrMonth;
        }

        /// <summary>
        /// 取得兩時間相差工作日天數(不包含週末) => 別人的Code, 靠這也太強了
        /// Calculates number of business days, taking into account:
        ///  - weekends (Saturdays and Sundays)
        ///  - bank holidays in the middle of the week
        /// </summary>
        /// <param name="firstDay">First day in the time interval</param>
        /// <param name="lastDay">Last day in the time interval</param>
        /// <param name="bankHolidays">List of bank holidays excluding weekends</param>
        /// <returns>Number of business days during the 'span'</returns>
        public static int BusinessDaysUntil(this DateTime firstDay, DateTime lastDay, params DateTime[] bankHolidays)
        {
            firstDay = firstDay.Date;
            lastDay = lastDay.Date;
            if (firstDay > lastDay)
                throw new ArgumentException("Incorrect last day " + lastDay);

            TimeSpan span = lastDay - firstDay;
            int businessDays = span.Days + 1;
            int fullWeekCount = businessDays / 7;
            // find out if there are weekends during the time exceedng the full weeks
            if (businessDays > fullWeekCount * 7)
            {
                // we are here to find out if there is a 1-day or 2-days weekend
                // in the time interval remaining after subtracting the complete weeks
                int firstDayOfWeek = (int)firstDay.DayOfWeek;
                int lastDayOfWeek = (int)lastDay.DayOfWeek;
                if (lastDayOfWeek < firstDayOfWeek)
                    lastDayOfWeek += 7;
                if (firstDayOfWeek <= 6)
                {
                    if (lastDayOfWeek >= 7)// Both Saturday and Sunday are in the remaining time interval
                        businessDays -= 2;
                    else if (lastDayOfWeek >= 6)// Only Saturday is in the remaining time interval
                        businessDays -= 1;
                }
                else if (firstDayOfWeek <= 7 && lastDayOfWeek >= 7)// Only Sunday is in the remaining time interval
                    businessDays -= 1;
            }

            // subtract the weekends during the full weeks in the interval
            businessDays -= fullWeekCount + fullWeekCount;

            // subtract the number of bank holidays during the time interval
            foreach (DateTime bankHoliday in bankHolidays)
            {
                DateTime bh = bankHoliday.Date;
                if (firstDay <= bh && bh <= lastDay)
                    --businessDays;
            }

            return businessDays;
        }

        /// <summary>
        /// To the simple taiwan date.
        /// </summary>
        /// <param name="datetime">The datetime.</param>
        /// <returns></returns>
        public static string ToSimpleTaiwanDate(this DateTime? datetime)
        {
            if (datetime == null)
            {
                return string.Empty;
            }

            var Thisdatetime = (DateTime)datetime;

            TaiwanCalendar taiwanCalendar = new TaiwanCalendar();

            return string.Format("{0}/{1}/{2}",
                taiwanCalendar.GetYear(Thisdatetime),
                Thisdatetime.Month,
                Thisdatetime.Day);
        }

        public static string ToSimpleTaiwanDateCYYMM(string YYMM, string Delimiter)
        {
            if (string.IsNullOrWhiteSpace(YYMM))
            {
                return string.Empty;
            }

            DateTime Thisdatetime = DateTime.ParseExact(YYMM + "01", "yyyyMMdd", null, System.Globalization.DateTimeStyles.AllowWhiteSpaces);

            TaiwanCalendar taiwanCalendar = new TaiwanCalendar();

            return string.Format("{0}{1}{2}",
                taiwanCalendar.GetYear(Thisdatetime),
                Delimiter,
                Thisdatetime.Month);
        }

        /// <summary>
        /// 取得年週相關陣列
        /// 傳回 DataType=Date  每週週未(星期六)日期  ex. 2015/12/05 ,2015/12/12....
        /// 傳回 DataType=YearWeek   ex. 41,42 .....
        /// </summary>
        /// <param name="dt">日期</param>
        /// <param name="MonthVal">要傳回幾個月的資料</param>
        /// <param name="DataType">Date 傳回週未日期 ,YearWeek 傳回年週</param>
        /// <returns></returns>
        private static List<string> GetArrWeekToArr(DateTime dt, int MonthVal, string DataType)
        {
            List<string> ArrWeek = new List<string>();

            DateTime StartDate = GetDateWhatoMonth(dt, MonthVal, 6);

            int MaxWeeK = GetWeekInBTime(StartDate, dt);

            int X = 0;

            for (int i = 0; i <= MaxWeeK - 1; i++)
            {
                if (DataType.ToUpper() == "DATE")
                {
                    X++;
                    string yyyyMMdd = StartDate.AddDays((X * 7)).Date.ToString("yyyy/MM/dd");

                    ArrWeek.Add(yyyyMMdd);
                }
                else if (DataType.ToUpper() == "YEARWEEK")
                {
                    ArrWeek.Add(i.ToString());
                }
            }

            return ArrWeek;
        }
    }
}