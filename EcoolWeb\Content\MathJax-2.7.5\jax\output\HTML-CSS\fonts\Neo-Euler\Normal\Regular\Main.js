/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Normal/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Normal={directory:"Normal/Regular",family:"NeoEulerMathJax_Normal",testString:"\u00A0\u210E\uD835\uDC00\uD835\uDC01\uD835\uDC02\uD835\uDC03\uD835\uDC04\uD835\uDC05\uD835\uDC06\uD835\uDC07\uD835\uDC08\uD835\uDC09\uD835\uDC0A\uD835\uDC0B\uD835\uDC0C",32:[0,0,333,0,0],160:[0,0,333,0,0],8462:[681,10,621,27,637],119808:[687,14,829,3,821],119809:[703,2,726,40,641],119810:[711,18,789,86,751],119811:[698,1,899,44,828],119812:[709,8,640,11,605],119813:[706,12,525,12,494],119814:[709,14,845,90,759],119815:[711,7,856,40,824],119816:[696,-1,461,48,387],119817:[705,192,457,45,412],119818:[702,12,747,46,728],119819:[702,5,620,42,588],119820:[701,16,1157,87,1125],119821:[713,3,922,52,792],119822:[720,15,881,72,822],119823:[699,6,664,49,617],119824:[714,197,918,86,835],119825:[701,10,714,48,664],119826:[723,14,654,81,591],119827:[714,11,543,22,628],119828:[717,10,858,28,837],119829:[712,5,745,27,807],119830:[701,16,1125,14,1178],119831:[705,13,737,29,705],119832:[707,4,583,28,682],119833:[709,11,733,29,703],119834:[498,15,664,98,666],119835:[705,14,673,29,589],119836:[496,10,549,90,531],119837:[699,11,673,84,668],119838:[495,16,613,87,558],119839:[686,10,430,23,455],119840:[495,242,658,86,545],119841:[696,8,664,30,664],119842:[687,13,367,10,365],119843:[687,246,363,-18,259],119844:[698,6,611,30,613],119845:[697,7,368,28,368],119846:[495,9,988,5,984],119847:[497,10,670,0,671],119848:[486,11,672,79,590],119849:[488,247,662,18,593],119850:[538,247,671,102,557],119851:[489,6,496,14,479],119852:[494,15,498,35,425],119853:[624,11,437,22,410],119854:[500,8,670,2,667],119855:[490,12,546,-26,518],119856:[496,13,919,-8,890],119857:[491,12,550,-23,531],119858:[491,232,670,11,552],119859:[492,14,544,17,524],120488:[687,14,829,3,821],120489:[703,2,726,40,641],120490:[692,9,536,54,579],120491:[699,7,782,23,751],120492:[709,8,640,11,605],120493:[709,11,733,29,703],120494:[711,7,856,40,824],120495:[699,14,968,77,872],120496:[696,-1,461,48,387],120497:[702,12,747,46,728],120498:[694,13,847,18,816],120499:[701,16,1157,87,1125],120500:[713,3,922,52,792],120501:[700,14,652,26,623],120502:[720,15,881,72,822],120503:[690,14,844,29,813],120504:[699,6,664,49,617],120506:[699,1,712,28,659],120507:[714,11,543,22,628],120508:[707,6,794,29,763],120509:[685,0,973,78,884],120510:[705,13,737,29,705],120511:[703,1,781,-30,770],120512:[700,5,959,26,928],120513:[699,7,782,23,751],120514:[476,18,714,90,722],120515:[676,194,700,107,615],120516:[475,201,645,-3,625],120517:[672,11,584,81,505],120518:[472,16,532,105,498],120519:[674,129,523,79,511],120520:[470,197,651,11,540],120521:[677,14,648,84,563],120522:[469,11,354,114,348],120523:[471,12,583,111,562],120524:[679,8,596,29,565],120525:[474,196,646,95,641],120526:[473,6,638,26,603],120527:[689,126,587,73,576],120528:[486,11,672,79,590],120529:[523,4,664,18,685],120530:[474,196,610,104,566],120532:[475,11,700,86,681],120533:[497,7,580,31,584],120534:[472,2,621,3,570],120535:[472,201,816,85,737],120536:[470,197,587,-9,598],120537:[672,196,821,-3,742],120538:[499,9,972,78,887],120539:[696,18,643,87,558],120540:[471,7,587,84,539],120541:[676,8,639,0,612],120543:[672,196,873,86,790],120545:[547,11,932,84,865],120782:[716,15,550,40,513],120783:[715,9,550,70,357],120784:[715,-5,550,15,529],120785:[708,8,550,25,496],120786:[708,12,550,2,519],120787:[694,12,550,18,495],120788:[704,12,550,44,520],120789:[696,15,550,50,547],120790:[720,5,550,42,505],120791:[707,10,550,48,514]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Normal"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/Regular/Main.js"]);
