﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP.EF;
using EcoolWeb.ViewModels;
using EcoolWeb.Models;
using ECOOL_APP;
using System.Data.Entity;
using com.ecool.service;
using EcoolWeb.CustomAttribute;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWA006Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private UserProfile user = UserProfileHelper.Get();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();

        // GET: AWA004
        public ActionResult Query(AWA006QueryViewModel model)
        {
            if (model == null) model = new AWA006QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);
            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);

            IQueryable<VAWA005> VAWA005List = db.VAWA005.AsQueryable();
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                int AWARD_NO;

                if (Int32.TryParse(model.whereKeyword, out AWARD_NO))
                {
                    if (AWARD_NO > 0)
                    {
                        VAWA005List = VAWA005List.Where(a => a.AWARD_NO == AWARD_NO);
                    }
                    else
                    {
                        VAWA005List = VAWA005List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                                   || a.NAME.Contains(model.whereKeyword.Trim())
                                                   || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                              );
                    }
                }
                else
                {
                    VAWA005List = VAWA005List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                                    || a.NAME.Contains(model.whereKeyword.Trim())
                                                    || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                               );
                }
            }
            if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
            {
                VAWA005List = VAWA005List.Where(a => a.NAME.Contains(model.whereSNAME));
            }
            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                VAWA005List = VAWA005List.Where(a => a.CLASS_NO == model.whereCLASS_NO);
            }
            if (string.IsNullOrWhiteSpace(model.whereAWARD_NAME) == false)
            {
                VAWA005List = VAWA005List.Where(a => a.AWARD_NAME == model.whereAWARD_NAME);
            }

            if (string.IsNullOrWhiteSpace(SchoolNO) == false && SchoolNO != "ALL")
            {
                VAWA005List = VAWA005List.Where(a => a.SCHOOL_NO == SchoolNO);
            }

            //  -- 排序開始 --
            Func<VAWA005, object> sortExpression;
            switch (model.OrderColumn)
            {
                case "SHORT_NAME":
                    sortExpression = (q => q.SHORT_NAME);
                    break;

                case "COST_CASH":
                    sortExpression = (q => q.COST_CASH);
                    break;

                case "TRANS_DATE":
                    sortExpression = (q => q.TRANS_DATE);
                    break;

                default:
                    sortExpression = (q => q.TRANS_NO);
                    break;
            }

            if (model.SortBy == "ASC")
            {
                VAWA005List = VAWA005List.OrderBy(sortExpression).AsQueryable();
            }
            else
            {
                VAWA005List = VAWA005List.OrderByDescending(sortExpression).AsQueryable();
            }

            //  -- 排序結束 --

            model.VAWA005List = VAWA005List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            return View(model);
        }

        public ActionResult ModifyQuery(AWA004_006_ModifyQueryViewModel model, FormCollection FC)
        {
            // 整合Code: 移轉至AWA004Controller做return View選擇控制
            model.RoleName = "Teacher";
            return RedirectToAction("ModifyQuery", "AWA004", new { model.RoleName });
        }

        [Obsolete("已整合至AWA004/Modify")]
        [HttpPost]
        public ActionResult ExportExcel(FormCollection form)
        {
            string strHtml = form["hHtml"];
            strHtml = HttpUtility.HtmlDecode(strHtml);//Html解碼
            byte[] b = System.Text.Encoding.Default.GetBytes(strHtml);//字串轉byte陣列
            return File(b, "application/vnd.ms-excel", "獎品兌換清單.xls");//輸出檔案給Client端
        }

        [Obsolete("已整合至AWA004/Modify")]
        [HttpPost]
        public ActionResult Modify(FormCollection form)
        {
            for (int i = 0; i < 20; i++)
            {
                if (form["[" + i + "].chkTRANS_NO"] == "on")
                {
                    if (form["[" + i + "].TRANS_NO"] != null || form["[" + i + "].TRANS_NO"] != string.Empty)
                    {
                        AWAT03 AW03 = new AWAT03();
                        string tmpTRANS_NO = form["[" + i + "].TRANS_NO"].Split(',')[0];
                        int iTRANS_NO = Convert.ToInt32(tmpTRANS_NO);
                        AW03 = db.AWAT03.Where(p => p.TRANS_NO == iTRANS_NO).FirstOrDefault();
                        AW03.TRANS_STATUS = (byte)1;
                        db.Entry(AW03).State = EntityState.Modified;
                        db.SaveChanges();
                    }
                }
            }
            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);
            return RedirectToAction("ModifyQuery", "AWA006", form);
        }

        [Obsolete("已整合至AWA004/CancelTrans")]
        [HttpPost]
        public ActionResult CancelTrans(FormCollection form)
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (form["hidSelectTRANS_NO"] != null)
            {
                //查詢交易紀錄
                int TranLogNO = Convert.ToInt32(form["hidSelectTRANS_NO"]);

                //更新交易紀錄-->取消 (9)
                AWAT03 TranLog = db.AWAT03.Where(p => p.TRANS_NO == TranLogNO).FirstOrDefault();

                if (TranLog.TRANS_STATUS != 9)
                {
                    TranLog.TRANS_STATUS = (byte)9;
                    TranLog.CANCEL_DATE = DateTime.Now.Date;
                    db.Entry(TranLog).State = EntityState.Modified;

                    //調整商品庫存量
                    AWAT09 BackAward = db.AWAT09.Where(a => a.AWARD_NO == TranLog.AWARD_NO).FirstOrDefault();

                    AWAT09_HIS HIS = db.AWAT09_HIS.Create();
                    HIS.AWARD_NO = BackAward.AWARD_NO;
                    HIS.SCHOOL_NO = BackAward.SCHOOL_NO;
                    HIS.AWARD_TYPE = BackAward.AWARD_TYPE;
                    HIS.AWARD_NAME = BackAward.AWARD_NAME;
                    HIS.COST_CASH = BackAward.COST_CASH;
                    HIS.QTY_STORAGE = BackAward.QTY_STORAGE;
                    HIS.SDATETIME = BackAward.SDATETIME;
                    HIS.EDATETIME = BackAward.EDATETIME;
                    HIS.DESCRIPTION = BackAward.DESCRIPTION;
                    HIS.IMG_FILE = BackAward.IMG_FILE;
                    HIS.IMG2_FILE = BackAward.IMG2_FILE;
                    HIS.AWARD_STATUS = BackAward.AWARD_STATUS;
                    HIS.HOT_YN = BackAward.HOT_YN;
                    HIS.BUY_PERSON_YN = BackAward.BUY_PERSON_YN;
                    HIS.CHG_DATE = DateTime.Now;
                    HIS.QTY_LIMIT = BackAward.QTY_LIMIT;
                    HIS.SHOW_DESCRIPTION_YN = BackAward.SHOW_DESCRIPTION_YN;
                    db.AWAT09_HIS.Add(HIS);

                    BackAward.QTY_STORAGE = BackAward.QTY_STORAGE.Value + 1;// +TranLog.TRANS_QTY.Value;
                    BackAward.QTY_TRANS = BackAward.QTY_TRANS.Value - 1;// TranLog.TRANS_QTY.Value;

                    CashHelper.TeachAddCash(user, TranLog.TRANS_CASH.Value, TranLog.SCHOOL_NO, TranLog.USER_NO, "AWAT09", TranLog.TRANS_NO.ToString(), "取消獎品兌換", false, null, ref db);

                    db.SaveChanges();
                    TempData["StatusMessage"] = "完成取消獎品兌換";

                    UserProfile.RefreshCashInfo(user, ref db);
                    UserProfileHelper.Set(user);
                }
                else
                {
                    TempData["StatusMessage"] = "重覆取消獎品";
                }
            }

            return RedirectToAction("Query", "AWA006");
        }
    }
}