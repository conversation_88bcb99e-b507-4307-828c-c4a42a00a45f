/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/BoxDrawing.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{9472:[340,-267,708,-11,719],9474:[910,303,696,312,385],9484:[340,303,708,318,720],9488:[340,303,708,-11,390],9492:[910,-267,708,318,720],9496:[910,-267,708,-11,390],9500:[910,303,708,318,720],9508:[910,303,708,-11,390],9516:[340,303,708,-11,719],9524:[910,-267,708,-11,719],9532:[910,303,708,-11,719],9552:[433,-174,708,-11,719],9553:[910,303,708,225,484],9554:[433,303,708,318,720],9555:[340,303,708,225,720],9556:[433,303,708,225,719],9557:[433,303,708,-11,390],9558:[340,303,708,-11,483],9559:[433,303,708,-11,483],9560:[910,-174,708,318,720],9561:[910,-267,708,225,720],9562:[910,-174,708,225,719],9563:[910,-174,708,-11,390],9564:[910,-267,708,-11,483],9565:[910,-174,708,-11,483],9566:[910,303,708,318,720],9567:[910,303,708,225,720],9568:[910,303,708,225,720],9569:[910,303,708,-11,390],9570:[910,303,708,-11,483],9571:[910,303,708,-11,483],9572:[433,303,708,-11,719],9573:[340,303,708,-11,719],9574:[433,303,708,-11,719],9575:[910,-174,708,-11,719],9576:[910,-267,708,-11,719],9577:[910,-174,708,-11,719],9578:[910,303,708,-11,719],9579:[910,303,708,-11,719],9580:[910,303,708,-11,719]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/BoxDrawing.js");
