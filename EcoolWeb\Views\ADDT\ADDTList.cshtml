﻿@model EcoolWeb.ViewModels.ADDTListViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "閱讀認證排行榜";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string HidStyle = "";
    int DataCount = 0;
    int RowNumber = 0;
    bool IsStudent = false;
    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student) { IsStudent = true; }
    }

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    if (Model.isCarousel || Model.IsPrint)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        HidStyle = "display:none";
    }

}

<script type='text/javascript' src='~/Scripts/jquery.simplemodal.js'></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@if (Model.isCarousel)
{
    <style type="text/css">
        .bigger {
            font-size: 30px;
        }

        .Carousel_hide {
            display: none;
        }

        .table-ecool thead > tr > th, .table-ecool thead > tr > td {
            font-size: 30px;
        }
    </style>
}
<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }

    a[href]:after {
        content: none !important;
    }
</style>

@section scripts{

    <script>
        var targetFormID = '#ADDT';

           window.onload = function () {
                if ($('#@Html.IdFor(m=>m.IsPrint)').val() == "true" && $('#@Html.IdFor(m=>m.IsToExcel)').val() != "true"  ) {
                    window.print()
               }

               initDatepicker();
         }

        //$(function () {
        //    var MonthDEF = "";
        //    MonthDEF = $(".active").attr("Mouth");
        //    if (MonthDEF == "true") {
        //        $("#Sdate").attr("hidden", "hidden");
        //    }
        //        $('#ButtonExcel').click(function () {
        //            var blob = new Blob([document.getElementById('tbData').innerHTML], {
        //                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        //            });
        //            var strFile = "Report.xls";
        //            saveAs(blob, strFile);
        //            return false;
        //        });
        // });
            function exportExcel() {

                $("#ADDT").attr("enctype", "multipart/form-data");
                $("#ADDT").attr("action", "@Url.Action("ExportExcelADDTList", (string)ViewBag.BRE_NO)");
                $("#ADDT").submit();

        }
        function initDatepicker() {
               var opt = {
                    showMonthAfterYear: true,
                    format: moment().format('YYYY-MM-DD'),
                    showSecond: true,
                    showButtonPanel: true,
                    showTime: true,
                    beforeShow: function () {
                        setTimeout(
                            function () {
                                $('#ui-datepicker-div').css("z-index", 15);
                            }, 100
                        );
                    },
                    onSelect: function (dateText, inst) {
                        $('#' + inst.id).attr('value', dateText);
                    }
                };
                $("#@Html.IdFor(m => m.WhereUP_DATE_START)").datetimepicker(opt);
                $("#@Html.IdFor(m => m.WhereUP_DATE_END)").datetimepicker(opt);
        }

        function PrintBooK()
        {
            $('#@Html.IdFor(m=>m.IsPrint)').val(true)
            $(targetFormID).attr('action','@Url.Action("ADDTList", "ADDT")')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            $('#@Html.IdFor(m=>m.IsPrint)').val(false)
        }

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

           function doMonthTop(val) {
               $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);
               $("#Sdate").attr("hidden", "hidden");
                FunPageProc(1)
            }

            function todoClear() {
                $("#doClear").val(true);
                $("#whereKeyword").val('');
                $("#OrdercColumn").val('');
                $("#whereCLASS_NO").val('');
                $("#whereGrade").val('');
                $("#WhereUP_DATE_START").val('');
                $("#WhereUP_DATE_END").val('');

                $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(false);
                FunPageProc(1)
            }

            @*function doMonthTop(val) {
                $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);
                FunPageProc(1)
            }*@
    </script>

}

@if (AppMode == false && Model.isCarousel == false)
{
    @Html.Partial("_Title_Secondary")
}

@using (Html.BeginForm("ADDTList", "ADDT", FormMethod.Post, new { name = "form1", id = "ADDT" }))
{
    <div>
        @*說明視窗*@
        <div id='container' style="@HidStyle">
            @{
                Html.RenderAction("_PageMenu", new { NowAction = "ADDTList" });
            }
        </div>

        @Html.HiddenFor(m => m.OrdercColumn)
        @Html.HiddenFor(m => m.Page)
        @Html.HiddenFor(m => m.WhereIsMonthTop)
        @Html.HiddenFor(m => m.IsPrint)
        @Html.HiddenFor(m => m.IsToExcel)

        <div class="form-inline" style="@HidStyle" id="Q_Div">
            <div class="form-inline" role="form">
                <div class="form-group">
                    <label class="control-label">學號/姓名</label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                </div>
                <div class="form-group">
                    <label class="control-label">年級</label>
                </div>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                </div>
                <div class="form-group">
                    <label class="control-label">班級</label>
                </div>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                </div>
                <div class="form-group">
                    <label class="control-label">座號</label>
                </div>
                <div class="form-group">

                    @Html.EditorFor(m => m.whereSeat_NO, new { htmlAttributes = new { @class = "form-control input-sm", @style = "width:70px" } })
                </div>
                <br />
                <div id="Sdate">
                    <div class="form-group">
                        <label class="control-label">授權日期(起)</label>
                    </div>

                    <div class="form-group">
                        @Html.EditorFor(m => m.WhereUP_DATE_START, new { htmlAttributes = new { id = "WhereUP_DATE_START", @class = "form-control input-sm", autocomplete = "off" } })
                    </div>
                    <div class="form-group">
                        <label class="control-label">授權日期(迄)</label>
                    </div>
                    <div class="form-group">
                        @Html.EditorFor(m => m.WhereUP_DATE_END, new { htmlAttributes = new { id = "WhereUP_DATE_END", @class = "form-control input-sm", autocomplete = "off" } })
                    </div>
                </div>
                <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />

                @if (user != null)
                {
                    if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
                    {

                        if (!Model.IsPrint)
                        {
                            <button id="ButtonExcel" class="btn-yellow btn btn-sm cScreen" style="float:right" onclick="exportExcel();">匯出excel</button>
                            <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right;margin-right:5px">我要列印</button>
                        }
                        else
                        {
                            <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right">我要列印</button>
                        }

                    }
                }
            </div>
        </div>
    </div>

    <div class="form-inline cScreen" style="text-align:right;@HidStyle">
        <br />
        <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active":"")" type="button" onclick="todoClear();doMonthTop('false');" Mouth="false">全部</button>
        <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active":"")" type="button" onclick="todoClear();doMonthTop('true');" Mouth="true">月排行榜</button>
    </div>

    if (!Model.IsPrint)
    {
        <img src="~/Content/img/web-bar2-revise-14.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    }

    <div class="@(Model.IsPrint ? "":"table-responsive")">
        <div class="text-center" id="tbData">
            <table class="@(Model.IsPrint ? "table table-bordered" : "table-ecool table-92Per table-hover table-ecool-reader")">
                <thead>
                    <tr>
                        <th>序號</th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                            班級
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                            座號
                            <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                            學號
                            <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                            姓名
                            <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        @if (Model.WhereIsMonthTop == true)
                        {
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('BOOK_QTY');">
                                本月閱讀冊數
                                <img id="BOOK_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                            </th>
                        }
                        else
                        {
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('BOOK_QTY');">
                                閱讀冊數
                                <img id="BOOK_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                            </th>

                            <th style="text-align: center;cursor:pointer; vertical-align: middle;" onclick="doSort('LEVEL_DESC');">
                                認證等級
                                <img id="LEVEL_DESC" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                            </th>
                            <th class=" Carousel_hide App_hide" style="text-align: center;cursor:pointer; vertical-align: middle;" onclick="doSort('UP_DATE');">
                                授權日期
                                <img id="UP_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                            </th>
                            if (AppMode == false)
                            {
                                <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('ShareCount');">
                                    被推薦數
                                    <img id="ShareCount" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                                </th>
                            }
                        }
                    </tr>
                </thead>
                <tbody>

                    @if (Model.WhereIsMonthTop == true)
                    {
                        foreach (var item in Model.MonthTopList)
                        {
                            DataCount++;
                            RowNumber = Model.MonthTopList.PageSize * (Model.MonthTopList.PageNumber - 1) + DataCount;
                            if (Model.isCarousel && DataCount > 5) { break; }

                            <tr>
                                <td>@RowNumber</td>

                                <td class="bigger" style="text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td class="Carousel_hide">
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                                <td class="Carousel_hide">
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                </td>
                                <td class="bigger">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                <td class="bigger">
                                    @Html.DisplayFor(modelItem => item.BOOK_QTY)
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        foreach (var item in Model.ADDT0809List)
                        {
                            DataCount++;

                            RowNumber = Model.ADDT0809List.PageSize * (Model.ADDT0809List.PageNumber - 1) + DataCount;
                            if (Model.isCarousel && DataCount > 5) { break; }
                            <tr>
                                <td>@RowNumber</td>
                                <td class="bigger" style="text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td class="Carousel_hide">
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                                <td class="Carousel_hide">
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                </td>
                                <td class="bigger">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                <td class="bigger">
                                    @Html.DisplayFor(modelItem => item.BOOK_QTY)
                                </td>

                                <td class="bigger">
                                    @if (string.IsNullOrEmpty(item.LEVEL_DESC) == false)
                                    {
                                        @Html.ActionLink(item.LEVEL_DESC, "PrizeList"
                                       , new
                                       {
                                           USER_NO = item.USER_NO,
                                           whereKeyword = Model.whereKeyword,
                                           OrdercColumn = Model.OrdercColumn,
                                           whereCLASS_NO = Model.whereCLASS_NO,
                                           whereGrade = Model.whereGrade,
                                           Page = Model.Page
                                       }, new { @class = "btn-table-link text-nowrap" })
                                    }
                                </td>

                                <td class="Carousel_hide App_hide" style="text-align: left;white-space:normal">
                                    @Html.DisplayFor(modelItem => item.UP_DATE, "ShortDateTime")
                                </td>
                                @if (AppMode == false)
                                {
                                    <td class="Carousel_hide">
                                        @Html.DisplayFor(modelItem => item.ShareCount)
                                    </td>
                                }
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
    if (Model.isCarousel == false)
    {
        <div>
            @if (Model.WhereIsMonthTop == true)
            {
                @Html.Pager(Model.MonthTopList.PageSize, Model.MonthTopList.PageNumber, Model.MonthTopList.TotalItemCount).Options(o => o
                   .DisplayTemplate("BootstrapPagination")
                  .MaxNrOfPages(5)
                  .SetPreviousPageText("上頁")
                  .SetNextPageText("下頁"))
            }
            else
            {
                @Html.Pager(Model.ADDT0809List.PageSize, Model.ADDT0809List.PageNumber, Model.ADDT0809List.TotalItemCount).Options(o => o
                   .DisplayTemplate("BootstrapPagination")
                  .MaxNrOfPages(5)
                  .SetPreviousPageText("上頁")
                  .SetNextPageText("下頁"))
            }
        </div>

        <div style="width:100%;max-width:600px">
            @{string Explain = ViewBag.BookExplain;}
            @Html.Raw(HttpUtility.HtmlDecode(@Explain))
        </div>
    }
}