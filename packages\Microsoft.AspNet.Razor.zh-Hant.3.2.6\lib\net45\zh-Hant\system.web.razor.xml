﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Razor</name>
  </assembly>
  <members>
    <member name="T:System.Web.Razor.CSharpRazorCodeLanguage">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示根據 C# 語法寫成的 Razor 程式碼語言。</summary>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.CSharpRazorCodeLanguage" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Razor.CSharpRazorCodeLanguage.CodeDomProviderType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼提供者的類型。</summary>
      <returns>程式碼提供者的類型。</returns>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。根據 C# 程式語言建立新的 Razor 程式碼產生器。</summary>
      <returns>根據 C# 程式語言新建立的 Razor 程式碼產生器。</returns>
      <param name="className">產生的程式碼類別名稱。</param>
      <param name="rootNamespaceName">已產生程式碼的根命名空間的名稱。</param>
      <param name="sourceFileName">原始程式碼檔案的名稱。</param>
      <param name="host">Razor 引擎主機。</param>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.CreateCodeParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 C# 程式語言的新程式碼剖析器。</summary>
      <returns>新阵立的 C# 程式語言程式碼剖析器。</returns>
    </member>
    <member name="P:System.Web.Razor.CSharpRazorCodeLanguage.LanguageName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 C# 程式語言的名稱。</summary>
      <returns>C# 程式語言的名稱。值為‘csharp’。</returns>
    </member>
    <member name="T:System.Web.Razor.DocumentParseCompleteEventArgs">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.DocumentParseCompleteEventArgs.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.GeneratorResults">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.SourceChange">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.TreeStructureChanged">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.GeneratorResults">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示程式碼產生的結果。</summary>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Boolean,System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError},System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.GeneratorResults" /> 類別的新執行個體。</summary>
      <param name="success">如果程式碼產生成功，則為 true，否則為 false。</param>
      <param name="document">文件。</param>
      <param name="parserErrors">剖析器錯誤。</param>
      <param name="generatedCode">產生的程式碼。</param>
      <param name="designTimeLineMappings">設計時間產生的程式碼對應字典。</param>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError},System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.GeneratorResults" /> 類別的新執行個體。</summary>
      <param name="document">文件。</param>
      <param name="parserErrors">剖析器錯誤。</param>
      <param name="generatedCode">產生的程式碼。</param>
      <param name="designTimeLineMappings">設計時間產生的程式碼對應字典。</param>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Web.Razor.ParserResults,System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.GeneratorResults" /> 類別的新執行個體。</summary>
      <param name="parserResults">剖析器結果。</param>
      <param name="generatedCode">產生的程式碼。</param>
      <param name="designTimeLineMappings">設計時間產生的程式碼對應字典。</param>
    </member>
    <member name="P:System.Web.Razor.GeneratorResults.DesignTimeLineMappings">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定設計時間產生的程式碼對應字典。</summary>
      <returns>設計時間產生的程式碼對應字典。</returns>
    </member>
    <member name="P:System.Web.Razor.GeneratorResults.GeneratedCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定產生的程式碼。</summary>
      <returns>產生的程式碼。</returns>
    </member>
    <member name="T:System.Web.Razor.ParserResults">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示剖析 Razor 文件的結果。</summary>
    </member>
    <member name="M:System.Web.Razor.ParserResults.#ctor(System.Boolean,System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.ParserResults" /> 類別的新執行個體。</summary>
      <param name="success">如果剖析成功，則為 true，否則為 false。</param>
      <param name="document">文件語法樹狀目錄中的根節點。</param>
      <param name="errors">剖析時發生的錯誤清單。</param>
    </member>
    <member name="M:System.Web.Razor.ParserResults.#ctor(System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.ParserResults" /> 類別的新執行個體。</summary>
      <param name="document">文件語法樹狀目錄中的根節點。</param>
      <param name="parserErrors">剖析時發生的錯誤清單。</param>
    </member>
    <member name="P:System.Web.Razor.ParserResults.Document">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定文件語法樹狀目錄中的根節點。</summary>
      <returns>文件語法樹狀目錄中的根節點。</returns>
    </member>
    <member name="P:System.Web.Razor.ParserResults.ParserErrors">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定剖析時發生的錯誤清單。</summary>
      <returns>剖析時發生的錯誤清單。</returns>
    </member>
    <member name="P:System.Web.Razor.ParserResults.Success">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定表示剖析是否成功的值。</summary>
      <returns>如果剖析成功，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Razor.PartialParseResult">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Accepted">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.AutoCompleteBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Provisional">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Rejected">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.SpanContextChanged">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.RazorCodeLanguage">
      <summary>表示所有 Razor 程式碼語言的基本。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.#ctor">
      <summary>初始化 <see cref="T:System.Web.Razor.RazorCodeLanguage" /> 類別的新執行個體。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.CodeDomProviderType">
      <summary>取得 CodeDOM 提供者的類型。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <returns>CodeDOM 提供者的類型。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>建立 Razor 程式碼語言的程式碼產生器。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <returns>Razor 程式碼語言的程式碼產生器。</returns>
      <param name="className">類別名稱。</param>
      <param name="rootNamespaceName">根命名空間的名稱。</param>
      <param name="sourceFileName">原始程式檔的名稱。</param>
      <param name="host">Razor 引擎主機。</param>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.CreateCodeParser">
      <summary>建立 Razor 程式碼語言的程式碼剖析器。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <returns>Razor 程式碼語言的程式碼剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.GetLanguageByExtension(System.String)">
      <summary>使用指定的副檔名取得 Razor 程式碼語言。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <returns>Razor 程式碼的語言。</returns>
      <param name="fileExtension">檔案副檔名。</param>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.LanguageName">
      <summary>取得目前 Razor 程式碼的語言名稱，也就是 “csharp” 或 “vb”。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <returns>目前 Razor 程式碼的語言名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.Languages">
      <summary>取得 Razor 程式碼支援的語言清單。這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <returns>Razor 程式碼支援的語言清單。</returns>
    </member>
    <member name="T:System.Web.Razor.RazorDirectiveAttribute">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 指示詞的屬性。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.#ctor(System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.RazorDirectiveAttribute" /> 類別的新執行個體。</summary>
      <param name="name">屬性的名稱。</param>
      <param name="value">屬性的值。</param>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷此執行個體是否等於指定的物件。</summary>
      <returns>如果物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.Name">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定屬性的名稱。</summary>
      <returns>屬性的名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.TypeId">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得屬性的唯一類型 ID。</summary>
      <returns>屬性的唯一類型 ID。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.Value">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定屬性值。</summary>
      <returns>屬性的值。</returns>
    </member>
    <member name="T:System.Web.Razor.RazorEditorParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。編輯人員會使用剖析器來避免每次文字變更時重新剖析整份文件。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.#ctor(System.Web.Razor.RazorEngineHost,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建構編輯人員剖析器。</summary>
      <param name="host">
        <see cref="T:System.Web.Razor.RazorEngineHost" /> 其定義產生的程式碼所在的環境。</param>
      <param name="sourceFileName">在行程式中要使用的實體路徑。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.CheckForStructureChanges(System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷變更是否會導致文件的結構變更。如果不會，則套用至現有的樹狀目錄。如果發生結構變更，會自動開始重新剖析。</summary>
      <returns>
        <see cref="T:System.Web.Razor.PartialParseResult" /> 值，表示增量剖析的結果。</returns>
      <param name="change">要套用到剖析樹狀目錄的變更。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.CurrentParseTree">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前剖析樹狀目錄。</summary>
      <returns>剖析樹狀目錄。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.Dispose">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將 <see cref="T:System.Web.Razor.RazorEditorParser" /> 目前的執行個體所使用的資源全部釋出。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.Dispose(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。釋放 <see cref="T:System.Web.Razor.RazorEditorParser" /> 類別所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="E:System.Web.Razor.RazorEditorParser.DocumentParseComplete">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。當文件的完整重新剖析完成時，會發送事件。</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.FileName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定要剖析的文件檔案名稱。</summary>
      <returns>要剖析的文件檔案名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.GetAutoCompleteString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。擷取自動完成字串。</summary>
      <returns>自動完成字串。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.Host">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定剖析的主機。</summary>
      <returns>剖析的主機。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.LastResultProvisional">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定一值，表示下次的部份剖析所是否可以條件性地接受上次的剖析結果。</summary>
      <returns>如果下次的部份剖析所可以條件性地接受上次的剖析結果，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Razor.RazorEngineHost">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 引擎主機產生的程式碼。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.RazorEngineHost" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor(System.Web.Razor.RazorCodeLanguage)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.RazorEngineHost" /> 類別的新執行個體。</summary>
      <param name="codeLanguage">指定的程式碼語言。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor(System.Web.Razor.RazorCodeLanguage,System.Func{System.Web.Razor.Parser.ParserBase})">
      <summary>初始化 <see cref="T:System.Web.Razor.RazorEngineHost" /> 類別的新執行個體。</summary>
      <param name="codeLanguage">指定的程式碼語言。</param>
      <param name="markupParserFactory">標記剖析器 Factory。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.CodeLanguage">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼產生器支援的語言。</summary>
      <returns>程式碼產生器支援的語言。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.CreateMarkupParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用 <see cref="T:System.Web.Razor.RazorEngineHost" /> 指定的語言剖析器來建立標記剖析器。</summary>
      <returns>使用 <see cref="T:System.Web.Razor.RazorEngineHost" /> 指定的語言剖析器建立的標記剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateCodeGenerator(System.Web.Razor.Generator.RazorCodeGenerator)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回方法為語言特定的 Razor 程式碼產生器。</summary>
      <returns>方法為語言特定的 Razor 程式碼產生器。</returns>
      <param name="incomingCodeGenerator">C# 或 Visual Basic 程式碼產生器。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateCodeParser(System.Web.Razor.Parser.ParserBase)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用指定的語言剖析器，傳回方法為語言特定的 Razor 程式碼剖析器。</summary>
      <returns>使用指定的語言剖析器，方法為語言特定的 Razor 程式碼剖析器。</returns>
      <param name="incomingCodeParser">C# 或 Visual Basic 程式碼剖析器。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateMarkupParser(System.Web.Razor.Parser.ParserBase)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回使用指定語言剖析器裝飾標記剖析器的方法。</summary>
      <returns>使用指定語言剖析器裝飾標記剖析器的方法。</returns>
      <param name="incomingMarkupParser">C# 或 Visual Basic 程式碼剖析器。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultBaseClass">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定主機的預設基本類型。</summary>
      <returns>主機的預設基本類型。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultClassName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定主機的預設類型名稱。</summary>
      <returns>主機的預設類型名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultNamespace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定主機的預設命名空間。</summary>
      <returns>主機的預設命名空間。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DesignTimeMode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定指出模式是否會為主機設計時間。</summary>
      <returns>如果模式會為主機設計時間，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.EnableInstrumentation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定啟用檢測設備的主機。</summary>
      <returns>啟用檢測設備的主機。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.GeneratedClassContext">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定主機的已產生類別內容。</summary>
      <returns>主機的已產生類別內容。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.InstrumentedSourceFilePath">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定主機的啟用檢測設備來源檔案路徑。</summary>
      <returns>主機的啟用檢測設備來源檔案路徑。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.IsIndentingWithTabs">
      <summary>取得或設定設計時間編輯器是使用定位點還是空格來縮排。</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.NamespaceImports">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得主機的命名空間匯入。</summary>
      <returns>主機的命名空間匯入。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.PostProcessGeneratedCode(System.CodeDom.CodeCompileUnit,System.CodeDom.CodeNamespace,System.CodeDom.CodeTypeDeclaration,System.CodeDom.CodeMemberMethod)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此方法為主機張貼所有已處理的已產生程式碼。</summary>
      <param name="codeCompileUnit">程式碼編譯單元。</param>
      <param name="generatedNamespace">產生的命名空間。</param>
      <param name="generatedClass">產生的類別。</param>
      <param name="executeMethod">執行方法。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.PostProcessGeneratedCode(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此方法為主機張貼所有已處理的已產生程式碼。</summary>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.StaticHelpers">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定主機的靜態 Helper。</summary>
      <returns>主機的靜態 Helper。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.TabSize">
      <summary>主控編輯器所使用的定位點大小 (當利用定位點縮排時)。</summary>
    </member>
    <member name="T:System.Web.Razor.RazorTemplateEngine">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示指向 Razor 範本引擎的項目點。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.#ctor(System.Web.Razor.RazorEngineHost)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.RazorTemplateEngine" /> 類別的新執行個體。</summary>
      <param name="host">主機。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.CreateCodeGenerator(System.String,System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立程式碼產生器。</summary>
      <returns>建立的 <see cref="T:System.Web.Razor.Generator.RazorCodeGenerator" />。</returns>
      <param name="className">產生的類別名稱。</param>
      <param name="rootNamespace">已產生類別中會存在命名空間。</param>
      <param name="sourceFileName">在行程式中要使用的檔案名稱。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.CreateParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 <see cref="T:System.Web.Razor.Parser.RazorParser" />。</summary>
      <returns>建立的 <see cref="T:System.Web.Razor.Parser.RazorParser" />。</returns>
    </member>
    <member name="F:System.Web.Razor.RazorTemplateEngine.DefaultClassName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示範本的預設類別名稱。</summary>
    </member>
    <member name="F:System.Web.Razor.RazorTemplateEngine.DefaultNamespace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示範本的預設命名空間。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.String,System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="className">產生的類別名稱，覆寫主棧中指定的任何項目。</param>
      <param name="rootNamespace">已產生類別中會存在命名空間。</param>
      <param name="sourceFileName">在行程式中要使用的檔案名稱。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="className">產生的類別名稱，覆寫主棧中指定的任何項目。</param>
      <param name="rootNamespace">已產生類別中會存在命名空間。</param>
      <param name="sourceFileName">在行程式中要使用的檔案名稱。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.String,System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="className">產生的類別名稱，覆寫主棧中指定的任何項目。</param>
      <param name="rootNamespace">已產生類別中會存在命名空間。</param>
      <param name="sourceFileName">在行程式中要使用的檔案名稱。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本，產生程式碼，並傳回建構的 CodeDOM 樹狀目錄。</summary>
      <returns>結果剖析 樹狀目錄 AND 產生的 generated Code DOM 樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="className">產生的類別名稱，覆寫主棧中指定的任何項目。</param>
      <param name="rootNamespace">已產生類別中會存在命名空間。</param>
      <param name="sourceFileName">在行程式中要使用的檔案名稱。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCodeCore(System.Web.Razor.Text.ITextDocument,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生程式碼核心。</summary>
      <returns>產生的核心結果。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="className">產生的類別名稱，覆寫主棧中指定的任何項目。</param>
      <param name="rootNamespace">已產生類別中會存在命名空間。</param>
      <param name="sourceFileName">在行程式中要使用的檔案名稱。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="P:System.Web.Razor.RazorTemplateEngine.Host">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 <see cref="T:System.Web.Razor.RazorEngineHost" />，其定義產生的範本程式碼所在的環境。</summary>
      <returns>
        <see cref="T:System.Web.Razor.RazorEngineHost" /> 其定義產生的範本程式碼所在的環境。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.IO.TextReader)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本並傳回其結果。</summary>
      <returns>結果剖析樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.IO.TextReader,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本並傳回其結果。</summary>
      <returns>結果剖析樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.Web.Razor.Text.ITextBuffer)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本並傳回其結果。</summary>
      <returns>結果剖析樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.Web.Razor.Text.ITextBuffer,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析由 TextBuffer 指定的範本並傳回其結果。</summary>
      <returns>結果剖析樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplateCore(System.Web.Razor.Text.ITextDocument,System.Nullable{System.Threading.CancellationToken})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析範本核心。</summary>
      <returns>結果剖析樹狀目錄。</returns>
      <param name="input">要剖析的輸入文字。</param>
      <param name="cancelToken">用來取消剖析器的權杖。</param>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示機器的狀態。</summary>
      <typeparam name="TReturn">一般類型傳回。</typeparam>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.StateMachine`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.CurrentState">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定機器目前的狀態。</summary>
      <returns>機器目前的狀態。</returns>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StartState">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得機器的開始狀態。</summary>
      <returns>機器的開始狀態。</returns>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stay">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在轉換期間保留在機器中。</summary>
      <returns>狀態機器的轉換。</returns>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stay(`0)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。透過指定的輸出在轉換期間保留在機器中。</summary>
      <returns>轉換輸出。</returns>
      <param name="output">輸出。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stop">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。轉換時停用機器。</summary>
      <returns>要停止的機器。</returns>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Transition(System.Web.Razor.StateMachine{`0}.State)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示該狀態的新轉換。</summary>
      <returns>該狀態的新轉換。</returns>
      <param name="newState">新狀態。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Transition(`0,System.Web.Razor.StateMachine{`0}.State)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示具有指定輸出的狀態之新轉換。</summary>
      <returns>具有指定輸出的狀態之新轉換。</returns>
      <param name="output">輸出。</param>
      <param name="newState">新狀態。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Turn">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。說明狀態的轉換處理。</summary>
      <returns>狀態的轉換處理。</returns>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1.State">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1.StateResult">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示狀態結果。</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.StateResult.#ctor(System.Web.Razor.StateMachine{`0}.State)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.StateMachine`1.StateResult" /> 類別的新執行個體。</summary>
      <param name="next">下一個輸出。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.StateResult.#ctor(`0,System.Web.Razor.StateMachine{`0}.State)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.StateMachine`1.StateResult" /> 類別的新執行個體。</summary>
      <param name="output">輸出。</param>
      <param name="next">下一個狀態。</param>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.HasOutput">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定表示狀態是否具有輸出的值。</summary>
      <returns>如果狀態具有輸出，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.Next">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定機器的下一個狀態。</summary>
      <returns>機器的下一個狀態。</returns>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.Output">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定輸出。</summary>
      <returns>
        <see cref="T:System.Web.Razor.StateMachine`1.State" /> 表示輸出。</returns>
    </member>
    <member name="T:System.Web.Razor.VBRazorCodeLanguage">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 VB Razor 程式碼的語言產生器和提供器。</summary>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.VBRazorCodeLanguage" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Razor.VBRazorCodeLanguage.CodeDomProviderType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 CodeDomProvider 的類型。</summary>
      <returns>CodeDomProvider 的類型。</returns>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立程式碼語言產生器。</summary>
      <returns>程式碼語言產生器。</returns>
      <param name="className">類別的名稱。</param>
      <param name="rootNamespaceName">根命名空間名稱。</param>
      <param name="sourceFileName">原始程式檔的名稱。</param>
      <param name="host">
        <see cref="T:System.Web.Razor.RazorEngineHost" />。</param>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.CreateCodeParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 <see cref="T:System.Web.Razor.Parser.ParserBase" /> 中建立程式碼剖析器。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.ParserBase" /> 中的程式碼剖析器。</returns>
    </member>
    <member name="P:System.Web.Razor.VBRazorCodeLanguage.LanguageName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得語言名稱。</summary>
      <returns>語言名稱。</returns>
    </member>
    <member name="T:System.Web.Razor.Editor.EditorHints">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.LayoutPage">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.None">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.VirtualPath">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Editor.EditResult">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示編輯器的編輯結果。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.EditResult.#ctor(System.Web.Razor.PartialParseResult,System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Editor.EditResult" /> 類別的新執行個體。</summary>
      <param name="result">部份剖析結果。</param>
      <param name="editedSpan">已編輯的範圍產生器。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.EditResult.EditedSpan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 的已編輯範圍。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 的已編輯的範圍。</returns>
    </member>
    <member name="P:System.Web.Razor.Editor.EditResult.Result">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定部份剖析結果。</summary>
      <returns>部份剖析結果。</returns>
    </member>
    <member name="T:System.Web.Razor.Editor.ImplicitExpressionEditHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供隱含運算的處理常式。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Collections.Generic.ISet{System.String},System.Boolean)">
      <summary>初始化 <see cref="T:System.Web.Razor.Editor.ImplicitExpressionEditHandler" /> 類別的新執行個體。</summary>
      <param name="tokenizer">Tokenizer。</param>
      <param name="keywords">關鍵字。</param>
      <param name="acceptTrailingDot">true 表示接受結尾的小數點，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.ImplicitExpressionEditHandler.AcceptTrailingDot">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定一值，指出運算式是否接受結尾的小數點。</summary>
      <returns>true 表示運算式接受結尾的小數點，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示剖析可以接受變更。</summary>
      <returns>部份剖析結果。</returns>
      <param name="target">目標。</param>
      <param name="normalizedChange">標準化變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為 true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。擷取此目前執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Editor.ImplicitExpressionEditHandler.Keywords">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定與運算式相關的關鍵字。</summary>
      <returns>與運算式相關聯的關鍵字。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Editor.SingleLineMarkupEditHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示此網頁的處理常式編輯器。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SingleLineMarkupEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>初始化 <see cref="T:System.Web.Razor.Editor.SingleLineMarkupEditHandler" /> 類別的新執行個體。</summary>
      <param name="tokenizer">Tokenizer 符號。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SingleLineMarkupEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>初始化 <see cref="T:System.Web.Razor.Editor.SingleLineMarkupEditHandler" /> 類別的新執行個體。</summary>
      <param name="tokenizer">Tokenizer 符號。</param>
      <param name="accepted">接受的字元。</param>
    </member>
    <member name="T:System.Web.Razor.Editor.SpanEditHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供處理範圍編輯的方法。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Editor.SpanEditHandler" /> 類別的新執行個體。</summary>
      <param name="tokenizer">用來將字串剖析為語彙基元的方法。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Editor.SpanEditHandler" /> 類別的新執行個體。</summary>
      <param name="tokenizer">用來將字串剖析為語彙基元的方法。</param>
      <param name="accepted">
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters" /> 列舉的其中一個值。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.AcceptedCharacters">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定指定接受字元的值。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters" /> 列舉的其中一個值。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將文字變更套用至範圍。</summary>
      <returns>套用作業的結果。</returns>
      <param name="target">要套用變更的範圍。</param>
      <param name="change">要套用的變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將文字變更套用至範圍。</summary>
      <returns>套用作業的結果。</returns>
      <param name="target">要套用變更的範圍。</param>
      <param name="change">要套用的變更。</param>
      <param name="force">true 表示接受結尾的小數點，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷範圍是否可接受指定的變更。</summary>
      <returns>如果範圍可以接受指定的變更，則為 true，否則為 false。</returns>
      <param name="target">要檢查的範圍。</param>
      <param name="normalizedChange">要套用的變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CreateDefault">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立新的預設範圍編輯處理常式。</summary>
      <returns>新建立的預設範圍編輯處理常式。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CreateDefault(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立新的預設範圍編輯處理常式。</summary>
      <returns>新建立的預設範圍編輯處理常式。</returns>
      <param name="tokenizer">用來將字串剖析為語彙基元的方法。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.EditorHints">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定編輯器提示。</summary>
      <returns>編輯器提示。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷此執行個體是否等於指定的物件。</summary>
      <returns>如果物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.GetOldText(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從範圍內容取得舊文字。</summary>
      <returns>範圍內容的舊文字。</returns>
      <param name="target">用來取得舊文字的範圍。</param>
      <param name="change">文字變更包含舊文字的位置。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsAtEndOfFirstLine(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定變更是否位於範圍內容第一行結尾。</summary>
      <returns>如果指定變更位於範圍內容第一行結尾則為 true，否則為 false。</returns>
      <param name="target">要檢查的範圍。</param>
      <param name="change">要檢查的變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsAtEndOfSpan(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定變更是否位於範圍結尾。</summary>
      <returns>如果指定變更位於範圍結尾，則為 true，否則為 false。</returns>
      <param name="target">要檢查的範圍。</param>
      <param name="change">要檢查的變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndDeletion(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定變更是否位於範圍內容結尾且需要刪除。</summary>
      <returns>如果指定變更位於範圍內容結尾且需要刪除，則為 true，否則為 false。</returns>
      <param name="target">要檢查的範圍。</param>
      <param name="change">要檢查的變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndInsertion(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定變更是否位於範圍內容結尾且需要插入。</summary>
      <returns>如果指定變更位於範圍內容結尾且需要插入，則為 true，否則為 false。</returns>
      <param name="target">要檢查的範圍。</param>
      <param name="change">要檢查的變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndReplace(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定變更是否位於範圍內容結尾且需要取代。</summary>
      <returns>如果指定變更位於範圍內容結尾且需要取代，則為 true，否則為 false。</returns>
      <param name="target">要檢查的範圍。</param>
      <param name="change">要檢查的變更。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.OwnsChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷範圍是否擁有指定的變更。</summary>
      <returns>如果範圍擁有指定的變更，則為 true，否則為 false。</returns>
      <param name="target">要檢查的範圍。</param>
      <param name="change">要檢查的變更。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.Tokenizer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定用來將字串剖析為語彙基元的方法。</summary>
      <returns>用來將字串剖析為語彙基元的方法。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回範圍編輯處理常式的字串表示法。</summary>
      <returns>範圍編輯處理常式的字串表示法。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.UpdateSpan(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用標準化變更更新範圍。</summary>
      <returns>指定目標的新範圍建立器。</returns>
      <param name="target">要更新的範圍。</param>
      <param name="normalizedChange">標準化變更。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.AddImportCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 的新增匯入程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.#ctor(System.String,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.AddImportCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="ns">字串命名空間。</param>
      <param name="namespaceKeywordLength">關鍵字命名空間的長度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩種物件執行個體是否相同。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用新增的匯入程式碼產生器產生具有指定參數的程式碼。</summary>
      <param name="target">目標範圍。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AddImportCodeGenerator.Namespace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得產生器的字串命名空間以新增匯入程式碼產生器。</summary>
      <returns>產生器的字串命名空間以新增匯入程式碼產生器。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AddImportCodeGenerator.NamespaceKeywordLength">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定程式碼產生器的關鍵字命名空間長度。</summary>
      <returns>程式碼產生器的關鍵字命名空間長度。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回表示目前物件的字串。</summary>
      <returns>表示目前物件的字串。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示區塊程式碼產生器的屬性。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.#ctor(System.String,System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.String})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="name">名稱。</param>
      <param name="prefix">前置詞字串。</param>
      <param name="suffix">後置字元字串。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用指定的參數產生程式碼以結束區塊。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用指定的參數產生程式碼以開始區塊。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此程式碼產生器的雜湊碼。</summary>
      <returns>此程式碼產生器的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Name">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator" /> 的字串名稱。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator" /> 的字串名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Prefix">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼產生器的前置詞。</summary>
      <returns>程式碼產生器的前置詞。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Suffix">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼產生器的後置字元。</summary>
      <returns>程式碼產生器的後置字元。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回表示目前物件的字串。</summary>
      <returns>表示目前物件的字串。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.BlockCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示此 Razor 語法的區塊程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.BlockCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生此 Razor 語法的區塊程式碼產生器結尾。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生此 Razor 語法的區塊程式碼產生器開頭。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回區塊程式碼產生器的雜湊碼。</summary>
      <returns>區塊程式碼產生器的雜湊碼。</returns>
    </member>
    <member name="F:System.Web.Razor.Generator.BlockCodeGenerator.Null">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生區塊程式碼產生器的 Null 值。</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示完成程式碼產生的事件引數。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.#ctor(System.String,System.String,System.CodeDom.CodeCompileUnit)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs" /> 類別的新執行個體。</summary>
      <param name="virtualPath">虛擬路徑字串。</param>
      <param name="physicalPath">實體路徑字串。</param>
      <param name="generatedCode">產生的程式碼編譯單元。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.GeneratedCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得產生的程式碼以完成事件引數。</summary>
      <returns>產生的程式碼以完成事件引數。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.PhysicalPath">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼產生的實體路徑。</summary>
      <returns>程式碼產生的實體路徑。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.VirtualPath">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼產生的虛擬路徑。</summary>
      <returns>程式碼產生的虛擬路徑。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.CodeGeneratorContext">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示程式碼產生器的內容。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddCodeMapping(System.Web.Razor.Text.SourceLocation,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在集合中新增新產生的程式碼對應。</summary>
      <returns>新增程式碼對應的集合索引。</returns>
      <param name="sourceLocation">已產生程式碼對應的來源位置。</param>
      <param name="generatedCodeStart">已產生程式碼對應的程式碼開始位置。</param>
      <param name="generatedCodeLength">已產生程式碼對應的長度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddContextCall(System.Web.Razor.Parser.SyntaxTree.Span,System.String,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在指定方法新增內容呼叫的程式碼陳述式。</summary>
      <param name="contentSpan">內容範圍。</param>
      <param name="methodName">要叫用的內容呼叫方法名稱。</param>
      <param name="isLiteral">true 表示方法參數為文字，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddDesignTimeHelperStatement(System.CodeDom.CodeSnippetStatement)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。新增在指定程式碼陳述式中插入 Razor 設計時間 Helper 方法的程式碼陳述式。</summary>
      <param name="statement">收到程式碼插入的程式碼陳述式。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddStatement(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在目標方法主體中新增指定的程式碼陳述式。</summary>
      <param name="generatedCode">要新增目標方法的程式碼陳述式。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddStatement(System.String,System.CodeDom.CodeLinePragma)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在目標方法主體中新增指定的程式碼陳述式。</summary>
      <param name="body">要新增目標方法的程式碼陳述式。</param>
      <param name="pragma">行程式。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在目前緩衝的陳述式中附加指定的片段。</summary>
      <param name="fragment">要新增的片段。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.String,System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在目前緩衝的陳述式中附加指定的片段。</summary>
      <param name="fragment">要新增的片段。</param>
      <param name="sourceSpan">
        <paramref name="fragment" /> 的來源範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在目前緩衝的陳述式中附加範圍內容。</summary>
      <param name="sourceSpan">要新增的來源範圍內容。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.ChangeStatementCollector(System.Action{System.String,System.CodeDom.CodeLinePragma})">
      <summary>指派新陳述式控制器並傳回還原舊陳述式控制器的可處置動作。</summary>
      <returns>還原舊陳述式控制器的可處置動作。</returns>
      <param name="collector">新的陳述式控制。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CodeMappings">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生程式碼對應的字典集合。</summary>
      <returns>已產生程式碼對應的字典集合。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CompileUnit">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定保留程式圖形的程式碼編譯單元。</summary>
      <returns>保留程式圖形的程式碼編譯單元。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.Create(System.Web.Razor.RazorEngineHost,System.String,System.String,System.String,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 <see cref="T:System.Web.Razor.Generator.CodeGeneratorContext" /> 類別的新執行個體。</summary>
      <returns>程式碼產生器內容新建立的執行個體。</returns>
      <param name="host">Razor 引擎主機。</param>
      <param name="className">產生的類別類型宣告類別名稱。</param>
      <param name="rootNamespace">產生的命名空間宣告名稱。</param>
      <param name="sourceFile">原始檔。</param>
      <param name="shouldGenerateLinePragmas">true 則啟用行程式的產生，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CurrentBufferedStatement">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前緩衝的陳述式。</summary>
      <returns>目前緩衝的陳述式。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.EnsureExpressionHelperVariable">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。若未新增，請在已產生類別新增表示式 Helper 變數，</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.FlushBufferedStatement">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。排清目前緩衝的陳述式。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.GeneratedClass">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生的類別類型宣告。</summary>
      <returns>產生的類別類型宣告。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生指定來源的行程式。</summary>
      <returns>指定來源的行程式。</returns>
      <param name="target">來源範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生來源的行程式。</summary>
      <returns>指定來源的行程式。</returns>
      <param name="target">來源範圍。</param>
      <param name="generatedCodeStart">程式碼的起始索引。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生來源的行程式。</summary>
      <returns>指定來源的行程式。</returns>
      <param name="target">來源範圍。</param>
      <param name="generatedCodeStart">程式碼的起始索引。</param>
      <param name="codeLength">程式碼長度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Text.SourceLocation,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生來源的行程式。</summary>
      <returns>指定來源的行程式。</returns>
      <param name="start">來源位置。</param>
      <param name="generatedCodeStart">程式碼的起始索引。</param>
      <param name="codeLength">程式碼長度。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.Host">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 Razor 引擎主機。</summary>
      <returns>Razor 引擎主機。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.MarkEndOfGeneratedCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。標記已產生程式碼結尾。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.MarkStartOfGeneratedCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。標記已產生程式碼開頭。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.Namespace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生的命名空間宣告。</summary>
      <returns>已產生的命名空間宣告。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.SourceFile">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定來源檔案。</summary>
      <returns>原始檔。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.TargetMethod">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生的成員方法。</summary>
      <returns>已產生的成員方法。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.TargetWriterName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定文字寫入器的名稱。</summary>
      <returns>文字寫入器的名稱。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.CSharpRazorCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 C# 語言的 Razor 程式碼碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CSharpRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.CSharpRazorCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="className">產生的類別類型宣告類別名稱。</param>
      <param name="rootNamespaceName">產生的命名空間宣告名稱。</param>
      <param name="sourceFileName">原始檔。</param>
      <param name="host">Razor 引擎主機。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CSharpRazorCodeGenerator.Initialize(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化此程式碼產生器的內容。</summary>
      <param name="context">此程式碼產生器的內容。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示區塊程式碼產生器的動態屬性。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Int32,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="prefix">前置字元。</param>
      <param name="offset">位移值。</param>
      <param name="line">行值。</param>
      <param name="col">col。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="prefix">字串前置詞。</param>
      <param name="valueStart">開始值。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用指定的參數產生程式碼以結束區塊。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用指定的參數產生程式碼以開始區塊。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.Prefix">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼產生器命名空間前置詞。</summary>
      <returns>程式碼產生的命名空間前置詞。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回表示目前物件的字串。</summary>
      <returns>表示目前物件的字串。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.ValueStart">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得針對動態屬性區塊程式碼產生器的開始值。</summary>
      <returns>針對動態屬性區塊程式碼產生器的開始值。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.ExpressionCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示運算式的程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.ExpressionCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示此執行個體和指定物件是否相同。</summary>
      <returns>如果 <paramref name="obj" /> 和此執行個體相同且表示相同的值，則為true，否則為 false。</returns>
      <param name="obj">要與目前執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生運算式的程式碼。</summary>
      <param name="target">代表表示式的來源範圍內容。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生區塊的結尾程式碼。</summary>
      <param name="target">結尾程式碼產生的目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生區塊的開始程式碼。</summary>
      <param name="target">開始程式碼產生的目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.ExpressionRenderingMode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.ExpressionRenderingMode.InjectCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.ExpressionRenderingMode.WriteToOutput">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.GeneratedClassContext">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示產生的類別內容。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 類別的新執行個體。</summary>
      <param name="executeMethodName">執行方法名稱。</param>
      <param name="writeMethodName">寫入方法名稱。</param>
      <param name="writeLiteralMethodName">寫入文字方法名稱。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 類別的新執行個體。</summary>
      <param name="executeMethodName">執行方法名稱。</param>
      <param name="writeMethodName">寫入方法名稱。</param>
      <param name="writeLiteralMethodName">寫入文字方法名稱。</param>
      <param name="writeToMethodName">寫入方法名稱。</param>
      <param name="writeLiteralToMethodName">寫入文字方法名稱。</param>
      <param name="templateTypeName">範本類型名稱。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 類別的新執行個體。</summary>
      <param name="executeMethodName">執行方法名稱。</param>
      <param name="writeMethodName">寫入方法名稱。</param>
      <param name="writeLiteralMethodName">寫入文字方法名稱。</param>
      <param name="writeToMethodName">寫入方法名稱。</param>
      <param name="writeLiteralToMethodName">寫入文字方法名稱。</param>
      <param name="templateTypeName">範本類型名稱。</param>
      <param name="defineSectionMethodName">定義區段方法名稱。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 類別的新執行個體。</summary>
      <param name="executeMethodName">執行方法名稱。</param>
      <param name="writeMethodName">寫入方法名稱。</param>
      <param name="writeLiteralMethodName">寫入文字方法名稱。</param>
      <param name="writeToMethodName">寫入方法名稱。</param>
      <param name="writeLiteralToMethodName">寫入文字方法名稱。</param>
      <param name="templateTypeName">範本類型名稱。</param>
      <param name="defineSectionMethodName">定義區段方法名稱。</param>
      <param name="beginContextMethodName">開始內容方法名稱。</param>
      <param name="endContextMethodName">結束內容方法名稱。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.AllowSections">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，這個值表示內容是否允許區段。</summary>
      <returns>如果內容允許區段，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.AllowTemplates">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，這個值表示內容是否允許範本。</summary>
      <returns>如果內容允許範本，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.BeginContextMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生內容之前的方法名稱。</summary>
      <returns>已產生內容之前的方法名稱。</returns>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.Default">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。定義預設的已產生內容。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultExecuteMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得執行方法的預設名稱。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultLayoutPropertyName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得版面配置屬性的預設名稱。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteAttributeMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。定義寫入方法的預設名稱。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteAttributeToMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。定義寫入方法的預設名稱。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteLiteralMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。指定寫入文字方法的預設名稱。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。指定寫入方法的預設名稱。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.DefineSectionMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定定義內容區段的方法名稱。</summary>
      <returns>定義內容區段的方法名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.EndContextMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生內容之後的方法名稱。</summary>
      <returns>已產生內容之後的方法名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.ExecuteMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定會在內容中叫用的方法名稱。</summary>
      <returns>會在內容中叫用的方法名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.LayoutPropertyName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定版面配置的屬性名稱。</summary>
      <returns>版面配置的屬性名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.op_Equality(System.Web.Razor.Generator.GeneratedClassContext,System.Web.Razor.Generator.GeneratedClassContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩種 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 物件是否相同。</summary>
      <returns>若兩個 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 物件相同，為 true，否則為 false。</returns>
      <param name="left">要比較的第一個物件。</param>
      <param name="right">要比較的第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.op_Inequality(System.Web.Razor.Generator.GeneratedClassContext,System.Web.Razor.Generator.GeneratedClassContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩種 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 物件是否不相同。</summary>
      <returns>若兩個 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 物件相同，為 true，否則為 false。</returns>
      <param name="left">要比較的第一個物件。</param>
      <param name="right">要比較的第二個物件。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.ResolveUrlMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定會解析內容 URL 的方法名稱。</summary>
      <returns>會解析內容 URL 的方法名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.SupportsInstrumentation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，這個值表示產生的類別是否支援檢測設備。</summary>
      <returns>如果產生的類別支援檢測設備，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.TemplateTypeName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範本的類型名稱。</summary>
      <returns>範本的類型名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteAttributeMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定會寫入屬性的方法名稱。</summary>
      <returns>會寫入屬性的方法名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteAttributeToMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定會寫入屬性的方法名稱位置。</summary>
      <returns>會寫入屬性的方法名稱位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteLiteralMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定寫入內容文字的方法名稱位置。</summary>
      <returns>寫入內容文字的方法名稱位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteLiteralToMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定寫入內容文字的方法名稱位置。</summary>
      <returns>寫入內容文字的方法名稱位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定會在內容中寫入的方法名稱。</summary>
      <returns>會在內容中寫入的方法名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteToMethodName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定會在內容中寫入的方法名稱。</summary>
      <returns>會在內容中寫入的方法名稱。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.GeneratedCodeMapping">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示已產生的程式碼對應物件。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedCodeMapping" /> 類別的新執行個體。</summary>
      <param name="startLine">開始行。</param>
      <param name="startColumn">開始欄。</param>
      <param name="startGeneratedColumn">開始產生的欄。</param>
      <param name="codeLength">程式碼長度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedCodeMapping" /> 類別的新執行個體。</summary>
      <param name="startOffset">開始位移。</param>
      <param name="startLine">開始行。</param>
      <param name="startColumn">開始欄。</param>
      <param name="startGeneratedColumn">開始產生的欄。</param>
      <param name="codeLength">程式碼長度。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.CodeLength">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生對應程式碼的長度。</summary>
      <returns>已產生對應程式碼的長度。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前已產生的程式碼對應物件。</summary>
      <returns>若指定的物件等於已產生的程式碼對應物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回已產生的程式碼對應物件的雜湊碼。</summary>
      <returns>已產生的程式碼對應物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.op_Equality(System.Web.Razor.Generator.GeneratedCodeMapping,System.Web.Razor.Generator.GeneratedCodeMapping)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩個指定的已產生程式碼對應物件是否具有相同值。</summary>
      <returns>如果判斷兩個指定的已產生程式碼對應物件具有相同值，則為 true，否則為 false。</returns>
      <param name="left">左側的已產生的程式碼對應物件。</param>
      <param name="right">右側的已產生的程式碼對應物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.op_Inequality(System.Web.Razor.Generator.GeneratedCodeMapping,System.Web.Razor.Generator.GeneratedCodeMapping)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩個指定的已產生程式碼對應物件是否具有不同值。</summary>
      <returns>如果判斷兩個指定的已產生程式碼對應物件具有不同值，則為 true，否則為 false。</returns>
      <param name="right">右側的已產生的程式碼對應物件。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartColumn">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生程式碼對應的開始欄。</summary>
      <returns>已產生程式碼對應的開始欄。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartGeneratedColumn">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生來源檔案中的已產生程式碼對應的開始欄。</summary>
      <returns>已產生來源檔案中的已產生程式碼對應的開始欄。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartLine">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得已產生程式碼對應的開始行。</summary>
      <returns>已產生程式碼對應的開始行。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartOffset">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已產生程式碼對應的開始位移。</summary>
      <returns>已產生程式碼對應的開始位移。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.ToString">
      <summary>傳回表示目前物件的字串。</summary>
      <returns>表示目前物件的字串。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.HelperCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Helper 程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.HelperCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="signature">簽章。</param>
      <param name="headerComplete">true 表示標頭完成，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.Footer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定此程式碼的頁尾。</summary>
      <returns>此程式碼的頁尾。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在程式碼之後產生區塊。</summary>
      <param name="target">要產生的區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在程式碼之前產生區塊。</summary>
      <param name="target">要產生的區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.HeaderComplete">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定值，這個值表示此程式碼的標頭是否已完成。</summary>
      <returns>如果已完成此程式碼的標頭，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.Signature">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定此程式碼的簽章。</summary>
      <returns>此程式碼的簽章。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.HybridCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示混合式程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.HybridCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生依參數識別的切換資料模型程式碼。</summary>
      <param name="target">目標物件。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生結尾區塊程式碼。</summary>
      <param name="target">目標物件。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生開始區塊程式碼。</summary>
      <param name="target">目標物件。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.IBlockCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示網頁的 <see cref="T:System.Web.Razor.Generator.IBlockCodeGenerator" />。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.IBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生 Razor 的結尾區塊程式碼。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.IBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生 Razor 的開始區塊程式碼。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.ISpanCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示程式碼產生器的階段。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ISpanCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生具有指定目標和內容的資料模型程式碼。</summary>
      <param name="target">目標物件。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.LiteralAttributeCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示文字屬性的程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.String})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。.初始化 <see cref="T:System.Web.Razor.Generator.LiteralAttributeCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="prefix">文字屬性的前置詞。</param>
      <param name="value">文字屬性的值。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.Web.Razor.Generator.SpanCodeGenerator})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。.初始化 <see cref="T:System.Web.Razor.Generator.LiteralAttributeCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="prefix">文字屬性的前置詞。</param>
      <param name="valueGenerator">文字屬性的值產生器。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此執行個體。</summary>
      <returns>如果指定的物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生文字屬性的程式碼。</summary>
      <param name="target">代表文字屬性的來源範圍內容。</param>
      <param name="context">程式碼產生器的內容。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Prefix">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定文字屬性的前置詞。</summary>
      <returns>文字屬性的前置詞。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Value">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定文字屬性值。</summary>
      <returns>文字屬性的值。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.ValueGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定文字屬性值產生器。</summary>
      <returns>文字屬性的值產生器。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.MarkupCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示標記的程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.MarkupCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此執行個體。</summary>
      <returns>如果指定的物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生標記的程式碼。</summary>
      <param name="target">代表標記的來源範圍內容。</param>
      <param name="context">程式碼產生器的內容。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.RazorCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="className">類別名稱。</param>
      <param name="rootNamespaceName">根命名空間名稱。</param>
      <param name="sourceFileName">原始程式檔的名稱。</param>
      <param name="host">主機。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.ClassName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定此程式碼的類別名稱。</summary>
      <returns>此程式碼的類別名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.Context">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此程式碼產生器的內容。</summary>
      <returns>此程式碼產生器的內容。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.DesignTimeMode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定一值，指出程式碼產生器是否為設計時間模式。</summary>
      <returns>如果程式碼產生器為設計時間模式，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.GenerateLinePragmas">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定一值，指出產生器是否應該產生 Razor 程式碼的行程式。</summary>
      <returns>如果產生器應該產生 Razor 程式碼的行程式，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.Host">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 Razor 引擎主機。</summary>
      <returns>Razor 引擎主機。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.Initialize(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化目前的 <see cref="T:System.Web.Razor.Generator.RazorCodeGenerator" /> 執行個體。</summary>
      <param name="context">內容。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.OnComplete">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。引發完成事件。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.RootNamespaceName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定根命名空間的名稱。</summary>
      <returns>根命名空間的名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.SourceFileName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定來源檔案的名稱。</summary>
      <returns>原始程式檔的名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。造訪結尾區塊。</summary>
      <param name="block">要造訪的區塊。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。造訪範圍。</summary>
      <param name="span">要造訪的範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。造訪開始區塊。</summary>
      <param name="block">要造訪的區塊。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorCommentCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示網頁的 razor 註解程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCommentCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.RazorCommentCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCommentCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生具有指定參數的開始區塊程式碼。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 指示詞屬性的程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.#ctor(System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="name">指示詞屬性的名稱。</param>
      <param name="value">指示詞屬性的值。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此執行個體。</summary>
      <returns>如果指定的物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生指示詞屬性的程式碼。</summary>
      <param name="target">代表要產生的指示詞屬性來源範圍內容。</param>
      <param name="context">程式碼產生器的內容。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Name">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定指示詞屬性的名稱。</summary>
      <returns>指示詞屬性的名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Value">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定指示詞屬性值。</summary>
      <returns>指示詞屬性的值。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.ResolveUrlCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示解析 Url 程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.ResolveUrlCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示此執行個體和指定物件是否相同。</summary>
      <returns>如果 <paramref name="obj" /> 和此執行個體相同且表示相同的值，則為true，否則為 false。</returns>
      <param name="obj">要與目前執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生 Url 的程式碼。</summary>
      <param name="target">目標物件。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此執行個體的完整限定類型名稱。</summary>
      <returns>完整限定類型名稱。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SectionCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示區段程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.#ctor(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.SectionCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="sectionName">區段程式碼名稱。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在區段程式碼之後產生區塊。</summary>
      <param name="target">要產生的目標。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在區段程式碼之前產生區塊。</summary>
      <param name="target">要產生的目標。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。擷取此目前執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.SectionCodeGenerator.SectionName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定區段的名稱。</summary>
      <returns>區段的名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SetBaseTypeCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示集基本類型的程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.#ctor(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.SetBaseTypeCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="baseType">集基本類型。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.BaseType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定集基本類型。</summary>
      <returns>集基本類型。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此執行個體。</summary>
      <returns>如果指定的物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生此集基本類型的程式碼。</summary>
      <param name="target">包含要產生程式碼的集基本類型的來源範圍。</param>
      <param name="context">程式碼產生器的內容。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.ResolveType(System.Web.Razor.Generator.CodeGeneratorContext,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。解析給定的集基本類型。</summary>
      <returns>解析的集基本類型。</returns>
      <param name="context">程式碼產生器的內容。</param>
      <param name="baseType">要解析的集基本類型。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SetLayoutCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示設定 Razor 網頁版面配置的程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.#ctor(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.SetLayoutCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="layoutPath">版面配置路徑。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生版面配置程式碼。</summary>
      <param name="target">要產生程式碼的目標位置。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。擷取此目前執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.SetLayoutCodeGenerator.LayoutPath">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定版面配置程式碼的路徑。</summary>
      <returns>版面配置程式碼的路徑。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示值的 SetVBOptionCodeGenerator 的轉換。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.#ctor(System.String,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="optionName">選項名稱。</param>
      <param name="value">如果物件具有值，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Explicit(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。明確地將 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 轉換為開始和關閉值。</summary>
      <returns>明確地將 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 轉換為開始和關閉值。</returns>
      <param name="onOffValue">如果可以將 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 轉換成開始和關閉值，則為 true，否則為 false。</param>
    </member>
    <member name="F:System.Web.Razor.Generator.SetVBOptionCodeGenerator.ExplicitCodeDomOptionName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示明確的程式碼 Dom 選項名稱。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生指定參數的程式碼。</summary>
      <param name="target">目標。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.SetVBOptionCodeGenerator.OptionName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼產生器的選項名稱。</summary>
      <returns>程式碼產生器的選項名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Strict(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。嚴格地將 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 轉換為開始和關閉值。</summary>
      <returns>嚴格地將 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 轉換為開始和關閉值。</returns>
      <param name="onOffValue">如果可以嚴格地將 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 轉換成開始和關閉值，則為 true，否則為 false。</param>
    </member>
    <member name="F:System.Web.Razor.Generator.SetVBOptionCodeGenerator.StrictCodeDomOptionName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示嚴格的程式碼 Dom 選項名稱。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回表示目前物件的字串。</summary>
      <returns>表示目前物件的字串。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Value">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，這個值表示 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 是否有值。</summary>
      <returns>如果 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 有值，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SpanCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 的範圍程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.SpanCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生指定目標和內容參數的程式碼。</summary>
      <param name="target">目標範圍。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回範圍程式碼產生器的雜湊碼。</summary>
      <returns>範圍程式碼產生器的雜湊碼。</returns>
    </member>
    <member name="F:System.Web.Razor.Generator.SpanCodeGenerator.Null">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生範圍程式碼產生器的 Null 值。</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.StatementCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。代表陳述式的程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.StatementCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此執行個體。</summary>
      <returns>如果指定的物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生陳述式的程式碼。</summary>
      <param name="target">包含要產生的陳述式範圍來源內容。</param>
      <param name="context">程式碼產生器的內容。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>此目前執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.TemplateBlockCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 的範本區塊程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.TemplateBlockCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生程式碼以結束範本區塊程式碼產生器的區塊。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生程式碼以開始範本區塊程式碼產生器的區塊。</summary>
      <param name="target">目標區塊。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.TypeMemberCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示類型成員程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.TypeMemberCodeGenerator" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。以給定的目標和內容產生程式碼。</summary>
      <param name="target">要產生程式碼的目標位置。</param>
      <param name="context">產生器內容的程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。擷取此目前執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此程式碼的字串表示法。</summary>
      <returns>此程式碼的字串表示法。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.VBRazorCodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 VB 的 Razor 程式碼產生器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.VBRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Generator.VBRazorCodeGenerator" /> 類別的新執行個體。</summary>
      <param name="className">類別的名稱。</param>
      <param name="rootNamespaceName">根命名空間。</param>
      <param name="sourceFileName">資產來源的檔案名稱。</param>
      <param name="host">主機。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.BalancingModes">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.AllowCommentsAndTemplates">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.AllowEmbeddedTransitions">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.BacktrackOnFailure">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.NoErrorOnFailure">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.None">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.CallbackVisitor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示在造訪完成時執行回呼的訪客。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 類別的新執行個體。</summary>
      <param name="spanCallback">針對範圍造訪的委派。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 類別的新執行個體。</summary>
      <param name="spanCallback">針對範圍造訪的委派。</param>
      <param name="errorCallback">針對錯誤造訪的委派。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 類別的新執行個體。</summary>
      <param name="spanCallback">針對範圍造訪的委派。</param>
      <param name="errorCallback">針對錯誤造訪的委派。</param>
      <param name="startBlockCallback">針對開始區塊造訪的委派。</param>
      <param name="endBlockCallback">針對結束區塊造訪的委派。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 類別的新執行個體。</summary>
      <param name="spanCallback">針對範圍造訪的委派。</param>
      <param name="errorCallback">針對錯誤造訪的委派。</param>
      <param name="startBlockCallback">針對開始區塊造訪的委派。</param>
      <param name="endBlockCallback">針對結束區塊造訪的委派。</param>
      <param name="completeCallback">執行完整事件的委派。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.OnComplete">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CallbackVisitor.SynchronizationContext">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定此回呼訪客的同步內容。</summary>
      <returns>此回呼訪客的同步內容。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。執行回呼訪客回呼以造訪結束區塊。</summary>
      <param name="block">要造訪的結束區塊。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。執行回呼訪客回呼以造訪錯誤。</summary>
      <param name="err">要造訪的 Razor 錯誤。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。執行回呼訪客回呼以造訪範圍。</summary>
      <param name="span">要造訪的範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。執行回呼訪客回呼以造訪開始區塊。</summary>
      <param name="block">要造訪的開始區塊。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpCodeParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 C sharp 程式碼剖析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.CSharpCodeParser" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.AcceptIf(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析器是否接受 ‘IF’ 關鍵字。</summary>
      <returns>如果剖析器接受 ‘IF’ 關鍵字，則為 true，否則為 false。</returns>
      <param name="keyword">要接受的關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.AssertDirective(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷提示指示詞程式碼。</summary>
      <param name="directive">判斷提示的指示詞程式碼。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.At(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否包含 ‘AT’ 關鍵字。</summary>
      <returns>如果程式碼是否包含 ‘AT’ 關鍵字，則為 true，否則為 false。</returns>
      <param name="keyword">關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.BaseTypeDirective(System.String,System.Func{System.String,System.Web.Razor.Generator.SpanCodeGenerator})">
      <summary>表示基本類型指示詞。</summary>
      <param name="noTypeNameError">沒有類型名稱錯誤。</param>
      <param name="createCodeGenerator">建立程式碼產生器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.FunctionsDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示函式指示詞。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.HandleEmbeddedTransition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示處理內嵌轉換的程式碼。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.HelperDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Helper 指示詞。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.InheritsDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示應用程式會指示檢視的類別，因此可以確保適當的類型檢查。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.InheritsDirectiveCore">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。繼承指示詞核心。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指示詞是否位於內嵌的轉換中。</summary>
      <returns>如果程式碼位於內嵌的轉換，則為 true，否則為 false。</returns>
      <param name="allowTemplatesAndComments">true 表示允許範本和調解，否則為 false。</param>
      <param name="allowTransitions">true 表示允許轉換，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.IsNested">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定值，這個值表示程式碼是否需要進行巢狀。</summary>
      <returns>如果程式碼需要進行巢狀，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.IsSpacingToken(System.Boolean,System.Boolean)">
      <summary>表示行和註解是否為空間語彙基元。</summary>
      <returns>表示空間語彙基元的函式。</returns>
      <param name="includeNewLines">true 表示包含新行，否則為 false。</param>
      <param name="includeComments">true 表示包括註解，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Keywords">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 C sharp 語言關鍵字。</summary>
      <returns>C sharp 語言關鍵字。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Language">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得剖析的特定語言。</summary>
      <returns>剖析的特定語言。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.LayoutDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示版面配置指示詞。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.MapDirectives(System.Action,System.String[])">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。對應指定的指示詞。</summary>
      <param name="handler">處理常式。</param>
      <param name="directives">指示詞。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.OtherParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼使用的其他剖析器。</summary>
      <returns>程式碼使用的其他剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.OutputSpanBeforeRazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在註解前定出剖析輸出範圍。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ParseBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。封鎖剖析。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ReservedDirective(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示保留指示詞。</summary>
      <param name="topLevel">判斷指示詞是否為頂層。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SectionDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示區段指示詞。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示工作階段狀態指示詞。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateDirectiveCore">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示工作階段狀態指示詞核心。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateTypeDirective(System.String,System.Func{System.String,System.String,System.Web.Razor.Generator.SpanCodeGenerator})">
      <summary>表示工作階段狀態類型指示詞。</summary>
      <param name="noValueError">沒有值錯誤。</param>
      <param name="createCodeGenerator">建立程式碼產生器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.TryGetDirectiveHandler(System.String,System.Action@)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得指示詞處理常式。</summary>
      <returns>如果成功，則為 true，否則為 false。</returns>
      <param name="directive">指示詞。</param>
      <param name="handler">處理常式。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ValidSessionStateValue">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷工作階段狀態的值是否有效。</summary>
      <returns>如果工作階段狀態的值有效，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpCodeParser.Block">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示此 CSharpCode 剖析器的區塊。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.Block.#ctor(System.String,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.CSharpCodeParser.Block" /> 類別的新執行個體。</summary>
      <param name="name">字串名稱。</param>
      <param name="start">來源位置的起點。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.Block.#ctor(System.Web.Razor.Tokenizer.Symbols.CSharpSymbol)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.CSharpCodeParser.Block" /> 類別的新執行個體。</summary>
      <param name="symbol">CSharp 符號。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Block.Name">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定區塊的字串名稱。</summary>
      <returns>區塊的字串名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Block.Start">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定開始區塊的來源位置。</summary>
      <returns>開始區塊的來源位置。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 CSharp 語言中不同的語言特性。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在程式碼中建立標記符號。</summary>
      <returns>程式碼中的標記符號。</returns>
      <param name="location">來源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在程式碼中建立符號。</summary>
      <returns>程式碼中的符號。</returns>
      <param name="location">來源位置。</param>
      <param name="content">內容值。</param>
      <param name="type">HTML 符號類型。</param>
      <param name="errors">錯誤清單。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立語言 Tokenizer。</summary>
      <returns>語言 Tokenizer。</returns>
      <param name="source">文字文件來源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在程式碼中翻轉括弧符號。</summary>
      <returns>程式碼中的括弧符號。</returns>
      <param name="bracket">符號括弧。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetKeyword(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼中的關鍵字。</summary>
      <returns>程式碼中的關鍵字。</returns>
      <param name="keyword">關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼中的 <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" />。</summary>
      <returns>程式碼中的 <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" />。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼中的簡單符號。</summary>
      <returns>程式碼中的簡單符號。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetSymbolSample(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼中的簡單符號。</summary>
      <returns>程式碼中的簡單符號。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpLanguageCharacteristics.Instance">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" /> 類別的執行個體。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" /> 類別的執行個體。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.HtmlLanguageCharacteristics">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 HTML 中不同的語言特性。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 HTML 中建立標記符號。</summary>
      <returns>HTML 中的標記符號。</returns>
      <param name="location">來源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 HTML 中建立符號。</summary>
      <returns>HTML 中的符號。</returns>
      <param name="location">來源位置。</param>
      <param name="content">內容值。</param>
      <param name="type">HTML 符號類型。</param>
      <param name="errors">錯誤清單。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 HTML Tokenizer。</summary>
      <returns>HTML Tokenizer。</returns>
      <param name="source">文字文件來源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 HTML 中翻轉括弧符號。</summary>
      <returns>HTML 中的括弧符號。</returns>
      <param name="bracket">符號括弧。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 HTML 中的 <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</summary>
      <returns>HTML 中的 <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 HTML 中的簡單符號。</summary>
      <returns>HTML 中的簡單符號。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlLanguageCharacteristics.Instance">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Parser.HtmlLanguageCharacteristics" /> 類別的執行個體。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.HtmlLanguageCharacteristics" /> 類別的執行個體。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.HtmlMarkupParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示特別用來剖析 HTML 標記的剖析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.HtmlMarkupParser" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用指定的範圍建立器建立給定內容的範圍。</summary>
      <param name="span">用來建立範圍的範圍建立器。</param>
      <param name="start">開始位置。</param>
      <param name="content">範圍內容。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.IsSpacingToken(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回用來判斷 HTML 空間所使用的語彙基元函式委派。</summary>
      <returns>用來判斷 HTML 空間所使用的語彙基元函式委派。</returns>
      <param name="includeNewLines">true 表示新行已視為空間語彙基元，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.Language">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得定義 HTML 語言特性的執行個體。</summary>
      <returns>定義 HTML 語言特性的執行個體。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.OtherParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得剖析 HTML 標記的其他剖析器。</summary>
      <returns>剖析 HTML 標記的其他剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.OutputSpanBeforeRazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 Razor 註解之前建立範圍。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析下一個 HTML 區塊。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseDocument">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析 HTML 文件。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseSection(System.Tuple{System.String,System.String},System.Boolean)">
      <summary>以巢狀序列剖析具有標記的區段。</summary>
      <param name="nestingSequences">指定標記巢狀序列的 Tuple。</param>
      <param name="caseSensitive">true 表示區分大小寫剖析，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.SkipToAndParseCode(System.Func{System.Web.Razor.Tokenizer.Symbols.HtmlSymbol,System.Boolean})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。略過剖析，直到符合指定的條件。</summary>
      <param name="condition">定義條件的函式委派。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.SkipToAndParseCode(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。略過剖析，直到遇到指定的 HTML 符號類型。</summary>
      <param name="type">HTML 符號類型。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.VoidElements">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得視為 void 的 HTML 標記。</summary>
      <returns>視為 void 的 HTML 標記。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.LanguageCharacteristics`3">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供定義 Razor 程式碼語言行為的方法。</summary>
      <typeparam name="TTokenizer">Razor 語言的程式碼 Tokenizer 類型。</typeparam>
      <typeparam name="TSymbol">語言符號的類型。</typeparam>
      <typeparam name="TSymbolType">語言符號的列舉類型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.LanguageCharacteristics`3" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。以指定來源位置建立程式碼語言符號做為起點標記。</summary>
      <returns>程式碼語言的符號。</returns>
      <param name="location">做為起點標記的來源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,`2,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。以指定來源位置建立具有指定來源位置的程式碼語言符號做為起點標記。</summary>
      <returns>程式碼語言的符號。</returns>
      <param name="location">做為起點標記的來源位置。</param>
      <param name="content">內容。</param>
      <param name="type">語言符號的列舉類型。</param>
      <param name="errors">錯誤的集合。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立指定來源文件的 Razor 程式碼語言。</summary>
      <returns>指定來源文件的 Razor 程式碼語言。</returns>
      <param name="source">來源文件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.FlipBracket(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回指定括弧符號的相反括弧符號。</summary>
      <returns>指定括弧符號的相反括弧符號。</returns>
      <param name="bracket">要翻轉的括弧符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得給定符號類型的指定語言符號類型。</summary>
      <returns>給定符號類型的指定語言符號類型。</returns>
      <param name="type">要取得的符號類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.GetSample(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得給定語言符號類型的實際符號。</summary>
      <returns>給定語言符號類型的實際符號。</returns>
      <param name="type">要取得的語言符號類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentBody(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為註解主體類型。</summary>
      <returns>如果符號為註解主體類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentStar(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為註解星號類型。</summary>
      <returns>如果符號為註解星號類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentStart(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為註解起點類型。</summary>
      <returns>如果符號為註解起點類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsIdentifier(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為識別項類型。</summary>
      <returns>如果符號為識別項類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsKeyword(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為關鍵字類型。</summary>
      <returns>如果符號為關鍵字類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsKnownSymbolType(`1,System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號類型是否為已知符號類型。</summary>
      <returns>如果符號類型為已知符號類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號類型。</param>
      <param name="type">符號的已知類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsNewLine(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為新行類型。</summary>
      <returns>如果符號為新行類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsTransition(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為轉換類型。</summary>
      <returns>如果符號為轉換類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsUnknown(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為已知類型。</summary>
      <returns>如果符號為已知類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsWhiteSpace(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為空白類型。</summary>
      <returns>如果符號為空白類型，則為 true，否則為 false。</returns>
      <param name="symbol">要檢查的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.KnowsSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷符號是否為已知類型。</summary>
      <returns>如果符號為已知類型，則為 true，否則為 false。</returns>
      <param name="type">符號的已知類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.SplitSymbol(`1,System.Int32,`2)">
      <summary>在指定的索引分割程式碼語言符號內容。</summary>
      <returns>程式碼語言符號 Tuple。</returns>
      <param name="symbol">要分割的符號內容。</param>
      <param name="splitAt">分割發生的索引。</param>
      <param name="leftType">語言符號的列舉類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.TokenizeString(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將指定的字串分割為語彙基元。</summary>
      <returns>語彙基元的集合。</returns>
      <param name="content">要 Tokenize 的字串。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.TokenizeString(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將指定的字串分割為語彙基元。</summary>
      <returns>語彙基元的集合。</returns>
      <param name="start">做為 Tokenizer 起點標記的來源位置。</param>
      <param name="input">要 Tokenize 的字串。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserBase">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 的剖析器基本類別。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.ParserBase" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立剖析器基本的範圍。</summary>
      <param name="span">範圍產生器。</param>
      <param name="start">來源位置的起點。</param>
      <param name="content">內容。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.Context">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 <see cref="T:System.Web.Razor.Parser.ParserContext" />。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.ParserContext" />。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.IsMarkupParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，這個值表示剖析器是否為標記剖析器。</summary>
      <returns>如果剖析器為標記剖析器，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.OtherParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得其他剖析器 <see cref="T:System.Web.Razor.Parser.ParserBase" />。</summary>
      <returns>其他剖析器 <see cref="T:System.Web.Razor.Parser.ParserBase" />。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。封鎖剖析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseDocument">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立剖析的文件。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseSection(System.Tuple{System.String,System.String},System.Boolean)">
      <summary>剖析位於元素順序清單中的區段。</summary>
      <param name="nestingSequences">巢狀順序配對。</param>
      <param name="caseSensitive">如果區分大小寫，則為 true，否則為 false。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserContext">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示可以切換為程式碼或標記的剖析器內容。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.#ctor(System.Web.Razor.Text.ITextDocument,System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.ParserContext" /> 類別的新執行個體。</summary>
      <param name="source">來源文件。</param>
      <param name="codeParser">內容的程式碼剖析器。</param>
      <param name="markupParser">內容的標記剖析器。</param>
      <param name="activeParser">內容的作用中剖析器。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.ActiveParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定內容的作用中剖析器。</summary>
      <returns>內容的作用中剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.AddSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在區塊建立器堆疊結尾處新增指定的範圍。</summary>
      <param name="span">要新增的範圍。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CodeParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定內容的程式碼剖析器。</summary>
      <returns>內容的程式碼剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.CompleteParse">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析上次範圍，並傳回包含新建立區塊的剖析結果。</summary>
      <returns>包含新建立區塊的剖析結果。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CurrentBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前區塊建立器。</summary>
      <returns>目前區塊建立器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CurrentCharacter">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從來源取得目前可用的字元。</summary>
      <returns>來源中目前可用的字元。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.DesignTimeMode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定值，這個值表示剖析器是否為設計模式。</summary>
      <returns>如果剖析器為設計模式，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.EndBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從區塊建立器堆疊的上個項目建立結尾區塊。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.EndOfFile">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，這個值表示來源狀態是否為檔案結尾。</summary>
      <returns>如果來源狀態為檔案結尾，則為true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.Errors">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定剖析時的錯誤清單。</summary>
      <returns>錯誤清單。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.IsWithin(System.Web.Razor.Parser.SyntaxTree.BlockType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定區塊類型是否存在於區塊建立器清單中。</summary>
      <returns>若指定區塊類型存在於區塊建立器清單，則為 true，否則為 false。</returns>
      <param name="type">要檢查的區塊類型。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.LastAcceptedCharacters">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得最後接受的字元。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters" /> 列舉的其中一個值。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.LastSpan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定上次範圍。</summary>
      <returns>最後範圍。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.MarkupParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定內容的標記剖析器。</summary>
      <returns>內容的標記剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.OnError(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。當剖析發生錯誤時產生。</summary>
      <param name="location">來源位置。</param>
      <param name="message">錯誤訊息。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.OnError(System.Web.Razor.Text.SourceLocation,System.String,System.Object[])">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。當剖析發生錯誤時產生。</summary>
      <param name="location">來源位置。</param>
      <param name="message">錯誤訊息。</param>
      <param name="args">關於來源位置的其他資訊。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.Source">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定來源文件的文字讀取器。</summary>
      <returns>來源文件的文字讀取器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.StartBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在區塊建立器堆疊結尾處新增新的區塊建立器，並傳回傳回結尾區塊的可處置動作。</summary>
      <returns>傳回結尾區塊的可處置動作。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.StartBlock(System.Web.Razor.Parser.SyntaxTree.BlockType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在區塊建立器堆疊結尾處新增新的區塊建立器，並傳回傳回結尾區塊的可處置動作。</summary>
      <returns>傳回結尾區塊的可處置動作。</returns>
      <param name="blockType">新區塊建立器的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.SwitchActiveParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。或者，切換程式碼剖析器或標記剖析器做為作用中剖析器。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.WhiteSpaceIsSignificantToAncestorBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定值，這個值表示空白字元對於上階區塊來說是否重要。</summary>
      <returns>true 表示空白字元對於上階區塊來說重要，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserHelpers">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供剖析器 Helper 方法。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsCombining(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是為否為空格標記，或是非空格標記。</summary>
      <returns>true 表示指定的字元值是為空格標記，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsConnecting(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為連接器標點符號。</summary>
      <returns>如果指定的字元值為連接器標點符號，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsDecimalDigit(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為十進位數字。</summary>
      <returns>如果指定的字元值為十進位數字，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsEmailPart(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否可用於電子郵件地址中。</summary>
      <returns>如果指定的字元值可用於電子郵件地址中，則為 true，否則為 false。</returns>
      <param name="character">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsFormatting(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否用來格式化文字版面配置或格式化文字作業。</summary>
      <returns>如果指定的字元值用來格式化文字版面配置或格式化文字作業，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsHexDigit(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為十六進位數字。</summary>
      <returns>如果指定的字元值為十六進位數字，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifier(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字串值是否為識別項。</summary>
      <returns>如果指定的字串值為識別項，為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifier(System.String,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字串值是否為識別項。</summary>
      <returns>如果指定的字串值為識別項，為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
      <param name="requireIdentifierStart">true 表示識別項需要以字母或底線 (_) 開始，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifierPart(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否可用於識別項。</summary>
      <returns>如果指定的字元值可用於識別項，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifierStart(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否可用於識別項的起點字元。</summary>
      <returns>如果指定的字元值可用於識別項的起點字元，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsLetter(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為字母。</summary>
      <returns>如果指定的字元值為字母，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsLetterOrDecimalDigit(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為字母或十進位數字。</summary>
      <returns>如果指定的字元值為字母或十進位數字，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsNewLine(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定值是否為新行。</summary>
      <returns>如果指定的字元值為新行，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsNewLine(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定值是否為新行。</summary>
      <returns>如果指定的字元值為新行，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsTerminatingCharToken(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為終止字元語彙基元。</summary>
      <returns>如果指定的字元值為終止字元語彙基元，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsTerminatingQuotedStringToken(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為終止配額字串。</summary>
      <returns>如果指定的字元值為終止配額字串，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsWhitespace(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為空白。</summary>
      <returns>如果指定的字元值為空白，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsWhitespaceOrNewLine(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元值是否為空白或新行。</summary>
      <returns>如果指定的字元值為空白或新行，則為 true，否則為 false。</returns>
      <param name="value">要檢查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.SanitizeClassName(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。標記指定的輸入名稱以符合類別名稱的有效值。</summary>
      <returns>標記的類別名稱。</returns>
      <param name="inputName">要檢查的值。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserVisitor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示剖析器訪客。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.ParserVisitor" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserVisitor.CancelToken">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定取消權杖。</summary>
      <returns>取消權杖。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.OnComplete">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示訪客方法已完整的執行。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.ThrowIfCanceled">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。造訪指定的區塊。</summary>
      <param name="block">要造訪的區塊。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析之後造訪指定的區塊。</summary>
      <param name="block">要造訪的區塊。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。造訪給定的 Razor 錯誤。</summary>
      <param name="err">要造訪的錯誤。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。造訪指定的範圍。</summary>
      <param name="span">要造訪的範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析之前造訪指定的區塊。</summary>
      <param name="block">要造訪的區塊。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserVisitorExtensions">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供剖析器訪客的擴充方法。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitorExtensions.Visit(System.Web.Razor.Parser.ParserVisitor,System.Web.Razor.ParserResults)"></member>
    <member name="T:System.Web.Razor.Parser.RazorParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 程式碼剖析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.#ctor(System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.RazorParser" /> 類別的新執行個體。</summary>
      <param name="codeParser">程式碼剖析器。</param>
      <param name="markupParser">標記剖析器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立剖析指定物件的工作。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要剖析的物件。</param>
      <param name="spanCallback">範圍回呼。</param>
      <param name="errorCallback">錯誤回呼。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.CancellationToken)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立剖析指定物件的工作。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要剖析的物件。</param>
      <param name="spanCallback">範圍回呼。</param>
      <param name="errorCallback">錯誤回呼。</param>
      <param name="cancelToken">取消權杖。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.SynchronizationContext)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立剖析指定物件的工作。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要剖析的物件。</param>
      <param name="spanCallback">範圍回呼。</param>
      <param name="errorCallback">錯誤回呼。</param>
      <param name="context">內容。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.SynchronizationContext,System.Threading.CancellationToken)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立剖析指定物件的工作。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要剖析的物件。</param>
      <param name="spanCallback">範圍回呼。</param>
      <param name="errorCallback">錯誤回呼。</param>
      <param name="context">內容。</param>
      <param name="cancelToken">取消權杖。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立剖析指定物件的工作。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要剖析的物件。</param>
      <param name="consumer">取用者。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.RazorParser.DesignTimeMode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得設計時間模式。</summary>
      <returns>設計時間模式。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.IO.TextReader)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析指定的物件。</summary>
      <returns>剖析器結果。</returns>
      <param name="input">要剖析的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.IO.TextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析指定的物件。</summary>
      <param name="input">要剖析的物件。</param>
      <param name="visitor">訪客。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析指定的物件。</summary>
      <returns>剖析器結果。</returns>
      <param name="input">要剖析的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.LookaheadTextReader)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析指定的物件。</summary>
      <returns>剖析器結果。</returns>
      <param name="input">要剖析的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.LookaheadTextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析指定的物件。</summary>
      <param name="input">要剖析的物件。</param>
      <param name="visitor">訪客。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.EndCommentSequence">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.StartCommentSequence">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TextTagName">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TransitionCharacter">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TransitionString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants.CSharp">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.ClassKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.ElseIfKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.FunctionsKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.HelperKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.InheritsKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.LayoutKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.NamespaceKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.SectionKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.SessionStateKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.UsingKeywordLength">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants.VB">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.CodeKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndCodeKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndFunctionsKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndHelperKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndSectionKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.ExplicitKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.FunctionsKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.HelperKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.ImportsKeywordLength">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.LayoutKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.OffKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SectionKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SelectCaseKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SessionStateKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.StrictKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.TokenizerBackedParser`3">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Tokenizer 支援的剖析器。</summary>
      <typeparam name="TTokenizer">Tokenize 類型。</typeparam>
      <typeparam name="TSymbol">符號的類型。</typeparam>
      <typeparam name="TSymbolType">SymbolTyp 類型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.TokenizerBackedParser`3" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Accept(System.Collections.Generic.IEnumerable{`1})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受符號清單</summary>
      <param name="symbols">符號清單。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Accept(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受指定的符號。</summary>
      <param name="symbol">要接受的符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptAll(`2[])">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析器是否接受所有類型的 Tokenizer。</summary>
      <returns>true 表示剖析器接受所有類型的 Tokenizer，否則為 false。</returns>
      <param name="types">類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptAndMoveNext">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析器是否接受並移動到下一個 Tokenizer。</summary>
      <returns>true 表示剖析器接並移動到下一個 Tokenizer，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptSingleWhiteSpaceCharacter">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析器是否接受單一空白字元。</summary>
      <returns>true 表示剖析器接受單一空白字元，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受語彙基元直到找到給定類型的語彙基元。</summary>
      <param name="type">語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2,`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受語彙基元直到找到給定類型的語彙基元且其會成為備份，這樣下一個語彙基元也會是給定的類型。</summary>
      <param name="type1">第一個語彙基元的類型。</param>
      <param name="type2">第二個語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2,`2,`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受給定的語彙基元直到找到給定類型的語彙基元。</summary>
      <param name="type1">第一個語彙基元的類型。</param>
      <param name="type2">第二個語彙基元的類型。</param>
      <param name="type3">第三個語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2[])">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受語彙基元直到找到給定類型的語彙基元。</summary>
      <param name="types">語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(System.Func{`1,System.Boolean})">
      <summary>當未逹到條件時接受語彙基元。</summary>
      <param name="condition">條件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。找不到給定類型的語彙基元時接受語彙基元。</summary>
      <param name="type">語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2,`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。當未逹到指定類型的語彙基元時接受語彙基元。</summary>
      <param name="type1">第一個語彙基元的類型。</param>
      <param name="type2">第二個語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2,`2,`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。當未逹到指定類型的語彙基元時接受語彙基元。</summary>
      <param name="type1">第一個語彙基元的類型。</param>
      <param name="type2">第二個語彙基元的類型。</param>
      <param name="type3">第三個語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2[])">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。當未逹到指定類型的語彙基元時接受語彙基元。</summary>
      <param name="types">類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhiteSpaceInLines">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析器是否接受行中的空白。</summary>
      <returns>如果剖析器接受行中的空白，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AddMarkerSymbolIfNecessary">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。若需要，新增標記符號。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AddMarkerSymbolIfNecessary(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。若需要，新增標記符號。</summary>
      <param name="location">新增符號的位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.At(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷語彙基元是否位於指定的類型。</summary>
      <returns>如果語彙基元位於指定的類型，為 true，否則為 false。</returns>
      <param name="type">型別。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AtIdentifier(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷語彙基元是否位於指定的識別項。</summary>
      <returns>如果語彙基元位於指定的識別項，為 true，否則為 false。</returns>
      <param name="allowKeywords">true 表示允許關鍵字，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Balance(System.Web.Razor.Parser.BalancingModes)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析是否平衡。</summary>
      <returns>如果剖析平衡，則為 true，否則為 false。</returns>
      <param name="mode">平衡模式。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Balance(System.Web.Razor.Parser.BalancingModes,`2,`2,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析是否平衡。</summary>
      <returns>如果剖析平衡，則為 true，否則為 false。</returns>
      <param name="mode">平衡模式。</param>
      <param name="left">左側剖析。</param>
      <param name="right">右側剖析。</param>
      <param name="start">模式的起點。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立指定的範圍。</summary>
      <param name="span">要建立的範圍。</param>
      <param name="start">要建立範圍的起點位置。</param>
      <param name="content">範圍內容。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ConfigureSpan(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。設定範圍。</summary>
      <param name="config">設定。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ConfigureSpan(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder}})">
      <summary>設定範圍。</summary>
      <param name="config">設定。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.CurrentLocation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前執行個體的目前位置。</summary>
      <returns>目前執行個體的目前位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.CurrentSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此執行個體的目前符號。</summary>
      <returns>此執行個體的目前符號。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.EndOfFile">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，表示 Tokenizer 是否在檔案結尾處。</summary>
      <returns>如果 Tokenizer 在檔案結尾處，則為true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.EnsureCurrent">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷是否要確定目前的剖析器。</summary>
      <returns>如果要確定目前的剖析器，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Expected(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示具備給定類型的預期語彙基元。</summary>
      <param name="type">型別。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Expected(`2[])">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示具備給定類型的預期語彙基元。</summary>
      <param name="types">類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.HandleEmbeddedTransition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。處理內嵌的轉換。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Initialize(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化給定的範圍。</summary>
      <param name="span">要初始化的範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷此執行個體是否位於內嵌的轉換中。</summary>
      <returns>如果此執行個體位於內嵌的轉換，則為 true，否則為 false。</returns>
      <param name="allowTemplatesAndComments">true 表示允許範本和調解，否則為 false。</param>
      <param name="allowTransitions">true 表示允許轉換，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Language">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得剖析使用的語言。</summary>
      <returns>剖析使用的語言。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(System.Func{`1,System.Boolean})">
      <summary>判斷是否會傳送具有給定條件的語彙基元。</summary>
      <returns>如果會傳送具有給定條件的語彙基元，為 true，否則為 false。</returns>
      <param name="condition">條件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷是否會傳送具有給定類型的語彙基元。</summary>
      <returns>如果會傳送具有給定類型的語彙基元，為 true，否則為 false。</returns>
      <param name="type">語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(`2[])">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷是否會傳送具有給定類型的語彙基元。</summary>
      <returns>如果會傳送具有給定類型的語彙基元，為 true，否則為 false。</returns>
      <param name="types">類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextToken">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析器是否進階到下一個語彙基元。</summary>
      <returns>如果剖析器進階到下一個語彙基元，為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Optional(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析具有給定類型的語彙基元是否為可選。</summary>
      <returns>如果具有給定類型的語彙基元為可選，為 true，否則為 false。</returns>
      <param name="type">語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Optional(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷剖析具有給定類型的語彙基元是否為可選。</summary>
      <returns>如果具有給定類型的語彙基元為可選，為 true，否則為 false。</returns>
      <param name="type">語彙基元的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。輸出具有已接受字元的語彙基元。</summary>
      <param name="accepts">接受的字元。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.SpanKind)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。輸出具有範圍類型的語彙基元。</summary>
      <param name="kind">範圍類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.SpanKind,System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。輸出具有給字範圍類型以及已接受的字元的語彙基元。</summary>
      <param name="kind">範圍類型。</param>
      <param name="accepts">接受的字元。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.OutputSpanBeforeRazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 Razor 註解之前輸出範圍。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.PreviousSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此執行個體的之前符號。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。推入範圍設定。</summary>
      <returns>關閉設定的 <see cref="T:System.IDisposable" />。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。推入範圍設定。</summary>
      <returns>關閉設定的 <see cref="T:System.IDisposable" />。</returns>
      <param name="newConfig">新設定。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder}})">
      <summary>推入範圍設定。</summary>
      <returns>關閉設定的 <see cref="T:System.IDisposable" />。</returns>
      <param name="newConfig">新設定。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutBack(System.Collections.Generic.IEnumerable{`1})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。放回轉換。</summary>
      <param name="symbols">符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutBack(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。放回轉換。</summary>
      <param name="symbol">符號。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutCurrentBack">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。放回目前轉換。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.RazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。顯示 Razor 註解。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ReadWhile(System.Func{`1,System.Boolean})">
      <summary>當未逹到條件時讀取語彙基元。</summary>
      <returns>要讀取的語彙基元。</returns>
      <param name="condition">條件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Required(`2,System.Boolean,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷預期的語彙基元是否必要。</summary>
      <returns>如果預期的語彙基元為必要，則為 true，否則為 false。</returns>
      <param name="expected">預期語彙基元。</param>
      <param name="errorIfNotFound">true 表示如果找不到則顯示錯誤，否則為 false。</param>
      <param name="errorBase">錯誤基本。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Span">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定與這個執行個體相關聯的 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" />。</summary>
      <returns>與此執行個體相關的 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" />。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.SpanConfig">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或進行範圍設定。</summary>
      <returns>範圍設定。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Tokenizer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Tokenizer。</summary>
      <returns>Tokenizer。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Was(`2)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷是否會剖析具有給定類型的語彙基元。</summary>
      <returns>如果會剖析具有給定類型的語彙基元，為 true，否則為 false。</returns>
      <param name="type">語彙基元的類型。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.VBCodeParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Visual Basic 程式碼剖析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.VBCodeParser" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.AcceptVBSpaces">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 VB 程式碼中接受空格。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Assert(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在程式碼中檢查條件並顯示關鍵字。</summary>
      <param name="keyword">關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.AssertDirective(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷提示指定的指示詞。</summary>
      <param name="directive">指定的指示詞。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.At(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指示詞是否為 ‘AT’ 指示詞。</summary>
      <returns>如果指示詞為 ‘AT’ 指示詞，則為 true，否則為 false。</returns>
      <param name="directive">指示詞。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.At(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷給定的關鍵字是否為 ‘AT’。</summary>
      <returns>如果給定的關鍵字為 ‘AT’，則為 true，否則為 false。</returns>
      <param name="keyword">關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedDirective(System.String,System.Web.Razor.Parser.SyntaxTree.BlockType,System.Web.Razor.Generator.SpanCodeGenerator,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。結束終止的指示詞。</summary>
      <returns>結束終止指示詞的函式。</returns>
      <param name="directive">指示詞。</param>
      <param name="blockType">區塊類型。</param>
      <param name="codeGenerator">程式碼產生器。</param>
      <param name="allowMarkup">true 表示允許標記，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedDirectiveBody(System.String,System.Web.Razor.Text.SourceLocation,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷終止指示詞主體是否已結束。</summary>
      <returns>如果終止指示詞主體已結束，則為 true，否則為 false。</returns>
      <param name="directive">指示詞。</param>
      <param name="blockStart">區塊開始。</param>
      <param name="allowAllTransitions">true 表示允許所有轉換，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedStatement(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Boolean,System.Boolean)">
      <summary>結束終止陳述式。</summary>
      <returns>結束終止的函式。</returns>
      <param name="keyword">關鍵字。</param>
      <param name="supportsExit">如果終止支援存在，則為 true，否則為 false。</param>
      <param name="supportsContinue">如果終止支援繼續，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedStatement(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Boolean,System.Boolean,System.String)">
      <summary>結束終止陳述式。</summary>
      <returns>結束終止的函式。</returns>
      <param name="keyword">關鍵字。</param>
      <param name="supportsExit">如果終止支援存在，則為 true，否則為 false。</param>
      <param name="supportsContinue">如果終止支援繼續，則為 true，否則為 false。</param>
      <param name="blockName">區塊名稱。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleEmbeddedTransition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。處理內嵌的轉換。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleEmbeddedTransition(System.Web.Razor.Tokenizer.Symbols.VBSymbol)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。處理內嵌的轉換。</summary>
      <param name="lastWhiteSpace">最後的空白字元。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleExitOrContinue(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示處理 Exit 或 Continue 關鍵字的程式碼。</summary>
      <param name="keyword">關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleTransition(System.Web.Razor.Tokenizer.Symbols.VBSymbol)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示處理轉換的程式碼。</summary>
      <param name="lastWhiteSpace">最後的空白字元。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HelperDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示程式碼是否為 Helper 指示詞。.</summary>
      <returns>如果程式碼為 Helper 指示詞，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ImportsStatement">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否匯入陳述式。</summary>
      <returns>如果程式碼匯入陳述式，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.InheritsStatement">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否繼承陳述式。</summary>
      <returns>如果程式碼繼承陳述式，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指示詞是否位於內嵌的轉換中。</summary>
      <returns>如果程式碼位於內嵌的轉換，則為 true，否則為 false。</returns>
      <param name="allowTemplatesAndComments">true 表示允許範本和調解，否則為 false。</param>
      <param name="allowTransitions">true 表示允許轉換，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.IsDirectiveDefined(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否為定義的指示詞。</summary>
      <returns>如果程式碼為為定義的指示詞，則為 true，否則為 false。</returns>
      <param name="directive">指示詞。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.Keywords">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得與程式碼相關聯的關鍵字。</summary>
      <returns>與程式碼相關聯的關鍵字。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.KeywordTerminatedStatement(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Boolean,System.Boolean)">
      <summary>表示終止陳述式的關鍵字。</summary>
      <returns>終止陳述式的函式。</returns>
      <param name="start">起點。</param>
      <param name="terminator">終止符。</param>
      <param name="supportsExit">如果終止支援存在，則為 true，否則為 false。</param>
      <param name="supportsContinue">如果終止支援繼續，則為 true，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.Language">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得剖析器的語言。</summary>
      <returns>剖析器的語言。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.LayoutDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否為版面配置指示詞。</summary>
      <returns>如果程式碼為版面配置指示詞，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.MapDirective(System.String,System.Func{System.Boolean})">
      <summary>對應指定的指示詞。</summary>
      <param name="directive">指示詞。</param>
      <param name="action">動作是否對應給定的指示詞。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.MapKeyword(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Func{System.Boolean})">
      <summary>對應指定的關鍵字。</summary>
      <param name="keyword">關鍵字。</param>
      <param name="action">動作是否對應給定的關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.NestedBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示巢狀區塊。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Optional(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼中的關鍵字是否為選用。</summary>
      <returns>如果為程式碼中的關鍵字為選用，則為 true，否則為 false。</returns>
      <param name="keyword">關鍵字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OptionStatement">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否為選用陳述式。</summary>
      <returns>如果程式碼為選用陳述式，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.OtherParser">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得其他剖析器。</summary>
      <returns>其他剖析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OtherParserBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示剖析器區塊。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OtherParserBlock(System.String,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示剖析器區塊。</summary>
      <param name="startSequence">開始序列。</param>
      <param name="endSequence">結束序列。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OutputSpanBeforeRazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 Razor 註解之前展開的的輸出。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ParseBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。封鎖剖析。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ReadVBSpaces">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。讀取 Visual Basic 空間的清單。</summary>
      <returns>Visual Basic 空間的清單。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Required(System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷預期的符號是否必要。</summary>
      <returns>如果預期的符號為必要，則為 true，否則為 false。</returns>
      <param name="expected">預期的符號。</param>
      <param name="errorBase">錯誤基本。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ReservedWord">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否為保留文字。</summary>
      <returns>如果程式碼為為保留文字，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.SectionDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否為區段指示詞。</summary>
      <returns>如果程式碼為區段指示詞，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.SessionStateDirective">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷程式碼是否為工作階段狀態指示詞。</summary>
      <returns>如果程式碼為工作階段狀態指示詞，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.VBLanguageCharacteristics">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Visual Basic 語言的特性。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 Visual Basic 標記符號。</summary>
      <returns>建立的 Visual Basic 標記符號。</returns>
      <param name="location">建立符號的位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 Visual Basic 符號。</summary>
      <returns>建立的 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" />。</returns>
      <param name="location">建立符號的位置。</param>
      <param name="content">內容。</param>
      <param name="type">符號的類型。</param>
      <param name="errors">錯誤。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 Visual Basic Tokenizer.。</summary>
      <returns>建立的 <see cref="T:System.Web.Razor.Tokenizer.VBTokenizer" />。</returns>
      <param name="source">建立 Tokenizer 的來源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。翻轉給定的括弧。</summary>
      <returns>Visual Basic 符號的類型。</returns>
      <param name="bracket">要翻轉的括弧。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。擷取已知符號的類型。</summary>
      <returns>已知符號的類型。</returns>
      <param name="type">要擷取的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得給定類型的範例符號。</summary>
      <returns>給定類型的範例符號。</returns>
      <param name="type">符號的類型。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.VBLanguageCharacteristics.Instance">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此 <see cref="T:System.Web.Razor.Parser.VBLanguageCharacteristics" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.VBLanguageCharacteristics" /> 的執行個體。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.AllWhiteSpace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.Any">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.AnyExceptNewline">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.NewLine">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.None">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.NonWhiteSpace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.WhiteSpace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示自動完成編輯處理常式類別。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler" /> 類別的新執行個體。</summary>
      <param name="tokenizer">Tokenizer。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler" /> 類別的新執行個體。</summary>
      <param name="tokenizer">Tokenizer。</param>
      <param name="accepted">接受的字元。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.AutoCompleteAtEndOfSpan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定表示自動完成函式是否在此範圍結尾的值。</summary>
      <returns>如果自動完成函式在此範圍結尾，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.AutoCompleteString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定要自動完成的字串值。</summary>
      <returns>要自動完成的字串值。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示可以接受變更的剖析結果。</summary>
      <param name="target">目標階段。</param>
      <param name="normalizedChange">
        <see cref="T:System.Web.Razor.Text.TextChange" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示此執行個體和指定物件是否相同。</summary>
      <returns>如果 <paramref name="obj" /> 和此執行個體相同且表示相同的值，則為true，否則為 false。</returns>
      <param name="obj">物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊程式碼之 32 位元簽署的整數。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此執行個體的完整限定類型名稱。</summary>
      <returns>包含完整限定類型名稱的字串。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.Block">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示建立網頁的區塊。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.#ctor(System.Web.Razor.Parser.SyntaxTree.BlockBuilder)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.Block" /> 類別的新執行個體。</summary>
      <param name="source">區塊建立器的來源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受區塊的剖析器訪客。</summary>
      <param name="visitor">剖析器訪客。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Children">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 SyntaxTreeNode 的集合以檢視區塊子系。</summary>
      <returns>SyntaxTreeNode 的集合以檢視區塊子系。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.CodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 IBlockCodeGenerator 以產生元素程式碼。</summary>
      <returns>IBlockCodeGenerator 以產生元素程式碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的區塊。</summary>
      <returns>若指定的物件等於目前區塊，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回表示區塊是否等於相同元素的值。</summary>
      <returns>如果區塊等於相同元素，則為 true，否則為 false。</returns>
      <param name="node">語法樹狀目錄節點。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.FindFirstDescendentSpan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。尋找區塊的第一個遞減範圍。</summary>
      <returns>區塊的第一個遞減範圍。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.FindLastDescendentSpan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。尋找區塊的最後一個遞減範圍。</summary>
      <returns>區塊的最後一個遞減範圍。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Flatten">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。簡維區塊的指定類型集合。</summary>
      <returns>要簡維的區塊指定類型集合。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.IsBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，這個值表示物件是否為區塊層級物件。</summary>
      <returns>如果物件為區塊層級物件，為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Length">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得區塊的長度值。</summary>
      <returns>區塊的長度值。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.LocateOwner(System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。尋找區塊擁有者。</summary>
      <returns>要尋找的區塊擁有者。</returns>
      <param name="change">文字變更。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Name">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得區塊的字串名稱。</summary>
      <returns>區塊的字串名稱。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Start">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得開始以識別區塊的指定位置。</summary>
      <returns>開始以識別區塊的指定位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回表示目前物件的字串。</summary>
      <returns>表示目前物件的字串。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Type">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得程式碼區塊的類型。</summary>
      <returns>程式碼區塊的類型。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.BlockBuilder">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示網頁的區塊建立器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.BlockBuilder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.#ctor(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.BlockBuilder" /> 類別的新執行個體。</summary>
      <param name="original">原始區塊產生器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Build">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立執行個體的區塊。</summary>
      <returns>此執行個體的區塊建置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Children">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得區塊建立器子元素的集合。</summary>
      <returns>區塊建立器子元素的集合。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.CodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定區塊建立器的程式碼產生器。</summary>
      <returns>區塊建立器的程式碼產生器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Name">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定區塊建立器的字串名稱。</summary>
      <returns>區塊建立器的字串名稱。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Reset">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在原始位置重設區塊建立器。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Type">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定可以指派為 Null 的區塊類型。</summary>
      <returns>可以指派為 Null 的區塊類型。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.BlockType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Comment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Directive">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Expression">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Functions">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Helper">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Markup">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Section">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Statement">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Template">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.RazorError">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 中的剖析錯誤。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Int32,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 類別的新執行個體。</summary>
      <param name="message">錯誤訊息。</param>
      <param name="absoluteIndex">來源位置的絕對索引。</param>
      <param name="lineIndex">來源位置的行索引。</param>
      <param name="columnIndex">來源位置的欄位索引。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 類別的新執行個體。</summary>
      <param name="message">錯誤訊息。</param>
      <param name="absoluteIndex">來源位置的絕對索引。</param>
      <param name="lineIndex">來源位置的行索引。</param>
      <param name="columnIndex">來源位置的欄位索引。</param>
      <param name="length">錯誤的長度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 類別的新執行個體。</summary>
      <param name="message">錯誤訊息。</param>
      <param name="location">錯誤的來源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Web.Razor.Text.SourceLocation,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 類別的新執行個體。</summary>
      <param name="message">錯誤訊息。</param>
      <param name="location">錯誤的來源位置。</param>
      <param name="length">錯誤的長度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此執行個體。</summary>
      <returns>如果指定的物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="obj">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.Equals(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此執行個體。</summary>
      <returns>如果指定的物件等於此執行個體，為 true，否則為 false。</returns>
      <param name="other">要與此執行個體比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Length">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定錯誤的長度。</summary>
      <returns>錯誤的長度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Location">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得錯誤的來源位置。</summary>
      <returns>錯誤的來源位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Message">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定錯誤訊息。</summary>
      <returns>錯誤訊息。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此錯誤執行個體的字串表示式。</summary>
      <returns>此錯誤執行個體的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.Span">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 剖析樹狀目錄節點，包含區塊節點的所有內容。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.#ctor(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.Span" /> 類別的新執行個體。</summary>
      <param name="builder">要用於此範圍的建立器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受指定訪客的造訪。</summary>
      <param name="visitor">執行來訪的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Change(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。變更此範圍的範圍建立器。</summary>
      <param name="changes">會與此變更一併執行的委派。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。設定此範圍的起點字元位置。</summary>
      <param name="newStart">要為此範圍設定的起點位置。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.CodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍的程式碼產生器。</summary>
      <returns>範圍的程式碼產生器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Content">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍的內容。</summary>
      <returns>範圍內容。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.EditHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍編輯的處理常式。</summary>
      <returns>範圍編輯的處理常式。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於此範圍。</summary>
      <returns>如果指定的物件等於此範圍，為 true，否則為 false。</returns>
      <param name="obj">要與此範圍比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的節點是否等於此範圍。</summary>
      <returns>如果指定的節點等於此範圍，為 true，否則為 false。</returns>
      <param name="node">要與此範圍比較的節點。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此範圍的雜湊碼。</summary>
      <returns>此目前範圍的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.IsBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定值，這個值表示節點是否為區塊節點。</summary>
      <returns>false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Kind">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定此範圍的類型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanKind" /> 列舉的其中一個值。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Length">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍內容的長度。</summary>
      <returns>範圍內容的長度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Next">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定樹狀目錄節點的下一個範圍。</summary>
      <returns>樹狀目錄節點的下一個範圍。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Previous">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定樹狀目錄節點的上一個範圍。</summary>
      <returns>樹狀目錄節點的上一個範圍。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ReplaceWith(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。以指定的範圍建立器取代此範圍的範圍建立器。</summary>
      <param name="builder">要用於此範圍的新建立器。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Start">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍的起點字元位置。</summary>
      <returns>範圍的起點字元位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Symbols">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定用來產生範圍程式碼的符號。</summary>
      <returns>用來產生範圍程式碼的符號。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前範圍的字串表示式。</summary>
      <returns>此目前範圍的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。代表語法樹狀目錄中的範圍建立器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.#ctor(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 類別的新執行個體。</summary>
      <param name="original">原始範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Accept(System.Web.Razor.Tokenizer.Symbols.ISymbol)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受範圍建立器的給定符號。</summary>
      <param name="symbol">符號建立器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Build">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立此執行個體的範圍建立器。</summary>
      <returns>此執行個體的範圍建立器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.ClearSymbols">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。清除範圍建立器符號。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.CodeGenerator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍程式碼產生器。</summary>
      <returns>範圍程式碼產生器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.EditHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定建立器的範圍編輯處理常式。</summary>
      <returns>建立器的範圍編輯處理常式。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Kind">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍建立器的範圍種類。</summary>
      <returns>範圍建立器的範圍種類。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Reset">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。重設範圍建立器。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Start">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定範圍建立器的來源位置。</summary>
      <returns>範圍建立器的來源位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Symbols">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得泛型唯讀集合的符號。</summary>
      <returns>泛型唯讀集合的符號。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SpanKind">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Code">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Comment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Markup">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.MetaCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Transition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。代表語法樹狀目錄中的節點。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受樹狀目錄節點的訪客。</summary>
      <param name="visitor">剖析器訪客。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示語法樹狀目錄節點是否等於給定的節點。</summary>
      <returns>true 表示語法樹狀目錄節點等於給定的節點，否則為 false。</returns>
      <param name="node">給定的節點。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.IsBlock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得表示語法樹狀目錄節點是否為區塊層級物件的值。</summary>
      <returns>若語法樹狀目錄節點為區塊層級物件，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Length">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得語法樹狀目錄節點的長度。</summary>
      <returns>語法樹狀目錄節點的長度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Parent">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前語法樹狀目錄節點的父語法樹狀目錄節點。</summary>
      <returns>目前語法樹狀目錄節點的父語法樹狀目錄節點。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Start">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得語法樹狀目錄節點的指定來源位置。</summary>
      <returns>語法樹狀目錄節點的指定來源位置。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.BufferingTextReader">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供文字讀取器的 lookahead 緩衝。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.#ctor(System.IO.TextReader)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.BufferingTextReader" /> 類別的新執行個體。</summary>
      <param name="source">建立器的文字讀取器。</param>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.BeginLookahead">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。開始此 <see cref="T:System.Web.Razor.Text.BufferingTextReader" /> 的 lookahead 緩衝作業。</summary>
      <returns>結束 lookhead 緩衝的可處置動作。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.CancelBacktrack">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。捨棄與 lookhead 緩衝作業相關聯的支援內容。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.BufferingTextReader.CurrentCharacter">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得緩衝中目前的字元。</summary>
      <returns>緩衝中的目前字元。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.BufferingTextReader.CurrentLocation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定緩衝中的字元目前位置。</summary>
      <returns>緩衝中的字元目前位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Dispose(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。釋放此類別的目前執行個體所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.ExpandBuffer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從文字讀取器讀取下一個字元，並附加到 lookhead 緩衝。</summary>
      <returns>如果從文字讀取器讀取下一個字元為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.NextCharacter">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在下一個字元之前的緩衝位置。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Peek">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回緩衝中目前的字元。</summary>
      <returns>緩衝中的目前字元。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Read">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從緩衝傳回目前字元，並將緩衝位置進階到下一個字元。</summary>
      <returns>緩衝中的目前字元。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.ITextBuffer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextBuffer.Length">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.ITextBuffer.Peek">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextBuffer.Position">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.ITextBuffer.Read">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.ITextDocument">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextDocument.Location">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.LocationTagged`1">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示標記的位置。</summary>
      <typeparam name="T">標記的位置類型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.#ctor(`0,System.Int32,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.LocationTagged`1" /> 類別的新執行個體。</summary>
      <param name="value">來源的值。</param>
      <param name="offset">位移。</param>
      <param name="line">行。</param>
      <param name="col">來源的欄位置。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.#ctor(`0,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.LocationTagged`1" /> 類別的新執行個體。</summary>
      <param name="value">來源的值。</param>
      <param name="location">來源的位置。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.LocationTagged`1.Location">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定來源的位置。</summary>
      <returns>來源的位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Equality(System.Web.Razor.Text.LocationTagged{`0},System.Web.Razor.Text.LocationTagged{`0})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩種 <see cref="T:System.Web.Razor.Text.LocationTagged{`0}" /> 物件是否相同。</summary>
      <returns>若兩個物件相同，為 true，否則為 false。</returns>
      <param name="left">要比較的第一個物件。</param>
      <param name="right">要比較的第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Implicit(System.Web.Razor.Text.LocationTagged{`0})~`0">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將指定的值轉換為 <see cref="T:System.Web.Razor.Text.LocationTagged`1" /> 物件。</summary>
      <returns>如果成功轉換，則為 true，否則為 false。</returns>
      <param name="value">要轉換的值。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Inequality(System.Web.Razor.Text.LocationTagged{`0},System.Web.Razor.Text.LocationTagged{`0})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩種 <see cref="T:System.Web.Razor.Text.LocationTagged{`0}" /> 物件是否不相同。</summary>
      <returns>若兩個物件相同，為 true，否則為 false。</returns>
      <param name="left">要比較的第一個物件。</param>
      <param name="right">要比較的第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>表示目前執行個體的字串。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.ToString(System.String,System.IFormatProvider)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的字串表示式。</summary>
      <returns>表示目前執行個體的字串。</returns>
      <param name="format">格式。</param>
      <param name="formatProvider">格式提供者。</param>
    </member>
    <member name="P:System.Web.Razor.Text.LocationTagged`1.Value">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定來源的值。</summary>
      <returns>來源的值。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.LookaheadTextReader">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.BeginLookahead">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.CancelBacktrack">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.LookaheadTextReader.CurrentLocation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.LookaheadToken">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示要尋找 Razor 的語彙基元。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.#ctor(System.Action)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.LookaheadToken" /> 類別的新執行個體。</summary>
      <param name="cancelAction">要取消的動作。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Accept">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受語彙基元。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Dispose">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將 <see cref="T:System.Web.Razor.Text.LookaheadToken" /> 目前的執行個體的資源全部釋出。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Dispose(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。釋放 <see cref="T:System.Web.Razor.Text.LookaheadToken" /> 所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="T:System.Web.Razor.Text.SeekableTextReader">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示讀取器。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.IO.TextReader)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.SeekableTextReader" /> 類別的新執行個體。</summary>
      <param name="source">來源讀取器。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.SeekableTextReader" /> 類別的新執行個體。</summary>
      <param name="content">字串內容。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.Web.Razor.Text.ITextBuffer)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.SeekableTextReader" /> 類別的新執行個體。</summary>
      <param name="buffer">文字緩衝。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Length">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得要讀取的文字長度。</summary>
      <returns>要讀取的文字長度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Location">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得文字讀取器的位置來源。</summary>
      <returns>文字讀取器的位置來源。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.Peek">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。讀取下一個字元無需變更讀取器或字元來源的狀態。</summary>
      <returns>表示要讀取的下一個字元的整數。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Position">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定位置以搜尋文字讀取器。</summary>
      <returns>搜尋文字讀取器的位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.Read">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從文字讀取器讀取下一個字元，並前進一個字元的位置。</summary>
      <returns>如果沒有其他字元，則為文字讀取器中的下一個字元，或 -1。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.SourceLocation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示來源位置。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.#ctor(System.Int32,System.Int32,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 類別的新執行個體。</summary>
      <param name="absoluteIndex">絕對索引。</param>
      <param name="lineIndex">行索引。</param>
      <param name="characterIndex">字元索引。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.AbsoluteIndex">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得來源位置的絕對索引。</summary>
      <returns>來源位置的絕對索引。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Add(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。新增兩個 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 物件。</summary>
      <returns>兩個 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 物件的總和。</returns>
      <param name="left">要新增的第一個物件。</param>
      <param name="right">要新增的第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Advance(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將特定物件放在指定位置。</summary>
      <returns>來源位置。</returns>
      <param name="left">放置物件的位置。</param>
      <param name="text">放在指定位置的文字。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.CharacterIndex">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得來源位置的字元索引。</summary>
      <returns>來源位置的字元索引。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.CompareTo(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。比較目前物件和其他物件。</summary>
      <returns>比較的物件值。</returns>
      <param name="other">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Equals(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷目前的 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 物件是否等於其他的 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 物件。</summary>
      <returns>若目前物件等於其他物件，則為 true，否則為 false。</returns>
      <param name="other">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>此執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.LineIndex">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得來源位置的行索引。</summary>
      <returns>來源位置的行索引。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Addition(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。新增兩個 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 物件。</summary>
      <returns>兩個物件的總和為 <see cref="T:System.Web.Razor.Text.SourceLocation" />。</returns>
      <param name="left">要新增的物件。</param>
      <param name="right">要新增的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Equality(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。決定兩種物件是否相同。</summary>
      <returns>若兩個物件相同，為 true，否則為 false。</returns>
      <param name="left">要比較的第一個物件。</param>
      <param name="right">要比較的第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_GreaterThan(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷第一個物件是否大於第二個物件。</summary>
      <returns>如果第一個物件大於第二個物件，為 true，否則為 false。</returns>
      <param name="left">第一個物件。</param>
      <param name="right">第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Inequality(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷兩種 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 物件是否不相同。</summary>
      <returns>若兩個物件相同，為 true，否則為 false。</returns>
      <param name="left">要比較的物件。</param>
      <param name="right">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_LessThan(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷第一個物件是否小於第二個物件。</summary>
      <returns>如果第一個物件大於第二個物件，為 true，否則為 false。</returns>
      <param name="left">第一個物件。</param>
      <param name="right">第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Subtraction(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <returns>傳回 <see cref="T:System.Web.Razor.Text.SourceLocation" />。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Subtract(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將第一個物件擷取至第二個物件。</summary>
      <returns>兩個物件的差異。</returns>
      <param name="left">第一個物件。</param>
      <param name="right">第二個物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回來源位置的字串表示式。</summary>
      <returns>來源位置的字串表示式。</returns>
    </member>
    <member name="F:System.Web.Razor.Text.SourceLocation.Undefined">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Text.SourceLocation.Zero">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.SourceLocationTracker">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供來源位置追蹤程式。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.#ctor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.SourceLocationTracker" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.#ctor(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.SourceLocationTracker" /> 類別的新執行個體。</summary>
      <param name="currentLocation">來源的目前位置。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.CalculateNewLocation(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。計算來源的新位置。</summary>
      <returns>新來源位置。</returns>
      <param name="lastPosition">最後位置。</param>
      <param name="newContent">新內容。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocationTracker.CurrentLocation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定來源的目前位置。</summary>
      <returns>來源的目前位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.UpdateLocation(System.Char,System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。更新來源位置。</summary>
      <param name="characterRead">要讀取的字元。</param>
      <param name="nextCharacter">要更新的字元。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.UpdateLocation(System.String)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。更新來源的位置。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Text.SourceLocationTracker" /> 物件。</returns>
      <param name="content">來源內容。</param>
    </member>
    <member name="T:System.Web.Razor.Text.TextBufferReader">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供文字緩衝讀取器。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.#ctor(System.Web.Razor.Text.ITextBuffer)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.TextBufferReader" /> 類別的新執行個體。</summary>
      <param name="buffer">要讀取的文字緩衝。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.BeginLookahead">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。開始讀取目前的文字緩衝。</summary>
      <returns>停止文字緩衝的 <see cref="T:System.IDisposable" /> 執行個體。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.CancelBacktrack">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取消支援。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextBufferReader.CurrentLocation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得文字緩衝的目前位置。</summary>
      <returns>文字緩衝的目前位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Dispose(System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。釋放 <see cref="T:System.Web.Razor.Text.TextBufferReader" /> 類別所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Peek">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回要讀取的下一個文字緩衝。</summary>
      <returns>下一個要讀取的文字緩衝。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Read">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。讀取目前的文字緩衝。</summary>
      <returns>目前的文字緩衝。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.TextChange">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。描述文字變更作業。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.#ctor(System.Int32,System.Int32,System.Web.Razor.Text.ITextBuffer,System.Int32,System.Int32,System.Web.Razor.Text.ITextBuffer)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.TextChange" /> 類別的新執行個體。</summary>
      <param name="oldPosition">快照中的文字位置會在變更前立即在快照中變更。</param>
      <param name="oldLength">舊文字的長度。</param>
      <param name="oldBuffer">舊文字緩衝。</param>
      <param name="newPosition">快照中的文字位置會在變更後立即在快照中變更。</param>
      <param name="newLength">新文字的長度。</param>
      <param name="newBuffer">新文字緩衝。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ApplyChange(System.String,System.Int32)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。套用指定的文字變更。</summary>
      <returns>包含文字值的字串。</returns>
      <param name="content">文字內容。</param>
      <param name="changeOffset">變更位移。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。套用指定的文字變更。</summary>
      <returns>包含文字值的字串。</returns>
      <param name="span">文字變更範圍。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此文字變更的雜湊碼。</summary>
      <returns>此文字變更的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsDelete">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得指出此文字變更是否為刪除的值。</summary>
      <returns>如果此文字變更為刪除，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsInsert">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得指出此文字變更是否為插入的值。</summary>
      <returns>如果此文字變更為插入，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsReplace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得指出此文字變更是否為取代的值。</summary>
      <returns>如果此文字變更為取代，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewBuffer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定新文字緩衝。</summary>
      <returns>新文字緩衝。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewLength">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定新文字長度。</summary>
      <returns>新文字的長度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewPosition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定快照中的文字位置會在變更後立即在快照中變更。</summary>
      <returns>快照中的文字位置會在變更後立即在快照中變更。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewText">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定取代舊文字的文字。</summary>
      <returns>取代舊文字的文字。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.Normalize">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此文字變更的標準化值。</summary>
      <returns>此文字變更的標準化值。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldBuffer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定舊文字緩衝。</summary>
      <returns>舊文字緩衝。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldLength">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定舊文字長度。</summary>
      <returns>舊文字的長度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldPosition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定快照中的文字位置會在變更前立即在快照中變更。</summary>
      <returns>快照中的文字位置會在變更前立即在快照中變更。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldText">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定已取代的文字。</summary>
      <returns>已取代的文字。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.op_Equality(System.Web.Razor.Text.TextChange,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。決定兩種文字變更是否相同。</summary>
      <returns>若兩個文字變更相同，為 true，否則為 false。</returns>
      <param name="left">左側文字變更。</param>
      <param name="right">右側文字變更。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.op_Inequality(System.Web.Razor.Text.TextChange,System.Web.Razor.Text.TextChange)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。決定兩種文字變更是否不相同。</summary>
      <returns>若兩個文字變更不相同，為 true，否則為 false。</returns>
      <param name="left">左側文字變更。</param>
      <param name="right">右側文字變更。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此文字變更的字串表示式。</summary>
      <returns>此文字變更的字串表示式。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.TextChangeType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Text.TextChangeType.Insert">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Text.TextChangeType.Remove">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.TextDocumentReader">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供文字文件讀取器。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Text.TextDocumentReader" /> 類別的新執行個體。</summary>
      <param name="source">要讀取的來源。</param>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Length">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得文件長度。</summary>
      <returns>文件長度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Location">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得文件位置。</summary>
      <returns>文件位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.Peek">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回要讀取的下一個文字文件。</summary>
      <returns>要讀取的下一個文字文件。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Position">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定文字文件位置。</summary>
      <returns>文字文件位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.Read">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。讀取指定的文字文件。</summary>
      <returns>文字文件。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.CSharpHelpers">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。提供 CSharp Tokenizer Helper 函式。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsIdentifierPart(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定字元是否可以用於識別項。</summary>
      <returns>如果指定字元可以用於識別項，則為 true，否則為 false。</returns>
      <param name="character">要檢查的字元。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsIdentifierStart(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定字元是否可以用於識別項起點字元。</summary>
      <returns>如果指定字元可以用於識別項起點字元，則為 true，否則為 false。</returns>
      <param name="character">要檢查的字元。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsRealLiteralSuffix(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的字元是否為實際數字的文字尾碼。</summary>
      <returns>如果指定的字元為實際數字的文字尾碼，則為 true，否則為 false。</returns>
      <param name="character">要檢查的字元。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.CSharpTokenizer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 CSharp Tokenizer。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.CSharpTokenizer" /> 類別的新執行個體。</summary>
      <param name="source">來源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 CSharp Tokenizer 符號。</summary>
      <returns>CSharp Tokenizer 符號。</returns>
      <param name="start">來源位置的起點。</param>
      <param name="content">內容。</param>
      <param name="type">CSharp 符號類型。</param>
      <param name="errors">Razor 錯誤集合。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentStarType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的星號類型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的星號類型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentTransitionType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 註解轉換類型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 註解轉換類型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 註解類型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 註解類型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.StartState">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得機器的狀態。</summary>
      <returns>機器的狀態。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.HtmlTokenizer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor 的 HTML Tokenizer。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.HtmlTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.HtmlTokenizer" /> 類別的新執行個體。</summary>
      <param name="source">文字文件來源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.HtmlTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立 HTML Tokenizer 的指定參數符號。</summary>
      <returns>建立 HTML Tokenizer 的指定參數的符號。</returns>
      <param name="start">來源位置。</param>
      <param name="content">內容字串。</param>
      <param name="type">HTML 符號的類型。</param>
      <param name="errors">Razor 錯誤。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentStarType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Razor 註解星號類型的 HTML 符號。</summary>
      <returns>Razor 註解星號類型的 HTML 符號。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentTransitionType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Razor 註解轉換類型的 HTML 符號。</summary>
      <returns>Razor 註解轉換類型的 HTML 符號。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Razor 註解類型的 HTML 符號。</summary>
      <returns>Razor 註解類型的 HTML 符號。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.StartState">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 HTML 機器的開始狀態。</summary>
      <returns>HTML 機器的開始狀態。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.ITokenizer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.ITokenizer.NextSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Tokenizer`2">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
      <typeparam name="TSymbol">語言符號的類型。</typeparam>
      <typeparam name="TSymbolType">語言符號的列舉類型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Tokenizer`2" /> 類別的新執行個體。</summary>
      <param name="source">來源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.AfterRazorCommentTransition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。在 Razor 註解轉換之後傳回結果。</summary>
      <returns>結果。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.At(System.String,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷 lookhead 緩衝是否包含預期的字串。</summary>
      <returns>若 lookhead 緩衝包含預期的字串則為 true，否則為 false。</returns>
      <param name="expected">要檢查的字串。</param>
      <param name="caseSensitive">true 表示比較區分大小寫，否則為 false。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.Buffer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 Tokenizer 的緩衝。</summary>
      <returns>Tokenizer 的緩衝。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.CharOrWhiteSpace(System.Char)">
      <summary>傳回函式委派，其會接受字元參數並傳回一值，表示字元參數是否等於指定字元或空白。</summary>
      <returns>函式委派。</returns>
      <param name="character">要比較的字元。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,`1,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。以指定的內容來建立 Tokenizer 語言符號類型。</summary>
      <returns>Tokenizer 的語言符號類型。</returns>
      <param name="start">來源位置的起點。</param>
      <param name="content">內容值。</param>
      <param name="type">符號類型。</param>
      <param name="errors">Razor 錯誤。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentCharacter">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Tokenizer 中目前的字元。</summary>
      <returns>目前的字元。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentErrors">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前 Razor 錯誤的清單。</summary>
      <returns>目前錯誤的清單。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentLocation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得目前來源位置。</summary>
      <returns>目前來源位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentStart">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得來源位置的目前起點。</summary>
      <returns>來源位置的目前起點。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.EndOfFile">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，表示 Tokenizer 目前位置是否在檔案結尾處。</summary>
      <returns>如果 Tokenizer 目前位置在檔案結尾處，則為true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.EndSymbol(System.Web.Razor.Text.SourceLocation,`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回 Tokenizer 使用的語言結尾符號類型。</summary>
      <returns>語言結尾符號類型。</returns>
      <param name="start">來源位置的起點。</param>
      <param name="type">語言符號的列舉類型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.EndSymbol(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回 Tokenizer 使用的語言結尾符號類型。</summary>
      <returns>語言結尾符號類型。</returns>
      <param name="type">語言符號的列舉類型。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.HaveContent">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，表示 Tokenizer 是否具有內容。</summary>
      <returns>若Tokenizer 具有內容，為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.MoveNext">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從程式碼讀取器讀取下一個字元。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.NextSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。顯示要使用的下一個符號。</summary>
      <returns>要使用的下一個符號。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Peek">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。讀取程式碼中的下一個符號。</summary>
      <returns>要讀取的下一個符號。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentBody">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。剖析 Razor 註解主體。</summary>
      <returns>表示結果狀態的物件。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentStarType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Razor 註解的星號類型。</summary>
      <returns>Razor 註解的星號類型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentTransitionType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Razor 註解的轉換類型。</summary>
      <returns>Razor 註解的轉換類型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Razor 註解的類型。</summary>
      <returns>Razor 註解的類型。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Reset">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將 Tokenizer 狀態設為其初始狀態。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.ResumeSymbol(`0)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用先前的語言符號類型繼續。</summary>
      <param name="previous">先前的語言符號類型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Single(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。使用符號的單一類型。</summary>
      <returns>符號的單一類型。</returns>
      <param name="type">符號的類型。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.Source">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得文字文件來源。</summary>
      <returns>來源文件來源。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.StartSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此類別使用的起點符號。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.System#Web#Razor#Tokenizer#ITokenizer#NextSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回下一個語言符號類型。</summary>
      <returns>下一個語言符號類型。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeAll(System.String,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。若在 lookhead 緩衝中找到字串，則用於 Tokenizer 緩衝。</summary>
      <returns>若 lookhead 緩衝包含預期的字串則為 true，否則為 false。</returns>
      <param name="expected">要符合的字串。</param>
      <param name="caseSensitive">true 表示比較區分大小寫，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeCurrent">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受緩衝中目前的字元。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeString(System.String,System.Boolean)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。接受緩衝中給定的輸入字串。</summary>
      <returns>true 表示接受全部輸入字串；若只接受子字串，則為 false。</returns>
      <param name="input">輸入字串。</param>
      <param name="caseSensitive">true 表示比較區分大小寫，否則為 false。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeUntil(System.Func{System.Char,System.Boolean})">
      <summary>剖析來源文件，直到符合述詞指定的條件或達到結尾檔案。</summary>
      <returns>如果符合述詞條件，則為true，否則為 false。</returns>
      <param name="predicate">指定處理條件的述詞。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.TokenizerView`3">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Tokenizer 檢視的指定參數。</summary>
      <typeparam name="TTokenizer">類型 Tokenizer。</typeparam>
      <typeparam name="TSymbol">類型符號。</typeparam>
      <typeparam name="TSymbolType">語彙基元符號類型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.#ctor(`0)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.TokenizerView`3" /> 類別的新執行個體。</summary>
      <param name="tokenizer">Tokenizer 檢視。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Current">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 TSymbol 的目前檢視。</summary>
      <returns>TSymbol 的目前檢視。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.EndOfFile">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得值，表示檢視是否可以達到檔案結尾。</summary>
      <returns>如果檢視可以達到檔案結尾，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.Next">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷 Tokenizer 是否移動到下一個檢視。</summary>
      <returns>如果 Tokenizer 移動到下一個檢視則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.PutBack(`1)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。將指定符號放到 Tokenizer 檢視中。</summary>
      <param name="symbol">符號。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Source">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Tokenizer 檢視的文字文件來源。</summary>
      <returns>Tokenizer 檢視的文字文件來源。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Tokenizer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Tokenizer 以檢視 Razor 符號。</summary>
      <returns>Tokenizer 以檢視 Razor 符號。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.VBHelpers">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 VB 中做為 Helper 的字元集。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsDoubleQuote(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示一值，指出是否指定的字元有包括在雙括號中 (")。</summary>
      <returns>如果指定的字元有包括在雙括號中 (")，則為 true，否則為 false。</returns>
      <param name="character">字元。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsOctalDigit(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示值，指出字元是否位於八位元數字。</summary>
      <returns>若字元位於八位元數字，為 true，否則為 false。</returns>
      <param name="character">字元。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsSingleQuote(System.Char)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示一值，指出是否指定的字元有包括在單括號中 (')。</summary>
      <returns>如果指定的字元有包括在單括號中 (')，則為 true，否則為 false。</returns>
      <param name="character">字元。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.VBTokenizer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。允許應用程式斷開 VB 符號為語彙基元。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.VBTokenizer" /> 類別的新執行個體。</summary>
      <param name="source">文字來源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。建立符號網域。</summary>
      <returns>符號網域。</returns>
      <param name="start">來源位置。</param>
      <param name="content">內容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
      <param name="errors">Razor 錯誤。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentStarType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 VB 符號類型。</summary>
      <returns>VB 符號類型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentTransitionType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 VB 符號的轉換樣式。</summary>
      <returns>VB 符號的轉換樣式。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" /> 的 Razor 類型註解。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" /> 的 Razor 類型註解。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.StartState">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得機器的開始狀態。</summary>
      <returns>機器的開始狀態。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Abstract">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.As">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Base">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Bool">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Break">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Byte">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Case">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Catch">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Char">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Checked">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Class">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Const">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Continue">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Decimal">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Default">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Delegate">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Do">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Double">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Else">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Enum">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Event">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Explicit">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Extern">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.False">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Finally">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Fixed">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Float">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.For">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Foreach">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Goto">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.If">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Implicit">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.In">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Int">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Interface">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Internal">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Is">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Lock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Long">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Namespace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.New">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Null">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Object">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Operator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Out">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Override">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Params">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Private">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Protected">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Public">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Readonly">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ref">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Return">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sbyte">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sealed">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Short">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sizeof">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Stackalloc">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Static">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.String">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Struct">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Switch">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.This">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Throw">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.True">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Try">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Typeof">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Uint">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ulong">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Unchecked">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Unsafe">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ushort">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Using">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Virtual">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Void">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Volatile">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.While">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Razor Tokenizer 的 C sharp 符號。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 類別的新執行個體。</summary>
      <param name="offset">符號的位移。</param>
      <param name="line">行。</param>
      <param name="column">欄</param>
      <param name="content">符號的內容。</param>
      <param name="type">符號的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 類別的新執行個體。</summary>
      <param name="offset">符號的位移。</param>
      <param name="line">行。</param>
      <param name="column">欄</param>
      <param name="content">符號的內容。</param>
      <param name="type">符號的類型。</param>
      <param name="errors">錯誤清單。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 類別的新執行個體。</summary>
      <param name="start">開始符號的位置。</param>
      <param name="content">符號的內容。</param>
      <param name="type">符號的類型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 類別的新執行個體。</summary>
      <param name="start">開始符號的位置。</param>
      <param name="content">符號的內容。</param>
      <param name="type">符號的類型。</param>
      <param name="errors">錯誤清單。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為 true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.EscapedIdentifier">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定值，這個值表示符號是否己逸出識別碼。</summary>
      <returns>如果符號己逸出識別碼，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此執行個體的雜湊碼。</summary>
      <returns>此目前執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.Keyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定語言關鍵字。</summary>
      <returns>語言關鍵字。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.And">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.AndAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Arrow">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Assign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.CharacterLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Colon">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Comma">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Comment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Decrement">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DivideAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Dot">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleAnd">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleColon">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleOr">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Equals">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.GreaterThan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.GreaterThanEqual">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Hash">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Identifier">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Increment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.IntegerLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Keyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftBrace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftBracket">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftParenthesis">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftShift">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftShiftAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LessThan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LessThanEqual">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Minus">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.MinusAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Modulo">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.ModuloAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.MultiplyAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NewLine">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Not">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NotEqual">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NullCoalesce">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Or">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.OrAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Plus">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.PlusAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.QuestionMark">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorCommentStar">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorCommentTransition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RealLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightBrace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightBracket">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightParenthesis">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightShift">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightShiftAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Semicolon">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Slash">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Star">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.StringLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Tilde">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Transition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Unknown">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.WhiteSpace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Xor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.XorAssign">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 HTML 符號。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 類別的新執行個體。</summary>
      <param name="offset">符號的位置。</param>
      <param name="line">找到符號的該行。</param>
      <param name="column">找到符號的欄位號碼。</param>
      <param name="content">內容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 類別的新執行個體。</summary>
      <param name="offset">符號的位置。</param>
      <param name="line">找到符號的該行。</param>
      <param name="column">找到符號的欄位號碼。</param>
      <param name="content">內容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
      <param name="errors">Razor 錯誤。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 類別的新執行個體。</summary>
      <param name="start">來源位置的起點。</param>
      <param name="content">內容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 類別的新執行個體。</summary>
      <param name="start">來源位置的起點。</param>
      <param name="content">內容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
      <param name="errors">Razor 錯誤。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Bang">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.CloseAngle">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Colon">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.DoubleHyphen">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.DoubleQuote">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Equals">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.LeftBracket">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.NewLine">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.OpenAngle">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.QuestionMark">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorCommentStar">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorCommentTransition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RightBracket">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.SingleQuote">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Solidus">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Text">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Transition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Unknown">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.WhiteSpace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.ISymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Web Razor 符號的介面。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.ISymbol.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。變更符號的位置。</summary>
      <param name="newStart">符號的新位置。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.ISymbol.Content">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得符號的內容。</summary>
      <returns>符號的內容。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.ISymbol.OffsetStart(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示符號的開始位移。</summary>
      <param name="documentStart">開始文件的位置。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.ISymbol.Start">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得符號的位置。</summary>
      <returns>符號的位置。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentBody">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentStar">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentStart">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Identifier">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Keyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.NewLine">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Transition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Unknown">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.WhiteSpace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示符號的新執行個體。</summary>
      <typeparam name="TType">一般型別。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.#ctor(System.Web.Razor.Text.SourceLocation,System.String,`0,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 類別的新執行個體。</summary>
      <param name="start">來源位置。</param>
      <param name="content">內容值。</param>
      <param name="type">型別。</param>
      <param name="errors">Razor 錯誤。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。變更機器的起點。</summary>
      <param name="newStart">新起點。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Content">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 的內容。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 的內容。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。判斷指定的物件是否等於目前的物件。</summary>
      <returns>若指定的物件等於目前物件，則為 true，否則為 false。</returns>
      <param name="obj">物件。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Errors">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得 Razor 錯誤。</summary>
      <returns>Razor 錯誤。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。擷取以目前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 物件為基準的雜湊碼。</summary>
      <returns>目前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.OffsetStart(System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。啟勳來源位置的時間位移。</summary>
      <param name="documentStart">文件起點。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Start">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得來源位置的起點。</summary>
      <returns>來源位置的起點。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.ToString">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。產生目前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 物件的字串表示式。</summary>
      <returns>目前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 物件的字串表示式。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Type">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得繼承自基本類型的類型。</summary>
      <returns>繼承自基本類型的類型。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 Web Tokenizer 的擴充程式符號。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol},System.Web.Razor.Text.SourceLocation)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</summary>
      <returns>取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</returns>
      <param name="symbols">要提供的符號。</param>
      <param name="spanStart">範圍的起點索引。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</summary>
      <returns>取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</returns>
      <param name="span">具有給定範圍的交集。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Func{System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol},System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</summary>
      <returns>取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</returns>
      <param name="span">具有給定範圍的交集。</param>
      <param name="filter">選擇的符號清單。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Tokenizer.Symbols.ISymbol)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</summary>
      <returns>取得此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 類別的內容。</returns>
      <param name="symbol">提供的符號。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBKeyword">
      <summary>列舉 Visual Basic 關鍵字的清單。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AddHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AddressOf">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Alias">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.And">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AndAlso">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.As">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Boolean">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ByRef">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Byte">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ByVal">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Call">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Case">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Catch">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CBool">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CByte">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CChar">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDate">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDbl">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDec">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Char">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CInt">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Class">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CLng">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CObj">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Const">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Continue">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CSByte">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CShort">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CSng">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CStr">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CUInt">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CULng">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CUShort">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Date">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Decimal">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Declare">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Default">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Delegate">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Dim">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.DirectCast">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Do">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Double">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Each">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Else">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ElseIf">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.End">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.EndIf">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Enum">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Erase">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Error">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Event">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Exit">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.False">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Finally">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.For">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Friend">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Function">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Get">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GetType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GetXmlNamespace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Global">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GoSub">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GoTo">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Handles">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.If">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Implements">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Imports">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.In">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Inherits">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Integer">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Interface">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Is">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.IsNot">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Let">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Lib">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Like">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Long">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Loop">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Me">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Mod">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Module">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MustInherit">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MustOverride">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MyBase">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MyClass">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Namespace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Narrowing">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.New">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Next">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Not">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Nothing">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.NotInheritable">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.NotOverridable">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Object">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Of">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.On">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Operator">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Option">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Optional">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Or">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.OrElse">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overloads">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overridable">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overrides">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ParamArray">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Partial">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Private">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Property">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Protected">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Public">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.RaiseEvent">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ReadOnly">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ReDim">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Rem">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.RemoveHandler">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Resume">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Return">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.SByte">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Select">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Set">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Shadows">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Shared">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Short">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Single">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Static">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Step">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Stop">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.String">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Structure">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Sub">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.SyncLock">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Then">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Throw">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.To">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.True">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Try">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.TryCast">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.TypeOf">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.UInteger">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ULong">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.UShort">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Using">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Variant">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Wend">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.When">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.While">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Widening">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.With">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.WithEvents">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.WriteOnly">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Xor">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。表示 VB 符號元件。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 類別的新執行個體。</summary>
      <param name="offset">位移值。</param>
      <param name="line">行值。</param>
      <param name="column">欄位值。</param>
      <param name="content">內容字串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 類別的新執行個體。</summary>
      <param name="offset">位移值。</param>
      <param name="line">行值。</param>
      <param name="column">欄位值。</param>
      <param name="content">內容字串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
      <param name="errors">Razor 錯誤清單。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 類別的新執行個體。</summary>
      <param name="start">來源位置的起點。</param>
      <param name="content">內容字串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 類別的新執行個體。</summary>
      <param name="start">來源位置的起點。</param>
      <param name="content">內容字串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
      <param name="errors">Razor 錯誤清單。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.Equals(System.Object)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。指出目前物件是否等於新物件的值。</summary>
      <returns>若目前物件等於新物件的值，則為true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.GetHashCode">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。傳回此目前執行個體的雜湊碼。</summary>
      <returns>要傳回的雜湊碼。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.GetSample(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。從物件中取得指定的資料範例。</summary>
      <returns>物件中指定的資料範例。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.VBSymbol.Keyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。取得或設定 VB 所使用的關鍵字。</summary>
      <returns>使用的關鍵字。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Add">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Bang">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.CharacterLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Colon">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Comma">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Comment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Concatenation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.DateLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Divide">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Dollar">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Dot">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Equal">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Exponentiation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.FloatingPointLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.GreaterThan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Hash">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Identifier">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.IntegerDivide">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.IntegerLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Keyword">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftBrace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftBracket">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftParenthesis">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LessThan">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LineContinuation">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Multiply">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.NewLine">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.QuestionMark">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorComment">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorCommentStar">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorCommentTransition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightBrace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightBracket">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightParenthesis">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.StringLiteral">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Subtract">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Transition">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Unknown">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.WhiteSpace">
      <summary>這個類型/成員可以支援 .NET Framework 基礎結構，但不能直接使用於您的程式碼中。</summary>
    </member>
  </members>
</doc>