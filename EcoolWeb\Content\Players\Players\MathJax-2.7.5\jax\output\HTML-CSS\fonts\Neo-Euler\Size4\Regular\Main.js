/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Size4/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Size4={directory:"Size4/Regular",family:"NeoEulerMathJax_Size4",testString:"\u00A0\u2016\u2044\u2215\u221A\u2223\u2225\u2308\u2309\u230A\u230B\u2329\u232A\u23DC\u23DD",32:[0,0,333,0,0],40:[2799,199,790,236,768],41:[2799,199,790,22,554],47:[2799,200,1277,50,1228],91:[2874,125,583,275,571],92:[2799,200,1277,50,1228],93:[2874,125,583,11,307],123:[2799,200,806,144,661],124:[3098,208,213,86,126],125:[2799,200,806,144,661],160:[0,0,333,0,0],8214:[3098,208,403,86,316],8260:[2799,200,1277,50,1228],8725:[2799,200,1277,50,1228],8730:[3002,1,1000,111,1023],8739:[3098,208,213,86,126],8741:[2498,208,403,86,316],8968:[2799,200,638,275,627],8969:[2799,200,638,11,363],8970:[2799,200,638,275,627],8971:[2799,200,638,11,363],9001:[2730,228,803,137,694],9002:[2730,228,859,109,666],9180:[814,-293,3111,56,3055],9181:[264,257,3111,56,3055],9182:[962,-445,3111,56,3055],9183:[110,407,3111,56,3055],10216:[2134,232,757,123,648],10217:[2134,232,818,100,625]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Size4"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size4/Regular/Main.js"]);
