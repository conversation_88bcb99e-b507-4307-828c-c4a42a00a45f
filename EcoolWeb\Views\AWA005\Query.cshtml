﻿@model EcoolWeb.Models.AWA005QueryViewModel
@{
    ViewBag.Title = "酷幣總數排行榜";
}
@using (Html.BeginForm("Query", "AWA005", FormMethod.Post, new { id = "AWA005" }))
{
    <a href='@Url.Action("Awat2Q02", "Awat2")'>
        <img src="~/Content/img/web-student-allpage-38button.png" />
    </a>
    <br />
    <span>搜尋瀏覽請輸入相關字串[學號/姓名]:</span>
    @Html.EditorFor(m => m.whereKeyword)
    <input type="submit" value="搜尋" />
    <br />
    <div class="pager">
        @Html.Pager(Model.VAWA005List.PageSize, Model.VAWA005List.PageNumber, Model.VAWA005List.TotalItemCount)
        @*@Html.Pager(Model.ADDT01List.PageSize,Model.ADDT01List.PageNumber,Model.ADDT01List.TotalItemCount).Options(o=>o.AddRouteValueFor(m=>m.whereKeyword))*@
        @*Displaying @<EMAIL> of @Model.ADDT01List.TotalItemCount item(s)*@
    </div>

    @section scripts{
        <script>
            $(function () {

                // Fields
                var _pageLinkers = $(".pager> a");

                // Binding click event
                _pageLinkers.each(function (i, item) {
                    var page = getParameterByName($(item).attr('href'), 'page')
                    $(item).attr('href', '#').click(function () { postPage(page); });
                });

            });

            function getParameterByName(url, name) {
                name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
                var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
                    results = regex.exec(url);
                return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
            }

            function postPage(page) {
                var targetFormID = '#AWA005';
                if ($(targetFormID).size() > 0) {
                    $('<input>')
                        .attr({ type: 'hidden', id: 'page', name: 'page', value: page })
                        .appendTo($(targetFormID));
                    $(targetFormID).submit();
                }
            };

        </script>
    }
}
<style>
    .table thead > tr > th, .table tbody > tr > th, .table tfoot > tr > th, .table thead > tr > td, .table tbody > tr > td, .table tfoot > tr > td {
        border-top: none;
    }
</style>
<img src="~/Content/img/web-Bar-18.png" />
<div class="TitleBarDiv"></div>
<div class="ListDiv">
    <table class="table" style="white-space: nowrap;">
        <tr class="ListColName">
            <th style="text-align: center;">
                班級
            </th>
            <th style="text-align: center;">
                座號
            </th>
            <th style="text-align: center;">
                姓名
            </th>
            <th style="text-align: center;">
                現有酷幣
            </th>
            <th style="text-align: center;">
                累計酷幣
            </th>
            <th>

            </th>
        </tr>

        @foreach (var item in Model.VAWA005List)
        {
            <tr class="ListRow">
                <td>
                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.NAME)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.CASH_AVAILABLE)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.CASH_ALL)
                </td>
                <td></td>
            </tr>
        }

    </table>
</div>