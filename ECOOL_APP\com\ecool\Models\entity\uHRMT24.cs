﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uHRMT24
    {

        ///Summary
        ///ROLE_ID
        ///Summary
        [Display(Name = "角色代碼")]
        [Required]
        [StringLength(20)]
        public string ROLE_ID { get; set; }

        ///Summary
        ///ROLE_NAME
        ///Summary
        [Display(Name = "角色名稱")]
        [StringLength(510)]
        public string ROLE_NAME { get; set; }

        ///Summary
        ///CHG_PERSON
        ///Summary
        [Display(Name = "修改人員")]
        [StringLength(40)]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [Display(Name = "修改日期")]
        public DateTime CHG_DATE { get; set; }

       
        ///Summary
        ///ROLE_TYPE => 1. Admin 2.跨校級 3.校級 4.學生 (用來對應HRMT01.USER_TYPE=S)
        ///Summary
        [Display(Name = "角色類別")]
        public int? ROLE_TYPE { get; set; }


        ///Summary
        ///ROLE_LEVEL
        ///Summary
        [Display(Name = "角色等級")]
        public decimal? ROLE_LEVEL { get; set; }



    }

}
