﻿@model EcoolWeb.ViewModels.AWA002QueryViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@using (Html.BeginForm("Query", "AWA002", FormMethod.Post, new { id = "AWA002" }))
{
    if (user != null)
    {
        if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
        {

            @Html.ActionLink("各類酷幣給點統計", "Query", null, new { @class = "btn btn-sm btn-sys active" })
            @Html.ActionLink("學生e酷幣給點紀錄", "Query2", null, new { @class = "btn btn-sm btn-sys" })
            @Html.ActionLink("學生數位存摺", "Query5", null, new { @class = "btn btn-sm btn-sys" })
        }

    }


    if (user != null)
    {

        if (user.USER_TYPE == UserType.Student)
        {
            @Html.ActionLink("我的數位存摺", "Query2", new { WhereIsPassbook = true }, new { @class = "btn btn-sm btn-sys" })

        }

    }



    if (user != null)
    {

        if (user.USER_TYPE == UserType.Student)
        {

            @Html.ActionLink("我的酷幣給點統計", "Query", null, new { @class = "btn btn-sm btn-sys active" })
        }
    }
    <br />

    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">日期區間</label>
        </div>
        <div class="form-group">
            <div class="row">
                <div class="col-xs-3">
                    @Html.EditorFor(m => m.whereLOG_TIME_S, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
                </div>
                <div class="col-xs-1">
                    至
                </div>
                <div class="col-xs-3">
                    @Html.EditorFor(m => m.whereLOG_TIME_E, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
                </div>
                <div class="col-xs-1">
                </div>
            </div>
        </div>
    </div>
    <br />
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">學號/姓名/班級/異動說明</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.Page)
            @Html.Hidden("User_No", Request["User_No"])
        </div>
        <div class="form-group">
            <label class="control-label">年級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

}

@if (AppMode == false)
{
    <div class="row">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="ToExcel()">匯出excel</button>
        </div>
    </div>
}
<img src="~/Content/img/web-bar2-revise-bk2.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<table class="table-ecool table-92Per table-hover table-ecool-AWA003">
    <thead>
        <tr>
            <th align="center">
                學號
            </th>
            <th align="center">
                班級
            </th>
            <th align="center">
                座號
            </th>
            <th align="center">
                姓名
            </th>
            <th align="center">
                進帳點數
            </th>
            <th align="center">
                異動說明
            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.VAWA002List)
        {
            <tr>
                <td align="center">

                    @if (item.USER_NO.Length >= 10)
                    {
                        if (user.RoleID_Default == HRMT24_ENUM.SuperAdminROLE)
                        {
                            @Html.DisplayFor(modelItem => item.USER_NO)
                        }
                        else
                        {
                            @ECOOL_APP.com.ecool.util.StringHelper.LeftStringR(item.USER_NO, 5, "*****")
                        }
                    }
                    else
                    {
                        @Html.DisplayFor(modelItem => item.USER_NO)
                    }
                </td>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                </td>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                </td>
                <td align="center">
                    @Html.ActionLink(item.NAME, "Query2", "AWA002", new { USER_NO = item.USER_NO, LogDESC = item.LOG_DESC }, new { @class = "btn-table-link" })
                </td>
                @if (item.LOG_DESC == "銀行定存")
                {
                    <td align="center">
                        @( (ViewBag.Time_Deposit + ViewBag.Demand).ToString("#,0"))
                    </td>

                }
                else
                {
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SumCASH_IN)
                    </td>

                }

                <td style="text-align: left;white-space:normal">
                    @Html.DisplayFor(modelItem => item.LOG_DESC)
                </td>
            </tr>
        }
    </tbody>
</table>
<div>
    @Html.Pager(Model.VAWA002List.PageSize, Model.VAWA002List.PageNumber, Model.VAWA002List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>

@section scripts{
    <script>
        var targetFormID = '#AWA002';

        function FunPageProc(page) {

            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function ToExcel() {
            $(targetFormID).attr('action', '@Url.Action("PrintExcel_Query", "AWA002")').attr('target', '_blank');
            $(targetFormID).submit();
        };

        function todoClear() {

            ////重設
            $("#OrderList").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }

        $(document).ready(function () {

            $("#whereLOG_TIME_S,#whereLOG_TIME_E").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "both",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });
        });
    </script>
}