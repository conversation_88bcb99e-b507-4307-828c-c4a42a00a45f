﻿@model GAAI01SysSetWearIndexViewModel

    @{
        ViewBag.Title = ViewBag.Panel_Title;

        ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    }

    @Html.Partial("_Title_Secondary")
    @Html.Partial("_Notice")

    @{
        Html.RenderAction("_PageMenu", new { NowAction = "SysSetWearIndex" });
    }

    @using (Html.BeginForm("SysSetWearIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
    {

        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(m => m.SCHOOL_NO)
        <div class="panel panel-ZZZ" name="TOP">
            <div class="panel-heading text-center">
                @Html.BarTitle(null, (string)ViewBag.Title)
            </div>
            <div class="panel-body">
                <div class="form-horizontal">
                    <div class="form-group row">
                        @Html.LabelFor(m => m.ALARM_NOTICE, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(m => m.ALARM_NOTICE, new { cols = "200", rows = "15", @class = "form-control form-control-required", @placeholder = "請勿超過250個字" })
                        </div>
                        <div class="col-md-9">
                            @Html.ValidationMessageFor(m => m.ALARM_NOTICE, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group row">
                        @Html.LabelFor(m => m.ALARM_CASH, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                        <div class="col-md-9">
                            @Html.EditorFor(m => m.ALARM_CASH, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "請填數字，最小可輸入0" } })
                        </div>
                        <div class="col-md-9">
                            @Html.ValidationMessageFor(m => m.ALARM_CASH, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    @if (user?.ROLE_TYPE <= HRMT24_ENUM.RoleTypeVal.ExceedLevel)
                    {
                        <div class="form-group row">
                            @Html.LabelFor(m => m.FIRST_DAY, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                            <div class="col-md-9">
                                @Html.EditorFor(m => m.FIRST_DAY, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                            </div>
                            <div class="col-md-9">
                                @Html.ValidationMessageFor(m => m.FIRST_DAY, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    }
                </div>
            </div>
            <div class="text-center">
                <hr />
                <button type="button" class="btn btn-default" onclick="onSave()">
                    <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
                </button>
            </div>
        </div>

    }

    @section Scripts {
        <script language="JavaScript">

        var targetFormID = '#form1';

        function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("SysSetWearIndexSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        $(document).ready(function () {

            $("#@Html.IdFor(m=>m.FIRST_DAY)").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });
        });
        </script>
    }