﻿@using System.Collections;
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "批次閱讀認證-上傳結果";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@model List<ADDT06>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="alert alert-dismissible alert-danger">
    <button class="close" type="button" data-dismiss="alert">×</button>
    <strong>
        @if (Model.Count() == ViewBag.SumCount)
        {
            <samp> 批次代閱讀認證作業 - 全部成功</samp>
        }
        else if (ViewBag.SumCount == ViewBag.ErrCount)
        {
            <samp>批次代閱讀認證作業 - 全部失敗</samp>
        }
        else
        {
            <samp>批次代閱讀認證作業 - 有一部份失敗</samp>
        }

    </strong>
</div>


@using (Html.BeginForm("Query2", "ADDI02", FormMethod.Post, new { id = "ADDI02" }))
{

    <img src="~/Content/img/web-bar2-revise-20.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="Details">
            <div class="table-responsive">
                <div class="text-center">
                    <table class="table-ecool table-92Per table-hover">
                        <caption class="Caption_Div_Left">
                            失敗清單：
                        </caption>
                        <thead>
                            <tr>
                                <th>檔名</th>
                                <th>訊息</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in (Dictionary<string, string>)ViewBag.FailList)
                            {
                                if (item.Value != string.Empty && item.Value != null)
                                {
                                    <tr>
                                        <td>@item.Key</td>
                                        <td>@item.Value</td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                    <div style="height:15px"></div>
                    <div class="btn-group btn-group-justified" role="group">
                        共 @ViewBag.ErrCount 人
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="height:50px"></div>
    <div class="Div-EZ-reader">
        <div class="Details">
            <div class="table-responsive">
                <div class="text-center">
                    <table class="table-ecool table-92Per table-hover">
                        <caption class="Caption_Div_Left">
                            成功清單： <font color="#FF0000">現在進行推薦作業</font></caption>
                        <thead>
                            <tr>
                                <th>
                                    申請日期
                                </th>
                                <th>
                                    學年
                                </th>
                                <th>
                                    學期
                                </th>
                                <th>
                                    班級
                                </th>
                                <th>
                                    座號
                                </th>
                                <th>
                                    姓名
                                </th>
                                <th>
                                    書名
                                </th>
                                <th>
                                    推薦
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @{ int rowIndex = 0; }
                            @foreach (var item in Model)
                            {
                                @Html.HiddenFor(model => Model[rowIndex].APPLY_NO)
                                @Html.HiddenFor(model => Model[rowIndex].CRE_DATE)
                                @Html.HiddenFor(model => Model[rowIndex].SYEAR)
                                @Html.HiddenFor(model => Model[rowIndex].SEMESTER)
                                @Html.HiddenFor(model => Model[rowIndex].CLASS_NO)
                                @Html.HiddenFor(model => Model[rowIndex].SEAT_NO)
                                @Html.HiddenFor(model => Model[rowIndex].SNAME)
                                @Html.HiddenFor(model => Model[rowIndex].BOOK_NAME)

                                <tr>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.SYEAR)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.SEMESTER)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.BOOK_NAME)
                                    </td>
                                    <td>
                                        @{bool SHARE_YN = item.SHARE_YN == "Y"; }
                                        @Html.CheckBox($"[{rowIndex}].SHARE_YN", new { @class = "chkSHARE_YN", value = "Y" })
                                    </td>
                                </tr>
                                rowIndex++;
                            }
                        </tbody>
                    </table>
                    <div style="height:15px"></div>
                    <div class="btn-group btn-group-justified" role="group">
                        共 @Model.Count() 人
                    </div>
                    <div>
                        <button type="submit" class="btn btn-default">推薦設定完成</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

}
<div style="height:15px"></div>
<div class="text-center">
    <a href='@Url.Action("UPLOAD", "ADDI02")' role="button" class="btn btn-default">
        回批次閱讀認證首頁
    </a>
</div>


