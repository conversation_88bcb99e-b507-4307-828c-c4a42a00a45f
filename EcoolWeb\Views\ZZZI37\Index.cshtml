﻿@model ZZZI37IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<br />

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")
<img src="~/Content/img/web-bar2-revise-CHRIS.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>


<div class="Div-EZ-ZZZI09">
    <div class="form-horizontal">
        @using (Html.BeginForm("ExportResultView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", target = "_blank" }))
        {
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.DropDownListFor(m => m.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm" })
                    @Html.ValidationMessageFor(m => m.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group text-left">
                @Html.LabelFor(model => model.CRE_DATEs, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.EditorFor(m => m.CRE_DATEs, new { htmlAttributes = new { @class = "form-control input-sm", @readonly = "readonly", @type = "text" } })
                </div>
            </div>
            <div class="form-group text-left">
                @Html.LabelFor(model => model.CRE_DATEe, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.EditorFor(m => m.CRE_DATEe, new { htmlAttributes = new { @class = "form-control input-sm", @readonly = "readonly", @type = "text" } })
                </div>
            </div>
            <div class="form-group text-left">

                @Html.LabelFor(model => model.CountryNO, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">

                    @Html.DropDownListFor(m => m.CountryNO, (IEnumerable<SelectListItem>)ViewBag.CountryNo, new { @class = "form-control input-sm" })
                    @Html.ValidationMessageFor(m => m.CountryNO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-offset-3 col-md-9 text-center">
                    <button type="submit" class="btn btn-default btn-block">匯出</button>
                </div>
            </div>
        }
    </div>
</div>
<div style="padding-top:20px;padding-left:33px;">
    <a class="btn btn-default" id="ChrisDetail" role="button" href='@Url.Action("MaxDetail", "ADDI01")'>
        如何列印給「耶老的信」說明
    </a>
</div>
<div hidden="hidden" id="notice"></div>
<script type="text/javascript">
    var targetFormID = '#form1';
        $(document).ready(function () { $("#ChrisDetail").colorbox({ iframe: true, width: "50%", height: "100%", opacity: 0.82 }); });
    $("#@Html.IdFor(m=>m.CRE_DATEs),#@Html.IdFor(m=>m.CRE_DATEe)").datepicker({
        dateFormat: "yy/mm/dd",
        changeMonth: true,
        changeYear: true,
        showOn: "button",
        buttonImage: "../Content/img/icon/calendar.gif",
        buttonImageOnly: true,
    });
    function MaxDetail() {
        window.open('@Url.Action("MaxDetail", "ADDI01")', '_blank');

    }
</script>
