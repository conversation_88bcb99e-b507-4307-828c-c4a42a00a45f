/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Arrows/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Arrows-bold"]={directory:"Arrows/Bold",family:"STIXMathJax_Arrows",weight:"bold",testString:"\u00A0\u219C\u219D\u219F\u21A1\u21A4\u21A5\u21A7\u21A8\u21AF\u21B2\u21B3\u21B4\u21B5\u21B8",32:[0,0,250,0,0],160:[0,0,250,0,0],8604:[462,-72,956,66,890],8605:[462,-72,956,66,890],8607:[676,165,568,86,482],8609:[676,165,568,86,482],8612:[451,-55,977,68,909],8613:[676,165,584,94,490],8615:[676,165,584,94,490],8616:[732,196,584,94,490],8623:[683,154,562,68,494],8626:[686,170,584,45,503],8627:[686,170,584,81,539],8628:[686,162,960,66,894],8629:[686,171,960,56,904],8632:[768,170,977,68,911],8633:[618,114,977,68,909],8645:[676,165,864,66,798],8662:[662,156,926,54,872],8663:[662,156,926,54,872],8664:[662,156,926,54,872],8665:[662,156,926,54,872],8668:[451,-55,977,62,914],8678:[551,45,926,60,866],8679:[662,156,685,45,641],8680:[551,45,926,60,866],8681:[662,156,685,45,641],8682:[705,201,685,45,641],8693:[676,165,864,66,798],57524:[555,-209,282,42,239],57525:[555,-209,282,43,240],57526:[478,-56,0,15,142]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Arrows-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Arrows/Bold/Main.js"]);
