﻿@model ADDI13IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string LogoAct = "GuestIndex";
    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        { LogoAct = "StudentIndex"; }
        else
        { LogoAct = "TeacherIndex"; }

    }
}

@Html.Partial("_Notice")

@using (Html.BeginForm("IndexExcel", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>@ViewBag.Title</h1>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="control-label col-md-3">上傳Excel檔</label>
                        <div class="col-md-9">
                            <input class="btn btn-default" type="file" name="files" placeholder="必填" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
                            @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <br />
                    <div class="form-group text-center">
                        <button class="btn btn-default">送出</button>
                        <a class="btn btn-default" href="@Url.Action(LogoAct,"Home")">回首頁</a>
                    </div>
                    <div class="form-group">
                        <div class="col-md-offset-1 col-md-9">
                            <label class="text-danger">
                                上傳說明:<br />
                                <span>
                                    1.上傳之 Excel 檔, 請依規定格式填寫(<a href="@Url.Content("~/Content/ExcelSample/ADDI14Sample.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                                </span>
                                <br />
                                <span>
                                    2.檔名不要是中文、特殊符號之類
                                </span>
                                <br />
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';
    </script>
}