/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Size3={directory:"Size3/Regular",family:"STIXMathJax_Size3",testString:"\u00A0\u02C6\u02C7\u02DC\u02F7\u0302\u0303\u0305\u030C\u0330\u0332\u0338\u203E\u20D0\u20D1",32:[0,0,250,0,0],40:[2066,394,750,182,667],41:[2066,394,750,83,568],47:[2066,394,1101,30,1071],91:[2066,394,508,225,491],92:[2066,394,1101,30,1071],93:[2066,394,508,17,283],123:[2066,394,906,143,717],125:[2066,394,906,189,763],160:[0,0,250,0,0],710:[777,-564,1460,0,1460],711:[777,-564,1460,0,1460],732:[774,-608,1458,-2,1458],759:[-117,283,1458,-2,1458],770:[777,-564,1460,0,1460],771:[774,-608,1460,0,1460],773:[820,-770,2000,0,2000],780:[777,-564,1460,0,1460],816:[-117,283,1460,0,1460],818:[-127,177,2000,0,2000],824:[662,156,0,-543,-132],8254:[820,-770,2000,0,2000],8400:[749,-584,1744,0,1744],8401:[749,-584,1744,0,1744],8406:[735,-482,1744,0,1744],8407:[735,-482,1744,0,1744],8428:[-123,288,1744,0,1744],8429:[-123,288,1744,0,1744],8430:[-26,279,1744,0,1744],8431:[-26,279,1744,0,1744],8730:[2565,510,1076,112,1110],8731:[2565,510,1076,112,1110],8732:[2565,510,1076,112,1110],8968:[2066,394,565,225,550],8969:[2066,394,565,15,340],8970:[2066,394,565,225,550],8971:[2066,394,565,15,340],9140:[766,-544,2147,78,2069],9141:[139,83,2147,78,2069],9180:[70,161,1886,0,1886],9181:[803,-572,1886,0,1886],9182:[157,86,1886,0,1886],9183:[815,-572,1886,0,1886],9184:[66,212,2312,0,2312],9185:[842,-564,2312,0,2312],10098:[2066,393,842,265,790],10099:[2066,393,842,52,577],10214:[2066,394,647,225,597],10215:[2066,394,647,50,422],10216:[2066,394,765,96,670],10217:[2066,394,765,95,669],10218:[2067,394,1091,104,955],10219:[2067,394,1091,136,987],10627:[2066,394,1031,143,867],10628:[2066,394,1031,164,888],10629:[2066,393,1029,180,914],10630:[2066,393,1029,115,849]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Size3"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size3/Regular/Main.js"]);
