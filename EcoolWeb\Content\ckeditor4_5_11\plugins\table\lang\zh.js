﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'zh', {
	border: '框線大小',
	caption: '標題',
	cell: {
		menu: '儲存格',
		insertBefore: '前方插入儲存格',
		insertAfter: '後方插入儲存格',
		deleteCell: '刪除儲存格',
		merge: '合併儲存格',
		mergeRight: '向右合併',
		mergeDown: '向下合併',
		splitHorizontal: '水平分割儲存格',
		splitVertical: '垂直分割儲存格',
		title: '儲存格屬性',
		cellType: '儲存格類型',
		rowSpan: '列全長',
		colSpan: '行全長',
		wordWrap: '自動斷行',
		hAlign: '水平對齊',
		vAlign: '垂直對齊',
		alignBaseline: '基準線',
		bgColor: '背景顏色',
		borderColor: '框線顏色',
		data: '資料',
		header: '頁首',
		yes: '是',
		no: '否',
		invalidWidth: '儲存格寬度必須為數字。',
		invalidHeight: '儲存格高度必須為數字。',
		invalidRowSpan: '列全長必須是整數。',
		invalidColSpan: '行全長必須是整數。',
		chooseColor: '選擇'
	},
	cellPad: '儲存格邊距',
	cellSpace: '儲存格間距',
	column: {
		menu: '行',
		insertBefore: '左方插入行',
		insertAfter: '右方插入行',
		deleteColumn: '刪除行'
	},
	columns: '行',
	deleteTable: '刪除表格',
	headers: '頁首',
	headersBoth: '同時',
	headersColumn: '第一行',
	headersNone: '無',
	headersRow: '第一列',
	invalidBorder: '框線大小必須是整數。',
	invalidCellPadding: '儲存格邊距必須為正數。',
	invalidCellSpacing: '儲存格間距必須為正數。',
	invalidCols: '行數須為大於 0 的正整數。',
	invalidHeight: '表格高度必須為數字。',
	invalidRows: '列數須為大於 0 的正整數。',
	invalidWidth: '表格寬度必須為數字。',
	menu: '表格屬性',
	row: {
		menu: '列',
		insertBefore: '上方插入列',
		insertAfter: '下方插入列',
		deleteRow: '刪除列'
	},
	rows: '列',
	summary: '總結',
	title: '表格屬性',
	toolbar: '表格',
	widthPc: '百分比',
	widthPx: '像素',
	widthUnit: '寬度單位'
} );
