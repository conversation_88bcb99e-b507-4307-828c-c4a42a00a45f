﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF

{
    public class ADDI12LinkPersonViewModel
    {
        /// <summary>
        ///按讚id
        /// </summary>
        [DisplayName("按讚id")]
        public string STAGE_LIKE_NO { get; set; }

        /// <summary>
        ///小小舞臺ID
        /// </summary>
        [DisplayName("小小舞臺ID")]
        public string STAGE_ID { get; set; }

        /// <summary>
        ///學校NO
        /// </summary>
        [DisplayName("學校NO")]
        public string SCHOOL_NO { get; set; }

        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///班級代碼
        /// </summary>
        [DisplayName("班級代碼")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///點數
        /// </summary>
        [DisplayName("點數")]
        public short CASH { get; set; }

        /// <summary>
        ///IP
        /// </summary>
        [DisplayName("IP")]
        public string IP_ADDRESS { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public byte? LIKE_STATUS { get; set; }

        /// <summary>
        ///
        /// </summary>
        [DisplayName("")]
        public bool? IS_PREMIER { get; set; }
    }
}