﻿@{
    ViewBag.Title = "酷幣點數升級名單-說明";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string ddlCLASS_NO = string.Empty;
    string ddlGrade = string.Empty;
    string whereKeyword = string.Empty;

    if (user != null)
    {
        ddlCLASS_NO = user.TEACH_CLASS_NO ?? user.CLASS_NO;
        ddlGrade = string.IsNullOrWhiteSpace(ddlCLASS_NO) == false ? new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(ddlCLASS_NO, 1) : "";

        if (user.USER_TYPE == UserType.Student)
        {
            whereKeyword = user.USER_NO;
        }
    }

    int WhereGRADE = string.IsNullOrWhiteSpace(ddlGrade) == false ? Convert.ToInt32(ddlGrade) : 1;
    string HidStyle = "";
}


@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
<div>


    @**說明視窗*@
    <div id='container' style="@HidStyle">
        @{
            Html.RenderAction("_PageMenu", new { NowAction = "Index" });
        }
    </div>
</div>
<img src="~/Content/img/web-bar2-revise-78.png" style="width:100%" class="img-responsive App_hide" alt="" />
<div style="width:100%;font-size:15px;">
    @{string Explain = "本功能主要是方便各校搭配自己的榮譽制度，例如有些學校學生每累計2000點，就可以兌換和校長拍照，利用本功能就可以順利管控是否達標拍照標準。";
        }
    @Html.Raw(HttpUtility.HtmlDecode(@Explain))

    

</div>


