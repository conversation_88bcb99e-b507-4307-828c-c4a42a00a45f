﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameLevelPersonDetailsViewModel
    {
        /// <summary>
        ///暫時使用者id
        /// </summary>
        [DisplayName("暫時使用者id")]
        public string TEMP_USER_ID { get; set; }

        /// <summary>
        ///數位學生證id/QR Code Id
        /// </summary>
        [DisplayName("卡號")]
        public string GAME_USER_ID { get; set; }

        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///活動 ID
        /// </summary>
        [DisplayName("活動 ID")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///身份
        /// </summary>
        [DisplayName("身份")]
        public string GAME_USER_TYPE { get; set; }

        [DisplayName("身份")]
        public string GAME_USER_TYPE_DESC { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///全名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///性別
        /// </summary>
        [DisplayName("性別")]
        public string SEX { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///電話
        /// </summary>
        [DisplayName("電話")]
        public string PHONE { get; set; }

        /// <summary>
        ///訪客臨時目前可用酷幣點數
        /// </summary>
        [DisplayName("訪客臨時目前可用酷幣點數")]
        public int? CASH_AVAILABLE { get; set; }

        /// <summary>
        /// 此關卡闖關次數
        /// </summary>
        [DisplayName("闖此關卡次數")]
        public double Number_Count { get; set; }

        [DisplayName("訊息")]
        public string ERROR { get; set; }

        public byte STATUS { get; set; }
    }
}