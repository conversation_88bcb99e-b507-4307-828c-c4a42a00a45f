﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class PrizeListViewModel
    {
        public string SCHOOL_NO { get; set; }

        public string USER_NO { get; set; }

        public string LEVEL_DESC { get; set; }
        public int LEVEL_ID { get; set; }
        public string NAME { get; set; }

        public string SNAME { get; set; }

        public string BOOK_QTY { get; set; }

        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? UP_DATE { get; set; }
    }
}
