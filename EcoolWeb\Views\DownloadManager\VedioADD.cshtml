﻿@model EcoolWeb.Models.DownloadManagerIndexViewModel
@using ECOOL_APP;
@using ECOOL_APP.EF;
@using EcoolWeb.Models;
@{
    UserProfile user = UserProfileHelper.Get();
    ViewBag.Title = "教學影片管理";
}

<link href="~/Content/css/EzCss.css" rel="stylesheet" />

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@if (user != null)
{
    if (user.USER_TYPE == UserType.Admin && user.SCHOOL_NO == "403605")
    {
        <a role="button" class="btn btn-sm btn-sys" onclick="onAdd()">
            <i class="fa fa-fw -square -circle fa-plus-square"></i>新增教學文件
        </a>
        <a role="button" class="btn btn-sm btn-sys" onclick="onVedioAdd()">
            <i class="fa fa-fw -square -circle fa-plus-square"></i>新增教學影片
        </a>
    }
}

       
            @using (Html.BeginForm("VedioADD", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "" }))
            {
                @Html.AntiForgeryToken()

                @Html.HiddenFor(m => m.OrdercColumn)
                @Html.HiddenFor(m => m.Page)
                @Html.HiddenFor(m => m.ShowDisable)
                @Html.HiddenFor(m => m.BackAction)
                @Html.HiddenFor(m => m.BackController)
                @Html.HiddenFor(m => m.DL_ID)

                <div class="form-inline " role="form" id="Q_Div">
                    <div class="form-group">
                        <label class="control-label">
                            @Html.DisplayNameFor(model => model.whereSearch)
                        </label>
                    </div>
                    <div class="form-group">
                        @Html.EditorFor(m => m.whereSearch, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
                    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
                </div>
                <div class="form-inline">
                    <div class="col-xs-12 text-right">
                        @{
                            string ShowDisable = (string.IsNullOrWhiteSpace(Model.ShowDisable) == false) ? "active" : "";
                            string NODisable = string.IsNullOrWhiteSpace(Model.ShowDisable) ? "active" : "";

                            <button class="btn btn-xs btn-pink  @NODisable" type="button" onclick="doSearch('ShowDisable', '');">隱藏已作廢</button>
                            <button class="btn btn-xs btn-pink  @ShowDisable" onclick="doSearch('ShowDisable','Y');" type="button">顯示已作廢</button>
                        }
                    </div>
                </div>
                <div class="panel panel-ACC" >
                    <div class="panel-heading text-center">
                        教學影片清單
                    </div>
                    <div class="table-responsive">
                        <table class="table-ecool table-92Per table-hover table-ecool-reader">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>文件分類</th>
                                    <th>文件名稱</th>
                                    <th>說明</th>
                                    <th>上傳日</th>
                                    <th>順序</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.ListData)
                                {
                                <tr>

                                    <td align="center">
                                        @Html.ActionLink("下載", "DownLoad", (string)ViewBag.BRE_NO, new { DL_ID = item.DL_ID, FILE_NAME = item.FILENAME }, new { @class = "btn btn-xs btn-Basic" })
                                        @if (item.YOUTUBE_URL != null && item.YOUTUBE_URL != "")
                                        {
                                            <a href="@item.YOUTUBE_URL" target="_blank" class="btn btn-xs btn-Basic">觀看</a>

                                        }

                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.DL_GROUP)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.DL_SUBJECT)
                                    </td>
                                    <td>@Html.DisplayFor(modelItem => item.DL_MEMO)</td>
                                    <td>@Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")</td>
                                    <td>@Html.DisplayFor(modelItem => item.Orderby)</td>
                                    <td align="center">
                                        @if (user != null)
                                        {
                                            if (user.USER_TYPE == UserType.Admin && user.SCHOOL_NO == "403605")
                                            {
                                                @Html.ActionLink("修改", "VedioADDEdit", new { DL_ID = item.DL_ID }, new { @class = "btn btn-xs btn-Basic" })
                                                @Html.ActionLink("作廢", "Delete", new { DL_ID = item.DL_ID }, new { @class = "btn btn-xs btn-Basic" })
                                            }
                                        }

                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            }
            @section scripts{
                <script type="text/javascript">

        var targetFormID = '#form1';

            function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)

            $(targetFormID).attr("action", "@Url.Action("VedioADD", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1);
        }
           function onAdd() {
            $('#DL_ID').val('')
            $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
        function onVedioAdd() {
            $('#DL_ID').val('')
            $(targetFormID).attr("action", "@Url.Action("VedioADDEdit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
        function onBtnDetails(Value) {
            $('#DL_ID').val(Value)
            $(targetFormID).attr("action", "@Url.Action("VedioADDEdit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function delete_show(Value) {

            if (confirm("您確定要刪除？") == true) {
                $('#DL_ID').val(Value)
                $(targetFormID).attr("action", "@Url.Action("Delete", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }


        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }

                </script>
            }




