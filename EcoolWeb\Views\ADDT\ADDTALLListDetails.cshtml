﻿@using ECOOL_APP.com.ecool.Models.entity;
@model IEnumerable<ECOOL_APP.EF.ADDT06>

@{
    ViewBag.Title = "閱讀護照-明細內容";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    int i = 0;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@foreach (var item in Model)
{
    List<Image_File> ImageModel = (List<Image_File>)ViewBag.ImageUrl;
    List<Image_File> ImageModel1 = (List<Image_File>)ViewBag.ImageUrl1;
    <img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="Details">
            <div class="row">
                <div class="col-md-5 col-sm-5 dl-horizontal-EZ">
                    <samp class="dt">
                        申請日
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => item.CRE_DATE, "ShortDateTime")
                    </samp>
                </div>
                <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                    <samp class="dt">
                        班級
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => item.CLASS_NO)
                    </samp>
                </div>
                <div class="col-md-4 col-sm-4 dl-horizontal-EZ">
                    <samp class="dt">
                        姓名
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => item.SNAME)
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-5 col-sm-5  dl-horizontal-EZ">
                    <samp class="dt">
                        書名
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => item.BOOK_NAME)
                    </samp>
                </div>
                <div class="col-md-3 col-sm-3 dl-horizontal-EZ">
                    <samp class="dt">
                        座號
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => item.SEAT_NO)
                    </samp>
                </div>
            </div>
            <div class="row">
            </div>
            <div style="height:15px"></div>
            <div class="row">
                @if (ImageModel[i].APPLY_NO == item.APPLY_NO && ImageModel[i].ImageUrl != null)
                {
                    if (string.IsNullOrWhiteSpace(item.REVIEW))
                    {
                        <div class="col-md-12 p-context">
                            <img src="@ImageModel[i].ImageUrl" id="imgArticle" class="img-responsive " style="float:right;margin:5px" href="@ImageModel1[i].ImageUrl" />
                        </div>
                    }
                    else
                    {
                        <div class="col-md-12 p-context">
                            <img src="@ImageModel[i].ImageUrl" id="imgArticle" class="img-responsive " style="float:right;margin:10px;max-height:250px;width:auto" href="@ImageModel1[i].ImageUrl" />
                            @if (ViewBag.ShowOriginalArticle == "V")
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW_VERIFY.Replace("\r\n", "<br />")))
                            }
                            else
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW.Replace("\r\n", "<br />")))
                            }
                        </div>
                    }
                }
            </div>
            <div class="Div-btn-center">

                @if (TempData["Search"] != null)
                {
                    EcoolWeb.Models.ADDT06ViewModel Search = TempData["Search"] as EcoolWeb.Models.ADDT06ViewModel;

                    if (ViewBag.ShowOriginalArticle == "O")
                    {
                        @Html.ActionLink("批閱後文章", "ADDTALLListDetails", new
                           {
                               APPLY_NO = item.APPLY_NO,
                               ShowOriginal = false,
                               whereKeyword = Search.whereKeyword,
                               whereUserNo = Search.whereUserNo,
                               whereBOOK_NAME = Search.whereBOOK_NAME,
                               whereAPPLY_STATUS = Search.whereAPPLY_STATUS,
                               OrdercColumn = Search.OrdercColumn,
                               whereCLASS_NO = Search.whereCLASS_NO,
                               whereGrade = Search.whereGrade,
                               Page = Search.Page,
                               PictureMode = Search.PictureMode
                           }, new { @role = "button", @class = "btn btn-default" })
                    }
                    else if (ViewBag.ShowOriginalArticle == "V")
                    {

                        <a href="@Url.Action("ADDTALLListDetails", "ADDT",new {
                                 APPLY_NO = item.APPLY_NO,
                               ShowOriginal = true,
                               whereKeyword = Search.whereKeyword,
                               whereUserNo = Search.whereUserNo,
                               whereBOOK_NAME = Search.whereBOOK_NAME,
                               whereAPPLY_STATUS = Search.whereAPPLY_STATUS,
                               OrdercColumn = Search.OrdercColumn,
                               whereCLASS_NO = Search.whereCLASS_NO,
                               whereGrade = Search.whereGrade,
                               Page = Search.Page,
                               PictureMode = Search.PictureMode
                        })" role="button" class="btn btn-default">
                            學生原稿
                        </a>
                    }

                    if (Search.BackAction == "QUERY")
                    {
                        <a href='@Url.Action(Search.BackAction, "ADDI02"
                        ,new {
                            whereKeyword = Search.whereKeyword,
                            whereUserNo = Search.whereUserNo,
                            whereBOOK_NAME = Search.whereBOOK_NAME,
                            whereAPPLY_STATUS = Search.whereAPPLY_STATUS,
                            OrdercColumn = Search.OrdercColumn,
                            whereCLASS_NO = Search.whereCLASS_NO,
                            whereGrade = Search.whereGrade,
                            Page = Search.Page,
                            PictureMode = Search.PictureMode
                        })' role="button" class="btn btn-default">
                            返回
                        </a>
                    }
                    else
                    {
                        <a href='@Url.Action("ADDTALLList", "ADDT"
                        ,new {
                            whereKeyword = Search.whereKeyword,
                            whereUserNo = Search.whereUserNo,
                            whereBOOK_NAME = Search.whereBOOK_NAME,
                            whereAPPLY_STATUS = Search.whereAPPLY_STATUS,
                            OrdercColumn = Search.OrdercColumn,
                            whereCLASS_NO = Search.whereCLASS_NO,
                            whereGrade = Search.whereGrade,
                            Page = Search.Page,
                            PictureMode = Search.PictureMode
                        })' role="button" class="btn btn-default">
                            返回
                        </a>
                    }
                }
            </div>
        </div>
    </div>
    i++;
}

<script language="javascript">
    $(document).ready(function () {
        $("#imgArticle").colorbox({ opacity: 0.82 });
    });
</script>