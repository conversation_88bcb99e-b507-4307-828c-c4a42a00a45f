﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// 組件的一般資訊會透過將設定 
// 控制。變更這些屬性值可修改與組件關聯的
// 資訊。
[assembly: AssemblyTitle("EcoolWeb")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Microsoft")]
[assembly: AssemblyProduct("EcoolWeb")]
[assembly: AssemblyCopyright("Copyright (C) Microsoft 2015")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: log4net.Config.XmlConfigurator(ConfigFile = "log4net.config", Watch = true)]

// ComVisible 為 false 的方式來控制，讓此組件中的類型在 
// COM 組件中為不可見。如果您需要從
// COM 存取此組件中的型別，請在該型別上將 ComVisible 屬性設定為 true。
[assembly: ComVisible(false)]

// 下列 GUID 為專案公開 (Expose) 至 COM 時所要使用的 typelib ID
[assembly: Guid("33cc69d2-398b-44a2-a638-d20172b28708")]

// 組件的版本資訊是由下列四項值構成:
//
//      主要版本
//      次要版本存取此組件中的類型，
//      組建編號
//      修訂編號
//
// 您可以指定所有值或預設修訂和組件數目
// 指定為預設值:
[assembly: AssemblyVersion("1.0.0.0")]
[assembly: AssemblyFileVersion("1.0.0.0")]
