﻿using ECOOL_APP.EF;
using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace ECOOL_APP
{
    public class AppHelper
    {
        private static string Key = "97283784";

        private static string CheckIdKey = "0226557299";

        private static string PageKey = "26557299";

        public static bool AppCheck(string CkAppKey, string LAST_LOGIN_TIME, string UUID, out string Err)
        {
            if (string.IsNullOrWhiteSpace(CkAppKey) || string.IsNullOrWhiteSpace(UUID) || string.IsNullOrWhiteSpace(LAST_LOGIN_TIME))
            {
                Err = "未傳入有效參數";
                return false;
            }

            string resultMd5 = GetAppKey(LAST_LOGIN_TIME, UUID);

            if (CkAppKey == resultMd5)
            {
                Err = "";
                return true;
            }
            else
            {
                Err = "驗正失敗";
                return false;
            }
        }

        /// <summary>
        /// 取得加密後通關密碼
        /// </summary>
        /// <param name="LAST_LOGIN_TIME">時間 yyyyMMddHHmmss</param>
        /// <param name="UUID">UUID</param>
        /// <returns></returns>
        public static string GetAppKey(string LAST_LOGIN_TIME, string UUID)
        {
            string ReturnVal = string.Empty;

            if (string.IsNullOrWhiteSpace(UUID) || string.IsNullOrWhiteSpace(UUID))
            {
                return ReturnVal;
            }

            string UnMd5Text = UUID + Key + LAST_LOGIN_TIME;

            return MD5Encrypt32(UnMd5Text);
        }

        public static string GetCheckId(string LAST_LOGIN_TIME, string UUID, int TempNo)
        {
            string ReturnVal = string.Empty;

            if (string.IsNullOrWhiteSpace(UUID) || string.IsNullOrWhiteSpace(UUID))
            {
                return ReturnVal;
            }

            string UnMd5Text = UUID + CheckIdKey + LAST_LOGIN_TIME + TempNo;

            return MD5Encrypt32(UnMd5Text);
        }

        public static string MD5String(string Text)
        {
            MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
            byte[] bArr = md5.ComputeHash(ASCIIEncoding.ASCII.GetBytes(Text));
            StringBuilder sb = new StringBuilder();
            foreach (byte b in bArr)
            {
                sb.Append(b.ToString("x2"));
            }

            return sb.ToString();
        }

        /// <summary>
        /// 32位MD5加密
        /// </summary>
        /// <param name="password"></param>
        /// <returns></returns>
        public static string MD5Encrypt32(string password)
        {
            string cl = password;
            string pwd = "";
            MD5 md5 = MD5.Create(); //实例化一个md5对像
                                    // 加密后是一个字节类型的数组，这里要注意编码UTF8/Unicode等的选择　
            byte[] s = md5.ComputeHash(Encoding.UTF8.GetBytes(cl));
            // 通过使用循环，将字节类型的数组转换为字符串，此字符串是常规字符格式化所得
            for (int i = 0; i < s.Length; i++)
            {
                // 将得到的字符串使用十六进制类型格式。格式后的字符是小写的字母，如果使用大写（X）则格式后的字符是大写字符
                pwd = pwd + s[i].ToString("x2");
            }
            return pwd;
        }

        /// <summary>
        /// 檢查驗証是否與確 進入後 每頁的驗証
        /// </summary>
        /// <param name="EncryptCheckId">加密後的BASE64編碼的字符串</param>
        /// <returns></returns>
        public static bool checkCheckId(string EncryptCheckId, string UUID, out string RealCheckId)
        {
            if (string.IsNullOrWhiteSpace(EncryptCheckId))
            {
                RealCheckId = null;
                return false;
            }

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                HRMT05_CHECK dbCk = db.HRMT05_CHECK.Where(a => a.UUID == UUID).FirstOrDefault();

                if (dbCk == null)
                {
                    RealCheckId = null;
                    return false;
                }

                if (dbCk.OS_TYPE == HRMT05.OS_TypeVal.Android)
                {
                    //暫時允許TempNO
                    if (dbCk.TempNO.ToString() == EncryptCheckId)
                    {
                        RealCheckId = EncryptCheckId;
                        return true;
                    }
                }

                int NewPageKey = Int32.Parse(PageKey) + (int)(dbCk.TempNO);

                string DecryptCheckId = Decrypt(EncryptCheckId, NewPageKey.ToString());

                //int NewPageKey = Int32.Parse(PageKey) + (int)(dbCk.TempNO);

                //string DecryptCheckId = Decrypt(EncryptCheckId, NewPageKey.ToString());

                if (DecryptCheckId == dbCk.CHECK_ID)
                {
                    RealCheckId = DecryptCheckId;
                    return true;
                }
                else
                {
                    RealCheckId = null;
                    return false;
                }
            }
        }

        /// <summary>
        /// 解密 CheckId
        /// </summary>
        /// <param name="EncryptCheckId">加密CheckId(加密後的BASE64編碼的字符串)</param>
        /// <param name="UUID">UUID</param>
        /// <returns></returns>
        public static string GetDecryptCheckId(string EncryptCheckId, string UUID, byte? OS_TYPE = 1)
        {
            log4net.ILog logger = LogManager.GetLogger("AppHelper ");
            string DecryptCheckId = string.Empty;

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                HRMT05_CHECK dbCk = db.HRMT05_CHECK.Where(a => a.UUID == UUID).FirstOrDefault();

                if (OS_TYPE == HRMT05.OS_TypeVal.Android)
                {
                    if (EncryptCheckId == dbCk.TempNO.ToString())
                    {
                        DecryptCheckId = dbCk.TempNO.ToString();
                    }
                }
                else
                {
                    int NewPageKey = Int32.Parse(PageKey) + (int)(dbCk.TempNO);
                    logger.Info("GetApiBaseModelALL NewPageKey" + NewPageKey);
                    DecryptCheckId = Decrypt(EncryptCheckId, NewPageKey.ToString());
                    logger.Info("GetApiBaseModelALL DecryptCheckId" + DecryptCheckId);
                }

                return DecryptCheckId;
            }
        }

        /// <summary>
        /// 加密 CheckId
        /// </summary>
        /// <param name="CheckId">原CheckId</param>
        /// <param name="UUID">UUID</param>
        /// <returns></returns>
        public static string GetEncryptCheckId(string CheckId, string UUID)
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                HRMT05_CHECK dbCk = db.HRMT05_CHECK.Where(a => a.UUID == UUID).FirstOrDefault();

                if (dbCk == null)
                {
                    return string.Empty;
                }

                int NewPageKey = Convert.ToInt32(PageKey) + Convert.ToInt32(dbCk.TempNO);

                string EncryptCheckId = Encrypt(CheckId, NewPageKey.ToString());

                return EncryptCheckId;
            }
        }

        #region 跨平台加解密（c#）

        /// <summary>
        /// 對字符串進行DES加密
        /// </summary>
        /// <param name="sourceString">待加密的字符串</param>
        /// <returns>加密後的BASE64編碼的字符串</returns>
        public static string Encrypt(string sourceString, string sKey)
        {
            byte[] btKey = Encoding.UTF8.GetBytes(sKey);
            byte[] btIV = Encoding.UTF8.GetBytes(sKey);
            DESCryptoServiceProvider des = new DESCryptoServiceProvider();
            using (MemoryStream ms = new MemoryStream())
            {
                byte[] inData = Encoding.UTF8.GetBytes(sourceString);
                try
                {
                    using (CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(btKey, btIV), CryptoStreamMode.Write))
                    {
                        cs.Write(inData, 0, inData.Length);
                        cs.FlushFinalBlock();
                    }

                    return Convert.ToBase64String(ms.ToArray());
                }
                catch
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="pToDecrypt">要解密的以Base64</param>
        /// <param name="sKey">密鑰，且必須為8位</param>
                 /// <returns>已解密的字符串</returns>
        public static string Decrypt(string pToDecrypt, string sKey)
        {
            pToDecrypt = Uri.UnescapeDataString(pToDecrypt);

            //轉義特殊字符
            pToDecrypt = pToDecrypt.Replace("-", "+");
            pToDecrypt = pToDecrypt.Replace("_", "/");
            pToDecrypt = pToDecrypt.Replace("~", "=");

            byte[] inputByteArray = Convert.FromBase64String(pToDecrypt);
            using (DESCryptoServiceProvider des = new DESCryptoServiceProvider())
            {
                des.Key = ASCIIEncoding.ASCII.GetBytes(sKey);
                des.IV = ASCIIEncoding.ASCII.GetBytes(sKey);
                System.IO.MemoryStream ms = new System.IO.MemoryStream();
                using (CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }
                string str = Encoding.UTF8.GetString(ms.ToArray());
                ms.Close();
                return str;
            }
        }

        #endregion 跨平台加解密（c#）
    }
}