//未來優化項目--暫時用
$link-color: #000;
@import "../../node_modules/bootstrap/scss/bootstrap-reboot.scss";
@import "../../node_modules/bootstrap/scss/grid";
@import "../../node_modules/bootstrap/scss/utilities/borders";
@import "../../node_modules/bootstrap/scss/utilities/clearfix";
@import "../../node_modules/bootstrap/scss/utilities/display";
@import "../../node_modules/bootstrap/scss/utilities/embed";
@import "../../node_modules/bootstrap/scss/utilities/flex";
@import "../../node_modules/bootstrap/scss/utilities/float";
@import "../../node_modules/bootstrap/scss/utilities/position";
@import "../../node_modules/bootstrap/scss/utilities/spacing";

// PC版 手機app 項目隱藏&顯示
.App_hide {
    display: block;
}

.App_show {
    display: none;
}

/*舊版右版按鈕重疊時區塊div穿透a保持不穿透*/
._Layout_RightBar {
    pointer-events: none;
}

._Layout_RightBar a {
    display: inline-block;
    pointer-events: visible;
}

//---PC版 css
body {
    -webkit-print-color-adjust: exact;
}

@media (min-width: 992px) {
    .modal-lg {
        width: 900px;
    }
}

#PageContent {
    word-break: break-all;
}

//顯示輔助
.div-hidden {
    display: none;
}

.vertical-middle-sm {
    display: table;

    .div {
        display: table-cell;
        height: 100%;
        min-height: 100%;
        float: none !important;
    }
}

a:not([href]):not([tabindex]) {
    color: #000;
}

//排版輔助
.msg-img {
    width: 100%;
    height: auto;
    max-width: 30px;
    max-height: 30px
}

.center {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    float: none;
}

.middle {
    display: table-cell;
    vertical-align: middle;
    float: none;
}

.center_bottom {
    display: table-cell;
    text-align: center;
    vertical-align: bottom;
    float: none;
}

.center_top {
    display: table-cell;
    text-align: center;
    vertical-align: top;
    float: none;
}

.bar-div {
    max-width: 664px;
    margin: 0 auto;
}

.col-height {
    margin-bottom: -10000px;
    padding-bottom: 10000px;
}

.row-hidden {
    overflow: hidden;
}

.prod-caption {
    text-align: left;
    line-height: 1.2;
    margin-left: 6px;
}

@media screen and (max-width: 1200px) {
    .prod-icon {
        text-align: left;
    }
}

@media screen and (min-width: 1200px) {
    .prod-icon {
        text-align: center;
    }
}

.prod-text {
    margin-right: 14px;
    display: inline;
}

.btn-prod {
    margin-top: 10px;
    margin-bottom: 10px;
}

.box {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    width: 270px;
    height: 200px;
    margin-bottom: 12px;

    >div {
        /*設置圖片垂直居中*/
        width: 100%;
        max-width: 200px;
        max-height: 200px;
        vertical-align: middle;
        text-align: center;
        margin: 0 auto;

        >img {
            /*設置圖片垂直居中*/
            display: block;
            width: 100%;
            max-width: 200px;
            max-height: 200px;
            vertical-align: middle;
        }
    }
}

.imgEZ {
    max-width: 100%;
    height: auto;
}

.imgMenu {
    max-width: 100%;
    width: auto\9;

    /* IE6, IE7, IE8, IE9 */
    width: 100%;
    height: auto;
}

//表格相關樣式
.table-eZ {
    margin-bottom: 15px;
    overflow-x: scroll;
    overflow-y: hidden;
    margin: 0px auto;

    .table {
        margin-bottom: 0;
        background-color: #fff;

        thead>tr>th,
        tbody>tr>th,
        tfoot>tr>th,
        thead>tr>td,
        tbody>tr>td,
        tfoot>tr>td {
            white-space: nowrap;
        }
    }

    .table-bordered {
        border: 0;

        thead>tr>th:first-child,
        tbody>tr>th:first-child,
        tfoot>tr>th:first-child,
        thead>tr>td:first-child,
        tbody>tr>td:first-child,
        tfoot>tr>td:first-child {
            border-left: 0;
        }

        thead>tr>th:last-child,
        tbody>tr>th:last-child,
        tfoot>tr>th:last-child,
        thead>tr>td:last-child,
        tbody>tr>td:last-child,
        tfoot>tr>td:last-child {
            border-right: 0;
        }

        thead>tr:last-child>th,
        tbody>tr:last-child>th,
        tfoot>tr:last-child>th,
        thead>tr:last-child>td,
        tbody>tr:last-child>td,
        tfoot>tr:last-child>td {
            border-bottom: 0;
        }
    }
}

.table-eZwhitespacinitial {
    margin-bottom: 15px;
    overflow-x: scroll;
    overflow-y: hidden;
    margin: 0px auto;

    .table {
        margin-bottom: 0;
        background-color: #fff;

        thead>tr>th,
        tbody>tr>th,
        tfoot>tr>th,
        thead>tr>td,
        tbody>tr>td,
        tfoot>tr>td {
            white-space: pre-line;
        }
    }

    .table-bordered {
        border: 0;

        thead>tr>th:first-child,
        tbody>tr>th:first-child,
        tfoot>tr>th:first-child,
        thead>tr>td:first-child,
        tbody>tr>td:first-child,
        tfoot>tr>td:first-child {
            border-left: 0;
        }

        thead>tr>th:last-child,
        tbody>tr>th:last-child,
        tfoot>tr>th:last-child,
        thead>tr>td:last-child,
        tbody>tr>td:last-child,
        tfoot>tr>td:last-child {
            border-right: 0;
        }

        thead>tr:last-child>th,
        tbody>tr:last-child>th,
        tfoot>tr:last-child>th,
        thead>tr:last-child>td,
        tbody>tr:last-child>td,
        tfoot>tr:last-child>td {
            border-bottom: 0;
        }
    }
}

.table-eZ_Not_overflow {
    width: 100%;
    margin-bottom: 15px;
    margin: 0px auto;

    .table {
        margin-bottom: 0;
        background-color: #fff;
        margin-bottom: 0;
        background-color: #fff;
    }

    .table-bordered {
        border: 0;

        thead>tr>th:first-child,
        tbody>tr>th:first-child,
        tfoot>tr>th:first-child,
        thead>tr>td:first-child,
        tbody>tr>td:first-child,
        tfoot>tr>td:first-child {
            border-left: 0;
        }

        thead>tr>th:last-child,
        tbody>tr>th:last-child,
        tfoot>tr>th:last-child,
        thead>tr>td:last-child,
        tbody>tr>td:last-child,
        tfoot>tr>td:last-child {
            border-right: 0;
        }

        thead>tr:last-child>th,
        tbody>tr:last-child>th,
        tfoot>tr:last-child>th,
        thead>tr:last-child>td,
        tbody>tr:last-child>td,
        tfoot>tr:last-child>td {
            border-bottom: 0;
        }
    }
}

//第二批 table
.table-ecool_list {
    thead tr {

        td,
        th {
            color: #004DA0;
            font-weight: bold;
            border-top-style: none;
            background-color: #FFFFEC;
            white-space: nowrap;
            text-align: center;
        }
    }

    tbody tr {
        &:nth-child(even) {

            td,
            th {
                background-color: #FFFFEC;
                white-space: normal;
            }
        }

        &:nth-child(odd) {

            td,
            th {
                background-color: #EBEBC8;
                white-space: normal;
            }
        }
    }

    &-hover tbody tr:hover {

        td,
        th {
            background-color: #f5f5f5;
        }
    }
}

.table-ecool_list_Notnowrap thead tr {

    td,
    th {
        font-weight: bold;
        border-top-style: none;
        background-color: #FFFFEC;
    }
}

.table-ecool-info {
    thead tr {

        td,
        th {
            color: #005AB5;
            background-color: #d9edf7;
            border-color: #bce8f1;
            text-align: center;
            font-weight: bold;
            white-space: nowrap;
            text-align: center;
        }
    }

    tbody tr {
        &:nth-child(even) {

            td,
            th {
                color: #3a87ad;
                background-color: #F0FFFF;
                border-color: #bce8f1;
                white-space: normal;
            }
        }

        &:nth-child(odd) {

            td,
            th {
                color: #005AB5;
                background-color: #FDFFFF;
                border-color: #bce8f1;
                white-space: normal;
            }
        }
    }

    tfoot tr {

        td,
        th {
            color: red;
            background-color: #FDFFFF;
            border-color: #bce8f1;
            white-space: normal;
        }
    }

    &-hover tbody tr:hover {

        td,
        th {
            background-color: #F5F5DC;
        }
    }
}

.table-ecool-qa {
    thead tr {

        td,
        th {
            background-color: #EEEEEF;
            font-weight: bold;
            white-space: nowrap;
        }
    }

    tbody tr {

        td,
        th {
            white-space: pre-line;
        }
    }

    &-Title {
        color: orangered;
    }
}

.table-ecool-infoA {
    thead tr {
        &:nth-child(odd) {

            td,
            th {
                color: #F0FFFF;
                background-color: #4682b4;
                border-color: #bce8f1;
                text-align: center;
                font-weight: bold;
            }
        }

        &:nth-child(even) {

            td,
            th {
                color: #3a87ad;
                background-color: #d9edf7;
                border-color: #bce8f1;
                text-align: center;
                font-weight: bold;
            }
        }
    }

    tbody tr {

        td,
        th {
            color: #3a87ad;
            background-color: #F0FFFF;
            border-color: #bce8f1;
        }
    }
}

.table-ecool-danger {
    thead tr {
        &:nth-child(odd) {

            td,
            th {
                color: #CE0000;
                background-color: #FFE4E1;
                border-color: #FFD2D2;
                text-align: center;
                font-weight: bold;
            }
        }

        &:nth-child(even) {

            td,
            th {
                color: #CE0000;
                background-color: #FFF0F5;
                border-color: #fff;
                text-align: center;
                font-weight: bold;
            }
        }
    }

    tbody tr {

        td,
        th {
            color: #CE0000;
            background-color: #fff;
            border-color: #FFD2D2;
        }
    }
}

.table-ecool-warning {
    thead tr {
        &:nth-child(odd) {

            td,
            th {
                color: #B8860B;
                background-color: #FAFAD2;
                border-color: #EEE8AA;
                text-align: center;
                font-weight: bold;
            }
        }

        &:nth-child(even) {

            td,
            th {
                color: #B8860B;
                background-color: #FFFFE0;
                border-color: #EEE8AA;
                text-align: center;
                font-weight: bold;
            }
        }
    }

    tbody tr {

        td,
        th {
            color: #B8860B;
            background-color: #fff;
            border-color: #EEE8AA;
        }
    }
}

.table-ecool-Bule {
    thead tr {
        &:nth-child(odd) {

            td,
            th {
                color: #F0FFFF;
                border-color: #E0FFFF;
                text-align: center;
                font-weight: bold;
                background: -webkit-linear-gradient(#00BFFF, #87CEFA);
                background: -o-linear-gradient(#00BFFF, #87CEFA);
                background: -moz-linear-gradient(#00BFFF, #87CEFA);
                background: linear-gradient(#00BFFF, #87CEFA);
            }
        }

        &:nth-child(even) {

            td,
            th {
                color: #003377;
                border-color: #FFFFFF;
                text-align: center;
                font-weight: bold;
                background: -webkit-linear-gradient(#B0E0E6, #FFFFFF);
                background: -o-linear-gradient(#B0E0E6, #FFFFFF);
                background: -moz-linear-gradient(#B0E0E6, #FFFFFF);
                background: linear-gradient(#B0E0E6, #FFFFFF);
            }
        }
    }

    tbody tr {

        td,
        th {
            color: #003377;
            background-color: #FFFFFF;
            border-color: #E0FFFF;
        }
    }
}

%sports-tr-icons {
    font-weight: 600;

    &::before {
        content: "";
        display: inline-block;
        min-width: 2.3em;
        min-height: 1em;
        margin-right: .5em;
        vertical-align: text-bottom;
        background-repeat: no-repeat;
        background-position: left 20% center;
        background-size: auto 1em;
        border-radius: 0.5rem;
    }
}

//table第三批
.table-ecool {
    width: 100%;
    margin-bottom: 20px;
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    margin: 0 auto;

    thead>tr>th,
    tbody>tr>th,
    tfoot>tr>th,
    thead>tr>td,
    tbody>tr>td,
    tfoot>tr>td {
        line-height: 1.428571429;
        padding: 8px;
        vertical-align: middle;

        @media (min-width: 767px) {
            line-height: 1.5;
            padding: 5px;
            vertical-align: middle;

        }
    }

    thead tr {

        th,
        td {

            //大於1920px
            @media screen and (min-width: 1920px) {
                font-size: 16px;
                white-space: nowrap;
            }

            //小於1920px SHow
            @media screen and (min-width: 1280px) and (max-width: 1920px) {
                font-size: 15px;
                white-space: nowrap;
            }

            //小於1024px SHow
            @media screen and (min-width: 767px) and (max-width: 1280px) {
                font-size: 13px;
                white-space: normal;
            }

            //小於768px SHow
            @media screen and (max-width: 767px) {
                font-size: 12px;
                white-space: nowrap;
            }

            @media print {
                font-size: 20px;
                white-space: nowrap;
            }
        }
    }

    //運動撲滿 代表示圖
    //火車
    .tr-train td:first-child {
        @extend %sports-tr-icons;
        background-color: #fff0f0;
        box-shadow: 0 0 3px #df9c9c;

        &::before {
            background-position: center center;
            background-image: url("data:image/svg+xml,%3Csvg enable-background='new 0 0 512 512' height='24' viewBox='0 0 512 512' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='_x32_3'%3E%3Cpath id='XMLID_214_' d='m256 32.31c85.36 0 154.53 69.2 154.53 154.53 0 64-114.82 220.49-154.53 292.07-38.07-68.7-154.53-231.83-154.53-292.07 0-85.33 69.2-154.53 154.53-154.53zm69.07 148.71c0-38.03-31.02-69.05-69.07-69.05-38.02 0-69.04 31.02-69.04 69.05 0 38.02 31.02 69.04 69.04 69.04 38.05 0 69.07-31.02 69.07-69.04z' fill='%23e74c3c'/%3E%3C/g%3E%3C/svg%3E");
        }
    }

    //捷運板南線
    .tr-mrt-bl td:first-child {
        @extend %sports-tr-icons;
        background-color: #f0f6ff;
        box-shadow: 0 0 3px #9cb5df;

        &::before {
            content: "BL";
            padding-left: .5em;
            font-size: 1em;
            font-weight: normal;
            color: #ffffff;
            background-color: #003377;
        }
    }

    //捷運文湖線
    .tr-mrt-br td:first-child {
        @extend %sports-tr-icons;
        background-color: #fffaf0;
        box-shadow: 0 0 3px #dfc29c;

        &::before {
            content: "BR";
            padding-left: .5em;
            font-size: 1em;
            font-weight: normal;
            color: #ffffff;
            background-color: #774900;
        }
    }
    //點數升級用
    .tr-coins:hover >td{
        background-color: #e0e7da;
    }
    .tr-coins td:first-child {
        @extend %sports-tr-icons;
        background-color: #fffaf0;
        box-shadow: 0 0 3px #dfc29c;
        &::before {
            content: "";
            display: none;
        }
    }
    .tr-coins:hover td:first-child{
        background-color: #fff5ca;
    }
    &-sm {
        width: 100%;
        margin-bottom: 20px;
        max-width: 100%;
        background-color: transparent;
        border-collapse: collapse;
        border-spacing: 0;
        margin: 0 auto;
        table-layout: fixed;
        word-wrap: break-word;

        thead>tr>th,
        tbody>tr>th,
        tfoot>tr>th,
        thead>tr>td,
        tbody>tr>td,
        tfoot>tr>td {
            line-height: 1.428571429;
            padding: 8px;
        }

        thead tr {

            th,
            td {
                @media screen and (min-width: 1920px) {
                    font-size: 13px;
                }

                @media screen and (min-width: 1280px) and (max-width: 1920px) {
                    font-size: 12px;
                }
            }
        }

        tbody tr td {
            @media screen and (min-width: 1920px) {
                font-size: 13px;
            }

            @media screen and (min-width: 1280px) and (max-width: 1920px) {
                font-size: 12px;
            }

        }
    }
}

.table-92Per {
    width: 92%;
}

//table-ecool-thead
.table-ecool-thead thead tr {

    td,
    th {
        font-weight: bold;
        text-align: center;
        color: #004da0;
    }
}

//table-ecool-List
.table-ecool-List {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(232, 209, 230, 0.2);
        }
    }
}

//table-ecool-reader
.table-ecool-reader {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(227, 236, 159, 0.2);
        }
    }
}

//table-ecool-rpp
.table-ecool-rpp {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(250, 233, 180, 0.2);
        }
    }
}

//table-ecool-rpp-sm
.table-ecool-rpp-sm {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(250, 233, 180, 0.2);
        }
    }
}

//table-ecool-ADDI06
.table-ecool-ADDI06 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(226, 197, 133, 0.2);
        }
    }
}

//table-ecool-ADDI07
.table-ecool-ADDI07 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(226, 197, 133, 0.2);
        }
    }
}

//table-ecool-AWA003
.table-ecool-AWA003 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(253, 230, 128, 0.2);
        }
    }
}

//table-ecool-ADDO05
.table-ecool-ADDO05 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(250, 206, 56, 0.2);
        }
    }
}

//Div-EZ-AWA004
.Div-EZ-AWA004 {
    width: 90%;
    background-color: rgba(243, 200, 176, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-BK {
        background-color: rgba(227, 236, 159, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }

        .control-label-D,
        .control-label-left-D {
            font-size: 17px;
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}

//table-ecool-AWA004
.table-ecool-AWA004 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(243, 200, 176, 0.2);
        }
    }
}

//table-ecool-AWA007
.table-ecool-AWA007 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(225, 231, 250, 0.2);
        }
    }
}

//table-ecool-ZZZI04
.table-ecool-ZZZI04 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(236, 237, 51, 0.2);
        }
    }
}



//table-ecool-Bule-01-SEC  
.table-ecool-Bule-01-SEC thead tr {

    td,
    th {
        background-color: rgba(198, 248, 245, 0.5);
        height: 30px;
        font-weight: bold;
    }
}

//table-ecool-Bule-SEC  
.table-ecool-Bule-SEC thead tr {
    &:nth-child(odd) {

        td,
        th {
            background-color: rgba(198, 248, 245, 1);
            text-align: center;
            height: 40px;
            font-weight: bold;
        }
    }

    &:nth-child(even) {

        td,
        th {
            background-color: rgba(198, 248, 245, 0.5);
            text-align: center;
            height: 30px;
            font-weight: bold;
        }
    }
}

//table-ecool-pink-SEC  
.table-ecool-pink-SEC thead tr {
    &:nth-child(odd) {

        td,
        th {
            background-color: rgba(232, 209, 230, 1);
            text-align: center;
            height: 40px;
            font-weight: bold;
        }
    }

    &:nth-child(even) {

        td,
        th {
            background-color: rgba(232, 209, 230, 0.5);
            text-align: center;
            height: 30px;
            font-weight: bold;
        }
    }
}

//table-ecool-yellow-SEC  
.table-ecool-yellow-SEC thead tr {
    &:nth-child(odd) {

        td,
        th {
            background-color: rgba(227, 236, 159, 1);
            text-align: center;
            height: 40px;
            font-weight: bold;
        }
    }

    &:nth-child(even) {

        td,
        th {
            background-color: rgba(227, 236, 159, 0.5);
            text-align: center;
            height: 30px;
            font-weight: bold;
        }
    }
}

//table-ecool-Tangerine-SEC  
.table-ecool-Tangerine-SEC thead tr {
    &:nth-child(odd) {

        td,
        th {
            background-color: rgba(228, 199, 130, 1);
            text-align: center;
            height: 40px;
            font-weight: bold;
        }
    }

    &:nth-child(even) {

        td,
        th {
            background-color: rgba(228, 199, 130, 0.5);
            text-align: center;
            height: 30px;
            font-weight: bold;
        }
    }
}

//table-ecool-Tangerine2-SEC
.table-ecool-Tangerine2-SEC thead tr {
    &:nth-child(odd) {

        td,
        th {
            background-color: rgba(245, 201, 175, 1);
            text-align: center;
            height: 40px;
            font-weight: bold;
        }
    }

    &:nth-child(even) {

        td,
        th {
            background-color: rgba(245, 201, 175, 0.5);
            text-align: center;
            height: 30px;
            font-weight: bold;
        }
    }
}

//table-ecool-AWA004
.table-ecool-AWA004 {
    thead tr {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }

    tbody tr {

        td,
        th {
            color: #333333;
            background-color: rgba(243, 200, 176, 0.2);
        }
    }
}

//panel樣式
.panel-ez {
    margin-bottom: 20px;
    background-color: #ffffff;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);

    &-body {
        padding: 15px;

        &:before {
            display: table;
            content: " ";
        }

        &:after {
            display: table;
            content: " ";
            clear: both;
        }

        &:before {
            display: table;
            content: " ";
        }

        &:after {
            display: table;
            content: " ";
            clear: both;
        }
    }

    .list-group {
        margin-bottom: 0;

        .list-group-item {
            border-width: 1px 0;

            &:first-child {
                border-top-right-radius: 0;
                border-top-left-radius: 0;
            }

            &:last-child {
                border-bottom: 0;
            }
        }
    }

    .table {
        margin-bottom: 0;
    }

    .panel-body+.table {
        border-top: 1px solid #dddddd;
    }
}

.panel-danger-ez {
    border-color: #f2dede;

    .panel-heading {
        color: #b94a48;
        background-color: #f2dede;
        border-color: #f2dede;

        +.panel-collapse .panel-body {
            border-top-color: #eed3d7;
        }
    }

    .panel-footer+.panel-collapse .panel-body {
        border-bottom-color: #eed3d7;
    }
}


//dropdown-menuEz
.dropdown-menuEz {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    background-clip: padding-box;

    &.pull-right {
        right: 0;
        left: auto;
    }

    .divider {
        height: 1px;
        margin: 9px 0;
        overflow: hidden;
        background-color: #e5e5e5;
    }

    li>a {
        display: block;
        clear: both;
        font-weight: normal;
        line-height: 1.428571429;
        color: #333333;
        white-space: nowrap;
        letter-spacing: 2px;

        &:hover,
        &:focus {
            color: #000000;
            text-decoration: none;
            background-color: #fcff5a;
        }
    }

    .active>a {
        color: #000000;
        text-decoration: none;
        background-color: #FFFFBF;
        outline: 0;

        &:hover,
        &:focus {
            color: #000000;
            text-decoration: none;
            background-color: #FFFFBF;
            outline: 0;
        }
    }

    .disabled>a {
        color: #999999;

        &:hover,
        &:focus {
            color: #999999;
        }

        &:hover,
        &:focus {
            text-decoration: none;
            cursor: not-allowed;
            background-color: transparent;
            background-image: none;
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
        }
    }
}

.open>.dropdown-menuEz {
    display: block;
}

.pull-right>.dropdown-menuEz {
    right: 0;
    left: auto;
}

.dropdown-menuEz {
    top: auto;
    bottom: 100%;
    margin-bottom: 1px;
}

@media (min-width: 767px) {
    .dropdown-menuEz {
        right: 0;
        left: auto;
    }
}

.dropdown-menuEz {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

@media (max-width: 767px) {
    .navbar-nav .open .dropdown-menuEz {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;

        li>a,
        .dropdown-header {
            padding: 5px 0px 5px 55px;
        }

        li>a {
            line-height: 20px;

            &:hover,
            &:focus {
                background-image: none;
            }
        }
    }
}

//button 按鈕樣式
.button_groupEz_pink {
    border: 1px solid #FEE8E9;
    letter-spacing: 1.4px;
    font-weight: bold;
    color: #000000;
    background-color: #FFFFFF;
    min-width: 124px;
    min-height: 29px;
    margin-right: 20px;

    &:hover {
        border: 1px solid #FEE8E9;
        background-color: #FEE8E9;
    }
}

.btn-bold {
    font-weight: bold;
}

.btn-pink {
    color: #000000;
    background-color: #FFFFFF;
    margin-bottom: 3px;

    &:hover,
    &:focus,
    &:active,
    &.active {
        color: #000000;
        background-color: #FEE8E9;
        border-color: #FEE8E9;
        box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
    }
}

@media print {
    .btn-pink {
        letter-spacing: 10px;
        border: 1.5px solid #FEE8E9;
    }
}

@media screen and (min-width: 1920px) {
    .btn-pink {
        letter-spacing: 2px;
        border: 1.5px solid #FEE8E9;
    }
}

@media screen and (min-width: 1440px) and (max-width: 1920px) {
    .btn-pink {
        letter-spacing: 1.4px;
        border: 1.5px solid #FEE8E9;
    }
}

@media screen and (max-width: 1439px) {
    .btn-pink {
        border: 1.3px solid #FEE8E9;
    }
}

.open .dropdown-toggle.btn-pink {
    color: #000000;
    background-color: #FEE8E9;
    border-color: #FEE8E9;
    box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-pink {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-pink {
    background-image: none;
}

.btn-pink {

    &.disabled,
    &[disabled] {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-pink {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-pink {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-pink:hover {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-pink {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-pink:focus {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-pink {

    &.disabled:active,
    &[disabled]:active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-pink:active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-pink {

    &.disabled.active,
    &[disabled].active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-pink.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

/*btn-Basic*/

.btn-Basic {
    color: #000000;
    background-color: #FFFFFF;
    border-color: #F2F2F2;
    margin-bottom: 3px;

    &:hover,
    &:focus,
    &:active,
    &.active {
        color: #000000;
        background-color: #F2F2F2;
        border-color: #FFFFFF;
        box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
    }
}

@media print {
    .btn-Basic {
        letter-spacing: 10px;
        border: 1.5px solid #F2F2F2;
    }
}

@media screen and (min-width: 1920px) {
    .btn-Basic {
        letter-spacing: 2px;
        border: 1.5px solid #F2F2F2;
    }
}

@media screen and (min-width: 1440px) and (max-width: 1920px) {
    .btn-Basic {
        letter-spacing: 1.4px;
        border: 1.5px solid #F2F2F2;
    }
}

@media screen and (max-width: 1439px) {
    .btn-Basic {
        border: 1.3px solid #F2F2F2;
    }
}

.open .dropdown-toggle.btn-Basic {
    color: #000000;
    background-color: #F2F2F2;
    border-color: #FFFFFF;
    box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-Basic {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-Basic {
    background-image: none;
}

.btn-Basic {

    &.disabled,
    &[disabled] {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-Basic {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-Basic {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-Basic:hover {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-Basic {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-Basic:focus {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-Basic {

    &.disabled:active,
    &[disabled]:active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-Basic:active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-Basic {

    &.disabled.active,
    &[disabled].active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-Basic.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

/*btn-yellow*/

.btn-yellow {
    color: #000000;
    background-color: #F8F3A5;
    margin-bottom: 3px;
    margin-top: 3px;

    &:hover,
    &:focus,
    &:active,
    &.active {
        color: #000000;
        background-color: #FFD700;
        box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
    }
}

.open .dropdown-toggle.btn-yellow {
    color: #000000;
    background-color: #FFD700;
    box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-yellow {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-yellow {
    background-image: none;
}

.btn-yellow {

    &.disabled,
    &[disabled] {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-yellow {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-yellow {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-yellow:hover {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-yellow {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-yellow:focus {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-yellow {

    &.disabled:active,
    &[disabled]:active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-yellow:active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-yellow {

    &.disabled.active,
    &[disabled].active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-yellow.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-link-ez {
    font-weight: normal;
    color: #428bca;
    cursor: pointer;
    border-radius: 0;
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;

    &:active,
    &[disabled] {
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}

fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.btn-link-ez {
    border-color: transparent;

    &:hover,
    &:focus,
    &:active {
        border-color: transparent;
    }

    &:hover,
    &:focus {
        color: #2a6496;
        text-decoration: underline;
        background-color: transparent;
    }

    &[disabled]:hover {
        color: #999999;
        text-decoration: none;
    }
}

fieldset[disabled] .btn-link-ez:hover,
.btn-link-ez[disabled]:focus,
fieldset[disabled] .btn-link-ez:focus {
    color: #999999;
    text-decoration: none;
}

//btn-table-link

.btn-table-link {
    font-weight: normal;
    color: #004da0;
    cursor: pointer;
    border-radius: 0;
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;

    &:active,
    &[disabled] {
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}

fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.btn-table-link {
    border-color: transparent;

    &:hover,
    &:focus,
    &:active {
        border-color: transparent;
    }

    &:hover,
    &:focus {
        color: #2a6496;
        text-decoration: underline;
        background-color: transparent;
    }

    &[disabled]:hover {
        color: #999999;
        text-decoration: none;
    }
}

fieldset[disabled] .btn-link:hover,
.btn-table-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
    color: #999999;
    text-decoration: none;
}

.btn-social-lg {
    position: relative;
    padding-left: 55px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    > :first-child {
        position: absolute;
        left: 2px;
        top: 2px;
        bottom: 0;
        width: 40px;
        line-height: 46px;
        font-size: 1.6em;
        text-align: center;

        /*border-right: 1px solid rgba(0,0,0,0.2);*/
    }
}

.btn-dropbox {
    color: #fff;
    background-color: #009eaf;
    border-color: rgba(0, 0, 0, 0.2);
}

.btn-social {
    position: relative;
    padding-left: 44px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    > :first-child {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 32px;
        line-height: 34px;
        font-size: 1.6em;
        text-align: center;
    }
}

.btn-social-lg {
    position: relative;
    padding-left: 5px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    > :first-child {
        position: absolute;
        left: 2px;
        top: 2px;
        bottom: 0;
        width: 40px;
        line-height: 46px;
        font-size: 1.6em;
        text-align: center;
    }
}

.btn-sys {
    border-color: #FAE20A;
    border-width: 1px 1px 5px 1px;
    border-style: solid;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    font-size: 12px;
    padding: 2px 6px 2px 6px;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
    color: #242424;
    background-color: #F9F9F9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#FAF219));
    background-image: -webkit-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: -moz-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: -ms-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: -o-linear-gradient(top, #F9F9F9, #FAF219);
    background-image: linear-gradient(to bottom, #F9F9F9, #FAF219);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#FAF219);
    margin-bottom: 15px;
    margin-right: 2px;

    &:hover,
    &:focus,
    &:active,
    &.active {
        border-color: #FAE319;
        border-width: 1px 1px 5px 1px;
        border-style: solid;
        background-color: #F9F9F9;
        background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#FAD312));
        background-image: -webkit-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: -moz-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: -ms-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: -o-linear-gradient(top, #F9F9F9, #FAD312);
        background-image: linear-gradient(to bottom, #F9F9F9, #FAD312);
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#FAD312);
    }
}

.open .dropdown-toggle.btn-sys {
    border-color: #FAE319;
    border-width: 1px 1px 5px 1px;
    border-style: solid;
    background-color: #F9F9F9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#FAD312));
    background-image: -webkit-linear-gradient(top, #F9F9F9, #FAD312);
    background-image: -moz-linear-gradient(top, #F9F9F9, #FAD312);
    background-image: -ms-linear-gradient(top, #F9F9F9, #FAD312);
    background-image: -o-linear-gradient(top, #F9F9F9, #FAD312);
    background-image: linear-gradient(to bottom, #F9F9F9, #FAD312);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#FAD312);
}

.btn-sys {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-sys {
    background-image: none;
}

.btn-sys {

    &.disabled,
    &[disabled] {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys:hover {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys:focus {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys {

    &.disabled:active,
    &[disabled]:active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys:active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys {

    &.disabled.active,
    &[disabled].active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

@media screen and (max-width: 767px) {
    .btn-sys {
        font-size: 10px;
    }
}

.btn-sys-busker {
    border-color: #84c1ff;
    border-width: 1px 1px 5px 1px;
    border-style: solid;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    font-size: 12px;
    padding: 2px 6px 2px 6px;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
    color: #242424;
    background-color: #F9F9F9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#c4e1ff));
    background-image: -webkit-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: -moz-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: -ms-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: -o-linear-gradient(top, #F9F9F9, #c4e1ff);
    background-image: linear-gradient(to bottom, #F9F9F9, #c4e1ff);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#c4e1ff);
    margin-bottom: 15px;
    margin-right: 2px;

    &:hover,
    &:focus,
    &:active,
    &.active {
        border-color: #84c1ff;
        border-width: 1px 1px 5px 1px;
        border-style: solid;
        background-color: #F9F9F9;
        background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#005ab5));
        background-image: -webkit-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: -moz-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: -ms-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: -o-linear-gradient(top, #F9F9F9, #005ab5);
        background-image: linear-gradient(to bottom, #F9F9F9, #005ab5);
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#005ab5);
    }
}

.open .dropdown-toggle.btn-sys-busker {
    border-color: #84c1ff;
    border-width: 1px 1px 5px 1px;
    border-style: solid;
    background-color: #F9F9F9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#F9F9F9), to(#005ab5));
    background-image: -webkit-linear-gradient(top, #F9F9F9, #005ab5);
    background-image: -moz-linear-gradient(top, #F9F9F9, #005ab5);
    background-image: -ms-linear-gradient(top, #F9F9F9, #005ab5);
    background-image: -o-linear-gradient(top, #F9F9F9, #005ab5);
    background-image: linear-gradient(to bottom, #F9F9F9, #005ab5);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#005ab5);
}

.btn-sys-busker {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-sys-busker {
    background-image: none;
}

.btn-sys-busker {

    &.disabled,
    &[disabled] {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys-busker {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys-busker {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys-busker:hover {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys-busker {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys-busker:focus {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys-busker {

    &.disabled:active,
    &[disabled]:active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys:active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

.btn-sys-busker {

    &.disabled.active,
    &[disabled].active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #9D9D9D;
    }
}

fieldset[disabled] .btn-sys-busker.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #9D9D9D;
}

@media screen and (max-width: 767px) {
    .btn-sys-busker {
        font-size: 10px;
    }
}

.btn:focus {
    outline: 3px solid #333;
}

.btn-sys-style {
    padding: 2px 6px 2px 6px;
    border-width: 1px 1px 5px 1px;
    border-style: solid;
    border-radius: 8px;
    font-weight: 600;
    color: #000;
    box-shadow: inset 0 20px 21px rgba(255, 255, 255, 0.9);

    &:hover,
    &:focus {
        color: #000;
        box-shadow: inset 0 18px 12px rgba(255, 255, 255, 0.7);
    }

    &.active {
        color: #000;
        box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2), inset 0 0 0 100px #fff;
    }
}

.btn-sys-margin {
    margin-bottom: 15px;
    margin-right: 2px;
}

//font文字樣式
.font_Last_Day_MEMO {
    line-height: 24px;
}

//橘色登入框
.font_Menu {
    line-height: 22px;
    color: #000000;
    background-size: 100% 100%;

    /*for IE*/
    max-width: 100%;
    width: auto\9;

    /* IE6, IE7, IE8, IE9 */
    width: 100%;

    >a {
        position: relative;
        z-index: 1;
    }

    .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
        width: 100%;

        .btn-default {
            margin: 0 0 2px 0;
        }
    }
}

.td_Menu_red {
    line-height: 25px;
    color: #000000;
    background-color: rgba(247, 201, 201, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.td_Menu_Coo {
    line-height: 25px;
    color: #000000;
    background-color: rgba(234, 220, 190, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.td_Menu_blue {
    line-height: 25px;
    color: #000000;
    background-color: rgba(226, 225, 254, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.td_Menu_green {
    line-height: 25px;
    color: #000000;
    background-color: rgba(237, 245, 225, 0.4);
    text-align: center;
    max-width: 155px;
    width: 85%;
}

.font_NAME {
    font-size: 18px;
    font-weight: bold;
    color: #333333;
}

.font_Cash {
    font-size: 180%;
    color: #000000;
    vertical-align: bottom;
}

.layout_font_title {
    font-size: 20px;
    font-weight: bold;
    color: #004aa2;
    text-align: center;
}

/*認證徵章*/
@media screen and (min-width: 1500px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 50px;
    }
}

@media screen and (max-width: 1500px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 32px;
    }
}

@media screen and (max-width: 1194px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 29px;
    }
}

@media screen and (max-width: 1024px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 28px;
    }
}

@media screen and (max-width: 767px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 100%;
        max-width: 100%;
    }
}

@media screen and (max-width: 480px) {
    .imgEz_Badge {
        width: auto;
        height: auto;
        max-height: 55px;
    }
}

/*護照徵章*/
@media screen and (min-width: 1501px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 45px;
    }
}

@media screen and (max-width: 1500px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 38px;
    }
}

@media screen and (max-width: 1194px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 32px;
    }
}

@media screen and (max-width: 1024px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 30px;
    }
}

@media screen and (max-width: 767px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 100%;
        max-width: 100%;
    }
}

@media screen and (max-width: 480px) {
    .imgEz_BadgeBook {
        width: auto;
        height: auto;
        max-height: 45px;
    }
}

.imgfooter {
    display: block;
    width: 100%;
    height: auto;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    margin: 0px auto;
    text-align: center;
}


@media screen and (min-width: 1500px) {
    .imgfooter {
        padding-top: 5px;
        font-size: 16px;
        min-height: 34px;
        max-width: 905px;

        >font {
            font-size: 16px;
        }
    }
}

@media screen and (max-width: 1500px) {
    .imgfooter {
        padding-top: 2px;
        font-size: 13px;
        min-height: 25px;
        max-width: 905px;

        >font {
            font-size: 13px;
        }
    }
}

@media screen and (max-width: 480px) {
    .imgfooter {
        font-size: 10px;
        max-width: 480px;

        >font {
            font-size: 10px;
        }
    }
}

@media screen and (max-width: 360px) {
    .imgfooter {
        font-size: 9px;
        max-width: 320px;

        >font {
            font-size: 9px;
        }
    }
}

//web 手機MENU
@media screen and (max-width: 767px) {
    .Div-Menu {
        background-color: #E6EB42;
        top: 50px;
        left: 0;
        position: fixed;
        z-index: 2;
    }

    .Top_footer {
        position: fixed;
        bottom: 0;
        right: 10px;
        height: 58px;
    }
}

@media screen and (max-width: 321px) {
    .Div-Menu {
        top: 50px;
        position: fixed;
        z-index: 2;
    }

    .Top_footer {
        position: fixed;
        bottom: 0;
        right: 10px;
        height: 58px;
    }
}

.containerEZ {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;

    @media all and (min-width: 1200px) {
        max-width: 1190px;
    }

    &::before,
    &::after {
        display: table;
        content: " ";
    }
}

body>.containerEZ>.row {
    @media all and (max-width: 767px) {
        margin-left: 0;
        margin-right: 0;
    }
}

.containerEZ-fluid {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;

    &::before,
    &::after {
        display: table;
        content: " ";
    }
}


.containerEZ>.navbar-header,
.containerEZ-fluid>.navbar-header,
.containerEZ>.navbar-collapse,
.containerEZ-fluid>.navbar-collapse {
    margin-right: -15px;
    margin-left: -15px;

    @media all and (min-width: 767px) {
        margin-right: 0px;
        margin-left: 0px;
    }
}

.containerEZ .jumbotron,
.containerEZ-fluid .jumbotron {
    padding-right: 60px;
    padding-left: 60px;
    border-radius: 6px;
}

@media (min-width: 767px) {
    .navbar-ECOOL {
        display: none !important;
    }
}

/*小於767px SHow*/
@media (max-width: 767px) {
    .Div-navbar {
        display: block;
        display: none\9;
    }
}

/*大於768px hide*/
@media (min-width: 767px) {
    .Div-navbar {
        display: none;
    }
}

.navbar-brandEZ {

    &:hover,
    &:focus {
        text-decoration: none;
    }
}

@media (max-width: 767px) {
    .navbar>.container .navbar-brandEZ {
        margin-left: -15px;
    }
}

.vr_pagebreak {
    page-break-before: always;
}

.paginationW {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 4px;

    li {
        display: inline;



        a,
        span {
            position: relative;
            float: left;
            padding: 6px 12px;
            margin-left: -1px;
            line-height: 1.428571429;
            text-decoration: none;
            cursor: pointer;
        }

        &:first-child> {

            a,
            span {
                margin-left: 0;
                border-bottom-left-radius: 4px;
                border-top-left-radius: 4px;
            }
        }

        &:last-child> {

            a,
            span {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }


        a:hover,
        span:hover,
        a:focus,
        span:focus {
            color: #004da0;
        }
    }

    .active {

        a,
        span,
        a:hover,
        span:hover,
        a:focus,
        span:focus {
            z-index: 2;
            color: #333333;
            cursor: default;
            border-color: #333333;
            border: 1px solid;
        }
    }

    .disabled {
        span {
            color: #999999;
            cursor: not-allowed;
            border-color: #dddddd;
        }

        a {
            color: #999999;
            cursor: not-allowed;
            border-color: #dddddd;

            &:hover,
            &:focus {
                color: #999999;
                cursor: not-allowed;
                border-color: #dddddd;
            }
        }
    }
}

.input-xs {
    height: 25px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

select.input-xs {
    height: 25px;
    line-height: 25px;
}

textarea.input-xs {
    height: auto;
}

.lobinLabel {
    color: white;

    @include media-breakpoint-up(lg) {
        font-size: 15px;
        margin-right: 5px;
    }

}

.LoginBox {
    max-width: 95px;
    overflow: hidden;
    width: auto\9;

    /* IE6, IE7, IE8, IE9 */
    width: 100%;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.table_Menu {
    max-width: 192px;
    width: auto\9;
    /* IE6, IE7, IE8, IE9 */
    width: 100%;
}

.dl-horizontal-EZ {
    dt {
        float: left;
        overflow: hidden;
        clear: left;
        text-align: right;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 6px;
        color: #004da0;
    }

    dd {
        &:before {
            display: table;
            content: " ";
        }

        &:after {
            display: table;
            content: " ";
            clear: both;
        }

        &:before {
            display: table;
            content: " ";
        }

        &:after {
            display: table;
            content: " ";
            clear: both;
        }
    }
}

.dt {
    font-weight: bold;
    color: #004da0;
    margin-left: 10px;
}

.dd {
    font-weight: bold;
}

@media print {
    .col-md-5 col-sm-6 dl-horizontal-EZ {
        dd {
            background-color: black;
        }

        dt {
            font-size: 3cm;
        }
    }

    .col-md-12 col-sm-12 {

        dl-h orizontal-EZ dd,
        dl-horizontal-EZ dt {
            font-size: 2cm;
        }
    }
}

@media (min-width: 1920px) {

    .dt,
    .dd {
        font-size: 16px;
    }
}

@media (max-width: 1920px) {

    .dt,
    .dd {
        font-size: 15px;
    }
}

@media (max-width: 1366px) {

    .dt,
    .dd {
        font-size: 14px;
    }
}

@media (max-width: 1024px) {

    .dt,
    .dd {
        font-size: 13px;
    }
}

@media (max-width: 767px) {

    .dt,
    .dd {
        font-size: 12px;
    }
}

.p-context {
    word-wrap: break-word;
    word-break: normal;
    text-align: left;
    width: 95%;
    margin: 0px auto;
    overflow: hidden;

    img {
        height: auto;
        max-width: 100%;
    }
}

@media (min-width: 1366px) {
    .p-context {
        font-size: 14px;
        line-height: 24px;
    }
}

@media (max-width: 1366px) {
    .p-context {
        font-size: 14px;
        line-height: 20px;
    }
}

@media (max-width: 1024px) {
    .p-context {
        font-size: 13px;
        line-height: normal;
    }
}

@media (max-width: 767px) {
    .p-context {
        font-size: 12px;
        line-height: normal;
    }
}

.label_dt_font16 {
    font-size: 16px;
    font-weight: bold;
    color: #004da0;
}

.label_dt {
    font-weight: bold;
    color: #004da0;
}

.label_dt_S {
    font-size: 14px;
    font-weight: bold;
    color: #004da0;
}

.label_dd_font18 {
    font-size: 16px;
    font-weight: bold;
    color: #004da0;
    line-height: 24px;
    letter-spacing: 1.5px;
}

.label_dd {
    font-weight: bold;
    color: #333333;
}


@media (min-width: 1920px) {

    .label_dt,
    .label_dd {
        font-size: 16px;
    }
}

@media (max-width: 1920px) {

    .label_dt,
    .label_dd {
        font-size: 15px;
    }
}

@media (max-width: 1366px) {

    .label_dt,
    .label_dd {
        font-size: 14px;
    }
}

@media (max-width: 1024px) {

    .label_dt,
    .label_dd {
        font-size: 13px;
    }
}

@media (max-width: 767px) {

    .label_dt,
    .label_dd {
        font-size: 12px;
    }
}

.lnkFont {
    font-weight: bold;
    font-size: 16px;
}

.lnkFont2 {
    text-align: left;
    display: inline-block;
    width: 50px;
    font-weight: bold;
    font-size: 15pt;
    color: #0061CA;
}

//Div-EZ-task
.Div-EZ-task {
    width: 90%;
    background-color: rgba(173, 251, 242, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-Feedback {
        background-color: rgba(232, 209, 230, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-Pink
.Div-EZ-Pink {
    width: 90%;
    background-color: rgba(232, 209, 230, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-Feedback {
        background-color: rgba(232, 209, 230, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }

        .control-label-D,
        .control-label-left-D {
            font-size: 17px;
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-reader
.Div-EZ-reader {
    width: 90%;
    background-color: rgba(227, 236, 159, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-BK {
        background-color: rgba(227, 236, 159, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }

        .control-label-D,
        .control-label-left-D {
            font-size: 17px;
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}

//Div-EZ-ArtGallery
.Div-EZ-ArtGallery {
    width: 90%;
    margin: 0px auto;
    margin-top: -40px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Div-BK {
        background-color: rgba(227, 236, 159, 0.4);
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }

        .control-label-D,
        .control-label-left-D {
            font-size: 17px;
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}

//Div-EZ-ADDI06
.Div-EZ-ADDI06 {
    width: 90%;
    background-color: rgba(226, 197, 133, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-ADDI07
.Div-EZ-ADDI07 {
    width: 90%;
    background-color: rgba(226, 197, 133, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-ADDI05
.Div-EZ-ADDI05 {
    width: 90%;
    background-color: rgba(250, 206, 56, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}

//Div-EZ-Awat
.Div-EZ-Awat {
    width: 90%;
    background-color: rgba(243, 200, 176, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-Awat02
.Div-EZ-Awat02 {
    width: 90%;
    background-color: rgba(243, 200, 176, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-AWAI02
.Div-EZ-AWAI02 {
    width: 90%;
    background-color: rgba(225, 231, 250, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-ZZZI04
.Div-EZ-ZZZI04 {
    width: 90%;
    background-color: rgba(236, 237, 51, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-purpose
.Div-EZ-purpose {
    width: 90%;
    background-color: rgba(204, 255, 255, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-rpp
.Div-EZ-rpp {
    width: 90%;
    background-color: rgba(250, 233, 180, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }
}

//Div-EZ-ADDI09
.Div-EZ-ADDI09 {
    width: 90%;
    background-color: rgba(226, 197, 133, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}

//Div-EZ-SECI01
.Div-EZ-SECI01 {
    width: 90%;
    background-color: rgba(254, 225, 233, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}

//Div-EZ-ZZZI26
.Div-EZ-ZZZI26 {
    width: 90%;
    background-color: rgba(226, 234, 162, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}

//Div-EZ-ZZZI09
.Div-EZ-ZZZI09 {
    width: 90%;
    background-color: rgba(239, 171, 184, 0.2);
    margin: 0px auto;
    margin-top: -15px;

    .form-horizontal {
        width: 90%;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .Div-btn-center {
        text-align: center;
        margin: 0px auto;
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }
    }

    .table-ecool>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
        }
    }
}


@media (min-width: 767px) {
    .form-horizontal .control-label-left {
        text-align: left\9;
    }
}


.imgTitle {
    display: block;
    width: 100%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    margin: 0px auto;
    text-align: center;
    min-height: 68px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .imgTitle {
        padding-top: 2px\9;
        font-size: 13px\9;
    }
}

@media screen and (min-width: 1500px) {
    .imgTitle {
        font-size: 16px;
    }
}

@media screen and (max-width: 1500px) {
    .imgTitle {
        padding-top: 2px;
        font-size: 13px;
    }
}

@media screen and (max-width: 480px) {
    .imgTitle {
        font-size: 10px;
    }
}

.imgTitleFont {
    color: #0b24fa;
    font-weight: bold;
    letter-spacing: 14px;
}

/* for  IE8 ~ IE10 */
@media screen\0 {
    .imgTitleFont {
        margin-top: 37px\9;
        font-size: 14px\9;
    }
}

@media screen and (min-width: 480px) {
    .imgTitleFont {
        margin-top: 37px;
        font-size: 14px;
    }
}

@media screen and (max-width: 480px) {
    .imgTitleFont {
        margin-top: 40px;
        font-size: 12px;
    }
}

.Title_Secondary {
    font-size: 16px;
    border-bottom: 1px solid #ccc;
    letter-spacing: 1px;
}

.Caption_Div_Left {
    font-size: 16px;
    font-weight: bold;
    color: #3293fb;
    margin: 5px 5px 2px 25px;
    text-align: left;
}

.Caption_Div {
    font-size: 16px;
    font-weight: bold;
    color: #3293fb;
    margin: 5px 5px 2px 25px;
}

@media screen and (max-width: 767px) {

    .Caption_Div_Left,
    .Caption_Div {
        font-size: 14px;
    }
}

.panel-Remove {
    background-color: transparent;
}

.panel-Img {
    background-color: rgba(226, 225, 254, 0.2);

    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    > {
        .panel-heading {
            margin-top: 0;
            margin-bottom: 0;
            letter-spacing: 16px;
            padding: 2px 2px;
            color: #0b24fa;
            background-color: rgba(226, 225, 254, 1);
            border-color: #c6c4e3;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            text-shadow: 2px 2px 2px #DDDDDD;

            +.panel-collapse .panel-body {
                border-top-color: #c6c4e3;
            }
        }

        .panel-footer+.panel-collapse .panel-body {
            border-bottom-color: #bce8f1;
        }
    }
}

.panel-ZZZ {
    background-color: rgba(226, 225, 254, 0.2);
}

.panel-ZZZ-Color2 {
    background-color: rgba(226, 225, 254, 0.4);
}

.panel-ZZZ-Color3 {
    background-color: rgba(226, 225, 254, 0.6);
}

.panel-ZZZ-Color4 {
    background-color: rgba(226, 225, 254, 0.8);
}

.panel-ZZZ-Color5 {
    background-color: rgba(226, 225, 254, 1);
}

.panel-ZZZ {
    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .panel-heading {
        margin-top: 0;
        margin-bottom: 0;
        letter-spacing: 16px;
        padding: 7px 7px;
        color: #0b24fa;
        background-color: rgba(226, 225, 254, 1);
        border-color: #c6c4e3;
        font-size: 16px;
        font-weight: bold;
        text-decoration: none;
        text-shadow: 2px 2px 2px #DDDDDD;

        +.panel-collapse .panel-body {
            border-top-color: #c6c4e3;
        }
    }

    .panel-footer+.panel-collapse .panel-body {
        border-bottom-color: #bce8f1;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }

        .Div-btn-center {
            text-align: center;
            margin: 0px auto;
            padding-top: 10px;
        }
    }

    .table-ecool-ZZZ {
        thead tr {

            td,
            th {
                font-weight: bold;
                text-align: center;
            }
        }

        tfoot {

            th:before,
            td:before {
                color: #220088;
                margin-right: 10px;
            }
        }

        tbody td:before {
            color: #220088;
            margin-right: 10px;
        }
    }
}

@media (max-width: 767px) {
    .panel-ZZZ .table-ecool-ZZZ {
        thead tr {

            td,
            th {
                text-align: left;
            }
        }

        tr:nth-child(odd) {
            background-color: #FAF2FF;
        }

        border-collapse: separate;
        border-spacing: 0px 2px;
    }
}

.panel-ACC {
    background-color: rgba(237, 245, 225, 0.2);
}

.panel-ACC-Color2 {
    background-color: rgba(237, 245, 225, 0.4);
}

.panel-ACC-Color3 {
    background-color: rgba(237, 245, 225, 0.6);
}

.panel-ACC-Color4 {
    background-color: rgba(237, 245, 225, 0.8);
}

.panel-ACC-Color5 {
    background-color: rgba(237, 245, 225, 1);
}

.panel-ACC {
    .Details {
        margin: 0px auto;
        padding-top: 20px;
    }

    .panel-heading {
        margin-top: 0;
        margin-bottom: 0;
        letter-spacing: 16px;
        padding: 7px 7px;
        color: #0b24fa;
        background-color: rgba(237, 245, 225, 1);
        border-color: #c6c4e3;
        font-size: 16px;
        font-weight: bold;
        text-decoration: none;
        text-shadow: 2px 2px 2px #DDDDDD;
    }

    .panel-Group {
        margin-top: 0;
        margin-bottom: 0;
        letter-spacing: 16px;
        padding: 7px 7px;
        color: #004da0;
        background-color: rgba(237, 245, 225, 8);
        border-color: #c6c4e3;
        font-size: 16px;
        font-weight: bold;
        text-decoration: none;
        border-bottom: 1px solid #DDDDDD;
    }

    .panel-heading+.panel-collapse .panel-body {
        border-top-color: #c6c4e3;
    }

    .panel-footer+.panel-collapse .panel-body {
        border-bottom-color: #bce8f1;
    }

    .form-horizontal {

        .control-label,
        .control-label-left {
            font-weight: bold;
            color: #004da0;
        }

        .Div-btn-center {
            text-align: center;
            margin: 0px auto;
            padding-top: 10px;
        }
    }

    .table-ecool-ACC>thead>tr> {

        td,
        th {
            font-weight: bold;
            text-align: center;
            color: #004da0;
            text-overflow: ellipsis;
        }
    }
}

.Group {
    font-weight: bold;
    color: #004da0;
    background-color: #ffffff;
    border-bottom: 1px solid #edf5e1;
}

//btn-SchoolList
.btn-SchoolList {
    color: #000000;
    background-color: #FFFFFF;
    margin-bottom: 3px;
    letter-spacing: 2px;
    border: 1.5px solid #c3e4f6;
    font-weight: bold;

    &:hover,
    &:focus,
    &:active,
    &.active {
        color: #000000;
        background-color: #c3e4f6;
        border-color: #c3e4f6;
        box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
    }
}

.open .dropdown-toggle.btn-SchoolList {
    color: #000000;
    background-color: #c3e4f6;
    border-color: #c3e4f6;
    box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-SchoolList {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-SchoolList {
    background-image: none;
}

.btn-SchoolList {

    &.disabled,
    &[disabled] {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-SchoolList {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-SchoolList {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-SchoolList:hover {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-SchoolList {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-SchoolList:focus {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-SchoolList {

    &.disabled:active,
    &[disabled]:active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-SchoolList:active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.btn-SchoolList {

    &.disabled.active,
    &[disabled].active {
        background-color: #E0E0E0;
        border-color: #E0E0E0;
        color: #999999;
        cursor: not-allowed;
    }
}

fieldset[disabled] .btn-SchoolList.active {
    background-color: #E0E0E0;
    border-color: #E0E0E0;
    color: #999999;
    cursor: not-allowed;
}

.navbar-phone {
    background-color: #FFFFFF;
    border-color: #FFFFFF;
    border-bottom: 1px solid #E6EB42;

    .navbar-brand {
        color: #ffffff;

        &:hover,
        &:focus {
            color: #ffffff;
            background-color: none;
        }
    }

    .navbar-text {
        color: #dddddd;
    }

    .navbar-nav> {
        li>a {
            color: #ffffff;

            &:hover,
            &:focus {
                color: #ffffff;
                background-color: #178acc;
            }
        }

        .active>a {
            color: #ffffff;
            background-color: #178acc;

            &:hover,
            &:focus {
                color: #ffffff;
                background-color: #178acc;
            }
        }

        .disabled>a {
            color: #dddddd;
            background-color: transparent;

            &:hover,
            &:focus {
                color: #dddddd;
                background-color: transparent;
            }
        }
    }

    .btn-navbar {
        border-color: #FFFFFF;

        &:hover,
        &:focus {
            background-color: #fff;
        }

        .icon-bar {
            background-color: #000000;
        }
    }

    .navbar-collapse,
    .navbar-form {
        border-color: #1995dc;
    }

    .navbar-nav>.open>a {
        background-color: #178acc;
        color: #ffffff;

        &:hover,
        &:focus {
            background-color: #178acc;
            color: #ffffff;
        }
    }

    .navbar-link {
        color: #ffffff;

        &:hover {
            color: #ffffff;
        }
    }

    .btn-link {
        color: #ffffff;

        &:hover,
        &:focus {
            color: #ffffff;
        }

        &[disabled]:hover {
            color: #dddddd;
        }
    }
}

@media (max-width: 767px) {
    .navbar-phone .navbar-nav .open .dropdown-menu> {
        li>a {
            color: #ffffff;

            &:hover,
            &:focus {
                color: #ffffff;
                background-color: #178acc;
            }
        }

        .active>a {
            color: #ffffff;
            background-color: #178acc;

            &:hover,
            &:focus {
                color: #ffffff;
                background-color: #178acc;
            }
        }

        .disabled>a {
            color: #dddddd;
            background-color: transparent;

            &:hover,
            &:focus {
                color: #dddddd;
                background-color: transparent;
            }
        }
    }
}

fieldset[disabled] .navbar-phone .btn-link:hover,
.navbar-phone .btn-link[disabled]:focus,
fieldset[disabled] .navbar-phone .btn-link:focus {
    color: #dddddd;
}

.btn-navbar {
    float: left !important;
    margin-top: 12px;
    margin-bottom: 8px;
    background-color: #fff;
}

.btn-logo {
    float: left !important;
    position: relative;
    margin-left: 10px;
}

.btn-font-school {
    margin-top: 12px;
    margin-right: 5px;
    color: #000000;
    font-size: 18px;
    font-weight: bold;
}

.btn-logo-layout,
.btn-logo-school,
.btn-font-school {
    float: left !important;
    position: relative;
}

.btn-logo>img,
.btn-logo-layout>img,
.btn-logo-school>img {
    margin-top: 3px;
    margin-right: 5px;
    margin-bottom: 3px;
    max-height: 47px;
}

.line-left {
    float: left !important;
    background-color: #d0d0d0;
    height: 37px;
    width: 1px;
    margin-top: 10px;
    margin-bottom: 5px;
    margin-left: 10px;
    margin-right: 10px;
}

.line-right {
    float: right !important;
    background-color: #d0d0d0;
    height: 37px;
    width: 1px;
    margin-top: 10px;
    margin-bottom: 5px;
    margin-left: 10px;
    margin-right: 10px;
}

.btn-User {
    float: right !important;
    margin-top: 12px;
    margin-right: 25px;
    background-color: #fff;
}

@media (max-width: 370px) {

    .btn-logo-school,
    .line-right,
    .line-left {
        display: none !important;
    }

    .btn-logo>img,
    .btn-logo-layout>img,
    .btn-logo-school>img {
        margin-right: 10px;
        max-height: 40px;
    }
}

.btn-navbar .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px;

    +.icon-bar {
        margin-top: 4px;
    }
}


@media (min-width: 767px) {
    .btn-navbar {
        display: none;
    }
}

.navPhone {
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;

    &:before {
        display: table;
        content: " ";
    }

    &:after {
        display: table;
        content: " ";
        clear: both;
    }

    &:before {
        display: table;
        content: " ";
    }

    &:after {
        display: table;
        content: " ";
        clear: both;
    }

    >li {
        position: relative;
        display: block;
        font-size: 15px;
        font-weight: bold;

        >a {
            position: relative;
            display: block;
            padding: 10px 15px;
            letter-spacing: 2px;

            &:hover,
            &:focus {
                text-decoration: none;
                background-color: #eeeeee;
            }
        }

        &.disabled>a {
            background-color: #e5e5e5;

            &:hover,
            &:focus {
                background-color: #e5e5e5;
                text-decoration: none;
                cursor: not-allowed;
                background-color: transparent;
            }
        }
    }

    .open {
        >a {
            background-color: #eeeeee;

            &:hover,
            &:focus {
                background-color: #eeeeee;
            }
        }

        .caret {
            border-top: 4px solid #008500;
        }
    }

    .navPhone-divider {
        height: 1px;
        margin: 9px 0;
        overflow: hidden;
        background-color: #e5e5e5;
    }

    >li>a>img {
        max-width: none;
    }
}

.iconPhone {
    margin-right: 5px;
    font-size: 1em;
}

.MyName {
    margin-top: 5px;
    margin-left: 5px;
    font-size: 15px;
    font-weight: bold;
}

.Title {
    white-space: nowrap;
    font-weight: bold;
}

@media (min-width: 1281px) {
    .Title {
        font-size: 16pt;
    }

    .navbar-Cach {
        width: 174px;
        min-width: 174px;
        padding-left: 55px;
        text-align: center;
        background-position-y: center;
        background-position-x: left;
        background-repeat: no-repeat;
    }

    .navbar-Cach-font {
        display: none !important;
    }

    ._Layout_RightBar {
        display: block !important;
    }
}

@media (min-width: 990px) and (max-width: 1280px) {
    .Title {
        font-size: 15pt;
    }

    .navbar-Cach {
        display: none !important;
    }

    .navbar-Cach-font {
        display: inline !important;
    }

    ._Layout_RightBar {
        display: block !important;
    }
}

@media (max-width: 989px) {
    .Title {
        font-size: 10pt;
    }

    .navbar-Cach {
        display: none !important;
    }

    .navbar-Cach-font {
        display: inline !important;
    }

    ._Layout_RightBar {
        display: none !important;
    }
}

.form-inlineEZ {
    padding-bottom: 10px;

    .form-group {
        display: inline;
        vertical-align: middle;
        padding-bottom: 10px;

        >label {
            margin-right: 10px;
            display: inline;
            font-size: 18px;
            font-weight: bold;
        }
    }
}

.form-control-Login {
    width: 180px;
    height: 35px;
    display: inline;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555555;
    vertical-align: middle;
    background-color: #ffffff;
    border: 1px solid #f8b4af;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

@media (min-width: 767px) {
    .btn-default-Login {
        margin: 2px;
        padding: 2px;
    }
}

.btn-default-Login {
    color: #333333;
    background-color: #FEE8E9;
    border-color: #f8b4af;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 20px;
    padding-left: 30px;
    padding-right: 10px;

    &:hover,
    &:focus,
    &:active,
    &.active {
        color: #333333;
        background-color: #f8b4af;
        border-color: #f8b4af;
    }
}

.open .dropdown-toggle.btn-default-Login {
    color: #333333;
    background-color: #f8b4af;
    border-color: #f8b4af;
}

.btn-default-Login {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-default-Login {
    background-image: none;
}

.btn-default-Login {

    &.disabled,
    &[disabled] {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-Login {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-Login {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-Login:hover {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-Login {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-Login:focus {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-Login {

    &.disabled:active,
    &[disabled]:active {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-Login:active {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-Login {

    &.disabled.active,
    &[disabled].active {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-Login.active {
    background-color: #ffffff;
    border-color: #cccccc;
}

.Div-EZ-Right {
    text-align: right;
    padding-top: 15px;
    padding-bottom: 15px;
    padding-right: 35px;
    padding-left: 15px;
}

@media print {

    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11 {
        float: left;
    }

    .col-sm-12 {
        float: left;
        width: 100%;
    }

    .col-sm-11 {
        width: 91.66666667%;
    }

    .col-sm-10 {
        width: 83.33333333%;
    }

    .col-sm-9 {
        width: 75%;
    }

    .col-sm-8 {
        width: 66.66666667%;
    }

    .col-sm-7 {
        width: 58.33333333%;
    }

    .col-sm-6 {
        width: 50%;
    }

    .col-sm-5 {
        width: 41.66666667%;
    }

    .col-sm-4 {
        width: 33.33333333%;
    }

    .col-sm-3 {
        width: 25%;
    }

    .col-sm-2 {
        width: 16.66666667%;
    }

    .col-sm-1 {
        width: 8.33333333%;
    }

    .col-sm-pull-12 {
        right: 100%;
    }

    .col-sm-pull-11 {
        right: 91.66666667%;
    }

    .col-sm-pull-10 {
        right: 83.33333333%;
    }

    .col-sm-pull-9 {
        right: 75%;
    }

    .col-sm-pull-8 {
        right: 66.66666667%;
    }

    .col-sm-pull-7 {
        right: 58.33333333%;
    }

    .col-sm-pull-6 {
        right: 50%;
    }

    .col-sm-pull-5 {
        right: 41.66666667%;
    }

    .col-sm-pull-4 {
        right: 33.33333333%;
    }

    .col-sm-pull-3 {
        right: 25%;
    }

    .col-sm-pull-2 {
        right: 16.66666667%;
    }

    .col-sm-pull-1 {
        right: 8.33333333%;
    }

    .col-sm-pull-0 {
        right: auto;
    }

    .col-sm-push-12 {
        left: 100%;
    }

    .col-sm-push-11 {
        left: 91.66666667%;
    }

    .col-sm-push-10 {
        left: 83.33333333%;
    }

    .col-sm-push-9 {
        left: 75%;
    }

    .col-sm-push-8 {
        left: 66.66666667%;
    }

    .col-sm-push-7 {
        left: 58.33333333%;
    }

    .col-sm-push-6 {
        left: 50%;
    }

    .col-sm-push-5 {
        left: 41.66666667%;
    }

    .col-sm-push-4 {
        left: 33.33333333%;
    }

    .col-sm-push-3 {
        left: 25%;
    }

    .col-sm-push-2 {
        left: 16.66666667%;
    }

    .col-sm-push-1 {
        left: 8.33333333%;
    }

    .col-sm-push-0 {
        left: auto;
    }

    .col-sm-offset-12 {
        margin-left: 100%;
    }

    .col-sm-offset-11 {
        margin-left: 91.66666667%;
    }

    .col-sm-offset-10 {
        margin-left: 83.33333333%;
    }

    .col-sm-offset-9 {
        margin-left: 75%;
    }

    .col-sm-offset-8 {
        margin-left: 66.66666667%;
    }

    .col-sm-offset-7 {
        margin-left: 58.33333333%;
    }

    .col-sm-offset-6 {
        margin-left: 50%;
    }

    .col-sm-offset-5 {
        margin-left: 41.66666667%;
    }

    .col-sm-offset-4 {
        margin-left: 33.33333333%;
    }

    .col-sm-offset-3 {
        margin-left: 25%;
    }

    .col-sm-offset-2 {
        margin-left: 16.66666667%;
    }

    .col-sm-offset-1 {
        margin-left: 8.33333333%;
    }

    .col-sm-offset-0 {
        margin-left: 0%;
    }
}

.progress-bar-gray {
    background-color: #9D9D9D;
}

.progress-striped .progress-bar-gray {
    background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.btn-default-buy {
    color: #333333;
    background-color: #ffffff;
    border-color: #cccccc;

    &:hover,
    &:focus,
    &:active,
    &.active {
        color: #333333;
        background-color: #ebebeb;
        border-color: #adadad;
    }
}

.open .dropdown-toggle.btn-default-buy {
    color: #333333;
    background-color: #ebebeb;
    border-color: #adadad;
}

.btn-default-buy {

    &:active,
    &.active {
        background-image: none;
    }
}

.open .dropdown-toggle.btn-default-buy {
    background-image: none;
}

.btn-default-buy {

    &.disabled,
    &[disabled] {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-buy {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-buy {

    &.disabled:hover,
    &[disabled]:hover {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-buy:hover {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-buy {

    &.disabled:focus,
    &[disabled]:focus {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-buy:focus {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-buy {

    &.disabled:active,
    &[disabled]:active {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-buy:active {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default-buy {

    &.disabled.active,
    &[disabled].active {
        background-color: #ffffff;
        border-color: #cccccc;
    }
}

fieldset[disabled] .btn-default-buy.active {
    background-color: #ffffff;
    border-color: #cccccc;
}

.css-table {
    display: table;
    border-collapse: collapse;
    width: 100%;

    .thead {
        display: table-header-group;
    }

    .tbody {
        display: table-row-group;
    }

    .tr {
        display: table-row;
    }

    .th,
    .td {
        display: table-cell;
        padding-left: 3px;
    }
}

@media screen and (min-width: 361px) {
    .Div-EZ-ArtGallery-pointer {
        cursor: pointer;
        position: relative;
        width: 129px;
        height: 170px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book {
        width: 129px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img {
        top: 22px;
        left: 24px;
        position: absolute;
        width: 89px;
        height: 76px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 76px;
    }

    .Div-EZ-ArtGallery-img {
        max-width: 88px;
        max-height: 76px;
    }

    .Div-EZ-ArtGallery-text {
        width: 129px;
    }
}

/*小於360px SHow*/
@media screen and (max-width: 360px) {
    .Div-EZ-ArtGallery-pointer {
        cursor: pointer;
        position: relative;
        width: 100px;
        height: 131px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book {
        width: 100px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img {
        top: 17px;
        left: 18px;
        position: absolute;
        width: 69px;
        height: 59px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 59px;
    }

    .Div-EZ-ArtGallery-img {
        max-width: 69px;
        max-height: 59px;
    }

    .Div-EZ-ArtGallery-text {
        width: 100px;
    }
}

@media screen and (min-width: 1091px) {
    .Div-EZ-ArtGallery-pointer-all {
        cursor: pointer;
        position: relative;
        width: 170px;
        height: 165px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book-all {
        width: 170px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img-all {
        cursor: pointer;
        top: 15px;
        left: 20px;
        position: absolute;
        width: 130px;
        height: 110px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 110px;
    }

    .Div-EZ-ArtGallery-img-all {
        max-width: 120px;
        max-height: 105px;
    }

    .Div-EZ-ArtGallery-text-all {
        width: 120px;
    }

    .Div-EZ-ArtGallery-icon {
        top: 127px;
        left: 27px;
        position: absolute;
        z-index: 5;
        width: 32px;
        height: 46px;
    }
}

/*小於 1090px SHow*/
@media screen and (max-width: 1090px) {
    .Div-EZ-ArtGallery-pointer-all {
        cursor: pointer;
        position: relative;
        width: 136px;
        height: 132px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book-all {
        width: 136px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img-all {
        cursor: pointer;
        top: 12px;
        left: 16px;
        position: absolute;
        width: 104px;
        height: 88px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 88px;
    }

    .Div-EZ-ArtGallery-img-all {
        max-width: 96px;
        max-height: 84px;
    }

    .Div-EZ-ArtGallery-text-all {
        width: 96px;
    }

    .Div-EZ-ArtGallery-icon {
        top: 102px;
        left: 22px;
        position: absolute;
        z-index: 5;
        width: 25px;
        height: 36px;
    }
}

/*小於 361px SHow*/
@media screen and (max-width: 361px) {
    .Div-EZ-ArtGallery-pointer-all {
        cursor: pointer;
        position: relative;
        width: 82px;
        height: 79px;
        margin: 0px auto;
    }

    .Div-EZ-ArtGallery-book-all {
        width: 82px;
        z-index: 1;
    }

    .Div-EZ-ArtGallery-div-img-all {
        cursor: pointer;
        top: 7px;
        left: 10px;
        position: absolute;
        width: 62px;
        height: 53px;
        z-index: 2;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
        line-height: 53px;
    }

    .Div-EZ-ArtGallery-img-all {
        max-width: 62px;
        max-height: 53px;
    }

    .Div-EZ-ArtGallery-text-all {
        width: 77px;
    }

    .Div-EZ-ArtGallery-icon {
        top: 43px;
        left: 13px;
        position: absolute;
        z-index: 5;
        width: 22px;
        height: 31px;
    }
}

#DivAddButton {
    position: fixed;

    /*固定在網頁上不隨卷軸移動，若要隨卷軸移動用absolute*/
    bottom: 25px;
    right: -80px;

    /*設置水平位置，依所放的內容多寡需要自行手動調整*/
    background-color: rgba(255, 255, 244, 0.5);
    padding: 12px 10px 15px 10px;
    z-index: 99;
    border-radius: 10px;

    /*圓角*/
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;

    &:hover {
        /*當滑鼠移至此區塊時，伸縮區塊*/
        right: -10px;
    }

    #title {
        padding-right: 5px;

        /*讓標題與連結中間有空隙*/
    }
}

.use-absolute {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
    top: 40%;
    right: 0;
}

.use-absoluteDiv {
    position: absolute;
    width: 60%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
}

.rwd-table {
    background: #fff;
    overflow: hidden;

    tr:nth-of-type(2n) {
        background: #eee;
    }

    th,
    td {
        margin: 0.5em 1em;
    }

    min-width: 100%;

    th {
        display: none;
    }

    td {
        display: block;

        &:before {
            content: attr(data-th) " : ";
            font-weight: bold;
            width: 6.5em;
            display: inline-block;
        }
    }

    th,
    td {
        text-align: left;
    }

    th,
    td:before {
        color: #D20B2A;
        font-weight: bold;
        font-size: 18px;
    }
}

@media (min-width: 480px) {
    .rwd-table {
        td:before {
            display: none;
        }

        th,
        td {
            display: table-cell;
            padding: 0.25em 0.5em;
        }

        th:first-child,
        td:first-child {
            padding-left: 0;
        }

        th:last-child,
        td:last-child {
            padding-right: 0;
        }

        th,
        td {
            padding: 1em !important;
            font-size: 13px;
        }
    }
}

#table-breakpoint {

    th,
    td {
        padding: 1em !important;
        font-size: 18px;
    }
}

@media only screen and (max-width: 568px) {
    #table-breakpoint {
        thead {
            display: none;
        }

        tbody td {
            border: none !important;
            display: block;
            vertical-align: top;

            &:before {
                content: attr(data-th) ": ";
                display: inline-block;
                font-weight: bold;
                width: 6.5em;
            }

            &.bt-hide {
                display: none;
            }
        }

        th,
        td {
            padding: 1em !important;
            font-size: 13px;
        }
    }
}

/* Others */

.ui-icon {
    text-indent: inherit !important;
}

/* href disabled */

.not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: black;
    opacity: 0.5;
}


.text-nowrap {
    white-space: nowrap !important;
}

.form-control-required {
    background-color: #ffffe7;
}

.control-label-required::after {
    content: "*";
    color: #E04;
}

.hr-line-dashed {
    border: 1px dashed #ddd;
}

label {
    &.btn span {
        font-size: 1.5em;
    }

    input[type="radio"] {
        ~i.fa {
            &.fa-circle-o {
                color: #c8c8c8;
                display: inline;
            }

            &.fa-dot-circle-o {
                display: none;
            }
        }

        &:checked~i.fa {
            &.fa-circle-o {
                display: none;
            }

            &.fa-dot-circle-o {
                color: #7AA3CC;
                display: inline;
            }
        }
    }

    &:hover input[type="radio"]~i.fa {
        color: #7AA3CC;
    }

    input[type="checkbox"] {
        ~i.fa {
            &.fa-square-o {
                color: #c8c8c8;
                display: inline;
            }

            &.fa-check-square-o {
                display: none;
            }
        }

        &:checked~i.fa {
            &.fa-square-o {
                display: none;
            }

            &.fa-check-square-o {
                color: #7AA3CC;
                display: inline;
            }
        }
    }

    &:hover input[type="checkbox"]~i.fa {
        color: #7AA3CC;
    }
}

div[data-toggle="buttons"] label {

    &.active,
    &:hover {
        color: #7AA3CC;
    }

    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 2em;
    text-align: left;
    white-space: nowrap;
    vertical-align: top;
    cursor: pointer;
    background-color: none;
    border: 0px solid #c8c8c8;
    border-radius: 3px;
    color: #c8c8c8;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;

    &:active,
    &.active {
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}

.print-only {
    display: none;
}

@media print {
    .no-print {
        display: none !important;

        * {
            display: none !important;
        }
    }

    .print-only {
        display: block;
    }
}

@media (min-width: 767px) {
    .text-center-Mobile-left {
        text-align: center;
    }
}

@media (max-width: 767px) {
    .text-center-Mobile-left {
        text-align: left;
    }
}

table.bt {
    tfoot {
        th:first-of-type {

            &:before,
            .bt-content {
                padding-top: 5px;
            }
        }

        td:first-of-type {

            &:before,
            .bt-content {
                padding-top: 5px;
            }
        }
    }

    tbody td:first-of-type {

        &:before,
        .bt-content {
            padding-top: 5px;
        }
    }

    tfoot {
        th:last-of-type {

            &:before,
            .bt-content {
                padding-bottom: 5px;
            }
        }

        td:last-of-type {

            &:before,
            .bt-content {
                padding-bottom: 5px;
            }
        }
    }

    tbody td:last-of-type {

        &:before,
        .bt-content {
            padding-bottom: 5px;
        }
    }
}

//
.quadrado {
    animation: crescendo 0.2s alternate infinite ease-in;
    animation-iteration-count: 2;
}

.quadrado:hover {
    animation: crescendo 0.5s alternate infinite ease-in;
}

@keyframes crescendo {
    0% {
        transform: scale(1.0);
    }

    100% {
        transform: scale(1.1);
    }
}

//BS4過渡期輔助
.form-group {
    display: flex;
    flex-wrap: wrap;
}

.text-red {
    color: red !important;
}

.text-white {
    color: #fff !important;
}

//未來優化項目----PC右側內容
.shortcut-links {
    display: inline;

    @include media-breakpoint-up(md) {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }

    &-item {
        flex: 0 0 auto;
        margin: 0 .3rem;
        font-size: 1rem;
        display: inline-block;

        @include media-breakpoint-up(md) {
            flex: 0 0 50px;
            display: block;
            margin: .3rem;
            font-size: 0;
            height: 50px;
            max-height: 50px;
            width: 50px;
            max-width: 50px;
            background-color: #ededed;
            border-radius: 3rem;
            box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.18);
        }

        @include media-breakpoint-up(lg) {
            margin: 0 0.3rem;
        }

        img {
            display: none;
            width: 100%;
            height: 100%;

            @include media-breakpoint-up(md) {
                display: block;
            }
        }
    }
}

.shortcut-qrcode {
    display: inline-block;
    margin: 0 .3rem;
    padding: 0;
    font-size: 1rem;

    @include media-breakpoint-up(md) {
        margin: 0;
        padding: 1rem 1rem 0 1rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #000;
    }

    img {
        display: none;
        margin: 0.3rem;
        height: auto;
        width: 100%;

        @include media-breakpoint-up(md) {
            display: block;
            max-width: 100%;
        }

        @include media-breakpoint-up(lg) {
            max-width: 100px;
        }
    }
}

.footer {
    text-align: center;
    padding: 0.2rem;
    background-color: #744624;
    color: #fff;
    border-top: 2px solid #c77966;
    border-bottom: 2px solid #c77966;
    position: relative;
    width: 80%;
    max-width: 905px;
    margin: 1rem auto;

    &::before {
        content: "";
        width: 4.5em;
        height: 2.35em;
        position: absolute;
        top: -0.15em;
        left: -4.5em;
        background-image: url(../images/site-footer-bg.png);
        background-repeat: no-repeat;
        background-position: left top;
        background-size: auto 100%;
    }

    &::after {
        content: "";
        width: 4.5em;
        height: 2.35em;
        position: absolute;
        top: -0.15em;
        right: -4.5em;
        background-image: url(../images/site-footer-bg.png);
        background-repeat: no-repeat;
        background-position: right top;
        background-size: auto 100%;
    }
}

//角色娃娃
.user-players {
    display: inline-block;
    width: 100%;
    max-width: 12rem;
    height: auto;
    vertical-align: text-bottom;
    pointer-events: visible;
}

//閱讀認證
.read-certification {
    max-width: 10rem;

    @include media-breakpoint-up(lg) {
        max-width: 100%;
    }

    &-title {
        display: inline-block;
        background-image: url(../images/read-certification-title.png);
        background-repeat: no-repeat;
        background-size: 100% auto;
        font-size: 0;
        height: 40px;
        max-width: 159px;
        width: 100%;
    }

    &-lv {
        display: inline-block;
        margin: 0.15rem;
        width: 1.5rem;
        height: 2rem;
        font-size: 0;
        background-image: url(../images/read-certification-card-bg.png);
        background-repeat: no-repeat;
        background-size: 100% auto;

        @include media-breakpoint-up(lg) {
            width: 32px;
            height: 42px;
        }

        span {
            display: inline-block;
            padding-top: 0.1rem;
            margin-top: 0.1rem;
            min-width: 1.4rem;
            font-family: -webkit-body;
            font-size: 1.2rem;
            line-height: 1;
            text-align: center;
            color: #fff;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 51%, rgba(229, 229, 229, 0) 100%),
                radial-gradient(ellipse at center, #948527 35% 0%, #3b3200 100%);
            border-radius: 1rem;
            transform: scale(0.6);

            @include media-breakpoint-up(lg) {
                padding-top: 4px;
                margin-top: 6px;
                min-width: 20px;
                min-height: 20px;
                font-family: -webkit-body;
                transform: scale(1);
            }
        }

        &1 {
            @extend .read-certification-lv;
        }

        &2 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(240deg);
            }
        }

        &3 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(330deg);
            }
        }

        &4 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(32deg);
            }
        }

        &5 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(137deg);
            }
        }

        &6 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(28deg) brightness(0.8) contrast(1.7) saturate(0.2);
            }
        }

        &7 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(212deg) brightness(0.8) contrast(1.7) saturate(0.5);
            }
        }

        &8 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(240deg) brightness(0.9) contrast(1.7) saturate(0.5);
            }
        }

        &9 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(239deg) brightness(0.8) contrast(1.7) saturate(0.5);
            }
        }

        &10 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(274deg) brightness(0.8) contrast(1.7) saturate(0.7);
            }
        }
    }
}

//閱讀護照榮譽
.read-books {
    &-title {
        display: inline-block;
        background-image: url(../images/read-books-title.png);
        background-repeat: no-repeat;
        background-size: 100% auto;
        font-size: 0;
        height: 40px;
        max-width: 159px;
        width: 100%;
    }

    &-lv {
        display: inline-block;
        position: relative;
        margin: 0.15rem;
        width: 1.5rem;
        height: 2rem;
        font-size: 0;
        background-image: url(../images/read-books-bg.png);
        background-repeat: no-repeat;
        background-size: 100% auto;

        &::before {
            content: "";
            display: inline-block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-image: url(../images/read-books-color.png);
            background-repeat: no-repeat;
            background-size: auto 100%;
            background-position: center center;
        }

        &::after {
            content: "";
            display: inline-block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-image: url(../images/read-books-bgup.png);
            background-repeat: no-repeat;
            background-size: auto 100%;
            background-position: center center;
        }

        @include media-breakpoint-up(lg) {
            width: 36px;
            height: 47px;
        }

        span {
            font-size: 1rem;
            font-weight: 600;
            font-family: -webkit-body;
            display: block;
            padding: 0;
            position: relative;
            z-index: 1;
            transform: scale(0.4);

            @include media-breakpoint-up(lg) {
                margin-top: 5px;
                padding: 0.425rem;
                transform: scale(0.7);
            }
        }

        &1 {
            @extend .read-books-lv;
        }

        &2 {
            @extend .read-books-lv;

            &::before {
                filter: hue-rotate(310deg) brightness(0.6);
            }
        }

        &3 {
            @extend .read-books-lv;

            &::before {
                filter: hue-rotate(189deg) brightness(0.7);
            }
        }

        &4 {
            @extend .read-books-lv;

            &::before {
                filter: hue-rotate(157deg) brightness(0.7);
            }
        }

        &5 {
            @extend .read-books-lv;

            &::before {
                filter: hue-rotate(314deg) brightness(0.5) saturate(2);
            }
        }

        &6 {
            @extend .read-books-lv;

            &::before {
                filter: hue-rotate(281deg) brightness(0.5) saturate(2);
            }
        }
    }
}

//運動撲滿
.sport-banker {
    display: inline-block;
    max-width: 50%;

    @include media-breakpoint-up(lg) {
        max-width: 100%;
    }

    img {
        max-width: 50px;
    }

    &-txt {
        color: #005ea3;
    }

    &-info {
        color: #a96b1a;
    }
}

//登入頁 選擇學校
.school-menu {
    position: relative;
    padding: .5em;
    margin-bottom: 1em;
    // background-image: url(../images/site-body-bg.png);
    // background-color: #fff;
    border-radius: 0;
    overflow: initial;
    font-size: 0.7rem;
    @include media-breakpoint-up(md) {
        position: static;
        background-image: none;
        background-color: #ffc100;
        box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.4);
        border-radius: 1em;
        max-width: 176px;
        margin: 0 auto;
    }

    .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
        width: 100%;

        .btn-default {
            margin: 0;
        }
    }

    &-none {
        display: none;

        @include media-breakpoint-up(md) {
            display: block;
        }
    }

    &-title {
        margin: -0.5em -0.5em 0.4em -0.5em;
        padding: 0.3em 0;
        display: block;
        border-radius: 0.3em 0.3em 0 0;
        font-size: 2.125em;
        font-weight: 600;
        text-align: center;
        color: #004aa2;

        @include media-breakpoint-up(md) {
            font-size: 1.725em;
            background-image: linear-gradient(45deg, rgba(255, 233, 136, 1) 0%, rgba(255, 220, 50, 1) 100%);
            color: #603813;
        }
    }

    &-list {
        padding: 0;
        margin: 1em 0;
        list-style-type: none;
        font-size: 1.326em;

        strong {
            padding: 0.3em;
            display: block;
            text-align: left;
            color: #3a87ad;
            background-color: #d9edf7;
            border-color: #bce8f1;
            border-radius: .3em .3em 0 0;

            @include media-breakpoint-up(md) {
                text-align: center;
                color: #000;
                background-color: transparent;
                border-color: transparent;
            }
        }

        >li {
            display: block;
            padding: 0;
            margin: 0;
            list-style-type: none;
            background-color: #ffffff;
            border: 1px solid #bce8f1;
            border-radius: 0.3em;

            @include media-breakpoint-up(md) {
                background-color: transparent;
                border-color: transparent;
            }
        }
    }

    &-child {
        padding: 1.5em;
        margin: 0;
        list-style-type: none;
        display: flex;
        flex-wrap: wrap;
        font-size: 0.725em;

        @include media-breakpoint-up(md) {
            padding: 0;
        }

        li {
            flex: 0 0 50%;
            display: inline-block;
            padding: 0;
            margin: .3em 0;

            a {
                display: block;
                font-size: 1.526em;
                font-weight: bold;
                text-align: center;
                padding: 0.5em 0.3em;
                margin: 0.1em 0.5em;
                letter-spacing: 2px;
                color: #000;
                background-color: #FFF;
                border: 1.5px solid #c3e4f6;
                border-radius: 0.3em;

                @include media-breakpoint-up(md) {
                    padding: 0;
                    margin: 0;
                    font-size: 1.4em;
                    font-weight: normal;
                    letter-spacing: 0;
                    background-color: transparent;
                    border-color: transparent;
                }
            }
        }
    }

}

//區塊文字標題
$ribbonColors:(default:(class:"default",
        bgcolor: #ebf1ff,
        txtcolor:#0036bf),
    ecool:(class:"ecool",
        bgcolor: #ccffff,
        txtcolor:#0036bf),
    news:(class:"news",
        bgcolor: #eced33,
        txtcolor:#0036bf),
    task:(class:"task",
        bgcolor: #adfbf2,
        txtcolor:#0036bf),
    readLeaderboard:(class:"readLeaderboard",
        bgcolor: #e2eaa2,
        txtcolor:#0036bf),
    read-books:(class:"read-books",
        bgcolor: #fceec1,
        txtcolor:#0036bf),
    carousel:(class:"carousel",
        bgcolor: #e2e1fe,
        txtcolor:#0036bf),
    articleLeaderboard:(class:"articleLeaderboard",
        bgcolor: #e8d1e6,
        txtcolor:#0036bf),
    hot-prize:(class:"hot-prize",
        bgcolor: #f3c8b0,
        txtcolor:#0036bf),
    coinLeaderboard:(class:"coinLeaderboard",
        bgcolor: #fde680,
        txtcolor:#0036bf),
    online-gallery-green:(class:"online-gallery-green",
        bgcolor: #dff0d8,
        txtcolor:#468847),
    online-gallery-pink:(class:"online-gallery-pink",
        bgcolor: #dba3ac,
        txtcolor:#ffe650),
);
@import "../styles/newsite/typography";

.ribbon-online-gallery-pink {
    text-shadow: 1px 1px 1px #000, 0px 1px 1px #000;

    &-bg {
        background-image: url(../img/GalleryBook_Asset-03.gif);
        border: 3px solid #dba3ac;
    }
}

.ribbon-content-edit {
    margin: 0;

    @include media-breakpoint-up(md) {
        margin: 0 3.5rem;
    }
}