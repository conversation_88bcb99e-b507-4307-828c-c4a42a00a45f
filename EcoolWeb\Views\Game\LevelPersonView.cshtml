﻿@model GameListLevelPersonDetailsViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>
<br />
<div class="form-inline">
    <div class="col-xs-12 text-right">
        <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
        <button class="btn btn-sm btn-sys" onclick='fn_save_Excel()'>另存新檔(Excel)</button>
    </div>
</div>
<br />

@using (Html.BeginForm("LevelPersonView", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.HiddenFor(m => m.GAME_NO)
    @Html.HiddenFor(m => m.Level_Count)
    @Html.HiddenFor(m => m.LEVEL_NO)
    @Html.HiddenFor(m => m.OrdercColumn)

    <div class="panel panel-default" id="tbData">
        <div class="panel-body">
            <div class="form-group">
                <div class="table-responsive">
                    <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                        <thead>
                            <tr>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SHORT_NAME')">
                                        @Html.DisplayNameFor(model => model.Person.First().SHORT_NAME)
                                    </samp>
                                </th>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('NAME')">
                                        @Html.DisplayNameFor(model => model.Person.First().NAME)
                                    </samp>
                                </th>

                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('PHONE')">
                                        @Html.DisplayNameFor(model => model.Person.First().PHONE)
                                    </samp>
                                </th>

                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_USER_TYPE')">
                                        @Html.DisplayNameFor(model => model.Person.First().GAME_USER_TYPE)
                                    </samp>
                                </th>

                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('USER_NO')">
                                        @Html.DisplayNameFor(model => model.Person.First().USER_NO)
                                    </samp>
                                </th>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CLASS_NO')">
                                        @Html.DisplayNameFor(model => model.Person.First().CLASS_NO)
                                    </samp>
                                </th>
                                <th>
                                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SEAT_NO')">
                                        @Html.DisplayNameFor(model => model.Person.First().SEAT_NO)
                                    </samp>
                                </th>
                                @if (!string.IsNullOrEmpty(Model.LEVEL_NO))
                                {
                                    <th>
                                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('Number_Count')">
                                            @Html.DisplayNameFor(model => model.Person.First().Number_Count)
                                        </samp>
                                    </th>
                                }
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.Person.Count() > 0)
                            {
                                foreach (var item in Model.Person)
                                {
                                    <tr>
                                        <td>
                                            @if (item.GAME_USER_TYPE == UserType.Student)
                                            {
                                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                            }
                                            else
                                            {
                                                <text>卡片</text>
                                            }
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.NAME)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.PHONE)
                                        </td>
                                        <td>
                                            @if (string.IsNullOrWhiteSpace(item.GAME_USER_TYPE_DESC))
                                            {
                                                @UserType.GetDesc(item.GAME_USER_TYPE)
                                            }
                                            else
                                            {
                                                @Html.DisplayFor(modelItem => item.GAME_USER_TYPE_DESC)
                                            }
                                        </td>
                                        <td>
                                            @if (item.GAME_USER_TYPE == UserType.Student)
                                            {
                                                @Html.DisplayFor(modelItem => item.USER_NO)
                                            }
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                                        </td>
                                        @if (!string.IsNullOrEmpty(Model.LEVEL_NO))
                                        {
                                            <td>
                                                @Html.DisplayFor(modelItem => item.Number_Count)
                                            </td>
                                        }
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    if (Model.Person.Count() == 0)
    {
        <div class="text-center">
            <h2>無任何資料</h2>
        </div>
    }
}

<script language="JavaScript">
            var targetFormID = '#form1'

            function FunSort(Val) {
              $('@Html.IdFor(m=>m.OrdercColumn)').val(Val)
              $(targetFormID).submit();
            }

            function PrintBooK() {
                $('#tbData').printThis();
            }

            function fn_save_Excel() {
                var blob = new Blob([document.getElementById('tbData').innerHTML], {
                    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                });
                var strFile = "Report.xls";
                saveAs(blob, strFile);
                return false;
            }
</script>