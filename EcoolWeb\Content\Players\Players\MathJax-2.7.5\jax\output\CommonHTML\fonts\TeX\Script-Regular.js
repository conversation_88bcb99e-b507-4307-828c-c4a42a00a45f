/*
 *  /MathJax/jax/output/CommonHTML/fonts/TeX/Script-Regular.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(b){var a="MathJax_Script";b.FONTDATA.FONTS[a]={className:b.FONTDATA.familyName(a),centerline:211,ascent:735,descent:314,skew:{65:0.389,66:0.194,67:0.278,68:0.111,69:0.139,70:0.222,71:0.25,72:0.333,73:0.333,74:0.417,75:0.361,76:0.306,77:0.444,78:0.389,79:0.167,80:0.222,81:0.278,82:0.194,83:0.333,84:0.222,85:0.25,86:0.222,87:0.25,88:0.278,89:0.194,90:0.306},32:[0,0,250,0,0],65:[717,8,803,35,1016],66:[708,28,908,31,928],67:[728,26,666,26,819],68:[708,31,774,68,855],69:[707,8,562,46,718],70:[735,36,895,39,990],71:[717,37,610,12,738],72:[717,36,969,29,1241],73:[717,17,809,59,946],74:[717,314,1052,92,1133],75:[717,37,914,29,1204],76:[717,17,874,14,1035],77:[721,50,1080,30,1216],78:[726,36,902,29,1208],79:[707,8,738,96,805],80:[716,37,1013,90,1031],81:[717,17,883,54,885],82:[717,17,850,-2,887],83:[708,36,868,29,1016],84:[735,37,747,92,996],85:[717,17,800,55,960],86:[717,17,622,56,850],87:[717,17,805,46,1026],88:[717,17,944,103,1131],89:[716,17,710,57,959],90:[717,16,821,83,1032],160:[0,0,250,0,0]};b.fontLoaded("TeX/"+a.substr(8))})(MathJax.OutputJax.CommonHTML);
