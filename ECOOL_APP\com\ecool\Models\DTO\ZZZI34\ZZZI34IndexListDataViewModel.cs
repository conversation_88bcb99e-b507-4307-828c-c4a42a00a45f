﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
     public class ZZZI34IndexListDataViewModel
    {
        /// <summary>
        ///藝廊 NO.
        /// </summary>
        [DisplayName("藝廊 NO.")]
        public string ART_GALLERY_NO { get; set; }

        public string SCHOOL_NO { get; set; }
        
        public string QRCODEGARY { get; set; }
        public string QRCODEGARYPHOTO { get; set; }
        /// <summary>
        ///藝廊類別
        /// </summary>
        [DisplayName("藝廊類別")]
        public string ART_GALLERY_TYPE { get; set; }

        /// <summary>
        ///藝廊名稱
        /// </summary>
        [DisplayName("藝廊名稱")]
        public string ART_SUBJECT { get; set; }

        /// <summary>
        ///作品類別
        /// </summary>
        [DisplayName("作品類別")]
        public string WORK_TYPE { get; set; }
        [DisplayName("藝廊描述")]
        public string ART_DESC { get; set; }


        /// <summary>
        ///封面
        /// </summary>
        [DisplayName("封面")]
        public string COVER_FILE { get; set; }

        public string COVER_FILE_PATH { get; set; }


        /// <summary>
        ///狀況
        /// </summary>
        [DisplayName("狀況")]
        public string STATUS { get; set; }


        /// <summary>
        ///藝廊建立人帳號
        /// </summary>
        [DisplayName("藝廊建立人帳號")]
        public string USER_NO { get; set; }


        /// <summary>
        ///藝廊建立人簡稱
        /// </summary>
        [DisplayName("藝廊建立人簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("上架日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CRE_DATE { get; set; }

        public string CRE_PERSON { get; set; }

        public string CLASS_NO { get; set; }


        /// <summary>
        ///批閱教師/批閱者
        /// </summary>
        [DisplayName("批閱教師/批閱者")]
        public string VERIFIER { get; set; }

        /// <summary>
        /// 作品數量
        /// </summary>
        public int WorkCount { get; set; }

    }
}