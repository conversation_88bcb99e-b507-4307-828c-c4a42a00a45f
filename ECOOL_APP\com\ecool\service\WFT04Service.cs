﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace com.ecool.service
{
    public class WFT04Service : ServiceBase
    {
        /// <summary>
        /// Modify By Orin
        /// Modify Date :2015/05/19
        /// Description : 待辦事項
        /// </summary>
        /// <param name="collection">畫面上的欄位</param>
        /// <returns></returns>
        public static List<uWFT04> USP_WFT04_QUERY(string User_Key)
        {
            List<uWFT04> list_data = new List<uWFT04>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT KEY_NO,TB_NAME, ");
                sb.Append("        BRE_NO,PAGE_ACTION,NAME, ");
                sb.Append("        LINK_ADDR,USER_KEY,SCHOOL_NO, ");
                sb.Append("        USER_NO,COUNT(*) as SumAccount ");
                sb.Append(" FROM WFT04 ");
                if (User_Key != string.Empty)
                {
                    sb.Append(" WHERE USER_KEY= '" + User_Key +"'");
                }
                sb.Append(" GROUP BY KEY_NO,TB_NAME,BRE_NO, ");
                sb.Append(" PAGE_ACTION,NAME,LINK_ADDR,  ");
                sb.Append(" USER_KEY,SCHOOL_NO,USER_NO ");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uWFT04()
                    {
                        KEY_NO = dr["KEY_NO"].ToString(),
                        TB_NAME = dr["TB_NAME"].ToString(),
                        BRE_NO = dr["BRE_NO"].ToString(),
                        PAGE_ACTION = dr["PAGE_ACTION"].ToString(),
                        NAME = dr["NAME"].ToString(),
                        LINK_ADDR = dr["LINK_ADDR"].ToString(),
                        USER_KEY = dr["USER_KEY"].ToString(),
                        SCHOOL_NO = dr["SCHOOL_NO"].ToString(),
                        USER_NO = dr["USER_NO"].ToString(),
                        SumAccount = dr["SumAccount"].ToString()
                    });
                }
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;

        }

        /// <summary>
        /// Modify By Orin
        /// Modify Date :2015/05/19
        /// Description : 待辦事項
        /// </summary>
        /// <param name="collection">畫面上的欄位</param>
        /// <returns></returns>
        public static List<uWFT04> USP_WFT04_QUERY(FormCollection collection)
        {
            List<uWFT04> list_data = new List<uWFT04>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT KEY_NO,TB_NAME, ");
                sb.Append("        BRE_NO,PAGE_ACTION,NAME, ");
                sb.Append("        LINK_ADDR,RECEIPT_ID,USER_KEY, ");
                sb.Append("        SCHOOL_NO,USER_NO ");
                sb.Append(" FROM WFT04 ");
                sb.Append(" WHERE 1 = 1 ");

                if (collection["KEY_NO"] != null)
                {
                    sb.Append(" AND KEY_NO = " + collection["KEY_NO"]);
                }

                if (collection["TB_NAME"] != null)
                {
                    sb.Append(" AND TB_NAME = " + collection["TB_NAME"]);
                }

                if (collection["BRE_NO"] != null)
                {
                    sb.Append(" AND BRE_NO = " + collection["BRE_NO"]);
                }

                if (collection["PAGE_ACTION"] != null)
                {
                    sb.Append(" AND PAGE_ACTION = " + collection["PAGE_ACTION"]);
                }

                if (collection["NAME"] != null)
                {
                    sb.Append(" AND NAME = " + collection["NAME"]);
                }

                if (collection["LINK_ADDR"] != null)
                {
                    sb.Append(" AND LINK_ADDR = " + collection["LINK_ADDR"]);
                }

                if (collection["RECEIPT_ID"] != null)
                {
                    sb.Append(" AND RECEIPT_ID = " + collection["RECEIPT_ID"]);
                }

                if (collection["USER_KEY"] != null)
                {
                    sb.Append(" AND USER_KEY = " + collection["USER_KEY"]);
                }

                if (collection["SCHOOL_NO"] != null)
                {
                    sb.Append(" AND SCHOOL_NO = " + collection["SCHOOL_NO"]);
                }

                if (collection["USER_NO"] != null)
                {
                    sb.Append(" AND USER_NO = " + collection["USER_NO"]);
                }

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uWFT04()
                    {
                        KEY_NO = dr["KEY_NO"].ToString(),
                        TB_NAME = dr["TB_NAME"].ToString(),
                        BRE_NO = dr["BRE_NO"].ToString(),
                        PAGE_ACTION = dr["PAGE_ACTION"].ToString(),
                        NAME = dr["NAME"].ToString(),
                        LINK_ADDR = dr["LINK_ADDR"].ToString(),
                        RECEIPT_ID = dr["RECEIPT_ID"].ToString(),
                        USER_KEY = dr["USER_KEY"].ToString(),
                        SCHOOL_NO = dr["SCHOOL_NO"].ToString(),
                        USER_NO = dr["USER_NO"].ToString()
                    });
                }
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;

        }
    }
}
