﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP.EF;
using EcoolWeb.ViewModels;
using EcoolWeb.Models;
using ECOOL_APP;
using System.Data.Entity;
using com.ecool.service;
using EcoolWeb.CustomAttribute;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using EcoolWeb.Util;
using System.Linq.Expressions;
using System.Data;
using ECOOL_APP.com.ecool.util;
using System.IO;
using NPOI.SS.UserModel;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWA004Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        // GET: AWA004
        public ActionResult Query(AWA004QueryViewModel model)
        {
            if (model == null) model = new AWA004QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.USER_TYPE == UserType.Student)
                {
                    ViewBag.MyUSER_NO = user.USER_NO;
                }
                else if (user.USER_TYPE == UserType.Parents)
                {
                    ViewBag.MyUSER_NO = HRMT06.GetStringMyPanyStudent(user);
                }
            }

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);
            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);

            IQueryable<VAWA004> VAWA004List = db.VAWA004;

            //人員
            if (SchoolNO != "ALL")
            {
                VAWA004List = VAWA004List.Where(a => a.SCHOOL_NO == SchoolNO);
            }

            //狀況
            if (string.IsNullOrWhiteSpace(model.whereSTATUS) == false)
            {
                byte TRANS_STATUS = Convert.ToByte(model.whereSTATUS);

                VAWA004List = VAWA004List.Where(a => a.TRANS_STATUS == TRANS_STATUS);
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                var arrUSER_NO = model.whereUserNo.Split(',');
                VAWA004List = VAWA004List.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);
            }

            //獎品
            if (string.IsNullOrWhiteSpace(model.whereAWARD_SCHOOL_NO) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.AWARD_SCHOOL_NO == model.whereAWARD_SCHOOL_NO);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                int AWARD_NO;

                if (Int32.TryParse(model.whereKeyword, out AWARD_NO))
                {
                    VAWA004List = VAWA004List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                               || a.NAME.Contains(model.whereKeyword.Trim())
                                               || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                               || a.AWARD_NO == AWARD_NO
                                               );
                }
                else
                {
                    VAWA004List = VAWA004List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                                    || a.NAME.Contains(model.whereKeyword.Trim())
                                                    || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                               );
                }
            }

            if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.NAME.Contains(model.whereSNAME));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.CLASS_NO == model.whereCLASS_NO);
            }
            if (string.IsNullOrWhiteSpace(model.whereAWARD_NAME) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.AWARD_NAME == model.whereAWARD_NAME);
            }
            if (string.IsNullOrWhiteSpace(model.whereAWARD_NO) == false)
            {
                int an = 0;
                int.TryParse(model.whereAWARD_NO, out an);
                VAWA004List = VAWA004List.Where(a => a.AWARD_NO == an);
            }

            model.PieChart = GetPieChart(VAWA004List);

            //  -- 排序開始 --
            Func<VAWA004, object> sortExpression;
            switch (model.OrderColumn)
            {
                case "SHORT_NAME":
                    sortExpression = (q => q.SHORT_NAME);
                    break;

                case "SYEAR":
                    sortExpression = (q => q.SYEAR);
                    break;

                case "CLASS_NO":
                    sortExpression = (q => q.CLASS_NO);
                    break;

                case "SEAT_NO":
                    sortExpression = (q => q.SEAT_NO);
                    break;

                case "COST_CASH":
                    sortExpression = (q => q.COST_CASH);
                    break;

                case "TRANS_DATE":
                    sortExpression = (q => q.TRANS_DATE);
                    break;

                default:
                    sortExpression = (q => q.TRANS_NO);
                    break;
            }

            if (model.SortBy == "ASC")
            {
                VAWA004List = VAWA004List.OrderBy(sortExpression).AsQueryable();
            }
            else
            {
                VAWA004List = VAWA004List.OrderByDescending(sortExpression).AsQueryable();
            }

            //  -- 排序結束 --

            model.VAWA004List = VAWA004List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
            if (!string.IsNullOrEmpty(model.whereAWARD_NO)) {
                int whereAWARD_NO = Int32.Parse(model.whereAWARD_NO);
                string AWARD_TYPE = "";
                AWARD_TYPE = db.AWAT02.Where(x => x.AWARD_NO == whereAWARD_NO).Select(x => x.AWARD_TYPE).FirstOrDefault();
                model.AWARD_TYPE = AWARD_TYPE;

            }
            
            return View(model);
        }

        #region 兌獎情形 類別 圓餅圖

        /// <summary>
        /// 兌獎情形 類別 圓餅圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetPieChart(IQueryable<VAWA004> Data)
        {
            var pl = from r in Data
                     where r.TRANS_STATUS != VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del
                     group r by r.AWARD_TYPE into grp
                     select new { key = grp.Key, cnt = grp.Count() };

            var returnObject = new List<object>();

            foreach (var item in pl)
            {
                returnObject.Add(new object[] { AWAT02.GetNameforAWARD_TYPE(item.key), item.cnt });
            }

            Highcharts TempPieChart = new Highcharts("TempPieChart")
               .InitChart(new Chart
               {
                   DefaultSeriesType = ChartTypes.Pie,
                   BackgroundColor = null,
                   BorderWidth = null,
                   Shadow = false,
               })
              .SetPlotOptions(new PlotOptions
              {
                  Pie = new PlotOptionsPie
                  {
                      AllowPointSelect = true,
                      Cursor = Cursors.Pointer,
                      //ShowInLegend = true,
                      DataLabels = new PlotOptionsPieDataLabels
                      {
                          Enabled = true,
                          Format = "<b>{point.name}</b>: {point.percentage:.1f} %",
                          Style = "color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'"
                      }
                  }
              })
             .SetTooltip(new Tooltip { PointFormat = "{series.name}: <b>{point.percentage:.1f}%</b>" })
             .SetTitle(new Title { Text = "兌獎情形類別圓餅圖(不含取消)" })
              .SetSeries(new Series
              {
                  Data = new Data(returnObject.ToArray())
              });

            chartsHelper.SetCopyright(TempPieChart);

            return TempPieChart;
        }

        #endregion 兌獎情形 類別 圓餅圖

        public ActionResult ModifyQuery(AWA004_006_ModifyQueryViewModel model)
        {
            ModelState.Clear();
            if (model == null) model = new AWA004_006_ModifyQueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);
            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);

            #region action method stuAction

            Action stuAction = () =>
            {
                IQueryable<VAWA004> VAWA004List = db.VAWA004;

                //人員
                if (SchoolNO != "ALL")
                {
                    VAWA004List = VAWA004List.Where(a => a.SCHOOL_NO == SchoolNO);
                }
                if (string.IsNullOrWhiteSpace(model.whereSTATUS))
                {
                    model.whereSTATUS = VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString();
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_NO) == false)
                {
                    int AWARD_NO;
                    if (Int32.TryParse(model.whereAWARD_NO, out AWARD_NO))
                    {
                        VAWA004List = VAWA004List.Where(a => a.AWARD_NO == AWARD_NO);
                    }
                    else
                    {
                        model.whereAWARD_NO = model.whereAWARD_NO + "-此獎品ID無效";
                    }
                }
                if (string.IsNullOrWhiteSpace(model.whereSTATUS) == false)
                {
                    int TRANS_STATUS = Convert.ToInt32(model.whereSTATUS);

                    VAWA004List = VAWA004List.Where(a => a.TRANS_STATUS == TRANS_STATUS);
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_SCHOOL_NO) == false)
                {
                    VAWA004List = VAWA004List.Where(a => a.AWARD_SCHOOL_NO == model.whereAWARD_SCHOOL_NO);
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_TYPE) == false)
                {
                    var arrAWARD_TYPE = model.whereAWARD_TYPE.Split(',');
                    VAWA004List = VAWA004List.Where(a => arrAWARD_TYPE.Contains(a.AWARD_TYPE.ToString()) && a.AWARD_TYPE != null);
                }
                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    VAWA004List = VAWA004List.Where(a =>
                        a.USER_NO.Contains(model.whereKeyword.Trim())
                     || a.NAME.Contains(model.whereKeyword.Trim())
                     || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                    );
                }
                if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
                {
                    VAWA004List = VAWA004List.Where(a => a.NAME.Contains(model.whereSNAME.Trim()));
                }
                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    VAWA004List = VAWA004List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                }
                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    VAWA004List = VAWA004List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_NAME) == false)
                {
                    VAWA004List = VAWA004List.Where(a => a.AWARD_NAME == model.whereAWARD_NAME.Trim());
                }

                //  -- 排序開始 --
                Func<VAWA004, object> sortExpression;
                switch (model.OrderColumn)
                {
                    case "SHORT_NAME":
                        sortExpression = (q => q.SHORT_NAME);
                        break;

                    case "SYEAR":
                        sortExpression = (q => q.SYEAR);
                        break;

                    case "CLASS_NO":
                        sortExpression = (q => q.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        sortExpression = (q => q.SEAT_NO);
                        break;

                    case "COST_CASH":
                        sortExpression = (q => q.COST_CASH);
                        break;

                    case "TRANS_DATE":
                        sortExpression = (q => q.TRANS_DATE);
                        break;

                    case "GOT_DATE":
                        sortExpression = (q => q.GOT_DATE);
                        break;

                    default:
                        sortExpression = (q => q.TRANS_NO);
                        break;
                }

                if (model.SortBy == "ASC")
                {
                    VAWA004List = VAWA004List.OrderBy(sortExpression).AsQueryable();
                }
                else
                {
                    VAWA004List = VAWA004List.OrderByDescending(sortExpression).AsQueryable();
                }

                //  -- 排序結束 --
                model.VAWA004List = VAWA004List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 300);

                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

                ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
            };

            #endregion action method stuAction

            #region action methd teaAction

            Action teaAction = () =>
            {
                IQueryable<VAWA005> VAWA005List = db.VAWA005;
                //人員
                if (SchoolNO != "ALL")
                {
                    VAWA005List = VAWA005List.Where(a => a.SCHOOL_NO == SchoolNO);
                }
                if (string.IsNullOrWhiteSpace(model.whereSTATUS))
                {
                    model.whereSTATUS = VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString();
                }
                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.NAME.Contains(model.whereKeyword.Trim()) || a.AWARD_NAME.Contains(model.whereKeyword.Trim()));
                }
                if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.NAME.Contains(model.whereSNAME.Trim()));
                }
                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_NAME) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.AWARD_NAME == model.whereAWARD_NAME.Trim());
                }
                if (string.IsNullOrWhiteSpace(model.whereSTATUS) == false)
                {
                    int TRANS_STATUS = Convert.ToInt32(model.whereSTATUS);

                    VAWA005List = VAWA005List.Where(a => a.TRANS_STATUS == TRANS_STATUS);
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_SCHOOL_NO) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.AWARD_SCHOOL_NO == model.whereAWARD_SCHOOL_NO);
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_TYPE) == false)
                {
                    var arrAWARD_TYPE = model.whereAWARD_TYPE.Split(',');
                    VAWA005List = VAWA005List.Where(a => arrAWARD_TYPE.Contains(a.AWARD_TYPE.ToString()) && a.AWARD_TYPE != null);
                }
                //  -- 排序開始 --
                Func<VAWA005, object> sortExpression;
                switch (model.OrderColumn)
                {
                    case "SHORT_NAME":
                        sortExpression = (q => q.SHORT_NAME);
                        break;

                    case "SYEAR":
                        sortExpression = (q => q.SYEAR);
                        break;

                    case "CLASS_NO":
                        sortExpression = (q => q.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        sortExpression = (q => q.SEAT_NO);
                        break;

                    case "COST_CASH":
                        sortExpression = (q => q.COST_CASH);
                        break;

                    case "TRANS_DATE":
                        sortExpression = (q => q.TRANS_DATE);
                        break;

                    case "GOT_DATE":
                        sortExpression = (q => q.GOT_DATE);
                        break;

                    default:
                        sortExpression = (q => q.TRANS_NO);
                        break;
                }

                if (model.SortBy == "ASC")
                {
                    VAWA005List = VAWA005List.OrderBy(sortExpression).AsQueryable();
                }
                else
                {
                    VAWA005List = VAWA005List.OrderByDescending(sortExpression).AsQueryable();
                }
                //  -- 排序結束 --
                model.VAWA005List = VAWA005List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 300);
            };

            #endregion action methd teaAction

            if (model.RoleName == "Student" || string.IsNullOrEmpty(model.RoleName))
            {
                ViewBag.Title = "獎品頒發-獎品未領取一覽表-學生";
                stuAction();
                model.RoleName = "Student";
            }
            else if (model.RoleName == "Teacher")
            {
                ViewBag.Title = "獎品頒發-獎品未領取一覽表-老師";
                teaAction();
            }
            ViewBag.ImgUrl = Url.Content(AWAI01Controller.GetImagePathUrl(model.RoleName));

            return View("../AWA004/ModifyQuery", model);
        }

        public ActionResult _ModifyView(AWA004PrintViewModel Item)
        {
            return PartialView(Item);
        }

        public ActionResult ExportExcel(AWA004QueryViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            model.PageSize = int.MaxValue;
            model.Page = 1;
            string USER_Tyep = "";
            DataTable DataTableExcel=null;
            if (model.Chk != null)
            {
                int V004Count = 0;
                int V005Count = 0;
                V004Count = model.Chk.Where(x=>x.V004!=null).ToList().Count();
                V005Count = model.Chk.Where(x => x.V005 != null).ToList().Count();
                if (V004Count > 0)
                {

                    USER_Tyep = "S";
                }
                else
                {
                    USER_Tyep = "T";


                }
               
            }
          
            if (USER_Tyep == "S") {
                List<VAWA004> vAWA004slist = new List<VAWA004>();
                List<int> TRANS_NOlist = new List<int>();
                vAWA004slist = model.Chk.Where(x => x.V004 != null && x.CheckBoxNo == true).Select(x => x.V004).ToList();
                TRANS_NOlist = vAWA004slist.Select(x => x.TRANS_NO).ToList();
                IQueryable<VAWA004> VAWA004List = db.VAWA004;
                VAWA004List = VAWA004List.Where(x => TRANS_NOlist.Contains(x.TRANS_NO));
            //人員
            //if (model.whereSchoolNo != "ALL")
            //{
                VAWA004List = VAWA004List.Where(a => a.SCHOOL_NO == user.SCHOOL_NO);
            //}

            //狀況
            if (string.IsNullOrWhiteSpace(model.whereSTATUS) == false)
            {
                byte TRANS_STATUS = Convert.ToByte(model.whereSTATUS);

                VAWA004List = VAWA004List.Where(a => a.TRANS_STATUS == TRANS_STATUS);
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                var arrUSER_NO = model.whereUserNo.Split(',');
                VAWA004List = VAWA004List.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);
            }

            //獎品
            if (string.IsNullOrWhiteSpace(model.whereAWARD_SCHOOL_NO) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.AWARD_SCHOOL_NO == model.whereAWARD_SCHOOL_NO);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                int AWARD_NO;

                if (Int32.TryParse(model.whereKeyword, out AWARD_NO))
                {
                    VAWA004List = VAWA004List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                               || a.NAME.Contains(model.whereKeyword.Trim())
                                               || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                               || a.AWARD_NO == AWARD_NO
                                               );
                }
                else
                {
                    VAWA004List = VAWA004List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                                    || a.NAME.Contains(model.whereKeyword.Trim())
                                                    || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                               );
                }
            }

            if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.NAME.Contains(model.whereSNAME));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.CLASS_NO == model.whereCLASS_NO);
            }
            if (string.IsNullOrWhiteSpace(model.whereAWARD_NAME) == false)
            {
                VAWA004List = VAWA004List.Where(a => a.AWARD_NAME == model.whereAWARD_NAME);
            }
            if (string.IsNullOrWhiteSpace(model.whereAWARD_NO) == false)
            {
                int an = 0;
                int.TryParse(model.whereAWARD_NO, out an);
                VAWA004List = VAWA004List.Where(a => a.AWARD_NO == an);
            }
            //  -- 排序開始 --
            Func<VAWA004, object> sortExpression;
            switch (model.OrderColumn)
            {
                case "SHORT_NAME":
                    sortExpression = (q => q.SHORT_NAME);
                    break;

                case "SYEAR":
                    sortExpression = (q => q.SYEAR);
                    break;

                case "CLASS_NO":
                    sortExpression = (q => q.CLASS_NO);
                    break;

                case "SEAT_NO":
                    sortExpression = (q => q.SEAT_NO);
                    break;

                case "COST_CASH":
                    sortExpression = (q => q.COST_CASH);
                    break;

                case "TRANS_DATE":
                    sortExpression = (q => q.TRANS_DATE);
                    break;

                default:
                    sortExpression = (q => q.TRANS_NO);
                    break;
            }

            if (model.SortBy == "ASC")
            {
                VAWA004List = VAWA004List.OrderBy(sortExpression).AsQueryable();
            }
            else
            {
                VAWA004List = VAWA004List.OrderByDescending(sortExpression).AsQueryable();
            }

            //  -- 排序結束 --
            List<VAWA004> vAWA004sItem = new List<VAWA004>();
            vAWA004sItem = VAWA004List.ToList();
            if (!string.IsNullOrEmpty(model.whereAWARD_NO))
            {
                int whereAWARD_NO = Int32.Parse(model.whereAWARD_NO);
                string AWARD_TYPE = "";
                AWARD_TYPE = db.AWAT02.Where(x => x.AWARD_NO == whereAWARD_NO).Select(x => x.AWARD_TYPE).FirstOrDefault();
                model.AWARD_TYPE = AWARD_TYPE;

            }

            var DataList = model.Chk?.Where(a => a.CheckBoxNo == true).Select(a => a.V004).ToList();

              DataTableExcel = vAWA004sItem?.AsDataTable();

           
          

           
            }
           else if (USER_Tyep == "T")
            {
                IQueryable<VAWA005> VAWA005List = db.VAWA005;
                List<VAWA005> vAWA005slist = new List<VAWA005>();
                List<int> TRANS_NOlist = new List<int>();
                vAWA005slist = model.Chk.Where(x => x.V005 != null && x.CheckBoxNo == true).Select(x => x.V005).ToList();
                TRANS_NOlist = vAWA005slist.Select(x => x.TRANS_NO).ToList();
                //人員
                //if (model.whereSchoolNo != "ALL")
                //{
                VAWA005List = VAWA005List.Where(x => TRANS_NOlist.Contains(x.TRANS_NO));
                VAWA005List = VAWA005List.Where(a => a.SCHOOL_NO == user.SCHOOL_NO);
                //}

                //狀況
                if (string.IsNullOrWhiteSpace(model.whereSTATUS) == false)
                {
                    byte TRANS_STATUS = Convert.ToByte(model.whereSTATUS);

                    VAWA005List = VAWA005List.Where(a => a.TRANS_STATUS == TRANS_STATUS);
                }

                if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
                {
                    var arrUSER_NO = model.whereUserNo.Split(',');
                    VAWA005List = VAWA005List.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);
                }

                //獎品
                if (string.IsNullOrWhiteSpace(model.whereAWARD_SCHOOL_NO) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.AWARD_SCHOOL_NO == model.whereAWARD_SCHOOL_NO);
                }

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    int AWARD_NO;

                    if (Int32.TryParse(model.whereKeyword, out AWARD_NO))
                    {
                        VAWA005List = VAWA005List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                                   || a.NAME.Contains(model.whereKeyword.Trim())
                                                   || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                                   || a.AWARD_NO == AWARD_NO
                                                   );
                    }
                    else
                    {
                        VAWA005List = VAWA005List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                                                        || a.NAME.Contains(model.whereKeyword.Trim())
                                                        || a.AWARD_NAME.Contains(model.whereKeyword.Trim())
                                                   );
                    }
                }

                if (string.IsNullOrWhiteSpace(model.whereSNAME) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.NAME.Contains(model.whereSNAME));
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.CLASS_NO == model.whereCLASS_NO);
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_NAME) == false)
                {
                    VAWA005List = VAWA005List.Where(a => a.AWARD_NAME == model.whereAWARD_NAME);
                }
                if (string.IsNullOrWhiteSpace(model.whereAWARD_NO) == false)
                {
                    int an = 0;
                    int.TryParse(model.whereAWARD_NO, out an);
                    VAWA005List = VAWA005List.Where(a => a.AWARD_NO == an);
                }
                //  -- 排序開始 --
                Func<VAWA005, object> sortExpression;
                switch (model.OrderColumn)
                {
                    case "SHORT_NAME":
                        sortExpression = (q => q.SHORT_NAME);
                        break;

                    case "SYEAR":
                        sortExpression = (q => q.SYEAR);
                        break;

                    case "CLASS_NO":
                        sortExpression = (q => q.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        sortExpression = (q => q.SEAT_NO);
                        break;

                    case "COST_CASH":
                        sortExpression = (q => q.COST_CASH);
                        break;

                    case "TRANS_DATE":
                        sortExpression = (q => q.TRANS_DATE);
                        break;

                    default:
                        sortExpression = (q => q.TRANS_NO);
                        break;
                }

                if (model.SortBy == "ASC")
                {
                    VAWA005List = VAWA005List.OrderBy(sortExpression).AsQueryable();
                }
                else
                {
                    VAWA005List = VAWA005List.OrderByDescending(sortExpression).AsQueryable();
                }

                //  -- 排序結束 --
                List<VAWA005> VAWA005sItem = new List<VAWA005>();
                VAWA005sItem = VAWA005List.ToList();
                if (!string.IsNullOrEmpty(model.whereAWARD_NO))
                {
                    int whereAWARD_NO = Int32.Parse(model.whereAWARD_NO);
                    string AWARD_TYPE = "";
                    AWARD_TYPE = db.AWAT09.Where(x => x.AWARD_NO == whereAWARD_NO).Select(x => x.AWARD_TYPE).FirstOrDefault();
                    model.AWARD_TYPE = AWARD_TYPE;

                }

                var DataList = model.Chk?.Where(a => a.CheckBoxNo == true).Select(a => a.V004).ToList();

             DataTableExcel = VAWA005sItem?.AsDataTable();

            
             
             

            }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/PrizeExchangeExportExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);
            if (DataTableExcel != null)
            {


                npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "獎品兌換清單", false, 2);
            }
            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }
            strTMPFile = strTMPFile + @"\獎品兌換清單_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "獎品兌換清單.xlsx");//輸出檔案給Client端
        }

        public ActionResult PrintSetView(AWA004QueryViewModel model)
        {
            return View(model);
        }

        public ActionResult _TrPrintSetView(AWA004PrintViewModel Item)
        {
            return View(Item);
        }

        public ActionResult PrintModify(AWA004QueryViewModel model)
        {
            return View(model);
        }

        [HttpPost]
        public ActionResult Modify(AWA004_006_ModifyQueryViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (model.RoleName == "Student")
            {
                foreach (var item in model.Chk.Where(a => a.CheckBoxNo == true).ToList())
                {
                    var AW03 = db.AWAT03.Where(p => p.TRANS_NO == item.V004.TRANS_NO).FirstOrDefault();
                    if (AW03 != null)
                    {
                        AW03.TRANS_STATUS = (byte)1;
                        AW03.GOT_DATE = DateTime.Now.Date;
                        db.Entry(AW03).State = System.Data.Entity.EntityState.Modified;
                        db.SaveChanges();
                    }
                }
            }
            else if (model.RoleName == "Teacher")
            {
                foreach (var item in model.Chk.Where(a => a.CheckBoxNo == true).ToList())
                {
                    var AW03 = db.AWAT03.Where(p => p.TRANS_NO == item.V005.TRANS_NO).FirstOrDefault();
                    if (AW03 != null)
                    {
                        AW03.TRANS_STATUS = (byte)1;
                        AW03.GOT_DATE = DateTime.Now.Date;
                        db.Entry(AW03).State = System.Data.Entity.EntityState.Modified;
                        db.SaveChanges();
                    }
                }
            }

            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);

            TempData["StatusMessage"] = "異動完成";

            return RedirectToAction("ModifyQuery", "AWA004", new { model.RoleName });
        }

        [HttpPost]
        public ActionResult CancelTrans(FormCollection vawa04)
        {
            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (vawa04["hidSelectTRANS_NO"] != null)
            {
                //查詢交易紀錄
                int TranLogNO = Convert.ToInt32(vawa04["hidSelectTRANS_NO"]);

                //更新交易紀錄-->取消 (9)
                AWAT03 TranLog = db.AWAT03.Where(p => p.TRANS_NO == TranLogNO).FirstOrDefault();

                if (TranLog.TRANS_STATUS != 9)
                {
                    TranLog.TRANS_STATUS = (byte)9;
                    TranLog.CANCEL_DATE = DateTime.Now.Date;
                    db.Entry(TranLog).State = System.Data.Entity.EntityState.Modified;

                    if (vawa04["RoleName"] == "Student")
                    {
                        //調整商品庫存量
                        AWAT02 BackAward = db.AWAT02.Where(a => a.AWARD_NO == TranLog.AWARD_NO).FirstOrDefault();

                        AWAT02_HIS HIS = db.AWAT02_HIS.Create();
                        HIS.AWARD_NO = BackAward.AWARD_NO;
                        HIS.SCHOOL_NO = BackAward.SCHOOL_NO;
                        HIS.AWARD_TYPE = BackAward.AWARD_TYPE;
                        HIS.AWARD_NAME = BackAward.AWARD_NAME;
                        HIS.COST_CASH = BackAward.COST_CASH;
                        HIS.QTY_STORAGE = BackAward.QTY_STORAGE;
                        HIS.SDATETIME = BackAward.SDATETIME;
                        HIS.EDATETIME = BackAward.EDATETIME;
                        HIS.DESCRIPTION = BackAward.DESCRIPTION;
                        HIS.IMG_FILE = BackAward.IMG_FILE;
                        HIS.IMG2_FILE = BackAward.IMG2_FILE;
                        HIS.AWARD_STATUS = BackAward.AWARD_STATUS;
                        HIS.HOT_YN = BackAward.HOT_YN;
                        HIS.BUY_PERSON_YN = BackAward.BUY_PERSON_YN;
                        HIS.CHG_DATE = DateTime.Now;
                        HIS.READ_LEVEL = BackAward.READ_LEVEL;
                        HIS.PASSPORT_LEVEL = BackAward.PASSPORT_LEVEL;
                        HIS.QTY_LIMIT = BackAward.QTY_LIMIT;
                        HIS.SHOW_DESCRIPTION_YN = BackAward.SHOW_DESCRIPTION_YN;
                        HIS.FULLSCREEN_YN = BackAward.FULLSCREEN_YN;
                        db.AWAT02_HIS.Add(HIS);
                        if (BackAward.AWARD_TYPE != "B")
                        {
                            BackAward.QTY_STORAGE = (BackAward.QTY_STORAGE.Value) + 1;// +TranLog.TRANS_QTY.Value;
                            BackAward.QTY_TRANS = BackAward.QTY_TRANS.Value - 1;// TranLog.TRANS_QTY.Value;
                        }
                        else {
                            int? COST_CASH = 0;
                           // COST_CASH = Int32.Parse(vawa04["COST_CASH"]);
                            if (BackAward.AWARD_TYPE == "B" && TranLog .TRANS_CASH< BackAward.BID_BUY_PRICE){
                                BackAward.QTY_STORAGE = (BackAward.QTY_STORAGE.Value) + 1;// +TranLog.TRANS_QTY.Value;
                                BackAward.QTY_TRANS = BackAward.QTY_TRANS.Value - 1;// TranLog.TRANS_QTY.Value;
                            }

                        }
                        string BATCH_ID = PushService.CreBATCH_ID();
                        string BODY_TXT = string.Empty;

                        //還原學生酷幣點數
                        if (BackAward.AWARD_TYPE == "C")
                        {
                            BODY_TXT = "被取消兌換募資公益，品名:" + HIS.AWARD_NAME + "加回原酷幣點數" + (TranLog.TRANS_CASH.Value).ToString() + "數";

                            CashHelper.AddCash(user, TranLog.TRANS_CASH.Value, TranLog.SCHOOL_NO, TranLog.USER_NO, "AWAT02", TranLog.TRANS_NO.ToString(), "取消兌換募資公益", false, ref db, "", "",ref valuesList);
                        }
                        else
                        {
                            BODY_TXT = "取消獎品兌換，品名:" + HIS.AWARD_NAME + "加回原酷幣點數" + (TranLog.TRANS_CASH.Value).ToString() + "數";

                            CashHelper.AddCash(user, TranLog.TRANS_CASH.Value, TranLog.SCHOOL_NO, TranLog.USER_NO, "AWAT02", TranLog.TRANS_NO.ToString(), "取消獎品兌換", false, ref db, "", "", ref valuesList);
                        }

                        //PushService.InsertPushDataParents(BATCH_ID, TranLog.SCHOOL_NO, TranLog.USER_NO, "", BODY_TXT, "", "AWA004", "CancelTrans", TranLogNO.ToString(), "", false, ref db);
                        PushService.InsertPushDataMe(BATCH_ID, TranLog.SCHOOL_NO, TranLog.USER_NO, "", BODY_TXT, "", "AWA004", "CancelTrans", TranLogNO.ToString(), "", false, ref db);

                        db.SaveChanges();
                        TempData["StatusMessage"] = $"完成取消 {TranLog.NAME} 獎品[{BackAward.AWARD_NAME}]的兌換";

                        //Push
                        PushHelper.ToPushServer(BATCH_ID);
                    }
                    else if (vawa04["RoleName"] == "Teacher")
                    {
                        //調整商品庫存量
                        AWAT09 BackAward = db.AWAT09.Where(a => a.AWARD_NO == TranLog.AWARD_NO).FirstOrDefault();

                        AWAT09_HIS HIS = db.AWAT09_HIS.Create();
                        HIS.AWARD_NO = BackAward.AWARD_NO;
                        HIS.SCHOOL_NO = BackAward.SCHOOL_NO;
                        HIS.AWARD_TYPE = BackAward.AWARD_TYPE;
                        HIS.AWARD_NAME = BackAward.AWARD_NAME;
                        HIS.COST_CASH = BackAward.COST_CASH;
                        HIS.QTY_STORAGE = BackAward.QTY_STORAGE;
                        HIS.SDATETIME = BackAward.SDATETIME;
                        HIS.EDATETIME = BackAward.EDATETIME;
                        HIS.DESCRIPTION = BackAward.DESCRIPTION;
                        HIS.IMG_FILE = BackAward.IMG_FILE;
                        HIS.IMG2_FILE = BackAward.IMG2_FILE;
                        HIS.AWARD_STATUS = BackAward.AWARD_STATUS;
                        HIS.HOT_YN = BackAward.HOT_YN;
                        HIS.BUY_PERSON_YN = BackAward.BUY_PERSON_YN;
                        HIS.CHG_DATE = DateTime.Now;
                        HIS.QTY_LIMIT = BackAward.QTY_LIMIT;
                        HIS.SHOW_DESCRIPTION_YN = BackAward.SHOW_DESCRIPTION_YN;
                        db.AWAT09_HIS.Add(HIS);

                        BackAward.QTY_STORAGE = BackAward.QTY_STORAGE.Value + 1;// +TranLog.TRANS_QTY.Value;
                        BackAward.QTY_TRANS = BackAward.QTY_TRANS.Value - 1;// TranLog.TRANS_QTY.Value;

                        CashHelper.TeachAddCash(user, TranLog.TRANS_CASH.Value, TranLog.SCHOOL_NO, TranLog.USER_NO, "AWAT09", TranLog.TRANS_NO.ToString(), "取消獎品兌換", false, null, ref db);

                        db.SaveChanges();
                        TempData["StatusMessage"] = $"完成取消 {TranLog.NAME} 獎品[{BackAward.AWARD_NAME}]的兌換";
                    }

                    UserProfile.RefreshCashInfo(user, ref db);
                    UserProfileHelper.Set(user);
                }
                else
                {
                    TempData["StatusMessage"] = "重覆取消[" + TranLog.NAME + "]的獎品";
                }
            }

            if (vawa04["ActionFrom"] != null) // 來源為Query Action傳來?
            {
                return RedirectToAction(vawa04["ActionFrom"].ToString(), "AWA004", new { RoleName = vawa04["RoleName"], whereUserNo = vawa04["whereUserNo"] });
            }

            return RedirectToAction("ModifyQuery", "AWA004", new { RoleName = vawa04["RoleName"], whereSTATUS= vawa04["whereSTATUS"] });
        }
    }
}