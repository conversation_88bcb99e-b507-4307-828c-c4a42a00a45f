/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXNonUnicode={directory:"NonUnicode/Regular",family:"STIXNonUnicode",Ranges:[[32,32,"All"],[160,160,"All"],[57344,63743,"PrivateUse"]],57344:[610,25,1184,829,895],57345:[667,-41,1184,829,1211],57656:[634,-584,480,-10,490],57657:[-127,177,480,-10,490],57659:[955,-512,897,-25,908],57660:[955,-512,897,-11,922],57661:[182,261,897,-25,908],57662:[182,261,897,-11,922],57664:[1218,-820,1844,-10,1854],57665:[-126,524,1844,-10,1854],57674:[955,-820,633,-1,634],57675:[-126,261,633,-1,634],57953:[422,10,523,41,481],57957:[421,0,523,127,405],57961:[421,0,523,68,455],57965:[424,198,523,47,463],57969:[420,198,523,58,480],57973:[421,198,523,66,457],57977:[612,8,523,37,486],57981:[421,198,523,25,490],57985:[606,12,523,47,477],57989:[421,200,523,41,483],57999:[135,0,325,-1,326],58000:[135,0,633,-1,634]};MathJax.OutputJax["HTML-CSS"].initFont("STIXNonUnicode");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/NonUnicode/Regular/Main.js");
