﻿@model BatchCashIntoIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "CashIntoView" });
    }
}

@using (Html.BeginForm("CashIntoView", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div id="PageContent">
        @Html.Action("_PageCashInto", (string)ViewBag.BRE_NO, Model)
    </div>
}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
    var targetFormID = '#form1'

        //分頁
         function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.CashSearch.Page)').val(page)
                funAjax()
            }
        };

         //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_PageCashInto", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function DelApply() {

            var OK = confirm("您確定要刪除選取的報名資料，按確定後，報名著資料及報名著參與的活動內容，將全部被刪除!!，刪除後無法還原!!!!")

            if (OK) {
                $('html, body').scrollTop(0);
                $(targetFormID).hide()
                $('#loading').fadeIn(3000)
                setTimeout(function () {
                     $(targetFormID).attr("action", "@Url.Action("DelApplyData", (string)ViewBag.BRE_NO)")
                     $(targetFormID).submit();
                }, 3000);
            }
        }

        function CashInto() {

            var OK = confirm("您確定要餘額轉入，按確定後，活動酷幣餘額將轉入原學生/老師酷幣裡，訪客、其它e酷幣無帳號或無酷幣身份者，活動酷幣餘額將歸 0，不會有轉入的動作!!!!")
            if (OK) {
                  $('html, body').scrollTop(0);
                  $(targetFormID).hide()
                  $('#loading').fadeIn(3000)
                  setTimeout(function () {
                 $(targetFormID).attr("action", "@Url.Action("CashIntoData", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
            }, 3000);
            }

        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("BatchWork", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}