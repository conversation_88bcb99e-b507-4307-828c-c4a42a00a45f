/*
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
mainui.css (part of editor.css)
=================================

This file styles the basic structure of the CKEditor user interface - the box
that holds everything.

CKEditor offers two main editing modes. The main UI blocks that compose these
modes are:

	For "Theme UI" mode, the one most generally used:

	+-- .cke_chrome ----------------------+
	|+-- .cke_inner ---------------------+|
	|| +-- .cke_top -------------------+ ||
	|| |                               | ||
	|| +-------------------------------+ ||
	|| +-- .cke_contents --------------+ ||
	|| |                               | ||
	|| +-------------------------------+ ||
	|| +-- .cke_bottom ----------------+ ||
	|| |                               | ||
	|| +-------------------------------+ ||
	|+-----------------------------------+|
	+-------------------------------------+

	For "Inline Editing" mode:

	+-- .cke_chrome .cke_float------------+
	|+-- .cke_inner ---------------------+|
	|| +-- .cke_top -------------------+ ||
	|| |                               | ||
	|| +-------------------------------+ ||
	|+-----------------------------------+|
	+-------------------------------------+

Special outer level classes used in this file:

	.cke_hc: Available when the editor is rendered on "High Contrast".

*/

/* The outer boundary of the interface. */
.cke_chrome
{
	/* This is <span>, so transform it into a block.*/
	display: block;
	border: 1px solid #b6b6b6;
	padding: 0;

	box-shadow: 0 0 3px rgba(0,0,0,.15);
}

/* The inner boundary of the interface. */
.cke_inner
{
	/* This is <span>, so transform it into a block.*/
	display: block;

	-webkit-touch-callout: none;

	background: #fff;
	padding: 0;
}

/* Added to the outer boundary of the UI when in inline editing,
   when the UI is floating. */
.cke_float
{
	/* Make white the space between the outer and the inner borders. */
	border: none;
}

.cke_float .cke_inner
{
	/* As we don't have blocks following top (toolbar) we suppress the padding
	   as the toolbar defines its own margin. */
	padding-bottom: 0;
}

/* Make the main spaces enlarge to hold potentially floated content. */
.cke_top,
.cke_contents,
.cke_bottom
{
	/* These are <span>s, so transform them into blocks.*/
	display: block;

	/* Ideally this should be "auto", but it shows scrollbars in IE7. */
	overflow: hidden;
}

.cke_top
{
	/*border: 1px solid #b2b2b2;*/
	border-bottom: 1px solid #b6b6b6;
	padding: 6px 8px 2px;

	/* Allow breaking toolbars when in a narrow editor. (#9947) */
	white-space: normal;

	box-shadow: 0 1px 0 #fff inset;

	background: #cfd1cf;
	background-image: linear-gradient(to bottom, #f5f5f5, #cfd1cf);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#f5f5f5', endColorstr='#cfd1cf');
}

.cke_float .cke_top
{
	border: 1px solid #b6b6b6;
	border-bottom-color: #999;
}

.cke_bottom
{
	padding: 6px 8px 2px;
	position: relative;

	border-top: 1px solid #bfbfbf;

	box-shadow: 0 1px 0 #fff inset;

	background: #cfd1cf;
	background-image: linear-gradient(to bottom, #ebebeb, #cfd1cf);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#ebebeb', endColorstr='#cfd1cf');
}

/* On iOS we need to manually enable scrolling in the contents block. (#9945) */
.cke_browser_ios .cke_contents
{
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* The resizer is the small UI element that is rendered at the bottom right
   part of the editor. It makes is possible to resize the editor UI. */
.cke_resizer
{
	/* To avoid using images for the resizer, we create a small triangle,
	   using some CSS magic. */
	width: 0;
	height: 0;
	overflow: hidden;
	width: 0;
	height: 0;
	overflow: hidden;
	border-width: 10px 10px 0 0;
	border-color: transparent #666 transparent transparent;
	border-style: dashed solid dashed dashed;

	font-size: 0;
	vertical-align: bottom;

	margin-top: 6px;

	/* 	A margin in case of no other element in the same container
		to keep a distance to the bottom edge. */
	margin-bottom: 2px;

	box-shadow: 0 1px 0 rgba(255,255,255,.3);
}

.cke_hc .cke_resizer
{
	font-size: 15px;
	width: auto;
	height: auto;
	border-width: 0;
}

.cke_resizer_ltr
{
	cursor: se-resize;

	float: right;
	margin-right: -4px;
}

/* This class is added in RTL mode. This is a special case for the resizer
   (usually the .cke_rtl class is used), because it may not necessarily be in
   RTL mode if the main UI is RTL. It depends instead on the context where the
   editor is inserted on. */
.cke_resizer_rtl
{
	border-width: 10px 0 0 10px;
	border-color: transparent transparent transparent #A5A5A5;
	border-style: dashed dashed dashed solid;

	cursor: sw-resize;

	float: left;
	margin-left: -4px;
	right: auto;
}

/* The editing area (where users type) can be rendered as an editable <div>
   element (e.g. divarea plugin). In that case, this is the class applied to
   that element. */
.cke_wysiwyg_div
{
	display: block;
	height: 100%;
	overflow: auto;
	padding: 0 8px;
	outline-style: none;

	box-sizing: border-box;
}
