#atm_bg_a1 {
    width: auto;
    height: 540px;
    background-image: url("../../assets/img/atm_bg_hor.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 13px;
    padding: 15px 25px 10px 25px;
}

@media (min-width: 576px) {
    #atm_bg_a1 {
        width: auto;
        height: 580px;
    }
}

@media (min-width: 768px) {
    #atm_bg_a1 {
        width: auto;
        height: 500px;
    }
}

@media (min-width: 992px) {
    #atm_bg_a1 {
        width: auto;
        height: 550px;
    }
}

@media (min-width: 1200px) {
    #atm_bg_a1 {
        width: auto;
        height: 90vh;
    }
}

#atm_head_a1 {
    text-align: center;
}

#atm_logo {
    width: 165px;
    height: 60px;
}

@media (min-width: 576px) {
    #atm_logo {
        width: 286px;
        height: 82px;
    }
}

@media (min-width: 768px) {
    #atm_logo {
        width: 293px;
        height: 88px;
        margin-top: -3px;
    }
}

@media (min-width: 992px) {
    #atm_logo {
        width: 293px;
        height: 99px;
        margin-top: -3px;
    }
}

@media (min-width: 1200px) {
    #atm_logo {
        width: 57vw;
        height: 18vh;
    }
}

#atm_content_l_a1 {
    height: 200px;
    background-image: url("../../assets/img/atm_display_hor.png");
    background-position: top;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 23px 20px 23px 20px;
    margin-top: -40px;
}

@media (min-width: 576px) {
    #atm_content_l_a1 {
        height: 290px;
        margin-top: 30px;
    }
}

@media (min-width: 768px) {
    #atm_content_l_a1 {
        height: 304px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 55px 23px 55px;
        margin-top: -80px;
    }
}

@media (min-width: 992px) {
    #atm_content_l_a1 {
        height: 364px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 65px 23px 65px;
        margin-top: -50px;
    }
}

@media (min-width: 1200px) {
    #atm_content_l_a1 {
        width: 70%;
        height: 90%;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 95px 23px 95px;
        margin-top: -22px;
    }
}

#board_text_size-a1 {
    margin-top: 14px;
    padding-bottom: 0px;
    font-size: 0.4em;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #01e1ff;
    text-shadow: 0 1px 0 #2d8396, 0 1.5px 0 #1a718d, 0 2px 0 #09486b, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25);
}

@media (min-width: 376px) {
    #board_text_size-a1 {
        margin-top: 15px;
        padding-bottom: 0px;
        font-size: 0.52em;
    }
}

@media (min-width: 576px) {
    #board_text_size-a1 {
        margin-top: 15px;
        padding-bottom: 0px;
        font-size: 0.82em;
    }
}

@media (min-width: 768px) {
    #board_text_size-a1 {
        padding-bottom: 0px;
        font-size: 0.62em;
    }
}

@media (min-width: 992px) {
    #board_text_size-a1 {
        padding-bottom: 1px;
        font-size: 0.81em;
    }
}

@media (min-width: 1024px) {
    #board_text_size-a1 {
        font-size: .8em;
    }
}

@media (min-width: 1200px) {
    #board_text_size-a1 {
        font-size: 1.0em;
    }
}

@media (min-width: 1440px) {
    #board_text_size-a1 {
        font-size: 1.0em;
    }
}

@media (min-width: 1600px) {
    #board_text_size-a1 {
        font-size: 1.0em;
    }
}

@media (min-width: 1900px) {
    #board_text_size-a1 {
        font-size: 1.2em;
    }
}

#board_text_size-a1-1 {
    position: relative;
    width: 100%;
    margin-top: 15px;
    padding-bottom: 0px;
    font-size: 0.7em;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #fff500;
    text-shadow: 0 1px 0 #d3c9af, 0 1.5px 0 #969164, 0 2px 0 #775f3a, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2);
}

@media (min-width: 576px) {
    #board_text_size-a1-1 {
        /*width:80%;*/
        margin-top: 14px;
        font-size: 1.3em;
    }
}

@media (min-width: 768px) {
    #board_text_size-a1-1 {
        /*width:80%;*/
        margin-top: 14px;
        font-size: 0.9em;
    }
}

@media (min-width: 992px) {
    #board_text_size-a1-1 {
        /*width:80%;*/
        margin-top: 15px;
        font-size: 1.1em;
    }
}

@media (min-width: 1200px) {
    #board_text_size-a1-1 {
        position: relative;
        /*width:430px;*/
        margin-top: 10px;
        font-size: 1.8em;
    }
}

#board_text_2-1_b1 {
    margin-left: 0px;
}

@media (min-height: 500px) {
    #board_text_2-1_b1 {
        margin-top: 30px;
    }
}

@media (min-height: 600px) {
    #board_text_2-1_b1 {
        margin-top: 40px;
    }
}

@media (min-height: 700px) {
    #board_text_2-1_b1 {
        margin-top: 65px;
    }
}

@media (min-height: 500px) {
    #run {
        height: 10px
    }
}

@media (min-height: 600px) {
    #run {
        height: 10px
    }
}

@media (min-height: 700px) {
    #run {
        height: 20px
    }
}

@media (min-height: 1000px) {
    #run {
        height: 50px
    }
}

#board_text_frame_a4_b2 {
    height: 100%;
    width: 100%;
}

#board_text_frame_a4_b2 {
    margin-top: 10px;
    padding: 0px;
}

@media (min-height: 500px) {
    #board_text_frame_a4_b2 {
        margin-top: 10px;
        padding: 0px;
    }
}

@media (min-height:600px) {
    #board_text_frame_a4_b2 {
        margin-top: 30px;
        padding: 0px;
    }
}

@media (min-height: 700px) {
    #board_text_frame_a4_b2 {
        margin-top: 40px;
        padding: 0px;
    }
}

@media (min-height: 1000px) {
    #board_text_frame_a4_b2 {
        margin-top: 70px;
        padding: 0px;
    }
}

#board_text_frame_a4_b1 {
    height: 100%;
    width: 100%;
}

#board_text_frame_a4_b1 {
    margin-top: 10px;
    padding: 0px;
}

@media (min-height: 500px) {
    #board_text_frame_a4_b1 {
        padding: 0px;
        margin-top: 20px;
    }
}

@media (min-height:600px) {
    #board_text_frame_a4_b1 {
        padding: 0px;
        margin-top: 10px;
    }
}

@media (min-height: 700px) {
    #board_text_frame_a4_b1 {
        padding: 0px;
        margin-top: 30px;
    }
}

@media (min-height: 1000px) {
    #board_text_frame_a4_b1 {
        padding: 0px;
        margin-top: 60px;
    }
}

#atm_con_l_row_a1 {
    height: 100%;
    width: 100%;
}

#board_text_input_a1 {
    font-size: 1em;
    margin-top: 24px;
    width: 95px;
    height: 13px;
    margin-left: 7px;
}

@media (min-width: 576px) {
    #board_text_input_a1 {
        margin-top: 35px;
        width: 164px;
        height: 20px;
        margin-left: 14px;
    }
}

@media (min-width: 768px) {
    #board_text_input_a1 {
        margin-top: 37px;
        width: 173px;
        height: 22px;
        margin-left: 13px;
    }
}

@media (min-width: 992px) {
    #board_text_input_a1 {
        margin-top: 47px;
        width: 215px;
        height: 26px;
        margin-left: 16px;
    }
}

@media (min-width: 1200px) {
    #board_text_input_a1 {
        margin-top: 8vh;
        width: 18vw;
        height: 19%;
        margin-left: 4%;
    }
}

#WhereKeyword {
    outline: none !important;
    border: none;
    font-size: 0.8em;
    margin-top: 20px;
    width: 95px;
    height: 13px;
    margin-left: 7px;
}

@media (min-width: 576px) {
    #WhereKeyword {
        margin-top: 35px;
        width: 164px;
        height: 20px;
        margin-left: 14px;
    }
}

@media (min-width: 768px) {
    #WhereKeyword {
        margin-top: 35px;
        width: 173px;
        height: 22px;
        margin-left: 13px;
    }
}

@media (min-width: 992px) {
    #WhereKeyword {
        margin-top: 47px;
        width: 215px;
        height: 26px;
        margin-left: 16px;
    }
}

@media (min-width: 1200px) {
    #WhereKeyword {
        margin-top: 9vh;
        width: 22vh;
        height: 19%;
        margin-left: 0%;
    }
}

@media (min-width: 1200px) {
    #atm_content_r-c2_a1 {
        height: 70%;
        width: 100%;
    }
}

#atm_btn_img_1:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/atm_button_click_01.png);
}

#atm_btn_img_4:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/istory_button_01.png);
}

#atm_btn_img_2:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/atm_button_click_02.png);
}

#atm_btn_img_3:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/Ribon_0004_box_c1.png);
}

#atm_btn_img_1 {
    height: 6vh;
    background-image: url(../../assets/img/atm_button_01.png);
}

@media (min-width: 576px) {
    #atm_btn_img_1 {
        height: 10vh;
    }
}

@media (min-width: 992px) {
    #atm_btn_img_1 {
        height: 13vh;
    }
}

#atm_btn_img_2 {
    height: 6vh;
    /*background-color:rgb(39,193,14);*/
    background-image: url(../../assets/img/atm_button_02.png);
}

#atm_btn_img_4 {
    height: 6vh;
    /*background-color:rgb(39,193,14);*/
    background-image: url(../../assets/img/istory_button_01.png);
}

@media (min-width: 576px) {
    #atm_btn_img_2 {
        height: 10vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/atm_button_02.png);
    }

    #atm_btn_img_4 {
        height: 10vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/istory_button_01.png);
    }
}

@media (min-width: 992px) {
    #atm_btn_img_2 {
        height: 13vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/atm_button_02.png);
    }

    #atm_btn_img_4 {
        height: 13vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/istory_button_01.png);
    }
}

#atm_video_v1 {
    width: 90%;
    height: 92%;
    background-position: center;
    background-repeat: no-repeat;
}

@media (min-width: 576px) {
    #atm_video_v1 {
        width: 84%;
        height: 93%;
        background-position: center;
        background-repeat: no-repeat;
    }
}

@media (min-width: 768px) {
    #atm_video_v1 {
        width: 100%;
        height: 89%;
        background-position: center;
        background-repeat: no-repeat;
    }
}

@media (min-width: 992px) {
    #atm_video_v1 {
        width: 78%;
        height: 95%;
        background-position: center;
        background-repeat: no-repeat;
    }
}

@media (min-width: 1200px) {
    #atm_video_v1 {
        width: 99%;
        height: 95%;
        background-position: center;
        background-repeat: no-repeat;
    }
}

#atm_contain_a1 {
    width: 95%;
    height: 440px;
    margin: 6px 0px 0px 5px;
}

@media (min-width: 576px) {
    #atm_contain_a1 {
        width: 90%;
        height: 470px;
        margin: 15px 0px 0px 25px;
    }
}

@media (min-width: 768px) {
    #atm_contain_a1 {
        width: 92%;
        padding: 20px;
        height: 420px;
        margin: 16px 0px 0px 30px;
    }
}

@media (min-width: 992px) {
    #atm_contain_a1 {
        width: 90%;
        padding: 40px;
        height: 420px;
        margin: 16px 0px 0px 30px;
    }
}

@media (min-width: 1200px) {
    #atm_contain_a1 {
        width: 100%;
        height: 100%;
        padding: 5vw;
        margin: -6vw 0px 0px 5vw;
    }
}

#atm_bg_a2 {
    width: auto;
    height: 340px;
    background-image: url("../../assets/img/atm_bg_hor.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 13px;
    padding: 15px 25px 10px 25px;
}

@media (min-width: 576px) {
    #atm_bg_a2 {
        width: auto;
        height: 490px;
    }
}

@media (min-width: 768px) {
    #atm_bg_a2 {
        width: auto;
        height: 600px;
    }
}

@media (min-width: 992px) {
    #atm_bg_a2 {
        width: auto;
        height: 610px;
    }
}

@media (min-width: 1200px) {
    #atm_bg_a2 {
        width: auto;
        height: 94vh;
    }
}

#atm_head {
    text-align: center;
}

#atm_content_l_a2 {
    height: 200px;
    background-image: url("../../assets/img/atm_display_hor.png");
    background-position: top;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 23px 20px 23px 20px;
    margin-top: -40px;
}

@media (min-width: 576px) {
    #atm_content_l_a2 {
        height: 330px;
        margin-top: 30px;
    }
}

@media (min-width: 768px) {
    #atm_content_l_a2 {
        height: 414px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 55px 23px 55px;
        margin-top: -20px;
    }
}

@media (min-width: 992px) {
    #atm_content_l_a2 {
        height: 444px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 65px 23px 65px;
        margin-top: 0px;
    }
}

@media (min-width: 1200px) {
    #atm_content_l_a2 {
        width: 70%;
        height: 70%;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 95px 23px 95px;
        margin-top: -82px;
    }
}

#board_text_size-a2-1 {
    margin-top: 14px;
    padding-bottom: 0px;
    font-size: 0.35em;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #01e1ff;
    text-shadow: 0 1px 0 #2d8396, 0 1.5px 0 #1a718d, 0 2px 0 #09486b, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25);
}

@media (min-width: 375px) {
    #board_text_size-a2-1 {
        width: 200px;
        margin-top: 15px;
        padding-bottom: 0px;
        font-size: 0.6em;
    }
}

@media (min-width: 486px) {
    #board_text_size-a2-1 {
        margin-top: 15px;
        padding-bottom: 0px;
        font-size: .8em;
    }
}

@media (min-width: 576px) {
    #board_text_size-a2-1 {
        margin-top: 15px;
        padding-bottom: 0px;
        font-size: 1em;
    }
}

@media (min-width: 768px) {
    #board_text_size-a2-1 {
        padding-bottom: 0px;
        font-size: 1.1em;
    }
}

@media (min-width: 992px) {
    #board_text_size-a2-1 {
        padding-bottom: 1px;
        font-size: 1.5em;
    }
}

@media (min-width: 1200px) {
    #board_text_size-a2-1 {
        font-size: 1.2em;
    }
}

@media (min-width: 1400px) {
    #board_text_size-a2-1 {
        font-size: 1.4em;
    }
}

@media (min-width: 1600px) {
    #board_text_size-a2-1 {
        font-size: 1.6em;
    }
}

@media (min-width: 1900px) {
    #board_text_size-a2-1 {
        font-size: 1.9em;
    }
}

#atm_con_l_row_a2 {
    height: 100%;
    width: 100%;
}

#board_text_input_a2_1 {
    font-size: 1em;
    margin-top: 24px;
    width: 94px;
    height: 13px;
    margin-left: 7px;
}

@media (min-width: 576px) {
    #board_text_input_a2_1 {
        margin-top: 42px;
        width: 194px;
        height: 20px;
        margin-left: 14px;
    }
}

@media (min-width: 768px) {
    #board_text_input_a2_1 {
        margin-top: 53px;
        width: 243px;
        height: 25px;
        margin-left: 14px;
    }
}

@media (min-width: 992px) {
    #board_text_input_a2_1 {
        margin-top: 59px;
        width: 265px;
        height: 29px;
        margin-left: 16px;
    }
}

@media (min-width: 1200px) {
    #board_text_input_a2_1 {
        margin-top: 8vh;
        width: 18vw;
        height: 19%;
        margin-left: 4%;
    }
}

@media (min-width: 1600px) {
    #board_text_input_a2_1 {
        margin-top: 14vh;
        width: 18vw;
        height: 19%;
        margin-left: 4%;
    }
}

#atm_btn_img_1:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/atm_button_click_01.png);
}

#atm_btn_img_2:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/atm_button_click_02.png);
}

#atm_btn_img_1 {
    height: 6vh;
    background-image: url(../../assets/img/atm_button_01.png);
}

@media (min-width: 576px) {
    #atm_btn_img_1 {
        height: 10vh;
    }
}

@media (min-width: 992px) {
    #atm_btn_img_1 {
        height: 13vh;
    }
}

#atm_btn_img_2 {
    height: 6vh;
    /*background-color:rgb(39,193,14);*/
    background-image: url(../../assets/img/atm_button_02.png);
}

@media (min-width: 576px) {
    #atm_btn_img_2 {
        height: 10vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/atm_button_02.png);
    }
}

@media (min-width: 992px) {
    #atm_btn_img_2 {
        height: 13vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/atm_button_02.png);
    }
}

#atm_video_v1 {
    width: 100%;
    height: auto;
    background-position: center;
    background-repeat: no-repeat;
}

#atm_contain_a2 {
    width: 95%;
    height: 240px;
    margin: 6px 0px 0px 5px;
}

@media (min-width: 576px) {
    #atm_contain_a2 {
        width: 90%;
        height: 280px;
        margin: 15px 0px 0px 25px;
    }
}

@media (min-width: 768px) {
    #atm_contain_a2 {
        width: 92%;
        padding: 20px;
        height: 420px;
        margin: 16px 0px 0px 30px;
    }
}

@media (min-width: 992px) {
    #atm_contain_a2 {
        width: 90%;
        padding: 40px;
        height: 420px;
        margin: 16px 0px 0px 42px;
    }
}

@media (min-width: 1200px) {
    #atm_contain_a2 {
        width: 95%;
        height: 100%;
        padding: 37px 87px 37px 87px;
        margin: -4vw 0px 0px 26px;
    }
}

#atm_content_l_b1 {
    height: 360px;
    background-image: url("../../assets/img/atm_display_hor.png");
    background-position: top;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 23px 20px 23px 20px;
    margin-top: 100px;
    font-size: 1vw;
}

@media (min-width: 576px) {
    #atm_content_l_b1 {
        height: 290px;
        margin-top: 30px;
        padding: 34px 30px 23px 30px;
    }
}

@media (min-width: 768px) {
    #atm_content_l_b1 {
        height: 304px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 40px 25px 23px 25px;
        margin-top: -80px;
    }
}

@media (min-width: 992px) {
    #atm_content_l_b1 {
        height: 364px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 50px 65px 43px 50px;
        margin-top: -50px;
    }
}

@media (min-width: 1200px) {
    #atm_content_l_b1 {
        width: 70%;
        height: 90%;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 3% 5% 5% 5%;
        margin-top: -22px;
    }
}

#atm_con_l_row_b1 {
    height: 100%;
    width: 100%;
}

#board_text_input_1 {
    font-size: 1em;
    margin-top: 24px;
    width: 94px;
    height: 13px;
    margin-left: 7px;
}

@media (min-width: 576px) {
    #board_text_input_1 {
        margin-top: 35px;
        width: 164px;
        height: 20px;
        margin-left: 14px;
    }
}

@media (min-width: 768px) {
    #board_text_input_1 {
        margin-top: 37px;
        width: 173px;
        height: 22px;
        margin-left: 13px;
    }
}

@media (min-width: 992px) {
    #board_text_input_1 {
        margin-top: 47px;
        width: 215px;
        height: 26px;
        margin-left: 16px;
    }
}

@media (min-width: 1200px) {
    #board_text_input_1 {
        margin-top: 11.5%;
        width: 51%;
        height: 23%;
        margin-left: 4%;
    }
}

#atm_content_r {
    margin-top: 10px;
    height: 180px;
}

@media (min-width: 576px) {
    #atm_content_r {
        margin-top: 20px;
        height: 205px;
    }
}

@media (min-width: 768px) {
    #atm_content_r {
        margin-top: -50px;
        height: 285px;
        padding: 0px;
    }
}

@media (min-width: 992px) {
    #atm_content_r {
        margin-top: -30px;
        height: 285px;
        padding: 0px;
    }
}

@media (min-width: 1200px) {
    #atm_content_r {
        /*position:relative;*/
        width: 97%;
        height: 44vh;
        margin-top: 0px;
        padding: 10px;
    }
}

#atm_content_r-1 {
    width: 110%;
    height: 100%;
}

@media (min-width: 576px) {
    #atm_content_r-1 {
        width: 110%;
        height: 100%;
    }
}

@media (min-width: 768px) {
    #atm_content_r-1 {
        width: 250px;
        height: 100%;
        padding: 17px;
        margin-top: -10px;
    }
}

@media (min-width: 992px) {
    #atm_content_r-1 {
        width: 330px;
        height: 110%;
        padding: 17px;
        margin-top: -25px;
    }
}

@media (min-width: 1200px) {
    #atm_content_r-1 {
        width: 80%;
        height: 110%;
        padding: 40px;
        margin-top: -65px;
    }
}

#atm_content_r-c1 {
    height: 50%;
    width: 100%;
}

@media (min-width: 768px) {
    #atm_content_r-c1 {
        height: 50%;
        width: 100%;
    }
}

@media (min-width: 1200px) {
    #atm_content_r-c1 {
        height: 70%;
        width: 100%;
    }
}

#atm_content_r-c2 {
    height: 50%;
    width: 100%;
}

@media (min-width: 768px) {
    #atm_content_r-c2 {
        height: 50%;
        width: 100%;
    }
}

@media (min-width: 1200px) {
    #atm_content_r-c2 {
        height: 70%;
        width: 100%;
    }
}

#atm_btn_img_1:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/atm_button_click_01.png);
}

#atm_btn_img_2:active {
    /*background-color:red;*/
    background-image: url(../../assets/img/atm_button_click_02.png);
}

#atm_btn_img_1 {
    height: 6vh;
    background-image: url(../../assets/img/atm_button_01.png);
}

@media (min-width: 576px) {
    #atm_btn_img_1 {
        height: 10vh;
    }
}

@media (min-width: 992px) {
    #atm_btn_img_1 {
        height: 13vh;
    }
}

#atm_btn_img_2 {
    height: 6vh;
    /*background-color:rgb(39,193,14);*/
    background-image: url(../../assets/img/atm_button_02.png);
}

@media (min-width: 576px) {
    #atm_btn_img_2 {
        height: 10vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/atm_button_02.png);
    }
}

@media (min-width: 992px) {
    #atm_btn_img_2 {
        height: 13vh;
        /*background-color:rgb(39,193,14);*/
        background-image: url(../../assets/img/atm_button_02.png);
    }
}

#atm_video_v1 {
    width: 90%;
    height: 92%;
    background-position: center;
    background-repeat: no-repeat;
}

@media (min-width: 576px) {
    #atm_video_v1 {
        width: 84%;
        height: 93%;
    }
}

@media (min-width: 768px) {
    #atm_video_v1 {
        width: 100%;
        height: 89%;
    }
}

@media (min-width: 992px) {
    #atm_video_v1 {
        width: 78%;
        height: 95%;
    }
}

@media (min-width: 1200px) {
    #atm_video_v1 {
        width: 99%;
        height: 95%;
    }
}

@media (min-width: 576px) {
    #board_text_2-2_b1 {
        margin-left: 0px;
    }
}

#board_text_2-2_b1 {
    margin-left: 10px;
}

#board_text_size-a3_b1 {
    position: relative;
    margin-top: 15px;
    margin-left: 3%;
    padding-bottom: 0px;
    font-size: 0.5em;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #01e1ff;
    text-shadow: 0 1px 0 #2d8396, 0 1.5px 0 #1a718d, 0 2px 0 #09486b, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25);
}

@media (min-width: 375px) {
    #board_text_size-a3_b1 {
        margin-top: 16px;
        margin-left: 7%;
        padding-bottom: 0px;
        font-size: 0.55em;
    }
}

@media (min-width: 486px) {
    #board_text_size-a3_b1 {
        margin-top: 16px;
        margin-left: 14%;
        padding-bottom: 0px;
        font-size: 0.75em;
    }
}

@media (min-width: 576px) {
    #board_text_size-a3_b1 {
        margin-top: 16px;
        margin-left: 9%;
        padding-bottom: 0px;
        font-size: 0.8em;
    }
}

@media (min-width: 768px) {
    #board_text_size-a3_b1 {
        margin-top: 16px;
        margin-left: 13%;
        padding-bottom: 0px;
        font-size: 0.8em;
    }
}

@media (min-width: 992px) {
    #board_text_size-a3_b1 {
        margin-top: 18px;
        margin-left: 9%;
        padding-bottom: 0px;
        font-size: 0.85em;
    }
}

@media (min-width: 1200px) {
    #board_text_size-a3_b1 {
        position: relative;
        margin-top: 4%;
        margin-left: 10%;
        /*padding-bottom:0px;*/
        font-size: 1.6em;
    }
}

#board_text_size-a4 {
    padding: 3% 0% 0% 21%;
    font-size: 40%;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB,;
    color: #dcdcdc;
    /*text-shadow: 0 0.5px 0 #0a0a0a,0 1px 0 #666664, 0 1.5px 0 #282827, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3);*/
}

@media (min-height: 400px) {
    #board_text_size-a4 {
        font-size: 125%;
        padding: 15% 0% 0% 1%;
    }
}

@media (min-height:700px) {
    #board_text_size-a4 {
        font-size: 130%;
        padding: 15% 0% 0% 1%;
    }
}

#atm_content_l_b2 {
    height: 200px;
    background-image: url("../../assets/img/atm_display_hor.png");
    background-position: top;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 23px 20px 23px 20px;
    margin-top: -40px;
}

@media (min-width: 576px) {
    #atm_content_l_b2 {
        height: 330px;
        margin-top: 30px;
    }
}

@media (min-width: 768px) {
    #atm_content_l_b2 {
        height: 414px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 55px 23px 55px;
        margin-top: -20px;
    }
}

@media (min-width: 992px) {
    #atm_content_l_b2 {
        height: 444px;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 65px 23px 65px;
        margin-top: 0px;
    }
}

@media (min-width: 1200px) {
    #atm_content_l_b2 {
        width: 70%;
        height: 70%;
        background-image: url("../../assets/img/atm_display_hor.png");
        background-position: top;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 30px 95px 23px 95px;
        margin-top: -82px;
    }
}

#board_text_size-a2_b2 {
    position: relative;
    width: 76%;
    margin-top: 15px;
    padding-bottom: 0px;
    font-size: 0.7em;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #fff500;
    text-shadow: 0 1px 0 #d3c9af, 0 1.5px 0 #969164, 0 2px 0 #775f3a, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2);
}

@media (min-width: 576px) {
    #row board_text {
        padding-top: 15px;
    }

    #board_text_size-a2_b2 {
        width: 80%;
        margin-top: 14px;
        font-size: 1.3em;
    }
}

@media (min-width: 768px) {
    #row board_text {
        padding-top: 15px;
    }

    #board_text_size-a2_b2 {
        width: 80%;
        margin-top: 14px;
        font-size: 1.2em;
    }
}

@media (min-width: 992px) {
    #row board_text {
        padding-top: 15px;
    }

    #board_text_size-a2_b2 {
        width: 80%;
        margin-top: 15px;
        font-size: 2.3vw;
    }
}

@media (min-width: 1200px) {
    #row board_text {
        padding-top: 15px;
    }

    #board_text_size-a2_b2 {
        position: relative;
        width: 80%;
        margin-top: 10px;
        font-size: 2.0vw;
    }
}

#atm_con_l_row_b2 {
    height: 100%;
    width: 100%;
    padding: 0% 0% 0% 0%;
}

@media (min-width: 576px) {
    #atm_con_l_row_b2 {
        height: 100%;
        width: 100%;
        padding: 4% 3% 0% 3%;
    }
}

#board_text_input_1 {
    font-size: 1em;
    margin-top: 24px;
    width: 94px;
    height: 13px;
    margin-left: 7px;
}

@media (min-width: 576px) {
    #board_text_input_1 {
        margin-top: 42px;
        width: 194px;
        height: 20px;
        margin-left: 14px;
    }
}

@media (min-width: 768px) {
    #board_text_input_1 {
        margin-top: 53px;
        width: 243px;
        height: 25px;
        margin-left: 14px;
    }
}

@media (min-width: 992px) {
    #board_text_input_1 {
        margin-top: 59px;
        width: 265px;
        height: 29px;
        margin-left: 16px;
    }
}

@media (min-width: 1200px) {
    #board_text_input_1 {
        position: absolute;
        margin-top: 4.2vh;
        width: 350px;
        height: 4.5vh;
        margin-left: 1vw;
    }
}

#board_text_2-s1 {
    margin-left: -13px;
}

@media (min-width: 576px) {
    #board_text_2-2 {
        margin-left: 0px;
    }
}

#board_text_2-2 {
    margin-left: 10px;
}

#board_text_size-a3_b2 {
    position: relative;
    margin-top: 16.5px;
    margin-left: 3%;
    padding-bottom: 0px;
    font-size: 0.5em;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #01e1ff;
    text-shadow: 0 1px 0 #2d8396, 0 1.5px 0 #1a718d, 0 2px 0 #09486b, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25);
}

@media (min-width: 375px) {
    #board_text_size-a3_b2 {
        margin-top: 16px;
        margin-left: 7%;
        padding-bottom: 0px;
        font-size: 0.55em;
    }
}

@media (min-width: 486px) {
    #board_text_size-a3_b2 {
        margin-top: 16px;
        margin-left: 14%;
        padding-bottom: 0px;
        font-size: 0.75em;
    }
}

@media (min-width: 576px) {
    #board_text_size-a3_b2 {
        margin-top: 17px;
        margin-left: 9%;
        padding-bottom: 0px;
        font-size: 0.8em;
    }
}

@media (min-width: 768px) {
    #board_text_size-a3_b2 {
        margin-top: 19px;
        margin-left: 10%;
        padding-bottom: 0px;
        font-size: 1em;
    }
}

@media (min-width: 992px) {
    #board_text_size-a3_b2 {
        margin-top: 22px;
        margin-left: 13%;
        padding-bottom: 0px;
        font-size: 1.2em;
    }
}

@media (min-width: 1200px) {
    #board_text_size-a3_b2 {
        /*position: relative;*/
        margin-top: 4%;
        margin-left: 15%;
        /*padding-bottom:0px;*/
        font-size: 1.66em;
        padding: 0% 0% 2% 5%;
    }
}

@media (min-width: 1400px) {
    #board_text_size-a3_b2 {
        font-size: 2em;
    }
}

#board_text_size-a4-1 {
    padding: 3% 0% 0% 31%;
    font-size: 40%;
    font-weight: 700;
    font-family: "儷黑 Pro", "LiHei Pro", "微軟正黑體", "Microsoft JhengHei", "標楷體", DFKai-SB, sans-serif;
    color: #dcdcdc;
    text-shadow: 0 0.5px 0 #0a0a0a,0 1px 0 #666664, 0 1.5px 0 #282827, 0 4px 1px rgba(0,0,0,.1), 0 0 2px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3);
}

@media (min-width: 375px) {
    #board_text_size-a4-1 {
        padding: 3% 0% 0% 24%;
        font-size: 43%;
    }
}

@media (min-width: 426px) {
    #board_text_size-a4-1 {
        padding: 1% 0% 0% 22%;
        font-size: 49%;
    }
}

@media (min-width: 576px) {
    #board_text_size-a4-1 {
        padding: 4% 0% 0% 21%;
        font-size: 71%;
    }
}

@media (min-width: 768px) {
    #board_text_size-a4-1 {
        padding: 5% 0% 0% 31%;
        font-size: 88%;
    }
}

@media (min-width: 992px) {
    #board_text_size-a4-1 {
        padding: 4% 0% 0% 25%;
        font-size: 1.8vw;
    }
}

@media (min-width: 1200px) {
    #board_text_size-a4-1 {
        font-size: 1.5vw;
        padding: 1.2% 0% 0% 20%;
    }
}

#board_text_1-4_a1 {
    height: 100%;
    padding: 0% 6% 0% 6%;
    width: 100%;
}

@media (min-width: 380px) {
    #board_text_1-4_a1 {
        height: 100%;
        padding: 0% 14% 0% 14%;
        width: 100%;
    }
}

@media (min-width: 450px) {
    #board_text_1-4_a1 {
        height: 100%;
        padding: 0% 18% 0% 18%;
        width: 100%;
    }
}

@media (min-width: 520px) {
    #board_text_1-4_a1 {
        height: 100%;
        padding: 0% 21% 0% 21%;
        width: 100%;
    }
}

@media (min-width: 560px) {
    #board_text_1-4_a1 {
        height: 100%;
        padding: 0% 24% 0% 24%;
        width: 100%;
    }
}

@media (min-width: 576px) {
    #board_text_1-4_a1 {
        height: 100%;
        padding: 0% 10% 0% 10%;
        width: 100%;
    }
}

@media (min-width: 676px) {
    #board_text_1-4_a1 {
        height: 100%;
        padding: 0% 18% 0% 18%;
        width: 100%;
    }
}

@media (min-width: 768px) {
    #board_text_1-4_a1 {
        height: 100%;
        padding: 0% 10% 0% 10%;
        width: 100%;
    }
}