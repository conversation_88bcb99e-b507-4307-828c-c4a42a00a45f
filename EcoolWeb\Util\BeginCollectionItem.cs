﻿using System;
using System.IO;
using System.Web.Mvc;
using System.Web;
using System.Collections.Generic;

namespace HtmlHelpers.BeginCollectionItem
{
    public static class HtmlPrefixScopeExtensions
    {

        private const string IdsToReuseKeySou = "__htmlPrefixScopeExtensions_IdsToReuse_";


        public static IDisposable BeginCollectionItem(this HtmlHelper html, string collectionName, bool isCopy = false)
        {
            return BeginCollectionItem(html, collectionName, html.ViewContext.Writer, isCopy);
        }

        public static IDisposable BeginCollectionItem(this HtmlHelper html, string collectionName, TextWriter writer, bool isCopy = false)
        {

            var idsToReuse = GetIdsToReuseSou(html.ViewContext.HttpContext, collectionName);
            var itemIndex = isCopy == true ? Guid.NewGuid().ToString() : idsToReuse.Count > 0 ? idsToReuse.Dequeue() : Guid.NewGuid().ToString();

            // autocomplete="off" is needed to work around a very annoying Chrome behaviour
            // whereby it reuses old values after the user clicks "Back", which causes the
            // xyz.index and xyz[...] values to get out of sync.
            writer.WriteLine(
                "<input type=\"hidden\" name=\"{0}.index\" autocomplete=\"off\" value=\"{1}\" />",
                collectionName, html.Encode(itemIndex));

            return BeginHtmlFieldPrefixScope(html, string.Format("{0}[{1}]", collectionName, itemIndex));
        }

        public static IDisposable BeginHtmlFieldPrefixScope(this HtmlHelper html, string htmlFieldPrefix)
        {
            return new HtmlFieldPrefixScopeSou(html.ViewData.TemplateInfo, htmlFieldPrefix);
        }

        public static string GetIndex(this HtmlHelper html, string collectionName)
        {
            var htmlFieldPrefix = html.ViewData.TemplateInfo.HtmlFieldPrefix;
            string Index = string.Empty;

            if (htmlFieldPrefix.Contains(collectionName))
            {
                Index = htmlFieldPrefix.Substring(htmlFieldPrefix.IndexOf(collectionName) + collectionName.Length + 1);

                Index = Index.Substring(0, Index.Length - 1);
            }

            return Index;
        }


        public static IDisposable BeginCollectionItemSou(this HtmlHelper html, string collectionName, string SouCollectionName, string SouIndex, bool isCopy = false)
        {
            return BeginCollectionItemSou(html, collectionName, html.ViewContext.Writer, SouCollectionName, SouIndex);
        }

        public static IDisposable BeginCollectionItemSou(this HtmlHelper html, string collectionName, TextWriter writer, string SouCollectionName, string SouIndex, bool isCopy = false)
        {
            var idsToReuse = GetIdsToReuseSou(html.ViewContext.HttpContext, collectionName);
            var itemIndex = isCopy == true ? Guid.NewGuid().ToString() : idsToReuse.Count > 0 ? idsToReuse.Dequeue() : Guid.NewGuid().ToString();

            // autocomplete="off" is needed to work around a very annoying Chrome behaviour
            // whereby it reuses old values after the user clicks "Back", which causes the
            // xyz.index and xyz[...] values to get out of sync.
            writer.WriteLine(
                "<input type=\"hidden\" name=\"{0}[{1}].{2}.index\" autocomplete=\"off\" value=\"{3}\" />", SouCollectionName, SouIndex, collectionName, html.Encode(itemIndex));
            return BeginHtmlFieldPrefixScopeSou(html, string.Format("{0}[{1}].{2}[{3}]", SouCollectionName, SouIndex, collectionName, itemIndex));
        }

        public static IDisposable BeginHtmlFieldPrefixScopeSou(this HtmlHelper html, string htmlFieldPrefix)
        {
            return new HtmlFieldPrefixScopeSou(html.ViewData.TemplateInfo, htmlFieldPrefix);
        }

        private static Queue<string> GetIdsToReuseSou(HttpContextBase httpContext, string collectionName)
        {
            // We need to use the same sequence of IDs following a server-side validation failure,
            // otherwise the framework won't render the validation error messages next to each item.
            var key = IdsToReuseKeySou + collectionName;
            var queue = (Queue<string>)httpContext.Items[key];
            if (queue == null)
            {
                httpContext.Items[key] = queue = new Queue<string>();
                var previouslyUsedIds = httpContext.Request[collectionName + ".index"];
                if (!string.IsNullOrEmpty(previouslyUsedIds))
                    foreach (var previouslyUsedId in previouslyUsedIds.Split(','))
                        queue.Enqueue(previouslyUsedId);
            }
            return queue;
        }

        internal class HtmlFieldPrefixScopeSou : IDisposable
        {
            internal readonly TemplateInfo TemplateInfo;
            internal readonly string PreviousHtmlFieldPrefix;

            public HtmlFieldPrefixScopeSou(TemplateInfo templateInfo, string htmlFieldPrefix)
            {
                TemplateInfo = templateInfo;

                PreviousHtmlFieldPrefix = TemplateInfo.HtmlFieldPrefix;
                TemplateInfo.HtmlFieldPrefix = htmlFieldPrefix;
            }

            public void Dispose()
            {
                TemplateInfo.HtmlFieldPrefix = PreviousHtmlFieldPrefix;
            }
        }
    }
}