﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'bn', {
	border: 'বর্ডারের সাইজ',
	caption: 'শীর্ষক',
	cell: {
		menu: 'সেল',
		insertBefore: 'Insert Cell Before',
		insertAfter: 'Insert Cell After',
		deleteCell: 'সেল মুছে দাও',
		merge: 'সেল জোড়া দাও',
		mergeRight: 'Merge Right',
		mergeDown: 'Merge Down',
		splitHorizontal: 'Split Cell Horizontally',
		splitVertical: 'Split Cell Vertically',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'পৃষ্ঠতলের রং',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'সেল প্যাডিং',
	cellSpace: 'সেল স্পেস',
	column: {
		menu: 'কলাম',
		insertBefore: 'Insert Column Before',
		insertAfter: 'Insert Column After',
		deleteColumn: 'কলাম মুছে দাও'
	},
	columns: 'কলাম',
	deleteTable: 'টেবিল ডিলীট কর',
	headers: 'Headers', // MISSING
	headersBoth: 'Both', // MISSING
	headersColumn: 'First column', // MISSING
	headersNone: 'None',
	headersRow: 'First Row', // MISSING
	invalidBorder: 'Border size must be a number.', // MISSING
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Cell spacing must be a positive number.', // MISSING
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Table height must be a number.', // MISSING
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Table width must be a number.', // MISSING
	menu: 'টেবিল প্রোপার্টি',
	row: {
		menu: 'রো',
		insertBefore: 'Insert Row Before',
		insertAfter: 'Insert Row After',
		deleteRow: 'রো মুছে দাও'
	},
	rows: 'রো',
	summary: 'সারাংশ',
	title: 'টেবিল প্রোপার্টি',
	toolbar: 'টেবিলের লেবেল যুক্ত কর',
	widthPc: 'শতকরা',
	widthPx: 'পিক্সেল',
	widthUnit: 'width unit' // MISSING
} );
