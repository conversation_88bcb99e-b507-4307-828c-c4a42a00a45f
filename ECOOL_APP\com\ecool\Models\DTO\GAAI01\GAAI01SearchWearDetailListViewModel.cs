﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01SearchWearDetailListViewModel
    {
        /// <summary>
        ///防身周期ID
        /// </summary>
        [DisplayName("防身周期ID")]
        public string ALARM_ID { get; set; }

        /// <summary>
        ///週期
        /// </summary>
        [DisplayName("週期")]
        public byte? CYCLE { get; set; }

        /// <summary>
        ///登記日期_開始
        /// </summary>
        [DisplayName("週期日期_開始")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATES { get; set; }

        /// <summary>
        ///登記日期_結束
        /// </summary>
        [DisplayName("週期日期_結束")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATEE { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        public string CLASS_NO { get; set; }

        /// <summary>
        ///使用者代碼
        /// </summary>
        [DisplayName("使用者代碼")]
        public string USER_NO { get; set; }

        [DisplayName("沒配戴學生")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///是否配戴
        /// </summary>
        [DisplayName("是否配戴")]
        public bool? IS_WEAR { get; set; }

        /// <summary>
        ///未配戴原因
        /// </summary>
        [DisplayName("未配戴原因")]
        public byte? UN_WEAR_TYPE { get; set; }
    }
}