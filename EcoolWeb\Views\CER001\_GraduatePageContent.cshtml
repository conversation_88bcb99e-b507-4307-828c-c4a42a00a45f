﻿@model CER001IndexViewModel
@using ECOOL_APP.com.ecool.util;
@Html.HiddenFor(m => m.WhereStatus)
   @Html.HiddenFor(x => x.Page)
<div id="Q_Div">
    <div role="form">

        <div class="row">
            <div class="col-md-2">
                <label class="control-label">護照名稱</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereACCREDITATION_TYPE, (IEnumerable<SelectListItem>)ViewBag.AccreditationTypeItems, new { @class = "form-control input-sm", @onchange = "GetADDTITEM()" })
            </div>
            <div class="col-md-2">
                <label class="control-label">護照明細名稱</label>
            </div>
            <div class="col-md-5" id="ADDTITEM">
                <select class="selectpicker show-menu-arrow form-control" title="" id="@Html.IdFor(m=>m.WhereACCREDITATION_NAME)" name="@Html.NameFor(m=>m.WhereACCREDITATION_NAME)" onnchange="GetSUBJECTItem()">
                    <option value="">請選擇</option>
                    @if (ViewBag.AccreditationsItems != null)
                    {
                        foreach (var item in ViewBag.AccreditationsItems as IEnumerable<SelectListItem>)
                        {
                            <option data-tokens="@item.Text" value="@item.Value" @(item.Selected ? "selected" : "")>@item.Text</option>
                        }
                    }
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-md-2">
                <label class="control-label">通過主旨</label>
            </div>
            <div class="col-md-3" id="SUBJECTItem">
                <select class="selectpicker show-menu-arrow form-control" title="" id="@Html.IdFor(m=>m.WhereSUBJECT_Item)" name="@Html.NameFor(m=>m.WhereSUBJECT_Item)">
                    <option value="">請選擇</option>
                    @if (ViewBag.SUBJECTItem != null)
                    {
                        foreach (var item in ViewBag.SUBJECTItem as IEnumerable<SelectListItem>)
                        {
                            <option data-tokens="@item.Text" value="@item.Value" @(item.Selected ? "selected" : "")>@item.Text</option>
                        }
                    }
                </select>
            </div>
            <div class="col-md-2">
                <label class="control-label">通過條件</label>
            </div>
            <div class="col-md-3">
                @Html.EditorFor(m => m.WhereSearch, new { htmlAttributes = new { @class = "form-control input-sm", @placeholder = "請輸入關鍵字" } })
            </div>
        </div>
    </div>
    <div class="row" style="padding-top:10px">
        <div class="col-md-2">
            <label class="control-label">年級</label>
        </div>
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.WhereGRADE, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", @onblur = "onGrade()", @onchange = "GetCALSSITEM()" })
        </div>
        <div class="col-md-2">
            <label class="control-label">班級</label>
        </div>
        <div class="col-md-5">
            @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm" })
        </div>
    </div>
    <div class="row" style="padding-top:10px">
        <div class="col-md-2">
            <label class="control-label">學號/姓名</label>
        </div>
        <div class="col-md-3">
            @Html.EditorFor(m => m.WhereUser, new { htmlAttributes = new { @class = "form-control input-sm" } })
        </div>
        <div class="col-md-2">
            <label class="control-label">認證時間</label>
        </div>
        <div class="col-sm-5">
            @{
                var SelectGradeSemesterItems = HRMT01.GetSelectGradeSemesterItems(Model?.WhereGRADE_SEMESTERs);
            }
            <select name="@Html.NameFor(m=>m.WhereGRADE_SEMESTERs)" id="@Html.IdFor(m=>m.WhereGRADE_SEMESTERs)" class="selectpicker form-control " multiple title="全部" data-actions-box="true" data-width="97%">

                @foreach (var item in SelectGradeSemesterItems)
                {
                    <option title="@item.Text.Replace("年級","").Replace("學期","")" value="@item.Value" @(item.Selected ? "selected" : "")> @item.Text</option>
                }
            </select>
        </div>
    </div>
    <br />
    <div class="col-ml-6 text-left">
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="funAjax()" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('true')" />
        <button type="button" class="btn-yellow btn btn-sm" onclick='fn_save()'>另存新檔(Excel)</button>

    </div>
    <div class="ol-ml-6 text-right">

        <button class="btn btn-xs btn-pink  @((Model.WhereStatus == ""||Model.WhereStatus==null) ? "active" : "")" type="button" onclick="$('#WhereStatus').val('');doSearch('WhereStatus', '');">全部</button>
        <button class="btn btn-xs btn-pink   @(Model.WhereStatus == "1" ? "active" : "")" type="button" onclick="$('#WhereStatus').val('1');doSearch('WhereStatus', '1');">通過</button>
        <button class="btn btn-xs btn-pink  @(Model.WhereStatus == "0" ? "active" : "")" type="button" onclick="$('#WhereStatus').val('0');doSearch('WhereStatus', '0');">沒通過</button>

    </div>
</div>



@if (Model.Accreditations?.Count() > 0 && Model.Students?.Count() > 0)
{

    <div id="final">
        <div id="TableData">
            <table>
                <thead>
                    <tr>
                        <th class="text-nowrap text-center" rowspan="2"></th>
                        @{ string ActName = "";}
                        @foreach (var item in Model.Accreditations.GroupBy(a => new { a.ACCREDITATION_ID, a.ACCREDITATION_NAME })
    .Select(x => new { ACCREDITATION_ID = x.Key.ACCREDITATION_ID, ACCREDITATION_NAME = x.Key.ACCREDITATION_NAME }))
                        {
                            
                            var colspan = Model.Accreditations.Where(a => a.ACCREDITATION_ID == item.ACCREDITATION_ID).GroupBy(a => new { a.ACCREDITATION_ID, a.ACCREDITATION_NAME,a.CONTENT }).Count();
                            
                            <th class="text-nowrap text-center" colspan="@colspan">@item.ACCREDITATION_NAME @colspan</th>
                        }
                    </tr>
                    <tr>
                        @{
                            double StudentsCount = (Model.Students?.Count ?? 0);
                           
                        }
                   
                        @foreach (var Accreditation1 in Model.Accreditations.GroupBy(a => new { a.ACCREDITATION_ID, a.ACCREDITATION_NAME }).Select(x => new { ACCREDITATION_ID = x.Key.ACCREDITATION_ID, ACCREDITATION_NAME = x.Key.ACCREDITATION_NAME }))
                        {
                            var Accreditation = Model.Accreditations.Where(a => a.ACCREDITATION_ID == Accreditation1.ACCREDITATION_ID && a.ACCREDITATION_NAME == Accreditation1.ACCREDITATION_NAME).GroupBy(x => new { x.SUBJECT }).ToList();
                            foreach (var ITEm2 in Accreditation)
                            {
                                List<string> strList = new List<string>();
                                string str = ITEm2.Select(x => x.ITEM_NO).FirstOrDefault();
                                strList = ITEm2.Select(x => x.ITEM_NO).ToList();
                                string Desstr = "";
                                foreach (var item34 in strList)
                                {




                                    string CONTENTs = ITEm2.Select(x => x.CONTENT).FirstOrDefault();
                                    string SUBJECT = ITEm2.Select(x => x.SUBJECT).FirstOrDefault();
                                    double PassListCount = Model.PassList.Where(x => x.ACCREDITATION_ID == Accreditation1.ACCREDITATION_ID && x.ITEM_NO == item34).Count();
                                    var PassRate = (PassListCount / StudentsCount).ToString("0%");

                                    var GRADESEMESTER = Model?.cERT03s.Where(x => x.ACCREDITATION_ID == Accreditation1.ACCREDITATION_ID && x.ITEM_NO == item34)
                                         .Select(x => $"{HRMT01.ParserGrade(x.GRADE)}{HRMT01.ParserSemester(x.SEMESTER)}").ToList();

                                    var StringGradeSemesters = string.Empty;

                                    if (GRADESEMESTER?.Count() > 0)
                                    {

                                        if (GRADESEMESTER.Count() >= 12)
                                        {
                                            StringGradeSemesters = "不分年級";
                                        }
                                        else
                                        {
                                            StringGradeSemesters = String.Join("", GRADESEMESTER).Replace("年級", "").Replace("學期", "");
                                        }

                                    }
                                    if ((!string.IsNullOrWhiteSpace(StringGradeSemesters) && !string.IsNullOrWhiteSpace(Desstr) && Desstr != StringGradeSemesters) || string.IsNullOrWhiteSpace(Desstr))
                                    {




                                        <th class="text-nowrap text-center">
                                            <span title='@CONTENTs'>
                                                @(SUBJECT) (@StringGradeSemesters)
                                            </span>
                                            <br />
                                            <span style="color:blue">通過率 @(PassRate)(通過人數 @PassListCount)</span>
                                        </th>}
                                    Desstr = StringGradeSemesters;

                                }

                            }





                        }
                    </tr>
                    <tr>
                        @{var AccreditationINF = Model.Accreditations.GroupBy(a => new { a.ACCREDITATION_ID, a.ACCREDITATION_NAME, a.CONTENT }).Select(x => new { CONTENT = x.Key.CONTENT, ACCREDITATION_NAME = x.Key.ACCREDITATION_NAME });}

                        <th class="text-nowrap text-center d-block p-2 py-3">通過條件</th>
                        @foreach (var Accreditation in AccreditationINF)
                        {

                            <th class="text-nowrap text-center">
                                @if (Accreditation.CONTENT.Length > 15)
                                {
                                    string Str = "";
                                    string Strlast = "";
                                    Str = Accreditation.CONTENT.Substring(0, 15);
                                    Strlast = Accreditation.CONTENT.Substring(15, Accreditation.CONTENT.Length - 15 - 1);

                                    @Str<span id="dots"></span>


                                    <span id="more">

                                        @Strlast
                                    </span>

                                    @*<a class="btn btn-xs btn-pink" href="#">更多</a>*@


                                    <input type="button" id="myBtn" class="btn btn-sm btn-pink " value="more" onclick="myFunction('@Strlast')" />
                                }
                                else
                                {
                                    @Accreditation.CONTENT



                                }

                            </th>

                        }

                    </tr>
                </thead>
                <tbody>
                    @foreach (var Student in Model.ListData)
                    {
                        var AccreditationINFS = Model.Accreditations.GroupBy(a => new { a.ACCREDITATION_ID, a.ACCREDITATION_NAME, a.CONTENT ,a.IsText,a.ITEM_NO}).Select(x => new { ACCREDITATION_ID= x.Key.ACCREDITATION_ID,CONTENT = x.Key.CONTENT, ACCREDITATION_NAME = x.Key.ACCREDITATION_NAME , IsText =x.Key.IsText, ITEM_NO =x.Key.ITEM_NO});
                        <tr>
                            <th width="150">
                                @(Student.CLASS_NO)班       @Student.SEAT_NO  @StringHelper.LeftStringR(Student.NAME, 5)
                            </th>
                            @foreach (var Accreditation in AccreditationINFS)
                            {
                                <td class="text-center">
                                    @if (Model.PassList.Where(x => x.ACCREDITATION_ID == Accreditation.ACCREDITATION_ID && x.ITEM_NO == Accreditation.ITEM_NO && x.SCHOOL_NO == Student.SCHOOL_NO && x.USER_NO == Student.USER_NO).Any())
                                    {
                                        if (!string.IsNullOrEmpty(Accreditation.IsText) && Accreditation.IsText != "N")
                                        {
                                            string str = "";
                                            CER001PassListViewModel cre = new CER001PassListViewModel();
                                            cre = Model.PassList.Where(x => x.ACCREDITATION_ID == Accreditation.ACCREDITATION_ID && x.ITEM_NO == Accreditation.ITEM_NO && x.SCHOOL_NO == Student.SCHOOL_NO && x.USER_NO == Student.USER_NO).FirstOrDefault();
                                            if (!string.IsNullOrEmpty(cre.PersonText))
                                            {

                                                str = "(" + cre.PersonText + ")";
                                            }

                                            @:V @str
                                        }
                                        else
                                        {
                                            @:V

                                        }


                                    }
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div>
        @Html.Pager(Model.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
            .DisplayTemplate("BootstrapPagination")
                 .MaxNrOfPages(5)
                 .SetPreviousPageText("上頁")
                 .SetNextPageText("下頁")
        )
    </div>
}
else
{
    if ((Model.Accreditations?.Count() ?? 0) == 0)
    {
        <div class="text-center" style="padding:10px"><strong>此條件無任何護照資料</strong></div>
    }
    else if ((Model.Students?.Count() ?? 0) == 0)
    {
        <div class="text-center" style="padding:10px"><strong>此條件無任何學生資料</strong></div>
    }
}

<div id="dialog" title="通過條件">
    <p></p>
</div>
<script>
    $(document).ready(function () {
        $("#show").colorbox({ iframe: true, width: "30%", height: "30%", opacity: 0.82 });
    });
    $(function () {
        $('#final table').stickySort({
            threshold: { rows: 4, }, sortable: false,
        });
        $('#@Html.IdFor(m=>m.WhereGRADE_SEMESTERs)').selectpicker('refresh');
        $('#@Html.IdFor(m=>m.WhereACCREDITATION_NAME)').selectpicker('refresh');
         $('#@Html.IdFor(m=>m.WhereSUBJECT_Item)').selectpicker('refresh');
    });
    function myFunction(str) {
        $("#dialog p").html(str);
        $("#dialog").dialog();
    }
</script>
