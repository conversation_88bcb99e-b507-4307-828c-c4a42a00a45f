﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

using ECOOL_APP.EF;
using MvcPaging;

namespace EcoolWeb.ViewModels
{
    public class HRMT01QueryViewModel
    {
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 搜尋帳號
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 搜尋狀態
        /// </summary>
        public string whereStatus { get; set; }

        /// <summary>
        /// 搜尋年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 搜尋班級
        /// </summary>
        public string whereClass_No { get; set; }

        /// <summary>
        /// 一次顯示幾筆
        /// </summary>
        public string ShowPageCount { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ZZZI24QueryListViewModel> HRMT01List;

        [DisplayName("啟用老師給點上限")]
        public Nullable<bool> CHECK_CASH_LIMIT { get; set; }

        public List<HRMT01CheckBoxListViewModel> DataList { get; set; }

        public HRMT01QueryViewModel()
        {
            Page = 0;
            OrdercColumn = "GRADE";
        }
    }

    public class HRMT01CheckBoxListViewModel
    {
        public bool Chk { get; set; }

        public string SCHOOL_NO { get; set; }

        public string USER_NO { get; set; }
    }
}