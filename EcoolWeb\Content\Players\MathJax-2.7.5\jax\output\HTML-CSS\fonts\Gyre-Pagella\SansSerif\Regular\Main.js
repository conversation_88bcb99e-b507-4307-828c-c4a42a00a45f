/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-<PERSON>lla/SansSerif/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_SansSerif={directory:"SansSerif/Regular",family:"GyrePagellaMathJax_SansSerif",testString:"\u00A0\uD835\uDDA0\uD835\uDDA1\uD835\uDDA2\uD835\uDDA3\uD835\uDDA4\uD835\uDDA5\uD835\uDDA6\uD835\uDDA7\uD835\uDDA8\uD835\uDDA9\uD835\uDDAA\uD835\uDDAB\uD835\uDDAC\uD835\uDDAD",32:[0,0,250,0,0],160:[0,0,250,0,0],120224:[671,0,617,8,609],120225:[671,0,634,98,563],120226:[683,13,639,56,585],120227:[671,0,709,98,650],120228:[671,0,584,98,521],120229:[671,0,533,98,475],120230:[683,13,710,56,629],120231:[671,0,695,98,598],120232:[671,0,284,98,187],120233:[671,184,269,-52,172],120234:[671,0,597,98,619],120235:[671,0,512,98,507],120236:[671,0,795,98,698],120237:[671,0,693,98,595],120238:[683,13,719,56,663],120239:[671,0,556,98,522],120240:[683,119,719,56,663],120241:[671,0,637,98,609],120242:[683,13,583,66,528],120243:[671,0,548,-3,552],120244:[671,13,675,87,589],120245:[671,0,617,8,609],120246:[671,0,896,33,864],120247:[671,0,623,30,592],120248:[671,0,548,-2,551],120249:[671,0,625,45,580],120250:[515,13,566,60,476],120251:[699,13,585,91,531],120252:[515,13,506,55,445],120253:[699,13,585,55,495],120254:[515,13,564,55,511],120255:[699,0,317,23,336],120256:[515,191,585,55,495],120257:[699,0,587,91,503],120258:[699,0,268,94,175],120259:[699,191,257,-18,164],120260:[699,0,530,91,527],120261:[699,0,268,94,175],120262:[515,0,894,91,809],120263:[515,0,587,91,503],120264:[515,13,561,55,507],120265:[515,191,585,91,531],120266:[515,191,585,55,495],120267:[515,0,379,91,379],120268:[515,13,478,54,430],120269:[646,0,358,27,334],120270:[503,13,587,85,497],120271:[503,0,538,30,509],120272:[503,0,744,42,703],120273:[503,0,538,29,506],120274:[503,191,538,30,509],120275:[503,0,480,43,438],120276:[671,0,697,5,693],120277:[671,0,702,92,632],120278:[683,13,671,50,608],120279:[671,0,761,92,709],120280:[671,0,631,92,558],120281:[671,0,632,92,548],120282:[683,13,750,50,677],120283:[671,0,771,92,680],120284:[671,0,353,92,261],120285:[671,184,338,-56,246],120286:[671,0,703,92,734],120287:[671,0,585,92,558],120288:[671,0,914,92,822],120289:[671,0,771,92,680],120290:[683,13,775,50,725],120291:[671,0,672,92,632],120292:[683,134,775,50,725],120293:[671,0,704,92,684],120294:[683,13,662,72,589],120295:[671,0,615,5,610],120296:[671,13,749,92,657],120297:[671,0,697,5,693],120298:[671,0,999,30,968],120299:[671,0,697,19,678],120300:[671,0,650,-10,660],120301:[671,0,661,45,616],120302:[515,13,619,43,541],120303:[699,13,656,84,612],120304:[515,13,544,43,478],120305:[699,13,656,45,573],120306:[515,13,619,43,571],120307:[699,0,392,19,401],120308:[514,199,656,45,573],120309:[699,0,656,84,579],120310:[699,0,324,84,241],120311:[699,199,313,-33,230],120312:[699,0,605,84,624],120313:[699,0,324,84,241],120314:[515,0,953,83,875],120315:[515,0,656,84,579],120316:[515,13,627,43,584],120317:[515,191,656,84,612],120318:[514,191,656,45,573],120319:[515,0,452,84,449],120320:[515,13,545,52,498],120321:[646,0,434,13,411],120322:[503,13,656,78,573],120323:[503,0,589,15,575],120324:[503,0,838,35,804],120325:[503,0,583,15,568],120326:[503,199,589,12,572],120327:[503,0,533,45,485],120328:[671,0,617,-53,548],120329:[671,0,626,27,565],120330:[683,13,633,42,630],120331:[671,0,700,27,652],120332:[671,0,571,27,570],120333:[671,0,519,27,531],120334:[683,13,706,45,657],120335:[671,0,681,27,655],120336:[671,0,270,27,244],120337:[671,184,251,-164,223],120338:[671,0,585,27,652],120339:[671,0,510,27,450],120340:[671,0,781,27,755],120341:[671,0,679,27,652],120342:[683,13,716,40,676],120343:[671,0,546,27,544],120344:[683,119,716,41,676],120345:[671,0,637,27,543],120346:[683,13,574,6,543],120347:[671,0,547,43,613],120348:[671,13,665,58,647],120349:[671,0,618,78,676],120350:[671,0,896,96,928],120351:[671,0,610,-43,628],120352:[671,0,549,63,615],120353:[671,0,612,-22,630],120354:[515,13,561,41,496],120355:[699,13,579,36,532],120356:[515,13,500,46,487],120357:[699,13,575,46,580],120358:[515,13,562,46,518],120359:[699,0,311,68,437],120360:[515,191,578,32,540],120361:[699,0,580,35,513],120362:[699,0,253,35,249],120363:[699,191,238,-113,238],120364:[699,0,521,35,554],120365:[699,0,253,35,249],120366:[515,0,887,35,819],120367:[515,0,580,35,513],120368:[515,13,559,46,514],120369:[515,191,575,-3,530],120370:[515,190,579,46,542],120371:[515,0,368,35,420],120372:[515,13,471,11,451],120373:[646,0,356,64,387],120374:[503,13,580,57,536],120375:[503,0,538,72,551],120376:[503,0,744,85,746],120377:[503,0,528,-26,537],120378:[503,191,528,-25,540],120379:[503,0,469,-3,488],120380:[671,0,697,-65,615],120381:[671,0,694,21,631],120382:[683,13,664,36,655],120383:[671,0,753,21,709],120384:[671,0,618,21,605],120385:[671,0,618,21,605],120386:[683,13,746,36,704],120387:[671,0,756,21,736],120388:[671,0,339,21,318],120389:[671,184,320,-166,299],120390:[671,0,692,21,755],120391:[671,0,582,21,511],120392:[671,0,900,21,879],120393:[671,0,756,21,736],120394:[683,13,772,36,736],120395:[671,0,662,21,649],120396:[683,134,772,36,736],120397:[671,0,704,21,619],120398:[683,13,655,15,600],120399:[671,0,612,48,678],120400:[671,13,740,68,719],120401:[671,0,697,76,764],120402:[671,0,999,107,1039],120403:[671,0,684,-51,726],120404:[671,0,649,63,734],120405:[671,0,648,-26,663],120406:[515,13,615,17,552],120407:[699,13,650,31,612],120408:[515,13,538,37,527],120409:[699,13,646,36,655],120410:[515,13,618,36,580],120411:[699,0,386,50,491],120412:[514,199,649,20,616],120413:[699,0,649,31,592],120414:[699,0,310,31,322],120415:[699,199,294,-128,306],120416:[699,0,596,31,647],120417:[699,0,310,31,322],120418:[515,0,946,31,889],120419:[515,0,649,31,592],120420:[515,13,625,36,589],120421:[515,191,646,-10,608],120422:[515,191,650,36,617],120423:[515,0,442,31,494],120424:[515,13,540,10,505],120425:[646,0,431,43,462],120426:[503,13,649,60,619],120427:[503,0,589,63,616],120428:[503,0,839,87,843],120429:[503,0,574,-41,600],120430:[503,199,582,0,618],120431:[503,0,522,-9,526],120662:[671,0,697,5,693],120663:[671,0,702,92,632],120664:[671,0,585,92,558],120665:[671,0,697,5,693],120666:[671,0,631,92,558],120667:[671,0,661,45,616],120668:[671,0,771,92,680],120669:[683,13,775,50,725],120670:[671,0,353,92,261],120671:[671,0,703,92,734],120672:[671,0,697,5,693],120673:[671,0,914,92,822],120674:[671,0,771,92,680],120675:[671,0,586,98,503],120676:[683,13,775,50,725],120677:[671,0,771,92,680],120678:[671,0,672,92,632],120679:[683,13,775,50,725],120680:[671,0,631,92,558],120681:[671,0,615,5,610],120682:[671,0,650,-10,660],120683:[671,0,775,50,725],120684:[671,0,697,19,678],120685:[671,0,775,56,721],120686:[683,0,770,27,743],120687:[671,0,697,5,693],120688:[514,11,627,48,585],120689:[711,191,656,84,612],120690:[503,191,616,15,602],120691:[707,13,627,43,585],120692:[515,13,512,54,449],120693:[699,191,540,43,492],120694:[515,191,656,84,579],120695:[707,10,627,43,585],120696:[503,17,362,78,321],120697:[503,0,653,84,598],120698:[699,0,575,30,546],120699:[503,192,673,85,642],120700:[503,0,619,15,573],120701:[699,191,540,43,492],120702:[515,13,627,43,584],120703:[503,17,722,42,663],120704:[517,191,656,84,612],120705:[515,191,544,43,478],120706:[503,13,710,43,659],120707:[503,17,579,21,553],120708:[503,9,620,78,574],120709:[508,191,716,64,659],120710:[503,191,585,25,560],120711:[503,191,727,65,658],120712:[503,12,791,43,748],120713:[620,13,536,60,475],120714:[515,13,596,67,502],120715:[707,10,604,50,556],120716:[504,6,680,55,624],120717:[699,191,729,60,662],120718:[517,199,656,84,612],120719:[503,12,787,22,761],120720:[671,0,697,-65,615],120721:[671,0,694,21,631],120722:[671,0,571,21,615],120723:[671,0,697,-66,622],120724:[671,0,618,21,605],120725:[671,0,648,-26,663],120726:[671,0,756,21,736],120727:[683,13,773,40,733],120728:[671,0,339,21,318],120729:[671,0,692,21,755],120730:[671,0,697,-66,622],120731:[671,0,900,21,879],120732:[671,0,756,21,736],120733:[671,0,572,27,560],120734:[683,13,772,36,736],120735:[671,0,756,21,736],120736:[671,0,662,21,649],120737:[683,13,773,40,733],120738:[671,0,618,21,605],120739:[671,0,612,48,678],120740:[671,0,649,63,734],120741:[671,0,774,43,730],120742:[671,0,684,-51,726],120743:[671,0,770,78,787],120744:[683,0,764,-45,726],120745:[661,10,697,-2,686],120746:[514,11,622,38,627],120747:[711,191,648,-11,593],120748:[503,191,614,66,673],120749:[707,13,625,16,572],120750:[515,13,507,27,471],120751:[699,191,530,32,567],120752:[515,191,649,50,610],120753:[707,10,624,28,596],120754:[503,17,362,42,286],120755:[503,0,645,31,614],120756:[699,0,575,-44,472],120757:[503,192,665,-27,600],120758:[503,0,622,68,594],120759:[699,191,530,26,567],120760:[515,13,625,36,589],120761:[503,17,720,73,716],120762:[517,191,646,9,630],120763:[515,191,538,55,541],120764:[503,13,704,37,707],120765:[503,17,576,52,605],120766:[503,9,616,54,586],120767:[508,191,714,76,684],120768:[503,191,571,-48,619],120769:[503,191,721,79,725],120770:[503,12,789,33,754],120771:[620,13,494,29,466],120772:[515,13,590,59,548],120773:[707,10,600,31,571],120774:[504,6,672,16,654],120775:[699,191,728,52,668],120776:[516,199,649,43,633],120777:[503,12,783,33,811],120802:[683,13,586,66,520],120803:[671,0,593,110,501],120804:[683,0,590,73,490],120805:[683,13,588,76,508],120806:[671,0,583,49,527],120807:[671,13,589,77,502],120808:[683,13,586,70,523],120809:[671,0,589,82,504],120810:[683,13,586,68,518],120811:[683,13,586,63,516],120812:[683,13,635,48,588],120813:[671,0,644,113,576],120814:[683,0,642,79,556],120815:[683,13,640,67,561],120816:[671,0,634,45,589],120817:[671,13,640,77,571],120818:[682,13,637,62,584],120819:[671,0,640,67,561],120820:[683,13,638,61,577],120821:[682,13,637,52,574]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_SansSerif"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Regular/Main.js"]);
