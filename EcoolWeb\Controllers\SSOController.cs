﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;
using System.Web;
using System.Web.Mvc;
using System.Text;
using System.Security.Cryptography;
using System.Net;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System.Xml;
using System.Xml.Linq;
using log4net;
using EcoolWeb.Service;
using UAParser;

namespace EcoolWeb.Controllers
{
 
    public class SSOController : Controller
    {
        public static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        public ActionResult Index()
        {
            LoginApiController LoginApi = new LoginApiController();
         string SSORandomNO=   Guid.NewGuid().ToString("N");
            UserProfileHelper.SetSSONoCookieData(SSORandomNO);
            string strGetSSONoCookie = "";
            if (UserProfileHelper.GetACESSNoCookie() != null)
            {
                strGetSSONoCookie = UserProfileHelper.GetACESSNoCookie().ToString();
                logger.Info("ACESS NO getCOokie" + strGetSSONoCookie);
            }
            else
            {

                logger.Info("SSO getCOokie 是空的");
            }
            string url = string.Format(@"{0}?response_type={1}&client_id={2}&redirect_uri={3}&scope={4}",
                    SSOConfig.Keys.AUTH_ENDPOINT,
                    "code",
                    SSOConfig.Keys.CLIENT_ID,
                    HttpUtility.UrlEncode(SSOConfig.Keys.REDIRECT_URI),
                    SSOConfig.Keys.ALLOWED_SCOPES
                    );

            return Redirect(url);
        }
        public ActionResult SSOPointLoginPage(string str,string SCHOOL_NO ,string Barcode)
        {
            log4net.ILog logger = LogManager.GetLogger("SSOPointLoginPage");
            //  string SSORandomNO = Guid.NewGuid().ToString("N");
            str = str + "&SCHOOL_NO=" + SCHOOL_NO + "&SCHOOL_NO1=" + SCHOOL_NO + "&Barcode=" + Barcode;
            UserProfileHelper.SetPointCookieData(str);
            string strGetATMNoCookie = "";
            logger.Info("SSORandomNO");


            string url = string.Format(@"{0}?response_type={1}&client_id={2}&redirect_uri={3}&scope={4}",
                SSOConfig.Keys.AUTH_ENDPOINT,
                "code",
                SSOConfig.Keys.CLIENT_ID,
                HttpUtility.UrlEncode(SSOConfig.Keys.REDIRECT_URI),
                SSOConfig.Keys.ALLOWED_SCOPES
                );
            //LogHelper.LogToTxt("url:" + JsonConvert.SerializeObject(url));

            logger.Info("url：" + JsonConvert.SerializeObject(url));
            return Redirect(url);
        }

        public ActionResult SSOPointLoginPage1(string str, string SCHOOL_NO, string Barcode)
        {
            log4net.ILog logger = LogManager.GetLogger("SSOPointLoginPage");
            //  string SSORandomNO = Guid.NewGuid().ToString("N");
            str = str + "&SCHOOL_NO=" + SCHOOL_NO + "&SCHOOL_NO1=" + SCHOOL_NO + "&Barcode=" + Barcode;
            UserProfileHelper.SetPointCookieData(str);
            string strGetATMNoCookie = "";
            logger.Info("SSORandomNO");


            string url = string.Format(@"{0}?response_type={1}&client_id={2}&redirect_uri={3}&scope={4}",
                SSOConfig.Keys.AUTH_ENDPOINT,
                "code",
                SSOConfig.Keys.CLIENT_ID,
                HttpUtility.UrlEncode(SSOConfig.Keys.REDIRECT_URI),
                SSOConfig.Keys.ALLOWED_SCOPES
                );
            //LogHelper.LogToTxt("url:" + JsonConvert.SerializeObject(url));
            return Redirect(url);

            //     return RedirectToAction("SSOBACKTEST", "SSO", new { SCHOOL_NO= SCHOOL_NO, SCHOOL_NO1= SCHOOL_NO, Barcode= Barcode });
        }
        /// <summary>
        /// 台北SSO 單一登入開始頁
        /// </summary>
        /// <returns></returns>
        public ActionResult SSOATMLoginPage()
        {
            log4net.ILog logger = LogManager.GetLogger("SSOATMLoginPage");
            // LoginApiController LoginApi = new LoginApiController();
            string SSORandomNO = Guid.NewGuid().ToString("N");
            UserProfileHelper.SetATMNoCookieData(SSORandomNO);
            string strGetATMNoCookie = "";
            logger.Info("SSORandomNO");


            string url = string.Format(@"{0}?response_type={1}&client_id={2}&redirect_uri={3}&scope={4}",
                SSOConfig.Keys.AUTH_ENDPOINT,
                "code",
                SSOConfig.Keys.CLIENT_ID,
                HttpUtility.UrlEncode(SSOConfig.Keys.REDIRECT_URI),
                SSOConfig.Keys.ALLOWED_SCOPES
                );
            //LogHelper.LogToTxt("url:" + JsonConvert.SerializeObject(url));
            
            logger.Info("url：" + JsonConvert.SerializeObject(url));
            return Redirect(url);
        }
        /// <summary>
        /// 台北SSO 單一登入開始頁
        /// </summary>
        /// <returns></returns>
        public ActionResult SSOLoginPage()
        {
            string url = string.Format(@"{0}?response_type={1}&client_id={2}&redirect_uri={3}&scope={4}",
                SSOConfig.Keys.AUTH_ENDPOINT,
                "code",
                SSOConfig.Keys.CLIENT_ID,
                HttpUtility.UrlEncode(SSOConfig.Keys.REDIRECT_URI),
                SSOConfig.Keys.ALLOWED_SCOPES
                );
            //LogHelper.LogToTxt("url:" + JsonConvert.SerializeObject(url));
            log4net.ILog logger = LogManager.GetLogger("SSOLoginPage");
            logger.Info("url：" + JsonConvert.SerializeObject(url));
            return Redirect(url);
        }

        public ActionResult SSOBACKTEST(string str, string SCHOOL_NO, string Barcode)
        {
            BackReturnObj reObj = new BackReturnObj();
            //str = str + "&SCHOOL_NO=" + SCHOOL_NO + "&SCHOOL_NO1=" + SCHOOL_NO + "&Barcode=" + Barcode;
           // UserProfileHelper.SetPointCookieData(str);
            reObj.code = "def502009f2bf6d841cf6b05ddf1d635222a042aeef99734ddfbe9cae736e5bb39b6bbba91542c596992e5164634320cf53c218e77c56af30e8b79cb1aff04e617c2afef192705471f9ba7e6e9b1ded5f735226fde9f43a63fa4d4334fb1fe0dcc1f7f300de57e2b9d2575aa0e969422a37fb45161e94e8aa4f8adac378acf91165d00a6da94b33b6a588880fc0eee75650413ed92562194eefaf81a3e7cf23af53e57108a31e9fb1bd8229da841eb71e3c44286ec6a48ac5805046a21c68a6b530ec8f186fb2e021f4b520630fb5a57e389d316e3b126de101b81a943a05cbb6f2a5e8dcd8ea9c03642564da8dbd8fdcd10b9b8748e923aa13d37a7649fd5503ee19c0d3d482021abc08bcf8315dd12cb7b2a02dd0b7f7e596941ca292beaba0fa03951ee24ec34f52432e1375dedc018b66c0de5aa045eda190869e7236e15e6172048d9422b7d9d5493babb6483cbd501c91599aa52abfe537c389caef56eb203cccdfcaf1cb6b2e0b477b25a6290c75434ff97f9b2e97fdec734dd7d69d92eac1a8347376a01e8931b9b170e744e161b48be3196cc1fe9918f";
            if (Request.Cookies["MyLoginwebsite"] != null)
            {
                // 如果Cookie有登入紀錄，將之轉記到Session
                //LogHelper.LogToTxt("MyLoginwebsite" + (string)Request.Cookies["MyLoginwebsite"].Value);
                // 讓cookie失效(逾期 = 將到期日設定為去年此時)
            }
            else
            {
                //LogHelper.LogToTxt("MyLoginwebsite");
            }
            return SSOBACK(reObj);
        }

        /// <summary>
        /// 台北SSO登入返回的連結
        /// </summary>
        /// <param name="code">code</param>
        /// <returns></returns>
        [AcceptVerbs(HttpVerbs.Get | HttpVerbs.Post)]
        public ActionResult SSOBACK(BackReturnObj reObj)
        {
            Session["SSOLOGIN"] = "SSOLOGIN";
            if (reObj != null)
            {
                string reObstr = "";
                reObstr = JsonConvert.SerializeObject(reObj);
                ////LogHelper.LogToTxt($"" + reObstr);
            }
            if (string.IsNullOrEmpty(reObj.code))
            {
                //LogHelper.LogToTxt($"SSOBACK返回失敗, code是空的");
                return RedirectToAction("PortalIndex", "HOME");
            }

            var service = new SSO_TaipeiService(reObj.code);
            try
            {
                var userAgent = HttpContext.Request.UserAgent;
                var uaParser = Parser.GetDefault();
                ClientInfo c = uaParser.Parse(userAgent);
                string Msg = "";
                string accessToken = "";
                //LogHelper.LogToTxt($@"IP:{service.GetIPAddress()}。Msg:{Msg}。UrlReferrer:{Request.UrlReferrer?.ToString()}。Url:{Request.Url?.ToString()},OS:{c.OS.Family.ToString()},UserAgent:{c.UserAgent.Family.ToString()},Device:{c.Device.Family.ToString()}");
                List<HRMT01> theUser = service.AccessData(ref accessToken);

                if (theUser == null)
                {
                    //LogHelper.LogToTxt("theUser空的");
                    TempData["StatusMessageCenter"] = "很抱歉，此帳號無法登入。";
                    //LogHelper.LogToTxt("TokenObj:" + JsonConvert.SerializeObject(service.TokenObj));
                    //LogHelper.LogToTxt("UserObj:" + JsonConvert.SerializeObject(service.UserObj));
                    //LogHelper.LogToTxt("ProfileObj:" + JsonConvert.SerializeObject(service.ProfileObj));
                    return RedirectToAction("COOCGuestIndex", "HOME", new { Message = "該帳號不在系統中，請聯絡相關人員!" });
                }
                else if (theUser.FirstOrDefault().USER_TYPE == UserType.Parents)
                {

                    UserProfileHelper.SetACESSNoCookieData(accessToken);

                    logger.Info("JsonConvert");
                    try
                    {
                        TempData["list"] = theUser.ToList();
                    }
                    catch (Exception e)
                    {
                        logger.Info(e.InnerException);
                    }
                    string strGetSSONoCookie = "";
                    string strGetATMNoCookie = "";
                    try
                    {
                        if (UserProfileHelper.GetSSONoCookie() != null)
                        {
                            strGetSSONoCookie = UserProfileHelper.GetSSONoCookie().ToString();
                            logger.Info("SSO getCOokie" + strGetSSONoCookie);
                        }
                        else {

                            logger.Info("SSO getCOokie 是空的");
                        }
                    }

                    catch (Exception e) {



                    }
                    try
                    {
                        if (UserProfileHelper.GetATMNoCookie() != null)
                        {
                            strGetATMNoCookie = UserProfileHelper.GetATMNoCookie().ToString();
                            logger.Info("SSO GetATM" + strGetATMNoCookie);
                        }
                        else
                        {

                          //  logger.Info("SSO GetATM 是空的");
                        }
                    }

                    catch (Exception e)
                    {



                    }

                    if (!string.IsNullOrEmpty(strGetSSONoCookie)) {
                        logger.Info("access_token"+ accessToken);
                        foreach (var UserInfo in theUser) {
                            logger.Info("UserInfoIDNO" + UserInfo.IDNO);
                            HRMT01 hRMT01temp = new HRMT01();
                            hRMT01temp = db.HRMT01.Where(x => x.IDNO == UserInfo.IDNO).FirstOrDefault();
                            hRMT01temp.uuid = UserInfo.uuid;
                            hRMT01temp.accessToken = accessToken;
                        }
                        db.SaveChanges();


                        return RedirectToAction("PortalIndex", "Home",new { school_type="國小" , school_code = theUser.FirstOrDefault().SCHOOL_NO, role_type = theUser.FirstOrDefault().USER_TYPE, access_token = accessToken });
                    }
                    else {

                        return RedirectToAction("ParsentSSO", "Home");

                    }

                }
                else
                {
                    //LogHelper.LogToTxt("TokenObj:" + JsonConvert.SerializeObject(service.TokenObj));
                    //LogHelper.LogToTxt("UserObj:" + JsonConvert.SerializeObject(service.UserObj));
                    //LogHelper.LogToTxt("ProfileObj:" + JsonConvert.SerializeObject(service.ProfileObj));
                    HRMT01 rMT01= theUser.FirstOrDefault();
                    UserProfile LoginUser = UserProfile.FillUserProfile(rMT01);
                    UserProfileHelper.Set(LoginUser);
                    LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");
                    string strGetSSONoCookie = "";
                    string strGetATMNoCookie = "";
                    string strGetPointCookie = "";
                    try
                    {
                        if (UserProfileHelper.GetSSONoCookie() != null)
                        {
                            strGetSSONoCookie = UserProfileHelper.GetSSONoCookie().ToString();
                            logger.Info("SSO getCOokie" + strGetSSONoCookie);
                        }
                        else if (UserProfileHelper.GetPointNoCookie() != null) {
                            strGetPointCookie = UserProfileHelper.GetPointNoCookie().ToString();
                            logger.Info("strGetPointCookie1" + "https://ecc.tp.edu.tw" + strGetPointCookie.Replace('\"', ' ').Trim());
                            strGetPointCookie = strGetPointCookie;
                            //return RedirectToAction("ParsentSSO", "Home");

                           return Redirect("https://ecc.tp.edu.tw" + strGetPointCookie.Replace('\"', ' ').Trim());

                         //   return Redirect("http://localhost:5321/" + strGetPointCookie.Replace('\"', ' ').Trim());

                        }
                        else
                        {

                            logger.Info("SSO getCOokie 是空的");
                        }
                    }

                    catch (Exception e)
                    {



                    }
                    try
                    {
                        if (UserProfileHelper.GetATMNoCookie() != null)
                        {
                            strGetATMNoCookie = UserProfileHelper.GetATMNoCookie().ToString();
                            logger.Info("SSO GetATM" + strGetATMNoCookie);
                        }
                        else if (UserProfileHelper.GetPointNoCookie() != null)
                        {
                            strGetPointCookie = UserProfileHelper.GetPointNoCookie().ToString();

                            logger.Info("strGetPointCookie2" + strGetPointCookie);
                        }
                        else
                        {

                            logger.Info("SSO GetATM 是空的");
                        }
                    }

                    catch (Exception e)
                    {



                    }
                    if (!string.IsNullOrEmpty(strGetSSONoCookie))
                    {
                        logger.Info("access_token" + accessToken);
                        UserProfileHelper.SetACESSNoCookieData(accessToken);
                        foreach (var UserInfo in theUser)
                        {
                            logger.Info("UserInfoIDNO" + UserInfo.IDNO);

                            HRMT01 hRMT01temp = new HRMT01();
                            hRMT01temp = db.HRMT01.Where(x => x.IDNO == UserInfo.IDNO).FirstOrDefault();
                            hRMT01temp.uuid = UserInfo.uuid;
                            hRMT01temp.accessToken = accessToken;
                        }
                        db.SaveChanges();


                        return RedirectToAction("PortalIndex", "Home", new { school_type = "國小", school_code = theUser.FirstOrDefault().SCHOOL_NO, role_type = theUser.FirstOrDefault().USER_TYPE, access_token = accessToken });

                        //return RedirectToAction("PortalIndex", "Home", new { school_type = "國小", school_code = theUser.FirstOrDefault().SCHOOL_NO, role_type = theUser.FirstOrDefault().USER_TYPE, access_token = accessToken });
                    }
                    else if (!string.IsNullOrEmpty(strGetATMNoCookie)) {
                        if (rMT01.USER_TYPE == UserType.Student)
                        {
                            return RedirectToAction("StudentIndex", "MobileHome", new { school = rMT01.SCHOOL_NO });
                        }

                        }
                    else
                    {


                        if (Request.UrlReferrer?.ToString().Contains("redirectLogin") == true)
                        {
                            if (rMT01.USER_TYPE == UserType.Admin || rMT01.USER_TYPE == UserType.Teacher)
                            {
                                return RedirectToAction("TeacherIndex", "MobileHome", new { school = rMT01.SCHOOL_NO });
                            }
                            else if (rMT01.USER_TYPE == UserType.Student)
                            {
                                return RedirectToAction("StudentIndex", "MobileHome", new { school = rMT01.SCHOOL_NO });
                            }
                            else if (rMT01.USER_TYPE == UserType.Parents)
                            {
                                return RedirectToAction("ParentLadp", "HOME", new { school = rMT01.SCHOOL_NO });

                            }
                        }
                        else
                        {
                            if (rMT01.USER_TYPE == UserType.Admin || rMT01.USER_TYPE == UserType.Teacher)
                            {
                                return RedirectToAction("TeacherIndex", "HOME", new { school = rMT01.SCHOOL_NO });
                            }
                            else if (rMT01.USER_TYPE == UserType.Student)
                            {
                                return RedirectToAction("StudentLadp", "HOME", new { school = rMT01.SCHOOL_NO });
                            }
                            else if (rMT01.USER_TYPE == UserType.Parents)
                            {
                                return RedirectToAction("ParentLadp", "HOME", new { school = rMT01.SCHOOL_NO });

                            }
                        }
                    }
                    //return RedirectToAction("TeacherIndex", "HOME", new { school = theUser.SCHOOL_NO });
                }
            }
            catch (Exception ex)
            {
                //LogHelper.LogToTxt("TokenObj:" + JsonConvert.SerializeObject(service.TokenObj));
                //LogHelper.LogToTxt("UserObj:" + JsonConvert.SerializeObject(service.UserObj));
                //LogHelper.LogToTxt("ProfileObj:" + JsonConvert.SerializeObject(service.ProfileObj));

                //LogHelper.LogToTxt(ex);

                TempData["StatusMessageCenter"] = "很抱歉，目前無法登入，請稍後再試。";
                return RedirectToAction("Index");
            }

            return RedirectToAction("PortalIndex", "HOME");
        }

        /// <summary>
        /// 員工愛上網 => 暫不使用
        /// </summary>
        /// <returns></returns>
        public ActionResult EmployeeLoveInternetLogin()
        {
            // 方法一：憑證檔案的目錄與位置
            string Certificate = Server.MapPath("~/certificate/GRCA2.cer");
            // Load the certificate into an X509Certificate object.
            X509Certificate cert = new X509Certificate(Certificate);

            // 產生字串表示的詳細資訊形式
            string resultsTrue = cert.ToString(true);

            var handler = new WebRequestHandler();
            handler.ClientCertificates.Add(new X509Certificate(Certificate));

            using (var client = new HttpClient(handler))
            {
            }
            return View();
        }

        public class BackReturnObj
        {
            public string code { get; set; }
        }
    }
}