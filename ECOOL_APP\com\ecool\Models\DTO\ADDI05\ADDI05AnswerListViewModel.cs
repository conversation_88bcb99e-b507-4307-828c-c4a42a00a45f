﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDI05AnswerListViewModel
    {
        /// <summary>
        /// 題目名稱
        /// </summary>
        public string DIALOG_NAME { get; set; }
        /// <summary>
        /// 題目NO
        /// </summary>
        public string DIALOG_ID { get; set; }

        public string SearchContents { get; set; }

        public string OrderByName { get; set; }

        public int Page { get; set; }

        public int PageSize { get; set; }
        public string OrderRank { get; set; }

        public IPagedList<ECOOL_APP.com.ecool.Models.entity.uADDT13> uADDT13;

        public ADDI05AnswerListViewModel()
        {
            Page = 1;
            PageSize = 100;
        }
    }


    public class ADDI05EachAnswerListViewModel
    {
        public string Where_USERNO { get; set; }
        public string Where_SCHOOLNO { get; set; }
        /// <summary>
        /// 題目名稱
        /// </summary>
        public string DIALOG_NAME { get; set; }
        /// <summary>
        /// 題目NO
        /// </summary>
        public string DIALOG_ID { get; set; }

        public string OrderByName { get; set; }

        public List<ECOOL_APP.com.ecool.Models.entity.uADDT13_Each> uADDT13_Each;

        public ADDI05EachAnswerListViewModel()
        {
        }
    }
}
