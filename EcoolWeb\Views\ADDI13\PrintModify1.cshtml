﻿@model RollCallBarcodeCashViewModel
@using ECOOL_APP
@{
    Layout = "~/Views/Shared/_LayoutEmpty1.cshtml";
    var itemCount = 0;
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
}
@section head{
    <link href="@Url.Content("~/Content/styles/PrintModify.min.css?v="+DateNowStr)" rel="stylesheet" />
}

<ul class="list-unstyled print-modify-list print-modify-list-sixEqual">
    @if (Model != null && Model.Details != null && Model.Details.Count() > 0)

    {
        foreach (var item in Model.Details.OrderBy(x => x.CASH))
        {
            @:<li class="col-6">
                string str = "";
                str = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ADDI13/Index1?ROLL_CALL_ID=" + item.ROLL_CALL_ID + "&SCHOOL_NO1=" + item.SCHOOL_NO + "&Barcode=" + item.BarCode;

                <div class="d-flex align-items-center">
                    <div class="col-4 p-1">
                        <img src="@Url.Action("Cre","Barcode", new {Value=str })" class="QRCode" />
                    </div>
                    <p class="col-8 print-modify-title">
                        恭喜你<span class="print-modify-icon"></span><br />
                        因為 @Model.Main.ROLL_CALL_NAME 活動<br />

                        @if (!Model.Main.IS_SHOW)
                        {
                            <span class="text-light">獲得酷幣 @item.CASH 點</span>


                        }
                        else
                        {
                            <span class="text-light">獲得酷幣神秘點數</span>

                        }

                    </p>
                </div>
                <strong class="d-block text-center text-date text-info">請於 @string.Format("{0:yyyy-MM-dd HH:mm}", Model.Main.ROLL_CALL_DATEE) 前完成點數取得</strong>
                <div class="txt">
                    你可以用以下的方式完成點數取得：
                    <ol>
                        <li>
                            「先登入自己的E酷幣帳號」利用酷幣秘書 取得點數 功能裡面打上代碼。
                            <span class="text-info text-number d-block text-center">@item.BarCode</span>
                        </li>
                        <li>
                            用手機、平板掃描QRCODE，輸入帳號密碼完成點數取得。<br />
                            發行老師：@item.Cre_Person，第 @item.ROLL_NUM 張。
                        </li>
                    </ol>
                </div>
            @:</li>
            itemCount++;
        }
    }
</ul>

@section scripts{
    <script>
        //代碼變色
        let numberList = document.querySelectorAll('.text-number');
        numberList.forEach(function (e) {
            let str = e.textContent;

            if (str.length == 9) {
                let number1 = str.substring(0, 3);
                console.log(number1);
                let number2 = str.substring(3, 6);
                let number3 = str.substring(6, 9);
                e.innerHTML = `${number1}<span class="text-pink">${number2}</span>${number3}`;

            }


        });
    </script>
}