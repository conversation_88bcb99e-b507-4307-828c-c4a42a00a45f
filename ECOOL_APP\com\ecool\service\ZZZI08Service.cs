﻿using com.ecool.service;
using com.ecool.sqlConnection;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.DTO.ZZZI08;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web.Mvc;
using Dapper;
using ECOOL_APP.EF;
using System.Web;
using System.IO;
using System.Text.RegularExpressions;
using System.Drawing.Imaging;
using System.Drawing;

namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI08Service
    {
        public string ErrorMsg;
        private static string SysPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\\ADDT11_FILE\\";
        private int ErrorInt = 0;

        public static string GetSysPath()
        {
            return SysPath;
        }

        #region 取得清單

        public List<uADDT11> GetListData(ZZZI08ListViewModel Data, UserProfile user, int pageSize, ref int Count)
        {
            List<uADDT11> list_data = new List<uADDT11>();

            uADDT11 ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("  SELECT A.DIALOG_ID ,A.SCHOOL_NO,B.SHORT_NAME,A.SYEAR,A.SEMESTER,A.DIALOG_NAME,A.DIALOG_EXPRESS,A.DIALOG_SDATE,A.DIALOG_EDATE ");
                sb.Append("  ,A.USER_NO,A.NAME,A.SNAME,A.JOB_NAME,A.CHG_PERSON,A.CHG_DATE,A.STATUS,A.DIALOG_TYPE ");
                sb.Append("  FROM ADDT11 A  (NOLOCK) ");
                sb.Append("  LEFT OUTER JOIN BDMT01 B  (NOLOCK)  ON A.SCHOOL_NO=B.SCHOOL_NO ");
                sb.Append("  WHERE 1=1 ");

                if (Data.Q_COPY_YN == "Y")
                {
                    sb.AppendFormat(" AND A.COPY_YN = '{0}' ", Data.Q_COPY_YN);

                    if (Data.Q_SCHOOL_NO != SharedGlobal.ALL)
                    {
                        sb.AppendFormat(" AND A.SCHOOL_NO = '{0}' ", Data.Q_SCHOOL_NO);
                    }
                }
                else
                {
                    if (HRMT24_ENUM.QOutSchoolList.Contains((byte)user?.ROLE_TYPE))
                    {
                        sb.AppendFormat(" AND (A.SCHOOL_NO = '{0}' or A.SCHOOL_NO = '{1}') ", Data.Q_SCHOOL_NO, SharedGlobal.ALL);
                    }
                    else
                    {
                        sb.AppendFormat(" AND A.SCHOOL_NO = '{0}' ", Data.Q_SCHOOL_NO);
                    }
                }

                //簡易查詢
                if (Data.SearchType == 1)
                {
                    if (string.IsNullOrWhiteSpace(Data.SearchContents) == false)
                    {
                        sb.Append(" AND (");

                        sb.AppendFormat("  A.DIALOG_NAME LIKE '%{0}%' ", Data.SearchContents);
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.DIALOG_EXPRESS LIKE '%{0}%' ", Data.SearchContents);
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.NAME LIKE '%{0}%' ", Data.SearchContents);
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.SNAME LIKE '%{0}%' ", Data.SearchContents);

                        int intDate;
                        if (int.TryParse(Data.SearchContents, out intDate))
                        {
                            sb.Append(" OR ");
                            sb.AppendFormat("  A.SYEAR LIKE '%{0}%' ", Data.SearchContents);
                        }

                        byte byteDate;
                        if (byte.TryParse(Data.SearchContents, out byteDate))
                        {
                            sb.Append(" OR ");
                            sb.AppendFormat("  A.SEMESTER LIKE '%{0}%' ", Data.SearchContents);
                        }

                        DateTime dtDate;
                        if (DateTime.TryParse(Data.SearchContents, out dtDate))
                        {
                            sb.Append(" OR ");
                            sb.AppendFormat("  A.DIALOG_SDATE = '{0}' ", Data.SearchContents);
                            sb.Append(" OR ");
                            sb.AppendFormat("  A.DIALOG_EDATE = '{0}' ", Data.SearchContents);
                        }

                        sb.Append(" )");
                    }
                }
                if (user.ROLE_LEVEL != (decimal)0.00 && user.USER_TYPE != "A" && user.ROLE_LEVEL != (decimal)4.00 && user.ROLE_LEVEL != (decimal)1.00 && user.ROLE_LEVEL != (decimal)2.00) {


                    sb.AppendFormat(" AND A.USER_NO= '{0}' ",user.USER_NO);
                }
                    //else if (Data.SearchType ==2) //進階查詢
                    //{
                    //    if (Data.Q_DIALOG_ID != string.Empty)
                    // {
                    //        sb.AppendFormat(" and  A.DIALOG_ID like '%{0}%' ", Data.Q_DIALOG_ID);
                    // }

                    //    if (Data.Q_SYEAR != null)
                    //    {
                    //        sb.AppendFormat(" and  A.SYEAR = {0} ", Data.Q_SYEAR);
                    //    }

                    //    if (Data.Q_SEMESTER != null)
                    //    {
                    //        sb.AppendFormat(" and  A.SEMESTER = {0} ", Data.Q_SEMESTER);
                    //    }

                    //    if (Data.Q_DIALOG_NAME != string.Empty)
                    //    {
                    //        sb.AppendFormat(" and  A.DIALOG_NAME like '%{0}%' ", Data.Q_DIALOG_NAME);
                    //    }

                    //    if (Data.Q_DIALOG_SDATE != null)
                    //    {
                    //        sb.AppendFormat(" and  A.DIALOG_SDATE >= '{0}' ", Data.Q_DIALOG_SDATE);
                    //    }

                    //    if (Data.Q_DIALOG_EDATE != null)
                    //    {
                    //        sb.AppendFormat(" and  A.DIALOG_EDATE <= '{0}' ", Data.Q_DIALOG_EDATE);
                    //    }

                    //    if (Data.Q_Name_Contents != null)
                    //    {
                    //        sb.Append(" and ( ");

                    //        sb.AppendFormat("  A.USER_NO LIKE '%{0}%' ", Data.Q_Name_Contents);

                    //        sb.Append(" OR ");

                    //        sb.AppendFormat("  A.NAME LIKE '%{0}%' ", Data.Q_Name_Contents);

                    //        sb.Append(" OR ");

                    //        sb.AppendFormat("  A.SNAME LIKE '%{0}%' ", Data.Q_Name_Contents);

                    //        sb.Append(" ) ");
                    //    }

                    //    if (Data.Q_STATUS != null)
                    //    {
                    //        string STATUSString = Data.Q_STATUS.Replace(",", "','");
                    //        sb.AppendFormat(" and  A.STATUS in ('{0}') ", STATUSString);
                    //    }

                    //}

                    string OrderBy = "A.DIALOG_EDATE DESC";

                if (string.IsNullOrWhiteSpace(Data.OrderByName) == false)
                {
                    OrderBy = "A." + Data.OrderByName + " " + Data.SyntaxName;
                }

                string ThisError = "";

                dt = new sqlConnection().executeQueryBSqlDataReaderListPage(Data.Page, pageSize, sb.ToString(), sb.ToString(), OrderBy, ref Count, ref ThisError);
                if (dt != null)
                {
                    foreach (DataRow dr in dt.Rows)
                    {
                        ReturnData = new uADDT11();

                        ReturnData.DIALOG_ID = (dr["DIALOG_ID"] == DBNull.Value ? "" : (string)dr["DIALOG_ID"]);
                        ReturnData.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                        ReturnData.SHORT_NAME = (dr["SHORT_NAME"] == DBNull.Value ? "" : (string)dr["SHORT_NAME"]);
                        ReturnData.SYEAR = (dr["SYEAR"] == DBNull.Value ? (int?)null : (int)dr["SYEAR"]);
                        ReturnData.SEMESTER = (dr["SEMESTER"] == DBNull.Value ? (byte?)null : (byte)dr["SEMESTER"]);
                        ReturnData.DIALOG_NAME = (dr["DIALOG_NAME"] == DBNull.Value ? "" : (string)dr["DIALOG_NAME"]);
                        ReturnData.DIALOG_EXPRESS = (dr["DIALOG_EXPRESS"] == DBNull.Value ? "" : (string)dr["DIALOG_EXPRESS"]);
                        ReturnData.DIALOG_SDATE = (dr["DIALOG_SDATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["DIALOG_SDATE"]);
                        ReturnData.DIALOG_EDATE = (dr["DIALOG_EDATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["DIALOG_EDATE"]);
                        ReturnData.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                        ReturnData.NAME = (dr["NAME"] == DBNull.Value ? "" : (string)dr["NAME"]);
                        ReturnData.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                        ReturnData.JOB_NAME = (dr["JOB_NAME"] == DBNull.Value ? "" : (string)dr["JOB_NAME"]);
                        ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                        ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);
                        ReturnData.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);
                        ReturnData.STATUS_NAME = uADDT11.GetSTATUS_NAME(ReturnData.STATUS);
                        ReturnData.DIALOG_TYPE = (dr["DIALOG_TYPE"] == DBNull.Value ? (byte?)null : (byte?)dr["DIALOG_TYPE"]);
                        list_data.Add(ReturnData);
                    }
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        #endregion 取得清單

        #region 取得.明細資料

        public ZZZI08EditViewModel GetGetDetailsData(string DIALOG_ID)
        {
            ZZZI08EditViewModel ReturnData = new ZZZI08EditViewModel();

            StringBuilder sb = new StringBuilder();
            try
            {
                sb.Append("  SELECT A.DIALOG_ID ,A.SCHOOL_NO,A.SYEAR,A.SEMESTER,A.DIALOG_NAME,A.DIALOG_EXPRESS,A.DIALOG_SDATE,A.DIALOG_EDATE ");
                sb.Append("  ,A.USER_NO,A.NAME,A.SNAME,A.JOB_NAME,A.CHG_PERSON,A.CHG_DATE,A.STATUS,A.ANSWER_COUNT,A.COPY_YN,A.ANSWER_PERSON_YN,A.CASH,A.GetCASH,A.RANDOM,A.DIALOG_TYPE ");
                sb.Append("  FROM ADDT11 A  (NOLOCK) ");
                sb.Append("  WHERE 1=1 ");
                sb.AppendFormat(" AND A.DIALOG_ID ='{0}' ", DIALOG_ID);

                var conn = new sqlConnection().getConnection4System();
                ReturnData.uADDT11 = conn.Query<uADDT11>(sb.ToString()).FirstOrDefault();
                ReturnData.uADDT11.STATUS_NAME = uADDT11.GetSTATUS_NAME(ReturnData.uADDT11.STATUS);

                sb.Clear();

                sb.Append("  Select B.DIALOG_ID,B.Q_NUM,B.Q_TYPE,B.TRUE_ANS,B.Q_TEXT");
                sb.Append("  ,B.Q_ANS1, B.Q_ANS2, B.Q_ANS3,B.Q_ANS4");
                sb.Append("  from ADDT12 B (nolock) ");
                sb.Append("  Where 1=1 ");
                sb.AppendFormat(" AND B.DIALOG_ID= '{0}' ", DIALOG_ID);
                sb.Append("  ORDER BY B.Q_TYPE,B.Q_NUM");

                var D_item = conn.Query<ZZZI08ADDT12ViewModel>(sb.ToString()).ToList();
                ReturnData.truefalseAns = D_item.Where(a => a.Q_TYPE == (int)uADDT12.enumQ_TYPE_NAME.是非).ToList();
                ReturnData.multipleschoiceAns = D_item.Where(a => a.Q_TYPE == (int)uADDT12.enumQ_TYPE_NAME.選擇).ToList();

                sb.Clear();

                sb.Append("  Select B.DIALOG_ID,B.FILE_NAME");
                sb.Append("  from ADDT11_FILE B (nolock) ");
                sb.Append("  Where 1=1 ");
                sb.AppendFormat(" AND B.DIALOG_ID= '{0}' ", DIALOG_ID);
                ReturnData.FILE = conn.Query<ZZZI08ADDT11_FILEViewModel>(sb.ToString()).ToList();
                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return ReturnData;
        }

        #endregion 取得.明細資料

        #region 新增資料

        public void CreateDate(ZZZI08EditViewModel Date, IEnumerable<HttpPostedFileBase> files)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlTransaction transaction = conn.BeginTransaction();

                try
                {
                    Date.uADDT11.DIALOG_ID = this.GetNewDIALOG_ID(Date.uADDT11.SCHOOL_NO, conn, transaction);

                    this.INSERT_NTO_ADDT11(conn, transaction, Date.uADDT11);

                    this.DoAddADDT12(conn, transaction, Date);
                  
                    if (files != null)
                    {
                        UpLoadFile(Date, files, @".*\.(doc|docx|ppt|pptx)");
                    }

                    if (!string.IsNullOrWhiteSpace(ErrorMsg))
                    {
                        ErrorInt = ErrorInt + 1;
                    }
                    else
                    {
                        if (Date.FILE != null)
                        {
                            this.FILEAddDate(Date.FILE, Date.uADDT11.DIALOG_ID, conn, transaction);
                        }

                        REFT01Service.UpdateStatus(Date.uADDT11.CHG_PERSON, "ADDT11", Date.REF_KEY, Date.uADDT11.DIALOG_ID, conn, transaction);
                    }

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        Date.uADDT11.DIALOG_ID = null;
                        transaction.Rollback();
                        ErrorMsg = "新增資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    Date.uADDT11.DIALOG_ID = null;
                    transaction.Rollback();
                    ErrorMsg = "新增資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 新增資料

        #region 修改資料

        public void UpDate(ZZZI08EditViewModel Date, IEnumerable<HttpPostedFileBase> files)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlTransaction transaction = conn.BeginTransaction();
                try
                {
                    this.UPDATE_SET_ADDT11(conn, transaction, Date.uADDT11);
                    this.DELETE_ADDT12(conn, transaction, Date.uADDT11.DIALOG_ID);
                    this.DoAddADDT12(conn, transaction, Date);

                    UpLoadFile(Date, files,@".*\.(doc|docx|ppt|pptx)");
                    if (!string.IsNullOrWhiteSpace(ErrorMsg))
                    {
                        ErrorInt = ErrorInt + 1;
                    }
                    else
                    {
                        if (Date.FILE != null)
                        {
                            this.FILEAddDate(Date.FILE, Date.uADDT11.DIALOG_ID, conn, transaction);
                        }

                        REFT01Service.UpdateStatus(Date.uADDT11.CHG_PERSON, "ADDT11", Date.REF_KEY, Date.uADDT11.DIALOG_ID, conn, transaction);
                    }

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "修改資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "修改資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 修改資料

        #region 刪除資料

        public void DelDate(string DIALOG_ID)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlTransaction transaction = conn.BeginTransaction();
                try
                {
                    this.DELETE_ADDT11(conn, transaction, DIALOG_ID);
                    this.DELETE_ADDT11_FILE(conn, transaction, DIALOG_ID);
                    this.DELETE_ADDT12(conn, transaction, DIALOG_ID);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "刪除資料處理;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "刪除資料處理;\r\n" + ex.Message;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 刪除資料

        #region 更新截止日期

        public void UpdateDIALOG_EDATE(ZZZI08EditViewModel Date)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlTransaction transaction = conn.BeginTransaction();
                try
                {
                    this.UPDATE_SET_ADDT11_DIALOG_EDATE(conn, transaction, Date.uADDT11);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "更新截止日期失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "更新截止日期失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 更新截止日期

        #region 更新STATUS

        public void UpdateSTATUS(ZZZI08EditViewModel Date)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlTransaction transaction = conn.BeginTransaction();
                try
                {
                    this.UPDATE_SET_ADDT11_STATUS(conn, transaction, Date.uADDT11);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "更新STATUS失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "更新STATUS失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 更新STATUS

        #region 取得新的ID

        /// <summary>
        /// 取得得新的ID
        /// </summary>
        /// <returns></returns>
        private string GetNewDIALOG_ID(string SCHOOL_NO, SqlConnection conn, SqlTransaction transaction)
        {
            try
            {
                string NUM = string.Empty;
                //ID = SCHOOL_NO+ 年 月 日+3 碼流水號
                string sSQL;

                sSQL = " SELECT RIGHT('00'+CONVERT(nvarchar(3),ISNULL(MAX(RIGHT(A.DIALOG_ID,3)),0)+1),3) AS NUM ";
                sSQL = sSQL + " FROM ADDT11 A ";
                sSQL = sSQL + "  WHERE 1=1 ";
                sSQL = sSQL + "  and CONVERT(varchar(10),A.CRE_DATE,112)='" + DateTime.Now.ToString("yyyyMMdd") + "'  ";
                sSQL = sSQL + "  and A.SCHOOL_NO=@SCHOOL_NO";
                NUM = conn.Query<string>(sSQL, new { SCHOOL_NO = SCHOOL_NO }, transaction).FirstOrDefault();

                if (string.IsNullOrWhiteSpace(NUM))
                {
                    NUM = "001";
                }

                string ID = string.Empty;

                if (string.IsNullOrWhiteSpace(SCHOOL_NO) == false)
                {
                    ID = SCHOOL_NO + "_" + DateTime.Now.ToString("yyyyMMdd") + NUM;
                }
                else
                {
                    ID = DateTime.Now.ToString("yyyyMMdd") + NUM;
                }

                return ID;
            }
            catch (Exception ex)
            {
                ErrorMsg = " 取得新的DIALOG_ID 失敗;\r\n" + ex.Message;
                return null;
            }
        }

        #endregion 取得新的ID

        public bool CheckFile(string DIALOG_ID, string FILE_NAME)
        {
            bool ReturnBool = false;

            try
            {
                using (SqlConnection conn = new sqlConnection().getConnection4Query())
                {
                    string sSQL;

                    sSQL = " SELECT Count(*) ";
                    sSQL = sSQL + " FROM ADDT11_FILE A  (nolock)";
                    sSQL = sSQL + "  WHERE 1=1 ";
                    sSQL = sSQL + "  and DIALOG_ID=@DIALOG_ID and FILE_NAME=@FILE_NAME ";
                    ReturnBool = conn.Query<bool>(sSQL, new { DIALOG_ID = DIALOG_ID, FILE_NAME = FILE_NAME }).Any();
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return ReturnBool;
        }

        #region 上傳檔案資料

        public void FILEAddDate(List<ZZZI08ADDT11_FILEViewModel> CreateDate, string DIALOG_ID, SqlConnection conn, SqlTransaction transaction)
        {
            string sSQL = string.Empty;

            foreach (var Temp in CreateDate)
            {
                sSQL = @"Delete ADDT11_FILE Where DIALOG_ID=@DIALOG_ID and FILE_NAME=@FILE_NAME ";

                try
                {
                    conn.Execute(sSQL, new { DIALOG_ID = DIALOG_ID, FILE_NAME = Temp.FILE_NAME }, transaction);
                }
                catch (Exception ex)
                {
                    ErrorInt++;
                    ErrorMsg = "Delete ADDT11_FILE 失敗;" + ex.Message;
                }

                sSQL = " INSERT INTO ADDT11_FILE ";
                sSQL = sSQL + " (DIALOG_ID,FILE_NAME) ";
                sSQL = sSQL + " VALUES (@DIALOG_ID,@FILE_NAME) ";

                try
                {
                    conn.Execute(sSQL, new { DIALOG_ID = DIALOG_ID, FILE_NAME = Temp.FILE_NAME }, transaction);
                }
                catch (Exception ex)
                {
                    ErrorInt++;
                    ErrorMsg = "INSERT INTO ADDT11_FILE 失敗;" + ex.Message;
                }
            }
        }

        #endregion 上傳檔案資料

        #region 上傳檔案資料刪除

        public void Del_FILE(string DIALOG_ID, string FILE_NAME)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlTransaction transaction = conn.BeginTransaction();
                try
                {
                    string sSQL = "Delete ADDT11_FILE Where DIALOG_ID=@DIALOG_ID and FILE_NAME=@FILE_NAME ";
                    conn.Execute(sSQL, new { DIALOG_ID = DIALOG_ID, FILE_NAME = FILE_NAME }, transaction);
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "Delete ADDT11_FILE;" + ex.Message;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 上傳檔案資料刪除

        #region INSERT INTO ADDT11

        private void INSERT_NTO_ADDT11(SqlConnection conn, SqlTransaction transaction, uADDT11 Date)
        {
            Date.DIALOG_NAME = HtmlUtility.SanitizeHtml(Date.DIALOG_NAME);

            string sSQL = @"INSERT INTO ADDT11 ( DIALOG_ID,SCHOOL_NO,SYEAR,SEMESTER,DIALOG_NAME
                                              ,DIALOG_EXPRESS,DIALOG_SDATE,DIALOG_EDATE,USER_NO
                                               ,NAME,SNAME,JOB_NAME,CHG_PERSON,CHG_DATE,CRE_PERSON
                                               ,CRE_DATE,STATUS,ANSWER_COUNT,COPY_YN,ANSWER_PERSON_YN,RANDOM,CASH,GetCASH,DIALOG_TYPE)
                          VALUES (@DIALOG_ID,@SCHOOL_NO,@SYEAR,@SEMESTER,@DIALOG_NAME
                            ,@DIALOG_EXPRESS,@DIALOG_SDATE,@DIALOG_EDATE,@USER_NO,@NAME
                            ,@SNAME,@JOB_NAME,@CHG_PERSON,@CHG_DATE,@CRE_PERSON
                           ,@CRE_DATE,@STATUS,@ANSWER_COUNT,@COPY_YN,@ANSWER_PERSON_YN,@RANDOM,@CASH,@GetCASH,@DIALOG_TYPE)";

            try
            {
                conn.Execute(sSQL, Date, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "INSERT_NTO_ADDT11 失敗;\r\n" + ex.Message;
            }
        }

        #endregion INSERT INTO ADDT11

        #region UPDATE SET ADDT11

        private void UPDATE_SET_ADDT11(SqlConnection conn, SqlTransaction transaction, uADDT11 Item)
        {
            Item.DIALOG_NAME = HtmlUtility.SanitizeHtml(Item.DIALOG_NAME);

            string sSQL = @" UPDATE ADDT11 set
             SYEAR=@SYEAR,SEMESTER=@SEMESTER,DIALOG_NAME=@DIALOG_NAME,DIALOG_EXPRESS=@DIALOG_EXPRESS
            ,DIALOG_SDATE=@DIALOG_SDATE,DIALOG_EDATE=@DIALOG_EDATE
            ,CHG_PERSON=@CHG_PERSON,CHG_DATE=@CHG_DATE,STATUS=@STATUS
            ,ANSWER_COUNT=@ANSWER_COUNT,COPY_YN=@COPY_YN,ANSWER_PERSON_YN=@ANSWER_PERSON_YN
            ,RANDOM=@RANDOM,CASH=@CASH,GetCASH=@GetCASH
            ,DIALOG_TYPE=@DIALOG_TYPE
             Where DIALOG_ID=@DIALOG_ID
            ";

            try
            {
                conn.Execute(sSQL, Item, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_ADDT11 失敗;\r\n" + ex.Message;
            }
        }

        #endregion UPDATE SET ADDT11

        #region UPDATE ADDT11 SET DIALOG_EDATE

        private void UPDATE_SET_ADDT11_DIALOG_EDATE(SqlConnection conn, SqlTransaction transaction, uADDT11 Item)
        {
            string sSQL = @" UPDATE ADDT11 set
             DIALOG_EDATE=@DIALOG_EDATE
            ,CHG_PERSON=@CHG_PERSON,CHG_DATE=@CHG_DATE
             Where DIALOG_ID=@DIALOG_ID
            ";

            try
            {
                conn.Execute(sSQL, Item, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_ADDT11_DIALOG_EDATE 失敗;\r\n" + ex.Message;
            }
        }

        #endregion UPDATE ADDT11 SET DIALOG_EDATE

        #region UPDATE_SET_ADDT11_STATUS

        private void UPDATE_SET_ADDT11_STATUS(SqlConnection conn, SqlTransaction transaction, uADDT11 Item)
        {
            string sSQL = @" UPDATE ADDT11 set
             STATUS=@STATUS
            ,CHG_PERSON=@CHG_PERSON,CHG_DATE=@CHG_DATE
             Where DIALOG_ID=@DIALOG_ID
            ";

            try
            {
                conn.Execute(sSQL, Item, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_ADDT11_DIALOG_EDATE 失敗;\r\n" + ex.Message;
            }
        }

        #endregion UPDATE_SET_ADDT11_STATUS

        #region DELETE ADDT11

        private void DELETE_ADDT11(SqlConnection conn, SqlTransaction transaction, string DIALOG_ID)
        {
            string sSQL = @" DELETE ADDT11 Where DIALOG_ID=@DIALOG_ID";

            try
            {
                conn.Execute(sSQL, new { DIALOG_ID = DIALOG_ID }, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "DELETE_ADDT11 失敗;\r\n" + ex.Message;
            }
        }

        #endregion DELETE ADDT11

        #region DELETE ADDT11_FILE

        private void DELETE_ADDT11_FILE(SqlConnection conn, SqlTransaction transaction, string DIALOG_ID)
        {
            string sSQL = @" DELETE ADDT11_FILE Where DIALOG_ID=@DIALOG_ID";

            try
            {
                conn.Execute(sSQL, new { DIALOG_ID = DIALOG_ID }, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "DELETE_ADDT11_FILE 失敗;\r\n" + ex.Message;
            }
        }

        #endregion DELETE ADDT11_FILE

        #region INSERT INTO ADDT12

        private void INSERT_NTO_ADDT12(SqlConnection conn, SqlTransaction transaction, uADDT12 Date)
        {
            string sSQL = @" INSERT INTO ADDT12 (DIALOG_ID,Q_NUM,Q_TYPE,TRUE_ANS,Q_TEXT,Q_ANS1,Q_ANS2,Q_ANS3,Q_ANS4)
            VALUES (@DIALOG_ID,@Q_NUM,@Q_TYPE,@TRUE_ANS,@Q_TEXT,@Q_ANS1,@Q_ANS2,@Q_ANS3,@Q_ANS4)";

            try
            {
                conn.Execute(sSQL, Date, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "INSERT_INTO_ADDT12 失敗;\r\n" + ex.Message;
            }
        }

        #endregion INSERT INTO ADDT12

        #region DELETE ADDT12

        private void DELETE_ADDT12(SqlConnection conn, SqlTransaction transaction, string DIALOG_ID)
        {
            string sSQL = @" DELETE ADDT12 Where DIALOG_ID=@DIALOG_ID";

            try
            {
                conn.Execute(sSQL, new { DIALOG_ID = DIALOG_ID }, transaction);
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "DELETE_ADDT12 失敗;\r\n" + ex.Message;
            }
        }

        #endregion DELETE ADDT12

        #region 新增 ADDT12

        /// <summary>
        /// 新增 ADDT12
        /// </summary>
        /// <param name="conn"></param>
        /// <param name="transaction"></param>
        /// <param name="Date"></param>
        private void DoAddADDT12(SqlConnection conn, SqlTransaction transaction, ZZZI08EditViewModel Date)
        {
            int Q_NUM = 0;

            if (Date.truefalseAns != null)
            {
                foreach (var item in Date.truefalseAns) //問答題
                {
                    uADDT12 D_ITEM = new uADDT12();
                    D_ITEM.DIALOG_ID = Date.uADDT11.DIALOG_ID;
                    D_ITEM.Q_NUM = Q_NUM + 1;
                    D_ITEM.Q_TEXT = HtmlUtility.SanitizeHtml(Date.truefalseAns[Q_NUM].Q_TEXT);
                    D_ITEM.Q_TYPE = 1;
                    D_ITEM.TRUE_ANS = Date.truefalseAns[Q_NUM].TRUE_ANS;

                    this.INSERT_NTO_ADDT12(conn, transaction, D_ITEM);

                    Q_NUM++;
                }
            }

            int M_Q_NUM = 0;

            if (Date.multipleschoiceAns != null)
            {
                foreach (var item in Date.multipleschoiceAns) //選擇題
                {
                    uADDT12 D_ITEM = new uADDT12();
                    D_ITEM.DIALOG_ID = Date.uADDT11.DIALOG_ID;
                    D_ITEM.Q_NUM = Q_NUM + 1;
                    D_ITEM.Q_TEXT = HtmlUtility.SanitizeHtml(Date.multipleschoiceAns[M_Q_NUM].Q_TEXT);
                    D_ITEM.Q_TYPE = 2;
                    D_ITEM.TRUE_ANS = Date.multipleschoiceAns[M_Q_NUM].TRUE_ANS;

                    D_ITEM.Q_ANS1 = HtmlUtility.SanitizeHtml(Date.multipleschoiceAns[M_Q_NUM].Q_ANS1);
                    D_ITEM.Q_ANS2 = HtmlUtility.SanitizeHtml(Date.multipleschoiceAns[M_Q_NUM].Q_ANS2);
                    D_ITEM.Q_ANS3 = HtmlUtility.SanitizeHtml(Date.multipleschoiceAns[M_Q_NUM].Q_ANS3);
                    D_ITEM.Q_ANS4 = HtmlUtility.SanitizeHtml(Date.multipleschoiceAns[M_Q_NUM].Q_ANS4);

                    this.INSERT_NTO_ADDT12(conn, transaction, D_ITEM);

                    M_Q_NUM++;
                    Q_NUM++;
                }
            }
        }

        #endregion 新增 ADDT12

        #region 上傳處理

        /// <summary>
        /// 上傳處理
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="files"></param>
        private void UpLoadFile(ZZZI08EditViewModel Data, IEnumerable<HttpPostedFileBase> files,string regexCodeInput)
        {
            if (ErrorMsg == null || ErrorMsg == string.Empty)
            {
                List<ZZZI08ADDT11_FILEViewModel> ListDa = new List<ZZZI08ADDT11_FILEViewModel>();

                int Item = 0;

                string tempPath = SysPath + Data.uADDT11.DIALOG_ID;
                if (files != null)
                {
                    if (Directory.Exists(tempPath) == false)
                    {
                        Directory.CreateDirectory(tempPath);
                    }
                }

                foreach (var file in files)
                {
                    if (file != null && file.ContentLength > 0)
                    {
                        Item = Item + 1;

                        string fileName = Path.GetFileName(file.FileName);
                        string UpLoadFile = tempPath + "\\" + fileName;

                        

                        try
                        {
                            Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                            if (regexCode.IsMatch(fileName.ToLower())) //判斷是否圖片
                            {
                                file.SaveAs(UpLoadFile);
                                System.Drawing.Image image = System.Drawing.Image.FromFile(UpLoadFile);
                                //必須使用絕對路徑

                                ImageFormat thisFormat = image.RawFormat;
                                //取得影像的格式

                                int fixWidth = 0;
                                int fixHeight = 0;

                                //第一種縮圖用
                                int maxPx = Convert.ToInt16(800);    //宣告一個最大值，demo是把該值寫到web.config裡

                                if (image.Width > maxPx || image.Height > maxPx)  //如果圖片的寬大於最大值或高大於最大值就往下執行
                                {
                                    if (image.Width >= image.Height)  //圖片的寬大於圖片的高

                                    {
                                        fixWidth = maxPx;
                                        //設定修改後的圖寬
                                        fixHeight = Convert.ToInt32((Convert.ToDouble(fixWidth) / Convert.ToDouble(image.Width)) * Convert.ToDouble(image.Height));
                                        //設定修改後的圖高
                                    }
                                    else
                                    {
                                        fixHeight = maxPx;
                                        //設定修改後的圖高
                                        fixWidth = Convert.ToInt32((Convert.ToDouble(fixHeight) / Convert.ToDouble(image.Height)) * Convert.ToDouble(image.Width));
                                        //設定修改後的圖寬
                                    }
                                }
                                else
                                //圖片沒有超過設定值，不執行縮圖
                                {
                                    fixHeight = image.Height;
                                    fixWidth = image.Width;
                                }

                                Bitmap imageOutput = new Bitmap(image, fixWidth, fixHeight);

                                image.Dispose();
                                //釋放掉圖檔

                                //砍掉原圖檔
                                System.IO.File.Delete(UpLoadFile);

                                //輸出一個新圖(就是修改過的圖)
                                //string fixSaveName = string.Concat(fileName+"_sys",".jpg");
                                //副檔名不應該這樣給，但因為此範例沒有讀取檔案的部份所以demo就直接給啦

                                imageOutput.Save(tempPath + "\\" + fileName, thisFormat);
                                //將修改過的圖存於設定的位子

                                imageOutput.Dispose();
                                //釋放記憶體
                                ListDa.Add(new ZZZI08ADDT11_FILEViewModel()
                                {
                                    DIALOG_ID = Data.uADDT11.DIALOG_ID,
                                    FILE_NAME = fileName
                                });
                            }
                            else if (!string.IsNullOrEmpty(regexCodeInput))

                            {
                                file.SaveAs(UpLoadFile);
                                Regex regexInput = new Regex(regexCodeInput);
                                if (regexInput.IsMatch(fileName.ToLower()))
                                {
                                    
                                    if (file.ContentLength / 1024 > (1024 * 10)) // 20MB
                                    {

                                        ErrorMsg += fileName + "上傳檔案不能超過10MB";
                                      
                                        
                                    }
                                    else { 
                                    ListDa.Add(new ZZZI08ADDT11_FILEViewModel()
                                    {
                                        DIALOG_ID = Data.uADDT11.DIALOG_ID,
                                        FILE_NAME = fileName
                                    });
                                    }

                                }
                                else
                                {
                                    ErrorMsg += fileName + "檔案格式錯誤，上傳失敗 ";

                                }

                            }
                            else {

                                ListDa.Add(new ZZZI08ADDT11_FILEViewModel()
                                {
                                    DIALOG_ID = Data.uADDT11.DIALOG_ID,
                                    FILE_NAME = fileName
                                });


                            }
                         
                        }
                        catch (Exception ex)
                        {
                            ErrorMsg += fileName + "上傳失敗" + ex + "r\n";
                        }
                    }
                }

                Data.FILE = ListDa;
            }
        }

        #endregion 上傳處理
    }
}