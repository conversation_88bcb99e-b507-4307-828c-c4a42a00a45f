﻿using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.LogicCenter.ADDI09
{
    public class LuckCollectPointRepository
    {
        ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        public List<AWAT01> Insert_Result_AWAT01 { get; set; }
        public List<AWAT01> Update_Result_AWAT01 { get; set; }
        public List<AWAT01_LOG> Insert_Result_AWAT01_LOG { get; set; }

        public LuckCollectPointRepository() : base()
        {
            Init();
        }
     

        private void Init()
        {
            Insert_Result_AWAT01 = new List<AWAT01>();
            Update_Result_AWAT01 = new List<AWAT01>();
            Insert_Result_AWAT01_LOG = new List<AWAT01_LOG>();
        }

        /// <summary>
        /// 批次給好運集點
        /// </summary>
        /// <param name="USER_KEY">給點人 null:系統給</param>
        /// <param name="ADD_CASH_WORKHARD">好運集點</param>
        /// <param name="SchoolNO"></param>
        /// <param name="UserNO"></param>
        public void GiveWORKHARD_ToStu_InMemory(string USER_KEY, int ADD_CASH_WORKHARD, string SchoolNO, string UserNO,
            string SourceType, string SourceNo, string LogDesc)
        {
            short? ADD_CASH_ALL = 0;
            short? ADD_CASH_AVAILABLE = 0;

            //AWAT01
            var find1 = db.AWAT01.Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO);
            AWAT01 aw1 = find1.FirstOrDefault();

            var EntityAWAT01 = ((IObjectContextAdapter)db).ObjectContext.ObjectStateManager
                .GetObjectStateEntries(System.Data.Entity.EntityState.Added)
                .Where(a => a.Entity is AWAT01).Select(a => (AWAT01)a.Entity).Where(u => u.SCHOOL_NO == SchoolNO && u.USER_NO == UserNO)
                .FirstOrDefault();

            if (EntityAWAT01 != null)
            {
                aw1 = EntityAWAT01;
            }

            if (aw1 == null)
            {
                aw1 = db.AWAT01.Create();
                aw1.SCHOOL_NO = SchoolNO;
                aw1.USER_NO = UserNO;
                aw1.CASH_ALL = ADD_CASH_ALL;
                aw1.CASH_AVAILABLE = ADD_CASH_AVAILABLE;
                aw1.CASH_WORKHARD = ADD_CASH_WORKHARD;

                Insert_Result_AWAT01.Add(aw1);
            }
            else
            {
                AWAT01 aWAT01Single = new AWAT01();

                if (aw1.CASH_ALL.HasValue == false) aw1.CASH_ALL = 0;
                if (aw1.CASH_AVAILABLE.HasValue == false) aw1.CASH_AVAILABLE = 0;
                aw1.CASH_WORKHARD = aw1.CASH_WORKHARD.Value + ADD_CASH_WORKHARD;
                Update_Result_AWAT01.Add(aw1);


            }

            //AWAT01_LOG
            AWAT01_LOG aw1_log = db.AWAT01_LOG.Create();
            aw1_log.SCHOOL_NO = aw1.SCHOOL_NO;
            aw1_log.USER_NO = aw1.USER_NO;
            aw1_log.LOG_TIME = DateTime.Now;
            aw1_log.SOURCE_TYPE = SourceType;
            aw1_log.SOURCE_NO = (SourceNo ?? "");
            aw1_log.CASH_IN = 0;
            aw1_log.LOG_DESC = LogDesc;
            aw1_log.ADD_CASH_ALL = ADD_CASH_ALL;
            aw1_log.ADD_CASH_AVAILABLE = ADD_CASH_AVAILABLE;
            aw1_log.ADD_CASH_WORKHARD = Convert.ToInt16(ADD_CASH_WORKHARD);
            aw1_log.AWAT01_CASH_ALL = aw1.CASH_ALL;
            aw1_log.AWAT01_CASH_AVAILABLE = aw1.CASH_AVAILABLE;
            aw1_log.AWAT01_CASH_WORKHARD = aw1.CASH_WORKHARD;
            aw1_log.LOG_PERSON = USER_KEY ?? "系統給好運集點";

            Insert_Result_AWAT01_LOG.Add(aw1_log);
        }

        /// <summary>
        /// 完成批次新增
        /// </summary>
        public void GiveBatchInsert()
        {

            if (Insert_Result_AWAT01.Count() > 0) {
                db.AWAT01.AddRange(Insert_Result_AWAT01);
                //InsertAll(Insert_Result_AWAT01);

            }
            if (Insert_Result_AWAT01_LOG.Count() > 0)
            {
                db.AWAT01_LOG.AddRange(Insert_Result_AWAT01_LOG);

            }
            if (Update_Result_AWAT01.Count() > 0) {
                foreach (var item in Update_Result_AWAT01) {
                    var find1 = db.AWAT01.Where(u => u.SCHOOL_NO == item.SCHOOL_NO && u.USER_NO == item.USER_NO).FirstOrDefault();

                    find1.CASH_WORKHARD = item.CASH_WORKHARD;
                }
              

            }
            db.SaveChanges();
            //   InsertAll(Insert_Result_AWAT01_LOG);

            //UpdateAll(Update_Result_AWAT01, c=> c.CASH_WORKHARD );
        }
    }
}
