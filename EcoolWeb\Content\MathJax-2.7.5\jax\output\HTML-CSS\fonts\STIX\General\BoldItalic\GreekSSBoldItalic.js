/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/GreekSSBoldItalic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{120720:[690,0,690,25,665],120721:[676,0,706,60,671],120722:[676,0,602,60,705],120723:[690,0,720,40,680],120724:[676,0,683,60,708],120725:[676,0,707,25,769],120726:[676,0,748,60,783],120727:[691,19,847,90,822],120728:[676,0,435,50,505],120729:[676,0,712,60,796],120730:[690,0,686,20,646],120731:[676,0,933,60,981],120732:[676,18,744,60,792],120733:[676,0,690,47,737],120734:[692,18,849,90,824],120735:[676,0,745,60,783],120736:[676,0,581,60,675],120737:[691,19,847,90,822],120738:[676,0,696,21,748],120739:[676,0,641,87,715],120740:[691,0,671,91,799],120741:[676,0,835,72,835],120742:[676,0,740,20,833],120743:[691,0,791,125,901],120744:[691,0,816,47,816],120745:[664,30,780,120,760],120746:[473,14,678,47,703],120747:[692,205,552,-12,581],120748:[473,204,525,84,571],120749:[692,14,507,30,547],120750:[473,14,504,45,508],120751:[692,205,480,49,539],120752:[473,205,532,38,525],120753:[692,14,560,65,553],120754:[462,14,325,56,302],120755:[473,0,537,38,582],120756:[692,14,574,18,540],120757:[462,205,594,-12,569],120758:[473,14,525,41,565],120759:[692,205,481,43,525],120760:[473,14,543,45,515],120761:[462,14,632,45,656],120762:[473,205,560,-33,536],120763:[473,205,517,52,554],120764:[462,14,614,45,639],120765:[462,14,523,42,547],120766:[473,14,550,61,526],120767:[473,205,683,55,659],120768:[473,205,575,-80,626],120769:[473,205,703,75,751],120770:[461,14,756,64,732],120771:[691,14,548,45,539],120772:[473,14,468,45,470],120773:[692,14,579,54,579],120774:[473,10,646,-10,665],120775:[692,205,678,48,654],120776:[473,205,544,38,520],120777:[462,14,889,40,912]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/GreekSSBoldItalic.js");
