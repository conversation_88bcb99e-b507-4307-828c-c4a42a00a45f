﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using ECOOL_APP.EF;
using MvcPaging;

namespace EcoolWeb.Models
{
    public class QAI01IndexViewModel
    {

        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }


        /// <summary>
        /// 狀態
        /// </summary>
        public string whereWritingStatus { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設20筆
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// BackAction 網頁用 ，前一頁網址
        /// </summary>
        public string BackAction { get; set; }

        /// <summary>
        /// 清除條件 
        /// </summary>
        public bool doClear { get; set; }


        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<QAT01> QAT01List;


        public QAI01IndexViewModel()
        {
            PageSize = 20;
            BackAction ="Index";
        }

        public void ClearWhere()
        {
            this.whereKeyword = null;
            this.whereWritingStatus = null;
            this.doClear = false;

            this.OrdercColumn = null;
        }
    }
}