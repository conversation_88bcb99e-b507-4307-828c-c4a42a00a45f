﻿@model ZZZI27DetailsViewModel
@using ECOOL_APP.com.ecool.service

@using (Html.BeginCollectionItem("Details_List"))
{
    var Index = Html.GetIndex("Details_List");



    @Html.HiddenFor(m => m.BRE_NO)
    @Html.HiddenFor(m => m.DATA_CODE)
    @Html.HiddenFor(m => m.DATA_TYPE)
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.ITEM_NO)
    @Html.HiddenFor(m => m.ADD_MODE)
    @Html.HiddenFor(m => m.TXT_MODE)
    @Html.HiddenFor(m => m.VAL_MODE)
    @Html.HiddenFor(m => m.TXT_SCHOOL_SET_YN)
    @Html.HiddenFor(m => m.VAL_SCHOOL_SET_YN)


    @*<div class="td" style="@(Model.TXT_SCHOOL_SET_YN == SharedGlobal.Y ? "" : "display:none")">
            @Html.EditorFor(m => m.CONTENT_TXT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Model.DATA_CODE == "TeacherIndex"?"請輸入要跳出公告的內容":"", @Type = @"" + BDMT02_ENUM.Mode.GetInputType(Model.TXT_MODE) + "" } })

        </div>*@

<div class="tr" id="Tr@(Index)">

    <div class="td">
        @Html.RadioButtonFor(model => model.CONTENT_VAL, true, new { @id = "AutherYN" })
        @Html.Label("學生可以上傳圖片")<br />
        @Html.RadioButtonFor(model => model.CONTENT_VAL, false, new { @id = "AutherYN" })
        @Html.Label("不開放學生可以上傳圖片")
    </div>
   
    </div>
    @*<div class="form-group">

            <div class="col-md-9  col-sm-6" style="padding-top:7px">
                @Html.RadioButtonFor(model => model.CONTENT_VAL, false, new { @id = "AutherYN" })
                @Html.Label("學生可以上傳圖片")
                @Html.RadioButtonFor(model => model.CONTENT_VAL, true, new { @id = "AutherYN" })
                @Html.Label("不開放學生可以上傳圖片")
            </div>
        </div>*@

    }
