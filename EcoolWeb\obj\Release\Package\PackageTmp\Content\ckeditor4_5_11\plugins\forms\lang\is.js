﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'is', {
	button: {
		title: 'Eigindi hnapps',
		text: 'Texti',
		type: '<PERSON>er<PERSON>',
		typeBtn: 'H<PERSON><PERSON>',
		typeSbm: 'Staðfesta',
		typeRst: 'Hreinsa'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Eigindi markreits',
		radioTitle: 'Eigindi valhnapps',
		value: 'Gil<PERSON>',
		selected: 'Valið',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Eigindi innsláttarforms',
		menu: 'Eigindi innsláttarforms',
		action: 'Aðgerð',
		method: 'Aðferð',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'Eigindi falins svæðis',
		name: 'Nafn',
		value: 'Gil<PERSON>'
	},
	select: {
		title: 'Eigindi lista',
		selectInfo: 'Upplýsingar',
		opAvail: '<PERSON><PERSON><PERSON>',
		value: '<PERSON><PERSON>',
		size: '<PERSON><PERSON><PERSON><PERSON>',
		lines: 'línur',
		chkMulti: '<PERSON><PERSON><PERSON> fleiri kosti',
		required: 'Required', // MISSING
		opText: 'Texti',
		opValue: 'Gildi',
		btnAdd: 'Bæta við',
		btnModify: 'Breyta',
		btnUp: 'Upp',
		btnDown: 'Niður',
		btnSetValue: 'Merkja sem valið',
		btnDelete: 'Eyða'
	},
	textarea: {
		title: 'Eigindi textasvæðis',
		cols: 'Dálkar',
		rows: 'Línur'
	},
	textfield: {
		title: 'Eigindi textareits',
		name: 'Nafn',
		value: 'Gildi',
		charWidth: 'Breidd (leturtákn)',
		maxChars: 'Hámarksfjöldi leturtákna',
		required: 'Required', // MISSING
		type: 'Gerð',
		typeText: 'Texti',
		typePass: 'Lykilorð',
		typeEmail: 'Email', // MISSING
		typeSearch: 'Search', // MISSING
		typeTel: 'Telephone Number', // MISSING
		typeUrl: 'Vefslóð'
	}
} );
