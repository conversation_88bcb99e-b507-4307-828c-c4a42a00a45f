﻿
@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string ImageUrl = Url.Content("~/Content/mp4/") + "RealtimePoint_" + Model.CASH + "P.mp4";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        ImageUrl = Url.Content("~/Content/mp4/") + "RealtimePoint_" + Model.CASH + "P.gif";
    }


}
@Html.Partial("_Title_Secondary")

<div style="text-align:center">
    <a href='@Url.Action("EditOne",new { IsRandom = "True", IsRandomHighPoint="True"})' role="button" class="btn btn-sm btn-sys">
        下一筆
    </a>
    <br />
    @if (AppMode)
    {
        <img src='@ImageUrl' />
    }
    else
    {
        <video id="SlotPlayer" style="width:400px ;height: auto" autoplay="autoplay">
            <source src='@ImageUrl' type="video/mp4">
        </video>
    }

</div>


<script type="text/javascript">
    window.history.forward(1);
</script>

