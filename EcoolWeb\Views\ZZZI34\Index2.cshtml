﻿
@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_ArtGalleryMenu", new { NowAction = "MenuIndex" });
}
@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })


    <img src="~/Content/img/web-bar2-revise-40.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ZZZI26">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
                  <div class="form-group text-center">
                      @{


                          <div class="col-md-4">
                              @Html.ActionLink("單一學生作品(ZIP)", "UPLOAD1", new { ModeValString = "" }, new { @class = "btn btn-default  btn-block", @role = "button" })
                          </div>
                          <div class="col-md-4">
                              @Html.ActionLink("同一班多學生作品(ZIP)", "UPLOAD", new { ModeValString = "" }, new { @class = "btn btn-default  btn-block", @role = "button" })
                          </div>
                          <div class="col-md-4">
                              @Html.ActionLink("不同班多學生作品(ZIP)", "UPLOAD2", new { ModeValString = "" }, new { @class = "btn btn-default  btn-block", @role = "button" })
                          </div>
                      }

                  </div>

        </div>
    </div>
    <div class="mx-3 px-5 py-3">
        說明:<br />
            1.	如果是同一班級的學生，請選同一班多學生作品。<br />
            2.	如果是不同學生，請使用不同班多學生作品。<br />
    </div>
}


