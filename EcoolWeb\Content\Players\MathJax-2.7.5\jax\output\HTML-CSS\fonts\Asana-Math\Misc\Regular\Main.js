/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Misc/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Misc={directory:"Misc/Regular",family:"AsanaMathJax_Misc",testString:"\u2070\u2071\u2074\u2075\u2076\u2077\u2078\u2079\u207A\u207B\u207C\u207D\u207E\u207F\u2080",32:[0,0,249,0,0],8304:[696,-271,300,14,286],8305:[685,-271,209,27,183],8308:[690,-271,299,2,296],8309:[696,-271,304,14,292],8310:[696,-271,299,14,286],8311:[687,-271,299,9,290],8312:[696,-271,299,15,285],8313:[696,-271,299,13,286],8314:[593,-271,406,35,372],8315:[449,-415,385,35,351],8316:[513,-349,406,35,372],8317:[727,-162,204,35,186],8318:[727,-162,204,19,170],8319:[555,-271,412,30,383],8320:[154,271,300,14,286],8321:[147,271,299,32,254],8322:[144,271,299,6,284],8323:[154,271,299,5,281],8324:[148,271,299,2,296],8325:[154,271,304,14,292],8326:[154,271,299,14,286],8327:[145,271,299,9,290],8328:[154,271,299,15,285],8329:[154,271,299,13,286],8330:[51,271,406,35,372],8331:[-93,127,385,35,351],8332:[-29,193,406,35,372],8333:[197,368,204,35,186],8334:[197,368,204,19,170],8336:[12,277,334,31,304],8337:[22,271,328,30,294],8338:[22,271,361,31,331],8339:[11,273,359,31,329],8340:[22,271,323,30,294],8364:[683,0,721,0,689],8531:[692,3,750,15,735],8532:[689,3,781,15,766],8533:[692,7,766,15,751],8534:[689,7,781,15,766],8535:[691,7,766,15,751],8536:[690,7,766,15,751],8537:[692,7,750,15,735],8538:[692,7,750,15,735],8539:[693,1,750,14,736],8540:[691,1,750,15,736],8541:[690,1,750,15,736],8542:[691,2,677,15,662],8543:[692,0,392,15,625],8544:[692,3,336,22,315],8545:[692,3,646,30,618],8546:[692,3,966,43,924],8547:[692,9,1015,12,1004],8548:[692,9,721,8,706],8549:[692,9,1015,12,1004],8550:[692,9,1315,15,1301],8551:[692,9,1609,16,1594],8552:[700,3,979,26,954],8553:[700,3,666,14,648],8554:[700,3,954,14,940],8555:[700,3,1254,14,1236],8556:[692,3,610,22,586],8557:[709,20,708,22,670],8558:[692,3,773,22,751],8559:[692,13,945,16,926],8560:[687,3,290,21,271],8561:[687,3,544,21,523],8562:[687,3,794,21,773],8563:[687,7,826,21,802],8564:[459,7,564,6,539],8565:[687,7,834,6,813],8566:[687,7,1094,6,1065],8567:[687,7,1339,6,1313],8568:[687,3,768,21,749],8569:[469,3,515,20,496],8570:[687,3,764,20,746],8571:[687,3,1019,20,997],8572:[726,3,290,21,271],8573:[469,20,443,26,413],8574:[726,12,610,35,579],8575:[469,3,882,16,869],10033:[669,-148,601,55,546],10038:[572,0,592,45,547]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Misc"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Misc/Regular/Main.js"]);
