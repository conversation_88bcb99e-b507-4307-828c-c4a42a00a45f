﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'si', {
	access: 'පිටපත් ප්‍රවේශය',
	accessAlways: 'හැමවිටම',
	accessNever: 'කිසිදා නොවේ',
	accessSameDomain: 'එකම වසමේ',
	alignAbsBottom: 'පතුල',
	alignAbsMiddle: 'Abs ',
	alignBaseline: 'පාද රේඛාව',
	alignTextTop: 'වගන්තිය ඉහල',
	bgcolor: 'පසුබිම් වර්ණය',
	chkFull: 'පුර්ණ තිරය සදහා අවසර',
	chkLoop: 'පුඩුව',
	chkMenu: 'සක්‍රිය බබලන මෙනුව',
	chkPlay: 'ස්‌වයංක්‍රිය ක්‍රියාත්මක වීම',
	flashvars: 'වෙනස්වන දත්ත',
	hSpace: 'HSpace',
	properties: 'බබලන ගුණ',
	propertiesTab: 'ගුණ',
	quality: 'තත්වය',
	qualityAutoHigh: 'ස්‌වයංක්‍රිය  ',
	qualityAutoLow: ' ස්‌වයංක්‍රිය   ',
	qualityBest: 'වඩාත් ගැලපෙන',
	qualityHigh: 'ඉහළ',
	qualityLow: 'පහළ',
	qualityMedium: 'මධ්‍ය',
	scale: 'පරිමාණ',
	scaleAll: 'සියල්ල ',
	scaleFit: 'හරියටම ගැලපෙන',
	scaleNoBorder: 'මාඉම් නොමැති',
	title: 'බබලන ',
	vSpace: 'VSpace',
	validateHSpace: 'HSpace සංක්‍යාවක් විය යුතුය.',
	validateSrc: 'URL හිස් නොවිය ',
	validateVSpace: 'VSpace සංක්‍යාවක් විය යුතුය',
	windowMode: 'ජනෙල ක්‍රමය',
	windowModeOpaque: 'විනිවිද පෙනෙන',
	windowModeTransparent: 'විනිවිද පෙනෙන',
	windowModeWindow: 'ජනෙල'
} );
