﻿using com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;

namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI33Service
    {
        public ZZZI33IndexViewModel GetQuestionManagerData(ZZZI33IndexViewModel model, ref ECOOL_DEVEntities db)
        {
            var temp = (from a in db.SAQT01
                        where a.SOU_KEY == model.Search.whereSOU_KEY
                        && a.STATUS != SAQT01.STATUSVal.Disabled && a.STATUS != SAQT01.STATUSVal.NotShow
                        select a
                   );
            if (!string.IsNullOrWhiteSpace(model.Search.whereSOU_Person)) {

                temp = temp.Where(a => a.CRE_PERSON.Contains(model.Search.whereSOU_Person.Trim()));

            }
            if (!string.IsNullOrWhiteSpace(model.Search.whereSearch))
            {
                temp = temp.Where(a => (a.QUESTIONNAIRE_NAME ?? "").Contains(model.Search.whereSearch.Trim())
                || (a.QUESTIONNAIRE_DESC ?? "").Contains(model.Search.whereSearch.Trim())
                );
            }

            temp = temp.OrderByDescending(a => a.CRE_DATE).ThenBy(a => a.QUESTIONNAIRE_NAME);

            model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

            return model;
        }

        public ZZZI33EditViewModel GetEditQuestionManage(ZZZI33EditViewModel model, ref ECOOL_DEVEntities db)
        {
            model.Title = (from a in db.SAQT01
                           where a.QUESTIONNAIRE_ID == model.Search.whereQUESTIONNAIRE_ID
                           select new ZZZI33EditTitleViewModel()
                           {
                               QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID,
                               QUESTIONNAIRE_NAME = a.QUESTIONNAIRE_NAME,
                               QUESTIONNAIRE_DESC = a.QUESTIONNAIRE_DESC,
                               QUESTIONNAIRE_SDATE = a.QUESTIONNAIRE_SDATE,
                               QUESTIONNAIRE_EDATE = a.QUESTIONNAIRE_EDATE,
                               ANSWER_COUNT=a.ANSWER_COUNT,
                               STATUS = a.STATUS,
                               CASH = (short)a.CASH,
                               RESULT_PERSON=a.RESULT_PERSON??"A",
                               RESULT = a.RESULT ?? false,
                               REGISTERED_BALLOT = a.REGISTERED_BALLOT ?? false,
                               ANSWER_PERSON_YN = a.ANSWER_PERSON_YN ?? "N",
                           }
                           ).FirstOrDefault();

            List<ZZZI33EditTopic_DViewModel> Temp_Topic_D = (from b in db.SAQT03
                                                             where b.QUESTIONNAIRE_ID == model.Search.whereQUESTIONNAIRE_ID
                                                             select new ZZZI33EditTopic_DViewModel()
                                                             {
                                                                 QUESTIONNAIRE_ID = b.QUESTIONNAIRE_ID,
                                                                 Q_NUM = b.Q_NUM,
                                                                 Q_T_NUM = b.Q_T_NUM,
                                                                 Q_TEXT = b.Q_TEXT,
                                                                 Q_VAL = b.Q_VAL,
                                                                 Q_INPUT_TYPE = b.Q_INPUT_TYPE,
                                                                 Q_JS = b.Q_JS,
                                                                 Q_CLASS = b.Q_CLASS,
                                                                 Q_PLACEHOLDER = b.Q_PLACEHOLDER,
                                                                 DEFAULT_VAL = b.DEFAULT_VAL,
                                                                 Q_REQUIRED_MSG = b.Q_REQUIRED_MSG,
                                                                 Q_LENGTH_MIN = b.Q_LENGTH_MIN,
                                                                 Q_LENGTH_MAX = b.Q_LENGTH_MAX,
                                                                 Q_REGEX = b.Q_REGEX,
                                                                 Q_REGEX_MSG = b.Q_REGEX_MSG,
                                                                 Q_FORMAT_MSG = b.Q_FORMAT_MSG,
                                                                 Q_ACCEPT = b.Q_ACCEPT,
                                                                 Q_Must= (b.Q_Must == 1 ? true:false)
                                                             }).ToList();

            var Temp = (from a in db.SAQT02
                        where a.QUESTIONNAIRE_ID == model.Search.whereQUESTIONNAIRE_ID
                        select new ZZZI33EditTopicViewModel()
                        {
                            QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID,
                            Q_NUM = a.Q_NUM,
                            Q_SUBJECT = a.Q_SUBJECT,
                            Q_ORDER_BY = a.Q_ORDER_BY,
                            Q_MUST = a.Q_MUST,
                            Q_TYPE = a.Q_TYPE,
                            Q_MEMO = a.Q_MEMO,
                            Q_KEY = a.Q_KEY,
                            Q_MUTIPLE_CHOICES_OF_NUM = a.Q_MUTIPLE_CHOICES_OF_NUM
                        }
           ).OrderBy(a => a.Q_ORDER_BY).ToList();

            model.Topic = Temp.Select(
             a =>
             {
                 a.Topic_D = Temp_Topic_D != null ? (from b in Temp_Topic_D
                                                     where b.QUESTIONNAIRE_ID == a.QUESTIONNAIRE_ID
                                                            && b.Q_NUM == a.Q_NUM
                                                     select b).ToList() : null;
                 a.INPUT_TYPE = a.Topic_D.Select(b => b.Q_INPUT_TYPE).FirstOrDefault();
                 a.MUST = Convert.ToBoolean(a.Q_MUST ?? 0);
                 a.KEY = Convert.ToBoolean(a.Q_KEY ?? 0);
                 return a;
             }).ToList();

            return model;
        }

        public bool SaveQuestionManage(ZZZI33EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, ref string QUESTIONNAIRE_ID)
        {
            SAQT01 SaveUp = null;

            SaveUp = db.SAQT01.Where(a => a.QUESTIONNAIRE_ID == data.Title.QUESTIONNAIRE_ID).FirstOrDefault();

            if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();

            using (TransactionScope tx = new TransactionScope())
            {
                try
                {
                    if (SaveUp != null)
                    {
                        if (SaveUp.STATUS == SAQT01.STATUSVal.Disabled)
                        {
                            Message = "此狀態不能異動資料";
                            return false;
                        }

                        SaveUp.QUESTIONNAIRE_NAME = data.Title.QUESTIONNAIRE_NAME;
                        SaveUp.QUESTIONNAIRE_DESC = HtmlUtility.SanitizeHtml(data.Title.QUESTIONNAIRE_DESC);
                        SaveUp.QUESTIONNAIRE_SDATE = data.Title.QUESTIONNAIRE_SDATE;
                        SaveUp.QUESTIONNAIRE_EDATE = data.Title.QUESTIONNAIRE_EDATE;
                        SaveUp.ANSWER_COUNT = data.Title.ANSWER_COUNT;
                        SaveUp.CHG_PERSON = User.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.END_DESC = data.Title.END_DESC;
                        SaveUp.CASH = data.Title.CASH;
                     
                        if (!string.IsNullOrWhiteSpace(data.Title.RESULT_PERSON))
                        {
                            SaveUp.RESULT = true;
                            SaveUp.RESULT_PERSON = data.Title.RESULT_PERSON;
                        }
                        else
                        {

                            SaveUp.RESULT = true;
                            SaveUp.RESULT_PERSON = "A";
                        }
                   
                        SaveUp.REGISTERED_BALLOT = data.Title.REGISTERED_BALLOT;
                        SaveUp.ANSWER_PERSON_YN = data.Title.ANSWER_PERSON_YN;
                    }
                    else if (SaveUp == null)
                    {
                        SaveUp = new SAQT01();

                        SaveUp.QUESTIONNAIRE_ID = Guid.NewGuid().ToString("N");
                        SaveUp.QUESTIONNAIRE_NAME = data.Title.QUESTIONNAIRE_NAME;
                        SaveUp.QUESTIONNAIRE_DESC = HtmlUtility.SanitizeHtml(data.Title.QUESTIONNAIRE_DESC);
                        SaveUp.QUESTIONNAIRE_SDATE = data.Title.QUESTIONNAIRE_SDATE;
                        SaveUp.QUESTIONNAIRE_EDATE = data.Title.QUESTIONNAIRE_EDATE;
                        SaveUp.ANSWER_COUNT = data.Title.ANSWER_COUNT;
                        SaveUp.STATUS = SAQT01.STATUSVal.NotStarted;
                        SaveUp.CHG_PERSON = User.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = User.USER_KEY;
                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.SOU_KEY = User.SCHOOL_NO;
                        SaveUp.REPORT_TYPE = SAQT01.REPORT_TYPE_VAL.vote;
                        SaveUp.END_DESC = data.Title.END_DESC;
                        SaveUp.CASH = data.Title.CASH;
                        if (!string.IsNullOrWhiteSpace(data.Title.RESULT_PERSON))
                        {
                            SaveUp.RESULT =true;
                            SaveUp.RESULT_PERSON = data.Title.RESULT_PERSON;
                        }
                        else {


                            SaveUp.RESULT = true;
                            SaveUp.RESULT_PERSON = "A";
                        }
                 
                        SaveUp.REGISTERED_BALLOT = data.Title.REGISTERED_BALLOT;
                        SaveUp.ANSWER_PERSON_YN = data.Title.ANSWER_PERSON_YN;

                        db.SAQT01.Add(SaveUp);
                    }
                    QUESTIONNAIRE_ID = SaveUp.QUESTIONNAIRE_ID;

                    db.SAQT02.Where(a => a.QUESTIONNAIRE_ID == SaveUp.QUESTIONNAIRE_ID).Delete();
                    db.SAQT03.Where(a => a.QUESTIONNAIRE_ID == SaveUp.QUESTIONNAIRE_ID).Delete();

                    List<SAQT02> SaveSAQT02_M_List = new List<SAQT02>();
                    List<SAQT03> SaveSAQT03_D_List = new List<SAQT03>();

                    if (data.Topic != null)
                    {
                        if (data.Topic.Count() > 0)
                        {
                            int Q_NUM = 0;
                            foreach (var M_item in data.Topic)
                            {
                                SAQT02 Cre_M = new SAQT02();
                                Q_NUM = Q_NUM + 1;

                                Cre_M.QUESTIONNAIRE_ID = SaveUp.QUESTIONNAIRE_ID;
                                Cre_M.Q_NUM = Q_NUM;
                                Cre_M.Q_SUBJECT = HtmlUtility.SanitizeHtml(M_item.Q_SUBJECT);
                                Cre_M.Q_ORDER_BY = Q_NUM;
                                if (M_item.INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.text || M_item.INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage)
                                {
                                    if (M_item.Topic_D!=null )
                                    {
                                        if (M_item.Topic_D.FirstOrDefault().Q_Must)
                                        {
                                            Cre_M.Q_MUST = Convert.ToByte(true);

                                        }
                                        else
                                        {

                                            Cre_M.Q_MUST = Convert.ToByte(false);

                                        }


                                    }
                                   
                                }
                                else {
                                    Cre_M.Q_MUST = Convert.ToByte(M_item.MUST);
                                }
                               
                                Cre_M.Q_KEY = Convert.ToByte(M_item.KEY);

                                if (M_item.INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.checkbox)
                                {
                                    Cre_M.Q_MUTIPLE_CHOICES_OF_NUM = M_item.Q_MUTIPLE_CHOICES_OF_NUM;
                                }

                                if (Convert.ToBoolean(Cre_M.Q_KEY))
                                {
                                    Cre_M.Q_MUST = Convert.ToByte(true);
                                }

                                if (M_item.INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.select)
                                {
                                    Cre_M.Q_TYPE = SAQT02.Q_TYPEVal.IsSelect;
                                }
                                else if (M_item.INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.StudentMenu)
                                {
                                    Cre_M.Q_TYPE = SAQT02.Q_TYPEVal.IsStudentMenu;
                                }
                                else
                                {
                                    Cre_M.Q_TYPE = M_item.Q_TYPE;
                                }

                                Cre_M.Q_MEMO = M_item.Q_MEMO;
                                SaveSAQT02_M_List.Add(Cre_M);

                                int Q_T_NUM = 0;

                                if (M_item.Topic_D != null)
                                {
                                    if (M_item.Topic_D.Count() > 0)
                                    {
                                        foreach (var D_item in M_item.Topic_D)
                                        {
                                            SAQT03 Cre_D = new SAQT03();
                                            Q_T_NUM = Q_T_NUM + 1;

                                            Cre_D.QUESTIONNAIRE_ID = SaveUp.QUESTIONNAIRE_ID;
                                            Cre_D.Q_NUM = Q_NUM;
                                            Cre_D.Q_T_NUM = Q_T_NUM;

                                            if (!string.IsNullOrWhiteSpace(D_item.Q_TEXT))
                                            {
                                                Cre_D.Q_TEXT = D_item.Q_TEXT.Replace(",", "，");
                                            }

                                            if (!string.IsNullOrWhiteSpace(D_item.Q_VAL))
                                            {
                                                Cre_D.Q_VAL = D_item.Q_VAL.Replace(",", "，");
                                            }

                                            Cre_D.Q_INPUT_TYPE = D_item.Q_INPUT_TYPE;
                                            Cre_D.Q_JS = D_item.Q_JS;
                                            Cre_D.Q_CLASS = D_item.Q_CLASS;
                                            Cre_D.Q_PLACEHOLDER = D_item.Q_PLACEHOLDER;
                                            Cre_D.DEFAULT_VAL = D_item.DEFAULT_VAL;
                                            Cre_D.Q_REQUIRED_MSG = D_item.Q_REQUIRED_MSG;
                                            Cre_D.Q_LENGTH_MIN = D_item.Q_LENGTH_MIN;
                                            Cre_D.Q_LENGTH_MAX = D_item.Q_LENGTH_MAX;
                                            Cre_D.Q_REGEX = D_item.Q_REGEX;
                                            Cre_D.Q_REGEX_MSG = D_item.Q_REGEX_MSG;
                                            Cre_D.Q_FORMAT_MSG = D_item.Q_FORMAT_MSG;
                                            Cre_D.Q_ACCEPT = D_item.Q_ACCEPT;
                                            if (D_item.Q_Must) {

                                                Cre_D.Q_Must = 1;
                                            }
                                          
                                            if (D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.text
                                                 || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.date
                                                 || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.email
                                                 || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.number
                                                 || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.url
                                                 || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.textarea
                                                 )
                                            {
                                                if (string.IsNullOrWhiteSpace(Cre_D.Q_CLASS))
                                                {
                                                    Cre_D.Q_CLASS = "form-control";
                                                }
                                            }

                                            if (D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.radio
                                             || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.checkbox
                                             || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.select
                                             )
                                            {
                                                if (string.IsNullOrWhiteSpace(Cre_D.Q_TEXT))
                                                {
                                                    if (!string.IsNullOrWhiteSpace(D_item.Q_VAL))
                                                    {
                                                        Cre_D.Q_TEXT = Cre_D.Q_VAL.Replace(",", "，");
                                                    }
                                                }
                                            }
                                           
                                            SaveSAQT03_D_List.Add(Cre_D);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (SaveSAQT02_M_List != null)
                    {
                        if (SaveSAQT02_M_List.Count() > 0)
                        {
                            db.SAQT02.AddRange(SaveSAQT02_M_List);
                        }
                    }

                    if (SaveSAQT03_D_List != null)
                    {
                        if (SaveSAQT03_D_List.Count() > 0)
                        {
                            db.SAQT03.AddRange(SaveSAQT03_D_List);
                        }
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }

                    if ((SaveUp.ANSWER_PERSON_YN ?? "N") == "Y")
                    {
                        try
                        {
                            REFT01Service.UpdateStatus(User.USER_KEY, "SAQT01", data.REF_KEY, SaveUp.QUESTIONNAIRE_ID, (db.Database.Connection) as SqlConnection);
                        }
                        catch (Exception ex)
                        {
                            Message = "系統發生錯誤;原因:" + ex.Message;
                            return false;
                        }
                    }

                    tx.Complete();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }
            }

            return true;
        }

        public ZZZI33PreviewViewModel GetQuestionManagerDetailsPreview(ZZZI33PreviewViewModel model, ref ECOOL_DEVEntities db)
        {
            model.Title = (from c in db.SAQT01
                           where c.QUESTIONNAIRE_ID == model.Search.whereQUESTIONNAIRE_ID
                           select c).FirstOrDefault();

            List<SAQT03> Temp_Topic_D = (from b in db.SAQT03
                                         where b.QUESTIONNAIRE_ID == model.Search.whereQUESTIONNAIRE_ID
                                         select b).ToList();

            var Temp = (from a in db.SAQT02
                        where a.QUESTIONNAIRE_ID == model.Search.whereQUESTIONNAIRE_ID
                        select new ZZZI33TopicViewModel()
                        {
                            QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID,
                            Q_NUM = a.Q_NUM,
                            Q_SUBJECT = a.Q_SUBJECT,
                            Q_ORDER_BY = a.Q_ORDER_BY,
                            Q_MUST = a.Q_MUST,
                            Q_TYPE = a.Q_TYPE,
                            Q_MEMO = a.Q_MEMO,
                        }
             ).OrderBy(a => a.Q_ORDER_BY).ToList();

            model.Topic = Temp.Select(
                a =>
                {
                    a.Topic_D = (from b in Temp_Topic_D
                                 where b.QUESTIONNAIRE_ID == a.QUESTIONNAIRE_ID
                                        && b.Q_NUM == a.Q_NUM
                                 select b).ToList();
                    a.ANSWER = (model.Topic != null) ? model.Topic.Where(b => b.Q_NUM == a.Q_NUM).Select(b => b.ANSWER).FirstOrDefault() : string.Empty;
                    a.Index = (model.Topic != null) ? model.Topic.Where(b => b.Q_NUM == a.Q_NUM).Select(b => b.Index).FirstOrDefault() : null;
                    return a;
                }).ToList();

            return model;
        }

        public bool UpdateStatusQuestionManage(ZZZI33EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            ZZZI33Service Service = new ZZZI33Service();
            SAQT01 SaveUp = db.SAQT01.Where(a => a.QUESTIONNAIRE_ID == data.Search.whereQUESTIONNAIRE_ID).FirstOrDefault();

            if (SaveUp == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆資料";
                return false;
            }

            //if (SaveUp.QUESTIONNAIRE_EDATE != null && SaveUp.QUESTIONNAIRE_EDATE < DateTime.Now && data.SaveType != ZZZI33EditViewModel.SaveTypeVal.NotShow)
            //{
            //    Message = "此投票已結束!!";
            //    return false;
            //}

            if (data.SaveType == ZZZI33EditViewModel.SaveTypeVal.Release)
            {
                if (SaveUp.STATUS != SAQT01.STATUSVal.NotStarted)
                {
                    Message = "您目前的狀態不可發佈!!";
                    return false;
                }
                if (SaveUp.STATUS == SAQT01.STATUSVal.NotStarted)
                {
                    string RQUESTIONNAIRE_ID = "";
                    RQUESTIONNAIRE_ID = SaveUp.QUESTIONNAIRE_ID;
                    bool OK = Service.SaveQuestionManage(data, User, ref db, ref Message, ref RQUESTIONNAIRE_ID);
                }
                SaveUp = db.SAQT01.Where(a => a.QUESTIONNAIRE_ID == data.Search.whereQUESTIONNAIRE_ID).FirstOrDefault();
                if (SaveUp.QUESTIONNAIRE_EDATE != null && SaveUp.QUESTIONNAIRE_EDATE < DateTime.Now)
                {
                    Message = "結束日期小於現在時間!!";
                    return false;
                }
                
                var check = db.SAQT02.Where(a => a.QUESTIONNAIRE_ID == SaveUp.QUESTIONNAIRE_ID).ToList();
                if (check.Count() == 0)
                {
                    Message = "未製作任何[標題/問題]，無法發佈!!";
                    return false;
                }
                
                SaveUp.STATUS = SAQT01.STATUSVal.StartedOut;
                Message = "發佈完成";
            }
            else if (data.SaveType == ZZZI33EditViewModel.SaveTypeVal.End && data.SaveType != ZZZI33EditViewModel.SaveTypeVal.NotShow)
            {
                if (SaveUp.STATUS != SAQT01.STATUSVal.StartedOut)
                {
                    Message = "您目前的狀態不可提早結束!!";
                    return false;
                }

                SaveUp.QUESTIONNAIRE_EDATE = DateTime.Now;
                Message = "提早結束完成";
            }
            else if (data.SaveType == ZZZI33EditViewModel.SaveTypeVal.Disabled)
            {
                var Use_YN = PermissionService.GetPermission_Use_YN("ZZZI33", "Del", User?.SCHOOL_NO, User?.USER_NO);

                if (SaveUp.STATUS != SAQT01.STATUSVal.NotStarted && Use_YN == SharedGlobal.N)
                {
                    Message = "您目前的狀態不可作廢!!";
                    return false;
                }

                SaveUp.STATUS = SAQT01.STATUSVal.Disabled;
                Message = "資料已作廢";
            }
            else if (data.SaveType == ZZZI33EditViewModel.SaveTypeVal.NotShow)
            {
                SaveUp.STATUS = SAQT01.STATUSVal.NotShow;
                Message = "資料已作廢(隱藏)";
            }
            SaveUp.CHG_PERSON = User.USER_KEY;
            SaveUp.CHG_DATE = DateTime.Now;

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }
    }
}