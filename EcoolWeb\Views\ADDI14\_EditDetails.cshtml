﻿@model ADDI14EditPeopleViewModel

@if (Model != null)
{
    using (Html.BeginCollectionItem("Details"))
    {
        var Index = Html.GetIndex("Details");

        <div class="Tr@(Model.SCHOOL_NO+Model.CLASS_NO+Model.SEAT_NO)  Tr@(Model.SCHOOL_NO+Model.USER_NO)   col-md-3" id="Tr@(Model.CARD_NO)">
            <span id="OkSpan@(Model.CARD_NO)" class="OkSpan@(Model.SCHOOL_NO+Model.CLASS_NO+Model.SEAT_NO) OkSpan@(Model.SCHOOL_NO+Model.USER_NO)" style="display:none;color:blue">
                <i class="glyphicon glyphicon-ok-sign fa-2x"></i>
            </span>
            <span id="NGSpan@(Model.CARD_NO)" class="NGSpan@(Model.SCHOOL_NO+Model.CLASS_NO+Model.SEAT_NO) NGSpan@(Model.SCHOOL_NO+Model.USER_NO)" style="color:red">
                <i class="glyphicon glyphicon-remove-sign fa-2x"></i>
            </span>
            @Html.HiddenFor(m => m.SCHOOL_NO)
            @Html.HiddenFor(m => m.SHORT_NAME)
            @Html.HiddenFor(m => m.USER_NO, new { @class = $"NAME{Model.SCHOOL_NO + Model.USER_NO}" })
            @Html.HiddenFor(m => m.NAME, new { @class = $"NAME{Model.CARD_NO}" })
            @Html.HiddenFor(m => m.GRADE)
            @Html.HiddenFor(m => m.CLASS_NO, new { @class = $"NAME{Model.SCHOOL_NO + Model.CLASS_NO + Model.SEAT_NO}" })
            @Html.HiddenFor(m => m.SEAT_NO)
            @Html.HiddenFor(m => m.CARD_NO)

            @Html.HiddenFor(m => m.CheckIn, new { @class = $"CheckIn{Model.CARD_NO} CheckIn{Model.SCHOOL_NO + Model.CLASS_NO + Model.SEAT_NO}  CheckIn{Model.SCHOOL_NO + Model.USER_NO}" })

            <b> @Model.SHORT_NAME   @Model.NAME    @(Model.CLASS_NO)班  @(Model.SEAT_NO)號</b>
        </div>
    }
}
@*@if (ViewBag.CallCashModel != null) {

    }*@