// JavaScript for ADDI05 Index page - 有獎徵答首頁
$(document).ready(function() {
    // 有獎徵答首頁模組
    const indexModule = {
        targetFormID: '#form1',
        
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
        },

        bindEvents: function() {
            // 綁定搜尋表單提交事件
            $(this.targetFormID).on('submit', this.handleFormSubmit.bind(this));
            
            // 綁定清除按鈕事件
            $('input[onclick*="todoClear"], button[onclick*="todoClear"]').on('click', this.handleClear.bind(this));
            
            // 綁定新增按鈕事件
            $('input[onclick*="onAdd"], button[onclick*="onAdd"]').on('click', this.handleAdd.bind(this));
            
            // 綁定分頁按鈕事件
            $('a[onclick*="FunPageProc"]').on('click', this.handlePageClick.bind(this));
            
            // 綁定排序按鈕事件
            $('a[onclick*="FunSort"], th[onclick*="FunSort"]').on('click', this.handleSortClick.bind(this));
            
            // 綁定篩選按鈕事件
            $('button[onclick*="doSearchBool"], a[onclick*="doSearchBool"]').on('click', this.handleSearchBool.bind(this));
            
            // 綁定詳細頁面連結事件
            $('a[onclick*="onBtnLink"]').on('click', this.handleDetailLink.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.FunPageProc = this.funPageProc.bind(this);
            window.FunSort = this.funSort.bind(this);
            window.doSearchBool = this.doSearchBool.bind(this);
            window.funAjax = this.funAjax.bind(this);
            window.onBtnLink = this.onBtnLink.bind(this);
            window.todoClear = this.todoClear.bind(this);
            window.onAdd = this.onAdd.bind(this);
            window.onGo = this.onGo.bind(this);
        },

        funPageProc: function(pageno) {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素');
                    return;
                }
                
                form.page.value = pageno;
                this.funAjax();
            } catch (error) {
                console.error('分頁處理時發生錯誤:', error);
                this.showMessage('分頁處理時發生錯誤，請稍後再試');
            }
        },

        funSort: function(sortName) {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素');
                    return;
                }

                if (form.OrderByName.value === sortName) {
                    if (form.SyntaxName.value === "Desc") {
                        form.SyntaxName.value = "ASC";
                    } else {
                        form.SyntaxName.value = "Desc";
                    }
                } else {
                    form.OrderByName.value = sortName;
                    form.SyntaxName.value = "Desc";
                }
                
                this.funAjax();
            } catch (error) {
                console.error('排序處理時發生錯誤:', error);
                this.showMessage('排序處理時發生錯誤，請稍後再試');
            }
        },

        doSearchBool: function(colName, whereValue) {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素');
                    return;
                }

                $("#" + colName).val(whereValue);
                form.page.value = 1;
                this.funAjax();
            } catch (error) {
                console.error('搜尋篩選時發生錯誤:', error);
                this.showMessage('搜尋篩選時發生錯誤，請稍後再試');
            }
        },

        funAjax: function() {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素');
                    return;
                }

                const searchContentsVal = form.SearchContents.value;
                const orderByNameVal = form.OrderByName.value;
                const syntaxNameVal = form.SyntaxName.value;
                const pageVal = form.page.value;
                const dialogTypeVal = form.DIALOG_TYPE.value;
                const whereShowDataVal = form.whereShowData.value;

                $.ajax({
                    url: window.ADDI05_INDEX_URLS.pageContent,
                    data: {
                        SearchContents: searchContentsVal,
                        OrderByName: orderByNameVal,
                        SyntaxName: syntaxNameVal,
                        page: pageVal,
                        DialogType: dialogTypeVal,
                        whereShowData: whereShowDataVal
                    },
                    type: 'POST',
                    cache: false,
                    dataType: 'html',
                    timeout: 10000
                })
                .done(function(data) {
                    $('#PageContent').html(data);
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('AJAX請求失敗:', { textStatus, errorThrown, status: jqXHR.status });
                    
                    let errorMessage = 'AJAX請求失敗';
                    if (jqXHR.status === 404) {
                        errorMessage = '請求的頁面未找到';
                    } else if (jqXHR.status === 500) {
                        errorMessage = '服務器內部錯誤，請聯繫系統管理員';
                    } else if (textStatus === 'timeout') {
                        errorMessage = '請求超時，請稍後再試';
                    }
                    
                    this.showMessage(errorMessage);
                }.bind(this));
            } catch (error) {
                console.error('AJAX處理時發生錯誤:', error);
                this.showMessage('AJAX處理時發生錯誤，請稍後再試');
            }
        },

        onBtnLink: function(dialogId) {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素');
                    return;
                }

                form.DIALOG_ID.value = dialogId;
                form.action = window.ADDI05_INDEX_URLS.detail;
                form.submit();
            } catch (error) {
                console.error('導航到詳細頁面時發生錯誤:', error);
                this.showMessage('導航到詳細頁面時發生錯誤，請稍後再試');
            }
        },

        todoClear: function() {
            try {
                // 先清除錯誤訊息
                $('.general-message').remove();
                
                // 重設表單元素
                $(this.targetFormID).find(":input,:selected").each(function(i) {
                    const $element = $(this);
                    const type = $element.attr('type');
                    const isReadonly = $element.attr('readonly');
                    const tag = this.tagName.toLowerCase();

                    // 只處理非唯讀元素
                    if (isReadonly !== 'readonly' && isReadonly !== true) {
                        if (type === 'radio' || type === 'checkbox') {
                            if ($element.attr("title") === 'Default') {
                                this.checked = true;
                            } else {
                                this.checked = false;
                            }
                        } else if (tag === 'select') {
                            // 下拉式選單重設為第一個選項
                            this.selectedIndex = 0;
                        } else if (type === 'text' || type === 'hidden' || type === 'password' || type === 'textarea') {
                            this.value = '';
                        }
                    }
                });

                this.funAjax();
                console.log('表單已清除');
            } catch (error) {
                console.error('清除表單時發生錯誤:', error);
                this.showMessage('清除表單時發生錯誤，請稍後再試');
            }
        },

        onAdd: function() {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素');
                    return;
                }

                $('#VIEW_DATA_TYPE').val(window.ADDI05_INDEX_CONFIG.viewDataTypeA);
                form.action = window.ADDI05_INDEX_URLS.edit;
                form.submit();
            } catch (error) {
                console.error('新增操作時發生錯誤:', error);
                this.showMessage('新增操作時發生錯誤，請稍後再試');
            }
        },

        onGo: function(actionVal) {
            // 使用共用的onGo函數
            return ADDI05Common.onGo(actionVal);
        },

        // 事件處理器
        handleFormSubmit: function(event) {
            try {
                console.log('表單提交');
                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                this.showMessage('表單提交時發生錯誤，請稍後再試');
                return false;
            }
        },

        handleClear: function(event) {
            event.preventDefault();
            this.todoClear();
        },

        handleAdd: function(event) {
            event.preventDefault();
            this.onAdd();
        },

        handlePageClick: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            if (onclick) {
                const match = onclick.match(/FunPageProc\('?([^']*)'?\)/);
                if (match) {
                    this.funPageProc(match[1]);
                }
            }
        },

        handleSortClick: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            if (onclick) {
                const match = onclick.match(/FunSort\('([^']*)'\)/);
                if (match) {
                    this.funSort(match[1]);
                }
            }
        },

        handleSearchBool: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            if (onclick) {
                const match = onclick.match(/doSearchBool\('([^']*)',\s*'([^']*)'\)/);
                if (match) {
                    this.doSearchBool(match[1], match[2]);
                }
            }
        },

        handleDetailLink: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            if (onclick) {
                const match = onclick.match(/onBtnLink\('([^']*)'\)/);
                if (match) {
                    this.onBtnLink(match[1]);
                }
            }
        },

        showMessage: function(message) {
            // 移除現有的訊息
            $('.general-message').remove();
            
            // 在頁面頂部顯示訊息
            const messageHtml = `<div class="general-message" style="color: red; background-color: #f9f9f9; padding: 10px; margin: 10px 0; border: 1px solid red; border-radius: 4px;">${message}</div>`;
            
            // 在表單頂部插入訊息
            $(this.targetFormID).prepend(messageHtml);
            
            // 滾動到訊息位置
            $('html, body').animate({
                scrollTop: $('.general-message').offset().top - 50
            }, 300);
            
            // 5秒後自動移除
            setTimeout(() => {
                $('.general-message').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

    // 初始化模組
    indexModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});
