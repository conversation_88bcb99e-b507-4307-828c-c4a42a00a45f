﻿@model GameLotteryViewModel

@Html.HiddenFor(m => m.Search.WhereGAME_NAME)
@Html.HiddenFor(m => m.Search.WhereGAME_NO)
@Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.OrdercColumn)

<div class="text-right">
    <a role="button" onclick="OnAddLottery()" class="btn btn-sm btn-sys"><i class="fa fa-plus-circle"></i> 新增抽獎</a>
    <br />
    <br />
</div>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        抽獎紀錄列表
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('LOTTERY_DESC')">
                            @Html.DisplayNameFor(model => model.ListData.First().LOTTERY_DESC)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CRE_DATE')">
                            @Html.DisplayNameFor(model => model.ListData.First().CRE_DATE)
                        </samp>
                    </th>
                    @if (ViewBag.GAME_TYPE == (byte)ADDT26.GameType.一般)
                    {
                        <th>

                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('LEVEL_COUNT')">
                                限制完成<br />關卡數
                            </samp>
                        </th>
                        <th>

                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('LEVEL')">
                                限制完成<br />某關卡數
                            </samp>
                        </th>
                    }

                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('PEOPLE_COUNT')">
                            @Html.DisplayNameFor(model => model.ListData.First().PEOPLE_COUNT)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('UNLOTTERY')">
                            排除<br />中獎人員
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('UNCUEST')">
                            @Html.DisplayNameFor(model => model.ListData.First().UNCUEST)
                        </samp>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {
                        <tr>
                            <td>
                                @if (item.STATUS == ((int)ADDT32.StatusVal.已抽獎).ToString())
                                {
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnLotteryDetails('@item.LOTTERY_NO')"> <i class="fa fa-list-alt"></i> 中獎名單</button>
                                }
                                else if (item.STATUS == ((int)ADDT32.StatusVal.待抽獎).ToString())
                                {
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnEditLotteryDetails('@item.LOTTERY_NO')"> <i class="fa fa-list-alt"></i> 編輯設定</button>
                                    <button type="button" class="btn btn-xs btn-Basic" onclick="OnChiefLotteryDetails('@item.LOTTERY_NO')"> <i class="fa fa-list-alt"></i> 進入開始抽獎畫面</button>
                                }
                                <button type="button" class="btn btn-xs btn-Basic" onclick="OnDel('@item.LOTTERY_NO')"> <i class="fa fa-list-alt"></i> 作廢</button>
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.LOTTERY_DESC)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CRE_DATE)
                            </td>
                            @if (ViewBag.GAME_TYPE == (byte)ADDT26.GameType.一般)
                            {
                                <td align="center">
                                    @if (item.LEVEL_COUNT == null)
                                    {
                                        <strong>無限制</strong>
                                    }
                                    else
                                    {
                                        <strong>@item.LEVEL_COUNT <text>關以上(含)</text></strong>

                                    }
                                </td>
                                <td align="center">
                                    @if (string.IsNullOrWhiteSpace(item.LEVEL))
                                    {
                                        <strong>無</strong>
                                    }
                                    else
                                    {
                                        if (item.LEVEL == SharedGlobal.ALL)
                                        {
                                            <strong>全過關</strong>
                                        }
                                        else
                                        {
                                            <strong> @item.LEVEL.Split(',').Length <text>關</text></strong>
                                        }
                                    }
                                </td>
                            }

                            <td align="center">
                                @Html.DisplayFor(modelItem => item.PEOPLE_COUNT)
                                <text>人</text>
                            </td>
                            <td align="center">
                                @if (item.UNLOTTERY)
                                {
                                    <code>排除</code>
                                }
                            </td>
                            <td align="center">
                                @if (item.UNCUEST)
                                {
                                    <code>排除</code>
                                }
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>
<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                                                      .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                                                      .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                                                      .SetNextPageText(PageGlobal.DfSetNextPageText)
                                                                     )
</div>