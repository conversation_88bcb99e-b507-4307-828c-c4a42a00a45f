﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.com.ecool.Models.entity;
using System.Data;
using System.Data.SqlClient;
using com.ecool.sqlConnection;
using ECOOL_APP;
using ECOOL_APP.EF;
using Dapper;

namespace com.ecool.service
{
    public class PermissionService
    {
        /// <summary>
        /// 依權限 取得 Menu
        /// </summary>
        /// <param name="SCHOOL_NO">學校代碼</param>
        /// <param name="USER_NO">帳號</param>
        /// <returns></returns>
        public static List<uZZT01> Permission_GetMenuListQUERY(string SCHOOL_NO, string USER_NO, int? ROLE_TYPE)
        {
            List<uZZT01> list_data = new List<uZZT01>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" WITH #TEMP_Menu AS ");
                sb.Append(" ( ");

                // 標題
                sb.Append(" SELECT DISTINCT A.BRE_NO ,A.BRE_NAME,A.CONTROLLER,A.ACTION_ID ");
                sb.Append(" ,A.LEVEL_ID,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY,A.TARGET,A.BRE_TYPE , A.LEVEL_NO,A.APP_USE_YN,A.ENABLE ");
                sb.Append(" FROM ZZT01 A (NOLOCK)   ");
                sb.Append(" Where 1=1 ");
                sb.Append(" AND A.BRE_TYPE ='3' ");

                sb.Append(" Union  ");

                // 全部
                sb.Append(" SELECT DISTINCT A.BRE_NO ,A.BRE_NAME,A.CONTROLLER,A.ACTION_ID ");
                sb.Append(" ,A.LEVEL_ID,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY,A.TARGET,A.BRE_TYPE , A.LEVEL_NO,A.APP_USE_YN,A.ENABLE ");
                sb.Append(" FROM ZZT01 A (NOLOCK)   ");
                sb.Append(" INNER JOIN ZZT34 B (NOLOCK) ON A.BRE_NO=B.BRE_NO AND A.ACTION_ID=B.ACTION_ID AND B.ACTION_TYPE='ALL' ");
                sb.Append(" Where 1=1 ");
                sb.Append(" AND A.BRE_TYPE ='1' ");

                if (USER_NO != "")
                {
                    sb.Append(" Union  ");

                    //依角色 for 角色功能權限對應檔 PS.學生要額外處理(因學生不會在HRMT25 裡)
                    sb.Append(" SELECT  DISTINCT  A.BRE_NO ,A.BRE_NAME,A.CONTROLLER,A.ACTION_ID  ");
                    sb.Append(" ,A.LEVEL_ID,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY,A.TARGET,A.BRE_TYPE ,A.LEVEL_NO,A.APP_USE_YN,A.ENABLE ");
                    sb.Append(" FROM HRMT01 H (NOLOCK) ");
                    sb.Append(" LEFT OUTER JOIN HRMT25 C (NOLOCK) ON H.SCHOOL_NO=C.SCHOOL_NO AND H.USER_NO=C.USER_NO ");
                    sb.Append(" INNER JOIN ZZT10 B (NOLOCK) ON B.ROLE_ID =  dbo.fn_NotHrmt25(H.USER_TYPE, C.ROLE_ID) ");
                    sb.Append(" INNER JOIN ZZT01 A (NOLOCK) ON A.BRE_NO=B.BRE_NO AND A.ACTION_ID=B.ACTION_ID ");
                    sb.Append(" Where A.BRE_TYPE ='2' ");
                    sb.AppendFormat(" AND H.SCHOOL_NO='{0}' AND H.USER_NO='{1}' ", SCHOOL_NO, USER_NO);

                    sb.Append(" Union  ");

                    //依人員 for 人員功能權限對應檔
                    sb.Append(" SELECT DISTINCT  A.BRE_NO ,A.BRE_NAME,A.CONTROLLER,A.ACTION_ID ");
                    sb.Append(" ,A.LEVEL_ID,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY,A.TARGET,A.BRE_TYPE  ,A.LEVEL_NO,A.APP_USE_YN,A.ENABLE ");
                    sb.Append(" FROM  ZZT01 A (NOLOCK)   ");
                    sb.Append(" INNER JOIN ZZT03 B  (NOLOCK)  ON A.BRE_NO=B.BRE_NO  AND A.ACTION_ID=B.ACTION_ID");
                    sb.Append(" Where A.BRE_TYPE ='2' ");
                    sb.AppendFormat(" AND B.SCHOOL_NO='{0}' AND B.USER_NO='{1}' ", SCHOOL_NO, USER_NO);

                    //超級使用者
                    if (ROLE_TYPE != null)
                    {
                        if (ROLE_TYPE == 1)
                        {
                            sb.Append(" Union  ");
                            sb.Append(" SELECT DISTINCT A.BRE_NO ,A.BRE_NAME,A.CONTROLLER,A.ACTION_ID ");
                            sb.Append(" ,A.LEVEL_ID,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY,A.TARGET,A.BRE_TYPE , A.LEVEL_NO,A.APP_USE_YN,A.ENABLE ");
                            sb.Append(" FROM ZZT01 A (NOLOCK)  ");
                            sb.Append(" INNER JOIN ZZT34 B (NOLOCK) ON A.BRE_NO=B.BRE_NO AND A.ACTION_ID=B.ACTION_ID");
                            sb.Append(" Where A.BRE_TYPE ='2'");
                        }
                    }
                }

                sb.Append(" ) ");

                sb.Append(" SELECT DISTINCT A.BRE_NO ,A.BRE_NAME,A.CONTROLLER,A.ACTION_ID ");
                sb.Append(" ,A.LEVEL_ID,A.LINK_ADDR,A.BRE_NO_PRE,A.ORDER_BY,A.TARGET,A.BRE_TYPE , A.LEVEL_NO,A.APP_USE_YN,A.ENABLE ");
                sb.Append(" FROM #TEMP_Menu A ");
                sb.Append(" WHERE (A.BRE_TYPE in ('1','2')    ");
                sb.Append("         or (   A.BRE_TYPE ='3' and (SELECT COUNT(*) FROM #TEMP_Menu S WHERE A.BRE_NO=S.BRE_NO_PRE)>0 ) "); //濾掉 有標題沒小孩的
                sb.Append("        )  ");
                sb.Append(" ORDER BY  A.LEVEL_NO; ");
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uZZT01()
                    {
                        BRE_NO = dr["BRE_NO"].ToString(),
                        BRE_NAME = dr["BRE_NAME"].ToString(),
                        CONTROLLER = dr["CONTROLLER"].ToString(),
                        ACTION_ID = (dr["ACTION_ID"] == DBNull.Value ? "" : (string)dr["ACTION_ID"]),
                        LEVEL_ID = Convert.ToDecimal(dr["LEVEL_ID"].ToString()),
                        LINK_ADDR = (dr["LINK_ADDR"] == DBNull.Value ? "" : (string)dr["LINK_ADDR"]),
                        BRE_NO_PRE = (dr["BRE_NO_PRE"] == DBNull.Value ? "" : (string)dr["BRE_NO_PRE"]),
                        ORDER_BY = Convert.ToDecimal(dr["ORDER_BY"].ToString()),
                        TARGET = (dr["TARGET"] == DBNull.Value ? "" : (string)dr["TARGET"]),
                        BRE_TYPE = (dr["BRE_TYPE"] == DBNull.Value ? "" : (string)dr["BRE_TYPE"]),
                        LEVEL_NO = (dr["LEVEL_NO"] == DBNull.Value ? "" : (string)dr["LEVEL_NO"]),
                        APP_USE_YN = (dr["APP_USE_YN"] == DBNull.Value ? "" : (string)dr["APP_USE_YN"]),
                        ENABLE = (dr["ENABLE"] == DBNull.Value ? true : (bool)dr["ENABLE"]),
                    });
                }
                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }

        /// <summary>
        /// 取回是否使用 此程式權限
        /// </summary>
        /// <param name="BRE_NO">程式代碼</param>
        /// <param name="ACTION_ID">動作代碼</param>
        /// <param name="SCHOOL_NO">學校代碼</param>
        /// <param name="USER_NO">帳號代碼</param>
        /// <returns>回傳Y.可使用 ,N不可使用 </returns>
        public static string GetPermission_Use_YN(string BRE_NO, string ACTION_ID, string SCHOOL_NO, string USER_NO)
        {
            string ReturnVal = string.Empty;
            if (string.IsNullOrWhiteSpace(BRE_NO) || string.IsNullOrWhiteSpace(ACTION_ID))
            {
                return "N";
            }

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(USER_NO))
            {
                USER_NO = string.Empty;
            }

            string sp_name = string.Empty;
            SqlConnection conn = null;

            sp_name = "sp_Permission_Use";
            com.ecool.sqlConnection.sqlConnection getConn = new com.ecool.sqlConnection.sqlConnection();
            conn = getConn.getConnection4Query();

            var sql = @"SELECT isnull(ENABLE,1) as ENABLE  FROM ZZT01 a (nolock) Where a.BRE_NO=@BRE_NO ";
            var enable = conn.Query<bool?>(sql, new { BRE_NO }).FirstOrDefault() ?? true;

            if (enable == false)
            {
                return "N";
            }

            SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("@BRE_NO", System.Data.SqlDbType.NVarChar, 50).Value = BRE_NO;
            cmd.Parameters.Add("@ACTION_ID", System.Data.SqlDbType.NVarChar, 50).Value = ACTION_ID;
            cmd.Parameters.Add("@SCHOOL_NO", System.Data.SqlDbType.NVarChar, 8).Value = SCHOOL_NO;
            cmd.Parameters.Add("@USER_NO", System.Data.SqlDbType.NVarChar, 24).Value = USER_NO;

            SqlParameter retValParam = cmd.Parameters.Add("@OutReturnValue", System.Data.SqlDbType.NVarChar, 10);
            retValParam.Direction = ParameterDirection.Output;

            sql = "DECLARE @OutReturnValue Nvarchar(1)" + "\r\n";
            sql = sql + " Exec dbo." + sp_name + " '" + BRE_NO + "','" + ACTION_ID + "','" + SCHOOL_NO + "','" + USER_NO + "',@OutReturnValue out " + "\r\n";
            sql = sql + " PRINT @OutReturnValue  " + "\r\n";

            try
            {
                cmd.ExecuteNonQuery();
                ReturnVal = retValParam.Value.ToString();
                cmd.Clone();
                cmd.Dispose();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                getConn.closeConnection4Query(conn);
            }

            return ReturnVal;
        }

        /// <summary>`
        /// 取得權限及判斷是否班導
        /// PS.當帳號 角色最大權限是 班導師時，需判斷資料是否是 自已班級
        /// </summary>
        /// <param name="ThisCLASS_NO"></param>
        /// <param name="ThisSchoolNO"></param>
        /// <param name="ThisBRE_NO"></param>
        /// <param name="ThisACTION_ID"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public static bool GetActionPermissionMyClassNO(string ThisCLASS_NO, string ThisSchoolNO, string ThisBRE_NO, string ThisACTION_ID, UserProfile user, string StdUserNO)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            bool ReturnVal = false;
            if (user == null)
            {
                return ReturnVal;
            }

            string UseYN = GetPermission_Use_YN(ThisBRE_NO, ThisACTION_ID, ThisSchoolNO, user.USER_NO);
            if (UseYN == "Y")
            {
                string teachClassNO = db.HRMT01.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled && x.USER_TYPE == "T").Select(x => x.CLASS_NO).FirstOrDefault();
                user.CLASS_NO = teachClassNO;
               
                ReturnVal = isMyClassNO(ThisCLASS_NO, user, StdUserNO);
               
                if (user.USER_TYPE == "A")
                {
                    ReturnVal = true;
                }
            }

            return ReturnVal;
        }
        //判斷是否為自己班或是為閱讀教師
        public static bool isMyClassNO(string ThisCLASS_NO, UserProfile user, string StdUserNO)
        {
            bool ReturnVal = false;
            DataTable dt;
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO) == false) //角色最大權限是 班導師時 TEACH_CLASS_NO 才會有值
            {
                if (string.IsNullOrEmpty(ThisCLASS_NO) == false)
                {
                    StringBuilder sb = new StringBuilder();
                    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
                    sb.AppendFormat("select count(*) as count  from HRMT01 where USER_NO = '{0}' and CLASS_NO = '{1}' and SCHOOL_NO = '{2}'", StdUserNO, user.CLASS_NO, user.SCHOOL_NO);
                    dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                    DataRow dr = dt.Rows[0];
                    int HrtCount = (int)dr["count"];
                    int Hrt25Count = 0;
                    Hrt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && (x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Library) || x.ROLE_ID.Contains(HRMT24_ENUM.ROLE_Read))).Count();
                    if (ThisCLASS_NO == user.TEACH_CLASS_NO || HrtCount > 0 || Hrt25Count>0) //是否自已班級或是為閱讀教師
                    {
                        ReturnVal = true;
                    }
                }
            }
            else
            {
                ReturnVal = true;
            }

            return ReturnVal;
        }

        /// <summary>
        /// 取得此 BRE_NO 所有 Action 權限
        /// </summary>
        /// <param name="BRE_NO"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        public static List<ControllerPermissionfile> GetActionPermissionForBreNO(string BRE_NO, string SCHOOL_NO = "", string USER_NO = "")
        {
            List<ControllerPermissionfile> ReturnVal = new List<ControllerPermissionfile>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT A.BRE_NO,A.ACTION_ID ");
                sb.AppendFormat(" ,Case When dbo.fn_Permission_Use(A.BRE_NO,A.ACTION_ID,'{0}','{1}') ='Y' Then 'true' else 'false' end Bool", SCHOOL_NO, USER_NO);
                sb.Append(" FROM ZZT34 A (NOLOCK) ");
                sb.Append(" WHERE 1=1  ");
                sb.AppendFormat(" and A.BRE_NO='{0}' ", BRE_NO);

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    ControllerPermissionfile Data = new ControllerPermissionfile();
                    Data.BreNoName = (string)dr["BRE_NO"];
                    Data.ActionName = (string)dr["ACTION_ID"];
                    Data.BoolUse = Convert.ToBoolean(dr["Bool"]);

                    ReturnVal.Add(Data);
                }
                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return ReturnVal;
        }

        /// <summary>
        /// 取得此人的角色等級
        /// </summary>
        /// <param name="USER_KEY"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        public static decimal? GetROLE_LEVEL(string USER_KEY = "", string SCHOOL_NO = "", string USER_NO = "")
        {
            decimal? ReturnVal = decimal.MaxValue;
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            int dtCount = 0;
            try
            {
                sb.Append(" SELECT TOP 1 Ob.ROLE_LEVEL ");
                sb.Append(" FROM HRMT01 Oh (nolock)  ");
                sb.Append(" inner join HRMT25 OJ (nolock) on Oh.SCHOOL_NO = OJ.SCHOOL_NO and Oh.USER_NO = OJ.USER_NO  ");
                sb.Append(" inner join HRMT24 Ob(nolock) on OJ.ROLE_ID = Ob.ROLE_ID ");
                sb.Append(" Where 1 = 1  ");

                if (USER_KEY != "")
                {
                    sb.AppendFormat(" AND Oh.USER_KEY = '{0}' ", USER_KEY);
                }
                else
                {
                    sb.AppendFormat(" AND Oh.SCHOOL_NO = '{0}' ", SCHOOL_NO);
                    sb.AppendFormat(" AND Oh.USER_NO = '{0}' ", USER_NO);
                }

                sb.Append(" ORDER BY Ob.ROLE_LEVEL ");
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    dtCount = 1;
                    if (dr["ROLE_LEVEL"] == DBNull.Value)
                    {

                        ReturnVal = 5555;
                        dtCount = 0;
                    }
                    else {

                        ReturnVal = (decimal)dr["ROLE_LEVEL"];
                        dtCount = 1;
                    }
                    //ReturnVal = dr["ROLE_LEVEL"] == DBNull.Value ? 5555 : (decimal)dr["ROLE_LEVEL"];
                }
               
                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            if (ReturnVal == 5555 || dtCount == 0) {

                ReturnVal = null;
            }
            return ReturnVal;
        }

        /// <summary>
        /// 判斷我的角色等級是個大於他人
        /// </summary>
        /// <param name="MyROLE_LEVEL">自已角色等級</param>
        /// <param name="HeROLE_LEVEL">他人角色等級</param>
        /// <returns></returns>
        public static bool CompareROLE_LEVEL(decimal? MyROLE_LEVEL, decimal? HeROLE_LEVEL)
        {
            bool ReturnVal = false;
            if (MyROLE_LEVEL == null) return ReturnVal;
            if (HeROLE_LEVEL == null) return ReturnVal;

            if (MyROLE_LEVEL == 0)
            {
                ReturnVal = true;
            }
            else
            {
                if (MyROLE_LEVEL < HeROLE_LEVEL)
                {
                    ReturnVal = true;
                }
            }

            return ReturnVal;
        }

        /// <summary>
        /// 判斷我的角色等級是個大於他人
        /// </summary>
        /// <param name="MyROLE_LEVEL"自已角色等級></param>
        /// <param name="HeSCHOOL_NO">他人SCHOOL_NO</param>
        /// <param name="HeUSER_NO">他人USER_NO</param>
        /// <returns></returns>
        public static bool CompareROLE_LEVEL(decimal? MyROLE_LEVEL, string HeSCHOOL_NO = "", string HeUSER_NO = "")
        {
            return CompareROLE_LEVEL(MyROLE_LEVEL, GetROLE_LEVEL("", HeSCHOOL_NO, HeUSER_NO));
        }

        /// <summary>
        /// 判斷我的角色等級是個大於他人
        /// </summary>
        /// <param name="MyROLE_LEVEL">自已角色等級</param>
        /// <param name="HeUSER_KEY">他人USER_KEY</param>
        /// <returns></returns>
        public static bool CompareROLE_LEVEL(decimal? MyROLE_LEVEL, string HeUSER_KEY = "")
        {
            return CompareROLE_LEVEL(MyROLE_LEVEL, GetROLE_LEVEL(HeUSER_KEY));
        }

        /// <summary>
        /// 判斷我的角色等級是個大於他人
        /// </summary>
        /// <param name="HeSCHOOL_NO">自已SCHOOL_NO</param>
        /// <param name="HeUSER_NO">自已USER_NO</param>
        /// <param name="HeSCHOOL_NO">他人SCHOOL_NO</param>
        /// <param name="HeUSER_NO">他人USER_NO</param>
        /// <returns></returns>
        public static bool CompareROLE_LEVEL(string MySCHOOL_NO = "", string MyUSER_NO = "", string HeSCHOOL_NO = "", string HeUSER_NO = "")
        {
            return CompareROLE_LEVEL(GetROLE_LEVEL("", MySCHOOL_NO, MyUSER_NO), GetROLE_LEVEL("", HeSCHOOL_NO, HeUSER_NO));
        }

        /// <summary>
        /// 判斷我的角色等級是個大於他人
        /// </summary>
        /// <param name="MyUSER_KEY">自已USER_KEY</param>
        /// <param name="HeUSER_KEY">他人USER_KEY</param>
        /// <returns></returns>
        public static bool CompareROLE_LEVEL(string MyUSER_KEY = "", string HeUSER_KEY = "")
        {
            return CompareROLE_LEVEL(GetROLE_LEVEL(MyUSER_KEY), GetROLE_LEVEL(HeUSER_KEY));
        }
    }
}