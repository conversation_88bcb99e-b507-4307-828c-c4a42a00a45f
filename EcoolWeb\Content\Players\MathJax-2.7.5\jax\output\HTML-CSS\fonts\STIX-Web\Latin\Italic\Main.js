/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Latin-italic"]={directory:"Latin/Italic",family:"STIXMathJax_Latin",style:"italic",testString:"\u00A0\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00AD\u00B2\u00B3\u00B6\u00B8\u00B9\u00BA",32:[0,0,250,0,0],160:[0,0,250,0,0],161:[474,205,389,59,321],162:[560,143,500,77,472],164:[534,10,500,-22,522],166:[666,18,275,105,171],169:[666,18,760,41,719],170:[676,-406,276,42,352],171:[403,-37,500,53,445],173:[255,-192,333,49,282],178:[676,-271,300,33,324],179:[676,-268,300,43,339],182:[653,123,559,60,621],184:[0,217,333,-30,182],185:[676,-271,300,43,284],186:[676,-406,310,67,362],187:[403,-37,500,55,447],188:[676,10,750,33,736],189:[676,10,750,34,749],190:[676,10,750,23,736],191:[473,205,500,28,367],192:[914,0,611,-51,564],193:[914,0,611,-51,564],194:[911,0,611,-51,564],195:[874,0,611,-51,572],196:[856,0,611,-51,564],197:[957,0,611,-51,564],198:[653,0,889,-27,911],199:[666,217,667,66,689],200:[914,0,611,-1,634],201:[914,0,611,-1,634],202:[911,0,611,-1,634],203:[856,0,611,-1,634],204:[914,0,333,-8,398],205:[914,0,333,-8,414],206:[911,0,333,-8,450],207:[856,0,333,-8,457],208:[653,0,722,-8,700],209:[874,15,667,-20,727],210:[914,18,722,60,699],211:[914,18,722,60,699],212:[911,18,722,60,699],213:[874,18,722,60,699],214:[856,18,722,60,699],216:[722,105,722,60,699],217:[914,18,722,102,765],218:[914,18,722,102,765],219:[911,18,722,102,765],220:[856,18,722,102,765],221:[914,0,556,78,633],222:[653,0,611,0,569],223:[679,207,500,-168,493],224:[664,11,501,17,476],225:[664,11,501,17,476],226:[661,11,501,17,497],227:[624,11,501,17,521],228:[606,11,501,17,503],229:[709,11,501,17,476],230:[441,11,667,23,640],231:[441,217,444,26,425],232:[664,11,444,31,414],233:[664,11,444,31,431],234:[661,11,444,31,466],235:[606,11,444,31,475],236:[664,11,278,47,302],237:[664,11,278,47,318],238:[661,11,278,47,351],239:[606,11,278,47,361],241:[624,9,500,14,488],242:[664,11,500,27,468],243:[664,11,500,27,468],244:[661,11,500,27,468],245:[624,11,500,27,494],246:[606,11,500,27,474],248:[554,135,500,28,469],249:[664,11,500,42,475],250:[664,11,500,42,475],251:[661,11,500,42,475],252:[606,11,500,42,475],253:[664,206,444,-24,426],254:[683,205,500,-75,469],255:[606,206,444,-24,442],256:[757,0,611,-51,564],257:[543,11,501,17,481],258:[862,0,611,-51,564],259:[650,11,501,17,481],260:[668,169,611,-51,626],261:[441,169,501,17,529],262:[876,18,667,66,689],263:[664,11,444,30,431],264:[875,18,667,66,689],265:[661,11,444,30,427],266:[818,18,667,66,689],267:[606,11,444,30,425],268:[875,18,667,66,689],269:[661,11,444,30,473],270:[875,0,722,-8,700],271:[691,13,609,15,697],272:[653,0,722,-8,700],273:[683,13,500,15,580],274:[757,0,611,-1,634],275:[542,11,444,31,466],276:[866,0,611,-1,634],277:[650,11,444,31,471],278:[818,0,611,-1,634],279:[606,11,444,31,412],280:[653,175,611,-1,634],281:[441,175,444,31,412],282:[875,0,611,-1,634],283:[661,11,444,31,502],284:[877,18,722,52,722],285:[661,206,500,8,471],286:[866,18,722,52,722],287:[650,206,500,8,476],288:[818,18,722,52,722],289:[606,206,500,8,471],290:[666,267,722,52,722],291:[724,206,500,8,471],292:[875,0,722,-8,769],293:[875,9,500,19,478],294:[653,0,722,-8,769],296:[836,0,333,-8,444],297:[624,11,278,30,357],298:[757,0,333,-8,439],299:[543,11,278,29,341],300:[866,0,333,-8,448],301:[650,11,278,46,347],302:[653,169,333,-8,384],303:[654,169,278,49,303],304:[818,0,333,-8,384],306:[653,18,750,-8,783],307:[654,207,500,49,500],308:[877,18,444,-6,536],309:[661,207,278,-124,353],310:[653,267,667,7,722],311:[683,267,444,14,461],312:[459,0,542,5,601],313:[876,0,556,-8,559],314:[876,11,278,41,348],315:[653,267,556,-8,559],316:[683,267,278,7,279],317:[666,0,556,-8,595],318:[693,11,278,41,448],319:[653,0,556,-8,559],320:[683,11,323,41,386],321:[653,0,556,-8,559],322:[683,11,278,37,307],323:[876,15,667,-20,727],324:[664,9,500,14,474],325:[653,267,667,-20,727],326:[441,267,500,14,474],327:[875,15,667,-20,727],328:[661,9,500,14,475],329:[691,9,577,58,540],330:[666,18,722,-8,700],331:[441,208,500,14,442],332:[757,18,722,60,699],333:[543,11,500,27,511],334:[866,18,722,60,709],335:[650,11,500,27,533],336:[876,18,722,60,720],337:[664,11,500,27,541],338:[666,8,944,49,964],339:[441,12,667,20,646],340:[876,0,611,-13,588],341:[664,0,389,45,412],342:[653,267,611,-13,588],343:[441,267,389,-2,412],344:[875,0,611,-13,588],345:[663,0,389,45,426],346:[876,18,500,17,508],347:[664,13,389,16,403],348:[877,18,500,17,508],349:[661,13,389,16,385],350:[667,217,500,17,508],351:[442,217,389,16,366],352:[875,18,500,17,532],353:[663,13,389,16,426],354:[653,217,556,59,633],355:[546,217,278,-38,296],356:[875,0,556,59,633],357:[693,11,278,38,453],358:[653,0,556,59,633],359:[546,11,278,28,296],360:[836,18,722,102,765],361:[624,11,500,42,475],362:[757,18,722,102,765],363:[543,11,500,42,475],364:[866,18,722,102,765],365:[650,11,500,42,480],366:[907,18,722,102,765],367:[691,11,500,42,475],368:[876,18,722,102,765],369:[664,11,500,42,511],370:[653,169,722,102,765],371:[441,169,500,42,538],372:[877,18,833,71,906],373:[661,18,667,15,648],374:[877,0,556,78,633],375:[661,206,444,-24,426],376:[818,0,556,78,633],377:[876,0,556,-6,606],378:[664,81,389,-2,390],379:[818,0,556,-6,606],380:[606,81,389,-2,380],381:[875,0,556,-6,606],382:[663,81,389,-2,426],383:[683,0,383,13,513],384:[683,11,500,23,473],392:[548,11,500,30,577],400:[684,6,667,66,671],402:[706,159,472,-62,494],405:[683,10,672,19,654],409:[683,11,500,14,490],410:[683,11,278,41,279],411:[668,0,490,30,478],414:[441,233,500,14,442],416:[691,18,722,60,783],417:[467,11,534,27,583],421:[669,205,504,-75,472],426:[684,233,340,31,319],427:[546,218,278,-54,296],429:[683,11,310,38,452],431:[765,18,754,102,881],432:[543,11,573,42,607],442:[450,234,500,8,462],443:[676,0,500,12,500],446:[539,12,500,47,453],448:[736,0,170,15,258],449:[736,0,290,15,379],450:[736,0,340,15,429],451:[667,11,333,39,304],496:[661,207,278,-124,397],506:[950,0,611,-51,564],507:[860,11,501,17,476],508:[876,0,889,-27,911],509:[664,11,667,23,640],510:[876,105,722,60,699],511:[664,135,500,28,469],7808:[880,18,833,71,906],7809:[664,18,667,15,648],7810:[876,18,833,71,906],7811:[664,18,667,15,648],7812:[818,18,833,71,906],7813:[606,18,667,15,648],7922:[880,0,556,78,633],7923:[664,206,444,-24,426],64256:[678,207,527,-147,673],64257:[681,207,500,-141,481],64258:[682,204,500,-141,518],64259:[681,207,744,-147,725],64260:[682,207,745,-147,763]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Latin-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/Italic/Main.js"]);
