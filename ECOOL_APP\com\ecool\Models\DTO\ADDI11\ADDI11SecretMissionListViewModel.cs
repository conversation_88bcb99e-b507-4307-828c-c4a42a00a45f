﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI11SecretMissionListViewModel
    {
        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///項次
        /// </summary>
        [DisplayName("項次")]
        public int? ITEM_NO { get; set; }

        /// <summary>
        ///里程數 (大於)
        /// </summary>
        [DisplayName("里程數(公里)")]
        public decimal? RUN_TOTAL_KM { get; set; }

        /// <summary>
        ///任務內容
        /// </summary>
        [DisplayName("任務內容")]
        public string TASK_DESC { get; set; }

        /// <summary>
        ///任務內容
        /// </summary>
        [DisplayName("任務內容")]
        public string TASK_MAINDESC { get; set; }

        /// <summary>
        ///是否需回答一段文字
        /// </summary>
        [DisplayName("是否需回答一段文字")]
        public bool? IS_TEXT { get; set; }

        /// <summary>
        ///是否上傳而上傳的數量
        /// </summary>
        [DisplayName("是否上傳而上傳的數量")]
        public byte? FILE_COUNT { get; set; }

        /// <summary>
        /// 是否可執行此任務
        /// </summary>
        public bool IsTask { get; set; }

        /// <summary>
        /// 是否完成此任務
        /// </summary>
        public bool IsExecutedTask { get; set; }
    }
}