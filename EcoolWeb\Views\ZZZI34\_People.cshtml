﻿@model ZZZI34EditPeopleViewModel
@using ECOOL_APP.com.ecool.service
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
@using (Html.BeginCollectionItem("People"))
{

    ECOOL_DEVEntities db = new ECOOL_DEVEntities();

    var index = Html.GetIndex("People");


    <div class="panel panel-default" id="@index">
        <div class="panel-body">
            @Html.HiddenFor(m => m.PHOTO_SCHOOL_NO)
            @Html.HiddenFor(m => m.ART_GALLERY_TYPE)
            @Html.HiddenFor(m => m.ShowBtn)

            @if (Model.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
            {
                if (ViewBag.ClassItems == null)
                {
                    ViewBag.ClassItems = HRMT01.GetClassListData(Model.PHOTO_SCHOOL_NO, Model.PHOTO_CLASS_NO, ref db);
                }

                if (ViewBag.UserNoItems == null)
                {
                    ViewBag.UserNoItems = HRMT01.GetUserNoListDataMust(Model.PHOTO_SCHOOL_NO, Model.PHOTO_CLASS_NO, Model.PHOTO_USER_NO, ref db);
                }

                <div class="row mb-3">
                    <label class="col-5 col-md-3 col-lg-3 control-label d-flex align-items-center mb-3">
                        <font color="red">*</font>
                        班級
                    </label>
                    <div class="col-7 col-md-9 col-lg-3 d-flex align-items-center mb-3">
                        <div class="input-group">
                            @Html.DropDownListFor(m => m.PHOTO_CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm rounded", @onchange = "ChangeUSER_NOUseReplaceWith(this.value,'" + Html.IdFor(m => m.PHOTO_USER_NO) + "','" + Html.NameFor(m => m.PHOTO_USER_NO).ToHtmlString() + "')" })
                        </div>
                        @Html.ValidationMessageFor(m => m.PHOTO_CLASS_NO, "", new { @class = "text-danger" })
                    </div>
                    <label class="col-5 col-md-3 col-lg-3 control-label d-flex align-items-center mb-3">
                        <font color="red">*</font>
                        姓名/座號
                    </label>
                    <div class="col-7 col-md-9 col-lg-3 d-flex align-items-center mb-3">
                        <div class="input-group">
                            @Html.DropDownListFor(m => m.PHOTO_USER_NO, (IEnumerable<SelectListItem>)ViewBag.UserNoItems, new { @class = "form-control input-sm rounded" })
                        </div>
                        @Html.ValidationMessageFor(m => m.PHOTO_USER_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="row mb-3">
                    <label class="col-5 col-md-3 control-label">
                        <font color="red">*</font>
                        是否顯示作者
                    </label>
                    <div class="col-7 col-md-9">
                        <div class="input-group">
                            <label class="mr-3">
                                @Html.RadioButtonFor(model => model.AutherYN, false, new { @id = "AutherYN" })
                                出現作者姓名
                            </label>
                            <label>
                                @Html.RadioButtonFor(model => model.AutherYN, true, new { @id = "AutherYN" })
                                不出現作者姓名
                            </label>
                        </div>
                    </div>
                </div>
            }
            else
            {
                @Html.HiddenFor(m => m.PHOTO_CLASS_NO)
                @Html.HiddenFor(m => m.PHOTO_USER_NO)
                <div class="row mb-3">
                    <label class="col-3 control-label">
                        <font color="red">*</font>
                        班級/姓名
                    </label>
                    <div class="col-9 flex-grow-0">
                        <div class="input-group">
                            @Html.DisplayFor(m => m.PHOTO_CLASS_NO, new { @class = "form-control input-sm rounded" })/
                            @if (user.USER_TYPE == "S" && !string.IsNullOrWhiteSpace(user.SNAME) == true)
                            {
                                @user.SNAME
                            }
                            else
                            {

                                @Html.DisplayFor(m => m.PHOTO_SNAME, new { @class = "form-control input-sm rounded" })
                            }
                        </div>
                    </div>
                </div>
                <div class="d-flex mb-3">
                    <label class="col-3 control-label">
                        <font color="red">*</font>
                        上傳時間
                    </label>
                    <div class="col-9 flex-grow-0">
                        <div class="input-group">
                            @{ string CHGDATE = "";}

                            @if (Model.PHOTO_CHG_DATE != null)
                            {

                                CHGDATE = ((DateTime)Model.PHOTO_CHG_DATE).ToShortDateString() + " " + ((DateTime)Model.PHOTO_CHG_DATE).ToShortTimeString();
                            }
                            else
                            {
                                CHGDATE = DateTime.Now.ToShortDateString();
                            }
                            @CHGDATE
                        </div>

                    </div>
                </div>
                <div class="d-flex mb-3">
                    <label class="col-3 control-label">
                        <font color="red">*</font>
                        是否顯示作者
                    </label>
                    <div class="col-9 flex-grow-0">
                        <div class="input-group">
                            @if (Model.AutherYN == null)
                            {
                                Model.AutherYN = false;
                            }
                            <label class="mr-3">
                                @Html.RadioButtonFor(model => model.AutherYN, false, new { @id = "AutherYN" })
                                出現作者姓名
                            </label>
                            <label>
                                @Html.RadioButtonFor(model => model.AutherYN, true, new { @id = "AutherYN" })
                                不出現作者姓名
                            </label>
                        </div>
                    </div>
                </div>
            }
            <div id="editorPhotoRows_@index">
                @if (Model.Photo != null)
                {
                    foreach (var PhotoItem in Model.Photo)
                    {
                        PhotoItem.index = index;
                        PhotoItem.ShowBtn = Model.ShowBtn;
                        @Html.Partial("_Photo", PhotoItem)
                    }
                }
            </div>
            @if (Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True)
            {
                <div class="text-right" id="Btn_@index">
                    <button type="button" class="btn btn-default btn-sm" onclick="BtnOnAddInput('@index')">
                        新增作品
                    </button>
                </div>
            }
            <div class="text-right pr-3 mr-1">
                @if (Model.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True && Model.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                {
                    <a role="button" class="btn btn-danger btn-sm glyphicon glyphicon-trash" onclick="onDelItem('@index')" title="刪除"></a>
                }
            </div>

        </div>
    </div>




}