﻿@model AWAI07DescViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<div class="text-center">
    <h3>利率表 </h3>
    <table class="table-ecool table-92Per table-hover  table-bordered">
        <thead>
            <tr style="background-color:cornflowerblue">
                <th class="text-center">定存期別</th>
                <th class="text-center">年利率</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.PeriodTypeList)
            {
                <tr>
                    <td>@Html.DisplayFor(modelItem => item.PERIOD_DESC)</td>
                    <td>@item.PERIOD_RATE.Value.ToString("P")</td>
                </tr>
            }
        </tbody>
    </table>

</div>
<div>
    *本功能為坊間銀行定存的簡易版，主要目的為了讓學生感受定存理財的概念。</br>
    *本酷幣定存之年利率遠高於坊間銀行，原因為學生酷幣點數不高，若比照坊間利率，可能利息趨近於零，達不到定存效果，故提高年利率實施。
</div>

