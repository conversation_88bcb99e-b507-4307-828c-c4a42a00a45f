﻿@model GameStatisticsAnsViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "StatisticsAns" });
    }
}

@using (Html.BeginForm("StatisticsAns", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    <div id="tbData">
        <div class="panel panel-danger">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <strong>活動名稱：@Model.GameInfo.GAME_NAME</strong>
                </h3>
            </div>
            <div class="panel-body">
                <div class="Caption_Div">
                    全部題目-答對答錯比例<br>
                    <h4 style="color:red">全部回答@(Model.AnsTotalNum)人次，全部答對@(Model.AnsTrueNum)人次，占全部的@(Model.AnsTruerRate)%</h4>
                </div>
                <div class="table-92Per" style="margin: 0px auto; ">
                    <div class="progress" href="@Url.Action("TotalGraphDetails", (string)ViewBag.BRE_NO,new {  GAME_NO= Model.GameInfo.GAME_NO,IsSeeGroupId =false })">
                        <div class="progress-bar progress-bar-info" style="width: @(Model.AnsTruerRate)% ;min-width: 6em;">
                            @(Model.AnsTrueNum)人次(@(Model.AnsTruerRate)%)
                        </div>
                        <div class="progress-bar progress-bar-gray" style="width: @(Model.AnsFalseNRate)% ;min-width: 6em;">
                            @(Model.AnsFalseNum)人次(@(Model.AnsFalseNRate)%)
                        </div>
                    </div>
                </div>

                <div style="height:15px"></div>
                <hr style="color:red">

                <div class="table-92Per" style="margin: 0px auto; ">
                    @if (Model.Rate?.Count > 0)
                    {
                        foreach (var item in Model.Rate)
                        {
                            <div class="form-group">
                                <strong class="label_dt_S">
                                    Q@(item.G_ORDER_BY).
                                    @Html.Raw(HttpUtility.HtmlDecode(item.G_SUBJECT))
                                </strong>
                            </div>

                            foreach (var ThisRate in item.RateData)
                            {

                                string bar = (ThisRate.TRUE_ANS ?? false) ? "progress-bar-info" : "progress-bar-gray";

                                <div style="height:15px"></div>
                                <label>
                                    @ThisRate.LEVEL_NAME
                                </label>
                                <br />
                                <div class="progress" href="@Url.Action("TotalGraphDetails",new {   GAME_NO= Model.GameInfo.GAME_NO,  GROUP_ID=ThisRate.GROUP_ID,  LEVEL_NO = ThisRate.LEVEL_NO,IsSeeGroupId =true  })">
                                    <div class="progress-bar @(bar)" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                         style="width: @(ThisRate.AnsRate)%;min-width: 6em;">
                                        @(ThisRate.AnsNum)人次(@(ThisRate.AnsRate)%)
                                    </div>
                                </div>
                            }

                        }
                    }
                </div>
            </div>
        </div>
    </div>

}

@section Scripts {
    <script language="JavaScript">

        $(document).ready(function () {
            $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
        });
    </script>
}