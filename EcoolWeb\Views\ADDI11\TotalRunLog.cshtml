﻿@model global::ECOOL_APP.EF.ADDI11TotalRunLogViewModel

@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}

@helper RunTableDisplay()
{

    if (Model.runLogViewType == TotalRunLogType.全校跑步情形)
    {
        <table class="table table-hover table-bordered table-responsive runtable">
            <thead>
                <tr class="text-primary">
                    <th colspan="3">
                        本校跑步情況
                    </th>
                </tr>
                <tr class="text-success">
                    <th>
                        @(Model.whereRUN_YEAR)年 /
                        @(Model.whereRUN_MONTH)月
                    </th>
                    <th>班級</th>
                    <th>跑步人數(學生)</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.TotalRunLogList)
                {
                    string runDetails = "";
                    int i = 1; int lastIndex = item.RUNNERS.Count();
                    foreach (var runner in item.RUNNERS)
                    {
                        runDetails += string.Format("{0}. {1} <span class='text-danger'>{2}圈</span>", i, runner.NAME, runner.LAP);
                        if (i != lastIndex) { runDetails += " 　"; }
                        if (i % 4 == 0) { runDetails += "<br>"; }
                        i++;
                    }
                    <tr class="trHover" title="跑步學生名單" data-toggle="popover" data-html="true" data-trigger="hover" data-placement="bottom" data-content="@runDetails">
                        <td>
                            @item.RUN_DATE.ToString("yyyy/MM/dd")
                            <a href="@Url.Action("Edit","ADDI11", new {SearchCLASS_NO= item.CLASS_NO, RunDate = item.RUN_DATE })" class="btn btn-xs btn-success">
                                查看
                            </a>
                        </td>
                        <td>@item.CLASS_NO</td>
                        <td class="text-danger">@item.RUN_COUNT</td>
                    </tr>

                }
            </tbody>
        </table>
    }
    else if (Model.runLogViewType == TotalRunLogType.本班跑步情形)
    {
        <table class="table table-hover table-bordered table-responsive runtable">
            <thead>
                <tr class="text-primary">
                    <th colspan="3">
                        @(Model.whereCLASS_NO)班 跑步情況
                    </th>
                </tr>
                <tr class="text-success">
                    <th>
                        @(Model.whereRUN_YEAR)年 /
                        @(Model.whereRUN_MONTH)月
                    </th>
                    <th>班級</th>
                    <th>跑步人數(學生)</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.TotalRunLogList)
                {

                    string runDetails = "";
                    int i = 1; int lastIndex = item.RUNNERS.Count();
                    foreach (var runner in item.RUNNERS)
                    {
                        runDetails += string.Format("{0}. {1} <span class='text-danger'>{2}圈</span>", i, runner.NAME, runner.LAP);
                        if (i != lastIndex) { runDetails += " 　"; }
                        if (i % 4 == 0) { runDetails += "<br>"; }
                        i++;
                    }
                    <tr class="trHover" title="跑步學生名單" data-toggle="popover" data-html="true" data-trigger="hover" data-placement="bottom" data-content="@runDetails">
                        <td>
                            @item.RUN_DATE.ToString("yyyy/MM/dd")
                            <a href="@Url.Action("Edit","ADDI11", new {SearchCLASS_NO= item.CLASS_NO, RunDate = item.RUN_DATE })" class="btn btn-xs btn-success">
                                查看
                            </a>
                        </td>
                        <td>
                            @item.CLASS_NO
                        </td>
                        <td class="text-danger">@item.RUN_COUNT</td>
                    </tr>
                }
            </tbody>
        </table>
    }
}


@{
    string nowAction = "";
    if (Model.runLogViewType == TotalRunLogType.全校跑步情形)
    {
        nowAction = "TotalRunLog";
    }
    if (Model.runLogViewType == TotalRunLogType.本班跑步情形)
    {
        nowAction = "TotalClassRunLog";
    }

}
@if (AppMode == false)
{
@Html.Partial("_Title_Secondary")
}
@{ Html.RenderAction("_RunMenu", new { NowAction = nowAction }); }

@using (Html.BeginForm(nowAction, "ADDI11", FormMethod.Get, new { id = "totalRunForm" }))
{
    @Html.HiddenFor(m => m.whereCLASS_NO)
    @Html.HiddenFor(m => m.Page)

    @Html.DropDownListFor(m => m.whereRUN_YEAR, (IEnumerable<SelectListItem>)Model.YearDropDown, new { @class = "drop", onchange = "this.form.submit();" })
    @Html.DropDownListFor(m => m.whereRUN_MONTH, (IEnumerable<SelectListItem>)Model.MonthDropDown, new { @class = "drop", onchange = "this.form.submit();" })

    @RunTableDisplay()

    @Html.Pager(Model.PageSize, Model.TotalRunLogList.PageNumber, Model.TotalRunLogList.TotalItemCount).Options(o => o
         .DisplayTemplate("BootstrapPagination")
         .MaxNrOfPages(5)
         .SetPreviousPageText("上頁")
         .SetNextPageText("下頁")
     )
}




@section css{
    <style>
        .runtable {
            background-color: white;
            padding: 2%;
        }

        .drop {
            font-size: 15px;
            padding: 5px;
            margin: 5px;
        }

        .trHover:hover {
            cursor: pointer;
        }

        .popover {
            max-width: 90%;
        }
    </style>
}
@section scripts{
    <script>
        var targetFormID = "#totalRunForm";
        // 換頁處理
        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        $('[data-toggle="popover"]').popover();
    </script>
}