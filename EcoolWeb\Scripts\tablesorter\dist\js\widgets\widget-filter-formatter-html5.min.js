(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: filter, html5 formatter functions - updated 7/17/2014 (v2.17.5) */
!function(u){"use strict";var f=u.tablesorter||{},m=".compare-select",h=f.filterFormatter=u.extend({},f.filterFormatter,{addCompare:function(e,t,a){if(a.compare&&u.isArray(a.compare)&&1<a.compare.length){var n="",i=[m.slice(1)," "+m.slice(1),""],l=a.cellText?'<label class="'+i.join("-label")+t+'">'+a.cellText+"</label>":"";u.each(a.compare,function(e,t){n+="<option "+(a.selected===e?"selected":"")+">"+t+"</option>"}),e.wrapInner('<div class="'+i.join("-wrapper")+t+'" />').prepend(l+'<select class="'+i.join("")+t+'" />').find("select").append(n)}},updateCompare:function(e,t,a){var n=t.val()||"",i=n.replace(/\s*?[><=]\s*?/g,""),l=n.match(/[><=]/g)||"";return a.compare&&(u.isArray(a.compare)&&(l=(l||[]).join("")||a.compare[a.selected||0]),e.find(m).val(l)),[n,i]},html5Number:function(d,t,e){var a,r,c=u.extend({value:0,min:0,max:100,step:1,delayed:!0,disabled:!1,addToggle:!1,exactMatch:!1,cellText:"",compare:"",skipTest:!1},e),n=u('<input type="number" style="visibility:hidden;" value="test">').appendTo(d),i=c.skipTest||"number"===n.attr("type")&&"test"!==n.val(),o=[],s=d.closest("table")[0].config,l=function(e,t){var a=!c.addToggle||d.find(".toggle").is(":checked"),n=d.find(".number").val(),i=(u.isArray(c.compare)?d.find(m).val()||c.compare[c.selected||0]:c.compare)||"",l=!s.$table[0].hasInitialized||(e||c.delayed||"");r.val(!c.addToggle||a?(i||(c.exactMatch?"=":""))+n:"").trigger(t?"":"search",l).end().find(".number").val(n),d.find(".number").length&&(d.find(".number")[0].disabled=c.disabled||!a),o.length&&(o.find(".number").val(n)[0].disabled=c.disabled||!a,o.find(m).val(i),c.addToggle&&(o.find(".toggle")[0].checked=a))};return n.remove(),i&&(a=c.addToggle?'<div class="button"><input id="html5button'+t+'" type="checkbox" class="toggle" /><label for="html5button'+t+'"></label></div>':"",a+='<input class="number" type="number" min="'+c.min+'" max="'+c.max+'" value="'+c.value+'" step="'+c.step+'" />',d.append(a+'<input type="hidden" />').find(".toggle, .number").bind("change",function(){l()}).closest("thead").find("th[data-column="+t+"]").addClass("filter-parsed").closest("table").bind("filterReset",function(){u.isArray(c.compare)&&d.add(o).find(m).val(c.compare[c.selected||0]),c.addToggle&&(d.find(".toggle")[0].checked=!1,o.length&&(o.find(".toggle")[0].checked=!1)),d.find(".number").val(c.value),setTimeout(function(){l()},0)}),r=d.find("input[type=hidden]").bind("change",function(){d.find(".number").val(this.value),l()}),s.$table.bind("filterFomatterUpdate",function(){var e=h.updateCompare(d,r,c)[0]||c.value;d.find(".number").val(((e||"")+"").replace(/[><=]/g,"")),l(!1,!0),f.filter.formatterUpdated(d,t)}),c.compare&&(h.addCompare(d,t,c),d.find(m).bind("change",function(){l()})),s.$table.bind("stickyHeadersInit",function(){(o=s.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(t).empty()).append(a).find(".toggle, .number").bind("change",function(){d.find(".number").val(u(this).val()),l()}),c.compare&&(h.addCompare(o,t,c),o.find(m).bind("change",function(){d.find(m).val(u(this).val()),l()})),l()}),l()),i?d.find('input[type="hidden"]'):u('<input type="search">')},html5Range:function(d,r,e){var c,o=u.extend({value:0,min:0,max:100,step:1,delayed:!0,valueToHeader:!0,exactMatch:!0,cellText:"",compare:"",allText:"all",skipTest:!1},e),t=u('<input type="range" style="visibility:hidden;" value="test">').appendTo(d),a=o.skipTest||"range"===t.attr("type")&&"test"!==t.val(),s=[],p=d.closest("table")[0].config,n=function(e,t,a){e=(void 0===e?c.val():e).toString().replace(/[<>=]/g,"")||o.value;var n=(u.isArray(o.compare)?d.find(m).val()||o.compare[o.selected||0]:o.compare)||"",i=" ("+(n?n+e:e==o.min?o.allText:e)+")",l=!p.$table[0].hasInitialized||(t||o.delayed||"");d.find("input[type=hidden]").val(n?n+e:e==o.min?"":(o.exactMatch?"=":"")+e).trigger(a?"":"search",l).end().find(".range").val(e),d.closest("thead").find("th[data-column="+r+"]").find(".curvalue").html(i),s.length&&(s.find(".range").val(e).end().find(m).val(n),s.closest("thead").find("th[data-column="+r+"]").find(".curvalue").html(i))};return t.remove(),a&&(d.html('<input type="hidden"><input class="range" type="range" min="'+o.min+'" max="'+o.max+'" value="'+o.value+'" />').closest("thead").find("th[data-column="+r+"]").addClass("filter-parsed").find(".tablesorter-header-inner").append('<span class="curvalue" />'),c=d.find("input[type=hidden]").bind("change"+p.namespace+"filter",function(){var e=this.value,t=(u.isArray(o.compare)?d.find(m).val()||o.compare[o.selected||0]:o.compare)||"";e!==this.lastValue&&(this.lastValue=t?t+e:e==o.min?"":(o.exactMatch?"=":"")+e,this.value=this.lastValue,n(e))}),d.find(".range").bind("change",function(){n(this.value)}),p.$table.bind("filterFomatterUpdate",function(){var e=h.updateCompare(d,c,o)[0];d.find(".range").val(e),n(e,!1,!0),f.filter.formatterUpdated(d,r)}),o.compare&&(h.addCompare(d,r,o),d.find(m).bind("change",function(){n()})),p.$table.bind("stickyHeadersInit",function(){(s=p.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(r).empty()).html('<input class="range" type="range" min="'+o.min+'" max="'+o.max+'" value="'+o.value+'" />').find(".range").bind("change",function(){n(s.find(".range").val())}),n(),o.compare&&(h.addCompare(s,r,o),s.find(m).bind("change",function(){d.find(m).val(u(this).val()),n()}))}),d.closest("table").bind("filterReset",function(){u.isArray(o.compare)&&d.add(s).find(m).val(o.compare[o.selected||0]),setTimeout(function(){n(o.value,!1,!0)},0)}),n()),a?d.find('input[type="hidden"]'):u('<input type="search">')},html5Color:function(i,l,e){var t,d,r=u.extend({value:"#000000",disabled:!1,addToggle:!0,exactMatch:!0,valueToHeader:!1,skipTest:!1},e),a=u('<input type="color" style="visibility:hidden;" value="test">').appendTo(i),n=r.skipTest||"color"===a.attr("type")&&"test"!==a.val(),c=[],o=i.closest("table")[0].config,s=function(e,t){var a=!0,n=" ("+(e=(void 0===e?d.val():e).toString().replace("=","")||r.value)+")";r.addToggle&&(a=i.find(".toggle").is(":checked")),i.find(".colorpicker").length&&(i.find(".colorpicker").val(e)[0].disabled=r.disabled||!a),d.val(a?e+(r.exactMatch?"=":""):"").trigger(!o.$table[0].hasInitialized||t?"":"search"),r.valueToHeader?i.closest("thead").find("th[data-column="+l+"]").find(".curcolor").html(n):i.find(".currentColor").html(n),c.length&&(c.find(".colorpicker").val(e)[0].disabled=r.disabled||!a,r.addToggle&&(c.find(".toggle")[0].checked=a),r.valueToHeader?c.closest("thead").find("th[data-column="+l+"]").find(".curcolor").html(n):c.find(".currentColor").html(n))};return a.remove(),n&&(t=""+l+Math.round(100*Math.random()),t='<div class="color-controls-wrapper">'+(r.addToggle?'<div class="button"><input id="colorbutton'+t+'" type="checkbox" class="toggle" /><label for="colorbutton'+t+'"></label></div>':"")+'<input type="hidden"><input class="colorpicker" type="color" />'+(r.valueToHeader?"":'<span class="currentColor">(#000000)</span>')+"</div>",i.html(t),r.valueToHeader&&i.closest("thead").find("th[data-column="+l+"]").find(".tablesorter-header-inner").append('<span class="curcolor" />'),i.find(".toggle, .colorpicker").bind("change",function(){s(i.find(".colorpicker").val())}),d=i.find("input[type=hidden]").bind("change"+o.namespace+"filter",function(){s(this.value)}),o.$table.bind("filterFomatterUpdate",function(){s(d.val(),!0),f.filter.formatterUpdated(i,l)}),i.closest("table").bind("filterReset",function(){r.addToggle&&(i.find(".toggle")[0].checked=!1),setTimeout(function(){s()},0)}),o.$table.bind("stickyHeadersInit",function(){(c=o.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(l)).html(t).find(".toggle, .colorpicker").bind("change",function(){s(c.find(".colorpicker").val())}),s(c.find(".colorpicker").val())}),s(r.value)),n?i.find('input[type="hidden"]'):u('<input type="search">')}})}(jQuery);return jQuery;}));
