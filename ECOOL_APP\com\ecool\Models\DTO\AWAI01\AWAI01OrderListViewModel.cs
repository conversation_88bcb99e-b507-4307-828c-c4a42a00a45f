﻿
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public  class AWAI01OrderListViewModel
    {
        /// <summary>
        /// 獎品狀態 選單 for Query
        /// </summary>
        public List<SelectListItem> AwardTypeItemQuery { get; set; }

        /// <summary>
        /// 獎品類別
        /// </summary>
        public List<SelectListItem> AwardSchoolItemQuery { get; set; }


        public AWAI01SearchViewModel Search { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }


        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<AWAI01IndexListViewModel> ListData;

        public AWAI01OrderListViewModel()
        {
            PageSize = int.MaxValue;
        }
    }
}