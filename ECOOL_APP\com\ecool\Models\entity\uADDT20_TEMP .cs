﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT20_TEMP
    {

        ///Summary
        ///SESSION_ID
        ///Summary
        [DisplayName("SESSION_ID")]
        public string SESSION_ID { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("SCHOOL_NO")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("USER_NO")]
        public string USER_NO { get; set; }

        ///Summary
        ///CRE_PERSON
        ///Summary
        [DisplayName("CRE_PERSON")]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
        [DisplayName("CRE_DATE")]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///MEMO
        ///Summary
        [DisplayName("備註")]
        public string MEMO { get; set; }

    }

}
