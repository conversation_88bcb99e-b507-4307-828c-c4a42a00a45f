﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'ja', {
	access: 'スクリプトアクセス(AllowScriptAccess)',
	accessAlways: 'すべての場合に通信可能(Always)',
	accessNever: 'すべての場合に通信不可能(Never)',
	accessSameDomain: '同一ドメインのみに通信可能(Same domain)',
	alignAbsBottom: '下部(絶対的)',
	alignAbsMiddle: '中央(絶対的)',
	alignBaseline: 'ベースライン',
	alignTextTop: 'テキスト上部',
	bgcolor: '背景色',
	chkFull: 'フルスクリーン許可',
	chkLoop: 'ループ再生',
	chkMenu: 'Flashメニュー可能',
	chkPlay: '再生',
	flashvars: 'フラッシュに渡す変数(FlashVars)',
	hSpace: '横間隔',
	properties: 'Flash プロパティ',
	propertiesTab: 'プロパティ',
	quality: '画質',
	qualityAutoHigh: '自動/高',
	qualityAutoLow: '自動/低',
	qualityBest: '品質優先',
	qualityHigh: '高',
	qualityLow: '低',
	qualityMedium: '中',
	scale: '拡大縮小設定',
	scaleAll: 'すべて表示',
	scaleFit: '上下左右にフィット',
	scaleNoBorder: '外が見えない様に拡大',
	title: 'Flash プロパティ',
	vSpace: '縦間隔',
	validateHSpace: '横間隔は数値で入力してください。',
	validateSrc: 'リンクURLを入力してください。',
	validateVSpace: '縦間隔は数値で入力してください。',
	windowMode: 'ウィンドウモード',
	windowModeOpaque: '背景を不透明設定',
	windowModeTransparent: '背景を透過設定',
	windowModeWindow: '標準'
} );
