﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI11RunMapViewModel
    {
        public string WhereSCHOOL_NO { get; set; }

        public string WhereUSER_NO { get; set; }

        public string WhereCLASS_NO { get; set; }
        public string WhereIsColorboxForUser { get; set; }
        public string WhereKM { get; set; }
        /// <summary>
        ///總累計里程(m)
        /// </summary>
        [DisplayName("總累計里程(m)")]
        public double? RUN_TOTAL_METER { get; set; }

        [DisplayName("跑步地圖路徑")]
        public string RUN_IMG_PATH { get; set; }

        public string LOCATION_NAME { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }
        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }
        public double? Range { get; set; }
        public string NextStop { get; set; }
    }
}