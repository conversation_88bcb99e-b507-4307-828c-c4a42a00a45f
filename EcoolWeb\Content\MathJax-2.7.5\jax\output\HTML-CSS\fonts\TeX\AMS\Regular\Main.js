/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_AMS={directory:"AMS/Regular",family:"MathJax_AMS",testString:"MATHJAX AMS \u02C6",Ranges:[[0,127,"BBBold"],[128,255,"Latin1Supplement"],[256,383,"LatinExtendedA"],[688,767,"SpacingModLetters"],[768,879,"CombDiacritMarks"],[880,1023,"GreekAndCoptic"],[8192,8303,"GeneralPunctuation"],[8448,8527,"LetterlikeSymbols"],[8592,8703,"Arrows"],[8704,8959,"MathOperators"],[8960,9215,"MiscTechnical"],[9312,9471,"EnclosedAlphanum"],[9472,9599,"BoxDrawing"],[9632,9727,"GeometricShapes"],[9728,9983,"MiscSymbols"],[9984,10175,"Dingbats"],[10624,10751,"MiscMathSymbolsB"],[10752,11007,"SuppMathOperators"],[57344,63743,"PUA"]]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_AMS"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/AMS/Regular/Main.js"]);
