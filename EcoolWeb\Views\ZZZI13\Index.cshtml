﻿@model IEnumerable<ECOOL_APP.com.ecool.Models.DTO.ZZZI13ViewModel>
@{
    ViewBag.Title = ViewBag.Panel_Title;

    int num = 0;
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id= "form1", name = "form1" }))
{
    @Html.Hidden("IsTeacher")
    <br />
    <div class="form-inline" role="form" id="Q_Div">
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("SCHOOL_NO", (IEnumerable<SelectListItem>)ViewBag.SchoolNoItems, new { @class = "form-control input-sm", @onchange = "SelectQ();" })
        </div>
        <div class="form-group">
            <label class="control-label">姓名</label>
        </div>
        <div class="form-group">
            @Html.Editor("NAME", null, new { htmlAttributes = new { @class = "form-control" } })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="SelectQ()"  />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <br />
    <br>
    <div>
        <div class="form-inline" style="text-align:right">
            <button class="btn btn-xs btn-pink  @(ViewBag.IsTeacher==false ? "active":"")" type="button" onclick="doSearch('@Html.Id("IsTeacher")', 'false');">全部</button>
            <button class="btn btn-xs btn-pink  @(ViewBag.IsTeacher==true ? "active":"")" type="button" onclick="doSearch('@Html.Id("IsTeacher")', 'true');">導師</button>
        </div>
    </div>
    <div class="panel panel-ACC">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                <thead>
                    <tr>
                  
                            <th>班級</th>
                       
                        <th>姓名</th>
                        <th>角色</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        num = 1;
                        <tr>
                         
                                <td align="center">@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                            
                            <td align="center" style="white-space:nowrap">@Html.DisplayFor(modelItem => item.NAME)</td>
                            <td>
                               
                                    @foreach (var D_Item in item.Details_List)
                                    {

                                        string CheckBoxName = item.SCHOOL_NO + '_' + item.USER_NO + '_' + D_Item.ROLE_ID;

                                        if ((item.SCHOOL_NO == ViewBag.MY_SCHOOL_NO && item.USER_NO == ViewBag.MY_USER_NO && D_Item.ROLE_ID == ViewBag.MY_ROLE_ID))
                                        {
                                            string titleValue;

                                            titleValue = "自已無法異動自已最高權限角色。需要比你更高權限者，才能異動你的角色。";
                                            <div class="checkbox-inline">
                                                @Html.CheckBox(CheckBoxName, D_Item.Checked, new { @disabled = "disabled", @title = titleValue, @class = "initialism" })
                                                <abbr class="initialism" title="@titleValue" style="color:#A9A9A9">
                                                    @Html.DisplayFor(modelItem => D_Item.ROLE_NAME, new { @title = titleValue, @class = "initialism" })
                                                </abbr>
                                            </div> 
                                        }
                                        else
                                        {
                                            <div class="checkbox-inline">
                                                @Html.CheckBox(CheckBoxName, D_Item.Checked, new { @id = CheckBoxName, @onclick = "DbSave('" + item.SCHOOL_NO + "','" + item.USER_NO + "','" + D_Item.ROLE_ID + "',this.checked,this.id)" })                                    
                                                @Html.DisplayFor(modelItem => D_Item.ROLE_NAME)
                                            </div>
                                        }

                                        if (num % 4 == 0)
                                        {
                                            <br />
                                        }
                                        @Html.HiddenFor(modelItem => item.SCHOOL_NO)
                                        @Html.HiddenFor(modelItem => item.USER_NO)
                                        num++;
                                    }
                            </td>
                        </tr>
                    }

                </tbody>
            </table>

            <a class="btn btn-default" href="@Url.Action("Index", (string)ViewBag.BRE_NO)#top">Top</a>
        </div>
    </div>
}
@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1'


        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            SelectQ()
        }

        function SelectQ()
        {
            $(targetFormID).submit();
        }


        function DbSave(SCHOOL_NO_Val, USER_NO_Val, ROLE_ID_Val, Checked_Val,ID_VAL) {


            $.ajax({
                url: "@(Url.Action("Save", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    SCHOOL_NO: SCHOOL_NO_Val
                    , USER_NO: USER_NO_Val
                    , ROLE_ID: ROLE_ID_Val
                    , Checked: Checked_Val
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        alert(res.Error);

                        if (Checked_Val)
                        {
                            $('#' + ID_VAL).prop("checked", false)
                        }
                        else
                        {
                            $('#' + ID_VAL).prop("checked", true)
                        }
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }
            });

        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}