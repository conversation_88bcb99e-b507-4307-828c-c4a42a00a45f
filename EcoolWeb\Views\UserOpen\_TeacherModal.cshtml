﻿@model TeacherModalViewModel

@using (Html.BeginForm("_TeacherModal", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1Details", name = "form1Details", @class = "form-horizontal" }))
{
    <div id="TeacherModalPageContent">
        @Html.Action("_TeacherModalPageContent")
    </div>

}

<script type="text/javascript">

    var form1Details = '#form1Details';

        $(document).ready(function () {
            $(window).keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                }
            });
        });

        function FunPageProc(page) {
            if ($(form1Details).length > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax();
            }
        };

        function doSort(SortCol) {

            var OrderByColumnName = $('#@Html.IdFor(m=>m.OrderByColumnName)').val();
            var SortType = $('#@Html.IdFor(m=>m.SortType)').val();

            $('#@Html.IdFor(m=>m.OrderByColumnName)').val(SortCol)

            if (OrderByColumnName == SortCol ) {

                if (SortType.toUpperCase()=="@PageGlobal.SortType.DESC") {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.ASC');
                }
                else {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
                }
            } else {
                 $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
            }

            FunPageProc(1)
        }

            //查詢
    function funAjax() {
            $.ajax({
                url: '@Url.Action("_TeacherModalPageContent")',
                data: $(form1Details).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#TeacherModalPageContent').html(data);
                }
            });
     }

     function AddRoTeacher(USER_KEY) {

            var data = {
                "USER_KEY": USER_KEY,
         };



           $.ajax({
               type: "POST",
                url: '@Model.UrlAction',
                data: data,
               cache: false,
               dataType: 'html',
               success: function (html) {
                   $("@Model.DivEditorRowsID").append(html);
                   $("#TeacherModaleditorRows").append(html);
                   funAjax()
               },
               error: function (data) {
                    alert(data);
                }
            });
        }
</script>