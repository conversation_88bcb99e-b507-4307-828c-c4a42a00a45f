﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'pl', {
	border: 'Grubo<PERSON>ć obramowania',
	caption: 'Ty<PERSON><PERSON>',
	cell: {
		menu: 'Ko<PERSON><PERSON><PERSON>',
		insertBefore: 'Wstaw komórkę z lewej',
		insertAfter: 'Wstaw komórkę z prawej',
		deleteCell: 'Usuń komórki',
		merge: 'Połącz komórki',
		mergeRight: 'Połącz z komórką z prawej',
		mergeDown: 'Połącz z komórką poniżej',
		splitHorizontal: 'Podziel komórkę poziomo',
		splitVertical: 'Podziel komórkę pionowo',
		title: 'Właściwości komórki',
		cellType: 'Typ komórki',
		rowSpan: '<PERSON><PERSON> wierszy',
		colSpan: 'Scalenie komórek',
		wordWrap: 'Zawijanie słów',
		hAlign: 'Wyrównanie poziome',
		vAlign: 'Wyrównanie pionowe',
		alignBaseline: '<PERSON><PERSON> bazowa',
		bgColor: 'Kolor tła',
		borderColor: 'Kolor obramowania',
		data: 'Dane',
		header: 'Nagłówek',
		yes: 'Tak',
		no: 'Nie',
		invalidWidth: 'Szerokość komórki musi być liczbą.',
		invalidHeight: 'Wysokość komórki musi być liczbą.',
		invalidRowSpan: 'Scalenie wierszy musi być liczbą całkowitą.',
		invalidColSpan: 'Scalenie komórek musi być liczbą całkowitą.',
		chooseColor: 'Wybierz'
	},
	cellPad: 'Dopełnienie komórek',
	cellSpace: 'Odstęp pomiędzy komórkami',
	column: {
		menu: 'Kolumna',
		insertBefore: 'Wstaw kolumnę z lewej',
		insertAfter: 'Wstaw kolumnę z prawej',
		deleteColumn: 'Usuń kolumny'
	},
	columns: 'Liczba kolumn',
	deleteTable: 'Usuń tabelę',
	headers: 'Nagłówki',
	headersBoth: 'Oba',
	headersColumn: 'Pierwsza kolumna',
	headersNone: 'Brak',
	headersRow: 'Pierwszy wiersz',
	invalidBorder: 'Wartość obramowania musi być liczbą.',
	invalidCellPadding: 'Dopełnienie komórek musi być liczbą dodatnią.',
	invalidCellSpacing: 'Odstęp pomiędzy komórkami musi być liczbą dodatnią.',
	invalidCols: 'Liczba kolumn musi być większa niż 0.',
	invalidHeight: 'Wysokość tabeli musi być liczbą.',
	invalidRows: 'Liczba wierszy musi być większa niż 0.',
	invalidWidth: 'Szerokość tabeli musi być liczbą.',
	menu: 'Właściwości tabeli',
	row: {
		menu: 'Wiersz',
		insertBefore: 'Wstaw wiersz powyżej',
		insertAfter: 'Wstaw wiersz poniżej',
		deleteRow: 'Usuń wiersze'
	},
	rows: 'Liczba wierszy',
	summary: 'Podsumowanie',
	title: 'Właściwości tabeli',
	toolbar: 'Tabela',
	widthPc: '%',
	widthPx: 'piksele',
	widthUnit: 'jednostka szerokości'
} );
