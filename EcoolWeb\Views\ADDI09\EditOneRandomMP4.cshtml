﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@using (Html.BeginForm("EditOne2", "ADDI09", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.USER_NAME)
    @Html.HiddenFor(model => model.CLASS_SEAT)
    @Html.HiddenFor(model => model.CASH)
    @Html.HiddenFor(model => model.CONTENT_TXT)
    @Html.HiddenFor(model => model.MEMO)
    @Html.HiddenFor(model => model.SUBJECT)
    @Html.HiddenFor(model => model.USER_NO)
    @Html.HiddenFor(model => model.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.IsFix)
    @Html.HiddenFor(model => model.IsRandom)

    <br />
    <span style="white-space: nowrap;font-size: 12pt;font-weight: bold; color:red">
        好運不常有，努力一定有！來試試好運囉！！
    </span>
    <br />
    @*short total = 6;
    for (short i = 1; i <= total; i++)
    {
        string filename = Url.Content("~/Content/img/") + "monkey-0" + i.ToString() + ".png";
        <img src='@filename' class="col-md-3 col-sm-4 col-xs-6 img-responsive center-block" style="cursor:pointer;max-width:150px;height:auto" onclick="form1.submit();" />

    }*@
    <img id="BT1" src="~/Content/mp4/BT_Active-C1_1x.png" style="width:100px;height:auto;visibility:visible" class="col-md-offset-2" onclick="ToPlay();"  onmouseover="BT1_OVER();" onmouseout="BT1_OUT();" />
    <img id="BT2" src="~/Content/mp4/BT_Result-C1_1x.png" style="width:100px;height:auto;visibility:hidden" class="col-md-offset-2" onclick="ToResult();" onmouseover="BT2_OVER();" onmouseout="BT2_OUT();"    />
    <video id="SlotPlayer" style="width:600px ;height: auto" autoplay="autoplay" loop="loop">
        <source src="~/Content/mp4/ST01-5_Slot-A1_W2-600_HQ.mp4" type="video/mp4" >
    </video>

   <script type= "text/javascript">
       function ToPlay() {
           var video = document.getElementById("SlotPlayer");
           video.src = '@Url.Content("~/Content/mp4/ST01-5_Slot-A2_W2-600_HQ.mp4")';
           
           video.load();
           video.play();

           var bt1 = document.getElementById("BT1");
           bt1.style.visibility = 'hidden';

           var bt2 = document.getElementById("BT2");
           bt2.style.visibility = 'visible';
       }

       function ToResult() {
           var video = document.getElementById("SlotPlayer");
           video.src = '@Url.Content("~/Content/mp4/ST01-5_Slot-30P_W2-600_HQ.mp4")';

           video.load();
           video.play();

           var bt2 = document.getElementById("BT2");
           bt2.style.visibility = 'hidden';
       }

       function BT1_OVER()
       {
           var bt1 = document.getElementById("BT1");
           bt1.src = '@Url.Content("~/Content/mp4/BT_Active-C2_1x.png")';
       }

       function BT1_OUT() {
           var bt1 = document.getElementById("BT1");
           bt1.src = '@Url.Content("~/Content/mp4/BT_Active-C1_1x.png")';
       }

       function BT2_OVER() {
           var bt2 = document.getElementById("BT2");
           bt2.src = '@Url.Content("~/Content/mp4/BT_Result-C1_1x.png")';
       }

       function BT2_OUT() {
           var bt2 = document.getElementById("BT2");
           bt2.src = '@Url.Content("~/Content/mp4/BT_Result-C2_1x.png")';
       }
   </script>

}
