﻿using AutoMapper;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.App_Start
{
    public class AutoMapperConfig
    {
        /// <summary>
        /// Config
        /// </summary>
        public static void Config()
        {
            //Mapper.Initialize(cfg =>
            //{
            //    //CERT01
            //    cfg.CreateMap<CERT01, AccreditationTypeEditMainViewModel>();
            //    cfg.CreateMap<AccreditationTypeEditMainViewModel, CERT01>();

            //    //CERT02
            //    cfg.CreateMap<CERT02, AccreditationManageEditMainViewModel>();
            //    cfg.CreateMap<AccreditationManageEditMainViewModel, CERT02>();

            //    //CERT02 =>CERT02_HIS
            //    cfg.CreateMap<CERT02, CERT02_HIS>();

            //    cfg.CreateMap<CERT02_D, CERT02_D_HIS>();

            //    //CERT02_D
            //    cfg.CreateMap<CERT02_D, AccreditationManageEditDetailsViewModel>();
            //    cfg.CreateMap<AccreditationManageEditDetailsViewModel, CERT02_D>();

            //    //CERT02_V
            //    cfg.CreateMap<CERT02_V, HRMT01>();
            //    cfg.CreateMap<HRMT01, CERT02_V>();

            //    //CERT03
            //    cfg.CreateMap<CERT03, CERI03EditMainViewModel>();
            //    cfg.CreateMap<CERI03EditMainViewModel, CERT03>();

            //    //GAAT03
            //    cfg.CreateMap<GAAT03, GAAI01SysSetWearIndexViewModel>();
            //    cfg.CreateMap<GAAI01SysSetWearIndexViewModel, GAAT03>();
            //});

            // Mapper.AssertConfigurationIsValid();
        }
    }
}