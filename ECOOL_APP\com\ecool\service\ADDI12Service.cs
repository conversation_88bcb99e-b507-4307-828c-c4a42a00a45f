﻿using Dapper;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Data.Entity.SqlServer;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class ADDI12Service
    {
        /// <summary>
        /// 首播按讚給點數
        /// </summary>
        private static readonly int pREMIER_CASH = 2;

        /// <summary>
        /// 首播按讚人數
        /// </summary>
        private static readonly int pRemierCount = 10;

        /// <summary>
        /// 一般按讚給點數
        /// </summary>
        private static readonly int cASH = 1;

        /// <summary>
        /// 一般按讚人數
        /// </summary>
        private static readonly int pCount = 10;

        public static string GetSetDirectoryPath(string SCHOOL_NO, string STAGE_ID)
        {
            string ReturnImgUrl = string.Empty;

            ReturnImgUrl = GetSetDirectoryPath() + $@"{SCHOOL_NO}\{STAGE_ID}";

            return ReturnImgUrl;
        }

        public static string GetSetDirectoryPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string ADDI12 = "ADDI12";
            ReturnImgUrl = $@"{UploadImageRoot}{ADDI12}\";

            return ReturnImgUrl;
        }

        /// <summary>
        /// 編輯時帶入資料
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ADDI12EditViewModel GetEditData(ADDI12EditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                model.Main = (from a in db.ADDT34
                              where a.STAGE_ID == model.Search.WhereSTAGE_ID
                              select new ADDI12EditMainViewModel()
                              {
                                  STAGE_ID = a.STAGE_ID,
                                  SCHOOL_NO = a.SCHOOL_NO,
                                  STAGE_NAME = a.STAGE_NAME,
                                  YOUTUBE_IMG = a.YOUTUBE_IMG,
                                  YOUTUBE_URL = a.YOUTUBE_URL,
                                  IS_PREMIER = a.IS_PREMIER ?? false,
                                  STAGE_DATES = a.STAGE_DATES,
                                  STAGE_DATEE = a.STAGE_DATEE,
                              }).FirstOrDefault();

                if (model.Main != null)
                {
                    if (!string.IsNullOrWhiteSpace(model.Main.YOUTUBE_IMG))
                    {
                        model.Main.YOUTUBE_IMG_Path = UrlCustomHelper.Url_Content(GetSetDirectoryPath(model.Main.SCHOOL_NO, model.Main.STAGE_ID) + @"\" + model.Main.YOUTUBE_IMG);
                    }

                    model.Details = (from a in db.ADDT35
                                     join c in db.BDMT01 on a.SCHOOL_NO equals c.SCHOOL_NO
                                     where a.STAGE_ID == model.Main.STAGE_ID
                                     select new ADDI12EditPeopleViewModel()
                                     {
                                         STAGE_PERSON_ID = a.STAGE_PERSON_ID,
                                         STAGE_PERSON_ITEM = a.STAGE_PERSON_ITEM,
                                         STAGE_ID = a.STAGE_ID,
                                         SCHOOL_NO = a.SCHOOL_NO,
                                         USER_NO = a.USER_NO,
                                         NAME = a.NAME,
                                         GRADE = a.GRADE,
                                         CLASS_NO = a.CLASS_NO,
                                         SHORT_NAME = c.SHORT_NAME,
                                         SEAT_NO = a.SEAT_NO,
                                     }).OrderBy(a => a.STAGE_PERSON_ITEM).ToList();
                }

                return model;
            }
        }

        /// <summary>
        /// 查詢學生資料列表
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public ADDI12OpenPersonViewViewModel GetOpenPersonData(ADDI12OpenPersonViewViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var listUser = db.ADDT35.Where(a => a.STAGE_ID == model.WhereSTAGE_ID).Select(a => a.USER_NO).ToList();
                var NowlistUser = model.Details?.Select(a => a.USER_NO).ToList();

             var    Temp = (from b in db.HRMT01
                        join c in db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                        where b.SCHOOL_NO == model.Search.WhereSCHOOL_NO
                        && b.USER_TYPE == UserType.Student
                        && (!UserStaus.NGUserStausList.Contains(b.USER_STATUS))
                        select new ADDI12EditPeopleViewModel()
                        {
                            SCHOOL_NO = b.SCHOOL_NO,
                            SHORT_NAME = c.SHORT_NAME,
                            USER_NO = b.USER_NO,
                            NAME = b.NAME,
                            GRADE = b.GRADE,
                            CLASS_NO = b.CLASS_NO,
                            SEAT_NO = b.SEAT_NO,
                        });
                //if (model.PersonRoleType == "stage") {
                //    Temp = (from b in db.HRMT01
                //            join c in db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                //            where b.USER_TYPE == UserType.Student
                //            && (!UserStaus.NGUserStausList.Contains(b.USER_STATUS))
                //            select new ADDI12EditPeopleViewModel()
                //            {
                //                SCHOOL_NO = b.SCHOOL_NO,
                //                SHORT_NAME = c.SHORT_NAME,
                //                USER_NO = b.USER_NO,
                //                NAME = b.NAME,
                //                GRADE = b.GRADE,
                //                CLASS_NO = b.CLASS_NO,
                //                SEAT_NO = b.SEAT_NO,
                //            });

                //}
               
                

                var QTemp = Temp;

                if (listUser != null)
                {
                    QTemp = QTemp.Where(b => !listUser.Any(x => x == b.USER_NO));
                }

                if (NowlistUser != null)
                {
                    QTemp = QTemp.Where(b => !NowlistUser.Any(x => x == b.USER_NO));
                }

                if (!string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
                {
                    QTemp = QTemp.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO);
                }

                if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
                {
                    QTemp = QTemp.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
                }

                if (!string.IsNullOrWhiteSpace(model.WhereNAME))
                {
                    QTemp = QTemp.Where(a => a.NAME.Contains(model.WhereNAME));
                }

                model.PersonData = QTemp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.USER_NO).ToList();

                List<SelectListItem> SchoolNoSelectItem = new List<SelectListItem>();
                SchoolNoSelectItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) });

                SchoolNoSelectItem.AddRange(Temp.Select(a => new { a.SCHOOL_NO, a.SHORT_NAME }).Distinct()
                    .Select(x => new SelectListItem() { Text = x.SHORT_NAME, Value = x.SCHOOL_NO, Selected = x.SCHOOL_NO == model.WhereSCHOOL_NO })
                    .OrderBy(a => a.Text).ToList());

                model.SchoolNoSelectItem = SchoolNoSelectItem;

                List<SelectListItem> ClassItems = new List<SelectListItem>();
                ClassItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereCLASS_NO) });

                ClassItems.AddRange(Temp.Select(a => new { a.CLASS_NO }).Distinct()
                   .Select(x => new SelectListItem() { Text = x.CLASS_NO, Value = x.CLASS_NO, Selected = x.CLASS_NO == model.WhereCLASS_NO })
                   .OrderBy(a => a.Text).ToList());

                model.ClassItems = ClassItems;

                return model;
            }
        }

        /// <summary>
        /// 查詢學生資料明細
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public ADDI12EditPeopleViewModel GetAddPersonData(string SCHOOL_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                ADDI12EditPeopleViewModel model = new ADDI12EditPeopleViewModel();

                model = (from b in db.HRMT01
                         join c in db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                         where b.SCHOOL_NO == SCHOOL_NO
                         && b.USER_NO == USER_NO
                         && b.USER_TYPE == UserType.Student
                         && (!UserStaus.NGUserStausList.Contains(b.USER_STATUS))
                         select new ADDI12EditPeopleViewModel()
                         {
                             SCHOOL_NO = b.SCHOOL_NO,
                             SHORT_NAME = c.SHORT_NAME,
                             USER_NO = b.USER_NO,
                             NAME = b.NAME,
                             GRADE = b.GRADE,
                             CLASS_NO = b.CLASS_NO,
                             SEAT_NO = b.SEAT_NO,
                         }).FirstOrDefault();

                return model;
            }
        }

        /// <summary>
        /// 資料存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveEditData(ADDI12EditViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            using (TransactionScope tx = new TransactionScope())
            {
                if (model.Details.Count() == 0)
                {
                    Message = "表演者隊伍未輸入";
                    return false;
                }

                var arrTEMP_USER_ID = model.Details.GroupBy(a => a.USER_NO).Where(g => g.Count() > 1).Select(g => g.Key).ToArray();

                if (arrTEMP_USER_ID.Length > 0)
                {
                    Message = "表演者名單重覆";
                    return false;
                }

                ADDT34 Cre = null;

                var EmbedYouTubeUrl = YoutubeHelper.ConvertToEmbedYouTubeVideo(model.Main.YOUTUBE_URL, ref Message);

                if (!string.IsNullOrWhiteSpace(Message))
                {
                    return false;
                }

                model.Main.YOUTUBE_URL = EmbedYouTubeUrl;
                bool ChangeImg = false;
                string OringinSchool_NO = "";
                Cre = Db.ADDT34.Where(a => a.STAGE_ID == model.Main.STAGE_ID).FirstOrDefault();
                if (user?.USER_NO=="stage") {
                    if (model.Main.SCHOOL_NO == "" || model.Main.SCHOOL_NO==null) {
                        Cre.SCHOOL_NO = "ALL";
                    }
                    else {
                        if (Cre != null)
                        {
                            if (Cre.SCHOOL_NO != model.Main.SCHOOL_NO)
                            {
                                ChangeImg = true;
                                OringinSchool_NO = Cre.SCHOOL_NO;
                            }

                            Cre.SCHOOL_NO = model.Main.SCHOOL_NO;
                        }
                        else {
                            if (model.Main != null && Cre != null)
                            {
                                OringinSchool_NO = model.Main.SCHOOL_NO;
                                Cre.SCHOOL_NO = model.Main.SCHOOL_NO;

                            }
                            else if(model.Main != null) {

                                OringinSchool_NO = model.Main.SCHOOL_NO;
                            }
                      
                        }
                    }
               
                }
                
                if (Cre == null)
                {
                    Cre = new ADDT34
                    {
                        STAGE_ID = Guid.NewGuid().ToString("N"),
                        SCHOOL_NO = model.Search.WhereSCHOOL_NO,
                        STAGE_NAME = model.Main.STAGE_NAME,
                        CRE_PERSON = user.USER_KEY,
                        CRE_DATE = DateTime.Now,
                        CHG_PERSON = user.USER_KEY,
                        CHG_DATE = DateTime.Now,
                        YOUTUBE_URL = model.Main.YOUTUBE_URL,
                        IS_PREMIER = model.Main.IS_PREMIER,
                        STAGE_DATES = model.Main.STAGE_DATES,
                        STAGE_DATEE = model.Main.STAGE_DATEE,
                        STATUS = Convert.ToByte(TableStatus.OK),
                    };
                    Db.ADDT34.Add(Cre);
                }
                else
                {
                    Cre.STAGE_NAME = model.Main.STAGE_NAME;
                    Cre.CHG_PERSON = user.USER_KEY;
                    Cre.CHG_DATE = DateTime.Now;
                    Cre.YOUTUBE_URL = model.Main.YOUTUBE_URL;
                    Cre.IS_PREMIER = model.Main.IS_PREMIER;
                    Cre.STAGE_DATES = model.Main.STAGE_DATES;
                    Cre.STAGE_DATEE = model.Main.STAGE_DATEE;

                    Db.Entry(Cre).State = System.Data.Entity.EntityState.Modified;
                }
                if (model.Main.UploadYoutubeFile?.ContentLength == null && ChangeImg) {

                    string New_TITLE_IMG_Path = GetSysPath(Cre.SCHOOL_NO, Cre.STAGE_ID) + $@"\";
                    string TITLE_IMG_Path = GetSysPath(OringinSchool_NO, Cre.STAGE_ID) + $@"\";
                    string UpLoadFile = TITLE_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    string New_UpLoadFile = New_TITLE_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    string TITLE_SMALL_IMG_Path = GetSysPath(OringinSchool_NO, Cre.STAGE_ID) + $@"\Small\";
                    string New_SMALL_TITLE_IMG_Path = GetSysPath(Cre.SCHOOL_NO, Cre.STAGE_ID) + $@"\Small\";
                    string UpLoadFile_SMALL = TITLE_SMALL_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    string New_SMALL_UpLoadFile = New_SMALL_TITLE_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    if (ChangeImg)
                    {

                        FileInfo file = new FileInfo(UpLoadFile);
                        if (System.IO.File.Exists(UpLoadFile))
                        {
                            file.CopyTo(New_UpLoadFile);
                        }
                        FileInfo file1 = new FileInfo(UpLoadFile_SMALL);
                        if (System.IO.File.Exists(UpLoadFile))
                        {

                            file1.CopyTo(New_SMALL_UpLoadFile);
                        }
                        else
                        {
                            UploadFileData(model.Main.UploadYoutubeFile, Cre, ref Message);
                            UpLoadFileSmallgif(model.Main.UploadYoutubeFile, Cre, ref Message);

                        }

                        if (!string.IsNullOrWhiteSpace(Message))
                        {
                            return false;
                        }
                    }
                }
              else  if (model.Main.UploadYoutubeFile?.ContentLength >= 0)
                {   string New_TITLE_IMG_Path = GetSysPath(Cre.SCHOOL_NO, Cre.STAGE_ID) + $@"\";
                    string TITLE_IMG_Path = GetSysPath(OringinSchool_NO, Cre.STAGE_ID) + $@"\";
                    string UpLoadFile = TITLE_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    string New_UpLoadFile = New_TITLE_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    string TITLE_SMALL_IMG_Path = GetSysPath(OringinSchool_NO, Cre.STAGE_ID) + $@"\Small\";
                    string New_SMALL_TITLE_IMG_Path = GetSysPath(Cre.SCHOOL_NO, Cre.STAGE_ID) + $@"\Small\";
                    string UpLoadFile_SMALL = TITLE_SMALL_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    string New_SMALL_UpLoadFile = New_SMALL_TITLE_IMG_Path + "\\" + Cre.YOUTUBE_IMG;
                    if (ChangeImg)
                    {

                        FileInfo file = new FileInfo(UpLoadFile);
                        if (System.IO.File.Exists(UpLoadFile))
                        {
                            file.CopyTo(New_UpLoadFile);
                        }
                        FileInfo file1 = new FileInfo(UpLoadFile_SMALL);
                        if (System.IO.File.Exists(UpLoadFile)) {

                            file1.CopyTo(New_SMALL_UpLoadFile);
                        }
                    }
                    else {
                        UploadFileData(model.Main.UploadYoutubeFile, Cre, ref Message);
                        UpLoadFileSmallgif(model.Main.UploadYoutubeFile, Cre, ref Message);

                    }
                  
                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return false;
                    }
                }

                Db.ADDT35.Where(a => a.STAGE_ID == Cre.STAGE_ID).Delete();

                if (model.Details.Count() > 0)
                {
                    List<ADDT35> listADDT35 = new List<ADDT35>();

                    string STAGE_PERSON_ID = model.Details.Where(a => a.STAGE_PERSON_ID != null).Select(a => a.STAGE_PERSON_ID).FirstOrDefault() ?? Guid.NewGuid().ToString("N");
                    int STAGE_PERSON_ITEM = 0;

                    foreach (var item in model.Details)
                    {
                        STAGE_PERSON_ITEM++;

                        ADDT35 Cre_D = new ADDT35
                        {
                            STAGE_PERSON_ID = STAGE_PERSON_ID,
                            STAGE_PERSON_ITEM = STAGE_PERSON_ITEM.ToString(),
                            STAGE_ID = Cre.STAGE_ID,
                            CHG_PERSON = user.USER_KEY,
                            CHG_DATE = DateTime.Now,
                            SCHOOL_NO = item.SCHOOL_NO,
                            USER_NO = item.USER_NO,
                            NAME = item.NAME,
                            SYEAR = Convert.ToByte(SYear),
                            SEMESTER = Convert.ToByte(Semesters),
                            GRADE = item.GRADE,
                            CLASS_NO = item.CLASS_NO,
                            SEAT_NO = item.SEAT_NO,
                        };

                        listADDT35.Add(Cre_D);
                        Db.ADDT35.Add(Cre_D);
                    }

                  //  EFBatchOperation.For(Db, Db.ADDT35).InsertAll(listADDT35);
                }

                try
                {
                    Db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }

        /// <summary>
        /// 作廢
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="Db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveDelData(ADDI12EditViewModel model, UserProfile user, ref ECOOL_DEVEntities Db, ref string Message)
        {
            var Del = Db.ADDT34.Where(a => a.STAGE_ID == model.Main.STAGE_ID).FirstOrDefault();

            if (Del != null)
            {
                Del.STATUS = Convert.ToByte(TableStatus.Disabled);
            }

            try
            {
                Db.SaveChanges();
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }

            return true;
        } /// <summary>
          /// 查詢列表
          /// </summary>
          /// <param name="model"></param>
          /// <param name="db"></param>
          /// <returns></returns>
        public ADDI12IndexListViewModel GetstageListData(ADDI12IndexListViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                IEnumerable<ADDI12IndexListDataViewModel> temp = GetStageADDT34ListData(model, User, db);

                model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);
            }

            return model;
        }

        /// <summary>
        /// 查詢列表
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public ADDI12IndexListViewModel GetListData(ADDI12IndexListViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                IEnumerable<ADDI12IndexListDataViewModel> temp = GetADDT34ListData(model, User, db);

                model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);
            }

            return model;
        }

        public ADDI12IndexListDataViewModel GetRandPremier(ADDI12IndexListViewModel model, UserProfile User, ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                IEnumerable<ADDI12IndexListDataViewModel> temp = GetADDT34ListData(model, User, db);
                temp = temp.Where(x => x.YOUTUBE_IMG != null);
                var Premier = temp.OrderBy(a => Guid.NewGuid()).FirstOrDefault();
                return Premier;
            }
        }

        public ADDI12YoutubeViewViewModel GetYoutubeData(string STAGE_ID, string IP_ADDRESS, UserProfile User, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                string sSQL = $@"Select a.*
                            ,(select Count(*) from ADDT36 c  (NOLOCK) where a.STAGE_ID =c.STAGE_ID and c.IS_PREMIER = 1 ) as PremierLikeCount
                            ,(select Count(*) from ADDT36 c  (NOLOCK) where a.STAGE_ID =c.STAGE_ID and c.IS_PREMIER = 0 ) as LikeCount
                            ,(select Count(*) from ADDT36 d  (NOLOCK) where a.STAGE_ID =d.STAGE_ID
                                and ((d.SCHOOL_NO='{(User?.SCHOOL_NO ?? "")}' and d.USER_NO='{(User?.USER_NO ?? "")}' and '{(User?.USER_NO ?? "")}'<>'')
                                    or ('{(User?.USER_NO ?? "")}'='' and isnull(d.USER_NO,'')='' and IP_ADDRESS='{(IP_ADDRESS ?? "")}')) ) as IsLikeCount
                            from ADDT34 a (nolock)
                            where a.STAGE_ID =@STAGE_ID  ";

                var model = db.Database.Connection.Query<ADDI12YoutubeViewViewModel>(sSQL
                  , new
                  {
                      STAGE_ID,
                  }).FirstOrDefault();

                if (model != null)
                {
                    if (model.IsLikeCount > 0) //按過讚了
                    {
                        model.BtnLike = false;
                    }
                    else
                    {
                        model.BtnLike = true;
                    }
                }

                return model;
            }
        }

        /// <summary>
        /// 表演人員名單
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<ADDI12EditPeopleViewModel> GetYoutubePeopleData(string STAGE_ID, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                List<ADDI12EditPeopleViewModel> model = new List<ADDI12EditPeopleViewModel>();

                model = (from a in db.ADDT35
                         join c in db.BDMT01 on a.SCHOOL_NO equals c.SCHOOL_NO
                         where a.STAGE_ID == STAGE_ID
                         select new ADDI12EditPeopleViewModel
                         {
                             SCHOOL_NO = a.SCHOOL_NO,
                             SHORT_NAME = c.SHORT_NAME,
                             USER_NO = a.USER_NO,
                             NAME = a.NAME.Remove(0, 1),
                             GRADE = a.GRADE,
                             CLASS_NO = a.CLASS_NO,
                             SEAT_NO = a.SEAT_NO,
                         }).OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToList();

                return model;
            }
        }

        /// <summary>
        /// 按讚人員清單
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<ADDI12LinkPersonViewModel> GetLinkPersonData(string STAGE_ID, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                List<ADDI12LinkPersonViewModel> model = new List<ADDI12LinkPersonViewModel>();

                model = (from a in db.ADDT36
                         join c in db.BDMT01 on a.SCHOOL_NO equals c.SCHOOL_NO into ps
                         from o in ps.DefaultIfEmpty()
                         where a.STAGE_ID == STAGE_ID
                         select new ADDI12LinkPersonViewModel
                         {
                             SCHOOL_NO = a.SCHOOL_NO,
                             SHORT_NAME = o.SHORT_NAME,
                             USER_NO = a.USER_NO,
                             NAME = a.NAME ?? "訪客",
                             CLASS_NO = a.CLASS_NO,
                             CRE_DATE = a.CRE_DATE,
                             IS_PREMIER = a.IS_PREMIER,
                         }).OrderByDescending(a => a.CRE_DATE).ToList();

                return model;
            }
        }

        /// <summary>
        /// 瀏覽人數+1
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public bool SaveReadCount(string STAGE_ID, ref ECOOL_DEVEntities db)
        {
            var dDT34 = db.ADDT34.Where(a => a.STAGE_ID == STAGE_ID).FirstOrDefault();

            if (dDT34 != null)
            {
                dDT34.READ_COUNT = (dDT34.READ_COUNT ?? 0) + 1;

                db.SaveChanges();
            }

            return true;
        }

        /// <summary>
        /// 按讚
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <param name="IP_ADDRESS"></param>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <param name="LikeCou"></param>
        /// <returns></returns>
        /// 
        public bool SaveLikeData(string STAGE_ID, string IP_ADDRESS, UserProfile User, ref ECOOL_DEVEntities db, ref string Message , ref List<Tuple<string, string, int>> valuesList)
        {
            short thisCash;          //這次要給的點數
            bool isPremier = false;  //是否首播

            ADDT34 dDT34 = db.ADDT34.Where(a => a.STAGE_ID == STAGE_ID).FirstOrDefault();
            if (dDT34 == null)
            {
                Message = $"系統發生錯誤;原因:找不到此筆資料STAGE_ID={STAGE_ID}";
                return false;
            }

            if (string.IsNullOrWhiteSpace(IP_ADDRESS) && User == null)
            {
                Message = $"未取得訪客IP";
                return false;
            }

            if (dDT34 != null)
            {
                var TempdDT36 = db.ADDT36.Where(a => a.STAGE_ID == STAGE_ID);

                if (dDT34.IS_PREMIER == true && dDT34.STAGE_DATES <= Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"))
                    && dDT34.STAGE_DATEE >= Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"))
                    )
                {
                    var nowPremierCount = TempdDT36.Where(a => a.IS_PREMIER == true).Count();

                    if (nowPremierCount >= pRemierCount)
                    {
                        thisCash = 0;
                    }
                    else
                    {
                        thisCash = (short)pREMIER_CASH;
                    }

                    isPremier = true;
                }
                else
                {
                    var nowCount = TempdDT36.Where(a => a.IS_PREMIER == false).Count();

                    if (nowCount >= pCount)
                    {
                        thisCash = 0;
                    }
                    else
                    {
                        thisCash = (short)cASH;
                    }
                    isPremier = false;
                }

                if (User != null)
                {
                    TempdDT36 = TempdDT36.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.USER_NO == User.USER_NO);
                }
                else
                {
                    TempdDT36 = TempdDT36.Where(a => a.IP_ADDRESS == IP_ADDRESS && a.USER_NO == null);
                }
                int TempdDT36COUNT = TempdDT36.Count();
                if (TempdDT36.Any() == false)
                {
                    if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();
                    using (TransactionScope tx = new TransactionScope())
                    {
                        try
                        {
                            ADDT36 SaveUp = new ADDT36();

                            SaveUp.STAGE_LIKE_NO = Guid.NewGuid().ToString("N");
                            SaveUp.STAGE_ID = STAGE_ID;

                            if (User != null)
                            {
                                SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                                SaveUp.USER_NO = User.USER_NO;
                                SaveUp.CLASS_NO = User.CLASS_NO;
                                SaveUp.NAME = User.SNAME;
                            }
                            else
                            {
                                SaveUp.NAME = "訪客";
                            }

                            SaveUp.CRE_DATE = DateTime.Now;
                            SaveUp.CASH = thisCash;
                            SaveUp.IS_PREMIER = isPremier;

                            SaveUp.IP_ADDRESS = IP_ADDRESS;
                            SaveUp.LIKE_STATUS = Convert.ToByte(TableStatus.OK);

                            db.ADDT36.Add(SaveUp);

                            // 首播按讚人數 與  一般按讚人數 各小於等於10個人才給點

                            if (thisCash > 0) //需要給點時才需求call  AddCash
                            {
                                List<ADDT35> aDDT35s = db.ADDT35.Where(a => a.STAGE_ID == STAGE_ID).ToList();

                                string Log_DESC = "小小舞臺 " + ((isPremier) ? "首播按讚給點" : "一般按讚給點");

                                foreach (var item in aDDT35s)
                                {
                                    ECOOL_APP.CashHelper.AddCash(User, thisCash, item.SCHOOL_NO, item.USER_NO, "ADDT36", STAGE_ID, Log_DESC, true, ref db,"", "",ref valuesList);
                                }
                            }

                            try
                            {
                                db.SaveChanges();
                            }
                            catch (Exception ex)
                            {
                                Message = "系統發生錯誤;原因:" + ex.Message;
                                return false;
                            }

                            tx.Complete();
                        }
                        catch (Exception ex)
                        {
                            Message = "系統發生錯誤;原因:" + ex.Message;
                            return false;
                        }
                    }
                }
            }

            return true;
        }

        public void UpLoadFileSmallgif(HttpPostedFileBase UploadBuskerFile, ADDT34 Cre, ref string Message)
        {
            if (UploadBuskerFile != null && UploadBuskerFile?.ContentLength > 0)
            {
                string TITLE_IMG_Path = GetSysPath(Cre.SCHOOL_NO, Cre.STAGE_ID) + $@"\Small\";

                if (Directory.Exists(TITLE_IMG_Path) == false)
                {
                    Directory.CreateDirectory(TITLE_IMG_Path);
                }
                string fileName = Path.GetFileName(UploadBuskerFile.FileName);
                string UpLoadFile = TITLE_IMG_Path + "\\" + fileName;
                try
                {
                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }
                    UploadBuskerFile.SaveAs(UpLoadFile);
                    System.Drawing.Image image = System.Drawing.Image.FromFile(UpLoadFile);
                    //必須使用絕對路徑

                    ImageFormat thisFormat = image.RawFormat;

                    int fixWidth = 0;
                    int fixHeight = 0;
                    int maxPx = 200;

                    if (image.Width > maxPx || image.Height > maxPx)  //如果圖片的寬大於最大值或高大於最大值就往下執行
                    {
                        if (image.Width >= image.Height)  //圖片的寬大於圖片的高

                        {
                            fixWidth = maxPx;
                            //設定修改後的圖寬
                            fixHeight = Convert.ToInt32((Convert.ToDouble(fixWidth) / Convert.ToDouble(image.Width)) * Convert.ToDouble(image.Height));
                            //設定修改後的圖高
                        }
                        else
                        {
                            fixHeight = maxPx;
                            //設定修改後的圖高
                            fixWidth = Convert.ToInt32((Convert.ToDouble(fixHeight) / Convert.ToDouble(image.Height)) * Convert.ToDouble(image.Width));
                            //設定修改後的圖寬
                        }
                    }
                    else
                    //圖片沒有超過設定值，不執行縮圖
                    {
                        fixHeight = image.Height;
                        fixWidth = image.Width;
                    }
                    Bitmap imageOutput = new Bitmap(image, fixWidth, fixHeight);

                    image.Dispose();
                    //釋放掉圖檔
                    //輸出一個新圖(就是修改過的圖)
                    //string fixSaveName = string.Concat(fileName+"_sys",".jpg");
                    //副檔名不應該這樣給，但因為此範例沒有讀取檔案的部份所以demo就直接給啦
                    imageOutput.Save(TITLE_IMG_Path + "\\" + fileName, thisFormat);
                    //將修改過的圖存於設定的位子
                    imageOutput.Dispose();
                    //釋放記憶體
                }
                catch (Exception e)
                {
                    Message = fileName + "上傳失敗" + e + "r\n";
                }
            }
        }

        /// <summary>
        /// 上傳
        /// </summary>
        /// <param name="UploadBuskerFile"></param>
        /// <param name="Cre"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool UploadFileData(HttpPostedFileBase UploadBuskerFile, ADDT34 Cre, ref string Message)
        {
            if (UploadBuskerFile != null && UploadBuskerFile?.ContentLength > 0)
            {
                try
                {
                    string TITLE_IMG_Path = GetSysPath(Cre.SCHOOL_NO, Cre.STAGE_ID) + $@"\";

                    if (Directory.Exists(TITLE_IMG_Path) == false)
                    {
                        Directory.CreateDirectory(TITLE_IMG_Path);
                    }

                    string fileName = Path.GetFileName(UploadBuskerFile.FileName);

                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                    if (regexCode.IsMatch(fileName.ToLower()) == false)
                    {
                        Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                        return false;
                    }

                    if (UploadBuskerFile.ContentLength / 1024 > (1024 * 20)) // 20MB
                    {
                        Message = "上傳檔案不能超過20MB";
                        return false;
                    }

                    if (!string.IsNullOrWhiteSpace(Cre.YOUTUBE_IMG))
                    {
                        string DelFile = TITLE_IMG_Path + "\\" + Cre.YOUTUBE_IMG;

                        if (System.IO.File.Exists(DelFile))
                        {
                            System.IO.File.Delete(DelFile);
                        }
                    }

                    string UpLoadFile = TITLE_IMG_Path + "\\" + fileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    UploadBuskerFile.SaveAs(UpLoadFile);

                    Cre.YOUTUBE_IMG = fileName;
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }
            }

            return true;
        }

        public string GetSysPath(string SCHOOL_NO, string STAGE_ID)
        {
            return HttpContext.Current.Server.MapPath(GetSetDirectoryPath()) + @"\" + SCHOOL_NO + @"\" + STAGE_ID + @"\";
        }

        /// <summary>
        /// 產生小小舞台通知
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        public void CreADDI12forNotice(string SCHOOL_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
         
            string sSQL = $@"Select a.*
            from ADDT34 a (nolock)
            left outer join APPT02 b (nolock) on b.SCHOOL_NO=@SCHOOL_NO and b.USER_NO=@USER_NO and b.REF_SOU_BRE_NO='ADDI12' and b.REF_SOU_KEY=a.STAGE_ID
            where a.SCHOOL_NO=@SCHOOL_NO and a.STAGE_DATES<=GETDATE()
            AND a.STAGE_DATEE >=GETDATE() and a.STATUS={TableStatus.OK} and b.REF_SOU_KEY is null   and a.YOUTUBE_IMG is not null ";

            var query = db.Database.Connection.Query<ADDT34>(sSQL
            , new
            {
                SCHOOL_NO,
                USER_NO,
            }).ToList();
            if (USER_NO == "stage") {

                string ALLSCHOOL = "ALL";
                var query1= db.Database.Connection.Query<ADDT34>(sSQL
                , new
                {
                    SCHOOL_NO=ALLSCHOOL,
                    USER_NO,
                }).ToList();
                query.AddRange(query1);
            }
            if (query?.Count > 0)
            {
                string BATCH_ID = PushService.CreBATCH_ID();

                foreach (var item in query)
                {
                    if (item.IS_PREMIER == true)
                    {
                        PushService.InsertPushDataMe(BATCH_ID, SCHOOL_NO, USER_NO, "", $"小小舞臺新首播影片通知-{item.STAGE_NAME}", "", "ADDI12", "PremierView"
                            , item.STAGE_ID.ToString(), "ADDI12/PremierView", false, ref db);
                    }
                    else
                    {
                        PushService.InsertPushDataMe(BATCH_ID, SCHOOL_NO, USER_NO, "", $"小小舞臺新一般影片通知-{item.STAGE_NAME}", "", "ADDI12", "AllVideoView"
                        , item.STAGE_ID.ToString(), "ADDI12/AllVideoView", false, ref db);
                    }
                }

                db.SaveChanges();

                PushHelper.ToPushServer(BATCH_ID);
            }
        }

        /// <summary>
        /// 讀取新的通知筆數
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="db"></param>
        public int? GetADDI12forNoticeCount(string SCHOOL_NO, string USER_NO, bool IS_PREMIER, ref ECOOL_DEVEntities db)
        {
            if (string.IsNullOrWhiteSpace(SCHOOL_NO) || string.IsNullOrWhiteSpace(USER_NO))
            {
                return null;
            }

            string REF_SOU_ITEM = string.Empty;

            if (IS_PREMIER)
            {
                REF_SOU_ITEM = "PremierView";
            }
            else
            {
                REF_SOU_ITEM = "AllVideoView";
            }

            return db.APPT02.Where(x => x.SCHOOL_NO == SCHOOL_NO && x.USER_NO == USER_NO
            && x.REF_SOU_BRE_NO == "ADDI12" && x.REF_SOU_ITEM == REF_SOU_ITEM && (x.STATUS == APPT02.StatusVal.UnRead || x.STATUS == APPT02.StatusVal.Cre)).NoLock(a => a.Count());
        }

        public void UpdateADDI12forNotice(string SCHOOL_NO, string USER_NO, bool IS_PREMIER, ref ECOOL_DEVEntities db)
        {
            if (!string.IsNullOrWhiteSpace(SCHOOL_NO) && !string.IsNullOrWhiteSpace(USER_NO))
            {
                string REF_SOU_ITEM = string.Empty;

                if (IS_PREMIER)
                {
                    REF_SOU_ITEM = "PremierView";
                }
                else
                {
                    REF_SOU_ITEM = "AllVideoView";
                }

                string sSQL = $@" Update APPT02 set STATUS='{APPT02.StatusVal.Read.ToString()}'
                where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO and REF_SOU_BRE_NO='ADDI12'  and REF_SOU_ITEM=@REF_SOU_ITEM
                and (STATUS='{APPT02.StatusVal.UnRead.ToString()}' or STATUS='{APPT02.StatusVal.Cre.ToString()}') ";

                db.Database.Connection.Execute(sSQL
                , new
                {
                    SCHOOL_NO,
                    USER_NO,
                    REF_SOU_ITEM
                });
                string sSQL1 = $@" Update APPT02 set STATUS='{APPT02.StatusVal.Read.ToString()}'
                where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO and REF_SOU_BRE_NO='ADDI12'  and REF_SOU_ITEM=@REF_SOU_ITEM
                and (STATUS='{APPT02.StatusVal.UnRead.ToString()}' or STATUS='{APPT02.StatusVal.Cre.ToString()}') ";

                db.Database.Connection.Execute(sSQL1
                , new
                {
                    SCHOOL_NO="ALL",
                    USER_NO,
                    REF_SOU_ITEM
                });

                db.SaveChanges();
            }
        }
        private static IEnumerable<ADDI12IndexListDataViewModel> GetStageADDT34ListData(ADDI12IndexListViewModel model, UserProfile User, ECOOL_DEVEntities db)
        {

            string sSQL = "";
            sSQL = $@"select a.*   from ADDT34 a (nolock)  where  a.STATUS = '{TableStatus.OK}'";
            var temp = db.Database.Connection.Query<ADDI12IndexListDataViewModel>(sSQL);
            temp = temp.OrderByDescending(a => a.CRE_DATE).ThenBy(a => a.STAGE_NAME);
            return temp;

        }
      private static IEnumerable<ADDI12IndexListDataViewModel> GetADDT34ListData(ADDI12IndexListViewModel model, UserProfile User, ECOOL_DEVEntities db)
        {
            string sSQL = "";
            if (model.Search != null && model.Search.WhereSCHOOL_NO != null)
            {
                sSQL = $@"Select a.*
                            ,YOUTUBE_IMG_Path = '{GetSetDirectoryPath()}' + a.SCHOOL_NO+'\'+a.STAGE_ID+'\'+a.YOUTUBE_IMG
,YOUTUBE_SmallURL='{GetSetDirectoryPath()}' + a.SCHOOL_NO+'\'+a.STAGE_ID+'\Small\'+a.YOUTUBE_IMG
,REPLACE(REPLACE ((select Name from ADDT35 (nolock)  where STAGE_ID=a.STAGE_ID FOR XML PATH('')),'<Name>' ,''),'</Name>' ,',') as Show_PERSON
                              ,REPLACE(REPLACE ((select top 1 Name from ADDT35 (nolock) where STAGE_ID=a.STAGE_ID FOR XML PATH('')),'<Name>' ,''),'</Name>' ,',') as View_PERSON
                            from ADDT34 a (nolock)
                            where (a.SCHOOL_NO =@SCHOOL_NO or a.SCHOOL_NO = 'ALL') and a.STATUS = '{TableStatus.OK}' ";
            }
           

            if (!string.IsNullOrWhiteSpace(model.Search.WhereKeyword))
            {
                sSQL = sSQL + $@" and ( REPLACE(REPLACE ((select Name from ADDT35 (nolock)  where STAGE_ID=a.STAGE_ID FOR XML PATH('')),'<Name>' ,''),'</Name>' ,',')  LIKE '%{model.Search.WhereKeyword}%' or REPLACE(REPLACE ((select USER_NO from ADDT35 (nolock)  where STAGE_ID=a.STAGE_ID FOR XML PATH('')),'<USER_NO>' ,''),'</USER_NO>' ,',')  LIKE '%{model.Search.WhereKeyword}%')";
                //or REPLACE(REPLACE ((select USER_NO from ADDT35 (nolock)  where STAGE_ID=a.STAGE_ID FOR XML PATH('')),'<USER_NO>' ,''),'</USER_NO>' ,',')  LIKE @WhereKeyword )";
            }

            if (model.Search.WhereSYEAR != null && model.Search.WhereSYEAR != 0)
            {
                sSQL = sSQL + @" and a.STAGE_ID in (select b.STAGE_ID from ADDT35 b (nolock) where b.SCHOOL_NO =@SCHOOL_NO
                                     and b.SYEAR=@WhereSYEAR)";
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereGrade))
            {
                sSQL = sSQL + @" and a.STAGE_ID in (select b.STAGE_ID from ADDT35 b (nolock) where b.SCHOOL_NO =@SCHOOL_NO
                                     and b.GRADE=@WhereGrade)";
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereCLASS_NO))
            {
                sSQL = sSQL + @" and a.STAGE_ID in (select b.STAGE_ID from ADDT35 b (nolock) where b.SCHOOL_NO =@SCHOOL_NO
                                     and b.CLASS_NO=@WhereCLASS_NO)";
            }

            //首播影片
            if (model.ActionResultType == ADDI12IndexListViewModel.ActionResultTypeVal.PremierView)
            {
                sSQL = sSQL + $@" and a.IS_PREMIER=1 and  CONVERT(nvarchar(10), a.STAGE_DATES ,111) <=  CONVERT(nvarchar(10),getdate(),111) and CONVERT(nvarchar(10), a.STAGE_DATEE ,111) >=  CONVERT(nvarchar(10),getdate(),111) ";
            } //我的影片
            else if (model.ActionResultType == ADDI12IndexListViewModel.ActionResultTypeVal.MyVideoView)
            {
                sSQL = sSQL + $@" and a.STAGE_ID in (select b.STAGE_ID from ADDT35 b (nolock) where b.SCHOOL_NO ='{User.SCHOOL_NO}'
                                  and b.USER_NO='{User.USER_NO}')
                                  and ((a.IS_PREMIER=1 and  CONVERT(nvarchar(10), a.STAGE_DATES ,111) <=  CONVERT(nvarchar(10),getdate(),111) and CONVERT(nvarchar(10), a.STAGE_DATEE ,111) >=  CONVERT(nvarchar(10),getdate(),111))
                                        or ( a.IS_PREMIER=1 and CONVERT(nvarchar(10), a.STAGE_DATEE ,111) <  CONVERT(nvarchar(10),getdate(),111) )
                                        or (a.IS_PREMIER=0 )  )";
            }//我上傳的影片
            else if (model.ActionResultType == ADDI12IndexListViewModel.ActionResultTypeVal.MyUploadVideoView)
            {
                sSQL = sSQL + $@" and a.CRE_PERSON='{User.USER_KEY}'  ";
            }
            else //一般
            {
                sSQL = sSQL + $@" and ( (a.IS_PREMIER=0 ) OR (a.IS_PREMIER=1 and CONVERT(nvarchar(10), a.STAGE_DATEE ,111) <  CONVERT(nvarchar(10),getdate(),111)) ) ";
            }

            var temp = db.Database.Connection.Query<ADDI12IndexListDataViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = model.Search.WhereSCHOOL_NO,
                    USER_NO = model.Search.WhereUSER_NO,

                    WhereSYEAR = model.Search.WhereSYEAR,
                    WhereKeyword = "%" + model.Search.WhereKeyword + "%",
                    WhereGrade = model.Search.WhereGrade,
                    WhereCLASS_NO = model.Search.WhereCLASS_NO,
                });

            //首播影片
            if (model.ActionResultType == ADDI12IndexListViewModel.ActionResultTypeVal.PremierView)
            {
                temp = temp.OrderByDescending(a => a.STAGE_DATES).ThenBy(a => a.STAGE_DATEE).ThenByDescending(a => a.CRE_DATE).ThenBy(a => a.STAGE_NAME);
            }
            else
            {
                temp = temp.OrderByDescending(a => a.CRE_DATE).ThenBy(a => a.STAGE_NAME);
            }

            return temp;
        }
    }
}