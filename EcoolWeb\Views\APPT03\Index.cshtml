﻿@model ECOOL_APP.com.ecool.Models.DTO.APPT03IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    string style = "width:100%;margin: 0px auto;";
    if (Model.REF_TABLE != "APPT03")
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        style = "width:90%;margin: 0px auto;";
    }

}

@if (Model.REF_TABLE != "APPT03")
{
  <br/>
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div style="@style">

    @using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
    {
        @Html.AntiForgeryToken()


        <div id="QuerySelect">
            @Html.Action("_QuerySelect", (string)ViewBag.BRE_NO, new { REF_TABLE = Model.REF_TABLE, REF_KEY = Model.REF_KEY, STATUS = Model.STATUS })
        </div>

        <div id="QuerySelectDataList">
            @Html.Action("_QuerySelectDataList", (string)ViewBag.BRE_NO, new { REF_TABLE = Model.REF_TABLE, REF_KEY = Model.REF_KEY, STATUS = Model.STATUS })
        </div>
        <div class="text-center">
            @if (Model.REF_TABLE == "APPT03")
            {
                @Html.ActionLink("確定匯入", "Edit", new { REF_TABLE = Model.REF_TABLE, REF_KEY = Model.REF_KEY }, new { @class = "btn btn-default", @role = "button" })
            }
            else
            {
                <a id="OKbutton" onclick="OKbuttonFun()" role="button" class = "btn btn-default">選取完成</a>
            }
        </div>
    }

</div>
<script type="text/javascript">
  function OKbuttonFun()
    {
       var URL ='@Url.Action("Index", "APPT03")'+'?REF_TABLE=@Model.REF_TABLE&REF_KEY=@Model.REF_KEY&BTN_ID=@Model.BTN_ID'
   
        $('#REF_KEY', window.parent.document).attr( "value", "@Model.REF_KEY");
        $('@Model.BTN_ID', window.parent.document).attr( "href", URL);

        parent.$.fn.colorbox.close();//關閉視窗
    }

</script>