/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeFourSym/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXSizeFourSym-bold"]={directory:"SizeFourSym/Bold",family:"STIXSizeFourSym",weight:"bold",32:[0,0,250,0,0],40:[2604,471,818,115,761],41:[2604,471,818,57,703],47:[2604,471,1321,3,1318],91:[2604,471,699,314,691],92:[2604,471,1321,3,1318],93:[2604,471,699,8,385],123:[2604,471,1119,197,944],125:[2604,471,1119,175,922],160:[0,0,250,0,0],8730:[1510,345,1184,101,915],8968:[2604,471,720,314,712],8969:[2604,471,720,8,406],8970:[2604,471,720,314,712],8971:[2604,471,720,8,406],10216:[2604,471,908,120,841],10217:[2604,471,908,67,788]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeFourSym-bold");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeFourSym/Bold/Main.js");
