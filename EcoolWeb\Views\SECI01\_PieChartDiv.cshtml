﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI01IndexViewModel

@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<body class="text-center">
 
    @if (Model.SumItemDescCASHList !=null && Model.SumItemDescCASHList.Count > 0)
    {
        <div class="row">
            <div class="col-sm-4">
                @if (Model.PreCashPieChart != null)
                {
                    @Model.PreCashPieChart
                }
            </div>
            <div class="col-sm-8">
                @if (Model.CashPreColumnChart != null)
                {
                    @Model.CashPreColumnChart
                }
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-sm-12">
                <table class="table table-bordered table-ecool-info-hover table-ecool-info">
                    <thead>
                        <tr class="text-center">
                            <th>
                                功能名稱
                            </th>
                            <th>
                                獲得點數
                            </th>
                            <th>
                                百分比
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var CASHItemDesc in Model.SumItemDescCASHList)
                        {
                            <tr>
                                <td title="功能名稱" class="text-center">@CASHItemDesc.LOG_DESC</td>
                                <td title="獲得點數" class="text-right">@CASHItemDesc.SUM_ADD_CASH_ALL</td>
                                <td title="百分比" class="text-right">@CASHItemDesc.PRE.ToString("0.0")%</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr class="text-center">
                            <td>累計酷幣點數</td>
                            <td class="text-right">@Model.CASH_ALL</td>
                            <td></td>
                        </tr>
                        <tr class="text-center">
                            <td>現有酷幣點數</td>
                            <td class="text-right">@Model.CASH_AVAILABLE</td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    }
    else
    {
       <h3>無任何酷幣點數資料</h3>
    }
</body>



