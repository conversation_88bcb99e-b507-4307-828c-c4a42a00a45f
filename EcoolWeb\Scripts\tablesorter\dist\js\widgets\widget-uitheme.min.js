(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: uitheme - updated 2018-03-18 (v2.30.0) */
!function(A){"use strict";var N=A.tablesorter||{};N.themes={bootstrap:{table:"table table-bordered table-striped",caption:"caption",header:"bootstrap-header",sortNone:"",sortAsc:"",sortDesc:"",active:"",hover:"",icons:"",iconSortNone:"bootstrap-icon-unsorted",iconSortAsc:"glyphicon glyphicon-chevron-up",iconSortDesc:"glyphicon glyphicon-chevron-down",filterRow:"",footerRow:"",footerCells:"",even:"",odd:""},jui:{table:"ui-widget ui-widget-content ui-corner-all",caption:"ui-widget-content",header:"ui-widget-header ui-corner-all ui-state-default",sortNone:"",sortAsc:"",sortDesc:"",active:"ui-state-active",hover:"ui-state-hover",icons:"ui-icon",iconSortNone:"ui-icon-carat-2-n-s ui-icon-caret-2-n-s",iconSortAsc:"ui-icon-carat-1-n ui-icon-caret-1-n",iconSortDesc:"ui-icon-carat-1-s ui-icon-caret-1-s",filterRow:"",footerRow:"",footerCells:"",even:"ui-widget-content",odd:"ui-state-default"}},A.extend(N.css,{wrapper:"tablesorter-wrapper"}),N.addWidget({id:"uitheme",priority:10,format:function(e,o,t){var s,r,i,a,n,c,l,d,h,m,u,p,v,f=N.themes,b=o.$table.add(A(o.namespace+"_extra_table")),C=o.$headers.add(A(o.namespace+"_extra_headers")),w=o.theme||"jui",S=f[w]||{},g=A.trim([S.sortNone,S.sortDesc,S.sortAsc,S.active].join(" ")),j=A.trim([S.iconSortNone,S.iconSortDesc,S.iconSortAsc].join(" ")),D=N.debug(o,"uitheme");for(D&&(n=new Date),b.hasClass("tablesorter-"+w)&&o.theme===o.appliedTheme&&t.uitheme_applied||(t.uitheme_applied=!0,m=f[o.appliedTheme]||{},u=(v=!A.isEmptyObject(m))?[m.sortNone,m.sortDesc,m.sortAsc,m.active].join(" "):"",p=v?[m.iconSortNone,m.iconSortDesc,m.iconSortAsc].join(" "):"",v&&(t.zebra[0]=A.trim(" "+t.zebra[0].replace(" "+m.even,"")),t.zebra[1]=A.trim(" "+t.zebra[1].replace(" "+m.odd,"")),o.$tbodies.children().removeClass([m.even,m.odd].join(" "))),S.even&&(t.zebra[0]+=" "+S.even),S.odd&&(t.zebra[1]+=" "+S.odd),b.children("caption").removeClass(m.caption||"").addClass(S.caption),d=b.removeClass((o.appliedTheme?"tablesorter-"+(o.appliedTheme||""):"")+" "+(m.table||"")).addClass("tablesorter-"+w+" "+(S.table||"")).children("tfoot"),o.appliedTheme=o.theme,d.length&&d.children("tr").removeClass(m.footerRow||"").addClass(S.footerRow).children("th, td").removeClass(m.footerCells||"").addClass(S.footerCells),C.removeClass((v?[m.header,m.hover,u].join(" "):"")||"").addClass(S.header).not(".sorter-false").unbind("mouseenter.tsuitheme mouseleave.tsuitheme").bind("mouseenter.tsuitheme mouseleave.tsuitheme",function(e){A(this)["mouseenter"===e.type?"addClass":"removeClass"](S.hover||"")}),C.each(function(){var e=A(this);e.find("."+N.css.wrapper).length||e.wrapInner('<div class="'+N.css.wrapper+'" style="position:relative;height:100%;width:100%"></div>')}),o.cssIcon&&C.find("."+N.css.icon).removeClass(v?[m.icons,p].join(" "):"").addClass(S.icons||""),N.hasWidget(o.table,"filter")&&(r=function(){b.children("thead").children("."+N.css.filterRow).removeClass(v&&m.filterRow||"").addClass(S.filterRow||"")},t.filter_initialized?r():b.one("filterInit",function(){r()}))),s=0;s<o.columns;s++)c=o.$headers.add(A(o.namespace+"_extra_headers")).not(".sorter-false").filter('[data-column="'+s+'"]'),l=N.css.icon?c.find("."+N.css.icon):A(),(h=C.not(".sorter-false").filter('[data-column="'+s+'"]:last')).length&&(c.removeClass(g),l.removeClass(j),h[0].sortDisabled?l.removeClass(S.icons||""):(i=S.sortNone,a=S.iconSortNone,h.hasClass(N.css.sortAsc)?(i=[S.sortAsc,S.active].join(" "),a=S.iconSortAsc):h.hasClass(N.css.sortDesc)&&(i=[S.sortDesc,S.active].join(" "),a=S.iconSortDesc),c.addClass(i),l.addClass(a||"")));D&&console.log("uitheme >> Applied "+w+" theme"+N.benchmark(n))},remove:function(e,o,t,s){if(t.uitheme_applied){var r=o.$table,i=o.appliedTheme||"jui",a=N.themes[i]||N.themes.jui,n=r.children("thead").children(),c=a.sortNone+" "+a.sortDesc+" "+a.sortAsc,l=a.iconSortNone+" "+a.iconSortDesc+" "+a.iconSortAsc;r.removeClass("tablesorter-"+i+" "+a.table),t.uitheme_applied=!1,s||(r.find(N.css.header).removeClass(a.header),n.unbind("mouseenter.tsuitheme mouseleave.tsuitheme").removeClass(a.hover+" "+c+" "+a.active).filter("."+N.css.filterRow).removeClass(a.filterRow),n.find("."+N.css.icon).removeClass(a.icons+" "+l))}}})}(jQuery);return jQuery;}));
