﻿using Dapper;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI27Service
    {
        public ZZZI27EditViewModel GetEditData(ZZZI27EditViewModel model, ref ECOOL_DEVEntities db)
        {
            string SCHOOL_NOItem = "";
            string sSQL = $@"Select a.BRE_NO,a.DATA_CODE, a.DATA_TYPE,b.SCHOOL_NO, b.ITEM_NO,  b.CONTENT_TXT,b.CONTENT_VAL, isnull(a.MEMO,a.DATA_DESC) MEMO ,
                a.ADD_MODE,a.TXT_MODE, a.VAL_MODE,a.TXT_SCHOOL_SET_YN,a.VAL_SCHOOL_SET_YN,a.Close_YN , b.DelayTime,b.IS_SELECT
                from BDMT02 a (nolock)
                join  BDMT02_REF b (nolock) on a.BRE_NO=b.BRE_NO and a.DATA_CODE=b.DATA_CODE and a.DATA_TYPE=b.DATA_TYPE
                Where a.BRE_NO=@BRE_NO and a.DATA_CODE=@DATA_CODE
                and b.SCHOOL_NO=@SCHOOL_NO";
            if (model.Search.wBRE_NO == "Home" && model.Search.wDATA_CODE == "TeacherIndex")
            {
                sSQL += " and a.SCHOOL_NO=@SCHOOL_NO";
                SCHOOL_NOItem = (model.IsLoadingDefault) ? SharedGlobal.Default : model.Search.wSCHOOL_NO;
            }
            else
            {
                SCHOOL_NOItem = (model.IsLoadingDefault) ? SharedGlobal.ALL : model.Search.wSCHOOL_NO;
            }
            var Q_Temp = db.Database.Connection.Query<ZZZI27DetailsViewModel>(sSQL
              , new
              {
                  BRE_NO = model.Search.wBRE_NO,
                  DATA_CODE = model.Search.wDATA_CODE,
                  SCHOOL_NO = SCHOOL_NOItem
              }).AsQueryable();

            model.Details_List = Q_Temp.ToList();

            return model;
        }

        public bool SaveEditData(ZZZI27EditViewModel model, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            BDMT02 dMT02 = new BDMT02();

            if (model.Search.wBRE_NO == "Home" && model.Search.wDATA_CODE == "TeacherIndex")
            {
                dMT02 = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).FirstOrDefault();
            }
           
            else
            {
                dMT02 = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE).FirstOrDefault();
            }

            if (dMT02 != null)
            {
                dMT02.Close_YN = model.T_BDMT02 == null ? "N" : model.T_BDMT02.Close_YN;
                model.T_BDMT02 = dMT02;
            }
            else
            {
                if (model.Search.wBRE_NO == "Home" && model.Search.wDATA_CODE == "TeacherIndex")
                {
                    BDMT02 nMT02 = new BDMT02();
                    nMT02.DATA_TYPE = BDMT02_ENUM.DataType.DataTypeMenu;
                    nMT02.DATA_DESC = "跳出公告";
                    nMT02.MEMO = "公告";
                    nMT02.TXT_MODE = "STRING";
                    nMT02.VAL_MODE = "STRING";
                    nMT02.BRE_NO = model.Search.wBRE_NO;
                    nMT02.DATA_CODE = model.Search.wDATA_CODE;
                    nMT02.SCHOOL_NO = model.Search.wSCHOOL_NO;
                    nMT02.ALL_SCHOOL_SET_YN = "Y";
                    nMT02.TXT_SCHOOL_SET_YN = "Y";
                    nMT02.VAL_SCHOOL_SET_YN = "N";
                    db.BDMT02.Add(nMT02);
                    db.SaveChanges();
                }
            }

            model.T_BDMT02 = dMT02;
            if (model.T_BDMT02 != null)
            {
                if (model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Multi)
                {
                    var BTList = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).ToList();
                    var REF = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.DATA_TYPE == model.T_BDMT02.DATA_TYPE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).ToList();
                    if (model.Search.wBRE_NO == "Home" && model.Search.wDATA_CODE == "TeacherIndex" && model.Details_List != null)
                    {
                        foreach (var T02 in BTList)
                        {
                            var Keyin = model.Details_List.Where(b => b.BRE_NO == T02.BRE_NO && b.DATA_CODE == T02.DATA_CODE && b.DATA_TYPE == T02.DATA_TYPE).FirstOrDefault();
                            if (Keyin != null)
                            {
                                T02.Close_YN = model.T_BDMT02.Close_YN;
                            }
                        }
                    }
                    if (REF.Count() > 0)
                    {
                        db.BDMT02_REF.RemoveRange(REF);
                        REF.Clear();
                    }

                    int ITEM_NO = 1;
                    if (model?.Details_List != null)
                    {
                        foreach (var item in model?.Details_List)
                        {
                            BDMT02_REF SaveUp = new BDMT02_REF();

                            SaveUp.BRE_NO = model.T_BDMT02.BRE_NO;
                            SaveUp.DATA_CODE = model.T_BDMT02.DATA_CODE;
                            SaveUp.DATA_TYPE = model.T_BDMT02.DATA_TYPE;
                            SaveUp.SCHOOL_NO = model.Search.wSCHOOL_NO;
                            SaveUp.ITEM_NO = new StringHelper().StrRigth("00" + ITEM_NO, 3);

                            if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.Y && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.N)
                            {
                                if (string.IsNullOrWhiteSpace(item.CONTENT_TXT))
                                {
                                    Message = $"第{ITEM_NO}筆-內容未輸入<br/>";
                                }

                                SaveUp.CONTENT_TXT = item.CONTENT_TXT;
                                SaveUp.CONTENT_VAL = item.CONTENT_TXT;
                            }
                            else if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.N && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.Y)
                            {
                                if (string.IsNullOrWhiteSpace(item.CONTENT_VAL))
                                {
                                    Message = $"第{ITEM_NO}筆-值未輸入<br/>";
                                }

                                SaveUp.CONTENT_TXT = item.CONTENT_VAL;
                                SaveUp.CONTENT_VAL = item.CONTENT_VAL;
                            }
                            else
                            {
                                if (string.IsNullOrWhiteSpace(item.CONTENT_TXT))
                                {
                                    Message = $"第{ITEM_NO}筆-內容未輸入<br/>";
                                }
                                if (string.IsNullOrWhiteSpace(item.CONTENT_VAL))
                                {
                                    Message = $"第{ITEM_NO}筆-值未輸入<br/>";
                                }

                                SaveUp.CONTENT_TXT = item.CONTENT_TXT;
                                SaveUp.CONTENT_VAL = item.CONTENT_VAL;
                            }

                            SaveUp.CHG_PERSON = User.USER_KEY;
                            SaveUp.CHG_DATE = DateTime.Now;
                            SaveUp.DEL_YN = SharedGlobal.N;

                            REF.Add(SaveUp);

                            ITEM_NO++;
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        Message = "系統發生錯誤;原因:以下內容錯誤<br/>" + Message;
                        return false;
                    }

                    try
                    {
                        if (REF.Count > 0)
                        {
                            db.BDMT02_REF.AddRange(REF);
                            db.SaveChanges();
                        }
                        else
                        {
                            Message = "系統發生錯誤;原因:未輸入任何資料";
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }
                else if (model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Check&&model.T_BDMT02.DATA_CODE != "BarcCodeMyCash_Mode")
                {
                    var BTList = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).ToList();
                    var REF = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.DATA_TYPE == model.T_BDMT02.DATA_TYPE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).ToList();
                    if (REF.Count() > 0)
                    {
                        db.BDMT02_REF.RemoveRange(REF);
                        REF.Clear();
                    }
                    int ITEM_NO = 1;

                    if (model?.Details_List != null)
                    {
                        foreach (var item in model?.Details_List)
                        {
                            BDMT02_REF SaveUp = new BDMT02_REF();

                            SaveUp.BRE_NO = model.T_BDMT02.BRE_NO;
                            SaveUp.DATA_CODE = model.T_BDMT02.DATA_CODE;
                            SaveUp.DATA_TYPE = model.T_BDMT02.DATA_TYPE;
                            SaveUp.SCHOOL_NO = model.Search.wSCHOOL_NO;
                            SaveUp.ITEM_NO = "1";
                            if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.N && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.Y)
                            {
                                if (string.IsNullOrWhiteSpace(item.CONTENT_VAL))
                                {
                                    Message = $"請點選選項<br/>";
                                }

                                SaveUp.CONTENT_TXT = item.CONTENT_VAL;
                                SaveUp.CONTENT_VAL = item.CONTENT_VAL;
                            }
                            SaveUp.CONTENT_TXT = item.CONTENT_VAL;
                            SaveUp.CONTENT_VAL = item.CONTENT_VAL;
                            SaveUp.CHG_PERSON = User.USER_KEY;
                            SaveUp.CHG_DATE = DateTime.Now;
                            SaveUp.DEL_YN = SharedGlobal.N;

                            REF.Add(SaveUp);
                            ITEM_NO++;


                        }
                    }
                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        Message = "系統發生錯誤;原因:以下內容錯誤<br/>" + Message;
                        return false;
                    }

                    try
                    {
                        if (REF.Count > 0)
                        {
                            db.BDMT02_REF.AddRange(REF);
                            db.SaveChanges();
                        }
                        else
                        {
                            Message = "系統發生錯誤;原因:未輸入任何資料";
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }
                else if (model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Check && model.T_BDMT02.DATA_CODE == "BarcCodeMyCash_Mode")
                {
                    var BTList = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).ToList();
                    var REF = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.DATA_TYPE == model.T_BDMT02.DATA_TYPE && A.SCHOOL_NO == model.Search.wSCHOOL_NO).ToList();
                    var REFBarcCodeMyCash = new List<BDMT02_REF>();
                    if (REF.Count() > 0 && model.T_BDMT02.DATA_CODE!= "BarcCodeMyCash_Mode")
                    {
                        db.BDMT02_REF.RemoveRange(REF);
                        REF.Clear();
                    }
                    int ITEM_NO = 1;

                    if (model?.Details_List != null)
                    {
                        decimal? DelayTime = 0;
                        //BarcCodeMyCash_Mode
                        if (model.T_BDMT02.DATA_CODE == "BarcCodeMyCash_Mode")
                        {
                            DelayTime =model?.Details_List.FirstOrDefault().DelayTime;
                            if (REF.Count() > 0)
                            {

                                foreach (var item in REF)
                                {

                                    if (model.Details_List.FirstOrDefault().MEMO == item.CONTENT_TXT)
                                    {


                                        item.IS_SELECT = 1;

                                    }
                                    else
                                    {
                                        item.IS_SELECT = 0;
                                    }
                                    item.DelayTime = DelayTime;
                                    db.BDMT02_REF.Attach(item);
                                    db.Entry(item).Property(p => p.IS_SELECT).IsModified = true;
                                    db.Entry(item).Property(p => p.DelayTime).IsModified = true;
                                }

                            }
                            else
                            {
                                var REF_temp = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.DATA_TYPE == model.T_BDMT02.DATA_TYPE && A.SCHOOL_NO == "ALL").ToList();
                                foreach (var item in REF_temp)
                                {
                                    BDMT02_REF SaveUp = new BDMT02_REF();

                                    SaveUp.BRE_NO = model.T_BDMT02.BRE_NO;
                                    SaveUp.DATA_CODE = model.T_BDMT02.DATA_CODE;
                                    SaveUp.DATA_TYPE = model.T_BDMT02.DATA_TYPE;
                                    SaveUp.SCHOOL_NO = model.Search.wSCHOOL_NO;
                                    SaveUp.ITEM_NO = ITEM_NO.ToString();
                                    if (model.Details_List.FirstOrDefault().MEMO == item.CONTENT_TXT)
                                    {


                                        SaveUp.IS_SELECT = 1;

                                    }
                                    else
                                    {
                                        SaveUp.IS_SELECT = 0;
                                    }

                                    SaveUp.CONTENT_TXT = item.CONTENT_VAL;
                                    SaveUp.CONTENT_VAL = item.CONTENT_VAL;
                                    SaveUp.CHG_PERSON = User.USER_KEY;
                                    SaveUp.CHG_DATE = DateTime.Now;
                                    SaveUp.DEL_YN = SharedGlobal.N;
                                    SaveUp.DelayTime = DelayTime;
                                    REFBarcCodeMyCash.Add(SaveUp);
                                    ITEM_NO++;

                                }
                            }

                        } 
                        //BarcCodeMyCash_Mode
                        else {


                      
                        foreach (var item in model?.Details_List)
                        {
                            BDMT02_REF SaveUp = new BDMT02_REF();

                            SaveUp.BRE_NO = model.T_BDMT02.BRE_NO;
                            SaveUp.DATA_CODE = model.T_BDMT02.DATA_CODE;
                            SaveUp.DATA_TYPE = model.T_BDMT02.DATA_TYPE;
                            SaveUp.SCHOOL_NO = model.Search.wSCHOOL_NO;
                            SaveUp.ITEM_NO = "1";
                            if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.N && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.Y)
                            {
                                if (string.IsNullOrWhiteSpace(item.CONTENT_VAL))
                                {
                                    Message = $"請點選選項<br/>";
                                }

                                SaveUp.CONTENT_TXT = item.CONTENT_VAL;
                                SaveUp.CONTENT_VAL = item.CONTENT_VAL;
                            }
                            SaveUp.CONTENT_TXT = item.CONTENT_VAL;
                            SaveUp.CONTENT_VAL = item.CONTENT_VAL;
                            SaveUp.CHG_PERSON = User.USER_KEY;
                            SaveUp.CHG_DATE = DateTime.Now;
                            SaveUp.DEL_YN = SharedGlobal.N;

                                REF.Add(SaveUp);
                            ITEM_NO++;


                            }
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        Message = "系統發生錯誤;原因:以下內容錯誤<br/>" + Message;
                        return false;
                    }

                    try
                    {
                        if (REF.Count > 0 && model.T_BDMT02.DATA_CODE != "BarcCodeMyCash_Mode")
                        {
                            db.BDMT02_REF.AddRange(REF);
                            db.SaveChanges();
                        }
                        else if (model.T_BDMT02.DATA_CODE == "BarcCodeMyCash_Mode") {
                            if (REFBarcCodeMyCash.Count > 0)
                            {
                                db.BDMT02_REF.AddRange(REFBarcCodeMyCash);


                            }
                            else {

                                //db.BDMT02_REF.Attach(REF);
                                //db.Entry(person).Property(p => p.FirstName).IsModified = true;

                            }
                            try {
                                db.SaveChanges();

                            }
                            catch (Exception e) {
                              string str=  e.Message;

                            }
                 
                        }
                        else
                        {
                            Message = "系統發生錯誤;原因:未輸入任何資料";
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }
                else
                {
                    var BTList = db.BDMT02.Where(A => A.DATA_TYPE != BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE).ToList();

                    foreach (var T02 in BTList)
                    {
                        var Keyin = model.Details_List.Where(b => b.BRE_NO == T02.BRE_NO && b.DATA_CODE == T02.DATA_CODE && b.DATA_TYPE == T02.DATA_TYPE).FirstOrDefault();

                        if (Keyin != null)
                        {
                            BDMT02_REF SaveUp = null;

                            SaveUp = db.BDMT02_REF.Where(b => b.BRE_NO == T02.BRE_NO && b.DATA_CODE == T02.DATA_CODE && b.DATA_TYPE == T02.DATA_TYPE && b.SCHOOL_NO == model.Search.wSCHOOL_NO).FirstOrDefault();

                            if (SaveUp != null)
                            {
                                
                                if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.Y && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.N)
                                {
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_TXT))
                                    {
                                        Message = $"{T02.MEMO}-內容未輸入<br/>";
                                    }

                                    SaveUp.CONTENT_TXT = Keyin.CONTENT_TXT;
                                    SaveUp.CONTENT_VAL = Keyin.CONTENT_TXT;
                                }
                                else if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.N && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.Y)
                                {
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_VAL))
                                    {
                                        Message = $"{T02.MEMO}-值未輸入<br/>";
                                    }

                                    SaveUp.CONTENT_TXT = Keyin.CONTENT_VAL;
                                    SaveUp.CONTENT_VAL = Keyin.CONTENT_VAL;
                                }
                                else
                                {
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_TXT))
                                    {
                                        Message = $"{T02.MEMO}-內容未輸入<br/>";
                                    }
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_VAL))
                                    {
                                        Message = $"{T02.MEMO}-值未輸入<br/>";
                                    }

                                    SaveUp.CONTENT_TXT = Keyin.CONTENT_TXT;
                                    SaveUp.CONTENT_VAL = Keyin.CONTENT_VAL;
                                }
                                if (model.Search.wBRE_NO == "Home" && model.Search.wDATA_CODE == "TeacherIndex")
                                {
                                    SaveUp.SCHOOL_NO = Keyin.SCHOOL_NO;
                                }
                                SaveUp.SCHOOL_NO = model.Search.wSCHOOL_NO;
                                SaveUp.CHG_PERSON = User.USER_KEY;
                                SaveUp.CHG_DATE = DateTime.Now;
                                db.BDMT02_REF.Add(SaveUp);
                            }
                            else
                            {
                                SaveUp = new BDMT02_REF();

                                SaveUp.BRE_NO = model.T_BDMT02.BRE_NO;
                                SaveUp.DATA_CODE = model.T_BDMT02.DATA_CODE;
                                if (model.T_BDMT02.DATA_CODE == "LAST_DAY_MEMO")
                                {
                                    SaveUp.DATA_TYPE = T02.DATA_TYPE;

                                }
                                else {

                                    SaveUp.DATA_TYPE = model.T_BDMT02.DATA_TYPE;
                                }
                                
                                SaveUp.SCHOOL_NO = model.Search.wSCHOOL_NO;
                                SaveUp.ITEM_NO = "001";

                                if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.Y && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.N)
                                {
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_TXT))
                                    {
                                        Message = $"{T02.MEMO}-內容未輸入<br/>";
                                    }

                                    SaveUp.CONTENT_TXT = Keyin.CONTENT_TXT;
                                    SaveUp.CONTENT_VAL = Keyin.CONTENT_TXT;
                                }
                                else if (model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.N && model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.Y)
                                {
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_VAL))
                                    {
                                        Message = $"{T02.MEMO}-值未輸入<br/>";
                                    }

                                    SaveUp.CONTENT_TXT = Keyin.CONTENT_VAL;
                                    SaveUp.CONTENT_VAL = Keyin.CONTENT_VAL;
                                }
                                else
                                {
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_TXT))
                                    {
                                        Message = $"{T02.MEMO}-內容未輸入<br/>";
                                    }
                                    if (string.IsNullOrWhiteSpace(Keyin.CONTENT_VAL))
                                    {
                                        Message = $"{T02.MEMO}-值未輸入<br/>";
                                    }

                                    SaveUp.CONTENT_TXT = Keyin.CONTENT_TXT;
                                    SaveUp.CONTENT_VAL = Keyin.CONTENT_VAL;
                                }

                                SaveUp.CHG_PERSON = User.USER_KEY;
                                SaveUp.CHG_DATE = DateTime.Now;
                                SaveUp.DEL_YN = SharedGlobal.N;

                                db.BDMT02_REF.Add(SaveUp);
                            }
                        }
                    }

                    try
                    {
                        if (db.SaveChanges() > 0)
                        {
                            db.SaveChanges();
                        }
                        else
                        {
                            Message = "系統發生錯誤;原因:未輸入任何資料";
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                } // End
            }

            return true;
        }

        public bool SaveEditDelData(ZZZI27EditViewModel model, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Search.wSCHOOL_NO == SharedGlobal.ALL)
            {
                Message = "系統發生錯誤;原因:預設值不能整筆刪除";
                return false;
            }

            model.T_BDMT02 = db.BDMT02.Where(A => A.DATA_TYPE == BDMT02_ENUM.DataType.DataTypeMenu && A.BRE_NO == model.Search.wBRE_NO && A.DATA_CODE == model.Search.wDATA_CODE).FirstOrDefault();

            var REF = db.BDMT02_REF.Where(A => A.BRE_NO == model.T_BDMT02.BRE_NO && A.DATA_CODE == model.T_BDMT02.DATA_CODE && A.SCHOOL_NO == model.Search.wSCHOOL_NO);

            if (model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Multi)
            {
                REF = REF.Where(a => a.DATA_TYPE == model.T_BDMT02.DATA_TYPE);
            }
            else
            {
                REF = REF.Where(a => a.DATA_TYPE != model.T_BDMT02.DATA_TYPE);
            }

            var REFList = REF.ToList();

            if (REFList.Count() > 0)
            {
                db.BDMT02_REF.RemoveRange(REFList);

                try
                {
                    if (db.SaveChanges() > 0)
                    {
                        db.SaveChanges();
                    }
                    else
                    {
                        Message = "系統發生錯誤;原因:未輸入任何資料";
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }
            }

            return true;
        }
    }
}