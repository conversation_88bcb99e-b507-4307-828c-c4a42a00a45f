﻿@model EcoolWeb.ViewModels.ZZZI36ImpoertExcelViewModel
@{
    ViewBag.Title = "匯入學生資料";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<br />
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("ImportStudentModifyZZZI08", "ZZZI36", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.ValidationSummary()
    @Html.AntiForgeryToken()

    <button type="button" class="btn btn-lg btn-default" data-toggle="modal" data-target="#excelModal">
        匯入Excel
    </button> <br />
    <div>
        1.建議下載<font style="color:red">舊的資料</font>修改後上傳<br />
        2.學期中的異動，只需要匯入<font style="color:red">轉出入人員</font>。<br />
        3.匯入後，請記得啟用<br />
    </div>
    @Html.Partial("_ExcelImportModal51")
}
<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        匯入Excel歷史紀錄
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th style="text-align: center">
                        學校代號
                    </th>
                    <th style="text-align: center">
                        學年
                    </th>
                    <th style="text-align: center;">
                        學期
                    </th>
                    <th style="text-align: center;">
                        筆數
                    </th>
                    <th style="text-align: center;">
                        操作時間
                    </th>
                    <th style="text-align: center">
                        資料類型
                    </th>
                    <th style="text-align: center">
                        舊檔下載
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (Model != null)
                {
                    foreach (var item in Model.ModifyInfoList.Where(x => x.IMPORT_TYPE == "Temp_STUDENT").ToList())
                    {
                        if (item.IMPORT_TYPE == "Temp_STUDENT")
                        {
                            item.IMPORT_TYPE = "學生資料";
                        }

                        <tr>
                            <td>
                                @Html.DisplayFor(modelItem => item.SCHOOL_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SEMESTER)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.RowsCount)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.EVENT_TIME)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.IMPORT_TYPE)
                            </td>
                            <td style="text-align: left;white-space:normal">
                                @Html.ActionLink("下載", "DownloadExcel", new { FilePath = item.FILE_PATH }) @*, new { @class="btn btn-sm btn-sys"})*@
                            </td>
                        </tr>
                    }

                }
            </tbody>
        </table>
    </div>
</div>