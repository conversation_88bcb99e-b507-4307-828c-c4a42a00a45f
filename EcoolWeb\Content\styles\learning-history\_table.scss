@include table-row-variant("thead-primary", desaturate(lighten($component-active-bg, 20%), 90%));
.table * {
  word-break: break-all;
}

table.table {
  max-width: 100%;
  font-size: 1.45rem;
  table {
    font-size: 1.45rem !important;
  }
  th,
  td {
    border-top: 0;
  }
  thead th {
    border-bottom: 0;
  }
}

.profile,
.record,
.honorRollInSchool,
.honorRollOutOffSchool {
  & .table {
    line-height: 1.25;
    margin-top: 1rem;
    th,
    td {
      padding: 0.65rem;
      border-top: 0;
      white-space: nowrap;
    }
    th {
      text-align: right;
      color: $th-color;
    }
  }
}

.record {
  //統計數量表格
  .table-record {
    font-size: 1.25rem;
    text-align: center;
    th {
      font-size: 1.5rem;
      text-align: center;
      background-color: $th-bgcolor;
    }
    tr {
      background-color: $td-bgcolor1;
      &:last-child {
        background-color: $td-bgcolor2;
      }
      td {
        white-space: nowrap;
      }
    }
  }
}

.ePassport,
.readingBooksData,
.healthData
 {
  .table {
    thead {
      th {
        background-color: $th-bgcolor;
      }
    }
  }
  .table-striped {
    tbody {
      tr {
        &:nth-of-type(odd) {
          background-color: $td-bgcolor1;
        }
        &:nth-of-type(even) {
          background-color: $td-bgcolor2;
        }
      }
    }
  }
}

.honorRollInSchool,
.honorRollOutOffSchool {
  .table {
    th,
    td {
      padding: 0.35rem;
    }
  }
}

.onlineSubmit,
.readingCertification {
  table.table {
    margin-bottom: 0;
    th {
      color: $th-color;
      text-align: right;
      white-space: nowrap;
    }
    th,
    td {
      padding: 0;
    }
  }
  .content {
    table {
      tr {
        font-size: 1.25rem;
      }
    }
  }
}
