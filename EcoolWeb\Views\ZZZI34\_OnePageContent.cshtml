﻿@model ZZZI34WorkIndexViewModel
@using ECOOL_APP.com.ecool.service
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}<!-- clipboard.js v1.7.1 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.7.1/clipboard.min.js"></script>
<link href="~/Content/styles/newArtgallery.min.css?V=@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
<style type="text/css">
    .modal {
        position: fixed;
        top: auto;
        right: 0;
        left: 0;
        z-index: 1040;
        display: none;
        overflow: auto;
        overflow-y: scroll;
    }
</style>

@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereUSER_NO)
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_NO)
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_TYPE)
@Html.HiddenFor(m => m.Search.WhereSTATUS)
@Html.HiddenFor(m => m.Search.WhereMyWork)
@Html.HiddenFor(m => m.Search.WhereSearch)
@Html.HiddenFor(m => m.Search.WherePHOTO_NO)
@Html.HiddenFor(m => m.WorkSearch.OrdercColumn)
@Html.HiddenFor(m => m.WorkSearch.SyntaxName)
@Html.HiddenFor(m => m.WorkSearch.Page)
@Html.HiddenFor(m => m.WhereIsColorboxForUser)
@Html.HiddenFor(m => m.ShareViewPHOTO_NO)
@Html.HiddenFor(m => m.WhereART_GALLERY_NOGallaryN1)
@Html.HiddenFor(m => m.WhereMyWork)
<div id="ShowDiv">
    @foreach (var item in Model.WorkListData)
    {
        if (Model.WhereART_GALLERY_NOGallaryN1 != null)
        {
            <div class="Art-navbar">
                @if (Model.FromURL == "ALL")
                {
                    <a href="../ZZZI34/QRCODEUrl?PHOTO_USER_NO=@item.PHOTO_USER_NO&PHOTO_SCHOOL_NO=@item.PHOTO_SCHOOL_NO" role="button" class="btn btn-home" title="回本藝廊首頁">
                        <i class='glyphicon glyphicon-home'></i>
                        <span class="text">藝廊</span>
                    </a>
                }
                else
                {
                    if (Model.Search != null && !string.IsNullOrEmpty(Model.Search.WhereUSER_NO) && !string.IsNullOrEmpty(Model.Search.WhereSCHOOL_NO))
                    {
                        <a href="../ZZZI34/QRCODEUrl?PHOTO_USER_NO=@Model.Search.WhereUSER_NO&PHOTO_SCHOOL_NO=@Model.Search.WhereSCHOOL_NO&WhereART_GALLERY_NO=@item.ART_GALLERY_NO" role="button" class="btn btn-home" title="回本藝廊首頁">
                            <i class='glyphicon glyphicon-home'></i>
                            <span class="text">藝廊</span>
                        </a>


                    }
                    else
                    {

                        <a href="../ZZZI34/QRCODEUrl?WhereART_GALLERY_NO=@item.ART_GALLERY_NO" role="button" class="btn btn-home" title="回本藝廊首頁">
                            <i class='glyphicon glyphicon-home'></i>
                            <span class="text">藝廊</span>
                        </a>
                    }

                    <a class="btn btn-home" role="button" href="@Url.Action("PortalIndex","Home")" title="e酷幣首頁">
                        <img src="../Content/images/icon-ecool.png" height="20" />
                        <span class="text">首頁</span>
                    </a>

                }
                @if (!string.IsNullOrWhiteSpace(Model.ShareViewPHOTO_NO))
                {
                    <a class="btn btn-primary btn-xs" role="button" href="@Url.Action("PortalIndex","Home")">
                        <i class='glyphicon glyphicon-home'></i>
                        e酷幣首頁
                    </a>
                }
            </div>}
    }

    @if (Model.WorkListData.Count() > 0)
    {
        <p>第 @Model.WorkListData.FirstOrDefault().PHOTO_ORDER_BY 張</p>}
    @if (Model.WorkListData.Count() == 0)
    {
        <div class="text-center">
            <h1>無任何資料</h1>
        </div>
    }

    @foreach (var item in Model.WorkListData)
    {

        <div class="Atrbtn-Area">
            @if ((string.IsNullOrWhiteSpace(Model?.ShareViewPHOTO_NO) && Model.WhereART_GALLERY_NOGallaryN1 != null) || (Model?.WhereMyWork != null && Model?.WhereMyWork == true && Model?.WhereIsColorboxForUser == true))
            {
                if (item.PHOTO_ORDER_BY != 1)
                {


                    <a role="button" class="Atrbtn-prev" onclick="FunNext()" href="javascript:;" title="上一幅作品">
                        <i class='glyphicon glyphicon-chevron-left'></i>
                    </a>
                }

                if (Model.WorkSearch.Page != Model.WorkListData.TotalItemCount || item.PHOTO_ORDER_BY == 1 || (Model?.WhereMyWork != null && Model?.WhereMyWork == true && Model?.WhereIsColorboxForUser == true))
                {

                    <a role="button" class="Atrbtn-next" onclick="FunPrev()" href="javascript:;" title="下一幅作品">
                        <i class='glyphicon glyphicon-chevron-right'></i>
                    </a>
                }
            }
            @*else
                {
                    if (item.PHOTO_ORDER_BY != 1)
                    {
                        <a role="button" class="Atrbtn-prev" onclick="FunNext()" href="javascript:;" title="上一幅作品">
                            <i class='glyphicon glyphicon-chevron-left'></i>
                        </a>
                    }

                    if (Model.WorkSearch.Page != Model.WorkListData.TotalItemCount || item.PHOTO_ORDER_BY == 1)
                    {

                        <a role="button" class="Atrbtn-next" onclick="FunPrev()" href="javascript:;" title="下一幅作品">
                            <i class='glyphicon glyphicon-chevron-right'></i>
                        </a>
                    }

                }*@
            @if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
            {
                string NewImg = item.PHOTO_FILE.Replace(Path.GetExtension(item.PHOTO_FILE), "_M" + Path.GetExtension(item.PHOTO_FILE));
                string ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, NewImg);
                if (ImgUrl == "")
                {
                    NewImg = item.PHOTO_FILE.Replace(Path.GetExtension(item.PHOTO_FILE), "_S" + Path.GetExtension(item.PHOTO_FILE));
                    ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, NewImg);
                }
                <div class="Atr-imgBox">
                    <img src="@ImgUrl" title="@item.PHOTO_SUBJECT" alt="@item.PHOTO_SUBJECT" class="img-responsive Atr-img" />
                </div>
            }
            @*else if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.video)
                {
                    string VideoUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE);
                    <div class="videoWrapper">
                        <video controls>
                            <source src="@VideoUrl" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                }*@
            else
            {
                <div class="Atr-videoBox">
                    <!--因應ios沒辦法在colorbox中載入iframe的解決方法-->
                    <div class="videoWrapper">
                        <iframe id="<EMAIL>" src=""></iframe>
                    </div>
                    <script>
                             $('#<EMAIL>').attr('src', '@item.PHOTO_FILE');
                    </script>
                </div>
            }
        </div>
        <div class="text-center">
            <div class="Art-infocard">
                <strong class="title" title="主題-@item.PHOTO_SUBJECT">@item.PHOTO_SUBJECT</strong>
                <span class="small">小畫家：@item.PHOTO_CLASS_NO   @item.PHOTO_NAME</span>

                @if (!string.IsNullOrEmpty(item.PHOTO_DESC))
                {

                    <span class="small">內容簡介：@item.PHOTO_DESC</span>
                }

                <div class="Atr-workActive">
                    <a class="btn-workActive" role="button" onclick="onWinOpenShareUrlLink()" href="javascript:;" title="跟好友分享本作品">
                        <i class='glyphicon glyphicon-link'></i>
                        分享
                    </a>
                    @if (item.STATUS == ADDT21.STATUSVal.Pass)
                    {
                        if ((item.IsLikeCount) > 0)
                        {
                            <a id="Link_@(item.PHOTO_NO)" class="btn-workActive" role="button" disabled href="javascript:;" title="您已經按讚過">
                                <i class='glyphicon glyphicon-thumbs-up'></i> 已按讚
                            </a>
                        }
                        else
                        {
                            <a id="Link_@(item.PHOTO_NO)" class="btn-workActive" role="button" href="javascript:;" onclick="SHARE_show('@item.PHOTO_NO','','@item.SHARE_COUNT')" title="按讚">
                                <i class='glyphicon glyphicon-thumbs-up'></i> 按讚
                            </a>
                        }
                    }
                    @if (item.SHARE_COUNT > 0)
                    {
                        <a class="btn btn-block" role="button" onclick="funAjaxGetLikeList('@(item.PHOTO_NO)')" href="javascript:;" title="檢視已按讚按讚人員">
                            <font color="red" id="Font_@(item.PHOTO_NO)">
                                <text>@(item.SHARE_COUNT) 人按讚</text>
                            </font>
                        </a>
                    }
                </div>
            </div>
            @Html.Pager(Model.WorkListData.PageSize, Model.WorkListData.PageNumber, Model.WorkListData.TotalItemCount).Options(o => o.DisplayTemplate("BootstrapPagination").MaxNrOfPages(1).SetPreviousPageText("上頁").SetNextPageText("下頁"))

        </div>
    }
</div>




<div class="modal fade bs-example-modal-lg" id="myShareUrlModal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-sm" role="document" style="top:200px">
        <div class="modal-content">

            <div class="input-group">
                <span class="input-group-btn">
                    <button type="button" id="id_copy"
                            data-clipboard-target="#id_text"
                            data-clipboard-action="copy" onclick="OnCopy()">
                        點擊複製
                    </button>
                </span>
                <div id="id_text">@ViewBag.WinOpenShareUrlLink</div>
                <div id="success" style="display:none">已複製</div>
                <input id="copyStr" type="hidden" value="@ViewBag.WinOpenYoutubeUrlLink">
            </div><!-- /input-group -->
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="myQrCodeModal" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="input-group">
                @if (ViewBag.WinOpenShareUrlLink != null)
                {
                    <img src="@Url.Action("Cre","Barcode", new {Value=ViewBag.WinOpenShareUrlLink })" style="max-width:150px" />
                }
            </div><!-- /input-group -->
        </div>
    </div>
</div>

<script type="text/javascript">

            $('.img-responsive').load(function () {
                     var WhereIsColorboxForUser = $('#@Html.IdFor(m=>m.WhereIsColorboxForUser)').val();

                if (WhereIsColorboxForUser.toLowerCase() != 'true' && $('#@Html.IdFor(m=>m.ShareViewPHOTO_NO)').val()=='') {
                    window.setTimeout(function () {
                        ResizePopUp();
                    }, 300);
                }
            });

            ResizePopUp = function () {
                var frameWidth = jQuery(".imgPopUpContainer").width();
                var frameHeight = jQuery(".imgPopUpContainer").height();
                try {
                    parent.jQuery.fn.colorbox.resize({ innerHeight: frameHeight, innerWidth: frameWidth });
                } catch (e) { }
            };

            function OnCopy() {
                var clipboard = new Clipboard("#id_copy");
                clipboard.on("success", function (element) {//複製成功的回調
                    console.info("複製成功，複製內容：    " + element.text);
                    $('#success').show()
                    $('#id_text').hide()

                    setTimeout('HidemySmallModal()', 2000);
                });
                clipboard.on("error", function (element) {//複製失敗的回調
                    console.info(element);
                });
            }

            function onWinOpenShareUrlLink() {

                $('#success').hide()
                $('#id_text').show()

                if ($('#myShareUrlModal').is(':visible') == false) {
                    $('#myShareUrlModal').modal('show');
                }
            }
            @*function FunNext() {
                    $("#Search_WhereART_GALLERY_NO").val('@Model.Search.WhereART_GALLERY_NO');

                    $('.One').find('#next').click()
                }

                function FunPrev() {
                    $("#Search_WhereART_GALLERY_NO").val('@Model.Search.WhereART_GALLERY_NO');

                    $('.One').find('#prev').click()
                }*@

            function onWinOpenShareQRCODE() {
                if ($('#myQrCodeModal').is(':visible') == false) {
                    $('#myQrCodeModal').modal('show');
                }
            }

            function HidemySmallModal() {
                $('#myShareUrlModal').modal('hide');
            }
</script>
