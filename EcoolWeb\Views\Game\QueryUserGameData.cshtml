﻿@using ECOOL_APP.EF
@using ECOOL_APP
@model GameCashQueryViewModel

@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();
    ADDT27 dDT27 = new ADDT27();
    dDT27 = Model.User;
    var booldDT27 = "N";
    if (dDT27 != null)
    {
        booldDT27 = "Y";
    }
}
<link href="@Url.Content("~/Content/css/childrens-month.css")" rel="stylesheet" />
<link href="@Url.Content("~/Content/css/bootstrap-select.css")" rel="stylesheet" />

@using (Html.BeginForm("QueryUserGameData", "Game", FormMethod.Post, new { id = "form1", name = "form1", @AutoComplete = "Off" }))
{
    @Html.HiddenFor(m => m.GAME_NO)

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div id="MainView">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel with-nav-tabs panel-primary" id="panel">
                    <div class="panel-heading">
                        <h1>
                            @Model.GameInfo.GAME_NAME-闖關查詢<strong id="status"></strong>
                            <a href="@Url.Action("QueryUserDataList", "Game", new { GAME_NO = Model.GAME_NO })" class="pull-right btn-success btn btn-sm">中獎查詢</a>
                        </h1>
                    </div>

                    <div class="panel-body">
                        <div id="PageContent">
                            <div role="form" id="Q_Div">

                                <ul class="nav nav-tabs nav-pills">
                                    <li><a data-toggle="tab" href="#query1" onclick="todoClear()">依學校及學號</a></li>
                                    <li class="active"><a data-toggle="tab" href="#query2" onclick="todoClear()">依臨時卡卡號/入場券號碼</a></li>
                                </ul>
                                <div class="tab-content">
                                    <div id="query1" class="tab-pane fade">
                                        <br />
                                        <label class="control-label" style="color:red">
                                            * 此處查詢限臺北市公私立國民小學學生，其他身份請至「依臨時卡卡號」查詢
                                            <br />
                                            &nbsp;&nbsp;此查詢學校及學號為必輸欄位
                                        </label>
                                        <br /> <br />
                                        <div class="form-group">
                                            <label class="control-label">* 學校</label>
                                        </div>
                                        <div class="form-group">

                                            @if (ViewBag.SchoolNoSelectItem != null)
                                            {
                                                <select class="selectpicker show-menu-arrow" title="" date-style="btn btn-default" data-size="auto" data-width="100%" data-live-search="true" id="CashSearch_WhereSCHOOL_NO" name="CashSearch.WhereSCHOOL_NO">
                                                    @foreach (var item in (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem)
                                                    {
                                                        <option data-tokens="@item.Text" value="@item.Value" @(item.Selected == true ? "selected" : "")>@item.Text</option>
                                                    }
                                                </select>
                                            }
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label">* 學號 (請參考數位學生證上之學籍號碼)</label>
                                        </div>
                                        <div class="form-group">
                                            @Html.EditorFor(model => model.CashSearch.WhereUSER_NO, new { htmlAttributes = new { @class = "form-control input-lg" } })
                                        </div>

                                        <div class="row" style="padding-top:10px">
                                            <div class="col-xs-6 col-md-3">
                                                <a href="#" class="thumbnail">
                                                    <img src="~/Content/img/student_card_new.jpg" />
                                                    數位學生證-新卡
                                                </a>
                                            </div>
                                            <div class="col-xs-6 col-md-3">
                                                <a href="#" class="thumbnail">
                                                    <img src="~/Content/img/student_card.jpg" />
                                                    數位學生證-舊卡
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="query2" class="tab-pane fade  in active">
                                        <br />
                                        <div class="form-group">
                                            <label class="control-label">臨時卡卡號/入場券號碼</label>
                                        </div>
                                        <div class="form-group">
                                            @Html.EditorFor(model => model.GameUserID, new { htmlAttributes = new { @class = "form-control input-lg" } })
                                        </div>
                                    </div>
                                </div>

                                <input type="submit" id="Searcher" class="btn-yellow btn btn-sm" value="搜尋" />
                                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
                                @if (TempData["StatusMessage"] != null)
                                {
                                    <h1 style="color:red">@Html.Raw(HttpUtility.HtmlDecode((string)TempData["StatusMessage"]))</h1>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    if (IsBtnGoHide == false)
    {
        <div id="DivAddButton">
            <i id="title" class="fa fa-arrow-left fa-3x"></i>
            <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回闖關地圖</button>
        </div>
    }
    else
    {
        <div class="text-center">
            <button type="button" onclick="CloseWin()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">關閉視窗</button>
        </div>
    }
}

<script src="@Url.Content("~/Scripts/bootstrap-select/bootstrap-select.js")"></script>
<script src="@Url.Content("~/Scripts/buzz/buzz.min.js")"></script>
<script>
    var targetFormID = '#form1';

    $(document).ready(function () {
        $('#CashSearch_WhereSCHOOL_NO').selectpicker();
        var GTEMP_USER_ID = "";
        GTEMP_USER_ID = "@ViewBag.TEMP_USER_ID";
        console.log(GTEMP_USER_ID);
        if (GTEMP_USER_ID != "")
        {

            $("#GameUserID").val(GTEMP_USER_ID);
          //  $(targetFormID).submit();
        }
        else {
            if ('@ViewBag.GUSER_NO' != "")
            {

        }

        }

        $(document).on('keypress', function (e) {
            if (e.which == 13) {
                FunPageProc(1)
            }
        });
    });

    function CloseWin() //這個不會提示是否關閉瀏覽器
    {
        open(location, '_self').close();
    }

    function OnBack() {
        $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")
        $(targetFormID).submit();
    }

    function todoClear() {
        ////重設

        $('#Q_Div').find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

       // FunPageProc(0)
    }
</script>