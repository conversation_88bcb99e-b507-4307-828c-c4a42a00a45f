﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'el', {
	euro: 'Σύμβολο Ευρώ',
	lsquo: 'Αριστερός χαρακτήρας μονού εισαγωγικού',
	rsquo: 'Δεξι<PERSON>ς χαρακτήρας μονού εισαγωγικού',
	ldquo: 'Αριστερός χαρακτήρας ευθύγραμμων εισαγωγικών',
	rdquo: 'Δεξι<PERSON>ς χαρακτήρας ευθύγραμμων εισαγωγικών',
	ndash: 'Παύλα en',
	mdash: 'Παύλα em',
	iexcl: 'Ανάποδο θαυμαστικ<PERSON>',
	cent: 'Σύμβολο σεντ',
	pound: 'Σύμβολο λίρας',
	curren: 'Σύμβολο συναλλαγματικής μονάδας',
	yen: 'Σύμβολο Γιεν',
	brvbar: 'Σπασμένη μπάρα',
	sect: 'Σύμβολο τμήματος',
	uml: 'Διαίρεση',
	copy: 'Σύμβολο πνευματικών δικαιωμάτων',
	ordf: 'Θηλυκός τακτικός δείκτης',
	laquo: 'Γωνιώδη εισαγωγικά αριστερής κατάδειξης',
	not: 'Σύμβολο άρνησης',
	reg: 'Σύμβολο σημάτων κατατεθέν',
	macr: 'Μακρόν',
	deg: 'Σύμβολο βαθμού',
	sup2: 'Εκτεθειμένο δύο',
	sup3: 'Εκτεθειμένο τρία',
	acute: 'Οξεία',
	micro: 'Σύμβολο μικρού',
	para: 'Σύμβολο παραγράφου',
	middot: 'Μέση τελεία',
	cedil: 'Υπογεγραμμένη',
	sup1: 'Εκτεθειμένο ένα',
	ordm: 'Αρσενικός τακτικός δείκτης',
	raquo: 'Γωνιώδη εισαγωγικά δεξιάς κατάδειξης',
	frac14: 'Γνήσιο κλάσμα ενός τετάρτου',
	frac12: 'Γνήσιο κλάσμα ενός δεύτερου',
	frac34: 'Γνήσιο κλάσμα τριών τετάρτων',
	iquest: 'Ανάποδο θαυμαστικό',
	Agrave: 'Λατινικό κεφαλαίο γράμμα A με βαρεία',
	Aacute: 'Λατινικό κεφαλαίο γράμμα A με οξεία',
	Acirc: 'Λατινικό κεφαλαίο γράμμα A με περισπωμένη',
	Atilde: 'Λατινικό κεφαλαίο γράμμα A με περισπωμένη',
	Auml: 'Λατινικό κεφαλαίο γράμμα A με διαλυτικά',
	Aring: 'Λατινικό κεφαλαίο γράμμα A με δακτύλιο επάνω',
	AElig: 'Λατινικό κεφαλαίο γράμμα Æ',
	Ccedil: 'Λατινικό κεφαλαίο γράμμα C με υπογεγραμμένη',
	Egrave: 'Λατινικό κεφαλαίο γράμμα E με βαρεία',
	Eacute: 'Λατινικό κεφαλαίο γράμμα E με οξεία',
	Ecirc: 'Λατινικό κεφαλαίο γράμμα Ε με περισπωμένη ',
	Euml: 'Λατινικό κεφαλαίο γράμμα Ε με διαλυτικά',
	Igrave: 'Λατινικό κεφαλαίο γράμμα I με βαρεία',
	Iacute: 'Λατινικό κεφαλαίο γράμμα I με οξεία',
	Icirc: 'Λατινικό κεφαλαίο γράμμα I  με περισπωμένη',
	Iuml: 'Λατινικό κεφαλαίο γράμμα I με διαλυτικά ',
	ETH: 'Λατινικό κεφαλαίο γράμμα Eth',
	Ntilde: 'Λατινικό κεφαλαίο γράμμα N με περισπωμένη',
	Ograve: 'Λατινικό κεφαλαίο γράμμα O με βαρεία',
	Oacute: 'Λατινικό κεφαλαίο γράμμα O με οξεία',
	Ocirc: 'Λατινικό κεφαλαίο γράμμα O με περισπωμένη ',
	Otilde: 'Λατινικό κεφαλαίο γράμμα O με περισπωμένη',
	Ouml: 'Λατινικό κεφαλαίο γράμμα O με διαλυτικά',
	times: 'Σύμβολο πολλαπλασιασμού',
	Oslash: 'Λατινικό κεφαλαίο γράμμα O με μολυβιά',
	Ugrave: 'Λατινικό κεφαλαίο γράμμα U με βαρεία',
	Uacute: 'Λατινικό κεφαλαίο γράμμα U με οξεία',
	Ucirc: 'Λατινικό κεφαλαίο γράμμα U με περισπωμένη',
	Uuml: 'Λατινικό κεφαλαίο γράμμα U με διαλυτικά',
	Yacute: 'Λατινικό κεφαλαίο γράμμα Y με οξεία',
	THORN: 'Λατινικό κεφαλαίο γράμμα Thorn',
	szlig: 'Λατινικό μικρό γράμμα απότομο s',
	agrave: 'Λατινικό μικρό γράμμα a με βαρεία',
	aacute: 'Λατινικό μικρό γράμμα a με οξεία',
	acirc: 'Λατινικό μικρό γράμμα a με περισπωμένη',
	atilde: 'Λατινικό μικρό γράμμα a με περισπωμένη',
	auml: 'Λατινικό μικρό γράμμα a με διαλυτικά',
	aring: 'Λατινικό μικρό γράμμα a με δακτύλιο πάνω',
	aelig: 'Λατινικό μικρό γράμμα æ',
	ccedil: 'Λατινικό μικρό γράμμα c με υπογεγραμμένη',
	egrave: 'Λατινικό μικρό γράμμα ε με βαρεία',
	eacute: 'Λατινικό μικρό γράμμα e με οξεία',
	ecirc: 'Λατινικό μικρό γράμμα e με περισπωμένη',
	euml: 'Λατινικό μικρό γράμμα e με διαλυτικά',
	igrave: 'Λατινικό μικρό γράμμα i με βαρεία',
	iacute: 'Λατινικό μικρό γράμμα i με οξεία',
	icirc: 'Λατινικό μικρό γράμμα i με περισπωμένη',
	iuml: 'Λατινικό μικρό γράμμα i με διαλυτικά',
	eth: 'Λατινικό μικρό γράμμα eth',
	ntilde: 'Λατινικό μικρό γράμμα n με περισπωμένη',
	ograve: 'Λατινικό μικρό γράμμα o με βαρεία',
	oacute: 'Λατινικό μικρό γράμμα o με οξεία ',
	ocirc: 'Λατινικό πεζό γράμμα o με περισπωμένη',
	otilde: 'Λατινικό μικρό γράμμα o με περισπωμένη ',
	ouml: 'Λατινικό μικρό γράμμα o με διαλυτικά',
	divide: 'Σύμβολο διαίρεσης',
	oslash: 'Λατινικό μικρό γράμμα o με περισπωμένη',
	ugrave: 'Λατινικό μικρό γράμμα u με βαρεία',
	uacute: 'Λατινικό μικρό γράμμα u με οξεία',
	ucirc: 'Λατινικό μικρό γράμμα u με περισπωμένη',
	uuml: 'Λατινικό μικρό γράμμα u με διαλυτικά',
	yacute: 'Λατινικό μικρό γράμμα y με οξεία',
	thorn: 'Λατινικό μικρό γράμμα thorn',
	yuml: 'Λατινικό μικρό γράμμα y με διαλυτικά',
	OElig: 'Λατινικό κεφαλαίο σύμπλεγμα ΟΕ',
	oelig: 'Λατινικό μικρό σύμπλεγμα oe',
	'372': 'Λατινικό κεφαλαίο γράμμα W με περισπωμένη',
	'374': 'Λατινικό κεφαλαίο γράμμα Y με περισπωμένη',
	'373': 'Λατινικό μικρό γράμμα w με περισπωμένη',
	'375': 'Λατινικό μικρό γράμμα y με περισπωμένη',
	sbquo: 'Ενιαίο χαμηλο -9 εισαγωγικό ',
	'8219': 'Ενιαίο υψηλο ανεστραμμένο-9 εισαγωγικό ',
	bdquo: 'Διπλό χαμηλό-9 εισαγωγικό ',
	hellip: 'Οριζόντια αποσιωπητικά',
	trade: 'Σύμβολο εμπορικού κατατεθέν',
	'9658': 'Μαύρος δείκτης που δείχνει προς τα δεξιά',
	bull: 'Κουκκίδα',
	rarr: 'Δεξί βελάκι',
	rArr: 'Διπλό δεξί βελάκι',
	hArr: 'Διπλό βελάκι αριστερά-δεξιά',
	diams: 'Μαύρο διαμάντι',
	asymp: 'Σχεδόν ίσο με'
} );
