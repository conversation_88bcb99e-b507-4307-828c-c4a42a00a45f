﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'ru', {
	border: 'Размер границ',
	caption: 'Заголовок',
	cell: {
		menu: 'Ячейка',
		insertBefore: 'Вставить ячейку слева',
		insertAfter: 'Вставить ячейку справа',
		deleteCell: 'Удалить ячейки',
		merge: 'Объединить ячейки',
		mergeRight: 'Объединить с правой',
		mergeDown: 'Объединить с нижней',
		splitHorizontal: 'Разделить ячейку по вертикали',
		splitVertical: 'Разделить ячейку по горизонтали',
		title: 'Свойства ячейки',
		cellType: 'Тип ячейки',
		rowSpan: 'Объединяет строк',
		colSpan: 'Объединяет колонок',
		wordWrap: 'Перенос по словам',
		hAlign: 'Горизонтальное выравнивание',
		vAlign: 'Вертикальное выравнивание',
		alignBaseline: 'По базовой линии',
		bgColor: 'Цвет фона',
		borderColor: 'Цвет границ',
		data: 'Данные',
		header: 'Заголовок',
		yes: 'Да',
		no: 'Нет',
		invalidWidth: 'Ширина ячейки должна быть числом.',
		invalidHeight: 'Высота ячейки должна быть числом.',
		invalidRowSpan: 'Количество объединяемых строк должно быть задано числом.',
		invalidColSpan: 'Количество объединяемых колонок должно быть задано числом.',
		chooseColor: 'Выберите'
	},
	cellPad: 'Внутренний отступ ячеек',
	cellSpace: 'Внешний отступ ячеек',
	column: {
		menu: 'Колонка',
		insertBefore: 'Вставить колонку слева',
		insertAfter: 'Вставить колонку справа',
		deleteColumn: 'Удалить колонки'
	},
	columns: 'Колонки',
	deleteTable: 'Удалить таблицу',
	headers: 'Заголовки',
	headersBoth: 'Сверху и слева',
	headersColumn: 'Левая колонка',
	headersNone: 'Без заголовков',
	headersRow: 'Верхняя строка',
	invalidBorder: 'Размер границ должен быть числом.',
	invalidCellPadding: 'Внутренний отступ ячеек (cellpadding) должен быть числом.',
	invalidCellSpacing: 'Внешний отступ ячеек (cellspacing) должен быть числом.',
	invalidCols: 'Количество столбцов должно быть больше 0.',
	invalidHeight: 'Высота таблицы должна быть числом.',
	invalidRows: 'Количество строк должно быть больше 0.',
	invalidWidth: 'Ширина таблицы должна быть числом.',
	menu: 'Свойства таблицы',
	row: {
		menu: 'Строка',
		insertBefore: 'Вставить строку сверху',
		insertAfter: 'Вставить строку снизу',
		deleteRow: 'Удалить строки'
	},
	rows: 'Строки',
	summary: 'Итоги',
	title: 'Свойства таблицы',
	toolbar: 'Таблица',
	widthPc: 'процентов',
	widthPx: 'пикселей',
	widthUnit: 'единица измерения'
} );
