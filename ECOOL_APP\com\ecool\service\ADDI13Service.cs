﻿using AutoMapper;
using <PERSON><PERSON>;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using log4net;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class ADDI13Service
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public ADDI13EditPeopleViewModel GetAddPersonData(string CARD_NO, ref ECOOL_DEVEntities db, string SCHOOL_NO = "")
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                ADDI13EditPeopleViewModel model = new ADDI13EditPeopleViewModel();

                model = (from b in db.HRMT01
                         join c in db.BDMT01 on b.SCHOOL_NO equals c.SCHOOL_NO
                         where (
                                        (b.CARD_NO == CARD_NO)
                                     || (b.SCHOOL_NO + b.CLASS_NO + b.SEAT_NO == SCHOOL_NO + CARD_NO)
                                     || (b.SCHOOL_NO + b.USER_NO == SCHOOL_NO + CARD_NO)
                                )
                         && b.USER_TYPE == UserType.Student
                         && (!UserStaus.NGUserStausList.Contains(b.USER_STATUS))
                         select new ADDI13EditPeopleViewModel()
                         {
                             SCHOOL_NO = b.SCHOOL_NO,
                             SHORT_NAME = c.SHORT_NAME,
                             USER_NO = b.USER_NO,
                             NAME = b.NAME,
                             GRADE = b.GRADE,
                             CLASS_NO = b.CLASS_NO,
                             SEAT_NO = b.SEAT_NO,
                             CARD_NO = b.CARD_NO,
                             CHG_DATE=DateTime.Now
                         }).FirstOrDefault();

                return model;
            }
        }

        public IResult checkBarcode(string Barcode, ref ECOOL_DEVEntities db, string SCHOOL_NO = "",HRMT01 hRMT= null)
        {
            bool ISBarcode = false;
            IResult result = new Result(false);
            string UserName = "";
            string CRE_PERSON = "";
            List<RollCallBarcodeEditMainViewModel> aDDT37s = new List<RollCallBarcodeEditMainViewModel>();
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
               
                aDDT37s = (from a in db.ADDT37
                           join b in db.ADDT38 on a.ROLL_CALL_ID equals b.ROLL_CALL_ID
                           where a.SCHOOL_NO == SCHOOL_NO && b.BarCode == Barcode
                           select new RollCallBarcodeEditMainViewModel
                           {
                               ROLL_CALL_ID = a.ROLL_CALL_ID,
                               ROLL_CALL_NAME =a .ROLL_CALL_NAME,
                               ROLL_CALL_DATES=a.ROLL_CALL_DATES,
                               ROLL_CALL_DATEE=a.ROLL_CALL_DATEE,
                               CASH=b.CASH,
                               CRE_PERSON = a.CRE_PERSON,
                               CRE_PERSON_NAME=""
                           }).ToList();
              
                if (aDDT37s.Count() > 0)
                {
                    ADDT38 aDDT38 = new ADDT38();
                    ADDT37 aDDT37Item= new ADDT37();
                    ADDT37 aDDT37TEMP = new ADDT37();
                    aDDT38 = db.ADDT38.Where(x => x.BarCode == Barcode && x.SCHOOL_NO == SCHOOL_NO).FutureFirstOrDefault();
                    aDDT37TEMP = db.ADDT37.Where(x=>x.ROLL_CALL_ID== aDDT38.ROLL_CALL_ID && x.SCHOOL_NO == SCHOOL_NO).FutureFirstOrDefault();
                    aDDT37Item.ROLL_CALL_DATES = aDDT37s.FirstOrDefault().ROLL_CALL_DATES;
                    aDDT37Item.ROLL_CALL_DATEE = aDDT37s.FirstOrDefault().ROLL_CALL_DATEE;
                    CRE_PERSON = aDDT37s.FirstOrDefault().CRE_PERSON;
                    UserName = db.HRMT01.Where(x => x.USER_KEY == CRE_PERSON).Select(x => x.NAME).FirstOrDefault();
                    if (!string.IsNullOrEmpty(aDDT38.USER_NO))
                    {
                        result.Success = false;
                        result.Message = "此代碼已經兌換過,如有問題請洽 " + UserName + "老師";
                    }
                    else  if (aDDT37TEMP.IS_Remark==false && hRMT!=null) {
                        int ADDT38Count = 0;
                        ADDT38Count = db.ADDT38.Where(x => x.ROLL_CALL_ID == aDDT38.ROLL_CALL_ID && x.SCHOOL_NO == hRMT.SCHOOL_NO && x.USER_NO == hRMT.USER_NO).Count();
                        if (ADDT38Count > 0)
                        {
                            logger.Info("重複領取,如有問題請洽 " + aDDT37TEMP.IS_Remark);
                            result.Success = false;
                            result.Message = "重複領取,如有問題請洽 " + UserName + "老師";
                        }
                        else {
                            logger.Info("沒有重複領取,如有問題請洽 " + aDDT37TEMP.IS_Remark);
                            foreach (var i in aDDT37s)
                            {
                                i.CRE_PERSON_NAME = UserName;
                            }
                            result.Success = true;
                            result.ModelItem = aDDT37s;
                        }
                    }
                    else if (aDDT37Item.ROLL_CALL_DATES > DateTime.Now)
                    {

                        result.Success = false;
                        result.Message = "兌換期限還未開始,如有問題請洽 " + UserName + "老師";
                    }
                    else if (aDDT37Item.ROLL_CALL_DATEE < DateTime.Now)

                    {
                        result.Success = false;
                        result.Message = "兌換期限已過,如有問題請洽 " + UserName + "老師";
                    }
                    else
                    {
                        foreach (var i in aDDT37s)
                        {
                            i.CRE_PERSON_NAME = UserName;
                        }
                        result.Success = true;
                        result.ModelItem = aDDT37s;
                    }

                }
                else {
                    logger.Info("代碼錯誤 SCHOOL_NO" + SCHOOL_NO);
                    logger.Info("代碼錯誤 Barcode"+ Barcode);
                    result.Success = false;
                    result.Message = "代碼錯誤";
                }
            }
           
            return result;
        }
        private string ExtractNumericValue(string input)
        {
            // 使用正則表達式提取字符串中的數字
            string numericValue = "0";
            System.Text.RegularExpressions.Regex rex =
       new System.Text.RegularExpressions.Regex(@"^\d+$");
            
            if (rex.IsMatch(input))
            {
                numericValue = input;

            }
            else {

                numericValue = "-1";
            }
            return numericValue;
        }

        /// <summary>
        /// 產生批次流水號
        /// </summary>
        /// <param name="SYS_TABLE_TYPE"></param>
        /// <returns></returns>
        public string GetNewBatchCashId(string SYS_TABLE_TYPE,string SCHOOL_NO)
        {
            string MaxString = string.Empty;
            string ReturnVal = string.Empty;
            ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();
            if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH) //其他
            {
                MaxString = (from u in EntitiesDb.ADDT20
                             where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID.Length>11 && u.BATCH_CASH_ID != null

                             select u.BATCH_CASH_ID).AsEnumerable()
                             .OrderByDescending(u => ExtractNumericValue(u))
                                   .FirstOrDefault();
            }
           
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK) //校內
            {
                MaxString = (from u in EntitiesDb.ADDT14
                             where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID != null 
                             select u.BATCH_CASH_ID).AsEnumerable()
                             .OrderByDescending(u => ExtractNumericValue(u))
                                   .FirstOrDefault();
            }
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC) //校外
            {
                MaxString = (from u in EntitiesDb.ADDT15
                             where u.SCHOOL_NO == SCHOOL_NO && u.BATCH_CASH_ID != null 
                             select u.BATCH_CASH_ID).AsEnumerable()
                             .OrderByDescending(u => ExtractNumericValue(u))
                                   .FirstOrDefault();
            }
            else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG
                || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_INADD) //老師
            {
                return SCHOOL_NO + DateTime.Now.ToString("yyyyMMddhhmmss");
            }

            if (!string.IsNullOrEmpty(MaxString))
            {
                ReturnVal = SCHOOL_NO + DateTime.Now.ToString("yyyyMMdd") + new StringHelper().StrRigth("00" + (Convert.ToInt16(new StringHelper().StrRigth(MaxString, 3)) + 1).ToString(), 3);
            }
            else
            {
                ReturnVal = SCHOOL_NO + DateTime.Now.ToString("yyyyMMdd") + "001";
            }

            return ReturnVal;
        }
        public IResult InsertADDT38(BarcodeEditPeopleViewModel model, ref ECOOL_DEVEntities db, List<HRMT01> H01List,string SessionID, string BATCH_CASH_ID, string SCHOOL_NO = "")
        {
            IResult result = new Result(false);
            ADDT38 SaveUp = null;
            ADDT37 SaveUp1 = null;
            int SYear;
            int Semesters;
            bool OK = false;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

          
        
            using (TransactionScope tx = new TransactionScope())
            {
                SaveUp = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID && a.BarCode == model.BarCode ).FirstOrDefault();
                if (SaveUp != null)
                {
                    SaveUp1=db.ADDT37.Where(x => x.ROLL_CALL_ID == model.ROLL_CALL_ID).FirstOrDefault();
                    if (SaveUp1.ROLL_CALL_DATES > DateTime.Now)
                    {
                        result.Message = "此紙本點數已還未開始兌換喔!";
                        return result;
                    }
                    if (SaveUp1.ROLL_CALL_DATEE < DateTime.Now)
                    {
                        result.Message = "此紙本點數已兌換結束囉!";
                        return result;
                    }
                    if (SaveUp.IS_PRESENT==true)
                    {
                        result.Message = "此紙本點數已兌換過囉";
                        return result;
                    }
                     else   if (SaveUp.IS_PRESENT == false )
                    {
                        SaveUp.USER_NO = model.USER_NO;
                        SaveUp.GRADE = model.GRADE;
                        SaveUp.CLASS_NO = model.CLASS_NO;
                        SaveUp.SYEAR = (byte)SYear;
                        SaveUp.SEMESTER = (byte)Semesters;
                        SaveUp.NAME = model.NAME;
                        SaveUp.SEAT_NO = model.SEAT_NO;
                        SaveUp.IS_PRESENT = true;
                        SaveUp.BATCH_CASH_ID = BATCH_CASH_ID;
                        SaveUp.CHG_DATE = DateTime.Now;
                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                       
                        //ECOOL_APP.CashHelper.AddCash(user, (int)model.CASH, SCHOOL_NO, model.USER_NO, "ADDT38"
                        //                   , model.ROLL_CALL_ID + SCHOOL_NO + model.USER_NO
                        //                   , $"{model.ROLL_CALL_NAME}，獲得 {model.CASH} 點", true, ref db);
                    }
                 
                }
                else {
                    result.Message = "此代碼不存在";
                    return result;
                }
                try
                {
                    db.SaveChanges();
                    result.ModelItem = model;
                    result.Success = true;
                }
                catch (Exception ex)
                {
                    result.Exception = ex;
                    result.Message += ex.Message;
                    return result;
                }

                tx.Complete();

            }
            return result;
        }

        public bool InsertADDT20(string BATCH_CASH_ID, byte SYear, byte Semesters, string LogDesc, List<HRMT01> H01List, UserProfile user, BarcodeEditPeopleViewModel Data, ADDT38 aDDT38, string SessionID, ref ECOOL_DEVEntities db, string Barcode)
        {
            bool ReturnVal = false;
            string BATCH_ID = PushService.CreBATCH_ID();
            List<APPT02> T02List = new List<APPT02>();
         
            int NUM = 0;
            List<ADDT20> ADDT20AddData = new List<ADDT20>();
            string ErrorMsg = "";
            string User_key = "";
            string ROLL_CALL_DESC = "";
            HRMT01 H02 = new HRMT01();
            string LogDescADDI01 = "";
            foreach (var item in H01List)
            {
                User_key = db.ADDT37.Where(x => x.ROLL_CALL_ID == aDDT38.ROLL_CALL_ID).Select(x => x.CRE_PERSON).FirstOrDefault();
                ROLL_CALL_DESC = db.ADDT37.Where(x => x.ROLL_CALL_ID == aDDT38.ROLL_CALL_ID).Select(x => x.ROLL_CALL_DESC).FirstOrDefault();
                H02 = db.HRMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_KEY == User_key).FirstOrDefault();

                HRMT01 H01 = H01List.Where(A => A.SCHOOL_NO == item.SCHOOL_NO && A.USER_NO == item.USER_NO).FirstOrDefault();
                int ADDT20temp = 0;
                ADDT20temp = db.ADDT20.Where(x => x.BATCH_CASH_ID == BATCH_CASH_ID && x.USER_NO == H01.USER_NO && x.SCHOOL_NO == H01.SCHOOL_NO).Count();
                if (ADDT20temp > 0)
                {
                    Regex rex = new Regex(BATCH_CASH_ID);
                    List<string> str = new List<string>();
                    str = db.ADDT20.Where(x => x.USER_NO == H01.USER_NO && x.SCHOOL_NO == H01.SCHOOL_NO).Select(x => x.BATCH_CASH_ID).Distinct().ToList();
                    List<int> temp20 = new List<int>();
                    int MATINT = 0;
                    bool MATINTBoolen = false;
                    foreach (var item2 in str)
                    {
                        if (rex.IsMatch(item2) && item2 != BATCH_CASH_ID)
                        {
                            string strint =
                            item2.Substring(BATCH_CASH_ID.Length, item2.Length - BATCH_CASH_ID.Length);
                            int temp20Int = Int32.Parse(strint);
                            temp20.Add(temp20Int);
                            MATINTBoolen = true;
                        }
                    }
                    if (MATINTBoolen)
                    {
                        MATINT = temp20.Max();
                        BATCH_CASH_ID = BATCH_CASH_ID + (MATINT + 1).ToString();
                    }
                    else
                    {

                        BATCH_CASH_ID = BATCH_CASH_ID + (ADDT20temp + 1).ToString();
                    }
                }
                    if (H01 != null)
                {
                    NUM++;


                    ADDT20 T20 = new ADDT20();
                    T20.BATCH_CASH_ID = BATCH_CASH_ID;
                    T20.SCHOOL_NO = H01.SCHOOL_NO;
                    T20.USER_NO = H01.USER_NO;
                    T20.CLASS_NO = H01.CLASS_NO;
                    T20.SYEAR = (byte)SYear;
                    T20.SEMESTER = (byte)Semesters;
                    T20.SEAT_NO = H01.SEAT_NO;
                    T20.NAME = H01.NAME;
                    T20.SNAME = H01.SNAME;
                    T20.CRE_PERSON = "系統紙本酷幣點數";
                    T20.CRE_DATE = DateTime.Today;
                    T20.CHG_PERSON = "系統紙本酷幣點數";
                    T20.CHG_DATE = DateTime.Now;
                    T20.SUBJECT = aDDT38.SUBJECT;
                    T20.CONTENT_TXT = ROLL_CALL_DESC;
                    T20.MEMO = "紙本酷幣點數";
                    T20.CASH = Data.CASH == null ? 0 : Data.CASH;
                    T20.STATUS = "R";
                    T20.NUM = NUM;
         
                    T20.APPLY_STATUS = "2";
                    ADDT20AddData.Add(T20);
                    logger.Info("紙本酷幣點數 ADDT20" + H01.NAME);
                    if (T20.CASH != 0)
                    {

                        GreADDT20PushData(BATCH_ID, T20, ref db, T02List);


                    }
                    LogDesc = StringHelper.LeftStringR("特殊加扣點-" + aDDT38.SUBJECT + "-" + ROLL_CALL_DESC, 47);
                    logger.Info("紙本酷幣點數 ADDT20" + LogDesc);
                }

             }

            string TableName = "ADDT20";
            try
            {

                ReturnVal = ADDT20AddData.BatchDatAddCasha("ADDI13", BATCH_CASH_ID, TableName, LogDesc, SessionID, TableName, user, out ErrorMsg, T02List, H01List.FirstOrDefault(), Barcode);
            }
            catch (Exception e)
            {

                logger.Info("紙本酷幣點數 BatchDatAddCasha" + e.InnerException);

            }

            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;

        }
            /// <summary>
            /// 處理ADDT14 (校內)
            /// </summary>
            /// <param name="BATCH_CASH_ID"></param>
            /// <param name="SYear"></param>
            /// <param name="Semesters"></param>
            /// <param name="H01List"></param>
            /// <param name="Data"></param>
            public bool InsertADDT14(string BATCH_CASH_ID, byte SYear, byte Semesters, string LogDesc, List<HRMT01> H01List, UserProfile user, BarcodeEditPeopleViewModel Data, ADDT38 aDDT38, string SessionID,ref ECOOL_DEVEntities db,string Barcode)
        {
            bool ReturnVal = false;
            string BATCH_ID = PushService.CreBATCH_ID();
            List<APPT02> T02List = new List<APPT02>();
            List<ADDT14> ADDT14AddData = new List<ADDT14>();
            int NUM = 0;
            
            string ErrorMsg = "";
            string User_key = "";
            string ROLL_CALL_DESC = "";
            HRMT01 H02 = new HRMT01();
            string LogDescADDI01 = "";
            foreach (var item in H01List)
            {
                User_key = db.ADDT37.Where(x => x.ROLL_CALL_ID == aDDT38.ROLL_CALL_ID).Select(x => x.CRE_PERSON).FirstOrDefault();
                ROLL_CALL_DESC= db.ADDT37.Where(x => x.ROLL_CALL_ID == aDDT38.ROLL_CALL_ID).Select(x => x.ROLL_CALL_DESC).FirstOrDefault();
                H02 = db.HRMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_KEY == User_key).FirstOrDefault();
                
                HRMT01 H01 = H01List.Where(A => A.SCHOOL_NO == item.SCHOOL_NO && A.USER_NO == item.USER_NO).FirstOrDefault();
                if (H01 != null)
                {
                    NUM++;

                    ADDT14 T14 = new ADDT14();
                    T14.BATCH_CASH_ID = BATCH_CASH_ID;
                    T14.SCHOOL_NO = H01.SCHOOL_NO;
                    T14.CREATEDATE = DateTime.Now;
                    T14.SYEAR = (byte)SYear;
                    T14.SEMESTER = (byte)Semesters;
                    T14.TNAME = H02.NAME;
                    T14.CLASS_NO = H01.CLASS_NO;
                    T14.USER_NO = H01.USER_NO;
                    T14.USERNAME = H01.NAME;
                    T14.SNAME = H01.SNAME;
                    T14.IAWARD_KIND = aDDT38.SUBJECT;
                    T14.IAWARD_ITEM = ROLL_CALL_DESC;
                    T14.IAWARD_From = "紙本酷幣點數";
                    T14.CASH = Data.CASH ==null? 0 : Data.CASH;
                    T14.REMARK = "";
                    T14.APPLY_STATUS = "2";
                    T14.IMG_FILE = "";
                    logger.Info("紙本酷幣點數 ADDT14" + H01.NAME);
                    if (T14.CASH != 0)
                    {
                        ADDT14AddData.Add(T14);

                        GreADDT14PushData(BATCH_ID, T14, ref db, T02List);
                    }
              
                    LogDesc = StringHelper.LeftStringR("校內表現-" + aDDT38.SUBJECT +"-"+ ROLL_CALL_DESC, 47);

                    logger.Info("紙本酷幣點數 ADDT14" + LogDesc);
                }
            }
     
            
        
            string TableName = "ADDT14";
            try
            {

                ReturnVal = ADDT14AddData.BatchDatAddCasha("ADDI13", BATCH_CASH_ID, TableName, LogDesc, SessionID, TableName, user, out ErrorMsg, T02List, H01List.FirstOrDefault(), Barcode);
            }
            catch (Exception e) {

                logger.Info("紙本酷幣點數 BatchDatAddCasha" + e.InnerException);

            }
             
            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;
        }
        public void GreADDT14PushData(string BATCH_ID, ADDT14 aDDT14, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            string BODY_TXT = "校內表現，獎懲類別:" + aDDT14.IAWARD_KIND + "，具體事蹟:" + aDDT14.IAWARD_ITEM + "，獎勵點數:" + aDDT14.CASH.ToString();

            PushService.InsertPushDataParents(BATCH_ID, aDDT14.SCHOOL_NO, aDDT14.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT14", aDDT14.IAWARD_ID.ToString(), "", false, ref db, T02List);
            PushService.InsertPushDataMe(BATCH_ID, aDDT14.SCHOOL_NO, aDDT14.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT14", aDDT14.IAWARD_ID.ToString(), "", false, ref db, T02List);
        }
        public void GreADDT20PushData(string BATCH_ID, ADDT20 aDDT20, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            string BODY_TXT = "特殊加扣點，獎懲主旨:" + aDDT20.SUBJECT + "，獎懲內容:" + aDDT20.CONTENT_TXT + "，獎勵點數:" + aDDT20.CASH.ToString();

            PushService.InsertPushDataParents(BATCH_ID, aDDT20.SCHOOL_NO, aDDT20.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT20", aDDT20.BATCH_CASH_ID, "", false, ref db, T02List);
            PushService.InsertPushDataMe(BATCH_ID, aDDT20.SCHOOL_NO, aDDT20.USER_NO, "", BODY_TXT, "", "ADDI09", "ADDT20", aDDT20.BATCH_CASH_ID, "", false, ref db, T02List);
        }

        public string ToExcelSample(List<SAQT02> aQT02) {

            return "";


         }
        public string GetTOExcelFileSample(ADDI13IndexViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Details?.Count == 0 || model.Details == null)
            {
                Message = "查無任何資料";
                return string.Empty;
            }
            try
            {
                var ExcelTable = model.Details.AsDataTable();

                NPOIHelper npoi = new NPOIHelper();
                string TempleteFileFullName = HttpContext.Current.Server.MapPath(@"~/Content/ExcelSample/ExportRunUserList.xlsx");
                FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);

                IWorkbook aBook = WorkbookFactory.Create(xlsfile);

                npoi.ExportExcelFromTemplete(ExcelTable, aBook, "CardList", false, 2);
                string SCHOOL_NO = model.Details.Select(a => a.SCHOOL_NO).FirstOrDefault();
                string SysTempPath = HttpContext.Current.Server.MapPath($@"~/Content/Temp/{SCHOOL_NO}");
                string strTMPFile = SysTempPath + @"\" + DateTime.Now.ToString("yyyyMMdd") + @"\";
                if (Directory.Exists(strTMPFile) == false)
                {
                    string DelDirectory = SysTempPath;

                    if (Directory.Exists(DelDirectory))
                    {
                        Directory.Delete(DelDirectory, true);
                    }

                    Directory.CreateDirectory(strTMPFile);
                }

                strTMPFile = strTMPFile + @"\ExportRunUserList" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";

                System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
                aBook.Write(FS);
                FS.Close();

                return strTMPFile;
            }
            catch (Exception ex)
            {
                Message = ex.Message;
                return string.Empty;
            }
        }

        public string GetToExceFile(ADDI13IndexViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Details?.Count == 0 || model.Details == null)
            {
                Message = "查無任何資料";
                return string.Empty;
            }

            try
            {



                var ExcelTable = model.Details.AsDataTable();

                NPOIHelper npoi = new NPOIHelper();
                string TempleteFileFullName = HttpContext.Current.Server.MapPath(@"~/Content/ExcelSample/CardExport.xlsx");
                FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);

                IWorkbook aBook = WorkbookFactory.Create(xlsfile);
                npoi.ExportExcelFromTemplete(ExcelTable, aBook, "CardList", false, 2);

                string SCHOOL_NO = model.Details.Select(a => a.SCHOOL_NO).FirstOrDefault();
                string SysTempPath = HttpContext.Current.Server.MapPath($@"~/Content/Temp/{SCHOOL_NO}");
                string strTMPFile = SysTempPath + @"\" + DateTime.Now.ToString("yyyyMMdd") + @"\";

                if (Directory.Exists(strTMPFile) == false)
                {
                    string DelDirectory = SysTempPath;

                    if (Directory.Exists(DelDirectory))
                    {
                        Directory.Delete(DelDirectory, true);
                    }

                    Directory.CreateDirectory(strTMPFile);
                }

                strTMPFile = strTMPFile + @"\CardExport" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";

                System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
                aBook.Write(FS);
                FS.Close();

                return strTMPFile;
            }
            catch (Exception ex)
            {
                Message = ex.Message;
                logger.Info("ROLL_CALL_ID EXC" + ex.InnerException);
                return string.Empty;
            }
        }

        /// <summary>
        /// 活動列表
        /// </summary>
        /// <param name="model"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public RollCallIndexViewModel GetListData(RollCallIndexViewModel model, string SCHOOL_NO, byte? ROLL_CALL_TYPE, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...

            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(RollCallListViewModel.ROLL_CALL_DATES);
                model.SortType = PageGlobal.SortType.DESC;
            }

            string sSQL = $@" Select a.ROLL_CALL_ID,a.ROLL_CALL_NAME,a.ROLL_CALL_DATES,a.ROLL_CALL_DATEE
                            ,isnull((select count(*) from AWAT13 b where a.ROLL_CALL_ID=b.ROLL_CALL_ID and b.IS_PRESENT=1 ),0) as ROLL_CALL_COUNT
                            ,a.CASH,b.NAME as CRE_PERSON_NAME,a.STATUS
                            from AWAT12 a (nolock)
                            join HRMT01 b  (nolock) on a.CRE_PERSON=b.USER_KEY
                            where a.DEL_YN='{SharedGlobal.N}'
                            and a.ROLL_CALL_TYPE =@ROLL_CALL_TYPE
                            and a.SCHOOL_NO =@SCHOOL_NO ";

            var temp = db.Database.Connection.Query<RollCallListViewModel>(sSQL
            , new
            {
                SCHOOL_NO = SCHOOL_NO,
                ROLL_CALL_TYPE = ROLL_CALL_TYPE,
            }).AsQueryable();

            temp = temp.OrderBy(model.OrderByColumnName, model.SortType);

            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.ListData.PageNumber;

            return model;
        }
        /// <summary>
        /// 活動列表
        /// </summary>
        /// <param name="model"></param>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public RollCallBarcodeIndexViewModel GetBarcodeListData(RollCallBarcodeIndexViewModel model, string SCHOOL_NO, byte? ROLL_CALL_TYPE, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...

            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(RollCallBarcodeListViewModel.ROLL_CALL_DATES);
                model.SortType = PageGlobal.SortType.DESC;
            }

            string sSQL = $@" Select a.ROLL_CALL_ID,a.ROLL_CALL_NAME,a.ROLL_CALL_DATES,a.ROLL_CALL_DATEE,a.ROLL_CALL_NUM,a.CRE_PERSON
                            ,isnull((select count(*) from ADDT38 b where a.ROLL_CALL_ID=b.ROLL_CALL_ID and b.IS_PRESENT=1 ),0) as ROLL_CALL_COUNT
                            ,a.CASH,b.NAME as CRE_PERSON_NAME,a.STATUS
                      
							,( select SUM(CASH) from ADDT38 where ROLL_CALL_ID=a.ROLL_CALL_ID and USER_NO is not null) as SumCash,( select count(CASH) from ADDT38 where ROLL_CALL_ID=a.ROLL_CALL_ID and USER_NO is not null) as personCount
                          	,( select SUM(CASH) from ADDT38 where ROLL_CALL_ID=a.ROLL_CALL_ID )as NOSumCash ,( select count(CASH) from ADDT38 where ROLL_CALL_ID=a.ROLL_CALL_ID ) as NOpersonCount
                           from ADDT37 a (nolock)
                            join HRMT01 b  (nolock) on a.CRE_PERSON=b.USER_KEY
                            where a.DEL_YN='{SharedGlobal.N}'
                            and a.ROLL_CALL_TYPE =@ROLL_CALL_TYPE
                            and a.SCHOOL_NO =@SCHOOL_NO ";
            

            var temp = db.Database.Connection.Query<RollCallBarcodeListViewModel>(sSQL
            , new
            {
                SCHOOL_NO = SCHOOL_NO,
                ROLL_CALL_TYPE = ROLL_CALL_TYPE,
            }).AsQueryable();
            if (model.search != null && !string.IsNullOrWhiteSpace(model.search.USER_key))
            {
                temp=temp.Where(x=>x.CRE_PERSON== model.search.USER_key).AsQueryable();


            }
            temp = temp.OrderBy(model.OrderByColumnName, model.SortType);

            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.ListData.PageNumber;

            return model;
        }

        /// <summary>
        /// 活動編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public RollCallEditViewModel GetEditData(RollCallEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.Main = (from a in db.AWAT12
                              where a.ROLL_CALL_ID == model.Keyword
                              select new RollCallEditMainViewModel()
                              {
                                  ROLL_CALL_ID = a.ROLL_CALL_ID,
                                  ROLL_CALL_NAME = a.ROLL_CALL_NAME,
                                  ROLL_CALL_DESC = a.ROLL_CALL_DESC,
                                  ROLL_CALL_DATES = a.ROLL_CALL_DATES,
                                  ROLL_CALL_DATEE = a.ROLL_CALL_DATEE,
                                  ROLL_CALL_TYPE = a.ROLL_CALL_TYPE,
                                  CASH = (short)a.CASH,
                                  STATUS = a.STATUS,
                              }).FirstOrDefault();

                return model;
            }
        }
        /// <summary>
        /// 活動編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public RollCallBarcodeEditViewModel GetEdit1Data(RollCallBarcodeEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.Main = (from a in db.ADDT37
                              where a.ROLL_CALL_ID == model.Keyword
                              select new RollCallBarcodeEditMainViewModel()
                              {
                                  ROLL_CALL_ID = a.ROLL_CALL_ID,
                                  ROLL_CASH_NUM = a.ROLL_CALL_NUM,
                                  ROLL_CASH_SUM = a.CASH,
                                  ROLL_CALL_NAME = a.ROLL_CALL_NAME,
                                  ROLL_CALL_DESC = a.ROLL_CALL_DESC,
                                  ROLL_CALL_DATES = a.ROLL_CALL_DATES,
                                  ROLL_CALL_DATEE = a.ROLL_CALL_DATEE,
                                  ROLL_CALL_TYPE = a.ROLL_CALL_TYPE,
                                  IS_SHOW =a.IS_SHOW??false,
                                  IS_Remark = a.IS_Remark ?? true,
                                  CASH = a.CASH,
                                  CRE_DATE = a.CRE_DATE,
                                  STATUS = a.STATUS,
                              }).FirstOrDefault();
                model.Details= (from a in db.ADDT38
                                where a.ROLL_CALL_ID == model.Keyword
                                select new RollCallBarcodeCashDetailViewModel()
                                {
                                    ROLL_CALL_ID = a.ROLL_CALL_ID,
                                    
                                    CASH = (short)(a.CASH??0),
                                    NUM = (short)(a.NUM??0),
                                }).Distinct().ToList();
             model.SUBJECT= db.ADDT38.Where(x => x.ROLL_CALL_ID == model.Keyword).Select(x => x.SUBJECT).FirstOrDefault();

                return model;
            }
        }
        /// <summary>
        /// 活動編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public RollCallBarcodeEditViewModel GetEdit1DataDetail(RollCallBarcodeEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.Main = (from a in db.ADDT37
                              where a.ROLL_CALL_ID == model.Keyword
                              select new RollCallBarcodeEditMainViewModel()
                              {
                                  ROLL_CALL_ID = a.ROLL_CALL_ID,
                                  ROLL_CASH_NUM = a.ROLL_CALL_NUM,
                                  ROLL_CASH_SUM = a.CASH,
                                  ROLL_CALL_NAME = a.ROLL_CALL_NAME,
                                  ROLL_CALL_DESC = a.ROLL_CALL_DESC,
                                  ROLL_CALL_DATES = a.ROLL_CALL_DATES,
                                  ROLL_CALL_DATEE = a.ROLL_CALL_DATEE,
                                  ROLL_CALL_TYPE = a.ROLL_CALL_TYPE,
                                  CASH = (short)a.CASH,
                                  CRE_DATE = a.CRE_DATE,
                                  STATUS = a.STATUS,
                              }).FirstOrDefault();
                model.Details = (from y in db.ADDT38
                                 join a in db.HRMT01 on new { y.SCHOOL_NO, USER_NO= y.USER_NO.Contains("A")==true && y.USER_NO.Length<10?y.USER_NO.Substring(1) :y.USER_NO} equals new { a.SCHOOL_NO, a.USER_NO } into aJoin
                                 from a in aJoin.DefaultIfEmpty()
                                 where  (y.ROLL_CALL_ID == model.Keyword && y.IS_PRESENT == true && y.IsDel!="y") 
                                 select new RollCallBarcodeCashDetailViewModel()
                                 {
                                     ROLL_CALL_ID = y.ROLL_CALL_ID ,
                                     BarCode =  y.BarCode ,
                                     SCHOOL_NO =  y.SCHOOL_NO ,
                                     CHG_DATE = y.CHG_DATE,
                                     USER_NO = a != null ? a.USER_NO : null,
                                     GRADE = a!=null? a.GRADE:y.GRADE,
                                     CLASS_NO = a != null ? a.CLASS_NO : y.CLASS_NO,
                                     NAME = a != null ? a.NAME : y.NAME,
                                     SNAME = a != null ? a.SNAME : y.SNAME,
                                     SEAT_NO = a != null ? a.SEAT_NO : y.SEAT_NO,
                                     ROLL_NUM = y.ROLL_NUM,
                                     CASH = (short)(y.CASH ?? 0),
                                     NUM = (short)(y.NUM ?? 0),
                                 }).Distinct().ToList();

                if (string.IsNullOrWhiteSpace(model.OrdercColumn)) {
                    model.Details = model.Details.OrderByDescending(x => x.CHG_DATE).ToList();


                }
                
                if (model.OrdercColumn == "SEAT_NO")
                {
                    List<RollCallBarcodeCashDetailViewModel> DetailsTemp = new List<RollCallBarcodeCashDetailViewModel>();
                    if (model.SyntaxName == "ASC")
                    {
                      //  DetailsTemp = model.Details.Where(x => x.SEAT_NO == null).ToList();
                        model.Details = model.Details.OrderBy(x => x.SEAT_NO != null && x.SEAT_NO.All(y => char.IsDigit(y)) ? int.Parse(x.SEAT_NO) : int.MaxValue).ThenBy(x=>x.SEAT_NO == null)
                     .ToList();
                    }
                    else
                    {
                      
                        model.Details = model.Details.OrderByDescending(x => x.SEAT_NO != null && x.SEAT_NO.All(y => char.IsDigit(y)) ? int.Parse(x.SEAT_NO) : int.MaxValue).ThenBy(x => x.SEAT_NO == null)
                         .ToList();

                         
                    }
                }
                else if (model.OrdercColumn == "CASH")
                {
                    if (model.SyntaxName == "ASC")
                    {
                        model.Details = model.Details.OrderBy(X => X.CASH).ToList();
                    }
                    else
                    {
                        model.Details = model.Details.OrderByDescending(X => X.CASH).ToList();
                    }
                }
                else if (model.OrdercColumn == "ROLL_NUM")
                {
                    if (model.SyntaxName == "ASC")
                    {
                        model.Details = model.Details.OrderBy(X => X.ROLL_NUM).ToList();
                    }
                    else
                    {
                        model.Details = model.Details.OrderByDescending(X => X.ROLL_NUM).ToList();
                    }
                }
                
                else if (model.OrdercColumn == "CLASS_NO")
                {
                    if (model.SyntaxName == "ASC")
                    {
                        model.Details = model.Details.OrderBy(X => X.CLASS_NO).ToList();
                    }
                    else
                    {
                        model.Details = model.Details.OrderByDescending(X => X.CLASS_NO).ToList();
                    }
                }

                else if (model.OrdercColumn == "CHARGETIME")
                {
                    if (model.SyntaxName == "ASC")
                    {
                        model.Details = model.Details.OrderBy(X => X.CHG_DATE).ToList();
                    }
                    else
                    {
                        model.Details = model.Details.OrderByDescending(X => X.CHG_DATE).ToList();
                    }
                }

                return model;
            }
        }
        /// <summary>
        /// 存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult SaveEditBarcodeData(RollCallBarcodeEditViewModel model, byte? ROLL_CALL_TYPE, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    //主檔
                    ADDT37 SaveUp = null;

                    SaveUp = db.ADDT37.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID).FirstOrDefault();
                  
                    if (SaveUp == null)
                    {

                        int Cost_cash = 0;
                        foreach (var item in model.Details)
                        {

                            int j = 0;
                            for (int i = 0; i < item.NUM; i++)
                            {
                                Cost_cash += item.CASH * item.NUM;

                            }

                        }
                        string ErrMsg;
                        short ThisMonthCash;
                        int CashTotal = 0; if (model.Main.ROLL_CALL_DESC.Contains("批次特殊加扣點") || model.Main.ROLL_CALL_DESC.Contains("即時加點特殊加扣點") || model.Main.ROLL_CALL_DESC.Contains("特殊加扣點")
            || model.Main.ROLL_CALL_DESC.Contains("批次校內表現班級小幫手") || model.Main.ROLL_CALL_DESC.Contains("批次校內表現班級幫手和榮譽")
            || model.Main.ROLL_CALL_DESC.Contains("校內表現-班級幫手和榮譽") || model.Main.ROLL_CALL_DESC.Contains("班級幫手和榮譽") || model.Main.ROLL_CALL_DESC.Contains("校內表現-班級服務")
               || model.Main.ROLL_CALL_DESC.Contains("批次快速大量加點-特殊加扣點") || model.Main.ROLL_CALL_DESC.Contains("特殊加扣點-小獎勵(班級加點，受點數控管)") || model.Main.ROLL_CALL_DESC.Contains("小獎勵(班級加點，受點數控管"))
                        {
                            int CashLimit = UserProfile.GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
                            if (model.SUBJECT == "小獎勵(班級加點，受點數控管)" && model.Main.ROLL_CASH_SUM != null)
                            {
                                CashTotal = (int)model.Main.ROLL_CASH_SUM;
                            }
                            if (CashLimit > 0 && CashLimit != short.MaxValue)
                            {
                                if ((CashTotal + ThisMonthCash) > CashLimit)
                                {
                                    result.Message += "本次給點將超過本月給點限制：" + CashLimit.ToString() + "。您本月已發出點數：" + ThisMonthCash.ToString();
                                    return result;
                                }
                            }
                        }
                        SaveUp = new ADDT37();
                        SaveUp.ROLL_CALL_ID = Guid.NewGuid().ToString("N");
                        SaveUp.ROLL_CALL_NAME = model.Main.ROLL_CALL_NAME;
                        SaveUp.ROLL_CALL_DESC = model.Main.ROLL_CALL_DESC;
                        SaveUp.ROLL_CALL_DATES = ((DateTime)model.Main.ROLL_CALL_DATES).AddHours(00).AddMinutes(00).AddSeconds(00);
                        SaveUp.ROLL_CALL_DATEE = ((DateTime)model.Main.ROLL_CALL_DATEE).AddHours(23).AddMinutes(59).AddSeconds(59);
                        SaveUp.ROLL_CALL_NUM = model.Main.ROLL_CASH_NUM;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.CHG_PERSON = user?.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.SCHOOL_NO = user?.SCHOOL_NO;
                        SaveUp.ROLL_CALL_TYPE = ROLL_CALL_TYPE;
                        SaveUp.CASH = model.Main.CASH;
                        SaveUp.DEL_YN = SharedGlobal.N;
                        SaveUp.STATUS = (byte)ADDT37.StatusVal.給點前;
                        SaveUp.IS_SHOW = model.Main.IS_SHOW;
                        SaveUp.IS_Remark = model.Main.IS_Remark;
                       db.ADDT37.Add(SaveUp);
                        model.Main.ROLL_CALL_ID = SaveUp.ROLL_CALL_ID;
                    }
                    else
                    {
                        SaveUp.ROLL_CALL_NAME = model.Main.ROLL_CALL_NAME;
                        SaveUp.ROLL_CALL_DESC = model.Main.ROLL_CALL_DESC;
                        SaveUp.ROLL_CALL_DATES = ((DateTime)model.Main.ROLL_CALL_DATES).AddHours(00).AddMinutes(00).AddSeconds(00);
                        SaveUp.ROLL_CALL_DATEE = ((DateTime)model.Main.ROLL_CALL_DATEE).AddHours(23).AddMinutes(59).AddSeconds(59);
                        SaveUp.CASH = model.Main.CASH;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        SaveUp.ROLL_CALL_NUM = model.Main.ROLL_CASH_NUM;

                        SaveUp.IS_SHOW = model.Main.IS_SHOW;
                        SaveUp.IS_Remark = model.Main.IS_Remark;
                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }
                result.ModelItem = model;
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }
        /// <summary>
        /// 存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult SaveEditData(RollCallEditViewModel model, byte? ROLL_CALL_TYPE, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    //主檔
                    AWAT12 SaveUp = null;

                    SaveUp = db.AWAT12.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID).FirstOrDefault();

                    if (SaveUp == null)
                    {
                        SaveUp = new AWAT12();
                        SaveUp.ROLL_CALL_ID = Guid.NewGuid().ToString("N");
                        SaveUp.ROLL_CALL_NAME = model.Main.ROLL_CALL_NAME;
                        SaveUp.ROLL_CALL_DESC = model.Main.ROLL_CALL_DESC;
                        SaveUp.ROLL_CALL_DATES = model.Main.ROLL_CALL_DATES;
                        SaveUp.ROLL_CALL_DATEE = model.Main.ROLL_CALL_DATEE;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.CHG_PERSON = user?.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.SCHOOL_NO = user?.SCHOOL_NO;
                        SaveUp.ROLL_CALL_TYPE = ROLL_CALL_TYPE;
                        SaveUp.CASH = model.Main.CASH;
                        SaveUp.DEL_YN = SharedGlobal.N;
                        SaveUp.STATUS = (byte)AWAT12.StatusVal.點名前;

                        db.AWAT12.Add(SaveUp);
                    }
                    else
                    {
                        SaveUp.ROLL_CALL_NAME = model.Main.ROLL_CALL_NAME;
                        SaveUp.ROLL_CALL_DESC = model.Main.ROLL_CALL_DESC;
                        SaveUp.ROLL_CALL_DATES = model.Main.ROLL_CALL_DATES;
                        SaveUp.ROLL_CALL_DATEE = model.Main.ROLL_CALL_DATEE;
                        SaveUp.CASH = model.Main.CASH;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        /// <summary>
        /// 作廢
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult DelDate(RollCallEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db,ref List<Tuple<string, string, int>> valuesList)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    model.Keyword = (!string.IsNullOrWhiteSpace(model.Keyword)) ? model.Keyword : model.Main?.ROLL_CALL_ID;

                    AWAT12 SaveUp = db.AWAT12.Where(a => a.ROLL_CALL_ID == model.Keyword).FirstOrDefault();

                    if (SaveUp == null)
                    {
                        result.Message += "查無此資料";
                        return result;
                    }
                    List<AWAT13> aWAT13SaveUp = db.AWAT13.Where(x => x.SCHOOL_NO == SaveUp.SCHOOL_NO && x.ROLL_CALL_ID == model.Keyword).ToList();
                    SaveUp.DEL_YN = SharedGlobal.Y;
                    SaveUp.DEL_DATE = DateTime.Now;
                    SaveUp.DEL_PERSON = user?.USER_KEY;
                    if (aWAT13SaveUp != null && aWAT13SaveUp.Count() > 0)
                    {
                        foreach (var item in aWAT13SaveUp)
                        {
                            ECOOL_APP.CashHelper.AddCash(user, (int)-item.CASH, item.SCHOOL_NO, item.USER_NO, "AWAT13"
                                        , item.ROLL_CALL_ID + item.SCHOOL_NO + item.USER_NO
                                        , $"{SaveUp.ROLL_CALL_NAME}，獲得 {item.CASH} 點", true, ref db,"","",ref  valuesList);
                        }
                    }
                    db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                   
                }
                

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
            }

            return result;
        }
        /// <summary>
        /// 作廢
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult DelDate1(RollCallBarcodeEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    model.Keyword = (!string.IsNullOrWhiteSpace(model.Keyword)) ? model.Keyword : model.Main?.ROLL_CALL_ID;

                    ADDT37 SaveUp = db.ADDT37.Where(a => a.ROLL_CALL_ID == model.Keyword).FirstOrDefault();

                    if (SaveUp == null)
                    {
                        result.Message += "查無此資料";
                        return result;
                    }
                    List<ADDT38> aDDT38SaveUp = db.ADDT38.Where(x => x.SCHOOL_NO == SaveUp.SCHOOL_NO && x.ROLL_CALL_ID == model.Keyword).ToList();
                    SaveUp.DEL_YN = SharedGlobal.Y;
                    SaveUp.DEL_DATE = DateTime.Now;
                    SaveUp.DEL_PERSON = user?.USER_KEY;
                    //if (aDDT38SaveUp != null && aDDT38SaveUp.Count() > 0)
                    //{
                    //    foreach (var item in aDDT38SaveUp)
                    //    {
                    //        ECOOL_APP.CashHelper.AddCash(user, (int)-item.CASH, item.SCHOOL_NO, item.USER_NO, "ADDT38"
                    //                    , item.ROLL_CALL_ID + item.SCHOOL_NO + item.USER_NO
                    //                    , $"{SaveUp.ROLL_CALL_NAME}，獲得 {item.CASH} 點", true, ref db);
                    //    }
                    //}
                    db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
            }

            return result;
        }
        /// <summary>
        /// 點名存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult SaveRollCallData(ADDI13IndexViewModel model, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    if (model.Details.Count > 0)
                    {
                        var SaveUp = db.AWAT12.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID).FirstOrDefault();

                        if (SaveUp == null)
                        {
                            result.Message += "查無此資料";
                            return result;
                        }

                        SaveUp.STATUS = (byte)AWAT12.StatusVal.點名後;

                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

                        //預設值
                        int SYear;
                        int Semesters;
                        SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                        List<AWAT13> aWAT13s = new List<AWAT13>();

                        foreach (var item in model.Details)
                        {
                            AWAT13 Cre = new AWAT13();

                            Cre.ROLL_CALL_ID = model.ROLL_CALL_ID;
                            Cre.SCHOOL_NO = item.SCHOOL_NO;
                            Cre.USER_NO = item.USER_NO;
                            Cre.GRADE = item.GRADE;
                            Cre.CLASS_NO = item.CLASS_NO;
                            Cre.SYEAR = (byte)SYear;
                            Cre.SEMESTER = (byte)Semesters;
                            Cre.NAME = item.NAME;
                            Cre.SEAT_NO = item.SEAT_NO;
                            Cre.IS_PRESENT = true;
                            Cre.CHG_DATE = DateTime.Now;
                            aWAT13s.Add(Cre);
                            db.AWAT13.Add(Cre);
                        }

                        //if (aWAT13s?.Count() > 0)
                        //{
                        //    EFBatchOperation.For(db, db.AWAT13).InsertAll(aWAT13s);
                        //}
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        public IResult UpdateADDT37Info(RollCallBarcodeEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db) {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    var CASHSUM = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID).Sum(x => x.CASH).Value;

                    var ADDT38Count = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID).Count();
                    var SaveUp = db.ADDT37.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID).FirstOrDefault();
                    SaveUp.CASH = (short)CASHSUM;
                    SaveUp.ROLL_CALL_NUM = (short)ADDT38Count;
                    SaveUp.ROLL_CALL_NAME = model.Main.ROLL_CALL_NAME;
                    SaveUp.ROLL_CALL_DESC = model.Main.ROLL_CALL_DESC;
                    SaveUp.ROLL_CALL_DATES = ((DateTime)model.Main.ROLL_CALL_DATES).AddHours(00).AddMinutes(00).AddSeconds(00);
                    SaveUp.ROLL_CALL_DATEE = ((DateTime)model.Main.ROLL_CALL_DATEE).AddHours(23).AddMinutes(59).AddSeconds(59);
                    SaveUp.IS_SHOW = model.Main.IS_SHOW;
                          SaveUp.IS_Remark = model.Main.IS_Remark;
                    db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                    try
                    {
                        db.SaveChanges();

                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }
                    tx.Complete();
                    result.Success = true;
                }
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }
            return result;
            }
        /// <summary>
        /// 點名存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult SaveBarcodeRollCallData(RollCallBarcodeEditViewModel  model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                //using (TransactionScope tx = new TransactionScope())
                //{
                   

                  
                    //for (int i = 0; i < 10; i++)
                    //{
                    //    Console.WriteLine(listLinq[i].ToString());
                    //}
                    if (model.Main.ROLL_CASH_NUM != 0 )
                    {
                        var SaveUp = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID).Count();
                        if (SaveUp == 0)
                        {
                            List<ADDT38> aDDT38s = new List<ADDT38>();
                            if (model.Details != null && model.Details.Count() != 0) {
                            int k = 1;
                            foreach (var item in model.Details) {
                                int j = 0;
                                for (int i = 0; i < item.NUM; i++)
                                {
                                    ADDT38 Cre = new ADDT38();

                                    Cre.ROLL_CALL_ID = model.Main.ROLL_CALL_ID;
                                    Cre.SCHOOL_NO = user?.SCHOOL_NO;
                                    Cre.CASH = item.CASH;
                                    Cre.NUM = item.NUM;
                                    Cre.SUBJECT = model.SUBJECT;
                                    Cre.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK;
                                    Cre.ROLL_NUM = k;
                                    Cre.CRE_DATE= DateTime.Now;
                                    Cre.CRE_PERSON = user.USER_KEY;
                                    //Cre.USER_NO = "";
                                    //Cre.GRADE = "";
                                    //Cre.CLASS_NO = item.CLASS_NO;
                                    //Cre.SYEAR = (byte)SYear;
                                    //Cre.SEMESTER = (byte)Semesters;
                                    //Cre.NAME = item.NAME;
                                    //Cre.SEAT_NO = item.SEAT_NO;
                                    var r = new Random(Guid.NewGuid().GetHashCode());
                                    string str = "";
                                    str = string.Format("{0:000000000}", (r.Next(0, 999999999))) ;
                                    Cre.BarCode = str;
                                    Cre.IS_PRESENT = false;
                                    Cre.CHG_DATE = DateTime.Now;
                                    aDDT38s.Add(Cre);
                                    db.ADDT38.Add(Cre);
                                    logger.Info("BarCode" + Cre.BarCode);
                                    j++;
                                    k++;
                                }
                                }

                            }
                           
                        //    if (aDDT38s?.Count() > 0)
                        //{
                        //    EFBatchOperation.For(db, db.ADDT38).InsertAll(aDDT38s);
                        //}
                        }
                        else {
                            //int COUNTSaveup = 0;
                            //COUNTSaveup = (int)model.Main.ROLL_CASH_NUM - SaveUp;
                            if (model.Details != null && model.Details.Count() != 0)
                            {
                                List<ADDT38> aDDT38s = new List<ADDT38>();
                            int j = 0;
                            //List<ADDT38> IS_PRSENTFalse = new List<ADDT38>();
                            //IS_PRSENTFalse = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID && a.IS_PRESENT == false).ToList();
                            //RemoveADDT38(IS_PRSENTFalse, ref db);
                            foreach (var item in model.Details)
                                {
                                    int IS_PRSENTCount = 0;
 
                                    
                                      for (int i = 0; i < item.NUM; i++)
                                      {

                                        ADDT38 Cre = new ADDT38();

                                        Cre.ROLL_CALL_ID = model.Main.ROLL_CALL_ID;
                                        Cre.SCHOOL_NO = user?.SCHOOL_NO;
                                        Cre.CASH = item.CASH;
                                        Cre.NUM = item.NUM;
                                    Cre.SUBJECT = model.SUBJECT;
                                    Cre.SYS_TABLE_TYPE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK;
                                    //Cre.USER_NO = "";
                                    //Cre.GRADE = "";
                                    //Cre.CLASS_NO = item.CLASS_NO;
                                    //Cre.SYEAR = (byte)SYear;
                                    //Cre.SEMESTER = (byte)Semesters;
                                    //Cre.NAME = item.NAME;
                                    //Cre.SEAT_NO = item.SEAT_NO;
                                    var r = new Random(Guid.NewGuid().GetHashCode());
                                    string str = "";
                                    str = string.Format("{0:00000000000}", (r.Next(0, 1999999999))) + j;
                                    Cre.BarCode = str;
                                        Cre.IS_PRESENT = false;
                                        Cre.CHG_DATE = DateTime.Now;
                                        aDDT38s.Add(Cre);
                                    db.ADDT38.Add(Cre);
                                    logger.Info("BarCode" + Cre.BarCode);
                                    j++;
                                }

                                }
                                //if (aDDT38s?.Count() > 0)
                                //{
                                //    EFBatchOperation.For(db, db.ADDT38).InsertAll(aDDT38s);
                                //}

                            }
                               

                        }
                    }
                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        logger.Info("ex"+ ex.Message);
                        logger.Info("ex" + ex.InnerException??"");
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                //    tx.Complete();
                //}
            }
            catch (Exception ex)
            {
                logger.Info("ex" + ex.Message);
                logger.Info("ex" + ex.InnerException??"");
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }
            return result;
        }
        public bool RemoveADDT38(List<ADDT38> IS_PRSENTFalse, ref ECOOL_DEVEntities db)

        {
            bool ISTrue = false;
            try
            {
                if (IS_PRSENTFalse.Count() > 0)
                {
                    db.ADDT38.RemoveRange(IS_PRSENTFalse);
                }
                db.SaveChanges();
                ISTrue = true;
                return ISTrue;
            }
            catch (Exception ex)
            {
                return ISTrue;
            }
        }
        public List<int> GetRandom(int minValue, int maxValue)
        {
            List<int> Numbers = new List<int>();
            //使用Guid.NewGuid().GetHashCode()作為種子，可以確保Random在極短時間產生的隨機數盡可能做到不重復     
            Random rand = new Random(Guid.NewGuid().GetHashCode());
            int item;
            for (int i = minValue; i <= maxValue; i++)
            {
                item = rand.Next(minValue, maxValue + 1);
                while (Numbers.IndexOf(item) != -1)
                {
                    item = rand.Next(minValue, maxValue + 1);
                }
                Numbers.Add(item);
            }
            return Numbers;
        }

        public RollCallCashViewModel GetRollCallCashData(RollCallCashViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.Main = (from a in db.AWAT12
                              where a.ROLL_CALL_ID == model.Keyword
                              select new RollCallEditMainViewModel()
                              {
                                  ROLL_CALL_ID = a.ROLL_CALL_ID,
                                  ROLL_CALL_NAME = a.ROLL_CALL_NAME,
                                  ROLL_CALL_DESC = a.ROLL_CALL_DESC,
                                  ROLL_CALL_DATES = a.ROLL_CALL_DATES,
                                  ROLL_CALL_DATEE = a.ROLL_CALL_DATEE,
                                  ROLL_CALL_TYPE = a.ROLL_CALL_TYPE,
                                  CASH = (short)a.CASH,
                                  STATUS = a.STATUS,
                              }).FirstOrDefault();

                model.Details = (from a in db.AWAT13
                                 where a.ROLL_CALL_ID == model.Keyword
                                 && a.IS_PRESENT == true
                                 select new RollCallCashDetailViewModel()
                                 {
                                     ROLL_CALL_ID = a.ROLL_CALL_ID,
                                     SCHOOL_NO = a.SCHOOL_NO,
                                     USER_NO = a.USER_NO,
                                     GRADE = a.GRADE,
                                     CLASS_NO = a.CLASS_NO,
                                     CHG_DATE = a.CHG_DATE,
                                     SYEAR = a.SYEAR,
                                     SEMESTER = a.SEMESTER,
                                     NAME = a.NAME,
                                     SNAME = a.SNAME,
                                     SEAT_NO = a.SEAT_NO,
                                     IS_PRESENT = a.IS_PRESENT,
                                     CASH = (short)(a.CASH ?? model.Main.CASH),
                                 }).ToList();

                return model;
            }
        }
        public RollCallBarcodeCashViewModel GetBarCodeRollCallCashData(RollCallBarcodeCashViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.Main = (from a in db.ADDT37
                              where a.ROLL_CALL_ID == model.Keyword
                              select new RollCallBarcodeEditMainViewModel()
                              {
                                  ROLL_CALL_ID = a.ROLL_CALL_ID,
                                  ROLL_CASH_NUM = a.ROLL_CALL_NUM,
                                  ROLL_CALL_NAME = a.ROLL_CALL_NAME,
                                  ROLL_CALL_DESC = a.ROLL_CALL_DESC,
                                  ROLL_CALL_DATES = a.ROLL_CALL_DATES,
                                  ROLL_CALL_DATEE = a.ROLL_CALL_DATEE,
                                  ROLL_CALL_TYPE = a.ROLL_CALL_TYPE,
                                  IS_SHOW=a.IS_SHOW??false,
                                  IS_Remark = a.IS_Remark ?? true,
                                  CASH = (short)a.CASH,
                                  STATUS = a.STATUS,
                              }).FirstOrDefault();
                string CrePerson = "";
                HRMT01 hRMT = new HRMT01();
                CrePerson = db.ADDT37.Where(x => x.ROLL_CALL_ID == model.Keyword).Select(x => x.CRE_PERSON).FirstOrDefault();
                hRMT = db.HRMT01.Where(x => x.USER_KEY == CrePerson && x.USER_STATUS==UserStaus.Enabled).FirstOrDefault();

                if (hRMT != null)
                {
                    CrePerson = hRMT?.NAME;
                }
             
                model.Details = (from a in db.ADDT38
                                 where a.ROLL_CALL_ID == model.Keyword
                                 //&& a.IS_PRESENT == false
                                 select new RollCallBarcodeCashDetailViewModel()
                                 {
                                     ROLL_CALL_ID = a.ROLL_CALL_ID,
                                     SCHOOL_NO = a.SCHOOL_NO,
                                     USER_NO = a.USER_NO,
                                  Cre_Person= CrePerson??"",
                                     GRADE = a.GRADE,
                                     CLASS_NO = a.CLASS_NO,
                                     CHG_DATE = a.CHG_DATE,
                                     ROLL_NUM=a.ROLL_NUM,
                                     SYEAR = a.SYEAR,
                                     SEMESTER = a.SEMESTER,
                                     NAME = a.NAME,
                                     SNAME = a.SNAME,
                                     SEAT_NO = a.SEAT_NO,
                                     BarCode = a.BarCode,
                                     IS_PRESENT = a.IS_PRESENT,
                                     CASH = (short)(a.CASH ?? model.Main.CASH),
                                 }).OrderBy(x=>x.ROLL_NUM).ToList();

                return model;
            }
        }
        public IResult SaveRollCallCashData(RollCallCashViewModel model, UserProfile user, ref ECOOL_DEVEntities db, ref List<Tuple<string, string, int>> valuesList)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    if (model.Details.Count > 0)
                    {
                        var SaveUp = db.AWAT12.Where(a => a.ROLL_CALL_ID == model.Keyword).FirstOrDefault();

                        if (SaveUp == null)
                        {
                            result.Message += "查無此資料";
                            return result;
                        }

                        SaveUp.STATUS = (byte)AWAT12.StatusVal.己給點;

                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

                        var oAWAT13s = db.AWAT13.Where(x => x.ROLL_CALL_ID == model.Keyword).ToList();

                        List<AWAT13> aWAT13s = new List<AWAT13>();

                        foreach (var item in model.Details)
                        {
                            var thisAWAT13 = oAWAT13s.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_NO == item.USER_NO).FirstOrDefault();

                            if (thisAWAT13 != null)
                            {
                                
                                thisAWAT13.CASH = item.CASH;
                                thisAWAT13.CHG_DATE = DateTime.Now;
                                aWAT13s.Add(thisAWAT13);
                              //  db.AWAT13.Add(thisAWAT13);
                                //這次有配載 ,需加點
                                if (thisAWAT13.CASH > 0)
                                {
                                    ECOOL_APP.CashHelper.AddCash(user, (int)thisAWAT13.CASH, thisAWAT13.SCHOOL_NO, thisAWAT13.USER_NO, "AWAT13"
                                        , thisAWAT13.ROLL_CALL_ID + thisAWAT13.SCHOOL_NO + thisAWAT13.USER_NO
                                        , $"{SaveUp.ROLL_CALL_NAME}，獲得 {thisAWAT13.CASH} 點", true, ref db,"","",ref valuesList);
                                }
                            }
                        }

                        //if (aWAT13s?.Count() > 0)
                        //{
                        //    EFBatchOperation.For(db, db.AWAT13).UpdateAll(aWAT13s, x => x.ColumnsToUpdate(u => u.CHG_DATE, u => u.CASH));
                        //}
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }
    }
}