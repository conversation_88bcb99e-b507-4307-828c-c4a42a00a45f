/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Alphabets/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Alphabets={directory:"Alphabets/Regular",family:"GyrePagellaMathJax_Alphabets",testString:"\u00A0\u0E3F\u2103\u2107\u2109\u2116\u2117\u211E\u2120\u2122\u2126\u212A\u212B\u212E\uFEFF",32:[0,0,250,0,0],160:[0,0,250,0,0],3647:[775,83,611,26,576],8451:[709,20,1077,50,1038],8455:[689,20,500,30,477],8457:[692,3,919,50,899],8470:[692,20,1096,0,1050],8471:[668,19,747,31,718],8478:[692,3,668,22,669],8480:[700,-320,938,40,898],8482:[692,-326,979,40,939],8486:[709,3,839,38,801],8490:[692,3,726,22,719],8491:[939,3,778,15,756],8494:[623,0,772,40,732],65279:[0,0,0,0,0]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Alphabets"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Alphabets/Regular/Main.js"]);
