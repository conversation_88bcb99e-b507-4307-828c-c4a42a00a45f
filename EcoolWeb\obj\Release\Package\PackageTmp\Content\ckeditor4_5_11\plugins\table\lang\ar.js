﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'ar', {
	border: 'الحدود',
	caption: 'الوصف',
	cell: {
		menu: 'خلية',
		insertBefore: 'إدراج خلية قبل',
		insertAfter: 'إدراج خلية بعد',
		deleteCell: 'حذف خلية',
		merge: 'دمج خلايا',
		mergeRight: 'دمج لليمين',
		mergeDown: 'دمج للأسفل',
		splitHorizontal: 'تقسيم الخلية أفقياً',
		splitVertical: 'تقسيم الخلية عمودياً',
		title: 'خصائص الخلية',
		cellType: 'نوع الخلية',
		rowSpan: 'امتداد الصفوف',
		colSpan: 'امتداد الأعمدة',
		wordWrap: 'التفاف النص',
		hAlign: 'محاذاة أفقية',
		vAlign: 'محاذاة رأسية',
		alignBaseline: 'خط القاعدة',
		bgColor: 'لون الخلفية',
		borderColor: 'لون الحدود',
		data: 'بيانات',
		header: 'عنوان',
		yes: 'نعم',
		no: 'لا',
		invalidWidth: 'عرض الخلية يجب أن يكون عدداً.',
		invalidHeight: 'ارتفاع الخلية يجب أن يكون عدداً.',
		invalidRowSpan: 'امتداد الصفوف يجب أن يكون عدداً صحيحاً.',
		invalidColSpan: 'امتداد الأعمدة يجب أن يكون عدداً صحيحاً.',
		chooseColor: 'اختر'
	},
	cellPad: 'المسافة البادئة',
	cellSpace: 'تباعد الخلايا',
	column: {
		menu: 'عمود',
		insertBefore: 'إدراج عمود قبل',
		insertAfter: 'إدراج عمود بعد',
		deleteColumn: 'حذف أعمدة'
	},
	columns: 'أعمدة',
	deleteTable: 'حذف الجدول',
	headers: 'العناوين',
	headersBoth: 'كلاهما',
	headersColumn: 'العمود الأول',
	headersNone: 'بدون',
	headersRow: 'الصف الأول',
	invalidBorder: 'حجم الحد يجب أن يكون عدداً.',
	invalidCellPadding: 'المسافة البادئة يجب أن تكون عدداً',
	invalidCellSpacing: 'المسافة بين الخلايا يجب أن تكون عدداً.',
	invalidCols: 'عدد الأعمدة يجب أن يكون عدداً أكبر من صفر.',
	invalidHeight: 'ارتفاع الجدول يجب أن يكون عدداً.',
	invalidRows: 'عدد الصفوف يجب أن يكون عدداً أكبر من صفر.',
	invalidWidth: 'عرض الجدول يجب أن يكون عدداً.',
	menu: 'خصائص الجدول',
	row: {
		menu: 'صف',
		insertBefore: 'إدراج صف قبل',
		insertAfter: 'إدراج صف بعد',
		deleteRow: 'حذف صفوف'
	},
	rows: 'صفوف',
	summary: 'الخلاصة',
	title: 'خصائص الجدول',
	toolbar: 'جدول',
	widthPc: 'بالمئة',
	widthPx: 'بكسل',
	widthUnit: 'وحدة العرض'
} );
