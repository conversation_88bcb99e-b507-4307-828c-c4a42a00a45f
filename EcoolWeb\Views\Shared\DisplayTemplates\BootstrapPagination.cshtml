﻿@model MvcPaging.PaginationModel

@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
 


    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        <div class="text-center">
            <nav>
                <ul class="pager">
                    @foreach (var link in Model.PaginationLinks)
                    {
                            @AppBuildLink(link)
                    }
                </ul>
            </nav>
            <div>
                第&nbsp; @Model.CurrentPage &nbsp;頁    / 共&nbsp; @Model.PageCount &nbsp;頁
            </div>
        </div>
    }
    else
    {
        <div id="pagination" class="text-center">
            <ul class="paginationW pagination-sm">
                @foreach (var link in Model.PaginationLinks)
    {
        @BuildLink(link)
                }
                <li><a role="button">&nbsp;共&nbsp; @Model.TotalItemCount &nbsp;筆</a></li>
            </ul>
        </div>

    }
}




@helper AppBuildLink(MvcPaging.PaginationLink link)
{
if (link.DisplayText == "上頁" || link.DisplayText == "下頁")
{
    var liBuilder = new TagBuilder("li");

    if (link.IsCurrent)
    {
        liBuilder.MergeAttribute("class", "active");
    }
    if (!link.Active)
    {
        liBuilder.MergeAttribute("class", "disabled");
    }


    var aBuilder = new TagBuilder("a");

    if (link.Url != null)
    {
        aBuilder.MergeAttribute("role", "button");
        aBuilder.MergeAttribute("onclick", "FunPageProc(" + link.PageIndex + ")");
    }

    if (link.DisplayText == "上頁")
    {
        aBuilder.MergeAttribute("id", "next");
        aBuilder.SetInnerText("上一頁");
    }
    else if (link.DisplayText == "下頁")
    {
        aBuilder.MergeAttribute("id", "prev");
        aBuilder.SetInnerText("下一頁");
    }


    liBuilder.InnerHtml = aBuilder.ToString();

    @Html.Raw(liBuilder.ToString())
  }
}



@helper BuildLink(MvcPaging.PaginationLink link)
{
var liBuilder = new TagBuilder("li");
if (link.IsCurrent)
{
    liBuilder.MergeAttribute("class", "active");
}
if (! link.Active)
{
    liBuilder.MergeAttribute("class", "disabled");
}

var aBuilder = new TagBuilder("a");

if (link.Url == null)
{

}
else
{
    aBuilder.MergeAttribute("role", "button");
    aBuilder.MergeAttribute("onclick", "FunPageProc(" + link.PageIndex + ")");
}

if (link.DisplayText == "上頁")
{
    aBuilder.MergeAttribute("id", "next");
}
else if (link.DisplayText == "下頁")
{
    aBuilder.MergeAttribute("id", "prev");
}
else
{
    aBuilder.SetInnerText(link.DisplayText);
}

aBuilder.SetInnerText(link.DisplayText);
liBuilder.InnerHtml = aBuilder.ToString();


	@Html.Raw(liBuilder.ToString())
}