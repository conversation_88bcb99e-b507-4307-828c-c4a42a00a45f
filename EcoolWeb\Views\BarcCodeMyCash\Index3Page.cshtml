﻿@model BarcCodeMyCashIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = null;
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
}

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    <link rel="stylesheet" href="~/assets/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="~/assets/css/stylesATM.css?@DateNowStr">

    <link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />
    <link href="~/Content/css/jquery.validationEngine.css" rel="stylesheet" />
    <link href="~/Content/css/bootstrap.css" rel="stylesheet" />

    <link href="~/Content/css/EzCss.css?@DateNowStr" rel="stylesheet" />
    <link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />

    <script src="~/Scripts/modernizr-2.6.2.js"></script>

    <script src=@Url.Content("~/Scripts/jquery-3.6.4.min.js")></script>
    <script src="~/Scripts/jquery-ui.min.js"></script>
    <script src="~/Scripts/ECoolBase.js"></script>

    <script src="~/Scripts/bootstrap.js"></script>
    <script src="~/Scripts/respond.js"></script>

    <style>
        #board_text_1-3_a1:hover {
            cursor: pointer;
        }

        .loading-image {
            opacity: 0.8;
            margin: 15% 20%;
            z-index: 10;
            color: white;
            background-color: black;
            padding: 2%;
        }

        .loader {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1; /* Sit on top */
            padding-top: 100px; /* Location of the box */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgb(0,0,0); /* Fallback color */
            background-color: rgba(0,0,0,0.8); /* Black w/ opacity */
        }

        .text-lightgreen {
            color: #17f2b8;
        }
    </style>
</head>

<body style="background-color:#324542">

    @using (Html.BeginForm("Index3Page", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
    {
        @Html.AntiForgeryToken()

        <div id="PageContent">
            @Html.Action("_PageContent3", (string)ViewBag.BRE_NO)
        </div>
    }

    <div class="loader">
        <center>
            <h1 class="loading-image"><span class="glyphicon glyphicon-search"></span>查詢中，請稍後 ...</h1>
        </center>
    </div>
    <script type="text/javascript">

  var targetFormID = '#form1';

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageContent3", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                },
                beforeSend: function () {
                    $('.loader').show()
                },
                complete: function () {
                    setTimeout(function () { $('.loader').hide(); }, 1000);
                }
            });
    }
    </script>
</body>
</html>