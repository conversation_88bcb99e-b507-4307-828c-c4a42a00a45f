// Modern jQuery code for ADDI02 PageContent partial view
$(document).ready(function() {
    // 狀態篩選模組
    const statusFilter = {
        init: function() {
            window.DoWRITING_STATUS = this.filterByStatus.bind(this);
            this.bindEvents();
        },

        bindEvents: function() {
            // 綁定狀態篩選按鈕事件
            $('.btn-pink').on('click', this.handleStatusButtonClick.bind(this));
        },

        handleStatusButtonClick: function(e) {
            const $button = $(e.target);
            const status = $button.data('status') || '';
            
            // 更新按鈕狀態
            $('.btn-pink').removeClass('active');
            $button.addClass('active');
            
            // 執行篩選
            this.filterByStatus(status);
        },

        filterByStatus: function(status) {
            // 更新隱藏欄位
            $('#whereAPPLY_STATUS').val(status);
            
            // 重置頁碼
            $('#Page').val(1);
            
            // 執行AJAX更新
            if (typeof window.funAjax === 'function') {
                window.funAjax();
            } else {
                console.warn('funAjax function not found');
            }
        }
    };

    // 表格排序模組
    const tableSorter = {
        init: function() {
            window.FunSort = this.sortTable.bind(this);
            this.bindEvents();
        },

        bindEvents: function() {
            // 綁定排序標題點擊事件
            $('.sortable-header').on('click', this.handleSortClick.bind(this));
        },

        handleSortClick: function(e) {
            const $element = $(e.target);
            const sortColumn = $element.data('sort-column');
            
            if (sortColumn) {
                this.sortTable(sortColumn);
            }
        },

        sortTable: function(column) {
            // 更新排序欄位
            const currentColumn = $('#OrdercColumn').val();
            const currentSyntax = $('#SyntaxName').val();
            
            // 如果是同一欄位，切換排序方向
            if (currentColumn === column) {
                const newSyntax = currentSyntax === 'ASC' ? 'DESC' : 'ASC';
                $('#SyntaxName').val(newSyntax);
            } else {
                $('#OrdercColumn').val(column);
                $('#SyntaxName').val('ASC');
            }
            
            // 重置頁碼
            $('#Page').val(1);
            
            // 更新排序指示器
            this.updateSortIndicators(column, $('#SyntaxName').val());
            
            // 執行AJAX更新
            if (typeof window.funAjax === 'function') {
                window.funAjax();
            } else {
                console.warn('funAjax function not found');
            }
        },

        updateSortIndicators: function(column, direction) {
            // 移除所有排序指示器
            $('.sort-indicator').remove();
            
            // 添加新的排序指示器
            const indicator = direction === 'ASC' ? '↑' : '↓';
            const $header = $(`[data-sort-column="${column}"]`);
            $header.append(`<span class="sort-indicator"> ${indicator}</span>`);
        }
    };

    // 行導航模組
    const rowNavigation = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            // 綁定表格行點擊事件
            $('.data-row').on('click', this.handleRowClick.bind(this));
        },

        handleRowClick: function(e) {
            // 防止在點擊特定元素時觸發行點擊
            if ($(e.target).is('a, button, input, select')) {
                return;
            }

            const $row = $(e.currentTarget);
            const applyNo = $row.data('apply-no');
            const applyStatus = $row.data('apply-status');

            if (applyNo && applyStatus) {
                // 調用父頁面的 onBtnLink 函數
                if (typeof window.onBtnLink === 'function') {
                    window.onBtnLink(applyNo, applyStatus);
                } else {
                    console.warn('onBtnLink function not found');
                }
            }
        }
    };

    // 分頁處理模組
    const paginationHandler = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            // 綁定分頁連結點擊事件
            $(document).on('click', '.pagination a', this.handlePageClick.bind(this));
        },

        handlePageClick: function(e) {
            e.preventDefault();
            
            const $link = $(e.target);
            const href = $link.attr('href');
            
            // 從URL中提取頁碼
            const pageMatch = href.match(/page=(\d+)/i);
            if (pageMatch) {
                const pageNumber = pageMatch[1];
                this.goToPage(pageNumber);
            }
        },

        goToPage: function(pageNumber) {
            $('#Page').val(pageNumber);
            
            // 執行AJAX更新
            if (typeof window.funAjax === 'function') {
                window.funAjax();
            } else {
                console.warn('funAjax function not found');
            }
        }
    };

    // 用戶體驗增強模組
    const uxEnhancer = {
        init: function() {
            this.enhanceTableRows();
            this.addLoadingStates();
            this.addTooltips();
        },

        enhanceTableRows: function() {
            // 為表格行添加hover效果
            $('.data-row').hover(
                function() {
                    $(this).addClass('table-row-hover');
                },
                function() {
                    $(this).removeClass('table-row-hover');
                }
            );
        },

        addLoadingStates: function() {
            // 為按鈕添加載入狀態
            $('.btn').on('click', function() {
                const $btn = $(this);
                const originalText = $btn.text();
                
                $btn.prop('disabled', true)
                    .html('<i class="fa fa-spinner fa-spin"></i> 載入中...');
                
                // 設置超時恢復
                setTimeout(function() {
                    $btn.prop('disabled', false).text(originalText);
                }, 3000);
            });
        },

        addTooltips: function() {
            // 為排序標題添加提示
            $('.sortable-header').attr('title', '點擊排序');
            
            // 為狀態按鈕添加提示
            $('.btn-pink').each(function() {
                const text = $(this).text();
                $(this).attr('title', '篩選' + text + '狀態');
            });
            
            // 為表格行添加提示
            $('.data-row').attr('title', '點擊查看詳細資料');
        }
    };

    // 數據統計模組
    const dataStats = {
        init: function() {
            this.updateStats();
        },

        updateStats: function() {
            const totalRows = $('.data-row').length;
            const passedRows = $('.data-row[data-apply-status="' + window.ADDI02_CONFIG.passStatus + '"]').length;
            const draftRows = $('.data-row[data-apply-status="' + window.ADDI02_CONFIG.tempSaveStatus + '"]').length;
            
            console.log('數據統計:', {
                總計: totalRows,
                已通過: passedRows,
                草稿: draftRows
            });
        }
    };

    // 錯誤處理模組
    const errorHandler = {
        init: function() {
            this.setupErrorHandling();
        },

        setupErrorHandling: function() {
            $(window).on('error', function(e) {
                console.error('頁面錯誤:', e);
            });
        }
    };

    // 初始化所有模組
    statusFilter.init();
    tableSorter.init();
    rowNavigation.init();
    paginationHandler.init();
    uxEnhancer.init();
    dataStats.init();
    errorHandler.init();

    // 設置全局配置（需要在父頁面中定義）
    window.ADDI02_CONFIG = window.ADDI02_CONFIG || {};
    window.ADDI02_URLS = window.ADDI02_URLS || {};
});
