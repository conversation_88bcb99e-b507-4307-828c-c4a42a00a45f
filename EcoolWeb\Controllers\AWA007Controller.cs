﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP.EF;
using EcoolWeb.ViewModels;
using EcoolWeb.Models;
using ECOOL_APP;
using System.Data.Entity;
using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using EcoolWeb.CustomAttribute;
using Dapper;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWA007Controller : Controller
    {

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private UserProfile user = UserProfileHelper.Get();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();
        // GET: AWA007
        public ActionResult Query(AWA007QueryViewModel model)
        {
            if (model == null) model = new AWA007QueryViewModel();

            string SchoolNO = string.Empty;

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            else
            {
                SchoolNO = "ALL";
            }


            string sSQL = @"SELECT aw07.PLAYER_NO,
                            aw06.PLAYER_NAME,
                            aw06.PLAYER_TYPE,
                            aw06.IMG_FILE,
	                        COUNT(*) AS PLAYER_COUNT
                        FROM AWAT07 aw07 (NOLOCK)
                        join AWAT06 aw06  (NOLOCK) ON aw07.PLAYER_NO = aw06.PLAYER_NO
                        WHERE aw07.TRANS_CASH > 0 ";

            if (SchoolNO!= SharedGlobal.ALL)
            {
                sSQL = sSQL + @" and aw07.SCHOOL_NO =@SCHOOL_NO ";
            }

            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                sSQL = sSQL + @" and CONVERT(nvarchar(6),aw07.TRANS_DATE,112) = CONVERT(nvarchar(6), GETDATE(), 112) ";
            }

            sSQL = sSQL+@" GROUP BY aw07.PLAYER_NO, aw06.PLAYER_NAME,aw06.PLAYER_TYPE, aw06.IMG_FILE
                          ORDER BY PLAYER_COUNT DESC";

            IQueryable<uAWA006> AWA006List = db.Database.Connection.Query<uAWA006>(sSQL
          , new
          {
              SCHOOL_NO = model.whereSchoolNo,
          }).AsQueryable();

          

            int PageSize = 20;

            if (model.IsPrint)
            {
                PageSize = int.MaxValue;
            }


            model.AWA006List = AWA006List.OrderByDescending(a => a.PLAYER_COUNT).ThenBy(a=>a.PLAYER_NO).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);


            if (model.IsPrint)
            {
                if (model.AWA006List.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    model.AWA006List = AWA006List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(SchoolNO, null);

            return View(model);
        }
    }
}