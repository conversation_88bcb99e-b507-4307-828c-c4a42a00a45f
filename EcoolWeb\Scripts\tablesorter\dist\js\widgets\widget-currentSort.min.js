(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: currentSort - 7/31/2016 (v2.27.0) */
!function(t){"use strict";var i=jQuery.tablesorter;i.currentSortLanguage={0:"asc",1:"desc",2:"unsorted"},i.currentSort={init:function(t){t.$table.on("sortEnd.tscurrentSort",function(){i.currentSort.update(this.config)})},update:function(t){if(t){var r,n=t.widgetOptions,o=i.currentSortLanguage,e=o[2],c=Array.apply(null,Array(t.columns)).map(String.prototype.valueOf,e),u=t.sortList,a=u.length;for(r=0;r<a;r++)c[u[r][0]]=o[u[r][1]];t.currentSort=c,"function"==typeof n.currentSort_callback&&n.currentSort_callback(t,c)}}},i.addWidget({id:"currentSort",options:{currentSort_callback:null},init:function(t,r,n,o){i.currentSort.init(n,o)},remove:function(t,r){r.$table.off("sortEnd.tscurrentSort")}})}();return jQuery;}));
