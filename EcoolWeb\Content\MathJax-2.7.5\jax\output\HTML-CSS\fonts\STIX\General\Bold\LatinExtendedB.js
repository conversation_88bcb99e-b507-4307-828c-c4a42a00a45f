/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LatinExtendedB.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{384:[676,14,553,-28,516],392:[576,14,568,30,574],400:[686,4,610,38,587],402:[706,155,500,0,498],405:[676,10,797,14,767],409:[691,0,533,12,533],410:[676,0,291,24,265],411:[666,0,536,60,526],414:[473,205,559,21,539],416:[732,19,778,35,788],417:[505,14,554,25,576],421:[673,205,550,10,515],426:[689,228,446,25,421],427:[630,218,347,18,331],429:[691,12,371,19,389],431:[810,19,796,16,836],432:[596,14,600,16,626],442:[450,237,441,9,415],443:[688,0,515,27,492],446:[541,10,527,78,449],448:[740,0,186,60,126],449:[740,0,313,60,253],450:[740,0,445,39,405],451:[691,13,333,81,251],496:[704,203,333,-57,335],506:[972,0,722,9,689],507:[923,14,500,25,488],508:[923,0,1000,4,951],509:[713,14,722,33,694],510:[923,74,778,35,743],511:[713,92,500,25,476],567:[461,203,333,-57,260]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/LatinExtendedB.js");
