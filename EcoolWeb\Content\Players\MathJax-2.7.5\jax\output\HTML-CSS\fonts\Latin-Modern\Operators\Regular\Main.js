/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Operators/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Operators={directory:"Operators/Regular",family:"LatinModernMathJax_Operators",testString:"\u00A0\u2206\u220A\u220C\u220E\u220F\u2210\u2211\u221F\u222C\u222D\u222E\u222F\u2230\u2231",32:[0,0,332,0,0],160:[0,0,332,0,0],8710:[716,0,833,47,785],8714:[443,-57,498,56,442],8716:[730,230,667,80,587],8718:[554,0,666,56,610],8719:[750,250,944,56,887],8720:[750,250,944,56,887],8721:[750,250,1056,56,999],8735:[679,-13,778,56,722],8748:[805,306,1035,56,979],8749:[805,306,1405,56,1349],8750:[805,306,665,56,609],8751:[805,306,1035,56,979],8752:[805,306,1405,56,1349],8753:[805,306,695,56,667],8754:[805,306,700,56,672],8755:[805,306,682,56,644],8758:[422,-78,278,86,192],8759:[422,-78,516,86,430],8760:[504,-230,778,56,722],8761:[422,-78,906,56,850],8762:[504,4,778,56,722],8763:[536,36,773,56,717],8766:[466,-34,901,56,845],8767:[492,-8,778,56,722],8772:[603,103,778,56,722],8775:[603,103,778,56,722],8777:[603,103,773,56,717],8779:[541,41,773,56,717],8780:[541,-36,778,56,722],8788:[422,-78,906,56,850],8789:[422,-78,906,56,850],8792:[619,-133,778,56,722],8793:[752,-133,778,56,722],8794:[752,-133,778,56,722],8795:[810,-133,778,56,722],8797:[793,-133,778,56,722],8798:[684,-133,778,56,722],8799:[803,-133,778,56,722],8802:[730,230,778,56,722],8803:[561,61,778,56,722],8813:[730,230,778,56,722],8820:[691,191,776,55,719],8821:[691,191,776,55,719],8824:[776,276,778,76,701],8825:[776,276,778,76,701],8836:[730,230,778,85,693],8837:[730,230,778,85,693],8844:[604,20,667,61,607],8845:[604,20,667,61,607],8860:[592,92,796,56,740],8870:[684,0,445,56,389],8871:[684,0,445,56,389],8875:[684,0,653,56,597],8886:[400,-100,1078,56,1022],8887:[400,-100,1078,56,1022],8889:[603,103,818,56,762],8893:[684,17,642,55,585],8894:[679,109,900,56,844],8895:[679,-13,778,56,722],8896:[780,264,833,51,781],8897:[764,280,833,51,781],8898:[772,250,833,50,784],8899:[750,272,833,50,784],8903:[586,86,802,56,745],8917:[750,250,778,56,722],8924:[631,119,778,76,702],8925:[631,119,778,76,702],8930:[730,230,778,76,702],8931:[730,230,778,76,702],8932:[627,211,778,76,702],8933:[627,211,778,76,702],8944:[500,0,613,56,556],10752:[743,243,1111,63,1049],10753:[743,243,1111,63,1049],10754:[743,243,1111,63,1049],10755:[750,272,833,50,784],10756:[750,272,833,50,784],10757:[764,264,833,50,784],10758:[764,264,833,50,784],10761:[740,240,1092,55,1036],10764:[805,306,1775,56,1719],10769:[805,306,695,56,667],10799:[496,-3,778,142,636]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Operators"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Operators/Regular/Main.js"]);
