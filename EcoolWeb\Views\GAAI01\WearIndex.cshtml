﻿@model GAAI01WearIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<style>
    .btnUserNo {
        width: 100%;
        box-sizing: border-box; /* 使元素的寬度包括padding margin, border*/
        -moz-box-sizing: border-box;
    }
</style>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "WearIndex" });
}

@using (Html.BeginForm("WearIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.IsSearch)
    @Html.HiddenFor(m => m.WhereWearModelType)
    @Html.HiddenFor(m => m.WhereSYEARSEMESTER)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.WhereCLASS_NO, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereCLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.WhereALARM_ID, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.WhereALARM_ID, (IEnumerable<SelectListItem>)ViewBag.AlarmCycleSelectItems, new { @class = "form-control" })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereALARM_ID, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-md-3">
                    </div>
                    <div class="col-md-9">
                        @if (Model.IsWearData == true)
                        {
                            <strong style="background-color:crimson;color:aliceblue;font-size:24px">
                                *此班級這個登記日期已登記過，詳細資料請至
                                「<a target="_blank" href="@Url.Action("SearchClassIndex" ,new { WhereCLASS_NO = Model.WhereCLASS_NO ,WhereSYEARSEMESTER=$"{ Model.gAAT01?.SYEAR}_{Model.gAAT01?.SEMESTER}"})" style="color:aliceblue"> 查詢班級登記</a>」
                                查詢
                            </strong>
                        }
                        else
                        {
                            <strong style="color:red">*一週只須登記一次，所以日期以一週做選擇，例如108/9/2~108/9/6</strong>
                        }
                    </div>
                </div>
            </div>
            @if (Model.IsWearData == true)
            {
                <div class="text-center">
                    <button type="button" class="btn btn-default" onclick="onShowContentDiv()">
                        <span class="fa fa-check-circle" aria-hidden="true"></span>我仍然要輸入
                    </button>
                    <a href="@Url.Action("WearIndex")" class="btn btn-default"> 放棄</a>
                </div>
            }
            else
            {
                <div class="text-center">
                    <button type="button" class="btn btn-default" onclick="onSearch()">
                        <span class="fa fa-check-circle" aria-hidden="true"></span>我要登記
                    </button>
                </div>
            }
        </div>
    </div>

    if (Model.IsSearch == 1)
    {
        @Html.HiddenFor(m => m.STUDENT_NUMBER)

        if (ViewBag.UserNoItems != null)
        {
            <div id="contentDiv" style="@(Model.IsWearData == true ? "display:none":"")">
                <div class="panel panel-ZZZ" name="TOP" id="DivUserNo">
                    <div class="panel-heading text-center">
                        ※勾選<font color="red">未配戴</font>學生 , 全班班級人數 @(Model.STUDENT_NUMBER) 人
                    </div>
                    <div class="panel-body">
                        <div class="row">

                            @if (ViewBag.UserNoItems != null)
                            {
                                var UserNoItems = ViewBag.UserNoItems as List<SelectListItem>;

                                <div class="col-md-12">
                                    <div class="btn-group btn-group" data-toggle="buttons">
                                        <label class="btnUserNoAll btn">
                                            <input class="inputUserNoAll" name="inputUserNoAll" id="inputUserNoAll" value="" type="checkbox" autocomplete="off" onchange="onUserNoCheckbox('DivUserNo','', this.checked)">
                                            <i class="fa fa-square-o fa-2x"></i><i class="fa fa-check-square-o fa-2x"></i>
                                            <span style="color:black">全選</span>
                                        </label>
                                    </div>
                                </div>

                                foreach (var item in UserNoItems.OrderBy(x=>x.Text))
                                {
                                    <div class="col-md-3 col-xs-6">
                                        <div class="btn-group btn-group" data-toggle="buttons">
                                            <label class="btnUserNo btn @(item.Selected ? "active":"") ">
                                                <input class="inputUserNo" name="@Html.NameFor(m=>m.USER_NOs)" title="" id="@(Html.NameFor(m=>m.USER_NOs))<EMAIL>" value="@item.Value" type="checkbox" autocomplete="off" @(item.Selected ? "checked" : "") onchange="onUserNoCheckbox('DivUserNo','@item.Value', this.checked)">
                                                <i class="fa fa-square-o fa-2x"></i><i class="fa fa-check-square-o fa-2x"></i>
                                                <span style="color:black" title="@item.Text"> @StringHelper.LeftStringR(item.Text, 6)</span>
                                            </label>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <strong style="color:red;font-size:20px">*全班都有配帶，就全部不用打勾，直接「下一步」送出即可</strong>
                <div class="text-center">
                    <hr />
                    <button type="button" class="btn btn-default" onclick="onSave()">
                        <span class="fa fa-check-circle" aria-hidden="true"></span>下一步
                    </button>
                </div>
            </div>

        }
        else
        {
            <div class="text-center">
                <strong style="color:red">查無資料</strong>
            </div>
        }

    }

}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#form1';

        function onShowContentDiv() {
            $('#contentDiv').show();
        }

        function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("UnWearMemo", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onSearch()
        {
            $('#@Html.IdFor(m=>m.IsSearch)').val(1)
            $(targetFormID).attr("action", "@Url.Action("WearIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onUserNoCheckbox(DivId,Value,checked) {
            if (checked) {
                if (Value=='') {
                    $('#'+DivId+' .inputUserNo').prop('checked', true);
                    $('#'+DivId+' .btnUserNo').addClass("active")
                }
                else {
                    $('#'+DivId+' .inputUserNoAll').prop('checked', false);
                    $('#'+DivId+' .btnUserNoAll').removeClass("active");
                }
            }
            else {
                if (checked == false) {
                  if (Value=='') {
                        $('#'+DivId+' .inputUserNo').prop('checked', false);
                        $('#'+DivId+' .btnUserNo').removeClass("active")
                  }
                  else {
                     $('#'+DivId+' .inputUserNoAll').prop('checked', false);
                     $('#'+DivId+' .btnUserNoAll').removeClass("active");
                   }
                }
            }
        }
    </script>
}