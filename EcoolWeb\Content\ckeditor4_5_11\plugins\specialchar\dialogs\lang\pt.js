﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'pt', {
	euro: 'Símbolo de Euro',
	lsquo: 'Aspa esquerda simples',
	rsquo: 'Aspa direita simples',
	ldquo: 'Aspa esquerda dupla',
	rdquo: 'Aspa direita dupla',
	ndash: 'Travessão simples',
	mdash: 'Travessão longo',
	iexcl: 'Ponto de exclamação invertido',
	cent: 'Símbolo de cêntimo',
	pound: 'Símbolo de Libra',
	curren: 'Sí<PERSON><PERSON> de Moeda',
	yen: 'Símbolo de Iene',
	brvbar: 'Barra quebrada',
	sect: 'Símbolo de secção',
	uml: 'Trema',
	copy: 'Símbolo de direitos de autor',
	ordf: 'Indicador ordinal feminino',
	laquo: 'Aspa esquerda ângulo duplo',
	not: 'Não símbolo',
	reg: 'Símbolo de registado',
	macr: '<PERSON><PERSON><PERSON><PERSON>',
	deg: 'Símbolo de graus',
	sup2: 'Expoente 2',
	sup3: 'Expoente 3',
	acute: 'Acento agudo',
	micro: 'Símbolo de micro',
	para: 'Símbolo de parágrafo',
	middot: 'Ponto do meio',
	cedil: 'Cedilha',
	sup1: 'Expoente 1',
	ordm: 'Indicador ordinal masculino',
	raquo: 'Aspas ângulo duplo para a direita',
	frac14: 'Fração vulgar 1/4',
	frac12: 'Fração vulgar 1/2',
	frac34: 'Fração vulgar 3/4',
	iquest: 'Ponto de interrogação invertido',
	Agrave: 'Letra maiúscula latina A com acento grave',
	Aacute: 'Letra maiúscula latina A com acento agudo',
	Acirc: 'Letra maiúscula latina A com circunflexo',
	Atilde: 'Letra maiúscula latina A com til',
	Auml: 'Letra maiúscula latina A com trema',
	Aring: 'Letra maiúscula latina A com sinal diacrítico',
	AElig: 'Letra maiúscula latina Æ',
	Ccedil: 'Letra maiúscula latina C com cedilha',
	Egrave: 'Letra maiúscula latina E com acento grave',
	Eacute: 'Letra maiúscula latina E com acento agudo',
	Ecirc: 'Letra maiúscula latina E com circunflexo',
	Euml: 'Letra maiúscula latina E com trema',
	Igrave: 'Letra maiúscula latina I com acento grave',
	Iacute: 'Letra maiúscula latina I com acento agudo',
	Icirc: 'Letra maiúscula latina I com cincunflexo',
	Iuml: 'Letra maiúscula latina I com trema',
	ETH: 'Letra maiúscula latina Eth (Ðð)',
	Ntilde: 'Letra maiúscula latina N com til',
	Ograve: 'Letra maiúscula latina O com acento grave',
	Oacute: 'Letra maiúscula latina O com acento agudo',
	Ocirc: 'Letra maiúscula latina I com circunflexo',
	Otilde: 'Letra maiúscula latina O com til',
	Ouml: 'Letra maiúscula latina O com trema',
	times: 'Símbolo de multiplicação',
	Oslash: 'Letra maiúscula O com barra',
	Ugrave: 'Letra maiúscula latina U com acento grave',
	Uacute: 'Letra maiúscula latina U com acento agudo',
	Ucirc: 'Letra maiúscula latina U com circunflexo',
	Uuml: 'Letra maiúscula latina E com trema',
	Yacute: 'Letra maiúscula latina Y com acento agudo',
	THORN: 'Letra maiúscula latina Rúnico',
	szlig: 'Letra minúscula latina s forte',
	agrave: 'Letra minúscula latina a com acento grave',
	aacute: 'Letra minúscula latina a com acento agudo',
	acirc: 'Letra minúscula latina a com circunflexo',
	atilde: 'Letra minúscula latina a com til',
	auml: 'Letra minúscula latina a com trema',
	aring: 'Letra minúscula latina a com sinal diacrítico',
	aelig: 'Letra minúscula latina æ',
	ccedil: 'Letra minúscula latina c com cedilha',
	egrave: 'Letra minúscula latina e com acento grave',
	eacute: 'Letra minúscula latina e com acento agudo',
	ecirc: 'Letra minúscula latina e com circunflexo',
	euml: 'Letra minúscula latina e com trema',
	igrave: 'Letra minúscula latina i com acento grave',
	iacute: 'Letra minúscula latina i com acento agudo',
	icirc: 'Letra minúscula latina i com circunflexo',
	iuml: 'Letra pequena latina i com trema',
	eth: 'Letra minúscula latina eth',
	ntilde: 'Letra minúscula latina n com til',
	ograve: 'Letra minúscula latina o com acento grave',
	oacute: 'Letra minúscula latina o com acento agudo',
	ocirc: 'Letra minúscula latina o com circunflexo',
	otilde: 'Letra minúscula latina o com til',
	ouml: 'Letra minúscula latina o com trema',
	divide: 'Símbolo de divisão',
	oslash: 'Letra minúscula latina o com barra',
	ugrave: 'Letra minúscula latina u com acento grave',
	uacute: 'Letra minúscula latina u com acento agudo',
	ucirc: 'Letra minúscula latina u com circunflexo',
	uuml: 'Letra minúscula latina u com trema',
	yacute: 'Letra minúscula latina y com acento agudo',
	thorn: 'Letra minúscula latina Rúnico',
	yuml: 'Letra minúscula latina y com trema',
	OElig: 'Ligadura maiúscula latina OE',
	oelig: 'Ligadura minúscula latina oe',
	'372': 'Letra maiúscula latina W com circunflexo',
	'374': 'Letra maiúscula latina Y com circunflexo',
	'373': 'Letra minúscula latina w com circunflexo',
	'375': 'Letra minúscula latina y com circunflexo',
	sbquo: 'Aspa Simples inferior-9',
	'8219': 'Aspa simples superior invertida-9',
	bdquo: 'Aspa duplas inferior-9',
	hellip: 'Elipse horizontal ',
	trade: 'Símbolo de marca registada',
	'9658': 'Ponteiro preto direito',
	bull: 'Marca',
	rarr: 'Seta para a direita',
	rArr: 'Seta dupla para a direita',
	hArr: 'Seta dupla direita esquerda',
	diams: 'Naipe diamante preto',
	asymp: 'Quase igual a '
} );
