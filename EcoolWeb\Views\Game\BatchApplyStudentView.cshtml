﻿@model BatchApplyStudentViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BatchWork" });
    }
}

@using (Html.BeginForm("BatchApplyStudentView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.BatchWorkType)

    @Html.HiddenFor(m => m.REF_KEY)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            請選取要報名的名單
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group text-center">
                    <div class="form-group">
                        <label class="text-info">PS.當選取的對象，若系統裡無數位學生證的卡號時，則無法報名 。</label>
                        <br>
                        <button type="button" class="btn btn-default btn-sm" title="選取對象" id="btn_Objects" href="@Url.Action("Index", "REFT01", new { BTN_ID = "#btn_Objects",REF_TABLE = "ADDT27" ,REF_KEY= Model.REF_KEY, REF_KEY_ID= "#REF_KEY" })">選取對象</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <button class="btn btn-default" type="button" onclick="OnBatchApplyStudentSave()">確定</button>
                <button class="btn btn-default" type="button" onclick="onBack()">上一步</button>
            </div>
        </div>
    </div>
}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

           $(document).ready(function () {

               $("#btn_Objects").colorbox({
                   iframe: true, width: "80%", height: "80%", opacity: 0.82
               });

    });

        function OnBatchApplyStudentSave() {

            $('#formEdit').hide()
            $('#loading').fadeIn(3000)
            setTimeout(function () { funSubmit(); }, 3000);

           }

        function funSubmit() {
                   $(targetFormID).attr("action", "@Url.Action("BatchApplyStudentSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $('@Html.IdFor(m=>m.BatchWorkType)').val('')
            $(targetFormID).attr("action", "@Url.Action("BatchWork", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}