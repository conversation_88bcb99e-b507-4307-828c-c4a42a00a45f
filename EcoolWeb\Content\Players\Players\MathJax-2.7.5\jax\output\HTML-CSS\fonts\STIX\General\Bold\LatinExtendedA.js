/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LatinExtendedA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{256:[810,0,722,9,689],257:[600,14,500,25,488],258:[901,0,722,9,689],259:[691,14,500,25,488],260:[690,205,722,9,721],261:[473,205,500,25,569],262:[923,19,722,49,687],263:[713,14,444,25,430],264:[914,19,722,49,687],265:[704,14,444,25,430],266:[876,19,722,49,687],267:[666,14,444,25,430],268:[914,19,722,49,687],269:[704,14,444,25,430],270:[914,0,722,14,690],271:[709,14,680,25,710],272:[676,0,722,6,690],273:[676,14,556,25,534],274:[810,0,667,16,641],275:[600,14,444,25,427],276:[901,0,667,16,641],277:[691,14,444,25,427],278:[876,0,667,16,641],279:[666,14,444,25,427],280:[676,205,667,16,641],281:[473,205,444,25,435],282:[914,0,667,16,641],283:[704,14,444,25,427],284:[914,19,778,37,755],285:[704,206,500,28,483],286:[901,19,778,37,755],287:[691,206,500,28,483],288:[876,19,778,37,755],289:[666,206,500,28,483],290:[691,378,778,37,755],291:[863,206,500,28,483],292:[914,0,778,21,759],293:[914,0,556,15,534],294:[676,0,778,21,759],295:[676,0,556,15,534],296:[884,0,389,14,379],297:[674,0,278,-47,318],298:[810,0,389,20,370],299:[600,0,278,-25,305],300:[900,0,389,20,370],301:[691,0,278,-11,292],302:[676,205,389,20,389],303:[691,205,278,15,321],304:[876,0,389,20,370],305:[461,0,278,15,256],306:[676,96,838,20,917],307:[691,203,552,15,531],308:[914,96,500,3,479],309:[704,203,333,-57,335],310:[676,378,778,30,769],311:[676,378,556,22,543],312:[470,0,600,19,627],313:[923,0,667,19,638],314:[923,0,278,15,260],315:[676,378,667,19,638],316:[676,378,278,15,256],317:[691,0,667,19,638],318:[709,0,457,15,442],319:[676,0,667,19,638],320:[676,0,414,15,441],321:[676,0,667,18,638],322:[676,0,278,-22,303],323:[923,18,722,16,701],324:[713,0,556,21,539],325:[676,378,722,16,701],326:[473,378,556,21,539],327:[914,18,722,16,701],328:[704,0,556,21,539],329:[709,0,705,13,693],330:[676,96,732,14,712],331:[473,205,556,21,490],332:[810,19,778,35,743],333:[600,14,500,25,476],334:[901,19,778,35,743],335:[691,14,500,25,476],336:[923,19,778,35,743],337:[713,14,500,25,476],338:[684,5,1000,22,981],339:[473,14,722,22,696],340:[923,0,722,26,716],341:[713,0,444,28,434],342:[676,378,722,26,716],343:[473,378,444,28,434],344:[914,0,722,26,716],345:[704,0,444,28,434],346:[923,19,556,35,513],347:[713,14,389,25,364],348:[914,19,556,35,513],349:[704,14,389,22,361],350:[692,218,556,35,513],351:[473,218,389,25,361],352:[914,19,556,35,513],353:[704,14,389,22,361],354:[676,218,667,31,636],355:[630,218,333,19,332],356:[914,0,667,31,636],357:[709,12,415,19,445],358:[676,0,667,31,636],359:[630,12,333,17,332],360:[886,19,722,16,701],361:[674,14,556,16,538],362:[810,19,722,16,701],363:[600,14,556,16,538],364:[901,19,722,16,701],365:[691,14,556,16,538],366:[935,19,722,16,701],367:[740,14,556,16,538],368:[923,19,722,16,701],369:[713,14,556,16,538],370:[676,205,722,16,701],371:[461,205,556,16,547],372:[914,15,1000,19,981],373:[704,14,722,23,707],374:[914,0,722,15,699],375:[704,205,500,16,482],376:[876,0,722,15,699],377:[923,0,667,28,634],378:[713,0,444,21,420],379:[876,0,667,28,634],380:[666,0,444,21,420],381:[914,0,667,28,634],382:[704,0,444,21,420],383:[691,0,333,14,389]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/LatinExtendedA.js");
