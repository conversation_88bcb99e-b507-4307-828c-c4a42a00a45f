﻿@model ADDI13IndexViewModel

@{
    ViewBag.Title = "點數領取-列表";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string LogoAct = "GuestIndex";
    //紙本掃描美化-Page1

}
@section css{
    <link href="@Url.Content("~/Content/styles/PointsCollection.min.css")" rel="stylesheet" />
    <style>
     /*本頁由學校首頁紙本兌換icon按鈕 或 ATM存款進入*/
        body {
            padding: 1rem;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background: linear-gradient(to bottom, #ddedff 0%,#7ea6d3 100%);
        }
        .challenge-loading{
            width:100%;height:100%;
            background-color:@SharedGlobal.Logo_loading_background_color;
            display:none;z-index:999;position:fixed;left:0;top:0;
        }
        /*loading*/
        .loading {
        position: absolute;
        z-index: 9999;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: none;
        }

        .loading .mask {
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: #444;
            opacity: 0.5;
        }

        .loading .animation {
            width: 64px;
            height: 64px;
            margin: auto;
            margin-top: 40px;
            background: url( @Url.Content("~/Content/images/loading-circle.gif") );
            position: relative;
        }
    </style>
}

@Html.Partial("_Notice")

@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG"  style="width:50%" class="img-responsive " alt="Responsive image" />*@

@using (Html.BeginForm("Index1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank", @class = "d-flex flex-column flex-grow-1 justify-content-center align-items-center" }))
{
    @Html.AntiForgeryToken()
    //@Html.HiddenFor(m => m.ROLL_CALL_ID)
    @Html.HiddenFor(m => m.SCHOOL_NO1)
    <div id="mainBody">
        <div class="points-collection my-3" id="panel">
            <h1 class="points-collection-title mt-2">點數領取</h1>
            <div class="input-group input-group-lg">
                <span class="input-group-addon"><i class="fa fa-user"></i></span>
                @Html.EditorFor(m => m.CARD_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入紙張裡的數字", @onKeyPress = "call(event,this);" } })

            </div>
            @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
            <div class="text-center py-3">
                <button type="button" class="btn btn-primary btn-lg btn-block px-5 py-3" onclick="OnclickCardNO()">送出</button>
            </div>
            <img src="@Url.Content("~/Content/images/img-getPaperPoints-bottom.png")" alt="" class="img-responsive" />
        </div>
    </div>

    <div class="loading">
        <div class="mask"></div>
        <div class="animation">
        </div>
    </div>

    <div id="DetailsView">
        <div id="editorRows">
            @if (Model != null && Model.Details != null && Model.Details.Count() > 0)
            {
                @Html.Action("_EditDetailsList", (string)ViewBag.BRE_NO, new { model = Model.Details.ToList() })
            }
        </div>
    </div>

    <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
            <br />


            <h3 style="color:#80b4fb">讀取中…</h3>

        </div>
    </div>


    <div class="use-absolute" id="ErrorDiv">
        <div class="use-absoluteDiv">
            <div class="alert alert-danger h1" role="alert">
                <i class="fa fa-exclamation-circle"></i>
                <strong id="ErrorStr"></strong>
            </div>
        </div>
    </div>


}

@section Scripts {

    <script src="@Url.Content("~/Scripts/buzz/buzz.min.js")"></script>
    <script>
        var targetFormID = '#form1';
        var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
        var game_trueSound = new buzz.sound("@Url.Content("~/Content/mp3/game_true.mp3")");
        var mainBody = $("#mainBody");

//        (function ($) {

//            $(window).on("beforeunload", function () {
//    return true;
//})
//        })(jQuery);




        $(document).ready(function () {
            $("#CARD_NO").focus();

        });
        function OnclickCardNO() {

            var SCHOOL_NO1 = "";
            var CARD_NO = $('#CARD_NO').val();
            SCHOOL_NO1 = $("#SCHOOL_NO1").val();

            if (CARD_NO != '') {

                if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length > 0) {
                    $('#ErrorStr').html('請勿重複刷卡，謝謝');
                    $('#ErrorDiv').show();

                    setTimeout(function () {
                        $('#CARD_NO').val('');
                        $("#CARD_NO").focus();
                        $('#ErrorStr').val('');
                        $('#ErrorDiv').hide();
                    }, 2000);
                }
                else {

                    $('#CARD_NO').prop('readonly', true);
                    mainBody.attr("style", "display:none");
                    setTimeout(function () {
                        OnKeyinUse(CARD_NO, SCHOOL_NO1)
                    });
                }

            }
        }
        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var SCHOOL_NO1 = "";
                var CARD_NO = $('#CARD_NO').val();
               SCHOOL_NO1 = $("#SCHOOL_NO1").val();
                event.preventDefault();

                if (CARD_NO != '') {

                    if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length >0 ) {
                        $('#ErrorStr').html('請勿重複刷卡，謝謝');
                        $('#ErrorDiv').show();

                        setTimeout(function () {
                            $('#CARD_NO').val('');
                            $("#CARD_NO").focus();
                            $('#ErrorStr').val('');
                            $('#ErrorDiv').hide();
                        }, 2000);
                    }
                    else {

                        $('#CARD_NO').prop('readonly', true);
                        mainBody.attr("style", "display:none");
                        setTimeout(function () {
                            OnKeyinUse(CARD_NO, SCHOOL_NO1)
                        });
                    }

                }
            }
        }

        function OnKeyinUse(CARD_NO, SCHOOL_NO1) {
            $("#StatusMessageDiv").remove()

            var data = {
                "CARD_NO": CARD_NO,
                "SCHOOL_NO1": SCHOOL_NO1
            };
            $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;");
            $.ajax({
                url: '@Url.Action("_OpenGetCash", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").html('');
                    $("#editorRows").prepend(html);
                    $('#loading').hide();
                    //$('#ErrorDiv').show();   不知為何網頁紙本掃描訊息會是空白的，先註解掉



                    if (html.length > 0) {

                        var l = 0;
                        l = $("#StatusMessageDiv").length;
                        let pattern = /恭喜領取點數 成功！/i;
                        let result = $("#StatusMessageHtmlMsg").html().match(pattern);
                        if (result == "恭喜領取點數 成功！") {


                            game_trueSound.play();
                        }
                        else {
                            if ($("#StatusMessageHtmlMsg").html() != undefined) {
                                game_falseSound.play();
                            }
                        }
                        if (l > 0) {
                            var i = 0;
                            mainBody.each(function () {
                                if (i == 0) {
                                    $(this).html('');
                                }
                                i++;
                            })
                        }
                        $('#ErrorStr').html('感應成功…')

                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");
                        //SwipeOK()
                        setTimeout(function () {

                                $('#CARD_NO').prop('readonly', false);
                                $('#CARD_NO').val('')
                                $("#CARD_NO").focus();
                                $('#ErrorDiv').hide()
                                $('#ErrorStr').html('');


                        }, 100);
                    }
                    else {
                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");

                        $('#ErrorStr').html('此數位學生證對應不到學生資料…')
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 800);
                    }

                }
            });
        }

         function SwipeOK() {
                 SwipeSound.play();
         }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }
    </script>
}
