﻿@model EcoolWeb.ViewModels.AWA003QueryViewModel
@{ ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "學生獲得點數查詢";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string HidStyle = "";
    int DataCount = 0;
    int RowNumber = 0;
    bool IsStudent = false;
    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student) { IsStudent = true; }
    }
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<script type='text/javascript' src='~/Scripts/jquery.simplemodal.js'></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@section scripts{
    <script>
        var targetFormID = '#AWA002';
        window.onload = function () {

            initDatepicker();
        }
        $(document).ready(function () {
            $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });

          function initDatepicker() {
               var opt = {
                    showMonthAfterYear: true,
                    format: moment().format('YYYY-MM-DD'),
                    showSecond: true,
                    showButtonPanel: true,
                    showTime: true,
                    beforeShow: function () {
                        setTimeout(
                            function () {
                                $('#ui-datepicker-div').css("z-index", 15);
                                $('#ui-datepicker-div').css("top", "220px");
                            }, 100
                        );
                    },
                    onSelect: function (dateText, inst) {
                        $('#' + inst.id).attr('value', dateText);
                    }
                };
                $("#@Html.IdFor(m => m.whereSTART_CRE_DATE)").datetimepicker(opt);
                $("#@Html.IdFor(m => m.whereEND_CRE_DATE)").datetimepicker(opt);
        }
        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1)
        }

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }


        function todoClear() {
            $("#doClear").val(true);
            $("#whereKeyword").val('');
            $("#OrdercColumn").val('');
            $("#whereCLASS_NO").val('');
            $("#whereGrade").val('');
            $("#whereSeat_NO").val('');
            $("#whereSTART_CRE_DATE").val('');
            $("#whereEND_CRE_DATE").val('');

            @* $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(false);*@
            FunPageProc(1)
        }
    </script>


}
@using (Html.BeginForm("AWA002_HIS", "AWA002", FormMethod.Post, new { id = "AWA002", name = "AWA002" }))
{

    <div id='container' style="@HidStyle">
        <a href='@Url.Action("ADDTALLList_HIS","ADDT", new { ADDTList = "ADDTList" })' role="button" class="btn btn-sm btn-sys  ">
            閱讀單查詢功能
        </a>
        <a href='@Url.Action("BorrowALLList_HIS","ADDT", new { ADDTList = "BorrowList" })' role="button" class="btn btn-sm btn-sys  ">
            閱讀校楷模查詢
        </a>

        <a href='@Url.Action("AWA002_HIS","AWA002", new { ADDTList = "AWA002_HIS" })' role="button" class="btn btn-sm btn-sys  active">
            學生獲得點數查詢
        </a>
        <a href='@Url.Action("AWA002_HISTeacher","AWA002", new { ADDTList = "AWA002_HIS" })' role="button" class="btn btn-sm btn-sys  ">
            老師每個月給點查詢
        </a>
    </div>
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.Page)
    <div class="form-inline" style="@HidStyle" id="Q_Div">
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">座號</label>
            </div>
            <div class="form-group">

                @Html.EditorFor(m => m.whereSeat_NO, new { htmlAttributes = new { @class = "form-control input-sm", @style = "width:70px" } })
            </div>
            <br />
            <div id="Sdate">
                <div class="form-group">
                    <label class="control-label">搜尋日期(起)</label>
                </div>

                <div class="form-group">
                    @Html.EditorFor(m => m.whereSTART_CRE_DATE, new { htmlAttributes = new { id = "whereSTART_CRE_DATE", @class = "form-control input-sm", autocomplete = "off" } })
                </div>
                <div class="form-group">
                    <label class="control-label">搜尋日期(迄)</label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereEND_CRE_DATE, new { htmlAttributes = new { id = "whereEND_CRE_DATE", @class = "form-control input-sm", autocomplete = "off" } })
                </div>
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
            @*<label>這段區間，學校一共寫了</label>

                @(Model.ADDT0809List?.FirstOrDefault().ShareCountSUM ?? 0)
                <label>張</label>*@
        </div>

    </div>
    <div class="table-responsive">
        <div class="text-center" id="tbData">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>編號</th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                            班級
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                            座號
                            <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                            學號<br />
                            <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                            姓名
                            <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('ADI07');">
                            校外榮譽
                            <img id="ADI07" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>


                        <th style="text-align: center;cursor:pointer;" onclick="doSort('ADI06');">
                            校內表現
                            <img id="ADI06" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                        </th>

                        <th style="text-align: center;cursor:pointer; vertical-align: middle;" onclick="doSort('Extend');">
                            其他(除校內外表現)
                            <img id="Extend" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                        </th>

                        <th style="text-align: center;cursor:pointer; vertical-align: middle;" onclick="doSort('SUMCash');">
                            總獲得點數
                            <img id="SUMCash" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                        </th>










                    </tr>





                </thead>
                <tbody>
                    @if (Model.VAWA003List != null)
                    {
                        foreach (var item in Model.VAWA003List)
                        {
                            DataCount++;

                            RowNumber = Model.VAWA003List.PageSize * (Model.VAWA003List.PageNumber - 1) + DataCount;
                    <tr>




                        <td>@RowNumber</td>
                        <td class="bigger" style="text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                        </td>
                        <td class="Carousel_hide">
                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                        </td>
                        <td class="Carousel_hide">
                            @Html.DisplayFor(modelItem => item.USER_NO)
                        </td>
                        <td class="bigger">
                            @Html.DisplayFor(modelItem => item.SNAME)
                        </td>
                        <td class="bigger">
                            <a href="@Url.Action("toGOQuery2","AWA002",new {whereUserNo=@Html.DisplayFor(modelItem => item.USER_NO)  ,
                                      whereSTART_CRE_DATE=  Model.whereSTART_CRE_DATE,whereEND_CRE_DATE=Model.whereEND_CRE_DATE,whereSOURCETABLE="校外榮譽"
                                    })" class=" colorbox ">
                                @Html.DisplayFor(modelItem => item.ADI07)
                            </a>

                        </td>
                        <td class="bigger">
                            <a href="@Url.Action("toGOQuery2","AWA002",new {whereUserNo=@Html.DisplayFor(modelItem => item.USER_NO)  ,
                                      whereSTART_CRE_DATE=  Model.whereSTART_CRE_DATE,whereEND_CRE_DATE=Model.whereEND_CRE_DATE,whereSOURCETABLE="校內表現"
                                    })" class=" colorbox ">
                                @Html.DisplayFor(modelItem => item.ADI06)
                            </a>

                        </td>
                        <td class="bigger">
                           
                                @Html.DisplayFor(modelItem => item.Extend)
                         
                        </td>
                        <td class="bigger">

                            <a href="@Url.Action("toGOQuery2","AWA002",new {whereUserNo=@Html.DisplayFor(modelItem => item.USER_NO)  ,
                                      whereSTART_CRE_DATE=  Model.whereSTART_CRE_DATE,whereEND_CRE_DATE=Model.whereEND_CRE_DATE,whereKeyword=""
                                    })" class=" colorbox ">@Html.DisplayFor(modelItem => item.SUMCash)</a>


                                </td>
                            </tr>

                        }
                    }

                </tbody>


            </table>


        </div>


    </div>
    if (Model.VAWA003List == null)
    {<div class="text-center">
            查無資料，請重新查詢
        </div>
    }
    else
    {
        <div>
            @Html.Pager(Model.VAWA003List.PageSize, Model.VAWA003List.PageNumber, Model.VAWA003List.TotalItemCount).Options(o => o
                  .DisplayTemplate("BootstrapPagination")
                 .MaxNrOfPages(5)
                 .SetPreviousPageText("上頁")
                 .SetNextPageText("下頁")
             )
        </div>

    }

}
