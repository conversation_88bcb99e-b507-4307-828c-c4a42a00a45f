﻿@model  ECOOL_APP.EF.ZZZI34EditViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<img src="~/Content/img/web-bar3-revise-21.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ZZZI26">
    <div class="form-horizontal">
        <label class="control-label">* 以下是成功清單：</label>
        <br /><br />
        <div class="table-responsive">
            <table class="table-ecool table-hover">
                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().NAME)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().SEAT_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().ART_SUBJECT)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Details_List)
                    {
                        <tr>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.PHOTO_SUBJECT)
                            </td>
                            <td align="center"> @Html.DisplayFor(modelItem => item.PHOTO_CASH)</td>
                            <td align="center">@Html.DisplayFor(modelItem => item.PHOTO_DESC)</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>