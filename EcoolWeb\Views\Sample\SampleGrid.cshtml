﻿@{
    ViewBag.Title = "SampleGrid";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<br />

<table id="list" class="scroll" cellpadding="0" cellspacing="0"></table>
<div id="pager" class="scroll" style="text-align: center;"></div>

<link href="/Content/css/ui.jqgrid.css" rel="stylesheet" type="text/css" />
@section Scripts
    {
    <script src="/Scripts/jquery-migrate-1.2.1.min.js" type="text/javascript"></script>
    <script src="/Scripts/js/i18n/grid.locale-zh-tw.js" type="text/javascript"></script>
    <script src="/Scripts/jquery.jqGrid.min.js" type="text/javascript"></script>
    <script type="text/javascript">

        $(function () {
            $("#list").jqGrid({
                url: '/Sample/GridGetData/',
                datatype: 'json',
                mtype: 'GET',
                height: 'auto',
                //colNames: ['獎品排序', '獎品名稱', '兌換點數', '獎品數量', '兌換起日', '兌換期限', '備註說明'],//
                colNames: ['獎品名稱', '兌換點數', '獎品數量', '兌換起日', '兌換期限', '備註說明'],//
                colModel: [
                //{ name: 'SORT_NO', index: 'SORT_NO', width: 60, align: 'left' },
                { name: 'AWARD_NAME', index: 'AWARD_NAME', width: 140, align: 'left' },
                { name: 'COST_CASH', index: 'COST_CASH', width: 60, align: 'right' },
                { name: 'COUNT_TOTAL', index: 'COUNT_TOTAL', width: 50, align: 'right' },
                { name: 'SDATETIME', index: 'SDATETIME', width: 90, align: 'center' },
                { name: 'EDATETIME', index: 'EDATETIME', width: 90, align: 'center' },
                { name: 'DESCRIPTION', index: 'DESCRIPTION', width: 200, align: 'left' }],
                pager: jQuery('#pager'),
                rowNum: 10,
                rowList: [5, 10, 20, 50],
                sortname: 'AWARD_NO',
                //sortname: 'SORT_NO',
                sortorder: "desc",
                viewrecords: true,
                imgpath: '/scripts/themes/coffee/images'
                //caption: '西湖獎品兌換清單'
            });
        });

    </script>
}
