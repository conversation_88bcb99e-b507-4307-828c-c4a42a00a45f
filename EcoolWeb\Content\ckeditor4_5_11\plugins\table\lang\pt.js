﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'pt', {
	border: 'Tamanho do contorno',
	caption: '<PERSON><PERSON>',
	cell: {
		menu: 'Célula',
		insertBefore: 'Inserir célula antes',
		insertAfter: 'Inserir célula depois',
		deleteCell: 'Apagar células',
		merge: 'Unir células',
		mergeRight: 'Unir à direita',
		mergeDown: 'Fundir abaixo',
		splitHorizontal: 'Dividir célula horizontalmente',
		splitVertical: 'Dividir célula verticalmente',
		title: 'Propriedades da célula',
		cellType: 'Tipo de célula',
		rowSpan: 'Linhas na célula',
		colSpan: 'Colunas na célula',
		wordWrap: 'Moldar texto',
		hAlign: 'Alinhamento horizontal',
		vAlign: 'Alinhamento vertical',
		alignBaseline: 'Base',
		bgColor: 'Cor de fundo',
		borderColor: 'Cor da margem',
		data: 'Dad<PERSON>',
		header: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
		yes: 'Sim',
		no: 'Não',
		invalidWidth: 'A largura da célula deve ser um número.',
		invalidHeight: 'A altura da célula deve ser um número.',
		invalidRowSpan: 'As filas da célula deve ter um número inteiro.',
		invalidColSpan: 'As colunas da célula devem ter um número inteiro.',
		chooseColor: 'Escolher'
	},
	cellPad: 'Espaço interior',
	cellSpace: 'Espaçamento de célula',
	column: {
		menu: 'Coluna',
		insertBefore: 'Inserir coluna antes',
		insertAfter: 'Inserir coluna depois',
		deleteColumn: 'Apagar colunas'
	},
	columns: 'Colunas',
	deleteTable: 'Apagar tabela',
	headers: 'Cabeçalhos',
	headersBoth: 'Ambos',
	headersColumn: 'Primeira coluna',
	headersNone: 'Nenhum',
	headersRow: 'Primeira linha',
	invalidBorder: 'O tamanho da margem tem de ser um número.',
	invalidCellPadding: 'A criação do espaço na célula deve ser um número positivo.',
	invalidCellSpacing: 'O espaçamento da célula deve ser um número positivo.',
	invalidCols: 'O número de colunas tem de ser um número maior que 0.',
	invalidHeight: 'A altura da tabela tem de ser um número.',
	invalidRows: 'O número de linhas tem de ser maior que 0.',
	invalidWidth: 'A largura da tabela tem de ser um número.',
	menu: 'Propriedades da tabela',
	row: {
		menu: 'Linha',
		insertBefore: 'Inserir linha antes',
		insertAfter: 'Inserir linha depois',
		deleteRow: 'Apagar linhas'
	},
	rows: 'Linhas',
	summary: 'Resumo',
	title: 'Propriedades da tabela',
	toolbar: 'Tabela',
	widthPc: 'percentagem',
	widthPx: 'pixéis',
	widthUnit: 'unidade da largura'
} );
