﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ValidationSummary(true, "", new { @class = "text-danger" })
@{
    Html.RenderAction("_ArtGalleryMenu", new { NowAction = "MenuIndex2" });
}
    <br />
    <div class="Div-EZ-ZZZI26">
        <div class="alert alert-success" style="padding:0 2%; margin-bottom:0px"><h3>線上藝廊申請模式</h3></div>
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group text-center">
                @{

                    string str = "";
                    string sep = Environment.NewLine;
                    string str1 = "";

                    str = "zip上傳";
                    str1 = "(只能上傳影像批次檔)";
                    string text = Html.Raw(str + sep + str1).ToString();
                    <div class="col-md-4">



                        <a class="btn btn-default  btn-block" href="../ZZZI34/StudentUploadZip?mode=批次線上藝廊" role="button">
                            多檔案壓縮上傳<br />
                            (ZIP上傳)

                        </a>

                    </div>
                    <div class="col-md-4">
                        @*@Html.ActionLink("非zip上傳", "Edit", new { FirstPage = "true", SouBre_NO = "ZZZI34" }, new { @class = "btn btn-default  btn-block", @role = "button" })*@
                        <a class="btn btn-default  btn-block" href="../ZZZI34/Edit?FirstPage=true&SouBre_NO=ZZZI34" role="button" style="height:53px">
                            一個個作品選擇上傳<br />
                            (非ZIP上傳)


                        </a>

                    </div>
                }
            </div>

        </div>
    </div>
    <div style="padding-left:30px">


        說明：<br />
        1.	Zip上傳是將影像檔案打包壓縮上傳， 非zip上傳，則是一個個檔案慢慢上傳。<br />
        2.	電腦版才有zip上傳；APP只能用非Zip上傳。<br />

    </div>