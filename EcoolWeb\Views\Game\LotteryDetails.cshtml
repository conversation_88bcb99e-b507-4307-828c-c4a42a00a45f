﻿@model GameLotteryCreViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "Lottery" });
    }
    int DataCount = 0;

}

<br />
<div class="form-inline">
    <div class="col-xs-12 text-right">
        <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
        <button class="btn btn-sm btn-sys" onclick='fn_save_Excel()'>另存新檔(Excel)</button>
    </div>
</div>
<br />

@using (Html.BeginForm("LotteryCreView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    if (Model.Main != null)
    {
        <div class="panel panel-ZZZ" name="TOP" id="tbData">
            <div class="panel-heading text-center">
                抽獎中獎名單明細
            </div>
            <div class="panel-body">
                <div class="form-horizontal">
                    <div class="">
                        <samp class="dt">
                            @Html.LabelFor(model => model.Main.CRE_DATE)  :
                        </samp>
                        <samp class="dd">
                            <strong>@Model.Main.CRE_DATE </strong>
                        </samp>
                    </div>
                    @if (ViewBag.GAME_TYPE == (byte)ADDT26.GameType.一般)
                    {
                        <div class="">
                            <samp class="dt">
                                @Html.LabelFor(model => model.Main.LEVEL_COUNT)  :
                            </samp>
                            <samp class="dd">
                                @if ((Model.Main.LEVEL_COUNT ?? 0) == 0)
                                {
                                    <strong>無限制</strong>
                                }
                                else
                                {
                                    <strong>@Model.Main.LEVEL_COUNT <text>關以上(含)</text></strong>
                                }
                            </samp>
                        </div>
                        <div class="">

                            <samp class="dt">
                                @Html.LabelFor(model => model.Main.LEVEL)  :
                            </samp>
                            <samp class="dd">
                                @if (string.IsNullOrWhiteSpace(Model.Main.LEVEL))
                                {
                                    <strong>無</strong>
                                }
                                @foreach (var item in ViewBag.LevelItem as List<SelectListItem>)
                                {
                                    if (item.Selected)
                                    {
                                        <code>@item.Text</code>
                                    }
                                }
                            </samp>
                        </div>
                    }
                    else
                    {
                        <div class="">

                            <samp class="dt">
                                @Html.LabelFor(model => model.Main.IS_FULL) :
                            </samp>
                            <samp class="dd">
                                @if (Model.Main.IS_FULL)
                                {
                                    <label>排除</label>
                                }
                                else
                                {
                                    <label>不排除</label>
                                }
                            </samp>
                        </div>
                    }

                    <div class="">
                        <samp class="dt">
                            @Html.LabelFor(model => model.Main.PEOPLE_COUNT)  :
                        </samp>
                        <samp class="dd">
                            @Model.Main.PEOPLE_COUNT
                            <text>人</text>
                        </samp>
                    </div>
                    <div class="">

                        <samp class="dt">
                            @Html.LabelFor(model => model.Main.UNLOTTERY) :
                        </samp>
                        <samp class="dd">
                            @if (Model.Main.UNLOTTERY)
                            {
                                <label>排除</label>
                            }
                            else
                            {
                                <label>不排除</label>
                            }
                        </samp>
                    </div>

                    <div class="">
                        <samp class="dt">
                            @Html.LabelFor(model => model.Main.UNCUEST)  :
                        </samp>
                        <samp class="dd">
                            @if (Model.Main.UNCUEST)
                            {
                                <label>排除</label>
                            }
                            else
                            {
                                <label>不排除</label>
                            }
                        </samp>
                    </div>

                    <div class="">
                        <samp class="dt">
                            @Html.LabelFor(model => model.Main.LOTTERY_DESC)  :
                        </samp>
                        <samp class="dd">
                            @Model.Main.LOTTERY_DESC
                        </samp>
                    </div>
                    <div class="">
                        <div class="text-center" style="padding-right:30px">
                            <button type="button" class="btn btn-default btn-sm" onclick="CheckAll()">
                                <span class="glyphicon glyphicon-ok"></span> 全部勾選
                            </button>
                            <button type="button" class="btn btn-default btn-sm" onclick="location.reload()">
                                <span class="glyphicon glyphicon-repeat"></span> 重新整理
                            </button>
                        </div>
                        <div class="table-responsive">

                            <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                                <thead>
                                    <tr>
                                        <th>序號</th>
                                        <th>
                                            <samp class="" style="cursor:pointer;" onclick="FunSort('SHORT_NAME')">
                                                @Html.DisplayNameFor(model => model.People.First().SHORT_NAME)
                                            </samp>
                                        </th>
                                        <th>
                                            <samp class="" style="cursor:pointer;" onclick="FunSort('NAME')">
                                                @Html.DisplayNameFor(model => model.People.First().NAME)
                                            </samp>
                                        </th>

                                        <th>
                                            <samp class="" style="cursor:pointer;" onclick="FunSort('PHONE')">
                                                @Html.DisplayNameFor(model => model.People.First().PHONE)
                                            </samp>
                                        </th>

                                        <th>
                                            <samp class="" style="cursor:pointer;" onclick="FunSort('GAME_USER_TYPE')">
                                                @Html.DisplayNameFor(model => model.People.First().GAME_USER_TYPE)
                                            </samp>
                                        </th>

                                        <th>
                                            <samp class="" style="cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.People.First().USER_NO)
                                            </samp>
                                        </th>
                                        <th>
                                            <samp class="" style="cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.People.First().CLASS_NO)
                                            </samp>
                                        </th>
                                        <th>
                                            <samp class="" style="cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.People.First().SEAT_NO)
                                            </samp>
                                        </th>
                                        <th>
                                            <samp class="" style="cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.People.First().GAME_USER_ID)
                                            </samp>
                                        </th>
                                        <th>
                                            <samp class="" style="cursor:pointer;" onclick="FunSort('RECEIVE_AWARD')">
                                                @Html.DisplayNameFor(model => model.People.First().RECEIVE_AWARD)
                                            </samp>
                                        </th>
                                        <th>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.People.Count() > 0)
                                    {
                                        foreach (var item in Model.People)
                                        {
                                            DataCount++;

                                            <tr id="Tr@(item.TEMP_USER_ID)" style="background-color:@(item.RECEIVE_AWARD ? "#B0E0E6" : "")">
                                                <td align="center">@DataCount</td>
                                                <td align="center">
                                                    @if (item.GAME_USER_TYPE == UserType.Student)
                                                    {
                                                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                                    }
                                                    else
                                                    {
                                                        <text>卡片</text>
                                                    }
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.NAME)
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.PHONE)
                                                </td>
                                                <td align="center">
                                                    @if (string.IsNullOrWhiteSpace(item.GAME_USER_TYPE_DESC))
                                                    {
                                                        @UserType.GetDesc(item.GAME_USER_TYPE)
                                                    }
                                                    else
                                                    {
                                                        @Html.DisplayFor(modelItem => item.GAME_USER_TYPE_DESC)
                                                    }
                                                </td>
                                                <td align="center">
                                                    @if (item.GAME_USER_TYPE == UserType.Student)
                                                    {
                                                        @Html.DisplayFor(modelItem => item.USER_NO)
                                                    }
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.GAME_USER_ID)
                                                </td>

                                                <td align="center">
                                                    <input class="receiveAward" id="RECEIVE_AWARD_@(DataCount)" type="checkbox" value="@item.TEMP_USER_ID" @(item.RECEIVE_AWARD ? "checked" : "") onclick="OnUpdateReceiveAward(this,'@item.LOTTERY_NO','@item.ITEM_NO')" onpropertychange="OnUpdateReceiveAward(this,'@item.LOTTERY_NO','@item.ITEM_NO')" />
                                                </td>
                                                <td align="center" style="width:100px">
                                                    <strong id="Strong@(item.TEMP_USER_ID)" style="display: @(item.RECEIVE_AWARD ? "block" : "none")"><span class="glyphicon glyphicon-ok"></span> 已領取</strong>
                                                </td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center">
            <h1>查無資料</h1>
        </div>
    }

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <button class="btn btn-default" type="button" onclick="onBack()">回上一頁</button>
            </div>
        </div>
    </div>

}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        jQuery(document).ready(function () {
            $(".checkbox-inline:eq(1)").before('<br />');
        })

        function CheckAll() {
            $(".receiveAward").each(function () {
                if (this.checked==false) {
                    $(this).click()
                }
            })
        }

        function OnUpdateReceiveAward(This, LOTTERY_NO_Val, ITEM_NO_Val) {

            var Checked_Val = This.checked;
            var ID_VAL = This.id;

            $.ajax({
                url: "@(Url.Action("SaveUpdateReceiveAward", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    LOTTERY_NO: LOTTERY_NO_Val
                    , ITEM_NO: ITEM_NO_Val
                    , Checked: Checked_Val
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                async: false, // 非同步
                timeout: 3000,
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        alert(res.Error);

                        if (Checked_Val)
                        {
                            $('#' + ID_VAL).prop("checked", false)
                        }
                        else
                        {
                            $('#' + ID_VAL).prop("checked", true)
                        }
                    }
                    else {
                        if (Checked_Val) {
                            $('#Tr' + This.value).css("background-color", "#B0E0E6");
                            $('#Strong' + This.value).show()
                        }
                        else {
                            $('#Tr' + This.value).css("background-color", "");
                            $('#Strong' + This.value).hide()
                        }
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);

                    if (Checked_Val) {
                        $('#' + ID_VAL).prop("checked", false)
                    }
                    else {
                        $('#' + ID_VAL).prop("checked", true)
                    }
                }
            });

        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Lottery", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function FunSort(SortName) {

           $('#@Html.IdFor(m=>m.Search.OrdercColumn)').val(SortName);
            $(targetFormID).attr("action", "@Url.Action("LotteryDetails", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function PrintBooK() {
            $('#tbData').printThis();
        }

        function fn_save_Excel() {
            var blob = new Blob([document.getElementById('tbData').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });
            var strFile = "Report.xls";
            saveAs(blob, strFile);
            return false;
        }
    </script>
}