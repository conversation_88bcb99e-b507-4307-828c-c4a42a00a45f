﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameTeamStatusIndexViewModel
    {
        public ADDT26 GameInfo { get; set; }

        /// <summary>
        /// 查詢ID URL 傳入
        /// </summary>
        public string GAME_NO { get; set; }

        /// <summary>
        /// 人
        /// </summary>
        public List<ADDT27> Teams { get; set; }

        /// <summary>
        /// 一般 關卡
        /// </summary>
        public List<ADDT26_D> GameInfoDs { get; set; }

        /// <summary>
        /// 一般 刷卡
        /// </summary>
        public List<ADDT28> GameLogs { get; set; }

        /// <summary>
        /// 有獎題目
        /// </summary>
        public List<ADDT26_Q> GameInfoQDs { get; set; }

        /// <summary>
        /// 有獎回答 刷卡
        /// </summary>
        public List<ADDT26_DAns> GameAnsLogs { get; set; }
        public List<GameTeamStatusRankList> GameTeamRankList { get; set; }




    }
}