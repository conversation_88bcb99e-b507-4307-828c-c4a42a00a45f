﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'ka', {
	find: 'ძებნა',
	findOptions: 'Find Options',
	findWhat: 'საძიებელი ტექსტი:',
	matchCase: 'დიდი და პატარა ასოების დამთხვევა',
	matchCyclic: 'დოკუმენტის ბოლოში გასვლის მერე თავიდან დაწყება',
	matchWord: 'მთელი სიტყვის დამთხვევა',
	notFoundMsg: 'მითითებული ტექსტი არ მოიძებნა.',
	replace: 'შეცვლა',
	replaceAll: 'ყველას შეცვლა',
	replaceSuccessMsg: '%1 მოძებნილი შეიცვალა.',
	replaceWith: 'შეცვლის ტექსტი:',
	title: 'ძებნა და შეცვლა'
} );
