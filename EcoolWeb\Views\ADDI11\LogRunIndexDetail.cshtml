﻿@model ADDI11IndexViewModel
@{
    /**/
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    string UUIDstr = "N";
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {

        UUIDstr = "Y";

    }
    string ISTASKLISTSTR = "";
    ViewBag.Title = "運動撲滿 - 感應圈數紀錄";
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    //Double Max_LAP_M = Math.Floor(Convert.ToDouble((2000.0 / Model.ONE_LAP_M) * 10)) / 10;
}
<style type="text/css">
    .css-table {
        display: table;
        border-collapse: collapse;
        width: 100%
    }

        .css-table .thead {
            display: table-header-group;
        }

        .css-table .tbody {
            display: table-row-group;
        }

        .css-table .tr {
            display: table-row;
            padding-bottom: 2px
        }

        .css-table .th, .css-table .td {
            display: table-cell;
            padding-left: 3px;
        }

        .css-table .th, .css-table .td_title {
            display: table-cell;
            padding-left: 3px;
            border: 1px solid #cccccc;
            color: black;
            background-color: chartreuse;
            text-align: center;
            line-height: 28px;
        }

    .input-group-btn {
        position: relative;
    }
</style>

@Html.Partial("_Notice")

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@if ("@UUIDstr" == "Y")
{
    ISTASKLISTSTR = "True";
}
@{ Html.RenderAction("_RunMenu", new { NowAction = "LogRunIndexDetail" }); }


@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{


    @Html.HiddenFor(m => m.WhereUserType)
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.RunDate)
    @Html.HiddenFor(m => m.LAP)
    @Html.HiddenFor(m => m.ONE_LAP_M)
    <div id="editorRows" class="tbody">
        @Html.Action("_LogRunIndexDetail", (string)ViewBag.BRE_NO)
    </div>
    <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
            <br />


            <h3 style="color:#80b4fb">讀取中…</h3>

        </div>
    </div>
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>


}
<script language="JavaScript">
        var targetFormID = '#form1';
        function deleteRow(user_no, runDate,Lap,Run_ID) {
            var result = confirm('確認要刪除嗎?');

            var data = {
                "USER_NO": user_no,
                "RunDtae": runDate,
                "Lap": Lap,
                "Run_ID": Run_ID
            };
            if (result) {

                $.ajax({

                    url: '@Url.Action("ADDRunDelete", (string)ViewBag.BRE_NO)',
                    data: data,
                    cache: false,
                    contentType: 'json',
                    success: function (hdt) {
                        console.log(hdt);
                        var res = $.parseJSON(hdt);
                        if (res.Success == "flase") {

                            $.ajax({

                                url: '@Url.Action("_LogRunIndexDetail", (string)ViewBag.BRE_NO)',
                                data: data,
                                cache: false,
                                success: function (html) {
                                    $("#editorRows").html('');
                                    $("#editorRows").prepend(html);
                                }


                            });
                            $('#ErrorStr').html(res.Name + '紀錄已刪除…');
                            $('#ErrorDiv').show();
                            setTimeout(function () {


                                $('#ErrorDiv').hide()
                            }, 800);
                        }
                    }
                });
            }
    }
      function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

        function doMonthTop(val)
        {
            $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);


                 $("#Sdate").attr("hidden", "hidden");

                FunPageProc(1)
            }


        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }



        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_LogRunIndexDetail", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#editorRows').html(data);
                }
            });
        }

    function todoClear() {
        ////重設

        $("#Q_Div").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        FunPageProc(1);
    }
      $(function () {
            var MonthDEF = "";
            MonthDEF = $(".btn-pink.active").attr("Mouth");
            if (MonthDEF == "true") {
                $("#Sdate").attr("hidden", "hidden");
            }
                $('#ButtonExcel').click(function () {
                   // $('#@Html.IdFor(m=>m.IsToExcel)').val(true)
                    var blob = new Blob([document.getElementById('tbData').innerHTML], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                    });
                    var strFile = "Report.xls";
                    saveAs(blob, strFile);
                    return false;
                });
                initDatepicker();
            });
</script>
