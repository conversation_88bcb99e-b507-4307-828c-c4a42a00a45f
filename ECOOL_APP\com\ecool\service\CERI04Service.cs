﻿using com.ecool.service;
using Dapper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.util;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;


namespace ECOOL_APP.EF
{
    public class CERI04Service
    {
        #region 列表資料

        public CERI04IndexViewModel GetListData(CERI04IndexViewModel model, UserProfile user, ref ECOOL_DEVEntities db,string Action_id)
        {
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(CERI04ListViewModel.TYPE_NAME);
                model.SortType = PageGlobal.SortType.ASC;
            }

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            string sSQL = $@"select B.ACCREDITATION_ID,B.ACCREDITATION_TYPE , B.IsText , C.TYPE_NAME,B.ACCREDITATION_NAME ,D.SCHOOL_NO,D.GRADE,D.CLASS_NO,BD.ITEM_NO,BD.SUBJECT,BD.CONTENT
	                          ,case when (select count(*) from HRMT01 PH (NOLOCK)
					                              JOIN CERT05 P (NOLOCK) on  P.SCHOOL_NO=PH.SCHOOL_NO and P.USER_NO=PH.USER_NO and P.ACCREDITATION_ID=BD.ACCREDITATION_ID and P.ITEM_NO=BD.ITEM_NO
					                              where PH.SCHOOL_NO=D.SCHOOL_NO and PH.CLASS_NO=D.CLASS_NO and PH.USER_TYPE='{UserType.Student}' and PH.USER_STATUS!={UserStaus.Invalid} ) > 0 then (select Top 1 V.VERIFIER_NAME+'-'+ CONVERT(nvarchar(10), V.VERIFIED_DATE,111)
                                from CERT04_V_M V (NOLOCK)
                                where  V.ACCREDITATION_ID =BD.ACCREDITATION_ID and V.ITEM_NO=BD.ITEM_NO and V.SCHOOL_NO=D.SCHOOL_NO and V.CLASS_NO=D.CLASS_NO
                              
                                order by V.CHG_DATE desc
                                )  else  null end as VERIFIER_STR
	                            ,(select count(*) from HRMT01 H (NOLOCK) where H.SCHOOL_NO=D.SCHOOL_NO and H.CLASS_NO=D.CLASS_NO and H.USER_TYPE='{UserType.Student}' and H.USER_STATUS!={UserStaus.Invalid} ) as CLASS_COUNT
	                            ,(select count(*) from HRMT01 PH (NOLOCK)
					                              JOIN CERT05 P (NOLOCK) on  P.SCHOOL_NO=PH.SCHOOL_NO and P.USER_NO=PH.USER_NO and P.ACCREDITATION_ID=BD.ACCREDITATION_ID and P.ITEM_NO=BD.ITEM_NO
					                              where PH.SCHOOL_NO=D.SCHOOL_NO and PH.CLASS_NO=D.CLASS_NO and PH.USER_TYPE='{UserType.Student}' and PH.USER_STATUS!={UserStaus.Invalid} ) CLASS_PASS_COUNT
	                            from CERT03_G A (NOLOCK)
	                            JOIN CERT02 B (NOLOCK)  on A.ACCREDITATION_ID  =B.ACCREDITATION_ID and B.DEL_YN='{SharedGlobal.N}'
                                JOIN CERT02_D BD (NOLOCK) on BD.ACCREDITATION_ID = B.ACCREDITATION_ID and a.ITEM_NO=BD.ITEM_NO
                                JOIN CERT01 C  (NOLOCK) on B.ACCREDITATION_TYPE =C.TYPE_ID  and B.DEL_YN='{SharedGlobal.N}'
	                            JOIN (SELECT H.SCHOOL_NO,H.GRADE,H.CLASS_NO FROM HRMT01 H (NOLOCK) where H.SCHOOL_NO=@SCHOOL_NO and USER_STATUS!=9 and USER_TYPE='S'
                                      Group by H.SCHOOL_NO,H.GRADE,H.CLASS_NO) D on A.SCHOOL_NO =D.SCHOOL_NO and A.GRADE =D.GRADE
	                            Where A.SCHOOL_NO=@SCHOOL_NO and a.SEMESTER={Semesters}";

            var temp = db.Database.Connection.Query<CERI04ListViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = user?.SCHOOL_NO,
             }).AsQueryable();

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_NAME))
            {
                temp = temp.Where(a => a.ACCREDITATION_ID == model.WhereACCREDITATION_NAME);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
            {
                temp = temp.Where(a => a.ACCREDITATION_TYPE == model.WhereACCREDITATION_TYPE);
            }

            if (model.WhereGRADE != null)
            {
                temp = temp.Where(a => a.GRADE == model.WhereGRADE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                temp = temp.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereSUBJECT))
            {
                temp = temp.Where(a => a.SUBJECT.Contains(model.WhereSUBJECT.Trim()));
            }

            if (model.WhereIsUnVerifier == true)
            {
                temp = temp.Where(a => a.VERIFIER_STR == null);
            }

            if (HRMT24_ENUM.CheckQAdmin(user) == false)
            {
                List<string> Accreditations = new List<string>();

                if (user != null)
                {
                    if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
                    {
                        temp = temp.Where(a => a.CLASS_NO == user.TEACH_CLASS_NO);
                    }
                    if (PermissionService.GetPermission_Use_YN("CERI04", Action_id, user.SCHOOL_NO, user.USER_NO)=="Y") {

                        temp = temp;

                    }
                    else
                    {
                        Accreditations.AddRange(db.CERT02_V.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Select(a => a.ACCREDITATION_ID).AsNoTracking().ToListNoLock());

                        if ((Accreditations?.Count ?? 0) == 0)
                        {
                            Accreditations.Add("");
                        }

                        temp = temp.Where(a => Accreditations.Contains(a.ACCREDITATION_ID));
                    }
                }
                else
                {
                    if ((Accreditations?.Count ?? 0) == 0)
                    {
                        Accreditations.Add("");
                    }

                    temp = temp.Where(a => Accreditations.Contains(a.ACCREDITATION_ID));
                }
            }

            //List<GridSort> gridsorts = new List<GridSort>();
            //gridsorts.Add(new GridSort() { Field = model.OrderByColumnName, Dir = model.SortType.ToString() });

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.TYPE_NAME))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.TYPE_NAME), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.ACCREDITATION_NAME))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.ACCREDITATION_NAME), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.GRADE))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.GRADE), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.CLASS_NO))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.CLASS_NO), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            // temp = temp.MultipleSort(gridsorts);

            // temp.OrderBy(sortBy + (reverse ? " descending" : ""));

            temp = temp.OrderBy(model.OrderByColumnName, model.SortType);
            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);

            model.ListData.Select(a =>
            {
                a.VERIFIER = !string.IsNullOrWhiteSpace(a.VERIFIER_STR) ? a.VERIFIER_STR.Substring(0, a.VERIFIER_STR.IndexOf("-")) : string.Empty;
                a.VERIFIED_DATE = !string.IsNullOrWhiteSpace(a.VERIFIER_STR) ? a.VERIFIER_STR.Substring((a.VERIFIER_STR.IndexOf("-") + 1)) : string.Empty;

                return a;
            }).ToList();

            model.Page = model.ListData.PageNumber;

            return model;
        }

        #endregion 列表資料


        #region 列表資料

        public CERI04IndexViewModel GetListData1(CERI04IndexViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(CERI04ListViewModel.TYPE_NAME);
                model.SortType = PageGlobal.SortType.ASC;
            }

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            string sSQL = $@"select *from ( select B.ACCREDITATION_ID,B.ACCREDITATION_TYPE , B.IsText , C.TYPE_NAME,B.ACCREDITATION_NAME ,D.SCHOOL_NO,D.GRADE,D.CLASS_NO,BD.ITEM_NO,BD.SUBJECT,BD.CONTENT
	                          ,case when (select count(*) from HRMT01 PH (NOLOCK)
					                              JOIN CERT05 P (NOLOCK) on  P.SCHOOL_NO=PH.SCHOOL_NO and P.USER_NO=PH.USER_NO and P.ACCREDITATION_ID=BD.ACCREDITATION_ID and P.ITEM_NO=BD.ITEM_NO
					                              where PH.SCHOOL_NO=D.SCHOOL_NO and PH.CLASS_NO=D.CLASS_NO and PH.USER_TYPE='{UserType.Student}' and PH.USER_STATUS!={UserStaus.Invalid} ) > 0 then (select Top 1 V.VERIFIER_NAME+'-'+ CONVERT(nvarchar(10), V.VERIFIED_DATE,111)
                                from CERT04_V_M V (NOLOCK)
                                where  V.ACCREDITATION_ID =BD.ACCREDITATION_ID and V.ITEM_NO=BD.ITEM_NO and V.SCHOOL_NO=D.SCHOOL_NO and V.CLASS_NO=D.CLASS_NO
                              
                                order by V.CHG_DATE desc
                                )  else  null end as VERIFIER_STR
	                            ,(select count(*) from HRMT01 H (NOLOCK) where H.SCHOOL_NO=D.SCHOOL_NO and H.CLASS_NO=D.CLASS_NO and H.USER_TYPE='{UserType.Student}' and H.USER_STATUS!={UserStaus.Invalid} ) as CLASS_COUNT
	                            ,(select count(*) from HRMT01 PH (NOLOCK)
					                              JOIN CERT05 P (NOLOCK) on  P.SCHOOL_NO=PH.SCHOOL_NO and P.USER_NO=PH.USER_NO and P.ACCREDITATION_ID=BD.ACCREDITATION_ID and P.ITEM_NO=BD.ITEM_NO
					                              where PH.SCHOOL_NO=D.SCHOOL_NO and PH.CLASS_NO=D.CLASS_NO and PH.USER_TYPE='{UserType.Student}' and PH.USER_STATUS!={UserStaus.Invalid} ) CLASS_PASS_COUNT
	                            from CERT03_G A (NOLOCK)
	                            JOIN CERT02 B (NOLOCK)  on A.ACCREDITATION_ID  =B.ACCREDITATION_ID and B.DEL_YN='{SharedGlobal.N}'
                                JOIN CERT02_D BD (NOLOCK) on BD.ACCREDITATION_ID = B.ACCREDITATION_ID and a.ITEM_NO=BD.ITEM_NO
                                JOIN CERT01 C  (NOLOCK) on B.ACCREDITATION_TYPE =C.TYPE_ID  and B.DEL_YN='{SharedGlobal.N}'
	                            JOIN (SELECT H.SCHOOL_NO,H.GRADE,H.CLASS_NO FROM HRMT01 H (NOLOCK) where H.SCHOOL_NO=@SCHOOL_NO and USER_STATUS!=9 and USER_TYPE='S'
                                      Group by H.SCHOOL_NO,H.GRADE,H.CLASS_NO) D on A.SCHOOL_NO =D.SCHOOL_NO 
	                            Where A.SCHOOL_NO=@SCHOOL_NO and a.SEMESTER={Semesters} and A.GRADE <=D.GRADE ) k
                               	group by ACCREDITATION_ID,ACCREDITATION_TYPE , IsText , TYPE_NAME,ACCREDITATION_NAME ,SCHOOL_NO,GRADE,CLASS_NO,ITEM_NO,SUBJECT,CONTENT,VERIFIER_STR,CLASS_COUNT,CLASS_PASS_COUNT ";


            var temp = db.Database.Connection.Query<CERI04ListViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = user?.SCHOOL_NO,
             }).AsQueryable();

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_NAME))
            {
                temp = temp.Where(a => a.ACCREDITATION_ID == model.WhereACCREDITATION_NAME);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
            {
                temp = temp.Where(a => a.ACCREDITATION_TYPE == model.WhereACCREDITATION_TYPE);
            }

            if (model.WhereGRADE != null)
            {
                temp = temp.Where(a => a.GRADE == model.WhereGRADE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                temp = temp.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereSUBJECT))
            {
                temp = temp.Where(a => a.SUBJECT.Contains(model.WhereSUBJECT.Trim()));
            }

            if (model.WhereIsUnVerifier == true)
            {
                temp = temp.Where(a => a.VERIFIER_STR == null);
            }

            if (HRMT24_ENUM.CheckQAdmin(user) == false)
            {
                List<string> Accreditations = new List<string>();

                if (user != null)
                {
                    if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
                    {
                        temp = temp.Where(a => a.CLASS_NO == user.TEACH_CLASS_NO);

                    
                    }
                    else
                    {
                        Accreditations.AddRange(db.CERT02_V.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Select(a => a.ACCREDITATION_ID).AsNoTracking().ToListNoLock());

                        if ((Accreditations?.Count ?? 0) == 0)
                        {
                            Accreditations.Add("");
                        }

                        temp = temp.Where(a => Accreditations.Contains(a.ACCREDITATION_ID));
                    }
                }
                else
                {
                    if ((Accreditations?.Count ?? 0) == 0)
                    {
                        Accreditations.Add("");
                    }


                    if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
                    {

                        temp = temp.Where(a => Accreditations.Contains(a.ACCREDITATION_ID));

                    }
                }
            }
            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
            {

                temp = temp.Where(a => a.ACCREDITATION_TYPE== model.WhereACCREDITATION_TYPE);

            }
            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_NAME)) {

                temp = temp.Where(a => a.ACCREDITATION_ID == model.WhereACCREDITATION_NAME);
            }

            //List<GridSort> gridsorts = new List<GridSort>();
            //gridsorts.Add(new GridSort() { Field = model.OrderByColumnName, Dir = model.SortType.ToString() });

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.TYPE_NAME))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.TYPE_NAME), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.ACCREDITATION_NAME))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.ACCREDITATION_NAME), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.GRADE))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.GRADE), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            //if (model.OrderByColumnName != nameof(CERI04ListViewModel.CLASS_NO))
            //{
            //    gridsorts.Add(new GridSort() { Field = nameof(CERI04ListViewModel.CLASS_NO), Dir = PageGlobal.SortType.ASC.ToString() });
            //}

            // temp = temp.MultipleSort(gridsorts);

            // temp.OrderBy(sortBy + (reverse ? " descending" : ""));

            temp = temp.OrderBy(model.OrderByColumnName, model.SortType);
            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);

            model.ListData.Select(a =>
            {
                a.VERIFIER = !string.IsNullOrWhiteSpace(a.VERIFIER_STR) ? a.VERIFIER_STR.Substring(0, a.VERIFIER_STR.IndexOf("-")) : string.Empty;
                a.VERIFIED_DATE = !string.IsNullOrWhiteSpace(a.VERIFIER_STR) ? a.VERIFIER_STR.Substring((a.VERIFIER_STR.IndexOf("-") + 1)) : string.Empty;

                return a;
            }).ToList();

            model.Page = model.ListData.PageNumber;

            return model;
        }

        #endregion 列表資料
        #region 取得編輯資料

        public CERI04EditViewModel GetEditData(CERI04EditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                IQueryable<CERI04EditDetailViewModel> Details = GetSQLforDetail(model.ThisSCHOOL_NO, model.ThisCLASS_NO, model.ThisACCREDITATION_ID, model.ThisITEM_NO, db);

                model.Details = Details.ToList();

                GetQdata(model, db);
            }

            return model;
        }

        private static void GetQdata(CERI04EditViewModel model, ECOOL_DEVEntities db)
        {
            model.QData = new CERI04ListViewModel();

            var eERT02 = db.CERT02.Where(a => a.ACCREDITATION_ID == model.ThisACCREDITATION_ID).FirstOrDefault();

            if (eERT02 != null)
            {
                var eERT01 = db.CERT01.Where(a => a.TYPE_ID == eERT02.ACCREDITATION_TYPE).FirstOrDefault();

                var eERT02_D = db.CERT02_D.Where(a => a.ACCREDITATION_ID == model.ThisACCREDITATION_ID && a.ITEM_NO == model.ThisITEM_NO).FirstOrDefault();

                if (eERT01 != null && eERT02_D != null)
                {
                    model.QData.ACCREDITATION_ID = eERT02.ACCREDITATION_ID;
                    model.QData.ACCREDITATION_NAME = eERT02.ACCREDITATION_NAME;
                    model.QData.ACCREDITATION_TYPE = eERT02.ACCREDITATION_TYPE;
                    model.QData.CLASS_NO = model.ThisCLASS_NO;
                    model.QData.CONTENT = eERT02_D.CONTENT;
                    model.QData.GRADE = model.ThisGRADE;
                    model.QData.ITEM_NO = model.ThisITEM_NO;
                    model.QData.SCHOOL_NO = model.ThisSCHOOL_NO;
                    model.QData.SUBJECT = eERT02_D.SUBJECT;
                    model.QData.TYPE_NAME = eERT01.TYPE_NAME;
                    model.QData.CASH = (short)eERT01.CASH;
                }
            }
        }

        private static IQueryable<CERI04EditDetailViewModel> GetSQLforDetail(string SCHOOL_NO, string CLASS_NO, string ACCREDITATION_ID, string ITEM_NO, ECOOL_DEVEntities db)
        {
            string sSQL = $@" 	Select H.SCHOOL_NO,H.USER_NO,H.NAME,H.SNAME,H.CLASS_NO,H.SEAT_NO,(Case When isnull(P.USER_NO,'')<>'' Then 1 else 0 End) O_IS_PASS,P.CASH as O_CASH
                                    ,(Case When isnull(P.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS , (select top 1 IsText from CERT02 where ACCREDITATION_ID= @ACCREDITATION_ID ) as IsText
                                    
                                      , sb.word as PersonText
                                    from HRMT01 H (NOLOCK)
                                   -- left join CERT04_V_M VM (NOLOCK) on VM.ACCREDITATION_ID = @ACCREDITATION_ID and VM.SCHOOL_NO = H.SCHOOL_NO and VM.CLASS_NO=@CLASS_NO
                                    --left join CERT04_V_D  VD (NOLOCK)  on VD.A_VERIFIER_ID = VM.A_VERIFIER_ID and VD.SCHOOL_NO =  H.SCHOOL_NO and VD.USER_NO = H.USER_NO
                                    left join CERT05 P (NOLOCK) on H.SCHOOL_NO = P.SCHOOL_NO and H.USER_NO = P.USER_NO and P.ACCREDITATION_ID=@ACCREDITATION_ID and P.ITEM_NO=@ITEM_NO
                                     	OUTER apply dbo.udf_Split(P.PersonText,',') sb                                    
                                           Where H.SCHOOL_NO=@SCHOOL_NO and H.CLASS_NO=@CLASS_NO
                                    and H.USER_TYPE='{UserType.Student}' and H.USER_STATUS!={UserStaus.Invalid}
                                    order by H.SCHOOL_NO,H.CLASS_NO,H.SEAT_NO";
            var Details = db.Database.Connection.Query<CERI04EditDetailViewModel>(sSQL
                 , new
                 {
                     SCHOOL_NO,
                     CLASS_NO,
                     ACCREDITATION_ID,
                     ITEM_NO,
                 }).AsQueryable();

            return Details;
        }

        #endregion 取得編輯資料

        #region Save處理

        public IResult SaveEditData(CERI04EditViewModel model, UserProfile user, ref ECOOL_DEVEntities db,ref List<Tuple<string, string, int>> valuesList)
        {
            IResult result = new Result(false);

            try
            {
                var IsText = "";
                IsText = db.CERT02.Where(x => x.ACCREDITATION_ID == model.ThisACCREDITATION_ID).Select(x => x.IsText).FirstOrDefault();
                using (TransactionScope tx = new TransactionScope())
                {
                    int SYear = SysHelper.GetNowSYear(DateTime.Now);

                    CERT04_V_M SaveUp = new CERT04_V_M();

                    SaveUp.A_VERIFIER_ID = Guid.NewGuid().ToString("N");
                    SaveUp.ACCREDITATION_ID = model.ThisACCREDITATION_ID;
                    SaveUp.ITEM_NO = model.ThisITEM_NO;
                    SaveUp.VERIFIER_SCHOOL_NO = user.SCHOOL_NO;
                    SaveUp.VERIFIER_USER_NO = user.USER_NO;
                    SaveUp.VERIFIER_NAME = user.NAME;
                    SaveUp.VERIFIED_DATE = DateTime.Now;
                    SaveUp.SCHOOL_NO = model.ThisSCHOOL_NO;
                    SaveUp.GRADE = model.ThisGRADE;
               
                    SaveUp.CLASS_NO = model.ThisCLASS_NO;
                    SaveUp.SYEAR = (byte)SYear;
                    SaveUp.CHG_PERSON = user?.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;

                    db.CERT04_V_M.Add(SaveUp);

                    //明細
                    if (model.Details?.Count() > 0)
                    {
                        List<CERT04_V_D> cERT04_V_Ds = new List<CERT04_V_D>();
                        List<CERT05> CrePass = new List<CERT05>();

                        var NowData = GetSQLforDetail(SaveUp.SCHOOL_NO, SaveUp.CLASS_NO, SaveUp.ACCREDITATION_ID, SaveUp.ITEM_NO, db).ToList();

                        GetQdata(model, db);
                        List<string> USER_NO = new List<string>();
                        USER_NO = model.Details.Select(x => x.USER_NO).Distinct().ToList();
                        string ARRASWER = "";
                        foreach (var items in USER_NO) {
                            bool doubelstr = false;
                            if (model.Details.Where(x => x.USER_NO == items).Count() > 1) {
                                doubelstr = true;
                                ARRASWER = String.Join(",", model.Details.Where(x => x.USER_NO == items && x.PersonText!=null).Select(x=>x.PersonText).ToList());
                             
                            }
                            var item = model.Details.Where(x => x.USER_NO == items && x.CLASS_NO != null &&x.SCHOOL_NO!=null).FirstOrDefault();


                     //   foreach (var item in model.Details.Where(x=>x.USER_NO== items && x.CLASS_NO!=null).FirstOrDefault())
                     //   {
                            CERT04_V_D CreD = new CERT04_V_D();

                            CreD.A_VERIFIER_ID = SaveUp.A_VERIFIER_ID;
                            CreD.SCHOOL_NO = item.SCHOOL_NO;
                            CreD.USER_NO = item.USER_NO;
                            CreD.O_IS_PASS = NowData.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).Select(a => a.O_IS_PASS).FirstOrDefault() ?? false;
                            CreD.IS_PASS = item.IS_PASS;
                                if (doubelstr)
                                {
                                    CreD.PersonText = ARRASWER ?? "";
                                item.PersonText = ARRASWER;

                                }
                                else {

                                    CreD.PersonText = item.PersonText ?? "";
                                }
                           
                            bool IsADDCASH = false;
                            if (IsText != "" && IsText != null && IsText == "Y" && item.PersonText!=null&&item.PersonText.Trim().Length>0)
                            {
                                int CheckCERT05 = 0;

                                CheckCERT05 = db.CERT05.Where(x => x.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && x.ITEM_NO == SaveUp.ITEM_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.USER_NO == item.USER_NO).Count();
                                if (CheckCERT05 == 0)
                                {
                                    CreD.O_IS_PASS = false;
                                    CreD.IS_PASS = true;
                                }
                                else
                                {
                                    CERT05 cERT = new CERT05();
                                    cERT = db.CERT05.Where(b => b.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && b.ITEM_NO == SaveUp.ITEM_NO && b.SCHOOL_NO == item.SCHOOL_NO && b.USER_NO == item.USER_NO).FirstOrDefault();
                                    db.Entry(cERT).State = System.Data.Entity.EntityState.Deleted;

                                    //EFBatchOperation.For(db, db.CERT05).Where(b => b.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && b.ITEM_NO == SaveUp.ITEM_NO && b.SCHOOL_NO == item.SCHOOL_NO && b.USER_NO == item.USER_NO).Delete();
                                    CreD.O_IS_PASS = false;
                                    CreD.IS_PASS = true;
                                    IsADDCASH = true;
                                }
                            }
                            if (IsText != "" && IsText != null && IsText == "Y" && item.PersonText != null && item.PersonText.Trim().Length == 0)
                            {

                              
                                    CreD.O_IS_PASS = true;
                                CreD.IS_PASS = false;
                            }

                            cERT04_V_Ds.Add(CreD);
                            db.CERT04_V_D.Add(CreD);
                            if (CreD.O_IS_PASS == false && CreD.IS_PASS == true) //通過
                            {
                                CERT05 CreP = new CERT05();
                                CreP.ACCREDITATION_ID = SaveUp.ACCREDITATION_ID;
                                CreP.ITEM_NO = SaveUp.ITEM_NO;
                                CreP.SCHOOL_NO = item.SCHOOL_NO;
                                CreP.USER_NO = item.USER_NO;
                                CreP.CASH = model.QData.CASH;
                                CreP.PersonText = item.PersonText ?? "";
                                    if (doubelstr)
                                    {
                                        CreP.PersonText = ARRASWER ?? "";
                                    item.PersonText = ARRASWER;
                                }
                                    else
                                    {

                                        CreP.PersonText = item.PersonText ?? "";
                                    }
                                    CrePass.Add(CreP);
                                db.CERT05.Add(CreP);
                                if (model.QData.CASH > 0 && IsADDCASH==false)
                                {
                                    ECOOL_APP.CashHelper.AddCash(user, model.QData.CASH, item.SCHOOL_NO, item.USER_NO, "CERT05", SaveUp.A_VERIFIER_ID, $"護照:{model.QData.TYPE_NAME}-{model.QData.ACCREDITATION_NAME}({model.QData.SUBJECT})  通過，獲得 {model.QData.CASH} 點", true, ref db,"", "",ref valuesList);
                                }
                            }
                            else if (CreD.O_IS_PASS == true && (CreD.IS_PASS ?? false) == false) //取消
                            {
                                //EFBatchOperation.For(db, db.CERT05).Where(b => b.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && b.ITEM_NO == SaveUp.ITEM_NO && b.SCHOOL_NO == item.SCHOOL_NO && b.USER_NO == item.USER_NO).Delete();
                                CERT05 cERT = new CERT05();
                                cERT = db.CERT05.Where(b => b.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && b.ITEM_NO == SaveUp.ITEM_NO && b.SCHOOL_NO == item.SCHOOL_NO && b.USER_NO == item.USER_NO).FirstOrDefault();
                                db.Entry(cERT).State = System.Data.Entity.EntityState.Deleted;
                                var O_CASH = (NowData.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).Select(a => a.O_CASH).FirstOrDefault() ?? 0) * -1;

                                if (O_CASH < 0)
                                {
                                    ECOOL_APP.CashHelper.AddCash(user, O_CASH, item.SCHOOL_NO, item.USER_NO, "CERT05", SaveUp.A_VERIFIER_ID, $"取消 原「護照:{model.QData.TYPE_NAME}-{model.QData.ACCREDITATION_NAME}({model.QData.SUBJECT}) 通過」，扣 {O_CASH} 點", true, ref db,"", "",ref valuesList);
                                }
                            }
                       // }
                        }

                      //  EFBatchOperation.For(db, db.CERT04_V_D).InsertAll(cERT04_V_Ds);
                   

                        if (CrePass?.Count > 0)
                        {
                           // EFBatchOperation.For(db, db.CERT05).InsertAll(CrePass);
                        }
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        #endregion Save處理
    }
}