﻿@model RollCallEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

<script src="~/Scripts/timepicker/jquery-ui-sliderAccess.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.Keyword)
    @Html.HiddenFor(m => m.Main.ROLL_CALL_ID)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ROLL_CALL_NAME, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.ROLL_CALL_NAME, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "必填" } })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ROLL_CALL_DESC, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.TextAreaFor(m => m.Main.ROLL_CALL_DESC, 2, 100, new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Main.ROLL_CALL_DESC) })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_DESC, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ROLL_CALL_DATES, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.ROLL_CALL_DATES, new { htmlAttributes = new { @class = "form-control form-control-required", @type = "text", @placeholder = "必填" } })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_DATES, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ROLL_CALL_DATEE, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.ROLL_CALL_DATEE, new { htmlAttributes = new { @class = "form-control form-control-required", @type = "text", @placeholder = "必填" } })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_DATEE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.CASH, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.CASH, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "請填數字，最小可輸入0" } })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.CASH, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center">
            <hr />
            <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
                取消
            </button>
            <button type="button" class="btn btn-default" onclick="onSave()">
                <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
            </button>
            @if (Model.Main?.ROLL_CALL_ID != null)
            {
                <button type="button" class="btn btn-default" onclick="onDel()">
                    <span class="fa fa-trash" aria-hidden="true"></span>作廢
                </button>
            }
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#form1';

        function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onDel()
        {
            var OK = confirm("您確定要作廢?己經給點的，點數會扣回來喔!!")

            if (OK==true)
            {
               $(targetFormID).attr("action", "@Url.Action("Del", (string)ViewBag.BRE_NO)")
               $(targetFormID).submit();
            }
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("RollCallIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

    var opt = {
        showMonthAfterYear: true,
        format: moment().format('YYYY-MM-DD h:mm'),
        showSecond: true,
        showButtonPanel: true,
        showTime: true,
        beforeShow: function () {
            setTimeout(
                function () {
                    $('#ui-datepicker-div').css("z-index", 15);
                }, 100
            );
        },
        onSelect: function (dateText, inst) {
            $('#' + inst.id).attr('value', dateText);
        }
    };

    $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATES)").datetimepicker(opt);
    $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATEE)").datetimepicker(opt);

    if ($("#@Html.IdFor(m => m.Main.ROLL_CALL_DATES)").val()==''
        && $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATEE)").val()=='')
    {
        var Today = new Date();

        $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATES)").val(moment(Today).format('YYYY-MM-DD HH:mm'));
        $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATEE)").val(moment(Today).format('YYYY/MM/DD 17:00'));
    }
    </script>
}