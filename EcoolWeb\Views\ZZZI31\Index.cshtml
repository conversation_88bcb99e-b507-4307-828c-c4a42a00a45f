﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new {id= "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO)
    </div>
}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#form1';

        function todoClear() {
            ////重設

            $(targetFormID).find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            funAjax()
        }

        function FunPageProc(pageno) {

            $('#Search_Page').val(pageno);
            funAjax()
        }

        function FunSort(SortName) {

            OrderByName = $('#Search_OrderByName').val();
            SyntaxName = $('#Search_SyntaxName').val();

            if (OrderByName == SortName) {

                if (SyntaxName == "Desc") {
                    $('#Search_SyntaxName').val("ASC");
                }
                else {
                    $('#Search_SyntaxName').val("Desc");
                }
            }
            else {
                $('#Search_OrderByName').val(SortName);
                $('#Search_SyntaxName').val("Desc");
            }
            funAjax()
        }

        function btnSTATUS(val)
        {
            $('#Search_STATUS').val(val)
            funAjax()
        }


        function funAjax() {

            var data = {
                "Search.OrderByName": $('#Search_OrderByName').val(),
                "Search.SyntaxName": $('#Search_SyntaxName').val() ,
                "Search.Page": $('#Search_Page').val() ,
                "Search.SearchContents": $('#Search_SearchContents').val(),
                "Search.STATUS": $('#Search_STATUS').val()
            };


           


            $.ajax({
                url: '@Url.Action("_PageContent", (string)ViewBag.BRE_NO)',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function onAdd() {
            $('#Search_Q_QUESTIONS_ID').val("")
            form1.action = '@Url.Action("QUESTIONS", (string)ViewBag.BRE_NO)';
            form1.submit();
        }

        function onBtnLink(KEY_NO, VIEW_DATA_TYPE) {

            $('#uQAT16_QUESTIONS_ID').val(KEY_NO)
            $('#Search_Q_QUESTIONS_ID').val(KEY_NO)

            $('#VIEW_DATA_TYPE').val(VIEW_DATA_TYPE)


            form1.action = '@Html.Raw(@Url.Action("Details", (string)ViewBag.BRE_NO))'
            form1.submit();
        }
    </script>
}