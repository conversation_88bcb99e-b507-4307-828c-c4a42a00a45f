/**
 * jQuery TouchWipe Plugin
 * 簡化版的觸控滑動插件，用於支持左右滑動手勢
 * 
 * 使用方法:
 * $(element).touchwipe({
 *     wipeLeft: function() { ... },
 *     wipeRight: function() { ... },
 *     preventDefaultEvents: false
 * });
 */
(function($) {
    $.fn.touchwipe = function(options) {
        var settings = $.extend({
            min_move_x: 20,
            min_move_y: 20,
            wipeLeft: function() {},
            wipeRight: function() {},
            wipeUp: function() {},
            wipeDown: function() {},
            preventDefaultEvents: true
        }, options);

        return this.each(function() {
            var startX, startY, endX, endY;
            var $this = $(this);

            // 觸控開始
            $this.on('touchstart', function(e) {
                var touch = e.originalEvent.touches[0];
                startX = touch.pageX;
                startY = touch.pageY;
                endX = startX;
                endY = startY;
                
                if (settings.preventDefaultEvents) {
                    e.preventDefault();
                }
            });

            // 觸控移動
            $this.on('touchmove', function(e) {
                if (e.originalEvent.touches.length > 1) return;
                
                var touch = e.originalEvent.touches[0];
                endX = touch.pageX;
                endY = touch.pageY;
                
                if (settings.preventDefaultEvents) {
                    e.preventDefault();
                }
            });

            // 觸控結束
            $this.on('touchend', function(e) {
                var deltaX = endX - startX;
                var deltaY = endY - startY;
                
                // 檢查是否達到最小移動距離
                if (Math.abs(deltaX) >= settings.min_move_x || Math.abs(deltaY) >= settings.min_move_y) {
                    // 判斷主要移動方向
                    if (Math.abs(deltaX) > Math.abs(deltaY)) {
                        // 水平移動
                        if (deltaX > 0) {
                            settings.wipeRight.call(this);
                        } else {
                            settings.wipeLeft.call(this);
                        }
                    } else {
                        // 垂直移動
                        if (deltaY > 0) {
                            settings.wipeDown.call(this);
                        } else {
                            settings.wipeUp.call(this);
                        }
                    }
                }
                
                if (settings.preventDefaultEvents) {
                    e.preventDefault();
                }
            });

            // 支援滑鼠事件（用於桌面測試）
            var mouseDown = false;
            
            $this.on('mousedown', function(e) {
                mouseDown = true;
                startX = e.pageX;
                startY = e.pageY;
                endX = startX;
                endY = startY;
                
                if (settings.preventDefaultEvents) {
                    e.preventDefault();
                }
            });

            $this.on('mousemove', function(e) {
                if (!mouseDown) return;
                
                endX = e.pageX;
                endY = e.pageY;
                
                if (settings.preventDefaultEvents) {
                    e.preventDefault();
                }
            });

            $this.on('mouseup', function(e) {
                if (!mouseDown) return;
                mouseDown = false;
                
                var deltaX = endX - startX;
                var deltaY = endY - startY;
                
                // 檢查是否達到最小移動距離
                if (Math.abs(deltaX) >= settings.min_move_x || Math.abs(deltaY) >= settings.min_move_y) {
                    // 判斷主要移動方向
                    if (Math.abs(deltaX) > Math.abs(deltaY)) {
                        // 水平移動
                        if (deltaX > 0) {
                            settings.wipeRight.call(this);
                        } else {
                            settings.wipeLeft.call(this);
                        }
                    } else {
                        // 垂直移動
                        if (deltaY > 0) {
                            settings.wipeDown.call(this);
                        } else {
                            settings.wipeUp.call(this);
                        }
                    }
                }
                
                if (settings.preventDefaultEvents) {
                    e.preventDefault();
                }
            });
        });
    };
})(jQuery);
