/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/CombDiactForSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{8400:[760,-627,0,-453,-17],8401:[760,-627,0,-426,10],8402:[662,156,0,-300,-234],8406:[760,-548,0,-453,-17],8407:[760,-548,0,-453,-17],8411:[622,-523,0,-453,44],8412:[622,-523,0,-582,114],8413:[725,221,0,-723,223],8417:[760,-548,0,-453,25],8420:[1023,155,0,-970,490],8421:[662,156,0,-430,-24],8422:[662,156,0,-351,-86],8423:[725,178,0,-595,221],8424:[-119,218,0,-462,35],8425:[681,-538,0,-478,55],8426:[419,-87,0,-793,153],8428:[-119,252,0,27,463],8429:[-119,252,0,27,463],8430:[-40,252,0,-453,-17],8431:[-40,252,0,-453,-17]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/CombDiactForSymbols.js");
