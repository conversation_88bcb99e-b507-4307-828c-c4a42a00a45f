/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeTwoSym/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeTwoSym,{710:[777,-564,979,0,979],711:[777,-564,979,0,979],732:[760,-608,977,-2,977],759:[-117,269,977,-2,977],773:[820,-770,0,-1500,0],780:[777,-564,0,-1150,-171],816:[-117,269,0,-1152,-173],818:[-127,177,0,-1500,0],824:[662,0,0,-720,-6],8254:[820,-770,1500,0,1500],8400:[749,-584,0,-1323,-15],8401:[749,-584,0,-1323,-15],8406:[735,-482,0,-1323,-15],8407:[735,-482,0,-1323,-15],8428:[-123,288,0,-1323,-15],8429:[-123,288,0,-1323,-15],8430:[-26,279,0,-1323,-15],8431:[-26,279,0,-1323,-15],8731:[2056,404,1124,110,1157],8732:[2056,404,1124,110,1157],9140:[766,-544,1606,74,1532],9141:[139,83,1606,74,1532],9180:[66,147,1460,0,1460],9181:[785,-572,1460,0,1460],9184:[66,212,1886,0,1886],9185:[842,-564,1886,0,1886],10098:[1566,279,688,230,651],10099:[1566,279,688,37,458],10214:[1566,279,555,190,517],10215:[1566,279,555,38,365],10218:[1566,279,901,93,793],10219:[1566,279,901,108,808],10627:[1566,279,827,122,692],10628:[1565,280,827,135,705],10629:[1566,282,793,155,693],10630:[1566,282,793,100,638],11004:[1586,289,906,133,773],11007:[1586,289,636,133,503]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeTwoSym/Regular/All.js");
