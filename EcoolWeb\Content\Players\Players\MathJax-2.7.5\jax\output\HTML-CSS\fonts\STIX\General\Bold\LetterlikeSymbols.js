/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LetterlikeSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{8450:[691,19,727,45,672],8453:[688,12,873,38,835],8455:[691,19,699,65,662],8460:[701,205,843,42,795],8461:[676,0,768,75,693],8462:[685,10,576,50,543],8463:[685,10,576,50,543],8465:[701,25,790,54,735],8467:[699,14,500,43,632],8469:[676,0,738,75,663],8470:[691,18,1093,10,1042],8471:[691,19,747,26,721],8472:[541,219,850,55,822],8473:[676,0,700,75,670],8474:[691,64,797,45,747],8476:[701,25,884,50,841],8477:[676,0,783,75,758],8478:[676,101,722,26,726],8482:[676,-271,1000,24,977],8484:[691,0,777,52,727],8485:[676,205,448,21,424],8486:[692,0,758,35,723],8487:[674,18,758,35,723],8488:[701,195,755,44,703],8489:[475,0,312,9,244],8491:[920,0,722,9,689],8493:[701,19,773,54,731],8498:[676,0,616,48,546],8501:[694,34,766,76,690],8502:[694,34,703,60,659],8503:[694,34,562,71,493],8504:[694,34,599,40,559],8508:[461,11,804,55,759],8509:[486,203,646,23,624],8510:[676,0,497,75,643],8511:[676,0,768,75,693],8512:[773,269,976,36,952],8523:[690,17,833,61,788]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/LetterlikeSymbols.js");
