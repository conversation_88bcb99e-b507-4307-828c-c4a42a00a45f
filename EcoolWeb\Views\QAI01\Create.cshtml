﻿@model EcoolWeb.Models.QAI01CreateViewModel
@using EcoolWeb.Models;
@using ECOOL_APP.com.ecool.util;
@{

    ViewBag.Title = "新增觀看影片作業";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
</head>
<body>
    @if (AppMode)
    {
    }
    else
    { @Html.Partial("_Title_Secondary")
    }

    @using (Html.BeginForm("Create", "QAI01", FormMethod.Post, new { id = "QAI01", name = "form1" }))
    {

        <div class="panel-heading text-center" style="background-color:rgba(68, 157, 68, 1);color:rgba(249, 242, 244, 1);text-shadow:0px 0px 0px #dddddd">
            步驟1. 請選擇酷客雲影片
        </div>
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">影片名稱關鍵字</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
            @Html.HiddenFor(m => m.Page)
            @Html.HiddenFor(m => m.total_pages)

            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1);" />
        </div>
        <div>
            <div style="height:35px"></div>
            <div class="p-context">

                @if (Model.MediasList.Count == 0)
                {
                    <div class="text-center">
                        <label class="label_dd_font18">無任何影片</label>
                    </div>
                }

                <div class="row">
                    @{
                string divrowSt = "<div class='row-hidden'>";
                string divrowEd = "</div> <!--row-hidden End-->";
                int num = 0;

                foreach (COOC_Medias award in Model.MediasList)
                {
                    string aImgUrl = award.thumbnail;
                    num++;

                    @*if (num % 2 != 0)
                        {
                            @Html.Raw(HttpUtility.HtmlDecode(divrowSt))
                        }*@
                        <div class="col-md-6 col-sm-6 col-xs-12 col-height">
                            <div class="box">
                                <a class="btn-default btn btn-xs btn-prod" href='@award.contentLink' target="_blank">
                                    <img src='@aImgUrl' href="@aImgUrl" style="max-height:200px;">
                                </a>
                            </div>
                            <div class="prod-caption">
                                <div class="form-group">
                                    <label class="label_dd_font18" title="@award.id @award.name"> @StringHelper.LeftStringR(award.name, 13)</label>
                                    <br />
                                    <div class="prod-icon">
                                        <a class="btn-default btn btn-xs btn-prod" href='@award.contentLink' target="_blank">觀看</a>
                                        <a class="btn-default btn btn-xs btn-prod" href='@Url.Action("Create2", "QAI01", new { SelectMedias_id = award.id,SelectMedias_name=award.name, SelectMedias_contentLink = award.contentLink, SelectMedias_thumbnail=award.thumbnail })'>選擇影片</a>
                                    </div>
                                </div>

                            </div>

                        </div>

                        @*if (num % 2 == 0)
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(divrowEd))
                            }
                            if (num % 2 != 0)
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(divrowEd))
                            }*@
                }

                    }
                </div><!--/row-->

            </div>
        </div>
        <div class="form-inline" >
            @if (Model.Page > 1)
            {
                <input type="button" class="btn-yellow btn btn-sm" value="上一頁" onclick="FunPagePre();" />
            }
            @if (Model.Page < Model.total_pages)
            {
                <input type="button" class="btn-yellow btn btn-sm" value="下一頁" onclick="FunPageNext();" />
            }
            
        </div>
    }

    @section scripts{
        <script type="text/javascript">
            var targetFormID = '#QAI01';

            //var ww=jQuery(window).width();
            //alert(ww);

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };
            function FunPageNext() {
                if ($(targetFormID).size() > 0) {
                    var page = $('#Page').val();
                    page =( Number(page) + 1);
                    $('#Page').val(page);
                    $(targetFormID).submit();
                }
            };
            function FunPagePre() {
                if ($(targetFormID).size() > 0) {
                    var page = $('#Page').val();
                    page = (Number(page) - 1);
                    $('#Page').val(page);
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

            function doSearchBool(ColName, whereValue) {
                $("#" + ColName).val(!whereValue);
                FunPageProc(1)
            }



            function todoClear() {
                $("#doClear").val(true);
                $("#OrdercColumn").val('');
                $("#whereKeyword").val('');
                $("#whereUserNo").val('');
                $("#whereWritingStatus").val('');
                $("#whereShareYN").val('');
                $("#whereComment").val('');
                $("#whereCommentCash").val('');
                $("#whereCLASS_NO ").val('');
                $("#whereGrade ").val('');
                $("#Page").val(1);

                $(targetFormID).submit();
            }

            function Clearleft() {
                $("#whereShareYN").val('');
                $("#whereComment").val('');
                $("#whereCommentCash").val('');
            }

            function DoWRITING_STATUS(Value) {
                $('#whereWritingStatus').val(Value)
                FunPageProc(1)
            }

        </script>
    }




</body>
</html>
