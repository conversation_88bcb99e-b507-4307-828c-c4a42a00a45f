﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'sr', {
	access: 'Script Access', // MISSING
	accessAlways: 'Always', // MISSING
	accessNever: 'Never', // MISSING
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Abs доле',
	alignAbsMiddle: 'Abs средина',
	alignBaseline: 'Базно',
	alignTextTop: 'Врх текста',
	bgcolor: 'Боја позадине',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'Понављај',
	chkMenu: 'Укључи флеш мени',
	chkPlay: 'Аутоматски старт',
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'HSpace',
	properties: 'Особине Флеша',
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'Скалирај',
	scaleAll: 'Прикажи све',
	scaleFit: 'Попуни површину',
	scaleNoBorder: 'Без ивице',
	title: 'Особине флеша',
	vSpace: 'VSpace',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'Унесите УРЛ линка',
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
