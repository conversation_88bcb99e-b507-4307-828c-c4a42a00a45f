/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/GeometricShapes.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_AMS,{9632:[689,0,778,55,722],9633:[689,0,778,55,722],9650:[575,20,722,84,637],9651:[575,20,722,84,637],9654:[539,41,778,83,694],9660:[576,19,722,84,637],9661:[576,19,722,84,637],9664:[539,41,778,83,694],9674:[716,132,667,56,611]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/AMS/Regular/GeometricShapes.js");
