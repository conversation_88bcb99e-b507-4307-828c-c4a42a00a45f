﻿using com.ecool.service;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using EcoolWeb.ViewModels;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Web;
using System.Web.Mvc;
using static ECOOL_APP.EF.AWAT10;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWAI07Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "AWAI07";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        // 單頁可容納之資料筆數(可參數化此數值)
        private const int PageSize = 15;

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private AWAI07Service Service = new AWAI07Service();

        public ActionResult _BankMenu(string NowAction)
        {
            this.Shared();
            ViewBag.NowAction = NowAction;
            ViewBag.BankListPermision = PermissionService.GetPermission_Use_YN(Bre_NO, "BankList", SCHOOL_NO, USER_NO) == "Y";
            return PartialView();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Index()
        {
            this.Shared(Bre_Name + "-說明");

            var model = Service.GetDesc(SCHOOL_NO, ref db);
            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _RateView()
        {
            this.Shared(Bre_Name + "-說明");
            var model = Service.GetDesc(SCHOOL_NO, ref db);
            return View(model);
        }

        /// <summary>
        /// 模擬器Page
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Simulator()
        {
            var model = new AWAI07EditViewModel();
            ViewBag.PeriodTypeItem = Service.PeriodTypeItem(ref db);
            return View(model);
        }

        /// <summary>
        /// 學生定存清單頁面
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "BankList")] //檢查權限
        public ActionResult BankListView(AWAI07BankListViewModel model)
        {
            if (model == null) model = new AWAI07BankListViewModel();
            this.Shared();

            var bankList = db.AWAT10
                .Join(db.HRMT01, a => new { a.SCHOOL_NO, a.USER_NO }, h => new { h.SCHOOL_NO, h.USER_NO }, (a, h) => new
                {
                    a = a,
                    h = h
                })
                .Join(db.AWAT11, a10 => a10.a.PERIOD_TYPE, a11 => (byte)a11.PERIOD_TYPE, (a10, a11) => new
                {
                    a = a10.a,
                    h = a10.h,
                    p = a11
                })
                .Where(a => a.a.SCHOOL_NO == SCHOOL_NO && a.h.USER_TYPE == UserType.Student && a.h.USER_STATUS != UserStaus.Invalid)
                .Select(s => new AWAI07BankDetail
                {
                    A_NO = s.a.A_NO,
                    SCHOOL_NO = s.a.SCHOOL_NO,
                    USER_NO = s.a.USER_NO,
                    NAME = s.h.NAME,
                    GRADE = s.h.GRADE,
                    CLASS_NO = s.h.CLASS_NO,
                    CLOSE_DATE = s.a.CLOSE_DATE,
                    CLOSE_RATE = s.a.CLOSE_RATE,
                    INTEREST_RATE = s.a.INTEREST_RATE,
                    BANK_DATE = s.a.BANK_DATE,
                    PERIOD_DATES = s.a.PERIOD_DATES,
                    PERIOD_DATEE = s.a.PERIOD_DATEE,
                    PERIOD_TYPE = s.a.PERIOD_TYPE,
                    STATUS = s.a.STATUS,
                    ACCT_CODE = s.a.ACCT_CODE,
                    AMT = s.a.AMT,
                    MATURITY_TYPE = s.a.MATURITY_TYPE,
                    PERIOD_DESC = s.p.PERIOD_DESC
                });

            var classes = HRMT01.GetClassListData(SCHOOL_NO, Convert.ToString(model.whereGrade), model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == Convert.ToString(model.whereGrade) });

            // 這個年級找不到whereCLASS_NO 則歸null
            if (!classes.Any(c => c.Value == model.whereCLASS_NO))
            {
                model.whereCLASS_NO = null;
            }

            if (!string.IsNullOrEmpty(model.whereKeyword))
            {
                bankList = bankList.Where(b => b.USER_NO.Contains(model.whereKeyword) || b.NAME.Contains(model.whereKeyword));
            }
            if (!string.IsNullOrEmpty(model.whereGrade))
            {
                byte whereG = Byte.Parse(model.whereGrade);
                bankList = bankList.Where(b => b.GRADE == whereG);
            }
            if (!string.IsNullOrEmpty(model.whereCLASS_NO))
            {
                bankList = bankList.Where(b => b.CLASS_NO == model.whereCLASS_NO);
            }
            if (!string.IsNullOrEmpty(model.whereStatus))
            {
                bankList = bankList.Where(b => b.STATUS == model.whereStatus);
            }

            //  -- 排序開始 --
            Func<AWAI07BankDetail, object> sortExpression;
            switch (model.OrderColumn)
            {
                case "NAME":
                    sortExpression = (q => q.NAME);
                    break;

                case "CLASS_NO":
                    sortExpression = (q => q.CLASS_NO);
                    break;

                case "BANK_DATE":
                    sortExpression = (q => q.BANK_DATE);
                    break;

                case "ACCT_CODE":
                    sortExpression = (q => q.ACCT_CODE);
                    break;

                case "PERIOD_TYPE":
                    sortExpression = (q => q.PERIOD_TYPE);
                    break;

                case "INTEREST_RATE":
                    sortExpression = (q => q.INTEREST_RATE);
                    break;

                case "PERIOD_DATES":
                    sortExpression = (q => q.PERIOD_DATES);
                    break;

                case "PERIOD_DATEE":
                    sortExpression = (q => q.PERIOD_DATEE);
                    break;

                case "AMT":
                    sortExpression = (q => q.AMT);
                    break;

                case "STATUS":
                    sortExpression = (q => q.STATUS);
                    break;

                default:
                    sortExpression = (q => q.BANK_DATE);
                    model.SortBy = "DESC";
                    break;
            }

            if (model.SortBy == "ASC")
            {
                bankList = bankList.OrderBy(sortExpression).AsQueryable();
            }
            else
            {
                bankList = bankList.OrderByDescending(sortExpression).AsQueryable();
            }
            //  -- 排序結束 --

            model.BankDetailList = bankList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            // 狀態下拉
            string[] statesDisplay = new string[] { "1", "8" };
            var statusSelectList = typeof(StatusVal).GetProperties()
                .Where(pi => statesDisplay.Contains(pi.GetValue(null).ToString()))
                .Select(pi =>
                {
                    return new SelectListItem()
                    {
                        Text = StatusVal.GetDesc(pi.GetValue(null).ToString()),
                        Value = pi.GetValue(null).ToString()
                    };
                }).ToList();
            statusSelectList.Insert(0, new SelectListItem() { Text = "全部", Value = "" });

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            ViewBag.ClassItems = classes;
            ViewBag.StatusItems = statusSelectList;

            return View(model);
        }

        /// <summary>
        /// 模擬器是跑這支去計算利息
        /// </summary>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public JsonResult InterestRatesTest(byte? MATURITY_TYPE, byte? ACCT_CODE, byte? PERIOD_TYPE, decimal? AMT, DateTime? TestCloseDate)
        {
            this.Shared();
            bool Success = false;
            string Message = string.Empty;
            decimal? PrincipleAndInterestAmt = 0;
            double CloseRate = 0;
            string MathJaxFunction = string.Empty;

            try
            {
                Success = Service.GetPrincipleAndInterest((byte)MATURITY_TYPE, (byte)ACCT_CODE, (byte)PERIOD_TYPE, (Decimal)AMT, DateTime.Now, (DateTime)TestCloseDate,false ,ref db, ref Message, out CloseRate, out PrincipleAndInterestAmt, out MathJaxFunction);
            }
            catch (Exception ex)
            {
                Success = false;
                Message = "試算失敗，原因:" + ex.Message;
            }

            var data = new
            {
                Success = Success.ToString(),
                MathJaxFunction = MathJaxFunction,
                PrincipleAndInterestAmt = PrincipleAndInterestAmt,
                Error = Message
            };
            return Json(data, JsonRequestBehavior.AllowGet);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public JsonResult CloseOrderTest(string A_NO)
        {
            this.Shared();
            bool Success = false;
            string Message = string.Empty;
            decimal? PrincipleAndInterestAmt = 0;
            double CloseRate;

            try
            {
                Success = Service.GetClosePrincipleAndInterest(A_NO, ref db, ref Message, out CloseRate, out PrincipleAndInterestAmt);
            }
            catch (Exception ex)
            {
                Success = false;
                Message = "試算失敗，原因:" + ex.Message;
            }

            var data = "{ \"Success\" : \"" + Success.ToString() + "\" , \"PrincipleAndInterestAmt\" : \"" + PrincipleAndInterestAmt + "\", \"Error\" : \"" + Message + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        public ActionResult MyList()
        {
            this.Shared("我的資產");
            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(AWAI07IndexViewModel model)
        {
            this.Shared("我的資產");
            if (model == null) model = new AWAI07IndexViewModel();
            if (model.Search == null) model.Search = new AWAI07SearchViewModel();

            model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            model.Search.WhereUSER_NO = USER_NO;
            ViewBag.STATUS = AWAT10.StatusVal.SelectItem(model.Search.WhereSTATUS, true);
            model = Service.GetListData(model, ref db);
            model.PieChart = GetPreCashPieChart(model);

            return PartialView(model);
        }

        #region 酷幣占比 圓餅圖

        /// <summary>
        /// 酷幣占比 圓餅圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetPreCashPieChart(AWAI07IndexViewModel model)
        {
            var returnObject = new List<object>();

            returnObject.Add(new object[] { $"定存{model.Time_Deposit.Value.ToString("#,0")}點", Math.Round(Convert.ToDouble(model.Time_Deposit / (model.Time_Deposit + model.Demand_Deposit) * 100), 2) });
            returnObject.Add(new object[] { $"活存{model.Demand_Deposit.Value.ToString("#,0")}點", Math.Round(Convert.ToDouble(model.Demand_Deposit / (model.Time_Deposit + model.Demand_Deposit) * 100), 2) });

            Color[] PieColors = { Color.Blue, Color.Red };

            Highcharts TempCashPieChart = new Highcharts("TempCashPieChart")
               .InitChart(new Chart
               {
                   DefaultSeriesType = ChartTypes.Pie,
                   BackgroundColor = null,
                   BorderWidth = null,
                   Shadow = false,
               })
              .SetPlotOptions(new PlotOptions
              {
                  Pie = new PlotOptionsPie
                  {
                      AllowPointSelect = true,
                      Cursor = Cursors.Pointer,
                      //ShowInLegend = true,
                      Colors = PieColors,
                      DataLabels = new PlotOptionsPieDataLabels
                      {
                          Enabled = true,
                          Format = "<b>{point.name}</b> {point.percentage:.1f} %",
                          Style = "color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'"
                      }
                  }
              })
             .SetTooltip(new Tooltip { PointFormat = "<b>{point.percentage:.1f}%</b>" })
             .SetTitle(new Title { Text = "資產" })
              .SetSeries(new Series
              {
                  Data = new Data(returnObject.ToArray())
              });

            chartsHelper.SetCopyright(TempCashPieChart);

            return TempCashPieChart;
        }

        #endregion 酷幣占比 圓餅圖

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Edit(AWAI07EditViewModel model)
        {
            this.Shared(Bre_Name + "-編輯");
            if (model == null) model = new AWAI07EditViewModel();
            if (model.Search == null) model.Search = new AWAI07SearchViewModel();

            model = Service.GetEditData(model, ref db);
            ViewBag.PeriodTypeItem = Service.PeriodTypeItem(ref db, model.Edit?.PERIOD_TYPE);

            if (model.Edit?.STATUS == AWAT10.StatusVal.Terminate)
            {
                this.SetTitle(Bre_Name + "-此定存已解約");
            }
            else if (model.Edit?.STATUS == AWAT10.StatusVal.SetUp)
            {
                this.SetTitle(Bre_Name + "-解約作業");
            }
            else if (!string.IsNullOrWhiteSpace(model.Edit?.STATUS))
            {
                this.SetTitle(Bre_Name + "-" + AWAT10.StatusVal.GetDesc(model.Edit.STATUS));
            }

            return View(model);
        }
        public ActionResult AWAI07GetALL() {

            AWAI07Service aWAI07 = new AWAI07Service();
            aWAI07.CheckAutoBankTask();
            return Json("true", JsonRequestBehavior.AllowGet);
        }

        public ActionResult AWAI07GetSCHOOL(string SCHOOL_NO)
        {

            AWAI07Service aWAI07 = new AWAI07Service();
            aWAI07.AutoBankTask(SCHOOL_NO);
            return Json("true", JsonRequestBehavior.AllowGet);
        }
        public ActionResult EditSave(AWAI07EditViewModel model)
        {
            this.Shared(Bre_Name + "-編輯");
            if (model == null) model = new AWAI07EditViewModel();
            if (model.Search == null) model.Search = new AWAI07SearchViewModel();

            string Message = string.Empty;
            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);

            if (model.Edit.MATURITY_TYPE == 2)
            {
                ModelState.Remove("Edit.ACCT_CODE");
            }

            if (model.Edit.AMT < 500)
            {
                ModelState.AddModelError("Edit.AMT", "*定存酷幣請輸入大於或等於500");
            }

            if (model.Edit.AMT > user.CASH)
            {
                ModelState.AddModelError("Edit.AMT", $"*定存酷幣({model.Edit.AMT})大於目前酷幣點數({user.CASH})");
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {

                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                bool OK = Service.SaveData(model, user, ref db, ref Message,ref valuesList);

                if (OK)
                {
                    //更新顯示
                    UserProfile.RefreshCashInfo(user, ref db);
                    UserProfileHelper.Set(user);

                    TempData["StatusMessage"] = "儲存完成";
                    AWAI07IndexViewModel Qmodel = new AWAI07IndexViewModel()
                    {
                        Search = new AWAI07SearchViewModel()
                    };
                    Qmodel.Search = model.Search;

                    return View("MyList", Qmodel);
                }
            }

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            TempData["StatusMessage"] = Message;
            ViewBag.PeriodTypeItem = Service.PeriodTypeItem(ref db, model.Edit?.PERIOD_TYPE);
            return View("Edit", model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult DelSave(AWAI07EditViewModel model)
        {
            this.Shared(Bre_Name + "-編輯");
            if (model == null) model = new AWAI07EditViewModel();
            if (model.Search == null) model.Search = new AWAI07SearchViewModel();

            string Message = string.Empty;

            bool OK = Service.DelData(model, user, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "作廢完成";
                AWAI07IndexViewModel Qmodel = new AWAI07IndexViewModel()
                {
                    Search = new AWAI07SearchViewModel()
                };
                Qmodel.Search = model.Search;

                return View("MyList", Qmodel);
            }

            TempData["StatusMessage"] = Message;
            ViewBag.PeriodTypeItem = Service.PeriodTypeItem(ref db, model.Edit?.PERIOD_TYPE);
            return View("Edit", model);
        }

        [CheckPermission(CheckACTION_ID = "CloseTrans",CheckBRE_NO = "AWA0032")] //檢查權限
        public ActionResult CloseTrans(CloseTransViewMode TranviewMode) {
            this.Shared(Bre_Name);
            List<BDMT01> bDMT01sList = new List<BDMT01>();
            if (TranviewMode == null) {
                TranviewMode = new CloseTransViewMode();
            }
            int pageSize = 20;
            var BDMT01List = from x in db.BDMT01 where x.CITY == "臺北市" select x;
         
            IQueryable < BDMT01 > BDMT01Qeury = BDMT01List.OrderBy(e => e.SCHOOL_NO);
            if (user != null && user.USER_NO != "0000" )
            {

                BDMT01Qeury = BDMT01Qeury.Where(x => x.SCHOOL_NO == user.SCHOOL_NO);

            }
            if (!string.IsNullOrWhiteSpace(TranviewMode.whereKeyword)) {


               
                BDMT01Qeury = BDMT01Qeury.Where(x => x.SCHOOL_NAME.Contains(TranviewMode.whereKeyword));
            }
            TranviewMode.BDMT01List= BDMT01Qeury.ToPagedList(TranviewMode.Page > 0 ? TranviewMode.Page - 1 : 0, pageSize);

            return View(TranviewMode);
        }
       //[CheckPermission(CheckACTION_ID = "CloseTransSave")] //檢查權限
       [HttpPost]
        public ActionResult CloseTransSave(AWAI07EditViewModel model)

        {
            this.Shared(Bre_Name + "-編輯");
           
            if (model == null) model = new AWAI07EditViewModel();
            if (model.Search == null) model.Search = new AWAI07SearchViewModel();
            bool OK = true;
            AWAI07IndexViewModel Qmodel = new AWAI07IndexViewModel()
            {
                Search = new AWAI07SearchViewModel()
            };
            Qmodel.Search = model.Search;
            string Message = string.Empty;
            int BDMT01Count = 0;
            BDMT01Count = db.BDMT01.Where(x => (x.ISSquare == false || x.ISSquare== null ) && x.SCHOOL_NO == model.school_no  &&x.CITY == "臺北市").Count();
            if (BDMT01Count > 0)
            {


                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
               OK = Service.CloseSaveDataTranse(model, user, ref db, ref Message, ref valuesList, model.school_no);
                if (OK)
                {

                    BDMT01 bDMT01temp = new BDMT01();
                    bDMT01temp = db.BDMT01.Where(x => (x.ISSquare == false || x.ISSquare ==null ) && x.SCHOOL_NO == model.school_no).FirstOrDefault();
                    List<AWAT10> aWAT10s = new List<AWAT10>();
                    //1.撈出學校中和約有成立的定存
                    AWAI07Service aWAI07Services = new AWAI07Service();
                    aWAT10s = aWAI07Services.TakeBankList(model.school_no);
                    if (aWAT10s.Count() == 0) {
                        bDMT01temp.ISSquare = true;
                        bDMT01temp.ISSquareDate = DateTime.Now;
                        TempData["StatusMessage"] = "結清完成";
                        db.SaveChanges();
                        Message = "結清完成";
                    }

                  
                    //更新顯示
                   // UserProfile.RefreshCashInfo(user, ref db);
                  //  UserProfileHelper.Set(user);
              
                

                }
            }
            else {
                TempData["StatusMessage"] = "之前已結清,無法再結清";
                Message = "之前已結清,無法再結清";


            }
            var data = new
            {
                Success = OK.ToString(),
            
                Error = Message
            };
            return Json(data, JsonRequestBehavior.AllowGet);
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult CloseSave(AWAI07EditViewModel model)
        {
            this.Shared(Bre_Name + "-編輯");
            if (model == null) model = new AWAI07EditViewModel();
            if (model.Search == null) model.Search = new AWAI07SearchViewModel();

            string Message = string.Empty;
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            bool OK = Service.CloseSaveData(model, user, ref db, ref Message,ref valuesList);
               
            if (OK)
            {
                //更新顯示
                UserProfile.RefreshCashInfo(user, ref db);
                UserProfileHelper.Set(user);
                TempData["StatusMessage"] = "解約完成";
                AWAI07IndexViewModel Qmodel = new AWAI07IndexViewModel()
                {
                    Search = new AWAI07SearchViewModel()
                };
                Qmodel.Search = model.Search;

                return View("MyList", Qmodel);
            }

            TempData["StatusMessage"] = Message;
            ViewBag.PeriodTypeItem = Service.PeriodTypeItem(ref db, model.Edit?.PERIOD_TYPE);
            return View("Edit", model);
        }

        public ActionResult Teaching()
        {
            return View();
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}