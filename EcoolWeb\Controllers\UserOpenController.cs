﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class UserOpenController : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "UserOpen";

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private UserOpenService Service = new UserOpenService();

        public ActionResult _TeacherModal(TeacherModalViewModel Data)
        {
            return PartialView(Data);
        }

        public ActionResult _TeacherModalPageContent(TeacherModalViewModel Data)
        {
            this.Shared();
            if (Data == null) Data = new TeacherModalViewModel();

            if (string.IsNullOrWhiteSpace(Data.SCHOOL_NO))
            {
                Data.SCHOOL_NO = SCHOOL_NO;
            }

            ModelState.Clear();
            Data.PageSize = 10;
            Data = Service.GetTeacherListData(Data, ref db);
            return PartialView("_TeacherModalPageContent", Data);
        }

        public ActionResult _DetailsTeacher(HRMT01 Item)
        {
            return PartialView("_DetailsTeacher", Item);
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = "老師人員" + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = "老師人員" + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }
    }
}