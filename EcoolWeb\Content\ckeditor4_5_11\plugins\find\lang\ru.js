﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'ru', {
	find: 'Найти',
	findOptions: 'Опции поиска',
	findWhat: 'Найти:',
	matchCase: 'Учитывать регистр',
	matchCyclic: 'По всему тексту',
	matchWord: 'Только слово целиком',
	notFoundMsg: 'Искомый текст не найден.',
	replace: 'Заменить',
	replaceAll: 'Заменить всё',
	replaceSuccessMsg: 'Успешно заменено %1 раз(а).',
	replaceWith: 'Заменить на:',
	title: 'Поиск и замена'
} );
