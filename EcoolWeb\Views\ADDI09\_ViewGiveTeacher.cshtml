﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO

<div class="form-group">
    @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
    </div>
</div>

@{
    Model.CONTENT_TXT = "AWAT08_LOG";
}
@Html.HiddenFor(model => model.CONTENT_TXT)

@if (!Model.Individual_Give)
{ 
    <div class="form-group">
        @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })
        </div>
    </div>
}