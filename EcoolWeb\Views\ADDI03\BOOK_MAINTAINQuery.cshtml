﻿@using System.Collections;
@{
    ViewBag.Title = "護照閱讀-書單維護一覽表";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    List<Hashtable> htbBook = ViewData["BOOK_HTB"] == null ? new List<Hashtable>() : (List<Hashtable>)ViewData["BOOK_HTB"];
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ActionLink("新增書單", "BOOK_MAINTAIN", new { Book_Status = "ADD" }, new { @class = "btn btn-sm btn-sys" })
<div class="form-group ">
    <label>

        <font class="text-primary">說明 1：本數</font>
        <br />
        <font class="text-primary">
            原本建議本數如下，但應群組學校的要求，112年10月起，每年級的護照書本，最多可以50本。
        </font><br />
        一年級：四本中文+1本英文。(建議共5本，程式可以到50本)。
        <br />
        二年級：六本中文+2本英文。(建議共8本，程式可以到50本)。
        <br />  三-六年級：八本中文+2本英文(建議共10本，程式可以到50本)。<br />
        <font sty="color:red">112年8月起，程式修正，每學年可以增加到50本以內。</font><br />
        <br />
        <font class="text-primary"> 說明 2：流水編號請遵循以下規則。</font><br />
        一年級：101、102、103.....<br />
        二年級：201、202、203.....<br />
        三年級：301、302、303.....<br />
        <br />
        <font class="text-primary">說明 3：其他注意事項</font><br />

        同一本書不能重複出現或更換不同編號，例如小王子如果是102，就不能再出現於203；也不行將原本編號102的小王子，改成105，避免系統造成混亂。<br />
    </label>
        
        
    
</div>

<img src="~/Content/img/web-bar2-revise-05.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center">
        <table class="table-ecool table-92Per table-hover table-ecool-rpp">
            <thead>
                <tr>
                    <th>閱讀年段</th>
                    <th>書本編號</th>
                    <th>書名</th>
                    <th>維護</th>
                </tr>
            </thead>

            @{
                if (null != htbBook && htbBook.Count != 0)
                {

                    foreach (Hashtable htb in htbBook)
                    {
                        string bkColor = string.Empty;
                        switch (htb["GRADE"].ToString())
                        {
                            case "一年級":
                            case "四年級":
                                bkColor = "#F4E0BB";
                                break;
                            case "二年級":
                            case "五年級":
                                bkColor = "#F8F5C7";
                                break;
                            case "三年級":
                            case "六年級":
                                bkColor = "#C8FFF0";
                                break;
                            default:
                                break;
                        }
                        //if (string.IsNullOrEmpty(htb["BOOK_NAME"].ToString()))
                        //{

                        //}
                        //else { 
                        <tr bgcolor="@bkColor">
                            <td align="center">@htb["GRADE"]</td>
                            <td align="center">@htb["BOOK_ID"]</td>
                            <td style="text-align: left;white-space:normal">@htb["BOOK_NAME"]</td>
                            <td style="text-align: center;white-space:normal">
                                @Html.ActionLink("維護", "BOOK_MAINTAIN", new { Book_Status = "EDIT", BOOK_ID = htb["BOOK_ID"] }, new { @class = "btn btn-xs btn-Basic" })
                                @Html.ActionLink("刪除", "BOOK_MAINTAIN", new { Book_Status = "DEL", BOOK_ID = htb["BOOK_ID"] }, new { @class = "btn btn-xs btn-Basic" })
                            </td>
                        </tr>
                        //}
                    }
                }
                else
                {
                    <tr bgcolor="#F4E0BB">
                        <td align="center" bgcolor="#F4E0BB" colspan="3"><h3>查無資料!!</h3></td>
                    </tr>
                }
            }
        </table>
    </div>
</div>
<div class="text-center">
    ( <a href="#top"><img src="../Content/img/icon/rpp/goto_top.gif" alt="goto top" width="17" height="15" border="0" align="absmiddle"></a> Top )
</div>

@section css {
    <style>
        .btn-hover-effect {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .table-row-hover {
            background-color: #f5f5f5 !important;
            transition: background-color 0.2s ease;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .book-statistics .label {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            font-size: 12px;
        }

        .alert-message {
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .text-primary {
            font-weight: bold;
            margin: 5px 0;
        }

        .table-ecool th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }

        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI03_BOOK_MAINTAIN_QUERY_URLS = {
            bookMaintain: "@Url.Action("BOOK_MAINTAIN", "ADDI03")"
        };
    </script>
    <script src="~/Scripts/ADDI03/book-maintain-query.js" nonce="cmlvaw"></script>
}