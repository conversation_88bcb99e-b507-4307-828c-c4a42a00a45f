﻿@model List<ECOOL_APP.com.ecool.Models.entity.uADDT19>

@if (Model != null &&Model.Count()!=0)
{
    string ImgUrl = string.Empty;

    if (Model.FirstOrDefault().IMG_FILE != null && Model.FirstOrDefault().IMG_FILE != string.Empty && ViewBag.ISSHOW == "Y")
    {
        ImgUrl = string.Format(@"{0}{1}\{2}", ViewBag.SYSUrl, Model.FirstOrDefault().IMG_ID, Model.FirstOrDefault().IMG_FILE);

        if (Model.FirstOrDefault().IMG_LINK != null && Model.FirstOrDefault().IMG_LINK != string.Empty)
        {

            <div class="row">
                <div style="text-align:center;margin: 0px auto">
                    <div style="text-align:center;margin: 0px auto">
                        <a href="@Model.FirstOrDefault().IMG_LINK" target="_blank">
                            <img src="@Url.Content(ImgUrl)" class="img-responsive" style="text-align:center;margin: 0px auto" />
                        </a>
                    </div>
                </div>
            </div>
            <Br />

        }
        else
        {
            <div class="row">
                <div style="text-align:center;margin: 0px auto">
                    <div style="text-align:center;margin: 0px auto">
                        <img src="@Url.Content(ImgUrl)" class="img-responsive" style="text-align:center;margin: 0px auto" />
                    </div>
                </div>
            </div>
            <Br />
        }
    }
}