﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI33Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI33";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ZZZI33Service Service = new ZZZI33Service();

        [CheckPermission] //檢查權限
        public ActionResult Index()
        {
            this.Shared();
            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(ZZZI33IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI33IndexViewModel();
            if (model.Search == null) model.Search = new ZZZI33SearchViewModel();

            if (model.Search.whereSOU_KEY == null)
            {
                model.Search.whereSOU_KEY = SCHOOL_NO;
            }

            model.Search.BackAction = "Index";
            model.Search.BackController = Bre_NO;
            if (HRMT24_ENUM.CheckQAdmin(user) != true)
            {
                model.Search.whereSOU_Person = user.USER_KEY;
               

            }
            model = Service.GetQuestionManagerData(model, ref db);

            return PartialView(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ExcelIndex(ZZZI33IndexViewModel Search)
        {
            this.Shared("Excel匯入");
            ModelState.Clear();
            return View(Search);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ExcelSave(HttpPostedFileBase files, ZZZI33IndexViewModel QData)
        {
            this.Shared("Excel匯入");

            if (files == null || files.FileName == string.Empty) ModelState.AddModelError("files", "請上傳檔案");
            else if (files != null)
            {
                Regex regexCode = new Regex(@".*\.(xls|xlsx)");

                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳Excel格式為xls、xlsx");
                }
            }

            string Message = string.Empty;

            ZZZI33EditViewModel Model = new ZZZI33EditViewModel();
            string QUESTIONNAIRE_ID = string.Empty;
            var OK = this.ExcelData(files, user, Model, ref QUESTIONNAIRE_ID, ref Message);
            if (OK)
            {
                TempData["StatusMessage"] = "上傳完成, 請記得發佈，本次新建投票才算完成";

                var Qmodel = new ZZZI33PreviewViewModel()
                {
                    Search = QData.Search
                };
                Qmodel.Search.whereQUESTIONNAIRE_ID = QUESTIONNAIRE_ID;
                Qmodel = Service.GetQuestionManagerDetailsPreview(Qmodel, ref db);

                if (Qmodel.Title == null)
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
                }

                this.SetTitle(Qmodel.Title.QUESTIONNAIRE_NAME);
                ModelState.Clear();
                Qmodel.IsCreExcel = true;
                return View("Details", Qmodel);
            }

            TempData["StatusMessage"] = Message;

            return View("ExcelIndex", QData);
        }

        #region Excel 匯入處理

        protected string _ErrorRowCellExcel = string.Empty;

        /// <summary>
        /// 是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckErr = false;

        /// <summary>
        /// 格式是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckDataTypeErr = false;

        /// <summary>
        /// 必輸未輸 true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckMustErr = false;

        /// <summary>
        /// 必輸欄位陣列
        /// </summary>
        private string[] MustMainArray; //必輸欄位

        /// <summary>
        /// Excel標題
        /// </summary>
        private string[] TitleMainArray; //標準標題

        private bool ExcelData(HttpPostedFileBase files, UserProfile user, ZZZI33EditViewModel model, ref string QUESTIONNAIRE_ID, ref string Message)
        {
            bool ReturnBool = true;

            NPOIHelper npoi = new NPOIHelper(); //NEW NPOIHelper

            npoi.onDataTypeConflict += new DataRowCellHandler(this.NPOI_DataTypeConflict); //宣告使用 輸入內容的型態 是否設定 及 Excel存儲格式一樣 ex. 日期 輸入[aaa] ，Excel存儲格式設日期，或欄位第一行有定義 System.DateTime
            npoi.onRowCheckValue += new DataRowCellHandler(this.NPOI_RowCheckValue); //宣告使用 NPOI_RowCheckValue (自訂檢查Value)

            string[] ArraySheetNames = { "主檔", "單選", "核取方塊", "簡答" }; //Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)

            TitleMainArray = new string[] { "QUESTIONNAIRE_NAME", "QUESTIONNAIRE_DESC", "QUESTIONNAIRE_SDATE", "QUESTIONNAIRE_EDATE", "CASH", "RESULT", "REGISTERED_BALLOT" }; //標題欄位

            MustMainArray = new string[] { "QUESTIONNAIRE_NAME", "QUESTIONNAIRE_DESC", "QUESTIONNAIRE_SDATE", "QUESTIONNAIRE_EDATE" }; // Excel 必輸欄位
            string _Error;

            try
            {
                DataSet ds = npoi.Excel2Table(files.InputStream, 0, 2, 1, ArraySheetNames);

                if (ds == null)
                {
                    _Error = "上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                    Message = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                ///讀取資料筆數為0
                if (ds.Tables.Count == 0)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br>讀取資料筆數為0，請確認 上傳Excel的 Sheet Name 是否有以下 ";
                    foreach (string ThisSheet in ArraySheetNames)
                    {
                        _Error = _Error + "【" + ThisSheet.ToString() + "】";
                    }
                    _Error = _Error + " Sheet Name ，請確認";

                    Message = _Error;

                    ReturnBool = false;
                    return ReturnBool;
                }

                ///EXCEL內容資料型態數誤
                if (_CheckDataTypeErr || _CheckMustErr)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> " + _ErrorRowCellExcel;
                    Message = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                DataTable dtSAQT01 = ds.Tables[ArraySheetNames[0]];
                DataTable dtRadio = ds.Tables[ArraySheetNames[1]];
                DataTable dtCheckbox = ds.Tables[ArraySheetNames[2]];
                DataTable dtText = ds.Tables[ArraySheetNames[3]];

                if (dtSAQT01.Rows.Count > 1 || dtSAQT01.Rows.Count <= 0)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> 主檔必需輸入1筆，不可不輸或超過";
                    Message = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                model.Title = dtSAQT01.ToList<ZZZI33EditTitleViewModel>().FirstOrDefault();
                if (model.Topic == null) model.Topic = new List<ZZZI33EditTopicViewModel>();

                //Radio
                if (dtRadio != null)
                {
                    if (dtRadio.Rows.Count > 0)
                    {
                        foreach (DataRow Row in dtRadio.Rows)
                        {
                            if (Row["Q_SUBJECT"] != DBNull.Value)
                            {
                                if (string.IsNullOrWhiteSpace((string)Row["Q_SUBJECT"]) == false)
                                {
                                    ZZZI33EditTopicViewModel Cre_M = new ZZZI33EditTopicViewModel();
                                    Cre_M.Q_SUBJECT = (Row["Q_SUBJECT"] == DBNull.Value ? "" : (string)Row["Q_SUBJECT"]);
                                    Cre_M.Q_TYPE = SAQT02.Q_TYPEVal.IsInput;

                                    if (Cre_M.Topic_D == null) Cre_M.Topic_D = new List<ZZZI33EditTopic_DViewModel>();

                                    for (int i = 1; i <= 5; i++)
                                    {
                                        if (Row["Q_NUM" + i] != DBNull.Value)
                                        {
                                            if (string.IsNullOrWhiteSpace((string)Row["Q_NUM" + i]) == false)
                                            {
                                                Cre_M.Topic_D.Add(new ZZZI33EditTopic_DViewModel()
                                                {
                                                    Q_INPUT_TYPE = SAQT03.Q_INPUT_TYPEVal.radio,
                                                    Q_VAL = (Row["Q_NUM" + i] == DBNull.Value ? "" : (string)Row["Q_NUM" + i]),
                                                });
                                            }
                                        }
                                    }

                                    model.Topic.Add(Cre_M);
                                }
                            }
                        }
                    }
                }

                //Checkbox
                if (dtCheckbox != null)
                {
                    if (dtCheckbox.Rows.Count > 0)
                    {
                        foreach (DataRow Row in dtCheckbox.Rows)
                        {
                            if (Row["Q_SUBJECT"] != DBNull.Value)
                            {
                                if (string.IsNullOrWhiteSpace((string)Row["Q_SUBJECT"]) == false)
                                {
                                    ZZZI33EditTopicViewModel Cre_M = new ZZZI33EditTopicViewModel();
                                    Cre_M.Q_SUBJECT = (Row["Q_SUBJECT"] == DBNull.Value ? "" : (string)Row["Q_SUBJECT"]);
                                    Cre_M.Q_TYPE = SAQT02.Q_TYPEVal.IsInput;

                                    if (Cre_M.Topic_D == null) Cre_M.Topic_D = new List<ZZZI33EditTopic_DViewModel>();

                                    for (int i = 1; i <= 55; i++)
                                    {
                                        if (Row["Q_NUM" + i] != DBNull.Value)
                                        {
                                            if (string.IsNullOrWhiteSpace((string)Row["Q_NUM" + i]) == false)
                                            {
                                                Cre_M.Topic_D.Add(new ZZZI33EditTopic_DViewModel()
                                                {
                                                    Q_INPUT_TYPE = SAQT03.Q_INPUT_TYPEVal.checkbox,
                                                    Q_VAL = (Row["Q_NUM" + i] == DBNull.Value ? "" : (string)Row["Q_NUM" + i]),
                                                });
                                            }
                                        }
                                    }

                                    model.Topic.Add(Cre_M);
                                }
                            }
                        }
                    }
                }

                //Text
                if (dtText != null)
                {
                    if (dtText.Rows.Count > 0)
                    {
                        foreach (DataRow Row in dtText.Rows)
                        {
                            if (Row["Q_SUBJECT"] != DBNull.Value)
                            {
                                if (string.IsNullOrWhiteSpace((string)Row["Q_SUBJECT"]) == false)
                                {
                                    ZZZI33EditTopicViewModel Cre_M = new ZZZI33EditTopicViewModel();
                                    Cre_M.Q_SUBJECT = (Row["Q_SUBJECT"] == DBNull.Value ? "" : (string)Row["Q_SUBJECT"]);
                                    Cre_M.Q_TYPE = SAQT02.Q_TYPEVal.IsInput;

                                    if (Cre_M.Topic_D == null) Cre_M.Topic_D = new List<ZZZI33EditTopic_DViewModel>();

                                    Cre_M.Topic_D.Add(new ZZZI33EditTopic_DViewModel()
                                    {
                                        Q_INPUT_TYPE = SAQT03.Q_INPUT_TYPEVal.text,
                                    });

                                    model.Topic.Add(Cre_M);
                                }
                            }
                        }
                    }
                }

                bool OK = GetOK(user, model, ref QUESTIONNAIRE_ID, ref Message);
                if (OK)
                {
                    ReturnBool = true;
                }
                else
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br>" + Message;
                    Message = _Error;
                    ReturnBool = false;
                }
            }
            catch (Exception ex)
            {
                _Error = "上傳錯誤，錯誤原因如下:<br><br>" + ex;
                Message = _Error;
                ReturnBool = false;
            }

            return ReturnBool;
        }

        private bool GetOK(UserProfile user, ZZZI33EditViewModel model, ref string QUESTIONNAIRE_ID, ref string Message)
        {
            return Service.SaveQuestionManage(model, user, ref db, ref Message, ref QUESTIONNAIRE_ID);
        }

        #endregion Excel 匯入處理

        #region 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_DataTypeConflict(object sender, DataRowCellFilledArgs e)
        {
            _CheckErr = true;
            _CheckDataTypeErr = true;
            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
        }

        #endregion 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        #region 作用:NPOI EXCEL 自訂檢查，顯示的錯誤訊息內容

        /// <summary>
        /// 自訂檢查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void NPOI_RowCheckValue(object sender, DataRowCellFilledArgs e)
        {
            if (MustMainArray.Any(s => e.ColName.Equals(s)) == true)
            {
                try
                {
                    if (e.CellToString == "" || e.CellToString == null || Convert.IsDBNull(e.CellToString))
                    {
                        _CheckErr = true;
                        _CheckMustErr = true;
                        _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        #endregion 作用:NPOI EXCEL 自訂檢查，顯示的錯誤訊息內容

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Edit(ZZZI33EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI33EditViewModel();
            if (model.Search == null) model.Search = new ZZZI33SearchViewModel();

            model.Search.BackAction = (string.IsNullOrWhiteSpace(model.Search.BackAction)) ? RouteData.Values["action"].ToString() : model.Search.BackAction;
            model.Search.BackController = RouteData.Values["Controller"].ToString();

            if (model.Search.whereSOU_KEY == null)
            {
                model.Search.whereSOU_KEY = SCHOOL_NO;
            }

            model = Service.GetEditQuestionManage(model, ref db);

            if (model.Title == null)
            {
                this.Shared(Bre_Name + "-新增");
                model.Title = new ZZZI33EditTitleViewModel
                {
                    CASH = 0,
                    ANSWER_COUNT= null,
                    REGISTERED_BALLOT = false,
                    RESULT = true
                };
            }
            else
            {
                this.Shared(Bre_Name + "-" + model.Title.QUESTIONNAIRE_NAME);
                if (model.Title.ANSWER_COUNT == null)
                {
                    model.Title.ANSWER_COUNT = 1;
                }
                model.REF_KEY = model.Title.QUESTIONNAIRE_ID;
            }

            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult EditSave(ZZZI33EditViewModel model)
        {
            if (model == null) model = new ZZZI33EditViewModel();
            if (model.Search == null) model.Search = new ZZZI33SearchViewModel();

            if (model.Title.QUESTIONNAIRE_ID == null)
            {
                this.Shared(Bre_Name + "-新增");
            }
            else
            {
                this.Shared(Bre_Name + "-" + model.Title.QUESTIONNAIRE_NAME);
            }

            string Message = string.Empty;
            if (model.Title.QUESTIONNAIRE_SDATE > model.Title.QUESTIONNAIRE_EDATE) {

                ModelState.AddModelError("Title.QUESTIONNAIRE_SDATE", "時間區間錯誤");
            }
            if ((model.Title.ANSWER_PERSON_YN ?? "N") == "Y")
            {
                string REF_KEY = model.Title.QUESTIONNAIRE_ID ?? Session.SessionID;

                if (db.REFT01_Q.Where(a => a.REF_TABLE == "SAQT01" && a.REF_KEY == REF_KEY).Any() == false)
                {
                    ModelState.AddModelError("Title.ANSWER_PERSON_YN", "勾選了是否限制對象，但未選取人員");
                }
            }

            if (model.Topic == null)
            {
                ModelState.AddModelError(string.Empty, "*投票明細內容未輸入");
            }
            else
            {
                foreach (var item in model.Topic.Where(a => a.INPUT_TYPE == "" || a.INPUT_TYPE == null))
                {
                    if (string.IsNullOrWhiteSpace(item.INPUT_TYPE))
                    {
                        ModelState.AddModelError($"Topic[{item.Html_ID}].INPUT_TYPE", "*投票選項類型未選擇");
                    }
                }
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                string QUESTIONNAIRE_ID = string.Empty;
                bool OK = Service.SaveQuestionManage(model, user, ref db, ref Message, ref QUESTIONNAIRE_ID);

                if (OK)
                {
                    if (model.Title.STATUS != SAQT01.STATUSVal.StartedOut)
                    {
                        TempData["StatusMessage"] = "儲存完成, 請記得發佈，本次新建投票才算完成";
                    }
                    else
                    {
                        TempData["StatusMessage"] = "儲存完成";
                    }

                    ZZZI33IndexViewModel Qmodel = new ZZZI33IndexViewModel()
                    {
                        Search = new ZZZI33SearchViewModel()
                    };
                    Qmodel.Search = model.Search;

                    return View("Index", Qmodel);
                }
            }

            TempData["StatusMessage"] = Message;

            return View("Edit", model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Details(ZZZI33PreviewViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI33PreviewViewModel();
            if (model.Search == null) model.Search = new ZZZI33SearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.whereQUESTIONNAIRE_ID))
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error");
            }

            model = Service.GetQuestionManagerDetailsPreview(model, ref db);

            if (model.Title == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
            }

            this.SetTitle(Bre_Name + "-" + model.Title.QUESTIONNAIRE_NAME);
            return View(model);
        }

        /// <summary>
        /// 標題/描述/問題
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        public ActionResult _detail(ZZZI33EditTopicViewModel Item)
        {
            if (Item == null)
            {
                Item = new ZZZI33EditTopicViewModel();
            }

            if (Item.Q_MUST == null)
            {
                Item.MUST = true;
            }

            if (Item.Q_TYPE != SAQT02.Q_TYPEVal.IsText)
            {
                var InputType = SAQT03.Q_INPUT_TYPEVal.SelectItem(Item.INPUT_TYPE);
                ViewBag.InputType = InputType;
            }

            return PartialView(Item);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        /// <summary>
        /// copy
        /// </summary>
        /// <param name="Model"></param>
        /// <returns></returns>
        public ActionResult _detailCopy(ZZZI33EditViewModel Model)
        {
            ZZZI33EditTopicViewModel Item = Model.Topic.Where(a => a.Html_ID == Model.Search.whereIndex).FirstOrDefault();

            if (Item.Q_TYPE != SAQT02.Q_TYPEVal.IsText)
            {
                var InputType = SAQT03.Q_INPUT_TYPEVal.SelectItem(Item.INPUT_TYPE);
                ViewBag.InputType = InputType;
            }
            Item.Html_ID = Guid.NewGuid().ToString();
            Item.isCopy = true;

            if (Item.Topic_D != null)
            {
                if (Item.Topic_D.Count > 0)
                {
                    Item.Topic_D = Item.Topic_D.Select(
                      a =>
                      {
                          a.isCopy = true;
                          a.Q_INPUT_TYPE = Item.INPUT_TYPE;
                          return a;
                      }
                     ).ToList();
                }
            }

            return PartialView("_detail", Item);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        /// <summary>
        /// Input 項次
        /// </summary>
        /// <param name="Input"></param>
        /// <returns></returns>
        public ActionResult _Input(ZZZI33EditTopic_DViewModel Input)
        {
            if (Input == null) Input = new ZZZI33EditTopic_DViewModel();
            return PartialView(Input);
        }

        public ActionResult UpdateStatus(ZZZI33EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI33EditViewModel();
            if (model.Search == null) model.Search = new ZZZI33SearchViewModel();

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = Service.UpdateStatusQuestionManage(model, user, ref db, ref Message);

                if (OK)
                {
                    TempData["StatusMessage"] = Message;

                    var Qmodel = new ZZZI33IndexViewModel();
                    Qmodel.Search = model.Search;
                    return View("Index", Qmodel);
                }
            }

            this.SetTitle(Bre_Name + "-" + model.Title.QUESTIONNAIRE_NAME);

            TempData["StatusMessage"] = Message;

            return View("Edit", model);
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }

            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}