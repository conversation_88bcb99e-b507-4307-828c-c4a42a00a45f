﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public static class QueriesNoLock
    {
        public static System.Transactions.TransactionScope GetNewReadUncommittedScope()
        {
            return new System.Transactions.TransactionScope(
                System.Transactions.TransactionScopeOption.RequiresNew,
                new System.Transactions.TransactionOptions
                {
                    IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted
                });
        }

        public static List<T> ToListNoLock<T>(this IQueryable<T> query)
        {
            using (var txn = GetNewReadUncommittedScope())
            {
                return query.ToList();
            }
        }

        public static U NoLock<T, U>(this IQueryable<T> query, Func<IQueryable<T>, U> expr)
        {
            using (var txn = GetNewReadUncommittedScope())
            {
                return expr(query);
            }
        }
    }
}