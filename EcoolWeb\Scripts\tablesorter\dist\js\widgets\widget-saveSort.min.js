(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: saveSort - updated 2018-03-19 (v2.30.1) */
!function(o){"use strict";var v=o.tablesorter||{};function g(t){var s=v.storage(t.table,"tablesorter-savesort");return s&&s.hasOwnProperty("sortList")&&o.isArray(s.sortList)?s.sortList:[]}function S(t,s){return(s||g(t)).join(",")!==t.sortList.join(",")}v.addWidget({id:"saveSort",priority:20,options:{saveSort:!0},init:function(t,s,o,r){s.format(t,o,r,!0)},format:function(s,t,o,r){var a,e=t.$table,i=!1!==o.saveSort,n={sortList:t.sortList},l=v.debug(t,"saveSort");l&&(a=new Date),e.hasClass("hasSaveSort")?i&&s.hasInitialized&&v.storage&&S(t)&&(v.storage(s,"tablesorter-savesort",n),l&&console.log("saveSort >> Saving last sort: "+t.sortList+v.benchmark(a))):(e.addClass("hasSaveSort"),n="",v.storage&&(n=g(t),l&&console.log('saveSort >> Last sort loaded: "'+n+'"'+v.benchmark(a)),e.bind("saveSortReset",function(t){t.stopPropagation(),v.storage(s,"tablesorter-savesort","")})),r&&n&&0<n.length?t.sortList=n:s.hasInitialized&&n&&0<n.length&&S(t,n)&&v.sortOn(t,n))},remove:function(t,s){s.$table.removeClass("hasSaveSort"),v.storage&&v.storage(t,"tablesorter-savesort","")}})}(jQuery);return jQuery;}));
