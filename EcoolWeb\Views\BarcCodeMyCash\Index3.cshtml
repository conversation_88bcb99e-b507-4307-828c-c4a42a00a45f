﻿@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <p>
    </p>
    <p>
    </p>
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-查詢", "Index3Page", null, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:30px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁", "Index3Page", new { TimeoutSeconds = "6" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:60px">
                    @Html.ActionLink("酷幣ATM-查詢(沒有借書)", "Index3Page", new { NoBook = "Y" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
                <div class="col-md-6" style="margin-top:60px">
                    @Html.ActionLink("酷幣ATM-6秒回首頁(沒有借書)", "Index3Page", new { TimeoutSeconds = "6", NoBook = "Y" }, new { @class = "btn-block btn btn-default", @role = "button" })
                </div>
            </div>
            <div class="form-group">
                <label class="text-danger">
                </label>
            </div>
        </div>
    </div>
}