﻿@using ECOOL_APP.com.ecool.Models.DTO

@{
    ViewBag.Title = ViewBag.Panel_Title;

    //檢查給點教師的限制
    //每月給點上限：綁的程式是
    //一、批次加扣點裡面的「特殊加扣點」、「班級小幫手」
    //二、即時加點的「特殊加點--固定」、「特殊加點--隨機」
    string CashNote = String.Empty;
    string ErrMsg;
    short ThisMonthCash;
    ECOOL_DEVEntities db = null;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    int CashLimit = ECOOL_APP.UserProfile.GetCashLimit(user?.SCHOOL_NO, user?.USER_NO, user?.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);

    if (string.IsNullOrWhiteSpace(ErrMsg) == false)
    {
        CashLimit = 0;
    }
    if (CashLimit > 0 && CashLimit != short.MaxValue)
    {

        CashNote = "本月給點限制：" + CashLimit.ToString() + "。您本月已發出點數：" + ThisMonthCash.ToString();

    }
    //Request.Params["SYS_TABLE_TYPE"];
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div>
    @if (ViewBag.IndexActionName == "SysIndex")
    {
        @Html.ActionLink("回「新增批次給點/扣點」首頁", (string)ViewBag.IndexActionName, null, new { @class = "btn btn-sm btn-sys", @role = "button" })
    }
    else
    {
        @Html.ActionLink("查詢歷史記綠", "ListView", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE }, new { @class = "btn btn-sm btn-sys", @role = "button" })
    }
</div>

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
                  <div class="form-group text-center">
                      @*批次班級畫面*@
                      @if (ViewBag.SYS_TABLE_TYPE == "_CLASS_BATCH")
                      {
                          <div class="col-md-offset-2 col-md-4">
                              @Html.ActionLink("無敵加點-校內表現", "QuerySelectView", new { SYS_TABLE_TYPE = "ADDT14_CLASS_BATCH", ADDT14_STYLE = ViewBag.ADDT14_STYLE }, new { @class = "btn-block btn btn-default", @role = "button" })
                          </div>

                          <div class="col-md-4 ol-md-offset-2">
                              @Html.ActionLink("無敵加點-特殊加扣點", "QuerySelectView", new { SYS_TABLE_TYPE = "ADDT20_CLASS_BATCH", ADDT14_STYLE = ViewBag.ADDT14_STYLE }, new { @class = "btn-block btn btn-default", @role = "button" })
                          </div>
                      }
                      else
                      {
                          @*一般畫面*@
                          if (ViewBag.SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG
                          && ViewBag.SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY) //這幾項尚未開放Excel匯入模式
                          {
                              <div class="col-md-offset-2 col-md-4">
                                  @Html.ActionLink("Excel匯入模式", "ExcelView", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE }, new { @class = "btn-block btn btn-default", @role = "button" })
                              </div>
                          }
                          <div class="col-md-4 ol-md-offset-2">
                              @Html.ActionLink("查詢選取模式", "QuerySelectView", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE }, new { @class = "btn-block btn btn-default", @role = "button" })
                          </div>
                      }

                  </div>
        </div>
    </div>
    if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER)
    {
        <div class="form-group text-center">
            <label class="text-danger col-md-12">
                @CashNote
            </label>
        </div>
    }
}