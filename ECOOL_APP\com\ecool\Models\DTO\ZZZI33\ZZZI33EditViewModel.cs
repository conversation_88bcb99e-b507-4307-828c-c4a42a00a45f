﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI33EditViewModel
    {
        public string REF_KEY { get; set; }
        public string SaveType { get; set; }
        public ZZZI33SearchViewModel Search { get; set; }

        public ZZZI33EditTitleViewModel Title { get; set; }

        public virtual ICollection<ZZZI33EditTopicViewModel> Topic { get; set; }

        public class SaveTypeVal
        {
            /// <summary>
            /// 存檔
            /// </summary>
            static public string Save = "Save";

            /// <summary>
            /// 發佈
            /// </summary>
            static public string Release = "Release";

            /// <summary>
            /// 作廢
            /// </summary>
            static public string Disabled = "Disabled";

            /// <summary>
            /// 不顯示
            /// </summary>
            static public string NotShow = "NotShow";

            /// <summary>
            /// 提早結束
            /// </summary>
            static public string End = "End";
        }
    }
}