﻿
@model SECI01BorrowIndexViewModel



@Html.HiddenFor(m => m.SCHOOL_NO)
@Html.HiddenFor(m => m.USER_NO)
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.BackAction)
@Html.HiddenFor(m => m.Search.BackController)



<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover table-ecool-reader">
        <thead>
            <tr>
                <th>@Html.DisplayNameFor(m => m.ListData.First().SEYEAR)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().SESEM)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().BORROW_DATE)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().BK_GRP)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().BKNAME)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().ISBN)</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ListData)
            {

                <tr>
                    <td>@Html.DisplayFor(modelItem => item.SEYEAR)</td>
                    <td>@Html.DisplayFor(modelItem => item.SESEM)</td>

                    <td>@Html.DisplayFor(modelItem => item.BORROW_DATE)</td>
                    <td>@Html.DisplayFor(modelItem => item.BK_GRP)</td>
                    <td>@Html.DisplayFor(modelItem => item.BKNAME)</td>
                    <td>@Html.DisplayFor(modelItem => item.ISBN)</td>
                </tr>
            }
        </tbody>
    </table>
</div>

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
</div>






