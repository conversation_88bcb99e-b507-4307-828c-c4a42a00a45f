/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Main/Bold/GreekAndCoptic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Main-bold"],{915:[680,0,692,39,643,"425 0L228 3Q63 3 51 0H39V62H147V618H39V680H612V676Q614 670 627 552T643 428V424H581V428Q580 430 576 461T562 524T532 576Q512 596 481 605T426 616T357 618H304V62H439V0H425"],916:[698,0,958,56,901,"901 12Q901 7 892 0H479Q65 0 62 2Q56 6 56 11Q56 14 242 347T433 685Q438 694 450 696Q454 698 480 698H506L523 687Q526 683 711 354T899 17Q901 13 901 12ZM653 137L427 538L202 137L315 136H540L653 137"],920:[696,10,894,64,830,"629 -10T446 -10T164 89T64 340Q64 380 71 420T102 510T163 596T266 662T418 696H438Q488 696 499 695Q582 686 644 655T741 584T796 495T823 409T829 338Q829 188 729 89ZM439 645Q416 645 390 638T333 615T275 564T236 480Q221 423 221 341Q221 272 230 228Q247 144 301 94T447 43T592 93T663 228Q672 272 672 341Q672 645 439 645ZM286 242V446H348V412H545V446H607V242H545V276H348V242H286"],923:[698,0,806,40,765,"285 0Q267 3 154 3Q56 3 47 0H40V62H131Q131 63 167 160T244 369T321 578T359 678Q366 698 393 698H404H413Q437 698 446 678Q448 672 560 369T674 62H765V0H754Q733 3 604 3Q453 3 429 0H416V62H461L507 63L355 470Q353 468 279 265L203 63L249 62H294V0H285"],926:[675,0,767,48,718,"54 465L63 674Q63 675 383 675T703 674L712 465Q712 464 681 464H650V467Q650 490 646 516T632 545Q612 550 383 550H283Q169 550 149 548T124 531Q123 530 123 529Q116 506 116 467V464H85Q54 464 54 465ZM160 256V447H222V414H544V447H606V256H544V289H222V256H160ZM57 0L48 222H110V219Q110 147 125 133Q127 130 130 129T160 127T235 126T383 126Q482 126 530 126T604 127T635 129T641 133Q656 146 656 219V222H718L709 0H57"],928:[680,0,900,39,860,"400 0Q376 3 226 3Q75 3 51 0H39V62H147V618H39V680H860V618H752V62H860V0H848Q824 3 674 3Q523 3 499 0H487V62H595V618H304V62H412V0H400"],931:[686,0,831,64,766,"766 271Q764 266 750 137T735 4V0H407Q74 0 71 4L70 5Q64 9 64 18Q64 24 82 41T213 158L359 288Q360 288 320 336T214 460Q67 633 66 635Q64 638 64 655Q64 679 75 684Q78 686 407 686H735V682Q738 676 751 558T766 434V430H735Q704 430 704 431Q704 434 703 444T696 477T681 520T654 563T613 598Q578 615 527 619T371 624H281L396 489Q506 358 513 351Q517 342 512 334Q503 325 371 208Q338 179 303 147T249 99L231 83L243 81Q258 81 364 81Q382 81 418 81T470 82T513 83T554 88T587 96T619 109T645 129Q689 173 702 260L704 274Q704 275 735 275H766V271"],933:[698,0,894,64,829,"64 556Q69 619 114 658T224 697Q271 697 310 677T375 622T417 554T444 484L447 477V479Q456 516 473 551T516 620T582 676T670 697Q735 697 780 656T829 556Q829 539 818 532H772Q761 539 761 548Q761 571 681 571Q664 571 653 570T623 562T587 537T555 490Q536 448 531 410T525 300V210V62H660V0H646L447 3Q257 1 247 0H233V62H368V210V301Q368 373 363 410T338 490Q324 518 307 536T270 561T240 569T212 571Q132 571 132 548Q132 539 121 532H75Q64 538 64 556"],934:[686,0,831,64,766,"609 0Q582 3 415 3T221 0H207V62H342V168L328 169Q193 180 117 241Q64 286 64 343T117 445Q193 506 328 517L342 518V624H207V686H221Q248 683 415 683T609 686H623V624H488V518L502 517Q637 506 713 445Q766 400 766 343T713 241Q637 180 502 169L488 168V62H623V0H609ZM342 219T342 343T340 467Q328 467 304 459Q277 451 261 439T237 409T228 378T226 343Q226 314 229 296T250 259T301 228Q331 219 341 219Q342 219 342 343ZM604 343Q604 365 602 379T591 413T560 446T503 464L489 467Q488 467 488 343T489 219Q499 219 529 228Q554 236 570 248T593 277T602 308T604 343"],936:[686,0,894,64,829,"64 515Q64 532 71 536T104 540H139Q200 540 207 538Q225 533 236 521T253 489T260 454T264 414Q264 340 287 296T347 237Q369 226 373 226Q374 226 374 425V624H239V686H253Q280 683 447 683T641 686H655V624H520V226L522 227Q525 228 531 229T552 240T580 261T606 298T624 354Q627 368 628 394T631 440T637 482T654 518T686 538Q693 540 754 540H794Q817 540 823 536T829 515Q829 500 824 495T811 489T796 483T782 461T775 408Q767 212 568 175Q526 168 521 168Q520 168 520 115V62H655V0H641Q614 3 447 3T253 0H239V62H374V168L364 169Q290 178 243 203Q126 261 118 409Q117 443 111 461T98 484T83 489T70 495T64 515"],937:[696,1,831,51,779,"598 645T415 645T232 458Q232 385 275 239T318 26Q318 8 311 4T272 -1Q265 -1 240 -1T196 0H88V3Q86 5 70 108Q52 211 51 212V215H113V212Q123 149 132 133Q136 124 149 122T202 118Q241 118 241 119Q241 132 132 277Q64 378 64 457Q64 564 158 630T403 696Q487 696 543 685T661 638Q722 599 744 549T766 458Q766 434 761 410T749 368T729 327T709 293T684 258T663 229Q632 187 614 160T592 126L589 119Q589 118 628 118Q667 119 680 121T698 133Q702 140 706 160T714 196L717 212V215H779V212Q778 211 760 108Q744 5 742 3V0H634H562Q528 0 520 4T512 26Q512 92 555 238T598 458Q598 645 415 645"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/GreekAndCoptic.js");
