/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Size3={directory:"Size3/Regular",family:"MathJax_Size3",testString:"() [] {}",32:[0,0,250,0,0],40:[1450,949,736,209,701],41:[1450,949,736,34,526],47:[1450,949,1044,55,989],91:[1450,949,528,247,516],92:[1450,949,1044,56,988],93:[1450,949,528,11,280],123:[1450,949,750,130,618],125:[1450,949,750,131,618],160:[0,0,250,0,0],710:[772,-564,1444,-4,1447],732:[749,-610,1444,1,1442],770:[772,-564,0,-1448,3],771:[749,-610,0,-1443,-2],8730:[1450,950,1000,111,1020],8968:[1450,949,583,246,571],8969:[1450,949,583,11,336],8970:[1450,949,583,246,571],8971:[1450,949,583,11,336],10216:[1450,950,750,126,654],10217:[1450,949,750,94,623]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Size3"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size3/Regular/Main.js"]);
