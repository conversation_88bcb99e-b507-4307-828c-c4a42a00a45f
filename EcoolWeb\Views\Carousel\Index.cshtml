﻿@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
   
}

    <div id='book' style="width:80%;margin: 0px auto;">
        
    </div>

<script type="text/javascript">
    i=0; k=0;

    man()
    //查詢
    function funAjax(UrlVal) {

        var data = {
            "isCarousel" : "true"
        };


        $.ajax({
            url: UrlVal,
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#book').html(data);
            }
        });
    }

    function man() {

        web = new Array("@Url.Action("START", "Carousel")", "@Url.Action("BooksOrder", "SECI02", new { Grade=1})",
            "@Url.Action("BooksOrder", "SECI02", new { Grade=2})",
            "@Url.Action("BooksOrder", "SECI02", new { Grade=3})",
            "@Url.Action("BooksOrder", "SECI02", new { Grade=4})",
            "@Url.Action("BooksOrder", "SECI02", new { Grade=5})",
            "@Url.Action("BooksOrder", "SECI02", new { Grade=6})",
            "@Url.Action("QUERY", "AWA003")", "@Url.Action("OrderList", "ADDI01")", "@Url.Action("ADDTList", "ADDT")");
        k = i % 10;
        funAjax(web[k])
        i++;

        setTimeout("man()", 5000);
    }


</script>