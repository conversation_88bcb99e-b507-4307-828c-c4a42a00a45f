/*
 *  /MathJax/jax/output/CommonHTML/fonts/TeX/SansSerif-Regular.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(b){var a="MathJax_SansSerif";b.FONTDATA.FONTS[a]={className:b.FONTDATA.familyName(a),centerline:250,ascent:750,descent:250,32:[0,0,250,0,0],33:[694,0,319,110,208],34:[694,-471,500,32,325],35:[694,194,833,56,777],36:[750,56,500,44,444],37:[750,56,833,56,776],38:[716,22,758,42,702],39:[694,-471,278,89,188],40:[750,250,389,74,333],41:[750,250,389,55,314],42:[750,-306,500,63,436],43:[583,82,778,56,722],44:[98,125,278,89,188],45:[259,-186,333,11,277],46:[98,0,278,90,188],47:[750,250,500,56,445],48:[678,22,500,39,460],49:[678,0,500,83,430],50:[677,0,500,42,449],51:[678,22,500,42,457],52:[656,0,500,28,471],53:[656,21,500,33,449],54:[677,22,500,42,457],55:[656,11,500,42,457],56:[678,22,500,43,456],57:[677,22,500,42,457],58:[444,0,278,90,188],59:[444,125,278,89,188],61:[370,-130,778,56,722],63:[704,0,472,55,416],64:[704,11,667,56,612],65:[694,0,667,28,638],66:[694,0,667,90,610],67:[705,11,639,59,587],68:[694,0,722,88,666],69:[691,0,597,86,554],70:[691,0,569,86,526],71:[704,11,667,59,599],72:[694,0,708,86,621],73:[694,0,278,87,191],74:[694,22,472,42,388],75:[694,0,694,88,651],76:[694,0,542,87,499],77:[694,0,875,92,782],78:[694,0,708,88,619],79:[715,22,736,55,680],80:[694,0,639,88,583],81:[715,125,736,55,680],82:[694,0,646,88,617],83:[716,22,556,44,500],84:[688,0,681,36,644],85:[694,22,688,87,600],86:[694,0,667,14,652],87:[694,0,944,14,929],88:[694,0,667,14,652],89:[694,0,667,3,663],90:[694,0,611,55,560],91:[750,250,289,94,266],93:[750,250,289,22,194],94:[694,-527,500,78,421],95:[-38,114,500,0,499],97:[460,10,481,38,407],98:[694,11,517,75,482],99:[460,10,444,34,415],100:[694,10,517,33,441],101:[461,10,444,28,415],102:[705,0,306,27,347],103:[455,206,500,28,485],104:[694,0,517,73,443],105:[680,0,239,67,171],106:[680,205,267,-59,192],107:[694,0,489,76,471],108:[694,0,239,74,164],109:[455,0,794,73,720],110:[455,0,517,73,443],111:[460,10,500,28,471],112:[455,194,517,75,483],113:[455,194,517,33,441],114:[455,0,342,74,327],115:[460,10,383,28,360],116:[571,10,361,18,333],117:[444,10,517,73,443],118:[444,0,461,14,446],119:[444,0,683,14,668],120:[444,0,461,0,460],121:[444,204,461,14,446],122:[444,0,435,28,402],126:[327,-193,500,83,416],160:[0,0,250,0,0],305:[444,0,239,74,164],567:[444,205,267,-59,192],768:[694,-527,0,-417,-199],769:[694,-527,0,-302,-84],770:[694,-527,0,-422,-79],771:[677,-543,0,-417,-84],772:[631,-552,0,-431,-70],774:[694,-508,0,-427,-74],775:[680,-576,0,-302,-198],776:[680,-582,0,-397,-104],778:[694,-527,0,-319,-99],779:[694,-527,0,-399,-84],780:[654,-487,0,-422,-79],915:[691,0,542,87,499],916:[694,0,833,42,790],920:[716,21,778,56,722],923:[694,0,611,28,582],926:[688,0,667,42,624],928:[691,0,708,86,621],931:[694,0,722,55,666],933:[716,0,778,55,722],934:[694,0,722,55,666],936:[694,0,778,55,722],937:[716,0,722,44,677],8211:[312,-236,500,0,499],8212:[312,-236,1000,0,999],8216:[694,-471,278,90,189],8217:[694,-471,278,89,188],8220:[694,-471,500,174,467],8221:[694,-471,500,32,325]};b.fontLoaded("TeX/"+a.substr(8))})(MathJax.OutputJax.CommonHTML);
