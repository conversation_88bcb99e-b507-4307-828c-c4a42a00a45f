/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Script/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Script={directory:"Script/Regular",family:"GyreTermesMathJax_Script",testString:"\u00A0\u210A\u210B\u2110\u2112\u211B\u212C\u212F\u2130\u2131\u2133\u2134\uD835\uDC9C\uD835\uDC9E\uD835\uDC9F",32:[0,0,250,0,0],160:[0,0,250,0,0],8458:[426,308,729,80,649],8459:[687,17,1070,80,990],8464:[689,15,806,80,726],8466:[687,17,948,80,868],8475:[690,17,1027,80,947],8492:[690,19,950,80,870],8495:[418,14,519,80,439],8496:[694,17,755,79,675],8497:[689,17,986,80,906],8499:[689,17,1266,80,1186],8500:[427,14,629,80,549],119964:[689,17,1090,80,1010],119966:[689,17,871,80,791],119967:[690,17,902,79,822],119970:[689,17,859,80,779],119973:[689,253,852,80,772],119974:[689,17,1156,80,1076],119977:[697,24,1193,80,1113],119978:[689,19,864,80,784],119979:[690,16,897,80,817],119980:[689,140,864,80,784],119982:[689,15,993,80,913],119983:[689,17,996,80,916],119984:[687,18,1016,80,936],119985:[689,19,1050,80,970],119986:[689,19,1269,80,1189],119987:[688,17,1001,80,921],119988:[689,264,974,80,894],119989:[687,19,938,80,858],119990:[418,14,726,80,646],119991:[735,14,617,80,537],119992:[418,14,484,80,404],119993:[721,14,778,80,698],119995:[735,280,797,80,717],119997:[735,14,748,80,668],119998:[671,14,466,80,386],119999:[671,308,770,80,690],120000:[735,14,720,80,640],120001:[735,14,644,80,564],120002:[413,14,1034,80,954],120003:[410,14,750,80,670],120005:[410,280,873,80,793],120006:[413,280,707,80,627],120007:[410,0,590,80,510],120008:[506,14,500,80,420],120009:[606,14,479,80,399],120010:[406,14,721,80,641],120011:[410,14,642,80,562],120012:[409,14,892,80,812],120013:[410,14,645,80,565],120014:[406,308,754,80,674],120015:[413,14,663,80,583],120016:[703,14,1265,80,1185],120017:[705,14,1099,80,1019],120018:[703,14,925,80,845],120019:[703,14,984,80,904],120020:[703,14,848,80,768],120021:[713,14,1092,80,1012],120022:[703,14,920,80,840],120023:[719,14,1220,80,1140],120024:[707,14,854,80,774],120025:[701,253,912,80,832],120026:[709,14,1290,80,1210],120027:[730,14,1062,80,982],120028:[703,15,1463,80,1383],120029:[718,26,1378,80,1298],120030:[703,17,899,80,819],120031:[706,14,1018,80,938],120032:[703,152,899,80,819],120033:[711,14,1196,80,1116],120034:[703,15,1096,80,1016],120035:[713,14,1112,80,1032],120036:[703,14,1162,80,1082],120037:[701,16,1240,80,1160],120038:[701,16,1556,80,1476],120039:[703,14,1090,80,1010],120040:[702,263,1130,80,1050],120041:[703,14,1093,80,1013],120042:[425,14,762,80,682],120043:[735,14,696,80,616],120044:[425,14,574,80,494],120045:[720,14,851,80,771],120046:[425,14,581,80,501],120047:[735,280,884,80,804],120048:[425,309,817,80,737],120049:[735,14,803,80,723],120050:[701,14,501,80,421],120051:[701,291,811,80,731],120052:[735,14,800,80,720],120053:[735,14,779,80,699],120054:[425,14,1088,80,1008],120055:[425,14,809,80,729],120056:[425,14,677,80,597],120057:[425,280,956,80,876],120058:[425,280,751,80,671],120059:[426,0,699,80,619],120060:[521,14,537,80,457],120061:[615,14,554,80,474],120062:[411,14,780,80,700],120063:[411,14,702,80,622],120064:[411,14,951,80,871],120065:[425,14,741,80,661],120066:[411,291,812,80,732],120067:[425,14,716,80,636]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Script"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Regular/Main.js"]);
