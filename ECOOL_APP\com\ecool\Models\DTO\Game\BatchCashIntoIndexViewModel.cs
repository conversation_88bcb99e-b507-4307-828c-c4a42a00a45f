﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class BatchCashIntoIndexViewModel
    {
        public string IsPostBack { get; set; }

        /// <summary>
        /// 查詢ID URL 傳入
        /// </summary>
        public string GAME_NO { get; set; }

        public GameSearchViewModel Search { get; set; }

        public BatchCashIntoSearchViewModel CashSearch { get; set; }

        public IPagedList<GameLevelPersonDetailsViewModel> ListData;

        public int PageSize { get; set; }
        public bool ISExcel { get; set; }
        public virtual ICollection<BatchCashIntoCheckBoxViewModel> CheckBox { get; set; }

        /// <summary>
        /// 建構式 預設值
        /// </summary>
        public BatchCashIntoIndexViewModel()
        {
            PageSize = 100;
        }
    }
}