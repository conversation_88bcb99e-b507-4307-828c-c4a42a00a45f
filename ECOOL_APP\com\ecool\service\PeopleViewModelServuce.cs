﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;


namespace com.ecool.service
{

    public class PeopleViewModelServuce
    {



          public string ErrorMsg;

        /// <summary>
        /// 取得人員相關資料
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="NAME"></param>
        /// <param name="ROLE_ID"></param>
        /// <returns></returns>
        public  List<PeopleViewModel> USP_PeopleViewModel_QUERY(string SCHOOL_NO, string USER_NO, string NAME, string ROLE_ID)
        {
            List<PeopleViewModel> list_data = new List<PeopleViewModel>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {

                 sb.Append(" SELECT A.SCHOOL_NO,A.<PERSON>ER_NO,A.USER_KEY ");
                 sb.Append(" ,A.NAME,A.SNAME,A.SEX,A.GRADE,A.CLASS_NO,A.SEAT_NO,A.BIRTHDAY,A.USER_STATUS ");
                 sb.Append(" ,A.USER_TYPE,A.TEACHER_TYPE,A.E_MAIL,A.TEL_NO,A.PHONE ");
                 sb.Append(" ,B.ROLE_ID,C.ROLE_NAME ");
                 sb.Append(" FROM HRMT01 A (NOLOCK) ");
                 sb.Append(" INNER JOIN HRMT25 B (NOLOCK) ON A.SCHOOL_NO=B.SCHOOL_NO AND A.USER_NO=B.USER_NO ");
                 sb.Append(" INNER JOIN HRMT24 C  (NOLOCK) ON B.ROLE_ID=C.ROLE_ID ");
                 sb.Append(" WHERE 1=1 AND A.USER_STATUS='1' ");

                 if (SCHOOL_NO != string.Empty)
                 {
                     sb.AppendFormat(" AND A.SCHOOL_NO ='{0}' ", SCHOOL_NO);
                 }

                 if (USER_NO != string.Empty)
                 {
                     sb.AppendFormat(" AND A.USER_NO ='{0}' ", USER_NO);
                 }

                 if (NAME != string.Empty)
                 {
                     sb.AppendFormat(" AND A.NAME LIKE '%{0}%' ", NAME);
                 }

                 if (ROLE_ID != string.Empty)
                 {
                     sb.AppendFormat(" AND B.ROLE_ID ='{0}' ", ROLE_ID);
                 }
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {

                    PeopleViewModel Data =new PeopleViewModel();

                    Data.SCHOOL_NO =(string) dr["SCHOOL_NO"];
                    Data.USER_NO =(string) dr["USER_NO"];
                    Data.NAME =(string) dr["NAME"];
                    Data.ROLE_ID =(string) dr["ROLE_ID"];
                    Data.ROLE_NAME =(string) dr["ROLE_NAME"];

                    list_data.Add(Data);
                   
                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;
        }
    }
}
