﻿@model RollCallIndexViewModel
@{
                /**/

                ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    <div id="PageContent">
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO)
    </div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function onBtnRollCall(Keyword) {

            var OK = confirm("您確定要進入點名模式?")

            if (OK==true)
            {
                 $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)?ROLL_CALL_ID="+Keyword)
                 $(targetFormID).submit();
            }
        }

        function FunPageProc(page) {
            if ($(targetFormID).length > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

        function doSort(SortCol) {

            var OrderByColumnName = $('#@Html.IdFor(m=>m.OrderByColumnName)').val();
            var SortType = $('#@Html.IdFor(m=>m.SortType)').val();

            $('#@Html.IdFor(m=>m.OrderByColumnName)').val(SortCol)

            if (OrderByColumnName == SortCol ) {

                if (SortType.toUpperCase()=="@PageGlobal.SortType.DESC") {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.ASC');
                }
                else {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
                }
            } else {
                 $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
            }

            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageContent")',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }
    </script>
}