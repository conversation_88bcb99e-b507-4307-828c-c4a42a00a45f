﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class AWAI01SearchViewModel
    {

        public string WhereSCHOOL_NO { get; set; }

        public int? WhereAWARD_NO { get; set; }
        public DateTime? SDATETIME { get; set; }
        public DateTime? EDATETIME { get; set; }
        public string WhereExchangeType { get; set; }
        public string WhereAWARD_STS { get; set; }
        /// <summary>
        /// 來源Table
        /// </summary>
        public string WhereSouTable { get; set; }

        /// <summary>
        /// 搜尋已下架商品
        /// </summary>
        public string unProduct { get; set; }

        /// <summary>
        /// 含已下架商品
        /// </summary>
        [DisplayName("含已下架商品")]
        public bool StopProduct { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        [DisplayName("品名")]
        public string whereKeyword { get; set; }

        /// <summary>
        /// 獎品類別
        /// </summary>
        public string whereAWARD_SCHOOL_NO { get; set; }

        /// <summary>
        /// 獎品狀態
        /// </summary>
        public string AWARD_TYPE { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        public string BackAction { get; set; }

        public string BackController { get; set; }

        public string SouAction { get; set; }

        public string SouController { get; set; }

        public class SouTableVal
        {
            static public string Student = "Student";
            static public string Teacher = "Teacher";


       

            static private string StudentTable = "AWAT02";
            static private string TeacherTable = "AWAT09";

            static public string StudentTableBID = "AWAT02_B";
            static public string TeacherTableBID = "AWAT09_B";

            static private string StudentAddSave = "Awat02";
            static private string TeacherAddSave = "Awat202";

            static private string StudentEditSave = "AwatMana02";
            static private string TeacherEditSave = "Awat2Mana02";


            /// <summary>
            /// 新增權限
            /// </summary>
            /// <param name="SouTable"></param>
            /// <returns></returns>
            static public string GetAddSave(string SouTable)
            {
                if (SouTable == Teacher)
                {
                    return TeacherAddSave;
                }
                else
                {
                    return StudentAddSave;
                }
            }

            /// <summary>
            /// 修改權限
            /// </summary>
            /// <param name="SouTable"></param>
            /// <returns></returns>
            static public string GetEditSave(string SouTable)
            {
                if (SouTable == Teacher)
                {
                    return TeacherEditSave;
                }
                else
                {
                    return StudentEditSave;
                }
            }

            /// <summary>
            /// 存取table
            /// </summary>
            /// <param name="SouTable"></param>
            /// <returns></returns>
            static public string GetSouTable(string SouTable)
            {
                if (SouTable== Teacher)
                {
                    return TeacherTable;
                }
                else
                {
                    return StudentTable;
                }
            }

            /// <summary>
            /// 存取table BID
            /// </summary>
            /// <param name="SouTable"></param>
            /// <returns></returns>
            static public string GetSouTableBID(string SouTable)
            {
                if (SouTable == Teacher)
                {
                    return TeacherTableBID;
                }
                else
                {
                    return StudentTableBID;
                }
            }

        }



    }
}