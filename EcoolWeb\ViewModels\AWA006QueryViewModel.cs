﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;


namespace EcoolWeb.ViewModels
{
    public class AWA006QueryViewModel
    {
         /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}


        /// <summary>
        /// 只顯示某一所學校
        /// </summary>
        public string whereSchoolNo { get; set; }


        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的暱稱
        /// </summary>
        public string whereSNAME { get; set; }

        /// <summary>
        /// 只顯示某一位學生的獎品名稱
        /// </summary>
        public string whereAWARD_NAME { get; set; }

        /// <summary>
        /// 只顯示某一位學生的班級
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderColumn { get; set; }


        /// <summary>
        /// 排序: 遞增/遞減
        /// </summary>
        public string SortBy { get; set; }

        /// <summary>
        /// 每頁顯示筆數
        /// </summary>
        public int PageSize { get; set; }

        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<VAWA005> VAWA005List;

        public AWA006QueryViewModel()
        {
            PageSize = 20;
            Page = 0;
            OrderColumn = "TRANS_NO";
        }
    }
}