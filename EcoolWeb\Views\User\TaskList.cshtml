﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

}
<style>
</style>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<div class="container" style="margin-top:10px">
    <div class="row" style="margin-top:10px">
        <div class="col-md-4 col-md-offset-4">
            <a class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " type="button" href="@Url.Action("VerifyList", "ADDI01",new { whereWritingStatus="0,2"})">
                線上投稿任務
            </a>
            <a type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("../ADDT/ADDTList_CheckPending", "ADDT")">
                閱讀認證任務
            </a>
            <a type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("ArtGalleryList", "ZZZI34",new { WhereVerify = true,ISTASKLIST=true})">
                線上藝廊任務
            </a>
        </div>
    </div>
</div>
<script language="javascript">

    $(document).ready(function () {
        $(".groupWRITING_NO").colorbox({ iframe: true, opacity: 0.5, width: "90%", height: "90%", rel: 'groupWRITING_NO' });
    });

    function funBookW() {

        if ($('#W1').length > 0) {
            $('#W1').click();
        }
        else {
            alert('無任何資料')
        }
    }
</script>