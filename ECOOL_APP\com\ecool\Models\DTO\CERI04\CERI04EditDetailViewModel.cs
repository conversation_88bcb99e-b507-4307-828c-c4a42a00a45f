﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CERI04EditDetailViewModel
    {
        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///登入帳號
        /// </summary>
        [DisplayName("登入帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        /// 原來狀態
        /// </summary>
        public bool? O_IS_PASS { get; set; }
        public string IsText { get; set; }
        /// <summary>
        /// 原來給的點數
        /// </summary>
        public short? O_CASH { get; set; }
        public string PersonText { get; set; }
        /// <summary>
        /// 這次異動狀態
        /// </summary>
        public bool? IS_PASS { get; set; }
    }
}