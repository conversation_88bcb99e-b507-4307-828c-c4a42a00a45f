﻿@model EcoolWeb.ViewModels.AWA004PrintViewModel

@using (Html.BeginCollectionItem("Chk"))
{
    @Html.CheckBoxFor(m => m.CheckBoxNo, new { @class = "css-checkbox" })
    if (Model.V004 != null)
    {
        @Html.HiddenFor(m => m.V004.TRANS_NO)
        @Html.HiddenFor(m => m.V004.USER_NO)
        @Html.HiddenFor(m => m.V004.SCHOOL_NO)
        @Html.HiddenFor(m => m.V004.SHORT_NAME)
        @Html.HiddenFor(m => m.V004.CLASS_NO)
        @Html.HiddenFor(m => m.V004.SEAT_NO)
        @Html.HiddenFor(m => m.V004.NAME)
        @Html.HiddenFor(m => m.V004.SNAME)
        @Html.HiddenFor(m => m.V004.TRANS_DATE)
        @Html.HiddenFor(m => m.V004.AWARD_NAME)
        @Html.HiddenFor(m => m.V004.CTRANS_STATUS)
        @Html.HiddenFor(m => m.V004.GOT_DATE)
        @Html.HiddenFor(m => m.V004.COST_CASH)
    }

    if (Model.V005 != null)
    {
        @Html.HiddenFor(m => m.V005.TRANS_NO)
        @Html.HiddenFor(m => m.V005.USER_NO)
        @Html.HiddenFor(m => m.V005.SCHOOL_NO)
        @Html.HiddenFor(m => m.V005.SHORT_NAME)
        @Html.HiddenFor(m => m.V005.CLASS_NO)
        @Html.HiddenFor(m => m.V005.SEAT_NO)
        @Html.HiddenFor(m => m.V005.NAME)
        @Html.HiddenFor(m => m.V005.SNAME)
        @Html.HiddenFor(m => m.V005.TRANS_DATE)
        @Html.HiddenFor(m => m.V005.AWARD_NAME)
        @Html.HiddenFor(m => m.V005.CTRANS_STATUS)
        @Html.HiddenFor(m => m.V005.GOT_DATE)
        @Html.HiddenFor(m => m.V005.COST_CASH)
    }

}