/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Italic/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-italic"],{768:[697,-500,0,-222,-74],769:[697,-500,0,-173,39],770:[694,-527,0,-251,17],771:[668,-558,0,-265,60],772:[589,-544,0,-282,54],774:[694,-515,0,-237,62],775:[669,-548,0,-165,-41],776:[669,-554,0,-251,45],778:[716,-542,0,-199,3],779:[697,-503,0,-248,65],780:[638,-502,0,-236,29]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Italic/CombDiacritMarks.js");
