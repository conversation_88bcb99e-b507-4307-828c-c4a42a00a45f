﻿@model EcoolWeb.ViewModels.AWAT15PrintViewModel


@using (Html.BeginCollectionItem("Chk"))
{

    if (Model.AWAT15 != null)
    {


        @Html.HiddenFor(m => m.CheckBoxNo)
        @Html.HiddenFor(m => m.AWAT15.AWAT15NO, new { Value = Model.AWAT15.AWAT15NO })
        @Html.HiddenFor(m => m.AWAT15.USER_NO, new { Value = Model.AWAT15.USER_NO })
        @Html.HiddenFor(m => m.AWAT15.SCHOOL_NO, new { Value = Model.AWAT15.SCHOOL_NO })
       
        @Html.HiddenFor(m => m.AWAT15.CLASS_NO, new { Value = Model.AWAT15.CLASS_NO })
        @Html.HiddenFor(m => m.AWAT15.SEAT_NO, new { Value = Model.AWAT15.SEAT_NO })
        @Html.HiddenFor(m => m.AWAT15.NAME, new { Value = Model.AWAT15.NAME })
        @Html.HiddenFor(m => m.AWAT15.SNAME, new { Value = Model.AWAT15.SNAME })
       
        @Html.HiddenFor(m => m.AWAT15.CreatDate, new { Value = Model.AWAT15.CreatDate })
        @Html.HiddenFor(m => m.AWAT15.CASH_Rank, new { Value = Model.AWAT15.CASH_Rank })

<div class="panel panel-default">
    <div class="panel-heading">
        @Model.AWAT15.CLASS_NO <span>班</span>  @Model.AWAT15.NAME <span>於</span> <font color="red">@Model.AWAT15.CreatDate</font> 升 <font color="red">@Model.AWAT15.CASH_Rank</font>
        <span>級</span>
    </div>
    <div class="panel-body">
        <div class="form-group">
            <label class="col-md-4 control-label">預計頒獎日</label>
            <div class="col-md-8">
                <div class="input-group">
                    @Html.EditorFor(m => m.ExpectedDate, new { htmlAttributes = new { @class = "form-control input-md" } })
                </div>
                @Html.ValidationMessageFor(m => m.ExpectedDate, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-4 control-label">頒獎(集合)地點</label>
            <div class="col-md-8">
                <div class="input-group">
                    @Html.EditorFor(m => m.Content, new { htmlAttributes = new { @class = "form-control input-md" ,@temp= "Content" } })
                </div>
                @Html.ValidationMessageFor(m => m.Content, "", new { @class = "text-danger" })
            </div>
        </div>
     
        <div class="form-group">
            <label class="col-md-4 control-label">PS.</label>
            <div class="col-md-8">
                <div class="input-group">
                    @Html.EditorFor(m => m.MEMO, new { htmlAttributes = new { @class = "form-control input-md", @temp = "MEMO" ,@placeholder="可空白"} })
                </div>
                
            </div>
        </div>
    </div>
</div>
    }
   
}