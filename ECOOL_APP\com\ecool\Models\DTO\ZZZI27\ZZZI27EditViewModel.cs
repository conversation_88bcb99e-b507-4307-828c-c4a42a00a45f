﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ZZZI27EditViewModel
    {
        public BDMT02 T_BDMT02 { get; set; }

        public ZZZI27SearchViewModel Search { get; set; }

        /// <summary>
        /// 載入 預設值
        /// </summary>
        public bool IsLoadingDefault { get; set; }

        public ICollection<ZZZI27DetailsViewModel> Details_List { get; set; }
    }

    public class ZZZI27SearchViewModel
    {
        public string wSCHOOL_NO { get; set; }

        public string wBRE_NO { get; set; }

        public string wDATA_CODE { get; set; }
    }

    public class ZZZI27DetailsViewModel
    {
        [DisplayName("控制項名稱")]
        public string BRE_NO { get; set; }

        [DisplayName("大類")]
        [Required]
        public string DATA_CODE { get; set; }

        [DisplayName("小類")]
        public string DATA_TYPE { get; set; }

        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        [DisplayName("流水號")]
        public string ITEM_NO { get; set; }

        [DisplayName("內容")]
        public string CONTENT_TXT { get; set; }

        [DisplayName("值")]
        public string CONTENT_VAL { get; set; }

        [DisplayName("說明")]
        public string MEMO { get; set; }
        public decimal? DelayTime { get; set; }

        /// <summary>
        /// 模式
        /// </summary>
        public string ADD_MODE { get; set; }

        /// <summary>
        /// 內容值的型態
        /// </summary>
        public string TXT_MODE { get; set; }

        /// <summary>
        /// 值的型態
        /// </summary>
        public string VAL_MODE { get; set; }

        /// <summary>
        /// 內容值是否改修改
        /// </summary>
        public string TXT_SCHOOL_SET_YN { get; set; }

        /// <summary>
        /// 值 是否改修改
        /// </summary>
        public string VAL_SCHOOL_SET_YN { get; set; }

        public string Close_YN { get; set; }
        public Nullable<byte> IS_SELECT { get; set; }
       
    }
}