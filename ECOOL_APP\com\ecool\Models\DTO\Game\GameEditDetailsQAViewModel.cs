﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public class GameEditDetailsQAViewModel
    {
        public string Html_ID { get; set; }
        public bool IsCopy { get; set; }

        public string InputType { get; set; }

        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///題目序號
        /// </summary>
        [DisplayName("題目序號")]
        public string GROUP_ID { get; set; }

        /// <summary>
        ///題目描述
        /// </summary>
        [DisplayName("題目描述")]
        [UIHint("Html")]
        [AllowHtml]
        public string G_SUBJECT { get; set; }

        /// <summary>
        ///題目排序
        /// </summary>
        [DisplayName("題目排序")]
        public int? G_ORDER_BY { get; set; }

        /// <summary>
        ///可重複闖關
        /// </summary>
        [DisplayName("可重複闖關")]
        public bool Y_REPEAT { get; set; }

        /// <summary>
        ///過場動畫秒數
        /// </summary>
        [DisplayName("過場動畫秒數")]
        public int? LOADING_TIME { get; set; }

        /// <summary>
        ///結果停留時間(秒)
        /// </summary>
        [DisplayName("結果停留時間(秒)")]
        public int? PASSED_TIME { get; set; }

        /// <summary>
        /// 回答的關卡
        /// </summary>
        public virtual ICollection<GameEditDetailsAnsLevelViewModel> AnsLeve { get; set; }
    }
}