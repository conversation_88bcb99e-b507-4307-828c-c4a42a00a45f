﻿using ECOOL_APP;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AudioController : Controller
    {
        private UserProfile user;

        // GET: Audio
        [CheckPermission(CheckBRE_NO = "ADDI01", CheckACTION_ID = "Create")] //檢查權限
        public ActionResult Index()
        {
            return PartialView();
        }

        [CheckPermission(CheckBRE_NO = "ADDI01", CheckACTION_ID = "Create")] //檢查權限
        public ActionResult FlashWavRecorder()
        {
            return PartialView();
        }

        [CheckPermission(CheckBRE_NO = "ADDI01", CheckACTION_ID = "Create")] //檢查權限
        [HttpPost]
        public ActionResult UploadAction()
        {
            string result = "error";

            if (Request.Files.Count > 0)
            {
                HttpPostedFileBase file = Request.Files[0];

                if (file.ContentLength > 0)
                {
                    try
                    {
                        DateTime dt = DateTime.Now;
                        string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
                        string USER_NO = string.Empty;

                        user = UserProfileHelper.Get();
                        if (user != null) USER_NO = user.USER_NO;

                        string fileName = dt.ToString("yyyy_MM_dd_HH_mm_ss") + "_" + SCHOOL_NO + USER_NO + ".wav";

                        string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                        if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                        if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
                        string Path = string.Format(@"{0}ADDI01Audio\{1}\{2}\Temp", Request.MapPath(UploadImageRoot), SCHOOL_NO, USER_NO);
                        if (Directory.Exists(Path) == true) Directory.Delete(Path, true);
                        if (Directory.Exists(Path) == false) Directory.CreateDirectory(Path);

                        string UpLoadFile = Path + @"\" + fileName;
                        file.SaveAs(UpLoadFile);
                        result = "ok," + fileName;
                    }
                    catch (Exception ex)
                    {
                        result = result + "," + ex.Message;
                    }
                }
            }

            return Content(result);
        }
    }
}