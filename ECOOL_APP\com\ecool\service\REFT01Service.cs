﻿using Dapper;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.service
{
    public class REFT01Service
    {
        public static void UpdateStatus(string USER_KEY,string REF_TABLE, string REF_KEY,string Sou_No, SqlConnection conn, SqlTransaction trans=null)
        {
        

            var sqlQuery = @"update REFT01_Q set STATUS =@STATUS ,REF_KEY =@Sou_No
                                    where REF_TABLE = @REF_TABLE AND REF_KEY = @REF_KEY ";
            conn.Execute(sqlQuery
           , new
           {
               STATUS = REFT01.StatusVal.RealKey,
               REF_TABLE = REF_TABLE,
               REF_KEY = REF_KEY,
               Sou_No = Sou_No
           }, trans);

            if (REF_KEY!= Sou_No)
            {
                REF_KEY = Sou_No;
            }

            string sSQL = @"
                            SELECT DISTINCT A.* 
                            FROM HRMT01 A (NOLOCK)
                            LEFT OUTER JOIN HRMT25 B  (NOLOCK) ON A.SCHOOL_NO =B.SCHOOL_NO AND A.USER_NO =B.USER_NO
                            LEFT OUTER JOIN HRMT25_UN C  (NOLOCK) ON A.USER_TYPE =C.USER_TYPE
                            WHERE A.USER_STATUS=@USER_STATUS
                            AND (
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@sys_role
                                 AND  M.USER_TYPE = A.USER_TYPE
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@role
                                AND  M.ROLE_ID =ISNULL(B.ROLE_ID,C.ROLE_ID)
                                AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@grade
                                 AND  M.GRADE = A.GRADE
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@Class
                                 AND  M.CLASS_NO = A.CLASS_NO
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@person
                                 AND  M.USER_NO = A.USER_NO
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                            )";

            var QTemp = conn.Query<HRMT01>(sSQL
                , new
                {
                    USER_STATUS = UserStaus.Enabled,
                    REF_TABLE = REF_TABLE,
                    REF_KEY = REF_KEY,
                    sys_role = APPT03_Q.BTN_TYPE_VAL.sys_role,
                    role = APPT03_Q.BTN_TYPE_VAL.role,
                    grade = APPT03_Q.BTN_TYPE_VAL.grade,
                    Class = APPT03_Q.BTN_TYPE_VAL.Class,
                    person = APPT03_Q.BTN_TYPE_VAL.person,
                }, trans);

            sqlQuery = @" Delete REFT01 Where REF_TABLE = @REF_TABLE AND REF_KEY = @REF_KEY ";
            conn.Execute(sqlQuery
           , new
           {
               REF_TABLE = REF_TABLE,
               REF_KEY = REF_KEY,
           }, trans);

            //批次id =>這次執行id 
            string BATCH_KEY = "BATCH" + DateTime.Now.ToString("yyyyMMddHHmmssfff");

            var REFT01List = QTemp.ToList()
                      .Select(x => new REFT01
                      {
                          BATCH_KEY = BATCH_KEY,
                          REF_TABLE = REF_TABLE,
                          REF_KEY = REF_KEY,
                          SCHOOL_NO = x.SCHOOL_NO,
                          USER_NO = x.USER_NO,
                          CRE_PERSON = USER_KEY,
                          CRE_DATE = DateTime.Now,
                          STATUS = REFT01.StatusVal.RealKey
                      }).ToList();

            if (REFT01List.Count()>0)
            {
                REFT01List.SqlBatchData("REFT01", conn, trans);
            }

        }
        public static void UpdateStatus(string USER_KEY, string REF_TABLE, string REF_KEY, string Sou_No, ref ECOOL_DEVEntities db)
        {
            db.Database.Connection.Open();

            var sqlQuery = @"update REFT01_Q set STATUS =@STATUS ,REF_KEY =@Sou_No
                                    where REF_TABLE = @REF_TABLE AND REF_KEY = @REF_KEY ";


            db.Database.Connection.Execute(sqlQuery
           , new
           {
               STATUS = REFT01.StatusVal.RealKey,
               REF_TABLE = REF_TABLE,
               REF_KEY = REF_KEY,
               Sou_No = Sou_No
           });

            if (REF_KEY != Sou_No)
            {
                REF_KEY = Sou_No;
            }

            string sSQL = @"
                            SELECT DISTINCT A.* 
                            FROM HRMT01 A (NOLOCK)
                            LEFT OUTER JOIN HRMT25 B  (NOLOCK) ON A.SCHOOL_NO =B.SCHOOL_NO AND A.USER_NO =B.USER_NO
                            LEFT OUTER JOIN HRMT25_UN C  (NOLOCK) ON A.USER_TYPE =C.USER_TYPE
                            WHERE A.USER_STATUS=@USER_STATUS
                            AND (
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@sys_role
                                 AND  M.USER_TYPE = A.USER_TYPE
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@role
                                AND  M.ROLE_ID =ISNULL(B.ROLE_ID,C.ROLE_ID)
                                AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@grade
                                 AND  M.GRADE = A.GRADE
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@Class
                                 AND  M.CLASS_NO = A.CLASS_NO
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR 
                                EXISTS
                                (
                                 select M.*
                                 from REFT01_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY 
                                AND M.BTN_TYPE=@person
                                 AND  M.USER_NO = A.USER_NO
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                            )";

            var QTemp = db.Database.Connection.Query<HRMT01>(sSQL
                , new
                {
                    USER_STATUS = UserStaus.Enabled,
                    REF_TABLE = REF_TABLE,
                    REF_KEY = REF_KEY,
                    sys_role = APPT03_Q.BTN_TYPE_VAL.sys_role,
                    role = APPT03_Q.BTN_TYPE_VAL.role,
                    grade = APPT03_Q.BTN_TYPE_VAL.grade,
                    Class = APPT03_Q.BTN_TYPE_VAL.Class,
                    person = APPT03_Q.BTN_TYPE_VAL.person,
                });

            sqlQuery = @" Delete REFT01 Where REF_TABLE = @REF_TABLE AND REF_KEY = @REF_KEY ";
            db.Database.Connection.Execute(sqlQuery
           , new
           {
               REF_TABLE = REF_TABLE,
               REF_KEY = REF_KEY,
           });

            //批次id =>這次執行id 
            string BATCH_KEY = "BATCH" + DateTime.Now.ToString("yyyyMMddHHmmssfff");

            var REFT01List = QTemp.ToList()
                      .Select(x => new REFT01
                      {
                          BATCH_KEY = BATCH_KEY,
                          REF_TABLE = REF_TABLE,
                          REF_KEY = REF_KEY,
                          SCHOOL_NO = x.SCHOOL_NO,
                          USER_NO = x.USER_NO,
                          CRE_PERSON = USER_KEY,
                          CRE_DATE = DateTime.Now,
                          STATUS = REFT01.StatusVal.RealKey
                      }).ToList();


          

            if (REFT01List.Count() > 0)
            {
                REFT01List.SqlBatchData("REFT01", (db.Database.Connection) as SqlConnection, null);
            }

        }
    }
}
