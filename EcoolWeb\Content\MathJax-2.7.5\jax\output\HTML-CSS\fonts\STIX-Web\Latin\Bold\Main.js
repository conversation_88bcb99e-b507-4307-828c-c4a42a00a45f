/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Latin-bold"]={directory:"Latin/Bold",family:"STIXMathJax_Latin",weight:"bold",testString:"\u00A0\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00AD\u00B2\u00B3\u00B6\u00B8\u00B9\u00BA",32:[0,0,250,0,0],160:[0,0,250,0,0],161:[501,203,333,82,252],162:[588,140,500,53,458],164:[542,10,500,-26,526],166:[691,19,220,66,154],169:[691,19,747,26,721],170:[688,-397,300,-1,301],171:[415,-36,500,23,473],173:[287,-171,333,44,287],178:[688,-275,300,0,300],179:[688,-268,300,3,297],182:[676,186,639,60,579],184:[0,218,333,68,294],185:[688,-275,300,28,273],186:[688,-397,330,18,312],187:[415,-36,500,27,477],188:[688,12,750,28,743],189:[688,12,750,-7,775],190:[688,12,750,23,733],191:[501,201,500,55,443],192:[963,0,722,9,689],193:[963,0,722,9,689],194:[954,0,722,9,689],195:[924,0,722,9,689],196:[916,0,722,9,689],197:[1000,0,722,9,689],198:[676,0,1000,4,951],199:[691,218,722,49,687],200:[963,0,667,16,641],201:[963,0,667,16,641],202:[954,0,667,16,641],203:[916,0,667,16,641],204:[963,0,389,20,370],205:[963,0,389,20,370],206:[954,0,389,20,370],207:[916,0,389,20,370],208:[676,0,722,6,690],209:[924,18,722,16,701],210:[963,19,778,35,743],211:[963,19,778,35,743],212:[954,19,778,35,743],213:[924,19,778,35,743],214:[916,19,778,35,743],216:[737,74,778,35,743],217:[963,19,722,16,701],218:[963,19,722,16,701],219:[954,19,722,16,701],220:[916,19,722,16,701],221:[963,0,722,15,699],222:[676,0,611,16,600],223:[691,12,556,19,517],224:[713,14,500,25,488],225:[713,14,500,25,488],226:[704,14,500,25,488],227:[674,14,500,25,488],228:[666,14,500,25,488],229:[752,14,500,25,488],230:[473,14,722,33,694],231:[473,218,444,25,430],232:[713,14,444,25,427],233:[713,14,444,25,427],234:[704,14,444,25,427],235:[666,14,444,25,427],236:[713,0,278,14,257],237:[713,0,278,15,258],238:[704,0,278,-29,308],239:[666,0,278,-29,310],241:[674,0,556,21,539],242:[713,14,500,25,476],243:[713,14,500,25,476],244:[704,14,500,25,476],245:[674,14,500,25,476],246:[666,14,500,25,476],248:[549,92,500,25,476],249:[713,14,556,16,538],250:[713,14,556,16,538],251:[704,14,556,16,538],252:[666,14,556,16,538],253:[713,205,500,16,482],254:[676,205,556,19,524],255:[666,205,500,16,482],256:[810,0,722,9,689],257:[600,14,500,25,488],258:[901,0,722,9,689],259:[691,14,500,25,488],260:[690,205,722,9,721],261:[473,205,500,25,569],262:[923,19,722,49,687],263:[713,14,444,25,430],264:[914,19,722,49,687],265:[704,14,444,25,430],266:[876,19,722,49,687],267:[666,14,444,25,430],268:[914,19,722,49,687],269:[704,14,444,25,430],270:[914,0,722,14,690],271:[709,14,680,25,710],272:[676,0,722,6,690],273:[676,14,556,25,534],274:[810,0,667,16,641],275:[600,14,444,25,427],276:[901,0,667,16,641],277:[691,14,444,25,427],278:[876,0,667,16,641],279:[666,14,444,25,427],280:[676,205,667,16,641],281:[473,205,444,25,435],282:[914,0,667,16,641],283:[704,14,444,25,427],284:[914,19,778,37,755],285:[704,206,500,28,483],286:[901,19,778,37,755],287:[691,206,500,28,483],288:[876,19,778,37,755],289:[666,206,500,28,483],290:[691,378,778,37,755],291:[863,206,500,28,483],292:[914,0,778,21,759],293:[914,0,556,15,534],294:[676,0,778,21,759],296:[884,0,389,14,379],297:[674,0,278,-47,318],298:[810,0,389,20,370],299:[600,0,278,-25,305],300:[900,0,389,20,370],301:[691,0,278,-11,292],302:[676,205,389,20,389],303:[691,205,278,15,321],304:[876,0,389,20,370],306:[676,96,838,20,917],307:[691,203,552,15,531],308:[914,96,500,3,479],309:[704,203,333,-57,335],310:[676,378,778,30,769],311:[676,378,556,22,543],312:[470,0,600,19,627],313:[923,0,667,19,638],314:[923,0,278,15,260],315:[676,378,667,19,638],316:[676,378,278,15,256],317:[691,0,667,19,638],318:[709,0,457,15,442],319:[676,0,667,19,638],320:[676,0,414,15,441],321:[676,0,667,18,638],322:[676,0,278,-22,303],323:[923,18,722,16,701],324:[713,0,556,21,539],325:[676,378,722,16,701],326:[473,378,556,21,539],327:[914,18,722,16,701],328:[704,0,556,21,539],329:[709,0,705,13,693],330:[676,96,732,14,712],331:[473,205,556,21,490],332:[810,19,778,35,743],333:[600,14,500,25,476],334:[901,19,778,35,743],335:[691,14,500,25,476],336:[923,19,778,35,743],337:[713,14,500,25,476],338:[684,5,1000,22,981],339:[473,14,722,22,696],340:[923,0,722,26,716],341:[713,0,444,28,434],342:[676,378,722,26,716],343:[473,378,444,28,434],344:[914,0,722,26,716],345:[704,0,444,28,434],346:[923,19,556,35,513],347:[713,14,389,25,364],348:[914,19,556,35,513],349:[704,14,389,22,361],350:[692,218,556,35,513],351:[473,218,389,25,361],352:[914,19,556,35,513],353:[704,14,389,22,361],354:[676,218,667,31,636],355:[630,218,333,19,332],356:[914,0,667,31,636],357:[709,12,415,19,445],358:[676,0,667,31,636],359:[630,12,333,17,332],360:[886,19,722,16,701],361:[674,14,556,16,538],362:[810,19,722,16,701],363:[600,14,556,16,538],364:[901,19,722,16,701],365:[691,14,556,16,538],366:[935,19,722,16,701],367:[740,14,556,16,538],368:[923,19,722,16,701],369:[713,14,556,16,538],370:[676,205,722,16,701],371:[461,205,556,16,547],372:[914,15,1000,19,981],373:[704,14,722,23,707],374:[914,0,722,15,699],375:[704,205,500,16,482],376:[876,0,722,15,699],377:[923,0,667,28,634],378:[713,0,444,21,420],379:[876,0,667,28,634],380:[666,0,444,21,420],381:[914,0,667,28,634],382:[704,0,444,21,420],383:[691,0,333,14,389],384:[676,14,553,-28,516],392:[576,14,568,30,574],400:[686,4,610,38,587],402:[706,155,500,0,498],405:[676,10,797,14,767],409:[691,0,533,12,533],410:[676,0,291,24,265],411:[666,0,536,60,526],414:[473,205,559,21,539],416:[732,19,778,35,788],417:[505,14,554,25,576],421:[673,205,550,10,515],426:[689,228,446,25,421],427:[630,218,347,18,331],429:[691,12,371,19,389],431:[810,19,796,16,836],432:[596,14,600,16,626],442:[450,237,441,9,415],443:[688,0,515,27,492],446:[541,10,527,78,449],448:[740,0,186,60,126],449:[740,0,313,60,253],450:[740,0,445,39,405],451:[691,13,333,81,251],496:[704,203,333,-57,335],506:[972,0,722,9,689],507:[923,14,500,25,488],508:[923,0,1000,4,951],509:[713,14,722,33,694],510:[923,74,778,35,743],511:[713,92,500,25,476],7808:[923,15,1000,19,981],7809:[713,14,722,23,707],7810:[923,15,1000,19,981],7811:[713,14,722,23,707],7812:[876,15,1000,19,981],7813:[666,14,722,23,707],7922:[923,0,722,15,699],7923:[713,205,500,16,482],42898:[691,19,769,27,734],64256:[691,0,610,15,666],64257:[691,0,556,14,536],64258:[691,0,556,15,535],64259:[691,0,833,15,813],64260:[691,0,833,15,812]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Latin-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/Bold/Main.js"]);
