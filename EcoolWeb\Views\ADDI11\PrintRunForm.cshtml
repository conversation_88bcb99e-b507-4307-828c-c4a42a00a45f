﻿@model global::EcoolWeb.ViewModels.ADDI11PrintRunFormViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@{
    Html.RenderAction("_RunMenu", new { NowAction = "PrintRunForm" });
}

@using (Html.BeginForm("PrintRunForm"))
{
    <div class="text-danger">
        @Html.ValidationSummary()
    </div>
    <br />
    //預設日期
    if (Model.StartDate == null || Model.EndDate == null)
    {
        int syear = DateTime.Now.Year;
        Model.StartDate = new DateTime(syear, 2, 15).Date;
        Model.EndDate = Model.StartDate.Value.AddMonths(4).AddDays(13).Date;
    }
    <div class="row">
        <div class="form-group">
            @Html.LabelFor(a => a.StartDate, htmlAttributes: new { @class = "col-md-3 control-label" })
            <div class="col-md-9">
                @Html.EditorFor(a => a.StartDate, new { htmlAttributes = new { @class = "form-control datepick", autocomplete = "off" } })
            </div>
        </div><br /><br />
        <div class="form-group">
            @Html.LabelFor(a => a.EndDate, htmlAttributes: new { @class = "col-md-3 control-label" })
            <div class="col-md-9">
                @Html.EditorFor(a => a.EndDate, new { htmlAttributes = new { @class = "form-control datepick", autocomplete = "off" } })
            </div>
        </div><br /><br />
        <div class="form-group">
            @Html.LabelFor(a => a.whereCLASS_NO, htmlAttributes: new { @class = "col-md-3 control-label" })
            <div class="col-md-9">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
            </div>
        </div>
        <br /><br />
        <div class="form-group">
            @Html.LabelFor(a => a.RunPeopleStandard, htmlAttributes: new { @class = "col-md-3 control-label" })
            <div class="col-md-9">
                @Html.EditorFor(a => a.RunPeopleStandard, new { htmlAttributes = new { @readonly = "readonly", @class = "form-control", type = "number", autocomplete = "off" } })
            </div>
        </div><br /><br />
        <div class="form-group">
            <div class="col-md-offset-3 col-md-9">
                <button type="submit" id="export" class="btn btn-default">匯出Excel檔案</button>
            </div>
        </div>
    </div>
}

@section scripts{
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>
    <script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
    @*<script src="~/Scripts/moment.js"></script>*@
    <script src="~/Scripts/moment.min.js"></script>
    <script>
        $(".datepick").datepicker();
        $("#export").on('click', function () {
            $(".validation-summary-errors").empty();
        })
    </script>
}