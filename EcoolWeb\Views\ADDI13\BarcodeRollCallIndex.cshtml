﻿@model RollCallIndexViewModel
@{
    /**/

    ViewBag.Title = "發放紙本點數";
}

<div class="Title_Secondary">發放紙本點數</div>
<br />
@Html.Partial("_Notice")

<a role="button" href='@Url.Action("Edit1",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys text-right">
    <span class="fa fa-plus" aria-hidden="true"></span>
    新增活動
</a>
<a role="button" href='https://youtu.be/7O-cd6qU0WA' class="btn btn-sm btn-sys text-right" target="_blank">

    教學影片
</a>

<br />
<B>
    說明:<br />
       <b>
           1.本模組可以產生紙本點數，再由學生利用紙張代碼「自行」轉到自己的酷幣帳戶。學生可「先登入自己的E酷幣帳號」, 然後再寫利用 酷幣秘書, 取得點數。<br />
           2.學生的操作方式在紙本獎勵單上會有詳細說明，或者網頁右上角的「?」也有操作說明影片。學生可以這樣做<br />
           (1)進自己的帳號到酷幣秘書→「取得點數」功能裡面打上代碼；<br />
           (2)掃描QRcode後，輸入帳號密碼取得酷幣；<br />
           (3)學校也可以利用網頁「右上角的發放紙本點數」按鈕，讓學生輸入紙本數字碼和感應自己的數位學生證以兌換紙本的點數。<br />
           3.列列印分為列印自選版和列印常用版。若選擇「自選版」，可以根據自己需求列印自己需要的樣式；若使用「列印常用版」，則是一張A4可以印8小張點數單。<br />
       </b>
</B>

@using (Html.BeginForm("Index1", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    <div id="PageContent">
        @Html.Action("_PageContent1", (string)ViewBag.BRE_NO)
    </div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function onBtnRollCall(Keyword) {

            var OK = confirm("您確定要進入點名模式?")

            if (OK==true)
            {
                 $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)?ROLL_CALL_ID="+Keyword)
                 $(targetFormID).submit();
            }
        }

        function FunPageProc(page) {
            if ($(targetFormID).length > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

        function doSort(SortCol) {

            var OrderByColumnName = $('#@Html.IdFor(m=>m.OrderByColumnName)').val();
            var SortType = $('#@Html.IdFor(m=>m.SortType)').val();

            $('#@Html.IdFor(m=>m.OrderByColumnName)').val(SortCol)

            if (OrderByColumnName == SortCol ) {

                if (SortType.toUpperCase()=="@PageGlobal.SortType.DESC") {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.ASC');
                }
                else {
                     $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
                }
            } else {
                 $('#@Html.IdFor(m=>m.SortType)').val('@PageGlobal.SortType.DESC');
            }

            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageContent1")',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }
    </script>
}