﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

using ECOOL_APP.EF;

namespace EcoolWeb.Models
{
    public class ADDI01CommentViewModel 
    {
        [DisplayName("問題")]
        public string Question1 { get; set; }

        [DisplayName("問題")]
        public string Question2 { get; set; }

        [Required]
        [DisplayName("留言")]
        public string UserComment { get; set; }//不要與Action同名?

        [Required]
        [DisplayName("通關數字")]
        public string UserAnswer { get; set; }

        [Required]
        [DisplayName("姓名")]
        public string NICK_NAME { get; set; }

        /// <summary>
        /// 表情符號 字典集合
        /// </summary>
        public Dictionary<string,string> Emoticon_Dictionary { get; set; }

        [DisplayName("表情符號")]
        public string EMOTICON { get; set; }

        public int COMMENT_NO { get; set; }

        public int WRITING_NO { get; set; }
        public string SCHOOL_NO { get; set; }
        public string USER_NO { get; set; }
        public string CLASS_NO { get; set; }

        public System.DateTime CRE_DATE { get; set; }
        public short CASH { get; set; }
        public string IP_ADDRESS { get; set; }

    }
}