﻿@model GameStatisticViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "Statistics" });
    }
}

@using (Html.BeginForm("Statistics", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    <div>
        @{
            Html.RenderAction("_LevelPersonCount", new { GAME_NO = Model.Search.WhereGAME_NO, IsStatistics = true });
        }
    </div>

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <button class="btn btn-default" type="button" onclick="onBack()">上一頁</button>
            </div>
        </div>
    </div>

}

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#formEdit';

          function onBack() {
            $(targetFormID).attr("action", "@Url.Action("GameIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}