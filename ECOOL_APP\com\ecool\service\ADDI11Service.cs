﻿using Dapper;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Transactions;
using System.Web;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class ADDI11Service
    {
        public int GetONE_LAP_M(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            return (db.BDMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO).Select(a => a.ONE_LAP_M).FirstOrDefault() ?? 200);
        }

        public string Numtostring(string num)
        {
            string[] cstr = { "零", "壹", "二", "三", "四", "五", "六", "七", "八", "九" };
            int len = num.Length;

            int i;

            string tmpstr, rstr, tmpstr1;

            rstr = "";

            int position = num.IndexOf("0");
            tmpstr = num.Substring(0, position);
            tmpstr1 = num.Substring(position + 1);
            rstr = string.Concat(cstr[Int32.Parse(tmpstr)] + "年" + tmpstr1, rstr);

            return rstr;
        }
        public bool SaveRunFromCard(ADDI11MyRunLogDataViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, ref List<Tuple<string, string, int>> valuesList) {


            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            List<ADDT25_IMG> listT25_I = new List<ADDT25_IMG>();
            ECOOL_DEVEntities db1 = new ECOOL_DEVEntities(); 
         //   listT25_I = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == User.SCHOOL_NO).ToList();
            listT25_I = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == SharedGlobal.ALL).ToList();
            var hrList = new List<HRMT01>();
            int ONE_LAP_M = GetONE_LAP_M(User.SCHOOL_NO, ref db);
           
            Regex NumberPattern = new Regex(@"^[0-9]+(\.[0-9]{1,1})?$");
            var hr = db.HRMT01.Where(a => a.SCHOOL_NO == data.SCHOOL_NO && a.USER_NO == data.USER_NO).FirstOrDefault();

            if (hr != null)
            {


                if (NumberPattern.IsMatch(data.LAP.ToString()) == false)
                {
                    Message = $"系統發生錯誤;原因:{hr.NAME}-輸入的圈數有問題";
                    return false;
                }

                if (Convert.ToInt32(data.LAP * ONE_LAP_M) > 3000)
                {
                    Message = $"系統發生錯誤;原因:{hr.NAME}-大於3公里";
                    return false;
                }

                //舊的
                int O_LAP_M = 0;
                decimal LAPS =0;
                int LapsM = 0;
                ADDT24  SaveUp = db.ADDT24.Where(a => a.SCHOOL_NO == data.SCHOOL_NO && a.USER_NO == data.USER_NO && a.RUN_DATE == data.RUN_DATE).FirstOrDefault();

                List<ADDT24_D_CARD> listAddt24 = new List<ADDT24_D_CARD>();
                if (SaveUp != null)
                {

                    ADDT24_D_CARD ADD24D = db1.ADDT24_D_CARD.Where(a => a.SCHOOL_NO == data.SCHOOL_NO && a.USER_NO == data.USER_NO && a.RUN_DATE == data.RUN_DATE && a.RUN_ID == data.RUN_ID).FirstOrDefault();
                    ADD24D.LAP = (decimal)(data.LAP ?? 0);
                    ADD24D.LAP_M =(int) (ONE_LAP_M * (decimal)(data.LAP?? 0));
                    O_LAP_M = (int)(SaveUp.LAP_M ?? 0);
                    db1.Entry(ADD24D).State = EntityState.Modified;

                    try
                    {
                        db1.SaveChanges();
                       
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                    listAddt24 = db.ADDT24_D_CARD.Where(a => a.SCHOOL_NO == data.SCHOOL_NO && a.USER_NO == data.USER_NO && a.RUN_DATE == data.RUN_DATE).ToList();
                    LAPS= listAddt24.Sum(x =>(decimal) x.LAP);
                    LapsM = listAddt24.Sum(x => (int)x.LAP_M);
                    SaveUp.LAP = LAPS;
                    SaveUp.LAP_M = LapsM;
                    SaveUp.CHG_PERSON = User.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;
                    SaveUp.ADDFromCard = "Y";
                    db.Entry(SaveUp).State = EntityState.Modified;
                }
                else {

                    return false;
                }
                ADDT25 SaveT25 = null;

                SaveT25 = db.ADDT25.Where(a => a.SCHOOL_NO == hr.SCHOOL_NO && a.USER_NO == hr.USER_NO).FirstOrDefault();
                if (SaveT25 != null)
                {
                    //O_LAP_M = 這個日期原先輸入的公尺
                    if (O_LAP_M != 0)
                    {
                        //扣回當初給的點數
                        var Subtraction = listT25_I.Where(a => a.RUN_TOTAL_KM > (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M) / 1000.00) && a.RUN_TOTAL_KM <= (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0)) / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                        if (Subtraction.Count() > 0)
                        {
                            foreach (var It in Subtraction)
                            {
                                if (hr.USER_TYPE == UserType.Teacher)
                                {
                                    CashHelper.TeachAddCash(User, (int)(It.CASH * -1), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString() + "_Subtract", $@"運動撲滿修改，取回原先{It.RUN_TOTAL_KM}公里，給的點數", true, DateTime.Now, ref db);
                                }
                                else
                                {
                                    CashHelper.AddCash(User, (int)(It.CASH * -1), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString() + "_Subtract", $@"運動撲滿修改，取回原先{It.RUN_TOTAL_KM}公里，給的點數", true, ref db,"","",ref valuesList);
                                }
                            }
                        }
                    }

                    //加這次因該給的點數
                    var Addition = listT25_I.Where(a => a.RUN_TOTAL_KM > (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M) / 1000.00) && a.RUN_TOTAL_KM <= (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M + SaveUp.LAP_M) / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                    if (Addition.Count() > 0)
                    {
                        foreach (var It in Addition)
                        {
                            if (hr.USER_TYPE == UserType.Teacher)
                            {
                                CashHelper.TeachAddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, DateTime.Now, ref db);
                            }
                            else
                            {
                                CashHelper.AddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, ref db,"","",ref valuesList);
                            }
                        }
                    }

                    SaveT25.RUN_TOTAL_METER = (SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M + SaveUp.LAP_M;

                    db.Entry(SaveT25).State = EntityState.Modified;
                }
                else
                {
                    SaveT25 = new ADDT25();
                    SaveT25.SCHOOL_NO = hr.SCHOOL_NO;
                    SaveT25.USER_NO = hr.USER_NO;
                    SaveT25.RUN_TOTAL_METER = SaveUp.LAP_M;

                    var Addition = listT25_I.Where(a => a.RUN_TOTAL_KM > 0 && a.RUN_TOTAL_KM <= (decimal)(SaveT25.RUN_TOTAL_METER / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                    if (Addition.Count() > 0)
                    {
                        foreach (var It in Addition)
                        {
                            if (hr.USER_TYPE  == UserType.Teacher)
                            {
                                CashHelper.TeachAddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, DateTime.Now, ref db);
                            }
                            else
                            {
                                CashHelper.AddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, ref db,"","",ref valuesList);
                            }
                        }
                    }

                    db.ADDT25.Add(SaveT25);
                }
            }
            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public bool SaveRunData(ADDI11EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, ref List<Tuple<string, string, int>> valuesList)
        {
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            List<ADDT25_IMG> listT25_I = new List<ADDT25_IMG>();

            listT25_I = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == User.SCHOOL_NO).ToList();
            var hrList = new List<HRMT01>();
            if (listT25_I.Count() == 0)
            {
                listT25_I = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == SharedGlobal.ALL).ToList();
            }
            if (data.SearchCLASS_NO == "級任導師")
            {
                hrList = db.HRMT01.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.CLASS_NO !=null && a.USER_TYPE=="T").ToList();

            }
            else if (data.SearchCLASS_NO == "非級任導師")
            {

                hrList = db.HRMT01.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.CLASS_NO == null && a.USER_TYPE == "T").ToList();
            }
            else if (data.SearchCLASS_NO == "全校老師")
            {

                hrList = db.HRMT01.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.USER_TYPE == "T").ToList();


            }
            else {


                hrList = db.HRMT01.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.CLASS_NO == data.SearchCLASS_NO).ToList();
            }
                

            int ONE_LAP_M = GetONE_LAP_M(User.SCHOOL_NO, ref db);

            Regex NumberPattern = new Regex(@"^[0-9]+(\.[0-9]{1,1})?$");

            foreach (var item in data.People)
            {
                ADDT24 SaveUp;

                var hr = hrList.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).FirstOrDefault();

                if (hr != null)
                {
                    if (NumberPattern.IsMatch(item.LAP.ToString()) == false)
                    {
                        Message = $"系統發生錯誤;原因:{hr.NAME}-輸入的圈數有問題";
                        return false;
                    }

                    if (Convert.ToInt32(item.LAP * ONE_LAP_M) > 3000)
                    {
                        Message = $"系統發生錯誤;原因:{hr.NAME}-大於3公里";
                        return false;
                    }

                    //舊的
                    int O_LAP_M = 0;

                    SaveUp = db.ADDT24.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO && a.RUN_DATE == data.RunDate).FirstOrDefault();
                           
                    if (SaveUp != null)
                    {
                        O_LAP_M = (int)(SaveUp.LAP_M ?? 0);

                        SaveUp.LAP = Convert.ToDecimal(item.LAP ?? 0);
                        SaveUp.LAP_M = Convert.ToInt32(item.LAP * ONE_LAP_M);
                        SaveUp.CHG_PERSON = User.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.ADDFromCard = "N";
                        db.Entry(SaveUp).State = EntityState.Modified;
                    }
                    else
                    {
                        SaveUp = new ADDT24();
                        SaveUp.RUN_ID = Guid.NewGuid().ToString("N");
                        SaveUp.SCHOOL_NO = hr.SCHOOL_NO;
                        SaveUp.USER_NO = hr.USER_NO;
                        SaveUp.CLASS_NO = hr.CLASS_NO;
                        SaveUp.SYEAR = (byte)SYear;
                        SaveUp.SEMESTER = (byte)Semesters;
                        SaveUp.SEAT_NO = hr.SEAT_NO;
                        SaveUp.NAME = hr.NAME;
                        SaveUp.SNAME = hr.SNAME;
                        SaveUp.RUN_DATE = data.RunDate;
                        SaveUp.ADDFromCard = "N";

                        SaveUp.LAP = Convert.ToDecimal(item.LAP ?? 0);
                        SaveUp.LAP_M = Convert.ToInt32(item.LAP * ONE_LAP_M);

                        SaveUp.CRE_PERSON = User.USER_KEY;
                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.CHG_PERSON = User.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.STATUS = ADDT24.STATUSVal.OK;

                        db.ADDT24.Add(SaveUp);
                    }

                    ADDT25 SaveT25 = null;

                    SaveT25 = db.ADDT25.Where(a => a.SCHOOL_NO == hr.SCHOOL_NO && a.USER_NO == hr.USER_NO).FirstOrDefault();

                    if (SaveT25 != null)
                    {
                        //O_LAP_M = 這個日期原先輸入的公尺
                        if (O_LAP_M != 0)
                        {
                            //扣回當初給的點數
                            var Subtraction = listT25_I.Where(a => a.RUN_TOTAL_KM > (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M) / 1000.00) && a.RUN_TOTAL_KM <= (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0)) / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                            if (Subtraction.Count() > 0)
                            {
                                foreach (var It in Subtraction)
                                {
                                    if (item.UserType == UserType.Teacher)
                                    {
                                        CashHelper.TeachAddCash(User, (int)(It.CASH * -1), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString() + "_Subtract", $@"運動撲滿修改，取回原先{It.RUN_TOTAL_KM}公里，給的點數", true, DateTime.Now, ref db);
                                    }
                                    else
                                    {
                                        CashHelper.AddCash(User, (int)(It.CASH * -1), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString() + "_Subtract", $@"運動撲滿修改，取回原先{It.RUN_TOTAL_KM}公里，給的點數", true, ref db,"","",ref valuesList);
                                    }
                                }
                            }
                        }

                        //加這次因該給的點數
                        var Addition = listT25_I.Where(a => a.RUN_TOTAL_KM > (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M) / 1000.00) && a.RUN_TOTAL_KM <= (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M + SaveUp.LAP_M) / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                        if (Addition.Count() > 0)
                        {
                            foreach (var It in Addition)
                            {
                                if (item.UserType == UserType.Teacher)
                                {
                                    CashHelper.TeachAddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, DateTime.Now, ref db);
                                }
                                else
                                {
                                    CashHelper.AddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, ref db,"","",ref valuesList);
                                }
                            }
                        }

                        SaveT25.RUN_TOTAL_METER = (SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M + SaveUp.LAP_M;

                        db.Entry(SaveT25).State = EntityState.Modified;
                    }
                    else
                    {
                        SaveT25 = new ADDT25();
                        SaveT25.SCHOOL_NO = hr.SCHOOL_NO;
                        SaveT25.USER_NO = hr.USER_NO;
                        SaveT25.RUN_TOTAL_METER = SaveUp.LAP_M;

                        var Addition = listT25_I.Where(a => a.RUN_TOTAL_KM > 0 && a.RUN_TOTAL_KM <= (decimal)(SaveT25.RUN_TOTAL_METER / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                        if (Addition.Count() > 0)
                        {
                            foreach (var It in Addition)
                            {
                                if (item.UserType == UserType.Teacher)
                                {
                                    CashHelper.TeachAddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, DateTime.Now, ref db);
                                }
                                else
                                {
                                    CashHelper.AddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, ref db,"","",ref valuesList);
                                }
                            }
                        }

                        db.ADDT25.Add(SaveT25);
                    }
                }
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }
        public bool SaveADDRun(HRMT01 hr, ADDI11IndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, ref List<Tuple<string, string, int>> valuesList)
        {



            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            List<ADDT25_IMG> listT25_I = new List<ADDT25_IMG>();

            listT25_I = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == SharedGlobal.ALL).ToList();

            ADDT24 SaveUp;
            ADDT24_D_CARD dDT24_D_CARD;
            List<ADDT24_D_CARD> ADDT24DCARDSaveUp;
        
            Regex NumberPattern = new Regex(@"^[0-9]+(\.[0-9]{1,1})?$");

            if (hr != null)
            {
                if (NumberPattern.IsMatch(model.LAP.ToString()) == false)
                {
                    Message = $"系統發生錯誤;原因:{hr.NAME}-輸入的圈數有問題";
                    return false;
                }

                if (Convert.ToInt32(model.LAP * model.ONE_LAP_M) > 3000)
                {
                    Message = $"系統發生錯誤;原因:{hr.NAME}-大於3公里";
                    return false;
                }

                //舊的
                int O_LAP_M = 0;
             
                SaveUp = db.ADDT24.Where(a => a.SCHOOL_NO == hr.SCHOOL_NO && a.USER_NO == hr.USER_NO && a.RUN_DATE == model.RunDate).FirstOrDefault();
                ADDT24DCARDSaveUp = db.ADDT24_D_CARD.Where(x => x.SCHOOL_NO == hr.SCHOOL_NO && x.USER_NO == hr.USER_NO && x.RUN_DATE == model.RunDate).ToList();
                if (SaveUp != null)
                {
                    O_LAP_M = (int)(SaveUp.LAP_M ?? 0);
                    int LAP_MADD = 0;
                    decimal LAPADD = 0;
                    if (ADDT24DCARDSaveUp.Count() > 0)
                    {

                        LAP_MADD = ADDT24DCARDSaveUp.Sum(x => (int)x.LAP_M);
                        LAPADD = ADDT24DCARDSaveUp.Sum(x => (decimal)x.LAP);
                        LAP_MADD = LAP_MADD + Convert.ToInt32(model.LAP * model.ONE_LAP_M);
                        LAPADD = LAPADD + Convert.ToDecimal(model.LAP ?? 0);
                        if (Convert.ToInt32(LAPADD * model.ONE_LAP_M) > 3000)
                        {
                            Message = $"系統發生錯誤;原因:{hr.NAME}-大於3公里";
                            return false;
                        }
                    }
                    else {
                        LAP_MADD = Convert.ToInt32(model.LAP * model.ONE_LAP_M);
                        LAPADD = Convert.ToDecimal(model.LAP ?? 0);
                        if (Convert.ToInt32(LAPADD * model.ONE_LAP_M) > 3000)
                        {
                            Message = $"系統發生錯誤;原因:{hr.NAME}-大於3公里";
                            return false;
                        }
                    }
                    dDT24_D_CARD = new ADDT24_D_CARD();
                    dDT24_D_CARD.RUN_ID = Guid.NewGuid().ToString("N");
                    dDT24_D_CARD.SCHOOL_NO = hr.SCHOOL_NO;
                    dDT24_D_CARD.USER_NO = hr.USER_NO;
                    dDT24_D_CARD.CLASS_NO = hr.CLASS_NO;
                    dDT24_D_CARD.SYEAR = (byte)SYear;
                    dDT24_D_CARD.SEMESTER = (byte)Semesters;
                    dDT24_D_CARD.SEAT_NO = hr.SEAT_NO;
                    dDT24_D_CARD.NAME = hr.NAME;
                    dDT24_D_CARD.SNAME = hr.SNAME;
                    dDT24_D_CARD.RUN_DATE = model.RunDate;
                    dDT24_D_CARD.LAP = Convert.ToDecimal(model.LAP ?? 0);
                    dDT24_D_CARD.LAP_M = Convert.ToInt32(model.LAP * model.ONE_LAP_M); ;
                    dDT24_D_CARD.CHG_PERSON = User.USER_KEY;
                    dDT24_D_CARD.CHG_DATE = DateTime.Now;
                    dDT24_D_CARD.ADDFromCard = "Y";

                    db.ADDT24_D_CARD.Add(dDT24_D_CARD);

                    SaveUp.LAP = LAPADD;
                    SaveUp.LAP_M = LAP_MADD;
                    SaveUp.CHG_PERSON = User.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;
                    SaveUp.ADDFromCard = "Y";
                    db.Entry(SaveUp).State = EntityState.Modified;
                }
                else
                {
                    int LAP_MADD = 0;
                    decimal LAPADD = 0;
                    if (ADDT24DCARDSaveUp.Count() > 0)
                    {

                        LAP_MADD = ADDT24DCARDSaveUp.Sum(x => (int)x.LAP_M);
                        LAPADD = ADDT24DCARDSaveUp.Sum(x => (decimal)x.LAP);
                        LAP_MADD = LAP_MADD + Convert.ToInt32(model.LAP * model.ONE_LAP_M);
                        LAPADD = LAPADD + Convert.ToDecimal(model.LAP ?? 0);
                    }
                    else
                    {
                        LAP_MADD = Convert.ToInt32(model.LAP * model.ONE_LAP_M);
                        LAPADD = Convert.ToDecimal(model.LAP ?? 0);
                    }
                    dDT24_D_CARD = new ADDT24_D_CARD();
                    dDT24_D_CARD.RUN_ID = Guid.NewGuid().ToString("N");
                    dDT24_D_CARD.SCHOOL_NO = hr.SCHOOL_NO;
                    dDT24_D_CARD.USER_NO = hr.USER_NO;
                    dDT24_D_CARD.CLASS_NO = hr.CLASS_NO;
                    dDT24_D_CARD.SYEAR = (byte)SYear;
                    dDT24_D_CARD.SEMESTER = (byte)Semesters;
                    dDT24_D_CARD.SEAT_NO = hr.SEAT_NO;
                    dDT24_D_CARD.NAME = hr.NAME;
                    dDT24_D_CARD.SNAME = hr.SNAME;
                    dDT24_D_CARD.RUN_DATE = model.RunDate;
                    dDT24_D_CARD.LAP = Convert.ToDecimal(model.LAP ?? 0);
                    dDT24_D_CARD.LAP_M = Convert.ToInt32(model.LAP * model.ONE_LAP_M); ;
                    dDT24_D_CARD.CHG_PERSON = User.USER_KEY;
                    dDT24_D_CARD.CHG_DATE = DateTime.Now;
                    dDT24_D_CARD.ADDFromCard = "Y";

                    db.ADDT24_D_CARD.Add(dDT24_D_CARD);


                    SaveUp = new ADDT24();
                    SaveUp.RUN_ID = Guid.NewGuid().ToString("N");
                    SaveUp.SCHOOL_NO = hr.SCHOOL_NO;
                    SaveUp.USER_NO = hr.USER_NO;
                    SaveUp.CLASS_NO = hr.CLASS_NO;
                    SaveUp.SYEAR = (byte)SYear;
                    SaveUp.SEMESTER = (byte)Semesters;
                    SaveUp.SEAT_NO = hr.SEAT_NO;
                    SaveUp.NAME = hr.NAME;
                    SaveUp.SNAME = hr.SNAME;
                    SaveUp.ADDFromCard = "Y";
                    SaveUp.RUN_DATE = model.RunDate;

                    SaveUp.LAP = LAPADD;
                    SaveUp.LAP_M = LAP_MADD;

                    SaveUp.CRE_PERSON = User.USER_KEY;
                    SaveUp.CRE_DATE = DateTime.Now;
                    SaveUp.CHG_PERSON = User.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;
                    SaveUp.STATUS = ADDT24.STATUSVal.OK;

                    db.ADDT24.Add(SaveUp);
                }

                ADDT25 SaveT25 = null;

                SaveT25 = db.ADDT25.Where(a => a.SCHOOL_NO == hr.SCHOOL_NO && a.USER_NO == hr.USER_NO).FirstOrDefault();

                if (SaveT25 != null)
                {
                    //O_LAP_M = 這個日期原先輸入的公尺
                    if (O_LAP_M != 0)
                    {
                        //扣回當初給的點數
                        var Subtraction = listT25_I.Where(a => a.RUN_TOTAL_KM > (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M) / 1000.00) && a.RUN_TOTAL_KM <= (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0)) / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                        if (Subtraction.Count() > 0)
                        {
                            foreach (var It in Subtraction)
                            {
                                if (hr.USER_TYPE == UserType.Teacher)
                                {
                                    CashHelper.TeachAddCash(User, (int)(It.CASH * -1), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString() + "_Subtract", $@"運動撲滿修改，取回原先{It.RUN_TOTAL_KM}公里，給的點數", true, DateTime.Now, ref db);
                                }
                                else
                                {
                                    CashHelper.AddCash(User, (int)(It.CASH * -1), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString() + "_Subtract", $@"運動撲滿修改，取回原先{It.RUN_TOTAL_KM}公里，給的點數", true, ref db,"","",ref valuesList);
                                }
                            }
                        }
                    }

                    //加這次因該給的點數
                    var Addition = listT25_I.Where(a => a.RUN_TOTAL_KM > (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M) / 1000.00) && a.RUN_TOTAL_KM <= (decimal)(((SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M + SaveUp.LAP_M) / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                    if (Addition.Count() > 0)
                    {
                        foreach (var It in Addition)
                        {
                            if (hr.USER_TYPE == UserType.Teacher)
                            {
                                CashHelper.TeachAddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, DateTime.Now, ref db);
                            }
                            else
                            {
                                CashHelper.AddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, ref db,"","",ref valuesList);
                            }
                        }
                    }

                    SaveT25.RUN_TOTAL_METER = (SaveT25.RUN_TOTAL_METER ?? 0) - O_LAP_M + SaveUp.LAP_M;

                    db.Entry(SaveT25).State = EntityState.Modified;
                }
                else
                {
                    SaveT25 = new ADDT25();
                    SaveT25.SCHOOL_NO = hr.SCHOOL_NO;
                    SaveT25.USER_NO = hr.USER_NO;
                    SaveT25.RUN_TOTAL_METER = SaveUp.LAP_M;

                    var Addition = listT25_I.Where(a => a.RUN_TOTAL_KM > 0 && a.RUN_TOTAL_KM <= (decimal)(SaveT25.RUN_TOTAL_METER / 1000.00) && (a.CASH ?? 0) > 0).OrderBy(b => b.RUN_TOTAL_KM).ToList();

                    if (Addition.Count() > 0)
                    {
                        foreach (var It in Addition)
                        {
                            if (hr.USER_TYPE == UserType.Teacher)
                            {
                                CashHelper.TeachAddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, DateTime.Now, ref db);
                            }
                            else
                            {
                                CashHelper.AddCash(User, (int)(It.CASH), hr.SCHOOL_NO, hr.USER_NO, "ADDI11", SaveUp.RUN_ID + "_" + It.RUN_TOTAL_KM.ToString(), $@"運動撲滿跑滿{It.RUN_TOTAL_KM}公里，給點", true, ref db,"","",ref valuesList);
                            }
                        }
                    }

                    db.ADDT25.Add(SaveT25);
                }
            }
            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }



        public ADDI11IndexViewModel GetADDListDataDetail(ADDI11IndexViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            string sSQLD = $@" select b.SCHOOL_NO,b.USER_NO, b.RUN_DATE as RUN_DATE, b.LAP as lap , b.RUN_ID, k.LAP as SUMLap ,b.CHG_DATE
                                from ADDT24_D_CARD b (nolock)
                                inner join HRMT01 c on b.SCHOOL_NO=c.SCHOOL_NO and b.USER_NO=c.USER_NO
                               inner  join ADDT24 k on c.SCHOOL_NO=k.SCHOOL_NO and c.USER_NO=k.USER_NO and b.RUN_DATE=k.RUN_DATE
                                 Where  b.SCHOOL_NO=@SCHOOL_NO and k.STATUS='{ADDT24.STATUSVal.OK}' and  CONVERT(nvarchar(6),b.RUN_DATE,112)< = CONVERT(nvarchar(6), GETDATE(), 112) AND b.ADDFromCard ='Y' AND b.LAP is not  null AND　b.LAP > 0
                                   ";
            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                sSQLD = sSQLD + " and CONVERT(nvarchar(6),b.RUN_DATE,112) = CONVERT(nvarchar(6), GETDATE(), 112) ";
            }
            if (!string.IsNullOrWhiteSpace(model.WhereStartRunDate))
            {
                sSQLD = sSQLD + " and b.RUN_DATE>=@WhereStartRunDate";
            }
            if (!string.IsNullOrWhiteSpace(model.WhereendRunDate))
            {
                sSQLD = sSQLD + " and b.RUN_DATE<=@WhereendRunDate";
            }
            if (!string.IsNullOrWhiteSpace(model.WhereUserType))
            {
                if (model.WhereUserType != "ALL")
                {
                    sSQLD = sSQLD + " and c.USER_TYPE = @WhereUserType";

                }



            }
            sSQLD = sSQLD + "  group by b.SCHOOL_NO,b.USER_NO,b.RUN_DATE,b.lap,b.RUN_ID, k.LAP,b.CHG_DATE ";

            string sSQL = $@"
                             
                                select a.SCHOOL_NO, a.USER_NO, a.NAME, a.SNAME, a.SEX,
                                a.GRADE, a.CLASS_NO, a.SEAT_NO, a.USER_STATUS,
                                a.USER_TYPE, AG.lap , AG.RUN_DATE ,AG.RUN_ID,AG.SUMLap,AG.CHG_DATE
                                from HRMT01 a (nolock)
                                JOIN ({sSQLD}) AS AG
                                ON AG.USER_NO = a.USER_NO AND  AG.SCHOOL_NO = a.SCHOOL_NO
                            
                             where USER_STATUS <> {UserStaus.Disable} AND USER_STATUS <> {UserStaus.Invalid}
                             and a.SCHOOL_NO=@SCHOOL_NO ";

            if (string.IsNullOrWhiteSpace(model.WhereUserNo) == false)
            {
                sSQL = sSQL + " and a.USER_NO =@USER_NO ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO) == false)
            {
                model.PageSize = int.MaxValue;
                sSQL = sSQL + " and a.CLASS_NO =@CLASS_NO ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereGrade) == false)
            {
                sSQL = sSQL + " and left(a.CLASS_NO,1) =@Grade ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                sSQL = sSQL + " and (a.USER_NO like @WhereKeyword or a.NAME like @WhereKeyword or a.SNAME like @WhereKeyword) ";
            }

            switch (model.OrdercColumn)
            {
                case "LAP":
                    sSQL = sSQL + " Order by AG.lap Desc , CLASS_NO, SEAT_NO ";
                    break;
                case "SEAT_NO":
                    sSQL = sSQL + "Order by SEAT_NO  Desc , AG.CHG_DATE ,AG.lap , CLASS_NO ";
                    break;
                case "CLASS_NO":
                    sSQL = sSQL + "Order by CLASS_NO  Desc  , AG.CHG_DATE , AG.lap , SEAT_NO";
                    break;
                case "CHG_DATE":
                    sSQL = sSQL + "Order by AG.CHG_DATE Desc ,  CLASS_NO , AG.lap , SEAT_NO";
                    break;
                default:
                    sSQL = sSQL + " Order by AG.CHG_DATE Desc,  CLASS_NO , AG.lap , SEAT_NO ";
                    break;
            }

            var temp = db.Database.Connection.Query<ADDI11MyRunLogDataViewModel>(sSQL
           , new
           {
               SCHOOL_NO = model.WhereSCHOOL_NO,
               USER_NO = model.WhereUserNo,
               CLASS_NO = model.WhereCLASS_NO,
               Grade = model.WhereGrade,
               WhereStartRunDate = model.WhereStartRunDate,
               WhereendRunDate = model.WhereendRunDate,
               WhereUserType = model.WhereUserType,
               WhereKeyword = '%' + model.WhereKeyword?.Trim() + '%',
           });

            if (model.IsPrint)
            {
                model.PageSize = int.MaxValue;
            }

            model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            if (model.IsPrint)
            {
                if (model.ListData.Count() > 10000)
                {
                    Message = "請縮小條件範圍，目前只顯示前10000筆";
                    model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            return model;
        }
        public ADDI11OrderListViewModel GetOrderListData(ADDI11OrderListViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            string sSQLD = $@" select b.SCHOOL_NO,b.USER_NO, MAX(RUN_DATE) as LAST_RUN_DATE, Sum(b.LAP_M) as Total_M
                                from ADDT24 b (nolock)
                                inner join HRMT01 c on b.SCHOOL_NO=c.SCHOOL_NO and b.USER_NO=c.USER_NO
                                Where  b.SCHOOL_NO=@SCHOOL_NO and b.STATUS='{ADDT24.STATUSVal.OK}' and  CONVERT(nvarchar(6),b.RUN_DATE,112)< = CONVERT(nvarchar(6), GETDATE(), 112)  and b.LAP>0
                                   ";
            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                sSQLD = sSQLD + " and CONVERT(nvarchar(6),b.RUN_DATE,112) = CONVERT(nvarchar(6), GETDATE(), 112) ";
            }
            if (!string.IsNullOrWhiteSpace(model.WhereStartRunDate))
            {
                sSQLD = sSQLD + " and b.RUN_DATE>=@WhereStartRunDate";
            }
            if (!string.IsNullOrWhiteSpace(model.WhereendRunDate))
            {
                sSQLD = sSQLD + " and b.RUN_DATE<=@WhereendRunDate";
            }
            if (!string.IsNullOrWhiteSpace(model.WhereUserType))
            {
                if (model.WhereUserType != "ALL")
                {
                    sSQLD = sSQLD + " and c.USER_TYPE = @WhereUserType";

                }
                
               

            }
                sSQLD = sSQLD + "  group by b.SCHOOL_NO,b.USER_NO ";

            string sSQL = $@"select * , RANK() OVER(ORDER BY Total_M DESC) AS RANK
                             from(
                                select a.SCHOOL_NO, a.USER_NO, a.NAME, a.SNAME, a.SEX,
                                a.GRADE, a.CLASS_NO, a.SEAT_NO, a.USER_STATUS,
                                a.USER_TYPE, AG.Total_M, AG.LAST_RUN_DATE
                                from HRMT01 a (nolock)
                                JOIN ({sSQLD}) AS AG
                                ON AG.USER_NO = a.USER_NO AND  AG.SCHOOL_NO = a.SCHOOL_NO
                             ) t
                             where USER_STATUS <> {UserStaus.Disable} AND USER_STATUS <> {UserStaus.Invalid}
                             and SCHOOL_NO=@SCHOOL_NO ";

            if (string.IsNullOrWhiteSpace(model.WhereUserNo) == false)
            {
                sSQL = sSQL + " and USER_NO =@USER_NO ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO) == false)
            {
                model.PageSize = int.MaxValue;
                sSQL = sSQL + " and CLASS_NO =@CLASS_NO ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereGrade) == false)
            {
                sSQL = sSQL + " and left(CLASS_NO,1) =@Grade ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                sSQL = sSQL + " and (USER_NO like @WhereKeyword or NAME like @WhereKeyword or SNAME like @WhereKeyword) ";
            }

            switch (model.OrdercColumn)
            {
                case "Total_M":
                    sSQL = sSQL + " Order by Total_M Desc, CLASS_NO, SEAT_NO ";
                    break;

                default:
                    sSQL = sSQL + " Order by Total_M Desc, CLASS_NO, SEAT_NO ";
                    break;
            }

            var temp = db.Database.Connection.Query<ADDI11OrderListDataViewModel>(sSQL
           , new
           {
               SCHOOL_NO = model.WhereSCHOOL_NO,
               USER_NO = model.WhereUserNo,
               CLASS_NO = model.WhereCLASS_NO,
               Grade = model.WhereGrade,
               WhereStartRunDate = model.WhereStartRunDate,
               WhereendRunDate = model.WhereendRunDate,
               WhereUserType= model.WhereUserType,
               WhereKeyword = '%' + model.WhereKeyword?.Trim() + '%',
           });

            if (model.IsPrint)
            {
                model.PageSize = int.MaxValue;
            }

            model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            if (model.IsPrint)
            {
                if (model.ListData.Count() > 10000)
                {
                    Message = "請縮小條件範圍，目前只顯示前10000筆";
                    model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            return model;
        }
        public ADDI11RunCityCount MyRunMapCountDetail(ADDI11RunMapViewModel model, ref ECOOL_DEVEntities db) {
            ADDI11RunCityCount aDDI11RunCityCount = new ADDI11RunCityCount();
            string sSQLD = $@"SELECT At25.SCHOOL_NO , At25.USER_NO , Hrt01.SNAME AS NAME ,RUN_TOTAL_METER  , Hrt01.CLASS_NO AS CLASS_NO,RUN_TOTAL_METER/1000,(select  top 1 LOCATION_NAME  from ADDT25_IMG where SCHOOL_NO='ALL'and  RUN_TOTAL_KM <=(RUN_TOTAL_METER/1000) ORDER BY   RUN_TOTAL_KM desc),Hrt01.SEAT_NO
                              FROM ADDT25 as At25
                             inner join HRMT01  as Hrt01 on At25.SCHOOL_NO=Hrt01.SCHOOL_NO and  At25.USER_NO=Hrt01.USER_NO
                                where At25.SCHOOL_NO=@SCHOOL_NO  and (select  top 1 RUN_TOTAL_KM  from ADDT25_IMG where SCHOOL_NO='ALL'and  RUN_TOTAL_KM <=(RUN_TOTAL_METER/1000) ORDER BY   RUN_TOTAL_KM desc) =@KM and Hrt01.USER_STATUS=1 ";

            if (model.OrdercColumn == "CLASS_NO")
            {
                if (model.SyntaxName == "ASC")
                {
                    sSQLD = sSQLD + " order by CLASS_NO";
                }
                else
                {
                    sSQLD = sSQLD + " order by CLASS_NO desc";
                }
            }
            if (model.OrdercColumn == "SEAT_NO")
            {
                if (model.SyntaxName == "ASC")
                {
                    sSQLD = sSQLD + " order by SEAT_NO";
                }
                else
                {
                    sSQLD = sSQLD + " order by SEAT_NO desc";
                }
            }
            else if (model.OrdercColumn == "LocalNameCount") {

            }
            else if (model.OrdercColumn == "RUN_TOTAL_METER")
            {
                if (model.SyntaxName == "ASC")
                {
                    sSQLD = sSQLD + " order by RUN_TOTAL_METER";
                }
                else
                {
                    sSQLD = sSQLD + " order by RUN_TOTAL_METER desc";
                }
            }
            var temp = db.Database.Connection.Query<ADDI11PeopleEditList>(sSQLD
         , new
         {
             SCHOOL_NO = model.WhereSCHOOL_NO,
             KM=model.WhereKM
         });

         
            List<ADDI11PeopleEditList> aDDI11PeopleEditLists = new List<ADDI11PeopleEditList>();
            aDDI11PeopleEditLists = temp.ToList();
            aDDI11RunCityCount.ADDI11PeopleEditListInfo = aDDI11PeopleEditLists;
            aDDI11RunCityCount.WhereKMS = model.WhereKM;
            aDDI11RunCityCount.WhereSCHOOL_NO = model.WhereSCHOOL_NO;
            return aDDI11RunCityCount;
        }

        public List<ADDI11RunCityCount> MyRunMapClassCount(ADDI11RunMapViewModel model, ref ECOOL_DEVEntities db)
        {
            ADDI11RunCityCount aDDI11RunCityCount = new ADDI11RunCityCount();
            string sSQLD = $@"   select SUM(meter) as CLASSSUMKM , CLASS_NOs as CLASS_NO
                                , case when CAST((select count(*) from HRMT01 where CLASS_NO=CLASS_NOs and USER_TYPE='S'and USER_STATUS=1 and SCHOOL_NO=SCHOOL_NOs) as numeric(5,3)) =0 then 0 else
								CAST(SUM(meter) /CAST((select count(*) from HRMT01 where CLASS_NO=CLASS_NOs and USER_TYPE='S'and USER_STATUS=1 and SCHOOL_NO=SCHOOL_NOs) as numeric(5,3)) as numeric(18,1)) end as AVGCLASS 
                                ,(select count(*) from HRMT01 where CLASS_NO=CLASS_NOs and USER_TYPE='S' and USER_STATUS=1 and SCHOOL_NO=SCHOOL_NOs) as CLASSCount
                                  from (
                                  SELECT RUN_TOTAL_METER/1000 as meter
                                 , hrmt01.SCHOOL_NO as SCHOOL_NOs
                                  ,hrmt01.CLASS_NO as CLASS_NOs
                                  FROM ADDT25 at25
                                  inner join HRMT01 hrmt01 on at25.SCHOOL_NO=hrmt01.SCHOOL_NO and at25.USER_NO=hrmt01.USER_NO and hrmt01.USER_TYPE='S'
                                  where hrmt01.SCHOOL_NO=@SCHOOL_NO  and USER_STATUS=1 
                                 
                                  )  as k  group by CLASS_NOs,SCHOOL_NOs order by SUM(meter) desc
                                      ";
            var temp = db.Database.Connection.Query<ADDI11RunCityCount>(sSQLD
         , new
         {
             SCHOOL_NO = model.WhereSCHOOL_NO
         });

            if (model.OrdercColumn == "CLASSSUMKM")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.CLASSSUMKM);
                }
                else
                {
                    temp = temp.OrderByDescending(a => a.CLASSSUMKM);
                }
            }
            else if (model.OrdercColumn == "AVGCLASS")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.AVGCLASS);
                }

                else
                {
                    temp = temp.OrderByDescending(X => X.AVGCLASS);
                }
            }

            List<ADDI11RunCityCount> ADDI11RunCityCountLists = new List<ADDI11RunCityCount>();
            ADDI11RunCityCountLists = temp.ToList();

            return ADDI11RunCityCountLists;
        }
        public List<ADDI11RunCityCount> MyRunMapCount(ADDI11RunMapViewModel model, ref ECOOL_DEVEntities db)
        {
            ADDI11RunCityCount aDDI11RunCityCount = new ADDI11RunCityCount();
            string sSQLD = $@" select *  from( select k.LocalName,COUNT(*) as LocalNameCount , (select  top 1 RUN_TOTAL_KM  from ADDT25_IMG where SCHOOL_NO='ALL'and LOCATION_NAME =k.LocalName and BGCOLOR=k.BGCOLOR ) as RUN_TOTAL_KM,k.BGCOLOR,k.USER_STATUS from (
                                 SELECT RUN_TOTAL_METER/1000 as meter,(select  top 1 LOCATION_NAME  from ADDT25_IMG where SCHOOL_NO='ALL'and  RUN_TOTAL_KM <=(RUN_TOTAL_METER/1000) ORDER BY   RUN_TOTAL_KM desc) as LocalName
                                ,(select  top 1 BGCOLOR  from ADDT25_IMG where SCHOOL_NO='ALL'and  RUN_TOTAL_KM <=(RUN_TOTAL_METER/1000) ORDER BY   RUN_TOTAL_KM desc) as BGCOLOR
                               ,(select top 1 USER_STATUS from HRMT01 where SCHOOL_NO=ADDT25.SCHOOL_NO and USER_NO=ADDT25.USER_NO and USER_STATUS=1)as USER_STATUS
                                 FROM ADDT25 

                                   where SCHOOL_NO=@SCHOOL_NO

                                    )  as k  group by k.LocalName,k.BGCOLOR ,k.USER_STATUS  ) as kk  	where kk.USER_STATUS is not null 
								
								group by kk.LocalName,kk.BGCOLOR,kk.USER_STATUS,kk.LocalNameCount,kk.RUN_TOTAL_KM
                             order by (select  top 1 RUN_TOTAL_KM  from ADDT25_IMG where SCHOOL_NO='ALL'and LOCATION_NAME =kk.LocalName and BGCOLOR=kk.BGCOLOR )  desc";
           
            var temp = db.Database.Connection.Query<ADDI11RunCityCount>(sSQLD
         , new
         {
             SCHOOL_NO = model.WhereSCHOOL_NO
         });

            if (model.OrdercColumn == "LocalNameCount")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.LocalNameCount);
                }
                else
                {
                    temp = temp.OrderByDescending(a => a.LocalNameCount);
                }
            }
            else if (model.OrdercColumn == "RUN_TOTAL_KM")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.RUN_TOTAL_KM);
                }

                else
                {
                    temp = temp.OrderByDescending(X => X.RUN_TOTAL_KM);
                }
            }
         
            List<ADDI11RunCityCount> ADDI11RunCityCountLists = new List<ADDI11RunCityCount>();
            ADDI11RunCityCountLists = temp.ToList();
         
            return ADDI11RunCityCountLists;
        }
        public ADDI11RunMapViewModel GetMyRunMap(ADDI11RunMapViewModel model, ref ECOOL_DEVEntities db)
        {
            model.RUN_TOTAL_METER = Math.Round((db.ADDT25.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_NO == model.WhereUSER_NO).Select(a => a.RUN_TOTAL_METER).FirstOrDefault() ?? 0) / 1000.0, 1);

            ADDT25_IMG IMG = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.RUN_TOTAL_KM <= (decimal)model.RUN_TOTAL_METER).OrderByDescending(a => a.RUN_TOTAL_KM).FirstOrDefault();

            if (IMG == null)
            {
                IMG = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == SharedGlobal.ALL && a.RUN_TOTAL_KM <= (decimal)model.RUN_TOTAL_METER).OrderByDescending(a => a.RUN_TOTAL_KM).FirstOrDefault();
                model.LOCATION_NAME = IMG.LOCATION_NAME;
            }

            if (IMG != null)
            {
                ADDT25_IMG nexttop = db.ADDT25_IMG.Where(a => a.SCHOOL_NO == SharedGlobal.ALL && a.RUN_TOTAL_KM > (decimal)model.RUN_TOTAL_METER).OrderBy(a => a.RUN_TOTAL_KM).FirstOrDefault();
                model.NextStop = nexttop.LOCATION_NAME;
                model.Range = Math.Round((double)((double)nexttop.RUN_TOTAL_KM - model.RUN_TOTAL_METER ?? 0), 2);

                model.RUN_IMG_PATH = ECOOL_APP.UrlCustomHelper.Url_Content(GetSetRunMapPath() + IMG.IMG_FOLDER + @"\" + IMG.IMG_FILENAME);
            }

            return model;
        }

        public string GetSetRunMapPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string RunMapPath = "run";
            ReturnImgUrl = $@"{UploadImageRoot}{RunMapPath}\";

            return ReturnImgUrl;
        }
        public ADDI11MyRunLogViewModel GetMyRunLogATM(ADDI11MyRunLogViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            //我的跑步累積里程 及 排名 => 僅學生
            string sSQL = "";

            if (string.IsNullOrEmpty(user?.USER_TYPE)) //酷幣運動查詢查累計里數
            {
                sSQL = $@"  select *
							   from ADDT25 (nolock)
							   where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO";
            }
            else if (user?.USER_TYPE == UserType.Student)
            {
                sSQL = $@"  select T1.*
							   from  (
							   select DENSE_RANK() OVER( ORDER BY a.RUN_TOTAL_METER desc) AS Ranking,a.*
							   from ADDT25 a  (nolock)
                               INNER JOIN HRMT01 h (nolock) on a.USER_NO = h.USER_NO and a.SCHOOL_NO = h.SCHOOL_NO
							   where a.SCHOOL_NO=@SCHOOL_NO and h.USER_TYPE ='{UserType.Student}'
							    ) as T1
                                where T1.SCHOOL_NO=@SCHOOL_NO and T1.USER_NO=@USER_NO";
            }
            else if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin) // 老師和管理者不用幫自己查排名
            {
                sSQL = $@"  select T1.*
							   from  (
							   select a.*
							   from ADDT25 a  (nolock)
                               INNER JOIN HRMT01 h (nolock) on a.USER_NO = h.USER_NO and a.SCHOOL_NO = h.SCHOOL_NO
							   where a.SCHOOL_NO=@SCHOOL_NO
							    ) as T1
                                where T1.SCHOOL_NO=@SCHOOL_NO and T1.USER_NO=@USER_NO ";
            }

            model.MyRunRank = db.Database.Connection.Query<ADDI11MyRunLogRankDataViewModel>(sSQL
            , new
            {
                SCHOOL_NO = model.WhereSCHOOL_NO,
                USER_NO = model.WhereUSER_NO,
            }).FirstOrDefault() ?? new ADDI11MyRunLogRankDataViewModel();


            return model;
        }
        public ADDI11MyRunLogViewModel GetMyRunLog(ADDI11MyRunLogViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            //我的跑步累積里程 及 排名 => 僅學生
            string sSQL = "";

            if (string.IsNullOrEmpty(user?.USER_TYPE)) //酷幣運動查詢查累計里數
            {
                sSQL = $@"  select *
							   from ADDT25 (nolock)
							   where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO";
            }
            else if (user?.USER_TYPE == UserType.Student)
            {
                sSQL = $@"  select T1.*
							   from  (
							   select DENSE_RANK() OVER( ORDER BY a.RUN_TOTAL_METER desc) AS Ranking,a.*
							   from ADDT25 a  (nolock)
                               INNER JOIN HRMT01 h (nolock) on a.USER_NO = h.USER_NO and a.SCHOOL_NO = h.SCHOOL_NO
							   where a.SCHOOL_NO=@SCHOOL_NO and h.USER_TYPE ='{UserType.Student}'
							    ) as T1
                                where T1.SCHOOL_NO=@SCHOOL_NO and T1.USER_NO=@USER_NO";
            }
            else if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin) // 老師和管理者不用幫自己查排名
            {
                sSQL = $@"  select T1.*
							   from  (
							   select a.*
							   from ADDT25 a  (nolock)
                               INNER JOIN HRMT01 h (nolock) on a.USER_NO = h.USER_NO and a.SCHOOL_NO = h.SCHOOL_NO
							   where a.SCHOOL_NO=@SCHOOL_NO
							    ) as T1
                                where T1.SCHOOL_NO=@SCHOOL_NO and T1.USER_NO=@USER_NO ";
            }

            model.MyRunRank = db.Database.Connection.Query<ADDI11MyRunLogRankDataViewModel>(sSQL
            , new
            {
                SCHOOL_NO = model.WhereSCHOOL_NO,
                USER_NO = model.WhereUSER_NO,
            }).FirstOrDefault() ?? new ADDI11MyRunLogRankDataViewModel();

            //全班的跑步平均累積里程 及 排名 => 僅學生
            sSQL = $@"select T1.*
							 from  (
							select DENSE_RANK() OVER( ORDER BY AVG(b.RUN_TOTAL_METER) desc) AS Ranking, a.SCHOOL_NO,a.CLASS_NO,AVG(b.RUN_TOTAL_METER) AVG_Total_M
							from HRMT01 a  (nolock)
							join  ADDT25 b  (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.USER_NO=b.USER_NO
							where a.SCHOOL_NO=@SCHOOL_NO and a.USER_TYPE ='{UserType.Student}'
							group by a.SCHOOL_NO,a.CLASS_NO
							) as T1
							Where  T1.SCHOOL_NO=@SCHOOL_NO and T1.CLASS_NO=@CLASS_NO ";
            model.MyClassRun = db.Database.Connection.Query<ADDI11MyClassRunLogRankDataViewModel>(sSQL
           , new
           {
               SCHOOL_NO = model.WhereSCHOOL_NO,
               CLASS_NO = model.WhereCLASS_NO,
           }).FirstOrDefault() ?? new ADDI11MyClassRunLogRankDataViewModel();

            // 我的跑步明細 => 學生、老師都可看到
            sSQL = $@"select a.* ,(select Sum(s.LAP_M)  from ADDT24 s (nolock) where s.STATUS='{ADDT24.STATUSVal.OK}' and s.SCHOOL_NO=a.SCHOOL_NO and s.USER_NO=a.USER_NO and a.RUN_DATE>=s.RUN_DATE ) as Total_M
							from ADDT24 a  (nolock)
							 where a.SCHOOL_NO=@SCHOOL_NO and a.USER_NO=@USER_NO
							 and a.STATUS='{ADDT24.STATUSVal.OK}' and a.LAP >0
							 order by a.RUN_DATE desc";

            var Temp = db.Database.Connection.Query<ADDI11MyRunLogDataViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = model.WhereSCHOOL_NO,
                 USER_NO = model.WhereUSER_NO,
             });

            model.ColumnListData = Temp.Take(7).ToList();

            model.ListData = Temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            return model;
        }

        /// <summary>
        /// 下次升級需要里數
        /// </summary>
        /// <returns></returns>
        public double? NextUpgradeRunningMiles(double? myNowRunM, ref ECOOL_DEVEntities db, out string resultStr)
        {
            decimal runKM = Convert.ToDecimal(myNowRunM == null ? 0 : myNowRunM / 1000);
            var nextStep = db.ADDT25_IMG.Where(ai => ai.RUN_TOTAL_KM > runKM).OrderBy(ai => ai.RUN_TOTAL_KM).FirstOrDefault();
            if (nextStep == null)
            {
                resultStr = "恭喜您已完成所有的跑步里程";
                return null;
            }

            double nextUpgradeMiles = Convert.ToDouble((nextStep.RUN_TOTAL_KM - runKM) * 1000);
            resultStr = $"距離下次升級還有{ nextUpgradeMiles }公尺";
            return nextUpgradeMiles;
        }

        /// <summary>
        /// 取得神密任務數量(未完成的)
        /// </summary>
        /// <param name="user"></param>
        /// <param name="db"></param>
        public int GetSecretMissionCount(UserProfile user, ref ECOOL_DEVEntities db)
        {
            if (user == null)
            {
                return 0;
            }
            var SecretMissionCount = this.GetSecretMissionTaskList(user, ref db).Where(A => A.IsTask).Count();

            return SecretMissionCount;
        }

        /// <summary>
        /// 取得這次要完全的神密任務
        /// </summary>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public ADDT25_T GetThisSecretMission(UserProfile user, ref ECOOL_DEVEntities db, string SOU_SCHOOL_NO = null, int? SOU_ITEM_NO = null)
        {
            var Temp = this.GetSecretMissionTaskList(user, ref db).AsEnumerable();

            if (!string.IsNullOrWhiteSpace(SOU_SCHOOL_NO))
            {
                Temp = Temp.Where(a => a.SCHOOL_NO == SOU_SCHOOL_NO && a.ITEM_NO == SOU_ITEM_NO);
            }
            else
            {
                Temp = Temp.Where(a => a.IsTask);
            }

            var ThisSecretMission = Temp.Select(a =>
             new ADDT25_T()
             {
                 SCHOOL_NO = a.SCHOOL_NO,
                 ITEM_NO = (int)a.ITEM_NO,
                 RUN_TOTAL_KM = (decimal)a.RUN_TOTAL_KM,
                 IS_TEXT = a.IS_TEXT,
                 TASK_DESC = a.TASK_DESC,
                 FILE_COUNT = a.FILE_COUNT
             }
             ).FirstOrDefault();

            return ThisSecretMission;
        }

        /// <summary>
        /// 取得這次你回答神密任務資料
        /// </summary>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public ADDT25_TREF GetThisAnsSecretMission(UserProfile user, ref ECOOL_DEVEntities db, string SOU_SCHOOL_NO = null, int? SOU_ITEM_NO = null)
        {
            if (user == null)
            {
                return null;
            }

            if (string.IsNullOrWhiteSpace(SOU_SCHOOL_NO) || SOU_ITEM_NO == null)
            {
                return null;
            }

            var Temp = db.ADDT25_TREF.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO
            && a.SOU_SCHOOL_NO == SOU_SCHOOL_NO && a.SOU_ITEM_NO == SOU_ITEM_NO).FirstOrDefault();

            return Temp;
        }

        public ADDT25_T GetThisSecretMission(string SCHOOL_NO, int ITEM_NO, ref ECOOL_DEVEntities db)
        {
            return db.ADDT25_T.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.ITEM_NO == ITEM_NO).FirstOrDefault();
        }

        /// <summary>
        /// 取得神密任務未完成清單
        /// </summary>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public List<ADDI11SecretMissionListViewModel> GetSecretMissionTaskList(UserProfile user, ref ECOOL_DEVEntities db)
        {
            var RUN_TOTAL_METER = db.ADDT25.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO)
                   .Select(a => a.RUN_TOTAL_METER).FirstOrDefault();

            var KM = Convert.ToDouble(RUN_TOTAL_METER ?? 0) / 1000.00;

            string SOU_SCHOOL_NO = SharedGlobal.ALL;

            if (db.ADDT25_T.Where(a => a.SCHOOL_NO == user.SCHOOL_NO).Any())
            {
                SOU_SCHOOL_NO = user.SCHOOL_NO;
            }

            string sSQL = $@"SELECT A.*
                            ,IsTask=Case When A.RUN_TOTAL_KM<=@KM and B.USER_NO IS NULL Then 1 else 0 end
                            ,IsExecutedTask=Case When A.RUN_TOTAL_KM<=@KM and B.USER_NO IS NOT NULL Then 1 else 0 end
                             FROM ADDT25_T A (NOLOCK)
                             LEFT JOIN ADDT25_TREF B  (NOLOCK) ON A.SCHOOL_NO=B.SOU_SCHOOL_NO AND A.ITEM_NO =B.SOU_ITEM_NO AND B.SCHOOL_NO='{user.SCHOOL_NO}' AND B.USER_NO='{user.USER_NO}'
                             WHERE A.SCHOOL_NO=@SOU_SCHOOL_NO
                             Order by A.ITEM_NO ";
            var SecretMission = db.Database.Connection.Query<ADDI11SecretMissionListViewModel>(sSQL, new
            {
                SOU_SCHOOL_NO,
                KM,
            }).ToList();

            return SecretMission;
        }

        /// <summary>
        /// 存檔任務資料
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveSecretMission(ADDI11SecretMissionEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db, ref string Message, ref List<Tuple<string, string, int>> valuesList)
        {
            ADDT25_T aDDT25_T = db.ADDT25_T.Where(a => a.SCHOOL_NO == model.Task.SCHOOL_NO && a.ITEM_NO == model.Task.ITEM_NO).FirstOrDefault();
            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
            using (TransactionScope tx = new TransactionScope())
            {
                ADDT25_TREF Cre = db.ADDT25_TREF.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO
                && a.SOU_SCHOOL_NO == aDDT25_T.SCHOOL_NO && a.SOU_ITEM_NO == aDDT25_T.ITEM_NO).FirstOrDefault();

                if (Cre == null)
                {
                    Cre = new ADDT25_TREF();
                    Cre.SCHOOL_NO = user.SCHOOL_NO;
                    Cre.USER_NO = user.USER_NO;
                    Cre.SOU_SCHOOL_NO = aDDT25_T.SCHOOL_NO;
                    Cre.SOU_ITEM_NO = aDDT25_T.ITEM_NO;
                    Cre.RUN_TOTAL_KM = aDDT25_T.RUN_TOTAL_KM;
                    Cre.TASK_DESC = aDDT25_T.TASK_DESC;
                    Cre.ANSWERS = model.Ans?.ANSWERS;
                    Cre.TASK_DATE = DateTime.Now;

                    UploadFileData(model.UploadFiles, Cre, ref Message);

                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return false;
                    }

                    db.ADDT25_TREF.Add(Cre);

                    string SOURCE_NO = $"{ Cre.SOU_SCHOOL_NO}_{Cre.SOU_ITEM_NO}";
                    //第一次執行才要給點
                    ECOOL_APP.CashHelper.AddCash(user, (int)aDDT25_T.CASH, Cre.SCHOOL_NO, Cre.USER_NO, "ADDI11", SOURCE_NO, "運動撲滿-神秘任務", true, ref db,"","",ref valuesList);

                    
                }
                else
                {
                    Cre.ANSWERS = model.Ans?.ANSWERS;

                    if (FileHelper.GetUploadCount(model.UploadFiles) > 0)
                    {
                        UploadFileData(model.UploadFiles, Cre, ref Message);

                        if (!string.IsNullOrWhiteSpace(Message))
                        {
                            return false;
                        }
                    }

                    db.Entry(Cre).State = System.Data.Entity.EntityState.Modified;
                }

                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                tx.Complete();
            }

            return true;
        }

        public bool UploadFileData(List<HttpPostedFileBase> UploadFiles, ADDT25_TREF Cre, ref string Message)
        {
            string UPLOAD_FILES = string.Empty;

            if (UploadFiles != null && UploadFiles?.Count > 0)
            {
                try
                {
                    string TITLE_IMG_Path = GetFilePath(Cre.SCHOOL_NO, Cre.USER_NO, Cre.SOU_SCHOOL_NO, Cre.SOU_ITEM_NO, 1) + $@"\";

                    if (Directory.Exists(TITLE_IMG_Path) == false)
                    {
                        Directory.CreateDirectory(TITLE_IMG_Path);
                    }
                    else
                    {
                        Directory.Delete(TITLE_IMG_Path, true);
                        Directory.CreateDirectory(TITLE_IMG_Path);
                    }

                    foreach (var item in UploadFiles)
                    {
                        string fileName = Path.GetFileName(item.FileName);

                        Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                        if (regexCode.IsMatch(fileName.ToLower()) == false)
                        {
                            Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                            return false;
                        }

                        if (item.ContentLength / 1024 > (1024 * 5)) // 5MB
                        {
                            Message = "上傳檔案不能超過5MB";
                            return false;
                        }

                        string UpLoadFile = TITLE_IMG_Path + "\\" + fileName;

                        if (System.IO.File.Exists(UpLoadFile))
                        {
                            System.IO.File.Delete(UpLoadFile);
                        }

                        item.SaveAs(UpLoadFile);

                        if (string.IsNullOrWhiteSpace(UPLOAD_FILES))
                        {
                            UPLOAD_FILES = fileName;
                        }
                        else
                        {
                            UPLOAD_FILES = UPLOAD_FILES + "|" + fileName;
                        }
                    }

                    Cre.UPLOAD_FILES = UPLOAD_FILES;
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 組路徑
        /// </summary>
        /// <param name="SCHOOL_NO">學校代碼</param>
        /// <param name="USER_NO">帳號</param>
        /// <param name="SOU_SCHOOL_NO">任務對應檔學校代碼</param>
        /// <param name="SOU_ITEM_NO">任務對應檔項次</param>
        /// <param name="PathType">1.取得實體路徑，2. 網頁路徑</param>
        /// <returns></returns>
        public static string GetFilePath(string SCHOOL_NO, string USER_NO, string SOU_SCHOOL_NO, int SOU_ITEM_NO, int PathType)
        {
            string ReturnImgUrl = string.Empty;

            //取得實體路徑
            if (PathType == 1)
            {
                ReturnImgUrl = HttpContext.Current.Server.MapPath(GetSetDirectoryPath());
            }
            else //虛
            {
                ReturnImgUrl = GetSetDirectoryPath();
            }

            ReturnImgUrl = ReturnImgUrl + $@"{SCHOOL_NO}\{USER_NO}\{SOU_SCHOOL_NO}{SOU_ITEM_NO}";

            return ReturnImgUrl;
        }

        private static string GetSetDirectoryPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string ADDI11 = "ADDI11";
            ReturnImgUrl = $@"{UploadImageRoot}{ADDI11}\";

            return ReturnImgUrl;
        }

        public ADDI11TotalRunLogViewModel GetTotalRunLog(ADDI11TotalRunLogViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            string whereExtension = "";
            // 全校跑步

            // 全班跑步 => 老師只能查看自己班級的跑步狀況
            if (model.runLogViewType == TotalRunLogType.本班跑步情形)
            {
                model.whereCLASS_NO =
                    string.Join(",",
                        db.HRMT03.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.TEACHER_NO == user.USER_NO).Select(c => c.CLASS_NO)
                    );
            }

            if (!string.IsNullOrEmpty(model.whereCLASS_NO))
            {
                whereExtension = " and addt24.CLASS_NO in (@CLASS_NO)";
            }
            // 找上下學期，只能看到某學期否則班級會換過
            int syear, semester;
            SysHelper.SemestersInfo(DateTime.Now, out syear, out semester);
            // 撈出一年前每天全校班級有跑步的人數
            string sSQL = $@"select addt24.SCHOOL_NO, addt24.CLASS_NO, RUN_DATE, count(*) as RUN_COUNT from
                             (
	                             select distinct SCHOOL_NO, USER_NO, CLASS_NO, RUN_DATE, STATUS, SYEAR, SEMESTER, SUM(LAP) as LAP from addt24 (nolock)
	                             group by SCHOOL_NO,USER_NO, CLASS_NO, RUN_DATE, STATUS, SYEAR, SEMESTER
                             ) as addt24
                             join HRMT01 as h01 (nolock) on h01.SCHOOL_NO = addt24.SCHOOL_NO and h01.USER_NO = addt24.USER_NO
                             where addt24.status=1 and addt24.LAP!=0 and addt24.SYEAR=@SYEAR and addt24.SCHOOL_NO=@SCHOOL_NO and h01.USER_TYPE='S' and addt24.SEMESTER=@SEMESTER
                             {whereExtension}
                             group by addt24.SCHOOL_NO, addt24.CLASS_NO, RUN_DATE
                             order by RUN_DATE desc, CLASS_NO desc";

            var totalRunLogList = db.Database.Connection.Query<ADDI11TotalRunLog>(sSQL, new
            {
                SCHOOL_NO = model.whereSCHOOL_NO,
                CLASS_NO = model.whereCLASS_NO,
                SEMESTER = semester,
                SYEAR = syear
            });
            // 撈出詳細
            foreach (var runLog in totalRunLogList)
            {
                var runners = db.ADDT24
                    .Join(db.HRMT01, a => new { a.USER_NO, a.SCHOOL_NO }, h => new { h.USER_NO, h.SCHOOL_NO }, (a, h) => new { a24 = a, h01 = h })
                    .Where(a => a.a24.SCHOOL_NO == runLog.SCHOOL_NO
                    && a.a24.CLASS_NO == runLog.CLASS_NO
                    && a.a24.STATUS == "1"
                    && a.a24.LAP > 0
                    && a.a24.RUN_DATE == runLog.RUN_DATE
                    && a.h01.USER_TYPE == UserType.Student)
                    .GroupBy(r => new { r.a24.USER_NO, r.a24.NAME })
                    .Select(g => new { NAME = g.Key.NAME, LAP = g.Sum(x => x.a24.LAP) })
                    .OrderByDescending(s => s.LAP);

                foreach (var runner in runners)
                {
                    runLog.RUNNERS.Add(new Runner()
                    {
                        NAME = runner.NAME,
                        LAP = (double)(runner.LAP ?? 0)
                    });
                }
            }

            // 年月Selector
            model.YearDropDown = totalRunLogList
                .Select(r => new { Text = r.RUN_DATE.Year + "年", Value = r.RUN_DATE.Year.ToString() })
                .Distinct()
                .Select(s => new SelectListItem() { Text = s.Text, Value = s.Value })
                .ToList();
            model.YearDropDown.Insert(0, new SelectListItem() { Text = "全部", Value = "" });

            model.MonthDropDown = new string[] { "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12" }
                .Select(m => new { Text = m + "月", Value = m.ToString() })
                .Distinct()
                .Select(s => new SelectListItem() { Text = s.Text, Value = s.Value })
                .ToList();
            model.MonthDropDown.Insert(0, new SelectListItem() { Text = "全部", Value = "" });

            model.ClassDropDown = HRMT01.GetClassListData(model.whereSCHOOL_NO, model.whereCLASS_NO, ref db);

            if (!string.IsNullOrEmpty(model.whereRUN_YEAR))
            {
                totalRunLogList = totalRunLogList.Where(tr => tr.RUN_DATE.Year.ToString() == model.whereRUN_YEAR);
            }
            if (!string.IsNullOrEmpty(model.whereRUN_MONTH))
            {
                totalRunLogList = totalRunLogList.Where(tr => tr.RUN_DATE.Month.ToString() == model.whereRUN_MONTH);
            }
            if (!string.IsNullOrEmpty(model.whereCLASS_NO))
            {
                totalRunLogList = totalRunLogList.Where(tr => tr.CLASS_NO == model.whereCLASS_NO);
            }

            model.TotalRunLogList = totalRunLogList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            return model;
        }
    }
}