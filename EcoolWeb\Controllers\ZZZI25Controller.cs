﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    ///  1.首頁圖片輪播 / 2.小小舞臺首播
    /// </summary>
    [SessionExpire]
    public class ZZZI25Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI25";

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg = string.Empty;

        /// <summary>
        /// 資料庫相關處理
        /// </summary>
        private ZZZI25Service Db = new ZZZI25Service();

        private ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_KEY = string.Empty;
        private string USER_NO = string.Empty;
        private string defaultIMG_LINK = "http://";

        /// <summary>
        /// 列表
        /// </summary>
        /// <returns></returns>
        public ActionResult Index(byte? IMG_TYPE)
        {
            ViewBag.BRE_NO = Bre_NO;

            IMG_TYPE = IMG_TYPE ?? ADDT19.IMG_TYPEVal.HomeViewImage;
            this.Shared(IMG_TYPE);

            if (IsPermission(IMG_TYPE, DefaultSCHOOL_NO, USER_NO) == false)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            if (IMG_TYPE == ADDT19.IMG_TYPEVal.HomeViewImage)
            {
                ViewBag.Panel_Title = "首播圖片輪播設定 -列表";
            }
            else
            {
                ViewBag.Panel_Title = "首播圖片輪播設定 -列表";
            }

            return View();
        }

        /// <summary>
        /// 列表部分檢示
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="STATUS"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public ActionResult _PageContent(ZZZ25IndexViewModel Data, string STATUS, int pageSize = 10)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared(Data.IMG_TYPE);

            Data.IMG_TYPE = Data.IMG_TYPE ?? ADDT19.IMG_TYPEVal.HomeViewImage;

            if (IsPermission(Data.IMG_TYPE, DefaultSCHOOL_NO, USER_NO) == false)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            int count = int.MinValue;

            if (Data.Search == null) Data.Search = new ZZZI25SearchViewModel();
            if (STATUS != null && STATUS != string.Empty) Data.Search.STATUS = STATUS;

            var DataList = Db.GetListData(Data, user, pageSize, ref count);
            if (user.USER_NO != "0000")
            {
                DataList = DataList.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).ToList();
            }
            Data.ADDT19List = DataList.ToPagedList(Data.Search.Page - 1, pageSize, count);

            ViewBag.SYSUrl = GetSYSUrl(2);

            return PartialView(Data);
        }

        public ActionResult _ImgPlay()
        {
            UserProfile user = UserProfileHelper.Get();
            string school = Request.QueryString["school"];
            List<uADDT19> Data = Db.GetGetNowDetailsData(ADDT19.IMG_TYPEVal.HomeViewImage);
            if (user != null)
            {
                Data = Data.Where(x => x.SCHOOL_NO == user.SCHOOL_NO || x.CHG_PERSON == "403605_0000").ToList();
            }
            else
            {
                Data = Data.Where(x => x.SCHOOL_NO == school || x.CHG_PERSON == "403605_0000").ToList();
            }

            ViewBag.SYSUrl = GetSYSUrl(2);

            return PartialView(Data);
        }

        /// <summary>
        /// 明細畫面
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="files"></param>
        /// <param name="DATA_TYPE"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(ZZZI25EditViewModel Data, HttpPostedFileBase files, string DATA_TYPE)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared(Data.IMG_TYPE);

            if (IsPermission(Data.IMG_TYPE, DefaultSCHOOL_NO, USER_NO) == false)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            if ((Data?.IMG_TYPE ?? ADDT19.IMG_TYPEVal.PremierViewImage) == ADDT19.IMG_TYPEVal.HomeViewImage)
            {
                ViewBag.Panel_Title = "首頁圖片輪播設定 -明細";
            }
            else
            {
                ViewBag.Panel_Title = "首播圖片輪播設定 -明細";
            }

            if (Data.uADDT19 == null) Data.uADDT19 = new uADDT19();

            if (DATA_TYPE != string.Empty && DATA_TYPE != null) //資料處理
            {
                if (Data.uADDT19.IMG_LINK == defaultIMG_LINK) Data.uADDT19.IMG_LINK = string.Empty;

                this.Save(Data, files, DATA_TYPE);

                ErrorMsg = ErrorMsg + Db.ErrorMsg;

                if (string.IsNullOrWhiteSpace(ErrorMsg))
                {
                    if (DATA_TYPE == ZZZI11Controller.DATA_TYPE.DATA_TYPE_D)
                    {
                        TempData["StatusMessage"] = "刪除成功";
                        return View("Index");
                    }
                    else
                    {
                        TempData["StatusMessage"] = "存檔成功";
                    }

                    return View("Index");
                }
                else
                {
                    TempData["StatusMessage"] = ErrorMsg;
                }
            }
            else
            {
                ModelState.Clear();

                if (Data.Search.Q_IMG_ID != string.Empty && Data.Search.Q_IMG_ID != null) //修改畫面 來源資料
                {
                    Data.uADDT19 = Db.GetGetDetailsData(Data.Search.Q_IMG_ID);
                }
                else
                {
                    Data.uADDT19.STATUS = uADDT19.STATUS_Val.STATUS_E;
                }

                if (Data.uADDT19.IMG_LINK == string.Empty || Data.uADDT19.IMG_LINK == null) Data.uADDT19.IMG_LINK = defaultIMG_LINK;
            }

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(Data.uADDT19?.IMG_ID, Data.uADDT19?.IMG_FILE);

            ViewBag.STATUS = uADDT19.GetSTATUS(Data.uADDT19?.STATUS);

            return View(Data);
        }

        private bool IsPermission(byte? IMG_TYPE, string SCHOOL_NO, string USER_NO)
        {
            if (IMG_TYPE == ADDT19.IMG_TYPEVal.HomeViewImage)
            {
                if (PermissionService.GetPermission_Use_YN("ZZZI25", "Save", DefaultSCHOOL_NO, USER_NO) == SharedGlobal.N)
                {
                    return false;
                }

                ViewBag.BtnPermission = PermissionService.GetActionPermissionForBreNO("ZZZI25", DefaultSCHOOL_NO, USER_NO);
                ViewBag.PermissionAction = "Save";
                ViewBag.PermissionBRE_NO = "ZZZI25";
            }
            else
            {
                if (PermissionService.GetPermission_Use_YN("ADDI12", "LiveEditSET", DefaultSCHOOL_NO, USER_NO) == SharedGlobal.N)
                {
                    return false;
                }

                ViewBag.BtnPermission = PermissionService.GetActionPermissionForBreNO("ADDI12", DefaultSCHOOL_NO, USER_NO);
                ViewBag.PermissionAction = "LiveEditSET";
                ViewBag.PermissionBRE_NO = "ADDI12";
            }

            return true;
        }

        #region 資料處理

        /// <summary>
        /// 資料處理
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="files"></param>
        /// <param name="DATA_TYPE"></param>
        public void Save(ZZZI25EditViewModel Data, HttpPostedFileBase files, string DATA_TYPE)
        {
            if (IsPermission(Data.IMG_TYPE, DefaultSCHOOL_NO, USER_NO) == false)
            {
                ErrorMsg = ErrorMsg + "您無異動權限";
            }
            else
            {
                if (DATA_TYPE == ZZZI11Controller.DATA_TYPE.DATA_TYPE_U || DATA_TYPE == ZZZI11Controller.DATA_TYPE.DATA_TYPE_A) //新增/修改
                {
#if DEBUG
                    var errorsaa = ModelState
                                .Where(x => x.Value.Errors.Count > 0)
                                .Select(x => new { x.Key, x.Value.Errors })
                                .ToArray();
#endif

                    if (files == null && (Data.uADDT19.IMG_FILE == null || Data.uADDT19.IMG_FILE == string.Empty)) ModelState.AddModelError("files", "請上傳圖片");
                    else if (files != null)
                    {
                        Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                        string fileNameExtension = Path.GetExtension(files.FileName);

                        if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                        {
                            ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                        }
                    }

                    if (Data.uADDT19.STATUS == "Enabled")
                    {
                        if (Db.CheckData(Data.uADDT19.IMG_DATES, Data.uADDT19.IMG_DATEE, Data.uADDT19.IMG_ID) == true)
                        {
                            if (DATA_TYPE == ZZZI11Controller.DATA_TYPE.DATA_TYPE_U)
                            {
                                ModelState.AddModelError("uADDT19.IMG_DATES", "輸入的日期期間已有資料，請停用那些資料");
                                ModelState.AddModelError("uADDT19.IMG_DATEE", "輸入的日期期間已有資料，請停用那些資料");
                            }
                            else
                            {
                                ModelState.AddModelError("uADDT19.IMG_DATES", "輸入的日期期間已有資料");
                                ModelState.AddModelError("uADDT19.IMG_DATEE", "輸入的日期期間已有資料");
                            }
                        }
                    }

                    if (ModelState.IsValid == false) ErrorMsg = "錯誤\r\n";

                    if (ModelState.IsValid) //沒有錯誤
                    {
                        Data.uADDT19.CRE_DATE = DateTime.Now;
                        Data.uADDT19.CHG_DATE = DateTime.Now;
                        Data.uADDT19.SCHOOL_NO = user.SCHOOL_NO;
                        Data.uADDT19.IMG_TYPE = Data.IMG_TYPE;
                        //Data.uADDT19
                        if (User != null)
                        {
                            Data.uADDT19.CHG_PERSON = user.USER_KEY;
                            Data.uADDT19.CRE_PERSON = user.USER_KEY;
                        }

                        if (files != null) Data.uADDT19.IMG_FILE = Path.GetFileName(files.FileName);

                        if (Data.uADDT19.IMG_ID != string.Empty && Data.uADDT19.IMG_ID != null && DATA_TYPE == ZZZI25Controller.DATA_TYPE.DATA_TYPE_U)
                        {
                            Db.UpDate(Data.uADDT19);
                        }
                        else if (DATA_TYPE == ZZZI11Controller.DATA_TYPE.DATA_TYPE_A)
                        {
                            Db.CreateDateADDT19(Data.uADDT19);
                        }

                        bool ans = DoLoadFile(Data.uADDT19.IMG_ID, files);
                    }
                }
                else if (DATA_TYPE == ZZZI25Controller.DATA_TYPE.DATA_TYPE_D) //刪除
                {
                    Db.DelAllDate(Data.uADDT19.IMG_ID);

                    if (string.IsNullOrWhiteSpace(Db.ErrorMsg))
                    {
                        string tempPath = string.Format(@"{0}{1}", GetSYSUrl(1), Data.uADDT19.IMG_ID.ToString());

                        if (Directory.Exists(tempPath) == true)
                        {
                            Directory.Delete(tempPath, true);
                        }
                    }
                }
            }
        }

        #endregion 資料處理

        #region 上傳檔案處理

        /// <summary>
        /// 上傳檔案處理
        /// </summary>
        /// <param name="IMG_ID"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        private bool DoLoadFile(string IMG_ID, HttpPostedFileBase file)
        {
            if (file == null) return false;

            try
            {
                string fileName = Path.GetFileName(file.FileName);

                string tempPath = string.Format(@"{0}{1}", GetSYSUrl(1), IMG_ID.ToString());

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                else
                {
                    //刪原檔
                    string[] tempFile = Directory.GetFiles(tempPath);

                    if (tempFile.Length >= 0)
                    {
                        foreach (var item in tempFile)
                        {
                            System.IO.File.Delete(item);
                        }
                    }
                }

                //c.縮圖
                System.Drawing.Image image = System.Drawing.Image.FromStream(file.InputStream);
                double FixWidth = 550;
                double FixHeight = 400;
                double rate = 1;
                if (image.Width > FixWidth || image.Height > FixHeight)
                {
                    if (image.Width > FixWidth) rate = FixWidth / image.Width;
                    if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    Bitmap imageOutput = new Bitmap(image, w, h);
                    imageOutput.Save(Path.Combine(tempPath, fileName), image.RawFormat);
                    imageOutput.Dispose();
                }
                else
                {
                    //直接儲存
                    file.SaveAs(Path.Combine(tempPath, fileName));
                }
                image.Dispose();
            }
            catch (Exception ex)
            {
                ErrorMsg += file.FileName + "上傳失敗" + ex + "r\n";
                return false;
            }

            return true;
        }

        #endregion 上傳檔案處理

        #region 取得圖片路徑

        /// <summary>
        /// 取得圖片路徑
        /// </summary>
        /// <param name="IMG_ID"></param>
        /// <param name="IMG_FILE"></param>
        /// <returns></returns>
        public string GetImageUrl(string IMG_ID, string IMG_FILE)
        {
            string ReturnImgUrl = string.Empty;

            if (string.IsNullOrEmpty(IMG_FILE) == false && (string.IsNullOrEmpty(IMG_ID) == false))
            {
                string tempPath = string.Format(@"{0}{1}", GetSYSUrl(1), IMG_ID.ToString());

                if (Directory.Exists(tempPath)) //判斷有此路徑
                {
                    if (System.IO.File.Exists(Path.Combine(tempPath, IMG_FILE)))  //判斷有此檔案
                    {
                        string imgUrl = string.Format(@"{0}{1}/{2}", GetSYSUrl(2), IMG_ID.ToString(), IMG_FILE);
                        ReturnImgUrl = Url.Content(imgUrl);
                    }
                }
            }

            return ReturnImgUrl;
        }

        #endregion 取得圖片路徑

        #region 取得目徑

        /// <summary>
        /// 取得目徑
        /// </summary>
        /// <param name="sTtype">1.實際 2.虛擬</param>
        /// <returns></returns>
        public string GetSYSUrl(byte sTtype)
        {
            return Db.GetSYSUrl(sTtype);
        }

        #endregion 取得目徑

        #region Shared

        private void Shared(byte? IMG_TYPE)
        {
            if (IMG_TYPE == ADDT19.IMG_TYPEVal.PremierViewImage)
            {
                ViewBag.Title = "首播圖片輪播設定";
            }
            else
            {
                ViewBag.Title = "首頁圖片輪播設定";
            }

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            ViewBag.USER_KEY = USER_KEY;
        }

        #endregion Shared

        public static class DATA_TYPE
        {
            /// <summary>
            /// 新增
            /// </summary>
            public static string DATA_TYPE_A = "A";

            /// <summary>
            /// 修改
            /// </summary>
            public static string DATA_TYPE_U = "U";

            /// <summary>
            /// 刪除
            /// </summary>
            public static string DATA_TYPE_D = "D";
        }
    }
}