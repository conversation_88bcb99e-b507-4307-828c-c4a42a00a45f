﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Text;
using System.Security.Cryptography;
using System.Net;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System.Xml;
using System.Xml.Linq;
using log4net;
using System.Net.Http;
using Newtonsoft.Json;
using System.IO;
using System.Text.RegularExpressions;

namespace EcoolWeb.Controllers
{
    public class COOCController : Controller
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        public static string HexStringFromBytes(byte[] bytes)
        {
            var sb = new StringBuilder();
            foreach (byte b in bytes)
            {
                var hex = b.ToString("x2");
                sb.Append(hex);
            }
            return sb.ToString();
        }

        public static string appKey = "squ19oBtWQZaPUZ3079bvSK0zzoOHEgK";
        public static string appSecret = "6NYAO8PEYBGwIXoJckgP8D8rLImtf5ER";

        public ActionResult SSOBACK(string access_token)
        {
            HRMT01 CheckUser = null;
            CheckUser = db.HRMT01.Include("HRMT25").Where(x => x.accessToken == access_token).FirstOrDefault();

            log4net.ILog logger = LogManager.GetLogger("COCO 親師生");
            logger.Info("親師生 access_token1：" + access_token);
            logger.Info("SSO GETUSERInfo" + access_token);
            //LogHelper.LogToTxt("親師生 access_token1：" + access_token);
            // LogHelper.AddLogToDB(user.SCHOOL_NO, user.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSOBACK 親師生", "ex.Message" + ex.Message);
            if (string.IsNullOrEmpty(access_token) || access_token == "")
            {
                return RedirectToAction("SSOLoginPage", "SSO");
            }
            else if (CheckUser != null)
            {
                ZZT08 zZT08 = new ZZT08();
                HRMT01 FindUser = null;
                FindUser = db.HRMT01.Include("HRMT25").Where(x => x.accessToken == access_token).FirstOrDefault();
            
                if (FindUser == null)
                {
                    return RedirectToAction("SSOLoginPage", "SSO");
                }
                else
                {
                    zZT08 = db.ZZT08.Where(x => x.SCHOOL_NO == FindUser.SCHOOL_NO && x.USER_NO == FindUser.USER_NO).FirstOrDefault();
                }

                //填入UserProfile
                UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                UserProfileHelper.Set(LoginUser);
                string SchoolNO1 = "";
                LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");
                SchoolNO1 = LoginUser.SCHOOL_NO;
                if (FindUser.USER_TYPE == UserType.Admin || FindUser.USER_TYPE == UserType.Teacher)
                {
                    return RedirectToAction("COOCIndex", "MobileHome", new { school = SchoolNO1 });
                }
                else if (FindUser.USER_TYPE == UserType.Student)
                {
                    return RedirectToAction("COOCStudentIndex", "MobileHome", new { school = SchoolNO1 });
                }
                else {
                    return RedirectToAction("SSOLoginPage", "SSO");
                }
            }
            else
            {
                ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                //                string agent = (Request.UserAgent + "").ToLower().Trim();
                //                if (agent == "" ||agent.IndexOf("mobile") != -1 ||agent.IndexOf("mobi") != -1 ||agent.IndexOf("nokia") != -1 ||agent.IndexOf("samsung") != -1 ||
                //agent.IndexOf("sonyericsson") != -1 ||agent.IndexOf("mot") != -1 ||agent.IndexOf("blackberry") != -1 ||agent.IndexOf("lg") != -1 ||agent.IndexOf("htc") != -1 ||agent.IndexOf("j2me") != -1 ||
                //agent.IndexOf("ucweb") != -1 ||agent.IndexOf("operamini") != -1 ||agent.IndexOf("mobi") != -1 ||agent.IndexOf("android") != -1 )
                //                {}

                using (var client = new HttpClient())
                {
                    //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenObj.access_token);
                    //LogHelper.LogToTxt("親師生 access_token：" + access_token);
                   
                    logger.Info("親師生 access_token1：" + access_token);
                    var json = client.GetStringAsync("https://cooc.tp.edu.tw/oauth/getUserinfo?access_token=" + access_token).Result;
                    //LogHelper.LogToTxt("親師生：" + json);
                    //json = "{\"uuid\":\"666527b2-a228-4d0b-8647-a4e6e57cae69\",\"user_name\":\"<EMAIL>\",\"first_name\":\"林美麗\",\"last_name\":\"\",\"email\":null,\"email_verified\":false,\"id_number\":\"A223666666\",\"sso_role\":[{\"source\":\"ldap\",\"identity\":\"09314d06-cdb7-1038-9daf-7b37af40d5d8\",\"user_name\":\"<EMAIL>\",\"first_name\":\"林美麗\",\"last_name\":\"\",\"id_number\":\"A223666666\",\"last_login_time\":\"2020-06-18T16:46:39.48843+08:00\",\"role\":[{\"role_type\":\"teacher\",\"school_code\":\"329999\",\"school_name\":\"臺北市開發測試國民小學\",\"school_type\":\"國小\",\"school_grade_year\":4,\"school_class_name\":\"403\",\"school_class_no\":\"403\",\"school_seat_no\":null,\"school_user_id\":\"zh223666\",\"school_student_number\":null,\"child_detail\":null},{\"role_type\":\"teacher\",\"school_code\":\"363602\",\"school_name\":\"臺北市大同區日新國民小學\",\"school_type\":null,\"school_grade_year\":null,\"school_class_name\":null,\"school_class_no\":null,\"school_seat_no\":null,\"school_user_id\":null,\"school_student_number\":null,\"child_detail\":null}]}]}";
                    dynamic userObj = JsonConvert.DeserializeObject<dynamic>(json);
                    string uuidstr = "";
                    try
                    {
                        if (userObj != null)
                        {

                            logger.Info("親師生 userObj ProfileObj:" + userObj);

                        //logger.Info("親師生 userObj ProfileObj:" + JsonConvert.SerializeObject(userObj));
                        }
                        uuidstr = userObj.id_number.ToString();
                    }
                    catch (Exception e)
                    {
                        return RedirectToAction("COOCGuestIndex", "HOME", new { Message = "該帳號不在系統中，請聯絡相關人員!" });
                    }
                    if (!string.IsNullOrEmpty(uuidstr))
                    {
                        string idcard = "";
                        idcard = uuidstr;
                        HRMT01 FindUser = null;
                        string SchoolNO = "";
                        using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                        {
                            //db.BDMT03.Where(x => x.SCHOOL_NO == str_input[0]).Select(x=>x.SCHOOL_NO.FirstOrDefault();
                            logger.Info("親師生 單一簽入：" + idcard);
                            FindUser = db.HRMT01.Include("HRMT25").Where(user => user.IDNO == idcard).FirstOrDefault();
                            if (FindUser == null)
                            {
                                return RedirectToAction("COOCGuestIndex", "HOME", new { Message = "該帳號不在系統中，請聯絡相關人員!" });
                            }
                        }
                        //填入UserProfile
                        UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                        UserProfileHelper.Set(LoginUser);
                        LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");

                        if (FindUser.USER_TYPE == UserType.Admin || FindUser.USER_TYPE == UserType.Teacher)
                        {
                            return RedirectToAction("COOCIndex", "MobileHome", new { school = SchoolNO });
                        }
                        else if (FindUser.USER_TYPE == UserType.Student)
                        {
                            return RedirectToAction("COOCStudentIndex", "MobileHome", new { school = SchoolNO });
                        }
                    }
                    return View();
                }
            }
        }

        //// GET: COOC
        //public ActionResult SSOBACK(string code, string series)
        //{
        //    //http://ecc.hhups.tp.edu.tw/Ecoo1Web/COOC/SSOBACK?code=HjKNV8&series=37BC329FC38E4516A8EABF453D15EF8C

        //    string idcard = string.Empty;
        //    string input = string.Format("client_id={0}&client_secret={1}&grant_type=authorization_code&code={2}&redirect_uri=http://ecc.hhups.tp.edu.tw/EcoolWeb/COOC/SSOBACK", appKey, appSecret, code);

        //    string input2 = string.Format("client_id={0}&client_secret={1}&grant_type=refresh_token&refresh_token={2}", appKey, appSecret, code);

        //    WebClient wc = new WebClient();
        //    wc.Encoding = Encoding.UTF8;
        //    wc.Headers[HttpRequestHeader.ContentType] = "application/x-www-form-urlencoded";
        //    WebClient wc2 = new WebClient();
        //    wc2.Encoding = Encoding.UTF8;
        //    wc2.Headers[HttpRequestHeader.ContentType] = "application/x-www-form-urlencoded";

        //    string tokenResult = "";
        //    string userInfo = "";
        //    try
        //    {
        //        tokenResult = wc.UploadString(@"https://sso.tp.edu.tw/oauth2/oauth/token", input);
        //        //tokenResult= wc.DownloadString(@"https://sso.tp.edu.tw/oauth2/oauth/token?" + input);
        //        logger.Info("單一簽入tokenResult：" + tokenResult);
        //        Newtonsoft.Json.Linq.JObject jo = Newtonsoft.Json.Linq.JObject.Parse(tokenResult);
        //        string access_token = jo["access_token"].ToString();

        //        string COOC_IN = appKey + "accessToken" + access_token + appSecret;

        //        SHA1 sha1 = new SHA1CryptoServiceProvider();//建立一個SHA1
        //        byte[] source = Encoding.UTF8.GetBytes(COOC_IN);//將字串轉為Byte[]
        //        byte[] crypto = sha1.ComputeHash(source);//進行SHA1加密
        //        //string result = Convert.ToBase64String(crypto);//把加密後的字串從Byte[]轉為字串
        //        string sign = HexStringFromBytes(crypto).ToUpper();

        //        string url_user = "accessToken=" + access_token + "&appKey=" + appKey + "&sign=" + sign;
        //        userInfo = wc2.UploadString(@"https://sso.tp.edu.tw/accountservice/cloudservice/account/userprofile/get.so", url_user);

        //        logger.Info("單一簽入：" + userInfo);

        //        XDocument doc = XDocument.Load(new System.IO.StringReader(userInfo));
        //        IEnumerable<XElement> allEle = doc.Elements("info");
        //        idcard = doc.Element("info").Element("body").Element("info").Element("idcard").Value;
        //    }
        //    catch (Exception err)
        //    {
        //        System.Diagnostics.Debug.WriteLine(tokenResult);
        //        System.Diagnostics.Debug.WriteLine(err.Message);
        //        logger.Info("單一簽入ERR：" + err.Message);
        //    }
        //    finally
        //    {
        //        wc2.Dispose();
        //        wc.Dispose();
        //    }
        //    HRMT01 FindUser = null;
        //    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
        //    using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
        //    {
        //        logger.Info("單一簽入：" + idcard);
        //        FindUser = db.HRMT01.Include("HRMT25").Where(user => user.IDNO == idcard).FirstOrDefault();
        //        if (FindUser == null)
        //        {
        //            return RedirectToAction("GuestIndex", "HOME", new { school = SchoolNO });
        //        }
        //    }
        //    //填入UserProfile
        //    UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
        //    UserProfileHelper.Set(LoginUser);
        //    LoginUser.SSO_series = series;
        //    LoginUser.SSO_Code = code;

        //    LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");

        //    string ActionName = string.Empty, ControllerName = string.Empty;
        //    HomeController.NextAction(LoginUser, out ActionName, out ControllerName);

        //    if (ActionName == "Edit_last" && ControllerName == "ZZZI28")
        //    {
        //        return RedirectToAction(ActionName, ControllerName, new { STUDENT_USER_NO = LoginUser.USER_NO.Substring(1) });
        //    }
        //    else if (ActionName == "MODIFY" && ControllerName == "ZZT08")
        //    {
        //        return RedirectToAction(ActionName, ControllerName);
        //    }

        //    return RedirectToAction(ActionName, ControllerName, new { school = SchoolNO });

        //    return View();
        //}
    }
}