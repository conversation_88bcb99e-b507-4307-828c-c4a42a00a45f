﻿@model GameBuskerAddViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

<script src="~/Scripts/jquery.simple.timer.js?ver=2"></script>
<span class="eachtimer" data-seconds-left=6 style="display:none"></span>
@using (Html.BeginForm("BuskerAddView", "Game", FormMethod.Post, new { name = "form1", id = "form1" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)
}

<div style="height:calc(10vh)"></div>
<div class="row">
    <div class="col-md-6 col-md-offset-3">
        <div class="login-panel panel panel-danger">
            <div class="panel-heading">
                <h1 class="panel-title text-center">訊息</h1>
            </div>
            <div class="panel-body">
                <h1 style="color:red;text-align:center">
                    <strong> @Html.Raw(HttpUtility.HtmlDecode(Model?.Message))</strong>
                </h1>
                @if (Model.IsOK)
                {
                    <br />
                    <div class="table-responsive">
                        <div class="css-table" style="width:92%;">
                            <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                                <div class="th" style="text-align:center">
                                    數位學生證卡號/QR Code 申請的電話號碼
                                </div>
                                <div class="th" style="text-align:center">
                                    姓名
                                </div>
                            </div>
                            <div id="editorRows" class="tbody">
                                @if (Model.Details != null)
                                {
                                    foreach (var Item in Model.Details)
                                    {
                                        <div class="tr">
                                            <div class="td" style="text-align:center">
                                                @Item.GAME_USER_ID
                                            </div>

                                            <div class="td" style="text-align:center">
                                                @Item.NAME
                                            </div>
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                    <br />
                    <div style="margin: 0px auto;text-align:center">
                        <img src="@Model.TITLE_IMG" style="max-height:calc(30vh)" />
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1'

        $('.eachtimer').startTimer({
            onComplete: function (element) {
                funGetExchange()
            },
        });

        function funGetExchange() {
            $(targetFormID).submit();
        }
    </script>
}