﻿using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.DTO.AWAT14;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWAT15PrintViewModel
    {

        public bool CheckBoxNo { get; set; }

        public VAWAT15 AWAT15 { get; set; }
        
      
        /// <summary>
        /// 預計日期
        /// </summary>
        public Nullable<System.DateTime> ExpectedDate { get; set; }

        /// <summary>
        /// 內容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 備註部分
        /// </summary>
        public string MEMO { get; set; }
    }
}


