{"version": 3, "sources": ["eccATM/_bs4-variables.scss", "eccATM/_bs4-custom.scss", "../../node_modules/bootstrap/scss/_root.scss", "../../node_modules/bootstrap/scss/_reboot.scss", "../../node_modules/bootstrap/scss/_variables.scss", "ecc-ATM.scss", "../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../node_modules/bootstrap/scss/_type.scss", "../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../node_modules/bootstrap/scss/mixins/_image.scss", "../../node_modules/bootstrap/scss/_images.scss", "../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../node_modules/bootstrap/scss/_code.scss", "../../node_modules/bootstrap/scss/_grid.scss", "../../node_modules/bootstrap/scss/mixins/_grid.scss", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../node_modules/bootstrap/scss/mixins/_grid-framework.scss", "../../node_modules/bootstrap/scss/_tables.scss", "../../node_modules/bootstrap/scss/mixins/_table-row.scss", "../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../node_modules/bootstrap/scss/_forms.scss", "../../node_modules/bootstrap/scss/_functions.scss", "../../node_modules/bootstrap/scss/mixins/_transition.scss", "../../node_modules/bootstrap/scss/mixins/_forms.scss", "../../node_modules/bootstrap/scss/mixins/_gradients.scss", "../../node_modules/bootstrap/scss/_buttons.scss", "../../node_modules/bootstrap/scss/mixins/_buttons.scss", "../../node_modules/bootstrap/scss/_button-group.scss", "../../node_modules/bootstrap/scss/_input-group.scss", "../../node_modules/bootstrap/scss/_custom-forms.scss", "../../node_modules/bootstrap/scss/utilities/_align.scss", "../../node_modules/bootstrap/scss/mixins/_background-variant.scss", "../../node_modules/bootstrap/scss/utilities/_background.scss", "../../node_modules/bootstrap/scss/utilities/_borders.scss", "../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../node_modules/bootstrap/scss/utilities/_display.scss", "../../node_modules/bootstrap/scss/utilities/_embed.scss", "../../node_modules/bootstrap/scss/utilities/_flex.scss", "../../node_modules/bootstrap/scss/utilities/_float.scss", "../../node_modules/bootstrap/scss/utilities/_interactions.scss", "../../node_modules/bootstrap/scss/utilities/_overflow.scss", "../../node_modules/bootstrap/scss/utilities/_position.scss", "../../node_modules/bootstrap/scss/mixins/_screen-reader.scss", "../../node_modules/bootstrap/scss/utilities/_screenreaders.scss", "../../node_modules/bootstrap/scss/utilities/_shadows.scss", "../../node_modules/bootstrap/scss/utilities/_sizing.scss", "../../node_modules/bootstrap/scss/utilities/_spacing.scss", "../../node_modules/bootstrap/scss/utilities/_stretched-link.scss", "../../node_modules/bootstrap/scss/utilities/_text.scss", "../../node_modules/bootstrap/scss/mixins/_text-truncate.scss", "../../node_modules/bootstrap/scss/mixins/_text-emphasis.scss", "../../node_modules/bootstrap/scss/mixins/_text-hide.scss", "../../node_modules/bootstrap/scss/utilities/_visibility.scss", "font-awesome/font-awesome.scss", "font-awesome/_path.scss", "font-awesome/_core.scss", "font-awesome/_larger.scss", "font-awesome/_fixed-width.scss", "font-awesome/_list.scss", "font-awesome/_bordered-pulled.scss", "font-awesome/_animated.scss", "font-awesome/_rotated-flipped.scss", "font-awesome/_mixins.scss", "font-awesome/_stacked.scss", "font-awesome/_icons.scss", "font-awesome/_variables.scss", "eccATM/_typography.scss", "eccATM/_icon.scss", "eccATM/_jquery.jConveyorTicker.min.scss"], "names": [], "mappings": "AAgCA,UACE,iBAAA,QACA,MAAA,KACD;;;;;AC9BE,MCDC,OAAA,QAAA,SAAA,QAAA,SAAA,QAAA,OAAA,QAAA,MAAA,QAAA,SAAA,QAAA,SAAA,QAAA,QAAA,QAAA,OAAA,QAAA,OAAA,QAAA,QAAA,KAAA,OAAA,QAAA,YAAA,QAIA,UAAA,QAAA,YAAA,QAAA,UAAA,QAAA,OAAA,QAAA,UAAA,QAAA,SAAA,QAAA,QAAA,QAAA,OAAA,QAAA,QAAA,QAAA,WAAA,QAAA,eAAA,QAAA,SAAA,QAIA,gBAAA,EAAA,gBAAA,MAAA,gBAAA,MAAA,gBAAA,OAAA,gBAAA,OAKF,yBAAA,OAAA,CAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,QAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,kBAAwB,wBAAA,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,UACD,EAAA,QAAA,SCExB,mBAAA,WAAA,WAAA,WACa,KACb,YAAA,WAGc,YAAA,KACA,yBAAA,KACa,4BAAA,YCVjB,QAAA,MAAA,WAAA,OAAA,OAAA,OAAA,OAAA,KAAA,IAAA,QDiB4D,QAAA,MAC5D,KACV,OAAA,EAUC,YAAA,OAAA,CAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,kBElCF,UAAA,KCmHM,YAAA,IH9EJ,YAAA,IACA,MAAA,QACA,WAAA,KACA,iBAAA,KACA,0CAYwB,QAAA,YACf,GAAA,mBAAA,YAAA,WAAA,YAUG,OAAA,EAAW,SAAA,QAEvB,GAAA,GAAA,GAAA,GAAA,GAAA,GAaQ,WAAA,EACR,cAAA,MACA,EAAA,WAAA,EAQA,cAAA,KACA,0BAAA,YAYE,gBAAA,UACe,wBAAA,UAAA,OAAA,gBAAA,UAAA,OACA,OAAA,KACjB,cAAA,EACA,iCAAA,KAAA,yBAAA,KACA,QAA0B,cAAA,KAI1B,WAAA,OACA,YAAA,QACA,GAAA,GAAA,GAAoB,WAAA,EAMpB,cAAA,KACA,MAAA,MAAA,MAAA,MAKA,cAAA,EAEA,GAAA,YAAA,IAIA,GAAA,cAAA,MAIA,YAAA,EACA,WACD,OAAA,EAAA,EAAA,KAGO,EAAA,OAAU,YAAA,OAKhB,MCoI4B,UAAA,IEzN1B,IAAA,IAAU,SAAA,SHmGJ,UAAA,IGnGN,YAAA,EHqGF,eAAA,SACc,IAAE,OAAA,OAGZ,IAAQ,IAAA,MACR,EAAA,MAAA,QAQJ,gBAAA,KACA,iBAAA,YACkB,QAAA,MAAA,QAGhB,gBAAA,UCbsC,2BDuBxB,MAAA,QAChB,gBAAA,KACA,iCAFgB,MAAA,QAKd,gBAAA,KACA,KAAA,IAAA,IAAA,KAUA,YAAA,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,UC4DqG,UAAA,IE7MrG,IAAA,WAAA,EH0JF,cAAA,KAEA,SAAA,KAEA,mBAAA,UAGoB,OAAA,OAAA,EAAA,EAAA,KAUd,IAAE,eAAA,OASR,aAAA,KACA,IAAY,SAAA,OAMZ,eAAA,OACA,MAAgB,gBAAA,SASD,QAAE,YAAA,OAIjB,eAAA,OACA,MAAA,QACA,WAAA,KACA,aAAA,OACA,GAAc,WAAA,QASd,WAAA,qBACY,MAAA,QAAA,aAUH,cAAA,MACT,OC2JsC,cAAA,EDnJtC,aACD,QAAA,IAAA,OAOU,QAAA,IAAA,KAAA,yBACA,OAAA,MAAA,SAAA,OAAA,SAMH,OAAA,EACA,YAAA,QAEN,UAAA,QG5PE,YAAA,QH8PF,OAAA,MACD,SAAA,QAIC,OAAA,OACD,eAAA,KAIC,cAMI,OAAA,QACJ,OAAQ,UAAA,OAOR,cAAA,aAAA,cAAA,OAUI,mBAAA,OACJ,6BAAA,4BAAA,6BAAA,sBASQ,OAAA,QACJ,gCAAA,+BAAA,gCAAA,yBASS,QAAA,EACb,aAAA,KACA,qBAAA,kBAIS,mBAAA,WAAA,WAAA,WACG,QAAA,EACZ,SAAU,SAAA,KAKV,OAAA,SAEM,SAAU,UAAA,EAShB,QAAA,EAEA,OAAA,EAAU,OAAA,EACD,OACH,QAAA,MAMN,MAAA,KACA,UAAA,KACA,QAAA,EACA,cAAA,MACA,UAAA,OGnSI,YAAA,QHqSJ,MAAA,QACA,YAAA,OACA,SAAmB,eAAA,SAIL,yCAAA,yCAKD,OAAA,KACb,cAGI,eAAA,KAKJ,mBAAA,KACA,yCAOa,mBAAA,KACb,6BACD,KAAA,QAQC,mBAAA,OACA,OAAoB,QAAA,aAQX,QAAA,QAAA,UAIA,OAAA,QACT,SAAe,QAAA,KAIf,SAAa,QAAA,eAMJ,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GI3dI,cAAA,MACb,YAAA,IAEA,YAAA,IACA,IAAA,GHqS4B,UAAA,OEjLxB,IAAA,GAtCW,UAAA,KAsCX,IAAA,GAtCW,UAAA,QAsCX,IAAA,GAtCW,UAAA,OAsCX,IAAA,GAtCW,UAAA,QAsCX,IAAA,GAtCW,UAAA,KAsCX,MAtCW,UAAA,QAsCX,YAAA,ICvGJ,WACD,UAAA,KDsGK,YAAA,ICjGJ,YAAA,IACA,WACD,UAAA,OD+FK,YAAA,IC5FJ,YAAA,IACA,WACD,UAAA,OD0FK,YAAA,ICvFJ,YAAA,IACA,WACD,UAAA,ODqFK,YAAA,IClFJ,YAAA,IACA,GAAA,WAAA,KASA,cAAA,KACA,OAAA,EH+EW,WAAA,IAAA,MAAA,eAtHF,OAAA,MG0CV,UAAA,IDaG,YAAA,ICHF,MAAA,KACD,QAAA,KAIC,iBAAA,QACA,eACD,aAAA,ECxEC,WAAA,KACA,aDgFD,aAAA,ECjFC,WAAA,KACA,kBDqFD,QAAA,aAEU,mCAEH,aAAA,MACJ,YACD,UAAA,IDxBC,eAAA,UCmCc,YACjB,cAAA,KAIC,UAAA,QDeI,mBCbL,QAAA,MAGC,UAAA,ID7CE,MAAA,QC+CF,2BAHF,QAAA,aAMa,WACV,UAAA,KEhHD,OAAA,KAGA,eCLD,QAAA,OAKC,iBAAA,KACA,OAAA,IAAA,MAAA,QACgC,cAAA,OCE9B,UAAA,KFPF,OAAA,KAGA,QAAQ,QAAA,aCgBC,YACV,cAAA,MAGC,YAAA,EACA,gBACD,UAAA,IJoCG,MAAA,QIhCF,KN3BS,UAAA,ME2DP,MAAA,QMrEF,UAAA,WACW,OAAA,MAAA,QAIT,IAAO,QAAA,MAAA,MRwlCyB,UAAA,MExhChC,MAAA,KMxDF,iBAAA,QACA,cAAA,MDCE,QP+N0B,QAAA,EQ3N1B,UAAA,KNkDA,YAAA,IMhDA,IAAA,QAAA,MAOF,UAAA,MNyCE,MAAA,QMvCF,SRjBgB,UAAA,QEwDd,MAAA,QMlCA,WAAA,OACA,gBACD,WAAA,MAKD,WAAA,OACA,WAAA,iBAAA,cAAA,cAAA,cAAA,cChCE,MAAA,KCTF,cAAA,KACA,aAAA,KACA,aAAA,KACA,YAAA,KACA,yBCmDU,WAAA,cF9CR,UAAA,OAMI,yBEwCI,WAAA,cAAA,cF9CR,UAAA,OAMI,0BEwCa,WAAA,cAAA,cAAA,cF9CjB,UAAA,OAMI,0BEwCa,WAAA,cAAA,cAAA,cAAA,cF9CjB,UAAA,QAMI,KTkMF,QAAA,YAAA,QAAA,YAAA,QAAA,KUzMJ,cAAA,KAAA,UAAA,KACA,aAAA,MACA,YAAA,MACA,YDkCC,aAAA,EAKC,YAAA,EACA,iBAAA,0BAGG,cAAA,EACD,aAAA,EACA,KAAA,OAAA,QAAA,QAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,UAAA,QAAA,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,aAAA,QAAA,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,aAAA,QAAA,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,aAAA,QAAA,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,aGlCF,SAAA,SAnBQ,MAAA,KACR,cAAA,KACA,aAAA,KACA,KAAc,wBAAA,EAAA,WAAA,EAuBV,iBAAA,EAAA,kBAAA,EAAA,UAAA,EACA,UAAA,KACA,cACD,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KF4BC,UAAA,KACJ,cEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,cEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,cEzBL,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,cEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,cEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,UEnBT,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFCA,MAAA,KACJ,UAAA,KACA,OAAW,iBAAA,EAAA,SAAA,EAAA,EAAA,SAAA,KAAA,EAAA,EAAA,SAVF,UAAA,SAIA,OAAE,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UAJD,UAAA,UAIC,OAAA,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IAJX,UAAA,IAIA,OAAW,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UAJD,UAAA,UAIC,OAAA,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UAJD,UAAA,UAIC,OAAA,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IAJX,UAAA,IAIA,OAAW,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UAJD,UAAA,UAIC,OAAA,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UAJD,UAAA,UAIC,OAAA,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IAJX,UAAA,IAIA,QAAW,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UAJD,UAAA,UAIC,QAAA,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UAJD,UAAA,UAIC,QAAA,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KAJP,UAAA,KAIJ,aEWS,0BAAA,EAAA,eAAA,GAAA,MAAA,GAIO,YAAyB,0BAAA,GAAA,eAAA,GAAA,MAAA,GAE1B,SZmKe,0BAAA,EAAA,eAAA,EAAA,MAAA,EYnKoB,SAEhC,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,EAAA,eAAA,EAAA,MAAA,EACZ,SADY,0BAAA,GAAA,eAAA,EAAA,MAAA,EACZ,UAAA,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAS,UAAT,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAS,UAAT,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAS,UAA4B,YAAA,SFThC,UEgBD,YAAA,UFhBoB,UEgBpB,YAAA,IFhBV,UEgBU,YAAA,UFhBoB,UEgBpB,YAAA,UFhBoB,UEgBpB,YAAA,IFhBV,UEgBU,YAAA,UFhBoB,UEgBpB,YAAA,UFhBoB,UEgBpB,YAAA,IFhBV,WEgBU,YAAA,UFhBoB,WEgBpB,YAAA,UFhBoB,yBCKpB,QAAgB,wBAAA,EAAA,WAAA,EC1BpB,iBAAA,EAAA,kBAAA,EAAA,UAAA,EACA,UAAA,KACA,iBACD,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KF4BC,UAAA,KACJ,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,iBEzBL,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,aEnBT,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFCA,MAAA,KACJ,UAAA,KACA,UEDK,iBAAA,EAAA,SAAA,EAAA,EAAA,SAAA,KAAA,EAAA,EAAA,SFTI,UAAA,SAIA,UESD,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,WESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFbJ,UAAA,KAIJ,gBEWS,0BAAA,EAAA,eAAA,GAAA,MAAA,GAIU,eAAsB,0BAAA,GAAA,eAAA,GAAA,MAAA,GAEvB,YAAgC,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAG5C,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,GAAA,eAAA,EAAA,MAAA,EAAA,aAAA,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAyB,YAAA,EFT3C,aEgBU,YAAA,SFhBC,aEgBD,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,cEgBU,YAAA,UFhBoB,cEgBpB,YAAA,WFhBoB,yBCKpB,QAAgB,wBAAA,EAAA,WAAA,EC1BpB,iBAAA,EAAA,kBAAA,EAAA,UAAA,EACA,UAAA,KACA,iBACD,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KF4BC,UAAA,KACJ,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,iBEzBL,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,aEnBT,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFCA,MAAA,KACJ,UAAA,KACA,UEDK,iBAAA,EAAA,SAAA,EAAA,EAAA,SAAA,KAAA,EAAA,EAAA,SFTI,UAAA,SAIA,UESD,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,WESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFbJ,UAAA,KAIJ,gBEWS,0BAAA,EAAA,eAAA,GAAA,MAAA,GAIU,eAAsB,0BAAA,GAAA,eAAA,GAAA,MAAA,GAEvB,YAAgC,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAG5C,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,GAAA,eAAA,EAAA,MAAA,EAAA,aAAA,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAyB,YAAA,EFT3C,aEgBU,YAAA,SFhBC,aEgBD,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,cEgBU,YAAA,UFhBoB,cEgBpB,YAAA,WFhBoB,0BCKX,QAAQ,wBAAA,EAAA,WAAA,EC1BrB,iBAAA,EAAA,kBAAA,EAAA,UAAA,EACA,UAAA,KACA,iBACD,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KF4BC,UAAA,KACJ,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,iBEzBL,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,aEnBT,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFCA,MAAA,KACJ,UAAA,KACA,UEDK,iBAAA,EAAA,SAAA,EAAA,EAAA,SAAA,KAAA,EAAA,EAAA,SFTI,UAAA,SAIA,UESD,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,WESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFbJ,UAAA,KAIJ,gBEWS,0BAAA,EAAA,eAAA,GAAA,MAAA,GAIU,eAAsB,0BAAA,GAAA,eAAA,GAAA,MAAA,GAEvB,YAAgC,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAG5C,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,GAAA,eAAA,EAAA,MAAA,EAAA,aAAA,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAyB,YAAA,EFT3C,aEgBU,YAAA,SFhBC,aEgBD,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,cEgBU,YAAA,UFhBoB,cEgBpB,YAAA,WFhBoB,0BCKX,QAAQ,wBAAA,EAAA,WAAA,EC1BrB,iBAAA,EAAA,kBAAA,EAAA,UAAA,EACA,UAAA,KACA,iBACD,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KF4BC,UAAA,KACJ,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,iBEzBL,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFwBN,UAAA,IACA,iBEzBM,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFwBI,UAAA,UACC,aEnBT,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFCA,MAAA,KACJ,UAAA,KACA,UEDK,iBAAA,EAAA,SAAA,EAAA,EAAA,SAAA,KAAA,EAAA,EAAA,SFTI,UAAA,SAIA,UESD,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,UESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,UESH,iBAAA,EAAA,SAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,IFbR,UAAA,IAIA,WESQ,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,UAAA,KAAA,EAAA,EAAA,UFbE,UAAA,UAIC,WESH,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KFbJ,UAAA,KAIJ,gBEWS,0BAAA,EAAA,eAAA,GAAA,MAAA,GAIU,eAAsB,0BAAA,GAAA,eAAA,GAAA,MAAA,GAEvB,YAAgC,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAG5C,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,EAAA,eAAA,EAAA,MAAA,EAAA,YAAA,0BAAA,GAAA,eAAA,EAAA,MAAA,EAAA,aAAA,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAZ,0BAAA,GAAA,eAAA,GAAA,MAAA,GAAY,aAAyB,YAAA,EFT3C,aEgBU,YAAA,SFhBC,aEgBD,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,aEgBU,YAAA,UFhBoB,aEgBpB,YAAA,UFhBoB,aEgBpB,YAAA,IFhBV,cEgBU,YAAA,UFhBoB,cEgBpB,YAAA,WFhBoB,OAAA,MAAA,KGlD9B,cAAA,KACA,MAAA,QACA,UAAA,UAHF,QAAA,OAQI,eAAA,IACA,WAAA,IAAA,MAAA,QACgC,gBAV9B,eAAA,OAcF,cAAA,IAAA,MAAA,QACyC,mBAG3C,WAAA,IAAA,MAAA,QACwC,aAAA,aAS1C,QAAA,MAGI,gBACD,OAAA,IAAA,MAAA,QAS2B,mBAAA,mBAD9B,OAAA,IAAA,MAAA,QAKgC,yBAAA,yBALjB,oBAAA,IAWT,8BAAA,qBAAA,qBAAA,2BASJ,OAAA,EAAa,yCAUL,iBAAA,gBbzDC,4BaoEJ,MAAA,QAED,iBAAA,iBbtEK,eAAA,kBAAA,kBcZT,iBAAA,QAII,2BAAA,kBAAA,kBAAA,wBAOA,aAAA,QACE,kCAUJ,iBAAA,QAEI,qCAAA,qCCjBL,iBAAA,QDqBO,iBAAA,oBAAA,oBA5BR,iBAAA,QAII,6BAAA,oBAAA,oBAAA,0BAOA,aAAA,QACE,oCAUJ,iBAAA,QAEI,uCAAA,uCCjBL,iBAAA,QDqBO,eAAA,kBAAA,kBA5BR,iBAAA,QAII,2BAAA,kBAAA,kBAAA,wBAOA,aAAA,QACE,kCAUJ,iBAAA,QAEI,qCAAA,qCCjBL,iBAAA,QDqBO,YAAA,eAAA,eA5BR,iBAAA,QAII,wBAAA,eAAA,eAAA,qBAOA,aAAA,QACE,+BAUJ,iBAAA,QAEI,kCAAA,kCCjBL,iBAAA,QDqBO,eAAA,kBAAA,kBA5BR,iBAAA,QAII,2BAAA,kBAAA,kBAAA,wBAOA,aAAA,QACE,kCAUJ,iBAAA,QAEI,qCAAA,qCCjBL,iBAAA,QDqBO,cAAA,iBAAA,iBA5BR,iBAAA,QAII,0BAAA,iBAAA,iBAAA,uBAOA,aAAA,QACE,iCAUJ,iBAAA,QAEI,oCAAA,oCCjBL,iBAAA,QDqBO,aAAA,gBAAA,gBA5BR,iBAAA,QAII,yBAAA,gBAAA,gBAAA,sBAOA,aAAA,QACE,gCAUJ,iBAAA,QAEI,mCAAA,mCCjBL,iBAAA,QDqBO,YAAA,eAAA,eA5BR,iBAAA,QAII,wBAAA,eAAA,eAAA,qBAOA,aAAA,QACE,+BAUJ,iBAAA,QAEI,kCAAA,kCCjBL,iBAAA,QDqBO,aAAA,gBAAA,gBA5BR,iBAAA,QAII,yBAAA,gBAAA,gBAAA,sBAOA,aAAA,QACE,gCAUJ,iBAAA,QAEI,mCAAA,mCCjBL,iBAAA,QDqBO,gBAAA,mBAAA,mBA5BR,iBAAA,QAII,4BAAA,mBAAA,mBAAA,yBAOA,aAAA,QACE,mCAUJ,iBAAA,QAEI,sCAAA,sCCjBL,iBAAA,QDqBO,oBAAA,uBAAA,uBA5BR,iBAAA,QAII,gCAAA,uBAAA,uBAAA,6BAOA,aAAA,QACE,uCAUJ,iBAAA,QAEI,0CAAA,0CCjBL,iBAAA,QDqBO,cAAA,iBAAA,iBA5BR,iBAAA,QAII,0BAAA,iBAAA,iBAAA,uBAOA,aAAA,QACE,iCAUJ,iBAAA,QAEI,oCAAA,oCCjBL,iBAAA,QDqBO,cAAA,iBAAA,iBA5BR,iBAAA,iBdYS,iCcUP,iBAAA,iBAFmB,oCAAA,oCCbpB,iBAAA,iBDaoB,sBDuFrB,MAAA,KAEI,iBAAA,QACA,aAAA,QACA,uBAIJ,MAAA,QAEI,iBAAA,QACA,aAAA,QACA,YACD,MAAA,KAKH,iBAAA,QACA,eAAA,eAAA,qBAFS,aAAA,QAOP,2BAPO,OAAA,EAUO,oDAKN,iBAAA,sBbzID,uCa+IF,MAAA,KAED,iBAAA,uBbjJG,4BWkEY,qBEiGnB,QAAA,MAEI,MAAA,KACA,WAAA,KACA,2BAAA,MACA,qCAGE,OAAA,GACA,4BF1Ga,qBEiGnB,QAAA,MAEI,MAAA,KACA,WAAA,KACA,2BAAA,MACA,qCAGE,OAAA,GACA,6BF1Ga,qBEiGnB,QAAA,MAEI,MAAA,KACA,WAAA,KACA,2BAAA,MACA,qCAGE,OAAA,GACA,6BF1Ga,qBEiGnB,QAAA,MAEI,MAAA,KACA,WAAA,KACA,2BAAA,MACA,qCAGE,OAAA,GACA,kBAdV,QAAA,MAOQ,MAAA,KACA,WAAA,KACA,2BAAA,MACA,kCAGE,OAAA,EAAe,cAEhB,QAAA,MG7KP,MAAA,KACA,OAAA,2BCkHiC,QAAA,QAAA,OjBwQL,UAAA,KEnQxB,YAAA,IclHJ,YAAA,IACA,MAAA,QACA,iBAAA,KACA,gBAAA,YACiB,OAAA,IAAA,MAAA,QACW,cAAA,OTA1B,mBAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAAA,WAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YPqemF,uCkBnenD,cFdpC,mBAAA,KAAA,WAAA,MEeQ,0BFfK,iBAAA,YAsBS,OAAA,EAAW,6BAtBpB,MAAA,YA4BF,YAAA,EAAA,EAAA,EAAA,QACU,oBGtBnB,MAAA,QACE,iBAAA,KACA,aAAA,QACA,QAAA,EACA,mBAAA,EAAA,EAAA,EAAA,MAAA,sBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,sBvBjBM,yCoBMG,MAAA,QAqCT,QAAA,EpB3CM,gCoBMG,MAAA,QAqCT,QAAA,EpB3CM,oCoBMG,MAAA,QAqCT,QAAA,EpB3CM,qCoBMG,MAAA,QAqCT,QAAA,EpB3CM,2BoBMG,MAAA,QAqCT,QAAA,EAEA,uBAAA,wBASD,iBAAA,QACC,QAAA,EAEA,8BAAA,wCAAA,+BAAA,8BAQD,mBAAA,KAAA,gBAAA,KAAA,WAAA,KACC,qCAKK,MAAA,QAML,iBAAA,KACA,mBAAA,oBAMJ,QAAA,MACE,MAAA,KACA,gBACD,YAAA,oBCmCkC,eAAA,oBAAA,cAAA,EDvBjC,UAAA,Qd3BE,YAAA,Ic6BF,mBACD,YAAA,kBCoBkC,eAAA,kBAAA,UAAA,QfK7B,YAAA,IcnBJ,mBACD,YAAA,mBCakC,eAAA,mBAAA,UAAA,QfK7B,YAAA,IcZJ,wBACD,QAAA,MASC,MAAA,KACA,QAAA,QAAA,EhBoQ4B,cAAA,EgBlQ5B,UAAA,KdDI,YAAA,IcGJ,MAAA,QACA,iBAAA,YACkB,OAAA,MAAA,YACV,aAAA,IAAA,EACR,wCAAA,wCAVqB,cAAA,EAcnB,aAAA,EACA,iBACD,OAAA,0BClBgC,QAAA,OAAA,MjBkRL,UAAA,QE7QxB,YAAA,Ic4BJ,cAAA,MTzIE,iBS2IH,OAAA,yBCnCkC,QAAA,MAAA,KjBuRL,UAAA,QElRxB,YAAA,IcoCJ,cAAA,MTjJE,8BAAA,0BSwJD,OAAA,KACC,sBAII,OAAA,KACN,YACD,cAAA,KAQC,WACD,QAAA,MAGC,WAAA,OACA,UACD,QAAA,YAAA,QAAA,YAAA,QAAA,KAQC,cAAA,KAAA,UAAA,KACA,aAAA,KACA,YAAA,KACA,eAAA,wBAGG,cAAA,IACD,aAAA,IACA,YACD,SAAA,SASO,QAAA,MACR,aAAA,QACA,kBACD,SAAA,SAGS,WAAA,MACR,YAAA,SACW,6CAAA,8CAIE,MAAA,QACX,kBACD,cAAA,EAID,mBACD,QAAA,mBAAA,QAAA,mBAAA,QAAA,YAGU,kBAAA,OAAA,eAAA,OAAA,YAAA,OACT,aAAA,EACA,aAAA,OACA,qCAGA,SAAA,OACE,WAAA,EACA,aAAA,SACY,YAAA,EACZ,gBG7MF,QAAA,KACE,MAAA,KACA,WAAA,OACA,UAAA,IjByBA,MAAA,QiBvBA,eACD,SAAA,SAGS,IAAA,KAAU,KAAA,EACb,QAAA,EAEL,QAAA,KACA,UAAA,KACA,QAAA,OAAA,MnBgyBgC,WAAA,MmB9xBhC,UAAA,QjBmEE,YAAA,IiBjEF,MAAA,KACA,iBAAA,oBvB9DI,cAAA,OWiBJ,0BAAA,yBAAA,sCAAA,qCYoDE,QAAA,MACA,uBAAA,mCAtCD,aAAA,QA4CC,cAAA,qBF6C6B,iBAAA,gQA7DlB,kBAAA,UEqBU,oBAAA,MAAA,wBAAA,OFwCQ,gBAAA,sBAAA,sBAAA,6BAAA,yCEzF9B,aAAA,QAuDG,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBvBvFA,2CAAA,+BuBgCH,cAAA,qBFyF8B,oBAAA,IAAA,wBAAA,MAAA,wBAAA,wBAAA,oCEzF9B,aAAA,QAyEC,cAAA,wBFgB6B,WAAA,+KAAA,UAAA,MAAA,OAAA,MAAA,CAAA,IAAA,IAAA,CAAA,gQAAA,KAAA,UAAA,OAAA,MAAA,OAAA,CAAA,sBAAA,sBEZ0D,8BAAA,0CA7ExF,aAAA,QAiFG,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBvBjHA,6CAAA,yDuByHA,MAAA,QACA,2CAAA,0CAAA,uDAAA,sDAIA,QAAA,MACA,qDAAA,iEAOA,MAAA,QACA,6DAAA,yEADA,aAAA,QAIE,qEAAA,iFAKA,aAAA,QACA,iBAAA,QC3IN,mEAAA,+EDiJM,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBvBtJF,iFAAA,6FuB0JkB,aAAA,QAChB,+CAAA,2DASF,aAAA,QACA,qDAAA,iEAIE,aAAA,QACA,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBvB1KF,kBuB2CN,QAAA,KACE,MAAA,KACA,WAAA,OACA,UAAA,IjByBA,MAAA,QiBvBA,iBACD,SAAA,SAGS,IAAA,KAAU,KAAA,EACb,QAAA,EAEL,QAAA,KACA,UAAA,KACA,QAAA,OAAA,MnBgyBgC,WAAA,MmB9xBhC,UAAA,QjBmEE,YAAA,IiBjEF,MAAA,KACA,iBAAA,mBnBxBM,cAAA,OOrBN,8BAAA,6BAAA,0CAAA,yCYoDE,QAAA,MACA,yBAAA,qCAtCD,aAAA,QA4CC,cAAA,qBF6C6B,iBAAA,2TA7DlB,kBAAA,UEqBU,oBAAA,MAAA,wBAAA,OFwCQ,gBAAA,sBAAA,sBAAA,+BAAA,2CEzF9B,aAAA,QAuDG,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBnBjDE,6CAAA,iCmBNL,cAAA,qBFyF8B,oBAAA,IAAA,wBAAA,MAAA,wBAAA,0BAAA,sCEzF9B,aAAA,QAyEC,cAAA,wBFgB6B,WAAA,+KAAA,UAAA,MAAA,OAAA,MAAA,CAAA,IAAA,IAAA,CAAA,2TAAA,KAAA,UAAA,OAAA,MAAA,OAAA,CAAA,sBAAA,sBEZ0D,gCAAA,4CA7ExF,aAAA,QAiFG,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBnB3EE,+CAAA,2DmBmFF,MAAA,QACA,+CAAA,8CAAA,2DAAA,0DAIA,QAAA,MACA,uDAAA,mEAOA,MAAA,QACA,+DAAA,2EADA,aAAA,QAIE,uEAAA,mFAKA,aAAA,QACA,iBAAA,QC3IN,qEAAA,iFDiJM,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBnBhHA,mFAAA,+FmBoHgB,aAAA,QAChB,iDAAA,6DASF,aAAA,QACA,uDAAA,mEAIE,aAAA,QACA,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBnBpIA,amBsID,QAAA,YAAA,QAAA,YAAA,QAAA,KHsGP,mBAAA,WAAA,sBAAA,OAAA,cAAA,IAAA,KAAA,UAAA,IAAA,KACS,kBAAA,OAAA,eAAA,OAAA,YAAA,OACT,yBAKA,MAAA,KACE,yBL/NQ,mBKsNZ,QAAA,YAAA,QAAA,YAAA,QAAA,KAeM,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,cAAA,EACA,yBAIF,QAAA,YAAA,QAAA,YAAA,QAAA,KACE,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KACI,mBAAA,WAAA,sBAAA,OAAA,cAAA,IAAA,KAAA,UAAA,IAAA,KACK,kBAAA,OAAA,eAAA,OAAA,YAAA,OACT,cAAA,EACA,2BAIF,QAAA,aACW,MAAA,KACT,eAAA,OACA,qCAIF,QAAA,aACW,4BAAA,0BAIX,MAAA,KACE,yBApCJ,QAAA,YAAA,QAAA,YAAA,QAAA,KA0CI,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KACA,aAAA,EACA,+BAEF,SAAA,SACU,kBAAA,EAAA,YAAA,EACR,WAAA,EACA,aAAA,OACA,YAAA,EACA,6BAGF,kBAAA,OAAA,eAAA,OAAA,YAAA,OACE,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,mCAEF,cAAA,GACE,KAAA,QAAA,aKhVK,YAAA,IAET,MAAA,QACA,WAAA,OACA,eAAA,OAGA,oBAAA,KAAA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,iBAAA,YACkB,OAAA,IAAA,MAAA,YACc,QAAA,QAAA,OrBiXJ,UAAA,KEnQxB,YAAA,IoBrBJ,cAAA,OfxFE,mBAAA,MAAA,KAAA,WAAA,CAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,MAAA,KAAA,WAAA,CAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,MAAA,KAAA,WAAA,CAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAAA,WAAA,MAAA,KAAA,WAAA,CAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YP8aoI,uCkB5apG,KAAE,mBAAA,KAAA,WAAA,MAC9B,WHTN,MAAA,QMUE,gBAAA,KACA,WAAA,WAjBJ,QAAA,EAsBI,mBAAA,EAAA,EAAA,EAAA,MAAA,sBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,sBzB5BM,cAAA,cyBMN,QAAA,IA6BA,mCAImB,OAAA,QACnB,eAAA,wBAeI,eAAA,KACN,aACD,MAAA,QCnDC,iBAAA,QFAE,aAAA,QEEF,mBPIA,MAAA,KOAE,iBAAA,QFNA,aAAA,QEQA,mBAAA,mBDmDF,MAAA,KC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAK0C,sBAAA,sBAM3C,MAAA,QACC,iBAAA,QACA,aAAA,QACA,kDAAA,kDAAA,mCASO,MAAA,KACP,iBAAA,QACA,aAAA,QAIA,wDAAA,wDAAA,yCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAaqC,eAE3C,MAAA,KArDH,iBAAA,QFAE,aAAA,QEEF,qBPIA,MAAA,KOAE,iBAAA,QFNA,aAAA,QEQA,qBAAA,qBDmDF,MAAA,KC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAK0C,wBAAA,wBAM3C,MAAA,KACC,iBAAA,QACA,aAAA,QACA,oDAAA,oDAAA,qCASO,MAAA,KACP,iBAAA,QACA,aAAA,QAIA,0DAAA,0DAAA,2CANO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAaqC,aAE3C,MAAA,KArDH,iBAAA,QFAE,aAAA,QEEF,mBPIA,MAAA,KOAE,iBAAA,QFNA,aAAA,QEQA,mBAAA,mBDmDF,MAAA,KC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAK0C,sBAAA,sBAM3C,MAAA,KACC,iBAAA,QACA,aAAA,QACA,kDAAA,kDAAA,mCASO,MAAA,KACP,iBAAA,QACA,aAAA,QAIA,wDAAA,wDAAA,yCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAaqC,UAE3C,MAAA,QArDH,iBAAA,QFAE,aAAA,QEEF,gBPIA,MAAA,QOAE,iBAAA,QFNA,aAAA,QEQA,gBAAA,gBDmDF,MAAA,QC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAK0C,mBAAA,mBAM3C,MAAA,QACC,iBAAA,QACA,aAAA,QACA,+CAAA,+CAAA,gCASO,MAAA,QACP,iBAAA,QACA,aAAA,QAIA,qDAAA,qDAAA,sCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAaqC,aAE3C,MAAA,QArDH,iBAAA,QFAE,aAAA,QEEF,mBPIA,MAAA,QOAE,iBAAA,QFNA,aAAA,QEQA,mBAAA,mBDmDF,MAAA,QC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAK0C,sBAAA,sBAM3C,MAAA,QACC,iBAAA,QACA,aAAA,QACA,kDAAA,kDAAA,mCASO,MAAA,QACP,iBAAA,QACA,aAAA,QAIA,wDAAA,wDAAA,yCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAaqC,YAE3C,MAAA,KArDH,iBAAA,QFAE,aAAA,QEEF,kBPIA,MAAA,KOAE,iBAAA,QFNA,aAAA,QEQA,kBAAA,kBDmDF,MAAA,KC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,mBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,mBAK0C,qBAAA,qBAM3C,MAAA,KACC,iBAAA,QACA,aAAA,QACA,iDAAA,iDAAA,kCASO,MAAA,KACP,iBAAA,QACA,aAAA,QAIA,uDAAA,uDAAA,wCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,mBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,mBAaqC,WAE3C,MAAA,QArDH,iBAAA,QFAE,aAAA,QEEF,iBPIA,MAAA,QOAE,iBAAA,QFNA,aAAA,QEQA,iBAAA,iBDmDF,MAAA,QC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAK0C,oBAAA,oBAM3C,MAAA,QACC,iBAAA,QACA,aAAA,QACA,gDAAA,gDAAA,iCASO,MAAA,QACP,iBAAA,QACA,aAAA,QAIA,sDAAA,sDAAA,uCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAaqC,UAE3C,MAAA,KArDH,iBAAA,QFAE,aAAA,QEEF,gBPIA,MAAA,KOAE,iBAAA,QFNA,aAAA,QEQA,gBAAA,gBDmDF,MAAA,KC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,kBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,kBAK0C,mBAAA,mBAM3C,MAAA,KACC,iBAAA,QACA,aAAA,QACA,+CAAA,+CAAA,gCASO,MAAA,KACP,iBAAA,QACA,aAAA,QAIA,qDAAA,qDAAA,sCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,kBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,kBAaqC,WAE3C,MAAA,KArDH,iBAAA,QFAE,aAAA,QEEF,iBPIA,MAAA,KOAE,iBAAA,QFNA,aAAA,QEQA,iBAAA,iBDmDF,MAAA,KC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAK0C,oBAAA,oBAM3C,MAAA,KACC,iBAAA,QACA,aAAA,QACA,gDAAA,gDAAA,iCASO,MAAA,KACP,iBAAA,QACA,aAAA,QAIA,sDAAA,sDAAA,uCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAaqC,cAE3C,MAAA,QArDH,iBAAA,QFAE,aAAA,QEEF,oBPIA,MAAA,QOAE,iBAAA,QFNA,aAAA,QEQA,oBAAA,oBDmDF,MAAA,QC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAK0C,uBAAA,uBAM3C,MAAA,QACC,iBAAA,QACA,aAAA,QACA,mDAAA,mDAAA,oCASO,MAAA,QACP,iBAAA,QACA,aAAA,QAIA,yDAAA,yDAAA,0CANO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAaqC,kBAE3C,MAAA,QArDH,iBAAA,QFAE,aAAA,QEEF,wBPIA,MAAA,QOAE,iBAAA,QFNA,aAAA,QEQA,wBAAA,wBDmDF,MAAA,QC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAK0C,2BAAA,2BAM3C,MAAA,QACC,iBAAA,QACA,aAAA,QACA,uDAAA,uDAAA,wCASO,MAAA,QACP,iBAAA,QACA,aAAA,QAIA,6DAAA,6DAAA,8CANO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBAaqC,YAE3C,MAAA,QArDH,iBAAA,QFAE,aAAA,QEEF,kBPIA,MAAA,QOAE,iBAAA,QFNA,aAAA,QEQA,kBAAA,kBDmDF,MAAA,QC9CE,iBAAA,QFbA,aAAA,QEeA,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAK0C,qBAAA,qBAM3C,MAAA,QACC,iBAAA,QACA,aAAA,QACA,iDAAA,iDAAA,kCASO,MAAA,KACP,iBAAA,QACA,aAAA,QAIA,uDAAA,uDAAA,wCANO,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBAaqC,qBAE3C,MAAA,QAKH,aAAA,QACA,2BPrDA,MAAA,QOwDE,iBAAA,QACA,aAAA,QACA,2BAAA,2BDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBzBvEQ,8BAAA,8B0B+EP,MAAA,QACC,iBAAA,YACkB,0DAAA,0DAAA,2CAKX,MAAA,QACP,iBAAA,QACA,aAAA,QACA,gEAAA,gEAAA,iDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qB1BtFD,uB0BkGL,MAAA,QAlCH,aAAA,QACA,6BPrDA,MAAA,KOwDE,iBAAA,QACA,aAAA,QACA,6BAAA,6BDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBrB1DS,gCAAA,gCsBkER,MAAA,QACC,iBAAA,YACkB,4DAAA,4DAAA,6CAKX,MAAA,KACP,iBAAA,QACA,aAAA,QACA,kEAAA,kEAAA,mDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBtBzEA,qBsBqFN,MAAA,QAlCH,aAAA,QACA,2BPrDA,MAAA,KOwDE,iBAAA,QACA,aAAA,QACA,2BAAA,2BDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBzBtEM,8BAAA,8B0B8EL,MAAA,QACC,iBAAA,YACkB,0DAAA,0DAAA,2CAKX,MAAA,KACP,iBAAA,QACA,aAAA,QACA,gEAAA,gEAAA,iDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oB1BrFH,kB0BiGH,MAAA,QAlCH,aAAA,QACA,wBPrDA,MAAA,QOwDE,iBAAA,QACA,aAAA,QACA,wBAAA,wBDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBzBpEK,2BAAA,2B0B4EJ,MAAA,QACC,iBAAA,YACkB,uDAAA,uDAAA,wCAKX,MAAA,QACP,iBAAA,QACA,aAAA,QACA,6DAAA,6DAAA,8CAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qB1BnFJ,qB0B+FF,MAAA,QAlCH,aAAA,QACA,2BPrDA,MAAA,QOwDE,iBAAA,QACA,aAAA,QACA,2BAAA,2BDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBzBnEQ,8BAAA,8B0B2EP,MAAA,QACC,iBAAA,YACkB,0DAAA,0DAAA,2CAKX,MAAA,QACP,iBAAA,QACA,aAAA,QACA,gEAAA,gEAAA,iDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qB1BlFD,oB0B8FL,MAAA,QAlCH,aAAA,QACA,0BPrDA,MAAA,KOwDE,iBAAA,QACA,aAAA,QACA,0BAAA,0BDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,mBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,mBrBhCQ,6BAAA,6BsBwCP,MAAA,QACC,iBAAA,YACkB,yDAAA,yDAAA,0CAKX,MAAA,KACP,iBAAA,QACA,aAAA,QACA,+DAAA,+DAAA,gDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,mBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,mBtB/CD,mBsB2DL,MAAA,QAlCH,aAAA,QACA,yBPrDA,MAAA,QOwDE,iBAAA,QACA,aAAA,QACA,yBAAA,yBDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBrB/DS,4BAAA,4BsBuER,MAAA,QACC,iBAAA,YACkB,wDAAA,wDAAA,yCAKX,MAAA,QACP,iBAAA,QACA,aAAA,QACA,8DAAA,8DAAA,+CAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBtB9EA,kBsB0FN,MAAA,QAlCH,aAAA,QACA,wBPrDA,MAAA,KOwDE,iBAAA,QACA,aAAA,QACA,wBAAA,wBDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,kBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,kBrBxDS,2BAAA,2BsBgER,MAAA,QACC,iBAAA,YACkB,uDAAA,uDAAA,wCAKX,MAAA,KACP,iBAAA,QACA,aAAA,QACA,6DAAA,6DAAA,8CAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,kBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,kBtBvEA,mBsBmFN,MAAA,QAlCH,aAAA,QACA,yBPrDA,MAAA,KOwDE,iBAAA,QACA,aAAA,QACA,yBAAA,yBDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBzBtEM,4BAAA,4B0B8EL,MAAA,QACC,iBAAA,YACkB,wDAAA,wDAAA,yCAKX,MAAA,KACP,iBAAA,QACA,aAAA,QACA,8DAAA,8DAAA,+CAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oB1BrFH,sB0BiGH,MAAA,QAlCH,aAAA,QACA,4BPrDA,MAAA,QOwDE,iBAAA,QACA,aAAA,QACA,4BAAA,4BDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBzBrEY,+BAAA,+B0B6EX,MAAA,QACC,iBAAA,YACkB,2DAAA,2DAAA,4CAKX,MAAA,QACP,iBAAA,QACA,aAAA,QACA,iEAAA,iEAAA,kDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qB1BpFG,0B0BgGT,MAAA,QAlCH,aAAA,QACA,gCPrDA,MAAA,QOwDE,iBAAA,QACA,aAAA,QACA,gCAAA,gCDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qBzBlEa,mCAAA,mC0B0EZ,MAAA,QACC,iBAAA,YACkB,+DAAA,+DAAA,gDAKX,MAAA,QACP,iBAAA,QACA,aAAA,QACA,qEAAA,qEAAA,sDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,qB1BjFI,oB0B6FV,MAAA,QAlCH,aAAA,QACA,0BPrDA,MAAA,QOwDE,iBAAA,QACA,aAAA,QACA,0BAAA,0BDCF,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oBzBjEO,6BAAA,6B0ByEN,MAAA,QACC,iBAAA,YACkB,yDAAA,yDAAA,0CAKX,MAAA,QACP,iBAAA,QACA,aAAA,QACA,+DAAA,+DAAA,gDAHO,mBAAA,EAAA,EAAA,EAAA,MAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,oB1BhFF,U0B4FJ,YAAA,IDfH,MAAA,QACA,gBAAA,KACA,gBNzEA,MAAA,QM4EE,gBAAA,UrB0FsC,gBAAA,gBqBhG1C,gBAAA,UrBgG0C,mBAAA,mBqBhGjC,MAAA,QAiBL,eAAA,KACA,mBAAA,QEvCJ,QAAA,MAAA,KvBoV8B,UAAA,QElRxB,YAAA,IoBrBJ,cAAA,MfxFE,mBAAA,QgB0CJ,QAAA,OAAA,MvBgV8B,UAAA,QE7QxB,YAAA,IoBrBJ,cAAA,MfxFE,WcmGH,QAAA,MAQC,MAAA,KACA,sBAGE,WAAA,MACA,6BAAA,4BAAA,6BAQD,MAAA,KACC,WAAA,oBEvIJ,SAAA,SACU,QAAA,mBAAA,QAAA,mBAAA,QAAA,YACC,eAAA,OACT,yBAAA,gBAHF,SAAA,SAMY,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KACJ,+BAAA,sBAFJ,QAAA,EAOE,gCAAA,gCAAA,+BAAA,uBAAA,uBAAA,sBAPF,QAAA,EAYE,aACD,QAAA,YAAA,QAAA,YAAA,QAAA,KAMH,cAAA,KAAA,UAAA,KACA,iBAAA,MAAA,cAAA,MAAA,gBAAA,WACiB,0BAEjB,MAAA,KACE,wCAAA,kCAMS,YAAA,KAET,4CAAA,uDpBqDI,wBAAA,EI5DJ,2BAAA,EACA,6CAAA,kCgBIS,uBAAA,EhBST,0BAAA,EACA,uBgBID,cAAA,SAgBY,aAAA,SACD,8BAAA,yCAAA,sCAFd,YAAA,EAOI,yCAPJ,aAAA,EAWI,0CAAA,+BAIM,cAAA,QACR,aAAA,QACA,0CAAA,+BAGQ,cAAA,OACR,aAAA,OACA,oBACD,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OAoBC,kBAAA,MAAA,eAAA,MAAA,YAAA,WACa,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACb,yBAAA,+BAGE,MAAA,KACA,iDAAA,2CA9ES,WAAA,KAmFT,qDAAA,gEpB5BI,2BAAA,EIrDJ,0BAAA,EACA,sDAAA,2CgBHS,uBAAA,EhBZT,wBAAA,EACA,uBAAA,kCgB4HA,cAAA,EACA,4CAAA,yCAAA,uDAAA,oDAGW,SAAA,SACD,KAAA,cACF,eAAA,KACN,aACD,SAAA,SCzJK,QAAA,YAAA,QAAA,YAAA,QAAA,KACR,cAAA,KAAA,UAAA,KACA,kBAAA,QAAA,eAAA,QAAA,YAAA,QACA,MAAA,KACA,0BAAA,4BAAA,2BAAA,qCAKE,SAAA,SACQ,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KACJ,MAAA,GAAU,UAAA,EAEd,cAAA,EACA,uCAAA,yCAAA,wCAAA,yCAAA,2CAAA,0CAAA,wCAAA,0CAAA,yCAAA,kDAAA,oDAAA,mDAIE,YAAA,KACA,sEAAA,kCAAA,iCAOsC,QAAA,EACxC,mDAIa,QAAA,EACb,6CAAA,4CrBuDI,wBAAA,EI5DJ,2BAAA,EACA,8CAAA,6CgBIS,uBAAA,EhBST,0BAAA,EACA,0BiBCA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,8DAAA,qEAGmB,wBAAA,EjBrBnB,2BAAA,EACA,+DiBqBoB,uBAAA,EjBRpB,0BAAA,EACA,oBAAA,qBiBmBJ,QAAA,YAAA,QAAA,YAAA,QAAA,KACE,yBAAA,0BADF,SAAA,SAOY,QAAA,EACR,+BAAA,gCAFF,QAAA,EAKI,8BAAA,2CAAA,2CAAA,wDAAA,+BAAA,4CAAA,4CAAA,yDAOJ,YAAA,KACE,qBACD,aAAA,KAGoB,oBAAsC,YAAA,KACvC,kBAAqC,QAAA,YAAA,QAAA,YAAA,QAAA,KASzD,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,QAAA,QAAA,OxB4R4B,cAAA,EwB1R5B,UAAA,KtBuBI,YAAA,IsBrBJ,YAAA,IACA,MAAA,QACA,WAAA,OACA,YAAA,OACA,iBAAA,QACA,OAAA,IAAA,MAAA,QAC4B,cAAA,OjB9F1B,uCAAA,oCiBmGS,WAAA,EACT,+BAAA,4CAWc,OAAA,yBPPiB,+BAAA,8BAAA,yCAAA,sDAAA,0CAAA,uDOgBjB,QAAA,MAAA,KxBuQY,UAAA,QElRxB,YAAA,IsBcJ,cAAA,MjB3HE,+BAAA,4CiBgIc,OAAA,0BPxBiB,+BAAA,8BAAA,yCAAA,sDAAA,0CAAA,uDOiCjB,QAAA,OAAA,MxBiPY,UAAA,QE7QxB,YAAA,IsB+BJ,cAAA,MjB5IE,+BAAA,+BiBiJc,cAAA,QAChB,wFAAA,+EAAA,uDAAA,oEAAA,uCAAA,oDrBtEM,wBAAA,EI5DJ,2BAAA,EACA,sCAAA,mDAAA,qEAAA,kFAAA,yDAAA,sEgBIS,uBAAA,EhBST,0BAAA,EACA,gBiB8IH,SAAA,SCrLS,QAAA,EACR,QAAA,MACA,WAAA,OACA,aAAA,OACA,2BAAA,MAAA,aAAA,MACA,uBACD,QAAA,mBAAA,QAAA,mBAAA,QAAA,YAGU,aAAA,KACT,sBACD,SAAA,SAGS,KAAA,EAAE,QAAA,GAEV,MAAA,KACA,OAAA,QACA,QAAA,EACA,4DAEY,MAAA,KACV,aAAA,QACA,iBAAA,QL3BA,0DKgCQ,mBAAA,EAAA,EAAA,EAAA,MAAA,sBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,sB7BtCF,wE6B+CgB,aAAA,QACtB,0EAGwB,MAAA,KACxB,iBAAA,QACA,aAAA,QACA,qDAAA,sDAOE,MAAA,QACA,6DAAA,8DADA,iBAAA,QAIE,sBACD,SAAA,SAUG,cAAA,EACR,eAAA,IAEA,8BAJF,SAAA,SASY,IAAA,OACR,KAAA,QACA,QAAA,MACA,MAAA,KACA,OAAA,KACA,eAAA,KACA,QAAA,GACA,iBAAA,KACA,OAAA,QAAA,MAAA,IAC+C,6BAlBnD,SAAA,SAwBY,IAAA,OACR,KAAA,QACA,QAAA,MACA,MAAA,KACA,OAAA,KACA,QAAA,GACA,WAAA,UAAA,GAAA,CAAA,IAAA,IACsB,+CAUxB,cAAA,OlBlGE,4EkBsG8B,iBAAA,iNR3DjB,mFQiEuB,aAAA,QAElC,iBAAA,QL1HF,kFKwHoC,iBAAA,8JRjEvB,sFQ6ED,iBAAA,qB7B1IN,4F6B6IY,iBAAA,qB7B7IZ,4C6BwJR,cAAA,IAEE,yEAG8B,iBAAA,6JRhGjB,mFQuGD,iBAAA,qB7BpKN,e6BsKL,aAAA,QAUH,6CAEA,KAAA,SAEQ,MAAA,QACJ,eAAA,IACA,cAAA,MAEA,4CANJ,IAAA,mBRxDiC,KAAA,qBAAA,MAAA,iBAoBA,OAAA,iBAAA,iBAAA,QQkD7B,cAAA,MAEA,mBAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,kBAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,kBAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,UAAA,KAAA,WAAA,CAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAAA,WAAA,UAAA,KAAA,WAAA,CAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,WAAA,CAAA,kBAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YzBuUoH,uCkBrftF,4CO8JlC,mBAAA,KAAA,WAAA,MP7JM,0EOkL0B,iBAAA,KAE5B,kBAAA,mBAAA,UAAA,mBACW,oFAKD,iBAAA,qB7B/MN,e6BiNL,QAAA,aAYM,MAAA,KACT,OAAA,2BRpGiC,QAAA,QAAA,QAAA,QAAA,OjBwQL,UAAA,KEnQxB,YAAA,IuBoGJ,YAAA,IACA,MAAA,QACA,eAAA,OACA,WAAA,KAAA,+KAAA,UAAA,MAAA,OAAA,MAAA,CAAA,IAAA,KzBmWgH,OAAA,IAAA,MAAA,QyBjW5E,cAAA,OlBtNlC,mBAAA,KAAA,gBAAA,KAAA,WAAA,KkByNF,qBAfF,aAAA,QAkBI,QAAA,EACA,mBAAA,EAAA,EAAA,EAAA,MAAA,sBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,sB7B/OM,gC6B6OD,MAAA,QAgBH,iBAAA,KACA,yBAAA,qCAKQ,OAAA,KACV,cAAA,OACA,iBAAA,KACA,wBA1CU,MAAA,QA8CV,iBAAA,QACA,2BA/CU,QAAA,KAoDV,8BApDU,MAAA,YAyDH,YAAA,EAAA,EAAA,EAAA,QACU,kBAClB,OAAA,0BR7JgC,YAAA,OQkKjC,eAAA,OACA,aAAA,MACA,UAAA,QvB/JI,kBuBiKL,OAAA,yBRtKkC,YAAA,MQ0KjC,eAAA,MACA,aAAA,KACA,UAAA,QvBvKI,auByKL,SAAA,SAQS,QAAA,aACC,MAAA,KACT,OAAA,2BRxLiC,cAAA,EQ0LjC,mBACD,SAAA,SAGS,QAAA,EACR,MAAA,KACA,OAAA,2BRhMiC,OAAA,EAA6B,QAAA,EQmM9D,4CAEU,aAAA,QACR,mBAAA,EAAA,EAAA,EAAA,MAAA,sBAAA,WAAA,EAAA,EAAA,EAAA,MAAA,sB7BhUM,+CAAA,gD6BsUK,iBAAA,QACX,sDAIa,QAAA,SACJ,0DAIS,QAAA,kBACT,mBACV,SAAA,SAIO,IAAA,EAAE,MAAA,EACL,KAAA,EACA,QAAA,EAEL,OAAA,2BRhOiC,QAAA,QAAA,OjBwQL,YAAA,IyBpC5B,YAAA,IACA,MAAA,QACA,iBAAA,KACA,OAAA,IAAA,MAAA,QACkC,cAAA,OlBhVhC,0BkBmUJ,SAAA,SAkBY,IAAA,EAAE,MAAA,EACL,OAAA,EACG,QAAA,EAER,QAAA,MACA,OAAA,qBRlP+B,QAAA,QAAA,OjBwQL,YAAA,IyBnB1B,MAAA,QACA,QAAA,SACO,iBAAA,QL3WP,YAAA,QK6WA,cAAA,EAAA,OAAA,OAAA,EzBpIgC,cyBsIjC,MAAA,KAUD,OAAA,OACA,QAAA,EACA,iBAAA,YACkB,mBAAA,KAAA,gBAAA,KAAA,WAAA,KAClB,oBALF,QAAA,EAQI,0CADK,mBAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sBAAA,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sB7BrYC,sC6BqYD,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sB7BrYC,+B6BqYD,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sB7BrYC,gC6B8XG,OAAA,EAiBQ,oCAjBR,MAAA,KAsBT,OAAA,KACA,WAAA,QACA,iBAAA,QLhZA,OAAA,ExBNa,cAAA,KWkBb,mBAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YPufsH,mBAAA,KAAA,WAAA,KyB7GtH,uCPxYgC,oCO0WvB,mBAAA,KAAA,WAAA,MPzWL,2COyWK,iBAAA,QLxXT,6CKwXS,MAAA,KAsCT,OAAA,MACA,MAAA,YACO,OAAA,QACP,iBAAA,QACA,aAAA,YACc,cAAA,KlBvZd,gCkB4WS,MAAA,KAiDT,OAAA,KACA,iBAAA,QL1aA,OAAA,ExBNa,cAAA,KWkBb,gBAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YPufsH,gBAAA,KAAA,WAAA,KyBnFtH,uCPlagC,gCO0WvB,gBAAA,KAAA,WAAA,MPzWL,uCOyWK,iBAAA,QLxXT,gCKwXS,MAAA,KAgET,OAAA,MACA,MAAA,YACO,OAAA,QACP,iBAAA,QACA,aAAA,YACc,cAAA,KlBjbd,yBkB4WS,MAAA,KA2ET,OAAA,KACA,WAAA,EACA,aAAA,MACA,YAAA,MACA,iBAAA,QLvcA,OAAA,ExBNa,cAAA,KWkBb,eAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YPufsH,WAAA,KyBtDtH,uCP/bgC,yBO0WvB,eAAA,KAAA,WAAA,MPzWL,gCOyWK,iBAAA,QLxXT,yBKwXS,MAAA,KA6FT,OAAA,MACA,MAAA,YACO,OAAA,QACP,iBAAA,YACkB,aAAA,YACJ,aAAA,MACd,8BAnGS,iBAAA,QAwGT,cAAA,KlBpdA,8BkB4WS,aAAA,KA6GT,iBAAA,QACA,cAAA,KlB1dA,6CkB8dQ,iBAAA,QAEN,sDAFM,OAAA,QAMN,yCANM,iBAAA,QAUN,yCAVM,OAAA,QAcN,kCAdM,iBAAA,QAkBN,8BAAA,mBAAA,eAON,mBAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAAA,WAAA,iBAAA,KAAA,WAAA,CAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,WAAA,CAAA,mBAAA,KAAA,YzBA0H,uCkBrftF,8BAAA,mBAAA,eOqfpC,mBAAA,KAAA,WAAA,MPpfQ,gBQnBR,eAAA,mBAAqC,WAAuB,eAAA,cACvB,cAAkB,eAAA,iBAClB,cAAqB,eAAA,iBACrB,mBAAqB,eAAA,sBACrB,gBAA0B,eAAA,mBAC1B,YCFnC,iBAAA,kBAC2B,mBAAA,mBAAA,wBAAA,wBAGrB,iBAAA,kBAEoC,cZQzC,iBAAA,kBYb0B,qBAAA,qBAAA,0BAAA,0BAGrB,iBAAA,kBAEoC,YZQzC,iBAAA,kBYb0B,mBAAA,mBAAA,wBAAA,wBAGrB,iBAAA,kBAEoC,SAAU,iBAAA,kBALzB,gBAAA,gBAAA,qBAAA,qBAGrB,iBAAA,kBAEoC,YZQzC,iBAAA,kBYb0B,mBAAA,mBAAA,wBAAA,wBAGrB,iBAAA,kBAEoC,WZQzC,iBAAA,kBYb0B,kBAAA,kBAAA,uBAAA,uBAGrB,iBAAA,kBAEoC,UZQzC,iBAAA,kBYb0B,iBAAA,iBAAA,sBAAA,sBAGrB,iBAAA,kBAEoC,SAAU,iBAAA,kBALzB,gBAAA,gBAAA,qBAAA,qBAGrB,iBAAA,kBAEoC,UZQzC,iBAAA,kBYb0B,iBAAA,iBAAA,sBAAA,sBAGrB,iBAAA,kBAEoC,aZQzC,iBAAA,kBYb0B,oBAAA,oBAAA,yBAAA,yBAGrB,iBAAA,kBAEoC,iBZQzC,iBAAA,kBYb0B,wBAAA,wBAAA,6BAAA,6BAGrB,iBAAA,kBAEoC,WZQzC,iBAAA,kBYb0B,kBAAA,kBAAA,uBAAA,uBAGrB,iBAAA,kBAEoC,UZQzC,iBAAA,eaNwB,gBAC1B,iBAAA,sBAGmB,QAAA,OAAA,IAAA,MAAA,kBCXwC,YAAc,WAAA,IAAA,MAAA,kBACV,cAAc,aAAA,IAAA,MAAA,kBACZ,eAAc,cAAA,IAAA,MAAA,kBACb,aAAc,YAAA,IAAA,MAAA,kBAChB,UAAc,OAAA,YAEpD,cAAgB,WAAA,YACZ,gBAAgB,aAAA,YACd,iBAAgB,cAAA,YACf,eAAgB,YAAA,YAClB,gBAAgB,aAAA,kBAIvB,kBADvB,aAAA,kBACuB,gBADvB,aAAA,kBACuB,aADvB,aAAA,kBACuB,gBADvB,aAAA,kBACuB,eADvB,aAAA,kBACuB,cADvB,aAAA,kBACuB,aADvB,aAAA,kBACuB,cADvB,aAAA,kBACuB,iBADvB,aAAA,kBACuB,qBADvB,aAAA,kBACuB,eADvB,aAAA,kBACuB,cACtB,aAAA,eAIoB,YACtB,cAAA,gBAOkC,SAAU,cAAA,iBAIb,aAC/B,uBAAA,iBAGwC,wBAAA,iBACC,eACzC,wBAAA,iBAGyC,2BAAA,iBACG,gBAC5C,2BAAA,iBAG4C,0BAAA,iBACD,cAC3C,uBAAA,iBAGwC,0BAAA,iBACG,YAC3C,cAAA,gBAGkC,gBAClC,cAAA,cAGgB,cAChB,cAAA,gBAG8B,WAC9B,cAAA,YAGgB,iBCxEf,QAAA,MACE,MAAA,KACA,QAAA,GACA,QAAS,QAAA,eCOiC,UAAxC,QAAA,iBAAwC,gBAAxC,QAAA,uBAAwC,SAAU,QAAA,gBAAV,SAAU,QAAA,gBAAV,aAAxC,QAAA,oBAAwC,cAAxC,QAAA,qBAAwC,QAAA,QAAA,sBAAA,QAAA,sBAAA,QAAA,eAAA,eAAxC,QAAA,6BAAA,QAAA,6BAAA,QAAA,sBAAwC,yBpBiDlC,WoBjDN,QAAA,eAAwC,aAAxC,QAAA,iBAAwC,mBAAxC,QAAA,uBAAwC,YAAxC,QAAA,gBAAwC,YAAxC,QAAA,gBAAwC,gBAAxC,QAAA,oBAAwC,iBAAxC,QAAA,qBAAwC,WAAxC,QAAA,sBAAA,QAAA,sBAAA,QAAA,eAAwC,kBAAxC,QAAA,6BAAA,QAAA,6BAAA,QAAA,uBAAwC,yBpBiDlC,WoBjDN,QAAA,eAAwC,aAAxC,QAAA,iBAAwC,mBAAxC,QAAA,uBAAwC,YAAxC,QAAA,gBAAwC,YAAxC,QAAA,gBAAwC,gBAAxC,QAAA,oBAAwC,iBAAxC,QAAA,qBAAwC,WAAxC,QAAA,sBAAA,QAAA,sBAAA,QAAA,eAAwC,kBAAxC,QAAA,6BAAA,QAAA,6BAAA,QAAA,uBAAwC,0BpBiDzB,WoBjDf,QAAA,eAAwC,aAAxC,QAAA,iBAAwC,mBAAxC,QAAA,uBAAwC,YAAxC,QAAA,gBAAwC,YAAxC,QAAA,gBAAwC,gBAAxC,QAAA,oBAAwC,iBAAxC,QAAA,qBAAwC,WAAxC,QAAA,sBAAA,QAAA,sBAAA,QAAA,eAAwC,kBAAxC,QAAA,6BAAA,QAAA,6BAAA,QAAA,uBAAwC,0BpBiDzB,WoBjDf,QAAA,eAAwC,aAAxC,QAAA,iBAAwC,mBAAxC,QAAA,uBAAwC,YAAxC,QAAA,gBAAwC,YAAxC,QAAA,gBAAwC,gBAAxC,QAAA,oBAAwC,iBAAxC,QAAA,qBAAwC,WAAxC,QAAA,sBAAA,QAAA,sBAAA,QAAA,eAAwC,kBAAxC,QAAA,6BAAA,QAAA,6BAAA,QAAA,uBAAwC,aAU9C,cAEI,QAAA,eAAqC,gBAArC,QAAA,iBAAqC,sBAArC,QAAA,uBAAqC,eAArC,QAAA,gBAAqC,eAArC,QAAA,gBAAqC,mBAArC,QAAA,oBAAqC,oBAArC,QAAA,qBAAqC,cAArC,QAAA,sBAAA,QAAA,sBAAA,QAAA,eAAqC,qBAArC,QAAA,6BAAA,QAAA,6BAAA,QAAA,uBAAqC,kBCrBzC,SAAA,SACU,QAAA,MACR,MAAA,KACA,QAAA,EACA,SAAA,OACA,0BALF,QAAA,MAQI,QAAA,GACA,yCAAA,wBAAA,yBAAA,yBAAA,wBATJ,SAAA,SAiBY,IAAA,EAAE,OAAA,EACJ,KAAA,EACA,MAAA,KAEN,OAAA,KACA,OAAA,EAAY,gCAEb,YAAA,UASgB,gCAFjB,YAAA,OAEI,+BAFJ,YAAA,IAEI,+BAFJ,YAAA,KAEI,UACD,mBAAA,qBAAA,sBAAA,iBAAA,mBAAA,cAAA,eAAA,cC1B+C,aAAkB,mBAAA,mBAAA,sBAAA,iBAAA,mBAAA,iBAAA,eAAA,iBAClB,kBAAqB,mBAAA,qBAAA,sBAAA,kBAAA,mBAAA,sBAAA,eAAA,sBACrB,qBAA0B,mBAAA,mBAAA,sBAAA,kBAAA,mBAAA,yBAAA,eAAA,yBAC1B,WAA6B,cAAA,eAAA,UAAA,eAEpC,aAAmB,cAAA,iBAAA,UAAA,iBACnB,mBAAqB,cAAA,uBAAA,UAAA,uBACrB,WAA2B,iBAAA,YAAA,SAAA,EAAA,EAAA,eAAA,KAAA,EAAA,EAAA,eAChC,aAAuB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAClB,aAAgB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAChB,eAAgB,kBAAA,YAAA,YAAA,YACd,eAAgB,kBAAA,YAAA,YAAA,YAChB,uBAAgB,iBAAA,gBAAA,cAAA,gBAAA,gBAAA,qBAEN,qBAAyB,iBAAA,cAAA,cAAA,cAAA,gBAAA,mBACzB,wBAAuB,iBAAA,iBAAA,cAAA,iBAAA,gBAAA,iBACvB,yBAAqB,iBAAA,kBAAA,cAAA,kBAAA,gBAAA,wBACrB,wBAA4B,cAAA,qBAAA,gBAAA,uBAC5B,mBAA2B,kBAAA,gBAAA,eAAA,gBAAA,YAAA,qBAElC,iBAAyB,kBAAA,cAAA,eAAA,cAAA,YAAA,mBACzB,oBAAuB,kBAAA,iBAAA,eAAA,iBAAA,YAAA,iBACvB,sBAAqB,kBAAA,mBAAA,eAAA,mBAAA,YAAA,mBACrB,qBAAuB,kBAAA,kBAAA,eAAA,kBAAA,YAAA,kBACvB,qBAAsB,mBAAA,gBAAA,cAAA,qBAEnB,mBAAyB,mBAAA,cAAA,cAAA,mBACzB,sBAAuB,mBAAA,iBAAA,cAAA,iBACvB,uBAAqB,mBAAA,kBAAA,cAAA,wBACrB,sBAA4B,mBAAA,qBAAA,cAAA,uBAC5B,uBAA2B,mBAAA,kBAAA,cAAA,kBAC3B,iBAAsB,oBAAA,eAAA,WAAA,eAE3B,kBAAmB,oBAAA,gBAAA,WAAA,qBACnB,gBAAyB,oBAAA,cAAA,WAAA,mBACzB,mBAAuB,oBAAA,iBAAA,WAAA,iBACvB,qBAAqB,oBAAA,mBAAA,WAAA,mBACrB,oBAAuB,oBAAA,kBAAA,WAAA,kBACvB,yBtBYpC,asBlDR,mBAAA,qBAAA,sBAAA,iBAAA,mBAAA,cAAA,eAAA,cAAgD,gBAAkB,mBAAA,mBAAA,sBAAA,iBAAA,mBAAA,iBAAA,eAAA,iBAClB,qBAAqB,mBAAA,qBAAA,sBAAA,kBAAA,mBAAA,sBAAA,eAAA,sBACrB,wBAA0B,mBAAA,mBAAA,sBAAA,kBAAA,mBAAA,yBAAA,eAAA,yBAC1B,cAA6B,cAAA,eAAA,UAAA,eAEpC,gBAAmB,cAAA,iBAAA,UAAA,iBACnB,sBAAqB,cAAA,uBAAA,UAAA,uBACrB,cAA2B,iBAAA,YAAA,SAAA,EAAA,EAAA,eAAA,KAAA,EAAA,EAAA,eAChC,gBAAuB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAClB,gBAAgB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAChB,kBAAgB,kBAAA,YAAA,YAAA,YACd,kBAAgB,kBAAA,YAAA,YAAA,YAChB,0BAAgB,iBAAA,gBAAA,cAAA,gBAAA,gBAAA,qBAEN,wBAAyB,iBAAA,cAAA,cAAA,cAAA,gBAAA,mBACzB,2BAAuB,iBAAA,iBAAA,cAAA,iBAAA,gBAAA,iBACvB,4BAAqB,iBAAA,kBAAA,cAAA,kBAAA,gBAAA,wBACrB,2BAA4B,cAAA,qBAAA,gBAAA,uBAC5B,sBAA2B,kBAAA,gBAAA,eAAA,gBAAA,YAAA,qBAElC,oBAAyB,kBAAA,cAAA,eAAA,cAAA,YAAA,mBACzB,uBAAuB,kBAAA,iBAAA,eAAA,iBAAA,YAAA,iBACvB,yBAAqB,kBAAA,mBAAA,eAAA,mBAAA,YAAA,mBACrB,wBAAuB,kBAAA,kBAAA,eAAA,kBAAA,YAAA,kBACvB,wBAAsB,mBAAA,gBAAA,cAAA,qBAEnB,sBAAyB,mBAAA,cAAA,cAAA,mBACzB,yBAAuB,mBAAA,iBAAA,cAAA,iBACvB,0BAAqB,mBAAA,kBAAA,cAAA,wBACrB,yBAA4B,mBAAA,qBAAA,cAAA,uBAC5B,0BAA2B,mBAAA,kBAAA,cAAA,kBAC3B,oBAAsB,oBAAA,eAAA,WAAA,eAE3B,qBAAmB,oBAAA,gBAAA,WAAA,qBACnB,mBAAyB,oBAAA,cAAA,WAAA,mBACzB,sBAAuB,oBAAA,iBAAA,WAAA,iBACvB,wBAAqB,oBAAA,mBAAA,WAAA,mBACrB,uBAAuB,oBAAA,kBAAA,WAAA,mBACvB,yBtBYpC,asBlDR,mBAAA,qBAAA,sBAAA,iBAAA,mBAAA,cAAA,eAAA,cAAgD,gBAAkB,mBAAA,mBAAA,sBAAA,iBAAA,mBAAA,iBAAA,eAAA,iBAClB,qBAAqB,mBAAA,qBAAA,sBAAA,kBAAA,mBAAA,sBAAA,eAAA,sBACrB,wBAA0B,mBAAA,mBAAA,sBAAA,kBAAA,mBAAA,yBAAA,eAAA,yBAC1B,cAA6B,cAAA,eAAA,UAAA,eAEpC,gBAAmB,cAAA,iBAAA,UAAA,iBACnB,sBAAqB,cAAA,uBAAA,UAAA,uBACrB,cAA2B,iBAAA,YAAA,SAAA,EAAA,EAAA,eAAA,KAAA,EAAA,EAAA,eAChC,gBAAuB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAClB,gBAAgB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAChB,kBAAgB,kBAAA,YAAA,YAAA,YACd,kBAAgB,kBAAA,YAAA,YAAA,YAChB,0BAAgB,iBAAA,gBAAA,cAAA,gBAAA,gBAAA,qBAEN,wBAAyB,iBAAA,cAAA,cAAA,cAAA,gBAAA,mBACzB,2BAAuB,iBAAA,iBAAA,cAAA,iBAAA,gBAAA,iBACvB,4BAAqB,iBAAA,kBAAA,cAAA,kBAAA,gBAAA,wBACrB,2BAA4B,cAAA,qBAAA,gBAAA,uBAC5B,sBAA2B,kBAAA,gBAAA,eAAA,gBAAA,YAAA,qBAElC,oBAAyB,kBAAA,cAAA,eAAA,cAAA,YAAA,mBACzB,uBAAuB,kBAAA,iBAAA,eAAA,iBAAA,YAAA,iBACvB,yBAAqB,kBAAA,mBAAA,eAAA,mBAAA,YAAA,mBACrB,wBAAuB,kBAAA,kBAAA,eAAA,kBAAA,YAAA,kBACvB,wBAAsB,mBAAA,gBAAA,cAAA,qBAEnB,sBAAyB,mBAAA,cAAA,cAAA,mBACzB,yBAAuB,mBAAA,iBAAA,cAAA,iBACvB,0BAAqB,mBAAA,kBAAA,cAAA,wBACrB,yBAA4B,mBAAA,qBAAA,cAAA,uBAC5B,0BAA2B,mBAAA,kBAAA,cAAA,kBAC3B,oBAAsB,oBAAA,eAAA,WAAA,eAE3B,qBAAmB,oBAAA,gBAAA,WAAA,qBACnB,mBAAyB,oBAAA,cAAA,WAAA,mBACzB,sBAAuB,oBAAA,iBAAA,WAAA,iBACvB,wBAAqB,oBAAA,mBAAA,WAAA,mBACrB,uBAAuB,oBAAA,kBAAA,WAAA,mBACvB,0BtBY3B,asBlDjB,mBAAA,qBAAA,sBAAA,iBAAA,mBAAA,cAAA,eAAA,cAAgD,gBAAkB,mBAAA,mBAAA,sBAAA,iBAAA,mBAAA,iBAAA,eAAA,iBAClB,qBAAqB,mBAAA,qBAAA,sBAAA,kBAAA,mBAAA,sBAAA,eAAA,sBACrB,wBAA0B,mBAAA,mBAAA,sBAAA,kBAAA,mBAAA,yBAAA,eAAA,yBAC1B,cAA6B,cAAA,eAAA,UAAA,eAEpC,gBAAmB,cAAA,iBAAA,UAAA,iBACnB,sBAAqB,cAAA,uBAAA,UAAA,uBACrB,cAA2B,iBAAA,YAAA,SAAA,EAAA,EAAA,eAAA,KAAA,EAAA,EAAA,eAChC,gBAAuB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAClB,gBAAgB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAChB,kBAAgB,kBAAA,YAAA,YAAA,YACd,kBAAgB,kBAAA,YAAA,YAAA,YAChB,0BAAgB,iBAAA,gBAAA,cAAA,gBAAA,gBAAA,qBAEN,wBAAyB,iBAAA,cAAA,cAAA,cAAA,gBAAA,mBACzB,2BAAuB,iBAAA,iBAAA,cAAA,iBAAA,gBAAA,iBACvB,4BAAqB,iBAAA,kBAAA,cAAA,kBAAA,gBAAA,wBACrB,2BAA4B,cAAA,qBAAA,gBAAA,uBAC5B,sBAA2B,kBAAA,gBAAA,eAAA,gBAAA,YAAA,qBAElC,oBAAyB,kBAAA,cAAA,eAAA,cAAA,YAAA,mBACzB,uBAAuB,kBAAA,iBAAA,eAAA,iBAAA,YAAA,iBACvB,yBAAqB,kBAAA,mBAAA,eAAA,mBAAA,YAAA,mBACrB,wBAAuB,kBAAA,kBAAA,eAAA,kBAAA,YAAA,kBACvB,wBAAsB,mBAAA,gBAAA,cAAA,qBAEnB,sBAAyB,mBAAA,cAAA,cAAA,mBACzB,yBAAuB,mBAAA,iBAAA,cAAA,iBACvB,0BAAqB,mBAAA,kBAAA,cAAA,wBACrB,yBAA4B,mBAAA,qBAAA,cAAA,uBAC5B,0BAA2B,mBAAA,kBAAA,cAAA,kBAC3B,oBAAsB,oBAAA,eAAA,WAAA,eAE3B,qBAAmB,oBAAA,gBAAA,WAAA,qBACnB,mBAAyB,oBAAA,cAAA,WAAA,mBACzB,sBAAuB,oBAAA,iBAAA,WAAA,iBACvB,wBAAqB,oBAAA,mBAAA,WAAA,mBACrB,uBAAuB,oBAAA,kBAAA,WAAA,mBACvB,0BtBY3B,asBlDjB,mBAAA,qBAAA,sBAAA,iBAAA,mBAAA,cAAA,eAAA,cAAgD,gBAAkB,mBAAA,mBAAA,sBAAA,iBAAA,mBAAA,iBAAA,eAAA,iBAClB,qBAAqB,mBAAA,qBAAA,sBAAA,kBAAA,mBAAA,sBAAA,eAAA,sBACrB,wBAA0B,mBAAA,mBAAA,sBAAA,kBAAA,mBAAA,yBAAA,eAAA,yBAC1B,cAA6B,cAAA,eAAA,UAAA,eAEpC,gBAAmB,cAAA,iBAAA,UAAA,iBACnB,sBAAqB,cAAA,uBAAA,UAAA,uBACrB,cAA2B,iBAAA,YAAA,SAAA,EAAA,EAAA,eAAA,KAAA,EAAA,EAAA,eAChC,gBAAuB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAClB,gBAAgB,iBAAA,YAAA,kBAAA,YAAA,UAAA,YAChB,kBAAgB,kBAAA,YAAA,YAAA,YACd,kBAAgB,kBAAA,YAAA,YAAA,YAChB,0BAAgB,iBAAA,gBAAA,cAAA,gBAAA,gBAAA,qBAEN,wBAAyB,iBAAA,cAAA,cAAA,cAAA,gBAAA,mBACzB,2BAAuB,iBAAA,iBAAA,cAAA,iBAAA,gBAAA,iBACvB,4BAAqB,iBAAA,kBAAA,cAAA,kBAAA,gBAAA,wBACrB,2BAA4B,cAAA,qBAAA,gBAAA,uBAC5B,sBAA2B,kBAAA,gBAAA,eAAA,gBAAA,YAAA,qBAElC,oBAAyB,kBAAA,cAAA,eAAA,cAAA,YAAA,mBACzB,uBAAuB,kBAAA,iBAAA,eAAA,iBAAA,YAAA,iBACvB,yBAAqB,kBAAA,mBAAA,eAAA,mBAAA,YAAA,mBACrB,wBAAuB,kBAAA,kBAAA,eAAA,kBAAA,YAAA,kBACvB,wBAAsB,mBAAA,gBAAA,cAAA,qBAEnB,sBAAyB,mBAAA,cAAA,cAAA,mBACzB,yBAAuB,mBAAA,iBAAA,cAAA,iBACvB,0BAAqB,mBAAA,kBAAA,cAAA,wBACrB,yBAA4B,mBAAA,qBAAA,cAAA,uBAC5B,0BAA2B,mBAAA,kBAAA,cAAA,kBAC3B,oBAAsB,oBAAA,eAAA,WAAA,eAE3B,qBAAmB,oBAAA,gBAAA,WAAA,qBACnB,mBAAyB,oBAAA,cAAA,WAAA,mBACzB,sBAAuB,oBAAA,iBAAA,WAAA,iBACvB,wBAAqB,oBAAA,mBAAA,WAAA,mBACrB,uBAAuB,oBAAA,kBAAA,WAAA,mBACvB,YC1C5C,MAAA,eAA+B,aAAmB,MAAA,gBACnB,YAAoB,MAAA,eACpB,yBvBoDvB,euBtDR,MAAA,eAA+B,gBAAmB,MAAA,gBACnB,eAAoB,MAAA,gBACpB,yBvBoDvB,euBtDR,MAAA,eAA+B,gBAAmB,MAAA,gBACnB,eAAoB,MAAA,gBACpB,0BvBoDd,euBtDjB,MAAA,eAA+B,gBAAmB,MAAA,gBACnB,eAAoB,MAAA,gBACpB,0BvBoDd,euBtDjB,MAAA,eAA+B,gBAAmB,MAAA,gBACnB,eAAoB,MAAA,gBACpB,iBCLjC,oBAAA,cAAA,iBAAA,cAAA,gBAAA,cAAA,YAAA,cAA6C,kBAA7C,oBAAA,eAAA,iBAAA,eAAA,gBAAA,eAAA,YAAA,eAA6C,kBAA7C,oBAAA,eAAA,iBAAA,eAAA,gBAAA,eAAA,YAAA,eAA6C,eAAc,SAAA,eCApB,iBAAvC,SAAA,iBAAuC,iBAAc,SAAA,iBCCR,mBAA7C,SAAA,mBAA6C,mBAA7C,SAAA,mBAA6C,gBAA7C,SAAA,gBAA6C,iBAA7C,SAAA,iBAA6C,WAAc,SAAA,MAM3D,IAAA,EAAU,MAAA,EACL,KAAA,EACA,QAAA,KAEL,cACD,SAAA,MAGC,MAAA,EAAU,OAAA,EACF,KAAA,EACF,QAAA,KAEN,4BAImB,YADrB,SAAA,OAEI,IAAA,EAAU,QAAA,MAEV,SAEH,SAAA,SCzBS,MAAA,IACR,OAAA,IACA,QAAA,EACA,OAAA,KACA,SAAA,OACA,KAAA,cACM,YAAA,OACN,OAAA,EAAmB,0BAAA,yBCLrB,SAAA,ODkBI,MAAA,KACA,OAAA,KACA,SAAA,QACA,KAAA,KACA,YAAA,OACA,WACD,mBAAA,EAAA,QAAA,OAAA,2BAAA,WAAA,EAAA,QAAA,OAAA,2BE9BqC,QAAA,mBAAA,EAAA,MAAA,KAAA,0BAAA,WAAA,EAAA,MAAA,KAAA,0BACN,WAAc,mBAAA,EAAA,KAAA,KAAA,2BAAA,WAAA,EAAA,KAAA,KAAA,2BACR,aAAc,mBAAA,eAAA,WAAA,eAC3B,MAAA,MAAA,cCCkB,MAAA,MAAA,cAAA,MAAA,MAAA,cAAA,OAAA,MAAA,eAAA,QAAA,MAAA,eAAA,MAAA,OAAA,cAAA,MAAA,OAAA,cAAA,MAAA,OAAA,cAAA,OAAA,OAAA,eAAA,QAAA,OAAA,eAAA,QAAA,UAAA,eAIxB,QAAA,WAAA,eACC,YAAmB,UAAA,gBAIhB,YAAoB,WAAA,gBACnB,QAAA,MAAA,gBAET,QAAA,OAAA,gBACC,KAAA,OAAA,YCTwC,MAAA,MAAc,WAAA,YAGpC,MAAA,MAC3B,aAAA,YAG+B,MAAA,MAC/B,cAAA,YAGiC,MAAA,MACjC,YAAA,YAG6B,KAAA,OAAA,iBAfoB,MAAA,MAAc,WAAA,iBAGpC,MAAA,MAC3B,aAAA,iBAG+B,MAAA,MAC/B,cAAA,iBAGiC,MAAA,MACjC,YAAA,iBAG6B,KAAA,OAAA,gBAfoB,MAAA,MAAc,WAAA,gBAGpC,MAAA,MAC3B,aAAA,gBAG+B,MAAA,MAC/B,cAAA,gBAGiC,MAAA,MACjC,YAAA,gBAG6B,KAAA,OAAA,eAfoB,MAAA,MAAc,WAAA,eAGpC,MAAA,MAC3B,aAAA,eAG+B,MAAA,MAC/B,cAAA,eAGiC,MAAA,MACjC,YAAA,eAG6B,KAAA,OAAA,iBAfoB,MAAA,MAAc,WAAA,iBAGpC,MAAA,MAC3B,aAAA,iBAG+B,MAAA,MAC/B,cAAA,iBAGiC,MAAA,MACjC,YAAA,iBAG6B,KAAA,OAAA,eAfoB,MAAA,MAAc,WAAA,eAGpC,MAAA,MAC3B,aAAA,eAG+B,MAAA,MAC/B,cAAA,eAGiC,MAAA,MACjC,YAAA,eAG6B,KAAA,QAAA,YAfoB,MAAA,MAAc,YAAA,YAGpC,MAAA,MAC3B,cAAA,YAG+B,MAAA,MAC/B,eAAA,YAGiC,MAAA,MACjC,aAAA,YAG6B,KAAA,QAAA,iBAfoB,MAAA,MAAc,YAAA,iBAGpC,MAAA,MAC3B,cAAA,iBAG+B,MAAA,MAC/B,eAAA,iBAGiC,MAAA,MACjC,aAAA,iBAG6B,KAAA,QAAA,gBAfoB,MAAA,MAAc,YAAA,gBAGpC,MAAA,MAC3B,cAAA,gBAG+B,MAAA,MAC/B,eAAA,gBAGiC,MAAA,MACjC,aAAA,gBAG6B,KAAA,QAAA,eAfoB,MAAA,MAAc,YAAA,eAGpC,MAAA,MAC3B,cAAA,eAG+B,MAAA,MAC/B,eAAA,eAGiC,MAAA,MACjC,aAAA,eAG6B,KAAA,QAAA,iBAfoB,MAAA,MAAc,YAAA,iBAGpC,MAAA,MAC3B,cAAA,iBAG+B,MAAA,MAC/B,eAAA,iBAGiC,MAAA,MACjC,aAAA,iBAG6B,KAAA,QAAA,eAfoB,MAAA,MAAc,YAAA,eAGpC,MAAA,MAC3B,cAAA,eAG+B,MAAA,MAC/B,eAAA,eAGiC,MAAA,MACjC,aAAA,eAG6B,MAAA,OAAA,kBAQW,OAAA,OAAc,WAAA,kBAGhC,OAAA,OACtB,aAAA,kBAGwB,OAAA,OACxB,cAAA,kBAGyB,OAAA,OACzB,YAAA,kBAGuB,MAAA,OAAA,iBAfiB,OAAA,OAAc,WAAA,iBAGhC,OAAA,OACtB,aAAA,iBAGwB,OAAA,OACxB,cAAA,iBAGyB,OAAA,OACzB,YAAA,iBAGuB,MAAA,OAAA,gBAfiB,OAAA,OAAc,WAAA,gBAGhC,OAAA,OACtB,aAAA,gBAGwB,OAAA,OACxB,cAAA,gBAGyB,OAAA,OACzB,YAAA,gBAGuB,MAAA,OAAA,kBAfiB,OAAA,OAAc,WAAA,kBAGhC,OAAA,OACtB,aAAA,kBAGwB,OAAA,OACxB,cAAA,kBAGyB,OAAA,OACzB,YAAA,kBAGuB,MAAA,OAAA,gBAfiB,OAAA,OAAc,WAAA,gBAGhC,OAAA,OACtB,aAAA,gBAGwB,OAAA,OACxB,cAAA,gBAGyB,OAAA,OACzB,YAAA,gBAGuB,QAAA,OAAA,eAMD,SAAA,SACnB,WAAA,eAEM,SAAA,SAEN,aAAA,eAEQ,SAAA,SAER,cAAA,eAES,SAAA,SAET,YAAA,eAEO,yB/BTP,QAAgB,OAAA,Y+BlD8B,SAAA,SAC1C,WAAA,YAEoB,SAAA,SAEpB,aAAA,YAEwB,SAAA,SAExB,cAAA,YAE0B,SAAA,SAE1B,YAAA,YAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,gBAfoB,SAAA,SAC1C,WAAA,gBAEoB,SAAA,SAEpB,aAAA,gBAEwB,SAAA,SAExB,cAAA,gBAE0B,SAAA,SAE1B,YAAA,gBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,QAAA,YAfoB,SAAA,SAC1C,YAAA,YAEoB,SAAA,SAEpB,cAAA,YAEwB,SAAA,SAExB,eAAA,YAE0B,SAAA,SAE1B,aAAA,YAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,gBAfoB,SAAA,SAC1C,YAAA,gBAEoB,SAAA,SAEpB,cAAA,gBAEwB,SAAA,SAExB,eAAA,gBAE0B,SAAA,SAE1B,aAAA,gBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,SAAU,OAAA,kBAQC,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,iBAfO,UAAA,UAEzC,WAAA,iBACuB,UAAA,UAGvB,aAAA,iBACyB,UAAA,UAGzB,cAAA,iBAC0B,UAAA,UAG1B,YAAA,iBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,SAAU,OAAA,kBAfO,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,WACvB,OAAA,eAKsB,YAAA,YAE3B,WAAA,eACc,YAAA,YAGd,aAAA,eACgB,YAAA,YAGhB,cAAA,eACiB,YAAA,YAGjB,YAAA,gBACe,yB/BTP,QAAgB,OAAA,Y+BlD8B,SAAA,SAC1C,WAAA,YAEoB,SAAA,SAEpB,aAAA,YAEwB,SAAA,SAExB,cAAA,YAE0B,SAAA,SAE1B,YAAA,YAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,gBAfoB,SAAA,SAC1C,WAAA,gBAEoB,SAAA,SAEpB,aAAA,gBAEwB,SAAA,SAExB,cAAA,gBAE0B,SAAA,SAE1B,YAAA,gBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,QAAA,YAfoB,SAAA,SAC1C,YAAA,YAEoB,SAAA,SAEpB,cAAA,YAEwB,SAAA,SAExB,eAAA,YAE0B,SAAA,SAE1B,aAAA,YAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,gBAfoB,SAAA,SAC1C,YAAA,gBAEoB,SAAA,SAEpB,cAAA,gBAEwB,SAAA,SAExB,eAAA,gBAE0B,SAAA,SAE1B,aAAA,gBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,SAAU,OAAA,kBAQC,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,iBAfO,UAAA,UAEzC,WAAA,iBACuB,UAAA,UAGvB,aAAA,iBACyB,UAAA,UAGzB,cAAA,iBAC0B,UAAA,UAG1B,YAAA,iBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,SAAU,OAAA,kBAfO,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,WACvB,OAAA,eAKsB,YAAA,YAE3B,WAAA,eACc,YAAA,YAGd,aAAA,eACgB,YAAA,YAGhB,cAAA,eACiB,YAAA,YAGjB,YAAA,gBACe,0B/BTE,QAAQ,OAAA,Y+BlD6B,SAAA,SAC1C,WAAA,YAEoB,SAAA,SAEpB,aAAA,YAEwB,SAAA,SAExB,cAAA,YAE0B,SAAA,SAE1B,YAAA,YAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,gBAfoB,SAAA,SAC1C,WAAA,gBAEoB,SAAA,SAEpB,aAAA,gBAEwB,SAAA,SAExB,cAAA,gBAE0B,SAAA,SAE1B,YAAA,gBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,QAAA,YAfoB,SAAA,SAC1C,YAAA,YAEoB,SAAA,SAEpB,cAAA,YAEwB,SAAA,SAExB,eAAA,YAE0B,SAAA,SAE1B,aAAA,YAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,gBAfoB,SAAA,SAC1C,YAAA,gBAEoB,SAAA,SAEpB,cAAA,gBAEwB,SAAA,SAExB,eAAA,gBAE0B,SAAA,SAE1B,aAAA,gBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,SAAU,OAAA,kBAQC,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,iBAfO,UAAA,UAEzC,WAAA,iBACuB,UAAA,UAGvB,aAAA,iBACyB,UAAA,UAGzB,cAAA,iBAC0B,UAAA,UAG1B,YAAA,iBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,SAAU,OAAA,kBAfO,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,WACvB,OAAA,eAKsB,YAAA,YAE3B,WAAA,eACc,YAAA,YAGd,aAAA,eACgB,YAAA,YAGhB,cAAA,eACiB,YAAA,YAGjB,YAAA,gBACe,0B/BTE,QAAQ,OAAA,Y+BlD6B,SAAA,SAC1C,WAAA,YAEoB,SAAA,SAEpB,aAAA,YAEwB,SAAA,SAExB,cAAA,YAE0B,SAAA,SAE1B,YAAA,YAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,gBAfoB,SAAA,SAC1C,WAAA,gBAEoB,SAAA,SAEpB,aAAA,gBAEwB,SAAA,SAExB,cAAA,gBAE0B,SAAA,SAE1B,YAAA,gBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,OAAA,iBAfoB,SAAA,SAC1C,WAAA,iBAEoB,SAAA,SAEpB,aAAA,iBAEwB,SAAA,SAExB,cAAA,iBAE0B,SAAA,SAE1B,YAAA,iBAEsB,QAAA,OAAA,eAfoB,SAAA,SAC1C,WAAA,eAEoB,SAAA,SAEpB,aAAA,eAEwB,SAAA,SAExB,cAAA,eAE0B,SAAA,SAE1B,YAAA,eAEsB,QAAA,QAAA,YAfoB,SAAA,SAC1C,YAAA,YAEoB,SAAA,SAEpB,cAAA,YAEwB,SAAA,SAExB,eAAA,YAE0B,SAAA,SAE1B,aAAA,YAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,gBAfoB,SAAA,SAC1C,YAAA,gBAEoB,SAAA,SAEpB,cAAA,gBAEwB,SAAA,SAExB,eAAA,gBAE0B,SAAA,SAE1B,aAAA,gBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,QAAA,QAAA,iBAfoB,SAAA,SAC1C,YAAA,iBAEoB,SAAA,SAEpB,cAAA,iBAEwB,SAAA,SAExB,eAAA,iBAE0B,SAAA,SAE1B,aAAA,iBAEsB,QAAA,QAAA,eAfoB,SAAA,SAC1C,YAAA,eAEoB,SAAA,SAEpB,cAAA,eAEwB,SAAA,SAExB,eAAA,eAE0B,SAAA,SAE1B,aAAA,eAEsB,SAAU,OAAA,kBAQC,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,iBAfO,UAAA,UAEzC,WAAA,iBACuB,UAAA,UAGvB,aAAA,iBACyB,UAAA,UAGzB,cAAA,iBAC0B,UAAA,UAG1B,YAAA,iBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,SAAU,OAAA,kBAfO,UAAA,UAEzC,WAAA,kBACuB,UAAA,UAGvB,aAAA,kBACyB,UAAA,UAGzB,cAAA,kBAC0B,UAAA,UAG1B,YAAA,kBACwB,SAAU,OAAA,gBAfO,UAAA,UAEzC,WAAA,gBACuB,UAAA,UAGvB,aAAA,gBACyB,UAAA,UAGzB,cAAA,gBAC0B,UAAA,UAG1B,YAAA,gBACwB,WACvB,OAAA,eAKsB,YAAA,YAE3B,WAAA,eACc,YAAA,YAGd,aAAA,eACgB,YAAA,YAGhB,cAAA,eACiB,YAAA,YAGjB,YAAA,gBACe,uBCjEnB,SAAA,SAEY,IAAA,EAAE,MAAA,EACL,OAAA,EACG,KAAA,EACF,QAAA,EAEN,eAAA,KAEA,QAAA,GACA,iBAAA,cAEkB,gBACnB,YAAA,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,oBCXmD,cAAc,WAAA,kBAIvC,WAAsB,YAAA,iBACrB,aAAqB,YAAA,iBACrB,eAAqB,SAAA,OCRjD,cAAA,SACa,YAAA,OACb,WDO2C,WAAA,eAQL,YAAmB,WAAA,gBACnB,aAAoB,WAAA,iBACpB,yBjCqC5B,ciCvCR,WAAA,eAAoC,eAAmB,WAAA,gBACnB,gBAAoB,WAAA,kBACpB,yBjCqC5B,ciCvCR,WAAA,eAAoC,eAAmB,WAAA,gBACnB,gBAAoB,WAAA,kBACpB,0BjCqCnB,ciCvCjB,WAAA,eAAoC,eAAmB,WAAA,gBACnB,gBAAoB,WAAA,kBACpB,0BjCqCnB,ciCvCjB,WAAA,eAAoC,eAAmB,WAAA,gBACnB,gBAAoB,WAAA,kBACpB,gBAMxC,eAAA,oBAAmC,gBAAwB,eAAA,oBACxB,iBAAwB,eAAA,qBACxB,mBAAyB,YAAA,cAIL,qBAAc,YAAA,kBACZ,oBAAc,YAAA,cACf,kBAAc,YAAA,cAChB,oBAAc,YAAA,iBACZ,aAAc,WAAA,iBACnC,YAAqB,MAAA,eAI5B,cEvC1B,MAAA,kBACgB,qBAAA,qBAGb,MAAA,kBAEmE,gB/BQrE,MAAA,kB+Bbe,uBAAA,uBAGb,MAAA,kBAEmE,c/BQrE,MAAA,kB+Bbe,qBAAA,qBAGb,MAAA,kBAEmE,W/BQrE,MAAA,kB+Bbe,kBAAA,kBAGb,MAAA,kBAEmE,c/BQrE,MAAA,kB+Bbe,qBAAA,qBAGb,MAAA,kBAEmE,a/BQrE,MAAA,kB+Bbe,oBAAA,oBAGb,MAAA,kBAEmE,Y/BQrE,MAAA,kB+Bbe,mBAAA,mBAGb,MAAA,kBAEmE,W/BQrE,MAAA,kB+Bbe,kBAAA,kBAGb,MAAA,kBAEmE,Y/BQrE,MAAA,kB+Bbe,mBAAA,mBAGb,MAAA,kBAEmE,e/BQrE,MAAA,kB+Bbe,sBAAA,sBAGb,MAAA,kBAEmE,mB/BQrE,MAAA,kB+Bbe,0BAAA,0BAGb,MAAA,kBAEmE,a/BQrE,MAAA,kB+Bbe,oBAAA,oBAGb,MAAA,kBAEmE,W/BQrE,MAAA,kB6B+B6B,YAAc,MAAA,kBACb,eAAc,MAAA,yBAEN,eAAc,MAAA,+BACd,WAAc,KAAA,CAAA,CAAA,EAAA,EGnDrD,MAAA,YACO,YAAA,KACP,iBAAA,YACkB,OAAA,EAAW,sBHsD9B,gBAAA,eAEwC,YAAmB,WAAA,qBAG9C,UAAA,qBACD,YACZ,MAAA,kBAIoB,SAAkB,WAAA,kBIhEzB,WACb,WAAA,iBAGa;;;ACRX,WCCD,YAAA,YACA,IAAA,8CACA,IAAA,qDAAA,2BAAA,CAAA,gDAAA,eAAA,CAAA,+CAAA,cAAA,CAAA,8CAAA,kBAAA,CAAA,iEAAA,cAMA,YAAA,IACA,WAAA,OCVF,IACE,QAAA,aACA,KAAA,OAAA,OAAA,OAAA,IAAA,CAAA,EAAA,YACA,UAAA,QACA,eAAA,KACA,uBAAA,YACA,wBAAA,UAED,OCNC,UAAA,UACA,YAAA,MACA,eAAA,KACD,OACuB,UAAA,IAAkB,OAClB,UAAA,IAAkB,OAClB,UAAA,IAAkB,OAClB,UAAA,IAAkB,OCTxC,MAAA,UACA,WAAA,OACD,OCDC,aAAA,EACA,YAAA,UACA,gBAAA,KAHF,UAIS,SAAA,SAAsB,OAG7B,SAAA,SACA,KAAA,WACA,MAAA,UACA,IAAA,SACA,WAAA,OALF,aAOI,KAAA,WACD,WCbD,QAAA,KAAA,MAAA,MACA,OAAA,MAAA,MAAA,KACA,cAAA,KACD,cAE8B,MAAA,KAAe,eACd,MAAA,MAAgB,iBAGd,aAAA,KADlC,kBAEmC,YAAA,KAAqB,YAI1C,MAAA,MAAgB,WACjB,MAAA,KAAe,cAGZ,aAAA,KADhB,eAEiB,YAAA,KAAqB,SCnBpC,kBAAA,QAAA,GAAA,SAAA,OACQ,UAAA,QAAA,GAAA,SAAA,OACT,UAGC,kBAAA,QAAA,GAAA,SAAA,SACQ,UAAA,QAAA,GAAA,SAAA,SACT,2BAGC,GACE,kBAAA,UACQ,UAAA,UAEV,KACE,kBAAA,eACQ,UAAA,gBAIZ,mBACE,GACE,kBAAA,UACQ,UAAA,UAEV,KACE,kBAAA,eACQ,UAAA,gBC5BZ,cCYE,kBAAA,cAEQ,UAAA,cDd4D,eCYpE,kBAAA,eAEQ,UAAA,eDb4D,eCWpE,kBAAA,eAEQ,UAAA,eDZ4D,oBCiBpE,kBAAA,YAEQ,UAAA,YDjB8D,kBCetE,kBAAA,YAEQ,UAAA,YDhB8D,0BAAA,wBAAA,qBAAA,qBAAA,oBAUtE,eAAA,KAAA,OAAA,KACD,UEfC,SAAA,SACA,QAAA,aACA,MAAA,IACA,OAAA,IACA,YAAA,IACA,eAAA,OACD,aAAA,aAEC,SAAA,SACA,KAAA,EACA,MAAA,KACA,WAAA,OACD,aAC6B,YAAA,QAAwB,aACxB,UAAA,IAAkB,YACnB,MAAA,KAAsB,iBChBjB,QAAA,ICwUZ,iBDvUN,QAAA,IC2dD,kBD1dL,QAAA,IC0jBM,sBDzjBF,QAAA,ICsOM,iBDrOX,QAAA,ICuWM,gBDtWP,QAAA,IAAgC,kBAC9B,QAAA,IAAyB,gBAC3B,QAAA,IAAyB,gBACzB,QAAA,IAAyB,oBAAyB,QAAA,IACrB,cAA6B,QAAA,IACnC,mBAAuB,QAAA,IAClB,iBAA4B,QAAA,IAC9B,iBAAA,kBAAA,iBAGlC,QAAA,IAAgB,uBAA4C,QAAA,IAC7C,wBAAyD,QAAA,IACxD,qBAA0D,QAAA,IAC7D,kBAAuD,QAAA,IAC1D,eAAA,gBACF,QAAA,IACD,mBAAiD,QAAA,IAC7C,gBC+qBM,QAAA,ID9qBT,kBCwVM,QAAA,IDxV4C,mBCwP1C,QAAA,IDvP8C,gBAC1B,QAAA,IAA4B,oBCohBlD,QAAA,IDnhB4C,+BACQ,QAAA,IAAA,6BACsB,QAAA,IAAA,iBAC1C,QAAA,IAAsC,yBCsWrE,QAAA,IDrW6C,kBAAA,wBACgB,QAAA,IAE5E,mBAAmC,QAAA,IAA2B,oBAC1B,QAAA,IAA4B,gBACpD,QAAA,ICwYM,gBDvYV,QAAA,IC2YM,sBD1YmB,QAAA,IAAyB,sBACnB,QAAA,ICoUZ,uBDnUY,QAAA,ICitBZ,qBDhtBZ,QAAA,IC+sBM,kBD9sBR,QAAA,IAAyB,mBAC5B,QAAA,IAAyB,eAA2B,QAAA,IAC1B,gBAA4B,QAAA,IAChC,gBAAwB,QAAA,IACzC,oBAA2C,QAAA,IACzB,iBAAyB,QAAA,IAC9C,kBAAsD,QAAA,IACzD,gBAAmD,QAAA,IAClD,gBC2EM,QAAA,ID1ER,kBC0Pa,QAAA,IDzPb,uBAAkD,QAAA,IACzC,sBAA6C,QAAA,IAC/C,sBAAyD,QAAA,IAC1D,wBAAwD,QAAA,IACxD,uBAAwD,QAAA,IACtD,yBAA0D,QAAA,IAC3D,gBCrCa,QAAA,IDqC4C,kBAAA,mBAEhE,QAAA,IAAyB,kBACvB,QAAA,IACV,wBC2aiB,QAAA,ID3a+C,iBAAA,iBAAA,qBCorB1C,QAAA,IDlrBoD,kBAEjE,QAAA,IACT,sBAAsC,QAAA,IAA8B,kBACjC,QAAA,IAA2B,gBAChD,QAAA,ICwXM,gBAAA,2BDtXZ,QAAA,IAAyB,0BAEjC,QAAA,IAAA,0BC+agC,QAAA,ID/agD,kBACrC,QAAA,IAAmC,yBACnC,QAAA,IAAmC,yBACpC,QAAA,IAAoB,oBACpB,QAAA,IAAkC,gBAAA,QAAA,IAClC,iBAAkC,QAAA,IACzD,gBAA+C,QAAA,IAC1D,mBAAkD,QAAA,IAC1C,wBACR,QAAA,IAAyB,wBACtB,QAAA,IC+NM,iBD/N+C,QAAA,IACvB,wBAAiC,QAAA,IACjC,yBAChC,QAAA,IC+IM,uBD9IC,QAAA,IAAyB,wBACxB,QAAA,IAAyB,wBAC3B,QAAA,IAAyB,wBAAgC,QAAA,IAC/B,2BACzB,QAAA,IAAyB,uBAAiC,QAAA,IACjC,sBAAiC,QAAA,IACvD,0BAA6D,QAAA,IAC1D,0BAAkD,QAAA,IACjC,eAA+B,QAAA,IAAA,sBCwlBvC,QAAA,IDvlB+C,uBCwDtD,QAAA,IDtDjB,oBCnCa,QAAA,IDmCoC,sBClDpC,QAAA,IDmDkD,wBAAA,iBAE1D,QAAA,IAAgC,kBAAsB,QAAA,IAC3B,oBAA+B,QAAA,IACtD,gBACP,QAAA,IC4dM,iBD3dL,QAAA,IAAyB,oBACvB,QAAA,IAAyB,8BACJ,QAAA,IAAyB,gBACxB,QAAA,IAA0B,gBACzC,QAAA,ICpDD,gBDoDgD,QAAA,IAC5C,eCuIM,QAAA,IDtIpB,qBAAkD,QAAA,IAClD,gCAAA,mBAED,QAAA,IAAyB,iBAAwB,QAAA,IACpC,oBAAgD,QAAA,IAClD,kBAClB,QAAA,IAAwB,mBAAkE,QAAA,IACjF,kBAAmD,QAAA,IAChD,sBAAsD,QAAA,IACxD,wBAAoD,QAAA,IAC1B,mBAA4B,QAAA,IAC7B,yBACrB,QAAA,IAAyB,kBAA+B,QAAA,IACtD,uBAA0D,QAAA,IACtC,oBAA4B,QAAA,IAC/C,oBAA2D,QAAA,IAClE,uBAAA,qBAC8B,QAAA,IAAgC,0BC1EtD,QAAA,ID2EgD,2BACA,QAAA,IAAA,wBAErD,QAAA,IAAyB,eAA8B,QAAA,IAClD,gBAAA,iBAA4D,QAAA,IAC3D,oBAA6D,QAAA,IAChE,uBAA0D,QAAA,IAC1C,yBACvB,QAAA,IACT,qBC6CqB,QAAA,ID7CqC,mBCmDxC,QAAA,IDlDgD,oBAC1B,QAAA,IAAgC,2BACvB,QAAA,IAA2B,sBAC/B,QAAA,IAAuB,yBC+N5C,QAAA,ID9NwC,mBCqc9C,QAAA,IDpcgD,kBACtB,QAAA,ICuRZ,yBDtRO,QAAA,IAA+B,kBACrD,QAAA,IAAyB,mBAC/B,QAAA,IAAyB,iBAA4B,QAAA,IAC7B,oBAA2B,QAAA,IAC7C,sBAA2D,QAAA,IACzC,wBACxB,QAAA,IAAyB,mBAC3B,QAAA,IAAyB,sBAAA,oBACgC,QAAA,IAAA,kBChD9C,QAAA,IDiDkD,kBAC7B,QAAA,IAAiC,uBACtC,QAAA,IAA4B,gBAAA,eAEpD,QAAA,IAAyB,iBAA6B,QAAA,IAC/B,oBACzB,QAAA,IAAyB,gBAA2B,QAAA,IAC/C,uBAAyD,QAAA,IAExE,wBC+Ya,QAAA,ID9YJ,uBAAmD,QAAA,IAChD,qBAAsD,QAAA,IACnD,uBAA2C,QAAA,IAC3C,6BAAyD,QAAA,IAC/B,8BAC1B,QAAA,ICkLM,2BDjLiB,QAAA,IAA8B,6BAC5B,QAAA,IAAgC,iBACnD,QAAA,IAAyB,kBAAsC,QAAA,IAC9D,iBCjIM,QAAA,IDiI0D,kBAC5D,QAAA,ICjID,qBDiIuD,QAAA,IAClC,sBACrC,QAAA,IAAyB,iBAAA,iBC8lBlB,QAAA,ID7lB8C,iBAAA,gBAEpD,QAAA,IAAyB,iBAA2B,QAAA,IACxB,iBAA8B,QAAA,IACtD,eAAA,oBACE,QAAA,IACP,gBAAA,mBACO,QAAA,IACR,qBAAkD,QAAA,IACjD,oBAAA,gBACyB,QAAA,IAA0B,kBAE5D,QAAA,IAAY,gBAAA,mBAAA,mBAEwB,QAAA,IAA4B,mBAC1B,QAAA,IAA8B,mBAEpE,QAAA,IAAY,yBAAsD,QAAA,IAC/B,qBAA2B,QAAA,IAE9D,iBACA,QAAA,IAAQ,iBAAkD,QAAA,IAC/C,iBCsOM,QAAA,IDtO+C,qBCqO/C,QAAA,IDpO+C,4BACY,QAAA,IAAA,8BACR,QAAA,IAClC,uBACzB,QAAA,ICyOM,iBDxON,QAAA,IAAgC,sBAC5B,QAAA,IAAgC,oBAAuB,QAAA,IAChD,sBAA8D,QAAA,IAAA,uBCiI/C,QAAA,IDhImD,mBACvC,QAAA,IAAyB,gBAAA,oBACZ,QAAA,IACrB,qBAAA,qBC7ErB,QAAA,ID8EgD,oBAAA,mBACI,QAAA,IAC9B,oBAAgC,QAAA,IACpC,oBAA4B,QAAA,IAEhE,uBAAA,gBAA0D,QAAA,IAE1D,iBAAA,iBAAoE,QAAA,IACzD,qBAAA,sBACuD,QAAA,IACtD,qBAAsD,QAAA,IACtD,sBAAsD,QAAA,IACnD,gBAAA,iBAC2C,QAAA,IACjD,mBACA,QAAA,ICoFM,oBDnFF,QAAA,IACb,qBAAA,iBAAsE,QAAA,IAChC,uBAA8B,QAAA,IAC7B,oBAA+B,QAAA,IAEtE,0BAA0D,QAAA,IAC/C,wBAAqD,QAAA,IACpD,mBAAsD,QAAA,IACzD,uBACW,QAAA,ICrED,oBDsEJ,QAAA,IAAgC,kBACnC,QAAA,IAAyB,kBAA6B,QAAA,IAChD,mBAA4D,QAAA,IAAA,uBClEjD,QAAA,IDoElB,sBAAqD,QAAA,IAAA,sBC+YpC,QAAA,ID9Y4C,qBC0Z/C,QAAA,IDxZf,kBC9JM,QAAA,ID+JN,uBAAoD,QAAA,IACnD,gBC1CM,QAAA,ID0C+C,oBACjB,QAAA,IAAyB,uBAC1B,QAAA,IAAwB,6BACA,QAAA,IAAA,8BACF,QAAA,IACjC,2BACpB,QAAA,IAAyB,6BACP,QAAA,IAAyB,sBACrB,QAAA,IAA6B,uBAC1B,QAAA,IAAgC,oBACnD,QAAA,IAAgC,sBAA+B,QAAA,IAC9D,mBAAgE,QAAA,IAAA,kBACnC,QAAA,IAA6B,kBAC3D,QAAA,IAAgC,wBAAA,kBACiB,QAAA,IAAA,oBCpOjD,QAAA,IDqOmD,sBCpOtD,QAAA,IDqOgD,uBCxO9C,QAAA,IDyOkD,mBCnDrD,QAAA,IDoD+C,kBC6IhD,QAAA,ID5I8C,sBAAA,iBAE9C,QAAA,IAChB,sBCuMgB,QAAA,IDvM8C,oBAClB,QAAA,IAAsB,yBC0Q9C,QAAA,IDzQkD,mBAC9B,QAAA,IAAgC,mBACpC,QAAA,IAA4B,iBAC7B,QAAA,IAA2B,mBAChD,QAAA,IACL,sBAAmD,QAAA,IAAA,kBCgDxC,QAAA,ID/CkD,0BACJ,QAAA,IAAA,oBCqB3C,QAAA,IDpBqD,gBACxC,QAAA,IAA4B,0BAAA,qBAE9B,QAAA,IAA0B,2BAAA,0BAAA,uBAEU,QAAA,IACnC,0BAA2B,QAAA,IACnB,gBAAmC,QAAA,IAClE,qBAAsD,QAAA,IACjC,wBAAA,kBAEjC,QAAA,IAAa,oBAAuD,QAAA,IAAA,gBAEpE,QAAA,IAAA,uBACA,QAAA,IAAwC,uBAAgC,QAAA,IACtD,qBAA4D,QAAA,IAC7C,kBAAyB,QAAA,IAC7C,wBAAuD,QAAA,IAEpE,sBAAyC,QAAA,ICtJZ,4BDuJe,QAAA,IAAsB,kBC2FpD,QAAA,ID1F4C,sBAClB,QAAA,IAAgC,6BCmWnD,QAAA,IDlWmD,kBAC3B,QAAA,IAAuB,kBACjC,QAAA,IAA2B,+BCiOjC,QAAA,IDhO6C,gCACJ,QAAA,IAClD,6BAA8D,QAAA,IAC/C,+BACrB,QAAA,IC5LM,iBD4LkD,QAAA,IACjD,gBAA+D,QAAA,IAC1E,kBCuPa,QAAA,IDtPb,sBAAoD,QAAA,IAAA,oBACd,QAAA,IAAwC,sBAChE,QAAA,IAAyB,sBAAyC,QAAA,IACrE,sBAA+D,QAAA,IAAA,uBCjKvD,QAAA,IDkK2D,kBC+DzE,QAAA,ID7DP,wBAAkD,QAAA,IAChD,0BAAoD,QAAA,IACvB,oBAA+B,QAAA,IACnD,sBAA+C,QAAA,IACpD,wBAAwD,QAAA,IACjD,yBAAiD,QAAA,IAC/B,gCACxB,QAAA,ICiMM,wBDhMc,QAAA,IAA2B,mBAC9C,QAAA,IC0IM,+BAAA,uBDzIwD,QAAA,IACzC,6BAAA,qBACiC,QAAA,IACtD,gCAAA,wBC2KO,QAAA,ID1KqD,eAAA,gBCnF9C,QAAA,IDoF4D,eAC1E,QAAA,ICwPM,kBAAA,eAxYL,QAAA,IDiJ+C,eAAA,iBAEhE,QAAA,IAAgD,eAAA,eAAA,eAAA,eAE3B,QAAA,IC9MM,kBAAA,eAAA,iBDgN3B,QAAA,IAAiD,eAAA,eAEjD,QAAA,IAAgC,mBAAA,eACA,QAAA,IAAwB,gBAExD,QAAA,IAAgC,qBAAwB,QAAA,IAExD,0BAAwD,QAAA,IACjD,2BAEA,QAAA,IACA,2BAAiD,QAAA,IAExD,4BACO,QAAA,ICiNM,4BD/MN,QAAA,IAAyB,6BAEhC,QAAA,IAAc,qBAA0C,QAAA,IACvB,uBACpB,QAAA,IAAyB,0BAA8B,QAAA,IACzB,mBAAmC,QAAA,IAC3D,gBC0PM,QAAA,ID1PuD,uBACpC,QAAA,IAAoC,wBACnC,QAAA,IC0PZ,mBD1PiD,QAAA,IACrC,0BAAqC,QAAA,IAC7D,qBAA+D,QAAA,IACvE,kBAAuD,QAAA,IAAA,eAC5B,QAAA,IAAgC,qBAC7B,QAAA,ICyaZ,4BAFd,QAAA,IDta+C,kBC4ZlD,QAAA,ID3Z4C,yBC4ZrC,QAAA,ID3ZmD,2BCqa3C,QAAA,IDnalB,yBAAqD,QAAA,IAAA,2BACc,QAAA,IACjE,4BACH,QAAA,IAAyB,iBAC5B,QAAA,IAAyB,mBAAwB,QAAA,IAClB,mBAA8B,QAAA,IAChD,iBC/RM,QAAA,ID+RwD,oBCgVlE,QAAA,ID/U8C,iBACpB,QAAA,IAAkC,sBACzD,QAAA,IAAgC,kBAA6B,QAAA,IACxD,kBAAoD,QAAA,IAAA,gBAChC,QAAA,IAAoC,kBAAA,oBACE,QAAA,IACzE,iBAAmD,QAAA,IACjD,kBCmYM,QAAA,IDnY+C,mBC5W/C,QAAA,ID8WR,eAAgC,QAAA,IAAmB,cACvB,QAAA,IC5KZ,iBD6KT,QAAA,ICgND,kBD/MD,QAAA,IAAyB,qBAC7B,QAAA,IC4TM,0BD3TmB,QAAA,IAA2B,gCACJ,QAAA,IACzC,+BC3DC,QAAA,ID6DT,+BAAA,uBACqD,QAAA,IACnD,wBAAqD,QAAA,IAChC,sBAC1B,QAAA,ICiWM,wBDhWsB,QAAA,IAA0B,eAAA,wBACE,QAAA,IACxB,yBAA8B,QAAA,IACzB,yBAAmC,QAAA,IACtD,iBC/WM,QAAA,ID+W4D,2BAC1C,QAAA,IAAwC,qBAExF,QAAA,IAAA,kBAAgD,QAAA,ICzRZ,gBAAA,uBAAA,sBD2RG,QAAA,IAA+B,0BAAA,wBACI,QAAA,IAE1E,iBAAgC,QAAA,IAAwB,kBACvC,QAAA,IAAgC,kBAA2B,QAAA,IACpD,yBACf,QAAA,IAAyB,8BACf,QAAA,ICzLM,uBD0LZ,QAAA,ICoWM,qBDnWF,QAAA,IC8DD,gBD9D8C,QAAA,IAE9D,yBACA,QAAA,IAAuC,0BAA+B,QAAA,IAEtE,kBAAkB,QAAA,IAAgC,kBACzC,QAAA,IAAgC,oBAC/B,QAAA,IClGM,eDmGN,QAAA,IAAyB,oBAA2B,QAAA,IAC7C,iBAA2D,QAAA,IAAA,eAC/C,QAAA,ICyND,iBDxNb,QAAA,IAAyB,gBAAgC,QAAA,IAC3D,iBAAuD,QAAA,IAC5D,mBAAkD,QAAA,IAAA,0BACkB,QAAA,IAAA,iBAC1B,QAAA,IAA4B,wBCtNvD,QAAA,IDwNb,mBCzCa,QAAA,IDyCuC,sBAAA,eAE9B,QAAA,ICtLZ,eAAA,gBAtJF,QAAA,ID8UT,gBC1RM,QAAA,ID2RR,mBCsDM,QAAA,IDrDJ,sBAAmD,QAAA,IACpD,sBAAkD,QAAA,IACxB,oBAA0B,QAAA,IACxB,sBAA4B,QAAA,IAC9C,uBAA4D,QAAA,IAC5C,wBAA0B,QAAA,IACnB,6BAC9B,QAAA,ICyFM,wBAAA,wBAAA,0BDrFjB,QAAA,IAAiC,0BAAA,sBACyB,QAAA,IACtB,wBAAA,wBACkC,QAAA,IAAA,wBAAA,wBAEjC,QAAA,IAA6B,uBAC3B,QAAA,IAA+B,gBACvD,QAAA,ICxLM,mBDyLL,QAAA,IAAyB,oBAAiC,QAAA,IACrD,qBAAA,qBAAA,qBAAA,sBAAA,mBCrMC,QAAA,IDwMoD,0BAE1E,QAAA,IAA2C,cAAA,iBAAA,sBAE3B,QAAA,IAAyB,kBAAA,cAClB,QAAA,IACvB,sBCpMsB,QAAA,IDoMoD,eAClC,QAAA,ICjNZ,uBAAA,+BAAA,qBDoNhB,QAAA,IAAyB,yBACxB,QAAA,IACb,cACA,QAAA,IAAA,kBAAA,kBAEA,QAAA,IAAoB,uBAAA,gBACK,QAAA,IC/TD,yBAAA,kBDkUxB,QAAA,IAAkC,mBAC5B,QAAA,IACN,uBCnQgB,QAAA,IDmQ8C,kBACvB,QAAA,IAA+B,qBC1KzD,QAAA,ID2K2C,mBACjC,QAAA,IACvB,qBACA,QAAA,IAAe,4BAAyD,QAAA,IAChD,gBAAoD,QAAA,IAC7C,oBAAA,yBAErB,QAAA,IC0QM,eDzQR,QAAA,IACR,sBAAwC,QAAA,IAAgC,gBAExE,QAAA,IAAA,sBCXuB,QAAA,IDWqD,kBCzI3D,QAAA,ID0I+C,gBACxB,QAAA,IAAgC,uBACrC,QAAA,IAA2B,gBAC1C,QAAA,ICbD,sBDciB,QAAA,IAA4B,kBACnD,QAAA,ICuEM,yBDtEC,QAAA,IAAyB,mBACrC,QAAA,IAAgC,yBACvB,QAAA,IACjB,uBC5MkB,QAAA,ID6MX,mBC0Ma,QAAA,ID1MoC,qBCvapC,QAAA,IDyaZ,qBAAkD,QAAA,IAAA,sBACY,QAAA,IAC5D,wBAAoD,QAAA,IAC7B,iBAAyB,QAAA,IAC3C,qBAAyD,QAAA,IAChE,cCwPa,QAAA,IDxPqC,sBCpZtC,QAAA,IDsZV,uBAAoD,QAAA,IAAA,yBACc,QAAA,IACjE,sBAAqD,QAAA,IAAA,qBC/XzC,QAAA,IDgYqD,sBClYvD,QAAA,IDmYmD,kBAC7B,QAAA,IAAqB,yBCjY7C,QAAA,IDkYiD,sBCjYjD,QAAA,IDkYiD,qBC1bhD,QAAA,ID2bkD,mBAC7B,QAAA,IAAiC,eAC1D,QAAA,IC+KD,mBD9KF,QAAA,ICnVM,qBDoVY,QAAA,IAAuB,cACxC,QAAA,IAAyB,eAAA,kBAAA,kBACiC,QAAA,IACvD,oBAA2D,QAAA,IAAA,sBACN,QAAA,IAAA,0BACF,QAAA,IACtD,oBAAwD,QAAA,IAC5D,oBAAoD,QAAA,IAAA,mBCtIvC,QAAA,IDuIqD,kBACrC,QAAA,IAA+B,wBAChC,QAAA,IAA8B,uBCvcnD,QAAA,IDycV,oBCjba,QAAA,IDkbT,qBAAqD,QAAA,IAAA,2BACI,QAAA,IACrC,mBAAuB,QAAA,IAEtD,gBACA,QAAA,IAAO,uBAAiD,QAAA,IACnB,sBAA6B,QAAA,IAC7C,uBAAiD,QAAA,IACpD,qBAA4D,QAAA,IAClE,iBC9Va,QAAA,ID8VyC,gBACtB,QAAA,IAAsB,mBAC9B,QAAA,IAA4B,oBAAA,uBAEhD,QAAA,IAAyB,2BAC1B,QAAA,ICoCM,wBDnCF,QAAA,ICqCD,uBDpCoB,QAAA,IC5aZ,sBD6aP,QAAA,IAAyB,uBACjC,QAAA,IC/VM,yBDgWgB,QAAA,IAAyB,yBAClB,QAAA,IAAgC,kBACjC,QAAA,IAA+B,sBAChD,QAAA,IC6ED,6BD5EiB,QAAA,IAA8B,uBCgLrD,QAAA,ID9KP,oBAAkD,QAAA,IAC/C,kBCnHa,QAAA,IDmHwC,qBAEhE,QAAA,IAAwC,sBAAgC,QAAA,IACrD,eAAA,iBAA6D,QAAA,IACvC,mBAAiC,QAAA,IAC3D,iBAAyD,QAAA,IAAA,kBC0KpD,QAAA,IDzKkD,kBAC9B,QAAA,IAAgC,wBAAA,cACI,QAAA,IAAA,yBACA,QAAA,IAClE,oBAAoD,QAAA,IAChD,wBAAwD,QAAA,IAAA,qBAAA,wBAAA,mBAEE,QAAA,IAC5D,qBAAA,kCACkD,QAAA,IACjD,qBAAA,wBAC0B,QAAA,IAA+B,qBAAA,2BAEd,QAAA,IACpB,qBAAA,yBACwB,QAAA,IAClD,yBAAoD,QAAA,IAC3B,oBAC7B,QAAA,IACN,wBC0LsB,QAAA,ID1LoD,0BChHnD,QAAA,IDiHqD,uBCpH1D,QAAA,IDqHgD,yBClW5C,QAAA,IDmWoD,kBAE1E,QAAA,IAAkB,0BACuB,QAAA,IAAiC,iBAC7D,QAAA,IACb,yBAAiC,QAAA,IC9gBD,uBDghBhC,QAAA,IAAA,uBAAA,2BAEA,QAAA,IAAA,uBAAA,0BAEA,QAAA,IAAA,uBAAA,yBAC0C,QAAA,IAAkC,qBACvC,QAAA,IAA6B,uBAAA,uBACQ,QAAA,IACxD,wBAAA,uBACsB,QAAA,IAAgC,2BACvB,QAAA,IAA2B,yBACd,QAAA,IAAA,wBCjetC,QAAA,IDmef,0BAAmD,QAAA,IAC3C,wBAA2D,QAAA,IAC7D,qBAAyD,QAAA,IAAA,sBAExE,QAAA,IAAmB,4BAA6D,QAAA,IAEhF,cAAA,QAAA,IAA2C,qBAAmC,QAAA,IAC/D,uBACE,QAAA,IAAgC,yBACpC,QAAA,ICrQM,gCDuQnB,QAAA,IAAe,sBAAyD,QAAA,IACzD,uBACC,QAAA,IAAyB,kBAAiC,QAAA,IACvD,kBC1Ra,QAAA,ID0RgD,mBACtC,QAAA,IAAkC,iBAC5D,QAAA,IC3RM,6BD4RG,QAAA,IC/RD,sBAAA,cADF,QAAA,IDiSoD,kBACpC,QAAA,IAA8B,iBAC7B,QAAA,IC/FZ,kBD+F2C,QAAA,IACzB,2BACd,QAAA,IAAuB,4BACT,QAAA,IAAuB,4BCmE/C,QAAA,IDlEmD,4BACI,QAAA,IAAA,oBAC3B,QAAA,ICrKZ,mBDsKvB,QAAA,IC5UM,qBD6UL,QAAA,IAAyB,iBAAgC,QAAA,IACrC,eAA2B,QAAA,IAC7C,sBAA6C,QAAA,IAC1B,wBAC3B,QAAA,IAAgC,iBAAmB,QAAA,IACvC,iBCpQM,QAAA,IDqQrB,qBACQ,QAAA,IAAgC,qBACpC,QAAA,ICjdM,wBDkdkB,QAAA,IAA0B,gBACzB,QAAA,IC/nBZ,2BDgoBG,QAAA,IC3hBD,oBD2hBuD,QAAA,IACnC,gBAAqC,QAAA,IAAA,wBACA,QAAA,IAAA,eACvD,QAAA,ICjiBD,wBDkiBW,QAAA,IAA6B,oBAC9B,QAAA,IAA4B,kBACnD,QAAA,IC5NM,wBD6Ne,QAAA,IAA0B,0BACJ,QAAA,IAAA,uBACc,QAAA,IAAA,yBACI,QAAA,IACjE,wBAAmD,QAAA,IAC1B,2BACrB,QAAA,IAAgC,mBAChC,QAAA,IAAyB,qBAA8B,QAAA,IAC3B,uBACjC,QAAA,IAAyB,mBAAyB,QAAA,IACvC,kBAA6D,QAAA,IACpE,sBAAsD,QAAA,IACnD,mBAA2C,QAAA,IAC1C,kBC1XM,QAAA,ID2Xf,4BAAiD,QAAA,IACxC,0BAA0D,QAAA,IACrC,6BACF,QAAA,IAA2B,iBAC9C,QAAA,IAAyB,6BACvB,QAAA,IAAgC,gCACV,QAAA,IAAgC,mBACvD,QAAA,IC3CM,uCAzDD,QAAA,IDqGoD,+CAAA,4BAEV,QAAA,IAC1B,gBAAA,oBAAA,2BAE3B,QAAA,IC/LM,iBDgMP,QAAA,IClXM,mBDmXF,QAAA,IAAyB,yBAAA,mBACyB,QAAA,IACtD,sBAAoD,QAAA,IAAA,kBC4BpC,QAAA,ID3BwD,yBACvC,QAAA,IAAmC,oBACzD,QAAA,ICxKM,0BDyKO,QAAA,IAA0B,2BACd,QAAA,IAAsC,sBAAA,QAAA,IACnC,uBACtC,QAAA,IAAyB,iBAA4B,QAAA,IAAA,qBACR,QAAA,IAAgD,8BAAA,gCAExG,QAAA,IAAgE,cAAA,wBAEhE,QAAA,IAAA,uBACQ,QAAA,ICvfM,yBDwfoB,QAAA,IAA0B,2BC/XpC,QAAA,IDiYb,kBACX,QAAA,IAAiB,wBAA2D,QAAA,IAC9D,0BACJ,QAAA,IAAyB,wBAAA,iBCkCZ,QAAA,IDjCqD,0BAAA,mBAE1D,QAAA,IAAyB,uBAAmC,QAAA,IAClC,yBAAoC,QAAA,IACzC,kBAA+B,QAAA,IACvD,oBAAyD,QAAA,IAC/D,2BAAA,mBCnDU,QAAA,IDoDiD,6BAAA,qBAE5C,QAAA,ICzYM,iBD0YjB,QAAA,IACb,0BAA0E,QAAA,IAAA,oBCrXrD,QAAA,IDsXmD,yBAAA,4BAAA,uBAEQ,QAAA,IAC7C,yBAAA,sCACuC,QAAA,IACjD,yBAAA,4BAET,QAAA,ICrtBM,yBAAA,+BACE,QAAA,IDstBsD,yBAAA,6BAEpC,QAAA,ICJZ,kBDKb,QAAA,ICHD,gBAAA,mBAAA,eDIkD,QAAA,IAElE,mBAAoC,QAAA,IC5WZ,2BD6WH,QAAA,IACrB,2BAAoE,QAAA,IAC3D,0BAAmD,QAAA,IAC1C,2BAAA,wBACgD,QAAA,IAAA,6BAAA,0BAG9C,QAAA,IAAyB,oBAAqC,QAAA,IACjE,gBACjB,QAAA,IAAA,gBAAuD,QAAA,IAA+C,gBACrF,QAAA,IACjB,mBAAoB,QAAA,IC3EM,mBD2EwD,QAAA,IAElF,qBAAuB,QAAA,IAAyB,uBAAwC,QAAA,IAExF,uBAAqB,QAAA,IAAgC,sBAC3C,QAAA,IC3KM,kBD4KL,QAAA,IACJ,SACP,SAAA,SAAiC,MAAA,IAAyB,OAAA,IAC/C,QAAA,EAAyB,OAAA,KAA4B,SAAA,OAAA,KAAA,cACgB,OAAA,EAAA,0BAAA,yBAE9D,SAAA,OCKM,MAAA,KDLsD,OAAA,KAC3D,OAAA,EACnB,SAAA,QAAgB,KAAA,KAAyB,KAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,KAAiC,OAAA,EAE1E,QAAA,EAAA,KAAA,UAAA,KAA2C,GAAO,UAAA,WACtC,YAAA,KCxsBa,YAAA,ODysBQ,GAAA,UAAA,WACzB,YAAA,IAAkD,YAAA,IACzB,GAAA,UAAA,WACtB,YAAA,IC/OM,GAAO,UAAA,WDgPY,YAAA,IAA4B,cAAA,MAC1B,GAAO,UAAA,WAC9B,YAAA,IC7KM,cAAA,ID6KmD,GACzD,UAAA,UAAyD,YAAA,IAC1D,aAAgC,UAAA,KAAwB,eACnC,YAAA,KFnvBjC,eAAU,MAAA,QAEV,cACA,QAAA,aAEA,OAAA,IAAgB,MAAA,IACV,sBpBfN,QAAA,aAAC,OAAA,OChBH,MAAA,ODiBG,eAAA,YoB4BQ,qBAED,QAAA,aAEN,OAAA,OIxCF,MAAA,OAGE,eAAA,YAKS,qBAzBQ,QAAA,aAGT,OAAA,KAON,MAAA,KAwBF,eAAA,YA3BA,sBAKE,QAAA,aA2BW,OAAA,OAhCb,MAAA,OAJ0C,eAAA,YAI1C,sBAKE,QAAA,aAoCW,OAAA,KAzCb,MAAA,KAJQ,eAAA,YAkDR,uBA9CA,QAAA,GAJQ,iBAAA,iCA2DX,gBAAA,KAOC,uBACD,QAAA,G9D1DU,iBAAA,iC+Dec,gBAAA,KACtB,oBAGY,QAAA,GAAA,iBAAA,8BAEO,gBAAA,KAGZ,sBAEK,QAAA,GAlBb,iBAAA,gCAoBoB,gBAAA,KAGZ,oBAEK,QAAA,GACS,iBAAA,8BAEnB,gBAAA,KAIC,mBAhCJ,QAAA,GAiC4B,iBAAA,6BAEzB,gBAAA,KAIC,qBAvCJ,QAAA,GACA,iBAAA,+BAyCG,gBAAA,KAhDH,oBAAkB,QAAA,GAAA,iBAAA,8BAsDf,gBAAA,KAIC,qBA1DJ,QAAA,GAAkB,iBAAA,+BA4Df,gBAAA,KAIC,oBAhEJ,QAAA,GAAkB,iBAAA,8BAkEf,gBAAA,KAIC;;;;;;;;;;;;;;ACtEH,eAAA,iBAGD,mBAAA,WAAA,WAAA,WACD,eAEC,QAAA,aACA,SAAA,SACA,MAAA,KACA,OAAA,KACA,eAAA,IACA,SAAA,OACD,kBAEC,SAAA,SACA,KAAA,EACA,IAAA,EACA,OAAA,KACA,OAAA,EACA,QAAA,EACA,WAAA,KACA,YAAA,OACA,UAAA,EACA,WAAA,KACA,QAAA,EACA,mBAAA,QAAA,GACA,WAAA,QAAA,GACD,oCAEC,QAAA,EACD,qBAEC,QAAA,aACA,UAAA,QACD,aAEC,QAAA,ajELF,KEhBE,iBAAA,QACA,eAAA,KAKD,0BFUD,KEZI,eAAA,GAKJ,YACE,QAAA,QAAA,EACA,UAAA,KACA,eAAA,MACA,WAAA,OACA,SAAA,SACA,MAAA,QAmCD,yBAzCD,YASI,QAAA,SAAA,GATJ,kBAAA,mBAcI,QAAA,GACA,QAAA,MACA,SAAA,SACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBACA,MAAA,wBACA,OAAA,IACA,iBAAA,QArBJ,mBAyBI,QAAA,KAMD,yBA/BH,mBA4BM,QAAA,MACA,KAAA,GA7BN,kBAkCI,QAAA,KAMD,yBAxCH,kBAqCM,QAAA,MACA,MAAA,GAMN,QACE,QAAA,EAAA,KADF,gBAII,WAAA,KACD,qBAOC,UAAA,SAKD,0BAND,qBAII,UAAA,QANN,8BAWI,UAAA,KAMD,0BAjBH,8BAcM,UAAA,KACA,eAAA,aAMN,kBACE,OAAA,IAAA,OAAA,QACA,MAAA,KACA,QAAA,MACA,iBAAA,QACA,cAAA,MAKD,0BAVD,kBAQI,QAAA,QAAA,QAIJ,oBACE,OAAA,EACA,QAAA,EACA,WAAA,KACA,UAAA,KACA,YAAA,OACA,MAAA,QANF,uBASI,QAAA,MACA,WAAA,OAcD,yBAxBH,uBAaM,QAAA,aACA,aAAA,QU9EF,0BVgEJ,uBAkBM,UAAA,QAlBN,4BAsBM,MAAA,KACD,cAAA,gBAQD,UAAA,SAKD,0BATH,cAAA,gBAOM,UAAA,MAMN,0BAGI,OAAA,KACD,kBAGC,MAAA,QACA,QAAA,OAAA,EACA,cAAA,EATJ,iBAaI,QAAA,QAAA,KAbJ,+BAkBM,YAAA,KAlBN,yBAuBI,SAAA,SAvBJ,0CA0BM,SAAA,SACA,IAAA,IACA,KAAA,KACA,MAAA,QACA,QAAA,IA9BN,gDAkCM,YAAA,MAlCN,2EAqCQ,YAAA,QACA,UAAA,QAtCR,kEAqCQ,YAAA,QACA,UAAA,QAtCR,sEAqCQ,YAAA,QACA,UAAA,QAtCR,uEAqCQ,YAAA,QACA,UAAA,QAtCR,6DAqCQ,YAAA,QACA,UAAA,QAED,SAOL,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,iBAAA,QACA,OAAA,IAAA,OAAA,QACA,QAAA,QAAA,KACA,cAAA,MACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,cAAA,WAAA,gBAAA,aAPF,wBAWI,MAAA,oBAXJ,2BAcM,cAAA,MACA,YAAA,IACA,UAAA,SACD,sCAUC,cAAA,IAFH,qCAMG,aAAA,IARN,eAaI,UAAA,QACA,YAAA,IACA,YAAA,IACA,cAAA,KAhBJ,4BAoBI,OAAA,IAAA,MAAA,QApBJ,qBAwBI,MAAA,QACA,OAAA,IAAA,MAAA,QAzBJ,oBA6BI,OAAA,IAAA,MAAA,QACA,MAAA,QA9BJ,mBAkCI,WAAA,OACA,iBAAA,QAnCJ,uBAsCM,UAAA,MACA,MAAA,KAKD,0BA5CL,uBA0CQ,UAAA,MAOR,cACE,QAAA,MAAA,EAAA,EAAA,EACA,UAAA,QAkBD,yBApBD,cAKI,UAAA,MALJ,gBASI,cAAA,MATJ,qBAaI,QAAA,MACA,MAAA,QAKD,yBAnBH,qBAiBM,QAAA", "file": "ecc-ATM.min.css", "sourcesContent": ["$primary: #67AAAC;\r\n$green: #237D80;\r\n$light-green: #F5FDF8;\r\n$info: #C7F0FF;\r\n$warning: #FFF8D8;\r\n$light-orange: #FFEAC7;\r\n$orange: #FD9B1A;\r\n\r\n$theme-colors: (\r\n  \"green\": $green,\r\n  \"tertiary\": $light-green,\r\n  \"light-orange\": $light-orange,\r\n  \"orange\": $orange\r\n);\r\n\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 480px,\r\n  md: 768px,\r\n  lg: 1024px,\r\n  xl: 1200px,\r\n);\r\n\r\n// $container-max-widths: (\r\n//   sm: 420px,\r\n//   md: 720px,\r\n//   lg: 960px,\r\n//   xl: 1140px\r\n// );\r\n\r\n//btn\r\n\r\n.btn-grey {\r\n  background-color: #a0a0a0;\r\n  color: #fff;\r\n}\r\n\r\n//footer\r\n", "/*!\r\n * Bootstrap v4.1.1 (https://getbootstrap.com/)\r\n * Copyright 2011-2018 The Bootstrap Authors\r\n * Copyright 2011-2018 Twitter, Inc.\r\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\r\n */\r\n\r\n@import \"../../node_modules/bootstrap/scss/functions\";\r\n@import \"../../node_modules/bootstrap/scss/variables\";\r\n@import \"../../node_modules/bootstrap/scss/mixins\";\r\n@import \"../../node_modules/bootstrap/scss/root\";\r\n@import \"../../node_modules/bootstrap/scss/reboot\";\r\n@import \"../../node_modules/bootstrap/scss/type\";\r\n@import \"../../node_modules/bootstrap/scss/images\";\r\n@import \"../../node_modules/bootstrap/scss/code\";\r\n@import \"../../node_modules/bootstrap/scss/grid\";\r\n@import \"../../node_modules/bootstrap/scss/tables\";\r\n@import \"../../node_modules/bootstrap/scss/forms\";\r\n@import \"../../node_modules/bootstrap/scss/buttons\";\r\n// @import \"../../node_modules/bootstrap/scss/transitions\";\r\n// @import \"../../node_modules/bootstrap/scss/dropdown\";\r\n@import \"../../node_modules/bootstrap/scss/button-group\";\r\n@import \"../../node_modules/bootstrap/scss/input-group\";\r\n@import \"../../node_modules/bootstrap/scss/custom-forms\";\r\n// @import \"../../node_modules/bootstrap/scss/nav\";\r\n// @import \"../../node_modules/bootstrap/scss/navbar\";\r\n// @import \"../../node_modules/bootstrap/scss/card\";\r\n// @import \"../../node_modules/bootstrap/scss/breadcrumb\";\r\n// @import \"../../node_modules/bootstrap/scss/pagination\";\r\n// @import \"../../node_modules/bootstrap/scss/badge\";\r\n// @import \"../../node_modules/bootstrap/scss/jumbotron\";\r\n// @import \"../../node_modules/bootstrap/scss/alert\";\r\n// @import \"../../node_modules/bootstrap/scss/progress\";\r\n// @import \"../../node_modules/bootstrap/scss/media\";\r\n// @import \"../../node_modules/bootstrap/scss/list-group\";\r\n// @import \"../../node_modules/bootstrap/scss/close\";\r\n// @import \"../../node_modules/bootstrap/scss/modal\";\r\n// @import \"../../node_modules/bootstrap/scss/tooltip\";\r\n// @import \"../../node_modules/bootstrap/scss/popover\";\r\n// @import \"../../node_modules/bootstrap/scss/carousel\";\r\n@import \"../../node_modules/bootstrap/scss/utilities\";\r\n// @import \"../../node_modules/bootstrap/scss/print\";\r\n", "// Do not forget to update getting-started/theming.md!\n:root {\n  // Custom variable values only support SassScript inside `#{}`.\n  @each $color, $value in $colors {\n    --#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$color}: #{$value};\n  }\n\n  @each $bp, $value in $grid-breakpoints {\n    --breakpoint-#{$bp}: #{$value};\n  }\n\n  // Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --font-family-sans-serif: #{inspect($font-family-sans-serif)};\n  --font-family-monospace: #{inspect($font-family-monospace)};\n}\n", "// stylelint-disable at-rule-no-vendor-prefix, declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// 1. Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n// 2. Change the default font family in all browsers.\n// 3. Correct the line height in all browsers.\n// 4. Prevent adjustments of font size after orientation changes in IE on Windows Phone and in iOS.\n// 5. Change the default tap highlight to be completely transparent in iOS.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box; // 1\n}\n\nhtml {\n  font-family: sans-serif; // 2\n  line-height: 1.15; // 3\n  -webkit-text-size-adjust: 100%; // 4\n  -webkit-tap-highlight-color: rgba($black, 0); // 5\n}\n\n// Shim for \"new\" HTML5 structural elements to display correctly (IE10, older browsers)\n// TODO: remove in v5\n// stylelint-disable-next-line selector-list-comma-newline-after\narticle, aside, figcaption, figure, footer, header, hgroup, main, nav, section {\n  display: block;\n}\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Set an explicit initial text-align value so that we can later use\n//    the `inherit` value on things like `<th>` elements.\n\nbody {\n  margin: 0; // 1\n  font-family: $font-family-base;\n  @include font-size($font-size-base);\n  font-weight: $font-weight-base;\n  line-height: $line-height-base;\n  color: $body-color;\n  text-align: left; // 3\n  background-color: $body-bg; // 2\n}\n\n// Future-proof rule: in browsers that support :focus-visible, suppress the focus outline\n// on elements that programmatically receive focus but wouldn't normally show a visible\n// focus outline. In general, this would mean that the outline is only applied if the\n// interaction that led to the element receiving programmatic focus was a keyboard interaction,\n// or the browser has somehow determined that the user is primarily a keyboard user and/or\n// wants focus outlines to always be presented.\n//\n// See https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible\n// and https://developer.paciellogroup.com/blog/2018/03/focus-visible-and-backwards-compatibility/\n[tabindex=\"-1\"]:focus:not(:focus-visible) {\n  outline: 0 !important;\n}\n\n\n// Content grouping\n//\n// 1. Add the correct box sizing in Firefox.\n// 2. Show the overflow in Edge and IE.\n\nhr {\n  box-sizing: content-box; // 1\n  height: 0; // 1\n  overflow: visible; // 2\n}\n\n\n//\n// Typography\n//\n\n// Remove top margins from headings\n//\n// By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n// margin for easier control within type scales as it avoids margin collapsing.\n// stylelint-disable-next-line selector-list-comma-newline-after\nh1, h2, h3, h4, h5, h6 {\n  margin-top: 0;\n  margin-bottom: $headings-margin-bottom;\n}\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n// Abbreviations\n//\n// 1. Duplicate behavior to the data-* attribute for our tooltip plugin\n// 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n// 3. Add explicit cursor to indicate changed behavior.\n// 4. Remove the bottom border in Firefox 39-.\n// 5. Prevent the text-decoration to be skipped.\n\nabbr[title],\nabbr[data-original-title] { // 1\n  text-decoration: underline; // 2\n  text-decoration: underline dotted; // 2\n  cursor: help; // 3\n  border-bottom: 0; // 4\n  text-decoration-skip-ink: none; // 5\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // Undo browser default\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nstrong {\n  font-weight: $font-weight-bolder; // Add the correct font weight in Chrome, Edge, and Safari\n}\n\nsmall {\n  @include font-size(80%); // Add the correct font size in all browsers\n}\n\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n//\n\nsub,\nsup {\n  position: relative;\n  @include font-size(75%);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n//\n// Links\n//\n\na {\n  color: $link-color;\n  text-decoration: $link-decoration;\n  background-color: transparent; // Remove the gray background on active links in IE 10.\n\n  @include hover() {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n  color: inherit;\n  text-decoration: none;\n\n  @include hover() {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n//\n// Code\n//\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-monospace;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\npre {\n  // Remove browser default top margin\n  margin-top: 0;\n  // Reset browser default of `1em` to use `rem`s\n  margin-bottom: 1rem;\n  // Don't allow content to break outside\n  overflow: auto;\n  // Disable auto-hiding scrollbar in IE & legacy Edge to avoid overlap,\n  // making it impossible to interact with the content\n  -ms-overflow-style: scrollbar;\n}\n\n\n//\n// Figures\n//\n\nfigure {\n  // Apply a consistent margin strategy (matches our type styles).\n  margin: 0 0 1rem;\n}\n\n\n//\n// Images and content\n//\n\nimg {\n  vertical-align: middle;\n  border-style: none; // Remove the border on images inside links in IE 10-.\n}\n\nsvg {\n  // Workaround for the SVG overflow bug in IE10/11 is still required.\n  // See https://github.com/twbs/bootstrap/issues/26878\n  overflow: hidden;\n  vertical-align: middle;\n}\n\n\n//\n// Tables\n//\n\ntable {\n  border-collapse: collapse; // Prevent double borders\n}\n\ncaption {\n  padding-top: $table-cell-padding;\n  padding-bottom: $table-cell-padding;\n  color: $table-caption-color;\n  text-align: left;\n  caption-side: bottom;\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\n\n//\n// Forms\n//\n\nlabel {\n  // Allow labels to use `margin` for spacing.\n  display: inline-block;\n  margin-bottom: $label-margin-bottom;\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n//\n// Details at https://github.com/twbs/bootstrap/issues/24093\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Work around a Firefox/IE bug where the transparent `button` background\n// results in a loss of the default `button` focus styles.\n//\n// Credit: https://github.com/suitcss/base/\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // Remove the margin in Firefox and Safari\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\nbutton,\ninput {\n  overflow: visible; // Show the overflow in Edge\n}\n\nbutton,\nselect {\n  text-transform: none; // Remove the inheritance of text transform in Firefox\n}\n\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n// Remove the inheritance of word-wrap in Safari.\n//\n// Details at https://github.com/twbs/bootstrap/issues/24990\nselect {\n  word-wrap: normal;\n}\n\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n}\n\n// Opinionated: add \"hand\" cursor to non-disabled button elements.\n@if $enable-pointer-cursor-for-buttons {\n  button,\n  [type=\"button\"],\n  [type=\"reset\"],\n  [type=\"submit\"] {\n    &:not(:disabled) {\n      cursor: pointer;\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  box-sizing: border-box; // 1. Add the correct box sizing in IE 10-\n  padding: 0; // 2. Remove the padding in IE 10-\n}\n\n\ntextarea {\n  overflow: auto; // Remove the default vertical scrollbar in IE.\n  // Textareas should really only resize vertically so they don't break their (horizontal) containers.\n  resize: vertical;\n}\n\nfieldset {\n  // Browsers set a default `min-width: min-content;` on fieldsets,\n  // unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n  // So we reset that to ensure fieldsets behave more like a standard block element.\n  // See https://github.com/twbs/bootstrap/issues/12359\n  // and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n  min-width: 0;\n  // Reset the default outline behavior of fieldsets so they don't affect page layout.\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\n// 1. Correct the text wrapping in Edge and IE.\n// 2. Correct the color inheritance from `fieldset` elements in IE.\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%; // 1\n  padding: 0;\n  margin-bottom: .5rem;\n  @include font-size(1.5rem);\n  line-height: inherit;\n  color: inherit; // 2\n  white-space: normal; // 1\n}\n\nprogress {\n  vertical-align: baseline; // Add the correct vertical alignment in Chrome, Firefox, and Opera.\n}\n\n// Correct the cursor style of increment and decrement buttons in Chrome.\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=\"search\"] {\n  // This overrides the extra rounded corners on search inputs in iOS so that our\n  // `.form-control` class can properly style them. Note that this cannot simply\n  // be added to `.form-control` as it's not specific enough. For details, see\n  // https://github.com/twbs/bootstrap/issues/11586.\n  outline-offset: -2px; // 2. Correct the outline style in Safari.\n  -webkit-appearance: none;\n}\n\n//\n// Remove the inner padding in Chrome and Safari on macOS.\n//\n\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n//\n// 1. Correct the inability to style clickable types in iOS and Safari.\n// 2. Change font properties to `inherit` in Safari.\n//\n\n::-webkit-file-upload-button {\n  font: inherit; // 2\n  -webkit-appearance: button; // 1\n}\n\n//\n// Correct element displays\n//\n\noutput {\n  display: inline-block;\n}\n\nsummary {\n  display: list-item; // Add the correct display in all browsers\n  cursor: pointer;\n}\n\ntemplate {\n  display: none; // Add the correct display in IE\n}\n\n// Always hide an element with the `hidden` HTML attribute (from PureCSS).\n// Needed for proper display in IE 10-.\n[hidden] {\n  display: none !important;\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge(\n  (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n  ),\n  $grays\n);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge(\n  (\n    \"blue\":       $blue,\n    \"indigo\":     $indigo,\n    \"purple\":     $purple,\n    \"pink\":       $pink,\n    \"red\":        $red,\n    \"orange\":     $orange,\n    \"yellow\":     $yellow,\n    \"green\":      $green,\n    \"teal\":       $teal,\n    \"cyan\":       $cyan,\n    \"white\":      $white,\n    \"gray\":       $gray-600,\n    \"gray-dark\":  $gray-800\n  ),\n  $colors\n);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge(\n  (\n    \"primary\":    $primary,\n    \"secondary\":  $secondary,\n    \"success\":    $success,\n    \"info\":       $info,\n    \"warning\":    $warning,\n    \"danger\":     $danger,\n    \"light\":      $light,\n    \"dark\":       $dark\n  ),\n  $theme-colors\n);\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold:  150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark:             $gray-900 !default;\n$yiq-text-light:            $white !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              false !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n  ),\n  $spacers\n);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge(\n  (\n    25: 25%,\n    50: 50%,\n    75: 75%,\n    100: 100%,\n    auto: auto\n  ),\n  $sizes\n);\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              theme-color(\"primary\") !default;\n$link-decoration:                         none !default;\n$link-hover-color:                        darken($link-color, 15%) !default;\n$link-hover-decoration:                   underline !default;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15% !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           30px !default;\n$grid-row-columns:            6 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$rounded-pill:                50rem !default;\n\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n$embed-responsive-aspect-ratios: () !default;\n$embed-responsive-aspect-ratios: join(\n  (\n    (21 9),\n    (16 9),\n    (4 3),\n    (1 1),\n  ),\n  $embed-responsive-aspect-ratios\n);\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                $font-size-base * 1.25 !default;\n$font-size-sm:                $font-size-base * .875 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-small-font-size:  $small-font-size !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    null !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-color:           $table-color !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n$table-th-font-weight:        null !default;\n\n$table-dark-color:            $white !default;\n$table-dark-bg:               $gray-800 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-color:      $table-dark-color !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\n\n$table-striped-order:         odd !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-level:              -9 !default;\n$table-border-level:          -6 !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$label-margin-bottom:                   .5rem !default;\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height-sm * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height-lg * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-grid-gutter-width:                10px !default;\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n$custom-control-cursor:                 null !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $input-bg !default;\n\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   $input-box-shadow !default;\n$custom-control-indicator-border-color: $gray-500 !default;\n$custom-control-indicator-border-width: $input-border-width !default;\n\n$custom-control-label-color:            null !default;\n\n$custom-control-indicator-disabled-bg:          $input-disabled-bg !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   null !default;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\n\n$custom-control-indicator-focus-box-shadow:     $input-focus-box-shadow !default;\n$custom-control-indicator-focus-border-color:   $input-focus-border-color !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    null !default;\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'><path stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/></svg>\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow:   null !default;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'><circle r='3' fill='#{$custom-control-indicator-checked-color}'/></svg>\") !default;\n\n$custom-switch-width:                           $custom-control-indicator-size * 1.75 !default;\n$custom-switch-indicator-border-radius:         $custom-control-indicator-size / 2 !default;\n$custom-switch-indicator-size:                  subtract($custom-control-indicator-size, $custom-control-indicator-border-width * 4) !default;\n\n$custom-select-padding-y:           $input-padding-y !default;\n$custom-select-padding-x:           $input-padding-x !default;\n$custom-select-font-family:         $input-font-family !default;\n$custom-select-font-size:           $input-font-size !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight:         $input-font-weight !default;\n$custom-select-line-height:         $input-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $input-bg !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-background:          escape-svg($custom-select-indicator) no-repeat right $custom-select-padding-x center / $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: add(1em * .75, (2 * $custom-select-padding-y * .75) + $custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\n\n$custom-select-border-width:        $input-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n$custom-select-box-shadow:          inset 0 1px 2px rgba($black, .075) !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-width:         $input-focus-width !default;\n$custom-select-focus-box-shadow:    0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\n\n$custom-select-padding-y-sm:        $input-padding-y-sm !default;\n$custom-select-padding-x-sm:        $input-padding-x-sm !default;\n$custom-select-font-size-sm:        $input-font-size-sm !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-padding-y-lg:        $input-padding-y-lg !default;\n$custom-select-padding-x-lg:        $input-padding-x-lg !default;\n$custom-select-font-size-lg:        $input-font-size-lg !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-range-track-width:          100% !default;\n$custom-range-track-height:         .5rem !default;\n$custom-range-track-cursor:         pointer !default;\n$custom-range-track-bg:             $gray-300 !default;\n$custom-range-track-border-radius:  1rem !default;\n$custom-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-range-thumb-width:                   1rem !default;\n$custom-range-thumb-height:                  $custom-range-thumb-width !default;\n$custom-range-thumb-bg:                      $component-active-bg !default;\n$custom-range-thumb-border:                  0 !default;\n$custom-range-thumb-border-radius:           1rem !default;\n$custom-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$custom-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in IE/Edge\n$custom-range-thumb-active-bg:               lighten($component-active-bg, 35%) !default;\n$custom-range-thumb-disabled-bg:             $gray-500 !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-height-inner:          $input-height-inner !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $input-focus-box-shadow !default;\n$custom-file-disabled-bg:           $input-disabled-bg !default;\n\n$custom-file-padding-y:             $input-padding-y !default;\n$custom-file-padding-x:             $input-padding-x !default;\n$custom-file-line-height:           $input-line-height !default;\n$custom-file-font-family:           $input-font-family !default;\n$custom-file-font-weight:           $input-font-weight !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $input-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}' viewBox='0 0 12 12'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n\n$form-validation-states: () !default;\n$form-validation-states: map-merge(\n  (\n    \"valid\": (\n      \"color\": $form-feedback-valid-color,\n      \"icon\": $form-feedback-icon-valid\n    ),\n    \"invalid\": (\n      \"color\": $form-feedback-invalid-color,\n      \"icon\": $form-feedback-icon-invalid\n    ),\n  ),\n  $form-validation-states\n);\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-divider-color:                 $gray-200 !default;\n$nav-divider-margin-y:              $spacer / 2 !default;\n\n\n// Navbar\n\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  $spacer !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .5) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-divider-margin-y:         $nav-divider-margin-y !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1.5rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\n\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-color:                   null !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 .25rem !default;\n$tooltip-padding-x:                 .5rem !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Toasts\n\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .25rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               .25rem !default;\n$toast-box-shadow:                  0 .25rem .75rem rgba($black, .1) !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-transition:                  $btn-transition !default;\n$badge-focus-width:                 $input-btn-focus-width !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               1rem !default;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up:    0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            1rem !default;\n$modal-header-padding-x:            1rem !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-xl:                          1140px !default;\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n\n// List group\n\n$list-group-color:                  null !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size:              null !default;\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n\n$breadcrumb-border-radius:          $border-radius !default;\n\n\n// Carousel\n\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n\n$carousel-control-icon-width:        20px !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n\n// Spinners\n\n$spinner-width:         2rem !default;\n$spinner-height:        $spinner-width !default;\n$spinner-border-width:  .25em !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Utilities\n\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\n$overflows: auto, hidden !default;\n$positions: static, relative, absolute, fixed, sticky !default;\n$user-selects: all, auto, none !default;\n\n\n// Printing\n\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "//bootstrap v4.1.3 設置\r\n//BS4參數調整\r\n$font-family-sans-serif: \"微軟正黑體\",\r\n-apple-system,\r\nBlinkMacSystemFont,\r\n\"Segoe UI\",\r\n\"Roboto\",\r\n\"Helvetica Neue\",\r\nArial,\r\nsans-serif,\r\n\"Apple Color Emoji\",\r\n\"Segoe UI Emoji\",\r\n\"Segoe UI Symbol\";\r\n$font-size-base: 1rem;\r\n@import \"eccATM/bs4-variables\";\r\n@import \"eccATM/bs4-custom\";\r\n\r\n//符號icon載入設定\r\n$fa-font-path: \"../fonts\";\r\n@import \"font-awesome/font-awesome\";\r\n\r\n//----客製化---//\r\n//元件模組\r\n@import \"eccATM/_typography.scss\";\r\n@import \"eccATM/_icon.scss\";\r\n@import \"eccATM/_jquery.jConveyorTicker.min.scss\"; //跑馬燈 jQuery 樣式\r\n\r\n//標籤修改\r\nbody {\r\n  background-color: #b5e2e3;\r\n  padding-bottom: 1rem;\r\n\r\n  @include media-breakpoint-up(lg) {\r\n    padding-bottom: 0;\r\n  }\r\n}\r\n\r\n//頁面標題\r\n.page-title {\r\n  padding: .525rem 0;\r\n  font-size: 3rem;\r\n  letter-spacing: 0.5rem;\r\n  text-align: center;\r\n  position: relative;\r\n  color: $green;\r\n\r\n  @include media-breakpoint-up(md) {\r\n    padding: 2.325rem 0;\r\n  }\r\n\r\n  &:before,\r\n  &:after {\r\n    content: \"\";\r\n    display: block;\r\n    position: absolute;\r\n    top: 50%;\r\n    transform: translateY(-25%);\r\n    width: calc((100% - 320px) / 2);\r\n    height: 3px;\r\n    background-color: $primary;\r\n  }\r\n\r\n  &:before {\r\n    display: none;\r\n\r\n    @include media-breakpoint-up(md) {\r\n      display: block;\r\n      left: 0;\r\n    }\r\n  }\r\n\r\n  &:after {\r\n    display: none;\r\n\r\n    @include media-breakpoint-up(md) {\r\n      display: block;\r\n      right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n//區塊(章節)\r\nsection {\r\n  padding: 0 1rem;\r\n\r\n  &+& {\r\n    margin-top: 1rem;\r\n  }\r\n}\r\n\r\n//累計點數排行榜\r\n.atm-honorRoll {\r\n\r\n  &-title {\r\n    font-size: 1.225rem;\r\n\r\n    @include media-breakpoint-up(lg) {\r\n      font-size: 1.8rem;\r\n    }\r\n  }\r\n\r\n  .icon-bigMedal {\r\n    font-size: 3rem;\r\n\r\n    @include media-breakpoint-up(lg) {\r\n      font-size: 5rem;\r\n      vertical-align: text-bottom;\r\n    }\r\n  }\r\n\r\n}\r\n\r\n.atm-honorRoll-bg {\r\n  border: 4px dashed darken($warning, 10%);\r\n  width: 100%;\r\n  padding: .5rem;\r\n  background-color: $warning;\r\n  border-radius: 0.5rem;\r\n\r\n  @include media-breakpoint-up(lg) {\r\n    padding: .725rem 1.5rem;\r\n  }\r\n}\r\n\r\n.atm-honorRoll-list {\r\n  margin: 0;\r\n  padding: 0;\r\n  list-style: none;\r\n  font-size: 1rem;\r\n  font-weight: bolder;\r\n  color: #8a4000;\r\n\r\n  li {\r\n    display: block;\r\n    text-align: center;\r\n\r\n    @include media-breakpoint-up(md) {\r\n      display: inline-block;\r\n      margin-right: 1.5rem;\r\n    }\r\n\r\n    @include media-breakpoint-up(lg) {\r\n      font-size: 1.5rem;\r\n    }\r\n\r\n    span {\r\n      color: #000;\r\n    }\r\n  }\r\n}\r\n\r\n.atm-info {\r\n\r\n  .fa,\r\n  .icon {\r\n    font-size: 1.125rem;\r\n\r\n    @include media-breakpoint-up(lg) {\r\n      font-size: 2rem;\r\n    }\r\n  }\r\n}\r\n\r\n//我要查詢區塊\r\n.searchArea {\r\n\r\n  .form-control {\r\n    height: auto;\r\n  }\r\n\r\n  &-title {\r\n    color: $gray-800;\r\n    padding: 0.25rem 0;\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .btn {\r\n    padding: 0.375rem 1rem;\r\n  }\r\n\r\n  input {\r\n    &.form-control {\r\n      text-indent: 2rem;\r\n    }\r\n  }\r\n\r\n  .input-group {\r\n    position: relative;\r\n\r\n    .placeholderIcon {\r\n      position: absolute;\r\n      top: 6px;\r\n      left: 12px;\r\n      color: $primary;\r\n      z-index: 100;\r\n    }\r\n\r\n    .placeholderIcon+input {\r\n      padding-top: 0.6rem;\r\n\r\n      &::placeholder {\r\n        text-indent: 2.25rem;\r\n        font-size: 1.35rem;\r\n        ;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n//跑馬燈區塊\r\n.marquee {\r\n  display: flex;\r\n  background-color: lighten($info, 4%);\r\n  border: 2px dashed lighten($primary, 6%);\r\n  padding: .325rem 1rem;\r\n  border-radius: 0.5rem;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n\r\n  //custom\r\n  .js-conveyor-1 {\r\n    width: calc(100% - 3.5rem);\r\n\r\n    li {\r\n      padding-right: 10rem;\r\n      line-height: 1.5;\r\n      font-size: 1.325rem;\r\n    }\r\n  }\r\n\r\n}\r\n\r\n//右側區塊\r\n.sideArea {\r\n\r\n  &-btnGroup {\r\n    .col-6:first-child {\r\n      padding-right: 8px;\r\n    }\r\n\r\n    .col-6:last-child {\r\n      padding-left: 8px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    font-size: 1.25rem;\r\n    font-weight: bold;\r\n    line-height: 1.8;\r\n    border-radius: 1rem;\r\n  }\r\n\r\n  .btn-light-orange {\r\n    border: 2px solid lighten($orange, 15%);\r\n  }\r\n\r\n  .btn-light {\r\n    color: $gray-700;\r\n    border: 2px solid $gray-500;\r\n  }\r\n\r\n  .btn-info {\r\n    border: 2px solid darken($info, 15%);\r\n    color: darken($info, 40%);\r\n  }\r\n\r\n  .img-box {\r\n    text-align: center;\r\n    background-color: #81caf7;\r\n\r\n    img {\r\n      max-width: 240px;\r\n      width: 100%;\r\n\r\n      @include media-breakpoint-up(lg) {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n//查詢結果\r\n.resultDetail {\r\n  padding: .5rem 0 0 0;\r\n  font-size: .925rem;\r\n\r\n  @include media-breakpoint-up(md) {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  p {\r\n    margin-bottom: .3rem;\r\n  }\r\n\r\n  strong {\r\n    display: block;\r\n    color: #237d80;\r\n\r\n    @include media-breakpoint-up(md) {\r\n      display: inline;\r\n    }\r\n  }\r\n}", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// stylelint-disable declaration-no-important, selector-list-comma-newline-after\n\n//\n// Headings\n//\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n}\n\nh1, .h1 { @include font-size($h1-font-size); }\nh2, .h2 { @include font-size($h2-font-size); }\nh3, .h3 { @include font-size($h3-font-size); }\nh4, .h4 { @include font-size($h4-font-size); }\nh5, .h5 { @include font-size($h5-font-size); }\nh6, .h6 { @include font-size($h6-font-size); }\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n.display-1 {\n  @include font-size($display1-size);\n  font-weight: $display1-weight;\n  line-height: $display-line-height;\n}\n.display-2 {\n  @include font-size($display2-size);\n  font-weight: $display2-weight;\n  line-height: $display-line-height;\n}\n.display-3 {\n  @include font-size($display3-size);\n  font-weight: $display3-weight;\n  line-height: $display-line-height;\n}\n.display-4 {\n  @include font-size($display4-size);\n  font-weight: $display4-weight;\n  line-height: $display-line-height;\n}\n\n\n//\n// Horizontal rules\n//\n\nhr {\n  margin-top: $hr-margin-y;\n  margin-bottom: $hr-margin-y;\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n}\n\n\n//\n// Emphasis\n//\n\nsmall,\n.small {\n  @include font-size($small-font-size);\n  font-weight: $font-weight-normal;\n}\n\nmark,\n.mark {\n  padding: $mark-padding;\n  background-color: $mark-bg;\n}\n\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size(90%);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $spacer;\n  @include font-size($blockquote-font-size);\n}\n\n.blockquote-footer {\n  display: block;\n  @include font-size($blockquote-small-font-size);\n  color: $blockquote-small-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid() {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size.\n\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url($file-1x);\n\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\n  // but doesn't convert dppx=>dpi.\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\n  // Compatibility info: https://caniuse.com/#feat=css-media-resolution\n  @media only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\n    only screen and (min-resolution: 2dppx) { // Standardized\n    background-image: url($file-2x);\n    background-size: $width-1x $height-1x;\n  }\n  @include deprecate(\"`img-retina()`\", \"v4.3.0\", \"v5\");\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer / 2;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// Inline code\ncode {\n  @include font-size($code-font-size);\n  color: $code-color;\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\n// User input typically entered via keyboard\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n  @include box-shadow($kbd-box-shadow);\n\n  kbd {\n    padding: 0;\n    @include font-size(100%);\n    font-weight: $nested-kbd-font-weight;\n    @include box-shadow(none);\n  }\n}\n\n// Blocks of code\npre {\n  display: block;\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\n// Enable scrollable blocks of code\n.pre-scrollable {\n  max-height: $pre-scrollable-max-height;\n  overflow-y: scroll;\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n  }\n\n  // Remove the negative margin from default .row, then the horizontal padding\n  // from all immediate children columns (to prevent runaway style inheritance).\n  .no-gutters {\n    margin-right: 0;\n    margin-left: 0;\n\n    > .col,\n    > [class*=\"col-\"] {\n      padding-right: 0;\n      padding-left: 0;\n    }\n  }\n}\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-container($gutter: $grid-gutter-width) {\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$gutter / 2;\n  margin-left: -$gutter / 2;\n}\n\n// For each breakpoint, define the maximum width of the container in a media query\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint, $container-max-width in $max-widths {\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      max-width: $container-max-width;\n    }\n  }\n  @include deprecate(\"The `make-container-max-widths` mixin\", \"v4.5.2\", \"v5\");\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  position: relative;\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we use `flex` values\n  // later on to override this initial width.\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 percentage($size / $columns);\n  // Add a `max-width` to ensure content within each column does not blow out\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\n  // do not appear to require this.\n  max-width: percentage($size / $columns);\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%; // Reset earlier grid tiers\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 100% / $count;\n    max-width: 100% / $count;\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Framework grid generation\n//\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  // Common properties for all breakpoints\n  %grid-column {\n    position: relative;\n    width: 100%;\n    padding-right: $gutter / 2;\n    padding-left: $gutter / 2;\n  }\n\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @if $columns > 0 {\n      // Allow columns to stretch full width below their breakpoints\n      @for $i from 1 through $columns {\n        .col#{$infix}-#{$i} {\n          @extend %grid-column;\n        }\n      }\n    }\n\n    .col#{$infix},\n    .col#{$infix}-auto {\n      @extend %grid-column;\n    }\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex-basis: 0;\n        flex-grow: 1;\n        max-width: 100%;\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n      }\n\n      .order#{$infix}-first { order: -1; }\n\n      .order#{$infix}-last { order: $columns + 1; }\n\n      @for $i from 0 through $columns {\n        .order#{$infix}-#{$i} { order: $i; }\n      }\n\n      @if $columns > 0 {\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  width: 100%;\n  margin-bottom: $spacer;\n  color: $table-color;\n  background-color: $table-bg; // Reset for nesting within parents with `background-color`.\n\n  th,\n  td {\n    padding: $table-cell-padding;\n    vertical-align: top;\n    border-top: $table-border-width solid $table-border-color;\n  }\n\n  thead th {\n    vertical-align: bottom;\n    border-bottom: (2 * $table-border-width) solid $table-border-color;\n  }\n\n  tbody + tbody {\n    border-top: (2 * $table-border-width) solid $table-border-color;\n  }\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  th,\n  td {\n    padding: $table-cell-padding-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n\n.table-bordered {\n  border: $table-border-width solid $table-border-color;\n\n  th,\n  td {\n    border: $table-border-width solid $table-border-color;\n  }\n\n  thead {\n    th,\n    td {\n      border-bottom-width: 2 * $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n  th,\n  td,\n  thead th,\n  tbody + tbody {\n    border: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n.table-striped {\n  tbody tr:nth-of-type(#{$table-striped-order}) {\n    background-color: $table-accent-bg;\n  }\n}\n\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  tbody tr {\n    @include hover() {\n      color: $table-hover-color;\n      background-color: $table-hover-bg;\n    }\n  }\n}\n\n\n// Table backgrounds\n//\n// Exact selectors below required to override `.table-striped` and prevent\n// inheritance to nested tables.\n\n@each $color, $value in $theme-colors {\n  @include table-row-variant($color, theme-color-level($color, $table-bg-level), theme-color-level($color, $table-border-level));\n}\n\n@include table-row-variant(active, $table-active-bg);\n\n\n// Dark styles\n//\n// Same table markup, but inverted color scheme: dark background and light text.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.table {\n  .thead-dark {\n    th {\n      color: $table-dark-color;\n      background-color: $table-dark-bg;\n      border-color: $table-dark-border-color;\n    }\n  }\n\n  .thead-light {\n    th {\n      color: $table-head-color;\n      background-color: $table-head-bg;\n      border-color: $table-border-color;\n    }\n  }\n}\n\n.table-dark {\n  color: $table-dark-color;\n  background-color: $table-dark-bg;\n\n  th,\n  td,\n  thead th {\n    border-color: $table-dark-border-color;\n  }\n\n  &.table-bordered {\n    border: 0;\n  }\n\n  &.table-striped {\n    tbody tr:nth-of-type(#{$table-striped-order}) {\n      background-color: $table-dark-accent-bg;\n    }\n  }\n\n  &.table-hover {\n    tbody tr {\n      @include hover() {\n        color: $table-dark-hover-color;\n        background-color: $table-dark-hover-bg;\n      }\n    }\n  }\n}\n\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n.table-responsive {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    &#{$infix} {\n      @include media-breakpoint-down($breakpoint) {\n        display: block;\n        width: 100%;\n        overflow-x: auto;\n        -webkit-overflow-scrolling: touch;\n\n        // Prevent double border on horizontal scroll due to use of `display: block;`\n        > .table-bordered {\n          border: 0;\n        }\n      }\n    }\n  }\n}\n", "// Tables\n\n@mixin table-row-variant($state, $background, $border: null) {\n  // Exact selectors below required to override `.table-striped` and prevent\n  // inheritance to nested tables.\n  .table-#{$state} {\n    &,\n    > th,\n    > td {\n      background-color: $background;\n    }\n\n    @if $border != null {\n      th,\n      td,\n      thead th,\n      tbody + tbody {\n        border-color: $border;\n      }\n    }\n  }\n\n  // Hover states for `.table-hover`\n  // Note: this is not available for cells or rows within `thead` or `tfoot`.\n  .table-hover {\n    $hover-background: darken($background, 5%);\n\n    .table-#{$state} {\n      @include hover() {\n        background-color: $hover-background;\n\n        > td,\n        > th {\n          background-color: $hover-background;\n        }\n      }\n    }\n  }\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Textual form controls\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: $input-height;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  // Unstyle the caret on `<select>`s in IE10+.\n  &::-ms-expand {\n    background-color: transparent;\n    border: 0;\n  }\n\n  // Remove select outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $input-color;\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  @include form-control-focus($ignore-warning: true);\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled and read-only inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled,\n  &[readonly] {\n    background-color: $input-disabled-bg;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n}\n\ninput[type=\"date\"],\ninput[type=\"time\"],\ninput[type=\"datetime-local\"],\ninput[type=\"month\"] {\n  &.form-control {\n    appearance: none; // Fix appearance for date inputs in Safari\n  }\n}\n\nselect.form-control {\n  &:focus::-ms-value {\n    // Suppress the nested default white text on blue background highlight given to\n    // the selected option text when the (still closed) <select> receives focus\n    // in IE and (under certain conditions) Edge, as it looks bad and cannot be made to\n    // match the appearance of the native widget.\n    // See https://github.com/twbs/bootstrap/issues/19398.\n    color: $input-color;\n    background-color: $input-bg;\n  }\n}\n\n// Make file inputs better match text inputs by forcing them to new lines.\n.form-control-file,\n.form-control-range {\n  display: block;\n  width: 100%;\n}\n\n\n//\n// Labels\n//\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<label>/<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  line-height: $input-line-height;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n}\n\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  @include font-size($input-font-size);\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n  @include border-radius($input-border-radius-sm);\n}\n\n.form-control-lg {\n  height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n  @include border-radius($input-border-radius-lg);\n}\n\n// stylelint-disable-next-line no-duplicate-selectors\nselect.form-control {\n  &[size],\n  &[multiple] {\n    height: auto;\n  }\n}\n\ntextarea.form-control {\n  height: auto;\n}\n\n// Form groups\n//\n// Designed to help with the organization and spacing of vertical forms. For\n// horizontal forms, use the predefined grid classes.\n\n.form-group {\n  margin-bottom: $form-group-margin-bottom;\n}\n\n.form-text {\n  display: block;\n  margin-top: $form-text-margin-top;\n}\n\n\n// Form grid\n//\n// Special replacement for our grid system's `.row` for tighter form layouts.\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$form-grid-gutter-width / 2;\n  margin-left: -$form-grid-gutter-width / 2;\n\n  > .col,\n  > [class*=\"col-\"] {\n    padding-right: $form-grid-gutter-width / 2;\n    padding-left: $form-grid-gutter-width / 2;\n  }\n}\n\n\n// Checkboxes and radios\n//\n// Indent the labels to position radios/checkboxes as hanging controls.\n\n.form-check {\n  position: relative;\n  display: block;\n  padding-left: $form-check-input-gutter;\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: $form-check-input-margin-y;\n  margin-left: -$form-check-input-gutter;\n\n  // Use [disabled] and :disabled for workaround https://github.com/twbs/bootstrap/issues/28247\n  &[disabled] ~ .form-check-label,\n  &:disabled ~ .form-check-label {\n    color: $text-muted;\n  }\n}\n\n.form-check-label {\n  margin-bottom: 0; // Override default `<label>` bottom margin\n}\n\n.form-check-inline {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 0; // Override base .form-check\n  margin-right: $form-check-inline-margin-x;\n\n  // Undo .form-check-input defaults and add some `margin-right`.\n  .form-check-input {\n    position: static;\n    margin-top: 0;\n    margin-right: $form-check-inline-input-margin-x;\n    margin-left: 0;\n  }\n}\n\n\n// Form validation\n//\n// Provide feedback to users when form field values are valid or invalid. Works\n// primarily for client-side validation via scoped `:invalid` and `:valid`\n// pseudo-classes but also includes `.is-invalid` and `.is-valid` classes for\n// server side validation.\n\n@each $state, $data in $form-validation-states {\n  @include form-validation-state($state, map-get($data, color), map-get($data, icon));\n}\n\n// Inline forms\n//\n// Make forms appear inline(-block) by adding the `.form-inline` class. Inline\n// forms begin stacked on extra small (mobile) devices and then go inline when\n// viewports reach <768px.\n//\n// Requires wrapping inputs and labels with `.form-group` for proper display of\n// default HTML form controls and our custom form controls (e.g., input groups).\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center; // Prevent shorter elements from growing to same height as others (e.g., small buttons growing to normal sized button height)\n\n  // Because we use flex, the initial sizing of checkboxes is collapsed and\n  // doesn't occupy the full-width (which is what we want for xs grid tier),\n  // so we force that here.\n  .form-check {\n    width: 100%;\n  }\n\n  // Kick in the inline\n  @include media-breakpoint-up(sm) {\n    label {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0;\n    }\n\n    // Inline-block all the things for \"inline\"\n    .form-group {\n      display: flex;\n      flex: 0 0 auto;\n      flex-flow: row wrap;\n      align-items: center;\n      margin-bottom: 0;\n    }\n\n    // Allow folks to *not* use `.form-group`\n    .form-control {\n      display: inline-block;\n      width: auto; // Prevent labels from stacking above inputs in `.form-group`\n      vertical-align: middle;\n    }\n\n    // Make static controls behave like regular ones\n    .form-control-plaintext {\n      display: inline-block;\n    }\n\n    .input-group,\n    .custom-select {\n      width: auto;\n    }\n\n    // Remove default margin on radios/checkboxes that were used for stacking, and\n    // then undo the floating of radios and checkboxes to match.\n    .form-check {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: auto;\n      padding-left: 0;\n    }\n    .form-check-input {\n      position: relative;\n      flex-shrink: 0;\n      margin-top: 0;\n      margin-right: $form-check-input-margin-x;\n      margin-left: 0;\n    }\n\n    .custom-control {\n      align-items: center;\n      justify-content: center;\n    }\n    .custom-control-label {\n      margin-bottom: 0;\n    }\n  }\n}\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  @if length($map) > 0 {\n    $values: map-values($map);\n    $first-value: nth($values, 1);\n    @if $first-value != 0 {\n      @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n    }\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n//\n// Requires the use of quotes around data URIs.\n\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      // Do not escape the url brackets\n      @if str-index($string, \"url(\") == 1 {\n        $string: url(\"#{str-replace(str-slice($string, 6, -3), $char, $encoded)}\");\n      } @else {\n        $string: str-replace($string, $char, $encoded);\n      }\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@function color-yiq($color, $dark: $yiq-text-dark, $light: $yiq-text-light) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= $yiq-contrasted-threshold) {\n    @return $dark;\n  } @else {\n    @return $light;\n  }\n}\n\n// Retrieve color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function gray($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, $black, $white);\n  $level: abs($level);\n\n  @return mix($color-base, $color, $level * $theme-color-interval);\n}\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "// stylelint-disable property-disallowed-list\n@mixin transition($transition...) {\n  @if length($transition) == 0 {\n    $transition: $transition-base;\n  }\n\n  @if length($transition) > 1 {\n    @each $value in $transition {\n      @if $value == null or $value == none {\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\n      }\n    }\n  }\n\n  @if $enable-transitions {\n    @if nth($transition, 1) != null {\n      transition: $transition;\n    }\n\n    @if $enable-prefers-reduced-motion-media-query and nth($transition, 1) != null and nth($transition, 1) != none {\n      @media (prefers-reduced-motion: reduce) {\n        transition: none;\n      }\n    }\n  }\n}\n", "// Form control focus state\n//\n// Generate a customized focus state and for any input with the specified color,\n// which defaults to the `$input-focus-border-color` variable.\n//\n// We highly encourage you to not customize the default value, but instead use\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\n// WebKit's default styles, but applicable to a wider range of browsers. Its\n// usability and accessibility should be taken into account with any change.\n//\n// Example usage: change the default blue border and shadow to white for better\n// contrast against a dark gray background.\n@mixin form-control-focus($ignore-warning: false) {\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($input-box-shadow, $input-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n  @include deprecate(\"The `form-control-focus()` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-********* for more details\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state($state, $color, $icon) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    left: 0;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: color-yiq($color);\n    background-color: rgba($color, $form-feedback-tooltip-opacity);\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .custom-select {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $custom-select-feedback-icon-padding-right;\n        background: $custom-select-background, escape-svg($icon) $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      ~ .form-check-label {\n        color: $color;\n      }\n\n      ~ .#{$state}-feedback,\n      ~ .#{$state}-tooltip {\n        display: block;\n      }\n    }\n  }\n\n  .custom-control-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-control-label {\n        color: $color;\n\n        &::before {\n          border-color: $color;\n        }\n      }\n\n      &:checked {\n        ~ .custom-control-label::before {\n          border-color: lighten($color, 10%);\n          @include gradient-bg(lighten($color, 10%));\n        }\n      }\n\n      &:focus {\n        ~ .custom-control-label::before {\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n\n        &:not(:checked) ~ .custom-control-label::before {\n          border-color: $color;\n        }\n      }\n    }\n  }\n\n  // custom file\n  .custom-file-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-file-label {\n        border-color: $color;\n      }\n\n      &:focus {\n        ~ .custom-file-label {\n          border-color: $color;\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n      }\n    }\n  }\n}\n", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Base styles\n//\n\n.btn {\n  display: inline-block;\n  font-family: $btn-font-family;\n  font-weight: $btn-font-weight;\n  color: $body-color;\n  text-align: center;\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  user-select: none;\n  background-color: transparent;\n  border: $btn-border-width solid transparent;\n  @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);\n  @include transition($btn-transition);\n\n  @include hover() {\n    color: $body-color;\n    text-decoration: none;\n  }\n\n  &:focus,\n  &.focus {\n    outline: 0;\n    box-shadow: $btn-focus-box-shadow;\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    opacity: $btn-disabled-opacity;\n    @include box-shadow(none);\n  }\n\n  &:not(:disabled):not(.disabled) {\n    cursor: if($enable-pointer-cursor-for-buttons, pointer, null);\n\n    &:active,\n    &.active {\n      @include box-shadow($btn-active-box-shadow);\n\n      &:focus {\n        @include box-shadow($btn-focus-box-shadow, $btn-active-box-shadow);\n      }\n    }\n  }\n}\n\n// Future-proof disabling of clicks on `<a>` elements\na.btn.disabled,\nfieldset:disabled a.btn {\n  pointer-events: none;\n}\n\n\n//\n// Alternate buttons\n//\n\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @include button-variant($value, $value);\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  font-weight: $font-weight-normal;\n  color: $link-color;\n  text-decoration: $link-decoration;\n\n  @include hover() {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus,\n  &.focus {\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $btn-link-disabled-color;\n    pointer-events: none;\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-line-height-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);\n}\n\n\n//\n// Block button\n//\n\n.btn-block {\n  display: block;\n  width: 100%;\n\n  // Vertically space out multiple block buttons\n  + .btn-block {\n    margin-top: $btn-block-spacing-y;\n  }\n}\n\n// Specificity overrides\ninput[type=\"submit\"],\ninput[type=\"reset\"],\ninput[type=\"button\"] {\n  &.btn-block {\n    width: 100%;\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    @if $enable-shadows {\n      @include box-shadow($btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 1 1 auto;\n\n    // Bring the hover, focused, and \"active\" buttons to the front to overlay\n    // the borders properly\n    @include hover() {\n      z-index: 1;\n    }\n    &:focus,\n    &:active,\n    &.active {\n      z-index: 1;\n    }\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n  // Prevent double borders when buttons are next to each other\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-left: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-right-radius(0);\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) > .btn {\n    @include border-left-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropright &::after {\n    margin-left: 0;\n  }\n\n  .dropleft &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  > .btn,\n  > .btn-group {\n    width: 100%;\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-top: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-bottom-radius(0);\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) > .btn {\n    @include border-top-radius(0);\n  }\n}\n\n\n// Checkbox and radio options\n//\n// In order to support the browser's form validation feedback, powered by the\n// `required` attribute, we have to \"hide\" the inputs via `clip`. We cannot use\n// `display: none;` or `visibility: hidden;` as that also hides the popover.\n// Simply visually hiding the inputs via `opacity` would leave them clickable in\n// certain cases which is prevented by using `clip` and `pointer-events`.\n// This way, we ensure a DOM element is visible to position the popover from.\n//\n// See https://github.com/twbs/bootstrap/pull/12794 and\n// https://github.com/twbs/bootstrap/pull/14559 for more information.\n\n.btn-group-toggle {\n  > .btn,\n  > .btn-group > .btn {\n    margin-bottom: 0; // Override default `<label>` value\n\n    input[type=\"radio\"],\n    input[type=\"checkbox\"] {\n      position: absolute;\n      clip: rect(0, 0, 0, 0);\n      pointer-events: none;\n    }\n  }\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-control-plaintext,\n  > .custom-select,\n  > .custom-file {\n    position: relative; // For focus state's z-index\n    flex: 1 1 auto;\n    width: 1%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n    margin-bottom: 0;\n\n    + .form-control,\n    + .custom-select,\n    + .custom-file {\n      margin-left: -$input-border-width;\n    }\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .custom-select:focus,\n  > .custom-file .custom-file-input:focus ~ .custom-file-label {\n    z-index: 3;\n  }\n\n  // Bring the custom file input above the label\n  > .custom-file .custom-file-input:focus {\n    z-index: 4;\n  }\n\n  > .form-control,\n  > .custom-select {\n    &:not(:last-child) { @include border-right-radius(0); }\n    &:not(:first-child) { @include border-left-radius(0); }\n  }\n\n  // Custom file inputs have more complex markup, thus requiring different\n  // border-radius overrides.\n  > .custom-file {\n    display: flex;\n    align-items: center;\n\n    &:not(:last-child) .custom-file-label,\n    &:not(:last-child) .custom-file-label::after { @include border-right-radius(0); }\n    &:not(:first-child) .custom-file-label { @include border-left-radius(0); }\n  }\n}\n\n\n// Prepend and append\n//\n// While it requires one extra layer of HTML for each, dedicated prepend and\n// append elements allow us to 1) be less clever, 2) simplify our selectors, and\n// 3) support HTML5 form validation.\n\n.input-group-prepend,\n.input-group-append {\n  display: flex;\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 3;\n    }\n  }\n\n  .btn + .btn,\n  .btn + .input-group-text,\n  .input-group-text + .input-group-text,\n  .input-group-text + .btn {\n    margin-left: -$input-border-width;\n  }\n}\n\n.input-group-prepend { margin-right: -$input-border-width; }\n.input-group-append { margin-left: -$input-border-width; }\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-padding-y $input-padding-x;\n  margin-bottom: 0; // Allow use of <label> elements by overriding our default margin-bottom\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $font-weight-normal;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n\n  // Nuke default margins from checkboxes and radios to vertically center within.\n  input[type=\"radio\"],\n  input[type=\"checkbox\"] {\n    margin-top: 0;\n  }\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control:not(textarea),\n.input-group-lg > .custom-select {\n  height: $input-height-lg;\n}\n\n.input-group-lg > .form-control,\n.input-group-lg > .custom-select,\n.input-group-lg > .input-group-prepend > .input-group-text,\n.input-group-lg > .input-group-append > .input-group-text,\n.input-group-lg > .input-group-prepend > .btn,\n.input-group-lg > .input-group-append > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control:not(textarea),\n.input-group-sm > .custom-select {\n  height: $input-height-sm;\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .custom-select,\n.input-group-sm > .input-group-prepend > .input-group-text,\n.input-group-sm > .input-group-append > .input-group-text,\n.input-group-sm > .input-group-prepend > .btn,\n.input-group-sm > .input-group-append > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .custom-select,\n.input-group-sm > .custom-select {\n  padding-right: $custom-select-padding-x + $custom-select-indicator-padding;\n}\n\n\n// Prepend and append rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {\n  @include border-right-radius(0);\n}\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {\n  @include border-left-radius(0);\n}\n", "// Embedded icons from Open Iconic.\n// Released under MIT and copyright 2014 Waybury.\n// https://useiconic.com/open\n\n\n// Checkboxes and radios\n//\n// Base class takes care of all the key behavioral aspects.\n\n.custom-control {\n  position: relative;\n  z-index: 1;\n  display: block;\n  min-height: $font-size-base * $line-height-base;\n  padding-left: $custom-control-gutter + $custom-control-indicator-size;\n  color-adjust: exact; // Keep themed appearance for print\n}\n\n.custom-control-inline {\n  display: inline-flex;\n  margin-right: $custom-control-spacer-x;\n}\n\n.custom-control-input {\n  position: absolute;\n  left: 0;\n  z-index: -1; // Put the input behind the label so it doesn't overlay text\n  width: $custom-control-indicator-size;\n  height: ($font-size-base * $line-height-base + $custom-control-indicator-size) / 2;\n  opacity: 0;\n\n  &:checked ~ .custom-control-label::before {\n    color: $custom-control-indicator-checked-color;\n    border-color: $custom-control-indicator-checked-border-color;\n    @include gradient-bg($custom-control-indicator-checked-bg);\n    @include box-shadow($custom-control-indicator-checked-box-shadow);\n  }\n\n  &:focus ~ .custom-control-label::before {\n    // the mixin is not used here to make sure there is feedback\n    @if $enable-shadows {\n      box-shadow: $input-box-shadow, $custom-control-indicator-focus-box-shadow;\n    } @else {\n      box-shadow: $custom-control-indicator-focus-box-shadow;\n    }\n  }\n\n  &:focus:not(:checked) ~ .custom-control-label::before {\n    border-color: $custom-control-indicator-focus-border-color;\n  }\n\n  &:not(:disabled):active ~ .custom-control-label::before {\n    color: $custom-control-indicator-active-color;\n    background-color: $custom-control-indicator-active-bg;\n    border-color: $custom-control-indicator-active-border-color;\n    @include box-shadow($custom-control-indicator-active-box-shadow);\n  }\n\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\n  &[disabled],\n  &:disabled {\n    ~ .custom-control-label {\n      color: $custom-control-label-disabled-color;\n\n      &::before {\n        background-color: $custom-control-indicator-disabled-bg;\n      }\n    }\n  }\n}\n\n// Custom control indicators\n//\n// Build the custom controls out of pseudo-elements.\n\n.custom-control-label {\n  position: relative;\n  margin-bottom: 0;\n  color: $custom-control-label-color;\n  vertical-align: top;\n  cursor: $custom-control-cursor;\n\n  // Background-color and (when enabled) gradient\n  &::before {\n    position: absolute;\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\n    left: -($custom-control-gutter + $custom-control-indicator-size);\n    display: block;\n    width: $custom-control-indicator-size;\n    height: $custom-control-indicator-size;\n    pointer-events: none;\n    content: \"\";\n    background-color: $custom-control-indicator-bg;\n    border: $custom-control-indicator-border-color solid $custom-control-indicator-border-width;\n    @include box-shadow($custom-control-indicator-box-shadow);\n  }\n\n  // Foreground (icon)\n  &::after {\n    position: absolute;\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\n    left: -($custom-control-gutter + $custom-control-indicator-size);\n    display: block;\n    width: $custom-control-indicator-size;\n    height: $custom-control-indicator-size;\n    content: \"\";\n    background: no-repeat 50% / #{$custom-control-indicator-bg-size};\n  }\n}\n\n\n// Checkboxes\n//\n// Tweak just a few things for checkboxes.\n\n.custom-checkbox {\n  .custom-control-label::before {\n    @include border-radius($custom-checkbox-indicator-border-radius);\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-image: escape-svg($custom-checkbox-indicator-icon-checked);\n    }\n  }\n\n  .custom-control-input:indeterminate ~ .custom-control-label {\n    &::before {\n      border-color: $custom-checkbox-indicator-indeterminate-border-color;\n      @include gradient-bg($custom-checkbox-indicator-indeterminate-bg);\n      @include box-shadow($custom-checkbox-indicator-indeterminate-box-shadow);\n    }\n    &::after {\n      background-image: escape-svg($custom-checkbox-indicator-icon-indeterminate);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      @include gradient-bg($custom-control-indicator-checked-disabled-bg);\n    }\n    &:indeterminate ~ .custom-control-label::before {\n      @include gradient-bg($custom-control-indicator-checked-disabled-bg);\n    }\n  }\n}\n\n// Radios\n//\n// Tweak just a few things for radios.\n\n.custom-radio {\n  .custom-control-label::before {\n    // stylelint-disable-next-line property-disallowed-list\n    border-radius: $custom-radio-indicator-border-radius;\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-image: escape-svg($custom-radio-indicator-icon-checked);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      @include gradient-bg($custom-control-indicator-checked-disabled-bg);\n    }\n  }\n}\n\n\n// switches\n//\n// Tweak a few things for switches\n\n.custom-switch {\n  padding-left: $custom-switch-width + $custom-control-gutter;\n\n  .custom-control-label {\n    &::before {\n      left: -($custom-switch-width + $custom-control-gutter);\n      width: $custom-switch-width;\n      pointer-events: all;\n      // stylelint-disable-next-line property-disallowed-list\n      border-radius: $custom-switch-indicator-border-radius;\n    }\n\n    &::after {\n      top: add(($font-size-base * $line-height-base - $custom-control-indicator-size) / 2, $custom-control-indicator-border-width * 2);\n      left: add(-($custom-switch-width + $custom-control-gutter), $custom-control-indicator-border-width * 2);\n      width: $custom-switch-indicator-size;\n      height: $custom-switch-indicator-size;\n      background-color: $custom-control-indicator-border-color;\n      // stylelint-disable-next-line property-disallowed-list\n      border-radius: $custom-switch-indicator-border-radius;\n      @include transition(transform .15s ease-in-out, $custom-forms-transition);\n    }\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-color: $custom-control-indicator-bg;\n      transform: translateX($custom-switch-width - $custom-control-indicator-size);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      @include gradient-bg($custom-control-indicator-checked-disabled-bg);\n    }\n  }\n}\n\n\n// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n//\n\n.custom-select {\n  display: inline-block;\n  width: 100%;\n  height: $custom-select-height;\n  padding: $custom-select-padding-y ($custom-select-padding-x + $custom-select-indicator-padding) $custom-select-padding-y $custom-select-padding-x;\n  font-family: $custom-select-font-family;\n  @include font-size($custom-select-font-size);\n  font-weight: $custom-select-font-weight;\n  line-height: $custom-select-line-height;\n  color: $custom-select-color;\n  vertical-align: middle;\n  background: $custom-select-bg $custom-select-background;\n  border: $custom-select-border-width solid $custom-select-border-color;\n  @include border-radius($custom-select-border-radius, 0);\n  @include box-shadow($custom-select-box-shadow);\n  appearance: none;\n\n  &:focus {\n    border-color: $custom-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($custom-select-box-shadow, $custom-select-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $custom-select-focus-box-shadow;\n    }\n\n    &::-ms-value {\n      // For visual consistency with other platforms/browsers,\n      // suppress the default white text on blue background highlight given to\n      // the selected option text when the (still closed) <select> receives focus\n      // in IE and (under certain conditions) Edge.\n      // See https://github.com/twbs/bootstrap/issues/19398.\n      color: $input-color;\n      background-color: $input-bg;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    height: auto;\n    padding-right: $custom-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $custom-select-disabled-color;\n    background-color: $custom-select-disabled-bg;\n  }\n\n  // Hides the default caret in IE11\n  &::-ms-expand {\n    display: none;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $custom-select-color;\n  }\n}\n\n.custom-select-sm {\n  height: $custom-select-height-sm;\n  padding-top: $custom-select-padding-y-sm;\n  padding-bottom: $custom-select-padding-y-sm;\n  padding-left: $custom-select-padding-x-sm;\n  @include font-size($custom-select-font-size-sm);\n}\n\n.custom-select-lg {\n  height: $custom-select-height-lg;\n  padding-top: $custom-select-padding-y-lg;\n  padding-bottom: $custom-select-padding-y-lg;\n  padding-left: $custom-select-padding-x-lg;\n  @include font-size($custom-select-font-size-lg);\n}\n\n\n// File\n//\n// Custom file input.\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  height: $custom-file-height;\n  margin-bottom: 0;\n}\n\n.custom-file-input {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: $custom-file-height;\n  margin: 0;\n  opacity: 0;\n\n  &:focus ~ .custom-file-label {\n    border-color: $custom-file-focus-border-color;\n    box-shadow: $custom-file-focus-box-shadow;\n  }\n\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\n  &[disabled] ~ .custom-file-label,\n  &:disabled ~ .custom-file-label {\n    background-color: $custom-file-disabled-bg;\n  }\n\n  @each $lang, $value in $custom-file-text {\n    &:lang(#{$lang}) ~ .custom-file-label::after {\n      content: $value;\n    }\n  }\n\n  ~ .custom-file-label[data-browse]::after {\n    content: attr(data-browse);\n  }\n}\n\n.custom-file-label {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1;\n  height: $custom-file-height;\n  padding: $custom-file-padding-y $custom-file-padding-x;\n  font-family: $custom-file-font-family;\n  font-weight: $custom-file-font-weight;\n  line-height: $custom-file-line-height;\n  color: $custom-file-color;\n  background-color: $custom-file-bg;\n  border: $custom-file-border-width solid $custom-file-border-color;\n  @include border-radius($custom-file-border-radius);\n  @include box-shadow($custom-file-box-shadow);\n\n  &::after {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 3;\n    display: block;\n    height: $custom-file-height-inner;\n    padding: $custom-file-padding-y $custom-file-padding-x;\n    line-height: $custom-file-line-height;\n    color: $custom-file-button-color;\n    content: \"Browse\";\n    @include gradient-bg($custom-file-button-bg);\n    border-left: inherit;\n    @include border-radius(0 $custom-file-border-radius $custom-file-border-radius 0);\n  }\n}\n\n// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.custom-range {\n  width: 100%;\n  height: add($custom-range-thumb-height, $custom-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  background-color: transparent;\n  appearance: none;\n\n  &:focus {\n    outline: none;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb { box-shadow: $custom-range-thumb-focus-box-shadow; }\n    &::-moz-range-thumb     { box-shadow: $custom-range-thumb-focus-box-shadow; }\n    &::-ms-thumb            { box-shadow: $custom-range-thumb-focus-box-shadow; }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    margin-top: ($custom-range-track-height - $custom-range-thumb-height) / 2; // Webkit specific\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent; // Why?\n    cursor: $custom-range-track-cursor;\n    background-color: $custom-range-track-bg;\n    border-color: transparent;\n    @include border-radius($custom-range-track-border-radius);\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent;\n    cursor: $custom-range-track-cursor;\n    background-color: $custom-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($custom-range-track-border-radius);\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-ms-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    margin-top: 0; // Edge specific\n    margin-right: $custom-range-thumb-focus-box-shadow-width; // Workaround that overflowed box-shadow is hidden.\n    margin-left: $custom-range-thumb-focus-box-shadow-width;  // Workaround that overflowed box-shadow is hidden.\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-ms-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent;\n    cursor: $custom-range-track-cursor;\n    background-color: transparent;\n    border-color: transparent;\n    border-width: $custom-range-thumb-height / 2;\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-ms-fill-lower {\n    background-color: $custom-range-track-bg;\n    @include border-radius($custom-range-track-border-radius);\n  }\n\n  &::-ms-fill-upper {\n    margin-right: 15px; // arbitrary?\n    background-color: $custom-range-track-bg;\n    @include border-radius($custom-range-track-border-radius);\n  }\n\n  &:disabled {\n    &::-webkit-slider-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n\n    &::-webkit-slider-runnable-track {\n      cursor: default;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-track {\n      cursor: default;\n    }\n\n    &::-ms-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n  }\n}\n\n.custom-control-label::before,\n.custom-file-label,\n.custom-select {\n  @include transition($custom-forms-transition);\n}\n", "// stylelint-disable declaration-no-important\n\n.align-baseline    { vertical-align: baseline !important; } // Browser default\n.align-top         { vertical-align: top !important; }\n.align-middle      { vertical-align: middle !important; }\n.align-bottom      { vertical-align: bottom !important; }\n.align-text-bottom { vertical-align: text-bottom !important; }\n.align-text-top    { vertical-align: text-top !important; }\n", "// stylelint-disable declaration-no-important\n\n// Contextual backgrounds\n\n@mixin bg-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    background-color: $color !important;\n  }\n  a#{$parent},\n  button#{$parent} {\n    @include hover-focus() {\n      background-color: darken($color, 10%) !important;\n    }\n  }\n  @include deprecate(\"The `bg-variant` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n@mixin bg-gradient-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x !important;\n  }\n  @include deprecate(\"The `bg-gradient-variant` mixin\", \"v4.5.0\", \"v5\", $ignore-warning);\n}\n", "// stylelint-disable declaration-no-important\n\n@each $color, $value in $theme-colors {\n  @include bg-variant(\".bg-#{$color}\", $value, true);\n}\n\n@if $enable-gradients {\n  @each $color, $value in $theme-colors {\n    @include bg-gradient-variant(\".bg-gradient-#{$color}\", $value, true);\n  }\n}\n\n.bg-white {\n  background-color: $white !important;\n}\n\n.bg-transparent {\n  background-color: transparent !important;\n}\n", "// stylelint-disable property-disallowed-list, declaration-no-important\n\n//\n// Border\n//\n\n.border         { border: $border-width solid $border-color !important; }\n.border-top     { border-top: $border-width solid $border-color !important; }\n.border-right   { border-right: $border-width solid $border-color !important; }\n.border-bottom  { border-bottom: $border-width solid $border-color !important; }\n.border-left    { border-left: $border-width solid $border-color !important; }\n\n.border-0        { border: 0 !important; }\n.border-top-0    { border-top: 0 !important; }\n.border-right-0  { border-right: 0 !important; }\n.border-bottom-0 { border-bottom: 0 !important; }\n.border-left-0   { border-left: 0 !important; }\n\n@each $color, $value in $theme-colors {\n  .border-#{$color} {\n    border-color: $value !important;\n  }\n}\n\n.border-white {\n  border-color: $white !important;\n}\n\n//\n// Border-radius\n//\n\n.rounded-sm {\n  border-radius: $border-radius-sm !important;\n}\n\n.rounded {\n  border-radius: $border-radius !important;\n}\n\n.rounded-top {\n  border-top-left-radius: $border-radius !important;\n  border-top-right-radius: $border-radius !important;\n}\n\n.rounded-right {\n  border-top-right-radius: $border-radius !important;\n  border-bottom-right-radius: $border-radius !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n\n.rounded-left {\n  border-top-left-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n\n.rounded-lg {\n  border-radius: $border-radius-lg !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: $rounded-pill !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Utilities for common `display` values\n//\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $value in $displays {\n      .d#{$infix}-#{$value} { display: $value !important; }\n    }\n  }\n}\n\n\n//\n// Utilities for toggling `display` in print\n//\n\n@media print {\n  @each $value in $displays {\n    .d-print-#{$value} { display: $value !important; }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden;\n\n  &::before {\n    display: block;\n    content: \"\";\n  }\n\n  .embed-responsive-item,\n  iframe,\n  embed,\n  object,\n  video {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border: 0;\n  }\n}\n\n@each $embed-responsive-aspect-ratio in $embed-responsive-aspect-ratios {\n  $embed-responsive-aspect-ratio-x: nth($embed-responsive-aspect-ratio, 1);\n  $embed-responsive-aspect-ratio-y: nth($embed-responsive-aspect-ratio, 2);\n\n  .embed-responsive-#{$embed-responsive-aspect-ratio-x}by#{$embed-responsive-aspect-ratio-y} {\n    &::before {\n      padding-top: percentage($embed-responsive-aspect-ratio-y / $embed-responsive-aspect-ratio-x);\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Flex variation\n//\n// Custom styles for additional flex alignment options.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .flex#{$infix}-row            { flex-direction: row !important; }\n    .flex#{$infix}-column         { flex-direction: column !important; }\n    .flex#{$infix}-row-reverse    { flex-direction: row-reverse !important; }\n    .flex#{$infix}-column-reverse { flex-direction: column-reverse !important; }\n\n    .flex#{$infix}-wrap         { flex-wrap: wrap !important; }\n    .flex#{$infix}-nowrap       { flex-wrap: nowrap !important; }\n    .flex#{$infix}-wrap-reverse { flex-wrap: wrap-reverse !important; }\n    .flex#{$infix}-fill         { flex: 1 1 auto !important; }\n    .flex#{$infix}-grow-0       { flex-grow: 0 !important; }\n    .flex#{$infix}-grow-1       { flex-grow: 1 !important; }\n    .flex#{$infix}-shrink-0     { flex-shrink: 0 !important; }\n    .flex#{$infix}-shrink-1     { flex-shrink: 1 !important; }\n\n    .justify-content#{$infix}-start   { justify-content: flex-start !important; }\n    .justify-content#{$infix}-end     { justify-content: flex-end !important; }\n    .justify-content#{$infix}-center  { justify-content: center !important; }\n    .justify-content#{$infix}-between { justify-content: space-between !important; }\n    .justify-content#{$infix}-around  { justify-content: space-around !important; }\n\n    .align-items#{$infix}-start    { align-items: flex-start !important; }\n    .align-items#{$infix}-end      { align-items: flex-end !important; }\n    .align-items#{$infix}-center   { align-items: center !important; }\n    .align-items#{$infix}-baseline { align-items: baseline !important; }\n    .align-items#{$infix}-stretch  { align-items: stretch !important; }\n\n    .align-content#{$infix}-start   { align-content: flex-start !important; }\n    .align-content#{$infix}-end     { align-content: flex-end !important; }\n    .align-content#{$infix}-center  { align-content: center !important; }\n    .align-content#{$infix}-between { align-content: space-between !important; }\n    .align-content#{$infix}-around  { align-content: space-around !important; }\n    .align-content#{$infix}-stretch { align-content: stretch !important; }\n\n    .align-self#{$infix}-auto     { align-self: auto !important; }\n    .align-self#{$infix}-start    { align-self: flex-start !important; }\n    .align-self#{$infix}-end      { align-self: flex-end !important; }\n    .align-self#{$infix}-center   { align-self: center !important; }\n    .align-self#{$infix}-baseline { align-self: baseline !important; }\n    .align-self#{$infix}-stretch  { align-self: stretch !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { float: left !important; }\n    .float#{$infix}-right { float: right !important; }\n    .float#{$infix}-none  { float: none !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $value in $user-selects {\n  .user-select-#{$value} { user-select: $value !important; }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $value in $overflows {\n  .overflow-#{$value} { overflow: $value !important; }\n}\n", "// stylelint-disable declaration-no-important\n\n// Common values\n@each $position in $positions {\n  .position-#{$position} { position: $position !important; }\n}\n\n// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.sticky-top {\n  @supports (position: sticky) {\n    position: sticky;\n    top: 0;\n    z-index: $zindex-sticky;\n  }\n}\n", "// Only display content to screen readers\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://hugogiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin sr-only() {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n//\n// Credit: HTML5 Boilerplate\n\n@mixin sr-only-focusable() {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    overflow: visible;\n    clip: auto;\n    white-space: normal;\n  }\n}\n", "//\n// Screenreaders\n//\n\n.sr-only {\n  @include sr-only();\n}\n\n.sr-only-focusable {\n  @include sr-only-focusable();\n}\n", "// stylelint-disable declaration-no-important\n\n.shadow-sm { box-shadow: $box-shadow-sm !important; }\n.shadow { box-shadow: $box-shadow !important; }\n.shadow-lg { box-shadow: $box-shadow-lg !important; }\n.shadow-none { box-shadow: none !important; }\n", "// stylelint-disable declaration-no-important\n\n// Width and height\n\n@each $prop, $abbrev in (width: w, height: h) {\n  @each $size, $length in $sizes {\n    .#{$abbrev}-#{$size} { #{$prop}: $length !important; }\n  }\n}\n\n.mw-100 { max-width: 100% !important; }\n.mh-100 { max-height: 100% !important; }\n\n// Viewport additional helpers\n\n.min-vw-100 { min-width: 100vw !important; }\n.min-vh-100 { min-height: 100vh !important; }\n\n.vw-100 { width: 100vw !important; }\n.vh-100 { height: 100vh !important; }\n", "// stylelint-disable declaration-no-important\n\n// Mar<PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto { margin: auto !important; }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .ml#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n    }\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::after {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: 1;\n    // Just in case `pointer-events: none` is set on a parent\n    pointer-events: auto;\n    content: \"\";\n    // IE10 bugfix, see https://stackoverflow.com/questions/16947967/ie10-hover-pseudo-class-doesnt-work-without-background-color\n    background-color: rgba(0, 0, 0, 0);\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Text\n//\n\n.text-monospace { font-family: $font-family-monospace !important; }\n\n// Alignment\n\n.text-justify  { text-align: justify !important; }\n.text-wrap     { white-space: normal !important; }\n.text-nowrap   { white-space: nowrap !important; }\n.text-truncate { @include text-truncate(); }\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: left !important; }\n    .text#{$infix}-right  { text-align: right !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n\n// Transformation\n\n.text-lowercase  { text-transform: lowercase !important; }\n.text-uppercase  { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Weight and italics\n\n.font-weight-light   { font-weight: $font-weight-light !important; }\n.font-weight-lighter { font-weight: $font-weight-lighter !important; }\n.font-weight-normal  { font-weight: $font-weight-normal !important; }\n.font-weight-bold    { font-weight: $font-weight-bold !important; }\n.font-weight-bolder  { font-weight: $font-weight-bolder !important; }\n.font-italic         { font-style: italic !important; }\n\n// Contextual colors\n\n.text-white { color: $white !important; }\n\n@each $color, $value in $theme-colors {\n  @include text-emphasis-variant(\".text-#{$color}\", $value, true);\n}\n\n.text-body { color: $body-color !important; }\n.text-muted { color: $text-muted !important; }\n\n.text-black-50 { color: rgba($black, .5) !important; }\n.text-white-50 { color: rgba($white, .5) !important; }\n\n// Misc\n\n.text-hide {\n  @include text-hide($ignore-warning: true);\n}\n\n.text-decoration-none { text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important; // Deprecated, but avoids issues with flex containers\n  word-wrap: break-word !important; // Used instead of `overflow-wrap` for IE & Edge Legacy\n}\n\n// Reset\n\n.text-reset { color: inherit !important; }\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// stylelint-disable declaration-no-important\n\n// Typography\n\n@mixin text-emphasis-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    color: $color !important;\n  }\n  @if $emphasized-link-hover-darken-percentage != 0 {\n    a#{$parent} {\n      @include hover-focus() {\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\n      }\n    }\n  }\n  @include deprecate(\"`text-emphasis-variant()`\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n", "// CSS image replacement\n@mixin text-hide($ignore-warning: false) {\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Visibility utilities\n//\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n", "/*!\n *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome\n *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)\n */\n\n@import \"variables\";\n@import \"mixins\";\n@import \"path\";\n@import \"core\";\n@import \"larger\";\n@import \"fixed-width\";\n@import \"list\";\n@import \"bordered-pulled\";\n@import \"animated\";\n@import \"rotated-flipped\";\n@import \"stacked\";\n@import \"icons\";\n@import \"screen-reader\";\n", "/* FONT PATH\n * -------------------------- */\n\n@font-face {\n  font-family: 'FontAwesome';\n  src: url('#{$fa-font-path}/fontawesome-webfont.eot?v=#{$fa-version}');\n  src: url('#{$fa-font-path}/fontawesome-webfont.eot?#iefix&v=#{$fa-version}') format('embedded-opentype'),\n    url('#{$fa-font-path}/fontawesome-webfont.woff2?v=#{$fa-version}') format('woff2'),\n    url('#{$fa-font-path}/fontawesome-webfont.woff?v=#{$fa-version}') format('woff'),\n    url('#{$fa-font-path}/fontawesome-webfont.ttf?v=#{$fa-version}') format('truetype'),\n    url('#{$fa-font-path}/fontawesome-webfont.svg?v=#{$fa-version}#fontawesomeregular') format('svg');\n//  src: url('#{$fa-font-path}/FontAwesome.otf') format('opentype'); // used when developing fonts\n  font-weight: normal;\n  font-style: normal;\n}\n", "// Base Class Definition\n// -------------------------\n\n.#{$fa-css-prefix} {\n  display: inline-block;\n  font: normal normal normal #{$fa-font-size-base}/#{$fa-line-height-base} FontAwesome; // shortening font declaration\n  font-size: inherit; // can't have font-size inherit on line above, so need to override\n  text-rendering: auto; // optimizelegibility throws things off #1094\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n\n}\n", "// Icon Sizes\n// -------------------------\n\n/* makes the font 33% larger relative to the icon container */\n.#{$fa-css-prefix}-lg {\n  font-size: (4em / 3);\n  line-height: (3em / 4);\n  vertical-align: -15%;\n}\n.#{$fa-css-prefix}-2x { font-size: 2em; }\n.#{$fa-css-prefix}-3x { font-size: 3em; }\n.#{$fa-css-prefix}-4x { font-size: 4em; }\n.#{$fa-css-prefix}-5x { font-size: 5em; }\n", "// Fixed Width Icons\n// -------------------------\n.#{$fa-css-prefix}-fw {\n  width: (18em / 14);\n  text-align: center;\n}\n", "// List Icons\n// -------------------------\n\n.#{$fa-css-prefix}-ul {\n  padding-left: 0;\n  margin-left: $fa-li-width;\n  list-style-type: none;\n  > li { position: relative; }\n}\n.#{$fa-css-prefix}-li {\n  position: absolute;\n  left: -$fa-li-width;\n  width: $fa-li-width;\n  top: (2em / 14);\n  text-align: center;\n  &.#{$fa-css-prefix}-lg {\n    left: -$fa-li-width + (4em / 14);\n  }\n}\n", "// Bordered & Pulled\n// -------------------------\n\n.#{$fa-css-prefix}-border {\n  padding: .2em .25em .15em;\n  border: solid .08em $fa-border-color;\n  border-radius: .1em;\n}\n\n.#{$fa-css-prefix}-pull-left { float: left; }\n.#{$fa-css-prefix}-pull-right { float: right; }\n\n.#{$fa-css-prefix} {\n  &.#{$fa-css-prefix}-pull-left { margin-right: .3em; }\n  &.#{$fa-css-prefix}-pull-right { margin-left: .3em; }\n}\n\n/* Deprecated as of 4.4.0 */\n.pull-right { float: right; }\n.pull-left { float: left; }\n\n.#{$fa-css-prefix} {\n  &.pull-left { margin-right: .3em; }\n  &.pull-right { margin-left: .3em; }\n}\n", "// Spinning Icons\n// --------------------------\n\n.#{$fa-css-prefix}-spin {\n  -webkit-animation: fa-spin 2s infinite linear;\n          animation: fa-spin 2s infinite linear;\n}\n\n.#{$fa-css-prefix}-pulse {\n  -webkit-animation: fa-spin 1s infinite steps(8);\n          animation: fa-spin 1s infinite steps(8);\n}\n\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(359deg);\n            transform: rotate(359deg);\n  }\n}\n\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(359deg);\n            transform: rotate(359deg);\n  }\n}\n", "// Rotated & Flipped Icons\n// -------------------------\n\n.#{$fa-css-prefix}-rotate-90  { @include fa-icon-rotate(90deg, 1);  }\n.#{$fa-css-prefix}-rotate-180 { @include fa-icon-rotate(180deg, 2); }\n.#{$fa-css-prefix}-rotate-270 { @include fa-icon-rotate(270deg, 3); }\n\n.#{$fa-css-prefix}-flip-horizontal { @include fa-icon-flip(-1, 1, 0); }\n.#{$fa-css-prefix}-flip-vertical   { @include fa-icon-flip(1, -1, 2); }\n\n// Hook for IE8-9\n// -------------------------\n\n:root .#{$fa-css-prefix}-rotate-90,\n:root .#{$fa-css-prefix}-rotate-180,\n:root .#{$fa-css-prefix}-rotate-270,\n:root .#{$fa-css-prefix}-flip-horizontal,\n:root .#{$fa-css-prefix}-flip-vertical {\n  filter: none;\n}\n", "// Mixins\n// --------------------------\n\n@mixin fa-icon() {\n  display: inline-block;\n  font: normal normal normal #{$fa-font-size-base}/#{$fa-line-height-base} FontAwesome; // shortening font declaration\n  font-size: inherit; // can't have font-size inherit on line above, so need to override\n  text-rendering: auto; // optimizelegibility throws things off #1094\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n\n}\n\n@mixin fa-icon-rotate($degrees, $rotation) {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation})\";\n  -webkit-transform: rotate($degrees);\n      -ms-transform: rotate($degrees);\n          transform: rotate($degrees);\n}\n\n@mixin fa-icon-flip($horiz, $vert, $rotation) {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation}, mirror=1)\";\n  -webkit-transform: scale($horiz, $vert);\n      -ms-transform: scale($horiz, $vert);\n          transform: scale($horiz, $vert);\n}\n\n\n// Only display content to screen readers. A la Bootstrap 4.\n//\n// See: http://a11yproject.com/posts/how-to-hide-content/\n\n@mixin sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0,0,0,0);\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n//\n// Useful for \"Skip to main content\" links; see http://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n//\n// Credit: HTML5 Boilerplate\n\n@mixin sr-only-focusable {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    margin: 0;\n    overflow: visible;\n    clip: auto;\n  }\n}\n", "// Stacked Icons\n// -------------------------\n\n.#{$fa-css-prefix}-stack {\n  position: relative;\n  display: inline-block;\n  width: 2em;\n  height: 2em;\n  line-height: 2em;\n  vertical-align: middle;\n}\n.#{$fa-css-prefix}-stack-1x, .#{$fa-css-prefix}-stack-2x {\n  position: absolute;\n  left: 0;\n  width: 100%;\n  text-align: center;\n}\n.#{$fa-css-prefix}-stack-1x { line-height: inherit; }\n.#{$fa-css-prefix}-stack-2x { font-size: 2em; }\n.#{$fa-css-prefix}-inverse { color: $fa-inverse; }\n", "/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen\n   readers do not read off random characters that represent icons */\n\n.#{$fa-css-prefix}-glass:before { content: $fa-var-glass; }\n.#{$fa-css-prefix}-music:before { content: $fa-var-music; }\n.#{$fa-css-prefix}-search:before { content: $fa-var-search; }\n.#{$fa-css-prefix}-envelope-o:before { content: $fa-var-envelope-o; }\n.#{$fa-css-prefix}-heart:before { content: $fa-var-heart; }\n.#{$fa-css-prefix}-star:before { content: $fa-var-star; }\n.#{$fa-css-prefix}-star-o:before { content: $fa-var-star-o; }\n.#{$fa-css-prefix}-user:before { content: $fa-var-user; }\n.#{$fa-css-prefix}-film:before { content: $fa-var-film; }\n.#{$fa-css-prefix}-th-large:before { content: $fa-var-th-large; }\n.#{$fa-css-prefix}-th:before { content: $fa-var-th; }\n.#{$fa-css-prefix}-th-list:before { content: $fa-var-th-list; }\n.#{$fa-css-prefix}-check:before { content: $fa-var-check; }\n.#{$fa-css-prefix}-remove:before,\n.#{$fa-css-prefix}-close:before,\n.#{$fa-css-prefix}-times:before { content: $fa-var-times; }\n.#{$fa-css-prefix}-search-plus:before { content: $fa-var-search-plus; }\n.#{$fa-css-prefix}-search-minus:before { content: $fa-var-search-minus; }\n.#{$fa-css-prefix}-power-off:before { content: $fa-var-power-off; }\n.#{$fa-css-prefix}-signal:before { content: $fa-var-signal; }\n.#{$fa-css-prefix}-gear:before,\n.#{$fa-css-prefix}-cog:before { content: $fa-var-cog; }\n.#{$fa-css-prefix}-trash-o:before { content: $fa-var-trash-o; }\n.#{$fa-css-prefix}-home:before { content: $fa-var-home; }\n.#{$fa-css-prefix}-file-o:before { content: $fa-var-file-o; }\n.#{$fa-css-prefix}-clock-o:before { content: $fa-var-clock-o; }\n.#{$fa-css-prefix}-road:before { content: $fa-var-road; }\n.#{$fa-css-prefix}-download:before { content: $fa-var-download; }\n.#{$fa-css-prefix}-arrow-circle-o-down:before { content: $fa-var-arrow-circle-o-down; }\n.#{$fa-css-prefix}-arrow-circle-o-up:before { content: $fa-var-arrow-circle-o-up; }\n.#{$fa-css-prefix}-inbox:before { content: $fa-var-inbox; }\n.#{$fa-css-prefix}-play-circle-o:before { content: $fa-var-play-circle-o; }\n.#{$fa-css-prefix}-rotate-right:before,\n.#{$fa-css-prefix}-repeat:before { content: $fa-var-repeat; }\n.#{$fa-css-prefix}-refresh:before { content: $fa-var-refresh; }\n.#{$fa-css-prefix}-list-alt:before { content: $fa-var-list-alt; }\n.#{$fa-css-prefix}-lock:before { content: $fa-var-lock; }\n.#{$fa-css-prefix}-flag:before { content: $fa-var-flag; }\n.#{$fa-css-prefix}-headphones:before { content: $fa-var-headphones; }\n.#{$fa-css-prefix}-volume-off:before { content: $fa-var-volume-off; }\n.#{$fa-css-prefix}-volume-down:before { content: $fa-var-volume-down; }\n.#{$fa-css-prefix}-volume-up:before { content: $fa-var-volume-up; }\n.#{$fa-css-prefix}-qrcode:before { content: $fa-var-qrcode; }\n.#{$fa-css-prefix}-barcode:before { content: $fa-var-barcode; }\n.#{$fa-css-prefix}-tag:before { content: $fa-var-tag; }\n.#{$fa-css-prefix}-tags:before { content: $fa-var-tags; }\n.#{$fa-css-prefix}-book:before { content: $fa-var-book; }\n.#{$fa-css-prefix}-bookmark:before { content: $fa-var-bookmark; }\n.#{$fa-css-prefix}-print:before { content: $fa-var-print; }\n.#{$fa-css-prefix}-camera:before { content: $fa-var-camera; }\n.#{$fa-css-prefix}-font:before { content: $fa-var-font; }\n.#{$fa-css-prefix}-bold:before { content: $fa-var-bold; }\n.#{$fa-css-prefix}-italic:before { content: $fa-var-italic; }\n.#{$fa-css-prefix}-text-height:before { content: $fa-var-text-height; }\n.#{$fa-css-prefix}-text-width:before { content: $fa-var-text-width; }\n.#{$fa-css-prefix}-align-left:before { content: $fa-var-align-left; }\n.#{$fa-css-prefix}-align-center:before { content: $fa-var-align-center; }\n.#{$fa-css-prefix}-align-right:before { content: $fa-var-align-right; }\n.#{$fa-css-prefix}-align-justify:before { content: $fa-var-align-justify; }\n.#{$fa-css-prefix}-list:before { content: $fa-var-list; }\n.#{$fa-css-prefix}-dedent:before,\n.#{$fa-css-prefix}-outdent:before { content: $fa-var-outdent; }\n.#{$fa-css-prefix}-indent:before { content: $fa-var-indent; }\n.#{$fa-css-prefix}-video-camera:before { content: $fa-var-video-camera; }\n.#{$fa-css-prefix}-photo:before,\n.#{$fa-css-prefix}-image:before,\n.#{$fa-css-prefix}-picture-o:before { content: $fa-var-picture-o; }\n.#{$fa-css-prefix}-pencil:before { content: $fa-var-pencil; }\n.#{$fa-css-prefix}-map-marker:before { content: $fa-var-map-marker; }\n.#{$fa-css-prefix}-adjust:before { content: $fa-var-adjust; }\n.#{$fa-css-prefix}-tint:before { content: $fa-var-tint; }\n.#{$fa-css-prefix}-edit:before,\n.#{$fa-css-prefix}-pencil-square-o:before { content: $fa-var-pencil-square-o; }\n.#{$fa-css-prefix}-share-square-o:before { content: $fa-var-share-square-o; }\n.#{$fa-css-prefix}-check-square-o:before { content: $fa-var-check-square-o; }\n.#{$fa-css-prefix}-arrows:before { content: $fa-var-arrows; }\n.#{$fa-css-prefix}-step-backward:before { content: $fa-var-step-backward; }\n.#{$fa-css-prefix}-fast-backward:before { content: $fa-var-fast-backward; }\n.#{$fa-css-prefix}-backward:before { content: $fa-var-backward; }\n.#{$fa-css-prefix}-play:before { content: $fa-var-play; }\n.#{$fa-css-prefix}-pause:before { content: $fa-var-pause; }\n.#{$fa-css-prefix}-stop:before { content: $fa-var-stop; }\n.#{$fa-css-prefix}-forward:before { content: $fa-var-forward; }\n.#{$fa-css-prefix}-fast-forward:before { content: $fa-var-fast-forward; }\n.#{$fa-css-prefix}-step-forward:before { content: $fa-var-step-forward; }\n.#{$fa-css-prefix}-eject:before { content: $fa-var-eject; }\n.#{$fa-css-prefix}-chevron-left:before { content: $fa-var-chevron-left; }\n.#{$fa-css-prefix}-chevron-right:before { content: $fa-var-chevron-right; }\n.#{$fa-css-prefix}-plus-circle:before { content: $fa-var-plus-circle; }\n.#{$fa-css-prefix}-minus-circle:before { content: $fa-var-minus-circle; }\n.#{$fa-css-prefix}-times-circle:before { content: $fa-var-times-circle; }\n.#{$fa-css-prefix}-check-circle:before { content: $fa-var-check-circle; }\n.#{$fa-css-prefix}-question-circle:before { content: $fa-var-question-circle; }\n.#{$fa-css-prefix}-info-circle:before { content: $fa-var-info-circle; }\n.#{$fa-css-prefix}-crosshairs:before { content: $fa-var-crosshairs; }\n.#{$fa-css-prefix}-times-circle-o:before { content: $fa-var-times-circle-o; }\n.#{$fa-css-prefix}-check-circle-o:before { content: $fa-var-check-circle-o; }\n.#{$fa-css-prefix}-ban:before { content: $fa-var-ban; }\n.#{$fa-css-prefix}-arrow-left:before { content: $fa-var-arrow-left; }\n.#{$fa-css-prefix}-arrow-right:before { content: $fa-var-arrow-right; }\n.#{$fa-css-prefix}-arrow-up:before { content: $fa-var-arrow-up; }\n.#{$fa-css-prefix}-arrow-down:before { content: $fa-var-arrow-down; }\n.#{$fa-css-prefix}-mail-forward:before,\n.#{$fa-css-prefix}-share:before { content: $fa-var-share; }\n.#{$fa-css-prefix}-expand:before { content: $fa-var-expand; }\n.#{$fa-css-prefix}-compress:before { content: $fa-var-compress; }\n.#{$fa-css-prefix}-plus:before { content: $fa-var-plus; }\n.#{$fa-css-prefix}-minus:before { content: $fa-var-minus; }\n.#{$fa-css-prefix}-asterisk:before { content: $fa-var-asterisk; }\n.#{$fa-css-prefix}-exclamation-circle:before { content: $fa-var-exclamation-circle; }\n.#{$fa-css-prefix}-gift:before { content: $fa-var-gift; }\n.#{$fa-css-prefix}-leaf:before { content: $fa-var-leaf; }\n.#{$fa-css-prefix}-fire:before { content: $fa-var-fire; }\n.#{$fa-css-prefix}-eye:before { content: $fa-var-eye; }\n.#{$fa-css-prefix}-eye-slash:before { content: $fa-var-eye-slash; }\n.#{$fa-css-prefix}-warning:before,\n.#{$fa-css-prefix}-exclamation-triangle:before { content: $fa-var-exclamation-triangle; }\n.#{$fa-css-prefix}-plane:before { content: $fa-var-plane; }\n.#{$fa-css-prefix}-calendar:before { content: $fa-var-calendar; }\n.#{$fa-css-prefix}-random:before { content: $fa-var-random; }\n.#{$fa-css-prefix}-comment:before { content: $fa-var-comment; }\n.#{$fa-css-prefix}-magnet:before { content: $fa-var-magnet; }\n.#{$fa-css-prefix}-chevron-up:before { content: $fa-var-chevron-up; }\n.#{$fa-css-prefix}-chevron-down:before { content: $fa-var-chevron-down; }\n.#{$fa-css-prefix}-retweet:before { content: $fa-var-retweet; }\n.#{$fa-css-prefix}-shopping-cart:before { content: $fa-var-shopping-cart; }\n.#{$fa-css-prefix}-folder:before { content: $fa-var-folder; }\n.#{$fa-css-prefix}-folder-open:before { content: $fa-var-folder-open; }\n.#{$fa-css-prefix}-arrows-v:before { content: $fa-var-arrows-v; }\n.#{$fa-css-prefix}-arrows-h:before { content: $fa-var-arrows-h; }\n.#{$fa-css-prefix}-bar-chart-o:before,\n.#{$fa-css-prefix}-bar-chart:before { content: $fa-var-bar-chart; }\n.#{$fa-css-prefix}-twitter-square:before { content: $fa-var-twitter-square; }\n.#{$fa-css-prefix}-facebook-square:before { content: $fa-var-facebook-square; }\n.#{$fa-css-prefix}-camera-retro:before { content: $fa-var-camera-retro; }\n.#{$fa-css-prefix}-key:before { content: $fa-var-key; }\n.#{$fa-css-prefix}-gears:before,\n.#{$fa-css-prefix}-cogs:before { content: $fa-var-cogs; }\n.#{$fa-css-prefix}-comments:before { content: $fa-var-comments; }\n.#{$fa-css-prefix}-thumbs-o-up:before { content: $fa-var-thumbs-o-up; }\n.#{$fa-css-prefix}-thumbs-o-down:before { content: $fa-var-thumbs-o-down; }\n.#{$fa-css-prefix}-star-half:before { content: $fa-var-star-half; }\n.#{$fa-css-prefix}-heart-o:before { content: $fa-var-heart-o; }\n.#{$fa-css-prefix}-sign-out:before { content: $fa-var-sign-out; }\n.#{$fa-css-prefix}-linkedin-square:before { content: $fa-var-linkedin-square; }\n.#{$fa-css-prefix}-thumb-tack:before { content: $fa-var-thumb-tack; }\n.#{$fa-css-prefix}-external-link:before { content: $fa-var-external-link; }\n.#{$fa-css-prefix}-sign-in:before { content: $fa-var-sign-in; }\n.#{$fa-css-prefix}-trophy:before { content: $fa-var-trophy; }\n.#{$fa-css-prefix}-github-square:before { content: $fa-var-github-square; }\n.#{$fa-css-prefix}-upload:before { content: $fa-var-upload; }\n.#{$fa-css-prefix}-lemon-o:before { content: $fa-var-lemon-o; }\n.#{$fa-css-prefix}-phone:before { content: $fa-var-phone; }\n.#{$fa-css-prefix}-square-o:before { content: $fa-var-square-o; }\n.#{$fa-css-prefix}-bookmark-o:before { content: $fa-var-bookmark-o; }\n.#{$fa-css-prefix}-phone-square:before { content: $fa-var-phone-square; }\n.#{$fa-css-prefix}-twitter:before { content: $fa-var-twitter; }\n.#{$fa-css-prefix}-facebook-f:before,\n.#{$fa-css-prefix}-facebook:before { content: $fa-var-facebook; }\n.#{$fa-css-prefix}-github:before { content: $fa-var-github; }\n.#{$fa-css-prefix}-unlock:before { content: $fa-var-unlock; }\n.#{$fa-css-prefix}-credit-card:before { content: $fa-var-credit-card; }\n.#{$fa-css-prefix}-feed:before,\n.#{$fa-css-prefix}-rss:before { content: $fa-var-rss; }\n.#{$fa-css-prefix}-hdd-o:before { content: $fa-var-hdd-o; }\n.#{$fa-css-prefix}-bullhorn:before { content: $fa-var-bullhorn; }\n.#{$fa-css-prefix}-bell:before { content: $fa-var-bell; }\n.#{$fa-css-prefix}-certificate:before { content: $fa-var-certificate; }\n.#{$fa-css-prefix}-hand-o-right:before { content: $fa-var-hand-o-right; }\n.#{$fa-css-prefix}-hand-o-left:before { content: $fa-var-hand-o-left; }\n.#{$fa-css-prefix}-hand-o-up:before { content: $fa-var-hand-o-up; }\n.#{$fa-css-prefix}-hand-o-down:before { content: $fa-var-hand-o-down; }\n.#{$fa-css-prefix}-arrow-circle-left:before { content: $fa-var-arrow-circle-left; }\n.#{$fa-css-prefix}-arrow-circle-right:before { content: $fa-var-arrow-circle-right; }\n.#{$fa-css-prefix}-arrow-circle-up:before { content: $fa-var-arrow-circle-up; }\n.#{$fa-css-prefix}-arrow-circle-down:before { content: $fa-var-arrow-circle-down; }\n.#{$fa-css-prefix}-globe:before { content: $fa-var-globe; }\n.#{$fa-css-prefix}-wrench:before { content: $fa-var-wrench; }\n.#{$fa-css-prefix}-tasks:before { content: $fa-var-tasks; }\n.#{$fa-css-prefix}-filter:before { content: $fa-var-filter; }\n.#{$fa-css-prefix}-briefcase:before { content: $fa-var-briefcase; }\n.#{$fa-css-prefix}-arrows-alt:before { content: $fa-var-arrows-alt; }\n.#{$fa-css-prefix}-group:before,\n.#{$fa-css-prefix}-users:before { content: $fa-var-users; }\n.#{$fa-css-prefix}-chain:before,\n.#{$fa-css-prefix}-link:before { content: $fa-var-link; }\n.#{$fa-css-prefix}-cloud:before { content: $fa-var-cloud; }\n.#{$fa-css-prefix}-flask:before { content: $fa-var-flask; }\n.#{$fa-css-prefix}-cut:before,\n.#{$fa-css-prefix}-scissors:before { content: $fa-var-scissors; }\n.#{$fa-css-prefix}-copy:before,\n.#{$fa-css-prefix}-files-o:before { content: $fa-var-files-o; }\n.#{$fa-css-prefix}-paperclip:before { content: $fa-var-paperclip; }\n.#{$fa-css-prefix}-save:before,\n.#{$fa-css-prefix}-floppy-o:before { content: $fa-var-floppy-o; }\n.#{$fa-css-prefix}-square:before { content: $fa-var-square; }\n.#{$fa-css-prefix}-navicon:before,\n.#{$fa-css-prefix}-reorder:before,\n.#{$fa-css-prefix}-bars:before { content: $fa-var-bars; }\n.#{$fa-css-prefix}-list-ul:before { content: $fa-var-list-ul; }\n.#{$fa-css-prefix}-list-ol:before { content: $fa-var-list-ol; }\n.#{$fa-css-prefix}-strikethrough:before { content: $fa-var-strikethrough; }\n.#{$fa-css-prefix}-underline:before { content: $fa-var-underline; }\n.#{$fa-css-prefix}-table:before { content: $fa-var-table; }\n.#{$fa-css-prefix}-magic:before { content: $fa-var-magic; }\n.#{$fa-css-prefix}-truck:before { content: $fa-var-truck; }\n.#{$fa-css-prefix}-pinterest:before { content: $fa-var-pinterest; }\n.#{$fa-css-prefix}-pinterest-square:before { content: $fa-var-pinterest-square; }\n.#{$fa-css-prefix}-google-plus-square:before { content: $fa-var-google-plus-square; }\n.#{$fa-css-prefix}-google-plus:before { content: $fa-var-google-plus; }\n.#{$fa-css-prefix}-money:before { content: $fa-var-money; }\n.#{$fa-css-prefix}-caret-down:before { content: $fa-var-caret-down; }\n.#{$fa-css-prefix}-caret-up:before { content: $fa-var-caret-up; }\n.#{$fa-css-prefix}-caret-left:before { content: $fa-var-caret-left; }\n.#{$fa-css-prefix}-caret-right:before { content: $fa-var-caret-right; }\n.#{$fa-css-prefix}-columns:before { content: $fa-var-columns; }\n.#{$fa-css-prefix}-unsorted:before,\n.#{$fa-css-prefix}-sort:before { content: $fa-var-sort; }\n.#{$fa-css-prefix}-sort-down:before,\n.#{$fa-css-prefix}-sort-desc:before { content: $fa-var-sort-desc; }\n.#{$fa-css-prefix}-sort-up:before,\n.#{$fa-css-prefix}-sort-asc:before { content: $fa-var-sort-asc; }\n.#{$fa-css-prefix}-envelope:before { content: $fa-var-envelope; }\n.#{$fa-css-prefix}-linkedin:before { content: $fa-var-linkedin; }\n.#{$fa-css-prefix}-rotate-left:before,\n.#{$fa-css-prefix}-undo:before { content: $fa-var-undo; }\n.#{$fa-css-prefix}-legal:before,\n.#{$fa-css-prefix}-gavel:before { content: $fa-var-gavel; }\n.#{$fa-css-prefix}-dashboard:before,\n.#{$fa-css-prefix}-tachometer:before { content: $fa-var-tachometer; }\n.#{$fa-css-prefix}-comment-o:before { content: $fa-var-comment-o; }\n.#{$fa-css-prefix}-comments-o:before { content: $fa-var-comments-o; }\n.#{$fa-css-prefix}-flash:before,\n.#{$fa-css-prefix}-bolt:before { content: $fa-var-bolt; }\n.#{$fa-css-prefix}-sitemap:before { content: $fa-var-sitemap; }\n.#{$fa-css-prefix}-umbrella:before { content: $fa-var-umbrella; }\n.#{$fa-css-prefix}-paste:before,\n.#{$fa-css-prefix}-clipboard:before { content: $fa-var-clipboard; }\n.#{$fa-css-prefix}-lightbulb-o:before { content: $fa-var-lightbulb-o; }\n.#{$fa-css-prefix}-exchange:before { content: $fa-var-exchange; }\n.#{$fa-css-prefix}-cloud-download:before { content: $fa-var-cloud-download; }\n.#{$fa-css-prefix}-cloud-upload:before { content: $fa-var-cloud-upload; }\n.#{$fa-css-prefix}-user-md:before { content: $fa-var-user-md; }\n.#{$fa-css-prefix}-stethoscope:before { content: $fa-var-stethoscope; }\n.#{$fa-css-prefix}-suitcase:before { content: $fa-var-suitcase; }\n.#{$fa-css-prefix}-bell-o:before { content: $fa-var-bell-o; }\n.#{$fa-css-prefix}-coffee:before { content: $fa-var-coffee; }\n.#{$fa-css-prefix}-cutlery:before { content: $fa-var-cutlery; }\n.#{$fa-css-prefix}-file-text-o:before { content: $fa-var-file-text-o; }\n.#{$fa-css-prefix}-building-o:before { content: $fa-var-building-o; }\n.#{$fa-css-prefix}-hospital-o:before { content: $fa-var-hospital-o; }\n.#{$fa-css-prefix}-ambulance:before { content: $fa-var-ambulance; }\n.#{$fa-css-prefix}-medkit:before { content: $fa-var-medkit; }\n.#{$fa-css-prefix}-fighter-jet:before { content: $fa-var-fighter-jet; }\n.#{$fa-css-prefix}-beer:before { content: $fa-var-beer; }\n.#{$fa-css-prefix}-h-square:before { content: $fa-var-h-square; }\n.#{$fa-css-prefix}-plus-square:before { content: $fa-var-plus-square; }\n.#{$fa-css-prefix}-angle-double-left:before { content: $fa-var-angle-double-left; }\n.#{$fa-css-prefix}-angle-double-right:before { content: $fa-var-angle-double-right; }\n.#{$fa-css-prefix}-angle-double-up:before { content: $fa-var-angle-double-up; }\n.#{$fa-css-prefix}-angle-double-down:before { content: $fa-var-angle-double-down; }\n.#{$fa-css-prefix}-angle-left:before { content: $fa-var-angle-left; }\n.#{$fa-css-prefix}-angle-right:before { content: $fa-var-angle-right; }\n.#{$fa-css-prefix}-angle-up:before { content: $fa-var-angle-up; }\n.#{$fa-css-prefix}-angle-down:before { content: $fa-var-angle-down; }\n.#{$fa-css-prefix}-desktop:before { content: $fa-var-desktop; }\n.#{$fa-css-prefix}-laptop:before { content: $fa-var-laptop; }\n.#{$fa-css-prefix}-tablet:before { content: $fa-var-tablet; }\n.#{$fa-css-prefix}-mobile-phone:before,\n.#{$fa-css-prefix}-mobile:before { content: $fa-var-mobile; }\n.#{$fa-css-prefix}-circle-o:before { content: $fa-var-circle-o; }\n.#{$fa-css-prefix}-quote-left:before { content: $fa-var-quote-left; }\n.#{$fa-css-prefix}-quote-right:before { content: $fa-var-quote-right; }\n.#{$fa-css-prefix}-spinner:before { content: $fa-var-spinner; }\n.#{$fa-css-prefix}-circle:before { content: $fa-var-circle; }\n.#{$fa-css-prefix}-mail-reply:before,\n.#{$fa-css-prefix}-reply:before { content: $fa-var-reply; }\n.#{$fa-css-prefix}-github-alt:before { content: $fa-var-github-alt; }\n.#{$fa-css-prefix}-folder-o:before { content: $fa-var-folder-o; }\n.#{$fa-css-prefix}-folder-open-o:before { content: $fa-var-folder-open-o; }\n.#{$fa-css-prefix}-smile-o:before { content: $fa-var-smile-o; }\n.#{$fa-css-prefix}-frown-o:before { content: $fa-var-frown-o; }\n.#{$fa-css-prefix}-meh-o:before { content: $fa-var-meh-o; }\n.#{$fa-css-prefix}-gamepad:before { content: $fa-var-gamepad; }\n.#{$fa-css-prefix}-keyboard-o:before { content: $fa-var-keyboard-o; }\n.#{$fa-css-prefix}-flag-o:before { content: $fa-var-flag-o; }\n.#{$fa-css-prefix}-flag-checkered:before { content: $fa-var-flag-checkered; }\n.#{$fa-css-prefix}-terminal:before { content: $fa-var-terminal; }\n.#{$fa-css-prefix}-code:before { content: $fa-var-code; }\n.#{$fa-css-prefix}-mail-reply-all:before,\n.#{$fa-css-prefix}-reply-all:before { content: $fa-var-reply-all; }\n.#{$fa-css-prefix}-star-half-empty:before,\n.#{$fa-css-prefix}-star-half-full:before,\n.#{$fa-css-prefix}-star-half-o:before { content: $fa-var-star-half-o; }\n.#{$fa-css-prefix}-location-arrow:before { content: $fa-var-location-arrow; }\n.#{$fa-css-prefix}-crop:before { content: $fa-var-crop; }\n.#{$fa-css-prefix}-code-fork:before { content: $fa-var-code-fork; }\n.#{$fa-css-prefix}-unlink:before,\n.#{$fa-css-prefix}-chain-broken:before { content: $fa-var-chain-broken; }\n.#{$fa-css-prefix}-question:before { content: $fa-var-question; }\n.#{$fa-css-prefix}-info:before { content: $fa-var-info; }\n.#{$fa-css-prefix}-exclamation:before { content: $fa-var-exclamation; }\n.#{$fa-css-prefix}-superscript:before { content: $fa-var-superscript; }\n.#{$fa-css-prefix}-subscript:before { content: $fa-var-subscript; }\n.#{$fa-css-prefix}-eraser:before { content: $fa-var-eraser; }\n.#{$fa-css-prefix}-puzzle-piece:before { content: $fa-var-puzzle-piece; }\n.#{$fa-css-prefix}-microphone:before { content: $fa-var-microphone; }\n.#{$fa-css-prefix}-microphone-slash:before { content: $fa-var-microphone-slash; }\n.#{$fa-css-prefix}-shield:before { content: $fa-var-shield; }\n.#{$fa-css-prefix}-calendar-o:before { content: $fa-var-calendar-o; }\n.#{$fa-css-prefix}-fire-extinguisher:before { content: $fa-var-fire-extinguisher; }\n.#{$fa-css-prefix}-rocket:before { content: $fa-var-rocket; }\n.#{$fa-css-prefix}-maxcdn:before { content: $fa-var-maxcdn; }\n.#{$fa-css-prefix}-chevron-circle-left:before { content: $fa-var-chevron-circle-left; }\n.#{$fa-css-prefix}-chevron-circle-right:before { content: $fa-var-chevron-circle-right; }\n.#{$fa-css-prefix}-chevron-circle-up:before { content: $fa-var-chevron-circle-up; }\n.#{$fa-css-prefix}-chevron-circle-down:before { content: $fa-var-chevron-circle-down; }\n.#{$fa-css-prefix}-html5:before { content: $fa-var-html5; }\n.#{$fa-css-prefix}-css3:before { content: $fa-var-css3; }\n.#{$fa-css-prefix}-anchor:before { content: $fa-var-anchor; }\n.#{$fa-css-prefix}-unlock-alt:before { content: $fa-var-unlock-alt; }\n.#{$fa-css-prefix}-bullseye:before { content: $fa-var-bullseye; }\n.#{$fa-css-prefix}-ellipsis-h:before { content: $fa-var-ellipsis-h; }\n.#{$fa-css-prefix}-ellipsis-v:before { content: $fa-var-ellipsis-v; }\n.#{$fa-css-prefix}-rss-square:before { content: $fa-var-rss-square; }\n.#{$fa-css-prefix}-play-circle:before { content: $fa-var-play-circle; }\n.#{$fa-css-prefix}-ticket:before { content: $fa-var-ticket; }\n.#{$fa-css-prefix}-minus-square:before { content: $fa-var-minus-square; }\n.#{$fa-css-prefix}-minus-square-o:before { content: $fa-var-minus-square-o; }\n.#{$fa-css-prefix}-level-up:before { content: $fa-var-level-up; }\n.#{$fa-css-prefix}-level-down:before { content: $fa-var-level-down; }\n.#{$fa-css-prefix}-check-square:before { content: $fa-var-check-square; }\n.#{$fa-css-prefix}-pencil-square:before { content: $fa-var-pencil-square; }\n.#{$fa-css-prefix}-external-link-square:before { content: $fa-var-external-link-square; }\n.#{$fa-css-prefix}-share-square:before { content: $fa-var-share-square; }\n.#{$fa-css-prefix}-compass:before { content: $fa-var-compass; }\n.#{$fa-css-prefix}-toggle-down:before,\n.#{$fa-css-prefix}-caret-square-o-down:before { content: $fa-var-caret-square-o-down; }\n.#{$fa-css-prefix}-toggle-up:before,\n.#{$fa-css-prefix}-caret-square-o-up:before { content: $fa-var-caret-square-o-up; }\n.#{$fa-css-prefix}-toggle-right:before,\n.#{$fa-css-prefix}-caret-square-o-right:before { content: $fa-var-caret-square-o-right; }\n.#{$fa-css-prefix}-euro:before,\n.#{$fa-css-prefix}-eur:before { content: $fa-var-eur; }\n.#{$fa-css-prefix}-gbp:before { content: $fa-var-gbp; }\n.#{$fa-css-prefix}-dollar:before,\n.#{$fa-css-prefix}-usd:before { content: $fa-var-usd; }\n.#{$fa-css-prefix}-rupee:before,\n.#{$fa-css-prefix}-inr:before { content: $fa-var-inr; }\n.#{$fa-css-prefix}-cny:before,\n.#{$fa-css-prefix}-rmb:before,\n.#{$fa-css-prefix}-yen:before,\n.#{$fa-css-prefix}-jpy:before { content: $fa-var-jpy; }\n.#{$fa-css-prefix}-ruble:before,\n.#{$fa-css-prefix}-rouble:before,\n.#{$fa-css-prefix}-rub:before { content: $fa-var-rub; }\n.#{$fa-css-prefix}-won:before,\n.#{$fa-css-prefix}-krw:before { content: $fa-var-krw; }\n.#{$fa-css-prefix}-bitcoin:before,\n.#{$fa-css-prefix}-btc:before { content: $fa-var-btc; }\n.#{$fa-css-prefix}-file:before { content: $fa-var-file; }\n.#{$fa-css-prefix}-file-text:before { content: $fa-var-file-text; }\n.#{$fa-css-prefix}-sort-alpha-asc:before { content: $fa-var-sort-alpha-asc; }\n.#{$fa-css-prefix}-sort-alpha-desc:before { content: $fa-var-sort-alpha-desc; }\n.#{$fa-css-prefix}-sort-amount-asc:before { content: $fa-var-sort-amount-asc; }\n.#{$fa-css-prefix}-sort-amount-desc:before { content: $fa-var-sort-amount-desc; }\n.#{$fa-css-prefix}-sort-numeric-asc:before { content: $fa-var-sort-numeric-asc; }\n.#{$fa-css-prefix}-sort-numeric-desc:before { content: $fa-var-sort-numeric-desc; }\n.#{$fa-css-prefix}-thumbs-up:before { content: $fa-var-thumbs-up; }\n.#{$fa-css-prefix}-thumbs-down:before { content: $fa-var-thumbs-down; }\n.#{$fa-css-prefix}-youtube-square:before { content: $fa-var-youtube-square; }\n.#{$fa-css-prefix}-youtube:before { content: $fa-var-youtube; }\n.#{$fa-css-prefix}-xing:before { content: $fa-var-xing; }\n.#{$fa-css-prefix}-xing-square:before { content: $fa-var-xing-square; }\n.#{$fa-css-prefix}-youtube-play:before { content: $fa-var-youtube-play; }\n.#{$fa-css-prefix}-dropbox:before { content: $fa-var-dropbox; }\n.#{$fa-css-prefix}-stack-overflow:before { content: $fa-var-stack-overflow; }\n.#{$fa-css-prefix}-instagram:before { content: $fa-var-instagram; }\n.#{$fa-css-prefix}-flickr:before { content: $fa-var-flickr; }\n.#{$fa-css-prefix}-adn:before { content: $fa-var-adn; }\n.#{$fa-css-prefix}-bitbucket:before { content: $fa-var-bitbucket; }\n.#{$fa-css-prefix}-bitbucket-square:before { content: $fa-var-bitbucket-square; }\n.#{$fa-css-prefix}-tumblr:before { content: $fa-var-tumblr; }\n.#{$fa-css-prefix}-tumblr-square:before { content: $fa-var-tumblr-square; }\n.#{$fa-css-prefix}-long-arrow-down:before { content: $fa-var-long-arrow-down; }\n.#{$fa-css-prefix}-long-arrow-up:before { content: $fa-var-long-arrow-up; }\n.#{$fa-css-prefix}-long-arrow-left:before { content: $fa-var-long-arrow-left; }\n.#{$fa-css-prefix}-long-arrow-right:before { content: $fa-var-long-arrow-right; }\n.#{$fa-css-prefix}-apple:before { content: $fa-var-apple; }\n.#{$fa-css-prefix}-windows:before { content: $fa-var-windows; }\n.#{$fa-css-prefix}-android:before { content: $fa-var-android; }\n.#{$fa-css-prefix}-linux:before { content: $fa-var-linux; }\n.#{$fa-css-prefix}-dribbble:before { content: $fa-var-dribbble; }\n.#{$fa-css-prefix}-skype:before { content: $fa-var-skype; }\n.#{$fa-css-prefix}-foursquare:before { content: $fa-var-foursquare; }\n.#{$fa-css-prefix}-trello:before { content: $fa-var-trello; }\n.#{$fa-css-prefix}-female:before { content: $fa-var-female; }\n.#{$fa-css-prefix}-male:before { content: $fa-var-male; }\n.#{$fa-css-prefix}-gittip:before,\n.#{$fa-css-prefix}-gratipay:before { content: $fa-var-gratipay; }\n.#{$fa-css-prefix}-sun-o:before { content: $fa-var-sun-o; }\n.#{$fa-css-prefix}-moon-o:before { content: $fa-var-moon-o; }\n.#{$fa-css-prefix}-archive:before { content: $fa-var-archive; }\n.#{$fa-css-prefix}-bug:before { content: $fa-var-bug; }\n.#{$fa-css-prefix}-vk:before { content: $fa-var-vk; }\n.#{$fa-css-prefix}-weibo:before { content: $fa-var-weibo; }\n.#{$fa-css-prefix}-renren:before { content: $fa-var-renren; }\n.#{$fa-css-prefix}-pagelines:before { content: $fa-var-pagelines; }\n.#{$fa-css-prefix}-stack-exchange:before { content: $fa-var-stack-exchange; }\n.#{$fa-css-prefix}-arrow-circle-o-right:before { content: $fa-var-arrow-circle-o-right; }\n.#{$fa-css-prefix}-arrow-circle-o-left:before { content: $fa-var-arrow-circle-o-left; }\n.#{$fa-css-prefix}-toggle-left:before,\n.#{$fa-css-prefix}-caret-square-o-left:before { content: $fa-var-caret-square-o-left; }\n.#{$fa-css-prefix}-dot-circle-o:before { content: $fa-var-dot-circle-o; }\n.#{$fa-css-prefix}-wheelchair:before { content: $fa-var-wheelchair; }\n.#{$fa-css-prefix}-vimeo-square:before { content: $fa-var-vimeo-square; }\n.#{$fa-css-prefix}-turkish-lira:before,\n.#{$fa-css-prefix}-try:before { content: $fa-var-try; }\n.#{$fa-css-prefix}-plus-square-o:before { content: $fa-var-plus-square-o; }\n.#{$fa-css-prefix}-space-shuttle:before { content: $fa-var-space-shuttle; }\n.#{$fa-css-prefix}-slack:before { content: $fa-var-slack; }\n.#{$fa-css-prefix}-envelope-square:before { content: $fa-var-envelope-square; }\n.#{$fa-css-prefix}-wordpress:before { content: $fa-var-wordpress; }\n.#{$fa-css-prefix}-openid:before { content: $fa-var-openid; }\n.#{$fa-css-prefix}-institution:before,\n.#{$fa-css-prefix}-bank:before,\n.#{$fa-css-prefix}-university:before { content: $fa-var-university; }\n.#{$fa-css-prefix}-mortar-board:before,\n.#{$fa-css-prefix}-graduation-cap:before { content: $fa-var-graduation-cap; }\n.#{$fa-css-prefix}-yahoo:before { content: $fa-var-yahoo; }\n.#{$fa-css-prefix}-google:before { content: $fa-var-google; }\n.#{$fa-css-prefix}-reddit:before { content: $fa-var-reddit; }\n.#{$fa-css-prefix}-reddit-square:before { content: $fa-var-reddit-square; }\n.#{$fa-css-prefix}-stumbleupon-circle:before { content: $fa-var-stumbleupon-circle; }\n.#{$fa-css-prefix}-stumbleupon:before { content: $fa-var-stumbleupon; }\n.#{$fa-css-prefix}-delicious:before { content: $fa-var-delicious; }\n.#{$fa-css-prefix}-digg:before { content: $fa-var-digg; }\n.#{$fa-css-prefix}-pied-piper-pp:before { content: $fa-var-pied-piper-pp; }\n.#{$fa-css-prefix}-pied-piper-alt:before { content: $fa-var-pied-piper-alt; }\n.#{$fa-css-prefix}-drupal:before { content: $fa-var-drupal; }\n.#{$fa-css-prefix}-joomla:before { content: $fa-var-joomla; }\n.#{$fa-css-prefix}-language:before { content: $fa-var-language; }\n.#{$fa-css-prefix}-fax:before { content: $fa-var-fax; }\n.#{$fa-css-prefix}-building:before { content: $fa-var-building; }\n.#{$fa-css-prefix}-child:before { content: $fa-var-child; }\n.#{$fa-css-prefix}-paw:before { content: $fa-var-paw; }\n.#{$fa-css-prefix}-spoon:before { content: $fa-var-spoon; }\n.#{$fa-css-prefix}-cube:before { content: $fa-var-cube; }\n.#{$fa-css-prefix}-cubes:before { content: $fa-var-cubes; }\n.#{$fa-css-prefix}-behance:before { content: $fa-var-behance; }\n.#{$fa-css-prefix}-behance-square:before { content: $fa-var-behance-square; }\n.#{$fa-css-prefix}-steam:before { content: $fa-var-steam; }\n.#{$fa-css-prefix}-steam-square:before { content: $fa-var-steam-square; }\n.#{$fa-css-prefix}-recycle:before { content: $fa-var-recycle; }\n.#{$fa-css-prefix}-automobile:before,\n.#{$fa-css-prefix}-car:before { content: $fa-var-car; }\n.#{$fa-css-prefix}-cab:before,\n.#{$fa-css-prefix}-taxi:before { content: $fa-var-taxi; }\n.#{$fa-css-prefix}-tree:before { content: $fa-var-tree; }\n.#{$fa-css-prefix}-spotify:before { content: $fa-var-spotify; }\n.#{$fa-css-prefix}-deviantart:before { content: $fa-var-deviantart; }\n.#{$fa-css-prefix}-soundcloud:before { content: $fa-var-soundcloud; }\n.#{$fa-css-prefix}-database:before { content: $fa-var-database; }\n.#{$fa-css-prefix}-file-pdf-o:before { content: $fa-var-file-pdf-o; }\n.#{$fa-css-prefix}-file-word-o:before { content: $fa-var-file-word-o; }\n.#{$fa-css-prefix}-file-excel-o:before { content: $fa-var-file-excel-o; }\n.#{$fa-css-prefix}-file-powerpoint-o:before { content: $fa-var-file-powerpoint-o; }\n.#{$fa-css-prefix}-file-photo-o:before,\n.#{$fa-css-prefix}-file-picture-o:before,\n.#{$fa-css-prefix}-file-image-o:before { content: $fa-var-file-image-o; }\n.#{$fa-css-prefix}-file-zip-o:before,\n.#{$fa-css-prefix}-file-archive-o:before { content: $fa-var-file-archive-o; }\n.#{$fa-css-prefix}-file-sound-o:before,\n.#{$fa-css-prefix}-file-audio-o:before { content: $fa-var-file-audio-o; }\n.#{$fa-css-prefix}-file-movie-o:before,\n.#{$fa-css-prefix}-file-video-o:before { content: $fa-var-file-video-o; }\n.#{$fa-css-prefix}-file-code-o:before { content: $fa-var-file-code-o; }\n.#{$fa-css-prefix}-vine:before { content: $fa-var-vine; }\n.#{$fa-css-prefix}-codepen:before { content: $fa-var-codepen; }\n.#{$fa-css-prefix}-jsfiddle:before { content: $fa-var-jsfiddle; }\n.#{$fa-css-prefix}-life-bouy:before,\n.#{$fa-css-prefix}-life-buoy:before,\n.#{$fa-css-prefix}-life-saver:before,\n.#{$fa-css-prefix}-support:before,\n.#{$fa-css-prefix}-life-ring:before { content: $fa-var-life-ring; }\n.#{$fa-css-prefix}-circle-o-notch:before { content: $fa-var-circle-o-notch; }\n.#{$fa-css-prefix}-ra:before,\n.#{$fa-css-prefix}-resistance:before,\n.#{$fa-css-prefix}-rebel:before { content: $fa-var-rebel; }\n.#{$fa-css-prefix}-ge:before,\n.#{$fa-css-prefix}-empire:before { content: $fa-var-empire; }\n.#{$fa-css-prefix}-git-square:before { content: $fa-var-git-square; }\n.#{$fa-css-prefix}-git:before { content: $fa-var-git; }\n.#{$fa-css-prefix}-y-combinator-square:before,\n.#{$fa-css-prefix}-yc-square:before,\n.#{$fa-css-prefix}-hacker-news:before { content: $fa-var-hacker-news; }\n.#{$fa-css-prefix}-tencent-weibo:before { content: $fa-var-tencent-weibo; }\n.#{$fa-css-prefix}-qq:before { content: $fa-var-qq; }\n.#{$fa-css-prefix}-wechat:before,\n.#{$fa-css-prefix}-weixin:before { content: $fa-var-weixin; }\n.#{$fa-css-prefix}-send:before,\n.#{$fa-css-prefix}-paper-plane:before { content: $fa-var-paper-plane; }\n.#{$fa-css-prefix}-send-o:before,\n.#{$fa-css-prefix}-paper-plane-o:before { content: $fa-var-paper-plane-o; }\n.#{$fa-css-prefix}-history:before { content: $fa-var-history; }\n.#{$fa-css-prefix}-circle-thin:before { content: $fa-var-circle-thin; }\n.#{$fa-css-prefix}-header:before { content: $fa-var-header; }\n.#{$fa-css-prefix}-paragraph:before { content: $fa-var-paragraph; }\n.#{$fa-css-prefix}-sliders:before { content: $fa-var-sliders; }\n.#{$fa-css-prefix}-share-alt:before { content: $fa-var-share-alt; }\n.#{$fa-css-prefix}-share-alt-square:before { content: $fa-var-share-alt-square; }\n.#{$fa-css-prefix}-bomb:before { content: $fa-var-bomb; }\n.#{$fa-css-prefix}-soccer-ball-o:before,\n.#{$fa-css-prefix}-futbol-o:before { content: $fa-var-futbol-o; }\n.#{$fa-css-prefix}-tty:before { content: $fa-var-tty; }\n.#{$fa-css-prefix}-binoculars:before { content: $fa-var-binoculars; }\n.#{$fa-css-prefix}-plug:before { content: $fa-var-plug; }\n.#{$fa-css-prefix}-slideshare:before { content: $fa-var-slideshare; }\n.#{$fa-css-prefix}-twitch:before { content: $fa-var-twitch; }\n.#{$fa-css-prefix}-yelp:before { content: $fa-var-yelp; }\n.#{$fa-css-prefix}-newspaper-o:before { content: $fa-var-newspaper-o; }\n.#{$fa-css-prefix}-wifi:before { content: $fa-var-wifi; }\n.#{$fa-css-prefix}-calculator:before { content: $fa-var-calculator; }\n.#{$fa-css-prefix}-paypal:before { content: $fa-var-paypal; }\n.#{$fa-css-prefix}-google-wallet:before { content: $fa-var-google-wallet; }\n.#{$fa-css-prefix}-cc-visa:before { content: $fa-var-cc-visa; }\n.#{$fa-css-prefix}-cc-mastercard:before { content: $fa-var-cc-mastercard; }\n.#{$fa-css-prefix}-cc-discover:before { content: $fa-var-cc-discover; }\n.#{$fa-css-prefix}-cc-amex:before { content: $fa-var-cc-amex; }\n.#{$fa-css-prefix}-cc-paypal:before { content: $fa-var-cc-paypal; }\n.#{$fa-css-prefix}-cc-stripe:before { content: $fa-var-cc-stripe; }\n.#{$fa-css-prefix}-bell-slash:before { content: $fa-var-bell-slash; }\n.#{$fa-css-prefix}-bell-slash-o:before { content: $fa-var-bell-slash-o; }\n.#{$fa-css-prefix}-trash:before { content: $fa-var-trash; }\n.#{$fa-css-prefix}-copyright:before { content: $fa-var-copyright; }\n.#{$fa-css-prefix}-at:before { content: $fa-var-at; }\n.#{$fa-css-prefix}-eyedropper:before { content: $fa-var-eyedropper; }\n.#{$fa-css-prefix}-paint-brush:before { content: $fa-var-paint-brush; }\n.#{$fa-css-prefix}-birthday-cake:before { content: $fa-var-birthday-cake; }\n.#{$fa-css-prefix}-area-chart:before { content: $fa-var-area-chart; }\n.#{$fa-css-prefix}-pie-chart:before { content: $fa-var-pie-chart; }\n.#{$fa-css-prefix}-line-chart:before { content: $fa-var-line-chart; }\n.#{$fa-css-prefix}-lastfm:before { content: $fa-var-lastfm; }\n.#{$fa-css-prefix}-lastfm-square:before { content: $fa-var-lastfm-square; }\n.#{$fa-css-prefix}-toggle-off:before { content: $fa-var-toggle-off; }\n.#{$fa-css-prefix}-toggle-on:before { content: $fa-var-toggle-on; }\n.#{$fa-css-prefix}-bicycle:before { content: $fa-var-bicycle; }\n.#{$fa-css-prefix}-bus:before { content: $fa-var-bus; }\n.#{$fa-css-prefix}-ioxhost:before { content: $fa-var-ioxhost; }\n.#{$fa-css-prefix}-angellist:before { content: $fa-var-angellist; }\n.#{$fa-css-prefix}-cc:before { content: $fa-var-cc; }\n.#{$fa-css-prefix}-shekel:before,\n.#{$fa-css-prefix}-sheqel:before,\n.#{$fa-css-prefix}-ils:before { content: $fa-var-ils; }\n.#{$fa-css-prefix}-meanpath:before { content: $fa-var-meanpath; }\n.#{$fa-css-prefix}-buysellads:before { content: $fa-var-buysellads; }\n.#{$fa-css-prefix}-connectdevelop:before { content: $fa-var-connectdevelop; }\n.#{$fa-css-prefix}-dashcube:before { content: $fa-var-dashcube; }\n.#{$fa-css-prefix}-forumbee:before { content: $fa-var-forumbee; }\n.#{$fa-css-prefix}-leanpub:before { content: $fa-var-leanpub; }\n.#{$fa-css-prefix}-sellsy:before { content: $fa-var-sellsy; }\n.#{$fa-css-prefix}-shirtsinbulk:before { content: $fa-var-shirtsinbulk; }\n.#{$fa-css-prefix}-simplybuilt:before { content: $fa-var-simplybuilt; }\n.#{$fa-css-prefix}-skyatlas:before { content: $fa-var-skyatlas; }\n.#{$fa-css-prefix}-cart-plus:before { content: $fa-var-cart-plus; }\n.#{$fa-css-prefix}-cart-arrow-down:before { content: $fa-var-cart-arrow-down; }\n.#{$fa-css-prefix}-diamond:before { content: $fa-var-diamond; }\n.#{$fa-css-prefix}-ship:before { content: $fa-var-ship; }\n.#{$fa-css-prefix}-user-secret:before { content: $fa-var-user-secret; }\n.#{$fa-css-prefix}-motorcycle:before { content: $fa-var-motorcycle; }\n.#{$fa-css-prefix}-street-view:before { content: $fa-var-street-view; }\n.#{$fa-css-prefix}-heartbeat:before { content: $fa-var-heartbeat; }\n.#{$fa-css-prefix}-venus:before { content: $fa-var-venus; }\n.#{$fa-css-prefix}-mars:before { content: $fa-var-mars; }\n.#{$fa-css-prefix}-mercury:before { content: $fa-var-mercury; }\n.#{$fa-css-prefix}-intersex:before,\n.#{$fa-css-prefix}-transgender:before { content: $fa-var-transgender; }\n.#{$fa-css-prefix}-transgender-alt:before { content: $fa-var-transgender-alt; }\n.#{$fa-css-prefix}-venus-double:before { content: $fa-var-venus-double; }\n.#{$fa-css-prefix}-mars-double:before { content: $fa-var-mars-double; }\n.#{$fa-css-prefix}-venus-mars:before { content: $fa-var-venus-mars; }\n.#{$fa-css-prefix}-mars-stroke:before { content: $fa-var-mars-stroke; }\n.#{$fa-css-prefix}-mars-stroke-v:before { content: $fa-var-mars-stroke-v; }\n.#{$fa-css-prefix}-mars-stroke-h:before { content: $fa-var-mars-stroke-h; }\n.#{$fa-css-prefix}-neuter:before { content: $fa-var-neuter; }\n.#{$fa-css-prefix}-genderless:before { content: $fa-var-genderless; }\n.#{$fa-css-prefix}-facebook-official:before { content: $fa-var-facebook-official; }\n.#{$fa-css-prefix}-pinterest-p:before { content: $fa-var-pinterest-p; }\n.#{$fa-css-prefix}-whatsapp:before { content: $fa-var-whatsapp; }\n.#{$fa-css-prefix}-server:before { content: $fa-var-server; }\n.#{$fa-css-prefix}-user-plus:before { content: $fa-var-user-plus; }\n.#{$fa-css-prefix}-user-times:before { content: $fa-var-user-times; }\n.#{$fa-css-prefix}-hotel:before,\n.#{$fa-css-prefix}-bed:before { content: $fa-var-bed; }\n.#{$fa-css-prefix}-viacoin:before { content: $fa-var-viacoin; }\n.#{$fa-css-prefix}-train:before { content: $fa-var-train; }\n.#{$fa-css-prefix}-subway:before { content: $fa-var-subway; }\n.#{$fa-css-prefix}-medium:before { content: $fa-var-medium; }\n.#{$fa-css-prefix}-yc:before,\n.#{$fa-css-prefix}-y-combinator:before { content: $fa-var-y-combinator; }\n.#{$fa-css-prefix}-optin-monster:before { content: $fa-var-optin-monster; }\n.#{$fa-css-prefix}-opencart:before { content: $fa-var-opencart; }\n.#{$fa-css-prefix}-expeditedssl:before { content: $fa-var-expeditedssl; }\n.#{$fa-css-prefix}-battery-4:before,\n.#{$fa-css-prefix}-battery:before,\n.#{$fa-css-prefix}-battery-full:before { content: $fa-var-battery-full; }\n.#{$fa-css-prefix}-battery-3:before,\n.#{$fa-css-prefix}-battery-three-quarters:before { content: $fa-var-battery-three-quarters; }\n.#{$fa-css-prefix}-battery-2:before,\n.#{$fa-css-prefix}-battery-half:before { content: $fa-var-battery-half; }\n.#{$fa-css-prefix}-battery-1:before,\n.#{$fa-css-prefix}-battery-quarter:before { content: $fa-var-battery-quarter; }\n.#{$fa-css-prefix}-battery-0:before,\n.#{$fa-css-prefix}-battery-empty:before { content: $fa-var-battery-empty; }\n.#{$fa-css-prefix}-mouse-pointer:before { content: $fa-var-mouse-pointer; }\n.#{$fa-css-prefix}-i-cursor:before { content: $fa-var-i-cursor; }\n.#{$fa-css-prefix}-object-group:before { content: $fa-var-object-group; }\n.#{$fa-css-prefix}-object-ungroup:before { content: $fa-var-object-ungroup; }\n.#{$fa-css-prefix}-sticky-note:before { content: $fa-var-sticky-note; }\n.#{$fa-css-prefix}-sticky-note-o:before { content: $fa-var-sticky-note-o; }\n.#{$fa-css-prefix}-cc-jcb:before { content: $fa-var-cc-jcb; }\n.#{$fa-css-prefix}-cc-diners-club:before { content: $fa-var-cc-diners-club; }\n.#{$fa-css-prefix}-clone:before { content: $fa-var-clone; }\n.#{$fa-css-prefix}-balance-scale:before { content: $fa-var-balance-scale; }\n.#{$fa-css-prefix}-hourglass-o:before { content: $fa-var-hourglass-o; }\n.#{$fa-css-prefix}-hourglass-1:before,\n.#{$fa-css-prefix}-hourglass-start:before { content: $fa-var-hourglass-start; }\n.#{$fa-css-prefix}-hourglass-2:before,\n.#{$fa-css-prefix}-hourglass-half:before { content: $fa-var-hourglass-half; }\n.#{$fa-css-prefix}-hourglass-3:before,\n.#{$fa-css-prefix}-hourglass-end:before { content: $fa-var-hourglass-end; }\n.#{$fa-css-prefix}-hourglass:before { content: $fa-var-hourglass; }\n.#{$fa-css-prefix}-hand-grab-o:before,\n.#{$fa-css-prefix}-hand-rock-o:before { content: $fa-var-hand-rock-o; }\n.#{$fa-css-prefix}-hand-stop-o:before,\n.#{$fa-css-prefix}-hand-paper-o:before { content: $fa-var-hand-paper-o; }\n.#{$fa-css-prefix}-hand-scissors-o:before { content: $fa-var-hand-scissors-o; }\n.#{$fa-css-prefix}-hand-lizard-o:before { content: $fa-var-hand-lizard-o; }\n.#{$fa-css-prefix}-hand-spock-o:before { content: $fa-var-hand-spock-o; }\n.#{$fa-css-prefix}-hand-pointer-o:before { content: $fa-var-hand-pointer-o; }\n.#{$fa-css-prefix}-hand-peace-o:before { content: $fa-var-hand-peace-o; }\n.#{$fa-css-prefix}-trademark:before { content: $fa-var-trademark; }\n.#{$fa-css-prefix}-registered:before { content: $fa-var-registered; }\n.#{$fa-css-prefix}-creative-commons:before { content: $fa-var-creative-commons; }\n.#{$fa-css-prefix}-gg:before { content: $fa-var-gg; }\n.#{$fa-css-prefix}-gg-circle:before { content: $fa-var-gg-circle; }\n.#{$fa-css-prefix}-tripadvisor:before { content: $fa-var-tripadvisor; }\n.#{$fa-css-prefix}-odnoklassniki:before { content: $fa-var-odnoklassniki; }\n.#{$fa-css-prefix}-odnoklassniki-square:before { content: $fa-var-odnoklassniki-square; }\n.#{$fa-css-prefix}-get-pocket:before { content: $fa-var-get-pocket; }\n.#{$fa-css-prefix}-wikipedia-w:before { content: $fa-var-wikipedia-w; }\n.#{$fa-css-prefix}-safari:before { content: $fa-var-safari; }\n.#{$fa-css-prefix}-chrome:before { content: $fa-var-chrome; }\n.#{$fa-css-prefix}-firefox:before { content: $fa-var-firefox; }\n.#{$fa-css-prefix}-opera:before { content: $fa-var-opera; }\n.#{$fa-css-prefix}-internet-explorer:before { content: $fa-var-internet-explorer; }\n.#{$fa-css-prefix}-tv:before,\n.#{$fa-css-prefix}-television:before { content: $fa-var-television; }\n.#{$fa-css-prefix}-contao:before { content: $fa-var-contao; }\n.#{$fa-css-prefix}-500px:before { content: $fa-var-500px; }\n.#{$fa-css-prefix}-amazon:before { content: $fa-var-amazon; }\n.#{$fa-css-prefix}-calendar-plus-o:before { content: $fa-var-calendar-plus-o; }\n.#{$fa-css-prefix}-calendar-minus-o:before { content: $fa-var-calendar-minus-o; }\n.#{$fa-css-prefix}-calendar-times-o:before { content: $fa-var-calendar-times-o; }\n.#{$fa-css-prefix}-calendar-check-o:before { content: $fa-var-calendar-check-o; }\n.#{$fa-css-prefix}-industry:before { content: $fa-var-industry; }\n.#{$fa-css-prefix}-map-pin:before { content: $fa-var-map-pin; }\n.#{$fa-css-prefix}-map-signs:before { content: $fa-var-map-signs; }\n.#{$fa-css-prefix}-map-o:before { content: $fa-var-map-o; }\n.#{$fa-css-prefix}-map:before { content: $fa-var-map; }\n.#{$fa-css-prefix}-commenting:before { content: $fa-var-commenting; }\n.#{$fa-css-prefix}-commenting-o:before { content: $fa-var-commenting-o; }\n.#{$fa-css-prefix}-houzz:before { content: $fa-var-houzz; }\n.#{$fa-css-prefix}-vimeo:before { content: $fa-var-vimeo; }\n.#{$fa-css-prefix}-black-tie:before { content: $fa-var-black-tie; }\n.#{$fa-css-prefix}-fonticons:before { content: $fa-var-fonticons; }\n.#{$fa-css-prefix}-reddit-alien:before { content: $fa-var-reddit-alien; }\n.#{$fa-css-prefix}-edge:before { content: $fa-var-edge; }\n.#{$fa-css-prefix}-credit-card-alt:before { content: $fa-var-credit-card-alt; }\n.#{$fa-css-prefix}-codiepie:before { content: $fa-var-codiepie; }\n.#{$fa-css-prefix}-modx:before { content: $fa-var-modx; }\n.#{$fa-css-prefix}-fort-awesome:before { content: $fa-var-fort-awesome; }\n.#{$fa-css-prefix}-usb:before { content: $fa-var-usb; }\n.#{$fa-css-prefix}-product-hunt:before { content: $fa-var-product-hunt; }\n.#{$fa-css-prefix}-mixcloud:before { content: $fa-var-mixcloud; }\n.#{$fa-css-prefix}-scribd:before { content: $fa-var-scribd; }\n.#{$fa-css-prefix}-pause-circle:before { content: $fa-var-pause-circle; }\n.#{$fa-css-prefix}-pause-circle-o:before { content: $fa-var-pause-circle-o; }\n.#{$fa-css-prefix}-stop-circle:before { content: $fa-var-stop-circle; }\n.#{$fa-css-prefix}-stop-circle-o:before { content: $fa-var-stop-circle-o; }\n.#{$fa-css-prefix}-shopping-bag:before { content: $fa-var-shopping-bag; }\n.#{$fa-css-prefix}-shopping-basket:before { content: $fa-var-shopping-basket; }\n.#{$fa-css-prefix}-hashtag:before { content: $fa-var-hashtag; }\n.#{$fa-css-prefix}-bluetooth:before { content: $fa-var-bluetooth; }\n.#{$fa-css-prefix}-bluetooth-b:before { content: $fa-var-bluetooth-b; }\n.#{$fa-css-prefix}-percent:before { content: $fa-var-percent; }\n.#{$fa-css-prefix}-gitlab:before { content: $fa-var-gitlab; }\n.#{$fa-css-prefix}-wpbeginner:before { content: $fa-var-wpbeginner; }\n.#{$fa-css-prefix}-wpforms:before { content: $fa-var-wpforms; }\n.#{$fa-css-prefix}-envira:before { content: $fa-var-envira; }\n.#{$fa-css-prefix}-universal-access:before { content: $fa-var-universal-access; }\n.#{$fa-css-prefix}-wheelchair-alt:before { content: $fa-var-wheelchair-alt; }\n.#{$fa-css-prefix}-question-circle-o:before { content: $fa-var-question-circle-o; }\n.#{$fa-css-prefix}-blind:before { content: $fa-var-blind; }\n.#{$fa-css-prefix}-audio-description:before { content: $fa-var-audio-description; }\n.#{$fa-css-prefix}-volume-control-phone:before { content: $fa-var-volume-control-phone; }\n.#{$fa-css-prefix}-braille:before { content: $fa-var-braille; }\n.#{$fa-css-prefix}-assistive-listening-systems:before { content: $fa-var-assistive-listening-systems; }\n.#{$fa-css-prefix}-asl-interpreting:before,\n.#{$fa-css-prefix}-american-sign-language-interpreting:before { content: $fa-var-american-sign-language-interpreting; }\n.#{$fa-css-prefix}-deafness:before,\n.#{$fa-css-prefix}-hard-of-hearing:before,\n.#{$fa-css-prefix}-deaf:before { content: $fa-var-deaf; }\n.#{$fa-css-prefix}-glide:before { content: $fa-var-glide; }\n.#{$fa-css-prefix}-glide-g:before { content: $fa-var-glide-g; }\n.#{$fa-css-prefix}-signing:before,\n.#{$fa-css-prefix}-sign-language:before { content: $fa-var-sign-language; }\n.#{$fa-css-prefix}-low-vision:before { content: $fa-var-low-vision; }\n.#{$fa-css-prefix}-viadeo:before { content: $fa-var-viadeo; }\n.#{$fa-css-prefix}-viadeo-square:before { content: $fa-var-viadeo-square; }\n.#{$fa-css-prefix}-snapchat:before { content: $fa-var-snapchat; }\n.#{$fa-css-prefix}-snapchat-ghost:before { content: $fa-var-snapchat-ghost; }\n.#{$fa-css-prefix}-snapchat-square:before { content: $fa-var-snapchat-square; }\n.#{$fa-css-prefix}-pied-piper:before { content: $fa-var-pied-piper; }\n.#{$fa-css-prefix}-first-order:before { content: $fa-var-first-order; }\n.#{$fa-css-prefix}-yoast:before { content: $fa-var-yoast; }\n.#{$fa-css-prefix}-themeisle:before { content: $fa-var-themeisle; }\n.#{$fa-css-prefix}-google-plus-circle:before,\n.#{$fa-css-prefix}-google-plus-official:before { content: $fa-var-google-plus-official; }\n.#{$fa-css-prefix}-fa:before,\n.#{$fa-css-prefix}-font-awesome:before { content: $fa-var-font-awesome; }\n.#{$fa-css-prefix}-handshake-o:before { content: $fa-var-handshake-o; }\n.#{$fa-css-prefix}-envelope-open:before { content: $fa-var-envelope-open; }\n.#{$fa-css-prefix}-envelope-open-o:before { content: $fa-var-envelope-open-o; }\n.#{$fa-css-prefix}-linode:before { content: $fa-var-linode; }\n.#{$fa-css-prefix}-address-book:before { content: $fa-var-address-book; }\n.#{$fa-css-prefix}-address-book-o:before { content: $fa-var-address-book-o; }\n.#{$fa-css-prefix}-vcard:before,\n.#{$fa-css-prefix}-address-card:before { content: $fa-var-address-card; }\n.#{$fa-css-prefix}-vcard-o:before,\n.#{$fa-css-prefix}-address-card-o:before { content: $fa-var-address-card-o; }\n.#{$fa-css-prefix}-user-circle:before { content: $fa-var-user-circle; }\n.#{$fa-css-prefix}-user-circle-o:before { content: $fa-var-user-circle-o; }\n.#{$fa-css-prefix}-user-o:before { content: $fa-var-user-o; }\n.#{$fa-css-prefix}-id-badge:before { content: $fa-var-id-badge; }\n.#{$fa-css-prefix}-drivers-license:before,\n.#{$fa-css-prefix}-id-card:before { content: $fa-var-id-card; }\n.#{$fa-css-prefix}-drivers-license-o:before,\n.#{$fa-css-prefix}-id-card-o:before { content: $fa-var-id-card-o; }\n.#{$fa-css-prefix}-quora:before { content: $fa-var-quora; }\n.#{$fa-css-prefix}-free-code-camp:before { content: $fa-var-free-code-camp; }\n.#{$fa-css-prefix}-telegram:before { content: $fa-var-telegram; }\n.#{$fa-css-prefix}-thermometer-4:before,\n.#{$fa-css-prefix}-thermometer:before,\n.#{$fa-css-prefix}-thermometer-full:before { content: $fa-var-thermometer-full; }\n.#{$fa-css-prefix}-thermometer-3:before,\n.#{$fa-css-prefix}-thermometer-three-quarters:before { content: $fa-var-thermometer-three-quarters; }\n.#{$fa-css-prefix}-thermometer-2:before,\n.#{$fa-css-prefix}-thermometer-half:before { content: $fa-var-thermometer-half; }\n.#{$fa-css-prefix}-thermometer-1:before,\n.#{$fa-css-prefix}-thermometer-quarter:before { content: $fa-var-thermometer-quarter; }\n.#{$fa-css-prefix}-thermometer-0:before,\n.#{$fa-css-prefix}-thermometer-empty:before { content: $fa-var-thermometer-empty; }\n.#{$fa-css-prefix}-shower:before { content: $fa-var-shower; }\n.#{$fa-css-prefix}-bathtub:before,\n.#{$fa-css-prefix}-s15:before,\n.#{$fa-css-prefix}-bath:before { content: $fa-var-bath; }\n.#{$fa-css-prefix}-podcast:before { content: $fa-var-podcast; }\n.#{$fa-css-prefix}-window-maximize:before { content: $fa-var-window-maximize; }\n.#{$fa-css-prefix}-window-minimize:before { content: $fa-var-window-minimize; }\n.#{$fa-css-prefix}-window-restore:before { content: $fa-var-window-restore; }\n.#{$fa-css-prefix}-times-rectangle:before,\n.#{$fa-css-prefix}-window-close:before { content: $fa-var-window-close; }\n.#{$fa-css-prefix}-times-rectangle-o:before,\n.#{$fa-css-prefix}-window-close-o:before { content: $fa-var-window-close-o; }\n.#{$fa-css-prefix}-bandcamp:before { content: $fa-var-bandcamp; }\n.#{$fa-css-prefix}-grav:before { content: $fa-var-grav; }\n.#{$fa-css-prefix}-etsy:before { content: $fa-var-etsy; }\n.#{$fa-css-prefix}-imdb:before { content: $fa-var-imdb; }\n.#{$fa-css-prefix}-ravelry:before { content: $fa-var-ravelry; }\n.#{$fa-css-prefix}-eercast:before { content: $fa-var-eercast; }\n.#{$fa-css-prefix}-microchip:before { content: $fa-var-microchip; }\n.#{$fa-css-prefix}-snowflake-o:before { content: $fa-var-snowflake-o; }\n.#{$fa-css-prefix}-superpowers:before { content: $fa-var-superpowers; }\n.#{$fa-css-prefix}-wpexplorer:before { content: $fa-var-wpexplorer; }\n.#{$fa-css-prefix}-meetup:before { content: $fa-var-meetup; }\n", "// Variables\n// --------------------------\n\n$fa-font-path:        \"../fonts\" !default;\n$fa-font-size-base:   14px !default;\n$fa-line-height-base: 1 !default;\n//$fa-font-path:        \"//netdna.bootstrapcdn.com/font-awesome/4.7.0/fonts\" !default; // for referencing Bootstrap CDN font files directly\n$fa-css-prefix:       fa !default;\n$fa-version:          \"4.7.0\" !default;\n$fa-border-color:     #eee !default;\n$fa-inverse:          #fff !default;\n$fa-li-width:         (30em / 14) !default;\n\n$fa-var-500px: \"\\f26e\";\n$fa-var-address-book: \"\\f2b9\";\n$fa-var-address-book-o: \"\\f2ba\";\n$fa-var-address-card: \"\\f2bb\";\n$fa-var-address-card-o: \"\\f2bc\";\n$fa-var-adjust: \"\\f042\";\n$fa-var-adn: \"\\f170\";\n$fa-var-align-center: \"\\f037\";\n$fa-var-align-justify: \"\\f039\";\n$fa-var-align-left: \"\\f036\";\n$fa-var-align-right: \"\\f038\";\n$fa-var-amazon: \"\\f270\";\n$fa-var-ambulance: \"\\f0f9\";\n$fa-var-american-sign-language-interpreting: \"\\f2a3\";\n$fa-var-anchor: \"\\f13d\";\n$fa-var-android: \"\\f17b\";\n$fa-var-angellist: \"\\f209\";\n$fa-var-angle-double-down: \"\\f103\";\n$fa-var-angle-double-left: \"\\f100\";\n$fa-var-angle-double-right: \"\\f101\";\n$fa-var-angle-double-up: \"\\f102\";\n$fa-var-angle-down: \"\\f107\";\n$fa-var-angle-left: \"\\f104\";\n$fa-var-angle-right: \"\\f105\";\n$fa-var-angle-up: \"\\f106\";\n$fa-var-apple: \"\\f179\";\n$fa-var-archive: \"\\f187\";\n$fa-var-area-chart: \"\\f1fe\";\n$fa-var-arrow-circle-down: \"\\f0ab\";\n$fa-var-arrow-circle-left: \"\\f0a8\";\n$fa-var-arrow-circle-o-down: \"\\f01a\";\n$fa-var-arrow-circle-o-left: \"\\f190\";\n$fa-var-arrow-circle-o-right: \"\\f18e\";\n$fa-var-arrow-circle-o-up: \"\\f01b\";\n$fa-var-arrow-circle-right: \"\\f0a9\";\n$fa-var-arrow-circle-up: \"\\f0aa\";\n$fa-var-arrow-down: \"\\f063\";\n$fa-var-arrow-left: \"\\f060\";\n$fa-var-arrow-right: \"\\f061\";\n$fa-var-arrow-up: \"\\f062\";\n$fa-var-arrows: \"\\f047\";\n$fa-var-arrows-alt: \"\\f0b2\";\n$fa-var-arrows-h: \"\\f07e\";\n$fa-var-arrows-v: \"\\f07d\";\n$fa-var-asl-interpreting: \"\\f2a3\";\n$fa-var-assistive-listening-systems: \"\\f2a2\";\n$fa-var-asterisk: \"\\f069\";\n$fa-var-at: \"\\f1fa\";\n$fa-var-audio-description: \"\\f29e\";\n$fa-var-automobile: \"\\f1b9\";\n$fa-var-backward: \"\\f04a\";\n$fa-var-balance-scale: \"\\f24e\";\n$fa-var-ban: \"\\f05e\";\n$fa-var-bandcamp: \"\\f2d5\";\n$fa-var-bank: \"\\f19c\";\n$fa-var-bar-chart: \"\\f080\";\n$fa-var-bar-chart-o: \"\\f080\";\n$fa-var-barcode: \"\\f02a\";\n$fa-var-bars: \"\\f0c9\";\n$fa-var-bath: \"\\f2cd\";\n$fa-var-bathtub: \"\\f2cd\";\n$fa-var-battery: \"\\f240\";\n$fa-var-battery-0: \"\\f244\";\n$fa-var-battery-1: \"\\f243\";\n$fa-var-battery-2: \"\\f242\";\n$fa-var-battery-3: \"\\f241\";\n$fa-var-battery-4: \"\\f240\";\n$fa-var-battery-empty: \"\\f244\";\n$fa-var-battery-full: \"\\f240\";\n$fa-var-battery-half: \"\\f242\";\n$fa-var-battery-quarter: \"\\f243\";\n$fa-var-battery-three-quarters: \"\\f241\";\n$fa-var-bed: \"\\f236\";\n$fa-var-beer: \"\\f0fc\";\n$fa-var-behance: \"\\f1b4\";\n$fa-var-behance-square: \"\\f1b5\";\n$fa-var-bell: \"\\f0f3\";\n$fa-var-bell-o: \"\\f0a2\";\n$fa-var-bell-slash: \"\\f1f6\";\n$fa-var-bell-slash-o: \"\\f1f7\";\n$fa-var-bicycle: \"\\f206\";\n$fa-var-binoculars: \"\\f1e5\";\n$fa-var-birthday-cake: \"\\f1fd\";\n$fa-var-bitbucket: \"\\f171\";\n$fa-var-bitbucket-square: \"\\f172\";\n$fa-var-bitcoin: \"\\f15a\";\n$fa-var-black-tie: \"\\f27e\";\n$fa-var-blind: \"\\f29d\";\n$fa-var-bluetooth: \"\\f293\";\n$fa-var-bluetooth-b: \"\\f294\";\n$fa-var-bold: \"\\f032\";\n$fa-var-bolt: \"\\f0e7\";\n$fa-var-bomb: \"\\f1e2\";\n$fa-var-book: \"\\f02d\";\n$fa-var-bookmark: \"\\f02e\";\n$fa-var-bookmark-o: \"\\f097\";\n$fa-var-braille: \"\\f2a1\";\n$fa-var-briefcase: \"\\f0b1\";\n$fa-var-btc: \"\\f15a\";\n$fa-var-bug: \"\\f188\";\n$fa-var-building: \"\\f1ad\";\n$fa-var-building-o: \"\\f0f7\";\n$fa-var-bullhorn: \"\\f0a1\";\n$fa-var-bullseye: \"\\f140\";\n$fa-var-bus: \"\\f207\";\n$fa-var-buysellads: \"\\f20d\";\n$fa-var-cab: \"\\f1ba\";\n$fa-var-calculator: \"\\f1ec\";\n$fa-var-calendar: \"\\f073\";\n$fa-var-calendar-check-o: \"\\f274\";\n$fa-var-calendar-minus-o: \"\\f272\";\n$fa-var-calendar-o: \"\\f133\";\n$fa-var-calendar-plus-o: \"\\f271\";\n$fa-var-calendar-times-o: \"\\f273\";\n$fa-var-camera: \"\\f030\";\n$fa-var-camera-retro: \"\\f083\";\n$fa-var-car: \"\\f1b9\";\n$fa-var-caret-down: \"\\f0d7\";\n$fa-var-caret-left: \"\\f0d9\";\n$fa-var-caret-right: \"\\f0da\";\n$fa-var-caret-square-o-down: \"\\f150\";\n$fa-var-caret-square-o-left: \"\\f191\";\n$fa-var-caret-square-o-right: \"\\f152\";\n$fa-var-caret-square-o-up: \"\\f151\";\n$fa-var-caret-up: \"\\f0d8\";\n$fa-var-cart-arrow-down: \"\\f218\";\n$fa-var-cart-plus: \"\\f217\";\n$fa-var-cc: \"\\f20a\";\n$fa-var-cc-amex: \"\\f1f3\";\n$fa-var-cc-diners-club: \"\\f24c\";\n$fa-var-cc-discover: \"\\f1f2\";\n$fa-var-cc-jcb: \"\\f24b\";\n$fa-var-cc-mastercard: \"\\f1f1\";\n$fa-var-cc-paypal: \"\\f1f4\";\n$fa-var-cc-stripe: \"\\f1f5\";\n$fa-var-cc-visa: \"\\f1f0\";\n$fa-var-certificate: \"\\f0a3\";\n$fa-var-chain: \"\\f0c1\";\n$fa-var-chain-broken: \"\\f127\";\n$fa-var-check: \"\\f00c\";\n$fa-var-check-circle: \"\\f058\";\n$fa-var-check-circle-o: \"\\f05d\";\n$fa-var-check-square: \"\\f14a\";\n$fa-var-check-square-o: \"\\f046\";\n$fa-var-chevron-circle-down: \"\\f13a\";\n$fa-var-chevron-circle-left: \"\\f137\";\n$fa-var-chevron-circle-right: \"\\f138\";\n$fa-var-chevron-circle-up: \"\\f139\";\n$fa-var-chevron-down: \"\\f078\";\n$fa-var-chevron-left: \"\\f053\";\n$fa-var-chevron-right: \"\\f054\";\n$fa-var-chevron-up: \"\\f077\";\n$fa-var-child: \"\\f1ae\";\n$fa-var-chrome: \"\\f268\";\n$fa-var-circle: \"\\f111\";\n$fa-var-circle-o: \"\\f10c\";\n$fa-var-circle-o-notch: \"\\f1ce\";\n$fa-var-circle-thin: \"\\f1db\";\n$fa-var-clipboard: \"\\f0ea\";\n$fa-var-clock-o: \"\\f017\";\n$fa-var-clone: \"\\f24d\";\n$fa-var-close: \"\\f00d\";\n$fa-var-cloud: \"\\f0c2\";\n$fa-var-cloud-download: \"\\f0ed\";\n$fa-var-cloud-upload: \"\\f0ee\";\n$fa-var-cny: \"\\f157\";\n$fa-var-code: \"\\f121\";\n$fa-var-code-fork: \"\\f126\";\n$fa-var-codepen: \"\\f1cb\";\n$fa-var-codiepie: \"\\f284\";\n$fa-var-coffee: \"\\f0f4\";\n$fa-var-cog: \"\\f013\";\n$fa-var-cogs: \"\\f085\";\n$fa-var-columns: \"\\f0db\";\n$fa-var-comment: \"\\f075\";\n$fa-var-comment-o: \"\\f0e5\";\n$fa-var-commenting: \"\\f27a\";\n$fa-var-commenting-o: \"\\f27b\";\n$fa-var-comments: \"\\f086\";\n$fa-var-comments-o: \"\\f0e6\";\n$fa-var-compass: \"\\f14e\";\n$fa-var-compress: \"\\f066\";\n$fa-var-connectdevelop: \"\\f20e\";\n$fa-var-contao: \"\\f26d\";\n$fa-var-copy: \"\\f0c5\";\n$fa-var-copyright: \"\\f1f9\";\n$fa-var-creative-commons: \"\\f25e\";\n$fa-var-credit-card: \"\\f09d\";\n$fa-var-credit-card-alt: \"\\f283\";\n$fa-var-crop: \"\\f125\";\n$fa-var-crosshairs: \"\\f05b\";\n$fa-var-css3: \"\\f13c\";\n$fa-var-cube: \"\\f1b2\";\n$fa-var-cubes: \"\\f1b3\";\n$fa-var-cut: \"\\f0c4\";\n$fa-var-cutlery: \"\\f0f5\";\n$fa-var-dashboard: \"\\f0e4\";\n$fa-var-dashcube: \"\\f210\";\n$fa-var-database: \"\\f1c0\";\n$fa-var-deaf: \"\\f2a4\";\n$fa-var-deafness: \"\\f2a4\";\n$fa-var-dedent: \"\\f03b\";\n$fa-var-delicious: \"\\f1a5\";\n$fa-var-desktop: \"\\f108\";\n$fa-var-deviantart: \"\\f1bd\";\n$fa-var-diamond: \"\\f219\";\n$fa-var-digg: \"\\f1a6\";\n$fa-var-dollar: \"\\f155\";\n$fa-var-dot-circle-o: \"\\f192\";\n$fa-var-download: \"\\f019\";\n$fa-var-dribbble: \"\\f17d\";\n$fa-var-drivers-license: \"\\f2c2\";\n$fa-var-drivers-license-o: \"\\f2c3\";\n$fa-var-dropbox: \"\\f16b\";\n$fa-var-drupal: \"\\f1a9\";\n$fa-var-edge: \"\\f282\";\n$fa-var-edit: \"\\f044\";\n$fa-var-eercast: \"\\f2da\";\n$fa-var-eject: \"\\f052\";\n$fa-var-ellipsis-h: \"\\f141\";\n$fa-var-ellipsis-v: \"\\f142\";\n$fa-var-empire: \"\\f1d1\";\n$fa-var-envelope: \"\\f0e0\";\n$fa-var-envelope-o: \"\\f003\";\n$fa-var-envelope-open: \"\\f2b6\";\n$fa-var-envelope-open-o: \"\\f2b7\";\n$fa-var-envelope-square: \"\\f199\";\n$fa-var-envira: \"\\f299\";\n$fa-var-eraser: \"\\f12d\";\n$fa-var-etsy: \"\\f2d7\";\n$fa-var-eur: \"\\f153\";\n$fa-var-euro: \"\\f153\";\n$fa-var-exchange: \"\\f0ec\";\n$fa-var-exclamation: \"\\f12a\";\n$fa-var-exclamation-circle: \"\\f06a\";\n$fa-var-exclamation-triangle: \"\\f071\";\n$fa-var-expand: \"\\f065\";\n$fa-var-expeditedssl: \"\\f23e\";\n$fa-var-external-link: \"\\f08e\";\n$fa-var-external-link-square: \"\\f14c\";\n$fa-var-eye: \"\\f06e\";\n$fa-var-eye-slash: \"\\f070\";\n$fa-var-eyedropper: \"\\f1fb\";\n$fa-var-fa: \"\\f2b4\";\n$fa-var-facebook: \"\\f09a\";\n$fa-var-facebook-f: \"\\f09a\";\n$fa-var-facebook-official: \"\\f230\";\n$fa-var-facebook-square: \"\\f082\";\n$fa-var-fast-backward: \"\\f049\";\n$fa-var-fast-forward: \"\\f050\";\n$fa-var-fax: \"\\f1ac\";\n$fa-var-feed: \"\\f09e\";\n$fa-var-female: \"\\f182\";\n$fa-var-fighter-jet: \"\\f0fb\";\n$fa-var-file: \"\\f15b\";\n$fa-var-file-archive-o: \"\\f1c6\";\n$fa-var-file-audio-o: \"\\f1c7\";\n$fa-var-file-code-o: \"\\f1c9\";\n$fa-var-file-excel-o: \"\\f1c3\";\n$fa-var-file-image-o: \"\\f1c5\";\n$fa-var-file-movie-o: \"\\f1c8\";\n$fa-var-file-o: \"\\f016\";\n$fa-var-file-pdf-o: \"\\f1c1\";\n$fa-var-file-photo-o: \"\\f1c5\";\n$fa-var-file-picture-o: \"\\f1c5\";\n$fa-var-file-powerpoint-o: \"\\f1c4\";\n$fa-var-file-sound-o: \"\\f1c7\";\n$fa-var-file-text: \"\\f15c\";\n$fa-var-file-text-o: \"\\f0f6\";\n$fa-var-file-video-o: \"\\f1c8\";\n$fa-var-file-word-o: \"\\f1c2\";\n$fa-var-file-zip-o: \"\\f1c6\";\n$fa-var-files-o: \"\\f0c5\";\n$fa-var-film: \"\\f008\";\n$fa-var-filter: \"\\f0b0\";\n$fa-var-fire: \"\\f06d\";\n$fa-var-fire-extinguisher: \"\\f134\";\n$fa-var-firefox: \"\\f269\";\n$fa-var-first-order: \"\\f2b0\";\n$fa-var-flag: \"\\f024\";\n$fa-var-flag-checkered: \"\\f11e\";\n$fa-var-flag-o: \"\\f11d\";\n$fa-var-flash: \"\\f0e7\";\n$fa-var-flask: \"\\f0c3\";\n$fa-var-flickr: \"\\f16e\";\n$fa-var-floppy-o: \"\\f0c7\";\n$fa-var-folder: \"\\f07b\";\n$fa-var-folder-o: \"\\f114\";\n$fa-var-folder-open: \"\\f07c\";\n$fa-var-folder-open-o: \"\\f115\";\n$fa-var-font: \"\\f031\";\n$fa-var-font-awesome: \"\\f2b4\";\n$fa-var-fonticons: \"\\f280\";\n$fa-var-fort-awesome: \"\\f286\";\n$fa-var-forumbee: \"\\f211\";\n$fa-var-forward: \"\\f04e\";\n$fa-var-foursquare: \"\\f180\";\n$fa-var-free-code-camp: \"\\f2c5\";\n$fa-var-frown-o: \"\\f119\";\n$fa-var-futbol-o: \"\\f1e3\";\n$fa-var-gamepad: \"\\f11b\";\n$fa-var-gavel: \"\\f0e3\";\n$fa-var-gbp: \"\\f154\";\n$fa-var-ge: \"\\f1d1\";\n$fa-var-gear: \"\\f013\";\n$fa-var-gears: \"\\f085\";\n$fa-var-genderless: \"\\f22d\";\n$fa-var-get-pocket: \"\\f265\";\n$fa-var-gg: \"\\f260\";\n$fa-var-gg-circle: \"\\f261\";\n$fa-var-gift: \"\\f06b\";\n$fa-var-git: \"\\f1d3\";\n$fa-var-git-square: \"\\f1d2\";\n$fa-var-github: \"\\f09b\";\n$fa-var-github-alt: \"\\f113\";\n$fa-var-github-square: \"\\f092\";\n$fa-var-gitlab: \"\\f296\";\n$fa-var-gittip: \"\\f184\";\n$fa-var-glass: \"\\f000\";\n$fa-var-glide: \"\\f2a5\";\n$fa-var-glide-g: \"\\f2a6\";\n$fa-var-globe: \"\\f0ac\";\n$fa-var-google: \"\\f1a0\";\n$fa-var-google-plus: \"\\f0d5\";\n$fa-var-google-plus-circle: \"\\f2b3\";\n$fa-var-google-plus-official: \"\\f2b3\";\n$fa-var-google-plus-square: \"\\f0d4\";\n$fa-var-google-wallet: \"\\f1ee\";\n$fa-var-graduation-cap: \"\\f19d\";\n$fa-var-gratipay: \"\\f184\";\n$fa-var-grav: \"\\f2d6\";\n$fa-var-group: \"\\f0c0\";\n$fa-var-h-square: \"\\f0fd\";\n$fa-var-hacker-news: \"\\f1d4\";\n$fa-var-hand-grab-o: \"\\f255\";\n$fa-var-hand-lizard-o: \"\\f258\";\n$fa-var-hand-o-down: \"\\f0a7\";\n$fa-var-hand-o-left: \"\\f0a5\";\n$fa-var-hand-o-right: \"\\f0a4\";\n$fa-var-hand-o-up: \"\\f0a6\";\n$fa-var-hand-paper-o: \"\\f256\";\n$fa-var-hand-peace-o: \"\\f25b\";\n$fa-var-hand-pointer-o: \"\\f25a\";\n$fa-var-hand-rock-o: \"\\f255\";\n$fa-var-hand-scissors-o: \"\\f257\";\n$fa-var-hand-spock-o: \"\\f259\";\n$fa-var-hand-stop-o: \"\\f256\";\n$fa-var-handshake-o: \"\\f2b5\";\n$fa-var-hard-of-hearing: \"\\f2a4\";\n$fa-var-hashtag: \"\\f292\";\n$fa-var-hdd-o: \"\\f0a0\";\n$fa-var-header: \"\\f1dc\";\n$fa-var-headphones: \"\\f025\";\n$fa-var-heart: \"\\f004\";\n$fa-var-heart-o: \"\\f08a\";\n$fa-var-heartbeat: \"\\f21e\";\n$fa-var-history: \"\\f1da\";\n$fa-var-home: \"\\f015\";\n$fa-var-hospital-o: \"\\f0f8\";\n$fa-var-hotel: \"\\f236\";\n$fa-var-hourglass: \"\\f254\";\n$fa-var-hourglass-1: \"\\f251\";\n$fa-var-hourglass-2: \"\\f252\";\n$fa-var-hourglass-3: \"\\f253\";\n$fa-var-hourglass-end: \"\\f253\";\n$fa-var-hourglass-half: \"\\f252\";\n$fa-var-hourglass-o: \"\\f250\";\n$fa-var-hourglass-start: \"\\f251\";\n$fa-var-houzz: \"\\f27c\";\n$fa-var-html5: \"\\f13b\";\n$fa-var-i-cursor: \"\\f246\";\n$fa-var-id-badge: \"\\f2c1\";\n$fa-var-id-card: \"\\f2c2\";\n$fa-var-id-card-o: \"\\f2c3\";\n$fa-var-ils: \"\\f20b\";\n$fa-var-image: \"\\f03e\";\n$fa-var-imdb: \"\\f2d8\";\n$fa-var-inbox: \"\\f01c\";\n$fa-var-indent: \"\\f03c\";\n$fa-var-industry: \"\\f275\";\n$fa-var-info: \"\\f129\";\n$fa-var-info-circle: \"\\f05a\";\n$fa-var-inr: \"\\f156\";\n$fa-var-instagram: \"\\f16d\";\n$fa-var-institution: \"\\f19c\";\n$fa-var-internet-explorer: \"\\f26b\";\n$fa-var-intersex: \"\\f224\";\n$fa-var-ioxhost: \"\\f208\";\n$fa-var-italic: \"\\f033\";\n$fa-var-joomla: \"\\f1aa\";\n$fa-var-jpy: \"\\f157\";\n$fa-var-jsfiddle: \"\\f1cc\";\n$fa-var-key: \"\\f084\";\n$fa-var-keyboard-o: \"\\f11c\";\n$fa-var-krw: \"\\f159\";\n$fa-var-language: \"\\f1ab\";\n$fa-var-laptop: \"\\f109\";\n$fa-var-lastfm: \"\\f202\";\n$fa-var-lastfm-square: \"\\f203\";\n$fa-var-leaf: \"\\f06c\";\n$fa-var-leanpub: \"\\f212\";\n$fa-var-legal: \"\\f0e3\";\n$fa-var-lemon-o: \"\\f094\";\n$fa-var-level-down: \"\\f149\";\n$fa-var-level-up: \"\\f148\";\n$fa-var-life-bouy: \"\\f1cd\";\n$fa-var-life-buoy: \"\\f1cd\";\n$fa-var-life-ring: \"\\f1cd\";\n$fa-var-life-saver: \"\\f1cd\";\n$fa-var-lightbulb-o: \"\\f0eb\";\n$fa-var-line-chart: \"\\f201\";\n$fa-var-link: \"\\f0c1\";\n$fa-var-linkedin: \"\\f0e1\";\n$fa-var-linkedin-square: \"\\f08c\";\n$fa-var-linode: \"\\f2b8\";\n$fa-var-linux: \"\\f17c\";\n$fa-var-list: \"\\f03a\";\n$fa-var-list-alt: \"\\f022\";\n$fa-var-list-ol: \"\\f0cb\";\n$fa-var-list-ul: \"\\f0ca\";\n$fa-var-location-arrow: \"\\f124\";\n$fa-var-lock: \"\\f023\";\n$fa-var-long-arrow-down: \"\\f175\";\n$fa-var-long-arrow-left: \"\\f177\";\n$fa-var-long-arrow-right: \"\\f178\";\n$fa-var-long-arrow-up: \"\\f176\";\n$fa-var-low-vision: \"\\f2a8\";\n$fa-var-magic: \"\\f0d0\";\n$fa-var-magnet: \"\\f076\";\n$fa-var-mail-forward: \"\\f064\";\n$fa-var-mail-reply: \"\\f112\";\n$fa-var-mail-reply-all: \"\\f122\";\n$fa-var-male: \"\\f183\";\n$fa-var-map: \"\\f279\";\n$fa-var-map-marker: \"\\f041\";\n$fa-var-map-o: \"\\f278\";\n$fa-var-map-pin: \"\\f276\";\n$fa-var-map-signs: \"\\f277\";\n$fa-var-mars: \"\\f222\";\n$fa-var-mars-double: \"\\f227\";\n$fa-var-mars-stroke: \"\\f229\";\n$fa-var-mars-stroke-h: \"\\f22b\";\n$fa-var-mars-stroke-v: \"\\f22a\";\n$fa-var-maxcdn: \"\\f136\";\n$fa-var-meanpath: \"\\f20c\";\n$fa-var-medium: \"\\f23a\";\n$fa-var-medkit: \"\\f0fa\";\n$fa-var-meetup: \"\\f2e0\";\n$fa-var-meh-o: \"\\f11a\";\n$fa-var-mercury: \"\\f223\";\n$fa-var-microchip: \"\\f2db\";\n$fa-var-microphone: \"\\f130\";\n$fa-var-microphone-slash: \"\\f131\";\n$fa-var-minus: \"\\f068\";\n$fa-var-minus-circle: \"\\f056\";\n$fa-var-minus-square: \"\\f146\";\n$fa-var-minus-square-o: \"\\f147\";\n$fa-var-mixcloud: \"\\f289\";\n$fa-var-mobile: \"\\f10b\";\n$fa-var-mobile-phone: \"\\f10b\";\n$fa-var-modx: \"\\f285\";\n$fa-var-money: \"\\f0d6\";\n$fa-var-moon-o: \"\\f186\";\n$fa-var-mortar-board: \"\\f19d\";\n$fa-var-motorcycle: \"\\f21c\";\n$fa-var-mouse-pointer: \"\\f245\";\n$fa-var-music: \"\\f001\";\n$fa-var-navicon: \"\\f0c9\";\n$fa-var-neuter: \"\\f22c\";\n$fa-var-newspaper-o: \"\\f1ea\";\n$fa-var-object-group: \"\\f247\";\n$fa-var-object-ungroup: \"\\f248\";\n$fa-var-odnoklassniki: \"\\f263\";\n$fa-var-odnoklassniki-square: \"\\f264\";\n$fa-var-opencart: \"\\f23d\";\n$fa-var-openid: \"\\f19b\";\n$fa-var-opera: \"\\f26a\";\n$fa-var-optin-monster: \"\\f23c\";\n$fa-var-outdent: \"\\f03b\";\n$fa-var-pagelines: \"\\f18c\";\n$fa-var-paint-brush: \"\\f1fc\";\n$fa-var-paper-plane: \"\\f1d8\";\n$fa-var-paper-plane-o: \"\\f1d9\";\n$fa-var-paperclip: \"\\f0c6\";\n$fa-var-paragraph: \"\\f1dd\";\n$fa-var-paste: \"\\f0ea\";\n$fa-var-pause: \"\\f04c\";\n$fa-var-pause-circle: \"\\f28b\";\n$fa-var-pause-circle-o: \"\\f28c\";\n$fa-var-paw: \"\\f1b0\";\n$fa-var-paypal: \"\\f1ed\";\n$fa-var-pencil: \"\\f040\";\n$fa-var-pencil-square: \"\\f14b\";\n$fa-var-pencil-square-o: \"\\f044\";\n$fa-var-percent: \"\\f295\";\n$fa-var-phone: \"\\f095\";\n$fa-var-phone-square: \"\\f098\";\n$fa-var-photo: \"\\f03e\";\n$fa-var-picture-o: \"\\f03e\";\n$fa-var-pie-chart: \"\\f200\";\n$fa-var-pied-piper: \"\\f2ae\";\n$fa-var-pied-piper-alt: \"\\f1a8\";\n$fa-var-pied-piper-pp: \"\\f1a7\";\n$fa-var-pinterest: \"\\f0d2\";\n$fa-var-pinterest-p: \"\\f231\";\n$fa-var-pinterest-square: \"\\f0d3\";\n$fa-var-plane: \"\\f072\";\n$fa-var-play: \"\\f04b\";\n$fa-var-play-circle: \"\\f144\";\n$fa-var-play-circle-o: \"\\f01d\";\n$fa-var-plug: \"\\f1e6\";\n$fa-var-plus: \"\\f067\";\n$fa-var-plus-circle: \"\\f055\";\n$fa-var-plus-square: \"\\f0fe\";\n$fa-var-plus-square-o: \"\\f196\";\n$fa-var-podcast: \"\\f2ce\";\n$fa-var-power-off: \"\\f011\";\n$fa-var-print: \"\\f02f\";\n$fa-var-product-hunt: \"\\f288\";\n$fa-var-puzzle-piece: \"\\f12e\";\n$fa-var-qq: \"\\f1d6\";\n$fa-var-qrcode: \"\\f029\";\n$fa-var-question: \"\\f128\";\n$fa-var-question-circle: \"\\f059\";\n$fa-var-question-circle-o: \"\\f29c\";\n$fa-var-quora: \"\\f2c4\";\n$fa-var-quote-left: \"\\f10d\";\n$fa-var-quote-right: \"\\f10e\";\n$fa-var-ra: \"\\f1d0\";\n$fa-var-random: \"\\f074\";\n$fa-var-ravelry: \"\\f2d9\";\n$fa-var-rebel: \"\\f1d0\";\n$fa-var-recycle: \"\\f1b8\";\n$fa-var-reddit: \"\\f1a1\";\n$fa-var-reddit-alien: \"\\f281\";\n$fa-var-reddit-square: \"\\f1a2\";\n$fa-var-refresh: \"\\f021\";\n$fa-var-registered: \"\\f25d\";\n$fa-var-remove: \"\\f00d\";\n$fa-var-renren: \"\\f18b\";\n$fa-var-reorder: \"\\f0c9\";\n$fa-var-repeat: \"\\f01e\";\n$fa-var-reply: \"\\f112\";\n$fa-var-reply-all: \"\\f122\";\n$fa-var-resistance: \"\\f1d0\";\n$fa-var-retweet: \"\\f079\";\n$fa-var-rmb: \"\\f157\";\n$fa-var-road: \"\\f018\";\n$fa-var-rocket: \"\\f135\";\n$fa-var-rotate-left: \"\\f0e2\";\n$fa-var-rotate-right: \"\\f01e\";\n$fa-var-rouble: \"\\f158\";\n$fa-var-rss: \"\\f09e\";\n$fa-var-rss-square: \"\\f143\";\n$fa-var-rub: \"\\f158\";\n$fa-var-ruble: \"\\f158\";\n$fa-var-rupee: \"\\f156\";\n$fa-var-s15: \"\\f2cd\";\n$fa-var-safari: \"\\f267\";\n$fa-var-save: \"\\f0c7\";\n$fa-var-scissors: \"\\f0c4\";\n$fa-var-scribd: \"\\f28a\";\n$fa-var-search: \"\\f002\";\n$fa-var-search-minus: \"\\f010\";\n$fa-var-search-plus: \"\\f00e\";\n$fa-var-sellsy: \"\\f213\";\n$fa-var-send: \"\\f1d8\";\n$fa-var-send-o: \"\\f1d9\";\n$fa-var-server: \"\\f233\";\n$fa-var-share: \"\\f064\";\n$fa-var-share-alt: \"\\f1e0\";\n$fa-var-share-alt-square: \"\\f1e1\";\n$fa-var-share-square: \"\\f14d\";\n$fa-var-share-square-o: \"\\f045\";\n$fa-var-shekel: \"\\f20b\";\n$fa-var-sheqel: \"\\f20b\";\n$fa-var-shield: \"\\f132\";\n$fa-var-ship: \"\\f21a\";\n$fa-var-shirtsinbulk: \"\\f214\";\n$fa-var-shopping-bag: \"\\f290\";\n$fa-var-shopping-basket: \"\\f291\";\n$fa-var-shopping-cart: \"\\f07a\";\n$fa-var-shower: \"\\f2cc\";\n$fa-var-sign-in: \"\\f090\";\n$fa-var-sign-language: \"\\f2a7\";\n$fa-var-sign-out: \"\\f08b\";\n$fa-var-signal: \"\\f012\";\n$fa-var-signing: \"\\f2a7\";\n$fa-var-simplybuilt: \"\\f215\";\n$fa-var-sitemap: \"\\f0e8\";\n$fa-var-skyatlas: \"\\f216\";\n$fa-var-skype: \"\\f17e\";\n$fa-var-slack: \"\\f198\";\n$fa-var-sliders: \"\\f1de\";\n$fa-var-slideshare: \"\\f1e7\";\n$fa-var-smile-o: \"\\f118\";\n$fa-var-snapchat: \"\\f2ab\";\n$fa-var-snapchat-ghost: \"\\f2ac\";\n$fa-var-snapchat-square: \"\\f2ad\";\n$fa-var-snowflake-o: \"\\f2dc\";\n$fa-var-soccer-ball-o: \"\\f1e3\";\n$fa-var-sort: \"\\f0dc\";\n$fa-var-sort-alpha-asc: \"\\f15d\";\n$fa-var-sort-alpha-desc: \"\\f15e\";\n$fa-var-sort-amount-asc: \"\\f160\";\n$fa-var-sort-amount-desc: \"\\f161\";\n$fa-var-sort-asc: \"\\f0de\";\n$fa-var-sort-desc: \"\\f0dd\";\n$fa-var-sort-down: \"\\f0dd\";\n$fa-var-sort-numeric-asc: \"\\f162\";\n$fa-var-sort-numeric-desc: \"\\f163\";\n$fa-var-sort-up: \"\\f0de\";\n$fa-var-soundcloud: \"\\f1be\";\n$fa-var-space-shuttle: \"\\f197\";\n$fa-var-spinner: \"\\f110\";\n$fa-var-spoon: \"\\f1b1\";\n$fa-var-spotify: \"\\f1bc\";\n$fa-var-square: \"\\f0c8\";\n$fa-var-square-o: \"\\f096\";\n$fa-var-stack-exchange: \"\\f18d\";\n$fa-var-stack-overflow: \"\\f16c\";\n$fa-var-star: \"\\f005\";\n$fa-var-star-half: \"\\f089\";\n$fa-var-star-half-empty: \"\\f123\";\n$fa-var-star-half-full: \"\\f123\";\n$fa-var-star-half-o: \"\\f123\";\n$fa-var-star-o: \"\\f006\";\n$fa-var-steam: \"\\f1b6\";\n$fa-var-steam-square: \"\\f1b7\";\n$fa-var-step-backward: \"\\f048\";\n$fa-var-step-forward: \"\\f051\";\n$fa-var-stethoscope: \"\\f0f1\";\n$fa-var-sticky-note: \"\\f249\";\n$fa-var-sticky-note-o: \"\\f24a\";\n$fa-var-stop: \"\\f04d\";\n$fa-var-stop-circle: \"\\f28d\";\n$fa-var-stop-circle-o: \"\\f28e\";\n$fa-var-street-view: \"\\f21d\";\n$fa-var-strikethrough: \"\\f0cc\";\n$fa-var-stumbleupon: \"\\f1a4\";\n$fa-var-stumbleupon-circle: \"\\f1a3\";\n$fa-var-subscript: \"\\f12c\";\n$fa-var-subway: \"\\f239\";\n$fa-var-suitcase: \"\\f0f2\";\n$fa-var-sun-o: \"\\f185\";\n$fa-var-superpowers: \"\\f2dd\";\n$fa-var-superscript: \"\\f12b\";\n$fa-var-support: \"\\f1cd\";\n$fa-var-table: \"\\f0ce\";\n$fa-var-tablet: \"\\f10a\";\n$fa-var-tachometer: \"\\f0e4\";\n$fa-var-tag: \"\\f02b\";\n$fa-var-tags: \"\\f02c\";\n$fa-var-tasks: \"\\f0ae\";\n$fa-var-taxi: \"\\f1ba\";\n$fa-var-telegram: \"\\f2c6\";\n$fa-var-television: \"\\f26c\";\n$fa-var-tencent-weibo: \"\\f1d5\";\n$fa-var-terminal: \"\\f120\";\n$fa-var-text-height: \"\\f034\";\n$fa-var-text-width: \"\\f035\";\n$fa-var-th: \"\\f00a\";\n$fa-var-th-large: \"\\f009\";\n$fa-var-th-list: \"\\f00b\";\n$fa-var-themeisle: \"\\f2b2\";\n$fa-var-thermometer: \"\\f2c7\";\n$fa-var-thermometer-0: \"\\f2cb\";\n$fa-var-thermometer-1: \"\\f2ca\";\n$fa-var-thermometer-2: \"\\f2c9\";\n$fa-var-thermometer-3: \"\\f2c8\";\n$fa-var-thermometer-4: \"\\f2c7\";\n$fa-var-thermometer-empty: \"\\f2cb\";\n$fa-var-thermometer-full: \"\\f2c7\";\n$fa-var-thermometer-half: \"\\f2c9\";\n$fa-var-thermometer-quarter: \"\\f2ca\";\n$fa-var-thermometer-three-quarters: \"\\f2c8\";\n$fa-var-thumb-tack: \"\\f08d\";\n$fa-var-thumbs-down: \"\\f165\";\n$fa-var-thumbs-o-down: \"\\f088\";\n$fa-var-thumbs-o-up: \"\\f087\";\n$fa-var-thumbs-up: \"\\f164\";\n$fa-var-ticket: \"\\f145\";\n$fa-var-times: \"\\f00d\";\n$fa-var-times-circle: \"\\f057\";\n$fa-var-times-circle-o: \"\\f05c\";\n$fa-var-times-rectangle: \"\\f2d3\";\n$fa-var-times-rectangle-o: \"\\f2d4\";\n$fa-var-tint: \"\\f043\";\n$fa-var-toggle-down: \"\\f150\";\n$fa-var-toggle-left: \"\\f191\";\n$fa-var-toggle-off: \"\\f204\";\n$fa-var-toggle-on: \"\\f205\";\n$fa-var-toggle-right: \"\\f152\";\n$fa-var-toggle-up: \"\\f151\";\n$fa-var-trademark: \"\\f25c\";\n$fa-var-train: \"\\f238\";\n$fa-var-transgender: \"\\f224\";\n$fa-var-transgender-alt: \"\\f225\";\n$fa-var-trash: \"\\f1f8\";\n$fa-var-trash-o: \"\\f014\";\n$fa-var-tree: \"\\f1bb\";\n$fa-var-trello: \"\\f181\";\n$fa-var-tripadvisor: \"\\f262\";\n$fa-var-trophy: \"\\f091\";\n$fa-var-truck: \"\\f0d1\";\n$fa-var-try: \"\\f195\";\n$fa-var-tty: \"\\f1e4\";\n$fa-var-tumblr: \"\\f173\";\n$fa-var-tumblr-square: \"\\f174\";\n$fa-var-turkish-lira: \"\\f195\";\n$fa-var-tv: \"\\f26c\";\n$fa-var-twitch: \"\\f1e8\";\n$fa-var-twitter: \"\\f099\";\n$fa-var-twitter-square: \"\\f081\";\n$fa-var-umbrella: \"\\f0e9\";\n$fa-var-underline: \"\\f0cd\";\n$fa-var-undo: \"\\f0e2\";\n$fa-var-universal-access: \"\\f29a\";\n$fa-var-university: \"\\f19c\";\n$fa-var-unlink: \"\\f127\";\n$fa-var-unlock: \"\\f09c\";\n$fa-var-unlock-alt: \"\\f13e\";\n$fa-var-unsorted: \"\\f0dc\";\n$fa-var-upload: \"\\f093\";\n$fa-var-usb: \"\\f287\";\n$fa-var-usd: \"\\f155\";\n$fa-var-user: \"\\f007\";\n$fa-var-user-circle: \"\\f2bd\";\n$fa-var-user-circle-o: \"\\f2be\";\n$fa-var-user-md: \"\\f0f0\";\n$fa-var-user-o: \"\\f2c0\";\n$fa-var-user-plus: \"\\f234\";\n$fa-var-user-secret: \"\\f21b\";\n$fa-var-user-times: \"\\f235\";\n$fa-var-users: \"\\f0c0\";\n$fa-var-vcard: \"\\f2bb\";\n$fa-var-vcard-o: \"\\f2bc\";\n$fa-var-venus: \"\\f221\";\n$fa-var-venus-double: \"\\f226\";\n$fa-var-venus-mars: \"\\f228\";\n$fa-var-viacoin: \"\\f237\";\n$fa-var-viadeo: \"\\f2a9\";\n$fa-var-viadeo-square: \"\\f2aa\";\n$fa-var-video-camera: \"\\f03d\";\n$fa-var-vimeo: \"\\f27d\";\n$fa-var-vimeo-square: \"\\f194\";\n$fa-var-vine: \"\\f1ca\";\n$fa-var-vk: \"\\f189\";\n$fa-var-volume-control-phone: \"\\f2a0\";\n$fa-var-volume-down: \"\\f027\";\n$fa-var-volume-off: \"\\f026\";\n$fa-var-volume-up: \"\\f028\";\n$fa-var-warning: \"\\f071\";\n$fa-var-wechat: \"\\f1d7\";\n$fa-var-weibo: \"\\f18a\";\n$fa-var-weixin: \"\\f1d7\";\n$fa-var-whatsapp: \"\\f232\";\n$fa-var-wheelchair: \"\\f193\";\n$fa-var-wheelchair-alt: \"\\f29b\";\n$fa-var-wifi: \"\\f1eb\";\n$fa-var-wikipedia-w: \"\\f266\";\n$fa-var-window-close: \"\\f2d3\";\n$fa-var-window-close-o: \"\\f2d4\";\n$fa-var-window-maximize: \"\\f2d0\";\n$fa-var-window-minimize: \"\\f2d1\";\n$fa-var-window-restore: \"\\f2d2\";\n$fa-var-windows: \"\\f17a\";\n$fa-var-won: \"\\f159\";\n$fa-var-wordpress: \"\\f19a\";\n$fa-var-wpbeginner: \"\\f297\";\n$fa-var-wpexplorer: \"\\f2de\";\n$fa-var-wpforms: \"\\f298\";\n$fa-var-wrench: \"\\f0ad\";\n$fa-var-xing: \"\\f168\";\n$fa-var-xing-square: \"\\f169\";\n$fa-var-y-combinator: \"\\f23b\";\n$fa-var-y-combinator-square: \"\\f1d4\";\n$fa-var-yahoo: \"\\f19e\";\n$fa-var-yc: \"\\f23b\";\n$fa-var-yc-square: \"\\f1d4\";\n$fa-var-yelp: \"\\f1e9\";\n$fa-var-yen: \"\\f157\";\n$fa-var-yoast: \"\\f2b1\";\n$fa-var-youtube: \"\\f167\";\n$fa-var-youtube-play: \"\\f16a\";\n$fa-var-youtube-square: \"\\f166\";\n\n", "$font-size-base: 18px;\r\n\r\n@function toRem($fontpx) {\r\n    @return ($fontpx / $font-size-base) * 1rem;\r\n  }\r\n  \r\n  @mixin font($fontpx) {\r\n    font-size: toRem($fontpx);\r\n\r\n    @if $fontpx >= 40px {\r\n      line-height: 1.25;\r\n    } @else if $fontpx >= 16px {\r\n      line-height: 1.5;\r\n    }\r\n  }\r\n  \r\n  html,\r\n  body,\r\n  h1,\r\n  h2,\r\n  h3,\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    margin: 0;\r\n    padding: 0;\r\n  }\r\n  \r\n  body {\r\n    font-size: $font-size-base;\r\n  }\r\n  \r\n  h1 {\r\n    @include font(40px);\r\n    font-weight: bolder;\r\n  }\r\n  \r\n  h2 {\r\n    @include font(32px);\r\n    font-weight: bold;\r\n  }\r\n  \r\n  h3 {\r\n    @include font(28px);\r\n  }\r\n  \r\n  h4 {\r\n    @include font(24px);\r\n    margin-bottom: 0.2rem;\r\n  }\r\n  \r\n  h5 {\r\n    @include font(20px);\r\n    margin-bottom: 4px;\r\n  }\r\n  \r\n  h6 {\r\n    @include font(16px);\r\n  }\r\n\r\n.font-size-s {\r\n  font-size: 16px;\r\n}\r\n\r\n// .font-color {\r\n//   color: $font-color;\r\n// }\r\n\r\n.text-indent-1 {\r\n  text-indent: 1rem;\r\n}\r\n\r\n.text-gray-500 {\r\n  color: $gray-500;\r\n}\r\n\r\n", "//icon\n\n%iconHover {\n  // cursor: pointer;\n  &:hover {\n    filter: brightness(0.8);\n  }\n  &:active {\n    filter: brightness(1.1);\n  }\n}\n\n// icon 引入\n@mixin icon($iconName, $bgSize) {\n  background-image: url(../images/#{$iconName});\n  background-size: $bgSize;\n}\n\n// 設定 icon 各種尺寸\n@mixin iconSize($iconSize) {\n  height: $iconSize;\n  width: $iconSize;\n}\n\n.icon {\n  &::before {\n    display: inline-block;\n    @include iconSize(1em);\n  }\n  &-size-xs {\n    &::before {\n      display: inline-block;\n      @include iconSize(1.2rem);\n      vertical-align: text-bottom;\n    }\n  }\n  &-size-s {\n    &::before {\n      display: inline-block;\n      @include iconSize(1.4rem);\n      vertical-align: text-bottom;\n    }\n  }\n  &-size-m {\n    &::before {\n      display: inline-block;\n      @include iconSize(2rem);\n      vertical-align: text-bottom;\n    }\n  }\n  &-size-lg {\n    &::before {\n      display: inline-block;\n      @include iconSize(3.5rem);\n      vertical-align: text-bottom;\n    }\n  }\n  &-size-xl {\n    &::before {\n      display: inline-block;\n      @include iconSize(5rem);\n      vertical-align: text-bottom;\n    }\n  }\n  &-announce {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-announce.svg\", 100%);\n    }\n  }\n  &-bigMedal {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-bigMedal.svg\", 100%);\n    }\n  }\n  &-books {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-books.svg\", 100%);\n    }\n  }\n  &-deposit {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-deposit.svg\", 100%);\n    }\n  }\n  &-ecool {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-ecool.png\", 100%);\n    }\n  }\n  &-gift {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-gift.svg\", 100%);\n    }\n  }\n  &-iStory {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-iStory.svg\", 100%);\n    }\n  }\n  &-medal {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-medal.svg\", 100%);\n    }\n  }\n \n  &-search {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-search.png\", 100%);\n    }\n  }\n \n  &-sport {\n    &::before {\n      content: \"\";\n      @include icon(\"icon-sport.svg\", 100%);\n    }\n  }\n \n  // &-info {\n  //   &::before {\n  //     content: \"\";\n  //     @include icon(\"icon-info.svg\", 100%);\n  //     @extend %iconHover;\n  //   }\n  // }\n  \n  \n  // &-ban {\n  //   height: 1rem;\n  //   width: 1rem;\n  //   @extend %iconHover;\n  //   &::before {\n  //     content: \"\";\n  //     @include icon(\"icon-ban.svg\", 100%);\n  //   }\n  // }\n}\n\n\n\n", "/*!\n * jQuery Conveyor Ticker (jConveyorTicker)\n * Description: jQuery plugin to create simple horizontal conveyor belt animated tickers.\n *\n * Copyright (c) 2017 <PERSON>XD Lda\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * Project home:\n *   https://github.com/lluz/jquery-conveyor-ticker\n *\n * Version:  1.1.0\n *\n */\n.jctkr-wrapper,\n.jctkr-wrapper * {\n  box-sizing: border-box;\n}\n.jctkr-wrapper {\n  display: inline-block;\n  position: relative;\n  width: 100%;\n  height:30px;\n  vertical-align: top;\n  overflow: hidden;\n}\n.jctkr-wrapper ul {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100%;\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  white-space: nowrap;\n  font-size: 0;\n  text-align: left;\n  opacity: 0;\n  -webkit-transition: opacity 1s;\n  transition: opacity 1s;\n}\n.jctkr-wrapper.jctkr-initialized ul {\n  opacity: 1;\n}\n.jctkr-wrapper ul li {\n  display: inline-block;\n  font-size: .725rem;\n}\n.jctkr-label {\n  display: inline-block;\n}\n"]}