/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeFourSym/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeFourSym={directory:"SizeFourSym/Regular",family:"STIXSizeFourSym",Ranges:[[688,767,"All"],[768,824,"All"],[8254,8254,"All"],[8400,8431,"All"],[8730,8732,"All"],[9115,9145,"All"],[9180,9185,"All"],[10098,10099,"All"],[10214,10219,"All"],[10627,10630,"All"]],32:[0,0,250,0,0],40:[2566,509,808,124,732],41:[2566,509,808,76,684],47:[2566,509,1309,16,1293],91:[2566,509,661,295,634],92:[2566,509,1309,16,1293],93:[2566,509,661,27,366],95:[-127,177,2500,0,2500],123:[2566,509,1076,173,882],125:[2566,509,1076,194,903],160:[0,0,250,0,0],770:[796,-573,0,-2040,-154],771:[771,-608,0,-2040,-154],8730:[1510,345,1184,112,895],8968:[2566,509,682,295,655],8969:[2566,509,682,27,387],8970:[2566,509,682,295,655],8971:[2566,509,682,27,387],9182:[175,90,2328,0,2328],9183:[837,-572,2328,0,2328],10216:[2566,509,908,113,796],10217:[2566,509,908,112,795]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeFourSym");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeFourSym/Regular/Main.js");
