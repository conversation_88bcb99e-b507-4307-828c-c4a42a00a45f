﻿
@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}
else
{
    using (Html.BeginForm())
    {


        @Html.Hidden("wIsQhisSchool", (bool)ViewBag.wIsQhisSchool)

        <label class="control-label"> * @ViewBag.Panel_Title</label>
                    <br /><br />
        if (ViewBag.wIsQhisSchool == false)
        {
            @Html.Hidden("SCHOOL_NO", (string)ViewBag.SCHOOL_NO)
            @Html.Hidden("IDNO", "")
            <div class="form-group">
                @Html.Label("班級", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                <div class="col-md-9">
                    @Html.DropDownList("CLASS_NO", (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeUSER_NOUseReplaceWith()" })
                    @Html.ValidationMessage("CLASS_NO", "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("是否匯出閱讀認證", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                <div class="col-md-9">
                   否<input type="checkbox" id="ReadYN" name="ReadYN"/>
                </div>

                </div>
                <div class="form-group">
                    @Html.Label("姓名", htmlAttributes: new { @class = "control-label col-md-3", @for = "USER_NO" })
                    <div class="col-md-9">
                        @Html.DropDownList("USER_NO", (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
                        @Html.ValidationMessage("USER_NO", "", new { @class = "text-danger" })
                    </div>
                </div>
                    <div class="form-group text-left">
                        @Html.Label("開始日期", htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.Editor("S_DATE", new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        </div>
                    </div>
                    <div class="form-group text-left">
                        @Html.Label("結束日期", htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.Editor("E_DATE", new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        </div>
                    </div>
        }
        else
        {
            <div class="form-group">
                @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "SCHOOL_NO" })
                <div class="col-md-9">
                    @Html.DropDownList("SCHOOL_NO", (List<SelectListItem>)ViewBag.SCHOOL_NOItems, new { @class = "form-control" })
                    @Html.ValidationMessage("SCHOOL_NO", "", new { @class = "text-danger" })
                </div>
            </div>
                @Html.Hidden("CLASS_NO", "")
                @Html.Hidden("USER_NO", "")
                @Html.Hidden("S_DATE", "")
                @Html.Hidden("E_DATE", "")
                @Html.Hidden("IDNO", (string)ViewBag.IDNO)
        }

        <div class="form-group">
            <div class="col-md-offset-3 col-md-9 text-center">
                <button type="button" class="btn btn-default btn-block" onclick="CK()">匯出</button>
            </div>
        </div>
    }

 



    <script type="text/javascript">

     

        $("#S_DATE,#E_DATE").datepicker({
            dateFormat: "yy/mm/dd",
            changeMonth: true,
            changeYear: true,
            showOn: "button",
            buttonImage: "../Content/img/icon/calendar.gif",
            buttonImageOnly: true,
        });

        window.onload = function () {
            ChangeUSER_NOUseReplaceWith()
        }


        function CK() {
            var Msg = '';

            if ($('#USER_NO').val() == '' && $('#IDNO').val() == '') {
                Msg = Msg + '請選擇「' + $("label[for='USER_NO']").text() + '」\n';
            }

            if (Msg != '') {
                alert(Msg);
            }
            else {
                var StrWhere = '?School_No=' + $('#SCHOOL_NO').val() + '&USER_NO=' + $('#USER_NO').val() + '&S_DATE=' + $('#S_DATE').val() + '&E_DATE=' + $('#E_DATE').val() + '&IDNO=' + $('#IDNO').val() + '&ReadYN=' + $("#ReadYN").prop("checked")
                window.open('@Url.Action("ExportResultView")' + StrWhere+'', '_blank');
            }
        }


        function SetUSER_NODDLEmpty() {
            $('#USER_NO').empty();
            $('#USER_NO').append($('<option></option>').val(' ').text('請選擇' + $("label[for='CLASS_NO']").text() + '...').prop('selected', true));
        }
     
        function ChangeUSER_NOUseReplaceWith() {


            var selectedCLASS_NO = $.trim($('#CLASS_NO option:selected').val());
            if (selectedCLASS_NO.length == 0) {
                SetUSER_NODDLEmpty();
            }
            else {
                $.ajax(
                {
                    url: '@Url.Action("_GetUSER_NODDLHtml", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: 'USER_NO',
                        tagName: 'USER_NO',
                        wSCHOOL_NO: '@ViewBag.SCHOOL_NO',
                        wUSER_NO: '@ViewBag.USER_NO',
                        wCLASS_NO: selectedCLASS_NO,
                        wDATA_ANGLE_TYPE: '@ViewBag.DATA_ANGLE_TYPE'
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        if (data.length > 0) {
                            $('#USER_NO').replaceWith(data);
                        }
                    }
                });
            }
        }
    </script>


}

       