// ADDI06 Common JavaScript Functions - 獎勵系統共用功能
window.ADDI06Common = (function() {
    'use strict';

    return {
        // 獲取表單元素
        getForm: function(formName = 'form1') {
            const form = document.forms[formName];
            if (!form) {
                console.error('ADDI06Common: 找不到表單元素:', formName);
                return null;
            }
            return form;
        },

        // 顯示訊息
        showMessage: function(message, type = 'info') {
            try {
                switch (type) {
                    case 'success':
                        console.log('ADDI06 成功:', message);
                        break;
                    case 'warning':
                        console.warn('ADDI06 警告:', message);
                        break;
                    case 'error':
                        console.error('ADDI06 錯誤:', message);
                        break;
                    default:
                        console.info('ADDI06 訊息:', message);
                        break;
                }
                
                // 可以在這裡添加更複雜的訊息顯示邏輯
                // 例如：toastr、sweetAlert 等
                alert(message);
            } catch (error) {
                console.error('ADDI06Common showMessage 錯誤:', error);
            }
        },

        // 獎勵類型變更處理 - 共用函數
        awardKindChange: function(awardKindSelector = '#IAWARD_KIND', cashSelector = '#CASH', labelSelector = '#lbIAWARD_KIND') {
            try {
                let awardKindText = '';
                const selectedText = $(awardKindSelector + " option:selected").text();
                
                console.log('ADDI06Common 事蹟類型變更:', selectedText);
                
                switch (selectedText) {
                    case "請選擇事蹟":
                        awardKindText = '';
                        $(cashSelector).val('0');
                        break;
                    case "志工":
                        awardKindText = '志工類每學期1次每次50-200點';
                        $(cashSelector).val('50');
                        break;
                    case "校內競賽":
                        awardKindText = '校內競賽每次10-200點';
                        $(cashSelector).val('10');
                        break;
                    case "品德表現":
                        awardKindText = '品德表現類10-200點';
                        $(cashSelector).val('10');
                        break;
                    case "學習表現":
                        awardKindText = '學習表現類10-200點';
                        $(cashSelector).val('10');
                        break;
                    case "領導人":
                        awardKindText = '領導人類獎勵';
                        $(cashSelector).val('0');
                        break;
                    case "七個習慣代言人":
                        awardKindText = '七個習慣代言人獎勵';
                        $(cashSelector).val('0');
                        break;
                    default:
                        awardKindText = '';
                        $(cashSelector).val('0');
                        console.warn('ADDI06Common: 未知的事蹟類型:', selectedText);
                        break;
                }

                $(labelSelector).html(awardKindText);
                
                // 觸發自定義事件，讓其他模組可以監聽
                $(document).trigger('addi06:awardKindChanged', {
                    selectedText: selectedText,
                    awardKindText: awardKindText,
                    cashValue: $(cashSelector).val()
                });
                
                return true;
            } catch (error) {
                console.error('ADDI06Common awardKindChange 錯誤:', error);
                this.showMessage('事蹟類型變更時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        // 獎勵點數驗證 - 共用函數
        validateCash: function(cashValue, awardKind) {
            try {
                const errors = [];
                
                // 基本驗證
                if (!cashValue || cashValue.length <= 0) {
                    errors.push('獎勵點數不能為空');
                    return errors;
                }
                
                if (!this.isNumber(cashValue)) {
                    errors.push('獎勵點數必須為數字');
                    return errors;
                }
                
                const cashNum = parseInt(cashValue);
                
                if (cashNum < 0) {
                    errors.push('獎勵點數不能為負數');
                }
                
                if (cashNum > 999) {
                    errors.push('獎勵點數不能超過999點');
                }
                
                // 根據事蹟類型驗證範圍
                switch (awardKind) {
                    case "志工":
                        if (cashNum > 200 || cashNum < 50) {
                            errors.push('志工類獎勵點數需介於50-200點');
                        }
                        break;
                    case "校內競賽":
                        if (cashNum > 200 || cashNum < 10) {
                            errors.push('校內競賽類獎勵點數需介於10-200點');
                        }
                        break;
                    case "品德表現":
                        if (cashNum > 200 || cashNum < 10) {
                            errors.push('品德表現類獎勵點數需介於10-200點');
                        }
                        break;
                    case "學習表現":
                        if (cashNum > 200 || cashNum < 10) {
                            errors.push('學習表現類獎勵點數需介於10-200點');
                        }
                        break;
                }
                
                return errors;
            } catch (error) {
                console.error('ADDI06Common validateCash 錯誤:', error);
                return ['點數驗證時發生錯誤'];
            }
        },

        // 數字驗證 - 共用函數
        isNumber: function(value) {
            if (!value || value.length === 0) {
                return false;
            }
            return /^\d+$/.test(value);
        },

        // 班級學生資料載入 - 共用函數
        loadStudentsByClass: function(classNo, targetSelector = '#USER_NO', urls = null) {
            try {
                if (!classNo || classNo.length === 0) {
                    this.setStudentDropdownEmpty(targetSelector);
                    return;
                }
                
                // 獲取URL
                let url = '@Url.Action("GetNameData")'; // 預設URL
                if (urls && urls.getNameData) {
                    url = urls.getNameData;
                } else if (window.ADDI06_URLS && window.ADDI06_URLS.getNameData) {
                    url = window.ADDI06_URLS.getNameData;
                }
                
                console.log('ADDI06Common 載入班級學生資料:', classNo);
                
                $.getJSON(url, { Class_No: classNo })
                    .done(function (data) {
                        $(targetSelector).empty();
                        $(targetSelector).append($('<option></option>').val('').text('請選擇學生'));
                        
                        $.each(data, function (i, item) {
                            $(targetSelector).append($('<option></option>').val(item.Value).text(item.Text));
                        });
                        
                        console.log('ADDI06Common 學生資料載入完成，共', data.length, '筆');
                        
                        // 觸發自定義事件
                        $(document).trigger('addi06:studentsLoaded', {
                            classNo: classNo,
                            students: data,
                            targetSelector: targetSelector
                        });
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        console.error('ADDI06Common 載入學生資料時發生錯誤:', textStatus, errorThrown);
                        ADDI06Common.showMessage('載入學生資料失敗，請稍後再試', 'error');
                    });
            } catch (error) {
                console.error('ADDI06Common loadStudentsByClass 錯誤:', error);
                this.showMessage('載入學生資料時發生錯誤，請稍後再試', 'error');
            }
        },

        // 清空學生下拉選單 - 共用函數
        setStudentDropdownEmpty: function(targetSelector = '#USER_NO') {
            try {
                $(targetSelector).empty();
                $(targetSelector).append($('<option></option>').val('').text('請選擇學生'));
                console.log('ADDI06Common 學生選單已清空');
            } catch (error) {
                console.error('ADDI06Common setStudentDropdownEmpty 錯誤:', error);
            }
        },

        // 表單清除功能 - 共用函數
        clearForm: function(formSelector, fieldSelector) {
            try {
                $(formSelector).find(fieldSelector).each(function () {
                    const type = $(this).attr('type');
                    const readonly = $(this).attr('readonly');
                    const tag = this.tagName.toLowerCase();

                    if (readonly !== 'readonly' && readonly !== true) {
                        if (type === 'radio' || type === 'checkbox') {
                            if ($(this).attr("title") === 'Default') {
                                this.checked = true;
                            } else {
                                this.checked = false;
                            }
                        } else if (tag === 'select') {
                            this.selectedIndex = 0;
                        } else if (type === 'text' || type === 'hidden' || type === 'password' || type === 'textarea') {
                            this.value = '';
                        }
                    }
                });
                
                console.log('ADDI06Common 表單已清除');
                return true;
            } catch (error) {
                console.error('ADDI06Common clearForm 錯誤:', error);
                return false;
            }
        },

        // 獲取URL配置
        getUrls: function() {
            return window.ADDI06_ADD_URLS ||
                   window.ADDI06_EDIT_URLS ||
                   window.ADDI06_LIST_URLS ||
                   window.ADDI06_URLS ||
                   {};
        },

        // 初始化共用功能
        init: function() {
            console.log('ADDI06Common 已初始化');
            
            // 設置全局錯誤處理
            window.addEventListener('error', function(e) {
                console.error('ADDI06 頁面錯誤:', e);
            });
        }
    };
})();

// 頁面載入完成後初始化
$(document).ready(function() {
    ADDI06Common.init();
});
