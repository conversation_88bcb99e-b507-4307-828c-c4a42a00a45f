﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using Spire.Doc;
using Spire.Doc.Documents;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 寫信給耶誕老人
    /// </summary>
    public class ZZZI37Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI37";

        private readonly ZZZI37Service Service = new ZZZI37Service();
        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private bool IsAdmin = false;

        private string SCHOOL_NO = string.Empty;

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string USER_NO = string.Empty;

        public ActionResult Index()
        {
            this.Shared();
            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value });

            List<SelectListItem> CountryNoSelectItem = new List<SelectListItem>();
            CountryNoSelectItem.Add(new SelectListItem() { Text = "請選擇", Value = "" });
            CountryNoSelectItem.Add(new SelectListItem() { Text = "法國", Value = "1" });
            CountryNoSelectItem.Add(new SelectListItem() { Text = "德國", Value = "2" });
            CountryNoSelectItem.Add(new SelectListItem() { Text = "斯洛伐克", Value = "3" });
            ViewBag.CountryNo = CountryNoSelectItem;
            return View();
        }

        public ActionResult ExportResultView(ZZZI37IndexViewModel model)
        {
            this.Shared();
            user = UserProfileHelper.Get();
            if (string.IsNullOrWhiteSpace(model.SCHOOL_NO))
            {
                model.SCHOOL_NO = user.SCHOOL_NO;
            }
            var BDM01 = db.BDMT01.Where(x => x.SCHOOL_NO == model.SCHOOL_NO).FirstOrDefault();
            model.EngCity = BDM01.EngCity ?? "";
            model.EngADDRESS = BDM01.EngADDR ?? "";
            model = this.Service.GetListData(model, ref db);
            ViewBag.ImgPath = ADDI01Controller.GetSysADDI01IMGPath(model.SCHOOL_NO);

            switch (model.CountryNO)
            {
                case "1":
                    model.ADDRESS = "Père Noël F - 33500 Libourne France";

                    break;

                case "2":
                    model.ADDRESS = "Weihnachtsmann Weihnachtspostfiliale D - 16798 Himmelpfort Germany";
                    break;

                case "3":
                    model.ADDRESS = "JEŽIŠKO 99 999 JEŽIŠKO Slovakia";
                    break;
            }
            return View(model);
        }

        [HttpPost]
        [ValidateInput(false)]
        [ValidateAntiForgeryToken]
        public ActionResult ToWord(string HtmlCode)
        {
            Document doc = new Document();
            Section s = doc.AddSection();

            Paragraph paragraph = doc.AddSection().AddParagraph();
            paragraph.AppendHTML(HtmlCode);

            //Insert Hmtl styled text to paragraph
            byte[] toArray = null;
            using (MemoryStream ms1 = new MemoryStream())
            {
                doc.SaveToStream(ms1, FileFormat.Docm2013);
                //save to byte array
                toArray = ms1.ToArray();
            }
            return File(toArray, "application/msword;charset=utf-8", $"寫信給耶誕老人{DateTime.Now.ToString("yyyyMMdd")}.docx");
        }

        public static string GetSetDirectoryPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            ReturnImgUrl = $@"{UploadImageRoot}{Bre_NO}\";

            return ReturnImgUrl;
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
            IsAdmin = HRMT24_ENUM.CheckQAdmin(user);
            ViewBag.IsAdmin = IsAdmin;
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}