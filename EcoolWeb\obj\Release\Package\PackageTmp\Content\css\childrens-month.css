@charset "UTF-8";
html,
body {
  font-size: 100%;
  font-size: 1rem;
  background: #fffde3; }
  @media (min-width: 1920px) {
    html,
    body {
      font-size: 1vw; } }

body,
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
strong {
  font-family: 'Microsoft JhengHei', '微軟正黑體', 'Helvetica Neue', Helvetica, Arial, 'STHeiti', sans-serif; }

h4 strong {
  font-size: 24;
  font-size: 1.5rem; }

.label {
  font-size: 26px;
  font-size: 1.7rem;
  border-radius: 2rem; }

.mb-3 {
  margin-bottom: 1.5rem !important; }

.panel-heading h1 {
  margin: 0;
  font-size: 53px;
  font-size: 3.31rem;
  line-height: 1;
  font-weight: 600; }

.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  height: 4rem;
  font-size: 24px;
  font-size: 1.5rem; }

body > div.row,
#MainView .row {
  margin: 0; }

.btn {
  margin: 0;
  font-size: 31px;
  font-size: 1.7rem; }

.label-noplay {
  background-color: #e9ecff;
  color: #394383;
  border: 1px solid #abb6ff; }

.title-bar {
  display: block;
  font-size: 1.7rem;
  font-weight: 600;
  padding: .2rem 1rem;
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 1rem;
  width: 320px;
  white-space: nowrap;
  margin-bottom: 1rem; }

.svg-award {
  width: 2.25rem;
  fill: #c72c2c; }

.svg-map {
  width: 2.25rem;
  fill: #394383; }

.childrens-user {
  display: block;
  color: #454545;
  font-size: 41px;
  font-size: 3.55rem; }

.childrens-val {
  display: block;
  color: red;
  font-size: 37px;
  font-size: 3.55rem; }

.childrens-item-title {
  display: inline-block;
  padding: .4rem 1.5rem;
  margin-bottom: 1rem;
  font-size: 24;
  font-size: 1.5rem;
  font-weight: 600;
  border-radius: 2rem;
  background: #394383;
  color: #fff;
  box-shadow: 0 5px 0 #dedde0; }

.childrens-award {
  background: #c72c2c;
  box-shadow: 0 5px 0 #8e1212; }
  .childrens-award .glyphicon {
    font-size: 2.7rem;
    vertical-align: middle; }
  .childrens-award-other {
    background: #de5c00;
    box-shadow: 0 5px 0 #952200; }

.childrens-level {
  font-size: 2rem;
  font-weight: 600;
  border-radius: .23em;
  padding: .3rem 1rem;
  margin-bottom: .7rem;
  border: 1px solid #abb6ff; }
  .childrens-level-success {
    background-color: #c0dfc0;
    border-color: #fff;
    color: #fff; }
  .childrens-level-ready {
    background-color: #ffce45;
    border-color: #b27622;
    color: #333333; }
  .childrens-level-noplay {
    background-color: #e9ecff;
    color: #394383; }

.childrens-awards-block {
  padding: 1rem;
  background-color: #fee;
  border: 6px dashed #ffd7d7;
  border-radius: 2rem;
  font-size: 1.52rem;
  font-weight: 600; }
  .childrens-awards-block .title-bar {
    background-color: #fff;
    color: #c72c2c;
    border: 2px solid #ffd7d7;
    margin: -1.4rem 0 1rem -1.5rem; }

.childrens-info-block {
  padding: 1rem;
  border: 2px solid #f4f4f4; }
  .childrens-info-block .title-bar {
    background-color: #fff;
    color: #394383;
    border: 2px solid #ededed;
    margin: -1.1rem 0 1rem -1.1rem; }

#DivAddButton {
  right: -9rem; }

.bg-lottery {
  overflow: hidden;
  padding: 0;
  margin: 0;
  background-color: #FFE633;
  background-image: linear-gradient(90deg, transparent 50%, #FFDA23 50%);
  background-size: 1.875rem 0.812rem;
  min-height: 100vh; }
  @media (max-width: 767px) {
    .bg-lottery {
      overflow-y: auto; } }
  .bg-lottery::before {
    display: block;
    pointer-events: auto;
    width: 75vw;
    height: 17vh;
    content: '';
    background: url(../img/<EMAIL>) center bottom no-repeat;
    background-size: 70% auto;
    position: absolute;
    top: 0;
    left: -13vw; }
  .bg-lottery::after {
    display: block;
    pointer-events: auto;
    width: 75vw;
    height: 17vh;
    content: '';
    background: url(../img/<EMAIL>) center bottom no-repeat;
    background-size: 70% auto;
    position: absolute;
    top: 0;
    right: -15vw; }
  .bg-lottery-playing::before {
    animation: gotop 2s 0s 1 ease forwards;
    -webkit-animation: gotop 2s 0s 1 ease forwards;
    -moz-animation: gotop 2s 0s 1 ease forwards;
    -o-animation: gotop 2s 0s 1 ease forwards; }
  .bg-lottery-playing::after {
    animation: gotop 2s 0s 1 ease forwards;
    -webkit-animation: gotop 2s 0s 1 ease forwards;
    -moz-animation: gotop 2s 0s 1 ease forwards;
    -o-animation: gotop 2s 0s 1 ease forwards; }
  .bg-lottery-award::before {
    background-size: 40% auto;
    top: -9vh;
    left: -23vw; }
    @media (max-width: 767px) {
      .bg-lottery-award::before {
        top: -14vh; } }
  .bg-lottery-award::after {
    background-size: 40% auto;
    top: -9vh;
    right: -22vw; }
    @media (max-width: 767px) {
      .bg-lottery-award::after {
        top: -14vh; } }

.ready-box {
  overflow: hidden;
  position: relative;
  min-height: 100vh;
  background: url(../img/<EMAIL>) no-repeat center center, url(../img/<EMAIL>) no-repeat center center, linear-gradient(to bottom, rgba(125, 185, 232, 0) 75%, #B4874C 75%, #B4874C 100%);
  background-size: auto 70%, auto 100%, auto 100%; }
  @media (max-width: 767px) {
    .ready-box {
      background-size: auto 45%, auto 50%, auto 100%; } }
  .ready-box img {
    display: block;
    opacity: 0;
    height: 100vh;
    width: auto;
    margin: 0 auto; }
    @media (max-width: 767px) {
      .ready-box img {
        height: auto;
        width: 100%;
        padding-top: 60%; } }
  .ready-box::before {
    display: block;
    width: 60%;
    height: 100vh;
    content: '';
    pointer-events: auto;
    background: url(../img/<EMAIL>) center bottom no-repeat;
    background-size: 70% auto;
    position: absolute;
    left: -16%;
    bottom: -5%;
    animation: bounce 6s 2s infinite ease reverse;
    -webkit-animation: bounce 6s 2s infinite ease reverse;
    -moz-animation: bounce 6s 2s infinite ease reverse;
    -o-animation: bounce 6s 2s infinite ease reverse; }
  .ready-box::after {
    display: block;
    width: 60%;
    height: 100vh;
    content: '';
    pointer-events: auto;
    background: url(../img/<EMAIL>) center bottom no-repeat;
    background-size: 70% auto;
    position: absolute;
    right: -16%;
    bottom: -12%;
    animation: bounce 6s 3s infinite ease reverse;
    -webkit-animation: bounce 6s 3s infinite ease reverse;
    -moz-animation: bounce 6s 3s infinite ease reverse;
    -o-animation: bounce 6s 3s infinite ease reverse; }
  .ready-box-playing {
    background: linear-gradient(to bottom, rgba(125, 185, 232, 0) 75%, #B4874C 75%, #B4874C 100%); }
    .ready-box-playing .btn-start {
      display: none; }
    .ready-box-playing img {
      animation: fadeIn 1s 1s 1 ease forwards;
      -webkit-animation: fadeIn 1s 1s 1 ease forwards;
      -moz-animation: fadeIn 1s 1s 1 ease forwards;
      -o-animation: fadeIn 1s 1s 1 ease forwards; }
    .ready-box-playing::before {
      animation: goleft 2s 0s 1 ease forwards;
      -webkit-animation: goleft 2s 0s 1 ease forwards;
      -moz-animation: goleft 2s 0s 1 ease forwards;
      -o-animation: goleft 2s 0s 1 ease forwards; }
    .ready-box-playing::after {
      animation: goright 2s 0s 1 ease forwards;
      -webkit-animation: goright 2s 0s 1 ease forwards;
      -moz-animation: goright 2s 0s 1 ease forwards;
      -o-animation: goright 2s 0s 1 ease forwards; }

.btn-start {
  background: #ff0034;
  background: -moz-linear-gradient(top, #ff0034 0%, #c20000 100%);
  background: -webkit-linear-gradient(top, #ff0034 0%, #c20000 100%);
  background: linear-gradient(to bottom, #ff0034 0%, #c20000 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff0034', endColorstr='#c20000', GradientType=0);
  color: #fff;
  position: absolute;
  padding: .7rem;
  left: 39%;
  right: 39%;
  width: 22%;
  bottom: 24%;
  font-family: 'Microsoft JhengHei', '微軟正黑體', 'Helvetica Neue', Helvetica, Arial, 'STHeiti', sans-serif;
  font-size: 3.18rem;
  font-weight: 600;
  letter-spacing: .5rem;
  border-radius: 1.5rem;
  border: 6px solid #710000;
  box-shadow: 0 6px 0 0px #960000, 0 4px 1px 6px #fff;
  animation: bounceIn 1s 1s 1 ease;
  -webkit-animation: bounceIn 1s 1s 1 ease;
  -moz-animation: bounceIn 1s 1s 1 ease;
  -o-animation: bounceIn 1s 1s 1 ease; }
  .btn-start:hover, .btn-start:active, .btn-start:focus {
    background: #ff0034;
    background: -moz-linear-gradient(top, #ff0034 0%, #c20000 100%);
    background: -webkit-linear-gradient(top, #ff0034 0%, #c20000 100%);
    background: linear-gradient(to bottom, #ff0034 0%, #c20000 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff0034', endColorstr='#c20000', GradientType=0);
    cursor: pointer;
    border: 2px solid #ff8383;
    box-shadow: 0 0 0 0px #960000, 0 0 1px 2px #fff;
    color: #fff; }
  @media (max-width: 1025px) {
    .btn-start {
      left: calc(50% - 160px);
      right: auto;
      width: 320px; } }

.award-head {
  background: #ff0034;
  background: -moz-linear-gradient(top, #ff0034 0%, #c20000 100%);
  background: -webkit-linear-gradient(top, #ff0034 0%, #c20000 100%);
  background: linear-gradient(to bottom, #ff0034 0%, #c20000 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff0034', endColorstr='#c20000', GradientType=0);
  box-shadow: 0 0 0 .3rem #fff;
  color: #fff;
  width: 30%;
  display: block;
  margin: 0 auto;
  text-align: center;
  font-size: 3rem;
  border-radius: 3rem; }
  @media (max-width: 1023px) {
    .award-head {
      width: 90%; } }

.list-award {
  padding: 0 0 1rem 0;
  margin: 0 1rem;
  display: flex;
  flex-wrap: wrap;
  list-style: none; }
  .list-award li {
    list-style: none;
    display: flex;
    min-height: 11vh;
    align-items: center;
    justify-content: space-between;
    padding: .5rem 1.5rem;
    margin: .7rem .5rem;
    flex: 1 1 40%;
    border-radius: 1rem;
    background-color: #fff;
    box-shadow: 0 0.351rem 0.625rem rgba(119, 0, 0, 0.57);
    font-size: 2.12rem;
    font-weight: 600;
    line-height: 1.2; }
    @media (max-width: 1025px) {
      .list-award li {
        font-size: 1.51rem;
        margin: .3rem; } }
    @media (max-width: 767px) {
      .list-award li {
        flex-direction: column;
        min-height: auto;
        flex: 1 1 100%;
        font-size: 1.51rem;
        align-items: start; } }
    .list-award li > span:first-child {
      margin-right: auto;
      flex: 0 0 63%; }
      @media (max-width: 767px) {
        .list-award li > span:first-child {
          flex: 1 1 auto; } }
    .list-award li small {
      text-align: right;
      display: block;
      color: #0020d1;
      font-size: 73%;
      padding: .3rem 0; }

.award-box {
  padding-top: 1.5rem;
  min-height: 100vh;
  background: url(../img/<EMAIL>) no-repeat 5% 130%, linear-gradient(to bottom, rgba(125, 185, 232, 0) 96%, #B4874C 96%, #B4874C 100%);
  background-size: auto 38%, auto 100%; }
  .award-box .carousel {
    margin-top: 1rem;
    margin-bottom: 3rem; }
    @media (max-width: 767px) {
      .award-box .carousel {
        margin-bottom: 5rem; } }
  .award-box .carousel-indicators {
    bottom: -4rem;
    counter-reset: section; }
    .award-box .carousel-indicators li,
    .award-box .carousel-indicators .active {
      width: 4rem;
      height: 4rem;
      border-radius: 4rem;
      border: .2rem solid #593300;
      background-color: #9c6814;
      text-indent: 0;
      box-shadow: 0 0.2rem 0 #593300; }
      .award-box .carousel-indicators li::before,
      .award-box .carousel-indicators .active::before {
        counter-increment: section;
        content: counter(section);
        font-size: 2.51rem;
        font-weight: 600;
        color: #eee; }
    .award-box .carousel-indicators .active,
    .award-box .carousel-indicators li:hover {
      border-color: #f00027;
      background-color: #f00027;
      box-shadow: 0 0.2rem 0 #a1001b, 0 .1rem 0 .2rem #fff; }
      .award-box .carousel-indicators .active::before,
      .award-box .carousel-indicators li:hover::before {
        color: #fff; }

.award-foot {
  padding: 0 1.5rem;
  margin-top: 3rem;
  padding-bottom: 1rem;
  text-align: right; }
  .award-foot .btn-default {
    border-width: 0;
    padding: .5rem .5rem .5rem 1rem;
    font-weight: 600;
    letter-spacing: .5rem;
    border-radius: .7rem;
    background-color: #A1A1A1;
    box-shadow: 0 0.3rem 0 #8F7E7E;
    color: #fff;
    margin-right: 1rem; }
    .award-foot .btn-default:hover {
      box-shadow: inset 0 0 0 10rem rgba(0, 0, 0, 0.3); }
  .award-foot .btn-success {
    border-width: 0;
    padding: .5rem .5rem .5rem 1rem;
    width: 15vw;
    font-weight: 600;
    letter-spacing: .5rem;
    border-radius: .7rem;
    background: #00b793;
    background: -moz-linear-gradient(top, #00b793 0%, #00816d 100%);
    background: -webkit-linear-gradient(top, #00b793 0%, #00816d 100%);
    background: linear-gradient(to bottom, #00b793 0%, #00816d 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00b793', endColorstr='#00816d', GradientType=0);
    box-shadow: 0 0.3rem 0 #006B2E; }
    @media (max-width: 767px) {
      .award-foot .btn-success {
        width: 50%; } }
    .award-foot .btn-success:hover {
      box-shadow: inset 0 0 0 10rem rgba(0, 0, 0, 0.3); }

@-webkit-keyframes bounce {
  from,
  20%,
  53%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0); } }

@keyframes bounce {
  from,
  20%,
  53%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0); } }

@-webkit-keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03); }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97); }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }

@keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03); }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97); }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }

@keyframes goleft {
  from { }
  to {
    background-size: 60% auto;
    left: -31.5%;
    bottom: 0; } }

@-webkit-keyframes goleft {
  from { }
  to {
    background-size: 60% auto;
    left: -31.5%;
    bottom: 0; } }

@keyframes goright {
  from { }
  to {
    background-size: 60% auto;
    right: -30.2%;
    bottom: -5%; } }

@-webkit-keyframes goright {
  from { }
  to {
    background-size: 60% auto;
    right: -30.2%;
    bottom: -5%; } }

@keyframes gotop {
  from { }
  to {
    top: -17vh; } }

@-webkit-keyframes gotop {
  from { }
  to {
    top: -17vh; } }

@-webkit-keyframes fadeIn {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }

@keyframes fadeIn {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }
