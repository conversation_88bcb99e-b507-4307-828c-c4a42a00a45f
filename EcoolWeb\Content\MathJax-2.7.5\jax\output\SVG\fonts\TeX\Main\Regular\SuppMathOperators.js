/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Main/Regular/SuppMathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS.MathJax_Main,{10815:[683,0,750,28,721,"28 660Q28 676 31 679T46 683H50Q87 681 182 681Q217 681 247 681T294 682T315 682Q321 682 323 682T328 679T331 673T332 660Q332 643 328 640T304 637Q239 637 231 626Q229 620 229 334V46H520V334Q520 620 518 626Q510 637 445 637Q426 637 422 640T417 660Q417 675 420 678T432 682H435Q437 682 467 682T569 681T671 681T703 682Q714 682 717 679T721 660Q721 643 717 640T693 637Q628 637 620 626Q619 623 619 342Q619 60 620 57Q628 46 693 46Q714 46 717 43T721 23Q721 5 715 1Q713 0 374 0Q36 0 34 1Q28 5 28 23Q28 40 31 43T56 46Q121 46 129 57Q131 63 131 342Q131 620 129 626Q121 637 56 637Q35 637 32 640T28 660"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Regular/SuppMathOperators.js");
