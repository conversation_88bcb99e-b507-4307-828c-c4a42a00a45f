﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Dapper;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using MvcPaging;


namespace EcoolWeb.Controllers
{
    public class DepositCashViewModel
    {
        public string TokenID { get; set; }

        public int CashIn { get; set; }

        public string SCHOOL_NO { get; set; }

        public string UserNO { get; set; }

        public string SourceType { get; set; }

        public string SourceNo { get; set; }

        public string LogDesc { get; set; }
    }

    public class DepositCashReturnViewModel
    {
        public int ErrorCode { get; set; }

        public string ErrorMessage { get; set; }

        public int CashAvailable { get; set; }

        public int CashMax
        {
            get { return 500; }
            set { }
        }
    }

    public class DepositCashApiController : ApiBase
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        [HttpPost, HttpGet]
        [AllowAnonymous]
        public DepositCashReturnViewModel DepositCash(DepositCashViewModel dData)
        {
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            DepositCashReturnViewModel Result = new DepositCashReturnViewModel();
            Result.ErrorCode = 0;

            if (dData.TokenID != "Ez97283784")
            {
                Result.ErrorCode = (int)eErrorCode.E302;
                return Result;
            }

            //目前只限南湖403612
            if (dData.SCHOOL_NO != "403612" && dData.SCHOOL_NO != "111111")
            {
                Result.ErrorCode = (int)eErrorCode.E101;
                Result.ErrorMessage = "現階段只開放南湖國小與一間國小使用";
                return Result;
            }

            //酷幣必須大於等於0
            if (dData.CashIn <0)
            {
                Result.ErrorCode = (int)eErrorCode.E202;
                Result.ErrorMessage = "酷幣必須大於等於0";
                return Result;
            }

            string strMessage =null;
            HRMT01 aStudent = HomeController.GetUser(dData.SCHOOL_NO, dData.UserNO, out strMessage);
            if (string.IsNullOrWhiteSpace(strMessage)==false)
            {
                Result.ErrorCode = (int)eErrorCode.E201;
                Result.ErrorMessage = strMessage;
                return Result;
            }
            if (aStudent.USER_TYPE!=UserType.Student)
            {
                Result.ErrorCode = (int)eErrorCode.E201;
                Result.ErrorMessage = "僅限學生身分";
                return Result;
            }

            CashHelper.AddCash(null, dData.CashIn, dData.SCHOOL_NO, dData.UserNO, dData.SourceType, dData.SourceNo, dData.LogDesc, true, ref db, "", "",ref valuesList);


            return Result;
        }

        // POST api/<controller>
        public void Post([FromBody]string value)
        {
        }

        // PUT api/<controller>/5
        public void Put(int id, [FromBody]string value)
        {
        }

        // DELETE api/<controller>/5
        public void Delete(int id)
        {
        }
    }


    public enum eErrorCode : int
    {
        /// <summary>
        /// 101=學校不存在
        /// </summary>
        E101 = 101,

        /// <summary>
        /// 102=帳號已經存在
        /// </summary>
        E102 = 102,

        /// <summary>
        /// 103=欄位長度超出限制
        /// </summary>
        E103 = 103,

        /// <summary>
        /// 104=參數不可空白或NULL
        /// </summary>
        E104 = 104,

        /// <summary>
        /// 201=帳號不存在
        /// </summary>
        E201 = 201,

        /// <summary>
        /// 202=酷幣值不符合規定
        /// </summary>
        E202 = 202,

        /// <summary>
        ///  301=此交易早已完成，本次不再處理
        /// </summary>
        E301 = 301,

        /// <summary>
        ///  302=TokenID授權碼錯誤
        /// </summary>
        E302 = 302,

        /// <summary>
        /// -1=內部作業錯誤
        /// </summary>
        E_1 = -1
    }

    public class ErrorCodeHelper
    {
        static public string ThrowErrorCode(int code, string ErrMsg)
        {
            return code.ToString();
        }
    }
}