﻿//------------------------------------------------------------------------------
// <auto-generated>
//     這段程式碼是由工具產生的。
//     執行階段版本:4.0.30319.42000
//
//     對這個檔案所做的變更可能會造成錯誤的行為，而且如果重新產生程式碼，
//     變更將會遺失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace EcoolWeb.EzCodeStudMap {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EzCodeStudData", Namespace="http://schemas.datacontract.org/2004/07/EzCodeStudMap")]
    [System.SerializableAttribute()]
    public partial class EzCodeStudData : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string cardExternalNoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string cardInternalNoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string eduNumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string studIdNumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string studNameField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string cardExternalNo {
            get {
                return this.cardExternalNoField;
            }
            set {
                if ((object.ReferenceEquals(this.cardExternalNoField, value) != true)) {
                    this.cardExternalNoField = value;
                    this.RaisePropertyChanged("cardExternalNo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string cardInternalNo {
            get {
                return this.cardInternalNoField;
            }
            set {
                if ((object.ReferenceEquals(this.cardInternalNoField, value) != true)) {
                    this.cardInternalNoField = value;
                    this.RaisePropertyChanged("cardInternalNo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string eduNumber {
            get {
                return this.eduNumberField;
            }
            set {
                if ((object.ReferenceEquals(this.eduNumberField, value) != true)) {
                    this.eduNumberField = value;
                    this.RaisePropertyChanged("eduNumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string studIdNumber {
            get {
                return this.studIdNumberField;
            }
            set {
                if ((object.ReferenceEquals(this.studIdNumberField, value) != true)) {
                    this.studIdNumberField = value;
                    this.RaisePropertyChanged("studIdNumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string studName {
            get {
                return this.studNameField;
            }
            set {
                if ((object.ReferenceEquals(this.studNameField, value) != true)) {
                    this.studNameField = value;
                    this.RaisePropertyChanged("studName");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="EzCodeStudMap.IEzCodeStudMap")]
    public interface IEzCodeStudMap {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IEzCodeStudMap/GetData", ReplyAction="http://tempuri.org/IEzCodeStudMap/GetDataResponse")]
        EcoolWeb.EzCodeStudMap.EzCodeStudData[] GetData(string eduNumber, string grade, string updateDate, string userCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IEzCodeStudMap/GetData", ReplyAction="http://tempuri.org/IEzCodeStudMap/GetDataResponse")]
        System.Threading.Tasks.Task<EcoolWeb.EzCodeStudMap.EzCodeStudData[]> GetDataAsync(string eduNumber, string grade, string updateDate, string userCode);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IEzCodeStudMapChannel : EcoolWeb.EzCodeStudMap.IEzCodeStudMap, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class EzCodeStudMapClient : System.ServiceModel.ClientBase<EcoolWeb.EzCodeStudMap.IEzCodeStudMap>, EcoolWeb.EzCodeStudMap.IEzCodeStudMap {
        
        public EzCodeStudMapClient() {
        }
        
        public EzCodeStudMapClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public EzCodeStudMapClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public EzCodeStudMapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public EzCodeStudMapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public EcoolWeb.EzCodeStudMap.EzCodeStudData[] GetData(string eduNumber, string grade, string updateDate, string userCode) {
            return base.Channel.GetData(eduNumber, grade, updateDate, userCode);
        }
        
        public System.Threading.Tasks.Task<EcoolWeb.EzCodeStudMap.EzCodeStudData[]> GetDataAsync(string eduNumber, string grade, string updateDate, string userCode) {
            return base.Channel.GetDataAsync(eduNumber, grade, updateDate, userCode);
        }
    }
}
