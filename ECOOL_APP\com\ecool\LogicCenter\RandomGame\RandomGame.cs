﻿using ECOOL_APP.com.ecool.LogicCenter.Interfaces;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.LogicCenter.RandomGame
{
    public class RandomGame<T> where T : new()
    {
        public RandomGame(IEnumerable<T> iteration,
            Func<T, string> groupingBy)
        {
            LotteryGroupsBy(iteration, groupingBy);
        }

        /// <summary>
        /// 總抽樣母體 => 已分群
        /// </summary>
        public List<IGroupWeight<T>> LotteryGroups { get; set; }

        /// <summary>
        /// 抽出結果
        /// </summary>
        public List<T> LuckyDrawResults { get; set; }

        /// <summary>
        /// 抽獎
        /// </summary>
        /// <param name="drawCount">抽出數量</param>
        public List<T> LuckyDraw(int drawCount)
        {
            //抽出結果
            if (LuckyDrawResults == null) LuckyDrawResults = new List<T>();

            var rand = new Random(Guid.NewGuid().GetHashCode());
            double choice;
            double sum;
            double totalweight;

            List<string> drawGroups = new List<string>();

            // 抽獎次數迴圈
            for (int count = 0; count < drawCount; count++)
            {
                totalweight = LotteryGroups.Sum(g => g.DataCount * g.Weights);
                sum = 0;

                // 抽群組
                choice = rand.NextDouble() * totalweight;
                for (int i = 0; i <= LotteryGroups.Count - 1; i++)
                {
                    if (LotteryGroups[i].DataCount == 0) continue;

                    sum += LotteryGroups[i].DataCount * LotteryGroups[i].Weights;

                    if (choice < sum)
                    {
                        drawGroups.Add(LotteryGroups[i].GroupName);
                        LotteryGroups[i].ReduceWeightsAndCount();

                        // 再從群組內抽出資料
                        foreach (var draw in LotteryGroups[i].Datas)
                        {
                            if (!LuckyDrawResults.Contains(draw))
                            {
                                LuckyDrawResults.Add(draw);
                                break;
                            }
                        }
                        break;
                    }
                }
            }
            return LuckyDrawResults;
        }

        /// <summary>
        /// 區分群組
        /// </summary>
        /// <param name="iteration">母體資料</param>
        /// <param name="groupingBy">分類方法</param>
        public void LotteryGroupsBy(IEnumerable<T> iteration, Func<T, string> groupingBy)
        {
            // 資料分群
            LotteryGroups = iteration
                .GroupBy(x => groupingBy(x))
                .Select(g => (IGroupWeight<T>)new LotteryGroup<T>(g.Key, g.Count(), 100, g))
                .ToList();

            // 打亂排序
            var rand = new Random(Guid.NewGuid().GetHashCode());
            LotteryGroups.ForEach(lg => lg.Datas = lg.Datas.OrderBy(d => rand.Next()));
        }
    }
}
