﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Mvc;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;


namespace ECOOL_APP.com.ecool.Models.entity
{

        public class uBET01
        {


            ///Summary
            ///
            ///Summary
            [DisplayName("CLASS_TYPE")]
            [Required]
            [StringLength(20)]
            public string CLASS_TYPE { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("CLASS_NAME")]
            [StringLength(100)]
            public string CLASS_NAME { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("ORDER_BY")]
            public int ORDER_BY { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("SHOW_COUNT")]
            public int SHOW_COUNT { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("CRE_PERSON")]
            [StringLength(100)]
            public string CRE_PERSON { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("CRE_DATE")]
            public DateTime CRE_DATE { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("CHG_PERSON")]
            [StringLength(100)]
            public string CHG_PERSON { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("CHG_DATE")]
            public DateTime CHG_DATE { get; set; }

            ///Summary
            ///
            ///Summary
            [DisplayName("MEMO")]
            [StringLength(100)]
            public string MEMO { get; set; }

        }

}
