﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.1" />
  </startup>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <connectionStrings>
    <!--<add name="ECOOL_DEVEntities" connectionString="metadata=res://*/EF.ECoolModel.csdl|res://*/EF.ECoolModel.ssdl|res://*/EF.ECoolModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=.\sqlexpress;initial catalog=ECOOL_DEV;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" /></connectionStrings>-->
    <!--<add name="ECOOL_DEVEntities" connectionString="metadata=res://*/EF.ECoolModel.csdl|res://*/EF.ECoolModel.ssdl|res://*/EF.ECoolModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***********;initial catalog=ECOOL_TEST;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="ECOOL_DEVEntities" connectionString="metadata=res://*/EF.ECoolDev.csdl|res://*/EF.ECoolDev.ssdl|res://*/EF.ECoolDev.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=EZ-5101N-0001\MSSQL2014;initial catalog=ECOOL_DEV;user id=sa;password=********;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="ECOOL_DEVEntities" connectionString="metadata=res://*/EF.ECoolDev.csdl|res://*/EF.ECoolDev.ssdl|res://*/EF.ECoolDev.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=.\;initial catalog=ECOOL_TEST;integrated security=True;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <add name="ECOOL_DEVEntities" connectionString="metadata=res://*/EF.ECoolDev.csdl|res://*/EF.ECoolDev.ssdl|res://*/EF.ECoolDev.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=.\SQLEXPRESS;initial catalog=ECOOL_DEV;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>