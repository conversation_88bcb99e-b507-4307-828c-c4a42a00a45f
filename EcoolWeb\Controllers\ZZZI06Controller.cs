﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.Util;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI06Controller : Controller
    {
        private static ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        // GET: ZZZI06
        [CheckPermission]
        public ActionResult QUERY(FormCollection viewADDI04)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            //取出該校所有的小朋友閱讀護照通過認證暫存表
            List<uADDT04> litmpADDI04 = new List<uADDT04>();
            //待回傳閱讀護照通過認證
            List<uADDT04Q02> liADDT04Q02 = new List<uADDT04Q02>();

            short iGRADE = (viewADDI04["ddlGrade"] != null) ? Convert.ToInt16(viewADDI04["ddlGrade"]) : Convert.ToInt16(1);

            if (iGRADE != 0)
            {
                litmpADDI04 = (from a04 in db.ADDT04
                               join h01 in db.HRMT01
                                     on new { a04.SCHOOL_NO, a04.USER_NO }
                                 equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a04.PASS_YN == "Y" && h01.GRADE == iGRADE && a04.SCHOOL_NO == SchoolNO
                               orderby
                                 a04.SCHOOL_NO,
                                 h01.CLASS_NO
                               select new uADDT04
                               {
                                   SCHOOL_NO = h01.SCHOOL_NO,
                                   CLASS_NO = h01.CLASS_NO,
                                   NAME = h01.NAME,
                                   SONAME = (h01.SEAT_NO + h01.NAME),
                                   GRADE = a04.GRADE,
                                   PASS_DATE = a04.PASS_DATE,
                                   USER_NO = h01.USER_NO,
                                   CASH_YN = a04.CASH_YN
                               }).ToList();
            }
            else
            {
                litmpADDI04 = (from a04 in db.ADDT04
                               join h01 in db.HRMT01
                                     on new { a04.SCHOOL_NO, a04.USER_NO }
                                 equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a04.PASS_YN == "Y" && a04.SCHOOL_NO == SchoolNO
                               orderby
                                 a04.SCHOOL_NO,
                                 h01.CLASS_NO
                               select new uADDT04
                               {
                                   SCHOOL_NO = h01.SCHOOL_NO,
                                   CLASS_NO = h01.CLASS_NO,
                                   NAME = h01.NAME,
                                   SONAME = (h01.SEAT_NO + h01.NAME),
                                   GRADE = a04.GRADE,
                                   PASS_DATE = a04.PASS_DATE,
                                   USER_NO = h01.USER_NO,
                                   CASH_YN = a04.CASH_YN
                               }).ToList();
            }

          

            int iStuSeat = -1;    //資料位置
            string Name = string.Empty;
            for (int i = 0; i < litmpADDI04.Count(); i++)
            {
                if (Name != litmpADDI04.ToList()[i].NAME)
                {
                    //取得該學生該年度的學習護照閱讀總筆數
                    int iADDT04Count = litmpADDI04.Where(p => p.USER_NO == litmpADDI04.ToList()[i].USER_NO).Count();

                    liADDT04Q02.Add(new uADDT04Q02
                    {
                        CLASS_NO = litmpADDI04.ToList()[i].CLASS_NO,
                        SONAME = litmpADDI04.ToList()[i].SONAME,
                        UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : "",
                        UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : "",
                        UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : "",
                        UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : "",
                        UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : "",
                        UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : "",
                        UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        FinishStatus = (iADDT04Count == 6) ? "Y" : "",
                        CASH_YN = litmpADDI04.ToList()[i].CASH_YN,
                        USER_NO = litmpADDI04.ToList()[i].USER_NO,
                        SCHOOL_NO = litmpADDI04.ToList()[i].SCHOOL_NO,
                        GRADE = litmpADDI04.ToList()[i].GRADE.ToString()
                    });
                    iStuSeat++;
                    Name = litmpADDI04.ToList()[i].NAME;
                }
                else
                {
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO06;

                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06;
                }
            }

            List<SelectListItem> GradeItems = new List<SelectListItem>();
            GradeItems.Add(new SelectListItem() { Text = "全部", Value = "0"  });
            GradeItems.Add(new SelectListItem() { Text = "一年級", Value = "1", Selected = true });
            GradeItems.Add(new SelectListItem() { Text = "二年級", Value = "2" });
            GradeItems.Add(new SelectListItem() { Text = "三年級", Value = "3" });
            GradeItems.Add(new SelectListItem() { Text = "四年級", Value = "4" });
            GradeItems.Add(new SelectListItem() { Text = "五年級", Value = "5" });
            GradeItems.Add(new SelectListItem() { Text = "六年級", Value = "6" });
            ViewBag.GradeItem = GradeItems.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == iGRADE.ToString() });

            return View(liADDT04Q02);

            //int iRowsCount = 0;
            //if (Request["hidRowsCount"] != null)
            //{
            //    iRowsCount = Convert.ToInt16(Request["hidRowsCount"]);
            //    for (int i = 1; i <= iRowsCount; i++)
            //    {
            //        string USER_NO = (Request["hidUSER_NO_" + i.ToString()] != null) ? Request["hidUSER_NO_" + i.ToString()].ToString() : "";
            //        string SCHOOL_NO = (Request["hidSCHOOL_NO_" + i.ToString()] != null) ? Request["hidSCHOOL_NO_" + i.ToString()].ToString() : "";
            //        int GRADE = (Request["hidGRADE_" + i.ToString()] != null) ? Convert.ToInt16(Request["hidGRADE_" + i.ToString()]) : 0;
            //        string ZZZI06 = (Request["ZZZI06_" + i.ToString()] != null) ? Request["ZZZI06_" + i.ToString()].ToString() : "";
            //        if (ZZZI06 == "on")
            //        {
            //            ADDT04 A04 = new ADDT04();
            //            A04 = db.ADDT04.Where(p => p.USER_NO == USER_NO &&
            //                                       p.SCHOOL_NO == SCHOOL_NO &&
            //                                       p.GRADE == GRADE).FirstOrDefault();
            //            A04.PASS_YN = "Y";
            //            A04.PASS_DATE = DateTime.Now;
            //            db.Entry(A04).State = EntityState.Modified;
            //            db.SaveChanges();
            //        }
            //    }
            //}
            //return View();
        }

        [HttpPost]
        public ActionResult ADDTList_CheckPendingDetailEDIT()
        {
            return RedirectToAction("../ZZZI06/QUERY");
        }

        [HttpPost]
        public ActionResult ADDT04_EDIT()
        {
            int iRowsCount = 0;
            if (Request["hidRowsCount"] != null)
            {
                iRowsCount = Convert.ToInt16(Request["hidRowsCount"]);
                for (int i = 1; i <= iRowsCount; i++)
                {
                    string USER_NO = (Request["hidUSER_NO_" + i.ToString()] != null) ? Request["hidUSER_NO_" + i.ToString()].ToString() : "";
                    string SCHOOL_NO = (Request["hidSCHOOL_NO_" + i.ToString()] != null) ? Request["hidSCHOOL_NO_" + i.ToString()].ToString() : "";
                    int GRADE = (Request["hidGRADE_" + i.ToString()] != null) ? Convert.ToInt16(Request["hidGRADE_" + i.ToString()]) : 0;
                    string ZZZI06 = (Request["ZZZI06_" + i.ToString()] != null) ? Request["ZZZI06_" + i.ToString()].ToString() : "";
                    if (ZZZI06 == "on")
                    {
                        List<ADDT04> liADDT04 = db.ADDT04.Where(p => p.USER_NO == USER_NO && p.SCHOOL_NO == SCHOOL_NO).ToList();
                        //liADDT04.ForEach(a => { a.CASH_YN = "Y";
                        //                        a.PASS_DATE = DateTime.Now;
                        //                      });
                        foreach (var item in liADDT04)
                        {
                            item.CASH_YN = "Y";
                            item.PASS_DATE = DateTime.Now;
                            db.Entry(item).State = EntityState.Modified;
                            db.SaveChanges();
                        }
                    }
                }
            }
            return RedirectToAction("../ZZZI06/QUERY");
        }


        //取得小小讀書人學生待審核
        public ActionResult GetZZZI06Data(string sidx, string sord, int page, int rows)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("  WITH A046 AS ");
            sb.Append(" ( ");
            sb.Append(" SELECT Distinct A06.SYEAR,A06.SEMESTER,A06.CLASS_NO,A06.SEAT_NO,A06.SNAME,A04.SCHOOL_NO,A04.USER_NO,A04.GRADE,A04.PASS_DATE ");
            sb.Append(" FROM ADDT04 A04 ");
            sb.Append(" LEFT JOIN ADDT06 A06 ON A04.SCHOOL_NO=A06.SCHOOL_NO AND A04.USER_NO=A06.USER_NO ");
            sb.Append(" WHERE A04.PASS_YN='N' ");
            sb.Append(" ) ");
            sb.Append(" , ");
            sb.Append(" A089 AS ");
            sb.Append(" ( ");
            sb.Append(" SELECT LEVEL_DESC,USER_NO,A08.SCHOOL_NO ");
            sb.Append(" FROM ADDT09 A09 ");
            sb.Append(" LEFT JOIN  ADDT08 A08 ON A09.SCHOOL_NO=A08.SCHOOL_NO AND A09.LEVEL_ID=A08.LEVEL_ID  ");
            sb.Append(" ) ");
            sb.Append(" , ");
            sb.Append(" ASZZZI06 AS ");
            sb.Append(" ( ");
            sb.Append(" SELECT A046.SYEAR,A046.SEMESTER,CASE A046.SEMESTER WHEN 1 THEN'上' WHEN 2 THEN'下' END AS CSEMESTER,A046.CLASS_NO,A046.SEAT_NO,A046.SNAME,A089.LEVEL_DESC,A046.GRADE,A046.PASS_DATE,ROW_NUMBER() OVER (ORDER BY " + sidx + @" " + sord + @") AS ZZZI06 ");
            sb.Append(" FROM A046  ");
            sb.Append(" LEFT JOIN A089 ON A046.USER_NO=A089.USER_NO AND A046.SCHOOL_NO=A089.SCHOOL_NO ");
            sb.Append(" ) ");
            sb.Append(" SELECT Count(*) ");
            sb.Append(" FROM ASZZZI06 ");

            return Content(JsonHelper.JsonForJqgrid(new ZZZI06Service().USPZZZI06_VIEWMODEL_QUERY(sidx, sord, page, rows), rows, new CommService().GetGirdTotalCount(sb.ToString(), ""), page), "application/json");
        }
    }
}