﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'colorbutton', 'en-gb', {
	auto: 'Automatic',
	bgColorTitle: 'Background Colour',
	colors: {
		'000': 'Black',
		'800000': 'Maroon',
		'8B4513': 'Saddle Brown',
		'2F4F4F': 'Dark Slate Grey',
		'008080': 'Teal',
		'000080': 'Navy',
		'4B0082': 'Indigo',
		'696969': 'Dark Grey',
		B22222: 'Fire Brick',
		A52A2A: 'Brown',
		DAA520: 'Golden Rod',
		'006400': 'Dark Green',
		'40E0D0': 'Turquoise',
		'0000CD': 'Medium Blue',
		'800080': 'Purple',
		'808080': 'Grey',
		F00: 'Red',
		FF8C00: 'Dark Orange',
		FFD700: 'Gold',
		'008000': '<PERSON>',
		'0FF': '<PERSON><PERSON>',
		'00F': '<PERSON>',
		EE82EE: 'Violet',
		A9A9A9: 'Dim <PERSON>',
		FFA07A: 'Light Salmon',
		FFA500: 'Orange',
		FFFF00: 'Yellow',
		'00FF00': 'Lime',
		AFEEEE: 'Pale Turquoise',
		ADD8E6: 'Light Blue',
		DDA0DD: 'Plum',
		D3D3D3: 'Light Grey',
		FFF0F5: 'Lavender Blush',
		FAEBD7: 'Antique White',
		FFFFE0: 'Light Yellow',
		F0FFF0: 'Honeydew',
		F0FFFF: 'Azure',
		F0F8FF: 'Alice Blue',
		E6E6FA: 'Lavender',
		FFF: 'White'
	},
	more: 'More Colours...',
	panelTitle: 'Colours',
	textColorTitle: 'Text Colour'
} );
