﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class AWAI01BidDataListViewModel
    {

        /// <summary>
        ///出價 NO
        /// </summary>
        [DisplayName("出價 NO")]
        public string BID_NO { get; set; }

        /// <summary>
        ///獎品代號
        /// </summary>
        [DisplayName("獎品代號")]
        public int? AWARD_NO { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///學年度
        /// </summary>
        [DisplayName("學年度")]
        public byte? SYEAR { get; set; }

        /// <summary>
        ///學期
        /// </summary>
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///出價日期
        /// </summary>
        [DisplayName("出價日期")]
        public DateTime? BID_DATE { get; set; }

        /// <summary>
        ///出價點數
        /// </summary>
        [DisplayName("出價點數")]
        public int? BID_CASH { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public byte? BID_STATUS { get; set; }

    }
}