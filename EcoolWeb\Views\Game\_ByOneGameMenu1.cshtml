﻿@using ECOOL_APP;
@{

    //var Permission = ViewBag.Permission as List<ControllerPermissionfile>;

    byte? GAME_TYPE =  (byte)ADDT26.GameType.一般;

}

    @*<div class="form-group">

        <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="GameIndex1" ? "active":"")" onclick="OnGameIndex()"> <i class="fa fa-edit"></i> 活動一覽表</button>

      


        <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="PassMod1" ? "active":"")" onclick="OnOnePassMode()"> <i class="fa fa-truck"></i> 過關模式</button>

       
       


    </div>*@


<!-- clipboard.js v1.7.1 複製內容到剪貼簿的小工具 -->
<script src="~/Scripts/clipboard.min.js"></script>
<script type="text/javascript">
    function OnOneEdit(val) {

        if (val.toUpperCase()=="EDIT") {
             $('form').attr("action", "@Url.Action("Edit", "Game")");
        }
        else {
            $('form').attr("action", "@Url.Action("EditQA", "Game")");
        }

        $('form').submit();
    }

   

      

    function OnGameIndex() {
        location.href = "@Url.Action("GameIndex1", (string)ViewBag.BRE_NO)";
    }

    
    function OnOnePassMode() {
        $('form').attr("action", "@Url.Action("PassMode1", "Game")");
        $('form').submit();
    }

  

</script>