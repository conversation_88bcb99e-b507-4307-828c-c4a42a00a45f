﻿@model ADDI13IndexViewModel

@{
    ViewBag.Title = "點數領取-列表";
    Layout = "~/Views/Shared/_LayoutSEO.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string LogoAct = "GuestIndex";
    bool IsAllList = (this.Request.CurrentExecutionFilePath.Contains("ADDTALLListDetails"));
    if (TempData["StatusMessage"] != null)
    {
        string HtmlMsg1 = TempData["StatusMessage"].ToString().Replace("\r\n", "<br />");




    }
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
}


@section css{
    <link href="@Url.Content("~/Content/colorbox/example3/colorbox.css")" rel="stylesheet" />
    <link href="@Url.Content("~/Content/styles/PointsCollection.min.css")" rel="stylesheet" />

}
@Html.Partial("_Notice")

@if (IsAllList == false)
{
    <div class="pb-3 d-flex flex-row align-items-center flex-wrap">
        @if (user != null)
        {
            if (user.USER_TYPE == ECOOL_APP.EF.UserType.Student)
            {

                <img src="~/Content/img/monkey-01.png" width="40" height="40" class="d-inline-block img-responsive" />
                <a class="h4 ml-2" href='@Url.Action("ArrivedChance2", "Home")'>
                    好運次數：
                    @user.Chance_ARRIVED_CASH
                </a>
            }
        }
    </div>
}
<div class="row">
    <div class="col-md-3 col-lg-2">
        <div class="text-center mb-3">
            @if (!string.IsNullOrEmpty(Model.PlayerUrl))
            {
                <img src="@(Url.Content(Model.PlayerUrl)+ "?refreshCache=" + DateTime.Now.ToString("mmddss"))" class="d-inline-block img-responsive mb-3" />
            }
        </div>
    </div>
    <div class="col-md-9 col-lg-10">

        @if ("ADDI13" == "ADDI13")
        {
            <a type="button" class="btn btn-sm btn-warning btn-sys-style btn-sys-margin mb-1 px-4 mr-1" id="MyPHOTO" href="@Url.Action("MyPhoto", "SECI01")">
                個人化編輯
                <i class="fa fa-pencil text-danger" aria-hidden="true"></i>
            </a>
            if (!AppMode)
            {
                <button type="button" class="btn btn-sm btn-dropbox btn-sys-style btn-sys-margin mb-1 px-4 mr-1" onclick="GameCash()">
                    取得點數
                    <i class="fa fa-arrow-circle-o-down text-info" aria-hidden="true"></i>
                </button>
            }
            else
            {
                <button type="button" class="btn btn-sm btn-dropbox btn-sys-style btn-sys-margin mb-1 px-4 mr-1" id="GameCash" href="@(Url.Action("GetStudentCashIndex", "ADDI13") + "?PRINT=Y&SCHOOL_NO1="+user.SCHOOL_NO)">
                    取得點數
                    <i class="fa fa-arrow-circle-o-down text-info" aria-hidden="true"></i>
                </button>
            }
            <button type="button" class="btn btn-sm btn-bold btn-pink" title="點數分析" id="PieChartbtn" href="@Url.Action("_PieChartDiv",  "SECI01")">點數分析</button>
            <button type="button" class="btn btn-sm btn-bold btn-pink" title="加值應用統計" id="Statisticalbtn" href="@Url.Action("_StatisticalDiv", "SECI01")">加值應用統計</button>

            if (Model.wIsQhisSchool && ViewBag.IsUseZZZI09 == true)
            {
                <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出(過去學校)" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出(過去學校)</button>
            }

            if (Model.DATA_ANGLE_TYPE != EcoolWeb.Models.UserProfileHelper.AngleVal.OneData)
            {
                if (ViewBag.IsUseZZZI09 == true)
                {
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出</button>
                }
            }
            else
            {
                <button type="button" class="btn btn-sm btn-bold btn-pink" title="我的線上投稿" onclick="funBookW()">我的線上投稿</button>


                <a role="button" href='@Url.Action("BOOK_APPLY",  "SECI01")' class="btn btn-sm btn-bold btn-pink">我的閱讀認證</a>
                <a role="button" href='@Url.Action("Index3",  "SECI01")' id="MyMOMO" class="btn btn-sm btn-bold btn-pink">我的默默等級</a>
                <div style="display:none">
                    @{
                        int No = 1;
                        string IdName = string.Empty;

                        <div class="arrWRITING_NO">
                            @foreach (var item in Model.arrWRITING_NO)
                            {
                                IdName = "W" + No.ToString();

                                <a id="@IdName" class="groupWRITING_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                    @item
                                </a>
                                No++;
                            }
                        </div>

                        No = 1;

                        <div class="arrAPPLY_NO">
                            @foreach (var item in Model.arrAPPLY_NO)
                            {
                                IdName = "A" + No.ToString();

                                <a id="@IdName" class="groupAPPLY_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                    @item
                                </a>

                                No++;
                            }
                        </div>

                    }
                </div>
            }
        }


        @using (Html.BeginForm("Index1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank" }))
        {
            @Html.AntiForgeryToken()
            //@Html.HiddenFor(m => m.ROLL_CALL_ID)
            @Html.HiddenFor(m => m.SCHOOL_NO1)

            <div class="points-collection my-3" id="panel">
                <strong class="points-collection-title">點數領取</strong>

                <div class="input-group input-group-lg">
                    <span class="input-group-addon"><i class="fa fa-user"></i></span>
                    @Html.EditorFor(m => m.CARD_NO, new { htmlAttributes = new { @class = "form-control", @ariaLabel = "請輸入紙張裡的代碼", @placeholder = "請輸入紙張裡的代碼", @onKeyPress = "call(event,this);" } })
                </div>
                @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })

                <div class="text-center py-3">
                    <input class="btn btn-primary mx-2 px-5 py-3" type="button" value="送出" onclick="OnclickCardNO()">
                    <button class="btn btn-light border mx-2 px-5 py-3" type="button" id="BtnClear">清除</button>
                </div>
                <div class="h5 bg-hint p-3">
                    說明：
                    <ol>
                        <li>本功能是將你獲得的酷幣紙本點數歸戶到自己的酷幣點數裡。</li>
                        <li>請輸入紙本點數的代碼。</li>
                    </ol>
                    <div class="text-center">
                        <img class="d-inline-block img-responsive" src="@Url.Content("~/Content/images/points-collection-sample.png")?v=@DateNowStr" alt="">
                    </div>
                </div>
            </div>



            <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
                <div style="margin: 0px auto;text-align:center">
                    <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
                    <br />
                    <h3 style="color:#80b4fb">讀取中…</h3>

                </div>
            </div>

        }
    </div>


</div>
<div id="ContentALL">

    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger h3 mb-1 text-black text-center" role="alert">
                        <img class="d-block mx-auto" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAAApCAYAAAClfnCxAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAR2SURBVFhH1ZlfaBxVFMbP2XUTTTYhyaYgSBVLa4s1pKG1UlN2Jr60lRaFUpCWYmmrQilKXgyoRbCg5qmirQ9RKw2oT0WQYpoXdzYkpfSPbciLsaEqfdGa7IZsUpOuO8dvluyYNe7OndkbG+chgZ1zv/Obb8+999xZJs1XOh6P55gPQ/YpYn4Y/39hootk2z2xgYELOtNBV981bhjdzPx6SUWRd2LJ5Nu6MmqDT5lmlxC9rwDWGbOsDxTiPEO0wKfi8RYJha4hW9gzI1Euwry+PpEYVYgtG6IFftw0L0NokzKMyAWUT7tyfInAiuEn4vGDFAp95htE5CAe4HPf4xYMqAg+bZoNNtFP0GsIADEeIlrTaFmTAcbmh1QEP2EYPVgOXwqanER64P4rQccHhp80zU05ostBE+fHiUiYeXODZV0JohMIXvbsCadu374G11uCJC0aIzLSlEy2AgQrrb8rEPyEab6GNFrW6vkv4NXmZPIjf+gBan7aNB+cFfkRO2md32Sl4lE9mSrmVfWWNe5H07fzcP0rJHjBTxKVWDzAl3B/n0psIcYXfNowOmzm71QS3LdhA0VaWyk7PEx/Xr+uMoTQvLX7ad6U4WXjxkgqGv0Bk3SVCknjuXPEtbUk09OU3rVLZYgzY0djmUwLX72aVRmgDI8W4C0EH1cRdVyvP3HCDZ3q7FR2H+XzBsrnPZU8SvBoAVai8bqB4GoV0Qrh/2CRtSifW1651OAN41uUyw4vscL9SuDnl85v4P5zXvk84VEuzyPoay+hhfcrhXe04P6z2Lz6yuUtCy9btjyQqqoahesr/2t4tA63mmpq1nBf31yp3GXhPY91JVR1OD/v/rtw/03f8FMdHWuztj0C1yN+XHdidcHD/Ww4l1vXMDh4898YSjqPdncI4E/7BdcKDzEsnQlM3meU4bGT7sdO2hsEXDf8fPnsRfk4bUnRtcj539vb60KRiPM1NS8XeOy8v0o2+9iKoaHMQqZF8Jikp9AxHgkKXhjntgczM5TeubNSOad+PsSpy2nF3asIPn86ErmEWvdc/71owqtXU9XWrXR3cJByY2Ne4Sr3c2zbbU0DAyOFYBcSXw2nDGNYy+lIBSVADBivNFvWk4vgAX5EmE8F0Fw0hKNRqt62zXV+rr8/311quUReRvl8kp/Izp8p02y+K3JT1+moevt2qu3qcllnurtp7vx5LewQmcQrk0edVyZ5eEzSXoDv16Vec/Qo3b97tys3e/Ys3Tl5Upe8M3lPw/1DjFfSbXYo9L0+ZaIIJmrd8b9b/8yxY5TFxNV5wf02hutfwPW9OoUdrSVYbYoQsfP2Mg7UP+PTR3TDL7UeVp4xB973y56lBlPRh/N3/r/Oi9xw4D/Fkx5SedrlFAPnP2b8HPME6sbdcpcTYDkWse3H8+t84B8I7tGTwuwDaBPOuL0N2oMd+KHgDJbNFfeIyTMtoH8D34uxRKLfCS7qHvFWrGYiGj2M5mwz7j30z/ue6ksTIM5hHJvSxUbm02xZs4U0fwEE+rxdAiYhiAAAAABJRU5ErkJggg==" height="40" alt="">
                        <span class="d-block pt-3">
                            @if (TempData["StatusMessage"] != null)
                            {
                                string HtmlMsg1 = TempData["StatusMessage"].ToString().Replace("\r\n", "<br />")+ "，領取點數失敗!";


                                @HtmlMsg1 <br />

                                 }


                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>

@* 成功與失敗畫面 *@





@section Scripts {
    <script src="@Url.Content("~/Scripts/Pring.js")"></script>
    <script src="@Url.Content("~/Content/colorbox/jquery.colorbox.js")"></script>
    <script src="@Url.Content("~/Scripts/buzz/buzz.min.js")"></script>

    <script>
        var targetFormID = '#form1';

        $(document).ready(function () {
            //開啟燈箱小視窗
            $("#PieChartbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
            $("#MyMOMO").colorbox({ iframe: true, width: "100%", height: "100%", opacity: 0.82 });
            $("#Statisticalbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
            $("#MyPHOTO").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
            $(".groupWRITING_NO").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%", rel: 'groupWRITING_NO' });
            $("#MyAPPLY").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%" });
            $("#BorrowIndexSesem,#BorrowIndex").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
            $("#GameCash").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
            //焦點點數領取輸入框
            $("#CARD_NO").focus();
            //清除輸入框內容
            var BtnClear = document.getElementById('BtnClear');
            BtnClear.addEventListener('click', function () {
                $('#CARD_NO').val('');
            });
        });

        var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
        var game_trueSound = new buzz.sound("@Url.Content("~/Content/mp3/game_true.mp3")");

        (function ($) {
            $(window).on("beforeunload", function () {
                return true;
            })
        })(jQuery);

        function ExportSave()
        {
            $(targetFormID).attr("action", "@Url.Action("Export", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onSave() {
             var OK = confirm("您確定點完名了?")
            if (OK==true)
            {
                 $(targetFormID).attr("target","_self")
                 $(targetFormID).attr("action", "@Url.Action("RollCallSave", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
            }
        }


        function funBookW() {
            if ($('#W1').length > 0) {
                $('#W1').click();
            }
            else {
                alert('無任何資料')
            }
        }
        function OnclickCardNO() {

            var SCHOOL_NO1 = "";
            var CARD_NO = $('#CARD_NO').val();
            SCHOOL_NO1 = $("#SCHOOL_NO1").val();

            if (CARD_NO != '') {

                if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length > 0) {
                    $('#ErrorStr').html('請勿重複刷卡，謝謝');
                    $('#ErrorDiv').show();

                    setTimeout(function () {
                        $('#CARD_NO').val('');
                        $("#CARD_NO").focus();
                        $('#ErrorStr').val('');
                        $('#ErrorDiv').hide();
                    }, 2000);
                }
                else {

                    $('#CARD_NO').prop('readonly', true);
                    $(".row").attr("style", "display:none");
                    setTimeout(function () {
                        OnKeyinUse(CARD_NO, SCHOOL_NO1);
                    });
                }

            }
        }
        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var SCHOOL_NO1 = "";
                var CARD_NO = $('#CARD_NO').val();
                SCHOOL_NO1 = $("#SCHOOL_NO1").val();
                event.preventDefault();

                if (CARD_NO != '') {

                    if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length >0 ) {
                        $('#ErrorStr').html('請勿重複刷卡，謝謝');
                        $('#ErrorDiv').show();

                        setTimeout(function () {
                            $('#CARD_NO').val('');
                            $("#CARD_NO").focus();
                            $('#ErrorStr').val('');
                            $('#ErrorDiv').hide();
                        }, 2000);
                    }
                    else {

                        $('#CARD_NO').prop('readonly', true);
                        $(".row").attr("style", "display:none");
                        setTimeout(function () {
                            OnKeyinUse(CARD_NO, SCHOOL_NO1);
                        });
                    }

                }
            }
        }

        function OnKeyinUse(CARD_NO, SCHOOL_NO1) {
            $("#StatusMessageDiv").remove()
            $(window).unbind('beforeunload');
            var data = {
                "CARD_NO": CARD_NO,
                "SCHOOL_NO": SCHOOL_NO1
            };

            $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;");

            $.ajax({
                url: '@Url.Action("GetStudentCashPerson", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {

                    $(".containerEZ").html('');
                    $(".containerEZ").html(html);
                    $("footer").remove();
                    $("div[style='width:auto;height:80px']").attr("style", "width:auto;height:50px");
                    $('#loading').hide();
                    $('#ErrorDiv').show();

                    if (html.length > 0) {

                        var l = 0;
                        l = $("#StatusMessageDiv").length;

                        let pattern = /恭喜領取點數 成功！/i;

                        let result = $("#StatusMessageHtmlMsg").html().match(pattern);

                        if (result=="恭喜領取點數 成功！") {
                            console.log("AA");
                            $(".containerEZ").html('');
                            $(".containerEZ").html(html);

                            game_trueSound.play();
                        }
                        else {
                            //失敗
                            if ($("#StatusMessageHtmlMsg").html() != undefined) {
                                console.log($("#StatusMessageHtmlMsg").html());
                                //$("#form1").attr("style", "display:none");
                                game_falseSound.play();
                               
                             
                                $('#ErrorDiv').show();
                               // $('#ErrorDiv').fadeOut(2000);
                                //setTimeout(function () { location.reload(); }, 2400);

                            }
                            else {
                                $(".containerEZ").html('');
                            }
                        }

                        if (l > 0) {
                            var i = 0;
                            $(".row").each(function () {
                                if (i == 0) {
                                    $(this).html('');
                                }
                                i++;
                            })
                        }
                        $('#ErrorStr').html('感應成功…');

                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");
                        //SwipeOK()
                        setTimeout(function () {

                                $('#CARD_NO').prop('readonly', false);
                                $('#CARD_NO').val('')
                                $("#CARD_NO").focus();
                                $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                            if ($("#StatusMessageHtmlMsg").html() != undefined) {
                                $('#ErrorDiv').show();
                                $('#ErrorDiv').fadeOut(6000);
                            }

                        }, 100);
                    }
                    else {
                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");

                        $('#ErrorStr').html('此數位學生證對應不到學生資料…')
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 800);
                    }

                }
            });
        }

        function SwipeOK() {
                 SwipeSound.play();
         }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }


    </script>
}
