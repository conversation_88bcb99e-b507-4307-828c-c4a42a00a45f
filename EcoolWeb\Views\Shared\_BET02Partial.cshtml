﻿@using ECOOL_APP.com.ecool.util;
@{
    ViewBag.Title = "活動公告";
    List<BET02> BET02List = ViewBag.BET02List;
}

<div class="ribbon ribbon-news">
    <img class="ribbon-img" src="@Url.Content("~/Content/images/ribbon-img-news.png")" alt="">
    <strong class="ribbon-title">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </strong>
</div>
<div class="ribbon-content">
    <ul class="list-table">
        @if (BET02List != null)
        {
            foreach (var item in BET02List)
            {
        <li>
            <span class="pl-3">@Html.DisplayFor(modelItem => item.S_DATE, "ShortDateTime")</span>
            @*@Html.ActionLink(item.GetSubject("zh-TW"), "Details", new { controller = "ZZZI04", BULLET_ID = item.BULLET_ID })*@
            @*<a href="@Url.Action("Details", new { controller = "ZZZI04", BULLET_ID = item.BULLET_ID })" target="_blank" >


                @item.GetSubject("zh-TW")
                @if (item.TOP_YN == "Y")
                {
                    <span class="badge badge-hot" title="熱門公告"></span>
                }
            </a>*@

            <a href="javascript:void(0);" class="btn-table-link" onclick="postToDetails('@(HtmlUtility.ComputeSHA256(item.BULLET_ID))', '@HtmlUtility.ComputeSHA256((TempData["SearchContents"]?.ToString() ?? ""))', '@HtmlUtility.ComputeSHA256((TempData["page"]?.ToString() ??""))', '@HtmlUtility.ComputeSHA256(ViewBag.PrevAction)', '@HtmlUtility.ComputeSHA256(ViewBag.Layout)')" rel="noopener noreferrer">

                @item.GetSubject("zh-TW")
                @if (item.TOP_YN == "Y")
                {
                    <span class="badge badge-hot" title="熱門公告"></span>
                }
            </a>
        </li>
            }
        }
    </ul>
    <div class="text-right">
        <a href="#" class="btn btn-link" onclick="postToZZZI04()">more</a>
        @*@Html.ActionLink("more", "Index", "ZZZI04", null, new { @class = "btn btn-link" })*@
    </div>
</div>
<script>
  function postToZZZI04() {
        const form = document.createElement("form");
        form.method = "POST";
        form.action = '@Url.Action("Index", "ZZZI04")';

        // 如果你的 Controller 有 [ValidateAntiForgeryToken]，需要這段
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        if (token) {
            form.appendChild(createHidden("__RequestVerificationToken", token.value));
        }

        // 提交
        document.body.appendChild(form);
        form.submit();
    }

    function createHidden(name, value) {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = name;
        input.value = value;
        return input;
    }
     function postToDetails(bulletId, searchContents, page, prevAction, layout) {
      

        const form = document.createElement("form");
        form.method = "POST";
        form.action = '@Url.Action("Details", "ZZZI04")';

        const inputs = {
            BULLET_ID: bulletId,
            SearchContents: searchContents,
            page: page,
            PrevAction: prevAction,
            Layout: layout
        };

        for (const key in inputs) {
            const input = document.createElement("input");
            input.type = "hidden";
            input.name = key;
            input.value = inputs[key] ?? "";
            form.appendChild(input);
        }

        document.body.appendChild(form);
        form.submit();
    }
</script>