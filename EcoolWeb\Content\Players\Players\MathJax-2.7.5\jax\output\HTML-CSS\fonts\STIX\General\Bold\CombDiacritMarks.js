/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{768:[713,-528,0,-369,-131],769:[713,-528,0,-369,-131],770:[704,-528,0,-418,-81],771:[674,-547,0,-432,-67],772:[637,-565,0,-415,-85],773:[838,-788,0,-500,0],774:[691,-528,0,-401,-98],775:[666,-537,0,-314,-185],776:[666,-537,0,-419,-80],777:[751,-491,0,-336,-131],778:[750,-537,0,-356,-143],779:[713,-528,0,-469,-31],780:[704,-528,0,-418,-81],781:[730,-530,0,-277,-211],782:[730,-530,0,-358,-142],783:[713,-528,0,-469,-31],784:[828,-528,0,-401,-98],785:[691,-528,0,-401,-98],786:[867,-532,0,-342,-158],787:[867,-532,0,-342,-158],788:[867,-532,0,-342,-158],789:[867,-532,0,-116,68],790:[-70,255,0,-369,-131],791:[-70,255,0,-369,-131],792:[-58,288,0,-425,-223],793:[-58,288,0,-288,-86],794:[752,-531,0,-410,-93],795:[505,-352,0,-62,66],796:[-33,313,0,-375,-190],797:[-70,272,0,-365,-135],798:[-70,272,0,-365,-135],799:[-70,287,0,-356,-144],800:[-140,206,0,-356,-144],801:[75,287,0,-241,-22],802:[75,287,0,-94,125],803:[-109,238,0,-314,-185],804:[-109,238,0,-419,-80],805:[-66,279,0,-356,-143],806:[-88,423,0,-342,-158],807:[0,218,0,-363,-137],808:[44,173,0,-364,-135],809:[-107,239,0,-277,-222],810:[-86,260,0,-425,-93],811:[-104,242,0,-420,-95],812:[-83,259,0,-418,-81],813:[-85,261,0,-418,-81],814:[-78,241,0,-401,-98],815:[-78,241,0,-401,-98],816:[-108,235,0,-432,-67],817:[-137,209,0,-415,-85],818:[-137,187,0,-500,0],819:[-137,287,0,-500,0],820:[316,-189,0,-432,-67],821:[282,-224,0,-414,-108],822:[282,-224,0,-510,-10],823:[580,74,0,-410,-43],824:[662,156,0,-410,31],825:[-33,313,0,-375,-190],826:[-71,245,0,-425,-93],827:[-70,264,0,-353,-167],828:[-89,234,0,-410,-109],829:[719,-520,0,-350,-150],830:[881,-516,0,-314,-187],831:[938,-788,0,-500,0],838:[717,-544,0,-410,-107],839:[-137,322,0,0,330],844:[837,-547,0,-446,-81],857:[-66,368,0,-359,-89],860:[-79,242,0,-401,300],864:[674,-529,0,-432,398],865:[691,-534,0,-403,265],866:[-54,293,0,-432,377]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/CombDiacritMarks.js");
