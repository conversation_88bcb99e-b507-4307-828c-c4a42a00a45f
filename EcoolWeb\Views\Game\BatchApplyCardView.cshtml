﻿@model BatchApplyCardViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BatchWork" });
    }
}

@using (Html.BeginForm("BatchApplyStudentView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            訪客匯入(臨時卡)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="">

                    <div class="form-group">
                        <label class="control-label col-md-3">身份</label>
                        <div class="col-md-9">
                            <div class="radio ">
                                <label>
                                    <input type="radio" name="GAME_USER_TYPE" id="GAME_USER_TYPE_G" value="@UserType.Guest" checked>@UserType.GetDesc(UserType.Guest)
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="GAME_USER_TYPE" id="GAME_USER_TYPE_V" value="@UserType.VIP">@UserType.GetDesc(UserType.VIP)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-3">狀態</label>
                        <div class="col-md-9">
                            <div class="radio">
                                <label>
                                    <input type="radio" name="STATUS" id="STATUS_1" value="@((byte)ADDT27.StatusVal.使用中)" checked>@(ADDT27.StatusVal.使用中.ToString())
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="STATUS" id="STATUS_0" value="@((byte)ADDT27.StatusVal.未使用)">@(ADDT27.StatusVal.未使用.ToString())
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-3">上傳報名EXCEL</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(m => m.UploadBatchCardFile, new { @class = "form-control input-md", @type = "file" })
                            @Html.ValidationMessageFor(m => m.UploadBatchCardFile, "", new { @class = "text-danger" })
                            <br />
                            <div class="text-left">
                                1.上傳之 Excel 檔, 請依規定格式填寫(<a href="@Url.Content("~/Content/ExcelSample/GameBatchApplyCardSample.xlsx?v="+DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss"))" target="_blank" class="btn-table-link">下載 Sample</a>)、
                                (<a href="@Url.Action("ExportTeacherExcel")" target="_blank" class="btn-table-link">下載 老師Sample</a>) 。<br />
                                2.卡片內碼一定是10碼，如果有少於10碼的，可能是前面的0被過濾掉了。  <br />
                                3.有酷幣帳號之學生/老師，請勿再使用此功能報名，不然活動結束的餘額轉入會產生錯誤。 <br />

                                4.身份是 「@UserType.GetDesc(UserType.VIP)」 ，無抽獎機會。 <br />
                                5.卡片狀態為 「@(ADDT27.StatusVal.未使用.ToString())」 ，無抽獎機會，但開卡、到各關卡過卡及查詢機過卡，立即變成「@(ADDT27.StatusVal.使用中.ToString())」，且可以抽獎。 <br />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <button class="btn btn-default" type="button" onclick="OnBatchApplyCardSave()">確定</button>
                <button class="btn btn-default" type="button" onclick="onBack()">上一步</button>
            </div>
        </div>
    </div>
}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

           function OnBatchApplyCardSave() {

            $('#formEdit').hide()
            $('#loading').fadeIn(3000)
            setTimeout(function () { funSubmit(); }, 3000);

           }

        function funSubmit() {
            $(targetFormID).attr("action", "@Url.Action("BatchApplyCardSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("BatchWork", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}