﻿@using System.Collections;
@using com.ecool.service;
@using EcoolWeb.Models;

@{
    string MODE = Request["MODE"];
    string Title = string.Empty;
    string AWARD_STATUS = string.Empty;
    string AWARD_TYPE = string.Empty;
    string radHOT_YN = "N";
    string sAWARD_STATUS = string.Empty;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string SHOW_DESCRIPTION_YN = string.Empty;

    if (Model != null)
    {
        if (string.IsNullOrWhiteSpace(Model[0]["HOT_YN"])==false)
        {
            radHOT_YN = Model[0]["HOT_YN"];
        }

        AWARD_TYPE = Model[0]["AWARD_TYPE"];
        AWARD_STATUS = Model[0]["AWARD_STATUS"];


        sAWARD_STATUS = Model[0]["AWARD_STATUS"];

        if (Convert.IsDBNull(Model[0]["SHOW_DESCRIPTION_YN"]) == false)
        {
            SHOW_DESCRIPTION_YN = (Model[0]["SHOW_DESCRIPTION_YN"] == "Y") ? "checked" : "";
        }

    }

    switch (MODE)
    {
        case "ADD":
            Title = "新增獎品";
            AWARD_STATUS = "0";
            break;
    }

    string AWARD_TYPE_A = string.Empty, AWARD_TYPE_T = string.Empty, AWARD_TYPE_S = string.Empty;
    string AWARD_TYPE_P = string.Empty, AWARD_TYPE_C = string.Empty;
    switch (AWARD_TYPE)
    {
        case "A":
            AWARD_TYPE_A = "selected";
            break;
        case "T":
            AWARD_TYPE_T = "selected";
            break;
        case "S":
            AWARD_TYPE_S = "selected";
            break;
        case "P":
            AWARD_TYPE_P = "selected";
            break;
        case "C":
            AWARD_TYPE_C = "selected";
            break;
        default:
            AWARD_TYPE_S = "selected";
            break;
    }

    string radHOT_Y = (radHOT_YN != string.Empty && radHOT_YN == "Y") ? "checked" : "";
    string radHOT_N = (radHOT_YN != string.Empty && radHOT_YN == "N") ? "checked" : "";
    string sCOST_CASHDis = (sAWARD_STATUS != string.Empty && sAWARD_STATUS != "0") ? "readonly" : "";
}


<div class="form-horizontal">
   <div class="form-group">
            @Html.Label("獎品狀態", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <select id="AWARD_TYPE" name="AWARD_TYPE" class="form-control">
                    <option value="A" @AWARD_TYPE_A>活動</option>
                    <option value="T" @AWARD_TYPE_T>票券</option>
                    <option value="S" @AWARD_TYPE_S>實體</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("獎品名稱", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="hidden" id="ParamAWARD_STATUS" name="ParamAWARD_STATUS" value="@AWARD_STATUS"  />
                <input type="text" id="ParamAWARD_NAME" name="ParamAWARD_NAME" class="form-control" maxlength="50" value="@(null != Model && Model.Count != 0 ? Model[0]["AWARD_NAME"] : "")" />
                <br/>
                <label  class = "text-info">PS.獎品名稱.最多50個中英文字元</label>
                
            </div>
        </div>
        <div class="form-group">
            @Html.Label("兌換點數", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamCOST_CASH" name="ParamCOST_CASH" class="form-control" @sCOST_CASHDis maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["COST_CASH"] : "")" />
                <input type="hidden" id="hidParamCOST_CASH" name="hidParamCOST_CASH" class="form-control" maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["COST_CASH"] : "")" />
            </div>
        </div>
        <div class="form-group">
            @Html.Label("獎品數量", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamCOUNT_TOTAL" name="ParamQTY_STORAGE" class="form-control" maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["QTY_STORAGE"] : "")" />
            </div>
        </div>
        <div class="form-group">
            @Html.Label("限制數量", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="QTY_LIMIT" name="QTY_LIMIT" class="form-control" maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["QTY_LIMIT"] : "")" />
                <br />
                <label class="text-info">PS.限制 每一個可兌換的數量，不填，不限制</label>
            </div>
        </div>
    <div class="form-group">
            @Html.Label("開始兌換", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamSDATETIME" name="ParamSDATETIME" class="form-control" readonly="readonly" value="@(null != Model && Model.Count != 0 ? Model[0]["SDATETIME"] : "")" onchange="ValidDate();" />
                <select id="selSH" name="selSH"></select>時
                <select id="selSM" name="selSM"></select>分
            </div>
        </div>
        <div class="form-group">
            @Html.Label("兌換期限", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamEDATETIME" name="ParamEDATETIME" class="form-control" readonly="readonly" value="@(null != Model && Model.Count != 0 ? Model[0]["EDATETIME"] : "")" onchange="ValidDate();" />
                <select id="selEH" name="selEH"></select>時
                <select id="selEM" name="selEM"></select>分
            </div>
        </div>
        <div class="form-group">
            @Html.Label("備註說明", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamDESCRIPTION" name="ParamDESCRIPTION" class="form-control" value="@(null != Model && Model.Count != 0 ? Model[0]["DESCRIPTION"] : "")" />
                <br/>
                <label class="text-info">【備註說明】是否顯示於獎品清單中</label>
                <input type="checkbox" id="SHOW_DESCRIPTION_YN" name="SHOW_DESCRIPTION_YN" value="Y" @SHOW_DESCRIPTION_YN /> <label class="text-info">是</label>
            </div>
        </div>

        <div class="form-group">
            @Html.Label("熱門獎項", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="radio" id="radHOT_Y" name="radHOT_YN" value="Y" @radHOT_Y /><label class="control-label">是</label>
                <input type="radio" id="radHOT_N" name="radHOT_YN" value="N" @radHOT_N /><label class="control-label">否</label>
                <br />
                <label class="text-info"><img src='~/Content/img/icons-07.png' style="max-height:35px;margin-right:5px">PS.此欄位會影響排序，順序【熱門獎項、兌換點數】</label>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("上傳圖片", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.Action("Upload", "Comm")
                <br />
                <label class="text-info">PS.上傳圖片，請上傳約 1:1 圖片，瘦高圖會變形</label>
            </div>
        </div>
    @if (null != Model && Model.Count != 0)
    {
        if (Model[0]["IMG_FILE"] != null)
        {
            string IMG_FILE = Model[0]["IMG_FILE"];
            string aImgUrl = ViewBag.ImgUrl  + @"/" + IMG_FILE;
            <div class="form-group">
                @Html.Label("原圖片", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="col-md-9 col-sm-9 col-lg-10">
                    @Html.Hidden("IMG_FILE", IMG_FILE)
                    <img src="@aImgUrl" class="img-responsive" style="max-height:300px;max-width:300px" />
                </div>
            </div>
        }
    }
</div>
<input type="hidden" id="ParamAWARD_NO" name="ParamAWARD_NO" value="@(null != Model && Model.Count != 0 ? Model[0]["AWARD_NO"] : "")" />





@{
    if (null != Model)
    {

        <script type="text/javascript">

            $(function () {
                var TData = [];
                for (var hi = 0; hi <= 23; hi++) {
                    if (hi == 0)
                        TData.push("<option value='" + hi + "' selected>" + hi + "</option>");
                    else
                        TData.push("<option value='" + hi + "' >" + hi + "</option>");
                }
                $('#selSH').append(TData);
                $('#selEH').append(TData);
                TData = [];
                for (var mi = 0; mi <= 59; mi++) {
                    TData.push("<option value='" + mi + "' >" + (mi.toString().length > 1 ? mi : "0" + mi) + "</option>");
                }
                $('#selSM').append(TData);
                $('#selEM').append(TData);

                $("#ParamSDATETIME").datepicker({
                    dateFormat: "yy/mm/dd",
                    changeMonth: true,
                    changeYear: true,
                    showOn: "button",
                    buttonImage: "../Content/img/icon/calendar.gif",
                    buttonImageOnly: true,

                });

                $('#ParamSTIME').val()
                $("#ParamEDATETIME").datepicker({
                    dateFormat: "yy/mm/dd",
                    changeMonth: true,
                    changeYear: true,
                    showOn: "button",
                    buttonImage: "../Content/img/icon/calendar.gif",
                    buttonImageOnly: true
                });
            });
        </script>
    }
}