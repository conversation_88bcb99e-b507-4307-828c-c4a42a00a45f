﻿@model EcoolWeb.ViewModels.BDMT01IndexViewModel
@{
    ViewBag.Title = "學校基本資料查詢";
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")


@using (Html.BeginForm("QUERY", "ZZZI01", FormMethod.Post, new { id = "ZZZI01" }))
{

        if (ViewBag.VisableInsert == true)
        {
            <a href='@Url.Action("ADD", "ZZZI01")' class="btn btn-sm btn-sys">
                新增學校資訊
            </a>
        }
        <br />

    if (ViewBag.ROLE_TYPE != 3)
    {
         <br />
            <div class="form-inline" role="form" id="Q_Div">
                <div class="form-group">
                    <label class="control-label">學校名稱/學校代號</label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                    @Html.HiddenFor(m => m.OrdercColumn)
                    @Html.HiddenFor(m => m.Page)
                </div>
                <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
            </div>
            <br />
    }

    @Html.Hidden("SCHOOL_NO")

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                <thead>
                    <tr>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                            建立日期
                            <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center">
                            學校簡稱
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CITY');">
                            縣市
                            <img id="CITY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center">
                            電話
                        </th>

                        <th style="text-align: center">
                            管理員
                        </th>
                        <th style="text-align: center">
                            管理員手機
                        </th>
                        <th style="text-align: center">

                        </th>
                    </tr>
                </thead>
             <tbody>
                 @foreach (var item in Model.BDMT01List)
                {
                     <tr>
                         <td>
                             @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                         </td>
                         <td>
                             @Html.DisplayFor(modelItem => item.SHORT_NAME)
                         </td>
                         <td style="text-align: center;white-space:nowrap">
                             @Html.DisplayFor(modelItem => item.CITY)
                         </td>
                         <td style="text-align: left;white-space:normal">
                             @Html.DisplayFor(modelItem => item.TEL)
                         </td>

                         <td style="text-align: center;">
                             @Html.DisplayFor(modelItem => item.Manager_Name)
                         </td>
                         <td style="text-align: left;white-space:normal">
                             @Html.DisplayFor(modelItem => item.Manager_Phone)
                         </td>
                         <td>
                             @if (ViewBag.VisableMODIFY == true)
                             {
                                 @Html.ActionLink("維護", "MODIFY", new { SCHOOL_NO = item.SCHOOL_NO }, new { @class = "btn btn-xs btn-Basic" })
                                 if (ViewBag.Show)
                                 {
                                    <button class="btn btn-xs btn-Basic" onclick="btnStartUser_onclick('@item.SCHOOL_NO')">解鎖定</button>
                                    <button class="btn btn-xs btn-Basic" onclick="btnDoResetPassword_onclick('@item.SCHOOL_NO')">重設密碼</button>
                                 }

                             }
                         </td>
                     </tr>
                 }

             </tbody>

              
            </table>
        </div>
    </div>
  

    if (ViewBag.ROLE_TYPE != 3)
    {
        <div>
            @Html.Pager(Model.BDMT01List.PageSize, Model.BDMT01List.PageNumber, Model.BDMT01List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
        </div>
    }
    @section scripts{
        <script>
            var targetFormID = '#ZZZI01';

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };


            function btnStartUser_onclick(val) {
                $('#SCHOOL_NO').val(val)
                $(targetFormID).attr ('action','@Url.Action("StartUser")');
                $(targetFormID).submit();


            }

            function btnDoResetPassword_onclick(val) {
                $('#SCHOOL_NO').val(val)
                $(targetFormID).attr ('action','@Url.Action("DoResetPassword")');
                $(targetFormID).submit();
            }

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }
            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

            function todoClear() {
                ////重設

                $("#Q_Div").find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }
        </script>
    }
}