﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI05UnusualViewModel

@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<div class="container">
    <h4>@ViewBag.Panel_Title</h4>
    @using (Html.BeginForm("_UnusualListDiv", "SECI05", FormMethod.Post))
    {
        <div>
            @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "this.form.submit()" })
        </div>
        <br />

        <p class="text-danger">※ 超過三個月沒有借書紀錄的學生</p>

        if (Model.BrrowUnusualList != null && Model.BrrowUnusualList.Count > 0)
        {
            <table class="table table-bordered table-hover">
                <thead class="text-primary">
                    <tr>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().NAME)</th>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().CLASS_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().USER_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().SumQTY)</th>
                        <th>最後借書日期</th>
                        <th>沒有借書紀錄(月)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var u in Model.BrrowUnusualList)
                    {
                        <tr class="LinkTR" onclick="window.open('@Url.Action("Index","SECI05",new {  whereUser_NO = u.USER_NO, WhereCLASS_NO = u.CLASS_NO })','_blank')">
                            <td>@u.NAME</td>
                            <td>@u.CLASS_NO</td>
                            <td>@u.USER_NO</td>
                            <td>@u.SumQTY</td>
                            <td>@u.LAST_BORROW_DATE</td>
                            <td>@u.DiffMonth</td>
                        </tr>
                    }
                </tbody>
            </table>
        }
        else
        {
            <div>尚無異常借書狀況的學生。</div>
        }

        <br />
        <p class="text-danger">※ 逾期1個月未還書的學生</p>
        if (Model.ReturnUnusualList != null && Model.ReturnUnusualList.Count > 0)
        {
            <table class="table table-bordered table-hover">
                <thead class="text-primary">
                    <tr>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().NAME)</th>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().CLASS_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().USER_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().BKNAME)</th>
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().SumQTY)</th>
                        <th>最後借書日期</th>
                      
                        <th>@Html.DisplayNameFor(m => m.BrrowUnusualList.FirstOrDefault().DiffMonth)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var u in Model.ReturnUnusualList)
                    {
                        <tr class="LinkTR" onclick="window.open('@Url.Action("Index","SECI05",new {  whereUser_NO = u.USER_NO, WhereCLASS_NO = u.CLASS_NO })','_blank')">
                            <td>@u.NAME</td>
                            <td>@u.CLASS_NO</td>
                            <td>@u.USER_NO</td>
                            <td>@u.BKNAME</td>
                            <td>@u.SumQTY</td>
                            <td>@u.LAST_BORROW_DATE</td>
                          
                            <td>@u.DiffMonth</td>
                        </tr>
                    }
                </tbody>
            </table>
        }
        else
        {
            <div>尚無異常還書狀況的學生。</div>
        }
    }
</div>



@section css{
    <style>
        .LinkTR:hover {
            cursor: pointer;
        }
    </style>
}
