/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Size3={directory:"Size3/Regular",family:"GyrePagellaMathJax_Size3",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[955,455,531,131,444],41:[955,455,531,87,400],47:[1149,649,838,80,758],91:[961,461,461,131,374],92:[1149,649,838,80,758],93:[961,461,461,87,330],123:[960,460,493,87,406],124:[941,441,216,80,136],125:[960,460,493,87,406],160:[0,0,250,0,0],770:[712,-542,874,0,874],771:[700,-544,870,0,870],774:[709,-551,897,0,897],780:[712,-542,874,0,874],785:[721,-563,897,0,897],812:[-60,230,874,0,874],813:[-70,240,874,0,874],814:[-60,218,897,0,897],815:[-78,236,897,0,897],816:[-78,233,870,0,870],8214:[941,441,392,80,312],8260:[1149,649,838,80,758],8425:[777,-649,1484,0,1484],8730:[1200,670,730,120,760],8739:[941,441,216,80,136],8741:[941,441,392,80,312],8968:[961,441,461,131,374],8969:[961,441,461,87,330],8970:[941,461,461,131,374],8971:[941,461,461,87,330],9001:[1155,655,487,87,400],9002:[1155,655,487,87,400],9140:[777,-649,1484,0,1484],9141:[-179,306,1484,0,1484],9180:[784,-571,2028,0,2028],9181:[-101,314,2028,0,2028],9182:[802,-589,2038,0,2038],9183:[-119,332,2038,0,2038],9184:[734,-528,2070,0,2070],9185:[-58,264,2070,0,2070],10214:[961,461,472,131,385],10215:[961,461,472,87,341],10216:[1155,655,487,87,400],10217:[1155,655,487,87,400],10218:[1155,655,753,87,666],10219:[1155,655,753,87,666],10222:[955,455,381,131,294],10223:[955,455,381,87,250]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Size3"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size3/Regular/Main.js"]);
