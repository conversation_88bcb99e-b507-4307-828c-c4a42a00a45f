﻿@model ZZZI33PreviewViewModel
@using ECOOL_APP.EF
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>





<div class="col-lg-offset-2 col-md-offset-2 col-md-sm-2 col-lg-7 col-md-7 col-sm-8 col-xs-12">

    @Html.Partial("_Title_Secondary")
    @Html.Partial("_Notice")

    @if (Model.IsCreExcel)
    {
        <button class="btn btn-sm btn-sys" type="button" onclick="onBack()">回投票系統列表</button>
    }



    @using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data", @AutoComplete = "Off" }))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.Search.OrdercColumn)
        @Html.HiddenFor(m => m.Search.SyntaxName)
        @Html.HiddenFor(m => m.Search.Page)
        @Html.HiddenFor(m => m.Search.whereQUESTIONNAIRE_ID)
        @Html.HiddenFor(m => m.Search.whereSearch)
        @Html.HiddenFor(m => m.Search.BackAction)
        @Html.HiddenFor(m => m.Search.BackController)
        @Html.HiddenFor(m => m.SaveType)
    }

    <img src="~/Content/img/web-bar-vote.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="Details">
                <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_NAME)
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_NAME)
                    </samp>
                </div>
                <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_SDATE)
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_SDATE)
                    </samp>
                </div>
                <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_EDATE)
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_EDATE)
                    </samp>
                </div>
                @if ((Model.Title.CASH ?? 0) > 0)
            {
                    <div class="dl-horizontal-EZ">
                        <samp class="dt">
                            @Html.DisplayNameFor(m => m.Title.CASH)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.Title.CASH)
                        </samp>
                    </div>
                }
                <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        詳細活動內容
                    </samp>
                </div>
                <div style="height:15px"></div>
                <div class="p-context">
                    @Html.Raw(HttpUtility.HtmlDecode(Model.Title.QUESTIONNAIRE_DESC))
                </div>
            </div>

            <div style="padding-left:10px">
                <div>
                    <div>
                        @if (Model.Topic != null && Model.Topic.Count() > 0)
                        {
                            int InputNum = 0;

                            foreach (var item in Model.Topic)
                            {
                                if (item.Q_TYPE != SAQT02.Q_TYPEVal.IsText)
                                {
                                    <div style="height:15px"></div>
                                    <div style="height:1px;background-color:#DDDDDD;width:99%"></div>
                                    <div style="height:15px"></div>
                                    @Html.HiddenFor(m => m.Topic[InputNum].Q_NUM)
                                    <samp class="label_dt_S">Q@(InputNum + 1).</samp>
                                        @Html.Raw(HttpUtility.HtmlDecode(item.Q_SUBJECT))

                                        <div style="height:12px"></div>


                                        <div class="form-group">
                                            <div class="col-md-12">
                                                @foreach (var D_item in item.Topic_D)
                                                {
                                                    if (item.Q_TYPE == SAQT02.Q_TYPEVal.IsInput)
                                                    {
                                                        @SAQT03.CreInputHtml(D_item, item.ANSWER, item.Q_MUST, InputNum, null)
                                                    }
                                                    else if (item.Q_TYPE == SAQT02.Q_TYPEVal.IsStudentMenu)
                                                    {
                                                        @Html.Action("_Index", "StudentMenu", new { Index = InputNum })

                                                    }
                                                    else
                                                    {
                                                        @SAQT03.CreSelectHtml(item.Topic_D, item.ANSWER, item.Q_MUST, InputNum, null)
                                                    }
                                                }
                                            </div>
                                        </div>

                                }

                                @Html.HiddenFor(m => m.Topic[InputNum].Index, InputNum)
                                InputNum = InputNum + 1;
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="height:25px"></div>
</div>





@section scripts{
    <script type="text/javascript">
        var targetFormID = '#formEdit';


        $(document).ready(function () {

        var SysUrl = 'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'

        $(".form-horizontal img").each(function () {

            var ThisSrc = $(this).attr("src")

            if (ThisSrc.indexOf("http") != 0) {
                $(this).attr("href", ThisSrc);
            }
            else if (ThisSrc.indexOf("data:image") != 0) {
                $(this).attr("href", ThisSrc);
            }
            else {
                $(this).attr("href", SysUrl + ThisSrc);
            }
        });

        $(".form-horizontal img").colorbox({ photo: true });
    });

        function onBack() {
            $(targetFormID).attr("action", "/EcoolWeb/ZZZI33/Index")
            $(targetFormID).submit();
        }

    </script>
}




