﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'en-gb', {
	alt: 'Alternative Text',
	border: 'Border',
	btnUpload: 'Send it to the Server',
	button2Img: 'Do you want to transform the selected image button on a simple image?',
	hSpace: 'HSpace',
	img2Button: 'Do you want to transform the selected image on a image button?',
	infoTab: 'Image Info',
	linkTab: 'Link',
	lockRatio: 'Lock Ratio',
	menu: 'Image Properties',
	resetSize: 'Reset Size',
	title: 'Image Properties',
	titleButton: 'Image Button Properties',
	upload: 'Upload',
	urlMissing: 'Image source URL is missing.',
	vSpace: 'VSpace',
	validateBorder: 'Border must be a whole number.',
	validateHSpace: 'HSpace must be a whole number.',
	validateVSpace: 'VSpace must be a whole number.'
} );
