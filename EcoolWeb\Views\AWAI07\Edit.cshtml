﻿@model AWAI07EditViewModel
@using EcoolWeb.Util;
@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@{ 
    string STATUS = Model.Edit != null ? Model.Edit.STATUS ?? AWAT10.StatusVal.NotStarted : AWAT10.StatusVal.NotStarted;

}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@{
    if ((Model.Edit != null ? Model.Edit.A_NO : null) != null)
    {
        Html.RenderAction("_BankMenu");
    }
    else
    {
        Html.RenderAction("_BankMenu", new { NowAction = "Edit" });
    }

}


@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.WhereA_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.WhereUSER_NO)
    @Html.HiddenFor(m => m.IsTemp)

    @Html.HiddenFor(m => m.Edit.A_NO)

    <img src="~/Content/img/web-bar-Bank.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div style="height:15px"></div>
            <fieldset @((Model.Edit != null ? Model.Edit.STATUS : null) == AWAT10.StatusVal.SetUp ? "disabled" : "")>
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.MATURITY_TYPE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.MATURITY_TYPE,
                            "_RadioButtonList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Edit.MATURITY_TYPE)).ToHtmlString(),
                                RadioItems = AWAT10.MATURITY_TYPE_Val.SelectItem(Model.Edit != null ? Model.Edit.MATURITY_TYPE : null),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "changeMaturity_Type();"
                            })
                        @Html.ValidationMessageFor(m => m.Edit.MATURITY_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group" id="acctCodeType">
                    @Html.LabelFor(m => m.Edit.ACCT_CODE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.ACCT_CODE,
                            "_RadioButtonList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Edit.ACCT_CODE)).ToHtmlString(),
                                RadioItems = AWAT10.AcctCodeVal.SelectItem(Model.Edit != null ? Model.Edit.ACCT_CODE : null),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue
                            })
                        @Html.ValidationMessageFor(m => m.Edit.ACCT_CODE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.PERIOD_TYPE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        <div class="input-group">
                            @Html.DropDownListFor(m => m.Edit.PERIOD_TYPE, (List<SelectListItem>)ViewBag.PeriodTypeItem, new { @class = "form-control input-md" })
                            <span class="input-group-btn">
                                <button class="btn btn-default btn-md colorbox " type="button" href="@Url.Action("_RateView", (string)ViewBag.BRE_NO)">利率表</button>
                            </span>
                        </div>
                        @Html.ValidationMessageFor(m => m.Edit.PERIOD_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>
                @if (STATUS != AWAT10.StatusVal.NotStarted)
                {
                    <div class="form-group">
                        @Html.LabelFor(m => m.Edit.PERIOD_DATES, htmlAttributes: new { @class = "col-md-3 control-label" })
                        <div class="col-md-9">
                            @Html.EditorFor(m => m.Edit.PERIOD_DATES, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Edit.PERIOD_DATES) } })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(m => m.Edit.PERIOD_DATEE, htmlAttributes: new { @class = "col-md-3 control-label" })
                        <div class="col-md-9">
                            @Html.EditorFor(m => m.Edit.PERIOD_DATEE, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Edit.PERIOD_DATEE) } })
                        </div>
                      
                    </div>
                }
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.AMT, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.AMT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Edit.AMT) } })
                        @Html.ValidationMessageFor(m => m.Edit.AMT, "", new { @class = "text-danger" })
                    </div>
                </div>
            </fieldset>
            @if (STATUS == AWAT10.StatusVal.NotStarted)
            {
                @*
                <div class="form-group">
                    @Html.Label("到期後本利和", htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        <div class="input-group">
                            @Html.Editor("PrincipleAndInterestAmt", new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "到期後本利和", @disabled = "true" } })
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" onclick="onTest()">利息試算</button>
                            </span>
                        </div><!-- /input-group -->
                    </div>
                </div>
                    *@
            }
            else if (STATUS == AWAT10.StatusVal.SetUp )
            {
                <div class="form-group">
                    @Html.Label("到期後本利和", htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.PrincipleAndInterestAmt, new { htmlAttributes = new { @class = "form-control input-md" } })
                    </div>
                </div>
                    <div class="form-group">
                        @Html.Label("今日解約試算", htmlAttributes: new { @class = "col-md-3 control-label" })
                        <div class="col-md-9">
                            <div class="input-group">
                                @Html.Editor("ClosePrincipleAndInterestAmt", new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "解約後本利和", @disabled = "true" } })
                                <span class="input-group-btn">
                                    <button class="btn btn-default" type="button" onclick="onCloseTest()">解約試算</button>
                                </span>
                            </div><!-- /input-group -->
                        </div>
                    </div>
            }
            else if (STATUS == AWAT10.StatusVal.Terminate)
            {
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.CLOSE_DATE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.CLOSE_DATE, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Edit.CLOSE_DATE) } })
                        @Html.ValidationMessageFor(m => m.Edit.CLOSE_DATE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.INTEREST_AMT, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.INTEREST_AMT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Edit.INTEREST_AMT) } })
                        @Html.ValidationMessageFor(m => m.Edit.INTEREST_AMT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("解約後本利和", htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                            @Html.EditorFor(m => m.Edit.ClosePrincipleAndInterestAmt, new { htmlAttributes = new { @class = "form-control input-md" } })
                    </div>
                </div>
            }
            else if (STATUS == AWAT10.StatusVal.Expire)
            {
                <div class="form-group">
                    @Html.Label("到期後本利和", htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.PrincipleAndInterestAmt, new { htmlAttributes = new { @class = "form-control input-md" } })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.INTEREST_AMT, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.INTEREST_AMT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Edit.INTEREST_AMT) } })
                        @Html.ValidationMessageFor(m => m.Edit.INTEREST_AMT, "", new { @class = "text-danger" })
                    </div>
                </div>
            }
        </div>
    </div>



        <div style="height:25px"></div>
        <div class="text-center">

          
                @if (STATUS == AWAT10.StatusVal.NotStarted)
                {
                    @*<button class="btn btn-default" type="button" onclick="onSave(true)">暫存儲存</button>*@
                    if ((Model.Edit != null ? Model.Edit.A_NO : null) != null)
                    {
                        <button class="btn btn-default" type="button" onclick="onDel()">作廢</button>
                    }
                    <button class="btn btn-default" type="button" onclick="onSave(false)">確定送出</button>
                    <button class="btn btn-default" type="button" onclick="onBack()">返回</button>
                }
                else if (STATUS == AWAT10.StatusVal.SetUp)
                {
                    if ((Model.Edit != null ? Model.Edit.PERIOD_DATEE : null) > DateTime.Now)
                    {
                        <button class="btn btn-default" type="button" onclick="onCloseSave()">確定解約</button>
                        <button class="btn btn-default" type="button" onclick="onBack()">返回</button>
                    }
                    else
                    {
                        <button class="btn btn-default" type="button" onclick="onBack()">返回</button>
                    }
                }
                else
                {
                    <button class="btn btn-default" type="button" onclick="onBack()">返回</button>
                }
            
          


        </div>
        <div class="form-group" style="margin-left:37px">

            <label class="text-info">
                說明:<br>
                @if (STATUS == AWAT10.StatusVal.NotStarted)
                {
                   <samp>
                       1、定存金額每筆最少為500點。<br>
                       2、利息四捨五入到整數位。<br>
                       3、到期自動續存：定存到期後，系統會自動將該筆再定存一週期，直到持有人自行解約。<br>
                       4、本息續存：係指到期後，本金 + 利息同時續存，採用<span class="text-danger">複利</span>計息。<br>
                       5、存本取息：係指到期後，本金續存，利息不續存，採用<span class="text-danger">單利</span>計息。<br>
                    </samp>

                }
                else if (STATUS == AWAT10.StatusVal.SetUp)
                {
                    <samp>
                        1、若需提前解約，解約利息打八折<br>
                    </samp>
                }
                else
                {
                    <samp>
                        1、定存金額每筆最少為500點。<br>
                        2、若需提前解約，解約利息打八折<br>
                        3、利息四捨五入到整數位。<br>
                        3、「到期」自動續存：定存到期後，系統會自動將該筆再定存一週期，直到持有人自行解約。<br>
                        4、本息續存：係指到期後，本金 + 利息同時續存，採用<span class="text-danger">複利</span>計息。<br>
                        5、存本取息：係指到期後，本金續存，利息不續存，採用<span class="text-danger">單利</span>計息。<br>
                    </samp>
                }
        </label>

    </div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#formEdit';

        $(document).ready(function () {
            $(".colorbox").colorbox({ iframe: true, width: "300px", height: "300px" });
        });

        function onTest() {
            var MATURITY_TYPE = $('input[name="@(Html.NameFor(m => m.Edit.MATURITY_TYPE))"]:checked').val();
            var ACCT_CODE = $('input[name="@(Html.NameFor(m => m.Edit.ACCT_CODE))"]:checked').val();
            var PERIOD_TYPE = $('#@Html.IdFor(m=>m.Edit.PERIOD_TYPE)').find(":selected").val();
            var AMT = $('#@Html.IdFor(m=>m.Edit.AMT)').val();
            



            if (ACCT_CODE == 'undefined' ) {
                alert('存款類型未輸入')
               return false;
             }

            if (PERIOD_TYPE == '') {
                alert('定存期別未輸入')
                        return false;
            }
            if (AMT == '') {
                alert('定存酷幣未輸入')
                        return false;
            }

            if (isNaN(AMT) || AMT < 0 ) {
                         alert('定存酷幣請輸入數字')
                        return false;
                    }



                    $.ajax({
                        url: "@(Url.Action("InterestRatesTest", (string)ViewBag.BRE_NO))",     // url位置
                        type: 'post',                   // post/get
                        data: {
                            MATURITY_TYPE: MATURITY_TYPE,
                            ACCT_CODE: ACCT_CODE,
                            PERIOD_TYPE: PERIOD_TYPE,
                            AMT: AMT
                        },     // data
                        dataType: 'json',               // xml/json/script/html
                        cache: false,                   // 是否允許快取
                        success: function (data) {
                            var res = jQuery.parseJSON(data);

                            if (res.Success == 'false') {
                                alert(res.Error);
                            }
                            else {
                                $('#PrincipleAndInterestAmt').val(parseFloat(res.PrincipleAndInterestAmt));
                            }
                        },
                        error: function (xhr, err) {
                            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                            alert("responseText: " + xhr.responseText);
                        }

                    });

        }

        function onCloseTest() {

            var A_NO = $('#@Html.IdFor(m=>m.Edit.A_NO)').val();

                    $.ajax({
                        url: "@(Url.Action("CloseOrderTest", (string)ViewBag.BRE_NO))",     // url位置
                        type: 'post',                   // post/get
                        data: {
                            A_NO: A_NO,
                        },     // data
                        dataType: 'json',               // xml/json/script/html
                        cache: false,                   // 是否允許快取
                        success: function (data) {
                            var res = jQuery.parseJSON(data);

                            if (res.Success == 'false') {
                                alert(res.Error);
                            }
                            else {
                                $('#ClosePrincipleAndInterestAmt').val(parseFloat(res.PrincipleAndInterestAmt));
                            }
                        },
                        error: function (xhr, err) {
                            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                            alert("responseText: " + xhr.responseText);
                        }
                    });
        }

        function onSave(IsTemp) {

            $('#@Html.IdFor(m=>m.IsTemp)').val(IsTemp);
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }


        function onCloseSave()
        {
            $(targetFormID).attr("action", "@Url.Action("CloseSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("MyList", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onDel() {
            $(targetFormID).attr("action", "@Url.Action("DelSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        changeMaturity_Type();
        function changeMaturity_Type() {
            var selectType = $('input[name="@Html.NameFor(m=>m.Edit.MATURITY_TYPE)"]:checked').val();
            if (selectType == 2) {
                $("#acctCodeType").hide();
            }
            else {
                $("#acctCodeType").show();
            }
        }
    </script>
}