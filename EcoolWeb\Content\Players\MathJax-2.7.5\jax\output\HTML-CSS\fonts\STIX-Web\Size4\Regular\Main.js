/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Size4/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Size4={directory:"Size4/Regular",family:"STIXMathJax_Size4",testString:"\u00A0\u02C6\u02C7\u02DC\u02F7\u0302\u0303\u0305\u030C\u0330\u0332\u0338\u203E\u20D0\u20D1",32:[0,0,250,0,0],40:[2566,509,808,124,732],41:[2566,509,808,76,684],47:[2566,509,1309,16,1293],91:[2566,509,661,295,634],92:[2566,509,1309,16,1293],93:[2566,509,661,27,366],123:[2566,509,1076,173,882],125:[2566,509,1076,194,903],160:[0,0,250,0,0],710:[796,-573,1886,0,1886],711:[796,-573,1886,0,1886],732:[771,-608,1886,0,1886],759:[-117,280,1886,0,1886],770:[796,-573,1886,0,1886],771:[771,-608,1886,0,1886],773:[820,-770,2500,0,2500],780:[796,-573,1886,0,1886],816:[-117,280,1886,0,1886],818:[-127,177,2500,0,2500],824:[731,228,0,-490,-169],8254:[820,-770,2500,0,2500],8400:[749,-584,2180,0,2180],8401:[749,-584,2180,0,2180],8406:[735,-482,2180,0,2180],8407:[735,-482,2180,0,2180],8428:[-123,288,2180,0,2180],8429:[-123,288,2180,0,2180],8430:[-26,279,2180,0,2180],8431:[-26,279,2180,0,2180],8968:[2566,509,682,295,655],8969:[2566,509,682,27,387],8970:[2566,509,682,295,655],8971:[2566,509,682,27,387],9140:[766,-544,2692,84,2608],9141:[139,83,2692,84,2608],9180:[76,168,2328,0,2328],9181:[817,-573,2328,0,2328],9182:[175,90,2328,0,2328],9183:[837,-572,2328,0,2328],9184:[66,212,2738,0,2738],9185:[842,-564,2738,0,2738],10098:[2566,509,1031,320,959],10099:[2566,509,1031,72,711],10214:[2566,509,778,295,708],10215:[2566,509,778,70,483],10216:[2566,509,908,113,796],10217:[2566,509,908,112,795],10218:[2566,509,1273,126,1133],10219:[2566,509,1273,140,1147],10627:[2566,509,1225,182,1052],10628:[2566,509,1225,173,1043],10629:[2566,509,1175,195,1050],10630:[2566,509,1175,194,1049]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Size4"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size4/Regular/Main.js"]);
