/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsD/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXIntegralsD,{32:[0,0,250,0,0],160:[0,0,250,0,0],8748:[2000,269,895,56,1345],8749:[2000,269,1205,56,1655],8751:[2000,269,945,56,1345],8752:[2000,269,1255,56,1655],8753:[2000,269,635,56,1035],8754:[2000,269,635,56,1035],8755:[2000,269,635,56,1035],10763:[2000,269,914,56,1035],10764:[2000,269,1515,56,1965],10765:[2000,269,635,56,1035],10766:[2000,269,635,56,1035],10767:[2000,269,635,56,1035],10768:[2000,269,635,56,1035],10769:[2000,269,635,56,1035],10770:[2000,269,735,56,1035],10771:[2000,269,635,56,1035],10772:[2000,269,844,56,1054],10773:[2000,269,635,56,1035],10774:[2000,269,735,56,1035],10775:[2000,269,819,24,1039],10776:[2000,269,635,56,1035],10777:[2000,269,735,56,1035],10778:[2000,269,735,56,1035],10779:[2157,269,636,56,1036],10780:[2000,426,585,56,1035]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsD/Regular/All.js");
