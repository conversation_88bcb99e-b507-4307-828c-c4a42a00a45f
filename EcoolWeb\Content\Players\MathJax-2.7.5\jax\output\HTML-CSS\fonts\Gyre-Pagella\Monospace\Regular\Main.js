/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Monospace/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Monospace={directory:"Monospace/Regular",family:"GyrePagellaMathJax_Monospace",testString:"\u00A0\uD835\uDE70\uD835\uDE71\uD835\uDE72\uD835\uDE73\uD835\uDE74\uD835\uDE75\uD835\uDE76\uD835\uDE77\uD835\uDE78\uD835\uDE79\uD835\uDE7A\uD835\uDE7B\uD835\uDE7C\uD835\uDE7D",32:[0,0,350,0,0],160:[0,0,350,0,0],120432:[625,0,350,14,336],120433:[611,0,350,13,326],120434:[623,12,350,23,327],120435:[611,0,350,10,328],120436:[611,0,350,15,340],120437:[611,0,350,17,331],120438:[623,12,350,21,335],120439:[611,0,350,12,338],120440:[611,0,350,53,297],120441:[611,12,350,48,322],120442:[611,0,350,13,334],120443:[611,0,350,19,330],120444:[611,0,350,7,343],120445:[611,0,350,15,334],120446:[623,12,350,34,316],120447:[611,0,350,15,325],120448:[623,143,350,34,316],120449:[611,12,350,12,353],120450:[623,12,350,31,319],120451:[611,0,350,13,336],120452:[611,12,350,0,350],120453:[611,9,350,8,342],120454:[611,9,350,3,347],120455:[611,0,350,14,335],120456:[611,0,350,8,342],120457:[611,0,350,28,325],120458:[435,6,350,24,345],120459:[611,6,350,5,329],120460:[435,6,350,47,314],120461:[611,6,350,21,344],120462:[435,6,350,34,314],120463:[617,0,350,24,295],120464:[438,236,350,15,345],120465:[611,0,350,5,344],120466:[601,0,350,52,303],120467:[601,235,350,28,247],120468:[611,0,350,10,343],120469:[611,0,350,39,311],120470:[431,0,350,1,350],120471:[431,0,350,5,344],120472:[435,6,350,36,314],120473:[431,229,350,5,329],120474:[431,229,350,24,362],120475:[431,0,350,19,329],120476:[435,6,350,45,309],120477:[552,6,350,12,303],120478:[425,6,350,5,344],120479:[425,5,350,11,339],120480:[425,5,350,5,344],120481:[425,0,350,14,335],120482:[425,235,350,12,339],120483:[425,0,350,17,321],120822:[623,12,350,30,320],120823:[623,0,350,69,298],120824:[623,0,350,31,319],120825:[623,12,350,25,325],120826:[625,0,350,15,334],120827:[611,12,350,31,319],120828:[623,12,350,32,317],120829:[626,12,350,25,325],120830:[623,12,350,25,325],120831:[623,12,350,32,317]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Monospace"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Monospace/Regular/Main.js"]);
