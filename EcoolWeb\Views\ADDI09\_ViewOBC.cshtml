﻿
@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel



<div class="form-group">
    <label class="control-label col-md-3">優良表現</label>
    <div class="col-md-9">
        @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    <label class="control-label col-md-3">成績</label>
    <div class="col-md-9">
        @Html.DropDownList("ddlOAWARD_SCORE", (IEnumerable<SelectListItem>)ViewBag.Scoreitems, new { @class = "form-control" })
        @Html.Label(" ", new { @id = "lbOAWARD_SCORE", @style = "color:blue;" })
        @Html.HiddenFor(model => model.CONTENT_TXT)
        @Html.ValidationMessageFor(model => model.CONTENT_TXT, "", new { @class = "text-danger" })
    </div>
</div>
@if (!Model.Individual_Give) { 
    <div class="form-group">
        @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })
        </div>
    </div>
}
<div class="form-group">
    @Html.LabelFor(model => model.MEMO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.MEMO, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.MEMO, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        <input class="btn btn-default" type="file" name="files" />
        @Html.ValidationMessage("files", "", new { @class = "text-danger" })
    </div>
</div>


<script type="text/javascript">
    $(function () {

        //選擇成績
        $("#ddlOAWARD_SCORE").change(function () { ddlOAWARD_SCORE_onchange(); });

    });

    function ddlOAWARD_SCORE_onchange() {
        $("#CONTENT_TXT").val($("#ddlOAWARD_SCORE option:selected").text());
        $("#CASH").val($("#ddlOAWARD_SCORE option:selected").val());

        var ScoreLabel;
        if ($("#ddlOAWARD_SCORE option:selected").val() != '') {
            $('span[data-valmsg-for="CONTENT_TXT"]').text('');
            ScoreLabel = $("#ddlOAWARD_SCORE option:selected").val();
            if ($("#ddlOAWARD_SCORE option:selected").text() == '特殊獎勵') {
                ScoreLabel = '0-300';
            }
            $("#lbOAWARD_SCORE").html('獎勵數點' + ScoreLabel + '點');
        }
        else {
            $("#lbOAWARD_SCORE").html('');
            $('span[data-valmsg-for="CONTENT_TXT"]').text('*此欄位必填 ');
        }
    }

</script>
