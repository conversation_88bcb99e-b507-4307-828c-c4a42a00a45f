﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'hu', {
	border: 'Szegélyméret',
	caption: '<PERSON><PERSON><PERSON>',
	cell: {
		menu: 'Cella',
		insertBefore: 'Besz<PERSON>rás balra',
		insertAfter: 'Beszúrás jobbra',
		deleteCell: 'Cellák törlése',
		merge: 'Cellák egyesítése',
		mergeRight: 'Cellák egyesítése jobbra',
		mergeDown: 'Cellák egyesítése lefelé',
		splitHorizontal: 'Cellák szétválasztása vízszintesen',
		splitVertical: 'Cellák szétválasztása függőlegesen',
		title: 'Cella tulajdonságai',
		cellType: 'Cella típusa',
		rowSpan: 'Függőleges egyesítés',
		colSpan: 'Vízszintes egyesítés',
		wordWrap: 'Hoss<PERSON><PERSON> sorok törése',
		hAlign: 'Vízszintes igazítás',
		vAlign: 'Függőleges igazítás',
		alignBaseline: 'Alapvonalra',
		bgColor: 'Háttér színe',
		borderColor: 'Keret színe',
		data: 'Adat',
		header: 'Fejléc',
		yes: 'Igen',
		no: 'Nem',
		invalidWidth: 'A szélesség mezőbe csak számokat írhat.',
		invalidHeight: 'A magasság mezőbe csak számokat írhat.',
		invalidRowSpan: 'A függőleges egyesítés mezőbe csak számokat írhat.',
		invalidColSpan: 'A vízszintes egyesítés mezőbe csak számokat írhat.',
		chooseColor: 'Válasszon'
	},
	cellPad: 'Cella belső margó',
	cellSpace: 'Cella térköz',
	column: {
		menu: 'Oszlop',
		insertBefore: 'Beszúrás balra',
		insertAfter: 'Beszúrás jobbra',
		deleteColumn: 'Oszlopok törlése'
	},
	columns: 'Oszlopok',
	deleteTable: 'Táblázat törlése',
	headers: 'Fejlécek',
	headersBoth: 'Mindkettő',
	headersColumn: 'Első oszlop',
	headersNone: 'Nincsenek',
	headersRow: 'Első sor',
	invalidBorder: 'A szegélyméret mezőbe csak számokat írhat.',
	invalidCellPadding: 'A cella belső margó mezőbe csak számokat írhat.',
	invalidCellSpacing: 'A cella térköz mezőbe csak számokat írhat.',
	invalidCols: 'Az oszlopok számának nagyobbnak kell lenni mint 0.',
	invalidHeight: 'A magasság mezőbe csak számokat írhat.',
	invalidRows: 'A sorok számának nagyobbnak kell lenni mint 0.',
	invalidWidth: 'A szélesség mezőbe csak számokat írhat.',
	menu: 'Táblázat tulajdonságai',
	row: {
		menu: 'Sor',
		insertBefore: 'Beszúrás fölé',
		insertAfter: 'Beszúrás alá',
		deleteRow: 'Sorok törlése'
	},
	rows: 'Sorok',
	summary: 'Leírás',
	title: 'Táblázat tulajdonságai',
	toolbar: 'Táblázat',
	widthPc: 'százalék',
	widthPx: 'képpont',
	widthUnit: 'Szélesség egység'
} );
