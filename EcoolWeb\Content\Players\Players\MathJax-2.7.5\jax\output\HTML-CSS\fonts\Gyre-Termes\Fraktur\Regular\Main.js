/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Fraktur/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Fraktur={directory:"Fraktur/Regular",family:"GyreTermesMathJax_Fraktur",testString:"\u00A0\u210C\u2128\u212D\uD835\uDD04\uD835\uDD05\uD835\uDD07\uD835\uDD08\uD835\uDD09\uD835\uDD0A\uD835\uDD0D\uD835\uDD0E\uD835\uDD0F\uD835\uDD10\uD835\uDD11",32:[0,0,250,0,0],160:[0,0,250,0,0],8460:[659,159,749,80,669],8488:[656,183,589,80,509],8493:[657,19,707,80,627],120068:[656,22,742,80,662],120069:[657,21,853,80,773],120071:[656,38,865,80,785],120072:[660,19,684,80,604],120073:[658,172,695,80,615],120074:[671,21,771,80,691],120077:[659,152,633,79,553],120078:[656,21,764,80,684],120079:[656,21,603,80,523],120080:[659,22,1030,80,950],120081:[659,22,798,80,718],120082:[675,19,813,80,733],120083:[658,175,731,80,651],120084:[675,31,869,80,789],120086:[657,21,794,80,714],120087:[658,21,708,80,628],120088:[657,28,685,80,605],120089:[656,19,842,80,762],120090:[669,23,1065,80,985],120091:[654,20,680,80,600],120092:[660,178,774,80,694],120094:[440,31,520,80,440],120095:[637,21,466,80,386],120096:[442,24,384,80,304],120097:[637,21,484,80,404],120098:[440,24,410,79,330],120099:[640,174,412,80,332],120100:[440,177,487,80,407],120101:[637,175,474,80,394],120102:[647,25,337,80,257],120103:[647,177,319,80,239],120104:[637,16,350,80,270],120105:[637,16,343,80,263],120106:[445,24,730,80,650],120107:[445,24,534,80,454],120108:[446,21,464,80,384],120109:[463,174,498,80,418],120110:[442,178,474,80,393],120111:[445,24,409,80,329],120112:[440,20,505,80,425],120113:[543,16,354,80,274],120114:[440,28,534,80,454],120115:[463,21,484,80,404],120116:[463,21,666,80,586],120117:[447,22,473,80,393],120118:[463,175,485,80,405],120119:[459,177,371,80,291],120172:[654,24,781,80,701],120173:[657,19,859,80,779],120174:[659,20,731,80,651],120175:[659,38,836,80,756],120176:[660,18,717,80,637],120177:[663,167,731,80,651],120178:[676,20,812,80,732],120179:[658,164,770,80,690],120180:[666,23,651,80,571],120181:[666,191,689,80,609],120182:[662,20,795,80,715],120183:[658,21,654,80,574],120184:[665,34,1091,80,1011],120185:[660,27,834,80,754],120186:[666,18,837,80,757],120187:[677,170,781,80,701],120188:[666,68,937,80,857],120189:[657,19,852,80,772],120190:[656,19,811,80,731],120191:[660,20,746,80,666],120192:[655,30,720,80,640],120193:[656,18,838,80,758],120194:[667,21,1058,80,978],120195:[652,19,716,80,636],120196:[661,209,774,80,694],120197:[653,179,612,80,532],120198:[439,35,549,80,469],120199:[627,19,491,80,411],120200:[441,30,420,80,340],120201:[629,19,506,80,426],120202:[439,30,438,80,358],120203:[629,167,440,80,360],120204:[438,169,508,80,428],120205:[627,175,507,80,427],120206:[657,23,378,80,298],120207:[657,169,360,80,280],120208:[628,21,379,80,299],120209:[627,26,388,80,308],120210:[450,20,752,80,672],120211:[450,20,567,80,487],120212:[454,20,496,79,416],120213:[470,167,524,80,444],120214:[441,170,496,80,416],120215:[450,25,440,80,360],120216:[443,19,532,80,452],120217:[538,21,381,80,301],120218:[435,36,567,80,487],120219:[469,20,517,80,437],120220:[469,20,701,80,621],120221:[452,29,504,80,424],120222:[469,175,512,80,432],120223:[461,170,403,80,323]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Fraktur"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Fraktur/Regular/Main.js"]);
