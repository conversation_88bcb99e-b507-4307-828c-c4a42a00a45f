﻿
@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })


    <img src="~/Content/img/web-bar3-revise-21.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ZZZI26">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group text-center">
                @{
                    var BtnItem = ViewBag.tModeList as List<SelectListItem>;
                    foreach (var Btn in BtnItem)
                    {
                        <div class="col-md-4">
                            @Html.ActionLink(Btn.Text, Btn.Value, new { ModeValString = Btn.Value }, new { @class = "btn btn-default  btn-block", @role = "button" })
                        </div>
                    }
                }
            </div>

        </div>
    </div>
    <div class="form-group ">
        <label class="text-danger col-md-12">
            1.「多學生單一書本(ZIP)」為「批次閱讀認證」的升級版，上傳Zip檔後可再勾選是否推薦。
        </label>
    </div>
}

