﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    public class ErrorController : Controller
    {
        private string ControllerChtName = "Error";

        /// <summary>
        /// Indexes the specified error.
        /// </summary>
        /// <returns></returns>
        [PreventDirectAccess]
        public virtual ActionResult Index(Exception exception = null, string HttpCode = "")
        {
            ViewBag.ControllerAction = "Index";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 處理你的請求發生錯誤!";
            if (exception == null)
            {
                ViewData["Description"] = "沒有exception";
            }
            else
            {
                ViewData["Description"] = exception.Message;
            }

            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View();
            }
        }

        /// <summary>
        /// Pages the not found.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        [PreventDirectAccess]
        public virtual ActionResult PageNotFound(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "PageNotFound";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 找不到您要的網頁";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        public virtual ActionResult PermissionError1999(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "PermissionError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "您沒有閱覽權限，如果想觀看精彩內容，請登入";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        [PreventDirectAccess]
        public virtual ActionResult ControllerNotFound(string error, string HttpCode = "")
        {
            return RedirectToAction("Logout", "Home");
        }

        /// <summary>
        /// Internals the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult InternalError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "InternalError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 處理你的請求發生500錯誤";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        /// <summary>
        /// Permission the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult PermissionError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "PermissionError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 您沒有權限";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        /// <summary>
        /// Permission the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult Error403(string error, string HttpCode = "")
        {
            return new HttpStatusCodeResult(403);
        }

        /// <summary>
        /// Not Data the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult NotSeeDataError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "NotDataError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 您無權看此筆資料";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        /// <summary>
        /// Not Data the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult NotDataError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "NotDataError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 沒有資料";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        /// <summary>
        /// Not Data the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult NotFindError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "NotDataError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 找不到任何資料";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        /// <summary>
        /// Not Data the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult NotFileError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "NotDataError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 找不到檔案";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        /// <summary>
        /// Not Parameter the error.
        /// </summary>
        /// <param name="error">The error.</param>
        /// <returns></returns>
        public virtual ActionResult NotParameterError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "NotDataError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 未傳入正確參數";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        public virtual ActionResult SessionTimeOutError1999(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "SessionTimeOutError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "你沒有閱覽權限，如果想觀看精彩內容，請登入網頁";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        public virtual ActionResult SessionTimeOutError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "SessionTimeOutError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "登入逾時!請重新登入";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        public virtual ActionResult GenericError(string error, string HttpCode = "")
        {
            ViewBag.ControllerAction = "GenericError";
            ViewBag.ControllerChtName = ControllerChtName;
            ViewData["Title"] = "抱歉, 處理你的請求發生通用Generic錯誤";
            ViewData["Description"] = error;
            ViewData["HttpCode"] = HttpCode;
            Response.StatusCode = 200;
            if (HttpContext.Request.IsAjaxRequest())
                return PartialView();
            else
            {
                ViewData["Layout"] = EcoolWeb.Models.UserProfileHelper.GetLayoutCookie();
                return View("Index");
            }
        }

        private class PreventDirectAccessAttribute : FilterAttribute, IAuthorizationFilter
        {
            public void OnAuthorization(AuthorizationContext filterContext)
            {
                object value = filterContext.RouteData.Values["from_Application_Error_Event"];
                if (!(value is bool && (bool)value))
                {
                    filterContext.Result = new ViewResult { ViewName = "PageNotFound" };
                }
            }
        }
    }
}