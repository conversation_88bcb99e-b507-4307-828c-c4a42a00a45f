﻿@model ADDT26

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_ByOneGameMenu", new { NowAction = "QueryTeamSetView" });
}

@if (TempData["StatusMessage"] == null)
{
    using (Html.BeginForm("QueryTeamSetView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit" }))
    {
        @Html.HiddenFor(m => m.GAME_NO)
        <input id="Search_WhereGAME_NO" name="Search.WhereGAME_NO" type="hidden" value="@Model.GAME_NO" />

        <div class="row">
            <div class="col-md-6 col-md-offset-3">
                <div class="btn-group" role="group" aria-label="...">
                    <button type="button" class="btn btn-group btn-default " id="BtnTeamGameStart" onclick="OnTeamGameStart()" @(Model.TEAM_GAME_DATES != null ? "disabled" : "")>
                        <span class="glyphicon glyphicon-camera" aria-hidden="true"> </span>
                        @if (Model.TEAM_GAME_DATES != null)
                        {
                            @:開始時間 @Model.TEAM_GAME_DATES
                        }
                        else
                        {
                            @: 比賽開始
                        }
                    </button>

                    <button type="button" class="btn btn-group btn btn-default " id="BtnTeamGameEnd" onclick="OnTeamGameEnd()" @(Model.TEAM_GAME_DATEE != null ? "disabled" : "")>
                        <span class="glyphicon glyphicon-camera" aria-hidden="true"> </span>

                        @if (Model.TEAM_GAME_DATEE != null)
                        {
                            @:結束時間 @Model.TEAM_GAME_DATEE
                        }
                        else
                        {
                            @: 比賽結束
                        }
                    </button>

                    @if (Model.TEAM_GAME_DATES != null)
                    {
                        <button type="button" class="btn btn-group btn btn-default " id="BtnTeamGameEnd" onclick="OnTeamGameStart()">
                            <span class="glyphicon glyphicon-camera" aria-hidden="true"> </span>
                            重新開始
                        </button>
                    }
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-md-offset-3">
                <button type="button" onclick="OnRenew()" class="btn btn-default btn-lg btn-block">重新整理</button>
            </div>
        </div>

        @Html.Action("QueryTeamStatus", "Game", new { GAME_NO = Model.GAME_NO })

    }
}

<script type="text/javascript">

       function OnRenew() {
              $('#formEdit').submit();
         }

        //比賽開始
        function OnTeamGameStart() {
            $.ajax({
                url: "@(Url.Action("TeamGameStart", "Game"))",     // url位置
                type: 'post',                   // post/get
                data: {
                    GAME_NO: $('#@Html.IdFor(m => m.GAME_NO)').val()
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                     var res = jQuery.parseJSON(data);

                    if (res.Success.toLowerCase == 'false') {
                        alert(res.Error)
                    }
                    else {
                        OnRenew()
                    }
                },
                error: function (xhr, err) {
                alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                alert("responseText: " + xhr.responseText);
                }
            });
        }

        //比賽結束
        function OnTeamGameEnd() {
                   $.ajax({
                url: "@(Url.Action("TeamGameEnd", "Game"))",     // url位置
                type: 'post',                   // post/get
                data: {
                    GAME_NO: $('#@Html.IdFor(m => m.GAME_NO)').val()
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                     var res = jQuery.parseJSON(data);

                    if (res.Success.toLowerCase == 'false') {
                        alert(res.Error)
                    }
                    else {
                        OnRenew()
                    }
                },
                error: function (xhr, err) {
                alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                alert("responseText: " + xhr.responseText);
                }
            });
    }
</script>