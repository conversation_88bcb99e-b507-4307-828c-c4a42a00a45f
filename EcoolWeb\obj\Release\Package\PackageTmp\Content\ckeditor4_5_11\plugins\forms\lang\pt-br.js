﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'pt-br', {
	button: {
		title: 'Formatar Botão',
		text: 'Texto (Valor)',
		type: 'Tipo',
		typeBtn: 'Bot<PERSON>',
		typeSbm: 'Enviar',
		typeRst: 'Limpar'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Formatar Caixa de Seleção',
		radioTitle: 'Formatar Botão de Opção',
		value: 'Valor',
		selected: 'Selecionado',
		required: 'Obrigatório'
	},
	form: {
		title: 'Formatar Formulário',
		menu: 'Formatar Formulário',
		action: 'Ação',
		method: 'Método',
		encoding: 'Codificação'
	},
	hidden: {
		title: 'Formatar Campo Oculto',
		name: 'Nome',
		value: 'Valor'
	},
	select: {
		title: 'Formatar Caixa de Listagem',
		selectInfo: 'Informações',
		opAvail: 'Opções disponíveis',
		value: 'Valor',
		size: 'Tamanho',
		lines: 'linhas',
		chkMulti: 'Permitir múltiplas seleções',
		required: 'Obrigatório',
		opText: 'Texto',
		opValue: 'Valor',
		btnAdd: 'Adicionar',
		btnModify: 'Modificar',
		btnUp: 'Para cima',
		btnDown: 'Para baixo',
		btnSetValue: 'Definir como selecionado',
		btnDelete: 'Remover'
	},
	textarea: {
		title: 'Formatar Área de Texto',
		cols: 'Colunas',
		rows: 'Linhas'
	},
	textfield: {
		title: 'Formatar Caixa de Texto',
		name: 'Nome',
		value: 'Valor',
		charWidth: 'Comprimento (em caracteres)',
		maxChars: 'Número Máximo de Caracteres',
		required: 'Obrigatório',
		type: 'Tipo',
		typeText: 'Texto',
		typePass: 'Senha',
		typeEmail: 'Email',
		typeSearch: 'Busca',
		typeTel: 'Número de Telefone',
		typeUrl: 'URL'
	}
} );
