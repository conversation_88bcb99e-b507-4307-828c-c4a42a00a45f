﻿@model ECOOL_APP.com.ecool.Models.entity.BT02AmdinViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

}
<script src="~/Content/ckeditor/ckeditor.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<br />

@using (Html.BeginForm("Create", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()

    <img src="~/Content/img/web-bar2-revise-02.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ZZZI04">
        <div class="form-horizontal">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.HiddenFor(a => a.CRE_PERSON)
            @Html.HiddenFor(a => a.REF_KEY)

            <input id="BULLET_ID" name="BULLET_ID" type="hidden" value="@ViewBag.BULLET_ID" />
            <div class="form-group">
                <div class="col-md-10" align="center">
                    <samp class="text-danger">
                        @Html.ValidationMessage("ErrorMsg")
                    </samp>
                </div>
            </div>
            <div class="form-group" style="display:@(SharedGlobal.HomeIndex == "ChildMonthIndex" ? "none":"block")">
                @Html.LabelFor(model => model.SCHOOL_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.SCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoItems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.SCHOOL_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_TYPE, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">

                    @Html.DropDownListFor(model => model.CLASS_TYPE, (IEnumerable<SelectListItem>)ViewBag.ClassTypeItems, new { @class = "form-control" })
                    @*@Html.EditorFor(model => model.CLASS_TYPE, new { htmlAttributes = new { @class = "form-control" } })*@
                    @Html.ValidationMessageFor(model => model.CLASS_TYPE, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.ISPUBLISH, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div class="checkbox">
                        @Html.CheckBox("ISPUBLISH", (bool)ViewBag.ISPUBLISH) @Resources.Resource.YES
                        @Html.ValidationMessageFor(model => model.ISPUBLISH, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.PUSH_YN, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div class="checkbox">
                        @Html.CheckBox("PUSH_YN", (bool)ViewBag.PUSH_YN) @Resources.Resource.YES
                        @Html.ValidationMessageFor(model => model.PUSH_YN, "", new { @class = "text-danger" })
                    </div>
                    <button type="button" class="btn btn-default" title="選取通知人員" id="BTN_PUSH" href="@Url.Action("Index", "APPT03", new { BTN_ID = "#BTN_PUSH",REF_TABLE = "BET02" ,REF_KEY= Model.REF_KEY})">選取通知人員</button>
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.S_DATE, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.S_DATE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                    @Html.ValidationMessageFor(model => model.S_DATE, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.E_DATE, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.E_DATE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                    @Html.ValidationMessageFor(model => model.E_DATE, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.TOP_YN, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div class="checkbox">
                        @Html.CheckBox("TOP_YN", (bool)ViewBag.TOP_YN) @Html.Label(Resources.Resource.YES)
                        @Html.ValidationMessageFor(model => model.TOP_YN, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.MEMO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.MEMO, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.MEMO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2" for="File input">附件</label>
                <div class="col-md-10">
                    @{
                        if (Model.Details_FILE != null)
                        {
                            int Num = 0;

                            foreach (var FILEItem in Model.Details_FILE)
                            {
                                @Html.Hidden("Details_FILE[" + Num + "].BULLET_ID", FILEItem.BULLET_ID)
                                @Html.Hidden("Details_FILE[" + Num + "].ITEM_NO", FILEItem.ITEM_NO)
                                @Html.Hidden("Details_FILE[" + Num + "].FILE_NAME", FILEItem.FILE_NAME)

                                @Html.ActionLink(FILEItem.FILE_NAME, "DownLoad", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = Model.BULLET_ID, name = FILEItem.FILE_NAME }, new { @class = "btn btn-link" })
                                @Html.ActionLink("X", "Delete", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = Model.BULLET_ID, ITEM_NO = FILEItem.ITEM_NO, name = FILEItem.FILE_NAME }, new { @class = "btn btn-default" })

                                Num++;
                            }
                        }
                    }
                </div>
            </div>

            <div class="form-group">
                    @Html.LabelFor(model => model.LinkContext, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.LinkContext, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.LinkContext, "", new { @class = "text-danger" })
                    </div>
                </div>
            <div class="form-group">
                <label class="control-label col-md-2" for="File input">@Resources.Resource.File_input</label>
                <div class="col-md-10">
                    @Html.Action("MultiFileUpload", "Comm")
                </div>
            </div>

            <ul class="nav nav-tabs align_left">
                @foreach (var LangItem in ViewBag.LANG as List<ECOOL_APP.com.ecool.Models.entity.uSYST02>)
                {
                    if (LangItem.ORDER_BY == 0)
                    {
                        <li class="active"><a aria-expanded="true" href="#@LangItem.LANGUAGE_ID" data-toggle="tab">@LangItem.LANGUAGE_NAME</a></li>
                    }
                    else
                    {
                        <li><a aria-expanded="false" href="#@LangItem.LANGUAGE_ID" data-toggle="tab">@LangItem.LANGUAGE_NAME</a></li>
                    }
                }
            </ul>
            <div class="tab-content" id="myTabContent">
                @foreach (var LangItem in ViewBag.LANG as List<ECOOL_APP.com.ecool.Models.entity.uSYST02>)
                {
                    if (LangItem.ORDER_BY == 0)
                    {
                        <div class="tab-pane fade active in" id="@LangItem.LANGUAGE_ID">
                            <p>
                                @Html.Hidden("Details_LANG[" + LangItem.ORDER_BY + "].LANGUAGE_ID", LangItem.LANGUAGE_ID)
                                <div class="form-group">
                                    @Html.LabelFor(model => model.Details_LANG[LangItem.ORDER_BY].SUBJECT, htmlAttributes: new { @class = "control-label col-md-2" })
                                    <div class="col-md-10">
                                        @Html.EditorFor(model => model.Details_LANG[LangItem.ORDER_BY].SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
                                        @Html.ValidationMessageFor(model => model.Details_LANG[LangItem.ORDER_BY].SUBJECT, "", new { @class = "text-danger" })
                                    </div>
                                </div>
                                <div class="form-group">
                                    @Html.LabelFor(model => model.Details_LANG[LangItem.ORDER_BY].CONTENT_TXT, htmlAttributes: new { @class = "control-label col-md-2" })
                                    <div class="col-md-10">
                                        @Html.TextAreaFor(model => model.Details_LANG[LangItem.ORDER_BY].CONTENT_TXT, 10, 100, new { @class = "ckeditor" })
                                        @Html.ValidationMessageFor(model => model.Details_LANG[LangItem.ORDER_BY].CONTENT_TXT, "", new { @class = "text-danger" })
                                    </div>
                                </div>
                            </p>
                        </div>
                    }
                    else
                    {
                        <div class="tab-pane fade" id="@LangItem.LANGUAGE_ID">
                            <p>
                                @Html.Hidden("Details_LANG[" + LangItem.ORDER_BY + "].LANGUAGE_ID", LangItem.LANGUAGE_ID)
                                <div class="form-group">
                                    @Html.LabelFor(model => model.Details_LANG[LangItem.ORDER_BY].SUBJECT, htmlAttributes: new { @class = "control-label col-md-2" })
                                    <div class="col-md-10">
                                        @Html.EditorFor(model => model.Details_LANG[LangItem.ORDER_BY].SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
                                        @Html.ValidationMessageFor(model => model.Details_LANG[LangItem.ORDER_BY].SUBJECT, "", new { @class = "text-danger" })
                                    </div>
                                </div>
                                <div class="form-group">
                                    @Html.LabelFor(model => model.Details_LANG[LangItem.ORDER_BY].CONTENT_TXT, htmlAttributes: new { @class = "control-label col-md-2" })
                                    <div class="col-md-10">
                                        @Html.TextAreaFor(model => model.Details_LANG[LangItem.ORDER_BY].CONTENT_TXT, 10, 100, new { @class = "form-control" })
                                        @Html.ValidationMessageFor(model => model.Details_LANG[LangItem.ORDER_BY].CONTENT_TXT, "", new { @class = "text-danger" })
                                    </div>
                                </div>
                            </p>
                        </div>
                    }
                }
            </div>
            <div class="form-group Div-btn-center">
                @if (ViewBag.Btn)
                {
                    <input type="submit" value="@ViewBag.submit" class="btn btn-default" />

                    if (ViewBag.BULLET_ID != null)
                    {
                        @Html.ActionLink(Resources.Resource.DELETE, "DeleteALL", new { controller = (string)ViewBag.BRE_NO, BULLET_ID = Model.BULLET_ID }, new { @class = "btn btn-default" })
                    }
                }
            </div>
        </div>
    </div>
    <div style="height:10px"></div>
    <div class="text-center">
        @Html.ActionLink("返回", "List", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-default" })
    </div>

}

@section Scripts {
    <script language="JavaScript">

                $(document).ready(function () {
                     $("#BTN_PUSH").colorbox({
                     iframe: true, width: "80%", height: "80%", opacity: 0.82
                     });
                });

            $(document).ready(function () {

                $("#S_DATE,#E_DATE").datepicker({
                    dateFormat: "yy/mm/dd",
                    changeMonth: true,
                    changeYear: true,
                    showOn: "button",
                    buttonImage: "../Content/img/icon/calendar.gif",
                    buttonImageOnly: true,
                });

                var Today = new Date();

                if ($("#S_DATE").val() == "")
                {
                    $("#S_DATE").datepicker("setDate", Today);
                }

                if ($("#E_DATE").val() == "") {
                    Today.setMonth(Today.getMonth() + 12);
                    $("#E_DATE").datepicker('setDate', Today);
                }

               CKEDITOR.replace('Details_LANG_0__CONTENT_TXT', {toolbar: 'Image', filebrowserImageUploadUrl: '@Url.Content("~/ZZZI04/UploadPicture")' });

            });
    </script>
}