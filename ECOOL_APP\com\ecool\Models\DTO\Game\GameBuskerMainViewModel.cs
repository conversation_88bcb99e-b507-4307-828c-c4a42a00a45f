﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public  class GameBuskerMainViewModel
    {

        /// <summary>
        ///表演 id
        /// </summary>
        [DisplayName("表演 id")]
        public string TITLE_SHOW_ID { get; set; }

        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///表演名稱
        /// </summary>
        [DisplayName("表演名稱")]
        [Required]
        public string TITLE_SHOW_NAME { get; set; }

        /// <summary>
        ///順序
        /// </summary>
        [DisplayName("順序")]
        public int? ORDER_BY { get; set; }

        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("建立人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///報名日期
        /// </summary>
        [DisplayName("報名日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日
        /// </summary>
        [DisplayName("修改日")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///圖片
        /// </summary>
        [DisplayName("圖片")]
        public string TITLE_IMG { get; set; }

        /// <summary>
        ///直播
        /// </summary>
        [DisplayName("直播")]
        public bool? LIVE_STREAM { get; set; }

        /// <summary>
        ///獲得點數
        /// </summary>
        [DisplayName("獲得點數")]
        public short? CASH { get; set; }
    }
}
