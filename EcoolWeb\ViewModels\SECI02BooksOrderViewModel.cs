﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using ECOOL_APP.EF;
using EcoolWeb.ViewModels;
using MvcPaging;

namespace EcoolWeb.Models
{
    public class SECI02BooksOrderViewModel
    {
        public string WhereSYEAR { get; set; }

        public string WhereMM { get; set; }
       
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }
        public Nullable<int> ShareCountSUM { get; set; }
        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }
        public string whereSeat_NO { get; set; }
        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生
        /// </summary>
        public string whereUserNo { get; set; }
        public DateTime? WhereUP_DATE_START { get; set; }

        public DateTime? WhereUP_DATE_END { get; set; }
        /// <summary>
        /// 月排行榜
        /// </summary>
        public bool WhereIsMonthTop { get; set; }

        public bool IsPrint { get; set; }

        public bool IsToExcel { get; set; }
       public string BORROW_DATE { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public string fromStr { get; set; }
        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<V_DB2_L_WORK2> BooksList;
        public IPagedList<V_DB_L_WORK2ViewModel> BooksListViewModel;
        /// <summary>
        /// 是否輪播不同網頁
        /// </summary>
        public bool isCarousel { get; set; }

        public SECI02BooksOrderViewModel()
        {
            Page = 0;
            OrdercColumn = "SE_QTY";
        }
    }
}