﻿@model ADDI05PassListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("PassList", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(m => m.SearchContents)
    @Html.HiddenFor(m => m.OrderByName)
    @Html.HiddenFor(m => m.SyntaxName)
    @Html.HiddenFor(m => m.page)
    @Html.HiddenFor(m => m.DIALOG_ID)
    @Html.HiddenFor(m => m.LotteryCount)
    @Html.HiddenFor(m => m.PassPage)

    @Html.Partial("_ADDI05Menu", 0)
    <table>
        <tr>
            <td>
                <img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive" alt="Responsive image" />
                <div class="Div-EZ-ADDI05">
                    <div class="Details">
                        <div class="table-responsive">
                            <div class="text-center">
                                <table class="table-ecool table-92Per table-hover">
                                    <caption class="Caption_Div_Left">
                                        活動名稱：@ViewBag.DIALOG_NAME
                                    </caption>
                                    <thead>
                                        <tr>
                                            <th style="text-align: center;cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.uADDT13.First().SHORT_NAME)
                                            </th>
                                            @*<th style="text-align: center;cursor:pointer;">
                                                    @Html.DisplayNameFor(model => model.uADDT13.First().SYEAR)
                                                </th>
                                                <th style="text-align: center;cursor:pointer;">
                                                    @Html.DisplayNameFor(model => model.uADDT13.First().SEMESTER)
                                                </th>*@
                                            <th style="text-align: center;cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.uADDT13.First().LOTTO_ORDER)
                                            </th>
                                            <th style="text-align: center;cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.uADDT13.First().CLASS_NO)
                                            </th>
                                            <th style="text-align: center;cursor:pointer;">
                                                @Html.DisplayNameFor(model => model.uADDT13.First().SEAT_NO)
                                            </th>
                                            <th style="text-align: center;cursor:pointer;">
                                                姓名
                                            </th>
                                            @*<th style="text-align: center;cursor:pointer;">
                                                    答對日期
                                                </th>*@
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.uADDT13)
                                        {
                                            <tr>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                                </td>
                                                @*<td align="center">
                                                        @Html.DisplayFor(modelItem => item.SYEAR)
                                                    </td>
                                                    <td align="center">
                                                        @Html.DisplayFor(modelItem => item.SEMESTER)
                                                    </td>*@
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.LOTTO_ORDER)
                                                </td>
                                                <td align="center">
                                                    @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                                    {
                                                        <samp>-</samp>
                                                    }
                                                    else
                                                    {
                                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                                    }
                                                </td>
                                                <td align="center">
                                                    @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                                    {
                                                        <samp>-</samp>
                                                    }
                                                    else
                                                    {
                                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                                    }
                                                </td>
                                                <td align="center">
                                                    @Html.DisplayFor(modelItem => item.SNAME)
                                                </td>
                                                @*<td align="center">
                                                        @Html.DisplayFor(modelItem => item.CRE_DATE)
                                                    </td>*@
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                                <div style="height:15px"></div>
                                <div class="btn-group btn-group-justified" role="group">
                                    共 @Model.LotteryCount 人
                                </div>
                                <div style="height:15px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </td>
            <td>

                <video id="SlotPlayer" style="width:250px ;height: auto" autoplay="autoplay">
                    <source src="~/Content/mp4/RealtimePoint2.mp4" type="video/mp4">
                </video>
                <br />
                <div>
                    <div class="col-md-12 col-sm-12" style="margin-left:auto;margin-right:auto;text-align:center">
                        <button type="button" class="btn btn-default" onclick="ToExcel()">匯出excel</button>
                    </div>
                </div>
            </td>
        </tr>
    </table>

}

@section Scripts {
    <script language="JavaScript">
                var targetFormID="#form1"

                function onGo(ActionVal) {
                    if (ActionVal == "Index") {
                        form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
                    }
                    else if (ActionVal == "detail") {
                        form1.action = '@Url.Action("detail", (string)ViewBag.BRE_NO)';
                    }
                    form1.submit();
                }

                function todoClear() {
                    ////重設

                    $(targetFormID).find("#DivSearch :input,:selected").each(function (i) {

                        var type = $(this).attr('type')
                        var InPreadonly = $(this).attr('readonly')
                        var tag = this.tagName.toLowerCase(); // normalize case

                        if (InPreadonly == false || InPreadonly == undefined) {

                            if (type == 'radio' || type == 'checkbox') {
                                if ($(this).attr("title") == 'Default') {
                                    this.checked = true;
                                }
                                else {
                                    this.checked = false;
                                }
                            }
                            else if (tag == 'select') { //下拉式選單
                                this.selectedIndex = 0;
                            }
                            else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                                this.value = '';
                            }
                        }
                    });

                    FunPageProc(1)
                }

                function FunPageProc(pageno) {
                    form1.PassPage.value = pageno
                    form1.submit();
                }

                function ToExcel() {
                    $(targetFormID).attr('action', '@Url.Action("PrintExcel", (string)ViewBag.BRE_NO)').attr('target', '_blank');
                    $(targetFormID).submit();
                };

                function doSort(SortCol) {
                    $("#PassOrderByName").val(SortCol);
                    FunPageProc(1)
                }
    </script>
}