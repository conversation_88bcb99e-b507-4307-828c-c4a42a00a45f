﻿@using System.Web.Script.Serialization;
@using Newtonsoft.Json;
@using Newtonsoft.Json.Linq;
@{
    ViewBag.Title = "CheckInfo";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    var userAgent = HttpContext.Current.Request.UserAgent;

    var user = JsonConvert.SerializeObject(EcoolWeb.Models.UserProfileHelper.Get());

    string userjsonFormatted = JValue.Parse(user).ToString(Newtonsoft.Json.Formatting.Indented);

    var SchoolNoInfo = JsonConvert.SerializeObject(EcoolWeb.Models.UserProfileHelper.GetSchoolNo());

    string SchoolNoInfojsonFormatted = JValue.Parse(SchoolNoInfo).ToString(Newtonsoft.Json.Formatting.Indented);

    var UUIDInfo = JsonConvert.SerializeObject(EcoolWeb.Models.UserProfileHelper.GetUUIDCookie());

    string UUIDInfojsonFormatted = JValue.Parse(UUIDInfo).ToString(Newtonsoft.Json.Formatting.Indented);

}
<div class="row">
    <div class="col-lg-12">
        <h2>
            userAgent:
            <code>@userAgent</code>
        </h2>

        <br />
        <br />
        <h2 style="word-break: break-all">
            Session UserInfo:
            <code>@userjsonFormatted</code>
        </h2>
        <br />
        <br />
        <h2 style="word-break: break-all">
            Session School:
            <code>@SchoolNoInfojsonFormatted</code>
        </h2>
        <br />
        <br />
        <h2 style="word-break: break-all">
            Session UUID:
            <code>@UUIDInfojsonFormatted</code>
        </h2>
    </div>
</div>