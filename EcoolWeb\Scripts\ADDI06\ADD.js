﻿$(document).ready(function () {
    const ADDModule = {
        init: function () {
            this.bind();
            this.setupGlobalFunctions();
        },
        bindEvents: function () {
            $('select[onchange*="Class_No"]').on('change', this.ChangeClass_No.bind(this));
            $('select[onchange*="IAWARD_KIND"]').on('change', this.IAWARD_KIND_onchange.bind(this));
        },
        setupGlobalFunction: function () {
            window.ChangeClass_No = this.ChangeClass_No.bind(this);
            window.Add = this.Valid.bind(this);
            window.IAWARD_KIND = this.IAWARD_KIND_onchange.bind(this);
            
        },
        SetClass_NoDDLEmpty: function () {

            $('#USER_NO').empty();
            $('#USER_NO').append($('<option></option>').val('').text('請選擇學生'));

        },
        IAWARD_KIND_onchange: function () {

            var IAWARD_KIND;
            switch ($("#IAWARD_KIND option:selected").text()) {
                case "請選擇事蹟":
                    IAWARD_KIND = '';
                    $("#CASH").val('0');
                    break;
                case "志工":
                    IAWARD_KIND = '志工類每學期1次每次50-200點';
                    $("#CASH").val('50');
                    break;
                case "校內競賽":
                    IAWARD_KIND = '校內競賽每次10-200點';
                    $("#CASH").val('10');
                    break;
                case "品德表現":
                    IAWARD_KIND = '品德表現類10-200點';
                    $("#CASH").val('10');
                    break;
                case "學習表現":
                    IAWARD_KIND = '學習表現類10-200點';
                    $("#CASH").val('10');
                    break;
                case "領導人":
                    IAWARD_KIND = '';
                    break;
                case "七個習慣代言人":
                    IAWARD_KIND = '';
                    break;
            }

            $("#lbIAWARD_KIND").html(IAWARD_KIND);
        },
        ChangeClass_No: function () {
            var selectedClass_No = $.trim($('#Class_No option:selected').val());
            if (selectedClass_No.length === 0) {
                this.SetClass_NoDDLEmpty();
            } else {
                $.getJSON('@Url.Action("GetNameData").ToString()', { Class_No: selectedClass_No })
                    .done(function (data) {
                        $('#USER_NO').empty();
                        $.each(data, function (i, item) {
                            $('#USER_NO').append($('<option></option>').val(item.Value).text(item.Text));
                        });
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        console.error('Error fetching name data:', textStatus, errorThrown);
                    });
            }

        },
        Valid: function () {
            var msg = '';
            var blStatus = false;

            if ($("#USER_NO").val() === '') {
                msg += '請選擇學生\r\n';
            }

            if ($("#CASH").val().match(/^\+{0,1}\d+(\.\d{1,2})?$/) === null) {
                msg += '獎勵點數不能為負\r\n';
            }

            if ($("#OAWARD_SCORE").val() === '') {
                msg += '請選擇成績\r\n';
            }

            if ($("#CASH").val().length <= 0) {
                msg += '獎勵點數不能為空\r\n';
            }

            if (isNumber($("#CASH").val()) == false) {
                msg += '獎勵點數不為數字\r\n';
            }

            if ($("#CASH").val() > 999) {
                msg += '獎勵點數不能超過1000點\r\n';
            }

            switch ($("#IAWARD_KIND option:selected").text()) {
                case "請選擇事蹟":
                    msg += '請選擇事蹟\r\n';
                    break;
                case "志工":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 50) {
                        msg += '志工類獎勵點數需介於50-200點\r\n';
                    }
                    break;
                case "校內競賽":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 10) {
                        msg += '校內競賽類獎勵點數需介於10-200點\r\n';
                    }
                    break;
                case "品德表現":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 10) {
                        msg += '品德表現類獎勵點數需介於10-200點\r\n';
                    }
                    break;
                case "學習表現":
                    if ($("#CASH").val() > 200 || $("#CASH").val() < 10) {
                        msg += '學習表現類獎勵點數需介於10-200點\r\n';
                    }
                    break;
            }

            if (msg !== '') {
                alert(msg);
                blStatus = false;
            } else {
                blStatus = true;
            }

            return blStatus;
        },
        isNumber: function () {

            if (name.length === 0) {
                return false;
            }
            for (var i = 0; i < name.length; i++) {
                if (name.charAt(i) < "0" || name.charAt(i) > "9") {
                    return false;
                }
            }
            return true;
        }
        
    }
})