﻿// JavaScript for ADDI06 ADD page - 新增獎勵記錄
$(document).ready(function () {
    const addModule = {
        init: function () {
            this.setupGlobalFunctions();
            this.bindEvents();
        },

        bindEvents: function () {
            // 綁定班級選擇事件
            $('select[onchange*="ChangeClass_No"]').on('change', this.handleClassChange.bind(this));

            // 綁定事蹟類型選擇事件
            $('select[onchange*="IAWARD_KIND_onchange"]').on('change', this.handleAwardKindChange.bind(this));

            // 綁定表單提交事件
            $('form').on('submit', this.handleFormSubmit.bind(this));
        },

        setupGlobalFunctions: function () {
            // 設置全局函數以保持向後兼容
            window.ChangeClass_No = this.changeClass_No.bind(this);
            window.Add = this.validateForm.bind(this);
            window.IAWARD_KIND_onchange = this.awardKind_onChange.bind(this);
        },


        awardKind_onChange: function () {
            // 使用共用的事蹟類型變更函數
            return ADDI06Common.awardKindChange('#IAWARD_KIND', '#CASH', '#lbIAWARD_KIND');
        },
        changeClass_No: function () {
            try {
                const selectedClass_No = $.trim($('#Class_No option:selected').val());

                // 使用共用的學生載入函數
                ADDI06Common.loadStudentsByClass(selectedClass_No, '#USER_NO', window.ADDI06_ADD_URLS);

                console.log('ADD 班級變更:', selectedClass_No);
            } catch (error) {
                console.error('ADD 班級變更時發生錯誤:', error);
                ADDI06Common.showMessage('班級變更時發生錯誤，請稍後再試', 'error');
            }
        },
        validateForm: function () {
            try {
                let msg = '';
                let isValid = false;

                // 檢查學生選擇
                if ($("#USER_NO").val() === '' || $("#USER_NO").val() === null) {
                    msg += '請選擇學生\n';
                }

                // 檢查獎勵點數
                const cashValue = $("#CASH").val();

                if (cashValue.length <= 0) {
                    msg += '獎勵點數不能為空\n';
                } else if (!this.isNumber(cashValue)) {
                    msg += '獎勵點數必須為數字\n';
                } else {
                    const cashNum = parseInt(cashValue);

                    if (cashNum < 0) {
                        msg += '獎勵點數不能為負數\n';
                    }

                    if (cashNum > 999) {
                        msg += '獎勵點數不能超過999點\n';
                    }
                }

                // 檢查成績選擇
                if ($("#OAWARD_SCORE").val() === '' || $("#OAWARD_SCORE").val() === null) {
                    msg += '請選擇成績\n';
                }

                // 檢查事蹟類型和對應的點數範圍
                const selectedAwardKind = $("#IAWARD_KIND option:selected").text();
                const cashNum = parseInt(cashValue);

                switch (selectedAwardKind) {
                    case "請選擇事蹟":
                        msg += '請選擇事蹟\n';
                        break;
                    case "志工":
                        if (cashNum > 200 || cashNum < 50) {
                            msg += '志工類獎勵點數需介於50-200點\n';
                        }
                        break;
                    case "校內競賽":
                        if (cashNum > 200 || cashNum < 10) {
                            msg += '校內競賽類獎勵點數需介於10-200點\n';
                        }
                        break;
                    case "品德表現":
                        if (cashNum > 200 || cashNum < 10) {
                            msg += '品德表現類獎勵點數需介於10-200點\n';
                        }
                        break;
                    case "學習表現":
                        if (cashNum > 200 || cashNum < 10) {
                            msg += '學習表現類獎勵點數需介於10-200點\n';
                        }
                        break;
                }

                if (msg !== '') {
                    alert(msg);
                    isValid = false;
                } else {
                    isValid = true;
                }

                console.log('ADD 表單驗證結果:', isValid ? '通過' : '失敗');
                return isValid;
            } catch (error) {
                console.error('ADD 表單驗證時發生錯誤:', error);
                alert('表單驗證時發生錯誤，請稍後再試');
                return false;
            }
        },
        isNumber: function (value) {
            if (!value || value.length === 0) {
                return false;
            }

            // 使用正則表達式檢查是否為數字
            return /^\d+$/.test(value);
        },

        // 事件處理器
        handleClassChange: function (event) {
            this.changeClass_No();
        },

        handleAwardKindChange: function (event) {
            this.awardKind_onChange();
        },

        handleFormSubmit: function (event) {
            try {
                console.log('ADD 表單提交');

                // 如果驗證失敗，阻止表單提交
                if (!this.validateForm()) {
                    event.preventDefault();
                    return false;
                }

                return true;
            } catch (error) {
                console.error('ADD 表單提交時發生錯誤:', error);
                event.preventDefault();
                alert('表單提交時發生錯誤，請稍後再試');
                return false;
            }
        }
    };

    // 初始化模組
    addModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('ADD 頁面錯誤:', e);
    });
});