/*
 Highcharts JS v6.0.6 (2018-02-05)
 Highstock as a plugin for Highcharts

 (c) 2017 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(I){"object"===typeof module&&module.exports?module.exports=I:I(Highcharts)})(function(I){(function(a){var E=a.defined,x=a.each,B=a.extend,u=a.merge,C=a.pick,w=a.timeUnits,H=a.win;a.Time=function(a){this.update(a,!1)};a.Time.prototype={defaultOptions:{},update:function(n){var h=C(n&&n.useUTC,!0),y=this;this.options=n=u(!0,this.options||{},n);this.Date=n.Date||H.Date;this.timezoneOffset=(this.useUTC=h)&&n.timezoneOffset;this.getTimezoneOffset=this.timezoneOffsetFunction();(this.variableTimezone=
!(h&&!n.getTimezoneOffset&&!n.timezone))||this.timezoneOffset?(this.get=function(a,c){var e=c.getTime(),b=e-y.getTimezoneOffset(c);c.setTime(b);a=c["getUTC"+a]();c.setTime(e);return a},this.set=function(A,c,e){var b;if(-1!==a.inArray(A,["Milliseconds","Seconds","Minutes"]))c["set"+A](e);else b=y.getTimezoneOffset(c),b=c.getTime()-b,c.setTime(b),c["setUTC"+A](e),A=y.getTimezoneOffset(c),b=c.getTime()+A,c.setTime(b)}):h?(this.get=function(a,c){return c["getUTC"+a]()},this.set=function(a,c,e){return c["setUTC"+
a](e)}):(this.get=function(a,c){return c["get"+a]()},this.set=function(a,c,e){return c["set"+a](e)})},makeTime:function(n,h,y,A,c,e){var b,l,m;this.useUTC?(b=this.Date.UTC.apply(0,arguments),l=this.getTimezoneOffset(b),b+=l,m=this.getTimezoneOffset(b),l!==m?b+=m-l:l-36E5!==this.getTimezoneOffset(b-36E5)||a.isSafari||(b-=36E5)):b=(new this.Date(n,h,C(y,1),C(A,0),C(c,0),C(e,0))).getTime();return b},timezoneOffsetFunction:function(){var n=this,h=this.options,y=H.moment;if(!this.useUTC)return function(a){return 6E4*
(new Date(a)).getTimezoneOffset()};if(h.timezone){if(y)return function(a){return 6E4*-y.tz(a,h.timezone).utcOffset()};a.error(25)}return this.useUTC&&h.getTimezoneOffset?function(a){return 6E4*h.getTimezoneOffset(a)}:function(){return 6E4*(n.timezoneOffset||0)}},dateFormat:function(n,h,y){if(!a.defined(h)||isNaN(h))return a.defaultOptions.lang.invalidDate||"";n=a.pick(n,"%Y-%m-%d %H:%M:%S");var A=this,c=new this.Date(h),e=this.get("Hours",c),b=this.get("Day",c),l=this.get("Date",c),m=this.get("Month",
c),t=this.get("FullYear",c),f=a.defaultOptions.lang,p=f.weekdays,d=f.shortWeekdays,g=a.pad,c=a.extend({a:d?d[b]:p[b].substr(0,3),A:p[b],d:g(l),e:g(l,2," "),w:b,b:f.shortMonths[m],B:f.months[m],m:g(m+1),y:t.toString().substr(2,2),Y:t,H:g(e),k:e,I:g(e%12||12),l:e%12||12,M:g(A.get("Minutes",c)),p:12>e?"AM":"PM",P:12>e?"am":"pm",S:g(c.getSeconds()),L:g(Math.round(h%1E3),3)},a.dateFormats);a.objectEach(c,function(r,d){for(;-1!==n.indexOf("%"+d);)n=n.replace("%"+d,"function"===typeof r?r.call(A,h):r)});
return y?n.substr(0,1).toUpperCase()+n.substr(1):n},getTimeTicks:function(a,h,y,A){var c=this,e=[],b={},l,m=new c.Date(h),t=a.unitRange,f=a.count||1,p;if(E(h)){c.set("Milliseconds",m,t>=w.second?0:f*Math.floor(c.get("Milliseconds",m)/f));t>=w.second&&c.set("Seconds",m,t>=w.minute?0:f*Math.floor(c.get("Seconds",m)/f));t>=w.minute&&c.set("Minutes",m,t>=w.hour?0:f*Math.floor(c.get("Minutes",m)/f));t>=w.hour&&c.set("Hours",m,t>=w.day?0:f*Math.floor(c.get("Hours",m)/f));t>=w.day&&c.set("Date",m,t>=w.month?
1:f*Math.floor(c.get("Date",m)/f));t>=w.month&&(c.set("Month",m,t>=w.year?0:f*Math.floor(c.get("Month",m)/f)),l=c.get("FullYear",m));t>=w.year&&c.set("FullYear",m,l-l%f);t===w.week&&c.set("Date",m,c.get("Date",m)-c.get("Day",m)+C(A,1));l=c.get("FullYear",m);A=c.get("Month",m);var d=c.get("Date",m),g=c.get("Hours",m);h=m.getTime();c.variableTimezone&&(p=y-h>4*w.month||c.getTimezoneOffset(h)!==c.getTimezoneOffset(y));m=m.getTime();for(h=1;m<y;)e.push(m),m=t===w.year?c.makeTime(l+h*f,0):t===w.month?
c.makeTime(l,A+h*f):!p||t!==w.day&&t!==w.week?p&&t===w.hour&&1<f?c.makeTime(l,A,d,g+h*f):m+t*f:c.makeTime(l,A,d+h*f*(t===w.day?1:7)),h++;e.push(m);t<=w.hour&&1E4>e.length&&x(e,function(r){0===r%18E5&&"000000000"===c.dateFormat("%H%M%S%L",r)&&(b[r]="day")})}e.info=B(a,{higherRanks:b,totalRange:t*f});return e}}})(I);(function(a){var E=a.addEvent,x=a.Axis,B=a.Chart,u=a.css,C=a.defined,w=a.each,H=a.extend,n=a.noop,h=a.pick,y=a.timeUnits,A=a.wrap;A(a.Series.prototype,"init",function(c){var a;c.apply(this,
Array.prototype.slice.call(arguments,1));(a=this.xAxis)&&a.options.ordinal&&E(this,"updatedData",function(){delete a.ordinalIndex})});A(x.prototype,"getTimeTicks",function(a,e,b,l,m,t,f,p){var d=0,g,r,z={},q,c,k,v=[],G=-Number.MAX_VALUE,N=this.options.tickPixelInterval,F=this.chart.time;if(!this.options.ordinal&&!this.options.breaks||!t||3>t.length||void 0===b)return a.call(this,e,b,l,m);c=t.length;for(g=0;g<c;g++){k=g&&t[g-1]>l;t[g]<b&&(d=g);if(g===c-1||t[g+1]-t[g]>5*f||k){if(t[g]>G){for(r=a.call(this,
e,t[d],t[g],m);r.length&&r[0]<=G;)r.shift();r.length&&(G=r[r.length-1]);v=v.concat(r)}d=g+1}if(k)break}a=r.info;if(p&&a.unitRange<=y.hour){g=v.length-1;for(d=1;d<g;d++)F.dateFormat("%d",v[d])!==F.dateFormat("%d",v[d-1])&&(z[v[d]]="day",q=!0);q&&(z[v[0]]="day");a.higherRanks=z}v.info=a;if(p&&C(N)){p=F=v.length;g=[];var K;for(q=[];p--;)d=this.translate(v[p]),K&&(q[p]=K-d),g[p]=K=d;q.sort();q=q[Math.floor(q.length/2)];q<.6*N&&(q=null);p=v[F-1]>l?F-1:F;for(K=void 0;p--;)d=g[p],l=Math.abs(K-d),K&&l<.8*
N&&(null===q||l<.8*q)?(z[v[p]]&&!z[v[p+1]]?(l=p+1,K=d):l=p,v.splice(l,1)):K=d}return v});H(x.prototype,{beforeSetTickPositions:function(){var a,e=[],b=!1,l,m=this.getExtremes(),t=m.min,f=m.max,p,d=this.isXAxis&&!!this.options.breaks,m=this.options.ordinal,g=Number.MAX_VALUE,r=this.chart.options.chart.ignoreHiddenSeries;l="highcharts-navigator-xaxis"===this.options.className;!this.options.overscroll||this.max!==this.dataMax||this.chart.mouseIsDown&&!l||this.eventArgs&&(!this.eventArgs||"navigator"===
this.eventArgs.trigger)||(this.max+=this.options.overscroll,!l&&C(this.userMin)&&(this.min+=this.options.overscroll));if(m||d){w(this.series,function(f,q){if(!(r&&!1===f.visible||!1===f.takeOrdinalPosition&&!d)&&(e=e.concat(f.processedXData),a=e.length,e.sort(function(r,k){return r-k}),g=Math.min(g,h(f.closestPointRange,g)),a))for(q=a-1;q--;)e[q]===e[q+1]&&e.splice(q,1)});a=e.length;if(2<a){l=e[1]-e[0];for(p=a-1;p--&&!b;)e[p+1]-e[p]!==l&&(b=!0);!this.options.keepOrdinalPadding&&(e[0]-t>l||f-e[e.length-
1]>l)&&(b=!0)}else this.options.overscroll&&(2===a?g=e[1]-e[0]:1===a?(g=this.options.overscroll,e=[e[0],e[0]+g]):g=this.overscrollPointsRange);b?(this.options.overscroll&&(this.overscrollPointsRange=g,e=e.concat(this.getOverscrollPositions())),this.ordinalPositions=e,l=this.ordinal2lin(Math.max(t,e[0]),!0),p=Math.max(this.ordinal2lin(Math.min(f,e[e.length-1]),!0),1),this.ordinalSlope=f=(f-t)/(p-l),this.ordinalOffset=t-l*f):(this.overscrollPointsRange=h(this.closestPointRange,this.overscrollPointsRange),
this.ordinalPositions=this.ordinalSlope=this.ordinalOffset=void 0)}this.isOrdinal=m&&b;this.groupIntervalFactor=null},val2lin:function(a,e){var b=this.ordinalPositions;if(b){var c=b.length,m,t;for(m=c;m--;)if(b[m]===a){t=m;break}for(m=c-1;m--;)if(a>b[m]||0===m){a=(a-b[m])/(b[m+1]-b[m]);t=m+a;break}e=e?t:this.ordinalSlope*(t||0)+this.ordinalOffset}else e=a;return e},lin2val:function(a,e){var b=this.ordinalPositions;if(b){var c=this.ordinalSlope,m=this.ordinalOffset,t=b.length-1,f;if(e)0>a?a=b[0]:a>
t?a=b[t]:(t=Math.floor(a),f=a-t);else for(;t--;)if(e=c*t+m,a>=e){c=c*(t+1)+m;f=(a-e)/(c-e);break}return void 0!==f&&void 0!==b[t]?b[t]+(f?f*(b[t+1]-b[t]):0):a}return a},getExtendedPositions:function(){var a=this,e=a.chart,b=a.series[0].currentDataGrouping,l=a.ordinalIndex,m=b?b.count+b.unitName:"raw",t=a.options.overscroll,f=a.getExtremes(),p,d;l||(l=a.ordinalIndex={});l[m]||(p={series:[],chart:e,getExtremes:function(){return{min:f.dataMin,max:f.dataMax+t}},options:{ordinal:!0},val2lin:x.prototype.val2lin,
ordinal2lin:x.prototype.ordinal2lin},w(a.series,function(f){d={xAxis:p,xData:f.xData.slice(),chart:e,destroyGroupedData:n};d.xData=d.xData.concat(a.getOverscrollPositions());d.options={dataGrouping:b?{enabled:!0,forced:!0,approximation:"open",units:[[b.unitName,[b.count]]]}:{enabled:!1}};f.processData.apply(d);p.series.push(d)}),a.beforeSetTickPositions.apply(p),l[m]=p.ordinalPositions);return l[m]},getOverscrollPositions:function(){var c=this.options.overscroll,e=this.overscrollPointsRange,b=[],
l=this.dataMax;if(a.defined(e))for(b.push(l);l<=this.dataMax+c;)l+=e,b.push(l);return b},getGroupIntervalFactor:function(a,e,b){var c;b=b.processedXData;var m=b.length,t=[];c=this.groupIntervalFactor;if(!c){for(c=0;c<m-1;c++)t[c]=b[c+1]-b[c];t.sort(function(f,a){return f-a});t=t[Math.floor(m/2)];a=Math.max(a,b[0]);e=Math.min(e,b[m-1]);this.groupIntervalFactor=c=m*t/(e-a)}return c},postProcessTickInterval:function(a){var c=this.ordinalSlope;return c?this.options.breaks?this.closestPointRange||a:a/
(c/this.closestPointRange):a}});x.prototype.ordinal2lin=x.prototype.val2lin;A(B.prototype,"pan",function(a,e){var b=this.xAxis[0],c=b.options.overscroll,m=e.chartX,t=!1;if(b.options.ordinal&&b.series.length){var f=this.mouseDownX,p=b.getExtremes(),d=p.dataMax,g=p.min,r=p.max,z=this.hoverPoints,q=b.closestPointRange||b.overscrollPointsRange,f=(f-m)/(b.translationSlope*(b.ordinalSlope||q)),D={ordinalPositions:b.getExtendedPositions()},q=b.lin2val,k=b.val2lin,v;D.ordinalPositions?1<Math.abs(f)&&(z&&
w(z,function(k){k.setState()}),0>f?(z=D,v=b.ordinalPositions?b:D):(z=b.ordinalPositions?b:D,v=D),D=v.ordinalPositions,d>D[D.length-1]&&D.push(d),this.fixedRange=r-g,f=b.toFixedRange(null,null,q.apply(z,[k.apply(z,[g,!0])+f,!0]),q.apply(v,[k.apply(v,[r,!0])+f,!0])),f.min>=Math.min(p.dataMin,g)&&f.max<=Math.max(d,r)+c&&b.setExtremes(f.min,f.max,!0,!1,{trigger:"pan"}),this.mouseDownX=m,u(this.container,{cursor:"move"})):t=!0}else t=!0;t&&(c&&(b.max=b.dataMax+c),a.apply(this,Array.prototype.slice.call(arguments,
1)))})})(I);(function(a){function E(){return Array.prototype.slice.call(arguments,1)}function x(a){a.apply(this);this.drawBreaks(this.xAxis,["x"]);this.drawBreaks(this.yAxis,B(this.pointArrayMap,["y"]))}var B=a.pick,u=a.wrap,C=a.each,w=a.extend,H=a.isArray,n=a.fireEvent,h=a.Axis,y=a.Series;w(h.prototype,{isInBreak:function(a,c){var e=a.repeat||Infinity,b=a.from,l=a.to-a.from;c=c>=b?(c-b)%e:e-(b-c)%e;return a.inclusive?c<=l:c<l&&0!==c},isInAnyBreak:function(a,c){var e=this.options.breaks,b=e&&e.length,
l,m,t;if(b){for(;b--;)this.isInBreak(e[b],a)&&(l=!0,m||(m=B(e[b].showPoints,this.isXAxis?!1:!0)));t=l&&c?l&&!m:l}return t}});u(h.prototype,"setTickPositions",function(a){a.apply(this,Array.prototype.slice.call(arguments,1));if(this.options.breaks){var c=this.tickPositions,e=this.tickPositions.info,b=[],l;for(l=0;l<c.length;l++)this.isInAnyBreak(c[l])||b.push(c[l]);this.tickPositions=b;this.tickPositions.info=e}});u(h.prototype,"init",function(a,c,e){var b=this;e.breaks&&e.breaks.length&&(e.ordinal=
!1);a.call(this,c,e);a=this.options.breaks;b.isBroken=H(a)&&!!a.length;b.isBroken&&(b.val2lin=function(a){var c=a,e,f;for(f=0;f<b.breakArray.length;f++)if(e=b.breakArray[f],e.to<=a)c-=e.len;else if(e.from>=a)break;else if(b.isInBreak(e,a)){c-=a-e.from;break}return c},b.lin2val=function(a){var c,e;for(e=0;e<b.breakArray.length&&!(c=b.breakArray[e],c.from>=a);e++)c.to<a?a+=c.len:b.isInBreak(c,a)&&(a+=c.len);return a},b.setExtremes=function(a,b,c,f,p){for(;this.isInAnyBreak(a);)a-=this.closestPointRange;
for(;this.isInAnyBreak(b);)b-=this.closestPointRange;h.prototype.setExtremes.call(this,a,b,c,f,p)},b.setAxisTranslation=function(a){h.prototype.setAxisTranslation.call(this,a);a=b.options.breaks;var c=[],e=[],f=0,p,d,g=b.userMin||b.min,r=b.userMax||b.max,z=B(b.pointRangePadding,0),q,D;C(a,function(k){d=k.repeat||Infinity;b.isInBreak(k,g)&&(g+=k.to%d-g%d);b.isInBreak(k,r)&&(r-=r%d-k.from%d)});C(a,function(k){q=k.from;for(d=k.repeat||Infinity;q-d>g;)q-=d;for(;q<g;)q+=d;for(D=q;D<r;D+=d)c.push({value:D,
move:"in"}),c.push({value:D+(k.to-k.from),move:"out",size:k.breakSize})});c.sort(function(k,a){return k.value===a.value?("in"===k.move?0:1)-("in"===a.move?0:1):k.value-a.value});p=0;q=g;C(c,function(k){p+="in"===k.move?1:-1;1===p&&"in"===k.move&&(q=k.value);0===p&&(e.push({from:q,to:k.value,len:k.value-q-(k.size||0)}),f+=k.value-q-(k.size||0))});b.breakArray=e;b.unitLength=r-g-f+z;n(b,"afterBreaks");b.options.staticScale?b.transA=b.options.staticScale:b.unitLength&&(b.transA*=(r-b.min+z)/b.unitLength);
z&&(b.minPixelPadding=b.transA*b.minPointOffset);b.min=g;b.max=r})});u(y.prototype,"generatePoints",function(a){a.apply(this,E(arguments));var c=this.xAxis,e=this.yAxis,b=this.points,l,m=b.length,h=this.options.connectNulls,f;if(c&&e&&(c.options.breaks||e.options.breaks))for(;m--;)l=b[m],f=null===l.y&&!1===h,f||!c.isInAnyBreak(l.x,!0)&&!e.isInAnyBreak(l.y,!0)||(b.splice(m,1),this.data[m]&&this.data[m].destroyElements())});a.Series.prototype.drawBreaks=function(a,c){var e=this,b=e.points,l,m,h,f;a&&
C(c,function(c){l=a.breakArray||[];m=a.isXAxis?a.min:B(e.options.threshold,a.min);C(b,function(d){f=B(d["stack"+c.toUpperCase()],d[c]);C(l,function(g){h=!1;if(m<g.from&&f>g.to||m>g.from&&f<g.from)h="pointBreak";else if(m<g.from&&f>g.from&&f<g.to||m>g.from&&f>g.to&&f<g.from)h="pointInBreak";h&&n(a,h,{point:d,brk:g})})})})};a.Series.prototype.gappedPath=function(){var h=this.currentDataGrouping,c=h&&h.totalRange,h=this.options.gapSize,e=this.points.slice(),b=e.length-1,l=this.yAxis;if(h&&0<b)for("value"!==
this.options.gapUnit&&(h*=this.closestPointRange),c&&c>h&&(h=c);b--;)e[b+1].x-e[b].x>h&&(c=(e[b].x+e[b+1].x)/2,e.splice(b+1,0,{isNull:!0,x:c}),this.options.stacking&&(c=l.stacks[this.stackKey][c]=new a.StackItem(l,l.options.stackLabels,!1,c,this.stack),c.total=0));return this.getGraphPath(e)};u(a.seriesTypes.column.prototype,"drawPoints",x);u(a.Series.prototype,"drawPoints",x)})(I);(function(a){var E=a.arrayMax,x=a.arrayMin,B=a.Axis,u=a.defaultPlotOptions,C=a.defined,w=a.each,H=a.extend,n=a.format,
h=a.isNumber,y=a.merge,A=a.pick,c=a.Point,e=a.Tooltip,b=a.wrap,l=a.Series.prototype,m=l.processData,t=l.generatePoints,f={approximation:"average",groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%A, %b %e, %H:%M:%S.%L","%A, %b %e, %H:%M:%S.%L","-%H:%M:%S.%L"],second:["%A, %b %e, %H:%M:%S","%A, %b %e, %H:%M:%S","-%H:%M:%S"],minute:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],hour:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],day:["%A, %b %e, %Y","%A, %b %e","-%A, %b %e, %Y"],week:["Week from %A, %b %e, %Y",
"%A, %b %e","-%A, %b %e, %Y"],month:["%B %Y","%B","-%B %Y"],year:["%Y","%Y","-%Y"]}},p={line:{},spline:{},area:{},areaspline:{},column:{approximation:"sum",groupPixelWidth:10},arearange:{approximation:"range"},areasplinerange:{approximation:"range"},columnrange:{approximation:"range",groupPixelWidth:10},candlestick:{approximation:"ohlc",groupPixelWidth:10},ohlc:{approximation:"ohlc",groupPixelWidth:5}},d=a.defaultDataGroupingUnits=[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,
10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]],g=a.approximations={sum:function(a){var r=a.length,d;if(!r&&a.hasNulls)d=null;else if(r)for(d=0;r--;)d+=a[r];return d},average:function(a){var r=a.length;a=g.sum(a);h(a)&&r&&(a/=r);return a},averages:function(){var a=[];w(arguments,function(r){a.push(g.average(r))});return void 0===a[0]?void 0:a},open:function(a){return a.length?a[0]:a.hasNulls?null:void 0},high:function(a){return a.length?
E(a):a.hasNulls?null:void 0},low:function(a){return a.length?x(a):a.hasNulls?null:void 0},close:function(a){return a.length?a[a.length-1]:a.hasNulls?null:void 0},ohlc:function(a,d,f,b){a=g.open(a);d=g.high(d);f=g.low(f);b=g.close(b);if(h(a)||h(d)||h(f)||h(b))return[a,d,f,b]},range:function(a,d){a=g.low(a);d=g.high(d);if(h(a)||h(d))return[a,d];if(null===a&&null===d)return null}};l.groupData=function(a,d,b,c){var k=this.data,v=this.options.data,r=[],q=[],F=[],z=a.length,e,D,m=!!d,l=[];c="function"===
typeof c?c:g[c]||p[this.type]&&g[p[this.type].approximation]||g[f.approximation];var n=this.pointArrayMap,t=n&&n.length,y=0;D=0;var A,u;t?w(n,function(){l.push([])}):l.push([]);A=t||1;for(u=0;u<=z&&!(a[u]>=b[0]);u++);for(u;u<=z;u++){for(;void 0!==b[y+1]&&a[u]>=b[y+1]||u===z;){e=b[y];this.dataGroupInfo={start:D,length:l[0].length};D=c.apply(this,l);void 0!==D&&(r.push(e),q.push(D),F.push(this.dataGroupInfo));D=u;for(e=0;e<A;e++)l[e].length=0,l[e].hasNulls=!1;y+=1;if(u===z)break}if(u===z)break;if(n){e=
this.cropStart+u;var C=k&&k[e]||this.pointClass.prototype.applyOptions.apply({series:this},[v[e]]),x;for(e=0;e<t;e++)x=C[n[e]],h(x)?l[e].push(x):null===x&&(l[e].hasNulls=!0)}else e=m?d[u]:null,h(e)?l[0].push(e):null===e&&(l[0].hasNulls=!0)}return[r,q,F]};l.processData=function(){var a=this.chart,f=this.options.dataGrouping,b=!1!==this.allowDG&&f&&A(f.enabled,a.options.isStock),g=this.visible||!a.options.chart.ignoreHiddenSeries,k,v=this.currentDataGrouping,c;this.forceCrop=b;this.groupPixelWidth=
null;this.hasProcessed=!0;if(!1!==m.apply(this,arguments)&&b){this.destroyGroupedData();var e=this.processedXData,F=this.processedYData,p=a.plotSizeX,a=this.xAxis,S=a.options.ordinal,h=this.groupPixelWidth=a.getGroupPixelWidth&&a.getGroupPixelWidth();if(h){this.isDirty=k=!0;this.points=null;b=a.getExtremes();c=b.min;b=b.max;S=S&&a.getGroupIntervalFactor(c,b,this)||1;h=h*(b-c)/p*S;p=a.getTimeTicks(a.normalizeTimeTickInterval(h,f.units||d),Math.min(c,e[0]),Math.max(b,e[e.length-1]),a.options.startOfWeek,
e,this.closestPointRange);e=l.groupData.apply(this,[e,F,p,f.approximation]);F=e[0];S=e[1];if(f.smoothed&&F.length){f=F.length-1;for(F[f]=Math.min(F[f],b);f--&&0<f;)F[f]+=h/2;F[0]=Math.max(F[0],c)}c=p.info;this.closestPointRange=p.info.totalRange;this.groupMap=e[2];C(F[0])&&F[0]<a.dataMin&&g&&(a.min===a.dataMin&&(a.min=F[0]),a.dataMin=F[0]);this.processedXData=F;this.processedYData=S}else this.groupMap=null;this.hasGroupedData=k;this.currentDataGrouping=c;this.preventGraphAnimation=(v&&v.totalRange)!==
(c&&c.totalRange)}};l.destroyGroupedData=function(){var a=this.groupedData;w(a||[],function(d,f){d&&(a[f]=d.destroy?d.destroy():null)});this.groupedData=null};l.generatePoints=function(){t.apply(this);this.destroyGroupedData();this.groupedData=this.hasGroupedData?this.points:null};b(c.prototype,"update",function(d){this.dataGroup?a.error(24):d.apply(this,[].slice.call(arguments,1))});b(e.prototype,"tooltipFooterHeaderFormatter",function(a,d,f){var b=this.chart.time,k=d.series,v=k.tooltipOptions,r=
k.options.dataGrouping,g=v.xDateFormat,c,e=k.xAxis;return e&&"datetime"===e.options.type&&r&&h(d.key)?(a=k.currentDataGrouping,r=r.dateTimeLabelFormats,a?(e=r[a.unitName],1===a.count?g=e[0]:(g=e[1],c=e[2])):!g&&r&&(g=this.getXDateFormat(d,v,e)),g=b.dateFormat(g,d.key),c&&(g+=b.dateFormat(c,d.key+a.totalRange-1)),n(v[(f?"footer":"header")+"Format"],{point:H(d.point,{key:g}),series:k},b)):a.call(this,d,f)});b(l,"destroy",function(a){this.destroyGroupedData();a.call(this)});b(l,"setOptions",function(a,
d){a=a.call(this,d);var b=this.type,g=this.chart.options.plotOptions,k=u[b].dataGrouping;p[b]&&(k||(k=y(f,p[b])),a.dataGrouping=y(k,g.series&&g.series.dataGrouping,g[b].dataGrouping,d.dataGrouping));this.chart.options.isStock&&(this.requireSorting=!0);return a});b(B.prototype,"setScale",function(a){a.call(this);w(this.series,function(a){a.hasProcessed=!1})});B.prototype.getGroupPixelWidth=function(){var a=this.series,d=a.length,f,b=0,k=!1,v;for(f=d;f--;)(v=a[f].options.dataGrouping)&&(b=Math.max(b,
v.groupPixelWidth));for(f=d;f--;)(v=a[f].options.dataGrouping)&&a[f].hasProcessed&&(d=(a[f].processedXData||a[f].data).length,a[f].groupPixelWidth||d>this.chart.plotSizeX/b||d&&v.forced)&&(k=!0);return k?b:0};B.prototype.setDataGrouping=function(a,d){var f;d=A(d,!0);a||(a={forced:!1,units:null});if(this instanceof B)for(f=this.series.length;f--;)this.series[f].update({dataGrouping:a},!1);else w(this.chart.options.series,function(d){d.dataGrouping=a},!1);d&&this.chart.redraw()}})(I);(function(a){var E=
a.each,x=a.Point,B=a.seriesType,u=a.seriesTypes;B("ohlc","column",{lineWidth:1,tooltip:{pointFormat:'\x3cspan class\x3d"highcharts-color-{point.colorIndex}"\x3e\u25cf\x3c/span\x3e \x3cb\x3e {series.name}\x3c/b\x3e\x3cbr/\x3eOpen: {point.open}\x3cbr/\x3eHigh: {point.high}\x3cbr/\x3eLow: {point.low}\x3cbr/\x3eClose: {point.close}\x3cbr/\x3e'},threshold:null,stickyTracking:!0},{directTouch:!1,pointArrayMap:["open","high","low","close"],toYData:function(a){return[a.open,a.high,a.low,a.close]},pointValKey:"close",
translate:function(){var a=this,w=a.yAxis,x=!!a.modifyValue,n=["plotOpen","plotHigh","plotLow","plotClose","yBottom"];u.column.prototype.translate.apply(a);E(a.points,function(h){E([h.open,h.high,h.low,h.close,h.low],function(y,u){null!==y&&(x&&(y=a.modifyValue(y)),h[n[u]]=w.toPixels(y,!0))});h.tooltipPos[1]=h.plotHigh+w.pos-a.chart.plotTop})},drawPoints:function(){var a=this,w=a.chart;E(a.points,function(u){var n,h,y,A,c=u.graphic,e,b=!c;void 0!==u.plotY&&(c||(u.graphic=c=w.renderer.path().add(a.group)),
h=c.strokeWidth()%2/2,e=Math.round(u.plotX)-h,y=Math.round(u.shapeArgs.width/2),A=["M",e,Math.round(u.yBottom),"L",e,Math.round(u.plotHigh)],null!==u.open&&(n=Math.round(u.plotOpen)+h,A.push("M",e,n,"L",e-y,n)),null!==u.close&&(n=Math.round(u.plotClose)+h,A.push("M",e,n,"L",e+y,n)),c[b?"attr":"animate"]({d:A}).addClass(u.getClassName(),!0))})},animate:null},{getClassName:function(){return x.prototype.getClassName.call(this)+(this.open<this.close?" highcharts-point-up":" highcharts-point-down")}})})(I);
(function(a){var E=a.defaultPlotOptions,x=a.each,B=a.merge;a=a.seriesType;a("candlestick","ohlc",B(E.column,{states:{hover:{lineWidth:2}},tooltip:E.ohlc.tooltip,threshold:null,stickyTracking:!0}),{drawPoints:function(){var a=this,B=a.chart;x(a.points,function(u){var w=u.graphic,n,h,y,A,c,e,b,l=!w;void 0!==u.plotY&&(w||(u.graphic=w=B.renderer.path().add(a.group)),c=w.strokeWidth()%2/2,e=Math.round(u.plotX)-c,n=u.plotOpen,h=u.plotClose,y=Math.min(n,h),n=Math.max(n,h),b=Math.round(u.shapeArgs.width/
2),h=Math.round(y)!==Math.round(u.plotHigh),A=n!==u.yBottom,y=Math.round(y)+c,n=Math.round(n)+c,c=[],c.push("M",e-b,n,"L",e-b,y,"L",e+b,y,"L",e+b,n,"Z","M",e,y,"L",e,h?Math.round(u.plotHigh):y,"M",e,n,"L",e,A?Math.round(u.yBottom):n),w[l?"attr":"animate"]({d:c}).addClass(u.getClassName(),!0))})}})})(I);var X=function(a){var E=a.each,x=a.seriesTypes,B=a.stableSort;return{getPlotBox:function(){return a.Series.prototype.getPlotBox.call(this.options.onSeries&&this.chart.get(this.options.onSeries)||this)},
translate:function(){x.column.prototype.translate.apply(this);var a=this.options,C=this.chart,w=this.points,H=w.length-1,n,h,y=a.onSeries;n=y&&C.get(y);var a=a.onKey||"y",y=n&&n.options.step,A=n&&n.points,c=A&&A.length,e=this.xAxis,b=this.yAxis,l=0,m,t,f,p;if(n&&n.visible&&c)for(l=(n.pointXOffset||0)+(n.barW||0)/2,n=n.currentDataGrouping,t=A[c-1].x+(n?n.totalRange:0),B(w,function(a,f){return a.x-f.x}),a="plot"+a[0].toUpperCase()+a.substr(1);c--&&w[H]&&!(m=A[c],n=w[H],n.y=m.y,m.x<=n.x&&void 0!==m[a]&&
(n.x<=t&&(n.plotY=m[a],m.x<n.x&&!y&&(f=A[c+1])&&void 0!==f[a]&&(p=(n.x-m.x)/(f.x-m.x),n.plotY+=p*(f[a]-m[a]),n.y+=p*(f.y-m.y))),H--,c++,0>H)););E(w,function(a,f){var d;a.plotX+=l;void 0===a.plotY&&(0<=a.plotX&&a.plotX<=e.len?a.plotY=C.chartHeight-e.bottom-(e.opposite?e.height:0)+e.offset-b.top:a.shapeArgs={});(h=w[f-1])&&h.plotX===a.plotX&&(void 0===h.stackIndex&&(h.stackIndex=0),d=h.stackIndex+1);a.stackIndex=d})}}}(I);(function(a,E){function x(a){n[a+"pin"]=function(h,u,c,e,b){var l=b&&b.anchorX;
b=b&&b.anchorY;"circle"===a&&e>c&&(h-=Math.round((e-c)/2),c=e);h=n[a](h,u,c,e);l&&b&&(h.push("M","circle"===a?h[1]-h[4]:h[1]+h[4]/2,u>b?u:u+e,"L",l,b),h=h.concat(n.circle(l-1,b-1,2,2)));return h}}var B=a.addEvent,u=a.each,C=a.noop,w=a.seriesType,H=a.TrackerMixin,n=a.SVGRenderer.prototype.symbols;w("flags","column",{pointRange:0,allowOverlapX:!1,shape:"flag",stackDistance:12,textAlign:"center",tooltip:{pointFormat:"{point.text}\x3cbr/\x3e"},threshold:null,y:-30},{sorted:!1,noSharedTooltip:!0,allowDG:!1,
takeOrdinalPosition:!1,trackerGroups:["markerGroup"],forceCrop:!0,init:a.Series.prototype.init,translate:E.translate,getPlotBox:E.getPlotBox,drawPoints:function(){var h=this.points,n=this.chart,w=n.renderer,c,e,b=this.options,l=b.y,m,t,f,p,d,g,r=this.yAxis,z={},q=[];for(t=h.length;t--;)f=h[t],g=f.plotX>this.xAxis.len,c=f.plotX,p=f.stackIndex,m=f.options.shape||b.shape,e=f.plotY,void 0!==e&&(e=f.plotY+l-(void 0!==p&&p*b.stackDistance)),f.anchorX=p?void 0:f.plotX,d=p?void 0:f.plotY,p=f.graphic,void 0!==
e&&0<=c&&!g?(p||(p=f.graphic=w.label("",null,null,m,null,null,b.useHTML).attr({align:"flag"===m?"left":"center",width:b.width,height:b.height,"text-align":b.textAlign}).addClass("highcharts-point").add(this.markerGroup),f.graphic.div&&(f.graphic.div.point=f),p.isNew=!0),0<c&&(c-=p.strokeWidth()%2),m={y:e,anchorY:d},b.allowOverlapX&&(m.x=c,m.anchorX=f.anchorX),p.attr({text:f.options.title||b.title||"A"})[p.isNew?"attr":"animate"](m),b.allowOverlapX||(z[f.plotX]?z[f.plotX].size=Math.max(z[f.plotX].size,
p.width):z[f.plotX]={align:0,size:p.width,target:c,anchorX:c}),f.tooltipPos=n.inverted?[r.len+r.pos-n.plotLeft-e,this.xAxis.len-c]:[c,e+r.pos-n.plotTop]):p&&(f.graphic=p.destroy());b.allowOverlapX||(a.objectEach(z,function(a){a.plotX=a.anchorX;q.push(a)}),a.distribute(q,this.xAxis.len),u(h,function(a){var k=a.graphic&&z[a.plotX];k&&(a.graphic[a.graphic.isNew?"attr":"animate"]({x:k.pos,anchorX:a.anchorX}),a.graphic.isNew=!1)}));b.useHTML&&a.wrap(this.markerGroup,"on",function(f){return a.SVGElement.prototype.on.apply(f.apply(this,
[].slice.call(arguments,1)),[].slice.call(arguments,1))})},drawTracker:function(){var a=this.points;H.drawTrackerPoint.apply(this);u(a,function(h){var n=h.graphic;n&&B(n.element,"mouseover",function(){0<h.stackIndex&&!h.raised&&(h._y=n.y,n.attr({y:h._y-8}),h.raised=!0);u(a,function(a){a!==h&&a.raised&&a.graphic&&(a.graphic.attr({y:a._y}),a.raised=!1)})})})},animate:C,buildKDTree:C,setClip:C});n.flag=function(a,u,w,c,e){var b=e&&e.anchorX||a;e=e&&e.anchorY||u;return n.circle(b-1,e-1,2,2).concat(["M",
b,e,"L",a,u+c,a,u,a+w,u,a+w,u+c,a,u+c,"Z"])};x("circle");x("square")})(I,X);(function(a){function E(a,b,d){this.init(a,b,d)}var x=a.addEvent,B=a.Axis,u=a.correctFloat,C=a.defaultOptions,w=a.defined,H=a.destroyObjectProperties,n=a.each,h=a.fireEvent,y=a.hasTouch,A=a.isTouchDevice,c=a.merge,e=a.pick,b=a.removeEvent,l=a.wrap,m,t={height:A?20:14,barBorderRadius:0,buttonBorderRadius:0,liveRedraw:a.svg&&!A,margin:10,minWidth:6,step:.2,zIndex:3};C.scrollbar=c(!0,t,C.scrollbar);a.swapXY=m=function(a,b){var d=
a.length,f;if(b)for(b=0;b<d;b+=3)f=a[b+1],a[b+1]=a[b+2],a[b+2]=f;return a};E.prototype={init:function(a,b,d){this.scrollbarButtons=[];this.renderer=a;this.userOptions=b;this.options=c(t,b);this.chart=d;this.size=e(this.options.size,this.options.height);b.enabled&&(this.render(),this.initEvents(),this.addEvents())},render:function(){var a=this.renderer,b=this.options,d=this.size,g;this.group=g=a.g("scrollbar").attr({zIndex:b.zIndex,translateY:-99999}).add();this.track=a.rect().addClass("highcharts-scrollbar-track").attr({x:0,
r:b.trackBorderRadius||0,height:d,width:d}).add(g);this.trackBorderWidth=this.track.strokeWidth();this.track.attr({y:-this.trackBorderWidth%2/2});this.scrollbarGroup=a.g().add(g);this.scrollbar=a.rect().addClass("highcharts-scrollbar-thumb").attr({height:d,width:d,r:b.barBorderRadius||0}).add(this.scrollbarGroup);this.scrollbarRifles=a.path(m(["M",-3,d/4,"L",-3,2*d/3,"M",0,d/4,"L",0,2*d/3,"M",3,d/4,"L",3,2*d/3],b.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup);this.scrollbarStrokeWidth=
this.scrollbar.strokeWidth();this.scrollbarGroup.translate(-this.scrollbarStrokeWidth%2/2,-this.scrollbarStrokeWidth%2/2);this.drawScrollbarButton(0);this.drawScrollbarButton(1)},position:function(a,b,d,g){var f=this.options.vertical,e=0,c=this.rendered?"animate":"attr";this.x=a;this.y=b+this.trackBorderWidth;this.width=d;this.xOffset=this.height=g;this.yOffset=e;f?(this.width=this.yOffset=d=e=this.size,this.xOffset=b=0,this.barWidth=g-2*d,this.x=a+=this.options.margin):(this.height=this.xOffset=
g=b=this.size,this.barWidth=d-2*g,this.y+=this.options.margin);this.group[c]({translateX:a,translateY:this.y});this.track[c]({width:d,height:g});this.scrollbarButtons[1][c]({translateX:f?0:d-b,translateY:f?g-e:0})},drawScrollbarButton:function(a){var f=this.renderer,d=this.scrollbarButtons,b=this.options,e=this.size,c;c=f.g().add(this.group);d.push(c);c=f.rect().addClass("highcharts-scrollbar-button").add(c);c.attr(c.crisp({x:-.5,y:-.5,width:e+1,height:e+1,r:b.buttonBorderRadius},c.strokeWidth()));
f.path(m(["M",e/2+(a?-1:1),e/2-3,"L",e/2+(a?-1:1),e/2+3,"L",e/2+(a?2:-2),e/2],b.vertical)).addClass("highcharts-scrollbar-arrow").add(d[a])},setRange:function(a,b){var d=this.options,f=d.vertical,e=d.minWidth,c=this.barWidth,q,p,k=this.rendered&&!this.hasDragged?"animate":"attr";w(c)&&(a=Math.max(a,0),q=Math.ceil(c*a),this.calculatedWidth=p=u(c*Math.min(b,1)-q),p<e&&(q=(c-e+p)*a,p=e),e=Math.floor(q+this.xOffset+this.yOffset),c=p/2-.5,this.from=a,this.to=b,f?(this.scrollbarGroup[k]({translateY:e}),
this.scrollbar[k]({height:p}),this.scrollbarRifles[k]({translateY:c}),this.scrollbarTop=e,this.scrollbarLeft=0):(this.scrollbarGroup[k]({translateX:e}),this.scrollbar[k]({width:p}),this.scrollbarRifles[k]({translateX:c}),this.scrollbarLeft=e,this.scrollbarTop=0),12>=p?this.scrollbarRifles.hide():this.scrollbarRifles.show(!0),!1===d.showFull&&(0>=a&&1<=b?this.group.hide():this.group.show()),this.rendered=!0)},initEvents:function(){var a=this;a.mouseMoveHandler=function(b){var d=a.chart.pointer.normalize(b),
e=a.options.vertical?"chartY":"chartX",c=a.initPositions;!a.grabbedCenter||b.touches&&0===b.touches[0][e]||(d=a.cursorToScrollbarPosition(d)[e],e=a[e],e=d-e,a.hasDragged=!0,a.updatePosition(c[0]+e,c[1]+e),a.hasDragged&&h(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMType:b.type,DOMEvent:b}))};a.mouseUpHandler=function(b){a.hasDragged&&h(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMType:b.type,DOMEvent:b});a.grabbedCenter=a.hasDragged=a.chartX=a.chartY=null};a.mouseDownHandler=
function(b){b=a.chart.pointer.normalize(b);b=a.cursorToScrollbarPosition(b);a.chartX=b.chartX;a.chartY=b.chartY;a.initPositions=[a.from,a.to];a.grabbedCenter=!0};a.buttonToMinClick=function(b){var d=u(a.to-a.from)*a.options.step;a.updatePosition(u(a.from-d),u(a.to-d));h(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMEvent:b})};a.buttonToMaxClick=function(b){var d=(a.to-a.from)*a.options.step;a.updatePosition(a.from+d,a.to+d);h(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMEvent:b})};
a.trackClick=function(b){var d=a.chart.pointer.normalize(b),e=a.to-a.from,c=a.y+a.scrollbarTop,f=a.x+a.scrollbarLeft;a.options.vertical&&d.chartY>c||!a.options.vertical&&d.chartX>f?a.updatePosition(a.from+e,a.to+e):a.updatePosition(a.from-e,a.to-e);h(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMEvent:b})}},cursorToScrollbarPosition:function(a){var b=this.options,b=b.minWidth>this.calculatedWidth?b.minWidth:0;return{chartX:(a.chartX-this.x-this.xOffset)/(this.barWidth-b),chartY:(a.chartY-
this.y-this.yOffset)/(this.barWidth-b)}},updatePosition:function(a,b){1<b&&(a=u(1-u(b-a)),b=1);0>a&&(b=u(b-a),a=0);this.from=a;this.to=b},update:function(a){this.destroy();this.init(this.chart.renderer,c(!0,this.options,a),this.chart)},addEvents:function(){var a=this.options.inverted?[1,0]:[0,1],b=this.scrollbarButtons,d=this.scrollbarGroup.element,e=this.mouseDownHandler,c=this.mouseMoveHandler,z=this.mouseUpHandler,a=[[b[a[0]].element,"click",this.buttonToMinClick],[b[a[1]].element,"click",this.buttonToMaxClick],
[this.track.element,"click",this.trackClick],[d,"mousedown",e],[d.ownerDocument,"mousemove",c],[d.ownerDocument,"mouseup",z]];y&&a.push([d,"touchstart",e],[d.ownerDocument,"touchmove",c],[d.ownerDocument,"touchend",z]);n(a,function(a){x.apply(null,a)});this._events=a},removeEvents:function(){n(this._events,function(a){b.apply(null,a)});this._events.length=0},destroy:function(){var a=this.chart.scroller;this.removeEvents();n(["track","scrollbarRifles","scrollbar","scrollbarGroup","group"],function(a){this[a]&&
this[a].destroy&&(this[a]=this[a].destroy())},this);a&&this===a.scrollbar&&(a.scrollbar=null,H(a.scrollbarButtons))}};l(B.prototype,"init",function(a){var b=this;a.apply(b,Array.prototype.slice.call(arguments,1));b.options.scrollbar&&b.options.scrollbar.enabled&&(b.options.scrollbar.vertical=!b.horiz,b.options.startOnTick=b.options.endOnTick=!1,b.scrollbar=new E(b.chart.renderer,b.options.scrollbar,b.chart),x(b.scrollbar,"changed",function(a){var d=Math.min(e(b.options.min,b.min),b.min,b.dataMin),
c=Math.max(e(b.options.max,b.max),b.max,b.dataMax)-d,f;b.horiz&&!b.reversed||!b.horiz&&b.reversed?(f=d+c*this.to,d+=c*this.from):(f=d+c*(1-this.from),d+=c*(1-this.to));b.setExtremes(d,f,!0,!1,a)}))});l(B.prototype,"render",function(a){var b=Math.min(e(this.options.min,this.min),this.min,e(this.dataMin,this.min)),d=Math.max(e(this.options.max,this.max),this.max,e(this.dataMax,this.max)),c=this.scrollbar,f=this.titleOffset||0;a.apply(this,Array.prototype.slice.call(arguments,1));if(c){this.horiz?(c.position(this.left,
this.top+this.height+2+this.chart.scrollbarsOffsets[1]+(this.opposite?0:f+this.axisTitleMargin+this.offset),this.width,this.height),f=1):(c.position(this.left+this.width+2+this.chart.scrollbarsOffsets[0]+(this.opposite?f+this.axisTitleMargin+this.offset:0),this.top,this.width,this.height),f=0);if(!this.opposite&&!this.horiz||this.opposite&&this.horiz)this.chart.scrollbarsOffsets[f]+=this.scrollbar.size+this.scrollbar.options.margin;isNaN(b)||isNaN(d)||!w(this.min)||!w(this.max)?c.setRange(0,0):(f=
(this.min-b)/(d-b),b=(this.max-b)/(d-b),this.horiz&&!this.reversed||!this.horiz&&this.reversed?c.setRange(f,b):c.setRange(1-b,1-f))}});l(B.prototype,"getOffset",function(a){var b=this.horiz?2:1,d=this.scrollbar;a.apply(this,Array.prototype.slice.call(arguments,1));d&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[b]+=d.size+d.options.margin)});l(B.prototype,"destroy",function(a){this.scrollbar&&(this.scrollbar=this.scrollbar.destroy());a.apply(this,Array.prototype.slice.call(arguments,
1))});a.Scrollbar=E})(I);(function(a){function E(a){this.init(a)}var x=a.addEvent,B=a.Axis,u=a.Chart,C=a.defaultOptions,w=a.defined,H=a.destroyObjectProperties,n=a.each,h=a.erase,y=a.error,A=a.extend,c=a.grep,e=a.hasTouch,b=a.isArray,l=a.isNumber,m=a.isObject,t=a.merge,f=a.pick,p=a.removeEvent,d=a.Scrollbar,g=a.Series,r=a.seriesTypes,z=a.wrap,q=[].concat(a.defaultDataGroupingUnits),D=function(a){var b=c(arguments,l);if(b.length)return Math[a].apply(0,b)};q[4]=["day",[1,2,3,4]];q[5]=["week",[1,2,3]];
A(C,{navigator:{height:40,margin:25,maskInside:!0,handles:{width:7,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0},series:{type:void 0===r.areaspline?"line":"areaspline",compare:null,dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,smoothed:!0,units:q},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},pointRange:0,threshold:null},xAxis:{overscroll:0,className:"highcharts-navigator-xaxis",
tickLength:0,tickPixelInterval:200,labels:{align:"left",x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",startOnTick:!1,endOnTick:!1,minPadding:.1,maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:null},tickLength:0,tickWidth:0}}});a.Renderer.prototype.symbols["navigator-handle"]=function(a,b,d,e,c){a=c.width/2;b=Math.round(a/3)+.5;c=c.height;return["M",-a-1,.5,"L",a,.5,"L",a,c+.5,"L",-a-1,c+.5,"L",-a-1,.5,"M",-b,4,"L",-b,c-3,"M",b-1,4,"L",b-1,c-3]};E.prototype={drawHandle:function(a,
b,d,c){var k=this.navigatorOptions.handles.height;this.handles[b][c](d?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(a,10)+.5-k)}:{translateX:Math.round(this.left+parseInt(a,10)),translateY:Math.round(this.top+this.height/2-k/2-1)})},drawOutline:function(a,b,d,c){var k=this.navigatorOptions.maskInside,e=this.outline.strokeWidth(),v=e/2,e=e%2/2,f=this.outlineHeight,g=this.scrollbarHeight,G=this.size,q=this.left-g,r=this.top;d?(q-=v,d=r+b+e,b=r+a+e,a=["M",q+
f,r-g-e,"L",q+f,d,"L",q,d,"L",q,b,"L",q+f,b,"L",q+f,r+G+g].concat(k?["M",q+f,d-v,"L",q+f,b+v]:[])):(a+=q+g-e,b+=q+g-e,r+=v,a=["M",q,r,"L",a,r,"L",a,r+f,"L",b,r+f,"L",b,r,"L",q+G+2*g,r].concat(k?["M",a-v,r,"L",b+v,r]:[]));this.outline[c]({d:a})},drawMasks:function(a,b,d,e){var k=this.left,c=this.top,v=this.height,f,g,q,G;d?(q=[k,k,k],G=[c,c+a,c+b],g=[v,v,v],f=[a,b-a,this.size-b]):(q=[k,k+a,k+b],G=[c,c,c],g=[a,b-a,this.size-b],f=[v,v,v]);n(this.shades,function(a,b){a[e]({x:q[b],y:G[b],width:g[b],height:f[b]})})},
renderElements:function(){var a=this,b=a.navigatorOptions,d=b.maskInside,c=a.chart,e=c.renderer,f;a.navigatorGroup=f=e.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();n([!d,d,!d],function(b,d){a.shades[d]=e.rect().addClass("highcharts-navigator-mask"+(1===d?"-inside":"-outside")).add(f)});a.outline=e.path().addClass("highcharts-navigator-outline").add(f);b.handles.enabled&&n([0,1],function(d){b.handles.inverted=c.inverted;a.handles[d]=e.symbol(b.handles.symbols[d],-b.handles.width/2-1,0,
b.handles.width,b.handles.height,b.handles);a.handles[d].attr({zIndex:7-d}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][d]).add(f)})},update:function(a){n(this.series||[],function(a){a.baseSeries&&delete a.baseSeries.navigatorSeries});this.destroy();t(!0,this.chart.options.navigator,this.options,a);this.init(this.chart)},render:function(b,d,c,e){var k=this.chart,v,g,q=this.scrollbarHeight,r,G=this.xAxis;v=G.fake?k.xAxis[0]:G;var D=this.navigatorEnabled,z,h=
this.rendered;g=k.inverted;var m,N=k.xAxis[0].minRange,n=k.xAxis[0].options.maxRange;if(!this.hasDragged||w(c)){if(!l(b)||!l(d))if(h)c=0,e=f(G.width,v.width);else return;this.left=f(G.left,k.plotLeft+q+(g?k.plotWidth:0));this.size=z=r=f(G.len,(g?k.plotHeight:k.plotWidth)-2*q);k=g?q:r+2*q;c=f(c,G.toPixels(b,!0));e=f(e,G.toPixels(d,!0));l(c)&&Infinity!==Math.abs(c)||(c=0,e=k);b=G.toValue(c,!0);d=G.toValue(e,!0);m=Math.abs(a.correctFloat(d-b));m<N?this.grabbedLeft?c=G.toPixels(d-N,!0):this.grabbedRight&&
(e=G.toPixels(b+N,!0)):w(n)&&m>n&&(this.grabbedLeft?c=G.toPixels(d-n,!0):this.grabbedRight&&(e=G.toPixels(b+n,!0)));this.zoomedMax=Math.min(Math.max(c,e,0),z);this.zoomedMin=Math.min(Math.max(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(c,e),0),z);this.range=this.zoomedMax-this.zoomedMin;z=Math.round(this.zoomedMax);c=Math.round(this.zoomedMin);D&&(this.navigatorGroup.attr({visibility:"visible"}),h=h&&!this.hasDragged?"animate":"attr",this.drawMasks(c,z,g,h),this.drawOutline(c,z,g,h),this.navigatorOptions.handles.enabled&&
(this.drawHandle(c,0,g,h),this.drawHandle(z,1,g,h)));this.scrollbar&&(g?(g=this.top-q,v=this.left-q+(D||!v.opposite?0:(v.titleOffset||0)+v.axisTitleMargin),q=r+2*q):(g=this.top+(D?this.height:-q),v=this.left-q),this.scrollbar.position(v,g,k,q),this.scrollbar.setRange(this.zoomedMin/r,this.zoomedMax/r));this.rendered=!0}},addMouseEvents:function(){var a=this,b=a.chart,d=b.container,c=[],g,f;a.mouseMoveHandler=g=function(b){a.onMouseMove(b)};a.mouseUpHandler=f=function(b){a.onMouseUp(b)};c=a.getPartsEvents("mousedown");
c.push(x(d,"mousemove",g),x(d.ownerDocument,"mouseup",f));e&&(c.push(x(d,"touchmove",g),x(d.ownerDocument,"touchend",f)),c.concat(a.getPartsEvents("touchstart")));a.eventsToUnbind=c;a.series&&a.series[0]&&c.push(x(a.series[0].xAxis,"foundExtremes",function(){b.navigator.modifyNavigatorAxisExtremes()}))},getPartsEvents:function(a){var b=this,d=[];n(["shades","handles"],function(k){n(b[k],function(c,e){d.push(x(c.element,a,function(a){b[k+"Mousedown"](a,e)}))})});return d},shadesMousedown:function(a,
b){a=this.chart.pointer.normalize(a);var d=this.chart,c=this.xAxis,k=this.zoomedMin,e=this.left,g=this.size,f=this.range,v=a.chartX,q,r;d.inverted&&(v=a.chartY,e=this.top);1===b?(this.grabbedCenter=v,this.fixedWidth=f,this.dragOffset=v-k):(a=v-e-f/2,0===b?a=Math.max(0,a):2===b&&a+f>=g&&(a=g-f,c.reversed?(a-=f,r=this.getUnionExtremes().dataMin):q=this.getUnionExtremes().dataMax),a!==k&&(this.fixedWidth=f,b=c.toFixedRange(a,a+f,r,q),w(b.min)&&d.xAxis[0].setExtremes(Math.min(b.min,b.max),Math.max(b.min,
b.max),!0,null,{trigger:"navigator"})))},handlesMousedown:function(a,b){this.chart.pointer.normalize(a);a=this.chart;var d=a.xAxis[0],c=a.inverted&&!d.reversed||!a.inverted&&d.reversed;0===b?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=c?d.min:d.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=c?d.max:d.min);a.fixedRange=null},onMouseMove:function(a){var b=this,d=b.chart,c=b.left,e=b.navigatorSize,k=b.range,f=b.dragOffset,g=d.inverted;a.touches&&
0===a.touches[0].pageX||(a=d.pointer.normalize(a),d=a.chartX,g&&(c=b.top,d=a.chartY),b.grabbedLeft?(b.hasDragged=!0,b.render(0,0,d-c,b.otherHandlePos)):b.grabbedRight?(b.hasDragged=!0,b.render(0,0,b.otherHandlePos,d-c)):b.grabbedCenter&&(b.hasDragged=!0,d<f?d=f:d>e+f-k&&(d=e+f-k),b.render(0,0,d-f,d-f+k)),b.hasDragged&&b.scrollbar&&b.scrollbar.options.liveRedraw&&(a.DOMType=a.type,setTimeout(function(){b.onMouseUp(a)},0)))},onMouseUp:function(a){var b=this.chart,d=this.xAxis,c=d&&d.reversed,e=this.scrollbar,
k,f,g=a.DOMEvent||a;(!this.hasDragged||e&&e.hasDragged)&&"scrollbar"!==a.trigger||(e=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?k=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(f=this.fixedExtreme),this.zoomedMax===this.size&&(f=c?e.dataMin:e.dataMax),0===this.zoomedMin&&(k=c?e.dataMax:e.dataMin),d=d.toFixedRange(this.zoomedMin,this.zoomedMax,k,f),w(d.min)&&b.xAxis[0].setExtremes(Math.min(d.min,d.max),Math.max(d.min,d.max),!0,this.hasDragged?!1:null,{trigger:"navigator",
triggerOp:"navigator-drag",DOMEvent:g}));"mousemove"!==a.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null)},removeEvents:function(){this.eventsToUnbind&&(n(this.eventsToUnbind,function(a){a()}),this.eventsToUnbind=void 0);this.removeBaseSeriesEvents()},removeBaseSeriesEvents:function(){var a=this.baseSeries||[];this.navigatorEnabled&&a[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&n(a,function(a){p(a,
"updatedData",this.updatedDataHandler)},this),a[0].xAxis&&p(a[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))},init:function(a){var b=a.options,c=b.navigator,e=c.enabled,k=b.scrollbar,g=k.enabled,b=e?c.height:0,q=g?k.height:0;this.handles=[];this.shades=[];this.chart=a;this.setBaseSeries();this.height=b;this.scrollbarHeight=q;this.scrollbarEnabled=g;this.navigatorEnabled=e;this.navigatorOptions=c;this.scrollbarOptions=k;this.outlineHeight=b+q;this.opposite=f(c.opposite,!e&&a.inverted);var r=
this,k=r.baseSeries,g=a.xAxis.length,h=a.yAxis.length,m=k&&k[0]&&k[0].xAxis||a.xAxis[0];a.extraMargin={type:r.opposite?"plotTop":"marginBottom",value:(e||!a.inverted?r.outlineHeight:0)+c.margin};a.inverted&&(a.extraMargin.type=r.opposite?"marginRight":"plotLeft");a.isDirtyBox=!0;r.navigatorEnabled?(r.xAxis=new B(a,t({breaks:m.options.breaks,ordinal:m.options.ordinal},c.xAxis,{id:"navigator-x-axis",yAxis:"navigator-y-axis",isX:!0,type:"datetime",index:g,offset:0,keepOrdinalPadding:!0,startOnTick:!1,
endOnTick:!1,minPadding:0,maxPadding:0,zoomEnabled:!1},a.inverted?{offsets:[q,0,-q,0],width:b}:{offsets:[0,-q,0,q],height:b})),r.yAxis=new B(a,t(c.yAxis,{id:"navigator-y-axis",alignTicks:!1,offset:0,index:h,zoomEnabled:!1},a.inverted?{width:b}:{height:b})),k||c.series.data?r.updateNavigatorSeries():0===a.series.length&&z(a,"redraw",function(b,d){0<a.series.length&&!r.series&&(r.setBaseSeries(),a.redraw=b);b.call(a,d)}),r.renderElements(),r.addMouseEvents()):r.xAxis={translate:function(b,d){var c=
a.xAxis[0],e=c.getExtremes(),k=c.len-2*q,f=D("min",c.options.min,e.dataMin),c=D("max",c.options.max,e.dataMax)-f;return d?b*c/k+f:k*(b-f)/c},toPixels:function(a){return this.translate(a)},toValue:function(a){return this.translate(a,!0)},toFixedRange:B.prototype.toFixedRange,fake:!0};a.options.scrollbar.enabled&&(a.scrollbar=r.scrollbar=new d(a.renderer,t(a.options.scrollbar,{margin:r.navigatorEnabled?0:10,vertical:a.inverted}),a),x(r.scrollbar,"changed",function(b){var d=r.size,c=d*this.to,d=d*this.from;
r.hasDragged=r.scrollbar.hasDragged;r.render(0,0,d,c);(a.options.scrollbar.liveRedraw||"mousemove"!==b.DOMType&&"touchmove"!==b.DOMType)&&setTimeout(function(){r.onMouseUp(b)})}));r.addBaseSeriesEvents();r.addChartEvents()},getUnionExtremes:function(a){var b=this.chart.xAxis[0],d=this.xAxis,c=d.options,e=b.options,k;a&&null===b.dataMin||(k={dataMin:f(c&&c.min,D("min",e.min,b.dataMin,d.dataMin,d.min)),dataMax:f(c&&c.max,D("max",e.max,b.dataMax,d.dataMax,d.max))});return k},setBaseSeries:function(a,
b){var d=this.chart,c=this.baseSeries=[];a=a||d.options&&d.options.navigator.baseSeries||0;n(d.series||[],function(b,d){b.options.isInternal||!b.options.showInNavigator&&(d!==a&&b.options.id!==a||!1===b.options.showInNavigator)||c.push(b)});this.xAxis&&!this.xAxis.fake&&this.updateNavigatorSeries(b)},updateNavigatorSeries:function(d){var c=this,e=c.chart,k=c.baseSeries,f,g,q=c.navigatorOptions.series,r,h={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:"navigator-x-axis",
yAxis:"navigator-y-axis",showInLegend:!1,stacking:!1,isInternal:!0,visible:!0},z=c.series=a.grep(c.series||[],function(b){var d=b.baseSeries;return 0>a.inArray(d,k)?(d&&(p(d,"updatedData",c.updatedDataHandler),delete d.navigatorSeries),b.destroy(),!1):!0});k&&k.length&&n(k,function(a){var v=a.navigatorSeries,D=A({color:a.color},b(q)?C.navigator.series:q);v&&!1===c.navigatorOptions.adaptToUpdatedData||(h.name="Navigator "+k.length,f=a.options||{},r=f.navigatorOptions||{},g=t(f,h,D,r),D=r.data||D.data,
c.hasNavigatorData=c.hasNavigatorData||!!D,g.data=D||f.data&&f.data.slice(0),v&&v.options?v.update(g,d):(a.navigatorSeries=e.initSeries(g),a.navigatorSeries.baseSeries=a,z.push(a.navigatorSeries)))});if(q.data&&(!k||!k.length)||b(q))c.hasNavigatorData=!1,q=a.splat(q),n(q,function(a,b){h.name="Navigator "+(z.length+1);g=t(C.navigator.series,{color:e.series[b]&&!e.series[b].options.isInternal&&e.series[b].color||e.options.colors[b]||e.options.colors[0]},h,a);g.data=a.data;g.data&&(c.hasNavigatorData=
!0,z.push(e.initSeries(g)))});this.addBaseSeriesEvents()},addBaseSeriesEvents:function(){var a=this,b=a.baseSeries||[];b[0]&&b[0].xAxis&&x(b[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes);n(b,function(b){x(b,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)});x(b,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)});!1!==this.navigatorOptions.adaptToUpdatedData&&b.xAxis&&x(b,"updatedData",this.updatedDataHandler);x(b,"remove",function(){this.navigatorSeries&&
(h(a.series,this.navigatorSeries),this.navigatorSeries.remove(!1),delete this.navigatorSeries)})},this)},modifyNavigatorAxisExtremes:function(){var a=this.xAxis,b;a.getExtremes&&(!(b=this.getUnionExtremes(!0))||b.dataMin===a.min&&b.dataMax===a.max||(a.min=b.dataMin,a.max=b.dataMax))},modifyBaseAxisExtremes:function(){var a=this.chart.navigator,b=this.getExtremes(),d=b.dataMin,c=b.dataMax,b=b.max-b.min,e=a.stickToMin,f=a.stickToMax,g=this.options.overscroll,q,r,h=a.series&&a.series[0],z=!!this.setExtremes;
this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger||(e&&(r=d,q=r+b),f&&(q=c+g,e||(r=Math.max(q-b,h&&h.xData?h.xData[0]:-Number.MAX_VALUE))),z&&(e||f)&&l(r)&&(this.min=this.userMin=r,this.max=this.userMax=q));a.stickToMin=a.stickToMax=null},updatedDataHandler:function(){var a=this.chart.navigator,b=this.navigatorSeries;a.stickToMax=a.xAxis.reversed?0===Math.round(a.zoomedMin):Math.round(a.zoomedMax)>=Math.round(a.size);a.stickToMin=l(this.xAxis.min)&&this.xAxis.min<=this.xData[0]&&(!this.chart.fixedRange||
!a.stickToMax);b&&!a.hasNavigatorData&&(b.options.pointStart=this.xData[0],b.setData(this.options.data,!1,null,!1))},addChartEvents:function(){x(this.chart,"redraw",function(){var a=this.navigator,b=a&&(a.baseSeries&&a.baseSeries[0]&&a.baseSeries[0].xAxis||a.scrollbar&&this.xAxis[0]);b&&a.render(b.min,b.max)})},destroy:function(){this.removeEvents();this.xAxis&&(h(this.chart.xAxis,this.xAxis),h(this.chart.axes,this.xAxis));this.yAxis&&(h(this.chart.yAxis,this.yAxis),h(this.chart.axes,this.yAxis));
n(this.series||[],function(a){a.destroy&&a.destroy()});n("series xAxis yAxis shades outline scrollbarTrack scrollbarRifles scrollbarGroup scrollbar navigatorGroup rendered".split(" "),function(a){this[a]&&this[a].destroy&&this[a].destroy();this[a]=null},this);n([this.handles],function(a){H(a)},this)}};a.Navigator=E;z(B.prototype,"zoom",function(a,b,d){var c=this.chart,e=c.options,f=e.chart.zoomType,g=e.navigator,e=e.rangeSelector,q;this.isXAxis&&(g&&g.enabled||e&&e.enabled)&&("x"===f?c.resetZoomButton=
"blocked":"y"===f?q=!1:"xy"===f&&this.options.range&&(c=this.previousZoom,w(b)?this.previousZoom=[this.min,this.max]:c&&(b=c[0],d=c[1],delete this.previousZoom)));return void 0!==q?q:a.call(this,b,d)});z(u.prototype,"init",function(a,b,d){x(this,"beforeRender",function(){var a=this.options;if(a.navigator.enabled||a.scrollbar.enabled)this.scroller=this.navigator=new E(this)});a.call(this,b,d)});z(u.prototype,"setChartSize",function(a){var b=this.legend,d=this.navigator,c,e,g,q;a.apply(this,[].slice.call(arguments,
1));d&&(e=b&&b.options,g=d.xAxis,q=d.yAxis,c=d.scrollbarHeight,this.inverted?(d.left=d.opposite?this.chartWidth-c-d.height:this.spacing[3]+c,d.top=this.plotTop+c):(d.left=this.plotLeft+c,d.top=d.navigatorOptions.top||this.chartHeight-d.height-c-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(e&&"bottom"===e.verticalAlign&&e.enabled&&!e.floating?b.legendHeight+f(e.margin,10):0)),g&&q&&(this.inverted?g.options.left=q.options.left=d.left:g.options.top=q.options.top=
d.top,g.setAxisSize(),q.setAxisSize()))});z(g.prototype,"addPoint",function(a,b,d,c,e){var f=this.options.turboThreshold;f&&this.xData.length>f&&m(b,!0)&&this.chart.navigator&&y(20,!0);a.call(this,b,d,c,e)});z(u.prototype,"addSeries",function(a,b,d,c){a=a.call(this,b,!1,c);this.navigator&&this.navigator.setBaseSeries(null,!1);f(d,!0)&&this.redraw();return a});z(g.prototype,"update",function(a,b,d){a.call(this,b,!1);this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,
!1);f(d,!0)&&this.chart.redraw()});u.prototype.callbacks.push(function(a){var b=a.navigator;b&&(a=a.xAxis[0].getExtremes(),b.render(a.min,a.max))})})(I);(function(a){function E(a){this.init(a)}var x=a.addEvent,B=a.Axis,u=a.Chart,C=a.css,w=a.createElement,H=a.defaultOptions,n=a.defined,h=a.destroyObjectProperties,y=a.discardElement,A=a.each,c=a.extend,e=a.fireEvent,b=a.isNumber,l=a.merge,m=a.pick,t=a.pInt,f=a.splat,p=a.wrap;c(H,{rangeSelector:{verticalAlign:"top",buttonTheme:{"stroke-width":0,width:28,
height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputPosition:{align:"right",x:0,y:0},buttonPosition:{align:"left",x:0,y:0}}});H.lang=l(H.lang,{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"From",rangeSelectorTo:"To"});E.prototype={clickButton:function(a,c){var d=this,e=d.chart,g=d.buttonOptions[a],h=e.xAxis[0],k=e.scroller&&e.scroller.getUnionExtremes()||h||{},v=k.dataMin,n=k.dataMax,l,F=h&&Math.round(Math.min(h.max,m(n,h.max))),p=g.type,t,k=g._range,u,w,y,C=g.dataGrouping;if(null!==
v&&null!==n){e.fixedRange=k;C&&(this.forcedDataGrouping=!0,B.prototype.setDataGrouping.call(h||{chart:this.chart},C,!1));if("month"===p||"year"===p)h?(p={range:g,max:F,chart:e,dataMin:v,dataMax:n},l=h.minFromRange.call(p),b(p.newMax)&&(F=p.newMax)):k=g;else if(k)l=Math.max(F-k,v),F=Math.min(l+k,n);else if("ytd"===p)if(h)void 0===n&&(v=Number.MAX_VALUE,n=Number.MIN_VALUE,A(e.series,function(a){a=a.xData;v=Math.min(a[0],v);n=Math.max(a[a.length-1],n)}),c=!1),F=d.getYTDExtremes(n,v,e.time.useUTC),l=
u=F.min,F=F.max;else{x(e,"beforeRender",function(){d.clickButton(a)});return}else"all"===p&&h&&(l=v,F=n);l+=g._offsetMin;F+=g._offsetMax;d.setSelected(a);h?h.setExtremes(l,F,m(c,1),null,{trigger:"rangeSelectorButton",rangeSelectorButton:g}):(t=f(e.options.xAxis)[0],y=t.range,t.range=k,w=t.min,t.min=u,x(e,"load",function(){t.range=y;t.min=w}))}},setSelected:function(a){this.selected=this.options.selected=a},defaultButtons:[{type:"month",count:1,text:"1m"},{type:"month",count:3,text:"3m"},{type:"month",
count:6,text:"6m"},{type:"ytd",text:"YTD"},{type:"year",count:1,text:"1y"},{type:"all",text:"All"}],init:function(a){var b=this,d=a.options.rangeSelector,c=d.buttons||[].concat(b.defaultButtons),f=d.selected,h=function(){var a=b.minInput,d=b.maxInput;a&&a.blur&&e(a,"blur");d&&d.blur&&e(d,"blur")};b.chart=a;b.options=d;b.buttons=[];a.extraTopMargin=d.height;b.buttonOptions=c;this.unMouseDown=x(a.container,"mousedown",h);this.unResize=x(a,"resize",h);A(c,b.computeButtonRange);void 0!==f&&c[f]&&this.clickButton(f,
!1);x(a,"load",function(){a.xAxis&&a.xAxis[0]&&x(a.xAxis[0],"setExtremes",function(d){this.max-this.min!==a.fixedRange&&"rangeSelectorButton"!==d.trigger&&"updatedData"!==d.trigger&&b.forcedDataGrouping&&this.setDataGrouping(!1,!1)})})},updateButtonStates:function(){var a=this.chart,c=a.xAxis[0],e=Math.round(c.max-c.min),f=!c.hasVisibleSeries,q=a.scroller&&a.scroller.getUnionExtremes()||c,h=q.dataMin,k=q.dataMax,a=this.getYTDExtremes(k,h,a.time.useUTC),m=a.min,n=a.max,l=this.selected,p=b(l),t=this.options.allButtonsEnabled,
u=this.buttons;A(this.buttonOptions,function(a,b){var d=a._range,g=a.type,q=a.count||1,r=u[b],z=0;a=a._offsetMax-a._offsetMin;b=b===l;var D=d>k-h,v=d<c.minRange,F=!1,w=!1,d=d===e;("month"===g||"year"===g)&&e+36E5>=864E5*{month:28,year:365}[g]*q-a&&e-36E5<=864E5*{month:31,year:366}[g]*q+a?d=!0:"ytd"===g?(d=n-m+a===e,F=!b):"all"===g&&(d=c.max-c.min>=k-h,w=!b&&p&&d);g=!t&&(D||v||w||f);q=b&&d||d&&!p&&!F;g?z=3:q&&(p=!0,z=2);r.state!==z&&r.setState(z)})},computeButtonRange:function(a){var b=a.type,d=a.count||
1,c={millisecond:1,second:1E3,minute:6E4,hour:36E5,day:864E5,week:6048E5};if(c[b])a._range=c[b]*d;else if("month"===b||"year"===b)a._range=864E5*{month:30,year:365}[b]*d;a._offsetMin=m(a.offsetMin,0);a._offsetMax=m(a.offsetMax,0);a._range+=a._offsetMax-a._offsetMin},setInputValue:function(a,b){var d=this.chart.options.rangeSelector,c=this.chart.time,e=this[a+"Input"];n(b)&&(e.previousValue=e.HCTime,e.HCTime=b);e.value=c.dateFormat(d.inputEditDateFormat||"%Y-%m-%d",e.HCTime);this[a+"DateBox"].attr({text:c.dateFormat(d.inputDateFormat||
"%b %e, %Y",e.HCTime)})},showInput:function(a){var b=this.inputGroup,d=this[a+"DateBox"];C(this[a+"Input"],{left:b.translateX+d.x+"px",top:b.translateY+"px",width:d.width-2+"px",height:d.height-2+"px",border:"2px solid silver"})},hideInput:function(a){C(this[a+"Input"],{border:0,width:"1px",height:"1px"});this.setInputValue(a)},drawInput:function(a){function d(){var a=n.value,d=(h.inputDateParser||Date.parse)(a),f=e.xAxis[0],g=e.scroller&&e.scroller.xAxis?e.scroller.xAxis:f,q=g.dataMin,g=g.dataMax;
d!==n.previousValue&&(n.previousValue=d,b(d)||(d=a.split("-"),d=Date.UTC(t(d[0]),t(d[1])-1,t(d[2]))),b(d)&&(e.time.useUTC||(d+=6E4*(new Date).getTimezoneOffset()),m?d>c.maxInput.HCTime?d=void 0:d<q&&(d=q):d<c.minInput.HCTime?d=void 0:d>g&&(d=g),void 0!==d&&f.setExtremes(m?d:f.min,m?f.max:d,void 0,void 0,{trigger:"rangeSelectorInput"})))}var c=this,e=c.chart,f=e.renderer,h=e.options.rangeSelector,k=c.div,m="min"===a,n,l,p=this.inputGroup;this[a+"Label"]=l=f.label(H.lang[m?"rangeSelectorFrom":"rangeSelectorTo"],
this.inputGroup.offset).addClass("highcharts-range-label").attr({padding:2}).add(p);p.offset+=l.width+5;this[a+"DateBox"]=f=f.label("",p.offset).addClass("highcharts-range-input").attr({padding:2,width:h.inputBoxWidth||90,height:h.inputBoxHeight||17,stroke:h.inputBoxBorderColor||"#cccccc","stroke-width":1,"text-align":"center"}).on("click",function(){c.showInput(a);c[a+"Input"].focus()}).add(p);p.offset+=f.width+(m?10:0);this[a+"Input"]=n=w("input",{name:a,className:"highcharts-range-selector",type:"text"},
{top:e.plotTop+"px"},k);n.onfocus=function(){c.showInput(a)};n.onblur=function(){c.hideInput(a)};n.onchange=d;n.onkeypress=function(a){13===a.keyCode&&d()}},getPosition:function(){var a=this.chart,b=a.options.rangeSelector,a="top"===b.verticalAlign?a.plotTop-a.axisOffset[0]:0;return{buttonTop:a+b.buttonPosition.y,inputTop:a+b.inputPosition.y-10}},getYTDExtremes:function(a,b,c){var d=this.chart.time,e=new d.Date(a),f=d.get("FullYear",e);c=c?d.Date.UTC(f,0,1):+new d.Date(f,0,1);b=Math.max(b||0,c);e=
e.getTime();return{max:Math.min(a||e,e),min:b}},render:function(a,b){var d=this,c=d.chart,e=c.renderer,f=c.container,g=c.options,h=g.exporting&&!1!==g.exporting.enabled&&g.navigation&&g.navigation.buttonOptions,n=H.lang,l=d.div,p=g.rangeSelector,g=p.floating,t=d.buttons,l=d.inputGroup,u=p.buttonTheme,y=p.buttonPosition,x=p.inputPosition,B=p.inputEnabled,C=u&&u.states,E=c.plotLeft,I,J=d.buttonGroup,P;P=d.rendered;var Q=d.options.verticalAlign,T=c.legend,U=T&&T.options,V=y.y,R=x.y,W=P||!1,O=0,L=0,M;
if(!1!==p.enabled){P||(d.group=P=e.g("range-selector-group").attr({zIndex:7}).add(),d.buttonGroup=J=e.g("range-selector-buttons").add(P),d.zoomText=e.text(n.rangeSelectorZoom,m(E+y.x,E),15).css(p.labelStyle).add(J),I=m(E+y.x,E)+d.zoomText.getBBox().width+5,A(d.buttonOptions,function(a,b){t[b]=e.button(a.text,I,0,function(){var c=a.events&&a.events.click,e;c&&(e=c.call(a));!1!==e&&d.clickButton(b);d.isActive=!0},u,C&&C.hover,C&&C.select,C&&C.disabled).attr({"text-align":"center"}).add(J);I+=t[b].width+
m(p.buttonSpacing,5)}),!1!==B&&(d.div=l=w("div",null,{position:"relative",height:0,zIndex:1}),f.parentNode.insertBefore(l,f),d.inputGroup=l=e.g("input-group").add(P),l.offset=0,d.drawInput("min"),d.drawInput("max")));E=c.plotLeft-c.spacing[3];d.updateButtonStates();h&&this.titleCollision(c)&&"top"===Q&&"right"===y.align&&y.y+J.getBBox().height-12<(h.y||0)+h.height&&(O=-40);"left"===y.align?M=y.x-c.spacing[3]:"right"===y.align&&(M=y.x+O-c.spacing[1]);J.align({y:y.y,width:J.getBBox().width,align:y.align,
x:M},!0,c.spacingBox);d.group.placed=W;d.buttonGroup.placed=W;!1!==B&&(O=h&&this.titleCollision(c)&&"top"===Q&&"right"===x.align&&x.y-l.getBBox().height-12<(h.y||0)+h.height+c.spacing[0]?-40:0,"left"===x.align?M=E:"right"===x.align&&(M=-Math.max(c.axisOffset[1],-O)),l.align({y:x.y,width:l.getBBox().width,align:x.align,x:x.x+M-2},!0,c.spacingBox),f=l.alignAttr.translateX+l.alignOptions.x-O+l.getBBox().x+2,h=l.alignOptions.width,n=J.alignAttr.translateX+J.getBBox().x,M=J.getBBox().width+20,(x.align===
y.align||n+M>f&&f+h>n&&V<R+l.getBBox().height)&&l.attr({translateX:l.alignAttr.translateX+(c.axisOffset[1]>=-O?0:-O),translateY:l.alignAttr.translateY+J.getBBox().height+10}),d.setInputValue("min",a),d.setInputValue("max",b),d.inputGroup.placed=W);d.group.align({verticalAlign:Q},!0,c.spacingBox);a=d.group.getBBox().height+20;b=d.group.alignAttr.translateY;"bottom"===Q&&(T=U&&"bottom"===U.verticalAlign&&U.enabled&&!U.floating?T.legendHeight+m(U.margin,10):0,a=a+T-20,L=b-a-(g?0:p.y)-10);if("top"===
Q)g&&(L=0),c.titleOffset&&(L=c.titleOffset+c.options.title.margin),L+=c.margin[0]-c.spacing[0]||0;else if("middle"===Q)if(R===V)L=0>R?b+void 0:b;else if(R||V)L=0>R||0>V?L-Math.min(R,V):b-a+NaN;d.group.translate(p.x,p.y+Math.floor(L));!1!==B&&(d.minInput.style.marginTop=d.group.translateY+"px",d.maxInput.style.marginTop=d.group.translateY+"px");d.rendered=!0}},getHeight:function(){var a=this.options,b=this.group,c=a.y,e=a.buttonPosition.y,a=a.inputPosition.y,b=b?b.getBBox(!0).height+13+c:0,c=Math.min(a,
e);if(0>a&&0>e||0<a&&0<e)b+=Math.abs(c);return b},titleCollision:function(a){return!(a.options.title.text||a.options.subtitle.text)},update:function(a){var b=this.chart;l(!0,b.options.rangeSelector,a);this.destroy();this.init(b);b.rangeSelector.render()},destroy:function(){var b=this,c=b.minInput,e=b.maxInput;b.unMouseDown();b.unResize();h(b.buttons);c&&(c.onfocus=c.onblur=c.onchange=null);e&&(e.onfocus=e.onblur=e.onchange=null);a.objectEach(b,function(a,c){a&&"chart"!==c&&(a.destroy?a.destroy():
a.nodeType&&y(this[c]));a!==E.prototype[c]&&(b[c]=null)},this)}};B.prototype.toFixedRange=function(a,c,e,f){var d=this.chart&&this.chart.fixedRange;a=m(e,this.translate(a,!0,!this.horiz));c=m(f,this.translate(c,!0,!this.horiz));e=d&&(c-a)/d;.7<e&&1.3>e&&(f?a=c-d:c=a+d);b(a)&&b(c)||(a=c=void 0);return{min:a,max:c}};B.prototype.minFromRange=function(){var a=this.range,c={month:"Month",year:"FullYear"}[a.type],e,f=this.max,q,h,k=function(a,b){var d=new Date(a),e=d["get"+c]();d["set"+c](e+b);e===d["get"+
c]()&&d.setDate(0);return d.getTime()-a};b(a)?(e=f-a,h=a):(e=f+k(f,-a.count),this.chart&&(this.chart.fixedRange=f-e));q=m(this.dataMin,Number.MIN_VALUE);b(e)||(e=q);e<=q&&(e=q,void 0===h&&(h=k(e,a.count)),this.newMax=Math.min(e+h,this.dataMax));b(f)||(e=void 0);return e};p(u.prototype,"init",function(a,b,c){x(this,"init",function(){this.options.rangeSelector.enabled&&(this.rangeSelector=new E(this))});a.call(this,b,c)});p(u.prototype,"render",function(a,b,c){var d=this.axes,e=this.rangeSelector;e&&
(A(d,function(a){a.updateNames();a.setScale()}),this.getAxisMargins(),e.render(),d=e.options.verticalAlign,e.options.floating||("bottom"===d?this.extraBottomMargin=!0:"middle"!==d&&(this.extraTopMargin=!0)));a.call(this,b,c)});p(u.prototype,"update",function(b,c,e,f){var d=this.rangeSelector,g;this.extraTopMargin=this.extraBottomMargin=!1;d&&(d.render(),g=c.rangeSelector&&c.rangeSelector.verticalAlign||d.options&&d.options.verticalAlign,d.options.floating||("bottom"===g?this.extraBottomMargin=!0:
"middle"!==g&&(this.extraTopMargin=!0)));b.call(this,a.merge(!0,c,{chart:{marginBottom:m(c.chart&&c.chart.marginBottom,this.margin.bottom),spacingBottom:m(c.chart&&c.chart.spacingBottom,this.spacing.bottom)}}),e,f)});p(u.prototype,"redraw",function(a,b,c){var d=this.rangeSelector;d&&!d.options.floating&&(d.render(),d=d.options.verticalAlign,"bottom"===d?this.extraBottomMargin=!0:"middle"!==d&&(this.extraTopMargin=!0));a.call(this,b,c)});u.prototype.adjustPlotArea=function(){var a=this.rangeSelector;
this.rangeSelector&&(a=a.getHeight(),this.extraTopMargin&&(this.plotTop+=a),this.extraBottomMargin&&(this.marginBottom+=a))};u.prototype.callbacks.push(function(a){function c(){d=a.xAxis[0].getExtremes();b(d.min)&&e.render(d.min,d.max)}var d,e=a.rangeSelector,f,h;e&&(h=x(a.xAxis[0],"afterSetExtremes",function(a){e.render(a.min,a.max)}),f=x(a,"redraw",c),c());x(a,"destroy",function(){e&&(f(),h())})});a.RangeSelector=E})(I);(function(a){var E=a.arrayMax,x=a.arrayMin,B=a.Axis,u=a.Chart,C=a.defined,w=
a.each,H=a.format,n=a.grep,h=a.inArray,y=a.isNumber,A=a.isString,c=a.map,e=a.merge,b=a.pick,l=a.Point,m=a.Series,t=a.splat,f=a.SVGRenderer,p=a.wrap,d=m.prototype,g=d.init,r=d.processData,z=l.prototype.tooltipFormatter;a.StockChart=a.stockChart=function(d,f,g){var h=A(d)||d.nodeName,k=arguments[h?1:0],q=k.series,l=a.getOptions(),n,m=b(k.navigator&&k.navigator.enabled,l.navigator.enabled,!0),p=m?{startOnTick:!1,endOnTick:!1}:null,r={marker:{enabled:!1,radius:2}},D={shadow:!1,borderWidth:0};k.xAxis=
c(t(k.xAxis||{}),function(a,b){return e({minPadding:0,maxPadding:0,overscroll:0,ordinal:!0,title:{text:null},labels:{overflow:"justify"},showLastLabel:!0},l.xAxis,l.xAxis&&l.xAxis[b],a,{type:"datetime",categories:null},p)});k.yAxis=c(t(k.yAxis||{}),function(a,c){n=b(a.opposite,!0);return e({labels:{y:-2},opposite:n,showLastLabel:!(!a.categories&&"category"!==a.type),title:{text:null}},l.yAxis,l.yAxis&&l.yAxis[c],a)});k.series=null;k=e({chart:{panning:!0,pinchType:"x"},navigator:{enabled:m},scrollbar:{enabled:b(l.scrollbar.enabled,
!0)},rangeSelector:{enabled:b(l.rangeSelector.enabled,!0)},title:{text:null},tooltip:{split:b(l.tooltip.split,!0),crosshairs:!0},legend:{enabled:!1},plotOptions:{line:r,spline:r,area:r,areaspline:r,arearange:r,areasplinerange:r,column:D,columnrange:D,candlestick:D,ohlc:D}},k,{isStock:!0});k.series=q;return h?new u(d,k,g):new u(k,f)};p(B.prototype,"autoLabelAlign",function(a){var b=this.chart,c=this.options,b=b._labelPanes=b._labelPanes||{},d=this.options.labels;return this.chart.options.isStock&&
"yAxis"===this.coll&&(c=c.top+","+c.height,!b[c]&&d.enabled)?(15===d.x&&(d.x=0),void 0===d.align&&(d.align="right"),b[c]=this,"right"):a.apply(this,[].slice.call(arguments,1))});p(B.prototype,"destroy",function(a){var b=this.chart,c=this.options&&this.options.top+","+this.options.height;c&&b._labelPanes&&b._labelPanes[c]===this&&delete b._labelPanes[c];return a.apply(this,Array.prototype.slice.call(arguments,1))});p(B.prototype,"getPlotLinePath",function(d,e,f,g,l,n){var k=this,q=this.isLinked&&!this.series?
this.linkedParent.series:this.series,m=k.chart,p=m.renderer,r=k.left,t=k.top,u,v,D,x,z=[],B=[],E,G;if("xAxis"!==k.coll&&"yAxis"!==k.coll)return d.apply(this,[].slice.call(arguments,1));B=function(a){var b="xAxis"===a?"yAxis":"xAxis";a=k.options[b];return y(a)?[m[b][a]]:A(a)?[m.get(a)]:c(q,function(a){return a[b]})}(k.coll);w(k.isXAxis?m.yAxis:m.xAxis,function(a){if(C(a.options.id)?-1===a.options.id.indexOf("navigator"):1){var b=a.isXAxis?"yAxis":"xAxis",b=C(a.options[b])?m[b][a.options[b]]:m[b][0];
k===b&&B.push(a)}});E=B.length?[]:[k.isXAxis?m.yAxis[0]:m.xAxis[0]];w(B,function(b){-1!==h(b,E)||a.find(E,function(a){return a.pos===b.pos&&a.len&&b.len})||E.push(b)});G=b(n,k.translate(e,null,null,g));y(G)&&(k.horiz?w(E,function(a){var b;v=a.pos;x=v+a.len;u=D=Math.round(G+k.transB);if(u<r||u>r+k.width)l?u=D=Math.min(Math.max(r,u),r+k.width):b=!0;b||z.push("M",u,v,"L",D,x)}):w(E,function(a){var b;u=a.pos;D=u+a.len;v=x=Math.round(t+k.height-G);if(v<t||v>t+k.height)l?v=x=Math.min(Math.max(t,v),k.top+
k.height):b=!0;b||z.push("M",u,v,"L",D,x)}));return 0<z.length?p.crispPolyLine(z,f||1):null});f.prototype.crispPolyLine=function(a,b){var c;for(c=0;c<a.length;c+=6)a[c+1]===a[c+4]&&(a[c+1]=a[c+4]=Math.round(a[c+1])-b%2/2),a[c+2]===a[c+5]&&(a[c+2]=a[c+5]=Math.round(a[c+2])+b%2/2);return a};p(B.prototype,"hideCrosshair",function(a,b){a.call(this,b);this.crossLabel&&(this.crossLabel=this.crossLabel.hide())});p(B.prototype,"drawCrosshair",function(a,c,d){var e,f;a.call(this,c,d);if(C(this.crosshair.label)&&
this.crosshair.label.enabled&&this.cross){a=this.chart;var g=this.options.crosshair.label,h=this.horiz;e=this.opposite;f=this.left;var k=this.top,l=this.crossLabel,m,q=g.format,n="",p="inside"===this.options.tickPosition,r=!1!==this.crosshair.snap,t=0;c||(c=this.cross&&this.cross.e);m=h?"center":e?"right"===this.labelAlign?"right":"left":"left"===this.labelAlign?"left":"center";l||(l=this.crossLabel=a.renderer.label(null,null,null,g.shape||"callout").addClass("highcharts-crosshair-label"+(this.series[0]&&
" highcharts-color-"+this.series[0].colorIndex)).attr({align:g.align||m,padding:b(g.padding,8),r:b(g.borderRadius,3),zIndex:2}).add(this.labelGroup));h?(m=r?d.plotX+f:c.chartX,k+=e?0:this.height):(m=e?this.width+f:0,k=r?d.plotY+k:c.chartY);q||g.formatter||(this.isDatetimeAxis&&(n="%b %d, %Y"),q="{value"+(n?":"+n:"")+"}");c=r?d[this.isXAxis?"x":"y"]:this.toValue(h?c.chartX:c.chartY);l.attr({text:q?H(q,{value:c},a.time):g.formatter.call(this,c),x:m,y:k,visibility:c<this.min||c>this.max?"hidden":"visible"});
c=l.getBBox();if(h){if(p&&!e||!p&&e)k=l.y-c.height}else k=l.y-c.height/2;h?(e=f-c.x,f=f+this.width-c.x):(e="left"===this.labelAlign?f:0,f="right"===this.labelAlign?f+this.width:a.chartWidth);l.translateX<e&&(t=e-l.translateX);l.translateX+c.width>=f&&(t=-(l.translateX+c.width-f));l.attr({x:m+t,y:k,anchorX:h?m:this.opposite?0:a.chartWidth,anchorY:h?this.opposite?a.chartHeight:0:k+c.height/2})}});d.init=function(){g.apply(this,arguments);this.setCompare(this.options.compare)};d.setCompare=function(a){this.modifyValue=
"value"===a||"percent"===a?function(b,c){var d=this.compareValue;if(void 0!==b&&void 0!==d)return b="value"===a?b-d:b/d*100-(100===this.options.compareBase?0:100),c&&(c.change=b),b}:null;this.userOptions.compare=a;this.chart.hasRendered&&(this.isDirty=!0)};d.processData=function(){var a,b=-1,c,d,e=!0===this.options.compareStart?0:1,f,g;r.apply(this,arguments);if(this.xAxis&&this.processedYData)for(c=this.processedXData,d=this.processedYData,f=d.length,this.pointArrayMap&&(b=h("close",this.pointArrayMap),
-1===b&&(b=h(this.pointValKey||"y",this.pointArrayMap))),a=0;a<f-e;a++)if(g=d[a]&&-1<b?d[a][b]:d[a],y(g)&&c[a+e]>=this.xAxis.min&&0!==g){this.compareValue=g;break}};p(d,"getExtremes",function(a){var b;a.apply(this,[].slice.call(arguments,1));this.modifyValue&&(b=[this.modifyValue(this.dataMin),this.modifyValue(this.dataMax)],this.dataMin=x(b),this.dataMax=E(b))});B.prototype.setCompare=function(a,c){this.isXAxis||(w(this.series,function(b){b.setCompare(a)}),b(c,!0)&&this.chart.redraw())};l.prototype.tooltipFormatter=
function(c){c=c.replace("{point.change}",(0<this.change?"+":"")+a.numberFormat(this.change,b(this.series.tooltipOptions.changeDecimals,2)));return z.apply(this,[c])};p(m.prototype,"render",function(a){this.chart.is3d&&this.chart.is3d()||this.chart.polar||!this.xAxis||this.xAxis.isRadial||(!this.clipBox&&this.animate?(this.clipBox=e(this.chart.clipBox),this.clipBox.width=this.xAxis.len,this.clipBox.height=this.yAxis.len):this.chart[this.sharedClipKey]?this.chart[this.sharedClipKey].attr({width:this.xAxis.len,
height:this.yAxis.len}):this.clipBox&&(this.clipBox.width=this.xAxis.len,this.clipBox.height=this.yAxis.len));a.call(this)});p(u.prototype,"getSelectedPoints",function(a){var b=a.call(this);w(this.series,function(a){a.hasGroupedData&&(b=b.concat(n(a.points||[],function(a){return a.selected})))});return b});p(u.prototype,"update",function(a,b){"scrollbar"in b&&this.navigator&&(e(!0,this.options.scrollbar,b.scrollbar),this.navigator.update({},!1),delete b.scrollbar);return a.apply(this,Array.prototype.slice.call(arguments,
1))})})(I)});
