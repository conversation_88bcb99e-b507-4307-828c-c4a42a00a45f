﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AntiXSS" version="4.3.0" targetFramework="net451" userInstalled="true" />
  <package id="Antlr" version="3.4.1.9004" targetFramework="net45" userInstalled="true" />
  <package id="Aspose.Words" version="23.9.0" targetFramework="net451" requireReinstallation="true" />
  <package id="AutoMapper" version="6.0.2" targetFramework="net451" />
  <package id="BeginCollectionItem" version="*******" targetFramework="net451" />
  <package id="bootstrap" version="3.0.0" targetFramework="net45" userInstalled="true" />
  <package id="Dapper" version="1.42" targetFramework="net451" />
  <package id="Dapper.SqlBuilder" version="1.40.0" targetFramework="net451" />
  <package id="DecipherSharp" version="0.2.8" targetFramework="net451" requireReinstallation="true" />
  <package id="DotNet.Highcharts" version="4.0" targetFramework="net451" />
  <package id="DotNetZip" version="1.9.3" targetFramework="net451" userInstalled="true" />
  <package id="EFUtilities" version="1.0.2" targetFramework="net451" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net451" userInstalled="true" />
  <package id="EntityFramework.Extended" version="*********" targetFramework="net451" />
  <package id="EntityFramework.zh-Hant" version="6.1.3" targetFramework="net451" userInstalled="true" />
  <package id="FreeSpire.Doc" version="6.3" targetFramework="net451" />
  <package id="FreeSpire.PDF" version="8.6.0" targetFramework="net461" />
  <package id="Hangfire" version="1.6.17" targetFramework="net451" />
  <package id="Hangfire.Core" version="1.6.17" targetFramework="net451" />
  <package id="Hangfire.MemoryStorage" version="1.5.1" targetFramework="net451" />
  <package id="Hangfire.SqlServer" version="1.6.17" targetFramework="net451" />
  <package id="HeadlessChromium.Puppeteer.Lambda.Dotnet" version="********" targetFramework="net461" />
  <package id="iTextSharp" version="5.5.12" targetFramework="net451" />
  <package id="itextsharp.xmlworker" version="5.5.12" targetFramework="net451" />
  <package id="jQuery" version="1.10.2" targetFramework="net45" userInstalled="true" />
  <package id="jquery.simplemodal" version="1.4.3.1" targetFramework="net451" />
  <package id="jQuery.Validation" version="1.11.1" targetFramework="net45" userInstalled="true" />
  <package id="log4net" version="2.0.8" targetFramework="net451" />
  <package id="Microsoft.AspNet.Cors" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.2" targetFramework="net451" userInstalled="true" />
  <package id="Microsoft.AspNet.Mvc.zh-Hant" version="5.2.2" targetFramework="net451" userInstalled="true" />
  <package id="Microsoft.AspNet.Razor" version="3.2.6" targetFramework="net451" userInstalled="true" />
  <package id="Microsoft.AspNet.Razor.zh-Hant" version="3.2.6" targetFramework="net451" userInstalled="true" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net45" userInstalled="true" />
  <package id="Microsoft.AspNet.Web.Optimization.zh-Hant" version="1.1.3" targetFramework="net45" userInstalled="true" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.Client.zh-Hant" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.Core.zh-Hant" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.WebHost.zh-Hant" version="5.2.2" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.6" targetFramework="net451" userInstalled="true" />
  <package id="Microsoft.AspNet.WebPages.zh-Hant" version="3.2.6" targetFramework="net451" userInstalled="true" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.0" targetFramework="net46" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.Extensions.Options" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.Extensions.Primitives" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.2" targetFramework="net45" userInstalled="true" />
  <package id="Microsoft.Net.Compilers" version="2.0.1" targetFramework="net46" developmentDependency="true" />
  <package id="Microsoft.Net.Http" version="2.0.20710.0" targetFramework="net451" />
  <package id="Microsoft.Net.Http.Headers" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.Owin" version="3.1.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="3.1.0" targetFramework="net451" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net45" userInstalled="true" />
  <package id="Modernizr" version="2.6.2" targetFramework="net45" userInstalled="true" />
  <package id="MvcPaging" version="2.1.7" targetFramework="net451" userInstalled="true" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net461" />
  <package id="NPOI" version="*******" targetFramework="net451" />
  <package id="NReco.PdfGenerator" version="1.1.15" targetFramework="net451" />
  <package id="Owin" version="1.0" targetFramework="net451" />
  <package id="PuppeteerSharp" version="1.17.2" targetFramework="net461" />
  <package id="PushSharp" version="4.0.10" targetFramework="net451" />
  <package id="Respond" version="1.2.0" targetFramework="net45" userInstalled="true" />
  <package id="Select.HtmlToPdf" version="23.1.0" targetFramework="net461" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net461" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net451" requireReinstallation="true" />
  <package id="System.Memory" version="4.5.4" targetFramework="net461" />
  <package id="System.Net.Http" version="4.3.3" targetFramework="net461" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net461" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net461" />
  <package id="UAParser" version="2.1.0.0" targetFramework="net451" />
  <package id="WebGrease" version="1.5.2" targetFramework="net45" userInstalled="true" />
  <package id="ZXing.Net" version="0.16.2" targetFramework="net451" requireReinstallation="true" />
</packages>