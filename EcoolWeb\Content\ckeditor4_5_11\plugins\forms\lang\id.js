﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'id', {
	button: {
		title: 'Properti Tombol',
		text: '<PERSON><PERSON> (<PERSON>lai)',
		type: 'Tipe',
		typeBtn: 'Tombol',
		typeSbm: 'Menyerah<PERSON>',
		typeRst: 'Atur Ulang'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Checkbox Properties', // MISSING
		radioTitle: 'Radio Button Properties', // MISSING
		value: '<PERSON><PERSON>',
		selected: 'Terpilih',
		required: 'Wajib'
	},
	form: {
		title: 'Form Properties', // MISSING
		menu: 'Form Properties', // MISSING
		action: 'Aksi',
		method: 'Metode',
		encoding: 'Encoding' // MISSING
	},
	hidden: {
		title: 'Hidden Field Properties', // MISSING
		name: 'Nama',
		value: '<PERSON><PERSON>'
	},
	select: {
		title: 'Selection Field Properties', // MISSING
		selectInfo: 'Select Info', // MISSING
		opAvail: 'Available Options', // MISSING
		value: '<PERSON><PERSON>',
		size: 'Ukuran',
		lines: 'garis',
		chkMulti: 'Izinkan pemilihan ganda',
		required: 'Wajib',
		opText: 'Teks',
		opValue: 'Nilai',
		btnAdd: 'Tambah',
		btnModify: 'Modifikasi',
		btnUp: 'Atas',
		btnDown: 'Bawah',
		btnSetValue: 'Atur sebagai nilai yang dipilih',
		btnDelete: 'Hapus'
	},
	textarea: {
		title: 'Textarea Properties', // MISSING
		cols: 'Kolom',
		rows: 'Baris'
	},
	textfield: {
		title: 'Text Field Properties', // MISSING
		name: 'Name',
		value: 'Nilai',
		charWidth: 'Character Width', // MISSING
		maxChars: 'Maximum Characters', // MISSING
		required: 'Wajib',
		type: 'Tipe',
		typeText: 'Teks',
		typePass: 'Kata kunci',
		typeEmail: 'Surel',
		typeSearch: 'Cari',
		typeTel: 'Nomor Telepon',
		typeUrl: 'URL'
	}
} );
