﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using PuppeteerSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace EcoolWeb.Service
{
    public class HTMLtoPDF
    {
        public  async Task AppetizerAsync(string str,string dicPath)
        {
            var firstUrl = $"https://github.com/explore";
            var secondUrl = $"https://leetcode.com/";
            byte[] mergePage = null;
            await new BrowserFetcher().DownloadAsync(BrowserFetcher.DefaultRevision);
            //var browser1 = await Puppeteer.ConnectAsync(new ConnectOptions
            //{
            //    BrowserURL = "http://127.0.0.1:2122"
            //});
            var pdfUrl = new[] { firstUrl, secondUrl };
            //var pdfUrl = new[] {str };
            using (var browser = await Puppeteer.LaunchAsync(new LaunchOptions()
            {
                Headless = true //偵測時可設定false觀察網頁顯示結果(註：非Headless時不能匯出PDF)
            }))
            {
                using (var stream = File.Create(@"D:\test\test.pdf"))
                using (var doc = new Document(PageSize.A4))
                using (var pdfWriter = PdfWriter.GetInstance(doc, stream))
                using (var page = await browser.NewPageAsync())

                {

                    doc.Open();
                    foreach (var item in pdfUrl)
                    {
                        await page.GoToAsync(item, new NavigationOptions
                        {
                            //此方法可以偵測JS連線都完成了
                            WaitUntil = new WaitUntilNavigation[2]
                        });
                        //但用上述waitUntil方式我的目標網站渲染方式不同，所以最後還是加上了等待。
                        Thread.Sleep(1000);
                        mergePage = await page.ScreenshotDataAsync(new ScreenshotOptions
                        {
                            OmitBackground = true,      //是否顯示背景圖式
                            FullPage = true,        //是否整頁
                            Type = ScreenshotType.Jpeg,     //可以設定檔案格式
                            Quality = 100           //可以設定照片品質
                        });
                        //檔案都取回後再透過itextSharp進行處理
                        var image = iTextSharp.text.Image.GetInstance(mergePage);
                        //這邊可以調整圖片大小
                        image.ScalePercent(60);
                        doc.Add(image);
                    }
                    doc.Close();
                    doc.Dispose();
                }
            }

            }
       
    }
}