﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI10StatisticsVoteDetailsViewModel
    {
        /// <summary>
        /// 題目主檔
        /// </summary>
        public SAQT01 Title { get; set; }

        public string OrdercColumn { get; set; }
        public string SyntaxName { get; set; }
        public int whereQ_NUM { get; set; }
        public string WhereSearch { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade1 { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO1 { get; set; }
        public SAQT03 GetSAQT03 { get; set; }
        /// <summary>
        /// 題目
        /// </summary>
        public SAQT02 Topic { get; set; }

        public List<ADDI10VotePeopleViewModel> VotePeople { get; set; }

        public string ANSWER { get; set; }
    }
}