@charset "UTF-8";
/*!
 * Bootstrap Reboot v4.1.1 (https://getbootstrap.com/)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)
 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}

[tabindex="-1"]:focus {
  outline: 0 !important;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

dfn {
  font-style: italic;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: #000;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}
a:hover {
  color: black;
  text-decoration: underline;
}

a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):hover, a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):focus {
  outline: 0;
}

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg:not(:root) {
  overflow: hidden;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
html [type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type=radio],
input[type=checkbox] {
  box-sizing: border-box;
  padding: 0;
}

input[type=date],
input[type=time],
input[type=datetime-local],
input[type=month] {
  -webkit-appearance: listbox;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

progress {
  vertical-align: baseline;
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type=search]::-webkit-search-cancel-button,
[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.no-gutters > .col,
.no-gutters > [class*=col-] {
  padding-right: 0;
  padding-left: 0;
}

.col-xl,
.col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg,
.col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md,
.col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm,
.col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col,
.col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1 {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: none;
}

.col-1 {
  flex: 0 0 8.3333333333%;
  max-width: 8.3333333333%;
}

.col-2 {
  flex: 0 0 16.6666666667%;
  max-width: 16.6666666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.3333333333%;
  max-width: 33.3333333333%;
}

.col-5 {
  flex: 0 0 41.6666666667%;
  max-width: 41.6666666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.3333333333%;
  max-width: 58.3333333333%;
}

.col-8 {
  flex: 0 0 66.6666666667%;
  max-width: 66.6666666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.3333333333%;
  max-width: 83.3333333333%;
}

.col-11 {
  flex: 0 0 91.6666666667%;
  max-width: 91.6666666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  order: -1;
}

.order-last {
  order: 13;
}

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.offset-1 {
  margin-left: 8.3333333333%;
}

.offset-2 {
  margin-left: 16.6666666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.3333333333%;
}

.offset-5 {
  margin-left: 41.6666666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.3333333333%;
}

.offset-8 {
  margin-left: 66.6666666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.3333333333%;
}

.offset-11 {
  margin-left: 91.6666666667%;
}

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-sm-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-sm-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-sm-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-sm-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-sm-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    order: -1;
  }
  .order-sm-last {
    order: 13;
  }
  .order-sm-0 {
    order: 0;
  }
  .order-sm-1 {
    order: 1;
  }
  .order-sm-2 {
    order: 2;
  }
  .order-sm-3 {
    order: 3;
  }
  .order-sm-4 {
    order: 4;
  }
  .order-sm-5 {
    order: 5;
  }
  .order-sm-6 {
    order: 6;
  }
  .order-sm-7 {
    order: 7;
  }
  .order-sm-8 {
    order: 8;
  }
  .order-sm-9 {
    order: 9;
  }
  .order-sm-10 {
    order: 10;
  }
  .order-sm-11 {
    order: 11;
  }
  .order-sm-12 {
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.3333333333%;
  }
  .offset-sm-2 {
    margin-left: 16.6666666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.3333333333%;
  }
  .offset-sm-5 {
    margin-left: 41.6666666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.3333333333%;
  }
  .offset-sm-8 {
    margin-left: 66.6666666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.3333333333%;
  }
  .offset-sm-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-md-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-md-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-md-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-md-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-md-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    order: -1;
  }
  .order-md-last {
    order: 13;
  }
  .order-md-0 {
    order: 0;
  }
  .order-md-1 {
    order: 1;
  }
  .order-md-2 {
    order: 2;
  }
  .order-md-3 {
    order: 3;
  }
  .order-md-4 {
    order: 4;
  }
  .order-md-5 {
    order: 5;
  }
  .order-md-6 {
    order: 6;
  }
  .order-md-7 {
    order: 7;
  }
  .order-md-8 {
    order: 8;
  }
  .order-md-9 {
    order: 9;
  }
  .order-md-10 {
    order: 10;
  }
  .order-md-11 {
    order: 11;
  }
  .order-md-12 {
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.3333333333%;
  }
  .offset-md-2 {
    margin-left: 16.6666666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.3333333333%;
  }
  .offset-md-5 {
    margin-left: 41.6666666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.3333333333%;
  }
  .offset-md-8 {
    margin-left: 66.6666666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.3333333333%;
  }
  .offset-md-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-lg-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-lg-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-lg-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-lg-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-lg-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    order: -1;
  }
  .order-lg-last {
    order: 13;
  }
  .order-lg-0 {
    order: 0;
  }
  .order-lg-1 {
    order: 1;
  }
  .order-lg-2 {
    order: 2;
  }
  .order-lg-3 {
    order: 3;
  }
  .order-lg-4 {
    order: 4;
  }
  .order-lg-5 {
    order: 5;
  }
  .order-lg-6 {
    order: 6;
  }
  .order-lg-7 {
    order: 7;
  }
  .order-lg-8 {
    order: 8;
  }
  .order-lg-9 {
    order: 9;
  }
  .order-lg-10 {
    order: 10;
  }
  .order-lg-11 {
    order: 11;
  }
  .order-lg-12 {
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.3333333333%;
  }
  .offset-lg-2 {
    margin-left: 16.6666666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.3333333333%;
  }
  .offset-lg-5 {
    margin-left: 41.6666666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.3333333333%;
  }
  .offset-lg-8 {
    margin-left: 66.6666666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.3333333333%;
  }
  .offset-lg-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-xl-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-xl-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-xl-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-xl-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-xl-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    order: -1;
  }
  .order-xl-last {
    order: 13;
  }
  .order-xl-0 {
    order: 0;
  }
  .order-xl-1 {
    order: 1;
  }
  .order-xl-2 {
    order: 2;
  }
  .order-xl-3 {
    order: 3;
  }
  .order-xl-4 {
    order: 4;
  }
  .order-xl-5 {
    order: 5;
  }
  .order-xl-6 {
    order: 6;
  }
  .order-xl-7 {
    order: 7;
  }
  .order-xl-8 {
    order: 8;
  }
  .order-xl-9 {
    order: 9;
  }
  .order-xl-10 {
    order: 10;
  }
  .order-xl-11 {
    order: 11;
  }
  .order-xl-12 {
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.3333333333%;
  }
  .offset-xl-2 {
    margin-left: 16.6666666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.3333333333%;
  }
  .offset-xl-5 {
    margin-left: 41.6666666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.3333333333%;
  }
  .offset-xl-8 {
    margin-left: 66.6666666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.3333333333%;
  }
  .offset-xl-11 {
    margin-left: 91.6666666667%;
  }
}
.border {
  border: 1px solid #dee2e6 !important;
}

.border-top {
  border-top: 1px solid #dee2e6 !important;
}

.border-right {
  border-right: 1px solid #dee2e6 !important;
}

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}

.border-left {
  border-left: 1px solid #dee2e6 !important;
}

.border-0 {
  border: 0 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-right-0 {
  border-right: 0 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: #007bff !important;
}

.border-secondary {
  border-color: #6c757d !important;
}

.border-success {
  border-color: #28a745 !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

.border-warning {
  border-color: #ffc107 !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.border-light {
  border-color: #f8f9fa !important;
}

.border-dark {
  border-color: #343a40 !important;
}

.border-white {
  border-color: #fff !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}
@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
}
.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}
.embed-responsive::before {
  display: block;
  content: "";
}
.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.embed-responsive-21by9::before {
  padding-top: 42.8571428571%;
}

.embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.embed-responsive-4by3::before {
  padding-top: 75%;
}

.embed-responsive-1by1::before {
  padding-top: 100%;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}
.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.float-none {
  float: none !important;
}

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }
  .float-sm-right {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
}
@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }
  .float-md-right {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
}
@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }
  .float-lg-right {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }
  .float-xl-right {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
}
.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

@supports (position: sticky) {
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important;
  }
  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important;
  }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important;
  }
  .ml-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mr-sm-1,
  .mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-sm-1,
  .mx-sm-1 {
    margin-left: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mr-sm-2,
  .mx-sm-2 {
    margin-right: 0.5rem !important;
  }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-sm-2,
  .mx-sm-2 {
    margin-left: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1rem !important;
  }
  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 1rem !important;
  }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1rem !important;
  }
  .ml-sm-3,
  .mx-sm-3 {
    margin-left: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 1.5rem !important;
  }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-sm-4,
  .mx-sm-4 {
    margin-left: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 3rem !important;
  }
  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 3rem !important;
  }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 3rem !important;
  }
  .ml-sm-5,
  .mx-sm-5 {
    margin-left: 3rem !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important;
  }
  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important;
  }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important;
  }
  .pl-sm-0,
  .px-sm-0 {
    padding-left: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pr-sm-1,
  .px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-sm-1,
  .px-sm-1 {
    padding-left: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pr-sm-2,
  .px-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-sm-2,
  .px-sm-2 {
    padding-left: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1rem !important;
  }
  .pr-sm-3,
  .px-sm-3 {
    padding-right: 1rem !important;
  }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pl-sm-3,
  .px-sm-3 {
    padding-left: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pr-sm-4,
  .px-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-sm-4,
  .px-sm-4 {
    padding-left: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 3rem !important;
  }
  .pr-sm-5,
  .px-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 3rem !important;
  }
  .pl-sm-5,
  .px-sm-5 {
    padding-left: 3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important;
  }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important;
  }
  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important;
  }
  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important;
  }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important;
  }
  .ml-md-0,
  .mx-md-0 {
    margin-left: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .mt-md-1,
  .my-md-1 {
    margin-top: 0.25rem !important;
  }
  .mr-md-1,
  .mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-md-1,
  .mx-md-1 {
    margin-left: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .mt-md-2,
  .my-md-2 {
    margin-top: 0.5rem !important;
  }
  .mr-md-2,
  .mx-md-2 {
    margin-right: 0.5rem !important;
  }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-md-2,
  .mx-md-2 {
    margin-left: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .mt-md-3,
  .my-md-3 {
    margin-top: 1rem !important;
  }
  .mr-md-3,
  .mx-md-3 {
    margin-right: 1rem !important;
  }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1rem !important;
  }
  .ml-md-3,
  .mx-md-3 {
    margin-left: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .mt-md-4,
  .my-md-4 {
    margin-top: 1.5rem !important;
  }
  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important;
  }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-md-4,
  .mx-md-4 {
    margin-left: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .mt-md-5,
  .my-md-5 {
    margin-top: 3rem !important;
  }
  .mr-md-5,
  .mx-md-5 {
    margin-right: 3rem !important;
  }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 3rem !important;
  }
  .ml-md-5,
  .mx-md-5 {
    margin-left: 3rem !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important;
  }
  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important;
  }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important;
  }
  .pl-md-0,
  .px-md-0 {
    padding-left: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .pt-md-1,
  .py-md-1 {
    padding-top: 0.25rem !important;
  }
  .pr-md-1,
  .px-md-1 {
    padding-right: 0.25rem !important;
  }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-md-1,
  .px-md-1 {
    padding-left: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .pt-md-2,
  .py-md-2 {
    padding-top: 0.5rem !important;
  }
  .pr-md-2,
  .px-md-2 {
    padding-right: 0.5rem !important;
  }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-md-2,
  .px-md-2 {
    padding-left: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .pt-md-3,
  .py-md-3 {
    padding-top: 1rem !important;
  }
  .pr-md-3,
  .px-md-3 {
    padding-right: 1rem !important;
  }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1rem !important;
  }
  .pl-md-3,
  .px-md-3 {
    padding-left: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .pt-md-4,
  .py-md-4 {
    padding-top: 1.5rem !important;
  }
  .pr-md-4,
  .px-md-4 {
    padding-right: 1.5rem !important;
  }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-md-4,
  .px-md-4 {
    padding-left: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .pt-md-5,
  .py-md-5 {
    padding-top: 3rem !important;
  }
  .pr-md-5,
  .px-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 3rem !important;
  }
  .pl-md-5,
  .px-md-5 {
    padding-left: 3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important;
  }
  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
  }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important;
  }
  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important;
  }
  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important;
  }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important;
  }
  .ml-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mr-lg-1,
  .mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-lg-1,
  .mx-lg-1 {
    margin-left: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mr-lg-2,
  .mx-lg-2 {
    margin-right: 0.5rem !important;
  }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-lg-2,
  .mx-lg-2 {
    margin-left: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1rem !important;
  }
  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 1rem !important;
  }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1rem !important;
  }
  .ml-lg-3,
  .mx-lg-3 {
    margin-left: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 1.5rem !important;
  }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-lg-4,
  .mx-lg-4 {
    margin-left: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 3rem !important;
  }
  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 3rem !important;
  }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 3rem !important;
  }
  .ml-lg-5,
  .mx-lg-5 {
    margin-left: 3rem !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important;
  }
  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important;
  }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important;
  }
  .pl-lg-0,
  .px-lg-0 {
    padding-left: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pr-lg-1,
  .px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-lg-1,
  .px-lg-1 {
    padding-left: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pr-lg-2,
  .px-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-lg-2,
  .px-lg-2 {
    padding-left: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1rem !important;
  }
  .pr-lg-3,
  .px-lg-3 {
    padding-right: 1rem !important;
  }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pl-lg-3,
  .px-lg-3 {
    padding-left: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pr-lg-4,
  .px-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-lg-4,
  .px-lg-4 {
    padding-left: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 3rem !important;
  }
  .pr-lg-5,
  .px-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 3rem !important;
  }
  .pl-lg-5,
  .px-lg-5 {
    padding-left: 3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important;
  }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important;
  }
  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important;
  }
  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important;
  }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important;
  }
  .ml-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xl-1,
  .mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xl-1,
  .mx-xl-1 {
    margin-left: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mr-xl-2,
  .mx-xl-2 {
    margin-right: 0.5rem !important;
  }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xl-2,
  .mx-xl-2 {
    margin-left: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1rem !important;
  }
  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 1rem !important;
  }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1rem !important;
  }
  .ml-xl-3,
  .mx-xl-3 {
    margin-left: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 1.5rem !important;
  }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-xl-4,
  .mx-xl-4 {
    margin-left: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 3rem !important;
  }
  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 3rem !important;
  }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 3rem !important;
  }
  .ml-xl-5,
  .mx-xl-5 {
    margin-left: 3rem !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important;
  }
  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important;
  }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important;
  }
  .pl-xl-0,
  .px-xl-0 {
    padding-left: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xl-1,
  .px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xl-1,
  .px-xl-1 {
    padding-left: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pr-xl-2,
  .px-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xl-2,
  .px-xl-2 {
    padding-left: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1rem !important;
  }
  .pr-xl-3,
  .px-xl-3 {
    padding-right: 1rem !important;
  }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pl-xl-3,
  .px-xl-3 {
    padding-left: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pr-xl-4,
  .px-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-xl-4,
  .px-xl-4 {
    padding-left: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 3rem !important;
  }
  .pr-xl-5,
  .px-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 3rem !important;
  }
  .pl-xl-5,
  .px-xl-5 {
    padding-left: 3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important;
  }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important;
  }
  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important;
  }
}
.App_hide {
  display: block;
}

.App_show {
  display: none;
}

/*舊版右版按鈕重疊時區塊div穿透a保持不穿透*/
._Layout_RightBar {
  pointer-events: none;
}

._Layout_RightBar a {
  display: inline-block;
  pointer-events: visible;
}

body {
  -webkit-print-color-adjust: exact;
}

@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
#PageContent {
  word-break: break-all;
}

.div-hidden {
  display: none;
}

.vertical-middle-sm {
  display: table;
}
.vertical-middle-sm .div {
  display: table-cell;
  height: 100%;
  min-height: 100%;
  float: none !important;
}

a:not([href]):not([tabindex]) {
  color: #000;
}

.msg-img {
  width: 100%;
  height: auto;
  max-width: 30px;
  max-height: 30px;
}

.center {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  float: none;
}

.middle {
  display: table-cell;
  vertical-align: middle;
  float: none;
}

.center_bottom {
  display: table-cell;
  text-align: center;
  vertical-align: bottom;
  float: none;
}

.center_top {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  float: none;
}

.bar-div {
  max-width: 664px;
  margin: 0 auto;
}

.col-height {
  margin-bottom: -10000px;
  padding-bottom: 10000px;
}

.row-hidden {
  overflow: hidden;
}

.prod-caption {
  text-align: left;
  line-height: 1.2;
  margin-left: 6px;
}

@media screen and (max-width: 1200px) {
  .prod-icon {
    text-align: left;
  }
}
@media screen and (min-width: 1200px) {
  .prod-icon {
    text-align: center;
  }
}
.prod-text {
  margin-right: 14px;
  display: inline;
}

.btn-prod {
  margin-top: 10px;
  margin-bottom: 10px;
}

.box {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  width: 270px;
  height: 200px;
  margin-bottom: 12px;
}
.box > div {
  /*設置圖片垂直居中*/
  width: 100%;
  max-width: 200px;
  max-height: 200px;
  vertical-align: middle;
  text-align: center;
  margin: 0 auto;
}
.box > div > img {
  /*設置圖片垂直居中*/
  display: block;
  width: 100%;
  max-width: 200px;
  max-height: 200px;
  vertical-align: middle;
}

.imgEZ {
  max-width: 100%;
  height: auto;
}

.imgMenu {
  max-width: 100%;
  width: auto\9 ;
  /* IE6, IE7, IE8, IE9 */
  width: 100%;
  height: auto;
}

.table-eZ {
  margin-bottom: 15px;
  overflow-x: scroll;
  overflow-y: hidden;
  margin: 0px auto;
}
.table-eZ .table {
  margin-bottom: 0;
  background-color: #fff;
}
.table-eZ .table thead > tr > th,
.table-eZ .table tbody > tr > th,
.table-eZ .table tfoot > tr > th,
.table-eZ .table thead > tr > td,
.table-eZ .table tbody > tr > td,
.table-eZ .table tfoot > tr > td {
  white-space: nowrap;
}
.table-eZ .table-bordered {
  border: 0;
}
.table-eZ .table-bordered thead > tr > th:first-child,
.table-eZ .table-bordered tbody > tr > th:first-child,
.table-eZ .table-bordered tfoot > tr > th:first-child,
.table-eZ .table-bordered thead > tr > td:first-child,
.table-eZ .table-bordered tbody > tr > td:first-child,
.table-eZ .table-bordered tfoot > tr > td:first-child {
  border-left: 0;
}
.table-eZ .table-bordered thead > tr > th:last-child,
.table-eZ .table-bordered tbody > tr > th:last-child,
.table-eZ .table-bordered tfoot > tr > th:last-child,
.table-eZ .table-bordered thead > tr > td:last-child,
.table-eZ .table-bordered tbody > tr > td:last-child,
.table-eZ .table-bordered tfoot > tr > td:last-child {
  border-right: 0;
}
.table-eZ .table-bordered thead > tr:last-child > th,
.table-eZ .table-bordered tbody > tr:last-child > th,
.table-eZ .table-bordered tfoot > tr:last-child > th,
.table-eZ .table-bordered thead > tr:last-child > td,
.table-eZ .table-bordered tbody > tr:last-child > td,
.table-eZ .table-bordered tfoot > tr:last-child > td {
  border-bottom: 0;
}

.table-eZwhitespacinitial {
  margin-bottom: 15px;
  overflow-x: scroll;
  overflow-y: hidden;
  margin: 0px auto;
}
.table-eZwhitespacinitial .table {
  margin-bottom: 0;
  background-color: #fff;
}
.table-eZwhitespacinitial .table thead > tr > th,
.table-eZwhitespacinitial .table tbody > tr > th,
.table-eZwhitespacinitial .table tfoot > tr > th,
.table-eZwhitespacinitial .table thead > tr > td,
.table-eZwhitespacinitial .table tbody > tr > td,
.table-eZwhitespacinitial .table tfoot > tr > td {
  white-space: pre-line;
}
.table-eZwhitespacinitial .table-bordered {
  border: 0;
}
.table-eZwhitespacinitial .table-bordered thead > tr > th:first-child,
.table-eZwhitespacinitial .table-bordered tbody > tr > th:first-child,
.table-eZwhitespacinitial .table-bordered tfoot > tr > th:first-child,
.table-eZwhitespacinitial .table-bordered thead > tr > td:first-child,
.table-eZwhitespacinitial .table-bordered tbody > tr > td:first-child,
.table-eZwhitespacinitial .table-bordered tfoot > tr > td:first-child {
  border-left: 0;
}
.table-eZwhitespacinitial .table-bordered thead > tr > th:last-child,
.table-eZwhitespacinitial .table-bordered tbody > tr > th:last-child,
.table-eZwhitespacinitial .table-bordered tfoot > tr > th:last-child,
.table-eZwhitespacinitial .table-bordered thead > tr > td:last-child,
.table-eZwhitespacinitial .table-bordered tbody > tr > td:last-child,
.table-eZwhitespacinitial .table-bordered tfoot > tr > td:last-child {
  border-right: 0;
}
.table-eZwhitespacinitial .table-bordered thead > tr:last-child > th,
.table-eZwhitespacinitial .table-bordered tbody > tr:last-child > th,
.table-eZwhitespacinitial .table-bordered tfoot > tr:last-child > th,
.table-eZwhitespacinitial .table-bordered thead > tr:last-child > td,
.table-eZwhitespacinitial .table-bordered tbody > tr:last-child > td,
.table-eZwhitespacinitial .table-bordered tfoot > tr:last-child > td {
  border-bottom: 0;
}

.table-eZ_Not_overflow {
  width: 100%;
  margin-bottom: 15px;
  margin: 0px auto;
}
.table-eZ_Not_overflow .table {
  margin-bottom: 0;
  background-color: #fff;
  margin-bottom: 0;
  background-color: #fff;
}
.table-eZ_Not_overflow .table-bordered {
  border: 0;
}
.table-eZ_Not_overflow .table-bordered thead > tr > th:first-child,
.table-eZ_Not_overflow .table-bordered tbody > tr > th:first-child,
.table-eZ_Not_overflow .table-bordered tfoot > tr > th:first-child,
.table-eZ_Not_overflow .table-bordered thead > tr > td:first-child,
.table-eZ_Not_overflow .table-bordered tbody > tr > td:first-child,
.table-eZ_Not_overflow .table-bordered tfoot > tr > td:first-child {
  border-left: 0;
}
.table-eZ_Not_overflow .table-bordered thead > tr > th:last-child,
.table-eZ_Not_overflow .table-bordered tbody > tr > th:last-child,
.table-eZ_Not_overflow .table-bordered tfoot > tr > th:last-child,
.table-eZ_Not_overflow .table-bordered thead > tr > td:last-child,
.table-eZ_Not_overflow .table-bordered tbody > tr > td:last-child,
.table-eZ_Not_overflow .table-bordered tfoot > tr > td:last-child {
  border-right: 0;
}
.table-eZ_Not_overflow .table-bordered thead > tr:last-child > th,
.table-eZ_Not_overflow .table-bordered tbody > tr:last-child > th,
.table-eZ_Not_overflow .table-bordered tfoot > tr:last-child > th,
.table-eZ_Not_overflow .table-bordered thead > tr:last-child > td,
.table-eZ_Not_overflow .table-bordered tbody > tr:last-child > td,
.table-eZ_Not_overflow .table-bordered tfoot > tr:last-child > td {
  border-bottom: 0;
}

.table-ecool_list thead tr td,
.table-ecool_list thead tr th {
  color: #004DA0;
  font-weight: bold;
  border-top-style: none;
  background-color: #FFFFEC;
  white-space: nowrap;
  text-align: center;
}
.table-ecool_list tbody tr:nth-child(even) td,
.table-ecool_list tbody tr:nth-child(even) th {
  background-color: #FFFFEC;
  white-space: normal;
}
.table-ecool_list tbody tr:nth-child(odd) td,
.table-ecool_list tbody tr:nth-child(odd) th {
  background-color: #EBEBC8;
  white-space: normal;
}
.table-ecool_list-hover tbody tr:hover td,
.table-ecool_list-hover tbody tr:hover th {
  background-color: #f5f5f5;
}

.table-ecool_list_Notnowrap thead tr td,
.table-ecool_list_Notnowrap thead tr th {
  font-weight: bold;
  border-top-style: none;
  background-color: #FFFFEC;
}

.table-ecool-info thead tr td,
.table-ecool-info thead tr th {
  color: #005AB5;
  background-color: #d9edf7;
  border-color: #bce8f1;
  text-align: center;
  font-weight: bold;
  white-space: nowrap;
  text-align: center;
}
.table-ecool-info tbody tr:nth-child(even) td,
.table-ecool-info tbody tr:nth-child(even) th {
  color: #3a87ad;
  background-color: #F0FFFF;
  border-color: #bce8f1;
  white-space: normal;
}
.table-ecool-info tbody tr:nth-child(odd) td,
.table-ecool-info tbody tr:nth-child(odd) th {
  color: #005AB5;
  background-color: #FDFFFF;
  border-color: #bce8f1;
  white-space: normal;
}
.table-ecool-info tfoot tr td,
.table-ecool-info tfoot tr th {
  color: red;
  background-color: #FDFFFF;
  border-color: #bce8f1;
  white-space: normal;
}
.table-ecool-info-hover tbody tr:hover td,
.table-ecool-info-hover tbody tr:hover th {
  background-color: #F5F5DC;
}

.table-ecool-qa thead tr td,
.table-ecool-qa thead tr th {
  background-color: #EEEEEF;
  font-weight: bold;
  white-space: nowrap;
}
.table-ecool-qa tbody tr td,
.table-ecool-qa tbody tr th {
  white-space: pre-line;
}
.table-ecool-qa-Title {
  color: orangered;
}

.table-ecool-infoA thead tr:nth-child(odd) td,
.table-ecool-infoA thead tr:nth-child(odd) th {
  color: #F0FFFF;
  background-color: #4682b4;
  border-color: #bce8f1;
  text-align: center;
  font-weight: bold;
}
.table-ecool-infoA thead tr:nth-child(even) td,
.table-ecool-infoA thead tr:nth-child(even) th {
  color: #3a87ad;
  background-color: #d9edf7;
  border-color: #bce8f1;
  text-align: center;
  font-weight: bold;
}
.table-ecool-infoA tbody tr td,
.table-ecool-infoA tbody tr th {
  color: #3a87ad;
  background-color: #F0FFFF;
  border-color: #bce8f1;
}

.table-ecool-danger thead tr:nth-child(odd) td,
.table-ecool-danger thead tr:nth-child(odd) th {
  color: #CE0000;
  background-color: #FFE4E1;
  border-color: #FFD2D2;
  text-align: center;
  font-weight: bold;
}
.table-ecool-danger thead tr:nth-child(even) td,
.table-ecool-danger thead tr:nth-child(even) th {
  color: #CE0000;
  background-color: #FFF0F5;
  border-color: #fff;
  text-align: center;
  font-weight: bold;
}
.table-ecool-danger tbody tr td,
.table-ecool-danger tbody tr th {
  color: #CE0000;
  background-color: #fff;
  border-color: #FFD2D2;
}

.table-ecool-warning thead tr:nth-child(odd) td,
.table-ecool-warning thead tr:nth-child(odd) th {
  color: #B8860B;
  background-color: #FAFAD2;
  border-color: #EEE8AA;
  text-align: center;
  font-weight: bold;
}
.table-ecool-warning thead tr:nth-child(even) td,
.table-ecool-warning thead tr:nth-child(even) th {
  color: #B8860B;
  background-color: #FFFFE0;
  border-color: #EEE8AA;
  text-align: center;
  font-weight: bold;
}
.table-ecool-warning tbody tr td,
.table-ecool-warning tbody tr th {
  color: #B8860B;
  background-color: #fff;
  border-color: #EEE8AA;
}

.table-ecool-Bule thead tr:nth-child(odd) td,
.table-ecool-Bule thead tr:nth-child(odd) th {
  color: #F0FFFF;
  border-color: #E0FFFF;
  text-align: center;
  font-weight: bold;
  background: linear-gradient(#00BFFF, #87CEFA);
}
.table-ecool-Bule thead tr:nth-child(even) td,
.table-ecool-Bule thead tr:nth-child(even) th {
  color: #003377;
  border-color: #FFFFFF;
  text-align: center;
  font-weight: bold;
  background: linear-gradient(#B0E0E6, #FFFFFF);
}
.table-ecool-Bule tbody tr td,
.table-ecool-Bule tbody tr th {
  color: #003377;
  background-color: #FFFFFF;
  border-color: #E0FFFF;
}

.table-ecool .tr-coins td:first-child, .table-ecool .tr-mrt-br td:first-child, .table-ecool .tr-mrt-bl td:first-child, .table-ecool .tr-train td:first-child {
  font-weight: 600;
}
.table-ecool .tr-coins td:first-child::before, .table-ecool .tr-mrt-br td:first-child::before, .table-ecool .tr-mrt-bl td:first-child::before, .table-ecool .tr-train td:first-child::before {
  content: "";
  display: inline-block;
  min-width: 2.3em;
  min-height: 1em;
  margin-right: 0.5em;
  vertical-align: text-bottom;
  background-repeat: no-repeat;
  background-position: left 20% center;
  background-size: auto 1em;
  border-radius: 0.5rem;
}

.table-ecool {
  width: 100%;
  margin-bottom: 20px;
  max-width: 100%;
  background-color: transparent;
  border-collapse: collapse;
  border-spacing: 0;
  margin: 0 auto;
}
.table-ecool thead > tr > th,
.table-ecool tbody > tr > th,
.table-ecool tfoot > tr > th,
.table-ecool thead > tr > td,
.table-ecool tbody > tr > td,
.table-ecool tfoot > tr > td {
  line-height: 1.428571429;
  padding: 8px;
  vertical-align: middle;
}
@media (min-width: 767px) {
  .table-ecool thead > tr > th,
  .table-ecool tbody > tr > th,
  .table-ecool tfoot > tr > th,
  .table-ecool thead > tr > td,
  .table-ecool tbody > tr > td,
  .table-ecool tfoot > tr > td {
    line-height: 1.5;
    padding: 5px;
    vertical-align: middle;
  }
}
@media screen and (min-width: 1920px) {
  .table-ecool thead tr th,
  .table-ecool thead tr td {
    font-size: 16px;
    white-space: nowrap;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1920px) {
  .table-ecool thead tr th,
  .table-ecool thead tr td {
    font-size: 15px;
    white-space: nowrap;
  }
}
@media screen and (min-width: 767px) and (max-width: 1280px) {
  .table-ecool thead tr th,
  .table-ecool thead tr td {
    font-size: 13px;
    white-space: normal;
  }
}
@media screen and (max-width: 767px) {
  .table-ecool thead tr th,
  .table-ecool thead tr td {
    font-size: 12px;
    white-space: nowrap;
  }
}
@media print {
  .table-ecool thead tr th,
  .table-ecool thead tr td {
    font-size: 20px;
    white-space: nowrap;
  }
}
.table-ecool .tr-train td:first-child {
  background-color: #fff0f0;
  box-shadow: 0 0 3px #df9c9c;
}
.table-ecool .tr-train td:first-child::before {
  background-position: center center;
  background-image: url("data:image/svg+xml,%3Csvg enable-background='new 0 0 512 512' height='24' viewBox='0 0 512 512' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='_x32_3'%3E%3Cpath id='XMLID_214_' d='m256 32.31c85.36 0 154.53 69.2 154.53 154.53 0 64-114.82 220.49-154.53 292.07-38.07-68.7-154.53-231.83-154.53-292.07 0-85.33 69.2-154.53 154.53-154.53zm69.07 148.71c0-38.03-31.02-69.05-69.07-69.05-38.02 0-69.04 31.02-69.04 69.05 0 38.02 31.02 69.04 69.04 69.04 38.05 0 69.07-31.02 69.07-69.04z' fill='%23e74c3c'/%3E%3C/g%3E%3C/svg%3E");
}
.table-ecool .tr-mrt-bl td:first-child {
  background-color: #f0f6ff;
  box-shadow: 0 0 3px #9cb5df;
}
.table-ecool .tr-mrt-bl td:first-child::before {
  content: "BL";
  padding-left: 0.5em;
  font-size: 1em;
  font-weight: normal;
  color: #ffffff;
  background-color: #003377;
}
.table-ecool .tr-mrt-br td:first-child {
  background-color: #fffaf0;
  box-shadow: 0 0 3px #dfc29c;
}
.table-ecool .tr-mrt-br td:first-child::before {
  content: "BR";
  padding-left: 0.5em;
  font-size: 1em;
  font-weight: normal;
  color: #ffffff;
  background-color: #774900;
}
.table-ecool .tr-coins:hover > td {
  background-color: #e0e7da;
}
.table-ecool .tr-coins td:first-child {
  background-color: #fffaf0;
  box-shadow: 0 0 3px #dfc29c;
}
.table-ecool .tr-coins td:first-child::before {
  content: "";
  display: none;
}
.table-ecool .tr-coins:hover td:first-child {
  background-color: #fff5ca;
}
.table-ecool-sm {
  width: 100%;
  margin-bottom: 20px;
  max-width: 100%;
  background-color: transparent;
  border-collapse: collapse;
  border-spacing: 0;
  margin: 0 auto;
  table-layout: fixed;
  word-wrap: break-word;
}
.table-ecool-sm thead > tr > th,
.table-ecool-sm tbody > tr > th,
.table-ecool-sm tfoot > tr > th,
.table-ecool-sm thead > tr > td,
.table-ecool-sm tbody > tr > td,
.table-ecool-sm tfoot > tr > td {
  line-height: 1.428571429;
  padding: 8px;
}
@media screen and (min-width: 1920px) {
  .table-ecool-sm thead tr th,
  .table-ecool-sm thead tr td {
    font-size: 13px;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1920px) {
  .table-ecool-sm thead tr th,
  .table-ecool-sm thead tr td {
    font-size: 12px;
  }
}
@media screen and (min-width: 1920px) {
  .table-ecool-sm tbody tr td {
    font-size: 13px;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1920px) {
  .table-ecool-sm tbody tr td {
    font-size: 12px;
  }
}

.table-92Per {
  width: 92%;
}

.table-ecool-thead thead tr td,
.table-ecool-thead thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.table-ecool-List thead tr td,
.table-ecool-List thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-List tbody tr td,
.table-ecool-List tbody tr th {
  color: #333333;
  background-color: rgba(232, 209, 230, 0.2);
}

.table-ecool-reader thead tr td,
.table-ecool-reader thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-reader tbody tr td,
.table-ecool-reader tbody tr th {
  color: #333333;
  background-color: rgba(227, 236, 159, 0.2);
}

.table-ecool-rpp thead tr td,
.table-ecool-rpp thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-rpp tbody tr td,
.table-ecool-rpp tbody tr th {
  color: #333333;
  background-color: rgba(250, 233, 180, 0.2);
}

.table-ecool-rpp-sm thead tr td,
.table-ecool-rpp-sm thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-rpp-sm tbody tr td,
.table-ecool-rpp-sm tbody tr th {
  color: #333333;
  background-color: rgba(250, 233, 180, 0.2);
}

.table-ecool-ADDI06 thead tr td,
.table-ecool-ADDI06 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-ADDI06 tbody tr td,
.table-ecool-ADDI06 tbody tr th {
  color: #333333;
  background-color: rgba(226, 197, 133, 0.2);
}

.table-ecool-ADDI07 thead tr td,
.table-ecool-ADDI07 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-ADDI07 tbody tr td,
.table-ecool-ADDI07 tbody tr th {
  color: #333333;
  background-color: rgba(226, 197, 133, 0.2);
}

.table-ecool-AWA003 thead tr td,
.table-ecool-AWA003 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-AWA003 tbody tr td,
.table-ecool-AWA003 tbody tr th {
  color: #333333;
  background-color: rgba(253, 230, 128, 0.2);
}

.table-ecool-ADDO05 thead tr td,
.table-ecool-ADDO05 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-ADDO05 tbody tr td,
.table-ecool-ADDO05 tbody tr th {
  color: #333333;
  background-color: rgba(250, 206, 56, 0.2);
}

.Div-EZ-AWA004 {
  width: 90%;
  background-color: rgba(243, 200, 176, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-AWA004 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-AWA004 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-AWA004 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-AWA004 .Div-BK {
  background-color: rgba(227, 236, 159, 0.4);
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-AWA004 .form-horizontal .control-label,
.Div-EZ-AWA004 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-AWA004 .form-horizontal .control-label-D,
.Div-EZ-AWA004 .form-horizontal .control-label-left-D {
  font-size: 17px;
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-AWA004 .table-ecool thead > tr > td,
.Div-EZ-AWA004 .table-ecool thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.table-ecool-AWA004 thead tr td,
.table-ecool-AWA004 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-AWA004 tbody tr td,
.table-ecool-AWA004 tbody tr th {
  color: #333333;
  background-color: rgba(243, 200, 176, 0.2);
}

.table-ecool-AWA007 thead tr td,
.table-ecool-AWA007 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-AWA007 tbody tr td,
.table-ecool-AWA007 tbody tr th {
  color: #333333;
  background-color: rgba(225, 231, 250, 0.2);
}

.table-ecool-ZZZI04 thead tr td,
.table-ecool-ZZZI04 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-ZZZI04 tbody tr td,
.table-ecool-ZZZI04 tbody tr th {
  color: #333333;
  background-color: rgba(236, 237, 51, 0.2);
}

.table-ecool-Bule-01-SEC thead tr td,
.table-ecool-Bule-01-SEC thead tr th {
  background-color: rgba(198, 248, 245, 0.5);
  height: 30px;
  font-weight: bold;
}

.table-ecool-Bule-SEC thead tr:nth-child(odd) td,
.table-ecool-Bule-SEC thead tr:nth-child(odd) th {
  background-color: rgb(198, 248, 245);
  text-align: center;
  height: 40px;
  font-weight: bold;
}
.table-ecool-Bule-SEC thead tr:nth-child(even) td,
.table-ecool-Bule-SEC thead tr:nth-child(even) th {
  background-color: rgba(198, 248, 245, 0.5);
  text-align: center;
  height: 30px;
  font-weight: bold;
}

.table-ecool-pink-SEC thead tr:nth-child(odd) td,
.table-ecool-pink-SEC thead tr:nth-child(odd) th {
  background-color: rgb(232, 209, 230);
  text-align: center;
  height: 40px;
  font-weight: bold;
}
.table-ecool-pink-SEC thead tr:nth-child(even) td,
.table-ecool-pink-SEC thead tr:nth-child(even) th {
  background-color: rgba(232, 209, 230, 0.5);
  text-align: center;
  height: 30px;
  font-weight: bold;
}

.table-ecool-yellow-SEC thead tr:nth-child(odd) td,
.table-ecool-yellow-SEC thead tr:nth-child(odd) th {
  background-color: rgb(227, 236, 159);
  text-align: center;
  height: 40px;
  font-weight: bold;
}
.table-ecool-yellow-SEC thead tr:nth-child(even) td,
.table-ecool-yellow-SEC thead tr:nth-child(even) th {
  background-color: rgba(227, 236, 159, 0.5);
  text-align: center;
  height: 30px;
  font-weight: bold;
}

.table-ecool-Tangerine-SEC thead tr:nth-child(odd) td,
.table-ecool-Tangerine-SEC thead tr:nth-child(odd) th {
  background-color: rgb(228, 199, 130);
  text-align: center;
  height: 40px;
  font-weight: bold;
}
.table-ecool-Tangerine-SEC thead tr:nth-child(even) td,
.table-ecool-Tangerine-SEC thead tr:nth-child(even) th {
  background-color: rgba(228, 199, 130, 0.5);
  text-align: center;
  height: 30px;
  font-weight: bold;
}

.table-ecool-Tangerine2-SEC thead tr:nth-child(odd) td,
.table-ecool-Tangerine2-SEC thead tr:nth-child(odd) th {
  background-color: rgb(245, 201, 175);
  text-align: center;
  height: 40px;
  font-weight: bold;
}
.table-ecool-Tangerine2-SEC thead tr:nth-child(even) td,
.table-ecool-Tangerine2-SEC thead tr:nth-child(even) th {
  background-color: rgba(245, 201, 175, 0.5);
  text-align: center;
  height: 30px;
  font-weight: bold;
}

.table-ecool-AWA004 thead tr td,
.table-ecool-AWA004 thead tr th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}
.table-ecool-AWA004 tbody tr td,
.table-ecool-AWA004 tbody tr th {
  color: #333333;
  background-color: rgba(243, 200, 176, 0.2);
}

.panel-ez {
  margin-bottom: 20px;
  background-color: #ffffff;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.panel-ez-body {
  padding: 15px;
}
.panel-ez-body:before {
  display: table;
  content: " ";
}
.panel-ez-body:after {
  display: table;
  content: " ";
  clear: both;
}
.panel-ez-body:before {
  display: table;
  content: " ";
}
.panel-ez-body:after {
  display: table;
  content: " ";
  clear: both;
}
.panel-ez .list-group {
  margin-bottom: 0;
}
.panel-ez .list-group .list-group-item {
  border-width: 1px 0;
}
.panel-ez .list-group .list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.panel-ez .list-group .list-group-item:last-child {
  border-bottom: 0;
}
.panel-ez .table {
  margin-bottom: 0;
}
.panel-ez .panel-body + .table {
  border-top: 1px solid #dddddd;
}

.panel-danger-ez {
  border-color: #f2dede;
}
.panel-danger-ez .panel-heading {
  color: #b94a48;
  background-color: #f2dede;
  border-color: #f2dede;
}
.panel-danger-ez .panel-heading + .panel-collapse .panel-body {
  border-top-color: #eed3d7;
}
.panel-danger-ez .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #eed3d7;
}

.dropdown-menuEz {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
}
.dropdown-menuEz.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menuEz .divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.dropdown-menuEz li > a {
  display: block;
  clear: both;
  font-weight: normal;
  line-height: 1.428571429;
  color: #333333;
  white-space: nowrap;
  letter-spacing: 2px;
}
.dropdown-menuEz li > a:hover, .dropdown-menuEz li > a:focus {
  color: #000000;
  text-decoration: none;
  background-color: #fcff5a;
}
.dropdown-menuEz .active > a {
  color: #000000;
  text-decoration: none;
  background-color: #FFFFBF;
  outline: 0;
}
.dropdown-menuEz .active > a:hover, .dropdown-menuEz .active > a:focus {
  color: #000000;
  text-decoration: none;
  background-color: #FFFFBF;
  outline: 0;
}
.dropdown-menuEz .disabled > a {
  color: #999999;
}
.dropdown-menuEz .disabled > a:hover, .dropdown-menuEz .disabled > a:focus {
  color: #999999;
}
.dropdown-menuEz .disabled > a:hover, .dropdown-menuEz .disabled > a:focus {
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}

.open > .dropdown-menuEz {
  display: block;
}

.pull-right > .dropdown-menuEz {
  right: 0;
  left: auto;
}

.dropdown-menuEz {
  top: auto;
  bottom: 100%;
  margin-bottom: 1px;
}

@media (min-width: 767px) {
  .dropdown-menuEz {
    right: 0;
    left: auto;
  }
}
.dropdown-menuEz {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menuEz {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }
  .navbar-nav .open .dropdown-menuEz li > a,
  .navbar-nav .open .dropdown-menuEz .dropdown-header {
    padding: 5px 0px 5px 55px;
  }
  .navbar-nav .open .dropdown-menuEz li > a {
    line-height: 20px;
  }
  .navbar-nav .open .dropdown-menuEz li > a:hover, .navbar-nav .open .dropdown-menuEz li > a:focus {
    background-image: none;
  }
}
.button_groupEz_pink {
  border: 1px solid #FEE8E9;
  letter-spacing: 1.4px;
  font-weight: bold;
  color: #000000;
  background-color: #FFFFFF;
  min-width: 124px;
  min-height: 29px;
  margin-right: 20px;
}
.button_groupEz_pink:hover {
  border: 1px solid #FEE8E9;
  background-color: #FEE8E9;
}

.btn-bold {
  font-weight: bold;
}

.btn-pink {
  color: #000000;
  background-color: #FFFFFF;
  margin-bottom: 3px;
}
.btn-pink:hover, .btn-pink:focus, .btn-pink:active, .btn-pink.active {
  color: #000000;
  background-color: #FEE8E9;
  border-color: #FEE8E9;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

@media print {
  .btn-pink {
    letter-spacing: 10px;
    border: 1.5px solid #FEE8E9;
  }
}
@media screen and (min-width: 1920px) {
  .btn-pink {
    letter-spacing: 2px;
    border: 1.5px solid #FEE8E9;
  }
}
@media screen and (min-width: 1440px) and (max-width: 1920px) {
  .btn-pink {
    letter-spacing: 1.4px;
    border: 1.5px solid #FEE8E9;
  }
}
@media screen and (max-width: 1439px) {
  .btn-pink {
    border: 1.3px solid #FEE8E9;
  }
}
.open .dropdown-toggle.btn-pink {
  color: #000000;
  background-color: #FEE8E9;
  border-color: #FEE8E9;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-pink:active, .btn-pink.active {
  background-image: none;
}

.open .dropdown-toggle.btn-pink {
  background-image: none;
}

.btn-pink.disabled, .btn-pink[disabled] {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-pink {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-pink.disabled:hover, .btn-pink[disabled]:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-pink:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-pink.disabled:focus, .btn-pink[disabled]:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-pink:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-pink.disabled:active, .btn-pink[disabled]:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-pink:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-pink.disabled.active, .btn-pink[disabled].active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-pink.active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

/*btn-Basic*/
.btn-Basic {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #F2F2F2;
  margin-bottom: 3px;
}
.btn-Basic:hover, .btn-Basic:focus, .btn-Basic:active, .btn-Basic.active {
  color: #000000;
  background-color: #F2F2F2;
  border-color: #FFFFFF;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

@media print {
  .btn-Basic {
    letter-spacing: 10px;
    border: 1.5px solid #F2F2F2;
  }
}
@media screen and (min-width: 1920px) {
  .btn-Basic {
    letter-spacing: 2px;
    border: 1.5px solid #F2F2F2;
  }
}
@media screen and (min-width: 1440px) and (max-width: 1920px) {
  .btn-Basic {
    letter-spacing: 1.4px;
    border: 1.5px solid #F2F2F2;
  }
}
@media screen and (max-width: 1439px) {
  .btn-Basic {
    border: 1.3px solid #F2F2F2;
  }
}
.open .dropdown-toggle.btn-Basic {
  color: #000000;
  background-color: #F2F2F2;
  border-color: #FFFFFF;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-Basic:active, .btn-Basic.active {
  background-image: none;
}

.open .dropdown-toggle.btn-Basic {
  background-image: none;
}

.btn-Basic.disabled, .btn-Basic[disabled] {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-Basic {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-Basic.disabled:hover, .btn-Basic[disabled]:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-Basic:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-Basic.disabled:focus, .btn-Basic[disabled]:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-Basic:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-Basic.disabled:active, .btn-Basic[disabled]:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-Basic:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-Basic.disabled.active, .btn-Basic[disabled].active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-Basic.active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

/*btn-yellow*/
.btn-yellow {
  color: #000000;
  background-color: #F8F3A5;
  margin-bottom: 3px;
  margin-top: 3px;
}
.btn-yellow:hover, .btn-yellow:focus, .btn-yellow:active, .btn-yellow.active {
  color: #000000;
  background-color: #FFD700;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.open .dropdown-toggle.btn-yellow {
  color: #000000;
  background-color: #FFD700;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-yellow:active, .btn-yellow.active {
  background-image: none;
}

.open .dropdown-toggle.btn-yellow {
  background-image: none;
}

.btn-yellow.disabled, .btn-yellow[disabled] {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-yellow {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-yellow.disabled:hover, .btn-yellow[disabled]:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-yellow:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-yellow.disabled:focus, .btn-yellow[disabled]:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-yellow:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-yellow.disabled:active, .btn-yellow[disabled]:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-yellow:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-yellow.disabled.active, .btn-yellow[disabled].active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-yellow.active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-link-ez {
  font-weight: normal;
  color: #428bca;
  cursor: pointer;
  border-radius: 0;
  background-color: transparent;
  box-shadow: none;
}
.btn-link-ez:active, .btn-link-ez[disabled] {
  background-color: transparent;
  box-shadow: none;
}

fieldset[disabled] .btn-link {
  background-color: transparent;
  box-shadow: none;
}

.btn-link-ez {
  border-color: transparent;
}
.btn-link-ez:hover, .btn-link-ez:focus, .btn-link-ez:active {
  border-color: transparent;
}
.btn-link-ez:hover, .btn-link-ez:focus {
  color: #2a6496;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link-ez[disabled]:hover {
  color: #999999;
  text-decoration: none;
}

fieldset[disabled] .btn-link-ez:hover,
.btn-link-ez[disabled]:focus,
fieldset[disabled] .btn-link-ez:focus {
  color: #999999;
  text-decoration: none;
}

.btn-table-link {
  font-weight: normal;
  color: #004da0;
  cursor: pointer;
  border-radius: 0;
  background-color: transparent;
  box-shadow: none;
}
.btn-table-link:active, .btn-table-link[disabled] {
  background-color: transparent;
  box-shadow: none;
}

fieldset[disabled] .btn-link {
  background-color: transparent;
  box-shadow: none;
}

.btn-table-link {
  border-color: transparent;
}
.btn-table-link:hover, .btn-table-link:focus, .btn-table-link:active {
  border-color: transparent;
}
.btn-table-link:hover, .btn-table-link:focus {
  color: #2a6496;
  text-decoration: underline;
  background-color: transparent;
}
.btn-table-link[disabled]:hover {
  color: #999999;
  text-decoration: none;
}

fieldset[disabled] .btn-link:hover,
.btn-table-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
  color: #999999;
  text-decoration: none;
}

.btn-social-lg {
  position: relative;
  padding-left: 55px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.btn-social-lg > :first-child {
  position: absolute;
  left: 2px;
  top: 2px;
  bottom: 0;
  width: 40px;
  line-height: 46px;
  font-size: 1.6em;
  text-align: center;
  /*border-right: 1px solid rgba(0,0,0,0.2);*/
}

.btn-dropbox {
  color: #fff;
  background-color: #009eaf;
  border-color: rgba(0, 0, 0, 0.2);
}

.btn-social {
  position: relative;
  padding-left: 44px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.btn-social > :first-child {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 32px;
  line-height: 34px;
  font-size: 1.6em;
  text-align: center;
}

.btn-social-lg {
  position: relative;
  padding-left: 5px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.btn-social-lg > :first-child {
  position: absolute;
  left: 2px;
  top: 2px;
  bottom: 0;
  width: 40px;
  line-height: 46px;
  font-size: 1.6em;
  text-align: center;
}

.btn-sys {
  border-color: #FAE20A;
  border-width: 1px 1px 5px 1px;
  border-style: solid;
  border-radius: 8px;
  font-size: 12px;
  padding: 2px 6px 2px 6px;
  text-decoration: none;
  display: inline-block;
  font-weight: bold;
  color: #242424;
  background-color: #F9F9F9;
  background-image: linear-gradient(to bottom, #F9F9F9, #FAF219);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#FAF219);
  margin-bottom: 15px;
  margin-right: 2px;
}
.btn-sys:hover, .btn-sys:focus, .btn-sys:active, .btn-sys.active {
  border-color: #FAE319;
  border-width: 1px 1px 5px 1px;
  border-style: solid;
  background-color: #F9F9F9;
  background-image: linear-gradient(to bottom, #F9F9F9, #FAD312);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#FAD312);
}

.open .dropdown-toggle.btn-sys {
  border-color: #FAE319;
  border-width: 1px 1px 5px 1px;
  border-style: solid;
  background-color: #F9F9F9;
  background-image: linear-gradient(to bottom, #F9F9F9, #FAD312);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#FAD312);
}

.btn-sys:active, .btn-sys.active {
  background-image: none;
}

.open .dropdown-toggle.btn-sys {
  background-image: none;
}

.btn-sys.disabled, .btn-sys[disabled] {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys.disabled:hover, .btn-sys[disabled]:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys.disabled:focus, .btn-sys[disabled]:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys.disabled:active, .btn-sys[disabled]:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys.disabled.active, .btn-sys[disabled].active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys.active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

@media screen and (max-width: 767px) {
  .btn-sys {
    font-size: 10px;
  }
}
.btn-sys-busker {
  border-color: #84c1ff;
  border-width: 1px 1px 5px 1px;
  border-style: solid;
  border-radius: 8px;
  font-size: 12px;
  padding: 2px 6px 2px 6px;
  text-decoration: none;
  display: inline-block;
  font-weight: bold;
  color: #242424;
  background-color: #F9F9F9;
  background-image: linear-gradient(to bottom, #F9F9F9, #c4e1ff);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#c4e1ff);
  margin-bottom: 15px;
  margin-right: 2px;
}
.btn-sys-busker:hover, .btn-sys-busker:focus, .btn-sys-busker:active, .btn-sys-busker.active {
  border-color: #84c1ff;
  border-width: 1px 1px 5px 1px;
  border-style: solid;
  background-color: #F9F9F9;
  background-image: linear-gradient(to bottom, #F9F9F9, #005ab5);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#005ab5);
}

.open .dropdown-toggle.btn-sys-busker {
  border-color: #84c1ff;
  border-width: 1px 1px 5px 1px;
  border-style: solid;
  background-color: #F9F9F9;
  background-image: linear-gradient(to bottom, #F9F9F9, #005ab5);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#F9F9F9, endColorstr=#005ab5);
}

.btn-sys-busker:active, .btn-sys-busker.active {
  background-image: none;
}

.open .dropdown-toggle.btn-sys-busker {
  background-image: none;
}

.btn-sys-busker.disabled, .btn-sys-busker[disabled] {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys-busker {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys-busker.disabled:hover, .btn-sys-busker[disabled]:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys-busker:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys-busker.disabled:focus, .btn-sys-busker[disabled]:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys-busker:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys-busker.disabled:active, .btn-sys-busker[disabled]:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

.btn-sys-busker.disabled.active, .btn-sys-busker[disabled].active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

fieldset[disabled] .btn-sys-busker.active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #9D9D9D;
}

@media screen and (max-width: 767px) {
  .btn-sys-busker {
    font-size: 10px;
  }
}
.btn:focus {
  outline: 3px solid #333;
}

.btn-sys-style {
  padding: 2px 6px 2px 6px;
  border-width: 1px 1px 5px 1px;
  border-style: solid;
  border-radius: 8px;
  font-weight: 600;
  color: #000;
  box-shadow: inset 0 20px 21px rgba(255, 255, 255, 0.9);
}
.btn-sys-style:hover, .btn-sys-style:focus {
  color: #000;
  box-shadow: inset 0 18px 12px rgba(255, 255, 255, 0.7);
}
.btn-sys-style.active {
  color: #000;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2), inset 0 0 0 100px #fff;
}

.btn-sys-margin {
  margin-bottom: 15px;
  margin-right: 2px;
}

.font_Last_Day_MEMO {
  line-height: 24px;
}

.font_Menu {
  line-height: 22px;
  color: #000000;
  background-size: 100% 100%;
  /*for IE*/
  max-width: 100%;
  width: auto\9 ;
  /* IE6, IE7, IE8, IE9 */
  width: 100%;
}
.font_Menu > a {
  position: relative;
  z-index: 1;
}
.font_Menu .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100%;
}
.font_Menu .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) .btn-default {
  margin: 0 0 2px 0;
}

.td_Menu_red {
  line-height: 25px;
  color: #000000;
  background-color: rgba(247, 201, 201, 0.4);
  text-align: center;
  max-width: 155px;
  width: 85%;
}

.td_Menu_Coo {
  line-height: 25px;
  color: #000000;
  background-color: rgba(234, 220, 190, 0.4);
  text-align: center;
  max-width: 155px;
  width: 85%;
}

.td_Menu_blue {
  line-height: 25px;
  color: #000000;
  background-color: rgba(226, 225, 254, 0.4);
  text-align: center;
  max-width: 155px;
  width: 85%;
}

.td_Menu_green {
  line-height: 25px;
  color: #000000;
  background-color: rgba(237, 245, 225, 0.4);
  text-align: center;
  max-width: 155px;
  width: 85%;
}

.font_NAME {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.font_Cash {
  font-size: 180%;
  color: #000000;
  vertical-align: bottom;
}

.layout_font_title {
  font-size: 20px;
  font-weight: bold;
  color: #004aa2;
  text-align: center;
}

/*認證徵章*/
@media screen and (min-width: 1500px) {
  .imgEz_Badge {
    width: auto;
    height: auto;
    max-height: 50px;
  }
}
@media screen and (max-width: 1500px) {
  .imgEz_Badge {
    width: auto;
    height: auto;
    max-height: 32px;
  }
}
@media screen and (max-width: 1194px) {
  .imgEz_Badge {
    width: auto;
    height: auto;
    max-height: 29px;
  }
}
@media screen and (max-width: 1024px) {
  .imgEz_Badge {
    width: auto;
    height: auto;
    max-height: 28px;
  }
}
@media screen and (max-width: 767px) {
  .imgEz_Badge {
    width: auto;
    height: auto;
    max-height: 100%;
    max-width: 100%;
  }
}
@media screen and (max-width: 480px) {
  .imgEz_Badge {
    width: auto;
    height: auto;
    max-height: 55px;
  }
}
/*護照徵章*/
@media screen and (min-width: 1501px) {
  .imgEz_BadgeBook {
    width: auto;
    height: auto;
    max-height: 45px;
  }
}
@media screen and (max-width: 1500px) {
  .imgEz_BadgeBook {
    width: auto;
    height: auto;
    max-height: 38px;
  }
}
@media screen and (max-width: 1194px) {
  .imgEz_BadgeBook {
    width: auto;
    height: auto;
    max-height: 32px;
  }
}
@media screen and (max-width: 1024px) {
  .imgEz_BadgeBook {
    width: auto;
    height: auto;
    max-height: 30px;
  }
}
@media screen and (max-width: 767px) {
  .imgEz_BadgeBook {
    width: auto;
    height: auto;
    max-height: 100%;
    max-width: 100%;
  }
}
@media screen and (max-width: 480px) {
  .imgEz_BadgeBook {
    width: auto;
    height: auto;
    max-height: 45px;
  }
}
.imgfooter {
  display: block;
  width: 100%;
  height: auto;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  margin: 0px auto;
  text-align: center;
}

@media screen and (min-width: 1500px) {
  .imgfooter {
    padding-top: 5px;
    font-size: 16px;
    min-height: 34px;
    max-width: 905px;
  }
  .imgfooter > font {
    font-size: 16px;
  }
}
@media screen and (max-width: 1500px) {
  .imgfooter {
    padding-top: 2px;
    font-size: 13px;
    min-height: 25px;
    max-width: 905px;
  }
  .imgfooter > font {
    font-size: 13px;
  }
}
@media screen and (max-width: 480px) {
  .imgfooter {
    font-size: 10px;
    max-width: 480px;
  }
  .imgfooter > font {
    font-size: 10px;
  }
}
@media screen and (max-width: 360px) {
  .imgfooter {
    font-size: 9px;
    max-width: 320px;
  }
  .imgfooter > font {
    font-size: 9px;
  }
}
@media screen and (max-width: 767px) {
  .Div-Menu {
    background-color: #E6EB42;
    top: 50px;
    left: 0;
    position: fixed;
    z-index: 2;
  }
  .Top_footer {
    position: fixed;
    bottom: 0;
    right: 10px;
    height: 58px;
  }
}
@media screen and (max-width: 321px) {
  .Div-Menu {
    top: 50px;
    position: fixed;
    z-index: 2;
  }
  .Top_footer {
    position: fixed;
    bottom: 0;
    right: 10px;
    height: 58px;
  }
}
.containerEZ {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media all and (min-width: 1200px) {
  .containerEZ {
    max-width: 1190px;
  }
}
.containerEZ::before, .containerEZ::after {
  display: table;
  content: " ";
}

@media all and (max-width: 767px) {
  body > .containerEZ > .row {
    margin-left: 0;
    margin-right: 0;
  }
}

.containerEZ-fluid {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
.containerEZ-fluid::before, .containerEZ-fluid::after {
  display: table;
  content: " ";
}

.containerEZ > .navbar-header,
.containerEZ-fluid > .navbar-header,
.containerEZ > .navbar-collapse,
.containerEZ-fluid > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media all and (min-width: 767px) {
  .containerEZ > .navbar-header,
  .containerEZ-fluid > .navbar-header,
  .containerEZ > .navbar-collapse,
  .containerEZ-fluid > .navbar-collapse {
    margin-right: 0px;
    margin-left: 0px;
  }
}

.containerEZ .jumbotron,
.containerEZ-fluid .jumbotron {
  padding-right: 60px;
  padding-left: 60px;
  border-radius: 6px;
}

@media (min-width: 767px) {
  .navbar-ECOOL {
    display: none !important;
  }
}
/*小於767px SHow*/
@media (max-width: 767px) {
  .Div-navbar {
    display: block;
    display: none\9 ;
  }
}
/*大於768px hide*/
@media (min-width: 767px) {
  .Div-navbar {
    display: none;
  }
}
.navbar-brandEZ:hover, .navbar-brandEZ:focus {
  text-decoration: none;
}

@media (max-width: 767px) {
  .navbar > .container .navbar-brandEZ {
    margin-left: -15px;
  }
}
.vr_pagebreak {
  page-break-before: always;
}

.paginationW {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px;
}
.paginationW li {
  display: inline;
}
.paginationW li a,
.paginationW li span {
  position: relative;
  float: left;
  padding: 6px 12px;
  margin-left: -1px;
  line-height: 1.428571429;
  text-decoration: none;
  cursor: pointer;
}
.paginationW li:first-child > a,
.paginationW li:first-child > span {
  margin-left: 0;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}
.paginationW li:last-child > a,
.paginationW li:last-child > span {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.paginationW li a:hover,
.paginationW li span:hover,
.paginationW li a:focus,
.paginationW li span:focus {
  color: #004da0;
}
.paginationW .active a,
.paginationW .active span,
.paginationW .active a:hover,
.paginationW .active span:hover,
.paginationW .active a:focus,
.paginationW .active span:focus {
  z-index: 2;
  color: #333333;
  cursor: default;
  border-color: #333333;
  border: 1px solid;
}
.paginationW .disabled span {
  color: #999999;
  cursor: not-allowed;
  border-color: #dddddd;
}
.paginationW .disabled a {
  color: #999999;
  cursor: not-allowed;
  border-color: #dddddd;
}
.paginationW .disabled a:hover, .paginationW .disabled a:focus {
  color: #999999;
  cursor: not-allowed;
  border-color: #dddddd;
}

.input-xs {
  height: 25px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

select.input-xs {
  height: 25px;
  line-height: 25px;
}

textarea.input-xs {
  height: auto;
}

.lobinLabel {
  color: white;
}
@media (min-width: 992px) {
  .lobinLabel {
    font-size: 15px;
    margin-right: 5px;
  }
}

.LoginBox {
  max-width: 95px;
  overflow: hidden;
  width: auto\9 ;
  /* IE6, IE7, IE8, IE9 */
  width: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.table_Menu {
  max-width: 192px;
  width: auto\9 ;
  /* IE6, IE7, IE8, IE9 */
  width: 100%;
}

.dl-horizontal-EZ dt {
  float: left;
  overflow: hidden;
  clear: left;
  text-align: right;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 6px;
  color: #004da0;
}
.dl-horizontal-EZ dd:before {
  display: table;
  content: " ";
}
.dl-horizontal-EZ dd:after {
  display: table;
  content: " ";
  clear: both;
}
.dl-horizontal-EZ dd:before {
  display: table;
  content: " ";
}
.dl-horizontal-EZ dd:after {
  display: table;
  content: " ";
  clear: both;
}

.dt {
  font-weight: bold;
  color: #004da0;
  margin-left: 10px;
}

.dd {
  font-weight: bold;
}

@media print {
  .col-md-5 col-sm-6 dl-horizontal-EZ dd {
    background-color: black;
  }
  .col-md-5 col-sm-6 dl-horizontal-EZ dt {
    font-size: 3cm;
  }
  .col-md-12 col-sm-12 dl-h orizontal-EZ dd,
  .col-md-12 col-sm-12 dl-horizontal-EZ dt {
    font-size: 2cm;
  }
}
@media (min-width: 1920px) {
  .dt,
  .dd {
    font-size: 16px;
  }
}
@media (max-width: 1920px) {
  .dt,
  .dd {
    font-size: 15px;
  }
}
@media (max-width: 1366px) {
  .dt,
  .dd {
    font-size: 14px;
  }
}
@media (max-width: 1024px) {
  .dt,
  .dd {
    font-size: 13px;
  }
}
@media (max-width: 767px) {
  .dt,
  .dd {
    font-size: 12px;
  }
}
.p-context {
  word-wrap: break-word;
  word-break: normal;
  text-align: left;
  width: 95%;
  margin: 0px auto;
  overflow: hidden;
}
.p-context img {
  height: auto;
  max-width: 100%;
}

@media (min-width: 1366px) {
  .p-context {
    font-size: 14px;
    line-height: 24px;
  }
}
@media (max-width: 1366px) {
  .p-context {
    font-size: 14px;
    line-height: 20px;
  }
}
@media (max-width: 1024px) {
  .p-context {
    font-size: 13px;
    line-height: normal;
  }
}
@media (max-width: 767px) {
  .p-context {
    font-size: 12px;
    line-height: normal;
  }
}
.label_dt_font16 {
  font-size: 16px;
  font-weight: bold;
  color: #004da0;
}

.label_dt {
  font-weight: bold;
  color: #004da0;
}

.label_dt_S {
  font-size: 14px;
  font-weight: bold;
  color: #004da0;
}

.label_dd_font18 {
  font-size: 16px;
  font-weight: bold;
  color: #004da0;
  line-height: 24px;
  letter-spacing: 1.5px;
}

.label_dd {
  font-weight: bold;
  color: #333333;
}

@media (min-width: 1920px) {
  .label_dt,
  .label_dd {
    font-size: 16px;
  }
}
@media (max-width: 1920px) {
  .label_dt,
  .label_dd {
    font-size: 15px;
  }
}
@media (max-width: 1366px) {
  .label_dt,
  .label_dd {
    font-size: 14px;
  }
}
@media (max-width: 1024px) {
  .label_dt,
  .label_dd {
    font-size: 13px;
  }
}
@media (max-width: 767px) {
  .label_dt,
  .label_dd {
    font-size: 12px;
  }
}
.lnkFont {
  font-weight: bold;
  font-size: 16px;
}

.lnkFont2 {
  text-align: left;
  display: inline-block;
  width: 50px;
  font-weight: bold;
  font-size: 15pt;
  color: #0061CA;
}

.Div-EZ-task {
  width: 90%;
  background-color: rgba(173, 251, 242, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-task .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-task .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-task .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-task .Div-Feedback {
  background-color: rgba(232, 209, 230, 0.4);
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-task .form-horizontal .control-label,
.Div-EZ-task .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-Pink {
  width: 90%;
  background-color: rgba(232, 209, 230, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-Pink .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-Pink .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-Pink .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-Pink .Div-Feedback {
  background-color: rgba(232, 209, 230, 0.4);
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-Pink .form-horizontal .control-label,
.Div-EZ-Pink .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-Pink .form-horizontal .control-label-D,
.Div-EZ-Pink .form-horizontal .control-label-left-D {
  font-size: 17px;
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-reader {
  width: 90%;
  background-color: rgba(227, 236, 159, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-reader .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-reader .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-reader .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-reader .Div-BK {
  background-color: rgba(227, 236, 159, 0.4);
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-reader .form-horizontal .control-label,
.Div-EZ-reader .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-reader .form-horizontal .control-label-D,
.Div-EZ-reader .form-horizontal .control-label-left-D {
  font-size: 17px;
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-reader .table-ecool > thead > tr > td,
.Div-EZ-reader .table-ecool > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.Div-EZ-ArtGallery {
  width: 90%;
  margin: 0px auto;
  margin-top: -40px;
}
.Div-EZ-ArtGallery .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ArtGallery .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ArtGallery .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ArtGallery .Div-BK {
  background-color: rgba(227, 236, 159, 0.4);
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ArtGallery .form-horizontal .control-label,
.Div-EZ-ArtGallery .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-ArtGallery .form-horizontal .control-label-D,
.Div-EZ-ArtGallery .form-horizontal .control-label-left-D {
  font-size: 17px;
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-ArtGallery .table-ecool > thead > tr > td,
.Div-EZ-ArtGallery .table-ecool > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.Div-EZ-ADDI06 {
  width: 90%;
  background-color: rgba(226, 197, 133, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-ADDI06 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI06 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ADDI06 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI06 .form-horizontal .control-label,
.Div-EZ-ADDI06 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-ADDI07 {
  width: 90%;
  background-color: rgba(226, 197, 133, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-ADDI07 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI07 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ADDI07 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI07 .form-horizontal .control-label,
.Div-EZ-ADDI07 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-ADDI05 {
  width: 90%;
  background-color: rgba(250, 206, 56, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-ADDI05 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI05 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ADDI05 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI05 .form-horizontal .control-label,
.Div-EZ-ADDI05 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-ADDI05 .table-ecool > thead > tr > td,
.Div-EZ-ADDI05 .table-ecool > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.Div-EZ-Awat {
  width: 90%;
  background-color: rgba(243, 200, 176, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-Awat .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-Awat .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-Awat .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-Awat .form-horizontal .control-label,
.Div-EZ-Awat .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-Awat02 {
  width: 90%;
  background-color: rgba(243, 200, 176, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-Awat02 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-Awat02 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-Awat02 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-Awat02 .form-horizontal .control-label,
.Div-EZ-Awat02 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-AWAI02 {
  width: 90%;
  background-color: rgba(225, 231, 250, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-AWAI02 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-AWAI02 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-AWAI02 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-AWAI02 .form-horizontal .control-label,
.Div-EZ-AWAI02 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-ZZZI04 {
  width: 90%;
  background-color: rgba(236, 237, 51, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-ZZZI04 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ZZZI04 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ZZZI04 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ZZZI04 .form-horizontal .control-label,
.Div-EZ-ZZZI04 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-purpose {
  width: 90%;
  background-color: rgba(204, 255, 255, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-purpose .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-purpose .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-purpose .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-purpose .form-horizontal .control-label,
.Div-EZ-purpose .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-rpp {
  width: 90%;
  background-color: rgba(250, 233, 180, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-rpp .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-rpp .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-rpp .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-rpp .form-horizontal .control-label,
.Div-EZ-rpp .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}

.Div-EZ-ADDI09 {
  width: 90%;
  background-color: rgba(226, 197, 133, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-ADDI09 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI09 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ADDI09 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ADDI09 .form-horizontal .control-label,
.Div-EZ-ADDI09 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-ADDI09 .table-ecool > thead > tr > td,
.Div-EZ-ADDI09 .table-ecool > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.Div-EZ-SECI01 {
  width: 90%;
  background-color: rgba(254, 225, 233, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-SECI01 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-SECI01 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-SECI01 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-SECI01 .form-horizontal .control-label,
.Div-EZ-SECI01 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-SECI01 .table-ecool > thead > tr > td,
.Div-EZ-SECI01 .table-ecool > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.Div-EZ-ZZZI26 {
  width: 90%;
  background-color: rgba(226, 234, 162, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-ZZZI26 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ZZZI26 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ZZZI26 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ZZZI26 .form-horizontal .control-label,
.Div-EZ-ZZZI26 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-ZZZI26 .table-ecool > thead > tr > td,
.Div-EZ-ZZZI26 .table-ecool > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

.Div-EZ-ZZZI09 {
  width: 90%;
  background-color: rgba(239, 171, 184, 0.2);
  margin: 0px auto;
  margin-top: -15px;
}
.Div-EZ-ZZZI09 .form-horizontal {
  width: 90%;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ZZZI09 .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.Div-EZ-ZZZI09 .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 5px;
}
.Div-EZ-ZZZI09 .form-horizontal .control-label,
.Div-EZ-ZZZI09 .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.Div-EZ-ZZZI09 .table-ecool > thead > tr > td,
.Div-EZ-ZZZI09 .table-ecool > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
}

@media (min-width: 767px) {
  .form-horizontal .control-label-left {
    text-align: left\9 ;
  }
}
.imgTitle {
  display: block;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  margin: 0px auto;
  text-align: center;
  min-height: 68px;
}

/* for  IE8 ~ IE10 */
@media screen\0  {
  .imgTitle {
    padding-top: 2px\9 ;
    font-size: 13px\9 ;
  }
}
@media screen and (min-width: 1500px) {
  .imgTitle {
    font-size: 16px;
  }
}
@media screen and (max-width: 1500px) {
  .imgTitle {
    padding-top: 2px;
    font-size: 13px;
  }
}
@media screen and (max-width: 480px) {
  .imgTitle {
    font-size: 10px;
  }
}
.imgTitleFont {
  color: #0b24fa;
  font-weight: bold;
  letter-spacing: 14px;
}

/* for  IE8 ~ IE10 */
@media screen\0  {
  .imgTitleFont {
    margin-top: 37px\9 ;
    font-size: 14px\9 ;
  }
}
@media screen and (min-width: 480px) {
  .imgTitleFont {
    margin-top: 37px;
    font-size: 14px;
  }
}
@media screen and (max-width: 480px) {
  .imgTitleFont {
    margin-top: 40px;
    font-size: 12px;
  }
}
.Title_Secondary {
  font-size: 16px;
  border-bottom: 1px solid #ccc;
  letter-spacing: 1px;
}

.Caption_Div_Left {
  font-size: 16px;
  font-weight: bold;
  color: #3293fb;
  margin: 5px 5px 2px 25px;
  text-align: left;
}

.Caption_Div {
  font-size: 16px;
  font-weight: bold;
  color: #3293fb;
  margin: 5px 5px 2px 25px;
}

@media screen and (max-width: 767px) {
  .Caption_Div_Left,
  .Caption_Div {
    font-size: 14px;
  }
}
.panel-Remove {
  background-color: transparent;
}

.panel-Img {
  background-color: rgba(226, 225, 254, 0.2);
}
.panel-Img .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.panel-Img > .panel-heading {
  margin-top: 0;
  margin-bottom: 0;
  letter-spacing: 16px;
  padding: 2px 2px;
  color: #0b24fa;
  background-color: rgb(226, 225, 254);
  border-color: #c6c4e3;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none;
  text-shadow: 2px 2px 2px #DDDDDD;
}
.panel-Img > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #c6c4e3;
}
.panel-Img > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #bce8f1;
}

.panel-ZZZ {
  background-color: rgba(226, 225, 254, 0.2);
}

.panel-ZZZ-Color2 {
  background-color: rgba(226, 225, 254, 0.4);
}

.panel-ZZZ-Color3 {
  background-color: rgba(226, 225, 254, 0.6);
}

.panel-ZZZ-Color4 {
  background-color: rgba(226, 225, 254, 0.8);
}

.panel-ZZZ-Color5 {
  background-color: rgb(226, 225, 254);
}

.panel-ZZZ .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.panel-ZZZ .panel-heading {
  margin-top: 0;
  margin-bottom: 0;
  letter-spacing: 16px;
  padding: 7px 7px;
  color: #0b24fa;
  background-color: rgb(226, 225, 254);
  border-color: #c6c4e3;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none;
  text-shadow: 2px 2px 2px #DDDDDD;
}
.panel-ZZZ .panel-heading + .panel-collapse .panel-body {
  border-top-color: #c6c4e3;
}
.panel-ZZZ .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #bce8f1;
}
.panel-ZZZ .form-horizontal .control-label,
.panel-ZZZ .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.panel-ZZZ .form-horizontal .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 10px;
}
.panel-ZZZ .table-ecool-ZZZ thead tr td,
.panel-ZZZ .table-ecool-ZZZ thead tr th {
  font-weight: bold;
  text-align: center;
}
.panel-ZZZ .table-ecool-ZZZ tfoot th:before,
.panel-ZZZ .table-ecool-ZZZ tfoot td:before {
  color: #220088;
  margin-right: 10px;
}
.panel-ZZZ .table-ecool-ZZZ tbody td:before {
  color: #220088;
  margin-right: 10px;
}

@media (max-width: 767px) {
  .panel-ZZZ .table-ecool-ZZZ {
    border-collapse: separate;
    border-spacing: 0px 2px;
  }
  .panel-ZZZ .table-ecool-ZZZ thead tr td,
  .panel-ZZZ .table-ecool-ZZZ thead tr th {
    text-align: left;
  }
  .panel-ZZZ .table-ecool-ZZZ tr:nth-child(odd) {
    background-color: #FAF2FF;
  }
}
.panel-ACC {
  background-color: rgba(237, 245, 225, 0.2);
}

.panel-ACC-Color2 {
  background-color: rgba(237, 245, 225, 0.4);
}

.panel-ACC-Color3 {
  background-color: rgba(237, 245, 225, 0.6);
}

.panel-ACC-Color4 {
  background-color: rgba(237, 245, 225, 0.8);
}

.panel-ACC-Color5 {
  background-color: rgb(237, 245, 225);
}

.panel-ACC .Details {
  margin: 0px auto;
  padding-top: 20px;
}
.panel-ACC .panel-heading {
  margin-top: 0;
  margin-bottom: 0;
  letter-spacing: 16px;
  padding: 7px 7px;
  color: #0b24fa;
  background-color: rgb(237, 245, 225);
  border-color: #c6c4e3;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none;
  text-shadow: 2px 2px 2px #DDDDDD;
}
.panel-ACC .panel-Group {
  margin-top: 0;
  margin-bottom: 0;
  letter-spacing: 16px;
  padding: 7px 7px;
  color: #004da0;
  background-color: rgb(237, 245, 225);
  border-color: #c6c4e3;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none;
  border-bottom: 1px solid #DDDDDD;
}
.panel-ACC .panel-heading + .panel-collapse .panel-body {
  border-top-color: #c6c4e3;
}
.panel-ACC .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #bce8f1;
}
.panel-ACC .form-horizontal .control-label,
.panel-ACC .form-horizontal .control-label-left {
  font-weight: bold;
  color: #004da0;
}
.panel-ACC .form-horizontal .Div-btn-center {
  text-align: center;
  margin: 0px auto;
  padding-top: 10px;
}
.panel-ACC .table-ecool-ACC > thead > tr > td,
.panel-ACC .table-ecool-ACC > thead > tr > th {
  font-weight: bold;
  text-align: center;
  color: #004da0;
  text-overflow: ellipsis;
}

.Group {
  font-weight: bold;
  color: #004da0;
  background-color: #ffffff;
  border-bottom: 1px solid #edf5e1;
}

.btn-SchoolList {
  color: #000000;
  background-color: #FFFFFF;
  margin-bottom: 3px;
  letter-spacing: 2px;
  border: 1.5px solid #c3e4f6;
  font-weight: bold;
}
.btn-SchoolList:hover, .btn-SchoolList:focus, .btn-SchoolList:active, .btn-SchoolList.active {
  color: #000000;
  background-color: #c3e4f6;
  border-color: #c3e4f6;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.open .dropdown-toggle.btn-SchoolList {
  color: #000000;
  background-color: #c3e4f6;
  border-color: #c3e4f6;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.btn-SchoolList:active, .btn-SchoolList.active {
  background-image: none;
}

.open .dropdown-toggle.btn-SchoolList {
  background-image: none;
}

.btn-SchoolList.disabled, .btn-SchoolList[disabled] {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-SchoolList {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-SchoolList.disabled:hover, .btn-SchoolList[disabled]:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-SchoolList:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-SchoolList.disabled:focus, .btn-SchoolList[disabled]:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-SchoolList:focus {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-SchoolList.disabled:active, .btn-SchoolList[disabled]:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-SchoolList:active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.btn-SchoolList.disabled.active, .btn-SchoolList[disabled].active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

fieldset[disabled] .btn-SchoolList.active {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  color: #999999;
  cursor: not-allowed;
}

.navbar-phone {
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #E6EB42;
}
.navbar-phone .navbar-brand {
  color: #ffffff;
}
.navbar-phone .navbar-brand:hover, .navbar-phone .navbar-brand:focus {
  color: #ffffff;
  background-color: none;
}
.navbar-phone .navbar-text {
  color: #dddddd;
}
.navbar-phone .navbar-nav > li > a {
  color: #ffffff;
}
.navbar-phone .navbar-nav > li > a:hover, .navbar-phone .navbar-nav > li > a:focus {
  color: #ffffff;
  background-color: #178acc;
}
.navbar-phone .navbar-nav > .active > a {
  color: #ffffff;
  background-color: #178acc;
}
.navbar-phone .navbar-nav > .active > a:hover, .navbar-phone .navbar-nav > .active > a:focus {
  color: #ffffff;
  background-color: #178acc;
}
.navbar-phone .navbar-nav > .disabled > a {
  color: #dddddd;
  background-color: transparent;
}
.navbar-phone .navbar-nav > .disabled > a:hover, .navbar-phone .navbar-nav > .disabled > a:focus {
  color: #dddddd;
  background-color: transparent;
}
.navbar-phone .btn-navbar {
  border-color: #FFFFFF;
}
.navbar-phone .btn-navbar:hover, .navbar-phone .btn-navbar:focus {
  background-color: #fff;
}
.navbar-phone .btn-navbar .icon-bar {
  background-color: #000000;
}
.navbar-phone .navbar-collapse,
.navbar-phone .navbar-form {
  border-color: #1995dc;
}
.navbar-phone .navbar-nav > .open > a {
  background-color: #178acc;
  color: #ffffff;
}
.navbar-phone .navbar-nav > .open > a:hover, .navbar-phone .navbar-nav > .open > a:focus {
  background-color: #178acc;
  color: #ffffff;
}
.navbar-phone .navbar-link {
  color: #ffffff;
}
.navbar-phone .navbar-link:hover {
  color: #ffffff;
}
.navbar-phone .btn-link {
  color: #ffffff;
}
.navbar-phone .btn-link:hover, .navbar-phone .btn-link:focus {
  color: #ffffff;
}
.navbar-phone .btn-link[disabled]:hover {
  color: #dddddd;
}

@media (max-width: 767px) {
  .navbar-phone .navbar-nav .open .dropdown-menu > li > a {
    color: #ffffff;
  }
  .navbar-phone .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-phone .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #ffffff;
    background-color: #178acc;
  }
  .navbar-phone .navbar-nav .open .dropdown-menu > .active > a {
    color: #ffffff;
    background-color: #178acc;
  }
  .navbar-phone .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-phone .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #ffffff;
    background-color: #178acc;
  }
  .navbar-phone .navbar-nav .open .dropdown-menu > .disabled > a {
    color: #dddddd;
    background-color: transparent;
  }
  .navbar-phone .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-phone .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #dddddd;
    background-color: transparent;
  }
}
fieldset[disabled] .navbar-phone .btn-link:hover,
.navbar-phone .btn-link[disabled]:focus,
fieldset[disabled] .navbar-phone .btn-link:focus {
  color: #dddddd;
}

.btn-navbar {
  float: left !important;
  margin-top: 12px;
  margin-bottom: 8px;
  background-color: #fff;
}

.btn-logo {
  float: left !important;
  position: relative;
  margin-left: 10px;
}

.btn-font-school {
  margin-top: 12px;
  margin-right: 5px;
  color: #000000;
  font-size: 18px;
  font-weight: bold;
}

.btn-logo-layout,
.btn-logo-school,
.btn-font-school {
  float: left !important;
  position: relative;
}

.btn-logo > img,
.btn-logo-layout > img,
.btn-logo-school > img {
  margin-top: 3px;
  margin-right: 5px;
  margin-bottom: 3px;
  max-height: 47px;
}

.line-left {
  float: left !important;
  background-color: #d0d0d0;
  height: 37px;
  width: 1px;
  margin-top: 10px;
  margin-bottom: 5px;
  margin-left: 10px;
  margin-right: 10px;
}

.line-right {
  float: right !important;
  background-color: #d0d0d0;
  height: 37px;
  width: 1px;
  margin-top: 10px;
  margin-bottom: 5px;
  margin-left: 10px;
  margin-right: 10px;
}

.btn-User {
  float: right !important;
  margin-top: 12px;
  margin-right: 25px;
  background-color: #fff;
}

@media (max-width: 370px) {
  .btn-logo-school,
  .line-right,
  .line-left {
    display: none !important;
  }
  .btn-logo > img,
  .btn-logo-layout > img,
  .btn-logo-school > img {
    margin-right: 10px;
    max-height: 40px;
  }
}
.btn-navbar .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.btn-navbar .icon-bar + .icon-bar {
  margin-top: 4px;
}

@media (min-width: 767px) {
  .btn-navbar {
    display: none;
  }
}
.navPhone {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navPhone:before {
  display: table;
  content: " ";
}
.navPhone:after {
  display: table;
  content: " ";
  clear: both;
}
.navPhone:before {
  display: table;
  content: " ";
}
.navPhone:after {
  display: table;
  content: " ";
  clear: both;
}
.navPhone > li {
  position: relative;
  display: block;
  font-size: 15px;
  font-weight: bold;
}
.navPhone > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
  letter-spacing: 2px;
}
.navPhone > li > a:hover, .navPhone > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.navPhone > li.disabled > a {
  background-color: #e5e5e5;
}
.navPhone > li.disabled > a:hover, .navPhone > li.disabled > a:focus {
  background-color: #e5e5e5;
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
}
.navPhone .open > a {
  background-color: #eeeeee;
}
.navPhone .open > a:hover, .navPhone .open > a:focus {
  background-color: #eeeeee;
}
.navPhone .open .caret {
  border-top: 4px solid #008500;
}
.navPhone .navPhone-divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.navPhone > li > a > img {
  max-width: none;
}

.iconPhone {
  margin-right: 5px;
  font-size: 1em;
}

.MyName {
  margin-top: 5px;
  margin-left: 5px;
  font-size: 15px;
  font-weight: bold;
}

.Title {
  white-space: nowrap;
  font-weight: bold;
}

@media (min-width: 1281px) {
  .Title {
    font-size: 16pt;
  }
  .navbar-Cach {
    width: 174px;
    min-width: 174px;
    padding-left: 55px;
    text-align: center;
    background-position-y: center;
    background-position-x: left;
    background-repeat: no-repeat;
  }
  .navbar-Cach-font {
    display: none !important;
  }
  ._Layout_RightBar {
    display: block !important;
  }
}
@media (min-width: 990px) and (max-width: 1280px) {
  .Title {
    font-size: 15pt;
  }
  .navbar-Cach {
    display: none !important;
  }
  .navbar-Cach-font {
    display: inline !important;
  }
  ._Layout_RightBar {
    display: block !important;
  }
}
@media (max-width: 989px) {
  .Title {
    font-size: 10pt;
  }
  .navbar-Cach {
    display: none !important;
  }
  .navbar-Cach-font {
    display: inline !important;
  }
  ._Layout_RightBar {
    display: none !important;
  }
}
.form-inlineEZ {
  padding-bottom: 10px;
}
.form-inlineEZ .form-group {
  display: inline;
  vertical-align: middle;
  padding-bottom: 10px;
}
.form-inlineEZ .form-group > label {
  margin-right: 10px;
  display: inline;
  font-size: 18px;
  font-weight: bold;
}

.form-control-Login {
  width: 180px;
  height: 35px;
  display: inline;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555555;
  vertical-align: middle;
  background-color: #ffffff;
  border: 1px solid #f8b4af;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

@media (min-width: 767px) {
  .btn-default-Login {
    margin: 2px;
    padding: 2px;
  }
}
.btn-default-Login {
  color: #333333;
  background-color: #FEE8E9;
  border-color: #f8b4af;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 20px;
  padding-left: 30px;
  padding-right: 10px;
}
.btn-default-Login:hover, .btn-default-Login:focus, .btn-default-Login:active, .btn-default-Login.active {
  color: #333333;
  background-color: #f8b4af;
  border-color: #f8b4af;
}

.open .dropdown-toggle.btn-default-Login {
  color: #333333;
  background-color: #f8b4af;
  border-color: #f8b4af;
}

.btn-default-Login:active, .btn-default-Login.active {
  background-image: none;
}

.open .dropdown-toggle.btn-default-Login {
  background-image: none;
}

.btn-default-Login.disabled, .btn-default-Login[disabled] {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-Login {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-Login.disabled:hover, .btn-default-Login[disabled]:hover {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-Login:hover {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-Login.disabled:focus, .btn-default-Login[disabled]:focus {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-Login:focus {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-Login.disabled:active, .btn-default-Login[disabled]:active {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-Login:active {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-Login.disabled.active, .btn-default-Login[disabled].active {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-Login.active {
  background-color: #ffffff;
  border-color: #cccccc;
}

.Div-EZ-Right {
  text-align: right;
  padding-top: 15px;
  padding-bottom: 15px;
  padding-right: 35px;
  padding-left: 15px;
}

@media print {
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11 {
    float: left;
  }
  .col-sm-12 {
    float: left;
    width: 100%;
  }
  .col-sm-11 {
    width: 91.66666667%;
  }
  .col-sm-10 {
    width: 83.33333333%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-8 {
    width: 66.66666667%;
  }
  .col-sm-7 {
    width: 58.33333333%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-5 {
    width: 41.66666667%;
  }
  .col-sm-4 {
    width: 33.33333333%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-2 {
    width: 16.66666667%;
  }
  .col-sm-1 {
    width: 8.33333333%;
  }
  .col-sm-pull-12 {
    right: 100%;
  }
  .col-sm-pull-11 {
    right: 91.66666667%;
  }
  .col-sm-pull-10 {
    right: 83.33333333%;
  }
  .col-sm-pull-9 {
    right: 75%;
  }
  .col-sm-pull-8 {
    right: 66.66666667%;
  }
  .col-sm-pull-7 {
    right: 58.33333333%;
  }
  .col-sm-pull-6 {
    right: 50%;
  }
  .col-sm-pull-5 {
    right: 41.66666667%;
  }
  .col-sm-pull-4 {
    right: 33.33333333%;
  }
  .col-sm-pull-3 {
    right: 25%;
  }
  .col-sm-pull-2 {
    right: 16.66666667%;
  }
  .col-sm-pull-1 {
    right: 8.33333333%;
  }
  .col-sm-pull-0 {
    right: auto;
  }
  .col-sm-push-12 {
    left: 100%;
  }
  .col-sm-push-11 {
    left: 91.66666667%;
  }
  .col-sm-push-10 {
    left: 83.33333333%;
  }
  .col-sm-push-9 {
    left: 75%;
  }
  .col-sm-push-8 {
    left: 66.66666667%;
  }
  .col-sm-push-7 {
    left: 58.33333333%;
  }
  .col-sm-push-6 {
    left: 50%;
  }
  .col-sm-push-5 {
    left: 41.66666667%;
  }
  .col-sm-push-4 {
    left: 33.33333333%;
  }
  .col-sm-push-3 {
    left: 25%;
  }
  .col-sm-push-2 {
    left: 16.66666667%;
  }
  .col-sm-push-1 {
    left: 8.33333333%;
  }
  .col-sm-push-0 {
    left: auto;
  }
  .col-sm-offset-12 {
    margin-left: 100%;
  }
  .col-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-sm-offset-9 {
    margin-left: 75%;
  }
  .col-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-sm-offset-6 {
    margin-left: 50%;
  }
  .col-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-sm-offset-3 {
    margin-left: 25%;
  }
  .col-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-sm-offset-0 {
    margin-left: 0%;
  }
}
.progress-bar-gray {
  background-color: #9D9D9D;
}

.progress-striped .progress-bar-gray {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.btn-default-buy {
  color: #333333;
  background-color: #ffffff;
  border-color: #cccccc;
}
.btn-default-buy:hover, .btn-default-buy:focus, .btn-default-buy:active, .btn-default-buy.active {
  color: #333333;
  background-color: #ebebeb;
  border-color: #adadad;
}

.open .dropdown-toggle.btn-default-buy {
  color: #333333;
  background-color: #ebebeb;
  border-color: #adadad;
}

.btn-default-buy:active, .btn-default-buy.active {
  background-image: none;
}

.open .dropdown-toggle.btn-default-buy {
  background-image: none;
}

.btn-default-buy.disabled, .btn-default-buy[disabled] {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-buy {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-buy.disabled:hover, .btn-default-buy[disabled]:hover {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-buy:hover {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-buy.disabled:focus, .btn-default-buy[disabled]:focus {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-buy:focus {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-buy.disabled:active, .btn-default-buy[disabled]:active {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-buy:active {
  background-color: #ffffff;
  border-color: #cccccc;
}

.btn-default-buy.disabled.active, .btn-default-buy[disabled].active {
  background-color: #ffffff;
  border-color: #cccccc;
}

fieldset[disabled] .btn-default-buy.active {
  background-color: #ffffff;
  border-color: #cccccc;
}

.css-table {
  display: table;
  border-collapse: collapse;
  width: 100%;
}
.css-table .thead {
  display: table-header-group;
}
.css-table .tbody {
  display: table-row-group;
}
.css-table .tr {
  display: table-row;
}
.css-table .th,
.css-table .td {
  display: table-cell;
  padding-left: 3px;
}

@media screen and (min-width: 361px) {
  .Div-EZ-ArtGallery-pointer {
    cursor: pointer;
    position: relative;
    width: 129px;
    height: 170px;
    margin: 0px auto;
  }
  .Div-EZ-ArtGallery-book {
    width: 129px;
    z-index: 1;
  }
  .Div-EZ-ArtGallery-div-img {
    top: 22px;
    left: 24px;
    position: absolute;
    width: 89px;
    height: 76px;
    z-index: 2;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    line-height: 76px;
  }
  .Div-EZ-ArtGallery-img {
    max-width: 88px;
    max-height: 76px;
  }
  .Div-EZ-ArtGallery-text {
    width: 129px;
  }
}
/*小於360px SHow*/
@media screen and (max-width: 360px) {
  .Div-EZ-ArtGallery-pointer {
    cursor: pointer;
    position: relative;
    width: 100px;
    height: 131px;
    margin: 0px auto;
  }
  .Div-EZ-ArtGallery-book {
    width: 100px;
    z-index: 1;
  }
  .Div-EZ-ArtGallery-div-img {
    top: 17px;
    left: 18px;
    position: absolute;
    width: 69px;
    height: 59px;
    z-index: 2;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    line-height: 59px;
  }
  .Div-EZ-ArtGallery-img {
    max-width: 69px;
    max-height: 59px;
  }
  .Div-EZ-ArtGallery-text {
    width: 100px;
  }
}
@media screen and (min-width: 1091px) {
  .Div-EZ-ArtGallery-pointer-all {
    cursor: pointer;
    position: relative;
    width: 170px;
    height: 165px;
    margin: 0px auto;
  }
  .Div-EZ-ArtGallery-book-all {
    width: 170px;
    z-index: 1;
  }
  .Div-EZ-ArtGallery-div-img-all {
    cursor: pointer;
    top: 15px;
    left: 20px;
    position: absolute;
    width: 130px;
    height: 110px;
    z-index: 2;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    line-height: 110px;
  }
  .Div-EZ-ArtGallery-img-all {
    max-width: 120px;
    max-height: 105px;
  }
  .Div-EZ-ArtGallery-text-all {
    width: 120px;
  }
  .Div-EZ-ArtGallery-icon {
    top: 127px;
    left: 27px;
    position: absolute;
    z-index: 5;
    width: 32px;
    height: 46px;
  }
}
/*小於 1090px SHow*/
@media screen and (max-width: 1090px) {
  .Div-EZ-ArtGallery-pointer-all {
    cursor: pointer;
    position: relative;
    width: 136px;
    height: 132px;
    margin: 0px auto;
  }
  .Div-EZ-ArtGallery-book-all {
    width: 136px;
    z-index: 1;
  }
  .Div-EZ-ArtGallery-div-img-all {
    cursor: pointer;
    top: 12px;
    left: 16px;
    position: absolute;
    width: 104px;
    height: 88px;
    z-index: 2;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    line-height: 88px;
  }
  .Div-EZ-ArtGallery-img-all {
    max-width: 96px;
    max-height: 84px;
  }
  .Div-EZ-ArtGallery-text-all {
    width: 96px;
  }
  .Div-EZ-ArtGallery-icon {
    top: 102px;
    left: 22px;
    position: absolute;
    z-index: 5;
    width: 25px;
    height: 36px;
  }
}
/*小於 361px SHow*/
@media screen and (max-width: 361px) {
  .Div-EZ-ArtGallery-pointer-all {
    cursor: pointer;
    position: relative;
    width: 82px;
    height: 79px;
    margin: 0px auto;
  }
  .Div-EZ-ArtGallery-book-all {
    width: 82px;
    z-index: 1;
  }
  .Div-EZ-ArtGallery-div-img-all {
    cursor: pointer;
    top: 7px;
    left: 10px;
    position: absolute;
    width: 62px;
    height: 53px;
    z-index: 2;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    line-height: 53px;
  }
  .Div-EZ-ArtGallery-img-all {
    max-width: 62px;
    max-height: 53px;
  }
  .Div-EZ-ArtGallery-text-all {
    width: 77px;
  }
  .Div-EZ-ArtGallery-icon {
    top: 43px;
    left: 13px;
    position: absolute;
    z-index: 5;
    width: 22px;
    height: 31px;
  }
}
#DivAddButton {
  position: fixed;
  /*固定在網頁上不隨卷軸移動，若要隨卷軸移動用absolute*/
  bottom: 25px;
  right: -80px;
  /*設置水平位置，依所放的內容多寡需要自行手動調整*/
  background-color: rgba(255, 255, 244, 0.5);
  padding: 12px 10px 15px 10px;
  z-index: 99;
  border-radius: 10px;
  /*圓角*/
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
}
#DivAddButton:hover {
  /*當滑鼠移至此區塊時，伸縮區塊*/
  right: -10px;
}
#DivAddButton #title {
  padding-right: 5px;
  /*讓標題與連結中間有空隙*/
}

.use-absolute {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: none;
  top: 40%;
  right: 0;
}

.use-absoluteDiv {
  position: absolute;
  width: 60%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}

.rwd-table {
  background: #fff;
  overflow: hidden;
  min-width: 100%;
}
.rwd-table tr:nth-of-type(2n) {
  background: #eee;
}
.rwd-table th,
.rwd-table td {
  margin: 0.5em 1em;
}
.rwd-table th {
  display: none;
}
.rwd-table td {
  display: block;
}
.rwd-table td:before {
  content: attr(data-th) " : ";
  font-weight: bold;
  width: 6.5em;
  display: inline-block;
}
.rwd-table th,
.rwd-table td {
  text-align: left;
}
.rwd-table th,
.rwd-table td:before {
  color: #D20B2A;
  font-weight: bold;
  font-size: 18px;
}

@media (min-width: 480px) {
  .rwd-table td:before {
    display: none;
  }
  .rwd-table th,
  .rwd-table td {
    display: table-cell;
    padding: 0.25em 0.5em;
  }
  .rwd-table th:first-child,
  .rwd-table td:first-child {
    padding-left: 0;
  }
  .rwd-table th:last-child,
  .rwd-table td:last-child {
    padding-right: 0;
  }
  .rwd-table th,
  .rwd-table td {
    padding: 1em !important;
    font-size: 13px;
  }
}
#table-breakpoint th,
#table-breakpoint td {
  padding: 1em !important;
  font-size: 18px;
}

@media only screen and (max-width: 568px) {
  #table-breakpoint thead {
    display: none;
  }
  #table-breakpoint tbody td {
    border: none !important;
    display: block;
    vertical-align: top;
  }
  #table-breakpoint tbody td:before {
    content: attr(data-th) ": ";
    display: inline-block;
    font-weight: bold;
    width: 6.5em;
  }
  #table-breakpoint tbody td.bt-hide {
    display: none;
  }
  #table-breakpoint th,
  #table-breakpoint td {
    padding: 1em !important;
    font-size: 13px;
  }
}
/* Others */
.ui-icon {
  text-indent: inherit !important;
}

/* href disabled */
.not-active {
  pointer-events: none;
  cursor: default;
  text-decoration: none;
  color: black;
  opacity: 0.5;
}

.text-nowrap {
  white-space: nowrap !important;
}

.form-control-required {
  background-color: #ffffe7;
}

.control-label-required::after {
  content: "*";
  color: #E04;
}

.hr-line-dashed {
  border: 1px dashed #ddd;
}

label.btn span {
  font-size: 1.5em;
}
label input[type=radio] ~ i.fa.fa-circle-o {
  color: #c8c8c8;
  display: inline;
}
label input[type=radio] ~ i.fa.fa-dot-circle-o {
  display: none;
}
label input[type=radio]:checked ~ i.fa.fa-circle-o {
  display: none;
}
label input[type=radio]:checked ~ i.fa.fa-dot-circle-o {
  color: #7AA3CC;
  display: inline;
}
label:hover input[type=radio] ~ i.fa {
  color: #7AA3CC;
}
label input[type=checkbox] ~ i.fa.fa-square-o {
  color: #c8c8c8;
  display: inline;
}
label input[type=checkbox] ~ i.fa.fa-check-square-o {
  display: none;
}
label input[type=checkbox]:checked ~ i.fa.fa-square-o {
  display: none;
}
label input[type=checkbox]:checked ~ i.fa.fa-check-square-o {
  color: #7AA3CC;
  display: inline;
}
label:hover input[type=checkbox] ~ i.fa {
  color: #7AA3CC;
}

div[data-toggle=buttons] label {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 2em;
  text-align: left;
  white-space: nowrap;
  vertical-align: top;
  cursor: pointer;
  background-color: none;
  border: 0px solid #c8c8c8;
  border-radius: 3px;
  color: #c8c8c8;
  -moz-user-select: none;
  -o-user-select: none;
  -webkit-user-select: none;
          user-select: none;
}
div[data-toggle=buttons] label.active, div[data-toggle=buttons] label:hover {
  color: #7AA3CC;
}
div[data-toggle=buttons] label:active, div[data-toggle=buttons] label.active {
  box-shadow: none;
}

.print-only {
  display: none;
}

@media print {
  .no-print {
    display: none !important;
  }
  .no-print * {
    display: none !important;
  }
  .print-only {
    display: block;
  }
}
@media (min-width: 767px) {
  .text-center-Mobile-left {
    text-align: center;
  }
}
@media (max-width: 767px) {
  .text-center-Mobile-left {
    text-align: left;
  }
}
table.bt tfoot th:first-of-type:before,
table.bt tfoot th:first-of-type .bt-content {
  padding-top: 5px;
}
table.bt tfoot td:first-of-type:before,
table.bt tfoot td:first-of-type .bt-content {
  padding-top: 5px;
}
table.bt tbody td:first-of-type:before,
table.bt tbody td:first-of-type .bt-content {
  padding-top: 5px;
}
table.bt tfoot th:last-of-type:before,
table.bt tfoot th:last-of-type .bt-content {
  padding-bottom: 5px;
}
table.bt tfoot td:last-of-type:before,
table.bt tfoot td:last-of-type .bt-content {
  padding-bottom: 5px;
}
table.bt tbody td:last-of-type:before,
table.bt tbody td:last-of-type .bt-content {
  padding-bottom: 5px;
}

.quadrado {
  animation: crescendo 0.2s alternate infinite ease-in;
  animation-iteration-count: 2;
}

.quadrado:hover {
  animation: crescendo 0.5s alternate infinite ease-in;
}

@keyframes crescendo {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}
.form-group {
  display: flex;
  flex-wrap: wrap;
}

.text-red {
  color: red !important;
}

.text-white {
  color: #fff !important;
}

.shortcut-links {
  display: inline;
}
@media (min-width: 768px) {
  .shortcut-links {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }
}
.shortcut-links-item {
  flex: 0 0 auto;
  margin: 0 0.3rem;
  font-size: 1rem;
  display: inline-block;
}
@media (min-width: 768px) {
  .shortcut-links-item {
    flex: 0 0 50px;
    display: block;
    margin: 0.3rem;
    font-size: 0;
    height: 50px;
    max-height: 50px;
    width: 50px;
    max-width: 50px;
    background-color: #ededed;
    border-radius: 3rem;
    box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.18);
  }
}
@media (min-width: 992px) {
  .shortcut-links-item {
    margin: 0 0.3rem;
  }
}
.shortcut-links-item img {
  display: none;
  width: 100%;
  height: 100%;
}
@media (min-width: 768px) {
  .shortcut-links-item img {
    display: block;
  }
}

.shortcut-qrcode {
  display: inline-block;
  margin: 0 0.3rem;
  padding: 0;
  font-size: 1rem;
}
@media (min-width: 768px) {
  .shortcut-qrcode {
    margin: 0;
    padding: 1rem 1rem 0 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #000;
  }
}
.shortcut-qrcode img {
  display: none;
  margin: 0.3rem;
  height: auto;
  width: 100%;
}
@media (min-width: 768px) {
  .shortcut-qrcode img {
    display: block;
    max-width: 100%;
  }
}
@media (min-width: 992px) {
  .shortcut-qrcode img {
    max-width: 100px;
  }
}

.footer {
  text-align: center;
  padding: 0.2rem;
  background-color: #744624;
  color: #fff;
  border-top: 2px solid #c77966;
  border-bottom: 2px solid #c77966;
  position: relative;
  width: 80%;
  max-width: 905px;
  margin: 1rem auto;
}
.footer::before {
  content: "";
  width: 4.5em;
  height: 2.35em;
  position: absolute;
  top: -0.15em;
  left: -4.5em;
  background-image: url(../images/site-footer-bg.png);
  background-repeat: no-repeat;
  background-position: left top;
  background-size: auto 100%;
}
.footer::after {
  content: "";
  width: 4.5em;
  height: 2.35em;
  position: absolute;
  top: -0.15em;
  right: -4.5em;
  background-image: url(../images/site-footer-bg.png);
  background-repeat: no-repeat;
  background-position: right top;
  background-size: auto 100%;
}

.user-players {
  display: inline-block;
  width: 100%;
  max-width: 12rem;
  height: auto;
  vertical-align: text-bottom;
  pointer-events: visible;
}

.read-certification {
  max-width: 10rem;
}
@media (min-width: 992px) {
  .read-certification {
    max-width: 100%;
  }
}
.read-certification-title {
  display: inline-block;
  background-image: url(../images/read-certification-title.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  font-size: 0;
  height: 40px;
  max-width: 159px;
  width: 100%;
}
.read-certification-lv, .read-certification-lv10, .read-certification-lv9, .read-certification-lv8, .read-certification-lv7, .read-certification-lv6, .read-certification-lv5, .read-certification-lv4, .read-certification-lv3, .read-certification-lv2, .read-certification-lv1 {
  display: inline-block;
  margin: 0.15rem;
  width: 1.5rem;
  height: 2rem;
  font-size: 0;
  background-image: url(../images/read-certification-card-bg.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
}
@media (min-width: 992px) {
  .read-certification-lv, .read-certification-lv10, .read-certification-lv9, .read-certification-lv8, .read-certification-lv7, .read-certification-lv6, .read-certification-lv5, .read-certification-lv4, .read-certification-lv3, .read-certification-lv2, .read-certification-lv1 {
    width: 32px;
    height: 42px;
  }
}
.read-certification-lv span, .read-certification-lv10 span, .read-certification-lv9 span, .read-certification-lv8 span, .read-certification-lv7 span, .read-certification-lv6 span, .read-certification-lv5 span, .read-certification-lv4 span, .read-certification-lv3 span, .read-certification-lv2 span, .read-certification-lv1 span {
  display: inline-block;
  padding-top: 0.1rem;
  margin-top: 0.1rem;
  min-width: 1.4rem;
  font-family: -webkit-body;
  font-size: 1.2rem;
  line-height: 1;
  text-align: center;
  color: #fff;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 51%, rgba(229, 229, 229, 0) 100%), radial-gradient(ellipse at center, #948527 35% 0%, #3b3200 100%);
  border-radius: 1rem;
  transform: scale(0.6);
}
@media (min-width: 992px) {
  .read-certification-lv span, .read-certification-lv10 span, .read-certification-lv9 span, .read-certification-lv8 span, .read-certification-lv7 span, .read-certification-lv6 span, .read-certification-lv5 span, .read-certification-lv4 span, .read-certification-lv3 span, .read-certification-lv2 span, .read-certification-lv1 span {
    padding-top: 4px;
    margin-top: 6px;
    min-width: 20px;
    min-height: 20px;
    font-family: -webkit-body;
    transform: scale(1);
  }
}
.read-certification-lv2 span {
  filter: hue-rotate(240deg);
}
.read-certification-lv3 span {
  filter: hue-rotate(330deg);
}
.read-certification-lv4 span {
  filter: hue-rotate(32deg);
}
.read-certification-lv5 span {
  filter: hue-rotate(137deg);
}
.read-certification-lv6 span {
  filter: hue-rotate(28deg) brightness(0.8) contrast(1.7) saturate(0.2);
}
.read-certification-lv7 span {
  filter: hue-rotate(212deg) brightness(0.8) contrast(1.7) saturate(0.5);
}
.read-certification-lv8 span {
  filter: hue-rotate(240deg) brightness(0.9) contrast(1.7) saturate(0.5);
}
.read-certification-lv9 span {
  filter: hue-rotate(239deg) brightness(0.8) contrast(1.7) saturate(0.5);
}
.read-certification-lv10 span {
  filter: hue-rotate(274deg) brightness(0.8) contrast(1.7) saturate(0.7);
}

.read-books-title {
  display: inline-block;
  background-image: url(../images/read-books-title.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  font-size: 0;
  height: 40px;
  max-width: 159px;
  width: 100%;
}
.read-books-lv, .read-books-lv6, .read-books-lv5, .read-books-lv4, .read-books-lv3, .read-books-lv2, .read-books-lv1 {
  display: inline-block;
  position: relative;
  margin: 0.15rem;
  width: 1.5rem;
  height: 2rem;
  font-size: 0;
  background-image: url(../images/read-books-bg.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
}
.read-books-lv::before, .read-books-lv6::before, .read-books-lv5::before, .read-books-lv4::before, .read-books-lv3::before, .read-books-lv2::before, .read-books-lv1::before {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: url(../images/read-books-color.png);
  background-repeat: no-repeat;
  background-size: auto 100%;
  background-position: center center;
}
.read-books-lv::after, .read-books-lv6::after, .read-books-lv5::after, .read-books-lv4::after, .read-books-lv3::after, .read-books-lv2::after, .read-books-lv1::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: url(../images/read-books-bgup.png);
  background-repeat: no-repeat;
  background-size: auto 100%;
  background-position: center center;
}
@media (min-width: 992px) {
  .read-books-lv, .read-books-lv6, .read-books-lv5, .read-books-lv4, .read-books-lv3, .read-books-lv2, .read-books-lv1 {
    width: 36px;
    height: 47px;
  }
}
.read-books-lv span, .read-books-lv6 span, .read-books-lv5 span, .read-books-lv4 span, .read-books-lv3 span, .read-books-lv2 span, .read-books-lv1 span {
  font-size: 1rem;
  font-weight: 600;
  font-family: -webkit-body;
  display: block;
  padding: 0;
  position: relative;
  z-index: 1;
  transform: scale(0.4);
}
@media (min-width: 992px) {
  .read-books-lv span, .read-books-lv6 span, .read-books-lv5 span, .read-books-lv4 span, .read-books-lv3 span, .read-books-lv2 span, .read-books-lv1 span {
    margin-top: 5px;
    padding: 0.425rem;
    transform: scale(0.7);
  }
}
.read-books-lv2::before {
  filter: hue-rotate(310deg) brightness(0.6);
}
.read-books-lv3::before {
  filter: hue-rotate(189deg) brightness(0.7);
}
.read-books-lv4::before {
  filter: hue-rotate(157deg) brightness(0.7);
}
.read-books-lv5::before {
  filter: hue-rotate(314deg) brightness(0.5) saturate(2);
}
.read-books-lv6::before {
  filter: hue-rotate(281deg) brightness(0.5) saturate(2);
}

.sport-banker {
  display: inline-block;
  max-width: 50%;
}
@media (min-width: 992px) {
  .sport-banker {
    max-width: 100%;
  }
}
.sport-banker img {
  max-width: 50px;
}
.sport-banker-txt {
  color: #005ea3;
}
.sport-banker-info {
  color: #a96b1a;
}

.school-menu {
  position: relative;
  padding: 0.5em;
  margin-bottom: 1em;
  border-radius: 0;
  overflow: initial;
  font-size: 0.7rem;
}
@media (min-width: 768px) {
  .school-menu {
    position: static;
    background-image: none;
    background-color: #ffc100;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.4);
    border-radius: 1em;
    max-width: 176px;
    margin: 0 auto;
  }
}
.school-menu .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100%;
}
.school-menu .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) .btn-default {
  margin: 0;
}
.school-menu-none {
  display: none;
}
@media (min-width: 768px) {
  .school-menu-none {
    display: block;
  }
}
.school-menu-title {
  margin: -0.5em -0.5em 0.4em -0.5em;
  padding: 0.3em 0;
  display: block;
  border-radius: 0.3em 0.3em 0 0;
  font-size: 2.125em;
  font-weight: 600;
  text-align: center;
  color: #004aa2;
}
@media (min-width: 768px) {
  .school-menu-title {
    font-size: 1.725em;
    background-image: linear-gradient(45deg, rgb(255, 233, 136) 0%, rgb(255, 220, 50) 100%);
    color: #603813;
  }
}
.school-menu-list {
  padding: 0;
  margin: 1em 0;
  list-style-type: none;
  font-size: 1.326em;
}
.school-menu-list strong {
  padding: 0.3em;
  display: block;
  text-align: left;
  color: #3a87ad;
  background-color: #d9edf7;
  border-color: #bce8f1;
  border-radius: 0.3em 0.3em 0 0;
}
@media (min-width: 768px) {
  .school-menu-list strong {
    text-align: center;
    color: #000;
    background-color: transparent;
    border-color: transparent;
  }
}
.school-menu-list > li {
  display: block;
  padding: 0;
  margin: 0;
  list-style-type: none;
  background-color: #ffffff;
  border: 1px solid #bce8f1;
  border-radius: 0.3em;
}
@media (min-width: 768px) {
  .school-menu-list > li {
    background-color: transparent;
    border-color: transparent;
  }
}
.school-menu-child {
  padding: 1.5em;
  margin: 0;
  list-style-type: none;
  display: flex;
  flex-wrap: wrap;
  font-size: 0.725em;
}
@media (min-width: 768px) {
  .school-menu-child {
    padding: 0;
  }
}
.school-menu-child li {
  flex: 0 0 50%;
  display: inline-block;
  padding: 0;
  margin: 0.3em 0;
}
.school-menu-child li a {
  display: block;
  font-size: 1.526em;
  font-weight: bold;
  text-align: center;
  padding: 0.5em 0.3em;
  margin: 0.1em 0.5em;
  letter-spacing: 2px;
  color: #000;
  background-color: #FFF;
  border: 1.5px solid #c3e4f6;
  border-radius: 0.3em;
}
@media (min-width: 768px) {
  .school-menu-child li a {
    padding: 0;
    margin: 0;
    font-size: 1.4em;
    font-weight: normal;
    letter-spacing: 0;
    background-color: transparent;
    border-color: transparent;
  }
}

.text-black {
  color: #000;
}

.text-ribbon {
  color: #2448a4;
}

.ribbon {
  width: 64%;
  margin: 1.5rem auto;
  text-align: center;
  font-size: 19px;
  letter-spacing: 0.5rem;
  position: relative;
  box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.4);
}
.ribbon::before, .ribbon::after {
  content: "";
  position: absolute;
  top: -0.5em;
  width: 20vw;
  border: 1rem solid;
  z-index: -1;
  box-shadow: 0 0.1rem 0 0 rgba(0, 0, 0, 0.2);
}
@media (min-width: 576px) {
  .ribbon::before, .ribbon::after {
    width: 19vw;
  }
}
@media (min-width: 768px) {
  .ribbon::before, .ribbon::after {
    width: 33.5%;
  }
}
.ribbon::before {
  left: -28%;
  border-left-color: transparent;
  border-left-width: 0.5em;
}
.ribbon::after {
  right: -28%;
  border-right-color: transparent;
  border-right-width: 0.5em;
}
.ribbon-img {
  position: absolute;
  min-width: 47px;
  width: 15%;
  height: auto;
  left: -19%;
  top: -1em;
  z-index: 1;
}
.ribbon-title:before, .ribbon-title:after {
  content: "";
  position: absolute;
  border-style: solid;
  top: -0.5em;
}
.ribbon-title:before {
  left: 0;
  border-width: 0.5em 1.2em 0 0;
}
@media (min-width: 992px) {
  .ribbon-title:before {
    border-width: 0.5em 1.44em 0 0;
  }
}
.ribbon-title:after {
  right: 0;
  border-width: 0 1.2em 0.5em 0;
}
@media (min-width: 992px) {
  .ribbon-title:after {
    border-width: 0 1.44em 0.5em 0;
  }
}
.ribbon-content {
  padding: 2rem;
}
.ribbon-content-info {
  position: absolute;
  right: 41px;
  margin-top: -50px;
  font-weight: 600;
}

.ribbon-default {
  background-color: #ebf1ff;
  color: #0036bf;
}
.ribbon-default::before, .ribbon-default::after {
  border-color: #dee7fc;
}
.ribbon-default::before {
  border-left-color: transparent;
}
.ribbon-default::after {
  border-right-color: transparent;
}
.ribbon-default .ribbon-title:before {
  border-color: transparent #a0b5e4 transparent transparent;
}
.ribbon-default .ribbon-title:after {
  border-color: transparent transparent #a0b5e4 #a0b5e4;
}

.ribbon-default + .ribbon-content {
  background-color: rgba(235, 241, 255, 0.2);
}

.ribbon-ecool {
  background-color: #ccffff;
  color: #0036bf;
}
.ribbon-ecool::before, .ribbon-ecool::after {
  border-color: #c2fafa;
}
.ribbon-ecool::before {
  border-left-color: transparent;
}
.ribbon-ecool::after {
  border-right-color: transparent;
}
.ribbon-ecool .ribbon-title:before {
  border-color: transparent #88dddd transparent transparent;
}
.ribbon-ecool .ribbon-title:after {
  border-color: transparent transparent #88dddd #88dddd;
}

.ribbon-ecool + .ribbon-content {
  background-color: rgba(204, 255, 255, 0.2);
}

.ribbon-news {
  background-color: #eced33;
  color: #0036bf;
}
.ribbon-news::before, .ribbon-news::after {
  border-color: #d9da37;
}
.ribbon-news::before {
  border-left-color: transparent;
}
.ribbon-news::after {
  border-right-color: transparent;
}
.ribbon-news .ribbon-title:before {
  border-color: transparent #818139 transparent transparent;
}
.ribbon-news .ribbon-title:after {
  border-color: transparent transparent #818139 #818139;
}

.ribbon-news + .ribbon-content {
  background-color: rgba(236, 237, 51, 0.2);
}

.ribbon-task {
  background-color: #adfbf2;
  color: #0036bf;
}
.ribbon-task::before, .ribbon-task::after {
  border-color: #a6f3ea;
}
.ribbon-task::before {
  border-left-color: transparent;
}
.ribbon-task::after {
  border-right-color: transparent;
}
.ribbon-task .ribbon-title:before {
  border-color: transparent #76ccc2 transparent transparent;
}
.ribbon-task .ribbon-title:after {
  border-color: transparent transparent #76ccc2 #76ccc2;
}

.ribbon-task + .ribbon-content {
  background-color: rgba(173, 251, 242, 0.2);
}

.ribbon-readLeaderboard {
  background-color: #e2eaa2;
  color: #0036bf;
}
.ribbon-readLeaderboard::before, .ribbon-readLeaderboard::after {
  border-color: #d7dd9f;
}
.ribbon-readLeaderboard::before {
  border-left-color: transparent;
}
.ribbon-readLeaderboard::after {
  border-right-color: transparent;
}
.ribbon-readLeaderboard .ribbon-title:before {
  border-color: transparent #a2a77f transparent transparent;
}
.ribbon-readLeaderboard .ribbon-title:after {
  border-color: transparent transparent #a2a77f #a2a77f;
}

.ribbon-readLeaderboard + .ribbon-content {
  background-color: rgba(226, 234, 162, 0.2);
}

.ribbon-read-books {
  background-color: #fceec1;
  color: #0036bf;
}
.ribbon-read-books::before, .ribbon-read-books::after {
  border-color: #f5e7b8;
}
.ribbon-read-books::before {
  border-left-color: transparent;
}
.ribbon-read-books::after {
  border-right-color: transparent;
}
.ribbon-read-books .ribbon-title:before {
  border-color: transparent #d2c085 transparent transparent;
}
.ribbon-read-books .ribbon-title:after {
  border-color: transparent transparent #d2c085 #d2c085;
}

.ribbon-read-books + .ribbon-content {
  background-color: rgba(252, 238, 193, 0.2);
}

.ribbon-carousel {
  background-color: #e2e1fe;
  color: #0036bf;
}
.ribbon-carousel::before, .ribbon-carousel::after {
  border-color: #d7d6fa;
}
.ribbon-carousel::before {
  border-left-color: transparent;
}
.ribbon-carousel::after {
  border-right-color: transparent;
}
.ribbon-carousel .ribbon-title:before {
  border-color: transparent #9e9cdd transparent transparent;
}
.ribbon-carousel .ribbon-title:after {
  border-color: transparent transparent #9e9cdd #9e9cdd;
}

.ribbon-carousel + .ribbon-content {
  background-color: rgba(226, 225, 254, 0.2);
}

.ribbon-articleLeaderboard {
  background-color: #e8d1e6;
  color: #0036bf;
}
.ribbon-articleLeaderboard::before, .ribbon-articleLeaderboard::after {
  border-color: #ddcddb;
}
.ribbon-articleLeaderboard::before {
  border-left-color: transparent;
}
.ribbon-articleLeaderboard::after {
  border-right-color: transparent;
}
.ribbon-articleLeaderboard .ribbon-title:before {
  border-color: transparent #aaaaaa transparent transparent;
}
.ribbon-articleLeaderboard .ribbon-title:after {
  border-color: transparent transparent #aaaaaa #aaaaaa;
}

.ribbon-articleLeaderboard + .ribbon-content {
  background-color: rgba(232, 209, 230, 0.2);
}

.ribbon-hot-prize {
  background-color: #f3c8b0;
  color: #0036bf;
}
.ribbon-hot-prize::before, .ribbon-hot-prize::after {
  border-color: #e9c1ab;
}
.ribbon-hot-prize::before {
  border-left-color: transparent;
}
.ribbon-hot-prize::after {
  border-right-color: transparent;
}
.ribbon-hot-prize .ribbon-title:before {
  border-color: transparent #ba9783 transparent transparent;
}
.ribbon-hot-prize .ribbon-title:after {
  border-color: transparent transparent #ba9783 #ba9783;
}

.ribbon-hot-prize + .ribbon-content {
  background-color: rgba(243, 200, 176, 0.2);
}

.ribbon-coinLeaderboard {
  background-color: #fde680;
  color: #0036bf;
}
.ribbon-coinLeaderboard::before, .ribbon-coinLeaderboard::after {
  border-color: #f2dc7c;
}
.ribbon-coinLeaderboard::before {
  border-left-color: transparent;
}
.ribbon-coinLeaderboard::after {
  border-right-color: transparent;
}
.ribbon-coinLeaderboard .ribbon-title:before {
  border-color: transparent #c7b150 transparent transparent;
}
.ribbon-coinLeaderboard .ribbon-title:after {
  border-color: transparent transparent #c7b150 #c7b150;
}

.ribbon-coinLeaderboard + .ribbon-content {
  background-color: rgba(253, 230, 128, 0.2);
}

.ribbon-online-gallery-green {
  background-color: #dff0d8;
  color: #468847;
}
.ribbon-online-gallery-green::before, .ribbon-online-gallery-green::after {
  border-color: #d8e7d2;
}
.ribbon-online-gallery-green::before {
  border-left-color: transparent;
}
.ribbon-online-gallery-green::after {
  border-right-color: transparent;
}
.ribbon-online-gallery-green .ribbon-title:before {
  border-color: transparent #b1b1b1 transparent transparent;
}
.ribbon-online-gallery-green .ribbon-title:after {
  border-color: transparent transparent #b1b1b1 #b1b1b1;
}

.ribbon-online-gallery-green + .ribbon-content {
  background-color: rgba(223, 240, 216, 0.2);
}

.ribbon-online-gallery-pink {
  background-color: #dba3ac;
  color: #ffe650;
}
.ribbon-online-gallery-pink::before, .ribbon-online-gallery-pink::after {
  border-color: #cca3a9;
}
.ribbon-online-gallery-pink::before {
  border-left-color: transparent;
}
.ribbon-online-gallery-pink::after {
  border-right-color: transparent;
}
.ribbon-online-gallery-pink .ribbon-title:before {
  border-color: transparent #8c8c8c transparent transparent;
}
.ribbon-online-gallery-pink .ribbon-title:after {
  border-color: transparent transparent #8c8c8c #8c8c8c;
}

.ribbon-online-gallery-pink + .ribbon-content {
  background-color: rgba(219, 163, 172, 0.2);
}

.ribbon + .ribbon-content {
  padding-top: 31px;
  margin: -31px 0 0 0;
}
@media (min-width: 768px) {
  .ribbon + .ribbon-content {
    margin: -31px 3.5rem 0 3.5rem;
  }
}

@media (min-width: 768px) {
  .col-md-4 > .ribbon {
    margin-top: 3rem;
  }
}
@media (min-width: 768px) {
  .col-md-4 > .ribbon .ribbon-img {
    left: -16%;
    top: -2.3em;
  }
}
.col-md-4 > .ribbon > .ribbon-title {
  letter-spacing: 0;
}
@media (min-width: 768px) {
  .col-md-4 > .ribbon > .ribbon-title {
    font-size: 0.925rem;
  }
  .col-md-4 > .ribbon > .ribbon-title:before {
    border-width: 0.5em 0.4em 0 0;
  }
  .col-md-4 > .ribbon > .ribbon-title:after {
    border-width: 0 0.4em 0.5em 0;
  }
}

.list-table {
  padding: 0;
  margin: 0;
  display: table;
  width: 100%;
  font-size: 0.825rem;
}
.list-table li {
  padding: 0;
  margin: 0;
  display: table-row-group;
}
.list-table li > * {
  padding: 0.2rem;
  display: table-cell;
  color: #000;
}

.badge-hot {
  color: #fff;
  background: linear-gradient(red 0, #ad0303 100%) !important;
  transform: scale(0.725);
  padding: 0 0.3rem;
  border-radius: 0.5rem;
  border: 3px red groove;
}
.badge-hot::before {
  content: "HOT!";
}
.badge-hot:empty {
  display: inline-block;
}

.ribbon-online-gallery-pink {
  text-shadow: 1px 1px 1px #000, 0px 1px 1px #000;
}
.ribbon-online-gallery-pink-bg {
  background-image: url(../img/GalleryBook_Asset-03.gif);
  border: 3px solid #dba3ac;
}

.ribbon-content-edit {
  margin: 0;
}
@media (min-width: 768px) {
  .ribbon-content-edit {
    margin: 0 3.5rem;
  }
}/*# sourceMappingURL=EzCss.css.map */