// Modern jQuery code for ADDI01Proxy Index page
$(document).ready(function() {
    // 頁面初始化模組
    const pageInitializer = {
        init: function() {
            this.enhanceButtons();
            this.addLoadingStates();
        },

        enhanceButtons: function() {
            // 為按鈕添加hover效果和點擊反饋
            $('.btn-default').hover(
                function() {
                    $(this).addClass('btn-primary').removeClass('btn-default');
                },
                function() {
                    $(this).addClass('btn-default').removeClass('btn-primary');
                }
            );
        },

        addLoadingStates: function() {
            // 為按鈕添加點擊時的載入狀態
            $('.btn').on('click', function() {
                const $btn = $(this);
                const originalText = $btn.text();
                
                $btn.prop('disabled', true)
                    .html('<i class="fa fa-spinner fa-spin"></i> 載入中...');
                
                // 如果頁面沒有在合理時間內跳轉，恢復按鈕狀態
                setTimeout(function() {
                    $btn.prop('disabled', false).text(originalText);
                }, 5000);
            });
        }
    };

    // 導航增強模組
    const navigationEnhancer = {
        init: function() {
            this.addKeyboardNavigation();
            this.addAccessibilityFeatures();
        },

        addKeyboardNavigation: function() {
            // 添加鍵盤導航支援
            $(document).on('keydown', this.handleKeyNavigation.bind(this));
        },

        handleKeyNavigation: function(e) {
            const $buttons = $('.btn-block');
            const currentIndex = $buttons.index($(':focus'));
            
            switch(e.which) {
                case 37: // 左箭頭
                case 38: // 上箭頭
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : $buttons.length - 1;
                    $buttons.eq(prevIndex).focus();
                    break;
                    
                case 39: // 右箭頭
                case 40: // 下箭頭
                    e.preventDefault();
                    const nextIndex = currentIndex < $buttons.length - 1 ? currentIndex + 1 : 0;
                    $buttons.eq(nextIndex).focus();
                    break;
                    
                case 13: // Enter
                    if ($(':focus').hasClass('btn')) {
                        $(':focus')[0].click();
                    }
                    break;
            }
        },

        addAccessibilityFeatures: function() {
            // 添加無障礙功能
            $('.btn-block').each(function(index) {
                $(this).attr({
                    'tabindex': '0',
                    'role': 'button',
                    'aria-label': '選擇模式: ' + $(this).text()
                });
            });
        }
    };

    // 用戶體驗增強模組
    const uxEnhancer = {
        init: function() {
            this.addTooltips();
            this.addAnimations();
        },

        addTooltips: function() {
            // 為按鈕添加提示信息
            $('.btn-block').each(function() {
                const buttonText = $(this).text();
                let tooltipText = '';
                
                switch(buttonText) {
                    case '單一學生':
                        tooltipText = '為單一學生代申請投稿';
                        break;
                    case '多位學生':
                        tooltipText = '為多位學生批量代申請投稿';
                        break;
                    case '班級學生':
                        tooltipText = '為整個班級的學生代申請投稿';
                        break;
                    default:
                        tooltipText = '點擊選擇此模式';
                }
                
                $(this).attr('title', tooltipText);
            });
        },

        addAnimations: function() {
            // 添加進入動畫
            $('.col-md-4').each(function(index) {
                $(this).css({
                    'opacity': '0',
                    'transform': 'translateY(20px)'
                }).delay(index * 200).animate({
                    'opacity': '1'
                }, 500, function() {
                    $(this).css('transform', 'translateY(0)');
                });
            });
        }
    };

    // 錯誤處理模組
    const errorHandler = {
        init: function() {
            this.setupErrorHandling();
        },

        setupErrorHandling: function() {
            // 處理可能的導航錯誤
            $(window).on('error', this.handleError.bind(this));
        },

        handleError: function(e) {
            console.error('頁面錯誤:', e);
            // 可以在這裡添加錯誤報告邏輯
        }
    };

    // 分析和追蹤模組
    const analytics = {
        init: function() {
            this.trackUserInteractions();
        },

        trackUserInteractions: function() {
            $('.btn-block').on('click', function() {
                const mode = $(this).text();
                console.log('用戶選擇模式:', mode);
                
                // 可以在這裡添加分析追蹤代碼
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'mode_selection', {
                        'mode_type': mode,
                        'page_title': document.title
                    });
                }
            });
        }
    };

    // 初始化所有模組
    pageInitializer.init();
    navigationEnhancer.init();
    uxEnhancer.init();
    errorHandler.init();
    analytics.init();

    // 設置全局配置（需要在 CSHTML 中定義）
    window.ADDI01PROXY_CONFIG = window.ADDI01PROXY_CONFIG || {};
});
