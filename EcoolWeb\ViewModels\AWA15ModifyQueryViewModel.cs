﻿using DotNet.Highcharts;
using ECOOL_APP.com.ecool.Models.DTO.AWAT14;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWA15ModifyQueryViewModel
    {
        /// <summary>
        /// 角色名稱 [Student, Teacher]
        /// </summary>
        public string RoleName { get; set; }


        /// <summary>
        /// 只顯示某一所學校=>人員
        /// </summary>
        public string whereSchoolNo { get; set; }


       

        /// <summary>
        /// 搜尋狀態
        /// </summary>
        public string whereSTATUS { get; set; }


        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的暱稱
        /// </summary>
        public string whereSNAME { get; set; }


        /// <summary>
        /// 只顯示某一位學生的升級ID
        /// </summary>
        public string whereAWAT15NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的升級ＮＯ
        /// </summary>
        public string whereAWARD_NAME { get; set; }


        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一位學生的班級
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }
        public string WhereRankNO { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderColumn { get; set; }

        /// <summary>
        /// 排序: 遞增/遞減
        /// </summary>
        public string SortBy { get; set; }

        public int Page { get; set; }

       
        /// <summary>
        /// 查詢結果 - 學生
        /// </summary>
        public IPagedList<VAWAT15> AWAT15List;

        public IPagedList<AWAT15> AWAT15tempList;

        public virtual ICollection<AWAT15PrintViewModel> Chk { get; set; }

        //public virtual ICollection<AWA004PrintViewModel> Chk { get; set; }

        public AWA15ModifyQueryViewModel()
        {
            Page = 0;
            OrderColumn = "RANK_NO";
            SortBy = "DESC";
            RoleName = "";
        }
    }
}