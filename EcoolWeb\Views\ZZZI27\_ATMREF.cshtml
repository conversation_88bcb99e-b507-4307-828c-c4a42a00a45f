﻿@model List<ZZZI27DetailsViewModel>
@using ECOOL_APP.com.ecool.service

@using (Html.BeginCollectionItem("Details_List"))
{
    var Index = Html.GetIndex("Details_List");

<div class="Div-EZ-reader">
    <div class="form-horizontal">
        @Html.Label("觸發時間(分鐘)", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
        <div class="col-md-8 col-sm-8 col-lg-8">
            <samp class="dd">
                @{ var MEMOInfo = "";
                    var MEMOItemInfo = "";

                    }
                @foreach (var emp in Model.Skip(2))
                {
                   @*@Html.EditorFor(model => emp.DelayTime, new { htmlAttributes = new { @class = "form-control input-md", @Type = @"" + BDMT02_ENUM.Mode.GetInputType(emp.VAL_MODE) + "" } })*@

                var itemInfo = "";
                var NameInfo = "";
                itemInfo = "Details_List_" + Index+ "__DelayTime";
                NameInfo = "Details_List[" + Index+ "].DelayTime";
                MEMOInfo = "Details_List_" + Index + "__MEMO";
                MEMOItemInfo = "Details_List[" + Index + "].MEMO";
                                            <input type="text" class="form-control input-md text-box single-line" data-val="true" data-val-number="欄位 DelayTime 必須是數字。" id="@itemInfo" name="@NameInfo" value="@(((decimal)emp.DelayTime).ToString("#0.0"))" placeholder="(0代表不設定此功能)">

                }
                @*@Html.EditorFor(model => model.FirstOrDefault().DelayTime, new { htmlAttributes = new { @class = "form-control input-md", @Type = @"" + BDMT02_ENUM.Mode.GetInputType(Model.FirstOrDefault().VAL_MODE) + "" } })*@
                @*@Html.EditorFor(model => model.DelayTime, new { htmlAttributes = new { @class = "form-control input-md", @Type = @"" + BDMT02_ENUM.Mode.GetInputType(Model.VAL_MODE) + "" } })*@
            </samp>
        </div>
    </div>
    @Html.HiddenFor(m => m.FirstOrDefault().BRE_NO)
    @Html.HiddenFor(m => m.FirstOrDefault().DATA_CODE)
    @Html.HiddenFor(m => m.FirstOrDefault().DATA_TYPE)
    @Html.HiddenFor(m => m.FirstOrDefault().SCHOOL_NO)
    @Html.HiddenFor(m => m.FirstOrDefault().ITEM_NO)
    @Html.HiddenFor(m => m.FirstOrDefault().ADD_MODE)
    @Html.HiddenFor(m => m.FirstOrDefault().TXT_MODE)
    @Html.HiddenFor(m => m.FirstOrDefault().VAL_MODE)
    @Html.HiddenFor(m => m.FirstOrDefault().TXT_SCHOOL_SET_YN)
    @Html.HiddenFor(m => m.FirstOrDefault().VAL_SCHOOL_SET_YN)
    <div class="form-horizontal">
        @Html.Label("閒置後，導引的頁面", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
        <div class="col-md-8 col-sm-8 col-lg-8">
            <samp class="dd">
                <select class="form-control" id="@MEMOInfo" name="@MEMOItemInfo">
                    @foreach (var ite in (IEnumerable<SelectListItem>)ViewBag.BarcCodeMyCashItem)
                    {
                        if (ite.Selected == true)
                        {
                            <option value="@ite.Value" selected="selected">

                                @ite.Value

                            </option>

                        }
                        else
                        {

                            <option value="@ite.Value">

                                @ite.Value

                            </option>

                        }


                    }

                </select>
              
                @*@Html.DropDownList("MEMO", (IEnumerable<SelectListItem>)ViewBag.BarcCodeMyCashItem, new { @class = "form-control" })*@
            </samp>
        </div>
    </div>
</div>
}
