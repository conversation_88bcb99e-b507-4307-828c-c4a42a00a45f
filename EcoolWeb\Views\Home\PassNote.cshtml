﻿
@{
    ViewBag.Title = "通過閱讀護照";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string ImagePath = Url.Content("~/Content/img/coolpass-0"+ user.NotePassGrade.ToString() +".png");
    short cash = 30;
    if (user.NotePassGrade >= 3) { cash = 40; }
    if (user.NotePassGrade >= 5) { cash = 50; }
}


<table align="center">
    <tr>
        <td>
            <span style="font-size: 12pt;font-weight: bold; color:red">
                小朋友恭喜你～<br />
                恭喜你獲得臺北e閱讀認證，並得到
                @cash<text>點酷幣！</text><br />
                請按下確認鍵，右邊將顯示您所達成的閱讀護照等級！<br /><br />
            </span>
        </td>
    </tr>
    <tr>
        <td align="center" style="white-space: nowrap;font-size: 16pt;font-weight: bold;color:blue">
            <a href="@Url.Action("PassNoteGet", "Home")" role="button" class="btn btn-default">
              按我確認
            </a>
        </td>
    </tr>
    <tr>
        <td align="center">
            <img src='@ImagePath' class="img-responsive " alt="Responsive image"/>
        </td>
    </tr>

</table>
<script type="text/javascript">
    window.history.forward(1);
</script>