﻿using com.ecool.service;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using ECOOL_APP;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.LogicCenter.ADDI11;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using EcoolWeb.ViewModels;
using MvcPaging;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI11Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ADDI11";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ADDI11Service Service = new ADDI11Service();
        public ActionResult Index2(ADDI11IndexViewModel model, string LAP)
        {

            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            RollCallBarcodeCashViewModel CallCashModel = new RollCallBarcodeCashViewModel();
            this.Shared();
            ViewBag.Title = "運動撲滿---感應跑步一圈";
            StringBuilder ret = new StringBuilder();
            string[] chineseNumber = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
            string[] unit = { "", "十", "百", "千", "萬", "十萬", "百萬", "千萬", "億", "十億", "百億", "千億", "兆", "十兆", "百兆", "千兆" };
            if (!string.IsNullOrEmpty(LAP)) {
                int idx = LAP.Length;
                bool needAppendZero = false;
                foreach (char c in LAP)
                {
                    idx--;
                    if (c > '0')
                    {
                        if (needAppendZero)
                        {
                            ret.Append(chineseNumber[0]);
                            needAppendZero = false;
                        }
                        ret.Append(chineseNumber[(int)(c - '0')] + unit[idx]);
                    }
                    else
                        needAppendZero = true;
                }
                ViewBag.Title = " 每跑" + ret + "圈感應一次";

            }
            model.ONE_LAP_M = Service.GetONE_LAP_M(SCHOOL_NO, ref db);
            if (model != null && !string.IsNullOrEmpty(model.CARD_NO))
            {
                model.CARD_NO = model.CARD_NO;
            }

            return View(model);
        }
        [CheckPermission(CheckACTION_ID = "LogRunIndexDetail")]
        public ActionResult LogRunIndexDetail(ADDI11IndexViewModel model, string LAP) {
            this.Shared(Bre_Name + "-    感應圈數紀錄");
            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            if (model == null) model = new ADDI11IndexViewModel();
            RollCallBarcodeCashViewModel CallCashModel = new RollCallBarcodeCashViewModel();
            this.Shared();
            user = UserProfileHelper.Get();
            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGrade, model.WhereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereGrade });
            return View(model);
        }
        [CheckPermission] //檢查權限修正
        public ActionResult Index()
        {
            user = UserProfileHelper.Get();
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

            if (AppMode)
            {
                return Redirect("IndexAPP");
            }
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }
            else
            {
                this.Shared(Bre_Name + "-說明");
            }

            return base.View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限修正
        public ActionResult IndexIstory()
        {
            user = UserProfileHelper.Get();
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);


            this.Shared(Bre_Name + "-說明");


            return base.View();
        }
        [CheckPermission(CheckACTION_ID = "TeacherEdit")] //檢查權限
        public ActionResult TeacherEdit(ADDI11EditViewModel model)
        {
            this.Shared(Bre_Name + "-新增老師跑步量");
            if (model == null) model = new ADDI11EditViewModel();

            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;

            ModelState.Clear();
            //是否可以對全部班級做修改的權限(例如體育老師可以修改全校班級跑步狀況)
            List<SelectListItem> NOitems = new List<SelectListItem>();
            NOitems.Add(new SelectListItem() { Text = "請選擇..", Value = "", Selected = string.IsNullOrWhiteSpace("") });
            NOitems.Add(new SelectListItem() { Text = "級任導師", Value = "級任導師" });
            NOitems.Add(new SelectListItem() { Text = "非級任導師", Value = "非級任導師" });
            NOitems.Add(new SelectListItem() { Text = "全校老師", Value = "全校老師" });
            //var canEditAllClass = PermissionService.GetPermission_Use_YN(Bre_NO, "CanEditOtherClassRun", SCHOOL_NO, USER_NO) != "N";

            //var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.SearchCLASS_NO, ref db);
            //if ((!HRMT24_ENUM.CheckQAdmin(user) && !canEditAllClass) || !string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
            //{
            //    if (user.USER_NO.Contains("run"))
            //    {
            //        char grade = user.USER_NO.Last();
            //        /* 運動撲滿小幫手 */
            //        ClassItems = ClassItems.Where(a => a.Value.FirstOrDefault() == grade).ToList();
            //    }
            //    else
            //    {
            //        if (user.TEACH_CLASS_NO == null)
            //        {
            //            if (HRMT24_ENUM.IsOtherTeacher(user) == false)
            //            {
            //                ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //            }
            //        }
            //        else
            //        {
            //            ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //        }
            //    }
            //}

            ViewBag.ClassItems = NOitems;

            model.ONE_LAP_M = Service.GetONE_LAP_M(SCHOOL_NO, ref db);

            if (!string.IsNullOrWhiteSpace(model.SearchCLASS_NO) && model.RunDate != null)
            {
                if (model.SearchCLASS_NO == "級任導師")
                {
                    model.People = (
               from a in db.HRMT01
               join o in db.ADDT24.Where(b => b.RUN_DATE == model.RunDate) on new { a.SCHOOL_NO, a.USER_NO } equals new { o.SCHOOL_NO, o.USER_NO } into ps
               from o in ps.DefaultIfEmpty()
               join h3 in db.HRMT03 on new { a.SCHOOL_NO, a.USER_NO } equals new { h3.SCHOOL_NO, USER_NO = h3.TEACHER_NO } into cls
               from h3 in cls.DefaultIfEmpty()
               where a.SCHOOL_NO == SCHOOL_NO && (a.USER_TYPE == UserType.Teacher) && a.CLASS_NO != null
             && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))
               //  orderby a.SEAT_NO
               select new ADDI11EditPeopleViewModel()
               {
                   SCHOOL_NO = a.SCHOOL_NO,
                   USER_NO = a.USER_NO,
                   CLASS_NO = a.CLASS_NO,
                   NAME = a.NAME,
                   LAP = (double)(o.LAP ?? 0),
                   UserType = a.USER_TYPE
               }).OrderBy(x => x.NAME).ToList();
                }
                else if (model.SearchCLASS_NO == "非級任導師")
                {
                    model.People = (
               from a in db.HRMT01
               join o in db.ADDT24.Where(b => b.RUN_DATE == model.RunDate) on new { a.SCHOOL_NO, a.USER_NO } equals new { o.SCHOOL_NO, o.USER_NO } into ps
               from o in ps.DefaultIfEmpty()
              .GroupBy(x => new { x.SCHOOL_NO, x.USER_NO, x.NAME, x.LAP })
                   //   join h3 in db.HRMT03 on new { a.SCHOOL_NO } equals new { h3.SCHOOL_NO} into cls
                   //from h3 in cls.DefaultIfEmpty()
               where a.SCHOOL_NO == SCHOOL_NO && (a.USER_TYPE == UserType.Teacher) && a.CLASS_NO == null
               && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))

               //  orderby a.SEAT_NO
               select new ADDI11EditPeopleViewModel()
               {
                   SCHOOL_NO = a.SCHOOL_NO,
                   USER_NO = a.USER_NO,
                   CLASS_NO = "",
                   NAME = a.NAME,
                   LAP = (double)(o.Key.LAP ?? 0),
                   UserType = a.USER_TYPE
               }).OrderBy(x => x.NAME).ToList();

                }
                else if (model.SearchCLASS_NO == "全校老師")
                {
                    model.People = (
              from a in db.HRMT01
              join o in db.ADDT24.Where(b => b.RUN_DATE == model.RunDate) on new { a.SCHOOL_NO, a.USER_NO } equals new { o.SCHOOL_NO, o.USER_NO } into ps
              from o in ps.DefaultIfEmpty()
                  //   join h3 in db.HRMT03 on new { a.SCHOOL_NO } equals new { h3.SCHOOL_NO} into cls
                  //from h3 in cls.DefaultIfEmpty()
              where a.SCHOOL_NO == SCHOOL_NO && (a.USER_TYPE == UserType.Teacher)
            && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))
              //  orderby a.SEAT_NO
              select new ADDI11EditPeopleViewModel()
              {
                  SCHOOL_NO = a.SCHOOL_NO,
                  USER_NO = a.USER_NO,
                  CLASS_NO = a.CLASS_NO,
                  NAME = a.NAME,
                  LAP = (double)(o.LAP ?? 0),
                  UserType = a.USER_TYPE
              }).OrderBy(x => x.NAME).ToList();

                }

            }
            else if (string.IsNullOrWhiteSpace(model.SearchCLASS_NO))
            {
                model.People = (
            from a in db.HRMT01
            join o in db.ADDT24.Where(b => b.RUN_DATE == model.RunDate) on new { a.SCHOOL_NO, a.USER_NO } equals new { o.SCHOOL_NO, o.USER_NO } into ps
            from o in ps.DefaultIfEmpty()
                //   join h3 in db.HRMT03 on new { a.SCHOOL_NO } equals new { h3.SCHOOL_NO} into cls
                //from h3 in cls.DefaultIfEmpty()
            where a.SCHOOL_NO == SCHOOL_NO && (a.USER_TYPE == UserType.Teacher)
        && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))
            //  orderby a.SEAT_NO
            select new ADDI11EditPeopleViewModel()
            {
                SCHOOL_NO = a.SCHOOL_NO,
                USER_NO = a.USER_NO,
                CLASS_NO = a.CLASS_NO,
                NAME = a.NAME,
                LAP = (double)(o.LAP ?? 0),
                UserType = a.USER_TYPE
            }).OrderBy(x => x.NAME).ToList();
            }
            if (!string.IsNullOrWhiteSpace(model.SearchName)) {


                model.People = model.People.Where(x => x.NAME.Contains(model.SearchName)).OrderBy(x => x.NAME).ToList();
            }

            return View(model);
        }
        [CheckPermission(CheckACTION_ID = "ADDRunIndex")] //檢查權限
        public ActionResult ADDRunIndex() {
            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            this.Shared(Bre_Name + "-    感應新增跑步量");
            ViewBag.ISTASKLIST = ISTASKLIST;
            return View();

        }
        [CheckPermission(CheckACTION_ID = "LogRunIndex")] //檢查權限
        public ActionResult LogRunIndex() {

            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            return View();
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Edit(ADDI11EditViewModel model)
        {
            this.Shared(Bre_Name + "-新增學生跑步量");
            if (model == null) model = new ADDI11EditViewModel();

            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;

            ModelState.Clear();
            //是否可以對全部班級做修改的權限(例如體育老師可以修改全校班級跑步狀況)
            var canEditAllClass = PermissionService.GetPermission_Use_YN(Bre_NO, "CanEditOtherClassRun", SCHOOL_NO, USER_NO) != "N";

            var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.SearchCLASS_NO, ref db);
            if ((!HRMT24_ENUM.CheckQAdmin(user) && !canEditAllClass) || !string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
            {
                if (user.USER_NO.Contains("run"))
                {
                    char grade = user.USER_NO.Last();
                    /* 運動撲滿小幫手 */
                    ClassItems = ClassItems.Where(a => a.Value.FirstOrDefault() == grade).ToList();
                }
                else
                {
                    if (user.TEACH_CLASS_NO == null)
                    {
                        if (HRMT24_ENUM.IsOtherTeacher(user) == false)
                        {
                            ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                        }
                    }
                    else
                    {
                        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                    }
                }
            }
            ViewBag.ClassItems = ClassItems;

            model.ONE_LAP_M = Service.GetONE_LAP_M(SCHOOL_NO, ref db);

            if (!string.IsNullOrWhiteSpace(model.SearchCLASS_NO) && model.RunDate != null)
            {
                model.People = (
                 from a in db.HRMT01
                 join o in db.ADDT24.Where(b => b.RUN_DATE == model.RunDate) on new { a.SCHOOL_NO, a.USER_NO } equals new { o.SCHOOL_NO, o.USER_NO } into ps
                 from o in ps.DefaultIfEmpty()
                 join h3 in db.HRMT03 on new { a.SCHOOL_NO, a.USER_NO } equals new { h3.SCHOOL_NO, USER_NO = h3.TEACHER_NO } into cls
                 from h3 in cls.DefaultIfEmpty()
                 where a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == model.SearchCLASS_NO && (a.USER_TYPE == UserType.Student || a.USER_TYPE == UserType.Teacher)
                 && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))
                 orderby a.SEAT_NO
                 select new ADDI11EditPeopleViewModel()
                 {
                     SCHOOL_NO = a.SCHOOL_NO,
                     USER_NO = a.USER_NO,
                     CLASS_NO = a.CLASS_NO,
                     LAP = (double)(o.LAP ?? 0),
                     UserType = a.USER_TYPE
                 }).ToList();
            }

            return View(model);
        }

        public ActionResult _ADDRunEditSAVE(ADDI11MyRunLogDataViewModel model)
        {
            this.Shared();


            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            if (model == null) model = new ADDI11MyRunLogDataViewModel();

            //var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.SearchCLASS_NO, ref db);

            string Message = string.Empty;
            string Success = "flase";
            int ONE_LAP_M = Service.GetONE_LAP_M(SCHOOL_NO, ref db);
            ADDT24 SaveUp = db.ADDT24.Where(a => a.SCHOOL_NO == model.SCHOOL_NO && a.USER_NO == model.USER_NO && a.RUN_DATE == model.RUN_DATE).FirstOrDefault();

            model.NAME = SaveUp.NAME;
            model.CLASS_NO = SaveUp.CLASS_NO;
            model.USER_NO = SaveUp.USER_NO;
            model.SNAME = SaveUp.SNAME;
            //   model.LAP = (double)(SaveUp.LAP ?? 0);
            model.LAP_M = SaveUp.LAP_M;
            var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + model.NAME + "\"}";
            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤<br/>" + string.Join("; ", ModelState.Values
                                        .SelectMany(x => x.Errors)
                                        .Select(x => x.ErrorMessage));

                //  return PartialView("_ADDRunEdit", new { USER_NO = model.USER_NO, RunDtae = model.RUN_DATE });

                Success = "true";
                data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
                return Json(@data, JsonRequestBehavior.AllowGet);
            }
            else
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                var OK = Service.SaveRunFromCard(model, user, ref db, ref Message, ref valuesList);
                if (OK)
                {
                    TempData["StatusMessage"] = "儲存完成";

                    SaveUp = db.ADDT24.Where(a => a.SCHOOL_NO == model.SCHOOL_NO && a.USER_NO == model.USER_NO && a.RUN_DATE == model.RUN_DATE).FirstOrDefault();

                    model.NAME = SaveUp.NAME;
                    model.CLASS_NO = SaveUp.CLASS_NO;
                    model.USER_NO = SaveUp.USER_NO;
                    model.SNAME = SaveUp.SNAME;
                    model.LAP = (double)(SaveUp.LAP ?? 0);
                    model.LAP_M = SaveUp.LAP_M;
                    data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + model.NAME + "\"}";
                    //return PartialView("_ADDRunEdit",new { USER_NO =model.USER_NO, RunDtae =model.RUN_DATE});
                }

            }
            return Json(@data, JsonRequestBehavior.AllowGet);
            // return PartialView("_ADDRunEdit", new { USER_NO = model.USER_NO, RunDtae = model.RUN_DATE });

        }

        [CheckPermission(CheckACTION_ID = "_ADDRunEdit")]
        public ActionResult _ADDRunEdit(string USER_NO, string RunDtae, string Lap, string Run_ID) {
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();
            string Message = string.Empty;
            string Success = "flase";
            var hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();
            ADDI11MyRunLogDataViewModel model = new ADDI11MyRunLogDataViewModel();

            model.RUN_DATE = DateTime.Parse(RunDtae);


            ADDT24_D_CARD SaveUp = db.ADDT24_D_CARD.Where(a => a.SCHOOL_NO == hr.SCHOOL_NO && a.USER_NO == hr.USER_NO && a.RUN_DATE == model.RUN_DATE && a.RUN_ID == Run_ID).FirstOrDefault();

            model.NAME = SaveUp.NAME;
            model.CLASS_NO = SaveUp.CLASS_NO;
            model.USER_NO = SaveUp.USER_NO;
            model.SNAME = SaveUp.SNAME;
            model.LAP = (double)(SaveUp.LAP ?? 0);
            model.LAP_M = SaveUp.LAP_M;
            model.RUN_ID = SaveUp.RUN_ID;

            model.SEAT_NO = SaveUp.SEAT_NO;
            model.SCHOOL_NO = SaveUp.SCHOOL_NO;

            return PartialView(model);
        }
        public ActionResult ADDRunDelete(string USER_NO, string RunDtae, string Lap, string Run_ID) {

            SCHOOL_NO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();
            string Message = string.Empty;
            string Success = "flase";
            var hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();
            ADDI11MyRunLogDataViewModel model = new ADDI11MyRunLogDataViewModel();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();

            model.LAP = 0;
            model.RUN_ID = Run_ID;
            model.RUN_DATE = DateTime.Parse(RunDtae);
            model.SCHOOL_NO = SCHOOL_NO;
            model.USER_NO = USER_NO;
            var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + hr.NAME + "\"}";
            var OK = Service.SaveRunFromCard(model, user, ref db, ref Message, ref valuesList);
            if (OK)
            {
                data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + hr.NAME + "\"}";
            }
            else {
                Success = "true";
                data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
                return Json(@data, JsonRequestBehavior.AllowGet);



            }
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        public ActionResult _LogRunIndexDetail(ADDI11IndexViewModel model)
        {
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            //model.People = (
            //    from a in db.HRMT01
            //    join o in db.ADDT24 on new { a.SCHOOL_NO, a.USER_NO } equals new { o.SCHOOL_NO, o.USER_NO } into ps
            //    from o in ps.DefaultIfEmpty()
            //        //join h3 in db.HRMT03 on new { a.SCHOOL_NO, a.USER_NO } equals new { h3.SCHOOL_NO, USER_NO = h3.TEACHER_NO } into cls
            //        // from h3 in cls.DefaultIfEmpty()
            //    where a.SCHOOL_NO == SCHOOL_NO && (a.USER_TYPE == UserType.Student || a.USER_TYPE == UserType.Teacher) && o.LAP > 0 && o.LAP != null && o.ADDFromCard == "Y"
            //    && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS)) && o.LAP == (decimal)model.LAP
            //    orderby o.CHG_DATE descending
            //    select new ADDI11EditPeopleViewModel()
            //    {
            //        SCHOOL_NO = a.SCHOOL_NO,
            //        NAME = a.NAME,
            //        SEAT_NO = a.SEAT_NO,
            //        CHG_DATE = o.CHG_DATE,
            //        USER_NO = a.USER_NO,
            //        CLASS_NO = a.CLASS_NO,
            //        RunDtae = o.RUN_DATE,
            //        LAP = (double)(o.LAP ?? 0),
            //        UserType = a.USER_TYPE
            //    }).Take(5).ToList();

            string Message = string.Empty;
            if (model == null) model = new ADDI11IndexViewModel();
            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }
            model = Service.GetADDListDataDetail(model, ref db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGrade, model.WhereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereGrade });

            return PartialView(model);

        }
        public ActionResult _ADDRunDetailLog(ADDI11EditViewModel model)
        {
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            //string sSQLD = $@" select b.SCHOOL_NO,b.USER_NO, RUN_DATE as RUN_DATE, b.LAP as lap
            //                    from ADDT24_D_CARD b (nolock)
            //                    inner join HRMT01 c on b.SCHOOL_NO=c.SCHOOL_NO and b.USER_NO=c.USER_NO
            //                    inner  join ADDT24 k on c.SCHOOL_NO=k.SCHOOL_NO and c.USER_NO=k.USER_NO
            //                    Where  b.SCHOOL_NO=@SCHOOL_NO and b.STATUS='{ADDT24.STATUSVal.OK}' and ADDFromCard ='Y' AND b.LAP is not  null AND　b.LAP > 0 && c.USER_STATUS! = 9 order by b.CHG_DATE desc
            //                       ";
            var temp = (
                 from a in db.HRMT01
                 join o in db.ADDT24_D_CARD on new { a.SCHOOL_NO, a.USER_NO } equals new { o.SCHOOL_NO, o.USER_NO } into ps
                 join k in db.ADDT24 on new { a.SCHOOL_NO, a.USER_NO } equals new { k.SCHOOL_NO, k.USER_NO } into psk
                 from o in ps.DefaultIfEmpty()
                 from kl in psk.DefaultIfEmpty()

                     //join h3 in db.HRMT03 on new { a.SCHOOL_NO, a.USER_NO } equals new { h3.SCHOOL_NO, USER_NO = h3.TEACHER_NO } into cls
                     // from h3 in cls.DefaultIfEmpty()
                 where a.SCHOOL_NO == SCHOOL_NO && (a.USER_TYPE == UserType.Student || a.USER_TYPE == UserType.Teacher) && o.LAP > 0 && o.LAP != null && o.ADDFromCard == "Y" && kl.SCHOOL_NO == o.SCHOOL_NO && kl.USER_NO == o.USER_NO
                 && o.RUN_DATE == kl.RUN_DATE
                 && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))
                 orderby o.CHG_DATE descending
                 select new ADDI11EditPeopleViewModel()
                 {
                     SCHOOL_NO = a.SCHOOL_NO,
                     NAME = a.NAME,
                     SEAT_NO = a.SEAT_NO,
                     CHG_DATE = o.CHG_DATE,
                     USER_NO = a.USER_NO,
                     CLASS_NO = a.CLASS_NO,
                     RunDtae = o.RUN_DATE,
                     LAP = (double)(o.LAP ?? 0),
                     UserType = a.USER_TYPE,
                     SUMLap = (decimal)(kl.LAP ?? 0),
                 });

            model.Peoples = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
            return PartialView(model);

        }
        public ActionResult CheckUSerInfo(ADDI11IndexViewModel model) {
            string Message = string.Empty;
            string Success = "flase";
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();
            var hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == model.CARD_NO && a.USER_STATUS != 9).FirstOrDefault();
            var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
            if (hr == null)
            {

                hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CARD_NO == model.CARD_NO && a.USER_STATUS != 9).FirstOrDefault();
                if (hr == null)
                {
                    Message = "警告!!輸入的內容有錯誤<br/>;此卡號或學號不正確，請重新輸入正確的卡號或學號";
                    Success = "true";
                    data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
                    return Json(@data, JsonRequestBehavior.AllowGet);


                }
                else {
                    data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + hr.NAME + "\"}";

                }
            }
            else
            {
                data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" , \"Name\" : \"" + hr.NAME + "\"}";


            }


            return Json(@data, JsonRequestBehavior.AllowGet);
        }
        [CheckPermission(CheckACTION_ID = "ADDRunIndex")]
        public ActionResult ADDRunEditSave(ADDI11IndexViewModel model) {
            this.Shared();

            string Message = string.Empty;
            string Success = "flase";
            if (model == null) model = new ADDI11IndexViewModel();
            var hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == model.CARD_NO && a.USER_STATUS != 9).FirstOrDefault();
            var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
            if (hr == null)
            {

                hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CARD_NO == model.CARD_NO).FirstOrDefault();
                if (hr == null)
                {
                    Message = "警告!!輸入的內容有錯誤<br/>;此卡號或學號不正確，請重新輸入正確的卡號或學號";
                    Success = "true";
                    data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
                    return Json(@data, JsonRequestBehavior.AllowGet);
                    return View("Index2", model);


                }
                else
                {
                    DateTime RunDate = DateTime.Parse(DateTime.Now.ToShortDateString());
                    model.RunDate = RunDate;
                    List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                    var OK = Service.SaveADDRun(hr, model, user, ref db, ref Message, ref valuesList);
                    if (OK)
                    {
                        TempData["StatusMessage"] = "儲存完成";
                        data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + TempData["StatusMessage"] + "\" , \"Name\" : \"" + hr.NAME + "\"}";
                        return Json(@data, JsonRequestBehavior.AllowGet);
                    }

                }

            }
            else {
                DateTime RunDate = DateTime.Parse(DateTime.Now.ToShortDateString());
                model.RunDate = RunDate;
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                var OK = Service.SaveADDRun(hr, model, user, ref db, ref Message, ref valuesList);
                if (OK)
                {
                    //TempData["StatusMessage"] = "儲存完成";

                    TempData["StatusMessage"] = "儲存完成";
                    data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + TempData["StatusMessage"] + "\" , \"Name\" : \"" + hr.NAME + "\"}";
                    return Json(@data, JsonRequestBehavior.AllowGet);
                }

            }
            if (!string.IsNullOrEmpty(Message))
            {
                TempData["StatusMessage"] = Message;
                // return View("Index2", model);

                Success = "true";
                data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\"}";
                return Json(@data, JsonRequestBehavior.AllowGet);
            }
            else {

                TempData["StatusMessage"] = "儲存完成";
                data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + TempData["StatusMessage"] + "\" , \"Name\" : \"" + hr.NAME + "\"}";
                return Json(@data, JsonRequestBehavior.AllowGet);
            }
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult EditSave(ADDI11EditViewModel model)
        {
            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;

            this.Shared(Bre_Name + "-新增");

            if (model == null) model = new ADDI11EditViewModel();
            var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.SearchCLASS_NO, ref db);
            if (!HRMT24_ENUM.CheckQAdmin(user))
            {
                if (user.TEACH_CLASS_NO == null)
                {
                    if (HRMT24_ENUM.IsOtherTeacher(user) == false && HRMT24_ENUM.IshelperUser(user) == false)
                    {
                        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                    }
                }
                else
                {
                    ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                }
            }
            ViewBag.ClassItems = ClassItems;

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤<br/>" + string.Join("; ", ModelState.Values
                                        .SelectMany(x => x.Errors)
                                        .Select(x => x.ErrorMessage));
            }
            else
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                var OK = Service.SaveRunData(model, user, ref db, ref Message, ref valuesList);

                if (OK)
                {
                    TempData["StatusMessage"] = "儲存完成";

                    return View("EditSuccess", model);
                }
            }

            TempData["StatusMessage"] = Message;

            return View("Edit", model);
        }

        public ActionResult RunDetail()
        {
            return View();
        }

        public ActionResult _People(ADDI11EditPeopleViewModel Item)
        {
            this.Shared();
            if (Item == null) Item = new ADDI11EditPeopleViewModel();

            var userList = HRMT01.GetUserNoListDataMust(Item.SCHOOL_NO, Item.CLASS_NO, Item.USER_NO, ref db);

            // 老師也加入
            if (Item.UserType == UserType.Teacher)
            {
                HRMT01 teacher = db.HRMT01.Where(h => h.SCHOOL_NO == Item.SCHOOL_NO && h.USER_NO == Item.USER_NO).FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(Item.CLASS_NO))
                {

                    userList.Add(new SelectListItem() { Text = teacher.NAME + "/班導師", Value = teacher.USER_NO, Selected = teacher.USER_NO == Item.USER_NO });
                }
                else {

                    userList.Add(new SelectListItem() { Text = teacher.NAME + "/老師", Value = teacher.USER_NO, Selected = teacher.USER_NO == Item.USER_NO });
                }
            }

            ViewBag.UserNoItems = userList;

            return PartialView(Item);
        }
        [HttpGet]
        public ActionResult OrderList()
        {
            ADDI11OrderListViewModel model = new ADDI11OrderListViewModel();

            this.Shared(Bre_Name + "-跑步排行榜");
            return View(model);
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult OrderList(ADDI11OrderListViewModel model)
        {
            this.Shared(Bre_Name + "-跑步排行榜");
            return View(model);
        }
        public ActionResult OrderListIstory(ADDI11OrderListViewModel model)
        {
            this.Shared(Bre_Name + "-跑步排行榜");
            return View(model);
        }

        public ActionResult _PageOrderList(ADDI11OrderListViewModel model)
        {
            this.Shared(Bre_Name + "-跑步排行榜");
            string Message = string.Empty;
            if (model == null) model = new ADDI11OrderListViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            model = Service.GetOrderListData(model, ref db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGrade, model.WhereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereGrade });

            return PartialView(model);
        }

        public ActionResult _PageRunCountLog(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-跑步紀錄");
            ADDI11RunMapViewModel aDDI11RunMapViewModel = new ADDI11RunMapViewModel();
            if (string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {

                if (user == null) {
                    return RedirectToAction("PermissionError1999", "Error");
                }
                aDDI11RunMapViewModel.WhereSCHOOL_NO = user.SCHOOL_NO;
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }
            if (!string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                aDDI11RunMapViewModel.WhereSCHOOL_NO = model.WhereSCHOOL_NO;
             
            }
            if (!string.IsNullOrEmpty(model.OrdercColumn))
            {

                aDDI11RunMapViewModel.OrdercColumn = model.OrdercColumn;
                aDDI11RunMapViewModel.SyntaxName = model.SyntaxName;
            }
              
            List<ADDI11RunCityCount> aDDI11RunCityCounts = new List<ADDI11RunCityCount>();
            aDDI11RunCityCounts = Service.MyRunMapCount(aDDI11RunMapViewModel, ref db);
            model.RunCity = aDDI11RunCityCounts;

            return PartialView(model);
        }
        public ActionResult _PageRunCountLogInfo(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-跑步紀錄");
            ADDI11RunMapViewModel aDDI11RunMapViewModel = new ADDI11RunMapViewModel();
            if (string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                if (user == null) {

                    return RedirectToAction("PermissionError1999", "Error");
                }
                aDDI11RunMapViewModel.WhereSCHOOL_NO = user.SCHOOL_NO;
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }
            if (!string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                aDDI11RunMapViewModel.WhereSCHOOL_NO = model.WhereSCHOOL_NO;

            }
            if (!string.IsNullOrEmpty(model.OrdercColumn))
            {

                aDDI11RunMapViewModel.OrdercColumn = model.OrdercColumn;
                aDDI11RunMapViewModel.SyntaxName = model.SyntaxName;
            }

            List<ADDI11RunCityCount> aDDI11RunCityCounts = new List<ADDI11RunCityCount>();
            aDDI11RunCityCounts = Service.MyRunMapClassCount(aDDI11RunMapViewModel, ref db);
            model.RunCity = aDDI11RunCityCounts;

            return PartialView(model);
        }
        public ActionResult _PageRunDetailLog(string WhereSCHOOL_NO ,string WhereKMS, string OrdercColumn , string SyntaxName) {
            ADDI11RunMapViewModel aDDI11RunMapViewModel = new ADDI11RunMapViewModel();
            if (!string.IsNullOrEmpty(WhereSCHOOL_NO))
            {
                aDDI11RunMapViewModel.WhereSCHOOL_NO = WhereSCHOOL_NO;
            }
            if (!string.IsNullOrEmpty(WhereKMS))
            {
                aDDI11RunMapViewModel.WhereKM = WhereKMS;
            }
            if (!string.IsNullOrEmpty(OrdercColumn))
            {

                aDDI11RunMapViewModel.OrdercColumn = OrdercColumn;
                aDDI11RunMapViewModel.SyntaxName = SyntaxName;
            }
            ADDI11RunCityCount aDDI11RunCityCountInfo = new ADDI11RunCityCount();
            aDDI11RunCityCountInfo = Service.MyRunMapCountDetail(aDDI11RunMapViewModel, ref db);
            return PartialView(aDDI11RunCityCountInfo);
        }
        public ActionResult RunCountLog(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-全校跑步里程一覽");
       
            return View(model);
        }
        public ActionResult RunCountLogIstory(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-全校跑步里程一覽");

            return View(model);
        }
        public ActionResult RunCountLogInfo(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-班級跑步總和排行");

            return View(model);
        }
        public ActionResult RunCountLogInfoIstory(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-班級跑步總和排行");

            return View(model);
        }
        public ActionResult RunMap(ADDI11RunMapViewModel model)
        {
            this.Shared(Bre_Name + "-我的跑步地圖");
            if (model == null) model = new ADDI11RunMapViewModel();

            if (string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }

            if (string.IsNullOrEmpty(model.WhereUSER_NO))
            {
                model.WhereUSER_NO = user.USER_NO;
            }

            if (string.IsNullOrEmpty(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = user.CLASS_NO;
            }

            model = Service.GetMyRunMap(model, ref db);
            ViewBag.UName = user.NAME;
            ViewBag.USERNOTitle = user.USER_TYPE == UserType.Student ? "小朋友" : user.USER_TYPE == UserType.Teacher ? "老師" : "";

            return View(model);
        }
        public ActionResult RunMapIstory(ADDI11RunMapViewModel model)
        {
            this.Shared(Bre_Name + "-我的跑步地圖");
            if (model == null) model = new ADDI11RunMapViewModel();

            if (string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }

            if (string.IsNullOrEmpty(model.WhereUSER_NO))
            {
                model.WhereUSER_NO = user.USER_NO;
            }

            if (string.IsNullOrEmpty(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = user.CLASS_NO;
            }

            model = Service.GetMyRunMap(model, ref db);
            ViewBag.UName = user.NAME;
            ViewBag.USERNOTitle = user.USER_TYPE == UserType.Student ? "小朋友" : user.USER_TYPE == UserType.Teacher ? "老師" : "";

            return View(model);
        }
        
        public ActionResult MyRunLog(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-我的跑步紀錄");
            return View(model);
        }
        public ActionResult MyRunLogIstory(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-我的跑步紀錄");
            return View(model);
        }
        public ActionResult IndexAPP()
        {
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
            return base.View();
        }

        public ActionResult _PageMyRunLog(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-我的跑步紀錄");
            if (model == null) model = new ADDI11MyRunLogViewModel();

            if (string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }

            if (string.IsNullOrEmpty(model.WhereUSER_NO))
            {
                model.WhereUSER_NO = user.USER_NO;
            }

            if (string.IsNullOrEmpty(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = user.CLASS_NO;
            }

            model = Service.GetMyRunLog(model, user, ref db);

            model.MyRunColumnChart = GetCashPreColumnChart(model.ColumnListData);

            return PartialView(model);
        }

        public ActionResult MyFriendsRunLog(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-我的好朋友跑步紀錄");
            return View(model);
        }
        public ActionResult MyFriendsRunLogIstory(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-我的好朋友跑步紀錄");
            return View(model);
        }
        public ActionResult _PageMyFriendsRunLog(ADDI11MyRunLogViewModel model)
        {
            this.Shared(Bre_Name + "-我的好朋友跑步紀錄");
            UserProfile user = UserProfileHelper.Get();
            if (model == null)
            { model = new ADDI11MyRunLogViewModel(); }

            // 好友們

            List<ADDI11MyFriendsViewModel> myFriends = db.HRMT07
                .Join(db.HRMT01,
                (h7) => new { h7.SCHOOL_NO, USER_NO = h7.STUDENT_USER_NO },
                (h1) => new { h1.SCHOOL_NO, h1.USER_NO },
                (h7, h1) => new { h7 = h7, h1 = h1 })
                .GroupJoin(db.ADDT25,
                (h7h1) => new { h7h1.h7.SCHOOL_NO, USER_NO = h7h1.h7.STUDENT_USER_NO },
                (ad25) => new { ad25.SCHOOL_NO, ad25.USER_NO },
                (h7h1, ad25) => new { h7h1 = h7h1, ad25 = ad25 })
                .SelectMany(f => f.ad25.DefaultIfEmpty(), (x, y) => new { h7h1 = x.h7h1, ad25 = y })
                .Where(f => f.h7h1.h7.SCHOOL_NO == user.SCHOOL_NO && f.h7h1.h7.USER_NO == user.USER_NO)
                .Select(h => new ADDI11MyFriendsViewModel()
                {
                    NAME = h.h7h1.h1.NAME,
                    USER_NO = h.h7h1.h7.STUDENT_USER_NO,
                    SCHOOL_NO = h.h7h1.h1.SCHOOL_NO,
                    CLASS_NO = h.h7h1.h1.CLASS_NO,
                    RUN_TOTAL_METER = h.ad25.RUN_TOTAL_METER ?? 0
                }).ToList();

            model.MyFriends = myFriends;

            // MyFriends(包括我)
            int my_run_total_meter = db.ADDT25.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO)
                .FirstOrDefault()?.RUN_TOTAL_METER ?? 0;
            model.MyFriends.Add(new ADDI11MyFriendsViewModel()
            {
                CLASS_NO = user.CLASS_NO,
                SCHOOL_NO = user.SCHOOL_NO,
                NAME = user.NAME + " (我)",
                USER_NO = user.USER_NO,
                RUN_TOTAL_METER = my_run_total_meter
            });

            if (string.IsNullOrEmpty(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = model.MyFriends.FirstOrDefault()?.SCHOOL_NO;
            }

            if (string.IsNullOrEmpty(model.WhereUSER_NO))
            {
                model.WhereUSER_NO = model.MyFriends.FirstOrDefault()?.USER_NO;
            }

            if (string.IsNullOrEmpty(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = model.MyFriends.FirstOrDefault()?.CLASS_NO;
            }

            // 取朋友跑步紀錄
            model = Service.GetMyRunLog(model, user, ref db);
            // 取朋友跑步紀錄詳細Chart
            model.MyRunColumnChart = GetCashPreColumnChart(model.ColumnListData);
            // 取各朋友跑步紀錄比較表
            model.FriendsCompareColumnChart = GetFriendsCompareColumnChart(model.MyFriends);

            return PartialView(model);
        }

        public ActionResult PrintRunForm()
        {
            this.Shared(Bre_Name + "-列印跑步總表");
            var model = new ADDI11PrintRunFormViewModel();
            int standard = db.BDMT01.Where(b => b.SCHOOL_NO == SCHOOL_NO).FirstOrDefault()?
                .RUN_PEOPLE_STANDARD ?? 8; // 預設達標為8人
            model.RunPeopleStandard = standard;
            //導師帶的班
            string bringClass = db.HRMT03.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.TEACHER_NO == user.USER_NO).FirstOrDefault()?.CLASS_NO;
            model.whereCLASS_NO = bringClass;

            var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value })
                .ToList();
            ViewBag.ClassItems = ClassItems;

            return View(model);
        }

        [HttpPost]
        public ActionResult PrintRunForm(ADDI11PrintRunFormViewModel model)
        {
            this.Shared(Bre_Name + "-列印跑步總表");

            //導師帶的班
            string bringClass = db.HRMT03.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.TEACHER_NO == user.USER_NO).FirstOrDefault()?.CLASS_NO;

            var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value })
                .ToList();
            ViewBag.ClassItems = ClassItems;

            // 檢查時間不能超過 22週 * 一週7天
            if (model.EndDate != null && model.StartDate != null)
            {
                if ((model.EndDate - model.StartDate).Value.TotalDays > 22 * 7)
                {
                    ModelState.AddModelError("DaysOverflowError", "日期間隔不可以超過22週。");
                }
                if (model.EndDate <= model.StartDate)
                {
                    ModelState.AddModelError("DaysOverflowError", "結束日期不可以小於開始日期。");
                }
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }
            ModelState.Clear();

            // Excel檔案建立及匯出
            RunReport report = new RunReport()
            {
                ClassNo = model.whereCLASS_NO,
                SchoolNo = SCHOOL_NO,
                SemesterStart = (DateTime)model.StartDate,
                SemesterEnd = (DateTime)model.EndDate,
                RunPeopleStandard = model.RunPeopleStandard
            };
            IWorkbook workbook = report.BuildWorkBook();
            using (System.IO.MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                byte[] bytes = ms.ToArray();
                report.Dispose();

                return File(bytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", model.whereCLASS_NO + "登記運動撲滿.xlsx");
            }
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult TotalRunLog(ADDI11TotalRunLogViewModel model)
        {
            // 全校跑步
            this.Shared(Bre_Name + "-本校跑步情形");

            model.runLogViewType = TotalRunLogType.全校跑步情形;
            model.whereSCHOOL_NO = user.SCHOOL_NO;

            model = Service.GetTotalRunLog(model, user, ref db);

            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult TotalClassRunLog(ADDI11TotalRunLogViewModel model)
        {
            // 全班跑步
            this.Shared(Bre_Name + "-本班跑步情形");

            model.runLogViewType = TotalRunLogType.本班跑步情形;
            model.whereSCHOOL_NO = user.SCHOOL_NO;

            model = Service.GetTotalRunLog(model, user, ref db);

            return View("TotalRunLog", model);
        }

        /// <summary>
        ///  酷幣各類別點數 長條圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        public Highcharts GetCashPreColumnChart(List<ADDI11MyRunLogDataViewModel> PerData)
        {
            var returnPoint = new List<DotNet.Highcharts.Options.Point>();

            foreach (var item in PerData.OrderBy(a => a.RUN_DATE))
            {
                returnPoint.Add(new DotNet.Highcharts.Options.Point
                {
                    Y = item.LAP_M
                    ,
                    Name = item.RUN_DATE.Value.ToString("yyyy/MM/dd")
                });
            }

            Data data = new Data(returnPoint.ToArray());

            Highcharts TempRunColumnChart = new Highcharts("TempRunColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
           })
           .SetTitle(new Title { Text = "近期跑步紀錄表" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category
                        ,
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "公尺" }, Min = 0, AllowDecimals = false })
           .SetSeries(new[]
                   {
                        new Series {
                            Data = data,
                            Name = "公尺",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 公尺</b>" })
           .SetLegend(new Legend { Enabled = false });

            chartsHelper.SetCopyright(TempRunColumnChart);

            return TempRunColumnChart;
        }

        /// <summary>
        /// 與好友累計跑步比較線性圖
        /// </summary>
        /// <returns></returns>
        public Highcharts GetFriendsCompareColumnChart(List<ADDI11MyFriendsViewModel> friendList)
        {
            var returnPoint = new List<DotNet.Highcharts.Options.Point>();

            List<Series> Series = new List<Series>();

            foreach (var item in friendList)
            {
                Series.Add(new Series()
                {
                    Data = new Data(new List<DotNet.Highcharts.Options.Point>() {
                        new DotNet.Highcharts.Options.Point() {  Y = item.RUN_TOTAL_METER , Name = item.NAME }
                    }.ToArray()),
                    Name = item.NAME
                });
            }

            Highcharts FriendsCompareColumnChart = new Highcharts("FriendsCompareColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
           })
           .SetTitle(new Title { Text = "與好友的累積里程比較表" })
           .SetXAxis(new XAxis
           {
               Labels = new XAxisLabels
               {
                   Enabled = false
               }
           })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "公尺" }, Min = 0, AllowDecimals = false })
           .SetSeries(Series.ToArray())
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{series.name}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 公尺</b>" })
           .SetLegend(new Legend { Enabled = true });

            chartsHelper.SetCopyright(FriendsCompareColumnChart);

            return FriendsCompareColumnChart;
        }

        /// <summary>
        /// 任務清單
        /// </summary>
        /// <returns></returns>
        public ActionResult SecretMission()
        {
            this.Shared(Bre_Name + "-神秘任務");
            List<ADDI11SecretMissionListViewModel> model = new List<ADDI11SecretMissionListViewModel>();
            model = this.Service.GetSecretMissionTaskList(user, ref db);
            return View(model);
        }

        /// <summary>
        /// 任務 Edit
        /// </summary>
        /// <returns></returns>
        public ActionResult SecretMissionEdit(string SOU_SCHOOL_NO, int SOU_ITEM_NO)
        {
            this.Shared(Bre_Name + "-神秘任務編輯");
            ADDI11SecretMissionEditViewModel model = new ADDI11SecretMissionEditViewModel();
            model.Task = this.Service.GetThisSecretMission(user, ref db, SOU_SCHOOL_NO, SOU_ITEM_NO);
            model.Ans = this.Service.GetThisAnsSecretMission(user, ref db, SOU_SCHOOL_NO, SOU_ITEM_NO);

            if (model.Ans != null)
            {
                if (!string.IsNullOrWhiteSpace(model.Ans.UPLOAD_FILES))
                {
                    model.FilePaths = ADDI11Service.GetFilePath(model.Ans.SCHOOL_NO, model.Ans.USER_NO, model.Ans.SOU_SCHOOL_NO, model.Ans.SOU_ITEM_NO, 2);
                    model.FileName = model.Ans.UPLOAD_FILES.Split('|');
                }
            }

            return View(model);
        }

        /// <summary>
        /// 任務存檔
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult SecretMissionSave(ADDI11SecretMissionEditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ADDI11SecretMissionEditViewModel();

            string Message = string.Empty;

            model.Task = this.Service.GetThisSecretMission(model.Task.SCHOOL_NO, model.Task.ITEM_NO, ref db);

            if ((model.Task.IS_TEXT ?? false) && string.IsNullOrWhiteSpace(model.Ans.ANSWERS))
            {
                ModelState.AddModelError("Ans.ANSWERS", "*未回答題目");
            }

            int UploadFilesCount = FileHelper.GetUploadCount(model.UploadFiles);

            if (model.Task.FILE_COUNT > 0)
            {
                if (string.IsNullOrWhiteSpace(model.Ans.UPLOAD_FILES))
                {
                    if (model.UploadFiles == null)
                    {
                        ModelState.AddModelError("UploadFiles", "*未上傳照片");
                    }
                    else if (model.Task.FILE_COUNT != UploadFilesCount)
                    {
                        ModelState.AddModelError("UploadFiles", "*上傳的照片數有誤，需上傳" + model.Task.FILE_COUNT + "張");
                    }
                }
                else
                {
                    if (UploadFilesCount > 0)
                    {
                        if (model.Task.FILE_COUNT != UploadFilesCount)
                        {
                            ModelState.AddModelError("UploadFiles", "*上傳的照片數有誤，需上傳" + model.Task.FILE_COUNT + "張");
                        }
                    }
                }
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                bool OK = this.Service.SaveSecretMission(model, user, ref db, ref Message,ref valuesList);

                if (OK)
                {
                    ModelState.Clear();

                    if (string.IsNullOrWhiteSpace(model.Ans.USER_NO))
                    {
                        TempData["StatusMessage"] = "完成任務";
                    }
                    else
                    {
                        TempData["StatusMessage"] = "修改完成";
                    }

                    return RedirectToAction("SecretMission");
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            if (model.Ans != null)
            {
                if (!string.IsNullOrWhiteSpace(model.Ans.UPLOAD_FILES))
                {
                    model.FilePaths = ADDI11Service.GetFilePath(model.Ans.SCHOOL_NO, model.Ans.USER_NO, model.Task.SCHOOL_NO, model.Task.ITEM_NO, 2);
                    model.FileName = model.Ans.UPLOAD_FILES.Split('|');
                }
            }

            return View("SecretMissionEdit", model);
        }

        public ActionResult _RunMenu(string NowAction)
        {
            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            this.Shared();
            ViewBag.NowAction = NowAction;

            // 檢查按鈕權限
            ViewBag.LookSchoolRunPermission =
                PermissionService.GetPermission_Use_YN(Bre_NO, "LookSchoolRunPermission", SCHOOL_NO, USER_NO) != "N";
            ViewBag.LookClassRunPermission =
                PermissionService.GetPermission_Use_YN(Bre_NO, "LookClassRunPermission", SCHOOL_NO, USER_NO) != "N";

            ViewBag.CanPrintRunForm =
                PermissionService.GetPermission_Use_YN(Bre_NO, "CanPrintRunForm", SCHOOL_NO, USER_NO) != "N";

            ViewBag.SecretMissionCount = this.Service.GetSecretMissionCount(user, ref db);

            return PartialView();
        }
        public ActionResult _RunMenuIstory(string NowAction)
        {
            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            this.Shared();
            ViewBag.NowAction = NowAction;

            // 檢查按鈕權限
            ViewBag.LookSchoolRunPermission =
                PermissionService.GetPermission_Use_YN(Bre_NO, "LookSchoolRunPermission", SCHOOL_NO, USER_NO) != "N";
            ViewBag.LookClassRunPermission =
                PermissionService.GetPermission_Use_YN(Bre_NO, "LookClassRunPermission", SCHOOL_NO, USER_NO) != "N";

            ViewBag.CanPrintRunForm =
                PermissionService.GetPermission_Use_YN(Bre_NO, "CanPrintRunForm", SCHOOL_NO, USER_NO) != "N";

            ViewBag.SecretMissionCount = this.Service.GetSecretMissionCount(user, ref db);

            return PartialView();
        }
        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
                ViewBag.Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
                ViewBag.Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}