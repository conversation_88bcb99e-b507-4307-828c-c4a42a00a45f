﻿@model List<AWAT06>
@{
    ViewBag.Title = "角色娃娃-展示櫥窗";
    string ImageUrl = Url.Content(@"~/Content/Players/");

    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<link rel="stylesheet" type="text/css" href="~/Content/css/Gallerydemo.css?@DateNowStr" />
<link rel="stylesheet" type="text/css" href="~/Content/css/Gallerystyle.css?@DateNowStr" />

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@helper  buttonFun()
{
    <div class="row text-right">
        選擇分類：
        @foreach (string pt in ViewBag.PlayerTypeList)
        {
            @Html.ActionLink(pt, "Gallery", new { PlayerType = pt }, new { @class = "btn btn-xs btn-pink" })
        }
    </div>
}

@if (ViewBag.VisibleMyGallery == "Y")
{
    <a class="btn btn-sm btn-sys" href='@Url.Action("MyGallery","AWAI02")'>
        我的角色娃娃
    </a>
}
&nbsp;
@if (ViewBag.VisibleIndex == "Y")
{
    <a class="btn btn-sm btn-sys" href='@Url.Action("Index","AWAI02")'>
        管理角色娃娃
    </a>
}
<br />
<span style="font-size:20px">
    角色娃娃可以妝點你的文章，也可以裝飾個人頁面的右下角娃娃，
    但不能拿到實際的物品，也不能像寶可夢一樣對戰。<b style="color:orangered">角色娃娃兌換後就不能退還酷幣點數，請仔細思考後再兌換。</b>
</span><br />
@buttonFun()
<img src="~/Content/img/web-bar2-revise-18.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />

<div class="Div-EZ-AWAI02">
    <div class="Details">
        <div class="g-container">
            <!-- Codrops top bar -->

            <section id="dg-container" class="dg-container">
                <div class="dg-wrapper">
                    @foreach (AWAT06 player in Model)
                    {
                        string PlayerImageUrl = ImageUrl + player.IMG_FILE;

                        <a id="@player.PLAYER_NO" href='@Url.Action("Buy", "AWAI02", new { PLAYER_NO = player.PLAYER_NO })'>
                            @*@player.PLAYER_NAME*@
                            <img src='@PlayerImageUrl' style="width:100%" class="img-responsive " alt="Responsive image" />
                            <div>
                                <text>兌換點數：</text>
                                @player.COST_CASH
                            </div>
                        </a>
                    }
                </div>
                <nav>
                    <span class="dg-prev">&lt;</span>
                    <span class="dg-next">&gt;</span>
                </nav>
            </section>
        </div>
    </div>
</div>

<script src="~/Scripts/jquery.gallery.js?@DateNowStr"></script>
<script type="text/javascript">
    $(function () {
        $('#dg-container').gallery();
    });
</script>