/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/fontdata-extra.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(u){var w="2.7.5";var l=u.FONTDATA.DELIMITERS;var n="H",d="V";var b="NeoEulerMathJax_Alphabets",s="NeoEulerMathJax_Arrows",x="NeoEulerMathJax_Fraktur",r="NeoEulerMathJax_Main",i="NeoEulerMathJax_Marks",v="NeoEulerMathJax_NonUnicode",o="NeoEulerMathJax_Normal",y="NeoEulerMathJax_Operators",m="NeoEulerMathJax_Script",c="NeoEulerMathJax_Shapes",j="NeoEulerMathJax_Size1",h="NeoEulerMathJax_Size2",g="NeoEulerMathJax_Size3",f="NeoEulerMathJax_Size4",e="NeoEulerMathJax_Size5",q="NeoEulerMathJax_Symbols",k="NeoEulerMathJax_Variants",t="NeoEulerMathJax_Normal",a="NeoEulerMathJax_Normal",A="NeoEulerMathJax_Normal";var z={8260:{dir:d,HW:[[912,r],[1199,j],[1799,h],[2399,g],[2999,f]]},8417:{dir:n,HW:[[449,i]],stretch:{left:[8406,i],rep:[57348,e],right:[8407,r]}},8430:{dir:n,HW:[[418,i]],stretch:{left:[8430,i],rep:[57349,e]}},8431:{dir:n,HW:[[418,i]],stretch:{rep:[57349,e],right:[8431,i]}},8719:{dir:d,HW:[[1000,y],[1400,j]]},8720:{dir:d,HW:[[1000,y],[1400,j]]},8721:{dir:d,HW:[[1000,y],[1400,j]]},8743:{dir:d,HW:[[718,r],[998,j],[1395,h]]},8744:{dir:d,HW:[[700,r],[998,j],[1395,h]]},8745:{dir:d,HW:[[600,r],[965,j],[1358,h]]},8746:{dir:d,HW:[[600,r],[965,j],[1358,h]]},8747:{dir:d,HW:[[1111,r],[2222,j]]},8748:{dir:d,HW:[[1111,y],[2222,j]]},8749:{dir:d,HW:[[1111,y],[2222,j]]},8750:{dir:d,HW:[[1111,y],[2222,j]]},8846:{dir:d,HW:[[600,r],[965,j],[1358,h]]},8896:{dir:d,HW:[[718,y],[998,j],[1395,h]]},8897:{dir:d,HW:[[700,y],[998,j],[1395,h]]},8898:{dir:d,HW:[[600,y],[965,j],[1358,h]]},8899:{dir:d,HW:[[600,y],[965,j],[1358,h]]},9180:{dir:n,HW:[[925,r],[1199,j],[1799,h],[2399,g],[2999,f]],stretch:{left:[57353,e],rep:[57354,e],right:[57355,e]}},9181:{dir:n,HW:[[925,r],[1199,j],[1799,h],[2399,g],[2999,f]],stretch:{left:[57356,e],rep:[57357,e],right:[57358,e]}},10764:{dir:d,HW:[[1111,y],[2222,j]]}};for(var p in z){if(z.hasOwnProperty(p)){l[p]=z[p]}}MathJax.Ajax.loadComplete(u.fontDir+"/fontdata-extra.js")})(MathJax.OutputJax.SVG);
