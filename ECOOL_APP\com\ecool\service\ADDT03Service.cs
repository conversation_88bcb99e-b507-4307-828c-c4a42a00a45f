﻿using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.dao;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Web.Mvc;

namespace com.ecool.service
{
    public class ADDT03Service
    {
        public static List<uADDT03> USP_ReadADDT03_QUERY(string USER_NO, string SCHOOL_NO, string GRADE)
        {

            List<uADDT03> list_data = new List<uADDT03>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT SCHOOL_NO,BOOK_ID, GRADE , "); 
                sb.Append(" CASE GRADE ");
                sb.Append("      WHEN 1 THEN '一年級' ");
                sb.Append("      WHEN 2 THEN '二年級' ");
                sb.Append("      WHEN 3 THEN '三年級' ");
                sb.Append("      WHEN 4 THEN '四年級' ");
                sb.Append("      WHEN 5 THEN '五年級' ");
                sb.Append("      WHEN 6 THEN '六年級' ");
                sb.Append("      End as CGRADE ,BOOK_NAME,BOOK_ISBN,BOOK_BARCODE ");
                sb.Append(" FROM ADDT03 A03 WHERE ");
                sb.AppendFormat("A03.BOOK_NAME  Not  IN (SELECT A05.BOOK_NAME FROM ADDT05 A05 WHERE USER_NO='{0}' AND  SCHOOL_NO='{1}' ) AND  SCHOOL_NO='{1}' and  BOOK_NAME !='' ", USER_NO, SCHOOL_NO);
                if (GRADE != string.Empty)
                {
                    sb.AppendFormat(" AND GRADE='{0}' ",  GRADE);
                }

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());    
       
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uADDT03()
                    {
                        SCHOOL_NO = dr["SCHOOL_NO"].ToString(),
                        BOOK_ID = dr["BOOK_ID"].ToString(),
                        GRADE = dr["GRADE"].ToString(),
                        CGRADE = dr["CGRADE"].ToString(),
                        BOOK_NAME = dr["BOOK_NAME"].ToString(),
                        BOOK_ISBN = dr["BOOK_ISBN"].ToString(),
                        BOOK_BARCODE = dr["BOOK_BARCODE"].ToString()
                    });
                }

            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }
        public static int UPLoginCount(string USER_NO, string SCHOOL_NO)
        {

            StringBuilder sb = new StringBuilder();
            StringBuilder sb1 = new StringBuilder();
            int LoginCount = 0;
            DataTable dt;
            try
            {
                sb.Append("SELECT LoginCount  FROM HRMT01 where SCHOOL_NO='{0}' and USER_NO='{1}' ");
                   //sb.Append("select (select count(*) from(select  FORMAT ( ACTIONTIME ,'dd/MM/yyyy, hh:mm') as ss  from  ZZT17 b  (nolock) where a.SCHOOL_NO =b.SCHOOL_NO and a.USER_NO=b.USER_NO and b.ACTIONTIME >=  DATEADD(year,-1,getdate()) and b.LOG_STATUS='LoginSuccess'  and b.ACTION_ID not like '%GET_session%' group by FORMAT ( ACTIONTIME ,'dd/MM/yyyy, hh:mm')) as h) as LoginCount");
                   //sb.Append(" from HRMT01 a (nolock)");
                   //sb.AppendFormat(" Where  a.SCHOOL_NO ='{0}'  and a.USER_TYPE = 'S'and  a.USER_STATUS <>'9' and a.USER_NO='{1}'", SCHOOL_NO, USER_NO);

                   dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {

                    string LoginCountstr = "";
                    LoginCountstr = dr["LoginCount"].ToString();
                    if (!string.IsNullOrWhiteSpace(LoginCountstr))
                    {
                        LoginCount = Int32.Parse(LoginCountstr) + 1;
                    }
                    else {
                        LoginCount = 0;

                    }

                }
                if (LoginCount > 0) {
                    sb1.AppendFormat("UPDATE HRMT01 set LoginCount={0} where SCHOOL_NO='{1}' and USER_NO='{2}' and USER_STATUS <>'9'", LoginCount, SCHOOL_NO, USER_NO);
                    new sqlConnection.sqlConnection().execute(sb1.ToString());
                }
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return LoginCount;
            }
     

    }
}
