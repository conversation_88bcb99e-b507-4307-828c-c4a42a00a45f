.heart {
    width: 500px;
    height: 500px;
    background-position: 0 0;
    cursor: pointer;
    transition-duration: 0s;
    background: url("../images/like.png") no-repeat;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -2;
}

    .heart.active {
        transition-duration: 1s;
        transition: background-position 2.5s steps(19);
        background-position: -9500px 0;
        z-index: 9999;
    }

.thumb {
    width: 500px;
    height: 500px;
    background-position: 0 0;
    cursor: pointer;
    transition-duration: 0s;
    background: url("../images/thumb.png") no-repeat;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -2;
}

    .thumb.active {
        transition-duration: 1s;
        transition: background-position 2.5s steps(19);
        background-position: -9500px 0;
        z-index: 9999;
    }

.placement {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}