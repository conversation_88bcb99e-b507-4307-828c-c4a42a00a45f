(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: Build Table - updated 2018-03-26 (v2.30.2) */
!function(y){"use strict";var f=y.tablesorter=y.tablesorter||{},_=f.buildTable=function(e,o){y.extend(!0,f.defaults.widgetOptions,_.defaults);var l="TABLE"===e.nodeName?y(e):y("<table>").appendTo(e),r=l[0],n=o.widgetOptions=y.extend(!0,{},_.defaults,o.widgetOptions),i=n.build_processing,d=n.build_type,t=n.build_source||o.data,s=f.debug(o,"build"),a=function(e){var t=y.type(e),l=e instanceof y;if("function"==typeof i&&(e=i(e,n)),o.data=e,l||"string"===t){if(l||/<\s*\/tr\s*>/.test(e))return _.html(r,e,n);try{if(e=y.parseJSON(e||"null"))return _.object(r,e,n)}catch(e){}}return"array"===t||"string"===t||"array"===d||"csv"===d?_.csv(r,e,n):_.object(r,e,n)};if(r.config=o,!f.buildTable.hasOwnProperty(d)&&""!==d)return s&&console.error("Build >> ERROR: Aborting build table widget, incorrect build type"),!1;t instanceof y?a(y.trim(t.html())):t&&(t.hasOwnProperty("url")||"json"===d)?y.ajax(n.build_source).done(function(e){a(e)}).fail(function(e,t){s&&console.error("Build >> ERROR: Aborting build table widget, failed ajax load"),l.html('<tr><td class="error">'+e.status+" "+t+"</td></tr>")}):a(t)};f.defaults.data="",_.defaults={build_type:"",build_source:"",build_processing:null,build_complete:"tablesorter-build-complete",build_headers:{rows:1,classes:[],text:[],widths:[]},build_footers:{rows:1,classes:[],text:[]},build_numbers:{addColumn:!1,sortable:!1},build_csvStartLine:0,build_csvSeparator:",",build_objectRowKey:"rows",build_objectCellKey:"cells",build_objectHeaderKey:"headers",build_objectFooterKey:"footers"},_.build={colgroup:function(e){var l="";return e&&e.length&&(l+="<colgroup>",y.each(e,function(e,t){l+="<col"+(t?' style="width:'+t+'"':"")+">"}),l+="</colgroup>"),l},cell:function(e,t,l,o,r){var n,i,d=r?y("<col>"):"",s=t.build_headers.classes,a=t.build_headers.widths;if(/string|number/.test(typeof e))i=y("<"+l+(s&&s[o]?' class="'+s[o]+'"':"")+">"+e+"</"+l+">"),r&&a&&a[o]&&d.width(a[o]||"");else for(n in i=y("<"+l+">"),e)e.hasOwnProperty(n)&&("text"===n||"html"===n?i[n](e[n]):r&&"width"===n?d.width(e[n]||""):i.attr(n,e[n]));return[i,d]},header:function(e,t){var l=t.build_headers.text,o=t.build_headers.classes,r="<tr>"+(t.build_numbers.addColumn?"<th"+(t.build_numbers.sortable?"":' class="sorter-false"')+">"+t.build_numbers.addColumn+"</th>":"");return y.each(e,function(e,t){/<\s*\/t(d|h)\s*>/.test(t)?r+=t:r+="<th"+(o&&o[e]?' class="'+o[e]+'"':"")+">"+(l&&l[e]?l[e]:t)+"</th>"}),r+"</tr>"},rows:function(e,l,o,t,r,n){var i=n?"th":"td",d="<tr>"+(t.build_numbers.addColumn?"<"+i+">"+(n?"":r)+"</"+i+">":"");return y.each(e,function(e,t){/<\s*\/t(d|h)\s*>/.test(t)?d+=t:d+="<"+(n?i+(o&&o[e]?' class="'+o[e]+'"':""):i)+">"+(n&&l&&l.length&&l[e]?l[e]:t)+"</"+i+">"}),d+"</tr>"}},_.buildComplete=function(e,t){y(e).triggerHandler(t.build_complete),e.config&&f.debug(e.config,"build")&&console.log("Build >> Table build complete"),f.setup(e,e.config)},_.array=function(e,t,l){return _.csv(e,t,l)},_.csv=function(e,t,l){var o,r,n,i="csv"===l.build_type||"string"==typeof t,d=y(e),s=i?t.replace("\r","").split("\n"):t,a=s.length,u=0,c=!1,b=l.build_headers.rows+(i?l.build_csvStartLine:0),h=l.build_footers.rows,p=0,f="",g=_.build.colgroup(l.build_headers.widths)+"<thead>";y.each(s,function(e,t){a-h<=e&&(c=!0),(!i||e>=l.build_csvStartLine)&&e<b?(r=i?_.splitCSV(t,l.build_csvSeparator):t,p=r.length,g+=_.build.header(r,l)):b<=e&&(e===b&&(g+="</thead><tbody>"),n=i?_.splitCSV(t,l.build_csvSeparator):t,c&&0<h&&(g+=(e===a-h?"</tbody><tfoot>":"")+(e===a?"</tfoot>":"")),1<n.length&&(u++,n.length!==p&&(f+="error on line "+e+": Item count ("+n.length+") does not match header count ("+p+") \n"),o=c?l.build_footers.classes:"",g+=_.build.rows(n,l.build_footers.text,o,l,u,c)))}),g+=0<h?"":"</tbody>",f?d.html(f):(d.html(g),_.buildComplete(e,l))},_.splitCSV=function(e,t){var l,o,r=y.trim(e).split(t=t||",");for(l=r.length-1;0<=l;l--)'"'===r[l].replace(/\"\s+$/,'"').charAt(r[l].length-1)?1<(o=r[l].replace(/^\s+\"/,'"')).length&&'"'===o.charAt(0)?r[l]=r[l].replace(/^\s*"|"\s*$/g,"").replace(/""/g,'"'):l?r.splice(l-1,2,[r[l-1],r[l]].join(t)):r=r.shift().split(t).concat(r):r[l].replace(/""/g,'"');return r},_.html=function(e,t,l){var o=y(e);t instanceof y?o.empty().append(t):o.html(t),_.buildComplete(e,l)},_.object=function(e,t,o){var l,r,n,i,d,s,a,u=e.config,c=o.build_objectHeaderKey,b=o.build_objectRowKey,h=t.hasOwnProperty(c)&&!y.isEmptyObject(t.kh)?t.kh:!!t.hasOwnProperty("headers")&&t.headers,p=t.hasOwnProperty(b)&&!y.isEmptyObject(t.kr)?t.kr:!!t.hasOwnProperty("rows")&&t.rows;if(!h||!p||0===h.length||0===p.length)return f.debug(u,"build")&&console.error("Build >> ERROR: Aborting build table widget, missing data for object build"),!1;i=y("<colgroup>"),d=y("<table><thead/></table>"),y.each(h,function(e,t){for(a=y("<tr>").appendTo(d.find("thead")),r=t.length,l=0;l<r;l++)(n=_.build.cell(t[l],o,"th",l,0===e))[0]&&n[0].length&&n[0].appendTo(a),0===e&&n[1]&&n[1].appendTo(i)}),i.find("col[style]").length&&d.prepend(i),s=y("<tbody>"),y.each(p,function(e,t){var l;if((n="object"===y.type(t))&&t.newTbody)for(l in s=y("<tbody>").appendTo(d),t)t.hasOwnProperty(l)&&"newTbody"!==l&&s.attr(l,t[l]);else{if(0===e&&s.appendTo(d),a=y("<tr>").appendTo(s),n){for(l in t)t.hasOwnProperty(l)&&l!==o.build_objectCellKey&&a.attr(l,t[l]);t.hasOwnProperty(o.build_objectCellKey)&&(t=t.cells)}for(r=t.length,l=0;l<r;l++)(i=_.build.cell(t[l],o,"td",l))[0]&&i[0].length&&i[0].appendTo(a)}}),t.hasOwnProperty(o.build_objectFooterKey)&&("clone"===(n=t[o.build_objectFooterKey])?(i=d.find("thead").html(),d.append("<tfoot>"+i+"</tfoot>")):(i=y("<tfoot>").appendTo(d),y.each(n,function(e,t){for(a=y("<tr>").appendTo(i),r=t.length,l=0;l<r;l++)(s=_.build.cell(t[l],o,"th",l))[0]&&s[0].length&&s[0].appendTo(a)}))),y(e).html(d.html()),_.buildComplete(e,o)},_.ajax=_.json=function(e,t,l){return _.object(e,t,l)}}(jQuery);return jQuery;}));
