/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/EnclosedAlphanum.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{9312:[676,14,684,0,684],9313:[676,14,684,0,684],9314:[676,14,684,0,684],9315:[676,14,684,0,684],9316:[676,14,684,0,684],9317:[676,14,684,0,684],9318:[676,14,684,0,684],9319:[676,14,684,0,684],9320:[676,14,684,0,684],9398:[676,14,684,0,684],9399:[676,14,684,0,684],9400:[676,14,684,0,684],9401:[676,14,684,0,684],9402:[676,14,684,0,684],9403:[676,14,684,0,684],9404:[676,14,684,0,684],9405:[676,14,684,0,684],9406:[676,14,684,0,684],9407:[676,14,684,0,684],9408:[676,14,684,0,684],9409:[676,14,684,0,684],9410:[676,14,684,0,684],9411:[676,14,684,0,684],9412:[676,14,684,0,684],9413:[676,14,684,0,684],9414:[676,14,684,0,684],9415:[676,14,684,0,684],9416:[676,14,684,0,684],9417:[676,14,684,0,684],9418:[676,14,684,0,684],9419:[676,14,684,0,684],9420:[676,14,684,0,684],9421:[676,14,684,0,684],9422:[676,14,684,0,684],9423:[676,14,684,0,684],9424:[676,14,684,0,684],9425:[676,14,684,0,684],9426:[676,14,684,0,684],9427:[676,14,684,0,684],9428:[676,14,684,0,684],9429:[676,14,684,0,684],9430:[676,14,684,0,684],9431:[676,14,684,0,684],9432:[676,14,684,0,684],9433:[676,14,684,0,684],9434:[676,14,684,0,684],9435:[676,14,684,0,684],9436:[676,14,684,0,684],9437:[676,14,684,0,684],9438:[676,14,684,0,684],9439:[676,14,684,0,684],9440:[676,14,684,0,684],9441:[676,14,684,0,684],9442:[676,14,684,0,684],9443:[676,14,684,0,684],9444:[676,14,684,0,684],9445:[676,14,684,0,684],9446:[676,14,684,0,684],9447:[676,14,684,0,684],9448:[676,14,684,0,684],9449:[676,14,684,0,684],9450:[676,14,684,0,684]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/EnclosedAlphanum.js");
