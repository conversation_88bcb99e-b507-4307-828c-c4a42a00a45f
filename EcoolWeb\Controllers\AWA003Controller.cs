﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using com.ecool.service;
using Dapper;
using System.Data;
using ECOOL_APP.com.ecool.util;
using System.IO;
using NPOI.SS.UserModel;
using AutoMapper;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWA003Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        // GET: AWA003
        public ActionResult Query(AWA003QueryViewModel model)
        {
            int PageSize = 20;

            if (model == null) model = new AWA003QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);

            IQueryable<HRMT01QTY> RankCash;

            RankCash = QeuryData(model);
            model.VAWA003List = RankCash.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            if (model.IsPrint)
            {
                if (model.VAWA003List.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    model.VAWA003List = RankCash.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            if (model.isCarousel)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }
        }
        // GET: AWA003
        public ActionResult Query2(AWA003QueryViewModel model)
        {
            int PageSize = 20;

            if (model == null) model = new AWA003QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);

            IQueryable<HRMT01QTY> RankCash;

            RankCash = QeuryData(model);
            model.VAWA003List = RankCash.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            if (model.IsPrint)
            {
                if (model.VAWA003List.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    model.VAWA003List = RankCash.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            if (model.isCarousel)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }
        }

        public IQueryable<HRMT01QTY> QeuryDataRank(AWA003QueryViewModel model)

        {
            IQueryable<HRMT01QTY> RankCash;

            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            string sSQL = @"select * ,DENSE_RANK()OVER(PARTITION BY  SHORT_NAME order by GA desc) as den_rank --累計酷幣


                              ,DENSE_RANK()OVER(PARTITION BY  SHORT_NAME order by SUMCASH_AVAILABLE desc) as SUMCASHden_rank --現有總資產

                              from (
                              select SCHOOL_NO, USER_NO,SHORT_NAME,CASE when  SUMCASH_AVAILABLE > CASH_ALL then SUMCASH_AVAILABLE else CASH_ALL end as GA,SYEAR ,CLASS_NO ,NAME,SEAT_NO,QTY,SUMCASH_AVAILABLE  from (
                              SELECT  SHORT_NAME=b1.SHORT_NAME,
                                 SCHOOL_NO=hh1.SCHOOL_NO,
                                 SYEAR = hh1.SYEAR,
                                 CLASS_NO = hh1.CLASS_NO,
                                 NAME = hh1.SNAME,
                                 USER_NO = hh1.USER_NO,
                                 SEAT_NO=hh1.SEAT_NO,
                                 QTY = w1.CASH_ALL,
                                 CASH_ALL =  w1.CASH_ALL,
                                CASH_AVAILABLE = w1.CASH_AVAILABLE,
	
                                   SUMCASH_AVAILABLE=w1.CASH_AVAILABLE+isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0),
                                 CASH_DEPOSIT = isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0)

                                 FROM
                                 (
	                               select a.SCHOOL_NO, a.USER_NO, case when aLog.CASH_ALL<0 then a.CASH_ALL else aLog.CASH_ALL end as CASH_ALL ,  case when  aLog.CASH_AVAILABLE <0 then a.CASH_AVAILABLE else aLog.CASH_AVAILABLE end as CASH_AVAILABLE,
                                      aLog.CASH_WORKHARD from AWAT01 a
	                                 inner join
	                                 (
	                                 select SCHOOL_NO, USER_NO, SUM(ADD_CASH_ALL) as CASH_ALL, SUM(ADD_CASH_AVAILABLE) as CASH_AVAILABLE, SUM(ADD_CASH_WORKHARD) as CASH_WORKHARD
	                                 from AWAT01_LOG (nolock)
                                     where 1=1
	                                 group by SCHOOL_NO, USER_NO
	                                 ) aLog
	                                 on a.SCHOOL_NO = aLog.SCHOOL_NO and a.USER_NO = aLog.USER_NO

                                 ) w1
                                 join BDMT01 b1  (nolock) on w1.SCHOOL_NO = b1.SCHOOL_NO
                                 join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS and USER_TYPE='S'  and CLASS_NO is not null) as g  		where SCHOOL_NO=@SCHOOL_NO
								) as ds";
            RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL, new { USER_STATUS = UserStaus.Invalid, whereSTART_CRE_DATE = model.whereSTART_CRE_DATE, whereEND_CRE_DATE = model.whereEND_CRE_DATE , SCHOOL_NO = SchoolNO }).AsQueryable();
            //if (SchoolNO != "ALL")
            //{
            //    RankCash = RankCash.Where(a => a.SCHOOL_NO == SchoolNO);
            //}
            return RankCash;
        }

        public IQueryable<HRMT01QTY> QeuryDataAwat10Rank(AWA003QueryViewModel model) {

            int PageSize = 20;

            if (model == null) model = new AWA003QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);

            IQueryable<HRMT01QTY> RankCash;
            string sSQL = @"select * from AWAT10Rank";
            RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL).AsQueryable();
            if (SchoolNO != "ALL")
            {
                RankCash = RankCash.Where(a => a.SCHOOL_NO == SchoolNO);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                RankCash = RankCash.Where(a => a.USER_NO.Contains(model.whereKeyword) || a.NAME.Contains(model.whereKeyword));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                RankCash = RankCash.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                RankCash = RankCash.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "CASH_ALL":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_ALL);
                    break;

                case "SUMCASH_AVAILABLE":
                    RankCash = RankCash.OrderByDescending(a => a.SUMCASH_AVAILABLE);
                    break;

                case "CASH_DEPOSIT":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_DEPOSIT);
                    break;

                case "CASH_AVAILABLE":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_AVAILABLE);
                    break;

                case "NAME":
                    RankCash = RankCash.OrderBy(a => a.NAME).ThenByDescending(a => a.CASH_ALL);
                    break;

                case "SEAT_NO":
                    RankCash = RankCash.OrderBy(a => a.SEAT_NO).ThenByDescending(a => a.CASH_ALL);
                    break;

                case "USER_NO":
                    RankCash = RankCash.OrderBy(a => a.USER_NO).ThenByDescending(a => a.CASH_ALL);
                    break;
                case "BOOK_QTY":
                    RankCash = RankCash.OrderBy(a => a.BOOK_QTY);
                    break;
                default:
                    RankCash = RankCash.OrderByDescending(a => a.CASH_ALL);
                    break;
            }
            return RankCash;


        }
        public IQueryable<HRMT01QTY> QeuryData(AWA003QueryViewModel model)
        {
            int PageSize = 20;

            if (model == null) model = new AWA003QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNO, null, user);

            IQueryable<HRMT01QTY> RankCash;
            if (model.WhereIsMonthTop)
            {
                string sSQL = @"


                                 SELECT SHORT_NAME,SCHOOL_NO,SYEAR,CLASS_NO,NAME,USER_NO,SEAT_NO,QTY, SUM(CASH_ALL) as CASH_ALL, CASH_AVAILABLE, SUM(CASH_DEPOSIT) as CASH_DEPOSIT FROM(
                                 SELECT  SHORT_NAME=b1.SHORT_NAME,
                                 SCHOOL_NO=hh1.SCHOOL_NO,
                                 SYEAR = hh1.SYEAR,
                                 CLASS_NO = hh1.CLASS_NO,
                                 NAME = hh1.SNAME,
                                 USER_NO = hh1.USER_NO,
                                 SEAT_NO=hh1.SEAT_NO,
                                 QTY = w1.CASH_ALL,
                                 CASH_ALL = WLog.CASH_IN,
                                 CASH_AVAILABLE = w1.CASH_AVAILABLE,
                                 SUMCASH_AVAILABLE=w1.CASH_AVAILABLE+isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0),
                                 CASH_DEPOSIT = isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0)
                                  FROM AWAT01 w1 (nolock)
                                  join BDMT01 b1  (nolock) on w1.SCHOOL_NO = b1.SCHOOL_NO
                                  join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS
                                  join AWAT01_LOG WLog  (nolock) on w1.SCHOOL_NO = WLog.SCHOOL_NO and w1.USER_NO = WLog.USER_NO
                                   where CONVERT(nvarchar(6),WLog.LOG_TIME,112) = CONVERT(nvarchar(6),GETDATE(),112)  and  hh1.CLASS_NO is not null and hh1.USER_TYPE='S'
                                ) t
                                group by SHORT_NAME,SCHOOL_NO,CLASS_NO, NAME, SYEAR,USER_NO,SEAT_NO,QTY,CASH_AVAILABLE
                                  HAVING sum(CASH_ALL)>0 ";
                RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL, new { USER_STATUS = UserStaus.Invalid }).AsQueryable();

            }
            else
            {
                string bankdatefilter1 = "", bankdatefilter2 = "", logdatefilter1 = "", logdatefilter2 = "";

                if (model.whereSTART_CRE_DATE != null)
                {
                    bankdatefilter1 = " and BANK_DATE>=@whereSTART_CRE_DATE";
                    logdatefilter1 = "and LOG_TIME>=@whereSTART_CRE_DATE";
                }
                if (model.whereEND_CRE_DATE != null)
                {
                    bankdatefilter2 = " and BANK_DATE<=@whereEND_CRE_DATE";
                    logdatefilter2 = " and LOG_TIME<=@whereEND_CRE_DATE";
                }

                string sSQL = $@"select SHORT_NAME,SCHOOL_NO,SCHOOL_NO,SYEAR,CLASS_NO,NAME,USER_NO,SEAT_NO,QTY,CASH_AVAILABLE,SUMCASH_AVAILABLE,CASH_DEPOSIT,BOOK_QTY,CASE when SUMCASH_AVAILABLE>CASH_ALL then SUMCASH_AVAILABLE else CASH_ALL end as CASH_ALL,ATD01_QTY,ADDT21_QTY,RUN_TOTAL_KM



                                from (
                                 SELECT  SHORT_NAME=b1.SHORT_NAME,
                                 SCHOOL_NO=hh1.SCHOOL_NO,
                                 SYEAR = hh1.SYEAR,
                                 CLASS_NO = hh1.CLASS_NO,
                                 NAME = hh1.SNAME,
                                 USER_NO = hh1.USER_NO,
                                 SEAT_NO=hh1.SEAT_NO,
                                 QTY = w1.CASH_ALL,
                                CASH_ALL =  +isnull((
                                 select Sum(w2.ADD_CASH_ALL) from AWAT01_LOG w2 (nolock) where w2.ADD_CASH_ALL>0 and w2.SCHOOL_NO=w1.SCHOOL_NO and w2.USER_NO=w1.USER_NO 
                                 ),0),
                                CASH_AVAILABLE = w1.CASH_AVAILABLE,
                                   SUMCASH_AVAILABLE=w1.CASH_AVAILABLE+isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0),
                                 CASH_DEPOSIT = isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0)
                                  ,BOOK_QTY=fg.BOOK_QTY
                             ,ATD01_QTY=dw.ATD01_QTY
                             ,ADDT21_QTY=dw1.ADDT21_QTY
                               ,RUN_TOTAL_KM=dw2.RUN_TOTAL_KM
                                 FROM
                                 (
	                               select a.SCHOOL_NO, a.USER_NO, case when aLog.CASH_ALL<0 then a.CASH_ALL else aLog.CASH_ALL end as CASH_ALL ,  case when  aLog.CASH_AVAILABLE <0 then a.CASH_AVAILABLE else aLog.CASH_AVAILABLE end as CASH_AVAILABLE,
                                      aLog.CASH_WORKHARD from AWAT01 a
	                                 inner join
	                                 (
	                                 select SCHOOL_NO, USER_NO, SUM(ADD_CASH_ALL) as CASH_ALL, SUM(ADD_CASH_AVAILABLE) as CASH_AVAILABLE, SUM(ADD_CASH_WORKHARD) as CASH_WORKHARD
	                                 from AWAT01_LOG (nolock)
                                     where 1=1 {logdatefilter1}{logdatefilter2}
	                                 group by SCHOOL_NO, USER_NO
	                                 ) aLog
	                                 on a.SCHOOL_NO = aLog.SCHOOL_NO and a.USER_NO = aLog.USER_NO

                                 ) w1
                                 join BDMT01 b1  (nolock) on w1.SCHOOL_NO = b1.SCHOOL_NO
                                 join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS and USER_TYPE='S'  and CLASS_NO is not null
                                  join (
									 select  CASE when a.BOOK_QTY is null then 0 else a.BOOK_QTY end as BOOK_QTY ,k.SCHOOL_NO,k.USER_NO
 ,Case when a.LEVEL_ID<10 Then (select Top 1 L.LEVEL_QTY from ADDT07 L (nolock) Where L.LEVEL_ID>a.LEVEL_ID order by L.LEVEL_ID)-a.BOOK_QTY
							       else 0 end as UNLEVEL_QTY
 from HRMT01 k left  join ADDT09 a 
on k.SCHOOL_NO=a.SCHOOL_NO and k.USER_NO=a.USER_NO and  USER_STATUS<>9 and USER_TYPE='S'  and CLASS_NO is not null
 left outer join ADDT08 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.LEVEL_ID=b.LEVEL_ID
								 ) fg on hh1.SCHOOL_NO=fg.SCHOOL_NO  and hh1.USER_NO =fg.USER_NO 
 left join (	 select  CASE when count(ATD01.SUBJECT)  is null then 0 else count(ATD01.SUBJECT) end as ATD01_QTY ,ATD02.SCHOOL_NO,ATD02.USER_NO

 from HRMT01 ATD02 left  join ADDT01 ATD01 
on ATD02.SCHOOL_NO=ATD01.SCHOOL_NO and ATD02.USER_NO=ATD01.USER_NO and  USER_STATUS<>9 and USER_TYPE='S' 
group by ATD02.SCHOOL_NO,ATD02.USER_NO
) dw on  hh1.SCHOOL_NO=dw.SCHOOL_NO  and hh1.USER_NO =dw.USER_NO 
 left join (	
 select  CASE when  count(ADT21101.ART_SUBJECT)  is null then 0 else  count(ADT21101.ART_SUBJECT)  end as ADDT21_QTY ,ADT211.SCHOOL_NO,ADT211.USER_NO

 from HRMT01 ADT211 left  join ADDT21 ADT21101 
on ADT211.SCHOOL_NO=ADT21101.SCHOOL_NO and ADT211.USER_NO=ADT21101.USER_NO and  USER_STATUS<>9 and USER_TYPE='S' 
where ART_GALLERY_TYPE='P'
group by ADT211.SCHOOL_NO,ADT211.USER_NO
) dw1 on  hh1.SCHOOL_NO=dw1.SCHOOL_NO  and hh1.USER_NO =dw1.USER_NO 
left join (	
 
 select case when (ADDT25101.RUN_TOTAL_METER)=0 or (ADDT25101.RUN_TOTAL_METER)is null then 0 else  ROUND(ADDT25101.RUN_TOTAL_METER / 1000.0, 1) end as RUN_TOTAL_KM ,ADT211.SCHOOL_NO,ADT211.USER_NO

 from HRMT01 ADT211 left  join ADDT25 ADDT25101 
on ADT211.SCHOOL_NO=ADDT25101.SCHOOL_NO and ADT211.USER_NO=ADDT25101.USER_NO and  USER_STATUS<>9 and USER_TYPE='S' 

group by ADDT25101.RUN_TOTAL_METER,ADT211.SCHOOL_NO,ADT211.USER_NO ) dw2 on  hh1.SCHOOL_NO=dw2.SCHOOL_NO  and hh1.USER_NO =dw2.USER_NO 
) as temp  group by SHORT_NAME,SCHOOL_NO,SCHOOL_NO,SYEAR,CLASS_NO,NAME,USER_NO,SEAT_NO,QTY,CASH_AVAILABLE,SUMCASH_AVAILABLE,CASH_DEPOSIT,BOOK_QTY, CASH_ALL,ATD01_QTY,ADDT21_QTY,RUN_TOTAL_KM

                                 ";

                RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL, new { USER_STATUS = UserStaus.Invalid, whereSTART_CRE_DATE = model.whereSTART_CRE_DATE, whereEND_CRE_DATE = model.whereEND_CRE_DATE }).AsQueryable();
            }

            if (SchoolNO != "ALL")
            {
                RankCash = RankCash.Where(a => a.SCHOOL_NO == SchoolNO);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                RankCash = RankCash.Where(a => a.USER_NO.Contains(model.whereKeyword) || a.NAME.Contains(model.whereKeyword));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                RankCash = RankCash.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                RankCash = RankCash.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "CASH_ALL":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_ALL);
                    break;

                case "SUMCASH_AVAILABLE":
                    RankCash = RankCash.OrderByDescending(a => a.SUMCASH_AVAILABLE);
                    break;

                case "CASH_DEPOSIT":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_DEPOSIT);
                    break;

                case "CASH_AVAILABLE":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_AVAILABLE);
                    break;

                case "NAME":
                    RankCash = RankCash.OrderBy(a => a.NAME).ThenByDescending(a => a.CASH_ALL);
                    break;

                case "SEAT_NO":
                    RankCash = RankCash.OrderBy(a => a.SEAT_NO).ThenByDescending(a => a.CASH_ALL);
                    break;

                case "USER_NO":
                    RankCash = RankCash.OrderBy(a => a.USER_NO).ThenByDescending(a => a.CASH_ALL);
                    break;
                case "BOOK_QTY":
                     RankCash = RankCash.OrderBy(a => a.BOOK_QTY);
                    break;
                default:
                    RankCash = RankCash.OrderByDescending(a => a.CASH_ALL);
                    break;
            }
            return RankCash;
        }

        public IQueryable<HRMT01QTY> rturnSimple(AWA003QueryViewModel model) {
            IQueryable<HRMT01QTY> RankCash;
            string bankdatefilter1 = "";
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            if (model.OrdercColumn != null && model.OrdercColumn == "CASH_ALL")
            {
                bankdatefilter1 = "  order by CASH_ALL desc ";
              
            }
            else
            {


                bankdatefilter1 = "  order by SUMCASH_AVAILABLE desc";
            }
            string sSQL = $@"SELECT   TOP 10 CASH_ALL =  w1.CASH_ALL,  SUMCASH_AVAILABLE=w1.CASH_AVAILABLE+isnull((
                                 select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                 ),0), CLASS_NO = hh1.CLASS_NO,
                                 NAME = hh1.SNAME
                                

                                 FROM
                                 (
	                                 select a.SCHOOL_NO, a.USER_NO, aLog.CASH_ALL, aLog.CASH_AVAILABLE, aLog.CASH_WORKHARD from AWAT01 a
	                                 inner join
	                                 (
	                                 select SCHOOL_NO, USER_NO, SUM(ADD_CASH_ALL) as CASH_ALL, SUM(ADD_CASH_AVAILABLE) as CASH_AVAILABLE, SUM(ADD_CASH_WORKHARD) as CASH_WORKHARD
	                                 from AWAT01_LOG (nolock)
                             
	                                 group by SCHOOL_NO, USER_NO
	                                 ) aLog
	                                 on a.SCHOOL_NO = aLog.SCHOOL_NO and a.USER_NO = aLog.USER_NO

                                 ) w1
                                 join BDMT01 b1  (nolock) on w1.SCHOOL_NO = b1.SCHOOL_NO
                                 join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS and USER_TYPE='S'  and CLASS_NO is not null and  w1.SCHOOL_NO= @SchoolNO {bankdatefilter1}";
           
                RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL, new { USER_STATUS = UserStaus.Invalid , SchoolNO = SchoolNO }).AsQueryable();
            switch (model.OrdercColumn)
            {
                case "CASH_ALL":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_ALL);
                    break;

                case "SUMCASH_AVAILABLE":
                    RankCash = RankCash.OrderByDescending(a => a.SUMCASH_AVAILABLE);
                    break;
            }
            return RankCash;

        }

        public ActionResult ExportExcel2(AWA003QueryViewModel model)
        {
            if (model == null) model = new AWA003QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            IQueryable<HRMT01QTY> RankCash;
            RankCash = QeuryData(model);

            var DataList = RankCash.ToList();

            List<AWA003QUERYExcelExport> AWA003QUERYExcelExport = new List<AWA003QUERYExcelExport>();

            foreach (var RankCashItem in DataList)
            {
                AWA003QUERYExcelExport AWA003QUERYExcelExportlist = new AWA003QUERYExcelExport(); ;
                AWA003QUERYExcelExportlist.CLASS_NO = RankCashItem.CLASS_NO;
                AWA003QUERYExcelExportlist.SEAT_NO = RankCashItem.SEAT_NO;
                AWA003QUERYExcelExportlist.USER_NO = RankCashItem.USER_NO;
                AWA003QUERYExcelExportlist.NAME = RankCashItem.NAME;
                AWA003QUERYExcelExportlist.CASH_ALL = RankCashItem.CASH_ALL;
                AWA003QUERYExcelExportlist.CASH_AVAILABLE = RankCashItem.CASH_AVAILABLE;
                AWA003QUERYExcelExportlist.BOOK_QTY = RankCashItem.BOOK_QTY;
                AWA003QUERYExcelExportlist.CASH_DEPOSIT = RankCashItem.CASH_DEPOSIT;
                AWA003QUERYExcelExportlist.SUMCASH_AVAILABLE = RankCashItem.SUMCASH_AVAILABLE;
                AWA003QUERYExcelExportlist.ATD01_QTY = RankCashItem.ATD01_QTY;
                AWA003QUERYExcelExportlist.ADDT21_QTY = RankCashItem.ADDT21_QTY;
                AWA003QUERYExcelExportlist.RUN_TOTAL_KM = RankCashItem.RUN_TOTAL_KM;
                AWA003QUERYExcelExport.Add(AWA003QUERYExcelExportlist);
            }

            DataTable DataTableExcel = AWA003QUERYExcelExport?.AsDataTable();
            DataTableExcel.Columns.Add("RowNum", typeof(int));
            int i = 0;
            foreach (DataRow row in DataTableExcel.Rows)
            { }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/AWA003QUERY2.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "備分酷幣點數", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\備分酷幣點數" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "備分酷幣點數.xlsx");//輸出檔案給Client端
        }

        public ActionResult ExportExcel(AWA003QueryViewModel model)
        {
            if (model == null) model = new AWA003QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            IQueryable<HRMT01QTY> RankCash;
            RankCash = QeuryData(model);

            var DataList = RankCash.ToList();

            List<AWA003QUERYExcelExport> AWA003QUERYExcelExport = new List<AWA003QUERYExcelExport>();

            foreach (var RankCashItem in DataList)
            {
                AWA003QUERYExcelExport AWA003QUERYExcelExportlist = new AWA003QUERYExcelExport(); ;
                AWA003QUERYExcelExportlist.CLASS_NO = RankCashItem.CLASS_NO;
                AWA003QUERYExcelExportlist.SEAT_NO = RankCashItem.SEAT_NO;
                AWA003QUERYExcelExportlist.USER_NO = RankCashItem.USER_NO;
                AWA003QUERYExcelExportlist.NAME = RankCashItem.NAME;
                AWA003QUERYExcelExportlist.CASH_ALL = RankCashItem.CASH_ALL;
                AWA003QUERYExcelExportlist.CASH_AVAILABLE = RankCashItem.CASH_AVAILABLE;

                AWA003QUERYExcelExportlist.CASH_DEPOSIT = RankCashItem.CASH_DEPOSIT;
                AWA003QUERYExcelExportlist.SUMCASH_AVAILABLE = RankCashItem.SUMCASH_AVAILABLE;
                AWA003QUERYExcelExport.Add(AWA003QUERYExcelExportlist);
            }

            DataTable DataTableExcel = AWA003QUERYExcelExport?.AsDataTable();
            DataTableExcel.Columns.Add("RowNum", typeof(int));
            int i = 0;
            foreach (DataRow row in DataTableExcel.Rows)
            { }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/AWA003QUERY1.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "酷幣總數排行榜", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\酷幣總數排行榜" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "酷幣總數排行榜.xlsx");//輸出檔案給Client端
        }

        public ActionResult TeacherExportExcel(AWA003QueryViewModel model)
        {
            if (model == null) model = new AWA003QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            IQueryable<HRMT01QTY> RankCash;
            RankCash = QeuryTeacherData(model);

            var DataList = RankCash.ToList();

            List<AWA003QUERYExcelExport> AWA003QUERYExcelExport = new List<AWA003QUERYExcelExport>();

            foreach (var RankCashItem in DataList)
            {
                AWA003QUERYExcelExport AWA003QUERYExcelExportlist = new AWA003QUERYExcelExport(); ;
                AWA003QUERYExcelExportlist.CLASS_NO = RankCashItem.CLASS_NO;
                AWA003QUERYExcelExportlist.SHORT_NAME = RankCashItem.SHORT_NAME;
                AWA003QUERYExcelExportlist.SEAT_NO = RankCashItem.SEAT_NO;
                AWA003QUERYExcelExportlist.USER_NO = RankCashItem.USER_NO;
                AWA003QUERYExcelExportlist.NAME = RankCashItem.NAME;
                AWA003QUERYExcelExportlist.CASH_ALL = RankCashItem.CASH_ALL;
                AWA003QUERYExcelExportlist.CASH_AVAILABLE = RankCashItem.CASH_AVAILABLE;
                AWA003QUERYExcelExportlist.CASH_DEPOSIT = RankCashItem.CASH_DEPOSIT;
                AWA003QUERYExcelExportlist.SUMCASH_AVAILABLE = RankCashItem.SUMCASH_AVAILABLE;
                AWA003QUERYExcelExport.Add(AWA003QUERYExcelExportlist);
            }

            DataTable DataTableExcel = AWA003QUERYExcelExport?.AsDataTable();
            DataTableExcel.Columns.Add("RowNum", typeof(int));
            int i = 0;
            foreach (DataRow row in DataTableExcel.Rows)
            { row["RowNum"] = ++i; }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/AWA003TeacherQUERY.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "教師酷幣點數排行榜", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\教師酷幣點數排行榜" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "教師酷幣點數排行榜.xlsx");//輸出檔案給Client端
        }

        public ActionResult QueryTeacher(AWA003QueryViewModel model)
        {
            if (model == null) model = new AWA003QueryViewModel();

            int PageSize = 20;

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = string.Empty;

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            else
            {
                SchoolNO = "ALL";
            }

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(SchoolNO, null);

            IQueryable<HRMT01QTY> RankCash;
            RankCash = QeuryTeacherData(model);
            if (model.IsPrint)
            {
                PageSize = int.MaxValue;
            }

            model.VAWA003List = RankCash.OrderByDescending(a => a.CASH_ALL).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            if (model.IsPrint)
            {
                if (model.VAWA003List.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    model.VAWA003List = RankCash.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            return View(model);
        }

        public IQueryable<HRMT01QTY> QeuryTeacherData(AWA003QueryViewModel model)
        {
            if (model == null) model = new AWA003QueryViewModel();

            int PageSize = 20;

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = string.Empty;

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            else
            {
                SchoolNO = "ALL";
            }

            ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(SchoolNO, null);

            IQueryable<HRMT01QTY> RankCash;

            if (model.WhereIsMonthTop)
            {
                string sSQL = @"SELECT  SHORT_NAME=b1.SHORT_NAME,
                               SCHOOL_NO=hh1.SCHOOL_NO,
                               SYEAR = hh1.SYEAR,
                               CLASS_NO = hh1.CLASS_NO,
                               NAME = hh1.SNAME,
                               USER_NO = hh1.USER_NO,
                               SEAT_NO=hh1.SEAT_NO,
                               QTY = w1.CASH_ALL,
                               CASH_ALL = sum(WLog.CASH_IN),
                               CASH_AVAILABLE = w1.CASH_AVAILABLE
                                FROM AWAT08 w1 (nolock)
                                join BDMT01 b1  (nolock) on w1.SCHOOL_NO = b1.SCHOOL_NO
                                join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS
                                join AWAT08_LOG WLog  (nolock) on w1.SCHOOL_NO = WLog.SCHOOL_NO and w1.USER_NO = WLog.USER_NO
                                where   CONVERT(nvarchar(6),WLog.LOG_TIME,112) = CONVERT(nvarchar(6),GETDATE(),112)
                                group by b1.SHORT_NAME,hh1.SCHOOL_NO,hh1.CLASS_NO,hh1.SYEAR,hh1.SNAME,hh1.USER_NO,hh1.SEAT_NO,w1.CASH_ALL,w1.CASH_AVAILABLE
                                HAVING sum(WLog.CASH_IN)>0 ";
                RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL, new { USER_STATUS = UserStaus.Invalid }).AsQueryable();
            }
            else
            {
                RankCash = from w8 in db.AWAT08
                           join b1 in db.BDMT01 on w8.SCHOOL_NO equals b1.SCHOOL_NO
                           join h1 in db.HRMT01
                           on new { w8.SCHOOL_NO, w8.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                         into h1Table
                           from hh1 in h1Table.DefaultIfEmpty()
                           where (hh1.USER_TYPE == UserType.Teacher|| hh1.USER_TYPE == UserType.Admin) && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
                           select new HRMT01QTY
                           {
                               SHORT_NAME = b1.SHORT_NAME,
                               SCHOOL_NO = hh1.SCHOOL_NO,
                               SYEAR = hh1.SYEAR,
                               CLASS_NO = hh1.CLASS_NO,
                               NAME = hh1.SNAME,
                               USER_NO = hh1.USER_NO,
                               SEAT_NO = hh1.SEAT_NO,
                               QTY = w8.CASH_ALL,
                               CASH_ALL = w8.CASH_ALL,
                               CASH_AVAILABLE = w8.CASH_AVAILABLE,
                           };
            }

            if (SchoolNO != "ALL")
            {
                RankCash = RankCash.Where(a => a.SCHOOL_NO == SchoolNO);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                RankCash = RankCash.Where(a => a.USER_NO.Contains(model.whereKeyword) || a.NAME.Contains(model.whereKeyword));
            }

            switch (model.OrdercColumn)
            {
                case "CASH_ALL":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_ALL);
                    break;

                case "CASH_AVAILABLE":
                    RankCash = RankCash.OrderByDescending(a => a.CASH_AVAILABLE);
                    break;

                case "NAME":
                    RankCash = RankCash.OrderBy(a => a.NAME).ThenByDescending(a => a.CASH_ALL);
                    break;

                default:
                    RankCash = RankCash.OrderByDescending(a => a.CASH_ALL);
                    break;
            }
            return RankCash;
        }
    }
}