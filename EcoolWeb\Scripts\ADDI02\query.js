// JavaScript for ADDI02 QUERY page
// 保持原有的JavaScript邏輯不變

function onBtnLink(APPLY_NO_Val, APPLY_STATUS_Val) {
    var USER_TYPE = $('#USER_TYPE').val();

    // 根據用戶類型和申請狀態決定導航目標
    if (USER_TYPE == window.ADDI02_QUERY_CONFIG.userTypeStudent || window.ADDI02_QUERY_CONFIG.addtListApplyYN == 'Y') {
        if (APPLY_STATUS_Val == window.ADDI02_QUERY_CONFIG.applyTypeWithVerify ||
            APPLY_STATUS_Val == window.ADDI02_QUERY_CONFIG.applyTypeBack ||
            APPLY_STATUS_Val == window.ADDI02_QUERY_CONFIG.applyTypeTempSave) {
            // 修改的項目導向 ADDTList_CheckPendingDetail2
            var url = window.ADDI02_QUERY_URLS.checkPendingDetail2 + '?APPLY_NO=' + APPLY_NO_Val + '&BackAction=QUERY';
            window.location.href = url;
        } else {
            // 其他狀態導向 ADDTALLListDetails
            var url = window.ADDI02_QUERY_URLS.addtAllListDetails + '?APPLY_NO=' + APPLY_NO_Val + '&BackAction=QUERY';
            window.location.href = url;
        }
    } else {
        // 非學生且無申請權限，導向 ADDTALLListDetails
        var url = window.ADDI02_QUERY_URLS.addtAllListDetails + '?APPLY_NO=' + APPLY_NO_Val + '&BackAction=QUERY';
        window.location.href = url;
    }
}

//分頁
function FunPageProc(pageno) {
    $('#Page').val(pageno);
    funAjax();
}

function DoWRITING_STATUS(Value) {
    $('#whereAPPLY_STATUS').val(Value);
    FunPageProc(1);
}

//排序
function FunSort(SortName) {
    OrderByName = $('#OrdercColumn').val();
    SyntaxName = $('#SyntaxName').val();

    if (OrderByName == SortName) {
        if (SyntaxName == "Desc") {
            $('#SyntaxName').val("ASC");
        }
        else {
            $('#SyntaxName').val("Desc");
        }
    }
    else {
        $('#OrdercColumn').val(SortName);
        $('#SyntaxName').val("Desc");
    }
    funAjax();
}

//查詢
function funAjax() {
    var data = {
        "OrdercColumn": $('#OrdercColumn').val(),
        "SyntaxName": $('#SyntaxName').val(),
        "Page": $('#Page').val(),
        "whereKeyword": $('#whereKeyword').val(),
        "whereAPPLY_STATUS": $('#whereAPPLY_STATUS').val()
    };

    $.ajax({
        url: window.ADDI02_QUERY_URLS.pageContent,
        data: data,
        type: 'post',
        async: false,
        cache: false,
        dataType: 'html',
        success: function (data) {
            $('#PageContent').html(data);
        }
    });
}
