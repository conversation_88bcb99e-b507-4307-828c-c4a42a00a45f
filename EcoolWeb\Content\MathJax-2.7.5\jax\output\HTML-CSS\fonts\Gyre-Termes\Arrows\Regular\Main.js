/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Arrows/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Arrows={directory:"Arrows/Regular",family:"GyreTermesMathJax_Arrows",testString:"\u00A0\u219F\u21A1\u21A4\u21A5\u21A7\u21B2\u21B3\u21B4\u21B5\u21C5\u21D6\u21D7\u21D8\u21D9",32:[0,0,250,0,0],160:[0,0,250,0,0],8607:[690,180,520,80,440],8609:[680,190,520,80,440],8612:[430,-70,850,80,770],8613:[600,90,520,80,440],8615:[590,100,520,80,440],8626:[598,98,540,80,460],8627:[598,98,540,80,460],8628:[478,-12,770,80,690],8629:[555,55,626,80,546],8645:[600,100,920,80,840],8662:[497,63,720,80,640],8663:[497,63,720,80,640],8664:[563,-3,720,80,640],8665:[563,-3,720,80,640],8668:[430,-70,850,80,770],8678:[470,-30,1073,80,993],8679:[715,198,600,80,520],8680:[470,-30,1073,80,993],8681:[698,215,600,80,520],8691:[715,215,600,80,520],8693:[600,100,920,80,840],8694:[830,330,850,80,770],10228:[568,68,1132,80,1052],10235:[430,-70,1170,80,1090],10237:[470,-30,1350,80,1270],10238:[470,-30,1350,80,1270],10239:[430,-70,1170,80,1090],10502:[470,-30,1030,80,950],10503:[470,-30,1030,80,950]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Arrows"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Arrows/Regular/Main.js"]);
