@model ADDI05AnswerListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    List<SelectListItem> CountNumItem = new List<SelectListItem>();
    CountNumItem.Add(new SelectListItem() { Text = "100筆", Value = "100", Selected = Model.PageSize == 100 });
    CountNumItem.Add(new SelectListItem() { Text = "300筆", Value = "300", Selected = Model.PageSize == 300 });
    CountNumItem.Add(new SelectListItem() { Text = "500筆", Value = "500", Selected = Model.PageSize == 500 });
    ViewBag.CountNumItem = CountNumItem;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("AnswerList", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(m => m.OrderByName)
    @Html.HiddenFor(m => m.DIALOG_ID)
    @Html.HiddenFor(m => m.Page)

    @Html.Partial("_ADDI05Menu", 0)


    <div class="form-inline" role="form" id="DivSearch">
        <div class="form-group">
            <label class="control-label">
                @Html.DisplayNameFor(model => model.uADDT13.First().NAME)
                /@Html.DisplayNameFor(model => model.uADDT13.First().CLASS_NO)
                /@Html.DisplayNameFor(model => model.uADDT13.First().SEAT_NO)
            </label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.SearchContents, new { htmlAttributes = new { @class = "form-control", @placeholder = "搜尋欲瀏覽之相關字串" } })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        <div class="col-md-3 col-xs-4">
            <div class="input-group input-group-sm">
                <span class="input-group-addon">顯示</span>
                @Html.DropDownList("PageSize", (IEnumerable<SelectListItem>)ViewBag.CountNumItem, new { @class = "form-control", @onchange = "FunPageProc(1)" })
            </div>
        </div>
    </div>
    <div style="height:25px">

    </div>

    <div class="row">
        <div class="col-md-8 col-xs-7">
        </div>

        <div class="col-xs-2">
        </div>
    </div>
    <img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-ADDI05">
        <div class="Details">
            <div class="table-responsive">
                <div class="text-center">
                    <table class="table-ecool table-92Per table-hover">
                        <caption class="Caption_Div_Left">
                            活動名稱：@Model.DIALOG_NAME
                        </caption>
                        <thead>
                            <tr>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SCHOOL_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SHORT_NAME)
                                    <img id="SCHOOL_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SYEAR');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SYEAR)
                                    <img id="SYEAR" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SEMESTER');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SEMESTER)
                                    <img id="SEMESTER" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().CLASS_NO)
                                    <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13.First().SEAT_NO)
                                    <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                                    姓名
                                    <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                                    最後答題日期
                                    <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('Count');">
                                    答題次數
                                    <img id="Count" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.uADDT13)
                            {

                                <tr>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SYEAR)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SEMESTER)
                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                        }

                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                                        }
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.Count)
                                    </td>
                                    <td align="center">
                                        <a href="@Url.Action("EachAnswerList", "ADDI05",new { Where_USERNO = item.USER_NO, Where_SCHOOLNO = item.SCHOOL_NO, DIALOG_ID = item.DIALOG_ID })"
                                           class="btn btn-xs btn-default">檢視</a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>

                    <div style="height:15px"></div>
                </div>
            </div>
        </div>
    </div>
    <div>
        @Html.Pager(Model.uADDT13.PageSize, Model.uADDT13.PageNumber, Model.uADDT13.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
        .MaxNrOfPages(5)
        .SetPreviousPageText("上頁")
        .SetNextPageText("下頁")
        .AlwaysAddFirstPageNumber()
        )
    </div>

}





@section Scripts {
    <script language="JavaScript">
         var targetFormID = "#form1";

        function onGo(ActionVal) {
            $("#OrderByName").val('');
            if (ActionVal == "Index") {
                form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "detail") {
                form1.action = '@Url.Action("detail", (string)ViewBag.BRE_NO)';
            }
            form1.submit();
        }


        function todoClear() {
            $(targetFormID).find("#DivSearch :input,:selected").each(function () {
                var type = $(this).attr('type');
                var InPreadonly = $(this).attr('readonly');
                var tag = this.tagName.toLowerCase();

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        $(this).prop('checked', $(this).attr("title") == 'Default');
                    }
                    else if (tag == 'select') {
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1)
        }

        function FunPageProc(pageno) {
            form1.Page.value = pageno
            form1.submit();
        }

        function doSort(SortCol) {
            $("#OrderByName").val(SortCol);
            FunPageProc(1);
        }
    </script>
}

