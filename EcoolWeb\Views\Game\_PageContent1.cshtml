﻿@model GameIndexViewModel
@using ECOOL_APP;
@{
    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;

    byte? GAME_TYPE = ViewBag.GAME_TYPE ?? (byte)ADDT26.GameType.贈品抽獎;

}
@Html.HiddenFor(m => m.GAME_NO)

@Html.HiddenFor(m => m.Search.WhereGAME_NAME)
@Html.HiddenFor(m => m.Search.WhereGAME_NO)
@Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.LEVEL_NO)
@Html.HiddenFor(m => m.Coupons_ITem)
<br />
<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">搜尋欲瀏覽之相關字串</label>
    </div>

    <div class="form-group">
        @Html.EditorFor(model => model.Search.WhereGAME_NAME, new { htmlAttributes = new { @class = "form-control" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" /><br />
    <br />
   
    <font style="font-size:19px">

        說明：
        <br />
        1.本功能可以用來抽獎，可以「抽獎品」或「酷幣點數」；如果是酷幣點數，會直接(自動)匯入學生酷幣帳戶。
        <br />
        2. 下面「開始抽獎」的按鈕，只限數位學生證感應抽獎。
        <br />
        3. 下面「用學號也可以抽獎」，除用學生證感應抽獎外，也可以輸入學號抽獎。
    </font><br />
    

</div>
<br />


@if (Permission.Where(a => a.ActionName == "Edit1").Any())
{
    <div class="text-right">
        <a role="button" href='@Url.Action("Edit1",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys"><i class="fa fa-plus-circle"></i>  新增</a>
        <br />
        <br />
    </div>
}

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th style="text-align:left;padding-left:240px">
                       項目
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_NAME')">
                            @Html.DisplayNameFor(model => model.ListData.First().GAME_NAME)
                        </samp>
                    </th>

                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_DATES')">
                            @Html.DisplayNameFor(model => model.ListData.First().GAME_DATES)
                        </samp>
                    </th>
                    <th>

                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_DATEE')">
                            @Html.DisplayNameFor(model => model.ListData.First().GAME_DATEE)
                        </samp>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {
                        <tr>
                            <td>

                                <button type="button" class="btn btn-xs btn-Basic" onclick="OnEdit('@item.GAME_NO','@item.GAME_TYPE')"> <i class="fa fa-edit"></i> 獎品抽獎管理</button>






                                @if (item?.GAME_DATES != null && item?.GAME_DATEE != null)
                                {
                                    if (DateTime.Now >= item?.GAME_DATES && DateTime.Now < item?.GAME_DATEE.Value.AddDays(1))
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnPassMode('@item.GAME_NO','@item.Coupons_ITem','@item.LEVEL_NO')"> <i class="fa fa-truck"></i> 開始抽獎</button>

                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnPassMode2('@item.GAME_NO','@item.Coupons_ITem','@item.LEVEL_NO')"> <i class="fa fa-truck"></i> 用學號，班級＋座號也可以抽獎</button>
                                    }
                                }

                                <button type="button" class="btn btn-xs btn-Basic" onclick="GetDetail('@item.GAME_NO','@item.GAME_TYPE')"> <i class="fa fa-edit"></i> 中獎名單</button>


                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.GAME_NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.GAME_DATES, "ShortDateTime")
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.GAME_DATEE, "ShortDateTime")
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>
<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                              .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                              .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                              .SetNextPageText(PageGlobal.DfSetNextPageText)
                                             )
   
</div>
<script>
    $(document).ready(function () {
        $(".paginationW").append("<li> <input type=\"button\" class=\"btn - yellow btn btn - sm\" value=\"回上一頁\" onclick=\"back()\" /></li>")
    });
</script>