﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public class ApiBaseModel
    {
        /// <summary>
        /// CheckId 加密後的BASE64編碼的字符串
        /// </summary>
        public string CheckId { get; set; }

        /// <summary>
        /// CheckId 解密後
        /// </summary>
        public string RealCheckId { get; set; }

        /// <summary>
        /// UUID
        /// </summary>
        public string UUID { get; set; }

        public byte? OS_TYPE { get; set; }
    }
}