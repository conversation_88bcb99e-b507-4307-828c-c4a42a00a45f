﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDI12EditPeopleViewModel
    {

        /// <summary>
        ///表演人員隊伍id
        /// </summary>
        [DisplayName("表演人員隊伍id")]
        public string STAGE_PERSON_ID { get; set; }

        /// <summary>
        ///表演人員隊伍項次
        /// </summary>
        [DisplayName("表演人員隊伍項次")]
        public string STAGE_PERSON_ITEM { get; set; }

        /// <summary>
        ///表演 id
        /// </summary>
        [DisplayName("小小舞臺ID")]
        public string STAGE_ID { get; set; }


        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }


        /// <summary>
        ///學號
        /// </summary>
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///全名
        /// </summary>
        [DisplayName("全名")]
        public string NAME { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }


    }
}
