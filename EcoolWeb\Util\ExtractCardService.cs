﻿using ECOOL_APP;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.EzCodeStudMap;
using EncryptDecipher;
using EntityFramework.Extensions;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Web;

namespace EcoolWeb.Util
{
    public class ExtractCardService
    {
        public static readonly string BASE_DIRECTORY = AppDomain.CurrentDomain.BaseDirectory;

        public static readonly string LOG_PATH = BASE_DIRECTORY + @"JobLogs\ExtractCardLog.txt";

        public static readonly string USER_CODE = "ecc.hhups.tp.edu.tw";
       // public static readonly string USER_CODE = "ecc.tp.edu.tw";
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public ExtractCardService()
        {
            if (!Directory.Exists(BASE_DIRECTORY + "JobLogs"))
            {
                Directory.CreateDirectory(BASE_DIRECTORY + "JobLogs");
            }
        }
        private static bool RemoteCertificateValidate(object sender, X509Certificate cert, X509Chain chain, SslPolicyErrors error)
        {
            // trust any certificate!!!
            System.Console.WriteLine("Warning, trust any certificate");
            return true;
        }
        /// <summary>
        /// 排程定期抓取學生內外卡號資料
        /// </summary>
        public void GetCardNumberSchedual()
        {
            string isInit = "IsCardNumberSchedual_Init";

            string lastUpdateDate = DateTime.UtcNow.AddHours(8).AddDays(-1).ToString("yyyy/MM/dd HH:mm:ss");

            if (ConfigHelpercs.GetappSettings(isInit) == "true")
            {
                // 1年前
                lastUpdateDate = DateTime.UtcNow.AddHours(8).AddYears(-2).ToString("yyyy/MM/dd HH:mm:ss");
            }




            //        ServicePointManager.ServerCertificateValidationCallback = (
            //  object s,
            //  System.Security.Cryptography.X509Certificates.X509Certificate certificate,
            //  System.Security.Cryptography.X509Certificates.X509Chain chain,
            //  System.Net.Security.SslPolicyErrors sslPolicyErrors) >=
            //{
            //            if (certificate.Subject.Contains("localhost"))
            //            {
            //                return true;
            //            }
            //            return false;
            //        };


            // 國小
            //try
            //{

            ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                logger.Info("request 5353");
                var client = new EzCodeStudMapClient();
                EzCodeStudData[] elemStuData = client.GetData(null, "1", lastUpdateDate, USER_CODE)
                     .Where(r => !string.IsNullOrEmpty(r.cardInternalNo))
                     .OrderBy(r => r.studIdNumber)
                     .ToArray();
                //object s= client.GetData(null, "1", lastUpdateDate, USER_CODE)
                //     .Where(r => !string.IsNullOrEmpty(r.cardInternalNo))
                //     .OrderBy(r => r.studIdNumber)
                //     .ToArray();
                client.Close();
                logger.Info("request 1234");
                // RespDataProcess(elemStuData, "國小");
            //}
            //catch (Exception e) {
            //    logger.Info("request"+e.Message);
            //}

            

            //// 國中
            //client = new EzCodeStudMapClient();
            //elemStuData = client.GetData(null, "2", lastUpdateDate, USER_CODE)
            //    .Where(r => !string.IsNullOrEmpty(r.cardInternalNo))
            //    .OrderBy(r => r.studIdNumber)
            //    .ToArray();
            //client.Close();

            //RespDataProcess(elemStuData, "國中");

            //// 高中
            //client = new EzCodeStudMapClient();
            //elemStuData = client.GetData(null, "3", lastUpdateDate, USER_CODE)
            //    .Where(r => !string.IsNullOrEmpty(r.cardInternalNo))
            //    .OrderBy(r => r.studIdNumber).ToArray();
            //client.Close();

            //RespDataProcess(elemStuData, "高中");

#if !DEBUG
            UpdateAppSettings(isInit, "false");
#endif
        }

        private void RespDataProcess(EzCodeStudData[] response, string level)
        {
            int row = 0;
            EzCodeStudData catchData = new EzCodeStudData();
            try
            {
                ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();
                using (var db = new ECOOL_DEVEntities())
                using (var bulkRepo = new EFBatchRepository())
                {
                    db.BULKCARD_TEMP.Delete();
                    db.SaveChanges();

                    List<BULKCARD_TEMP> tempB = new List<BULKCARD_TEMP>();
                    List<HRMT01> tempsH = new List<HRMT01>();

                    List<string> idnos = new List<string>(); // 檢查不要重複身分證

                    foreach (var r in response)
                    {
                        ++row;
                        catchData = r;

                        string idno = Decipher.aseDecrypt(r.studIdNumber);

                        if (string.IsNullOrEmpty(idno) || string.IsNullOrEmpty(r.cardInternalNo) || string.IsNullOrEmpty(r.eduNumber))
                        {
                            continue;
                        }

                        bool find = db.HRMT01.Any(h => h.SCHOOL_NO == r.eduNumber && h.IDNO == idno);
                        if (find)
                        {
                            if (!string.IsNullOrEmpty(idno) && !string.IsNullOrEmpty(r.cardInternalNo))
                            {
                                if (!idnos.Contains(idno))
                                {
                                    tempB.Add(new BULKCARD_TEMP()
                                    {
                                        studIdNumber = idno,
                                        studName = r.studName,
                                        eduNumber = r.eduNumber,
                                        cardInternalNo = r.cardInternalNo,
                                        cardExternalNo = r.cardExternalNo,
                                        tempUserNo = ""
                                    });

                                    idnos.Add(idno);
                                }
                            }
                        }
                    }
                    WriteLog("確認是否正確A");
                    if (tempB!=null && tempB.Count() > 0) {

                        EntitiesDb.BULKCARD_TEMP.AddRange(tempB);

                    }
                    EntitiesDb.SaveChanges();
                    WriteLog("確認是否正確B");

                    if (tempB.Count > 0)
                    {
                        IDbConnection cnn = db.Database.Connection;
                        string insertQuery = @"update HRMT01 set CARD_NO = b.cardInternalNo
                                               from HRMT01 a
                                               inner join BULKCARD_TEMP b on a.IDNO=b.studIdNumber and a.SCHOOL_NO = b.eduNumber";
                        var result = db.Database.ExecuteSqlCommand(insertQuery);
                    }

                    string success = $"\r\n{DateTime.Now.ToString()} ========={level}完成{row}筆=========\r\n\r\n";
                    WriteLog(success);
                }
            }
            catch (Exception ex)
            {
                string err = $"\r\n{DateTime.Now.ToString()} ========={level}第{row}筆=========\r\n{JsonConvert.SerializeObject(catchData)}\r\n{ex.InnerException.ToString()}\r\n\r\n";
                WriteLog(err);

                throw;
            }
        }

        private void WriteLog(string log)
        {
            File.WriteAllText(LOG_PATH, log);
        }

        private void UpdateAppSettings(string key, string value)
        {
            try
            {
                System.Configuration.Configuration configFile = null;
                if (System.Web.HttpContext.Current != null)
                {
                    configFile =
                        System.Web.Configuration.WebConfigurationManager.OpenWebConfiguration("~");
                }
                else
                {
                    configFile =
                        ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                }
                var settings = configFile.AppSettings.Settings;
                if (settings[key] == null)
                {
                    settings.Add(key, value);
                }
                else
                {
                    settings[key].Value = value;
                }
                configFile.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection(configFile.AppSettings.SectionInformation.Name);
            }
            catch (Exception)
            {
            }
        }
    }
}