/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/Arrows.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_AMS,{8592:[437,-64,500,64,422],8594:[437,-64,500,58,417],8602:[437,-60,1000,56,942],8603:[437,-60,1000,54,942],8606:[417,-83,1000,56,944],8608:[417,-83,1000,55,943],8610:[417,-83,1111,56,1031],8611:[417,-83,1111,79,1054],8619:[575,41,1000,56,964],8620:[575,41,1000,35,943],8621:[417,-83,1389,57,1331],8622:[437,-60,1000,56,942],8624:[722,0,500,56,444],8625:[722,0,500,55,443],8630:[461,1,1000,17,950],8631:[460,1,1000,46,982],8634:[650,83,778,56,722],8635:[650,83,778,56,721],8638:[694,194,417,188,375],8639:[694,194,417,41,228],8642:[694,194,417,188,375],8643:[694,194,417,41,228],8644:[667,0,1000,55,944],8646:[667,0,1000,55,944],8647:[583,83,1000,55,944],8648:[694,193,833,83,749],8649:[583,83,1000,55,944],8650:[694,194,833,83,749],8651:[514,14,1000,55,944],8652:[514,14,1000,55,944],8653:[534,35,1000,54,942],8654:[534,37,1000,32,965],8655:[534,35,1000,55,943],8666:[611,111,1000,76,944],8667:[611,111,1000,55,923],8669:[417,-83,1000,56,943],8672:[437,-64,1334,64,1251],8674:[437,-64,1334,84,1251]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/AMS/Regular/Arrows.js");
