﻿@model EcoolWeb.ViewModels.AWA004QueryViewModel

@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    @Html.HiddenFor(m => m.whereSchoolNo)

    ViewBag.Title = "學生兌獎情形";


    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();



    int i = 0;
    string iUser_no = (user != null) ? user.USER_NO : "";
    bool BoolbtnModifyQuery = System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck("ModifyQuery", "ModifyQuery", null);

}
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>


<style>
    .orderColumn {
        color: #6badcf
    }
</style>

@section scripts{
    <script>
        function btnTRANS_NO_onclick(TRANS_NO) {
            $("#hidSelectTRANS_NO").val(TRANS_NO);
            $('#whereSchoolNo').val()
            document.AWA004.enctype = "multipart/form-data";
            document.AWA004.action = "CancelTrans";
            document.AWA004.submit();
        }

        var targetFormID = '#AWA004';



        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function todoClear() {
            ////重設

            $('#Q-Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }

        function exportExcel() {
            var StrHTML = $("#OutList")[0].outerHTML
            $("#Ex").append(StrHTML);
            $("#Ex :input").remove()
            $("#Ex > table").removeClass();
            $("#Ex table").attr("border", "1")
            var sHtml = htmlEncode($("#Ex")[0].outerHTML)
            $("#hHtml").val(sHtml);
            $("#Ex").empty();
  
            var whereSchoolNo = $("select[name='whereSchoolNo']").val();
            console.log(whereSchoolNo);
            $("#whereAWARD_SCHOOL_NO").val(whereSchoolNo);
            //表單提交
            var newform = $('<form action="@(Url.Action("ExportExcel"))" method="post" enctype="multipart/form-data"></form>');
            newform.append($("#hHtml"));
            newform.appendTo('body').submit();
        }
        function htmlEncode(value) {
            return $('<div/>').text(value).html();
        }
    </script>
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@helper  buttonFun(string SchoolNO, ECOOL_APP.UserProfile user)
{
    string AWARD_SCHOOL_NO_EMPTY = (string.IsNullOrWhiteSpace(Model.whereAWARD_SCHOOL_NO)) ? "active" : "";
    string AWARD_SCHOOL_NO_ALL = (Model.whereAWARD_SCHOOL_NO == "ALL") ? "active" : "";
    string AWARD_SCHOOL_NO_Me = (Model.whereAWARD_SCHOOL_NO == SchoolNO && string.IsNullOrWhiteSpace(Model.whereAWARD_SCHOOL_NO) == false) ? "active" : "";

    string STATUS_EMPIT = (string.IsNullOrWhiteSpace(Model.whereSTATUS)) ? "active" : "";
    string STATUS_Receive = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive.ToString()) ? "active" : "";
    string STATUS_UN = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN.ToString()) ? "active" : "";
    string STATUS_Del = (Model.whereSTATUS == VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del.ToString()) ? "active" : "";

    string MyList = (string.IsNullOrWhiteSpace(Model.whereUserNo) == false) ? "active" : "";


    <div class="row">
        <div class="col-md-6 col-xs-12 text-left">
            <samp>獎品類別：</samp>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_EMPTY" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', '');">全部</button>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_ALL" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', 'ALL');">總召學校獎品</button>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_Me" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', '@SchoolNO');">本校獎品</button>
        </div>

        <div class="col-md-6 col-xs-12 text-right">
            <samp>狀態：</samp>
            <button class="btn btn-xs btn-pink  @STATUS_EMPIT" type="button" onclick="doSearch('whereSTATUS', '');">全部</button>
            <button class="btn btn-xs btn-pink  @STATUS_Receive" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive');">已領取</button>
            <button class="btn btn-xs btn-pink  @STATUS_UN" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN');">未領取</button>
            <button class="btn btn-xs btn-pink  @STATUS_Del" type="button" onclick="doSearch('whereSTATUS', '@VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Del');">已取消</button>

            @if (string.IsNullOrWhiteSpace(ViewBag.MyUSER_NO) == false)
            {
                string MyBtnName = string.Empty;

                if (user.USER_TYPE == UserType.Student)
                {
                    MyBtnName = "我的兌獎情形";
                }
                else
                {
                    MyBtnName = "寶貝兌獎情形";
                }

                <button class="btn btn-xs btn-pink @MyList" type="button" onclick="doSearch('whereSTATUS', '');doSearch('whereUserNo', '@ViewBag.MyUSER_NO');">@MyBtnName</button>
            }
        </div>
    </div>


}



@using (Html.BeginForm("Query", "AWA004", FormMethod.Post, new { name = "AWA004", id = "AWA004" }))
{
    @Html.Hidden("Awat", Request["Awat"])
    @Html.Hidden("hHtml")

    @Html.HiddenFor(m => m.OrderColumn)
    <input type="hidden" name="SortBy" value="@Model.SortBy" />
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.whereSNAME)
    @Html.HiddenFor(m => m.whereAWARD_NO)
    @Html.HiddenFor(m => m.whereAWARD_NAME)
    @Html.HiddenFor(m => m.whereCLASS_NO)
    @*@Html.HiddenFor(m => m.whereAWARD_SCHOOL_NO)*@
    @Html.HiddenFor(m => m.whereSTATUS)
    @Html.HiddenFor(m => m.whereUserNo)
    @Html.Hidden("RoleName", "Student")
    @Html.Hidden("ActionFrom", "Query")

    @Html.Hidden("hidSelectTRANS_NO")

    <br />
    <div id="Q-Div">
        @if (ViewBag.Show)
        {
            <div class="form-inline">
                <div class="form-group">
                    <label class="control-label">學校</label>
                </div>
                <div class="form-group">
                    @Html.DropDownList("whereSchoolNo", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "AWA004.submit();" })
                </div>
            </div>
            <br />
        }
        <div class="form-inline">
            <div class="form-inline" role="form">
                <div class="form-group">
                    <label class="control-label">搜尋</label>
                </div>
                <div class="form-group" title="獎品ID/學號/姓名/品名">
                    @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })


                </div>

                <input type="submit" class="btn-yellow btn btn-sm" value="搜尋" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
            </div>
        </div>
    </div>
    <br />

    <span>每頁筆數</span>
    @Html.DropDownListFor(m=>m.PageSize,
        new List<SelectListItem>() {
            new SelectListItem() { Text="20", Value="20" },
            new SelectListItem() { Text = "40", Value = "40"},
            new SelectListItem() { Text = "60", Value = "60"},
            new SelectListItem() { Text = "80", Value = "80"},
            new SelectListItem() { Text = "100", Value = "100"}
        }, new { onchange= "document.forms[0].submit()" }
        )
    <br /> <br />

}

@buttonFun(SchoolNO, user)

<img src="~/Content/img/web-bar2-revise-25.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center">
        <table class="table-ecool table-92Per table-hover table-ecool-ADDO05" id="OutList">
            @Html.HiddenFor(m => m.whereAWARD_SCHOOL_NO)
            <thead>
                <tr>
                    @if (ViewBag.Show)
                    {
                        <th>
                            <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SHORT_NAME';document.forms[0].submit()">
                                學校
                            </a>
                        </th>
                    }
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SYEAR';document.forms[0].submit()">
                            學年
                        </a>
                    </th>
                    <th>
                        學期
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='CLASS_NO';document.forms[0].submit()">
                            班級
                        </a>
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SEAT_NO';document.forms[0].submit()">
                            座號
                        </a>
                    </th>
                    <th>
                        姓名
                    </th>
                    <th>
                        獎品
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='COST_CASH';document.forms[0].submit()">
                            酷幣數
                        </a>
                    </th>
                    <th>
                        狀態
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='TRANS_DATE';document.forms[0].submit()">
                            日期
                        </a>
                    </th>
                    <th>

                    </th>

                </tr>
            </thead>
            <tbody>
                @if (Model.VAWA004List.Count() == 0 && Model.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                {
                    <tr>
                        <td style="white-space:nowrap;color:red" colspan="9"> 本競標還沒截止</td>
                    </tr>
                }
                else
                {
                    foreach (var item in Model.VAWA004List)
                    {
                        <tr>
                            @if (ViewBag.Show)
                            {
                                <td style="white-space:nowrap">
                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                </td>
                            }
                            <td>
                                @Html.DisplayFor(modelItem => item.SYEAR)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SEMESTER)
                            </td>
                            <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            @if (item.SNAME.Trim() == "")
                            {
                                <td>
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                            }
                            else
                            {
                                <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereSNAME','@item.SNAME');">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                            }

                            <td style="text-align: left;cursor:pointer;white-space:normal" onclick="doSearch('whereAWARD_NAME','@item.AWARD_NAME');">
                                @Html.DisplayFor(modelItem => item.AWARD_NAME)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.COST_CASH)
                            </td>
                            <td style="white-space:nowrap">
                                @Html.DisplayFor(modelItem => item.CTRANS_STATUS)
                               
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.TRANS_DATE, "ShortDateTime")
                            </td>
                            <td>
                                @*@Html.DisplayFor(modelItem => item.MEMO)*@
                                @if (item.AWARD_TYPE != Awa.AWARD_TYPE_Val.AWARD_TYPE_B && item.CTRANS_STATUS == "已訂未領" && item.USER_NO == iUser_no)
                                {
                                    <br />
                                    <input type="button" id='[@i].btnTRANS_NO' name='[@i].btnTRANS_NO' value="取消領取" onclick="btnTRANS_NO_onclick('@item.TRANS_NO');" class="btn btn-xs btn-Basic">
                                    <input type="hidden" id='[@i].TRANS_NO' name='[@i].TRANS_NO' value=@item.TRANS_NO />
                                }
                            </td>

                        </tr>
                        i++;
                    }
                }
            </tbody>
        </table>
    </div>
</div>

<div>
    @Html.Pager(Model.PageSize, Model.VAWA004List.PageNumber, Model.VAWA004List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>
@if (string.IsNullOrWhiteSpace(Model.whereAWARD_NO))
{
    <div class="col-sm-12">
        @if (Model.PieChart != null)
        {
            <div style="height:30px"></div>
            <div>
                @Model.PieChart
            </div>
        }
    </div>
}


<div style="height:15px"></div>
<div style="text-align:center;">
    @if (Request["Awat"] != null)
    {
        if (Model.VAWA004List.Count() > 0
            && BoolbtnModifyQuery)
        {
            <input type="button" id="btnExcel" value="匯出Excel" class="btn btn-default" onclick="exportExcel();" />
            <div id="Ex" style="display:none">
            </div>
        }
        <a href='@Url.Action("AwatQ02", "Awat")' role="button" class="btn btn-default">
            返回
        </a>

    }

</div>

