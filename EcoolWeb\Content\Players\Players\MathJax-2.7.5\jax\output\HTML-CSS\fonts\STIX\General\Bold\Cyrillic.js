/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Cyrillic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{1025:[916,0,667,16,641],1026:[676,97,856,40,809],1027:[963,0,632,20,600],1028:[691,19,685,37,638],1029:[692,19,556,35,513],1030:[676,0,389,20,370],1031:[916,0,389,20,370],1032:[676,96,500,3,478],1033:[676,18,1005,10,958],1034:[676,0,1054,21,1007],1035:[676,0,883,40,868],1036:[923,0,759,21,741],1038:[926,22,722,15,699],1039:[676,176,770,21,753],1040:[690,0,722,9,689],1041:[676,0,667,16,619],1042:[676,0,667,16,619],1043:[676,0,632,20,600],1044:[676,176,715,24,691],1045:[676,0,667,16,641],1046:[684,0,1130,32,1091],1047:[691,19,570,22,531],1048:[676,0,778,21,759],1049:[926,0,778,21,759],1050:[684,0,759,21,741],1051:[676,18,738,10,719],1052:[676,0,944,14,921],1053:[676,0,778,21,759],1054:[691,19,778,35,743],1055:[676,0,762,13,751],1056:[676,0,611,16,600],1057:[691,19,709,36,674],1058:[676,0,667,31,636],1059:[676,22,722,15,699],1060:[676,0,850,25,825],1061:[676,0,722,16,699],1062:[676,176,770,21,753],1063:[676,0,732,7,710],1064:[676,0,1020,21,1001],1065:[676,176,1020,21,1001],1066:[676,0,805,41,757],1067:[676,0,1004,16,985],1068:[676,0,672,19,624],1069:[691,19,685,37,648],1070:[691,19,955,21,920],1071:[676,0,736,12,687],1072:[473,14,517,42,505],1073:[691,14,500,25,476],1074:[461,0,492,21,475],1075:[461,0,451,21,434],1076:[461,143,541,19,524],1077:[473,14,444,25,427],1078:[467,0,762,14,748],1079:[473,17,446,25,420],1080:[461,0,556,21,543],1081:[691,0,556,21,543],1082:[467,0,556,22,543],1083:[461,11,546,11,529],1084:[461,0,657,20,640],1085:[461,0,560,21,543],1086:[473,14,500,25,476],1087:[461,0,556,21,543],1088:[473,205,556,19,524],1089:[473,14,444,25,430],1090:[461,0,509,18,493],1091:[461,205,520,16,502],1092:[676,205,726,31,693],1093:[461,0,500,12,484],1094:[461,143,556,21,543],1095:[461,0,559,20,542],1096:[461,0,841,21,824],1097:[461,143,841,21,824],1098:[461,0,607,15,592],1099:[461,0,759,22,741],1100:[461,0,498,21,483],1101:[473,14,453,24,429],1102:[473,14,785,21,761],1103:[461,0,526,11,509],1105:[666,14,444,25,427],1106:[676,205,556,15,485],1107:[713,0,451,21,434],1108:[473,14,453,24,429],1109:[473,14,389,25,361],1110:[691,0,278,15,256],1111:[666,0,278,-30,309],1112:[691,203,333,-57,263],1113:[461,11,760,11,745],1114:[461,0,775,21,760],1115:[676,0,556,15,534],1116:[713,0,556,22,543],1118:[691,205,500,16,502],1119:[461,143,556,21,543],1122:[676,0,793,31,745],1123:[676,0,602,15,587],1130:[676,0,1123,30,1088],1131:[461,0,762,14,748],1138:[691,19,778,35,743],1139:[473,14,500,25,476],1140:[691,18,793,16,778],1141:[470,14,559,21,550],1168:[833,0,626,14,594],1169:[602,0,451,21,434]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/Cyrillic.js");
