﻿using com.ecool.service;
using com.ecool.sqlConnection;
using Dapper;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace ECOOL_APP.com.ecool.service
{
    public class SECSharedService
    {
        public bool IsUseZZZI09(UserProfile user, ref ECOOL_DEVEntities db, ref string Message)
        {
            var Permission = PermissionService.GetActionPermissionForBreNO("ZZZI091", user.SCHOOL_NO, user.USER_NO);
            if (Permission.Count() == 0)
            {
                Message = "您無權限使用本功能(1)";
                return false;
            }

            if (Permission.Where(a => a.ActionName == "ExportResultViewMax").Select(a => a.BoolUse).FirstOrDefault() == false)
            {
                if (Permission.Where(a => a.ActionName == "ExportResultViewDate").Select(a => a.BoolUse).FirstOrDefault() == false)
                {
                    Message = "您無權限使用本功能(ExportResultViewDate)";
                    return false;
                }
                else
                {
                    var B01 = db.BDMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();

                    if (B01 == null)
                    {
                        Message = "您無權限查看此學校資料(B01)";
                        return false;
                    }

                    string ZZZI09_USE_S_MMDD = DateTime.Now.Year.ToString() + @"/05/01";

                    if (!string.IsNullOrWhiteSpace(B01.ZZZI09_USE_S_MMDD))
                    {
                        ZZZI09_USE_S_MMDD = DateTime.Now.Year.ToString() + @"/" + B01.ZZZI09_USE_S_MMDD;
                    }

                    DateTime dateFromS_DATE = Convert.ToDateTime(ZZZI09_USE_S_MMDD);

                    if (DateTime.Now.Date < dateFromS_DATE)
                    {
                        Message = "匯出時間未開始";
                        return false;
                    }

                    string ZZZI09_USE_E_MMDD = DateTime.Now.Year.ToString() + @"/07/31";

                    if (!string.IsNullOrWhiteSpace(B01.ZZZI09_USE_E_MMDD))
                    {
                        ZZZI09_USE_E_MMDD = DateTime.Now.Year.ToString() + @"/" + B01.ZZZI09_USE_E_MMDD;
                    }

                    DateTime dateFromE_DATE = Convert.ToDateTime(ZZZI09_USE_E_MMDD);

                    if (DateTime.Now.Date > dateFromE_DATE)
                    {
                        Message = "匯出時間已結束";
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 取得月統計資料
        /// </summary>
        /// <param name="eachMonth"></param>
        /// <param name="wData"></param>
        /// <returns></returns>
        public List<SECSharedChartLineViewModel> GetMonthData(List<string> eachMonth, SECSharedSearchViewModel wData)
        {
            List<SECSharedChartLineViewModel> ReturnData = new List<SECSharedChartLineViewModel>();
            string sSQL = string.Empty;
            DataTable dt;

            string _StrYYMM = "[" + String.Join("],[", eachMonth.ToArray()) + "]";
            string StartMM = eachMonth.Min();

            try
            {
                sSQL = " SELECT TYPE_NAME, " + _StrYYMM;
                sSQL = sSQL + " FROM ";
                sSQL = sSQL + " (  ";
                sSQL = sSQL + " SELECT '" + ADDT01.GetWritingName + "' as TYPE_NAME, a.WRITING_NO  AS ID, CONVERT(nvarchar(6), a.CRE_DATE, 112) as YYMM  ";
                sSQL = sSQL + " FROM ADDT01 a(nolock)  ";
                sSQL = sSQL + " WHERE 1 = 1  ";

                if (string.IsNullOrWhiteSpace(wData.SCHOOL_NO) == false && wData.SCHOOL_NO != SharedGlobal.ALL)
                {
                    sSQL = sSQL + " AND  A.SCHOOL_NO = '" + wData.SCHOOL_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.USER_NO) == false)
                {
                    sSQL = sSQL + " AND A.USER_NO = '" + wData.USER_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.CLASS_NO) == false)
                {
                    sSQL = sSQL + " AND A.CLASS_NO = '" + wData.CLASS_NO + "'  ";
                }

                sSQL = sSQL + " AND A.WRITING_STATUS != '" + (byte)ADDStatus.eADDT01Status.Disable + "'  ";
                sSQL = sSQL + " AND CONVERT(nvarchar(6), a.CRE_DATE, 112) >='" + StartMM + "' ";

                sSQL = sSQL + " UNION ALL  ";

                sSQL = sSQL + " SELECT  '" + ADDT06.GetPassportName + "' as TYPE_NAME, a.APPLY_NO AS ID, CONVERT(nvarchar(6), a.CRE_DATE, 112) as YYMM  ";
                sSQL = sSQL + " FROM ADDT06 a(nolock)  inner join HRMT01 hh on a.SCHOOL_NO = hh.SCHOOL_NO and a.USER_NO=hh.USER_NO ";
                sSQL = sSQL + " WHERE 1 = 1  ";

                if (string.IsNullOrWhiteSpace(wData.SCHOOL_NO) == false && wData.SCHOOL_NO != SharedGlobal.ALL)
                {
                    sSQL = sSQL + " AND  A.SCHOOL_NO = '" + wData.SCHOOL_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.USER_NO) == false)
                {
                    sSQL = sSQL + " AND A.USER_NO = '" + wData.USER_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.CLASS_NO) == false)
                {
                    sSQL = sSQL + " AND A.CLASS_NO = '" + wData.CLASS_NO + "'  ";
                }

                sSQL = sSQL + " AND A.APPLY_STATUS != '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL + "'   and (hh.USER_STATUS <> 8) and	(hh.USER_STATUS <> 9)  ";
                sSQL = sSQL + " AND CONVERT(nvarchar(6), a.CRE_DATE, 112)  >='" + StartMM + "'   ";

                sSQL = sSQL + " ) AS STable  ";
                sSQL = sSQL + " PIVOT  ";
                sSQL = sSQL + "(  ";
                sSQL = sSQL + "Count(ID)  ";
                sSQL = sSQL + "FOR  ";
                sSQL = sSQL + " YYMM IN (" + _StrYYMM + ")  "; //-- 需要動態增長
                sSQL = sSQL + " ) AS PTable ";
                dt = new sqlConnection().executeQueryByDataTableList(sSQL.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    List<int> eachMonthCount = new List<int>();
                    SECSharedChartLineViewModel ItemData = new SECSharedChartLineViewModel();

                    foreach (string MM in eachMonth)
                    {
                        eachMonthCount.Add((dr[MM] == DBNull.Value ? 0 : (int)dr[MM]));
                    }
                    ItemData.TYPE_NAME = (dr["TYPE_NAME"] == DBNull.Value ? "" : (string)dr["TYPE_NAME"]);
                    ItemData.DATA_COUNT = eachMonthCount;

                    ReturnData.Add(ItemData);
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return ReturnData;
        }

        /// <summary>
        /// 取得週統計資料
        /// </summary>
        /// <param name="eachWeek"></param>
        /// <param name="wData"></param>
        /// <returns></returns>
        public List<SECSharedChartLineViewModel> GetWeekData(List<string> eachWeek, SECSharedSearchViewModel wData)
        {
            List<SECSharedChartLineViewModel> ReturnData = new List<SECSharedChartLineViewModel>();
            string sSQL = string.Empty;
            DataTable dt;

            string _StrWK = "[" + String.Join("],[", eachWeek.ToArray()) + "]";

            string StartWK = eachWeek.Min();

            try
            {
                sSQL = " SELECT TYPE_NAME, " + _StrWK;
                sSQL = sSQL + " FROM ";
                sSQL = sSQL + " (  ";
                sSQL = sSQL + " SELECT '" + ADDT01.GetWritingName + "'  as TYPE_NAME, a.WRITING_NO  AS ID,CONVERT(varchar(10),DATEADD(wk,DATEDIFF(wk,0,a.CRE_DATE), 5),111 )  AS WEEK_DATE  ";
                sSQL = sSQL + " FROM ADDT01 a(nolock)  ";
                sSQL = sSQL + " WHERE 1 = 1  ";

                if (string.IsNullOrWhiteSpace(wData.SCHOOL_NO) == false && wData.SCHOOL_NO != SharedGlobal.ALL)
                {
                    sSQL = sSQL + " AND  A.SCHOOL_NO = '" + wData.SCHOOL_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.USER_NO) == false)
                {
                    sSQL = sSQL + " AND A.USER_NO = '" + wData.USER_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.CLASS_NO) == false)
                {
                    sSQL = sSQL + " AND A.CLASS_NO = '" + wData.CLASS_NO + "'  ";
                }

                sSQL = sSQL + " AND A.WRITING_STATUS != '" + (byte)ADDStatus.eADDT01Status.Disable + "'  ";
                sSQL = sSQL + " AND CONVERT(varchar(10),DATEADD(wk,DATEDIFF(wk,0,a.CRE_DATE), 5),111 ) >='" + StartWK + "' ";

                sSQL = sSQL + " UNION ALL  ";

                sSQL = sSQL + " SELECT '" + ADDT06.GetPassportName + "' as TYPE_NAME, a.APPLY_NO AS ID,CONVERT(varchar(10),DATEADD(wk,DATEDIFF(wk,0,a.CRE_DATE), 5),111 )  AS WEEK_DATE  ";
                sSQL = sSQL + " FROM ADDT06 a(nolock)  ";
                sSQL = sSQL + " WHERE 1 = 1  ";

                if (string.IsNullOrWhiteSpace(wData.SCHOOL_NO) == false && wData.SCHOOL_NO != SharedGlobal.ALL)
                {
                    sSQL = sSQL + " AND  A.SCHOOL_NO = '" + wData.SCHOOL_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.USER_NO) == false)
                {
                    sSQL = sSQL + " AND A.USER_NO = '" + wData.USER_NO + "'  ";
                }

                if (string.IsNullOrWhiteSpace(wData.CLASS_NO) == false)
                {
                    sSQL = sSQL + " AND A.CLASS_NO = '" + wData.CLASS_NO + "'  ";
                }

                sSQL = sSQL + " AND A.APPLY_STATUS != '" + ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL + "'  ";
                sSQL = sSQL + " AND CONVERT(varchar(10),DATEADD(wk,DATEDIFF(wk,0,a.CRE_DATE), 5),111 )  >='" + StartWK + "'   ";

                sSQL = sSQL + " ) AS STable  ";
                sSQL = sSQL + " PIVOT  ";
                sSQL = sSQL + "(  ";
                sSQL = sSQL + "Count(ID)  ";
                sSQL = sSQL + "FOR  ";
                sSQL = sSQL + " WEEK_DATE IN (" + _StrWK + ")  "; //-- 需要動態增長
                sSQL = sSQL + " ) AS PTable ";
                dt = new sqlConnection().executeQueryByDataTableList(sSQL.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    List<int> eachWeekCount = new List<int>();
                    SECSharedChartLineViewModel ItemData = new SECSharedChartLineViewModel();

                    foreach (string WK in eachWeek)
                    {
                        eachWeekCount.Add((dr[WK] == DBNull.Value ? 0 : (int)dr[WK]));
                    }
                    ItemData.TYPE_NAME = (dr["TYPE_NAME"] == DBNull.Value ? "" : (string)dr["TYPE_NAME"]);
                    ItemData.DATA_COUNT = eachWeekCount;

                    ReturnData.Add(ItemData);
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return ReturnData;
        }

        public SECI02ReadIndexViewModel GetArrData(SECI02ReadIndexViewModel Data, int SetMonth)
        {
            //月

            var T01Month = (from a in Data.MonthList
                            where a.TYPE_NAME == ADDT01.GetWritingName
                            select a.DATA_COUNT).FirstOrDefault();

            var T06Month = (from a in Data.MonthList
                            where a.TYPE_NAME == ADDT06.GetPassportName
                            select a.DATA_COUNT).FirstOrDefault();

            if (T01Month != null)
            {
                Data.MonthArrWRITING = T01Month.Cast<object>().ToArray();
            }
            else
            {
                Data.MonthArrWRITING = new int[Data.TEachMonth.ToArray().Length - 1].Cast<object>().ToArray();
            }

            if (T06Month != null)
            {
                Data.MonthArrPASSPORT = T06Month.Cast<object>().ToArray();
            }
            else
            {
                Data.MonthArrPASSPORT = new int[Data.TEachMonth.ToArray().Length - 1].Cast<object>().ToArray();
            }

            /////週
            var ArrEachWeek = DateHelper.GetArrWeekToDate(DateTime.Now, SetMonth).ToArray();

            try
            {
                Data.WeekArrWRITING = (from a in Data.WeekList
                                       where a.TYPE_NAME == ADDT01.GetWritingName
                                       select a.DATA_COUNT).FirstOrDefault().Cast<object>().ToArray();
            }
            catch (Exception)
            {
                Data.WeekArrWRITING = new int[ArrEachWeek.Length - 1].Cast<object>().ToArray();
            }

            try
            {
                Data.WeekArrPASSPORT = (from a in Data.WeekList
                                        where a.TYPE_NAME == ADDT06.GetPassportName
                                        select a.DATA_COUNT).FirstOrDefault().Cast<object>().ToArray();
            }
            catch (Exception)
            {
                Data.WeekArrPASSPORT = new int[ArrEachWeek.Length - 1].Cast<object>().ToArray();
            }

            return Data;
        }

        /// <summary>
        /// 取得各類別 Cash 點數、百分比
        /// </summary>
        /// <param name="db"></param>
        /// <param name="Search"></param>
        /// <param name="ToTAL_CASH_ALL"></param>
        /// <returns></returns>
        public List<SECSharedCashPreViewModel> GetCashPre(ref ECOOL_DEVEntities db, SECSharedSearchViewModel Search, int ToTAL_CASH_ALL)
        {
            List<SECSharedCashPreViewModel> PerData = new List<SECSharedCashPreViewModel>();

            var TempHRMT01 = db.HRMT01.Where(B => (!UserStaus.NGUserStausList.Contains(B.USER_STATUS)));

            if (string.IsNullOrWhiteSpace(Search.SCHOOL_NO) == false && Search.SCHOOL_NO != SharedGlobal.ALL)
            {
                TempHRMT01 = TempHRMT01.Where(B => B.SCHOOL_NO == Search.SCHOOL_NO);
            }

            if (string.IsNullOrWhiteSpace(Search.USER_NO) == false)
            {
                TempHRMT01 = TempHRMT01.Where(B => B.USER_NO == Search.USER_NO);
            }

            if (string.IsNullOrWhiteSpace(Search.CLASS_NO) == false)
            {
                TempHRMT01 = TempHRMT01.Where(B => B.CLASS_NO == Search.CLASS_NO);
            }

            //各類別 酷幣點數，及比例
            var Temp = (from A in db.AWAT01_LOG
                        join B in TempHRMT01 on new { A.SCHOOL_NO, A.USER_NO } equals new { B.SCHOOL_NO, B.USER_NO }
                        join C in db.AWAT01_X_REF on new { A.SOURCE_TYPE } equals new { C.SOURCE_TYPE } into REF
                        from X in REF.DefaultIfEmpty()
                        group A by new
                        {
                            LOG_DESC = X.CHART_DESC ?? A.LOG_DESC,
                        } into g
                        select new SECSharedCashPreViewModel
                        {
                            LOG_DESC = g.Key.LOG_DESC,
                            SUM_ADD_CASH_ALL = g.Sum(a => a.ADD_CASH_ALL),
                            PRE = Math.Round(((Double)g.Sum(a => a.ADD_CASH_ALL) / (Double)ToTAL_CASH_ALL) * 100, 1)
                        }).Where(t => t.SUM_ADD_CASH_ALL > 0).OrderByDescending(t => t.SUM_ADD_CASH_ALL);

            PerData = Temp.ToList();

            return PerData;
        }

        /// <summary>
        /// 取得各類別 Cash 點數、百分比
        /// </summary>
        /// <param name="db"></param>
        /// <param name="Search"></param>
        /// <param name="ToTAL_CASH_ALL"></param>
        /// <returns></returns>
        public List<SECSharedCashPreViewModel> GetCashPre(ref ECOOL_DEVEntities db, SECSharedSearchViewModel Search)
        {
            int? ToTAL_CASH_ALL = GetTotalCash(ref db, Search).ToTAL_CASH_ALL;

            return GetCashPre(ref db, Search, (int)ToTAL_CASH_ALL);
        }

        /// <summary>
        /// 取得 Cash Total
        /// </summary>
        /// <param name="db"></param>
        /// <param name="Search"></param>
        /// <returns></returns>
        public SECSharedTotalCashDataViewModel GetTotalCash(ref ECOOL_DEVEntities db, SECSharedSearchViewModel Search)
        {
            SECSharedTotalCashDataViewModel TData = new SECSharedTotalCashDataViewModel();

            string sSQL = $@" Select isnull(Sum(A.CASH_ALL),0) as SINACE_Total_CASH_ALL
                            ,isnull(Sum(Case When B.USER_NO is not null Then A.CASH_ALL Else 0 End),0) as ToTAL_CASH_ALL
                            ,isnull(Sum(Case When B.USER_NO is not null Then A.CASH_AVAILABLE else 0 end ),0) as ToTAL_CASH_AVAILABLE
                             from AWAT01 A (nolock)
                             left join  HRMT01 B  (nolock)  on A.SCHOOL_NO=B.SCHOOL_NO and  A.USER_NO = B.USER_NO and  B.USER_STATUS not in ('{UserStaus.Disable}','{UserStaus.Invalid}')
                             Where 1=1  ";

            if (!string.IsNullOrWhiteSpace(Search.SCHOOL_NO) && Search.SCHOOL_NO != SharedGlobal.ALL)
            {
                sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.USER_NO))
            {
                sSQL = sSQL + " and B.USER_NO=@USER_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.CLASS_NO))
            {
                sSQL = sSQL + " and B.CLASS_NO=@CLASS_NO";
            }

            TData = db.Database.Connection.Query<SECSharedTotalCashDataViewModel>(sSQL
              , new
              {
                  SCHOOL_NO = Search.SCHOOL_NO,
                  USER_NO = Search.USER_NO,
                  CLASS_NO = Search.CLASS_NO,
              }).FirstOrDefault();

            if (TData == null)
            {
                TData = new SECSharedTotalCashDataViewModel
                {
                    SINACE_Total_CASH_ALL = 0,
                    ToTAL_CASH_ALL = 0,
                    ToTAL_CASH_AVAILABLE = 0
                };
            }

            return TData;
        }

        public List<SECI02PeopleCountViewModel> GetPeopleCountData(ref ECOOL_DEVEntities db, SECSharedSearchViewModel Search)
        {
            string sSQL = $@" Select CONVERT(nvarchar(6),A.ACTIONTIME,112) YYMM,count(*) People_Count
                            ,COUNT(DISTINCT Case When isnull(a.USER_NO,'')<>'' Then isnull(a.SCHOOL_NO,'')+'-'+isnull(a.USER_NO,'') Else isnull(a.SCHOOL_NO,'')+'-'++a.IP_ADRESS End) People_Num
                            ,COUNT( DISTINCT  Case When a.ACTION_ID like 'APP%' Then Case When isnull(a.USER_NO,'')<>'' Then isnull(a.SCHOOL_NO,'')+'-'+isnull(a.USER_NO,'') Else isnull(a.SCHOOL_NO,'')+'-'++a.IP_ADRESS End End) APP_People_Num
                            from ZZT17 A (nolock)
                            left join  HRMT01 B  (nolock)  on A.SCHOOL_NO=B.SCHOOL_NO and  A.USER_NO = B.USER_NO and  B.USER_STATUS not in ('{UserStaus.Disable}','{UserStaus.Invalid}')
                            where a.ACTIONTIME >=  DATEADD(year,-1,getdate()) and A.LOG_STATUS='LoginSuccess' ";

            if (!string.IsNullOrWhiteSpace(Search.SCHOOL_NO) && Search.SCHOOL_NO != SharedGlobal.ALL)
            {
                sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.USER_NO))
            {
                sSQL = sSQL + " and B.USER_NO=@USER_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.CLASS_NO))
            {
                sSQL = sSQL + " and B.CLASS_NO=@CLASS_NO";
            }

            sSQL = sSQL + @" group by CONVERT(nvarchar(6),a.ACTIONTIME,112)
                          order by YYMM ";

            var TData = db.Database.Connection.Query<SECI02PeopleCountViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = Search.SCHOOL_NO,
                 USER_NO = Search.USER_NO,
                 CLASS_NO = Search.CLASS_NO,
             }).ToList();

            return TData;
        }

        public List<SECI02PeopleUnusedViewModel> GetPeopleUnusedData(ref ECOOL_DEVEntities db, SECSharedSearchViewModel Search)
        {
            string sSQL = $@"select a.SCHOOL_NO,a.CLASS_NO,a.SEAT_NO, a.USER_NO ,a.NAME
                            from HRMT01 a (nolock)
                            left outer join ZZT17 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.USER_NO=b.USER_NO and b.ACTIONTIME>=DATEADD(M,-1,getdate())
                            where b.USER_NO is null
                            and a.USER_STATUS not in ('{UserStaus.Disable}','{UserStaus.Invalid}')
                            and a.USER_TYPE='{UserType.Student}'  ";

            if (!string.IsNullOrWhiteSpace(Search.SCHOOL_NO))
            {
                sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.USER_NO))
            {
                sSQL = sSQL + " and B.USER_NO=@USER_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.GRADE))
            {
                sSQL = sSQL + " and left(A.CLASS_NO,1)=@GRADE";
            }

            if (!string.IsNullOrWhiteSpace(Search.CLASS_NO))
            {
                sSQL = sSQL + " and A.CLASS_NO=@CLASS_NO";
            }

            sSQL = sSQL + @" group by  a.SCHOOL_NO,a.CLASS_NO,a.SEAT_NO,a.USER_NO ,a.NAME
                             order by a.SCHOOL_NO,a.CLASS_NO,a.USER_NO";

            var TData = db.Database.Connection.Query<SECI02PeopleUnusedViewModel>(sSQL
            , new
            {
                SCHOOL_NO = Search.SCHOOL_NO,
                USER_NO = Search.USER_NO,
                CLASS_NO = Search.CLASS_NO,
                GRADE = Search.GRADE,
            }).ToList();

            return TData;
        }

        public List<SECI02PeopleUnusedViewModel> GetBookPeopleUnusedData(ref ECOOL_DEVEntities db, SECSharedSearchViewModel Search)
        {
            string sSQL = $@"select a.SCHOOL_NO,a.CLASS_NO,a.SEAT_NO, a.USER_NO ,a.NAME
                            from HRMT01 a (nolock)
                            left outer join DB2_L_WORK b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.IDNO=b.NO_READ and b.BORROW_DATE>=DATEADD(M,-1,getdate())
                            where b.NO_READ is null
                            and a.USER_STATUS not in ('{UserStaus.Disable}','{UserStaus.Invalid}')
                            and a.USER_TYPE='{UserType.Student}'  ";

            if (!string.IsNullOrWhiteSpace(Search.SCHOOL_NO))
            {
                sSQL = sSQL + " and a.SCHOOL_NO=@SCHOOL_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.USER_NO))
            {
                sSQL = sSQL + " and a.USER_NO=@USER_NO";
            }

            if (!string.IsNullOrWhiteSpace(Search.GRADE))
            {
                sSQL = sSQL + " and left(A.CLASS_NO,1)=@GRADE";
            }

            if (!string.IsNullOrWhiteSpace(Search.CLASS_NO))
            {
                sSQL = sSQL + " and A.CLASS_NO=@CLASS_NO";
            }

            sSQL = sSQL + @" group by  a.SCHOOL_NO,a.CLASS_NO,a.SEAT_NO,a.USER_NO ,a.NAME
                             order by a.SCHOOL_NO,a.CLASS_NO,a.USER_NO";

            var TData = db.Database.Connection.Query<SECI02PeopleUnusedViewModel>(sSQL
            , new
            {
                SCHOOL_NO = Search.SCHOOL_NO,
                USER_NO = Search.USER_NO,
                CLASS_NO = Search.CLASS_NO,
                GRADE = Search.GRADE,
            }).ToList();

            return TData;
        }

        public SECI02BorrowIndexViewModel GetBorrowData(SECI02BorrowIndexViewModel model, ref ECOOL_DEVEntities db)
        {
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            string sSQLClass = ",a.CLASS_NO";

            if (model.SearchSCHOOL_NO == SharedGlobal.ALL)
            {
                sSQLClass = "";
            }

            string sSQL = $@"select a.SCHOOL_NO,c.SHORT_NAME {sSQLClass}
                            ,sum(case When b.BORROW_DATE is not null Then 1 else 0 end) as BORROW_COUNT
                            from HRMT01 a  (nolock)
                            join BDMT01 c (nolock) on a.SCHOOL_NO =c.SCHOOL_NO
                            left outer join  DB2_L_WORK b (nolock) on a.IDNO=b.NO_READ and b.SEYEAR=@SEYEAR ";

            if (!string.IsNullOrWhiteSpace(model.WhereMM))
            {
                sSQL = sSQL + " and  month(b.BORROW_DATE) = @WhereMM ";
            }

            sSQL = sSQL + $@" where  a.USER_TYPE='{UserType.Student}'  and a.USER_STATUS<>'{UserStaus.Invalid}' ";

            if (!string.IsNullOrWhiteSpace(model.SearchSCHOOL_NO) && model.SearchSCHOOL_NO != SharedGlobal.ALL)
            {
                sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO";
            }

            sSQL = sSQL + $@" group by  a.SCHOOL_NO,c.SHORT_NAME {sSQLClass}
                              order by a.SCHOOL_NO { sSQLClass}";

            model.BorrowList = db.Database.Connection.Query<SECI02BorrowListViewModel>(sSQL
            , new
            {
                SCHOOL_NO = model.SearchSCHOOL_NO,
                SEYEAR = SYear.ToString(),
                WhereMM = model.WhereMM,
            }).ToList() ?? new List<SECI02BorrowListViewModel>();

            return model;
        }

        public SECI02UseSituationIndexViewModel GetUseSituationTotalData(ref ECOOL_DEVEntities db, SECI02UseSituationIndexViewModel Data)
        {
            string sSQL = @" Select T.SCHOOL_NO,T.SHORT_NAME ";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",T.CLASS_NO ";
            }
            else
            {
                sSQL = sSQL + " ,Sum(T.People_Count) People_Count ";
                sSQL = sSQL + " ,Sum(T.ADD_CASH_ALL) ADD_CASH_ALL ";
            }

            sSQL = sSQL + @" ,Sum(T.ADDT01_ToTAL) ADDT01_ToTAL
                                 ,Sum(T.ADDT06_ToTAL) ADDT06_ToTAL
                                 ,Sum(T.ADDT22_ToTAL) ADDT22_ToTAL
                              from ( " + "\r\n";

            if (!Data.IsClass)
            {
                //人次
                sSQL = sSQL + @" Select A.SCHOOL_NO,C.SHORT_NAME,Count(*) People_Count
                                    ,0 ADD_CASH_ALL,0 as ADDT01_ToTAL,0 ADDT06_ToTAL,0 ADDT22_ToTAL
                                    from ZZT17 A (nolock)
                                    join BDMT01 C  (nolock) on A.SCHOOL_NO=C.SCHOOL_NO
                                    where 1=1 and A.LOG_STATUS='LoginSuccess' ";

                if (!string.IsNullOrWhiteSpace(Data.WhereSCHOOL_NO))
                {
                    sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO ";
                }

                if (!string.IsNullOrWhiteSpace(Data.WhereMM))
                {
                    sSQL = sSQL + " and month(A.ACTIONTIME)=@MM ";
                }

                if (!string.IsNullOrWhiteSpace(Data.WhereSYEAR))
                {
                    sSQL = sSQL + " and A.ACTIONTIME >= CONVERT(datetime,CONVERT(nvarchar(10),@SYEAR+1911)+'/08/01') ";
                    sSQL = sSQL + " and A.ACTIONTIME < DATEADD(year,1,CONVERT(datetime,CONVERT(nvarchar(10),@SYEAR+1911)+'/08/01')) ";
                }

                sSQL = sSQL + "Group by A.SCHOOL_NO,C.SHORT_NAME " + "\r\n";

                sSQL = sSQL + " union All " + "\r\n";

                //酷幣
                sSQL = sSQL + @" select A.SCHOOL_NO,C.SHORT_NAME,0 People_Count,Sum(A.ADD_CASH_ALL) ADD_CASH_ALL
                                    ,0 as ADDT01_ToTAL,0 ADDT06_ToTAL,0 ADDT22_ToTAL
                                    from AWAT01_LOG A (nolock)
                                    join BDMT01 C  (nolock) on A.SCHOOL_NO=C.SCHOOL_NO
                                    where 1=1 ";

                if (!string.IsNullOrWhiteSpace(Data.WhereSCHOOL_NO))
                {
                    sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO ";
                }

                if (!string.IsNullOrWhiteSpace(Data.WhereMM))
                {
                    sSQL = sSQL + " and month(A.LOG_TIME)=@MM ";
                }

                if (!string.IsNullOrWhiteSpace(Data.WhereSYEAR))
                {
                    sSQL = sSQL + " and A.LOG_TIME >= CONVERT(datetime,CONVERT(nvarchar(10),@SYEAR+1911)+'/08/01') ";
                    sSQL = sSQL + " and A.LOG_TIME < DATEADD(year,1,CONVERT(datetime,CONVERT(nvarchar(10),@SYEAR+1911)+'/08/01')) ";
                }

                sSQL = sSQL + " group by A.SCHOOL_NO,C.SHORT_NAME " + "\r\n";

                sSQL = sSQL + " union All " + "\r\n";
            }

            //線上

            sSQL = sSQL + " select A.SCHOOL_NO,C.SHORT_NAME ";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",A.CLASS_NO ";
            }

            sSQL = sSQL + @" ,0 People_Count,0 as ADD_CASH_ALL
                                ,count(*) as ADDT01_ToTAL,0 ADDT06_ToTAL,0 ADDT22_ToTAL
                                from ADDT01 A (nolock)
                                join BDMT01 C  (nolock) on A.SCHOOL_NO=C.SCHOOL_NO
                                Where A.WRITING_STATUS!='9' ";

            if (!string.IsNullOrWhiteSpace(Data.WhereSCHOOL_NO))
            {
                sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO ";
            }

            if (!string.IsNullOrWhiteSpace(Data.WhereMM))
            {
                sSQL = sSQL + " and month(A.CRE_DATE)=@MM ";
            }

            if (!string.IsNullOrWhiteSpace(Data.WhereSYEAR))
            {
                sSQL = sSQL + " and A.SYEAR=@SYEAR ";
            }

            sSQL = sSQL + " group by a.SCHOOL_NO,C.SHORT_NAME  " + "\r\n";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",A.CLASS_NO " + "\r\n";
            }

            //閱讀
            sSQL = sSQL + " union All " + "\r\n";

            sSQL = sSQL + " Select A.SCHOOL_NO,C.SHORT_NAME ";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",A.CLASS_NO ";
            }

            sSQL = sSQL + @",0 People_Count,0 as ADD_CASH_ALL
                                ,0 as ADDT01_ToTAL,count(*)  ADDT06_ToTAL,0 ADDT22_ToTAL
                                from ADDT06 A (nolock)
                                join BDMT01 C  (nolock) on A.SCHOOL_NO=C.SCHOOL_NO
                                Where A.APPLY_STATUS!='9' ";

            if (!string.IsNullOrWhiteSpace(Data.WhereSCHOOL_NO))
            {
                sSQL = sSQL + " and A.SCHOOL_NO=@SCHOOL_NO ";
            }

            if (!string.IsNullOrWhiteSpace(Data.WhereMM))
            {
                sSQL = sSQL + " and month(A.CRE_DATE)=@MM ";
            }

            if (!string.IsNullOrWhiteSpace(Data.WhereSYEAR))
            {
                sSQL = sSQL + " and A.SYEAR=@SYEAR ";
            }

            sSQL = sSQL + " group by a.SCHOOL_NO,C.SHORT_NAME  " + "\r\n";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",A.CLASS_NO " + "\r\n";
            }

            //藝廊
            sSQL = sSQL + " union All " + "\r\n";

            sSQL = sSQL + " Select A.PHOTO_SCHOOL_NO as SCHOOL_NO,C.SHORT_NAME ";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",A.PHOTO_CLASS_NO as CLASS_NO";
            }

            sSQL = sSQL + @",0 People_Count,0 as ADD_CASH_ALL
                                ,0 as ADDT01_ToTAL,0  ADDT06_ToTAL,count(*) ADDT22_ToTAL
                                from ADDT22 A (nolock)
                                inner join ADDT21 B (nolock) on A.ART_GALLERY_NO=B.ART_GALLERY_NO
                                join BDMT01 C  (nolock) on A.PHOTO_SCHOOL_NO=C.SCHOOL_NO
                                Where B.STATUS!='9' and A.PHOTO_STATUS='0' ";

            if (!string.IsNullOrWhiteSpace(Data.WhereSCHOOL_NO))
            {
                sSQL = sSQL + " and A.PHOTO_SCHOOL_NO=@SCHOOL_NO ";
            }

            if (!string.IsNullOrWhiteSpace(Data.WhereMM))
            {
                sSQL = sSQL + " and month(A.CRE_DATE)=@MM ";
            }

            if (!string.IsNullOrWhiteSpace(Data.WhereSYEAR))
            {
                sSQL = sSQL + " and A.PHOTO_SYEAR=@SYEAR ";
            }

            sSQL = sSQL + " group by  A.PHOTO_SCHOOL_NO,C.SHORT_NAME " + "\r\n";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",A.PHOTO_CLASS_NO " + "\r\n";
            }
            sSQL = sSQL + " ) as T " + "\r\n";
            sSQL = sSQL + " Group by T.SCHOOL_NO,T.SHORT_NAME " + "\r\n";

            if (Data.IsClass)
            {
                sSQL = sSQL + ",T.CLASS_NO " + "\r\n";
            }

            sSQL = sSQL + " order by T.SCHOOL_NO,T.SHORT_NAME" + "\r\n";
            if (Data.IsClass)
            {
                sSQL = sSQL + ",T.CLASS_NO " + "\r\n";
            }

            Data.UseSituationTotal = db.Database.Connection.Query<SECI02UseSituationTotalIndexViewModel>(sSQL
           , new
           {
               SCHOOL_NO = Data.WhereSCHOOL_NO,
               MM = Data.WhereMM,
               SYEAR = Data.WhereSYEAR,
           }).ToList();

            Data.UseSituationTotal.Add(new SECI02UseSituationTotalIndexViewModel()
            {
                SCHOOL_NO = "ALL",
                SHORT_NAME = "合計",
                CLASS_NO = "合計",
                People_Count = Data.UseSituationTotal.AsEnumerable().Sum(o => o.People_Count),
                ADD_CASH_ALL = Data.UseSituationTotal.AsEnumerable().Sum(o => o.ADD_CASH_ALL),
                ADDT01_ToTAL = Data.UseSituationTotal.AsEnumerable().Sum(o => o.ADDT01_ToTAL),
                ADDT06_ToTAL = Data.UseSituationTotal.AsEnumerable().Sum(o => o.ADDT06_ToTAL),
                ADDT22_ToTAL = Data.UseSituationTotal.AsEnumerable().Sum(o => o.ADDT22_ToTAL),
            });

            return Data;
        }
    }
}