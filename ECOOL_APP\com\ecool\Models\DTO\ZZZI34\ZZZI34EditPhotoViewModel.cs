﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace ECOOL_APP.EF
{
     public class ZZZI34EditPhotoViewModel
    {

        public string index { get; set; }

        public int ShowBtn { get; set; }

        public string ART_GALLERY_TYPE { get; set; }

        public string WORK_TYPE { get; set; }
        public bool? AutherYN { get; set; }
        /// <summary>
        ///照片 NO.
        /// </summary>
        [DisplayName("照片 NO.")]
        public string PHOTO_NO { get; set; }

        /// <summary>
        ///藝廊 NO.
        /// </summary>
        [DisplayName("藝廊 NO.")]
        public string ART_GALLERY_NO { get; set; }
        public string PHOTO_SNAME { get; set; }
        /// <summary>
        ///照片作者學校代碼
        /// </summary>
        [DisplayName("照片作者學校代碼")]
        public string PHOTO_SCHOOL_NO { get; set; }

        /// <summary>
        ///照片作者帳號
        /// </summary>
        [DisplayName("照片作者帳號")]
        public string PHOTO_USER_NO { get; set; }


        /// <summary>
        ///照片作者班級
        /// </summary>
        [DisplayName("照片作者班級")]
        public string PHOTO_CLASS_NO { get; set; }

        /// <summary>
        ///照片檔名
        /// </summary>
        [DisplayName("照片檔名")]
        public string PHOTO_FILE { get; set; }

        /// <summary>
        ///照片描述
        /// </summary>
        [DisplayName("主題")]

        public string PHOTO_SUBJECT { get; set; }

        /// <summary>
        ///
        /// </summary>
        [DisplayName("內容")]
        public string PHOTO_DESC { get; set; }

        /// <summary>
        ///給予點數(照片作者)
        /// </summary>
        [DisplayName("給予點數(照片作者)")]
        public short? PHOTO_CASH { get; set; }


        /// <summary>
        ///照片狀態
        /// </summary>
        [DisplayName("照片狀態")]
        public string PHOTO_STATUS { get; set; }

        /// <summary>
        ///照片排序
        /// </summary>
        [DisplayName("照片排序")]
        public int? PHOTO_ORDER_BY { get; set; }

        public DateTime? PHOTO_CHG_DATE { get; set; }
    public HttpPostedFileBase PhotoFiles { get; set; }

    }
}