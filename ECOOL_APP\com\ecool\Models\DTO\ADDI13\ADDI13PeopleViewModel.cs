﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO.ADDI13
{
    public  class ADDI13PeopleViewModel
    {
        [DisplayName("投票者學校")]
        public string SCHOOL_NO { get; set; }
        public string ROLL_CALL_ID { get; set; }
        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        [DisplayName("座號")]
        public string SEAT_NO { get; set; }
        /// <summary>
        ///投票者帳號
        /// </summary>
        [DisplayName("投票者帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///投票者姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///投票者簡稱
        /// </summary>
        [DisplayName("姓名")]
        public string SNAME { get; set; }
    }
}
