﻿/*
 * Inline Form Validation Engine 1.7, jQuery plugin
 * 
 * Copyright(c) 2010, <PERSON><PERSON>
 * http://www.position-relative.net
 *	
 * Form validation engine allowing custom regex rules to be added.
 * Thanks to <PERSON><PERSON> and <PERSON> 
 * and everyone helping me find bugs on the forum
 * Licenced under the MIT Licence
 */
 
(function ($) {
    
    $.fn.validationEngine = function (settings) {
        if ($.validationEngineLanguage) {				// IS THERE A LANGUAGE LOCALISATION ?
            allRules = $.validationEngineLanguage.allRules;
        } else {
            $.validationEngine.debug("Validation engine rules are not loaded check your external file");
        }
        settings = jQuery.extend({
            allrules: allRules,
            validationEventTriggers: "focusout",
            inlineValidation: true,
            returnIsValid: false,
            unbindEngine: true,
            containerOverflow: false,
            containerOverflowDOM: "",
            ajaxSubmit: false,
            scroll: true,
            promptPosition: "topRight", // OPENNING BOX POSITION, IMPLEMENTED: topLeft, topRight, bottomLeft, centerRight, bottomRight
            success: false,
            beforeSuccess: function () { },
            failure: function () { }
        }, settings);
        $.validationEngine.settings = settings;
        $.validationEngine.ajaxValidArray = new Array(); // ARRAY FOR AJAX: VALIDATION MEMORY 

        if (settings.inlineValidation == true) { 		// Validating Inline ?
            function _inlinEvent(caller) {
                $.validationEngine.settings = settings;
                if ($.validationEngine.intercept == false || !$.validationEngine.intercept) {		// STOP INLINE VALIDATION THIS TIME ONLY
                    $.validationEngine.onSubmitValid = false;
                    $.validationEngine.loadValidation(caller);
                } else {
                    $.validationEngine.intercept = false;
                }
            }
        }
        if (settings.returnIsValid) {		// Do validation and return true or false, it bypass everything;
            if ($.validationEngine.submitValidation(this, settings)) {
                return false;
            } else {
                return true;
            }
        }
        $(this).bind("submit", function (caller) {   // ON FORM SUBMIT, CONTROL AJAX FUNCTION IF SPECIFIED ON DOCUMENT READY
            $.validationEngine.onSubmitValid = true;
            $.validationEngine.settings = settings;
            if ($.validationEngine.submitValidation(this, settings) == false) {
                if ($.validationEngine.submitForm(this, settings) == true) return false;
            } else {
                settings.failure && settings.failure();
                return false;
            }
        })
    };
    $.validationEngine = {
        defaultSetting: function (caller) {		// NOT GENERALLY USED, NEEDED FOR THE API, DO NOT TOUCH
            if ($.validationEngineLanguage) {
                allRules = $.validationEngineLanguage.allRules;
            } else {
                $.validationEngine.debug("Validation engine rules are not loaded check your external file");
            }
            settings = {
                allrules: allRules,
                validationEventTriggers: "blur",
                inlineValidation: true,
                containerOverflow: false,
                containerOverflowDOM: "",
                returnIsValid: false,
                scroll: true,
                unbindEngine: true,
                ajaxSubmit: false,
                promptPosition: "topRight", // OPENNING BOX POSITION, IMPLEMENTED: topLeft, topRight, bottomLeft, centerRight, bottomRight
                success: false,
                failure: function () { }
            }
            $.validationEngine.settings = settings;
        },
        loadValidation: function (caller) {		// GET VALIDATIONS TO BE EXECUTED
            if (!$.validationEngine.settings) $.validationEngine.defaultSetting()
            rulesParsing = $(caller).attr('class');
            rulesRegExp = /\[(.*)\]/;
            getRules = rulesRegExp.exec(rulesParsing);
            if (getRules == null) return false;
            str = getRules[1];
            pattern = /\[|,|\]/;
            result = str.split(pattern);
            var validateCalll = $.validationEngine.validateCall(caller, result)
            return validateCalll;
        },
        validateCall: function (caller, rules) {	// EXECUTE VALIDATION REQUIRED BY THE USER FOR THIS FIELD
            
            var promptText = ""

            if (!$(caller).attr("id")) $.validationEngine.debug("This field have no ID attribut( name & class displayed): " + $(caller).attr("name") + " " + $(caller).attr("class"))

            caller = caller;
            ajaxValidate = false;
            var callerName = $(caller).attr("name");
            $.validationEngine.isError = false;
            $.validationEngine.showTriangle = true;
            callerType = $(caller).attr("type");

            for (i = 0; i < rules.length; i++) {
                switch (rules[i]) {
                    case "custID":
                        _custID(caller, rules);
                        break;
                    case "copID":
                        _copID(caller, rules);
                        break;
                    case "custIDorCopID":
                        _custIDorCopID(caller, rules);
                        break;
                    case "dateCheck":
                        _dateCheck(caller, rules, i);
                        break;
                    case "dateDiff":
                        _dateDiff(caller, rules, i);
                        break;
                    case "ValidateDoubleSecialChar":
                        _ValidateDoubleSecialChar(caller, rules);
                        break;
                    case "number":
                        _number(caller, rules);
                        break;
                    case "optional":
                        if (!$(caller).val()) {
                            $.validationEngine.closePrompt(caller);
                            return $.validationEngine.isError;
                        }
                        break;
                    case "required":
                        _required(caller, rules);
                        break;
                    case "custom":
                        _customRegex(caller, rules, i);
                        break;
                    case "exemptString":
                        _exemptString(caller, rules, i);
                        break;
                    case "ajax":
                        if (!$.validationEngine.onSubmitValid) _ajax(caller, rules, i);
                        break;
                    case "length":
                        _length(caller, rules, i);
                        break;
                    case "maxCheckbox":
                        _maxCheckbox(caller, rules, i);
                        groupname = $(caller).attr("name");
                        caller = $("input[name='" + groupname + "']");
                        break;
                    case "minCheckbox":
                        _minCheckbox(caller, rules, i);
                        groupname = $(caller).attr("name");
                        caller = $("input[name='" + groupname + "']");
                        break;
                    case "confirm":
                        _confirm(caller, rules, i);
                        break;
                    case "funcCall":
                        _funcCall(caller, rules, i);
                        break;
                    case "seqnoDiff":
                        _seqnoDiff(caller, rules, i);
                        break;
                    case "intLimit":
                        _intLimit(caller, rules, i);
                        break;
                    case "checkCreditCARD":
                        _checkCreditCARD(caller, rules, i);
                        break;
                    case "telandacd":
                        _telandacd(caller, rules, i);
                        break;
                    case "stringDiff":
                        _stringDiff(caller, rules, i);
                        break;
                    default: ;
                };
            };
            radioHack();
            if ($.validationEngine.isError == true) {
                var linkTofieldText = "." + $.validationEngine.linkTofield(caller);
                if (linkTofieldText != ".") {
                    if (!$(linkTofieldText)[0]) {
                        $.validationEngine.buildPrompt(caller, promptText, "error")
                    } else {
                        $.validationEngine.updatePromptText(caller, promptText);
                    }
                } else {
                    $.validationEngine.updatePromptText(caller, promptText);
                }
            } else {
                $.validationEngine.closePrompt(caller);
            }

            function _custID(caller, rules) {
                feildValue = $(caller).attr('value');

                if (undefined == feildValue || "" == feildValue) {
                    return;
                }

                if (feildValue.length != 10 && feildValue.length != 11) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["custID"].alertText + "<br />";
                    return;
                }

                /*正規表示法規範區*/
                Re1 = /^[A-Za-z][1-2]{1}\d{8}/g; //第一碼須為英文,第二碼須為1或2,後8碼須為數字
                Re2 = /^[A-Za-z][1-2]{1}\d{9}/g; //第一碼須為英文,第二碼須為1或2,後9碼須為數字
                Re3 = /\d{8}[A-Za-z]{2}/g; //外國人前8碼數字,後兩碼英文
                Re4 = /\d{10}/g; //大陸人10碼為數字
                Re5 = /[A-Za-z]{2}\d{8}/g; //FNS外國人前兩碼英文,後8碼數字                
                if (Re1.test(feildValue) || Re2.test(feildValue)) {
                    //本國人檢核程式
                    //依法規判斷身份字號 
                    str = feildValue;
                    Temp = "";
                    TempID = "";
                    CheckSum = 0;
                    Weight = new Array(1, 9, 8, 7, 6, 5, 4, 3, 2, 1, 1);
                    Location1 = new Array("A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "I", "O");
                    Location2 = new Array("10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "32", "30", "31", "33", "34", "35");

                    for (x = 0; x < Location1.length; x++) {
                        if ((str.substring(0, 1).toUpperCase()) == Location1[x]) {
                            Temp = Location2[x];
                        }
                    }

                    TempID = Temp + str.substring(1, str.length);

                    for (x = 0; x < TempID.length; x++) {
                        w = eval(Weight[x]);
                        y = eval(TempID.substring(x, x + 1));
                        CheckSum += w * y;
                    }

                    if (CheckSum % 10 != 0) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["custID"].alertText + "<br />";
                        return;
                    }
                }
                else if (Re3.test(caller) || Re5.test(caller)) {
                }
                else if (Re4.test(caller)) {
                }
                else {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["custID"].alertText + "<br />";
                }
            }
            function _copID(caller, rules) {
                feildValue = $(caller).attr('value');

                if (undefined == feildValue || "" == feildValue) {
                    return;
                }

                if (feildValue.length != 8) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["copID"].alertText + "<br />";
                    return;
                }
                else {
                    var tmp = new String("12121241");
                    re = /^\d{8}$/;
                    if (!re.test(feildValue)) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["copID"].alertText + "<br />";
                        return;
                    }

                    var sum = 0;
                    for (i = 0; i < 8; i++) {
                        var n = parseInt(feildValue.substr(i, 1)) * parseInt(tmp.substr(i, 1));
                        var tempSum = 0;
                        while (n != 0) {
                            tempSum += (n % 10);
                            n = (n - n % 10) / 10;  // 取整數
                        }
                        sum += tempSum;
                    }

                    if (0 != sum % 10) {
                        if (feildValue.substr(6, 1) == "7") {
                            if (0 == (sum + 1) % 10) {
                                return;
                            }
                            else {
                                $.validationEngine.isError = true;
                                promptText += $.validationEngine.settings.allrules["copID"].alertText + "<br />";
                                return;
                            }
                        }
                    }

                    if (0 != sum % 10) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["copID"].alertText + "<br />";
                    }
                }
            }
            function _custIDorCopID(caller, rules) {
                feildValue = $(caller).attr('value');

                if (undefined == feildValue || "" == feildValue) {
                    return;
                }

                if (8 == feildValue.length) {
                    _copID(caller, rules);
                }
                else if (10 == feildValue.length || 11 == feildValue.length) {
                    _custID(caller, rules);
                }
                else {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["custIDorCopID"].alertText + "<br />";
                }
            }
            function _dateCheck(caller, rules, position) {
                /*
                Purpose: return true if the date is valid, false otherwise
                Arguments: day integer representing day of month
                month integer representing month of year
                year integer representing year
                Variables: dteDate - date object
                */
                var dteDate, month, day, year;
                var txtDate = $(caller).attr('value');
                //var format = rules[position + 1];  //沒有在用
                var objRegExp;

                //有空值就不比對
                if (undefined == txtDate || "" == txtDate || null == txtDate) {
                    return;
                }

                var txtlength = txtDate.length;

                if (txtlength !== 6 && txtlength !== 7 && txtlength !== 8 && txtlength !== 10) {     // 6 => yyyyMM ,7=> yyyy-MM ,8 => yyyyMMdd ,10 => yyyy-MM-dd
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["dateCheck"].alertText + "<br />";
                    return;
                }


                switch (txtlength) {
                    case 6:
                        objRegExp = /^\d{4}\d{2}$/;     //yyyyMM

                        if (!objRegExp.test(txtDate)) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertText + "yyyyMM<br />";
                            return;
                        }
                        if (txtDate.length !== 6) {
                            return;
                        }
                        else {
                            year = parseInt(txtDate.substr(0, 4), 10) - 0;
                            month = parseInt(txtDate.substr(4, 2), 10) - 1; // because months in JS start from 0    
                            day = parseInt(1, 10) - 0;
                        }
                        if (year < 1000 || year > 10000) { //年可以輸入到9999
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextGreater + "<br />";
                            return;
                        }
                        //set up a Date object based on the day, month and year arguments
                        //javascript months start at 0 (0-11 instead of 1-12)
                        dteDate = new Date(year, month, day);
                        /*
                        Javascript Dates are a little too forgiving and will change the date to a reasonable guess if it's invalid. We'll use this to our advantage by creating the date object and then comparing it to the details we put it. If the Date object is different, then it must have been an invalid date to start with...
                        */
                        if ((month == dteDate.getMonth()) && (year == dteDate.getFullYear())) {

                        }
                        else {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextTarget + "<br />";
                            return;
                        }

                        break;

                    case 8:
                        objRegExp = /^\d{4}\d{2}\d{2}$/;    //yyyyMMdd

                        if (!objRegExp.test(txtDate)) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertText + "yyyyMMdd<br />";
                            return;
                        }

                        if (txtDate.length !== 8) {
                            return;
                        }
                        else {
                            year = parseInt(txtDate.substr(0, 4), 10) - 0;
                            month = parseInt(txtDate.substr(4, 2), 10) - 1; // because months in JS start from 0    
                            day = parseInt(txtDate.substr(6, 2), 10) - 0;
                        }
                        if (year < 1000 || year > 10000) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextGreater + "<br />";
                            return;
                        }
                        dteDate = new Date(year, month, day);
                        if ((day == dteDate.getDate()) && (month == dteDate.getMonth()) && (year == dteDate.getFullYear())) {

                        }
                        else {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextTarget + "<br />";
                            return;
                        }
                        break;

                    case 7:
                        objRegExp = /^\d{4}(\-|\/|\.)\d{2}$/;    //yyyy-MM

                        if (!objRegExp.test(txtDate)) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertText + "yyyy-MM<br />";
                            return;
                        }

                        if (txtDate.length !== 7) {
                            return;
                        } else {
                            var ArrayDate = txtDate.split("-");

                            year = parseInt(ArrayDate[0], 10) - 0;
                            month = parseInt(ArrayDate[1], 10) - 1; // because months in JS start from 0    
                            day = parseInt(1, 10) - 0;

                        }
                        if (year < 1000 || year > 3000) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextGreater + "<br />";
                            return;
                        }
                        dteDate = new Date(year, month, day);
                        if ((month == dteDate.getMonth()) && (year == dteDate.getFullYear())) {

                        }
                        else {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextTarget + "<br />";
                            return;
                        }
                        break;

                    case 10:
                        objRegExp = /^\d{4}(\-|\/|\.)\d{2}(\-|\/|\.)\d{2}$/;    //yyyy-MM-dd

                        if (!objRegExp.test(txtDate)) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertText + "yyyy-MM-dd<br />";
                            return;
                        }

                        if (txtDate.length !== 10) {
                            return;
                        } else {
                            var ArrayDate = txtDate.split("-");

                            year = parseInt(ArrayDate[0], 10) - 0;
                            month = parseInt(ArrayDate[1], 10) - 1; // because months in JS start from 0    
                            day = parseInt(ArrayDate[2], 10) - 0;

                        }
                        if (year < 1000 || year > 3000) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextGreater + "<br />";
                            return;
                        }
                        dteDate = new Date(year, month, day);
                        if ((day == dteDate.getDate()) && (month == dteDate.getMonth()) && (year == dteDate.getFullYear())) {

                        }
                        else {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateCheck"].alertTextTarget + "<br />";
                            return;
                        }
                        break;
                }

            }

            function _intLimit(caller, rules, position) {
                srcObj = $(caller);
                symbol = rules[position + 1]; // '>'、'<'、'<='、'>='
                intervalSize = rules[position + 2]; //數值大小
                textLabel = rules[position + 3]; //標簽訊息文字

                //開頭為英文字母，後7碼
                var reg = /^[0-9\ ]+$/;
                var srcValue;

                srcValue = srcObj.attr("value");
                if ("" == srcValue) {
                    return;
                }
                if (!reg.test(srcValue)) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["intLimit"].alertText + "<br />";
                    return;
                }


                //有空值就不比對
                if (undefined == srcObj.attr('value') || "" == srcObj.attr('value')) {
                    return;
                }
                else {
                    srcValue = srcObj.attr("value");
                }
                srcValue = parseInt(srcValue, 10);

                if (isNaN(srcValue)) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["intLimit"].alertText + "<br />";
                    return;
                }

                var balanceNum = srcValue; //兩數相減
                var intervalNum = parseInt(intervalSize, 10); //間距數
                if (undefined == intervalSize || "" == intervalSize || isNaN(intervalNum)) {
                    return;
                }

                if (undefined == textLabel || null == textLabel || "custom" == textLabel) {
                    textLabel = "";
                }

                //是否符合「限制的數值大小」
                switch (symbol) {
                    case ">":
                        if (srcValue <= intervalNum) {
                            $.validationEngine.isError = true;
                            promptText += textLabel + $.validationEngine.settings.allrules["intLimit"].alertTextGreater + "[" + intervalNum + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<":
                        if (srcValue >= intervalNum) {
                            $.validationEngine.isError = true;
                            promptText += textLabel + $.validationEngine.settings.allrules["intLimit"].alertTextLess + "[" + intervalNum + "]" + "<br />";
                            return;
                        }
                        break;
                    case ">=":
                        if (srcValue < intervalNum) {
                            $.validationEngine.isError = true;
                            promptText += textLabel + $.validationEngine.settings.allrules["intLimit"].alertTextGreaterEqual + "[" + intervalNum + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<=":
                        if (srcValue > intervalNum) {
                            $.validationEngine.isError = true;
                            promptText += textLabel + $.validationEngine.settings.allrules["intLimit"].alertTextLessEqual + "[" + intervalNum + "]" + "<br />";
                            return;
                        }
                        break;
                    default:
                        return;
                }
            }

            function _seqnoDiff(caller, rules, position) {
                srcObj = $(caller);
                symbol = rules[position + 1]; // '>'、'<'、'<='、'>='
                diffObj = $("input[type=text][name=" + rules[position + 2] + "]");
                intervalSize = rules[position + 3]; //區間大小

                //開頭為英文字母，後7碼
                var reg1 = /^[A-Za-z]/g;
                var reg2 = /^[0-9]/g;
                var srcValue, diffValue;

                srcValue = srcObj.attr("value");
                if (reg1.test(srcValue) || reg2.test(srcValue)) {
                    //不做事
                }
                else {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertText + "<br />";
                    return;
                }


                //有空值就不比對
                if (undefined == srcObj.attr('value') || "" == srcObj.attr('value') ||
                    undefined == diffObj.attr('value') || "" == diffObj.attr('value')) {
                    return;
                }
                else {
                    srcValue = srcObj.attr("value");
                    diffValue = diffObj.attr("value");
                }


                if (reg1.test(srcValue)) {   //格式為 A1234567 把 字首英文換成0
                    srcValue = srcValue.replace(/[A-Za-z]/g, "0");
                    diffValue = diffValue.replace(/[A-Za-z]/g, "0");
                }
                srcValue = parseInt(srcValue, 10);
                diffValue = parseInt(diffValue, 10);

                if (isNaN(srcValue)) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertText + "<br />";
                    return;
                }
                else if (isNaN(diffValue)) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertText + "<br />";
                    return;
                }


                //比較=> 起不得大於迄
                switch (symbol) {
                    case ">":
                        if (srcValue <= diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextGreater + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<":
                        if (srcValue >= diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextLess + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    case ">=":
                        if (srcValue < diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextGreaterEqual + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<=":
                        if (srcValue > diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextLessEqual + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    default:
                        return;
                }

                var balanceNum = srcValue - diffValue; //兩數相減
                var intervalNum = parseInt(intervalSize, 10); //間距數
                if (undefined == intervalSize || "" == intervalSize || isNaN(intervalNum)) {
                    return;
                }
                //比較間距是否符合規則
                switch (symbol) {
                    case ">":
                        if (balanceNum > intervalNum) {//正號
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextGreater + "[" + diffObj.attr('value') + "] ," + $.validationEngine.settings.allrules["seqnoDiff"].alertTextMiddle + intervalNum + $.validationEngine.settings.allrules["seqnoDiff"].alertTextInterval + "<br />"; //alertTextMiddle
                            return;
                        }
                        break;
                    case "<":
                        if (balanceNum < -intervalNum) {//負號
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextLess + "[" + diffObj.attr('value') + "] ," + $.validationEngine.settings.allrules["seqnoDiff"].alertTextMiddle + intervalNum + $.validationEngine.settings.allrules["seqnoDiff"].alertTextInterval + "<br />";
                            return;
                        }
                        break;
                    case ">=":
                        if (balanceNum >= intervalNum) {//正號
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextGreaterEqual + "[" + diffObj.attr('value') + "] ," + $.validationEngine.settings.allrules["seqnoDiff"].alertTextMiddle + intervalNum + $.validationEngine.settings.allrules["seqnoDiff"].alertTextInterval + "<br />";
                            return;
                        }
                        break;
                    case "<=":
                        if (balanceNum <= -intervalNum) {//負號
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["seqnoDiff"].alertTextLessEqual + "[" + diffObj.attr('value') + "] ," + $.validationEngine.settings.allrules["seqnoDiff"].alertTextMiddle + intervalNum + $.validationEngine.settings.allrules["seqnoDiff"].alertTextInterval + "<br />";
                            return;
                        }
                        break;
                    default:
                        return;

                }


            }

            function _dateDiff(caller, rules, position) {    	  // VALIDATE LENGTH
                srcObj = $(caller);
                symbol = rules[position + 1]; // '>'、'<'、'<='、'>='
                diffObj = $("input[type=text][name=" + rules[position + 2] + "]");
                intervalSize = rules[position + 3]; //區間大小
                intervalUnit = rules[position + 4]; // 'y'年、'm'月 or 'd'日

                /***************確定日期格式是否正確************begin****************/

                _dateCheck(caller, rules, position);

                /***************確定日期格式是否正確************end****************/

                //有空值就不比對
                if (undefined == srcObj.attr('value') || "" == srcObj.attr('value') ||
                    undefined == diffObj.attr('value') || "" == diffObj.attr('value')) {
                    return;
                }



                var srcDate, diffDate, diffArray;
                var srcArray = new Array();
                if (-1 < srcObj.attr('value').indexOf("-")) {
                    srcArray = srcObj.attr('value').split("-");
                    diffArray = diffObj.attr('value').split("-");
                }
                else if (-1 < srcObj.attr('value').indexOf("/")) {
                    srcArray = srcObj.attr('value').split("/");
                    diffArray = diffObj.attr('value').split("/");
                }
                else if (srcObj.attr('value').length == 8 && -1 < diffObj.attr('value').indexOf("-")) {
                    srcArray[0] = srcObj.attr('value').substr(0, 4);
                    srcArray[1] = srcObj.attr('value').substr(4, 2);
                    srcArray[2] = srcObj.attr('value').substr(6, 2);
                    diffArray = diffObj.attr('value').split("-");
                }
                else {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["dateDiff"].alertText + "<br />";
                    return;
                }

                srcDate = new Date(parseInt(srcArray[0], 10), parseInt(srcArray[1], 10) - 1, parseInt(srcArray[2], 10), 0, 0, 0);
                diffDate = new Date(parseInt(diffArray[0], 10), parseInt(diffArray[1], 10) - 1, parseInt(diffArray[2], 10), 0, 0, 0);
                if (isNaN(srcDate)) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["dateDiff"].alertText + "<br />";
                    return;
                } else if (isNaN(diffDate)) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextTarget + "<br />";
                    return;
                }

                switch (symbol) {
                    case ">":
                        if (srcDate <= diffDate) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextGreater + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<":
                        if (srcDate >= diffDate) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextLess + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    case ">=":
                        if (srcDate < diffDate) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextGreaterEqual + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<=":
                        if (srcDate > diffDate) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextLessEqual + "[" + diffObj.attr('value') + "]" + "<br />";
                            return;
                        }
                        break;
                    default:
                        return;
                }

                var intervalNum = parseInt(intervalSize, 10);
                if (undefined == intervalUnit || "" == intervalUnit ||
                    undefined == intervalSize || "" == intervalSize ||
                    isNaN(intervalNum)) {
                    return;
                }

                var strUnit; //天、月、年
                switch (intervalUnit) {
                    case "y":
                        switch (symbol) {
                            case ">":
                            case ">=":
                                srcDate = new Date(parseInt(srcArray[0], 10) - 1, parseInt(srcArray[1], 10) - 1, parseInt(srcArray[2], 10), 0, 0, 0);
                                break;
                            case "<":
                            case "<=":
                                srcDate = new Date(parseInt(srcArray[0], 10) + 1, parseInt(srcArray[1], 10) - 1, parseInt(srcArray[2], 10), 0, 0, 0);
                                break;
                        }
                        strUnit = $.validationEngine.settings.allrules["dateDiff"].alertTextYear;
                        break;
                    case "m":
                        switch (symbol) {
                            case ">":
                            case ">=":
                                if (1 > (parseInt(srcArray[1], 10) - intervalNum)) {
                                    srcDate = new Date(parseInt(srcArray[0], 10) - 1, parseInt(srcArray[1], 10) + 11 - intervalNum, parseInt(srcArray[2], 10), 0, 0, 0);
                                }
                                else {
                                    srcDate = new Date(parseInt(srcArray[0], 10), parseInt(srcArray[1], 10) - 1 - intervalNum, parseInt(srcArray[2], 10), 0, 0, 0);
                                }
                                break;
                            case "<":
                            case "<=":
                                if (12 < (parseInt(srcArray[1], 10) + intervalNum)) {
                                    srcDate = new Date(parseInt(srcArray[0], 10) + 1, parseInt(srcArray[1], 10) - 13 + intervalNum, parseInt(srcArray[2], 10), 0, 0, 0);
                                }
                                else {
                                    srcDate = new Date(parseInt(srcArray[0], 10), parseInt(srcArray[1], 10) - 1 + intervalNum, parseInt(srcArray[2], 10), 0, 0, 0);
                                }
                                break;
                        }
                        strUnit = $.validationEngine.settings.allrules["dateDiff"].alertTextMonth;
                        break;
                    case "d":
                        switch (symbol) {
                            case ">":
                            case ">=":
                                srcDate = new Date(parseInt(srcArray[0], 10), parseInt(srcArray[1], 10) - 1, parseInt(srcArray[2], 10) - intervalNum, 0, 0, 0);
                                break;
                            case "<":
                            case "<=":
                                srcDate = new Date(parseInt(srcArray[0], 10), parseInt(srcArray[1], 10) - 1, parseInt(srcArray[2], 10) + intervalNum, 0, 0, 0);
                                break;
                        }
                        strUnit = $.validationEngine.settings.allrules["dateDiff"].alertTextDay;
                        break;
                    default:
                        return;
                }
                switch (symbol) {
                    case ">":
                        if (srcDate >= diffDate) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextGreater + "[" + diffObj.attr('value') + "] " + intervalNum + strUnit + "<br />";
                            return;
                        }
                        break;
                    case "<":
                        if (srcDate <= diffDate) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextLess + "[" + diffObj.attr('value') + "] " + intervalNum + strUnit + "<br />";
                            return;
                        }
                        break;
                    case ">=":
                        if (srcDate > diffDate) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextGreaterEqual + "[" + diffObj.attr('value') + "] " + intervalNum + strUnit + "<br />";
                            return;
                        }
                        break;
                    case "<=":
                        if (srcDate < diffDate) {
                            $.validationEngine.isError = true;
                            promptText += promptText += $.validationEngine.settings.allrules["dateDiff"].alertTextLessEqual + "[" + diffObj.attr('value') + "] " + intervalNum + strUnit + "<br />";
                            return;
                        }
                        break;
                }
            }

            function _telandacd(caller, rules, position) {    	  // VALIDATE LENGTH
                srcObj = $(caller);
                diffObj = $("input[type=text][name=" + rules[position + 1] + "]");

                if (undefined == srcObj.attr('value') || "" == srcObj.attr('value') ||
                    undefined == diffObj.attr('value') || "" == diffObj.attr('value')) {
                    return;
                }

                objRegExp02 = /[0-9]{8}/;
                objRegExpNot02 = /[0-9]{7}/;

                if (diffObj.attr("value") == "02" || (diffObj.attr("value") == "04" && (srcObj.attr("value").substring(0, 1) == "2" || srcObj.attr("value").substring(0, 1) == "3" || srcObj.attr("value").substring(0, 1) == "9"))) {
                    if (srcObj.attr("value").toString().length != 8) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["telandacd"].alertText + "<br />";
                        return;
                    } else {
                        if (!objRegExp02.test(srcObj.attr("value"))) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["telandacd"].alertText + "<br />";
                            return;
                        }
                    }
                } else {
                    if (srcObj.attr("value").toString().length != 7) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["telandacd"].alertText + "<br />";
                        return;
                    } else {
                        if (!objRegExpNot02.test(srcObj.attr("value"))) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["telandacd"].alertText + "<br />";
                            return;
                        }
                    }
                }
            }
            function _checkCreditCARD(caller, rules, position) {
                var callerValue = $(caller).attr('value').toString();
                var EvenIntegers = 0; //偶數
                var OddIntegers = 0; //奇數
                for (var i = 0; i < callerValue.length; i += 2) {
                    var EvenTemp = parseInt(callerValue.charAt(i)) * 2;
                    if (EvenTemp / 10 >= 1) {
                        EvenTemp = (EvenTemp % 10) + 1;
                    }
                    EvenIntegers += EvenTemp;

                }
                for (var j = 1; j < callerValue.length; j += 2) {
                    var OddTemp = parseInt(callerValue.charAt(j));
                    OddIntegers += OddTemp;
                }
                var Total = (EvenIntegers + OddIntegers) % 10;
                if (Total != 0) {
                    $.validationEngine.isError = true;
                    promptText += "[" + callerValue + "]" + "<br />" + $.validationEngine.settings.allrules["checkCreditCARD"].alertText + "<br />";
                    return;
                }
            }

            function _ValidateDoubleSecialChar(caller, rules) {
                var callerValue = $(caller).attr('value');
                if ((-1 != callerValue.indexOf("--")) || (-1 != callerValue.indexOf("/*")) || (-1 != callerValue.indexOf("*/"))) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules['ValidateDoubleSecialChar'].alertText + "<br />";
                }
            }

            function radioHack() {
                if ($("input[name='" + callerName + "']").size() > 1 && (callerType == "radio" || callerType == "checkbox")) {        // Hack for radio/checkbox group button, the validation go the first radio/checkbox of the group
                    caller = $("input[name='" + callerName + "'][type!=hidden]:first");
                    $.validationEngine.showTriangle = false;
                }
            }
            function _number(caller, rules) {
                if (isNaN($(caller).val())) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules['number'].alertText + "<br />";
                }
            }
            /* VALIDATION FUNCTIONS */
            function _required(caller, rules) {   // VALIDATE BLANK FIELD
                callerType = $(caller).attr("type");
                if (callerType == "text" || callerType == "password" || callerType == "textarea") {

                    if (!$(caller).val()) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules[rules[i]].alertText + "<br />";
                    }
                }
                if (callerType == "radio" || callerType == "checkbox") {
                    callerName = $(caller).attr("name");

                    if ($("input[name='" + callerName + "']:checked").size() == 0) {
                        $.validationEngine.isError = true;
                        if ($("input[name='" + callerName + "']").size() == 1) {
                            promptText += $.validationEngine.settings.allrules[rules[i]].alertTextCheckboxe + "<br />";
                        } else {
                            promptText += $.validationEngine.settings.allrules[rules[i]].alertTextCheckboxMultiple + "<br />";
                        }
                    }
                }
                if (callerType == "select-one") { // <NAME_EMAIL> for select boxes, Thank you		
                    if (!$(caller).val()) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules[rules[i]].alertText + "<br />";
                    }
                }
                if (callerType == "select-multiple") { // <NAME_EMAIL> for select boxes, Thank you	
                    if (!$(caller).find("option:selected").val()) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules[rules[i]].alertText + "<br />";
                    }
                }
            }
            function _customRegex(caller, rules, position) {		 // VALIDATE REGEX RULES
                customRule = rules[position + 1];
                pattern = eval($.validationEngine.settings.allrules[customRule].regex);

                if (undefined == $(caller).attr('value') ||
                    "" == $(caller).attr('value')) {
                    return;
                }

                if (!pattern.test($(caller).attr('value'))) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules[customRule].alertText + "<br />";
                }
            }
            function _exemptString(caller, rules, position) {		 // VALIDATE REGEX RULES
                customString = rules[position + 1];
                if (customString == $(caller).attr('value')) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules['required'].alertText + "<br />";
                }
            }

            function _funcCall(caller, rules, position) {  		// VALIDATE CUSTOM FUNCTIONS OUTSIDE OF THE ENGINE SCOPE
                customRule = rules[position + 1];
                funce = $.validationEngine.settings.allrules[customRule].nname;

                var fn = window[funce];
                if (typeof (fn) === 'function') {
                    var fn_result = fn();
                    if (!fn_result) {
                        $.validationEngine.isError = true;
                    }

                    promptText += $.validationEngine.settings.allrules[customRule].alertText + "<br />";
                }
            }
            function _ajax(caller, rules, position) {				 // VALIDATE AJAX RULES

                customAjaxRule = rules[position + 1];
                postfile = $.validationEngine.settings.allrules[customAjaxRule].file;
                fieldValue = $(caller).val();
                ajaxCaller = caller;
                fieldId = $(caller).attr("id");
                ajaxValidate = true;
                ajaxisError = $.validationEngine.isError;

                if ($.validationEngine.settings.allrules[customAjaxRule].extraData) {
                    extraData = $.validationEngine.settings.allrules[customAjaxRule].extraData;
                } else {
                    extraData = "";
                }
                /* AJAX VALIDATION HAS ITS OWN UPDATE AND BUILD UNLIKE OTHER RULES */
                if (!ajaxisError) {
                    $.ajax({
                        type: "POST",
                        url: postfile,
                        async: true,
                        data: "validateValue=" + fieldValue + "&validateId=" + fieldId + "&validateError=" + customAjaxRule + "&extraData=" + extraData,
                        beforeSend: function () {		// BUILD A LOADING PROMPT IF LOAD TEXT EXIST		   			
                            if ($.validationEngine.settings.allrules[customAjaxRule].alertTextLoad) {

                                if (!$("div." + fieldId + "formError")[0]) {
                                    return $.validationEngine.buildPrompt(ajaxCaller, $.validationEngine.settings.allrules[customAjaxRule].alertTextLoad, "load");
                                } else {
                                    $.validationEngine.updatePromptText(ajaxCaller, $.validationEngine.settings.allrules[customAjaxRule].alertTextLoad, "load");
                                }
                            }
                        },
                        error: function (data, transport) { $.validationEngine.debug("error in the ajax: " + data.status + " " + transport) },
                        success: function (data) {					// GET SUCCESS DATA RETURN JSON
                            data = eval("(" + data + ")"); 			// GET JSON DATA FROM PHP AND PARSE IT
                            ajaxisError = data.jsonValidateReturn[2];
                            customAjaxRule = data.jsonValidateReturn[1];
                            ajaxCaller = $("#" + data.jsonValidateReturn[0])[0];
                            fieldId = ajaxCaller;
                            ajaxErrorLength = $.validationEngine.ajaxValidArray.length;
                            existInarray = false;

                            if (ajaxisError == "false") {			// DATA FALSE UPDATE PROMPT WITH ERROR;

                                _checkInArray(false)				// Check if ajax validation alreay used on this field

                                if (!existInarray) {		 			// Add ajax error to stop submit		 		
                                    $.validationEngine.ajaxValidArray[ajaxErrorLength] = new Array(2);
                                    $.validationEngine.ajaxValidArray[ajaxErrorLength][0] = fieldId;
                                    $.validationEngine.ajaxValidArray[ajaxErrorLength][1] = false;
                                    existInarray = false;
                                }

                                $.validationEngine.ajaxValid = false;
                                promptText += $.validationEngine.settings.allrules[customAjaxRule].alertText + "<br />";
                                $.validationEngine.updatePromptText(ajaxCaller, promptText, "", true);
                            } else {
                                _checkInArray(true);
                                $.validationEngine.ajaxValid = true;
                                if (!customAjaxRule) { $.validationEngine.debug("wrong ajax response, are you on a server or in xampp? if not delete de ajax[ajaxUser] validating rule from your form ") }
                                if ($.validationEngine.settings.allrules[customAjaxRule].alertTextOk) {	// NO OK TEXT MEAN CLOSE PROMPT	 			
                                    $.validationEngine.updatePromptText(ajaxCaller, $.validationEngine.settings.allrules[customAjaxRule].alertTextOk, "pass", true);
                                } else {
                                    ajaxValidate = false;
                                    $.validationEngine.closePrompt(ajaxCaller);
                                }
                            }
                            function _checkInArray(validate) {
                                for (x = 0; x < ajaxErrorLength; x++) {
                                    if ($.validationEngine.ajaxValidArray[x][0] == fieldId) {
                                        $.validationEngine.ajaxValidArray[x][1] = validate;
                                        existInarray = true;

                                    }
                                }
                            }
                        }
                    });
                }
            }
            function _confirm(caller, rules, position) {		 // VALIDATE FIELD MATCH
                confirmField = rules[position + 1];

                if ($(caller).attr('value') != $("#" + confirmField).attr('value')) {
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["confirm"].alertText + "<br />";
                }
            }
            function _length(caller, rules, position) {    	  // VALIDATE LENGTH
                startLength = eval(rules[position + 1]);
                endLength = eval(rules[position + 2]);
                feildLength = $(caller).attr('value').length;

                //length[-1,*] 最多*個字元 
                if (-1 == startLength) {
                    if (feildLength > endLength) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["length"].alertText7 + endLength + $.validationEngine.settings.allrules["length"].alertText5 + "<br />";
                    }
                }
                //length[*,-1] 最少*個字元
                else if (-1 == endLength) {
                    if (feildLength < startLength) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["length"].alertText6 + startLength + $.validationEngine.settings.allrules["length"].alertText5 + "<br />";
                    }
                }
                else if (feildLength < startLength || feildLength > endLength) {
                    //length[*,**] 字元長度介於*至**之間
                    if (startLength != endLength) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["length"].alertText + startLength + $.validationEngine.settings.allrules["length"].alertText2 + endLength + $.validationEngine.settings.allrules["length"].alertText3 + "<br />";
                    }
                    //length[*,*] 限定*個字元
                    else if (0 != feildLength) {
                        $.validationEngine.isError = true;
                        promptText += $.validationEngine.settings.allrules["length"].alertText4 + startLength + $.validationEngine.settings.allrules["length"].alertText5 + "<br />";
                    }
                }
            }
            function _maxCheckbox(caller, rules, position) {  	  // VALIDATE CHECKBOX NUMBER

                nbCheck = eval(rules[position + 1]);
                groupname = $(caller).attr("name");
                groupSize = $("input[name='" + groupname + "']:checked").size();
                if (groupSize > nbCheck) {
                    $.validationEngine.showTriangle = false;
                    $.validationEngine.isError = true;
                    promptText += $.validationEngine.settings.allrules["maxCheckbox"].alertText + "<br />";
                }
            }
            function _minCheckbox(caller, rules, position) {  	  // VALIDATE CHECKBOX NUMBER

                nbCheck = eval(rules[position + 1]);
                groupname = $(caller).attr("name");
                groupSize = $("input[name='" + groupname + "']:checked").size();
                if (groupSize < nbCheck) {

                    $.validationEngine.isError = true;
                    $.validationEngine.showTriangle = false;
                    promptText += $.validationEngine.settings.allrules["minCheckbox"].alertText + " " + nbCheck + " " + $.validationEngine.settings.allrules["minCheckbox"].alertText2 + "<br />";
                }
            }
            function _stringDiff(caller, rules, position) {
                srcValue = $(caller).attr('value');
                symbol = rules[position + 1]; // '>'、'<'、'<='、'>='
                diffValue = $("input[type=text][name=" + rules[position + 2] + "]").attr('value');

                //有空值就不比對
                if (undefined == srcValue || "" == srcValue ||
                    undefined == diffValue || "" == diffValue) {
                    return;
                }

                switch (symbol) {
                    case ">":
                        if (srcValue <= diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["stringDiff"].alertTextGreater + "[" + diffValue + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<":
                        if (srcValue >= diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["stringDiff"].alertTextLess + "[" + diffValue + "]" + "<br />";
                            return;
                        }
                        break;
                    case ">=":
                        if (srcValue < diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["stringDiff"].alertTextGreaterEqual + "[" + diffValue + "]" + "<br />";
                            return;
                        }
                        break;
                    case "<=":
                        if (srcValue > diffValue) {
                            $.validationEngine.isError = true;
                            promptText += $.validationEngine.settings.allrules["stringDiff"].alertTextLessEqual + "[" + diffValue + "]" + "<br />";
                            return;
                        }
                        break;
                    default:
                        return;
                }
            }
            return ($.validationEngine.isError) ? $.validationEngine.isError : false;
        },
        submitForm: function (caller) {
            if ($.validationEngine.settings.ajaxSubmit) {
                if ($.validationEngine.settings.ajaxSubmitExtraData) {
                    extraData = $.validationEngine.settings.ajaxSubmitExtraData;
                } else {
                    extraData = "";
                }
                $.ajax({
                    type: "POST",
                    url: $.validationEngine.settings.ajaxSubmitFile,
                    async: true,
                    data: $(caller).serialize() + "&" + extraData,
                    error: function (data, transport) { $.validationEngine.debug("error in the ajax: " + data.status + " " + transport) },
                    success: function (data) {
                        if (data == "true") {			// EVERYTING IS FINE, SHOW SUCCESS MESSAGE
                            $(caller).css("opacity", 1)
                            $(caller).animate({ opacity: 0, height: 0 }, function () {
                                $(caller).css("display", "none");
                                $(caller).before("<div class='ajaxSubmit'>" + $.validationEngine.settings.ajaxSubmitMessage + "</div>");
                                $.validationEngine.closePrompt(".formError", true);
                                $(".ajaxSubmit").show("slow");
                                if ($.validationEngine.settings.success) {	// AJAX SUCCESS, STOP THE LOCATION UPDATE
                                    $.validationEngine.settings.success && $.validationEngine.settings.success();
                                    return false;
                                }
                            })
                        } else {						// HOUSTON WE GOT A PROBLEM (SOMETING IS NOT VALIDATING)
                            data = eval("(" + data + ")");
                            if (!data.jsonValidateReturn) {
                                $.validationEngine.debug("you are not going into the success fonction and jsonValidateReturn return nothing");
                            }
                            errorNumber = data.jsonValidateReturn.length
                            for (index = 0; index < errorNumber; index++) {
                                fieldId = data.jsonValidateReturn[index][0];
                                promptError = data.jsonValidateReturn[index][1];
                                type = data.jsonValidateReturn[index][2];
                                $.validationEngine.buildPrompt(fieldId, promptError, type);
                            }
                        }
                    }
                })
                return true;
            }
            // LOOK FOR BEFORE SUCCESS METHOD		
            if (!$.validationEngine.settings.beforeSuccess()) {
                if ($.validationEngine.settings.success) {	// AJAX SUCCESS, STOP THE LOCATION UPDATE
                    if ($.validationEngine.settings.unbindEngine) { $(caller).unbind("submit") }
                    $.validationEngine.settings.success && $.validationEngine.settings.success();
                    return true;
                }
            } else {
                return true;
            }
            return false;
        },
        buildPrompt: function (caller, promptText, type, ajaxed) {			// ERROR PROMPT CREATION AND DISPLAY WHEN AN ERROR OCCUR
            if (!$.validationEngine.settings) {
                $.validationEngine.defaultSetting()
            }
            deleteItself = "." + $(caller).attr("id") + "formError"

            if ($(deleteItself)[0]) {
                $(deleteItself).stop();
                $(deleteItself).remove();
            }
            var divFormError = document.createElement('div');
            var formErrorContent = document.createElement('div');
            linkTofield = $.validationEngine.linkTofield(caller)
            $(divFormError).addClass("formError")
            if (type == "tip") $(divFormError).addClass("TipWidthPopup");
            if (type == "pass") $(divFormError).addClass("greenPopup")
            if (type == "load") $(divFormError).addClass("blackPopup")
            if (ajaxed) $(divFormError).addClass("ajaxed")

            $(divFormError).addClass(linkTofield);
            $(formErrorContent).addClass("formErrorContent");

            if ($.validationEngine.settings.containerOverflow) {		// Is the form contained in an overflown container?
                $(caller).before(divFormError);
            } else {
                $("body").append(divFormError);
            }

            $(divFormError).append(formErrorContent);

            if ($.validationEngine.showTriangle != false) {		// NO TRIANGLE ON MAX CHECKBOX AND RADIO
                var arrow = document.createElement('div');
                $(arrow).addClass("formErrorArrow");
                $(divFormError).append(arrow);
                if ($.validationEngine.settings.promptPosition == "bottomLeft" || $.validationEngine.settings.promptPosition == "bottomRight") {
                    $(arrow).addClass("formErrorArrowBottom")
                    $(arrow).html('<div class="line1"><!-- --></div><div class="line2"><!-- --></div><div class="line3"><!-- --></div><div class="line4"><!-- --></div><div class="line5"><!-- --></div><div class="line6"><!-- --></div><div class="line7"><!-- --></div><div class="line8"><!-- --></div><div class="line9"><!-- --></div><div class="line10"><!-- --></div>');
                }
                if ($.validationEngine.settings.promptPosition == "topLeft" || $.validationEngine.settings.promptPosition == "topRight") {
                    $(divFormError).append(arrow);
                    $(arrow).html('<div class="line10"><!-- --></div><div class="line9"><!-- --></div><div class="line8"><!-- --></div><div class="line7"><!-- --></div><div class="line6"><!-- --></div><div class="line5"><!-- --></div><div class="line4"><!-- --></div><div class="line3"><!-- --></div><div class="line2"><!-- --></div><div class="line1"><!-- --></div>');
                }
            }
            $(formErrorContent).html(promptText)

            var calculatedPosition = $.validationEngine.calculatePosition(caller, promptText, type, ajaxed, divFormError)

            calculatedPosition.callerTopPosition += "px";
            calculatedPosition.callerleftPosition += "px";
            calculatedPosition.marginTopSize += "px"
            $(divFormError).css({
                "top": calculatedPosition.callerTopPosition,
                "left": calculatedPosition.callerleftPosition,
                "marginTop": calculatedPosition.marginTopSize,
                "opacity": 0
            })
            return $(divFormError).animate({ "opacity": 0.87 }, function () { return true; });
        },
        buildPromptFormat: function (caller, promptText, type, ajaxed, contWidth) {			// ERROR PROMPT CREATION AND DISPLAY WHEN AN ERROR OCCUR
            if (!$.validationEngine.settings) {
                $.validationEngine.defaultSetting()
            }
            deleteItself = "." + $(caller).attr("id") + "formError"

            if ($(deleteItself)[0]) {
                $(deleteItself).stop();
                $(deleteItself).remove();
            }
            var divFormError = document.createElement('div');
            var formErrorContent = document.createElement('div');
            linkTofield = $.validationEngine.linkTofield(caller)
            $(divFormError).addClass("formError")
            if (type == "tip") $(divFormError).addClass("TipWidthPopup");
            if (type == "pass") $(divFormError).addClass("greenPopup")
            if (type == "load") $(divFormError).addClass("blackPopup")
            if (ajaxed) $(divFormError).addClass("ajaxed")

            $(formErrorContent).addClass("formErrorContent");

            if ($.validationEngine.settings.containerOverflow) {		// Is the form contained in an overflown container?
                $(caller).before(divFormError);
            } else {
                $("body").append(divFormError);
            }

            $(divFormError).append(formErrorContent);

            if ($.validationEngine.showTriangle != false) {		// NO TRIANGLE ON MAX CHECKBOX AND RADIO
                var arrow = document.createElement('div');
                $(arrow).addClass("formErrorArrow");
                $(divFormError).append(arrow);
                if ($.validationEngine.settings.promptPosition == "bottomLeft" || $.validationEngine.settings.promptPosition == "bottomRight") {
                    $(arrow).addClass("formErrorArrowBottom")
                    $(arrow).html('<div class="line1"><!-- --></div><div class="line2"><!-- --></div><div class="line3"><!-- --></div><div class="line4"><!-- --></div><div class="line5"><!-- --></div><div class="line6"><!-- --></div><div class="line7"><!-- --></div><div class="line8"><!-- --></div><div class="line9"><!-- --></div><div class="line10"><!-- --></div>');
                }
                if ($.validationEngine.settings.promptPosition == "topLeft" || $.validationEngine.settings.promptPosition == "topRight") {
                    $(divFormError).append(arrow);
                    $(arrow).html('<div class="line10"><!-- --></div><div class="line9"><!-- --></div><div class="line8"><!-- --></div><div class="line7"><!-- --></div><div class="line6"><!-- --></div><div class="line5"><!-- --></div><div class="line4"><!-- --></div><div class="line3"><!-- --></div><div class="line2"><!-- --></div><div class="line1"><!-- --></div>');
                }
            }

            $(formErrorContent).html(promptText);

            $(formErrorContent).css('width', contWidth);

            var calculatedPosition = $.validationEngine.calculatePosition(caller, promptText, type, ajaxed, divFormError)

            calculatedPosition.callerTopPosition += "px";
            calculatedPosition.callerleftPosition += "px";
            calculatedPosition.marginTopSize += "px"
            $(divFormError).css({
                "top": calculatedPosition.callerTopPosition,
                "left": calculatedPosition.callerleftPosition,
                "marginTop": calculatedPosition.marginTopSize,
                "opacity": 0
            })
            return $(divFormError).animate({ "opacity": 0.87 }, function () { return true; });
        },
        updatePromptText: function (caller, promptText, type, ajaxed) {	// UPDATE TEXT ERROR IF AN ERROR IS ALREADY DISPLAYED

            linkTofield = $.validationEngine.linkTofield(caller);
            var updateThisPrompt = "." + linkTofield;

            if (type == "pass") { $(updateThisPrompt).addClass("greenPopup") } else { $(updateThisPrompt).removeClass("greenPopup") };
            if (type == "load") { $(updateThisPrompt).addClass("blackPopup") } else { $(updateThisPrompt).removeClass("blackPopup") };
            if (ajaxed) { $(updateThisPrompt).addClass("ajaxed") } else { $(updateThisPrompt).removeClass("ajaxed") };

            $(updateThisPrompt).find(".formErrorContent").html(promptText);

            var calculatedPosition = $.validationEngine.calculatePosition(caller, promptText, type, ajaxed, updateThisPrompt)

            calculatedPosition.callerTopPosition += "px";
            calculatedPosition.callerleftPosition += "px";
            calculatedPosition.marginTopSize += "px"
            $(updateThisPrompt).animate({ "top": calculatedPosition.callerTopPosition, "marginTop": calculatedPosition.marginTopSize });
        },
        calculatePosition: function (caller, promptText, type, ajaxed, divFormError) {

            if ($.validationEngine.settings.containerOverflow) {		// Is the form contained in an overflown container?
                callerTopPosition = 0;
                callerleftPosition = 0;
                callerWidth = $(caller).width();
                inputHeight = $(divFormError).height(); 				// compasation for the triangle
                var marginTopSize = "-" + inputHeight;
            } else {
                callerTopPosition = $(caller).offset().top;
                callerleftPosition = $(caller).offset().left;
                callerWidth = $(caller).width();
                inputHeight = $(divFormError).height();
                var marginTopSize = 0;
            }

            /* POSITIONNING */
            if ($.validationEngine.settings.promptPosition == "topRight") {
                if ($.validationEngine.settings.containerOverflow) {		// Is the form contained in an overflown container?
                    callerleftPosition += callerWidth - 30;
                } else {
                    callerleftPosition += callerWidth - 30;
                    callerTopPosition += -inputHeight;
                }
            }
            if ($.validationEngine.settings.promptPosition == "topLeft") { callerTopPosition += -inputHeight - 10; }

            if ($.validationEngine.settings.promptPosition == "centerRight") { callerleftPosition += callerWidth + 13; }

            if ($.validationEngine.settings.promptPosition == "bottomLeft") {
                callerHeight = $(caller).height();
                callerTopPosition = callerTopPosition + callerHeight + 15;
            }
            if ($.validationEngine.settings.promptPosition == "bottomRight") {
                callerHeight = $(caller).height();
                callerleftPosition += callerWidth - 30;
                callerTopPosition += callerHeight + 5;
            }
            return {
                "callerTopPosition": callerTopPosition,
                "callerleftPosition": callerleftPosition,
                "marginTopSize": marginTopSize
            }
        },
        linkTofield: function (caller) {
            var linkTofield = $(caller).attr("id") + "formError";
            linkTofield = linkTofield.replace(/\[/g, "");
            linkTofield = linkTofield.replace(/\]/g, "");
            return linkTofield;
        },
        closePrompt: function (caller, outside) {						// CLOSE PROMPT WHEN ERROR CORRECTED
            if (!$.validationEngine.settings) {
                $.validationEngine.defaultSetting()
            }
            if (outside) {
                $(caller).fadeTo("fast", 0, function () {
                    $(caller).remove();
                });
                return false;
            }
            if (typeof (ajaxValidate) == 'undefined') { ajaxValidate = false }
            if (!ajaxValidate) {
                linkTofield = $.validationEngine.linkTofield(caller);
                closingPrompt = "." + linkTofield;
                $(closingPrompt).fadeTo("fast", 0, function () {
                    $(closingPrompt).remove();
                });
            }
        },
        debug: function (error) {
            if (!$("#debugMode")[0]) {
                $("body").append("<div id='debugMode'><div class='debugError'><strong>This is a debug mode, you got a problem with your form, it will try to help you, refresh when you think you nailed down the problem</strong></div></div>");
            }
            $(".debugError").append("<div class='debugerror'>" + error + "</div>");
        },
        submitValidation: function (caller) {					// FORM SUBMIT VALIDATION LOOPING INLINE VALIDATION
            var stopForm = false;
            $.validationEngine.ajaxValid = true;
            var toValidateSize = $(caller).find("[class*=validate]").size();

            $(caller).find("[class*=validate]").each(function () {
                linkTofield = $.validationEngine.linkTofield(this);

                if (!$("." + linkTofield).hasClass("ajaxed")) {	// DO NOT UPDATE ALREADY AJAXED FIELDS (only happen if no normal errors, don't worry)
                    var validationPass = $.validationEngine.loadValidation(this);
                    return (validationPass) ? stopForm = true : "";
                };
            });
            ajaxErrorLength = $.validationEngine.ajaxValidArray.length; 	// LOOK IF SOME AJAX IS NOT VALIDATE
            for (x = 0; x < ajaxErrorLength; x++) {
                if ($.validationEngine.ajaxValidArray[x][1] == false) $.validationEngine.ajaxValid = false;
            }
            if (stopForm || !$.validationEngine.ajaxValid) {		// GET IF THERE IS AN ERROR OR NOT FROM THIS VALIDATION FUNCTIONS
                if ($.validationEngine.settings.scroll) {
                    if (!$.validationEngine.settings.containerOverflow) {
                        var destination = $(".formError:not('.greenPopup'):first").offset().top;
                        $(".formError:not('.greenPopup')").each(function () {
                            testDestination = $(this).offset().top;
                            if (destination > testDestination) destination = $(this).offset().top;
                        })
                        $("html:not(:animated),body:not(:animated)").animate({ scrollTop: destination }, 1100);
                    } else {
                        var destination = $(".formError:not('.greenPopup'):first").offset().top;
                        var scrollContainerScroll = $($.validationEngine.settings.containerOverflowDOM).scrollTop();
                        var scrollContainerPos = -parseInt($($.validationEngine.settings.containerOverflowDOM).offset().top);
                        var destination = scrollContainerScroll + destination + scrollContainerPos - 5
                        var scrollContainer = $.validationEngine.settings.containerOverflowDOM + ":not(:animated)"

                        $(scrollContainer).animate({ scrollTop: destination }, 1100);
                    }
                }
                return true;
            } else {
                return false;
            }
        }
    }
})(jQuery);
