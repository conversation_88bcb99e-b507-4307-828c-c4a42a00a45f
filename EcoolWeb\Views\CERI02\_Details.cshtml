﻿@model AccreditationManageEditDetailsViewModel

@using (Html.BeginCollectionItem("ContentData", Model?.IsCopy ?? false))
{
    var Index = Html.GetIndex("ContentData");
    var checkBoxsIsTextstr = false;
    var SelectGradeSemesterItems = HRMT01.GetSelectGradeSemesterItems(Model?.GRADE_SEMESTERs);

    <li class="list-group-item clearfix" id="Tr@(Index)">
        <a class="btn btn-xs btn-Basic" role="button" onclick="deleteRow('Tr@(Index)','@Model.ITEM_NO')">
            <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
        </a>

        <div class="row">
            <div class="col-sm-2">
                通過主旨:
            </div>
            <div class="col-sm-10">
                @Html.HiddenFor(m => m.ITEM_NO)
                @{
                    var CONTENTplaceholderstr = "通過條件(提示文字)..";
                    var placeholderstr = "通過主旨";

                    }
                @if (Model.IsText == "true")
                {
                    placeholderstr = "通過其它專業檢定或比賽";
                    CONTENTplaceholderstr = "必須要專業檢定或比賽的證明.";
                }
                else {

                    placeholderstr = "通過主旨";
                    CONTENTplaceholderstr = "通過條件(提示文字)..";
                }
                @Html.EditorFor(m => m.SUBJECT, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = placeholderstr, maxlength = "15" } })
                @Html.ValidationMessageFor(m => m.SUBJECT, "", new { @class = "text-danger" })
            </div>
            </div>
        <div class="row">
            <div class="col-sm-2">
                通過條件:
            </div>
            <div class="col-sm-10">
          
                @Html.TextAreaFor(m => m.CONTENT, new { placeholder = CONTENTplaceholderstr, @class = "form-control form-control-required", maxlength = "50" })
                @Html.ValidationMessageFor(m => m.CONTENT, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="row">
            <div class="col-sm-2">
                設定認證<br />時間:
            </div>
            <div class="col-sm-10">
                
                <select name="@Html.NameFor(m=>m.GRADE_SEMESTERs)" id="@Html.IdFor(m=>m.GRADE_SEMESTERs)" class="selectpicker form-control " multiple title="設定認證時間" data-actions-box="true" data-width="97%">
                    @foreach (var item in SelectGradeSemesterItems)
                    {
                        <option title="@item.Text.Replace("年級","").Replace("學期","")" value="@item.Value" @(item.Selected ? "selected" : "")> @item.Text</option>
                    }
                </select>
                @Html.ValidationMessageFor(m => m.GRADE_SEMESTERs, "", new { @class = "text-danger" })
            </div>
        </div>
</li>
    <script type="text/javascript">
        $(function() {
            $('#@Html.IdFor(m=>m.GRADE_SEMESTERs)').selectpicker('refresh');
            $('#@Html.IdFor(m=>m.GRADE_SEMESTERs)').selectpicker('setStyle', 'form-control-required', 'add');
    
            //checkBoxsIsTextstr = $("#checkBoxsIsText").prop("checked");
            //if (checkBoxsIsTextstr == true) {
            //    $(".bs-select-all").click()
            //    $(".dropdown-toggle").attr("disabled", "disabled");
            //}
            //else {
            //    $(".dropdown-toggle").removeAttr("disabled");

            //}
		});
    </script>
}