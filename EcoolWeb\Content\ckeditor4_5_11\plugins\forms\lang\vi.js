﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'vi', {
	button: {
		title: 'Thuộc tính của nút',
		text: 'Chuỗi hiển thị (gi<PERSON> trị)',
		type: 'Kiểu',
		typeBtn: 'Nút bấm',
		typeSbm: 'Nút gửi',
		typeRst: 'Nút nhập lại'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Thuộc tính nút kiểm',
		radioTitle: 'Thuộc tính nút chọn',
		value: 'Gi<PERSON> trị',
		selected: '<PERSON><PERSON>ợc chọn',
		required: 'Bắt buộc'
	},
	form: {
		title: 'Thuộc tính biểu mẫu',
		menu: 'Thuộc tính biểu mẫu',
		action: 'Hành động',
		method: 'Phương thức',
		encoding: '<PERSON>ảng mã'
	},
	hidden: {
		title: '<PERSON><PERSON><PERSON><PERSON> t<PERSON>h trường ẩn',
		name: '<PERSON><PERSON><PERSON>',
		value: '<PERSON><PERSON><PERSON> trị'
	},
	select: {
		title: '<PERSON><PERSON><PERSON><PERSON> tính ô chọn',
		selectInfo: 'Thông tin',
		opAvail: 'Các tùy chọn có thể sử dụng',
		value: 'Giá trị',
		size: 'Kích cỡ',
		lines: 'dòng',
		chkMulti: 'Cho phép chọn nhiều',
		required: 'Bắt buộc',
		opText: 'Văn bản',
		opValue: 'Giá trị',
		btnAdd: 'Thêm',
		btnModify: 'Thay đổi',
		btnUp: 'Lên',
		btnDown: 'Xuống',
		btnSetValue: 'Giá trị được chọn',
		btnDelete: 'Nút xoá'
	},
	textarea: {
		title: 'Thuộc tính vùng văn bản',
		cols: 'Số cột',
		rows: 'Số hàng'
	},
	textfield: {
		title: 'Thuộc tính trường văn bản',
		name: 'Tên',
		value: 'Giá trị',
		charWidth: 'Độ rộng của ký tự',
		maxChars: 'Số ký tự tối đa',
		required: 'Bắt buộc',
		type: 'Kiểu',
		typeText: 'Ký tự',
		typePass: 'Mật khẩu',
		typeEmail: 'Email',
		typeSearch: 'Tìm kiếm',
		typeTel: 'Số điện thoại',
		typeUrl: 'URL'
	}
} );
