/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Arrows/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Arrows={directory:"Arrows/Regular",family:"STIXMathJax_Arrows",testString:"\u00A0\u219C\u219D\u219F\u21A1\u21A4\u21A5\u21A7\u21A8\u21AF\u21B2\u21B3\u21B4\u21B5\u21B8",32:[0,0,250,0,0],160:[0,0,250,0,0],8604:[411,-102,926,70,856],8605:[411,-102,926,70,856],8607:[662,154,511,60,451],8609:[662,154,511,60,451],8612:[450,-57,926,70,857],8613:[662,154,511,60,451],8615:[662,154,511,59,451],8616:[662,154,511,59,451],8623:[662,154,511,60,451],8626:[662,154,463,25,419],8627:[662,154,463,39,433],8628:[662,154,926,70,856],8629:[662,156,926,70,856],8632:[732,156,926,55,872],8633:[598,92,926,60,866],8645:[662,156,773,31,742],8662:[662,156,926,55,874],8663:[662,156,926,55,874],8664:[662,156,926,55,874],8665:[662,156,926,55,874],8668:[449,-58,926,60,866],8670:[662,156,511,60,451],8671:[662,156,511,60,451],8673:[662,156,511,60,451],8675:[662,156,511,60,451],8676:[450,-58,926,60,866],8677:[450,-58,926,60,866],8678:[551,45,926,60,866],8679:[662,156,685,45,641],8680:[551,45,926,60,866],8681:[662,156,685,45,641],8682:[690,184,685,45,641],8692:[448,-57,926,70,856],8693:[662,156,773,31,742],8694:[739,232,926,60,866],8695:[450,-58,926,60,866],8696:[450,-58,926,55,861],8697:[450,-58,926,48,878],8698:[450,-58,926,60,866],8699:[450,-58,926,60,866],8700:[450,-58,926,38,888],8701:[449,-57,926,60,866],8702:[449,-57,926,60,866],8703:[449,-57,926,20,906],10224:[662,156,1033,69,965],10225:[662,156,1033,69,965],10226:[626,116,974,54,882],10227:[626,116,974,92,920],10228:[569,61,1200,52,1147],10235:[450,-57,1574,55,1519],10237:[551,45,1574,55,1519],10238:[551,45,1574,55,1519],10239:[449,-58,1574,55,1519],10496:[450,-57,926,56,871],10497:[450,-57,926,55,871],10498:[551,45,926,55,871],10499:[551,45,926,55,871],10500:[551,45,926,20,906],10501:[450,-57,926,55,871],10502:[551,45,926,55,871],10503:[551,45,926,55,871],10504:[662,156,511,59,452],10505:[662,156,511,59,452],10506:[662,156,926,71,854],10507:[662,156,926,72,855],10508:[449,-57,926,55,871],10509:[449,-57,926,55,871],10510:[449,-57,926,55,871],10511:[449,-57,926,55,871],10512:[449,-57,1412,55,1357],10513:[449,-57,926,55,873],10514:[662,156,511,59,452],10515:[662,156,511,59,452],10516:[450,-57,926,55,871],10517:[450,-57,926,55,871],10518:[449,-57,926,55,871],10519:[450,-57,926,55,871],10520:[450,-57,926,50,876],10521:[449,-57,926,55,871],10522:[449,-57,926,55,871],10523:[449,-57,926,55,871],10524:[449,-57,926,55,871],10525:[449,-57,926,55,871],10526:[449,-57,926,55,871],10527:[450,-57,926,55,871],10528:[450,-57,926,55,871],10529:[662,156,926,55,871],10530:[660,156,926,55,873],10531:[662,156,926,55,871],10532:[662,156,926,55,871],10533:[662,156,926,55,871],10534:[662,156,926,55,871],10535:[662,156,926,55,873],10536:[662,156,926,53,871],10537:[662,156,926,53,871],10538:[662,156,926,55,873],10539:[662,156,926,55,871],10540:[662,156,926,55,871],10541:[662,156,926,55,871],10542:[662,156,926,55,871],10543:[662,156,926,55,871],10544:[662,154,926,55,873],10545:[662,156,926,54,870],10546:[662,156,926,55,871],10547:[449,-57,926,55,871],10548:[562,0,926,141,797],10549:[562,0,926,141,797],10550:[493,163,784,87,649],10551:[493,163,784,135,697],10552:[657,153,511,70,415],10553:[657,153,511,96,441],10554:[423,-78,926,69,866],10555:[423,-78,926,60,857],10556:[423,-64,926,59,856],10557:[423,29,926,69,866],10558:[563,116,926,69,856],10559:[563,116,926,69,856],10560:[788,116,926,92,834],10561:[788,116,926,92,834],10562:[598,92,926,55,871],10563:[598,92,926,55,871],10564:[598,92,926,55,871],10565:[449,69,926,55,871],10566:[449,69,926,55,871],10567:[449,-57,926,55,871],10568:[449,-57,926,38,888],10569:[662,154,511,60,451],10570:[439,-67,926,38,888],10571:[439,-67,926,38,888],10572:[662,156,511,69,441],10573:[662,156,511,69,441],10574:[439,-220,926,38,888],10575:[662,156,511,222,441],10576:[286,-67,926,38,888],10577:[662,156,511,69,288],10578:[448,-58,926,55,871],10579:[448,-58,926,55,871],10580:[662,156,511,60,451],10581:[662,156,511,60,451],10582:[448,-58,926,55,871],10583:[448,-58,926,55,871],10584:[662,156,511,60,451],10585:[662,156,511,60,451],10586:[448,-58,926,55,871],10587:[448,-58,926,55,871],10588:[662,156,511,60,451],10589:[662,156,511,60,451],10590:[448,-58,926,55,871],10591:[448,-58,926,55,871],10592:[662,156,511,59,450],10593:[662,156,511,59,450],10594:[539,33,926,55,871],10595:[662,156,685,57,629],10596:[539,33,926,55,871],10597:[662,156,685,57,629],10598:[539,-120,926,55,871],10599:[386,33,926,55,871],10600:[539,-120,926,55,871],10601:[386,33,926,55,871],10602:[539,-120,926,55,871],10603:[386,33,926,55,871],10604:[539,-120,926,55,871],10605:[386,33,926,55,871],10606:[662,156,685,57,629],10607:[662,156,685,57,629],10608:[386,-120,926,55,871],10609:[565,-57,926,55,871],10610:[508,-57,926,55,871],10611:[449,2,926,55,871],10612:[449,2,926,55,871],10613:[449,141,926,55,871],10614:[607,283,685,64,621],10615:[532,26,926,45,871],10616:[608,282,685,64,621],10617:[627,262,685,64,621],10618:[532,26,926,45,871],10619:[627,262,685,63,620],10620:[511,5,926,135,791],10621:[511,5,926,135,791],10622:[581,75,685,84,600],10623:[581,75,685,84,600],57524:[556,-220,313,55,258],57525:[556,-220,313,55,258],57526:[449,-57,0,30,124],57618:[662,156,926,55,872],57619:[662,156,926,55,872],57620:[662,156,926,54,871],57621:[662,156,926,54,871],57626:[662,156,511,59,451],57627:[662,156,511,59,451],57628:[662,156,926,54,872],57629:[662,156,926,54,872],57630:[662,156,926,54,872],57631:[662,156,926,54,872],57632:[411,-94,511,220,293],57633:[290,-217,311,-3,314],57634:[382,-123,367,54,313],57635:[383,-124,367,54,313],57636:[662,156,511,59,451],57637:[662,156,511,59,451],57638:[449,-57,926,54,872],57639:[449,-57,926,54,872],57640:[662,155,926,54,872],57641:[662,156,926,55,872],57642:[662,156,926,54,871],57643:[661,156,926,54,872],57644:[404,-101,511,220,293],57645:[403,-100,511,220,293],57646:[290,-217,371,14,317],57647:[290,-217,371,54,357],57648:[373,-134,379,70,309],57649:[373,-134,379,70,309],57650:[373,-134,379,70,309],57651:[373,-134,379,70,309],57652:[486,-20,315,0,315],57653:[405,-101,926,230,696],57654:[541,35,315,0,315],57655:[405,-101,1033,229,805]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Arrows"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Arrows/Regular/Main.js"]);
