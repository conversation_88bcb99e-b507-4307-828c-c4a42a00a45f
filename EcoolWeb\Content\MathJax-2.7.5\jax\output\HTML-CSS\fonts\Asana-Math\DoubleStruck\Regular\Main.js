/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/DoubleStruck/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_DoubleStruck={directory:"DoubleStruck/Regular",family:"AsanaMathJax_DoubleStruck",testString:"\u2102\u210D\u2115\u2119\u211A\u211D\u2124\u213C\u213D\u213E\u213F\u2140\u2145\u2146\u2147",32:[0,0,249,0,0],8450:[709,20,708,22,669],8461:[692,3,948,22,927],8469:[692,3,951,17,934],8473:[692,3,720,22,697],8474:[709,176,785,22,764],8477:[692,3,784,22,786],8484:[692,3,816,15,788],8508:[525,15,718,10,688],8509:[485,230,494,-10,466],8510:[692,3,727,22,703],8511:[692,3,899,27,873],8512:[696,1,645,30,618],8517:[692,3,898,-39,888],8518:[694,10,667,-7,707],8519:[463,20,546,24,522],8520:[669,0,388,-17,298],8521:[669,210,409,-98,353],120120:[700,3,887,15,866],120121:[692,3,739,26,705],120123:[692,3,898,22,876],120124:[692,3,727,22,689],120125:[692,3,672,22,653],120126:[709,20,762,22,728],120128:[692,3,453,22,432],120129:[692,194,440,-15,419],120130:[692,3,842,22,836],120131:[692,3,727,22,703],120132:[692,13,1066,16,1047],120134:[709,20,785,22,764],120138:[709,20,524,24,503],120139:[694,3,737,18,720],120140:[692,22,907,12,889],120141:[692,9,851,8,836],120142:[700,9,1119,8,1104],120143:[700,3,783,14,765],120144:[704,3,666,9,654],120146:[463,14,602,42,596],120147:[694,10,667,18,649],120148:[456,16,546,29,517],120149:[694,10,667,17,649],120150:[462,20,546,28,518],120151:[720,0,448,18,456],120152:[460,214,602,38,576],120153:[699,0,673,24,650],120154:[669,0,388,42,346],120155:[669,210,409,-35,316],120156:[698,0,639,25,619],120157:[690,0,390,44,372],120158:[466,0,977,25,959],120159:[457,0,684,27,665],120160:[462,11,602,27,572],120161:[442,194,681,29,666],120162:[442,194,681,22,660],120163:[442,0,509,27,497],120164:[454,14,496,32,463],120165:[615,11,499,23,482],120166:[442,11,699,23,675],120167:[441,11,669,17,653],120168:[437,12,889,17,844],120169:[431,0,704,15,676],120170:[431,204,700,17,674],120171:[447,0,560,12,548],120792:[689,16,600,28,568],120793:[689,3,600,44,556],120794:[679,3,600,30,570],120795:[679,17,600,36,564],120796:[689,3,600,50,550],120797:[675,17,600,27,573],120798:[679,17,600,29,571],120799:[675,3,600,29,571],120800:[679,17,600,38,562],120801:[679,17,600,38,562]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_DoubleStruck"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/DoubleStruck/Regular/Main.js"]);
