﻿using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using ECOOL_APP;
using log4net;

namespace EcoolWeb.Mvc.Filter
{
    public class UserAuthAttribute : System.Web.Mvc.ActionFilterAttribute
    {
        private log4net.ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private long START;
        private long END;

        public override void OnActionExecuting(ActionExecutingContext ctx)
        {

            START = Now;

            UserProfile session_user = (UserProfile)ctx.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY];
            if (session_user == null)
            {
                //導引未登入頁面
                ctx.Result = new RedirectToRouteResult(
                    new RouteValueDictionary 
                    { 
                        { "controller", "Account" }, 
                        { "action", "Access" } 
                    });
            }
            else
            {
                //UserProfile user = new UserProfileHelper();
                ////user.renewCASH();
                //ctx.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = user;
            }

            //ending
            END = Now;
            double sec = new TimeSpan(END - START).TotalSeconds;
            sec = sec * 1000;
            int elapsedTime = Int32.Parse(sec.ToString("0"));
            //UserProfile user = UserProfile.Get();
            /*
            logger.Info(" -----------  Elapsed Time for UserAuth of " + user.PGMNM + "(" + user.PGMID + ")  : " + sec + " milliseconds------------- "); 
             * */
        }

        public override void OnActionExecuted(ActionExecutedContext ctx)
        {


        }

        private long Now
        {
            get
            {
                return DateTime.Now.Ticks;
            }
        }
    }
}