﻿@model EcoolWeb.Models.ADDT06ViewModel
@{
    ViewBag.Title = "閱讀認證-我的待審核清單";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")


@using (Html.BeginForm("ADDTList_CheckPending", "ADDT", FormMethod.Post, new { id = "ADDTList_CheckPending", name = "form1" }))
{

    <div class="form-inline">
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">書名/學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                @Html.HiddenFor(m => m.OrdercColumn)
                @Html.HiddenFor(m => m.whereUserNo)
                @Html.HiddenFor(m => m.whereAPPLY_STATUS)
                @Html.HiddenFor(m => m.whereBOOK_NAME)
                @Html.HiddenFor(m => m.Page)
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋"  onclick = "FunPageProc(1)"/>
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
    </div>



    @section scripts{
        <script>
            var targetFormID = '#ADDTList_CheckPending';
    

     

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }
        </script>
    }


    <img src="~/Content/img/web-Bar-09.png" class="img-responsive App_hide" alt="Responsive image" />
    <div class="table-responsive">
        <div class=row text-center">
            <table  class="table-ecool table-92Per table-hover table-ecool-reader">
                <thead>
                    <tr>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                            申請日期
                            <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center">
                            學年
                        </th>
                        <th style="text-align: center">
                            學期
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                            班級
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                            座號
                            <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                            姓名
                            <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center">
                            書名
                        </th>
                        <th style="text-align: center">
                            批閱
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.ADDT06List.Count() == 0)
                    {
                        <tr><td colspan="8">目前無需要批閱的內容</td></tr>
                    }
                    else
                    {
                        foreach (var item in Model.ADDT06List)
                        {

                            string Mode = (user.USER_TYPE == "T") ? "Edit" : "Del";
                            string Rule = user.USER_TYPE;
                            <tr>
                                <td style="text-align: left;white-space:normal">
                                    @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SYEAR)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SEMESTER)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                <td style="text-align: left;white-space:normal;">
                                    @Html.DisplayFor(modelItem => item.BOOK_NAME)
                                </td>
                                <td align="left">
                                    @Html.ActionLink("批閱", "ADDTList_CheckPendingDetail"
                                     , new
                                     {
                                         Mode = "Edit",
                                         Rule = Rule,
                                         APPLY_NO = item.APPLY_NO,
                                         BackAction = "ADDTList_CheckPending",
                                         whereKeyword = Model.whereKeyword,
                                         whereUserNo = Model.whereUserNo,
                                         whereBOOK_NAME = Model.whereBOOK_NAME,
                                         whereAPPLY_STATUS = Model.whereAPPLY_STATUS,
                                         OrdercColumn = Model.OrdercColumn,
                                         whereCLASS_NO = Model.whereCLASS_NO,
                                         whereGrade = Model.whereGrade,
                                         Page = Model.Page
                                     }, new { @class = "btn btn-xs btn-Basic" })
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <div>
        @Html.Pager(Model.ADDT06List.PageSize, Model.ADDT06List.PageNumber, Model.ADDT06List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>
}