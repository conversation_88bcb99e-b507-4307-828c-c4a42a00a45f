﻿@model CER001IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}
@section css{
    <link href="~/Scripts/stickysort/stickysort.css" rel="stylesheet" />
    <script src="~/Scripts/stickysort/prefixfree.min.js"></script>
    <link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
    <link href="~/Scripts/stickysort/styles.css?var=@DateTime.Now" rel="stylesheet" />
    <style>
        .tooltip-inner {
            text-align: left;
        }

        .show-more {
            padding: 10px 0;
            text-align: right;
        }

        #more {
            display: none;
        }

        .ui-dialog-titlebar {
            background-color: #c2c2c2;
        }

        .ui-dialog .ui-dialog-titlebar-close {
            background-image: url(https://cdnjs.cloudflare.com/ajax/libs/aui/6.0.1/aui/css/icons/aui-icon-close.png);
            z-index: 999999;
        }

        .sticky-wrap .sticky-thead {
            top: 83px;
        }
    </style>
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_Ceri02Menu", "CERI02", new { NowAction = "CER001" });
}
@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
 
   
    <div id="PageContent">
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO)
    </div>
}
@section scripts{
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>
    <script src="~/Scripts/stickysort/jquery.stickysort.js"></script>
    <script src="~/Scripts/stickysort/jquery.ba-throttle-debounce.min.js"></script>
    <script src="~/Scripts/Blob.js"></script>
    <script src="~/Scripts/FileSaver.js"></script>
    <script src="~/Scripts/tableexport.js"></script>
    <script type="text/javascript">

        var targetFormID = '#form1';
        function GetADDTITEM() {

            var selectedACCREDITATION_TYPE = $.trim($('#@Html.IdFor(m => m.WhereACCREDITATION_TYPE) option:selected').val());
            var selectedACCREDITATION_NAME = $.trim($('#@Html.IdFor(m => m.WhereACCREDITATION_NAME) option:selected').val());
            $.ajax({
                url: '@Url.Action("_GetAccreditationsItems_NODDLHtml")',
               data: {
                        tagId: '@Html.IdFor(m => m.WhereACCREDITATION_NAME)',
                        tagName: '@Html.IdFor(m => m.WhereACCREDITATION_NAME)',
                   SCHOOL_NO: '',
                   WhereACCREDITATION_TYPE: selectedACCREDITATION_TYPE,
                   WhereACCREDITATION_NAME: selectedACCREDITATION_NAME
                    },
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#ADDTITEM').html('');
                    $('#ADDTITEM').html(data);
                    $('#ADDTITEM').attr("onchange", "GetSUBJECTItem()");
                }
            });
        }

        function doSearch(ColName, whereValue) {

            $("#" + ColName).val(whereValue);
            funAjax()
        }
      
        function GetSUBJECTItem() {

            var selectedACCREDITATION_TYPE = $.trim($('#@Html.IdFor(m => m.WhereACCREDITATION_TYPE) option:selected').val());
            var selectedACCREDITATION_NAME = $.trim($('#@Html.IdFor(m => m.WhereACCREDITATION_NAME) option:selected').val());
             var selectedSubject_Item = $.trim($('#@Html.IdFor(m => m.WhereSUBJECT_Item) option:selected').val());
            $.ajax({
                url: '@Url.Action("_GetSubjectsItems_NODDLHtml")',
               data: {
                        tagId: '@Html.IdFor(m => m.WhereSUBJECT_Item)',
                        tagName: '@Html.IdFor(m => m.WhereSUBJECT_Item)',
                   ACCREDITATION_ID: selectedACCREDITATION_NAME,
                   WhereACCREDITATION_NAME: selectedACCREDITATION_NAME

                    },
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#SUBJECTItem').html('');
                    $('#SUBJECTItem').html(data);

                }
            });
        }
        function GetCALSSITEM() {

            var selectedGRADE = $.trim($('#@Html.IdFor(m => m.WhereGRADE) option:selected').val());
            var selectedCLASS_NO = $.trim($('#@Html.IdFor(m => m.WhereCLASS_NO) option:selected').val());
            $.ajax({
                url: '@Url.Action("_GetCALSSITEMs_NODDLHtml")',
               data: {
                        tagId: '@Html.IdFor(m => m.WhereCLASS_NO)',
                        tagName: '@Html.IdFor(m => m.WhereCLASS_NO)',
                   SCHOOL_NO: '',
                   WhereGRADE: selectedGRADE,
                   WhereCLASS_NO: selectedCLASS_NO
                    },
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    @*$('@Html.IdFor(m => m.WhereCLASS_NO)').html('');*@
                    $('#@Html.IdFor(m => m.WhereCLASS_NO)').replaceWith(data);
                }
            });
        }
        function FunPageProc(page) {
            $("#Page").val(page);
            funAjax();
        }
        function fn_save() {

               $(".sticky-col").remove();
               $(".sticky-intersect").remove();
            $(".sticky-enabled").each(function (index, value) {

                var tableHtml = '<html><head><meta charset="UTF-8"></head><body><style>th,td{border:3px #FFAC55 solid;padding:5px;}</style><table style="border:3px #FFAC55 solid;padding:5px;text-align:center;" >'
                tableHtml += $(".sticky-enabled").html();
                tableHtml +="</table></body></html>"
                var blob = new Blob([tableHtml], {
                    //type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                    type:'application/vnd.ms-excel'
                });
                var strFile = "Report.xls";

                saveAs(blob, strFile);

            })

                funAjax()
            return false;
        }

        function onGrade() {
            $('#@Html.IdFor(m=>m.WhereGRADE_SEMESTERs)').val('default').selectpicker("refresh");

            var Grade = $('#@Html.IdFor(m=>m.WhereGRADE)').val();
            $('#@Html.IdFor(m=>m.WhereGRADE_SEMESTERs)').selectpicker('val', [Grade+'_1',Grade+'_2']);
            $('#@Html.IdFor(m=>m.WhereGRADE_SEMESTERs)').selectpicker("refresh");
        }

        //查詢
        function funAjax() {
            $("#TableData").html("");
            $.ajax({
                url: '@Url.Action("_PageContent")',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear(IsSubmit) {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            if (IsSubmit) {
                FunPageProc(1);
            }

        }
    </script>
}