﻿
@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }


    ViewBag.Title = "生日賀禮";
}


<table align="center">
    <tr>
        <td>
            <span style="white-space: nowrap;font-size: 12pt;font-weight: bold; color:red">
                小朋友恭喜你～祝你生日快樂喔！<br />
                你是這個月的壽星，恭喜你得到50點e酷幣生日賀禮！
            </span>
            <br/>
            <span style="white-space: nowrap;font-size: 12pt;font-weight: bold;color:blue">
                新的一年，希望你的願望都能夠達成喔，祝福你！<br />
                請務必在生日當月內領取，按下"領取生日禮金"，跨月逾期則自動取消生日酷幣點數。
            </span>
        </td>
    
    </tr>
    <tr>
        <td align="center" style="white-space: nowrap;font-size: 16pt;font-weight: bold;color:blue">
            <a href="@Url.Action("BirthdayGet", "Home")" class="btn btn-default" role="button">
                領取生日禮金
            </a>
        </td>
    </tr>
    <tr>
        <td align="center">
            @if (AppMode)
            {

                   <img style="width:400px ;height: auto" src="~/Content/mp4/coolBirthday-50.gif" />

                @*<img src="~/Content/img/web-student-allpage-16.png" class="img-responsive " alt="Responsive image" />*@
                @*<video id="SlotPlayer" style="width:400px ;height: auto" autoplay="autoplay">
                    <source src='~/Content/mp4/Birthday.mp4' type="video/mp4">
                </video>*@
            }
            else
            {
                <video id="SlotPlayer" style="width:400px ;height: auto" autoplay="autoplay">
                    <source src='~/Content/mp4/Birthday.mp4' type="video/mp4">
                </video>
            }

        </td>
    </tr>

</table>

<script type="text/javascript">
    window.history.forward(1);
</script>