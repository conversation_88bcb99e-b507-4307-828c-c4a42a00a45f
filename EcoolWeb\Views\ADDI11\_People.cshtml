﻿
@model ADDI11EditPeopleViewModel

@using (Html.BeginCollectionItem("People"))
{
    var index = Html.GetIndex("People");
    @Html.HiddenFor(m=>m.UserType)
    <div class="tr" id="Tr@(index)">
        <div class="td">
            @Html.DropDownListFor(m => m.USER_NO, (IEnumerable<SelectListItem>)ViewBag.UserNoItems, new { @class = "form-control" })
            @Html.ValidationMessageFor(m => m.USER_NO, "", new { @class = "text-danger" })

            @Html.HiddenFor(m => m.SCHOOL_NO)
            @Html.HiddenFor(m => m.CLASS_NO)
        </div>
        <div class="td">
            @Html.EditorFor(m => m.LAP, new { htmlAttributes = new { @class = "form-control LAP_Edit" } })
            @Html.ValidationMessageFor(m => m.LAP, "", new { @class = "text-danger" })
        </div>
    </div>

}