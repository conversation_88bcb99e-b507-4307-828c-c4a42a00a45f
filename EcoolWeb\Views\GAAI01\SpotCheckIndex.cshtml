﻿@model GAAI01SpotCheckIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();

}
<style>
    #Data div {
        font-size: 24px
    }
</style>

@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank" }))
{
    @Html.HiddenFor(m => m.SetUN_WEAR_TYPE)
    @Html.AntiForgeryToken()
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>@ViewBag.Title</h1>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                            @Html.Editor("CARD_NO", new { htmlAttributes = new { @class = "form-control", @placeholder = "請用數位學生證感應", @onKeyPress = "call(event,this);" } })
                        </div>
                        @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                    </div>
                    <div class="form-inline" role="form" id="search">
                        <div class="form-group">
                            @Html.LabelFor(m => m.SetIs_WEAR, htmlAttributes: new { @class = "control-label control-label-required" })
                        </div>
                        <div class="form-group">
                            @Html.DropDownListFor(m => m.SetIs_WEAR, (IEnumerable<SelectListItem>)ViewBag.IsWearItgem, new { @class = "form-control form-control-required" })
                        </div>
                        <div class="form-group">
                            @Html.LabelFor(m => m.SetCASH, htmlAttributes: new { @class = "control-label control-label-required" })
                        </div>
                        <div class="form-group">
                            @Html.EditorFor(m => m.SetCASH, new { htmlAttributes = new { @class = "form-control form-control-required" } })
                        </div>
                    </div>

                    @*<div class="form-group row">
                            @Html.LabelFor(m => m.SetUN_WEAR_TYPE, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                            <div class="col-md-9">
                                @Html.DropDownListFor(m => m.SetUN_WEAR_TYPE, GAAT02_U.GetUnWearTypeItems(Model?.SetUN_WEAR_TYPE), new { @class = "form-control form-control-required" })
                            </div>
                            <div class="col-md-9">
                                @Html.ValidationMessageFor(m => m.SetUN_WEAR_TYPE, "", new { @class = "text-danger" })
                            </div>
                        </div>*@
                </div>
            </div>
            <div id="DetailsView">
                <div style="margin-top:5px;margin-bottom:5px;text-align:center">
                    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
                </div>
                @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                <div class="panel panel-ZZZ">
                    <div class="panel-heading text-center">
                        有配載的學生
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive" id="Data">
                            <div class="css-table" style="width:92%;" id="tbData">
                                <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">

                                    <div class="th" style="text-align:center">
                                        全名
                                    </div>
                                    <div class="th" style="text-align:center">
                                        學號
                                    </div>
                                    <div class="th" style="text-align:center">
                                        班級
                                    </div>
                                    <div class="th" style="text-align:center">
                                        座號
                                    </div>
                                    <div class="th" style="text-align:center">
                                        是否配載
                                    </div>
                                    <div class="th" style="text-align:center">
                                        加酷幣點數
                                    </div>
                                </div>
                                <div id="editorRows" class="tbody">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-xs-12 text-center">
                        <div class="form-inline">
                            <a class="btn btn-default" href="@Url.Action("Logout","Home")">回首頁</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr" style="font-size:50px"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script src="~/Scripts/buzz/buzz.min.js"></script>
    <script language="JavaScript">
        var targetFormID = '#form1';

        var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );

        (function ($) {
            $(window).on("beforeunload", function () {
                window.location.href= "@Url.Action("Logout","Home")";
            })
        })(jQuery);

        $(document).ready(function () {
            $("#CARD_NO").focus();
        });

        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var CARD_NO = $('#CARD_NO').val();

                event.preventDefault();

                if (CARD_NO != '') {
                        $('#CARD_NO').prop('readonly', true);

                        setTimeout(function () {
                            OnKeyinUse(CARD_NO)
                        });
                }
            }
        }

        function OnKeyinUse(CARD_NO) {

            var data = {
                "CARD_NO": CARD_NO,
                "SetIs_WEAR":  $('#@Html.IdFor(m=>m.SetIs_WEAR)').val(),
                "SetCASH": $('#@Html.IdFor(m=>m.SetCASH)').val(),
                "SetUN_WEAR_TYPE": $('#@Html.IdFor(m=>m.SetUN_WEAR_TYPE)').val(),
            };

            $.ajax({
                url: '@Url.Action("SaveSpotCheckData", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {

                    $('#ErrorDiv').show()

                    if (html.indexOf('Error') == -1) {
                        $("#editorRows").prepend(html);
                        $('#ErrorStr').html('感應成功…')
                        SwipeOK()
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 300);
                    }
                    else {
                        $('#ErrorStr').html(html)
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 1400);
                    }

                }
            });
        }

         function SwipeOK() {

                 SwipeSound.play();
         }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }
    </script>
}