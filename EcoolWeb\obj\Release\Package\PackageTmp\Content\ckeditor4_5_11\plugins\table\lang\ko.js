﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'ko', {
	border: '테두리 두께',
	caption: '주석',
	cell: {
		menu: '셀',
		insertBefore: '앞에 셀 삽입',
		insertAfter: '뒤에 셀 삽입',
		deleteCell: '셀 삭제',
		merge: '셀 합치기',
		mergeRight: '오른쪽 합치기',
		mergeDown: '왼쪽 합치기',
		splitHorizontal: '수평 나누기',
		splitVertical: '수직 나누기',
		title: '셀 속성',
		cellType: '셀 종류',
		rowSpan: '행 간격',
		colSpan: '열 간격',
		wordWrap: '줄 끝 단어 줄 바꿈',
		hAlign: '가로 정렬',
		vAlign: '세로 정렬',
		alignBaseline: '영문 글꼴 기준선',
		bgColor: '배경색',
		borderColor: '테두리 색',
		data: '자료',
		header: '머릿칸',
		yes: '예',
		no: '아니오',
		invalidWidth: '셀 너비는 숫자여야 합니다.',
		invalidHeight: '셀 높이는 숫자여야 합니다.',
		invalidRowSpan: '행 간격은 정수여야 합니다.',
		invalidColSpan: '열 간격은 정수여야 합니다.',
		chooseColor: '선택'
	},
	cellPad: '셀 여백',
	cellSpace: '셀 간격',
	column: {
		menu: '열',
		insertBefore: '왼쪽에 열 삽입',
		insertAfter: '오른쪽에 열 삽입',
		deleteColumn: '열 삭제'
	},
	columns: '열',
	deleteTable: '표 삭제',
	headers: '머릿칸',
	headersBoth: '모두',
	headersColumn: '첫 열',
	headersNone: '없음',
	headersRow: '첫 행',
	invalidBorder: '테두리 두께는 숫자여야 합니다.',
	invalidCellPadding: '셀 여백은 0 이상이어야 합니다.',
	invalidCellSpacing: '셀 간격은 0 이상이어야 합니다.',
	invalidCols: '열 번호는 0보다 커야 합니다.',
	invalidHeight: '표 높이는 숫자여야 합니다.',
	invalidRows: '행 번호는 0보다 커야 합니다.',
	invalidWidth: '표의 너비는 숫자여야 합니다.',
	menu: '표 속성',
	row: {
		menu: '행',
		insertBefore: '위에 행 삽입',
		insertAfter: '아래에 행 삽입',
		deleteRow: '행 삭제'
	},
	rows: '행',
	summary: '요약',
	title: '표 속성',
	toolbar: '표',
	widthPc: '백분율',
	widthPx: '픽셀',
	widthUnit: '너비 단위'
} );
