﻿@{
    if (Session["CheckBrowser"] == null)
    {
        Session["CheckBrowser"] = true;

        var hbc = Request.Browser;
        float Version;
        float.TryParse(hbc.Version.ToString(), out Version);


        if ((hbc.Browser.ToString() == "InternetExplorer" || hbc.Browser.ToString() == "IE") && Version <= 10)
        {
            <div class="alert alert-warning alert-dismissible fade in" role="alert">
                <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <strong>注意!</strong>為了正常瀏覽臺北e酷幣，建議您使用最新版Google Chrome、Firefox、Internet Explorer 11 (IE11) 以上版本 瀏覽器，使用IE 10以下版本可能會產生部分功能無法正常顯示的問題。
            </div>
        }
    }
}
