/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsUp/Bold/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXIntegralsUp-bold"],{32:[0,0,250,0,0],160:[0,0,250,0,0],8747:[824,320,425,59,467],8748:[824,320,715,59,757],8749:[824,320,1005,59,1047],8750:[834,310,394,35,483],8751:[824,320,650,35,739],8752:[824,320,951,54,1047],8753:[824,320,484,54,553],8754:[824,320,445,35,534],8755:[824,320,456,35,545],10764:[824,320,1295,59,1337],10765:[824,320,511,59,553],10766:[824,320,511,59,553],10767:[824,320,592,59,634],10768:[824,320,385,35,474],10769:[824,320,484,54,553],10770:[824,320,417,35,486],10771:[824,320,424,54,493],10772:[824,320,535,54,604],10773:[824,320,416,35,505],10774:[824,320,459,35,528],10775:[824,320,824,45,884],10776:[824,320,527,45,587],10777:[824,320,567,45,632],10778:[824,320,567,45,632],10779:[959,320,479,45,521],10780:[824,455,411,35,511]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsUp/Bold/All.js");
