﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'bs', {
	access: 'Script Access', // MISSING
	accessAlways: 'Always', // MISSING
	accessNever: 'Never', // MISSING
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Abs dole',
	alignAbsMiddle: 'Abs sredina',
	alignBaseline: '<PERSON>zno',
	alignTextTop: 'Vrh teksta',
	bgcolor: '<PERSON>ja pozadine',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'Loop', // MISSING
	chkMenu: 'Enable Flash Menu', // MISSING
	chkPlay: 'Auto Play', // MISSING
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'HSpace',
	properties: 'Flash Properties', // MISSING
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'Scale', // MISSING
	scaleAll: 'Show all', // MISSING
	scaleFit: 'Exact Fit', // MISSING
	scaleNoBorder: 'No Border', // MISSING
	title: 'Flash Properties', // MISSING
	vSpace: 'VSpace',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'Molimo ukucajte URL link',
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
