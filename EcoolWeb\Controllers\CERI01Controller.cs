﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 護照設定
    /// </summary>
    [SessionExpire]
    [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
    public class CERI01Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "CERI01";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CERI01Service Service = new CERI01Service();

        #region 列表畫面

        public ActionResult Index()
        {
            this.Shared();
            return View();
        }

        public ActionResult _PageContent(AccreditationTypeIndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new AccreditationTypeIndexViewModel();
            model = Service.GetListData(model, SCHOOL_NO, ref db);
            return PartialView(model);
        }

        #endregion 列表畫面

        #region 編輯畫面及處理

        public ActionResult Edit(AccreditationTypeEditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new AccreditationTypeEditViewModel();

            if (!string.IsNullOrWhiteSpace(model.Keyword))
            {
                model = this.Service.GetEditData(model, ref db);
            }

            return View(model);
        }

        /// <summary>
        /// 編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult EditSave(AccreditationTypeEditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new AccreditationTypeEditViewModel();

            string Message = string.Empty;

            if (model.Main == null)
            {
                return RedirectToAction("Edit");
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                var Result = this.Service.SaveEditData(model, user, ref db);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "護照名稱設定完成，請至「護照明細設定 」繼續設定";

                    return RedirectToAction(nameof(CERI01Controller.Index));
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(CERI01Controller.Edit), model);
        }

        #endregion 編輯畫面及處理

        #region 作廢

        /// <summary>
        /// 編輯 Del
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult Del(AccreditationTypeEditViewModel model)
        {
            this.Shared("編輯");

            if (string.IsNullOrWhiteSpace(model.Keyword) && string.IsNullOrWhiteSpace(model.Main?.TYPE_ID))
            {
                return RedirectToAction(nameof(CERI01Controller.Index));
            }

            string Message = string.Empty;

            var Result = this.Service.DelDate(model, user, ref db);

            if (Result.Success)
            {
                TempData[SharedGlobal.StatusMessageName] = "作廢完成";
                return RedirectToAction(nameof(CERI01Controller.Index));
            }
            Message += Result.Message;

            TempData[SharedGlobal.StatusMessageName] = Message;

            return RedirectToAction(nameof(CERI01Controller.Index));
        }

        #endregion 作廢

        [AllowAnonymous]
        public JsonResult GetACCREDITATION_TYPEs(string term)
        {
            this.Shared();
            var listhe = this.Service.GetACCREDITATION_TYPEs(term, SCHOOL_NO, ref db).Select(a => new { id = a.TYPE_ID, firstName = a.TYPE_NAME });

            return Json(listhe, JsonRequestBehavior.AllowGet);
        }

        #region Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared
    }
}