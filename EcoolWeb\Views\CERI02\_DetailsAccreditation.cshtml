﻿@model AccreditationEditModalViewModel

@using (Html.BeginCollectionItem("oAccreditations"))
{
    var Index = Html.GetIndex("oAccreditations");

    <li class="list-group-item clearfix" id="Tr@(Index)">
        <a class="btn btn-xs btn-Basic" role="button" onclick="deleteRow('Tr@(Index)')">
            <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
        </a>
        <div class="row">
            <div class="col-sm-10">
                @Html.HiddenFor(m => m.ACCREDITATION_ID)
                @Html.HiddenFor(m => m.ACCREDITATION_NAME)
                @Html.HiddenFor(m => m.ACCREDITATION_TYPE)
                @Html.HiddenFor(m => m.TYPE_NAME)
                <strong>@Model.ACCREDITATION_NAME</strong>
                <small>@Model.TYPE_NAME</small>
            </div>
        </div>
    </li>

}