﻿@model  ZZZI27IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}



@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.Search.wBRE_NO)
    @Html.HiddenFor(m => m.Search.wDATA_CODE)

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    string[] BtnOrderList = { "SECI01LAST_DAY_MEMO", "ADD01VERIFY_COMMENT", "ADDI01BACK_REASON", "ADDI01REMIND", "ADDTBACK_REASON", "ADDTREMIND", "ADDI09SUBJECT", "HomeTeacherIndex", "ADDI06IMAGE_Student", "ADDI14SYS_TABLE_TYPE", "ADDI14SYS_TABLE_Person_TYPE", "ADDI09SYS_TABLE_SUBJECT" };
    <div class="form-horizontal">
        <div class="panel panel-ACC">
            <div class="panel-heading text-center">
                <h3 class="panel-title" name="TOP">@ViewBag.Panel_Title</h3>
            </div>
            <div class="panel-body">

                @{
                    foreach (var BtnOrder in BtnOrderList)
                    {
                        foreach (var Btn in Model.Data_List.Where(x => x.BRE_NO + x.DATA_CODE == BtnOrder).ToList())
                        {
                          
                                <button class="btn btn-default  btn-block" onclick="onActionLink('@Btn.BRE_NO','@Btn.DATA_CODE')">@Btn.DATA_DESC</button>
                         

                        }
                    }
                }

            </div>
        </div>
    </div>
}
<script type="text/javascript">
    var targetFormID = '#form1';


    function onActionLink(wBRE_NO, wDATA_CODE) {
        $('#@Html.IdFor(m => m.Search.wBRE_NO)').val(wBRE_NO)
        $('#@Html.IdFor(m => m.Search.wDATA_CODE)').val(wDATA_CODE)
           $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
           $(targetFormID).submit();
        }
</script>