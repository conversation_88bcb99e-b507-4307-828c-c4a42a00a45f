﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO

<div class="form-group">
    @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.CONTENT_TXT, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.TextAreaFor(model => model.CONTENT_TXT, 5, 100, new { @class = "form-control" })
        @Html.ValidationMessageFor(model => model.CONTENT_TXT, "", new { @class = "text-danger" })
    </div>
</div>

@if (!Model.Individual_Give) { 
    <div class="form-group">
        @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })
        </div>
    </div>
}

<div class="form-group">
    @Html.LabelFor(model => model.MEMO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.MEMO, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.MEMO, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        <input class="btn btn-default" type="file" name="files" />
        @Html.ValidationMessage("files", "", new { @class = "text-danger" })
    </div>
</div>
