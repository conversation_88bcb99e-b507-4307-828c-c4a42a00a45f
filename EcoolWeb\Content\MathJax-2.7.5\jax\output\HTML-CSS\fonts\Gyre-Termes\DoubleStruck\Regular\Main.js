/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/DoubleStruck/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_DoubleStruck={directory:"DoubleStruck/Regular",family:"GyreTermesMathJax_DoubleStruck",testString:"\u00A0\u2102\u210D\u2115\u2119\u211A\u211D\u2124\u213C\u213D\u213E\u213F\u2140\u2145\u2146",32:[0,0,250,0,0],160:[0,0,250,0,0],8450:[718,14,816,80,736],8461:[702,0,923,79,843],8469:[702,11,915,80,835],8473:[702,0,754,80,674],8474:[716,178,875,80,795],8477:[702,0,876,80,796],8484:[702,0,808,79,728],8508:[511,16,738,80,658],8509:[491,234,715,80,635],8510:[702,0,734,80,654],8511:[702,0,883,80,803],8512:[728,228,884,80,803],8517:[702,0,869,43,834],8518:[723,8,699,80,659],8519:[500,10,624,83,552],8520:[728,0,455,43,403],8521:[728,218,493,6,511],120120:[714,0,923,80,843],120121:[702,0,806,80,726],120123:[702,0,869,80,789],120124:[702,0,795,80,714],120125:[702,0,756,80,676],120126:[719,14,897,80,817],120128:[702,0,517,80,437],120129:[702,14,600,80,520],120130:[702,0,929,80,849],120131:[702,0,795,80,714],120132:[702,0,1037,80,957],120134:[716,14,873,80,793],120138:[716,17,679,79,599],120139:[702,0,800,79,720],120140:[702,14,911,80,831],120141:[702,13,892,80,812],120142:[702,14,1167,80,1087],120143:[702,0,914,80,834],120144:[702,0,903,80,823],120146:[500,10,630,80,550],120147:[723,10,695,80,615],120148:[500,10,608,80,528],120149:[723,8,699,80,619],120150:[500,10,624,80,544],120151:[723,0,583,80,503],120152:[500,218,632,80,552],120153:[721,0,725,80,645],120154:[728,0,455,79,375],120155:[728,218,493,80,413],120156:[723,0,754,79,674],120157:[722,0,460,80,380],120158:[502,0,1022,80,942],120159:[499,0,713,80,633],120160:[501,11,661,80,581],120161:[500,217,702,80,622],120162:[501,217,698,80,618],120163:[501,0,562,80,482],120164:[503,14,497,80,417],120165:[601,14,471,80,391],120166:[486,13,713,79,633],120167:[490,14,690,80,610],120168:[490,14,936,80,856],120169:[487,0,677,79,597],120170:[490,218,704,80,624],120171:[490,0,620,80,540],120792:[716,14,672,80,592],120793:[716,0,503,80,423],120794:[716,-1,659,80,579],120795:[718,14,610,80,530],120796:[716,0,723,80,643],120797:[727,14,607,80,527],120798:[723,14,672,80,592],120799:[703,8,630,80,550],120800:[716,14,599,80,519],120801:[716,21,672,80,592]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_DoubleStruck"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/DoubleStruck/Regular/Main.js"]);
