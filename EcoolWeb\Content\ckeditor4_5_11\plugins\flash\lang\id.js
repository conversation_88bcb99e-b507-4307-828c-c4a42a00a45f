﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'id', {
	access: 'Script Access', // MISSING
	accessAlways: 'Selalu',
	accessNever: 'Tidak Pernah',
	accessSameDomain: 'Domain yang sama',
	alignAbsBottom: 'Abs Bottom', // MISSING
	alignAbsMiddle: 'Abs Middle', // MISSING
	alignBaseline: 'Dasar',
	alignTextTop: 'Text Top', // MISSING
	bgcolor: 'Warna La<PERSON> Belakang',
	chkFull: 'Izinkan Layar Penuh',
	chkLoop: 'Loop', // MISSING
	chkMenu: 'Enable Flash Menu', // MISSING
	chkPlay: 'Mainkan Otomatis',
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'HSpace', // MISSING
	properties: 'Flash Properties', // MISSING
	propertiesTab: 'Properti',
	quality: '<PERSON><PERSON><PERSON>',
	qualityAutoHigh: 'Tinggi Otomatis',
	qualityAutoLow: 'Rendah Otomatis',
	qualityBest: 'Terbaik',
	qualityHigh: 'Tinggi',
	qualityLow: 'Rendah',
	qualityMedium: 'Sedang',
	scale: 'Scale', // MISSING
	scaleAll: 'Perlihatkan semua',
	scaleFit: 'Exact Fit', // MISSING
	scaleNoBorder: 'Tanpa Batas',
	title: 'Flash Properties', // MISSING
	vSpace: 'VSpace',
	validateHSpace: 'HSpace harus sebuah angka',
	validateSrc: 'URL tidak boleh kosong',
	validateVSpace: 'VSpace harus sebuah angka',
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparan',
	windowModeWindow: 'Jendela'
} );
