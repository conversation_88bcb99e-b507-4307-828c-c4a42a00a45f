﻿@model EcoolWeb.ViewModels.AWA007QueryViewModel
@using ECOOL_APP.com.ecool.util

@{ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();}
@{
    ViewBag.Title = "角色娃娃統計排行";
    int i = 0;
    string iUser_no = (user != null) ? user.USER_NO : "";

    string HidStyle = Model.IsPrint == true ? "display:none" : "";
    int RowNumber = 0;
    int ResetNumber = 0;
    if (Model.IsPrint)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
}
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }

    a[href]:after {
        content: none !important;
    }

</style>


@using (Html.BeginForm("Query", "AWA007", FormMethod.Post, new { name = "AWA007", id = "AWA007" }))
{
    @*@Html.HiddenFor(m => m.whereSNAME)
    @Html.HiddenFor(m => m.whereAWARD_NAME)
    @Html.HiddenFor(m => m.whereCLASS_NO)*@
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.IsPrint)
    @Html.HiddenFor(m => m.IsToExcel)
    @Html.HiddenFor(m => m.WhereIsMonthTop)
    <br />

    @section scripts{
        <script>
            var targetFormID = '#AWA007';


         window.onload = function () {
                if ($('#@Html.IdFor(m=>m.IsPrint)').val() == "true" && $('#@Html.IdFor(m=>m.IsToExcel)').val() != "true"  ) {
                    window.print()
                }
         }

         $(function () {
                $('#ButtonExcel').click(function () {
                    var blob = new Blob([document.getElementById('tbData').innerHTML], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                    });
                    var strFile = "Report.xls";
                    saveAs(blob, strFile);
                    return false;
                });
            });

        function PrintBooK()
        {
            $('#@Html.IdFor(m=>m.IsPrint)').val(true)
            $(targetFormID).attr('action','@Url.Action("Query", "AWA007")')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            $('#@Html.IdFor(m=>m.IsPrint)').val(false)
        }

         function doMonthTop(val) {
                $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);
                FunPageProc(1)
            }



            $(function () {

                // Fields
                var _pageLinkers = $(".pager> a");

                // Binding click event
                _pageLinkers.each(function (i, item) {
                    var page = getParameterByName($(item).attr('href'), 'page')
                    $(item).attr('href', '#').click(function () { postPage(page); });
                });

            });

            function getParameterByName(url, name) {
                name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
                var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
                    results = regex.exec(url);
                return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
            }

            function postPage(page) {
                if ($(targetFormID).size() > 0) {
                    $('<input>')
                        .attr({ type: 'hidden', id: 'page', name: 'page', value: page })
                        .appendTo($(targetFormID));
                    $(targetFormID).submit();
                }
            };

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                $(targetFormID).submit();
            }

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {

                    $('#Page').val(page);
                    $(targetFormID).submit();
                }
            };


            function todoClear() {

                ////重設
                $("#AWA007").find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }
        </script>
    }

    if (user != null)
    {
        if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
        {
            <br />
            <div class="form-inline">
                <div class="col-xs-12 text-right">
                    @if (!Model.IsPrint)
                    {
                        <button id="ButtonExcel" class="btn btn-sm btn-sys cScreen">匯出excel</button>
                        <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
                    }
                    else
                    {
                        <button type="button" class="btn btn-sm btn-sys cScreen" onclick="PrintBooK()">我要列印</button>
                    }

                </div>
            </div>
        }
    }

    <div class="form-inline cScreen" style="text-align:right;@HidStyle">
        <br />
        <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active":"")" type="button" onclick="todoClear();doMonthTop('false');">全部</button>
        <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active":"")" type="button" onclick="todoClear();doMonthTop('true');">月排行榜</button>
    </div>


    <img src="~/Content/img/web-bar2-revise-26.png" style="width:100%;@HidStyle" class="img-responsive " alt="Responsive image" />
    <div class="form-inline" style="margin:15px;@HidStyle">
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">學校</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("whereSchoolNo", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
                @*@Html.DropDownListFor(m=>m.whereSchoolNo, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })*@
            </div>
        </div>
    </div>
}

<div class="@(Model.IsPrint ? "":"table-responsive")">
    <div class="text-center" id="tbData">
        <table class="@(Model.IsPrint ? "table table-bordered" : "table-ecool table-92Per table-hover table-ecool-AWA007")">
            <thead>
                <tr>
                    <th>序號</th>
                    <th style="text-align: center;">
                        角色名稱
                    </th>
                    <th style="text-align: center;">
                        角色類別
                    </th>
                    <th style="text-align: center;">
                        選擇次數
                    </th>
                    <th style="text-align: center;">
                        角色圖示
                    </th>
                </tr>

            </thead>
            <tbody>
                @foreach (var item in Model.AWA006List)
                {
                    ResetNumber++;
                       RowNumber = Model.AWA006List.PageSize * (Model.AWA006List.PageNumber - 1) + (ResetNumber);
                    string ImgPath = @ECOOL_APP.UrlCustomHelper.Url_Content(@"~/Content/Players/" + item.IMG_FILE);

                    <tr>
                        <td>@RowNumber</td>
                        <td>
                            @Html.DisplayFor(modelItem => item.PLAYER_NAME)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.PLAYER_TYPE)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.PLAYER_COUNT)
                        </td>
                        <td>
                            <img style="height:120px;width:51PX;" src='@ImgPath'>
                        </td>

                    </tr>
                    i++;
                }
            </tbody>
        </table>
    </div>
</div>


<div>
    @Html.Pager(Model.AWA006List.PageSize, Model.AWA006List.PageNumber, Model.AWA006List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>