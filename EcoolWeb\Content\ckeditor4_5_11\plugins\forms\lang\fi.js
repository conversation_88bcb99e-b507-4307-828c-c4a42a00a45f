﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'fi', {
	button: {
		title: 'Painikkeen ominaisuudet',
		text: '<PERSON><PERSON><PERSON> (arvo)',
		type: 'Tyyppi',
		typeBtn: 'Painike',
		typeSbm: '<PERSON>ähetä',
		typeRst: 'Tyhjennä'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Valintaruudun ominaisuudet',
		radioTitle: 'Radiopainikkeen ominaisuudet',
		value: 'Arvo',
		selected: 'Valittu',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Lomakkeen ominaisuudet',
		menu: 'Lomakkeen ominaisuudet',
		action: 'Toiminto',
		method: 'Tapa',
		encoding: 'Enkoodaus'
	},
	hidden: {
		title: 'Piilokentän ominaisuudet',
		name: '<PERSON><PERSON>',
		value: 'Arvo'
	},
	select: {
		title: '<PERSON><PERSON><PERSON><PERSON><PERSON>n ominaisuudet',
		selectInfo: 'Info',
		opAvail: 'Ominaisuudet',
		value: 'Arvo',
		size: 'Ko<PERSON>',
		lines: 'Rivit',
		chkMulti: 'Salli usea valinta',
		required: 'Required', // MISSING
		opText: 'Teksti',
		opValue: 'Arvo',
		btnAdd: 'Lisää',
		btnModify: 'Muuta',
		btnUp: 'Ylös',
		btnDown: 'Alas',
		btnSetValue: 'Aseta valituksi',
		btnDelete: 'Poista'
	},
	textarea: {
		title: 'Tekstilaatikon ominaisuudet',
		cols: 'Sarakkeita',
		rows: 'Rivejä'
	},
	textfield: {
		title: 'Tekstikentän ominaisuudet',
		name: 'Nimi',
		value: 'Arvo',
		charWidth: 'Leveys',
		maxChars: 'Maksimi merkkimäärä',
		required: 'Required', // MISSING
		type: 'Tyyppi',
		typeText: 'Teksti',
		typePass: 'Salasana',
		typeEmail: 'Sähköposti',
		typeSearch: 'Haku',
		typeTel: 'Puhelinnumero',
		typeUrl: 'Osoite'
	}
} );
