(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: sort2Hash (BETA) - updated 9/27/2017 (v2.29.0) */
!function(g){"use strict";var p=g.tablesorter||{},u=p.sort2Hash={init:function(e,t){var a,r,o,s,n=e.table,i=e.pager,h=p.hasWidget(n,"saveSort"),l=u.decodeHash(e,t,"sort");(l&&!h||l&&h&&t.sort2Hash_overrideSaveSort)&&u.convertString2Sort(e,t,l),p.hasWidget(e.table,"pager")&&(r=parseInt(u.decodeHash(e,t,"page"),10),o=i.page=r<0?0:r>i.totalPages?i.totalPages-1:r,s=i.size=parseInt(u.decodeHash(e,t,"size"),10)),p.hasWidget(n,"filter")&&(a=u.decodeHash(e,t,"filter"))&&(a=a.split(t.sort2Hash_separator),e.$table.one("tablesorter-ready",function(){setTimeout(function(){e.$table.one("filterEnd",function(){g(this).triggerHandler("pageAndSize",[o,s])}),(r=p.filter.equalFilters?p.filter.equalFilters(e,e.lastSearch,a):(e.lastSearch||[]).join("")!==(a||[]).join(""))||g.tablesorter.setFilters(n,a,!0)},100)})),a||e.$table.one("tablesorter-ready",function(){e.$table.triggerHandler("pageAndSize",[o,s])}),e.$table.on("sortEnd.sort2hash filterEnd.sort2hash pagerComplete.sort2Hash",function(){this.hasInitialized&&u.setHash(this.config,this.config.widgetOptions)})},getTableId:function(e,t){return t.sort2Hash_tableId||e.table.id||"table"+g("table").index(e.$table)},regexEscape:function(e){return e.replace(/([\.\^\$\*\+\-\?\(\)\[\]\{\}\\\|])/g,"\\$1")},convertString2Sort:function(e,t,a){for(var r,o,s,n,i,h,l=a.split(t.sort2Hash_separator),d=0,c=l.length,H=[];d<c;){if(o=l[d++],n=parseInt(o,10),isNaN(n)||n>e.columns)for(r=new RegExp("("+u.regexEscape(o)+")","i"),i=0;i<e.columns;i++)h=e.$headerIndexed[i],r.test(h.attr(t.sort2Hash_headerTextAttr))&&(o=i,i=e.columns);s=l[d++],void 0!==o&&void 0!==s&&(isNaN(s)&&(s=-1<s.indexOf(t.sort2Hash_directionText[1])?1:0),H.push([o,s]))}H.length&&(e.sortList=H)},convertSort2String:function(e,t){var a,r,o,s,n=[],i=e.sortList||[],h=i.length;for(a=0;a<h;a++)o=i[a][0],r=g.trim(e.$headerIndexed[o].attr(t.sort2Hash_headerTextAttr)),n.push(""!==r?encodeURIComponent(r):o),s=t.sort2Hash_directionText[i[a][1]],n.push(s);return n.join(t.sort2Hash_separator)},convertFilter2String:function(e,t){var a,r,o,s,n=[],i=e.sortList||[],h=i.length;for(a=0;a<h;a++)o=i[a][0],o=void 0!==(r=g.trim(e.$headerIndexed[o].attr(t.sort2Hash_headerTextAttr)))?encodeURIComponent(r):o,n.push(o),s=t.sort2Hash_directionText[i[a][1]],n.push(s);return n.join(t.sort2Hash_separator)},getParam:function(e,t,a){t||(t=window.location.hash);var r=new RegExp("[\\?&]"+u.regexEscape(e)+"=([^&#]*)"),o=r.exec(t);return a?r:null===o?"":decodeURIComponent(o[1])},removeParam:function(e,t){t||(t=window.location.hash);var a,r=u.getParam(e,t,!0),o=[],s=t.split("&"),n=s.length;for(a=0;a<n;a++)r.test("&"+s[a])||o.push(s[a]);return o.length?o.join("&"):""},encodeHash:function(e,t,a,r,o){var s=!1,n=u.getTableId(e,t);return"function"==typeof t.sort2Hash_encodeHash&&(s=t.sort2Hash_encodeHash(e,n,a,r,o||r)),!1===s&&(s="&"+a+"["+n+"]="+r),s},decodeHash:function(e,t,a){var r=!1,o=u.getTableId(e,t);return"function"==typeof t.sort2Hash_decodeHash&&(r=t.sort2Hash_decodeHash(e,o,a)),!1===r&&(r=u.getParam(a+"["+o+"]")),r||""},cleanHash:function(e,t,a,r){var o=!1,s=u.getTableId(e,t);return"function"==typeof t.sort2Hash_cleanHash&&(o=t.sort2Hash_cleanHash(e,s,a,r)),!1===o&&(o=u.removeParam(a+"["+s+"]",r)),o||""},setHash:function(a,r){var o="",s=window.location.hash,e=p.hasWidget(a.table,"pager"),t=p.hasWidget(a.table,"filter"),n=u.convertSort2String(a,r),i=t&&""!==a.lastSearch.join("")?a.lastSearch:[],h=encodeURIComponent(i.join(a.widgetOptions.sort2Hash_separator)),l={sort:n?u.encodeHash(a,r,"sort",n,a.sortList):"",page:e?u.encodeHash(a,r,"page",a.pager.page+1):"",size:e?u.encodeHash(a,r,"size",a.pager.size):"",filter:h?u.encodeHash(a,r,"filter",h,i):""};g.each(l,function(e,t){s=u.cleanHash(a,r,e,s),o+=t});var d=r.sort2Hash_hash,c=((window.location.hash||"").replace(d,"").length?s:d)+o;if(r.sort2Hash_replaceHistory){var H=window.location.href.split(d)[0];c[0]!==d&&(c=d+c),window.location.replace(H+c)}else window.location.hash=c}};p.addWidget({id:"sort2Hash",priority:60,options:{sort2Hash_hash:"#",sort2Hash_separator:"-",sort2Hash_headerTextAttr:"data-header",sort2Hash_directionText:[0,1],sort2Hash_overrideSaveSort:!1,sort2Hash_replaceHistory:!1,sort2Hash_tableId:null,sort2Hash_encodeHash:null,sort2Hash_decodeHash:null,sort2Hash_cleanHash:null},init:function(e,t,a,r){u.init(a,r)},remove:function(e,t){t.$table.off(".sort2hash")}})}(jQuery);return jQuery;}));
