﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Net;
using System.IO;
using System.Data.Entity.Validation;
using System.Data.Entity;
using com.ecool.service;
using System.ComponentModel.DataAnnotations;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZ23Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ZZZ23";

        // GET: ZZZ23
        public ActionResult QUERY(ZZZ23IndexViewModel model)
        {
            if (model == null) model = new ZZZ23IndexViewModel();
            IQueryable<HRMT24> HRMT24List = db.HRMT24.AsQueryable();
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                HRMT24List = HRMT24List.Where(a => a.ROLE_NAME.Contains(model.whereKeyword.Trim()));
            }

            switch (model.OrdercColumn)
            {
                case "ROLE_ID":
                    HRMT24List = HRMT24List.OrderByDescending(a => a.ROLE_ID);
                    break;
                default:
                    HRMT24List = HRMT24List.OrderByDescending(a => a.ROLE_ID);
                    break;
            }

            model.HRMT24List = HRMT24List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 20);

            string UseYN = "N";
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "ADD", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableInsert = false;
            }
            else
            {
                ViewBag.VisableInsert = true;
            }
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "MODIFY", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableModify = false;
            }
            else
            {
                ViewBag.VisableModify = true;
            }

            return View(model);
        }

        [HttpPost]
        [Display(Name = "ROLE_ID")]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult MODIFY([Bind(Include = "ROLE_ID,ROLE_NAME,CHG_PERSON,CHG_DATE,ROLE_TYPE,ROLE_LEVEL")] HRMT24 hrmt24)
        {
            if (ModelState.IsValid == false) return View(hrmt24);

            HRMT24 oldhrmt24 = db.HRMT24.Find(hrmt24.ROLE_ID);
            if (hrmt24 == null)
            {
                return HttpNotFound();
            }

            oldhrmt24.ROLE_NAME = hrmt24.ROLE_NAME;
            oldhrmt24.CHG_DATE = DateTime.Now;

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "修改完成";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }
            return RedirectToAction("QUERY");

        }
        
        // GET: ZZZ23/MODIFY/5
        public ActionResult MODIFY(string ROLE_ID)
        {
            if (ROLE_ID == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            HRMT24 hrmt24 = db.HRMT24.Where(p => p.ROLE_ID == ROLE_ID.ToString()).FirstOrDefault();
            //ADDT15 aDDT15 = db.ADDT15.Find(id);
            if (hrmt24 == null)
            {
                return HttpNotFound();
            }
            return View(hrmt24);
        }

        [CheckPermission] //檢查權限
        public ActionResult INSERT()
        {
            return View();
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult INSERT([Bind(Include = "ROLE_ID,ROLE_NAME,CHG_PERSON,CHG_DATE,ROLE_TYPE,ROLE_LEVEL")] HRMT24 hrmt24)
        {

            //List<int> iRole_ID = (from db.HRMT24 SELECT )
            List<string> ltRole_ID = (from h24 in db.HRMT24
                                     select    h24.ROLE_ID ).ToList();
            int sRole_ID = ltRole_ID.Max(p => Convert.ToInt32(p));
            if (ModelState.IsValid)
            {
                hrmt24.ROLE_ID = (sRole_ID+1).ToString();
                hrmt24.CHG_PERSON = "1519";
                hrmt24.CHG_DATE = DateTime.Now;
                hrmt24.ROLE_TYPE = 3;
                hrmt24.ROLE_LEVEL = 5;


                db.HRMT24.Add(hrmt24);
                try
                {
                    TempData["StatusMessage"] = "新增完成";
                    db.SaveChanges();
                }
                catch (DbEntityValidationException ex)
                {
                    var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                    var getFullMessage = string.Join("; ", entityError);
                    var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
                }
                return RedirectToAction("Query");
            }
            return View(hrmt24);
        }

    }
}