﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'sv', {
	find: 'Sök',
	findOptions: 'Sökalternativ',
	findWhat: '<PERSON><PERSON>k efter:',
	matchCase: 'Skiftläge',
	matchCyclic: 'Matcha cykliska',
	matchWord: 'Inkludera hela ord',
	notFoundMsg: 'Angiven text kunde ej hittas.',
	replace: '<PERSON>rsätt',
	replaceAll: 'Ersätt alla',
	replaceSuccessMsg: '%1 förekomst(er) ersatta.',
	replaceWith: 'Ersätt med:',
	title: 'Sök och ersätt'
} );
