<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.WebUtilities</name>
    </assembly>
    <members>
        <member name="F:Microsoft.Extensions.WebEncoders.Sources.EncoderResources.WebEncoders_InvalidCountOffsetOrLength">
            <summary>
            Invalid {0}, {1} or {2} length.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.WebEncoders.Sources.EncoderResources.WebEncoders_MalformedInput">
            <summary>
            Malformed input: {0} is an invalid input length.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.WebEncoders.Sources.EncoderResources.FormatWebEncoders_InvalidCountOffsetOrLength(System.Object,System.Object,System.Object)">
            <summary>
            Invalid {0}, {1} or {2} length.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.WebEncoders.Sources.EncoderResources.FormatWebEncoders_MalformedInput(System.Object)">
            <summary>
            Malformed input: {0} is an invalid input length.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.WebEncoders">
            <summary>
            Contains utility APIs to assist with common encoding and decoding operations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlDecode(System.String)">
            <summary>
            Decodes a base64url-encoded string.
            </summary>
            <param name="input">The base64url-encoded input to decode.</param>
            <returns>The base64url-decoded form of the input.</returns>
            <remarks>
            The input must not contain any whitespace or padding characters.
            Throws <see cref="T:System.FormatException"/> if the input is malformed.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlDecode(System.String,System.Int32,System.Int32)">
            <summary>
            Decodes a base64url-encoded substring of a given string.
            </summary>
            <param name="input">A string containing the base64url-encoded input to decode.</param>
            <param name="offset">The position in <paramref name="input"/> at which decoding should begin.</param>
            <param name="count">The number of characters in <paramref name="input"/> to decode.</param>
            <returns>The base64url-decoded form of the input.</returns>
            <remarks>
            The input must not contain any whitespace or padding characters.
            Throws <see cref="T:System.FormatException"/> if the input is malformed.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlDecode(System.String,System.Int32,System.Char[],System.Int32,System.Int32)">
            <summary>
            Decodes a base64url-encoded <paramref name="input"/> into a <c>byte[]</c>.
            </summary>
            <param name="input">A string containing the base64url-encoded input to decode.</param>
            <param name="offset">The position in <paramref name="input"/> at which decoding should begin.</param>
            <param name="buffer">
            Scratch buffer to hold the <see cref="T:System.Char"/>s to decode. Array must be large enough to hold
            <paramref name="bufferOffset"/> and <paramref name="count"/> characters as well as Base64 padding
            characters. Content is not preserved.
            </param>
            <param name="bufferOffset">
            The offset into <paramref name="buffer"/> at which to begin writing the <see cref="T:System.Char"/>s to decode.
            </param>
            <param name="count">The number of characters in <paramref name="input"/> to decode.</param>
            <returns>The base64url-decoded form of the <paramref name="input"/>.</returns>
            <remarks>
            The input must not contain any whitespace or padding characters.
            Throws <see cref="T:System.FormatException"/> if the input is malformed.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.GetArraySizeRequiredToDecode(System.Int32)">
            <summary>
            Gets the minimum <c>char[]</c> size required for decoding of <paramref name="count"/> characters
            with the <see cref="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlDecode(System.String,System.Int32,System.Char[],System.Int32,System.Int32)"/> method.
            </summary>
            <param name="count">The number of characters to decode.</param>
            <returns>
            The minimum <c>char[]</c> size required for decoding  of <paramref name="count"/> characters.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlEncode(System.Byte[])">
            <summary>
            Encodes <paramref name="input"/> using base64url encoding.
            </summary>
            <param name="input">The binary input to encode.</param>
            <returns>The base64url-encoded form of <paramref name="input"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlEncode(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Encodes <paramref name="input"/> using base64url encoding.
            </summary>
            <param name="input">The binary input to encode.</param>
            <param name="offset">The offset into <paramref name="input"/> at which to begin encoding.</param>
            <param name="count">The number of bytes from <paramref name="input"/> to encode.</param>
            <returns>The base64url-encoded form of <paramref name="input"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlEncode(System.Byte[],System.Int32,System.Char[],System.Int32,System.Int32)">
            <summary>
            Encodes <paramref name="input"/> using base64url encoding.
            </summary>
            <param name="input">The binary input to encode.</param>
            <param name="offset">The offset into <paramref name="input"/> at which to begin encoding.</param>
            <param name="output">
            Buffer to receive the base64url-encoded form of <paramref name="input"/>. Array must be large enough to
            hold <paramref name="outputOffset"/> characters and the full base64-encoded form of
            <paramref name="input"/>, including padding characters.
            </param>
            <param name="outputOffset">
            The offset into <paramref name="output"/> at which to begin writing the base64url-encoded form of
            <paramref name="input"/>.
            </param>
            <param name="count">The number of <c>byte</c>s from <paramref name="input"/> to encode.</param>
            <returns>
            The number of characters written to <paramref name="output"/>, less any padding characters.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.GetArraySizeRequiredToEncode(System.Int32)">
            <summary>
            Get the minimum output <c>char[]</c> size required for encoding <paramref name="count"/>
            <see cref="T:System.Byte"/>s with the <see cref="M:Microsoft.AspNetCore.WebUtilities.WebEncoders.Base64UrlEncode(System.Byte[],System.Int32,System.Char[],System.Int32,System.Int32)"/> method.
            </summary>
            <param name="count">The number of characters to encode.</param>
            <returns>
            The minimum output <c>char[]</c> size required for encoding <paramref name="count"/> <see cref="T:System.Byte"/>s.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.Base64UrlTextEncoder.Encode(System.Byte[])">
            <summary>
            Encodes supplied data into Base64 and replaces any URL encodable characters into non-URL encodable
            characters.
            </summary>
            <param name="data">Data to be encoded.</param>
            <returns>Base64 encoded string modified with non-URL encodable characters</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.Base64UrlTextEncoder.Decode(System.String)">
            <summary>
            Decodes supplied string by replacing the non-URL encodable characters with URL encodable characters and
            then decodes the Base64 string.
            </summary>
            <param name="text">The string to be decoded.</param>
            <returns>The decoded data.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.BufferedReadStream">
            <summary>
            A Stream that wraps another stream and allows reading lines.
            The data is buffered in memory.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Creates a new stream.
            </summary>
            <param name="inner">The stream to wrap.</param>
            <param name="bufferSize">Size of buffer in bytes.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.#ctor(System.IO.Stream,System.Int32,System.Buffers.ArrayPool{System.Byte})">
            <summary>
            Creates a new stream.
            </summary>
            <param name="inner">The stream to wrap.</param>
            <param name="bufferSize">Size of buffer in bytes.</param>
            <param name="bytePool">ArrayPool for the buffer.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.BufferedData">
            <summary>
            The currently buffered data.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.CanRead">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.CanSeek">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.CanTimeout">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.CanWrite">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.Length">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.Position">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.SetLength(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.Flush">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.FlushAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.Write(System.Byte[],System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.Read(System.Byte[],System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.EnsureBuffered">
            <summary>
            Ensures that the buffer is not empty.
            </summary>
            <returns>Returns <c>true</c> if the buffer is not empty; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.EnsureBufferedAsync(System.Threading.CancellationToken)">
            <summary>
            Ensures that the buffer is not empty.
            </summary>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Returns <c>true</c> if the buffer is not empty; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.EnsureBuffered(System.Int32)">
            <summary>
            Ensures that a minimum amount of buffered data is available.
            </summary>
            <param name="minCount">Minimum amount of buffered data.</param>
            <returns>Returns <c>true</c> if the minimum amount of buffered data is available; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.EnsureBufferedAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Ensures that a minimum amount of buffered data is available.
            </summary>
            <param name="minCount">Minimum amount of buffered data.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Returns <c>true</c> if the minimum amount of buffered data is available; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.ReadLine(System.Int32)">
            <summary>
            Reads a line. A line is defined as a sequence of characters followed by
            a carriage return immediately followed by a line feed. The resulting string does not
            contain the terminating carriage return and line feed.
            </summary>
            <param name="lengthLimit">Maximum allowed line length.</param>
            <returns>A line.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.BufferedReadStream.ReadLineAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads a line. A line is defined as a sequence of characters followed by
            a carriage return immediately followed by a line feed. The resulting string does not
            contain the terminating carriage return and line feed.
            </summary>
            <param name="lengthLimit">Maximum allowed line length.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A line.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.FileBufferingReadStream">
            <summary>
            A Stream that wraps another stream and enables rewinding by buffering the content as it is read.
            The content is buffered in memory up to a certain size and then spooled to a temp file on disk.
            The temp file will be deleted on Dispose.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.FileMultipartSection">
            <summary>
            Represents a file multipart section
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FileMultipartSection.#ctor(Microsoft.AspNetCore.WebUtilities.MultipartSection)">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.AspNetCore.WebUtilities.FileMultipartSection"/> class
            </summary>
            <param name="section">The section from which to create the <see cref="T:Microsoft.AspNetCore.WebUtilities.FileMultipartSection"/></param>
            <remarks>Reparses the content disposition header</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FileMultipartSection.#ctor(Microsoft.AspNetCore.WebUtilities.MultipartSection,Microsoft.Net.Http.Headers.ContentDispositionHeaderValue)">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.AspNetCore.WebUtilities.FileMultipartSection"/> class
            </summary>
            <param name="section">The section from which to create the <see cref="T:Microsoft.AspNetCore.WebUtilities.FileMultipartSection"/></param>
            <param name="header">An already parsed content disposition header</param>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FileMultipartSection.Section">
            <summary>
            Gets the original section from which this object was created
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FileMultipartSection.FileStream">
            <summary>
            Gets the file stream from the section body
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FileMultipartSection.Name">
            <summary>
            Gets the name of the section
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FileMultipartSection.FileName">
            <summary>
            Gets the name of the file from the section
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.FormMultipartSection">
            <summary>
            Represents a form multipart section
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FormMultipartSection.#ctor(Microsoft.AspNetCore.WebUtilities.MultipartSection)">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.AspNetCore.WebUtilities.FormMultipartSection"/> class
            </summary>
            <param name="section">The section from which to create the <see cref="T:Microsoft.AspNetCore.WebUtilities.FormMultipartSection"/></param>
            <remarks>Reparses the content disposition header</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FormMultipartSection.#ctor(Microsoft.AspNetCore.WebUtilities.MultipartSection,Microsoft.Net.Http.Headers.ContentDispositionHeaderValue)">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.AspNetCore.WebUtilities.FormMultipartSection"/> class
            </summary>
            <param name="section">The section from which to create the <see cref="T:Microsoft.AspNetCore.WebUtilities.FormMultipartSection"/></param>
            <param name="header">An already parsed content disposition header</param>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FormMultipartSection.Section">
            <summary>
            Gets the original section from which this object was created
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FormMultipartSection.Name">
            <summary>
            The form name
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FormMultipartSection.GetValueAsync">
            <summary>
            Gets the form value
            </summary>
            <returns>The form value</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.FormReader">
            <summary>
            Used to read an 'application/x-www-form-urlencoded' form.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FormReader.ValueCountLimit">
            <summary>
            The limit on the number of form values to allow in ReadForm or ReadFormAsync.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FormReader.KeyLengthLimit">
            <summary>
            The limit on the length of form keys.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.FormReader.ValueLengthLimit">
            <summary>
            The limit on the length of form values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FormReader.ReadNextPair">
            <summary>
            Reads the next key value pair from the form.
            For unbuffered data use the async overload instead.
            </summary>
            <returns>The next key value pair, or null when the end of the form is reached.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FormReader.ReadNextPairAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next key value pair from the form.
            </summary>
            <param name="cancellationToken"></param>
            <returns>The next key value pair, or null when the end of the form is reached.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FormReader.ReadForm">
            <summary>
            Parses text from an HTTP form body.
            </summary>
            <returns>The collection containing the parsed HTTP form body.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.FormReader.ReadFormAsync(System.Threading.CancellationToken)">
            <summary>
            Parses an HTTP form body.
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/>.</param>
            <returns>The collection containing the parsed HTTP form body.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.HttpResponseStreamWriter">
            <summary>
            Writes to the <see cref="T:System.IO.Stream"/> using the supplied <see cref="P:Microsoft.AspNetCore.WebUtilities.HttpResponseStreamWriter.Encoding"/>.
            It does not write the BOM and also does not close the stream.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.MultipartReader.HeadersCountLimit">
            <summary>
            The limit for the number of headers to read.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.MultipartReader.HeadersLengthLimit">
            <summary>
            The combined size limit for headers per multipart section.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.MultipartReader.BodyLengthLimit">
            <summary>
            The optional limit for the total response body length.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.MultipartReaderStream.#ctor(Microsoft.AspNetCore.WebUtilities.BufferedReadStream,Microsoft.AspNetCore.WebUtilities.MultipartBoundary)">
            <summary>
            Creates a stream that reads until it reaches the given boundary pattern.
            </summary>
            <param name="stream">The <see cref="T:Microsoft.AspNetCore.WebUtilities.BufferedReadStream"/>.</param>
            <param name="boundary">The boundary pattern to use.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.MultipartReaderStream.#ctor(Microsoft.AspNetCore.WebUtilities.BufferedReadStream,Microsoft.AspNetCore.WebUtilities.MultipartBoundary,System.Buffers.ArrayPool{System.Byte})">
            <summary>
            Creates a stream that reads until it reaches the given boundary pattern.
            </summary>
            <param name="stream">The <see cref="T:Microsoft.AspNetCore.WebUtilities.BufferedReadStream"/>.</param>
            <param name="boundary">The boundary pattern to use.</param>
            <param name="bytePool">The ArrayPool pool to use for temporary byte arrays.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.MultipartSection.BaseStreamOffset">
            <summary>
            The position where the body starts in the total multipart body.
            This may not be available if the total multipart body is not seekable.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.MultipartSectionConverterExtensions">
            <summary>
            Various extensions for converting multipart sections
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.MultipartSectionConverterExtensions.AsFileSection(Microsoft.AspNetCore.WebUtilities.MultipartSection)">
            <summary>
            Converts the section to a file section
            </summary>
            <param name="section">The section to convert</param>
            <returns>A file section</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.MultipartSectionConverterExtensions.AsFormDataSection(Microsoft.AspNetCore.WebUtilities.MultipartSection)">
            <summary>
            Converts the section to a form section
            </summary>
            <param name="section">The section to convert</param>
            <returns>A form section</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.MultipartSectionConverterExtensions.GetContentDispositionHeader(Microsoft.AspNetCore.WebUtilities.MultipartSection)">
            <summary>
            Retrieves and parses the content disposition header from a section
            </summary>
            <param name="section">The section from which to retrieve</param>
            <returns>A <see cref="T:Microsoft.Net.Http.Headers.ContentDispositionHeaderValue"/> if the header was found, null otherwise</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.MultipartSectionStreamExtensions">
            <summary>
            Various extension methods for dealing with the section body stream
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.MultipartSectionStreamExtensions.ReadAsStringAsync(Microsoft.AspNetCore.WebUtilities.MultipartSection)">
            <summary>
            Reads the body of the section as a string
            </summary>
            <param name="section">The section to read from</param>
            <returns>The body steam as string</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.QueryHelpers.AddQueryString(System.String,System.String,System.String)">
            <summary>
            Append the given query key and value to the URI.
            </summary>
            <param name="uri">The base URI.</param>
            <param name="name">The name of the query key.</param>
            <param name="value">The query value.</param>
            <returns>The combined result.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.QueryHelpers.AddQueryString(System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Append the given query keys and values to the uri.
            </summary>
            <param name="uri">The base uri.</param>
            <param name="queryString">A collection of name value query pairs to append.</param>
            <returns>The combined result.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(System.String)">
            <summary>
            Parse a query string into its component key and value parts.
            </summary>
            <param name="queryString">The raw query string value, with or without the leading '?'.</param>
            <returns>A collection of parsed keys and values.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseNullableQuery(System.String)">
            <summary>
            Parse a query string into its component key and value parts.
            </summary>
            <param name="queryString">The raw query string value, with or without the leading '?'.</param>
            <returns>A collection of parsed keys and values, null if there are no entries.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.WebUtilities.Resources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.Resources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.Resources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.Resources.HttpRequestStreamReader_StreamNotReadable">
            <summary>
               Looks up a localized string similar to The stream must support reading..
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.Resources.HttpResponseStreamWriter_StreamNotWritable">
            <summary>
               Looks up a localized string similar to The stream must support writing..
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.WebUtilities.Resources.WebEncoders_InvalidCountOffsetOrLength">
            <summary>
               Looks up a localized string similar to Invalid {0}, {1} or {2} length..
            </summary>
        </member>
    </members>
</doc>
