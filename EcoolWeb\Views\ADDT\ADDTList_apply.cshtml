﻿@using System.Collections;
@using ECOOL_APP.com.ecool.Models.entity;
@{
    ViewBag.Title = "閱讀認證-我要申請閱讀認證";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string BookID = ViewBag.BookID;
    bool BookStatus = (ViewBag.BookStatus == "ALL") ? true : false;
    string ReadrppBookCheckF = (BookID == null) ? "selected" : "";
    string ReadrppBookCheckT = (BookID != null) ? "selected" : "";
    var Book_Info = from Book in this.Model
                    where Book.BOOK_ID == BookID
                    select Book;
    string Book_Name = (Book_Info.FirstOrDefault() != null) ? Book_Info.FirstOrDefault().BOOK_NAME : string.Empty;

    ADDT06 AT06 = TempData["TempAT06"] as ADDT06;
    if (AT06 == null)
    {
        AT06 = new ADDT06();
    }
    else
    {
        Book_Name = AT06.BOOK_NAME;
    }
}
@model IEnumerable<uADDT03>

@Html.Partial("_Title_Secondary")

<div style="width:600px">
    @{string Explain = ViewBag.ADDT06SEXPLAIN;}
    @Html.Raw(HttpUtility.HtmlDecode(@Explain))
</div>

@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "ADDTList_apply" });
}

@using (Html.BeginForm("ADDTList_apply", "ADDT06", FormMethod.Post, new { name = "contentForm", id = "contentForm", enctype = "multipart/form-data" }))
{
    @Html.Hidden("TempSave")
    <img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.Label("是否為閱讀護照書籍", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <select id="ddlReadrppBook" name="ddlReadrppBook" onchange="ddlReadrppBook_onchange();" class="form-control input-sm">
                        <option value="N" @ReadrppBookCheckF>否</option>
                        <option value="Y" @ReadrppBookCheckT>是</option>
                    </select>
                </div>
            </div>
            <div class="form-group" id="sgradebook" style="display: none;">
                @Html.Label("1~6年級書", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    @Html.CheckBox("gradebook", BookStatus, new { onclick = "gradebook_click()" })
                </div>
            </div>

            @if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
            {
                <div class="form-group">
                    @Html.Label("班級", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        @if (ViewBag.ClassItems != null)
                        {
                            @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeClass_No();" })
                        }
                        else
                        {
                            <select>
                                <option></option>
                            </select>
                        }
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("姓名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        @if (ViewBag.USER_NOItems != null)
                        {
                            @Html.DropDownList("USER_NO", (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
                        }
                        else
                        {
                            <select>
                                <option></option>
                            </select>
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="form-group">
                    @Html.Label("學號", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        <samp class="dd">
                            @(user != null ? user.USER_NO : "")
                            @Html.Hidden("Class_No", user?.CLASS_NO)
                            @Html.Hidden("USER_NO", user?.USER_NO)
                        </samp>
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("姓名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        <samp class="dd">
                            @(user != null ? user.NAME : "")
                        </samp>
                    </div>
                </div>
            }
            <div class="form-group">
                @Html.Label("閱讀書名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        <input type="text" id="txtBOOK_NAME" name="txtBOOK_NAME" value="@Book_Name" class="form-control input-sm" maxlength="50" />
                        @Html.DropDownList("BOOK_ID", (IEnumerable<SelectListItem>)ViewBag.BookList, "選擇書名", new { onchange = "ddlBOOK_NAME_onchange();", @class = "form-control input-sm" })
                        @Html.Hidden("BOOK_NAME", Book_Name)
                        <input type="hidden" id="hidBOOK_NAME" name="hidBOOK_NAME">
                    </samp>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12">
                    詳細內容:限制6000字，目前字數為<span id="ShowFontLen" style="color:red"></span>字(如果沒有上傳圖片，心得必須20個字以上)
                </label>
                <div class="col-md-12 col-sm-12 col-lg-12">
                    @Html.TextArea("txtARTICLE", AT06.REVIEW, new { rows = "15", @class = "form-control" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("上傳圖片:(請上傳6MB以下的jpg檔)", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <input type="radio" id="radupdatepicY" name="radupdatepic" value="Y" onclick="UpdateLoadStatus();" /><label>是</label>
                    <input type="radio" id="radupdatepicN" name="radupdatepic" value="N" onclick="UpdateLoadStatus();" checked /><label>否</label>
                    <samp id="TRupdateload" style="display: none;">
                        @Html.Action("Upload", "Comm")
                    </samp>
                </div>
            </div>

            <div class="form-group Div-btn-center">

                <div class="col-md-12">
                    <button class="btn btn-default" type="button" name="btnSendTempSave" onclick="btnSend_onclick(true);">
                        暫存草稿
                    </button>
                    <button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick(false);">
                        確定送出
                    </button>
                    <button role="button" class="btn btn-default" type="button" id="btnCancel" onclick="btnCancel_onclick();">
                        放棄編輯
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myLargeDetailsModalLabel" id="DivDetailsModal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">增加認證老師</h4>
                </div>

                <div class="modal-body" id="DetailsModal">
                </div>
            </div><!-- /.modal-content -->
        </div>
    </div>

    <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">提醒您，需注意下面內容</h4>
                </div>
                <div class="modal-body" id="remind-content">

                    @if (ViewBag.RemindItems != null)
                    {

                        <div class="row">
                            <div class="col-xs-2 text-right"> <img src="~/Content/img/Prohibition_plagiarism@0,25x.png" style="width:100px;height:auto" /></div>
                            <div class="col-xs-8">
                                <ol style="font-size:25px">

                                    @foreach (var item in ViewBag.RemindItems as List<BDMT02_REF>)
                                    {
                                        @item.CONTENT_TXT
                                        <br />

                                    }
                                </ol>
                            </div>
                            @if (!AppMode)
                            {
                                <div class="col-xs-2  "> <img src="~/Content/img/Copyright@0,25x.png" style="width:100px;height:auto" /></div>
                            }
                            else
                            {
                                <div class="col-xs-2  " style="margin-left: 200px;"> <img src="~/Content/img/Copyright@0,25x.png" style="width:70px;height:auto" /></div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

@section Scripts
{
    <script type="text/javascript">
    $(function () {
        ddlReadrppBook_onchange();

        $('#txtARTICLE').on("keyup", KeyIn).on("change", KeyIn);

    });

    window.onload = function () {
        KeyIn()
        RemindShow()
        }

        function RemindShow() {
            var remind_font = $("#remind-content").text().length;

            if (remind_font>0) {
                $('#remind-modal').modal('show');
            }
        }

        function KeyIn() {
            var NowTextLen = $("#txtARTICLE").val().length;
           $("#ShowFontLen").text(NowTextLen);
        }

    function SetClass_NoDDLEmpty() {
        $('#USER_NO').empty();
        $('#USER_NO').append($('<option></option>').val('').text('請選擇學生'));
    }

    function ChangeClass_No() {
        var selectedClass_No = $.trim($('#Class_No option:selected').val());

        if (selectedClass_No.length == 0) {
            SetClass_NoDDLEmpty();
        }
        else {
            $.getJSON('@Url.Action("GetNameData")', { Class_No: selectedClass_No }, function (data) {
                    $('#USER_NO').empty();
                    $.each(data, function (i, item) {
                        $('#USER_NO').append($('<option></option>').val(item.Value).text(item.Text));
                    });
                });
            }
        }

        function gradebook_click() {
            $.getJSON('@Url.Action("GetBookNameData")', { GRADEBOOK: $("#gradebook").prop("checked") }, function (data) {
                $('#BOOK_ID').empty();
                $.each(data, function (i, item) {
                    $('#BOOK_ID').append($('<option></option>').val(item.Value).text(item.Text));
                });
            });
            if ($("#gradebook").prop("checked") == true) {
                //被勾選做什麼事
                //alert($("#gradebook").prop("checked"));

            }
            else {
                //沒被勾選做什麼事
                //alert($("#gradebook").prop('checked'));
            }
        }

        //抓取閱讀護照上所選到的書名
        function ddlBOOK_NAME_onchange() {
            $("#BOOK_NAME").val($("#BOOK_ID option:selected").text());
        }

        //設定是否讀取閱讀護照書籍
        function ddlReadrppBook_onchange() {
            try {

                if ($('select[name="ddlReadrppBook"]').val() == 'N') {
                    $('#txtBOOK_NAME').show();
                    $('#BOOK_ID').hide();
                    $('#sgradebook').hide();
                }
                else {
                    $('#txtBOOK_NAME').hide();
                    $('#BOOK_ID').show();
                    $('#sgradebook').show();
                }
            }
            catch (err) {
            }

            //document.contentForm.action = "../../Account/Account";
            //document.contentForm.submit();
        }

        function UpdateLoadStatus() {
            try {
                if ($('input[name="radupdatepic"][value="Y"]').is(':checked') == true) {
                    $('#TRupdateload').show();
                }
                else {
                    $('#TRupdateload').hide();
                }
            }
            catch (err) {
            }
        }

        function btnCancel_onclick() {
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "../ADDT/ADDTList";
            document.contentForm.submit();
        }

        function btnSend_onclick(BoolVal) {
            var strMsg = '';
            try {

                if ($('select[name="ddlReadrppBook"]').val() == 'Y' && $('select[name="BOOK_ID"]').val() == '') {
                    strMsg += '請選擇閱讀書名\r\n';
                }
                if ($('select[name="ddlReadrppBook"]').val() == 'N' && $('#txtBOOK_NAME').val() == '' && $("#txtBOOK_NAME").val().trim().length == 0) {
                    strMsg += '閱讀書名為必填\r\n';
                }

                if ($('input[name="radupdatepic"][value="Y"]').is(':checked') == true) {

                    if ($('#file').val() == '')
                    {
                        strMsg += '上傳檔案需為必填\r\n';
                    }
                }

                else {
                    if ($('#txtARTICLE').val() == '' || $('#txtARTICLE').val().trim() == '' || $('#txtARTICLE').val().trim().length<20) {
                        strMsg += '如果沒有上傳圖片，心得必須20個字以上\r\n';
                    }
                }

                 if ($('#USER_NO').val() == '') {
                        strMsg += '姓名必選\r\n';
                    }

                if ($('#txtARTICLE').val().length >= 6000) {
                    strMsg += '心得字數超過6000\r\n';
                }

                if (strMsg != '') {
                    alert(strMsg);
                    return;
                }
                else {
                    $('#TempSave').val(BoolVal)
                    document.contentForm.enctype = "multipart/form-data";
                    document.contentForm.action = "reader_applyInsert";
                    document.contentForm.submit();
                }
            }
            catch (err) {
            }
        }
    </script>
}