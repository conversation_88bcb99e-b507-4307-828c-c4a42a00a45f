﻿@model ECOOL_APP.com.ecool.Models.DTO.Menu

@{
    ECOOL_APP.UserProfile user = ViewBag.user;
    int CASH_NextChance = 0;
    int Chance_ARRIVED_CASH = 0;
    string Grade_desc = "";
    string NAME = "";
    string USER_TYPE_DESC = "";
    int CASH = 0;
    string USER_TYPE = "";
    string USER_NO = "";
    if (user != null)
    {
        CASH_NextChance = user.CASH_NextChance;
        Grade_desc = user.GRADE_DESC;
        NAME = user.NAME;
        USER_TYPE_DESC = user.USER_TYPE_DESC;
        CASH = user.CASH;
        Chance_ARRIVED_CASH = user.Chance_ARRIVED_CASH;
        USER_TYPE = user.USER_TYPE;
        USER_NO = user.USER_NO;
    }
}

<!--[if lte IE 9 ]>
     <style type="text/css">
        .ADDselector {
            filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(
            src='@Url.Content("~/Content/img/web-student_allpage-04-body.png")',
            sizingMethod='scale');

            -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(
            src='@Url.Content("~/Content/img/web-student_allpage-04-body.png")',
            sizingMethod='scale')";

        }

            .AWAselector {
            filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(
            src='@Url.Content("~/Content/img/web-student_allpage-05-body.png")',
            sizingMethod='scale');

            -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(
            src='@Url.Content("~/Content/img/web-student_allpage-05-body.png")',
            sizingMethod='scale')";
        }

                .SARselector {
            filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(
            src='@Url.Content("~/Content/img/web-student_allpage-06-body.png")',
            sizingMethod='scale');

            -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(
            src='@Url.Content("~/Content/img/web-student_allpage-06-body.png")',
            sizingMethod='scale')";
        }
</style>
<![endif]-->
<!--[if !IE]><!-->
<style type="text/css">
          .ADDselector {
        background-image:url('@Url.Content("~/Content/img/web-student_allpage-04-body.png")');
        }

      .AWAselector {
        background-image:url('@Url.Content("~/Content/img/web-student_allpage-05-body.png")');
        }

    .SARselector {
        background-image:url('@Url.Content("~/Content/img/web-student_allpage-06-body.png")');
        }

</style>
<style>
    .dropdown {
        position: relative;
        display: inline-block;
    }

    .dropdown-button {
        font-weight: bold;
        position: relative;
        display: block;
        padding: 10px 15px;
        letter-spacing: 2px;
        background-color: transparent;
        border: none;
    }

    .dropdown-menu {
        display: none;
        position: absolute;
        background-color: white;
        min-width: 200px;
        border: 1px solid #ccc;
        z-index: 1000;
        margin-top: 4px;
        border-radius: 4px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

        .dropdown-menu a {
            color: #333;
            padding: 10px 14px;
            text-decoration: none;
            display: block;
        }

            .dropdown-menu a:hover {
                background-color: #f1f1f1;
            }

    .show {
        display: block;
    }
    .caret-small {
        font-size: 7px;
        vertical-align: middle;
        padding-left: 1px;
    }
</style>
<!--<![endif]-->
<!-- 手機-->
<div class="collapse navbar-ECOOL navbar-ex1-collapse">

    @if (ViewBag.user != null)
    {
        <div>
            <div style="height:15px"></div>
            @if (USER_TYPE == ECOOL_APP.EF.UserType.Student)
            {

                <table border="0">
                    @if (USER_TYPE == ECOOL_APP.EF.UserType.Student)
                    {
                        <tr>
                            <td class="navbar-ClassCach Title" nowrap="nowrap" height="71px">
                                @if (Chance_ARRIVED_CASH > 0)
                                {
                                    <a href='@Url.Action("ArrivedChance2", "Home")'>
                                        <div class="ChanceQty">
                                            @Chance_ARRIVED_CASH
                                        </div>
                                    </a>
                                }
                                else
                                {
                                    <div class="ChanceQty">0</div>
                                }
                                <div class="NextChance">@CASH_NextChance</div>
                            </td>
                        </tr>
                    }
                </table>
                <br />

                <img src="~/Content/img/web-revise-money0309-07.png" style="max-height:30px" />
                <b class="MyName">
                    @Grade_desc
                    &nbsp;
                    @NAME
                    @USER_TYPE_DESC
                    ，酷幣點數：@CASH



                </b>
            }
            else
            {
                <b class="MyName">
                    @Grade_desc
                    &nbsp;
                    @NAME
                    @USER_TYPE_DESC
                    @if (USER_TYPE != ECOOL_APP.EF.UserType.Admin && USER_TYPE != ECOOL_APP.EF.UserType.Parents)
                    {
                        <samp>，酷幣點數：@CASH </samp>
                    }
                </b>
            }
        </div>
        <hr />
    }
    
        <ul class="navPhone navbar-nav">
            @if (Model.SEC.Count() > 0 && USER_NO != "stage")
            {
                <li class="dropdown">
                    <button class="fa fa-dashboard iconPhone dropdown-button" onclick="toggleDropdown()">我的秘書 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.SEC)
                        {
                
                        <a class="dropdown-item" href='@Url.Action(ITEM.ACTION_ID, ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">
                            @ITEM.BRE_NAME
                        </a>
                  
                         }


                    </div>
                </li>

       
            }

            @if (Model.ADD.Count() > 0)
            {
                
                
               
                  <li class="dropdown">
                      <button class="fa fa-edit iconPhone dropdown-button" onclick="toggleDropdown()">加值應用 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.ADD)
                        {
                            
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                          
                        }


                    </div>
                </li>


            }

            @if (Model.AWA.Count() > 0 && USER_NO != "stage")
            {
              <li class="dropdown">
                      <button class="glyphicon glyphicon-gift iconPhone dropdown-button" onclick="toggleDropdown()">加值應用 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.AWA)
                        {
                            
                              <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                      
                        }


                    </div>
                </li>

            }

            @if (Model.SAR.Count() > 0 && USER_NO != "stage")
            {
               <li class="dropdown">
                      <button class="fa fa-bar-chart-o iconPhone dropdown-button" onclick="toggleDropdown()">統計與排行 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.SAR)
                        {
                            
                              <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                      
                        }


                    </div>
                </li>

            }

            @if (Model.CAR.Count() > 0 && USER_NO != "stage")
            {
               
                 <li class="dropdown">
                      <button class="glyphicon glyphicon-gift iconPhone dropdown-button" onclick="toggleDropdown()">全校能力認證 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.CAR)
                        {
                            
                              <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                      
                        }


                    </div>
                </li>

            }
            @if (Model.CER.Count() > 0 && USER_NO != "stage")
            {
           
                
                 <li class="dropdown">
                      <button class="glyphicon glyphicon-gift iconPhone dropdown-button" onclick="toggleDropdown()">能力認證 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.CER)
                        {
                            
                              <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                      
                        }


                    </div>
                </li>
            }

            @if (Model.COO.Count() > 0 && USER_NO != "stage")
            {
                
                  <li class="dropdown">
                      <button class="fa fa-dashboard iconPhone dropdown-button" onclick="toggleDropdown()">酷嗶嗶嗶 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.COO)
                        {
                            
                              <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                      
                        }


                    </div>
                </li>
            }

            @if (Model.ZZZ.Count() > 0 && USER_NO != "stage")
            {
               
               <li class="dropdown">
                      <button class="glyphicon glyphicon-cog iconPhone dropdown-button" onclick="toggleDropdown()">維運管理 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.ZZZ)
                        {
                            
                              <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                      
                        }


                    </div>
                </li>
               
            }

            @if (Model.ACC.Count() > 0 && USER_NO != "stage")
            {
            
                  <li class="dropdown">
                      <button class="glyphicon glyphicon-user iconPhone dropdown-button" onclick="toggleDropdown()">帳號管理 <span class="caret-small">▼</span></button>
                    <div id="dropdownContent" class="dropdown-menu">
                        @foreach (var ITEM in Model.ACC)
                        {
                            
                              <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                      
                        }


                    </div>
                </li>
            }
        </ul>
    <div style="height:10px"></div>
</div><!-- /.navbar-collapse -->
<!-- pc-->
<div class="hidden-xs">
    @if (ViewBag.user == null)
    {
        @Html.Partial("../Shared/_Login")
    }

    <table border="0" cellspacing="0" cellpadding="0" align="center" style="width:100%;max-width:179px">

        @if (Model.SEC.Count() > 0 && USER_NO != "stage")
        {

            <tr>
                <td align="center">
                    <img src="~/Content/img/web-revise-secretary-seo.png" class="imgMenu" />
                    <div class="td_Menu_red">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.SEC)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            }
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>

        }

        @if (Model.ADD.Count() > 0)
        {
            <tr>
                <td>
                    <img src="~/Content/img/web-student_allpage-04-top.png" class="imgMenu" />
                </td>
            </tr>
            <tr>
                @{
                    string str = "";
                    str = "font_Menu ADDselector";
                    string stylestr = "";

                }
                @if (USER_NO == "stage")
                {
                    str = "font_Menu ADDselector";
                    stylestr = "line-height: 1px;";
                }

                <td align="center">
                    <div class="@str" style="@stylestr">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.ADD)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                if (USER_NO == "stage")
                                {
                                    if (ITEM.BRE_NO == "ADDI12")
                                    {

                                        <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>

                                    }
                                }
                                else
                                {

                                    <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                                }

                            }
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <img src="~/Content/img/web-student_allpage-04-footer.png" style="vertical-align:top" class="imgMenu" />
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>
        }

        @if (Model.AWA.Count() > 0 && USER_NO != "stage")
        {

            <tr>
                <td>
                    <img src="~/Content/img/web-student_allpage-05-top.png" class="imgMenu" />
                </td>
            </tr>
            <tr>
                <td class="font_Menu AWAselector" align="center">
                    @{
                        int num = 0;

                        foreach (var ITEM in Model.AWA)
                        {
                            num++;
                            if (num > 1)
                            {
                                <br />
                            }
                            <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                        }
                    }
                </td>
            </tr>
            <tr>
                <td>
                    <img src="~/Content/img/web-student_allpage-05-footer.png" style="vertical-align:top" class="imgMenu" />
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>
        }

        @if (Model.SAR.Count() > 0 && USER_NO != "stage")
        {

            <tr>
                <td>
                    <img src="~/Content/img/web-student_allpage-06-top.png" class="imgMenu" />
                </td>
            </tr>
            <tr>
                <td class="font_Menu SARselector" align="center">
                    @{
                        int num = 0;

                        foreach (var ITEM in Model.SAR)
                        {
                            num++;
                            if (num > 1)
                            {
                                <br />
                            }
                            <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                        }
                    }
                </td>
            </tr>
            <tr>
                <td>
                    <img src="~/Content/img/web-student_allpage-06-footer.png" style="vertical-align:top" class="imgMenu" />
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>
        }

        @if (Model.CER.Count() > 0 && USER_NO != "stage")
        {

            <tr>
                <td align="center">
                    <img src="~/Content/img/web-revise-secretary-Cert.png" class="imgMenu" />
                    <div class="td_Menu_green">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.CER)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            }
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>

        }
        @if (Model.CAR.Count() > 0 && USER_NO != "stage")
        {

            <tr>
                <td align="center">
                    <img src="~/Content/img/web-revise-secretary-Cart.png" class="imgMenu" />
                    <div class="td_Menu_green">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.CAR)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            }
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>

        }

        @if (Model.COO.Count() > 0 && USER_NO != "stage")
        {

            <tr>
                <td align="center">
                    <img src="~/Content/img/web-revise-secretary-Coo.png" class="imgMenu" />
                    <div class="td_Menu_Coo">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.COO)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            }
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>

        }

        @if (Model.ZZZ.Count() > 0 && USER_NO != "stage")
        {
            <tr>
                <td align="center">
                    <img src="~/Content/img/web-revise-secretary-05.png" class="imgMenu" />
                    <div class="td_Menu_blue">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.ZZZ)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            }
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td height="5px"></td>
            </tr>
        }

        @if (Model.ACC.Count() > 0 && USER_NO != "stage")
        {
            <tr>
                <td align="center">
                    <img src="~/Content/img/web-revise-secretary-06.png" class="imgMenu" />
                    <div class="td_Menu_green">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.ACC)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            }
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td height="5px" align="center">
                    <a href="http://cooc.tp.edu.tw" target="_blank">COOC</a>
                </td>
            </tr>

        } @if (Model.ACC.Count() > 0 && USER_NO != "stage")
        {
            <tr>
                <td align="center">
                    <img src="~/Content/img/web-revise-secretary-0111.png" class="imgMenu" />
                    <div class="td_Menu_green">
                        @{
                            int num = 0;

                            foreach (var ITEM in Model.AWAC)
                            {
                                num++;
                                if (num > 1)
                                {
                                    <br />
                                }
                                <a href='@Url.Action(ITEM.ACTION_ID,ITEM.CONTROLLER)?FirstPage=true&SouBre_NO=@ITEM.BRE_NO' target="@(ITEM?.TARGET ?? "_self")">@ITEM.BRE_NAME</a>
                            }
                        }
                    </div>
                </td>
            </tr>
            

        }
    </table>
</div>
<script>
    function toggleDropdown() {
        document.getElementById("dropdownContent").classList.toggle("show");
    }

    // 關閉下拉選單，如果使用者點擊外部
    window.onclick = function (event) {
        if (!event.target.matches('.dropdown-button')) {
            const dropdowns = document.getElementsByClassName("dropdown-menu");
            for (let i = 0; i < dropdowns.length; i++) {
                dropdowns[i].classList.remove('show');
            }
        }
    }
</script>