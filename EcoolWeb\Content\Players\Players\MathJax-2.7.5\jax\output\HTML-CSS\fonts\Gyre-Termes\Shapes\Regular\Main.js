/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Shapes/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Shapes={directory:"Shapes/Regular",family:"GyreTermesMathJax_Shapes",testString:"\u00A0\u2422\u2423\u2500\u2502\u251C\u2524\u252C\u2534\u253C\u2581\u2588\u2591\u2592\u2593",32:[0,0,250,0,0],160:[0,0,250,0,0],9250:[683,10,500,3,468],9251:[160,106,500,40,460],9472:[276,-224,500,0,500],9474:[650,150,500,224,276],9500:[650,150,500,224,500],9508:[650,150,500,0,276],9516:[276,150,500,0,500],9524:[650,-224,500,0,500],9532:[650,150,500,0,500],9601:[100,0,800,0,800],9608:[800,0,800,0,800],9617:[800,0,800,0,800],9618:[800,0,800,0,800],9619:[800,0,800,0,800],9642:[400,-100,460,80,380],9643:[400,-100,460,80,380],9644:[375,-125,660,80,580],9645:[375,-125,660,80,580],9655:[601,101,768,80,688],9665:[601,101,768,80,688],9675:[568,68,796,80,716],9679:[568,68,796,80,716],9702:[400,-100,460,80,380],9828:[668,0,796,80,716],9829:[668,0,760,80,680],9830:[670,0,782,80,702],9831:[668,0,822,80,742],9834:[662,14,600,66,556],9901:[475,-25,500,-117,617],9902:[700,200,500,-171,671],11012:[470,-30,1091,80,1011],11013:[470,-30,1030,80,950],11014:[690,180,600,80,520],11015:[680,190,600,80,520],11020:[470,-30,1040,80,960],11021:[690,190,600,80,520],11034:[702,202,1008,52,956],11057:[830,330,850,80,770],11059:[430,-70,1170,80,1090]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Shapes"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Shapes/Regular/Main.js"]);
