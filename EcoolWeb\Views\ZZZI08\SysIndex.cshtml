﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI08.ZZZI08EditViewModel
@using EcoolWeb.Util;

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
  
    @Html.HiddenFor(m => m.VIEW_DATA_TYPE)
    @Html.HiddenFor(m => m.DATA_TYPE)

    @Html.HiddenFor(m => m.whereIndex)

    @Html.HiddenFor(m => m.uADDT11.DIALOG_ID)
    @Html.HiddenFor(m => m.uADDT11.STATUS)

    @Html.HiddenFor(m => m.uADDT11.ANSWER_PERSON_YN)

    @Html.HiddenFor(m => m.uADDT11.ANSWER_COUNT)

    @Html.Hidden("Submit_YN", "Y")

    var QData = ViewBag.QData as ECOOL_APP.com.ecool.Models.DTO.ZZZI08ListViewModel;

    @Html.Hidden("OrderByName", QData.OrderByName)
    @Html.Hidden("SyntaxName", QData.SyntaxName)
    @Html.Hidden("Page", QData.Page)
    @Html.Hidden("SearchType", QData.SearchType)
    @Html.Hidden("Q_COPY_YN", QData.Q_COPY_YN)
    @Html.Hidden("Q_SCHOOL_NO", QData.Q_SCHOOL_NO)
    @Html.Hidden("SearchContents", QData.SearchContents)


    <img src="~/Content/img/web-bar2-revise-07.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group text-center">
                <div class="col-md-6">
                    <button class="btn-block btn btn-default" onclick="onAdd(1)">不限對象，回答到對</button>
                </div>
               <div class="col-md-6"> 
                   <button class = "btn-block btn btn-default"  onclick = "onAdd(2)">限制對象，回答到對</button>
                </div>
                <div class="col-md-6">
                    <button class="btn-block btn btn-default" onclick="onAdd(3)">不限對象，只答1次</button>
                </div>
                <div class="col-md-6">
                    <button class="btn-block btn btn-default" onclick="onAdd(4)">限制對象，只答1次</button>
                </div>
            </div>
            <div class="form-group text-center">
                <label class="text-danger">
                    PS. 新增有獎徵答發佈後，可得酷幣10點
                </label>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">

   function onAdd(Type) {
        $('#VIEW_DATA_TYPE').val("@ViewBag.VIEW_A")
        form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';


        if (Type==1) { //不限對象，回答到對

        }
        else if (Type == 2) //限制對象，回答到對
        {
            $('#@Html.IdFor(m=>m.uADDT11.ANSWER_PERSON_YN)').val('Y')
        }
        else if (Type == 3) { //不限對象，只答1次
             $('#@Html.IdFor(m=>m.uADDT11.ANSWER_COUNT)').val(1)
        }
        else if (Type == 4) { //限制對象，只答1次
            $('#@Html.IdFor(m=>m.uADDT11.ANSWER_PERSON_YN)').val('Y')
            $('#@Html.IdFor(m=>m.uADDT11.ANSWER_COUNT)').val(1)
        }


       form1.submit();

    }



    </script>
}