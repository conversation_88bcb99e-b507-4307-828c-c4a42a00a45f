﻿@model GameQRCodeToGuestViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

<div class="panel with-nav-tabs panel-info">
    @if (!string.IsNullOrWhiteSpace((string)TempData["GameName"]))
    {
        <div class="panel-heading">
            <h1>@TempData["GameName"]</h1>
        </div>
    }
    <div class="panel-body" style="background-color:#eef6fa;">
        <div>
            @using (Html.BeginForm("Cre", "Barcode", FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off" }))
            {
                @Html.HiddenFor(m => m.GAME_NO)
                @Html.Hidden("Value")

                <div class="form-group">
                    <div class="input-group input-group-lg">
                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                        @Html.EditorFor(model => model.NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入英文暱稱" } })
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group input-group-lg">
                        <span class="input-group-addon"><i class="glyphicon glyphicon-earphone"></i></span>
                        @Html.EditorFor(model => model.PHONE, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入行動電話號碼" } })
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-xs-12 text-center">
                        <div class="form-inline">
                            <input type="button" value="產生" class="btn btn-default btn-lg" onclick="CreQrCode()" />
                        </div>
                    </div>
                </div>

            }
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

        function CreQrCode() {

            if ($('#@Html.IdFor(m=>m.NAME)').val() == '') {
                alert('英文暱稱未輸入')
                return false;
            }

            if ($('#@Html.IdFor(m=>m.PHONE)').val() == '') {
                alert('行動電話號碼未輸入')
                return false;
            }

            var Value = '{"GAME_NO":"' + $('#@Html.IdFor(m=>m.GAME_NO)').val() + '","NAME":"' + $('#@Html.IdFor(m=>m.NAME)').val() + '","PHONE":"' + $('#@Html.IdFor(m=>m.PHONE)').val() + '"}';
            $('#Value').val(Value);

             $(targetFormID).submit();
        }
    </script>
}