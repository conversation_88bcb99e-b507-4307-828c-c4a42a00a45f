﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'ms', {
	button: {
		title: 'Ciri-ciri Butang',
		text: '<PERSON><PERSON> (<PERSON><PERSON>)',
		type: '<PERSON><PERSON>',
		typeBtn: 'Button',
		typeSbm: 'Submit',
		typeRst: 'Reset'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Ciri-ciri Checkbox',
		radioTitle: 'Ciri-ciri Butang Radio',
		value: '<PERSON><PERSON>',
		selected: 'Dipilih',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Ciri-ciri Borang',
		menu: 'Ciri-ciri Borang',
		action: 'Tindakan borang',
		method: 'Cara borang dihantar',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'Ciri-ciri Field Tersembunyi',
		name: '<PERSON><PERSON>',
		value: '<PERSON><PERSON>'
	},
	select: {
		title: 'Ciri-ciri Selection Field',
		selectInfo: 'Select Info',
		opAvail: '<PERSON><PERSON><PERSON> se<PERSON>',
		value: '<PERSON><PERSON>',
		size: '<PERSON><PERSON>',
		lines: 'garisan',
		chkMulti: 'Benarkan pilihan pelbagai',
		required: 'Required', // MISSING
		opText: 'Teks',
		opValue: 'Nilai',
		btnAdd: 'Tambah Pilihan',
		btnModify: 'Ubah Pilihan',
		btnUp: 'Naik ke atas',
		btnDown: 'Turun ke bawah',
		btnSetValue: 'Set sebagai nilai terpilih',
		btnDelete: 'Padam'
	},
	textarea: {
		title: 'Ciri-ciri Textarea',
		cols: 'Lajur',
		rows: 'Baris'
	},
	textfield: {
		title: 'Ciri-ciri Text Field',
		name: 'Nama',
		value: 'Nilai',
		charWidth: 'Lebar isian',
		maxChars: 'Isian Maksimum',
		required: 'Required', // MISSING
		type: 'Jenis',
		typeText: 'Teks',
		typePass: 'Kata Laluan',
		typeEmail: 'Email', // MISSING
		typeSearch: 'Search', // MISSING
		typeTel: 'Telephone Number', // MISSING
		typeUrl: 'URL'
	}
} );
