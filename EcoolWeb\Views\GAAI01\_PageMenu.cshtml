﻿@using ECOOL_APP;
@{

    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;

}

@if (Permission != null)
{
<div class="form-group">

    @if (Permission.Where(a => a.ActionName == "Index").Any())
    {
        <a href="@Url.Action("Index")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="Index" ? "active":"")"> 說明頁</a>
    }

    @if (Permission.Where(a => a.ActionName == "WearIndex").Any())
    {
        <a href="@Url.Action("WearIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="WearIndex" ? "active":"")"> 未配戴點選登記(模式一)</a>
    }
    @if (Permission.Where(a => a.ActionName == "TagWearIndex").Any())
    {
        <a href="@Url.Action("TagWearIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="TagWearIndex" ? "active":"")"> 有配戴感應登記(模式二)</a>
    }

    @if (Permission.Where(a => a.ActionName == "AllWearIndex").Any())
    {
        <a href="@Url.Action("AllWearIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="AllWearIndex" ? "active":"")"> 全部都配戴登記(模式三)</a>
    }

    @if (Permission.Where(a => a.ActionName == "SearchClassIndex").Any())
    {
        <a href="@Url.Action("SearchClassIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="SearchClassIndex" ? "active":"")"> 查詢班級登記</a>
    }

    @if (Permission.Where(a => a.ActionName == "CareList").Any())
    {
        <a href="@Url.Action("CareList")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="CareList" ? "active":"")"> 關懷名單</a>
    }

    @if (Permission.Where(a => a.ActionName == "SysSetWearIndex").Any())
    {
        <a href="@Url.Action("SysSetWearIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="SysSetWearIndex" ? "active":"")"> 管理者參數設定</a>
    }

    @if (Permission.Where(a => a.ActionName == "ClassWearIndex").Any())
    {
        <a href="@Url.Action("ClassWearIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ClassWearIndex" ? "active":"")"> 班級統計表</a>

    }

    @if (Permission.Where(a => a.ActionName == "MonthWearIndex").Any())
    {
        <a href="@Url.Action("MonthWearIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="MonthWearIndex" ? "active":"")"> 督學月統計表</a>
    }
    @if (Permission.Where(a => a.ActionName == "WeekWearIndex").Any())
    {
        <a href="@Url.Action("WeekWearIndex")" class="btn btn-sm btn-sys @(ViewBag.NowAction=="WeekWearIndex" ? "active":"")"> 督學週統計表</a>
    }
    @if (Permission.Where(a => a.ActionName == "SpotCheckIndex").Any())
    {
        <a href="@Url.Action("SpotCheckIndex")" class="btn btn-sm btn-sys-busker @(ViewBag.NowAction=="SpotCheckIndex" ? "active":"")"> 學校隨機抽查登記</a>
    }

    @if (Permission.Where(a => a.ActionName == "SpotCheckSearch").Any())
    {
        <a href="@Url.Action("SpotCheckSearch")" class="btn btn-sm btn-sys-busker @(ViewBag.NowAction=="SpotCheckSearch" ? "active":"")"> 學校隨機抽查查詢</a>
    }
</div>
}