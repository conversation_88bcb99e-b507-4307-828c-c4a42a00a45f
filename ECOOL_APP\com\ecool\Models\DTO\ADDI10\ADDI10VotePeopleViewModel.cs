﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI10VotePeopleViewModel
    {
        /// <summary>
        ///回覆id
        /// </summary>
        [DisplayName("回覆id")]
        public string ANSWER_ID { get; set; }

        /// <summary>
        ///投票ID
        /// </summary>
        [DisplayName("投票ID")]
        public string QUESTIONNAIRE_ID { get; set; }

        /// <summary>
        ///投票者學校
        /// </summary>
        [DisplayName("投票者學校")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        [DisplayName("座號")]
        public string SEAT_NO { get; set; }
        /// <summary>
        ///投票者帳號
        /// </summary>
        [DisplayName("投票者帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///投票者姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///投票者簡稱
        /// </summary>
        [DisplayName("姓名")]
        public string SNAME { get; set; }

        /// <summary>
        ///投票者日期
        /// </summary>
        [DisplayName("投票日期")]
        public DateTime? ANSWER_DATE { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public string STATUS { get; set; }

        /// <summary>
        ///作廢與否
        /// </summary>
        [DisplayName("作廢與否")]
        public string DEL_YN { get; set; }

        [DisplayName("回答")]
        public string ANSWER { get; set; }
    }
}