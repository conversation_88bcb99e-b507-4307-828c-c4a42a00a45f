﻿
@using (Html.BeginForm("Index", "Encrypt", FormMethod.Post))
{
    <div class="form-group">
         <label class="control-label">MD5</label>
    </div>
    <div class="form-group">
        @Html.Editor("StrA", new { htmlAttributes = new { @class = "form-control input-sm" } })
        <br/>
        @ViewBag.StrA
    </div>
    <div class="form-group">
        <label class="control-label">Dec</label>
    </div>
    <div class="form-group">
        要加密字串
        @Html.Editor("StrB", (string )ViewBag.StrA, new { htmlAttributes = new { @class = "form-control input-sm" } })
        key
        @Html.Editor("NewPageKey", new { htmlAttributes = new { @class = "form-control input-sm" } })
        <br />
        @ViewBag.StrB
    </div>

    <input type="submit" class="btn-yellow btn btn-sm" value="確定"  />
}