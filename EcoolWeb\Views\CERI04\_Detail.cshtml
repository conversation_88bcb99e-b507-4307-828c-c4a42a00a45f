﻿@model List<CERI04EditDetailViewModel>

@using (Html.BeginCollectionItem("Details", true))
{
    var Index = Html.GetIndex("Details");
   
<tr>

    <td>
        @Html.HiddenFor(m => m.FirstOrDefault().SCHOOL_NO)
        @Html.HiddenFor(m => m.FirstOrDefault().USER_NO)
        @Html.HiddenFor(m => m.FirstOrDefault().CLASS_NO)
        @Html.HiddenFor(m => m.FirstOrDefault().SEAT_NO)
        @Html.HiddenFor(m => m.FirstOrDefault().NAME)
        @Html.HiddenFor(m => m.FirstOrDefault().SNAME)
        @Html.HiddenFor(m => m.FirstOrDefault().O_IS_PASS)

        @Html.DisplayFor(m => m.FirstOrDefault().CLASS_NO)
    </td>
    <td>
        @Html.DisplayFor(m => m.FirstOrDefault().SEAT_NO)
    </td>
    <td>
        @Html.DisplayFor(m => m.FirstOrDefault().NAME)
    </td>
    <td>
        @if (Model.FirstOrDefault().IsText == "Y")
        {
            if (Model.Count() > 1)
            {

                foreach (var item in Model)
                {

                    @Html.Partial("_ADDDetail", item)
                }
            }
            else
            {
               
                @Html.EditorFor(m => m.FirstOrDefault().PersonText, new { @class = "form-control" })

            }
            <div id="<EMAIL>().USER_NO"></div>
        @:</td>

        @*<div class="col-xs-2 "></div> <div class="col-xs-2 "></div> <div class="col-xs-4 "></div>*@

        @*<div id="<EMAIL>().USER_NO"></div>*@
     
        <td>
          
                <button class="btn btn-block btn-default-buy" type="button" onclick="AddItemD('<EMAIL>().USER_NO',@Model.FirstOrDefault().USER_NO,@Model.FirstOrDefault().SCHOOL_NO)">
                    <span class="fa fa-plus" aria-hidden="true"title="增加一個特殊表現"></span>
                </button>
         
        </td>



    }
    else
    {

        <td class="col-xs-4 text-center">
            <input type="checkbox" class="IsPass" name="@Html.NameFor(m => m.FirstOrDefault().IS_PASS)" id="@Html.IdFor(m => m.FirstOrDefault().IS_PASS)" value="true" @((Model.FirstOrDefault().IS_PASS ?? false) ? "checked" : "") />
        </td>}

    </tr>

    }
@section Scripts {

    <script language="JavaScript">


    </script>

}
