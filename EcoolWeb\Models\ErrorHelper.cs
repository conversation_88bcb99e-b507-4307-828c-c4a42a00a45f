﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public class ErrorHelper
    {
        public class ErrorVal
        {

            /// <summary>
            /// 活動未開始 
            /// </summary>
            public static string NotFGD_IdKey = "NotFGD_IdKey";


            /// <summary>
            /// 抱歉, 找不到您要的網頁
            /// </summary>
            public static string PageNotFound = "PageNotFound";

            /// <summary>
            /// 抱歉, 處理你的請求發生500錯誤
            /// </summary>
            public static string InternalError = "InternalError";

            /// <summary>
            /// 抱歉, 您沒有權限
            /// </summary>
            public static string PermissionError = "PermissionError";

            /// <summary>
            /// 抱歉, 您無權看此筆資料
            /// </summary>
            public static string NotSeeDataError = "NotSeeDataError";
            public static string NotSeeDataErrorDesc()
            {
                return "抱歉, 您無權看此筆資料";
            }

            /// <summary>
            ///抱歉, 沒有資料
            /// </summary>
            public static string NotDataError = "NotDataError";

            /// <summary>
            ///抱歉, 找不到任何資料
            /// </summary>
            public static string NotFindError = "NotFindError";

            /// <summary>
            ///抱歉, 此檔案已不存在
            /// </summary>
            public static string NotFileError = "NotFileError";

            /// <summary>
            ///抱歉, 未傳入正確參數
            /// </summary>
            public static string NotParameterError = "NotParameterError";

            /// <summary>
            ///抱歉, 處理你的請求發生通用Generic錯誤
            /// </summary>
            public static string GenericError = "GenericError";

            /// <summary>
            /// 登入逾時!請重新登入
            /// </summary>
            public static string SessionTimeOutError = "SessionTimeOutError";

            /// <summary>
            /// 一般訊息
            /// </summary>
            public static string StatusMessage = "StatusMessage";
        }
    }
}