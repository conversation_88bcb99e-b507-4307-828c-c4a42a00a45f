/* Tables */
#final table {
    border-collapse: collapse;
    width: 100%;
    background: #fff;
}

#final th {
    background-color: #f5f583;
    background-image: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(247, 142, 142,.25) 100%);
    font-family: "<PERSON><PERSON><PERSON>", <PERSON><PERSON>, sans-serif;
    font-weight: bold;
    color: #333333;
    white-space: nowrap;
    padding: 7px;
    height: 35px;
}

#final head th {
    z-index: 1;
    position: relative;
}

#final tbody th {
    background-color: #f5f583;
    background-image: none;
}

#final tbody td {
    padding: 15px;
}

#final tbody tr:nth-child(2n-1) {
    background-color: #eee;
    transition: all .125s ease-in-out;
}

    #final tbody tr:nth-child(2n-1) th {
        background-image: linear-gradient(to left, rgba(0,0,0,.125) 0%, rgba(247, 142, 142,.25) 100%);
    }

#final tbody tr:hover {
    background-color: #ccc;
}

#final tbody img {
    border-radius: 50%;
    display: block;
    width: 4rem;
    height: 4rem;
}

/* Final */
#final .sticky-wrap {
    overflow-x: auto;
    overflow-y: auto;
}

#final .toggle {
    background-color: #004B80;
}
.sticky-col table {
    background-color: transparent !important;
}
#final .sticky-col thead th {
    background: transparent !important;
}