﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    @{
        ViewBag.Title = "登入";
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

        ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

        ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        List<BDMT01> SchoolList = db.BDMT01.OrderBy(school => school.SHORT_NAME).ToList();

    }

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @Styles.Render("~/Content/css")
    <link href="@Url.Content("~/Content/css/EzCss.css")?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss")" rel="stylesheet" />
    <link href="@Url.Content("~/Content/font-awesome/css/font-awesome.min.css")" rel="stylesheet" />
    <style>
        body{
            background-image:url('@Url.Content("~/Content/img/web-01.png")');
            background-repeat:repeat;
        }
    </style>
    @RenderSection("css", required: false)
</head>
<body>
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script type="text/javascript">
    function AuthorizeClass() {
       this.AuthIdentity = function () {
                    if (this.validateLoginTextBox() == false) {
                        return;
                    }
                    $.ajax({
                        url: '@Url.Action("CheckUserStatus","Home")',
                        data: { SchoolNo: 403605, UserNo: 107038, sPwd: 6267, InputCode: $("#txtInputCode").val(), CheckCode: false },
                        type: 'post',
                        dataType: 'json',               // xml/json/script/html
                        cache: false,                   // 是否允許快取
                        success: function (data) {
                            var res = jQuery.parseJSON(data);
                            if (res.ReValue != '') {
                                strMsg += res.ReValue + '\r\n';

                                if (res.IsInputCode == 1) {
                                    $('.classInputCode').show()
                                }
                                alert(strMsg);
                                return;
                            }
                            else {
                                document.loginform.submit();
                            }
                        },
                         error: function (request, status, error) {
                            alert(request.responseText);
                        }
                  });
                }
    }
    $(function () {
        AuthorizeClass();
    });
    </script>
    @RenderSection("scripts", required: false)
</body>
</html>
