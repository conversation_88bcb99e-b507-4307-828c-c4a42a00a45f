/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Bold/BasicLatin.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Fraktur-bold"],{32:[0,0,250,0,0],33:[689,12,349,107,241],34:[695,-432,254,10,231],38:[696,16,871,44,839],39:[695,-436,250,80,158],40:[737,186,459,134,347],41:[735,187,459,105,326],42:[692,-449,328,40,277],43:[598,82,893,56,837],44:[107,191,328,118,253],45:[275,-236,893,54,833],46:[102,15,328,103,237],47:[721,182,593,41,550],48:[501,12,593,42,533],49:[489,0,593,54,548],50:[491,-2,593,44,563],51:[487,193,593,31,523],52:[495,196,593,13,565],53:[481,190,593,19,518],54:[704,12,593,48,547],55:[479,197,593,54,591],56:[714,5,593,45,542],57:[487,195,593,29,549],58:[457,12,255,57,197],59:[458,190,255,56,211],61:[343,-168,582,22,559],63:[697,14,428,40,422],65:[686,31,847,29,827],66:[684,31,1044,57,965],67:[676,32,723,72,726],68:[683,29,982,31,896],69:[686,29,783,74,728],70:[684,146,722,17,727],71:[687,29,927,74,844],72:[683,126,851,6,752],73:[681,25,655,32,623],74:[680,141,652,-8,616],75:[681,26,789,20,806],76:[683,28,786,30,764],77:[683,32,1239,27,1232],78:[679,30,983,26,973],79:[726,30,976,12,881],80:[688,223,977,33,943],81:[726,83,976,12,918],82:[688,28,978,31,978],83:[685,31,978,82,905],84:[686,30,790,31,802],85:[688,39,851,18,871],86:[685,29,982,25,966],87:[683,30,1235,26,1240],88:[681,35,849,32,835],89:[688,214,984,34,878],90:[677,148,711,-4,624],91:[740,130,257,36,226],93:[738,132,257,14,208],94:[734,-452,590,1,584],97:[472,32,603,80,586],98:[690,32,590,86,504],99:[473,26,464,87,424],100:[632,28,589,-1,511],101:[471,27,472,81,428],102:[687,222,388,35,372],103:[472,208,595,17,541],104:[687,207,615,89,507],105:[686,25,331,3,327],106:[682,203,332,-19,238],107:[682,25,464,34,432],108:[681,24,337,100,312],109:[476,31,921,16,900],110:[473,28,654,5,608],111:[482,34,609,107,515],112:[557,207,604,-1,519],113:[485,211,596,87,515],114:[472,26,460,13,453],115:[479,34,523,-23,481],116:[648,27,393,43,407],117:[472,32,589,9,603],118:[546,27,604,56,507],119:[549,32,918,55,815],120:[471,188,459,8,441],121:[557,221,589,60,512],122:[471,214,461,-7,378]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Fraktur/Bold/BasicLatin.js");
