﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewBag.Title</title>
    @Styles.Render("~/Content/cssTwo")
    <link href="~/Content/css/EzCss.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @{
        EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData("~/Views/Shared/_LayoutGame.cshtml");
    }

    @{ Html.RenderPartial("_GoogleAnalytics"); }
</head>
<body>
    <div style="width:90%;margin: 0px auto;padding-top:10px">
        @RenderBody()
    </div>
</body>
</html>
@RenderSection("scripts", required: false)
<script type="text/javascript">
    window.history.forward(1);
</script>