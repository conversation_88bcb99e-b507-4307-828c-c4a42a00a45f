/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpSm/Bold/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXIntegralsUpSm-bold"],{32:[0,0,250,0,0],160:[0,0,250,0,0],8747:[732,193,396,52,414],8748:[732,193,666,52,684],8749:[732,193,936,52,954],8750:[732,193,466,52,426],8751:[732,193,736,52,696],8752:[732,193,998,52,965],8753:[732,193,501,52,468],8754:[732,193,501,52,469],8755:[732,193,496,52,486],10764:[732,193,1206,52,1224],10765:[732,193,450,52,420],10766:[732,193,450,52,420],10767:[732,193,550,40,518],10768:[732,193,479,52,447],10769:[732,193,511,52,478],10770:[732,193,489,52,449],10771:[732,193,487,52,447],10772:[732,193,572,52,534],10773:[732,193,520,52,480],10774:[732,193,523,52,483],10775:[732,193,600,8,646],10776:[733,192,505,31,467],10777:[732,193,516,52,476],10778:[732,193,516,52,476],10779:[802,193,403,40,428],10780:[732,268,411,52,440]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsUpSm/Bold/All.js");
