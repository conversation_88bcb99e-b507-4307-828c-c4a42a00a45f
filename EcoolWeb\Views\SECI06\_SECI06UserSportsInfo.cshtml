﻿@model SECI06IndexViewModel

<style type="text/css">
    /*Div-EZ-Health*/
    .Div-EZ-Health {
        width: 90%;
        background-color: rgba(218, 237, 229, 1);
        margin: 0px auto;
        margin-top: -75px;
        border-style: solid;
        border-color: #abd6ca;
        min-width: 290px;
    }

        .Div-EZ-Health .form-horizontal {
            width: 90%;
            margin: 0px auto;
            padding-top: 130px;
            padding-bottom: 55px;
        }

        .Div-EZ-Health .Details {
            margin: 0px auto;
            padding-top: 20px;
        }

        .Div-EZ-Health .Div-btn-center {
            text-align: center;
            margin: 0px auto;
            padding-top: 20px;
            padding-bottom: 5px;
        }

        .Div-EZ-Health .form-horizontal .control-label,
        .Div-EZ-Health .form-horizontal .control-label-left {
            font-weight: bold;
            color: #004da0;
        }

    @@media (min-width:992px ) {
        .Div-EZ-Health,
        .Div-EZ-Health-bottom {
            margin-top: -75px;
        }
    }

    @@media (max-width:991px ) {
        .Div-EZ-Health,
        .Div-EZ-Health-bottom {
            margin-top: -55px;
        }
    }

    .parent {
        display: table;
        table-layout: fixed;
    }

    .child {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
    }

    @@media (max-width:768px ) {

        .Div-EZ-Health-icon-B,
        .Div-EZ-Health-icon-Hrmt08,
        .Div-EZ-Health-icon-Hrmt09,
        .Div-EZ-Health-icon-Addt24,
        .Div-EZ-Health-icon-chart {
            width: 150px;
        }

        .Div-EZ-Health-icon-B {
            top: -25px;
        }

        .Div-EZ-Health-icon-Hrmt08 {
            top: -23px;
        }

        .Div-EZ-Health-icon-Hrmt09 {
            top: -23px;
        }

        .Div-EZ-Health-icon-Addt24 {
            top: -23px;
        }

        .Div-EZ-Health-icon-BMI {
            top: -15px;
        }

        .Div-EZ-Health-icon-chart {
            top: -22px;
        }
    }

    @@media (max-width:930px ) and (min-width:769px ) {
        .Div-EZ-Health-icon-B,
        .Div-EZ-Health-icon-Hrmt08,
        .Div-EZ-Health-icon-Hrmt09,
        .Div-EZ-Health-icon-Addt24,
        .Div-EZ-Health-icon-chart {
            width: 150px;
        }

        .Div-EZ-Health-icon-B {
            top: -25px;
        }

        .Div-EZ-Health-icon-Hrmt08 {
            top: -7px;
        }

        .Div-EZ-Health-icon-Hrmt09 {
            top: -10px;
        }

        .Div-EZ-Health-icon-Addt24 {
            top: -7px;
        }

        .Div-EZ-Health-icon-BMI {
            top: -15px;
        }

        .Div-EZ-Health-icon-chart {
            top: -24px;
        }
    }

    @@media (min-width:931px ) {

        .Div-EZ-Health-icon-B {
            top: -35px;
        }

        .Div-EZ-Health-icon-Hrmt08 {
            top: -16px;
        }

        .Div-EZ-Health-icon-Hrmt09 {
            top: -18px;
        }

        .Div-EZ-Health-icon-Addt24 {
            top: -15px;
        }

        .Div-EZ-Health-icon-BMI {
            top: -15px;
        }

        .Div-EZ-Health-icon-chart {
            top: -30px;
        }
    }

    .Div-EZ-Health-icon-B {
        left: 43px;
        max-width: 200px;
        position: absolute;
        z-index: 5;
    }

    .Div-EZ-Health-icon-Hrmt08 {
        left: 43px;
        max-width: 200px;
        position: absolute;
        z-index: 5;
    }

    .Div-EZ-Health-icon-Hrmt09 {
        left: 43px;
        max-width: 200px;
        position: absolute;
        z-index: 5;
    }

    .Div-EZ-Health-icon-Addt24 {
        left: 43px;
        max-width: 200px;
        position: absolute;
        z-index: 5;
    }

    .Div-EZ-Health-icon-BMI {
        max-width: 375px;
        width: 100%;
        margin-top: -30px;
    }

    .Div-EZ-Health-icon-BMI-font {
        margin: 0px auto;
        height: 100%;
        max-width: 340px;
        min-height: 150px;
        width: 90%;
        background-color: #f2f2f2;
        padding-top: 35px;
        padding-left: 10px;
        padding-right: 10px;
        padding-bottom: 10px;
        margin-top: -35px;
        text-align: left;
    }

    .Div-EZ-Health-icon-chart {
        left: 43px;
        max-width: 200px;
        position: absolute;
        z-index: 5;
    }

    .Div-EZ-Health-icon-Player {
        top: -30px;
        left: 28px;
        position: absolute;
        z-index: 5;
        width: 233px;
        height: 233px;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
    }

    .center-vertical {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
    }

    @@media (min-width: 768px) {
        .row-h {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
        }

            .row-h > [class*='col-'] {
                padding-top: 15px;
                padding-bottom: 15px;
                margin-left: 5px;
            }
    }

    .vertical-alignment-helper {
        display: table;
        height: 100%;
        width: 100%;
    }

    .vertical-align-center {
        /* To center vertically */
        display: table-cell;
        vertical-align: middle;
    }

    .chart {
        border-style: dashed;
        border-color: #a3a7b8;
        margin: 0px auto;
        height: 100%;
        padding-top: 35px;
        padding-left: 5px;
        padding-bottom: 25px;
        width: 700px;
        overflow: auto;
    }
</style>
<style media="print">
    .dt {
        font-size: 0.7cm;
    }

    @@media print {
        .chart {
            width: 100%;
            overflow: visible;
        }

        .Div-EZ-Health {
            width: 90%;
            background-color: rgba(218, 237, 229, 1);
            margin: 0px auto;
            margin-top: -75px;
            border-style: solid;
            border-color: #abd6ca;
            min-width: 290px;
        }

            .Div-EZ-Health .form-horizontal {
                width: 90%;
                margin: 0px auto;
                padding-top: 130px;
                padding-bottom: 55px;
            }

            .Div-EZ-Health .Details {
                margin: 0px auto;
                padding-top: 20px;
            }

            .Div-EZ-Health .Div-btn-center {
                text-align: center;
                margin: 0px auto;
                padding-top: 20px;
                padding-bottom: 5px;
            }

            .Div-EZ-Health .form-horizontal .control-label,
            .Div-EZ-Health .form-horizontal .control-label-left {
                font-weight: bold;
                color: #004da0;
            }
    }
    }
</style>
<style media="print">
    .td {
        font-size: 0.7cm;
    }
</style>

@if (!string.IsNullOrWhiteSpace(Model.WhereUSER_NO))
{
    <div style="height:30px"></div>
    <img src="~/Content/img/04_health_01.png" style="width:100%;min-width:290px;" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-Health">
        <div class="form-horizontal">
            <div class="row">
                <div class="col-md-5 col-xs-12" style="max-width:400px;padding-bottom:50px;margin: 0px auto;">
                    <div class="text-center">
                        <img src="~/Content/img/04_health_09.png" />
                        @if (!string.IsNullOrWhiteSpace(Model.PlayerUrl))
                        {

                            <div class="Div-EZ-Health-icon-Player">
                                <img src='@Url.Content(Model.PlayerUrl)' style="max-width:100%; max-height:100%;margin:auto;display:block; z-index: 6;" />
                            </div>

                        }
                    </div>
                </div>
                <div class="col-md-7 col-xs-12" style="padding-bottom:50px">
                    <div style="background-color:#bdf1f1;padding:6px 6px 6px 6px;height: 100%;" ;>
                        <img src="~/Content/img/04_health_03.png" class="Div-EZ-Health-icon-B" />
                        <div style="border-style:dashed;border-color:#90d5d6;margin: 0px auto;height: 100%;padding-top:35px;padding-left:5px;padding-bottom:25px">
                            @if (Model.MyHRMT01 != null)
                            {
                                <table class="table-ecool table-92Per table-hover">
                                    <tr>
                                        <th class="dt">
                                            學生
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.MyHRMT01.SNAME)
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            生日
                                        </th>
                                        <th class="dd">
                                            @Model.MyHRMT01.BIRTHDAY.Value.ToString("yyyy/MM/dd")
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            性別
                                        </th>
                                        <th class="dd">
                                            @HRMT01.ParserSex(Model.MyHRMT01.SEX)
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            年級
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.MyHRMT01.GRADE)
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            班級
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.MyHRMT01.CLASS_NO)
                                        </th>
                                    </tr>

                                    <tr>
                                        <th class="dt">
                                            年紀
                                        </th>
                                        <th class="dd">
                                            @if (Model.MyAge != null)
                                            {
                                                @Model.MyAge.Value.ToString("#.0")
                                            }
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            BMI
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt08 != null)
                                            {
                                                @(Model.NowHrmt08.ShowBMI.Value.ToString("#.0"))<samp>(@(Model.BMI_TYPE_NAME))</samp>
                                            }
                                            else
                                            {
                                                <samp>無資料</samp>
                                            }
                                            <button id="BMIbtn" type="button" class="btn btn-link-ez" href="@Url.Action("BMI_MEMO", (string)ViewBag.BRE_NO)">說明</button>
                                        </th>
                                    </tr>
                                    @if (!string.IsNullOrWhiteSpace(Model.SUGGEST_BMI_MEMO))
                                    {
                                        <tr>
                                            <th class="dt">
                                            </th>
                                            <th class="dt">
                                                @Model.SUGGEST_BMI_MEMO
                                            </th>
                                        </tr>
                                    }
                                </table>
                            }
                            else
                            {
                                <div class="text-center">
                                    <h3>無資料</h3>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="row row-h">
                <div class="col-md-6 col-xs-12" style="padding-bottom:50px">
                    <div style="background-color:#bef0e3;padding:6px 6px 6px 6px;height: 100%;" ;>
                        <img src="~/Content/img/04_health_04.png" class="Div-EZ-Health-icon-Hrmt08" />
                        <div style="border-style:dashed;border-color:#92d4c4;margin: 0px auto;height: 100%;padding-top:35px;padding-left:5px;padding-bottom:25px">
                            @if (Model.NowHrmt08 != null)
                            {
                                <table class="table-ecool table-92Per table-hover">
                                    <tr>
                                        <th class="dt">
                                            測量時間
                                        </th>
                                        <th class="dd">
                                            @Model.NowHrmt08.SYEAR
                                            @(Model.NowHrmt08.SEMESTER == 1 ? "上" : "下")
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            身高
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.TALL)
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            體重
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.WEIGHT)
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            右祼視力
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.VISION_RIGHT)
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            左祼視力
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.NowHrmt08.VISION_LEFT)
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                        </th>
                                        <th class="dd text-right">
                                            <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkHrmt08More('@Model.NowHrmt08.IDNO')">more</button>
                                        </th>
                                    </tr>
                                </table>
                            }
                            else
                            {
                                <div class="text-center">
                                    <h3>無資料</h3>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xs-12" style="padding-bottom:50px">
                    <div style="background-color:#caeccf;padding:6px 6px 6px 6px;height: 100%;" ;>
                        <img src="~/Content/img/04_health_05.png" class="Div-EZ-Health-icon-Hrmt09" />
                        <div style="border-style:dashed;border-color:#a3d0ab;margin: 0px auto;height: 100%;padding-top:35px;padding-left:5px;padding-bottom:25px">
                            @if (Model.NowHrmt09 != null)
                            {
                                <table class="table-ecool table-92Per table-hover">
                                    <tr>
                                        <th class="dt">
                                            測量時間
                                        </th>
                                        <th class="dt">
                                            @Html.DisplayFor(modelItem => Model.NowHrmt09.SYEAR)
                                            學年
                                        </th>
                                        <th class="dt">
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                        </th>
                                        <th class="dt">
                                            結果
                                        </th>
                                        <th class="dt">
                                            當屆平均
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            坐姿體前彎
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.V_SET_REACH_TEST != null  && Model.NowHrmt09.AVG_V_SET_REACH_TEST != 0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.V_SET_REACH_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.AVG_V_SET_REACH_TEST != null && Model.NowHrmt09.AVG_V_SET_REACH_TEST != 0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_V_SET_REACH_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            立定跳遠
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.S_L_JUMP_TEST != null && Model.NowHrmt09.S_L_JUMP_TEST != 0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.S_L_JUMP_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.AVG_S_L_JUMP_TEST != null && Model.NowHrmt09.AVG_S_L_JUMP_TEST != 0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_S_L_JUMP_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            仰臥起坐
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.SIT_UPS_TEST != null && Model.NowHrmt09.SIT_UPS_TEST != 0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.SIT_UPS_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.AVG_SIT_UPS_TEST != null && Model.NowHrmt09.AVG_SIT_UPS_TEST != 0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_SIT_UPS_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            800公尺跑走
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.C_P_F_TEST != null && Model.NowHrmt09.C_P_F_TEST != 0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.C_P_F_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                        <th class="dd">
                                            @if (Model.NowHrmt09.AVG_C_P_F_TEST != null&& Model.NowHrmt09.AVG_C_P_F_TEST!=0)
                                            {
                                                @Html.DisplayFor(modelItem => Model.NowHrmt09.AVG_C_P_F_TEST)
                                            }
                                            else
                                            {
                                                <span>無資料</span>
                                            }
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                        </th>
                                        <th class="dd">
                                        </th>
                                        <th class="dd text-right">
                                            <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkHrmt09More('@Model.NowHrmt08.IDNO')">more</button>
                                        </th>
                                    </tr>
                                </table>
                            }
                            else
                            {
                                <div class="text-center">
                                    <h3>無資料</h3>
                                </div>

                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="row row-h" id="secI06">
                <div class="col-md-6 col-xs-12" style="padding-bottom:50px">
                    <div style="background-color:#b6ece8;padding:6px 6px 6px 6px;height: 100%;" ;>
                        <img src="~/Content/img/04_health_06.png" class="Div-EZ-Health-icon-Addt24" />
                        <div style="border-style:dashed;border-color:#53ada6;margin: 0px auto;height: 100%;padding-top:35px;padding-left:5px;padding-bottom:25px">

                            @if (Model.NowAddt24 != null)
                            {
                                <table class="table-ecool table-92Per table-hover">
                                    <tr>
                                        <th class="dt">
                                            當日日期
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.NowAddt24.The_day_M)
                                            <span>公尺</span>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            昨日日期
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.NowAddt24.Yesterday_M)
                                            <span>公尺</span>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            前日日期
                                        </th>
                                        <th class="dd">
                                            @Html.DisplayFor(modelItem => Model.NowAddt24.The_day_before_yesterday_M)
                                            <span>公尺</span>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                            累積里程
                                        </th>
                                        <th class="dd">
                                            @((((Double)Model.NowAddt24.Total_M) / 1000.00).ToString("#,0.000"))
                                            <span>公里</span>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="dt">
                                        </th>
                                        <th class="dd text-right">
                                            <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkAddt24More('@Model.WhereSCHOOL_NO','@Model.WhereUSER_NO')">more</button>
                                        </th>
                                    </tr>
                                </table>
                            }
                            else
                            {
                                <div class="text-center">
                                    <h3>無資料</h3>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xs-12" style="padding-bottom:50px;margin: 0px auto;">
                    <div class="text-center">
                        <img src="~/Content/img/04_health_14.png" class="Div-EZ-Health-icon-BMI" />
                        <div class="Div-EZ-Health-icon-BMI-font">
                            <img src="~/Content/img/04_health_14_font.png" />
                            <br /><br />
                            @if (!string.IsNullOrEmpty(Model.COMMENT))
                            {
                                @Model.COMMENT

                                <br /><br />
                                <div class="text-right">
                                    <button type="button" class="btn btn-link-ez btn-xs" onclick="onInModal()">詳細建議</button>
                                </div>

                                <div id="CkeditorModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel">
                                    <div>
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">

                                                <div class="modal-header">
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                                    <h4 class="modal-title" id="modal-title">詳細建議</h4>
                                                </div>
                                                <div class="modal-body">
                                                    @if (!string.IsNullOrWhiteSpace(Model.COMMENT_ACTION))
                                                    {

                                                        @Html.Raw(HttpUtility.HtmlDecode(Model.COMMENT_ACTION.Replace("\r\n", "<br /><br />")))
                                                    }
                                                </div>
                                            </div><!-- /.modal-content -->
                                        </div><!-- /.modal-dialog -->
                                    </div>
                                </div><!-- /.modal -->
                            }
                            else
                            {
                                <h3>無建議</h3>
                            }
                        </div>
                    </div>
                </div>
            </div>
            <p style="page-break-after:always"></p>
            <div class="row" id="secI06">
                <div class="col-md-12" style="padding-bottom:50px">
                    <div style="background-color:#dcdee7;padding:6px 6px 6px 6px;height: 100%;" ;>
                        <img src="~/Content/img/04_health_07.png" class="Div-EZ-Health-icon-chart" />
                        <div class="chart">
                            @if (Model.TALLchart != null)
                            {
                                @Model.TALLchart
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" id="secI06">
                <div class="col-md-12" style="padding-bottom:50px">
                    <div style="background-color:#d7e5ec;padding:6px 6px 6px 6px;height: 100%;" ;>
                        <img src="~/Content/img/04_health_08.png" class="Div-EZ-Health-icon-chart" />
                        <div class="chart">
                            @if (Model.WEIGHTchart != null)
                            {
                                @Model.WEIGHTchart
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <img src="~/Content/img/04_health_02.png" id="secI06" style="width:100%;min-width:290px;" class="Div-EZ-Health-bottom img-responsive App_hide" alt="Responsive image" />
    <div style="height:30px"></div>
}
<p style="page-break-after:always"></p>