﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    //public enum  eUserType{Student='S',Teacher='T',Admin='A' }
    public class UserType
    {
        /// <summary>
        /// 訪客
        /// </summary>
        static public string Guest = "G";

        /// <summary>
        /// 同學
        /// </summary>
        static public string Student = "S";

        /// <summary>
        /// 家長
        /// </summary>
        static public string Parents = "P";

        /// <summary>
        /// 老師
        /// </summary>
        static public string Teacher = "T";

        /// <summary>
        /// 管理員
        /// </summary>
        static public string Admin = "A";

        static public string VIP = "V";

        static public string GetDesc(string userType)
        {
            if (userType == Student) return "同學";
            if (userType == Parents) return "家長";
            if (userType == Teacher) return "老師";
            if (userType == Admin) return "管理員";
            if (userType == Guest) return "訪客";
            if (userType == VIP) return "VIP";

            return userType;
        }

        static public List<string> GetList()
        {
            List<string> ReturnData = new List<string>();
            ReturnData.Add(UserType.Admin);
            ReturnData.Add(UserType.Teacher);
            ReturnData.Add(UserType.Parents);
            ReturnData.Add(UserType.Student);
            ReturnData.Add(UserType.Guest);

            return ReturnData;
        }

        static public List<SelectListItem> GetSelectItem(string defaultSelectValue)
        {
            List<SelectListItem> items = new List<SelectListItem>();

            items.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });
            items.Add(new SelectListItem() { Text = GetDesc(UserType.Admin), Value = UserType.Admin, Selected = UserType.Admin == defaultSelectValue });
            items.Add(new SelectListItem() { Text = GetDesc(UserType.Teacher), Value = UserType.Teacher, Selected = UserType.Teacher == defaultSelectValue });
            items.Add(new SelectListItem() { Text = GetDesc(UserType.Parents), Value = UserType.Parents, Selected = UserType.Parents == defaultSelectValue });
            items.Add(new SelectListItem() { Text = GetDesc(UserType.Student), Value = UserType.Student, Selected = UserType.Student == defaultSelectValue });
            items.Add(new SelectListItem() { Text = GetDesc(UserType.Guest), Value = UserType.Guest, Selected = UserType.Guest == defaultSelectValue });
            items.Add(new SelectListItem() { Text = GetDesc(UserType.VIP), Value = UserType.VIP, Selected = UserType.VIP == defaultSelectValue });

            return items;
        }
    }

    public class TableStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        static public string OK = "1";

        /// <summary>
        /// 異常
        /// </summary>
        static public string NG = "0";

        /// <summary>
        /// 9.停用/已作廢
        /// </summary>
        static public string Disabled = "9";
    }

    public class UserStaus
    {
        /// <summary>
        /// 啟用
        /// </summary>
        static public byte Enabled = 1;

        /// <summary>
        /// 帳號鎖定
        /// </summary>
        static public byte Lock = 7;

        /// <summary>
        /// 停用
        /// </summary>
        static public byte Disable = 8;

        /// <summary>
        /// 失效(畢業，轉出)
        /// </summary>
        static public byte Invalid = 9;

        static public string GetDesc(byte? UserStaus)
        {
            if (UserStaus == Enabled) return "啟用";
            else if (UserStaus == Lock) return "帳號鎖定";
            else if (UserStaus == Disable) return "停用";
            else if (UserStaus == Invalid) return "失效(畢業，轉出)";
            else return "狀態有問題請洽管理員";
        }

        /// <summary>
        /// 正常
        /// </summary>
        static public List<byte?> OkUserStausList = SetOkUserStaus();

        /// <summary>
        /// 不正常 for 查詢  (只能套用在排行榜系列) 。輸入時停用帳號 ，老師要可選的到，因為要幫他代做
        /// </summary>
        static public List<byte?> NGUserStausList = SetNGUserStaus();

        /// <summary>
        /// 不正常 for 輸入
        /// </summary>
        static public List<byte?> NGKeyinUserStausList = SetKeyinNGUserStaus();

        static private List<byte?> SetOkUserStaus()
        {
            List<byte?> ArrOK = new List<byte?>();
            ArrOK.Add(Enabled);
            ArrOK.Add(Lock);

            return ArrOK;
        }

        static private List<byte?> SetNGUserStaus()
        {
            List<byte?> Arr = new List<byte?>();
            Arr.Add(Disable);
            Arr.Add(Invalid);

            return Arr;
        }

        static private List<byte?> SetKeyinNGUserStaus()
        {
            List<byte?> Arr = new List<byte?>();
            Arr.Add(Invalid);

            return Arr;
        }

        static public List<SelectListItem> GetUserStausItemsALL(string defaultSelectValue)
        {
            List<SelectListItem> UserStausItems = new List<SelectListItem>();
            UserStausItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            UserStausItems.Add(new SelectListItem() { Text = GetDesc(Enabled), Value = (Enabled).ToString(), Selected = (Enabled).ToString() == defaultSelectValue });
            UserStausItems.Add(new SelectListItem() { Text = GetDesc(Lock), Value = (Lock).ToString(), Selected = (Lock).ToString() == defaultSelectValue });
            UserStausItems.Add(new SelectListItem() { Text = GetDesc(Disable), Value = (Disable).ToString(), Selected = (Disable).ToString() == defaultSelectValue });
            UserStausItems.Add(new SelectListItem() { Text = GetDesc(Invalid), Value = (Invalid).ToString(), Selected = (Invalid).ToString() == defaultSelectValue });

            return UserStausItems;
        }
    }
}