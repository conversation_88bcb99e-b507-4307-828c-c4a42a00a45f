﻿@model AWAI07EditViewModel
@using EcoolWeb.Util;

@{
    ViewBag.Title = "酷幣定存-試算模擬器";
}

@Html.Partial("_Title_Secondary")

@{
    Html.RenderAction("_BankMenu", new { NowAction = "Simulator" });
}



<div class="card" style="background-color:rgba(227, 236, 159, 0.2);">
    <div class="card-content">
        <span class="card-title text-muted"><b>試算模擬器</b></span>
        <button type="button" id="show" class="btn btn-custom pull-right" aria-label="Left Align">
            <span class="text-danger">公式說明 <span class="glyphicon glyphicon-question-sign" aria-hidden="true"></span></span>
        </button>
    </div><!-- card content -->
    <div class="card-action">
        <div class="row">
            <div class="col-md-7 form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.MATURITY_TYPE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.MATURITY_TYPE,
                            "_RadioButtonList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Edit.MATURITY_TYPE)).ToHtmlString(),
                                RadioItems = AWAT10.MATURITY_TYPE_Val.SelectItem(Model.Edit != null ? Model.Edit.MATURITY_TYPE : null),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "changeMaturity_Type();"
                            })
                        @Html.ValidationMessageFor(m => m.Edit.MATURITY_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group" id="acctCodeType">
                    @Html.LabelFor(m => m.Edit.ACCT_CODE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Edit.ACCT_CODE,
                            "_RadioButtonList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Edit.ACCT_CODE)).ToHtmlString(),
                                RadioItems = AWAT10.AcctCodeVal.SelectItem(Model.Edit != null ? Model.Edit.ACCT_CODE : null),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue
                            })
                        @Html.ValidationMessageFor(m => m.Edit.ACCT_CODE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.Edit.PERIOD_TYPE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        <div class="input-group">
                            @Html.DropDownListFor(m => m.Edit.PERIOD_TYPE, (List<SelectListItem>)ViewBag.PeriodTypeItem, new { @class = "form-control input-md" })
                            <span class="input-group-btn">
                                <button class="btn btn-default btn-md colorbox " type="button" href="@Url.Action("_RateView", (string)ViewBag.BRE_NO)">利率表</button>
                            </span>
                        </div>
                        @Html.ValidationMessageFor(m => m.Edit.PERIOD_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">預計存入酷幣</label>
                    <div class="col-md-9">
                        <input type="number" name="name" id="point" min="0" class="form-control" value="" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="col-md-5">
                <div class="row">
                    <div>
                        <div class="row1">
                            <div class="col-md-12">
                                <div class="num-pad">
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                1
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                2
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                3
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                4
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                5
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                6
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                7
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                8
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                9
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">C</div>
                                        </div>
                                    </div>
                                    <div class="span4">
                                        <div class="num">
                                            <div class="txt">
                                                0
                                            </div>
                                        </div>
                                    </div>
                                    <div class="span4" style="pointer-events:none">
                                        <div class="num">
                                            <div class="txt">
                                                
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <hr style="margin-top:10px;margin-bottom:10px;" />
        <div class="row">
            <div class="form-group">
                @Html.Label("現在日期", htmlAttributes: new { @class = "col-md-3 control-label" })
                <div class="col-md-9">
                    @DateTime.Now.ToString("yyyy/MM/dd")
                </div>
            </div><br /> <br />
            <div class="form-group">
                @Html.Label("預計解約日期", htmlAttributes: new { @class = "col-md-3 control-label" })
                <div class="col-md-9">
                    <input type="text" id="testCloseDate" class="form-control" />
                </div>
            </div>
            <br /> <br />
            <div class="form-group">
                @Html.Label("本利和", htmlAttributes: new { @class = "col-md-3 control-label text-danger" })
                <div class="col-md-7">
                    <div class="input-group">
                        @Html.Editor("PrincipleAndInterestAmt", new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "到期後本利和", @disabled = "true" } })
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button" onclick="onTest()">試算</button>
                        </span>
                    </div><!-- /input-group -->
                </div>
            </div>
            <br /> <br />
            <div class="form-group">
                <div class="col-md-12">
                    <div id="caculateResultMsg">

                    </div><!-- /input-group -->
                </div>
                <br />
                <div class="col-md-12">
                    <div id="generateInterest">
                    
                    </div><!-- /input-group -->
                </div>
            </div>
        </div>
    </div><!-- card actions -->
    <div class="card-reveal">
        <span class="card-title text-danger">計算公式說明</span> <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
        <p><label>1. 到期自動續存 - 本息續存 (到期後)</label></p>

        <div class="form-group">
            <div class="col-md-12 col-md-offset-1">
                利息計算公式：　\(本金 \times (1+ {利率\% \over 12} )^月 - 本金 \)
            </div>
        </div>

        <p><label>2. 到期自動續存 - 存本取息 (到期後)</label></p>
        <div class="form-group">
            <div class="col-md-12 col-md-offset-1">
                利息計算公式：　\(本金 \times {利率\% \over 12} \times 月 \)
            </div>
        </div>
        <p><label>3. 到期解約</label></p>
        <div class="form-group">
            <div class="col-md-12 col-md-offset-1">
                利息計算公式：　\(本金 \times {利率\% \over 12} \times 期別(月) \)
            </div>
        </div>

        <p><label>4. 備註(a): 提早解約存款金額利率會打八折</label></p>
        <div class="form-group">
            <div class="col-md-12 col-md-offset-1">
                利率計算公式：　\(利率\% \times 0.8\)
            </div>
        </div>
        <p><label>5. 備註(b): 本利和</label></p>
        <div class="form-group">
            <div class="col-md-12 col-md-offset-1">
                本利和 = 本金 + 利息
            </div>
        </div>
    </div><!-- card reveal -->
</div>


@section scripts{
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>
    <script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
    @*<script src="~/Scripts/moment.js"></script>*@
    <script src="~/Scripts/moment.min.js"></script>
    <script type="text/javascript" src="@Url.Content("~/Content/MathJax-2.7.5/MathJax.js?config=TeX-MML-AM_CHTML")" async></script>

    <script>

        $(function () {
            $("#testCloseDate").datepicker({ maxDate: "+10y"});

            $(".colorbox").colorbox({ iframe: true, width: "300px", height: "300px" });

            $('#show').on('click', function () {
                $('.card-reveal').slideToggle('slow');
            });

            $('.card-reveal .close').on('click', function () {
                $('.card-reveal').slideToggle('slow');
            });

            $('.num').click(function () {
                var num = $(this);
                var text = $.trim(num.find('.txt').clone().children().remove().end().text());

                var point = $('#point');
                $(point).val(point.val() + text);
                if (num.find('.txt').text() == "C") {
                    $(point).val('');
                }
            });

            // 定存期別下拉改變
            $('#Edit_PERIOD_TYPE').on('change', function () {
                // 到期處理方式 - 1:到期自動續存 2:到期解約
                var selectType = $('input[name="@Html.NameFor(m=>m.Edit.MATURITY_TYPE)"]:checked').val();

                var current = new Date();
                var addMonth = $(this).val();
                var endDateMoment = moment(current);
                endDateMoment.add(addMonth, 'months');
                $("#testCloseDate").val(endDateMoment.year() + '/' + (endDateMoment.month() + 1) + '/' + endDateMoment.date());
                if (selectType == 2) {
                    $("#testCloseDate").datepicker("destroy");
                    $("#testCloseDate").datepicker({ maxDate: "+" + addMonth + "M" });
                }
            });
        });

        // 預設第一項
        $('input[name="@Html.NameFor(m=>m.Edit.MATURITY_TYPE)"]:first').prop("checked", true);
        $('input[name="@Html.NameFor(m=>m.Edit.ACCT_CODE)"]:first').prop("checked", true);
        function changeMaturity_Type() {
            // 到期處理方式 - 1:到期自動續存 2:到期解約
            var selectType = $('input[name="@Html.NameFor(m=>m.Edit.MATURITY_TYPE)"]:checked').val();

            if (selectType == 2) {
                $("#acctCodeType").hide();
                $('#Edit_PERIOD_TYPE').trigger('change');
            }
            else {
                $("#acctCodeType").show();
                $("#testCloseDate").datepicker("destroy");
                $("#testCloseDate").datepicker({ maxDate: "+10y" });
            }
        }

        function onTest() {

            var MATURITY_TYPE = $('input[name="@(Html.NameFor(m => m.Edit.MATURITY_TYPE))"]:checked').val();
            var ACCT_CODE = $('input[name="@(Html.NameFor(m => m.Edit.ACCT_CODE))"]:checked').val();
            var PERIOD_TYPE = $('#@Html.IdFor(m=>m.Edit.PERIOD_TYPE)').find(":selected").val();
            var AMT = $('#point').val();
            var TestCloseDate = $("#testCloseDate").val();
            var InterestPercent = ""; // 計算後的利率%

            if (ACCT_CODE == 'undefined' ) {
                alert('存款類型未輸入');
               return false;
             }

            if (PERIOD_TYPE == '') {
                alert('定存期別未輸入');
                return false;
            }
            if (AMT == '') {
                alert('定存酷幣未輸入');
                return false;
            }
            if (AMT > 999999999) {
                alert('定存酷幣金額過高');
                return false;
            }
            if (TestCloseDate == '') {
                alert('解約日期未輸入');
                return false;
            }
            var newTestCloseDate = new Date(TestCloseDate);
            var newNowDate = new Date();
            if (newTestCloseDate < newNowDate) {
                alert('解約日期需大於現在日期');
                return false;
            }
            if (newTestCloseDate.getFullYear() > newNowDate.getFullYear() + 10 + 1) {
                alert('解約日期不能超過10年');
                return false;
            }

            if (isNaN(AMT) || AMT < 0 ) {
                alert('定存酷幣請輸入數字');
                        return false;
            }

            $.ajax({
                url: "@(Url.Action("InterestRatesTest", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    MATURITY_TYPE: MATURITY_TYPE,
                    ACCT_CODE: ACCT_CODE,
                    PERIOD_TYPE: PERIOD_TYPE,
                    AMT: AMT,
                    TestCloseDate: TestCloseDate,
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    console.log(data);
                    var res = data;
            
                    if (res.Success == 'false') {
                        alert(res.Error);
                    }
                    else {
                        var finalAmount = parseFloat(res.PrincipleAndInterestAmt);
                        $('#PrincipleAndInterestAmt').val(finalAmount);
            
                        // 計算結果
                        var result = "";
                        result = res.MathJaxFunction;
                        console.log(result);
                        $("#caculateResultMsg").html(result);
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);

                        // 利息
                        $("#generateInterest").html( "<span class='text-danger'>產生利息：</span> " + (finalAmount - AMT));
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }
            
            });
        }


        // 月份差數
        function monthDiff(d1, d2) {
            var months;
            months = (d2.getFullYear() - d1.getFullYear()) * 12;
            months -= d1.getMonth();
            if (d2.getDate() < d1.getDate()) {
                months--;
            }
            months += d2.getMonth();
            return months <= 0 ? 0 : months;
        }
    </script>
}




@section css{
    <link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
    <link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />

    <style>
        .card {
            font-family: 'Roboto', sans-serif;
            margin-top: 5px;
            position: relative;
            -webkit-box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
            -moz-box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
            box-shadow: 4 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        }

            .card .card-content {
                padding: 10px;
            }

                .card .card-content .card-title, .card-reveal .card-title {
                    font-size: 20px;
                    font-weight: 200;
                }

            .card .card-action {
                padding: 20px;
                border-top: 1px solid rgba(160, 160, 160, 0.2);
            }

                .card .card-action a {
                    font-size: 15px;
                    color: #ffab40;
                    text-transform: uppercase;
                    margin-right: 20px;
                    -webkit-transition: color 0.3s ease;
                    -moz-transition: color 0.3s ease;
                    -o-transition: color 0.3s ease;
                    -ms-transition: color 0.3s ease;
                    transition: color 0.3s ease;
                }

                    .card .card-action a:hover {
                        color: #ffd8a6;
                        text-decoration: none;
                    }

            .card .card-reveal {
                padding: 20px;
                position: absolute;
                background-color: #FFF;
                width: 100%;
                overflow-y: auto;
                /*top: 0;*/
                left: 0;
                bottom: 0;
                height: 100%;
                z-index: 1;
                display: none;
            }

                .card .card-reveal p {
                    color: rgba(0, 0, 0, 0.71);
                    margin: 20px;
                }

        .btn-custom {
            background-color: transparent;
            font-size: 18px;
        }


        .span4 {
            width: 50px;
            float: left;
            margin: 0 8px 10px 8px;
        }


        .num-pad {
            padding-left: 15px;
        }


        .num {
            border: 1px solid #9e9e9e;
            -webkit-border-radius: 999px;
            border-radius: 999px;
            -moz-border-radius: 999px;
            height: 45px;
            width: 45px;
            background-color: #fff;
            color: #333;
            cursor: pointer;
        }

            .num:hover {
                background-color: #9e9e9e;
                color: #fff;
                transition-property: background-color .2s linear 0s;
                -moz-transition: background-color .2s linear 0s;
                -webkit-transition: background-color .2s linear 0s;
                -o-transition: background-color .2s linear 0s;
            }

        .txt {
            font-size: 24px;
            text-align: center;
            margin-top: 8px;
            font-family: 'Lato', sans-serif;
            line-height: 30px;
            color: #333;
        }

        .small {
            font-size: 15px;
        }

        .btn {
            font-weight: bold;
            -webkit-transition: .1s ease-in background-color;
            -webkit-font-smoothing: antialiased;
            letter-spacing: 1px;
        }

            .btn:hover {
                transition-property: background-color .2s linear 0s;
                -moz-transition: background-color .2s linear 0s;
                -webkit-transition: background-color .2s linear 0s;
                -o-transition: background-color .2s linear 0s;
            }

        .form-group {
            margin-bottom: 12px;
        }

        .spanicons {
            width: 72px;
            float: left;
            text-align: center;
            margin-top: 40px;
            color: #9e9e9e;
            font-size: 30px;
            cursor: pointer;
        }

            .spanicons:hover {
                color: #3498db;
                transition-property: color .2s linear 0s;
                -moz-transition: color .2s linear 0s;
                -webkit-transition: color .2s linear 0s;
                -o-transition: color .2s linear 0s;
            }

        .active {
            color: #3498db;
        }
    </style>
}