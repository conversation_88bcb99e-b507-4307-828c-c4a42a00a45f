//閱讀護照榮譽
.read-books {
    &-title {
        display: inline-block;
        background-image: url(../images/read-books-title.png);
        background-repeat: no-repeat;
        background-size: 100% auto;
        font-size: 0;
        height: 2.3rem;
        max-width: 159px;
        width: 100%;
    }

    &-lv {
        display: inline-block;
        position: relative;
        margin: 0.15rem;
        width: 1.5rem;
        height: 2rem;
        font-size: 0;
        background-image: url(../images/read-books-bg.png);
        background-repeat: no-repeat;
        background-size: 100% auto;

        &::before {
            content: "";
            display: inline-block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-image: url(../images/read-books-color.png);
            background-repeat: no-repeat;
            background-size: auto 100%;
            background-position: center center;
        }

        &::after {
            content: "";
            display: inline-block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-image: url(../images/read-books-bgup.png);
            background-repeat: no-repeat;
            background-size: auto 100%;
            background-position: center center;
        }

        @include media-breakpoint-up(lg) {
            width: 36px;
            height: 47px;
        }

        span {
            font-size: 1rem;
            font-weight: 600;
            font-family: -webkit-body;
            display: block;
            padding: 0;
            position: relative;
            z-index: 1;
            transform: scale(0.4);
            @include media-breakpoint-up(lg) {
                padding: 0.425rem;
                transform: scale(0.7);
            }
        }

        &1 {
            @extend .read-books-lv;
        }

        &2 {
            @extend .read-books-lv;
            &::before {
                filter: hue-rotate(310deg) brightness(0.6);
            }
        }
        &3 {
            @extend .read-books-lv;
            &::before {
                filter: hue-rotate(189deg) brightness(0.7);
            }
        }
        &4 {
            @extend .read-books-lv;
            &::before {
                filter: hue-rotate(157deg) brightness(0.7);
            }
        }
        &5 {
            @extend .read-books-lv;
            &::before {
                filter: hue-rotate(314deg) brightness(0.5) saturate(2);
            }
        }
        &6 {
            @extend .read-books-lv;
            &::before {
                filter: hue-rotate(281deg) brightness(0.5) saturate(2);
            }
        }
    }
}
.opacity-50 {
    opacity: 0.5;
}