﻿@model GameBatchWorkIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BatchWork" });
    }
}

@using (Html.BeginForm("BatchWork", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.BatchWorkType)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            請點選功能
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group text-center">
                    <br />
                    <br />
                    @foreach (var item in ViewBag.BatchWorkTypeItem as List<SelectListItem>)
                    {
                        <div class="col-md-3">
                            <a class="btn-block btn btn-default" onclick="OnBatchWork('@item.Value')" role="button">@item.Text</a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        function OnBatchWork(Value) {

            $('@Html.IdFor(m=>m.BatchWorkType)').val(Value)

            if (Value == '@ECOOL_APP.GameService.BatchWorkTypeVal.BatchApplyStudent') {
                $(targetFormID).attr("action", "@Url.Action("BatchApplyStudentView", (string)ViewBag.BRE_NO)")
            }
            else if (Value == '@ECOOL_APP.GameService.BatchWorkTypeVal.BatchApplyCard')
            {
                $(targetFormID).attr("action", "@Url.Action("BatchApplyCardView", (string)ViewBag.BRE_NO)")
            }
            else if (Value == '@ECOOL_APP.GameService.BatchWorkTypeVal.AddApplyCard')
            {
                $(targetFormID).attr("action", "@Url.Action("OneApplyCardView", (string)ViewBag.BRE_NO)")
            }
            else if (Value == '@ECOOL_APP.GameService.BatchWorkTypeVal.EditApplyCard')
            {
                $(targetFormID).attr("action", "@Url.Action("EditApplyCard", (string)ViewBag.BRE_NO)")
            }

            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("GameIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}