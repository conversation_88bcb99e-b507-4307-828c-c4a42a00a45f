﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'en-ca', {
	border: 'Border size',
	caption: 'Caption',
	cell: {
		menu: 'Cell',
		insertBefore: 'Insert Cell Before',
		insertAfter: 'Insert Cell After',
		deleteCell: 'Delete Cells',
		merge: 'Merge Cells',
		mergeRight: 'Merge Right',
		mergeDown: 'Merge Down',
		splitHorizontal: 'Split Cell Horizontally',
		splitVertical: 'Split Cell Vertically',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'Background Color',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'Cell padding',
	cellSpace: 'Cell spacing',
	column: {
		menu: 'Column',
		insertBefore: 'Insert Column Before',
		insertAfter: 'Insert Column After',
		deleteColumn: 'Delete Columns'
	},
	columns: 'Columns',
	deleteTable: 'Delete Table',
	headers: 'Headers',
	headersBoth: 'Both',
	headersColumn: 'First column',
	headersNone: 'None',
	headersRow: 'First Row',
	invalidBorder: 'Border size must be a number.',
	invalidCellPadding: 'Cell padding must be a number.',
	invalidCellSpacing: 'Cell spacing must be a number.',
	invalidCols: 'Number of columns must be a number greater than 0.',
	invalidHeight: 'Table height must be a number.',
	invalidRows: 'Number of rows must be a number greater than 0.',
	invalidWidth: 'Table width must be a number.',
	menu: 'Table Properties',
	row: {
		menu: 'Row',
		insertBefore: 'Insert Row Before',
		insertAfter: 'Insert Row After',
		deleteRow: 'Delete Rows'
	},
	rows: 'Rows',
	summary: 'Summary',
	title: 'Table Properties',
	toolbar: 'Table',
	widthPc: 'percent',
	widthPx: 'pixels',
	widthUnit: 'width unit' // MISSING
} );
