﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CkEditIndexListViewModel
    {

        /// <summary>
        ///流水號
        /// </summary>
        [DisplayName("流水號")]
        public string REF_NO { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///上傳類別
        /// </summary>
        [DisplayName("上傳類別")]
        public string REF_TYPE { get; set; }

        /// <summary>
        ///標題
        /// </summary>
        [DisplayName("標題")]
        public string SUBJECT { get; set; }

        /// <summary>
        ///檔名
        /// </summary>
        [DisplayName("檔名")]
        public string FILE { get; set; }
        public string FILE_PATH { get; set; }
        /// <summary>
        ///檔案大小
        /// </summary>
        [DisplayName("檔案大小")]
        public long FILE_SIZE { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("建立人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///修改日期
        /// </summary>
        [DisplayName("修改日期")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }
    }
}
