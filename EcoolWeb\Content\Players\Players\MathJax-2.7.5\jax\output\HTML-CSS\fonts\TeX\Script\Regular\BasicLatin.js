/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Script/Regular/BasicLatin.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Script,{32:[0,0,250,0,0],65:[717,8,803,35,1016],66:[708,28,908,31,928],67:[728,26,666,26,819],68:[708,31,774,68,855],69:[707,8,562,46,718],70:[735,36,895,39,990],71:[717,37,610,12,738],72:[717,36,969,29,1241],73:[717,17,809,59,946],74:[717,314,1052,92,1133],75:[717,37,914,29,1204],76:[717,17,874,14,1035],77:[721,50,1080,30,1216],78:[726,36,902,29,1208],79:[707,8,738,96,805],80:[716,37,1013,90,1031],81:[717,17,883,54,885],82:[717,17,850,-2,887],83:[708,36,868,29,1016],84:[735,37,747,92,996],85:[717,17,800,55,960],86:[717,17,622,56,850],87:[717,17,805,46,1026],88:[717,17,944,103,1131],89:[716,17,710,57,959],90:[717,16,821,83,1032]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Regular/BasicLatin.js");
