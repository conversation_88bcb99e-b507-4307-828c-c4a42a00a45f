﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Data.Entity.Validation;
using System.Collections;
using System.Data.Entity;
using System.Net;
using com.ecool.service;
using System.ComponentModel.DataAnnotations;
using System.Transactions;
using System.IO;
using System.Text.RegularExpressions;
using ECOOL_APP.com.ecool.util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI07Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ADDI07";

        public static string ImgPath = "ADDT15IMG";

        /// <summary>
        /// 校外榮譽
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult QUERY(ADDI07IndexViewModel model)
        {
            if (model == null) model = new ADDI07IndexViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }
            //IQueryable<ADDT15> ADDT15List = db.ADDT15.Where(a => a.SCHOOL_NO == SchoolNO && a.APPLY_STATUS != "9");
            int StudentUploadIMG = 0;
           
            StudentUploadIMG = db.BDMT02_REF.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.BRE_NO == "ADDI06" && x.DATA_CODE == "IMAGE_Student" && x.CONTENT_VAL == "true").Count();
            if (StudentUploadIMG > 0)
            {
                ViewBag.StudentUploadIMG = true;
            }
            else
            {
                ViewBag.StudentUploadIMG = false;
            }
            GetADDT15(model, SchoolNO);

            string UseYN = "N";
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "ADD", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableInsert = false;
            }
            else
            {
                ViewBag.VisableInsert = true;
            }
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "Delete", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableDelete = false;
            }
            else
            {
                ViewBag.VisableDelete = true;
            }
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "MODIFY", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableModify = false;
            }
            else
            {
                ViewBag.VisableModify = true;
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            return View(model);
        }

        private void GetADDT15(ADDI07IndexViewModel model, string SchoolNO, int PageSize = 20)
        {
            List<ImageModifyTemp> ImageModifyTempInfo = null;
            IQueryable<ADDT15> ADDT15List =
                        from a15 in db.ADDT15
                        join h01 in db.HRMT01 on new { a15.USER_NO, a15.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                        from h01 in h01_join.DefaultIfEmpty()
                        where a15.SCHOOL_NO == SchoolNO && a15.APPLY_STATUS != "9" && h01.USER_STATUS != UserStaus.Disable && h01.USER_STATUS != UserStaus.Invalid
                        select a15;

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                ADDT15List = ADDT15List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim())
                || a.USERNAME.Contains(model.whereKeyword.Trim())
                || a.TNAME.Contains(model.whereKeyword.Trim())
                || a.OAWARD_ITEM.Contains(model.whereKeyword.Trim())
                || a.OAWARD_SCORE.Contains(model.whereKeyword.Trim())
                );
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO == model.whereCLASS_NO.Trim()).Select(a => a.USER_NO).ToList();
                ADDT15List = ADDT15List.Where(a => HrList.Contains(a.USER_NO ));

                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO.Substring(0, 1) == model.whereGrade).Select(a => a.USER_NO).ToList();
                ADDT15List = ADDT15List.Where(a => HrList.Contains(a.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                var arrUSER_NO = model.whereUserNo.Split(',');
                ADDT15List = ADDT15List.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);
            }

            switch (model.OrdercColumn)
            {
                case "CREATEDATE":
                    ADDT15List = ADDT15List.OrderByDescending(a => a.CREATEDATE);
                    break;

                case "CLASS_NO":
                    ADDT15List = ADDT15List.OrderByDescending(a => a.CLASS_NO);
                    break;

                case "USERNAME":
                    ADDT15List = ADDT15List.OrderByDescending(a => a.USERNAME);
                    break;

                default:
                    ADDT15List = ADDT15List.OrderByDescending(a => a.CREATEDATE);
                    break;
            }

            model.ADDT15List = ADDT15List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            foreach (var item in model.ADDT15List.Where(x => x.ImgSourceNO != null).ToList())
            {
                ImageModifyTempInfo = new List<ImageModifyTemp>();

                ImageModifyTemp Images = new ImageModifyTemp();
                Images = db.ImageModifyTemp.Where(x => x.Source_ID == item.ImgSourceNO).FirstOrDefault();
                ImageModifyTempInfo.Add(Images);


            }
            if (ImageModifyTempInfo != null)
            {
                model.ImageModifyTempList = ImageModifyTempInfo;
            }
        }

        public ActionResult PrintQuery(ADDI07IndexViewModel model)
        {
            if (model == null) model = new ADDI07IndexViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();

            GetADDT15(model, SchoolNO, int.MaxValue);

            return View(model);
        }

        [CheckPermission] //檢查權限
        public ActionResult ADD()
        {
            var ClassNoitems = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled &&　u.USER_TYPE=="S").
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);

            ViewBag.ClassNoItem = ClassNoitems;
            ViewBag.Scoreitems = GetScoreitems();

            ADDT15 aDDT15 = new ADDT15();

            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10 };

            //預設值
            aDDT15.CASH = 0;
            //aDDT01.SHARE_YN = "n";

            return View(aDDT15);
        }

        static public List<SelectListItem> GetScoreitems(string SelectedText = "")
        {
            List<SelectListItem> Scoreitems = new List<SelectListItem>();
            Scoreitems.Add(new SelectListItem() { Text = "請選擇成績", Value = "", Selected = SelectedText == "" });
            Scoreitems.Add(new SelectListItem() { Text = "行政區特優(第一名)", Value = "200", Selected = SelectedText == "行政區特優(第一名)" });
            Scoreitems.Add(new SelectListItem() { Text = "行政區優等(第二名)", Value = "150", Selected = SelectedText == "行政區特優(第二名)" });
            Scoreitems.Add(new SelectListItem() { Text = "行政區佳作(第三名)", Value = "100", Selected = SelectedText == "行政區特優(第三名)" });
            Scoreitems.Add(new SelectListItem() { Text = "行政區入選", Value = "50", Selected = SelectedText == "行政區入選" });
            Scoreitems.Add(new SelectListItem() { Text = "臺北市特優(第一名)", Value = "250", Selected = SelectedText == "臺北市特優(第一名)" });
            Scoreitems.Add(new SelectListItem() { Text = "臺北市優等(第二名)", Value = "200", Selected = SelectedText == "臺北市特優(第二名)" });
            Scoreitems.Add(new SelectListItem() { Text = "臺北市佳作(第三名)", Value = "150", Selected = SelectedText == "臺北市特優(第三名)" });
            Scoreitems.Add(new SelectListItem() { Text = "臺北市入選", Value = "100", Selected = SelectedText == "臺北市入選" });
            Scoreitems.Add(new SelectListItem() { Text = "全國特優(第一名)", Value = "300", Selected = SelectedText == "全國特優(第一名)" });
            Scoreitems.Add(new SelectListItem() { Text = "全國優等(第二名)", Value = "250", Selected = SelectedText == "全國特優(第二名)" });
            Scoreitems.Add(new SelectListItem() { Text = "全國佳作(第三名)", Value = "200", Selected = SelectedText == "全國特優(第三名)" });
            Scoreitems.Add(new SelectListItem() { Text = "全國入選", Value = "150", Selected = SelectedText == "全國入選" });
            Scoreitems.Add(new SelectListItem() { Text = "國際特優(第一名)", Value = "350", Selected = SelectedText == "國際特優(第一名)" });
            Scoreitems.Add(new SelectListItem() { Text = "國際優等(第二名)", Value = "300", Selected = SelectedText == "國際特優(第二名)" });
            Scoreitems.Add(new SelectListItem() { Text = "國際佳作(第三名)", Value = "250", Selected = SelectedText == "國際特優(第三名)" });
            Scoreitems.Add(new SelectListItem() { Text = "國際入選", Value = "200", Selected = SelectedText == "國際入選" });
            Scoreitems.Add(new SelectListItem() { Text = "特殊獎勵", Value = "300", Selected = SelectedText == "特殊獎勵" });
            Scoreitems.Add(new SelectListItem() { Text = "鄉鎮級特優(第一名)", Value = "", Selected = SelectedText == "鄉鎮級特優(第一名)" });
            Scoreitems.Add(new SelectListItem() { Text = "鄉鎮級優等(第二名)", Value = "", Selected = SelectedText == "鄉鎮級優等(第二名)" });
            Scoreitems.Add(new SelectListItem() { Text = "鄉鎮級佳作(第三名)", Value = "", Selected = SelectedText == "鄉鎮級佳作(第三名)" });
            Scoreitems.Add(new SelectListItem() { Text = "鄉鎮級入選", Value = "", Selected = SelectedText == "鄉鎮級入選" });
            Scoreitems.Add(new SelectListItem() { Text = "區級特優(第一名)", Value = "", Selected = SelectedText == "區級特優(第一名)" });
            Scoreitems.Add(new SelectListItem() { Text = "區級優等(第二名)", Value = "", Selected = SelectedText == "區級優等(第二名)" });
            Scoreitems.Add(new SelectListItem() { Text = "區級佳作(第三名)", Value = "", Selected = SelectedText == "區級佳作(第三名)" });
            Scoreitems.Add(new SelectListItem() { Text = "區級入選", Value = "", Selected = SelectedText == "區級入選" });
            Scoreitems.Add(new SelectListItem() { Text = "縣市級特優(第一名)", Value = "", Selected = SelectedText == "縣市級特優(第一名)" });
            Scoreitems.Add(new SelectListItem() { Text = "縣市級優等(第二名)", Value = "", Selected = SelectedText == "縣市級優等(第二名)" });
            Scoreitems.Add(new SelectListItem() { Text = "縣市級佳作(第三名)", Value = "", Selected = SelectedText == "縣市級佳作(第三名)" });
            Scoreitems.Add(new SelectListItem() { Text = "縣市級入選", Value = "", Selected = SelectedText == "縣市級入選" });
            return Scoreitems;
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ADD([Bind(Include = "OAWARD_ID,SCHOOL_NO,CREATEDATE,SYEAR,SEMESTER,SNAME,CLASS_NO,USER_NO,USERNAME,SEX,OAWARD_ITEM,OAWARD_SCORE,CASH,REMARK")] ADDT15 aDDT15, HttpPostedFileBase files)
        {
            string Message = string.Empty;

            if (files != null && files.ContentLength > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                }
            }

            //產是要 Push 批號
            string BATCH_ID = PushService.CreBATCH_ID();

            if (ModelState.IsValid)
            {
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                aDDT15.SCHOOL_NO = user.SCHOOL_NO;
                aDDT15.CREATEDATE = DateTime.Now;
                aDDT15.SEMESTER = Convert.ToByte(Semesters);
                aDDT15.SYEAR = Convert.ToByte(SYear);
                aDDT15.TNAME = user.NAME;
                aDDT15.APPLY_STATUS = "2";
                var hr = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == aDDT15.USER_NO).FirstOrDefault();

                aDDT15.USERNAME = hr.NAME;
                aDDT15.SNAME = hr.SNAME;

                db.ADDT15.Add(aDDT15);

                using (TransactionScope tx = new TransactionScope())
                {
                    try
                    {
                        db.SaveChanges();
                    }
                    catch (DbEntityValidationException ex)
                    {
                        var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                        var getFullMessage = string.Join("; ", entityError);
                        var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);

                        TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
                        return View(aDDT15);
                    }
                    ImageModifyTemp temp = new ImageModifyTemp();
                    List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                    if (files != null && files.ContentLength >= 0)
                    {
                        if (FileHelper.DoLoadFile(files, new FileHelper().GetMapPathImg(aDDT15.SCHOOL_NO, ImgPath), ref Message, true, aDDT15.OAWARD_ID.ToString()) == false)
                        {
                            TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + Message;
                            return View(aDDT15);
                        }

                        temp.Source_ID = Guid.NewGuid().ToString("N");
                        temp.SCHOOL_NO = user.SCHOOL_NO;
                        temp.USER_NO = user.USER_NO;
                        temp.Source = "ADDI15";
                        temp.CHG_DATE = DateTime.Now;
                        temp.Action = "Modify";
                        db.ImageModifyTemp.Add(temp);
                        aDDT15.IMG_FILE = Path.GetFileName(files.FileName);
                        aDDT15.ImgSourceNO = temp.Source_ID;
                    }

                    ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(aDDT15.CASH), aDDT15.SCHOOL_NO, aDDT15.USER_NO, "ADDI07", aDDT15.OAWARD_ID.ToString(), StringHelper.LeftStringR("校外榮譽 - " + aDDT15.OAWARD_ITEM + ' ' + aDDT15.OAWARD_SCORE, 47), true, ref db, "", "",ref valuesList);

                    //塞資料
                    GrePushData(BATCH_ID, aDDT15, ref db);

                    try
                    {
                        db.SaveChanges();
                        TempData["StatusMessage"] = "新增完成";
                    }
                    catch (DbEntityValidationException ex)
                    {
                        var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                        var getFullMessage = string.Join("; ", entityError);
                        var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                        TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
                        return View(aDDT15);
                    }

                    tx.Complete();
                }

                //Push
                PushHelper.ToPushServer(BATCH_ID);

                return RedirectToAction("Query");
            }

            var ClassNoitems = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled && u.USER_TYPE == "S").
             Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO, Selected = x.CLASS_NO == aDDT15.CLASS_NO }).Distinct().OrderBy(o => o.Value);

            ViewBag.ClassNoItem = ClassNoitems;
            ViewBag.Scoreitems = GetScoreitems(aDDT15.OAWARD_SCORE);
            //給予e酷幣選項
            ViewBag.CashArray = new short[] { 50, 40, 30, 25, 20, 15, 10, 5, 0, -5, -10 };

            return View(aDDT15);
        }
        [HttpPost]
 

        [ValidateAntiForgeryToken]
        public ActionResult MODIFY1([Bind(Include = "OAWARD_ID,SCHOOL_NO,USER_NO,OAWARD_ITEM,OAWARD_SCORE,REMARK,CASH")] ADDT15 aDDT15, HttpPostedFileBase files)
        {
            string Message = string.Empty;

            if (files != null && files.ContentLength > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                }
            }

            if (ModelState.IsValid == false) return View(aDDT15);

            ADDT15 oldADDT15 = db.ADDT15.Find(aDDT15.OAWARD_ID);
            if (aDDT15 == null)
            {
                return HttpNotFound();
            }
           
            oldADDT15.UPDATEDATE = DateTime.Now;

            if (files != null && files.ContentLength >= 0)
            {
                if (FileHelper.DoLoadFile(files, new FileHelper().GetMapPathImg(oldADDT15.SCHOOL_NO, ImgPath), ref Message, true, oldADDT15.OAWARD_ID.ToString()) == false)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + Message;
                    return RedirectToAction("Query");
                }

                oldADDT15.IMG_FILE = Path.GetFileName(files.FileName);
            }

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "修改完成";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }

            ADDI07IndexViewModel model = new ADDI07IndexViewModel();
            model.whereUserNo = user.USER_NO;
            return RedirectToAction("QUERY",new { whereUserNo = user.USER_NO });
        }
        /// <summary>
        /// PushData
        /// </summary>
        /// <param name="BATCH_ID"></param>
        /// <param name="aDDT15"></param>
        /// <param name="db"></param>
        public void GrePushData(string BATCH_ID, ADDT15 aDDT15, ref ECOOL_DEVEntities db)
        {
            string BODY_TXT = "校外榮譽，優良表現:" + aDDT15.OAWARD_ITEM + "，成績:" + aDDT15.OAWARD_SCORE + "，獎勵點數:" + aDDT15.CASH.ToString();

            PushService.InsertPushDataParents(BATCH_ID, aDDT15.SCHOOL_NO, aDDT15.USER_NO, "", BODY_TXT, "", Bre_NO, "ADD", aDDT15.OAWARD_ID.ToString(), "", false, ref db);
            PushService.InsertPushDataMe(BATCH_ID, aDDT15.SCHOOL_NO, aDDT15.USER_NO, "", BODY_TXT, "", Bre_NO, "ADD", aDDT15.OAWARD_ID.ToString(), "", false, ref db);
        }

        [HttpPost]
        [Display(Name = "OAWARD_ID")]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult MODIFY([Bind(Include = "OAWARD_ID,SCHOOL_NO,USER_NO,OAWARD_ITEM,OAWARD_SCORE,REMARK,CASH")] ADDT15 aDDT15, HttpPostedFileBase files)
        {
            string Message = string.Empty;

            if (files != null && files.ContentLength > 0)
            {
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳圖片格式為jpg、jpeg、png、gif、bmp");
                }
            }
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (ModelState.IsValid == false) return View(aDDT15);

            ADDT15 oldADDT15 = db.ADDT15.Find(aDDT15.OAWARD_ID);
            if (aDDT15 == null)
            {
                return HttpNotFound();
            }
            if (aDDT15.CASH != oldADDT15.CASH)
            {
                ECOOL_APP.CashHelper.AddCash(user, -(Convert.ToInt32(oldADDT15.CASH)), oldADDT15.SCHOOL_NO, oldADDT15.USER_NO, "ADDI06", oldADDT15.OAWARD_ID.ToString(), "校外榮譽-修改", true, ref db, "", "",ref valuesList);
                db.SaveChanges();
                string BATCH_ID = PushService.CreBATCH_ID();
                string BODY_TXT = "校外榮譽被修改，具體事蹟: " + oldADDT15.OAWARD_ITEM + "-、減少酷幣點數" + (oldADDT15.CASH).ToString() + "數";
                PushService.InsertPushDataMe(BATCH_ID, oldADDT15.SCHOOL_NO, oldADDT15.USER_NO, "", BODY_TXT, "", "ADDI06", "Delete", oldADDT15.OAWARD_ID.ToString(), "", false, ref db);
                PushHelper.ToPushServer(BATCH_ID);
                ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(aDDT15.CASH), aDDT15.SCHOOL_NO, aDDT15.USER_NO, "ADDI06", aDDT15.OAWARD_ID.ToString(), "校外榮譽-修改", true, ref db, "", "",ref valuesList);
                db.SaveChanges();
                BATCH_ID = PushService.CreBATCH_ID();
                string BODY_TXTafter = "校外榮譽被修改，具體事蹟: " + oldADDT15.OAWARD_ITEM + "-、修改後的幣點數" + (oldADDT15.CASH).ToString() + "數";
                PushService.InsertPushDataMe(BATCH_ID, aDDT15.SCHOOL_NO, aDDT15.USER_NO, "", BODY_TXTafter, "", "ADDI06", "ADD", aDDT15.OAWARD_ID.ToString(), "", false, ref db);
                PushHelper.ToPushServer(BATCH_ID);
                oldADDT15.CASH = aDDT15.CASH;
            }
            oldADDT15.OAWARD_ITEM = aDDT15.OAWARD_ITEM;
            oldADDT15.OAWARD_SCORE = aDDT15.OAWARD_SCORE;
            oldADDT15.REMARK = aDDT15.REMARK;
            oldADDT15.UPDATEDATE = DateTime.Now;
            ImageModifyTemp temp = new ImageModifyTemp();
            if (files != null && files.ContentLength >= 0)
            {
                if (FileHelper.DoLoadFile(files, new FileHelper().GetMapPathImg(oldADDT15.SCHOOL_NO, ImgPath), ref Message, true, oldADDT15.OAWARD_ID.ToString()) == false)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + Message;
                    return RedirectToAction("Query");
                }

                
                temp.Source_ID = Guid.NewGuid().ToString("N");
                temp.SCHOOL_NO = user.SCHOOL_NO;
                temp.USER_NO = user.USER_NO;
                temp.Source = "ADDT15";
                temp.CHG_DATE = DateTime.Now;
                temp.Action = "Modify";
                db.ImageModifyTemp.Add(temp);
                oldADDT15.IMG_FILE = Path.GetFileName(files.FileName);
                oldADDT15.ImgSourceNO= temp.Source_ID;
            }

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "修改完成";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }
            return RedirectToAction("QUERY");
        }
        // GET: ADDI15/MODIFY/5
        public ActionResult MODIFY1(int? OAWARD_ID)
        {
            if (OAWARD_ID == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT15 aDDT15 = db.ADDT15.Where(p => p.OAWARD_ID == OAWARD_ID).FirstOrDefault();
            //ADDT15 aDDT15 = db.ADDT15.Find(id);
            if (aDDT15 == null)
            {
                return HttpNotFound();
            }
            ViewBag.IMG_FILE = new FileHelper().GetDirectorySysPathImg(aDDT15.SCHOOL_NO, ImgPath, aDDT15.OAWARD_ID.ToString(), aDDT15.IMG_FILE);

            return View(aDDT15);
        }
        // GET: ADDI15/MODIFY/5
        public ActionResult MODIFY(int? OAWARD_ID)
        {
            if (OAWARD_ID == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT15 aDDT15 = db.ADDT15.Where(p => p.OAWARD_ID == OAWARD_ID).FirstOrDefault();
            //ADDT15 aDDT15 = db.ADDT15.Find(id);
            if (aDDT15 == null)
            {
                return HttpNotFound();
            }
            ViewBag.IMG_FILE = new FileHelper().GetDirectorySysPathImg(aDDT15.SCHOOL_NO, ImgPath, aDDT15.OAWARD_ID.ToString(), aDDT15.IMG_FILE);

            return View(aDDT15);
        }

        // GET: ADDI15/Delete/5
        public ActionResult Delete(int? OAWARD_ID)
        {
            if (OAWARD_ID == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT15 aDDT15 = db.ADDT15.Where(p => p.OAWARD_ID == OAWARD_ID).FirstOrDefault();
            if (aDDT15 == null)
            {
                return HttpNotFound();
            }
            return View(aDDT15);
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult Delete(int OAWARD_ID)
        {
            string BATCH_ID = PushService.CreBATCH_ID();
            ADDT15 delaDDT15 = db.ADDT15.Where(p => p.OAWARD_ID == OAWARD_ID).FirstOrDefault();
            delaDDT15.APPLY_STATUS = "9";
            db.Entry(delaDDT15).State = EntityState.Modified;
            try
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                ECOOL_APP.CashHelper.AddCash(user, -(Convert.ToInt32(delaDDT15.CASH)), delaDDT15.SCHOOL_NO, delaDDT15.USER_NO, "ADDI07", delaDDT15.OAWARD_ID.ToString(), "校外表現-作廢", true, ref db, "", "",ref valuesList);

                string BODY_TXT = "校外表現被作廢，優良表現: " + delaDDT15.OAWARD_ITEM + "-、減少酷幣點數" + (delaDDT15.CASH).ToString() + "數";

                // PushService.InsertPushDataParents(BATCH_ID, delaDDT15.SCHOOL_NO, delaDDT15.USER_NO, "", BODY_TXT, "", "ADDI07", "Delete", delaDDT15.OAWARD_ID.ToString(), "", false, ref db);
                PushService.InsertPushDataMe(BATCH_ID, delaDDT15.SCHOOL_NO, delaDDT15.USER_NO, "", BODY_TXT, "", "ADDI07", "Delete", delaDDT15.OAWARD_ID.ToString(), "", false, ref db);

                db.SaveChanges();
                TempData["StatusMessage"] = "刪除完成";
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            return RedirectToAction("QUERY");
        }

        [HttpGet]
        public JsonResult GetNameData(string Class_No)
        {
            List<SelectListItem> items = new List<SelectListItem>();
            if (!string.IsNullOrWhiteSpace(Class_No))
            {
                var ltCLASS_NO = db.HRMT01.Where(p => p.CLASS_NO == Class_No && p.SCHOOL_NO == user.SCHOOL_NO && p.USER_TYPE == "S"
                                          && (!UserStaus.NGKeyinUserStausList.Contains(p.USER_STATUS))).OrderBy(p => p.SEAT_NO).ToList();
                foreach (var item in ltCLASS_NO)
                {
                    items.Add(new SelectListItem() { Text = item.SEAT_NO + " " + item.NAME, Value = item.USER_NO });
                }
                if (!items.Count.Equals(0))
                {
                    items.Insert(0, new SelectListItem() { Text = "請選擇學生", Value = "" });
                }
            }

            return Json(items, JsonRequestBehavior.AllowGet);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}