﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'link', 'en-au', {
	acccessKey: 'Access Key',
	advanced: 'Advanced',
	advisoryContentType: 'Advisory Content Type',
	advisoryTitle: 'Advisory Title',
	anchor: {
		toolbar: 'Anchor',
		menu: 'Edit Anchor',
		title: 'Anchor Properties',
		name: 'Anchor Name',
		errorName: 'Please type the anchor name',
		remove: 'Remove Anchor'
	},
	anchorId: 'By Element Id',
	anchorName: 'By Anchor Name',
	charset: 'Linked Resource Charset',
	cssClasses: 'Stylesheet Classes',
	displayText: 'Display Text', // MISSING
	emailAddress: 'E-Mail Address',
	emailBody: 'Message Body',
	emailSubject: 'Message Subject',
	id: 'Id',
	info: 'Link Info',
	langCode: 'Language Code',
	langDir: 'Language Direction',
	langDirLTR: 'Left to Right (LTR)',
	langDirRTL: 'Right to Left (RTL)',
	menu: 'Edit Link',
	name: 'Name',
	noAnchors: '(No anchors available in the document)',
	noEmail: 'Please type the e-mail address',
	noUrl: 'Please type the link URL',
	other: '<other>',
	popupDependent: 'Dependent (Netscape)',
	popupFeatures: 'Popup Window Features',
	popupFullScreen: 'Full Screen (IE)',
	popupLeft: 'Left Position',
	popupLocationBar: 'Location Bar',
	popupMenuBar: 'Menu Bar',
	popupResizable: 'Resizable',
	popupScrollBars: 'Scroll Bars',
	popupStatusBar: 'Status Bar',
	popupToolbar: 'Toolbar',
	popupTop: 'Top Position',
	rel: 'Relationship', // MISSING
	selectAnchor: 'Select an Anchor',
	styles: 'Style',
	tabIndex: 'Tab Index',
	target: 'Target',
	targetFrame: '<frame>',
	targetFrameName: 'Target Frame Name',
	targetPopup: '<popup window>',
	targetPopupName: 'Popup Window Name',
	title: 'Link',
	toAnchor: 'Link to anchor in the text',
	toEmail: 'E-mail',
	toUrl: 'URL',
	toolbar: 'Link',
	type: 'Link Type',
	unlink: 'Unlink',
	upload: 'Upload'
} );
