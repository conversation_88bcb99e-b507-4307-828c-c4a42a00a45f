﻿using ECOOL_APP.com.ecool.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using MvcPaging;
using System.ComponentModel.DataAnnotations;
using ECOOL_APP.EF;

namespace EcoolWeb.ViewModels
{
    public class AWAT14ListViewModel
    {

        /// <summary>
        /// 只顯示某一所學校
        /// </summary>
        public string whereSchoolNo { get; set; }
        public string SyntaxName { get; set; }
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }
        public string WhereRankNO { get; set; }
        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某座號
        /// </summary>
        public string whereSeat_NO { get; set; }

        /// <summary>
        /// 月排行榜
        /// </summary>
        public bool WhereIsMonthTop { get; set; }

        public DateTime? WhereUP_DATE_START { get; set; }

        public DateTime? WhereUP_DATE_END { get; set; }

        public bool IsPrint { get; set; }

        public bool IsToExcel { get; set; }
        public int  TotalItemCount { get; set; }
   
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<AWAT1409> AWAT14List;
       
        /// <summary>
        /// 月排行榜
        /// </summary>
      //  public IPagedList<ADDTMonthTopViewModel> MonthTopList;

        /// <summary>
        /// 獎狀處理清單 checkBox
        /// </summary>
        public List<AWAT15> AWAT15List { get; set; }

        /// <summary>
        /// 清除條件
        /// </summary>
        public bool doClear { get; set; }
        public int ShareCountSUM { get; set; }
        /// <summary>
        /// 獎狀處理狀況
        /// </summary>
        public byte? a09_his_STATUS { get; set; }

        /// <summary>
        /// 是否輪播不同網頁
        /// </summary>
        public bool isCarousel { get; set; }

        public AWAT14ListViewModel()
        {
            Page = 0;
            OrdercColumn = "CASH_Rank";
          
        }

        public void ClearWhere()
        {
            this.whereKeyword = null;
            this.doClear = false;
            this.OrdercColumn = null;
            WhereIsMonthTop = false;
        }
    }
}