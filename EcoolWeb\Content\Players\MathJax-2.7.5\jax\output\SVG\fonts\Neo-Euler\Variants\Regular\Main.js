/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Variants/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Variants={directory:"Variants/Regular",family:"NeoEulerMathJax_Variants",id:"NEOEULERVARIANTS",32:[0,0,333,0,0,""],160:[0,0,333,0,0,""],8242:[559,-41,329,48,299,"290 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32"],8243:[559,-41,640,48,610,"290 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32zM601 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47 c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32"],8244:[559,-41,950,48,920,"290 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32zM601 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47 c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32zM911 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32"],8245:[559,-41,329,48,299,"57 472l188 -414c7 -15 8 -17 15 -17c8 0 39 10 39 22c0 1 -4 14 -5 16l-132 433c-5 16 -17 47 -55 47c-31 0 -59 -24 -59 -55c0 -7 1 -15 9 -32"],8246:[559,-41,640,48,610,"368 472l188 -414c7 -15 8 -17 15 -17c8 0 39 10 39 22c0 1 -4 14 -5 16l-132 433c-5 16 -17 47 -55 47c-31 0 -59 -24 -59 -55c0 -7 1 -15 9 -32zM57 472l188 -414c7 -15 8 -17 15 -17c8 0 39 10 39 22c0 1 -4 14 -5 16l-132 433c-5 16 -17 47 -55 47 c-31 0 -59 -24 -59 -55c0 -7 1 -15 9 -32"],8247:[559,-41,950,48,919,"677 472l188 -414c7 -15 8 -17 15 -17c8 0 39 10 39 22c0 1 -4 14 -5 16l-132 433c-5 16 -17 47 -55 47c-31 0 -59 -24 -59 -55c0 -7 1 -15 9 -32zM367 472l188 -414c7 -15 8 -17 15 -17c8 0 39 10 39 22c0 1 -4 14 -5 16l-132 433c-5 16 -17 47 -55 47 c-31 0 -59 -24 -59 -55c0 -7 1 -15 9 -32zM57 472l188 -414c7 -15 8 -17 15 -17c8 0 39 10 39 22c0 1 -4 14 -5 16l-132 433c-5 16 -17 47 -55 47c-31 0 -59 -24 -59 -55c0 -7 1 -15 9 -32"],8279:[559,-41,1260,48,1230,"290 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32zM601 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47 c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32zM911 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16l132 433c5 16 17 47 55 47c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32zM1221 472l-188 -414c-7 -15 -8 -17 -15 -17c-8 0 -39 10 -39 22c0 1 4 14 5 16 l132 433c5 16 17 47 55 47c31 0 59 -24 59 -55c0 -7 -1 -15 -9 -32"],57856:[493,13,501,41,456,"261 493c45 -13 87 -38 120 -72c44 -43 75 -99 75 -161c0 -75 -36 -143 -88 -195c-43 -42 -95 -78 -155 -78c-42 0 -83 16 -112 46c-43 43 -60 104 -60 165c0 73 34 141 85 193c40 40 85 75 135 102zM195 418c-23 -14 -35 -31 -44 -45c-23 -34 -32 -81 -32 -124 c0 -57 16 -114 57 -154c23 -23 59 -42 92 -42c29 0 44 2 64 16c38 27 49 99 49 143c0 55 -20 109 -59 148c-28 28 -86 55 -127 58"],57857:[469,1,501,46,460,"299 469l10 -8c-10 -77 -12 -154 -12 -232c0 -54 1 -108 3 -162c0 -6 1 -12 5 -16c3 -4 7 -8 12 -8c48 0 95 1 143 3l-1 -47c-69 4 -138 7 -207 7c-68 0 -136 -2 -206 -7v45c48 -1 96 -1 144 -1c6 0 12 1 16 6c3 2 4 12 4 19c2 18 2 128 2 192c0 44 0 102 -7 131 c-4 16 -17 25 -32 30c-16 5 -46 9 -69 12l-1 27c66 0 131 2 196 9"],57858:[474,-1,501,59,485,"475 73l10 -6l-25 -66h-399l-2 16c1 0 67 62 100 94c41 38 80 79 111 125c20 30 39 61 39 97c0 23 -11 44 -27 60c-15 14 -35 22 -56 22c-29 0 -76 -27 -112 -48l-10 18c29 25 61 49 95 66c26 13 54 23 83 23c28 0 55 -12 74 -32c19 -19 29 -44 29 -70 c0 -17 -6 -34 -13 -51c-10 -23 -24 -45 -38 -66c-45 -64 -99 -121 -154 -177c-2 -2 -4 -4 -4 -5c0 -8 10 -8 16 -8c95 3 282 8 283 8"],57859:[474,182,501,38,430,"246 210l3 -7c45 0 101 -12 134 -45c31 -31 47 -73 47 -116c0 -56 -23 -110 -63 -149c-49 -50 -117 -75 -186 -75c-51 0 -99 16 -143 41l16 27c36 -19 74 -35 115 -35c42 0 84 10 113 39c37 37 56 87 56 139c0 39 -10 78 -38 106c-17 17 -41 24 -64 29 c-36 7 -72 8 -108 6v34c29 2 58 6 86 16c21 8 42 18 58 34c19 20 34 44 34 72c0 26 -9 52 -28 71c-18 18 -43 25 -69 25c-12 0 -24 -4 -36 -9c-16 -8 -31 -18 -45 -28l-16 11c23 22 47 44 76 59c24 11 50 19 77 19c32 0 65 -7 88 -30c21 -20 32 -47 32 -76 c0 -37 -15 -72 -41 -98c-25 -25 -65 -46 -98 -60"],57860:[476,192,501,10,482,"380 476l5 -3c-2 -83 -4 -167 -4 -251c0 -61 1 -121 3 -182c33 1 65 2 98 5l-8 -46h-87c0 -53 0 -127 2 -161l-78 -30l-12 9c6 61 7 121 7 182h-295l-1 23l289 429zM83 41h223c0 108 0 246 -7 324h-12c-81 -101 -149 -210 -208 -324h4"],57861:[458,184,501,47,441,"421 458l-24 -80h-259c-7 0 -17 -3 -17 -12v-176l9 -5c40 19 84 30 129 30c48 0 97 -12 132 -47c33 -33 50 -79 50 -126c0 -56 -21 -111 -61 -151c-58 -59 -142 -75 -224 -75c-37 0 -74 10 -109 25l12 25c31 -12 64 -20 97 -20c45 0 91 11 123 43c35 35 56 83 56 134 c0 41 -10 83 -39 112c-24 24 -57 34 -90 34c-42 0 -79 -24 -107 -55l-18 11v333h340"],57862:[700,13,501,45,471,"460 691l-9 -57l-7 -5c-19 8 -40 12 -60 12c-61 0 -115 -32 -160 -73c-71 -67 -94 -166 -95 -262l7 -1c53 43 116 72 185 72c38 0 76 -16 103 -43c32 -33 47 -78 47 -123c0 -60 -30 -113 -72 -155c-43 -43 -101 -69 -163 -69c-45 0 -89 15 -121 47 c-53 53 -70 130 -70 204c0 125 45 249 134 338c35 37 75 69 120 92c36 19 76 32 117 32c15 0 30 -4 44 -9zM132 275c3 -42 10 -96 22 -136c9 -29 22 -58 43 -79c18 -17 50 -34 75 -34c32 0 45 1 73 18c35 21 47 87 47 128c0 40 -17 79 -46 107c-23 24 -75 39 -100 39 c-53 0 -78 -16 -114 -43"],57863:[468,181,501,37,498,"297 458h201v-16c-79 -112 -155 -225 -227 -341c-57 -92 -111 -185 -158 -282h-73l-3 13c127 177 243 361 359 545c-1 0 -4 3 -6 5h-332l32 86c69 -8 138 -10 207 -10"],57864:[706,10,501,40,461,"220 344c-27 -14 -48 -35 -65 -58c-21 -31 -32 -72 -32 -109c0 -41 14 -82 43 -112c21 -20 60 -44 89 -44c33 0 54 10 71 19c26 17 49 63 49 128c0 41 -22 77 -51 106c-25 25 -74 50 -104 70zM300 394c38 -27 97 -69 115 -87c29 -29 46 -68 46 -110 c0 -48 -16 -97 -52 -130c-52 -48 -106 -77 -190 -77c-48 0 -97 11 -132 46c-32 32 -47 76 -47 121c0 42 12 85 43 115c35 36 78 63 122 88c-33 22 -63 49 -86 81c-17 27 -30 57 -30 89c0 40 14 81 45 108c35 31 77 51 120 68c30 -2 60 -6 88 -15c21 -7 41 -16 57 -32 c24 -24 36 -57 36 -91s-15 -66 -35 -93c-26 -34 -64 -61 -100 -81zM283 410c42 28 66 81 66 134c0 28 -7 57 -27 78c-26 26 -63 47 -86 55c-20 -7 -52 -30 -62 -54c-8 -18 -10 -40 -10 -60c0 -26 13 -50 29 -71c21 -30 60 -62 90 -82"],57865:[470,182,501,27,468,"359 102l-6 4c-38 -31 -90 -72 -149 -72c-43 3 -86 13 -116 44c-42 41 -61 98 -61 156c0 52 13 106 51 143c35 36 101 67 156 93c33 -2 66 -6 97 -16c25 -9 48 -20 66 -38c25 -25 41 -57 52 -90c13 -41 19 -83 19 -126c0 -105 -40 -207 -115 -282 c-73 -73 -178 -100 -281 -100v28c75 0 153 19 207 73c46 46 70 121 80 183zM339 125c21 21 30 50 30 79c0 63 -14 135 -60 181c-25 25 -70 45 -101 49c-15 -7 -33 -16 -47 -34c-31 -37 -45 -89 -45 -136c0 -51 17 -102 54 -139c20 -20 47 -31 76 -31c34 0 69 7 93 31"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Variants/Regular/Main.js");
