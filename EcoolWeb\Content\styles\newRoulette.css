.panel-Roulette {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}
.panel-Roulette > .panel-heading {
  background-color: transparent;
  border-color: transparent;
  text-align: center;
  overflow: hidden;
}
.panel-Roulette > .panel-heading h1 {
  position: relative;
  font-size: 3.125rem;
  width: 60%;
  min-width: 250px;
  background: #d70002;
  color: #fff;
  text-align: center;
  padding: 0.526rem;
  margin: 1rem auto;
  box-shadow: 0 3px 2px #7f0000;
  z-index: 1;
}
@media (max-width: 992px) {
  .panel-Roulette > .panel-heading h1 {
    font-size: 2rem;
  }
}
.panel-Roulette > .panel-heading h1::before, .panel-Roulette > .panel-heading h1::after {
  content: "";
  position: absolute;
  display: block;
  bottom: -0.5em;
  width: 20%;
  height: 100%;
  background-color: #7f0000;
  z-index: -1;
}
.panel-Roulette > .panel-heading h1::before {
  left: -20%;
  background-image: url(../../Content/images/Roulette-ribbon-bg.png);
  background-repeat: no-repeat;
  background-position: left center;
  background-size: 21px 100%;
}
.panel-Roulette > .panel-heading h1::after {
  right: -20%;
  background-image: url(../../Content/images/Roulette-ribbon-bg-right.png);
  background-repeat: no-repeat;
  background-position: right center;
  background-size: 21px 100%;
}
.panel-Roulette > .panel-heading .hint {
  font-size: 2rem;
  color: #4400CC;
  background-color: #fffde3;
}
.panel-Roulette .panel-body {
  padding: 1.5rem 1.5rem 3rem 1.5rem;
  background-color: #fff;
  border-radius: 2rem;
  box-shadow: inset 0 0 35px #fffde3;
}
.panel-Roulette .input-group {
  box-shadow: 0 0 0 6px #777edd;
  border-radius: 0.4rem;
}

.statusButton {
  position: fixed;
  bottom: 9px;
  left: 9px;
  width: 100px;
  height: 100px;
  border-radius: 50px;
  line-height: 1;
  white-space: wrap;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
}
.statusButton.btn-success {
  background-color: #007f05;
}

.toast-error {
  opacity: 1 !important;
  background-color: rgb(255, 235, 235);
  border: 6px solid #f09191;
  border-radius: 1rem;
  box-shadow: 0 0 10px #7f0000, 0 10px 0 #d70002, 0 -10px 0 #d70002, 20vw 0 0 10px #d70002, -20vw 0 0 10px #d70002 !important;
}
.toast-error .toast-progress {
  background-color: #f09191;
}
.toast-error .toast-close-button {
  color: #7f0000;
}
.toast-error .toast-message {
  text-align: center;
  font-size: 2.325rem;
  font-weight: bold;
  line-height: 1.5;
  color: #7f0000;
}

#toast-container > .toast-error {
  padding-left: 3.5em;
  background-size: 2em auto;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAA7AAAAOwBeShxvQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAT4SURBVFiF7ZZpbFRVFMd/577pdGa6TCtQJHFtC3FD1KAxSiQqfHGJGlsMoJR2sC611LokbkgTMZoYAi1GE6AN0RiN1cTtW41GjRoDxoVgUjodNBDckEpLy/TNvHv8UGY609bXovGb/2SS8876v/ece+fC/5gG+xtaK/tWN5eealx/Y2N0f0Nr5XR+xs+YWLf+SUe8uAnJ7n21bcGZFt9X2xbEC+12xIsnGlqe8PMN+GZSlgACLCiMHl0LbM+Y9tzcGCkvdB+14VAi4CS7z921K5mxFUaPrkWZD6CiV/uV8N0BVbsBSAOI0pjRx2+7tyJqhn6xAdNmgsFXrBP97Kf77ivP2AW956SYRu3Gf0ygquvFPcDTgIfSM05s6HOMKTFFxRnN4rQb3DnO3PQAHiIbTub4W4ifMYN9tW3BC7vbXID+mrot3omRB505FeA4E9M1VXW2vzQxZkYE4rHmpwSpFWRTZWd791TO/TWx6+2JwR5KS8VEiqZySeLZK6p2vbh3KmMi1lKrwgZV+2Z157ZNWQIHY62nuXi/c7IlCq9WdXbUCWgmWNvaAvFveo+oSNSZNdtvUd8NOMnLF2/fnsrGgiRi618B7jypsoFgavbZL788YADOGDx0DPgwZ1vu+nHt/XNzs/Z9n3hTU6moKY36FQdYVGZDj+UqDjY8MC+nOKrac9aRI4PZHcggsa5luao2i/JtZVfH0xl9720NS4w79CnhiJjoGIHIwguILlsKwLEPP2Fk7w+5qVw8uzi3FYlYyzOKLhKRbZU727MDPaMh7Ltl5VH1vHKnogJk7ODMe6SJ8HkLAEj2xjn8wrYJUbKncvDwldLd7fnl9j2GMDb1mkqVm+KSbHEAMTknIDDxNADo4gOl8+qny+9LoG/16lLPTTaJMUhRZLpckymoPNvf2Og7NIED9c1LrSPXYvVPHBOv2tH+QTbBCfMWaa9AomXMsFv5EK0gHXoMeDyj6r+75SY8W42RMlH5yFhj3kXZiMgWrL7fv279DQDxO2IXMZpcJo6DRMKnXjxLgofisdZqgP5Y841YfR+RLSgbFd4zYAdz3D1rzQCAuqNvoypSUvrPVj+OoKj3HIAaGQByhtIOGhyzRJAVqC43nqme37X1y/23163U0eQCAgEkPPXqvaGhcfnY4JQ+WQg1iVjLNdU7Or4wnqlGdbkgK9QWXD3l0vpuXfWbuu4cU1aGhKcePlMUoeiShQAMf7sXOzziTwI+rursuG4ytwnor127yhs+/hrG4FSc/i93Px9WzVXzu7Z+maub9CCx7uhmABMp8i1eMHcORZdeDMDwN9+T+vX3aQkY0UeA23N1eSX6aupv0ZGhdwBMxVxk0t/tOOY93ET4/LGb8ERvnJ8n3YRTworKBZVd7b1ZUvlm93kACYV8iwOYcGhcDoV8PPPD1OijeYqM0HdHXZWOuucBSKR4YuAk2NHxt4a6ozMlAMqaeP1DZ2Y+szMgKbtJVcEJIIXTP4D/eP1tiq+4DEQ4/tXXMycABcbxVgCb8whYtecASIH/QzkD99Bhjh46fCqFs1DVWRk52wJTUNAFoKlUzjvoP8GIiLyR+cg7Bb01azaadLqJYCGmpNiCRIEZT9gEeMBA5qeqA2LkgMLO6p0d2Z5Ne80crG0NJ4vTxSqBEoAAtsAazZtSY+V4GpMC0IAZNMOuO/+1bdPcz/9jDH8BgajSK5ZTk+oAAAAASUVORK5CYII=") !important;
}

.toast-success {
  opacity: 1 !important;
  background-color: rgb(255, 253, 235);
  border: 6px solid #ff8b1e;
  border-radius: 1rem;
  box-shadow: 0 0 10px #d69e03, 0 10px 0 #ffc400, 0 -10px 0 #ffc400, 20vw 0 0 10px #ffc400, -20vw 0 0 10px #ffc400 !important;
}
.toast-success .toast-progress {
  background-color: #dfc44a;
}
.toast-success .toast-close-button {
  color: #6b6b6b;
}
.toast-success .toast-message {
  text-align: center;
  font-size: 2.325rem;
  font-weight: bold;
  line-height: 1.5;
  color: #000;
}
.toast-success .toast-message [color=red] {
  color: #d86800;
}
.toast-success::before {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.6);
  pointer-events: none;
  z-index: -1;
}/*# sourceMappingURL=newRoulette.css.map */