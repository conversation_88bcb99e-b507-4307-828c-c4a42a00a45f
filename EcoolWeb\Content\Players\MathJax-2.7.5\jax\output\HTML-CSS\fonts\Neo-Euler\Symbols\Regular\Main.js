/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Symbols/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Symbols={directory:"Symbols/Regular",family:"NeoEulerMathJax_Symbols",testString:"\u00A0\u2320\u2321\u2329\u232A\u239B\u239C\u239D\u239E\u239F\u23A0\u23A1\u23A2\u23A3\u23A4",32:[0,0,333,0,0],160:[0,0,333,0,0],8992:[915,0,444,180,452],8993:[925,0,444,-23,265],9001:[737,237,388,107,330],9002:[737,237,388,57,280],9115:[1808,0,883,292,851],9116:[620,0,875,292,403],9117:[1808,0,883,292,851],9118:[1808,0,873,22,581],9119:[620,0,875,472,583],9120:[1808,0,873,22,581],9121:[1799,0,666,326,659],9122:[602,0,666,326,395],9123:[1800,-1,666,326,659],9124:[1799,0,666,7,340],9125:[602,0,666,271,340],9126:[1800,-1,666,7,340],9127:[909,0,889,395,718],9128:[1820,0,889,170,492],9129:[909,0,889,395,718],9130:[320,0,889,395,492],9131:[909,0,889,170,492],9132:[1820,0,889,395,718],9133:[909,0,889,170,492],9134:[381,0,444,181,265]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Symbols"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Symbols/Regular/Main.js"]);
