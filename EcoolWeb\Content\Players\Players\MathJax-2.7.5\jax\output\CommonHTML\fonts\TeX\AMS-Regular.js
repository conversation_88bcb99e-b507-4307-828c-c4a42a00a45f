/*
 *  /MathJax/jax/output/CommonHTML/fonts/TeX/AMS-Regular.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(b){var a="MathJax_AMS";b.FONTDATA.FONTS[a]={className:b.FONTDATA.familyName(a),centerline:270,ascent:1003,descent:463,32:[0,0,250,0,0],65:[701,1,722,17,703],66:[683,1,667,11,620],67:[702,19,722,39,684],68:[683,1,722,16,688],69:[683,1,667,12,640],70:[683,1,611,12,584],71:[702,19,778,39,749],72:[683,1,778,14,762],73:[683,1,389,20,369],74:[683,77,500,6,478],75:[683,1,778,22,768],76:[683,1,667,12,640],77:[683,1,944,17,926],78:[683,20,722,20,702],79:[701,19,778,34,742],80:[683,1,611,16,597],81:[701,181,778,34,742],82:[683,1,722,16,705],83:[702,12,556,28,528],84:[683,1,667,33,635],85:[683,19,722,16,709],86:[683,20,722,0,719],87:[683,19,1000,5,994],88:[683,1,722,16,705],89:[683,1,722,16,704],90:[683,1,667,29,635],107:[683,1,556,17,534],160:[0,0,250,0,0],165:[683,0,750,11,738],174:[709,175,947,32,915],240:[749,21,556,42,509],295:[695,13,540,42,562],710:[845,-561,2333,-14,2346],732:[899,-628,2333,1,2330],770:[845,-561,0,-2347,13],771:[899,-628,0,-2332,-3],989:[605,85,778,55,719],1008:[434,6,667,37,734],8245:[560,-43,275,12,244],8463:[695,13,540,42,562],8487:[684,22,722,44,675],8498:[695,1,556,55,497],8502:[763,21,667,-22,687],8503:[764,43,444,-22,421],8504:[764,43,667,54,640],8513:[705,23,639,37,577],8592:[437,-64,500,64,422],8594:[437,-64,500,58,417],8602:[437,-60,1000,56,942],8603:[437,-60,1000,54,942],8606:[417,-83,1000,56,944],8608:[417,-83,1000,55,943],8610:[417,-83,1111,56,1031],8611:[417,-83,1111,79,1054],8619:[575,41,1000,56,964],8620:[575,41,1000,35,943],8621:[417,-83,1389,57,1331],8622:[437,-60,1000,56,942],8624:[722,0,500,56,444],8625:[722,0,500,55,443],8630:[461,1,1000,17,950],8631:[460,1,1000,46,982],8634:[650,83,778,56,722],8635:[650,83,778,56,721],8638:[694,194,417,188,375],8639:[694,194,417,41,228],8642:[694,194,417,188,375],8643:[694,194,417,41,228],8644:[667,0,1000,55,944],8646:[667,0,1000,55,944],8647:[583,83,1000,55,944],8648:[694,193,833,83,749],8649:[583,83,1000,55,944],8650:[694,194,833,83,749],8651:[514,14,1000,55,944],8652:[514,14,1000,55,944],8653:[534,35,1000,54,942],8654:[534,37,1000,32,965],8655:[534,35,1000,55,943],8666:[611,111,1000,76,944],8667:[611,111,1000,55,923],8669:[417,-83,1000,56,943],8672:[437,-64,1334,64,1251],8674:[437,-64,1334,84,1251],8705:[846,21,500,56,444],8708:[860,166,556,55,497],8709:[587,3,778,54,720],8717:[440,1,429,102,456],8722:[270,-230,500,84,417],8724:[766,93,778,57,722],8726:[430,23,778,91,685],8733:[472,-28,778,56,722],8736:[694,0,722,55,666],8737:[714,20,722,55,666],8738:[551,51,722,55,666],8739:[430,23,222,91,131],8740:[750,252,278,-21,297],8741:[431,23,389,55,331],8742:[750,250,500,-20,518],8756:[471,82,667,24,643],8757:[471,82,667,23,643],8764:[365,-132,778,55,719],8765:[367,-133,778,56,722],8769:[467,-32,778,55,719],8770:[463,-34,778,55,720],8774:[652,155,778,54,720],8776:[481,-50,778,55,719],8778:[579,39,778,51,725],8782:[492,-8,778,56,722],8783:[492,-133,778,56,722],8785:[609,108,778,56,722],8786:[601,101,778,15,762],8787:[601,102,778,14,762],8790:[367,-133,778,56,722],8791:[721,-133,778,56,722],8796:[859,-133,778,56,723],8806:[753,175,778,83,694],8807:[753,175,778,83,694],8808:[752,286,778,82,693],8809:[752,286,778,82,693],8812:[750,250,500,74,425],8814:[708,209,778,82,693],8815:[708,209,778,82,693],8816:[801,303,778,82,694],8817:[801,303,778,82,694],8818:[732,228,778,56,722],8819:[732,228,778,56,722],8822:[681,253,778,44,734],8823:[681,253,778,83,694],8828:[580,153,778,83,694],8829:[580,154,778,82,694],8830:[732,228,778,56,722],8831:[732,228,778,56,722],8832:[705,208,778,82,693],8833:[705,208,778,82,693],8840:[801,303,778,83,693],8841:[801,303,778,82,691],8842:[635,241,778,84,693],8843:[635,241,778,82,691],8847:[539,41,778,83,694],8848:[539,41,778,64,714],8858:[582,82,778,57,721],8859:[582,82,778,57,721],8861:[582,82,778,57,721],8862:[689,0,778,55,722],8863:[689,0,778,55,722],8864:[689,0,778,55,722],8865:[689,0,778,55,722],8872:[694,0,611,55,555],8873:[694,0,722,55,666],8874:[694,0,889,55,833],8876:[695,1,611,-55,554],8877:[695,1,611,-55,554],8878:[695,1,722,-55,665],8879:[695,1,722,-55,665],8882:[539,41,778,83,694],8883:[539,41,778,83,694],8884:[636,138,778,83,694],8885:[636,138,778,83,694],8888:[408,-92,1111,55,1055],8890:[431,212,556,57,500],8891:[716,0,611,55,555],8892:[716,0,611,55,555],8901:[189,0,278,55,222],8903:[545,44,778,55,720],8905:[492,-8,778,146,628],8906:[492,-8,778,146,628],8907:[694,22,778,55,722],8908:[694,22,778,55,722],8909:[464,-36,778,56,722],8910:[578,21,760,83,676],8911:[578,22,760,83,676],8912:[540,40,778,84,694],8913:[540,40,778,83,693],8914:[598,22,667,55,611],8915:[598,22,667,55,611],8916:[736,22,667,56,611],8918:[541,41,778,82,693],8919:[541,41,778,82,693],8920:[568,67,1333,56,1277],8921:[568,67,1333,55,1277],8922:[886,386,778,83,674],8923:[886,386,778,83,674],8926:[734,0,778,83,694],8927:[734,0,778,82,694],8928:[801,303,778,82,693],8929:[801,303,778,82,694],8934:[730,359,778,55,719],8935:[730,359,778,55,719],8936:[730,359,778,55,719],8937:[730,359,778,55,719],8938:[706,208,778,82,693],8939:[706,208,778,82,693],8940:[802,303,778,82,693],8941:[801,303,778,82,693],8994:[378,-122,778,55,722],8995:[378,-143,778,55,722],9416:[709,175,902,8,894],9484:[694,-306,500,55,444],9488:[694,-306,500,55,444],9492:[366,22,500,55,444],9496:[366,22,500,55,444],9585:[694,195,889,0,860],9586:[694,195,889,0,860],9632:[689,0,778,55,722],9633:[689,0,778,55,722],9650:[575,20,722,84,637],9651:[575,20,722,84,637],9654:[539,41,778,83,694],9660:[576,19,722,84,637],9661:[576,19,722,84,637],9664:[539,41,778,83,694],9674:[716,132,667,56,611],9733:[694,111,944,49,895],10003:[706,34,833,84,749],10016:[716,22,833,48,786],10731:[716,132,667,56,611],10846:[813,97,611,55,555],10877:[636,138,778,83,694],10878:[636,138,778,83,694],10885:[762,290,778,55,722],10886:[762,290,778,55,722],10887:[635,241,778,82,693],10888:[635,241,778,82,693],10889:[761,387,778,57,718],10890:[761,387,778,57,718],10891:[1003,463,778,83,694],10892:[1003,463,778,83,694],10901:[636,138,778,83,694],10902:[636,138,778,83,694],10933:[752,286,778,82,693],10934:[752,286,778,82,693],10935:[761,294,778,57,717],10936:[761,294,778,57,717],10937:[761,337,778,57,718],10938:[761,337,778,57,718],10949:[753,215,778,84,694],10950:[753,215,778,83,694],10955:[783,385,778,82,693],10956:[783,385,778,82,693],57350:[430,23,222,-20,240],57351:[431,24,389,-20,407],57352:[605,85,778,55,719],57353:[434,6,667,37,734],57356:[752,284,778,82,693],57357:[752,284,778,82,693],57358:[919,421,778,82,694],57359:[801,303,778,82,694],57360:[801,303,778,82,694],57361:[919,421,778,82,694],57366:[828,330,778,82,694],57367:[752,332,778,82,694],57368:[828,330,778,82,694],57369:[752,333,778,82,693],57370:[634,255,778,84,693],57371:[634,254,778,82,691]};b.fontLoaded("TeX/"+a.substr(8))})(MathJax.OutputJax.CommonHTML);
