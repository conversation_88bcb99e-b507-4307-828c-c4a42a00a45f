﻿@model BarcodeEditPeopleViewModel
@{
    Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    bool IsAllList = (this.Request.CurrentExecutionFilePath.Contains("ADDTALLListDetails"));
}
@Html.Partial("_Notice")
<script src="~/Scripts/Pring.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG" style="width:50%" class="img-responsive " alt="Responsive image" />*@
@if (IsAllList == false)
{
    <div class="row">
        @*   <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap">
                <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                酷幣點數：@Model.CASH_AVAILABLE
                @if (user != null && user.USER_TYPE == ECOOL_APP.EF.UserType.Teacher)
                {
                    <span class="text-danger">
                        &nbsp;&nbsp;
                        <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                        本月已發放點數: @Model.Month_Given_Cash 。@Model.Special__Cash_Limit
                    </span>
                }
            </div>*@

        @*@if (@Model.BOOKS > 0)
            {
                <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash">
                    <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                    <a id="BorrowIndexSesem" href='@Url.Action("BorrowIndex", (string)ViewBag.BRE_NO)'> <text>本學期借閱本數：</text>@Model.BOOKS</a>
                </div>
            }
            @if (@Model.BOOKS_MONTH >= 0)
            {
                <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash">
                    <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                    <a id="BorrowIndex" href='@Url.Action("BorrowIndex", (string)ViewBag.BRE_NO)'><text>本月借閱本數：</text>@Model.BOOKS_MONTH</a>
                </div>
            }*@
        @if (user != null)
        {
            if (user.USER_TYPE == ECOOL_APP.EF.UserType.Student)
            {
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash">
                    @*<img src="~/Content/img/monkey-01.png" style="max-width:40px;max-height:40px;" class="imgEZ" />
                    <a href='@Url.Action("ArrivedChance2", "Home")'>
                        <text>好運次數：</text>

                        @user.Chance_ARRIVED_CASH
                    </a>*@
                </div>
            }
        }
    </div>
}
<div class="row">
    <div class="col-sm-2 col-xs-12">
        <div>
            @*@if (!string.IsNullOrEmpty(Model.PlayerUrl))
            {
                <img src="@(Url.Content(Model.PlayerUrl)+ "?refreshCache=" + DateTime.Now.ToString("mmddss"))" class="imgEZ" style="margin-top:30px;max-width:90%;" />
            }*@
        </div>

    </div>


    @if (Model != null)
    {

        //using (Html.BeginForm("_EditDetails3", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_self" }))
        //{
        @Html.HiddenFor(m => m.SCHOOL_NO)
        @Html.HiddenFor(m => m.SHORT_NAME)
        @Html.HiddenFor(m => m.USER_NO)
        @Html.HiddenFor(m => m.NAME)
        @Html.HiddenFor(m => m.GRADE)
        @Html.HiddenFor(m => m.CLASS_NO)
        @Html.HiddenFor(m => m.SEAT_NO)
        @Html.HiddenFor(m => m.CARD_NO)
        @Html.HiddenFor(m => m.BarCode)
        @Html.HiddenFor(m => m.ROLL_CALL_NAME)
        @Html.HiddenFor(m => m.ROLL_CALL_ID)
        @Html.HiddenFor(m => m.CASH)
        @Html.HiddenFor(m => m.txtUSER_NO)
        @Html.HiddenFor(m => m.txtPASSWORD)

    <div class="py-3 col-lg-12">



        <div class="alert alert-success h2 mb-1 text-black text-center" role="alert">
            <img class="d-block mx-auto" src="data:image/png;base64,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" height="40" alt="">
            <span class="d-block pt-3">
                恭喜領取點數 成功! <br />
                <span style="white-space: nowrap;font-weight: bold; color:blue">
                    @Model.SHORT_NAME 同學
                </span> ，因為    <span style="white-space: nowrap;font-weight: bold; color:red">
                    @Model.ROLL_CALL_NAME 活動
                </span>，
                <span style="white-space: nowrap;font-weight: bold;color:blue">
                    獲得酷幣點數 @Model.CASH 點，目前共有 @Model.SumCash 點。
                </span> <br /><br />
                如有問題請洽 @Model.NAME 老師
            </span>
        </div>
        <div class="text-center pt-3">
            <button type="button" class="btn btn-default mr-0" onclick="GOBack()">
                返回領取兌換
            </button>
            <br />
            5秒後系統自動轉跳
        </div>
    </div>
        //}

    }

</div>

@section Scripts {
<script language="JavaScript">
        var targetFormID = '#form1';
        $(document).ready(function () {

            //     var l = 0;
            //     l = $("#StatusMessageDiv").length;
            ////     $(".containerEZ")[1].remove();

            //     console.log($(".containerEZ").length)
            //     if ($(".containerEZ").length > 1) {

            //         $(".containerEZ")[0].remove();
            //     }
            if ($(".containerEZ").length > 1) {
                $(".containerEZ")[1].remove();
                setTimeout(function () { GOBack() }, 8000)
            }
        });
        $(document).ready(function () {
            $("#PieChartbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });
        $(document).ready(function () { $("#MyMOMO").colorbox({ iframe: true, width: "100%", height: "100%", opacity: 0.82 }); });
        $(document).ready(function () {
            $("#Statisticalbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });

        $(document).ready(function () {
            $("#MyPHOTO").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });
        $(document).ready(function () {
            $(".groupWRITING_NO").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%", rel: 'groupWRITING_NO' });
        });

        $(document).ready(function () {
            $("#MyAPPLY").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%" });
            $("#BorrowIndexSesem,#BorrowIndex").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
            $("#GameCash").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
            $("#MyBOOK").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
        });
                    function GOBack() {
                        window.location = "@Url.Action("GetStudentCashIndex", "ADDI13",new { SCHOOL_NO1=Model.SCHOOL_NO})";
                    }

</script>
}