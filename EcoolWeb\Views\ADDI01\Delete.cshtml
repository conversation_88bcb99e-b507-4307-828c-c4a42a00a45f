﻿@model ECOOL_APP.EF.ADDT01

@{
    ViewBag.Title = "Delete";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<h2>Delete</h2>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>ADDT01</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.SCHOOL_NO)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SCHOOL_NO)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.USER_NO)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.USER_NO)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CLASS_NO)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CLASS_NO)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SYEAR)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SYEAR)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SEMESTER)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SEMESTER)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SEAT_NO)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SEAT_NO)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.NAME)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.NAME)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SNAME)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SNAME)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SUBJECT)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SUBJECT)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ARTICLE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ARTICLE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ARTICLE_VERIFY)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ARTICLE_VERIFY)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.VERIFY_COMMENT)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.VERIFY_COMMENT)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.IMG_FILE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.IMG_FILE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.VOICE_FILE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.VOICE_FILE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SHARE_YN)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SHARE_YN)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CASH)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CASH)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.READ_COUNT)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.READ_COUNT)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.WRITING_STATUS)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.WRITING_STATUS)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.VERIFIER)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.VERIFIER)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.VERIFIED_DATE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.VERIFIED_DATE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CRE_DATE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CRE_DATE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CHG_DATE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CHG_DATE)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Back to List", "Index")
        </div>
    }
</div>
