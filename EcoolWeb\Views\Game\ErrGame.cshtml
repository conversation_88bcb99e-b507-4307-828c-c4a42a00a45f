﻿@model GameErrorViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}
<link href="~/Content/css/childrens-month.min.css" rel="stylesheet" />
<div style="margin: 0px auto;text-align:center">
    <img src="~/Content/img/@SharedGlobal.Logo" style="margin: 0px auto;text-align:center;" class="img-responsive" />
</div>

@if (Model != null)
{
    string HtmlMsg = Model.Error.ToString().Replace("\r\n", "<br />");
    <div class="text-center">
        <p class="bg-danger"><h2>@Html.Raw(HtmlMsg)</h2></p>
    </div>

    <div class="col-xs-12 text-center">
        <a role="button" class="btn btn-sm btn-sys" href="@Url.Action(SharedGlobal.HomeIndex,"Home")">回首頁</a>
    </div>
}