﻿
@{
    ViewBag.Title = ViewBag.Panel_Title;

    IEnumerable<SelectListItem> classList = ViewBag.ClassItems;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ActionLink("回「新增批次給點/扣點」首頁", (string)ViewBag.IndexActionName, null, new { @class = "btn btn-sm btn-sys", @role = "button" })

@using (Html.BeginForm("ClassImportStudentTemp", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()

    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.Hidden("ADDT14_STYLE", (string)ViewBag.ADDT14_STYLE)

    <br />
    <div class="Div-EZ-ADDI09 row">

        @foreach (var item in classList)
        {
            <div class="col-md-3">
                <h2>
                    <input type="checkbox" name="classSelect" value="@item.Value" /> <span>@item.Text</span>
                </h2>
            </div>
        }


    </div>
    <br />
    <div class="text-center">
        <input type="submit" class="btn btn-default btn-lg" value="確定匯入" />
    </div>
}

@section css{
    <style>
        input[type="checkbox"] {
            width: 25px; /*Desired width*/
            height: 25px; /*Desired height*/
        }
    </style>
}

@section Scripts {
    <script language="JavaScript">
        $("input[type=submit]").on('click', function () {
            let classArr = [];
            let checks = $("input[type=checkbox]:checked");
            $.each(checks, function (idx, item) {
                classArr.push(item.value);
            });
            console.log(classArr);
            // save temp class
            localStorage.setItem("temp_class_array", JSON.stringify(classArr));
        });
    </script>
}
