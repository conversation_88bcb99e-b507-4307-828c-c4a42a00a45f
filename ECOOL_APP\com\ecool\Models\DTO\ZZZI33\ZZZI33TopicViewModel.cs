﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI33TopicViewModel
    {
        /// <summary>
        ///投票ID
        /// </summary>
        [DisplayName("投票ID")]
        public string QUESTIONNAIRE_ID { get; set; }

        /// <summary>
        ///投票題目序號
        /// </summary>
        [DisplayName("投票題目序號")]
        public int? Q_NUM { get; set; }

        /// <summary>
        ///投票題目描述
        /// </summary>
        [DisplayName("投票題目描述")]
        public string Q_SUBJECT { get; set; }

        /// <summary>
        ///投票題目排序
        /// </summary>
        [DisplayName("投票題目排序")]
        public int? Q_ORDER_BY { get; set; }

        /// <summary>
        ///必填與否
        /// </summary>
        [DisplayName("必填與否")]
        public byte? Q_MUST { get; set; }

        /// <summary>
        ///類別
        /// </summary>
        [DisplayName("類別")]
        public byte? Q_TYPE { get; set; }

        /// <summary>
        ///說明(給設計人看的)
        /// </summary>
        [DisplayName("說明(給設計人看的)")]
        public string Q_MEMO { get; set; }

        /// <summary>
        /// Input 設定檔
        /// </summary>
        public List<SAQT03> Topic_D { get; set; }


        public int? Index { get; set; }

        public List<string> ArrANSWER { get; set; }

        private string _ANSWER;
        public string ANSWER
        {
            get
            {

                if (ArrANSWER != null)
                {
                    return String.Join(",", ArrANSWER);
                }
                else
                {
                    return _ANSWER;
                }
            }

            set { _ANSWER = value; }

        }
    }
}