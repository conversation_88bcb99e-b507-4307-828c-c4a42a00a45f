﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public class ZZZI33EditTitleViewModel
    {
   

        /// <summary>
        ///投票ID
        /// </summary>
        [DisplayName("投票ID")]
        public string QUESTIONNAIRE_ID { get; set; }

        /// <summary>
        ///投票標題
        /// </summary>
        [DisplayName("活動名稱")]
        [Required(ErrorMessage = "此欄位必輸")]
        [UIHint("Html")]
        [AllowHtml]
        public string QUESTIONNAIRE_NAME { get; set; }

        /// <summary>
        ///投票敘述
        /// </summary>
        [DisplayName("內容敘述")]
        [UIHint("Html")]
        [AllowHtml]
        public string QUESTIONNAIRE_DESC { get; set; }

        /// <summary>
        ///投票開始日
        /// </summary>
        [DisplayName("開始日")]
        [Required(ErrorMessage = "此欄位必輸")]
        public DateTime? QUESTIONNAIRE_SDATE { get; set; }

        /// <summary>
        ///投票結束日
        /// </summary>
        [DisplayName("結束日")]
        [Required(ErrorMessage = "此欄位必輸")]
        public DateTime? QUESTIONNAIRE_EDATE { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public byte? STATUS { get; set; }

        [DisplayName("結束語內容")]
        [UIHint("Html")]
        [AllowHtml]
        public string END_DESC { get; set; }

        /// <summary>
        ///給予點數
        /// </summary>
        [DisplayName("酷幣點數")]
        [RegularExpression("^[0-9]+$", ErrorMessage = "請輸入正整數！")]
        [Range(0, 5, ErrorMessage = "範圍值為0~5")]
        public short? CASH { get; set; }

        /// <summary>
        ///結束開放看結果
        /// </summary>
        [DisplayName("結束開放看結果")]
        public bool RESULT { get; set; }
        [DisplayName("開放看結果人員")]
        public string RESULT_PERSON { get; set; }
        /// <summary>
        ///是否記名
        /// </summary>
        [DisplayName("是否記名")]
        public bool REGISTERED_BALLOT { get; set; }
        /// <summary>
        ///回答次數 null =>不限制
        /// </summary>
        [DisplayName("限制回答次數")]
        [RegularExpression("^[0-9]+$", ErrorMessage = "請輸入正整數！")]
        public int? ANSWER_COUNT { get; set; }

        /// <summary>
        ///是否限制對象
        /// </summary>
        [DisplayName("是否限制對象")]
        public string ANSWER_PERSON_YN { get; set; }
    }
}