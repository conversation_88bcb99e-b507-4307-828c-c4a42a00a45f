﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT13
    {
        ///Summary
        ///DIALOG_ID
        ///Summary
        [DisplayName("活動代碼")]
        public string DIALOG_ID { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
         [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("帳號代碼")]
        public string USER_NO { get; set; }

        ///Summary
        ///CLASS_NO
        ///Summary
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        ///Summary
        ///SYEAR
        ///Summary
        [DisplayName("年級")]
        public int? SYEAR { get; set; }

        ///Summary
        ///SEMESTER
        ///Summary
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        ///Summary
        ///SEAT_NO
        ///Summary
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        ///Summary
        ///NAME
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
         [DisplayName("姓名")]
        public string SNAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        [DisplayName("分數")]
        public int? Grade { get; set; }
        ///Summary
        ///SNAME
        ///Summary
        [DisplayName("次數")]
        public int? Count { get; set; }
        ///Summary
        ///CRE_PERSON
        ///Summary
        [DisplayName("建立人員")]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
         [DisplayName("通過時間")]
         [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public virtual DateTime? CRE_DATE { get; set; }


        [DisplayName("序號")]
        public short LOTTO_ORDER { get; set; }

    }


    /// <summary>
    /// 有分多次作答
    /// </summary>
    public class uADDT13_Each : uADDT13
    {
        [DisplayName("答題時間")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd hh時mm分}", ApplyFormatInEditMode = true)]
        public override DateTime? CRE_DATE { get; set; }
        /// <summary>
        /// 作答ID
        /// </summary>
        public string HIS_NO { get; set; }
        /// <summary>
        /// 是否全對: Y / N
        /// </summary>
        [DisplayName("是否全對")]
        public string RIGHT_YN { get; set; }

    }

}
