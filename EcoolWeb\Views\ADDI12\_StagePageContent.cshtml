﻿@model ADDI12IndexListViewModel
@using ECOOL_APP.com.ecool.service
@using ECOOL_APP.com.ecool.util
@{
    /**/

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    bool IsAdmin = ViewBag.IsAdmin;

    string DivHotImg = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-11.png");
    string DivNormalImg = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-10.png");
    string IconHotImg = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-07.png");

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<style type="text/css">
    .element {
        float: left;
        padding: 3px;
        box-sizing: border-box;
    }
</style>
<script src="~/Scripts/grids.js"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@Html.Hidden("AppMode", new { value = AppMode })
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)

@Html.HiddenFor(m => m.Search.WhereUSER_NO)
@Html.HiddenFor(m => m.Search.WhereSTAGE_ID)
@Html.HiddenFor(m => m.Search.WhereSTATUS)
@Html.HiddenFor(m => m.Search.WhereSearch)
@Html.HiddenFor(m => m.WhereIsColorboxForUser)
@Html.HiddenFor(m => m.ActionResultType)


<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">學校</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.Search.WhereSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">學號/姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.Search.WhereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
    </div>

    <div class="form-group">
        <label class="control-label">年級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.Search.WhereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.Search.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear(true);" />
</div>
        <br />

@*@if (Model.ActionResultType != ADDI12IndexListViewModel.ActionResultTypeVal.MyVideoView)
    {
        <div class="form-inline" role="form" id="Q_Div">
            <div class="form-group">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.Search.WhereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>

            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.Search.WhereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.Search.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear(true);" />
        </div>
        <br />
    }*@
<img src="~/Content/img/web-bar-STAGE-02.png" class="img-responsive" alt="Responsive image" style="min-width:300px;" />

<div class="Div-EZ-ArtGallery" style="min-width:288px;background-image:url('@ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_Asset-04.gif")');background-repeat:repeat;border-color:#a3bbdb; border-style:solid;">
    <div style="height:35px"></div>
    <div class="form-horizontal" style="display: table; margin: 0 auto;">
        <div class="row">
            @if (Model.ListData==null ||Model.ListData.Count() == 0)
            {
                <div class="col-xs-12 text-center">
                    <h3>目前尚未有表演活動</h3>
                </div>
            }
            @foreach (var item in Model.ListData)
            {
                <div class="col-md-4  col-xs-6 text-align:center;" style="height:300px">
                    <div class="element">

                        <div class="Div-EZ-ArtGallery-pointer-all">
                            <img class="Div-EZ-ArtGallery-book-all" />

                            @if (string.IsNullOrWhiteSpace(item.YOUTUBE_IMG))
                            {

                                <div class="colorboxPHOTO Div-EZ-ArtGallery-div-img-all" onclick="ShowColorbox('@item.STAGE_ID')">
                                    <img src="#" class="Div-EZ-ArtGallery-img-all" title="@item.STAGE_NAME" alt="@item.STAGE_NAME" />
                                </div>
                            }
                            else
                            {
                                string str = "";
                                str = $@"~\Content\ADDI12\" + item.SCHOOL_NO + $@"\" + item.STAGE_ID + $@"\Small\" + item.YOUTUBE_IMG;


                                          <div class="colorboxPHOTO Div-EZ-ArtGallery-div-img-all" onclick="ShowColorbox('@item.STAGE_ID')">
                                    <img src="@Url.Content(str)" class="Div-EZ-ArtGallery-img-all" title="@item.STAGE_NAME" alt="@item.STAGE_NAME" />
                                </div>
                            }
                        </div>

                        <div class="caption">
                            <p style="text-align:center;padding-top:5px; ">
                                @if (item.STATUS.ToString() == TableStatus.NG)
                                {
                                    <font color="red">已作廢</font>

                                }
                                else
                                {
                                    if (user != null)
                                    {
                                        if ((user.USER_KEY == item.CRE_PERSON && item.STATUS.ToString() == TableStatus.OK) || (item.STATUS.ToString() == TableStatus.OK && IsAdmin) || user.USER_NO == "stage")
                                        {

                                            <a class="btn btn-primary btn-xs" role="button" onclick="Edit_show('@item.STAGE_ID')">修改</a>
                                        }

                                    }
                                }

                                <a class="btn btn-primary btn-xs" role="button" onclick="onYoutubePersonList('@item.STAGE_ID')">
                                    表演人員
                                </a>
                            </p>
                            @{ECOOL_DEVEntities db = new ECOOL_DEVEntities();
                                BDMT01 bDMT01 = new BDMT01();
                                string SHortNAMEstr = "";
                                if (item.SCHOOL_NO != "ALL" && !string.IsNullOrWhiteSpace(item.SCHOOL_NO))
                                {
                                    SHortNAMEstr = item.SCHOOL_NO;
                                    bDMT01 = db.BDMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO).FirstOrDefault();
                                    if (bDMT01 != null) {
                                        SHortNAMEstr = bDMT01.SHORT_NAME;
                                    }
                                }
                                else
                                {

                                    SHortNAMEstr = "全部學校";

                                }

                            }
                            <strong>
                                學校:@(SHortNAMEstr)
                            </strong>    <br />
                            <strong>
                                @if (item.CRE_DATE != null )
                                {
                                上架時間: item.CRE_DATE.Value.ToString("yyyy/MM/dd");
                                }
                                else {
                                @:上架時間:

                                }

                            </strong>
                            <br />
                            <strong>主題:<font color="#c01000">@item.STAGE_NAME</font></strong>
                            <br />
                            @*<strong>
            表演者:<font color="#c01000">

                @(item.View_PERSON.Substring(0, item.View_PERSON.Length - 1))
            </font>
            <a href="#" data-toggle="tooltip" data-placement="top" title="@item.Show_PERSON.Substring(0, item.Show_PERSON.Length - 1)">.....</a>
        </strong>*@
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<div>
    @*@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                                                        .DisplayTemplate(PageGlobal.BootstrapPaginationTwo)
                                                        .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                                        .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                                        .SetNextPageText(PageGlobal.DfSetNextPageText)
                                                        )*@
</div>

<script type="text/javascript">

    $('.element').responsiveEqualHeightGrid();

    var targetFormID = '#form1';

    function ShowColorbox(STAGE_ID)
     {
        var AppMode = $('#AppMode').val();

       if (AppMode.toLowerCase() == 'true') {
           window.location = '@Url.Action("YoutubeView", (string)ViewBag.BRE_NO)?STAGE_ID=' + STAGE_ID + '&ActionResultType='+ $('#@Html.IdFor(m=>m.ActionResultType)').val() +''
        }
        else {
                    $.ajax({
                        type: 'Get',
                        url: '@Url.Action("YoutubeView", (string)ViewBag.BRE_NO)?STAGE_ID=' + STAGE_ID,
                        data: $(targetFormID).serialize(),
                        success: function(data) {
                                $.colorbox({ html: data, width: "100%", height: "100%", opacity: 0.82 });
                        }
         });
        }
    }
     

</script>