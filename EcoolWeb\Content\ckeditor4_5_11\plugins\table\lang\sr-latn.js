﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'sr-latn', {
	border: 'Velič<PERSON> okvira',
	caption: 'Naslov tabele',
	cell: {
		menu: 'Cell',
		insertBefore: 'Insert Cell Before',
		insertAfter: 'Insert Cell After',
		deleteCell: 'Obriši ćelije',
		merge: 'Spoj celije',
		mergeRight: 'Merge Right',
		mergeDown: 'Merge Down',
		splitHorizontal: 'Split Cell Horizontally',
		splitVertical: 'Split Cell Vertically',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'Background Color',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'Razmak ćelija',
	cellSpace: 'Ćelijski prostor',
	column: {
		menu: 'Column',
		insertBefore: 'Insert Column Before',
		insertAfter: 'Insert Column After',
		deleteColumn: 'Obriši kolone'
	},
	columns: 'Kolona',
	deleteTable: 'Izbriši tabelu',
	headers: 'Zaglavlja',
	headersBoth: 'Oba',
	headersColumn: 'Prva kolona',
	headersNone: 'None',
	headersRow: 'Prvi red',
	invalidBorder: 'Veličina okvira mora biti broj.',
	invalidCellPadding: 'Padding polja mora biti pozitivan broj.',
	invalidCellSpacing: 'Razmak između ćelija mora biti pozitivan broj.',
	invalidCols: 'Broj kolona mora biti broj veći od 0.',
	invalidHeight: 'Visina tabele mora biti broj.',
	invalidRows: 'Broj redova mora biti veći od 0.',
	invalidWidth: 'Širina tabele mora biti broj.',
	menu: 'Osobine tabele',
	row: {
		menu: 'Row',
		insertBefore: 'Insert Row Before',
		insertAfter: 'Insert Row After',
		deleteRow: 'Obriši redove'
	},
	rows: 'Redova',
	summary: 'Sažetak',
	title: 'Osobine tabele',
	toolbar: 'Tabela',
	widthPc: 'procenata',
	widthPx: 'piksela',
	widthUnit: 'jedinica za širinu'
} );
