﻿@model GAAI0SpotCheckSearchViewModel

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.WhereSCHOOL_NO)
<div id="Q_Div">
    <div role="form">
        <div class="row" style="padding-top:10px">
            <div class="col-md-2">
                <label class="control-label">年級</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereGRADE, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm" })
            </div>
            <div class="col-md-2">
                <label class="control-label">班級</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm" })
            </div>
        </div>
        <div class="row" style="padding-top:10px">
            <div class="col-md-2">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="col-md-3">
                @Html.EditorFor(m => m.WhereUser, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
        </div>
        <div class="row" style="padding-top:10px">

            <div class="col-md-2">
                <label class="control-label">抽查時間</label>
            </div>
            <div class="col-sm-3">
                @Html.EditorFor(m => m.WhereALARM_DATEs, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
            </div>
            <div class="col-sm-1">
                ~
            </div>
            <div class="col-sm-3">
                @Html.EditorFor(m => m.WhereALARM_DATEe, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
            </div>
        </div>
        <br />
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="funAjax()" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('true')" />

        @if (Model.ListData?.Count() > 0)
        {
            <input type="button" class="btn-yellow btn btn-sm" value="匯出Excel" onclick="onSpotCheckSearchPrintExcel()" />
        }
    </div>
</div>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-white">
                <tr class="thead-primary">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ALARM_DATE, Model?.OrderByColumnName, Model?.SortType, true)</th>

                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().USER_NO, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().SEAT_NO, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().GRADE, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CLASS_NO, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().IS_WEAR, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CASH, Model?.OrderByColumnName, Model?.SortType, true)</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {
                        <tr class="text-center">
                            <td>@Html.DisplayFor(modelItem => item.ALARM_DATE)</td>

                            <td>@Html.DisplayFor(modelItem => item.USER_NO)</td>
                            <td>@Html.DisplayFor(modelItem => item.NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                            <td>@Html.DisplayFor(modelItem => item.GRADE)</td>
                            <td>@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                            <td>
                                @if ((item.IS_WEAR ?? false) == true)
                                {
                                    <span>有配載</span>
                                }
                                else
                                {
                                    <span>未配載</span>
                                }
                            </td>
                            <td>@Html.DisplayFor(modelItem => item.CASH)</td>
                        </tr>
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center" style="padding:10px"><strong>目前無資料</strong></div>
        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)

<script>
        $(document).ready(function () {

                $("#@Html.IdFor(m=>m.WhereALARM_DATEs),#@Html.IdFor(m=>m.WhereALARM_DATEe)").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
                });

        });
</script>