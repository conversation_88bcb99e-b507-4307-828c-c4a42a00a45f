﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public class IawardKindModel
    {
        public string Text { get; set; }

        public string Value { get; set; }

        public int? CASH_S { get; set; }

        public int? CASH_E { get; set; }
    }

    public class IawardKind
    {
        private static List<IawardKindModel> SetIawardKind()
        {
            List<IawardKindModel> Cre = new List<IawardKindModel>
            {
                 new IawardKindModel() { Text = "學習表現", Value = "學習表現", CASH_S = 10, CASH_E = 200 },
                new IawardKindModel() { Text = "校內競賽", Value = "校內競賽", CASH_S = 10, CASH_E = 200 },
                new IawardKindModel() { Text = "品德表現", Value = "品德表現", CASH_S = 10, CASH_E = 200 },
                new IawardKindModel() { Text = "志工", Value = "志工", CASH_S = 50, CASH_E = 100 },
       
                new IawardKindModel() { Text = "班級幹部", Value = "班級幹部", CASH_S = 10, CASH_E = 200 },


                new IawardKindModel() { Text = "班級幫手和榮譽", Value = "班級幫手和榮譽", CASH_S = 10, CASH_E = 200 },
                new IawardKindModel() { Text = "其他", Value = "其他" },
                new IawardKindModel() { Text = "領導人", Value = "領導人", CASH_S = 50, CASH_E = 100 },
               new IawardKindModel() { Text = "七個習慣代言人", Value = "七個習慣代言人", CASH_S = 50, CASH_E = 100 }
            };
            return Cre;
        }

        /// <summary>
        /// 取得獎懲類別清單 含 Cash 限定值
        /// </summary>
        public static List<IawardKindModel> GetIawardKindList = SetIawardKind();

        /// <summary>
        /// 取得獎懲類別清單
        /// </summary>
        /// <param name="SelectedVal"></param>
        /// <returns></returns>
        public static List<SelectListItem> GetIAWARD_KIND(string SelectedVal = "")
        {
            List<SelectListItem> IAWARD_KIND = new List<SelectListItem>
            {
                new SelectListItem { Text = "請選擇事蹟", Value = "", Selected = string.IsNullOrWhiteSpace(SelectedVal) }
            };

            IAWARD_KIND.AddRange(GetIawardKindList.Select(x => new SelectListItem
            {
                Text = x.Text,
                Value = x.Value,
                Selected = SelectedVal == x.Value
            }).ToList());

            return IAWARD_KIND;
        }
    }
}