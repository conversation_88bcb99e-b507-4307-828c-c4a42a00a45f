/*
 Highcharts JS v4.2.0 (2105-12-15)

 3D features for Highcharts JS

 @license: www.highcharts.com/license
*/
(function(c){typeof module==="object"&&module.exports?module.exports=c:c(Highcharts)})(function(c){function r(d,b,a){var e,g,f=b.options.chart.options3d,c=!1;a?(c=b.inverted,a=b.plotWidth/2,b=b.plotHeight/2,e=f.depth/2,g=u(f.depth,1)*u(f.viewDistance,0)):(a=b.plotLeft+b.plotWidth/2,b=b.plotTop+b.plotHeight/2,e=f.depth/2,g=u(f.depth,1)*u(f.viewDistance,0));var j=[],k=a,i=b,l=e,w=g,a=A*(c?f.beta:-f.beta),f=A*(c?-f.alpha:f.alpha),o=p(a),q=m(a),n=p(f),y=m(f),x,s,z,t,v,C;B(d,function(a){x=(c?a.y:a.x)-
k;s=(c?a.x:a.y)-i;z=(a.z||0)-l;t=q*x-o*z;v=-o*n*x+y*s-q*n*z;C=o*y*x+n*s+q*y*z;w>0&&w<Number.POSITIVE_INFINITY&&(t*=w/(C+l+w),v*=w/(C+l+w));t+=k;v+=i;C+=l;j.push({x:c?v:t,y:c?t:v,z:C})});return j}function D(d){return d!==void 0&&d!==null}function I(d){var b=0,a,e;for(a=0;a<d.length;a++)e=(a+1)%d.length,b+=d[a].x*d[e].y-d[e].x*d[a].y;return b/2}function F(d){var b=0,a;for(a=0;a<d.length;a++)b+=d[a].z;return d.length?b/d.length:0}function q(d,b,a,e,c,f,h,j){var k=[];f>c&&f-c>o/2+1.0E-4?(k=k.concat(q(d,
b,a,e,c,c+o/2,h,j)),k=k.concat(q(d,b,a,e,c+o/2,f,h,j))):f<c&&c-f>o/2+1.0E-4?(k=k.concat(q(d,b,a,e,c,c-o/2,h,j)),k=k.concat(q(d,b,a,e,c-o/2,f,h,j))):(k=f-c,k=["C",d+a*m(c)-a*E*k*p(c)+h,b+e*p(c)+e*E*k*m(c)+j,d+a*m(f)+a*E*k*p(f)+h,b+e*p(f)-e*E*k*m(f)+j,d+a*m(f)+h,b+e*p(f)+j]);return k}function J(d){if(this.chart.is3d()){var b=this.chart.options.plotOptions.column.grouping;if(b!==void 0&&!b&&this.group.zIndex!==void 0&&!this.zIndexSet)this.group.attr({zIndex:this.group.zIndex*10}),this.zIndexSet=!0;var a=
this.options,e=this.options.states;this.borderWidth=a.borderWidth=D(a.edgeWidth)?a.edgeWidth:1;c.each(this.data,function(b){if(b.y!==null)b=b.pointAttr,this.borderColor=c.pick(a.edgeColor,b[""].fill),b[""].stroke=this.borderColor,b.hover.stroke=c.pick(e.hover.edgeColor,this.borderColor),b.select.stroke=c.pick(e.select.edgeColor,this.borderColor)})}d.apply(this,[].slice.call(arguments,1))}var B=c.each,M=c.extend,N=c.inArray,G=c.merge,u=c.pick,K=c.wrap,o=Math.PI,A=o/180,p=Math.sin,m=Math.cos,L=Math.round;
c.perspective=r;var E=4*(Math.sqrt(2)-1)/3/(o/2);c.SVGRenderer.prototype.toLinePath=function(d,b){var a=[];c.each(d,function(b){a.push("L",b.x,b.y)});d.length&&(a[0]="M",b&&a.push("Z"));return a};c.SVGRenderer.prototype.cuboid=function(d){var b=this.g(),d=this.cuboidPath(d);b.front=this.path(d[0]).attr({zIndex:d[3],"stroke-linejoin":"round"}).add(b);b.top=this.path(d[1]).attr({zIndex:d[4],"stroke-linejoin":"round"}).add(b);b.side=this.path(d[2]).attr({zIndex:d[5],"stroke-linejoin":"round"}).add(b);
b.fillSetter=function(a){var b=c.Color(a).brighten(0.1).get(),d=c.Color(a).brighten(-0.1).get();this.front.attr({fill:a});this.top.attr({fill:b});this.side.attr({fill:d});this.color=a;return this};b.opacitySetter=function(a){this.front.attr({opacity:a});this.top.attr({opacity:a});this.side.attr({opacity:a});return this};b.attr=function(a){a.shapeArgs||D(a.x)?(a=this.renderer.cuboidPath(a.shapeArgs||a),this.front.attr({d:a[0],zIndex:a[3]}),this.top.attr({d:a[1],zIndex:a[4]}),this.side.attr({d:a[2],
zIndex:a[5]})):c.SVGElement.prototype.attr.call(this,a);return this};b.animate=function(a,b,d){D(a.x)&&D(a.y)?(a=this.renderer.cuboidPath(a),this.front.attr({zIndex:a[3]}).animate({d:a[0]},b,d),this.top.attr({zIndex:a[4]}).animate({d:a[1]},b,d),this.side.attr({zIndex:a[5]}).animate({d:a[2]},b,d)):a.opacity?(this.front.animate(a,b,d),this.top.animate(a,b,d),this.side.animate(a,b,d)):c.SVGElement.prototype.animate.call(this,a,b,d);return this};b.destroy=function(){this.front.destroy();this.top.destroy();
this.side.destroy();return null};b.attr({zIndex:-d[3]});return b};c.SVGRenderer.prototype.cuboidPath=function(d){function b(a){return i[a]}var a=d.x,e=d.y,g=d.z,f=d.height,h=d.width,j=d.depth,k=c.map,i=[{x:a,y:e,z:g},{x:a+h,y:e,z:g},{x:a+h,y:e+f,z:g},{x:a,y:e+f,z:g},{x:a,y:e+f,z:g+j},{x:a+h,y:e+f,z:g+j},{x:a+h,y:e,z:g+j},{x:a,y:e,z:g+j}],i=r(i,c.charts[this.chartIndex],d.insidePlotArea),e=function(a,d){a=k(a,b);d=k(d,b);return I(a)<0?a:I(d)<0?d:[]},d=e([3,2,1,0],[7,6,5,4]),a=e([1,6,7,0],[4,5,2,3]),
e=e([1,2,5,6],[0,7,4,3]);return[this.toLinePath(d,!0),this.toLinePath(a,!0),this.toLinePath(e,!0),F(d),F(a),F(e)]};c.SVGRenderer.prototype.arc3d=function(d){function b(a){var b=!1,d={},e;for(e in a)N(e,g)!==-1&&(d[e]=a[e],delete a[e],b=!0);return b?d:!1}var a=this.g(),e=a.renderer,g="x,y,r,innerR,start,end".split(","),d=G(d);d.alpha*=A;d.beta*=A;a.top=e.path();a.side1=e.path();a.side2=e.path();a.inn=e.path();a.out=e.path();a.onAdd=function(){var b=a.parentGroup;a.top.add(a);a.out.add(b);a.inn.add(b);
a.side1.add(b);a.side2.add(b)};a.setPaths=function(b){var d=a.renderer.arc3dPath(b),e=d.zTop*100;a.attribs=b;a.top.attr({d:d.top,zIndex:d.zTop});a.inn.attr({d:d.inn,zIndex:d.zInn});a.out.attr({d:d.out,zIndex:d.zOut});a.side1.attr({d:d.side1,zIndex:d.zSide1});a.side2.attr({d:d.side2,zIndex:d.zSide2});a.zIndex=e;a.attr({zIndex:e});b.center&&(a.top.setRadialReference(b.center),delete b.center)};a.setPaths(d);a.fillSetter=function(a){var b=c.Color(a).brighten(-0.1).get();this.fill=a;this.side1.attr({fill:b});
this.side2.attr({fill:b});this.inn.attr({fill:b});this.out.attr({fill:b});this.top.attr({fill:a});return this};B(["opacity","translateX","translateY","visibility"],function(b){a[b+"Setter"]=function(b,d){a[d]=b;B(["out","inn","side1","side2","top"],function(e){a[e].attr(d,b)})}});K(a,"attr",function(d,e,c){var g;if(typeof e==="object"&&(g=b(e)))M(a.attribs,g),a.setPaths(a.attribs);return d.call(this,e,c)});K(a,"animate",function(a,d,e,c){var g,l=this.attribs,m;delete d.center;delete d.z;delete d.depth;
delete d.alpha;delete d.beta;if(e=u(e,this.renderer.globalAnimation))if(typeof e!=="object"&&(e={}),d=G(d),g=b(d))m=g,e.step=function(a,b){function d(a){return l[a]+(u(m[a],l[a])-l[a])*b.pos}b.elem.setPaths(G(l,{x:d("x"),y:d("y"),r:d("r"),innerR:d("innerR"),start:d("start"),end:d("end")}))};return a.call(this,d,e,c)});a.destroy=function(){this.top.destroy();this.out.destroy();this.inn.destroy();this.side1.destroy();this.side2.destroy();c.SVGElement.prototype.destroy.call(this)};a.hide=function(){this.top.hide();
this.out.hide();this.inn.hide();this.side1.hide();this.side2.hide()};a.show=function(){this.top.show();this.out.show();this.inn.show();this.side1.show();this.side2.show()};return a};c.SVGRenderer.prototype.arc3dPath=function(d){function b(a){a%=2*o;a>o&&(a=2*o-a);return a}var a=d.x,e=d.y,c=d.start,f=d.end-1.0E-5,h=d.r,j=d.innerR,k=d.depth,i=d.alpha,l=d.beta,w=m(c),u=p(c),d=m(f),r=p(f),n=h*m(l);h*=m(i);var y=j*m(l),x=j*m(i),j=k*p(l),s=k*p(i),k=["M",a+n*w,e+h*u],k=k.concat(q(a,e,n,h,c,f,0,0)),k=k.concat(["L",
a+y*d,e+x*r]),k=k.concat(q(a,e,y,x,f,c,0,0)),k=k.concat(["Z"]),z=l>0?o/2:0,l=i>0?0:o/2,z=c>-z?c:f>-z?-z:c,t=f<o-l?f:c<o-l?o-l:f,v=2*o-l,i=["M",a+n*m(z),e+h*p(z)],i=i.concat(q(a,e,n,h,z,t,0,0));f>v&&c<v?(i=i.concat(["L",a+n*m(t)+j,e+h*p(t)+s]),i=i.concat(q(a,e,n,h,t,v,j,s)),i=i.concat(["L",a+n*m(v),e+h*p(v)]),i=i.concat(q(a,e,n,h,v,f,0,0)),i=i.concat(["L",a+n*m(f)+j,e+h*p(f)+s]),i=i.concat(q(a,e,n,h,f,v,j,s)),i=i.concat(["L",a+n*m(v),e+h*p(v)]),i=i.concat(q(a,e,n,h,v,t,0,0))):f>o-l&&c<o-l&&(i=i.concat(["L",
a+n*m(t)+j,e+h*p(t)+s]),i=i.concat(q(a,e,n,h,t,f,j,s)),i=i.concat(["L",a+n*m(f),e+h*p(f)]),i=i.concat(q(a,e,n,h,f,t,0,0)));i=i.concat(["L",a+n*m(t)+j,e+h*p(t)+s]);i=i.concat(q(a,e,n,h,t,z,j,s));i=i.concat(["Z"]);l=["M",a+y*w,e+x*u];l=l.concat(q(a,e,y,x,c,f,0,0));l=l.concat(["L",a+y*m(f)+j,e+x*p(f)+s]);l=l.concat(q(a,e,y,x,f,c,j,s));l=l.concat(["Z"]);w=["M",a+n*w,e+h*u,"L",a+n*w+j,e+h*u+s,"L",a+y*w+j,e+x*u+s,"L",a+y*w,e+x*u,"Z"];a=["M",a+n*d,e+h*r,"L",a+n*d+j,e+h*r+s,"L",a+y*d+j,e+x*r+s,"L",a+y*d,
e+x*r,"Z"];r=Math.atan2(s,-j);e=Math.abs(f+r);d=Math.abs(c+r);c=Math.abs((c+f)/2+r);e=b(e);d=b(d);c=b(c);c*=1E5;f=d*1E5;e*=1E5;return{top:k,zTop:o*1E5+1,out:i,zOut:Math.max(c,f,e),inn:l,zInn:Math.max(c,f,e),side1:w,zSide1:e*0.99,side2:a,zSide2:f*0.99}};c.Chart.prototype.is3d=function(){return this.options.chart.options3d&&this.options.chart.options3d.enabled};c.wrap(c.Chart.prototype,"isInsidePlot",function(d){return this.is3d()||d.apply(this,[].slice.call(arguments,1))});c.getOptions().chart.options3d=
{enabled:!1,alpha:0,beta:0,depth:100,viewDistance:25,frame:{bottom:{size:1,color:"rgba(255,255,255,0)"},side:{size:1,color:"rgba(255,255,255,0)"},back:{size:1,color:"rgba(255,255,255,0)"}}};c.wrap(c.Chart.prototype,"init",function(d){var b=[].slice.call(arguments,1),a;if(b[0].chart.options3d&&b[0].chart.options3d.enabled)b[0].chart.options3d.alpha=(b[0].chart.options3d.alpha||0)%360,b[0].chart.options3d.beta=(b[0].chart.options3d.beta||0)%360,a=b[0].plotOptions||{},a=a.pie||{},a.borderColor=c.pick(a.borderColor,
void 0);d.apply(this,b)});c.wrap(c.Chart.prototype,"setChartSize",function(d){d.apply(this,[].slice.call(arguments,1));if(this.is3d()){var b=this.inverted,a=this.clipBox,c=this.margin;a[b?"y":"x"]=-(c[3]||0);a[b?"x":"y"]=-(c[0]||0);a[b?"height":"width"]=this.chartWidth+(c[3]||0)+(c[1]||0);a[b?"width":"height"]=this.chartHeight+(c[0]||0)+(c[2]||0)}});c.wrap(c.Chart.prototype,"redraw",function(d){if(this.is3d())this.isDirtyBox=!0;d.apply(this,[].slice.call(arguments,1))});c.wrap(c.Chart.prototype,"renderSeries",
function(d){var b=this.series.length;if(this.is3d())for(;b--;)d=this.series[b],d.translate(),d.render();else d.call(this)});c.Chart.prototype.retrieveStacks=function(d){var b=this.series,a={},e,g=1;c.each(this.series,function(c){e=u(c.options.stack,d?0:b.length-1-c.index);a[e]?a[e].series.push(c):(a[e]={series:[c],position:g},g++)});a.totalStacks=g+1;return a};c.wrap(c.Axis.prototype,"setOptions",function(d,b){var a;d.call(this,b);if(this.chart.is3d())a=this.options,a.tickWidth=c.pick(a.tickWidth,
0),a.gridLineWidth=c.pick(a.gridLineWidth,1)});c.wrap(c.Axis.prototype,"render",function(d){d.apply(this,[].slice.call(arguments,1));if(this.chart.is3d()){var b=this.chart,a=b.renderer,c=b.options.chart.options3d,g=c.frame,f=g.bottom,h=g.back,g=g.side,j=c.depth,k=this.height,i=this.width,l=this.left,m=this.top;if(!this.isZAxis)this.horiz?(h={x:l,y:m+(b.xAxis[0].opposite?-f.size:k),z:0,width:i,height:f.size,depth:j,insidePlotArea:!1},this.bottomFrame?this.bottomFrame.animate(h):this.bottomFrame=a.cuboid(h).attr({fill:f.color,
zIndex:b.yAxis[0].reversed&&c.alpha>0?4:-1}).css({stroke:f.color}).add()):(c={x:l+(b.yAxis[0].opposite?0:-g.size),y:m+(b.xAxis[0].opposite?-f.size:0),z:j,width:i+g.size,height:k+f.size,depth:h.size,insidePlotArea:!1},this.backFrame?this.backFrame.animate(c):this.backFrame=a.cuboid(c).attr({fill:h.color,zIndex:-3}).css({stroke:h.color}).add(),b={x:l+(b.yAxis[0].opposite?i:-g.size),y:m+(b.xAxis[0].opposite?-f.size:0),z:0,width:g.size,height:k+f.size,depth:j,insidePlotArea:!1},this.sideFrame?this.sideFrame.animate(b):
this.sideFrame=a.cuboid(b).attr({fill:g.color,zIndex:-2}).css({stroke:g.color}).add())}});c.wrap(c.Axis.prototype,"getPlotLinePath",function(d){var b=d.apply(this,[].slice.call(arguments,1));if(!this.chart.is3d())return b;if(b===null)return b;var a=this.chart,c=a.options.chart.options3d,a=this.isZAxis?a.plotWidth:c.depth,c=this.opposite;this.horiz&&(c=!c);b=[this.swapZ({x:b[1],y:b[2],z:c?a:0}),this.swapZ({x:b[1],y:b[2],z:a}),this.swapZ({x:b[4],y:b[5],z:a}),this.swapZ({x:b[4],y:b[5],z:c?0:a})];b=r(b,
this.chart,!1);return b=this.chart.renderer.toLinePath(b,!1)});c.wrap(c.Axis.prototype,"getLinePath",function(d){return this.chart.is3d()?[]:d.apply(this,[].slice.call(arguments,1))});c.wrap(c.Axis.prototype,"getPlotBandPath",function(d){if(!this.chart.is3d())return d.apply(this,[].slice.call(arguments,1));var b=arguments,a=b[1],b=this.getPlotLinePath(b[2]);(a=this.getPlotLinePath(a))&&b?a.push("L",b[10],b[11],"L",b[7],b[8],"L",b[4],b[5],"L",b[1],b[2]):a=null;return a});c.wrap(c.Tick.prototype,"getMarkPath",
function(d){var b=d.apply(this,[].slice.call(arguments,1));if(!this.axis.chart.is3d())return b;b=[this.axis.swapZ({x:b[1],y:b[2],z:0}),this.axis.swapZ({x:b[4],y:b[5],z:0})];b=r(b,this.axis.chart,!1);return b=["M",b[0].x,b[0].y,"L",b[1].x,b[1].y]});c.wrap(c.Tick.prototype,"getLabelPosition",function(d){var b=d.apply(this,[].slice.call(arguments,1));if(!this.axis.chart.is3d())return b;var a=r([this.axis.swapZ({x:b.x,y:b.y,z:0})],this.axis.chart,!1)[0];a.x-=!this.axis.horiz&&this.axis.opposite?this.axis.transA:
0;a.old=b;return a});c.wrap(c.Tick.prototype,"handleOverflow",function(d,b){if(this.axis.chart.is3d())b=b.old;return d.call(this,b)});c.wrap(c.Axis.prototype,"getTitlePosition",function(d){var b=this.chart.is3d(),a,c;if(b)c=this.axisTitleMargin,this.axisTitleMargin=0;a=d.apply(this,[].slice.call(arguments,1));if(b)a=r([this.swapZ({x:a.x,y:a.y,z:0})],this.chart,!1)[0],a[this.horiz?"y":"x"]+=(this.horiz?1:-1)*(this.opposite?-1:1)*c,this.axisTitleMargin=c;return a});c.wrap(c.Axis.prototype,"drawCrosshair",
function(d){var b=arguments;this.chart.is3d()&&b[2]&&(b[2]={plotX:b[2].plotXold||b[2].plotX,plotY:b[2].plotYold||b[2].plotY});d.apply(this,[].slice.call(b,1))});c.Axis.prototype.swapZ=function(d,b){if(this.isZAxis){var a=b?0:this.chart.plotLeft,c=this.chart;return{x:a+(c.yAxis[0].opposite?d.z:c.xAxis[0].width-d.z),y:d.y,z:d.x-a}}return d};var H=c.ZAxis=function(){this.isZAxis=!0;this.init.apply(this,arguments)};c.extend(H.prototype,c.Axis.prototype);c.extend(H.prototype,{setOptions:function(d){d=
c.merge({offset:0,lineWidth:0},d);c.Axis.prototype.setOptions.call(this,d);this.coll="zAxis"},setAxisSize:function(){c.Axis.prototype.setAxisSize.call(this);this.width=this.len=this.chart.options.chart.options3d.depth;this.right=this.chart.chartWidth-this.width-this.left},getSeriesExtremes:function(){var d=this,b=d.chart;d.hasVisibleSeries=!1;d.dataMin=d.dataMax=d.ignoreMinPadding=d.ignoreMaxPadding=null;d.buildStacks&&d.buildStacks();c.each(d.series,function(a){if(a.visible||!b.options.chart.ignoreHiddenSeries)if(d.hasVisibleSeries=
!0,a=a.zData,a.length)d.dataMin=Math.min(u(d.dataMin,a[0]),Math.min.apply(null,a)),d.dataMax=Math.max(u(d.dataMax,a[0]),Math.max.apply(null,a))})}});c.wrap(c.Chart.prototype,"getAxes",function(d){var b=this,a=this.options,a=a.zAxis=c.splat(a.zAxis||{});d.call(this);if(b.is3d())this.zAxis=[],c.each(a,function(a,d){a.index=d;a.isX=!0;(new H(b,a)).setScale()})});c.wrap(c.seriesTypes.column.prototype,"translate",function(d){d.apply(this,[].slice.call(arguments,1));if(this.chart.is3d()){var b=this.chart,
a=this.options,e=a.depth||25,g=(a.stacking?a.stack||0:this._i)*(e+(a.groupZPadding||1));a.grouping!==!1&&(g=0);g+=a.groupZPadding||1;c.each(this.data,function(a){if(a.y!==null){var d=a.shapeArgs,c=a.tooltipPos;a.shapeType="cuboid";d.z=g;d.depth=e;d.insidePlotArea=!0;c=r([{x:c[0],y:c[1],z:g}],b,!1)[0];a.tooltipPos=[c.x,c.y]}});this.z=g}});c.wrap(c.seriesTypes.column.prototype,"animate",function(d){if(this.chart.is3d()){var b=arguments[1],a=this.yAxis,e=this,g=this.yAxis.reversed;if(c.svg)b?c.each(e.data,
function(b){if(b.y!==null&&(b.height=b.shapeArgs.height,b.shapey=b.shapeArgs.y,b.shapeArgs.height=1,!g))b.shapeArgs.y=b.stackY?b.plotY+a.translate(b.stackY):b.plotY+(b.negative?-b.height:b.height)}):(c.each(e.data,function(a){if(a.y!==null)a.shapeArgs.height=a.height,a.shapeArgs.y=a.shapey,a.graphic&&a.graphic.animate(a.shapeArgs,e.options.animation)}),this.drawDataLabels(),e.animate=null)}else d.apply(this,[].slice.call(arguments,1))});c.wrap(c.seriesTypes.column.prototype,"init",function(d){d.apply(this,
[].slice.call(arguments,1));if(this.chart.is3d()){var b=this.options,a=b.grouping,c=b.stacking,g=u(this.yAxis.options.reversedStacks,!0),f=0;if(a===void 0||a){a=this.chart.retrieveStacks(c);f=b.stack||0;for(c=0;c<a[f].series.length;c++)if(a[f].series[c]===this)break;f=10*(a.totalStacks-a[f].position)+(g?c:-c);this.xAxis.reversed||(f=a.totalStacks*10-f)}b.zIndex=f}});c.wrap(c.Series.prototype,"alignDataLabel",function(c){if(this.chart.is3d()&&(this.type==="column"||this.type==="columnrange")){var b=
arguments[4],a={x:b.x,y:b.y,z:this.z},a=r([a],this.chart,!0)[0];b.x=a.x;b.y=a.y}c.apply(this,[].slice.call(arguments,1))});c.seriesTypes.columnrange&&c.wrap(c.seriesTypes.columnrange.prototype,"drawPoints",J);c.wrap(c.seriesTypes.column.prototype,"drawPoints",J);c.wrap(c.seriesTypes.pie.prototype,"translate",function(c){c.apply(this,[].slice.call(arguments,1));if(this.chart.is3d()){var b=this,a=b.options,e=a.depth||0,g=b.chart.options.chart.options3d,f=g.alpha,h=g.beta,j=a.stacking?(a.stack||0)*e:
b._i*e;j+=e/2;a.grouping!==!1&&(j=0);B(b.data,function(c){var d=c.shapeArgs;c.shapeType="arc3d";d.z=j;d.depth=e*0.75;d.alpha=f;d.beta=h;d.center=b.center;d=(d.end+d.start)/2;c.slicedTranslation={translateX:L(m(d)*a.slicedOffset*m(f*A)),translateY:L(p(d)*a.slicedOffset*m(f*A))}})}});c.wrap(c.seriesTypes.pie.prototype.pointClass.prototype,"haloPath",function(c){var b=arguments;return this.series.chart.is3d()?[]:c.call(this,b[1])});c.wrap(c.seriesTypes.pie.prototype,"drawPoints",function(d){var b=this.options,
a=b.states;if(this.chart.is3d())this.borderWidth=b.borderWidth=b.edgeWidth||1,this.borderColor=b.edgeColor=c.pick(b.edgeColor,b.borderColor,void 0),a.hover.borderColor=c.pick(a.hover.edgeColor,this.borderColor),a.hover.borderWidth=c.pick(a.hover.edgeWidth,this.borderWidth),a.select.borderColor=c.pick(a.select.edgeColor,this.borderColor),a.select.borderWidth=c.pick(a.select.edgeWidth,this.borderWidth),B(this.data,function(b){var c=b.pointAttr;c[""].stroke=b.series.borderColor||b.color;c[""]["stroke-width"]=
b.series.borderWidth;c.hover.stroke=a.hover.borderColor;c.hover["stroke-width"]=a.hover.borderWidth;c.select.stroke=a.select.borderColor;c.select["stroke-width"]=a.select.borderWidth});d.apply(this,[].slice.call(arguments,1));this.chart.is3d()&&B(this.points,function(a){var b=a.graphic;if(b)b[a.y?"show":"hide"]()})});c.wrap(c.seriesTypes.pie.prototype,"drawDataLabels",function(c){if(this.chart.is3d()){var b=this.chart.options.chart.options3d;B(this.data,function(a){var c=a.shapeArgs,d=c.r,f=(c.beta||
b.beta)*A,h=(c.start+c.end)/2,j=a.labelPos,k=-d*(1-m((c.alpha||b.alpha)*A))*p(h),i=d*(m(f)-1)*m(h);B([0,2,4],function(a){j[a]+=i;j[a+1]+=k})})}c.apply(this,[].slice.call(arguments,1))});c.wrap(c.seriesTypes.pie.prototype,"addPoint",function(c){c.apply(this,[].slice.call(arguments,1));this.chart.is3d()&&this.update(this.userOptions,!0)});c.wrap(c.seriesTypes.pie.prototype,"animate",function(d){if(this.chart.is3d()){var b=arguments[1],a=this.options.animation,e=this.center,g=this.group,f=this.markerGroup;
if(c.svg)if(a===!0&&(a={}),b){if(g.oldtranslateX=g.translateX,g.oldtranslateY=g.translateY,b={translateX:e[0],translateY:e[1],scaleX:0.001,scaleY:0.001},g.attr(b),f)f.attrSetters=g.attrSetters,f.attr(b)}else b={translateX:g.oldtranslateX,translateY:g.oldtranslateY,scaleX:1,scaleY:1},g.animate(b,a),f&&f.animate(b,a),this.animate=null}else d.apply(this,[].slice.call(arguments,1))});c.wrap(c.seriesTypes.scatter.prototype,"translate",function(d){d.apply(this,[].slice.call(arguments,1));if(this.chart.is3d()){var b=
this.chart,a=c.pick(this.zAxis,b.options.zAxis[0]),e=[],g,f,h;for(h=0;h<this.data.length;h++)g=this.data[h],f=a.isLog&&a.val2lin?a.val2lin(g.z):g.z,g.plotZ=a.translate(f),g.isInside=g.isInside?f>=a.min&&f<=a.max:!1,e.push({x:g.plotX,y:g.plotY,z:g.plotZ});b=r(e,b,!0);for(h=0;h<this.data.length;h++)g=this.data[h],a=b[h],g.plotXold=g.plotX,g.plotYold=g.plotY,g.plotX=a.x,g.plotY=a.y,g.plotZ=a.z}});c.wrap(c.seriesTypes.scatter.prototype,"init",function(c,b,a){if(b.is3d())this.axisTypes=["xAxis","yAxis",
"zAxis"],this.pointArrayMap=["x","y","z"],this.parallelArrays=["x","y","z"];c=c.apply(this,[b,a]);if(this.chart.is3d())this.tooltipOptions.pointFormat=this.userOptions.tooltip?this.userOptions.tooltip.pointFormat||"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>":"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>";return c});if(c.VMLRenderer)c.setOptions({animate:!1}),c.VMLRenderer.prototype.cuboid=c.SVGRenderer.prototype.cuboid,c.VMLRenderer.prototype.cuboidPath=
c.SVGRenderer.prototype.cuboidPath,c.VMLRenderer.prototype.toLinePath=c.SVGRenderer.prototype.toLinePath,c.VMLRenderer.prototype.createElement3D=c.SVGRenderer.prototype.createElement3D,c.VMLRenderer.prototype.arc3d=function(d){d=c.SVGRenderer.prototype.arc3d.call(this,d);d.css({zIndex:d.zIndex});return d},c.VMLRenderer.prototype.arc3dPath=c.SVGRenderer.prototype.arc3dPath,c.wrap(c.Axis.prototype,"render",function(c){c.apply(this,[].slice.call(arguments,1));this.sideFrame&&(this.sideFrame.css({zIndex:0}),
this.sideFrame.front.attr({fill:this.sideFrame.color}));this.bottomFrame&&(this.bottomFrame.css({zIndex:1}),this.bottomFrame.front.attr({fill:this.bottomFrame.color}));this.backFrame&&(this.backFrame.css({zIndex:0}),this.backFrame.front.attr({fill:this.backFrame.color}))})});
