﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uSYST02
    {

        ///Summary
        ///
        ///Summary
        public string LANGUAGE_ID { get; set; }

        ///Summary
        ///
        ///Summary
        public string LANGUAGE_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        public string FUN_DESC { get; set; }

        ///Summary
        ///
        ///Summary
        public string CHG_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        public DateTime CHG_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        public string CRE_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        public DateTime CRE_DATE { get; set; }

        public Int32 ORDER_BY { get; set; }

    }
}
