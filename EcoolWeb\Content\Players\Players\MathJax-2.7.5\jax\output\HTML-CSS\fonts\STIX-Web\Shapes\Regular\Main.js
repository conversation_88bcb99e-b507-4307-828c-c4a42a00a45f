/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Shapes/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Shapes={directory:"Shapes/Regular",family:"STIXMathJax_Shapes",testString:"\u00A0\u2423\u2500\u2502\u2506\u2508\u250A\u251C\u2524\u252C\u2534\u253C\u2550\u2551\u2552",32:[0,0,250,0,0],160:[0,0,250,0,0],9251:[16,120,500,40,460],9472:[340,-267,708,-11,719],9474:[910,303,708,317,390],9478:[910,303,708,317,390],9480:[340,-267,708,-11,719],9482:[910,303,708,317,390],9500:[910,303,708,317,719],9508:[910,303,708,-11,390],9516:[340,303,708,-11,719],9524:[910,-267,708,-11,719],9532:[910,303,708,-11,719],9552:[433,-174,708,-11,719],9553:[910,303,708,225,483],9554:[433,303,708,317,720],9555:[340,303,708,225,720],9556:[433,303,708,225,719],9557:[433,303,708,-11,390],9558:[340,303,708,-11,483],9559:[433,303,708,-11,483],9560:[910,-174,708,317,720],9561:[910,-267,708,225,720],9562:[910,-174,708,225,719],9563:[910,-174,708,-11,390],9564:[910,-267,708,-11,483],9565:[910,-174,708,-11,483],9566:[910,303,708,317,720],9567:[910,303,708,225,720],9568:[910,303,708,225,720],9569:[910,303,708,-11,390],9570:[910,303,708,-11,483],9571:[910,303,708,-11,483],9572:[433,303,708,-11,719],9573:[340,303,708,-11,719],9574:[433,303,708,-11,719],9575:[910,-174,708,-11,719],9576:[910,-267,708,-11,719],9577:[910,-174,708,-11,719],9578:[910,303,708,-11,719],9579:[910,303,708,-11,719],9580:[910,303,708,-11,719],9600:[910,-304,1213,0,1213],9604:[303,303,1213,0,1213],9608:[910,303,1213,0,1213],9612:[910,303,1212,0,606],9616:[910,303,1212,606,1212],9617:[860,258,1200,0,1200],9618:[874,273,1200,0,1200],9619:[874,273,1200,0,1200],9634:[662,158,910,45,865],9635:[662,158,910,45,865],9636:[662,158,910,45,865],9637:[662,158,910,45,865],9638:[662,158,910,45,865],9639:[662,158,910,45,865],9640:[662,158,910,45,865],9641:[662,158,910,45,865],9642:[460,-40,484,32,452],9643:[460,-40,484,32,452],9644:[469,11,1020,38,982],9645:[469,11,1020,38,982],9646:[724,220,560,40,520],9647:[724,220,560,40,520],9648:[514,11,1140,28,1112],9649:[514,11,1140,29,1111],9652:[553,-28,660,27,632],9653:[553,-28,660,27,632],9655:[791,284,1043,70,1008],9658:[555,50,930,65,885],9659:[555,50,930,65,885],9662:[477,48,660,27,632],9663:[477,48,660,27,632],9665:[791,284,1043,70,1008],9668:[555,50,930,45,865],9669:[555,50,930,45,865],9670:[744,242,1064,39,1025],9671:[744,242,1064,39,1025],9672:[744,242,1064,39,1025],9673:[623,119,842,50,792],9675:[623,119,842,50,792],9676:[680,176,910,29,881],9677:[680,176,910,27,884],9678:[623,119,842,50,792],9679:[623,119,842,50,792],9680:[623,119,842,50,792],9681:[623,119,842,50,792],9682:[623,119,842,50,792],9683:[623,119,842,50,792],9684:[623,119,842,50,792],9685:[623,119,842,50,792],9686:[680,176,580,66,494],9687:[680,176,580,86,514],9688:[662,158,910,45,865],9689:[662,158,910,45,865],9690:[662,-252,910,45,865],9691:[252,158,910,45,865],9692:[680,-252,910,27,455],9693:[680,-252,910,455,884],9694:[252,176,910,455,884],9695:[252,176,910,26,455],9696:[680,-251,910,27,884],9697:[252,176,910,27,884],9698:[662,158,911,45,865],9699:[662,158,911,45,865],9700:[662,158,911,45,865],9701:[662,158,911,45,865],9702:[444,-59,523,70,455],9703:[662,157,910,45,865],9704:[662,157,910,45,865],9705:[662,157,910,45,865],9706:[662,157,910,45,865],9707:[662,157,910,45,865],9708:[811,127,1145,35,1110],9709:[811,127,1145,35,1110],9710:[811,127,1145,35,1110],9712:[662,158,910,45,865],9713:[662,158,910,45,865],9714:[662,158,910,45,865],9715:[662,158,910,45,865],9716:[623,119,842,50,792],9717:[623,119,842,50,792],9718:[623,119,842,50,792],9719:[623,119,842,50,792],9720:[662,158,911,45,865],9721:[662,158,911,45,865],9722:[662,158,911,45,865],9723:[580,76,746,45,701],9724:[580,76,746,45,701],9725:[513,12,601,38,563],9726:[514,11,601,38,563],9727:[662,158,911,45,865],9733:[655,66,870,60,810],9734:[655,66,870,60,810],9737:[583,79,762,50,712],9740:[634,131,581,54,553],9742:[676,0,1000,32,967],9746:[662,158,910,45,865],9761:[630,35,619,70,549],9785:[728,82,1150,170,980],9786:[728,82,1150,170,980],9787:[728,82,1150,170,980],9788:[623,122,837,46,791],9789:[728,82,641,40,601],9790:[728,82,641,40,601],9791:[702,198,603,65,538],9792:[638,135,603,65,538],9793:[638,135,603,65,538],9794:[634,131,660,54,620],9795:[732,176,970,66,904],9796:[793,140,970,63,866],9798:[760,110,840,60,780],9799:[730,110,632,76,576],9800:[760,110,964,25,939],9801:[643,139,781,43,738],9828:[609,99,685,34,651],9829:[603,105,685,34,651],9830:[609,105,685,41,643],9831:[603,99,685,34,651],9833:[714,125,390,45,345],9834:[714,125,560,50,510],9835:[842,125,840,40,721],9854:[775,271,1186,70,1116],9856:[669,23,1032,170,862],9857:[669,23,1032,170,862],9858:[669,23,1032,170,862],9859:[669,23,1032,170,862],9860:[669,23,1032,170,862],9861:[669,23,1032,170,862],9862:[687,42,1032,152,881],9863:[687,42,1032,152,881],9864:[687,42,1032,152,881],9865:[687,42,1032,152,881],9888:[1023,155,1510,25,1485],9893:[784,281,660,54,620],9898:[583,79,762,50,712],9899:[583,79,762,50,712],9900:[487,-14,565,46,519],9906:[638,135,603,65,538],9954:[773,80,700,94,606],11026:[662,157,910,45,865],11027:[662,157,910,45,865],11028:[662,157,910,45,865],11029:[662,157,910,45,865],11030:[744,242,1064,39,1025],11031:[744,242,1064,39,1025],11032:[744,242,1064,39,1025],11033:[744,242,1064,39,1025],11034:[662,157,910,45,865],11035:[780,180,1040,40,1000],11036:[780,180,1040,40,1000],11037:[332,-172,240,50,190],11038:[332,-172,240,50,190],11039:[690,105,910,36,874],11040:[690,105,910,36,874],11041:[680,178,910,82,828],11042:[680,178,910,82,828],11043:[633,127,926,24,902],11044:[785,282,1207,70,1137],11045:[581,96,779,45,734],11046:[581,96,779,45,734],11047:[609,105,544,40,504],11048:[609,105,544,40,504],11049:[488,-16,523,26,497],11050:[488,-16,357,26,331],11051:[488,-16,357,26,331],11052:[500,-4,842,50,792],11053:[500,-4,842,50,792],11054:[623,119,596,50,546],11055:[623,119,596,50,546],11056:[448,-57,926,70,856],11057:[739,232,926,60,866],11058:[569,61,1200,52,1147],11059:[449,-58,1574,55,1519],11060:[450,-57,926,56,871],11061:[450,-57,926,55,871],11062:[450,-57,926,55,871],11063:[449,-57,1412,55,1357],11064:[449,-57,926,55,873],11065:[450,-57,926,55,871],11066:[450,-57,926,55,871],11067:[449,-57,926,55,871],11068:[450,-57,926,55,871],11069:[450,-57,926,50,876],11070:[449,-57,926,55,871],11071:[449,-57,926,55,871],11072:[565,-57,926,55,871],11073:[508,-57,926,55,871],11074:[449,141,926,55,871],11075:[532,26,926,45,871],11076:[532,26,926,45,871],11077:[701,195,928,55,873],11078:[701,195,928,55,873],11079:[508,-57,926,55,871],11080:[449,141,926,55,871],11081:[508,-57,926,55,871],11082:[449,141,926,55,871],11083:[449,2,926,55,871],11084:[449,2,926,55,871],11088:[619,30,794,60,734],11089:[619,30,794,60,734],11090:[597,13,700,35,665],11091:[712,126,865,45,840],11092:[712,127,865,45,840],57344:[610,25,1184,829,895],57345:[667,-41,1184,829,1211],57346:[1022,0,1192,30,1162],57497:[567,183,612,25,587],57511:[702,-506,376,30,346],57512:[662,156,496,114,371],57513:[497,-167,647,49,619],57514:[702,-506,376,30,346],57515:[662,156,496,114,371],57516:[702,-506,470,30,440],57517:[662,156,638,35,513],57518:[662,0,423,55,345],57519:[662,0,423,55,345],57520:[735,-531,0,100,400],57521:[-50,254,0,0,300],57522:[-50,254,0,0,300],57559:[583,79,762,50,712],57571:[638,134,842,35,807],57573:[690,189,523,72,450],57574:[690,189,523,72,450],57575:[811,127,772,35,737],57576:[532,26,1077,55,1022],57577:[547,41,685,48,636],57578:[661,158,910,45,865],57579:[567,58,716,45,671],57581:[819,312,511,192,319],57582:[751,156,926,85,841],57583:[547,41,686,49,637],57585:[66,0,390,48,342],57604:[409,-253,100,-64,164],57612:[384,-122,400,69,330],57613:[384,-122,400,69,330],57614:[405,-101,652,193,459],57615:[386,-120,315,0,315],57616:[432,-28,652,124,528],57617:[432,-28,652,124,528],57622:[214,-107,511,223,289],57623:[286,-220,229,61,168],57624:[271,-134,277,70,207],57625:[271,-134,277,70,207],57999:[135,0,325,-1,326],58000:[135,0,633,-1,634],58001:[955,-820,325,-1,326],58311:[662,156,902,0,863],58312:[662,156,902,0,863]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Shapes"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Shapes/Regular/Main.js"]);
