/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Misc-bold-italic"]={directory:"Misc/BoldItalic",family:"STIXMathJax_Misc",weight:"bold",style:"italic",testString:"\u00A0\u0250\u0251\u0252\u0253\u0254\u0255\u0256\u0257\u0258\u0259\u025A\u025B\u025C\u025D",32:[0,0,250,0,0],160:[0,0,250,0,0],592:[473,14,512,13,492],593:[473,14,612,25,592],594:[473,14,612,25,592],595:[691,13,500,-14,449],596:[462,13,444,-5,392],597:[462,157,444,-5,406],598:[699,233,500,-21,517],599:[683,13,570,-21,653],600:[462,13,444,5,421],601:[462,13,444,5,398],602:[462,13,626,5,626],603:[475,14,444,5,482],604:[475,14,480,5,469],605:[475,14,689,5,689],606:[475,14,486,7,475],607:[462,207,367,-100,364],608:[683,245,720,-52,751],609:[472,245,549,-52,520],610:[462,11,561,21,544],611:[462,234,444,20,400],612:[450,10,493,10,488],613:[459,249,556,-13,498],614:[683,9,556,-13,498],615:[683,205,533,-13,475],616:[684,9,278,-10,262],617:[456,8,253,2,237],618:[462,0,304,-32,321],619:[699,9,320,9,368],620:[699,9,445,17,417],621:[699,233,291,-47,290],622:[699,236,623,2,585],623:[462,9,778,-14,723],624:[462,233,778,-14,723],625:[462,233,759,-14,704],626:[462,233,694,-109,632],627:[462,233,505,-6,486],628:[462,12,588,-27,614],629:[462,13,500,-3,441],630:[462,5,749,23,751],631:[477,2,685,-3,626],632:[685,231,691,-3,632],633:[462,0,427,0,410],634:[699,0,493,0,476],635:[462,233,436,0,417],636:[462,233,389,-87,389],637:[462,233,389,-47,389],638:[484,0,360,-21,417],639:[484,0,338,10,292],640:[464,0,498,8,515],641:[464,0,498,8,597],642:[462,218,389,-32,333],643:[683,233,424,-104,584],644:[683,207,394,-90,576],645:[470,233,415,79,344],646:[683,243,521,-40,641],647:[513,90,310,7,299],648:[594,233,311,-60,281],649:[462,9,556,-16,514],650:[452,8,500,15,552],651:[462,10,534,18,492],652:[462,13,444,15,401],653:[462,13,667,15,614],654:[667,0,444,16,502],655:[464,0,633,65,606],656:[449,218,440,-24,405],657:[449,97,411,-24,376],658:[450,236,499,-10,558],659:[450,307,499,-10,528],660:[685,0,530,25,520],661:[685,0,530,65,509],662:[669,14,487,25,453],663:[462,237,479,20,544],664:[680,17,723,13,734],665:[464,0,493,-10,486],666:[475,14,465,16,504],667:[538,11,580,29,690],668:[464,0,582,21,676],669:[685,233,475,-50,463],670:[457,250,500,22,528],671:[464,0,485,10,468],672:[582,205,488,1,674],673:[685,0,530,25,520],674:[685,0,530,65,507],675:[699,13,750,-21,735],676:[699,236,820,-21,813],677:[699,97,817,-21,743],678:[594,13,560,-3,524],679:[683,233,453,-30,670],680:[594,18,600,-3,618],8355:[669,0,668,-13,661],8356:[683,12,500,-32,510],8359:[669,13,1229,-28,1173],8364:[681,17,562,34,546],9312:[690,19,695,0,695],9313:[690,19,695,0,695],9314:[690,19,695,0,695],9315:[690,19,695,0,695],9316:[690,19,695,0,695],9317:[690,19,695,0,695],9318:[690,19,695,0,695],9319:[690,19,695,0,695],9320:[690,19,695,0,695],9398:[690,19,695,0,695],9399:[690,19,695,0,695],9400:[690,19,695,0,695],9401:[690,19,695,0,695],9402:[690,19,695,0,695],9403:[690,19,695,0,695],9404:[690,19,695,0,695],9405:[690,19,695,0,695],9406:[690,19,695,0,695],9407:[690,19,695,0,695],9408:[690,19,695,0,695],9409:[690,19,695,0,695],9410:[690,19,695,0,695],9411:[690,19,695,0,695],9412:[690,19,695,0,695],9413:[690,19,695,0,695],9414:[690,19,695,0,695],9415:[690,19,695,0,695],9417:[690,19,695,0,695],9418:[690,19,695,0,695],9419:[690,19,695,0,695],9420:[690,19,695,0,695],9421:[690,19,695,0,695],9422:[690,19,695,0,695],9423:[690,19,695,0,695],9424:[690,19,695,0,695],9425:[690,19,695,0,695],9426:[690,19,695,0,695],9427:[690,19,695,0,695],9428:[690,19,695,0,695],9429:[690,19,695,0,695],9430:[690,19,695,0,695],9431:[690,19,695,0,695],9432:[690,19,695,0,695],9433:[690,19,695,0,695],9434:[690,19,695,0,695],9435:[690,19,695,0,695],9436:[690,19,695,0,695],9437:[690,19,695,0,695],9438:[690,19,695,0,695],9439:[690,19,695,0,695],9440:[690,19,695,0,695],9441:[690,19,695,0,695],9442:[690,19,695,0,695],9443:[690,19,695,0,695],9444:[690,19,695,0,695],9445:[690,19,695,0,695],9446:[690,19,695,0,695],9447:[690,19,695,0,695],9448:[690,19,695,0,695],9449:[690,19,695,0,695],9450:[690,19,695,0,695]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Misc-bold-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Misc/BoldItalic/Main.js"]);
