﻿using ECOOL_APP;
using ECOOL_APP.EF;
using ECOOL_APP.EF.Inherit;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP.com.ecool.Models.entity;
using EcoolWeb.CustomAttribute;
using System.Web.Mvc.Html;
using System.Data.Entity.Core.Objects;
using System.Data.Entity;
using ECOOL_APP.com.ecool.util;
using System.Data;
using System.IO;
using NPOI.SS.UserModel;
using System.Transactions;
using EcoolWeb.Util;
using ECOOL_APP.com.ecool.LogicCenter;
using Dapper;
using System.Data.Objects.SqlClient;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWA002Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        // GET: AWA002

        public ActionResult BankBook()
        {
            UserProfile user = UserProfileHelper.Get();
            if (user.USER_TYPE == "T" || user.USER_TYPE == "A")
            {

                return RedirectToAction("Query2");

            }
            else {


                return RedirectToAction("Query2", new { WhereIsPassbook = true });
            }
            
        }
        public ActionResult Query4(AWA01labViewModel model)
        {
            int PageSize = 20;
            UserProfile user = UserProfileHelper.Get();
           var AWA01_labnormalogs= db.AWA01_labnormalog.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).OrderBy(x=>x.StartDate);
            model.labnormaloglist = AWA01_labnormalogs.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            return View(model);

        }
        public ActionResult Query5(AWA005QueryViewModel model) {
            int PageSize = 20;
            UserProfile user = UserProfileHelper.Get();
         
            List<HRMT01> hRMT01s = new List<HRMT01>();
            if (model == null) new AWA005QueryViewModel();
            hRMT01s = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_STATUS != UserStaus.Invalid && x.USER_TYPE == UserType.Student).ToList();
            IQueryable<HRMT01QTY> hrt01Item =  from hrt01 in db.HRMT01
            where hrt01.SCHOOL_NO == user.SCHOOL_NO && hrt01.USER_STATUS == UserStaus.Enabled && hrt01.USER_TYPE == UserType.Student
            select new HRMT01QTY
            {
                SCHOOL_NO=hrt01.SCHOOL_NO,
                USER_NO=hrt01.USER_NO,
                SEAT_NO=hrt01.SEAT_NO,
                CLASS_NO=hrt01.CLASS_NO,
                SNAME=hrt01.SNAME,
                GRADE = hrt01.GRADE,

                   NAME = hrt01.NAME
                       
            };

            if (!string.IsNullOrEmpty(model.whereGrade)) {
                int whereGrade = byte.Parse(model.whereGrade);
                hrt01Item = hrt01Item.Where(x => x.GRADE == whereGrade);

            }
            if (!string.IsNullOrEmpty(model.whereCLASS_NO))
            {
                hrt01Item = hrt01Item.Where(x => x.CLASS_NO == model.whereCLASS_NO);

            }
            if (!string.IsNullOrEmpty(model.whereKeyword))
            {
                hrt01Item = hrt01Item.Where(x =>
                x.NAME.Contains(model.whereKeyword.Trim())
                || x.USER_NO.Contains(model.whereKeyword.Trim()));

            }

           // hrt01Item = hrt01Item.OrderBy(x => new { x.CLASS_NO, x.SEAT_NO});
            switch (model.OrdercColumn)
            {

                case "USER_NO":
                    hrt01Item = hrt01Item.OrderByDescending(a => a.USER_NO);
                    break;

                case "SNAME":
                    hrt01Item = hrt01Item.OrderByDescending(a => a.SNAME);
                    break;
                case "CLASS_NO":
                    hrt01Item = hrt01Item.OrderByDescending(a => a.CLASS_NO);
                    break;
                case "SEAT_NO":
                    hrt01Item = hrt01Item.OrderByDescending(a => a.SEAT_NO);
                    break;
                default:
                    hrt01Item = hrt01Item.OrderBy(a => new { a.CLASS_NO, a.SEAT_NO });
                    break;
            }
            HRMT01QTY hRMT01QTY = new HRMT01QTY();
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
        
          var result= hrt01Item.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            if (result.Count() > 0) {
                model.VAWA005List = result;


            }
            return View(model);
        }
        public ActionResult _PageContent(DateTime? StartDate, DateTime? EndDate, AWA002QueryViewModel model)
        {
            if(model==null) model = new AWA002QueryViewModel();
            int PageSize = 20;
            UserProfile user = UserProfileHelper.Get();
            //if (model == null) model = new AWA002Query2ViewModel();

            //if (HtnlHelperService.IsPostBack() == false)
            //{
            //    if (!model.WhereIsPassbook)
            //    {
            //        model.whereLOG_TIME_S = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
            //        model.whereLOG_TIME_E = DateTime.Now.ToString("yyyy-MM-dd");
            //    }

            if (model.whereLOG_TIME_S != null && model.whereLOG_TIME_E != null)
            {
                StartDate= DateTime.Parse(model.whereLOG_TIME_S);
                EndDate = DateTime.Parse(model.whereLOG_TIME_E);
            }
                if (StartDate!=null && EndDate!=null)
            {
                model.whereLOG_TIME_S = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
                model.whereLOG_TIME_E = DateTime.Now.ToString("yyyy-MM-dd");
            }
                   
           
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (user.USER_TYPE == UserType.Student)
            {
                ViewBag.Title = "酷幣給點紀錄-我的各類酷幣給點統計一覽表";
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                ViewBag.Title = "酷幣給點紀錄-寶貝各類酷幣給點統計一覽表";
            }
            else
            {
                ViewBag.Title = "酷幣給點紀錄-學生各類酷幣給點統計一覽表";
            }
            // -- 檢查點數增加異常: 各校管理者以上權限才可看到 --
            string[] exclude_DESC = new string[] { "取消獎品兌換", "定存", "銀行定存到期解約", "銀行定存解約，本金", "銀行定存" }; // 排除的細項
            DateTime? todayDate = null;
            DateTime? todayEnd = null;
            //if (Int32.Parse(user.RoleID_Default) == (int)UserRoles.各校管理者 || (Int32.Parse(user.RoleID_Default) < (int)UserRoles.各校管理者 && user.ROLE_TYPE != 3))
            if (StartDate != null && EndDate != null)
            {                                                                                         //{
                    todayDate =( (DateTime)(StartDate)).Date;
                   todayEnd =( (DateTime)EndDate).Date;
            }
            string CYear = DateTime.Now.AddYears(-1911).ToString("yyy");
            List<string> UserNOlist = new List<string>();
            if (StartDate != null && EndDate != null)
            {

                var alertWhos = db.AWAT01_LOG
                .Join(db.HRMT01, a => new { a.SCHOOL_NO, a.USER_NO }, h => new { h.SCHOOL_NO, h.USER_NO },
                (a, h) => new { a.SCHOOL_NO, a.USER_NO, a.LOG_TIME, a.CASH_IN, h.CLASS_NO, h.SEAT_NO, a.LOG_DESC, a.SOURCE_TYPE })
                .Where(l => l.SCHOOL_NO == user.SCHOOL_NO && l.LOG_TIME >= todayDate && l.LOG_TIME <= todayEnd && l.CASH_IN > 0
                && !exclude_DESC.Contains(l.LOG_DESC) && l.SOURCE_TYPE != "AWAT10")
                .GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.CASH_IN })
                .Select(g => new
                {
                    g.FirstOrDefault().USER_NO,
                    g.FirstOrDefault().CLASS_NO,
                    g.FirstOrDefault().SEAT_NO,
                    g.FirstOrDefault().SCHOOL_NO,


                    CachAddToday = g.Sum(a => a.CASH_IN)
                })
                .Where(s => s.CachAddToday > 500).ToList();
                UserNOlist = alertWhos.Select(w => w.USER_NO).Distinct().ToList();
            }
            else {


                var alertWhos = db.AWAT01_LOG
                .Join(db.HRMT01, a => new { a.SCHOOL_NO, a.USER_NO }, h => new { h.SCHOOL_NO, h.USER_NO },
                (a, h) => new { a.SCHOOL_NO, a.USER_NO, a.LOG_TIME, a.CASH_IN, h.CLASS_NO, h.SEAT_NO, a.LOG_DESC, a.SOURCE_TYPE })
                .Where(l => l.SCHOOL_NO == user.SCHOOL_NO  && l.CASH_IN > 0
                && !exclude_DESC.Contains(l.LOG_DESC) && l.SOURCE_TYPE != "AWAT10")
                .GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.CASH_IN })
                .Select(g => new
                {
                    g.FirstOrDefault().USER_NO,
                    g.FirstOrDefault().CLASS_NO,
                    g.FirstOrDefault().SEAT_NO,
                    g.FirstOrDefault().SCHOOL_NO,


                    CachAddToday = g.Sum(a => a.CASH_IN)
                })
                .Where(s => s.CachAddToday > 500).ToList();

                UserNOlist = alertWhos.Select(w => w.USER_NO).Distinct().ToList();

            }

         
            if (model.whereKeyword != null)

            {
                UserNOlist = UserNOlist.Where(x => x == model.whereKeyword).ToList();
            }

            if (StartDate != null && EndDate != null)
            {
                List<HRMT02QTY> alertWhos = db.AWAT01_LOG
              .Join(db.HRMT01, a => new { a.SCHOOL_NO, a.USER_NO }, h => new { h.SCHOOL_NO, h.USER_NO },
              (a, h) => new { a.SCHOOL_NO, a.USER_NO, a.LOG_TIME, a.CASH_IN, h.CLASS_NO, h.SEAT_NO, a.LOG_DESC, a.SOURCE_TYPE })
              .Where(l => l.SCHOOL_NO == user.SCHOOL_NO && l.CASH_IN > 0
              && !exclude_DESC.Contains(l.LOG_DESC) && l.SOURCE_TYPE != "AWAT10")
              .GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.LOG_TIME })
              .Select(g => new HRMT02QTY
              {
                  USER_NO = g.FirstOrDefault().USER_NO,
                  CLASS_NO = g.FirstOrDefault().CLASS_NO,
                  SEAT_NO = g.FirstOrDefault().SEAT_NO,
                  SCHOOL_NO = g.FirstOrDefault().SCHOOL_NO,
                  LOG_TIME = g.FirstOrDefault().LOG_TIME,
                    //  Log_TIMEDATE = g.FirstOrDefault().LOG_TIME.ToShortDateString(),

                    CachAddToday = g.Sum(a => a.CASH_IN)
              })
              .Where(s => s.CachAddToday > 500).ToList();
                foreach (var ALEt in alertWhos)
                {
                    ALEt.Log_TIMEDATE = ALEt.LOG_TIME.Value.ToShortDateString();
                }
                List<HRMT02QTY> alertWhos1 = alertWhos.Where(l => l.LOG_TIME >= StartDate && l.LOG_TIME<= EndDate)
                    .GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.Log_TIMEDATE })
                
                    .Select(g => new HRMT02QTY
                    {
                        USER_NO = g.FirstOrDefault().USER_NO,
                        CLASS_NO = g.FirstOrDefault().CLASS_NO,
                        SEAT_NO = g.FirstOrDefault().SEAT_NO,
                        SCHOOL_NO = g.FirstOrDefault().SCHOOL_NO,
                        Log_TIMEDATE = g.FirstOrDefault().Log_TIMEDATE,
                        LOG_TIME = g.FirstOrDefault().LOG_TIME,
                        CachAddToday = g.Sum(x => x.CachAddToday)
                    }).Where(s => s.CachAddToday > 500).ToList();
                var SumCool = from aw01 in db.AWAT01_LOG
                              join hr01 in db.HRMT01
                                    on new { aw01.SCHOOL_NO, aw01.USER_NO }
                                equals new { hr01.SCHOOL_NO, hr01.USER_NO }

                              join c in db.AWAT01_X_REF on new { aw01.SOURCE_TYPE } equals new
                              { c.SOURCE_TYPE } into g

                              from x in g.DefaultIfEmpty()
                              where aw01.SCHOOL_NO == user.SCHOOL_NO && aw01.CASH_IN > 0 && !exclude_DESC.Contains(aw01.LOG_DESC) && aw01.SOURCE_TYPE != "AWAT10" && UserNOlist.Contains(aw01.USER_NO)
                              group new
                              {
                                  hr01,
                                  aw01,

                                  //x.SOURCE_TYPE,

                                  x.CHART_DESC
                              } by new
                              {
                                  hr01.SYEAR,
                                  hr01.SNAME,
                                  hr01.SCHOOL_NO,
                                  hr01.USER_NO,
                                  hr01.CLASS_NO,
                                  hr01.SEAT_NO,
                                  LOG_DESC = aw01.LOG_DESC,
                                  LogTime = aw01.LOG_TIME
                                  // x.SOURCE_TYPE
                              } into g

                              orderby
                                g.Key.CLASS_NO, g.Key.SEAT_NO
                              select new HRMT02QTY
                              {
                                  SCHOOL_NO = user.SCHOOL_NO,
                                  CYEAR = CYear,
                                  SYEAR = g.Key.SYEAR,
                                  CLASS_NO = g.Key.CLASS_NO,
                                  SEAT_NO = g.Key.SEAT_NO,
                                  NAME = g.Key.SNAME,
                                  USER_NO = g.Key.USER_NO,
                                  LOG_DESC = g.Key.LOG_DESC,
                                  LOG_TIME = g.Key.LogTime,
                                  // SOURCE_TYPE = g.Key.SOURCE_TYPE,
                                  SumCASH_IN = (System.Int16?)g.Sum(p => p.aw01.CASH_IN),
                                  SEMESTER = (DateTime.Now.Month > 1 && DateTime.Now.Month < 8) ? 2 : 1
                              };
                var Demand_Deposit = 0;
                var Time_Deposit = db.AWAT10.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO
                                                  && a.STATUS == AWAT10.StatusVal.SetUp).Select(a => a.AMT).Sum() ?? 0;

                HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();

                if (H01?.USER_TYPE == UserType.Student)
                {
                    Demand_Deposit = db.AWAT01.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
                }
                else
                {
                    Demand_Deposit = db.AWAT08.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
                }
                List<HRMT02QTY> HRMT02QTYItem = new List<HRMT02QTY>();
                HRMT02QTYItem = alertWhos1;
                ViewBag.SumCool = SumCool.ToList();
                ViewBag.Demand = Demand_Deposit;
                ViewBag.Time_Deposit = Time_Deposit;
                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

                ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model.whereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
                model.VAWA002List = HRMT02QTYItem.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            }
            else {
                List<HRMT02QTY> alertWhos = db.AWAT01_LOG
                .Join(db.HRMT01, a => new { a.SCHOOL_NO, a.USER_NO }, h => new { h.SCHOOL_NO, h.USER_NO },
                (a, h) => new { a.SCHOOL_NO, a.USER_NO, a.LOG_TIME, a.CASH_IN, h.CLASS_NO, h.SEAT_NO, a.LOG_DESC, a.SOURCE_TYPE })
                .Where(l => l.SCHOOL_NO == user.SCHOOL_NO && l.CASH_IN > 0
                && !exclude_DESC.Contains(l.LOG_DESC) && l.SOURCE_TYPE != "AWAT10")
                .GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.LOG_TIME })
                .Select(g => new HRMT02QTY 
                {
                    USER_NO=  g.FirstOrDefault().USER_NO,
                    CLASS_NO= g.FirstOrDefault().CLASS_NO,
                    SEAT_NO=  g.FirstOrDefault().SEAT_NO,
                    SCHOOL_NO= g.FirstOrDefault().SCHOOL_NO,
                    LOG_TIME=  g.FirstOrDefault().LOG_TIME,
                  //  Log_TIMEDATE = g.FirstOrDefault().LOG_TIME.ToShortDateString(),
             
                    CachAddToday = g.Sum(a => a.CASH_IN)
                })
                .Where(s => s.CachAddToday > 500).ToList();
                foreach (var ALEt in alertWhos) {
                    ALEt.Log_TIMEDATE = ALEt.LOG_TIME.Value.ToShortDateString();
                }
                List<HRMT02QTY> alertWhos1 = alertWhos.GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.Log_TIMEDATE })
                    .Select(g => new HRMT02QTY
                {
                    USER_NO = g.FirstOrDefault().USER_NO,
                    CLASS_NO = g.FirstOrDefault().CLASS_NO,
                    SEAT_NO = g.FirstOrDefault().SEAT_NO,
                    SCHOOL_NO = g.FirstOrDefault().SCHOOL_NO,
                    Log_TIMEDATE = g.FirstOrDefault().Log_TIMEDATE,
                        LOG_TIME = g.FirstOrDefault().LOG_TIME,
                        CachAddToday = g.Sum(x=>x.CachAddToday)
                }).Where(s => s.CachAddToday > 500).ToList();
                var SumCool = from aw01 in db.AWAT01_LOG
                              join hr01 in db.HRMT01
                                    on new { aw01.SCHOOL_NO, aw01.USER_NO }
                                equals new { hr01.SCHOOL_NO, hr01.USER_NO }
                           
                              join c in db.AWAT01_X_REF on new { aw01.SOURCE_TYPE } equals new
                              { c.SOURCE_TYPE } into g
                              
                              from x in g.DefaultIfEmpty()
                              where aw01.SCHOOL_NO == user.SCHOOL_NO  && aw01.CASH_IN > 0 && !exclude_DESC.Contains(aw01.LOG_DESC) && aw01.SOURCE_TYPE != "AWAT10" && UserNOlist.Contains(aw01.USER_NO)
                              group new
                              {
                                  hr01,
                                  aw01,

                                  //x.SOURCE_TYPE,

                                  x.CHART_DESC
                              } by new
                              {
                                  hr01.SYEAR,
                                  hr01.SNAME,
                                  hr01.SCHOOL_NO,
                                  hr01.USER_NO,
                                  hr01.CLASS_NO,
                                  hr01.SEAT_NO,
                                  LOG_DESC = aw01.LOG_DESC,
                                  LogTime = aw01.LOG_TIME
                                  // x.SOURCE_TYPE
                              } into g

                              orderby
                                g.Key.CLASS_NO, g.Key.SEAT_NO
                              select new HRMT02QTY
                              {
                                  SCHOOL_NO = user.SCHOOL_NO,
                                  CYEAR = CYear,
                                  SYEAR = g.Key.SYEAR,
                                  CLASS_NO = g.Key.CLASS_NO,
                                  SEAT_NO = g.Key.SEAT_NO,
                                  NAME = g.Key.SNAME,
                                  USER_NO = g.Key.USER_NO,
                                  LOG_DESC = g.Key.LOG_DESC,
                                  LOG_TIME = g.Key.LogTime,
                                  // SOURCE_TYPE = g.Key.SOURCE_TYPE,
                                  SumCASH_IN = (System.Int16?)g.Sum(p => p.aw01.CASH_IN),
                                  SEMESTER = (DateTime.Now.Month > 1 && DateTime.Now.Month < 8) ? 2 : 1
                              };
        
                List <HRMT02QTY> HRMT02QTYItem = new List<HRMT02QTY>();
                List<HRMT02QTY> HRMT02QTYItem2 = new List<HRMT02QTY>();
                HRMT02QTYItem = alertWhos1;
                //var temp = (from x in UserNOlist

                //            join old in alertWhos
                //            on x equals old.USER_NO

                //            select new
                //            { USER_NO = x, LOG_TIME = old.LOG_TIME }
                //           );
                //foreach (var TEMPItem in temp) {
                //         HRMT02QTY hRMT02QTY = new HRMT02QTY();

                //      hRMT02QTY = SumCool.Where(y => y.USER_NO == TEMPItem.USER_NO && y.LOG_TIME == TEMPItem.LOG_TIME).FirstOrDefault();
                //            HRMT02QTYItem.Add(hRMT02QTY);


                //}

                HRMT02QTYItem = HRMT02QTYItem.OrderByDescending(X => X.LOG_TIME).ToList();
                // SumCool = HRMT02QTYItem;
              var Demand_Deposit = 0;
                var Time_Deposit = db.AWAT10.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO
                                                  && a.STATUS == AWAT10.StatusVal.SetUp).Select(a => a.AMT).Sum() ?? 0;

                HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();

                if (H01?.USER_TYPE == UserType.Student)
                {
                    Demand_Deposit = db.AWAT01.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
                }
                else
                {
                    Demand_Deposit = db.AWAT08.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
                }
                ViewBag.Demand = Demand_Deposit;
                ViewBag.Time_Deposit = Time_Deposit;
                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
                ViewBag.SumCool = SumCool.ToList();
                ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model.whereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
                model.VAWA002List = HRMT02QTYItem.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);


            }
            //}

            return View(model);
        }
        public ActionResult Query3(AWA002QueryViewModel model)
        {
            int PageSize = 20;
            UserProfile user = UserProfileHelper.Get();
            //if (model == null) model = new AWA002Query2ViewModel();

            //if (HtnlHelperService.IsPostBack() == false)
            //{
            //    if (!model.WhereIsPassbook)
            //    {
            //        model.whereLOG_TIME_S = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
            //        model.whereLOG_TIME_E = DateTime.Now.ToString("yyyy-MM-dd");
            //    }
            //}
             model.whereLOG_TIME_S = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
             model.whereLOG_TIME_E = DateTime.Now.ToString("yyyy-MM-dd");
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (user.USER_TYPE == UserType.Student)
            {
                ViewBag.Title = "酷幣給點紀錄-我的各類酷幣給點統計一覽表";
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                ViewBag.Title = "酷幣給點紀錄-寶貝各類酷幣給點統計一覽表";
            }
            else
            {
                ViewBag.Title = "酷幣給點紀錄-學生各類酷幣給點統計一覽表";
            }
            // -- 檢查點數增加異常: 各校管理者以上權限才可看到 --
            string[] exclude_DESC = new string[] { "取消獎品兌換", "定存", "銀行定存到期解約", "銀行定存解約，本金", "銀行定存" }; // 排除的細項
            //if (Int32.Parse(user.RoleID_Default) == (int)UserRoles.各校管理者 || (Int32.Parse(user.RoleID_Default) < (int)UserRoles.各校管理者 && user.ROLE_TYPE != 3))
            //{
                DateTime todayDate = DateTime.Now.Date;
                DateTime todayEnd = DateTime.Now.Date.AddDays(1);
            string CYear = DateTime.Now.AddYears(-1911).ToString("yyy");
     

            var alertWhos = db.AWAT01_LOG
                .Join(db.HRMT01, a => new { a.SCHOOL_NO, a.USER_NO }, h => new { h.SCHOOL_NO, h.USER_NO },
                (a, h) => new { a.SCHOOL_NO, a.USER_NO, a.LOG_TIME, a.CASH_IN, h.CLASS_NO, h.SEAT_NO, a.LOG_DESC, a.SOURCE_TYPE })
                .Where(l => l.SCHOOL_NO == user.SCHOOL_NO && l.LOG_TIME >= todayDate && l.LOG_TIME <= todayEnd && l.CASH_IN > 0
                && !exclude_DESC.Contains(l.LOG_DESC) && l.SOURCE_TYPE != "AWAT10")
                .GroupBy(l => new { l.SCHOOL_NO, l.USER_NO, l.CASH_IN })
                .Select(g => new
                {
                    g.FirstOrDefault().USER_NO,
                    g.FirstOrDefault().CLASS_NO,
                    g.FirstOrDefault().SEAT_NO,
                    g.FirstOrDefault().SCHOOL_NO,


                    CachAddToday = g.Sum(a => a.CASH_IN)
                })
                .Where(s => s.CachAddToday > 500).ToList();
            List<string> UserNOlist = new List<string>();
            UserNOlist = alertWhos.Select(w =>  w.USER_NO).Distinct().ToList();
            if (model.whereKeyword != null)

            {
                UserNOlist = UserNOlist.Where(x => x == model.whereKeyword).ToList();
            }
            var SumCool = from aw01 in db.AWAT01_LOG
                          join hr01 in db.HRMT01
                                on new { aw01.SCHOOL_NO, aw01.USER_NO }
                            equals new { hr01.SCHOOL_NO, hr01.USER_NO }
                          join c in db.AWAT01_X_REF on new { aw01.SOURCE_TYPE } equals new
                          { c.SOURCE_TYPE } into g
                          from x in g.DefaultIfEmpty()
                          where aw01.SCHOOL_NO == user.SCHOOL_NO && aw01.LOG_TIME >= todayDate && aw01.LOG_TIME <= todayEnd && aw01.CASH_IN > 0 && !exclude_DESC.Contains(aw01.LOG_DESC) && aw01.SOURCE_TYPE != "AWAT10" && UserNOlist.Contains(aw01.USER_NO)
                          group new
                          {
                              hr01,
                              aw01,

                              //x.SOURCE_TYPE,

                              x.CHART_DESC
                          } by new
                          {
                              hr01.SYEAR,
                              hr01.SNAME,
                              hr01.SCHOOL_NO,
                              hr01.USER_NO,
                              hr01.CLASS_NO,
                              hr01.SEAT_NO,
                              LOG_DESC = aw01.LOG_DESC,
                       
                              LogTime = aw01.LOG_TIME
                              // x.SOURCE_TYPE
                          } into g

                          orderby
                            g.Key.CLASS_NO, g.Key.SEAT_NO
                          select new HRMT02QTY
                          {
                              SCHOOL_NO = user.SCHOOL_NO,
                              CYEAR = CYear,
                              SYEAR = g.Key.SYEAR,
                              CLASS_NO = g.Key.CLASS_NO,
                              SEAT_NO = g.Key.SEAT_NO,
                              NAME = g.Key.SNAME,
                              USER_NO = g.Key.USER_NO,
                              LOG_DESC = g.Key.LOG_DESC,
                              LOG_TIME = g.Key.LogTime,
                              // SOURCE_TYPE = g.Key.SOURCE_TYPE,
                              SumCASH_IN = (System.Int16?)g.Sum(p => p.aw01.CASH_IN),
                              SEMESTER = (DateTime.Now.Month > 1 && DateTime.Now.Month < 8) ? 2 : 1
                          };
        

              
            //}
            var Demand_Deposit = 0;
            var Time_Deposit = db.AWAT10.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO
                                              && a.STATUS == AWAT10.StatusVal.SetUp).Select(a => a.AMT).Sum() ?? 0;

            HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();

            if (H01?.USER_TYPE == UserType.Student)
            {
                Demand_Deposit = db.AWAT01.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
            }
            else
            {
                Demand_Deposit = db.AWAT08.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
            }
            ViewBag.Demand = Demand_Deposit;
            ViewBag.Time_Deposit = Time_Deposit;
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            ViewBag.SumCool= SumCool.ToList();
            ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
            model.VAWA002List = SumCool.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            return View(model);
        }
        public ActionResult Query(AWA002QueryViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (user.USER_TYPE == "T" || user.USER_TYPE =="A")
            {
                if (!string.IsNullOrEmpty(model.whereLOG_TIME_S) && !string.IsNullOrEmpty(model.whereLOG_TIME_E))
                {
          
                }
                else
                {

                    model.whereLOG_TIME_S = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
                    model.whereLOG_TIME_E = DateTime.Now.ToString("yyyy-MM-dd");
                }
            }
            model = QuerySouData(model, user);

            return View("",model);
        }

        private AWA002QueryViewModel QuerySouData(AWA002QueryViewModel model, UserProfile user)
        {
            if (model == null) model = new AWA002QueryViewModel();
            int PageSize = 20;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            string CYear = DateTime.Now.AddYears(-1911).ToString("yyy");

            string qUserNo = Request["User_No"];

            if (user.USER_TYPE == UserType.Student)
            {
                ViewBag.Title = "酷幣給點紀錄-我的各類酷幣給點統計一覽表";
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                ViewBag.Title = "酷幣給點紀錄-寶貝各類酷幣給點統計一覽表";
            }
            else
            {
                ViewBag.Title = "酷幣給點紀錄-學生各類酷幣給點統計一覽表";
            }

            var SumCool = from aw01 in db.AWAT01_LOG
                          join hr01 in db.HRMT01
                                on new { aw01.SCHOOL_NO, aw01.USER_NO }
                            equals new { hr01.SCHOOL_NO, hr01.USER_NO }
                          join c in db.AWAT01_X_REF on new { aw01.SOURCE_TYPE } equals new
                          { c.SOURCE_TYPE } into g
                          from x in g.DefaultIfEmpty()
                          where aw01.SCHOOL_NO == SchoolNO
                          group new
                          {
                              hr01,
                              aw01,

                              //x.SOURCE_TYPE,

                              x.CHART_DESC
                          } by new
                          {
                              hr01.SYEAR,
                              hr01.SNAME,
                              hr01.SCHOOL_NO,
                              hr01.USER_NO,
                              hr01.CLASS_NO,
                              hr01.SEAT_NO,
                              LOG_DESC = x.CHART_DESC ?? aw01.LOG_DESC,
                              // x.SOURCE_TYPE
                          } into g

                          orderby
                            g.Key.CLASS_NO, g.Key.SEAT_NO
                          select new HRMT02QTY
                          {
                              SCHOOL_NO = SchoolNO,
                              CYEAR = CYear,
                              SYEAR = g.Key.SYEAR,
                              CLASS_NO = g.Key.CLASS_NO,
                              SEAT_NO = g.Key.SEAT_NO,
                              NAME = g.Key.SNAME,
                              USER_NO = g.Key.USER_NO,
                              LOG_DESC = g.Key.LOG_DESC,
                              // SOURCE_TYPE = g.Key.SOURCE_TYPE,
                              SumCASH_IN = (System.Int16?)g.Sum(p => p.aw01.CASH_IN),
                              SEMESTER = (DateTime.Now.Month > 1 && DateTime.Now.Month < 8) ? 2 : 1
                          };

            if (string.IsNullOrWhiteSpace(model.whereLOG_TIME_S) == false && string.IsNullOrWhiteSpace(model.whereLOG_TIME_E) == false)
            {
                DateTime LOG_TIME_S;
                DateTime LOG_TIME_E;
                DateTime.TryParse(model.whereLOG_TIME_S, out LOG_TIME_S);
                DateTime.TryParse(model.whereLOG_TIME_E, out LOG_TIME_E);

                SumCool = from aw01 in db.AWAT01_LOG

                          join hr01 in db.HRMT01
                                on new { aw01.SCHOOL_NO, aw01.USER_NO }
                            equals new { hr01.SCHOOL_NO, hr01.USER_NO }
                          join c in db.AWAT01_X_REF on new { aw01.SOURCE_TYPE } equals new
                          { c.SOURCE_TYPE } into g
                          from x in g.DefaultIfEmpty()
                          where aw01.SCHOOL_NO == SchoolNO && aw01.LOG_TIME >= LOG_TIME_S && aw01.LOG_TIME <= LOG_TIME_E
                          group new { hr01, aw01, x.SOURCE_TYPE, x.CHART_DESC } by new
                          {
                              hr01.SYEAR,
                              hr01.SNAME,
                              hr01.SCHOOL_NO,
                              hr01.USER_NO,
                              hr01.CLASS_NO,
                              hr01.SEAT_NO,
                              LOG_DESC = x.CHART_DESC ?? aw01.LOG_DESC,
                              aw01.SOURCE_TYPE
                          } into g

                          orderby
                            g.Key.CLASS_NO, g.Key.SEAT_NO
                          select new HRMT02QTY
                          {
                              SCHOOL_NO = SchoolNO,
                              CYEAR = CYear,
                              SYEAR = g.Key.SYEAR,
                              CLASS_NO = g.Key.CLASS_NO,
                              SEAT_NO = g.Key.SEAT_NO,
                              NAME = g.Key.SNAME,
                              USER_NO = g.Key.USER_NO,
                              LOG_DESC = g.Key.LOG_DESC,
                              SOURCE_TYPE = g.Key.SOURCE_TYPE,
                              SumCASH_IN = (System.Int16?)g.Sum(p => p.aw01.CASH_IN),
                              SEMESTER = (DateTime.Now.Month > 1 && DateTime.Now.Month < 8) ? 2 : 1
                          };
            }
            var Demand_Deposit = 0;
            var Time_Deposit = db.AWAT10.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO
                                              && a.STATUS == AWAT10.StatusVal.SetUp).Select(a => a.AMT).Sum() ?? 0;

            HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();

            if (H01?.USER_TYPE == UserType.Student)
            {
                Demand_Deposit = db.AWAT01.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
            }
            else
            {
                Demand_Deposit = db.AWAT08.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
            }
            if (user != null)
            {
                if (user.USER_TYPE == UserType.Student)
                {
                    SumCool = SumCool.Where(a => a.USER_NO == user.USER_NO);
                }
                else if (user.USER_TYPE == UserType.Parents)
                {
                    var arrUSER_NO = HRMT06.GetArrMyPanyStudent(user);
                    SumCool = SumCool.Where(a => arrUSER_NO.Contains(a.USER_NO.ToString()) && a.USER_NO != null);
                }
                else if (UserProfileHelper.CheckROLE_SCHOOL_ADMIN(user, db) && qUserNo != null && qUserNo != string.Empty)
                {
                    SumCool = SumCool.Where(a => a.USER_NO == qUserNo);
                }
            }
            else
            {
                SumCool = SumCool.Where(a => a.USER_NO == null);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                SumCool = SumCool.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()) || a.CLASS_NO.Contains(model.whereKeyword.Trim()) || a.LOG_DESC.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                SumCool = SumCool.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                SumCool = SumCool.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            if (model.whereIsExcel)
            {
                model.VAWA002List = SumCool.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, int.MaxValue);
            }
            else
            {
                model.VAWA002List = SumCool.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            }
            ViewBag.Demand = Demand_Deposit;
            ViewBag.Time_Deposit = Time_Deposit;
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            return model;
        }
        public ActionResult toGOQuery2Teacher(AWA003QueryViewModel modelW)
        {
            AWA002Query2ViewModel model1 = new AWA002Query2ViewModel();
            string USER_NO = "";
            if (modelW.whereSTART_CRE_DATE != null)
            {

                model1.whereLOG_TIME_S = modelW.whereSTART_CRE_DATE.ToString();

            }
            if (modelW.whereEND_CRE_DATE != null)
            {
                model1.whereLOG_TIME_E = modelW.whereEND_CRE_DATE.ToString();
            }
            if (modelW.whereUserNo != null)
            {
                model1.whereUserNo= modelW.whereUserNo;

            }
            if (modelW.whereSOURCETABLE != null)
            {

                model1.whereSOURCETABLE = modelW.whereSOURCETABLE;
            }
                Query2SouDataTaecher(modelW.whereUserNo, "", model1);
            model1.whereFromType = "T";
            return View("Query2", model1);
        }
      public ActionResult toGOQuery2(AWA003QueryViewModel modelW) {

            AWA002Query2ViewModel model1 = new AWA002Query2ViewModel();
            string USER_NO = "";
            if (modelW.whereSTART_CRE_DATE != null) {

                model1.whereLOG_TIME_S = modelW.whereSTART_CRE_DATE.ToString();

            }
            if (modelW.whereEND_CRE_DATE != null)
            {
                model1.whereLOG_TIME_E = modelW.whereEND_CRE_DATE.ToString();
            }
            if (modelW.whereUserNo != null) {
                model1.whereKeyword = modelW.whereUserNo;

            }
            if (modelW.whereSOURCETABLE != null)
            {
                model1.whereArrCHART_DESC= new string[] { modelW.whereSOURCETABLE };

            }
           
            Query2SouData(modelW.whereUserNo, "", model1);
            return View("Query2", model1);

        }
        public ActionResult Query2(string User_No, string LogDESC, AWA002Query2ViewModel model)
        {
            if (model == null) model = new AWA002Query2ViewModel();

            if (HtnlHelperService.IsPostBack() == false)
            {
                if (!model.WhereIsPassbook&& model.whereFromType != "T")
                {
                    model.whereLOG_TIME_S = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
                    model.whereLOG_TIME_E = DateTime.Now.ToString("yyyy-MM-dd");
                }
            }
            if (!string.IsNullOrEmpty(model.whereUserNo)) {
                User_No = model.whereUserNo;

            }
                if (model.whereFromType == "T") {
                if (!string.IsNullOrEmpty(model.whereLOG_TIME_S) && !string.IsNullOrEmpty(model.whereLOG_TIME_E))
                {
                    if ((model.whereSOURCETABLE != null && (model.whereSOURCETABLE == "ADI07" || model.whereSOURCETABLE == "Extend")) || model.whereUserNo != null)
                    {


                        Query2SouDataTaecher(User_No, LogDESC, model);
                    }
                    else
                    {

                        Query2SouDataTaecher(model.whereUserNo, "", model);

                    }
                 
                }
                else {

                    model.whereLOG_TIME_S = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
                    model.whereLOG_TIME_E = DateTime.Now.ToString("yyyy-MM-dd");
                }
            }
            else { 
            Query2SouData(User_No, LogDESC, model);
            }
            if (!string.IsNullOrEmpty(User_No)) {
                model.whereUserNo = User_No;

            }
            return View(model);
        }
        public ActionResult AWA002_HISTeacher(AWA003QueryViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            bool AppMode = string.IsNullOrEmpty(EcoolWeb.Models.UserProfileHelper.GetUUID()) == false;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (model.whereSTART_CRE_DATE == null || model.whereEND_CRE_DATE == null)
            {
                model.whereSTART_CRE_DATE = DateTime.Parse(DateTime.Now.AddMonths(-2).ToString("yyyy/MM/dd 00:00"));
                model.whereEND_CRE_DATE = DateTime.Parse(DateTime.Now.Date.ToString("yyyy/MM/dd 23:59"));

            }

            int PageSize = (AppMode) ? 12 : 20;
            if (model == null) model = new AWA003QueryViewModel();
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            var ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
              .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereCLASS_NO });
            ViewBag.ClassItems = ClassItems;
            List<HRMT01QTY> hRMT01QTies = null;
            hRMT01QTies = QeuryAWA002Taecher(model);
            if (hRMT01QTies.ToList().Count() > 0)
            {

                model.VAWA003List = hRMT01QTies.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            }
            else
            {

                model.VAWA003List = null;
            }
            return View(model);
        }
            public ActionResult AWA002_HIS(AWA003QueryViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            bool AppMode = string.IsNullOrEmpty(EcoolWeb.Models.UserProfileHelper.GetUUID()) == false;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (model.whereSTART_CRE_DATE == null || model.whereEND_CRE_DATE == null) {

                model.whereSTART_CRE_DATE = DateTime.Parse(DateTime.Now.AddMonths(-6).ToString("yyyy/MM/dd 00:00"));
                model.whereEND_CRE_DATE = DateTime.Parse(DateTime.Now.Date.ToString("yyyy/MM/dd 23:59"));

            }
            int PageSize = (AppMode) ? 12 : 20;
            if (model == null) model = new AWA003QueryViewModel();
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            var ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
              .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereCLASS_NO });
            ViewBag.ClassItems = ClassItems;
            IQueryable<HRMT01QTY> hRMT01QTies;
            hRMT01QTies = QeuryAWA002(model);
            var notNullResources2 = 0;
            try {
                notNullResources2 = hRMT01QTies.AsEnumerable().Count();
            } catch (Exception e) {

           
            }
        
            if (notNullResources2 >0) {
            if (hRMT01QTies!=null&&  hRMT01QTies.ToList().Count() > 0)
            {

                model.VAWA003List = hRMT01QTies.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

                }
            }
            else {

                model.VAWA003List = null;
            }
            return View(model);
        }
        public ActionResult PrintQuery2(string User_No, string LogDESC, AWA002Query2ViewModel model)
        {
            model.whereIsPrint = true;
            Query2SouData(User_No, LogDESC, model);
            return View(model);
        }
        public List<HRMT01QTY> QeuryAWA002Taecher(AWA003QueryViewModel model)
        {
            List<HRMT01QTY> RankCash;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
           

            if (model.whereSTART_CRE_DATE != null && model.whereEND_CRE_DATE != null)
            {
                List<SUMALLPOINT> SUMALLPOINTExtend = new List<SUMALLPOINT>();
                List<SUMALLPOINT> SUMALLPOINTADDI06 = new List<SUMALLPOINT>();
              
                SUMALLPOINTExtend = db.SUMALLPOINT.Where(x => (x.TYPE == "Extend" || x.TYPE == "ADI06")
                && x.SCHOOL_NO == SchoolNO && x.LOG_TIME >= model.whereSTART_CRE_DATE && x.LOG_TIME <= model.whereEND_CRE_DATE && x.SNAME != null).ToList();
                var temp1 = SUMALLPOINTExtend.GroupBy(l => new { l.USER_NO}).Select(x => new HRMT01QTYTemp2
                {
                    Extend = x.Where(c => c.TYPE == "Extend").Sum(c => c.CASH_IN),
                    USER_NO = x.Select(y => y.SNAME).FirstOrDefault(),
                    ADI06 = x.Where(c => c.TYPE == "ADI06").Sum(c => c.CASH_IN),


                }).ToList();
                var temp = db.HRMT01.Where(x => (x.USER_TYPE == UserType.Admin
                || x.USER_TYPE == UserType.Teacher) && x.USER_STATUS == UserStaus.Enabled && x.SCHOOL_NO == SchoolNO).Select(x => new HRMT01QTY
                {
                    SNAME = x.SNAME,
                    USER_NO = x.USER_NO,
                    SCHOOL_NO = x.SCHOOL_NO,


                }).ToList();
                var results = (from  line in db.SUMALLPOINT where( line.SCHOOL_NO == SchoolNO && line.LOG_TIME >= model.whereSTART_CRE_DATE && line.LOG_TIME <= model.whereEND_CRE_DATE && line.SNAME != null)
                              group line  by new { line.USER_NO,line.SNAME,line.SCHOOL_NO }  into g
                select  new HRMT01QTY
                {
                    SNAME = g.Key.SNAME,
                    USER_NO = g.Key.USER_NO,
                    SCHOOL_NO =g.Key.SCHOOL_NO,
                    Extend = g.Where(c => c.TYPE == "Extend").Sum(c => c.CASH_IN),
                    ADI06 = g.Where(c => c.TYPE == "ADI06").Sum(c => c.CASH_IN),
                    AWT08Exced = g.Where(c => c.TYPE == "AWT08Exced").Sum(c => c.CASH_IN),
                    AWT08ExcedREADYBATCHID = g.Where(c => c.TYPE == "AWT08ExcedREADYBATCHID").Sum(c => c.CASH_IN),
                    AWT08Exted = g.Where(c => c.TYPE == "AWT08Exted").Sum(c => c.CASH_IN),
                    AWT08ExtedRedey = g.Where(c => c.TYPE == "AWT08ExtedRedey").Sum(c => c.CASH_IN)
                }).ToList();
                var tempHRMT01 = db.HRMT01.Where(x => (x.USER_TYPE == UserType.Admin
           || x.USER_TYPE == UserType.Teacher) && x.USER_STATUS == UserStaus.Enabled && x.SCHOOL_NO == SchoolNO).ToList();

                var temp4 = tempHRMT01.Select(x => new HRMT01QTY
                {
                    SNAME = x.SNAME,
                    USER_NO = x.USER_NO,
                    SCHOOL_NO = x.SCHOOL_NO,
                
                }).ToList();
                var resualt3 = (from y in results
                                join j in temp4 on new { y.SCHOOL_NO, y.USER_NO } equals new { j.SCHOOL_NO, j.USER_NO }
                                select new HRMT01QTY
                                {
                                    SNAME = j.SNAME,
                                    USER_NO = j.USER_NO,
                                    SCHOOL_NO = j.SCHOOL_NO,
                                    Extend = CalculateExtend(y.Extend, y.AWT08Exted, y.AWT08ExtedRedey),
                                    ADI06 = CalculateExtend(y.ADI06, y.AWT08Exced, y.AWT08ExcedREADYBATCHID)
                                }).ToList();

          
                RankCash = resualt3;

              
            }
            else {
                model.whereSTART_CRE_DATE = DateTime.Now.AddMonths(-3);
                model.whereEND_CRE_DATE = DateTime.Now;
                List<SUMALLPOINT> SUMALLPOINTExtend = new List<SUMALLPOINT>();
                List<SUMALLPOINT> SUMALLPOINTADDI06 = new List<SUMALLPOINT>();
                SUMALLPOINTExtend = db.SUMALLPOINT.Where(x => (x.TYPE == "Extend" || x.TYPE == "ADI06")
                && x.SCHOOL_NO == SchoolNO && x.LOG_TIME >= model.whereSTART_CRE_DATE && x.LOG_TIME <= model.whereEND_CRE_DATE && x.SNAME != null).ToList();
                var temp1 = SUMALLPOINTExtend.GroupBy(l => new { l.USER_NO, l.TYPE }).Select(x => new HRMT01QTY
                {
                    Extend = x.Where(c => c.TYPE == "Extend").Sum(c => c.CASH_IN),
                    USER_NO = x.Select(y => y.SNAME).FirstOrDefault(),
                    ADI06 = x.Where(c => c.TYPE == "ADI06").Sum(c => c.CASH_IN),


                }).ToList();
                var temp = db.HRMT01.Where(x => (x.USER_TYPE == UserType.Admin
            || x.USER_TYPE == UserType.Teacher) && x.USER_STATUS == UserStaus.Enabled && x.SCHOOL_NO == SchoolNO).Select(x => new HRMT01QTY
            {
                SNAME = x.SNAME,
                USER_NO = x.USER_NO,
                SCHOOL_NO = x.SCHOOL_NO,


            }).ToList();
                var results = (from line in db.SUMALLPOINT
                              where (line.SCHOOL_NO == SchoolNO && line.LOG_TIME >= model.whereSTART_CRE_DATE && line.LOG_TIME <= model.whereEND_CRE_DATE && line.SNAME != null)
                              group line by new { line.USER_NO, line.SNAME, line.SCHOOL_NO } into g
                              select new HRMT01QTY
                              {
                                  SNAME = g.Key.SNAME,
                                  USER_NO = g.Key.USER_NO,
                                  SCHOOL_NO = g.Key.SCHOOL_NO,
                                  Extend = g.Where(c => c.TYPE == "Extend").Sum(c => c.CASH_IN),
                                  ADI06 = g.Where(c => c.TYPE == "ADI06").Sum(c => c.CASH_IN),
                                  AWT08Exced = g.Where(c => c.TYPE == "AWT08Exced").Sum(c => c.CASH_IN),
                                  AWT08ExcedREADYBATCHID = g.Where(c => c.TYPE == "AWT08ExcedREADYBATCHID").Sum(c => c.CASH_IN),
                                  AWT08Exted = g.Where(c => c.TYPE == "AWT08Exted").Sum(c => c.CASH_IN),
                                  AWT08ExtedRedey = g.Where(c => c.TYPE == "AWT08ExtedRedey").Sum(c => c.CASH_IN)
                              }).ToList();
                var tempHRMT01 = db.HRMT01.Where(x => (x.USER_TYPE == UserType.Admin
           || x.USER_TYPE == UserType.Teacher) && x.USER_STATUS == UserStaus.Enabled && x.SCHOOL_NO == SchoolNO).ToList();
                var temp4 = tempHRMT01.Select(x => new HRMT01QTY
                {
                    SNAME = x.SNAME,
                    USER_NO = x.USER_NO,
                    SCHOOL_NO = x.SCHOOL_NO,
                    Extend = 0,
                    ADI06 = 0
                }).ToList();
                var resualt3 = (from y in results
                                join j in temp4 on new { y.SCHOOL_NO, y.USER_NO } equals new { j.SCHOOL_NO, j.USER_NO }
                                select new HRMT01QTY
                                {
                                    SNAME = j.SNAME,
                                    USER_NO = j.USER_NO,
                                    SCHOOL_NO = j.SCHOOL_NO,
                                    Extend = CalculateExtend(y.Extend, y.AWT08Exted, y.AWT08ExtedRedey),
                                    ADI06 = CalculateExtend(y.ADI06, y.AWT08Exced, y.AWT08ExcedREADYBATCHID)
                                }).ToList();

               
             
                    RankCash = resualt3;
             
            }
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                RankCash = RankCash.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim())).ToList();
            }
            switch (model.OrdercColumn)
            {
               
                case "ADI06":
                    RankCash = RankCash.OrderByDescending(a => a.ADI06).ToList();
                    break;
             
                case "Extend":
                    RankCash = RankCash.OrderByDescending(a => a.Extend).ToList();
                    break;

                case "ADI07":
                    RankCash = RankCash.OrderByDescending(a => a.Extend).ToList();
                    break;

                default:
                    RankCash= RankCash;
                    break;
            }
            return RankCash;
        }
        public  int? CalculateExtend(int? extend, int? awt08Exted, int? awt08ExtedRedey)
        {
            return (extend ?? 0) + (awt08Exted ?? 0) - (awt08ExtedRedey ?? 0);
        }

        public IQueryable<HRMT01QTY> QeuryAWA002(AWA003QueryViewModel model)
        {
            IQueryable<HRMT01QTY> RankCash;

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }


            string sSQL = @" select *,( k.ADI06+k.ADI07+k.Extend ) as SUMCash from( SELECT AWA01L.[SCHOOL_NO],h1.SNAME,h1.SEAT_NO,h1.CLASS_NO
      ,AWA01L.[USER_NO],
     (select top 1 case when SUM(a1.CASH_IN)  is null then 0 else SUM(a1.CASH_IN) end
	 from [dbo].[AWAT01_LOG]  a1
	 where a1.SCHOOL_NO =h1.[SCHOOL_NO]  
	 and a1.USER_NO = h1.USER_NO  and (LOG_DESC like'%校內表現%' or LOG_DESC like'%批次校內表現%'  or SOURCE_TYPE='ADDI13' or SOURCE_TYPE='ADDI06' or (SOURCE_TYPE='ADDI09'and LOG_DESC not like'%批次校外榮礜%')) ) as ADI06
	 ,
	 (select top 1 case when SUM(a2.CASH_IN)is null then 0 else SUM(a2.CASH_IN) end
	 from [dbo].[AWAT01_LOG]  a2
	 where a2.SCHOOL_NO =h1.[SCHOOL_NO]  
	 and a2.USER_NO = h1.USER_NO  and ( LOG_DESC like'%校外榮譽%' or LOG_DESC like '%校外表現%' or ((SOURCE_TYPE='ADDI09'and LOG_DESC  like'%批次校外榮礜%')))) as ADI07,
	  (select case when SUM(a3.CASH_IN)is null then 0 else SUM(a3.CASH_IN) end
	 from [dbo].[AWAT01_LOG]  a3
	 where a3.SCHOOL_NO =h1.[SCHOOL_NO]  
	 and  a3.USER_NO = h1.USER_NO and (LOG_DESC not like'%校外榮譽%'  and  LOG_DESC not like '%校外表現%' and SOURCE_TYPE!='ADDI09' and LOG_DESC not like'%校內表現%' and  LOG_DESC not like'%批次校內表現%' and LOG_DESC not like'%獎品兌換%' and SOURCE_TYPE!='ADDI13' and SOURCE_TYPE!='ADDI06'  and LOG_DESC not like'%角色娃娃%' and LOG_DESC not like'%定存%') ) as Extend

  FROM [dbo].[AWAT01_LOG] as AWA01L 
  left join HRMT01 AS h1 ON h1.SCHOOL_NO = AWA01L.SCHOOL_NO AND h1.USER_NO = AWA01L.USER_NO
  left join AWAT01_X_REF as X_REF   on X_REF.SOURCE_TYPE=AWA01L.SOURCE_TYPE
  where AWA01L.[SCHOOL_NO]=@SCHOOL_NO  and  h1.USER_STATUS<>9 ";
            string sSQL3 = @" group by h1.[SCHOOL_NO], AWA01L.[SCHOOL_NO],AWA01L.[USER_NO]
      ,h1.[USER_NO],h1.SNAME,h1.SEAT_NO,h1.CLASS_NO";
            string sSQL2 = @") k";
            if (model.whereSTART_CRE_DATE != null && model.whereEND_CRE_DATE != null)
            {

                string sSQL4 = @" select *,( k.ADI06+k.ADI07+k.Extend ) as SUMCash from( SELECT AWA01L.[SCHOOL_NO],h1.SNAME,h1.SEAT_NO,h1.CLASS_NO
      ,AWA01L.[USER_NO],
     (select top 1 case when SUM(a1.CASH_IN)  is null then 0 else SUM(a1.CASH_IN) end
	 from [dbo].[AWAT01_LOG]  a1
	 where a1.SCHOOL_NO =h1.[SCHOOL_NO]  
	 and a1.USER_NO = h1.USER_NO  and (LOG_DESC like'%校內表現%' or LOG_DESC like'%批次校內表現%'  or SOURCE_TYPE='ADDI13' or SOURCE_TYPE='ADDI06' or (SOURCE_TYPE='ADDI09'and LOG_DESC not like'%批次校外榮礜%')) and LOG_TIME between @SDate and @EDate ) as ADI06
	 ,
	 (select top 1 case when SUM(a2.CASH_IN)is null then 0 else SUM(a2.CASH_IN) end
	 from [dbo].[AWAT01_LOG]  a2
	 where a2.SCHOOL_NO =h1.[SCHOOL_NO]  
	 and a2.USER_NO = h1.USER_NO  and( LOG_DESC like'%校外榮譽%' or LOG_DESC like '%校外表現%' or ((SOURCE_TYPE='ADDI09'and LOG_DESC  like'%批次校外榮礜%'))) and LOG_TIME between @SDate and @EDate ) as ADI07,
	  (select case when SUM(a3.CASH_IN)is null then 0 else SUM(a3.CASH_IN) end
	 from [dbo].[AWAT01_LOG]  a3
	 where a3.SCHOOL_NO =h1.[SCHOOL_NO]  
	 and  a3.USER_NO = h1.USER_NO and (LOG_DESC not like'%校外榮譽%'  and  LOG_DESC not like '%校外表現%' and SOURCE_TYPE!='ADDI09' and LOG_DESC not like'%校內表現%' and  LOG_DESC not like'%批次校內表現%' and LOG_DESC not like'%獎品兌換%' and SOURCE_TYPE!='ADDI13' and SOURCE_TYPE!='ADDI06'  and LOG_DESC not like'%角色娃娃%' and LOG_DESC not like'%定存%') and LOG_TIME between @SDate and @EDate ) as Extend

  FROM [dbo].[AWAT01_LOG] as AWA01L 
  left join HRMT01 AS h1 ON h1.SCHOOL_NO = AWA01L.SCHOOL_NO AND h1.USER_NO = AWA01L.USER_NO
  left join AWAT01_X_REF as X_REF   on X_REF.SOURCE_TYPE=AWA01L.SOURCE_TYPE
  where AWA01L.[SCHOOL_NO]=@SCHOOL_NO  and  h1.USER_STATUS<>9 ";
                string sSQL1 = @" and LOG_TIME between @SDate and @EDate";
                sSQL4 = sSQL4 + sSQL1+ sSQL3 + sSQL2;
                RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL4 , new { SCHOOL_NO = SchoolNO ,SDate = model.whereSTART_CRE_DATE, EDate = model.whereEND_CRE_DATE }).AsQueryable();
            }
            else {

          
                sSQL = sSQL + sSQL3+ sSQL2;
                RankCash = db.Database.Connection.Query<HRMT01QTY>(sSQL, new { SCHOOL_NO = SchoolNO }).AsQueryable();
            }
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
              
                RankCash = RankCash.Where(a => (model.whereKeyword.Contains(a.USER_NO ))||(model.whereKeyword.Contains(a.SNAME)));
                //Contains 不可有位篩選的欄位
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                RankCash = RankCash.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim()).AsQueryable();

            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                RankCash = RankCash.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade).AsQueryable();
            }
            switch (model.OrdercColumn)
            {
                case "CLASS_NO":
                    RankCash = RankCash.OrderByDescending(a => a.CLASS_NO);
                    break;

                case "SEAT_NO":
                    RankCash = RankCash.OrderByDescending(a => a.SEAT_NO);
                    break;

                case "SNAME":
                    RankCash = RankCash.OrderByDescending(a => a.SNAME);
                    break;
                case "USER_NO":
                    RankCash = RankCash.OrderByDescending(a => a.USER_NO);
                    break;
                case "ADI06":
                    RankCash = RankCash.OrderByDescending(a => a.ADI06);
                    break;
                case "ADI07":
                    RankCash = RankCash.OrderByDescending(a => a.ADI07);
                    break;
                case "Extend":
                    RankCash = RankCash.OrderByDescending(a => a.Extend);
                    break;
                case "SUMCash":
                    RankCash = RankCash.OrderByDescending(a => a.SUMCash);
                    break;
                default:
                    RankCash = RankCash.OrderByDescending(a => a.SUMCash);
                    break;
            }
            return RankCash;
        }
        public ActionResult PrintExcel(string User_No, string LogDESC, AWA002Query2ViewModel model)
        {
            model.whereIsExcel = true;
            if ((model.whereSOURCETABLE != null && (model.whereSOURCETABLE == "ADI07" || model.whereSOURCETABLE == "Extend") )||model.whereUserNo!=null)
            {


                Query2SouDataTaecher(User_No, LogDESC, model);
            }
            else {

                Query2SouData(User_No, LogDESC, model);

            }
           

            DataTable DataTableExcel = model.VAWAT01_LOG.ToList().AsDataTable();

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/Cash_Log_ExportExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "酷幣紀錄清單", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\酷幣紀錄清單_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "酷幣紀錄清單.xlsx");//輸出檔案給Client端
        }

        public ActionResult PrintExcel_Query(string User_No, string LogDESC, AWA002QueryViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            model.whereIsExcel = true;

            QuerySouData(model, user);

            DataTable dt = new DataTable();// model.VAWA002List.ToList().AsDataTable();
            dt.Columns.Add("USER_NO");
            dt.Columns.Add("CLASS_NO");
            dt.Columns.Add("SEAT_NO");
            dt.Columns.Add("NAME");
            dt.Columns.Add("SumCASH_IN", typeof(int));
            dt.Columns.Add("LOG_DESC");
            foreach (HRMT02QTY q in model.VAWA002List)
            {
                DataRow dr = dt.NewRow();
                dr["USER_NO"] = q.USER_NO;
                dr["CLASS_NO"] = q.CLASS_NO;
                dr["SEAT_NO"] = q.SEAT_NO;
                dr["NAME"] = q.NAME;
                if (q.SumCASH_IN.HasValue) dr["SumCASH_IN"] = q.SumCASH_IN.Value;
                dr["LOG_DESC"] = q.LOG_DESC;
                dt.Rows.Add(dr);
            }

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/Cash_Log_ExportExcel_Q.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(dt, aBook, "各類酷幣給點統計清單", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\各類酷幣給點統計清單_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "各類酷幣給點統計清單.xlsx");//輸出檔案給Client端
        }

        public ActionResult MODIFY(uAWAT01_Detail_LOG item)
        {
            TempData["USER_NO"] = item.USER_NO;
            TempData["fromSOurce"] = "AWA002";
            ADDT38 aDDT38temp = new ADDT38();
            if (item.SOURCE_TYPE == "ADDI13") {


                db.ADDT38.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_NO == item.USER_NO && x.BATCH_CASH_ID == item.SOURCE_NO).FirstOrDefault(); 


            }
            if (item.SOURCE_TYPEEdite == "ZZZI20") {

                return RedirectToAction("ListView1", "ADDI09", new { ListView = "", SOURCE_NO = item.SOURCE_NO, ThisSaveBATCH_CASH_ID = "", SYS_TABLE_TYPE = "ZZZI20", ADDT14_STYLE = "ZZZI20", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            if (item.SOURCE_TYPEEdite == "ADDT26_D") {


                if (item.LOG_TABLE == "ADDT14")
                {

                    return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = item.SOURCE_NO, ThisSaveBATCH_CASH_ID = "", SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
                }
                else if (item.LOG_TABLE == "ADDT20") {

                    return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = "", ThisSaveBATCH_CASH_ID = item.SOURCE_NO, SYS_TABLE_TYPE = "ADDT20", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });

                }
                
            }
            if (!string.IsNullOrEmpty(item.LOG_TABLE))
            {


                return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = "", ThisSaveBATCH_CASH_ID = item.SOURCE_NO, SYS_TABLE_TYPE = item.LOG_TABLE, ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            else { 
            if (item.LOG_DESC.Contains("批次校外") || item.LOG_DESC.Contains("批次校外榮礜 -"))
            {
                return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = "", ThisSaveBATCH_CASH_ID = item.SOURCE_NO, SYS_TABLE_TYPE = "ADDT15", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            else if  ((item.LOG_DESC.Contains("批次校內表現") || item.SOURCE_TYPE.Contains("批次校內表現") || item.LOG_DESC.Contains("批次校內表現-") || item.LOG_DESC.Contains("批次校內表現班級幫手和榮譽-") || item.LOG_DESC.Contains("批次校內表現班級幹部-") || item.LOG_DESC.Contains("批次校內表現班級小幫手 -") || item.LOG_DESC.Contains("批次快速大量加點-特殊加扣點-")) && !item.LOG_DESC.Contains("特殊加扣點") )
            {

                return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = "", ThisSaveBATCH_CASH_ID = item.SOURCE_NO, SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            else if (item.LOG_DESC.Contains("特殊加扣點") || item.LOG_DESC.Contains("批次特殊加扣點") || item.LOG_DESC.Contains("批次特殊加扣點 -") || item.LOG_DESC.Contains("特殊加扣點-"))
            {
                return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = "", ThisSaveBATCH_CASH_ID = item.SOURCE_NO, SYS_TABLE_TYPE = "ADDT20", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            else if (item.LOG_DESC.Contains("批次快速大量加點") || item.LOG_DESC.Contains("批次快速大量加點 -校內表現-"))
            {
                return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = "", ThisSaveBATCH_CASH_ID = item.SOURCE_NO, SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            else if (item.LOG_DESC.Contains("校外榮譽") || item.LOG_DESC.Contains("校外榮譽 - "))
            {
                return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = item.SOURCE_NO, ThisSaveBATCH_CASH_ID = "", SYS_TABLE_TYPE = "ADDT15", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            else if (item.LOG_DESC.Contains("校內表現") || item.LOG_DESC.Contains("校內表現 -") || item.LOG_DESC.Contains("班級幫手和榮譽") || item.LOG_DESC.Contains("班級幫手和榮譽 -"))
            {
                return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = item.SOURCE_NO, ThisSaveBATCH_CASH_ID = "", SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO });
            }
            else { return RedirectToAction("ListView", "ADDI09", new { ListView = "", SOURCE_NO = "", ThisSaveBATCH_CASH_ID = "", SYS_TABLE_TYPE = "ADDT14", ADDT14_STYLE = "", USER_NO = item.USER_NO, SCHOOL_NO = item.SCHOOL_NO }); }
            }
        }

        private void Query2SouDataTaecher(string User_No, string LogDESC, AWA002Query2ViewModel model)
        {

            int PageSize = 20;
            UserProfile user = UserProfileHelper.Get();
            string School_No = UserProfileHelper.GetSchoolNo();

      
                IQueryable<uAWAT01_Detail_LOG> VAWAT01_LOGList = (from aw01_log in db.AWAT01_LOG

                                                                  join h01 in db.HRMT01 on new { aw01_log.SCHOOL_NO, aw01_log.USER_NO } equals new { h01.SCHOOL_NO, h01.USER_NO }

                                                                  join P_h01 in db.HRMT01 on aw01_log.LOG_PERSON equals P_h01.USER_KEY into temp

                                                                  from ds in temp.DefaultIfEmpty()
                                                                 
                                                                  join P_REF in db.AWAT01_X_REF on new { aw01_log.SOURCE_TYPE } equals new { P_REF.SOURCE_TYPE } into temp_ref

                                                                  from ds_ref in temp_ref.DefaultIfEmpty()

                                                              

                                                                  where aw01_log.SCHOOL_NO == School_No && h01.USER_STATUS == UserStaus.Enabled && aw01_log.LOG_PERSON== School_No+"_"+model.whereUserNo

                                                                  select new uAWAT01_Detail_LOG
                                                                  {
                                                                      CLASS_NO = h01.CLASS_NO,
                                                                      SEAT_NO = h01.SEAT_NO,
                                                                      SCHOOL_NO = aw01_log.SCHOOL_NO,
                                                                      USER_NO = aw01_log.USER_NO,
                                                                      USER_TYPE = h01.USER_TYPE,
                                                                      SOURCE_TYPE = aw01_log.SOURCE_TYPE == "ADDI09" && aw01_log.LOG_DESC.Contains("批次校外榮礜") ? "批次校外榮礜" : (ds_ref.LOG_DESC ?? ds_ref.CHART_DESC),
                                                                      LOG_DESCDetail= aw01_log.LOG_DESC,
                                                                      SOURCE_NO = aw01_log.SOURCE_NO,
                                                                      CASH_IN = aw01_log.CASH_IN,
                                                                      LOG_TIME = aw01_log.LOG_TIME,
                                                                      LOG_DESC = aw01_log.LOG_DESC ?? ds_ref.LOG_DESC,
                                                                      CHART_DESC = ds_ref.CHART_DESC,
                                                                      SOURCE_TYPEEdite = aw01_log.SOURCE_TYPE == "ADDI09" && aw01_log.LOG_DESC.Contains("批次校外榮礜") ? "ADDT15" : aw01_log.SOURCE_TYPE,
                                                                      LOG_PERSON = ds.NAME ?? aw01_log.LOG_PERSON,
                                                                      LOG_PERSON_NAME = ds.NAME ?? aw01_log.LOG_PERSON,
                                                                      USERNAME = h01.NAME,
                                                                      SNAME = h01.SNAME,
                                                                      SYEAR = aw01_log.LOG_TIME.Year - 1911,
                                                                      SEMESTER = (byte)((aw01_log.LOG_TIME.Month >= 2 && aw01_log.LOG_TIME.Month < 8) ? 2 : 1),
                                                                      AWAT01_CASH_AVAILABLE = aw01_log.AWAT01_CASH_AVAILABLE,
                                                                      LOG_TABLE = aw01_log.LOG_TABLE
        
                                                                  });



          

            if (model.whereSOURCETABLE == "ADI07")
                {

                    // (LOG_DESC not like '%特殊加扣點%' and LOG_DESC  not like '%班級特殊加扣點%' and LOG_DESC  not like'%班級幫手和榮譽%') and LOG_PERSON!= '系統給扣點'
                    VAWAT01_LOGList = VAWAT01_LOGList.Where(x => !x.LOG_DESCDetail.Contains("特殊加扣點") && !x.LOG_DESCDetail.Contains("特殊加扣點") && !x.LOG_DESCDetail.Contains("特殊加扣點") && !x.LOG_DESCDetail.Contains("獎品兌換") && !x.LOG_DESCDetail.Contains("角色娃娃") && !x.LOG_DESCDetail.Contains("定存") && x.LOG_PERSON != "特殊加扣點" && x.LOG_PERSON != "系統給扣點");


                }
                if (model.whereSOURCETABLE == "Extend")
                {


                    VAWAT01_LOGList = VAWAT01_LOGList.Where(x => (x.LOG_DESCDetail.Contains("特殊加扣點") || x.LOG_DESCDetail.Contains("特殊加扣點") || x.LOG_DESCDetail.Contains("特殊加扣點") || x.LOG_DESCDetail.Contains("班級特殊加扣點") || x.LOG_DESCDetail.Contains("班級幫手和榮譽") ) && x.LOG_PERSON != "特殊加扣點");

                }
                if (model.whereLOG_TIME_S != null && model
                    .whereLOG_TIME_E != null)
                {
                    DateTime Sdate = DateTime.Parse(model.whereLOG_TIME_S);
                    DateTime Edate = DateTime.Parse(model.whereLOG_TIME_E);
                    VAWAT01_LOGList = VAWAT01_LOGList.Where(x => x.LOG_TIME >= Sdate && x.LOG_TIME <= Edate);
                }
              
                model.VAWAT01_LOG = VAWAT01_LOGList.OrderByDescending(a => a.LOG_TIME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
                    
                //感覺好像沒用 mark 掉  by 2018.10.31  king
                ////線上投稿有用的留言
                //if (model.VAWAT01_LOG.Count() < 100)
                //{
                //    foreach (var it in model.VAWAT01_LOG)
                //    {
                //        if (it.SOURCE_TYPE == "ADDT02")
                //        {
                //            int sno = 0;
                //            if (int.TryParse(it.SOURCE_NO, out sno))
                //            {
                //                int WritingNO = db.ADDT02.Where(a => a.COMMENT_NO == sno).Select(a => a.WRITING_NO).FirstOrDefault();
                //                if (WritingNO > 0) it.SOURCE_NO = WritingNO.ToString();
                //            }
                //        }
                //    }
                //}

                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

                ViewBag.ClassItems = HRMT01.GetClassListData(School_No, model.whereGrade, model.whereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

                var ArrCHART_DESC = (from p in db.AWAT01_X_REF
                                     group p by new { p.CHART_DESC } into g
                                     select g.Key.CHART_DESC).ToList();

                List<CheckBoxListInfo> ListCHART_DESC = new List<CheckBoxListInfo>();

                foreach (var item in ArrCHART_DESC)
                {
                    CheckBoxListInfo CHART_DESC = new CheckBoxListInfo();
                    CHART_DESC.DisplayText = item;
                    CHART_DESC.Value = item;
                    CHART_DESC.IsChecked = model.whereArrCHART_DESC != null ? model.whereArrCHART_DESC.Contains(item) : false;
                    ListCHART_DESC.Add(CHART_DESC);
                }

                ViewBag.ArrCHART_DESC = ListCHART_DESC;
      

        }
       private void Query2SouData(string User_No, string LogDESC, AWA002Query2ViewModel model)
        {
            int PageSize = 20;
            UserProfile user = UserProfileHelper.Get();
            string School_No = UserProfileHelper.GetSchoolNo();

          
                IQueryable<uAWAT01_Detail_LOG> VAWAT01_LOGList = (from aw01_log in db.AWAT01_LOG

                                                                  join h01 in db.HRMT01 on new { aw01_log.SCHOOL_NO, aw01_log.USER_NO } equals new { h01.SCHOOL_NO, h01.USER_NO }

                                                                  join P_h01 in db.HRMT01 on aw01_log.LOG_PERSON equals P_h01.USER_KEY into temp
                                                                  from ds in temp.DefaultIfEmpty()

                                                                  join P_REF in db.AWAT01_X_REF on new { aw01_log.SOURCE_TYPE } equals new { P_REF.SOURCE_TYPE } into temp_ref

                                                                  from ds_ref in temp_ref.DefaultIfEmpty()

                                                                  where aw01_log.SCHOOL_NO == School_No && h01.USER_STATUS == UserStaus.Enabled 
                                                                  
                                                                  select new uAWAT01_Detail_LOG
                                                                  {
                                                                      CLASS_NO = h01.CLASS_NO,
                                                                      SEAT_NO = h01.SEAT_NO,
                                                                      SCHOOL_NO = aw01_log.SCHOOL_NO,
                                                                      USER_NO = aw01_log.USER_NO,
                                                                      USER_TYPE = h01.USER_TYPE,
                                                                      SOURCE_TYPE = 
                                                                      (aw01_log.SOURCE_TYPE == "ADDI09" && aw01_log.LOG_DESC.Contains("批次校外榮礜") 
                                                                      ? "批次校外榮礜" : aw01_log.SOURCE_TYPE == "ADDI06" && (aw01_log.LOG_DESC.Contains("校外榮礜") || aw01_log.LOG_DESC == "校外榮譽-修改") ? "校外榮礜" : (ds_ref.LOG_DESC ?? ds_ref.CHART_DESC))
                                                                      
                                                                      ,



                                                                      SOURCE_NO = aw01_log.SOURCE_NO,
                                                                      CASH_IN = (short)aw01_log.CASH_IN,
                                                                      LOG_TIME = aw01_log.LOG_TIME,
                                                                      LOG_DESC = aw01_log.LOG_DESC ?? ds_ref.LOG_DESC,
                                                                      CHART_DESC = aw01_log.SOURCE_TYPE == "ADDI06" && (aw01_log.LOG_DESC.Contains("校外榮礜")|| aw01_log.LOG_DESC== "校外榮譽-修改") ? "校外榮礜" : ds_ref.CHART_DESC,
                                                                      SOURCE_TYPEEdite = aw01_log.SOURCE_TYPE == "ADDI09" && aw01_log.LOG_DESC.Contains("批次校外榮礜") ? "ADDT15" : aw01_log.SOURCE_TYPE,
                                                                      LOG_PERSON = ds.NAME ?? aw01_log.LOG_PERSON,
                                                                      LOG_PERSON_NAME = ds.NAME ?? aw01_log.LOG_PERSON,
                                                                      USERNAME = h01.NAME,
                                                                      SNAME = h01.SNAME,
                                                                      SYEAR = aw01_log.LOG_TIME.Year - 1911,
                                                                      SEMESTER = (byte)((aw01_log.LOG_TIME.Month >= 2 && aw01_log.LOG_TIME.Month < 8) ? 2 : 1),
                                                                      AWAT01_CASH_AVAILABLE = aw01_log.AWAT01_CASH_AVAILABLE,
                                                                      LOG_TABLE = aw01_log.LOG_TABLE 
                                                                  });

                if (user != null && string.IsNullOrEmpty(User_No))
                {
                    if (model.WhereIsPassbook)
                    {
                        if (user.USER_TYPE == UserType.Parents)
                        {
                            string StudentNO = user.USER_NO.Substring(1, user.USER_NO.Length - 1);
                            VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.USER_NO == StudentNO);
                        }
                        else
                        {
                            VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.USER_NO == user.USER_NO);
                        }
                    }
                    else
                    {
                        if (user.USER_TYPE == UserType.Student)
                        {
                            VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.USER_NO == user.USER_NO);
                        }
                        else if (user.USER_TYPE == UserType.Parents)
                        {
                            string StudentNO = user.USER_NO.Substring(1, user.USER_NO.Length - 1);
                            VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.USER_NO == StudentNO);
                        }
                    }
                }
                else
                {
                    VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.USER_NO == User_No);
                }

                if (string.IsNullOrEmpty(LogDESC) == false)
                {
                    VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.LOG_DESC == LogDESC);
                }

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                if (model.whereKeyword == "轉學生") {


                    VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()) || a.CLASS_NO.Contains(model.whereKeyword.Trim()) || a.LOG_DESC.Contains("酷幣匯轉區域管理") || a.LOG_PERSON_NAME.Contains(model.whereKeyword.Trim()));
                }

                else {

                    VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()) || a.CLASS_NO.Contains(model.whereKeyword.Trim()) || a.LOG_DESC.Contains(model.whereKeyword.Trim()) || a.LOG_PERSON_NAME.Contains(model.whereKeyword.Trim()));
                }
                  
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());

                    PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    VAWAT01_LOGList = VAWAT01_LOGList.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }

                if (model.whereArrCHART_DESC != null)
                {
                    string stringToCheck1 = "校外榮譽";

                    //兩邊榮譽 榮礜 字不相同
                    string stringToCheck = "批次校外榮礜";
                    if (model.whereArrCHART_DESC.Any(stringToCheck1.Contains))
                    {

                        VAWAT01_LOGList = VAWAT01_LOGList.Where(a => (model.whereArrCHART_DESC.Contains(a.CHART_DESC)) || (model.whereArrCHART_DESC.Contains(a.LOG_DESC)) || (a.SOURCE_TYPEEdite == "ADDT15"));
                    }
                    else {

                        VAWAT01_LOGList = VAWAT01_LOGList.Where(a => (model.whereArrCHART_DESC.Contains(a.CHART_DESC) && a.SOURCE_TYPEEdite != "ADDT15") || (model.whereArrCHART_DESC.Contains(a.LOG_DESC)) );
                    }
                       
               
                }

                if (string.IsNullOrWhiteSpace(model.whereLOG_TIME_S) == false)
                {
                    DateTime LOG_TIME_S;
                    if (DateTime.TryParse(model.whereLOG_TIME_S, out LOG_TIME_S))
                    {
                        VAWAT01_LOGList = VAWAT01_LOGList.Where(a => DbFunctions.TruncateTime(a.LOG_TIME) >= LOG_TIME_S.Date);
                    }
                }

                if (string.IsNullOrWhiteSpace(model.whereLOG_TIME_E) == false)
                {
                    DateTime LOG_TIME_E;
                    if (DateTime.TryParse(model.whereLOG_TIME_E, out LOG_TIME_E))
                    {
                        VAWAT01_LOGList = VAWAT01_LOGList.Where(a => DbFunctions.TruncateTime(a.LOG_TIME) <= LOG_TIME_E.Date);
                    }
                }

                if (model.WhereIsPassbook)
                {
                    if (model.whereIsExcel)
                    {
                        model.VAWAT01_LOG = VAWAT01_LOGList.OrderByDescending(a => a.LOG_TIME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
                    }
                    else
                    {
                        model.VAWAT01_LOG = VAWAT01_LOGList.OrderByDescending(a => a.LOG_TIME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 100);
                    }
                }
                else
                {
                    if (model.whereIsExcel)
                    {
                        model.VAWAT01_LOG = VAWAT01_LOGList.OrderByDescending(a => a.LOG_TIME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, int.MaxValue); 
                    }
                    else if (model.whereIsPrint)
                    {
                        int VAWAT01Count = VAWAT01_LOGList.Count();
                        model.VAWAT01_LOG = VAWAT01_LOGList.OrderByDescending(a => a.LOG_TIME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, VAWAT01Count);
                    }
                    else
                    {
                   var Item= VAWAT01_LOGList.Count();
                    model.VAWAT01_LOG = VAWAT01_LOGList.OrderByDescending(a => a.LOG_TIME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
                    //model.VAWAT01_LOG = VAWAT01_LOGList.OrderByDescending(a => a.LOG_TIME).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToPagedList(0, PageSize);
                }
                }

                //感覺好像沒用 mark 掉  by 2018.10.31  king
                ////線上投稿有用的留言
                //if (model.VAWAT01_LOG.Count() < 100)
                //{
                //    foreach (var it in model.VAWAT01_LOG)
                //    {
                //        if (it.SOURCE_TYPE == "ADDT02")
                //        {
                //            int sno = 0;
                //            if (int.TryParse(it.SOURCE_NO, out sno))
                //            {
                //                int WritingNO = db.ADDT02.Where(a => a.COMMENT_NO == sno).Select(a => a.WRITING_NO).FirstOrDefault();
                //                if (WritingNO > 0) it.SOURCE_NO = WritingNO.ToString();
                //            }
                //        }
                //    }
                //}

                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

                ViewBag.ClassItems = HRMT01.GetClassListData(School_No, model.whereGrade, model.whereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

                var ArrCHART_DESC = (from p in db.AWAT01_X_REF
                                     group p by new { p.CHART_DESC } into g
                                     select g.Key.CHART_DESC).ToList();

                List<CheckBoxListInfo> ListCHART_DESC = new List<CheckBoxListInfo>();

                foreach (var item in ArrCHART_DESC)
                {
                    CheckBoxListInfo CHART_DESC = new CheckBoxListInfo();
                    CHART_DESC.DisplayText = item;
                    CHART_DESC.Value = item;
                    CHART_DESC.IsChecked = model.whereArrCHART_DESC != null ? model.whereArrCHART_DESC.Contains(item) : false;
                    ListCHART_DESC.Add(CHART_DESC);
                }

                ViewBag.ArrCHART_DESC = ListCHART_DESC;
        
        }
    }
}