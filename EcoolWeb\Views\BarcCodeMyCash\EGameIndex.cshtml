﻿@model ZZZI34IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<style type="text/css">
    /* set border-box so that percents can be used for width, padding, etc (personal preference) */
    .cycle-slideshow, .cycle-slideshow * {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }


    .cycle-slideshow {
        width: 100%;
        margin: 10px auto;
        padding: 0;
        position: relative;
        background: url(http://malsup.github.com/images/spinner.gif) 50% 50% no-repeat;
    }

        /* slideshow images (for most of the demos, these are the actual "slides") */
        .cycle-slideshow img {
            /*
    some of these styles will be set by the plugin (by default) but setting them here
    helps avoid flash-of-unstyled-content
    */
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 0;
            display: block;
        }

            /* in case script does not load */
            .cycle-slideshow img:first-child {
                position: static;
                z-index: 100;
            }
</style>
<script src="~/Scripts/jquery.cycle2.js"></script>


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_EGameMenu", new { NowAction = "EGameIndex" });
}


<div class="cycle-slideshow">
    <img src="~/Content/Game/Game0.jpg" class="img-responsive" alt="Responsive image" />
    <img src="~/Content/Game/Game0.jpg" class="img-responsive" alt="Responsive image" />
</div>
<br />

<div style="text-align:center">
    <h3>
        本次「親愛阿目」市集採行動支付，不可金錢交易
    </h3>
    <br />
    <video id="SlotPlayer" style="width:250px ;height: auto" autoplay="autoplay" loop="loop">
        <source src="~/Content/mp4/RealtimePoint.mp4" type="video/mp4">
    </video>
</div>





