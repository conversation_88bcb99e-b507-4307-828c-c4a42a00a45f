@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO
@using ECOOL_APP.com.ecool.util
@using ECOOL_APP
@using EcoolWeb.Models

@{

    UserProfile user = UserProfileHelper.Get();
    string SYS_TABLE_TYPE = Request.Params["SYS_TABLE_TYPE"];
    bool Individual_Give = Convert.ToBoolean(Request.Params["Individual_Give"] == "" ? "false" : Request.Params["Individual_Give"]);
}

<div style="text-align: center; margin-top: 20px; margin-bottom: 30px;">
    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
</div>


@if (Model.ShowType == "Edit" && Individual_Give) // Edit畫面可修改點數
{
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <div style="height:15px"></div>
            <div class="row">
                <div class="col-md-3">
                    <div class="Caption_Div">
                        批次給分的貼心工具
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="input-group">
                        <input class="form-control input-sm text-box single-line" id="sweetAutoCash" placeholder="輸入獎懲點數" type="number" value="">
                        <span class="input-group-btn">
                            <button class="btn btn-default btn-sm" type="button" onclick="sweetAutoImport()">全部自動帶入這個點數</button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px; margin-bottom: 30px;">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
    </div>
}

<div class="Div-EZ-ADDI09">
    <div class="form-horizontal">
        <div class="row">
            <div class="col-xs-10">

                <label class="text-danger">
                    <samp>

                        @Html.HiddenFor(model => model.ShowType)

                        @if (Model.ShowType == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV)
                        {
                            <samp>* 已選取人員清單，請點選學號「刪除」</samp>
                        }
                        else
                        {
                            <samp>* 已選取 / 匯入 - 人員清單</samp>
                        }
                    </samp>
                    <samp>
                        總共已選 <span class="badge">@Model.SelectDataCount</span> 人
                    </samp>
                </label>
            </div>
            <div class="col-xs-2 text-right">
                <a href="@Url.Action("TempDataToExcel", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE })" title="匯出excel">
                    <img src="~/Content/img/excel-icon-16677.png" style="width:25px;height:auto" />
                </a>
            </div>
        </div>

        <br />
        <div class="btn-group btn-group-xs" role="group">
            @Html.HiddenFor(model => model.DivHeight)
            <button type="button" class="btn btn-default" id="DivHeight_plus" onclick="setDivHeight('+')"><i class="glyphicon glyphicon-plus"></i></button>
            <button type="button" class="btn btn-default" id="DivHeight_minus" onclick="setDivHeight('-')"><i class="glyphicon glyphicon-minus"></i></button>
        </div>

        <div class="table-responsive" id="DivSelectData">
            <table class="table-ecool  table-hover">
                <thead>
                    <tr>

                        <th align="center">
                            @*全選<input id="chkALL"   value="全選" type="checkbox" />*@
                        </th>

                        @if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                        {

                            <th align="center">
                                @Html.DisplayNameFor(model => model.DataList.First().USER_NO)
                            </th>}

                        @if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                        {
                            if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                            {

                                <th align="center">
                                    @Html.DisplayNameFor(model => model.DataList.First().CLASS_NO)
                                </th>


                            }



                            <th align="center">
                                @Html.DisplayNameFor(model => model.DataList.First().SEAT_NO)
                            </th>

                        }
                        <th align="center">
                            @Html.DisplayNameFor(model => model.DataList.First().NAME)
                        </th>

                        @if (Model.ShowType == "Edit" && Individual_Give) // Edit畫面可修改點數
                        {
                            <th align="center" style="margin-top:11px;height:30px;width:80px">
                                @Html.DisplayNameFor(model => model.DataList.First().TempCash)
                            </th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @{int Num = 0; }
                    @foreach (var item in Model.DataList)
                    {

                    <tr onclick="@(Individual_Give ? "" : "onBtnLink('" + item.USER_NO + "','')")" style="cursor:pointer">

                        @*<td class="text-center">*@
                        @*<input id="DataList_@Num" name="DataList[@Num]" type="checkbox" value="true" />*@
                        @*</td>*@
                        <td class="text-center"></td>
                        @if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                        {

                            <td align="center">
                                @if (!string.IsNullOrWhiteSpace(item.SEAT_NO) && item.USER_NO.Length != 9)
                                {
                                    @item.USER_NO
                                }
                                else
                                {
                                    @StringHelper.LeftStringR(item.USER_NO, 5, "*****");
                                }
                            </td>


                        }


                        @if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                        {
                            if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                            {


                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                            }


                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>

                        }
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.SNAME)
                        </td>
                        @if (Model.ShowType == "Edit" && Individual_Give) // Edit畫面可修改點數
                        {
                            <td align="center">
                                <input type="text" class="btn btn-default btn-xs"
                                       placeholder="0" id="inputNum-@item.USER_NO"
                                       style="margin-top:11px;height:30px;width:80px"
                                       onchange="changeCash(event, '@item.USER_NO','@user.USER_NO')"
                                       value="@(item.TempCash == 0 ? "": item.TempCash.ToString())" />
                                <div class="d-grid gap-2 d-md-block">
                                    <button type="button" class="jq-addNum btn btn-primary px-2 mb-1" onclick="addNum('inputNum-@item.USER_NO','@item.USER_NO')">+1</button>
                                    <button type="button" class="jq-delNum btn btn-primary px-2 mb-1" onclick="delNum('inputNum-@item.USER_NO','@item.USER_NO')">-1</button>
                                    <button type="button" class="jq-format btn btn-primary " onclick="formatNum('inputNum-@item.USER_NO','@item.USER_NO')">zero</button>
                                </div>
                            </td>


                        }
                    </tr>
                    Num++;
                }
                    </tbody>
                </table>
        </div>
    </div>
   
    <div class="text-center">
        @if (Model.ShowType == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV)
        {

            @Html.ActionLink("確定匯入", "Edit", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE }, new { @class = "btn btn-default", @role = "button" })
        }
    </div>
</div>

<script language="JavaScript">

    DivHeight()
    $('#EditmyButton').on('click', function () {

      $.ajax({
            url: '@Url.Action("Edit", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE })',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelectDataList').html(data);
                if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV') {
                    funAjax()
                }
            }
        });
        form1.submit();
    });
    //function CheckAll(obj) {
    //    var checkvalue = $(obj).is(":checked")
    //    if (checkvalue) {
    //        $("input[type='checkbox'][value='Y']").each(function () { this.checked = true; });

    //    }
    //    else {
    //        $("input[type='checkbox'][value='Y']").each(function () { this.checked = false; });
    //    }
    //}
    $("#chkALL").click(function () {

        if ($("#chkALL").prop("checked")) {
            $("input:checkbox").each(function () {
                if ($(this).attr("id") != 'cbPicture') {

                    $(this).prop("checked", true);
                }

            });
        }
        else {
            $("input:checkbox").each(function () {
                if ($(this).attr("id") != 'cbPicture') {
                    $(this).prop("checked", false);
                }
            });
        }
    });
    function addNum(str,UNO) {
        console.log("123"+str);
        var testr = "";
        testr = "#" + str;
        let inputNum = $(testr).val();
        $(testr).val(++inputNum);
        ChangeCashAJAX(false, UNO, $(testr).val());

    }
    function delNum(str, UNO) {
        var testr = "";
        testr = "#" + str;
        let inputNum = $(testr).val();
        $(testr).val(--inputNum);
        console.log(typeof inputNum);
        ChangeCashAJAX(false, UNO, $(testr).val());
    }
    function formatNum(str, UNO) {

        var testr = "";
        testr = "#" + str;
        $(testr).val(0);
        ChangeCashAJAX(false, UNO, $(testr).val());
    }
    function plus() {
        let inputNum = $(".inputNum").val();
        $(".inputNum").val(++inputNum);
        console.log(typeof inputNum);
    }
    function DivHeight() {
        var FixedHeight = 300;
        var Height = $('#DivSelectData').innerHeight();

        var DivHeight = $('#DivHeight').val();
        if (DivHeight == '') DivHeight = '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.DivHeightVal.DivHeightM'

        $('#DivHeight_plus').hide();
        $('#DivHeight_minus').hide();
        if (DivHeight == "-") {
            $('#DivHeight_plus').show();
        }
        else {
            $('#DivHeight_minus').show();
        }

        if (Height > FixedHeight && DivHeight == "-") {
            $('#DivSelectData').css("height", FixedHeight).css("overflow", "auto")
        }
        else {
            $('#DivSelectData').removeClass();
        }
    }

    function onBtnLink(USER_NO, DataType) {

        var ShowType = $('#ShowType').val();

        if (DataType == '') {
            if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV') {
                DataType = '@EcoolWeb.Controllers.ADDI09Controller.DataTypeVal.DataTypeD'
            }
        }

        var data = {
            "SYS_TABLE_TYPE": '@ViewBag.SYS_TABLE_TYPE',
            "ADDT14_STYLE": '@ViewBag.ADDT14_STYLE',
            "ShowType": ShowType,
            "DivHeight": $('#DivHeight').val(),
            "USER_NO": USER_NO,
            "DataType": DataType
        };

        $.ajax({
            url: '@Url.Action("_QuerySelectDataList", (string)ViewBag.BRE_NO, new { Individual_Give = Individual_Give })',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelectDataList').html(data);
                if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV') {
                    funAjax()
                }
            }
        });

    }

    function setDivHeight(Value) {

        $('#DivHeight').val(Value);

        onBtnLink('', '')
    }

    // -------------------- 手動加點 --------------------
    // -------------------- ------- --------------------

    function sweetAutoImport() {
        ChangeCashAJAX(true, '', '','');
    }

    function changeCash(e, userno,loginUserNO) {
        console.log(e.target);
        //let cash = $(e.target).val();
        ChangeCashAJAX(false, userno, e.target.value);
    }
    function setTime() {
        timerr++;
        if (timerr < "70") {
            $("#barr").css("width", timerr + "%");

        }
    }
  
    function ChangeCashAJAX(isAll, userno, cash) {

       @*if ('@user.USER_NO' == loginUserNO) {*@
        let data = {
            vm :[]
            };

        // 全部改動
            if (isAll) {

            let dataList = JSON.parse('@Html.Raw(Json.Encode(Model.DataList))');
        let cash = $("#sweetAutoCash").val();
            $.each(dataList, function (idx, item) {
            data.vm.push({ User_No: item.USER_NO, cash: cash});
        });
        } else {
            data.vm.push({ User_No: userno, cash: cash });
        }

        // ajax
        $.ajax({
    url: '@Url.Action("ChangeTempCash", (string)ViewBag.BRE_NO)',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function () {

                if (isAll) {
                    console.log("onBtnLink");
                onBtnLink('', '');
            }
        }
            });
        }
    //}
    // -------------------- ------- --------------------
</script>