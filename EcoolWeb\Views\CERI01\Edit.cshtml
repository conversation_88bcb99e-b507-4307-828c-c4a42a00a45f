﻿@model AccreditationTypeEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_Ceri02Menu", "CERI02", new { NowAction = "CERI01" });
}
@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.Keyword)
    @Html.HiddenFor(m => m.Main.TYPE_ID)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.TYPE_NAME, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })

                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.TYPE_NAME, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "必填", maxlength = "15" } })
                        <span style="color:red">(字數最多15個字)</span>
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.TYPE_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.CASH, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.CASH, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "請填數字，最小可輸入0" } })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.CASH, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center">
            <hr />
            <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
                取消
            </button>
            <button type="button" class="btn btn-default" onclick="onSave()">
                <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
            </button>
            @if (Model.Main?.TYPE_ID != null)
            {
                <button type="button" class="btn btn-default" onclick="onDel()">
                    <span class="fa fa-trash" aria-hidden="true"></span>作廢
                </button>
            }
        </div>
    </div>
    <img src="~/Content/img/AccreditationBook.png" class="img-responsive" style="max-height:150px" />
    <br />
    <ol class="Title_Secondary">
        <li>
            <strong>※每校護照不限一本，可以好幾本，專長護照、特色護照、體育護照……</strong>
        </li>
        <li>
            <strong>※除了護照名稱，還有兩層設定，各校依實際需求轉換設定</strong>
        </li>


    </ol>
    <br />
    <ul style="list-style: none">
        <li>
            <strong>
                110年新增用<span style="color:red">文字輸入專長</span>的功能，因為各校需求不同，建議以下做法
            </strong>

        </li>
    </ul>
    <ol>

        <li>
            <strong>
                護照名稱就稱為「其他專長護照」(也可以不同名稱)
            </strong>

        </li>
        <li>
            <strong>
                第一層(<span style="color:blue">護照細項名稱</span>)就稱為「其他專長」(有可以自訂)
            </strong>

        </li>
        <li>
            <strong>
                第二層，在各年級各開一個認證
            </strong>

        </li>
    </ol>
    <img src="~/Content/images/103.jpg" class="img-responsive" style="max-width:600px" />
    <br />
}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#form1';

        function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onDel()
        {
            var OK = confirm("您確定要作廢?作廢會同時作廢此護照的「護照明細設定」")

            if (OK==true)
            {
               $(targetFormID).attr("action", "@Url.Action("Del", (string)ViewBag.BRE_NO)")
               $(targetFormID).submit();
            }
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}
