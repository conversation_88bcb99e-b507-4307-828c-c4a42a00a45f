﻿@model GameEditPersonViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BatchWork" });
    }
}

@using (Html.BeginForm("EditPerson", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.WhereTEMP_USER_ID)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    @Html.HiddenFor(m => m.Person.TEMP_USER_ID)
    @Html.HiddenFor(m => m.Person.GAME_USER_TYPE)
    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            人員編輯
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(model => model.Person.NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Person.NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.Person.NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Person.GAME_USER_TYPE_DESC, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @if (Model.Person.GAME_USER_TYPE == UserType.Student)
                        {
                            @Html.EditorFor(model => model.Person.GAME_USER_TYPE_DESC, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填", @readonly = "readonly" } })
                        }
                        else
                        {
                            @Html.EditorFor(model => model.Person.GAME_USER_TYPE_DESC, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        }
                        @Html.ValidationMessageFor(model => model.Person.GAME_USER_TYPE_DESC, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Person.GAME_USER_ID, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Person.GAME_USER_ID, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.Person.GAME_USER_ID, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Person.PHONE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Person.PHONE, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.Person.PHONE, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="存檔" class="btn btn-default" onclick="Save()" />
                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="col-xs-12">
            <label class="text-info">
                備註：<br />
                身分可以是幼兒園、訪客、來賓等，10個字以內<br />
                姓名是為了抽獎辨識用<br />
            </label>
        </div>
    </div>

}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        function Save()
        {
            $(targetFormID).attr("action", "@Url.Action("EditPersonSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $('#@Html.IdFor(m=>m.Search.WhereTEMP_USER_ID)').val('')
            $(targetFormID).attr("action", "@Url.Action("EditApplyCard", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}