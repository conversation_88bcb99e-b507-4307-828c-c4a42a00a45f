﻿@model GameLotteryListIndexViewModel
@using ECOOL_APP.com.ecool.util

@Html.HiddenFor(m => m.Search.WhereGAME_NAME)
@Html.HiddenFor(m => m.Search.WhereGAME_NO)
@Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.WhereTEMP_USER_ID)

@Html.HiddenFor(m => m.CashSearch.SyntaxName)
@Html.HiddenFor(m => m.CashSearch.Page)
@Html.HiddenFor(m => m.CashSearch.OrdercColumn)
@Html.HiddenFor(m => m.IsPostBack, new { Value = SharedGlobal.Y })

<br />
<div role="form" id="Q_Div">

    <ul class="nav nav-tabs nav-pills">
        <li class="active"><a id="Btnquery1" data-toggle="tab" href="#query1" onclick="todoClear()">依學校及學號</a></li>
        <li><a id="Btnquery2" data-toggle="tab" href="#query2" onclick="todoClear()">依臨時卡卡號/入場券號碼</a></li>
    </ul>
    <div class="tab-content">
        <div id="query1" class="tab-pane fade in active">
            <div class="form-group">
                <label class="control-label">學校  (此處查詢限臺北市公私立國民小學學生，其他身份請至「依臨時卡卡號/入場券號碼」查詢)</label>
            </div>
            <div class="form-group">

                @if (ViewBag.SchoolNoSelectItem != null)
                {
                    <select class="selectpicker show-menu-arrow" title="" date-style="btn btn-default" data-size="auto" data-width="100%" data-live-search="true" id="CashSearch_WhereSCHOOL_NO" name="CashSearch.WhereSCHOOL_NO">
                        @foreach (var item in (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem)
                        {
                            <option data-tokens="@item.Text" value="@item.Value" @(item.Selected == true ? "selected" : "")>@item.Text</option>
                        }
                    </select>
                }
            </div>

            <div class="form-group">
                <label class="control-label">姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(model => model.CashSearch.WhereNAME, new { htmlAttributes = new { @class = "form-control input-lg" } })
            </div>
            <div class="form-group">
                <label class="control-label">學號 (請參考數位學生證上之學籍號碼)</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(model => model.CashSearch.WhereUSER_NO, new { htmlAttributes = new { @class = "form-control input-lg" } })
            </div>
        </div>
        <div id="query2" class="tab-pane fade">
            <div class="form-group">
                <label class="control-label">臨時卡卡號/入場券號碼</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(model => model.CashSearch.WhereCARD_NO, new { htmlAttributes = new { @class = "form-control input-lg", @placeholder = "請輸入臨時卡卡號/入場券號碼" } })
            </div>
        </div>
    </div>
    <br />
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    <br /><strong>*未輸條件，按「搜尋」時，列出全部中獎清單</strong>
</div>
<br />
@if (Model?.IsPostBack == SharedGlobal.Y)
{

    if (Model.Main.Count > 0)
    {
        foreach (var Main in Model.Main)
        {
            <strong class="childrens-item-title">  @Main.LOTTERY_DESC</strong>

            <div class="table-responsive">

                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>
                                <samp class="form-group">
                                    @Html.DisplayNameFor(model => model.People.First().SHORT_NAME)
                                </samp>
                            </th>
                            <th>
                                <samp class="form-group">
                                    @Html.DisplayNameFor(model => model.People.First().USER_NO)
                                </samp>
                            </th>
                            <th>
                                <samp class="form-group">
                                    @Html.DisplayNameFor(model => model.People.First().NAME)
                                </samp>
                            </th>

                            <th>
                                <samp class="form-group">
                                    身份
                                </samp>
                            </th>

                            <th>
                                <samp class="form-group">
                                    @Html.DisplayNameFor(model => model.People.First().CLASS_NO)
                                </samp>
                            </th>
                            <th>
                                <samp class="form-group">
                                    @Html.DisplayNameFor(model => model.People.First().SEAT_NO)
                                </samp>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.People.Where(a => a.LOTTERY_NO == Main.LOTTERY_NO).Count() > 0)
                        {
                            foreach (var item in Model.People.Where(a => a.LOTTERY_NO == Main.LOTTERY_NO))
                            {
                                <tr>
                                    <td>
                                        @if (item.GAME_USER_TYPE == UserType.Student)
                                        {
                                            @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                        }
                                        else
                                        {
                                            <text>臨時卡/入場券</text>
                                        }
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.USER_NO)
                                    </td>
                                    <td>
                                        @if (string.IsNullOrWhiteSpace(item.SHORT_NAME))
                                        {
                                            if (item.NAME.IndexOf(item.GAME_USER_ID) == -1)
                                            {
                                                @StringHelper.MaskName(item.NAME)
                                            }
                                            else
                                            {
                                                @Html.DisplayFor(modelItem => item.NAME)
                                            }
                                        }
                                        else
                                        {
                                            @StringHelper.MaskName(item.NAME)
                                        }
                                    </td>

                                    <td>
                                        @if (string.IsNullOrWhiteSpace(item.GAME_USER_TYPE_DESC))
                                        {
                                            @UserType.GetDesc(item.GAME_USER_TYPE)
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.GAME_USER_TYPE_DESC)
                                        }
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>

        }
    }
    else
    {
        <h3 class="text-center">抱歉！查無資料</h3>
    }

}

<script>
    $(document).ready(function () {
        $('#CashSearch_WhereSCHOOL_NO').selectpicker();

        $(document).on('keypress', function (e) {
            if (e.which == 13) {
                FunPageProc(1)
            }
        });
    });
</script>