/*
 *  /MathJax/jax/input/MathML/entities/o.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(a){MathJax.Hub.Insert(a.Parse.Entity,{OElig:"\u0152",Oacute:"\u00D3",Ocirc:"\u00D4",Ocy:"\u041E",Odblac:"\u0150",Ograve:"\u00D2",Omacr:"\u014C",Omicron:"\u039F",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",Or:"\u2A54",Oslash:"\u00D8",Otilde:"\u00D5",Otimes:"\u2A37",Ouml:"\u00D6",OverBracket:"\u23B4",OverParenthesis:"\u23DC",oS:"\u24C8",oacute:"\u00F3",oast:"\u229B",ocir:"\u229A",ocirc:"\u00F4",ocy:"\u043E",odash:"\u229D",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",oelig:"\u0153",ofcir:"\u29BF",ogon:"\u02DB",ograve:"\u00F2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",omacr:"\u014D",omid:"\u29B6",ominus:"\u2296",opar:"\u29B7",operp:"\u29B9",oplus:"\u2295",orarr:"\u21BB",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\u00AA",ordm:"\u00BA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oslash:"\u00F8",otilde:"\u00F5",otimes:"\u2297",otimesas:"\u2A36",ouml:"\u00F6",ovbar:"\u233D"});MathJax.Ajax.loadComplete(a.entityDir+"/o.js")})(MathJax.InputJax.MathML);
