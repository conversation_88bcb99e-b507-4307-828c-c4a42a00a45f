﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'cs', {
	border: 'Ohraničení',
	caption: 'Pop<PERSON>',
	cell: {
		menu: 'Buňka',
		insertBefore: 'V<PERSON><PERSON>it buňku před',
		insertAfter: 'Vlo<PERSON>it buňku za',
		deleteCell: '<PERSON>mazat buňky',
		merge: 'Sloučit buňky',
		mergeRight: 'Sloučit doprava',
		mergeDown: 'Sloučit dolů',
		splitHorizontal: '<PERSON>ozd<PERSON><PERSON> buňky vodorovně',
		splitVertical: 'Rozd<PERSON><PERSON> buňky svisle',
		title: '<PERSON>lastnosti buňky',
		cellType: 'Typ buňky',
		rowSpan: 'Spojit řádky',
		colSpan: 'Spojit sloupce',
		wordWrap: 'Zalamován<PERSON>',
		hAlign: 'Vod<PERSON><PERSON><PERSON><PERSON> zarov<PERSON>',
		vAlign: '<PERSON>vis<PERSON> zaro<PERSON>nání',
		alignBaseline: 'Na úča<PERSON>',
		bgColor: '<PERSON>va pozadí',
		borderColor: 'Barva okraje',
		data: 'Data',
		header: 'Hlavička',
		yes: 'Ano',
		no: 'Ne',
		invalidWidth: 'Šířka buňky musí být číslo.',
		invalidHeight: 'Zadaná výška buňky musí být číslená.',
		invalidRowSpan: 'Zadaný počet sloučených řádků musí být celé číslo.',
		invalidColSpan: 'Zadaný počet sloučených sloupců musí být celé číslo.',
		chooseColor: 'Výběr'
	},
	cellPad: 'Odsazení obsahu v buňce',
	cellSpace: 'Vzdálenost buněk',
	column: {
		menu: 'Sloupec',
		insertBefore: 'Vložit sloupec před',
		insertAfter: 'Vložit sloupec za',
		deleteColumn: 'Smazat sloupec'
	},
	columns: 'Sloupce',
	deleteTable: 'Smazat tabulku',
	headers: 'Záhlaví',
	headersBoth: 'Obojí',
	headersColumn: 'První sloupec',
	headersNone: 'Žádné',
	headersRow: 'První řádek',
	invalidBorder: 'Zdaná velikost okraje musí být číselná.',
	invalidCellPadding: 'Zadané odsazení obsahu v buňce musí být číselné.',
	invalidCellSpacing: 'Zadaná vzdálenost buněk musí být číselná.',
	invalidCols: 'Počet sloupců musí být číslo větší než 0.',
	invalidHeight: 'Zadaná výška tabulky musí být číselná.',
	invalidRows: 'Počet řádků musí být číslo větší než 0.',
	invalidWidth: 'Šířka tabulky musí být číslo.',
	menu: 'Vlastnosti tabulky',
	row: {
		menu: 'Řádek',
		insertBefore: 'Vložit řádek před',
		insertAfter: 'Vložit řádek za',
		deleteRow: 'Smazat řádky'
	},
	rows: 'Řádky',
	summary: 'Souhrn',
	title: 'Vlastnosti tabulky',
	toolbar: 'Tabulka',
	widthPc: 'procent',
	widthPx: 'bodů',
	widthUnit: 'jednotka šířky'
} );
