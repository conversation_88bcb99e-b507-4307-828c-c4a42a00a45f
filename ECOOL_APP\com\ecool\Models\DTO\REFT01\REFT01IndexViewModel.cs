﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class REFT01IndexViewModel
    {
        /// <summary>
        /// 原視窗按鈕ID
        /// </summary>
        public string BTN_ID { get; set; }


        /// <summary>
        /// 來源TABLE
        /// </summary>
        public string REF_TABLE { get; set; }

        /// <summary>
        /// 來源KEY 
        /// </summary>
        public string REF_KEY { get; set; }

        /// <summary>
        /// 原視窗 REF_KEY input id
        /// </summary>
        public string REF_KEY_ID { get; set; }
        


        /// <summary>
        /// TABLE 資料狀態 
        /// </summary>
        public byte? STATUS { get; set; }

        /// <summary>
        /// 抬頭 
        /// </summary>
        public string Panel_Title { get; set; }


        public string DATA_TYPE { get; set; }
    }
}
