﻿@{
    ViewBag.Title = "e酷幣首頁";
    Layout = "~/Views/Shared/_LayoutWebView.cshtml";
}

@Html.Partial("_Notice")

@Html.Partial("_BET02Partial")

<br />
@Html.Action("_ImgPlay", "ZZZI25")

<br />
<style>
    .modal-body {
        position: relative;
        padding: 10px;
    }
</style>
@*<div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">酷幣主機停機(暫停服務)公告：</h4>
                </div>
                <div class="modal-body" id="remind-content">
                    <ol>
                        配合市政府機電檢修停電，酷幣主機預計計於109年1月17日(星期五)下午7時至109年1月19日(星期日)上午9時止暫停服務，不便之處，請包涵。
                    </ol>
                </div>
            </div>
        </div>
    </div>*@
@if (ViewBag.GetDataListShowYN == true || ViewBag.GetALLDataListShowYN == true)
{
    if (ViewBag.RemindItems != null || ViewBag.ALLRemindItems != null)
    {
        <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">

            <div class="modal-dialog modal-lg" role="document" >
                <div class="modal-content">
                    @if (ViewBag.GetALLDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.ALLRemindItems as List<BDMT02_REF>)
                        {
                            var str = "";
                            str = "https://line.me/R/ti/p/@101hrbss";
                            string str2 = "";
                            str2 = "https://www.youtube.com/watch?v=3m3RLukLqSU";
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header" style="background-color:#ffcece">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003" && item.CONTENT_TXT == "【觀看說明影片】")
                                        {

                                        }
                                        else
                                        {
                                            @item.CONTENT_TXT

                                        }
                                        <br />
                                        @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003")
                                        {

                                            <a href="@str2">【觀看說明影片】</a><br />
                                            @:客服連結<a href="@str">@str</a>
                                        }

                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">
                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003" && item.CONTENT_TXT == "【觀看說明影片】")
                                    {

                                    }
                                    else
                                    {
                                        @item.CONTENT_TXT

                                    }
                                    <br />
                                    @if (item.SCHOOL_NO == "ALL")
                                    {



                                        <a href="@str2">【觀看說明影片】</a><br />

                                        <br />
                                        @:客服連結<a href="@str">@str</a>
                                    }
                                </div>
                            }
                        }
                    }
                    @if (ViewBag.GetDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.RemindItems as List<BDMT02_REF>)
                        {
                            var str = "";
                            str = "https://line.me/R/ti/p/@101hrbss";
                            string str2 = "";
                            str2 = "https://www.youtube.com/watch?v=3m3RLukLqSU";
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header"  style="background-color:#ffcece">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003" && item.CONTENT_TXT == "【觀看說明影片】")
                                        {

                                        }
                                        else
                                        {
                                            @item.CONTENT_TXT

                                        }
                                        <br />
                                        @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003")
                                        {



                                            <a href="@str2">【觀看說明影片】</a><br /><br />

                                        
                                            @:客服連結<a href="@str">@str</a>
                                        }

                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">

                                    @item.CONTENT_TXT

                                    <br /> @if (item.SCHOOL_NO == "ALL")
                                    {

                                     
                                        @:客服連結<a href="@str">@str</a>
                                    }
                                </div>
                            }

                        }
                    }
                </div>
            </div>
        </div>
    }

}
<div class="row text-center">
    @{
        List<ADDV01> ADDV01List = ViewBag.ADDV01List;
        List<HRMT01QTY> ADDT09List = ViewBag.ADDT09List;
        List<HRMT01QTY> AWAT01List = ViewBag.AWAT01List;
    }

    <div class="text-center" style="width:95%;margin: 0px auto; ">
        <div class="Div-bar-reader App_show">
            閱讀認證排行榜
        </div>
        @if (ADDT09List != null)
        {
            <table class="table-ecool table-hover table-ecool-reader" style="width:90%;margin: 0px auto; ">
                <thead>
                    <tr style="background-color: rgba(227, 236, 159, 0.2);">
                        <th>
                            @Html.DisplayNameFor(m => ADDT09List.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(m => ADDT09List.First().NAME)
                        </th>
                        <th>
                            閱讀冊數
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (ADDT09List.Count > 0)
                    {
                        foreach (var item in ADDT09List)
                        {
                            <tr>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.QTY)
                                </td>
                            </tr>
                        }
                    }
                    <tr>
                        <td align="right" colspan="3">
                            <u>
                                @Html.ActionLink("more", "../ADDT/ADDTList", "ADDT", new { @class = "btn btn-link-ez btn-xs" })
                            </u>
                        </td>
                    </tr>
                </tbody>
            </table>
        }
    </div>
    <br />
    <br />

    <div class="text-center" style="width:95%;margin: 0px auto; ">
        <div class="Div-bar-List App_show">
            線上投稿排行榜
        </div>
        @if (ADDV01List != null)
        {

            <table class="table-ecool table-hover table-ecool-List" style="width:90%;margin: 0px auto; ">
                <thead>
                    <tr style="background-color: rgba(232, 209, 230, 0.2);">
                        <th>
                            @Html.DisplayNameFor(m => ADDV01List.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(m => ADDV01List.First().NAME)
                        </th>
                        <th>
                            投稿數
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (ADDV01List.Count > 0)
                    {
                        foreach (var item in ADDV01List)
                        {
                            <tr>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.WRITING_QTY)
                                </td>
                            </tr>
                        }
                    }
                    <tr>
                        <td align="right" colspan="3">
                            <u>
                                @Html.ActionLink("more", "../ADDI01/OrderList", "ADDI01", new { @class = "btn btn-link-ez btn-xs" })
                            </u>
                        </td>
                    </tr>
                </tbody>
            </table>
        }
    </div>
    <br />
    <br />

    <div class="text-center" style="width:95%;margin: 0px auto; ">
        <div class="Div-bar-AWA003 App_show">
            酷幣總數排行榜
        </div>
        @if (AWAT01List != null)
        {

            <table class="table-ecool table-hover table-ecool-AWA003" style="width:90%;margin: 0px auto; ">
                <thead>
                    <tr style="background-color: rgba(253, 230, 128, 0.2);">
                        <th>
                            @Html.DisplayNameFor(m => AWAT01List.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(m => AWAT01List.First().NAME)
                        </th>
                        <th>
                            酷幣點數
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (AWAT01List.Count > 0)
                    {
                        foreach (var item in AWAT01List)
                        {
                            <tr>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CASH_ALL)
                                </td>
                            </tr>
                        }
                    }
                    <tr>
                        <td align="right" colspan="3">
                            <u>
                                @Html.ActionLink("more", "../AWA003/QUERY", "AWA003", new { @class = "btn btn-link-ez btn-xs" })
                            </u>
                        </td>
                    </tr>
                </tbody>
            </table>
        }
    </div>
</div>
@section scripts{
    <script>
        window.onload = function () {
            RemindShow()
        }
        function RemindShow() {
            var remind_font = $("#remind-content").text().length;

            if (remind_font > 0) {
                $('#remind-modal').modal('show');
            }
        }
    </script>

}