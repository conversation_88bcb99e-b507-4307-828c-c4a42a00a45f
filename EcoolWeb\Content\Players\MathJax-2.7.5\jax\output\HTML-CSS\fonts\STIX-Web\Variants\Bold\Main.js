/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Variants/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Variants-bold"]={directory:"Variants/Bold",family:"STIXMathJax_Variants",weight:"bold",testString:"\u00A0\u019B\u2032\u2033\u2034\u2035\u2036\u2037\u2057\u2140\u2190\u2191\u2192\u2193\u21D1",32:[0,0,250,0,0],124:[691,189,340,126,214],160:[0,0,250,0,0],411:[666,0,536,60,526],8242:[586,-12,394,44,350],8243:[586,-12,713,44,669],8244:[586,-12,1032,44,988],8245:[586,-12,394,44,350],8246:[586,-12,713,44,669],8247:[586,-12,1032,44,988],8279:[586,-12,1351,43,1306],8512:[691,0,780,55,725],8592:[451,-55,428,68,428],8593:[680,15,556,80,476],8594:[451,-55,428,0,360],8595:[680,15,556,80,476],8657:[600,15,714,40,674],8659:[600,15,714,40,674],8709:[729,74,584,36,548],8719:[676,0,734,27,707],8720:[676,0,734,27,707],8721:[676,0,690,39,649],8726:[732,193,518,45,473],8730:[943,-28,800,112,844],8733:[431,0,750,56,687],8739:[451,19,290,89,201],8747:[824,320,425,59,467],8748:[824,320,715,59,757],8749:[824,320,1005,59,1047],8750:[834,310,394,35,483],8751:[824,320,650,35,739],8752:[824,320,951,54,1047],8753:[824,320,484,54,553],8754:[824,320,445,35,534],8755:[824,320,456,35,545],8772:[543,45,750,68,683],8775:[648,144,750,68,683],8777:[598,64,750,68,683],8800:[687,183,750,68,682],8802:[747,243,750,68,682],8808:[728,293,750,80,670],8809:[728,293,750,80,670],8814:[672,166,750,80,670],8815:[672,166,750,80,670],8816:[742,236,750,80,670],8817:[742,236,750,80,670],10764:[824,320,1295,59,1337],10765:[824,320,511,59,553],10766:[824,320,511,59,553],10767:[824,320,592,59,634],10768:[824,320,385,35,474],10769:[824,320,484,54,553],10770:[824,320,417,35,486],10771:[824,320,424,54,493],10772:[824,320,535,54,604],10773:[824,320,416,35,505],10774:[824,320,459,35,528],10775:[824,320,824,45,884],10776:[824,320,527,45,587],10777:[824,320,567,45,632],10778:[824,320,567,45,632],10779:[959,320,479,45,521],10780:[824,455,411,35,511],57955:[422,10,523,26,496],57959:[425,0,523,111,420],57963:[421,0,523,53,470],57967:[424,198,523,31,478],57971:[420,198,523,42,496],57975:[421,198,523,49,474],57979:[614,8,523,21,502],57983:[421,198,523,8,507],57987:[606,12,523,31,493],57991:[421,202,523,25,499]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Variants-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Variants/Bold/Main.js"]);
