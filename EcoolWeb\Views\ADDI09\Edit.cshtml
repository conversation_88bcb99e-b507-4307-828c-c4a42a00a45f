﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO;
@using ECOOL_APP;
@using EcoolWeb.Models;
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;
    UserProfile user = UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })

@Html.ActionLink("回「新增批次給點/扣點」首頁", (string)ViewBag.IndexActionName, null, new { @class = "btn btn-sm btn-sys", @role = "button" })

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            @Html.ValidationSummary()
        </div>
    }

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.Hidden("ADDT14_STYLE", (string)ViewBag.ADDT14_STYLE)
    @Html.Hidden("Individual_Give", (bool)ViewBag.Individual_Give)

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            @if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER
|| ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
            {
                @Html.Action("_ViewOTHER", (string)ViewBag.BRE_NO, Model)
            }
            else if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_LEADER_BATCH)
            {
                @Html.Action("_ViewLEADER", (string)ViewBag.BRE_NO, Model)
            }
            else if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
            {
                @Html.Action("_ViewIN", (string)ViewBag.BRE_NO, Model)
            }
            else if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
            {
                @Html.Action("_ViewOBC", (string)ViewBag.BRE_NO, Model)
            }
            else if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
            {
                @Html.Action("_ViewGiveTeacher", (string)ViewBag.BRE_NO, Model)
            }
            else if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY)
            {
                @Html.Action("_ViewLuckyPoint", (string)ViewBag.BRE_NO, Model)
            }
            else if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH)
            {
                @Html.Action("_ViewIN", (string)ViewBag.BRE_NO, Model)
            }
            else if (ViewBag.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
            {
                @Html.Action("_ViewOTHER", (string)ViewBag.BRE_NO, Model)
            }
            <div>
                @{
                    bool give = false;

                    if (!string.IsNullOrEmpty(Request.Params["Individual_Give"]))
                    {
                        give = Convert.ToBoolean(Request.Params["Individual_Give"]);
                    }

                    if (!Convert.ToBoolean(give))
                    {
                        //暫不開放個人給點
                        @*@Html.ActionLink("開啟個別給點", "Edit", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE, Individual_Give = true }, new { @class = "btn btn-default", @role = "button" })*@
                    }
                    else
                    {
                        @Html.ActionLink("一般給點", "Edit", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE, Individual_Give = false }, new { @class = "btn btn-default", @role = "button" })

                    }
                }
            </div>
        </div>
    </div>
    <div id="QuerySelectDataList">
        @Html.ValidationMessage("DataList", new { @class = "text-danger" })
        @Html.Action("_QuerySelectDataList", (string)ViewBag.BRE_NO, new { ShowType = ViewContext.RouteData.Values["action"] })
    </div>
    <div style="height:20px"></div>
    <div class="text-center">
        @*@Html.ActionLink("回選擇「學號」輸入模式", "Index", new { SYS_TABLE_TYPE = ViewBag.ADDT14_STYLE , ADDT14_STYLE = ViewBag.ADDT14_STYLE }, new { @class = "btn btn-default", @role = "button" })*@

        @Html.ActionLink("重選人員", "QuerySelectView", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE, @DataType = "reelect" }, new { @class = "btn btn-default", @role = "button" })

        <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
            確定給點
        </button>
    </div>
    <div id="areapoint"></div>
}

@{

    ADDI09SelectListViewModel temp = new ADDI09SelectListViewModel();

}
    <script type="text/javascript">
        @*$(document).ready(function () {
     $("#DivSelectData tbody tr").each(function (index) {
            var str0 = "";
            var str = "";
            var str1 = "";
            var str2 = "";
            var str3 = "";
            var str4= "";
            if ($(this).find('td').eq(0).text() != null) {
                str0 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].Chk\" value=\"" + "false" + "\" /><input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].SCHOOL_NO\" value=\"" + @user.SCHOOL_NO + "\" />";
                str = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].USER_NO\" value=\"" + $(this).find('td').eq(0).text().trim() + "\" />";
                str1 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].SNAME\" value=\"" + $(this).find('td').eq(1).text().trim() + "\" />";
                str2 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].NAME\" value=\"" + $(this).find('td').eq(1).text().trim() + "\" />";
                str3 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].CLASS_NO\" value=\"" + $(this).find('td').eq(2).text().trim() + "\" />";
                str4 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].SEAT_NO\" value=\"" + $(this).find('td').eq(3).text().trim() + "\" />";

                $("#areapoint").append(str0 + str + str1 + str2 + str3 + str4);


                }

                }); 
        });*@
    $('#myButton').on('click', function () {

        var $btn = $(this).button('loading....')
     
        form1.submit();
    });
    </script>