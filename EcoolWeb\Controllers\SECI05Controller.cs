﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System.Collections.Generic;
using System.Web.Mvc;
using EcoolWeb.Util;
using ECOOL_APP.com.ecool.service;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using static ECOOL_APP.EF.HRMT01;
using System;
using System.Net;
using ECOOL_APP.com.ecool.util;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using iTextSharp.tool.xml;
using System.Web;
using EcoolWeb.CustomAttribute;
using System.Linq;
using ECOOL_APP.com.ecool.Models.DTO;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class SECI05Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "SECI05";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private SECI05Service Service = new SECI05Service();

        public ActionResult Index(SECI05IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "學生閱讀狀況";
            this.Shared(ViewBag.Panel_Title);

            List<string> MyPanyStudent = new List<string>();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = user.CLASS_NO ?? user.TEACH_CLASS_NO;
            }

            if (user.USER_TYPE == UserType.Student)
            {
                model.WhereCLASS_NO = user.CLASS_NO;
                model.WhereUSER_NO = user.USER_NO;
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                var Hr06 = HRMT06.GetMyPanyStudent(user, db);

                if (Hr06 != null)
                {
                    if (Hr06.Count() > 0)
                    {
                        MyPanyStudent = Hr06.Select(a => a.STUDENT_USER_NO).ToList();
                    }
                }

                if (MyPanyStudent.Count >= 1 && string.IsNullOrWhiteSpace(model.WhereUSER_NO))
                {
                    model.WhereUSER_NO = MyPanyStudent.FirstOrDefault();
                }
            }

            List<SelectListItem> SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.WhereSCHOOL_NO, null, user, true, null, null, false);

            if (!HRMT24_ENUM.CheckQQutSchool(user))
            {
                SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == model.WhereSCHOOL_NO).ToList();
            }
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            if (!HRMT24_ENUM.CheckQAdmin(user))
            {
                if (user.TEACH_CLASS_NO == null)
                {
                    if (HRMT24_ENUM.IsOtherTeacher(user) == false)
                    {
                        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                    }
                }
                else
                {
                    ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                }
            }
            ViewBag.ClassItems = ClassItems;

            List<SelectListItem> USER_NOItems = null;

            if (user.USER_TYPE == UserType.Parents)
            {
                USER_NOItems = HRMT01.GetUserNoListDataForP(model.WhereUSER_NO, user, db).ToListNoLock();
            }
            else
            {
                USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db, true);
            }

            ViewBag.USER_NOItems = USER_NOItems;

            if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                model = Service.GetBorrowData(model, user, ref db);

                if (model.MyBorrow != null)
                {
                    model.BorrowColumnChart = GetBorrowColumnChart(model, 0, 0);
                    model.GradeQtyCharts = GetGradeQtyCharts(model.MyBorrow, 0, 0);
                }

                List<SelectListItem> GradeSeyearItem = new List<SelectListItem>();

                int InSchoolYear = Convert.ToInt32(model.WhereUSER_NO.Substring(0, 3));

                GradeSeyearItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSEYEAR) });

                foreach (var Item in Enum.GetValues(typeof(GradeVal)))
                {
                    GradeSeyearItem.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = (InSchoolYear + ((byte)Item - 1)).ToString(), Selected = (InSchoolYear + ((byte)Item - 1)).ToString() == model.WhereSEYEAR });
                }

                ViewBag.GradeSeyearItem = GradeSeyearItem;
            }

            return View(model);
        }

        public ActionResult _SEI05Partial1(SECI05IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            //  ViewBag.Panel_Title = "學生閱讀狀況";
            //  this.Shared(ViewBag.Panel_Title);

            user = UserProfileHelper.Get();
            List<string> MyPanyStudent = new List<string>();

            //if (user == null)
            //{
            //    return RedirectToAction("SessionTimeOutError", "Error");
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            //{
            //    model.WhereSCHOOL_NO = user.SCHOOL_NO;
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO ?? user.TEACH_CLASS_NO;
            //}

            //if (user.USER_TYPE == UserType.Student)
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO;
            //    model.WhereUSER_NO = user.USER_NO;
            //}
            //else if (user.USER_TYPE == UserType.Parents)
            //{
            //    var Hr06 = HRMT06.GetMyPanyStudent(user, db);

            //    if (Hr06 != null)
            //    {
            //        if (Hr06.Count() > 0)
            //        {
            //            MyPanyStudent = Hr06.Select(a => a.STUDENT_USER_NO).ToList();
            //        }
            //    }

            //    if (MyPanyStudent.Count >= 1 && string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            //    {
            //        model.WhereUSER_NO = MyPanyStudent.FirstOrDefault();
            //    }
            //}

            //List<SelectListItem> SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.WhereSCHOOL_NO, null, user, true, null, null, false);

            //if (!HRMT24_ENUM.CheckQQutSchool(user))
            //{
            //    SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == model.WhereSCHOOL_NO).ToList();
            //}
            //ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            //var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            //if (!HRMT24_ENUM.CheckQAdmin(user))
            //{
            //    if (user.TEACH_CLASS_NO == null)
            //    {
            //        if (HRMT24_ENUM.IsOtherTeacher(user) == false)
            //        {
            //            ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //        }
            //    }
            //    else
            //    {
            //        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //    }
            //}
            //ViewBag.ClassItems = ClassItems;

            //List<SelectListItem> USER_NOItems = null;

            //if (user.USER_TYPE == UserType.Parents)
            //{
            //    USER_NOItems = HRMT01.GetUserNoListDataForP(model.WhereUSER_NO, user, db).ToListNoLock();
            //}
            //else
            //{
            //    USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db, true);
            //}

            //ViewBag.USER_NOItems = USER_NOItems;

            if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                model = Service.GetBorrowData(model, user, ref db);

                if (model.MyBorrow != null)
                {
                    model.BorrowColumnChart = GetBorrowColumnChart(model, 750, 1000);
                    model.GradeQtyCharts = GetGradeQtyCharts(model.MyBorrow, 750, 1000);
                }

                List<SelectListItem> GradeSeyearItem = new List<SelectListItem>();

                int InSchoolYear = Convert.ToInt32(model.WhereUSER_NO.Substring(0, 3));

                GradeSeyearItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSEYEAR) });

                foreach (var Item in Enum.GetValues(typeof(GradeVal)))
                {
                    GradeSeyearItem.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = (InSchoolYear + ((byte)Item - 1)).ToString(), Selected = (InSchoolYear + ((byte)Item - 1)).ToString() == model.WhereSEYEAR });
                }

                ViewBag.GradeSeyearItem = GradeSeyearItem;
            }

            return View(model);
        }

        public ActionResult _SEI05Partial(SECI05IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            //  ViewBag.Panel_Title = "學生閱讀狀況";
            //  this.Shared(ViewBag.Panel_Title);

            user = UserProfileHelper.Get();
            List<string> MyPanyStudent = new List<string>();

            //if (user == null)
            //{
            //    return RedirectToAction("SessionTimeOutError", "Error");
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            //{
            //    model.WhereSCHOOL_NO = user.SCHOOL_NO;
            //}

            //if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO ?? user.TEACH_CLASS_NO;
            //}

            //if (user.USER_TYPE == UserType.Student)
            //{
            //    model.WhereCLASS_NO = user.CLASS_NO;
            //    model.WhereUSER_NO = user.USER_NO;
            //}
            //else if (user.USER_TYPE == UserType.Parents)
            //{
            //    var Hr06 = HRMT06.GetMyPanyStudent(user, db);

            //    if (Hr06 != null)
            //    {
            //        if (Hr06.Count() > 0)
            //        {
            //            MyPanyStudent = Hr06.Select(a => a.STUDENT_USER_NO).ToList();
            //        }
            //    }

            //    if (MyPanyStudent.Count >= 1 && string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            //    {
            //        model.WhereUSER_NO = MyPanyStudent.FirstOrDefault();
            //    }
            //}

            //List<SelectListItem> SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.WhereSCHOOL_NO, null, user, true, null, null, false);

            //if (!HRMT24_ENUM.CheckQQutSchool(user))
            //{
            //    SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == model.WhereSCHOOL_NO).ToList();
            //}
            //ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            //var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            //if (!HRMT24_ENUM.CheckQAdmin(user))
            //{
            //    if (user.TEACH_CLASS_NO == null)
            //    {
            //        if (HRMT24_ENUM.IsOtherTeacher(user) == false)
            //        {
            //            ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //        }
            //    }
            //    else
            //    {
            //        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
            //    }
            //}
            //ViewBag.ClassItems = ClassItems;

            //List<SelectListItem> USER_NOItems = null;

            //if (user.USER_TYPE == UserType.Parents)
            //{
            //    USER_NOItems = HRMT01.GetUserNoListDataForP(model.WhereUSER_NO, user, db).ToListNoLock();
            //}
            //else
            //{
            //    USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db, true);
            //}

            //ViewBag.USER_NOItems = USER_NOItems;

            if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                model = Service.GetBorrowData(model, user, ref db);

                if (model.MyBorrow != null)
                {
                    model.BorrowColumnChart = GetBorrowColumnChart(model, 600, 1800);
                    model.GradeQtyCharts = GetGradeQtyCharts(model.MyBorrow, 600, 1800);
                }

                List<SelectListItem> GradeSeyearItem = new List<SelectListItem>();

                int InSchoolYear = Convert.ToInt32(model.WhereUSER_NO.Substring(0, 3));

                GradeSeyearItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSEYEAR) });

                foreach (var Item in Enum.GetValues(typeof(GradeVal)))
                {
                    GradeSeyearItem.Add(new SelectListItem() { Text = ParserGrade((byte)Item), Value = (InSchoolYear + ((byte)Item - 1)).ToString(), Selected = (InSchoolYear + ((byte)Item - 1)).ToString() == model.WhereSEYEAR });
                }

                ViewBag.GradeSeyearItem = GradeSeyearItem;
            }

            return View(model);
        }

        /// <summary>
        /// 每月借書變化表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult MonthQty(SECI05MonthQtyIndexViewModel model)
        {
            this.Shared("每月借書變化表");
            string Strfrom = Request["from"] == null ? (model.fromStr == null ? null : model.fromStr) : Request["from"];

            ViewBag.from = Strfrom;
            if (model.Where_SCHOOLNO == null)
            {
                model.Where_SCHOOLNO = user.SCHOOL_NO;
            }

            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model.Where_SYEAR = SYear.ToString();
            }
            SECI07Controller sECI07Controller = new SECI07Controller();

            var SYearItems = sECI07Controller.GetSYearsItems(model.Where_SYEAR);

            if (SYearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_SYEAR = SYearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                SYearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_SYEAR;
                    return a;
                }).ToList();
            }

            ViewBag.SYearItems = SYearItems;

            if (string.IsNullOrWhiteSpace(model.Where_CLASS_NO))
            {
                model.Where_CLASS_NO = user.CLASS_NO ?? user.TEACH_CLASS_NO;
            }

            var ClassItems = HRMT01.GetClassListData(model.Where_SCHOOLNO, null, model.Where_CLASS_NO, ref db).Where(a => a.Value != string.Empty).ToList();
            if (!HRMT24_ENUM.CheckQAdmin(user))
            {
                if (user.TEACH_CLASS_NO == null)
                {
                    if (HRMT24_ENUM.IsOtherTeacher(user) == false)
                    {
                        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                    }
                }
                else
                {
                    ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                }
            }

            if (ClassItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_CLASS_NO = ClassItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                ClassItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_CLASS_NO;
                    return a;
                }).ToList();
            }

            ViewBag.ClassItems = ClassItems;

            if (!string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model = Service.GetMonthQty(model, ref db);
                if (model.MonthQtyList != null && model.MonthQtyList.Count > 0)
                {
                    model.MonthQtyList = model.MonthQtyList.OrderBy(a => a.RET_YYMM).ToList();
                }
                model.MonthQtycharts = GetMonthcharts(model);
            }

            return View(model);
        }

        /// <summary>
        /// 鼓勵名單: 本學期借書量後30%
        /// </summary>
        /// <returns></returns>
        public ActionResult _EncourageDiv(SECI05EncourageViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "閱讀加油名單";
            this.Shared(ViewBag.Panel_Title);

            var classItem = GetClassItem(model.WhereSCHOOL_NO, model.WhereCLASS_NO);

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }
            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = classItem.FirstOrDefault()?.Value;
            }

            ViewBag.ClassItems = GetClassItem(model.WhereSCHOOL_NO, model.WhereCLASS_NO);

            model = Service.GetBorrowEncourageList(model, 30, ref db);

            return View(model);
        }

        /// <summary>
        /// 閱讀偏食: 單一類別80%以上
        /// </summary>
        /// <returns></returns>
        public ActionResult _ReadingPartialEclipseDiv(SECI05ReadingPartialViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "疑似閱讀偏食名單";
            this.Shared(ViewBag.Panel_Title);

            var classItem = GetClassItem(model.WhereSCHOOL_NO, model.WhereCLASS_NO);

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }
            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = classItem.FirstOrDefault()?.Value;
            }

            ViewBag.ClassItems = GetClassItem(model.WhereSCHOOL_NO, model.WhereCLASS_NO);

            model = Service.GetReadingPartialEclipseList(model, 80, ref db);

            return View(model);
        }

        /// <summary>
        /// 異常名單: 三個月沒有借書
        /// </summary>
        /// <returns></returns>
        public ActionResult _UnusualListDiv(SECI05UnusualViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "異常名單";
            this.Shared(ViewBag.Panel_Title);

            var classItem = GetClassItem(model.WhereSCHOOL_NO, model.WhereCLASS_NO);

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }
            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = classItem.FirstOrDefault()?.Value;
            }

            ViewBag.ClassItems = GetClassItem(model.WhereSCHOOL_NO, model.WhereCLASS_NO);

            model = Service.GetUnusualList(model, 3, ref db);

            return View(model);
        }

        public ActionResult BorrowList(SECI05BorrowListIndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我的借書情形";
            this.Shared(ViewBag.Panel_Title);

            if (user == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            model = Service.GetBorrowListData(model, user, ref db);

            return View(model);
        }

        public ActionResult TypeList(SECI05TypeListIndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "各類別借書量";
            this.Shared(ViewBag.Panel_Title);

            if (user == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            model = Service.GetTypeListData(model, user, ref db);

            return View(model);
        }

        [HttpPost]
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }

            var UserNoListData = HRMT01.GetUserNoListData(SCHOOL_NO, CLASS_NO, ref db);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, USER_NO, true, null);

            return Content(_html);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        /// <summary>
        /// 產生 每月借書變化表
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetMonthcharts(SECI05MonthQtyIndexViewModel Data)
        {
            if (Data.MonthQtyList.Count == 0)
            {
                return null;
            }

            Highcharts Monthchart = new Highcharts("Monthchart");

            var TEachMonth = Data.MonthQtyList.Select(a => DateHelper.ToSimpleTaiwanDateCYYMM(a.RET_YYMM, "-")).ToArray();

            var ArrClassQty = (from a in Data.MonthQtyList
                               select a.CLASS_SUM).ToArray().Cast<object>().ToArray();

            var ArrSchoolQty = (from a in Data.MonthQtyList
                                select a.SCHOOL_SUM).ToArray().Cast<object>().ToArray();

            var ArrAvgQty = (from a in Data.MonthQtyList
                             select a.BORROW_BOOK_AVG).ToArray().Cast<object>().ToArray();

            Monthchart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = "每月借書變化表", X = 0 })
            .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年月" }, Categories = TEachMonth })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "筆數" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "筆" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 {
                                   Name =  $"{Data.Where_CLASS_NO}班",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrClassQty)
                                 },
                                 new Series
                                 {
                                   Name ="全校",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrSchoolQty)
                                 }
                                 ,
                                 new Series
                                 {
                                   Name ="平均",
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrAvgQty)
                                 }
                        }
            );

            chartsHelper.SetCopyright(Monthchart);

            return Monthchart;
        }

        #region 借閱類別統計表 長條圖

        /// <summary>
        ///  借閱類別統計表
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetBorrowColumnChart(SECI05IndexViewModel model, int HightNum, int WidthNum)
        {
            var returnPoint = new List<Point>();

            foreach (var item in model.BorrowTypeQty)
            {
                returnPoint.Add(new Point
                {
                    Y = item.QTY
                    ,
                    Name = item.TYPE_NAME
                });
            }

            Data data = new Data(returnPoint.ToArray());
            if (HightNum > 0)
            {
                Highcharts TempPreColumnChart = new Highcharts("TempBorrowColumnChart")
        .InitChart(new Chart
        {
            DefaultSeriesType = ChartTypes.Column,

            Height = HightNum,
            Width = WidthNum
        })
        .SetTitle(new Title { Text = "借閱類別統計表" })
        .SetXAxis(new XAxis
        {
            Type = AxisTypes.Category
                     ,
            Labels = new XAxisLabels
            {
                Rotation = -45,
                Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
            }
        })
        .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借閱數量" }, AllowDecimals = false })
        .SetSeries(new[]
                {
                        new Series {
                            Data = data,
                            Name = "借閱數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                })
        .SetPlotOptions(new PlotOptions
        {
            Column = new PlotOptionsColumn
            {
                DataLabels = new PlotOptionsColumnDataLabels
                {
                    Enabled = true,
                    Rotation = 0,
                    Color = System.Drawing.Color.Black,
                    Format = "{point.y:.1f}",
                    Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                }
            }
        })
        .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
        .SetLegend(new Legend { Enabled = false });
                chartsHelper.SetCopyright(TempPreColumnChart);

                return TempPreColumnChart;
            }
            else
            {
                Highcharts TempPreColumnChart = new Highcharts("TempBorrowColumnChart")
               .InitChart(new Chart
               {
                   DefaultSeriesType = ChartTypes.Column,
               })
               .SetTitle(new Title { Text = "借閱類別統計表" })
               .SetXAxis(new XAxis
               {
                   Type = AxisTypes.Category
                            ,
                   Labels = new XAxisLabels
                   {
                       Rotation = -45,
                       Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
                   }
               })
               .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借閱數量" }, AllowDecimals = false })
               .SetSeries(new[]
                       {
                        new Series {
                            Data = data,
                            Name = "借閱數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                       })
               .SetPlotOptions(new PlotOptions
               {
                   Column = new PlotOptionsColumn
                   {
                       DataLabels = new PlotOptionsColumnDataLabels
                       {
                           Enabled = true,
                           Rotation = 0,
                           Color = System.Drawing.Color.Black,
                           Format = "{point.y:.1f}",
                           Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                       }
                   }
               })
               .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
               .SetLegend(new Legend { Enabled = false });
                chartsHelper.SetCopyright(TempPreColumnChart);

                return TempPreColumnChart;
            }
        }

        #endregion 借閱類別統計表 長條圖

        /// <summary>
        /// 產生 個人借閱數量趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetGradeQtyCharts(SECI05MyBorrowViewModel Data, int HightNum, int WidthNum)
        {
            if (Data == null)
            {
                return null;
            }

            List<int> GradeQtys = new List<int>();
            GradeQtys.Add(Data.QTY_GRADE_1);
            GradeQtys.Add(Data.QTY_GRADE_2);
            GradeQtys.Add(Data.QTY_GRADE_3);
            GradeQtys.Add(Data.QTY_GRADE_4);
            GradeQtys.Add(Data.QTY_GRADE_5);
            GradeQtys.Add(Data.QTY_GRADE_6);

            List<string> Grades = new List<string>();
            foreach (var Item in Enum.GetValues(typeof(GradeVal)))
            {
                Grades.Add(ParserGrade((byte)Item));
            }
            if (HightNum > 0)
            {
                Highcharts GradeQtyChart = new Highcharts("GradeQtyChart");

                GradeQtyChart
                .InitChart(new DotNet.Highcharts.Options.Chart
                {
                    DefaultSeriesType = ChartTypes.Line,
                    Height = HightNum,
                    Width = WidthNum
                })
                .SetTitle(new Title { Text = "個人借閱數量趨示圖", X = 0 })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = Grades.ToArray() })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "本" }, Min = 0 })
                .SetPlotOptions(new PlotOptions
                {
                    Line = new PlotOptionsLine
                    {
                        DataLabels = new PlotOptionsLineDataLabels
                        {
                            Enabled = true
                        },
                        EnableMouseTracking = false
                    }
                })
                .SetTooltip(new Tooltip { ValueSuffix = "本" })
                .SetSeries(new Series[]
                            {
                                 new Series
                                 {
                                     Name ="各年級借閱數量",
                                   Data = new DotNet.Highcharts.Helpers.Data( GradeQtys.Cast<object>().ToArray())
                                 }
                            }
                );

                chartsHelper.SetCopyright(GradeQtyChart);

                return GradeQtyChart;
            }
            else
            {
                Highcharts GradeQtyChart = new Highcharts("GradeQtyChart");

                GradeQtyChart
                .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
                .SetTitle(new Title { Text = "個人借閱數量趨示圖", X = 0 })
                .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "年級" }, Categories = Grades.ToArray() })
                .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "本" }, Min = 0 })
                .SetPlotOptions(new PlotOptions
                {
                    Line = new PlotOptionsLine
                    {
                        DataLabels = new PlotOptionsLineDataLabels
                        {
                            Enabled = true
                        },
                        EnableMouseTracking = false
                    }
                })
                .SetTooltip(new Tooltip { ValueSuffix = "本" })
                .SetSeries(new Series[]
                            {
                                 new Series
                                 {
                                     Name ="各年級借閱數量",
                                   Data = new DotNet.Highcharts.Helpers.Data( GradeQtys.Cast<object>().ToArray())
                                 }
                            }
                );

                chartsHelper.SetCopyright(GradeQtyChart);

                return GradeQtyChart;
            }
        }

        #region Shared

        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;

            string Temp_Panel_Title = Bre_Name;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                Temp_Panel_Title = Temp_Panel_Title + " - " + Panel_Title;
            }
            ViewBag.Panel_Title = Temp_Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        private List<SelectListItem> GetClassItem(string schoolno, string classno)
        {
            #region 班級下拉 - 以及限制老師班級

            var ClassItems = HRMT01.GetClassListData(schoolno, null, classno, ref db);
            if (!HRMT24_ENUM.CheckQAdmin(user))
            {
                if (user.TEACH_CLASS_NO == null)
                {
                    if (HRMT24_ENUM.IsOtherTeacher(user) == false)
                    {
                        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                    }
                }
                else
                {
                    ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                }
            }
            return ClassItems;

            #endregion 班級下拉 - 以及限制老師班級
        }

        #endregion Shared
    }
}