// Modern jQuery code for ADDI01 Create page
$(document).ready(function() {
    // 設備類型檢測
    const deviceDetection = {
        init: function() {
            this.deviceType = this.judgeDeviceType();
            this.handleIOSScrolling();
        },

        judgeDeviceType: function() {
            const ua = window.navigator.userAgent.toLocaleLowerCase();
            const isIOS = /iphone|ipad|ipod/.test(ua);
            const isAndroid = /android/.test(ua);
            return { isIOS: isIOS, isAndroid: isAndroid };
        },

        handleIOSScrolling: function() {
            if (this.deviceType.isIOS) {
                document.body.scrollTop = document.documentElement.scrollTop = 0;
            }
        }
    };

    // YouTube URL 檢查功能
    const youtubeChecker = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $('#CheckYoutube').on('click', this.checkYoutubeUrl.bind(this));
        },

        checkYoutubeUrl: function() {
            const youtubeUrl = $('#YOUTUBE_URL').val();
            
            $.ajax({
                url: window.ADDI01_URLS.getUrlArgument,
                type: 'post',
                data: { StrUrl: youtubeUrl },
                dataType: 'json',
                cache: false,
                success: this.handleYoutubeResponse.bind(this),
                error: this.handleYoutubeError.bind(this)
            });
        },

        handleYoutubeResponse: function(data) {
            try {
                const res = jQuery.parseJSON(data);
                
                if (res.Success == 0) {
                    $('#YOUTUBE_URL').val('');
                    alert(res.Error);
                } else if (res.Success == 1) {
                    alert('正確');
                    $('#YOUTUBE_URL').val(res.EmbedYouTubeUrl);
                }
            } catch (e) {
                console.error('YouTube response parsing error:', e);
                alert('處理YouTube回應時發生錯誤');
            }
        },

        handleYoutubeError: function(xhr, err) {
            console.error('YouTube check error:', xhr, err);
            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
            alert("responseText: " + xhr.responseText);
        }
    };

    // 文字計數功能
    const textCounter = {
        init: function() {
            this.bindEvents();
            this.updateCount();
            // 將函數暴露到全局作用域供 HTML 調用
            window.KeyIn = this.updateCount.bind(this);
        },

        bindEvents: function() {
            $('#ARTICLE').on("keyup change", this.updateCount.bind(this));
        },

        updateCount: function() {
            const text = $("#ARTICLE").val();
            const textWithoutWhitespace = text.replace(/[\n\s]/g, "");
            const length = textWithoutWhitespace.length;
            $("#ShowFontLen").text(length);
        }
    };

    // 提醒模態框功能
    const reminderModal = {
        init: function() {
            this.showReminder();
        },

        showReminder: function() {
            const reminderLength = $("#remind-content").text().length;
            
            if (reminderLength > 0) {
                $('#remind-modal').modal('show');
            }
        }
    };

    // 主題檢查功能
    const subjectChecker = {
        init: function() {
            // 這個功能通過 HTML 的 onblur 事件調用
            window.CheckSubject = this.checkSubject.bind(this);
        },

        checkSubject: function() {
            const subject = $('#SUBJECT').val();
            
            $.ajax({
                url: window.ADDI01_URLS.getCheckSubject,
                data: { Subject: subject },
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function(data) {
                    $('#ADDI01Form [data-valmsg-for="SUBJECT"]').html(data);
                },
                error: function(xhr, err) {
                    console.error('Subject check error:', xhr, err);
                }
            });
        }
    };

    // 音頻功能
    const audioHandler = {
        init: function() {
            window.Audio = this.loadAudio.bind(this);
        },

        loadAudio: function() {
            $.ajax({
                url: window.ADDI01_URLS.audioIndex,
                data: {},
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function(data) {
                    $('#IDAudio').html(data);
                },
                error: function(xhr, err) {
                    console.error('Audio load error:', xhr, err);
                }
            });
        }
    };

    // 表單提交功能
    const formSubmission = {
        init: function() {
            window.TempSave = this.tempSave.bind(this);
            window.CheckSubmitD = this.checkSubmitD.bind(this);
        },

        tempSave: function() {
            const form = $("#ADDI01Form");
            $('<input>').attr({
                type: 'hidden',
                name: 'tempSave',
                value: true
            }).appendTo(form);

            form.submit();
        },

        checkSubmitD: function(buttonElement, functionName) {
            /**
             * 防止重複按鈕點擊
             * @param {Element} buttonElement - 按鈕元素
             * @param {string} functionName - 要執行的函數名稱
             */
            buttonElement.disabled = true;

            setTimeout(() => {
                if (buttonElement.disabled === true) {
                    const functionArray = functionName.split(';');
                    
                    for (let i = 0; i < functionArray.length; i++) {
                        try {
                            eval(functionArray[i]);
                        } catch (e) {
                            console.error('Function execution error:', e);
                        }
                    }
                } else {
                    this.checkSubmitD(buttonElement, functionName);
                }
            }, 0);
        }
    };

    // 初始化所有功能
    deviceDetection.init();
    youtubeChecker.init();
    textCounter.init();
    reminderModal.init();
    subjectChecker.init();
    audioHandler.init();
    formSubmission.init();

    // 設置全局 URL 配置（需要在 CSHTML 中定義）
    window.ADDI01_URLS = window.ADDI01_URLS || {};

    // 頁面加載完成後的額外初始化
    $(window).on('load', function() {
        // 確保文字計數和提醒功能在頁面完全加載後執行
        textCounter.updateCount();
        reminderModal.showReminder();
    });
});
