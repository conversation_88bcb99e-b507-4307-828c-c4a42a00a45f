﻿using com.ecool.service;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using Dapper;
using System.Data;
using System.IO;
using NPOI.SS.UserModel;
using ECOOL_APP.com.ecool.Models;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class SECI02Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "SECI02";

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private ECOOL_APP.com.ecool.service.SECSharedService SharedService = new ECOOL_APP.com.ecool.service.SECSharedService();

        /// <summary>
        /// 定義要取幾月份資料(前12個月)
        /// </summary>
        private int SetMonth = -12;

        public ActionResult _Menu(string NowAction)
        {
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        public IQueryable<V_DB2_L_WORK2> QeuryData(SECI02BooksOrderViewModel model)
        {
            if (model == null) model = new SECI02BooksOrderViewModel();
            int PageSize = 20;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            string Grade = string.Empty;
            string CLASS_NO = string.Empty;
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.WhereSYEAR))
            {
                model.WhereSYEAR = SYear.ToString();
            }
            string SQL2 = $@" union

                            select SCHOOL_NO, GRADE, CLASS_NO, USER_NO, NAME, SNAME,0 AS SE_QTY from dbo.HRMT01 AS b where IDNO NOT IN(select NO_READ from dbo.DB2_L_WORK2 where SCHOOL_NO = b.SCHOOL_NO ";
            string sSQL = @"		select SCHOOL_NO,GRADE,CLASS_NO,USER_NO ,NAME, SNAME,SE_QTY  from(	 SELECT  h.SCHOOL_NO, h.GRADE, h.CLASS_NO, h.USER_NO, h.NAME, h.SNAME, SUM(b.QTY) AS SE_QTY
                            FROM  dbo.DB2_L_WORK2 AS b
                            INNER JOIN dbo.HRMT01 AS h ON h.IDNO = b.NO_READ AND h.USER_TYPE = 'S' AND h.USER_STATUS <> 9
                            Where b.RET_YYMM <> '      '
                            and b.SCHOOL_NO = @SCHOOL_NO ";

            if (string.IsNullOrWhiteSpace(model.WhereSYEAR) == false)
            {
                sSQL = sSQL + " and b.SEYEAR=@WhereSYEAR ";
                SQL2 = SQL2 + " and SEYEAR=@WhereSYEAR ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereMM) == false)
            {
                sSQL = sSQL + " and right(b.RET_YYMM,2)=@WhereMM ";
                SQL2 = SQL2 + " and right(RET_YYMM,2)=@WhereMM ";
            }

            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                sSQL = sSQL + " and b.RET_YYMM = CONVERT(nvarchar(6), GETDATE(), 112) ";
                SQL2 = SQL2 + " and RET_YYMM = CONVERT(nvarchar(6), GETDATE(), 112)";
            }

            sSQL = sSQL + "  GROUP BY   h.SCHOOL_NO, h.GRADE, h.CLASS_NO, h.USER_NO, h.NAME, h.SNAME ) as temp";
            SQL2 = SQL2 + ") and SCHOOL_NO = @SCHOOL_NO AND USER_TYPE = 'S' AND USER_STATUS <> 9";
            sSQL = sSQL + SQL2;
            IQueryable<V_DB2_L_WORK2> BooksList = db.Database.Connection.Query<V_DB2_L_WORK2>(sSQL
             , new
             {
                 SCHOOL_NO = SchoolNO,
                 WhereSYEAR = model.WhereSYEAR,
                 WhereMM = new StringHelper().StrRigth("00" + model.WhereMM, 2),
             }).AsQueryable();

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                BooksList = BooksList.Where(a => a.USER_NO == model.whereUserNo);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                BooksList = BooksList.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                BooksList = BooksList.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                BooksList = BooksList.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "SE_QTY":
                    BooksList = BooksList.OrderByDescending(a => a.SE_QTY).ThenBy(a => a.SNAME);
                    break;

                default:
                    BooksList = BooksList.OrderByDescending(a => a.SE_QTY).ThenBy(a => a.SNAME);
                    break;
            }
            return BooksList;
        }

        public ActionResult ExcelExport(SECI02BooksOrderViewModel model)
        {
            int PageSize = 20;

            if (model == null) model = new SECI02BooksOrderViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            string Grade = string.Empty;
            string CLASS_NO = string.Empty;

            IQueryable<V_DB2_L_WORK2> BooksList = QeuryData(model);
            DataTable DataTableExcel = BooksList?.AsDataTable();
            DataTableExcel.Columns.Add("RowNum", typeof(int));
            int i = 0;
            foreach (DataRow row in DataTableExcel.Rows)
            {
                row["RowNum"] = ++i;
            }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/booksorderList.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "借閱排行榜", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\借閱排行榜" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "借閱排行榜.xlsx");//輸出檔案給Client端
        }

        /// <summary>
        /// 借書排行
        /// </summary>
        /// <param name="model"></param>
        /// <param name="vOrderList"></param>
        /// <returns></returns>
        public ActionResult BooksOrder(SECI02BooksOrderViewModel model)
        {
            string Strfrom = Request["from"] == null ? (model.fromStr == null ? null : model.fromStr) : Request["from"];
            
            ViewBag.from = Strfrom;
            ViewBag.Title = "借書排行榜";
            int PageSize = 20;

            if (model == null) model = new SECI02BooksOrderViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            string Grade = string.Empty;
            string CLASS_NO = string.Empty;
            ViewBag.RunStatus = "";
            List<SelectListItem> GradeList = HRMT01.GetGradeItems(model.whereGrade);
            ViewBag.GradeItem = GradeList;

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            if (string.IsNullOrWhiteSpace(this.Request["Grade"]) == false)
            {
                model.whereGrade = this.Request["Grade"];
                SelectListItem it = GradeList.Where(a => a.Value == this.Request["Grade"]).FirstOrDefault();
                if (it != null) ViewBag.Title = it.Text + ViewBag.Title;
            }
            string Sdate = DateTime.Now.Date.Year.ToString() + DateTime.Now.Date.Month.ToString("00") + DateTime.Now.Date.Day.ToString("00");
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            string sSQL4 = @"SELECT 
                     CASE his.run_status 
                      WHEN 0 THEN N'失敗'
                      WHEN 1 THEN N'成功'
                      WHEN 3 THEN N'取消'
                      WHEN 4 THEN N'進行中'
                      WHEN 5 THEN N'未知'
                     END RunMsg, 
                     his.message N'message',
                     CASE his.run_date WHEN 0 THEN NULL ELSE
                      convert(datetime, stuff(stuff(cast(his.run_date as nchar(8)), 7, 0, '-'), 5, 0, '-') + N' ' + 
                      stuff(stuff(substring(cast(1000000 + his.run_time as nchar(7)), 2, 6), 5, 0, ':'), 3, 0, ':'), 120) 
                     END AS CHG_DATE
                     FROM msdb.dbo.sysjobs jb INNER JOIN msdb.dbo.sysjobhistory his
                     ON jb.job_id = his.job_id
                     where jb.name='MERAGE_HRMT01_SQL' and his.run_status<>4 and his.step_name<>'(作業結果)'  and SUBSTRING(his.step_name,1,6)=@SCHOOL_NO
                     order by his.step_name,his.run_date desc";
                    
           var temp = db.Database.Connection.Query <MergeHrmtLog>(sSQL4
             , new
             {
                 SCHOOL_NO = SchoolNO,
                 

             }).AsQueryable();
             temp.Where(x => x.RunMsg == "成功").FirstOrDefault();
            if (temp != null && temp.Count()!=0)
            {

              
                ViewBag.RunStatusDate = " 資料統計至" + temp.Where(x => x.RunMsg == "成功").Select(x => x.CHG_DATE).FirstOrDefault().ToLongDateString();
                ViewBag.RunStatus = "同步成功";
            }
            //else {
            //    ViewBag.RunStatus = "同步失敗";

            //}
            if (string.IsNullOrWhiteSpace(model.WhereSYEAR))
            {
                model.WhereSYEAR = SYear.ToString();
            }

            ViewBag.SYearItems = GetSYearsItems(model.WhereSYEAR);

            ViewBag.MonthItems = GetSMonthItems(model.WhereMM);

            string SQL2 = $@" union

                            select SCHOOL_NO, GRADE, CLASS_NO, USER_NO, NAME, SNAME,0 AS SE_QTY from dbo.HRMT01 AS b where IDNO NOT IN(select NO_READ from dbo.DB2_L_WORK2 where SCHOOL_NO = b.SCHOOL_NO ";
            string sSQL = @"		select SCHOOL_NO,GRADE,CLASS_NO,USER_NO ,NAME, SNAME,SE_QTY  from(	 SELECT  h.SCHOOL_NO, h.GRADE, h.CLASS_NO, h.USER_NO, h.NAME, h.SNAME,  count(*) AS SE_QTY
                            FROM  dbo.DB2_L_WORK AS b
                            INNER JOIN dbo.HRMT01 AS h ON h.IDNO = b.NO_READ AND h.USER_TYPE = 'S' AND h.USER_STATUS <> 9
                            Where b.BORROW_DATE <> '      '
                            and b.SCHOOL_NO = @SCHOOL_NO ";

            if (string.IsNullOrWhiteSpace(model.WhereSYEAR) == false &&(model.WhereUP_DATE_START == null || model.WhereUP_DATE_END == null))
            {
                sSQL = sSQL + " and b.SEYEAR=@WhereSYEAR ";
                SQL2 = SQL2 + " and SEYEAR=@WhereSYEAR ";
            }
            if (model.WhereUP_DATE_START!= null && model.WhereUP_DATE_END != null)
            {
                sSQL = sSQL + " and b.BORROW_DATE between @StartDate and @EndDate";
            }

                if (string.IsNullOrWhiteSpace(model.WhereMM) == false)
            {
                sSQL = sSQL + " and replicate('0', (2-len(MONTH(BORROW_DATE)))) + CONVERT(nvarchar, MONTH(BORROW_DATE)) = @WhereMM ";

                SQL2 = SQL2 + " and SUBSTRING(RET_YYMM,len(RET_YYMM),len(RET_YYMM)) =@WhereMM ";
            }

            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                sSQL = sSQL + " and Cast(YEAR(BORROW_DATE) as nvarchar)+replicate('0', (2-len(MONTH(BORROW_DATE)))) + CONVERT(nvarchar, MONTH(BORROW_DATE))= CONVERT(nvarchar(6), GETDATE(), 112) ";
                SQL2 = SQL2 + " and RET_YYMM = CONVERT(nvarchar(6), GETDATE(), 112)";
            }

            sSQL = sSQL + "  GROUP BY   h.SCHOOL_NO, h.GRADE, h.CLASS_NO, h.USER_NO, h.NAME, h.SNAME ) as temp";
            SQL2 = SQL2 + ") and SCHOOL_NO = @SCHOOL_NO AND USER_TYPE = 'S' AND USER_STATUS <> 9";
           // sSQL = sSQL + SQL2;
            IQueryable<V_DB2_L_WORK2> BooksList = db.Database.Connection.Query<V_DB2_L_WORK2>(sSQL
             , new
             {
                // StartDate=model.WhereUP_DATE_START,
               //  EndDate=model.WhereUP_DATE_END,
                 SCHOOL_NO = SchoolNO,
                 WhereSYEAR = model.WhereSYEAR,
                 WhereMM = new StringHelper().StrRigth("00" + model.WhereMM, 2),
             }).AsQueryable();
            if (model.WhereUP_DATE_START != null && model.WhereUP_DATE_END != null)
            {

                BooksList = db.Database.Connection.Query<V_DB2_L_WORK2>(sSQL
             , new
             {
                  StartDate=model.WhereUP_DATE_START,
                  EndDate=model.WhereUP_DATE_END,
                 SCHOOL_NO = SchoolNO,
                 WhereSYEAR = model.WhereSYEAR,
                 WhereMM = new StringHelper().StrRigth("00" + model.WhereMM, 2),
             }).AsQueryable();
            }
           if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                BooksList = BooksList.Where(a => a.USER_NO == model.whereUserNo);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                BooksList = BooksList.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                BooksList = BooksList.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                BooksList = BooksList.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "SE_QTY":
                    BooksList = BooksList.OrderByDescending(a => a.SE_QTY).ThenBy(a => a.SNAME);
                    break;

                default:
                    BooksList = BooksList.OrderByDescending(a => a.SE_QTY).ThenBy(a => a.SNAME);
                    break;
            }

            if (model.IsPrint)
            {
                PageSize = int.MaxValue;
            }

            model.BooksList = BooksList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            if (model.IsPrint)
            {
                if (model.BooksList.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    if (BooksList != null)
                    {
                        model.BooksList = BooksList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                    }
                }
            }

            if (model.isCarousel)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }
        }

        /// <summary>
        /// 借書排行by月
        /// </summary>
        /// <param name="model"></param>
        /// <param name="vOrderList"></param>
        /// <returns></returns>
        //public ActionResult BooksOrderMonth(SECI02BooksOrderViewModel model)
        //{
        //    ViewBag.Title = "本月借書排行榜";

        //    if (model == null) model = new SECI02BooksOrderViewModel();

        //    string SchoolNO = UserProfileHelper.GetSchoolNo();
        //    string Grade = string.Empty;
        //    string CLASS_NO = string.Empty;

        //    List<SelectListItem> GradeList = HRMT01.GetGradeItems(model.whereGrade);
        //    ViewBag.GradeItem = GradeList;

        //    ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
        //       .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

        //    if (string.IsNullOrWhiteSpace(this.Request["Grade"]) == false)
        //    {
        //        model.whereGrade = this.Request["Grade"];
        //        SelectListItem it = GradeList.Where(a => a.Value == this.Request["Grade"]).FirstOrDefault();
        //        if (it != null) ViewBag.Title = it.Text + ViewBag.Title;
        //    }
        //    //if (string.IsNullOrWhiteSpace(this.Request["CLASS_NO"]) == false) model.whereCLASS_NO = this.Request["CLASS_NO"];

        //    string yymm = DateTime.Today.ToString("yyyyMM");

        //    var test = from lw in db.DB2_L_WORK2
        //               where lw.SCHOOL_NO == SchoolNO && lw.RET_YYMM == yymm && lw.QTY > 0
        //               select lw;

        //    //if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
        //    //{
        //    //    BooksList = BooksList.Where(a => a.USER_NO == model.whereUserNo);
        //    //}

        //    //if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
        //    //{
        //    //    BooksList = BooksList.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
        //    //}

        //    //if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
        //    //{
        //    //    BooksList = BooksList.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
        //    //}

        //    //if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
        //    //{
        //    //    BooksList = BooksList.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
        //    //}

        //    //switch (model.OrdercColumn)
        //    //{
        //    //    case "SE_QTY":
        //    //        BooksList = BooksList.OrderByDescending(a => a.SE_QTY).ThenBy(a => a.SNAME);
        //    //        break;
        //    //    default:
        //    //        BooksList = BooksList.OrderByDescending(a => a.SE_QTY).ThenBy(a => a.SNAME);
        //    //        break;
        //    //}

        //    //model.BooksList = BooksList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 20);

        //    if (model.isCarousel)
        //    {
        //        return PartialView(model);
        //    }
        //    else
        //    {
        //        return View(model);
        //    }

        //}

        [CheckPermission] //檢查權限
        public ActionResult Index(string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "儀表板-點數統計";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            //條件
            SECSharedSearchViewModel wData = UseSharedSearch(SCHOOL_NO, CLASS_NO, USER_NO);

            SECI02IndexViewModel Data = new SECI02IndexViewModel();
            //總酷幣點數
            SECSharedTotalCashDataViewModel TotalData = SharedService.GetTotalCash(ref db, wData);
            Data.SumCashBarChart = GetSumCashBarChart(TotalData);

            //各類別 酷幣點數，及比例
            List<SECSharedCashPreViewModel> PerData = SharedService.GetCashPre(ref db, wData, (int)TotalData.ToTAL_CASH_ALL);
            Data.PerData = PerData;
            Data.PreCashPieChart = GetPreCashPieChart(PerData);
            Data.CashPreColumnChart = GetCashPreColumnChart(PerData);

            //   var USER_NOItems = HRMT01.GetUserNoListData(wData.SCHOOL_NO, wData.CLASS_NO, "", ref db);
            //  ViewBag.USER_NOItems = USER_NOItems;

            return View(Data);
        }

        /// <summary>
        /// 投稿統計
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        public ActionResult ReadIndex(string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "儀表板-投稿統計";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            SECSharedSearchViewModel wData = UseSharedSearch(SCHOOL_NO, CLASS_NO, USER_NO);

            SECI02ReadIndexViewModel Data = new SECI02ReadIndexViewModel();

            Data.TEachMonth = DateHelper.GetArrMonth(DateTime.Now, SetMonth);
            Data.MonthList = SharedService.GetMonthData(Data.TEachMonth, wData);

            Data.TEachWeekToDate = DateHelper.GetArrWeekToDate(DateTime.Now, SetMonth);
            Data.WeekList = SharedService.GetWeekData(Data.TEachWeekToDate, wData);

            Data = SharedService.GetArrData(Data, SetMonth);

            Data.Monthcharts = GetMonthcharts(Data);
            Data.Weekcharts = GetWeekcharts(Data);

            return View(Data);
        }

        /// <summary>
        /// 使用統計
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        public ActionResult UseIndex(string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "儀表板-使用統計";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            SECSharedSearchViewModel wData = UseSharedSearch(SCHOOL_NO, CLASS_NO, USER_NO);

            SECI02UseIndexViewModel Data = new SECI02UseIndexViewModel();
            Data.StudentPieChart = GetPreStudentPieChart(wData);
            Data.PeopleCount = SharedService.GetPeopleCountData(ref db, wData);

            return View(Data);
        }

        /// <summary>
        /// 使用情形
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult UseSituationIndex(SECI02UseSituationIndexViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (Data == null) Data = new SECI02UseSituationIndexViewModel();

            if ((user != null ? (byte)user.ROLE_TYPE : HRMT24_ENUM.RoleTypeVal.VisitorsLevel) <= HRMT24_ENUM.RoleTypeVal.AdminLevel)
            {
                ViewBag.Panel_Title = "儀表板-各校使用情形";
                Data.IsClass = false;
            }
            else
            {
                ViewBag.Panel_Title = "儀表板-各班使用情形";
                Data.IsClass = true;

                Data.WhereSCHOOL_NO = user.SCHOOL_NO;
            }

            ViewBag.SYearItems = GetSYearsItems(Data.WhereSYEAR);

            ViewBag.MonthItems = SysHelper.GetSemesterMonthItems(Data.WhereMM);

            Data = SharedService.GetUseSituationTotalData(ref db, Data);

            return View(Data);
        }

        /// <summary>
        /// 關懷清單
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        public ActionResult CareIndex(string SCHOOL_NO, string GRADE, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "儀表板-關懷清單(登入)";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            SECSharedSearchViewModel wData = UseSharedSearch(SCHOOL_NO, CLASS_NO, USER_NO, GRADE);

            SECI02CareIndexViewModel Data = new SECI02CareIndexViewModel();

            Data.PeopleUnused = SharedService.GetPeopleUnusedData(ref db, wData);

            return View(Data);
        }

        public ActionResult CareBookIndex(string SCHOOL_NO, string GRADE, string CLASS_NO, string USER_NO, string fromStr)
        {
            string Strfrom = Request["from"] == null ? (fromStr == null ? null : fromStr) : Request["from"];

            ViewBag.from = Strfrom;
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "儀表板-關懷清單(借書)";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            SECSharedSearchViewModel wData = UseSharedSearch(SCHOOL_NO, CLASS_NO, USER_NO, GRADE);

            SECI02CareIndexViewModel Data = new SECI02CareIndexViewModel();

            Data.PeopleUnused = SharedService.GetBookPeopleUnusedData(ref db, wData);

            return View(Data);
        }

        /// <summary>
        /// 借書統計
        /// </summary>
        /// <returns></returns>
        public ActionResult BorrowIndex(SECI02BorrowIndexViewModel model)
        {
            string Strfrom = Request["from"] == null ? (model.fromStr == null ? null : model.fromStr) : Request["from"];
            ViewBag.from = Strfrom;
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "儀表板-本學期借書統計";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (model == null) model = new SECI02BorrowIndexViewModel();

            if (string.IsNullOrWhiteSpace(model.SearchSCHOOL_NO))
            {
                model.SearchSCHOOL_NO = user.SCHOOL_NO;
            }

            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.SearchSCHOOL_NO, null, user, true, "全部學校", "ALL", true);

            string DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            if (DATA_ANGLE_TYPE != UserProfileHelper.AngleVal.AllSchoolData)
            {
                SchoolNoSelectItem.Where(a => a.Value == user.SCHOOL_NO);
            }

            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            ViewBag.MonthItems = GetSMonthItems(model.WhereMM, SYear.ToString());

            model = SharedService.GetBorrowData(model, ref db);

            return View(model);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }

            var UserNoListData = HRMT01.GetUserNoListData(SCHOOL_NO, CLASS_NO, ref db);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, USER_NO, true, null);

            return Content(_html);
        }

        protected List<SelectListItem> GetSYearsItems(string defaultSelectValue)
        {
            List<SelectListItem> SYEARItems = new List<SelectListItem>();
            SYEARItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            Byte MinYear = this.db.HRMT01.Where(a => a.USER_TYPE == UserType.Student).Min(a => a.SYEAR).Value;
            Byte MaxYear = this.db.HRMT01.Where(a => a.USER_TYPE == UserType.Student).Max(a => a.SYEAR).Value;
            for (Byte i = MinYear; i <= MaxYear; i++)
            {
                SYEARItems.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString(), Selected = i.ToString() == defaultSelectValue });
            }

            return SYEARItems;
        }

        protected List<SelectListItem> GetSMonthItems(string defaultSelectValue, string withSYear = "")
        {
            List<SelectListItem> SYEARItems = new List<SelectListItem>();
            SYEARItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            for (int i = 8; i <= 12; i++)
                SYEARItems.Add(new SelectListItem() { Text = i.ToString() + $"月 ({withSYear}上學期)", Value = i.ToString(), Selected = i.ToString() == defaultSelectValue });

            for (int i = 1; i <= 7; i++)
                SYEARItems.Add(new SelectListItem() { Text = i.ToString() + $"月 ({withSYear}下學期)", Value = i.ToString(), Selected = i.ToString() == defaultSelectValue });

            return SYEARItems;
        }

        private SECSharedSearchViewModel UseSharedSearch(string SCHOOL_NO, string CLASS_NO, string USER_NO, string GRADE = null)
        {
            //條件
            SECSharedSearchViewModel wData = new SECSharedSearchViewModel();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                wData.SCHOOL_NO = user.SCHOOL_NO;
            }
            else
            {
                wData.SCHOOL_NO = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(GRADE) == false)
            {
                wData.GRADE = GRADE;
            }

            if (string.IsNullOrWhiteSpace(CLASS_NO) == false)
            {
                wData.CLASS_NO = CLASS_NO;
            }

            if (string.IsNullOrWhiteSpace(USER_NO) == false)
            {
                wData.USER_NO = USER_NO;
            }

            string DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);

            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(wData.SCHOOL_NO, null, user, true, "全部學校", "ALL", true);

            if (DATA_ANGLE_TYPE != UserProfileHelper.AngleVal.AllSchoolData)
            {
                SchoolNoSelectItem.Where(a => a.Value == user.SCHOOL_NO);
            }

            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            var ClassItems = HRMT01.GetClassListData(wData.SCHOOL_NO, wData.GRADE, wData.CLASS_NO, ref db);
            ViewBag.ClassItems = ClassItems;

            ViewBag.GradeItem = HRMT01.GetGradeItems(wData.GRADE);

            List<SelectListItem> USER_NOItems = new List<SelectListItem>();
            if (!string.IsNullOrWhiteSpace(wData.CLASS_NO))
            {
                USER_NOItems.AddRange(HRMT01.GetUserNoListData(wData.SCHOOL_NO, wData.CLASS_NO, "", ref db));
            }
            if (!string.IsNullOrWhiteSpace(wData.USER_NO))
            {
                USER_NOItems = USER_NOItems.Select(a =>
                {
                    a.Selected = a.Value == wData.USER_NO;
                    return a;
                }).ToList();
            }
            else
            {
                USER_NOItems.Add(new SelectListItem { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(USER_NO) });
            }
            ViewBag.USER_NOItems = USER_NOItems;
            return wData;
        }

        #region 全校 總累積酷幣點數、總現有酷幣點數 長條圖

        /// <summary>
        ///  總累積酷幣點數、總現有酷幣點數 長條圖
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        private Highcharts GetSumCashBarChart(SECSharedTotalCashDataViewModel sAWAT01)
        {
            Data data = new Data(new[]
            {
                    new Point { Y = sAWAT01.SINACE_Total_CASH_ALL, Color=System.Drawing.Color.Yellow},
                    new Point { Y = sAWAT01.ToTAL_CASH_ALL, Color=System.Drawing.Color.Blue},
                    new Point { Y = sAWAT01.ToTAL_CASH_AVAILABLE,Color= System.Drawing.Color.Red },
            });

            Highcharts TempSumCashBarChart = new Highcharts("TempSumCashBarChart")
            .InitChart(new Chart { DefaultSeriesType = ChartTypes.Column })
            .SetTitle(new Title { Text = "酷幣狀況表" })
            .SetXAxis(new XAxis { Categories = new[] { "酷幣總發行量", "在校生累積點數", "在校生尚未兌換點數" }, Labels = new XAxisLabels { Rotation = -60, Style = "fontSize: '12px'" } })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "酷幣數量" }, AllowDecimals = false })
            .SetSeries(new[]
                   {
                        new Series {
                            Data = data,
                            Name = "酷幣數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                   })
          .SetPlotOptions(new PlotOptions
          {
              Column = new PlotOptionsColumn
              {
                  DataLabels = new PlotOptionsColumnDataLabels
                  {
                      Enabled = true,
                      Rotation = 0,
                      Color = System.Drawing.Color.Black,
                      Format = "{point.y:.1f}",
                      Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                  }
              }
          })
            .SetLegend(new Legend { Enabled = false });

            chartsHelper.SetCopyright(TempSumCashBarChart);

            return TempSumCashBarChart;
        }

        #endregion 全校 總累積酷幣點數、總現有酷幣點數 長條圖

        #region 酷幣占比 圓餅圖

        /// <summary>
        /// 酷幣占比 圓餅圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetPreCashPieChart(List<SECSharedCashPreViewModel> PerData)
        {
            var returnObject = new List<object>();

            foreach (var item in PerData)
            {
                returnObject.Add(new object[] { item.LOG_DESC, item.PRE });
            }

            Highcharts TempCashPieChart = new Highcharts("TempCashPieChart")
               .InitChart(new Chart
               {
                   DefaultSeriesType = ChartTypes.Pie,
                   BackgroundColor = null,
                   BorderWidth = null,
                   Shadow = false,
               })
              .SetPlotOptions(new PlotOptions
              {
                  Pie = new PlotOptionsPie
                  {
                      AllowPointSelect = true,
                      Cursor = Cursors.Pointer,
                      //ShowInLegend = true,
                      DataLabels = new PlotOptionsPieDataLabels
                      {
                          Enabled = true,
                          Format = "<b>{point.name}</b>: {point.percentage:.1f} %",
                          Style = "color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'"
                      }
                  }
              })
             .SetTooltip(new Tooltip { PointFormat = "{series.name}: <b>{point.percentage:.1f}%</b>" })
             .SetTitle(new Title { Text = "酷幣點數占比圖表(在校生)" })
              .SetSeries(new Series
              {
                  Data = new Data(returnObject.ToArray())
              });

            chartsHelper.SetCopyright(TempCashPieChart);

            return TempCashPieChart;
        }

        #endregion 酷幣占比 圓餅圖

        #region 酷幣各類別點數 長條圖

        /// <summary>
        ///  酷幣各類別點數 長條圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        public Highcharts GetCashPreColumnChart(List<SECSharedCashPreViewModel> PerData)
        {
            var returnPoint = new List<Point>();

            foreach (var item in PerData)
            {
                returnPoint.Add(new Point
                {
                    Y = item.SUM_ADD_CASH_ALL
                    ,
                    Name = item.LOG_DESC
                });
            }

            Data data = new Data(returnPoint.ToArray());

            Highcharts TempPreColumnChart = new Highcharts("TempPreColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
               //BackgroundColor = new BackColorOrGradient(
               //                                 new Gradient
               //                                 {
               //                                     LinearGradient = new[] { 0, 0, 0, 400 },
               //                                     Stops = new object[,]
               //                                                {
               //                                                     {   0, System.Drawing.Color.White }
               //                                                     , {1, "#C7DFF5" }
               //                                                }
               //                                 })
           })
           .SetTitle(new Title { Text = "各類別酷幣點數表(在校生)" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category
                        ,
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           //.SetYAxis(new YAxis { Title = new YAxisTitle { Text = "酷幣數量" }, Min = 0 })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "酷幣數量" }, AllowDecimals = false })
           .SetSeries(new[]
                   {
                        new Series {
                            Data = data,
                            Name = "酷幣數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 點</b>" })
           .SetLegend(new Legend { Enabled = false });

            chartsHelper.SetCopyright(TempPreColumnChart);

            return TempPreColumnChart;
        }

        #endregion 酷幣各類別點數 長條圖

        #region 線上投稿、閱讀認証月趨示圖

        /// <summary>
        /// 產生 月趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetMonthcharts(SECI02ReadIndexViewModel Data)
        {
            if (Data.MonthList.Count == 0)
            {
                return null;
            }

            Highcharts Monthchart = new Highcharts("Monthchart");

            Monthchart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = "月統計表", X = 0 })
            .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "月份" }, Categories = Data.TEachMonth.ToArray() })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "筆數" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "筆" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 { Name =  ADDT01.GetWritingName,
                                   Data = new DotNet.Highcharts.Helpers.Data(Data.MonthArrWRITING)
                                 },
                                 new Series
                                 { Name = ADDT06.GetPassportName,
                                   Data = new DotNet.Highcharts.Helpers.Data(Data.MonthArrPASSPORT)
                                 }
                        }
            );

            chartsHelper.SetCopyright(Monthchart);

            return Monthchart;
        }

        #endregion 線上投稿、閱讀認証月趨示圖

        #region 線上投稿、閱讀認証週趨示圖

        /// <summary>
        /// 產生 週趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetWeekcharts(SECI02ReadIndexViewModel Data)
        {
            if (Data.WeekList.Count == 0)
            {
                return null;
            }

            Highcharts Weekchart = new Highcharts("Weekchart");

            Weekchart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = "週統計表", X = 0 })
            .SetXAxis(new XAxis
            {
                Title = new XAxisTitle { Text = "時間" },
                Categories = Data.ArrEachWeek,
                Labels = new XAxisLabels
                {
                    Rotation = -90,
                    Style = "fontSize: '12px'"
                }
            })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "筆數" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "筆" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 { Name = ADDT01.GetWritingName,
                                   Data = new DotNet.Highcharts.Helpers.Data(Data.WeekArrWRITING)
                                 },
                                 new Series
                                 { Name = ADDT06.GetPassportName,
                                   Data = new DotNet.Highcharts.Helpers.Data(Data.WeekArrPASSPORT)
                                 }
                        }
            );

            chartsHelper.SetCopyright(Weekchart);

            return Weekchart;
        }

        #endregion 線上投稿、閱讀認証週趨示圖

        #region 學生參與比率 圓餅圖

        /// <summary>
        /// 學生參與比率 圓餅圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetPreStudentPieChart(SECSharedSearchViewModel wData)
        {
            var HRMT01List = db.HRMT01.Where(A => A.USER_TYPE == UserType.Student);

            if (string.IsNullOrWhiteSpace(wData.SCHOOL_NO) == false && wData.SCHOOL_NO != SharedGlobal.ALL)
            {
                HRMT01List = HRMT01List.Where(a => a.SCHOOL_NO == wData.SCHOOL_NO);
            }

            if (string.IsNullOrWhiteSpace(wData.CLASS_NO) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.CLASS_NO == wData.CLASS_NO);
            }

            if (string.IsNullOrWhiteSpace(wData.USER_NO) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO == wData.USER_NO);
            }

            int StudentAll = HRMT01List.Count();

            var StudentData = (from A in HRMT01List
                               where A.USER_STATUS != UserStaus.Invalid
                               group A by new
                               {
                                   USER_STATUS = A.USER_STATUS
                               } into g
                               select new
                               {
                                   USER_STATUS = g.Key.USER_STATUS,
                                   STATUS_Count = g.Count(),
                                   STATUS_PRE = Math.Round(((Double)g.Count() / (Double)StudentAll) * 100)
                               }).OrderByDescending(t => t.STATUS_Count).ToList();

            var returnObject = new List<object>();

            foreach (var item in StudentData)
            {
                returnObject.Add(new object[] { UserStaus.GetDesc(item.USER_STATUS) + " (" + item.STATUS_Count + "位) ", item.STATUS_PRE });
            }

            Highcharts TempStudentPieChart = new Highcharts("TempStudentPieChart")
               .InitChart(new Chart
               {
                   DefaultSeriesType = ChartTypes.Pie,
                   BackgroundColor = null,
                   BorderWidth = null,
                   Shadow = false
               })
              .SetPlotOptions(new PlotOptions
              {
                  Pie = new PlotOptionsPie
                  {
                      AllowPointSelect = true,
                      Cursor = Cursors.Pointer,
                      //ShowInLegend = true,
                      DataLabels = new PlotOptionsPieDataLabels
                      {
                          Enabled = true,
                          Format = "<b>{point.name}</b>: {point.percentage:.1f} %",
                          Style = "color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'"
                      }
                  }
              })
             .SetTooltip(new Tooltip { PointFormat = "{series.name}: <b>{point.percentage:.1f}%</b>" })
             .SetTitle(new Title { Text = "學生參與比率圖表" })
              .SetSeries(new Series
              {
                  Data = new Data(returnObject.ToArray())
              });

            chartsHelper.SetCopyright(TempStudentPieChart);

            return TempStudentPieChart;
        }

        #endregion 學生參與比率 圓餅圖
    }
}