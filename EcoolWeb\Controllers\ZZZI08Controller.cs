﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP;
using EcoolWeb.CustomAttribute;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.Models.DTO.ZZZI08;
using System.IO;
using System.Text.RegularExpressions;
using System.Drawing.Imaging;
using System.Drawing;
using EcoolWeb.Util;
using ECOOL_APP.com.ecool.util;
using System.Data;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI08Controller : Controller
    {
        protected string _ErrorRowCellExcel = string.Empty;

        /// <summary>
        /// 是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckErr = false;

        /// <summary>
        /// 格式是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckDataTypeErr = false;

        /// <summary>
        /// 必輸未輸 true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckMustErr = false;

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI08";

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg = string.Empty;

        /// <summary>
        /// 資料庫相關處理
        /// </summary>
        private ZZZI08Service Db = new ZZZI08Service();

        private ECOOL_DEVEntities Edb = new ECOOL_DEVEntities();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;

        private string SysPath = ZZZI08Service.GetSysPath();

        /// <summary>
        /// 必輸欄位陣列
        /// </summary>
        private string[] MustMainArray; //必輸欄位

        /// <summary>
        /// Excel標題
        /// </summary>
        private string[] TitleMainArray; //標準標題

        [CheckPermission] //檢查權限
        public ActionResult Index(ZZZI08ListViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            if (Data.Q_COPY_YN == "Y")
            {
                ViewBag.Panel_Title = "有獎徵答-複製一覽表";
            }
            else
            {
                ViewBag.Panel_Title = "有獎徵答-維護一覽表";
            }

            this.Shared();
            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index", IsGetAllActionPermission = true)] //檢查權限
        public ActionResult PageContent(ZZZI08ListViewModel Data, int pageSize = 10)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();
            this.GetSCHOOL_NO();

            if (string.IsNullOrWhiteSpace(Data.Q_SCHOOL_NO))
            {
                ModelState.Remove("Q_SCHOOL_NO");
                Data.Q_SCHOOL_NO = "ALL";
            }

            if (Data.Q_COPY_YN == "Y")
            {
                if (string.IsNullOrWhiteSpace(Data.Q_SCHOOL_NO))
                {
                    ModelState.Remove("Q_SCHOOL_NO");
                    Data.Q_SCHOOL_NO = "ALL";
                }

                var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(Data.Q_SCHOOL_NO, null);
                ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;
            }
            else
            {
                ModelState.Remove("Q_SCHOOL_NO");
                Data.Q_SCHOOL_NO = DefaultSCHOOL_NO;
            }

            int count = int.MinValue;
            var DataList = Db.GetListData(Data, user, pageSize, ref count);
            Data.ADDT11List = DataList.ToPagedList(Data.Page - 1, pageSize, count);

            return PartialView(Data);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _QView(ZZZI08ListViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;

            this.GetSCHOOL_NO();

            if (Data.Q_SCHOOL_NO == null)
            {
                Data.Q_SCHOOL_NO = DefaultSCHOOL_NO;
            }

            List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO(Data.Q_SCHOOL_NO, user == null ? Data.Q_SCHOOL_NO : "", user);
            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;

            ViewBag.StatuslListItem = new uADDT11().GetSTATUSListItem();

            return PartialView();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult SysIndex(ZZZI08EditViewModel Data, ZZZI08ListViewModel QData)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-快選精靈";
            ViewBag.QData = QData;
            this.Shared();

            return View(Data);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(ZZZI08EditViewModel Model, ZZZI08ListViewModel QData, IEnumerable<HttpPostedFileBase> files)
        {
            bool OK = false;
            string Panel_Title = string.Empty;
            if (Model.uADDT11 == null) Model.uADDT11 = new uADDT11();
            ViewBag.BRE_NO = Bre_NO;
            this.GetSCHOOL_NO();
            this.Shared();

            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_U
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_D
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_Z
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_E
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_R
                    || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_H
                )
            {
                OK = this.Save(Model, user, files);

                if (OK)
                {
                    ModelState.Clear();
                    if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_D)
                    {
                        TempData["StatusMessage"] = "刪除成功";
                        return View("Index", QData);
                    }
                    else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_H)
                    {
                        TempData["StatusMessage"] = "隱藏成功";
                        return View("Index", QData);
                    }
                    else
                    {
                        string Msg = string.Empty;

                        if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A)
                        {
                            Msg = "新增成功";
                        }
                        else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_U
                            || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_E)
                        {
                            Msg = "修改成功";
                        }
                        else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_Z)
                        {
                            Msg = "提早結案成功";
                        }
                        else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_R)
                        {
                            Msg = "發佈成功";
                        }

                        TempData["StatusMessage"] = Msg;

                        return View("Index", QData);
                    }
                }
                else
                {
                    TempData["StatusMessage"] = ErrorMsg;
                }
            }
            else
            {
                ModelState.Clear();
            }

            if (Model.uADDT11.DIALOG_ID == null)
            {
                Panel_Title = "新增活動";
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                Model.uADDT11.SYEAR = SYear;
                Model.uADDT11.SEMESTER = (byte)Semesters;
            }
            else
            {
                if (Model.Submit_YN != "Y" || OK == true)
                {
                    ZZZI08EditViewModel oModel = Model;
                    Model = Db.GetGetDetailsData(Model.uADDT11.DIALOG_ID);
                    Model.DATA_TYPE = oModel.DATA_TYPE;
                    Model.VIEW_DATA_TYPE = oModel.VIEW_DATA_TYPE;
                }

                if (Model.VIEW_DATA_TYPE == ZZZI08EditViewModel.VIEW_C)
                {
                    Model = Db.GetGetDetailsData(Model.uADDT11.DIALOG_ID);
                    Panel_Title = "新增活動";
                    int SYear;
                    int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                    Model.uADDT11.DIALOG_ID = null;
                    Model.uADDT11.SCHOOL_NO = DefaultSCHOOL_NO;
                    Model.uADDT11.SYEAR = SYear;
                    Model.uADDT11.SEMESTER = (byte)Semesters;
                    Model.uADDT11.DIALOG_SDATE = null;
                    Model.uADDT11.DIALOG_EDATE = null;
                    Model.uADDT11.USER_NO = null;
                    Model.uADDT11.NAME = null;
                    Model.uADDT11.SNAME = null;
                    Model.uADDT11.JOB_NAME = null;
                    Model.uADDT11.CHG_PERSON = null;
                    Model.uADDT11.CHG_DATE = null;
                    Model.uADDT11.STATUS = null;
                    Model.uADDT11.CRE_PERSON = null;
                    Model.uADDT11.CRE_DATE = null;
                    QData.Q_COPY_YN = "N";
                }
                else
                {
                    Model.REF_KEY = Model.uADDT11.DIALOG_ID;
                    string STATUS = Model.uADDT11.STATUS;

                    Panel_Title = "編輯活動";
                    string StatusMsg = string.Empty;

                    if (STATUS == uADDT11.STATUS_D)
                    {
                        StatusMsg = "此活動未發佈";
                    }
                    else if (STATUS == uADDT11.STATUS_R)
                    {
                        StatusMsg = "此活動已發佈";
                    }
                    else if (STATUS == uADDT11.STATUS_Z)
                    {
                        StatusMsg = "此活動已提早結案";
                    }

                    Panel_Title = Panel_Title + "-" + StatusMsg;
                }
            }

            ViewBag.Panel_Title = Panel_Title;
            ViewBag.QData = QData;

            List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO_ALL(DefaultSCHOOL_NO, user == null ? DefaultSCHOOL_NO : "", user);
            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;

            ViewBag.DialogTypeItems = ADDT11.GetDialogTypeItems(Model.uADDT11?.DIALOG_TYPE, "");

            return View(Model);
        }

        //是非
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _truefalseAns(ZZZI08ADDT12ViewModel Item)
        {
            if (Item == null) Item = new ZZZI08ADDT12ViewModel();
            return PartialView(Item);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _truefalseAnsCopy(ZZZI08EditViewModel Model)
        {
            ZZZI08ADDT12ViewModel Item = Model.truefalseAns.Where(a => a.Html_ID == Model.whereIndex).FirstOrDefault();

            Item.Html_ID = Guid.NewGuid().ToString();
            Item.isCopy = true;

            return PartialView("_truefalseAns", Item);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _multipleschoiceAns(ZZZI08ADDT12ViewModel Item)
        {
            if (Item == null) Item = new ZZZI08ADDT12ViewModel();
            return PartialView(Item);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _multipleschoiceAnsCopy(ZZZI08EditViewModel Model)
        {
            ZZZI08ADDT12ViewModel Item = Model.multipleschoiceAns.Where(a => a.Html_ID == Model.whereIndex).FirstOrDefault();

            Item.Html_ID = Guid.NewGuid().ToString();
            Item.isCopy = true;

            return PartialView("_multipleschoiceAns", Item);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult PreView(string DIALOG_ID, string PreviewY)
        {
            ViewBag.DIALOG_ID = DIALOG_ID;
            ViewBag.PreviewY = PreviewY;
            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ExcelIndex(ZZZI08ListViewModel Data)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-Excel匯入";
            this.Shared();
            return View(Data);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ExcelIndex(HttpPostedFileBase files, ZZZI08ListViewModel QData)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-Excel匯入";
            this.Shared();
            this.GetSCHOOL_NO();

            if (files == null || files.FileName == string.Empty) ModelState.AddModelError("files", "請上傳檔案");
            else if (files != null)
            {
                Regex regexCode = new Regex(@".*\.(xls|xlsx)");

                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳Excel格式為xls、xlsx");
                }
            }

            if (ModelState.IsValid == false)
            {
                ErrorMsg = "錯誤\r\n";
                return View();
            }
            else
            {
                ZZZI08EditViewModel Model = new ZZZI08EditViewModel();
                var OK = this.ExcelData(files, user, ref Model);
                if (OK)
                {
                    string Panel_Title = "編輯活動";
                    string StatusMsg = "此活動未發佈";
                    ViewBag.Panel_Title = Panel_Title + "-" + StatusMsg; ;
                    ViewBag.QData = QData;

                    List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO(DefaultSCHOOL_NO, user == null ? DefaultSCHOOL_NO : "", user);
                    ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
                    ViewBag.DialogTypeItems = ADDT11.GetDialogTypeItems(Model.uADDT11?.DIALOG_TYPE, "");
                    return View("Edit", Model);
                }
                else
                {
                    return View();
                }
            }
        }

        #region Excel 匯入處理

        /// <summary>
        /// Excel 匯入處理
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        private bool ExcelData(HttpPostedFileBase files, UserProfile user, ref ZZZI08EditViewModel OutModel)
        {
            bool ReturnBool = true;

            NPOIHelper npoi = new NPOIHelper(); //NEW NPOIHelper

            npoi.onDataTypeConflict += new DataRowCellHandler(this.NPOI_DataTypeConflict); //宣告使用 輸入內容的型態 是否設定 及 Excel存儲格式一樣 ex. 日期 輸入[aaa] ，Excel存儲格式設日期，或欄位第一行有定義 System.DateTime
            npoi.onRowCheckValue += new DataRowCellHandler(this.NPOI_RowCheckValue); //宣告使用 NPOI_RowCheckValue (自訂檢查Value)

            string[] ArraySheetNames = { "主檔", "是非題", "選擇題" }; //Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)

            TitleMainArray = new string[] { "SYEAR", "SEMESTER", "DIALOG_NAME", "DIALOG_EXPRESS", "DIALOG_SDATE", "DIALOG_EDATE", "ANSWER_COUNT", "COPY_YN" }; //標題欄位

            MustMainArray = new string[] { "SYEAR", "SEMESTER", "DIALOG_NAME", "DIALOG_EXPRESS", "COPY_YN" }; // Excel 必輸欄位

            string _Error;

            try
            {
                DataSet ds = npoi.Excel2Table(files.InputStream, 0, 2, 1, ArraySheetNames);

                if (ds == null)
                {
                    _Error = "上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                ///讀取資料筆數為0
                if (ds.Tables.Count == 0)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br>讀取資料筆數為0，請確認 上傳Excel的 Sheet Name 是否有以下 ";
                    foreach (string ThisSheet in ArraySheetNames)
                    {
                        _Error = _Error + "【" + ThisSheet.ToString() + "】";
                    }
                    _Error = _Error + " Sheet Name ，請確認";

                    TempData["StatusMessage"] = _Error;

                    ReturnBool = false;
                    return ReturnBool;
                }

                ///EXCEL內容資料型態數誤
                if (_CheckDataTypeErr || _CheckMustErr)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> " + _ErrorRowCellExcel;
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                DataTable dtADDT11 = ds.Tables[ArraySheetNames[0]];
                DataTable dtTrueFalse = ds.Tables[ArraySheetNames[1]];
                DataTable dtMultipleschoice = ds.Tables[ArraySheetNames[2]];

                if (dtADDT11.Rows.Count > 1 || dtADDT11.Rows.Count <= 0)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> 主檔必需輸入1筆，不可不輸或超過";
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                ZZZI08EditViewModel Model = new ZZZI08EditViewModel();
                Model.DATA_TYPE = ZZZI08EditViewModel.DATA_TYPE_A_E;
                Model.uADDT11 = dtADDT11.ToList<uADDT11>().FirstOrDefault();
                Model.truefalseAns = (dtTrueFalse != null) ? dtTrueFalse.ToList<ZZZI08ADDT12ViewModel>() : new List<ZZZI08ADDT12ViewModel>();
                Model.multipleschoiceAns = (dtMultipleschoice != null) ? dtMultipleschoice.ToList<ZZZI08ADDT12ViewModel>() : new List<ZZZI08ADDT12ViewModel>();

                bool OK = this.Save(Model, user, null);
                if (OK)
                {
                    string Msg = "新增成功";

                    TempData["StatusMessage"] = Msg;

                    if (OutModel == null) OutModel = new ZZZI08EditViewModel();
                    OutModel = Db.GetGetDetailsData(Model.uADDT11.DIALOG_ID);
                    OutModel.DATA_TYPE = Model.DATA_TYPE;
                    OutModel.VIEW_DATA_TYPE = Model.VIEW_DATA_TYPE;
                    OutModel.REF_KEY = Model.uADDT11.DIALOG_ID;
                }
                else
                {
                    TempData["StatusMessage"] = ErrorMsg;
                    ReturnBool = false;
                    return ReturnBool;
                }
            }
            catch (Exception ex)
            {
                _Error = "上傳錯誤，錯誤原因如下:<br><br>" + ex;
                TempData["StatusMessage"] = _Error;
                ReturnBool = false;
                return ReturnBool;
            }

            return ReturnBool;
        }

        #endregion Excel 匯入處理

        #region 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_DataTypeConflict(object sender, DataRowCellFilledArgs e)
        {
            _CheckErr = true;
            _CheckDataTypeErr = true;
            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
        }

        #endregion 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        #region 作用:NPOI EXCEL 自訂檢查，顯示的錯誤訊息內容

        /// <summary>
        /// 自訂檢查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void NPOI_RowCheckValue(object sender, DataRowCellFilledArgs e)
        {
            if (MustMainArray.Any(s => e.ColName.Equals(s)) == true)
            {
                try
                {
                    if (e.CellToString == "" || e.CellToString == null || Convert.IsDBNull(e.CellToString))
                    {
                        _CheckErr = true;
                        _CheckMustErr = true;
                        _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        #endregion 作用:NPOI EXCEL 自訂檢查，顯示的錯誤訊息內容

        #region 下載(全部人)

        public ActionResult DownLoad(String DIALOG_ID, String name)
        {
            string tempPath = SysPath + DIALOG_ID;
            return File(tempPath + "\\" + name, "text/plain", Server.HtmlEncode(name));
        }

        #endregion 下載(全部人)

        #region 附件刪除

        public JsonResult DeleteFile(string DIALOG_ID, string FILE_NAME)
        {
            string Success = string.Empty;
            string tempPath = SysPath + DIALOG_ID + "\\" + FILE_NAME;

            try
            {
                Db.Del_FILE(DIALOG_ID, FILE_NAME);

                if (string.IsNullOrEmpty(Db.ErrorMsg) == true)
                {
                    if (System.IO.File.Exists(tempPath))
                    {
                        System.IO.File.Delete(tempPath);
                    }

                    Success = "true";
                }
                else
                {
                    Success = "false";
                    ErrorMsg = "附件檔案刪除失敗;" + Db.ErrorMsg;
                }
            }
            catch (Exception ex)
            {
                Success = "false";
                ErrorMsg = "附件檔案刪除失敗" + ex.Message;
            }

            var data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"" + ErrorMsg + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        private void AllDeleteFile(string DIALOG_ID, bool All)
        {
            try
            {
                string tempPath = SysPath + DIALOG_ID;

                if (Directory.Exists(tempPath))
                {
                    if (All)
                    {
                        Directory.Delete(tempPath, true);
                    }
                    else
                    {
                        string[] tempFile = Directory.GetFiles(tempPath);

                        if (tempFile.Length >= 0)
                        {
                            foreach (var item in tempFile)
                            {
                                if (Db.CheckFile(DIALOG_ID, Path.GetFileName(item)) == false)
                                {
                                    System.IO.File.Delete(item);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorMsg += "刪除目錄、檔案失敗" + ex + "r\n";
            }
        }

        #endregion 附件刪除

        #region Check

        private void Check(ZZZI08EditViewModel Model)
        {
            if (Model.uADDT11.ANSWER_PERSON_YN == "Y")
            {
                string REF_KEY = Model.uADDT11.DIALOG_ID ?? Session.SessionID;

                if (Edb.REFT01_Q.Where(a => a.REF_TABLE == "ADDT11" && a.REF_KEY == REF_KEY).Any() == false)
                {
                    ModelState.AddModelError("uADDT11.ANSWER_PERSON_YN", "勾選了是否限制對象，但未選取人員");

                    if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                    {
                        ErrorMsg = ErrorMsg + "<br/>勾選了是否限制對象，但未選取人員";
                    }
                }
            }

            int Num = 0;
            if (Model.truefalseAns != null)
            {
                Model.truefalseAns.RemoveAll(a =>
                     (a.Q_TEXT ?? "") == ""
                  && (a.TRUE_ANS ?? "") == ""
                );

                foreach (var Data in Model.truefalseAns)
                {
                    if (string.IsNullOrWhiteSpace(Data.Q_TEXT) && string.IsNullOrWhiteSpace(Data.TRUE_ANS))
                    {
                    }
                    else
                    {
                        Num = Num + 1;
                        if (string.IsNullOrWhiteSpace(Data.Q_TEXT))
                        {
                            ModelState.AddModelError("truefalseAns[" + Data.Html_ID + "].Q_TEXT", "請輸入題目");

                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【是否題】-欄位名稱【題目】，第" + Num.ToString() + "列-請輸入題目<br/>";
                            }
                        }

                        if (string.IsNullOrWhiteSpace(Data.TRUE_ANS))
                        {
                            ModelState.AddModelError("truefalseAns[" + Data.Html_ID + "].Q_TEXT", "請輸入正確答案");
                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【是否題】-欄位名稱【正確答案】，第" + Num.ToString() + "列-請輸入正確答案<br/>";
                            }
                        }
                    }
                }
            }

            Num = 0;
            if (Model.multipleschoiceAns != null)
            {
                Model.multipleschoiceAns.RemoveAll(a =>
                   (a.Q_TEXT ?? "") == ""
                && (a.TRUE_ANS ?? "") == ""
                && (a.Q_ANS1 ?? "") == ""
                && (a.Q_ANS2 ?? "") == ""
                && (a.Q_ANS3 ?? "") == ""
                && (a.Q_ANS4 ?? "") == ""
                );

                foreach (var Data in Model.multipleschoiceAns)
                {
                    if (string.IsNullOrWhiteSpace(Data.Q_TEXT) && string.IsNullOrWhiteSpace(Data.TRUE_ANS)
                        && string.IsNullOrWhiteSpace(Data.Q_ANS1)
                        && string.IsNullOrWhiteSpace(Data.Q_ANS2)
                        && string.IsNullOrWhiteSpace(Data.Q_ANS3)
                        && string.IsNullOrWhiteSpace(Data.Q_ANS4)
                        )
                    {
                    }
                    else
                    {
                        Num = Num + 1;
                        if (string.IsNullOrWhiteSpace(Data.Q_TEXT))
                        {
                            ModelState.AddModelError("multipleschoiceAns[" + Data.Html_ID + "].Q_TEXT", "請輸入題目");

                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【選擇題】-欄位名稱【題目】，第" + Num.ToString() + "列-請輸入題目<br/>";
                            }
                        }

                        if (string.IsNullOrWhiteSpace(Data.TRUE_ANS))
                        {
                            ModelState.AddModelError("multipleschoiceAns[" + Data.Html_ID + "].TRUE_ANS", "請輸入正確答案");

                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【選擇題】-欄位名稱【正確答案】，第" + Num.ToString() + "列-請輸入正確答案<br/>";
                            }
                        }

                        if (string.IsNullOrWhiteSpace(Data.Q_ANS1) && Data.TRUE_ANS == "1")
                        {
                            ModelState.AddModelError("multipleschoiceAns[" + Data.Html_ID + "].Q_ANS1", "正確答案為1，但【選擇答案1】內容未輸入");

                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【選擇題】-欄位名稱【選擇答案1】，第" + Num.ToString() + "列-正確答案為1，但【選擇答案1】內容未輸入<br/>";
                            }
                        }

                        if (string.IsNullOrWhiteSpace(Data.Q_ANS2) && Data.TRUE_ANS == "2")
                        {
                            ModelState.AddModelError("multipleschoiceAns[" + Data.Html_ID + "].Q_ANS2", "正確答案為2，但【選擇答案2】內容未輸入");

                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【選擇題】-欄位名稱【選擇答案2】，第" + Num.ToString() + "列-正確答案為2，但【選擇答案2】內容未輸入<br/>";
                            }
                        }

                        if (string.IsNullOrWhiteSpace(Data.Q_ANS3) && Data.TRUE_ANS == "3")
                        {
                            ModelState.AddModelError("multipleschoiceAns[" + Data.Html_ID + "].Q_ANS3", "正確答案為3，但【選擇答案3】內容未輸入");

                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【選擇題】-欄位名稱【選擇答案3】，第" + Num.ToString() + "列-正確答案為3，但【選擇答案3】內容未輸入<br/>";
                            }
                        }

                        if (string.IsNullOrWhiteSpace(Data.Q_ANS4) && Data.TRUE_ANS == "4")
                        {
                            ModelState.AddModelError("multipleschoiceAns[" + Data.Html_ID + "].Q_ANS4", "正確答案為4，但【選擇答案4】內容未輸入");

                            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E)
                            {
                                ErrorMsg = ErrorMsg + "Sheet名稱【選擇題】-欄位名稱【選擇答案4】，第" + Num.ToString() + "列-正確答案為4，但【選擇答案4】內容未輸入<br/>";
                            }
                        }
                    }
                }
            }
        }

        #endregion Check

        public ActionResult UploadPicture(HttpPostedFileBase upload, string CKEditorFuncNum, string CKEditor, string langCode)
        {
            string result = "";
            if (upload != null && upload.ContentLength > 0)
            {
                DateTime dt = DateTime.Now;
                string fileName = dt.ToString("yyyy_MM_dd_HH_mm_ss") + "_" + Path.GetFileName(upload.FileName);
                string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
                string imgPath = string.Format(@"{0}ZZZI08IMG\{1}\", Request.MapPath(UploadImageRoot), SCHOOL_NO);
                if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

                string UpLoadFile = imgPath + @"\" + fileName;

                System.Drawing.Image image = System.Drawing.Image.FromStream(upload.InputStream);
                double FixWidth = 500;
                double FixHeight = 1000;
                double rate = 1;
                if (image.Width > FixWidth || image.Height > FixHeight)
                {
                    if (image.Width > FixWidth) rate = FixWidth / image.Width;
                    else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    Bitmap imageOutput = new Bitmap(image, w, h);
                    imageOutput.Save(Path.Combine(imgPath, fileName), image.RawFormat);
                    imageOutput.Dispose();
                }
                else
                {
                    //直接儲存
                    upload.SaveAs(Path.Combine(imgPath, fileName));
                }
                image.Dispose();

                string tempImageUrl = @"~/Content/ZZZI08IMG/" + SCHOOL_NO + @"/" + fileName;
                var imageUrl = Url.Content(tempImageUrl);

                var vMessage = string.Empty;

                result = @"<html><body><script>window.parent.CKEDITOR.tools.callFunction(" + CKEditorFuncNum + ", \"" + imageUrl + "\", \"" + vMessage + "\");</script></body></html>";
            }

            return Content(result);
        }

        private bool Save(ZZZI08EditViewModel Model, UserProfile User, IEnumerable<HttpPostedFileBase> files)
        {
            if (System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck(Bre_NO, "Save", ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>) == false)
            {
                ErrorMsg = "您無權限\r\n" + ErrorMsg;
                return false;
            }

            bool ReturnBool = false;

            if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_U
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_R
                || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_E
                )
            {
                this.Check(Model);

#if DEBUG
                var errorsaa = ModelState
                            .Where(x => x.Value.Errors.Count > 0)
                            .Select(x => new { x.Key, x.Value.Errors })
                            .ToArray();
#endif
                //if (Model.uADDT11.DIALOG_TYPE == null)
                //{
                //    ErrorMsg = "錯誤\r\n   請選擇「分類」,「分類」欄位為必要欄位";
                //    ReturnBool = false;
                //    return ReturnBool;
                //}
                if (ModelState.IsValid) //沒有錯誤
                {
                    if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_A_E) //新增
                    {
                        if (string.IsNullOrWhiteSpace(Model.uADDT11.SCHOOL_NO))
                        {
                            Model.uADDT11.SCHOOL_NO = DefaultSCHOOL_NO;
                        }

                        Model.uADDT11.CHG_DATE = DateTime.Now;
                        Model.uADDT11.CRE_DATE = DateTime.Today;
                        Model.uADDT11.STATUS = uADDT11.STATUS_D;

                        if (User != null)
                        {
                            Model.uADDT11.CHG_PERSON = User.USER_KEY;
                            Model.uADDT11.CRE_PERSON = User.USER_KEY;
                            Model.uADDT11.JOB_NAME = User.SNAME;
                            Model.uADDT11.NAME = User.NAME;
                            Model.uADDT11.SNAME = User.SNAME;
                            Model.uADDT11.USER_NO = User.USER_NO;
                        }

                        Db.CreateDate(Model, files);

                        this.AllDeleteFile(Model.uADDT11.DIALOG_ID, false);
                    }
                    else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_U
                        || Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_E) //修改
                    {
                        Model.uADDT11.CHG_DATE = DateTime.Now;
                        Model.uADDT11.CHG_PERSON = User.USER_KEY;
                        Db.UpDate(Model, files);

                        this.AllDeleteFile(Model.uADDT11.DIALOG_ID, false);
                    }
                    else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_R) //發佈
                    {
                        Model.uADDT11.CHG_DATE = DateTime.Now;
                        Model.uADDT11.CHG_PERSON = User.USER_KEY;
                        Model.uADDT11.STATUS = ZZZI08EditViewModel.DATA_TYPE_R;

                        Db.UpdateSTATUS(Model);

                        //新增老師批閱後應該給予的酷幣點數
                        string strStatusMsg = string.Empty;
                        //批閱應該給予的點數
                        int iSetCoolCash = 10;

                        ECOOL_DEVEntities db = new ECOOL_DEVEntities();

                        CashHelper.TeachAddCash(user, iSetCoolCash, user.SCHOOL_NO, user.USER_NO, "ZZZI08", Model.uADDT11.DIALOG_ID, "有獎徵答發佈題目", true, null, ref db);
                        db.SaveChanges();

                        //更新顯示
                        UserProfile.RefreshCashInfo(user, ref db);
                        UserProfileHelper.Set(user);

                        db.Dispose();
                    }
                }
            }
            else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_Z) //提早結案
            {
                Model.uADDT11.CHG_DATE = DateTime.Now;
                Model.uADDT11.CHG_PERSON = User.USER_KEY;
                Model.uADDT11.STATUS = ZZZI08EditViewModel.DATA_TYPE_Z;

                Db.UpdateSTATUS(Model);
            }
            else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_D) //刪除
            {
                if (Edb.ADDT13_HIS.Where(a => a.DIALOG_ID == Model.uADDT11.DIALOG_ID).Any() == true)
                {
                    ErrorMsg = "錯誤\r\n" + "此筆活動已有人作答，無法刪除!!";
                    ReturnBool = false;
                }
                else
                {
                    Db.DelDate(Model.uADDT11.DIALOG_ID);
                    this.AllDeleteFile(Model.uADDT11.DIALOG_ID, true);
                }
            }
            else if (Model.DATA_TYPE == ZZZI08EditViewModel.DATA_TYPE_H)
            {
                Model.uADDT11.CHG_DATE = DateTime.Now;
                Model.uADDT11.CHG_PERSON = User.USER_KEY;
                Model.uADDT11.STATUS = ZZZI08EditViewModel.DATA_TYPE_H;

                Db.UpdateSTATUS(Model);
            }
            ErrorMsg = ErrorMsg + Db.ErrorMsg;

            if ((ErrorMsg != null && ErrorMsg != string.Empty) || (ModelState.IsValid == false))
            {
                if (ModelState.IsValid == false) ErrorMsg = "錯誤\r\n" + ErrorMsg;
                ReturnBool = false;
            }
            else
            {
                ReturnBool = true;
            }

            return ReturnBool;
        }

        private void GetSCHOOL_NO()
        {
            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                }
            }
        }

        /// <summary>
        /// ckeditor上傳圖片
        /// </summary>
        /// <param name="upload">預設參數叫upload</param>
        /// <param name="CKEditorFuncNum"></param>
        /// <param name="CKEditor"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        private void Shared()
        {
            ViewBag.DATA_TYPE_A = ZZZI08EditViewModel.DATA_TYPE_A;
            ViewBag.DATA_TYPE_U = ZZZI08EditViewModel.DATA_TYPE_U;
            ViewBag.DATA_TYPE_D = ZZZI08EditViewModel.DATA_TYPE_D;
            ViewBag.DATA_TYPE_H = ZZZI08EditViewModel.DATA_TYPE_H;
            ViewBag.DATA_TYPE_Z = ZZZI08EditViewModel.DATA_TYPE_Z;
            ViewBag.DATA_TYPE_E = ZZZI08EditViewModel.DATA_TYPE_E;
            ViewBag.DATA_TYPE_R = ZZZI08EditViewModel.DATA_TYPE_R;

            ViewBag.VIEW_A = ZZZI08EditViewModel.VIEW_A;
            ViewBag.VIEW_U = ZZZI08EditViewModel.VIEW_U;
            ViewBag.VIEW_D = ZZZI08EditViewModel.VIEW_D;
            ViewBag.VIEW_C = ZZZI08EditViewModel.VIEW_C;
        }
    }
}