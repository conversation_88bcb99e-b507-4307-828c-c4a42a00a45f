﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;

namespace com.ecool.service
{
    public class ZZZI12ViewModelService
    {
        public string ErrorMsg;

        #region 取得此帳號權限清單

        /// <summary>
        /// 取得此帳號權限清單
        /// </summary>
        /// <param name="SCHOOL_NO">學校代碼</param>
        /// <param name="USER_NO">帳號代碼</param>
        /// <returns>List<ZZZI12ViewModel></returns>
        public List<ZZZI12ViewModel> USP_ZZZI12ViewModel_QUERY(string SCHOOL_NO, string USER_NO, string ROLE_ID, ECOOL_APP.UserProfile User)
        {
            List<ZZZI12ViewModel> list_data = new List<ZZZI12ViewModel>();

            ZZZI12ViewModel ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("   Select DISTINCT H.SCHOOL_NO,H.USER_NO,B.BRE_NO,B.BRE_NAME,B.BRE_TYPE,B.LEVEL_NO ");
                sb.Append(" , isnull(A.ACTION_ID, '') ACTION_ID,isnull(A.ACTION_NAME, '') ACTION_NAME ");
                sb.Append(" , isnull(A.ACTION_TYPE, '') ACTION_TYPE ");
                sb.Append(" , CASE WHEN ISNULL(A.ACTION_TYPE, '') = 'ALL' Then 'true' Else 'false' End as ACTION_Checked ");
                sb.Append(" , Case When dbo.fn_Permission_Use(B.BRE_NO, A.ACTION_ID, H.SCHOOL_NO, H.USER_NO) = 'Y' Then   'true' Else 'false' End as Checked ");
                sb.Append(" , Case When (SELECT count(*) FROM HRMT25 T25(NOLOCK) ");
                sb.Append("              INNER JOIN ZZT10 T10(NOLOCK) ON T10.ROLE_ID = T25.ROLE_ID ");
                sb.Append("              WHERE 1 = 1  AND T25.USER_NO = H.USER_NO  AND T25.SCHOOL_NO = H.SCHOOL_NO ");

                //if (ROLE_ID != string.Empty)
                //{
                //    sb.AppendFormat(" AND (CASE WHEN H.USER_TYPE='S' THEN (SELECT R.ROLE_ID FROM HRMT24 R (NOLOCK) WHERE R.ROLE_TYPE='4') ELSE T25.ROLE_ID END ) ='{0}' ", ROLE_ID);
                //}

                sb.Append("              AND T10.BRE_NO = A.BRE_NO AND T10.ACTION_ID = A.ACTION_ID) > 0 Then 'true' else  'false' End ROLE_Checked ");
                sb.AppendFormat(" , Case When dbo.fn_Permission_Use(B.BRE_NO,A.ACTION_ID,'{0}','{1}')='Y' Then   'true' Else 'false' End as MY_Permission_Checked", User.SCHOOL_NO, User.USER_NO);
                sb.Append(" from HRMT01 H  (NOLOCK) ");
                sb.Append(" CROSS JOIN ZZT01 B(NOLOCK) ");
                sb.Append(" FULL JOIN ZZT34 A  (NOLOCK)ON A.BRE_NO = B.BRE_NO ");
                sb.Append(" WHERE 1 = 1  and isnull(B.ENABLE,1)=1 ");

                if (USER_NO != string.Empty)
                {
                    sb.AppendFormat(" AND H.USER_NO ='{0}' ", USER_NO);
                }

                if (SCHOOL_NO != string.Empty)
                {
                    sb.AppendFormat(" AND H.SCHOOL_NO ='{0}' ", SCHOOL_NO);
                }

                sb.Append(" ORDER BY B.LEVEL_NO,B.BRE_NO,ACTION_ID ");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());

                string BRE_NO = string.Empty;

                foreach (DataRow dr in dt.Rows)
                {
                    if (BRE_NO != dr["BRE_NO"].ToString())
                    {
                        ReturnData = new ZZZI12ViewModel();
                        ReturnData.Details_List = new List<ZZZI12_D_ViewModel>();

                        ReturnData.SCHOOL_NO = dr["SCHOOL_NO"].ToString();
                        ReturnData.USER_NO = dr["USER_NO"].ToString();
                        // ReturnData.ROLE_ID = dr["ROLE_ID"].ToString();
                        ReturnData.BRE_NO = dr["BRE_NO"].ToString();
                        ReturnData.BRE_NAME = dr["BRE_NAME"].ToString();
                        ReturnData.BRE_TYPE = dr["BRE_TYPE"].ToString();
                        ReturnData.LEVEL_NO = dr["LEVEL_NO"].ToString();

                        list_data.Add(ReturnData);
                    }

                    ZZZI12_D_ViewModel Details = new ZZZI12_D_ViewModel();

                    Details.SCHOOL_NO = dr["SCHOOL_NO"].ToString();
                    Details.USER_NO = dr["USER_NO"].ToString();
                    // Details.ROLE_ID = dr["ROLE_ID"].ToString();
                    Details.BRE_NO = dr["BRE_NO"].ToString();
                    Details.ACTION_ID = dr["ACTION_ID"].ToString();
                    Details.ACTION_NAME = dr["ACTION_NAME"].ToString();
                    Details.ACTION_TYPE = dr["ACTION_TYPE"].ToString();
                    Details.Checked = Convert.ToBoolean(dr["Checked"]);
                    Details.ACTION_Checked = Convert.ToBoolean(dr["ACTION_Checked"]);
                    Details.ROLE_Checked = Convert.ToBoolean(dr["ROLE_Checked"]);
                    //  Details.USER_Checked = Convert.ToBoolean(dr["USER_Checked"]);
                    Details.MY_Permission_Checked = Convert.ToBoolean(dr["MY_Permission_Checked"]);
                    ReturnData.Details_List.Add(Details);
                    BRE_NO = dr["BRE_NO"].ToString();
                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        #endregion 取得此帳號權限清單

        #region 新增帳號權限

        /// <summary>
        /// 新增帳號權限
        /// </summary>
        /// <param name="Date">uZZT03</param>
        public void AddDate(uZZT03 Date)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                IDbCommand cmd = new SqlCommand(@"INSERT INTO ZZT03(SCHOOL_NO,USER_NO,BRE_NO,ACTION_ID,ADM,CRE_PERSON,CRE_DATE)
                                                 VALUES (@SCHOOL_NO,@USER_NO,@BRE_NO,@ACTION_ID,@ADM,@CRE_PERSON,@CRE_DATE)");

                cmd.Connection = conn;
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;

                cmd.Parameters.Add(
                (Date.SCHOOL_NO == null)
                ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
                : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO));

                cmd.Parameters.Add(
                (Date.USER_NO == null)
                ? new SqlParameter("@USER_NO", DBNull.Value)
                : new SqlParameter("@USER_NO", Date.USER_NO));

                cmd.Parameters.Add(
                (Date.BRE_NO == null)
                ? new SqlParameter("@BRE_NO", DBNull.Value)
                : new SqlParameter("@BRE_NO", Date.BRE_NO));

                cmd.Parameters.Add(
                (Date.ACTION_ID == null)
                ? new SqlParameter("@ACTION_ID", DBNull.Value)
                : new SqlParameter("@ACTION_ID", Date.ACTION_ID));

                cmd.Parameters.Add(new SqlParameter("@ADM", Date.ADM));

                cmd.Parameters.Add(
                (Date.CRE_PERSON == null)
                ? new SqlParameter("@CRE_PERSON", DBNull.Value)
                : new SqlParameter("@CRE_PERSON", Date.CRE_PERSON));

                cmd.Parameters.Add(
                (Date.CRE_DATE == null)
                ? new SqlParameter("@CRE_DATE", DBNull.Value)
                : new SqlParameter("@CRE_DATE", Date.CRE_DATE));

                try
                {
                    cmd.ExecuteNonQuery();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "異動資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 新增帳號權限

        #region 刪除角色權限

        public void DelDate(uZZT03 Date)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                IDbCommand cmd = new SqlCommand(@"DELETE ZZT03 Where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO and BRE_NO=@BRE_NO and ACTION_ID=@ACTION_ID");

                cmd.Connection = conn;
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;

                cmd.Parameters.Add(
                (Date.SCHOOL_NO == null)
                ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
                : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO));

                cmd.Parameters.Add(
                (Date.USER_NO == null)
                ? new SqlParameter("@USER_NO", DBNull.Value)
                : new SqlParameter("@USER_NO", Date.USER_NO));

                cmd.Parameters.Add(
                (Date.BRE_NO == null)
                ? new SqlParameter("@BRE_NO", DBNull.Value)
                : new SqlParameter("@BRE_NO", Date.BRE_NO));

                cmd.Parameters.Add(
                (Date.ACTION_ID == null)
                ? new SqlParameter("@ACTION_ID", DBNull.Value)
                : new SqlParameter("@ACTION_ID", Date.ACTION_ID));

                try
                {
                    cmd.ExecuteNonQuery();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "異動資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 刪除角色權限
    }
}