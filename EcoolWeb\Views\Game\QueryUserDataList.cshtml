﻿@model GameLotteryListIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    bool IsBtnGoHide = EcoolWeb.Models.UserProfileHelper.GetGameIsBtnGoHideCookie();

    long? TotalSeconds = null;

    if (Model.RECEIVE_DATE != null && Model.RECEIVE_DATE != DateTime.MinValue)
    {
        DateTime Now = DateTime.Now;
        DateTime EndDate = (DateTime)Model.RECEIVE_DATE;

        TotalSeconds = DateHelper.Get_DateDiff(DateTime.Now, EndDate, "sec");
    }

}
<link href="@Url.Content("~/Content/css/childrens-month.css")" rel="stylesheet" />
<link href="@Url.Content("~/Content/css/bootstrap-select.css")" rel="stylesheet" />
<style type="text/css">
    .timer, .timer-done, .timer-loop {
        font-size: 30px;
        color: black;
        font-weight: bold;
    }

    .jst-hours {
        float: left;
    }

    .jst-minutes {
        float: left;
    }

    .jst-seconds {
        float: left;
    }

    .jst-clearDiv {
        clear: both;
    }

    .jst-timeout {
        color: red;
    }
</style>

@using (Html.BeginForm("QueryUserDataList", "Game", FormMethod.Post, new { id = "form1", name = "form1", @AutoComplete = "Off" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)

    <div id="MainView">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel with-nav-tabs panel-success" id="panel">
                    <div class="panel-heading">
                        <h1>
                            @Model.GameInfo.GAME_NAME-中獎查詢<strong id="status"></strong>

                            @if (Model.GameInfo.GAME_DATEE >= DateTime.Now)
                            {
                                <a href="@Url.Action("QueryUserGameData","Game", new {GAME_NO = Model.GAME_NO })" class="pull-right btn-primary btn btn-sm">闖關查詢</a>
                            }
                        </h1>
                    </div>
                    <div class="panel-body">
                        @if (Model.RECEIVE_DATE != null && Model.RECEIVE_DATE != DateTime.MinValue)
                        {
                            <div class="row">
                                <label class="col-xs-12" style="font-size: 30px;color: black;font-weight: bold;">
                                    最後領獎日為 @Model.RECEIVE_DATE.Value.ToString("yyyy/MM/dd HH:mm")
                                </label>
                            </div>

                            if (TotalSeconds > 0)
                            {
                                <div class="row">
                                    <label class="col-xs-2 text-right" style="font-size: 30px;color: black;font-weight: bold;">
                                        倒數:
                                    </label>
                                    <div class="col-xs-10 text-left">
                                        <div class="timer" data-seconds-left=@TotalSeconds></div>
                                    </div>
                                </div>
                            }
                        }

                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                        <div id="PageContent">
                            @Html.Action("_PageQueryUserDataList", (string)ViewBag.BRE_NO, Model)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@if (IsBtnGoHide == false)
{
    <div id="DivAddButton">
        <i id="title" class="fa fa-arrow-left fa-3x"></i>
        <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
    </div>
}
else
{
    <div class="text-center">
        <button type="button" onclick="CloseWin()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">關閉視窗</button>
    </div>
}
<div style="width:100%;height:100%;background-color:black;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3 style="color:azure">查詢中…</h3>
    </div>
</div>
<script src="@Url.Content("~/Scripts/jquery.simple.timer.js")"></script>
@*<script src="@Url.Content("~/Scripts/bootstrap-select/bootstrap-select.js")"></script>*@
<script src="@Url.Content("~/Scripts/buzz/buzz.min.js")"></script>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1'

        $('.timer').startTimer({
            onComplete: function (element) {
                element.addClass('is-complete');
            },
            loop: false,
            loopInterval: 3,
        });

        function CloseWin() //這個不會提示是否關閉瀏覽器
        {
            open(location, '_self').close();
        }

        //分頁
         function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.CashSearch.Page)').val(page)
                funAjax()
                $('#QRCode').hide();
            }
        };

         //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_PageQueryUserDataList", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(0)
        }

           function OnBack() {
             $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")
             $(targetFormID).submit();
         }
    </script>
}