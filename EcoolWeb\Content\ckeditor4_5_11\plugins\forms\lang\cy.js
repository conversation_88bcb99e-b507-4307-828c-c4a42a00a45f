﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'cy', {
	button: {
		title: 'Priodweddau Botymau',
		text: 'Testun (Gwerth)',
		type: 'Math',
		typeBtn: 'Botwm',
		typeSbm: 'Anfon',
		typeRst: 'Ailosod'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Priodweddau Blwch Ticio',
		radioTitle: 'Priodweddau Botwm Radio',
		value: 'Gwerth',
		selected: 'Dewiswyd',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Priodweddau Ffurflen',
		menu: 'Priodweddau Ffurflen',
		action: 'Gweithred',
		method: 'Dull',
		encoding: 'Amgodio'
	},
	hidden: {
		title: 'Priodweddau <PERSON>',
		name: 'Enw',
		value: 'Gwerth'
	},
	select: {
		title: '<PERSON>riodwed<PERSON><PERSON>',
		selectInfo: '<PERSON><PERSON><PERSON><PERSON>',
		opAvail: 'Opsiynau ar Gael',
		value: 'Gwerth',
		size: 'Maint',
		lines: 'llinellau',
		chkMulti: 'Caniatàu aml-ddewisiadau',
		required: 'Required', // MISSING
		opText: 'Testun',
		opValue: 'Gwerth',
		btnAdd: 'Ychwanegu',
		btnModify: 'Newid',
		btnUp: 'Lan',
		btnDown: 'Lawr',
		btnSetValue: 'Gosod fel gwerth a ddewiswyd',
		btnDelete: 'Dileu'
	},
	textarea: {
		title: 'Priodweddau Ardal Testun',
		cols: 'Colofnau',
		rows: 'Rhesi'
	},
	textfield: {
		title: 'Priodweddau Maes Testun',
		name: 'Enw',
		value: 'Gwerth',
		charWidth: 'Lled Nod',
		maxChars: 'Uchafswm y Nodau',
		required: 'Required', // MISSING
		type: 'Math',
		typeText: 'Testun',
		typePass: 'Cyfrinair',
		typeEmail: 'Ebost',
		typeSearch: 'Chwilio',
		typeTel: 'Rhif Ffôn',
		typeUrl: 'URL'
	}
} );
