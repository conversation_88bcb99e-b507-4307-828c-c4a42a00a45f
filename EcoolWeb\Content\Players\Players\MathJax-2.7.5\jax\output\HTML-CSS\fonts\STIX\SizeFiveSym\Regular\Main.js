/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeFiveSym/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeFiveSym={directory:"SizeFiveSym/Regular",family:"STIXSizeFiveSym",Ranges:[[688,767,"All"],[768,824,"All"],[8254,8254,"All"],[8400,8431,"All"],[9115,9145,"All"],[9180,9185,"All"]],32:[0,0,250,0,0],95:[-127,177,3000,0,3000],160:[0,0,250,0,0],770:[816,-572,0,-2485,-157],771:[780,-617,0,-2485,-157],9182:[181,90,3238,0,3238],9183:[844,-573,3238,0,3238]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeFiveSym");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeFiveSym/Regular/Main.js");
