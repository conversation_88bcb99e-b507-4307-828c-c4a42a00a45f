﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'et', {
	button: {
		title: 'Nupu omadused',
		text: 'Tekst (väärtus)',
		type: 'Liik',
		typeBtn: 'Nupp',
		typeSbm: 'Saada',
		typeRst: 'Lähtesta'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Märkeruudu omadused',
		radioTitle: 'Raadionupu omadused',
		value: 'Väärtus',
		selected: 'Märgitud',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Vormi omadused',
		menu: 'Vormi omadused',
		action: 'Toiming',
		method: 'Meetod',
		encoding: 'Kodeering'
	},
	hidden: {
		title: 'Varjatud lahtri omadused',
		name: '<PERSON><PERSON>',
		value: 'Väärtus'
	},
	select: {
		title: '<PERSON><PERSON><PERSON><PERSON> omadused',
		selectInfo: 'Info',
		opAvail: 'Võimalikud valikud:',
		value: 'Väärtus',
		size: '<PERSON><PERSON>',
		lines: 'ridu',
		chkMulti: 'Võimalik mitu valikut',
		required: 'Required', // MISSING
		opText: 'Tekst',
		opValue: 'Väärtus',
		btnAdd: 'Lisa',
		btnModify: 'Muuda',
		btnUp: 'Üles',
		btnDown: 'Alla',
		btnSetValue: 'Määra vaikimisi',
		btnDelete: 'Kustuta'
	},
	textarea: {
		title: 'Tekstiala omadused',
		cols: 'Veerge',
		rows: 'Ridu'
	},
	textfield: {
		title: 'Tekstilahtri omadused',
		name: 'Nimi',
		value: 'Väärtus',
		charWidth: 'Laius (tähemärkides)',
		maxChars: 'Maksimaalselt tähemärke',
		required: 'Required', // MISSING
		type: 'Liik',
		typeText: 'Tekst',
		typePass: 'Parool',
		typeEmail: 'E-mail',
		typeSearch: 'Otsi',
		typeTel: 'Telefon',
		typeUrl: 'URL'
	}
} );
