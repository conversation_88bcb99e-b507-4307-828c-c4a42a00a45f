﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI11EditPeopleViewModel
    {
        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }
        public string NAME { get; set; }
        public string SEAT_NO { get; set; }
        public decimal SUMLap { get; set; }
        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }
        public DateTime? RunDtae { get; set; }
        public string CLASS_NO { get; set; }
        /// <summary>
        ///圈數
        /// </summary>
        [DisplayName("圈數")]
        [Required(ErrorMessage = "此欄位必輸")]
        public Double? LAP { get; set; }

        /// <summary>
        /// 學生 / 老師
        /// </summary>
        public string UserType { get; set; }
        public DateTime? CRE_DATE { get; set; }
        public DateTime? CHG_DATE { get; set; }

    }
}