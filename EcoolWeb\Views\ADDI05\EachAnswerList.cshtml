
@model ADDI05EachAnswerListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("EachAnswerList", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(m => m.OrderByName)
    @Html.HiddenFor(m => m.DIALOG_ID)
    @Html.HiddenFor(m => m.Where_USERNO)
    @Html.HiddenFor(m => m.Where_SCHOOLNO)

    @Html.Partial("_ADDI05Menu", 0)

    <div style="height:25px">

    </div>

    <div class="row">
        <div class="col-md-8 col-xs-7">
        </div>

        <div class="col-xs-2">
        </div>
    </div>
    <img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-ADDI05">
        <div class="Details">
            <div class="table-responsive">
                <div class="text-center">
                    <table class="table-ecool table-92Per table-hover">
                        <caption class="Caption_Div_Left">
                            活動名稱：@Model.DIALOG_NAME
                        </caption>
                        <thead>
                            <tr>

                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SCHOOL_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13_Each.First().SHORT_NAME)
                                    <img id="SCHOOL_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SYEAR');">
                                    @Html.DisplayNameFor(model => model.uADDT13_Each.First().SYEAR)
                                    <img id="SYEAR" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SEMESTER');">
                                    @Html.DisplayNameFor(model => model.uADDT13_Each.First().SEMESTER)
                                    <img id="SEMESTER" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO DESC');">
                                    @Html.DisplayNameFor(model => model.uADDT13_Each.First().CLASS_NO)
                                    <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                                    @Html.DisplayNameFor(model => model.uADDT13_Each.First().SEAT_NO)
                                    <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                                    姓名
                                    <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                                    答題日期
                                    <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;" onclick="doSort('Grade');">
                                    分數
                                    <img id="Grade" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.uADDT13_Each)
                            {
                                <tr>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SYEAR)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SEMESTER)
                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                        }

                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                                        }
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.Grade)
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>

                    <div style="height:15px"></div>
                </div>
            </div>
        </div>
    </div>
    <br />
    <div class="" style="margin-left:auto;margin-right:auto;text-align:center">
        <a href="@Url.Action("AnswerList","ADDI05", new { DIALOG_ID = Model.DIALOG_ID }) " class="btn btn-default">回上頁</a>
    </div>
}





@section Scripts {
    <script language="JavaScript">
         var targetFormID = "#form1"

        function onGo(ActionVal) {
            if (ActionVal == "Index") {
                form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "detail") {
                form1.action = '@Url.Action("detail", (string)ViewBag.BRE_NO)';
            }
            form1.submit();
        }


        function todoClear() {
            ////重設

            $(targetFormID).find("#DivSearch :input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1)
        }

        function FunPageProc(pageno) {
            form1.Page.value = pageno
            form1.submit();
        }

        function doSort(SortCol) {
            $("#OrderByName").val(SortCol);
            FunPageProc(1)
        }
    </script>
}

