﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'is', {
	alt: '<PERSON>k<PERSON><PERSON><PERSON><PERSON> texti',
	border: '<PERSON><PERSON>',
	btnUpload: 'Hlaða upp',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: '<PERSON><PERSON>ri bil',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Almennt',
	linkTab: 'Stikla',
	lockRatio: 'Festa stærðarhlutfall',
	menu: 'Eigindi myndar',
	resetSize: '<PERSON><PERSON><PERSON> stær<PERSON>',
	title: 'Eigindi myndar',
	titleButton: 'Eigindi myndahnapps',
	upload: 'Hlaða upp',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'Hægri bil',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
} );
