﻿using System;
using System.Collections.Generic;
using System.Collections;
using System.Data;

namespace com.ecool.service
{
    /// <summary>
    /// ProductService
    /// </summary>
    public class ProductService : ServiceBase
    {
        /// <summary>
        /// 產品查詢
        /// </summary>
        public List<Hashtable> USP_PRODUCT_QUERY(string ParamAWARD_NO)
        {
            List<Hashtable> list_data = new List<Hashtable>();

            try
            {
                if (ParamAWARD_NO != "")
                    ParamAWARD_NO = "WHERE AWARD_NO = '" + ParamAWARD_NO + "'";
                list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT AWARD_NO, AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS from AWAT02 " + ParamAWARD_NO + "Where AWARD_STATUS='y' ORDER BY COST_CASH DESC");
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        /// <summary>
        /// 產品查詢 Grid 畫面呈現
        /// </summary>
        /// <param name="sidx">排序欄位名稱</param>
        /// <param name="sord">排序方式</param>
        /// <param name="page">目前頁數</param>
        /// <param name="pageSize">一頁幾筆</param>
        public DataTable USP_PRODUCT_QUERY_DATATB(string sidx, string sord, int page, int pageSize)
        {
            DataTable list_data = null;
            try
            {
                int startIndex = ((page - 1) * pageSize) + 1;
                int endIndex = page * pageSize;
                string sql = @"WITH PAGED_AWAT01 AS
                        (
	                        SELECT	AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION,
			                        ROW_NUMBER() OVER (ORDER BY " + sidx + @" " + sord + @") AS AWARD_NO
	                        FROM	 AWAT02
                        )
                        SELECT AWARD_NAME, COST_CASH, QTY_STORAGE, CONVERT(varchar(12), SDATETIME, 111 ) AS SDATETIME, CONVERT(varchar(12), EDATETIME, 111 ) AS EDATETIME, DESCRIPTION
                        FROM PAGED_AWAT01
                        WHERE	AWARD_NO BETWEEN " + startIndex + @" AND " + endIndex + @";";
                list_data = new sqlConnection.sqlConnection().executeQueryByDataTableList(sql);
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        /// <summary>
        /// 產品新增
        /// </summary>
        public void USP_PRODUCT_INSERT(string ParamAWARD_NAME, string ParamCOST_CASH, string ParamQTY_STORAGE, string ParamSDATETIME, string ParamEDATETIME, string ParamDESCRIPTION, string ParamIMG_FILE)
        {
            try
            {
                new sqlConnection.sqlConnection().execute("INSERT INTO AWAT02(AWARD_NAME, COST_CASH, QTY_STORAGE, QTY_TRANS, SDATETIME, EDATETIME, DESCRIPTION, IMG_FILE, AWARD_STATUS ) VALUES ('" + ParamAWARD_NAME + "', '" + ParamCOST_CASH + "', '" + ParamQTY_STORAGE + "', '0', '" + ParamSDATETIME + "', '" + ParamEDATETIME + "', '" + ParamDESCRIPTION + "', '" + ParamIMG_FILE + "', '0')");
            }
            catch (Exception exception)
            {
                throw exception;
            }
        }

        /// <summary>
        /// 產品修改
        /// </summary>
        public void USP_PRODUCT_UPDATE(string ParamAWARD_NAME, string ParamCOST_CASH, string ParamQTY_STORAGE, string ParamSDATETIME, string ParamEDATETIME, string ParamDESCRIPTION, string ParamIMG_FILE, string ParamAWARD_NO)
        {
            try
            {
                string strSQL = "UPDATE AWAT02 set AWARD_NAME = '" + ParamAWARD_NAME + "', COST_CASH = '" + ParamCOST_CASH + "', QTY_STORAGE = '" + ParamQTY_STORAGE + "', SDATETIME = '" + ParamSDATETIME + "', EDATETIME = '" + ParamEDATETIME + "', DESCRIPTION = '" + ParamDESCRIPTION + "'";
                if (ParamIMG_FILE != "")
                {
                    strSQL += ", IMG_FILE = '" + ParamIMG_FILE + "'";
                }
                new sqlConnection.sqlConnection().execute(strSQL + "WHERE AWARD_NO = '" + ParamAWARD_NO + "' ");
            }
            catch (Exception exception)
            {
                throw exception;
            }
        }

        /// <summary>
        /// 產品刪除
        /// </summary>
        public void USP_PRODUCT_DELETE(string ParamAWARD_NO)
        {
            try
            {
                new sqlConnection.sqlConnection().execute("DELETE FROM AWAT02 WHERE AWARD_NO = '" + ParamAWARD_NO + "' ");
            }
            catch (Exception exception)
            {
                throw exception;
            }
        }

        public void USP_ADDI03_DELETE(string SCHOOL_NO, string hidBOOK_ID)
        {
            try
            {
                new sqlConnection.sqlConnection().execute("DELETE ADDT03 Where SCHOOL_NO='" + SCHOOL_NO + "' and BOOK_ID='" + hidBOOK_ID + "' ");
            }
            catch (Exception exception)
            {
                throw exception;
            }
        }
    }
}