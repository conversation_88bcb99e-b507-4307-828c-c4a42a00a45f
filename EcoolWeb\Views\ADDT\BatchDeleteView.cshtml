﻿@model EcoolWeb.ViewModels.ADDTBatchDeleteViewViewModel
@{
    ViewBag.Title = "閱讀認證-批次作廢資料確認";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("BatchDeleteView", "ADDT", FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.StringDelApply)

    <h3 style="color:red">請確認以下是你勾選要作廢資料是否正確?及填作廢原因</h3>
    <div class="form-horizontal">
        <div style="height:15px"></div>
        <div class="row">

            <div class="form-group text-left">
                &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;
                @Html.Label("作廢原因", htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-12 col-sm-12" })
                <div class="col-md-12  col-sm12">
                    @Html.DropDownList("BACK_MEMO_DropDownList", (IEnumerable<SelectListItem>)ViewBag.BackSelectItem, new { @class = "form-control", @onchange = "BackDropDownList(this.value)" })
                    @Html.Hidden("BACK_MEMO")
                </div>
            </div>
        </div>
    </div>

    <img src="~/Content/img/web-Bar-09.png" class="img-responsive App_hide" alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-reader">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            申請日期
                        </th>
                        <th style="text-align: center;">
                            班級
                        </th>
                        <th style="text-align: center;">
                            座號
                        </th>
                        <th style="text-align: center;">
                            姓名
                        </th>
                        <th style="text-align: center;">
                            書名
                        </th>
                        <th style="text-align: center;">
                            狀態
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.BatchDeleteList)
                    {
                        @Html.Partial("_BatchDeleteList", item)
                    }
                </tbody>
            </table>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="text-center">
            <a href='@Url.Action("ADDTALLList", "ADDT")' role="button" class="btn btn-default">
                取消
            </a>
            <button type="button" class="btn btn-default" onclick="btnBatchDelSubmit();">
                確定批次作廢
            </button>
        </div>
    </div>
}

<script type="text/javascript">
      var targetFormID = '#form1';

           function btnBatchDelSubmit() {

            if ($('#@Html.IdFor(m=>m.StringDelApply)').val() == '') {
                alert('未勾選要作廢資料')
                return false;
               }

              if ($('#BACK_MEMO').val() == '') {
                 alert('未輸入作廢原因')
                return false;
             }

           if (confirm("您確定要批次作廢？") == true) {
                $(targetFormID).attr("action", "@Url.Action("BatchDeleteData", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
    }

    function BackDropDownList(Val) {
        if (Val == '@ECOOL_APP.com.ecool.service.BDMT02Service.OtherVal') {
            $('#BACK_MEMO').val("")
            $('#BACK_MEMO').attr("type", "text").attr("placeholder", "請輸入原因").addClass("form-control");
        }
        else {
            $('#BACK_MEMO').attr("type", "hidden").removeClass();
            $('#BACK_MEMO').val(Val)
        }
    }
</script>