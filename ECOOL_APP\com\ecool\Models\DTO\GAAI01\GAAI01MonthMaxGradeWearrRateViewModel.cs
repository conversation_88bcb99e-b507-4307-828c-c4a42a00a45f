﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01MonthMaxGradeWearrRateViewModel
    {
        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///當記當下總學生人數
        /// </summary>
        [DisplayName("學生人數")]
        public int? STUDENT_NUMBER { get; set; }

        /// <summary>
        ///配戴人數
        /// </summary>
        [DisplayName("配戴人數")]
        public int? WEAR_NUMBER { get; set; }

        /// <summary>
        ///配戴率
        /// </summary>
        [DisplayName("配戴率")]
        public decimal? WEAR_RATE { get; set; }

        /// <summary>
        /// 未配載原因
        /// </summary>
        [DisplayName("未配載原因")]
        public string UN_WEAR_MEMO { get; set; }

        public byte UN_WEAR_TYPE { get; set; }
    }
}