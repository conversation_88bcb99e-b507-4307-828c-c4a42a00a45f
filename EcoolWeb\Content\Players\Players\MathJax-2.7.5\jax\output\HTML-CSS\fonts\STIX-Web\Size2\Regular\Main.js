/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Size2={directory:"Size2/Regular",family:"STIXMathJax_Size2",testString:"\u00A0\u02C6\u02C7\u02DC\u02F7\u0302\u0303\u0305\u030C\u0330\u0332\u0338\u203E\u20D0\u20D1",32:[0,0,250,0,0],40:[1566,279,589,139,503],41:[1566,279,608,114,478],47:[1566,279,806,25,781],91:[1566,279,459,190,422],92:[1566,279,806,25,781],93:[1566,279,459,37,269],123:[1566,279,717,124,531],125:[1566,279,717,186,593],160:[0,0,250,0,0],710:[777,-564,979,0,979],711:[777,-564,979,0,979],732:[760,-608,977,-2,977],759:[-117,269,977,-2,977],770:[777,-564,979,0,979],771:[760,-608,979,0,979],773:[820,-770,1500,0,1500],780:[777,-564,979,0,979],816:[-117,269,979,0,979],818:[-127,177,1500,0,1500],824:[662,0,0,-720,-6],8254:[820,-770,1500,0,1500],8400:[749,-584,1307,0,1308],8401:[749,-584,1308,0,1308],8406:[735,-482,1308,0,1308],8407:[735,-482,1308,0,1308],8428:[-123,288,1308,0,1308],8429:[-123,288,1308,0,1308],8430:[-26,279,1308,0,1308],8431:[-26,279,1308,0,1308],8730:[2056,404,1124,110,1157],8731:[2056,404,1124,110,1157],8732:[2056,404,1124,110,1157],8968:[1566,279,524,190,479],8969:[1566,279,526,47,336],8970:[1566,279,524,190,479],8971:[1566,279,526,47,336],9140:[766,-544,1606,74,1532],9141:[139,83,1606,74,1532],9168:[690,189,266,100,166],9180:[66,147,1460,0,1460],9181:[785,-572,1460,0,1460],9182:[143,81,1460,0,1460],9183:[797,-573,1460,0,1460],9184:[66,212,1886,0,1886],9185:[842,-564,1886,0,1886],10098:[1566,279,688,230,651],10099:[1566,279,688,37,458],10214:[1566,279,555,190,517],10215:[1566,279,555,38,365],10216:[1566,279,622,95,531],10217:[1566,279,622,91,527],10218:[1566,279,901,93,793],10219:[1566,279,901,108,808],10627:[1566,279,827,122,692],10628:[1565,280,827,135,705],10629:[1566,282,793,155,693],10630:[1566,282,793,100,638],11004:[1586,289,906,133,773],11007:[1586,289,636,133,503]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Size2"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size2/Regular/Main.js"]);
