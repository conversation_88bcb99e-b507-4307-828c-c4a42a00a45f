﻿using com.ecool.sqlConnection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace ECOOL_APP.EF
{
    public static class DataTableExtensions
    {
        public static IQueryable<TEntity> dynamicOrderBy<TEntity>(this IQueryable<TEntity> source, string orderByProperty,
                 string SyntaxName)
        {
            string command = SyntaxName.ToUpper() == "DESC" ? "OrderByDescending" : "OrderBy";
            var type = typeof(TEntity);
            var property = type.GetProperty(orderByProperty);
            var parameter = Expression.Parameter(type, "p");
            var propertyAccess = Expression.MakeMemberAccess(parameter, property);
            var orderByExpression = Expression.Lambda(propertyAccess, parameter);
            var resultExpression = Expression.Call(typeof(Queryable), command, new Type[] { type, property.PropertyType },
                                          source.Expression, Expression.Quote(orderByExpression));
            return source.Provider.CreateQuery<TEntity>(resultExpression);
        }

        /// <summary>
        /// DataTable to List
        /// 如果欄位名稱跟物件屬性名稱相同，則直接對應
        /// ex.=> dt.ToList<className>();
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="table"></param>
        /// <returns></returns>
        public static List<T> ToList<T>(this DataTable table) where T : new()
        {
            List<PropertyInfo> properties = typeof(T).GetProperties().ToList();
            List<T> result = new List<T>();

            foreach (var row in table.Rows)
            {
                var item = CreateItemFromRow<T>((DataRow)row);

                result.Add(item);
            }

            return result;
        }

        private static T CreateItemFromRow<T>(DataRow row) where T : new()
        {
            T item = new T();
            Type itemType = typeof(T);

            foreach (DataColumn Col in row.Table.Columns)
            {
                PropertyInfo pInfo = itemType.GetProperty(Col.ColumnName);
                if (pInfo == null) continue;

                Type t = Nullable.GetUnderlyingType(pInfo.PropertyType) ?? pInfo.PropertyType;

                object safeValue = null;

                if (row[Col] == null || DBNull.Value.Equals(row[Col]))
                {
                    safeValue = null;
                }
                else
                {
                    if (t == typeof(bool))
                    {
                        bool boolValue;
                        if (bool.TryParse(Convert.ToString(row[Col]), out boolValue))
                            safeValue = Convert.ChangeType(boolValue, t);
                        else
                        {
                            int intValue;
                            if (int.TryParse(Convert.ToString(row[Col]), out intValue))
                                safeValue = Convert.ChangeType(intValue, t);
                        }
                    }
                    else
                    {
                        var ThisCol = (Convert.ToString(row[Col])).Trim();

                        if (string.IsNullOrWhiteSpace(ThisCol))
                        {
                            safeValue = null;
                        }
                        else
                        {
                            safeValue = Convert.ChangeType(row[Col], t);
                        }
                    }
                }

                pInfo.SetValue(item, safeValue, null);
            }
            return item;
        }

        /// <summary>
        ///  DataTable to List
        /// 如果欄位名稱跟物件屬性名稱不同，則建立Dictionary型態的Mapping物件
        /// ex.
        /// var mappings = new Dictionary<string, string>();
        /// mappings.Add("CompId", "CompId");
        /// mappings.Add("HandleUnit", "HandleUnit");
        /// mappings.Add("No", "No");
        /// var Way3 = dt.ToList<className>(mappings);
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="table"></param>
        /// <param name="mappings"></param>
        /// <returns></returns>
        public static List<T> ToList<T>(this DataTable table, Dictionary<string, string> mappings) where T : new()
        {
            List<PropertyInfo> properties = typeof(T).GetProperties().ToList();
            List<T> result = new List<T>();

            foreach (var row in table.Rows)
            {
                var item = CreateItemFromRow<T>((DataRow)row, properties, mappings);
                result.Add(item);
            }

            return result;
        }

        private static T CreateItemFromRow<T>(DataRow row, List<PropertyInfo> properties, Dictionary<string, string> mappings) where T : new()
        {
            T item = new T();
            foreach (var property in properties)
            {
                if (mappings.ContainsKey(property.Name))
                    property.SetValue(item, Convert.ChangeType(row[mappings[property.Name]], property.PropertyType), null);
            }

            return item;
        }

        /// <summary>
        /// List to DataTabl
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <returns></returns>
        public static DataTable AsDataTable<T>(this IEnumerable<T> data)
        {
            PropertyDescriptor property = null;
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            foreach (PropertyDescriptor prop in TypeDescriptor.GetProperties(typeof(T)))
            {
                if (prop.Name == "")
                {
                    property = prop;
                    break;
                }
            }

            var table = new DataTable();
            foreach (PropertyDescriptor prop in properties)
                table.Columns.Add(prop.Name, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
            foreach (T item in data)
            {
                DataRow row = table.NewRow();
                foreach (PropertyDescriptor prop in properties)
                    row[prop.Name] = prop.GetValue(item) ?? DBNull.Value;
                table.Rows.Add(row);
            }
            return table;
        }

        public static bool SqlBatchData<T>(this IEnumerable<T> Data, string TableName, out string Msg)
        {
            bool ReturnBool = false;

            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                try
                {
                    SqlTransaction trans = conn.BeginTransaction();

                    using (SqlBulkCopy sbCopy = new SqlBulkCopy(conn, SqlBulkCopyOptions.FireTriggers, trans))
                    {
                        try
                        {
                            sbCopy.DestinationTableName = TableName;
                            sbCopy.WriteToServer(Data.AsDataTable());

                            Msg = string.Empty;
                            trans.Commit();
                            ReturnBool = true;
                        }
                        catch (Exception ex)
                        {
                            Msg = TableName + "新增資料失敗;\r\n" + ex.Message;
                            trans.Rollback();
                        }
                        finally
                        {
                            trans.Dispose();
                        }
                    }
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }

            return ReturnBool;
        }

        public static void SqlBatchData<T>(this IEnumerable<T> Data, string TableName, SqlConnection conn, SqlTransaction trans = null)
        {
            using (SqlBulkCopy sbCopy = new SqlBulkCopy(conn, SqlBulkCopyOptions.FireTriggers, trans))
            {
                sbCopy.DestinationTableName = TableName;
                sbCopy.WriteToServer(Data.AsDataTable());
            }
        }
    }
}