﻿using ECOOL_APP.EF;
using MvcPaging;

namespace EcoolWeb.Models
{
    public class NoticeApiIndexViewModel
    {

        /// <summary>
        /// 作業類別 1.OS ,2.Android
        /// </summary>
        public int OS_TYPE { get; set; }


        /// <summary>
        /// 學校代碼
        /// </summary>
        public string SCHOOL_NO { get; set; }


        /// <summary>
        /// 帳號
        /// </summary>
        public string USER_NO { get; set; }



        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設20筆
        /// </summary>
        public int PageSize { get; set; }


        /// 排序欄位
        /// </summary>
        public string OrderByName { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SearchContents { get; set; }

        


        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<NoticeApiListViewModel> List;


        public NoticeApiIndexViewModel()
        {
            PageSize = 10;
            Page = 1;
            OrderByName = "";
            SyntaxName = "ASC";
        }
    }

    public class NoticeApiListViewModel
    {

        public string AppControllerName { get; set; }

        public string AppIdentifierName { get; set; }

        public string AppTitleName { get; set; }
        
        public string WebViewPath { get; set; }

        public APPT02 RefAPPT02;
    }
  }